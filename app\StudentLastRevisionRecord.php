<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentLastRevisionRecord extends Model
{
    use HasFactory;
    protected $table = 'student_last_revision_record';

    protected $fillable = [
        'student_id',
        'revision_year_month_day',
        'from_surat',
        'from_ayat',
        'to_surat',
        'to_ayat',
    ];

    /**
     * Get the student that owns the plan.
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }


}

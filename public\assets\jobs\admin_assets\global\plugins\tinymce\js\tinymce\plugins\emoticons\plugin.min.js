!function(){"use strict";var t=tinymce.util.Tools.resolve("tinymce.PluginManager"),e=tinymce.util.Tools.resolve("tinymce.util.Tools"),i=[["cool","cry","embarassed","foot-in-mouth"],["frown","innocent","kiss","laughing"],["money-mouth","sealed","smile","surprised"],["tongue-out","undecided","wink","yell"]],o=function(t){var o;return o='<table role="list" class="mce-grid">',e.each(i,function(i){o+="<tr>",e.each(i,function(e){var i=t+"/img/smiley-"+e+".gif";o+='<td><a href="#" data-mce-url="'+i+'" data-mce-alt="'+e+'" tabindex="-1" role="option" aria-label="'+e+'"><img src="'+i+'" style="width: 18px; height: 18px" role="presentation" /></a></td>'}),o+="</tr>"}),o+="</table>"},n=function(t,e){var i=o(e);t.addButton("emoticons",{type:"panelbutton",panel:{role:"application",autohide:!0,html:i,onclick:function(e){var i,o,n,a=t.dom.getParent(e.target,"a");a&&(i=t,o=a.getAttribute("data-mce-url"),n=a.getAttribute("data-mce-alt"),i.insertContent(i.dom.createHTML("img",{src:o,alt:n})),this.hide())}},tooltip:"Emoticons"})};t.add("emoticons",function(t,e){n(t,e)})}();
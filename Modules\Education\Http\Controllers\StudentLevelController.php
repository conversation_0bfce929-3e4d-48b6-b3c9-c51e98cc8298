<?php

namespace Modules\Education\Http\Controllers;

use App\Classes;
use App\Http\Controllers\Controller;
use App\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;


class StudentLevelController extends Controller
{


    public function fetchStudentLevels($classId)
    {
        $students = Student::query()
            ->with('studentProgramLevels.programlevel')
            ->whereHas('joint_classes', function ($query) use ($classId) {
                $query->where('class_id', $classId)
                    ->whereNull('class_students.end_date') // Ensure the student is still active in the class
                    ->whereNull('class_students.deleted_at'); // Exclude soft-deleted relationships
            })

            ->get();


        return \DataTables::of($students)
            ->addColumn('current_level', function ($student) {
                $currentLevel = $student->studentProgramLevels->first();
                return $currentLevel ? $currentLevel->programlevel->title : null;
            })
            ->addColumn('current_level_id', function ($student) {
                $currentLevel = $student->studentProgramLevels->first();
                return $currentLevel ? $currentLevel->level_id : null;
            })
            ->addColumn('available_levels', function ($student) use ($classId) {
                return \App\ProgramLevel::whereHas('program.classes.students', function ($query) use ($student,$classId) {
                    $query->where('students.id', $student->id)
                        ->where('classes.id', $classId); // Restrict to the specific class

                })
                    ->get(['id'])
                    ->map(function ($programLevel) {
                        return [
                            'id' => $programLevel->id,
                            'title' => $programLevel->title, // Fetch the translated title
                        ];
                    })
                    ->toArray();
            })
            ->addColumn('student_id', function ($student) {
                return $student->id;
            })
            ->addColumn('name', function ($row) {
                // Extract the student relationship
                $student = $row;

                // Ensure the student relationship exists
                if (!$student) {
                    return '<span class="student-name">N/A</span>';
                }

                // Clean and format the student's full name
                $cleanName = str_replace(['"', "'"], '', $student->full_name);
                $studentName = ucwords(strtolower($cleanName));
                $studentNameLimited = Str::limit($studentName, 15, '...');

                // Determine the gender for placeholder image
                $gender = strtolower($student->gender);
                $femalePlaceholder = asset('assets/workplace/img/female student profile picture placeholderrr.png');
                $malePlaceholder = asset('assets/workplace/img/male profile picture placeholder.png');
                $defaultImage = ($gender === 'female') ? $femalePlaceholder : $malePlaceholder;

                // Determine the image URL using Laravel's Storage facade
                $imageSrc = null;
                if (!empty($student->student_photo)) {
                    // Remove 'public/' from the path if present
                    $studentPhotoPath = Str::startsWith($student->student_photo, 'public/')
                        ? Str::replaceFirst('public/', '', $student->student_photo)
                        : $student->student_photo;

                    // Check if the student's photo exists in the 'public' disk
                    if (Storage::disk('public')->exists($studentPhotoPath)) {
                        $imageSrc = Storage::url($studentPhotoPath);
                    }
                }

                // Use gender-based placeholder if image doesn't exist
                if (!$imageSrc) {
                    $imageSrc = $defaultImage;
                }

                // Sanitize the full and truncated names for safe HTML output
                $safeFullName = e($studentName);
                $safeTruncatedName = e($studentNameLimited);

                // Create the HTML for the student's name with tooltip
                $nameWithTooltip = '<span class="student-name-tooltip" data-toggle="tooltip" title="' . $safeFullName . '">' . $safeTruncatedName . '</span>';

                // Combine everything into an HTML block (with image on the left and text on the right)
                $html = '
        <div class="student-container">
            <img src="' . $imageSrc . '" 
                 alt="Profile picture of ' . $safeFullName . '" 
                 class="student-image" 
                 onclick="enlargeImage(\'' . $imageSrc . '\')" 
                 role="button" 
                 aria-label="Enlarge profile picture of ' . $safeFullName . '"
                 tabindex="0"
                 onkeypress="if(event.key === \'Enter\'){ enlargeImage(\'' . $imageSrc . '\'); }"
                 loading="lazy"
            />
            <div class="student-details">
                ' . $nameWithTooltip . '
            </div>
        </div>
    ';

                return $html;
            })

            ->rawColumns(['name'])
            ->make(true);
    }





    public function updateStudentLevel(Request $request, $studentId)
    {
        $request->validate([
            'level' => 'required|integer|exists:program_levels,id',
            'class_id' => 'required|integer|exists:classes,id',
        ]);

        $student = Student::findOrFail($studentId);

        // Handle existing assignments in other classes
        $existingLevel = $student->studentProgramLevels()
            ->where('class_id', '!=', $request->class_id)
            ->first();

        if ($existingLevel) {
            $existingLevel->delete(); // Soft-delete the existing level assignment
        }

        // Assign new level
        $student->studentProgramLevels()->updateOrCreate(
            ['class_id' => $request->class_id],
            ['level_id' => $request->level, 'status' => 'active']
        );

        return response()->json(['message' => 'Student level updated successfully.']);
    }






}

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use <PERSON><PERSON><PERSON>\JobSeeker\Entities\CommandScheduleRule;
use Mo<PERSON>les\JobSeeker\Entities\CommandScheduleExecution;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Artisan;
use Carbon\Carbon;
use Cron\CronExpression;

/**
 * CommandScheduleService
 * 
 * Handles command execution tracking, scheduling logic, and command isolation
 * to prevent blocking and ensure reliable execution of scheduled commands.
 */
final class CommandScheduleService
{
    /**
     * Calculate the next run time for a schedule rule based on its cron expression
     *
     * @param CommandScheduleRule $rule
     * @param Carbon|null $from
     * @return Carbon|null
     */
    public function calculateNextRunTime(CommandScheduleRule $rule, ?Carbon $from = null): ?Carbon
    {
        if (!$rule->is_active) {
            return null;
        }
        
        $from = $from ?? Carbon::now($rule->timezone);
        
        try {
            // Handle different schedule types
            switch ($rule->schedule_type) {
                case 'cron':
                    return $this->calculateNextRunFromCron($rule->schedule_expression, $from, $rule->timezone);
                    
                case 'daily_at':
                    return $this->calculateNextRunDaily($rule->schedule_expression, $from, $rule->timezone);
                    
                case 'weekly_at':
                    return $this->calculateNextRunWeekly($rule->schedule_expression, $from, $rule->timezone);
                    
                default:
                    // Try to parse as cron expression fallback
                    return $this->calculateNextRunFromCron($rule->schedule_expression, $from, $rule->timezone);
            }
        } catch (\Exception $e) {
            Log::error('CommandScheduleService: Failed to calculate next run time', [
                'rule_id' => $rule->id,
                'rule_name' => $rule->name,
                'schedule_type' => $rule->schedule_type,
                'schedule_expression' => $rule->schedule_expression,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }
    
    /**
     * Calculate next run time from cron expression
     *
     * @param string $cronExpression
     * @param Carbon $from
     * @param string $timezone
     * @return Carbon|null
     */
    private function calculateNextRunFromCron(string $cronExpression, Carbon $from, string $timezone): ?Carbon
    {
        try {
            $cron = new CronExpression($cronExpression);
            $nextRun = $cron->getNextRunDate($from->toDateTime());
            
            return Carbon::createFromFormat('Y-m-d H:i:s', $nextRun->format('Y-m-d H:i:s'), $timezone);
        } catch (\Exception $e) {
            Log::warning('CommandScheduleService: Invalid cron expression', [
                'cron_expression' => $cronExpression,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }
    
    /**
     * Calculate next run time for daily schedule
     *
     * @param string $timeExpression
     * @param Carbon $from
     * @param string $timezone
     * @return Carbon|null
     */
    private function calculateNextRunDaily(string $timeExpression, Carbon $from, string $timezone): ?Carbon
    {
        try {
            // Handle both "HH:MM" format and cron format "MM HH * * *"
            if (preg_match('/^(\d{1,2}):(\d{2})$/', $timeExpression, $matches)) {
                $hour = (int)$matches[1];
                $minute = (int)$matches[2];
            } elseif (preg_match('/^(\d+)\s+(\d+)\s+\*\s+\*\s+\*$/', $timeExpression, $matches)) {
                $minute = (int)$matches[1];
                $hour = (int)$matches[2];
            } else {
                throw new \InvalidArgumentException("Invalid daily time format: {$timeExpression}");
            }
            
            $nextRun = Carbon::createFromTime($hour, $minute, 0, $timezone);
            
            // If the time has already passed today, schedule for tomorrow
            if ($nextRun->lessThanOrEqualTo($from)) {
                $nextRun->addDay();
            }
            
            return $nextRun;
        } catch (\Exception $e) {
            Log::warning('CommandScheduleService: Invalid daily time expression', [
                'time_expression' => $timeExpression,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }
    
    /**
     * Calculate next run time for weekly schedule
     *
     * @param string $weeklyExpression
     * @param Carbon $from
     * @param string $timezone
     * @return Carbon|null
     */
    private function calculateNextRunWeekly(string $weeklyExpression, Carbon $from, string $timezone): ?Carbon
    {
        try {
            // Handle cron format "MM HH * * DOW"
            if (preg_match('/^(\d+)\s+(\d+)\s+\*\s+\*\s+(\d+)$/', $weeklyExpression, $matches)) {
                $minute = (int)$matches[1];
                $hour = (int)$matches[2];
                $dayOfWeek = (int)$matches[3]; // 0=Sunday, 6=Saturday
            } else {
                throw new \InvalidArgumentException("Invalid weekly format: {$weeklyExpression}");
            }
            
            $nextRun = Carbon::now($timezone)
                ->startOfWeek()
                ->addDays($dayOfWeek)
                ->setTime($hour, $minute, 0);
            
            // If the time has already passed this week, schedule for next week
            if ($nextRun->lessThanOrEqualTo($from)) {
                $nextRun->addWeek();
            }
            
            return $nextRun;
        } catch (\Exception $e) {
            Log::warning('CommandScheduleService: Invalid weekly expression', [
                'weekly_expression' => $weeklyExpression,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }
    
    /**
     * Update next run time for a schedule rule
     *
     * @param CommandScheduleRule $rule
     * @param Carbon|null $nextRunTime
     * @return bool
     */
    public function updateNextRunTime(CommandScheduleRule $rule, ?Carbon $nextRunTime = null): bool
    {
        if ($nextRunTime === null) {
            $nextRunTime = $this->calculateNextRunTime($rule);
        }
        
        $rule->next_run_at = $nextRunTime;
        $result = $rule->save();
        
        Log::info('CommandScheduleService: Updated next run time', [
            'rule_id' => $rule->id,
            'rule_name' => $rule->name,
            'command' => $rule->command,
            'next_run_at' => $nextRunTime?->toDateTimeString()
        ]);
        
        return $result;
    }
    
    /**
     * Execute a command with isolation and timeout protection
     *
     * @param CommandScheduleRule $rule
     * @return array{success: bool, output: string, error: string|null, duration: int, execution_id: int|null}
     */
    public function executeCommandWithIsolation(CommandScheduleRule $rule): array
    {
        $startTime = microtime(true);
        $output = '';
        $error = null;
        $success = false;
        $execution = null;
        
        Log::info('CommandScheduleService: Starting command execution', [
            'rule_id' => $rule->id,
            'rule_name' => $rule->name,
            'command' => $rule->command,
            'max_execution_time' => $rule->max_execution_time
        ]);
        
        try {
            // Check if command is already running (prevent concurrent executions)
            if ($rule->isCurrentlyExecuting()) {
                Log::warning('CommandScheduleService: Command already running, skipping', [
                    'rule_id' => $rule->id,
                    'rule_name' => $rule->name,
                    'command' => $rule->command
                ]);
                
                return [
                    'success' => false,
                    'output' => '',
                    'error' => 'Command is already running',
                    'duration' => 0,
                    'execution_id' => null
                ];
            }
            
            // Create execution record
            $execution = CommandScheduleExecution::markStarted($rule->id, $rule->command);
            
            // Mark rule execution as started
            $rule->markExecutionStarted();
            
            // Execute command with timeout
            $result = $this->executeWithTimeout($rule->command, $rule->execution_timeout ?? $rule->max_execution_time);
            
            $output = $result['output'];
            $error = $result['error'];
            $success = $result['success'];
            
            // Get memory usage
            $memoryUsageMb = round(memory_get_peak_usage(true) / 1024 / 1024, 2);
            
            if ($success) {
                // Mark execution as completed
                $execution->markCompleted(0, $output, $memoryUsageMb);
                
                // Calculate and set next run time
                $nextRunTime = $this->calculateNextRunTime($rule);
                $rule->markExecutionCompleted($nextRunTime);
            } else {
                // Mark execution as failed
                $execution->markFailed(1, $error, $output, $memoryUsageMb);
                
                // On failure, still calculate next run time for retry
                $nextRunTime = $this->calculateNextRunTime($rule);
                $rule->markExecutionFailed($error, $nextRunTime);
            }
            
        } catch (\Exception $e) {
            $error = $e->getMessage();
            $success = false;
            
            // Mark execution as failed if it was created
            if ($execution) {
                $memoryUsageMb = round(memory_get_peak_usage(true) / 1024 / 1024, 2);
                $execution->markFailed(1, $error, $output, $memoryUsageMb);
            }
            
            // Calculate next run time even on exception
            $nextRunTime = $this->calculateNextRunTime($rule);
            $rule->markExecutionFailed($error, $nextRunTime);
            
            Log::error('CommandScheduleService: Command execution exception', [
                'rule_id' => $rule->id,
                'rule_name' => $rule->name,
                'command' => $rule->command,
                'execution_id' => $execution?->id,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        $duration = (int)((microtime(true) - $startTime) * 1000); // Duration in milliseconds
        
        Log::info('CommandScheduleService: Command execution completed', [
            'rule_id' => $rule->id,
            'rule_name' => $rule->name,
            'command' => $rule->command,
            'execution_id' => $execution?->id,
            'success' => $success,
            'duration_ms' => $duration,
            'output_length' => strlen($output),
            'has_error' => !empty($error)
        ]);
        
        return [
            'success' => $success,
            'output' => $output,
            'error' => $error,
            'duration' => $duration,
            'execution_id' => $execution?->id
        ];
    }
    
    /**
     * Execute command with timeout protection
     *
     * @param string $command
     * @param int $timeoutSeconds
     * @return array{success: bool, output: string, error: string|null}
     */
    private function executeWithTimeout(string $command, int $timeoutSeconds): array
    {
        try {
            // Use Laravel's Process facade for better control and timeout handling
            $result = Process::timeout($timeoutSeconds)->run("php artisan {$command}");
            
            return [
                'success' => $result->successful(),
                'output' => $result->output(),
                'error' => $result->errorOutput() ?: null
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'output' => '',
                'error' => "Process execution failed: " . $e->getMessage()
            ];
        }
    }
    
    /**
     * Check if a command is safe to execute (prevent dangerous commands)
     *
     * @param string $command
     * @return bool
     */
    public function isCommandSafe(string $command): bool
    {
        // List of dangerous commands that should not be executed
        $dangerousCommands = [
            'migrate:fresh',
            'migrate:reset',
            'db:wipe',
            'down',
            'key:generate',
            'config:clear',
            'cache:clear',
            'route:clear',
            'view:clear'
        ];
        
        foreach ($dangerousCommands as $dangerous) {
            if (str_starts_with($command, $dangerous)) {
                Log::warning('CommandScheduleService: Blocked dangerous command', [
                    'command' => $command,
                    'blocked_pattern' => $dangerous
                ]);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Get rules that are due to run
     *
     * @return \Illuminate\Database\Eloquent\Collection<CommandScheduleRule>
     */
    public function getDueRules(): \Illuminate\Database\Eloquent\Collection
    {
        return CommandScheduleRule::where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('next_run_at')
                      ->orWhere('next_run_at', '<=', Carbon::now());
            })
            ->orderBy('priority')
            ->orderBy('created_at')
            ->get();
    }
    
    /**
     * Check dependencies and return rules ready to execute
     *
     * @param \Illuminate\Database\Eloquent\Collection<CommandScheduleRule> $dueRules
     * @return \Illuminate\Database\Eloquent\Collection<CommandScheduleRule>
     */
    public function filterRulesReadyToExecute(\Illuminate\Database\Eloquent\Collection $dueRules): \Illuminate\Database\Eloquent\Collection
    {
        return $dueRules->filter(function (CommandScheduleRule $rule) {
            // If no dependency, rule is ready
            if (empty($rule->depends_on_command)) {
                return true;
            }
            
            // Check if dependency command has run recently
            $dependencyRule = CommandScheduleRule::where('command', $rule->depends_on_command)
                ->where('is_active', true)
                ->first();
                
            if (!$dependencyRule) {
                Log::warning('CommandScheduleService: Dependency command not found', [
                    'rule_id' => $rule->id,
                    'rule_name' => $rule->name,
                    'depends_on_command' => $rule->depends_on_command
                ]);
                return false;
            }
            
            // Check if dependency has run and delay period has passed
            if ($dependencyRule->next_run_at && $rule->delay_after_dependency > 0) {
                $delayUntil = $dependencyRule->next_run_at->addSeconds($rule->delay_after_dependency);
                if (Carbon::now()->lessThan($delayUntil)) {
                    return false;
                }
            }
            
            return true;
        });
    }
    
    /**
     * Validate schedule rule configuration
     *
     * @param array $data
     * @return array{valid: bool, errors: array}
     */
    public function validateScheduleRule(array $data): array
    {
        $errors = [];
        
        // Validate command safety
        if (isset($data['command']) && !$this->isCommandSafe($data['command'])) {
            $errors[] = 'Command is not safe to execute via scheduler';
        }
        
        // Validate schedule expression
        if (isset($data['schedule_expression']) && isset($data['schedule_type'])) {
            if (!$this->isValidScheduleExpression($data['schedule_expression'], $data['schedule_type'])) {
                $errors[] = 'Invalid schedule expression for the specified schedule type';
            }
        }
        
        // Validate timezone
        if (isset($data['timezone']) && !in_array($data['timezone'], timezone_identifiers_list())) {
            $errors[] = 'Invalid timezone specified';
        }
        
        // Validate execution time
        if (isset($data['max_execution_time']) && ($data['max_execution_time'] < 1 || $data['max_execution_time'] > 7200)) {
            $errors[] = 'Max execution time must be between 1 and 7200 seconds';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Validate schedule expression format
     *
     * @param string $expression
     * @param string $type
     * @return bool
     */
    private function isValidScheduleExpression(string $expression, string $type): bool
    {
        switch ($type) {
            case 'cron':
                try {
                    new CronExpression($expression);
                    return true;
                } catch (\Exception $e) {
                    return false;
                }
                
            case 'daily_at':
                return preg_match('/^\d{1,2}:\d{2}$/', $expression) ||
                       preg_match('/^\d+\s+\d+\s+\*\s+\*\s+\*$/', $expression);
                
            case 'weekly_at':
                return preg_match('/^\d+\s+\d+\s+\*\s+\*\s+\d+$/', $expression);
                
            default:
                return true; // Custom types are assumed valid
        }
    }
} 
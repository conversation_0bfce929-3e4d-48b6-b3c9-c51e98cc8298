<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\EvaluationSchemaOption
 *
 * @property int $id
 * @property int $evaluation_schema_id
 * @property string $code
 * @property string $title
 * @property string|null $description
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $extra_field
 * @property-read \App\EvaluationSchema $info
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchemaOption newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchemaOption newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchemaOption query()
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchemaOption whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchemaOption whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchemaOption whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchemaOption whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchemaOption whereEvaluationSchemaId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchemaOption whereExtraField($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchemaOption whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchemaOption whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchemaOption whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class EvaluationSchemaOption extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'evaluation_schema_options';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = [
                'evaluation_schema_id',
                'code',
                'title',
                'description',
                'extra_field',
                'weight'
    ];

    public function info()
    {
        return $this->belongsTo('App\EvaluationSchema', 'evaluation_schema_id', 'id');
    }
}

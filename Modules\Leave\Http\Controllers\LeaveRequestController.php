<?php

namespace Modules\Leave\Http\Controllers;

use App\ApiBaseMethod;
use App\GeneralSettings;
use App\LeaveDefine;
use App\LeaveRequest;
use App\LeaveType;
use App\Repositories\UserRepository;
use App\Traits\Notification;
use App\Traits\PdfGenerate;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Employee;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Modules\Leave\Entities\ApplyLeave;
use Modules\Leave\Http\Requests\EmployeeApplyLeaveStoreRequest;
use Modules\Leave\Repositories\LeaveRepository;
use Modules\Leave\Repositories\LeaveTypeRepository;
use Modules\Setup\Repositories\DepartmentRepository;
use Modules\UserActivityLog\Traits\LogActivity;

class LeaveRequestController extends Controller
{
    use Notification, PdfGenerate;
    private $leaveRepository, $userRepository, $deptRepo,$leaveTypeRepository;

    public function __construct(LeaveRepository $leaveRepository, UserRepository $userRepository,DepartmentRepository $deptRepo,LeaveTypeRepository $leaveTypeRepository)
    {
        $this->leaveRepository = $leaveRepository;
        $this->userRepository = $userRepository;
        $this->deptRepo = $deptRepo;
        $this->leaveTypeRepository = $leaveTypeRepository;
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        try {



            $apply_leaves = $this->leaveRepository->all(['leave_type']);
            $apply_leave_histories = $this->leaveRepository->user_leave_history(Auth::id());
            $total_leave = $this->leaveRepository->totalLeave(Auth::user());
            $my_leaves = LeaveDefine::where('employee_id', auth()->user()->id)->with('leaveRequests')->with('leaveType')->get();


            return view('leave::apply_leave', [
                'my_leaves' => $my_leaves,
                'apply_leaves' => $apply_leaves,
                'apply_leave_histories' => $apply_leave_histories,
                'total_leave' => $total_leave,
                'types' => $this->leaveTypeRepository->activeTypes(),
                'users' =>$this->userRepository->normalUser(),

            ]);
        } catch (\Exception $e) {


            dd($e->getMessage());
            LogActivity::errorLog($e->getMessage());
            Toastr::error('Operation failed');
            return back();
        }



        try {

            $user = Auth::user();
            if ($user) {
                $my_leaves = LeaveDefine::where('user_id', $user->id)->where('role_id', getMaxLeaveDaysRoleId($user->id))->with('leaveRequests')->get();
                $apply_leaves = LeaveRequest::where('role_id', getMaxLeaveDaysRoleId($user->id))
                   ->has('leaveDefine')->where('employee_id',Auth::user()->id)->get();
                $leave_types = $my_leaves;
            } else {
                $my_leaves = LeaveDefine::where('user_id', $user->id)->where('role_id', $request->role_id)->get();
                $apply_leaves = LeaveRequest::where('role_id', $request->role_id)->get();
                $leave_types = $my_leaves;
            }



            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['my_leaves'] = $my_leaves->toArray();
                $data['apply_leaves'] = $apply_leaves->toArray();
                $data['leave_types'] = $leave_types->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            
//            $my_leaves = LeaveType::all();
//            $leave_types = $my_leaves;
//            $apply_leaves = LeaveRequest::where('employee_id', Auth::user()->id)
//               ->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['my_leaves'] = $my_leaves->toArray();
                $data['apply_leaves'] = $apply_leaves->toArray();
                $data['leave_types'] = $leave_types->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }





            return view('leave::apply_leave', compact('apply_leaves', 'leave_types', 'my_leaves'));


        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function store(EmployeeApplyLeaveStoreRequest $request)
    {







        try {
            $user = $this->userRepository->findUser($request->user);
            if ($user->leave_applicable_date <= Carbon::parse($request->start_date)->format('Y-m-d')) {
                $apply_leave = $this->leaveRepository->create($request->except("_token"));

                // inform super admin/managing director and HR manager
                $users= Employee::whereHas('roles', function ($q) {
                    return $q->where('name', 'like', 'human-resource_%_')->orWhere('name', 'like', 'managing-director_%_');
                })->with(['roles' => function ($query) {
                    $query->select('id');
                }])->where('id','!=',auth()->user()->id)
                    ->get(['id']);
                $class = $apply_leave;

                $content = __('notification.A Leave Has been Applied By ') . $apply_leave->user->name;
                $subject = __('notification.Leave Reminder');
                $route = route('leave.pending_index');

//                $this->sendNotification($class, app('general_setting')->email,$subject, $content, app('s')->phone, $content, $users, $role_id=null, $route);
                $this->sendNotification($class, '<EMAIL>',$subject, $content, '01111709378', $content, $users, $role_id=null, $route);

                LogActivity::successLog('Leave Apply has been submitted.');
                return response()->json([
                    'success' => trans("leave.Leave Apply has been submitted Successfully"),
                    'table' => $this->table(),
                ]);
            } else {
                return response()->json([
                    'error' => trans("leave.User is not Permitted for leave yet")
                ]);
            }
        } catch (\Exception $e) {

            dd($e->getMessage());
            LogActivity::errorLog($e->getMessage() . ' - Error has been detected for Applying Leave');
            return response()->json(["message" => __('common.Something Went Wrong')], 503);
        }
    }
    public function table()
    {
        $apply_leaves = $this->leaveRepository->all();

        return (string)view('leave::apply_leaves.table', compact('apply_leaves'));
    }



    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {


        try {
            $user = Auth::user();
            if ($user) {
                $my_leaves = LeaveDefine::where('user_id', $user->id)->where('role_id', $user->role_id)->get();
                $apply_leaves = LeaveRequest::where('role_id', $user->role_id)->get();
                $leave_types = LeaveDefine::where('role_id', $user->role_id)->get();
            } else {
                $my_leaves = LeaveDefine::where('role_id', $request->role_id)->get();
                $apply_leaves = LeaveRequest::where('role_id', $request->role_id)->get();
                $leave_types = LeaveDefine::where('role_id', $request->role_id)->get();
            }

            $apply_leave = LeaveRequest::find($id);

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['my_leaves'] = $my_leaves->toArray();
                $data['apply_leaves'] = $apply_leaves->toArray();
                $data['leave_types'] = $leave_types->toArray();
                $data['apply_leave'] = $apply_leave->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }

            return view('backEnd.humanResource.apply_leave', compact('apply_leave', 'apply_leaves', 'leave_types', 'my_leaves'));
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function update(Request $request)
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        $input = $request->all();
        if (ApiBaseMethod::checkUrl($request->fullUrl())) {
            $validator = Validator::make($input, [
                'id' => "required",
                'apply_date' => "required",
                'leave_type' => "required",
                'leave_from' => 'required|before_or_equal:leave_to',
                'leave_to' => "required",
                'login_id' => "required",
                'role_id' => "required",
                'file' => "sometimes|nullable|mimes:pdf,doc,docx,jpg,jpeg,png,txt",
            ]);
        } else {
            $validator = Validator::make($input, [
                'apply_date' => "required",
                'leave_type' => "required",
                'leave_from' => 'required|before_or_equal:leave_to',
                'leave_to' => "required",
                'file' => "sometimes|nullable|mimes:pdf,doc,docx,jpg,jpeg,png,txt",
            ]);
        }

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }


        try {
            $fileName = "";
            if ($request->file('file') != "") {
                $maxFileSize = GeneralSettings::first('file_size')->file_size;
                $file = $request->file('file');
                $fileSize = filesize($file);
                $fileSizeKb = ($fileSize / 1000000);
                if ($fileSizeKb >= $maxFileSize) {
                    Toastr::error('Max upload file size ' . $maxFileSize . ' Mb is set in system', 'Failed');
                    return redirect()->back();
                }
                $apply_leave = LeaveRequest::find($request->id);
                if (file_exists($apply_leave->file)) unlink($apply_leave->file);
                $file = $request->file('file');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/leave_request/', $fileName);
                $fileName = 'public/uploads/leave_request/' . $fileName;
            }


            $user = Auth()->user();

            if ($user) {
                $login_id = $user->id;
                $role_id = $user->role_id;
            } else {
                $login_id = $request->login_id;
                $role_id = $request->role_id;
            }

            $apply_leave = LeaveRequest::find($request->id);
            $apply_leave->employee_id = $login_id;
            $apply_leave->role_id = $role_id;
            $apply_leave->apply_date = date('Y-m-d', strtotime($request->apply_date));
            $apply_leave->leave_define_id = $request->leave_type;
            $apply_leave->leave_from = date('Y-m-d', strtotime($request->leave_from));
            $apply_leave->leave_to = date('Y-m-d', strtotime($request->leave_to));
            $apply_leave->approve_status = 'P';
            $apply_leave->reason = $request->reason;
            if ($fileName != "") {
                $apply_leave->file = $fileName;
            }
            $result = $apply_leave->save();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($result) {
                    return ApiBaseMethod::sendResponse(null, 'Leave Request has been updated successfully');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
                }
            } else {
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect('apply-leave');
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function viewLeaveDetails(Request $request, $id)
    {

        try {
            $leaveDetails = LeaveRequest::find($id);

            $apply = "";

            // $apply_leaves = LeaveRequest::all();
            // $leave_types = LeaveType::all();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['leaveDetails'] = $leaveDetails->toArray();
                $data['apply'] = $apply;
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('leave.humanResource.viewLeaveDetails', compact('leaveDetails', 'apply'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {



        try {
            $this->leaveRepository->delete($id);
            LogActivity::successLog("Apply Leave Deleted Successfully");
            Toastr::success(__('leave.Apply Leave Deleted Successfully'), __('common.Success'));
            return back();
        } catch (\Exception $e) {
            LogActivity::errorLog($e->getMessage());
            Toastr::error(__('common.Something Went Wrong'), __('common.Success'));
            return back();
        }
    }

    public function pending_index()
    {



        $pending_leaves = $this->leaveRepository->pending_all();

//        return view('leave::apply_approvals.pending_list', [
        return view('leave::approveLeaveRequest', [
            'pending_leaves' => $pending_leaves,

        ]);




    }
}

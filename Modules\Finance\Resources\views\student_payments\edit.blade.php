@extends('layouts.hound')

@section('mytitle', 'Edit Student Payment')

@section('content')
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-default">
                    <div class="panel-heading">Edit Student Payment {{ $payment->id }}</div>
                    <div class="panel-body">

                        @if ($errors->any())
                            <ul class="alert alert-danger">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        @endif

                        {!! Form::model($payment, [
                            'method' => 'PATCH',
                            'route' => ['student_payments.update', $payment->id],
                            'class' => 'form-horizontal',
                            'files' => true
                        ]) !!}

                        @include ('finance::student_payments.form', ['submitButtonText' => 'Update'])

                        {!! Form::close() !!}

                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
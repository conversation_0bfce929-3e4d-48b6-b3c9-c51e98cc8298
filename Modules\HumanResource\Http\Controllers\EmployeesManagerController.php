<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Admission;
use App\ApiBaseMethod;
use App\Attendance;
use App\BankAccount;
use App\BankAccountType;
use App\BaseSetup;
use App\Center;
use App\CenterEmployee;
use App\CenterTeacher;
use App\CenterTranslation;
use App\Classes;
use App\ClassTeacher;
use App\GeneralSettings;
use App\LeaveRequest;
use App\MissedClockOut;
use App\Notifications\UserStatusChangedToNewApplication;
use App\Notifications\WelcomeMailtoNewEmployeeNotification;
use App\PublicHoliday;
use App\Role;
use App\Employee;

use App\Student;
use App\Document;
use App\User;
use App\WeekDay;
use App\Weekend;
use App\YearCheck;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use DB;
use App\Permission;
use App\EmployeeSalary;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\EmployeeTimetable;
use App\Http\Controllers\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Modules\Admission\Notifications\StudentOfffered;
use Modules\HumanResource\Http\Requests\EmployeeStoreRequest;
use Modules\HumanResource\Http\Requests\EmployeeUpdateRequest;
use PhpOffice\PhpSpreadsheet\Calculation\DateTimeExcel\WorkDay;


class EmployeesManagerController extends Controller
{



    public function __invoke()
    {

        $employees = Employee::orderby('full_name', 'asc')->get();
        $employee = new Employee;
        $employee = $employee->getHTML($employees);


    }


}

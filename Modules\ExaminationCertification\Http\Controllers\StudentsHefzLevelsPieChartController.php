<?php

namespace Modules\ExaminationCertification\Http\Controllers;

use App\Attendance;
use App\AttendanceOption;
use App\Student;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\ExaminationCertification\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentAttendance;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class StudentsHefzLevelsPieChartController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function __invoke(Request $request)
    {



        $record = StudentHefzPlan::select("COUNT(*) as count", "level_id as level")
//            ->where('created_at', '>', Carbon::today()->subDay(6))
//            ->groupBy('level','day')
            ->whereNotnuLL('level_id')
            ->groupBy('level')
            ->orderByRaw('COUNT(*) desc')
            ->get();






        $data = [];

        foreach ($record as $row) {
            $data['label'][] = $row->level;
            $data['data'][] = (int)$row->count;
        }

        $data['chart_data'] = json_encode($data);
        return response()->json($data);

    }
}

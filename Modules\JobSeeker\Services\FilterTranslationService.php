<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Mo<PERSON>les\JobSeeker\Entities\JobCategory;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * FilterTranslationService
 * 
 * Handles translation between canonical job categories (UI) and provider-specific formats
 * ensuring that job fetching APIs receive the correct category identifiers.
 */
final class FilterTranslationService
{
    private const CACHE_TTL = 3600; // 1 hour
    private const CACHE_PREFIX = 'filter_translation:';

    /**
     * Translate canonical category IDs to Jobs.af category names
     *
     * @param array $canonicalCategoryIds
     * @return array Jobs.af category names
     * @deprecated This method is kept for backward compatibility. New implementations should use ProviderJobCategory table.
     */
    public function translateToJobsAfCategories(array $canonicalCategoryIds): array
    {
        if (empty($canonicalCategoryIds)) {
            return [];
        }

        Log::warning('FilterTranslationService: Using legacy config-based translation for Jobs.af', [
            'canonical_category_ids' => $canonicalCategoryIds,
            'recommendation' => 'Consider migrating to ProviderJobCategory table for dynamic mapping'
        ]);

        $cacheKey = self::CACHE_PREFIX . 'jobsaf:' . md5(implode(',', $canonicalCategoryIds));
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($canonicalCategoryIds) {
            try {
                // Get canonical categories
                $canonicalCategories = JobCategory::whereIn('id', $canonicalCategoryIds)
                    ->where('is_canonical', true)
                    ->where('is_active', true)
                    ->pluck('name')
                    ->toArray();

                // Get Jobs.af category mapping from config
                $jobsAfCategories = config('jobseeker.jobs_af_default_filters.searchFilters.categories', []);
                
                // Map canonical names to Jobs.af categories
                $result = [];
                foreach ($canonicalCategories as $canonicalName) {
                    // Direct match first
                    if (in_array($canonicalName, $jobsAfCategories)) {
                        $result[] = $canonicalName;
                        continue;
                    }
                    
                    // Fuzzy matching for variations
                    foreach ($jobsAfCategories as $jobsAfCategory) {
                        if ($this->isCategoryMatch($canonicalName, $jobsAfCategory)) {
                            $result[] = $jobsAfCategory;
                            break;
                        }
                    }
                }

                // Remove duplicates and return
                $result = array_unique($result);
                
                Log::info('FilterTranslationService: Legacy Jobs.af translation completed', [
                    'canonical_ids' => $canonicalCategoryIds,
                    'canonical_names' => $canonicalCategories,
                    'jobsaf_categories' => $result
                ]);

                return $result;

            } catch (\Exception $e) {
                Log::error('FilterTranslationService: Error in legacy Jobs.af translation', [
                    'canonical_ids' => $canonicalCategoryIds,
                    'error' => $e->getMessage()
                ]);
                
                return [];
            }
        });
    }

    /**
     * Translate canonical category IDs to ACBAR category IDs
     *
     * @param array $canonicalCategoryIds
     * @return array ACBAR category IDs
     * @deprecated This method is kept for backward compatibility. New implementations should use ProviderJobCategory table.
     */
    public function translateToAcbarCategories(array $canonicalCategoryIds): array
    {
        if (empty($canonicalCategoryIds)) {
            return [];
        }

        Log::warning('FilterTranslationService: Using legacy config-based translation for ACBAR', [
            'canonical_category_ids' => $canonicalCategoryIds,
            'recommendation' => 'Consider migrating to ProviderJobCategory table for dynamic mapping'
        ]);

        $cacheKey = self::CACHE_PREFIX . 'acbar:' . md5(implode(',', $canonicalCategoryIds));
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($canonicalCategoryIds) {
            try {
                // Get reverse mapping from ACBAR config (ACBAR ID -> Canonical ID)
                $acbarToCanonicalMapping = config('jobseeker.acbar_default_filters.category_mapping', []);
                
                // Create reverse mapping (Canonical ID -> ACBAR IDs)
                $canonicalToAcbarMapping = [];
                foreach ($acbarToCanonicalMapping as $acbarId => $canonicalId) {
                    if (!isset($canonicalToAcbarMapping[$canonicalId])) {
                        $canonicalToAcbarMapping[$canonicalId] = [];
                    }
                    $canonicalToAcbarMapping[$canonicalId][] = $acbarId;
                }

                // Translate canonical IDs to ACBAR IDs
                $result = [];
                foreach ($canonicalCategoryIds as $canonicalId) {
                    if (isset($canonicalToAcbarMapping[$canonicalId])) {
                        $result = array_merge($result, $canonicalToAcbarMapping[$canonicalId]);
                    }
                }

                // Remove duplicates and convert to integers
                $result = array_unique(array_map('intval', $result));
                
                Log::info('FilterTranslationService: Legacy ACBAR translation completed', [
                    'canonical_ids' => $canonicalCategoryIds,
                    'acbar_category_ids' => $result
                ]);

                return $result;

            } catch (\Exception $e) {
                Log::error('FilterTranslationService: Error in legacy ACBAR translation', [
                    'canonical_ids' => $canonicalCategoryIds,
                    'error' => $e->getMessage()
                ]);
                
                return [];
            }
        });
    }

    /**
     * Translate Jobs.af category names back to canonical category IDs (for edit mode)
     *
     * @param array $jobsAfCategories
     * @return array Canonical category IDs
     */
    public function translateFromJobsAfCategories(array $jobsAfCategories): array
    {
        if (empty($jobsAfCategories)) {
            return [];
        }

        try {
            $canonicalCategories = JobCategory::where('is_canonical', true)
                ->where('is_active', true)
                ->get(['id', 'name']);

            $result = [];
            foreach ($jobsAfCategories as $jobsAfCategory) {
                foreach ($canonicalCategories as $canonical) {
                    if ($this->isCategoryMatch($canonical->name, $jobsAfCategory)) {
                        $result[] = $canonical->id;
                        break;
                    }
                }
            }

            return array_unique($result);

        } catch (\Exception $e) {
            Log::error('FilterTranslationService: Error translating from Jobs.af categories', [
                'jobsaf_categories' => $jobsAfCategories,
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * Translate ACBAR category IDs back to canonical category IDs (for edit mode)
     *
     * @param array $acbarCategoryIds
     * @return array Canonical category IDs
     */
    public function translateFromAcbarCategories(array $acbarCategoryIds): array
    {
        if (empty($acbarCategoryIds)) {
            return [];
        }

        try {
            $acbarToCanonicalMapping = config('jobseeker.acbar_default_filters.category_mapping', []);
            
            $result = [];
            foreach ($acbarCategoryIds as $acbarId) {
                if (isset($acbarToCanonicalMapping[$acbarId])) {
                    $result[] = $acbarToCanonicalMapping[$acbarId];
                }
            }

            return array_unique($result);

        } catch (\Exception $e) {
            Log::error('FilterTranslationService: Error translating from ACBAR categories', [
                'acbar_category_ids' => $acbarCategoryIds,
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * Check if two category names match (handles variations)
     *
     * @param string $name1
     * @param string $name2
     * @return bool
     */
    private function isCategoryMatch(string $name1, string $name2): bool
    {
        // Normalize names for comparison
        $normalized1 = strtolower(trim($name1));
        $normalized2 = strtolower(trim($name2));
        
        // Exact match
        if ($normalized1 === $normalized2) {
            return true;
        }
        
        // Handle common variations
        $variations = [
            'information technology' => ['it - software', 'it', 'software', 'computer science'],
            'management' => ['leadership', 'executive'],
            'human resources' => ['hr', 'human resource'],
            'sales/marketing' => ['sales', 'marketing'],
            'research/survey' => ['research', 'survey'],
            'security/safety' => ['security', 'safety']
        ];
        
        foreach ($variations as $canonical => $variants) {
            if ($normalized1 === $canonical && in_array($normalized2, $variants)) {
                return true;
            }
            if ($normalized2 === $canonical && in_array($normalized1, $variants)) {
                return true;
            }
            if (in_array($normalized1, $variants) && in_array($normalized2, $variants)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Clear translation cache
     *
     * @return void
     */
    public function clearCache(): void
    {
        try {
            // Clear all translation caches
            Cache::forget(self::CACHE_PREFIX . 'jobsaf:*');
            Cache::forget(self::CACHE_PREFIX . 'acbar:*');
            
            Log::info('FilterTranslationService: Translation cache cleared');
            
        } catch (\Exception $e) {
            Log::warning('FilterTranslationService: Error clearing cache', [
                'error' => $e->getMessage()
            ]);
        }
    }
} 
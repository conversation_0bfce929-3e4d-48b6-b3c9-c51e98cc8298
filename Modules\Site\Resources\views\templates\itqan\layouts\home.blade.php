<!DOCTYPE html>
<!--[if IE 8]>			<html class="ie ie8"> <![endif]-->
<!--[if IE 9]>			<html class="ie ie9"> <![endif]-->
<!--[if gt IE 9]><!-->	<html>
<!--<![endif]-->
	<head>

		<meta charset="utf-8" />

		<title>@yield('page_title') :: {{cache('title_'.App::getLocale())}}</title>

		<meta name="keywords" content="{{cache('system_key_words')}}" />

		<meta name="description" content="{{cache('description_'.App::getLocale())}}" />

		<meta name="Author" content="IT4OMAN" />

		<!-- mobile settings -->
		<meta name="viewport" content="width=device-width, maximum-scale=1, initial-scale=1, user-scalable=0" />
		<!--[if IE]><meta http-equiv='X-UA-Compatible' content='IE=edge,chrome=1'><![endif]-->

		<!-- WEB FONTS : use %7C instead of | (pipe) -->
		<link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400%7CRaleway:300,400,500,600,700%7CLato:300,400,400italic,600,700" rel="stylesheet" type="text/css" />

		<link rel="icon" type="image/png" href="{{URL::to('assets/templates/itqan/images/sys_icons/icon(1).ico')}}" sizes="16x16">
		<link rel="icon" type="image/png" href="{{URL::to('assets/templates/itqan/images/sys_icons/icon(1).ico')}}" sizes="32x32">

        @include('site::templates.'.$theme.'.partial.header')
	</head>

	<body class="smoothscroll enable-animation helvetica" style="@if(App::getLocale()=="ar") direction: rtl; @endif" >

		<!-- wrapper E7E99D-->
		<div id="wrapper">

			<!--Menu-->
			@include('site::templates.'.$theme.'.partial.menu')

			<!--Flash messages-->
			<div class="flash-message">
				@foreach (['danger', 'warning', 'success', 'info'] as $msg)
					@if(Session::has('alert-' . $msg))
						<p style="position:fixed;z-index: 999;left: 2%;width: auto;" class="center-block alert alert-{{$msg}}">
							<?php $errors_msgs=Session::pull('alert-' . $msg); ?>
							@if(is_array($errors_msgs))
								@foreach ($errors_msgs as $errors_msg)
									 {{$errors_msg}}<br/>
								@endforeach
							@else
								{{$errors_msgs}}
							@endif
							<a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
						</p>
					@endif
				@endforeach
			</div>


			<!--CONTENT-->
			@yield('content')

			<!-- FOOTER -->
			@include('site::templates.'.$theme.'.partial.footer')

		</div>
		<!-- /wrapper -->


		<!-- SCROLL TO TOP -->
		<a href="#" id="toTop"></a>


		<!-- PRELOADER -->
		<div id="preloader">
			<div class="inner">
				<span class="loader"></span>
			</div>
		</div><!-- /PRELOADER -->

		<!-- JAVASCRIPT FILES -->
		@include('site::templates.'.$theme.'.partial.scripts')

		@yield('afterscripts')


	</body>
</html>
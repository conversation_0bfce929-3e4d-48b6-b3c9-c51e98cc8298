<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class EmployeesAttendanceExport implements FromArray, WithHeadings
{
    protected $data;

    /**
     * Pass the formatted data from the controller.
     *
     * @param array $data
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Convert the grouped data into a flat array with headers.
     *
     * @return array
     */
    public function array(): array
    {
        $rows = [];
        foreach ($this->data as $employeeData) {
            // Add a header row for the employee
            $rows[] = ['Employee: ' . $employeeData['employee']['full_name'] . ' (ID: ' . $employeeData['employee']['id'] . ')'];
            // Then the column headers for daily records
            $rows[] = [
                'Date',
                'Day',
                'Status',
                'In Time',
                'Out Time',
                'Hours Worked',
                'Volunteer Hours',
                'Salary %'
            ];
            foreach ($employeeData['details'] as $daily) {
                $rows[] = [
                    $daily['formatted_date'],  // Using array notation instead of object notation
                    $daily['day_name'],        // Use "day_name" from stored procedure (abbreviated day)
                    $daily['status'],
                    $daily['in_time'],
                    $daily['out_time'],
                    $daily['hours_worked'],
                    $daily['volunteer_hours'],
                    $daily['salary_percentage']
                ];
            }
            // Optionally add a blank row between employees
            $rows[] = [];
        }
        return $rows;
    }

    /**
     * Optional headings (if needed).
     *
     * @return array
     */
    public function headings(): array
    {
        return []; // Already handled in the rows
    }
}

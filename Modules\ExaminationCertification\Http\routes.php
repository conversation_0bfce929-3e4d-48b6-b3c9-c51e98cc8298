<?php




use Illuminate\Support\Facades\Password;

Route::group(['middleware' => ['web', 'auth:employee','missedClockOutMiddleware'], 'prefix' => 'workplace/examinationcertification', 'namespace' => 'Modules\ExaminationCertification\Http\Controllers'], function () {



//    Route::get('examination-list', 'ExaminationListController@index')->name('examination-list')->middleware('userRolePermission:49');
//    Route::get('exams-list', 'ExamController@index')->name('exams-list');
//    Route::get('exams-list/data', 'ExamController@data')->name('exams.data');
//    Route::post('examination-list', ['as' => 'examination_list_post', 'uses' => 'ExaminationListController@examinationListSearch']);
//    Route::post('student-certificate', 'StudentCertificateController@store')->name('student-certificate')->middleware(['role:superviser_2_|managing-director_2_']);
//    Route::get('student-certificate/{id}', 'StudentCertificateController@edit')->name('student-certificate-edit')->middleware(['role:superviser_2_|managing-director_2_']);
//    Route::put('student-certificate/{id}', 'StudentCertificateController@update')->name('student-certificate-update')->middleware(['role:superviser_2_|managing-director_2_']);
//    Route::delete('student-certificate/{id}', 'StudentCertificateController@destroy')->name('student-certificate-delete')->middleware(['role:superviser_2_|managing-director_2_']);
//    Route::get('student-certificate', 'StudentCertificateController@index')->name('student-certificate')->middleware(['role:teacher_2_|superviser_2_|managing-director_2_']);
//    Route::get('/certificates', 'CertificateController@index');
//    Route::get('generate-certificate', ['as' => 'generate_certificate', 'uses' => 'StudentCertificateController@generateCertificate']);
//    Route::get('issued-certificate', ['as' => 'issued_certificate', 'uses' => 'StudentIssuedCertificateController@index']);
//        Route::post('generate-certificate', ['as' => 'generate_certificate', 'uses' => 'StudentCertificateController@generateCertificateSearch']);
        // print certificate
//        Route::get('generate-certificate-print/{s_id}/{c_id}', ['as' => 'student_certificate_generate', 'uses' => 'StudentCertificateController@generateCertificateGenerate']);




//        Route::get('remove-students-from-examination-list/{s_id}/', ['as' => 'remove.students.from.examination.list', 'uses' => 'ExaminationListController@removeStudentsFromExaminationList']);
//

    // Online Exam
    // Route::resource('online-exam', 'OnlineExamController')->middleware('userRolePermission:238');
//    Route::get('online-exam', 'OnlineExamController@index')->name('online-exam')->middleware('userRolePermission:238');
//    Route::get('online-exam/class/{classId}/student/{studentId}', 'AccessStudentOnlineExamsPerClassController')->name('online-exam.class.student');
//    Route::get('online-exam/student/{studentId}/class/{classId}', 'AccessStudentOnlineExamsController')->name('online-exam.student');
//    Route::get('online-exam/class/{classId}', 'AccessClassOnlineExamsController')->name('online-exam.class');
//    Route::post('online-exam', 'OnlineExamController@store')->name('online-exam')->middleware('userRolePermission:239');
//    Route::post('online-exam', 'OnlineExamController@store')->name('online-exam');
//    Route::get('online-exam/{id}', 'OnlineExamController@edit')->name('online-exam-edit')->middleware('userRolePermission:240');
//    Route::get('online-exam/{id}/class/{classId}', 'OnlineExamController@edit')->name('online-exam-edit');
//    Route::get('view-online-exam-question/{id}', 'OnlineExamController@viewOnlineExam')->name('online-exam-question-view')->middleware('userRolePermission:238');
//    Route::put('online-exam/{id}', 'OnlineExamController@update')->name('online-exam-update');
    // Route::delete('online-exam/{id}', 'OnlineExamController@delete')->name('online-exam-delete')->middleware('userRolePermission:241');

//    Route::post('online-exam-delete', 'OnlineExamController@delete')->name('online-exam-delete');
//    Route::get('manage-online-exam-question/{id}', ['as' => 'manage_online_exam_question', 'uses' => 'OnlineExamController@manageOnlineExamQuestion'])->middleware('userRolePermission:242');
//    Route::post('online_exam_question_store', ['as' => 'online_exam_question_store', 'uses' => 'OnlineExamController@manageOnlineExamQuestionStore']);

//    Route::get('online-exam-publish/{id}', ['as' => 'online_exam_publish', 'uses' => 'OnlineExamController@onlineExamPublish']);
//    Route::get('online-exam-publish-cancel/{id}', ['as' => 'online_exam_publish_cancel', 'uses' => 'OnlineExamController@onlineExamPublishCancel']);

//    Route::get('online-question-edit/{id}/{type}/{examId}', 'OnlineExamController@onlineQuestionEdit');
//    Route::post('online-exam-question-edit', ['as' => 'online_exam_question_edit', 'uses' => 'OnlineExamController@onlineExamQuestionEdit']);
//    Route::post('online-exam-question-delete', 'OnlineExamController@onlineExamQuestionDelete')->name('online-exam-question-delete');

    // store online exam question
//    Route::post('online-exam-question-assign', ['as' => 'online_exam_question_assign', 'uses' => 'OnlineExamController@onlineExamQuestionAssign']);

//    Route::get('view_online_question_modal/{id}', ['as' => 'view_online_question_modal', 'uses' => 'OnlineExamController@viewOnlineQuestionModal']);


    // Online exam marks
//    Route::get('online-exam-marks-register/{id}', ['as' => 'online_exam_marks_register', 'uses' => 'OnlineExamController@onlineExamMarksRegister'])->middleware('userRolePermission:243');

    // Route::post('online-exam-marks-store', ['as' => 'online_exam_marks_store', 'uses' => 'OnlineExamController@onlineExamMarksStore']);
//    Route::get('online-exam-result/{id}', ['as' => 'online_exam_result', 'uses' => 'OnlineExamController@onlineExamResult'])->middleware('userRolePermission:244');

//    Route::get('online-exam-marking/{exam_id}/{s_id}', ['as' => 'online_exam_marking', 'uses' => 'OnlineExamController@onlineExamMarking']);
//    Route::post('online-exam-marks-store', ['as' => 'online_exam_marks_store', 'uses' => 'OnlineExamController@onlineExamMarkingStore']);
//    Route::resource('program-minimum-score', 'ProgramMinimumScoreToPassController');



//    Route::post('program-marks-grade', 'MarksGradeController@store')->name('marks-grade-post');
//    Route::get('program-marks-grade/{id}/{program}', 'MarksGradeController@show')->name('marks-grade-edit');
//    Route::put('program-marks-grade/{id}/{program}', 'MarksGradeController@update')->name('marks-grade-update');
//    Route::delete('program-marks-grade/{id}/{program}', 'MarksGradeController@destroy')->name('marks-grade-delete');
//
//    Route::resource('education-calendar', 'AcademicCalendarController');
//    Route::resource('education-calendar-ajax', 'AcademicCalendarAjaxController');
//    Route::post('add-new-class-routine-store', 'ClassRoutineNewController@addNewClassRoutineStore');
//    Route::get('get-class-teacher-ajax', 'ClassRoutineNewController@getClassTeacherAjax');
//    Route::get('add-new-class-routine-store', 'ClassRoutineNewController@classRoutineSearch');
//    Route::get('edit-class-routine/{class_time_id}/{day}/{class_id}/{section_id}/{subject_id}/{room_id}/{assigned_id}/{employee_id}', 'ClassRoutineNewController@addNewClassRoutineEdit');
//    Route::get('delete-class-routine-modal/{id}', 'ClassRoutineNewController@deleteClassRoutineModal');
//    Route::get('delete-class-routine/{id}', 'ClassRoutineNewController@deleteClassRoutine');
//    Route::get('class-routine-new/{class_id}/{section_id}', 'ClassRoutineNewController@classRoutineRedirect');
//    Route::post('class-routine-report', 'ClassRoutineNewController@classRoutineReportSearch')->name('class-routine-report');
//    Route::post('images/upload', 'ImageController@upload')->name('ckeditor.upload');
//    Route::get('class-routine-report', ['as' => 'class_routine_report', 'uses' => 'ClassRoutineNewController@classRoutineReport']);
//    Route::get('teacher-class-routine-report', ['as' => 'teacher_class_routine_report', 'uses' => 'ClassRoutineNewController@teacherClassRoutineReport']);
//    Route::post('teacher-class-routine-report', 'ClassRoutineNewController@teacherClassRoutineReportSearch')->name('teacher-class-routine-report');
//    Route::get('/', 'EducationController@index');
//
//    Route::resource('dailyreport', 'NewDailyClassReportController');
//
//    Route::put('update/student/exam/readiness', 'ClassesController@updateStudentExamReadiness')->name('classes.updateStudentExamReadiness')->middleware('permission:access classes');
//
//
//
//    Route::get('/get-highlighted-examed-days', function(){
//        //get data from database
//        $days = \App\OnlineExam::all();
//
//        //return data in json format
//        return response()->json($days);
//    })->name('get.highlighted.examed.days');




    Route::get('public-holidays', '\Modules\HumanResource\Http\Controllers\PublicHolidaysCalendar');
















    Route::get('classes-timetables', 'ClassesTimetablesController@index');

});

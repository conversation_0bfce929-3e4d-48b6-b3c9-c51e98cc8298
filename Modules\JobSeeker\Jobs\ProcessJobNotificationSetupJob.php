<?php

namespace Modules\JobSeeker\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\RateLimiter;
use Modules\JobSeeker\Entities\JobNotificationSetup;
use Modules\JobSeeker\Entities\JobNotificationSentJob;
use Modules\JobSeeker\Entities\JobNotificationLog;
use Modules\JobSeeker\Services\JobService;
use Modules\JobSeeker\Repositories\JobRepository;
use Carbon\Carbon;
use App\Services\EmailService;
use Illuminate\Support\Facades\DB;
use Modules\JobSeeker\Services\SystemErrorNotificationService;

class ProcessJobNotificationSetupJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * Rate limiting configuration to prevent thundering herd
     */
    private const RATE_LIMIT_KEY = 'job_notification_rate_limit';
    private const MAX_JOBS_PER_MINUTE = 30;
    private const RATE_LIMIT_DURATION = 60; // seconds

    /**
     * The setup ID to process
     *
     * @var int
     */
    protected $setupId;

    /**
     * Create a new job instance.
     *
     * @param int $setupId
     * @return void
     */
    public function __construct(int $setupId)
    {
        $this->setupId = $setupId;
        // Use default connection and queue for now to fix immediate issue
        // $this->connection = 'job_notifications';
        // $this->queue = 'setup_processors';
    }

    /**
     * Execute the job.
     *
     * @param JobService $jobService
     * @return void
     */
    public function handle(JobService $jobService)
    {
        try {
            // Implement rate limiting to prevent thundering herd
            if (!$this->acquireRateLimitSlot()) {
                // Release the job back to the queue with a delay if rate limit is exceeded
                $this->release(rand(30, 90)); // Random delay between 30-90 seconds
                Log::info("Rate limit exceeded, releasing job back to queue", [
                    'setup_id' => $this->setupId,
                    'delay' => '30-90 seconds'
                ]);
                return;
            }

            Log::info("Processing notification setup job", ['setup_id' => $this->setupId]);
            
            // Get the setup with its categories and recipients
            $setup = JobNotificationSetup::with(['categories', 'recipients', 'jobSeeker'])
                ->find($this->setupId);
            
            if (!$setup) {
                Log::error("Setup not found", ['setup_id' => $this->setupId]);
                return;
            }
            
            // Check if the setup is active
            if (!$setup->is_active) {
                Log::info("Setup is not active, skipping notification", ['setup_id' => $this->setupId]);
                return;
            }
            
            // Process the notification with idempotency check
            $result = $this->processSetupNotificationWithIdempotency($setup, $jobService);
            
            if ($result) {
                // Update last notified timestamp and increment sent count
                $setup->last_notified_at = now();
                $setup->incrementSentCount();
            
                Log::info("Setup notification processed successfully", ['setup_id' => $this->setupId]);
            } else {
                Log::warning("Failed to process setup notification", ['setup_id' => $this->setupId]);
            }
            
        } catch (\Exception $e) {
            Log::error("Error processing notification setup job", [
                'setup_id' => $this->setupId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Rethrow to mark job as failed in queue
            throw $e;
        }
    }

    /**
     * Acquire a rate limit slot using Laravel's built-in RateLimiter
     *
     * @return bool
     */
    private function acquireRateLimitSlot(): bool
    {
        try {
            // ** Task 7 Fix: Key is now per-setup and per-minute for granular rate limiting **
            $rateLimiterKey = self::RATE_LIMIT_KEY . ':' . $this->setupId . ':' . now()->format('Y-m-d-H-i');
            
            // Use Laravel's RateLimiter::attempt() for atomic, cross-process rate limiting
            $acquired = RateLimiter::attempt(
                $rateLimiterKey,
                self::MAX_JOBS_PER_MINUTE,
                function () {
                    // This callback is executed if the rate limit allows the action
                    return true;
                },
                self::RATE_LIMIT_DURATION
            );
            
            if (!$acquired) {
                Log::warning("Rate limit exceeded for job notification setup", [
                    'setup_id' => $this->setupId,
                    'max_allowed' => self::MAX_JOBS_PER_MINUTE,
                    'rate_limiter_key' => $rateLimiterKey,
                    'duration' => self::RATE_LIMIT_DURATION
                ]);
            }
            
            return $acquired;
            
        } catch (\Exception $e) {
            // If rate limiting fails, allow the job to proceed to avoid blocking
            Log::error("Rate limiting check failed, allowing job to proceed", [
                'error' => $e->getMessage(),
                'setup_id' => $this->setupId
            ]);
            return true;
        }
    }

    /**
     * Process setup notification with idempotency checks
     * 
     * This method ensures that duplicate notifications are not sent when
     * jobs are retried or events are processed multiple times.
     *
     * @param JobNotificationSetup $setup
     * @param JobService $jobService
     * @return bool
     */
    private function processSetupNotificationWithIdempotency(JobNotificationSetup $setup, JobService $jobService): bool
    {
        Log::info("Processing notification setup with idempotency: {$setup->id} for job seeker: {$setup->job_seeker_id}");

        if (!$setup->jobSeeker) {
            Log::warning("Job seeker not found for setup ID: {$setup->id}. Skipping.");
            return false;
        }

        // Get recent jobs for this setup using the service method
        $recentJobs = $jobService->getRecentJobsForNotificationSetup(
            $setup->categories()->pluck('job_categories.id')->toArray(),
            $setup->id,
            $setup->jobSeeker->email,
            config('jobseeker.notifications.job_age_days', 5)
        );

        if ($recentJobs->isEmpty()) {
            Log::info("No new jobs found for setup ID: {$setup->id}. Nothing to notify.");
            $setup->updateLastActivityCheck();
            return true; // No jobs, but process was successful
        }

        Log::info("Found {$recentJobs->count()} jobs for setup ID: {$setup->id}, checking for already sent notifications");

        // Get all job IDs that have already been sent for this setup (idempotency check) - Optimized to prevent N+1 queries
        $sentJobIds = JobNotificationLog::where('job_notification_setup_id', $setup->id)
            ->whereIn('job_id', $recentJobs->pluck('id')->toArray())
            ->pluck('job_id')
            ->toArray();

        // Filter out jobs that have already been sent for this setup
        $jobsToNotify = $recentJobs->filter(function ($job) use ($setup, $sentJobIds) {
            $alreadySent = in_array($job->id, $sentJobIds);
            
            if ($alreadySent) {
                Log::debug("Notification already sent for job", [
                    'setup_id' => $setup->id,
                    'job_id' => $job->id,
                    'job_position' => $job->position ?? 'unknown'
                ]);
            }
            
            return !$alreadySent;
        });

        if ($jobsToNotify->isEmpty()) {
            Log::info("All jobs have already been notified for setup ID: {$setup->id}. Skipping duplicate notifications.");
            $setup->updateLastActivityCheck();
            return true; // All jobs already sent, considered successful
        }

        Log::info("Found {$jobsToNotify->count()} new jobs to notify for setup ID: {$setup->id}");

        $emailSent = false;
        $fcmSent = false;

        // Send Email Notification using centralized EmailService
        if ($setup->jobSeeker->email) {
            try {
                $emailService = app(EmailService::class);

                // Prepare email data to match template expectations
                $emailData = [
                    'jobSeeker' => (object) [
                        'name' => $setup->jobSeeker->full_name ?? 'Job Seeker',
                    ],
                    'setup' => (object) [
                        'name' => $setup->name,
                    ],
                    'jobs' => $jobsToNotify->map(function($job) {
                        return [
                            'title' => $job->position,
                            'position' => $job->position,
                            'company' => $job->company_name,
                            'company_name' => $job->company_name,
                            'location' => $job->location,
                            'source' => $job->source,
                            'url' => $job->url,
                            'publish_date' => $job->created_at->format('M d, Y'),
                            'created_at' => $job->created_at->format('M d, Y'),
                        ];
                    })->toArray(),
                ];

                $result = $emailService->sendEmail(
                    [
                        'email' => $setup->jobSeeker->email,
                        'name' => $setup->jobSeeker->full_name ?? 'Job Seeker'
                    ],
                    'New Job Opportunities Available',
                    'modules.jobseeker.emails.jobs.jobseeker_notification',
                    $emailData
                );

                if ($result['success']) {
                    $emailSent = true;
                    Log::info("Email notification sent successfully for setup ID: {$setup->id} to job seeker: {$setup->jobSeeker->email}");
                } else {
                    $errorMessage = $result['message'] ?? 'Unknown error';
                    Log::error("Failed to send email notification for setup ID: {$setup->id}. EmailService error: " . $errorMessage);

                    // Report email failure to founder
                    $errorNotificationService = app(SystemErrorNotificationService::class);
                    $errorNotificationService->reportEmailSendFailure(
                        $setup->jobSeeker->email,
                        "Email notification failed: " . $errorMessage,
                        [
                            'setup_id' => $setup->id,
                            'setup_name' => $setup->name,
                            'job_count' => count($jobsToNotify),
                            'email_service_result' => $result,
                        ]
                    );
                }

            } catch (\Throwable $e) {
                Log::error("Failed to send email notification for setup ID: {$setup->id}. Error: " . $e->getMessage(), [
                    'exception' => $e,
                    'trace' => $e->getTraceAsString(),
                ]);

                // Report email exception to founder
                $errorNotificationService = app(SystemErrorNotificationService::class);
                $errorNotificationService->reportEmailSendFailure(
                    $setup->jobSeeker->email,
                    "Email notification exception: " . $e->getMessage(),
                    [
                        'setup_id' => $setup->id,
                        'setup_name' => $setup->name,
                        'job_count' => count($jobsToNotify),
                    ],
                    $e
                );
            }
        }

        // Send FCM Push Notification
        if ($setup->receive_push_notifications && $setup->jobSeeker->deviceTokens()->exists()) {
            try {
                $setup->jobSeeker->notify(new \Modules\JobSeeker\Notifications\JobAlertFcmNotification($jobsToNotify, $setup));
                $fcmSent = true;
                Log::info("FCM notification queued for setup ID: {$setup->id} to job seeker ID: {$setup->jobSeeker->id}");

            } catch (\Throwable $e) {
                Log::error("Failed to send FCM notification for setup ID: {$setup->id}. Error: " . $e->getMessage(), [
                    'exception' => $e,
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        // If at least one notification was sent successfully, log the jobs as sent
        if ($emailSent || $fcmSent) {
            DB::transaction(function () use ($setup, $jobsToNotify) {
                foreach ($jobsToNotify as $job) {
                    try {
                        // Use insertOrIgnore to prevent duplicate logging (handles race conditions)
                        $inserted = DB::table('job_notification_log')->insertOrIgnore([
                            'job_notification_setup_id' => $setup->id,
                            'job_id' => $job->id,
                            'sent_at' => now(),
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                        
                        // Also maintain the existing JobNotificationSentJob for compatibility
                        JobNotificationSentJob::firstOrCreate([
                            'setup_id' => $setup->id,
                            'job_id' => $job->id,
                            'recipient_email' => $setup->jobSeeker->email,
                        ], [
                            'sent_at' => now(),
                        ]);
                        
                        Log::debug("Logged sent notification for job", [
                            'setup_id' => $setup->id,
                            'job_id' => $job->id,
                            'was_new_log' => $inserted > 0
                        ]);
                        
                    } catch (\Exception $e) {
                        // Log error but don't fail the entire process
                        Log::error("Failed to log sent notification", [
                            'setup_id' => $setup->id,
                            'job_id' => $job->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            });
        }

        $setup->updateLastActivityCheck();

        return $emailSent || $fcmSent;
    }

    /**
     * Format jobs for email template
     * 
     * @param \Illuminate\Database\Eloquent\Collection $jobs
     * @return array
     */
    protected function formatJobsForEmail($jobs)
    {
        $formattedJobs = [];
        $categoryPriority = [
            'IT - Software' => 1,
            'Software engineering' => 1,
            'software development' => 1, 
            'software development ' => 1,
            'Information Technology' => 1,
            'Leadership' => 2,
            'Management' => 3
        ];

        foreach ($jobs as $job) {
            // Determine job category priority based on position or category
            $priority = 10; // Default low priority
            $position = strtolower($job->position ?? '');
            
            // Check position for keywords
            if (strpos($position, 'developer') !== false || 
                strpos($position, 'engineer') !== false || 
                strpos($position, 'software') !== false || 
                strpos($position, 'it') !== false) {
                $priority = 1; // IT jobs
            } elseif (strpos($position, 'lead') !== false || 
                     strpos($position, 'chief') !== false || 
                     strpos($position, 'head') !== false || 
                     strpos($position, 'director') !== false) {
                $priority = 2; // Leadership jobs
            } elseif (strpos($position, 'manager') !== false || 
                     strpos($position, 'management') !== false) {
                $priority = 3; // Management jobs
            }
            
            $formattedJobs[] = [
                'position' => $job->position ?? '',
                'company_name' => $job->company_name ?? '',
                'locations' => $job->locations ?? '',
                'contract_type' => $job->contract_type ?? '',
                'work_type' => $job->work_type ?? '',
                'publish_date' => $job->publish_date ?? '',
                'salary' => $job->salary ?? '',
                'slug' => $job->slug ?? '',
                'updated_at' => $job->updated_at ?? now()->format('Y-m-d H:i:s'),
                'priority' => $priority // Add priority for sorting
            ];
        }
        
        // Sort formatted jobs by priority (lower number first)
        usort($formattedJobs, function($a, $b) {
            return $a['priority'] <=> $b['priority'];
        });
        
        return $formattedJobs;
    }

    /**
     * Get relative time string (e.g., "2 hours ago", "1 day ago")
     *
     * @param string $dateString
     * @return string
     */
    protected function getTimeAgo($dateString)
    {
        try {
            $date = Carbon::parse($dateString);
            $now = Carbon::now();
            
            if ($date->diffInMinutes($now) < 60) {
                return $date->diffInMinutes($now) . ' minutes ago';
            } elseif ($date->diffInHours($now) < 24) {
                return $date->diffInHours($now) . ' hours ago';
            } else {
                return $date->diffInDays($now) . ' days ago';
            }
        } catch (\Exception $e) {
            Log::warning("Failed to parse date for time ago: {$dateString} - " . $e->getMessage());
            return 'recently';
        }
    }
} 
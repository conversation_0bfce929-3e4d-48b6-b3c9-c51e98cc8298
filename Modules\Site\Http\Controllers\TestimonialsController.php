<?php

namespace Modules\Site\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Testimonial;
use Illuminate\Http\Request;
use Session;

class TestimonialsController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view testimonials|create testimonial|edit testimonial|delete testimonial', ['only' => ['index','show']]);
        $this->middleware('permission:create testimonial', ['only' => ['create','store']]);
        $this->middleware('permission:edit testimonial', ['only' => ['edit','update']]);
        $this->middleware('permission:delete testimonial', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $keyword = $request->get('search');
        $perPage = 25;

        if (!empty($keyword)) {
            $testimonials = Testimonial::where('image', 'LIKE', "%$keyword%")
				->orWhere('status', 'LIKE', "%$keyword%")
				->paginate($perPage);
        } else {
            $testimonials = Testimonial::paginate($perPage);
        }

        return view('site::testimonials.index', compact('testimonials'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('site::testimonials.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        
        $requestData = $request->all();
        
        Testimonial::create($requestData);

        Session::flash('flash_message', 'Testimonial added!');

        return redirect('workplace/site/testimonials');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $testimonial = Testimonial::findOrFail($id);

        return view('site::testimonials.show', compact('testimonial'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $testimonial = Testimonial::findOrFail($id);

        return view('site::testimonials.edit', compact('testimonial'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {
        
        $requestData = $request->all();
        
        $testimonial = Testimonial::findOrFail($id);
        $testimonial->update($requestData);

        Session::flash('flash_message', 'Testimonial updated!');

        return redirect('workplace/site/testimonials');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Testimonial::destroy($id);

        Session::flash('flash_message', 'Testimonial deleted!');

        return redirect('workplace/site/testimonials');
    }
}

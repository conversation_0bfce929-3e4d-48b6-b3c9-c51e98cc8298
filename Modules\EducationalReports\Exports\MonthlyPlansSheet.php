<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\Student;
use App\StudentHefzPlan;
use App\StudentRevisionPlan;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

final class MonthlyPlansSheet implements WithTitle, WithStyles, WithEvents
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get memorization plans data
     */
    private function getMemorizationPlans(): Collection
    {
        $classId = $this->filters['classId'];
        $year = $this->filters['year'];
        $month = $this->filters['month'];
        $studentId = $this->filters['studentId'] ?? null;

        // Use plan_year_and_month for filtering instead of created_at
        $planYearMonth = sprintf('%d-%02d', $year, $month);

        $query = StudentHefzPlan::with([
            'student',
            'halaqah.center',
            'halaqah.programs',
            'halaqah.teachers',
            'fromSurat',
            'toSurat'
        ])
        ->where('class_id', $classId)
        ->where('plan_year_and_month', $planYearMonth)
        ->where('status', 'active');

        if (!empty($studentId)) {
            $query->where('student_id', $studentId);
        }

        $plans = $query->get();

        return $plans->map(function ($plan) {
            $classProgram = $plan->halaqah->programs->first()->title ?? 'N/A';
            $teacherNames = $plan->halaqah->teachers->pluck('full_name')->join(', ');
            $numberOfPages = $this->calculatePages($plan);

            return [
                'centre_id' => $plan->halaqah->center->id ?? 'N/A',
                'centre_name' => $plan->halaqah->center->name ?? 'N/A',
                'class_id' => $plan->class_id,
                'class_program' => $classProgram,
                'teacher_name' => $teacherNames ?: 'N/A',
                'month' => $plan->created_at->format('F Y'),
                'student_id' => $plan->student_id,
                'student_name' => $plan->student->full_name ?? 'N/A',
                'from_surah' => $plan->fromSurat->name ?? 'N/A',
                'from_verse' => $plan->start_from_ayat ?? 'N/A',
                'to_surah' => $plan->toSurat->name ?? 'N/A',
                'to_verse' => $plan->to_ayat ?? 'N/A',
                'no_of_pages' => $numberOfPages,
                'status' => $plan->status ?? 'N/A',
                'study_direction' => $plan->study_direction ?? 'N/A',
            ];
        });
    }

    /**
     * Get revision plans data
     */
    private function getRevisionPlans(): Collection
    {
        $classId = $this->filters['classId'];
        $year = $this->filters['year'];
        $month = $this->filters['month'];
        $monthName = $this->filters['monthName'];
        $studentId = $this->filters['studentId'] ?? null;

        // Use plan_year_and_month for filtering instead of created_at
        $planYearMonth = sprintf('%d-%02d', $year, $month);

        $query = StudentRevisionPlan::with([
            'student',
            'class.center',
            'class.programs',
            'class.teachers',
            'fromSurat',
            'toSurat'
        ])
        ->where('class_id', $classId)
        ->where('plan_year_and_month', $planYearMonth)
        ->where('status', 'active');

        if (!empty($studentId)) {
            $query->where('student_id', $studentId);
        }

        $plans = $query->get();

        return $plans->map(function ($plan) {
            $classProgram = $plan->class->programs->first()->title ?? 'N/A';
            $teacherNames = $plan->class->teachers->pluck('full_name')->join(', ');
            $numberOfPages = $this->calculateRevisionPages($plan);

            return [
                'centre_id' => $plan->class->center->id ?? 'N/A',
                'centre_name' => $plan->class->center->name ?? 'N/A',
                'class_id' => $plan->class_id,
                'class_program' => $classProgram,
                'teacher_name' => $teacherNames ?: 'N/A',
                'month' => $plan->created_at->format('F Y'),
                'student_id' => $plan->student_id,
                'student_name' => $plan->student->full_name ?? 'N/A',
                'from_surah' => $plan->fromSurat->name ?? 'N/A',
                'from_verse' => $plan->start_from_ayat ?? 'N/A',
                'to_surah' => $plan->toSurat->name ?? 'N/A',
                'to_verse' => $plan->to_ayat ?? 'N/A',
                'no_of_pages' => $numberOfPages,
                'status' => $plan->status ?? 'N/A',
                'study_direction' => $plan->study_direction ?? 'N/A',
            ];
        });
    }

    /**
     * Calculate number of pages using stored procedure for memorization
     */
    private function calculatePages(StudentHefzPlan $plan): int
    {
        try {
            if ($plan->study_direction === 'backward') {
                $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $plan->start_from_surat,
                    $plan->start_from_ayat,
                    $plan->to_surat,
                    $plan->to_ayat
                ]);

                return $result[0]->numberofPagesSum ?? 0;
            } else {
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $plan->start_from_surat,
                    $plan->start_from_ayat,
                    $plan->to_surat,
                    $plan->to_ayat
                ]);

                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                return $results[0]->number_of_pages_sum ?? 0;
            }
        } catch (\Exception $e) {
            \Log::error('Error calculating pages for memorization plan: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Calculate number of pages using stored procedure for revision
     */
    private function calculateRevisionPages(StudentRevisionPlan $plan): int
    {
        try {
            if ($plan->study_direction === 'backward') {
                $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $plan->start_from_surat,
                    $plan->start_from_ayat,
                    $plan->to_surat,
                    $plan->to_ayat
                ]);

                return $result[0]->numberofPagesSum ?? 0;
            } else {
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $plan->start_from_surat,
                    $plan->start_from_ayat,
                    $plan->to_surat,
                    $plan->to_ayat
                ]);

                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                return $results[0]->number_of_pages_sum ?? 0;
            }
        } catch (\Exception $e) {
            \Log::error('Error calculating pages for revision plan: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get table headings
     */
    private function getTableHeadings(): array
    {
        return [
            'Centre ID',
            'Centre Name',
            'Class ID',
            'Class Program',
            'Teacher Name',
            'Month',
            'Student ID',
            'Student Name',
            'From Surah',
            'From Verse',
            'To Surah',
            'To Verse',
            'No. of Pages',
            'Status',
            'Study Direction',
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Monthly Plans';
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [];
    }

    /**
     * Register events for creating dual tables
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $this->createDualTables($event->sheet);
            },
        ];
    }

    /**
     * Create dual tables on the worksheet with supervisory analytics
     */
    private function createDualTables($sheet)
    {
        $worksheet = $sheet->getDelegate();

        // Get data
        $memorizationData = $this->getMemorizationPlans();
        $revisionData = $this->getRevisionPlans();
        $headings = $this->getTableHeadings();

        // Get supervisory analytics
        $analytics = $this->getSupervisoryAnalytics();

        // Set main title
        $monthName = $this->filters['monthName'];
        $year = $this->filters['year'];
        $className = $this->filters['class']->name ?? 'N/A';

        $worksheet->setCellValue('A1', "MONTHLY PLANS REPORT - {$className} - {$monthName} {$year}");
        $worksheet->mergeCells('A1:O1');

        // Style main title
        $worksheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 16],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'E8F4F8']]
        ]);

        $currentRow = 3;

        // Add Executive Summary for Supervisors
        $currentRow = $this->createExecutiveSummary($worksheet, $currentRow, $analytics);
        $currentRow += 2;

        // Create Memorization Table
        $currentRow = $this->createTable($worksheet, $currentRow, 'MEMORIZATION PLANS', $headings, $memorizationData);

        // Add spacing
        $currentRow += 2;

        // Create Revision Table
        $this->createTable($worksheet, $currentRow, 'REVISION PLANS', $headings, $revisionData);

        // Auto-size columns (updated for new columns)
        foreach (range('A', 'O') as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }
    }

    /**
     * Create a single table with title, headers, and data
     */
    private function createTable($worksheet, $startRow, $title, $headings, $data): int
    {
        // Set table title
        $worksheet->setCellValue("A{$startRow}", $title);
        $worksheet->mergeCells("A{$startRow}:O{$startRow}");

        // Style table title
        $worksheet->getStyle("A{$startRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2E7D32']]
        ]);

        $headerRow = $startRow + 1;

        // Set headers
        $col = 'A';
        foreach ($headings as $heading) {
            $worksheet->setCellValue($col . $headerRow, $heading);
            $col++;
        }

        // Style headers
        $worksheet->getStyle("A{$headerRow}:O{$headerRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '4CAF50']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $dataStartRow = $headerRow + 1;
        $currentDataRow = $dataStartRow;

        // Add data rows
        foreach ($data as $row) {
            $col = 'A';
            foreach ($row as $value) {
                $worksheet->setCellValue($col . $currentDataRow, $value);
                $col++;
            }
            $currentDataRow++;
        }

        // Style data rows
        if ($data->count() > 0) {
            $dataEndRow = $currentDataRow - 1;
            $worksheet->getStyle("A{$dataStartRow}:O{$dataEndRow}")->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ]);

            // Alternate row colors
            for ($row = $dataStartRow; $row <= $dataEndRow; $row++) {
                if (($row - $dataStartRow) % 2 == 1) {
                    $worksheet->getStyle("A{$row}:O{$row}")->applyFromArray([
                        'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'F5F5F5']]
                    ]);
                }
            }

            return $dataEndRow;
        }

        // If no data, add "No data available" message
        $worksheet->setCellValue("A{$dataStartRow}", 'No data available');
        $worksheet->mergeCells("A{$dataStartRow}:O{$dataStartRow}");
        $worksheet->getStyle("A{$dataStartRow}")->applyFromArray([
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'font' => ['italic' => true],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        return $dataStartRow;
    }

    /**
     * Get supervisory analytics for quick decision making
     */
    private function getSupervisoryAnalytics(): array
    {
        $classId = $this->filters['classId'];
        $year = $this->filters['year'];
        $month = $this->filters['month'];
        $planYearMonth = sprintf('%d-%02d', $year, $month);

        // Get class performance metrics
        $classMetrics = DB::select("
            SELECT
                COUNT(DISTINCT shp.student_id) as total_students_with_plans,
                COUNT(shp.id) as total_memorization_plans,
                COUNT(srp.id) as total_revision_plans,
                AVG(CASE WHEN shp.status = 'active' THEN 1 ELSE 0 END) * 100 as plan_approval_rate,
                COUNT(CASE WHEN shp.study_direction = 'forward' THEN 1 END) as forward_direction_count,
                COUNT(CASE WHEN shp.study_direction = 'backward' THEN 1 END) as backward_direction_count
            FROM student_hefz_plans shp
            LEFT JOIN student_revision_plans srp ON shp.student_id = srp.student_id
                AND srp.class_id = ? AND srp.plan_year_and_month = ? AND srp.deleted_at IS NULL
            WHERE shp.class_id = ? AND shp.plan_year_and_month = ? AND shp.deleted_at IS NULL
        ", [$classId, $planYearMonth, $classId, $planYearMonth]);

        // Get attendance and performance from reports
        $performanceMetrics = DB::select("
            SELECT
                COUNT(DISTINCT shr.student_id) as students_with_reports,
                COUNT(shr.id) as total_sessions,
                AVG(eso.weight) as avg_evaluation_score,
                COUNT(CASE WHEN ao.title IN ('on_time', 'late') THEN 1 END) as present_sessions,
                COUNT(CASE WHEN ao.title = 'absent' THEN 1 END) as absent_sessions,
                SUM(COALESCE(shr.pages_memorized, 0)) as total_pages_memorized,
                COUNT(CASE WHEN eso.weight >= 0.8 THEN 1 END) as excellent_sessions,
                COUNT(CASE WHEN eso.weight < 0.5 THEN 1 END) as poor_sessions
            FROM student_hefz_report shr
            LEFT JOIN evaluation_schema_options eso ON shr.hefz_evaluation_id = eso.id
            LEFT JOIN attendance_options ao ON shr.attendance_id = ao.id
            WHERE shr.class_id = ?
            AND DATE_FORMAT(shr.created_at, '%Y-%m') = ?
            AND shr.deleted_at IS NULL
        ", [$classId, $planYearMonth]);

        // Get teacher information
        $teacherMetrics = DB::select("
            SELECT
                COUNT(DISTINCT shr.teacher_id) as total_teachers,
                emp.full_name as primary_teacher,
                COUNT(shr.id) as sessions_by_primary_teacher
            FROM student_hefz_report shr
            LEFT JOIN employees emp ON shr.teacher_id = emp.id
            WHERE shr.class_id = ?
            AND DATE_FORMAT(shr.created_at, '%Y-%m') = ?
            AND shr.deleted_at IS NULL
            GROUP BY shr.teacher_id, emp.full_name
            ORDER BY sessions_by_primary_teacher DESC
            LIMIT 1
        ", [$classId, $planYearMonth]);

        return [
            'class_metrics' => $classMetrics[0] ?? null,
            'performance_metrics' => $performanceMetrics[0] ?? null,
            'teacher_metrics' => $teacherMetrics[0] ?? null,
        ];
    }

    /**
     * Create executive summary for supervisors
     */
    private function createExecutiveSummary($worksheet, $startRow, $analytics): int
    {
        $classMetrics = $analytics['class_metrics'];
        $performanceMetrics = $analytics['performance_metrics'];
        $teacherMetrics = $analytics['teacher_metrics'];

        // Title
        $worksheet->setCellValue("A{$startRow}", "📊 EXECUTIVE SUMMARY - SUPERVISORY DASHBOARD");
        $worksheet->mergeCells("A{$startRow}:O{$startRow}");
        $worksheet->getStyle("A{$startRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '1565C0']]
        ]);

        $currentRow = $startRow + 2;

        // Key Performance Indicators
        $kpis = [
            ['Metric', 'Value', 'Status', 'Action Required'],
            ['Total Students with Plans', $classMetrics->total_students_with_plans ?? 0,
             ($classMetrics->total_students_with_plans ?? 0) > 0 ? '✅ Active' : '⚠️ Low',
             ($classMetrics->total_students_with_plans ?? 0) > 0 ? 'Monitor' : 'Investigate'],
            ['Plan Approval Rate', round((float)($classMetrics->plan_approval_rate ?? 0), 1) . '%',
             ((float)($classMetrics->plan_approval_rate ?? 0)) >= 80 ? '✅ Good' : '🔴 Critical',
             ((float)($classMetrics->plan_approval_rate ?? 0)) >= 80 ? 'Maintain' : 'Urgent Review'],
            ['Attendance Rate', round(((float)($performanceMetrics->present_sessions ?? 0) / max((float)($performanceMetrics->total_sessions ?? 1), 1)) * 100, 1) . '%',
             (((float)($performanceMetrics->present_sessions ?? 0) / max((float)($performanceMetrics->total_sessions ?? 1), 1)) * 100) >= 75 ? '✅ Good' : '⚠️ Concern',
             (((float)($performanceMetrics->present_sessions ?? 0) / max((float)($performanceMetrics->total_sessions ?? 1), 1)) * 100) >= 75 ? 'Monitor' : 'Intervention Needed'],
            ['Avg Performance Score', round((float)($performanceMetrics->avg_evaluation_score ?? 0), 2),
             ((float)($performanceMetrics->avg_evaluation_score ?? 0)) >= 0.7 ? '✅ Good' : '🔴 Poor',
             ((float)($performanceMetrics->avg_evaluation_score ?? 0)) >= 0.7 ? 'Maintain' : 'Training Required'],
            ['Total Pages Memorized', $performanceMetrics->total_pages_memorized ?? 0,
             ($performanceMetrics->total_pages_memorized ?? 0) > 50 ? '✅ On Track' : '⚠️ Behind',
             ($performanceMetrics->total_pages_memorized ?? 0) > 50 ? 'Continue' : 'Accelerate'],
            ['Primary Teacher', $teacherMetrics->primary_teacher ?? 'N/A', '📋 Info', 'Monitor Performance'],
        ];

        // Add KPI table with dynamic comments
        foreach ($kpis as $rowIndex => $kpi) {
            $row = $currentRow + $rowIndex;
            $worksheet->setCellValue("A{$row}", $kpi[0]);
            $worksheet->setCellValue("D{$row}", $kpi[1]);
            $worksheet->setCellValue("G{$row}", $kpi[2]);
            $worksheet->setCellValue("J{$row}", $kpi[3]);

            // Add dynamic comments based on the metric
            $this->addDynamicComment($worksheet, "D{$row}", $kpi[0], $kpi[1], $classMetrics, $performanceMetrics, $teacherMetrics);
        }

        // Style KPI table
        $kpiEndRow = $currentRow + count($kpis) - 1;
        $worksheet->getStyle("A{$currentRow}:O{$kpiEndRow}")->applyFromArray([
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        // Header row styling
        $worksheet->getStyle("A{$currentRow}:O{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2E7D32']]
        ]);

        return $kpiEndRow;
    }

    /**
     * Add dynamic comments based on actual data metrics
     */
    private function addDynamicComment($worksheet, $cellAddress, $metricName, $metricValue, $classMetrics, $performanceMetrics, $teacherMetrics)
    {
        $comment = $worksheet->getComment($cellAddress);
        $commentText = '';

        switch ($metricName) {
            case 'Total Students with Plans':
                $totalStudents = (int)($classMetrics->total_students_with_plans ?? 0);
                $memorizationPlans = (int)($classMetrics->total_memorization_plans ?? 0);
                $revisionPlans = (int)($classMetrics->total_revision_plans ?? 0);

                $commentText = "STUDENT PLANNING ANALYSIS:\n\n";
                $commentText .= "• Total Students: {$totalStudents}\n";
                $commentText .= "• Memorization Plans: {$memorizationPlans}\n";
                $commentText .= "• Revision Plans: {$revisionPlans}\n\n";

                if ($totalStudents == 0) {
                    $commentText .= "⚠️ CRITICAL: No students have active plans!\n";
                    $commentText .= "ACTION: Immediate plan creation required.";
                } elseif ($totalStudents < 10) {
                    $commentText .= "📊 LOW ENROLLMENT: Consider class consolidation\n";
                    $commentText .= "or additional student recruitment.";
                } else {
                    $commentText .= "✅ HEALTHY ENROLLMENT: Good class size for\n";
                    $commentText .= "effective teaching and individual attention.";
                }
                break;

            case 'Plan Approval Rate':
                $approvalRate = (float)($classMetrics->plan_approval_rate ?? 0);
                $totalPlans = (int)($classMetrics->total_memorization_plans ?? 0) + (int)($classMetrics->total_revision_plans ?? 0);
                $activePlans = round($totalPlans * ($approvalRate / 100));
                $pendingPlans = $totalPlans - $activePlans;

                $commentText = "PLAN APPROVAL ANALYSIS:\n\n";
                $commentText .= "• Total Plans: {$totalPlans}\n";
                $commentText .= "• Active Plans: {$activePlans}\n";
                $commentText .= "• Pending Plans: {$pendingPlans}\n";
                $commentText .= "• Approval Rate: {$approvalRate}%\n\n";

                if ($approvalRate >= 95) {
                    $commentText .= "🌟 EXCELLENT: Nearly all plans approved.\n";
                    $commentText .= "Strong planning and approval process.";
                } elseif ($approvalRate >= 80) {
                    $commentText .= "✅ GOOD: Most plans approved.\n";
                    $commentText .= "Monitor pending plans for bottlenecks.";
                } else {
                    $commentText .= "🔴 CRITICAL: Low approval rate!\n";
                    $commentText .= "ACTION: Review approval process and\n";
                    $commentText .= "address {$pendingPlans} pending plans immediately.";
                }
                break;

            case 'Attendance Rate':
                $presentSessions = (int)($performanceMetrics->present_sessions ?? 0);
                $totalSessions = (int)($performanceMetrics->total_sessions ?? 1);
                $absentSessions = (int)($performanceMetrics->absent_sessions ?? 0);
                $attendanceRate = round(($presentSessions / max($totalSessions, 1)) * 100, 1);

                $commentText = "ATTENDANCE ANALYSIS:\n\n";
                $commentText .= "• Total Sessions: {$totalSessions}\n";
                $commentText .= "• Present Sessions: {$presentSessions}\n";
                $commentText .= "• Absent Sessions: {$absentSessions}\n";
                $commentText .= "• Attendance Rate: {$attendanceRate}%\n\n";

                $commentText .= "CALCULATION: (Present ÷ Total) × 100\n";
                $commentText .= "Present = 'on_time' + 'late' reports\n\n";

                if ($attendanceRate >= 90) {
                    $commentText .= "🌟 EXCELLENT: Outstanding attendance!\n";
                    $commentText .= "Students are highly engaged.";
                } elseif ($attendanceRate >= 75) {
                    $commentText .= "✅ GOOD: Acceptable attendance level.\n";
                    $commentText .= "Monitor {$absentSessions} absent sessions.";
                } else {
                    $commentText .= "🔴 CONCERN: Low attendance rate!\n";
                    $commentText .= "ACTION: Investigate {$absentSessions} absences\n";
                    $commentText .= "and implement attendance improvement plan.";
                }
                break;

            case 'Avg Performance Score':
                $avgScore = (float)($performanceMetrics->avg_evaluation_score ?? 0);
                $scorePercentage = round($avgScore * 100, 1);
                $excellentSessions = (int)($performanceMetrics->excellent_sessions ?? 0);
                $poorSessions = (int)($performanceMetrics->poor_sessions ?? 0);
                $totalSessions = (int)($performanceMetrics->total_sessions ?? 1);

                $commentText = "PERFORMANCE ANALYSIS:\n\n";
                $commentText .= "• Average Score: {$scorePercentage}%\n";
                $commentText .= "• Excellent Sessions (≥80%): {$excellentSessions}\n";
                $commentText .= "• Poor Sessions (<50%): {$poorSessions}\n";
                $commentText .= "• Total Sessions: {$totalSessions}\n\n";

                $commentText .= "SCORE MAPPING:\n";
                $commentText .= "• Excellent = 100% • Very Good = 85%\n";
                $commentText .= "• Good = 70% • Weak = 30%\n\n";

                if ($scorePercentage >= 85) {
                    $commentText .= "🌟 EXCELLENT: Outstanding performance!\n";
                    $commentText .= "Students are mastering the curriculum.";
                } elseif ($scorePercentage >= 70) {
                    $commentText .= "✅ GOOD: Solid performance level.\n";
                    $commentText .= "Continue current teaching methods.";
                } else {
                    $commentText .= "🔴 CONCERN: Below target performance!\n";
                    $commentText .= "ACTION: Review teaching methods and\n";
                    $commentText .= "provide additional support for {$poorSessions} poor sessions.";
                }
                break;

            case 'Total Pages Memorized':
                $totalPages = (int)($performanceMetrics->total_pages_memorized ?? 0);
                $totalSessions = (int)($performanceMetrics->total_sessions ?? 1);
                $avgPagesPerSession = round($totalPages / max($totalSessions, 1), 1);
                $studentsWithReports = (int)($performanceMetrics->students_with_reports ?? 1);
                $avgPagesPerStudent = round($totalPages / max($studentsWithReports, 1), 1);

                $commentText = "MEMORIZATION PROGRESS:\n\n";
                $commentText .= "• Total Pages: {$totalPages}\n";
                $commentText .= "• Total Sessions: {$totalSessions}\n";
                $commentText .= "• Students: {$studentsWithReports}\n";
                $commentText .= "• Avg Pages/Session: {$avgPagesPerSession}\n";
                $commentText .= "• Avg Pages/Student: {$avgPagesPerStudent}\n\n";

                if ($avgPagesPerSession >= 2.0) {
                    $commentText .= "🌟 EXCELLENT: High memorization rate!\n";
                    $commentText .= "Students are progressing rapidly.";
                } elseif ($avgPagesPerSession >= 1.0) {
                    $commentText .= "✅ GOOD: Steady memorization progress.\n";
                    $commentText .= "Maintain current pace.";
                } else {
                    $commentText .= "⚠️ SLOW PROGRESS: Below expected rate.\n";
                    $commentText .= "ACTION: Review teaching methods and\n";
                    $commentText .= "consider additional practice sessions.";
                }
                break;

            case 'Primary Teacher':
                $teacherName = $teacherMetrics->primary_teacher ?? 'N/A';
                $sessionsByTeacher = (int)($teacherMetrics->sessions_by_primary_teacher ?? 0);
                $totalTeachers = (int)($performanceMetrics->total_teachers ?? 1);

                $commentText = "TEACHER ASSIGNMENT:\n\n";
                $commentText .= "• Primary Teacher: {$teacherName}\n";
                $commentText .= "• Sessions by Primary: {$sessionsByTeacher}\n";
                $commentText .= "• Total Teachers: {$totalTeachers}\n\n";

                if ($totalTeachers == 1) {
                    $commentText .= "📋 SINGLE TEACHER: Consistent instruction.\n";
                    $commentText .= "Monitor teacher workload and backup coverage.";
                } elseif ($totalTeachers <= 3) {
                    $commentText .= "👥 TEAM TEACHING: Good teacher coverage.\n";
                    $commentText .= "Ensure coordination between teachers.";
                } else {
                    $commentText .= "⚠️ MANY TEACHERS: Potential inconsistency.\n";
                    $commentText .= "ACTION: Standardize teaching methods\n";
                    $commentText .= "and improve teacher coordination.";
                }
                break;
        }

        if (!empty($commentText)) {
            $comment->getText()->createText($commentText);
            $comment->setWidth('400px');
            $comment->setHeight('200px');
        }
    }
}

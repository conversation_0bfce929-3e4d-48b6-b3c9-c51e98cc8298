<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureStudentNationalityIsFilled
{
    /**
     * Handle an incoming request.
     * This is only for students who registered with google
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Auth::guard('web')->check()) {
            return $next($request);
        }

        $user = Auth::guard('web')->user();

        if (!$user) {
            return $next($request);
        }

        if ($user->hasRole('member') && $user->nationality === null) {
            return redirect('/student-nationality-google-registration/nationality');
        }

        return $next($request);
    }
}

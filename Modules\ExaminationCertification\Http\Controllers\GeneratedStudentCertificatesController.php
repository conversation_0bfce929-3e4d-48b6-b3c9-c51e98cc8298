<?php

namespace Modules\ExaminationCertification\Http\Controllers;

use App\Classes;
use App\GeneratedStudentCertificate;
use App\Student;
use App\YearCheck;
use App\GeneralSettings;
use Dompdf\Dompdf;
use Dompdf\Options;
use Illuminate\Http\Request;
use App\StudentCertificate;
use Barryvdh\DomPDF\Facade as PDF;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Routing\Controller;


class GeneratedStudentCertificatesController extends Controller
{
    public function __construct()
	{
//        $this->middleware('PM');
        // User::checkAuth();
	}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        try {



            $certificates = GeneratedStudentCertificate::where('active_status', 1)->get();
            return view('examinationcertification::certification.certificate_list', compact('certificates'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function data(Request $request)
    {
        $certificates = GeneratedStudentCertificate::with(['student', 'exam.center', 'exam.class', 'exam.program', 'exam.level'])
            ->whereHas('exam', function ($query) use ($request) {
                if ($request->center) {
                    $query->whereHas('center', function ($query) use ($request) {
                        $query->where('id', $request->center);
                    });
                }
                if ($request->class) {
                    $query->whereHas('class', function ($query) use ($request) {
                        $query->where('id', $request->class);
                    });
                }
                if ($request->program) {
                    $query->whereHas('program', function ($query) use ($request) {
                        $query->where('id', $request->program);
                    });
                }
                if ($request->level) {
                    $query->whereHas('level', function ($query) use ($request) {
                        $query->where('id', $request->level);
                    });
                }
                if ($request->start_date) {
                    $query->where('exam_date', '>=', $request->start_date);
                }
                if ($request->end_date) {
                    $query->where('exam_date', '<=', $request->end_date);
                }
                if ($request->status) {
                    $query->where('status', $request->status);
                }
            })
            ->get();

        return \DataTables::of($certificates)
            ->addColumn('student_name', function ($certificate) {
                return $certificate->student->name;
            })
            ->addColumn('center', function ($certificate) {
                return $certificate->exam->center->name;
            })
            ->addColumn('class', function ($certificate) {
                return $certificate->exam->class->name;
            })
            ->addColumn('program', function ($certificate) {
                return $certificate->exam->program->name;
            })
            ->addColumn('level', function ($certificate) {
                return $certificate->exam->level->name;
            })
            ->addColumn('exam_date', function ($certificate) {
                return $certificate->exam->exam_date;
            })
            ->addColumn('examiner', function ($certificate) {
                return $certificate->exam->examiner;
            })
            ->addColumn('status', function ($certificate) {
                return $certificate->exam->status;
            })
            ->addColumn('action', function ($certificate) {
                return $certificate->exam->status;
            });
    }


    public function store(Request $request)
    {


      
        $request->validate([
            'name' => "required|max:50",
            'file' => "required|mimes:pdf,txt,doc,docx,jpg,jpeg,png|dimensions:width=1100,height=850"

        ]);
        
        try {
            $fileName = "";
            if ($request->file('file') != "") {
                $maxFileSize = GeneralSettings::first('file_size')->file_size;
                $file = $request->file('file');
                $fileSize =  filesize($file);
                $fileSizeKb = ($fileSize / 1000000);
                if($fileSizeKb >= $maxFileSize){
                    Toastr::error( 'Max upload file size '. $maxFileSize .' Mb is set in system', 'Failed');
                    return redirect()->back();
                }
                $file = $request->file('file');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/certificate/', $fileName);
                $fileName = 'public/uploads/certificate/' . $fileName;
            }

            $certificate = new StudentCertificate();
            $certificate->name = $request->name;
            $certificate->header_left_text = $request->header_left_text;
            $certificate->date = date('Y-m-d', strtotime($request->date));
            $certificate->body = $request->body;
            $certificate->footer_left_text = $request->footer_left_text;
            $certificate->footer_center_text = $request->footer_center_text;
            $certificate->footer_right_text = $request->footer_right_text;
            $certificate->student_photo = $request->student_photo;
            $certificate->file = $fileName;
            $certificate->organization_id = config('organization_id');
//            $certificate->academic_id = getAcademicId();

            $result = $certificate->save();
            if ($result) {
                Toastr::success('Operation successful', 'Success');

                return redirect()->back();
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {

            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function edit($id)
    {

        try {
             if (checkAdmin()) {
               $certificate = StudentCertificate::find($id);
            }else{
                $certificate = StudentCertificate::where('id',$id)->first();
            }
            $certificates = StudentCertificate::where('active_status', 1)->get();
            return view('examinationcertification::student_certificate', compact('certificates', 'certificate'));
        } catch (\Exception $e) {

            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => "required|max:50",
            'file' => "mimes:pdf,txt,doc,docx,jpg,jpeg,png|dimensions:width=1100,height=850"
        ]);


        try {
            $fileName = "";
            if ($request->file('file') != "") {
                $maxFileSize = GeneralSettings::first('file_size')->file_size;
                $file = $request->file('file');
                $fileSize =  filesize($file);
                $fileSizeKb = ($fileSize / 1000000);
                if($fileSizeKb >= $maxFileSize){
                    Toastr::error( 'Max upload file size '. $maxFileSize .' Mb is set in system', 'Failed');
                    return redirect()->back();
                }
                $certificate = StudentCertificate::find($request->id);
                if ($certificate->file != "") {
                    @unlink($certificate->file);
                }


                $file = $request->file('file');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/certificate/', $fileName);
                $fileName = 'public/uploads/certificate/' . $fileName;
            }

              if (checkAdmin()) {
               $certificate = StudentCertificate::find($request->id);
            }else{
                $certificate = StudentCertificate::where('id',$request->id)->first();
            }
            $certificate->name = $request->name;
            $certificate->header_left_text = $request->header_left_text;
            $certificate->date = date('Y-m-d', strtotime($request->date));
            $certificate->body = $request->body;
            $certificate->footer_left_text = $request->footer_left_text;
            $certificate->footer_center_text = $request->footer_center_text;
            $certificate->footer_right_text = $request->footer_right_text;
            $certificate->student_photo = $request->student_photo;
            if ($fileName != "") {
                $certificate->file = $fileName;
            }

            $result = $certificate->save();
            if ($result) {
                Toastr::success('Operation successful', 'Success');

                return redirect(route('student-certificate'));
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {

        try {
            // $certificate = StudentCertificate::find($id);
              if (checkAdmin()) {
               $certificate = StudentCertificate::find($id);
            }else{
                $certificate = StudentCertificate::where('id',$id)->first();
            }
            unlink($certificate->file);
            $result = $certificate->delete();

            if ($result) {
                Toastr::success('Operation successful', 'Success');

                return redirect(route('student-certificate'));
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {

            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    // for get route
    public function generateCertificate()
    {


        try {
            $classes = Classes::all();

            $certificates = StudentCertificate::where('active_status', 1)->get();
            return view('examinationcertification::generate_certificate', compact('classes', 'certificates'));
        } catch (\Exception $e) {

            
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    // for post route
    public function generateCertificateSearch(Request $request)
    {


        $request->validate([
            'class' => 'required',
            'certificate' => 'required'
        ]);

        try {
            $certificate_id = $request->certificate;
            $class_id = $request->class;
             $students = Student::with('joint_classes','parents','gender')->whereHas("joint_classes", function ($q) use ($request) {
                 return $q->where('class_id', $request->class);
             })->get();


            $classes = Classes::all();
            $certificates = StudentCertificate::where('active_status', 1)->get();
            return view('examinationcertification::generate_certificate', compact('classes', 'certificates', 'certificate_id', 'certificates', 'students', 'class_id'));
        } catch (\Exception $e) {


            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function generateCertificateGenerate($s_id, $c_id)
    {

        try {
            $s_ids = explode('-', $s_id);
            $students = [];
            foreach ($s_ids as $sId) {
                $students[] = Student::find($sId);
            }

            $certificate = StudentCertificate::find($c_id);



            // return view('backEnd.admin.student_certificate_print', ['students' => $students, 'certificate' => $certificate]);
            $pdf = PDF::loadView('examinationcertification::student_certificate_print', ['students' => $students, 'certificate' => $certificate]);
            $pdf->setPaper('A4', 'landscape')->setOptions(['isRemoteEnabled' => true,'enable_remote' => true,'isHtml5ParserEnabled' => true]);






            return $pdf->stream('certificate.pdf');
        } catch (\Exception $e) {
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}
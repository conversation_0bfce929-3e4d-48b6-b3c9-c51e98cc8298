<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\General\Services\FacebookNotifier;

class TestFacebookCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:facebook {message? : The message to send} {recipient? : The recipient ID to send to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the Facebook Messenger integration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Facebook Messenger Integration');
        
        // Get the message from the command line or use a default
        $message = $this->argument('message') ?? 'This is a test message from Facebook Messenger integration';
        
        // Create a new instance of the notifier
        $notifier = app(FacebookNotifier::class);
        
        // If a recipient ID was provided, set it
        if ($this->argument('recipient')) {
            $notifier->setRecipientId($this->argument('recipient'));
            $this->info('Using custom recipient ID: ' . $notifier->getRecipientId());
        } else {
            $this->info('Using default recipient ID: ' . $notifier->getRecipientId());
        }
        
        // Send the test message
        $this->info('Sending test message: "' . $message . '"');
        
        // Test with buttons
        $buttons = [
            ['title' => 'Visit Website', 'url' => 'https://example.com'],
            ['title' => 'Contact Us', 'url' => 'https://example.com/contact']
        ];
        
        $result = $notifier->sendMessageWithButtons($message, $buttons);
        
        if ($result) {
            $this->info('✅ Message sent successfully!');
            return 0;
        } else {
            $this->error('❌ Failed to send message');
            return 1;
        }
    }
} 
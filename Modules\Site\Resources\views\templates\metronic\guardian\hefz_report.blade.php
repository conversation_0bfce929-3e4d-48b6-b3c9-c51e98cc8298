@extends(theme_path('guardian.layout.studentprofile'))
@section('side_content')
<link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.css">
 <script src="//ajax.googleapis.com/ajax/libs/jquery/1.9.0/jquery.min.js"></script>
 <script src="//cdnjs.cloudflare.com/ajax/libs/raphael/2.1.0/raphael-min.js"></script>
 <script src="//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.min.js"></script>
<div class="form-group" > <div class="form-group">
@if($student->status=="active")
 

 

  <form id="myform" method="post" action="{{ route('hefzreport.gurdianaction') }}" >


  
  {{ csrf_field() }}

<label for="></label">

<select  id="bymonth" class="form-control" name="bymonth" onchange="this.form.submit();" >
<!-- onchange="this.form.submit();"-->
          <option value="0" selected="true" disabled selected>{{ \Carbon\Carbon::now()->format('Y,M') }}</option>
          
          @foreach ($month as $key => $value)
          <option value="{{ $key }}">{{ $key }} </option>
   
@endforeach
</select>
<input type="hidden" value="{{$student->id}}"  name="student_id" id ="student_id">
</form>
</div></div>
                     
  
           <ul class="nav nav-tabs nav-top-border">
           <li class="active"><a href="#attendence" data-toggle="tab">Attendence Report</a></li>
                <li><a href="#hefz" data-toggle="tab"> Hefz Report</a></li>
                <li><a href="#revision" data-toggle="tab">Revision Report</a></li>
             
               
           </ul>
<div class="tab-content margin-top-20">
<!------------------------------------------------------------------------------->
                     <!-- Hefz Report TAB -->
 <!------------------------------------------------------------------------------->
    <div class="tab-pane fade " id="hefz">
        <div class="col-md-12">
           <div class="well">
               
              <!--   <h3>Student Hefz Report </h3> -->
           
              <table class="table table-striped" id="hefz_table">
               
              
               
                <thead>
                <tr>
       
                 <th scope="col">Class Date</th>
                 <th scope="col">From Surat</th>
                 <th scope="col">To Surat</th>
                 <th scope="col">From Ayat</th>
                 <th scope="col">To Ayat</th>
                 <th scope="col">Evaluation</th>

                </tr>
               </thead>
               <tbody>
        @if(isset($hefzrep))
    
          @foreach($hefzrep as $s)
                 <td>
                  {{date('d-m-Y', strtotime($s->class_time))}}
                 </td>
                 <td>
                  {{ \App\MoshafSurah::where(['id' => $s->hefz_from_surat])->pluck('name')->first() }}
                 </td>
                 <td>
                  {{ \App\MoshafSurah::where(['id' => $s->hefz_to_surat])->pluck('name')->first() }}
                 </td>
                 <td>
                  {{ $s->hefz_from_ayat }}
                 </td>
                 <td>
                  {{  $s->hefz_to_ayat }}
 
                 </td>
                 <td>
                  {{ \App\EvaluationSchemaOption::where(['id' => $s->hefz_evaluation_id])->pluck('title')->first() }}
                 </td>
                 </tr>
          @endforeach
         @endif
               </tbody>
             </table>         
            </div> 
        </div>
 </div>
 
  <!------------------------------------------------------------------------------->

  <!------------------------------------------------------------------------------->
                     <!-- Revision Report TAB -->
 <!------------------------------------------------------------------------------->

 <div class="tab-pane fade " id="revision">
        <div class="col-md-12">
           <div class="well">
               
              <!--   <h3>Student Hefz Report </h3> -->
           
              <table class="table table-striped">
                <thead>
                <tr>
       
                 <th scope="col">Class Date</th>
                 <th scope="col">From Surat</th>
                 <th scope="col">To Surat</th>
                 <th scope="col">From Ayat</th>
                 <th scope="col">To Ayat</th>
                 <th scope="col">Evaluation</th>
                </tr>
               </thead>
               <tbody>
        @if(isset($rev))
           @foreach($rev as $s)

                 <td>
                 {{date('d-m-Y', strtotime($s->class_time))}}
                 </td>
                 <td>
                 {{ \App\MoshafSurah::where(['id' => $s->revision_from_surat])->pluck('name')->first() }}
                 </td>
                 <td>
                 {{ \App\MoshafSurah::where(['id' => $s->revision_to_surat])->pluck('name')->first() }}
                 </td>
                 <td>
                 {{ $s->revision_from_ayat }}
                 </td>
                 <td>
                 {{  $s->revision_to_ayat }}
 
                 </td>
                 <td>
                  {{ \App\EvaluationSchemaOption::where(['id' => $s->revision_evaluation_id])->pluck('title')->first() }}
                 </td>
                 </tr>
          @endforeach
        @endif
               </tbody>
             </table>  

            </div> 
            
           
        </div>
        
        
 </div>
  <!------------------------------------------------------------------------------->

<!------------------------------------------------------------------------------->
                     <!-- Hefz Attemdence TAB -->
 <!------------------------------------------------------------------------------->
 <div class="tab-pane fade in active " id="attendence">
        <div class="col-md-12">
           <div class="well">
               
              <!--   <h3>Student Hefz Report </h3> -->
           
              <table class="table table-striped" >
                <thead>
                <tr>
       
                 <th scope="col">Class Date</th>
                 <th scope="col" ><button type="button" class="btn btn-success">-Present-</button></th>
                 <th scope="col"><button type="button" class="btn btn-info">-Late -</button></th>
               
                 <th scope="col"><button type="button" class="btn btn-danger">-Absent-</button></th>
                 <th scope="col"><button type="button" class="btn btn-warning">-Absent with Excused-</button></th>

                </tr>
               </thead>
               <tbody style="text-align: center;">
        @if(isset($atten))
    
          @foreach($atten as $s)
          <?php $total+=1?>
          @if($s->attendance=='on_time')
                 <td>
                 {{date('d-m-Y', strtotime($s->class_time))}}
            <?php $ontime+=1?>
                 
                 </td>
                 <td >
                  <span class="fa fa-check" aria-hidden="true"></span>
                 </td>
                 <td>
                  
                 </td>
                 <td>
                  
                 </td>
                 <td>
                  
 
                 </td>
              
              @elseif($s->attendance=='late')
              <td>
              {{date('d-m-Y', strtotime($s->class_time))}}
              <?php $late+=1?>
                 </td>
                 <td>
                  
                 </td>
                 <td>
                 <span class="fa fa-check" aria-hidden="true"></span>
                 </td>
                 <td>
                  
                 </td>
                 <td>
                  
 
                 </td>
              
              @elseif($s->attendance=='absent')
              <td>
              {{date('d-m-Y', strtotime($s->class_time))}}
              <?php $absent+=1?>
                 </td>
                 <td>
                  
                 </td>
                 <td>
                  
                 </td>
                 <td>
                 <span class="fa fa-check" aria-hidden="true"></span>
                 </td>
                 <td>
                 
 
                 </td>
                 @else
                 <td>
                 {{date('d-m-Y', strtotime($s->class_time))}}
                 <?php $exe+=1?>
                 </td>
                 <td>
                 
                 </td>
                 <td>
                  
                 </td>
                 <td>
                 
                 </td>
                 <td>
                 <span class="fa fa-check" aria-hidden="true"></span>
 
                 </td>

                 @endif
                 </tr>
          @endforeach
         @endif
               </tbody>
             </table>  
                    
            </div> 
            <div id="graph2" style="text-size:10px;"></div>

        </div>
        @if(!$total==0)
       

       <!--   Chart script-->
       <script>
       Morris.Donut({
         element: 'graph2',
         data: [
           {value:{{round($ontime/$total*100)}}, label: 'Present'},
           {value: {{round($late/$total*100)}}, label: 'Late'},
           {value:{{round($absent/$total*100)}}, label: 'Absent'},
           {value: {{round($exe/$total*100)}}, label: 'Excused'}
         ],
         colors: ['#5bb75b','#5cc0de','#d8524f','#f0ad4d'],
         formatter: function (x) { return x+"%" }
       }).on('click', function(i, row){
         console.log(i, row);
       });
       </script>
       <!---------------->
       
       
       @endif


 </div>
 
  

</div>
@elseif($student->status == "profile_completed")
<ul class="process-steps nav nav-justified">
    <li class="active">
        <a href="#"><i class="icon-ok"></i></a>
        
    </li>
</ul>
<div class="well">

@include('forms.admission.application')

</div>

    @endif

@endsection



<!--   Select Box script-->


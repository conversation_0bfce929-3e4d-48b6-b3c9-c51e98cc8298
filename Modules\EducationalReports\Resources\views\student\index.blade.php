@extends('layouts.hound')
@section('mytitle' , trans('units.students'))

@section("css")






    <link href="https://cdnjs.cloudflare.com/ajax/libs/semantic-ui/2.3.1/semantic.min.css"
          type="text/css" rel="stylesheet"/>
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css"
          type="text/css" rel="stylesheet"/>
    <link href="https://cdn.datatables.net/1.10.22/css/dataTables.bootstrap.min.css"
          type="text/css" rel="stylesheet"/>
    <link href="https://cdn.datatables.net/responsive/2.2.6/css/responsive.semanticui.min.css"
          type="text/css" rel="stylesheet"/>
    {{--    <link href="{{ url("assets/platform/css/bootstrap.min.css")}}" rel="stylesheet">--}}

    <link href="https://netdna.bootstrapcdn.com/font-awesome/4.0.3/css/font-awesome.min.css" rel="stylesheet"
          type="text/css"/>

    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/semantic.min.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/dropdown.min.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/search.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/icon.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/grid.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/placeholder.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/transition.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/divider.css')}}">
    <link rel="stylesheet" type="text/css" href="{{ url('css/semantic/components/button.css')}}">
    <script
        src="https://code.jquery.com/jquery-3.5.1.min.js"
        integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0="
        crossorigin="anonymous"></script>

    {{--    <script src="{{ asset('js/typeahead/jquery.typeahead.js') }}"></script>--}}

    <script src="{{ asset('js/semantic/semantic.min.js')}}"></script>
    <script src="{{ asset('js/semantic/components/dropdown.js')}}"></script>
    <script src="{{ asset('js/semantic/components/search.js')}}"></script>
    <script src="{{ asset('js/semantic/components/transition.js')}}"></script>







@endsection
@section('content')
    <!-- Row -->
    {{$ss=""}}


    <header class="page-header">
        <div class="d-flex align-items-center">

            <div class="align-self-center text-right m-b-10">

                <a class="btn collapsed btn-info" data-toggle="collapse" href="#UsersDataTable_filtersCollapse"
                   aria-expanded="false"><i class="fa fa-filter"></i></a>


                <div class="btn-group bulk_actions" id="bulk_actions_UsersDataTable" data-table="UsersDataTable">
                    <a type="button" class="btn btn-primary " href="{{ route('students.create') }}">Add New
                        Student</a>


                </div>

                <div class="btn-group">
                    <a type="button" class="btn btn-primary " href="{{ route('student.archive.index') }}">Student
                        Archive</a>
                </div>
            </div>
        </div>
    </header>


    <div class="row">
        <div class="col-sm-12">
            <div class="panel panel-default card-view">
                <div class="panel-wrapper collapse in">
                    <div class="panel-body ">


                        <div id="UsersDataTable_filtersCollapse" class="filtersCollapse collapse">

                            {{--                            <div class="ui three column divided grid">--}}
                            {!! Form::open(['method' => 'get', 'id' => 'search-form','class' => 'form-inline']) !!}
                            <div class="ui grid divided column">

                                <div class="four wide column">
                                    <select id="gender" class="ui dropdown gender">
                                        <option value="">Gender</option>
                                        <option value="male">Male</option>
                                        <option value="female">Female</option>
                                    </select>
                                </div>
                                <div class="four wide column">
                                    <div class="ui category  search">
                                        <div class="ui left icon input focus">
                                            <input name="name" autocomplete="off" class="prompt" type="text" style="border-radius: 5px; !important;"
                                                   placeholder="Name ( Eng or Ar...">
                                            <i class="user icon"></i>
                                        </div>
                                        <div class="results"></div>
                                    </div>
                                </div>
                                <div class="four wide column">
                                    <div class="ui form">
                                        <div class="field remote">
                                            <select id="centers" name="centers" class="ui search selection dropdown"
                                                    multiple>
                                                <option value="">Centers...</option>
                                            </select>
                                        </div>

                                    </div>
                                </div>
                                <div class="four wide column">
                                    <button class="btn btn-primary filterBtn"
                                            data-table="UsersDataTable" type="submit"><i
                                            class="fa fa-search"></i></button>&nbsp;&nbsp;<button
                                        class="btn btn-default clearBtn" data-table="UsersDataTable"
                                        type="button"><i class="fa fa-eraser"></i></button>
                                </div>

                                <div class="four wide column">
                                    <div class="ui form">
                                        <div class="field remote">
                                            <select id="status" name="centers" class="ui search selection dropdown status"
                                                    multiple>
                                                <option value="">Status</option>
                                            </select>
                                        </div>

                                    </div>
                                </div>
                                <div class="four wide column">
                                    <div class="ui form search">
                                        <div class="ui input focus">
                                            <input
                                                class="form-control filter ui ui-datepicker"
                                                placeholder="Registered at" id="created_at"
                                                name="created_at" type="date">
                                        </div>
                                    </div>
                                </div>

                            </div>


                            <div class="filters" data-table="UsersDataTable" id="UsersDataTable_filters">



                            </div>


                            {!! Form::close() !!}
                        </div>
                    </div>


                </div>


                <div class="panel-body row">



                    <div class="table-wrap">
                        <div class="table-responsive">
                            {{--                                <div class="col-sm-13">--}}
                            {{--                                    <div class="col-sm-5">--}}
                            {{--                                        {!! Form::open(['method' => 'get']) !!}--}}
                            {{--                                        <div class="input-group mb-15">--}}
                            {{--                                            <input type="text" id="filter_name" name="filter_name" class="form-control"--}}
                            {{--                                                   value="{{ $filter_name }}" placeholder="Search Name">--}}
                            {{--                                            <span class="input-group-btn">--}}
                            {{--                                        <button type="submit" class="btn btn-success btn-anim"><i--}}
                            {{--                                                class="icon-rocket"></i><span class="btn-text">Search</span></button>--}}
                            {{--                                        </span>--}}
                            {{--                                        </div>--}}
                            {{--                                        {!! Form::close() !!}--}}
                            {{--                                    </div>--}}
                            {{--                                    <div class="col-sm-3">--}}
                            {{--                                        <select name="filter" id="filter" class="form-control"--}}
                            {{--                                                onchange="window.location = '/workplace/admission/students?filter='+this.value">--}}
                            {{--                                            <option value="all">All Student</option>--}}
                            {{--                                            <option value="application_waiting_approval"--}}
                            {{--                                                    @if($filter == 'application_waiting_approval') selected @endif>--}}
                            {{--                                                Applications Waiting for Approval--}}
                            {{--                                            </option>--}}
                            {{--                                            <option value="plan_waiting_approval"--}}
                            {{--                                                    @if($filter == 'plan_waiting_approval') selected @endif>Plan Waiting--}}
                            {{--                                                for Approval--}}
                            {{--                                            </option>--}}
                            {{--                                        </select>--}}
                            {{--                                    </div>--}}
                            {{--                                    <div class="col-sm-4">--}}
                            {{--                                        <div class="col-sm-6">--}}
                            {{--                                            <a href="{{ route('admission.students.create') }}"--}}
                            {{--                                               class="btn btn-primary btn-xs" title="Add New Center"><i--}}
                            {{--                                                    class="glyphicon glyphicon-plus " aria-hidden="true"/></i>  Add New--}}
                            {{--                                                Student</a>--}}

                            {{--                                        </div>--}}
                            {{--                                        <div class="col-sm-6">--}}
                            {{--                                            <a href="{{ route('student.archive.index') }}"--}}
                            {{--                                               class="btn btn-primary btn-xs" title="Add New Center"> Student--}}
                            {{--                                                Archive</a>--}}

                            {{--                                        </div>--}}
                            {{--                                    </div>--}}
                            {{--                                </div>--}}

                            {{--                            <table class="table display responsive product-overview mb-30" id="myTable">--}}


                            <table class="table display responsive product-overview mb-30 ui fixed table"  style="width:100%"
                                   id="UsersDataTable">
                                <thead>
                                <tr>
                                    <th>ID</th>
                                    <th> Name</th>
                                    <th> Arabic Name</th>
                                    <th> Date of Birth</th>
                                    <th> Mobile</th>
                                    <th> Center</th>
                                    <th> Application Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                                </thead>
                                {{--                                <tbody>--}}
                                {{--                                @foreach($students as $student)--}}
                                {{--                                    <tr>--}}
                                {{--                                        <td>{{ $student->student_number }}</td>--}}
                                {{--                                        <td>{{ $student->full_name }}</td>--}}
                                {{--                                        <td>{{ $student->date_of_birth }}</td>--}}
                                {{--                                        <td>{{ $student->mobile}}</td>--}}
                                {{--                                        <td>{{ $student->current_admission->center->name  ?? '' }}</td>--}}
                                {{--                                        <td>{{ $student->created_at->format('Y-m-d')}}</td>--}}


                                {{--                                        <td>{{ $student->current_admission ? ( ($student->current_hefz_plan && $student->current_hefz_plan->status != 'active') ? $student->current_hefz_plan->status : $student->current_admission->status ): 'not registered' }}</td>--}}
                                {{--                                        <td>--}}
{{--                                                                            <a href="{{ route('admission.students.show', $student->id) }}"--}}
{{--                                                                               class="btn btn-success btn-xs" title="View Center"><span--}}
{{--                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>--}}
{{--                                                                             <a href="{{ route('admission.students.edit',$student->id) }}" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a> --}}

{{--                                                                            <button class="btn btn-danger btn-xs"--}}
{{--                                                                                    data-catid={{$student->id}} data-toggle="modal"--}}
{{--                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"--}}
{{--                                                                                                                title="Delete Center"/></button>--}}

                                {{--                                        </td>--}}
                                {{--                                    </tr>--}}
                                {{--                                @endforeach--}}
                                {{--                                </tbody>--}}
                            </table>
                            {{--                                <div class="text-center">--}}
                            {{--                                    {{ $students->links() }}--}}
                            {{--                                </div>--}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- /Row -->
    <!-- Modal -->
    <div class="modal modal-danger fade" id="delete" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title text-center" id="myModalLabel">Delete Confirmation</h4>
                </div>
                <form id="deleteFormInsideModal" style="display:inline" class="" role="form">
                <div class="modal-body">



                    <p class="text-center">
                        Are you sure you want to delete the student?
                    </p>
                    <div class="form-group">
                        <label for="Reason for Deletion">Reason for Deletion</label>
                        <select class="form-control" id="reason" name="reason">
                            <option>نقل السكن</option>
                            <option>السفر النهائي</option>
                            <option>فصل من الحلقة</option>
                            <option>تجاوز فترة الغياب المسموحة</option>
                            <option>مرض</option>
                            <option>بيانات خاطئة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="notice">Notice</label>
                        <textarea class="form-control" id="notice" rows="3" name="notice"></textarea>
                    </div>


                </div>
                <div class="modal-footer">
                    {!! Form::button('Yes, Delete', array(
                        'type' => 'submit',
                        'class' => 'btn btn-danger delete',
                        'title' => 'Delete Student',
                        //'onclick'=>'return confirm("Confirm delete?")'
                )) !!}
                    {!! Form::button('No, Cancel', array(
                       'type' => 'submit',
                       'class' => 'btn btn-success ',
                       'title' => 'No, Cancel',
                       ' data-dismiss'=>'modal'
               )) !!}
                </div>
                {!! Form::close() !!}
            </div>
        </div>
    </div>
    <script src=" http://submissioncheck.com:81/assets/themes/quantum/plugins/datepicker/moment.min.js"></script>
    <script
        src="http://submissioncheck.com:81/assets/themes/quantum/plugins/datepicker/daterangepicker.min.js"></script>
    <script src="http://submissioncheck.com:81/assets/themes/quantum/plugins/datepicker/flatpickr.min.js"></script>
    <script src="http://submissioncheck.com:81/assets/themes/quantum/plugins/datepicker/datepicker.min.js"></script>
    <script src="http://submissioncheck.com:81/assets/themes/quantum/plugins/datepicker/datepicker.en.js"></script>
    <script src="https://cdn.datatables.net/1.10.22/js/jquery.dataTables.min.js"
            type="text/javascript"></script>
    <script src="https://cdn.datatables.net/1.10.22/js/dataTables.bootstrap.min.js"
            type="text/javascript"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"
            type="text/javascript"></script>
    <!-- corals js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.4.0/clipboard.min.js"></script>
    <script type="text/javascript">
        var dynamicURL = "{{url('workplace/admission/students')}}";

        var oTable = $("#UsersDataTable").DataTable({

            "serverSide": true,
            "processing": true,
            "ajax": {
                "url": dynamicURL,

                data: function (d) {
                    d.name = $('input[name=name]').val();
                    d.center = $("#centers").val();
                    d.gender = $("#gender").val();
                    d.account_type = $("#account_type option:selected").text();
                    d.created_at = $('input[name=created_at]').val();
                    d.status = $("#status").val();
                },
                error: function (xhr, error, thrown) {
                    // Parse the JSON response from the server
                    var jsonResponse = JSON.parse(xhr.responseText);

                    // Extract the error message
                    var errorMessage = jsonResponse.message || 'An unknown error occurred';

                    // Form the full error message
                    var fullErrorMessage = `There is an error: ${errorMessage} when accessing this URL: ${dynamicURL}`;

                    // Display the error using Toastr with HTML content
                    var toastrElement = toastr.error(`<div id="copyableToastr" style="cursor: pointer;">${fullErrorMessage} <span style="color: blue;">(Click to copy)</span></div>`, 'Error', {
                        "timeOut": 0,
                        "extendedTimeOut": 0,
                        "closeButton": true,
                        "tapToDismiss": false
                    });

                    // Attach click event to copy the message
                    $(document).on('click', '#copyableToastr', function() {
                        var textArea = document.createElement("textarea");
                        textArea.value = fullErrorMessage;
                        document.body.appendChild(textArea);
                        textArea.select();
                        try {
                            var successful = document.execCommand('copy');
                            var msg = successful ? 'successful' : 'unsuccessful';
                            toastr.success('Error message copied to clipboard');

                            // Remove the original Toastr notification
                            toastrElement.remove();
                        } catch (err) {
                            toastr.error('Failed to copy text');
                        }
                        document.body.removeChild(textArea);
                    });

                    // Log additional details for debugging
                    console.error('Error:', error);
                    console.error('Details:', thrown);
                    console.error('Status:', xhr.status);
                }
            },
            "columns": [
                {data: 'DT_RowIndex', name: 'DT_RowIndex'},
                {data: 'full_name', name: 'full_name'},
                {data: 'full_name_trans', name: 'full_name_trans'},
                {data: 'date_of_birth', name: 'date_of_birth'},
                {data: 'mobile', name: 'mobile'},
                {data: 'center', name: 'center'},
                {data: 'created_at', name: 'created_at'},
                {data: 'status', name: 'status'},
                {data: 'action', name: 'action'},
            ],

            "order": [
                [1, "desc"]
            ]

        });


        $('#search-form').on('submit', function (e) {
            oTable.draw();
            e.preventDefault();
        });

        $(document).on('click', '.clearBtn', function (e) {
            var tableId = $(this).data('table');
            var filtersId = "#" + tableId + "_filters";

            e.preventDefault();

            $(filtersId + ' input').val("");
            $(filtersId + ' select').val("");
            $(filtersId + ' input[type="checkbox"]').val(1);
            $(filtersId + ' input[type="checkbox"]').prop('checked', false);
            if ($.fn.iCheck) {
                $(filtersId + ' input[type="checkbox"]').iCheck('uncheck');
            }


            oTable.draw();
        });

        $(document).on('keydown', '.filters', function (e) {
            if (e.keyCode == 13) {
                var tableId = $(this).data('table');
                window.LaravelDataTables["UsersDataTable"].draw();
            }
        });

        $(".filtersCollapse").on('shown.bs.collapse', function (e) {
            var filtersCollapseHref = "#" + $(this).attr('id');
            var $filterCollapseBtn = $("a[href='" + filtersCollapseHref + "']");
            $filterCollapseBtn.html('<i class="fa fa-remove"></i>');
            $filterCollapseBtn.removeClass('btn-info');
            $filterCollapseBtn.addClass('btn-warning');
        });

        $(".filtersCollapse").on('hidden.bs.collapse', function (e) {
            var filtersCollapseHref = "#" + $(this).attr('id');
            var $filterCollapseBtn = $("a[href='" + filtersCollapseHref + "']");
            $filterCollapseBtn.html('<i class="fa fa-filter"></i>');
            $filterCollapseBtn.removeClass('btn-warning');
            $filterCollapseBtn.addClass('btn-info');
        });
    </script>


    <script>

        $(document).ready(function () {

            // By Hashmat Waziri: used the following technique so that I can use the custom attribute (data-catid)  value of the trigger button (id="delete") when delete ajax is called.

            $(function() {

                var stId = null;
                function getCatId() {
                    $('#delete').on('show.bs.modal', function (event) {
                        var button = $(event.relatedTarget) // Button that triggered the modal
                        var id = button.data('catid'); // Extract info from data-* attributes
                        stId = id;

                    });

                }

                getCatId();



            $('.modal-footer').on('click', '.delete', function(e) {
                e.preventDefault();
                var token = $("meta[name='csrf-token']").attr("content");

                var reason = $("#reason option:selected").text();
                var notice = $("#notice").val();
                $.ajax({
                    type: 'DELETE',
                    url: "{{url("workplace/admission/students/")}}/"+stId,
                    data: {
                        'id': stId,
                        'reason': reason,
                        'notice': notice,
                        "_token": token,
                    },
                    success: function (data) {

                        var parentDiv = $("#delete");
                        $.post(parentDiv.find('form').attr('action'),
                            $("#delete").find('form').serialize(),
                            function(data) {
                            });

                        $("#deleteFormInsideModal :input").prop("disabled", true);
                        //
                        // console.log(data);
                        // $('.item' + $('.id').text()).remove();
                    }
                });
            });

            });



        });




    </script>

    <script type="text/javascript">
        // $(document).ready(function () {
        //
        //     // the following logic is used to to make typeahead compatible with the form input cloning
        //     $(document).on('click', '#btnAdd, add', function () {
        //         initStudentNameTypeahead();
        //     });
        //
        //     $(document).on('click', '#btnAddParty', function () {
        //         initStudentNameTypeahead();
        //
        //     });
        //
        //
        // });


    </script>

    <script>
        // All of the following examples are taken from the Semantic UI official page

        // gender dropdown
        $('.ui.dropdown').dropdown();


        //  student name
        {{--$('.ui.search')--}}
        {{--    .search({--}}
        {{--        apiSettings: {--}}
        {{--            url: '{{route("students.json.data")}}?q={query}'--}}
        {{--        },--}}
        {{--        fields: {--}}
        {{--            results: 'items',--}}
        {{--            title: 'full_name',--}}
        {{--            // url: 'html_url'--}}
        {{--        },--}}
        {{--        minCharacters: 3--}}
        {{--    });--}}

        $('.ui.search')
            .search({
                type          : 'category',
                minCharacters : 3,
                apiSettings   : {
                    onResponse: function(studentNamesResponse) {
                        var
                            response = {
                                results : {}
                            }
                        ;
                        // translate GitHub API response to work with search
                        $.each(studentNamesResponse.items, function(index, item) {
                            var
                                language   = item.nationality || 'Unknown',
                                maxResults = 8
                            ;
                            if(index >= maxResults) {
                                return false;
                            }
                            // create new language category
                            if(response.results[language] === undefined) {
                                response.results[language] = {
                                    name    : language,
                                    results : []
                                };
                            }
                            var title = studentNamesResponse.language == "English" ? item.full_name : item.full_name_trans;
                            var description = studentNamesResponse.language == "English" ? item.full_name_trans : item.full_name;

                            // add result to category
                            response.results[language].results.push({

                                title       : title,
                                description : description,
                                // url         : item.html_url
                            });
                        });
                        return response;
                    },
                    url: '{{route("students.json.data")}}?q={query}'
                }
            })
        ;




        //center data
        $('.field.remote .ui.dropdown')
            .dropdown({
                apiSettings: {
                    url: '{{route("students.centers.json.data")}}{query}',
                    cache: false
                }
            });



        // status
        $('.field.remote .ui.dropdown.status')
            .dropdown({
                apiSettings: {
                    url: '{{route("students.status.json.data")}}{query}',
                    cache: false
                }
            });
    </script>

@endsection

<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Attendance;
use App\BankAccount;
use App\BankAccountType;
use App\Center;
use App\CenterTeacher;
use App\CenterTranslation;
use App\Classes;
use App\ClassTeacher;
use App\GeneralSettings;
use App\LeaveType;
use App\MissedClockOut;
use App\Role;
use App\Employee;
use App\Document;
use App\Traits\Notification;
use App\WeekDay;

use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use DB;
use App\Permission;
use App\EmployeeSalary;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\EmployeeTimetable;
use App\Http\Controllers\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\HumanResource\Http\Requests\EmployeeStoreRequest;
use Modules\HumanResource\Http\Requests\EmployeeUpdateRequest;
use Modules\UserActivityLog\Traits\LogActivity;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;

class EmployeesController extends Controller
{

    use Notification;

    /**
     * @var Attendance
     */
    private $attendanceModal;

    public function __construct(Attendance $attendance)
    {

        $this->attendanceModal = $attendance;


    }

    public function index(Request $request)
    {


        if ($request->ajax()) {
            $tableQuerySnippet = '';
            if (($request->filled('permissions') || $request->filled('roles') or $request->filled('name') or $request->filled('dateRange') or $request->filled('gender') or $request->filled('status') or $request->filled('workMood') or $request->filled('employeeHiredFromYear'))) {


                $whereCondition = '';


//                $whereCondition = '';
                if ($name = $request->name and isset($request->name)) {
                    $bindings['name'] = $name;
                    $requestedName = '%' . $name . '%';

                    $whereCondition .= " AND (employees.name LIKE " . "'" . $requestedName . "'" . " OR employees.display_name LIKE  " . "'" . $requestedName . "'" . " OR employees.full_name LIKE  " . "'" . $requestedName . "'" . " OR employees.full_name_trans LIKE " . "'" . $requestedName . "'" . ")";
                }

                if ($request->status == "inactive" and $request->filled('status')) {
                    $whereCondition = " AND employees.deleted_at is not NULL";
                } else {

                    $whereCondition .= " AND employees.deleted_at is NULL ";
                }

                // Teacher Centers Filter


                if ($request->filled('permissions')) {
                    // Sanitize and prepare permission IDs for SQL IN clause
                    $permissions = array_map('intval', $request->permissions);
                    $permissionsList = implode(',', $permissions);

                    // Add necessary tables to the FROM clause if not already included
                    $tableQuerySnippet .= ', model_has_permissions, permissions';

                    // Ensure the permissions are linked to employees
                    $whereCondition .= " AND model_has_permissions.model_id = employees.id 
                        AND model_has_permissions.permission_id = permissions.id 
                        AND permissions.id IN (" . $permissionsList . ")";
                }




                
                if ($request->has("roles") && $request->filled("roles")) {
                    $roles = $request->get("roles");

                    $arr = join(",", $request->roles);
                    $bindings['roles'] = $roles;
                    $whereCondition .= " AND roles.id IN (" . $arr . ")";

                }

               

                if ($request->filled('dateRange')) {
                    $dateSeperator = explode(",", $request->dateRange);

                    $whereCondition .= " AND date(employees.created_at) between " . "'" . $dateSeperator[0] . "'" . " AND " . "'" . $dateSeperator[1] . "'";

                }

                if (($request->filled('gender'))) {
                    $bindings['gender'] = $request->gender;
                    $whereCondition .= " AND employees.gender = " . "'" . $request->gender . "'";
                }
                if ($workMode = $request->workMood and isset($request->workMood)) {
                    $bindings['workMood'] = $workMode;
                    $whereCondition .= " AND employees.work_mood = " . "'" . $request->workMood . "'";
                }
                

                $tablesKeyMatchQuerySnippet = '';
                if ($request->filled('teacherCenters')) {
                    $arr = join(",", $request->teacherCenters);
                    $whereCondition .= " AND cen_teachers.cen_id IN (" . $arr . ")";
                    $tableQuerySnippet .= ', cen_teachers,';
                    $tablesKeyMatchQuerySnippet .= 'AND employees.id = cen_teachers.emp_id';


                }

                if ($request->filled('employeeHiredFromYear')) {


                    $whereCondition .= " AND year(employees.join_date) >= " . "'" . $request->get('employeeHiredFromYear') . "'";


                }


                // Role-Based Additional Conditions

                if ((auth()->user()->hasRole(['education-manager_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {


                    $arr = Role::where('name', 'supervisor_' . config('organization_id') . '_')->pluck('id');

                    $whereCondition .= " AND roles.id IN (" . $arr[0] . ")";


                    $trxDatatables = DB::select(
                        'SELECT employees.hours_per_month,employees.work_mood,employees.email,employees.deleted_at,employees.status,employees.date_of_birth,employees.id,employees.name,employees.full_name,employees.full_name_trans,employees.display_name,employees.mobile,employees.created_at,employees.gender
                    FROM employees,roles, model_has_roles
                  ' . $tableQuerySnippet . '
                    WHERE employees.id = model_has_roles.model_id
                    AND roles.id = model_has_roles.role_id
                                            ' . $whereCondition . ' group by employees.email');

                }

                elseif ((auth()->user()->hasRole(['supervisor_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {


                    $arr = Role::where('name', 'teacher_' . config('organization_id') . '_')->pluck('id');

//                    $roles_condition = " AND roles.id IN (" . $arr . ")";
                    $whereCondition .= " AND roles.id IN (" . $arr[0] . ")";


                    if ($request->filled('teacherCenters')) {


                        $trxDatatables = DB::select(
                            'SELECT employees.hours_per_month,employees.work_mood,employees.email,employees.deleted_at,employees.status,employees.date_of_birth,employees.id,employees.name,employees.full_name,employees.full_name_trans,employees.display_name,employees.mobile,employees.created_at,employees.gender FROM employees,roles, model_has_roles
                     ' . $tableQuerySnippet . '
                    WHERE employees.id = model_has_roles.model_id
                    AND roles.id = model_has_roles.role_id
                                        
                                            ' . $whereCondition . 'group by employees.email');
                    } else {

                        $trxDatatables = DB::select(
                            'SELECT employees.hours_per_month,employees.work_mood,employees.email,employees.deleted_at,employees.status,employees.date_of_birth,employees.id,employees.name,employees.full_name,employees.full_name_trans,employees.display_name,employees.mobile,employees.created_at,employees.gender FROM employees,roles, model_has_roles
                     ' . $tableQuerySnippet . '
                    WHERE employees.id = model_has_roles.model_id
                    AND roles.id = model_has_roles.role_id
                                        
                                            ' . $whereCondition . 'group by employees.email');

                    }


                }


                else {




                 
                   
                    $trxDatatables = DB::select(
                        'SELECT employees.image,
                                                           employees.hours_per_month,
                                                           employees.work_mood,
                                                           employees.email,
                                                           employees.deleted_at,
                                                           employees.status,
                                                           employees.date_of_birth,
                                                           employees.id,
                                                           employees.name,
                                                           employees.full_name,
                                                           employees.full_name_trans,
                                                           employees.display_name,
                                                           employees.mobile,
                                                           employees.created_at,
                                                           employees.gender
                                                          
                                                    FROM employees,
                                                         roles,
                                                         model_has_roles
                                                         ' . $tableQuerySnippet . '
                                                    WHERE employees.id = model_has_roles.model_id
                                                      AND roles.id = model_has_roles.role_id
                                                        ' . $tablesKeyMatchQuerySnippet . '
                                                        ' . $whereCondition
                    );

                   

                }

                return \Yajra\DataTables\DataTables::of($trxDatatables)
                    ->addColumn('role', function ($employee) use ($request) {


                        $roles = '';
                        $colors = ['orange', 'purple', 'yellow', 'green', 'violet', 'pink', 'brown', 'grey', 'black'];

                        $randomColor = Arr::random($colors);
                        // if no roles filter is selected

                        if ($request->filled('status')) {


                            $employeeWithRoles = Employee::where('id', $employee->id)->with('roles')->withTrashed()->first();

                        } else {

                            $employeeWithRoles = Employee::where('id', $employee->id)->with('roles')->first();

                        }
                        $empModal = Employee::with('roles')->find($employee->id);


                        foreach ($empModal->roles as $role) {


                            // she which supervisor has teachers who have not done OUT record yet
                            if ($role->name == ('supervisor_' . config('organization_id') . '_')) {

                                $arr = Role::where('name', 'teacher_' . config('organization_id') . '_')->pluck('id');
                                $roleId = $arr[0];
                                $employeeId = MissedClockOut::all()->pluck('employee_id')->toArray();

                                $missedClockoutCountforTeacher = Employee::whereIn('id', $employeeId)->whereHas("roles", function ($q) use ($roleId) {
                                    $q->where("id", $roleId);
                                })->whereHas("teacherCenter", function ($q) use ($empModal) {
                                    $supervisorCenId = $empModal->center()->get()->pluck('id');
                                    $supervisorCenId = $supervisorCenId->toArray();
                                    $q->whereIn("cen_id", $supervisorCenId);
                                })->count();

                                if ($missedClockoutCountforTeacher > 0) {
                                    $roles .= '<div class="ui compact menu">
                                          <a class="item label ' . "$randomColor" . '" data-position="bottom left" data-tooltip="' . $missedClockoutCountforTeacher . ' teacher(s) have not clocked out" data-inverted="">
                                           ' . $role->description . '
                                            <div class="floating ui red label" >' . $missedClockoutCountforTeacher . '</div>
                                          </a>

                                        </div>';

                                } else {
                                    $roles .= '<div class="ui compact menu">
                                          <a class="item label  ' . "$randomColor" . '">
                                             ' . $role->description . '
                                            
                                          </a>

                                        </div>';

                                }


                            } else {
                                $roles .= '<div class="ui compact menu">
                                          <a class="item label">
                                             ' . $role->description . '
                                            
                                          </a>

                                        </div>';

//                                $roles .= '<a class="ui ' . "$randomColor" . ' label">' . $role->description . '</a>&nbsp;';


                            }


                        }


//

                        return $roles;

                    })
                    ->addColumn('image', function ($employee) use ($request) {


                        $genderBasedDefaultImage = $employee->gender == 'Male' || $employee->gender == 'Male (ذكر)' || $employee->gender == 'male' ? asset('uploads/staff/demo/mstaff.jfif') : asset('uploads/staff/demo/fstaff.jpg');
                        $image = '';

                        if (file_exists($employee->image)) {

                            $image = asset($employee->image);
                        } elseif (Str::contains($employee->image, 'http')) {

                            $image = $employee->image;
                        } else {
                            $image = $genderBasedDefaultImage;


                        }


//                        return $image;


                        // TODO: replace with employee route
                        $stShowRoute = route('employees.show', $employee->id);
                        $genderColor = $employee->gender == 'Male' || $employee->gender == 'male' || $employee->gender == 'Male (ذكر)' ? '#34b8bc;!important' : '#FA5661;!important';
                        $fulllnameHtml = '';
                        if (strlen($employee->full_name) > 35) {

                            $fullname = Str::limit(Str::title($employee->full_name), 35, ' ...');
                            $fulllnameHtml = '<a style="color:' . $genderColor . '" target="_blank" href="' . $stShowRoute . '" ><strong  style="font-size: 16px;" data-tooltip="' . Str::title($employee->full_name) . '" >' . $fullname . '</strong></a>';
                        } else {
                            $fullname = Str::title($employee->full_name);
                            $fulllnameHtml = '<a style="color:' . $genderColor . '" target="_blank" href="' . $stShowRoute . '" ><strong style="font-size: 16px;"  style="color:"' . $genderColor . '">' . $fullname . '</strong></a>';
                        }

                        $email = Str::title($employee->email);
                        $emailHtml = '';
                        if (strlen($employee->email) > 35) {
                            $email = Str::limit(Str::title($email), 35, ' ...');

                            $emailHtml .= '<strong  data-tooltip="' . Str::title($employee->email) . '" style="color: #34b8bc ;    top: 1px;">' . $email . '</strong><br>';
                        } else {
                            $emailHtml .= '<strong  style="color: #34b8bc;    top: 10px;">' . $email . '</strong><br>';

                        }
                        $applicationDateHtml = value(Carbon::parse($employee->created_at))->diffForHumans();

                        $stShowRoute = is_null($employee->deleted_at) == true ?
                            route('employees.show', $employee->id)
                            : route('employees.show.archived', $employee->id);

                        $empModal = Employee::with('roles')->find($employee->id);


                        if ((!is_null($empModal)) && ($empModal->hasRole(['supervisor_' . config('organization_id') . '_']))) {

                            $supervisorCenId = $empModal->center()->get()->pluck('id');
                            $supervisorCenId = $supervisorCenId->toArray();

                            $centerNames = Center::find($supervisorCenId)->pluck('name');


                            $supervisorTeachersCount = DB::table('cen_teachers')->whereIn("cen_id", $supervisorCenId)->count();
                            $supervisorCentershtml = '';
//                            if ($supervisorTeachersCount > 0) {

                            // Wrap the clickable part (teachers count + centers) in a span with a dedicated class
                            $supervisorCentershtml = '<div class="row">
        <div class="col-md-5">
            <a target="_blank" style="color: #E2EEFF; text-decoration: underline; cursor: pointer;" href="'.route('mindmap.supervisor.show', ['employee_id' => $employee->id]).'" class=" ">
                Supervises
            </a> <span >'
                                . $supervisorTeachersCount . ' teachers across ' . collect($centerNames)->implode(', ') .
                                '</span>
       
    </div></div>';

//                            }
                        }


                        $result = ' <div class="row">
      <div class="col-md-5" style="
         margin-top: 63px;
         "><img style="border-radius: 50%" width="100" height="100" src="' . $image . '"></div>
         <div class="col-md-4 col-md-push-1" style="margin-top: 107px;"><a target="_blank" href="' . $stShowRoute . '" class="ui mini yellow basic button"> View Profile</a></div>
   </div>
   <div class="row" style="margin-top: -20px;">
      <div class="col-md-12" style="
         font-weight: bolder;
         font-size: 21px;padding-bottom: 8px;
         ">' . $fulllnameHtml . '</div>
   </div>
   <div class="row">
      <div class="col-md-12" style="color: #E2EEFF;"><i class="envelope icon"></i>: ' . $emailHtml . '</div>
   </div>
   <div class="row">
      <div class="col-md-12" style="color: #E2EEFF "><i class="phone icon"></i>: ' . $employee->mobile . '</div>
   </div>
   <div class="row">
      <div style="color: #E2EEFF " data-tooltip= ' . value(Carbon::parse($employee->created_at))->format('Y-d-M  g:i a') . ' class="col-md-12"><i class="calendar alternate outline icon"></i>: Joined ' . $applicationDateHtml . '</div>
   </div>
    <div class="row">
      <div data-tooltip= ' . value(Carbon::parse($employee->date_of_birth))->format('Y-d-M  g:i a') . '   class="col-md-12" style="color: #E2EEFF "><i class="hourglass half icon"></i>: Age ' . Carbon::parse($employee->date_of_birth)->age . '</div>
   </div>
   <div class="row">
      <div class="col-md-12" style="color: #E2EEFF "><i class="hourglass half icon"></i>: Emp.No ' . $employee->employee_number . '</div>
   </div>
   <div class="row">
      <div class="col-md-12" style="color: #E2EEFF "><i class="hourglass half icon"></i>: Id.No ' . $employee->identity_number . '</div>
   </div><br>' . $supervisorCentershtml;
                        return $result;

                    })

                    ->addColumn('status', function ($employee) use ($request) {
                        if (!is_null($employee->deleted_at)) {

                            // return a toggle
                            $input = '
<button  
data-empId="' . $employee->id . '"
 data-stname="' . $employee->full_name . '" 
 data-classid="' . $employee->class_id . '" 
 data-center="' . $employee->center . '" 
 data-class="' . $employee->className . '" 
 class="ui button archiveModalTriggerBtn "
 id="archiveModalTriggerBtn ">
    Activate
                               </button>';
                            return $input;
                        } else {

                            return $employee->status;
                        }


                    })

                    ->addColumn('action', function ($row) use ($request) {



                        if (\Auth::user()->can("update employee")) {
                            $stEditRoute = route('employees.edit', $row->id);
                            $viewBtnTitle = "view " . $row->full_name;

                            // different url for archived ( deleted) employees
                            $stShowRoute = is_null($row->deleted_at) == true ?
                                route('employees.show', $row->id)
                                : route('employees.show.archived', $row->id);

                            $viewBtnTitle = "view " . $row->full_name;
                            $editBtnTitle = "edit " . $row->full_name;
                            $archiveBtnTitle = "archive " . $row->full_name;
                            $deleteBtnTitle = "delete " . $row->full_name;
                            $btns = '';
                            $empAttendanceRoute = route('individual.employee.monthly.attendance', $row->id);
                            if ($row->work_mood == 4/** per_month */) {
                                $btns = '<a target="_blank" href="' . $empAttendanceRoute . '" class="btn btn-primary btn-xs" title="Show Attendance"><span class="glyphicon glyphicon-calendar" aria-hidden="true"/></a>';
                            }
                            if ($row->status == "active") {
                                $impersonationRoute = route('multiAuthImpersonate', ['id' => $row->id, 'guardName' => 'employee']);

                                $btns .= ' <a target="_blank" href="' . $impersonationRoute . '" class="btn btn-success btn-xs"/>Login</a>
                           
                            
                               <button  title="' . $archiveBtnTitle . '" class="btn btn-warning btn-xs archiveModalTriggerBtn " id="archiveModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#archive"><span class="glyphicon glyphicon-file"
                               /></button>
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>
                              

';


                            } else {
                                $btns = ' <a href="' . $stShowRoute . '" class="btn btn-success btn-xs" title="' . $viewBtnTitle . '">
                            <span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                             
                            
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                if ($row->work_mood == 4/** per_month */) {
                                    $btns .= '<a target="_blank"  href="' . $empAttendanceRoute . '" class="btn btn-primary btn-xs" title="Show Attendance"><span class="glyphicon glyphicon-calendar" aria-hidden="true"/></a>';
                                }


                                return $btns;

                            }


                            return $btns;
                        }

                        if (\Auth::user()->can("delete employee")) {
                            $stEditRoute = route('employees.edit', $row->id);
                            $stShowRoute = route('employees.show', $row->id);


                            $archiveBtnTitle = "archive " . $row->full_name;
                            $deleteBtnTitle = "delete " . $row->full_name;


                            if ($row->status == "active") {


                                $btns = '<button  title="' . $archiveBtnTitle . '" class="btn btn-warning btn-xs archiveModalTriggerBtn " id="archiveModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#archive"><span class="glyphicon glyphicon-file"
                               /></button>
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';

                            } else {
                                $btns = ' <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                return $btns;

                            }


                            return $btns;
                        }

                    })->
                    rawColumns(['role', 'action', 'login', 'status', 'full_name', 'image'])
                    ->make(true);


            }
            else {

                if ((auth()->user()->hasRole(['education-manager_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {
                    $arr = Role::where('name', 'supervisor_' . config('organization_id') . '_')->pluck('id');
                    $roleId = $arr[0];
                    $whereCondition = '';
                    $employeeDatatables = Employee::with(["roles" => function ($q) use ($roleId) {

                        $q->where('id', $roleId);


                    }])
                        ->whereHas("roles", function ($q) use ($roleId) {
                            $q->where("id", $roleId);
                        })
                        ->select();


                } elseif ((auth()->user()->hasRole(['supervisor_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {
                    $arr = Role::where('name', 'teacher_' . config('organization_id') . '_')->pluck('id');
                    $roleId = $arr[0];


                    $supervisorCenId = auth()->user()->center()->get()->pluck('id');
                    $supervisorCenId = $supervisorCenId->toArray();
                    $employeeDatatables = Employee::with(["roles" => function ($q) use ($roleId) {
                        $q->where('id', $roleId);
                    }])->whereHas("roles", function ($q) use ($roleId) {
                        $q->where("id", $roleId);
                    })->with(["teacherCenter" => function ($q) use ($supervisorCenId) {
                        $q->whereIn('cen_id', $supervisorCenId);

                    }])->whereHas("teacherCenter", function ($q) use ($supervisorCenId) {
                        $supervisorCenId = auth()->user()->center()->get()->pluck('id');
                        $supervisorCenId = $supervisorCenId->toArray();
                        $q->whereIn("cen_id", $supervisorCenId);
                    })->select();


                } else {


                    $employeeDatatables = Employee::with("roles",'permissions')->select();


                }

                return \Yajra\DataTables\DataTables::of($employeeDatatables)
                    ->filterColumn('image', function ($query, $keyword) {
                        $sql = "CONCAT(employees.full_name,' ',employees.full_name_trans,' ',employees.name,' ',employees.mobile,' ',employees.date_of_birth,' ',employees.email,' ',employees.full_name_trans,' ',employees.nationality,' ',employees.status,' ',employees.employee_number,' ',employees.identity_number)  like ?";
                        $query->whereRaw($sql, ["%{$keyword}%"]);
                    })
                    ->addIndexColumn()
                    ->addColumn('image', function ($employee) use ($request) {
                        $genderBasedDefaultImage = $employee->gender == 'Male' || $employee->gender == 'Male (ذكر)' || $employee->gender == 'male' ? asset('uploads/staff/demo/mstaff.jfif') : asset('uploads/staff/demo/fstaff.jpg');
                        $image = '';
                        if (file_exists($employee->image)) {
                            $image = asset($employee->image);
                        } elseif (Str::contains($employee->image, 'http')) {

                            $image = $employee->image;
                        } else {
                            $image = $genderBasedDefaultImage;
                        }
//                        return $image;

                        // TODO: replace with employee route
                        $stShowRoute = route('employees.show', $employee->id);
                        $genderColor = $employee->gender == 'Male' || $employee->gender == 'male' || $employee->gender == 'Male (ذكر)' ? '#34b8bc;!important' : '#FA5661;!important';
                        $fulllnameHtml = '';
                        if (strlen($employee->full_name) > 35) {
                            $fullname = Str::limit(Str::title($employee->full_name), 35, ' ...');


                            $fulllnameHtml = '<a style="color:' . $genderColor . '" target="_blank" href="' . $stShowRoute . '" ><strong  style="font-size: 16px;" data-tooltip="' . Str::title($employee->full_name) . '" >' . $fullname . '</strong></a>';
                        } else {
                            $fullname = Str::title($employee->full_name);
                            $fulllnameHtml = '<a style="color:' . $genderColor . '" target="_blank" href="' . $stShowRoute . '" ><strong style="font-size: 16px;" data-tooltip="' . Str::title($employee->full_name) . '" style="color:"' . $genderColor . '">' . $fullname . '</strong></a>';
//                            return '<a target="_blank" href="' . $stShowRoute . '" ><strong  style="color: #34b8bc">' .$fullname. '</strong></a>';
                        }

                        $email = Str::title($employee->email);
                        $emailHtml = '';
                        if (strlen($employee->email) > 30) {
                            $email = Str::limit(Str::title($email), 30, ' ...');

                            $emailHtml .= '<strong  data-tooltip="' . Str::title($employee->email) . '" style="color: #34b8bc ;    top: 1px;">' . $email . '</strong><br>';
                        } else {
                            $emailHtml .= '<strong  style="color: #34b8bc;    top: 10px;">' . $email . '</strong><br>';

                        }
                        $applicationDateHtml = value($employee->created_at)->diffForHumans();

//                        TODO: add condition here to only show this button if the person can update an employee
                        $stShowRoute = is_null($employee->deleted_at) == true ?
                            route('employees.show', $employee->id)
                            : route('employees.show.archived', $employee->id);


                        $empModal = Employee::with('roles')->find($employee->id);
                        if ((!is_null($empModal)) && ($empModal->hasRole(['supervisor_' . config('organization_id') . '_']))) {

                            $supervisorCenId = $empModal->center()->get()->pluck('id');
                            $supervisorCenId = $supervisorCenId->toArray();

                            $centerNames = Center::find($supervisorCenId)->pluck('name');


                            $supervisorTeachersCount = DB::table('cen_teachers')->whereIn("cen_id", $supervisorCenId)->count();
                            $supervisorCentershtml = '';
//                            if ($supervisorTeachersCount > 0) {
                            $supervisorCentershtml = '<div class="row">
      <div class="col-md-12 p-wrap" style="color: #E2EEFF ">Supervises ' . $supervisorTeachersCount . ' teachers across ' . collect($centerNames)->implode(', ') . '</div>
   </div>';

//                            }
                        }


                        $result = ' <div class="row">
      <div class="col-md-5" style="
         margin-top: 63px;
         "><img style="border-radius: 50%" width="100" height="100" src="' . $image . '"></div>
         <div class="col-md-4 col-md-push-1" style="margin-top: 107px;"><a target="_blank" href="' . $stShowRoute . '" class="ui mini yellow basic button"> View Profile</a></div>
   </div>
   <div class="row" style="margin-top: -20px;">
      <div class="col-md-12" style="
         font-weight: bolder;
         font-size: 21px;padding-bottom: 8px;
         ">' . $fulllnameHtml . '</div>
   </div>
   <div class="row">
      <div class="col-md-12" style="color: #E2EEFF;"><i class="envelope icon"></i><a href = "mailto:' . $employee->email . '">: ' . $emailHtml . '</a></div>
   </div>
   <div class="row">
      <div class="col-md-12" style="color: #E2EEFF "><i class="phone icon"></i>: ' . $employee->mobile . '</div>
   </div>
   <div class="row">
      <div style="color: #E2EEFF " data-tooltip= ' . value($employee->created_at)->format('Y-d-M  g:i a') . ' class="col-md-12"><i class="calendar alternate outline icon"></i>: Joined ' . $applicationDateHtml . '</div>
   </div>
    <div class="row">
      <div data-tooltip= ' . value(Carbon::parse($employee->date_of_birth))->format('Y-d-M  g:i a') . ' class="col-md-12" style="color: #E2EEFF "><i class="hourglass half icon"></i>: Age ' . Carbon::parse($employee->date_of_birth)->age . '</div>
   </div>
   <div class="row">
      <div class="col-md-12" style="color: #E2EEFF "><i class="hourglass half icon"></i>: Emp.No ' . $employee->employee_number . '</div>
   </div>
   <div class="row">
      <div class="col-md-12" style="color: #E2EEFF "><i class="hourglass half icon"></i>: Id.No ' . $employee->identity_number . '</div>
   </div>
   <br>
    ' . $supervisorCentershtml;
                        return $result;

                    })
                    ->addColumn('role', function (Employee $employee) {
                        $roles = '';
                        $colors = ['orange', 'purple', 'yellow', 'green', 'violet', 'pink', 'brown', 'grey', 'black'];


                        $empModal = Employee::with('roles')->find($employee->id);


                        foreach ($empModal->roles as $role) {
                            $randomColor = Arr::random($colors);


                            // she which supervisor has teachers who have not done OUT record yet
                            if ($role->name == ('supervisor_' . config('organization_id') . '_')) {

                                $arr = Role::where('name', 'teacher_' . config('organization_id') . '_')->pluck('id');
                                $roleId = $arr[0];
                                $employeeId = MissedClockOut::all()->pluck('employee_id')->toArray();

                                $missedClockoutCountforTeacher = Employee::whereIn('id', $employeeId)->whereHas("roles", function ($q) use ($roleId) {
                                    $q->where("id", $roleId);
                                })->whereHas("teacherCenter", function ($q) use ($empModal) {
                                    $supervisorCenId = $empModal->center()->get()->pluck('id');
                                    $supervisorCenId = $supervisorCenId->toArray();
                                    $q->whereIn("cen_id", $supervisorCenId);
                                })->count();

                                if ($missedClockoutCountforTeacher > 0) {
                                    $roles .= '<div class="ui compact menu ">
                                          <a class="item label ' . "$randomColor" . '" data-position="bottom left" data-tooltip="' . $missedClockoutCountforTeacher . ' teacher(s) have not clocked out" data-inverted="">
                                            ' . $role->description . '
                                            <div class="floating ui red label" >' . $missedClockoutCountforTeacher . '</div>
                                          </a>

                                        </div>';

                                } else {
                                    $roles .= '<div class="ui compact menu">
                                          <a class="item label ' . "$randomColor" . '">
                                            ' . $role->description . '
                                            
                                          </a>

                                        </div>';

                                }


                            } else {
                                $roles .= '<div class="ui compact menu">
                                          <a class="item label ">
                                            ' . $role->description . '
                                            
                                          </a>

                                        </div>';

//                                $roles .= '<a class="ui ' . "$randomColor" . ' label">' . $role->description . '</a>&nbsp;';


                            }


                        }
                        return $roles;

                    })
                    ->addColumn('action', function ($row) use ($request) {

                        $impersonationRoute = route('multiAuthImpersonate', ['id' => $row->id, 'guardName' => 'employee']);
                        $empAttendanceRoute = route('individual.employee.monthly.attendance', $row->id);
                        $btns = '';
                        if ($row->work_mood == 4/** per_month */) {
                            $btns = '<a target="_blank" href="' . $empAttendanceRoute . '" class="btn btn-primary btn-xs" title="Show Attendance"><span class="glyphicon glyphicon-calendar" aria-hidden="true"/></a>';
                        }
                        if ((auth()->user()->hasRole(['education-manager_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {

                            if (\Auth::user()->can("update employee")) {
                                $stEditRoute = route('employees.edit', $row->id);
                                $stShowRoute = route('employees.show', $row->id);

                                $viewBtnTitle = "view " . $row->full_name;
                                $editBtnTitle = "edit " . $row->full_name;
                                $deleteBtnTitle = "delete " . $row->full_name;

                                if ($row->status == "active") {


                                    $btns = '<a href="' . $impersonationRoute . '" class="btn btn-success btn-xs"/>Login</a> 
                                   
                              
                               
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';


                                } else {
                                    $btns = ' <a href="' . $impersonationRoute . '" class="btn btn-success btn-xs"/>Login</a> 
                             
                            
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';

                                    return $btns;

                                }


                                return $btns;
                            }

                            if (\Auth::user()->can("delete employee")) {
                                $stEditRoute = route('employees.edit', $row->id);
                                $stShowRoute = route('employees.show', $row->id);


                                $deleteBtnTitle = "delete " . $row->full_name;


                                if ($row->status == "active") {


                                    $btns = '<button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';

                                } else {
                                    $btns = ' <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                    return $btns;

                                }


                                return $btns;
                            }
                        } elseif ((auth()->user()->hasRole(['supervisor_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {
                            if (\Auth::user()->can("update employee")) {

                                $deleteBtnTitle = "delete " . $row->full_name;

                                if ($row->status == "active") {
                                    $btns .= ' <a href="' . $impersonationRoute . '" class="btn btn-success btn-xs"/>Login</a> 
                             
                               
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';


                                } else {
                                    $btns .= ' <a href="' . $impersonationRoute . '" class="btn btn-success btn-xs"/>Login</a> 
                             
                            
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';

                                    return $btns;

                                }


                                return $btns;
                            }

                            if (\Auth::user()->can("delete employee")) {
                                $stEditRoute = route('employees.edit', $row->id);
                                $stShowRoute = route('employees.show', $row->id);


                                $deleteBtnTitle = "delete " . $row->full_name;

                                if ($row->status == "active") {


                                    $btns = '<button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';


                                } else {
                                    $btns = ' <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                    return $btns;

                                }


                                return $btns;
                            }

                        } else {

                            if (\Auth::user()->can("update employee")) {
                                $stEditRoute = route('employees.edit', $row->id);
                                $stShowRoute = route('employees.show', $row->id);

                                $viewBtnTitle = "view " . $row->full_name;
                                $editBtnTitle = "edit " . $row->full_name;
                                $archiveBtnTitle = "archive " . $row->full_name;
                                $deleteBtnTitle = "delete " . $row->full_name;

                                if ($row->status == "active") {

                                    $empAttendanceRoute = route('individual.employee.monthly.attendance', $row->id);
                                    if ($row->work_mood == 4/** per_month */) {
                                        $btns = '<a target="_blank" href="' . $empAttendanceRoute . '" class="btn btn-primary btn-xs" title="Show Attendance"><span class="glyphicon glyphicon-calendar" aria-hidden="true" /></a>';
                                    }
                                    $btns .= ' <a href="' . $impersonationRoute . '" class="btn btn-success btn-xs"/>Login</a> 
                              
                               <button  title="' . $archiveBtnTitle . '" class="btn btn-warning btn-xs archiveModalTriggerBtn " id="archiveModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#archive"><span class="glyphicon glyphicon-file"
                               /></button>
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>
                               <button   class="btn btn-primary assignPermissionBtn" data-employee-id="'.$row->id.'">Assign Permission</button>';

                                }
                                else
                                {
                                    $btns = ' <a href="' . $impersonationRoute . '" class="btn btn-success btn-xs"/>Login</a> 
                             
                            
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';

                                    return $btns;

                                }




                                return $btns;
                            }

                            if (\Auth::user()->can("delete employee")) {
                                $stEditRoute = route('employees.edit', $row->id);
                                $stShowRoute = route('employees.show', $row->id);

                                $viewBtnTitle = "view " . $row->full_name;

                                $archiveBtnTitle = "archive " . $row->full_name;
                                $deleteBtnTitle = "delete " . $row->full_name;


                                if ($row->status == "active") {


                                    $btns = '<button  title="' . $archiveBtnTitle . '" class="btn btn-warning btn-xs archiveModalTriggerBtn " id="archiveModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#archive"><span class="glyphicon glyphicon-file"
                               /></button>
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';

                                } else {
                                    $btns = ' <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                    return $btns;

                                }


                                return $btns;
                            }
                        }

                    })
                    ->rawColumns(['role', 'action', 'login', 'status', 'full_name', 'image'])
                    ->make(true);
            }
        }

        $topFiveSalaries = EmployeeSalary::with('employee')->limit(7)->orderBy('basic_salary', 'desc')->get();
        $averageSalariesPerDepartment = DB::select("
        
                        SELECT a.departmentName, ROUND(SUM(a.salary)/ COUNT(*)) AS avgSalary
                        FROM (
                        SELECT e.salary,d.department  AS departmentName
                        FROM employees e, 
                        departments d
                        WHERE e.department_id = d.id 
                        and e.deleted_at is null
                        ORDER BY e.salary) a
                        
                        
                        GROUP BY departmentName
                        ORDER BY avgSalary;
        ");


//        $topFiveSalariesTotal = DB::select("SELECT SUM(salary) as salaryTotal FROM (
//        SELECT e.full_name,  round(es.basic_salary,0) salary
//                FROM employee_salaries es,employees e
//                WHERE es.employee_id = e.id
//                and e.deleted_at is null
//                ORDER BY es.basic_salary DESC
//                LIMIT 7 ) a");
//        $topFiveSalariesTotal = round($topFiveSalariesTotal[0]->salaryTotal, 2);


//        $highestSalaryperDepartment = DB::select("# employees with highest salary in each of the department
//
//                                    SELECT d.department,e.full_name, e.salary
//                                    FROM employees e, departments d
//                                    WHERE e.department_id = d.id
//                                    AND (e.department_id,e.salary) IN
//                                    (
//                                    SELECT e.department_id, MAX(e.salary)
//                                    FROM employees e
//                                    where e.deleted_at is null
//                                    GROUP BY e.department_id
//
//                                    )
//                                    and e.deleted_at is null
//                                    ORDER BY salary DESC"
//        );
//
//        $avgSalaryPerRole = DB::select("
//
//                        -- calculate the highest average salary for a given job.
//                        -- display each unique job, the total average salary, the total people and the total salary
//                        -- and order by highest average salary.
//
//                        SELECT r.description as roleName, round( AVG(e.salary),2) as avgSalary,
//                        count(*) as totalPeople, round(sum(e.salary),2) as total_salary
//                        FROM employees e,
//                        model_has_roles mhr,
//                        roles r
//                        WHERE e.id = mhr.model_id
//                        AND mhr.role_id = r.id
//                        and e.deleted_at is null
//                        GROUP BY r.description
//                        order by avgSalary desc;
//
//        "
//        );
        $modelType = 'App\Employee';

        $permissions = Permission::query()
            ->join('model_has_permissions', 'permissions.id', '=', 'model_has_permissions.permission_id')
            ->where('model_has_permissions.model_type', $modelType)
            ->select('permissions.*')
            ->distinct()
            ->get();
        $guardName = 'employee';
        $roles = Role::query()
            ->join('model_has_roles', 'roles.id', '=', 'model_has_roles.role_id')
            ->where('model_has_roles.model_type', $modelType)
            ->where('roles.guard_name', $guardName)
            ->select('roles.*')
            ->distinct()
            ->get();


        return view('humanresource::employees.list', compact('roles','permissions'));
    }


    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {


        $roles = \App\Role::all()->filter(function ($value, $item) {

            return !empty($value->description);
        })->pluck('description', 'name')->prepend('Select Role ...', '');

        $centers = \App\Center::all()->sortBy('location')->pluck('location', 'id')->prepend('Select Sup Center...', "");


//        $roles = Role::pluck('description', 'name');

        return view('humanresource::employees.create', compact('roles', 'centers'));
    }


    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public
    function store(EmployeeStoreRequest $request)
    {


        try {
            DB::beginTransaction();


            // Create the employee
            $employee = new Employee;
            $employee->employee_number = $request->employee_number;
            $employee->full_name = $request->full_name;
            $employee->full_name_trans = $request->full_name_trans;
            $employee->name = $request->name;
            $employee->gender = $request->gender;
            $employee->date_of_birth = $request->date_of_birth;
            $employee->email = $request->email;
            $employee->mobile = $request->mobile;
            $employee->address_1 = $request->address_1;
            $employee->address_2 = $request->address_2;
            $employee->address_state = $request->province_state;
            $employee->address_city = $request->city;
            $employee->address_country = $request->country;
            $employee->organization_id = config('organization_id');
            $employee->password = bcrypt($request->get('password'));
            $employee->nationality = $request->nationality;
            $employee->identity_type = $request->identity_type;
            $employee->identity_number = $request->identity;
            $employee->epf_no = $request->epf_no;
            $employee->status = 'active';
            $employee->department_id = $request->department_id;
            $employee->contract_type = $request->contract_type;
            $employee->leave_applicable_date = Carbon::parse($request->leave_applicable_date)->format('Y-m-d');

            $employee->start_at = $request->start_at;

            $employee->work_mood = $request->work_mood;
            // if work_mood is task_based, then make hours_per_month null else populate with its value
            if ($request->get('work_mood') == 4 /** 'per_month' */) {

                if ($request->get('hours_per_month')) {
                    $employee->hours_per_month = $request->get('hours_per_month');
                }
            } else {
                $employee->hours_per_month = null;

            }

            $employee->marital_status = $request->get('marital_status');

            $employee->save();


            $employee->syncRoles($request->roles);


//            attach employee to the leave types that are available currently in the database
            $leaveTypeIds = LeaveType::all()->pluck('id');
            foreach ($leaveTypeIds as $id) {
                // this will add details to the employee_id and leave_type_id columns
                $employee->leaveType()->attach($id,
                    [
                        'organization_id' => config('organization_id'),
                        'created_at' => Carbon::now(),
                        'year' => Carbon::now()->year
                    ]);
            }

            $resume = "";
            if ($request->file('resume') != "") {

                $file = $request->file('resume');

                $resumeTitle = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('uploads/staff/resume/', $resumeTitle);
                $resume = 'public/uploads/staff/resume/' . $resumeTitle;

                $document = new Document();
                $document->title = $resumeTitle;
                $document->documentable_id = $employee->id;
                $document->documentable_type = 'App\Employee';
                $document->type = 'stf';
                $document->file = $resume;
                $document->created_by = Auth()->user()->id;
                $document->organization_id = config('organization_id');
                $document->save();

            }
            $employee->syncRoles($request->roles);

            $leaveTypeIds = LeaveType::all()->pluck('id');
            $employee->leaveType()->attach($leaveTypeIds->mapWithKeys(function ($id) {
                return [$id => [
                    'organization_id' => config('organization_id'),
                    'created_at' => now(),
                    'year' => now()->year,
                ]];
            })->toArray());

            if ($request->hasFile('resume')) {
                $resumeTitle = md5($request->file('resume')->getClientOriginalName() . time()) . '.' . $request->file('resume')->getClientOriginalExtension();
                $resume = $request->file('resume')->storeAs('public/uploads/staff/resume', $resumeTitle);

                $employee->documents()->createMany([[
                    'title' => $resumeTitle,
                    'type' => 'stf',
                    'file' => $resume,
                    'created_by' => Auth()->user()->id,
                    'organization_id' => config('organization_id'),
                ]]);
            }


            // for upload Employee Joining Letter
            $joining_letter = "";
            if ($request->file('joining_letter') != "") {
                $file = $request->file('joining_letter');
                $joining_letterTitle = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('uploads/staff/staff_joining_letter/', $joining_letterTitle);
                $joining_letter = 'public/uploads/staff/staff_joining_letter/' . $joining_letterTitle;
                $document = new Document();
                $document->title = $joining_letterTitle;
                $document->documentable_id = $employee->id;
                $document->documentable_type = 'App\Employee';

                $document->type = 'stf';
                $document->file = $joining_letter;
                $document->created_by = Auth()->user()->id;
                $document->organization_id = config('organization_id');
                $document->save();


            }

            // for upload Employee Other Documents
            $other_document = "";
            Required:
            if ($request->file('other_document') != "") {
                $file = $request->file('other_document');
                $other_documentTitle = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('uploads/staff/others_documents/', $other_documentTitle);
                $other_document = 'public/uploads/staff/others_documents/' . $other_documentTitle;

                $document = new Document();
                $document->title = $other_documentTitle;
                $document->documentable_id = $employee->id;
                $document->documentable_type = 'App\Employee';
                $document->type = 'stf';
                $document->file = $other_document;
                $document->created_by = Auth()->user()->id;
                $document->organization_id = config('organization_id');
                $document->save();


            }

            $employee->resume = $resume;
            $employee->joining_letter = $other_document;
            $employee->other_document = $other_document;



            if ($request->has('bank_name') || $request->filled('bank_name')) {


                //if no bank details available , then create

                foreach ($request->get('bank_name') as $key => $bankNameId) {

                    $accName = $request->get('bank_account_name')[$key];
                    $accNumber = $request->get('bank_account_no')[$key];
                    $accType = $request->get('bank_account_type')[$key];
                    $accNote = $request->get('bank_notes')[$key];
                    BankAccount::create([
                        'account_name' => strtoupper($accName),
                        'account_number' => $accNumber,
                        'account_type_id' => $accType,
                        'note' => $accNote,
                        'bank_id' => $bankNameId,
                        'emp_id' => $employee->id,

                    ]);

                }


            }
            if ($request->filled('teacher_halaqah')) {


                $employee->teacherCenter()->attach($request->get('teacher_center'));
                $halaqahs = $request->teacher_halaqah;

                $data = array();
                foreach ($halaqahs as $halaqah) {
                    if (!empty($halaqah)) {
                        $data[] = [
                            'class_id' => $halaqah,
                            'employee_id' => $employee->id,
                            'start_date' => $request->get('halaqah_start_date'),
                            'created_at' => Carbon::now(),

                        ];

                    }
                }
                ClassTeacher::insert($data);
//                $employeeClass = ClassTeacher::create([
//                    'employee_id' => $employee->id,
//                    'class_id' => $request->get('teacher_halaqah'),
//                    'start_date' => $request->get('halaqah_start_date'),
//                ]);

            }

            if ($request->has('supervisor_center')) {
                $employee->center()->attach($request->get('supervisor_center'));

            }


            $employee_salary = EmployeeSalary::create([
                'employee_id' => $employee->id,
//                "start_at" => $request->start_at,
                "status" => 'active',
                'basic_salary' => $request->basic_salary,
//                'hours_per_month' => $request->work_mood == 'per_month' ? $request->hours_per_month : $request->hours_per_month*20 /** this condition will be hour, so dont be confused ;) */,
//                'hours_per_day' => $request->work_mood == 'per_hour' ? $request->hours_per_month : null

            ]);
            if ($request->get('work_mood') == 4/** 'per_month'  */) {
                if ($request->get('hours_per_month')) {
                    $days = $request->get('days');
                    foreach ($days as $day) {
                        EmployeeTimetable::create([
                            'employee_id' => $employee->id,
                            'day' => $day,
//                            'clockin' => '09:00:00',
//                            'clockout' => '18:00:00',
                            'break' => '0'
                        ]);

                    }

                    foreach ($request->get('start') as $day => $time) {

                        $employee->timetable()->where('day', $day)->update([


                            'clockin' => Carbon::parse($time)->toTimeString()
                        ]);


                    }
                    foreach ($request->get('end') as $day => $time) {

                        $employee->timetable()->where('day', $day)->update([


                            'clockout' => Carbon::parse($time)->toTimeString()
                        ]);


                    }


                }
            }

            if ($request->has('user_notification')) {


                $user_info[0]["empName"] = $request->full_name;
                if ($request->has('login_info')) {
                    $user_info[0]["empEmail"] = $request->email;
                    $user_info[0]["empPassword"] = $request->password;

                }






                // TODO: this email-sending feature should be implemented in a separate class, not in the controller

                $apiKey = '0d008acd816a79cfd79d741e0756e9fe';
                $mailtrap = new MailtrapClient(new Config($apiKey));

                $email = (new Email())
                    ->from(new Address("<EMAIL>", "ITQAN"))
                    ->to(new Address($user_info[0]['empEmail'], $user_info[0]['empName']))
                    ->subject("Welcome aboard!")
                    ->text("Your Text");

                $htmlContent = view('modules.site.templates.wajeha.backEnd.humanResource.new_employee_welcome_message', ['data' => $user_info])->render();
                $email->html($htmlContent);

                $response = $mailtrap->sending()->emails()->send($email);
//                                $mj = new \Mailjet\Client(env('MAILJETAPIKEY'), env('MAILJETAPISECRET'), true, ['version' => 'v3.1']);
//                $body = [
//                    'Messages' => [
//                        [
//                            'From' => [
//                                'Email' => "<EMAIL>",
//                                'Name' => "ITQAN"
//                            ],
//                            'To' => [
//                                [
//                                    'Email' => $user_info[0]['empEmail'],
//                                    'Name' => $user_info[0]['empName']
//                                ]
//                            ],
//                            'Subject' => "Welcome aboard!",
//                            'TextPart' => "Your Text",
//                            'HTMLPart' => view('modules.site.templates.wajeha.backEnd.humanResource.new_employee_welcome_message', ['data'=> $user_info])->render()
//                        ]
//                    ]
//                ];
//                $response = $mj->post(Resources::$Email, ['body' => $body]);

                $employee->employee_creation_email = view('modules.site.templates.wajeha.backEnd.humanResource.new_employee_welcome_message', ['data'=> $user_info])->render();
                $employee->employee_creation_email_sent_at = Carbon::now();


//                $employee->notify(new WelcomeMailtoNewEmployeeNotification($user_info));


                // inform super admin/managing director and HR manager
                $users = Employee::whereHas('roles', function ($q) {
                    return $q->where('name', 'like', 'human-resource_%_')->orWhere('name', 'like', 'managing-director_%_');
                })->with(['roles' => function ($query) {
                    $query->select('id');
                }])->where('id', '!=', auth()->user()->id)
                    ->get(['id']);

//                $users=User::whereIn('role_id',[1,2])->where('id','!=',auth()->user()->id)
//                    ->where('is_active','1')
//                    ->get(['id','role_id']);

                $created_by = Auth::user()->name;
                $company = \DB::table('general_settings')->first()->organization_name;
                $content = __('notification.Staff Has been added by ') . $created_by . __('notification.for') . $company . ' ';
                $subject = __('notification.Staff Added');
                if ($employee and $employee->mobile) {
                    $number = $employee->mobile;
                    $message = __('notification.Congrats ! Staff Has been added by ') . $created_by . __('notification.for') . $company . ' ';
//                    $this->sendNotification($employee, $employee->email, $subject, $content, $number, $message, $users);
                }


            }

            $employee->save();
            DB::commit();
            \Toastr::success('Operation successful', 'Success');
            return redirect()->route('employees.index');
        } catch (\Exception $e) {
            \Log::error($e);

            dd($e->getMessage());

            DB::rollBack();

            Toastr::error('Operation Failed', $e->getMessage());
            return redirect()->back()->withInput()->withErrors(['error' => 'An error occurred while creating the employee: ' . $e->getMessage()]);
        }


    }

    function getEmployeesRemainingLeaves(Request $request)
    {

        if (Auth::user()->roles->contains('type', 'system_user')) {

            $userId = $request->q;

        } else {

            $userId = auth()->user()->id;


        }

        $results = [];
        $leaveTypes = \Modules\Leave\Entities\LeaveType::all()->map(function ($value, $key) use ($userId) {
            $totLeaveDaysperType = \DB::select('call getemployeetotleavesByLeaveType(?,?,?)', [$userId, 2022, $value->id]);

            $remainingDays = $totLeaveDaysperType[0]->totLeaveDays - approvedleavesByType($userId, $value->id);
            $results['name'] = $value->type . ' ( ' . $remainingDays . ' left)';
            $results['value'] = $value->id;
            $results['text'] = $value->type . ' ( ' . $remainingDays . ' left)';
            return $results;
        });
        return response()->json(["success" => true, 'results' => $leaveTypes], 200);

    }

    public
    function saveUploadDocument(Request $request)
    {

        try {
            if ($request->file('staff_upload_document') != "" && $request->title != "") {
                $document_photo = "";
                if ($request->file('staff_upload_document') != "") {
                    $file = $request->file('staff_upload_document');
//                    $document_photo = 'staff-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                    $document_photo = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                    $file->move('uploads/staff/document/', $document_photo);
                    $document_photo = 'public/uploads/staff/document/' . $document_photo;
                }

                $document = new Document();
                $document->title = $request->title;
                $document->documentable_id = $request->employee_id;
                $document->type = 'stf';
                $document->file = $document_photo;
                $document->created_by = Auth()->user()->id;
                $document->organization_id = config('organization_id');
                $results = $document->save();
            }

            if ($results) {
                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            \Log::error($e);
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public
    function show(Request $request, Employee $employee)
    {

        $employee = $employee->loadMissing(['teacherCenter', 'classes', 'bankAccounts.bank', 'bankAccounts.bankAccountType']);


        $workingDaysandHoursPerCurrMonth = DB::table('working_days')->where('year', Carbon::now()->year)->where('month_id', Carbon::now()->month)->get(['days', 'hours'])[0];

//        $workModePercentage = round((($employee->hours_per_month * 100) / $workingDaysandHoursPerCurrMonth->hours), 2);
        if ($workingDaysandHoursPerCurrMonth->hours != 0) {
            $workModePercentage = round((($employee->hours_per_month * 100) / $workingDaysandHoursPerCurrMonth->hours), 2);
        } else {
            $workModePercentage = 0;
            // Handle the case where hours is zero
        }


        $workMode = '';
        if ($workModePercentage >= 30 && $workModePercentage <= 70) {

            $workMode = 'Part Time';
        }
        if ($workModePercentage < 30) {

            $workMode = 'Casual Employees';
        }
        if ($workModePercentage > 70) {

            $workMode = 'Full Time';
        }
        // decision made on this date that : any new employeee after this date should have only mon-friday working
        if ($employee->created_at < Carbon::parse('2020-12-01')) {
            $newWorkTimeTable = 0;
        } else {
            $newWorkTimeTable = 1;
        }

        $todayAttendance = $this->attendanceModal->dailyAttendanceReport($employee);
        $weeklyAttendance = $this->attendanceModal->WeeklyAttendanceReport($employee);
        $monthlyAttendance = $this->attendanceModal->monthlyAttendanceReport($employee);

        $employeeDocumentsDetails = Document::where('documentable_id', $employee->id)->where('type', '=', 'stf')->get();
        $empAttMonths = DB::select("SELECT DISTINCT (MONTH(clock)) AS months FROM attendances WHERE employee_id = ? ORDER BY months ASC", [$id]);
        $employeeAttYears = DB::select("SELECT DISTINCT (year(clock)) AS years FROM attendances WHERE employee_id = ? ORDER BY years ASC", [$id]);
        $roles = Role::cursor()->pluck('description', 'name');
//        $classes = Classes::where('center_id', $employee->teacherCenter()->first()->id)->get()->pluck('name', 'id');
        $classes = Classes::all()->pluck('name', 'id');
        $bankAccTypes = BankAccountType::all()->pluck('name', 'id');


        $timetable = EmployeeTimetable::where('employee_id', $employee->id)->orderBy('day_order', 'asc')->pluck('day', 'id');
        $days = EmployeeTimetable::where('employee_id', $employee->id)->orderBy('day_order', 'asc')->get();
        $workDays = WeekDay::pluck('name', 'slug');


        $centers = CenterTranslation::cursor()->where('locale', 'en')->pluck('name', 'name');
        $centers = \App\Center::all()->pluck('location', 'id')->sortBy('location')->prepend('Select Sup Center...', "");


        $employeeContainsSupervisorRole = $employee->roles->contains(function ($item, $key) {
            return $item->name == 'supervisor_2_';

        });
        $employeeContainsTeacherRole = $employee->roles->contains(function ($item, $key) {
            return $item->name == 'teacher_' . config('organization_id') . '_';

        });

        $empDayCount = $employee->loadCount('timetable');

        $empDayCount = $empDayCount['timetable_count'];


        $permissions = Permission::all('name', 'id');

        if ($employee->hasRole('teacher_2_')) {

            $classes = Classes::whereDoesntHave('teachers', function ($query) use ($employee) {


                $query->where('employee_id', $employee);
            })->with('programs')->get()->pluck('name', 'id');


            $teacherClasses = Classes::whereHas('teachers', function ($query) use ($employee) {


                $query->where('employee_id', $employee->id);
            })->get()->pluck('name', 'id');


        }




        return view('humanresource::employees.show', compact('workMode', 'employeeContainsTeacherRole', 'bankAccTypes', 'classes', 'days', 'workDays', 'timetable', 'employeeContainsSupervisorRole', 'centers', 'teacherClasses', 'newWorkTimeTable', 'employee', 'roles', 'permissions', 'empAttMonths', 'employeeAttYears', 'todayAttendance', 'weeklyAttendance', 'monthlyAttendance', 'empDayCount'));
    }


    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        $employee = Employee::find($id);


        $roles = Role::pluck('description', 'name');

        $permissions = Permission::all('name', 'id');

        return view('humanresource::employees.edit', compact('employee', 'roles', 'permissions'));
    }

    public function update(EmployeeUpdateRequest $request, Employee $employee)
    {


        \Illuminate\Support\Facades\DB::beginTransaction();
        try {


            // if request has supervisor role, then sync the record in the cen_emps many to many table
            if (collect($request->get('roles'))->contains('supervisor_' . config('organization_id') . '_')) {
                $employee->center()->sync($request->get('supervisorCenters'));
            } else {
//                if an employe had supervisor role previously, remove the center attachment
                if (DB::table('cen_emps')->where('emp_id', $employee->id)->exists()) {
                    $centerIds = DB::table('cen_emps')->where('emp_id', $employee->id)->pluck('id');
                    $employee->center()->detach($centerIds);

                }
            }
            $existingTeacherClassesIds = $employee->classes()->pluck('classes.id');


            if (collect($request->get('roles'))->contains('teacher_' . config('organization_id') . '_')) {

                if (collect($request->get('teacherClasses'))->isNotEmpty()) {
                    // TODO: in the frontend add the class start date feature for each class
                    $startDate = [];
                    for ($i = 0; $i < count($request->get('teacherClasses')); $i++) {
                        $startDate[$i] = date('Y-m-d');
                    }


                    // detach all the relationships
                    $employee->classes()->detach($existingTeacherClassesIds);
                    foreach ($request->get('teacherClasses') as $key => $teacherClass) {
                        $employee->classes()->attach($teacherClass, ['start_date' => $startDate[$key]]);
                    }

                    // here we extract center IDs based on the classes selected
                    $centerIds = Center::whereHas('classes', function ($q) use ($request) {
                        $q->whereIn('id', $request->get('teacherClasses'));
                    })->get()->pluck('id');

                    // if teacher role is taken from an employee, remove the centers that are assigned to them.
//                    $employee->teacherCenter()->sync($centerIds);
                    $employee->teacherCenter()->sync($request->get('teacherCenter'));
                }
            } else {
                $centerIds = CenterTeacher::where('emp_id', $employee->id)->pluck('cen_id');
                $employee->classes()->detach($existingTeacherClassesIds);
                $employee->teacherCenter()->detach($centerIds);
            }
            // check for password change
            if ($request->get('password')) {
                $employee->password = bcrypt($request->get('password'));
            }

            if ($request->get('start_at')) {
                $employee->start_at = $request->start_at;
            }
            if ($request->get('image')) {
                $employee->image = $request->get('image');
            }
            $employee->employee_number = $request->get('employee_number');
            if ($request->get('mobile')) {
                $employee->mobile = $request->get('mobile');
            }
            if ($request->get('identity')) {
                $employee->identity_type = $request->get('identity');
            }
            $employee->identity_number = $request->get('identity_number');

            if ($request->get('address_1')) {
                $employee->address_1 = $request->get('address_1');
            }
            if ($request->get('address_2')) {
                $employee->address_2 = $request->get('address_2');
            }
            if ($request->get('address_country')) {
                $employee->address_country = $request->get('address_country');
            }
            if ($request->get('address_state')) {
                $employee->address_state = $request->get('address_state');
            }
            if ($request->get('address_city')) {
                $employee->address_city = $request->get('address_city');
            }
            $employee->gender = $request->get('gender');
            $employee->marital_status = $request->get('marital_status');
            $employee->full_name = $request->get('full_name');
            if ($request->get('full_name_trans')) {
                $employee->full_name_trans = $request->get('full_name_trans');
            }
            if ($request->get('name')) {
                $employee->name = $request->get('name');
            }
            $employee->email = $request->get('email');
            $employee->date_of_birth = $request->get('date_of_birth');
            if ($request->get('work_mood')) {
                $employee->work_mood = $request->get('work_mood');
            }
            if ($request->get('department_id')) {
                $employee->department_id = $request->get('department_id');
            }
            if ($request->get('date_of_joining')) {
                $employee->start_at = Carbon::parse($request->get('date_of_joining'))->format('Y-m-d');
            }
            if ($request->get('basic_salary')) {
                $employee->salary = $request->get('basic_salary');
            }
            if ($request->get('employment_type')) {
                $employee->contract_type = $request->get('employment_type');
            }
            if ($request->get('provisional_months')) {
                $employee->provisional_months = $request->get('provisional_months');
            }
            // if wrk_mood is task_based, then make hours_per_month null else populate with its value
            if ($request->get('work_mood') == \App\EmployeeWorkMood::where('slug', 'per_month')->first()->id) {
                if ($request->get('hours_per_month')) {
                    $employee->hours_per_month = $request->get('hours_per_month');
                }
                if ($request->has('days') && $request->filled('days')) {
                    $employee->syncTimeTable($request, $employee->id);
                }
            } else {
                $employee->hours_per_month = null;
            }
            $employee->nationality = $request->get('nationality');
            $employee->updated_at = Carbon::now();
            $employee->update();

//            foreach ($request->get('start') as $day => $time) {
//
//                $employee->timetable()->where('day', $day)->update([
//
//
//                    'clockin' => Carbon::parse($time)->toTimeString()
//                ]);
//
//
//            }
//            foreach ($request->get('end') as $day => $time) {
//
//                $employee->timetable()->where('day', $day)->update([
//
//
//                    'clockout' => Carbon::parse($time)->toTimeString()
//                ]);
//
//
//            }

            // Loop through the 'start' times received from the request
            foreach ($request->get('start') as $day => $time) {

                // For each 'start' time, update or create a timetable entry
                // If an entry for the given day already exists, it is updated.
                // If an entry for the given day does not exist, a new one is created.
                $employee->timetable()->updateOrCreate(
                    ['day' => $day], // conditions for update
                    ['clockin' => Carbon::parse($time)->toTimeString()] // values for update or create
                );
            }

// Loop through the 'end' times received from the request
            foreach ($request->get('end') as $day => $time) {

                // For each 'end' time, update or create a timetable entry
                // If an entry for the given day already exists, it is updated.
                // If an entry for the given day does not exist, a new one is created.
                $employee->timetable()->updateOrCreate(
                    ['day' => $day], // conditions for update
                    ['clockout' => Carbon::parse($time)->toTimeString()] // values for update or create
                );
            }


            // Handle the user roles
            $employee->syncRoles($request->roles);

            if ($request->has('bank_name') || $request->filled('bank_name')) {


                if ($request->has('account_id') || $request->filled('account_id')) {


                    // delete those bank accounts that are not available in the request array
                    $deleteBankAccount = BankAccount::whereNotIn('id', $request->get('account_id'))->where('emp_id', $employee->id)->delete();
                    // update bank account details
                    foreach ($request->get('account_id') as $key => $bankAccountId) {


                        $accName = $request->get('bank_account_name')[$key];
                        $accNumber = $request->get('bank_account_no')[$key];
                        $accType = $request->get('bank_account_type')[$key];
                        $accNote = $request->get('bank_notes')[$key];
                        $bankId = $request->get('bank_name')[$key];


                        BankAccount::updateOrCreate([
                            'id' => $bankAccountId
                        ], [
                            'account_name' => strtoupper($accName),
                            'account_number' => $accNumber,
                            'account_type_id' => $accType,
                            'bank_id' => $bankId,
                            'note' => $accNote,
                            'emp_id' => $employee->id,
                        ]);
                    }
                } else {


                    //if no bank details available , then create

                    foreach ($request->get('bank_name') as $key => $bankNameId) {

                        $accName = $request->get('bank_account_name')[$key];
                        $accNumber = $request->get('bank_account_no')[$key];
                        $accType = $request->get('bank_account_type')[$key];
                        $accNote = $request->get('bank_notes')[$key];
                        BankAccount::create([
                            'account_name' => strtoupper($accName),
                            'account_number' => $accNumber,
                            'account_type_id' => $accType,
                            'note' => $accNote,
                            'bank_id' => $bankNameId,
                            'emp_id' => $employee->id,

                        ]);

                    }

                }

            }
            $users = Employee::where('id', $employee->id)
                ->where('id', '!=', auth()->user()->id)
                ->with(['roles' => function ($query) {
                    $query->select('id');
                }])
                ->get(['id']);

            $created_by = \Illuminate\Support\Facades\Auth::user()->name;
            $company = GeneralSettings::find(1)->organization_name;
            $content = __('notification.Your info has been updated as a Staff by') . $created_by . __('notification.for') . $company . ' ';
            $number = $employee->mobile;
            $subject = __('notification.Staff Added');
            $message = __('notification.Your info Have Been updated by') . $created_by . __('notification.as a Staff for') . $company . ' ';
            $this->sendNotification($employee, $employee->email, $subject, $content, $number, $message, $users);

            \Illuminate\Support\Facades\DB::commit();
            LogActivity::successLog($request->username . '- profile has been updated.');
            Toastr::success(__('common.Staff info has been updated Successfully'));
            flash()->success(ucfirst($employee->name) . ' has been updated.');
            return redirect()->route('employees.index');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\DB::rollBack();
            dd($e->getMessage());
            LogActivity::errorLog($e->getMessage());
            Toastr::error(__('common.Something Went Wrong'));
            return back();
        }

    }

    public function destroy($id)
    {

        try {
            \Illuminate\Support\Facades\DB::beginTransaction();


            $employee = Employee::where("id", $id)->whereHas('roles', function ($q) {

                $q->where('name', 'teacher_' . config('organization_id') . '_');

            });


            if ($employee->exists()) {
                // proceed with detaching the record for the related classes and centers
                $centerIds = DB::table('cen_emps')->where('emp_id', $id)->pluck('id');
                $employee->first()->teacherCenter()->detach($centerIds);
                $existingTeacherClassesIds = $employee->first()->classes()->pluck('classes.id');
                $employee->first()->classes()->detach($existingTeacherClassesIds);


            }
            // permanantly delete an employee
            $employee->first()->forceDelete();


//        \Toastr::success('Employee Deleted', 'Title', ["positionClass" => "toast-top-center"]);
            \Illuminate\Support\Facades\DB::commit();

            return response()->json(["message" => 'Employee Deleted !!']);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\DB::rollback();
            return response()->json(['error' => $e->getMessage()], 201);

        }
    }

    public function getEmployeesJsonFormat(Request $request)
    {
        $requestedName = '%' . $request->q . '%';
        $name_condition = "name LIKE " . "'" . $requestedName . "'" . " OR display_name LIKE " . "'" . $requestedName . "'" . " OR full_name LIKE  " . "'" . $requestedName . "'" . " OR full_name_trans LIKE " . "'" . $requestedName . "'";


        $my_query = "select * from employees where " . $name_condition;


        $employee = \DB::select($my_query, array($request->q));
        $totalCounts = \DB::select($my_query, array($request->q));
        $totalCounts = count($totalCounts);

        $searchLang = is_arabic($request->q) == true ? "Arabic" : "English";


        return response()->json(["total_count" => $totalCounts, "incomplete_results" => false, "items" => $employee, 'language' => $searchLang], 200);

    }

    public function activate(Request $request, $employeeId = null)
    {

        try {
            if ($request->ajax()) {

                $validation = \Validator::make($request->all(), [
                    'id' => 'required',
                ]);
                if ($validation->passes()) {

                    $creator_role = 'employee';
                    $employeeId = $request->id;


                    $employee = Employee::withTrashed()->findOrfail($employeeId);

                    $employee->status = "active";
                    $employee->deleted_at = NULL;
                    $employee->save();


                    // send email to the user

//                    $employee->notify(new UserStatusChangedToNewApplication($employee, $request->program_id, $request->center_id, $request->class_id));

                    return response()->json("successfully activated the employee");


                }
            }


            return response()->json(['error' => $validation->errors()->all()], 422);


        } catch (\Exception $e) {

            dd($e->getMessage());
            return response()->json($e->getMessage(), 500);
        }
    }

    public function refreshDropdown(Request $request)
    {
        // 1. Retrieve filters from the request
        $roleIdsString = $request->input('role_ids');             // e.g., "3,4"
        $teacherCentersString = $request->input('teacher_centers'); // e.g., "5,6"
        $month = $request->input('month'); // Required
        $year = $request->input('year');   // Required

        // Validate that month and year are present
        if (empty($month) || empty($year)) {
            return response()->json([], 400); // Bad Request
        }

        // 2. Convert comma-separated strings to arrays
        $roleIds = $roleIdsString ? explode(',', $roleIdsString) : [];
        $teacherCenters = $teacherCentersString ? explode(',', $teacherCentersString) : [];

        // 3. Query employees based on filters and attendance
        $query = Employee::query();

        // Filter by roles if provided
        if (!empty($roleIds)) {
            $query->whereHas('roles', function ($q) use ($roleIds) {
                $q->whereIn('id', $roleIds);
            });
        }

        // Filter by teacher centers if provided
        if (!empty($teacherCenters)) {
            $query->whereHas('teacherCenter', function ($q) use ($teacherCenters) {
                $q->whereIn('cen_id', $teacherCenters);
            });
        }

        // Ensure employees have attendance records for the selected month and year
        $query->whereHas('attendance', function ($q) use ($month, $year) {
            $q->whereMonth('clock', $month)
                ->whereYear('clock', $year);
        });

        // 4. Retrieve employees with necessary data
        $employees = $query->select('id', 'full_name')->orderBy('full_name')->get();

        // 5. Return as JSON
        return response()->json($employees);
    }







    public function logoutAll($employeeId)
    {
        // Retrieve the employee record.
        $employee = Employee::findOrFail($employeeId);

        // Clear the remember_token so that the "remember me" cookie cannot refresh the session.
        $employee->forceFill([
            'remember_token' => null,
        ])->save();

        $sessionDriver = config('session.driver');  // e.g. "database", "file", "redis"

        switch ($sessionDriver) {
            case 'database':
                // For database sessions, delete all rows where 'user_id' matches.
                DB::table('sessions')->where('user_id', $employee->id)->delete();
                break;

            case 'file':
                // For file sessions, iterate through session files and delete those whose content references the employee ID.
                $files = Storage::files('framework/sessions');
                foreach ($files as $file) {
                    $content = Storage::get($file);
                    if (strpos($content, 'user_id";i:' . $employee->id . ';') !== false) {
                        Storage::delete($file);
                    }
                }
                break;

            case 'redis':
                // Connect to Redis using the session connection.
                $redisConnection = config('session.connection') ?? 'default';
                $redis = Redis::connection($redisConnection);

                // Get the session prefix from config or default to your observed prefix.
                $prefix = config('session.prefix', 'itqanalquran_database_laravel:');
                if (substr($prefix, -1) !== ':') {
                    $prefix .= ':';
                }

                // Retrieve all keys with the session prefix.
                $keys = $redis->keys($prefix . '*');

                foreach ($keys as $key) {
                    $payload = $redis->get($key);
                    if (!$payload) {
                        continue;
                    }
                    $sessionData = null;
                    try {
                        // Attempt to decrypt (if sessions are encrypted)
                        $decrypted = Crypt::decrypt($payload);
                        $sessionData = @unserialize($decrypted);
                    } catch (\Exception $e) {
                        // Fallback if decryption is not applicable.
                        $sessionData = @unserialize($payload);
                    }

                    if (!is_array($sessionData)) {
                        continue;
                    }

                    // Look for a key that starts with "login_employee_"
                    foreach ($sessionData as $subKey => $subValue) {
                        if (strpos($subKey, 'login_employee_') === 0) {
                            // If the value is not an array, compare directly.
                            if ((!is_array($subValue) && $subValue == $employee->id) ||
                                (is_array($subValue) && isset($subValue['id']) && $subValue['id'] == $employee->id)
                            ) {
                                $redis->del($key);
                                // Once found, no need to check further for this key.
                                break 2;
                            }
                        }
                    }
                }
                break;

            default:
                // For custom session drivers, implement your custom logic here.
                break;
        }

        return redirect()->back()->with('success', 'User has been logged out of all devices.');
    }












}

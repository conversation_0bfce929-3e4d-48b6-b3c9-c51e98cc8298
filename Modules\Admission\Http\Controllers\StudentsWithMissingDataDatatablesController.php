<?php

namespace Modules\Admission\Http\Controllers;


use App\Admission;
use App\Center;
use App\CenterEmployee;
use App\Classes;
use App\Form;
use App\MissedClockOut;
use App\Program;
use App\Role;
use App\Scopes\OrganizationScope;
use App\Student;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;

use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use PHPUnit\Exception;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Exports\StudentsWithMissingDataExport;
use Maatwebsite\Excel\Facades\Excel;


class StudentsWithMissingDataDatatablesController extends Controller
{

    /**
     * Check if student has an active admission with valid program/center/class data
     */
    private function hasActiveAdmission($student)
    {
        $admission = $student->admissions->first();
        return $admission &&
               $admission->program_id &&
               $admission->center_id &&
               $admission->class_id;
    }

    /**
     * Handles the request to display students with missing data in a datatable.
     *
     * @param Request $request The incoming request object.
     * @throws Exception If an error occurs while processing the request.
     * @return \Illuminate\Http\JsonResponse The datatable data in JSON format.
     */
    public function __invoke(Request $request)
    {



        // Base query for students - include both with and without admissions for filtering
        // Also load admission data to determine UI behavior
        $query = Student::with(['user', 'admissions' => function($q) {
            $q->whereNull('deleted_at')
              ->where('status', '!=', 'archived')
              ->with(['program', 'center', 'class']);
        }])->orderBy('full_name');

        // Check if any admission-related filters are applied
        $hasAdmissionFilters = $request->filled('program_filter') ||
                              $request->filled('center_filter') ||
                              $request->filled('class_filter');

        if ($hasAdmissionFilters) {
            // If admission filters are applied, include students with admissions
            $query->whereHas('admissions', function ($q) use ($request) {
                if ($request->filled('program_filter')) {
                    $q->where('program_id', $request->input('program_filter'));
                }
                if ($request->filled('center_filter')) {
                    $q->where('center_id', $request->input('center_filter'));
                }
                if ($request->filled('class_filter')) {
                    $q->where('class_id', $request->input('class_filter'));
                }
            });
        } else {
            // Default behavior: show students without admissions OR students without user accounts
            $query->where(function($q) {
                $q->doesntHave("admissions")
                  ->orWhereDoesntHave('user');
            });
        }

        // Apply the search filter if a search term is provided
        if ($request->has('search') && !empty($request->input('search.value'))) {
            $searchValue = $request->input('search.value');
            $query->where(function ($q) use ($searchValue) {
                $q->where('full_name', 'like', "%{$searchValue}%")
                    ->orWhere('display_name', 'like', "%{$searchValue}%")
                    ->orWhereHas('user', function ($q) use ($searchValue) {
                        $q->where('username', 'like', "%{$searchValue}%")
                            ->orWhere('email', 'like', "%{$searchValue}%");
                    })
                    ->orWhere('identity_number', 'like', "%{$searchValue}%")
                    ->orWhere('gender', 'like', "%{$searchValue}%")
                    ->orWhere('mobile', 'like', "%{$searchValue}%");
            });
        }

        // Fetch all programs for the dropdown
        $programs = Program::all();

        // Preload all gender data to avoid N+1 queries
        $genders = \App\BaseSetup::where('base_group_id', 1)
            ->where('active_status', 1)
            ->get()
            ->keyBy('id');

            return Datatables::of($query)
                ->addColumn('full_name', function ($student) {
                    // Check if we have any valid name from Student or User models
                    $studentFullName = (!empty($student['full_name'])) ? $student['full_name'] : null;
                    $studentDisplayName = (!empty($student['display_name'])) ? $student['display_name'] : null;
                    $userFullName = ($student->user && !empty($student->user->full_name)) ? $student->user->full_name : null;
                    $userDisplayName = ($student->user && !empty($student->user->display_name)) ? $student->user->display_name : null;

                    // Priority order: Student full_name > Student display_name > User full_name > User display_name
                    $fullName = $studentFullName ?: ($studentDisplayName ?: ($userFullName ?: $userDisplayName));

                    // If we have any valid name, show it
                    if ($fullName) {
                        // Check if student has a user account for clickable link
                        if ($student->user_id && $student->user) {
                            $profileUrl = route('students.show', ['id' => $student->user_id]);
                            return '<div class="name-cell">
                                        <a href="' . $profileUrl . '" target="_blank" style="color: #15CDC2; font-weight: bold; text-decoration: underline;">' . e($fullName) . '</a>
                                    </div>';
                        } else {
                            // Student has no user account - show name with helpful tooltip
                            return '<div class="name-cell">
                                        <span class="no-user-name"
                                              title="User account will be created automatically when you assign program/center/class or update student data"
                                              data-toggle="tooltip"
                                              data-placement="top">' . e($fullName) . '</span>
                                        <small class="no-user-warning">
                                            <i class="fa fa-exclamation-triangle"></i> No user account
                                        </small>
                                    </div>';
                        }
                    } else {
                        // No valid name found - show inline input for admin to enter name
                        // This will update both Student and User records (if user exists)
                        return '<div class="name-cell">
                                    <input type="text"
                                           class="form-control individual-name-input"
                                           data-student-id="' . $student->id . '"
                                           data-has-user="' . ($student->user ? 'true' : 'false') . '"
                                           style="font-size: 12px; height: 32px; width: 100%; max-width: 150px;"
                                           placeholder="Enter full name"
                                           maxlength="100">
                                    <div class="save-status" style="margin-top: 2px; font-size: 11px; height: 14px;"></div>
                                </div>';
                    }
                })

                ->addColumn('email', function ($student) {
                    return (!empty($student->email)) ? $student->email : 'N/A';
                })

                ->addColumn('gender', function ($student) use ($genders) {
                    if ($student->gender_id) {
                        $gender = $genders->get($student->gender_id);
                        return '<div class="gender-cell">
                                    <span class="badge badge-success" style="font-size: 12px;">
                                        <i class="fa fa-check" style="margin-right: 4px;"></i>' .
                                        ucfirst((!empty($gender->base_setup_name)) ? $gender->base_setup_name : 'Unknown') .
                                    '</span>
                                </div>';
                    } elseif ($student->gender) {
                        return '<div class="gender-cell">
                                    <span class="badge badge-success" style="font-size: 12px;">
                                        <i class="fa fa-check" style="margin-right: 4px;"></i>' .
                                        ucfirst($student->gender) .
                                    '</span>
                                </div>';
                    } else {
                        // Individual gender control for missing data - use preloaded genders
                        $options = '<option value="">Choose Gender</option>';
                        foreach ($genders as $gender) {
                            $options .= '<option value="' . $gender->id . '">' . ucfirst($gender->base_setup_name) . '</option>';
                        }

                        return '<div class="gender-cell">
                                    <select class="form-control individual-gender-select"
                                            data-student-id="' . $student->id . '"
                                            style="font-size: 12px; height: 32px; min-width: 100px;">
                                        ' . $options . '
                                    </select>
                                    <div class="save-status" style="margin-top: 2px; font-size: 11px; height: 14px;"></div>
                                </div>';
                    }
                })
                ->addColumn('date_of_birth', function ($student) {
                    if ($student->date_of_birth) {
                        try {
                            $age = \Carbon\Carbon::parse($student->date_of_birth)->age;
                            $ageText = ' (' . $age . ' years)';
                        } catch (\Exception $e) {
                            // If date parsing fails, just show the date without age
                            $ageText = '';
                        }

                        return '<div class="dob-cell">
                                    <span class="badge badge-success" style="font-size: 12px;">
                                        <i class="fa fa-check" style="margin-right: 4px;"></i>' .
                                        $student->date_of_birth . $ageText .
                                    '</span>
                                </div>';
                    } else {
                        // Individual date of birth control for missing data
                        $maxDate = date('Y-m-d', strtotime('-3 years'));
                        $minDate = date('Y-m-d', strtotime('-100 years'));

                        return '<div class="dob-cell">
                                    <input type="date"
                                           class="form-control individual-dob-input"
                                           data-student-id="' . $student->id . '"
                                           style="font-size: 12px; height: 32px; width: 100%; max-width: 120px;"
                                           max="' . $maxDate . '"
                                           min="' . $minDate . '"
                                           placeholder="Select DOB">
                                    <div class="save-status" style="margin-top: 2px; font-size: 11px; height: 14px;"></div>
                                </div>';
                    }
                })

                ->addColumn('program', function ($student) use ($programs) {
                    if ($this->hasActiveAdmission($student)) {
                        // Student has active admission - show existing program
                        $admission = $student->admissions->first();
                        $programName = $admission->program ? $admission->program->title : 'N/A';

                        return '<div class="existing-program">
                                    <strong>' . e($programName) . '</strong>
                                    <small class="text-muted"><br>From Admission</small>
                                </div>';
                    } else {
                        // Student has no active admission - show dropdown for assignment
                        return view('admission::student.partials.program_dropdown', compact('student', 'programs'))->render();
                    }
                })
                ->addColumn('center', function ($student) {
                    if ($this->hasActiveAdmission($student)) {
                        // Student has active admission - show existing center
                        $admission = $student->admissions->first();
                        $centerName = $admission->center ? $admission->center->name : 'N/A';

                        return '<div class="existing-center">
                                    <strong>' . e($centerName) . '</strong>
                                    <small class="text-muted"><br>From Admission</small>
                                </div>';
                    } else {
                        // Student has no active admission - show dropdown for assignment
                        return view('admission::student.partials.center_dropdown', compact('student'))->render();
                    }
                })
                ->addColumn('class', function ($student) {
                    if ($this->hasActiveAdmission($student)) {
                        // Student has active admission - show existing class
                        $admission = $student->admissions->first();
                        $className = $admission->class ? $admission->class->name : 'N/A';

                        return '<div class="existing-class">
                                    <strong>' . e($className) . '</strong>
                                    <small class="text-muted"><br>From Admission</small>
                                </div>';
                    } else {
                        // Student has no active admission - show dropdown for assignment
                        return view('admission::student.partials.class_dropdown', compact('student'))->render();
                    }
                })
                ->addColumn('username', function ($student) {
                    if ($student->user) {
                        // Student has user account - show username
                        return '<div class="existing-username">
                                    <strong>' . e($student->user->username) . '</strong>
                                    <small class="text-muted"><br>Active Account</small>
                                </div>';
                    } else {
                        // Student has no user account
                        if ($this->hasActiveAdmission($student)) {
                            // Student has active admission - show interactive create user button
                            return '<div class="no-user-with-admission">
                                        <span class="text-muted">No Account</span><br>
                                        <button class="btn btn-xs btn-success create-user-btn"
                                                data-student-id="' . $student->id . '"
                                                data-toggle="tooltip"
                                                title="Create user account for this student">
                                            <i class="fa fa-user-plus"></i> Create
                                        </button>
                                    </div>';
                        } else {
                            // Student has no admission - automatic creation will happen during assignment
                            return '<div class="no-user-no-admission">
                                        <span class="text-muted">No Account</span><br>
                                        <small class="text-info">Auto-created</small>
                                    </div>';
                        }
                    }
                })
                ->addColumn('created_at', function ($student) {
                    return $student ? $student->created_at->diffForHumans() : 'N/A';
                })
                ->addColumn('nationality', function ($student) {
                    // Check if student has nationality
                    if (!empty($student->nationality)) {
                        // Get country name from code
                        $country = \App\Country::where('code', $student->nationality)->first();
                        $countryName = $country ? $country->name : $student->nationality;

                        return '<div class="nationality-cell">
                                    <span class="badge badge-info" style="font-size: 12px;">
                                        <i class="fa fa-globe" style="margin-right: 4px;"></i>' .
                                        e($countryName) .
                                    '</span>
                                </div>';
                    } else {
                        // No nationality - show inline select for admin to choose
                        $countries = \App\Country::orderBy('name')->get();
                        $options = '<option value="">Choose Nationality</option>';
                        foreach ($countries as $country) {
                            $options .= '<option value="' . $country->code . '">' . e($country->name) . '</option>';
                        }

                        return '<div class="nationality-cell">
                                    <select class="form-control individual-nationality-select"
                                            data-student-id="' . $student->id . '"
                                            data-has-user="' . ($student->user ? 'true' : 'false') . '"
                                            style="font-size: 12px; height: 32px; min-width: 120px;">
                                        ' . $options . '
                                    </select>
                                    <div class="save-status" style="margin-top: 2px; font-size: 11px; height: 14px;"></div>
                                </div>';
                    }
                })
                ->addColumn('image', function ($student) {
                    // Check if student has an image
                    $hasImage = !empty($student->student_photo) && file_exists(public_path('storage/' . $student->student_photo));

                    if ($hasImage) {
                        // Show existing image thumbnail
                        return '<div class="image-cell">
                                    <img src="' . asset('storage/' . $student->student_photo) . '"
                                         class="img-thumbnail student-image-thumbnail"
                                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px; cursor: pointer;"
                                         title="Click to change image"
                                         data-student-id="' . $student->id . '">
                                    <div class="save-status" style="margin-top: 2px; font-size: 11px; height: 14px;"></div>
                                </div>';
                    } else {
                        // No image - show upload interface
                        return '<div class="image-cell">
                                    <div class="image-upload-container" style="text-align: center;">
                                        <input type="file"
                                               class="individual-image-upload"
                                               data-student-id="' . $student->id . '"
                                               accept="image/*"
                                               style="display: none;">
                                        <button type="button"
                                                class="btn btn-sm btn-outline-primary image-upload-btn"
                                                data-student-id="' . $student->id . '"
                                                style="font-size: 11px; padding: 4px 8px;">
                                            <i class="fa fa-camera"></i> Upload
                                        </button>
                                    </div>
                                    <div class="save-status" style="margin-top: 2px; font-size: 11px; height: 14px;"></div>
                                </div>';
                    }
                })

                ->setRowAttr([
//                    'data-program-id' => function($student) {
//                        return $student->program_id;
//                    },
//                    'data-center-id' => function($student) {
//                        return $student->center_id;
//                    },
                    'data-student-id' => function($student) {
                        return $student->id;
                    },
                    'data-identity-number' => function($student) {
                        return $student->identity_number;
                    },
                    'data-gender' => function($student) {
                        return (!empty($student['gender'])) ? $student['gender'] : $student->gender;
                    },

                    'data-dob' => function($student) {
                        return $student->date_of_birth;
                    },
                    'data-mobile' => function($student) {
                        return $student->mobile;
                    },
                    'data-has-gender' => function($student) {
                        return $student->gender_id || $student->gender ? 'true' : 'false';
                    },
                    'data-has-dob' => function($student) {
                        return $student->date_of_birth ? 'true' : 'false';
                    },

                    'data-guardian-id' => function($student) {
                        return $student->guardian_id;
                    },
                    'data-has-guardian' => function($student) {
                        return $student->guardian_id ? 'true' : 'false';
                    },
                    'data-has-user' => function($student) {
                        return $student->user ? 'true' : 'false';
                    },
                    'data-nationality' => function($student) {
                        return $student->nationality ?: '';
                    },
                    'data-has-nationality' => function($student) {
                        return !empty($student->nationality) ? 'true' : 'false';
                    },
                    'data-has-image' => function($student) {
                        return (!empty($student->student_photo) && file_exists(public_path('storage/' . $student->student_photo))) ? 'true' : 'false';
                    },
                ])

                ->rawColumns(['program', 'center', 'class','full_name', 'username', 'gender', 'date_of_birth', 'nationality', 'image'])  // Enable HTML rendering in these columns
//                ->make(true);
                ->toJson();
//        }


//        if ($request->ajax()) {
//            try {
//
//                $missingStudents = Student::doesntHave("admissions")->orderBy('full_name');
//                $classes = \App\Classes::all()->sortBy('name')->pluck('name', 'id')->prepend('Select class','');
//
//                return DataTables::eloquent($missingStudents->select())
//
//                    ->addColumn('full_name', function (MissedClockOut $row) {
//                        $genderColor = $row->gender == 'Male' || $row->gender == 'Male (ذكر)' || $row->gender == 'male' ? '#34b8bc;!important' : '#FA5661;!important';
//                        $genderBasedDefaultImage = $row->gender == 'Male' || $row->gender == 'Male (ذكر)' || $row->gender == 'male' ? asset('uploads/staff/demo/mstaff.jfif') : asset('uploads/staff/demo/fstaff.jpg');
//                        if (file_exists($row->image)) {
//
//                            $image = asset($row->image);
//                        } elseif (Str::contains($row->image, 'http')) {
//
//                            $image = $row->image;
//                        } else {
//                            $image = $genderBasedDefaultImage;
//
//
//                        }
//
//
//                        if (strlen($row->full_name) > 22) {
//                            $fullname = Str::limit(Str::title($row->full_name), 19, ' ...');
//                            return '<a target="_blank" href="' . route('students.show', $row->id) . '" data-missedClockOut_id="' . $row->id . '" data-employee_id="' . $row->id . '" data-tooltip="' . Str::title($row->full_name) . '"   style="color:' . $genderColor . '" class="ui image label">
//                          <img src="' . $image . '" "' . asset($row->image) . '">
//                          ' . ucwords(strtolower($fullname)) . '
//                        </a>';
//
//                        } else {
//                            $fullname = Str::title($row->full_name);
//
//                            return '<a  target="_blank" href="' . route('students.show', $row->id) . '" data-missedClockOut_id="' . $row->id . '" data-employee_id="' . $row->id . '" style="color:' . $genderColor . '" class="ui image label">
//                                          <img src="' . $image . '" "' . asset($row->image) . '">
//                                          ' . ucwords(strtolower($fullname)) . '
//                                        </a>';
//
//                        }
//
//
//                        return '<span  data-missedClockOut_id="' . $row->id . '" data-employee_id="' . $row->id . '">' . $row->full_name . '</span>';
////
//
//
//                    })
//                    ->addColumn('login', function ($row) use ($request) {
//
//
//                        $impersonationRoute = route('multiAuthImpersonate', ['id' => $row->id, 'guardName' => 'employee']);
//                        return '<span class="badge badge-primary badge-pill"><a
//                                style="color: white; padding: 0px; background: transparent "
//                                href="' . $impersonationRoute . '">login</a></span>';
//
//                    })
//
//                    ->addColumn('program', function ($row) {
//                        $programs = Program::where('status', 'active')->with('translations')->get();
//
//
//
//                    })
//                    ->addColumn('center', function ($row) {
//
//
//
//
//                    })
//                    ->addColumn('class', function ($row) {
//
//                    })->rawColumns(['program','center','class', 'full_name', 'login'])
//                    ->toJson();
//
//            } catch (Exception $exception) {
//
//
//                return response()->json($exception->getMessage());
//            }
//
//
//        }


    }





    /**
     * Export students with missing data to Excel
     */
    public function exportToExcel(Request $request)
    {
        $filename = 'students_with_missing_data_' . date('Y-m-d_H-i-s') . '.xlsx';

        return Excel::download(new StudentsWithMissingDataExport($request), $filename);
    }

}









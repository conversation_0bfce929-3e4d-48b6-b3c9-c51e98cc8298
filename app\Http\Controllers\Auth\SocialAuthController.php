<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class SocialAuthController extends Controller
{
    public function redirectToProvider($optionalParam = null)
    {



        // Check if the $optionalParam is present and equal to "login"
        if ($optionalParam === 'login') {
            session(['custom_param' => 'login']);

        }
            // Proceed with the normal Google OAuth driver without custom parameters
            return Socialite::driver('google')->redirect();

    }

    public function handleProviderCallback($optionalParam = null)
    {
        try {


            $user = Socialite::driver('google')->user();

            $existingUser = User::where('email', $user->email)->first();


            if ($existingUser) {

                // If the optionalParam is present and equals "login," log in the user
                if (session()->has('custom_param')) {
                    session()->forget('custom_param');
                    Auth::login($existingUser, true);
//                    return redirect('/dashboard'); // Redirect to the dashboard or wherever you want after login
                }
                // Redirect them to a registration page with a message
                return redirect('/register')->with('status', 'You are already registered with this email. Please use the standard login.');
//            Auth::login($existingUser, true);
            } else {

                //TODO: to prompt the student to enter their nationality after google return
//            $newUser = User::create([
//                'nationality' => $data['nationality'],
//
//            ]);

                // Begin a transaction to ensure that the user is saved fully without any errors.
                \DB::beginTransaction();

                $newUser = new User();
                $newUser->display_name = $user->name;
                $newUser->full_name = $user->name;
                $newUser->email_verified_at = Carbon::now();
                $newUser->is_administrator = 'no';
                $newUser->access_status = '0';
                $newUser->username = $this->generateUsername($user->name);  // Generated username
                $newUser->organization_id = config('organization_id');
                $newUser->email = $user->email;
                $newUser->google_id = $user->id;
                $newUser->password = bcrypt(uniqid());


                $newUser->save();
                // assign default role
                $this->assignDefaultRoles($newUser);
                try {

                    Auth::login($newUser, true);
                } catch (\Exception $e) {
                    \DB::rollBack();
                    \Log::error("Authentication error: " . $e->getMessage());


                    return redirect()->route('register')->withErrors([
                        'error' => 'An authentication error occurred. Please try again or contact our support <NAME_EMAIL>'
                    ]);
                }//            Auth::login($newUser, true);
                // Commit the transaction
                \DB::commit();
            }


            return redirect()->route('register');
        } catch (Exception $e) {
            \Log::error("Error during Google OAuth: " . $e->getMessage());

            return redirect()->route('register')->with('error', 'Something went wrong during Google OAuth');
        }

    }


    private function generateUsername($name)
    {
        $usernameBase = Str::slug($name, '_');
        $username = $usernameBase;
        $count = 1;

        // Find unique username avoiding N+1 problem
        $existingUsernames = User::where('username', 'like', "$usernameBase%")->pluck('username')->toArray();

        while (in_array($username, $existingUsernames)) {
            $username = $usernameBase . '_' . $count;
            $count++;
        }

        return strtolower($username);
    }

    public function assignDefaultRoles($user)
    {


//        $default_role_name = Settings::get('default_user_role', 'member');
//        $default_role_name = Settings::get('default_user_role', 'member');


//        $roleName = $default_role_name;


        $user->assignRole('member');
    }

}

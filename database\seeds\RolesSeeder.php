<?php

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $roles = ['admin', 'admin_basic' , 'admin_standerd' , 'admin_featured' , 'admin_professional'];

        foreach ($roles as $role ) {
            if(!Role::where('name' , '=', $role)){
                Role::create(['name' => $role , 'organization_id' => 0]);                
            }
        }

    }
}

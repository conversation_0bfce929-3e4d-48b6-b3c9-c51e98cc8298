<?php

use Monolog\Handler\StreamHandler;
use Monolog\Handler\SyslogUdpHandler;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

    'default' => env('LOG_CHANNEL', 'stack'),

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "monolog",
    |                    "custom", "stack"
    |
    */

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['single'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
            'days' => 14,
//            'permission' => 0664,  // New permissions for created log files
            'permission' => 0775,  // New permissions for created log files

        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji' => ':boom:',
            'level' => 'critical',
        ],
        'slackError' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_ERROR_WEBHOOK_URL'),
            'username' => 'admin',
            'emoji' => ':boom:',
            'level' => 'error',
        ],

        'papertrail' => [
            'driver' => 'monolog',
            'level' => 'debug',
            'handler' => SyslogUdpHandler::class,
            'handler_with' => [
                'host' => env('PAPERTRAIL_URL'),
                'port' => env('PAPERTRAIL_PORT'),
            ],
        ],

        'stderr' => [
            'driver' => 'monolog',
            'handler' => StreamHandler::class,
            'formatter' => env('LOG_STDERR_FORMATTER'),
            'with' => [
                'stream' => 'php://stderr',
            ],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => 'debug',
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level' => 'debug',
        ],
        'backup-restore' => [
        'driver' => 'daily',
        'path' => storage_path('logs/backup-restore.log'),
        'level' => env('LOG_LEVEL', 'debug'),
        'days' => 14,
    ],

    'activity' => [
        'driver' => 'daily',
        'path' => storage_path('logs/activity/activity.log'),
        'level' => env('LOG_LEVEL', 'info'),
        'days' => 30,
        'permission' => 0664,
    ],

    'email' => [
        'driver' => 'single',
        'path' => storage_path('logs/email.log'),
        'level' => 'debug',
    ],

    'command_failures' => [
        'driver' => 'single',
        'path' => storage_path('logs/command_failures.log'),
        'level' => env('LOG_LEVEL', 'debug'),
        'days' => 14,
    ],
    ],

];

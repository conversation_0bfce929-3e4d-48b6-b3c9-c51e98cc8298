<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\EmailService;
use App\Employee;
use Illuminate\Support\Facades\Log;
use App\IjazasanadMemorizationPlan;
use App\IjazasanadEmailSetting;
use App\IjazasanadEmailExclusion;
use App\IjazasanadEmailLog;

class SendPendingIjazasanadPlansReminderEmails extends Command
{
    protected $signature = 'email:send-pending-ijazasanad-plans-reminder';
    protected $description = 'Send reminder emails to employees with pending Ijazasanad plans awaiting approval.';

    /**
     * Execute the command.
     *
     * @return int Exit code
     */
    public function handle(): int
    {
        try {
            // Start logging - add a clear separator for easier log reading
            Log::channel('daily')->info('======================================');
            Log::channel('daily')->info('[IJAZASANAD COMMAND] Starting Ijazasanad email reminder process.');
            Log::info('[IJAZASANAD COMMAND] Starting Ijazasanad email reminder process.');

            // 1) Load the Ijazasanad email settings using the Eloquent model
            $settings = IjazasanadEmailSetting::first();

            if (!$settings) {
                Log::channel('daily')->warning('[IJAZASANAD COMMAND] No Ijazasanad email settings found in database; command execution skipped.');
                Log::warning('[IJAZASANAD COMMAND] No Ijazasanad email settings found in database; command execution skipped.');
                $this->warn('No Ijazasanad email settings found. Please configure them in the admin panel.');
                return Command::SUCCESS;
            }

            // Add detailed information to the logs for easier tracking
            Log::channel('daily')->info("[IJAZASANAD COMMAND] Loaded settings: frequency={$settings->frequency}, timezone={$settings->timezone}");
            Log::info("[IJAZASANAD COMMAND] Loaded settings: frequency={$settings->frequency}, timezone={$settings->timezone}");

            // 2) Decide if we should run today based on frequency and configured timezone
            $timezone = $settings->timezone ?? 'Asia/Kuala_Lumpur';
            $today = now()->timezone($timezone);
            $run = false;

            switch ($settings->frequency) {
                case 'daily':
                    // Format times for comparison
                    $configuredTime = $settings->daily_time;
                    $currentTime = $today->format('H:i');
                    
                    // Daily runs - we could add more specific time checking here
                    Log::info("[IJAZASANAD COMMAND] Daily schedule - configured time: {$configuredTime}, current time: {$currentTime}");
                    $run = true;
                    break;
                
                case 'weekly':
                    // Check if today is the designated day (Mon, Tue, etc.)
                    $configuredDay = $settings->weekly_day;
                    $currentDay = $today->format('D');
                    
                    Log::info("[IJAZASANAD COMMAND] Weekly schedule - configured day: {$configuredDay}, current day: {$currentDay}");
                    
                    if ($currentDay === $configuredDay) {
                        $run = true;
                        Log::info("[IJAZASANAD COMMAND] Weekly schedule matches today - will run");
                    } else {
                        Log::info("[IJAZASANAD COMMAND] Weekly schedule does not match today - skipping");
                    }
                    break;
                
                case 'monthly':
                    // Check if today's day-of-month matches the configured day
                    $configuredDay = $settings->monthly_day;
                    $currentDay = $today->format('j'); // Day of month without leading zeros
                    
                    Log::info("[IJAZASANAD COMMAND] Monthly schedule - configured day: {$configuredDay}, current day: {$currentDay}");
                    
                    if ((int)$currentDay === (int)$configuredDay) {
                        $run = true;
                        Log::info("[IJAZASANAD COMMAND] Monthly schedule matches today - will run");
                    } else {
                        Log::info("[IJAZASANAD COMMAND] Monthly schedule does not match today - skipping");
                    }
                    break;
                
                default:
                    Log::warning("[IJAZASANAD COMMAND] Invalid frequency setting: {$settings->frequency}");
                    $this->error("Invalid frequency setting: {$settings->frequency}");
                    return Command::FAILURE;
            }

            if (!$run) {
                Log::info("[IJAZASANAD COMMAND] Skipping Ijazasanad email reminder since frequency does not match today.");
                return Command::SUCCESS;
            }

            // 3) Fetch employees with the correct permission
            $permissionName = 'approve_ijazasanad_plan';
            $employees = Employee::permission($permissionName)->get();

            Log::info("[IJAZASANAD COMMAND] Found {$employees->count()} employees with permission: {$permissionName}");

            // 4) Get excluded emails using the Eloquent model
            $excludedEmails = IjazasanadEmailExclusion::pluck('email')->toArray();
            
            if (count($excludedEmails) > 0) {
                Log::info("[IJAZASANAD COMMAND] Excluding " . count($excludedEmails) . " email addresses");
            }

            if ($employees->isEmpty()) {
                Log::warning("[IJAZASANAD COMMAND] No employees found with required permission: {$permissionName}");
                return Command::SUCCESS;
            }

            // 5) Calculate pending plans
            $level2Count = IjazasanadMemorizationPlan::has('student')
                ->has('center')
                ->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();

            $level1Count = IjazasanadMemorizationPlan::has('student')
                ->has('center')
                ->where('status', 'waiting_for_approval')
                ->where(function ($query) {
                    $query->where(function ($subQuery) {
                        $subQuery->whereNotNull('talqeen_from_lesson')
                            ->whereNotNull('talqeen_to_lesson');
                    })
                        ->orWhere(function ($subQuery) {
                            $subQuery->whereNotNull('revision_from_lesson')
                                ->whereNotNull('revision_to_lesson');
                        })
                        ->orWhere(function ($subQuery) {
                            $subQuery->whereNotNull('jazariyah_from_lesson')
                                ->whereNotNull('jazariyah_to_lesson');
                        })
                        ->orWhere(function ($subQuery) {
                            $subQuery->whereNotNull('seminars_from_lesson')
                                ->whereNotNull('seminars_to_lesson');
                        });
                })
                ->count();

            $totalPending = $level1Count + $level2Count;
            
            Log::info("[IJAZASANAD COMMAND] Found {$totalPending} total pending plans (Level 1: {$level1Count}, Level 2: {$level2Count})");

            if ($totalPending === 0) {
                Log::info('[IJAZASANAD COMMAND] No pending plans awaiting approval; no emails will be sent.');
                return Command::SUCCESS;
            }

            // 6) Send reminders to qualified employees
            $emailService = app(\App\Services\EmailService::class);
            $sentCount = 0;
            $failedCount = 0;
            
            foreach ($employees as $employee) {
                // Skip excluded employees
                if (in_array($employee->email, $excludedEmails)) {
                    Log::info("[IJAZASANAD COMMAND] Skipping excluded email: {$employee->email}");
                    continue;
                }
                
                $to = [
                    'email' => $employee->email,
                    'name'  => $employee->name,
                ];
                
                $subject = 'Ijazasanad Pending Plans Awaiting Your Approval';
                $view = 'emails.pending_plans_reminder';
                $viewData = [
                    'employee'    => $employee,
                    'level1Count' => $level1Count,
                    'level2Count' => $level2Count,
                ];

                try {
                    $emailService->sendEmail($to, $subject, $view, $viewData);
                    
                    // Log the successful email
                    IjazasanadEmailLog::logSuccess(
                        $employee->id,
                        $employee->email,
                        $subject
                    );
                    
                    Log::info("[IJAZASANAD COMMAND] Email sent successfully to: {$employee->email}");
                    $sentCount++;
                    
                } catch (\Exception $e) {
                    // Log the failed email
                    IjazasanadEmailLog::logFailure(
                        $employee->id,
                        $employee->email,
                        $subject,
                        $e->getMessage()
                    );
                    
                    Log::error("[IJAZASANAD COMMAND] Failed to send email to {$employee->email}: " . $e->getMessage());
                    $failedCount++;
                }
            }

            // At the end, add a summary log entry
            Log::channel('daily')->info("[IJAZASANAD COMMAND] Command completed. Sent: {$sentCount}, Failed: {$failedCount}");
            Log::info("[IJAZASANAD COMMAND] Command completed. Sent: {$sentCount}, Failed: {$failedCount}");
            
            if ($sentCount > 0) {
                $this->info("Successfully sent {$sentCount} reminder emails.");
            }
            
            if ($failedCount > 0) {
                $this->warn("{$failedCount} emails failed to send. Check the logs for details.");
            }

            Log::channel('daily')->info('======================================');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            // Add comprehensive error logging
            Log::channel('daily')->error("[IJAZASANAD COMMAND] Command failed with exception: " . $e->getMessage());
            Log::channel('daily')->error("[IJAZASANAD COMMAND] Exception trace: " . $e->getTraceAsString());
            Log::error("[IJAZASANAD COMMAND] Command failed with exception: " . $e->getMessage());
            
            $this->error("Command failed: " . $e->getMessage());
            return Command::FAILURE;
        }
    }
}

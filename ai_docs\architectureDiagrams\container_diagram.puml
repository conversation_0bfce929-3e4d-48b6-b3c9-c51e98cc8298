@startuml Container Diagram (Level 2 - Itqan)

!theme vibrant

title Container Diagram for Itqan ERP System

' Include C4 specific macros/styling
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

' Actors (from Level 1)
Person(admin, "Administrator", "Manages system settings, users, modules")
Person(staff, "Staff/Teacher", "Manages courses, students, attendance, grades")
Person(student, "Student/Parent", "Accesses profile, grades, fees, communication")

' External Systems (from Level 1)
System_Ext(email, "Email Service", "e.g., Mailgun, SES")
System_Ext(payment, "Payment Gateway", "e.g., Stripe, PayPal")
System_Ext(sms, "SMS Gateway", "e.g., Twilio")

' System Boundary (Level 1 System)
System_Boundary(itqan_system, "Itqan ERP System") {

    ' Containers within the Itqan System
    Container(webapp, "Web Application", "PHP, Laravel 10", "The main monolithic Laravel application serving the UI and API.")
    ContainerDb(db, "Database", "MySQL", "Stores all application data: users, students, courses, finance, HR, etc.")
    Container(cache, "Cache", "Redis/Memcached", "Stores sessions, cached queries, etc., for performance.")
    Container(queue_worker, "Queue Worker", "PHP, Laravel Queue", "Processes background jobs asynchronously (e.g., sending emails, generating reports).", $sprite="gear")
    Container(storage, "File Storage", "Local Filesystem / Cloud Storage", "Stores uploaded documents, images, backups.")

    ' Internal Container Relationships
    Rel(webapp, db, "Reads/Writes", "SQL")
    Rel(webapp, cache, "Reads/Writes", "TCP")
    Rel(webapp, queue_worker, "Dispatches Jobs To", "Redis/DB")
    Rel(webapp, storage, "Reads/Writes Files", "File I/O / API")

    Rel(queue_worker, db, "Reads/Writes", "SQL")
    Rel(queue_worker, cache, "Reads/Writes", "TCP")
    Rel(queue_worker, storage, "Reads/Writes Files", "File I/O / API")

}

' Relationships to External Systems/Actors
Rel(admin, webapp, "Uses", "HTTPS")
Rel(staff, webapp, "Uses", "HTTPS")
Rel(student, webapp, "Uses", "HTTPS")

Rel(webapp, email, "Sends Emails Via", "API/SMTP")
Rel(webapp, sms, "Sends SMS Via", "API")
Rel(webapp, payment, "Processes Payments Via", "HTTPS/API")
Rel_Back(webapp, payment, "Receives Payment Status", "Webhook/HTTPS")

' Queue worker might also interact with external systems for jobs
Rel(queue_worker, email, "Sends Emails Via", "API/SMTP")
Rel(queue_worker, sms, "Sends SMS Via", "API")

@enduml 
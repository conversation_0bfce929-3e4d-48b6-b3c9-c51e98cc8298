<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class BackupRestoreLog extends Model
{
    protected $fillable = [
        'backup_id',
        'user_id',
        'success',
        'message',
        'details',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'success' => 'boolean',
        'details' => 'array'
    ];

    public function backup()
    {
        return $this->belongsTo(CenterClassStudentsBackup::class, 'backup_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
} 
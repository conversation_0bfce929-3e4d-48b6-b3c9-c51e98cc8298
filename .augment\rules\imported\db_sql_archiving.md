---
type: "always_apply"
---

- **Archive every DB interaction**
  - When you execute SQL (via tools or manual), save a readable copy of the exact MySQL statements.
  - Store inside the relevant module’s documentation directory under `Modules/<ModuleName>/docs/sql/`.
  - Use timestamped filenames: `YYYYMMDD_HHMMSS_<short-purpose>.sql`.
  - Add a short header comment (what/why, related route/command).

- **Separation of purposes**
  - This rule is for documentation/traceability only (archival of actual statements used in dev/test).
  - It does not replace existing schema change rules. Keep following:
    - Schema changes live in module-specific DB dirs (e.g., `Modules/<ModuleName>/Database/`).
    - Project convention to ship schema changes as direct SQL (no migrations) still applies.

- **Content expectations**
  - Include the full statement set you ran (SELECT/INSERT/UPDATE/DELETE), ordered by execution.
  - Prefer idempotent, comment-rich SQL. For destructive statements, add protective WHERE clauses and comments.
  - Example header:

```sql
-- Context: Jobs.af + ACBAR end-to-end notification test
-- Command(s): php artisan jobseeker:sync-jobs-af; php artisan jobseeker:sync-acbar-jobs --category=5
-- Purpose: Verify per-category aggregation and email delivery, capture counts before/after
```

- **Examples (JobSeeker module)**
  - Place under: `[JobSeeker tests SQL](mdc:Modules/JobSeeker/docs/sql/)`
  - Example filename: `20250808_162500_jobs_sync_aggregation_verification.sql`
  - Example snippet:

```sql
-- 1) Verify active setups and target email(s)
SELECT s.id AS setup_id, js.email, js.name
FROM job_notification_setups s
JOIN job_seekers js ON js.id = s.job_seeker_id
WHERE s.is_active = 1 AND js.email IS NOT NULL
ORDER BY s.id DESC
LIMIT 5;

-- 2) Check notification sent counts (before/after sync)
SELECT COUNT(*) AS cnt
FROM job_notification_sent_jobs
WHERE recipient_email='<EMAIL>';
```

- **Cross-references**
  - See general rules: `[cursor_rules.mdc](mdc:.cursor/rules/cursor_rules.mdc)`
  - Module: `[JobSeeker](mdc:Modules/JobSeeker)`

- **Quality bar**
  - Keep files small, focused, and discoverable (one scenario per file).
  - Name files so a future maintainer can guess the scenario/purpose quickly.
  - When sensitive values exist (emails, IDs), keep them but prefer realistic test identities.


<?php

return [

    /*
    |--------------------------------------------------------------------------
    | JobSeeker Rate Limiting Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains all the rate limiting configuration for the JobSeeker
    | module including authentication throttling, general API throttling, and
    | search query throttling settings.
    |
    */

    'authentication' => [
        'login' => [
            'max_attempts' => env('JOBSEEKER_LOGIN_MAX_ATTEMPTS', 5),
            'decay_minutes' => env('JOBSEEKER_LOGIN_DECAY_MINUTES', 10),
        ],
        'registration' => [
            'max_attempts' => env('JOBSEEKER_REGISTRATION_MAX_ATTEMPTS', 3),
            'decay_minutes' => env('JOBSEEKER_REGISTRATION_DECAY_MINUTES', 30),
        ],
        'social_login' => [
            'max_attempts' => env('JOBSEEKER_SOCIAL_LOGIN_MAX_ATTEMPTS', 5),
            'decay_minutes' => env('JOBSEEKER_SOCIAL_LOGIN_DECAY_MINUTES', 10),
        ],
        'password_reset' => [
            'max_attempts' => env('JOBSEEKER_PASSWORD_RESET_MAX_ATTEMPTS', 3),
            'decay_minutes' => env('JOBSEEKER_PASSWORD_RESET_DECAY_MINUTES', 15),
        ],
        'exponential_backoff' => [
            'enabled' => env('JOBSEEKER_AUTH_EXPONENTIAL_BACKOFF', true),
            'max_minutes' => env('JOBSEEKER_AUTH_MAX_BACKOFF_MINUTES', 1440), // 24 hours
        ],
    ],

    'general' => [
        'authenticated' => [
            'max_attempts' => env('JOBSEEKER_AUTHENTICATED_MAX_ATTEMPTS', 120),
            'decay_minutes' => env('JOBSEEKER_AUTHENTICATED_DECAY_MINUTES', 60),
        ],
        'unauthenticated' => [
            'max_attempts' => env('JOBSEEKER_UNAUTHENTICATED_MAX_ATTEMPTS', 60),
            'decay_minutes' => env('JOBSEEKER_UNAUTHENTICATED_DECAY_MINUTES', 60),
        ],
        'api' => [
            'authenticated' => [
                'max_attempts' => env('JOBSEEKER_API_AUTHENTICATED_MAX_ATTEMPTS', 200),
                'decay_minutes' => env('JOBSEEKER_API_AUTHENTICATED_DECAY_MINUTES', 60),
            ],
            'unauthenticated' => [
                'max_attempts' => env('JOBSEEKER_API_UNAUTHENTICATED_MAX_ATTEMPTS', 100),
                'decay_minutes' => env('JOBSEEKER_API_UNAUTHENTICATED_DECAY_MINUTES', 60),
            ],
        ],
        'exponential_backoff' => [
            'enabled' => env('JOBSEEKER_GENERAL_EXPONENTIAL_BACKOFF', true),
            'max_minutes' => env('JOBSEEKER_GENERAL_MAX_BACKOFF_MINUTES', 240), // 4 hours
        ],
    ],

    'search' => [
        'authenticated' => [
            'max_attempts' => env('JOBSEEKER_SEARCH_AUTHENTICATED_MAX_ATTEMPTS', 100),
            'decay_minutes' => env('JOBSEEKER_SEARCH_AUTHENTICATED_DECAY_MINUTES', 60),
        ],
        'unauthenticated' => [
            'max_attempts' => env('JOBSEEKER_SEARCH_UNAUTHENTICATED_MAX_ATTEMPTS', 30),
            'decay_minutes' => env('JOBSEEKER_SEARCH_UNAUTHENTICATED_DECAY_MINUTES', 60),
        ],
        'identical_query' => [
            'max_attempts' => env('JOBSEEKER_SEARCH_IDENTICAL_MAX_ATTEMPTS', 5),
            'decay_minutes' => env('JOBSEEKER_SEARCH_IDENTICAL_DECAY_MINUTES', 15),
        ],
        'cache' => [
            'enabled' => env('JOBSEEKER_SEARCH_CACHE_ENABLED', true),
            'duration_minutes' => env('JOBSEEKER_SEARCH_CACHE_DURATION_MINUTES', 5),
            'min_query_length' => env('JOBSEEKER_SEARCH_MIN_QUERY_LENGTH', 3),
        ],
        'pattern_detection' => [
            'enabled' => env('JOBSEEKER_SEARCH_PATTERN_DETECTION_ENABLED', true),
            'thresholds' => [
                'rapid_searches' => env('JOBSEEKER_SEARCH_RAPID_THRESHOLD', 10),
                'identical_queries' => env('JOBSEEKER_SEARCH_IDENTICAL_THRESHOLD', 3),
                'empty_queries' => env('JOBSEEKER_SEARCH_EMPTY_THRESHOLD', 5),
                'suspicious_terms' => env('JOBSEEKER_SEARCH_SUSPICIOUS_THRESHOLD', 2),
            ],
        ],
        'suspicious_patterns' => [
            'sql_injection' => ['union', 'select', 'insert', 'delete', 'drop', 'alter', 'update', 'create'],
            'xss_attempts' => ['<script>', 'javascript:', 'onload=', 'onerror=', 'onclick=', 'onmouseover='],
            'excessive_wildcards' => ['%', '*', '?'],
            'automated_patterns' => ['test', 'bot', 'crawler', 'spider', 'scraper', 'automated'],
        ],
    ],

    'logging' => [
        'enabled' => env('JOBSEEKER_RATE_LIMITING_LOGGING', true),
        'level' => env('JOBSEEKER_RATE_LIMITING_LOG_LEVEL', 'warning'),
        'sampling_rate' => [
            'successful_requests' => env('JOBSEEKER_LOG_SUCCESS_SAMPLING', 10), // Log every 10th request
            'search_requests' => env('JOBSEEKER_LOG_SEARCH_SAMPLING', 5), // Log every 5th search
        ],
    ],

    'monitoring' => [
        'enabled' => env('JOBSEEKER_RATE_LIMITING_MONITORING', true),
        'alert_thresholds' => [
            'high_rate_limit_hits' => env('JOBSEEKER_ALERT_RATE_LIMIT_THRESHOLD', 10),
            'suspicious_patterns' => env('JOBSEEKER_ALERT_SUSPICIOUS_THRESHOLD', 5),
            'backoff_activations' => env('JOBSEEKER_ALERT_BACKOFF_THRESHOLD', 3),
        ],
    ],

]; 
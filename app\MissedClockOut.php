<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;


class MissedClockOut extends Model
{

    use SoftDeletes;
    protected $casts = [
        'clock' => 'datetime'
    ];

    protected $table = 'missed_clockouts';

    protected $guarded = [ 'updated_at'];






    public function employee(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {

        return $this->belongsTo(Employee::class)->whereNULL('deleted_at');
    }
    public function attendance(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {

        return $this->belongsTo(Attendance::class)->withTrashed();
    }

}

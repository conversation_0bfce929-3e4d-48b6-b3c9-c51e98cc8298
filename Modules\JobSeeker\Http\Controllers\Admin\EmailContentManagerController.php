<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Modules\JobSeeker\Entities\EmailContentSetting;
use Modules\JobSeeker\Services\EmailContentManagerService;
use App\Services\EmailService;
use App\Http\Controllers\Controller;

/**
 * EmailContentManagerController
 * 
 * Founder-level admin interface for managing email content settings
 * Controls which job fields appear in notification emails and how they're formatted
 */
final class EmailContentManagerController extends Controller
{
    private EmailContentManagerService $contentManager;
    
    public function __construct(EmailContentManagerService $contentManager)
    {
        $this->contentManager = $contentManager;
        $this->middleware('auth:job_seeker');
        // Note: Add founder-only permission middleware when implemented
        // $this->middleware('permission:manage_email_content');
    }
    
    /**
     * Show email content management interface
     */
    public function index()
    {
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('EmailContentManager: Accessing email content management interface', [
            'user_email' => $user->email ?? 'unknown'
        ]);
        
        try {
            // Guard against missing schema to avoid redirect loops
            $schemaOk = Schema::hasTable('jobseeker_email_content_settings');
            $settings = collect();
            if ($schemaOk) {
                $settings = EmailContentSetting::orderBy('field_group')
                    ->orderBy('display_order')
                    ->get();
            }
            
            $fieldGroups = $schemaOk ? $settings->groupBy('field_group') : collect();
            
            return view('modules.jobseeker.admin.email-content-manager.index', [
                'settings' => $settings,
                'fieldGroups' => $fieldGroups,
                'availableFields' => $this->contentManager->getAvailableFields(),
                'presets' => $this->contentManager->getPresets(),
                'groupIcons' => $this->getGroupIcons(),
                'schemaMissing' => !$schemaOk
            ]);
            
        } catch (\Exception $e) {
            Log::error('EmailContentManager: Error loading interface', [
                'error' => $e->getMessage(),
                'user_email' => $user->email ?? 'unknown'
            ]);

            // Render a safe fallback view instead of redirecting
            return view('modules.jobseeker.admin.email-content-manager.index', [
                'settings' => collect(),
                'fieldGroups' => collect(),
                'availableFields' => $this->contentManager->getAvailableFields(),
                'presets' => $this->contentManager->getPresets(),
                'groupIcons' => $this->getGroupIcons(),
                'schemaMissing' => true,
                'fatalError' => $e->getMessage(),
            ]);
        }
    }
    
    /**
     * Update field settings
     */
    public function updateField(Request $request, int $id): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        $validator = Validator::make($request->all(), [
            'is_enabled' => ['required', Rule::in([true, false, 1, 0, '1', '0', 'true', 'false'])],
            'display_label' => 'required|string|max:255',
            'display_order' => 'required|integer|min:0',
            'field_group' => ['required', Rule::in(['basic','company','details','requirements','application'])],
            'conditional_display' => ['nullable', Rule::in([true, false, 1, 0, '1', '0', 'true', 'false'])],
            'formatting_options' => 'nullable|json'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $setting = EmailContentSetting::findOrFail($id);
            
            // Validate JSON formatting options
            $formattingOptions = $request->input('formatting_options');
            if ($formattingOptions && !empty($formattingOptions)) {
                json_decode($formattingOptions);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid JSON in formatting options'
                    ], 422);
                }
            }
            
            $setting->update([
                'is_enabled' => filter_var($request->input('is_enabled'), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? (int)$request->input('is_enabled') === 1,
                'display_label' => $request->input('display_label'),
                'display_order' => $request->input('display_order'),
                'field_group' => $request->input('field_group'),
                'conditional_display' => filter_var($request->input('conditional_display'), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? (int)$request->input('conditional_display') === 1,
                'formatting_options' => $formattingOptions ? json_decode($formattingOptions, true) : null,
                'updated_by' => $user->email ?? 'admin',
            ]);
            
            // Clear cache
            $this->contentManager->clearCache();
            
            Log::info('EmailContentManager: Field updated successfully', [
                'field_id' => $id,
                'field_name' => $setting->field_name,
                'updated_by' => $user->email ?? 'admin'
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Field updated successfully',
                'setting' => $setting->fresh()
            ])->header('Cache-Control','no-store, no-cache, must-revalidate, max-age=0');
            
        } catch (\Exception $e) {
            Log::error('EmailContentManager: Error updating field', [
                'field_id' => $id,
                'error' => $e->getMessage(),
                'user_email' => $user->email ?? 'unknown'
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to update field'
            ], 500);
        }
    }
    
    /**
     * Bulk update field order
     */
    public function updateOrder(Request $request): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        $validator = Validator::make($request->all(), [
            'fields' => 'required|array',
            'fields.*.id' => 'required|integer|exists:jobseeker_email_content_settings,id',
            'fields.*.display_order' => 'required|integer|min:0'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            DB::transaction(function () use ($request, $user) {
                foreach ($request->input('fields') as $fieldData) {
                    EmailContentSetting::where('id', $fieldData['id'])
                        ->update([
                            'display_order' => $fieldData['display_order'],
                            'updated_by' => $user->email ?? 'admin',
                        ]);
                }
            });
            
            $this->contentManager->clearCache();
            
            Log::info('EmailContentManager: Field order updated successfully', [
                'fields_count' => count($request->input('fields')),
                'updated_by' => $user->email ?? 'admin'
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Field order updated successfully'
            ])->header('Cache-Control','no-store, no-cache, must-revalidate, max-age=0');
            
        } catch (\Exception $e) {
            Log::error('EmailContentManager: Error updating field order', [
                'error' => $e->getMessage(),
                'user_email' => $user->email ?? 'unknown'
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to update field order'
            ], 500);
        }
    }
    
    /**
     * Apply preset configuration
     */
    public function applyPreset(Request $request): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        $validator = Validator::make($request->all(), [
            'preset' => 'required|string|in:minimal,standard,detailed'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid preset selection'
            ], 422);
        }
        
        try {
            $presetName = $request->input('preset');
            $success = $this->contentManager->applyPreset($presetName, $user->email ?? 'admin');
            
            if ($success) {
            return response()->json([
                'success' => true,
                'message' => ucfirst($presetName) . ' preset applied successfully'
            ])->header('Cache-Control','no-store, no-cache, must-revalidate, max-age=0');
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to apply preset'
                ], 500);
            }
            
        } catch (\Exception $e) {
            Log::error('EmailContentManager: Error applying preset', [
                'preset' => $request->input('preset'),
                'error' => $e->getMessage(),
                'user_email' => $user->email ?? 'unknown'
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to apply preset'
            ], 500);
        }
    }
    
    /**
     * Generate email preview with comprehensive error detection and route validation
     * 
     * Purpose: Render email preview with validation to catch route failures, template 
     * errors, and view regressions before scheduling runs.
     * Side effects: Logs preview errors with context for debugging; validates critical routes.
     * Errors: Returns detailed error info for debugging; gracefully handles all failures.
     */
    public function preview(Request $request): JsonResponse
    {
        $validationErrors = [];
        $warnings = [];
        
        try {
            // Get sample job data with enhanced validation - multiple jobs like real notifications
            $sampleJobs = $this->contentManager->getSampleJobData();
            
            // inject virtual setup_name for headline if enabled for each job
            if ($this->contentManager->isFieldEnabled('setup_name')) {
                foreach ($sampleJobs as &$job) {
                    $job['setup_name'] = 'Preview Setup';
                }
                unset($job); // Clear reference
            }
            
            // Ensure all jobs have valid slugs for AI tailor route testing
            foreach ($sampleJobs as &$job) {
                if (empty($job['slug'])) {
                    $job['slug'] = 'sample-job-slug-' . $job['id'];
                }
            }
            unset($job); // Clear reference
            
            $sampleJobSeeker = (object) [
                'name' => 'John Doe',
                'email' => '<EMAIL>'
            ];
            $sampleSetup = (object) [
                'name' => 'Software Development Jobs',
                'id' => 1
            ];
            
            // Pre-validate critical routes before rendering (test with first job's slug)
            $firstJob = $sampleJobs[0] ?? null;
            if ($firstJob) {
                try {
                    $aiTailorUrl = route('ai.tailor.show', $firstJob['slug']);
                    Log::info('EmailContentManager: AI tailor route validation successful', ['url' => $aiTailorUrl]);
                } catch (\Exception $e) {
                    $validationErrors[] = "AI Tailor Route Error: " . $e->getMessage();
                    Log::warning('EmailContentManager: AI tailor route validation failed', [
                        'error' => $e->getMessage(),
                        'slug' => $firstJob['slug']
                    ]);
                }
            }
            
            // Validate other critical routes used in email
            $routesToValidate = [
                'jobseeker.unsubscribe.public' => 'Unsubscribe route',
                'jobseeker.dashboard' => 'Dashboard route'
            ];
            
            foreach ($routesToValidate as $routeName => $description) {
                try {
                    $url = route($routeName);
                    Log::debug("EmailContentManager: Route validation successful for {$description}", ['url' => $url]);
                } catch (\Exception $e) {
                    $warnings[] = "{$description} Error: " . $e->getMessage();
                    Log::warning("EmailContentManager: Route validation failed for {$description}", [
                        'route' => $routeName,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            // Render email template with error capture - using multiple jobs
            $renderStartTime = microtime(true);
            $html = view('modules.jobseeker.emails.jobs.jobseeker_notification_new', [
                'jobs' => $sampleJobs, // Multiple jobs like real notifications
                'jobSeeker' => $sampleJobSeeker,
                'setup' => $sampleSetup,
            ])->render();
            $renderTime = round((microtime(true) - $renderStartTime) * 1000, 2);
            
            // Validate rendered HTML for common issues
            $htmlValidation = $this->validateRenderedHtml($html);
            if (!empty($htmlValidation['errors'])) {
                $validationErrors = array_merge($validationErrors, $htmlValidation['errors']);
            }
            if (!empty($htmlValidation['warnings'])) {
                $warnings = array_merge($warnings, $htmlValidation['warnings']);
            }
            
            Log::info('EmailContentManager: Preview rendered successfully', [
                'render_time_ms' => $renderTime,
                'html_size_bytes' => strlen($html),
                'validation_errors' => count($validationErrors),
                'warnings' => count($warnings)
            ]);
            
            // ensure no caching during preview
            return response()->json([
                'success' => true,
                'html' => $html,
                'validation' => [
                    'errors' => $validationErrors,
                    'warnings' => $warnings,
                    'render_time_ms' => $renderTime,
                    'html_size_bytes' => strlen($html),
                    'jobs_count' => count($sampleJobs)
                ],
                'preview_info' => [
                    'jobs_count' => count($sampleJobs),
                    'aggregation_limits' => $this->contentManager->getAggregationLimits(),
                    'note' => 'Preview shows multiple jobs like real notifications'
                ]
            ])->header('Cache-Control','no-store, no-cache, must-revalidate, max-age=0');
            
            
        } catch (\Exception $e) {
            Log::error('EmailContentManager: Error generating email preview', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate email preview: ' . $e->getMessage(),
                'validation' => [
                    'errors' => ['Preview Generation Failed: ' . $e->getMessage()],
                    'warnings' => $warnings
                ]
            ], 500);
        }
    }
    
    /**
     * Send test email
     */
    public function sendTestEmail(Request $request): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        $validator = Validator::make($request->all(), [
            'email' => 'required|email'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid email address'
            ], 422);
        }
        
        try {
            $testEmail = $request->input('email');
            $sampleJobs = $this->contentManager->getSampleJobData();
            
            // Use EmailService to send test email with multiple jobs like real notifications
            $emailService = app(EmailService::class);
            
            $result = $emailService->sendEmail(
                ['email' => $testEmail, 'name' => 'Test User'],
                'Test: New Job Opportunities - Email Content Preview (' . count($sampleJobs) . ' jobs)',
                'modules.jobseeker.emails.jobs.jobseeker_notification_new',
                [
                    'jobs' => $sampleJobs,
                    'jobSeeker' => (object) ['name' => 'Test User'],
                    'setup' => (object) ['name' => 'Test Email Content Settings'],
                ]
            );
            
            Log::info('EmailContentManager: Test email sent', [
                'recipient' => $testEmail,
                'sent_by' => $user->email ?? 'admin',
                'success' => $result['success']
            ]);
            
            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Test email sent successfully!' : 'Failed to send test email'
            ]);
            
        } catch (\Exception $e) {
            Log::error('EmailContentManager: Error sending test email', [
                'error' => $e->getMessage(),
                'user_email' => $user->email ?? 'unknown'
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email'
            ], 500);
        }
    }
    
    /**
     * Get field statistics and usage analytics
     */
    public function getFieldStatistics(): JsonResponse
    {
        try {
            $statistics = [
                'total_fields' => EmailContentSetting::count(),
                'enabled_fields' => EmailContentSetting::enabled()->count(),
                'fields_by_group' => EmailContentSetting::select('field_group', DB::raw('count(*) as count'))
                    ->groupBy('field_group')
                    ->get()
                    ->pluck('count', 'field_group'),
                'enabled_by_group' => EmailContentSetting::enabled()
                    ->select('field_group', DB::raw('count(*) as count'))
                    ->groupBy('field_group')
                    ->get()
                    ->pluck('count', 'field_group'),
                'last_updated' => EmailContentSetting::latest('updated_at')->first()?->updated_at,
                'requires_fetch_count' => EmailContentSetting::where('requires_provider_fetch', true)->count(),
                'conditional_display_count' => EmailContentSetting::where('conditional_display', true)->count(),
            ];
            
            return response()->json([
                'success' => true,
                'statistics' => $statistics
            ]);
            
        } catch (\Exception $e) {
            Log::error('EmailContentManager: Error getting field statistics', [
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get field statistics'
            ], 500);
        }
    }
    
    /**
     * Export current settings as JSON
     */
    public function exportSettings(): JsonResponse
    {
        try {
            $settings = EmailContentSetting::orderBy('field_group')
                ->orderBy('display_order')
                ->get()
                ->map(function ($setting) {
                    return [
                        'field_name' => $setting->field_name,
                        'is_enabled' => $setting->is_enabled,
                        'display_label' => $setting->display_label,
                        'display_order' => $setting->display_order,
                        'field_group' => $setting->field_group,
                        'formatting_options' => $setting->formatting_options,
                        'conditional_display' => $setting->conditional_display,
                        'requires_provider_fetch' => $setting->requires_provider_fetch,
                    ];
                });
            
            $export = [
                'exported_at' => now()->toISOString(),
                'exported_by' => Auth::guard('job_seeker')->user()->email ?? 'admin',
                'version' => '1.0',
                'settings' => $settings
            ];
            
            return response()->json([
                'success' => true,
                'export' => $export
            ]);
            
        } catch (\Exception $e) {
            Log::error('EmailContentManager: Error exporting settings', [
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to export settings'
            ], 500);
        }
    }
    
    /**
     * Persist aggregation limits (min/max jobs per email) using the pseudo-setting 'aggregation_prefs'.
     */
    public function updateAggregationLimits(Request $request): JsonResponse
    {
        try {
            $raw = $request->input('formatting_options');
            $opts = is_string($raw) ? json_decode($raw, true) : (array) $raw;
            $min = (int)($opts['min_jobs_per_email'] ?? 1);
            $max = (int)($opts['max_jobs_per_email'] ?? 20);
            if ($min < 1) { $min = 1; }
            if ($max < $min) { $max = $min; }

            $setting = EmailContentSetting::firstOrCreate(
                ['field_name' => 'aggregation_prefs'],
                [
                    'display_label' => 'Aggregation Preferences',
                    'field_group' => 'basic',
                    'is_enabled' => true,
                    'display_order' => 999,
                ]
            );
            $setting->formatting_options = [
                'min_jobs_per_email' => $min,
                'max_jobs_per_email' => $max,
            ];
            $setting->save();

            $this->contentManager->clearCache();

            return response()->json([
                'success' => true,
                'message' => 'Aggregation limits saved',
                'data' => ['min' => $min, 'max' => $max]
            ])->header('Cache-Control','no-store, no-cache, must-revalidate, max-age=0');
        } catch (\Throwable $e) {
            Log::error('EmailContentManager: Failed to save aggregation limits', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to save aggregation limits'
            ], 500);
        }
    }

    /**
     * Validate rendered HTML for common issues and regressions
     * 
     * Purpose: Detect broken links, missing content, route failures, and template issues
     * in rendered email HTML to prevent delivery of broken emails.
     * 
     * @param string $html Rendered HTML content to validate
     * @return array Array with 'errors' and 'warnings' keys containing validation issues
     */
    private function validateRenderedHtml(string $html): array
    {
        $errors = [];
        $warnings = [];
        
        // Check for obvious template errors
        if (strpos($html, 'ErrorException') !== false || strpos($html, 'FatalError') !== false) {
            $errors[] = 'Template contains PHP error traces';
        }
        
        if (strpos($html, 'Undefined variable') !== false) {
            $errors[] = 'Template has undefined variables';
        }
        
        // Check for broken route() calls
        if (preg_match('/route\([\'"][^\'"]*/i', $html)) {
            $errors[] = 'Template contains unresolved route() calls';
        }
        
        // Check for missing URLs (common signs of route failures)
        if (strpos($html, 'href=""') !== false || strpos($html, 'src=""') !== false) {
            $warnings[] = 'Template contains empty href or src attributes';
        }
        
        // Check for Laravel error pages
        if (strpos($html, 'Whoops, looks like something went wrong') !== false) {
            $errors[] = 'Template rendered Laravel error page';
        }
        
        // Check for missing critical sections
        $criticalSections = [
            'btn-ai-tailor' => 'AI Tailor button missing',
            'unsubscribe' => 'Unsubscribe link missing',
            'job-card' => 'Job cards missing'
        ];
        
        foreach ($criticalSections as $class => $description) {
            if (strpos($html, $class) === false) {
                $warnings[] = $description;
            }
        }
        
        // Check HTML size (too small might indicate rendering failure)
        if (strlen($html) < 1000) {
            $warnings[] = 'Rendered HTML seems unusually small (' . strlen($html) . ' bytes)';
        }
        
        // Check for inline CSS (should be present for email compatibility)
        if (strpos($html, 'style=') === false) {
            $warnings[] = 'No inline styles detected - email may not render properly';
        }
        
        return [
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }

    /**
     * Get group icons for UI
     */
    private function getGroupIcons(): array
    {
        return [
            'basic' => 'info-circle',
            'company' => 'building',
            'details' => 'list-alt',
            'requirements' => 'graduation-cap',
            'application' => 'paper-plane'
        ];
    }
}

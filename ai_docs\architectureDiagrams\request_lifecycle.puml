@startuml Laravel Request Lifecycle

!theme vibrant

participant "Web Server" as WebServer
participant "public/index.php" as Index
participant "HTTP Kernel" as Kernel
participant "Service Providers" as SP
participant "Router" as Router
participant "Middleware" as Middleware
participant "Controller" as Controller
participant "View" as View
participant "Response" as Response

activate WebServer
WebServer -> Index : Request
deactivate WebServer

activate Index
Index -> Kernel : Creates Instance
activate Kernel
Index -> Kernel : handle(Request)

Kernel -> SP : bootstrapApplication()
activate SP
SP --> Kernel : Application Bootstrapped
deactivate SP

Kernel -> Middleware : Send Request Through Pipeline (Global Middleware)
activate Middleware
Middleware --> Kernel : Processed Request

Kernel -> Router : dispatchToRoute(Request)
activate Router
Router --> Kernel : Found Route / Matched Action
deactivate Router

Kernel -> Middleware : Send Request Through Pipeline (Route Middleware)
Middleware --> Kernel : Processed Request

Kernel -> Controller : Dispatch Request to Controller Action
deactivate Middleware
activate Controller
Controller -> View : (Optional) Load View / Data
activate View
View --> Controller : Rendered Content
deactivate View
Controller --> Kernel : Returns Response Object
deactivate Controller

Kernel -> Middleware : Send Response Through Pipeline (Middleware Termination)
activate Middleware
Middleware --> Kernel : Processed Response
deactivate Middleware

Kernel -> Response : Send Response Content
activate Response
Response --> Index : Response Sent
deactivate Response
Index --> WebServer : Return Response
deactivate Index

@enduml 
<?php

namespace Modules\EducationalReports\Http\Controllers;


use App\Classes;
use App\EvaluationSchemaOption;
use App\MoshafSurah;
use App\Student;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;


class CenterWiseStudentRevisionReportDatatablesController extends Controller
{

    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function studentRecords(Request $request)
    {
        DB::connection()->enableQueryLog();
        if ($request->filled('classDate')) {
            $planYearMonth = Carbon::parse($request->get('classDate'));

            $year = $planYearMonth->year;
            $month = $planYearMonth->month;
            $centerId = $request->get('centerId');
            $classes = Classes::whereHas('revision_reports', function ($q) use ($month, $year) {
                $q->whereYear('created_at', $year)
                    ->whereMonth('created_at', $month)
                    ->whereIn('attendance_id', [2/** on-time */, 1/** late */]);
            })
                ->whereHas('revision_plans', function ($query) use ($month, $year) {
                    $query->where('status', 'active')
                        ->whereMonth('created_at', $month)
                        ->whereYear('created_at', $year);
                })
                ->withCount(['revision_plans' => function ($query) use ($month, $year) {
                    $query->where('status', 'active')
                        ->whereMonth('created_at', $month)
                        ->whereYear('created_at', $year);
                }])
                ->where('center_id', $centerId)->withCount('students as studentsCount')
                ->with(['students' => function ($query2) use ($month, $year) {
                    $query2->withCount(['completedHefzReport as attendanceCount' => function ($q) use ($year, $month) {
                        $q->where(function ($q1) use ($year, $month) {
                            $q1->whereYear('created_at', $year)
                                ->whereMonth('created_at', $month);
                        })->where('attendance_id', 2);
                    }]);
                }])
                ->with(['students.completedRevisionReport' => function ($query) use ($month, $year) {

                    $query->where(function ($q) use ($year, $month) {
                        $q->whereYear('student_revision_report.created_at', $year)
                            ->whereMonth('student_revision_report.created_at', $month);
                    });
                }])
                ->with(['students.revision_plans' => function ($query) use ($month, $year) {
                    $query->whereMonth('created_at', $month)
                        ->whereYear('created_at', $year);
                }])
                ->with('teachers')
                ->get();



        return \Yajra\DataTables\DataTables::of($classes)
            ->addIndexColumn()
            ->addColumn('halaqah', function ($halaqah) use ($request, $planYearMonth) {
                return '<a style="color:#b4eeb0; text-decoration: none;" target="_blank" href="'.url('workplace/education/classes/' . $halaqah->id).'" class="class-hover-effect">' . $halaqah->name . '</a>';


                return '<a  style="color: #b4eeb0;" class="section" target="_blank" href="#"> ' . $halaqah->name . '</a>  <a class="section reportDetails" id="reportDetails" target="_blank" href="' . url('workplace/education/classes/' . $halaqah->id . '/reports/create?from_date=' . $planYearMonth->toDateString()) . '"><i class="external alternate icon"></i></a>';


                return $halaqah->name;


            })
            ->addColumn('teacher', function ($halaqah) use ($request, $planYearMonth) {

                $teacherTags = $halaqah->teachers()->get()->map(function ($employee) {
                    $teacherName = ucfirst($employee->name);
                    $supervisorProfileUrl = route('employees.show', ['employee' => $employee->id]);
                    return '<a style="color:#b4eeb0; padding: 2px; display: inline-block; transition: all 0.3s ease;" data-id='.$employee->id.' class="section teacher-link" target="_blank" href="' . $supervisorProfileUrl . '">' . $teacherName . '</a>';
                })->implode(' <span style="color: #1fff0f;"> | </span> ');

                return $teacherTags;
                
                $teachers = $halaqah->teachers()->pluck('employees.name', 'employees.id');


                $tags = [];


                foreach ($teachers as $teacherId => $teacherName) {

                    $tags[] = '<a class="section" target="_blank" href="#" style="color: #b4eeb0;">' . $teacherName . '</a>';
                }

                return implode(' <span style="color: #1fff0f;"> | </span> ', $tags);


            })
            ->addColumn('studentsCount', function ($halaqah) use ($request) {

                return '<h2 style="color: #1fff0f;
    font-weight: bolder;
    font-size: 24px;">' . $halaqah->revision_plans_count . '</h2>';

                return $halaqah->revision_plans_count;


            })
            ->addColumn('revisedPages', function ($classDetails) use ($request) {

//                    get me the total number of pages memorized for this halaqah


                $numberofPagesSum = 0;
                foreach ($classDetails->students as $studentDetails) {


                    if ($studentDetails->completedRevisionReport->count() > 0) {
                        $firstRevision = $studentDetails->completedRevisionReport->sortBy(function ($row) {
                            return [$row->revision_from_surat, $row->revision_from_ayat];
                        })->first();

                        $lastRevision = $studentDetails->completedRevisionReport->sortByDesc(function ($row) {
                            return [$row->revision_to_surat, $row->revision_to_ayat];
                        })->first();


                        $min_revision_from_surat = $firstRevision->revision_from_surat;
                        $min_revision_from_ayat = $firstRevision->revision_from_ayat;
                        $max_revision_to_surat = $lastRevision->revision_to_surat;
                        $max_revision_to_ayat = $lastRevision->revision_to_ayat;
                        $revisionPlan = $studentDetails->revision_plans;
                        $revisionPlan = $revisionPlan[0];

                        if ($revisionPlan->study_direction == 'backward') {
                            $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                $min_revision_from_surat,
                                $min_revision_from_ayat,
                                $max_revision_to_surat,
                                $max_revision_to_ayat
                            ]);


                            $numberofPagesSum += $numberofPages[0]->numberofPagesSum;

                        } else {


                            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                $min_revision_from_surat,
                                $min_revision_from_ayat,
                                $max_revision_to_surat,
                                $max_revision_to_ayat
                            ]);

                            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                            $numberofPagesSum += $results[0]->number_of_pages_sum;
                        }

                    }
                }
                return '<h2 style="color: #1fff0f;
    font-weight: bolder;
    font-size: 24px;">' . $numberofPagesSum . '</h2>';

                return $numberofPagesSum;
            })
            ->addColumn('attendanceDaysPercentage', function ($classDetails) use ($request,$year,$month) {


//                $result = round($classDetails->students->avg('revision_report_attendance_percentage'), 2);
                $result= round($classDetails->averageAttendancePercentage($month,$year),2);



                return  '<div class="progress" style="position: relative;">
  <div  class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
  </div>
</div>';






            })
            ->addColumn('revisionAchievementComparedtoHefzPlan', function ($classDetails) use ($request) {
                try {
                    $revisedNumberofPagesSum = 0;
                    $plannedNumberofPagesSum = 0;
                    $testArray = [];
                    foreach ($classDetails->students as $studentDetails) {
//                        if ($studentDetails->completedHefzReport->count() > 0) {
                            $firstRevision = $studentDetails->completedRevisionReport->sortBy(function ($row) {
                                return [$row->revision_from_surat, $row->revision_from_ayat];
                            })->first();
                            $lastHefz = $studentDetails->completedRevisionReport->sortByDesc(function ($row) {
                                return [$row->revision_to_surat, $row->revision_to_ayat];
                            })->first();
                            $min_revision_from_surat = $firstRevision->revision_from_surat;
                            $min_revision_from_ayat = $firstRevision->revision_from_ayat;
                            $max_revision_to_surat = $lastHefz->revision_to_surat;
                            $max_revision_to_ayat = $lastHefz->revision_to_ayat;
                            $revisionPlan = $studentDetails->revision_plans;
                            $revisionPlan = $revisionPlan[0];
                            // now find out the number of pages memorized so far
                            if ($revisionPlan->study_direction == 'backward') {
                                $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $min_revision_from_surat,
                                    $min_revision_from_ayat,
                                    $max_revision_to_surat,
                                    $max_revision_to_ayat
                                ]);


                                $revisedNumberofPagesSum += $numberofPages[0]->numberofPagesSum;

                            } else {

                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $min_revision_from_surat,
                                    $min_revision_from_ayat,
                                    $max_revision_to_surat,
                                    $max_revision_to_ayat
                                ]);

                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $revisedNumberofPagesSum += $results[0]->number_of_pages_sum;
                            }

                            $firstPlanSurat = $revisionPlan->start_from_surat;
                            $firstPlanAyat = $revisionPlan->start_from_ayat;
                            $lastPlanSurat = $revisionPlan->to_surat;
                            $lastPlanAyat = $revisionPlan->to_ayat;
                            // now find out the number of pages asssigned at the hefz plan
                            if ($revisionPlan->study_direction == 'backward') {
                                $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $firstPlanSurat,
                                    $firstPlanAyat,
                                    $lastPlanSurat,
                                    $lastPlanAyat
                                ]);


                                $plannedNumberofPagesSum += $numberofPages[0]->numberofPagesSum;

                            } else {

                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $firstPlanSurat,
                                    $firstPlanAyat,
                                    $lastPlanSurat,
                                    $lastPlanAyat
                                ]);

                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $plannedNumberofPagesSum += $results[0]->number_of_pages_sum;


                            }


                            if (empty($revisedNumberofPagesSum) || is_null($revisedNumberofPagesSum)) {
                                $result = 0;
                            } elseif (empty($plannedNumberofPagesSum) || is_null($plannedNumberofPagesSum)) {
                                $result = 0;
                            } else {
                                $actual_percentage = round(($revisedNumberofPagesSum / $plannedNumberofPagesSum * 100), 2);
                                $expected_percentage = 100;
//                                    $result = min($actual_percentage, $expected_percentage);
                                $testArray[] = [
                                    'percentage' => min($actual_percentage, $expected_percentage)
                                ];
                            }
//                        }
                    }
                    $sum = array_sum(array_column($testArray, 'percentage'));
                    if ($classDetails->revision_plans_count != 0) {
                        $sum = round($sum / $classDetails->revision_plans_count, 2);
                    } else {
                        // Handle the case where hefz_plans_count is zero
                        $sum = 0;
                    }


                    return  '<div class="progress" style="position: relative;">
  <div data-studentId="' . $studentDetails->id . '" class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$sum . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $sum . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $sum . '%</span>
  </div>
</div>';

                } catch (\Exception $exception) {
                    dd(5);
                }
            })
            ->rawColumns(['revisionAchievementComparedtoHefzPlan', 'halaqah', 'attendanceDaysPercentage','teacher','memorizedPages','revisedPages','studentsCount'])
            ->make(true);

    }


}

}

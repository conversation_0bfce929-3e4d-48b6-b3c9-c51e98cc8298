<?php

Route::group(['middleware' => ['web','auth:employee','missedClockOutMiddleware'], 'prefix' => 'workplace/curriculum', 'namespace' => 'Modules\Curriculum\Http\Controllers'], function()
{
    Route::get('/', 'CurriculumController@index');
    Route::resource('contents' , 'ContentsController')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');;
    Route::resource('content_categories' , 'ContentCategoriesController')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');
//    Route::resource('subjects', 'SubjectsController')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');;
    Route::resource('subjects', 'SubjectsController')->middleware('permission:access subjects');;

//    Route::resource('evaluation_schemas', 'EvaluationSchemasController')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');
    Route::resource('evaluation_schemas', 'EvaluationSchemasController');

    Route::post('subject-contents', 'SubjectsController@contents');
    Route::delete('delete_content/{id}', 'SubjectsController@delete_content')->name('delete_content');

});

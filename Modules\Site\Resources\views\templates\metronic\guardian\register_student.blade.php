@extends(theme_path('guardian.layout.guardian'))

@section('side_content')


 
@if($student == null || $student->status == "update_profile")
<ul id="progressbar">
    <li class="active " id="confirm"><strong> 1-Registration</strong></li>
    <li  id="confirm"><strong> 2-  Student Information</strong></li>

    <li  id="confirm"><strong> 3- {{ trans('common.program_registration') }}</strong></li>
   
  
</ul>


    {!! Form::open(['route' => 'students.store', 'class' => 'form-horizontal','files' => 'true']) !!}

    @include('forms.student.profile_to_gurdian')
    
    <input type="hidden" id="status" name="status" value="profile_completed">
    {!! Form::close() !!}
    Application Progress
    <div class="progress">
      
        <div class="progress-bar progress-bar-striped bg-success" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
      </div>
      @elseif($student->status == "profile_completed")
    <ul id="progressbar">
        <li class="active " id="confirm"><strong> 1-Registration</strong></li>
        <li  class="active " id="confirm"><strong> 2-  Student Information</strong></li>
    
        <li  id="confirm"><strong> 3- {{ trans('common.program_registration') }}</strong></li>
       
      
    </ul>
  
    @include('forms.admission.application')
    Application Progress
    <div class="progress">
      
        <div class="progress-bar progress-bar-striped bg-success" role="progressbar" style="width:40%" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100"></div>
      </div>
      


      @elseif($student->status == "new_admission")
      <div class="well">
          @if($student->current_admission->status == "offered")
       
          <div class="alert alert-success">
            Congratulations, You have an offer to register in the program with the following details
        </div>
        @elseif($student->current_admission->status == "rejected")
        <div class="alert alert-danger">
            Sorry, Your application to register  the program is rejected
        </div>
        @elseif($student->current_admission->status == "waiting_for_interview")
        <div class="alert alert-warning">
            An Interview appointment has been set, Please check the details bellow
        </div>
        @else
        <div class="alert alert-warning">
            Your application is still under process
        </div>
        @endif
        <h3>Admission Details</h3>
        <table class="table table-responsive table-stripped">
            <tr>
                <td>Center</td>
                <td>{{ $student->current_admission->center->name }}</td>
            </tr>
            <tr>
                <td>Class</td>
                <td>{{ $student->current_admission->class->name }}</td>
            </tr>
            <tr>
                <td>Program</td>
                <td>
                    @foreach($student->current_admission->programs as $program)
                    <div>
                        {{ $program->title }}                            
                    </div>
                    @endforeach
                </td>
                <tr>
                    <td>Status</td>
                    <td>{{ $student->current_admission->status }}</td>
                </tr>
            </tr>
            
        </table>
        @if($student->current_admission->status == "waiting_for_interview")
        <h3>Interview Details</h3>
        @foreach($student->current_admission->interviews as $interview)
        <table class="table table-responsive table-stripped">
            <tr>
                <td>Program</td>
                <td>{{ $interview->program->title }}</td>
            </tr>
            <tr>
                <td>Date & Time</td>
                <td>{{ $interview->interview_time }}</td>
            </tr>
            <tr>
                <td>Location</td>
                <td>{{ $interview->location }}</td>
            </tr>
        </table>
        @endforeach
        @elseif($student->current_admission->status == "offered")
        <div class="text-center">
            {!! Form::open(['url' => route('admission.offer_response')]) !!}
            {!! Form::hidden('admission_id' , $student->current_admission->id ) !!}             
            <button name="offer_response" value="1" type="submit"  class="btn btn-success">Accept</button>
            <button name="offer_response" value="0" type="submit"  class="btn btn-danger">Reject</button>
            {!! Form::close() !!}
        </div>
        @elseif($student->current_admission->status == "waiting_for_payment" || $student->current_admission->status == "payment_proof_not_valid")
        <h3>Payment Details</h3>
        {!! Form::open(['url' => route('admission.upload_payment_proof') , 'files' => true]) !!}        
        {!! Form::hidden('admission_id' , $student->current_admission->id ) !!} 
        
        @if(count($errors->all()))
        <div class="alert alert-danger">
            @foreach($errors->all() as $error)
            <p>{{ $error }}</p>
            @endforeach
        </div>
        @endif
        <table class="table table-responsive table-stripped">
            <tr>
                <td>Required Fees Amount</td>
                <td>RM**</td>
            </tr>
            <tr>
                <td>Payment Method</td>
                <td> 
                        <input type="radio" name="payment_method" id="" checked> Upload Proof of Payment/Receipt/Payslip <br>
                        <input type="radio" name="payment_method" id="" disabled> Online Banking <br>
                        <input type="radio" name="payment_method" id="" disabled> Visa/Master  <br>
                </td>
            </tr>
            <tr>
                <td>Paid Amount</td>
                <td>
                    {!! Form::number('paid_amount' , null , ['class' => 'form-control' ,'step' => '.01'  , 'required']) !!}
                    {!! $errors->first('paid_amount', '
                    <p class="help-block alert-danger">
                        :message
                    </p>
                    ') !!}
                </td>
            </tr>
            <tr>
                <td>Proof of Payment</td>
                <td>
                    {!! Form::file('payment_proof' , null , ['class' => 'form-control' , 'required' ]) !!}
                    {!! $errors->first('payment_proof', '
                    <p class="help-block alert-danger">
                        :message
                    </p>
                    ') !!}
                </td>
            </tr>
            <tr>
                <td colspan="2" class="text-center">
                    {!! Form::submit('Upload'  , ['class' => 'btn btn-success']) !!}
                </td>

            </tr>
        </table>
        {!! Form::close() !!}
        @elseif($student->current_admission->status == "waiting_for_orientation")
        <h3>Orientation Details</h3>
        <table class="table table-responsive table-stripped">
           
            <tr>
                <td>Date & Time</td>
                <td>{{ $student->current_admission->orientation->orientation_time}}</td>
            </tr>
            <tr>
                <td>Location</td>
                <td>{{ $student->current_admission->orientation->location}}</td>
            </tr>
        </table>
      
      @endif

      </div>













































      @endif

<style>
        .board{
        width: 75%;
    margin: 60px auto;
    height: 500px;
    background: #fff;
    /*box-shadow: 10px 10px #ccc,-10px 20px #ddd;*/
    }
    .board .nav-tab {
        position: relative;
      
        /* width: 80%; */
        margin: 40px auto;
        margin-bottom: 0;
        box-sizing: border-box;
    
    }
    
    
    
    p.narrow{
        width: 60%;
        margin: 10px auto;
    }
    
    .liner{
        height: 2px;
        background: #ddd;
        position: absolute;
        width: 80%;
        margin: 0 auto;
        left: 0;
        right: 0;
        top: 50%;
        z-index: 1;
    }
    
    .nav-tab > li.active > a, .nav-tab > li.active > a:hover, .nav-tab > li.active > a:focus {
        color: #555555;
        cursor: default;
        /* background-color: #ffffff; */
        border: 0;
       
    }
    
    span.round-tabs{
        width: 70px;
        height: 70px;
        line-height: 70px;
        display: inline-block;
        border-radius: 100px;
        background: white;
        z-index: 2;
        position: absolute;
        left: 0;
        text-align: center;
        font-size: 25px;
    }
    
    span.round-tabs.one{
        color: rgb(34, 194, 34);border: 2px solid rgb(34, 194, 34);
    }
    
    li.active span.round-tabs.one{
        background: #fff !important;
        border: 2px solid #ddd;
        color: rgb(34, 194, 34);
    }
    
    span.round-tabs.two{
        color: #febe29;border: 2px solid #febe29;
    }
    
    li.active span.round-tabs.two{
        background: #fff !important;
        border: 2px solid #ddd;
        color: #febe29;
    }
    
    span.round-tabs.three{
        color: #3e5e9a;border: 2px solid #3e5e9a;
    }
    
    li.active span.round-tabs.three{
        background: #fff !important;
        border: 2px solid #ddd;
        color: #3e5e9a;
    }
    
    span.round-tabs.four{
        color: #f1685e;border: 2px solid #f1685e;
    }
    
    li.active span.round-tabs.four{
        background: #fff !important;
        border: 2px solid #ddd;
        color: #f1685e;
    }
    
    span.round-tabs.five{
        color: #999;border: 2px solid #999;
    }
    
    li.active span.round-tabs.five{
        background: #fff !important;
        border: 2px solid #ddd;
        color: #999;
    }
    
    .nav-tab > li.active > a span.round-tabs{
        background: #fafafa;
    }
    .nav-tab > li {
        width: 20%;
    }
    
    .nav-tab > li:after {
        content: " ";
        position: absolute;
        left: 45%;
       opacity:0;
        margin: 0 auto;
        bottom: 0px;
        border: 5px solid transparent;
    
        transition:0.1s ease-in-out;
        
    }
    .nav-tab > li.active:after {
        content: " ";
        position: absolute;
        left: 45%;
       opacity:1;
        margin: 0 auto;
        bottom: 0px;
        border: 10px solid transparent;
     
        
    }
    .nav-tab > li a{
       width: 70px;
       height: 70px;
       margin: 20px auto;
       
       padding: 0;
    }
    
    .nav-tab > li a:hover{
        background: transparent;
    }
    
    .tab-content{
    }
    .tab-pane{
       position: relative;
    padding-top: 50px;
    }
    .tab-content .head{
        font-family: 'Roboto Condensed', sans-serif;
        font-size: 25px;
        text-transform: uppercase;
        padding-bottom: 10px;
    }
    .btn-outline-rounded{
        padding: 10px 40px;
        margin: 20px 0;
        border: 2px solid transparent;
        border-radius: 25px;
    }
    
    .btn.green{
        background-color:#5cb85c;
        /*border: 2px solid #5cb85c;*/
        color: #ffffff;
    }
    
    
    
    @media( max-width : 585px ){
        
        .board {
    width: 90%;
    height:auto !important;
    }
        span.round-tabs {
            font-size:16px;
    width: 50px;
    height: 50px;
    line-height: 50px;
        }
        .tab-content .head{
            font-size:20px;
            }
        .nav-tab > li a {
    width: 50px;
    height: 50px;
    line-height:50px;
    }
    
    .nav-tab > li.active:after {
    content: " ";
    position: absolute;
    left: 35%;
    }
    
    .btn-outline-rounded {
        padding:12px 20px;
        }
    }
    * {
    margin: 0;
    padding: 0
}

html {
    height: 100%
}

#grad1 {
    background-color: : #9C27B0;
    background-image: linear-gradient(120deg, #FF4081, #81D4FA)
}

#msform {
    text-align: center;
    position: relative;
    margin-top: 20px
}

#msform fieldset .form-card {
    background: white;
    border: 0 none;
    border-radius: 0px;
    box-shadow: 0 2px 2px 2px rgba(0, 0, 0, 0.2);
    padding: 20px 40px 30px 40px;
    box-sizing: border-box;
    width: 94%;
    margin: 0 3% 20px 3%;
    position: relative
}

#msform fieldset {
    background: white;
    border: 0 none;
    border-radius: 0.5rem;
    box-sizing: border-box;
    width: 100%;
    margin: 0;
    padding-bottom: 20px;
    position: relative
}

#msform fieldset:not(:first-of-type) {
    display: none
}

#msform fieldset .form-card {
    text-align: left;
    color: #9E9E9E
}

#msform input,
#msform textarea {
    padding: 0px 8px 4px 8px;
    border: none;
    border-bottom: 1px solid #ccc;
    border-radius: 0px;
    margin-bottom: 25px;
    margin-top: 2px;
    width: 100%;
    box-sizing: border-box;
    font-family: montserrat;
    color: #2C3E50;
    font-size: 16px;
    letter-spacing: 1px
}

#msform input:focus,
#msform textarea:focus {
    -moz-box-shadow: none !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    border: none;
    font-weight: bold;
    border-bottom: 2px solid skyblue;
    outline-width: 0
}

#msform .action-button {
    width: 100px;
    background: skyblue;
    font-weight: bold;
    color: white;
    border: 0 none;
    border-radius: 0px;
    cursor: pointer;
    padding: 10px 5px;
    margin: 10px 5px
}

#msform .action-button:hover,
#msform .action-button:focus {
    box-shadow: 0 0 0 2px white, 0 0 0 3px skyblue
}

#msform .action-button-previous {
    width: 100px;
    background: #616161;
    font-weight: bold;
    color: white;
    border: 0 none;
    border-radius: 0px;
    cursor: pointer;
    padding: 10px 5px;
    margin: 10px 5px
}

#msform .action-button-previous:hover,
#msform .action-button-previous:focus {
    box-shadow: 0 0 0 2px white, 0 0 0 3px #616161
}

select.list-dt {
    border: none;
    outline: 0;
    border-bottom: 1px solid #ccc;
    padding: 2px 5px 3px 5px;
    margin: 2px
}

select.list-dt:focus {
    border-bottom: 2px solid skyblue
}

.card {
    z-index: 0;
    border: none;
    border-radius: 0.5rem;
    position: relative
}

.fs-title {
    font-size: 25px;
    color: #2C3E50;
    margin-bottom: 10px;
    font-weight: bold;
    text-align: left
}

#progressbar {
    margin-bottom: 30px;
    overflow: hidden;
    color: lightgrey
}

#progressbar .active {
    color: #000000
}

#progressbar li {
    list-style-type: none;
    font-size: 12px;
    width: 25%;
    float: left;
    position: relative
}

#progressbar #account:before {
    font-family: FontAwesome;
    content: "\f023"
}

#progressbar #personal:before {
    font-family: FontAwesome;
    content: "\f007"
}

#progressbar #payment:before {
    font-family: FontAwesome;
    content: "\f09d"
}

#progressbar #confirm:before {
    font-family: FontAwesome;
    content: "\f00c"
}

#progressbar li:before {
    width: 50px;
    height: 50px;
    line-height: 45px;
    display: block;
    font-size: 18px;
    color: #ffffff;
    background: lightgray;
    border-radius: 50%;
    margin: 0 auto 10px auto;
    padding: 2px
}

#progressbar li:after {
    content: '';
    width: 100%;
    height: 2px;
    background: lightgray;
    position: absolute;
    left: 0;
    top: 25px;
    z-index: -1
}

#progressbar li.active:before,
#progressbar li.active:after {
    background: skyblue
}

.radio-group {
    position: relative;
    margin-bottom: 25px
}

.radio {
    display: inline-block;
    width: 204;
    height: 104;
    border-radius: 0;
    background: lightblue;
    box-shadow: 0 2px 2px 2px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    cursor: pointer;
    margin: 8px 2px
}

.radio:hover {
    box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.3)
}

.radio.selected {
    box-shadow: 1px 1px 2px 2px rgba(0, 0, 0, 0.1)
}

.fit-image {
    width: 100%;
    object-fit: cover
}
    </style>
@endsection
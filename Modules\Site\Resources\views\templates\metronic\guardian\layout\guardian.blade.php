@extends(theme_path('layouts.master'))
@section('content')
<section>
    <div class="container">
        <!-- RIGHT -->
        <div class="col-lg-9 col-md-9 col-sm-8 col-lg-push-3 col-md-push-3 col-sm-push-4 margin-bottom-80">
            @yield('side_content')            
        </div>
        <!-- LEFT -->
        <div class="col-lg-3 col-md-3 col-sm-4 col-lg-pull-9 col-md-pull-9 col-sm-pull-8">
            <div class="thumbnail text-center">
                <img src="{{Auth::user()->image ? asset(Auth::user()->image) : asset('avatar.jpg') }}" alt="" />
                <h2 class="size-18 margin-top-10 margin-bottom-0">{{ Auth::user()->name}}</h2>
                <h3 class="size-11 margin-top-0 margin-bottom-10 text-muted"></h3>
            </div>
            <!-- /completed -->

            <!-- SIDE NAV -->
            <ul class="side-nav list-group margin-bottom-60" id="sidebar-nav">
                <li class="list-group-item"><a href="{{ url(config('app.locale').'/guardian/home') }}"><i class="fa fa-home"></i> Dashboard</a></li>
                <li class="list-group-item"><a href="profile"><i class="fa fa-eye"></i> PROFILE</a></li>
                <li class="list-group-item"><a href="{{ url(config('app.locale').'/guardian/home') }}"><i class="fa fa-tasks"></i> Students</a></li>
                <li class="list-group-item"><a href="update"><i class="fa fa-gears"></i> SETTINGS</a></li>

            </ul>
            <!-- /SIDE NAV -->


            
            </div>

        </div>
        
    </div>
</section>
<!-- / -->



@endsection

<?php

declare(strict_types=1);

namespace Modules\General\Http\Controllers;

use App\Http\Controllers\Controller;
use App\StudentRevisionPlan;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

final class AddRevisionPlanCommentController extends Controller
{
    public function __invoke(Request $request)
    {
        try {
            Log::info('Revision Plan Comment Addition Started', [
                'user_id' => auth()->id(),
                'plan_id' => $request->plan_id
            ]);

            $request->validate([
                'plan_id' => 'required|integer|exists:student_revision_plans,id',
                'comment' => 'required|string|max:1000'
            ]);

            DB::beginTransaction();

            $plan = StudentRevisionPlan::with(['student', 'center'])->find($request->plan_id);

            if (!$plan) {
                return response()->json([
                    'success' => false,
                    'message' => 'Plan not found'
                ], 404);
            }

            // Update the plan with supervisor comment
            $plan->supervisor_comment = $request->comment;
            $plan->commented_by = auth()->user()->id;
            $plan->commented_at = Carbon::now();
            $plan->updated_at = Carbon::now();
            $plan->save();

            DB::commit();

            Log::info('Revision Plan Comment Added Successfully', [
                'user_id' => auth()->id(),
                'plan_id' => $request->plan_id,
                'student_id' => $plan->student_id,
                'student_name' => $plan->student->full_name
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Comment added successfully for ' . $plan->student->full_name . '\'s revision plan',
                'comment' => $request->comment,
                'commented_at' => Carbon::now()->diffForHumans(),
                'commented_by' => auth()->user()->full_name
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Revision Plan Comment Validation Failed', [
                'user_id' => auth()->id(),
                'plan_id' => $request->plan_id ?? null,
                'errors' => $e->errors()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->validator->errors()->all())
            ], 422);

        } catch (\Exception $e) {
            DB::rollback();
            
            Log::error('Revision Plan Comment Addition Failed', [
                'user_id' => auth()->id(),
                'plan_id' => $request->plan_id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while adding the comment: ' . $e->getMessage()
            ], 500);
        }
    }
} 
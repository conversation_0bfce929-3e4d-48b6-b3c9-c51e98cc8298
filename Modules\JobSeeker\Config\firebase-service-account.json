{"type": "service_account", "project_id": "jobseeker-push-notification", "private_key_id": "093ce41cd0500d405cd3193b9cb9cf3c683a6104", "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDtucq0YvkJzjPO\nUz73po0dgLQY4lytYtxjZse3Hwr7qxdebFn3jqkJGKRiJDQvwHlr/qaVlJ1k4YFJ\njSp4QiEQomUCs0gdnbG1yZ09xezm+JoPVh6oF4G5FvbnY0oI/4irR3J62vEpgk4f\nmBgbftrRbwy/zFYHp55glu0gUZCDAXUbD6xujoUXc8MkxsN+ytqvj49lOhynT28j\nRCtk9sHjF5ElckfEDEym6bSr4L6N3ts+/9XDXnhXF8VvD8Bk8xQk78/pLdBAR2u7\nE/7B2Q1sBsIFy/mX0GLsdWmuQnDUaKxugUU3rwbTj/ih8RGQSNyCTee6hW7DblzC\nunP6JrBBAgMBAAECggEAHrEOfnSKvN0uQL9Bpr042dbB1GFL4Cdz1weFgFsV0sEs\n5AgZeMXRMAyfkyYjxgh+WgGHZfzJHKWlSKbc6qggVw1IUkLC7EPtliVAqPbdQO1W\nt+Ur/GgF26zJ1WeIaZn9ffOG+wfk+9wdaaR8HvdP7psnlQ70tR9LFM2s3AUmeFXf\nnWQ5nEtIeV7lxVk2bhmV+DDHzSFGplwgJrzYxUwRidjfWjV2+hb4w6Z1NPnpV8YJ\nALyY6PtK8HNrw6a1H31vTFuBsFzvUReUR9M1qiDYPCu2qReB3aTDRgm6sgVQeGlV\n3xq6GmdBD9vKwTqthjh8FrfaC9O+fnt/P5baKT+XSwKBgQD9CMxBtyxvC2N43v1b\nk/4F0trvulVx3uWnGwanU+ioDH0BteJ7yqxes6frDDoopQd2946uaW0JGnPyv8/p\noI0rAFpbj+BibX/+QQg+hDAui9xkHwKVKTLh2mX5SAjFgqkQEsPu4zognLIY8qx/\nzlk1KpYAo3tYv8HopWbOJS9PjwKBgQDwgw/lZfZ3y04IdWj09zWn6ejkFsDKUgyH\nAuuc7Cm0TVh7ACKFpdHTvWo74w6mrko0U0tTCyFoWKapGH7LCXxSVCWbcYDfqQdw\nE5wh3ciWg2Hi6tIeDB0FI2/rAPGE8WjZOZPmi67XH5GtC7YwCL6lGkU+vvX3WSRH\nr60pjD0bLwKBgQC4/zSvGygea9onJglgahv/0ACH4tjTrx6ChUzhC4T/UFoY5Q0Y\nar6JYppzS/Y9fyKtBSPJOoUgcbmyEjEFY3+yn8hwRwnLjLlWf99yLZ04esMAZHez\n7HIqFE4X6mWXWgcXHFEqF/GIpGfFu68Ipiosp90RzHTDFzfDSyKVtzQW2wKBgQCM\nQUHh2sYSMeL9l8pvIEKP+x4u9P3UfmiDyzYvOqFPXdN9VyTKn1pvigQLjGuc1AK9\n5N0GPnVq7leyQHCgYpW3RQ7xSQ2IYH/P4f+o2DcDXvaUZ6/EiffaaUH434H4rvcf\nxi3zwf1hwi0npq7F8DONAHblEmNJrRg1HcNzeS6hTQKBgATNhNT0Rhn0wS5D0Hpk\nw3Hx4U50zJpwwfd0Xql8+4zXL6cM7DFpMZE1SkOQfAAjgLHOP38RzSnxCGRb/NIT\nI/XAKO6SO2rI5n4gnui7akkWp5Jh7QMO+ujLcDNIUNexogzHEl0+hK6eMM8LE5h2\n/ujXJJfIuGVBoTrtbQ8hrkL\n-----END PRIVATE KEY-----\n", "client_email": "*******", "client_id": "111391222403786823992", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs", "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40jobseeker-push-notification.iam.gserviceaccount.com", "universe_domain": "googleapis.com"}
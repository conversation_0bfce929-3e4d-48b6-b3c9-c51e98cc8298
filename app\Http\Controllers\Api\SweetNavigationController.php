<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * Sweet Navigation API Controller
 * 
 * Provides sample endpoints for testing the Sweet Navigation component.
 * This controller demonstrates the expected JSON response format and
 * can be used as a template for implementing navigation endpoints.
 */
final class SweetNavigationController extends Controller
{
    /**
     * Get sample classes navigation data
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getClassesNavigation(Request $request): JsonResponse
    {
        try {
            $currentId = $request->get('current_id');
            
            // Sample data - replace with actual database queries
            $groups = [
                [
                    'name' => 'Mathematics Program',
                    'items' => [
                        [
                            'id' => '1',
                            'name' => 'Algebra Fundamentals',
                            'url' => '/classes/1',
                            'is_current' => $currentId === '1',
                            'subtitle' => 'Teacher: <a href="/teachers/5" class="sweet-navigation-link">Dr. <PERSON></a> | Mon, Wed, Fri 10:00-11:30',
                            'count' => '28',
                            'actions' => [
                                [
                                    'type' => 'report',
                                    'url' => '/classes/1/report',
                                    'title' => 'View Class Report',
                                    'label' => 'R'
                                ],
                                [
                                    'type' => 'show',
                                    'url' => '/classes/1',
                                    'title' => 'View Class Details',
                                    'label' => 'V'
                                ],
                                [
                                    'type' => 'reports',
                                    'url' => '/classes/1/monthly-plan',
                                    'title' => 'Monthly Plan',
                                    'label' => 'P'
                                ]
                            ]
                        ],
                        [
                            'id' => '2',
                            'name' => 'Advanced Calculus',
                            'url' => '/classes/2',
                            'is_current' => $currentId === '2',
                            'subtitle' => 'Teacher: <a href="/teachers/8" class="sweet-navigation-link">Prof. Johnson</a> | Tue, Thu 14:00-16:00',
                            'count' => '15',
                            'actions' => [
                                [
                                    'type' => 'report',
                                    'url' => '/classes/2/report',
                                    'title' => 'View Class Report',
                                    'label' => 'R'
                                ],
                                [
                                    'type' => 'show',
                                    'url' => '/classes/2',
                                    'title' => 'View Class Details',
                                    'label' => 'V'
                                ]
                            ]
                        ],
                        [
                            'id' => '3',
                            'name' => 'Statistics & Probability',
                            'url' => '/classes/3',
                            'is_current' => $currentId === '3',
                            'subtitle' => 'Teacher: <a href="/teachers/12" class="sweet-navigation-link">Dr. Wilson</a> | Mon, Wed 16:00-17:30',
                            'count' => '22'
                        ]
                    ]
                ],
                [
                    'name' => 'Science Program',
                    'items' => [
                        [
                            'id' => '4',
                            'name' => 'Physics Laboratory',
                            'url' => '/classes/4',
                            'is_current' => $currentId === '4',
                            'subtitle' => 'Teacher: <a href="/teachers/15" class="sweet-navigation-link">Dr. Brown</a> | Wed, Fri 16:00-18:00',
                            'count' => '20',
                            'actions' => [
                                [
                                    'type' => 'report',
                                    'url' => '/classes/4/report',
                                    'title' => 'View Class Report',
                                    'label' => 'R'
                                ],
                                [
                                    'type' => 'show',
                                    'url' => '/classes/4',
                                    'title' => 'View Class Details',
                                    'label' => 'V'
                                ]
                            ]
                        ],
                        [
                            'id' => '5',
                            'name' => 'Chemistry Advanced',
                            'url' => '/classes/5',
                            'is_current' => $currentId === '5',
                            'subtitle' => 'Teacher: <a href="/teachers/18" class="sweet-navigation-link">Prof. Davis</a> | Tue, Thu 10:00-12:00',
                            'count' => '18'
                        ]
                    ]
                ],
                [
                    'name' => 'Language Arts',
                    'items' => [
                        [
                            'id' => '6',
                            'name' => 'English Literature',
                            'url' => '/classes/6',
                            'is_current' => $currentId === '6',
                            'subtitle' => 'Teacher: <a href="/teachers/22" class="sweet-navigation-link">Ms. Anderson</a> | Daily 09:00-10:00',
                            'count' => '32',
                            'actions' => [
                                [
                                    'type' => 'report',
                                    'url' => '/classes/6/report',
                                    'title' => 'View Class Report',
                                    'label' => 'R'
                                ]
                            ]
                        ]
                    ]
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'groups' => $groups
                ],
                'message' => 'Classes navigation data loaded successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Sweet Navigation Classes Error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to load classes navigation data',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get sample students navigation data
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudentsNavigation(Request $request): JsonResponse
    {
        try {
            $currentId = $request->get('current_id');
            
            $groups = [
                [
                    'name' => 'Grade 10',
                    'items' => [
                        [
                            'id' => '101',
                            'name' => 'Ahmed Al-Rashid',
                            'url' => '/students/101',
                            'is_current' => $currentId === '101',
                            'subtitle' => 'Class: Math-A | Guardian: <a href="/guardians/50" class="sweet-navigation-link">Mr. Al-Rashid</a>',
                            'count' => '95%',
                            'actions' => [
                                [
                                    'type' => 'show',
                                    'url' => '/students/101',
                                    'title' => 'View Student Profile',
                                    'label' => 'V'
                                ],
                                [
                                    'type' => 'report',
                                    'url' => '/students/101/grades',
                                    'title' => 'View Grades',
                                    'label' => 'G'
                                ]
                            ]
                        ],
                        [
                            'id' => '102',
                            'name' => 'Fatima Hassan',
                            'url' => '/students/102',
                            'is_current' => $currentId === '102',
                            'subtitle' => 'Class: Science-B | Guardian: <a href="/guardians/51" class="sweet-navigation-link">Mrs. Hassan</a>',
                            'count' => '88%'
                        ]
                    ]
                ],
                [
                    'name' => 'Grade 11',
                    'items' => [
                        [
                            'id' => '201',
                            'name' => 'Omar Khalil',
                            'url' => '/students/201',
                            'is_current' => $currentId === '201',
                            'subtitle' => 'Class: Physics-A | Guardian: <a href="/guardians/75" class="sweet-navigation-link">Dr. Khalil</a>',
                            'count' => '92%'
                        ]
                    ]
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'groups' => $groups
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Sweet Navigation Students Error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to load students navigation data'
            ], 500);
        }
    }

    /**
     * Get sample teachers navigation data
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getTeachersNavigation(Request $request): JsonResponse
    {
        try {
            $currentId = $request->get('current_id');
            
            $groups = [
                [
                    'name' => 'Mathematics Department',
                    'items' => [
                        [
                            'id' => '5',
                            'name' => 'Dr. Smith',
                            'url' => '/teachers/5',
                            'is_current' => $currentId === '5',
                            'subtitle' => 'Specialization: Algebra & Calculus | Classes: 3',
                            'count' => '85',
                            'actions' => [
                                [
                                    'type' => 'show',
                                    'url' => '/teachers/5',
                                    'title' => 'View Teacher Profile',
                                    'label' => 'V'
                                ],
                                [
                                    'type' => 'report',
                                    'url' => '/teachers/5/schedule',
                                    'title' => 'View Schedule',
                                    'label' => 'S'
                                ]
                            ]
                        ]
                    ]
                ],
                [
                    'name' => 'Science Department',
                    'items' => [
                        [
                            'id' => '15',
                            'name' => 'Dr. Brown',
                            'url' => '/teachers/15',
                            'is_current' => $currentId === '15',
                            'subtitle' => 'Specialization: Physics & Chemistry | Classes: 2',
                            'count' => '40'
                        ]
                    ]
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'groups' => $groups
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Sweet Navigation Teachers Error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to load teachers navigation data'
            ], 500);
        }
    }

    /**
     * Simulate an error response for testing error handling
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getErrorTest(Request $request): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'This is a test error response',
            'error' => 'Simulated error for testing error handling functionality'
        ], 404);
    }

    /**
     * Get empty navigation data for testing empty states
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getEmptyNavigation(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'groups' => []
            ],
            'message' => 'No items found'
        ]);
    }
}

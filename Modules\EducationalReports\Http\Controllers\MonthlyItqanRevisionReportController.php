<?php

namespace Modules\EducationalReports\Http\Controllers;

use App\AttendanceOption;
use App\Employee;
use App\EvaluationSchemaOption;
use App\StudentAttendance;
use App\Student;
use App\StudentRevisionPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentRevisionReport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class MonthlyItqanRevisionReportController extends Controller
{


    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function __invoke(Request $request)
    {

        DB::connection()->enableQueryLog();
        //        if ($request->ajax()) {
        try {
            $planYearMonth = Carbon::parse($request->get('classDate'));
            $year = $planYearMonth->year;
            $month = $planYearMonth->month;


            $centers = Center::whereHas('classes.revision_plans', function ($query) use ($month, $year) {
                $query->where('status', 'active')
                    ->whereMonth('created_at', $month)
                    ->whereYear('created_at', $year);

            })->with([
                    'employee',
                    'classes' => function ($query) use ($month, $year) {
                        $query->whereHas('revision_plans', function ($q) use ($month, $year) {
                            $q->where('status', 'active')
                                ->whereMonth('created_at', $month)
                                ->whereYear('created_at', $year);
                        })->withCount([
                            'completedRevisionReport as attendance_days_count' => function ($query) use ($month, $year){
                                $query->whereMonth('created_at', $month)
                                    ->whereYear('created_at', $year)
                                    ->where('attendance_id', 2);
                            },
                            'completedRevisionReport as completed_revision_count' => function ($query) use ($month, $year) {
                                $query->whereMonth('created_at', $month)
                                    ->whereYear('created_at', $year);
                            }
                        ])
                            ->with([
                                'completedRevisionReport' => function ($query) use ($month, $year) {
                                    $query->whereMonth('created_at', $month)
                                        ->whereYear('created_at', $year);
                                },
                                'completedRevisionReport' => function ($query) use ($month, $year) {
                                    $query->whereMonth('created_at', $month)
                                        ->whereYear('created_at', $year);
                                },
                                'revision_plans' => function ($query) use ($month, $year) {
                                    $query->whereMonth('created_at', $month)
                                        ->whereYear('created_at', $year);
                                },

                            ]);
                    }
                ])->whereHas('classes', function ($query) use ($month, $year) {
                $query->whereHas('revision_plans', function ($q) use ($month, $year) {
                    $q->where('status', 'active')
                        ->whereMonth('created_at', $month)
                        ->whereYear('created_at', $year);
                });
            })->get();



            return \Yajra\DataTables\DataTables::of($centers)
                ->addIndexColumn()
                ->addColumn('centersCount', function ($center) use ($request, $month, $year) {

                    return '<a style="color:#b4eeb0; text-decoration: none;" target="_blank" href="/workplace/education/classes?centers[]=' . $center->id . '" class="center-hover-effect">' . $center->name . '</a>';

                })
                ->addColumn('supervisor', function ($center) use ($request, $month, $year) {



                    $supervisorTags = $center->employee()->get()->map(function ($employee) {
                        $supervisorName = ucfirst($employee->name);
                        $supervisorProfileUrl = route('employees.show', ['employee' => $employee->id]);
                        return '<a style="color:#b4eeb0; padding: 2px; display: inline-block; transition: all 0.3s ease;" data-id='.$employee->id.' class="section supervisor-link" target="_blank" href="' . $supervisorProfileUrl . '">' . $supervisorName . '</a>';
                    })->implode(' <span style="color: #1fff0f;"> | </span> ');

                    return $supervisorTags;


                    $supervisors = $center->employee()->pluck('employees.name', 'employees.id');


                    $supervisorTags = [];

                    foreach ($supervisors as $supervisorId => $supervisorName) {

                        $supervisorTags[] = '<a data-id='.$supervisorId.' class="section" target="_blank" href="#">' . $supervisorName . '</a>';
                    }

                    return implode(' | ', $supervisorTags);
                })
                ->addColumn('halaqahsCount', function ($center) use ($request, $month, $year) {


                    $halaqahCount = $center->classes->pluck('revision_plans.*.class_id')->flatten()->unique()->count();

                    return '<h2 style="color: #1fff0f;
font-weight: bolder;
font-size: 24px;">' . $halaqahCount . '</h2>';


                })
                ->addColumn('studentsCount', function ($center) use ($request, $month, $year) {

//                    This code uses the load method to load the related revision_plans for each class in the $center. The pluck method is then used to extract the student_ids from the loaded revision_plans, and the flatten, unique, and count methods are used to calculate the number of unique student_ids
                    $studentCount = $center->classes->pluck('revision_plans.*.student_id')->flatten()->unique()->count();

                    return '<h2 style="color: #1fff0f;
font-weight: bolder;
font-size: 24px;">' . $studentCount . '</h2>';

                })
                ->addColumn('revisedPages', function ($center) use ($request) {
                    $numberofPagesSum = 0;
                    foreach ($center->classes as $classDetails) {

                        foreach ($classDetails->completedRevisionReport as $reportDetails) {

                            if ($reportDetails->revisionPlan->study_direction == 'backward') {
                                $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $reportDetails->revision_from_surat,
                                    $reportDetails->revision_from_ayat,
                                    $reportDetails->revision_to_surat,
                                    $reportDetails->revision_to_ayat
                                ]);
                                $numberofPagesSum += $numberofPages[0]->numberofPagesSum;


                            } else {

                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $reportDetails->revision_from_surat,
                                    $reportDetails->revision_from_ayat,
                                    $reportDetails->revision_to_surat,
                                    $reportDetails->revision_to_ayat
                                ]);

                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $numberofPagesSum += $results[0]->number_of_pages_sum;


                            }
                        }

                    }

                    return '<h2 style="color: #1fff0f;
    font-weight: bolder;
    font-size: 24px;">' . $numberofPagesSum . '</h2>';
                    return $numberofPagesSum;


                })
                ->addColumn('attendanceDaysPercentage', function ($center) use ($request, $month, $year) {

                    $attendanceCount = 0;
                    $totalSessions = 0;
                    foreach ($center->classes as $classDetails) {

                        $attendanceCount += $classDetails->attendance_days_count ?? 0;
                        $totalSessions += $classDetails->completed_revision_count ?? 0;

                    }

//                    $result = 0;
//                    if (($attendanceCount == 0 or is_null($attendanceCount)) && ($totalSessions == 0 or is_null($totalSessions))) {
//                        $result = 0;
//                    } elseif ($attendanceCount == 0 or is_null($attendanceCount)) {
//                        $result = 0;
//                    } elseif ($totalSessions == 0 or is_null($totalSessions)) {
//                        $result = 0;
//                    } else {
//                        $result = round(($attendanceCount / 30 * 100), 2);
//                    }
                    $result = ($totalSessions > 0) ? round(($attendanceCount / $totalSessions * 100), 2) : 0;


                    return  '<div class="progress" style="position: relative;">
  <div  class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
  </div>
</div>';

                })
                ->addColumn('revisionAchievementComparedtoRevisionPlan', function ($center) use ($request, $month, $year) {
                    $planYearMonth = Carbon::parse($request->get('classDate'));
                    $memorizedNumberofPagesSum = 0;
                    $plannedNumberofPagesSum = 0;
                    $classLevelMemorizedPages = [];
                    $plannedPagesOfClassesOfCurrentCenter = [];
                    $percentageOfRevisionAchievementForEachStudent = [];
                    foreach ($center->classes as $classDetails) {
                        $classLevelMemorizedPages[] = $memorizedNumberofPagesSum;
                        $plannedPagesOfClassesOfCurrentCenter[] = $plannedNumberofPagesSum;
                        foreach ($classDetails->students as $studentDetails) {
                            $studentDetails->load(['completedRevisionReport' => function ($query)use ($month, $year) {
                                $query->whereMonth('created_at', $month)
                                    ->whereYear('created_at', $year);
                            }]);
                            if ($studentDetails->completedRevisionReport->count() > 0) {
                                $firstRevision = $studentDetails->completedRevisionReport->sortBy(function ($row) {
                                    return [$row->revision_from_surat, $row->revision_from_ayat];
                                })->first();
                                $lastRevision = $studentDetails->completedRevisionReport->sortByDesc(function ($row) {
                                    return [$row->revision_to_surat, $row->revision_to_ayat];
                                })->first();
                                $min_revision_from_surat = $firstRevision->revision_from_surat;
                                $min_revision_from_ayat = $firstRevision->revision_from_ayat;
                                $max_revision_to_surat = $lastRevision->revision_to_surat;
                                $max_revision_to_ayat = $lastRevision->revision_to_ayat;
                                $revisionPlan = $studentDetails->revision_plans;
                                $revisionPlan = $revisionPlan[0];

                                // now find out the number of pages memorized so far
                                if ($revisionPlan->study_direction == 'backward') {
                                    $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                        $min_revision_from_surat,
                                        $min_revision_from_ayat,
                                        $max_revision_to_surat,
                                        $max_revision_to_ayat
                                    ]);


                                    $memorizedNumberofPagesSum += $numberofPages[0]->numberofPagesSum;

                                } else {

                                    DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                        $min_revision_from_surat,
                                        $min_revision_from_ayat,
                                        $max_revision_to_surat,
                                        $max_revision_to_ayat
                                    ]);

                                    $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                    $memorizedNumberofPagesSum += $results[0]->number_of_pages_sum;
                                }
                            }


                            // Now we are going to get the planned Revision PLanned
                            $firstPlanSurat = $revisionPlan->start_from_surat;
                            $firstPlanAyat = $revisionPlan->start_from_ayat;
                            $lastPlanSurat = $revisionPlan->to_surat;
                            $lastPlanAyat = $revisionPlan->to_ayat;
                            if ($revisionPlan->study_direction == 'backward') {
                                $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $firstPlanSurat,
                                    $firstPlanAyat,
                                    $lastPlanSurat,
                                    $lastPlanAyat
                                ]);


                                $plannedNumberofPagesSum += $numberofPages[0]->numberofPagesSum;

                            }
                            else {

                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $firstPlanSurat,
                                    $firstPlanAyat,
                                    $lastPlanSurat,
                                    $lastPlanAyat
                                ]);

                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $plannedNumberofPagesSum += $results[0]->number_of_pages_sum;


                            }


                            if (empty($memorizedNumberofPagesSum) || is_null($memorizedNumberofPagesSum)) {
//                            $result = "No revision so far";
                                $result = 0;
                            } elseif (empty($plannedNumberofPagesSum) || is_null($plannedNumberofPagesSum)) {
//                            $result = "No plan available";
                                $result = 0;
                            } else {


                                $actual_percentage = round(($memorizedNumberofPagesSum / $plannedNumberofPagesSum * 100), 2);
                                $expected_percentage = 100;
//                                    $result = min($actual_percentage, $expected_percentage);
                                $percentageOfRevisionAchievementForEachStudent[$studentDetails->pivot->class_id] = [
//                                        'memorizedNumberofPagesSum' => $memorizedNumberofPagesSum,
//                                        'plannedNumberofPagesSum' => $plannedNumberofPagesSum,
//                                        'stId' => $studentDetails->id,
                                    'percentage' => min($actual_percentage, $expected_percentage)
                                ];
                            }


                        }


                    }

                    $totalPercentageOfRevisionAchievement = array_sum(array_column($percentageOfRevisionAchievementForEachStudent, 'percentage'));
//                    if ($classDetails->revision_plans_count != 0) {
                    if (count($percentageOfRevisionAchievementForEachStudent) > 0) {
                        $totalPercentageOfRevisionAchievement = round($totalPercentageOfRevisionAchievement / count($percentageOfRevisionAchievementForEachStudent), 2);
                    } else {
                        // Handle the case where the denominator is zero
                                                $totalPercentageOfRevisionAchievement = 0;

                    }
                    return  '<div class="progress" style="position: relative;">
  <div  class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$totalPercentageOfRevisionAchievement . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $totalPercentageOfRevisionAchievement . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $totalPercentageOfRevisionAchievement . '%</span>
  </div>
</div>';
                    return $totalPercentageOfRevisionAchievement;

                })
                ->rawColumns(['halaqahsCount', 'revisedPages', 'centersCount', 'studentsCount', 'revisionAchievementComparedtoRevisionPlan', 'supervisor','attendanceDaysPercentage'])

                ->
                make(true);
        } catch
        (\Exception $e) {
            // Handle the exception

            dd($e->getMessage());
            \Log::error($e->getMessage());
            return response()->json(['error' => 'An error occurred while loading the data for the table.'], 500);
        }
//    }
    }

}

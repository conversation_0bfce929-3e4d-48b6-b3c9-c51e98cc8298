<?php

namespace Modules\EducationalReports\Http\Controllers;

use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\HefzLevel;
use App\MoshafJuz;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStaatisticsRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class HalaqahReportsController extends Controller
{

    public function __construct()
    {
//        $this->middleware('writeCurrentClassReportOnly', ['only' => ['create']]);
    }


    public function getReport(Request $request)
    {
        if ($request->ajax()) {

            DB::connection()->enableQueryLog();
            try {
                if (request()->filled('classId')) {
                    $students = Student::whereHas('joint_classes', function ($query) {
                        $query->where('class_id', request('classId'));
                    })->has('hefz')->with('joint_classes.programs')->with('hefz')->with('joint_classes.hefz_plans')->with('revision')->select();
                } else {
                    $students = Student::has('joint_classes')->has('hefz')->with('joint_classes.programs')->with('hefz')->with('joint_classes.hefz_plans')->with('revision')->select();
                }

                // Apply student filter if provided
                if (request()->filled('studentId')) {
                    $studentIds = request('studentId');
                    if (is_array($studentIds)) {
                        $students->whereIn('id', $studentIds);
                    } else {
                        $students->where('id', $studentIds);
                    }
                }



                return \Yajra\DataTables\DataTables::of($students)

                    ->addIndexColumn()
                    ->addColumn('name', function ($studentDetails) use ($request) {

                        return $studentDetails->full_name;

                    })
                    ->addColumn('age', function ($studentDetails) use ($request) {


                        return $studentDetails->age;
                    })
                    ->addColumn('nationality', function ($studentDetails) use ($request) {

                        return $studentDetails->nationality;


                    })
                    ->addColumn('program', function ($studentDetails) use ($request) {


                        $program = $studentDetails->joint_classes->map(function ($classes) {

                            return $classes->programs->first()->title;
                        });

                        return $program->unique()[0];


                    })
                    ->addColumn('lastSurat', function ($studentDetails) use ($request) { // get the last surat for hefz and revision



                        // TODO: add the total count of ayats memorized so far for surats that are not same
//                        $queryforHefzHayatCoun = $studentDetails->hefz->filter(function ($value, $key){
//
//
//
//                            return (!is_null($value->hefz_from_ayat) &&  !is_null($value->hefz_to_ayat));
//
//
//                        })->reduce(function ($carry,$item){
//
//
//
//                              $itemCount = abs($item->hefz_from_ayat-$item->hefz_to_ayat);
//
////                            if($item->hefz_from_surat == $item->hefz_to_surat){
////                                $itemCount = abs($item->hefz_from_ayat-$item->hefz_to_ayat);
////
////                            }else{
////
////
////
////                            }
//
//                            return $carry+$itemCount;
//                        },0);


                        $surats = MoshafSurah::all();
                        $revisionLastSurat = '';
                        foreach ($surats as $key => $surat) {

                            if ($studentDetails->hefz->last()->hefz_to_surat == $surat->id) {
                                $hefzLastSurat = $surat->name;
                            }

                        }


                        $lastSurats = '';
                        $lastSurats .= is_null($hefzLastSurat) ? '' : '<span class="badge badge-primary" ><code style="color: white">' . $hefzLastSurat . ' </code></span><br>';

                        return $lastSurats;
                    })
                    ->addColumn('noOfJuz', function ($studentDetails) use ($request) { // number of juz


                        ini_set('memory_limit', '800M');
                        $pageCount = 0;
                        $juz = [];
                        foreach ($studentDetails->hefz as $key => $reports) {

                            $numberofPages = DB::select(DB::raw("select *,abs(first_page-last_page) as pageCount
         from
         (select id,page_number as first_page
                   from moshaf_pages
                   where (surah_id = :startSurahId and first_ayah <= :startAyah)
                   order by page_number desc
                   limit 1) A
                  INNER JOIN
    (select id,page_number as last_page
                   from moshaf_pages
                   where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                   order by page_number desc
                   limit 1) B
on A.id <> B.id"), array(
                                'startSurahId' => $reports->hefz_from_surat,
                                'startAyah' => $reports->hefz_from_ayat,
                                'lastSurahId' => $reports->hefz_to_surat,
                                'lastAyah' => $reports->hefz_to_ayat,
                                'lastAyah2' => $reports->hefz_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                            ));


                            $juz[$reports->from_surat_juz_id] = $reports->from_surat_juz_id;
                            $juz[$reports->to_surat_juz_id] = $reports->to_surat_juz_id;

                            $pageCount += count(range($numberofPages[0]->first_page, $numberofPages[0]->last_page));


                        }

                        $juz = Arr::sort($juz);
                        $juz = implode(',', $juz);

                        $pageCount = $pageCount / 20;


                        // A standard quran chapter( Sephara) contains almost 20 pages and we consider that here according to the top management decision ( Dr Nashwan)
                        return '<span class="ui icon" data-toggle="tooltip"  title="' . $juz . '"  data-placement="right" >' . $pageCount . '</span>
                       <hr><span  class="badge badge-info">' . number_format(($pageCount * 100) / 604, 2) . '% of Quran</span>';

                    })
                    ->addColumn('level', function ($studentDetails) use ($request) {


                        return HefzLevel::where('surah_id', '=', $studentDetails->latest_hefz->hefz_to_surat)->first()->level;


                    })
                    ->rawColumns(['noOfJuz', 'lastSurat'])
                    ->make(true);
            } catch (\Exception $e) {


                dd($e->getMessage());
                return response()->json($e->getMessage());
            }
        }
        $classes = Classes::all();
        return view('educationalreports::halaqah.halaqah_report', compact('classes'));


    }

    public function hefzLevelsRecordsStatistics(Request $request)
    {



        try {

            $levelOneCount = 0;
            $levelTwoCount = 0;
            $levelThreeCount = 0;
            $levelFourCount = 0;
            $levelFiveCount = 0;
            $levelSixCount = 0;
            $altamhedee = 0;
            $hafiz = 0;
            if ($request->filled('class_id')) {

                $hefzReports = Student::whereHas('class', function ($q) use ($request) {
                    $q->where('class_id', $request->get('class_id'));
                })->whereHas('lastReportedSurat')->with('lastReportedSurat.hefzLevel')->get();
            }
                else{

                    $hefzReports = Student::whereHas('class')->whereHas('lastReportedSurat')->with('lastReportedSurat.hefzLevel')->get();

                }






            foreach ($hefzReports as $student) {


                $levelsCount[] = $student->lastReportedSurat->hefzLevel()->first()->level;


            }

           $hefzLevelCount =  collect($levelsCount)->groupBy(function ($val) {
                return $val;
            });
           $levelOneCount = 0;
                $levelTwoCount = 0;
                $levelThreeCount = 0;
                $levelFourCount = 0;
                $levelFiveCount= 0;
                $levelSixCount = 0;
                $altamhedee = 0;
                $hafiz = 0;
            foreach($hefzLevelCount as $key => $value){

                if($key == 'التمهيدي'){
                    $altamhedee = count($value);
                }
                if($key == 1){
                    $levelOneCount = count($value);
                }
                if($key == 2){
                    $levelTwoCount = count($value);
                }
                if($key == 3){
                    $levelThreeCount = count($value);
                }if($key == 4){
                    $levelFourCount = count($value);
                }
                if($key == 5){
                    $levelFiveCount = count($value);
                }
                if($key == 6){
                    $levelSixCount = count($value);
                }
                if($key == 'Hafiz'){
                    $hafiz = count($value);
                }


            }


            //                ['lastPageNumberMemorized' => $lastPageNumberMemorized, 'attendancePercentage' => $attendancePercentage, 'attendanceDaysCount' => $attendanceDaysCount]);

            return response()->json([

                'levelOneCount' => $levelOneCount,
                'levelTwoCount' => $levelTwoCount,
                'levelThreeCount' => $levelThreeCount,
                'levelFourCount' => $levelFourCount,
                'levelFiveCount' => $levelFiveCount,
                'levelSixCount' => $levelSixCount,
                'التمهيدي' => $altamhedee,
                'Hafiz' => $hafiz
            ]);


        } catch (\Exception $exception) {
            \Log::error($exception);
            return response()->json($exception->getMessage());

        }


        dd('only ajax requests are allowed');


    }


}

<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Translatable;
use App\Scopes\OrganizationScope;


/**
 * App\EvaluationSchema
 *
 * @property int $id
 * @property int $organization_id
 * @property string $type
 * @property string $target
 * @property string $title
 * @property string|null $description
 * @property int $created_by
 * @property string $status
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\EvaluationSchemaOption[] $options
 * @property-read int|null $options_count
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema query()
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema whereTarget($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EvaluationSchema whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class EvaluationSchema extends Model
{
    
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'evaluation_schemas';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = [
            'organization_id',
            'type',
            'target',
            'title',
            'description',
            'created_by',
            'status',
            'program_id'
            ];


    public function program(){
        return $this->belongsTo('App\Program');
    }
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
    }

    public function options(){
        return $this->hasMany('App\EvaluationSchemaOption')->orderBy('id', 'asc');
    }
    
}

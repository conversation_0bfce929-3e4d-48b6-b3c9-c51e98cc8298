<?php

namespace Modules\JobSeeker\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Entities\JobNotificationFailure;
use Carbon\Carbon;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Schema;
use Modules\JobSeeker\Jobs\RetryFailedNotificationJob;
use Modules\JobSeeker\Entities\NotificationFailure;

class JobNotificationMonitoringController extends Controller
{
    /**
     * Display the job notification monitoring dashboard
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        try {
            // Get statistics for queued jobs
            $queuedJobs = DB::table('job_notification_queue')
                ->select('queue', DB::raw('count(*) as total'))
                ->groupBy('queue')
                ->get()
                ->pluck('total', 'queue')
                ->toArray();
            
            // Total queued jobs
            $totalQueuedJobs = array_sum($queuedJobs);
            
            // Get statistics for failed jobs
            $failedJobs = DB::table('job_notification_failed_queue')
                ->count();
            
            // Get recent queue activity (last 50 jobs processed)
            $recentActivity = DB::table('job_notification_queue')
                ->select('id', 'queue', 'payload', 'attempts', 'reserved_at', 'available_at', 'created_at')
                ->orderBy('id', 'desc')
                ->limit(50)
                ->get()
                ->map(function($job) {
                    // Parse the payload to get the job details
                    $payload = json_decode($job->payload, true);
                    $jobName = isset($payload['displayName']) ? $payload['displayName'] : 'Unknown Job';
                    
                    return [
                        'id' => $job->id,
                        'job_name' => $jobName,
                        'queue' => $job->queue,
                        'attempts' => $job->attempts,
                        'status' => $job->reserved_at ? 'processing' : 'pending',
                        'created_at' => Carbon::createFromTimestamp($job->created_at)->format('Y-m-d H:i:s'),
                        'available_at' => Carbon::createFromTimestamp($job->available_at)->format('Y-m-d H:i:s'),
                    ];
                });
            
            // Get recent failed jobs
            $recentFailures = DB::table('job_notification_failed_queue')
                ->select('id', 'connection', 'queue', 'payload', 'exception', 'failed_at')
                ->orderBy('id', 'desc')
                ->limit(20)
                ->get()
                ->map(function($job) {
                    // Parse the payload to get the job details
                    $payload = json_decode($job->payload, true);
                    $jobName = isset($payload['displayName']) ? $payload['displayName'] : 'Unknown Job';
                    
                    return [
                        'id' => $job->id,
                        'job_name' => $jobName,
                        'queue' => $job->queue,
                        'exception' => substr($job->exception, 0, 200) . (strlen($job->exception) > 200 ? '...' : ''),
                        'failed_at' => $job->failed_at,
                    ];
                });
            
            // Get job notification failures from our custom tracking
            $notificationFailures = JobNotificationFailure::with(['setup'])
                ->orderBy('created_at', 'desc')
                ->limit(20)
                ->get();
            
            // Get worker status (check if workers are running)
            $workerStatus = $this->checkWorkerStatus();
            
            // Get queue processing rates (jobs processed per hour)
            $hourlyRate = $this->getProcessingRate(60); // Last hour
            $dailyAvgRate = $this->getProcessingRate(1440); // Last 24 hours
            
            // Get rate limits
            $rateLimits = $this->getRateLimits();
            
            // Check database schema for issues
            $schemaStatus = $this->checkDatabaseSchema();
            
            // Return the view with all the data
            return view('modules.jobseeker.monitoring', compact(
                'queuedJobs',
                'totalQueuedJobs',
                'failedJobs',
                'recentActivity',
                'recentFailures',
                'notificationFailures',
                'workerStatus',
                'hourlyRate',
                'dailyAvgRate',
                'rateLimits',
                'schemaStatus'
            ));
        } catch (\Exception $e) {
            Log::error('Error in job monitoring dashboard', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return view('modules.jobseeker.monitoring', [
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Retry a failed job
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function retryFailedJob(Request $request)
    {
        try {
            $id = $request->input('id');
            
            // Get the failed job from the database
            $failedJob = DB::table('job_notification_failed_queue')
                ->where('id', $id)
                ->first();
            
            if (!$failedJob) {
                return redirect()
                    ->route('jobseeker.monitoring')
                    ->with('error', 'Failed job not found');
            }
            
            // Insert the job back into the queue
            DB::table('job_notification_queue')->insert([
                'queue' => $failedJob->queue,
                'payload' => $failedJob->payload,
                'attempts' => 0,
                'reserved_at' => null,
                'available_at' => time(),
                'created_at' => time()
            ]);
            
            // Delete the failed job
            DB::table('job_notification_failed_queue')
                ->where('id', $id)
                ->delete();
            
            Log::info('Failed job retried', ['id' => $id]);
            
            return redirect()
                ->route('jobseeker.monitoring')
                ->with('success', 'Job has been queued for retry');
        } catch (\Exception $e) {
            Log::error('Error retrying failed job', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()
                ->route('jobseeker.monitoring')
                ->with('error', 'Error retrying job: ' . $e->getMessage());
        }
    }
    
    /**
     * Clear all failed jobs
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearFailedJobs()
    {
        try {
            // Count failed jobs before clearing
            $count = DB::table('job_notification_failed_queue')->count();
            
            // Clear the failed jobs table
            DB::table('job_notification_failed_queue')->truncate();
            
            Log::info('All failed jobs cleared', ['count' => $count]);
            
            return redirect()
                ->route('jobseeker.monitoring')
                ->with('success', "Cleared {$count} failed jobs");
        } catch (\Exception $e) {
            Log::error('Error clearing failed jobs', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()
                ->route('jobseeker.monitoring')
                ->with('error', 'Error clearing failed jobs: ' . $e->getMessage());
        }
    }
    
    /**
     * Retry a notification failure
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function retryNotificationFailure(Request $request)
    {
        try {
            $failure = NotificationFailure::findOrFail($request->id);
            
            // Dispatch retry job
            RetryFailedNotificationJob::dispatch($failure->id)
                ->onQueue('notifications');

            return redirect()
                ->route('jobseeker.monitoring')
                ->with('success', 'Notification has been queued for retry');
        } catch (\Exception $e) {
            return redirect()
                ->route('jobseeker.monitoring')
                ->with('error', 'Error retrying notification: ' . $e->getMessage());
        }
    }
    
    /**
     * Helper method to check if workers are running
     * 
     * @return array
     */
    private function checkWorkerStatus()
    {
        try {
            // In a production environment, we would check supervisor status
            // or use ps to find running queue workers
            
            // For now, we'll check if there are any reserved jobs in the queue
            // which is a proxy for worker activity
            $reservedJobs = DB::table('job_notification_queue')
                ->whereNotNull('reserved_at')
                ->count();
                
            $lastActivity = DB::table('job_notification_queue')
                ->whereNotNull('reserved_at')
                ->max('reserved_at');
                
            if ($lastActivity) {
                $lastActivity = Carbon::createFromTimestamp($lastActivity)->diffForHumans();
            } else {
                $lastActivity = 'Never';
            }
            
            return [
                'running' => $reservedJobs > 0,
                'active_jobs' => $reservedJobs,
                'last_activity' => $lastActivity
            ];
        } catch (\Exception $e) {
            Log::error('Error checking worker status', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'running' => false,
                'active_jobs' => 0,
                'last_activity' => 'Error',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get job processing rate over a period of time
     * 
     * @param int $minutes Minutes to look back
     * @return int Jobs processed per hour
     */
    private function getProcessingRate($minutes)
    {
        try {
            // Get completed jobs in the last X minutes
            $since = Carbon::now()->subMinutes($minutes);
            $sinceTimestamp = $since->timestamp;
            
            // We don't have a completed_at field, so we'll use jobs that were available
            // but are no longer in the queue as a proxy
            $completedCount = DB::table('job_notification_queue')
                ->where('created_at', '>=', $sinceTimestamp)
                ->count();
                
            // Calculate hourly rate
            $hoursElapsed = $minutes / 60;
            if ($hoursElapsed > 0) {
                $hourlyRate = round($completedCount / $hoursElapsed);
            } else {
                $hourlyRate = 0;
            }
            
            return $hourlyRate;
        } catch (\Exception $e) {
            Log::error('Error calculating processing rate', [
                'error' => $e->getMessage(),
                'minutes' => $minutes
            ]);
            
            return 0;
        }
    }
    
    /**
     * Get current rate limits for job notifications
     *
     * @return array
     */
    private function getRateLimits()
    {
        try {
            // These are the keys used in JobsController for rate limiting
            $rateLimitPrefixes = [
                'job_setup_create_' => 'Setup Creation',
                'job_subscription_' => 'Subscription'
            ];
            
            $result = [];
            
            // If using Redis for rate limiting, we can fetch the keys
            if (config('cache.default') === 'redis') {
                $redis = Redis::connection('cache');
                
                foreach ($rateLimitPrefixes as $prefix => $label) {
                    $keys = $redis->keys($prefix . '*');
                    $limits = [];
                    
                    foreach ($keys as $key) {
                        $attempts = $redis->get($key . ':attempts');
                        $availableIn = $redis->ttl($key . ':timer');
                        
                        // Extract user ID or IP from the key
                        $identifier = str_replace($prefix, '', $key);
                        
                        // Check if identifier is numeric (likely a user ID)
                        if (is_numeric($identifier)) {
                            $user = \App\User::find($identifier);
                            $identifierLabel = $user ? "{$user->name} (ID: {$identifier})" : "User ID: {$identifier}";
                        } else {
                            $identifierLabel = "IP: {$identifier}";
                        }
                        
                        $limits[] = [
                            'key' => $key,
                            'identifier' => $identifier,
                            'identifier_label' => $identifierLabel,
                            'attempts' => $attempts ?? 0,
                            'available_in' => $availableIn > 0 ? $availableIn : 0
                        ];
                    }
                    
                    $result[$prefix] = [
                        'label' => $label,
                        'limits' => $limits
                    ];
                }
            } else {
                // For other cache drivers, we can't easily list the rate limits
                // So we'll just return an empty result
                foreach ($rateLimitPrefixes as $prefix => $label) {
                    $result[$prefix] = [
                        'label' => $label,
                        'limits' => []
                    ];
                }
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('Error getting rate limits', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [];
        }
    }

    /**
     * Clear a rate limit
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearRateLimit(Request $request)
    {
        try {
            $key = $request->input('key');
            
            if (empty($key)) {
                return redirect()
                    ->route('jobseeker.monitoring')
                    ->with('error', 'No rate limit key provided');
            }
            
            // Clear the rate limit using Laravel's RateLimiter
            \Illuminate\Support\Facades\RateLimiter::clear($key);
            
            Log::info('Rate limit cleared', ['key' => $key]);
            
            return redirect()
                ->route('jobseeker.monitoring')
                ->with('success', 'Rate limit cleared successfully');
        } catch (\Exception $e) {
            Log::error('Error clearing rate limit', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()
                ->route('jobseeker.monitoring')
                ->with('error', 'Error clearing rate limit: ' . $e->getMessage());
        }
    }

    /**
     * Check database schema for job notification system
     * This will check if all required columns exist and report any issues
     *
     * @return array
     */
    private function checkDatabaseSchema()
    {
        try {
            $issues = [];
            $fixes = [];
            
            // Check job_notification_sent_jobs table for recipient_email column
            $hasRecipientEmail = Schema::hasTable('job_notification_sent_jobs') && 
                                Schema::hasColumn('job_notification_sent_jobs', 'recipient_email');
                                
            if (!$hasRecipientEmail && Schema::hasTable('job_notification_sent_jobs')) {
                $issues[] = [
                    'table' => 'job_notification_sent_jobs',
                    'issue' => 'Missing column: recipient_email',
                    'severity' => 'high',
                    'fix_sql' => "ALTER TABLE `job_notification_sent_jobs` ADD COLUMN `recipient_email` VARCHAR(255) NULL COMMENT 'Email of the recipient';"
                ];
                
                $fixes[] = "ALTER TABLE `job_notification_sent_jobs` ADD COLUMN `recipient_email` VARCHAR(255) NULL COMMENT 'Email of the recipient';";
            }
            
            // Check job_notification_queue table exists
            $hasQueueTable = Schema::hasTable('job_notification_queue');
            if (!$hasQueueTable) {
                $issues[] = [
                    'table' => 'job_notification_queue',
                    'issue' => 'Missing table: job_notification_queue',
                    'severity' => 'high',
                    'fix_sql' => "See README-JOB-NOTIFICATION-QUEUE.md for full SQL to create this table"
                ];
            }
            
            return [
                'has_issues' => count($issues) > 0,
                'issues' => $issues,
                'fixes' => $fixes
            ];
        } catch (\Exception $e) {
            Log::error('Error checking database schema', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'has_issues' => true,
                'issues' => [
                    [
                        'table' => 'N/A',
                        'issue' => 'Error checking database schema: ' . $e->getMessage(),
                        'severity' => 'critical',
                        'fix_sql' => 'N/A'
                    ]
                ],
                'fixes' => []
            ];
        }
    }

    /**
     * Fix database schema issues
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function fixDatabaseSchema(Request $request)
    {
        try {
            $schema = $this->checkDatabaseSchema();
            
            if ($schema['has_issues']) {
                $fixCount = 0;
                
                foreach ($schema['fixes'] as $sql) {
                    DB::statement($sql);
                    $fixCount++;
                    
                    Log::info('Applied database schema fix', [
                        'sql' => $sql
                    ]);
                }
                
                return redirect()
                    ->route('jobseeker.monitoring')
                    ->with('success', "Successfully applied {$fixCount} database fixes. Please refresh the page to see the updated status.");
            } else {
                return redirect()
                    ->route('jobseeker.monitoring')
                    ->with('info', 'No database schema issues found.');
            }
        } catch (\Exception $e) {
            Log::error('Error fixing database schema', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()
                ->route('jobseeker.monitoring')
                ->with('error', 'Error fixing database schema: ' . $e->getMessage());
        }
    }

    public function showDashboard()
    {
        $stats = $this->getMonitoringStats();
        $recentFailures = $this->getRecentFailures();
        $rateLimitStatus = $this->getRateLimitStatus();

        return view('modules.jobseeker.monitoring', [
            'stats' => $stats,
            'recentFailures' => $recentFailures,
            'rateLimitStatus' => $rateLimitStatus
        ]);
    }
}
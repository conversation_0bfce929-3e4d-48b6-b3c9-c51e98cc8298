<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\View as ViewFacade;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Modules\JobSeeker\Entities\EmailSendingLog;
use Modules\JobSeeker\Entities\JobSeekerSetting;
use Modules\JobSeeker\Entities\EmailContentSetting;
use App\Services\EmailService;
use App\Services\CircuitBreakerService;
use Modules\JobSeeker\Services\MissedJobService;
use Modules\JobSeeker\Services\EmailContentManagerService;
use Exception;

/**
 * Email Control Board Controller
 * 
 * Handles the admin dashboard for managing email sending configuration,
 * allowing real-time switching between providers and sending modes.
 */
final class EmailControlBoardController extends Controller
{
    private EmailContentManagerService $contentManager;
    
    public function __construct(EmailContentManagerService $contentManager)
    {
        $this->contentManager = $contentManager;
    }
    /**
     * Display the Email Control Board dashboard
     *
     * @return View
     */
    public function index(): View
    {
        Log::info('EmailControlBoardController: Accessing Email Control Board dashboard');

        // Get current email configuration (new JSON format with fallback to legacy)
        $emailConfig = JobSeekerSetting::getEmailConfiguration();

        $data = [
            'currentConfig' => [
                'mode' => $emailConfig['mode'],
                'providers' => $emailConfig['providers'],
                'primary_provider' => JobSeekerSetting::getPrimaryEmailProvider(),
                'using_json_config' => JobSeekerSetting::isUsingJsonConfiguration(),
                'schema_version' => JobSeekerSetting::getConfigurationSchemaVersion(),
                // NEW: expose current job alerts delivery mode
                'job_alerts_email_delivery_mode' => JobSeekerSetting::getValue('job_alerts_email_delivery_mode', 'queue'),
                // NEW: expose global pause for Job Alerts
                'job_alerts_global_pause' => JobSeekerSetting::getValue('job_alerts_global_pause', 'false'),
                'job_alerts_global_pause_reason' => JobSeekerSetting::getValue('job_alerts_global_pause_reason', ''),
                'job_alerts_global_pause_at' => JobSeekerSetting::getValue('job_alerts_global_pause_at', ''),
            ],
            'gmailConfig' => [
                'host' => JobSeekerSetting::getValue('gmail_host', 'smtp.gmail.com'),
                'port' => JobSeekerSetting::getValue('gmail_port', '587'),
                'encryption' => JobSeekerSetting::getValue('gmail_encryption', 'tls'),
                'username' => JobSeekerSetting::getValue('gmail_username', ''),
                'password' => JobSeekerSetting::getValue('gmail_password', ''),
                'from_email' => JobSeekerSetting::getValue('gmail_from_email', ''),
                'from_name' => JobSeekerSetting::getValue('gmail_from_name', ''),
            ],
            'mailtrapConfig' => [
                'api_key' => JobSeekerSetting::getValue('mailtrap_api_key', ''),
                'from_email' => JobSeekerSetting::getValue('mailtrap_from_email', ''),
                'from_name' => JobSeekerSetting::getValue('mailtrap_from_name', ''),
            ],
            'mailConfig' => [
                'from_email' => JobSeekerSetting::getValue('mail_from_email', '<EMAIL>'),
                'from_name' => JobSeekerSetting::getValue('mail_from_name', 'ITQAN ERP (Local)'),
            ],
            'resendConfig' => [
                'api_key' => JobSeekerSetting::getValue('resend_api_key', ''),
                'from_email' => JobSeekerSetting::getValue('resend_from_email', ''),
                'from_name' => JobSeekerSetting::getValue('resend_from_name', ''),
            ],
            'logMonitoringConfig' => [
                'enabled' => JobSeekerSetting::getValue('log_monitoring_enabled', 'true') === 'true',
                'sensitivity' => JobSeekerSetting::getValue('log_monitoring_sensitivity', 'normal'),
                'keywords' => json_decode(JobSeekerSetting::getValue('log_monitoring_keywords', '[]'), true) ?: [
                    'error', 'exception', 'fatal', 'failed', 'critical', 'emergency', 'unexpected', 'crash', 'timeout'
                ],
                'excluded_patterns' => json_decode(JobSeekerSetting::getValue('log_monitoring_excluded_patterns', '[]'), true) ?: [
                    'legacy_email_', 'transactional outbox', 'Email sent successfully', 
                    'Starting email send process', 'Created in transactional outbox'
                ],
                'alert_recipient' => JobSeekerSetting::getValue('log_monitoring_alert_recipient', '<EMAIL>'),
                'schedule_cron' => JobSeekerSetting::getValue('log_monitoring_schedule_cron', '0 0 */2 * *'),
                'include_info_logs' => JobSeekerSetting::getValue('log_monitoring_include_info_logs', 'false') === 'true',
                'context_analysis' => JobSeekerSetting::getValue('log_monitoring_context_analysis', 'true') === 'true',
            ],
        ];

        return view('modules.jobseeker.admin.email-control-board.index', $data);
    }

    /**
     * Update email configuration settings
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateConfiguration(Request $request): JsonResponse
    {
        // Ensure this is an AJAX request as per Laravel best practices
        if (!$request->ajax()) {
            return response()->json([
                'success' => false,
                'message' => 'This endpoint only accepts AJAX requests.'
            ], 400);
        }

        Log::info('EmailControlBoardController: Updating email configuration', [
            'request_data' => $request->except(['gmail_password', 'mailtrap_api_key'])
        ]);

        try {
            // [NEW] Handle Job Alerts delivery mode toggle (direct|queue) as a simple atomic update
            if ($request->has('job_alerts_email_delivery_mode')) {
                $validator = Validator::make($request->all(), [
                    'job_alerts_email_delivery_mode' => 'required|in:direct,queue',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validation failed for job alerts delivery mode.',
                        'errors' => $validator->errors()
                    ], 422);
                }

                JobSeekerSetting::updateOrCreate(
                    ['key' => 'job_alerts_email_delivery_mode'],
                    ['value' => $request->input('job_alerts_email_delivery_mode')]
                );

                Log::info('EmailControlBoardController: Updated job alerts delivery mode', [
                    'mode' => $request->input('job_alerts_email_delivery_mode')
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Job alerts delivery mode updated successfully.',
                    'job_alerts_email_delivery_mode' => $request->input('job_alerts_email_delivery_mode')
                ]);

            // NEW: Handle Global Job Alerts Pause toggle
            } elseif ($request->has('job_alerts_global_pause')) {
                $validator = Validator::make($request->all(), [
                    'job_alerts_global_pause' => 'required|in:true,false,1,0',
                    'job_alerts_global_pause_reason' => 'nullable|string|max:500',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validation failed for job alerts global pause.',
                        'errors' => $validator->errors()
                    ], 422);
                }

                $pauseValue = $request->input('job_alerts_global_pause');
                $isPaused = ($pauseValue === 'true' || $pauseValue === '1' || $pauseValue === 1 || $pauseValue === true);
                $reason = trim((string)$request->input('job_alerts_global_pause_reason', ''));
                $timestamp = now()->toDateTimeString();

                JobSeekerSetting::updateOrCreate(['key' => 'job_alerts_global_pause'], ['value' => $isPaused ? 'true' : 'false']);
                JobSeekerSetting::updateOrCreate(['key' => 'job_alerts_global_pause_reason'], ['value' => $reason]);
                JobSeekerSetting::updateOrCreate(['key' => 'job_alerts_global_pause_at'], ['value' => $isPaused ? $timestamp : '']);

                Log::warning('EmailControlBoardController: Global Job Alerts pause state changed', [
                    'paused' => $isPaused,
                    'reason' => $reason,
                    'at' => $timestamp,
                    'admin_user' => auth()->user()?->email ?? 'unknown',
                ]);

                return response()->json([
                    'success' => true,
                    'message' => $isPaused ? 'Global Job Alerts paused' : 'Global Job Alerts resumed',
                    'job_alerts_global_pause' => $isPaused ? 'true' : 'false',
                    'job_alerts_global_pause_reason' => $reason,
                    'job_alerts_global_pause_at' => $isPaused ? $timestamp : '',
                ]);

            // UAT FIX #2: Different validation rules for provider priority vs single provider config
            } elseif ($request->has('providers')) {
                // Provider Priority Order Update - UAT FIX #2
                $validator = Validator::make($request->all(), [
                    'providers' => 'required|array|min:1',
                    'providers.*' => 'required|string|in:mail,gmail,mailtrap,resend',
                    'mode' => 'required|in:sync,async',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validation failed for provider priority.',
                        'errors' => $validator->errors()
                    ], 422);
                }

                $providers = $request->input('providers', []);
                $mode = $request->input('mode', 'sync');

                // Validate providers array
                $validProviders = ['gmail', 'mailtrap', 'mail', 'resend'];
                $providers = array_filter($providers, function($provider) use ($validProviders) {
                    return in_array($provider, $validProviders);
                });

                if (empty($providers)) {
                    $providers = ['gmail']; // Default fallback
                }

                // Update using new JSON configuration format
                JobSeekerSetting::setEmailConfiguration($mode, $providers);

                Log::info('EmailControlBoardController: Updated provider priority configuration', [
                    'mode' => $mode,
                    'providers' => $providers
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Provider priority order updated successfully.',
                    'providers' => $providers,
                    'mode' => $mode
                ]);
            } else {
                // Legacy Single Provider Update
                $validator = Validator::make($request->all(), [
                    'provider' => 'required|in:mail,gmail,mailtrap,resend',
                    'mode' => 'required|in:sync,async',
                    // Gmail settings
                    'gmail_host' => 'required_if:provider,gmail|string|max:255',
                    'gmail_port' => 'required_if:provider,gmail|integer|min:1|max:65535',
                    'gmail_encryption' => 'required_if:provider,gmail|in:tls,ssl',
                    'gmail_username' => 'required_if:provider,gmail|email|max:255',
                    'gmail_password' => 'nullable|string|max:255',
                    'gmail_from_email' => 'nullable|email|max:255',
                    'gmail_from_name' => 'nullable|string|max:255',
                    // Mailtrap settings
                    'mailtrap_api_key' => 'nullable|string|max:255',
                    'mailtrap_from_email' => 'nullable|email|max:255',
                    'mailtrap_from_name' => 'nullable|string|max:255',
                    // Mail function settings
                    'mail_from_email' => 'nullable|email|max:255',
                    'mail_from_name' => 'nullable|string|max:255',
                    // Resend settings
                    'resend_api_key' => 'nullable|string|max:255',
                    'resend_from_email' => 'nullable|email|max:255',
                    'resend_from_name' => 'nullable|string|max:255',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validation failed for provider configuration.',
                        'errors' => $validator->errors()
                    ], 422);
                }

                // Legacy single provider update
                JobSeekerSetting::updateOrCreate(
                    ['key' => 'email_provider'],
                    ['value' => $request->input('provider')]
                );

                JobSeekerSetting::updateOrCreate(
                    ['key' => 'email_sending_mode'],
                    ['value' => $request->input('mode')]
                );

                // Update provider-specific settings based on selected provider
                if ($request->input('provider') === 'gmail') {
                    JobSeekerSetting::updateOrCreate(['key' => 'gmail_host'], ['value' => $request->input('gmail_host')]);
                    JobSeekerSetting::updateOrCreate(['key' => 'gmail_port'], ['value' => $request->input('gmail_port')]);
                    JobSeekerSetting::updateOrCreate(['key' => 'gmail_encryption'], ['value' => $request->input('gmail_encryption')]);

                    // Encrypt sensitive credentials before saving
                    if ($request->filled('gmail_username')) {
                        JobSeekerSetting::updateOrCreate(['key' => 'gmail_username'], ['value' => encrypt($request->input('gmail_username'))]);
                    }

                    // Only update password if provided
                    if ($request->filled('gmail_password')) {
                        JobSeekerSetting::updateOrCreate(['key' => 'gmail_password'], ['value' => encrypt($request->input('gmail_password'))]);
                    }

                    if ($request->filled('gmail_from_email')) {
                        JobSeekerSetting::updateOrCreate(['key' => 'gmail_from_email'], ['value' => $request->input('gmail_from_email')]);
                    }

                    if ($request->filled('gmail_from_name')) {
                        JobSeekerSetting::updateOrCreate(['key' => 'gmail_from_name'], ['value' => $request->input('gmail_from_name')]);
                    }
                } elseif ($request->input('provider') === 'mailtrap') {
                    // Only update API key if provided and encrypt it
                    if ($request->filled('mailtrap_api_key')) {
                        JobSeekerSetting::updateOrCreate(['key' => 'mailtrap_api_key'], ['value' => encrypt($request->input('mailtrap_api_key'))]);
                    }

                    if ($request->filled('mailtrap_from_email')) {
                        JobSeekerSetting::updateOrCreate(['key' => 'mailtrap_from_email'], ['value' => $request->input('mailtrap_from_email')]);
                    }

                    if ($request->filled('mailtrap_from_name')) {
                        JobSeekerSetting::updateOrCreate(['key' => 'mailtrap_from_name'], ['value' => $request->input('mailtrap_from_name')]);
                    }
                } elseif ($request->input('provider') === 'mail') {
                    if ($request->filled('mail_from_email')) {
                        JobSeekerSetting::updateOrCreate(['key' => 'mail_from_email'], ['value' => $request->input('mail_from_email')]);
                    }

                    if ($request->filled('mail_from_name')) {
                        JobSeekerSetting::updateOrCreate(['key' => 'mail_from_name'], ['value' => $request->input('mail_from_name')]);
                    }
                } elseif ($request->input('provider') === 'resend') {
                    // Only update API key if provided and encrypt it
                    if ($request->filled('resend_api_key')) {
                        JobSeekerSetting::updateOrCreate(['key' => 'resend_api_key'], ['value' => encrypt($request->input('resend_api_key'))]);
                    }

                    if ($request->filled('resend_from_email')) {
                        JobSeekerSetting::updateOrCreate(['key' => 'resend_from_email'], ['value' => $request->input('resend_from_email')]);
                    }

                    if ($request->filled('resend_from_name')) {
                        JobSeekerSetting::updateOrCreate(['key' => 'resend_from_name'], ['value' => $request->input('resend_from_name')]);
                    }
                }

                Log::info('EmailControlBoardController: Single provider configuration updated successfully', [
                    'provider' => $request->input('provider'),
                    'mode' => $request->input('mode')
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Configuration updated successfully.'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('EmailControlBoardController: Failed to update configuration', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update configuration: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send a test email to verify current configuration
     * UAT Change Request #4: Fixed provider priority handling
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function sendTestEmail(Request $request): JsonResponse
    {
        // Ensure this is an AJAX request as per Laravel best practices
        if (!$request->ajax()) {
            return response()->json([
                'success' => false,
                'message' => 'This endpoint only accepts AJAX requests.'
            ], 400);
        }

        Log::info('EmailControlBoardController: Sending test email with provider priority debugging');

        try {
            $validator = Validator::make($request->all(), [
                'recipient' => 'required|email|max:255',
                'force_provider' => 'nullable|string|in:gmail,mailtrap,mail,resend',
                'debug_priority' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid email address.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $recipientEmail = $request->input('recipient');
            $forceProvider = $request->input('force_provider');
            $debugPriority = $request->input('debug_priority', false);

            // Get current email configuration
            $emailConfig = JobSeekerSetting::getEmailConfiguration();
            $currentMode = $emailConfig['mode'];
            $currentProviders = $emailConfig['providers'];
            $primaryProvider = $currentProviders[0] ?? 'gmail';

            Log::info('EmailControlBoardController: Current email configuration for test', [
                'mode' => $currentMode,
                'providers' => $currentProviders,
                'primary_provider' => $primaryProvider,
                'force_provider' => $forceProvider,
                'using_json_config' => JobSeekerSetting::isUsingJsonConfiguration(),
            ]);

            // If a specific provider is forced, temporarily override the configuration
            if ($forceProvider) {
                // Temporarily force a specific provider for the test
                $testProviders = [$forceProvider];
                if ($debugPriority) {
                    Log::info('EmailControlBoardController: Forcing provider for test email', [
                        'forced_provider' => $forceProvider,
                        'original_providers' => $currentProviders,
                    ]);
                }
            } else {
                $testProviders = $currentProviders;
            }

            // Initialize the EmailService with CircuitBreakerService dependency
            $emailService = app(\App\Services\EmailService::class);

            // Prepare test email data with enhanced debugging info
            $subject = 'Email Control Board Test - ' . now()->format('Y-m-d H:i:s');
            $view = 'modules.jobseeker.emails.test-email';
            $viewData = [
                'test_time' => now()->format('Y-m-d H:i:s'),
                'current_provider' => $primaryProvider,
                'current_mode' => $currentMode,
                'provider_priority' => $testProviders,
                'forced_provider' => $forceProvider,
                'debug_enabled' => $debugPriority,
                'schema_version' => JobSeekerSetting::getConfigurationSchemaVersion(),
            ];

            try {
                // For forced provider tests, use a different approach
                if ($forceProvider) {
                    Log::info('EmailControlBoardController: Using direct provider sending for forced test', [
                        'forced_provider' => $forceProvider,
                        'bypassing_normal_flow' => true,
                    ]);

                    // Use direct provider sending, bypassing normal configuration flow
                    $result = $this->sendDirectTestEmail(
                        $forceProvider,
                        $recipientEmail,
                        $subject,
                        $viewData,
                        $view
                    );
                } else {
                    // Send the test email using normal flow
                    $result = $emailService->send(
                        to: $recipientEmail,
                        subject: $subject,
                        body: 'Test email body - Provider Priority Test',
                        viewData: $viewData,
                        view: $view
                    );
                }

                if ($result === true) {
                    Log::info('EmailControlBoardController: Test email sent successfully with provider debugging', [
                        'recipient' => $recipientEmail,
                        'provider_used' => $forceProvider ?: $primaryProvider,
                        'provider_priority' => $testProviders,
                    ]);

                    return response()->json([
                        'success' => true,
                        'message' => 'Test email sent successfully to ' . $recipientEmail,
                        'provider_used' => $forceProvider ?: $primaryProvider,
                        'priority_info' => 'Priority: ' . implode(' → ', $testProviders),
                        'debug_info' => $debugPriority ? 'Provider forced for test: ' . ($forceProvider ?: $primaryProvider) : null,
                    ]);
                } elseif (is_array($result)) {
                    // Async result
                    Log::info('EmailControlBoardController: Test email queued successfully', [
                        'recipient' => $recipientEmail,
                        'result' => $result,
                    ]);

                    return response()->json([
                        'success' => true,
                        'message' => 'Test email queued successfully for ' . $recipientEmail,
                        'provider_used' => $forceProvider ?: $primaryProvider,
                        'priority_info' => 'Priority: ' . implode(' → ', $testProviders),
                        'status' => 'queued',
                        'queue_info' => $result,
                    ]);
                } else {
                    Log::warning('EmailControlBoardController: Test email failed to send', [
                        'recipient' => $recipientEmail,
                        'result' => $result,
                        'provider_priority' => $testProviders,
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to send test email. Please check your configuration.',
                        'debug_info' => $debugPriority ? 'Provider attempted: ' . ($forceProvider ?: $primaryProvider) : null,
                    ], 500);
                }
            } catch (\Exception $e) {
                throw $e; // Re-throw to be handled by outer catch block
            }

        } catch (\Exception $e) {
            Log::error('EmailControlBoardController: Exception during test email sending', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'recipient' => $recipientEmail ?? 'unknown',
                'force_provider' => $forceProvider ?? null,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage(),
                'debug_info' => $debugPriority ? 'Error during provider priority test' : null,
            ], 500);
        }
    }

    /**
     * Send test email directly using specified provider (bypassing normal priority logic)
     * UAT FIX: Ensures forced provider is actually used for testing
     *
     * @param string $provider
     * @param string $recipient
     * @param string $subject
     * @param array $viewData
     * @param string $view
     * @return bool|array
     */
    private function sendDirectTestEmail(
        string $provider,
        string $recipient,
        string $subject,
        array $viewData,
        string $view
    ): bool|array {
        Log::info('EmailControlBoardController: Sending direct test email', [
            'provider' => $provider,
            'recipient' => $recipient,
            'bypassing_normal_priority' => true,
        ]);

        try {
            // Create outgoing email entry for audit trail
            $outgoingEmail = \Modules\JobSeeker\Entities\OutgoingEmail::createInOutbox(
                $recipient,
                $subject,
                'Test email body - Direct Provider Test',
                $provider,
                'sync_forced_test',
                [
                    'view_data' => $viewData,
                    'view' => $view,
                    'correlation_id' => 'direct_test_' . uniqid(),
                    'forced_provider' => $provider,
                    'test_type' => 'direct_provider_test',
                ]
            );

            // Generate email content
            $htmlContent = ViewFacade::exists($view) ? 
                ViewFacade::make($view, $viewData)->render() : 
                'Test email content - Provider: ' . $provider . ' - Sent at: ' . now()->format('Y-m-d H:i:s');

            // Use the appropriate provider directly
            $success = match ($provider) {
                'mail' => $this->sendTestViaMailFunction($recipient, $subject, $htmlContent),
                'gmail' => $this->sendTestViaGmail($recipient, $subject, $htmlContent),
                'mailtrap' => $this->sendTestViaMailtrap($recipient, $subject, $htmlContent),
                'resend' => $this->sendTestViaResend($recipient, $subject, $htmlContent),
                default => throw new \Exception("Unsupported provider for direct test: {$provider}")
            };

            if ($success) {
                $outgoingEmail->markAsSent();

                // Create log entry for backward compatibility
                EmailSendingLog::create([
                    'recipient' => $recipient,
                    'subject' => $subject,
                    'provider' => $provider,
                    'mode' => 'sync_forced_test',
                    'status' => 'success',
                    'error_message' => null,
                ]);

                Log::info('EmailControlBoardController: Direct test email sent successfully', [
                    'provider' => $provider,
                    'recipient' => $recipient,
                    'outgoing_email_id' => $outgoingEmail->id,
                ]);

                return true;
            } else {
                $outgoingEmail->markSendFailed('Direct provider test failed');

                EmailSendingLog::create([
                    'recipient' => $recipient,
                    'subject' => $subject,
                    'provider' => $provider,
                    'mode' => 'sync_forced_test',
                    'status' => 'failure',
                    'error_message' => 'Direct provider test failed',
                ]);

                return false;
            }
        } catch (\Exception $e) {
            Log::error('EmailControlBoardController: Direct test email failed', [
                'provider' => $provider,
                'recipient' => $recipient,
                'error' => $e->getMessage(),
            ]);

            if (isset($outgoingEmail)) {
                $outgoingEmail->markSendFailed($e->getMessage());
            }

            throw $e;
        }
    }

    /**
     * Send test email via PHP mail() function directly
     * UAT FIX #1: Enhanced error handling and debugging for mail function failures
     */
    private function sendTestViaMailFunction(string $recipient, string $subject, string $htmlContent): bool
    {
        Log::info('EmailControlBoardController: Sending test via PHP mail() function', [
            'recipient' => $recipient,
            'method' => 'mail_function_direct',
            'php_version' => phpversion(),
            'environment' => app()->environment(),
        ]);

        $fromEmail = JobSeekerSetting::getValue('mail_from_email', env('MAIL_FROM_ADDRESS', '<EMAIL>'));
        $fromName = JobSeekerSetting::getValue('mail_from_name', env('MAIL_FROM_NAME', 'ITQAN ERP (Local)'));

        // UAT FIX #1: Validate email configuration before sending
        if (empty($fromEmail) || !filter_var($fromEmail, FILTER_VALIDATE_EMAIL)) {
            $error = 'Invalid or missing from email address: ' . ($fromEmail ?: 'empty');
            Log::error('EmailControlBoardController: Mail function validation failed', [
                'error' => $error,
                'from_email' => $fromEmail,
                'from_name' => $fromName,
            ]);
            throw new \Exception($error);
        }

        // UAT FIX #1: Enhanced headers with more debugging information
        $headers = [
            "From: {$fromName} <{$fromEmail}>",
            "Reply-To: {$fromEmail}",
            "X-Mailer: PHP/" . phpversion(),
            "X-Test-Provider: mail-function-forced",
            "X-Laravel-Environment: " . app()->environment(),
            "X-Test-Timestamp: " . now()->toISOString(),
            "MIME-Version: 1.0",
            "Content-Type: text/html; charset=UTF-8",
        ];

        // UAT FIX #1: Enhanced content with debugging information
        $debugContent = $htmlContent . "<hr style='margin: 20px 0; border: 1px solid #ccc;'>";
        $debugContent .= "<small style='color: #666; font-size: 12px;'>";
        $debugContent .= "<strong>Debug Information:</strong><br>";
        $debugContent .= "PHP Version: " . phpversion() . "<br>";
        $debugContent .= "Laravel Environment: " . app()->environment() . "<br>";
        $debugContent .= "From Email: {$fromEmail}<br>";
        $debugContent .= "From Name: {$fromName}<br>";
        $debugContent .= "Sent At: " . now()->format('Y-m-d H:i:s T') . "<br>";
        $debugContent .= "Server: " . ($_SERVER['SERVER_NAME'] ?? 'unknown') . "<br>";

        // Check for common mail function requirements
        if (!function_exists('mail')) {
            $debugContent .= "<span style='color: red;'>ERROR: mail() function is not available!</span><br>";
        } elseif (ini_get('sendmail_path') === '') {
            $debugContent .= "<span style='color: orange;'>WARNING: sendmail_path is not configured</span><br>";
        } else {
            $debugContent .= "Sendmail Path: " . ini_get('sendmail_path') . "<br>";
        }

        $debugContent .= "</small>";

        // UAT FIX #1: Check if mail() function exists
        if (!function_exists('mail')) {
            $error = 'PHP mail() function is not available on this server';
            Log::error('EmailControlBoardController: Mail function not available', [
                'error' => $error,
                'recipient' => $recipient,
            ]);
            throw new \Exception($error);
        }

        // UAT FIX #1: Clear any previous errors before calling mail()
        error_clear_last();

        Log::info('EmailControlBoardController: Attempting to send mail with enhanced debugging', [
            'recipient' => $recipient,
            'subject' => $subject,
            'from_email' => $fromEmail,
            'from_name' => $fromName,
            'content_length' => strlen($debugContent),
            'headers_count' => count($headers),
        ]);

        $sent = mail($recipient, $subject, $debugContent, implode("\r\n", $headers));

        if ($sent) {
            Log::info('EmailControlBoardController: Mail function test successful', [
                'recipient' => $recipient,
                'subject' => $subject,
                'headers_sent' => $headers,
                'mail_success' => true,
            ]);
        } else {
            $error = error_get_last();
            $errorMessage = 'PHP mail() function failed';

            if ($error) {
                $errorMessage .= ': ' . $error['message'];
            }

            // UAT FIX #1: Enhanced error logging with system diagnostics
            Log::error('EmailControlBoardController: Mail function test failed', [
                'recipient' => $recipient,
                'subject' => $subject,
                'php_error' => $error ? $error['message'] : 'Unknown mail() error',
                'php_error_type' => $error ? $error['type'] : null,
                'php_error_file' => $error ? $error['file'] : null,
                'php_error_line' => $error ? $error['line'] : null,
                'sendmail_path' => ini_get('sendmail_path'),
                'smtp_server' => ini_get('SMTP'),
                'smtp_port' => ini_get('smtp_port'),
                'environment' => app()->environment(),
                'headers_attempted' => $headers,
            ]);

            throw new \Exception($errorMessage);
        }

        return $sent;
    }

    /**
     * Send test email via Gmail directly
     */
    private function sendTestViaGmail(string $recipient, string $subject, string $htmlContent): bool
    {
        Log::info('EmailControlBoardController: Sending test via Gmail SMTP directly');

        // Get Gmail configuration
        $gmailConfig = [
            'host' => JobSeekerSetting::getValue('gmail_host', env('GMAIL_HOST', 'smtp.gmail.com')),
            'port' => JobSeekerSetting::getValue('gmail_port', env('GMAIL_PORT', 587)),
            'encryption' => JobSeekerSetting::getValue('gmail_encryption', env('GMAIL_ENCRYPTION', 'tls')),
            'username' => $this->safelyDecrypt(JobSeekerSetting::getValue('gmail_username'), env('GMAIL_USERNAME')),
            'password' => $this->safelyDecrypt(JobSeekerSetting::getValue('gmail_password'), env('GMAIL_PASSWORD')),
            'from_email' => JobSeekerSetting::getValue('gmail_from_email', env('MAIL_FROM_ADDRESS')),
            'from_name' => JobSeekerSetting::getValue('gmail_from_name', env('MAIL_FROM_NAME')),
        ];

        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);

        $mail->isSMTP();
        $mail->Host = $gmailConfig['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $gmailConfig['username'];
        $mail->Password = $gmailConfig['password'];
        $mail->SMTPSecure = $gmailConfig['encryption'];
        $mail->Port = (int)$gmailConfig['port'];

        $mail->setFrom($gmailConfig['from_email'], $gmailConfig['from_name']);
        $mail->addAddress($recipient);

        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $htmlContent;
        $mail->addCustomHeader('X-Test-Provider', 'gmail-forced');

        $mail->send();
        return true;
    }

    /**
     * Send test email via Mailtrap directly
     */
    private function sendTestViaMailtrap(string $recipient, string $subject, string $htmlContent): bool
    {
        Log::info('EmailControlBoardController: Sending test via Mailtrap API directly');

        $apiKey = $this->safelyDecrypt(JobSeekerSetting::getValue('mailtrap_api_key'), env('MAILTRAP_API_KEY'));
        $fromEmail = JobSeekerSetting::getValue('mailtrap_from_email', env('MAIL_FROM_ADDRESS'));
        $fromName = JobSeekerSetting::getValue('mailtrap_from_name', env('MAIL_FROM_NAME'));

        if (empty($apiKey)) {
            throw new \Exception('Mailtrap API key is not configured');
        }

        $mailtrap = \Mailtrap\MailtrapClient::initSendingEmails(apiKey: $apiKey);

        $email = (new \Mailtrap\Mime\MailtrapEmail())
            ->from(new \Symfony\Component\Mime\Address($fromEmail, $fromName))
            ->to(new \Symfony\Component\Mime\Address($recipient))
            ->subject($subject)
            ->html($htmlContent);

        $email->getHeaders()->addTextHeader('X-Test-Provider', 'mailtrap-forced');

        $response = $mailtrap->send($email);
        return true;
    }

    /**
     * Send test email via Resend directly
     */
    private function sendTestViaResend(string $recipient, string $subject, string $htmlContent): bool
    {
        Log::info('EmailControlBoardController: Sending test via Resend API directly');

        $apiKey = $this->safelyDecrypt(JobSeekerSetting::getValue('resend_api_key'), env('RESEND_API_KEY'));
        $fromEmail = JobSeekerSetting::getValue('resend_from_email', env('MAIL_FROM_ADDRESS'));
        $fromName = JobSeekerSetting::getValue('resend_from_name', env('MAIL_FROM_NAME'));

        if (empty($apiKey)) {
            throw new \Exception('Resend API key is not configured. Please configure your Resend API key in the Email Control Board settings.');
        }

        if (empty($fromEmail) || !filter_var($fromEmail, FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('Invalid or missing from email address for Resend. Please configure a valid from email address in the Resend configuration section.');
        }

        // Validate that the from email domain is likely verified
        $domain = substr(strrchr($fromEmail, "@"), 1);
        if (in_array($domain, ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'])) {
            throw new \Exception("Resend requires domain verification. You cannot use public email domains like {$domain}. Please use an email address from a domain you own and have verified with Resend.");
        }

        $resend = \Resend\Resend::client($apiKey);

        try {
            $response = $resend->emails->send([
                'from' => $fromName ? "{$fromName} <{$fromEmail}>" : $fromEmail,
                'to' => [$recipient],
                'subject' => $subject,
                'html' => $htmlContent,
                'headers' => [
                    'X-Test-Provider' => 'resend-forced',
                    'X-Test-Timestamp' => now()->toISOString(),
                ],
            ]);

            Log::info('EmailControlBoardController: Resend test email sent successfully', [
                'response_id' => $response->id ?? 'unknown',
                'recipient' => $recipient,
                'from_email' => $fromEmail,
            ]);

            return true;

        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();

            // Enhanced error messages for common Resend issues
            if (str_contains($errorMessage, 'domain') || str_contains($errorMessage, 'verified')) {
                $errorMessage = "Domain verification required: {$errorMessage}. Please verify your domain '{$domain}' in your Resend dashboard before sending emails.";
            } elseif (str_contains($errorMessage, 'from')) {
                $errorMessage = "Invalid from address: {$errorMessage}. Ensure you're using an email address from a domain you own and have verified with Resend.";
            } elseif (str_contains($errorMessage, 'api_key') || str_contains($errorMessage, 'authentication')) {
                $errorMessage = "Authentication failed: {$errorMessage}. Please check your Resend API key configuration.";
            }

            Log::error('EmailControlBoardController: Resend test email failed', [
                'error' => $errorMessage,
                'recipient' => $recipient,
                'from_email' => $fromEmail,
                'domain' => $domain,
            ]);

            throw new \Exception($errorMessage);
        }
    }

    /**
     * Safely decrypt a value if it's encrypted, otherwise return as-is
     */
    private function safelyDecrypt(?string $value, ?string $fallback = null): ?string
    {
        if (empty($value)) {
            return $fallback;
        }

        try {
            // Check if the value looks like an encrypted Laravel value
            $decoded = base64_decode($value, true);
            if ($decoded !== false) {
                $json = json_decode($decoded, true);
                if (is_array($json) && isset($json['iv']) && isset($json['value']) && isset($json['mac'])) {
                    // This looks like an encrypted value, try to decrypt it
                    return decrypt($value);
                }
            }

            // If it doesn't look encrypted, return as-is
            return $value;
        } catch (\Exception $e) {
            // If decryption fails, log the error and return the fallback
            Log::warning('EmailControlBoardController: Failed to decrypt credential', [
                'error' => $e->getMessage(),
                'fallback_used' => !empty($fallback),
            ]);
            return $fallback ?: $value;
        }
    }

    /**
     * Fetch recent email logs
     *
     * @return JsonResponse
     */
    public function fetchLogs(): JsonResponse
    {
        // Ensure this is an AJAX request as per Laravel best practices
        if (!request()->ajax()) {
            return response()->json([
                'success' => false,
                'message' => 'This endpoint only accepts AJAX requests.'
            ], 400);
        }

        try {
            $logs = EmailSendingLog::orderBy('created_at', 'desc')
                ->take(50)
                ->get()
                ->map(function ($log) {
                    return [
                        'id' => $log->id,
                        'recipient' => $log->recipient,
                        'subject' => $log->subject,
                        'provider' => $log->provider,
                        'mode' => $log->mode,
                        'status' => $log->status,
                        'error_message' => $log->error_message,
                        'created_at' => $log->created_at->format('Y-m-d H:i:s'),
                    ];
                });

            return response()->json([
                'success' => true,
                'logs' => $logs
            ]);

        } catch (\Exception $e) {
            Log::error('EmailControlBoardController: Failed to fetch logs', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch logs: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Fetch circuit breaker states for all providers
     */
    public function fetchCircuitBreakerStates(): JsonResponse
    {
        try {
            Log::info('EmailControlBoard: Fetching circuit breaker states');

            $circuitBreakerService = app(\App\Services\CircuitBreakerService::class);
            $states = $circuitBreakerService->getAllStates();

            return response()->json([
                'success' => true,
                'states' => $states,
                'timestamp' => now()->toISOString(),
            ]);

        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to fetch circuit breaker states', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch circuit breaker states: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reset circuit breaker for a specific provider
     */
    public function resetCircuitBreaker(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'provider' => 'required|string|in:gmail,mailtrap,mail,resend,all',
            ]);

            $provider = $request->input('provider');
            $circuitBreakerService = app(\App\Services\CircuitBreakerService::class);

            Log::info('EmailControlBoard: Resetting circuit breaker', [
                'provider' => $provider,
                'admin_user' => auth()->user()?->email ?? 'unknown',
            ]);

            if ($provider === 'all') {
                $resetCount = $circuitBreakerService->resetAll();

                return response()->json([
                    'success' => true,
                    'message' => "Reset all circuit breakers successfully",
                    'reset_count' => $resetCount,
                    'provider' => 'all',
                ]);
            } else {
                $success = $circuitBreakerService->resetProvider($provider);

                if ($success) {
                    return response()->json([
                        'success' => true,
                        'message' => "Circuit breaker for {$provider} reset successfully",
                        'provider' => $provider,
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => "Failed to reset circuit breaker for {$provider}",
                    ], 500);
                }
            }

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to reset circuit breaker', [
                'provider' => $request->input('provider'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to reset circuit breaker: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update circuit breaker configuration for a provider
     */
    public function updateCircuitBreakerConfig(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'provider' => 'required|string|in:gmail,mailtrap,mail',
                'failure_threshold' => 'nullable|integer|min:1|max:20',
                'success_threshold' => 'nullable|integer|min:1|max:10',
                'timeout_duration' => 'nullable|integer|min:60|max:3600', // 1 minute to 1 hour
            ]);

            $provider = $request->input('provider');
            $config = $request->only(['failure_threshold', 'success_threshold', 'timeout_duration']);

            // Remove null values
            $config = array_filter($config, function($value) {
                return $value !== null;
            });

            $circuitBreakerService = app(\App\Services\CircuitBreakerService::class);

            Log::info('EmailControlBoard: Updating circuit breaker configuration', [
                'provider' => $provider,
                'config' => $config,
                'admin_user' => auth()->user()?->email ?? 'unknown',
            ]);

            $success = $circuitBreakerService->updateProviderConfig($provider, $config);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => "Circuit breaker configuration for {$provider} updated successfully",
                    'provider' => $provider,
                    'updated_config' => $config,
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => "Failed to update circuit breaker configuration for {$provider}",
                ], 500);
            }

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to update circuit breaker configuration', [
                'provider' => $request->input('provider'),
                'config' => $request->only(['failure_threshold', 'success_threshold', 'timeout_duration']),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update circuit breaker configuration: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Toggle emergency pause for all email sending
     */
    public function toggleEmergencyPause(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'paused' => 'required|boolean',
            ]);

            $paused = $request->boolean('paused');
            $currentUser = auth()->user()?->email ?? 'unknown';

            Log::warning('EmailControlBoard: Emergency pause toggle requested', [
                'requested_state' => $paused ? 'PAUSED' : 'ACTIVE',
                'admin_user' => $currentUser,
                'timestamp' => now()->toISOString(),
            ]);

            // Update the setting
            JobSeekerSetting::setValue('mail_sending_paused', $paused ? 'true' : 'false');

            $message = $paused 
                ? '🚨 EMERGENCY: All email sending has been PAUSED'
                : '✅ Email sending has been RESUMED';

            if ($paused) {
                Log::critical('EmailControlBoard: EMERGENCY MAIL PAUSE ACTIVATED', [
                    'admin_user' => $currentUser,
                    'timestamp' => now()->toISOString(),
                ]);
            } else {
                Log::info('EmailControlBoard: Email sending resumed', [
                    'admin_user' => $currentUser,
                    'timestamp' => now()->toISOString(),
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'paused' => $paused,
                'admin_user' => $currentUser,
                'timestamp' => now()->toISOString(),
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to toggle emergency pause', [
                'requested_state' => $request->input('paused'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle emergency pause: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get current emergency pause status
     */
    public function getEmergencyPauseStatus(): JsonResponse
    {
        try {
            $paused = JobSeekerSetting::getValue('mail_sending_paused', 'false');
            $isPaused = $paused === 'true' || $paused === true;

            return response()->json([
                'success' => true,
                'paused' => $isPaused,
                'timestamp' => now()->toISOString(),
            ]);

        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to get emergency pause status', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get emergency pause status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get current Job Alerts global pause status (JobSeeker job alert emails only)
     */
    public function getJobAlertsPauseStatus(): JsonResponse
    {
        try {
            $paused = JobSeekerSetting::getValue('job_alerts_global_pause', 'false');
            $reason = JobSeekerSetting::getValue('job_alerts_global_pause_reason', '');
            $at = JobSeekerSetting::getValue('job_alerts_global_pause_at', '');
            $isPaused = ($paused === 'true' || $paused === true);

            return response()->json([
                'success' => true,
                'paused' => $isPaused,
                'reason' => $reason,
                'at' => $at,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to get job alerts pause status', [
                'error' => $e->getMessage(),
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to get job alerts pause status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get recovery statistics for Epic 8 dashboard
     */
    public function getRecoveryStats(): JsonResponse
    {
        try {
            $stats = DB::table('outgoing_emails')->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN status = "sent" THEN 1 ELSE 0 END) as sent,
                SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed,
                SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = "retry_scheduled" THEN 1 ELSE 0 END) as retry_scheduled,
                AVG(send_attempts) as avg_attempts
            ')->first();

            return response()->json([
                'success' => true,
                'stats' => $stats,
            ]);
        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to get recovery stats', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Analyze failed emails for Epic 8 recovery
     */
    public function analyzeFailedEmails(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'provider' => 'nullable|string|in:gmail,mailtrap,mail',
                'limit' => 'nullable|integer|min:1|max:1000',
            ]);

            $provider = $request->input('provider');
            $limit = $request->input('limit', 100);

            // Run the recovery command analyze action via Artisan
            $command = "jobseeker:email-recovery analyze --limit={$limit}";
            if ($provider) {
                $command .= " --provider={$provider}";
            }

            Artisan::call($command);
            $output = Artisan::output();

            return response()->json([
                'success' => true,
                'message' => 'Analysis completed successfully',
                'output' => $output,
            ]);
        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to analyze failed emails', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Retry failed emails for Epic 8 recovery
     */
    public function retryFailedEmails(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'provider' => 'nullable|string|in:gmail,mailtrap,mail',
                'limit' => 'nullable|integer|min:1|max:100',
                'dry_run' => 'nullable|boolean',
            ]);

            $provider = $request->input('provider');
            $limit = $request->input('limit', 10);
            $dryRun = $request->boolean('dry_run', true);

            $command = "jobseeker:email-recovery retry --limit={$limit}";
            if ($provider) {
                $command .= " --provider={$provider}";
            }
            if ($dryRun) {
                $command .= " --dry-run";
            }

            Artisan::call($command);
            $output = Artisan::output();

            return response()->json([
                'success' => true,
                'message' => $dryRun ? 'Dry run completed' : 'Retry operation completed',
                'output' => $output,
            ]);
        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to retry failed emails', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Export emails for Epic 8 recovery
     */
    public function exportEmails(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'format' => 'required|string|in:csv,json',
                'status' => 'nullable|string|in:failed,sent,pending,retry_scheduled',
                'provider' => 'nullable|string|in:gmail,mailtrap,mail',
                'limit' => 'nullable|integer|min:1|max:10000',
            ]);

            $format = $request->input('format');
            $status = $request->input('status');
            $provider = $request->input('provider');
            $limit = $request->input('limit', 1000);

            $command = "jobseeker:email-recovery export --format={$format} --limit={$limit}";
            if ($status) {
                $command .= " --status={$status}";
            }
            if ($provider) {
                $command .= " --provider={$provider}";
            }

            Artisan::call($command);
            $output = Artisan::output();

            return response()->json([
                'success' => true,
                'message' => 'Export completed successfully',
                'output' => $output,
            ]);
        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to export emails', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Reset email attempts for Epic 8 recovery
     */
    public function resetEmailAttempts(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'provider' => 'nullable|string|in:gmail,mailtrap,mail',
                'status' => 'nullable|string|in:failed,retry_scheduled',
            ]);

            $provider = $request->input('provider');
            $status = $request->input('status', 'failed');

            $command = "jobseeker:email-recovery reset-attempts --status={$status}";
            if ($provider) {
                $command .= " --provider={$provider}";
            }

            Artisan::call($command);
            $output = Artisan::output();

            return response()->json([
                'success' => true,
                'message' => 'Reset attempts completed successfully',
                'output' => $output,
            ]);
        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to reset email attempts', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Browse outbox emails for Epic 8 management
     */
    public function browseOutbox(): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'message' => 'Outbox browser endpoint ready',
            ]);
        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to browse outbox', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Get outbox data for Epic 8 datatable
     */
    public function getOutboxData(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'status' => 'nullable|string',
                'provider' => 'nullable|string',
                'start' => 'nullable|integer|min:0',
                'length' => 'nullable|integer|min:1|max:1000',
                'search' => 'nullable|array',
            ]);

            $query = DB::table('outgoing_emails')->select([
                'id', 'recipient', 'subject', 'provider', 'mode', 'status', 
                'send_attempts', 'created_at', 'sent_at', 'failed_at', 'last_error_message'
            ]);

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }
            if ($request->filled('provider')) {
                $query->where('provider', $request->input('provider'));
            }

            // Apply search
            if ($request->filled('search.value')) {
                $searchValue = $request->input('search.value');
                $query->where(function($q) use ($searchValue) {
                    $q->where('recipient', 'like', "%{$searchValue}%")
                      ->orWhere('subject', 'like', "%{$searchValue}%")
                      ->orWhere('status', 'like', "%{$searchValue}%");
                });
            }

            $totalRecords = $query->count();

            // Apply pagination
            $start = $request->input('start', 0);
            $length = $request->input('length', 25);
            $query->offset($start)->limit($length);

            // Order by latest first
            $query->orderBy('created_at', 'desc');

            $data = $query->get()->map(function($email) {
                return [
                    'id' => $email->id,
                    'recipient' => $email->recipient,
                    'subject' => Str::limit($email->subject, 50),
                    'provider' => $email->provider,
                    'mode' => $email->mode,
                    'status' => $email->status,
                    'attempts' => $email->send_attempts,
                    'created_at' => $email->created_at,
                    'sent_at' => $email->sent_at,
                    'failed_at' => $email->failed_at,
                    'error' => $email->last_error_message ? Str::limit($email->last_error_message, 100) : null,
                ];
            });

            return response()->json([
                'draw' => $request->input('draw', 1),
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $totalRecords,
                'data' => $data,
            ]);
        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to get outbox data', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Get outbox statistics for Epic 8 dashboard
     */
    public function getOutboxStatistics(): JsonResponse
    {
        try {
            $stats = DB::table('outgoing_emails')->selectRaw('
                COUNT(*) as total_emails,
                SUM(CASE WHEN status = "sent" THEN 1 ELSE 0 END) as sent_count,
                SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_count,
                SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN status = "retry_scheduled" THEN 1 ELSE 0 END) as retry_count,
                ROUND(AVG(CASE WHEN status = "sent" THEN 100 ELSE 0 END), 2) as success_rate
            ')->first();

            return response()->json([
                'success' => true,
                'statistics' => $stats,
            ]);
        } catch (Exception $e) {
            Log::error('EmailControlBoard: Failed to get outbox statistics', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Update missed job service settings
     */
    public function updateMissedJobSettings(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'missed_job_tracking_enabled' => 'required|boolean',
                'missed_job_catchup_hours' => 'required|integer|min:1|max:168',
                'missed_job_admin_recipients' => 'nullable|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed for missed job settings.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $enabled = $request->boolean('missed_job_tracking_enabled');
            $lookbackHours = $request->integer('missed_job_catchup_hours');
            $adminRecipients = $request->input('missed_job_admin_recipients', '');

            // Validate email addresses if provided
            if (!empty($adminRecipients)) {
                $emails = array_map('trim', explode(',', $adminRecipients));
                foreach ($emails as $email) {
                    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        return response()->json([
                            'success' => false,
                            'message' => "Invalid email address: {$email}",
                        ], 422);
                    }
                }
            }

            // Save settings using MissedJobService
            $missedJobService = app(MissedJobService::class);
            $settings = [
                'missed_job_tracking_enabled' => $enabled,
                'missed_job_catchup_hours' => $lookbackHours,
                'missed_job_admin_recipients' => $adminRecipients,
            ];

            $saved = $missedJobService->saveSettings($settings);

            if ($saved) {
                Log::info('EmailControlBoardController: Missed job settings updated successfully', [
                    'enabled' => $enabled,
                    'lookback_hours' => $lookbackHours,
                    'admin_recipients_count' => count(array_filter(explode(',', $adminRecipients))),
                    'admin_user' => auth()->user()?->email ?? 'unknown',
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Missed job settings updated successfully.',
                    'settings' => $settings
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to save missed job settings.',
                ], 500);
            }

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (Exception $e) {
            Log::error('EmailControlBoardController: Failed to update missed job settings', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update missed job settings: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get current missed job service settings
     */
    public function getMissedJobSettings(): JsonResponse
    {
        try {
            $missedJobService = app(MissedJobService::class);
            $settings = $missedJobService->getSettings();
            
            // Add real-time toggle state for UI verification
            $settings['missed_job_tracking_enabled_realtime'] = $missedJobService->isFeatureEnabledRealTime();
            $settings['cache_state'] = 'db_verified';
            $settings['last_check'] = now()->format('Y-m-d H:i:s');

            return response()->json([
                'success' => true,
                'settings' => $settings,
            ]);

        } catch (Exception $e) {
            Log::error('EmailControlBoardController: Failed to get missed job settings', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get missed job settings: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update notification window (job age days) setting.
     *
     * Controls how many days back to include unsent jobs in consolidated emails.
     * Global setting that applies to all JobSeeker notification setups.
     *
     * @param Request $request Contains job_age_days (1-30)
     * @return JsonResponse Success/failure with validation messages
     */
    public function updateNotificationWindow(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'job_age_days' => 'required|integer|min:1|max:30',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed for notification window setting.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $jobAgeDays = $request->integer('job_age_days');

            // Save using JobSeekerSetting
            $saved = JobSeekerSetting::setValue('job_age_days', $jobAgeDays);

            if ($saved) {
                // Clear any cache to ensure immediate effect
                Cache::forget('jobseeker_notification_window');
                Cache::forget('job_age_days_setting');

                Log::info('EmailControlBoardController: Notification window updated successfully', [
                    'job_age_days' => $jobAgeDays,
                    'admin_user' => auth()->user()?->email ?? 'unknown',
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Notification window updated successfully.',
                    'job_age_days' => $jobAgeDays,
                    'setting_source' => 'db'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to save notification window setting.',
                ], 500);
            }

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (Exception $e) {
            Log::error('EmailControlBoardController: Failed to update notification window', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update notification window: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get current notification window (job age days) setting.
     *
     * @return JsonResponse Current job age days value and metadata
     */
    public function getNotificationWindow(): JsonResponse
    {
        try {
            $jobAgeDays = (int) JobSeekerSetting::getValue('job_age_days', 7);

            return response()->json([
                'success' => true,
                'job_age_days' => $jobAgeDays,
                'default_value' => 7,
                'min_value' => 1,
                'max_value' => 30,
                'last_check' => now()->format('Y-m-d H:i:s'),
            ]);

        } catch (Exception $e) {
            Log::error('EmailControlBoardController: Failed to get notification window', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get notification window: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update log monitoring configuration settings
     *
     * Handles centralized configuration of log monitoring system including
     * sensitivity levels, keyword filtering, exclusion patterns, and alert settings.
     * 
     * @param Request $request Contains log monitoring configuration parameters
     * @return JsonResponse Success/failure status with validation messages
     * 
     * Purpose: Provide real-time control over log monitoring behavior from Email Control Board
     * Side effects: Updates jobseeker_settings table, affects LogMonitorCommand behavior
     * Security/Permissions: Admin-only access via route middleware
     * Errors: Returns validation errors for invalid inputs; logs configuration changes
     */
    public function updateLogMonitoring(Request $request): JsonResponse
    {
        // Ensure this is an AJAX request as per Laravel best practices
        if (!$request->ajax()) {
            return response()->json([
                'success' => false,
                'message' => 'This endpoint only accepts AJAX requests.'
            ], 400);
        }

        try {
            Log::info('EmailControlBoardController: Updating log monitoring configuration', [
                'user_id' => auth()->id(),
                'request_data' => $request->except(['_token']),
            ]);

            // Validate the incoming request with comprehensive rules
            $validator = Validator::make($request->all(), [
                'enabled' => 'required|boolean',
                'sensitivity' => 'required|in:strict,normal,verbose',
                'keywords' => 'required|array|min:1',
                'keywords.*' => 'required|string|max:50|regex:/^[a-z_]+$/',
                'excluded_patterns' => 'nullable|array',
                'excluded_patterns.*' => 'nullable|string|max:100',
                'alert_recipient' => 'required|email|max:255',
                'schedule_cron' => 'required|string|max:50',
                'include_info_logs' => 'required|boolean',
                'context_analysis' => 'required|boolean',
            ], [
                'keywords.*.regex' => 'Keywords must contain only lowercase letters and underscores',
                'sensitivity.in' => 'Sensitivity must be strict, normal, or verbose',
                'schedule_cron.required' => 'Schedule cron expression is required',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $validated = $validator->validated();

            // Validate cron expression format (basic validation)
            if (!preg_match('/^[0-9*\/,-]+\s+[0-9*\/,-]+\s+[0-9*\/,-]+\s+[0-9*\/,-]+\s+[0-9*\/,-]+$/', $validated['schedule_cron'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid cron expression format. Use standard 5-field cron syntax.',
                ], 422);
            }

            // Update settings in database transaction for atomicity
            DB::beginTransaction();

            try {
                $settingsToUpdate = [
                    'log_monitoring_enabled' => $validated['enabled'] ? 'true' : 'false',
                    'log_monitoring_sensitivity' => $validated['sensitivity'],
                    'log_monitoring_keywords' => json_encode(array_values($validated['keywords'])),
                    'log_monitoring_excluded_patterns' => json_encode(array_values($validated['excluded_patterns'] ?? [])),
                    'log_monitoring_alert_recipient' => $validated['alert_recipient'],
                    'log_monitoring_schedule_cron' => $validated['schedule_cron'],
                    'log_monitoring_include_info_logs' => $validated['include_info_logs'] ? 'true' : 'false',
                    'log_monitoring_context_analysis' => $validated['context_analysis'] ? 'true' : 'false',
                ];

                foreach ($settingsToUpdate as $key => $value) {
                    JobSeekerSetting::updateOrCreate(
                        ['key' => $key],
                        [
                            'value' => $value,
                            'updated_at' => now(),
                        ]
                    );
                }

                DB::commit();

                Log::info('EmailControlBoardController: Log monitoring configuration updated successfully', [
                    'user_id' => auth()->id(),
                    'updated_settings' => array_keys($settingsToUpdate),
                    'sensitivity' => $validated['sensitivity'],
                    'enabled' => $validated['enabled'],
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Log monitoring configuration updated successfully',
                    'settings' => [
                        'enabled' => $validated['enabled'],
                        'sensitivity' => $validated['sensitivity'],
                        'keywords_count' => count($validated['keywords']),
                        'excluded_patterns_count' => count($validated['excluded_patterns'] ?? []),
                        'alert_recipient' => $validated['alert_recipient'],
                        'include_info_logs' => $validated['include_info_logs'],
                        'context_analysis' => $validated['context_analysis'],
                    ],
                ]);

            } catch (Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (Exception $e) {
            Log::error('EmailControlBoardController: Failed to update log monitoring configuration', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update log monitoring configuration: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test log monitoring configuration with dry-run mode
     *
     * Validates log monitoring settings by running LogMonitorCommand in dry-run mode
     * to ensure configuration works without sending actual alerts.
     * 
     * @return JsonResponse Test results and validation status
     * 
     * Purpose: Provide safe testing of log monitoring configuration changes
     * Side effects: Executes LogMonitorCommand with --dry-run flag; logs test results
     * Security/Permissions: Admin-only access via route middleware
     * Errors: Returns command execution errors; logs test failures with context
     */
    public function testLogMonitoring(): JsonResponse
    {
        try {
            Log::info('EmailControlBoardController: Testing log monitoring configuration', [
                'user_id' => auth()->id(),
            ]);

            // Execute log monitor command in dry-run mode for today's logs
            $exitCode = Artisan::call('logs:monitor', [
                '--date' => 'today',
                '--dry-run' => true,
            ]);

            $output = Artisan::output();

            Log::info('EmailControlBoardController: Log monitoring test completed', [
                'exit_code' => $exitCode,
                'output_length' => strlen($output),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => $exitCode === 0,
                'message' => $exitCode === 0 ? 'Log monitoring test completed successfully' : 'Log monitoring test failed',
                'exit_code' => $exitCode,
                'output' => $output,
                'test_date' => now()->format('Y-m-d H:i:s'),
            ]);

        } catch (Exception $e) {
            Log::error('EmailControlBoardController: Log monitoring test failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Log monitoring test failed: ' . $e->getMessage(),
                'exit_code' => -1,
            ], 500);
        }
    }

    /**
     * Update aggregation limits configuration (min/max jobs per email) with "off" support
     *
     * @param Request $request Request containing aggregation settings
     * @return JsonResponse Success/failure status
     *
     * Purpose: Manage email aggregation limits including complete disable functionality
     * Side effects: Updates jobseeker_email_content_settings table; clears content manager cache
     * Security/Permissions: Admin-only access via route middleware
     * Errors: Returns validation errors or system failures; logs with context
     */
    public function updateAggregationLimits(Request $request): JsonResponse
    {
        try {
            Log::info('EmailControlBoardController: Updating aggregation limits', [
                'request_data' => $request->all(),
                'user_id' => auth()->id(),
            ]);

            // Extract parameters from request
            $raw = $request->input('formatting_options');
            $opts = is_string($raw) ? json_decode($raw, true) : (array) $raw;
            
            $isEnabled = (bool)($opts['enabled'] ?? true);
            $min = $isEnabled ? (int)($opts['min_jobs_per_email'] ?? 1) : 0;
            $max = $isEnabled ? (int)($opts['max_jobs_per_email'] ?? 20) : 0;
            
            // Validate ranges when enabled
            if ($isEnabled) {
                if ($min < 1) { $min = 1; }
                if ($max < $min) { $max = $min; }
            }

            $setting = EmailContentSetting::firstOrCreate(
                ['field_name' => 'aggregation_prefs'],
                [
                    'display_label' => 'Aggregation Preferences',
                    'field_group' => 'basic',
                    'is_enabled' => true,
                    'display_order' => 999,
                ]
            );

            $setting->formatting_options = [
                'enabled' => $isEnabled,
                'min_jobs_per_email' => $min,
                'max_jobs_per_email' => $max,
            ];
            $setting->save();

            $this->contentManager->clearCache();

            Log::info('EmailControlBoardController: Aggregation limits updated successfully', [
                'enabled' => $isEnabled,
                'min' => $min,
                'max' => $max,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => $isEnabled 
                    ? 'Aggregation limits saved successfully' 
                    : 'Aggregation limits disabled - all jobs will be sent immediately',
                'data' => [
                    'enabled' => $isEnabled,
                    'min' => $min,
                    'max' => $max
                ]
            ])->header('Cache-Control','no-store, no-cache, must-revalidate, max-age=0');

        } catch (\Throwable $e) {
            Log::error('EmailControlBoardController: Failed to save aggregation limits', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to save aggregation limits: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current security settings for login page
     */
    public function getSecuritySettings(): JsonResponse
    {
        try {
            $settings = [
                'captcha_enabled' => JobSeekerSetting::getValue('login_captcha_enabled', 'true') === 'true',
                'rate_limiting_enabled' => JobSeekerSetting::getValue('login_rate_limiting_enabled', 'true') === 'true',
                'account_lockout_enabled' => JobSeekerSetting::getValue('login_account_lockout_enabled', 'true') === 'true',
            ];

            Log::info('EmailControlBoard: Security settings retrieved', [
                'settings' => $settings,
                'admin_user' => auth()->user()?->email ?? 'unknown',
            ]);

            return response()->json([
                'success' => true,
                'settings' => $settings,
            ]);

        } catch (\Throwable $e) {
            Log::error('EmailControlBoard: Failed to retrieve security settings', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'admin_user' => auth()->user()?->email ?? 'unknown',
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve security settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update security settings for login page
     */
    public function updateSecuritySettings(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'captcha_enabled' => 'required|boolean',
                'rate_limiting_enabled' => 'required|boolean',
                'account_lockout_enabled' => 'required|boolean',
            ]);

            $captchaEnabled = $request->boolean('captcha_enabled');
            $rateLimitingEnabled = $request->boolean('rate_limiting_enabled');
            $accountLockoutEnabled = $request->boolean('account_lockout_enabled');
            $currentUser = auth()->user()?->email ?? 'unknown';

            // Log the security change request
            Log::warning('EmailControlBoard: Security settings change requested', [
                'captcha_enabled' => $captchaEnabled,
                'rate_limiting_enabled' => $rateLimitingEnabled,
                'account_lockout_enabled' => $accountLockoutEnabled,
                'admin_user' => $currentUser,
                'timestamp' => now()->toISOString(),
            ]);

            // Update the settings
            JobSeekerSetting::setValue('login_captcha_enabled', $captchaEnabled ? 'true' : 'false');
            JobSeekerSetting::setValue('login_rate_limiting_enabled', $rateLimitingEnabled ? 'true' : 'false');
            JobSeekerSetting::setValue('login_account_lockout_enabled', $accountLockoutEnabled ? 'true' : 'false');

            // Log successful update
            Log::info('EmailControlBoard: Security settings updated successfully', [
                'captcha_enabled' => $captchaEnabled,
                'rate_limiting_enabled' => $rateLimitingEnabled,
                'account_lockout_enabled' => $accountLockoutEnabled,
                'admin_user' => $currentUser,
            ]);

            $message = 'Security settings updated successfully';
            if (!$captchaEnabled || !$rateLimitingEnabled || !$accountLockoutEnabled) {
                $message .= ' - WARNING: Some security features are now disabled';
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'settings' => [
                    'captcha_enabled' => $captchaEnabled,
                    'rate_limiting_enabled' => $rateLimitingEnabled,
                    'account_lockout_enabled' => $accountLockoutEnabled,
                ],
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (\Throwable $e) {
            Log::error('EmailControlBoard: Failed to update security settings', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'admin_user' => auth()->user()?->email ?? 'unknown',
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update security settings: ' . $e->getMessage()
            ], 500);
        }
    }
}

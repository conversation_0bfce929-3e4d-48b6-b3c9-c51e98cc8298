<?php

namespace Modules\Education\Http\Controllers\SpecialPrograms;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Center;
use App\Program;
use App\ProgramLevel;
use App\ProgramSetting;
use App\StudentHefzPlan;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use App\Moshaf;
use App\MoshafPage;
use App\MoshafSurah;
use App\EvaluationSchema;
use App\EvaluationSchemaOption;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Session;

class HefzController extends Controller
{
    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $program = Program::findOrFail($id);

        // return $program->setting['special_program_code'];

        $centers = Center::all();

        return view('education::programs.hefz.show', compact('program', 'centers'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id, Request $request)
    {
        $program = Program::findOrFail($id);

        $hefz_evaluation_schema = EvaluationSchema::where('target', 'hefz')->first();

        $revision_evaluation_schema = EvaluationSchema::where('target', 'revision')->first();

        return view('education::programs.hefz.edit', compact('program', 'revision_evaluation_schema', 'hefz_evaluation_schema'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {
        try {
            DB::beginTransaction();

            $request->organization_id = config('organization_id');

            $requestData = $request->all();

            $requestData['organization_id'] = config('organization_id');

            $program = Program::findOrFail($id);

            $program->update($requestData);

            foreach ($request->translate as $code => $translate) {
                $program->translateOrNew($code)->title = $translate['title'];
                $program->translateOrNew($code)->description = $translate['description'];
            }

            // dd($program);

            $program->save();

            if (isset($request->settings)) {
                foreach ($request->settings as $key => $value) {
                    $setting = ProgramSetting::where('program_id', $program->id)
                        ->where('setting', $key)
                        ->first();
                    if (!$setting) {
                        $setting = new ProgramSetting();
                        $setting->program_id = $program->id;
                        $setting->setting = $key;
                    }
                    $setting->value = $value;

                    $setting->save();
                }
            }


            $hefz_ids = $request->input('hefz_id', []);


// Check if any of the arrays are not empty
            if (!empty($hefz_ids)) {
                $hefz_codes = $request->input('hefz_code', []);
                $hefz_titles = $request->input('hefz_title', []);
                $hefz_descriptions = $request->input('hefz_description', []);
                $hefz_extra_fields = $request->input('hefz_extra_field', []);

                $hefz_evaluation_schema = EvaluationSchema::where('organization_id', config('organization_id'))
                    ->where('target', 'hefz')->first();

                if ($hefz_evaluation_schema) {
                    foreach ($hefz_codes as $key => $code) {

                        $id = $hefz_ids[$key] ?? null;

                        $title = $hefz_titles[$key] ?? null;
                        $description = $hefz_descriptions[$key] ?? null;
                        $extra_field = $hefz_extra_fields[$key] ?? null;

                        // Check if the ID is a "New" placeholder, case-insensitive
                        if (preg_match('/^New\d+$/i', $id)) {
                            // Insert a new record because it's a new placeholder
                            EvaluationSchemaOption::create([
                                'evaluation_schema_id' => $hefz_evaluation_schema->id,
                                'code' => $code,
                                'title' => $title,
                                'description' => $description,
                                'extra_field' => $extra_field
                            ]);
                        } else {

                            // Update or insert for existing records
                            EvaluationSchemaOption::updateOrInsert(
                                [
                                    'id' => $id,
                                ],
                                [
                                    'evaluation_schema_id' => $hefz_evaluation_schema->id,
                                    'code' => $code,
                                    'title' => $title,
                                    'description' => $description,
                                    'extra_field' => $extra_field
                                ]
                            );
                        }
                    }

                    // Update the `updated_at` field for the EvaluationSchema model
                    $hefz_evaluation_schema->touch();
                }
            }


            $revision_ids = $request->input('revision_id', []);


//        dd($revision_ids, $revision_codes, $revision_titles, $revision_descriptions, $revision_extra_fields);
            if (!empty($revision_ids)) {

                $revision_codes = $request->input('revision_code', []);
                $revision_titles = $request->input('revision_title', []);
                $revision_descriptions = $request->input('revision_description', []);
                $revision_extra_fields = $request->input('revision_extra_field', []);
                $revision_evaluation_schema = EvaluationSchema::where('organization_id', config('organization_id'))
                    ->where('target', 'revision')->first();


                if ($revision_evaluation_schema) {
                    foreach ($revision_codes as $key => $code) {
                        $revisionId = $revision_ids[$key] ?? null;
                        $title = $revision_titles[$key] ?? null;
                        $description = $revision_descriptions[$key] ?? null;
                        $extra_field = $revision_extra_fields[$key] ?? null;


                        // Update or insert a new evaluation schema option
                        // Check if the ID is a "New" placeholder, case-insensitive
                        if (preg_match('/^New\d+$/i', $revisionId)) {
                            // Insert a new record because it's a new placeholder
                            EvaluationSchemaOption::create([
                                'evaluation_schema_id' => $revision_evaluation_schema->id,
                                'code' => $code,
                                'title' => $title,
                                'description' => $description,
                                'extra_field' => $extra_field
                            ]);
                        } else {

                            // Update or insert for existing records
                            EvaluationSchemaOption::updateOrInsert(
                                [
                                    'id' => $revisionId,
                                ],
                                [
                                    'evaluation_schema_id' => $revision_evaluation_schema->id,
                                    'code' => $code,
                                    'title' => $title,
                                    'description' => $description,
                                    'extra_field' => $extra_field
                                ]
                            );
                        }
                    }

                    // Update the `updated_at` field for the EvaluationSchema model
                    $revision_evaluation_schema->touch();
                }
            }

            Session::flash('flash_message', 'Program updated!');

            DB::commit();  // Commit the transaction

            return redirect()->route('programs.index');
        } catch (\Exception $e) {
            DB::rollback();  // Rollback the transaction

            // Log the error message
            Log::error('An error occurred while updating the program: ' . $e->getMessage());

            // Flash a failure message to the session
            Session::flash('flash_message', 'An error occurred while updating the program.');

            return redirect()->route('programs.index');
        }
    }

    public function evaluation($data)
    {
        $debug = true;

        $program_id = $data->program->id;

        $students = [];
        foreach ($data->attendace as $attendant) {
            $debug_info = [];
            // dd($this->getNextHefzLession($attendant));
            if ($attendant->attendance != "absent" && $attendant->attendance != "excused") {
                $hefz_plan = StudentHefzPlan::where('student_id', $attendant->student_id)
                    ->where('status', 'active')
                    ->where('start_date', "!=", null)
                    ->where('end_date', "=", null)
                    ->latest()->first();

                if (!$hefz_plan) {
                    continue;
                }


                $latest_hefz_report = StudentHefzReport::where('student_id', $attendant->student_id)
                    ->latest()->first();


                // info
                if ($latest_hefz_report) {
                    $last_hefz_surah = MoshafSurah::find($latest_hefz_report->hefz_to_surat);
                } elseif ($hefz_plan) {
                    $last_hefz_surah = MoshafSurah::find($hefz_plan->start_from_surat);
                } else {
                    continue;
                    // return [];
                }
                // dd($last_hefz_surah);
                // check if last lesson finished a surah


                if ($latest_hefz_report && $latest_hefz_report->hefz_to_ayat == $last_hefz_surah->num_ayat) {
                    // $debug_info['revision_lession']
                    // dd($last_hefz_surah);
                    $reviewed_surah = StudentRevisionReport::where('student_id', $attendant->student_id)
                        ->where('revision_to_surat', $last_hefz_surah->id)
                        ->where('revision_to_ayat', $last_hefz_surah->num_ayat)
                        ->where('revision_type', 'step_surah')
                        ->latest()->first();

                    if (isset($reviewed_surah) && count($reviewed_surah) == 0) {
                        $students[$attendant->student_id]['hefz'] = null;

                        $reviewed_surah_steps = StudentRevisionReport::where('student_id', $attendant->student_id)
                            ->where('revision_to_surat', $last_hefz_surah->id)
                            ->where('revision_type', 'step_surah')
                            ->latest()->first();

                        if ($reviewed_surah_steps) {
                            $from_ayat = $reviewed_surah_steps->revision_to_ayat + 1;
                        } else {
                            $from_ayat = 1;
                        }

                        $students[$attendant->student_id]['revision'] = [
                            'from_surat_name' => $last_hefz_surah->name,
                            'from_surat' => $last_hefz_surah->id,
                            'from_ayat' => $from_ayat,
                            'to_surat_name' => $last_hefz_surah->name,
                            'to_surat' => $last_hefz_surah->id,
                            'to_ayat' => $last_hefz_surah->num_ayat,
                            'revision_type' => 'step_surah',
                        ];
                        $students[$attendant->student_id]['info'] = $attendant;
                        continue;
                    }
                }


                $latest_revision_report = StudentRevisionReport::where('student_id', $attendant->student_id)
                    ->where('revision_type', null)
                    ->latest()->first();

                // info
                if ($latest_hefz_report) {
                    $last_surat = $latest_hefz_report->hefz_to_surat;
                    $last_ayat = $latest_hefz_report->hefz_to_ayat;
                } elseif ($hefz_plan) {
                    $last_surat = $hefz_plan->start_from_surat;
                    $last_ayat = $hefz_plan->start_from_ayat;
                } else {
                    continue;
                    // return [];
                }

                if ($latest_revision_report) {
                    $last_revised_surat = $latest_revision_report->revision_to_surat;
                    $last_revised_ayat = $latest_revision_report->revision_to_ayat;
                } elseif ($hefz_plan->study_direction == 'forward') {
                    $last_revised_surat = 1;
                    $last_revised_ayat = 1;
                } else {
                    $last_revised_surat = 114;
                    $last_revised_ayat = 1;
                }

                $memorization_mood = $hefz_plan->memorization_mood;

                $num_to_memorize = $hefz_plan->num_to_memorize;

                if ($latest_revision_report
                    && isset($data->program->setting['hefz_restricted_to_revision'])
                    && $data->program->setting['hefz_restricted_to_revision'] == 1
                    && $latest_revision_report->result->extra_field
                ) {
                    $students[$attendant->student_id]['hefz'] = null;

                    //if  $latest_hefz_report->result->extra field == 1 , the lesson should be repeated
                } elseif ($latest_hefz_report && $latest_hefz_report->result->extra_field) {
                    if ($latest_hefz_report->hefz_from_surat == 0 && $latest_hefz_report->hefz_to_surat == 0) {
                    }
                    $from_surat_name = MoshafSurah::find($latest_hefz_report->hefz_from_surat)->name;
                    $to_surat_name = MoshafSurah::find($latest_hefz_report->hefz_to_surat)->name;
                    $students[$attendant->student_id]['hefz'] = [
                        'from_surat_name' => $from_surat_name,
                        'from_surat' => $latest_hefz_report->hefz_from_surat,
                        'from_ayat' => $latest_hefz_report->hefz_from_ayat,
                        'to_surat_name' => $to_surat_name,
                        'to_surat' => $latest_hefz_report->hefz_to_surat,
                        'to_ayat' => $latest_hefz_report->hefz_to_ayat,
                    ];
                } else {
                    $students[$attendant->student_id]['hefz'] = $this->getLessionRange($last_surat, $last_ayat, $memorization_mood, $num_to_memorize, $hefz_plan->study_direction);
                }


                $num_pages_to_revise = $this->getNumberOfPagesToRevise($last_surat, $last_ayat, $last_revised_surat, $last_revised_ayat, $hefz_plan->pages_to_revise, $hefz_plan->study_direction);
                // dd($num_pages_to_revise , $last_surat , $last_ayat , $last_revised_surat , $last_revised_ayat, $hefz_plan->pages_to_revise , $hefz_plan->study_direction);

                if ($latest_revision_report && $latest_revision_report->result->extra_field) {
                    if ($latest_revision_report->revision_from_surat == 0 && $latest_revision_report->revision_to_surat == 0) {
                    }
                    $from_surat_name = MoshafSurah::find($latest_revision_report->revision_from_surat)->name;
                    $to_surat_name = MoshafSurah::find($latest_revision_report->revision_to_surat)->name;

                    $revision_lesson = [
                        'from_surat_name' => $from_surat_name,
                        'from_surat' => $latest_revision_report->revision_from_surat,
                        'from_ayat' => $latest_revision_report->revision_from_ayat,
                        'to_surat_name' => $to_surat_name,
                        'to_surat' => $latest_revision_report->revision_to_surat,
                        'to_ayat' => $latest_revision_report->revision_to_ayat,
                    ];
                    $students[$attendant->student_id]['revision'] = $revision_lesson;
                } elseif ($num_pages_to_revise > 1) {
                    $revision_lesson = $this->getLessionRange($last_revised_surat, $last_revised_ayat, 'page', $num_pages_to_revise, $hefz_plan->study_direction);
                    // dd($students[$attendant->student_id]['revision'] ,$last_revised_surat, $last_revised_ayat , 'page' , $num_pages_to_revise , $hefz_plan->study_direction);
                } else {
                    $students[$attendant->student_id]['revision'] = null;
                }

                // Revision should not execed the Hefz surats
                if (isset($revision_lesson)) {
                    if ($hefz_plan->study_direction == 'forward') {
                        if ($revision_lesson['to_surat'] > $students[$attendant->student_id]['hefz']['to_surat']
                            || ($revision_lesson['to_surat'] == $students[$attendant->student_id]['hefz']['to_surat']
                                && $revision_lesson->to_ayat >= $students[$attendant->student_id]['hefz']['from_ayat'])
                        ) {
                            $students[$attendant->student_id]['revision'] = $this->getLessionRange(1, 1, 'page', $num_pages_to_revise, $hefz_plan->study_direction);
                        } else {
                            $students[$attendant->student_id]['revision'] = $revision_lesson;
                        }
                    } else {
                        if ($revision_lesson['to_surat'] < $students[$attendant->student_id]['hefz']['to_surat']
                            || ($revision_lesson['to_surat'] == $students[$attendant->student_id]['hefz']['to_surat']
                                && $revision_lesson['to_ayat'] >= $students[$attendant->student_id]['hefz']['from_ayat'])
                        ) {
                            $students[$attendant->student_id]['revision'] = $this->getLessionRange(114, 1, 'page', $num_pages_to_revise, $hefz_plan->study_direction);
                        } else {
                            $students[$attendant->student_id]['revision'] = $revision_lesson;
                        }
                    }
                }

                if ($debug) {
                    $debug_info['hefz_plan'] = $hefz_plan ? $hefz_plan->toArray() : [];
                    $debug_info['latest_hefz_report'] = $latest_hefz_report ? $latest_hefz_report->toArray() : [];
                    $debug_info['latest_revision_report'] = $latest_revision_report ? $latest_revision_report->toArray() : [];
                    $debug_info['hefz_lession'] = $students[$attendant->student_id]['hefz'];

                    $debug_info['num_pages_to_revise'] = $num_pages_to_revise;
                    $debug_info['revision_lession'] = $students[$attendant->student_id]['revision'];
                }
                // $next_lession = $this->getNextHefzLession($last_surat , $last_ayat , $memorization_mood , $num_to_memorize , $hefz_plan->study_direction);

                // $lessions = [$next_lession];


                // for ($i=1; $i < 30; $i++) {
                //     $lessions[] = $this->getNextHefzLession($lessions[$i-1]['to_surat'] , $lessions[$i-1]['to_ayat'] , $memorization_mood , $num_to_memorize , $hefz_plan->study_direction);
                // }

                // dd($lessions);

                // // $students[$attendant->student_id]['revision'] = $this->getNextHefzLession($attendant);

                $students[$attendant->student_id]['info'] = $attendant;

                $students[$attendant->student_id]['debug'] = $debug_info;


                // // forward
                // $plan = [];

                // $mood = 'page';

                // $study_direction = 'forward';

                // // get current lesson


                // $surah = MoshafSurah::findOrFail($last_surat);

                // if($mood == 'page'){
                //     $current_page = MoshafPage::where("surah_id" , $last_surat)
                //                             ->where("moshaf_id" , 1)
                //                             ->where( "first_ayah" , ">=" , $last_ayat)
                //                             ->where( "last_ayah" , "<=" , $last_ayat)
                //                             ->get();
                //     // if($current_page)

                //     if($study_direction == 'forward'){
                //         for ($i=0; $i < 30; $i++) {
                //             $start_from;

                //             $end_to ;
                //         }
                //     }else{
                //         for ($i=0; $i < 30; $i++) {
                //         }
                //     }
                // }
                // elseif($mood == 'ayat'){
                //     // ayat mood
                //     for ($i=0; $i < 30; $i++) {


                //     }
                // }
            }
        }
        // dd($students);
        return $students;
    }

    private function getPageBySurahAndAyah($surat, $ayat, $mushaf_id = 1)
    {
        return MoshafPage::where("surah_id", $surat)
            ->where("moshaf_id", $mushaf_id)
            ->where("first_ayah", "<=", $ayat)
            ->where("last_ayah", ">=", $ayat)
            ->first();
    }


    private function getNumberOfPagesToRevise($last_hefz_surat, $last_hefz_ayat, $last_revised_surat, $last_revised_ayat, $num_to_revise, $study_direction)
    {
        $last_revised_page = $this->getPageBySurahAndAyah($last_revised_surat, $last_revised_ayat, 1);

        $last_hefz_page = $this->getPageBySurahAndAyah($last_hefz_surat, $last_hefz_ayat, 1);
        if (!$last_hefz_page) {
            return 1;
        }
        if ($study_direction == 'forward') {
            $num_of_memorized_pages = $last_hefz_page->page_number;
        } else {
            $revised_surat = MoshafSurah::findOrFail($last_revised_surat);//+ 1
            $hefz_surat = MoshafSurah::findOrFail($last_hefz_surat);//+ 1
            $num_of_memorized_pages = (604 - MoshafSurah::findOrFail($last_hefz_surat)->pages->first_page) + ($last_hefz_page->page_number - $hefz_surat->pages->first_page);
        }

        return round($num_of_memorized_pages / 20 * $num_to_revise);
    }

    private function getLessionRange($last_surah_id, $last_ayah, $per_page_or_ayat, $num_pages_or_ayat, $direction, $lession_type = 'hefz')
    {
        $last_surah = MoshafSurah::findOrFail($last_surah_id);
        $from_ayah = $last_ayah;
        if ($last_surah->num_ayat >= $last_ayah + 1) {
            $from_ayah++;
        } else {
            if ($direction == "forward" && $last_surah_id < 114) {
                $last_surah_id = $last_surah_id + 1;
            } elseif ($last_surah_id == 114 || $last_surah_id == 1) {
                // do nothing
            } else {
                $last_surah_id = $last_surah_id - 1;
            }
            $from_ayah = 1;
        }

        $from_surah = MoshafSurah::findOrFail($last_surah_id);


        if ($per_page_or_ayat == 'page') {
            $current_page = MoshafPage::where('moshaf_id', 1)
                ->where('surah_id', $from_surah->id)
                ->where('first_ayah', '<=', $from_ayah)
                ->where('last_ayah', '>=', $from_ayah)
                ->first();

            if ($direction == 'backward') {
                $next_page_number = $current_page->page_number - $num_pages_or_ayat + 1;
                $required_surah_id = MoshafPage::where('moshaf_id', 1)
                    ->where('page_number', $next_page_number)
                    ->where('first_ayah', 1)
                    ->min('surah_id');
            } else {
                $next_page_number = $current_page->page_number + $num_pages_or_ayat - 1;
                $required_surah_id = MoshafPage::where('moshaf_id', 1)
                    ->where('page_number', $next_page_number)
                    ->where('first_ayah', 1)
                    ->max('surah_id');
            }

            if ($last_surah_id > 93) {
                $to_page = MoshafPage::where('moshaf_id', 1)
                    ->where('page_number', $next_page_number)
                    ->where('first_ayah', 1)
                    ->where('surah_id', $required_surah_id)
                    ->first();
            } else {
                $to_page = MoshafPage::where('moshaf_id', 1)
                    ->where('surah_id', $from_surah->id)
                    ->where('page_number', $next_page_number)
                    ->first();
            }

            if ($to_page) {
                $to_surah = MoshafSurah::findOrFail($to_page->surah_id);
                $to_ayah = $to_page->last_ayah;
            } else {
                $to_surah = $from_surah;
                $to_ayah = $to_surah->num_ayat;
            }
            // if($direction == 'forward') dd($to_ayah);
        } else {
            dump($last_surah_id, $last_ayah, $per_page_or_ayat, $num_pages_or_ayat, $direction);
        }
        return [
            'from_surat_name' => $from_surah->name,
            'from_surat' => $from_surah->id,
            'from_ayat' => $from_ayah,
            'to_surat_name' => $to_surah->name,
            'to_surat' => $to_surah->id,
            'to_ayat' => $to_ayah,
        ];
    }

    private function getLessionRangeTMPDISABLE($last_surah_id, $last_ayah, $per_page_or_ayat, $num_pages_or_ayat, $direction)
    {
        // echo ('last_surah_id' .$last_surah_id
        // .', last_ayah '. $last_ayah
        // .', per_page_or_ayat '. $per_page_or_ayat
        // .', num_pages_or_ayat '. $num_pages_or_ayat
        // .', direction '. $direction)."<br>";
        $last_surah = MoshafSurah::findOrFail($last_surah_id);

        if ($last_surah->num_ayat >= $last_ayah + 1) {
            $last_ayah++;
        } else {
            if ($direction == "forward" && $last_surah_id < 114) {
                $last_surah_id = $last_surah_id + 1;
            } else {
                $last_surah_id = $last_surah_id - 1;
            }
            $last_ayah = 1;
        }

        $last_page = $this->getPageBySurahAndAyah($last_surah_id, $last_ayah, 1); // 1 moshaf almadinah

        if (!$last_page) {
        }
        // if(!$last_page){
        //     dd($last_surah_id , $last_ayah , 1);
        // }
        if ($last_surah_id < 114) {
            $next_surah_id = $last_surah_id + 1;
        } else {
            $next_surah_id = 114;
        }


        //        $last_surah = MoshafSurah::findOrFail($last_surah_id);

        $next_surah_by_order = MoshafSurah::findOrFail($next_surah_id);

        if ($direction == 'forward') {
            $num_of_previos_pages = $last_page->page_number;

            $next_surah_by_direction = $next_surah_by_order;
        } else {
            // dd($next_surah_by_order , $last_page , $last_surah);
            $num_of_previos_pages = (604 - $next_surah_by_order->pages->first_page) + ($last_page->page_number - $last_surah->pages->first_page);
            $next_surah_by_direction = MoshafSurah::findOrFail($last_surah->id - 1);
        }

        if ($per_page_or_ayat == 'ayat') {
            if ($last_surah->num_ayat >= ($last_ayah + $num_pages_or_ayat)) {
                $from_surat = $last_surat;
                $from_ayat = $last_ayah + 1;

                $to_surat = $last_surat;
                $to_ayat = ($last_ayah + $num_pages_or_ayat);
            } else {
                if ($last_surah->num_ayat >= ($last_ayah + 1)) {
                    $from_surat = $last_surat;
                    $from_ayat = $last_ayah + 1;

                    $remaining_ayat_in_surah = $last_surah->num_ayat - $from_ayat;
                } else {
                    $from_surat = $next_surah_by_direction->id;
                    $from_ayat = 1;

                    $remaining_ayat_in_surah = 0;
                }
                if ($num_pages_or_ayat < ($next_surah_by_direction->num_ayat + $remaining_ayat_in_surah)) {
                    $total_ayat = $next_surah_by_direction->num_ayat + $remaining_ayat_in_surah;

                    while ($total_ayat < $num_pages_or_ayat) {
                        if ($direction == "forward") {
                            $next_surah_by_direction = MoshafSurah::findOrFail($next_surah_by_direction->id + 1);
                        } else {
                            $next_surah_by_direction = MoshafSurah::findOrFail($next_surah_by_direction->id - 1);
                        }
                        $total_ayat += $next_surah_by_direction->num_ayat;
                    }
                    $to_ayat = $next_surah_by_direction->num_ayat - ($total_ayat - $num_pages_or_ayat);
                } else {
                    $to_ayat = $num_pages_or_ayat - ($next_surah_by_direction->num_ayat - $last_ayat);
                }
                $to_surat = $next_surah_by_direction->id;
            }
        } elseif ($per_page_or_ayat == 'page') {
            // dd($last_surah);
            $from_page_number = $this->get_pages_limit($last_page->page_number); // +1

            if ($last_surah->num_ayat >= ($last_ayah + 1)) {
                $from_surat = $last_surah->id;
                $from_ayat = $last_ayah;
            } else {
                if ($direction == 'forward') {
                    $from_surat = $last_surat + 1 < 114 ? $last_surat + 1 : 114;
                    $from_ayat = 1;
                } else {
                    $from_surat = $last_surat - 1 > 0 ? $last_surat - 1 : 1;
                    $from_ayat = 1;
                }
            }

            if ($direction == 'forward') {
                $form_page = MoshafPage::where("page_number", $this->get_pages_limit($from_page_number))->get();

                // $from_surat = $form_page[0]->surah_id;
                // $from_ayat =  $form_page[0]->first_ayah;

                $to_page = MoshafPage::where("page_number", $this->get_pages_limit($from_page_number + $num_pages_or_ayat - 1))->get();

                $to_surat = $to_page->last()->surah_id;
                $to_ayat = $to_page->last()->last_ayah;
            } else {
                if ($from_page_number > 600) {
                }

                // if($last_surah->pages->last_page - $last_surah->pages->first_page < $num_pages_or_ayat){
                //     $next_page = MoshafPage::where("page_number" ,$this->get_pages_limit($from_page_number))->get();
                //     dd($next_page);
                // }
                // if(MoshafPage::where("page_number" ,$this->get_pages_limit($from_page_number))->get()->count() > 1){


                // }

                if ($last_surah->num_ayat > $last_page->last_ayah) {
                    $next_page = MoshafPage::where("page_number", $this->get_pages_limit($from_page_number))->get();
                    // $from_surat = $next_page[0]->surah_id;
                    // $from_ayat =  $next_page[0]->first_ayah;
                } else {
                    // $from_surat = $next_surah_by_direction->id;
                    // $from_ayat = 1;

                    $next_page = MoshafPage::where("page_number", $this->get_pages_limit($next_surah_by_direction->pages->first_page))->get();
                }


                if ($last_surah->pages->last_page >= $from_page_number + $num_pages_or_ayat) {
                    $to_page = MoshafPage::where("page_number", $this->get_pages_limit($from_page_number + $num_pages_or_ayat - 1))->get();

                    $to_surat = $to_page->last()->surah_id;
                    $to_ayat = $to_page->last()->last_ayah;
                } else {
                    $to_surat = $last_surah->id;
                    $to_ayat = $last_surah->num_ayat;
                }

                /* $remaining_pages_in_surah = $last_surah->pages->last_page -  $from_page_number ;
                 // // dd($last_surah->pages->last_page , $from_page_number , $num_pages_or_ayat ,  $next_surah_by_direction->pages->first_page );
                 if(($next_surah_by_direction->pages->last_page - $next_surah_by_direction->pages->first_page ) >= ( $num_pages_or_ayat - $remaining_pages_in_surah)){

                     $to_page = MoshafPage::where("page_number" , $this->get_pages_limit($next_surah_by_direction->pages->first_page + ( $num_pages_or_ayat - ($last_surah->pages->last_page - $from_page_number + 1))))->get();


                 }else{



                     // TEMP

                     $total_pages = $remaining_pages_in_surah;
                     $pages = [];

                     // echo "<pre>";
                     while ($total_pages < $num_pages_or_ayat) {
                         if($direction == "forward"){
                             $next_surah_by_direction = MoshafSurah::findOrFail($next_surah_by_direction->id + 1);
                         }else{
                             $next_surah_by_direction = MoshafSurah::findOrFail($next_surah_by_direction->id - 1);
                         }

                         $pages = array_unique(array_merge($pages , MoshafPage::where('surah_id' , $next_surah_by_direction->id)->get()->pluck('page_number')->toArray()));

                         // if(!isset($pages[$next_surah_by_direction->pages->last_page]) || $pages[$next_surah_by_direction->pages->last_page] < $next_surah_by_direction->pages->last_page - $next_surah_by_direction->pages->first_page ){
                         //     $pages[$next_surah_by_direction->pages->last_page] = $next_surah_by_direction->pages->last_page - $next_surah_by_direction->pages->first_page +1;
                         // }
                         // if(!isset($pages[$next_surah_by_direction->pages->first_page])){
                         //     $pages[$next_surah_by_direction->pages->first_page] = 0;
                         // }
                         $total_pages = count($pages);

                         // $tmp[$next_surah_by_direction->pages->last_page .'-'. $next_surah_by_direction->pages->first_page] = count($next_surah_by_direction->pages);
                         // $total_pages += $next_surah_by_direction->pages->last_page - $next_surah_by_direction->pages->first_page;
                     }
                     // dd($pages);

                     // dd($next_surah_by_direction->pages->last_page - $total_pages + $num_pages_or_ayat, $total_pages , $tmp);
                     $to_page = MoshafPage::where("page_number" , $this->get_pages_limit( $next_surah_by_direction->pages->last_page - $total_pages + $num_pages_or_ayat ))->get();

                 }
                 // NEEEEEED REFACTORING
                }



                $to_surat = $to_page->last()->surah_id;
                $to_ayat =  $to_page->last()->last_ayah; */
            }
        }

        $from_surat_name = MoshafSurah::find($from_surat)->name;
        $to_surat_name = MoshafSurah::find($to_surat)->name;

        return [
            'from_surat_name' => $from_surat_name,
            'from_surat' => $from_surat,
            'from_ayat' => $from_ayat,
            'to_surat_name' => $to_surat_name,
            'to_surat' => $to_surat,
            'to_ayat' => $to_ayat,
        ];
    }

    private function get_pages_limit($page_number)
    {
        $moshaf_pages = 604;

        if ($page_number == 0) {
            return 1;
        } elseif ($page_number > $moshaf_pages) {
            return $moshaf_pages;//$page_number - $moshaf_pages;
        }

        return floor($page_number);
    }

    // temp to be moved to testing module

    public function testStudentPlanForward()
    {
        echo "From Start of the Moshaf - Forward<br>";

        // Hefz: 1 page per class , Revision 1 Page per 20 memorized Page | per page

        $last_hefz_surat = 1;
        $last_hefz_ayat = 1;

        $last_revised_surat = 1;
        $last_revised_ayat = 1;

        $pages_or_ayat = 'page';

        $num_to_memorize_per_class = 1;

        $pages_to_revise_per_juz = 1; // 1 page per 20

        $direction = 'forward';
        $hefz_to_surah = null;
        $lession = 1;

        $lessons = [];

        while ($last_hefz_surat < 114) {
            $lessons['hefz'] = $this->getHefzLesson($last_hefz_surat, $last_hefz_ayat, $pages_or_ayat, $num_to_memorize_per_class, $direction);


            $current_hefz_surat = $lessons['hefz']['to_surat'];
            $current_hefz_ayat = $lessons['hefz']['to_ayat'];

            echo "Hefz Class $lession : From (" . $lessons['hefz']['from_ayat'] . ") " . $lessons['hefz']['from_surat_name'] . " 
                To (" . $lessons['hefz']['to_ayat'] . ") " . $lessons['hefz']['to_surat_name'] . " <br>";


            $num_pages_to_revise = $this->getNumberOfPagesToRevise($last_hefz_surat, $last_hefz_ayat, $last_revised_surat  // no need
                , $last_revised_ayat //no need
                , $pages_to_revise_per_juz, $direction);
            echo "num_pages_to_revise $num_pages_to_revise <br>";

            if ($num_pages_to_revise) {
                $revison_lesson = $this->getRevisionLesson($last_revised_surat, $last_revised_ayat, $last_hefz_surat, $last_hefz_ayat, $pages_or_ayat, $num_pages_to_revise, $direction);

                // forward

                echo "Revision Class $lession : From (" . $revison_lesson['from_ayat'] . ") " . $revison_lesson['from_surat_name'] . " 
                                                    To (" . $revison_lesson['to_ayat'] . ") " . $revison_lesson['to_surat_name'] . " <br><br>";

                $last_revised_surat = $revison_lesson['to_surat'];
                $last_revised_ayat = $revison_lesson['to_ayat'];
            } else {
                echo "No revision<br><br>";
            }

            // $last_revised_surat = $lessons['revision']['to_surat'];
            // $last_revised_ayat = $lessons['revision']['to_ayat'];

            $lession++;

            $last_hefz_surat = $current_hefz_surat;
            $last_hefz_ayat = $current_hefz_ayat;
        }
    }

    public function testStudentPlan()
    {
        echo "From Start of the Moshaf - Backward<br>";

        // Hefz: 1 page per class , Revision 1 Page per 20 memorized Page | per page

        $last_hefz_surat = 114;
        $last_hefz_ayat = 1;

        $last_revised_surat = 114;
        $last_revised_ayat = 1;

        $pages_or_ayat = 'page';

        $num_to_memorize_per_class = 2;

        $pages_to_revise_per_juz = 1; // 1 page per 20

        $direction = 'backward';

        $hefz_to_surah = null;
        $lession = 1;

        $lessons = [];

        while ($last_hefz_surat > 1) {
            $lessons['hefz'] = $this->getHefzLesson($last_hefz_surat, $last_hefz_ayat, $pages_or_ayat, $num_to_memorize_per_class, $direction);


            $current_hefz_surat = $lessons['hefz']['to_surat'];
            $current_hefz_ayat = $lessons['hefz']['to_ayat'];

            echo "Hefz Class $lession : From [" . $lessons['hefz']['from_surat'] . "] (" . $lessons['hefz']['from_ayat'] . ") " . $lessons['hefz']['from_surat_name'] . " 
                To (" . $lessons['hefz']['to_ayat'] . ") " . $lessons['hefz']['to_surat_name'] . " <br>";


            $num_pages_to_revise = $this->getNumberOfPagesToRevise($last_hefz_surat, $last_hefz_ayat, $last_revised_surat  // no need
                , $last_revised_ayat //no need
                , $pages_to_revise_per_juz, $direction);
            /* echo "num_pages_to_revise $num_pages_to_revise <br>";

            if($num_pages_to_revise){

                $revison_lesson = $this->getRevisionLesson($last_revised_surat , $last_revised_ayat , $last_hefz_surat , $last_hefz_ayat , $pages_or_ayat , $num_pages_to_revise
                , $direction );

                // forward

                echo "Revision Class $lession : From (".$revison_lesson['from_ayat'].") ".$revison_lesson['from_surat_name']."
                                                To (".$revison_lesson['to_ayat'].") ".$revison_lesson['to_surat_name']." <br><br>";

                $last_revised_surat = $revison_lesson['to_surat'];
                $last_revised_ayat = $revison_lesson['to_ayat'];
            }else{
                echo "No revision<br><br>";
            } */

            // $last_revised_surat = $lessons['revision']['to_surat'];
            // $last_revised_ayat = $lessons['revision']['to_ayat'];

            $lession++;

            $last_hefz_surat = $current_hefz_surat;

            $last_hefz_ayat = $current_hefz_ayat;
        }
    }


    public function getHefzLesson($last_hefz_surat, $last_hefz_ayat, $pages_or_ayat, $num_to_memorize_per_class, $direction)
    {

        // if($direction == 'forward'){
        return $this->getLessionRange($last_hefz_surat, $last_hefz_ayat, $pages_or_ayat, $num_to_memorize_per_class, $direction);
        // }else{

        // }
    }

    public function getRevisionLesson($last_revised_surat, $last_revised_ayat, $last_hefz_surat, $last_hefz_ayat, $pages_or_ayat, $num_pages_to_revise, $direction)
    {
        if ($direction == 'forward') {
            $starting_surah = 1;
        } else {
            $starting_surah = 114;
        }

        $revison_lesson = $this->getLessionRange($last_revised_surat, $last_revised_ayat, $pages_or_ayat, $num_pages_to_revise, $direction);

        if ($revison_lesson['to_surat'] > $last_hefz_surat || ($revison_lesson['to_surat'] == $last_hefz_surat && $revison_lesson['to_ayat'] >= $last_hefz_ayat)) {

            // get pages diff
            $last_hefz_page = $this->getPageBySurahAndAyah($last_hefz_surat, $last_hefz_ayat);
            $last_revision_page = $this->getPageBySurahAndAyah($last_revised_surat, $last_revised_ayat);

            $remaining_pages = ($last_hefz_page->page_number - $last_revision_page->page_number) ?? 1;

            if ($remaining_pages) {
                echo("remaining_pages" . $remaining_pages);
                // var_dump(  $last_hefz_page->page , $last_revision_page->page);
            }


            $revison_lesson['loop_note']['to_surat'] = $last_hefz_surat;
            $revison_lesson['loop_note']['to_surat_name'] = MoshafSurah::find($last_hefz_surat)->name;
            $revison_lesson['loop_note']['to_ayat'] = $last_hefz_ayat;
            $revison_lesson['loop_note']['remaining_pages'] = $remaining_pages;

            $continue_revision_lesson = $this->getLessionRange($starting_surah, 1, $pages_or_ayat, $remaining_pages, $direction);


            $revison_lesson['to_surat'] = $continue_revision_lesson['to_surat'];
            $revison_lesson['to_surat_name'] = $continue_revision_lesson['to_surat_name'];
            $revison_lesson['to_ayat'] = $continue_revision_lesson['to_ayat'];
            // $revison_lesson['to_surat_name'] =
        }

        return $revison_lesson;
    }


    //  V2

    public function studentsEvaluation($data)
    {
        $debug = true;

        $program_id = $data->program->id;

        $students = [];
        // dd($data->class->students);
        foreach ($data->class->students as $student) {
            $student_id = $student->id;
            $debug_info = [];
            // dd($this->getNextHefzLession($student));
            $hefz_plan = StudentHefzPlan::where('student_id', $student->id)
                ->where('status', 'active')
                ->where('start_date', "!=", null)
                ->where('end_date', "=", null)
                ->latest()->first();

            if (!$hefz_plan) {
                continue;
            }


            $latest_hefz_report = StudentHefzReport::where('student_id', $student_id)
                ->latest()->first();


            // info
            if ($latest_hefz_report) {
                $last_hefz_surah = MoshafSurah::find($latest_hefz_report->hefz_to_surat);
            } elseif ($hefz_plan) {
                $last_hefz_surah = MoshafSurah::find($hefz_plan->start_from_surat);
            } else {
                continue;
                // return [];
            }
            // dd($last_hefz_surah);
            // check if last lesson finished a surah


            if ($latest_hefz_report && $latest_hefz_report->hefz_to_ayat == $last_hefz_surah->num_ayat) {
                // $debug_info['revision_lession']
                // dd($last_hefz_surah);
                $reviewed_surah = StudentRevisionReport::where('student_id', $student_id)
                    ->where('revision_to_surat', $last_hefz_surah->id)
                    ->where('revision_to_ayat', $last_hefz_surah->num_ayat)
                    ->where('revision_type', 'step_surah')
                    ->latest()->first();

                if (isset($reviewed_surah) && $reviewed_surah) { // && count($reviewed_surah) == 0
                    $students[$student_id]['hefz'] = null;

                    $reviewed_surah_steps = StudentRevisionReport::where('student_id', $student_id)
                        ->where('revision_to_surat', $last_hefz_surah->id)
                        ->where('revision_type', 'step_surah')
                        ->latest()->first();

                    if ($reviewed_surah_steps) {
                        $from_ayat = $reviewed_surah_steps->revision_to_ayat + 1;
                    } else {
                        $from_ayat = 1;
                    }

                    $students[$student_id]['revision'] = [
                        'from_surat_name' => $last_hefz_surah->name,
                        'from_surat' => $last_hefz_surah->id,
                        'from_ayat' => $from_ayat,
                        'to_surat_name' => $last_hefz_surah->name,
                        'to_surat' => $last_hefz_surah->id,
                        'to_ayat' => $last_hefz_surah->num_ayat,
                        'revision_type' => 'step_surah',
                    ];
                    continue;
                }
            }


            $latest_revision_report = StudentRevisionReport::where('student_id', $student_id)
                ->where('revision_type', null)
                ->latest()->first();

            // info
            if ($latest_hefz_report) {
                $last_surat = $latest_hefz_report->hefz_to_surat;
                $last_ayat = $latest_hefz_report->hefz_to_ayat;
            } elseif ($hefz_plan) {
                $last_surat = $hefz_plan->start_from_surat;
                $last_ayat = $hefz_plan->start_from_ayat;
            } else {
                continue;
                // return [];
            }

            if ($latest_revision_report) {
                $last_revised_surat = $latest_revision_report->revision_to_surat;
                $last_revised_ayat = $latest_revision_report->revision_to_ayat;
            } elseif ($hefz_plan->study_direction == 'forward') {
                $last_revised_surat = 1;
                $last_revised_ayat = 1;
            } else {
                $last_revised_surat = 114;
                $last_revised_ayat = 1;
            }

            $memorization_mood = $hefz_plan->memorization_mood;

            $num_to_memorize = $hefz_plan->num_to_memorize;

            if ($latest_revision_report
                && isset($data->program->setting['hefz_restricted_to_revision'])
                && $data->program->setting['hefz_restricted_to_revision'] == 1
                && $latest_revision_report->result->extra_field
            ) {
                $students[$student_id]['hefz'] = null;

                //if  $latest_hefz_report->result->extra field == 1 , the lesson should be repeated
            } elseif ($latest_hefz_report && $latest_hefz_report->result->extra_field) {
                if ($latest_hefz_report->hefz_from_surat == 0 && $latest_hefz_report->hefz_to_surat == 0) {
                }
                $from_surat_name = MoshafSurah::find($latest_hefz_report->hefz_from_surat)->name;
                $to_surat_name = MoshafSurah::find($latest_hefz_report->hefz_to_surat)->name;
                $students[$student_id]['hefz'] = [
                    'from_surat_name' => $from_surat_name,
                    'from_surat' => $latest_hefz_report->hefz_from_surat,
                    'from_ayat' => $latest_hefz_report->hefz_from_ayat,
                    'to_surat_name' => $to_surat_name,
                    'to_surat' => $latest_hefz_report->hefz_to_surat,
                    'to_ayat' => $latest_hefz_report->hefz_to_ayat,
                ];
            } else {
                $students[$student_id]['hefz'] = $this->getLessionRange($last_surat, $last_ayat, $memorization_mood, $num_to_memorize, $hefz_plan->study_direction);
            }


            $num_pages_to_revise = $this->getNumberOfPagesToRevise($last_surat, $last_ayat, $last_revised_surat, $last_revised_ayat, $hefz_plan->pages_to_revise, $hefz_plan->study_direction);
            // dd($num_pages_to_revise , $last_surat , $last_ayat , $last_revised_surat , $last_revised_ayat, $hefz_plan->pages_to_revise , $hefz_plan->study_direction);

            if ($latest_revision_report && $latest_revision_report->result->extra_field) {
                if ($latest_revision_report->revision_from_surat == 0 && $latest_revision_report->revision_to_surat == 0) {
                }
                $from_surat_name = MoshafSurah::find($latest_revision_report->revision_from_surat)->name;
                $to_surat_name = MoshafSurah::find($latest_revision_report->revision_to_surat)->name;

                $revision_lesson = [
                    'from_surat_name' => $from_surat_name,
                    'from_surat' => $latest_revision_report->revision_from_surat,
                    'from_ayat' => $latest_revision_report->revision_from_ayat,
                    'to_surat_name' => $to_surat_name,
                    'to_surat' => $latest_revision_report->revision_to_surat,
                    'to_ayat' => $latest_revision_report->revision_to_ayat,
                ];
                $students[$student_id]['revision'] = $revision_lesson;
            } elseif ($num_pages_to_revise > 1) {
                $revision_lesson = $this->getLessionRange($last_revised_surat, $last_revised_ayat, 'page', $num_pages_to_revise, $hefz_plan->study_direction);
                // dd($students[$student_id]['revision'] ,$last_revised_surat, $last_revised_ayat , 'page' , $num_pages_to_revise , $hefz_plan->study_direction);
            } else {
                $students[$student_id]['revision'] = null;
            }

            // Revision should not execed the Hefz surats
            if (isset($revision_lesson)) {
                if ($hefz_plan->study_direction == 'forward') {
                    if ($revision_lesson['to_surat'] > $students[$student_id]['hefz']['to_surat']
                        || ($revision_lesson['to_surat'] == $students[$student_id]['hefz']['to_surat']
                            && $revision_lesson->to_ayat >= $students[$student_id]['hefz']['from_ayat'])
                    ) {
                        $students[$student_id]['revision'] = $this->getLessionRange(1, 1, 'page', $num_pages_to_revise, $hefz_plan->study_direction);
                    } else {
                        $students[$student_id]['revision'] = $revision_lesson;
                    }
                } else {
                    if ($revision_lesson['to_surat'] < $students[$student_id]['hefz']['to_surat']
                        || ($revision_lesson['to_surat'] == $students[$student_id]['hefz']['to_surat']
                            && $revision_lesson['to_ayat'] >= $students[$student_id]['hefz']['from_ayat'])
                    ) {
                        $students[$student_id]['revision'] = $this->getLessionRange(114, 1, 'page', $num_pages_to_revise, $hefz_plan->study_direction);
                    } else {
                        $students[$student_id]['revision'] = $revision_lesson;
                    }
                }
            }

            if ($debug) {
                $debug_info['hefz_plan'] = $hefz_plan ? $hefz_plan->toArray() : [];
                $debug_info['latest_hefz_report'] = $latest_hefz_report ? $latest_hefz_report->toArray() : [];
                $debug_info['latest_revision_report'] = $latest_revision_report ? $latest_revision_report->toArray() : [];
                $debug_info['hefz_lession'] = $students[$student_id]['hefz'];

                $debug_info['num_pages_to_revise'] = $num_pages_to_revise;
                $debug_info['revision_lession'] = $students[$student_id]['revision'];
            }
        }
        return $students;
    }
}

var ComponentsTypeahead=function(){var e=function(){var e=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.num)},queryTokenizer:Bloodhound.tokenizers.whitespace,local:[{num:"metronic"},{num:"keenthemes"},{num:"metronic theme"},{num:"metronic template"},{num:"keenthemes team"}]});e.initialize(),App.isRTL()&&$("#typeahead_example_1").attr("dir","rtl"),$("#typeahead_example_1").typeahead(null,{displayKey:"num",hint:App.isRTL()?!1:!0,source:e.ttAdapter()});var t=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.name)},queryTokenizer:Bloodhound.tokenizers.whitespace,limit:10,prefetch:{url:"../demo/typeahead_countries.json",filter:function(e){return $.map(e,function(e){return{name:e}})}}});t.initialize(),App.isRTL()&&$("#typeahead_example_2").attr("dir","rtl"),$("#typeahead_example_2").typeahead(null,{name:"typeahead_example_2",displayKey:"name",hint:App.isRTL()?!1:!0,source:t.ttAdapter()});var a=new Bloodhound({datumTokenizer:function(e){return e.tokens},queryTokenizer:Bloodhound.tokenizers.whitespace,remote:"../demo/typeahead_custom.php?query=%QUERY"});a.initialize(),App.isRTL()&&$("#typeahead_example_3").attr("dir","rtl"),$("#typeahead_example_3").typeahead(null,{name:"datypeahead_example_3",displayKey:"value",source:a.ttAdapter(),hint:App.isRTL()?!1:!0,templates:{suggestion:Handlebars.compile(['<div class="media">','<div class="pull-left">','<div class="media-object">','<img src="{{img}}" width="50" height="50"/>',"</div>","</div>",'<div class="media-body">','<h4 class="media-heading">{{value}}</h4>',"<p>{{desc}}</p>","</div>","</div>"].join(""))}});var n=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.team)},queryTokenizer:Bloodhound.tokenizers.whitespace,prefetch:"../demo/typeahead_nba.json"}),i=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.team)},queryTokenizer:Bloodhound.tokenizers.whitespace,prefetch:"../demo/typeahead_nhl.json"});n.initialize(),i.initialize(),App.isRTL()&&$("#typeahead_example_4").attr("dir","rtl"),$("#typeahead_example_4").typeahead({hint:App.isRTL()?!1:!0,highlight:!0},{name:"nba",displayKey:"team",source:n.ttAdapter(),templates:{header:"<h3>NBA Teams</h3>"}},{name:"nhl",displayKey:"team",source:i.ttAdapter(),templates:{header:"<h3>NHL Teams</h3>"}})},t=function(){var e=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.num)},queryTokenizer:Bloodhound.tokenizers.whitespace,local:[{num:"metronic"},{num:"keenthemes"},{num:"metronic theme"},{num:"metronic template"},{num:"keenthemes team"}]});e.initialize(),App.isRTL()&&$("#typeahead_example_modal_1").attr("dir","rtl"),$("#typeahead_example_modal_1").typeahead(null,{displayKey:"num",hint:App.isRTL()?!1:!0,source:e.ttAdapter()});var t=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.name)},queryTokenizer:Bloodhound.tokenizers.whitespace,limit:10,prefetch:{url:"../demo/typeahead_countries.json",filter:function(e){return $.map(e,function(e){return{name:e}})}}});t.initialize(),App.isRTL()&&$("#typeahead_example_modal_2").attr("dir","rtl"),$("#typeahead_example_modal_2").typeahead(null,{name:"typeahead_example_modal_2",displayKey:"name",hint:App.isRTL()?!1:!0,source:t.ttAdapter()});var a=new Bloodhound({datumTokenizer:function(e){return e.tokens},queryTokenizer:Bloodhound.tokenizers.whitespace,remote:"../demo/typeahead_custom.php?query=%QUERY"});a.initialize(),App.isRTL()&&$("#typeahead_example_modal_3").attr("dir","rtl"),$("#typeahead_example_modal_3").typeahead(null,{name:"datypeahead_example_modal_3",displayKey:"value",hint:App.isRTL()?!1:!0,source:a.ttAdapter(),templates:{suggestion:Handlebars.compile(['<div class="media">','<div class="pull-left">','<div class="media-object">','<img src="{{img}}" width="50" height="50"/>',"</div>","</div>",'<div class="media-body">','<h4 class="media-heading">{{value}}</h4>',"<p>{{desc}}</p>","</div>","</div>"].join(""))}});var n=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.team)},queryTokenizer:Bloodhound.tokenizers.whitespace,limit:3,prefetch:"../demo/typeahead_nba.json"}),i=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.team)},queryTokenizer:Bloodhound.tokenizers.whitespace,limit:3,prefetch:"../demo/typeahead_nhl.json"});n.initialize(),i.initialize(),$("#typeahead_example_modal_4").typeahead({hint:App.isRTL()?!1:!0,highlight:!0},{name:"nba",displayKey:"team",source:n.ttAdapter(),templates:{header:"<h3>NBA Teams</h3>"}},{name:"nhl",displayKey:"team",source:i.ttAdapter(),templates:{header:"<h3>NHL Teams</h3>"}})};return{init:function(){e(),t()}}}();jQuery(document).ready(function(){ComponentsTypeahead.init()});
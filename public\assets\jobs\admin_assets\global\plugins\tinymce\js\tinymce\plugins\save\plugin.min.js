!function(){"use strict";var n=tinymce.util.Tools.resolve("tinymce.PluginManager"),e=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),t=tinymce.util.Tools.resolve("tinymce.util.Tools"),a=function(n){return n.getParam("save_enablewhendirty",!0)},o=function(n){return!!n.getParam("save_onsavecallback")},c=function(n){return!!n.getParam("save_oncancelcallback")},i=function(n,e){n.notificationManager.open({text:n.translate(e),type:"error"})},r=function(n){var t;if(t=e.DOM.getParent(n.id,"form"),!a(n)||n.isDirty()){if(n.save(),o(n))return n.execCallback("save_onsavecallback",n),void n.nodeChanged();t?(n.setDirty(!1),t.onsubmit&&!t.onsubmit()||("function"==typeof t.submit?t.submit():i(n,"Error: Form submit field collision.")),n.nodeChanged()):i(n,"Error: No form element found.")}},l=function(n){var e=t.trim(n.startContent);c(n)?n.execCallback("save_oncancelcallback",n):(n.setContent(e),n.undoManager.clear(),n.nodeChanged())},d=function(n){n.addCommand("mceSave",function(){r(n)}),n.addCommand("mceCancel",function(){l(n)})},s=function(n){return function(e){var t=e.control;n.on("nodeChange dirty",function(){t.disabled(a(n)&&!n.isDirty())})}},u=function(n){n.addButton("save",{icon:"save",text:"Save",cmd:"mceSave",disabled:!0,onPostRender:s(n)}),n.addButton("cancel",{text:"Cancel",icon:!1,cmd:"mceCancel",disabled:!0,onPostRender:s(n)}),n.addShortcut("Meta+S","","mceSave")};n.add("save",function(n){u(n),d(n)})}();
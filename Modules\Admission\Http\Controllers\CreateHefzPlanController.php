<?php

namespace Modules\Admission\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Rules\CheckIfStringIsArabic;
use App\StudentHefzPlan;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\EducationalReports\Http\Requests\CreateHefzPlanRequest;
use Session;
use App\Student;

class CreateHefzPlanController extends Controller
{

    public function __invoke(CreateHefzPlanRequest $request, $redirect = false)
    {




        try {
            DB::beginTransaction();

            $planYearMonth = $request->hefz['start_date'];
            $dateMonthArray = explode('-', $planYearMonth);
            $year = $dateMonthArray[0];
            $month = $dateMonthArray[1];
            $planYearMonth = Carbon::createFromDate($year, $month, 1);
            $planYearMonth = $planYearMonth->format('Y-m');

            $plan = new StudentHefzPlan();
            $plan->start_date = $request->hefz['start_date'];
            $plan->plan_year_and_month = $planYearMonth;
            $plan->class_id = $request->hefz['class_id'];
            $plan->study_direction = $request->hefz['study_direction'];
            $plan->start_from_surat = $request->hefz['start_from_surat'];
            $plan->start_from_ayat = $request->hefz['start_from_ayat'];
            $plan->num_to_memorize = $request->hefz['num_to_memorize'];
            $plan->to_surat = $request->hefz['to_surat'];
            $plan->to_ayat = $request->hefz['to_ayat'];
            $plan->memorization_mood = $request->hefz['memorization_mood'];
            $plan->pages_to_revise = $request->hefz['pages_to_revise'];
            $plan->student_id = $request->student_id;
            $plan->organization_id = config('organization_id');
            $plan->created_by = auth()->user()->id;
            if (isset($request->hefz['start_date'])) {
//            $plan->start_date = $request->hefz['start_date'];
                $plan->start_date = Carbon::parse($request->hefz['start_date'])->toDateString();
                $plan->status = 'waiting_for_approval';
            } else {
                $plan->status = 'waiting_for_approval';
            }

            $plan->save();
            DB::commit();
            Toastr::success('Student Study Plan was Added ! Next step is to approve the study plan', 'Success');

            if ($redirect) {
                \Illuminate\Support\Facades\Session::flash('flash_message', 'Student Study Plan was Added <Successfuly></Successfuly>!');
            }
            return redirect()->back();
        } catch (\Exception $e) {

            DB::rollback();

            Toastr::warning($e->getMessage(), 'Danger');
            return redirect()->back();
        }
    }

}

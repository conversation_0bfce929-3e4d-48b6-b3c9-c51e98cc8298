<?php

namespace Modules\Education\Http\Middleware;

use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;

class WriteCurrentClassReportOnlyMiddleware
{
    /**
     * Handle an incoming request.
     * only allow teachers to add report for current day

     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $requestedClassDate = Carbon::parse($request->get('from_date'));

        \Session::remove('onlyCurrentDayClassReports');
        if($requestedClassDate->gt(Carbon::today())){

            abort('403');
        }


        return $next($request);
    }
}

<!DOCTYPE html>
<html>
  <head>
    <title>ITQAN Platform - Class Reports Unit</title>
    <link
      href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/@mdi/font@4.x/css/materialdesignicons.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/vuetify@2.x/dist/vuetify.min.css"
      rel="stylesheet"
    />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, minimal-ui"
    />
    <style>
      [v-cloak] {
        display: none;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <!-- App.vue -->

      <v-app v-cloak>
        <v-app-bar app color="blue darken-4" dark>
          <v-app-bar-nav-icon @click="goBack()">
            <v-icon>mdi-arrow-left</v-icon>
          </v-app-bar-nav-icon>

          <v-toolbar-title>
            ITQAN
          </v-toolbar-title>
          <div class="flex-grow-1"></div>
          <v-subheader>
            Class Reports Unit
          </v-subheader>
        </v-app-bar>
        <!-- Sizes your content based upon application components -->
        <v-content>
          <!-- Provides the application the proper gutter -->
          <v-container fluid>
            <v-card class="mx-auto" outlined>
              <v-list-item three-line>
                <v-list-item-content>
                  <div class="overline mb-4">
                    Report Created By: {{ $report->creator->name }}
                  </div>
                  <v-list-item-title
                    class="headline mb-1"
                    >{{ $class->name }}</v-list-item-title
                  >
                  <v-row>
                    <v-list-item-subtitle class="col-md">
                      <strong>
                        Teacher Name:
                      </strong>
                      {{ $report->teacher->full_name }}</v-list-item-subtitle
                    >
                    <v-list-item-subtitle class="col-md">
                      <strong>
                        Subject :
                      </strong>
                      @if ($report->subject_id == 0)
                      {{ $report->program->title }} Program [All Levels &
                      Subjects] @else
                      {{ $report->subject->title }}
                      @endif
                    </v-list-item-subtitle>
                    <v-list-item-subtitle class="col-md">
                      <strong>
                        Class Time:
                      </strong>
                      {{ $report->class_time->format('d-M-Y   H:iA') }}
                    </v-list-item-subtitle>
                  </v-row>
                </v-list-item-content>
              </v-list-item>
            </v-card>
            <v-divider></v-divider>
            <v-card class="mx-auto mt-4">
              <v-card-title>
                <h6>Teacher Attandance</h6>
                <div class="flex-grow-1"></div>
                <div class="text-right">
                  <!-- <label for="" class="v-label theme--light">Absent </label> -->
                  <v-switch
                    v-model="report.teacher_attended"
                    label="Attended"
                  ></v-switch>
                </div>
              </v-card-title>
            </v-card>
            <div class="flex-grow-1"></div>

            <div v-if="report.teacher_attended">
              <v-subheader>Class Student List</v-subheader>
              <v-expansion-panels popout>
                @foreach($class->students as $student)
                <v-expansion-panel :key="{{ $student->id }}">
                  <v-expansion-panel-header
                    :class="studentReportStatus(report.students[{{ $student->id }}])"
                  >
                    <div style="max-width: 90%">
                      <div class="no-wrap text-truncate student-name">
                        {{ ucwords($student->full_name) }}
                      </div>
                    </div>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-divider></v-divider>
                    <v-flex>
                      <v-select
                        :items="attandanceOptions"
                        v-model="report.students[{{ $student->id }}].attandance"
                        label="Attandance"
                      ></v-select>
                    </v-flex>
                    <div
                      v-show="isInClass(report.students[{{ $student->id }}])"
                    >
                      @if($special_program == 'hefz')
                      <v-flex xs12 v-if="report.students[{{ $student->id }}].hefz">
                        <v-card class="outline ">
                          <v-card-title class="pa-1">
                            <h6>
                              Memorization
                            </h6>
                          </v-card-title>
                          <v-row class="px-3">
                            <v-col md="4" sm="6" cols="12" class="py-0">
                              <label>From</label>
                              <v-row>
                                <v-col class="py-0" cols="8">
                                  <v-autocomplete
                                    :items="suar"
                                    v-model="report.students[{{ $student->id }}].hefz.from_surat"
                                    label="Surah"
                                  ></v-autocomplete>
                                </v-col>
                                <v-col class="py-0" cols="4">
                                  <v-text-field
                                    type="number"
                                    v-model="report.students[{{ $student->id }}].hefz.from_ayat"
                                    min="1"
                                    :max="numAyat(report.students[{{ $student->id }}].hefz.from_surat)"
                                    label="Ayah"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                            </v-col>
                            <v-col md="4" sm="6" cols="12" class="py-0">
                              <label>To</label>
                              <v-row>
                                <v-col class="py-0" cols="8">
                                  <v-autocomplete
                                    :items="suar"
                                    v-model="report.students[{{ $student->id }}].hefz.to_surat"
                                    label="Surah"
                                  ></v-autocomplete>
                                </v-col>
                                <v-col class="py-0" cols="4">
                                  <v-text-field
                                    type="number"
                                    v-model="report.students[{{ $student->id }}].hefz.to_ayat"
                                    min="1"
                                    :max="numAyat(report.students[{{ $student->id }}].hefz.to_surat)"
                                    label="Ayah"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                            </v-col>
                            <v-col md="4" sm="6" cols="12" class="py-0">
                              <label>Evaluation</label>
                              <v-select
                                :items="hefzEvaluationOptions"
                                v-model="report.students[{{ $student->id }}].hefz.evaluation"
                                label="Grade"
                              ></v-select>
                            </v-col>
                          </v-row>
                        </v-card>
                      </v-flex>
                      <v-flex xs12 class="mt-2" v-if="report.students[{{ $student->id }}].revision">
                        <v-card outline elevation-0>
                          <v-card-title class="pa-1">
                            <h6>
                              Revision
                            </h6>
                          </v-card-title>
                          <v-row class="px-3">
                            <v-col md="4" sm="6" cols="12" class="py-0">
                              <label>From</label>
                              <v-row>
                                <v-col class="py-0" cols="8">
                                  <v-autocomplete
                                    :items="suar"
                                    v-model="report.students[{{ $student->id }}].revision.from_surat"
                                    label="Surah"
                                  ></v-autocomplete>
                                </v-col>
                                <v-col cols="4" class="py-0">
                                  <v-text-field
                                    type="number"
                                    v-model="report.students[{{ $student->id }}].revision.from_ayat"
                                    min="1"
                                    :max="numAyat(report.students[{{ $student->id }}].revision.from_surat)"
                                    label="Ayah"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                            </v-col>
                            <v-col md="4" sm="6" cols="12" class="py-0">
                              <label>To</label>
                              <v-row>
                                <v-col cols="8" class="py-0">
                                  <v-autocomplete
                                    :items="suar"
                                    v-model="report.students[{{ $student->id }}].revision.to_surat"
                                    label="Surah"
                                  ></v-autocomplete>
                                </v-col>
                                <v-col cols="4" class="py-0">
                                  <v-text-field
                                    type="number"
                                    v-model="report.students[{{ $student->id }}].revision.to_ayat"
                                    min="1"
                                    :max="numAyat(report.students[{{ $student->id }}].revision.to_surat)"
                                    label="Ayah"
                                  ></v-text-field>
                                </v-col>
                              </v-row>
                            </v-col>
                            <v-col md="4" sm="6" cols="12" class="py-0">
                              <label>Evaluation</label>

                              <v-select
                                :items="hefzEvaluationOptions"
                                v-model="report.students[{{ $student->id }}].revision.evaluation"
                                label="Grade"
                              ></v-select>
                            </v-col>
                          </v-row>
                        </v-card>
                      </v-flex>

                      @else 
                      
                      <v-row>
                        <v-col>
                          <v-autocomplete
                            :items="lessons"
                            v-model="report.students[{{ $student->id }}].lesson.id"
                            label="Lesson"
                          ></v-autocomplete>
                        </v-col>
                      </v-row>
                      <v-row v-if="report.students[{{ $student->id }}].lesson.id">
                        <v-col v-for="(evaluation, label) in selectedLessonEvaluations(report.students[{{ $student->id }}])" :key="evaluation.id">
                          <v-select
                            :items="evaluation"
                            v-model="report.students[{{ $student->id }}].lesson.evaluations[label]"
                            @change="setEvaluation({{ $student->id }},label, $event)"
                            :label=label
                          ></v-select>
                        </v-col>
                      </v-row>
                       @endif
                    </div>
                  </v-expansion-panel-content>
                </v-expansion-panel>
                @endforeach
              </v-expansion-panels>
            </div>

            <v-row justify="center" class="mt-4">
              <v-btn color="primary" dark @click.stop="dialog = true">
                Submit Report
              </v-btn>

              <v-dialog v-model="dialog" max-width="290">
                <v-card>
                  <v-card-title class="headline"
                    >Do you want to submit the report</v-card-title
                  >

                  <v-card-text>
                    You will not be able to edit this report after submiting it.
                    Make sure all data are correct
                  </v-card-text>

                  <v-card-actions>
                    <div class="flex-grow-1"></div>

                    <v-btn color="green darken-1" text @click="dialog = false">
                      Later
                    </v-btn>

                    <v-btn color="green darken-1" text @click="submitReport()">
                      Submit
                    </v-btn>
                  </v-card-actions>
                </v-card>
              </v-dialog>
            </v-row>
          </v-container>
        </v-content>

        <v-fab-transition>
          <v-btn v-show="loading" fab small fixed bottom right>
            <v-progress-circular
              :size="42"
              color="amber"
              indeterminate
              small
            ></v-progress-circular>
          </v-btn>
        </v-fab-transition>

        <v-footer app>
          <!-- -->
        </v-footer>
      </v-app>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.x/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vuetify@2.x/dist/vuetify.js"></script>
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.19.0/axios.min.js"
      integrity="sha256-S1J4GVHHDMiirir9qsXWc8ZWw74PHHafpsHp5PXtjTs="
      crossorigin="anonymous"
    ></script>
    <script>
      new Vue({
        el: "#app",
        vuetify: new Vuetify({
          rtl: false
        }),
        data: function() {
          return {
            dialog:false,
            loading:false,
            suar: {!! json_encode($suar) !!},
            lessons: {!! json_encode($lessons) !!},
            hefzEvaluationOptions: {!! json_encode($hefzEvaluationOptions) !!},
            revisionEvaluationOptions: {!! json_encode($revisionEvaluationOptions) !!},
            report: {
              teacher_attended: true,
              students: {!! json_encode($students) !!}
            },
            attandanceOptions: [
              { text: "On Time", value: "on_time" },
              { text: "Late", value: "late" },
              { text: "Absent with Reason", value: "excused" },
              { text: "Absent", value: "absent" }
            ]
          };
        },
        watch: {
          report: {
            deep: true,
            handler() {
              console.log(this.report);
              var data = this.report;
              var self = this;
              data._token = "{{ csrf_token() }}";
              this.loading = true;

              axios.post('{{ route("class.reports.storeTemp", [$report->class_id , $report->id]) }}',
                   data).then(function(){

                   }).finally(function(){
                    self.loading = false;
                   });
            }
          }
        },
        methods: {
          goBack(){
            window.location = '{{ url("workplace/education/classes/{$report->class_id}/reports") }}';
          },
          isInClass(student) {
            if(student.attandance == "on_time" || student.attandance == "late"){
                return true;
            }
            return false;
          },
          setEvaluation(student_id, evaluation_type, evaluation_option){
            console.log({student_id, evaluation});

            this.report.students[student_id].lesson.evaluations[evaluation_type] = evaluation_option;
          },
          selectedLessonEvaluations(student){
            if(!student.lesson.id){
              return [
                {text : "Select Lesson First" , value: null}
              ];
            }
            var evaluation_schems = this.lessons.find(function(l){return l.value == student.lesson.id; }).evaluation_schems;
            console.log(student);
            //this.report.students[student.id].lesson = evaluation_schems;
            return evaluation_schems;
          },
          studentReportStatus(student){
              if(student.attandance == 'absent' || student.attandance == 'excused'){
                return 'success lighten-5';
              }
              @if($special_program && $special_program == 'hefz')
                if(student.hefz && student.hefz.from_ayat && !student.hefz.evaluation){
                  return '';
                }
                if(student.revision && student.revision.from_ayat && !student.revision.evaluation){
                  return '';
                }
                return 'success lighten-5';
              @endif
              if(student.lesson && student.lesson.evaluations){
                var lesson_id = student.lesson.id;

                var evaluation = this.lessons.find(function(les){return les.value == lesson_id});
                if(!evaluation){
                  return '';
                }
                var options = evaluation.evaluation_schems;
                if(JSON.stringify(Object.keys(options))  === JSON.stringify(Object.keys(student.lesson.evaluations))){
                  return 'success lighten-5';
                }

                return '';

              }
          },
          isStudentReportComplete(student){
            if(student.attandance == 'absent' || student.attandance == 'excused'){
                return true;
              }
              @if($special_program && $special_program == 'hefz')
              if(student.hefz && student.hefz.from_ayat && !student.hefz.evaluation){
                return false;
              }
              if(student.revision && student.revision.from_ayat && !student.revision.evaluation){
                return false;
              }
              @endif

              if(student.lesson && student.lesson.evaluations){
                var lesson_id = student.lesson.id;

                var evaluation = this.lessons.find(function(les){return les.value == lesson_id});
                if(!evaluation){
                  return false;
                }
                var options = evaluation.evaluation_schems;
                if(JSON.stringify(Object.keys(options))  === JSON.stringify(Object.keys(student.lesson.evaluations))){
                  return true;
                }

                return false;

              }


              return true;
          },
          numAyat(surah_id){
              var surah = this.suar.find(function(s) {return s.value == surah_id});
              if(surah){
                return surah.num_ayat;
              }
          },
          submitReport(){

            var data = this.report;
              var self = this;
              data._token = "{{ csrf_token() }}";
              this.dialog = false;

              var notComplete = Object.values(this.report.students).filter(function(student){
                return !self.isStudentReportComplete(student);
              });

              if(this.report.teacher_attended == true && notComplete.length > 0){
                alert('Report is not complete. add data to all students');
                return false;
              }

              this.loading = true;

              axios.post('{{ route("class.reports.submit", [$report->class_id , $report->id]) }}',
                   data).then(function(){

                   }).finally(function(){
                    self.loading = false;
                    window.location.reload();
                   });
          }
        },
        created() {
            @if($report->temp_data)
            this.report = {!! $report->temp_data !!};
            @endif
        },
      });
    </script>
  </body>
</html>

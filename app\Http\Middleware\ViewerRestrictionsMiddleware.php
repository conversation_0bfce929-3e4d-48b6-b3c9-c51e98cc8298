<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * Viewer Restrictions Middleware
 * 
 * Enforces view-only access for system viewer accounts
 * Blocks all write operations (POST, PUT, PATCH, DELETE)
 * Optionally anonymizes data on-the-fly for demonstrations
 */
final class ViewerRestrictionsMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::guard('employee')->user();
        
        if (!$user) {
            return $next($request);
        }

        // Check if user is a system viewer
        $isSystemViewer = $user->hasRole('system_viewer_' . config('organization_id') . '_');
        
        if ($isSystemViewer) {
            // Allow GET requests (viewing data) for system viewers
            if ($request->isMethod('GET')) {
                return $next($request);
            }
            
            // Block write operations for system viewers
            $writeOperations = ['POST', 'PUT', 'PATCH', 'DELETE'];
            if (in_array($request->method(), $writeOperations)) {
                // Log the blocked operation
                Log::info('System viewer blocked operation', [
                    'user_id' => $user->id,
                    'method' => $request->method(),
                    'url' => $request->url(),
                    'ip' => $request->ip()
                ]);
                
                if ($request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Demo mode: Write operations are not allowed'
                    ], 403);
                }
                
                return redirect()->back()->with('error', 'Demo mode: Write operations are not allowed');
            }
        }

        return $next($request);
    }

    /**
     * Determine if data should be anonymized for this request
     */
    private function shouldAnonymizeData(Request $request): bool
    {
        // Get user preference for demo data mode
        $user = Auth::guard('employee')->user();
        
        // Check if demo mode is enabled (you can store this in user meta or session)
        return $request->session()->get('viewer_demo_mode', false) ||
               $user->getMeta('demo_mode', false); // If you have user meta system
    }

    /**
     * Anonymize response data for demonstration purposes
     */
    private function anonymizeResponseData($response, Request $request)
    {
        // Only anonymize HTML responses (not JSON/API)
        if (!$response instanceof \Illuminate\Http\Response) {
            return $response;
        }

        $content = $response->getContent();
        
        // Apply anonymization patterns
        $anonymizedContent = $this->applyAnonymizationPatterns($content);
        
        $response->setContent($anonymizedContent);
        
        return $response;
    }

    /**
     * Apply data anonymization patterns to content
     */
    private function applyAnonymizationPatterns(string $content): string
    {
        // Email anonymization
        $content = preg_replace(
            '/([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/',
            'demo$<EMAIL>',
            $content
        );

        // Phone number anonymization (basic pattern)
        $content = preg_replace(
            '/(\+?\d{1,3}[-.\s]?)?(\d{3})[-.\s]?(\d{3})[-.\s]?(\d{4})/',
            '******-DEMO',
            $content
        );

        // You can add more patterns as needed
        // Names, addresses, etc.

        return $content;
    }
} 
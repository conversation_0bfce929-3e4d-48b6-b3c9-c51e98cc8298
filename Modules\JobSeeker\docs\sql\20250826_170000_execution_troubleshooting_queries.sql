-- Context: JobSeeker notification system troubleshooting queries
-- Purpose: Provide single-query solutions for common troubleshooting scenarios
-- Created: 2025-08-26 17:00:00

-- =====================================================
-- QUICK EXECUTION OVERVIEW
-- =====================================================

-- Get recent execution summary with key metrics
SELECT 
    execution_id,
    command,
    started_at,
    execution_status,
    jobs_fetched,
    logged_steps,
    error_steps,
    warning_steps,
    failure_count,
    emails_sent,
    jobs_notified,
    unique_recipients
FROM v_execution_troubleshooting 
ORDER BY started_at DESC 
LIMIT 10;

-- =====================================================
-- DETAILED EXECUTION ANALYSIS
-- =====================================================

-- Get step-by-step breakdown for a specific execution
-- Replace 593 with actual execution_id
SELECT 
    step_sequence,
    step_name,
    step_type,
    status,
    provider,
    message,
    step_offset_ms,
    duration_ms,
    created_at
FROM v_execution_step_analysis 
WHERE execution_id = 593
ORDER BY step_sequence;

-- =====================================================
-- FAILURE ANALYSIS
-- =====================================================

-- Get all failures with context for the last 24 hours
SELECT 
    failure_id,
    error_type,
    error_message,
    setup_name,
    job_title,
    recipient_email,
    execution_id,
    failure_time
FROM v_execution_failures 
WHERE failure_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY failure_time DESC;

-- Get failure patterns (most common error types)
SELECT 
    error_type,
    COUNT(*) as occurrence_count,
    COUNT(DISTINCT recipient_email) as affected_recipients,
    COUNT(DISTINCT setup_id) as affected_setups,
    MIN(failure_time) as first_seen,
    MAX(failure_time) as last_seen
FROM v_execution_failures 
WHERE failure_time >= DATE_SUB(NOW(), INTERVAL 7 DAYS)
GROUP BY error_type
ORDER BY occurrence_count DESC;

-- =====================================================
-- CATEGORY MAPPING ANALYSIS
-- =====================================================

-- Find executions with category mapping issues
SELECT 
    execution_id,
    command,
    started_at,
    COUNT(*) as mapping_warnings
FROM v_execution_step_analysis 
WHERE step_name LIKE '%category_mapping%' 
    AND status IN ('warning', 'error')
    AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAYS)
GROUP BY execution_id, command, started_at
ORDER BY mapping_warnings DESC, started_at DESC;

-- =====================================================
-- EMAIL DELIVERY ANALYSIS
-- =====================================================

-- Get email delivery success rates by day
SELECT 
    metric_date,
    total_emails_sent,
    total_emails_failed,
    email_success_rate_percent,
    total_setups_processed,
    total_recipients_processed,
    executions_count
FROM v_execution_health_summary 
WHERE metric_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)
ORDER BY metric_date DESC;

-- Find executions with email sending problems
SELECT 
    execution_id,
    command,
    started_at,
    jobs_fetched,
    emails_sent,
    failure_count,
    CASE 
        WHEN jobs_fetched > 0 AND emails_sent = 0 THEN 'No emails sent despite jobs found'
        WHEN failure_count > emails_sent THEN 'More failures than successes'
        WHEN emails_sent < (jobs_fetched * 0.1) THEN 'Very low email rate'
        ELSE 'Normal'
    END as issue_type
FROM v_execution_troubleshooting 
WHERE started_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    AND (
        (jobs_fetched > 0 AND emails_sent = 0) OR
        (failure_count > emails_sent) OR
        (emails_sent < (jobs_fetched * 0.1) AND jobs_fetched > 10)
    )
ORDER BY started_at DESC;

-- =====================================================
-- PERFORMANCE ANALYSIS
-- =====================================================

-- Find slow executions
SELECT 
    execution_id,
    command,
    started_at,
    completed_at,
    TIMESTAMPDIFF(SECOND, started_at, completed_at) as duration_seconds,
    jobs_fetched,
    emails_sent,
    peak_memory_mb,
    max_processing_time_ms
FROM v_execution_troubleshooting 
WHERE completed_at IS NOT NULL
    AND TIMESTAMPDIFF(SECOND, started_at, completed_at) > 300  -- More than 5 minutes
    AND started_at >= DATE_SUB(NOW(), INTERVAL 7 DAYS)
ORDER BY duration_seconds DESC;

-- =====================================================
-- USER SETUP ANALYSIS
-- =====================================================

-- Find setups that never receive notifications
SELECT 
    jns.id as setup_id,
    jns.name as setup_name,
    js.email as owner_email,
    jns.created_at as setup_created,
    COUNT(jnsj.id) as notifications_sent,
    MAX(jnsj.sent_at) as last_notification
FROM job_notification_setups jns
JOIN job_seekers js ON jns.job_seeker_id = js.id
LEFT JOIN job_notification_sent_jobs jnsj ON jns.id = jnsj.setup_id
WHERE jns.is_active = 1
GROUP BY jns.id, jns.name, js.email, jns.created_at
HAVING notifications_sent = 0
    AND setup_created < DATE_SUB(NOW(), INTERVAL 7 DAYS)
ORDER BY setup_created DESC;

-- =====================================================
-- RECENT ACTIVITY SUMMARY
-- =====================================================

-- Get activity summary for the last 24 hours
SELECT 
    'Executions' as metric_type,
    COUNT(*) as count,
    COUNT(CASE WHEN execution_status = 'completed' THEN 1 END) as successful,
    COUNT(CASE WHEN execution_status = 'failed' THEN 1 END) as failed
FROM v_execution_troubleshooting 
WHERE started_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)

UNION ALL

SELECT 
    'Process Steps' as metric_type,
    COUNT(*) as count,
    COUNT(CASE WHEN status = 'success' THEN 1 END) as successful,
    COUNT(CASE WHEN status = 'error' THEN 1 END) as failed
FROM v_execution_step_analysis 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)

UNION ALL

SELECT 
    'Failures' as metric_type,
    COUNT(*) as count,
    COUNT(CASE WHEN failure_status = 'resolved' THEN 1 END) as successful,
    COUNT(CASE WHEN failure_status = 'failed' THEN 1 END) as failed
FROM v_execution_failures 
WHERE failure_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR);

<?php

namespace Modules\ApplicationCenter\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Factory;

class ApplicationCenterServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
//        $this->app->register(RouteServiceProvider::class);
        // Register the StudentPhotoValidationServiceProvider
        // $this->app->register(StudentPhotoValidationServiceProvider::class);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            __DIR__.'/../Config/config.php' => config_path('applicationcenter.php'),

//            module_path('StudentDashboard', 'Config/config.php') => config_path('studentapplication.php'),
        ], 'config');
        $this->mergeConfigFrom(
            __DIR__.'/../Config/config.php', 'applicationcenter'

//            module_path('StudentDashboard', 'Config/config.php'), 'studentapplication'
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
//        $viewPath = base_path('resources/views/modules/studentapplication');
        $viewPath = resource_path('views/modules/applicationcenter');

        $sourcePath = __DIR__.'/../Resources/views';
//        $sourcePath = module_path('StudentDashboard', 'Resources/views');


//        $this->publishes([
//            $sourcePath => $viewPath
//        ],'views');
        $this->publishes([
            $sourcePath => $viewPath
        ]);

        $this->loadViewsFrom(array_merge(array_map(function ($path) {
            return $path . '/modules/applicationcenter';
        }, \Config::get('view.paths')), [$sourcePath]), 'applicationcenter');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/applicationcenter');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'applicationcenter');
        } else {
            $this->loadTranslationsFrom(__DIR__ .'/../Resources/lang', 'applicationcenter');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (! app()->environment('production') && $this->app->runningInConsole()) {
            app(Factory::class)->load(module_path('ApplicationCenter', 'Database/factories'));
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}

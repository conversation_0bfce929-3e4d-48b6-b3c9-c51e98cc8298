<?php

namespace Modules\Education\Http\Controllers;

use App\AttendanceOption;
use App\Employee;
use App\EvaluationSchemaOption;
use App\StudentAttendance;
use App\Student;
use App\StudentHefzPlan;
use App\StudentRevisionPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class IjazasanadMonthEndHalaqahSummaryController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(Request $request)
    {

        DB::connection()->enableQueryLog();

//        if ($request->ajax()) {
        try {
            $planYearMonth = Carbon::parse($request->get('classDate'));

            $year = $planYearMonth->year;
            $month = $planYearMonth->month;


            $classId = $request->get('classId');

            $classes = Classes::where('id', $classId)

                ->with([
                    'students' => function ($query) use ($month, $year, $classId) {
                        $query->whereHas('ijazasanad_memorization_plans',function ($q) use ($year, $month, $classId) {

                            $q->where(function ($q) use ($year, $month,$classId) {
                                $q->whereYear('created_at', $year)
                                    ->whereMonth('created_at', $month)
                                    ->whereNotNull('start_from_surat')
                                    ->whereNotNull('start_from_ayat')
                                    ->whereNotNull('to_surat')
                                    ->whereNotNull('to_ayat')
                                    ->where('status','active')
                                    ->where('class_id', $classId);
                            })->orWhere(function ($q2) use ($year, $month,$classId) {
                                $q2->whereYear('start_date', $year)
                                    ->whereMonth('start_date', $month)
                                    ->whereNotNull('start_from_surat')
                                    ->whereNotNull('start_from_ayat')
                                    ->whereNotNull('to_surat')
                                    ->whereNotNull('to_ayat')
                                    ->where('status','active')
                                    ->where('class_id', $classId);
                            });

                        })
                            ->with([
                            'ijazaMemorizationReport' => function ($q) use ($year, $month, $classId) {
                                $q->where(function ($q) use ($year, $month, $classId) {
                                    $q->whereYear('created_at', $year)
                                        ->whereMonth('created_at', $month)
                                        ->where('class_id', $classId)
                                        ->whereNotNull('hefz_from_surat')
                                        ->whereNotNull('hefz_from_ayat')
                                        ->whereNotNull('hefz_to_surat')
                                        ->whereNotNull('hefz_to_ayat');
                                });
                            },

                            'ijazaRevisionReport' => function ($q) use ($year, $month, $classId) {

                                $q->whereYear('created_at', $year)
                                    ->whereMonth('created_at', $month)
                                    ->where('class_id', $classId)
                                    ->whereNotNull('revision_from_surat')
                                    ->whereNotNull('revision_from_ayat')
                                    ->whereNotNull('revision_to_surat')
                                    ->whereNotNull('revision_to_ayat');

                            },
                            'ijazasanad_revision_plans' => function ($q) use ($year, $month, $classId) {
                                $q->where(function ($q) use ($year, $month,$classId) {
                                    $q->whereYear('created_at', $year)
                                        ->whereMonth('created_at', $month)
                                        ->whereNotNull('start_from_surat')
                                        ->whereNotNull('start_from_ayat')
                                        ->whereNotNull('to_surat')
                                        ->whereNotNull('to_ayat')
                                        ->where('status','active')
                                        ->where('class_id', $classId);
                                })->orWhere(function ($q2) use ($year, $month,$classId) {
                                    $q2->whereYear('start_date', $year)
                                        ->whereMonth('start_date', $month)
                                        ->whereNotNull('start_from_surat')
                                        ->whereNotNull('start_from_ayat')
                                        ->whereNotNull('to_surat')
                                        ->whereNotNull('to_ayat')
                                        ->where('status','active')
                                        ->where('class_id', $classId);
                                });


                            },
                                'ijazasanad_memorization_plans' => function ($q) use ($year, $month, $classId) {

                                    $q->where(function ($q) use ($year, $month,$classId) {
                                        $q->whereYear('created_at', $year)
                                            ->whereMonth('created_at', $month)
                                            ->whereNotNull('start_from_surat')
                                            ->whereNotNull('start_from_ayat')
                                            ->whereNotNull('to_surat')
                                            ->whereNotNull('to_ayat')
                                            ->where('status','active')
                                            ->where('class_id', $classId);
                                    })->orWhere(function ($q2) use ($year, $month,$classId) {
                                        $q2->whereYear('start_date', $year)
                                            ->whereMonth('start_date', $month)
                                            ->whereNotNull('start_from_surat')
                                            ->whereNotNull('start_from_ayat')
                                            ->whereNotNull('to_surat')
                                            ->whereNotNull('to_ayat')
                                            ->where('status','active')
                                            ->where('class_id', $classId);
                                    });

                                },
                        ])

                            ->withCount(['ijazaMemorizationReport as total_sessions' => function ($query) use ($month, $year, $classId) {
                            $start_date = Carbon::createFromDate($year, $month, 1); // first day of the month
                            $end_date = Carbon::createFromDate($year, $month, 1)->endOfMonth(); // last day of the month
                            $query->whereBetween('created_at', [$start_date, $end_date])
                                ->where('class_id', $classId);
                        }])
                        ->withCount(['completedIjazasanadRevisionReport as total_revisions' => function ($query) use ($month, $year, $classId) {
                            $start_date = Carbon::createFromDate($year, $month, 1); // first day of the month
                            $end_date = Carbon::createFromDate($year, $month, 1)->endOfMonth(); // last day of the month
                            $query->whereBetween('created_at', [$start_date, $end_date])
                                ->where('class_id', $classId);
                        }]);

                    },

                ])
                ->get();




            return \Yajra\DataTables\DataTables::of($classes)
                ->addIndexColumn()
                ->addColumn('noOfStudents', function ($classDetails) use ($request) {
                    return '<h2 style="color: #1fff0f;
    font-weight: bolder;
    font-size: 24px;">' . $classDetails->students->count() . '</h2>';

                })
                ->addColumn('memorizedPages', function ($classDetails) use ($request) {

//                    get me the total number of pages memorized for this halaqah



                    $numberofPagesSum = 0;
                    foreach ($classDetails->students as $studentDetails) {


                        if ($studentDetails->total_sessions > 0) {
                            $firstHefz = $studentDetails->ijazaMemorizationReport->sortBy(function ($row) {
                                return [$row->hefz_from_surat, $row->hefz_from_ayat];
                            })->first();

                            $lastHefz = $studentDetails->ijazaMemorizationReport->sortByDesc(function ($row) {
                                return [$row->hefz_to_surat, $row->hefz_to_ayat];
                            })->first();


                            $min_hefz_from_surat = $firstHefz->hefz_from_surat;
                            $min_hefz_from_ayat = $firstHefz->hefz_from_ayat;
                            $max_hefz_to_surat = $lastHefz->hefz_to_surat;
                            $max_hefz_to_ayat = $lastHefz->hefz_to_ayat;
                            $hefzPlan = $studentDetails->ijazasanad_memorization_plans;
                            $hefzPlan = $hefzPlan[0];
                            if ($hefzPlan->study_direction == 'backward') {
                                $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $min_hefz_from_surat,
                                    $min_hefz_from_ayat,
                                    $max_hefz_to_surat,
                                    $max_hefz_to_ayat
                                ]);
                                $numberofPagesSum += $numberofPages[0]->numberofPagesSum;
                            } else {
                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $min_hefz_from_surat,
                                    $min_hefz_from_ayat,
                                    $max_hefz_to_surat,
                                    $max_hefz_to_ayat
                                ]);
                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $numberofPagesSum += $results[0]->number_of_pages_sum;
                            }
                        }
                    }

                    return '<h2 style="color: #1fff0f;
    font-weight: bolder;
    font-size: 24px;">' . $numberofPagesSum . '</h2>';

                })
                ->editColumn('revisedPages', function ($classDetails) use ($request) {

                    $numberofRevisedPagesSum = 0;
                    $numberofPagesSumsss = [];


                    // Filter students
                    $filteredStudents = $classDetails->students->reject(function ($student) {
                        return $student->total_revisions == 0;
                    });


                    foreach ($filteredStudents as $studentDetails) {



                            $firstRevision = $studentDetails->ijazaRevisionReport->sortBy(function ($row) {
                                return [$row->revision_from_surat, $row->revision_from_ayat];
                            })->first();

                            $lastRevision = $studentDetails->ijazaRevisionReport->sortByDesc(function ($row) {
                                return [$row->revision_to_surat, $row->revision_to_ayat];
                            })->first();



                            $min_revision_from_surat = $firstRevision->revision_from_surat;
                            $min_revision_from_ayat = $firstRevision->revision_from_ayat;
                            $max_revision_to_surat = $lastRevision->revision_to_surat;
                            $max_revision_to_ayat = $lastRevision->revision_to_ayat;
                            $revisionPlan = $studentDetails->ijazasanad_revision_plans->first();
//                            $revisionPlan = $revisionPlan[0];
                            if ($revisionPlan->study_direction == 'backward') {
                                $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $min_revision_from_surat,
                                    $min_revision_from_ayat,
                                    $max_revision_to_surat,
                                    $max_revision_to_ayat
                                ]);
                                $numberofRevisedPagesSum += $numberofPages[0]->numberofPagesSum;
                                $numberofPagesSumsss[$studentDetails->id] = [$min_revision_from_surat,
                                    $min_revision_from_ayat,
                                    $max_revision_to_surat,
                                    $max_revision_to_ayat];

                            } else {
                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $min_revision_from_surat,
                                    $min_revision_from_ayat,
                                    $max_revision_to_surat,
                                    $max_revision_to_ayat
                                ]);

                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $numberofRevisedPagesSum += $results[0]->number_of_pages_sum;
                                $numberofPagesSumsss[$studentDetails->id] = [$min_revision_from_surat,
                                    $min_revision_from_ayat,
                                    $max_revision_to_surat,
                                    $max_revision_to_ayat];
                            }

                    }





                    return '<h2 style="color: #1fff0f;
    font-weight: bolder;
    font-size: 24px;">' . $numberofRevisedPagesSum . '</h2>';



                })
                ->addColumn('attendanceDaysPercentage', function ($classDetails) use ($request,$year,$month) {


//                    $result= round($classDetails->students->avg('attendance_percentage'),2);
                    $result= round($classDetails->ijazasanadAverageAttendancePercentage($month,$year),2);

                    return  '<div class="progress" style="position: relative;">
  <div  class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
  </div>
</div>';


//                    $attendanceCount = 0;
//                    $totalSessions = 0;
//                    foreach ($classDetails->students as $studentDetails) {
//
//                        $attendanceCount += $studentDetails->completedHefzReport->reduce(function ($carry, $item) {
//                            if ($item->attendance_id == 2) {
//                                $carry++;
//                            }
//                            return $carry;
//                        }, 0);
//                        $totalSessions += $studentDetails->completedHefzReport->reduce(function ($carry, $item) {
//
//                                $carry++;
//
//                            return $carry;
//                        }, 0);
//
//
//                    }
//
//                    $attendance_percentage = $attendanceCount / $totalSessions * 100;
//                    return $attendance_percentage;
                })
                ->addColumn('hefzAchievementComparedtoHefzPlan', function ($classDetails) use ($request, $classId) {
                    try {

                        $memorizedNumberofPagesSum = 0;
                        $plannedNumberofPagesSum = 0;
                        $testArray = [];
                        foreach ($classDetails->students as $studentDetails) {
                            if ($studentDetails->ijazaMemorizationReport->count() > 0) {

                                $firstHefz = $studentDetails->ijazaMemorizationReport->sortBy(function ($row) {
                                    return [$row->hefz_from_surat, $row->hefz_from_ayat];
                                })->first();
                                $lastHefz = $studentDetails->ijazaMemorizationReport->sortByDesc(function ($row) {
                                    return [$row->hefz_to_surat, $row->hefz_to_ayat];
                                })->first();
                                $min_hefz_from_surat = $firstHefz->hefz_from_surat;
                                $min_hefz_from_ayat = $firstHefz->hefz_from_ayat;
                                $max_hefz_to_surat = $lastHefz->hefz_to_surat;
                                $max_hefz_to_ayat = $lastHefz->hefz_to_ayat;
                                $hefzPlan = $studentDetails->ijazasanad_memorization_plans;
                                $hefzPlan = $hefzPlan[0];
                                // now find out the number of pages memorized so far
                                if ($hefzPlan->study_direction == 'backward') {

                                    $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                        $min_hefz_from_surat,
                                        $min_hefz_from_ayat,
                                        $max_hefz_to_surat,
                                        $max_hefz_to_ayat
                                    ]);
                                    $memorizedNumberofPagesSum += $numberofPages[0]->numberofPagesSum;

                                }
                                else {

                                    DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                        $min_hefz_from_surat,
                                        $min_hefz_from_ayat,
                                        $max_hefz_to_surat,
                                        $max_hefz_to_ayat
                                    ]);
                                    $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                    $memorizedNumberofPagesSum += $results[0]->number_of_pages_sum;


                                }

                                $firstPlanSurat = $hefzPlan->start_from_surat;
                                $firstPlanAyat = $hefzPlan->start_from_ayat;
                                $lastPlanSurat = $hefzPlan->to_surat;
                                $lastPlanAyat = $hefzPlan->to_ayat;
                                // now find out the number of pages asssigned at the hefz plan
                                if ($hefzPlan->study_direction == 'backward') {
                                    $numberofPagesPlanned = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                        $firstPlanSurat,
                                        $firstPlanAyat,
                                        $lastPlanSurat,
                                        $lastPlanAyat
                                    ]);
                                    $plannedNumberofPagesSum += $numberofPagesPlanned[0]->numberofPagesSum;

                                }
                                else {
                                    DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                        $firstPlanSurat,
                                        $firstPlanAyat,
                                        $lastPlanSurat,
                                        $lastPlanAyat
                                    ]);
                                    $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                    $plannedNumberofPagesSum += $results[0]->number_of_pages_sum;

                                }
                                if (empty($memorizedNumberofPagesSum) || is_null($memorizedNumberofPagesSum)) {
                                    $testArray[] = [

                                        'percentage' => 0
                                    ];
                                } elseif (empty($plannedNumberofPagesSum) || is_null($plannedNumberofPagesSum)) {
                                    $testArray[] = [

                                        'percentage' => 0
                                    ];
                                } else {


                                    $actual_percentage = round(($memorizedNumberofPagesSum / $plannedNumberofPagesSum * 100), 2);

                                    $expected_percentage = 100;

                                    $testArray[] = [

                                        'percentage' => min($actual_percentage, $expected_percentage)
                                    ];

                                }
                            }
                        }

                        $sum = array_sum(array_column($testArray, 'percentage'));
                        $studentCount = $classDetails->students->count();

                        if ($studentCount > 0) {
                            $sum = round($sum/$studentCount, 2);
                        } else {
                            $sum = 0;
                        }
//                        $sum = round($sum/$classDetails->students->count(),2);

                        return  '<div class="progress" style="position: relative;">
  <div  data-studentId="'.$studentDetails->id.'" class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$sum . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $sum . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $sum . '%</span>
  </div>
</div>';






                        return $result;
                    } catch (\Exception $exception) {
                        dd(5);
                    }
                })
                ->rawColumns(['noOfStudents','hefzAchievementComparedtoHefzPlan','attendanceDaysPercentage','revisedPages','memorizedPages'])
                ->make(true);
        } catch (\Exception $e) {
            // Handle the exception
            dd($e->getMessage());
            \Log::error($e->getMessage());
            return response()->json(['error' => 'An error occurred while loading the data for the table.'], 500);
        }


//        }


// return $class_programs;
        return view('education::classes.reports.index', compact('attendances', 'days', 'year', 'month', 'current_day', 'class', 'teacher_timetable', 'teacher', 'from_date', 'to_date', 'report_summery', 'class_programs', 'class_subjects_reports', 'class_subjects', 'students_full_details', 'surats', 'hefzEvaluationOptions'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public
    function create(Request $request, $id)
    {

        $class = Classes::findOrFail($id);
        $students = $class->students()->get();
        $report_id = $request->get('report_id');
        $from_date = $request->from_date;
        $surats = MoshafSurah::all();
        $teachers = $class->teachers()->pluck('employees.name', 'employees.id');
        $hefz_evaluation_schema = EvaluationSchema::where('target', 'hefz')->first();
        $revision_evaluation_schema = EvaluationSchema::where('target', 'revision')->first();
        $hefz_valuation_options = $hefz_evaluation_schema->options()->get();
        $revision_valuation_options = $revision_evaluation_schema->options()->get();

//        if (isset($report_id)){
        if (isset($from_date)) {
            foreach ($students as $student) {
//                $student->hefz_report = $student->hefz()->where('class_report_id',$report_id)->first();
                $student->hefz_report = $student->hefz()->whereDate('created_at', '=', Carbon::parse($from_date)->toDate())->first();


                $student->ijazasanad_memorization_plans->start_from_ayat = $this->getAyatListBySurat($student->ijazasanad_memorization_plans->hefz_from_surat, $student->ijazasanad_memorization_plans->start_from_ayat);
                $student->ijazasanad_memorization_plans->to_ayat = $this->getAyatListBySurat($student->ijazasanad_memorization_plans->hefz_to_surat, $student->ijazasanad_memorization_plans->to_ayat);

//                $student->revision_report = $student->revision()->where('class_report_id',$report_id)->first();
                $student->revision_report = $student->ijazaRevisionReport()->whereDate('created_at', Carbon::parse($from_date)->toDate())->first();

                $student->revision_report->revision_from_ayat = $this->getAyatListBySurat($student->revision_report->revision_from_surat, $student->revision_report->revision_from_ayat);
                $student->revision_report->revision_to_ayat = $this->getAyatListBySurat($student->revision_report->revision_to_surat, $student->revision_report->revision_to_ayat);
            }
        }

        //dd($hefz_valuation_options);
        return view('education::classes.reports.create', compact('students', 'class', 'from_date', 'surats', 'teachers', 'hefz_valuation_options', 'revision_valuation_options'));


        // Old stuffs
        if (in_array(auth()->user()->id, $class->teachers()->pluck('employee_id')->toArray())) {
            //$teachers = [auth()->user()->id => auth()->user()->full_name];
        } else {
            //$teachers = $class->teachers()->pluck('full_name', 'employee_id');
        }

        $subjects = [];

        foreach ($class->programs as $program) {
            if ($program->pivot->program_level_id == 0) {
                $subjects['p' . $program->id] = $program->title . ' Program [All levels & Subjects]';
            } else {
                foreach ($program->levels as $level) {
                    if ($level->id == $program->pivot->program_level_id) {
                        foreach ($level->subjects as $subject) {
                            $subjects[$subject->id] = $subject->title;
                        }
                    }
                }
            }
        }

        // $subjects = $class->programs[0]->subjects;

        // return $class->programs[1]->levels[1]->subjects;

        return view('education::classes.reports.create', compact('class', 'teachers', 'subjects'));
    }

    /**
     * Get list of ayats based on surah
     *
     * @param $surah_id
     * @param $ayat_num
     * @return string
     */
    public
    function getAyatListBySurat($surah_id, $ayat_num): string
    {
        $surah = MoshafSurah::find($surah_id);
        $options = '<option value="" selected>Select</option>';

        if (isset($surah)) {
            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {
                if ($i === $ayat_num) {
                    $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                } else {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';
                }
            }
        }

        return $options;
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
//public function store(Request $request)
    public
    function store(Request $request)
    {


        $field = $request->get('field');
        $field_value = $request->get('field_value');
        $organization_id = $request->get('organization_id');
        $table = $request->get('table');
        $teacher_id = $request->get('teacher_id');
        $subject_id = $request->get('subject_id');
        $report_id = $request->get('report_id');
        $studyDirection = $request->get('studyDirection');
        $planYearMonth = $request->get('from_date');
        $dateMonthArray = explode('-', $planYearMonth);
        $year = $dateMonthArray[0];
        $month = $dateMonthArray[1];
        $planYearMonth = Carbon::createFromDate($year, $month, 1);
        $planYearMonth = $planYearMonth->format('Y-m');

        $hefz_from_surat = $request->get('start_from_surat');
        $start_from_ayat = $request->get('start_from_ayat');
        $to_surat = $request->get('to_surat');
        $to_ayat = $request->get('to_ayat');

        $student = Student::find($request->get('student_id'));


        //$report = ClassReport::where('class_id',$request->get('class_id'));


        $numberofPages = DB::select("select *,abs(first_page-last_page) as pageCount
         from
         (select id,page_number as first_page
                   from moshaf_pages
                   where (surah_id = :startSurahId and first_ayah <= :startAyah)
                   order by page_number desc
                   limit 1) A
                  INNER JOIN
    (select id,page_number as last_page
                   from moshaf_pages
                   where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                   order by page_number desc
                   limit 1) B
on A.id <> B.id", array(
            'startSurahId' => $hefz_from_surat,
            'startAyah' => $start_from_ayat,
            'lastSurahId' => $to_surat,
            'lastAyah' => $to_ayat,
            'lastAyah2' => $to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
        ));


        $numberofPages = $numberofPages[0]->pageCount;


        $student->ijazasanad_memorization_plans()->updateOrCreate(
            [
                'organization_id' => $organization_id,
                'student_id' => $student->id,
                'plan_year_and_month' => $planYearMonth
            ],
            [

                "start_from_surat" => !empty($hefz_from_surat) ? $hefz_from_surat : null,
                "start_from_ayat" => !empty($start_from_ayat) ? $start_from_ayat : null,
                "to_surat" => !empty($to_surat) ? $to_surat : null,
                "to_ayat" => !empty($to_ayat) ? $to_ayat : null,
                "plan_year_and_month" => $planYearMonth,
                "study_direction" => $studyDirection
            ]
        );


        return response()->json(['message' => 'success', 'plan' => $student->ijazasanad_memorization_plans, 'numberofPages' => $numberofPages], 200);


        /*dd('reached store',$student, $report);
        $requestData = $request->all();

        $requestData['organization_id'] = config('organization_id');

        $report = new ClassReport();

        $report->class_id = $request->class_id;

        $report->employee_id = $request->employee_id;
        $report->class_time = Carbon::parse($request->class_time);


        if (strpos($request->subject_id, 'p') !== false) {
            $report->program_id = str_replace('p', '', $request->subject_id);
            $report->subject_id = 0;
        } else {
            $report->subject_id = $request->subject_id;
            $report->program_id = 0;
        }

        $report->created_by = auth()->user()->id;

        $report->notes = $request->notes;

        $report->save();

        Session::flash('flash_message', 'Class added!');

        return redirect('workplace/education/classes/'.$report->class_id.'/reports/'.$report->id.'/prepare');*/
        // return redirect('workplace/education/classes/'.$report->class_id.'/reports/'.$report->id.'/edit');
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public
    function show(Request $request, $id)
    {


        $from_date_year = Carbon::parse($request->from_date)->year;
        $from_date_month = Carbon::parse($request->from_date)->format('m');
        $from_date = $from_date_year . '-' . $from_date_month;
        $class = Classes::whereId($id)->with('students')->with(['students.hefz_plans' => function ($query) use ($from_date) {
            $query->where('plan_year_and_month', $from_date);
        }])->get();

//        $students = $class->students()->get();
//        $students = $class->students()->get();


        $report_id = $request->get('report_id');

        $surats = MoshafSurah::all();
//        $teachers = $class->teachers()->pluck('employees.name','employees.id');
        $hefz_evaluation_schema = EvaluationSchema::where('target', 'hefz')->first();
        $revision_evaluation_schema = EvaluationSchema::where('target', 'revision')->first();
        $hefz_valuation_options = $hefz_evaluation_schema->options()->get();
        $revision_valuation_options = $revision_evaluation_schema->options()->get();

//        if (isset($report_id)){
        if (isset($from_date)) {
            foreach ($class as $classDtails) {

                foreach ($classDtails->students as $student) {

                    foreach ($student->ijazasanad_memorization_plans as $hefz_plans) {


                        $numberofPages = DB::select("select *,abs(first_page-last_page) as pageCount
         from
         (select id,page_number as first_page
                   from moshaf_pages
                   where (surah_id = :startSurahId and first_ayah <= :startAyah)
                   order by page_number desc
                   limit 1) A
                  INNER JOIN
    (select id,page_number as last_page
                   from moshaf_pages
                   where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                   order by page_number desc
                   limit 1) B
on A.id <> B.id", array(
                            'startSurahId' => $hefz_plans->start_from_surat,
                            'startAyah' => $hefz_plans->start_from_ayat,
                            'lastSurahId' => $hefz_plans->to_surat,
                            'lastAyah' => $hefz_plans->to_ayat,
                            'lastAyah2' => $hefz_plans->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                        ));


                        $hefz_plans->no_of_pages = $numberofPages[0]->pageCount;
//                    $student->hefz_plans->start_from_ayat = $this->getAyatListBySurat($student->hefz_plans->start_from_surat, $student->hefz_plans->start_from_ayat);
                        $hefz_plans->start_from_ayat = $this->getAyatListBySurat($hefz_plans->start_from_surat, $hefz_plans->start_from_ayat);
//                    $student->hefz_plans->to_ayat = $this->getAyatListBySurat($student->hefz_plans->hefz_to_surat, $student->hefz_plans->to_ayat);
                        $hefz_plans->to_ayat = $this->getAyatListBySurat($hefz_plans->to_surat, $hefz_plans->to_ayat);

//                $student->revision_report = $student->revision()->where('class_report_id',$report_id)->first();
                        $student->revision_report = $student->ijazaRevisionReport()->whereDate('created_at', Carbon::parse($from_date)->toDate())->first();

                        $student->revision_report->revision_from_ayat = $this->getAyatListBySurat($student->revision_report->revision_from_surat, $student->revision_report->revision_from_ayat);
                        $student->revision_report->revision_to_ayat = $this->getAyatListBySurat($student->revision_report->revision_to_surat, $student->revision_report->revision_to_ayat);
                    }
                }
            }
        }


//        $class->appends(['numberofPages' => Request::get('search')])
//        $class = Classes::findOrFail($id);

        $programs = Program::all();

//        return view('education::monthlyPlan.create', compact('students','class','from_date','surats','teachers','hefz_valuation_options','revision_valuation_options'));
        return view('education::monthlyPlan.create', compact('class', 'from_date', 'surats', 'hefz_valuation_options', 'revision_valuation_options'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public
    function edit($class_id, $report_id)
    {
        $surats = MoshafSurah::all();

        $report = ClassReport::findOrFail($report_id);

        $class = Classes::findOrFail($report->class_id);
        $subject = [];
        $special_program_data = [];

        if ($report->subject_id == 0) {
            if ($report->program->setting['special_program_code']) {
                if ($report->program->setting['special_program_code'] = 'hefz') {
                    $special_program_data['data'] = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->evaluation($report);

                    $special_program_data['hefz_evaluation_schema'] = EvaluationSchema::where('target', 'hefz')->first();

                    $special_program_data['revision_evaluation_schema'] = EvaluationSchema::where('target', 'revision')->first();
                }
            }
        } else {
            $subject = Subject::findOrFail($report->subject_id);
        }

        $students = [];
        // return $special_program_data;
        // return $subject->contents;

        return view('education::classes.reports.edit', compact('class', 'report', 'students', 'subject', 'special_program_data', 'surats'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public
    function update(Request $request)
    {
        $requestData = $request->all();
        // return $requestData;

        $report = ClassReport::findOrFail($request->report_id);
        if (isset($request->teacher_attendance) && $request->teacher_attendance == 'absent') {
            $report->status = 'completed';
            $report->notes = 'teacher is absent';
            $report->save();

            return redirect('workplace/education/classes/' . $report->class_id . '/reports');
        } elseif (isset($request->student_attendance)) {
            foreach ($request->student_attendance as $key => $value) {
                $attendance = new StudentAttendance();

                $attendance->organization_id = config('organization_id');
                $attendance->class_report_id = $report->id;
                $attendance->student_id = $key;
                $attendance->class_time = $report->class_time;
                $attendance->attendance = $value;
                $attendance->created_by = auth()->user()->id;

                $attendance->save();
                // $attendance->note = $report->;
            }


            $report->status = 'attendance_submited';

            $report->save();
        } elseif (isset($request->student_performance)) {
            if ($report->subject_id == 0) {
                if ($report->program->setting['special_program_code']) {
                    if ($report->program->setting['special_program_code'] = 'hefz') {
                        $report_data = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->evaluation($report);
                        foreach ($request->student_performance as $student_id => $result) {
                            if (isset($report_data[$student_id])) {
                                if (isset($report_data[$student_id]['hefz']) && $report_data[$student_id]['hefz'] && $result['hefz']) {
                                    $hefz_report = new StudentHefzReport();

                                    $hefz_plans->student_id = $student_id;
                                    $hefz_plans->organization_id = config('organization_id');
                                    $hefz_plans->class_id = $report->class_id;
                                    $hefz_plans->class_time = $report->class_time;
                                    $hefz_plans->created_by = auth()->user()->id;
                                    // $hefz_plans->hefz_from_surat = $report_data[$student_id]['hefz']['from_surat'];
                                    // $hefz_plans->start_from_ayat = $report_data[$student_id]['hefz']['from_ayat'];
                                    // $hefz_plans->hefz_to_surat = $report_data[$student_id]['hefz']['to_surat'];
                                    // $hefz_plans->to_ayat = $report_data[$student_id]['hefz']['to_ayat'];

                                    $hefz_plans->hefz_from_surat = $requestData['report'][$student_id]['hefz']['from_surat'];
                                    $hefz_plans->start_from_ayat = $requestData['report'][$student_id]['hefz']['from_ayat'];
                                    $hefz_plans->hefz_to_surat = $requestData['report'][$student_id]['hefz']['to_surat'];
                                    $hefz_plans->to_ayat = $requestData['report'][$student_id]['hefz']['to_ayat'];

                                    $hefz_plans->hefz_evaluation_id = $result['hefz'];

                                    $hefz_plans->class_report_id = $report->id;


                                    $hefz_plans->save();
                                }
                                if (isset($report_data[$student_id]['revision']) && $report_data[$student_id]['revision'] && $result['revision']) {
                                    $revision_report = new StudentRevisionReport();

                                    $revision_report->student_id = $student_id;
                                    $revision_report->organization_id = config('organization_id');
                                    $revision_report->class_id = $report->class_id;
                                    $revision_report->created_by = auth()->user()->id;
                                    $revision_report->class_time = $report->class_time;
                                    // $revision_report->revision_from_surat = $report_data[$student_id]['revision']['from_surat'];
                                    // $revision_report->revision_from_ayat = $report_data[$student_id]['revision']['from_ayat'];
                                    // $revision_report->revision_to_surat = $report_data[$student_id]['revision']['to_surat'];
                                    // $revision_report->revision_to_ayat = $report_data[$student_id]['revision']['to_ayat'];

                                    $revision_report->revision_from_surat = $requestData['report'][$student_id]['revision']['from_surat'];
                                    $revision_report->revision_from_ayat = $requestData['report'][$student_id]['revision']['from_ayat'];
                                    $revision_report->revision_to_surat = $requestData['report'][$student_id]['revision']['to_surat'];
                                    $revision_report->revision_to_ayat = $requestData['report'][$student_id]['revision']['to_ayat'];

                                    if (isset($requestData['report'][$student_id]['revision']['revision_note'])) {
                                        $revision_report->revision_evaluation_note = $requestData['report'][$student_id]['revision']['revision_note'];
                                    }
                                    if (isset($requestData['report'][$student_id]['revision']['revision_type'])) {
                                        $revision_report->revision_type = $requestData['report'][$student_id]['revision']['revision_type'];
                                    }


                                    $revision_report->revision_evaluation_id = $result['revision'];

                                    $revision_report->class_report_id = $report->id;

                                    $revision_report->save();
                                }
                            }
                        }

                        $report->status = 'completed';

                        $report->save();
                    }
                }
            } else {
            }
        }

        // return $requestData;

        Session::flash('flash_message', 'Class updated!');

        return redirect('workplace/education/classes/' . $report->class_id . '/reports/' . $report->id . '/edit');
    }


    /**
     * Update programs availabilty in class.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public
    function programs(Request $request)
    {
        auth()->user()->can('edit class_programs');

        $id = $request->class_id;

        $class = Classes::findOrFail($id);

        $class->programs()->sync($request->class_programs);

        Session::flash('flash_message', 'Program updated!');

        if ($request->ajax()) {
            return response()->json(['status' => 'success']);
        }

        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public
    function destroy($id)
    {
        Classes::destroy($id);

        Session::flash('flash_message', 'Class deleted!');

        return redirect('workplace/education/classes');
    }


    public
    function studentReport($student_id)
    {
        return DB::table('student_attendances')
            ->leftJoin('class_reports', 'student_attendances.class_report_id', 'class_reports.id')
            ->leftJoin('student_hefz_report', 'student_attendances.class_report_id', '=', 'student_hefz_report.class_report_id')
            ->leftJoin('evaluation_schema_options as hefz_evaluation', 'hefz_evaluation.id', '=', 'student_hefz_report.hefz_evaluation_id')
            ->leftJoin('student_revision_report', 'student_attendances.class_report_id', '=', 'student_revision_report.class_report_id')
            ->leftJoin('evaluation_schema_options as revision_evaluation', 'revision_evaluation.id', '=', 'student_revision_report.revision_evaluation_id')
            ->select(
                'student_attendances.attendance',
                'student_attendances.note as student_attendance_note',
                'class_reports.*',
                'student_hefz_report.*',
                'hefz_evaluation.title as hefz_evaluation_title',
                'student_revision_report.*',
                'revision_evaluation.title as revision_evaluation_title'
            )
            ->where('student_attendances.student_id', $student_id)
            ->orderBy('class_reports.class_time')
            // ->select('users.*', 'contacts.phone', 'orders.price')
            ->get();
    }

    public
    function studentExpectedProgressPlan($student_id) // Hefz and Morja'ah Program
    {
    }

    private
    function getNextReportTime($last_report_time, $timetable, $class)
    {

        if (!$last_report_time) {
            $class_date = $timetable->start_at;
        } else {
            $class_date = Carbon::parse($last_report_time);
            $class_date = $class_date->addDay();
//            $class_date = $last_report_time->addDay();
        }

        while ($class->studentsAtDate($class_date)->count() < 1 && $class_date < Carbon::now()) {
            $class_date = $class_date->addDay();
        }

        while (!$timetable[strtolower($class_date->format('D'))]) {
            $class_date = $class_date->addDay();
        }
        $class_date = $class_date->addDay();
        // $class_date = $class_date->addDay();
        // dump($class_date);

        return $class_date;
    }

    private
    function errorNoTeacher($class_id)
    {
        $error_msg = "Opps! . Some Subjects In the Class Don't have Teacher!!";

        return view('education::classes.errors', compact('class_id', 'error_msg'));
    }

    private
    function errorNoTimetable($class_id)
    {
        $error_msg = "Opps! . Some Subjects In the Class Don't have Timetable!!";

        return view('education::classes.errors', compact('class_id', 'error_msg'));
    }


// V2

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public
    function prepareReport($class_id, $report_id)
    {
        $report = ClassReport::findOrFail($report_id);


        if ($report->status == 'completed') {
            return redirect('workplace/education/classes/' . $report->class_id . '/reports/' . $report->id . '/edit');
        }
        $surats = MoshafSurah::all();

        $suar = $surats->map(function ($surah) {
            return [
                'text' => $surah->name,
                'value' => $surah->id,
                'num_ayat' => $surah->num_ayat
            ];
        });


        $class = Classes::with('students.hefz')->with('students.revision')->findOrFail($report->class_id);


        $subject = [];
        $special_program_data = [];
        $special_program = null;

        $hefzEvaluationOptions = [];
        $revisionEvaluationOptions = [];

        if ($report->subject_id == 0 && $report->program->setting['special_program_code'] && $report->program->setting['special_program_code'] = 'hefz') {
            $special_program_data = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->studentsEvaluation($report);

            $hefzEvaluationOptions = EvaluationSchema::where('target', 'hefz')->first()->options->map(function ($option) {
                return ['text' => $option->code . ' - ' . $option->title, 'value' => $option->id];
            });

            $revisionEvaluationOptions = EvaluationSchema::where('target', 'revision')->first()->options->map(function ($option) {
                return ['text' => $option->code . ' - ' . $option->title, 'value' => $option->id];
            });

            $special_program = 'hefz';
        } else {
            $subject = Subject::findOrFail($report->subject_id);
        }
        $students = [];
        $lessons = [];
        // $class->students->map(function($student){
        //     return [

        //     ]
        // });

        foreach ($class->students as $student) {
            if ($special_program
                && $special_program == 'hefz'
                && isset($special_program_data[$student->id])
            ) {
                $students[$student->id] = $special_program_data[$student->id];
                $students[$student->id]['attandance'] = null;
                $students[$student->id]['evaluation'] = null;
            } else {
                $students[$student->id] = [
                    'attandance' => null,
                    'evaluation' => null,
                    'hefz' => [],
                    'revision' => [],
                    'lesson' => [
                        'id' => null,
                        'evaluations' => json_decode('{}')
                    ]
                ];
                if ($subject) {
                    $lessons = $subject->contents->map(function ($lesson) {
                        $evaluation_schems = [];
                        foreach ($lesson->evaluation_schemas as $evaluation) {
                            foreach ($evaluation->options as $option) {
                                $evaluation_schems[$evaluation->title][] = [
                                    'text' => $option->title,
                                    'value' => $option->id
                                ];
                            }
                        }

                        return [
                            'text' => $lesson->title,
                            'value' => $lesson->id,
                            'evaluation_schems' => $evaluation_schems
                        ];
                    });
                }
            }
        }


        return view('education::classes.reports.v3.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
//        return view('education::classes.reports.v2.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
//        return view('education::classes.reportsBackup.v2.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
    }

    public
    function storeTempReport($class_id, $report_id)
    {

        $report = ClassReport::findOrFail($report_id);
        $report->temp_data = json_encode(request()->except('_token'));
        $report->save();
    }

    public
    function storeFinalReport(Request $request, $class_id, $report_id)
    {
        $this->validate($request, [
            'students.*' => 'required',
            'teacher_attended' => 'required'
        ]);

        $report = ClassReport::findOrFail($report_id);

        StudentAttendance::where('class_report_id', $report->id)->delete();
        StudentHefzReport::where('class_report_id', $report->id)->delete();
        StudentRevisionReport::where('class_report_id', $report->id)->delete();


        $requestData = $request->all();

        if (!$request->teacher_attended) {
            $report->status = 'completed';
            $report->notes = 'teacher is absent';
            $report->temp_data = '';
            $report->save();

            return response()->json(['status' => 'completed'], 200);
        }

        foreach ($request->students as $studentID => $studentReport) {
            $attendance = new StudentAttendance();

            $attendance->organization_id = config('organization_id');
            $attendance->class_report_id = $report->id;
            $attendance->student_id = $studentID;
            $attendance->class_time = $report->class_time;
            $attendance->attendance = $studentReport['attandance'];
            $attendance->created_by = auth()->user()->id;
            $attendance->save();

            if (in_array($attendance->attendance, ['on_time', 'late'])) {
                if ($report->subject_id == 0 && $report->program->setting['special_program_code'] && $report->program->setting['special_program_code'] = 'hefz') {
                    if (isset($studentReport['hefz']) && $studentReport['hefz']) {
                        $hefz_report = new StudentHefzReport();
                        $hefz_plans->student_id = $studentID;
                        $hefz_plans->organization_id = config('organization_id');
                        $hefz_plans->class_id = $report->class_id;
                        $hefz_plans->class_time = $report->class_time;
                        $hefz_plans->created_by = auth()->user()->id;

                        $hefz_plans->hefz_from_surat = $studentReport['hefz']['from_surat'];
                        $hefz_plans->start_from_ayat = $studentReport['hefz']['from_ayat'];
                        $hefz_plans->hefz_to_surat = $studentReport['hefz']['to_surat'];
                        $hefz_plans->to_ayat = $studentReport['hefz']['to_ayat'];

                        $hefz_plans->hefz_evaluation_id = $studentReport['hefz']['evaluation'];
                        $hefz_plans->class_report_id = $report->id;

                        $hefz_plans->save();
                    }
                    if (isset($studentReport['revision']) && $studentReport['revision']) {
                        $revision_report = new StudentRevisionReport();

                        $revision_report->student_id = $studentID;
                        $revision_report->organization_id = config('organization_id');
                        $revision_report->class_id = $report->class_id;
                        $revision_report->created_by = auth()->user()->id;
                        $revision_report->class_time = $report->class_time;


                        $revision_report->revision_from_surat = $studentReport['revision']['from_surat'];
                        $revision_report->revision_from_ayat = $studentReport['revision']['from_ayat'];
                        $revision_report->revision_to_surat = $studentReport['revision']['to_surat'];
                        $revision_report->revision_to_ayat = $studentReport['revision']['to_ayat'];

                        if (isset($studentReport['revision']['revision_note'])) {
                            $revision_report->revision_evaluation_note = $studentReport['revision']['revision_note'];
                        }
                        if (isset($studentReport['revision']['revision_type'])) {
                            $revision_report->revision_type = $studentReport['revision']['revision_type'];
                        }


                        $revision_report->revision_evaluation_id = $studentReport['revision']['evaluation'];

                        $revision_report->class_report_id = $report->id;

                        $revision_report->save();
                    }
                } else {
                    // Todo: ADD REPORT OF NORMAL PROGRAM
                    if (isset($studentReport['lesson']) && $studentReport['lesson']) {
                        $lesson_report = new LessonReport();

                        $lesson_report->student_id = $studentID;
                        $lesson_report->organization_id = config('organization_id');
                        $lesson_report->class_id = $report->class_id;
                        $lesson_report->created_by = auth()->user()->id;
                        $lesson_report->class_time = $report->class_time;


                        $lesson_report->lesson_id = $studentReport['lesson']['id'];

                        if (isset($studentReport['lesson']['note'])) {
                            $lesson_report->note = $studentReport['revision']['note'];
                        }

                        $lesson_report->class_report_id = $report->id;
                        $lesson_report->save();
                        foreach ($studentReport['lesson']['evaluations'] as $label => $evaluation_option) {
                            $lesson_report->evaluations()->create(['evaluation_option_id' => $evaluation_option]);
                        }
                    }
                }
            }
        }
        $report->temp_data = '';
        $report->status = 'completed';
        $report->save();
        return response()->json(['status' => 'completed'], 200);
    }
}

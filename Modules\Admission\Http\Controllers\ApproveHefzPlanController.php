<?php

namespace Modules\Admission\Http\Controllers;

use App\Admission;
use App\AdmissionInterview;
use App\Http\Controllers\Controller;
use App\StudentAdmissionHefzPlan;
use App\StudentHefzPlan;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;
use App\Student;

class ApproveHefzPlanController extends Controller
{

    public function __invoke(Request $request)
    {



        try {
            DB::beginTransaction();

            // add student Number
//            $student = Student::find($request->student_id);
//            $student->student_number = $request->student_number;
//            $student->save();



            $planYearMonth = $request->hefz['start_date'];
            $dateMonthArray = explode('-', $planYearMonth);
            $year = $dateMonthArray[0];
            $month = $dateMonthArray[1];
            $planYearMonth = Carbon::createFromDate($year, $month, 1);
            $planYearMonth = $planYearMonth->format('Y-m');

            $plan = new StudentHefzPlan();

//            $plan = StudentAdmissionHefzPlan::find($request->plan_id);
            $plan->plan_year_and_month = $planYearMonth;
            $plan->start_date =  $request->hefz['start_date'];
//            $plan->status = 'active';
            $plan->class_id = $request->hefz['class_id'];
            $plan->study_direction = $request->hefz['study_direction'];
            $plan->start_date = Carbon::parse($request->hefz['start_date'])->toDateString();
            $plan->approved_by = auth()->user()->id;
            $plan->save();


//            $plan = StudentHefzPlan::find($request->plan_id);
//            $plan->plan_year_and_month = $planYearMonth;
//            $plan->start_date =  $request->hefz['start_date'];
//
//            $plan->status = 'active';
//            $plan->class_id = $request->hefz['class_id'];
//            $plan->study_direction = $request->hefz['study_direction'];
//            $plan->start_from_surat = $request->hefz['start_from_surat'];
//            $plan->start_from_ayat = $request->hefz['start_from_ayat'];
//            $plan->to_surat = $request->hefz['to_surat'];
//            $plan->to_ayat = $request->hefz['to_ayat'];
//            $plan->num_to_memorize = $request->hefz['num_to_memorize'];
//            $plan->memorization_mood = $request->hefz['memorization_mood'];
//            $plan->pages_to_revise = $request->hefz['pages_to_revise'];
//            $plan->start_date = Carbon::parse($request->hefz['start_date'])->toDateString();
//            $plan->approved_by = auth()->user()->id;
//            $plan->save();


            // update AdmissionInterview status

            $admissionId = Admission::where('student_id',$request->student_id)->first()->id;
            AdmissionInterview::where('admission_id',$admissionId)->update(
                [
                'status' => 'interviewed',
                'confirmed_at' => Carbon::now(),
                'updated_by' => auth()->user()->id
            ]);

            DB::commit();

            Toastr::success('Student Memorization Plan Approved !', 'Success');

            return redirect()->back();

        } catch (\Exception $e) {

            DB::rollback();

            Toastr::warning($e->getMessage(), 'Danger');
            return redirect()->back();
        }
    }

}

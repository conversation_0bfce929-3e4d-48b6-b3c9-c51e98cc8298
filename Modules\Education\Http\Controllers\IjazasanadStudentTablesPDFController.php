<?php

namespace Modules\Education\Http\Controllers;

use App\Classes;
use App\Student;
use App\StudentAttendance;
use App\ClassReport;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use App\Exports\IjazasanadStudentReportExport;
use App\Exports\IjazasanadClassReportExport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf as PDF;

class IjazasanadStudentTablesPDFController extends Controller
{

    public function __construct()
	{
//        $this->middleware('PM');
        // User::checkAuth();
	}



    public function downloadPDF(Request $request, $studentId, $monthYear, $levelOne = 0)
    {
        try {
            // Convert the monthYear string to a Carbon instance
            $date = \Carbon\Carbon::createFromFormat('F Y', $monthYear);
            $classId = $request->get('classId');

            // Extract the month and year separately
            $month = $date->format('m');
            $monthText = $date->format('M');
            $year = $date->format('Y');
            $letterHead = asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg");

            $student = Student::findOrFail($studentId);
            $class = Classes::findOrFail($classId);

            // Detect if this is Level 1 or Level 2 student
            $isLevel1 = $levelOne == "1" || $this->detectStudentLevelFromId($studentId) === 'level1';

            
            // Get student reports based on level type
            if ($isLevel1) {
                // Level 1: Query for lesson-based data (talqeen, revision, jazariyah, seminars)
                $studentReportDetails = \App\StudentIjazasanadMemorizationReport::where('student_id', $studentId)
                    ->where('class_id', $classId)
                    ->whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->where(function ($query) {
                        $query->whereNotNull('talqeen_from_lesson')
                              ->orWhereNotNull('revision_from_lesson')
                              ->orWhereNotNull('jazariyah_from_lesson')
                              ->orWhereNotNull('seminars_from_lesson');
                    })
                    ->with(['result', 'attendanceOptions'])
                    ->orderBy('created_at', 'asc')
                    ->get();
            } else {
                // Level 2: Query for Surah/Ayat-based data (hefz memorization)
                $studentReportDetails = \App\StudentIjazasanadMemorizationReport::where('student_id', $studentId)
                    ->where('class_id', $classId)
                    ->whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->whereNotNull('hefz_from_surat')
                    ->whereNotNull('hefz_from_ayat')
                    ->whereNotNull('hefz_to_surat')
                    ->whereNotNull('hefz_to_ayat')
                    ->with(['result', 'attendanceOptions'])
                    ->orderBy('created_at', 'asc')
                    ->get();
            }

            // Get student summary data for the PDF summary table
            $studentSummaryReportDetails = Student::where('id', $studentId)
                ->with(['ijazaMemorizationReport' => function ($query) use ($date, $classId, $isLevel1) {
                    if ($isLevel1) {
                        $query->where(function ($q) use ($date, $classId) {
                            $q->whereYear('student_ijazasanad_memorization_report.created_at', $date->year)
                                ->whereMonth('student_ijazasanad_memorization_report.created_at', $date->month)
                                ->where('student_ijazasanad_memorization_report.class_id', $classId)
                                ->where(function ($subQuery) {
                                    $subQuery->whereNotNull('talqeen_from_lesson')
                                             ->orWhereNotNull('revision_from_lesson')
                                             ->orWhereNotNull('jazariyah_from_lesson')
                                             ->orWhereNotNull('seminars_from_lesson');
                                });
                        })->with('result');
                    } else {
                        $query->where(function ($q) use ($date, $classId) {
                            $q->whereYear('student_ijazasanad_memorization_report.created_at', $date->year)
                                ->whereMonth('student_ijazasanad_memorization_report.created_at', $date->month)
                                ->where('student_ijazasanad_memorization_report.class_id', $classId)
                                ->whereNotNull('hefz_from_surat')
                                ->whereNotNull('hefz_from_ayat')
                                ->whereNotNull('hefz_to_surat')
                                ->whereNotNull('hefz_to_ayat');
                        })->with('result');
                    }
                }])
                ->with(['studentProgramLevels.programlevel'])
                ->with(['ijazasanad_memorization_plans' => function ($query) use ($date, $classId) {
                    $query->where(function ($q) use ($date, $classId) {
                        $q->whereYear('created_at', $date->year)
                            ->whereMonth('created_at', $date->month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    })->orWhere(function ($q2) use ($date, $classId) {
                        $q2->whereYear('start_date', $date->year)
                            ->whereMonth('start_date', $date->month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    });
                }])
                ->get();

            // Debug: Log query results
            \Log::info('Query Results Debug:', [
                'studentReportDetails_count' => $studentReportDetails->count(),
                'studentReportDetails_sample' => $studentReportDetails->take(2)->toArray(),
                'isLevel1' => $isLevel1,
                'date_year' => $date->year,
                'date_month' => $date->month
            ]);

            // Get summary data using level-aware logic
            $summaryData = $this->getSummaryData($studentId, $classId, $date->month, $date->year, $isLevel1);

            // Preload Surah names to avoid database queries in Blade template
            $surahNames = \App\MoshafSurah::pluck('eng_name', 'id')->toArray();

            $data = [
                'letterHead' => $letterHead,
                'studentName' => $student->full_name,
                'classTeachers' => $class->teachers()->pluck('full_name')->toArray(),
                'monthYear' => $date,
                'monthText' => $monthText,
                'centerName' => $class->center->name,
                'studentId' => $studentId,
                'classId' => $classId,
                'year' => $year,
                'month' => $month,
                'className' => $class->name,
                'summaryData' => $summaryData,
                'studentReportDetails' => $studentReportDetails, // Add detailed report data
                'studentSummaryReportDetails' => $studentSummaryReportDetails, // Add summary data
                'isLevel1' => $isLevel1, // Add level information
                'surahNames' => $surahNames, // Preloaded surah names
            ];

            // Determine which view to use
            $viewName = $isLevel1    
                ? 'education::classes.reports.pdf.student.ijazasanadLevel1All'
                : 'education::classes.reports.pdf.student.ijazasanadAll';

            // Debug: Log data to check what's being passed
            \Log::info('PDF Generation Debug:', [
                'studentId' => $studentId,
                'classId' => $classId,
                'monthYear' => $monthYear,
                'isLevel1' => $isLevel1,
                'studentName' => $data['studentName'] ?? 'NULL',
                'className' => $data['className'] ?? 'NULL',
                'centerName' => $data['centerName'] ?? 'NULL',
                'studentReportDetails_count' => isset($data['studentReportDetails']) ? $data['studentReportDetails']->count() : 'NULL',
                'studentReportDetails_data' => isset($data['studentReportDetails']) ? $data['studentReportDetails']->toArray() : 'NULL',
                'summaryData' => $data['summaryData'] ?? 'NULL',
                'surahNames_count' => isset($data['surahNames']) ? count($data['surahNames']) : 'NULL'
            ]);

            // Generate PDF with DOMPDF
            $pdf = PDF::loadView($viewName, $data)
                ->setPaper('a4', 'portrait');

            $filename = 'StudentReport_' . Str::slug($student->full_name) . '_' . Str::slug($class->name) . '_' . $date->format('Y_m') . '.pdf';
            return $pdf->stream($filename);

        } catch (\Exception $e) {
            // Log error and return response
            \Log::error('PDF Generation Error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Download Excel export of student Ijazasanad reports
     */
    public function downloadExcel(Request $request, $studentId, $monthYear, $levelOne = 0)
    {
       
        try {
            $classId = $request->get('classId');
            $student = Student::findOrFail($studentId);
            $class = Classes::findOrFail($classId);
            $date = Carbon::createFromFormat('F Y', $monthYear);

            // Detect if this is Level 1 or Level 2 student (same logic as PDF method)
            $isLevel1 = $levelOne == "1" || $this->detectStudentLevelFromId($studentId) === 'level1';

            $filename = 'IjazasanadReport_' . Str::slug($student->full_name) . '_' . Str::slug($class->name) . '_' . $date->format('Y_m') . '.xlsx';

            return Excel::download(
                new IjazasanadStudentReportExport($studentId, $classId, $monthYear, $isLevel1),
                $filename
            );

        } catch (\Exception $e) {
            \Log::error('Excel Export Error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Download Excel export of class Ijazasanad reports
     */
    public function downloadClassExcel(Request $request, $classId, $monthYear)
    {
        try {
            $class = Classes::findOrFail($classId);
            $date = Carbon::createFromFormat('F Y', $monthYear);

            $filename = 'IjazasanadClassReport_' . Str::slug($class->class_name) . '_' . $date->format('Y_m') . '.xlsx';

            return Excel::download(
                new IjazasanadClassReportExport($classId, $monthYear),
                $filename
            );

        } catch (\Exception $e) {
            \Log::error('Class Excel Export Error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get summary data for the student (level-aware logic)
     */
    private function getSummaryData($studentId, $classId, $month, $year, $isLevel1)
    {
        $student = Student::where('id', $studentId)
            ->with(['ijazaMemorizationReport' => function ($query) use ($month, $year, $classId, $isLevel1) {
                if ($isLevel1) {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('student_ijazasanad_memorization_report.created_at', $year)
                            ->whereMonth('student_ijazasanad_memorization_report.created_at', $month)
                            ->where('student_ijazasanad_memorization_report.class_id', $classId)
                            ->where(function ($subQuery) {
                                $subQuery->whereNotNull('talqeen_from_lesson')
                                         ->orWhereNotNull('revision_from_lesson')
                                         ->orWhereNotNull('jazariyah_from_lesson')
                                         ->orWhereNotNull('seminars_from_lesson');
                            });
                    })->with('result');
                } else {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('student_ijazasanad_memorization_report.created_at', $year)
                            ->whereMonth('student_ijazasanad_memorization_report.created_at', $month)
                            ->where('student_ijazasanad_memorization_report.class_id', $classId)
                            ->whereNotNull('hefz_from_surat')
                            ->whereNotNull('hefz_from_ayat')
                            ->whereNotNull('hefz_to_surat')
                            ->whereNotNull('hefz_to_ayat');
                    })->with('result');
                }
            }])
            ->with(['studentProgramLevels.programlevel'])
            ->with(['ijazasanad_memorization_plans' => function ($query) use ($month, $year, $classId) {
                $query->where(function ($q) use ($year, $month, $classId) {
                    $q->whereYear('created_at', $year)
                        ->whereMonth('created_at', $month)
                        ->where('class_id', $classId)
                        ->where('status', 'active');
                })->orWhere(function ($q2) use ($year, $month, $classId) {
                    $q2->whereYear('start_date', $year)
                        ->whereMonth('start_date', $month)
                        ->where('class_id', $classId)
                        ->where('status', 'active');
                });
            }])
            ->first();

        if (!$student) {
            return null;
        }

        // Calculate total reports
        $totalReports = $student->ijazaMemorizationReport->count();

        // Calculate attendance percentage (same for both levels)
        $attendanceData = $this->calculateAttendancePercentage($studentId, $classId, $month, $year, $isLevel1);

        if ($isLevel1) {
            // Level 1 specific calculations
            $level1Data = $this->calculateLevel1SummaryData($student->ijazaMemorizationReport);
            
            // Calculate completion rate using Level 1 logic
            $completionData = $this->calculateCompletionRate($student, $month, $year, $classId);

            return [
                'totalReports' => $totalReports,
                'attendancePercentage' => $attendanceData['percentage'],
                'attendanceDetails' => $attendanceData,
                'completionRate' => $completionData['percentage'],
                'completionDetails' => $completionData,
                'averagePerformance' => $level1Data['averagePerformance'],
                // Level 1 specific data
                'revision_plan' => "<span style='color: #b4eeb0;'>Level 1 Revision Plan</span>",
                'talqeen_plan' => "<span style='color: #b4eeb0;'>Level 1 Talqeen Plan</span>",
                'jazariyah_plan' => "<span style='color: #b4eeb0;'>Level 1 Jazariyah Plan</span>",
                'seminar_plan' => "<span style='color: #b4eeb0;'>Level 1 Seminar Plan</span>",
                'total_revision_lessons' => "<span style='color: #b4eeb0;'>{$level1Data['totalRevisionLessons']}</span>",
                'total_talqeen_lessons' => "<span style='color: #b4eeb0;'>{$level1Data['totalTalqeenLessons']}</span>",
                'total_jazariyah_lessons' => "<span style='color: #b4eeb0;'>{$level1Data['totalJazariyahLessons']}</span>",
                'total_seminar_lessons' => "<span style='color: #b4eeb0;'>{$level1Data['totalSeminarLessons']}</span>",
                'total_lessons' => "<span style='color: #b4eeb0;'>{$level1Data['totalLessons']}</span>",
                'revision_report' => "<span style='color: #b4eeb0;'>Revision Progress</span>",
                'talqeen_report' => "<span style='color: #b4eeb0;'>Talqeen Progress</span>",
                'jazariyah_report' => "<span style='color: #b4eeb0;'>Jazariyah Progress</span>",
                'seminar_report' => "<span style='color: #b4eeb0;'>Seminar Progress</span>",
                'revision_percentage' => "<span style='color: #b4eeb0;'>" . round($completionData['percentage'] / 4, 2) . '%</span>',
                'talqeen_percentage' => "<span style='color: #b4eeb0;'>" . round($completionData['percentage'] / 4, 2) . '%</span>',
                'jazariyah_percentage' => "<span style='color: #b4eeb0;'>" . round($completionData['percentage'] / 4, 2) . '%</span>',
                'seminar_percentage' => "<span style='color: #b4eeb0;'>" . round($completionData['percentage'] / 4, 2) . '%</span>',
            ];
        } else {
            // Level 2 specific calculations (existing logic)
            $totalPagesMemorized = $student->ijazaMemorizationReport->sum('pages_memorized');
            $averagePerformance = $this->calculateAveragePerformance($student->ijazaMemorizationReport);
            $completionData = $this->calculateCompletionRate($student, $month, $year, $classId);

            return [
                'totalReports' => $totalReports,
                'totalPagesMemorized' => $totalPagesMemorized,
                'averagePerformance' => $averagePerformance,
                'attendancePercentage' => $attendanceData['percentage'],
                'attendanceDetails' => $attendanceData,
                'completionRate' => $completionData['percentage'],
                'completionDetails' => $completionData,
            ];
        }
    }

    /**
     * Calculate Level 1 specific summary data
     */
    private function calculateLevel1SummaryData($reports)
    {
        $totalRevisionLessons = 0;
        $totalTalqeenLessons = 0;
        $totalJazariyahLessons = 0;
        $totalSeminarLessons = 0;

        foreach ($reports as $report) {
            if (!empty($report->revision_from_lesson) && !empty($report->revision_to_lesson)) {
                $totalRevisionLessons += max(0, $report->revision_to_lesson - $report->revision_from_lesson + 1);
            }
            if (!empty($report->talqeen_from_lesson) && !empty($report->talqeen_to_lesson)) {
                $totalTalqeenLessons += max(0, $report->talqeen_to_lesson - $report->talqeen_from_lesson + 1);
            }
            if (!empty($report->jazariyah_from_lesson) && !empty($report->jazariyah_to_lesson)) {
                $totalJazariyahLessons += max(0, $report->jazariyah_to_lesson - $report->jazariyah_from_lesson + 1);
            }
            if (!empty($report->seminars_from_lesson) && !empty($report->seminars_to_lesson)) {
                $totalSeminarLessons += max(0, $report->seminars_to_lesson - $report->seminars_from_lesson + 1);
            }
        }

        $totalLessons = $totalRevisionLessons + $totalTalqeenLessons + $totalJazariyahLessons + $totalSeminarLessons;
        
        // Calculate average performance for Level 1
        $averagePerformance = $this->calculateAveragePerformance($reports);

        return [
            'totalRevisionLessons' => $totalRevisionLessons,
            'totalTalqeenLessons' => $totalTalqeenLessons,
            'totalJazariyahLessons' => $totalJazariyahLessons,
            'totalSeminarLessons' => $totalSeminarLessons,
            'totalLessons' => $totalLessons,
            'averagePerformance' => $averagePerformance,
        ];
    }

    /**
     * Calculate average performance from reports
     */
    private function calculateAveragePerformance($reports)
    {
        if ($reports->isEmpty()) {
            return 0;
        }

        $totalWeightedScore = 0;
        $totalEvaluations = 0;

        foreach ($reports as $report) {
            if ($report->result && isset($report->result->weight)) {
                $totalWeightedScore += $report->result->weight;
                $totalEvaluations++;
            }
        }

        if ($totalEvaluations === 0) {
            return 0;
        }

        return round(($totalWeightedScore / $totalEvaluations) * 100, 1);
    }

    /**
     * Calculate attendance percentage - Level-aware logic
     */
    private function calculateAttendancePercentage($studentId, $classId, $month, $year, $isLevel1)
    {
        // Get the student with their memorization reports for the specific month/year/class
        $student = Student::where('id', $studentId)
            ->with(['ijazaMemorizationReport' => function ($query) use ($month, $year, $classId, $isLevel1) {
                if ($isLevel1) {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('student_ijazasanad_memorization_report.created_at', $year)
                            ->whereMonth('student_ijazasanad_memorization_report.created_at', $month)
                            ->where('student_ijazasanad_memorization_report.class_id', $classId)
                            ->where(function ($subQuery) {
                                $subQuery->whereNotNull('talqeen_from_lesson')
                                         ->orWhereNotNull('revision_from_lesson')
                                         ->orWhereNotNull('jazariyah_from_lesson')
                                         ->orWhereNotNull('seminars_from_lesson');
                            });
                    });
                } else {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('student_ijazasanad_memorization_report.created_at', $year)
                            ->whereMonth('student_ijazasanad_memorization_report.created_at', $month)
                            ->where('student_ijazasanad_memorization_report.class_id', $classId)
                            ->whereNotNull('hefz_from_surat')
                            ->whereNotNull('hefz_from_ayat')
                            ->whereNotNull('hefz_to_surat')
                            ->whereNotNull('hefz_to_ayat');
                    });
                }
            }])
            ->first();

        if (!$student) {
            return [
                'total_classes' => 0,
                'attended' => 0,
                'absent' => 0,
                'percentage' => 0,
            ];
        }

        // Use EXACT same logic as main controller for attendance calculation
        $reports = $student->ijazaMemorizationReport;
        $totalDays = $reports->count();
        $attendedDays = $reports->whereIn('attendance_id', [1, 2])->count(); // Late=1, Present=2
        $absentDays = $totalDays - $attendedDays;
        $percentage = $totalDays > 0 ? round(($attendedDays / $totalDays) * 100) : 0;

        return [
            'total_classes' => $totalDays,
            'attended' => $attendedDays,
            'absent' => $absentDays,
            'percentage' => $percentage,
        ];
    }

    /**
     * Calculate completion rate using the EXACT same logic as MonthEndIjazasanadStudentSummaryController
     */
    private function calculateCompletionRate($studentDetails, $month, $year, $classId)
    {
        $hefzPlan = $studentDetails->ijazasanad_memorization_plans->first();
        $reports = $studentDetails->ijazaMemorizationReport;
        
        if (!$hefzPlan) {
            return [
                'percentage' => 0,
                'tooltip' => 'No memorization plan found for this month.',
                'error' => true,
            ];
        }
    
        // Phase 1: Level Detection - using exact same logic as MonthEndIjazasanadStudentSummaryController
        $studentLevel = $this->detectStudentLevel($studentDetails);
    
        if ($studentLevel === 'level1') {
            // Phase 2: Calculate Level 1 Completion - using exact same logic
            $completionData = $this->calculateLevel1CompletionExact($hefzPlan, $reports);
            return [
                'percentage' => $completionData['completion_rate'],
                'tooltip' => $completionData['tooltip'],
                'error' => false,
            ];
        }
    
        // Phase 3: Calculate Level 2 Completion - using exact same logic
        $completionData = $this->calculateLevel2CompletionExact($hefzPlan, $reports);
        return [
            'percentage' => $completionData['completion_rate'],
            'tooltip' => $completionData['tooltip'],
            'error' => false,
        ];
    }

    /**
     * Detect student level - EXACT same logic as MonthEndIjazasanadStudentSummaryController
     */
    private function detectStudentLevel($studentDetails): ?string
    {
        // Eager load if not already loaded, though the main query should handle this.
        // Only call loadMissing if this is an Eloquent model
        if (method_exists($studentDetails, 'loadMissing')) {
            $studentDetails->loadMissing('studentProgramLevels.programlevel');
        }
    
        foreach ($studentDetails->studentProgramLevels as $studentProgramLevel) {
            if ($studentProgramLevel->programlevel) {
                $levelName = strtolower($studentProgramLevel->programlevel->title); // Use 'title' attribute from translations
                if (str_contains($levelName, 'level 1')) {
                    return 'level1';
                }
                if (str_contains($levelName, 'level 2')) {
                    return 'level2';
                }
            }
        }
        return null;
    }

    /**
     * Calculate Level 1 completion - EXACT same logic as MonthEndIjazasanadStudentSummaryController
     */
    private function calculateLevel1CompletionExact($plan, $reports): array
    {
        $components = [
            'talqeen',
            'revision',
            'jazariyah',
            'seminars'
        ];
    
        $componentDetails = [];
        $totalCompletion = 0;
        $validComponents = 0;
    
        foreach ($components as $componentName) {
            $fromField = "{$componentName}_from_lesson";
            $toField = "{$componentName}_to_lesson";
    
            if (!empty($plan->$fromField) && !empty($plan->$toField) && $plan->$fromField <= $plan->$toField) {
                $validComponents++;
                $plannedFrom = $plan->$fromField;
                $plannedTo = $plan->$toField;
                $plannedRangeCount = $plannedTo - $plannedFrom + 1;
    
                $completedLessons = $this->getUniqueCompletedLessonsForComponentExact($reports, $componentName);
                
                $plannedLessonsRange = range($plannedFrom, $plannedTo);
                $achievedInRange = count(array_intersect($completedLessons, $plannedLessonsRange));
                
                $componentProgress = ($plannedRangeCount > 0) ? ($achievedInRange / $plannedRangeCount) * 100 : 0;
                
                $totalCompletion += $componentProgress;
                $componentDetails[$componentName] = round($componentProgress, 2);
            } else {
                $componentDetails[$componentName] = 0;
            }
        }
    
        $overallCompletionRate = $validComponents > 0 ? ($totalCompletion / $validComponents) : 0;
    
        $tooltip = $validComponents === 0 ? "No valid lesson plans found" : null;
        if ($reports->count() === 0) {
            $tooltip = "No progress reports available";
        }
    
        return [
            'completion_rate' => round($overallCompletionRate, 2),
            'type' => 'level1',
            'components' => $componentDetails,
            'valid_components' => $validComponents,
            'tooltip' => $tooltip,
        ];
    }

    /**
     * Get unique completed lessons - EXACT same logic as MonthEndIjazasanadStudentSummaryController
     */
    private function getUniqueCompletedLessonsForComponentExact($reports, string $componentName): array
    {
        $allLessons = [];
        $fromField = "{$componentName}_from_lesson";
        $toField = "{$componentName}_to_lesson";
    
        foreach ($reports as $report) {
            if (!empty($report->$fromField) && !empty($report->$toField) && $report->$fromField <= $report->$toField) {
                $allLessons = array_merge($allLessons, range($report->$fromField, $report->$toField));
            }
        }
    
        return array_unique($allLessons);
    }

    /**
     * Calculate Level 2 completion - EXACT same logic as MonthEndIjazasanadStudentSummaryController
     */
    private function calculateLevel2CompletionExact($plan, $reports): array
    {
        $plannedPages = 0;
        $tooltip = null;
        
        // Check if we have valid hefz plan coordinates
        if (!empty($plan->start_from_surat) && !empty($plan->start_from_ayat) && 
            !empty($plan->to_surat) && !empty($plan->to_ayat)) {
            
            try {
                // Use existing page calculation logic from the controller
                if ($plan->study_direction == 'backward') {
                    $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                        $plan->start_from_surat,
                        $plan->start_from_ayat,
                        $plan->to_surat,
                        $plan->to_ayat
                    ]);
                    $plannedPages = $numberofPages[0]->numberofPagesSum ?? 0;
                } else {
                    // Forward direction
                    DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                        $plan->start_from_surat,
                        $plan->start_from_ayat,
                        $plan->to_surat,
                        $plan->to_ayat
                    ]);
                    $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                    $plannedPages = $results[0]->number_of_pages_sum ?? 0;
                }
            } catch (\Exception $e) {
                \Log::error('Error calculating planned pages for Level 2 student: ' . $e->getMessage());
                $plannedPages = 0;
                $tooltip = "Error calculating planned pages";
            }
        } else {
            $tooltip = "Incomplete hefz plan coordinates";
        }
        
        // Calculate achieved pages from reports
        $achievedPages = $reports->sum('pages_memorized') ?? 0;
        
        // Cap achieved pages at planned pages maximum to prevent percentage > 100%
        if ($plannedPages > 0 && $achievedPages > $plannedPages) {
            $achievedPages = $plannedPages;
        }
        
        // Calculate completion percentage
        $percentage = $plannedPages > 0 ? round(($achievedPages / $plannedPages) * 100) : 0;
        
        // Set tooltip for edge cases
        if ($reports->count() === 0) {
            $tooltip = "No progress reports available";
        } elseif ($plannedPages === 0 && !$tooltip) {
            $tooltip = "No valid hefz plan found";
        }
        
        return [
            'completion_rate' => $percentage,
            'type' => 'level2',
            'planned_pages' => $plannedPages,
            'achieved_pages' => $achievedPages,
            'tooltip' => $tooltip,
        ];
    }

    private function detectStudentLevelFromId($studentId)
    {
        $student = Student::findOrFail($studentId);
        return $this->detectStudentLevel($student);
    }
}
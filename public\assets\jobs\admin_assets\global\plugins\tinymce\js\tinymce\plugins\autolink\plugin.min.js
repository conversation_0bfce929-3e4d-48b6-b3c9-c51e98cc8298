!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=tinymce.util.Tools.resolve("tinymce.Env"),n=function(e){return e.getParam("autolink_pattern",/^(https?:\/\/|ssh:\/\/|ftp:\/\/|file:\/|www\.|(?:mailto:)?[A-Z0-9._%+\-]+@)(.+)$/i)},i=function(e){return e.getParam("default_link_target","")},o=function(e,t){if(t<0&&(t=0),3===e.nodeType){var n=e.data.length;t>n&&(t=n)}return t},r=function(e,t,n){1!==t.nodeType||t.hasChildNodes()?e.setStart(t,o(t,n)):e.setStartBefore(t)},a=function(e,t,n){1!==t.nodeType||t.hasChildNodes()?e.setEnd(t,o(t,n)):e.setEndAfter(t)},f=function(e,t,o){var f,s,d,l,c,u,g,h,C,m,y=n(e),k=i(e);if("A"!==e.selection.getNode().tagName){if((f=e.selection.getRng(!0).cloneRange()).startOffset<5){if(!(h=f.endContainer.previousSibling)){if(!f.endContainer.firstChild||!f.endContainer.firstChild.nextSibling)return;h=f.endContainer.firstChild.nextSibling}if(C=h.length,r(f,h,C),a(f,h,C),f.endOffset<5)return;s=f.endOffset,l=h}else{if(3!==(l=f.endContainer).nodeType&&l.firstChild){for(;3!==l.nodeType&&l.firstChild;)l=l.firstChild;3===l.nodeType&&(r(f,l,0),a(f,l,l.nodeValue.length))}s=1===f.endOffset?2:f.endOffset-1-t}for(d=s;r(f,l,s>=2?s-2:0),a(f,l,s>=1?s-1:0),s-=1," "!==(m=f.toString())&&""!==m&&160!==m.charCodeAt(0)&&s-2>=0&&m!==o;);var p;(p=f.toString())===o||" "===p||160===p.charCodeAt(0)?(r(f,l,s),a(f,l,d),s+=1):0===f.startOffset?(r(f,l,0),a(f,l,d)):(r(f,l,s),a(f,l,d)),"."===(u=f.toString()).charAt(u.length-1)&&a(f,l,d-1),(g=(u=f.toString().trim()).match(y))&&("www."===g[1]?g[1]="http://www.":/@$/.test(g[1])&&!/^mailto:/.test(g[1])&&(g[1]="mailto:"+g[1]),c=e.selection.getBookmark(),e.selection.setRng(f),e.execCommand("createlink",!1,g[1]+g[2]),k&&e.dom.setAttrib(e.selection.getNode(),"target",k),e.selection.moveToBookmark(c),e.nodeChanged())}},s=function(e){var n;e.on("keydown",function(t){13!==t.keyCode||f(e,-1,"")}),t.ie?e.on("focus",function(){if(!n){n=!0;try{e.execCommand("AutoUrlDetect",!1,!0)}catch(t){}}}):(e.on("keypress",function(t){41!==t.keyCode||f(e,-1,"(")}),e.on("keyup",function(t){32!==t.keyCode||f(e,0,"")}))};e.add("autolink",function(e){s(e)})}();
<?php

namespace App\Rules;

use App\User;
use Illuminate\Contracts\Validation\Rule;

class CheckDependentEmail implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {



        $email =   \DB::select("SELECT email
                                    FROM (
                                    SELECT  email
                                    FROM students
                                    WHERE email not IN (
                                    SELECT email
                                    FROM students
                                    WHERE 
                                    (email = ? OR email  IN (
                                    SELECT email
                                    FROM students
                                    WHERE guardian_id = (
                                    SELECT id
                                    FROM guardians
                                    WHERE email = ?))))) AS a
                                    WHERE a.email = ?",[\Auth::guard('web')->user()->email,\Auth::guard('web')->user()->email,$value]);




        if (count($email) > 0) { /* if another user with the dependant email already exists*/
            return false;
        } else {
            return true;
        }
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The :attribute is already taken. please try another email';
    }
}

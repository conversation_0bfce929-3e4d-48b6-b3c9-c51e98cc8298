<?php

namespace App\Debugbar;

use DebugBar\DataCollector\AssetProvider;
use DebugBar\DataCollector\DataCollector;
use DebugBar\DataCollector\Renderable;

class CopyToolsCollector extends DataCollector implements Renderable, AssetProvider
{
    public function collect()
    {
        // Expose minimal data. The UI is fully JS-driven; we keep PHP side light.
        return [
            'enabled' => true,
        ];
    }

    public function getName()
    {
        return 'copytools';
    }

    public function getWidgets()
    {
        // We don't rely on a dedicated widget; buttons are injected via JS for resilience.
        // Still, expose a tiny, non-intrusive panel to make the feature discoverable.
        return [
            'copytools' => [
                'icon' => 'copy',
                'widget' => 'PhpDebugBar.Widgets.VariableListWidget',
                'map' => 'copytools',
                'default' => '{}',
                'title' => 'Copy Tools',
            ],
        ];
    }

    public function getAssets()
    {
        // Read config toggles with sensible defaults
        $enablePerPanel = (bool) config('debugbar-copytools.enable_per_panel', true);
        $enableMaster = (bool) config('debugbar-copytools.enable_master_copy', true);

        $inline = [];
        $inline[] = 'window.__copytools_cfg = '.json_encode([
            'enable_per_panel' => $enablePerPanel,
            'enable_master_copy' => $enableMaster,
        ]).';';

        return [
            'css' => [
                '/vendor/copytools/copytools.css',
            ],
            'js' => [
                '/vendor/copytools/copytools.js',
            ],
            'inline_js' => $inline,
        ];
    }
}

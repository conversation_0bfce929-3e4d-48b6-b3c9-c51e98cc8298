<?php

namespace Modules\General\Console;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Modules\General\Services\JobNotificationMonitoringService;

class ProcessJobNotificationRetriesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'general:process-notification-retries {--limit=50 : Maximum number of retries to process}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending job notification retries';

    /**
     * @var JobNotificationMonitoringService
     */
    protected $monitoringService;

    /**
     * Create a new command instance.
     *
     * @param JobNotificationMonitoringService $monitoringService
     * @return void
     */
    public function __construct(JobNotificationMonitoringService $monitoringService)
    {
        parent::__construct();
        $this->monitoringService = $monitoringService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting job notification retry processing');
        Log::info('ProcessJobNotificationRetriesCommand: Started');
        
        $startTime = microtime(true);
        $limit = (int)$this->option('limit');
        
        try {
            $stats = $this->monitoringService->processRetries($limit);
            
            $this->info('Processed ' . $stats['processed'] . ' pending retries');
            $this->info('Scheduled ' . $stats['scheduled'] . ' new retry attempts');
            $this->info('Marked ' . $stats['max_retries_reached'] . ' failures as failed (max retries reached)');
            
            if ($stats['errors'] > 0) {
                $this->warn('Encountered ' . $stats['errors'] . ' errors during processing');
            }
            
            $executionTime = round((microtime(true) - $startTime), 2);
            $this->info("Completed in {$executionTime} seconds");
            
            Log::info('ProcessJobNotificationRetriesCommand: Completed', [
                'stats' => $stats,
                'execution_time' => $executionTime
            ]);
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Error processing retries: ' . $e->getMessage());
            Log::error('ProcessJobNotificationRetriesCommand: Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }
} 
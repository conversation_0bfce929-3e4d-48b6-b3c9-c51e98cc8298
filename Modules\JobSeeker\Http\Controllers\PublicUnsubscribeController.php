<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;

final class PublicUnsubscribeController extends BaseController
{
    /**
     * Public unsubscribe endpoint (placeholder).
     * For now, returns JSON only without modifying state.
     */
    public function unsubscribe(Request $request): JsonResponse
    {
        $email = (string) $request->query('email', '');
        $setupId = (int) $request->query('setup', 0);

        Log::info('JobSeeker unsubscribe link visited (placeholder)', [
            'email' => $email,
            'setup_id' => $setupId,
            'ip' => $request->ip(),
            'user_agent' => (string) $request->userAgent(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Unsubscribe endpoint reached. No action taken yet.',
            'data' => [
                'email' => $email,
                'setup_id' => $setupId,
            ],
        ]);
    }
}



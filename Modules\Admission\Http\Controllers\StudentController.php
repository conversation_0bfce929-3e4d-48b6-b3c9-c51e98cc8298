<?php

namespace Modules\Admission\Http\Controllers;

use App\Center;
use App\CenterTranslation;
use App\ClassProgram;
use App\ClassStudent;
use App\Country;
use App\IjazasanadMemorizationPlan;
use App\Notifications\SuperiorResetPassword;
use App\Notifications\UserStatusChangedToNewApplication;
use App\Organization;
use App\Program;
use App\ProgramLevel;
use App\Scopes\OrganizationScope;
use App\StudentAdmissionHefzPlan;
use App\StudentHefzReport;
use App\StudentIjazasanadMemorizationReport;
use App\StudentNouranyaPlan;
use App\StudentNouranyaReport;
use App\StudentProgramLevel;
use App\StudentRevisionPlan;
use App\StudentRevisionReport;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Faker\Factory;
use Faker\Provider\en_UG\PhoneNumber;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Mail\StudentCreated;
use App\Student;
use App\Guardian;
use App\Employee;
use App\Role;
use App\Cen_Emp;
use App\Admission;
use App\Classes;
use App\StudentHefzPlan;
use App\Services\StudentImageService;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Mail;
use PhpParser\Node\Stmt\Foreach_;
use Yajra\DataTables\Facades\DataTables;
use App\Services\StudentLevelService;
use Illuminate\Support\Facades\Cache;
use App\Facades\StudentImage;
use Modules\Admission\Http\Requests\UpdateStudentRequest;

class StudentController extends Controller
{
    protected $studentLevelService;
    protected $studentImageService;

    public function __construct(StudentLevelService $studentLevelService, StudentImageService $studentImageService)
    {
        $this->studentLevelService = $studentLevelService;
        $this->studentImageService = $studentImageService;
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Request $request)
    {
        // Begin request logging
        Log::info('StudentController@index - Request received', [
            'request_params' => $request->all(),
            'user_id' => auth()->id(),
            'user_ip' => $request->ip()
        ]);

        // Enable query log only in non-production environments to avoid performance impact
        if (config('app.env') !== 'production') {
            DB::connection()->enableQueryLog();
        }

        if ($request->ajax()) {
            try {
                // Log the specific filters being applied
                Log::info('StudentController@index - Processing filters', [
                    'name' => $request->filled('name') ? $request->get('name') : null,
                    'dateRange' => $request->filled('dateRange') ? $request->get('dateRange') : null,
                    'status' => $request->filled('status') ? $request->get('status') : null,
                    'programs' => $request->filled('programs') ? $request->get('programs') : null,
                    'centers' => $request->filled('centers') ? $request->get('centers') : null,
                    'hasImage' => $request->filled('hasImage') ? $request->get('hasImage') : null,
                    'minAge' => $request->filled('minAge') ? $request->get('minAge') : null,
                    'maxAge' => $request->filled('maxAge') ? $request->get('maxAge') : null,
                    'gender' => $request->filled('gender') ? $request->get('gender') : null,
                    'googleLogin' => $request->filled('googleLogin') ? $request->get('googleLogin') : null,
                ]);

                // Use caching for frequently accessed data with a reasonable TTL
                $cacheKey = 'students_datatable_' . md5(json_encode($request->all()));
                $cacheTTL = 5; // Cache for 5 minutes

                // Check if using cache
                $usingCache = !$request->filled('name') &&
                    !$request->filled('dateRange') &&
                    !$request->filled('status') &&
                    !$request->filled('programs') &&
                    !$request->filled('centers') &&
                    !$request->filled('hasImage') &&
                    !$request->filled('minAge') &&
                    !$request->filled('maxAge') &&
                    !$request->filled('gender') &&
                    !$request->filled('googleLogin') &&
                    config('app.env') === 'production';
                
                Log::info('StudentController@index - Cache status', [
                    'using_cache' => $usingCache,
                    'cache_key' => $cacheKey,
                    'cache_exists' => Cache::has($cacheKey)
                ]);

                // Only use cache if no specific search is being performed AND no other filters are active
                if ($usingCache) {
                    if (Cache::has($cacheKey)) {
                        Log::info('StudentController@index - Returning cached result');
                        return Cache::get($cacheKey);
                    }
                }

                // Base query with necessary joins using optimized eager loading
                $students = Admission::select([
                        'admissions.id',
                        'admissions.organization_id',
                        'admissions.student_id',
                        'admissions.center_id',
                        'admissions.class_id',
                        'admissions.created_at',
                        'admissions.status',
                        'admissions.deleted_at'
                    ])
                    ->where('admissions.organization_id', config('organization_id'))
                    ->whereHas('programs')
                    ->with(['programs' => function ($query) {
                        $query->select('programs.id', 'programs.status');
                        $query->with(['programTranslations' => function ($q) {
                            $q->select('program_id', 'title');
                        }]);
                    }])
                    ->whereHas('center')
                    ->with(['center' => function ($query) {
                        $query->select('centers.id', 'centers.location');
                        $query->with('translations');
                    }])
                    ->whereHas('student')
                    ->with(['class' => function ($query) {
                        $query->select('id', 'class_code', 'center_id');
                        $query->with('translations');
                    }])
                    ->with(['student' => function ($query) {
                        $query->select(
                            'students.id', 
                            'students.user_id', 
                            'students.full_name', 
                            'students.full_name_trans', 
                            'students.gender', 
                            'students.date_of_birth', 
                            'students.identity_number',
                            'students.email',
                            'students.mobile',
                            'students.mobile_2',
                            'students.nationality',
                            'students.student_photo'
                        );
                        $query->with(['user' => function ($q) {
                            $q->select('id', 'username', 'google_id');
                        }]);
                    }])
                    ->with(['interviews' => function ($query) {
                        $query->select('admission_interviews.id', 'admission_interviews.admission_id', 'admission_interviews.status');
                    }]);

                // Log query structure before any filters
                Log::info('StudentController@index - Base query structure created');

                // Move image filtering to top level query
                // use has() so "0" still counts as "present"
                if ($request->filled('hasImage')) {
                    $hasImage = $request->input('hasImage') === '1';
                    Log::info('StudentController@index - Filtering by image existence', ['hasImage' => $hasImage]);

                    $studentImageService = $this->studentImageService;
                    
                    // Get all students first to perform detailed filtering
                    $allAdmissions = $students->get();
                    Log::info('StudentController@index - Found admissions for image filtering', ['count' => $allAdmissions->count()]);
                    
                    // Filter based on image existence - this checks both DB and filesystem
                    $validIds = $allAdmissions
                        ->filter(function ($admission) use ($hasImage, $studentImageService) {
                            $student = $admission->student;
                            
                            // Skip if student is null (shouldn't happen, but just in case)
                            if (!$student) {
                                Log::warning('StudentController@index - Found admission without student', ['admission_id' => $admission->id]);
                                return false;
                            }
                            
                            // This checks both database value and actual file existence
                            $hasCustomImage = $studentImageService->hasCustomImage($student);
                            
                            Log::debug('StudentController@index - Image check result', [
                                'student_id' => $student->id,
                                'has_custom_image' => $hasCustomImage,
                                'filter_value' => $hasImage,
                                'included_in_results' => $hasImage === $hasCustomImage
                            ]);
                            
                            // Return true if the filter matches (has image when we want images, or doesn't when we don't)
                            return $hasImage === $hasCustomImage;
                        })
                        ->pluck('id')
                        ->toArray();

                    Log::info('StudentController@index - After image filtering', ['matching_count' => count($validIds)]);
                    
                    // Apply the filter
                    $students->whereIn('admissions.id', $validIds);
                }


                // Role-based access filtering
                if (!auth()->user()->hasRole('managing-director_'.config('organization_id').'_')) {
                    // Get centers the supervisor is assigned to
                    $centerIds = auth()->user()->center()
                        ->select('centers.id')
                        ->pluck('centers.id')
                        ->toArray();
                    
                    if (!empty($centerIds)) {
                        Log::info('StudentController@index - Applying role-based center filter', [
                            'user_role' => auth()->user()->getRoleNames(),
                            'center_ids' => $centerIds
                        ]);
                        $students->whereIn('admissions.center_id', $centerIds);
                    }
                }

                $students->orderBy('admissions.created_at', 'desc');
                
                // Google login filter optimization
                if ($request->filled('googleLogin')) {
                    $googleLogin = $request->get('googleLogin');
                    Log::info('StudentController@index - Applying googleLogin filter', [
                        'googleLogin' => $googleLogin
                    ]);
                    
                    if ($googleLogin === 'enabled') {
                        $students->whereHas('student.user', function ($query) {
                            $query->whereNotNull('google_id');
                        });
                    } else if ($googleLogin === 'disabled') {
                        $students->whereHas('student.user', function ($query) {
                            $query->whereNull('google_id');
                        });
                    }
                }

                return DataTables::eloquent($students)
                    ->filter(function ($query) use ($request) {
                        // Status filtering optimization
                        if ($request->filled('status')) {
                            $status = $request->get('status');
                            Log::info('StudentController@index - Applying status filter', [
                                'status' => $status
                            ]);

                            switch ($status) {
                                case 'archived':
                                    $query->onlyTrashed();
                                    break;

                                case 'waiting_for_approval':
                                    $query->whereHas('student.hefz_plans', function ($q) {
                                        $q->where('student_hefz_plans.status', 'waiting_for_approval');
                                    });
                                    break;

                                case 'waiting_for_interview':
                                    $query->whereHas('interviews', function ($query2) {
                                        $query2->where('admission_interviews.status', 'waiting_for_interview');
                                    });
                                    break;

                                case 'active':
                                case 'new_admission':
                                case 'New Admission':
                                case 'rejected':
                                case 'accepted':
                                    $query->where('admissions.status', $status);
                                    break;
                            }
                        }

                        // Program filtering optimization
                        if ($request->filled('programs')) {
                            Log::info('StudentController@index - Applying programs filter', [
                                'program_id' => $request->get('programs')
                            ]);
                            
                            $query->whereHas('programs', function ($query) {
                                $query->where('programs.id', request('programs'));
                            });
                        }

                        // Center filtering optimization
                        if ($request->filled('centers')) {
                            Log::info('StudentController@index - Applying centers filter', [
                                'center_id' => $request->get('centers')
                            ]);
                            
                            $query->where('admissions.center_id', request('centers'));
                        }

                        // Optimize full-name search
                        if ($request->filled('name')) {
                            $searchTerm = $request->get('name');
                            Log::info('StudentController@index - Applying name search filter', [
                                'search_term' => $searchTerm
                            ]);
                            
                            // Optimize name search to use indexes effectively
                            $query->where(function ($q) use ($searchTerm) {
                                $q->whereHas('student', function ($subQuery) use ($searchTerm) {
                                    $subQuery->where(function ($nameQuery) use ($searchTerm) {
                                        $nameQuery->where('students.full_name', 'LIKE', "%{$searchTerm}%")
                                            ->orWhere('students.full_name_trans', 'LIKE', "%{$searchTerm}%");
                                    });
                                })
                                ->orWhereHas('student.user', function ($subQuery) use ($searchTerm) {
                                    $subQuery->where(function ($nameQuery) use ($searchTerm) {
                                        $nameQuery->where('users.full_name', 'LIKE', "%{$searchTerm}%")
                                            ->orWhere('users.full_name_trans', 'LIKE', "%{$searchTerm}%");
                                    });
                                });
                            });
                        }

                        // Date range filtering optimization
                        if ($request->filled('dateRange')) {
                            $range = explode(",", $request->get('dateRange'));
                            Log::info('StudentController@index - Applying dateRange filter', [
                                'date_range' => $range
                            ]);
                            
                            $query->whereBetween('admissions.created_at', $range);
                        }

                        // Age range filtering optimization
                        if ($request->filled('minAge') && $request->filled('maxAge')) {
                            $minDate = Carbon::now()->subYear($request->get('minAge'))->toDateString();
                            $maxDate = Carbon::now()->subYear($request->get('maxAge'))->toDateString();
                            
                            Log::info('StudentController@index - Applying age range filter', [
                                'min_age' => $request->get('minAge'),
                                'max_age' => $request->get('maxAge'),
                                'min_date' => $minDate,
                                'max_date' => $maxDate
                            ]);
                            
                            $query->ageBetween($maxDate, $minDate);
                        }

                        // Gender filtering optimization
                        if ($request->filled('gender')) {
                            Log::info('StudentController@index - Applying gender filter', [
                                'gender' => $request->get('gender')
                            ]);
                            
                            $query->whereHas('student', function ($query) {
                                $query->where('students.gender', request('gender'));
                            });
                        }
                    }, true)
                    ->addIndexColumn()
                    ->addColumn('google_login_enabled', function ($student) {
                        return !empty($student->student->user->google_id) ? 'Enabled' : 'Disabled';
                    })
                    ->addColumn('identity_number', function ($row) {
                        return Str::upper($row->student->identity_number);
                    })
                    ->addColumn('nationality', function ($row) {
                        return Str::upper($row->student->nationality);
                    })
                    ->addColumn('status', function ($row) use ($request) {

                       
                        $status = $row->status;
                       
                        if ($row->status === 'waiting_for_interview') {
                            $status = "Waiting for Interview";
                        }
                        if ($row->status === 'new_admission') {
                            $status = "New";
                        }
                        
                        // Define status colors
                        $statusColors = [
                            'active' => '#28a745',
                            'new_admission' => '#17a2b8',
                            'waiting_for_interview' => '#ffc107',
                            'archived' => '#6c757d',
                            'rejected' => '#dc3545'
                        ];
                        
                        $color = $statusColors[strtolower($status)] ?? '#6c757d';
                        
                        return '<span class="status-badge" style="
                            background-color: ' . $color . ';
                            color: white;
                            padding: 5px 12px;
                            border-radius: 15px;
                            font-size: 12px;
                            font-weight: 500;
                            display: inline-block;
                            text-transform: capitalize;
                        ">' . $status . '</span>';
                    })
                    ->addColumn('full_name', function ($row) {
                        return $row->student->full_name;
                    })
                    ->addColumn('email', function ($row) {
                        return $row->student->email;
                    })
                    ->addColumn('mobile', function ($row) {
                        return $row->student->mobile;
                    })
                    ->addColumn('full_name_trans', function ($row) {
                        $stShowRoute = route('students.show', ['id' => $row->student->user_id]);
                        $genderColor = $row->student->gender == 'Male' || $row->student->gender == 'male' ? '#34b8bc;!important' : '#FA5661;!important';
                        $fullname = Str::title($row->student->full_name_trans);
                        
                        if (strlen($row->student->full_name_trans) > 13) {
                            return '<a style="color:' . $genderColor . '" target="_blank" href="' . $stShowRoute . '" ><strong data-tooltip="' . Str::title($row->student->full_name_trans) . '" >' . $fullname . '</strong></a>';
                        } else {
                            return '<a style="color:' . $genderColor . '" target="_blank" href="' . $stShowRoute . '" ><strong data-tooltip="' . Str::title($row->student->full_name_trans) . '" style="color:"' . $genderColor . '">' . $fullname . '</strong></a>';
                        }
                    })
                    ->editColumn('date_of_birth', function ($row) {
                        $birthDate = \Carbon\Carbon::parse($row->student->date_of_birth);
                        return $birthDate->toDateString();
                    })
                    ->addColumn('image', function ($row) {
                        $stShowRoute = route('students.show', ['id' => $row->student->user->id]);
                        $genderColor = $row->student->gender == 'Male' || $row->student->gender == 'male' ? '#34b8bc;!important' : '#FA5661;!important';
                        
                        $studentName = Str::title($row->student->full_name);
                        // Handle long student names with truncation and tooltip
                        if (strlen($studentName) > 24) {
                            $displayName = Str::limit($studentName, 24, ' ...');
                            $studentNameHtml = '<a style="color:' . $genderColor . '" target="_blank" href="' . $stShowRoute . '">
                                <strong style="font-size: 18px;" data-tooltip="' . $studentName . '" data-student-full-name="' . $studentName . '">' . $displayName . '</strong>
                            </a>';
                        } else {
                            $studentNameHtml = '<a style="color:' . $genderColor . '" target="_blank" href="' . $stShowRoute . '">
                                <strong style="font-size: 18px;" data-student-full-name="' . $studentName . '">' . $studentName . '</strong>
                            </a>';
                        }

                        // Action Menu Items for student name hover
                        $hoverActionMenuItems = [];
                        if (is_null($row->deleted_at)) {
                            $impersonationRoute = route('multiAuthImpersonate', [
                                'id' => $row->student()->first()->user_id,
                                'guardName' => 'web'
                            ]);
                            $hoverActionMenuItems[] = '<a href="'.$impersonationRoute.'" target="_blank" class="dropdown-item" title="Login as Student"><i class="sign in alternate icon"></i> Login</a>';
                        }
                        if (!(request()->filled('status') && request()->get('status') == 'archived')) {
                            // Ensure catid is student->id for delete action
                            $hoverActionMenuItems[] = '<a href="javascript:void(0);" class="dropdown-item delete-action" data-admissionid="'.$row->id.'" data-catid="'.$row->student->id.'" title="Delete Student"><i class="trash icon"></i> Delete</a>';
                        }
                        if (request()->get('status') == 'new_admission' || $row->student->status == 'New Admission' || $row->status == 'New') {
                            $hoverActionMenuItems[] = '<a href="javascript:void(0);" class="dropdown-item add-to-halaqa" data-student_id="'.$row->student->id.'" title="Add to Halaqa"><i class="plus icon"></i> Add to Halaqa</a>';
                        }
                         // View option for hover menu
                        $hoverActionMenuItems[] = '<a href="'.$stShowRoute.'" target="_blank" class="dropdown-item" title="View Student"><i class="eye icon"></i> View</a>';

                        $hoverActionsMenuHtml = '';
                        if (!empty($hoverActionMenuItems)) {
                            $hoverActionsMenuHtml = '<div class="action-menu-on-name-hover">' . implode('', $hoverActionMenuItems) . '</div>';
                        }

                        $nameWithActionsWrapper = '<div class="student-name-action-trigger">' . $studentNameHtml . $hoverActionsMenuHtml . '</div>';

                        $email = Str::title($row->student->email);
                        $emailHtml = '';
                        if (strlen($row->student->email) > 24) {
                            $email = Str::limit(Str::title($email), 24, ' ...');
                            $emailHtml .= '<span data-tooltip="' . Str::title($row->student->email) . '" style=" font-size: 12px;">' . $email . '</span>';
                        } else {
                            $emailHtml .= '<span style=" font-size: 12px;">' . $email . '</span>';
                        }
                        
                        $applicationDateHtml = value($row['created_at'])->diffForHumans();
                        $image = $row->student ? $this->studentImageService->getStudentImageUrl($row->student) : asset('maleStudentProfilePicture.png');
                        
                        return '<div style="display: flex; align-items: center; gap: 15px;">
                            <div style="flex-shrink: 0;">
                                <img class="studentImage" style="border-radius: 50%; width: 60px; height: 60px; object-fit: cover;" src="' . $image . '">
                            </div>
                            <div style="flex-grow: 1;">
                                <div style="font-weight: bolder; margin-bottom: 4px;">
                                    ' . $nameWithActionsWrapper . ' 
                                </div>
                                <div style="color: #E2EEFF; margin-bottom: 2px;">
                                    <i class="envelope icon"></i> ' . $emailHtml . '
                                </div>
                                <div style="color: #E2EEFF; font-size: 11px;">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span><img src="' . asset("assets/workplace/icons/usernameicon.png") . '" alt="username icon" style="width: 14px; height: 14px; vertical-align: middle;"> ' . $row->student->user->username . '</span>
                                        <span><i class="phone icon"></i> ' . $row->student->mobile . '</span>
                                        <span data-tooltip="' . value($row['created_at'])->format('Y-d-M  g:i a') . '"><i class="calendar alternate outline icon"></i> Joined ' . $applicationDateHtml . '</span>
                                    </div>
                                </div>
                            </div>
                        </div>';
                    })
                    ->addColumn('center', function ($row) {
                        // Retrieve the center name in English (limited to 30 characters)
                        $centerName = $row->center->translations
                            ->filter(function ($translation) {
                                return $translation->locale == 'en';
                            })
                            ->map(function ($translation) {
                                return Str::limit($translation->name, 30, '...');
                            })
                            ->implode('<br>');

                        // Create change icon
                        $changeIcon = '<img src="' . asset('assets/workplace/icons/studentTransferfromCentertoCenter.png') . '" alt="Transfer Icon" class="change-center-btn" data-current-class-id="' . $row->class_id . '" data-student-id="' . $row->student_id . '" data-current-center-id="' . $row->center_id . '" data-current-center-name="' . strip_tags($centerName) . '" />';

                        return $centerName . '&nbsp;&nbsp;' . $changeIcon;
                    })
                    ->addColumn('program', function ($row) {
                        return $row->programs->map(function ($program) {
                            return Str::limit($program->programTranslations->first()->title, 30, '...');
                        })->implode('<br>');
                    })
                    ->addColumn('class', function ($row) {
                        $className = $row->class->translations
                            ->filter(function ($translation) { return $translation->locale == 'en'; })
                            ->first();
                        if (!$className) { $className = $row->class->translations->first(); }
                        $displayName = $className ? $className->name : $row->class->class_code;
                        $monthlyPlanRoute = route('monthly-plan.show', ['id' => $row->class_id, 'from_date' => Carbon::now()->format('Y-m-d')]);
                        $dailyReportRoute = route('reports.create', ['id' => $row->class_id, 'from_date' => Carbon::now()->format('Y-m-d')]);
                        return '<div class="class-actions-wrapper">
                                    <span class="class-name">' . $displayName . '</span>
                                    <div class="class-actions-menu">
                                        <a href="' . $monthlyPlanRoute . '" class="class-action-item"><i class="calendar icon"></i> Monthly Plan</a>
                                        <a href="' . $dailyReportRoute . '" class="class-action-item"><i class="file alternate icon"></i> Daily Report</a>
                                    </div>
                                </div>';
                    })
                    ->addColumn('mobile', function ($row) {
                        $mobileNumbers = '';
                        if ($row->student->mobile) {
                            $mobileNumbers .= '<a class="ui label">' . $row->student->mobile . '</a>&nbsp;';
                        }
                        if ($row->student->mobile_2) {
                            $mobileNumbers .= '<a class="ui  label">' . $row->student->mobile_2 . '</a>&nbsp;';
                        }
                        return $mobileNumbers;
                    })
                    
                    ->rawColumns(['status','center', 'login', 'mobile', 'created_at', 'full_name', 'full_name_trans', 'email', 'image', 'date_of_birth', 'class'])
                    ->make(true);

                // Log the executed SQL queries after making the DataTables response
                if (config('app.env') !== 'production') {
                    // Log the executed SQL queries
                    $queries = DB::getQueryLog();
                    Log::info('StudentController@index - Generated queries', [
                        'query_count' => count($queries),
                        'last_query' => end($queries),
                        'all_queries' => $queries
                    ]);
                }
                
                Log::info('StudentController@index - DataTables response generated');
                
            } catch (\Exception $e) {
                Log::error("StudentController@index - DataTables error", [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                    'request' => $request->all()
                ]);
                
                return response()->json([
                    'draw' => $request->input('draw', 1),
                    'recordsTotal' => 0,
                    'recordsFiltered' => 0,
                    'data' => [],
                    'error' => 'An error occurred while processing your request. Please try again or contact support.'
                ]);
            }
        }

        // Cache missing students for 30 minutes (using repository pattern would be ideal for production)
        $missingStudents = Cache::remember('missing_students', now()->addMinutes(30), function () {
            return User::with('student')
                ->whereHas('student', function($query) {
                    $query->whereDoesntHave('admission');
                })
                ->orderBy('full_name')
                ->get();
        });

        // Cache programs for 1 day
        $programs = Cache::remember('programs_active_with_translations', now()->addDay(), function () {
            $employee = auth()->guard('employee')->user();

            // If managing director, return all active programs with translations.
            if ($employee->hasRole('managing-director_' . config('organization_id') . '_')) {
                return Program::select('id', 'status')
                    ->where('status', 'active')
                    ->with('translations')
                    ->get();
            } else {
                // For supervisors, fetch only programs from centers the employee supervises.
                $centerIds = $employee->center()->pluck('centers.id')->toArray();

                return Program::select('id', 'status')
                    ->where('status', 'active')
                    ->whereHas('centers', function ($query) use ($centerIds) {
                        $query->whereIn('centers.id', $centerIds);
                    })
                    ->with('translations')
                    ->get();
            }
        });

        $filter_name = $request->filter_name;
        $filter = $request->filter;
        $perPage = 25;

        $students = Student::orderBy('id', 'DESC');

        Log::info('StudentController@index - Rendering view', [
            'missing_students_count' => $missingStudents->count(),
            'programs_count' => $programs->count()
        ]);

        return view("admission::student.index", compact('missingStudents', 'students', 'filter_name', 'filter', 'programs'));
    }

    public function changeHalaqah(Request $request)
    {
        // Validate the incoming request.
        $this->validate($request, [
            'student_id'         => 'required',
            'halaqah'            => 'required', // Current Halaqah ID
            'new_halaqah_id'     => 'required',
            'new_center_id'      => 'required',
            'new_program_id'     => 'required',
            'new_program_title'  => 'required'
        ]);

        try {
            DB::beginTransaction();

            $studentId       = $request->student_id;
            $currentHalaqah  = $request->halaqah;
            $newHalaqah      = $request->new_halaqah_id;
            $newCenterId     = $request->new_center_id;
            $newProgramId    = $request->new_program_id;
            $newProgramTitle = $request->new_program_title;

            // Hard-delete any existing ClassStudent records for the current Halaqah.
            $existingRecord = ClassStudent::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah);
            if ($existingRecord->exists()) {
                $existingRecord->forceDelete();
            }

            // Get the new Halaqah's associated ClassProgram.
            $classProgram = ClassProgram::where('class_id', $newHalaqah)->first();
            if (!$classProgram) {
                throw new \Exception("No class program found for the new Halaqah.");
            }

            // Retrieve the student's existing admission record (even trashed).
            $existingAdmission = Admission::withTrashed()->where('student_id', $studentId)->first();
            if (!$existingAdmission) {
                throw new \Exception("No existing admission found for student ID {$studentId}.");
            }
            // If the record is soft-deleted, restore it.
            if ($existingAdmission->trashed()) {
                $existingAdmission->restore();
            }

            // Get roles of the authenticated user.
            $rolesString = auth()->user()->getRoleNames()->implode(',');

            // Prepare a critical data array (includes values previously entered by the student).
            $criticalData = [
                'gender_id'       => $existingAdmission->gender_id,
                'student_email'   => $existingAdmission->student_email,
                'student_mobile'  => $existingAdmission->student_mobile,
                'date_of_birth'   => $existingAdmission->date_of_birth,
                'age'             => $existingAdmission->age,
                'organization_id' => $existingAdmission->organization_id ?? config('organization_id'),
                // Capture previous center, program, and class from the existing admission.
                'old_center_id'       => $existingAdmission->center_id,
                'old_program_id'      => $existingAdmission->program_id,
                'old_class_id'        => $existingAdmission->class_id,
            ];

            // Update the existing admission with new details (merging in the critical data).
            $existingAdmission->update(array_merge([
                'program_id'   => $newProgramId,
                'center_id'    => $newCenterId,
                'class_id'     => $newHalaqah,
                'created_by'   => auth()->user()->id,
                'status'       => 'active',
                'creator_role' => $rolesString,
            ], $criticalData));


            // TODO : admission center should be synced

            // Sync the new program with the admission.
            $existingAdmission->programs()->sync([
                $newProgramId => ['created_at' => Carbon::now()]
            ]);




            // Update student level based on new program using the service
            $newProgram = Program::find($newProgramId);
            if ($newProgram) {
                $student = Student::find($studentId); // Ensure student model is fetched
                if ($student) {
                    try {
                        $this->studentLevelService->assignInitialLevel($student, $newHalaqah, $newProgram);
                    } catch (\Exception $e) {
                        Log::error("Failed to assign level during halaqah change: " . $e->getMessage(), ['student_id' => $studentId, 'new_class_id' => $newHalaqah]);
                        // Decide how to handle this error in the context of the transaction
                        // Maybe throw the exception again to rollback, or log and continue?
                        // For now, rethrow to ensure transaction rollback on level assignment failure.
                        throw $e;
                    }
                }
                 else {
                    Log::error('Student not found during halaqah change level assignment.', ['student_id' => $studentId]);
                    // Throw an exception or handle appropriately
                }
            }

            // Build the delete reason message.
            $currentClassName = optional(Classes::find($currentHalaqah))->name;
            $newClassName     = optional(Classes::find($newHalaqah))->name;
            $deleteReason     = "Student transferred from Halaqah {$currentClassName} to Halaqah {$newClassName}";

            // Soft-delete related reports and plans.
            StudentHefzReport::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            StudentHefzPlan::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            StudentRevisionReport::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            StudentRevisionPlan::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);

            // Also soft-delete Nouranya and Ijazasanad related reports and plans.
            StudentNouranyaReport::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            StudentNouranyaPlan::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            StudentIjazasanadMemorizationReport::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            IjazasanadMemorizationPlan::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);

            // Hard-delete all old ClassStudent entries for the student.
            ClassStudent::where('student_id', $studentId)->forceDelete();

            // Create a new ClassStudent record for the new Halaqah.
            ClassStudent::create([
                'student_id' => $studentId,
                'class_id'   => $newHalaqah,
                'start_date' => Carbon::now()->toDateString(),
                'created_at' => Carbon::now(),
                'added_at'   => Carbon::now()
            ]);

            // Update the student's status to 'active'.
            Student::where('id', $studentId)->update(['status' => 'active']);

            // Use the critical data from the old admission for logging.
            $oldCenterId  = $criticalData['old_center_id'];
            $oldProgramId = $criticalData['old_program_id'];
            $oldClassId   = $criticalData['old_class_id'];

            $logMessage = "Student Transfer Log: Student ID {$studentId} transferred from Center [{$oldCenterId}], Program [{$oldProgramId}], Class [{$oldClassId}] " .
                "to Center [{$newCenterId}], Program [{$newProgramId}], Class [{$newHalaqah}]. " .
                "Operations performed: Updated existing Admission record (and restored it, if trashed) with transferred critical data, synced program, updated student status, updated program level (if applicable), and soft-deleted related reports and plans. " .
                "Performed by User ID: " . auth()->user()->id . " (Roles: {$rolesString}). " .
                "Timestamp: " . Carbon::now()->toDateTimeString();

            \Log::info($logMessage);
            DB::commit();

            return response()->json(['message' => 'Student transferred successfully'], 201);
        } catch (\Exception $exception) {
            \Log::error($exception);
            DB::rollBack();
            return response()->json(['message' => $exception->getMessage()], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view("admission::student.create");
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {

    

        if (!(auth()->guard('guardian')->check() || auth()->user()->can('add student'))) {
            // return back with warining
            flash('You do not have permission to add new student');
        }
        $this->validation($request);
        if (auth()->guard('guardian')->check() || auth()->guard('employee')->check()) {
            $request->merge([
                "guardian_id" => auth()->user()->id,
            ]);
            $request->merge([
                "password" => \Illuminate\Support\Str::random(8)
            ]);
        }
        $request->merge([
            "organization_id" => config('organization_id'),
        ]);

        $student = Student::create($request->all());

        // Mail::to($student)->send(new StudentCreated($request->all())); // Consider if Mail facade is used elsewhere or needs import

        return redirect()->route('students.show', $student);
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show(Request $request, $studentId)
    {
        $student = Student::withTrashed()->where('user_id', $studentId)->first();

        if ($student) {
            $student->loadMissing(['admissionInterviews.interviewers', 'joint_classes', 'admissions.programs.levels','studentProgramLevels']);
        }

        $interviewers = Employee::all()->pluck('name', 'id');

        $programs = \App\Program::with('translations')->get()->pluck('title', 'id');

        $surat = [
            ["name" => "	Al-Fatihah	 - 	الفاتحة	", "num_ayat" => 7],
            ["name" => "	Al-Baqarah	 - 	البقرة	", "num_ayat" => 286],
            ["name" => "	Al Imran	 - 	آل عمران	", "num_ayat" => 200],
            ["name" => "	An-Nisa'	 - 	النساء	", "num_ayat" => 176],
            ["name" => "	Al-Ma'idah	 - 	المائدة	", "num_ayat" => 120],
            ["name" => "	Al-An'am	 - 	الأنعام	", "num_ayat" => 165],
            ["name" => "	Al-A'raf	 - 	الأعراف	", "num_ayat" => 206],
            ["name" => "	Al-Anfal	 - 	الأنفال	", "num_ayat" => 75],
            ["name" => "	At-Tawbah	 - 	التوبة	", "num_ayat" => 129],
            ["name" => "	Yunus	 - 	يونس	", "num_ayat" => 109],
            ["name" => "	Hud	 - 	هود	", "num_ayat" => 123],
            ["name" => "	Yusuf	 - 	يوسف	", "num_ayat" => 111],
            ["name" => "	Ar-Ra'd	 - 	الرعد	", "num_ayat" => 43],
            ["name" => "	Ibraheem	 - 	إبراهيم	", "num_ayat" => 52],
            ["name" => "	Al-Hijr	 - 	الحجر	", "num_ayat" => 99],
            ["name" => "	An-Nahl	 - 	النحل	", "num_ayat" => 128],
            ["name" => "	Al-Isra	 - 	الإسراء	", "num_ayat" => 111],
            ["name" => "	Al-Kahf	 - 	الكهف	", "num_ayat" => 110],
            ["name" => "	Maryam	 - 	مريم	", "num_ayat" => 98],
            ["name" => "	Ta-Ha	 - 	طه	", "num_ayat" => 135],
            ["name" => "	Al-Anbiya'	 - 	الأنبياء	", "num_ayat" => 112],
            ["name" => "	Al-Hajj	 - 	الحج	", "num_ayat" => 78],
            ["name" => "	Al-Mu'minoon	 - 	المؤمنون	", "num_ayat" => 118],
            ["name" => "	An-Nur	 - 	النور	", "num_ayat" => 64],
            ["name" => "	Al-Furqan	 - 	الفرقان	", "num_ayat" => 77],
            ["name" => "	ash-Shu`ara'	 - 	الشعراء	", "num_ayat" => 227],
            ["name" => "	An-Naml	 - 	النمل	", "num_ayat" => 93],
            ["name" => "	Al-Qasas	 - 	القصص	", "num_ayat" => 88],
            ["name" => "	Al-`Ankabut	 - 	العنكبوت	", "num_ayat" => 69],
            ["name" => "	Ar-Rum	 - 	الروم	", "num_ayat" => 60],
            ["name" => "	Luqman	 - 	لقمان	", "num_ayat" => 34],
            ["name" => "	As-Sajdah	 - 	السجدة	", "num_ayat" => 30],
            ["name" => "	Al-Ahzab	 - 	الأحزاب	", "num_ayat" => 73],
            ["name" => "	Saba'	 - 	سبأ	", "num_ayat" => 54],
            ["name" => "	Fatir	 - 	فاطر	", "num_ayat" => 45],
            ["name" => "	Ya seen	 - 	يس	", "num_ayat" => 83],
            ["name" => "	As-Saffat	 - 	الصافات	", "num_ayat" => 182],
            ["name" => "	Sad	 - 	ص	", "num_ayat" => 88],
            ["name" => "	Az-Zumar	 - 	الزمر	", "num_ayat" => 75],
            ["name" => "	Ghafir	 - 	غافر	", "num_ayat" => 85],
            ["name" => "	Fussilat	 - 	فصلت	", "num_ayat" => 54],
            ["name" => "	Ash-Shura	 - 	الشورى	", "num_ayat" => 53],
            ["name" => "	Az-Zukhruf	 - 	الزخرف	", "num_ayat" => 89],
            ["name" => "	Ad-Dukhan	 - 	الدخان	", "num_ayat" => 59],
            ["name" => "	Al-Jathiyah	 - 	الجاثية	", "num_ayat" => 37],
            ["name" => "	Al-Ahqaf	 - 	الأحقاف	", "num_ayat" => 35],
            ["name" => "	Muhammad	 - 	محمد	", "num_ayat" => 38],
            ["name" => "	Al-Fath	 - 	الفتح	", "num_ayat" => 29],
            ["name" => "	Al-Hujurat	 - 	الحجرات	", "num_ayat" => 18],
            ["name" => "	Qaf	 - 	ق	", "num_ayat" => 45],
            ["name" => "	Ad-Dhariyat	 - 	الذاريات	", "num_ayat" => 60],
            ["name" => "	At-Tur	 - 	الطور	", "num_ayat" => 49],
            ["name" => "	An-Najm	 - 	النجم	", "num_ayat" => 62],
            ["name" => "	Al-Qamar	 - 	القمر	", "num_ayat" => 55],
            ["name" => "	Ar-Rahman	 - 	الرحمن	", "num_ayat" => 78],
            ["name" => "	Al-Waqi'ah	 - 	الواقعة	", "num_ayat" => 96],
            ["name" => "	Al-Hadeed	 - 	الحديد	", "num_ayat" => 29],
            ["name" => "	Al-Mujadilah	 - 	المجادلة	", "num_ayat" => 22],
            ["name" => "	Al-Hashr	 - 	الحشر	", "num_ayat" => 24],
            ["name" => "	Al-Mumtahanah	 - 	الممتحنة	", "num_ayat" => 13],
            ["name" => "	As-Saff	 - 	الصف	", "num_ayat" => 14],
            ["name" => "	Al-Jumu'ah	 - 	الجمعة	", "num_ayat" => 11],
            ["name" => "	Al-Munafiqun	 - 	المنافقون	", "num_ayat" => 11],
            ["name" => "	At-Taghabun	 - 	التغابن	", "num_ayat" => 18],
            ["name" => "	At-Talaq	 - 	الطلاق	", "num_ayat" => 12],
            ["name" => "	At-Tahreem	 - 	التحريم	", "num_ayat" => 12],
            ["name" => "	Al-Mulk	 - 	الملك	", "num_ayat" => 30],
            ["name" => "	Al-Qalam	 - 	القلم	", "num_ayat" => 52],
            ["name" => "	Al-Haqqah	 - 	الحاقة	", "num_ayat" => 52],
            ["name" => "	Al-Ma'aarij	 - 	المعارج	", "num_ayat" => 44],
            ["name" => "	Nuh	 - 	نوح	", "num_ayat" => 28],
            ["name" => "	Al-Jinn	 - 	الجن	", "num_ayat" => 28],
            ["name" => "	Al-Muzzammil	 - 	المزّمِّل	", "num_ayat" => 20],
            ["name" => "	Al-Muddathir	 - 	المدّثر	", "num_ayat" => 56],
            ["name" => "	Al-Qiyamah	 - 	القيامة	", "num_ayat" => 40],
            ["name" => "	Al-Insan	 - 	الإنسان	", "num_ayat" => 31],
            ["name" => "	Al-Mursalat	 - 	المرسلات	", "num_ayat" => 50],
            ["name" => "	An-Naba'	 - 	النبأ	", "num_ayat" => 40],
            ["name" => "	An-Nazi'at	 - 	النازعات	", "num_ayat" => 46],
            ["name" => "	`Abasa	 - 	عبس	", "num_ayat" => 42],
            ["name" => "	At-Takweer	 - 	التكوير	", "num_ayat" => 29],
            ["name" => "	Al-Infitar	 - 	الانفطار	", "num_ayat" => 19],
            ["name" => "	Al-Mutaffifeen	 - 	المطففين	", "num_ayat" => 36],
            ["name" => "	Al-Inshiqaq	 - 	الانشقاق	", "num_ayat" => 25],
            ["name" => "	Al-Burooj	 - 	البروج	", "num_ayat" => 22],
            ["name" => "	At-Tariq	 - 	الطارق	", "num_ayat" => 17],
            ["name" => "	Al-A'la	 - 	الاعلى	", "num_ayat" => 19],
            ["name" => "	Al-Ghashiyah	 - 	الغاشية	", "num_ayat" => 26],
            ["name" => "	Al-Fajr	 - 	الفجر	", "num_ayat" => 30],
            ["name" => "	Al-Balad	 - 	البلد 	", "num_ayat" => 20],
            ["name" => "	Ash-Shams	 - 	الشمس	", "num_ayat" => 15],
            ["name" => "	Al-Lail	 - 	الليل 	", "num_ayat" => 21],
            ["name" => "	Ad-Dhuha	 - 	الضحى	", "num_ayat" => 11],
            ["name" => "	Al-Inshirah	 - 	الشرح	", "num_ayat" => 8],
            ["name" => "	Al-Teen	 - 	التين 	", "num_ayat" => 8],
            ["name" => "	al-`Alaq	 - 	العلق	", "num_ayat" => 19],
            ["name" => "	Al-Qadr	 - 	القدر	", "num_ayat" => 5],
            ["name" => "	Al-Bayyinah	 - 	البينة 	", "num_ayat" => 8],
            ["name" => "	Az-Zalzala	 - 	الزلزلة	", "num_ayat" => 8],
            ["name" => "	Al-Adiyat	 - 	العاديات	", "num_ayat" => 11],
            ["name" => "	al-Qari`ah	 - 	القارعة	", "num_ayat" => 11],
            ["name" => "	At-Takathur	 - 	التكاثر	", "num_ayat" => 8],
            ["name" => "	Al-Asr	 - 	العصر	", "num_ayat" => 3],
            ["name" => "	Al-Humazah	 - 	الهمزة 	", "num_ayat" => 9],
            ["name" => "	Al-Feel	 - 	الفيل 	", "num_ayat" => 5],
            ["name" => "	Al-Quraish	 - 	قريش	", "num_ayat" => 4],
            ["name" => "	Al-Maa'oun	 - 	الماعون	", "num_ayat" => 7],
            ["name" => "	Al-Kawthar	 - 	 الكوثر	", "num_ayat" => 3],
            ["name" => "	Al-Kafiroun	 - 	الكافرون	", "num_ayat" => 6],
            ["name" => "	An-Nasr	 - 	النصر	", "num_ayat" => 3],
            ["name" => "	Al-Masad	 - 	المسد 	", "num_ayat" => 5],
            ["name" => "	Al-Ikhlas	 - 	الإخلاص	", "num_ayat" => 4],
            ["name" => "	Al-Falaq	 - 	الفلق	", "num_ayat" => 5],
            ["name" => "	Al-Nas	 - 	الناس	", "num_ayat" => 6]
        ];
        
        $plan = StudentAdmissionHefzPlan::where('student_id', '=', $student->id)->get();

        // get last student number
        $last_student_number = Student::max('student_number');
        
        // Get student image URL using StudentImageService
        $studentImageUrl = $this->studentImageService->getStudentImageUrl($student);
        
        return view("admission::student.show", compact('student', 'interviewers', 'programs', 'surat', 'plan', 'last_student_number', 'studentImageUrl'));
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        // check_permission('update student');
  
        $student = Student::where('user_id', $id)->first();

        return view("admission::student.edit", compact('student'));
    }


    public function update(UpdateStudentRequest $request, $studentUserId)
    {
        $student = Student::where('user_id', $studentUserId)->first();
        
        Log::debug('Student update request received', [
            'student_id' => $student->id,
            'has_image_upload' => $request->hasFile('image')
        ]);

        if (isset($request->update_profile)) {
            try {
                DB::beginTransaction();
                
                // Handle image upload using StudentImageService
                if ($request->hasFile('image')) {
                    $imagePath = $this->studentImageService->storeStudentImage($student, $request->file('image'));
                    Log::info('Student photo updated via profile update', [
                        'student_id' => $student->id,
                        'image_path' => $imagePath,
                        'image_url' => $this->studentImageService->getStudentImageUrl($student)
                    ]);
                }

                $student->fill($request->except('image'));
                $student->save();

                // update admissions table
                $student->admissions()->update(['date_of_birth' => $request->get('date_of_birth')]);
                DB::commit();
                
                flash('Profile updated successfully!');
                return redirect()->route('students.show', $studentUserId);

            } catch (\Exception $e) {
                DB::rollback();
                Log::error('Error updating student profile', [
                    'student_id' => $student->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                flash($e->getMessage())->error();
                return redirect()->back();
            }
        }
        
        return back();
    }

    private function validateRequest($request)
    {
        if (config("settings.student_form_full_name") == "required") {
            $roles["full_name"] = "required";
        }
        if (config("settings.student_form_full_name_trans") == "required") {
            $roles["full_name_trans"] = "required";
        }
        if (config("settings.student_form_full_name_language") == "required") {
            $roles["full_name_language"] = "required";
        }
        if (config("settings.student_form_gender") == "required") {
            $roles["gender"] = "required";
        }
        if (config("settings.student_form_date_of_birth") == "required") {
            $roles["date_of_birth"] = "required";
        }
        if (config("settings.student_form_identity_number") == "required") {
            $roles["identity_number"] = "required";
        }
        if (config("settings.student_form_identity") == "required") {
            $roles["identity"] = "required| mimes:jpeg,jpg,bmp,png,gif,svg,pdf,zip | max:5000";
        }
        if (config("settings.student_form_image") == "required") {
//            $roles["image"] = "required|image| max:3000";
            $roles["image"] = "sometimes|required| max:3000";
        }
        if (config("settings.student_form_nationality") == "required") {
            $roles["nationality"] = "required";
        }
        if (config("settings.student_form_mobile") == "required") {
            $roles["mobile"] = "required";
        }
        if ($roles) {
            $this->validate($request, $roles);
        }
    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy(Request $request, Student $student)
    {
        try {
            DB::beginTransaction();
            
            // Get the student ID - prioritize route model binding, fallback to request params
            $studentId = $student->id ?? $request->id ?? $request->s_id;
            
            if (!$studentId) {
                throw new \Exception('No student ID provided');
            }

            // Find the student if not already injected via route model binding
            if (!$student->exists) {
                $student = Student::withTrashed()->findOrFail($studentId);
            }

            // Get admission IDs if specified and parse them
            $admissionIds = [];
            if ($request->admissionId) {
                $admissionIds = is_array($request->admissionId) 
                    ? $request->admissionId 
                    : array_map('trim', explode(',', $request->admissionId));
            }

            // Log the deletion attempt
            \Log::info('Attempting to delete student', [
                'student_id' => $studentId,
                'admission_ids' => $admissionIds,
                'active_admissions_count' => $student->admissions()->whereNull('deleted_at')->count(),
                'reason' => $request->reason,
                'notice' => $request->notice
            ]);

            // Count only active (non-deleted) admissions
            $activeAdmissionsCount = $student->admissions()->whereNull('deleted_at')->count();
        
            // Handle deletion based on whether it's specific admissions or entire student record
            if (!empty($admissionIds) && $activeAdmissionsCount > count($admissionIds)) {
                // Only delete specific admissions
                $admissions = Admission::whereIn('id', $admissionIds)->get();
                foreach ($admissions as $admission) {
                    $admission->update([
                        'deleted_at' => Carbon::now(),
                        'status' => 'archived'
                    ]);
                }
                
                \Log::info('Archived specific admissions', [
                    'admission_ids' => $admissionIds,
                    'student_id' => $studentId,
                    'remaining_active_admissions' => $activeAdmissionsCount - count($admissionIds)
                ]);
            } else {
                // Delete entire student record and related data
                $student->update([
                    'delete_reason' => $request->reason,
                    'delete_notice' => $request->notice ?? NULL,
                    'archived_by' => auth()->id(),
                    'archived_by_ip' => request()->ip(),
                    'status' => 'archived'
                ]);

                // Archive all active admissions
                Admission::where('student_id', $studentId)
                    ->whereNull('deleted_at')
                    ->update([
                        'deleted_at' => Carbon::now(),
                        'status' => 'archived'
                    ]);

                // Delete related records
                StudentHefzPlan::where('student_id', $studentId)->delete();
                StudentHefzReport::where('student_id', $studentId)->delete();
                StudentRevisionReport::where('student_id', $studentId)->delete();
                StudentRevisionPlan::where('student_id', $studentId)->delete();
                
                ClassStudent::where('student_id', $studentId)
                    ->whereNull('deleted_at')
                    ->update([
                        'deleted_at' => Carbon::now(),
                        'end_date' => Carbon::now()->toDateString()
                    ]);

                // Finally delete the student
                $student->delete();

                \Log::info('Deleted student and related records', [
                    'student_id' => $studentId,
                    'student_name' => $student->full_name
                ]);
            }

            DB::commit();

            if (preg_match('~classes/\d+~', url()->previous())) {
                return redirect()->to(url()->previous())->with('status', 'Operation completed!');
            }

            return response()->json([
                "stId" => $studentId,
                "message" => 'Student: ' . $student->full_name . ' Deleted !!'
            ]);

        } catch (\Exception $exception) {
            DB::rollBack();
            
            \Log::error('Failed to delete student', [
                'student_id' => $studentId ?? null,
                'error' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString()
            ]);

            return response()->json([
                'error' => true,
                'message' => 'Failed to delete student: ' . $exception->getMessage()
            ], 500);
        }
    }


    public function restoreStudent(Request $request, $id = null)
    {


        $id = is_null($request->id) == true ? $request->stuId : $request->id; // this ternary operation is place here because multiple places are calling this method. future refactoring is needed.
        $student = Student::withTrashed()->findOrFail($id);

        $student->delete_reason = NULL;
        $student->delete_notice = NULL;
        $student->deleted_at = NULL;
        $student->status = 'new_admission';
        $student->save();


        Admission::withTrashed()->where('student_id', $id)->update([
            'deleted_at' => NULL,
            'status' => 'new_admission',
        ]);

        return response()->json("Student: " . $student->full_name . " Revoked");
    }

    public function verify(Request $request)
    {


//        We all know that update() returns boolean but we want it to return our edited $user so we can pass it to our json response fo.Here come the rule of tap()
        $user = User::where('id', $request->get('userId'))->first();
        $user = tap($user, function ($user) {

            $user->update(
                [
                    'email_verified_at' => Carbon::now()->toDateTimeString()
                ]);


        });

        return response()->json("Successfull!  <b>" . $user->full_name . "</b> can login now");
    }

    public function returnStudentToPreviousHalaqah(Request $request)
    {


//        $id = is_null($request->id) == true ? $request->stuId : $request->id; // this ternary operation is place here because multiple places are calling this method. future refactoring is needed.

        if (\Auth::guard('student')->check()) {
            $student_id = \Auth::user()->id;
            $creator_role = 'student';
        } elseif (\Auth::guard('guardian')->check()) {

            $creator_role = 'guardian';
            $student_id = $request->stuId;

        } elseif (\Auth::guard('employee')->check()) {
            //  if(!auth()->user()->can('register student') || !$request->student_id){

            //     flash('Error. Not Authorized');

            //     return redirect()->back();
            //  }
            $creator_role = 'employee';
            $student_id = $request->stuId;
        }


        $student = Student::withTrashed()->findOrFail($student_id);
        $student->joint_classes()->updateExistingPivot($request->class_id, ['deleted_at' => NULL]);
        $admission = new Admission;

        $admission->organization_id = config('organization_id');
        $admission->student_id = $student_id;
        $admission->creator_role = $creator_role;
        $admission->created_by = \Auth::user()->id;;
        $admission->center_id = $request->center_id;
        $admission->class_id = $request->class_id;
        $admission->start_date = date('Y-m-d');
        $admission->status = 'reapplication';

        $admission->save();

        $admission->programs()->attach($request->program_id);

        $student->status = "active";
        $student->deleted_at = NULL;
        $student->save();

        $student->delete_reason = NULL;
        $student->delete_notice = NULL;
        $student->deleted_at = NULL;
//        $student->status = 'active';
        $student->save();


        return response()->json("Student: " . $student->full_name . " Revoked");
    }


    public function getStudentsJsonFormat(Request $request)
    {
        $requestedName = '%' . $request->q . '%';
        $name_condition = "display_name LIKE " . "'" . $requestedName . "'" . " OR full_name LIKE  " . "'" . $requestedName . "'" . " OR full_name_trans LIKE " . "'" . $requestedName . "'";

        $addmision = Admission::pluck('student_id');

        $student = Student::whereIn('id', $addmision)->whereRaw($name_condition)->get();


        $totalCounts = $student->count();
//        $my_query = "select * from students where " . $name_condition;


//        $student = DB::select($my_query, array($request->q));
//        $totalCounts = DB::select($my_query, array($request->q));
//        $totalCounts = count($totalCounts);

        $searchLang = is_arabic($request->q) == true ? "Arabic" : "English";

//        $student = Student::where("full_name",'LIKE','%'.$request->q.'%')->orWhere("full_name_trans",'LIKE','%'.$request->q.'%')->get();

        return response()->json(["total_count" => $totalCounts, "incomplete_results" => false, "items" => $student, 'language' => $searchLang], 200);

    }

    public function getCentersJsonFormat(Request $request)
    {
        $center = DB::select("select location as name, id as value from centers where deleted_at is null order by location");


        return response()->json(["success" => true, "results" => $center], 200);

    }

    public function getCenterBasedHalaqah(Request $request)
    {


        $center_id = $request->get('center_id');
        $classes = Classes::where('center_id', $center_id)
            ->get();


        return response()->json(["success" => true, "results" => $classes], 200);

    }

    public function getProgramsJsonFormat(Request $request)
    {
        $programs = DB::select("SELECT programs.id AS value, program_translations.title AS name FROM programs,program_translations WHERE programs.id = program_translations.program_id  and program_translations.locale= 'en'");


        return response()->json(["success" => true, "results" => $programs], 200);

    }

    public function getStudentStatusJsonFormat(Request $request)
    {
        $center = DB::select('SELECT a.value,
                CASE a.name  
                WHEN "active" then "Active"
                WHEN "new_admission" then "New Admission"
                WHEN "update_guardian" then "Update Guardian"
                WHEN "profile_completed" then "Profile Completed"
                else "Not Defined"  END AS "name"  FROM (SELECT DISTINCT STATUS AS NAME,STATUS AS value FROM students) a');


        return response()->json(["success" => true, "results" => $center], 200);

    }

    public function hasInput(Request $request)
    {
        if ($request->has('_token')) {
            return count($request->all()) > 1;
        } else {
            return count($request->all()) > 0;
        }
    }

    /**
     * Get centers based on program ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCenters(Request $request)
    {
        try {
            $program_id = (int)$request->program_id;
           
            $centers = \App\Center::whereHas('programs', function ($query) use ($program_id) {
                $query->where('program_id', $program_id);
            })->get();

            return response()->json([
                'success' => true,
                'centers' => $centers
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching centers: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get classes based on center ID and program ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getClasses(Request $request)
    {
        try {
            $center_id = (int)$request->center_id;
            $program_id = (int)$request->program_id;

            $classes = \App\Classes::where('center_id', $center_id)
                ->whereHas('center.programs', function ($query) use ($program_id) {
                    $query->where('program_id', $program_id);
                })
                
                ->get();

            return response()->json([
                'success' => true,
                'classes' => $classes
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching classes: ' . $e->getMessage()
            ], 500);
        }
    }

}
<?php

namespace App\DataTables;

use App\ClassReport;
use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rule;
use Yajra\DataTables\DataTablesEditor;

class DailyClassReportsDataTableEditor extends DataTablesEditor
{
    protected $model = ClassReport::class;

    /**
     * Get create action validation rules.
     *
     * @return array
     */
    public function createRules()
    {
        return [
            'student_id' => 'required',
            'from_surat_name'  => 'required',
            'from_surat'  => 'required',
            'from_ayat'  => 'required',
            'to_surat_name'  => 'required',
            'attendance'  => 'required',
            'evaluation'  => 'required',
        ];
    }

    /**
     * Get edit action validation rules.
     *
     * @param Model $model
     * @return array
     */
    public function editRules(Model $model)
    {
        return [
            'email' => 'sometimes|required|email|' . Rule::unique($model->getTable())->ignore($model->getKey()),
            'name'  => 'sometimes|required',
        ];
    }

    /**
     * Get remove action validation rules.
     *
     * @param Model $model
     * @return array
     */
    public function removeRules(Model $model)
    {
        return [];
    }
}

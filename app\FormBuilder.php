<?php

namespace App;

use App\Scopes\OrganizationScope;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Model;

/**
 * App\FormBuilder
 *
 * @property int $id
 * @property int $organization_id
 * @property string $date_or_range
 * @property int $request_time
 * @property string|null $dynamic_action
 * @property string|null $target
 * @property int|null $is_removable
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\FormBuilderApprovalFlow[] $approvalFlow
 * @property-read int|null $approval_flow_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\FormBuilderTranslation[] $translations
 * @property-read int|null $translations_count
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder listsTranslations($translationField)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder notTranslatedIn($locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder orWhereTranslation($key, $value, $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder orWhereTranslationLike($key, $value, $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder query()
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder translated()
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder translatedIn($locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder whereDateOrRange($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder whereDynamicAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder whereIsRemovable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder whereRequestTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder whereTarget($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder whereTranslation($key, $value, $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder whereTranslationLike($key, $value, $locale = null)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilder withTranslation()
 * @mixin \Eloquent
 */
class FormBuilder extends Model
{
    use Translatable;

    protected $fillable = ['organization_id' ,  'date_or_range', 'request_time' , 'target' , 'status'];

    protected $translatedAttributes = ['title', 'date_label'];

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
    }
    
    public function getTargetAttribute($value)
    {
        return json_decode($value);
    }

    public function setOrganizationIdAttribute()
    {
        $this->attributes['organization_id'] = config('organization_id');
    }

    public function setTargetAttribute($value)
    {
        $this->attributes['target'] =  json_encode($value);
    }

    public function approvalFlow()
    {
        return $this->hasMany('App\FormBuilderApprovalFlow')->orderBy('step_order');
    }
}

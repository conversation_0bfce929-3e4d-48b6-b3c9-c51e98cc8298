<?php
namespace Modules\Education\Http\Controllers\SpecialPrograms\Hefz;

use App\MoshafPage;
use App\MoshafSurah;

class Lesson
{
    private $from_surat;
    private $from_ayat;
    private $to_surat;
    private $to_ayat;
    private $moshaf_id = 1;
    private $end_of_surah_stop = true;

    /**
     * Constructor
     *
     * @param integer $from_surat
     * @param integer $from_ayat
     * @param integer $to_surat
     * @param integer $to_ayat
     * @param integer $moshaf_id
     */
    public function __construct($from_surat, $from_ayat, $to_surat, $to_ayat, $moshaf_id = 1)
    {
        $this->from_surat = $from_surat;
        $this->from_ayat = $from_ayat;
        $this->to_surat = $to_surat;
        $this->to_ayat = $to_ayat;
        $this->moshaf_id = $moshaf_id;
    }


    /**
     * Get Next Lesson
     *
     * @param string $mood      Can be **page** or **ayah**
     * @param integer $count    Number of pages or ayat
     * @param string $direction forward or backward
     * @param boolean $end_of_surah_stop
     * @return array
     */
    public function next($mood, $count, $direction, $end_of_surah_stop = true)
    {
        
        $lesson_start = $this->nextAyah($this->to_surat , $this->to_ayat , $direction);
        
        $from_ayah = $lesson_start['ayah'];
        
        $from_surah = MoshafSurah::findOrFail($lesson_start['surah_id']);

        if ($mood == 'page') {
            $current_page = MoshafPage::where('moshaf_id', 1)
                            ->where('surah_id', $from_surah->id)
                            ->where('first_ayah', '<=', $from_ayah)
                            ->where('last_ayah', '>=', $from_ayah)
                            ->first();

            if ($direction == 'backward') {
                $next_page_number = $current_page->page_number - $count + 1;

                if($end_of_surah_stop){
                    $required_surah_id = MoshafPage::where('moshaf_id', 1)
                    ->where('page_number', $next_page_number)
                    ->where('first_ayah', 1)
                    ->min('surah_id');
                }else{
                    
                }
            } 
        }
        return [
            'from_surat_name' => $from_surah->name,
            'from_surat' => $from_surah->id,
            'from_ayat' => $from_ayah,
            'to_surat_name' => $to_surah->name,
            'to_surat' => $to_surah->id,
            'to_ayat' => $to_ayah,
        ];
    }

    /**
     * get next ayah 
     *
     * @param Integer $surah_id Surah Number 
     * @param Integer $ayah Ayah Number
     * @param String $direction Lession direction ("forward" or "backward")
     * @return Array
     */
    private function nextAyah($surah_id , $ayah , $direction){
        $surah = MoshafSurah::findOrFail($surah_id);

        if ($surah->num_ayat >= $ayah +1) {
            $ayah++;
        } else {
            if ($direction == "forward" && $surah_id < 114) {
                $surah_id = $surah_id + 1;
            }else if ($direction == "forward" && $surah_id == 114) {
                $surah_id = 1;

            } else {
                $surah_id = $surah_id - 1;
            }
            $ayah = 1;
        }
        return ['surah_id' => $surah_id , 'ayah' => $ayah ];
    }
}

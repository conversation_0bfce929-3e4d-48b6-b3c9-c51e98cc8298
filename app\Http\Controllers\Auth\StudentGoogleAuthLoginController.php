<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Resources\User;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;

class StudentGoogleAuthLoginController extends Controller
{
    public function redirectToProvider()
    {
        return Socialite::driver('google')->redirect();
    }

    public function handleProviderCallback()
    {
        try {
            $user = Socialite::driver('google')->user();
        } catch (\Exception $e) {
            return redirect()->route('custom_google_login');
        }

        $existingUser = User::where('google_id', $user->getId())->first();

        if ($existingUser) {
            Auth::login($existingUser, true);
//            return redirect()->route('home');
        } else {
            return redirect()->route('custom_google_login')->with('error', 'Google ID not found in our records.');
        }
    }
}

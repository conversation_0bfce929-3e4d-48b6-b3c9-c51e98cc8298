@startuml Module Interaction Overview

!theme vibrant

package "Laravel Application" {

    frame "Core Services/Components" {
        [App Core] <<(C,#ADD1B2)>>
        [Events] <<(E,#FFA500)>>
        [Shared Services] <<(S,#A9A9A9)>>
        [Database] <<(D,#6495ED)>>
    }

    package "Modules (nwidart/laravel-modules)" {
        component [Account]
        component [Activities]
        component [Admission]
        component [ApplicationCenter]
        component [Attendance]
        component [Backup]
        component [Chat]
        component [Communicate]
        component [Contact]
        component [Curriculum]
        component [Education]
        component [EducationalReports]
        component [ExaminationCertification]
        component [Finance]
        component [General]
        component [HumanResource]
        component [Inventory]
        component [Jobs]
        component [Leave]
        component [Localization]
        component [Marketing]
        component [MenuManage]
        component [ModuleManager]
        component [Payroll]
        component [Planning]
        component [Platform]
        component [Purchase]
        component [RolePermission]
        component [Sale]
        component [Setting]
        component [Setup]
        component [Site]
        component [TemplateSettings]
        component [UserActivityLog]
    }

    ' Potential Interaction Patterns (Examples - Not exhaustive)
    Account ..> [Database] : Reads/Writes User Data
    Admission ..> [Database] : Manages Application Data
    Finance ..> [Database] : Manages Financial Records
    HumanResource ..> [Database] : Manages Employee Data
    Education ..> [Database] : Manages Educational Data

    [App Core] ..> [Events] : Dispatches Core Events
    [Events] ..> Admission : (Listens for Events)
    [Events] ..> Finance : (Listens for Events)
    [Events] ..> HumanResource : (Listens for Events)

    Setup ..> [Shared Services] : Provides Configuration
    [Shared Services] ..> Setting : Uses Shared Logic
    [Shared Services] ..> RolePermission : Uses Shared Logic

    Communicate ..> [App Core] : Uses Core Mail/Notification

    ' Modules *may* interact directly, via Services, Events, or shared DB tables
    ' Example: Admission might trigger an event handled by Finance for fee creation
    Admission ..> [Events] : Dispatches Admission Event
    Finance ..> [Events] : Listens for Admission Event

    ' Example: HR might use a service from General
    HumanResource ..> [Shared Services]
    [Shared Services] ..> General : Provides General Utility

}

@enduml 
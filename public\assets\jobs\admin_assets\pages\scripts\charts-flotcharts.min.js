var ChartsFlotcharts=function(){return{init:function(){App.addResizeHandler(function(){Charts.initPieCharts()})},initCharts:function(){function e(){for(s.length>0&&(s=s.slice(1));s.length<l;){var e=s.length>0?s[s.length-1]:50,i=e+10*Math.random()-5;0>i&&(i=0),i>100&&(i=100),s.push(i)}for(var t=[],a=0;a<s.length;++a)t.push([a,s[a]]);return t}function i(){if(1==$("#chart_1").size()){for(var e=[],i=0;i<2*Math.PI;i+=.25)e.push([i,Math.sin(i)]);for(var t=[],i=0;i<2*Math.PI;i+=.25)t.push([i,Math.cos(i)]);for(var a=[],i=0;i<2*Math.PI;i+=.1)a.push([i,Math.tan(i)]);$.plot($("#chart_1"),[{label:"sin(x)",data:e,lines:{lineWidth:1},shadowSize:0},{label:"cos(x)",data:t,lines:{lineWidth:1},shadowSize:0},{label:"tan(x)",data:a,lines:{lineWidth:1},shadowSize:0}],{series:{lines:{show:!0},points:{show:!0,fill:!0,radius:3,lineWidth:1}},xaxis:{tickColor:"#eee",ticks:[0,[Math.PI/2,"π/2"],[Math.PI,"π"],[3*Math.PI/2,"3π/2"],[2*Math.PI,"2π"]]},yaxis:{tickColor:"#eee",ticks:10,min:-2,max:2},grid:{borderColor:"#eee",borderWidth:1}})}}function t(){function e(){return Math.floor(21*Math.random())+20}function i(e,i,t){$('<div id="tooltip">'+t+"</div>").css({position:"absolute",display:"none",top:i+5,left:e+15,border:"1px solid #333",padding:"4px",color:"#fff","border-radius":"3px","background-color":"#333",opacity:.8}).appendTo("body").fadeIn(200)}if(1==$("#chart_2").size()){var t=[[1,e()],[2,e()],[3,2+e()],[4,3+e()],[5,5+e()],[6,10+e()],[7,15+e()],[8,20+e()],[9,25+e()],[10,30+e()],[11,35+e()],[12,25+e()],[13,15+e()],[14,20+e()],[15,45+e()],[16,50+e()],[17,65+e()],[18,70+e()],[19,85+e()],[20,80+e()],[21,75+e()],[22,80+e()],[23,75+e()],[24,70+e()],[25,65+e()],[26,75+e()],[27,80+e()],[28,85+e()],[29,90+e()],[30,95+e()]],a=[[1,e()-5],[2,e()-5],[3,e()-5],[4,6+e()],[5,5+e()],[6,20+e()],[7,25+e()],[8,36+e()],[9,26+e()],[10,38+e()],[11,39+e()],[12,50+e()],[13,51+e()],[14,12+e()],[15,13+e()],[16,14+e()],[17,15+e()],[18,15+e()],[19,16+e()],[20,17+e()],[21,18+e()],[22,19+e()],[23,20+e()],[24,21+e()],[25,14+e()],[26,24+e()],[27,25+e()],[28,26+e()],[29,27+e()],[30,31+e()]],r=($.plot($("#chart_2"),[{data:t,label:"Unique Visits",lines:{lineWidth:1},shadowSize:0},{data:a,label:"Page Views",lines:{lineWidth:1},shadowSize:0}],{series:{lines:{show:!0,lineWidth:2,fill:!0,fillColor:{colors:[{opacity:.05},{opacity:.01}]}},points:{show:!0,radius:3,lineWidth:1},shadowSize:2},grid:{hoverable:!0,clickable:!0,tickColor:"#eee",borderColor:"#eee",borderWidth:1},colors:["#d12610","#37b7f3","#52e136"],xaxis:{ticks:11,tickDecimals:0,tickColor:"#eee"},yaxis:{ticks:11,tickDecimals:0,tickColor:"#eee"}}),null);$("#chart_2").bind("plothover",function(e,t,a){if($("#x").text(t.x.toFixed(2)),$("#y").text(t.y.toFixed(2)),a){if(r!=a.dataIndex){r=a.dataIndex,$("#tooltip").remove();var o=a.datapoint[0].toFixed(2),s=a.datapoint[1].toFixed(2);i(a.pageX,a.pageY,a.series.label+" of "+o+" = "+s)}}else $("#tooltip").remove(),r=null})}}function a(){function e(){o=null;var e=s,i=plot.getAxes();if(!(e.x<i.xaxis.min||e.x>i.xaxis.max||e.y<i.yaxis.min||e.y>i.yaxis.max)){var t,a,l=plot.getData();for(t=0;t<l.length;++t){var n=l[t];for(a=0;a<n.data.length&&!(n.data[a][0]>e.x);++a);var h,d=n.data[a-1],c=n.data[a];h=null==d?c[1]:null==c?d[1]:d[1]+(c[1]-d[1])*(e.x-d[0])/(c[0]-d[0]),r.eq(t).text(n.label.replace(/=.*/,"= "+h.toFixed(2)))}}}if(1==$("#chart_3").size()){for(var i=[],t=[],a=0;14>a;a+=.1)i.push([a,Math.sin(a)]),t.push([a,Math.cos(a)]);plot=$.plot($("#chart_3"),[{data:i,label:"sin(x) = -0.00",lines:{lineWidth:1},shadowSize:0},{data:t,label:"cos(x) = -0.00",lines:{lineWidth:1},shadowSize:0}],{series:{lines:{show:!0}},crosshair:{mode:"x"},grid:{hoverable:!0,autoHighlight:!1,tickColor:"#eee",borderColor:"#eee",borderWidth:1},yaxis:{min:-1.2,max:1.2}});var r=$("#chart_3 .legendLabel");r.each(function(){$(this).css("width",$(this).width())});var o=null,s=null;$("#chart_3").bind("plothover",function(i,t,a){s=t,o||(o=setTimeout(e,50))})}}function r(){function i(){r.setData([e()]),r.draw(),setTimeout(i,a)}if(1==$("#chart_4").size()){var t={series:{shadowSize:1},lines:{show:!0,lineWidth:.5,fill:!0,fillColor:{colors:[{opacity:.1},{opacity:1}]}},yaxis:{min:0,max:100,tickColor:"#eee",tickFormatter:function(e){return e+"%"}},xaxis:{show:!1},colors:["#6ef146"],grid:{tickColor:"#eee",borderWidth:0}},a=30,r=$.plot($("#chart_4"),[e()],t);i()}}function o(){function e(){$.plot($("#chart_5"),[{label:"sales",data:i,lines:{lineWidth:1},shadowSize:0},{label:"tax",data:a,lines:{lineWidth:1},shadowSize:0},{label:"profit",data:r,lines:{lineWidth:1},shadowSize:0}],{series:{stack:o,lines:{show:l,fill:!0,steps:n,lineWidth:0},bars:{show:s,barWidth:.5,lineWidth:0,shadowSize:0,align:"center"}},grid:{tickColor:"#eee",borderColor:"#eee",borderWidth:1}})}if(1==$("#chart_5").size()){for(var i=[],t=0;10>=t;t+=1)i.push([t,parseInt(30*Math.random())]);for(var a=[],t=0;10>=t;t+=1)a.push([t,parseInt(30*Math.random())]);for(var r=[],t=0;10>=t;t+=1)r.push([t,parseInt(30*Math.random())]);var o=0,s=!0,l=!1,n=!1;$(".stackControls input").click(function(i){i.preventDefault(),o="With stacking"==$(this).val()?!0:null,e()}),$(".graphControls input").click(function(i){i.preventDefault(),s=-1!=$(this).val().indexOf("Bars"),l=-1!=$(this).val().indexOf("Lines"),n=-1!=$(this).val().indexOf("steps"),e()}),e()}}if(jQuery.plot){var s=[],l=250;i(),t(),a(),r(),o()}},initBarCharts:function(){function e(e){var t=[],a=100+e,r=200+e;for(i=1;i<=20;i++){var o=Math.floor(Math.random()*(r-a+1)+a);t.push([i,o]),a++,r++}return t}var t=e(0),a={series:{bars:{show:!0}},bars:{barWidth:.8,lineWidth:0,shadowSize:0,align:"left"},grid:{tickColor:"#eee",borderColor:"#eee",borderWidth:1}};0!==$("#chart_1_1").size()&&$.plot($("#chart_1_1"),[{data:t,lines:{lineWidth:1},shadowSize:0}],a);var r=[[10,10],[20,20],[30,30],[40,40],[50,50]],a={series:{bars:{show:!0}},bars:{horizontal:!0,barWidth:6,lineWidth:0,shadowSize:0,align:"left"},grid:{tickColor:"#eee",borderColor:"#eee",borderWidth:1}};0!==$("#chart_1_2").size()&&$.plot($("#chart_1_2"),[r],a)},initPieCharts:function(){function e(e,i,t){t&&(percent=parseFloat(t.series.percent).toFixed(2),$("#hover").html('<span style="font-weight: bold; color: '+t.series.color+'">'+t.series.label+" ("+percent+"%)</span>"))}function i(e,i,t){t&&(percent=parseFloat(t.series.percent).toFixed(2),alert(""+t.series.label+": "+percent+"%"))}var t=[],a=Math.floor(10*Math.random())+1;a=5>a?5:a;for(var r=0;a>r;r++)t[r]={label:"Series"+(r+1),data:Math.floor(100*Math.random())+1};0!==$("#pie_chart").size()&&$.plot($("#pie_chart"),t,{series:{pie:{show:!0}}}),0!==$("#pie_chart_1").size()&&$.plot($("#pie_chart_1"),t,{series:{pie:{show:!0}},legend:{show:!1}}),0!==$("#pie_chart_2").size()&&$.plot($("#pie_chart_2"),t,{series:{pie:{show:!0,radius:1,label:{show:!0,radius:1,formatter:function(e,i){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(i.percent)+"%</div>"},background:{opacity:.8}}}},legend:{show:!1}}),0!==$("#pie_chart_3").size()&&$.plot($("#pie_chart_3"),t,{series:{pie:{show:!0,radius:1,label:{show:!0,radius:.75,formatter:function(e,i){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(i.percent)+"%</div>"},background:{opacity:.5}}}},legend:{show:!1}}),0!==$("#pie_chart_4").size()&&$.plot($("#pie_chart_4"),t,{series:{pie:{show:!0,radius:1,label:{show:!0,radius:.75,formatter:function(e,i){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(i.percent)+"%</div>"},background:{opacity:.5,color:"#000"}}}},legend:{show:!1}}),0!==$("#pie_chart_5").size()&&$.plot($("#pie_chart_5"),t,{series:{pie:{show:!0,radius:.75,label:{show:!0,radius:.75,formatter:function(e,i){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(i.percent)+"%</div>"},background:{opacity:.5,color:"#000"}}}},legend:{show:!1}}),0!==$("#pie_chart_6").size()&&$.plot($("#pie_chart_6"),t,{series:{pie:{show:!0,radius:1,label:{show:!0,radius:2/3,formatter:function(e,i){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(i.percent)+"%</div>"},threshold:.1}}},legend:{show:!1}}),0!==$("#pie_chart_7").size()&&$.plot($("#pie_chart_7"),t,{series:{pie:{show:!0,combine:{color:"#999",threshold:.1}}},legend:{show:!1}}),0!==$("#pie_chart_8").size()&&$.plot($("#pie_chart_8"),t,{series:{pie:{show:!0,radius:300,label:{show:!0,formatter:function(e,i){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(i.percent)+"%</div>"},threshold:.1}}},legend:{show:!1}}),0!==$("#pie_chart_9").size()&&$.plot($("#pie_chart_9"),t,{series:{pie:{show:!0,radius:1,tilt:.5,label:{show:!0,radius:1,formatter:function(e,i){return'<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'+e+"<br/>"+Math.round(i.percent)+"%</div>"},background:{opacity:.8}},combine:{color:"#999",threshold:.1}}},legend:{show:!1}}),0!==$("#donut").size()&&$.plot($("#donut"),t,{series:{pie:{innerRadius:.5,show:!0}}}),0!==$("#interactive").size()&&($.plot($("#interactive"),t,{series:{pie:{show:!0}},grid:{hoverable:!0,clickable:!0}}),$("#interactive").bind("plothover",e),$("#interactive").bind("plotclick",i))},initAxisLabelsPlugin:function(){for(var e=[],i=0;i<2*Math.PI;i+=.25)e.push([i,Math.sin(i)]);for(var t=[],i=0;i<2*Math.PI;i+=.25)t.push([i,Math.cos(i)]);for(var a=[],i=0;i<2*Math.PI;i+=.1)a.push([i,Math.tan(i)]);var r={axisLabels:{show:!0},xaxes:[{axisLabel:"hor label",tickColor:"#eee"}],yaxes:[{position:"left",axisLabel:"ver label",tickColor:"#eee"},{position:"right",axisLabel:"bleem"}],grid:{borderColor:"#eee",borderWidth:1}};$.plot($("#chart_1_1"),[{label:"sin(x)",data:e,lines:{lineWidth:1},shadowSize:0},{label:"cos(x)",data:t,lines:{lineWidth:1},shadowSize:0},{label:"tan(x)",data:a,lines:{lineWidth:1},shadowSize:0}],r)}}}();jQuery(document).ready(function(){ChartsFlotcharts.init(),ChartsFlotcharts.initCharts(),ChartsFlotcharts.initPieCharts(),ChartsFlotcharts.initBarCharts(),ChartsFlotcharts.initAxisLabelsPlugin()});
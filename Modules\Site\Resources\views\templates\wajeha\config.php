<?php
	/*
		AVAILABLE BODY CLASSES:
		
		smoothscroll 			= create a browser smooth scroll
		enable-animation		= enable WOW animations

		bg-grey					= grey background
		grain-grey				= grey grain background
		grain-blue				= blue grain background
		grain-green				= green grain background
		grain-blue				= blue grain background
		grain-orange			= orange grain background
		grain-yellow			= yellow grain background
		
		boxed 					= boxed layout
		pattern1 ... patern11	= pattern background
		menu-vertical-hide		= hidden, open on click
		
		BACKGROUND IMAGE [together with .boxed class]
		data-background="assets/images/boxed_background/1.jpg"
	*/
	/*
		AVAILABLE HEADER CLASSES

		Default nav height: 96px
		.header-md 		= 70px nav height
		.header-sm 		= 60px nav height

		.noborder 		= remove bottom border (only with transparent use)
		.transparent	= transparent header
		.translucent	= translucent header
		.sticky			= sticky header
		.static			= static header
		.dark			= dark header
		.bottom			= header on bottom
		
		shadow-before-1 = shadow 1 header top
		shadow-after-1 	= shadow 1 header bottom
		shadow-before-2 = shadow 2 header top
		shadow-after-2 	= shadow 2 header bottom
		shadow-before-3 = shadow 3 header top
		shadow-after-3 	= shadow 3 header bottom

		.clearfix		= required for mobile menu, do not remove!

		Example Usage:  class="clearfix sticky header-sm transparent noborder"
	*/

return [
	'theme_name' => 'wajeha',
	'theme_modes' => [ // body classes
		'smoothscroll',
		'enable-animation',
		'boxed'
	],
	'backgrounds' => [
		'bg-grey',
		'grain-grey',
		'grain-blue',
		'grain-green',
		'grain-orange',
		'grain-yellow',
	],
	'headers' => [
		'noborder',	// remove bottom border (only with transparent use)
		'transparent',	// transparent header
		'translucent',	// translucent header
		'sticky',	// sticky header
		'static',	// static header
		'dark',	// dark header
		'bottom',	// header on bottom
	],
	'sliders' => [
		'elastic_slider' => [
			'title',
			'image',
			'link'
		],
		'slider_camera' => [
			'title',
			'image'
		],
		'nivo_slider' => [
			'title',
			'image'
		],
	],
	'contact_page' => [
		'contact1',
		'contact2',
		'contact3',
		'contact4',
		'contact5',
	],
	'footers' => [
		'minimal',
		'flat_gray',
		'footer5',
	],
	'theme_colors' => [
		'blue',
		'darkblue',
		'green',
		'orange',
		'red',
		'brown',
		'darkgreen',
		'lightgrey',
		'pink',
		'yellow'
	],
	'settings' => [
		'show_student_registeration_link'=> 'boolean',
		'show_student_guardian_login_link'=> 'boolean',
		'show_contact_page_link'=> 'boolean',

		'theme_boxed_layout'=> 'boolean',
		'theme_enable_animation'=> 'boolean',
		'theme_smooth_scroll'=> 'boolean',
		'theme_slider'  	=> 'sliders',
		'theme_header'		=> 'headers',			
		'theme_footer'		=> 'footers',
		'theme_contact_page'		=> 'contact_page',
		'theme_background'	=> 'backgrounds',
		'theme_color'		=> 'theme_colors',
	],
	'widgets' => [
		'call_to_action' => [
			'type' => 'one_block',
			'name' => 'call_to_action',
			'widget_settings' => [
				'title' => 'multilang_string',
				'description' => 'multilang_string',
				'theme' => [
					'type' => 'select',
					'options' => ['transparent' , 'success', 'primary' , 'warning' , 'danger' ]
				],
				'button_text' => 'multilang_string',
				'button_link' => 'multilang_string'
			]
		],
		'info_block' => [
			'type' => 'one_block',
			'name' => 'info_block',
			'widget_settings' => [
				'title' => 'multilang_string',
				'tagline' => 'multilang_string',
				'description' => 'text',
				'image' => 'image',
				'button_text' => 'multilang_string',
				'button_link' => 'multilang_string'
			]
		],
		'intro' => [
			'type' => 'one_block',
			'name' => 'intro',
			'widget_settings' => [
				'title' => 'multilang_string',
				'description' => 'multilang_string',
				'dark_background' => 'boolean',
			]
		],
		'stats' => [
			'type' => 'blocks',
			'name' => 'stats',
			'number_of_blocks' => [
				'type' => 'select',
				'options' => [4, 8, 12]
			],
			"block_elements" => [
				'title' => 'multilang_string',
				'icon' => 'icon',
				'icon_color' => 'color',
				'stat' => 'number',
			],
			'widget_settings' => [
				'is_parallax' => 'boolean',
				'background_image' => 'image'
			]
		],
		'logos_carousel' => [
			'type' => 'blocks',
			'name' => 'logos_carousel',
			'number_of_blocks' => [
				'type' => 'select',
				'options' => range(4,20)
			],
			"block_elements" => [
				'name' => 'multilang_string',
				'logo' => 'image',
			],
			'widget_settings' => [
				'title' => 'multilang_string',
				'background_color' => 'color'
			]
		],
		'features' => [
				'type' => 'blocks',
				'name' => 'features',
				'number_of_blocks' => [
					'type' => 'select',
					'options' => range(4,20)
				],
				"block_elements" => [
					'icon' => 'icon',
					'icon_color' => 'color',
					'name' => 'multilang_string',
					'link' => 'multilang_string',
				],
				'widget_settings' => [
					'title' => 'multilang_string',
					'background_color' => 'color'
				]
			],
		// 'partners' => [
		// 	'type' => 'blocks',
		// 	'name' => 'partners',
		// 	'number_of_blocks' => [
		// 		'type' => 'select',
		// 		'options' => range(4, 20)
		// 	],
		// 	"block_elements" => [
		// 		'logo' => 'image',
		// 		'name' => 'multilang_string'
		// 	],
		// 	'widget_settings' => [
		// 		'title' => 'multilang_string',
		// 		// 'background_color' => 'color'
		// 	]
		// ],
		'testimonials' => [
			'type' => 'blocks',
			'name' => 'testimonials',
			'number_of_blocks' => [
				'type' => 'select',
				'options' => range(3, 20)
			],
			"block_elements" => [
				'image' => 'image',
				'name' => 'multilang_string',
				'designation' => 'multilang_string',
				'testimonial' => 'multilang_string'
			],
			'widget_settings' => [
				'title' => 'multilang_string',
				// 'background_color' => 'color'
			]
		],
		'social_feeds' => [
			'type' => 'one_block',
			'name' => 'social_feeds',
			'widget_settings' => [
				'show_facebook_feeds' => 'boolean',
				'show_twitter_feeds' => 'boolean',
				'show_youtube_feeds' => 'boolean',
				'youtube_video_link' => 'string',
				// 'background_color' => 'color'
			]
		],
		'home_news' => [
			'type' => 'one_block',
			'name' => 'home_news',
			'data_source' => 'news',
			'widget_settings' => [
				'block_title' => 'multilang_string',
				// 'background_color' => 'color'
			]
		],

	]


];
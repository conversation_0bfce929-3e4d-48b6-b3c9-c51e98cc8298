<?php

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\ClassStudent;
use App\Student;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Role;
use App\Permission;
use App\Authorizable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Validation\Rule;
use Modules\RolePermission\Entities\PermissionAssign;


class AddNewStudenttoHalaqahController extends Controller
{
    public function __invoke(Request $request)
    {




        $validator = $this->validate($request,
            ['halaqah' => 'required',
                'student_id' =>'required'] );




        try {
            \DB::beginTransaction();




        $class_student = ClassStudent::insert(
            [
                'student_id' => $request->student_id,
                'class_id'   => $request->halaqah,
                'start_date'   => $request->get('start_at'),
                'created_at'   => Carbon::now(),
                'added_at'   => Carbon::now()
            ]);
            \DB::commit();



            $student =  Student::find($request->get('student_id'));
            $student->status  = "active";
            $student->delete_reason=null;
            $student->deleted_at=null;
            $student->delete_notice=null;
            $student->save();

            Admission::where('student_id',$student->id)->update(['status' => "active"]);

        return response()->json(['message' => 'successfully added the student to this Halaqah'],201);



        } catch (\Exception $exception) {
            \Log::error($exception);

            \DB::rollBack();
            $errorMessage = $exception->getMessage();
            return response()->json(['message' => $exception->getMessage()]);
        }

    }



}

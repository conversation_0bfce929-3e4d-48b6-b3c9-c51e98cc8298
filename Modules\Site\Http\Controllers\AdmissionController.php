<?php

namespace Modules\Site\Http\Controllers;

use App\BaseSetup;
use App\Notifications\UserStatusChangedToNewApplication;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Admission;
use App\Student;
use App\StudentPayment;
use Auth;
use Illuminate\Validation\Rule;

class AdmissionController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view('site::admission.create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request, $student_id = null)
    {
        if (Auth::guard('student')->check()) {
            $student_id = Auth::user()->id;
            $creator_role = 'student';
        } elseif (Auth::guard('guardian')->check()) {

            $creator_role = 'guardian';
            $student_id = $request->student_id;

        } elseif (Auth::guard('employee')->check()) {
            //  if(!auth()->user()->can('register student') || !$request->student_id){

            //     flash('Error. Not Authorized');

            //     return redirect()->back();
            //  }
            $creator_role = 'employee';
            $student_id = $request->student_id;
        }

        $student = Student::findOrfail($student_id);


        $admission = new Admission;

        $admission->organization_id = config('organization_id');
        $admission->student_id = $student_id;
        $admission->creator_role = $creator_role;
        $admission->created_by = Auth::user()->id;;
        $admission->center_id = $request->center_id;
        $admission->class_id = $request->class_id;
        $admission->start_date = $request->start_date;
        $admission->status = 'new_admission';

        $admission->save();

        $admission->programs()->attach($request->program_id);

        $student->status = "new_admission";
        $student->save();

        flash('Registered Successfuly');


        if (Auth::guard('guardian')->check()) {
            return redirect()->route('guardian.login');

        } else {
            if ($request->ajax()) {

                // send email to the user
                $student->notify(new UserStatusChangedToNewApplication($student, $request->program_id, $request->center_id, $request->class_id));
                return \response()->json("successfully added the admission details");
            }
            return redirect()->back();
        }
    }

    /**
     * Student/Guardian approval of admission offer
     * @param Request $request
     * @return Response
     */

    public function changeStatusToNewAdmission(Request $request, $student_id = null)
    {

        try {
            if ($request->ajax()) {

                $validation = \Validator::make($request->all(), [
                    'id' => 'required',
                    'program_id' => 'required|exists:programs,id',
                    'center_id' => 'required|exists:centers,id',
                    'class_id' => 'required|exists:classes,id'
                ]);
                if ($validation->passes()) {
                if (Auth::guard('student')->check()) {
                    $student_id = Auth::user()->id;
                    $creator_role = 'student';
                } elseif (Auth::guard('guardian')->check()) {

                    $creator_role = 'guardian';
                    $student_id = $request->id;

                } elseif (Auth::guard('employee')->check()) {
                    //  if(!auth()->user()->can('register student') || !$request->student_id){

                    //     flash('Error. Not Authorized');

                    //     return redirect()->back();
                    //  }
                    $creator_role = 'employee';
                    $student_id = $request->id;
                }


                $student = Student::withTrashed()->findOrfail($student_id);

                $student->joint_classes()->updateExistingPivot($request->class_id, ['deleted_at' => Carbon::now()]);

                $admission = new Admission;

                $admission->organization_id = config('organization_id');
                $admission->student_id = $student_id;
                $admission->creator_role = $creator_role;
                $admission->created_by = Auth::user()->id;;
                $admission->center_id = $request->center_id;
                $admission->class_id = $request->class_id;
                $admission->program_id = $request->program_id;
                $admission->date_of_birth = $student->date_of_birth;
                $admission->student_email = $student->email;
                $admission->gender_id = BaseSetup::where('base_group_id', '=', '1')->where('base_setup_name',$student->gender)->first()->id;
                $admission->student_mobile = $student->mobile ?? $student->mobile_2;
                $admission->start_date = date('Y-m-d');
                $admission->status = 'new_admission';

                $admission->save();

                $admission->programs()->attach($request->program_id);

                $student->status = "new_admission";
                $student->deleted_at = NULL;
                $student->delete_reason = NULL;
                $student->save();


                // send email to the user

                $student->notify(new UserStatusChangedToNewApplication($student, $request->program_id, $request->center_id, $request->class_id));

                return response()->json("successfully added the admission details");


                if (Auth::guard('guardian')->check()) {
                    return redirect()->route('guardian.login');

                } else {

                    return redirect()->back();
                }
            }
            }


            return response()->json(['error'=>$validation->errors()->all()],422);


        }catch (\Exception $e) {

            dd($e->getMessage());
                return response()->json($e->getMessage(),500);
            }
    }


    public function offer_response(Request $request)
    {
        $requestData = $request->all();

        $admission_id = $request->admission_id;

        $admission = Admission::findOrFail($admission_id);

        if (auth()->user()->id == $admission->student->id ||
            (auth()->guard('guardian')->check() && is_guardian_of_student(auth()->user()->id, $admission->student->id))) {
            if ($request->offer_response == 1) {
                $admission->status = "waiting_for_payment";
            } else {
                if (auth()->guard('guardian')->check()) {
                    $admission->status = "rejected_by_guardian";
                } elseif (auth()->guard('student')->check()) {
                    $admission->status = "rejected_by_student";
                }
            }
            $admission->save();
        }

        return redirect()->back();
    }

    /**
     * Upload Proof of Payment byStudent/Guardian
     * @param Request $request
     * @return Response
     */

    public function payment_proof(Request $request)
    {
        $this->validate($request, [
            'paid_amount' => 'required|numeric',
            'payment_proof' => 'required| mimes:jpeg,jpg,png,pdf,zip | max:5000'
        ]);

        $requestData = $request->all();

        $admission_id = $request->admission_id;

        $admission = Admission::findOrFail($admission_id);

        $file_name = \Illuminate\Support\Str::random(9) . '.' . $request->file('payment_proof')->getClientOriginalExtension();

        $path = 'userfiles/' . userfolder($admission->student->id) . '/' . \Illuminate\Support\Str::random(9) . '_payment_proof/';

        $request->file('payment_proof')->move(
            base_path() . '/public/' . $path,
            $file_name
        );

        $payment_proof = $path . $file_name;


        if (auth()->user()->id == $admission->student->id ||
            (auth()->guard('guardian')->check() && is_guardian_of_student(auth()->user()->id, $admission->student->id))) {
            $payment = new StudentPayment;

            $payment->organization_id = config('organization_id');

            $payment->student_id = $admission->student->id;

            $payment->creator_role = auth()->guard('student')->check() ? 'student' : 'guardian';

            $payment->creator_id = auth()->user()->id;

            $payment->payment_proof = $payment_proof;

            $payment->payment_category = 'program_fees';

            $payment->method_of_payment = 'bank_deposit';

            $payment->admission_id = $admission->id;

            $payment->amount = $request->paid_amount;

            $payment->save();

            $admission->status = 'payment_verification';

            $admission->save();
        }

        return redirect()->back();
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show($slug)
    {
        $menu = Menu::where('slug', '=', \Illuminate\Support\Str::slug($slug))->first();

        if ($menu) {
            return view(theme_path('menu_page'), compact('menu'));
        }

        return 'Opps!! No Page';
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        $menus = Menu::all()->reject(function ($value, $key) use ($id) {
            return $value->id == $id;
        })->pluck('title', 'id');

        $languages = config('app.locales');

        $menu_detail = Menu::findOrFail($id);


        if (isset($edit_mode)) {
            return view(theme_path('menu_page'));
        }

        return view('site::menu.edit', compact('languages', 'menus', 'menu_detail'));
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @return Response
     */
    public function update(Request $request, $id)
    {
        $request->id = $id;

        if ($request->type == 3) {
            $request->slug = "";
        }
        $request->slug = \Illuminate\Support\Str::slug($request->slug);

        $this->validateMenu($request);

        $requestData = $request->all();


        $menu = Menu::findOrFail($id);

        $menu->parent = $request->parent;
        $menu->type = $request->type;
        $menu->slug = $request->slug;
        $menu->status = $request->status;

        foreach ($request->translate as $code => $translate) {
            $menu->translateOrNew($code)->title = $translate['title'];
            $menu->translateOrNew($code)->content = $translate['content'];
        }

        $menu->save();

        flash('Menu updated!');

        return redirect(route('menu.index'));
    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
        Menu::destroy(request()->menu);

        flash('Menu deleted!');

        return redirect(route('menu.index'));
    }

    private function validateMenu($request)
    {
        $rules = [];
        if ($request->type != 3) {
            $ignore = '';

            if (isset($request->id)) {
                $ignore = ',' . $request->id;
            }

            $rules['slug'] = 'required|alpha_dash|unique:menus,slug' . $ignore;
        }

        $rules['translate.*.title'] = 'required|min:3';

        // dd($request->all() , $rules);

        $this->validate($request, $rules);
    }
}

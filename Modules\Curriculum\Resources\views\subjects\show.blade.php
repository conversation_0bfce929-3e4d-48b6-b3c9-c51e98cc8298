@extends('layouts.hound')

@section('content')
<div class="panel panel-default card-view">
    <div class="panel-heading clearfix">
        <h4 class="pull-left">{{ trans('common.subject') }}: {{ $subject->title }} <small class="btn-xs btn-success">{{ $subject->status }}</small></h4>
        <div class="pull-right">
            <a href="{{ url('/workplace/curriculum/subjects') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
            <a href="{{ url('/workplace/curriculum/subjects/' . $subject->id . '/edit') }}" title="Edit Subject"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>
            {!! Form::open([
                'method'=>'DELETE',
                'url' => ['workplace/curriculum/subjects', $subject->id],
                'style' => 'display:inline'
            ]) !!}
                {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                        'type' => 'submit',
                        'class' => 'btn btn-danger btn-xs',
                        'title' => 'Delete Subject',
                        'onclick'=>'return confirm("Confirm delete?")'
                ))!!}
            {!! Form::close() !!}
        </div>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <div class="panel-group accordion-struct" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading" role="tab" id="heading_5">
                        <a role="button" data-toggle="collapse" href="#preface" aria-expanded="false" class="collapsed">{{ trans('common.preface') }}</a> 
                    </div>
                    <div id="preface" class="panel-collapse collapse" role="tabpanel" aria-expanded="false" style="height: 0px;">
                        <div class="panel-body pa-15">
                            {!! $subject->preface !!}
                        </div>
                    </div>
                </div>
                <div class="panel panel-default">
                    <div class="panel-heading activestate" role="tab" id="heading_6">
                        <a class="" role="button" data-toggle="collapse" href="#content" aria-expanded="true">Content </a>
                    </div>
                    <div id="content" class="panel-collapse collapse in" role="tabpanel" aria-expanded="true">
                        <div class="panel-body pa-15">
                            <a class="btn btn-primary pull-right" data-toggle="modal" href='#editContent'>Edit Content</a>

                            <div class="dd" id="nestable">
                                <ol class="dd-list">
                                    @foreach($subject->contents as $content)
                                    <li class="dd-item dd3-item" data-id="{{ $content->id }}">
                                        <div class="dd-handle dd3-handle"></div>
                                        <div class="dd3-content"> {{ $content->title }}  </div>
                                        <div class="dd3-content"> {!! Form::open([
                                            'method'=>'DELETE',
                                            'route' => ['delete_content', $content->id],
                                            'style' => 'display:inline'
                                        ]) !!}
                                            {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                                                    'type' => 'submit',
                                                    'class' => 'btn btn-danger btn-xs',
                                                    'title' => 'Delete ProgramLevel',
                                                    'onclick'=>'return confirm("Confirm delete?")'
                                            ))!!}
                                        {!! Form::close() !!}</div>
                                        
                                    </li>
                                    @endforeach
                                </ol>
                            </div>
                            

                        </div>
                    </div>
                </div>
                <div class="panel panel-default">
                    <div class="panel-heading activestate" role="tab" id="heading_6">
                        <a class="" role="button" data-toggle="collapse" href="#content" aria-expanded="true"> Lessons Evaluation Scheme </a>
                    </div>
                    <div id="content" class="panel-collapse collapse in" role="tabpanel" aria-expanded="true">
                        <div class="panel-body pa-15">
                            <a class="btn btn-primary pull-right" data-toggle="modal" href='#editContent'>Edit Subject lessons Evaluation Scheme</a>
                            <div class="row">
                                <div class="col-sm-4">Evaluation Type</div>
                                <div class="col-sm-8"> Marks [0 - 100]</div>    
                            </div>

                        </div>
                    </div>
                </div>

            </div>

        </div>

    </div>
</div>

<div class="modal fade" id="editContent">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">All Content</h4>
            </div>
            {!! Form::open(['url' => '/workplace/curriculum/subject-contents', 'class' => 'form-horizontal', 'id' => 'editSubjectContents', 'files' => false]) !!}
            {!! Form::hidden('subject_id' , $subject->id ) !!}
            <div class="modal-body">
                <div id="contents_form_errors" class="error alert-danger">
                </div>
        
                <table id="datable_1" class="table table-hover display  pb-30" >
                        <thead>
                            <tr>
                                <th>C</th>
                                <th>Content</th>
                                <th>Category</th>
                                <th>Last Modification</th>
                            </tr>
                        </thead>
                        <tfoot>
                            <tr>
                                <th>C</th>
                                <th>Content</th>
                                <th>Category</th>
                                <th>Last Modification</th>
                            </tr>
                        </tfoot>
                        <tbody>
                            @foreach($contents as $content)
                            <tr>
                                <th>{!! Form::checkbox('contents[]' , $content->id , in_array($content->id, $subject->contents->pluck('id')->toArray()) ) !!}</th>
                                <th>{{ $content->title }}</th>
                                <th>{{ $content->category->title }}</th>
                                <th>{{ $content->updated_at }}</th>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
            </div>
            <div class="modal-footer">
                {!! Form::submit(trans('common.save'), ['class' => 'btn btn-primary']) !!}
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
            {!! Form::close() !!}
            
        </div>
    </div>
</div>

@endsection
@section('css')
<link rel="stylesheet" href="{{ asset('assets/workplace/hound/vendors/datatables/media/css/jquery.dataTables.min.css')}}">
<link rel="stylesheet" href="{{ asset('assets/workplace/hound/vendors/nestable2/jquery.nestable.css')}}">
@endsection
@section('js')
<script src="{{ asset('assets/workplace/hound/vendors/datatables/media/js/jquery.dataTables.min.js')}}"></script>
<script src="{{ asset('assets/workplace/hound/vendors/nestable2/jquery.nestable.js')}}"></script>

<script>
    $('#nestable').nestable({
        maxDepth : 1
    })

    $('form#editSubjectContents').submit(function(e){
        e.preventDefault();

        $.ajax({
            type: "post",
            url: '/workplace/curriculum/subject-contents',
            data: $(this).serialize(),
            dataType: "json",
            success: function (response) {
                if(response.status = "success"){
                    window.location.reload();
                }
            }
        }).fail (function (req){
            
            $('#contents_form_errors').text('');
            $('#contents_form_errors').addClass('alert');
            $.each(req.responseJSON , function (index , value) {
                $.each(value, function (key , err) {
                    $('#contents_form_errors').append(err+'<br>');
                })
            })
        });
    })
</script>

@endsection
<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Entities\OutgoingEmail;
use Mo<PERSON>les\JobSeeker\Entities\JobSeekerSetting;
use App\Services\EmailService;
use Exception;

/**
 * Email Recovery Command - Ultimate Recovery Tool for Failed Emails
 * 
 * This command implements comprehensive email recovery capabilities for the 
 * transactional outbox pattern, providing enterprise-grade data recovery
 * and business continuity features.
 * 
 * Epic 8: Transactional Outbox & Ultimate Recovery
 * 
 * Features:
 * - Analyze failed emails with detailed failure patterns
 * - Bulk retry operations with configurable filtering
 * - Export failed emails for external processing
 * - Comprehensive reporting and statistics
 * - Safe recovery operations with rollback capabilities
 * - Integration with circuit breaker protection
 */
final class EmailRecoveryCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'jobseeker:email-recovery 
                          {action : Action to perform: analyze, retry, export, stats, reset-attempts}
                          {--provider= : Filter by email provider (gmail, mailtrap, mail)}
                          {--status= : Filter by status (failed, retry_scheduled, pending)}
                          {--since= : Filter emails since date (Y-m-d format)}
                          {--until= : Filter emails until date (Y-m-d format)}
                          {--limit=100 : Limit number of emails to process}
                          {--dry-run : Show what would be done without actually doing it}
                          {--force : Force operations without confirmation prompts}
                          {--export-format=csv : Export format (csv, json)}
                          {--export-path= : Custom export path}
                          {--max-attempts=5 : Set new max attempts for reset-attempts action}
                          {--batch-size=50 : Number of emails to process in each batch}';

    /**
     * The console command description.
     */
    protected $description = 'Ultimate recovery tool for analyzing and recovering failed emails from the transactional outbox';

    /**
     * Email service for recovery operations
     */
    private EmailService $emailService;

    /**
     * Create a new command instance.
     */
    public function __construct(EmailService $emailService)
    {
        parent::__construct();
        $this->emailService = $emailService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');
        
        $this->info("🚀 Email Recovery Tool - Epic 8: Ultimate Recovery");
        $this->info("Action: " . strtoupper($action));
        $this->line("═══════════════════════════════════════════════════");

        try {
            return match ($action) {
                'analyze' => $this->analyzeFailedEmails(),
                'retry' => $this->retryFailedEmails(),
                'export' => $this->exportFailedEmails(),
                'stats' => $this->showStatistics(),
                'reset-attempts' => $this->resetAttempts(),
                default => $this->handleInvalidAction($action),
            };
        } catch (Exception $e) {
            $this->error("❌ Recovery operation failed: " . $e->getMessage());
            Log::channel('email')->error('EmailRecoveryCommand: Operation failed', [
                'action' => $action,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    /**
     * Analyze failed emails and provide detailed insights
     */
    private function analyzeFailedEmails(): int
    {
        $this->info("🔍 Analyzing failed emails...");
        
        $query = $this->buildFilteredQuery();
        $failedEmails = $query->get();
        
        if ($failedEmails->isEmpty()) {
            $this->info("✅ No failed emails found matching the criteria.");
            return 0;
        }

        $this->displayAnalysisResults($failedEmails);
        return 0;
    }

    /**
     * Display comprehensive analysis results
     */
    private function displayAnalysisResults(Collection $failedEmails): void
    {
        $this->line("");
        $this->info("📊 FAILED EMAIL ANALYSIS REPORT");
        $this->line("═══════════════════════════════════════════════════");

        // Overall Statistics
        $totalFailed = $failedEmails->count();
        $this->line("📈 Total Failed Emails: {$totalFailed}");

        // Failure by Provider
        $byProvider = $failedEmails->groupBy('provider');
        $this->line("\n🏢 Failures by Provider:");
        foreach ($byProvider as $provider => $emails) {
            $count = $emails->count();
            $percentage = round(($count / $totalFailed) * 100, 1);
            $this->line("  • {$provider}: {$count} ({$percentage}%)");
        }

        // Failure by Status
        $byStatus = $failedEmails->groupBy('status');
        $this->line("\n📋 Emails by Status:");
        foreach ($byStatus as $status => $emails) {
            $count = $emails->count();
            $percentage = round(($count / $totalFailed) * 100, 1);
            $statusIcon = $this->getStatusIcon($status);
            $this->line("  {$statusIcon} {$status}: {$count} ({$percentage}%)");
        }

        // Common Error Patterns
        $this->analyzeErrorPatterns($failedEmails);

        // Retry Statistics
        $this->analyzeRetryPatterns($failedEmails);

        // Recovery Recommendations
        $this->provideRecoveryRecommendations($failedEmails);
    }

    /**
     * Analyze common error patterns
     */
    private function analyzeErrorPatterns(Collection $failedEmails): void
    {
        $this->line("\n🚨 Common Error Patterns:");
        
        $errorCounts = [];
        foreach ($failedEmails as $email) {
            if ($email->last_error_message) {
                // Extract error type from message
                $errorType = $this->categorizeError($email->last_error_message);
                $errorCounts[$errorType] = ($errorCounts[$errorType] ?? 0) + 1;
            }
        }

        arsort($errorCounts);
        $top5Errors = array_slice($errorCounts, 0, 5, true);
        
        foreach ($top5Errors as $errorType => $count) {
            $percentage = round(($count / $failedEmails->count()) * 100, 1);
            $this->line("  🔸 {$errorType}: {$count} ({$percentage}%)");
        }
    }

    /**
     * Analyze retry patterns
     */
    private function analyzeRetryPatterns(Collection $failedEmails): void
    {
        $this->line("\n🔄 Retry Analysis:");
        
        $retryStats = [
            'no_retries' => 0,
            'single_retry' => 0,
            'multiple_retries' => 0,
            'max_attempts_reached' => 0,
        ];

        foreach ($failedEmails as $email) {
            if ($email->send_attempts == 0) {
                $retryStats['no_retries']++;
            } elseif ($email->send_attempts == 1) {
                $retryStats['single_retry']++;
            } elseif ($email->send_attempts < $email->max_attempts) {
                $retryStats['multiple_retries']++;
            } else {
                $retryStats['max_attempts_reached']++;
            }
        }

        foreach ($retryStats as $category => $count) {
            $label = ucwords(str_replace('_', ' ', $category));
            $this->line("  📊 {$label}: {$count}");
        }
    }

    /**
     * Provide recovery recommendations
     */
    private function provideRecoveryRecommendations(Collection $failedEmails): void
    {
        $this->line("\n💡 RECOVERY RECOMMENDATIONS:");
        $this->line("═══════════════════════════════════════════════════");

        $retryableCount = $failedEmails->filter(fn($email) => $email->canRetry())->count();
        if ($retryableCount > 0) {
            $this->line("✅ {$retryableCount} emails can be retried safely");
            $this->line("   Command: php artisan jobseeker:email-recovery retry --force");
        }

        $maxAttemptsReached = $failedEmails->filter(fn($email) => $email->send_attempts >= $email->max_attempts)->count();
        if ($maxAttemptsReached > 0) {
            $this->line("🔧 {$maxAttemptsReached} emails reached max attempts - consider resetting");
            $this->line("   Command: php artisan jobseeker:email-recovery reset-attempts --max-attempts=5");
        }

        $oldFailures = $failedEmails->filter(fn($email) => $email->failed_at && $email->failed_at->lt(now()->subDays(7)))->count();
        if ($oldFailures > 0) {
            $this->line("📦 {$oldFailures} emails failed over 7 days ago - consider exporting");
            $this->line("   Command: php artisan jobseeker:email-recovery export --export-format=csv");
        }
    }

    /**
     * Retry failed emails with comprehensive recovery logic
     */
    private function retryFailedEmails(): int
    {
        $this->info("🔄 Preparing to retry failed emails...");
        
        $query = $this->buildFilteredQuery();
        
        // Only get emails that can actually be retried
        $retryableEmails = $query->get()->filter(fn($email) => $email->canRetry() || $email->isReadyForRetry());
        
        if ($retryableEmails->isEmpty()) {
            $this->info("✅ No retryable emails found matching the criteria.");
            return 0;
        }

        $totalCount = $retryableEmails->count();
        $this->line("Found {$totalCount} retryable emails");

        if (!$this->option('dry-run') && !$this->option('force')) {
            if (!$this->confirm("Do you want to proceed with retrying {$totalCount} emails?")) {
                $this->info("❌ Operation cancelled.");
                return 0;
            }
        }

        return $this->processRetryBatch($retryableEmails);
    }

    /**
     * Process retry operations in batches
     */
    private function processRetryBatch(Collection $emails): int
    {
        $batchSize = (int)$this->option('batch-size');
        $isDryRun = $this->option('dry-run');
        
        $successCount = 0;
        $failureCount = 0;
        
        $progressBar = $this->output->createProgressBar($emails->count());
        $progressBar->setFormat('verbose');
        $progressBar->start();

        foreach ($emails->chunk($batchSize) as $batch) {
            foreach ($batch as $email) {
                $progressBar->advance();
                
                if ($isDryRun) {
                    $this->line("\n[DRY RUN] Would retry email ID: {$email->id} to {$email->recipient}");
                    continue;
                }

                try {
                    // Reset for retry
                    $email->update([
                        'status' => OutgoingEmail::STATUS_PENDING,
                        'last_error_message' => null,
                        'scheduled_at' => now(),
                    ]);

                    // Extract email data and retry
                    $emailData = $email->email_data ?? [];
                    $result = $this->emailService->send(
                        $email->recipient,
                        $email->subject,
                        $email->body,
                        $emailData['view_data'] ?? [],
                        $emailData['view'] ?? '',
                        $emailData['attachments'] ?? [],
                        $emailData['cc'] ?? [],
                        $emailData['from_email'] ?? null,
                        $emailData['from_name'] ?? null
                    );

                    if ($result === true || (is_array($result) && $result['status'] === 'queued')) {
                        $successCount++;
                    } else {
                        $failureCount++;
                    }

                } catch (Exception $e) {
                    $failureCount++;
                    Log::channel('email')->warning('EmailRecoveryCommand: Retry failed', [
                        'email_id' => $email->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }

        $progressBar->finish();
        $this->line("\n");

        $this->displayRetryResults($successCount, $failureCount, $isDryRun);
        return 0;
    }

    /**
     * Display retry operation results
     */
    private function displayRetryResults(int $successCount, int $failureCount, bool $isDryRun): void
    {
        $this->line("");
        $this->info("📊 RETRY OPERATION RESULTS");
        $this->line("═══════════════════════════════════════════════════");

        if ($isDryRun) {
            $this->line("🔍 DRY RUN COMPLETED - No actual emails were sent");
        } else {
            $this->line("✅ Successfully retried: {$successCount}");
            $this->line("❌ Failed to retry: {$failureCount}");
            
            if ($successCount > 0) {
                $this->info("🎉 Recovery operation completed successfully!");
            }
            
            if ($failureCount > 0) {
                $this->warn("⚠️  Some emails could not be retried. Check logs for details.");
            }
        }
    }

    /**
     * Export failed emails for external processing
     */
    private function exportFailedEmails(): int
    {
        $this->info("📤 Exporting failed emails...");
        
        $query = $this->buildFilteredQuery();
        $emails = $query->get();
        
        if ($emails->isEmpty()) {
            $this->info("✅ No emails found matching the criteria for export.");
            return 0;
        }

        $format = $this->option('export-format');
        $exportPath = $this->generateExportPath($format);
        
        $this->line("Exporting {$emails->count()} emails to: {$exportPath}");
        
        if (!$this->option('dry-run')) {
            $this->performExport($emails, $exportPath, $format);
            $this->info("✅ Export completed successfully!");
        } else {
            $this->line("[DRY RUN] Would export to: {$exportPath}");
        }

        return 0;
    }

    /**
     * Perform the actual export operation
     */
    private function performExport(Collection $emails, string $exportPath, string $format): void
    {
        if ($format === 'csv') {
            $this->exportToCsv($emails, $exportPath);
        } elseif ($format === 'json') {
            $this->exportToJson($emails, $exportPath);
        }

        // Mark emails as exported
        if (!$this->option('dry-run')) {
            $emails->each(function ($email) {
                $email->markAsExported('recovery_command');
            });
        }
    }

    /**
     * Export emails to CSV format
     */
    private function exportToCsv(Collection $emails, string $exportPath): void
    {
        $file = fopen($exportPath, 'w');
        
        // CSV Headers
        fputcsv($file, [
            'ID', 'Recipient', 'Subject', 'Provider', 'Status', 
            'Send Attempts', 'Last Error', 'Created At', 'Failed At'
        ]);

        // CSV Data
        foreach ($emails as $email) {
            fputcsv($file, [
                $email->id,
                $email->recipient,
                $email->subject,
                $email->provider,
                $email->status,
                $email->send_attempts,
                $email->last_error_message,
                $email->created_at->toISOString(),
                $email->failed_at?->toISOString(),
            ]);
        }

        fclose($file);
    }

    /**
     * Export emails to JSON format
     */
    private function exportToJson(Collection $emails, string $exportPath): void
    {
        $exportData = [
            'export_timestamp' => now()->toISOString(),
            'total_emails' => $emails->count(),
            'emails' => $emails->map(function ($email) {
                return [
                    'id' => $email->id,
                    'recipient' => $email->recipient,
                    'subject' => $email->subject,
                    'body' => $email->body,
                    'provider' => $email->provider,
                    'mode' => $email->mode,
                    'status' => $email->status,
                    'send_attempts' => $email->send_attempts,
                    'max_attempts' => $email->max_attempts,
                    'last_error_message' => $email->last_error_message,
                    'error_history' => $email->error_history,
                    'email_data' => $email->email_data,
                    'created_at' => $email->created_at->toISOString(),
                    'failed_at' => $email->failed_at?->toISOString(),
                ];
            })->toArray(),
        ];

        file_put_contents($exportPath, json_encode($exportData, JSON_PRETTY_PRINT));
    }

    /**
     * Show comprehensive statistics
     */
    private function showStatistics(): int
    {
        $this->info("📊 Email System Statistics");
        $this->line("═══════════════════════════════════════════════════");

        // Overall stats
        $total = OutgoingEmail::count();
        $sent = OutgoingEmail::withStatus(OutgoingEmail::STATUS_SENT)->count();
        $failed = OutgoingEmail::withStatus(OutgoingEmail::STATUS_FAILED)->count();
        $pending = OutgoingEmail::withStatus(OutgoingEmail::STATUS_PENDING)->count();
        $queued = OutgoingEmail::withStatus(OutgoingEmail::STATUS_QUEUED)->count();

        $this->line("📈 Overall Statistics:");
        $this->line("  • Total Emails: {$total}");
        $this->line("  • Successfully Sent: {$sent}");
        $this->line("  • Failed: {$failed}");
        $this->line("  • Pending: {$pending}");
        $this->line("  • Queued: {$queued}");

        if ($total > 0) {
            $successRate = round(($sent / $total) * 100, 2);
            $failureRate = round(($failed / $total) * 100, 2);
            $this->line("  • Success Rate: {$successRate}%");
            $this->line("  • Failure Rate: {$failureRate}%");
        }

        // Recent activity (last 24 hours)
        $recent = OutgoingEmail::recent()->count();
        $this->line("\n📅 Recent Activity (24 hours): {$recent} emails");

        // Provider statistics
        $this->displayProviderStatistics();

        return 0;
    }

    /**
     * Display provider statistics
     */
    private function displayProviderStatistics(): void
    {
        $this->line("\n🏢 Provider Statistics:");
        
        $providers = OutgoingEmail::selectRaw('provider, status, COUNT(*) as count')
            ->groupBy(['provider', 'status'])
            ->get()
            ->groupBy('provider');

        foreach ($providers as $provider => $stats) {
            $this->line("  📧 {$provider}:");
            foreach ($stats as $stat) {
                $statusIcon = $this->getStatusIcon($stat->status);
                $this->line("    {$statusIcon} {$stat->status}: {$stat->count}");
            }
        }
    }

    /**
     * Reset attempt counts for failed emails
     */
    private function resetAttempts(): int
    {
        $this->info("🔧 Resetting attempt counts...");
        
        $maxAttempts = (int)$this->option('max-attempts');
        $query = $this->buildFilteredQuery();
        
        $emails = $query->whereIn('status', [
            OutgoingEmail::STATUS_FAILED,
            OutgoingEmail::STATUS_RETRY_SCHEDULED
        ])->get();

        if ($emails->isEmpty()) {
            $this->info("✅ No emails found matching the criteria for reset.");
            return 0;
        }

        $this->line("Found {$emails->count()} emails to reset");

        if (!$this->option('dry-run') && !$this->option('force')) {
            if (!$this->confirm("Reset attempt counts for {$emails->count()} emails to {$maxAttempts}?")) {
                $this->info("❌ Operation cancelled.");
                return 0;
            }
        }

        if (!$this->option('dry-run')) {
            $emails->each(function ($email) use ($maxAttempts) {
                $email->update([
                    'send_attempts' => 0,
                    'max_attempts' => $maxAttempts,
                    'status' => OutgoingEmail::STATUS_PENDING,
                    'last_error_message' => null,
                    'scheduled_at' => now(),
                    'failed_at' => null,
                ]);
            });

            $this->info("✅ Reset {$emails->count()} emails with max attempts set to {$maxAttempts}");
        } else {
            $this->line("[DRY RUN] Would reset {$emails->count()} emails");
        }

        return 0;
    }

    /**
     * Build filtered query based on command options
     */
    private function buildFilteredQuery()
    {
        $query = OutgoingEmail::query();

        // Filter by provider
        if ($provider = $this->option('provider')) {
            $query->byProvider($provider);
        }

        // Filter by status (default to failed if not specified for most actions)
        if ($status = $this->option('status')) {
            $query->withStatus($status);
        } else {
            // Default to failed emails for most recovery operations
            $action = $this->argument('action');
            if (in_array($action, ['analyze', 'retry', 'export', 'reset-attempts'])) {
                $query->whereIn('status', [
                    OutgoingEmail::STATUS_FAILED,
                    OutgoingEmail::STATUS_RETRY_SCHEDULED
                ]);
            }
        }

        // Date filters
        if ($since = $this->option('since')) {
            $query->where('created_at', '>=', Carbon::createFromFormat('Y-m-d', $since));
        }

        if ($until = $this->option('until')) {
            $query->where('created_at', '<=', Carbon::createFromFormat('Y-m-d', $until)->endOfDay());
        }

        // Limit
        if ($limit = $this->option('limit')) {
            $query->limit((int)$limit);
        }

        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Generate export file path
     */
    private function generateExportPath(string $format): string
    {
        if ($customPath = $this->option('export-path')) {
            return $customPath;
        }

        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "failed_emails_export_{$timestamp}.{$format}";
        
        return storage_path("app/email_exports/{$filename}");
    }

    /**
     * Categorize error messages into common types
     */
    private function categorizeError(string $errorMessage): string
    {
        $errorMessage = strtolower($errorMessage);
        
        return match (true) {
            str_contains($errorMessage, 'connection') || str_contains($errorMessage, 'timeout') => 'Connection Issues',
            str_contains($errorMessage, 'authentication') || str_contains($errorMessage, 'auth') => 'Authentication Failures',
            str_contains($errorMessage, 'smtp') => 'SMTP Errors',
            str_contains($errorMessage, 'invalid') || str_contains($errorMessage, 'malformed') => 'Invalid Data',
            str_contains($errorMessage, 'quota') || str_contains($errorMessage, 'limit') => 'Rate Limiting',
            str_contains($errorMessage, 'dns') => 'DNS Issues',
            str_contains($errorMessage, 'certificate') || str_contains($errorMessage, 'ssl') => 'SSL/TLS Issues',
            default => 'Other Errors',
        };
    }

    /**
     * Get status icon for display
     */
    private function getStatusIcon(string $status): string
    {
        return match ($status) {
            OutgoingEmail::STATUS_PENDING => '⏳',
            OutgoingEmail::STATUS_QUEUED => '📤',
            OutgoingEmail::STATUS_SENDING => '📡',
            OutgoingEmail::STATUS_SENT => '✅',
            OutgoingEmail::STATUS_FAILED => '❌',
            OutgoingEmail::STATUS_CANCELLED => '🚫',
            OutgoingEmail::STATUS_RETRY_SCHEDULED => '🔄',
            default => '❓',
        };
    }

    /**
     * Handle invalid action
     */
    private function handleInvalidAction(string $action): int
    {
        $this->error("❌ Invalid action: {$action}");
        $this->line("");
        $this->line("Available actions:");
        $this->line("  • analyze        - Analyze failed emails and provide insights");
        $this->line("  • retry          - Retry failed emails");
        $this->line("  • export         - Export failed emails for external processing");
        $this->line("  • stats          - Show comprehensive email statistics");
        $this->line("  • reset-attempts - Reset attempt counts for failed emails");
        
        return 1;
    }
} 
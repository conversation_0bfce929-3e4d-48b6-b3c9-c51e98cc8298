<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\JobSeeker\Entities\ProviderSetting;

final class ProviderSettingsController extends Controller
{
    public function index()
    {
        $providers = ProviderSetting::orderBy('provider_name')->get();
        return view('modules.jobseeker.admin.providers.index', compact('providers'));
    }

    public function show(string $provider)
    {
        $setting = ProviderSetting::forProvider($provider);
        if (!$setting) {
            $setting = new ProviderSetting([
                'provider_name' => $provider,
                'allow_non_english_default' => false,
                'mixed_as_english' => true,
                'max_rtl_ratio' => 0.0,
            ]);
        }
        return response()->json(['success' => true, 'data' => $setting]);
    }

    public function update(Request $request, string $provider)
    {
        $validated = $request->validate([
            'allow_non_english_default' => 'required|boolean',
            'mixed_as_english' => 'required|boolean',
            'max_rtl_ratio' => 'required|numeric|min:0|max:0.5',
        ]);

        $setting = ProviderSetting::updateOrCreate(
            ['provider_name' => $provider],
            $validated
        );

        return response()->json(['success' => true, 'message' => 'Provider language policy updated', 'data' => $setting]);
    }
}



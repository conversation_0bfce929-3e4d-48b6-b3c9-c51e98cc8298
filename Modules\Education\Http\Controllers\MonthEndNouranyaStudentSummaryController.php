<?php

declare(strict_types=1);

namespace Modules\Education\Http\Controllers;

use App\Classes;
use App\Student;
use App\StudentNouranyaPlan;
use App\StudentNouranyaReport;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Yajra\DataTables\DataTables;

final class MonthEndNouranyaStudentSummaryController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @param int $id  Class ID from the route parameter
     * @return JsonResponse
     */
    public function __invoke(Request $request, int $id): JsonResponse
    {
        try {
            $classId   = $id;
            $studentId = (int) $request->input('studentId');
            $monthYear = $request->input('monthYear');

            if (!$studentId || !$monthYear) {
                return response()->json(['message' => 'Missing required parameters'], 400);
            }

            // Try to parse monthYear (supports "F Y" or ISO formats)
            try {
                $date = Carbon::createFromFormat('F Y', $monthYear);
            } catch (\Throwable $e) {
                // fallback
                $date = Carbon::parse($monthYear);
            }

            $month = $date->month;
            $year  = $date->year;

            // Total Nouranya reports for the student in the given month & class
            $reportsQuery = StudentNouranyaReport::query()
                ->where('student_id', $studentId)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month);

            $totalReports = $reportsQuery->count();

            // Total unique lessons completed (based on to_lesson)
            $totalUniqueLessons = (clone $reportsQuery)->distinct('to_lesson')->count('to_lesson');

            // Average performance (based on nouranya_evaluation_id). If not numeric, just count percentage of non-nulls
            $averagePerformance = (clone $reportsQuery)->whereNotNull('nouranya_evaluation_id')->avg('nouranya_evaluation_id');
            $averagePerformance = $averagePerformance ? round((float) $averagePerformance, 2) : 0.0;

            // Attendance percentage (attended: attendance_id in [1,2])
            // Determine total scheduled classes for the month via timetable if available
            $attendancePercentage = 0.0;
            $class = Classes::find($classId);
            if ($class && $class->timetable) {
                $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
                if ($totalClasses > 0) {
                    $attended = (clone $reportsQuery)->whereIn('attendance_id', [1, 2])->count();
                    $attendancePercentage = min(100.0, ($attended / $totalClasses) * 100);
                }
            }

            // Completion rate (planned vs completed lessons)
            $completionRate = 0.0;
            // Get plan for the month (assumes one plan per month)
            $plan = StudentNouranyaPlan::query()
                ->where('student_id', $studentId)
                ->whereYear('start_date', $year)
                ->whereMonth('start_date', $month)
                ->first();
            if ($plan) {
                $plannedLessons = 0;
                if ($plan->from_lesson && $plan->to_lesson) {
                    $plannedLessons = max(0, (int)$plan->to_lesson - (int)$plan->from_lesson + 1);
                }
                $completedLessons = $totalUniqueLessons;
                if ($plannedLessons > 0) {
                    $completionRate = min(100.0, ($completedLessons / $plannedLessons) * 100);
                }
            }

            $data = [[
                'totalReports'        => $totalReports,
                'totalUniqueLessons'  => $totalUniqueLessons,
                'averagePerformance'  => number_format($averagePerformance, 1) . '%',
                'attendancePercentage'=> number_format($attendancePercentage, 1) . '%',
                'completionRate'      => number_format($completionRate, 1) . '%',
            ]];

            return DataTables::of($data)->toJson();
        } catch (\Throwable $e) {
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }
} 
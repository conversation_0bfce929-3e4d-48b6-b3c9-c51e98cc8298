<?php

namespace Modules\Admission\Http\Controllers;

use App\Admission;
use App\Attendance;
use App\AttendanceOption;
use App\BaseSetup;
use App\Cen_Emp;
use App\Employee;
use App\MoshafJuz;
use App\Role;
use App\Student;
use App\StudentHefzPlan;
use App\YearCheck;
use Brian2694\Toastr\Facades\Toastr;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\Admission\Http\Requests\StudentAdmissionRequest;
use Modules\Admission\Http\Requests\StudentMissingDataRequest;
use Modules\EducationalReports\Http\Requests\CreateHefzPlanRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentAttendance;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class StudentMissingDataController extends Controller
{




    public function update(StudentMissingDataRequest $request)
    {

        DB::beginTransaction();
        try {





            // create or update a record for the teacher center
            $classId = $request->get('classId');

            $student = Student::find($request->get('student_id'));

            $dateOfBirth = $student->date_of_birth; // The date of birth as a string
            $dob = Carbon::parse($dateOfBirth);
            $age = $dob->age; // This calculates the age based on the current date


//            $student->date_of_birth->age
            // Check if the student record exists
            if ($student) {
                // Use transaction to ensure data consistency (aligned with bulk operations)
                DB::transaction(function () use ($student, $request, $classId, $age) {
                    // Check if student already has an admission
                    $admission = $student->admissions()->first();

                    if ($admission) {
                        // Update existing admission (aligned with bulk operations)
                        $admission->update([
                            'program_id' => $request->program_id,
                            'center_id' => $request->center_id,
                            'class_id' => $classId,
                            'status' => 'active', // Aligned with bulk operations
                            'student_email' => $student->email,
                            'student_mobile' => $student->phone,
                            'added_from' => 'individual_assign', // Track source
                        ]);

                        // program() is a BelongsTo relationship - handled by program_id foreign key above
                    } else {
                        // Create new admission (aligned with bulk operations)
                        $admission = Admission::create([
                            'program_id' => $request->program_id,
                            'center_id' => $request->center_id,
                            'class_id' => $classId,
                            'gender_id' => BaseSetup::where('base_setup_name', '=', $student->gender)->first()->id,
                            'creator_role' => Auth::user()->roles->pluck('name')->toArray()[0],
                            'created_by' => Auth::user()->id,
                            'status' => 'active', // Aligned with bulk operations
                            'student_id' => $student->id,
                            'student_email' => $student->email,
                            'student_mobile' => $student->phone,
                            'date_of_birth' => $student->date_of_birth,
                            'age' => $age,
                            'organization_id' => config('organization_id'),
                            'admission_date' => now(),
                            'start_date' => date('Y-m-d'),
                            'added_from' => 'individual_assign', // Track source
                        ]);

                        // program() is a BelongsTo relationship - handled by program_id foreign key above
                    }

                    // Sync student-class relationship (enforce one class per student rule)
                    $student->joint_classes()->sync([$classId => [
                        'start_date' => date('Y-m-d'),
                        'created_at' => now(),
                        'updated_at' => now()
                    ]]);
                });

            }else{     // If the student does not exist, create a new student record
                $userId = $request->get('user_id');

               $user =  \App\User::find($userId);

                $softDeletedStudent = Student::onlyTrashed()
                    ->where('email', $user->email)
                    ->first();

                if ($softDeletedStudent) {
                    $softDeletedStudent->forceDelete(); // Hard delete the soft-deleted record
                }

                $student = new Student();
                $student->user_id = $userId;
                // student number format ( two digits of year+two digits of month+two digits of  hour+two digits of minutes+two digits of  seconds
                $student->student_number = Carbon::now()->format('y') . Carbon::now()->format('n') . Carbon::now()->format('H') . Carbon::now()->format('i') . Carbon::now()->format('s');
                $student->display_name = $user->display_name;
                $student->full_name = $user->full_name;
                $student->full_name_trans = $user->full_name_trans;
                $student->identity_number = strtoupper($request->get('identity_number'));
                $student->nationality = $user->nationality;
                $student->gender = BaseSetup::where('base_setup_name',strtolower($request->get('gender')))->first()->base_setup_name;
                $student->date_of_birth = $request->get('dob');
                $student->email = $user->email;
                $student->mobile = $request->get('mobile');
                $student->organization_id = config('organization_id');

                $student->status = 'new_admission';
                $student->active_status = '0';
                $student->save();

                $admission = new Admission();
                $admission->program_id = $request->program_id;
                $admission->center_id = $request->center_id;
                $admission->class_id = $classId;
                $admission->gender_id = BaseSetup::where('base_setup_name', '=', $student->gender)->first()->id;
                $admission->creator_role = Auth::user()->roles->pluck('name')->toArray()[0];
                $admission->created_by = Auth::user()->id;
                $admission->status = 'new_admission';
                $admission->student_id = $student->id;
                $admission->student_email = $student->email;
                $admission->student_mobile = $student->mobile;
                $admission->date_of_birth = $student->date_of_birth;
                $admission->age = \Carbon\Carbon::parse($request->get('dob'))->age;
                $admission->organization_id = config('organization_id');
                $admission->save();
                // program() is a BelongsTo relationship - handled by program_id foreign key above

                // Sync student-class relationship (enforce one class per student rule)
                $student->joint_classes()->sync([$classId => [
                    'start_date' => date('Y-m-d'),
                    'created_at' => now(),
                    'updated_at' => now()
                ]]);

            }
            DB::commit();


            return response()->json([

                'message' => 'Student is assigned to the related class'
            ], 201);

        }catch (\Exception $e) {
            DB::rollback();

            // Log the error with detailed information
            \Log::error('Error in updating student admission: '.$e->getMessage(), [
                'request_data' => $request->all(),
                'error_trace' => $e->getTraceAsString()
            ]);

            // Provide detailed error information in the response (for development purposes)
            $response = [
                'error' => 'An error occurred while processing your request.',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'request_data' => $request->all()
            ];

            // Return a full detailed error response only in a non-production environment
            if (app()->environment('local', 'staging')) {
                return response()->json($response, 500);
            } else {
                return response()->json($response, 500);
                // Return a generic error message in production
                return response()->json([
                    // 'error' => 'An error occurred while processing your request. Please try again later.',
                    'error' => 'An error occurred while processing your request. Please try again later.',
                ], 500);
            }
            }


    }


    /**
     * Update programs availabilty in class.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function programs(Request $request)
    {
        auth()->user()->can('edit class_programs');

        $id = $request->class_id;

        $class = Classes::findOrFail($id);

        $class->programs()->sync($request->class_programs);

        Session::flash('flash_message', 'Program updated!');

        if ($request->ajax()) {
            return response()->json(['status' => 'success']);
        }

        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Classes::destroy($id);

        Session::flash('flash_message', 'Class deleted!');

        return redirect('workplace/education/classes');
    }


    public function studentReport($student_id)
    {
        return DB::table('student_attendances')
            ->leftJoin('class_reports', 'student_attendances.class_report_id', 'class_reports.id')
            ->leftJoin('student_hefz_report', 'student_attendances.class_report_id', '=', 'student_hefz_report.class_report_id')
            ->leftJoin('evaluation_schema_options as hefz_evaluation', 'hefz_evaluation.id', '=', 'student_hefz_report.hefz_evaluation_id')
            ->leftJoin('student_revision_report', 'student_attendances.class_report_id', '=', 'student_revision_report.class_report_id')
            ->leftJoin('evaluation_schema_options as revision_evaluation', 'revision_evaluation.id', '=', 'student_revision_report.revision_evaluation_id')
            ->select(
                'student_attendances.attendance',
                'student_attendances.note as student_attendance_note',
                'class_reports.*',
                'student_hefz_report.*',
                'hefz_evaluation.title as hefz_evaluation_title',
                'student_revision_report.*',
                'revision_evaluation.title as revision_evaluation_title'
            )
            ->where('student_attendances.student_id', $student_id)
            ->orderBy('class_reports.class_time')
            // ->select('users.*', 'contacts.phone', 'orders.price')
            ->get();
    }

    public function studentExpectedProgressPlan($student_id) // Hefz and Morja'ah Program
    {
    }

    private function getNextReportTime($last_report_time, $timetable, $class)
    {

        if (!$last_report_time) {
            $class_date = $timetable->start_at;
        } else {
            $class_date = Carbon::parse($last_report_time);
            $class_date = $class_date->addDay();
//            $class_date = $last_report_time->addDay();
        }

        while ($class->studentsAtDate($class_date)->count() < 1 && $class_date < Carbon::now()) {
            $class_date = $class_date->addDay();
        }

        while (!$timetable[strtolower($class_date->format('D'))]) {
            $class_date = $class_date->addDay();
        }
        $class_date = $class_date->addDay();
        // $class_date = $class_date->addDay();
        // dump($class_date);

        return $class_date;
    }

    private function errorNoTeacher($class_id)
    {
        $error_msg = "Opps! . Some Subjects In the Class Don't have Teacher!!";

        return view('education::classes.errors', compact('class_id', 'error_msg'));
    }

    private function errorNoTimetable($class_id)
    {
        $error_msg = "Opps! . Some Subjects In the Class Don't have Timetable!!";

        return view('education::classes.errors', compact('class_id', 'error_msg'));
    }


    // V2

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function prepareReport($class_id, $report_id)
    {
        $report = ClassReport::findOrFail($report_id);


        if ($report->status == 'completed') {
            return redirect('workplace/education/classes/' . $report->class_id . '/reports/' . $report->id . '/edit');
        }
        $surats = MoshafSurah::all();

        $suar = $surats->map(function ($surah) {
            return [
                'text' => $surah->name,
                'value' => $surah->id,
                'num_ayat' => $surah->num_ayat
            ];
        });


        $class = Classes::with('students.hefz')->with('students.revision')->findOrFail($report->class_id);


        $subject = [];
        $special_program_data = [];
        $special_program = null;

        $hefzEvaluationOptions = [];
        $revisionEvaluationOptions = [];

        if ($report->subject_id == 0 && $report->program->setting['special_program_code'] && $report->program->setting['special_program_code'] = 'hefz') {
            $special_program_data = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->studentsEvaluation($report);

            $hefzEvaluationOptions = EvaluationSchema::where('target', 'hefz')->first()->options->map(function ($option) {
                return ['text' => $option->code . ' - ' . $option->title, 'value' => $option->id];
            });

            $revisionEvaluationOptions = EvaluationSchema::where('target', 'revision')->first()->options->map(function ($option) {
                return ['text' => $option->code . ' - ' . $option->title, 'value' => $option->id];
            });

            $special_program = 'hefz';
        } else {
            $subject = Subject::findOrFail($report->subject_id);
        }
        // dd($special_program_data);
        $students = [];
        $lessons = [];
        // $class->students->map(function($student){
        //     return [

        //     ]
        // });

        foreach ($class->students as $student) {
            if ($special_program
                && $special_program == 'hefz'
                && isset($special_program_data[$student->id])
            ) {
                $students[$student->id] = $special_program_data[$student->id];
                $students[$student->id]['attandance'] = null;
                $students[$student->id]['evaluation'] = null;
            } else {
                $students[$student->id] = [
                    'attandance' => null,
                    'evaluation' => null,
                    'hefz' => [],
                    'revision' => [],
                    'lesson' => [
                        'id' => null,
                        'evaluations' => json_decode('{}')
                    ]
                ];
                if ($subject) {
                    $lessons = $subject->contents->map(function ($lesson) {
                        $evaluation_schems = [];
                        foreach ($lesson->evaluation_schemas as $evaluation) {
                            foreach ($evaluation->options as $option) {
                                $evaluation_schems[$evaluation->title][] = [
                                    'text' => $option->title,
                                    'value' => $option->id
                                ];
                            }
                        }

                        return [
                            'text' => $lesson->title,
                            'value' => $lesson->id,
                            'evaluation_schems' => $evaluation_schems
                        ];
                    });
                }
            }
        }


        return view('education::classes.reports.v3.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
//        return view('education::classes.reports.v2.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
//        return view('education::classes.reportsBackup.v2.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
    }

    public function storeTempReport($class_id, $report_id)
    {

        $report = ClassReport::findOrFail($report_id);
        $report->temp_data = json_encode(request()->except('_token'));
        $report->save();
    }

    public function storeFinalReport(Request $request, $class_id, $report_id)
    {
        // dd($request->all());
        $this->validate($request, [
            'students.*' => 'required',
            'teacher_attended' => 'required'
        ]);

        $report = ClassReport::findOrFail($report_id);

        StudentAttendance::where('class_report_id', $report->id)->delete();
        StudentHefzReport::where('class_report_id', $report->id)->delete();
        StudentRevisionReport::where('class_report_id', $report->id)->delete();


        $requestData = $request->all();

        if (!$request->teacher_attended) {
            $report->status = 'completed';
            $report->notes = 'teacher is absent';
            $report->temp_data = '';
            $report->save();

            return response()->json(['status' => 'completed'], 200);
        }

        foreach ($request->students as $studentID => $studentReport) {
            $attendance = new StudentAttendance();

            $attendance->organization_id = config('organization_id');
            $attendance->class_report_id = $report->id;
            $attendance->student_id = $studentID;
            $attendance->class_time = $report->class_time;
            $attendance->attendance = $studentReport['attandance'];
            $attendance->created_by = auth()->user()->id;
            $attendance->save();

            if (in_array($attendance->attendance, ['on_time', 'late'])) {
                if ($report->subject_id == 0 && $report->program->setting['special_program_code'] && $report->program->setting['special_program_code'] = 'hefz') {
                    if (isset($studentReport['hefz']) && $studentReport['hefz']) {
                        $hefz_report = new StudentHefzReport();
                        $hefz_plans->student_id = $studentID;
                        $hefz_plans->organization_id = config('organization_id');
                        $hefz_plans->class_id = $report->class_id;
                        $hefz_plans->class_time = $report->class_time;
                        $hefz_plans->created_by = auth()->user()->id;

                        $hefz_plans->hefz_from_surat = $studentReport['hefz']['from_surat'];
                        $hefz_plans->start_from_ayat = $studentReport['hefz']['from_ayat'];
                        $hefz_plans->hefz_to_surat = $studentReport['hefz']['to_surat'];
                        $hefz_plans->to_ayat = $studentReport['hefz']['to_ayat'];

                        $hefz_plans->hefz_evaluation_id = $studentReport['hefz']['evaluation'];
                        $hefz_plans->class_report_id = $report->id;

                        $hefz_plans->save();
                    }
                    if (isset($studentReport['revision']) && $studentReport['revision']) {
                        $revision_report = new StudentRevisionReport();

                        $revision_report->student_id = $studentID;
                        $revision_report->organization_id = config('organization_id');
                        $revision_report->class_id = $report->class_id;
                        $revision_report->created_by = auth()->user()->id;
                        $revision_report->class_time = $report->class_time;


                        $revision_report->revision_from_surat = $studentReport['revision']['from_surat'];
                        $revision_report->revision_from_ayat = $studentReport['revision']['from_ayat'];
                        $revision_report->revision_to_surat = $studentReport['revision']['to_surat'];
                        $revision_report->revision_to_ayat = $studentReport['revision']['to_ayat'];

                        if (isset($studentReport['revision']['revision_note'])) {
                            $revision_report->revision_evaluation_note = $studentReport['revision']['revision_note'];
                        }
                        if (isset($studentReport['revision']['revision_type'])) {
                            $revision_report->revision_type = $studentReport['revision']['revision_type'];
                        }


                        $revision_report->revision_evaluation_id = $studentReport['revision']['evaluation'];

                        $revision_report->class_report_id = $report->id;

                        $revision_report->save();
                    }
                } else {
                    // Todo: ADD REPORT OF NORMAL PROGRAM
                    if (isset($studentReport['lesson']) && $studentReport['lesson']) {
                        $lesson_report = new LessonReport();

                        $lesson_report->student_id = $studentID;
                        $lesson_report->organization_id = config('organization_id');
                        $lesson_report->class_id = $report->class_id;
                        $lesson_report->created_by = auth()->user()->id;
                        $lesson_report->class_time = $report->class_time;


                        $lesson_report->lesson_id = $studentReport['lesson']['id'];

                        if (isset($studentReport['lesson']['note'])) {
                            $lesson_report->note = $studentReport['revision']['note'];
                        }

                        $lesson_report->class_report_id = $report->id;
                        $lesson_report->save();
                        foreach ($studentReport['lesson']['evaluations'] as $label => $evaluation_option) {
                            $lesson_report->evaluations()->create(['evaluation_option_id' => $evaluation_option]);
                        }
                    }
                }
            }
        }
        $report->temp_data = '';
        $report->status = 'completed';
        $report->save();
        return response()->json(['status' => 'completed'], 200);
    }

    private function determineStudyDirection($hefz_from_surat, $to_surat, $start_from_ayat, $to_ayat)
    {


        if (!empty($hefz_from_surat) && !empty($to_surat)) {

            if (($hefz_from_surat == $to_surat) && $start_from_ayat < $to_ayat) {


                return 'forward';

            }

            if (($hefz_from_surat == $to_surat) && $start_from_ayat > $to_ayat) {


                return 'backward';

            }


            if ($hefz_from_surat > $to_surat) {

                return 'backward';

            }

            if ($hefz_from_surat < $to_surat) {

                return 'forward';

            }
        }

    }
}

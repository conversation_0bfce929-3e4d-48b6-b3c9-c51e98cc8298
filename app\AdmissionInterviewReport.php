<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\AdmissionInterviewReport
 *
 * @property int $id
 * @property int $admission_interview_id
 * @property int $employee_id
 * @property int $program_level_id student is rejected if value = 0
 * @property string $attachement
 * @property string $notes
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read \App\AdmissionInterview $interview
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionInterviewReport whereAdmissionInterviewId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionInterviewReport whereAttachement($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionInterviewReport whereCreatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionInterviewReport whereEmployeeId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionInterviewReport whereId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionInterviewReport whereNotes($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionInterviewReport whereProgramLevelId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionInterviewReport whereUpdatedAt($value)
 * @mixin \Eloquent
 * @method static \Illuminate\Database\Eloquent\Builder|AdmissionInterviewReport newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdmissionInterviewReport newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdmissionInterviewReport query()
 */
class AdmissionInterviewReport extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'admission_interview_reports';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['admission_interview_id'
                            , 'employee_id'
                            , 'program_level_id'
                            , 'attachement'
                            , 'notes'
                        ];


    public function interview()
    {
        return $this->belongsTo('App\AdmissionInterview');     
    }


    public function interviewers()
    {
        return $this->belongsTo('App\Employee','employee_id' );
    }

}

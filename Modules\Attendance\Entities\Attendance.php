<?php

namespace Modules\Attendance\Entities;

use App\Employee;
use App\MissedClockOut;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use App\User;
use Auth;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class Attendance extends Model
{
//    use SoftDeletes;


    protected $table = "attendances";
    protected $guarded = ['id'];

    public function user()
    {
//        return $this->belongsTo(User::class);
        return $this->belongsTo(Employee::class);
    }

    public function getTableColumns()
    {
        return $this->getConnection()->getSchemaBuilder()->getColumnListing($this->getTable());
    }

    public static function boot()
    {
        parent::boot();
        static::saving(function ($brand) {
            $brand->created_by = Auth::user()->id ?? null;
        });

        static::updating(function ($brand) {
            $brand->updated_by = Auth::user()->id ?? null;
        });
    }

    public static function get_day_number ( $years,$months,$day )
    {
        if($day=='mon')
        {$day='Monday';}

        elseif($day=='tue')
        {$day='Tuesday';}
        elseif($day=='wed')
        {$day='Wednesday';}
        elseif($day=='thu')
        {$day='Thursday';}
        elseif($day=='fri')
        {$day='Friday';}
        elseif($day=='sat')
        {$day='Saturday';}
        elseif($day=='sun')
        {$day='Sunday';}



        $dayy=$day.'day';
        $monthName = date("F", mktime(0, 0, 0, $months));
        $fromdt=date('Y-m-01 ',strtotime("First Day Of  $monthName $years")) ;
        $todt=date('Y-m-d ',strtotime("Last Day of $monthName $years"));

        $num_day='';
        for ($i = 0; $i < ((strtotime($todt) - strtotime($fromdt)) / 86400); $i++)
        {
            if(date('l',strtotime($fromdt) + ($i * 86400)) ==   $day)
            {
                $num_day++;
            }
        }

        return  $num_day;
    }


    public function monthlyAttendanceReport($employee)
    {

        $now = Carbon::now();
        $startDate = $now->month($now->month)->firstOfMonth()->format('Y-m-d');
        $endDate = today()->format('Y-m-d');
        $current_month = Carbon::createFromFormat('Y-m-d', $startDate);

        /* NEW */
        $end_date = $current_month->copy();
        $date = $current_month->copy();

        $total_working_minutes = 0;
        $total_required_hours = 0;
        $total_working_hours = 0;

        $day = strtolower($date->format('D'));


//        $salary_on_this_date = optional($employee)->salaryOn($date->format('Y-m-d'));


        $record = [];

        $attendance_list = \App\Attendance::where('employee_id', $employee->id)
            ->where(DB::raw('date(clock)'), '>=', $startDate)
            ->where(DB::raw('date(clock)'), '<=', $endDate)
            ->orderBy('clock')
            ->get();


        $attendance_duration = $this->calculateAttendanceDuration($attendance_list);


        $record['day'] = $day;

        $record['worked_hours'] = number_format($attendance_duration['duration'] / 60, 2);
        $record['attendance'] = $attendance_duration['attendance'];

        if ($employee->work_mood == 'per_hour') {
            $total_required_hours = $employee->hours_per_month;
        }


        if ($employee->timetable) {
            $required = $employee->timetable->where('day', $day)->first();


            if ($required) {
                $total_working_hours += $record['worked_hours'];

                if ($employee->work_mood == \App\EmployeeWorkMood::where('slug','per_month')->first()->id) {
                    // $diff = strtotime("2001-01-01 ".$required->clockin.":00")->diffInMinutes(strtotime("2001-01-01 ".$required->clockout.":00"));

                    $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;
                    $total_required_hours += $required_hours;
                    $record['required_hours'] = $required_hours;
                }

            } else {
                $record['required_hours'] = 'off';
            }
        } elseif ($employee->work_mood == 'per_hour') {
            $total_working_hours += $record['worked_hours'];
        }

        $daily_record[$date->format('Y-m-d')] = $record;
        $date->addDay();


//        return $total_working_hours;

        return [
            'current_month' => $current_month,
            'total_required_hours' => $total_required_hours,
            'total_working_hours' => $total_working_hours
        ];
    }

    public
    function WeeklyAttendanceReport($employee)
    {
        // temp ;to be dynamicly loaded

        $weekend = ['Sat', 'Sun'];
        $public_holidays = ['2018-01-01', '2018-01-31'];

        // end of temp


        $report_date = false;


        $now = Carbon::now();
        $startDate = $now->subDays(7)->format('Y-m-d');
        $endDate = today()->format('Y-m-d');
        $current_month = Carbon::createFromFormat('Y-m-d', $startDate);

//        $current_month = $date;

        /* NEW */
        $end_date = $current_month->copy();
        $date = $current_month->copy();

        $total_working_minutes = 0;
        $total_required_hours = 0;
        $total_working_hours = 0;

        $day = strtolower($date->format('D'));

//        $salary_on_this_date = optional($employee)->salaryOn($date->format('Y-m-d'));


        $record = [];

        $attendance_list = Attendance::where('employee_id', $employee->id)
            ->where(DB::raw('date(clock)'), '>=', $startDate)
            ->where(DB::raw('date(clock)'), '<=', $endDate)
            ->orderBy('clock')
            ->get();


        $attendance_duration = $this->calculateAttendanceDuration($attendance_list);


        $record['day'] = $day;

        $record['worked_hours'] = number_format($attendance_duration['duration'] / 60, 2);
        $record['attendance'] = $attendance_duration['attendance'];

        if ($employee->work_mood == 'per_hour') {
            $total_required_hours = $employee->hours_per_month;
        }

        if ($employee->timetable) {
            $required = $employee->timetable->where('day', $day)->first();


            if ($required) {
                $total_working_hours += $record['worked_hours'];

                if ($employee->work_mood ==  4 /** per_month */) {
                    // $diff = strtotime("2001-01-01 ".$required->clockin.":00")->diffInMinutes(strtotime("2001-01-01 ".$required->clockout.":00"));

                    $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;
                    $total_required_hours += $required_hours;
                    $record['required_hours'] = $required_hours;
                }

            } else {
                $record['required_hours'] = 'off';
            }
        } elseif ($employee->work_mood == 'per_hour') {
            $total_working_hours += $record['worked_hours'];
        }

        $daily_record[$date->format('Y-m-d')] = $record;
        $date->addDay();


//        return $total_working_hours;

        return [
            'current_month' => $current_month,
            'total_required_hours' => $total_required_hours,
            'total_working_hours' => $total_working_hours
        ];
    }

    public
    function dailyAttendanceReport($employee)
    {


        $date = today()->format("Y-m");
        // temp ;to be dynamicly loaded

        $weekend = ['Sat', 'Sun'];
        $public_holidays = ['2018-01-01', '2018-01-31'];

        // end of temp


        $report_date = false;

        if ($date) {
            $report_date = Carbon::createFromFormat('Y-m', $date);
        }
        $start_date = $report_date->format('Y-m') . '-' . today()->day;
        $current_month = Carbon::createFromFormat('Y-m-d', $start_date);

//        $current_month = $date;

        /* NEW */
        $end_date = $current_month->copy();
        $date = $current_month->copy();

        $total_working_minutes = 0;
        $total_required_hours = 0;
        $total_working_hours = 0;



        $day = strtolower($date->format('D'));


//        $salary_on_this_date = optional($employee)->salaryOn($date->format('Y-m-d'));


        $record = [];


        $attendance_list = Attendance::where('employee_id', $employee->id)
            ->where(DB::raw('date(clock)'), '=', $date->format('Y-m-d'))
            ->orderBy('clock')
            ->get();


        $attendance_duration = $this->calculateAttendanceDuration($attendance_list);


        $record['day'] = $day;

        $record['worked_hours'] = number_format($attendance_duration['duration'] / 60, 2); // two decimals after 0
        $record['attendance'] = $attendance_duration['attendance'];


        if ($employee->work_mood == 'per_hour') {
            $total_required_hours = $employee->hours_per_month;
        }


        if ($employee->timetable) {
//            $required = $salary_on_this_date->timetable->where('day', $day)->first();

//
            $required = $employee->timetable->where('day', $day)->first();




            if (optional($required)->exists()) {
                $total_working_hours += $record['worked_hours'];

                if ($employee->work_mood == \App\EmployeeWorkMood::where('slug','per_month')->first()->id) {
                    // $diff = strtotime("2001-01-01 ".$required->clockin.":00")->diffInMinutes(strtotime("2001-01-01 ".$required->clockout.":00"));

                    $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;
                    $total_required_hours += $required_hours;
                    $record['required_hours'] = $required_hours;
                }

            } else {
                $record['required_hours'] = 'off';
            }

        } elseif ($employee->work_mood == 'task_based') {

            $total_working_hours = floatval($total_working_hours)+floatval($record['worked_hours']);

        }else{


            $total_working_hours = floatval($total_working_hours)+floatval($record['worked_hours']);

        }

        $daily_record[$date->format('Y-m-d')] = $record;
        $date->addDay();





        return $total_working_hours;

        return [
            'current_month' => $current_month,
            'total_required_hours' => $total_required_hours,
            'total_working_hours' => $total_working_hours
        ];
    }

    private
    function calculateAttendanceDuration($attendance_list)
    {

        $attendance = [];

        $total_working_minutes = 0;

        $counter = 0;
        $steps = 1;


        foreach ($attendance_list as $item) {
            if ($item->type == 'in') {
                $counter++;
            }
            $attendance[$counter][$item->type] = $item;
        }

        foreach ($attendance as $i => $entry) {

            // count hours and minutes
            if (isset($entry['in']) && isset($entry['out'])) {
                $working_minutes = $entry['out']->clock->diffInMinutes($entry['in']->clock);
                $attendance[$i]['duration_in_hours'] = number_format($working_minutes / 60, 2);
                $total_working_minutes += $working_minutes;
            } else {
                $attendance[$i]['duration_in_hours'] = 'Error';
            }
        }


        return ['attendance' => $attendance, 'duration' => $total_working_minutes];
    }

//

    public function previous()
    {
        return $this->where('id',--$this->id)->where('type','in')->first();
    }
    public function next()
    {
        return $this->where('id',++$this->id)->where('type','out')->first();
    }



    public function employee(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {

        return $this->belongsTo(Employee::class);
    }

    public function missedClockout(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {

        return $this->hasMany(MissedClockOut::class,'attendance_id');
    }




}

@media screen and (max-width: 768px) {
  #main {
    padding: 0px;
  }
  #nav {
    margin-bottom: -1px;
  }
  #content > .row {
    margin: 0px;
  }
}

@media screen and (min-width: 768px) {
  #main {
    border-left: 1px solid silver;
  }
}

#wrapper {
  padding: 0px;
}

#wrapper > .panel {
  border-radius: 0;
}

#wrapper > .panel > .panel-heading {
  border-radius: 0;
}

#wrapper > .panel > .panel-heading > .panel-title {
  padding: 10px 0;
}

#wrapper > .row {
  margin: 0px;
}

#fab a:hover, #fab a:focus{
  color: white;
}

.item_name {
  width: 120px;
  overflow:hidden;
  white-space:nowrap;
  text-overflow: ellipsis;
}

.clickable {
  cursor: pointer;
}

.img-preview {
  background-color: #f7f7f7;
  overflow: hidden;
  width: 100%;
  text-align: center;
  height: 200px;
}

.hidden {
  display: none;
}

.square {
  width: 100%;
  padding-bottom: 100%;
  position: relative;
  border: 1px solid rgb(221, 221, 221);
  border-radius: 3px;
  // max-width: 210px;
  max-height: 210px;
}
.visible-xs .square {
  width: 60px;
}
.square > img {
  padding: 5px;
  position: absolute;
  max-width: 100%;
  max-height: 100%;
  margin: 0 auto;
  display: inline-block;
  vertical-align: middle;
}
.square > i {
  font-size: 80px;
  padding: 5px;
  position: absolute;
  top: calc(50% - 40px);
  left: calc(50% - 40px);
}
.visible-xs .square > i {
  font-size: 50px;
  padding: 0px auto;
  padding-top: 5px;
  top: calc(50% - 25px);
  left: calc(50% - 25px);
}

.caption {
  margin-top: 10px;
  margin-bottom: 20px;
}
.caption > .btn-group {
  width: 100%;
}
.caption > .btn-group > .item_name {
  width: calc(100% - 25px);
}
.caption > .btn-group > .dropdown-toggle {
  width: 25px;
}

#lfm-loader{
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: gray;
  opacity: 0.7;
  z-index: 9999;
  text-align: center;
}
#lfm-loader:before {
  content: "";
  display: inline-block;
  vertical-align: middle;
  height: 100%;
}
#lfm-loader img{
  width: 100px;
  margin: 0 auto;
  display: inline-block;
  vertical-align: middle;
}

.table-list-view{
  margin-bottom: 120px;
}
.table-list-view .actions{
  text-align: right;
}
.table-list-view .actions a:hover{
  text-decoration: none;
}
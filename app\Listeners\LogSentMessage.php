<?php

namespace App\Listeners;

use Illuminate\Mail\Events\MessageSending;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class LogSentMessage
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  MessageSending  $event
     * @return void
     */
    public function handle(MessageSending $event)
    {
        $message = $event->message;
        
        // Log email details instead of the entire object
        \Log::info('Email being sent', [
            'to' => $message->getTo(),
            'subject' => $message->getSubject(),
            'from' => $message->getFrom(),
            'timestamp' => now()->toDateTimeString()
        ]);
    }
}

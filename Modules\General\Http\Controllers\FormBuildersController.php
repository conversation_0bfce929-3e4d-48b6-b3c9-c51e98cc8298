<?php

namespace Modules\General\Http\Controllers;

use App\Role;
use App\FormBuilder;
use App\Http\Requests;
// use App\Message;
use Illuminate\Http\Request;
use App\FormBuilderApprovalFlow;
use App\Http\Controllers\Controller;

class FormBuildersController extends Controller
{
    public function index()
    {
        $buliders = FormBuilder::all();
        
        return view('general::forms.builder.index', compact('buliders'));
    }
    
    public function create()
    {
        $messages   =   [];// Message::limit(5)->get();

        $roles = Role::all();

        return view('general::forms.builder.create', compact('roles'));
    }

    public function store(Request $request)
    {
        $this->validator();
        
        $request->merge(["organization_id" => config("organization_id")]);

        $builder = FormBuilder::create($request->all());
        // dd($builder->save());

        foreach ($request->translate as $lang => $fields) {
            foreach ($fields as $key => $value) {
                $builder->{$key.':'.$lang} = $value;
            }
        }
        $builder->save();

        foreach ($request->approval_flow as $step) {
            FormBuilderApprovalFlow::create([
                'form_builder_id' => $builder->id,
                'step_order' => $step['step_order'],
                'role' => $step['role'],
                'can_reject' => $step['can_reject'] ?? 0,
                'can_approve' => $step['can_approve'] ?? 0,
                'can_request_clearfication' => $step['can_request_clearfication'] ?? 0,
                'has_final_approval' => $step['has_final_approval'] ?? 0,
            ]);
        }

        $builder->save();

        return redirect(route('general.form.builder.index'));
    }


    public function edit($id)
    {
        $builder    =   FormBuilder::findOrFail($id);
        $roles = Role::all();

        $messages       =  [];// Message::limit(5)->get();
        return view('general::forms.builder.edit', compact('builder', 'roles'));
    }

    public function update(Request $request, $id)
    {
        $this->validator();

        $builder = FormBuilder::findOrFail($id);
        
        $builder->update($request->all());

        foreach ($request->translate as $lang => $fields) {
            foreach ($fields as $key => $value) {
                $builder->{$key.':'.$lang} = $value;
            }
        }
        $builder->approvalFlow()->delete();
        foreach ($request->approval_flow as $step) {
            FormBuilderApprovalFlow::create([
                'form_builder_id' => $builder->id,
                'step_order' => $step['step_order'],
                'role' => $step['role'],
                'can_reject' => $step['can_reject'] ?? 0,
                'can_approve' => $step['can_approve'] ?? 0,
                'can_request_clearfication' => $step['can_request_clearfication'] ?? 0,
                'has_final_approval' => $step['has_final_approval'] ?? 0,
            ]);
        }

        $builder->save();

        return redirect(route('general.form.builder.index'));
    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
        return Request::all();
        // $department=Department::findOrFail($id);

        // $department->delete();

        // return redirect()->back();
    }

    public function delete($id)
    {
    }


    private function validator($attr = [])
    {
        $this->validate(request(), [
            "translate.*.title" => "required",
            "translate.*.date_label" => "required",
            "date_or_range" => "required",
            "request_time" => "required",
            "approval_flow" => "required"
        ]);
    }
}

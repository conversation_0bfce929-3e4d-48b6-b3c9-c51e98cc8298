<?php

namespace Modules\General\Traits;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait BackupRollbackTrait
{
    /**
     * Store the original state of class_students table before restore
     * 
     * @param array $classIds
     * @return array
     */
    private function storeOriginalState(array $classIds): array
    {
        $originalState = [];
        
        foreach ($classIds as $classId) {
            $originalState[$classId] = DB::table('class_students')
                ->where('class_id', $classId)
                ->whereNull('deleted_at')
                ->get()
                ->toArray();
        }
        
        return $originalState;
    }

    /**
     * Perform rollback of class_students table to original state
     * 
     * @param array $originalState
     * @param array $classIds
     * @return bool
     */
    private function performRollback(array $originalState, array $classIds): bool
    {
        try {
            DB::beginTransaction();

            // First, remove all current records for these classes
            DB::table('class_students')
                ->whereIn('class_id', $classIds)
                ->whereNull('deleted_at')
                ->delete();

            // Then restore the original records
            foreach ($originalState as $classId => $records) {
                foreach ($records as $record) {
                    DB::table('class_students')->insert((array) $record);
                }
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::channel('backup-restore')->error('Rollback failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'class_ids' => $classIds
            ]);
            return false;
        }
    }

    /**
     * Log rollback attempt
     * 
     * @param int $backupId
     * @param array $classIds
     * @param bool $success
     * @param string|null $error
     * @return void
     */
    private function logRollbackAttempt(int $backupId, array $classIds, bool $success, ?string $error = null): void
    {
        Log::channel('backup-restore')->info('Rollback attempt', [
            'backup_id' => $backupId,
            'class_ids' => $classIds,
            'success' => $success,
            'error' => $error
        ]);
    }
} 
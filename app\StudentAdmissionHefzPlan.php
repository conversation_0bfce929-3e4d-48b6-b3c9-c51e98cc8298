<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class StudentAdmissionHefzPlan extends Model
{
    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'admission_hefz_plans';
    protected $casts = ['start_date'];
    protected $appends = ['NooFPages'];

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    protected $fillable = [
        'class_id',
        'organization_id',
        'student_id',
        'class_report_id',
        'plan_year_and_month',
        'start_date',
        'end_date',
        'study_direction',
        'level_id',
        'center_id',
        'status',
        'approved_by',
        'supervisor_comment',
        'created_by',
        'updated_by',
        'delete_reason',
        'deleted_at',
        'num_to_memorize',
        'memorization_mood',
        'pages_to_revise',
    ];
    protected static function boot() {


        parent::boot();

        static::creating(function ($model) {
            $model->created_by = is_object(\Auth::guard('employee')->user()) ? \Auth::guard('employee')->user()->id : 1;
            $model->updated_by = NULL;
        });

        static::updating(function ($model) {
            $model->updated_by = is_object(\Auth::guard('employee')->user()) ? \Auth::guard('employee')->user()->id : 1;
        });
    }

    public function creator(){

        return $this->belongsTo(Employee::class,'created_by');
    }

    public function updator(){

        return $this->belongsTo(Employee::class,'updated_by');
    }


    public function student(){

        return $this->belongsTo(Student::class);
    }


    public function teacher()
    {

        return $this->belongsTo(Employee::class,'teacher_id','id');
    }

    public function halaqah(){


        return $this->belongsTo(Classes::class,'class_id','id','student_hefz_plans');
    }










}

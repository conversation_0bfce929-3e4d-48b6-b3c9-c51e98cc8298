# Itqan Al Quran Project Structure

## Overview
This document provides a comprehensive overview of the Itqan Al Quran project structure, architecture, and key components. The project is built using Laravel 10 with a modular architecture leveraging the nwidart/laravel-modules package.

## Core Technologies
- **Backend**: Laravel 10, PHP 8.1+
- **Frontend**: Bootstrap 3/5, jQuery
- **Database**: MySQL
- **Modular Structure**: nwidart/laravel-modules

## Directory Structure

### Root Level Directories
- `app/` - Core application files (Laravel framework)
- `bootstrap/` - Laravel bootstrap files
- `config/` - Configuration files
- `database/` - SQL files for database schema and seed data
- `Modules/` - Modular components of the application
- `public/` - Publicly accessible files
- `resources/` - Frontend resources (views, assets)
- `routes/` - Application routes
- `storage/` - Storage files (logs, cache)
- `tests/` - Application tests
- `vendor/` - Composer dependencies

### Module Structure (`Modules/`)
Each module follows a standard structure:
- `Modules/{ModuleName}/`
  - `Config/` - Module-specific configuration
  - `Console/` - Artisan commands
  - `Database/` - Module database migrations and seeders (not used directly)
  - `Entities/` - Module models
  - `Events/` - Module events
  - `Http/` - Controllers, middleware, and routes
    - `Controllers/` - Module controllers
    - `Middleware/` - Module middleware
    - `Requests/` - Form requests
    - `routes.php` - Web routes
    - `api.php` - API routes
  - `Listeners/` - Event listeners
  - `Providers/` - Service providers
  - `Services/` - Service classes
  - `Repositories/` - Repository classes
  - `Resources/` - Module assets (not used for views)
  - `Tests/` - Module tests

### View Structure
**Important:** Module views are NOT located in the standard `Modules/{ModuleName}/Resources/views/` directory. Instead, they are located in:
- `resources/views/modules/{module-name-lowercase}/`

This is a custom convention specific to this project.

### Primary Modules
- `Admission/` - Student admission management
- `Authentication/` - User authentication
- `Communication/` - Messaging and notifications
- `Education/` - Core educational features
- `General/` - General functionality
- `HumanResource/` - HR management
- `JobSeeker/` - Job seeker functionality
- `MajorTrack/` - Student major tracking
- `Transportation/` - Transportation management

## Key Architectural Patterns

### Modular Architecture
The application is divided into functional modules using nwidart/laravel-modules. Each module is a self-contained unit with its own controllers, models, views, and routes.

### MVC Pattern
The application follows the Model-View-Controller pattern:
- Models (Entities) - Data structure and business logic
- Views - User interface
- Controllers - Request handling and response generation

### Repository Pattern
Complex modules implement the repository pattern to abstract data access:
- `{Module}/Repositories/` - Contains repository interfaces and implementations
- `{Module}/Services/` - Contains business logic services that use repositories

### Service Layer
Business logic is encapsulated in services:
- `{Module}/Services/` - Service classes that implement business logic
- Controllers depend on services rather than implementing logic directly

## Database
- Direct SQL used for schema creation (no migrations)
- Eloquent ORM used for data interaction
- Schema files located in `database/sql_files/`
- Contains timestamp naming convention: YYYYMMDD_HHMMSS_descriptive_name.sql

## Routes
Routes are defined in each module:
- Web routes: `Modules/{ModuleName}/Http/routes.php`
- API routes: `Modules/{ModuleName}/Http/api.php`

## Authentication & Authorization
- Laravel's built-in authentication is used
- Custom middleware is implemented for specific modules
- Role-based access control is implemented

## Frontend
- Bootstrap 3 is used for general UI components
- Bootstrap 5 is used for specific modules (e.g., JobSeeker admin)
- jQuery is used for client-side interactions
- Blade templates for view rendering

## Important Conventions
1. Controllers should be final classes and read-only
2. Models should be final classes
3. Views are stored in `resources/views/modules/{module-name-lowercase}/`
4. Database schema changes should be made using direct SQL files
5. PSR-12 coding standards are followed
6. Strict typing is enforced with `declare(strict_types=1);`

## Documentation
- README files in the root directory for general information
- Module-specific documentation in the `ai_docs/` directory
- SQL files with comments explaining schema changes 
<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;

/**
 * App\BookIssue
 *
 * @property int $id
 * @property int|null $quantity
 * @property string|null $given_date
 * @property string|null $due_date
 * @property string|null $issue_status
 * @property string|null $note
 * @property int $active_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $book_id
 * @property int|null $member_id
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $organization_id
 * @property int|null $academic_id
 * @property-read \App\Book|null $books
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue query()
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereAcademicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereActiveStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereBookId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereDueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereGivenDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereIssueStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereMemberId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereNote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BookIssue whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class BookIssue extends Model
{

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);


    }
    public function books(){
    	return $this->belongsTo('App\Book', 'book_id', 'id');
    }
}

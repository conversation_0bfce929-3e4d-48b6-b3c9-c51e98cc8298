<?php

namespace App\Console\Commands;

use App\Classes;
use App\Student;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class CacheTrashedStudentsCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'cache:trashedstudents';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cache trashed students for improved application performance';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            Log::info('Starting cache:trashedstudents command');
            $this->info('Fetching trashed students...');
            
            // Get start time to measure performance
            $startTime = microtime(true);
            
            $students = Student::onlyTrashed()->get();
            
            $count = $students->count();
            Log::info("Retrieved {$count} trashed students");
            $this->info("Retrieved {$count} trashed students");

            // Put results in cache
            Cache::put('trashedstudents', $students);
            
            $executionTime = round(microtime(true) - $startTime, 2);
            Log::info("Cached {$count} trashed students successfully in {$executionTime} seconds");
            $this->info("Cached {$count} trashed students successfully in {$executionTime} seconds");
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            Log::error('Failed to cache trashed students: ' . $e->getMessage());
            $this->error('Command failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}

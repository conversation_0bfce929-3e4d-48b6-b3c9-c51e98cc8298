@extends('home.layouts.home')
@section('page_title') {{trans('home_header.programs')}}   @endsection

@section('content')
    <section class="page-header dark page-header-xs">
        <div class="container">
            <h1>{{trans('home_header.programs')}} </h1>
        </div>
    </section>

    <section>
        <div class="container">


            <div class="row">
                @foreach($programs as $program)
                    <div class="col-sm-4">
                        <div class="flexslider">
                            <ul class="slides">
                                @foreach($program->attachments as $att)
                                    <li>
                                        <a>
                                            <img style="height: 241px;max-height: 241px;min-height: 241px;"
                                                 src="{{URL::to($att->program_attachment)}}"
                                                 alt="<?php echo $program->{'title_' . App::getLocale()} ?>">
                                            <div class="flex-caption"><?php echo $program->{'title_' . App::getLocale()} ?></div>
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        </div>

                        <h3 class="margin-top-10"><?php echo $program->{'title_' . App::getLocale()} ?></h3>
                        <button type="button" class="btn btn-primary btn-sm" style="margin-bottom:10px;" data-toggle="modal"
                                data-target="#modal_{{$program->id}}">
                            {{trans('home_content.chech_more')}}
                        </button>

                        <div id="modal_{{$program->id}}" class="modal fade" tabindex="-1" role="dialog"
                             aria-labelledby="modaltitle" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <!-- Modal Header -->
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span></button>
                                        <h4 class="modal-title"
                                            id="modaltitle"><?php echo $program->{'title_' . App::getLocale()} ?></h4>
                                    </div>
                                    <!-- Modal Body -->
                                    <div class="modal-body">
                                        <p><?php echo $program->{'description_' . App::getLocale()} ?></p>
                                        <img style=""
                                             src="{{URL::to($att->program_attachment)}}"
                                             alt="<?php echo $program->{'title_' . App::getLocale()} ?>">

                                    </div>
                                    <!-- Modal Footer -->
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-success btn-sm" data-dismiss="modal">
                                            Close
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>


        </div>
    </section>

@endsection
			
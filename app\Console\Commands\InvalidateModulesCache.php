<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class InvalidateModulesCache extends Command
{
    protected $signature = 'cache:invalidate-modules';
    protected $description = 'Invalidate the cache for the modules variables such as main menu and etc';

    /**
     * Execute the console command.
     * 
     * @return int
     */
    public function handle()
    {
        try {
            Log::info('Starting cache:invalidate-modules command');
            
            // Check if modules cache exists before trying to delete it
            if (Cache::has('modules')) {
                Cache::forget('modules');
                Log::info('Cache invalidated for modules successfully');
                $this->info('Module cache successfully invalidated. Next request will rebuild the cache with fresh data.');
            } else {
                Log::info('Modules cache was not found - nothing to invalidate');
                $this->info('No modules cache found to invalidate.');
            }
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            Log::error('Failed to invalidate modules cache: ' . $e->getMessage());
            $this->error('Command failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}

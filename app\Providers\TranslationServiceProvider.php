<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class TranslationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Get locale from session or use default
        $locale = Session::get('locale', config('app.locale'));
        
        // Set locale
        App::setLocale($locale);
        
        // Set document direction based on locale
        $direction = ($locale === 'ar') ? 'rtl' : 'ltr';
        Session::put('direction', $direction);
        
        // Share locale and direction with all views
        view()->share('locale', $locale);
        view()->share('direction', $direction);
    }
} 
<?php

namespace Modules\EducationalReports\Http\Controllers;

use App\AttendanceOption;
use App\Employee;
use App\EvaluationSchemaOption;
use App\StudentAttendance;
use App\Student;
use App\StudentHefzPlan;
use App\StudentRevisionPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class MonthlyItqanReportController extends Controller
{


    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function __invoke(Request $request)
    {


        DB::connection()->enableQueryLog();
        //        if ($request->ajax()) {
        try {
            $planYearMonth = Carbon::parse($request->get('classDate'));
            $year = $planYearMonth->year;
            $month = $planYearMonth->month;



            $centers = Center::with([
                'employee',
                'classes' => function ($query) use ($month, $year) {
                    $query->whereHas('hefz_plans', function ($q) use ($month, $year) {
                        $q->where('status', 'active')
                            ->whereMonth('created_at', $month)
                            ->whereYear('created_at', $year);
                    })->withCount([
                        'completedHefzReport as attendance_days_count' => function ($query) use ($month, $year){
                            $query->whereMonth('created_at', $month)
                                ->whereYear('created_at', $year)
                            ->where('attendance_id', 2);
                        },
                        'completedHefzReport as completed_hefz_count' => function ($query) use ($month, $year) {
                            $query->whereMonth('created_at', $month)
                                ->whereYear('created_at', $year);
                        }
                    ])
                        ->with([
                            'completedHefzReport' => function ($query) use ($month, $year) {
                                $query->whereMonth('created_at', $month)
                                    ->whereYear('created_at', $year);
                            },
//

                            'hefz_plans' => function ($query) use ($month, $year) {
                                $query->where('status', 'active')
                                    ->whereMonth('created_at', $month)
                                    ->whereYear('created_at', $year);
                            }
                        ]);
                },
            ])->whereHas('classes', function ($query) use ($month, $year) {
                $query->whereHas('hefz_plans', function ($q) use ($month, $year) {
                    $q->where('status', 'active')
                        ->whereMonth('created_at', $month)
                        ->whereYear('created_at', $year);
                });
            })->get();

            return \Yajra\DataTables\DataTables::of($centers)
                ->addColumn('attendanceDaysPercentage', function ($center) use ($request, $month, $year) {



                    $attendanceCount = 0;
                    $totalSessions = 0;
                    foreach ($center->classes as $classDetails) {

                        $attendanceCount += $classDetails->attendance_days_count ?? 0;
                        $totalSessions += $classDetails->completed_hefz_count ?? 0;

                    }


                    $result = ($totalSessions > 0) ? round(($attendanceCount / $totalSessions * 100), 2) : 0;


                    return  '<div class="progress" style="position: relative;">
  <div  class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
  </div>
</div>';



                })

                ->addIndexColumn()
                ->addColumn('center', function ($center) use ($request, $month, $year) {

                    return '<a style="color:#b4eeb0; text-decoration: none;" target="_blank" href="/workplace/education/classes?centers[]=' . $center->id . '" class="center-hover-effect">' . $center->name . '</a>';


                })
                ->addColumn('supervisor', function ($center) use ($request, $month, $year) {


                    $supervisorTags = $center->employee()->get()->map(function ($employee) {
                        $supervisorName = ucfirst($employee->name);
                        $supervisorProfileUrl = route('employees.show', ['employee' => $employee->id]);
                        return '<a style="color:#b4eeb0; padding: 2px; display: inline-block; transition: all 0.3s ease;" data-id='.$employee->id.' class="section supervisor-link" target="_blank" href="' . $supervisorProfileUrl . '">' . $supervisorName . '</a>';
                    })->implode(' <span style="color: #1fff0f;"> | </span> ');

                    return $supervisorTags;



                })
                ->addColumn('halaqahsCount', function ($center) use ($request, $month, $year) {



                    $halaqahCount = $center->classes->pluck('hefz_plans.*.class_id')->flatten()->unique()->count();

                    return '<h2 style="color: #1fff0f;
font-weight: bolder;
font-size: 24px;">' . $halaqahCount . '</h2>';



                })
                ->addColumn('studentsCount', function ($center) use ($request, $month, $year) {

//                    This code uses the load method to load the related hefz_plans for each class in the $center. The pluck method is then used to extract the student_ids from the loaded hefz_plans, and the flatten, unique, and count methods are used to calculate the number of unique student_ids
                    $studentCount = $center->classes->pluck('hefz_plans.*.student_id')->flatten()->unique()->count();

                    return '<h2 style="color: #1fff0f;
font-weight: bolder;
font-size: 24px;">' . $studentCount . '</h2>';

                })
                ->addColumn('memorizedPages', function ($center) {
                    $numberofPagesSum = $center->classes->flatMap(function ($classDetails) {
                        return $classDetails->completedHefzReport->map(function ($reportDetails) {
                            if ($reportDetails->hefzPlan->study_direction == 'backward') {
                                $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $reportDetails->hefz_from_surat,
                                    $reportDetails->hefz_from_ayat,
                                    $reportDetails->hefz_to_surat,
                                    $reportDetails->hefz_to_ayat
                                ]);
                                return $numberofPages[0]->numberofPagesSum;
                            } else {
                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $reportDetails->hefz_from_surat,
                                    $reportDetails->hefz_from_ayat,
                                    $reportDetails->hefz_to_surat,
                                    $reportDetails->hefz_to_ayat
                                ]);
                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                return $results[0]->number_of_pages_sum;
                            }
                        });
                    })->sum();

                    return '<h2 style="color: #1fff0f;
font-weight: bolder;
font-size: 24px;">' . $numberofPagesSum . '</h2>';
                })
                ->addColumn('hefzAchievementComparedtoHefzPlan', function ($center) use ($request, $month, $year) {
                    $planYearMonth = Carbon::parse($request->get('classDate'));
                    $memorizedNumberofPagesSum = 0;
                    $plannedNumberofPagesSum = 0;
                    $percentageOfHefzAchievementForEachStudent = [];
                    $plannedNumberofPagesSums =  [];

                    $n = 0;
                    foreach ($center->classes as $classDetails) {
                        $classDetails->load([
                            'students' => function ($query) use ($month, $year) {
                                $query->whereHas('completedHefzReport', function ($q) use ($month, $year) {
                                    $q->whereMonth('created_at', $month)
                                        ->whereYear('created_at', $year);
                                })->whereHas('hefz_plans', function ($q) use ($month, $year) {
                                    $q->where('status', 'active')
                                        ->whereMonth('created_at', $month)
                                        ->whereYear('created_at', $year);
                                });
                            }
                        ]);


                        foreach ($classDetails->students as $studentDetails) {
                            $studentDetails->load([
                                'completedHefzReport' => function ($query) use ($month, $year) {
                                    $query->whereMonth('created_at', $month)
                                        ->whereYear('created_at', $year);
                                },
                                'hefz_plans' => function ($query) use ($month, $year) {
                                    $query->where('status', 'active')
                                        ->whereMonth('created_at', $month)
                                        ->whereYear('created_at', $year);
                                },
                            ]);
                            if ($studentDetails->completedHefzReport->count() > 0) {
                                $firstHefz = $studentDetails->completedHefzReport->sortBy(function ($row) {
                                    return [$row->hefz_from_surat, $row->hefz_from_ayat];
                                })->first();
                                $lastHefz = $studentDetails->completedHefzReport->sortByDesc(function ($row) {
                                    return [$row->hefz_to_surat, $row->hefz_to_ayat];
                                })->first();
                                $min_hefz_from_surat = $firstHefz->hefz_from_surat;
                                $min_hefz_from_ayat = $firstHefz->hefz_from_ayat;
                                $max_hefz_to_surat = $lastHefz->hefz_to_surat;
                                $max_hefz_to_ayat = $lastHefz->hefz_to_ayat;
                                $hefzPlan = $studentDetails->hefz_plans;
                                $hefzPlan = $hefzPlan[0];


                                // now find out the number of pages memorized so far
                                if ($hefzPlan->study_direction == 'backward') {
                                    $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                        $min_hefz_from_surat,
                                        $min_hefz_from_ayat,
                                        $max_hefz_to_surat,
                                        $max_hefz_to_ayat
                                    ]);


                                    $memorizedNumberofPagesSum += $numberofPages[0]->numberofPagesSum;

                                } else {

                                    DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                        $min_hefz_from_surat,
                                        $min_hefz_from_ayat,
                                        $max_hefz_to_surat,
                                        $max_hefz_to_ayat
                                    ]);

                                    $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                    $memorizedNumberofPagesSum += $results[0]->number_of_pages_sum;
                                }
                            }


                            // Now we are going to get the planned Hefz PLanned
                            $firstPlanSurat = $hefzPlan->start_from_surat;
                            $firstPlanAyat = $hefzPlan->start_from_ayat;
                            $lastPlanSurat = $hefzPlan->to_surat;
                            $lastPlanAyat = $hefzPlan->to_ayat;
                            if ($hefzPlan->study_direction == 'backward') {
                                $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $firstPlanSurat,
                                    $firstPlanAyat,
                                    $lastPlanSurat,
                                    $lastPlanAyat
                                ]);


                                $plannedNumberofPagesSum += $numberofPages[0]->numberofPagesSum;

                            }
                            else {

                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $firstPlanSurat,
                                    $firstPlanAyat,
                                    $lastPlanSurat,
                                    $lastPlanAyat
                                ]);

                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $plannedNumberofPagesSum += $results[0]->number_of_pages_sum;


                            }



                            if (empty($memorizedNumberofPagesSum) || is_null($memorizedNumberofPagesSum)) {
//                            $result = "No revision so far";
                                $result = 0;
                            } elseif (empty($plannedNumberofPagesSum) || is_null($plannedNumberofPagesSum)) {
//                            $result = "No plan available";
                                $result = 0;
                            } else {


                                $actual_percentage = round(($memorizedNumberofPagesSum / $plannedNumberofPagesSum * 100), 2);
                                $expected_percentage = 100;
//                                    $result = min($actual_percentage, $expected_percentage);
                                $percentageOfHefzAchievementForEachStudent[$studentDetails->pivot->class_id] = [
                                    'percentage' => min($actual_percentage, $expected_percentage)
                                ];
                            }


                        }
                    }


                    $totalPercentageOfHefzAchievement = array_sum(array_column($percentageOfHefzAchievementForEachStudent, 'percentage'));
//                    if ($classDetails->hefz_plans_count != 0) {
                    if (count($percentageOfHefzAchievementForEachStudent) > 0) {


                        $totalPercentageOfHefzAchievement = round($totalPercentageOfHefzAchievement / count($percentageOfHefzAchievementForEachStudent), 2);
                        $totalPercentageOfHefzAchievement = round(($memorizedNumberofPagesSum / $plannedNumberofPagesSum * 100), 2) ;



                    } else {
                        // Handle the case where the denominator is zero
                                                $totalPercentageOfHefzAchievement = 0;

                    }

//                    } else {
                    // Handle the case where hefz_plans_count is zero
//                        $totalPercentageOfHefzAchievement = 0;
//                    }



                    return  '<div class="progress" style="position: relative;">
  <div  class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$totalPercentageOfHefzAchievement . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $totalPercentageOfHefzAchievement . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $totalPercentageOfHefzAchievement . '%</span>
  </div>
</div>';



                })
                ->rawColumns(['supervisor','halaqahsCount','center','studentsCount','memorizedPages','revisedPages','attendanceDaysPercentage','hefz_achievement_percentage','hefzAchievementComparedtoHefzPlan'])
                ->
                make(true);
        } catch
        (\Exception $e) {
            // Handle the exception

            dd($e->getMessage());
            \Log::error($e->getMessage());
            return response()->json(['error' => 'An error occurred while loading the data for the table.'], 500);
        }
//    }
    }

}

/**
 * Czech translation for bootstrap-datetimepicker
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 * Fixes by <PERSON><PERSON> <<EMAIL>>
 */
;(function($){
	$.fn.datetimepicker.dates['cs'] = {
		days: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>í", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Čtvrtek", "Pátek", "Sobot<PERSON>", "<PERSON>ě<PERSON>"],
		daysShort: ["<PERSON>", "<PERSON>n", "<PERSON><PERSON>", "<PERSON><PERSON>", "Čtv", "<PERSON><PERSON><PERSON>", "Sob", "<PERSON>"],
		daysMin: ["Ne", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "So", "Ne"],
		months: ["<PERSON>en", "<PERSON><PERSON>", "B<PERSON><PERSON>en", "<PERSON><PERSON>", "<PERSON>v<PERSON><PERSON>", "Červen", "Červenec", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Říjen", "Listopad", "Prosinec"],
		monthsShort: ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Říj", "<PERSON><PERSON>", "<PERSON>"],
		today: "<PERSON><PERSON>",
		suffix: [],
		meridiem: [],
		weekStart: 1,
		format: "dd.mm.yyyy"
	};
}(j<PERSON>uery));

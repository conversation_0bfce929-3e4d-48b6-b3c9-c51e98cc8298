<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Repositories;

use <PERSON><PERSON><PERSON>\JobSeeker\Entities\CommandScheduleFilter;
use Modules\JobSeeker\Entities\Province;
use Modules\JobSeeker\Services\FilterTranslationService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Exception;

/**
 * FilterRepository
 * 
 * Handles CRUD operations and caching for CommandScheduleFilter entities.
 * Provides methods to get rule-specific filters with fallback to defaults.
 */
final class FilterRepository
{
    /**
     * Cache key prefix for filter data
     */
    private const CACHE_PREFIX = 'jobseeker_filters:';
    
    /**
     * Cache TTL in minutes
     */
    private const CACHE_TTL = 60;

    /**
     * @var FilterTranslationService
     */
    private FilterTranslationService $translationService;

    /**
     * FilterRepository constructor.
     *
     * @param FilterTranslationService $translationService
     */
    public function __construct(FilterTranslationService $translationService)
    {
        $this->translationService = $translationService;
    }

    /**
     * Get filters for a specific schedule rule
     *
     * @param int $ruleId
     * @return CommandScheduleFilter|null
     */
    public function getByRule(int $ruleId): ?CommandScheduleFilter
    {
        $cacheKey = self::CACHE_PREFIX . "rule:{$ruleId}";

        try {
            return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($ruleId) {
                Log::debug('FilterRepository: Loading filters from database', ['rule_id' => $ruleId]);
                
                // Check if table exists before querying
                if (!\Schema::hasTable('command_schedule_filters')) {
                    Log::warning('FilterRepository: command_schedule_filters table does not exist', ['rule_id' => $ruleId]);
                    return null;
                }
                
                return CommandScheduleFilter::forRule($ruleId)->first();
            });
        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('FilterRepository: Database error loading rule filters', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage(),
                'sql_state' => $e->getCode()
            ]);
            
            // Return null on database errors to allow fallback behavior
            return null;
        } catch (Exception $e) {
            Log::error('FilterRepository: Error loading rule filters', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Get fallback default filters
     *
     * @return CommandScheduleFilter|null
     */
    public function getFallback(): ?CommandScheduleFilter
    {
        $cacheKey = self::CACHE_PREFIX . 'default';

        try {
            return Cache::remember($cacheKey, self::CACHE_TTL, function () {
                Log::debug('FilterRepository: Loading default filters from database');
                
                // Check if table exists before querying
                if (!\Schema::hasTable('command_schedule_filters')) {
                    Log::warning('FilterRepository: command_schedule_filters table does not exist - using null fallback');
                    return null;
                }
                
                return CommandScheduleFilter::default()->first();
            });
        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('FilterRepository: Database error loading default filters', [
                'error' => $e->getMessage(),
                'sql_state' => $e->getCode()
            ]);
            
            // Return null on database errors to allow system to use config defaults
            return null;
        } catch (Exception $e) {
            Log::error('FilterRepository: Error loading default filters', [
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Get filters for rule with fallback to default
     *
     * @param int $ruleId
     * @return CommandScheduleFilter|null
     */
    public function getWithFallback(int $ruleId): ?CommandScheduleFilter
    {
        $filters = $this->getByRule($ruleId);
        
        if (!$filters) {
            Log::info('FilterRepository: No rule-specific filters found, using fallback', [
                'rule_id' => $ruleId
            ]);
            $filters = $this->getFallback();
        }

        return $filters;
    }

    /**
     * Create or update filters for a schedule rule
     *
     * @param int $ruleId
     * @param array $filterData
     * @return CommandScheduleFilter
     * @throws Exception
     */
    public function createOrUpdate(int $ruleId, array $filterData): CommandScheduleFilter
    {
        try {
            // Check if table exists before attempting operations
            if (!\Schema::hasTable('command_schedule_filters')) {
                $errorMsg = 'command_schedule_filters table does not exist';
                Log::error('FilterRepository: Table does not exist', [
                    'rule_id' => $ruleId,
                    'error' => $errorMsg,
                    'filter_data' => $filterData
                ]);
                throw new Exception($errorMsg);
            }

            // Validate filter data
            $errors = CommandScheduleFilter::validateFilterData($filterData);
            if (!empty($errors)) {
                throw new Exception('Validation errors: ' . implode(', ', $errors));
            }

            // Prepare data for database
            $data = array_merge($filterData, ['schedule_rule_id' => $ruleId]);
            
            // Create or update
            $filter = CommandScheduleFilter::updateOrCreate(
                ['schedule_rule_id' => $ruleId],
                $data
            );

            // Clear cache
            $this->clearCache($ruleId);

            Log::info('FilterRepository: Filter created/updated successfully', [
                'rule_id' => $ruleId,
                'filter_id' => $filter->id,
                'has_specific_filters' => $filter->hasSpecificFilters()
            ]);

            return $filter;
            
        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('FilterRepository: Database error creating/updating filters', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage(),
                'sql_state' => $e->getCode(),
                'filter_data' => $filterData
            ]);
            
            throw new Exception('Database error: ' . $e->getMessage(), 0, $e);
        } catch (Exception $e) {
            Log::error('FilterRepository: Error creating/updating filters', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage(),
                'filter_data' => $filterData
            ]);
            
            throw $e;
        }
    }

    /**
     * Delete filters for a schedule rule
     *
     * @param int $ruleId
     * @return bool
     */
    public function delete(int $ruleId): bool
    {
        try {
            $deleted = CommandScheduleFilter::forRule($ruleId)->delete();
            
            if ($deleted) {
                $this->clearCache($ruleId);
                
                Log::info('FilterRepository: Filter deleted successfully', [
                    'rule_id' => $ruleId
                ]);
            }

            return $deleted > 0;
            
        } catch (Exception $e) {
            Log::error('FilterRepository: Error deleting filters', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Get filter options for frontend (categories, provinces, etc.)
     *
     * @return array
     */
    public function getFilterOptions(): array
    {
        $cacheKey = self::CACHE_PREFIX . 'options';

        try {
            return Cache::remember($cacheKey, self::CACHE_TTL * 2, function () {
                Log::debug('FilterRepository: Loading filter options from database');

                // Get canonical job categories (this is the key fix!)
                $canonicalCategories = \Modules\JobSeeker\Entities\JobCategory::where('is_canonical', true)
                    ->where('is_active', true)
                    ->orderBy('name')
                    ->get(['id', 'name'])
                    ->map(function ($category) {
                        return ['id' => $category->id, 'text' => $category->name];
                    })
                    ->values()
                    ->all();

                // Get provinces for location selection
                $provinces = Province::getForSelect2();

                // Define experience levels
                $experienceLevels = [
                    ['id' => 'Entry', 'text' => 'Entry Level'],
                    ['id' => 'Mid', 'text' => 'Mid Level'], 
                    ['id' => 'Senior', 'text' => 'Senior Level'],
                    ['id' => 'Executive', 'text' => 'Executive'],
                    ['id' => 'Internship', 'text' => 'Internship'],
                ];

                // Define work types
                $workTypes = [
                    ['id' => '', 'text' => 'Any'],
                    ['id' => 'Remote', 'text' => 'Remote'],
                    ['id' => 'On-site', 'text' => 'On-site'],
                    ['id' => 'Hybrid', 'text' => 'Hybrid'],
                ];

                return [
                    'categories' => $canonicalCategories,
                    'provinces' => $provinces,
                    'experience_levels' => $experienceLevels,
                    'work_types' => $workTypes
                ];
            });
        } catch (Exception $e) {
            Log::error('FilterRepository: Error loading filter options', [
                'error' => $e->getMessage()
            ]);
            
            // Return minimal fallback options
            return [
                'categories' => [],
                'provinces' => [['id' => 'Kabul', 'text' => 'Kabul']],
                'experience_levels' => [],
                'work_types' => [['id' => '', 'text' => 'Any']]
            ];
        }
    }

    /**
     * Get ACBAR filter options for frontend
     *
     * @return array
     */
    public function getAcbarFilterOptions(): array
    {
        $cacheKey = self::CACHE_PREFIX . 'acbar_options';

        try {
            return Cache::remember($cacheKey, self::CACHE_TTL * 2, function () {
                Log::debug('FilterRepository: Loading ACBAR filter options from database');

                // Use canonical job categories (unified with Jobs.af approach)
                $canonicalCategories = \Modules\JobSeeker\Entities\JobCategory::where('is_canonical', true)
                    ->where('is_active', true)
                    ->orderBy('name')
                    ->get(['id', 'name'])
                    ->map(function ($category) {
                        return ['id' => $category->id, 'text' => $category->name];
                    })
                    ->values()
                    ->all();

                // Get provinces (same for all job sources)
                $provinces = Province::getForSelect2();

                // Get default settings from config
                $acbarConfig = config('jobseeker.acbar_default_filters', []);

                return [
                    'categories' => $canonicalCategories, // Use same key as Jobs.af for consistency
                    'acbar_categories' => $canonicalCategories, // Keep legacy key for backwards compatibility
                    'provinces' => $provinces,
                    'default_settings' => [
                        'location_id' => $acbarConfig['default_location_id'] ?? 14,
                        'max_retries' => $acbarConfig['max_retries'] ?? 5,
                        'timeout' => $acbarConfig['timeout'] ?? 60,
                        'base_delay' => $acbarConfig['base_delay'] ?? 1000000,
                    ]
                ];
            });
        } catch (Exception $e) {
            Log::error('FilterRepository: Error loading ACBAR filter options', [
                'error' => $e->getMessage()
            ]);
            
            // Return minimal fallback options
            return [
                'acbar_categories' => [],
                'provinces' => [['id' => '14', 'text' => 'Kabul']],
                'default_settings' => [
                    'location_id' => 14,
                    'max_retries' => 5,
                    'timeout' => 60,
                    'base_delay' => 1000000,
                ]
            ];
        }
    }

    /**
     * Get filters translated for Jobs.af API consumption
     *
     * @param int $ruleId
     * @return array Translated filters ready for Jobs.af API
     */
    public function getJobsAfTranslatedFilters(int $ruleId): array
    {
        try {
            // Use common dynamic mapping logic
            $dynamicResult = $this->getDynamicProviderFilters($ruleId, 'jobs.af', 'jobs_af');
            if ($dynamicResult !== null) {
                // Attach allow_non_english from filter
                $filter = \Modules\JobSeeker\Entities\CommandScheduleFilter::where('schedule_rule_id', $ruleId)->first();
                if ($filter) {
                    $dynamicResult['allow_non_english'] = (bool) ($filter->allow_non_english ?? false);
                }
                // Check for advanced settings in database
                $advancedSettings = $this->getJobsAfAdvancedSettings($ruleId);
                if ($advancedSettings) {
                    // Override default settings with database values
                    $dynamicResult = array_merge($dynamicResult, $advancedSettings);
                }
                return $dynamicResult;
            }

            // BACKWARD COMPATIBILITY: Fall back to legacy approach
            Log::debug('FilterRepository: Using legacy translation approach for Jobs.af', [
                'rule_id' => $ruleId
            ]);

            $filters = $this->getWithFallback($ruleId);
            
            if (!$filters) {
                $defaultConfig = $this->getDefaultConfig('jobs_af');
                // Check for advanced settings even with default config
                $advancedSettings = $this->getJobsAfAdvancedSettings($ruleId);
                if ($advancedSettings) {
                    $defaultConfig = array_merge($defaultConfig, $advancedSettings);
                }
                return $defaultConfig;
            }

            // Use legacy translation service
            $translatedCategories = [];
            if (!empty($filters->categories)) {
                $translatedCategories = $this->translationService->translateToJobsAfCategories($filters->categories);
            }

            // Get advanced settings (page, max_pages, delays, etc.) from database
            $advancedSettings = $this->getJobsAfAdvancedSettings($ruleId);
            $pageNumber = $advancedSettings['page'] ?? 1;

            $result = [
                'page' => $pageNumber,
                'searchFilters' => [
                    'searchTerm' => '',
                    'categories' => $translatedCategories,
                    'workType' => '',
                    'companies' => [],
                    'experienceLevels' => [],
                    'locations' => $filters->locations ?? ['Kabul']
                ]
            ];

            // Add advanced settings to result
            if ($advancedSettings) {
                $result = array_merge($result, array_diff_key($advancedSettings, ['page' => null]));
            }

            Log::debug('FilterRepository: Legacy Jobs.af filters prepared with advanced settings', [
                'rule_id' => $ruleId,
                'canonical_categories' => $filters->categories,
                'translated_categories' => $translatedCategories,
                'locations' => $result['searchFilters']['locations'],
                'page_number' => $pageNumber,
                'advanced_settings' => $advancedSettings
            ]);

            // Attach allow_non_english from filter
            $filter = \Modules\JobSeeker\Entities\CommandScheduleFilter::where('schedule_rule_id', $ruleId)->first();
            if ($filter) {
                $result['allow_non_english'] = (bool) ($filter->allow_non_english ?? false);
            }

            return $result;

        } catch (Exception $e) {
            Log::error('FilterRepository: Error getting translated Jobs.af filters', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage()
            ]);

            $defaultConfig = $this->getDefaultConfig('jobs_af');
            // Try to get advanced settings even on error
            try {
                $advancedSettings = $this->getJobsAfAdvancedSettings($ruleId);
                if ($advancedSettings) {
                    $defaultConfig = array_merge($defaultConfig, $advancedSettings);
                }
            } catch (Exception $advancedError) {
                Log::warning('FilterRepository: Could not load advanced settings for fallback', [
                    'rule_id' => $ruleId,
                    'error' => $advancedError->getMessage()
                ]);
            }

            return $defaultConfig;
        }
    }

    /**
     * Get filters translated for ACBAR API consumption
     *
     * @param int $ruleId
     * @return array Translated filters ready for ACBAR API
     */
    public function getAcbarTranslatedFilters(int $ruleId): array
    {
        try {
            // Use common dynamic mapping logic
            $dynamicResult = $this->getDynamicProviderFilters($ruleId, 'acbar', 'acbar');
            if ($dynamicResult !== null) {
                // Attach allow_non_english from filter
                $filter = \Modules\JobSeeker\Entities\CommandScheduleFilter::where('schedule_rule_id', $ruleId)->first();
                if ($filter) {
                    $dynamicResult['allow_non_english'] = (bool) ($filter->allow_non_english ?? false);
                }
                return $dynamicResult;
            }

            // BACKWARD COMPATIBILITY: Fall back to legacy approach
            Log::debug('FilterRepository: Using legacy translation approach for ACBAR', [
                'rule_id' => $ruleId
            ]);

            $filters = $this->getWithFallback($ruleId);
            
            if (!$filters) {
                return $this->getDefaultConfig('acbar');
            }

            // Use legacy translation service
            $translatedCategoryIds = [];
            if (!empty($filters->categories)) {
                $translatedCategoryIds = $this->translationService->translateToAcbarCategories($filters->categories);
            }

            $result = [
                'category_ids' => $translatedCategoryIds,
                'location_ids' => $filters->locations ?? [14],
                'max_retries' => $filters->max_retries ?? 5,
                'timeout' => $filters->timeout ?? 60,
                'base_delay' => $filters->base_delay ?? 1000000
            ];

            Log::debug('FilterRepository: Legacy ACBAR filters prepared', [
                'rule_id' => $ruleId,
                'canonical_categories' => $filters->categories,
                'translated_category_ids' => $translatedCategoryIds,
                'location_ids' => $result['location_ids']
            ]);

            // Attach allow_non_english from filter
            $filter = \Modules\JobSeeker\Entities\CommandScheduleFilter::where('schedule_rule_id', $ruleId)->first();
            if ($filter) {
                $result['allow_non_english'] = (bool) ($filter->allow_non_english ?? false);
            }
            return $result;

        } catch (Exception $e) {
            Log::error('FilterRepository: Error getting translated ACBAR filters', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage()
            ]);

            return $this->getDefaultConfig('acbar');
        }
    }

    /**
     * Common dynamic mapping logic for providers
     *
     * @param int $ruleId
     * @param string $providerName
     * @param string $configKey
     * @return array|null Returns array if dynamic mapping is used, null if should fall back to legacy
     */
    private function getDynamicProviderFilters(int $ruleId, string $providerName, string $configKey): ?array
    {
        // Get the rule
        $rule = \Modules\JobSeeker\Entities\CommandScheduleRule::find($ruleId);

        if (!$rule) {
            Log::warning('FilterRepository: CommandScheduleRule not found', ['rule_id' => $ruleId]);
            return $this->getDefaultConfig($configKey);
        }

        // FIXED: Get the filter configuration for this rule
        $filter = \Modules\JobSeeker\Entities\CommandScheduleFilter::where('schedule_rule_id', $ruleId)->first();

        if (!$filter) {
            Log::debug('FilterRepository: No CommandScheduleFilter found, falling back to legacy approach', [
                'rule_id' => $ruleId
            ]);
            return null; // Fall back to legacy approach
        }

        // Check if we have categories to work with
        if (empty($filter->categories)) {
            Log::debug('FilterRepository: No categories in filter, using empty category list', [
                'rule_id' => $ruleId
            ]);
            $providerIdentifiers = [];
        } else {
            Log::debug("FilterRepository: Using dynamic provider category mapping for {$providerName}", [
                'rule_id' => $ruleId,
                'filter_categories' => $filter->categories
            ]);

            // FIXED: Get provider identifiers from ProviderJobCategory table using filter categories
            $providerIdentifiers = \Modules\JobSeeker\Entities\ProviderJobCategory::getProviderIdentifiers(
                $filter->categories,
                $providerName
            );
        }

        // FIXED: Get location identifiers from ProviderJobLocation table using filter locations
        $locationIdentifiers = [];
        if (!empty($filter->locations)) {
            $locationIdentifiers = \Modules\JobSeeker\Entities\ProviderJobLocation::getProviderIdentifiers(
                $filter->locations,
                $providerName
            );
        }

        // Handle provider-specific result formatting
        if ($providerName === 'jobs.af') {
            $result = [
                'page' => 1,
                'searchFilters' => [
                    'searchTerm' => '',
                    'categories' => $providerIdentifiers, // Direct provider identifiers as strings
                    'workType' => '',
                    'companies' => [],
                    'experienceLevels' => [],
                    'locations' => $locationIdentifiers // FIXED: Use dynamic location identifiers
                ]
            ];
        } else { // ACBAR
            // For ACBAR, provider_identifier contains numeric category IDs
            $categoryIds = array_map('intval', $providerIdentifiers);
            // For ACBAR, location identifiers are also numeric
            $locationIds = array_map('intval', $locationIdentifiers);

            $result = [
                'category_ids' => $categoryIds, // Direct provider identifiers as integers
                'location_ids' => $locationIds, // FIXED: Use dynamic location identifiers
                'max_retries' => 5,
                'timeout' => 60,
                'base_delay' => 1000000
            ];
        }

        Log::debug("FilterRepository: Dynamic {$providerName} filters prepared", [
            'rule_id' => $ruleId,
            'filter_categories' => $filter->categories ?? [],
            'filter_locations' => $filter->locations ?? [],
            'provider_category_identifiers' => $providerIdentifiers,
            'provider_location_identifiers' => $locationIdentifiers,
            'result_structure' => array_keys($result)
        ]);

        return $result;
    }

    /**
     * Get default configuration for a provider
     *
     * @param string $configKey
     * @return array
     */
    private function getDefaultConfig(string $configKey): array
    {
        $defaultFilters = config("jobseeker.{$configKey}_default_filters", []);
        
        if ($configKey === 'jobs_af') {
            return [
                'page' => 1,
                'searchFilters' => [
                    'searchTerm' => '',
                    'categories' => $defaultFilters['searchFilters']['categories'] ?? [],
                    'workType' => '',
                    'companies' => [],
                    'experienceLevels' => [],
                    'locations' => ['Kabul']
                ]
            ];
        } else { // ACBAR
            return [
                'category_ids' => [], // No category filter = all categories
                'location_ids' => [$defaultFilters['default_location_id'] ?? 14], // Default to Kabul
                'max_retries' => $defaultFilters['max_retries'] ?? 5,
                'timeout' => $defaultFilters['timeout'] ?? 60,
                'base_delay' => $defaultFilters['base_delay'] ?? 1000000
            ];
        }
    }

    /**
     * Get Jobs.af advanced settings from database for a specific rule
     *
     * @param int $ruleId
     * @return array|null
     */
    private function getJobsAfAdvancedSettings(int $ruleId): ?array
    {
        try {
            // Check if table exists before querying
            if (!\Schema::hasTable('command_schedule_filters')) {
                Log::debug('FilterRepository: command_schedule_filters table does not exist for advanced settings', ['rule_id' => $ruleId]);
                return null;
            }

            $advancedFilters = CommandScheduleFilter::where('command_schedule_rule_id', $ruleId)
                ->where('filter_type', 'jobs_af_advanced')
                ->get();

            if ($advancedFilters->isEmpty()) {
                Log::debug('FilterRepository: No advanced settings found for Jobs.af rule', ['rule_id' => $ruleId]);
                return null;
            }

            $settings = [];
            foreach ($advancedFilters as $filter) {
                $key = $filter->filter_key;
                $value = $filter->filter_value;

                // Convert string values to appropriate types
                switch ($key) {
                    case 'page':
                    case 'max_pages':
                        $settings[$key] = (int) $value;
                        break;
                    case 'request_delay':
                        $settings[$key] = (float) $value;
                        break;
                    case 'user_agent_rotation':
                    case 'random_delays':
                        $settings[$key] = (bool) $value;
                        break;
                    default:
                        $settings[$key] = $value;
                        break;
                }
            }

            Log::debug('FilterRepository: Advanced settings loaded for Jobs.af rule', [
                'rule_id' => $ruleId,
                'settings' => $settings
            ]);

            return $settings;

        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('FilterRepository: Database error loading advanced settings', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage(),
                'sql_state' => $e->getCode()
            ]);
            return null;
        } catch (Exception $e) {
            Log::error('FilterRepository: Error loading advanced settings', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get default Jobs.af configuration
     * @deprecated Use getDefaultConfig('jobs_af') instead
     *
     * @return array
     */
    private function getJobsAfDefaultConfig(): array
    {
        return $this->getDefaultConfig('jobs_af');
    }

    /**
     * Get default ACBAR configuration
     * @deprecated Use getDefaultConfig('acbar') instead
     *
     * @return array
     */
    private function getAcbarDefaultConfig(): array
    {
        return $this->getDefaultConfig('acbar');
    }

    /**
     * Get locations from CommandScheduleRule for Jobs.af (as location names)
     * CORRECTED APPROACH: CommandScheduleRule.location_ids references provider_job_locations.id directly
     *
     * @param \Modules\JobSeeker\Entities\CommandScheduleRule $rule
     * @return array
     */
    private function getLocationsFromRule($rule): array
    {
        // CORRECTED: location_ids now directly references provider_job_locations.id
        if (!empty($rule->location_ids)) {
            try {
                $providerIdentifiers = \Modules\JobSeeker\Entities\ProviderJobLocation::getProviderIdentifiers(
                    $rule->location_ids, // These are provider_job_location IDs now
                    'jobs.af'
                );

                if (!empty($providerIdentifiers)) {
                    Log::debug('FilterRepository: Using provider job location mapping for Jobs.af', [
                        'rule_id' => $rule->id,
                        'provider_job_location_ids' => $rule->location_ids,
                        'provider_identifiers' => $providerIdentifiers
                    ]);
                    return $providerIdentifiers;
                }
            } catch (Exception $e) {
                Log::warning('FilterRepository: Error getting provider job locations for Jobs.af', [
                    'rule_id' => $rule->id,
                    'provider_job_location_ids' => $rule->location_ids,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // DEFAULT: Return empty array if no locations configured (no hardcoded defaults)
        Log::debug('FilterRepository: No locations configured for Jobs.af', [
            'rule_id' => $rule->id
        ]);
        return [];
    }

    /**
     * Get location IDs from CommandScheduleRule for ACBAR
     * CORRECTED APPROACH: CommandScheduleRule.location_ids references provider_job_locations.id directly
     *
     * @param \Modules\JobSeeker\Entities\CommandScheduleRule $rule
     * @return array
     */
    private function getLocationIdsFromRule($rule): array
    {
        // CORRECTED: location_ids now directly references provider_job_locations.id
        if (!empty($rule->location_ids)) {
            try {
                $providerIdentifiers = \Modules\JobSeeker\Entities\ProviderJobLocation::getProviderIdentifiers(
                    $rule->location_ids, // These are provider_job_location IDs now
                    'acbar'
                );

                if (!empty($providerIdentifiers)) {
                    // For ACBAR, provider_identifier should contain numeric IDs
                    $locationIds = array_map('intval', $providerIdentifiers);
                    Log::debug('FilterRepository: Using provider job location mapping for ACBAR', [
                        'rule_id' => $rule->id,
                        'provider_job_location_ids' => $rule->location_ids,
                        'provider_identifiers' => $providerIdentifiers,
                        'acbar_location_ids' => $locationIds
                    ]);
                    return $locationIds;
                }
            } catch (Exception $e) {
                Log::warning('FilterRepository: Error getting provider job locations for ACBAR', [
                    'rule_id' => $rule->id,
                    'provider_job_location_ids' => $rule->location_ids,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // DEFAULT: Return empty array if no locations configured (no hardcoded defaults)
        Log::debug('FilterRepository: No locations configured for ACBAR', [
            'rule_id' => $rule->id
        ]);
        return [];
    }

    /**
     * Clear cache for specific rule or all filter caches
     *
     * @param int|null $ruleId
     * @return void
     */
    public function clearCache(?int $ruleId = null): void
    {
        try {
            if ($ruleId) {
                Cache::forget(self::CACHE_PREFIX . "rule:{$ruleId}");
                Log::debug('FilterRepository: Cleared cache for rule', ['rule_id' => $ruleId]);
            } else {
                // Clear all filter-related caches
                Cache::forget(self::CACHE_PREFIX . 'default');
                Cache::forget(self::CACHE_PREFIX . 'options');
                
                // Clear individual rule caches (this is expensive but thorough)
                // In production, consider using cache tags instead
                Log::debug('FilterRepository: Cleared all filter caches');
            }
        } catch (Exception $e) {
            Log::warning('FilterRepository: Error clearing cache', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get usage statistics for filters
     *
     * @return array
     */
    public function getUsageStats(): array
    {
        try {
            $stats = [
                'total_filters' => CommandScheduleFilter::count(),
                'rules_with_custom_filters' => CommandScheduleFilter::where('is_default', false)->count(),
                'default_filters' => CommandScheduleFilter::where('is_default', true)->count(),
                'filters_with_categories' => CommandScheduleFilter::whereNotNull('categories')
                    ->where('categories', '!=', '[]')->count(),
                'filters_with_locations' => CommandScheduleFilter::whereNotNull('locations')
                    ->where('locations', '!=', '[]')->count(),
            ];

            Log::debug('FilterRepository: Generated usage statistics', $stats);
            
            return $stats;
            
        } catch (Exception $e) {
            Log::error('FilterRepository: Error generating usage statistics', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'total_filters' => 0,
                'rules_with_custom_filters' => 0,
                'default_filters' => 0,
                'filters_with_categories' => 0,
                'filters_with_locations' => 0,
            ];
        }
    }
} 
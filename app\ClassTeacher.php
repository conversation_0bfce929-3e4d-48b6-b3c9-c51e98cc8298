<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class ClassTeacher extends Model
{

    use SoftDeletes;

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $casts = ['deleted_at'];
    protected $fillable = ['class_id','employee_id','start_date','end_date','deleted_at'];
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'class_teachers';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

//    public function data()
//    {
//        return $this->belongsTo('App\Employee' , 'employee_id');
//    }

    public function subjects()
    {
        return $this->hasMany('App\ClassTeacherSubject')->where('end_date' , null)->with('timetable');
//        return $this->hasOne('App\ClassTeacherSubject')->where('end_date' , null)->with('timetable');
    }
    public function subject()
    {
//        return $this->hasMany('App\ClassTeacherSubject')->where('end_date' , null)->with('timetable');
        return $this->hasOne('App\ClassTeacherSubject')->where('end_date' , null)->with('timetable');
    }

    public function class_info()
    {
        return $this->belongsTo('App\Classes' , 'class_id');
    }


    public function employee(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {

        return $this->belongsTo(Employee::class);
    }

//    public function timetable()
//    {
//        return $this->hasOneThrough(ClassSubjectTimetable::class , ClassTeacherSubject::class,'id','class_teacher_subject_id')->whereNull('end_at');
//    }
    
}

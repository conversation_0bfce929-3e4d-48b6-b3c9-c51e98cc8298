<?php

namespace App\Http\Controllers\Auth;


use App\Employee;
use App\UserLog;
use Hesto\MultiAuth\Traits\LogsoutGuard;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;
use Jensse<PERSON>\Agent\Agent;
use Illuminate\Http\Request;
use Illuminate\Cache\RateLimiter;
use Illuminate\Auth\Events\Lockout;
use App\Http\Controllers\Controller;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;

use App\Organization;
use Laravel\Socialite\Facades\Socialite;

class LoginController extends Controller
{
    use AuthenticatesUsers;

    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
     */
    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/after-login';
    /**
     * Get the post register / login redirect path.
     *
     * @return string
     */
//


    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {



        $this->middleware('guest')->except('logout');
    }



    /**
     * Redirect the user to the GitHub authentication page.
     *
     * @return \Illuminate\Http\Response
     */
    public function redirectToProvider()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Obtain the user information from Google.
     *
     * @return \Illuminate\Http\Response
     */
    public function handleProviderCallback()
    {
        try {
            $user = Socialite::driver('google')->user();
        } catch (\Exception $e) {
            return redirect('workplace/login');
        }

        // check if they're an existing user
        $existingEmployee = Employee::where('email', $user->email)->first();
        if($existingEmployee){
            // log them in
            auth()->login($existingEmployee, true);
        } else {
            return redirect('workplace/login');
        }
        return redirect()->to('workplace/dashboard');
    }



    /**
     * Get the post register / login redirect path.
     *
     * @return string
     */
    public function redirectPath()
    {
       

        if (method_exists($this, 'redirectTo')) {
            return $this->redirectTo();
        }
       $this->redirectTo = \Auth::guard("web")->user()->hasRole("parent") /** parent */ ? '/parent-dashboard' : '/student-dashboard';
  


        return property_exists($this, 'redirectTo') ? $this->redirectTo : '/';
    }

    public function login(Request $request)
    {

//        Session::flush();
        // return $request;
        $this->validateLogin($request);


        // If the class is using the ThrottlesLogins trait, we can automatically throttle
        // the login attempts for this application. We'll key this by the username and
        // the IP address of the client making these requests into this application.
        if ($this->hasTooManyLoginAttempts($request)) {
            $this->fireLockoutEvent($request);

            return $this->sendLockoutResponse($request);
        }

        // attempt to do the login
        if ($this->attemptLogin($request)) {




            // validation successful!
            // redirect them to the secure section or whatever
            // check if verified
            $user = Auth::getLastAttempted();

            // checking the user verification
            if (is_null($user->email_verified_at)) {





                //the verification link should be resent
//                $request->user()->sendEmailVerificationNotification();


                Auth::logout();

//                return view('emailverification::resend', ['verified' => $user->verified, 'email' => $user->email]);
//                return view('auth.verify', ['verified' => $user->verified, 'email' => $user->email]);

                return redirect()->back()
                    ->with('verificationRequired','Your account is not verified. Check your email for further process ');
//                    ->withInput(Input::except('password'));



            }
            else{
                DB::beginTransaction();
                try {
                    $agent = new Agent();
                    $user_log = new UserLog();
                    $user_log->user_id = Auth::guard('web')->user()->id;
//                $user_log->role_id = Auth::user()->role_id;
                    $user_log->organization_id = Auth::user()->organization_id;
                    $user_log->ip_address = $request->ip();
                    $user_log->user_agent = $agent->browser() . ', ' . $agent->platform();
                    $user_log->save();
                    DB::commit();
                    return $this->sendLoginResponse($request);
                } catch (\Exception $e) {
                    DB::rollback();
                    Toastr::error('User Log failed', 'Success');
                    return redirect()->intended();
                }
            }




        } else {


            // If the login attempt was unsuccessful we will increment the number of attempts
            // to login and redirect the user back to the login form. Of course, when this
            // user surpasses their maximum number of attempts they will get locked out.
            $this->incrementLoginAttempts($request);


            return $this->sendFailedLoginResponse($request);

            $this->guard()->logout();
            Toastr::error('You are not allowed, Please contact with administrator.', 'Failed');
            return redirect('login');
        }


    }

    protected function sendFailedLoginResponse(Request $request)
    {
        throw ValidationException::withMessages([
            $this->username() => [trans('auth.failed')],
        ]);
    }


    /**
     * Validate the user login request.
     *
     * @param \Illuminate\Http\Request $request
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateLogin(Request $request)
    {

      $customMessage = ['g-recaptcha-response' => [
            'required' => 'Please verify that you are not a robot.',
            'captcha' => 'Captcha error! try again later or contact site admin.',
        ]];


        $this->validate($request, [
            $this->username() => 'required|string|exists:users,username',
            'password' => 'required|string',
//            'g-recaptcha-response' => 'required|captcha',
        ],$customMessage);

    }

    /**
     * Attempt to log the user into the application.
     *
     * @param \Illuminate\Http\Request $request
     * @return bool
     */
    protected function attemptLogin(Request $request)
    {

        return $this->guard()->attempt(
            $this->credentials($request),
            $request->has('remember')
        );
    }

    /**
     * Get the needed authorization credentials from the request.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    protected function credentials(Request $request)
    {

            return $request->only('username', 'password', 'organization_id');

    }





//    /**
//     * Get the failed login response instance.
//     *
//     * @param \Illuminate\Http\Request $request
//     * @return \Symfony\Component\HttpFoundation\Response
//     *
//     * @throws \Illuminate\Validation\ValidationException
//     */
//    protected function sendFailedLoginResponse(Request $request)
//    {
//        throw ValidationException::withMessages([
//            $this->username() => [trans('auth.failed')],
//        ]);
//    }

    /**
     * Get the login username to be used by the controller.
     *
     * @return string
     */
    public function username()
    {
//        return 'email';
        return 'username';
    }

    /**
     * Log the user out of the application.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    // public function logout(Request $request)
    // {
    //     $this->guard()->logout();
    //     $request->session()->invalidate();

    //     session_destroy();
    //     session(['role_id' => '']);

    //     return $this->loggedOut($request) ?: redirect('/');
    // }



    /**
     * Get the guard to be used during authentication.
     *
     * @return \Illuminate\Contracts\Auth\StatefulGuard
     */
    protected function guard()
    {
        return Auth::guard("web");
    }




    public function loginFormTwo()
    {

//            return view('auth.loginCodeCanyon');
            return view('auth.login2');


    }



    public function showLoginForm()
    {





        return view('auth.login2');
    }

    //user logout method
    public function logout(Request $request)
    {


        // return $request;
        // if (Auth::user()->role_id == 1) {
        //     $data = AcademicYear::where('year', date('Y'))->first();
        //     $selected = Setting::find(1);
        //     $selected->session_id = $data->id;
        //     $selected->session_year = $data->year;
        //     $selected->save();
        // }
        Auth::guard('web')->logout();
        session(['role_id' => '']);
//        Session::flush();
        return redirect('/login');
    }

}

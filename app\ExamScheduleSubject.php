<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\ExamScheduleSubject
 *
 * @property int $id
 * @property string|null $date
 * @property string|null $start_time
 * @property string|null $end_time
 * @property string|null $room
 * @property int|null $full_mark
 * @property int|null $pass_mark
 * @property int $active_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $exam_schedule_id
 * @property int|null $subject_id
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $organization_id
 * @property int|null $academic_id
 * @property-read \App\Subject|null $subject
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject query()
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereAcademicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereActiveStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereExamScheduleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereFullMark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject wherePassMark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereRoom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereSchoolId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereSubjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamScheduleSubject whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class ExamScheduleSubject extends Model
{
    public function subject(){
    	return $this->belongsTo('App\Subject', 'subject_id', 'id');
    }
}

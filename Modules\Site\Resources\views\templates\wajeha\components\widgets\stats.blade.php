<section id="stats"
    @if($stats_is_parallax ?? null)
    class="parallax parallax-2 padding-xxs" style="background-image: url('{{url($stats_background_image ?? 'image_placeholder.jpg')}}');"
    >
	<div class="overlay dark-4">
		<!-- dark overlay [0 to 9 opacity] -->
	</div>
    @else
    >
    @endif
	<div class="container">
		<div class="row countTo-md text-center">
            @for($i = 1 ; $i <= $stats_number_of_blocks ; $i++)
			<div class="col-xs-6 col-sm-3">
				<i class="fa {{ ${'stats_icon_'.$i} }}" style="color:{{ ${'stats_icon_color_'.$i} }}"></i>
				<span class="countTo" data-speed="3000">{{ ${'stats_stat_'.$i} }}</span>
				<h5>{{ ${'stats_title_'.$i} }}</h5>
			</div>
            @endfor
		</div>

	</div>
</section>
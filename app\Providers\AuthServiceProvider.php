<?php

namespace App\Providers;

use App\Employee;
use App\Menu;
//use App\Policies\MenuPolicy;
use App\User;
use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Modules\HumanResource\Policies\EmployeePolicy;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        'App\Model' => 'App\Policies\ModelPolicy',
//        Menu::class => MenuPolicy::class,
//        Employee::class => EmployeePolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();



        // Implicitly grant "Managing Director"R role all permissions
        // This works in the app by using gate-related functions like auth()->user->can() and @can()
        Gate::before(function ($user, $ability) {
            // Grant all permissions to managing directors
            if ($user->hasRole('managing-director_'.config('organization_id').'_')) {
                return true;
            }
            
            // Grant all permissions to system viewers for comprehensive demo access
            if ($user->hasRole('system_viewer_'.config('organization_id').'_')) {
                return true;
            }

            return null;
        });



        //
    }
}

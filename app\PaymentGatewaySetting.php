<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PaymentGatewaySetting extends Model
{
    public static function getStripeDetails(){
    	
		try {
			$stripeDetails = PaymentGatewaySetting::select('*')->where('gateway_name', '=', 'Stripe')->first();
				if(!empty($stripeDetails)){
					return $stripeDetails->stripe_publisher_key;
				}
		} catch (\Exception $e) {
			$data=[];
			return $data;
		}
    }
}

<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;

/**
 * App\AcademicYear
 *
 * @property int $id
 * @property string $year
 * @property string $title
 * @property string $starting_date
 * @property string $ending_date
 * @property bool $active_status
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property int $created_by
 * @property int $updated_by
 * @property int $organization_id
 * @method static \Illuminate\Database\Query\Builder|\App\AcademicYear whereActiveStatus($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AcademicYear whereCreatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AcademicYear whereCreatedBy($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AcademicYear whereEndingDate($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AcademicYear whereId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AcademicYear whereOrganizationId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AcademicYear whereStartingDate($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AcademicYear whereTitle($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AcademicYear whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AcademicYear whereUpdatedBy($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AcademicYear whereYear($value)
 * @mixin \Eloquent
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicYear newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicYear newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AcademicYear query()
 */
class AcademicYear extends Model
{
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
    }
}

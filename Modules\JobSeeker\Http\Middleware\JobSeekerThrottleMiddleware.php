<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Middleware;

use Illuminate\Routing\Middleware\ThrottleRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Closure;

/**
 * JobSeeker General API Rate Limiting Middleware
 * 
 * Implements rate limiting for all JobSeeker public-facing endpoints with:
 * - Different limits for authenticated vs unauthenticated users
 * - Exponential backoff for repeated violations
 * - Comprehensive logging
 */
final class JobSeekerThrottleMiddleware extends ThrottleRequests
{
    /**
     * Rate limits configuration
     */
    private const RATE_LIMITS = [
        'authenticated' => [
            'max_attempts' => 120,    // 120 requests per hour for authenticated users
            'decay_minutes' => 60
        ],
        'unauthenticated' => [
            'max_attempts' => 60,     // 60 requests per hour for unauthenticated users
            'decay_minutes' => 60
        ],
        'api' => [
            'authenticated' => [
                'max_attempts' => 200,    // 200 API requests per hour for authenticated users
                'decay_minutes' => 60
            ],
            'unauthenticated' => [
                'max_attempts' => 100,    // 100 API requests per hour for unauthenticated users
                'decay_minutes' => 60
            ]
        ]
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  mixed  ...$parameters
     * @return mixed
     */
    public function handle($request, Closure $next, ...$parameters)
    {
        // Determine if this is an API request
        $isApiRequest = $this->isApiRequest($request);
        
        // Determine authentication status
        $isAuthenticated = Auth::guard('job_seeker')->check();
        
        // Get rate limit configuration
        $rateLimits = $this->getRateLimits($isApiRequest, $isAuthenticated);
        
        $maxAttempts = $rateLimits['max_attempts'];
        $decayMinutes = $rateLimits['decay_minutes'];
        
        // Create a unique key for this client
        $key = $this->buildRateLimitKey($request, $isApiRequest, $isAuthenticated);
        
        // Check for exponential backoff
        $backoffKey = $key . ':backoff';
        $backoffUntil = Cache::get($backoffKey);
        
        if ($backoffUntil && $backoffUntil > now()) {
            Log::warning('JobSeeker API request blocked due to exponential backoff', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'is_api' => $isApiRequest,
                'is_authenticated' => $isAuthenticated,
                'user_id' => $isAuthenticated ? Auth::guard('job_seeker')->id() : null,
                'route' => $request->route()?->getName(),
                'uri' => $request->getRequestUri(),
                'backoff_until' => $backoffUntil
            ]);
            
            return $this->buildException($request, $key, $maxAttempts, $backoffUntil->diffInSeconds(now()));
        }
        
        // Apply the rate limiting
        $response = parent::handle($request, $next, $maxAttempts, $decayMinutes, $key);
        
        // If the response indicates rate limiting was hit, implement exponential backoff
        if ($response->getStatusCode() === 429) {
            $this->implementExponentialBackoff($key, $request, $isApiRequest, $isAuthenticated);
        }
        
        // Log successful requests for monitoring
        $this->logSuccessfulRequest($request, $isApiRequest, $isAuthenticated);
        
        return $response;
    }

    /**
     * Determine if this is an API request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function isApiRequest(Request $request): bool
    {
        return str_starts_with($request->getPathInfo(), '/api/') ||
               $request->expectsJson() ||
               in_array('application/json', $request->getAcceptableContentTypes());
    }

    /**
     * Get rate limits based on request type and authentication status
     *
     * @param  bool  $isApiRequest
     * @param  bool  $isAuthenticated
     * @return array
     */
    protected function getRateLimits(bool $isApiRequest, bool $isAuthenticated): array
    {
        if ($isApiRequest) {
            return self::RATE_LIMITS['api'][$isAuthenticated ? 'authenticated' : 'unauthenticated'];
        }
        
        return self::RATE_LIMITS[$isAuthenticated ? 'authenticated' : 'unauthenticated'];
    }

    /**
     * Build rate limit key based on request characteristics
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  bool  $isApiRequest
     * @param  bool  $isAuthenticated
     * @return string
     */
    protected function buildRateLimitKey(Request $request, bool $isApiRequest, bool $isAuthenticated): string
    {
        $identifier = $request->ip();
        
        // For authenticated users, include user ID in the key
        if ($isAuthenticated) {
            $userId = Auth::guard('job_seeker')->id();
            $identifier .= '|user:' . $userId;
        }
        
        // Add request type to the key
        $type = $isApiRequest ? 'api' : 'web';
        
        return 'jobseeker_throttle:' . $type . ':' . $identifier;
    }

    /**
     * Resolve request signature for rate limiting (parent method override)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string
     */
    protected function resolveRequestSignature($request)
    {
        $isApiRequest = $this->isApiRequest($request);
        $isAuthenticated = Auth::guard('job_seeker')->check();
        
        return $this->buildRateLimitKey($request, $isApiRequest, $isAuthenticated);
    }

    /**
     * Implement exponential backoff for repeated violations
     *
     * @param  string  $key
     * @param  \Illuminate\Http\Request  $request
     * @param  bool  $isApiRequest
     * @param  bool  $isAuthenticated
     * @return void
     */
    protected function implementExponentialBackoff(string $key, Request $request, bool $isApiRequest, bool $isAuthenticated): void
    {
        $backoffKey = $key . ':backoff';
        $violationsKey = $key . ':violations';
        
        // Get current violation count
        $violations = Cache::get($violationsKey, 0) + 1;
        
        // Calculate backoff time: 2^violations minutes, max 4 hours for general throttling
        $backoffMinutes = min(pow(2, $violations), 240);
        $backoffUntil = now()->addMinutes($backoffMinutes);
        
        // Store the backoff period and violation count
        Cache::put($backoffKey, $backoffUntil, $backoffUntil);
        Cache::put($violationsKey, $violations, now()->addHours(12));
        
        Log::warning('JobSeeker API exponential backoff applied', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'is_api' => $isApiRequest,
            'is_authenticated' => $isAuthenticated,
            'user_id' => $isAuthenticated ? Auth::guard('job_seeker')->id() : null,
            'route' => $request->route()?->getName(),
            'uri' => $request->getRequestUri(),
            'violations' => $violations,
            'backoff_minutes' => $backoffMinutes,
            'backoff_until' => $backoffUntil
        ]);
    }

    /**
     * Log successful request for monitoring
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  bool  $isApiRequest
     * @param  bool  $isAuthenticated
     * @return void
     */
    protected function logSuccessfulRequest(Request $request, bool $isApiRequest, bool $isAuthenticated): void
    {
        // Only log every 10th request to avoid log flooding
        if (mt_rand(1, 10) === 1) {
            Log::info('JobSeeker API request processed', [
                'ip' => $request->ip(),
                'is_api' => $isApiRequest,
                'is_authenticated' => $isAuthenticated,
                'user_id' => $isAuthenticated ? Auth::guard('job_seeker')->id() : null,
                'method' => $request->method(),
                'route' => $request->route()?->getName(),
                'uri' => $request->getRequestUri(),
                'user_agent' => $request->userAgent()
            ]);
        }
    }

    /**
     * Get the rate limiting key for named routes
     *
     * @param  string  $name
     * @return string
     */
    public static function named(string $name): string
    {
        return 'jobseeker_throttle:named:' . $name;
    }

    /**
     * Get per-user rate limiting key
     *
     * @param  string  $name
     * @return string
     */
    public static function perUser(string $name): string
    {
        return 'jobseeker_throttle:user:' . $name;
    }
} 
<?php

namespace Modules\Education\Http\Controllers;

use App\StudentHefzPlan;
use App\StudentHefzReport;
use App\StudentRevisionPlan;
use App\StudentRevisionReport;
use Illuminate\Database\Eloquent\Model;
use Matrix\Builder;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Employee;
use Carbon\Carbon;
use App\ClassStudent;
use App\ClassTeacher;
use App\Http\Requests;
use App\ClassTeacherSubject;
use Illuminate\Http\Request;
use App\ClassSubjectTimetable;
use App\Http\Controllers\Controller;
use App\Cen_Emp;

class ChangeStudentClassController extends Controller
{


    public function __invoke(Request $request)
    {



        // validate and check authorization
        $this->validate($request, [
            "student_id" => "required",
            "class_id" => "required",
            "new_class_id" => "required",
            "ended_at" => "required|date",
            "start_at" => "required|date",
        ]);

        // delete current teacher
        $class_student = ClassStudent::where('student_id', $request->student_id);

//        $class_student = ClassStudent::where('student_id', $request->student_id)
//            ->where('class_id', $request->class_id)
//            ->first();






        if ($class_student->exists()) {

            $studentClassDetails =  ClassStudent::where('student_id', $request->student_id)->update([
                'end_date' => $request->ended_at,
                'deleted_at' => Carbon::now()
            ]);


            // update hef report records

            StudentHefzReport::where('student_id', $request->student_id)
                ->where('class_id', $request->class_id)
                ->update([
                    'delete_reason' => 'Student transferred from Halaqah '.Classes::where('id',$request->class_id)->first()->name. ' to Halqah '.Classes::where('id',$request->new_class_id)->first()->name,
                    'deleted_at' => Carbon::now()
                ]);
            // update hefzPlan recors
            StudentHefzPlan::where('student_id', $request->student_id)
                ->where('class_id', $request->class_id)
                ->update([
                    'delete_reason' => 'Student transferred from Halaqah '.Classes::where('id',$request->class_id)->first()->name. ' to Halqah '.Classes::where('id',$request->new_class_id)->first()->name,
                    'deleted_at' => Carbon::now()
                ]);
            // update revision reports
            StudentRevisionReport::where('student_id', $request->student_id)
                ->where('class_id', $request->class_id)
                ->update([
                    'delete_reason' => 'Student transferred from Halaqah '.Classes::where('id',$request->class_id)->first()->name. ' to Halqah '.Classes::where('id',$request->new_class_id)->first()->name,
                    'deleted_at' => Carbon::now()
                ]);
            // update revision plans
            StudentRevisionPlan::where('student_id', $request->student_id)
                ->where('class_id', $request->class_id)
                ->update([
                    'delete_reason' => 'Student transferred from Halaqah '.Classes::where('id',$request->class_id)->first()->name. ' to Halqah '.Classes::where('id',$request->new_class_id)->first()->name,
                    'deleted_at' => Carbon::now()
                ]);




//            $class_student->end_date = $request->ended_at;
//            $class_student->deleted_at = Carbon::now();
//            $class_student->save();

            $new_class_student = new ClassStudent();
            $new_class_student->student_id = $request->student_id;
            $new_class_student->class_id = $request->new_class_id;
            $new_class_student->start_date = $request->start_at;
            $new_class_student->transfer_from = $request->class_id;
            $new_class_student->transfer_at = Carbon::now();

            $new_class_student->save();
            Session::flash('flash_message', 'Class changed successfully');

        } else {

            // return with error

            Session::flash('flash_message', 'Error while Transfering Student!');
        }

        return redirect()->back();
    }

}

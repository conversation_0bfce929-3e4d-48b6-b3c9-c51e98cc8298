<?php

namespace Modules\Education\Http\Controllers;

use App\BaseSetup;
use App\Employee;
use App\EvaluationSchemaOption;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\StudentHefzReport;
use App\Subject;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Matrix\Builder;
use Modules\UserActivityLog\Traits\LogActivity;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use Carbon\Carbon;
use App\ClassStudent;
use App\ClassTeacher;
use App\Http\Requests;
use App\ClassTeacherSubject;
use Illuminate\Http\Request;
use App\ClassSubjectTimetable;
use App\Http\Controllers\Controller;
use App\Cen_Emp;
use Tests\Psalm\LaravelPlugin\Models\Car;

class EvaluationSchemaOptionController extends Controller
{

    public function deleteRecord($id)
    {
        try {
            // Assuming you have a model named "YourModel"
            $record = EvaluationSchemaOption::findOrFail($id);
            $record->delete();

            return response()->json(['status' => 'success', 'message' => 'Record deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => 'Something went wrong']);
        }
    }



}

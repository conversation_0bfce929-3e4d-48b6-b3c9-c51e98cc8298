<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Employee;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;
use Yajra\DataTables\Services\DataTable;

class EmployeeDocumentDatatableController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return view('humanresource::index');
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view('humanresource::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        //
    }


    public function show(Request $request, Employee $employee_document_dataTable)
    {


        $employee_document_dataTable = $employee_document_dataTable->load('documents');

        return \DataTables::of(collect($employee_document_dataTable->documents))
            ->setRowClass(function ($user) {
                return 'employeeDocumentRow';
            })
            ->setRowAttr([
                'data-id' => function($document) {
                    return $document->id;
                },
            ])
            ->addIndexColumn()
            ->addColumn('file', function ($document){




                    return strtoupper($document->title);




            })
            ->addColumn('position', function ($document){




                return $document->position;




            })
            ->addColumn('action', function ($document) {
                $actions = '';


                if (\Auth::user()->can("update employee")) {


                $url = explode('/',$document->file);
               $url = array_slice($url, -2, 2); // get the last two items of the array
//                $fileName = $url[2].'-'.$url[3];
                $fileName = $url[0].'-'.$url[1];

                        return  '<div class="row">
                               <div class="col-md-4 ">
                                   <a class="ui green button tr-bg text-uppercase bord-rad"
                                      href="' . route('download.staff.document', ['file_name' => $fileName]) . '">
                                       download
                                       
                                   </a>
                               </div>
                               <div class="col-md-4 ">
                                   <form class="delete"
                                         action="' . route('employee_document.destroy', [$document->documentable_id]) . '"
                                         method="POST">
                                       <input type="hidden"
                                              name="_method"
                                              value="DELETE">
                                       <input type="hidden"
                                              name="_token"
                                              value="{{ csrf_token() }}"/>
                                       <input class="ui red button fix-gr-bg modalLink"
                                              title="Delete Document"
                                              type="submit"
                                              value="Delete">
                                   </form>
                               </div>
                           </div>';

                }
                return $actions;

            })
            ->rawColumns(['file', 'action'])
            ->make(true);


        return view('humanresource::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Response
     */
    public function edit($id)
    {
        return view('humanresource::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy($id)
    {
        //
    }
}

@if(isset($role))
    {!! Form::hidden('role_id', $role->id) !!}
    
@endif


<div class="form-group {{ $errors->has('role') ? 'has-error' : ''}}">
    {!! Form::label('role', 'Role Title', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::text('description', isset($role) ? $role->description :  '', ['class' => 'form-control' , 'required' => 'required']) !!}
        {!! $errors->first('role', '<p class="help-block">:message</p>') !!}
    </div>
</div>
<h3>User Dashboard</h3>
<hr>
<div class="row">
    <div class="col-sm-3">

        @if(auth()->user()->can('have_teacher_dashboard'))
        {!! Form::checkbox("permissions['have_teacher_dashboard']", 'have_teacher_dashboard', false) !!}                                    
        Teacher Dashboard
        @endif
    </div>
    <div class="col-sm-3">
        @if(auth()->user()->can('have_supervisor_dashboard'))
        {!! Form::checkbox("permissions['have_supervisor_dashboard']", 'have_supervisor_dashboard', false) !!}                                    
        Supervisor Dashboard
        @endif
    </div>
    <div class="col-sm-3">
        @if(auth()->user()->can('have_finance_dashboard'))
        {!! Form::checkbox("permissions['have_finance_dashboard']", 'have_finance_dashboard', false) !!}                                    
        Finance Dashboard
        @endif
    </div>
    <div class="col-sm-3">
        @if(auth()->user()->can('have_human_resource_dashboard'))
        {!! Form::checkbox("permissions['have_human_resource_dashboard']", 'have_human_resource_dashboard', false) !!}                                    
        HR Dashboard
        @endif
    </div>
</div>


<h3>Permissions</h3>
<hr>

@foreach($modules as $key => $module)
@if(count($module))
<div class="clearfix">
<h4>{{$key}}</h4>
    <div class="well">
        @foreach($module as $unit)
        <h4>{{ $unit['name'] }} <input type="checkbox" class="toggle-actions" id="{{ \Illuminate\Support\Str::slug(str_replace('.', '_' ,$unit['name'])) }}" > </h4>
        <div class="row">
            @if(isset($unit['actions']))
            @foreach($unit['actions'] as $action)
                <div class="col-sm-3">
                
                {!! Form::checkbox("permissions[$action]", $action, isset($role) &&  is_permission_granted($role , $action), ['class' => \Illuminate\Support\Str::slug(str_replace('.', '_' ,$unit['name']))]) !!}
                {{ ucwords(str_replace('_' , ' ' ,$action)) }}
                </div>
            @endforeach
            @endif
        </div>
        @endforeach
    </div>
</div>

@endif
@endforeach

<div class="form-group">
    <div class="col-md-offset-4 col-md-4">
        {!! Form::submit(trans('common.save'), ['class' => 'btn btn-danger']) !!}
    </div>
</div>


@section('js')
<script>
    $('.toggle-actions').click(function(){
        var unite_class = this.id;
        that = this;
        $('.'+unite_class).each(function(index,el){
            $(el).prop('checked' , $(that).prop('checked'));
        })
    })
</script>
@append
<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Log;

/**
 * JobProvider Entity
 * 
 * Master table for all job providers (Jobs.af, ACBAR, etc.)
 * Centralizes provider configuration and capabilities
 * 
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property string $base_url
 * @property string|null $api_endpoint
 * @property string|null $scraping_endpoint
 * @property bool $is_active
 * @property bool $supports_api
 * @property bool $supports_scraping
 * @property string $fetch_method
 * @property int $rate_limit_per_minute
 * @property int $timeout_seconds
 * @property int $retry_attempts
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
final class JobProvider extends Model
{
    protected $table = 'jobseeker_job_providers';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'base_url',
        'api_endpoint',
        'scraping_endpoint',
        'is_active',
        'supports_api',
        'supports_scraping',
        'fetch_method',
        'rate_limit_per_minute',
        'timeout_seconds',
        'retry_attempts',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'supports_api' => 'boolean',
        'supports_scraping' => 'boolean',
        'rate_limit_per_minute' => 'integer',
        'timeout_seconds' => 'integer',
        'retry_attempts' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get all jobs for this provider
     */
    public function jobs(): HasMany
    {
        return $this->hasMany(Job::class, 'provider_id');
    }

    /**
     * Get all categories for this provider
     */
    public function categories(): HasMany
    {
        return $this->hasMany(ProviderJobCategory::class, 'provider_id');
    }

    /**
     * Get all locations for this provider
     */
    public function locations(): HasMany
    {
        return $this->hasMany(ProviderJobLocation::class, 'provider_id');
    }

    /**
     * Get detailed job information for this provider
     */
    public function jobDetailedInfo(): HasMany
    {
        return $this->hasMany(JobDetailedInfo::class, 'provider_id');
    }

    /**
     * Provider-level settings
     */
    public function settings(): HasOne
    {
        return $this->hasOne(ProviderSetting::class, 'provider_id');
    }

    /**
     * Get provider by slug
     */
    public static function findBySlug(string $slug): ?JobProvider
    {
        return self::where('slug', $slug)->where('is_active', true)->first();
    }

    /**
     * Get active providers only
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get providers that support API
     */
    public function scopeSupportsApi($query)
    {
        return $query->where('supports_api', true);
    }

    /**
     * Get providers that support scraping
     */
    public function scopeSupportsScraping($query)
    {
        return $query->where('supports_scraping', true);
    }

    /**
     * Check if provider can use API
     */
    public function canUseApi(): bool
    {
        return $this->supports_api && !empty($this->api_endpoint) && $this->is_active;
    }

    /**
     * Check if provider can use scraping
     */
    public function canUseScraping(): bool
    {
        return $this->supports_scraping && !empty($this->scraping_endpoint) && $this->is_active;
    }

    /**
     * Get the appropriate endpoint based on fetch method
     */
    public function getEndpoint(): ?string
    {
        switch ($this->fetch_method) {
            case 'api':
                return $this->api_endpoint;
            case 'scraping':
                return $this->scraping_endpoint;
            case 'both':
                return $this->canUseApi() ? $this->api_endpoint : $this->scraping_endpoint;
            default:
                return null;
        }
    }

    /**
     * Log provider activity
     */
    public function logActivity(string $action, array $context = []): void
    {
        Log::info("JobProvider activity: {$action}", array_merge([
            'provider_id' => $this->id,
            'provider_name' => $this->name,
            'provider_slug' => $this->slug,
        ], $context));
    }
}

<?php

namespace Modules\Education\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Center;
use App\MarksGrade;
use App\Program;
use App\ProgramLevel;

use http\Env\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use PhpOffice\PhpWord\IOFactory;
use Session;

class UpdateProgramToLessonController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function __invoke(Request $request,$id,$fromSuat,$levelId)
    {




        ProgramLevel::where('program_id', $id)->where('id',$levelId)->update(['to_lesson' => $fromSuat]);


        return response()->json('Program updated', 200);


    }

}





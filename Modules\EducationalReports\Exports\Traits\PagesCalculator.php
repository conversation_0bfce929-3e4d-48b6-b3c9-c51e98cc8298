<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports\Traits;

use Illuminate\Support\Facades\DB;

/**
 * Shared trait for calculating pages from Quran memorization and revision
 * using the proper stored procedures based on study direction.
 *
 * This trait provides methods to calculate pages for both daily reports
 * and teacher performance metrics, ensuring consistent page calculations
 * across all educational report exports.
 */
trait PagesCalculator
{
    /**
     * Calculate pages for memorization using stored procedures based on direction.
     * 
     * @param string $studyDirection Either 'forward' or 'backward'
     * @param int $fromSurat Starting surah number
     * @param int $fromAyat Starting ayat number  
     * @param int $toSurat Ending surah number
     * @param int $toAyat Ending ayat number
     * @return int Number of pages calculated
     */
    protected function calculatePagesRange(
        string $studyDirection, 
        int $fromSurat, 
        int $fromAyat, 
        int $toSurat, 
        int $toAyat
    ): int {
        try {
            if ($studyDirection === 'backward') {
                // EXACT copy from MonthlyPlanController lines 409-417
                $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $fromSurat,
                    $fromAyat,
                    $toSurat,
                    $toAyat
                ]);
                return (int) ($numberofPages[0]->numberofPagesSum ?? 0);
            } else {
                // EXACT copy from MonthlyPlanController lines 425-433
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $fromSurat,
                    $fromAyat,
                    $toSurat,
                    $toAyat
                ]);

                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                return (int) ($results[0]->number_of_pages_sum ?? 0);
            }
        } catch (\Exception $e) {
            \Log::error('Error calculating pages range: ' . $e->getMessage(), [
                'study_direction' => $studyDirection,
                'from_surat' => $fromSurat,
                'from_ayat' => $fromAyat,
                'to_surat' => $toSurat,
                'to_ayat' => $toAyat
            ]);
            return 0;
        }
    }

    /**
     * Calculate pages for revision (same logic as memorization).
     * 
     * @param string $studyDirection Either 'forward' or 'backward'
     * @param int $fromSurat Starting surah number
     * @param int $fromAyat Starting ayat number  
     * @param int $toSurat Ending surah number
     * @param int $toAyat Ending ayat number
     * @return int Number of pages calculated
     */
    protected function calculateRevisionPages(
        string $studyDirection, 
        int $fromSurat, 
        int $fromAyat, 
        int $toSurat, 
        int $toAyat
    ): int {
        // Revision uses the same page calculation logic as memorization
        return $this->calculatePagesRange($studyDirection, $fromSurat, $fromAyat, $toSurat, $toAyat);
    }

    /**
     * Calculate pages for a daily report row based on the student's monthly plan.
     * 
     * @param array $reportData Array containing report data including student_id, class_id, etc.
     * @param string $reportType Either 'memorization' or 'revision'
     * @return int Number of pages calculated
     */
    protected function calculateReportPages(array $reportData, string $reportType = 'memorization'): int
    {
        // Determine column names based on report type
        if ($reportType === 'memorization') {
            $fromSuratCol = 'hefz_from_surat';
            $fromAyatCol = 'hefz_from_ayat';
            $toSuratCol = 'hefz_to_surat';
            $toAyatCol = 'hefz_to_ayat';
        } else {
            $fromSuratCol = 'revision_from_surat';
            $fromAyatCol = 'revision_from_ayat';
            $toSuratCol = 'revision_to_surat';
            $toAyatCol = 'revision_to_ayat';
        }

        // If no range specified in the report, return 0
        if (empty($reportData[$fromSuratCol]) || empty($reportData[$toSuratCol])) {
            return 0;
        }

        // Get the student's plan to determine study direction - use EXACT logic from MonthlyPlanController
        $fromSurat = (int) $reportData[$fromSuratCol];
        $fromAyat = (int) $reportData[$fromAyatCol];
        $toSurat = (int) $reportData[$toSuratCol];
        $toAyat = (int) $reportData[$toAyatCol];

        // Use MonthlyPlanController's determineStudyDirection logic
        $studyDirection = $this->determineStudyDirection($fromSurat, $toSurat, $fromAyat, $toAyat);

        return $this->calculatePagesRange($studyDirection, $fromSurat, $fromAyat, $toSurat, $toAyat);
    }

    /**
     * Determine study direction using EXACT logic from MonthlyPlanController
     * Lines 1393-1427 of Modules/Education/Http/Controllers/MonthlyPlanController.php
     */
    private function determineStudyDirection($hefz_from_surat, $to_surat, $start_from_ayat, $to_ayat): string
    {
        if (!empty($hefz_from_surat) && !empty($to_surat)) {
            if (($hefz_from_surat == $to_surat) && $start_from_ayat < $to_ayat) {
                return 'forward';
            }

            if (($hefz_from_surat == $to_surat) && $start_from_ayat > $to_ayat) {
                return 'backward';
            }

            if ($hefz_from_surat > $to_surat) {
                return 'backward';
            }

            if ($hefz_from_surat < $to_surat) {
                return 'forward';
            }
        }

        return 'forward'; // Default fallback
    }

    /**
     * Calculate target sessions for a class in a given month based on timetable.
     * 
     * @param int $classId The class ID
     * @param int|null $year The year (e.g., 2025)
     * @param int|null $month The month (1-12)
     * @return int Number of target sessions
     */
    protected function calculateTargetSessions(int $classId, ?int $year, ?int $month): int
    {
        try {
            // Return 0 if year or month is null
            if ($year === null || $month === null) {
                \Log::warning('calculateTargetSessions called with null year or month', [
                    'class_id' => $classId,
                    'year' => $year,
                    'month' => $month
                ]);
                return 0;
            }

            // Get the class timetable
            $timetable = DB::selectOne("
                SELECT sat, sun, mon, tue, wed, thu, fri
                FROM class_timetable 
                WHERE class_id = ?
            ", [$classId]);

            if (!$timetable) {
                return 0;
            }

            // Count days in the month where classes are scheduled
            $daysWithClasses = [];
            $dayColumns = ['sat', 'sun', 'mon', 'tue', 'wed', 'thu', 'fri'];
            
            foreach ($dayColumns as $dayColumn) {
                if (!empty($timetable->{$dayColumn})) {
                    $daysWithClasses[] = array_search($dayColumn, $dayColumns); // 0=sat, 1=sun, etc.
                }
            }

            if (empty($daysWithClasses)) {
                return 0;
            }

            // Calculate how many of those days occur in the given month
            $firstDay = mktime(0, 0, 0, $month, 1, $year);
            $lastDay = mktime(0, 0, 0, $month + 1, 0, $year);
            $targetSessions = 0;

            for ($day = $firstDay; $day <= $lastDay; $day += 86400) { // 86400 = seconds in a day
                $dayOfWeek = (int) date('w', $day); // 0=Sunday, 1=Monday, ..., 6=Saturday
                $adjustedDay = ($dayOfWeek + 1) % 7; // Adjust to match our array (0=Saturday, 1=Sunday, ...)
                
                if (in_array($adjustedDay, $daysWithClasses)) {
                    $targetSessions++;
                }
            }

            return $targetSessions;
        } catch (\Exception $e) {
            \Log::error('Error calculating target sessions: ' . $e->getMessage(), [
                'class_id' => $classId,
                'year' => $year,
                'month' => $month
            ]);
            return 0;
        }
    }

    /**
     * Calculate total pages taught by a teacher in a specific class and month.
     * 
     * @param int $classId The class ID
     * @param int|null $year The year (e.g., 2025) 
     * @param int|null $month The month (1-12)
     * @return int Total pages taught
     */
    /**
     * Calculate total pages taught by teacher using PROVEN ClassReportController methodology
     * 
     * @param int $classId The class ID
     * @param int|null $year The year (null-safe)
     * @param int|null $month The month (null-safe)
     * @return int Total pages taught
     */
    protected function calculateTeacherPagesTaught(int $classId, ?int $year, ?int $month): int
    {
        if ($year === null || $month === null) {
            return 0;
        }

        try {
            // Get all reports for this class in this month with valid surah/ayat data
            $reports = DB::select("
                SELECT 
                    shr.id,
                    shr.hefz_from_surat,
                    shr.hefz_from_ayat,
                    shr.hefz_to_surat,
                    shr.hefz_to_ayat,
                    shp.study_direction,
                    shr.hefz_plan_id
                FROM student_hefz_report shr
                LEFT JOIN student_hefz_plans shp ON shr.hefz_plan_id = shp.id
                WHERE shr.class_id = ?
                    AND YEAR(shr.created_at) = ?
                    AND MONTH(shr.created_at) = ?
                    AND shr.hefz_from_surat IS NOT NULL
                    AND shr.hefz_to_surat IS NOT NULL
                    AND shr.deleted_at IS NULL
            ", [$classId, $year, $month]);

            $totalPages = 0;

            // Use EXACT same methodology as ClassReportController lines 1132, 1138-1157
            foreach ($reports as $report) {
                $memorizedNumberofPages = 0; // Line 1132 equivalent

                if ($report->study_direction == 'backward') {
                    // EXACT copy from lines 1140-1146
                    $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                        $report->hefz_from_surat,
                        $report->hefz_from_ayat,
                        $report->hefz_to_surat,
                        $report->hefz_to_ayat
                    ]);
                    $memorizedNumberofPages = $result[0]->numberofPagesSum ?? 0;
                } else {
                    // EXACT copy from lines 1149-1156
                    DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                        $report->hefz_from_surat,
                        $report->hefz_from_ayat,
                        $report->hefz_to_surat,
                        $report->hefz_to_ayat
                    ]);
                    $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                    $memorizedNumberofPages = $results[0]->number_of_pages_sum ?? 0;
                }

                $totalPages += $memorizedNumberofPages;
            }

            return $totalPages;
        } catch (\Exception $e) {
            \Log::error('Error calculating teacher pages taught: ' . $e->getMessage(), [
                'class_id' => $classId,
                'year' => $year,
                'month' => $month
            ]);
            return 0;
        }
    }

    /**
     * Split teacher names for Excel comment display.
     * Returns array with 'display' (first name) and 'comment' (remaining names).
     * 
     * @param string $teacherNames Comma-separated teacher names
     * @return array
     */
    protected function splitTeacherNames(string $teacherNames): array
    {
        $names = array_map('trim', explode(',', $teacherNames));
        
        return [
            'display' => $names[0] ?? '',
            'comment' => count($names) > 1 ? 'Additional Teachers: ' . implode(', ', array_slice($names, 1)) : null
        ];
    }

    /**
     * Calculate total pages planned for a class in a given month using PROVEN ClassReportController methodology
     * 
     * @param int $classId The class ID
     * @param int|null $year The year (null-safe)
     * @param int|null $month The month (null-safe)
     * @return int Total pages planned
     */
    protected function calculatePagesPlanned(int $classId, ?int $year, ?int $month): int
    {
        if ($year === null || $month === null) {
            return 0;
        }

        try {
            $planYearMonth = sprintf('%d-%02d', $year, $month);
            
            // Get all student hefz plans for this class and month using PROVEN approach
            $studentHefzPlans = DB::select("
                SELECT id, start_from_surat, start_from_ayat, to_surat, to_ayat, study_direction
                FROM student_hefz_plans
                WHERE class_id = ?
                    AND plan_year_and_month = ?
                    AND status IN ('active', 'approved')
                    AND deleted_at IS NULL
            ", [$classId, $planYearMonth]);

            $totalPagesPlanned = 0;

            // Use EXACT same methodology as ClassReportController lines 1132, 1138-1157
            foreach ($studentHefzPlans as $studentHefzPlan) {
                $memorizedNumberofPages = 0; // Line 1132 equivalent

                if ($studentHefzPlan->study_direction == 'backward') {
                    // EXACT copy from lines 1140-1146
                    $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                        $studentHefzPlan->start_from_surat,
                        $studentHefzPlan->start_from_ayat,
                        $studentHefzPlan->to_surat,
                        $studentHefzPlan->to_ayat
                    ]);
                    $memorizedNumberofPages = $result[0]->numberofPagesSum ?? 0;
                } else {
                    // EXACT copy from lines 1149-1156
                    DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                        $studentHefzPlan->start_from_surat,
                        $studentHefzPlan->start_from_ayat,
                        $studentHefzPlan->to_surat,
                        $studentHefzPlan->to_ayat
                    ]);
                    $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                    $memorizedNumberofPages = $results[0]->number_of_pages_sum ?? 0;
                }

                $totalPagesPlanned += $memorizedNumberofPages;
            }

            return $totalPagesPlanned;
        } catch (\Exception $e) {
            \Log::error('Error calculating pages planned: ' . $e->getMessage(), [
                'class_id' => $classId,
                'year' => $year,
                'month' => $month
            ]);
            return 0;
        }
    }
}
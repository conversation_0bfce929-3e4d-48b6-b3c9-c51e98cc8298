<?php

namespace App\Jobs;

use App\Employee;
use App\Services\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\EmailSetting;

use Illuminate\Contracts\Mail\Mailer;

class SendStudentInterviewConfirmationToStudentMailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user_info = [];
    protected $sender;
    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_info, $sender)
    {
        $this->user_info = $user_info;
        $this->sender = $sender;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle(Mailer $mailer)
    {
        $emailService = app(\App\Services\EmailService::class);

        foreach ($this->user_info as $info) {
            $emailService->sendEmail(
                ['email' => $info['studentEmail'], 'name' => $info['studentName']],
                'Interview Confirmation Received !',
                'modules.site.templates.wajeha.backEnd.studentInformation.student_interview_confirmation',
                ['data' => $info]
            );
        }

//        $mj = new Client(env('MAILJETAPIKEY'), env('MAILJETAPISECRET'), true, ['version' => 'v3.1']);
//
//        foreach ($this->user_info as $info) {
//
//            $body = [
//                'Messages' => [
//                    [
//                        'From' => [
//                            'Email' => $this->sender['system_email'],
//                            'Name' => $this->sender['organization_name']
//                        ],
//                        'To' => [
//                            [
//                                'Email' => $info['studentEmail'],
//                                'Name' => $info['studentName']
//                            ]
//                        ],
//                        'Subject' => 'Interview Confirmation Received !',
//                        'TextPart' => 'Your interview invitation has been accepted.',
//                        'HTMLPart' => view('modules.site.templates.wajeha.backEnd.studentInformation.student_interview_confirmation', ['data' => $info])->render() // Use your actual email template
//                    ]
//                ]
//            ];
//
//            $response = $mj->post(Resources::$Email, ['body' => $body]);
//            if (!$response->success()) {
//                throw new \Exception("Failed to send email: " . json_encode($response->getData()));
//            }
//
//        }


    }
}

<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentLastApprovedNouranyaPlan extends Model
{
    use HasFactory,SoftDeletes;
    protected $table = 'student_last_approved_nouranya_plans';

    protected $fillable = [
        'student_id',
        'approved_by',
        'updated_at',
        'class_id',
        'organization_id',
        'student_id',
        'level_id',
        'plan_year_and_month',
        'start_date',
        'end_date',
        'from_lesson',
        'to_lesson',
        'center_id',
        'status',
        'approved_by',
        'supervisor_comment',
        'created_by',
        'updated_by',
        'delete_reason',
        'deleted_at',
        'talqeen_talaqqi',
        'talaqqi_from_lesson',
        'talqeen_from_lesson',
        'talaqqi_to_lesson',
        'talqeen_to_lesson',
        'from_lesson_line_number',
        'to_lesson_line_number',
        'plan_year_month_day'

    ];
    public $timestamps = true; // Ensure timestamps are used

    /**
     * Get the student that owns the plan.
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    /**
     * Get the user who approved the plan.
     */
    public function approvedBy()
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }
}

<?php

namespace Modules\Education\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;


/**
 * Class ClassReportStoreRequest
 * @package Modules\Communicate\Http\Requests
 *
 *
 */
class ClassReportStaatisticsRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $todayDate = date('M Y');

        return [
            'classDate' => 'date_format:M Y|before_or_equal:'.$todayDate

        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            'classDate' => 'The date  must be a value preceding or equal to today'




        ];
    }
}

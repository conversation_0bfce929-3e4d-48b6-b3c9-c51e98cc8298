@extends('layouts.hound')

@section('mytitle', 'Commands Home')

@section("css")
    <!-- Include your existing CSS links (Bootstrap, Semantic UI, etc.) -->
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/semantic-ui/2.4.1/semantic.min.css" rel="stylesheet" type="text/css"/>
    <!-- You may add additional CSS as needed -->
@endsection

@section('content')
    {{-- Breadcrumb --}}
    <div class="pull-right">
        <ol class="breadcrumb custom-breadcrumb">
            <li><a href="{{ url('workplace') }}">Dashboard</a></li>
            <li class="active">Commands Home</li>
        </ol>
    </div>

    <div class="panel-heading">
        <h3>Commands Home</h3>
    </div>

    <div class="panel-body">
        <!-- Bootstrap horizontal tabs -->
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active">
                <a href="#ijazasanad" aria-controls="ijazasanad" role="tab" data-toggle="tab">Ijazasanad Plans</a>
            </li>
            <li role="presentation">
                <a href="#memorization" aria-controls="memorization" role="tab" data-toggle="tab">Memorization Revision</a>
            </li>
            <li role="presentation">
                <a href="#nouranya" aria-controls="nouranya" role="tab" data-toggle="tab">Nouranya Plans</a>
            </li>
            <li role="presentation">
                <a href="#trashClockOut" aria-controls="trashClockOut" role="tab" data-toggle="tab">Trash Clockouts</a>
            </li>
            <li role="presentation">
                <a href="#cacheTrashed" aria-controls="cacheTrashed" role="tab" data-toggle="tab">Cache Trashed Students</a>
            </li>
            <li role="presentation">
                <a href="#attendanceSponsors" aria-controls="attendanceSponsors" role="tab" data-toggle="tab">Attendance Email Sponsors</a>
            </li>
            <li role="presentation">
                <a href="#dbDocs" aria-controls="dbDocs" role="tab" data-toggle="tab">Database Documentation</a>
            </li>
        </ul>

        <div class="tab-content" style="margin-top:20px;">
            <!-- Ijazasanad Plans Reminder -->
            <div role="tabpanel" class="tab-pane active" id="ijazasanad">
                <h4>Ijazasanad Plans Reminder</h4>
                <p>Manage settings, exclusions, and logs for the Ijazasanad Plans Reminder command.</p>
                <a href="{{ route('general.commands.ijazasanad.show') }}" class="btn btn-primary">Manage Ijazasanad</a>
            </div>

            <!-- Memorization Revision Plans Reminder -->
            <div role="tabpanel" class="tab-pane" id="memorization">
                <h4>Memorization Revision Plans Reminder</h4>
                <p>Manage settings for the Memorization Revision Plans Reminder command.</p>
                <a href="{{ route('general.commands.memorization.show') }}" class="btn btn-primary">Manage Memorization Revision</a>
            </div>

            <!-- Nouranya Plans Reminder -->
            <div role="tabpanel" class="tab-pane" id="nouranya">
                <h4>Nouranya Plans Reminder</h4>
                <p>Manage settings for the Nouranya Plans Reminder command.</p>
                <a href="{{ route('general.commands.nouranya.show') }}" class="btn btn-primary">Manage Nouranya</a>
            </div>

            <!-- Trash Missed Clockouts -->
            <div role="tabpanel" class="tab-pane" id="trashClockOut">
                <h4>Trash Missed Clockouts</h4>
                <p>Configure and monitor the Trash Missed Clockouts command.</p>
                <a href="{{ route('general.commands.trashClockOut.show') }}" class="btn btn-primary">Manage Trash Clockouts</a>
            </div>

            <!-- Cache Trashed Students -->
            <div role="tabpanel" class="tab-pane" id="cacheTrashed">
                <h4>Cache Trashed Students</h4>
                <p>Manage settings for the Cache Trashed Students command.</p>
                <a href="{{ route('general.commands.cacheTrashed.show') }}" class="btn btn-primary">Manage Cache Trashed Students</a>
            </div>

            <!-- Attendance Email Sponsors -->
            <div role="tabpanel" class="tab-pane" id="attendanceSponsors">
                <h4>Attendance Email Sponsors</h4>
                <p>Configure the Attendance Email Sponsors command.</p>
                <a href="{{ route('general.commands.attendanceSponsors.show') }}" class="btn btn-primary">Manage Attendance Sponsors</a>
            </div>
            
            <!-- Database Documentation -->
            <div role="tabpanel" class="tab-pane" id="dbDocs">
                <h4>Database Documentation</h4>
                <p>Generate and manage comprehensive database schema documentation.</p>
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            <h5>Documentation Status</h5>
                            <p>
                                <strong>Last Generated:</strong> {{ file_exists(base_path('ai_docs/architectureDiagrams/complete_database_documentation_with_tables.html')) ? date('Y-m-d H:i:s', filemtime(base_path('ai_docs/architectureDiagrams/complete_database_documentation_with_tables.html'))) : 'Never' }}
                            </p>
                            <p>
                                <strong>Documentation Path:</strong> <code>ai_docs/architectureDiagrams/complete_database_documentation_with_tables.html</code>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="row" style="margin-top:10px;">
                    <div class="col-md-6">
                        <form action="{{ route('general.commands.dbdocs.generate') }}" method="POST" class="form-inline">
                            @csrf
                            <button type="submit" class="btn btn-success">
                                <i class="fa fa-refresh"></i> Generate Documentation Now
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <a href="{{ url('ai_docs/architectureDiagrams/complete_database_documentation_with_tables.html') }}" target="_blank" class="btn btn-primary">
                            <i class="fa fa-eye"></i> View Documentation
                        </a>
                    </div>
                </div>
                <div class="row" style="margin-top:15px;">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <strong>Note:</strong> Database documentation is automatically generated weekly (Sundays at 1:00 AM) and after migrations in non-production environments.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function(){
            // Activate tab on click
            $('.nav-tabs a').click(function (e) {
                e.preventDefault();
                $(this).tab('show');
            });
        });
    </script>
@endsection 
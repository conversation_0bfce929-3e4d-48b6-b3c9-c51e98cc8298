<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\EmailLog;

class QueueMonitor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:monitor {--show-failed : Show failed jobs} {--show-stats : Show queue statistics}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor queue jobs and email sending status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Queue Monitor Report');
        $this->info(str_repeat('=', 50));

        if ($this->option('show-stats')) {
            $this->showQueueStatistics();
        }

        if ($this->option('show-failed')) {
            $this->showFailedJobs();
        }

        if (!$this->option('show-stats') && !$this->option('show-failed')) {
            $this->showQueueStatistics();
            $this->line('');
            $this->showRecentEmailLogs();
        }
    }

    protected function showQueueStatistics()
    {
        $this->info('Queue Statistics:');
        $this->line(str_repeat('-', 30));

        // Queue jobs statistics
        $pendingJobs = DB::table('queue_jobs')->count();
        $failedJobs = DB::table('failed_jobs')->count();

        $this->line("Pending Jobs: {$pendingJobs}");
        $this->line("Failed Jobs: {$failedJobs}");

        // Email statistics from last 24 hours
        $emailStats = EmailLog::where('created_at', '>=', now()->subDay())
            ->selectRaw('
                COUNT(*) as total_emails,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_emails,
                SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_emails
            ')
            ->first();

        $this->line('');
        $this->info('Email Statistics (Last 24 hours):');
        $this->line("Total Emails: {$emailStats->total_emails}");
        $this->line("Successful: {$emailStats->successful_emails}");
        $this->line("Failed: {$emailStats->failed_emails}");

        if ($emailStats->total_emails > 0) {
            $successRate = round(($emailStats->successful_emails / $emailStats->total_emails) * 100, 2);
            $this->line("Success Rate: {$successRate}%");
        }
    }

    protected function showFailedJobs()
    {
        $this->info('Recent Failed Jobs:');
        $this->line(str_repeat('-', 30));

        $failedJobs = DB::table('failed_jobs')
            ->orderBy('failed_at', 'desc')
            ->limit(10)
            ->get();

        if ($failedJobs->isEmpty()) {
            $this->line('No failed jobs found.');
            return;
        }

        foreach ($failedJobs as $job) {
            $payload = json_decode($job->payload, true);
            $jobClass = $payload['displayName'] ?? 'Unknown';
            
            $this->line("ID: {$job->id}");
            $this->line("Job: {$jobClass}");
            $this->line("Queue: {$job->queue}");
            $this->line("Failed At: {$job->failed_at}");
            $this->line("Exception: " . substr($job->exception, 0, 100) . '...');
            $this->line('');
        }
    }

    protected function showRecentEmailLogs()
    {
        $this->info('Recent Email Logs (Last 10):');
        $this->line(str_repeat('-', 30));

        $recentEmails = EmailLog::orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        if ($recentEmails->isEmpty()) {
            $this->line('No email logs found.');
            return;
        }

        foreach ($recentEmails as $email) {
            $status = $email->success ? '✅ SUCCESS' : '❌ FAILED';
            $this->line("To: {$email->to}");
            $this->line("Subject: {$email->subject}");
            $this->line("Status: {$status}");
            $this->line("Time: {$email->created_at}");
            if (!$email->success && $email->error_message) {
                $this->line("Error: {$email->error_message}");
            }
            if ($email->correlation_id) {
                $this->line("Correlation ID: {$email->correlation_id}");
            }
            $this->line('');
        }
    }
} 
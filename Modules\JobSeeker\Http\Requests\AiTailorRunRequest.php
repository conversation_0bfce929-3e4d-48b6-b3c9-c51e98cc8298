<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use <PERSON><PERSON>les\JobSeeker\Entities\Job;
use <PERSON><PERSON>les\JobSeeker\Entities\JobSeekerResume;

/**
 * AiTailorRunRequest validates AI resume tailoring execution requests.
 * 
 * Purpose: Ensure valid job slug, owned resume selection, and reasonable constraints.
 * Business rules: Job must exist; max 5 resumes; distinct resume IDs; user ownership.
 * Security: Prevents unauthorized access to others' resumes; validates job existence.
 */
final class AiTailorRunRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request
     */
    public function authorize(): bool
    {
        // User must be authenticated as job_seeker and have tailor_run permission
        return auth('job_seeker')->check() && 
               auth('job_seeker')->user()->can('jobseeker.ai.tailor_run');
    }

    /**
     * Get the validation rules that apply to the request
     */
    public function rules(): array
    {
        return [
            'job_slug' => [
                'required',
                'string',
                'min:3',
                'max:255',
                'regex:/^[a-zA-Z0-9\-_]+$/', // Alphanumeric with hyphens and underscores
            ],
            'resume_ids' => [
                'required',
                'array',
                'min:1',
                'max:5', // Maximum 5 resumes
            ],
            'resume_ids.*' => [
                'required',
                'integer',
                'min:1',
                'distinct', // All resume IDs must be unique
            ],
            'constraints' => [
                'nullable',
                'array',
            ],
            'constraints.prefer_ats' => [
                'nullable',
                'boolean',
            ],
            'constraints.summary_length' => [
                'nullable',
                'integer',
                'min:1',
                'max:10', // 1-10 sentences
            ],
            'constraints.max_bullets_per_role' => [
                'nullable',
                'integer',
                'min:2',
                'max:10',
            ],
            'constraints.emphasize_keywords' => [
                'nullable',
                'array',
                'max:20', // Maximum 20 keywords
            ],
            'constraints.emphasize_keywords.*' => [
                'string',
                'max:50',
                'regex:/^[a-zA-Z0-9\s\-+#.()]+$/',
            ],
            'idempotency_key' => [
                'nullable',
                'string',
                'min:10',
                'max:64',
                'regex:/^[a-zA-Z0-9\-_]+$/',
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules
     */
    public function messages(): array
    {
        return [
            'job_slug.required' => 'Job slug is required to tailor your resume.',
            'job_slug.regex' => 'Invalid job slug format.',
            'resume_ids.required' => 'Please select at least one resume to tailor.',
            'resume_ids.min' => 'Please select at least one resume.',
            'resume_ids.max' => 'You can select a maximum of 5 resumes.',
            'resume_ids.*.integer' => 'Invalid resume selection.',
            'resume_ids.*.distinct' => 'Each resume can only be selected once.',
            'constraints.summary_length.min' => 'Summary length must be at least 1 sentence.',
            'constraints.summary_length.max' => 'Summary length cannot exceed 10 sentences.',
            'constraints.max_bullets_per_role.min' => 'Must have at least 2 bullet points per role.',
            'constraints.max_bullets_per_role.max' => 'Cannot exceed 10 bullet points per role.',
            'constraints.emphasize_keywords.max' => 'You can emphasize a maximum of 20 keywords.',
            'constraints.emphasize_keywords.*.max' => 'Each keyword cannot exceed 50 characters.',
            'constraints.emphasize_keywords.*.regex' => 'Keywords can only contain letters, numbers, and common punctuation.',
            'idempotency_key.min' => 'Idempotency key must be at least 10 characters.',
            'idempotency_key.max' => 'Idempotency key cannot exceed 64 characters.',
            'idempotency_key.regex' => 'Idempotency key contains invalid characters.',
        ];
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation(): void
    {
        // Clean up job slug
        if ($this->has('job_slug')) {
            $slug = trim($this->input('job_slug'));
            $this->merge(['job_slug' => $slug]);
        }

        // Ensure resume_ids are integers and remove duplicates
        if ($this->has('resume_ids') && is_array($this->input('resume_ids'))) {
            $resumeIds = array_unique(array_map('intval', array_filter($this->input('resume_ids'))));
            $this->merge(['resume_ids' => array_values($resumeIds)]);
        }

        // Clean up emphasize_keywords if provided
        if ($this->has('constraints.emphasize_keywords') && is_array($this->input('constraints.emphasize_keywords'))) {
            $keywords = array_unique(array_filter(array_map('trim', $this->input('constraints.emphasize_keywords'))));
            $this->merge([
                'constraints' => array_merge($this->input('constraints', []), [
                    'emphasize_keywords' => array_values($keywords)
                ])
            ]);
        }
    }

    /**
     * Configure the validator instance
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $userId = auth('job_seeker')->id();

            // Validate job exists and is accessible
            if ($this->has('job_slug')) {
                $job = Job::where('slug', $this->input('job_slug'))->first();
                
                if (!$job) {
                    $validator->errors()->add('job_slug', 'The specified job could not be found.');
                } else {
                    // Store job for later use in controller
                    $this->merge(['_validated_job' => $job]);
                }
            }

            // Validate resume ownership and existence
            if ($this->has('resume_ids') && is_array($this->input('resume_ids'))) {
                $resumeIds = $this->input('resume_ids');
                
                // Check that all resumes exist and belong to the current user
                $userResumes = JobSeekerResume::where('job_seeker_id', $userId)
                    ->whereIn('id', $resumeIds)
                    ->pluck('id')
                    ->toArray();

                $missingResumeIds = array_diff($resumeIds, $userResumes);
                
                if (!empty($missingResumeIds)) {
                    $validator->errors()->add('resume_ids', 
                        'Some selected resumes do not exist or do not belong to you: ' . implode(', ', $missingResumeIds));
                }

                // Store validated resumes for later use
                if (empty($missingResumeIds)) {
                    $resumes = JobSeekerResume::whereIn('id', $resumeIds)->get();
                    $this->merge(['_validated_resumes' => $resumes]);
                }
            }

            // Validate constraints if provided
            if ($this->has('constraints') && is_array($this->input('constraints'))) {
                $constraints = $this->input('constraints');

                // Validate emphasize_keywords content
                if (isset($constraints['emphasize_keywords']) && is_array($constraints['emphasize_keywords'])) {
                    foreach ($constraints['emphasize_keywords'] as $index => $keyword) {
                        if (empty(trim($keyword))) {
                            $validator->errors()->add("constraints.emphasize_keywords.{$index}", 
                                'Empty keywords are not allowed.');
                        }
                    }
                }
            }

            // Check for recent runs to prevent spam (rate limiting at application level)
            $recentRuns = \Modules\JobSeeker\Entities\JobSeekerAiTailorRun::where('job_seeker_id', $userId)
                ->where('created_at', '>', now()->subMinutes(5))
                ->count();

            if ($recentRuns >= 3) {
                $validator->errors()->add('job_slug', 
                    'You have reached the limit of AI tailoring runs. Please wait a few minutes before trying again.');
            }
        });
    }

    /**
     * Get the validated job instance
     */
    public function getValidatedJob(): ?Job
    {
        return $this->input('_validated_job');
    }

    /**
     * Get the validated resumes collection
     */
    public function getValidatedResumes(): ?\Illuminate\Database\Eloquent\Collection
    {
        return $this->input('_validated_resumes');
    }

    /**
     * Get custom attributes for error messages
     */
    public function attributes(): array
    {
        return [
            'job_slug' => 'job',
            'resume_ids' => 'selected resumes',
            'resume_ids.*' => 'resume',
            'constraints.prefer_ats' => 'ATS preference',
            'constraints.summary_length' => 'summary length',
            'constraints.max_bullets_per_role' => 'bullet points per role',
            'constraints.emphasize_keywords' => 'keywords to emphasize',
            'constraints.emphasize_keywords.*' => 'keyword',
            'idempotency_key' => 'idempotency key',
        ];
    }

    /**
     * Handle a failed validation attempt
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        // Log validation failures for monitoring
        \Illuminate\Support\Facades\Log::info('AI tailor run validation failed', [
            'user_id' => auth('job_seeker')->id(),
            'errors' => $validator->errors()->toArray(),
            'job_slug' => $this->input('job_slug'),
            'resume_ids' => $this->input('resume_ids'),
            'constraints' => $this->input('constraints'),
            'idempotency_key' => $this->input('idempotency_key'),
        ]);

        parent::failedValidation($validator);
    }
}

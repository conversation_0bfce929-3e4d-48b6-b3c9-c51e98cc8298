<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Define supported locales
        $supportedLocales = ['ar', 'en', 'ms'];
        $defaultLocale = config('app.locale', 'en');
        
        // Get the first segment of the URL path
        $segments = $request->segments();
        $urlLocale = !empty($segments) ? $segments[0] : null;
        
        // Determine the locale to use
        $locale = $defaultLocale;
        
        // Priority 1: Check if URL contains a supported locale
        if (in_array($urlLocale, $supportedLocales)) {
            $locale = $urlLocale;
            // Store in session for persistence
            Session::put('locale', $locale);
        }
        // Priority 2: Check session
        elseif (Session::has('locale') && in_array(Session::get('locale'), $supportedLocales)) {
            $locale = Session::get('locale');
        }
        
        // Set the application locale
        App::setLocale($locale);
        
        // Also set the config for consistency with WidgetsController
        config(['app.locale' => $locale]);

        return $next($request);
    }
} 
<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Console\Commands;

use Illuminate\Console\Command;
use Mo<PERSON>les\JobSeeker\Services\JobService;
use Illuminate\Support\Facades\Log;

final class NotifyJobSeekersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobseeker:notify-job-seekers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notify job seekers about new job opportunities based on their notification frequency settings';

    /**
     * @var JobService
     */
    protected $jobService;

    /**
     * Create a new command instance.
     *
     * @param JobService $jobService
     */
    public function __construct(JobService $jobService)
    {
        parent::__construct();
        $this->jobService = $jobService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info('Starting job notification process for job seekers...');
        
        try {
            // Use the JobService to notify job seekers
            $result = $this->jobService->notifyJobSeekers();
            
            $this->info("Job notification process completed.");
            $this->info("Dispatched jobs for {$result['setup_jobs_dispatched']} setups belonging to {$result['total_job_seekers']} job seekers.");
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Error during job seeker notifications: " . $e->getMessage());
            Log::error('Error during job seeker notifications via command', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return Command::FAILURE;
        }
    }
} 
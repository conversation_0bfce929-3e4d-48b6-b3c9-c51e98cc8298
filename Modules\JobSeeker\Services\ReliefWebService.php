<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\JobSeeker\Repositories\JobRepository;
use Mo<PERSON><PERSON>\JobSeeker\Repositories\FilterRepository;
use Carbon\Carbon;
use App\Services\EmailService;
use Illuminate\Support\Facades\DB;
use Modules\JobSeeker\Services\JobNotificationService;
use Modules\JobSeeker\Entities\JobCategory;
use Modules\JobSeeker\Entities\ProviderJobCategory;
use Modules\JobSeeker\Services\SystemErrorNotificationService;
use Modules\JobSeeker\Services\MissedJobService;
use Mo<PERSON><PERSON>\JobSeeker\Contracts\JobProviderInterface;

/**
 * ReliefWebService handles fetching and processing humanitarian jobs from ReliefWeb API.
 * 
 * Purpose: Integrates ReliefWeb as a job provider into the ITQAN job aggregation platform,
 * focusing specifically on Afghanistan humanitarian job opportunities.
 * Side effects: Writes jobs table, creates provider category mappings, sends notifications.
 * Errors: Logs API failures, invalid data, network issues; returns structured stats for health monitoring.
 * Dependencies: ReliefWeb API, provider category mappings, JobNotificationService integration.
 * Performance: Implements rate limiting, efficient pagination, and caching for category mappings.
 */
final class ReliefWebService implements \Modules\JobSeeker\Contracts\JobProviderSyncInterface, JobProviderInterface
{
    /**
     * Run-context identifiers for end-to-end log correlation
     */
    protected ?string $traceId = null;
    protected ?int $executionId = null;
    
    protected JobRepository $jobRepository;
    protected FilterRepository $filterRepository;
    protected EmailService $emailService;
    protected SystemErrorNotificationService $errorNotificationService;
    protected MissedJobService $missedJobService;
    
    /**
     * ReliefWeb API configuration
     */
    protected string $apiUrl = 'https://api.reliefweb.int/v2/jobs';
    protected string $appName = 'itqan-job-aggregation-platform';
    private const MAX_REQUESTS_PER_DAY = 1000;
    private const MAX_ENTRIES_PER_REQUEST = 1000;

    public function __construct(
        JobRepository $jobRepository,
        FilterRepository $filterRepository,
        EmailService $emailService,
        SystemErrorNotificationService $errorNotificationService,
        MissedJobService $missedJobService
    ) {
        $this->jobRepository = $jobRepository;
        $this->filterRepository = $filterRepository;
        $this->emailService = $emailService;
        $this->errorNotificationService = $errorNotificationService;
        $this->missedJobService = $missedJobService;
    }

    public function setRunContext(string $traceId, ?int $executionId = null, ?int $scheduleRuleId = null): void
    {
        $this->traceId = $traceId;
        $this->executionId = $executionId;
    }

    /**
     * Execute a provider sync and trigger aggregation-based notifications.
     */
    public function syncAndAggregate(?array $providerCategoryIdentifiers = null, ?int $scheduleRuleId = null): array
    {
        return $this->fetchAndNotifyJobs($providerCategoryIdentifiers, $scheduleRuleId);
    }

    /**
     * Fetch and process jobs from ReliefWeb API with centralized notification integration.
     */
    public function fetchAndNotifyJobs(?array $categoryIds = null, ?int $scheduleRuleId = null): array
    {
        $startTime = microtime(true);
        $stats = $this->initializeStats();
        
        try {
            Log::info('ReliefWeb job synchronization started', [
                'category_ids' => $categoryIds,
                'schedule_rule_id' => $scheduleRuleId,
                'trace_id' => $this->traceId,
                'execution_id' => $this->executionId,
            ]);

            // Fetch jobs from ReliefWeb API
            $apiJobs = $this->fetchJobsFromApi($categoryIds);
            $stats['jobs_fetched'] = count($apiJobs);
            $stats['api_response_time'] = microtime(true) - $startTime;

            // Process and normalize jobs
            $processedJobs = $this->processJobs($apiJobs, $stats);
            
            // Call centralized notification service
            $scheduleContext = [
                'schedule_rule_id' => $scheduleRuleId,
                'command_name' => 'jobseeker:sync-reliefweb-jobs',
                'provider' => 'reliefweb'
            ];

            $notificationService = app(JobNotificationService::class);
            $notificationResult = $notificationService->notifyAggregatedJobs(
                $processedJobs['new'] ?? [],
                $processedJobs['updated'] ?? [],
                $processedJobs['missed'] ?? [],
                $scheduleContext
            );

            Log::info('ReliefWeb notification service result', [
                'notification_result' => $notificationResult,
                'new_jobs_count' => count($processedJobs['new'] ?? []),
                'updated_jobs_count' => count($processedJobs['updated'] ?? []),
                'missed_jobs_count' => count($processedJobs['missed'] ?? []),
                'trace_id' => $this->traceId,
            ]);

            $stats['success'] = true;
            $stats['execution_time_seconds'] = microtime(true) - $startTime;

            Log::info('ReliefWeb job synchronization completed', [
                'stats' => $stats,
                'trace_id' => $this->traceId,
            ]);

        } catch (Exception $e) {
            $stats['success'] = false;
            $stats['errors']++;
            $stats['error_types']['SYNC_FAILED'] = ($stats['error_types']['SYNC_FAILED'] ?? 0) + 1;
            $stats['execution_time_seconds'] = microtime(true) - $startTime;

            Log::error('ReliefWeb job synchronization failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'stats' => $stats,
                'trace_id' => $this->traceId,
            ]);

            // Wrap notifier call to prevent hiding original error
            try {
                $this->errorNotificationService->reportJobFetchFailure(
                    $this->getProviderName(),
                    'Failed to sync jobs from provider',
                    [
                        'error' => $e->getMessage(),
                        'short_error' => substr($e->getMessage(), 0, 200),
                        'trace_id' => $this->traceId,
                        'execution_id' => $this->executionId ?? null,
                        'stats' => $stats,
                        'timestamp' => now()->toISOString()
                    ],
                    $e
                );
            } catch (\Throwable $notifierException) {
                Log::error('ReliefWebService: Failed to send error notification', [
                    'original_error' => $e->getMessage(),
                    'notifier_error' => $notifierException->getMessage(),
                    'trace_id' => $this->traceId
                ]);
            }
        }

        return $stats;
    }

    /**
     * Fetch jobs from ReliefWeb API with pagination and filtering.
     */
    protected function fetchJobsFromApi(?array $categoryIds = null): array
    {
        $allJobs = [];
        $offset = 0;
        $limit = 100;
        $maxPages = 10;
        $page = 0;

        Log::debug('ReliefWeb API fetch started', [
            'category_ids' => $categoryIds,
            'trace_id' => $this->traceId,
        ]);

        do {
            $params = [
                'appname' => $this->appName,
                'preset' => 'latest',
                'limit' => $limit,
                'offset' => $offset,
                'fields' => [
                    'include' => [
                        'id', 'title', 'body', 'body-html', 'url', 'date.created', 'date.closing',
                        'country', 'city', 'source', 'career_categories', 'theme', 'type', 'experience',
                        'how_to_apply', 'how_to_apply-html', 'status'
                    ]
                ]
            ];

            // AFGHANISTAN-SPECIFIC FILTERING: Always filter for Afghanistan jobs (country ID: 13)
            $params['filter'] = [
                'field' => 'country.id',
                'value' => 13 // Afghanistan
            ];

            // Add category filter if specified
            if (!empty($categoryIds)) {
                $params['filter'] = [
                    'operator' => 'AND',
                    'conditions' => [
                        [
                            'field' => 'country.id',
                            'value' => 13
                        ],
                        [
                            'field' => 'career_categories.id',
                            'value' => $categoryIds,
                            'operator' => 'OR'
                        ]
                    ]
                ];
            }

            Log::debug('ReliefWeb API request', [
                'url' => $this->apiUrl,
                'params' => $params,
                'page' => $page + 1,
                'trace_id' => $this->traceId,
            ]);

            $response = Http::timeout(30)
                ->retry(3, 1000)
                ->get($this->apiUrl, $params);

            if (!$response->successful()) {
                throw new Exception("ReliefWeb API request failed: " . $response->status() . " - " . $response->body());
            }

            $data = $response->json();
            
            if (!isset($data['data']) || !is_array($data['data'])) {
                throw new Exception("Invalid ReliefWeb API response structure");
            }

            $pageJobs = $data['data'];
            $allJobs = array_merge($allJobs, $pageJobs);

            Log::debug('ReliefWeb API response', [
                'page_jobs' => count($pageJobs),
                'total_jobs' => count($allJobs),
                'total_count' => $data['totalCount'] ?? 0,
                'page' => $page + 1,
                'trace_id' => $this->traceId,
            ]);

            // Check if we have more pages
            $hasMore = count($pageJobs) === $limit && $page < $maxPages;
            $offset += $limit;
            $page++;

        } while ($hasMore);

        return $allJobs;
    }

    /**
     * Process and normalize jobs from ReliefWeb API format to internal format.
     */
    protected function processJobs(array $apiJobs, array &$stats): array
    {
        $processedJobs = ['new' => [], 'updated' => [], 'missed' => []];
        
        foreach ($apiJobs as $apiJob) {
            try {
                if (!$this->validateJobData($apiJob)) {
                    $stats['errors']++;
                    $stats['error_types']['INVALID_DATA'] = ($stats['error_types']['INVALID_DATA'] ?? 0) + 1;
                    continue;
                }

                $jobData = $this->normalizeJobData($apiJob);
                $fields = $apiJob['fields'];

                // Use JobRepository to handle creation/updating with proper deduplication
                $job = $this->jobRepository->createOrUpdate($jobData);
                
                // Build the notification payload with provider category IDs (DB IDs, not external identifiers)
                $providerIdentifiers = $this->extractProviderCategoryIds($fields);
                $providerCategoryIds = $this->mapProviderIdentifiersToIds($providerIdentifiers);

                // Persist provider category mappings on the pivot for consistency
                if (!empty($providerCategoryIds) && method_exists($job, 'providerCategories')) {
                    try {
                        $job->providerCategories()->syncWithoutDetaching($providerCategoryIds);
                    } catch (\Throwable $e) {
                        Log::warning('ReliefWebService: Failed to attach provider categories to job', [
                            'job_id' => $job->id,
                            'provider_category_ids' => $providerCategoryIds,
                            'error' => $e->getMessage(),
                            'trace_id' => $this->traceId,
                        ]);
                    }
                }

                $notificationPayload = $this->buildJobPayloadForNotification($job, $providerCategoryIds);
                
                if ($job->wasRecentlyCreated) {
                    $processedJobs['new'][] = $notificationPayload;
                    $stats['created']++;
                } else {
                    $processedJobs['updated'][] = $notificationPayload;
                    $stats['updated']++;
                }

            } catch (Exception $e) {
                Log::error('Error processing ReliefWeb job', [
                    'job_id' => $apiJob['id'] ?? 'unknown',
                    'error' => $e->getMessage(),
                    'trace_id' => $this->traceId,
                ]);
                $stats['errors']++;
                $stats['error_types']['PROCESSING_ERROR'] = ($stats['error_types']['PROCESSING_ERROR'] ?? 0) + 1;
            }
        }

        return $processedJobs;
    }

    /**
     * Validate ReliefWeb job data structure.
     */
    protected function validateJobData(array $apiJob): bool
    {
        // Check basic structure
        if (!isset($apiJob['id']) || !isset($apiJob['fields'])) {
            return false;
        }

        $fields = $apiJob['fields'];
        
        // Check required fields
        if (!isset($fields['title']) || empty($fields['title'])) {
            return false;
        }

        return true;
    }

    /**
     * Normalize ReliefWeb job data to internal format.
     */
    protected function normalizeJobData(array $apiJob): array
    {
        $fields = $apiJob['fields'];
        
        return [
            'slug' => Str::slug($fields['title'] ?? '') . '-' . $apiJob['id'],
            'source' => 'ReliefWeb',
            'position' => $fields['title'] ?? '',
            'company_name' => $this->extractCompanyName($fields),
            'about_company' => $this->extractCompanyDescription($fields),
            'description' => $fields['body'] ?? '',
            'locations' => $this->extractLocations($fields),
            'publish_date' => $this->parseDate($fields['date']['created'] ?? null),
            'expire_date' => $this->parseDate($fields['date']['closing'] ?? null),
            'submission_guideline' => $fields['how_to_apply'] ?? null,
            'external_id' => (string) $apiJob['id'],
            'raw_data' => json_encode($apiJob),
        ];
    }

    /**
     * Extract company name from ReliefWeb job data.
     */
    protected function extractCompanyName(array $fields): string
    {
        if (isset($fields['source']) && is_array($fields['source'])) {
            foreach ($fields['source'] as $source) {
                if (isset($source['name'])) {
                    return $source['name'];
                }
            }
        }
        return 'Unknown Organization';
    }

    /**
     * Extract company description from ReliefWeb job data.
     */
    protected function extractCompanyDescription(array $fields): ?string
    {
        if (isset($fields['source']) && is_array($fields['source'])) {
            foreach ($fields['source'] as $source) {
                if (isset($source['longname'])) {
                    return $source['longname'];
                }
            }
        }
        return null;
    }

    /**
     * Extract location information from ReliefWeb job data.
     */
    protected function extractLocations(array $fields): string
    {
        $locations = [];
        
        if (isset($fields['country']) && is_array($fields['country'])) {
            foreach ($fields['country'] as $country) {
                if (isset($country['name']) && $country['name'] === 'Afghanistan') {
                    $locations[] = $country['name'];
                    break; // We only want Afghanistan
                }
            }
        }
        
        return implode(', ', array_filter($locations));
    }

    /**
     * Extract provider category IDs from ReliefWeb job fields.
     */
    protected function extractProviderCategoryIds(array $fields): array
    {
        $providerCategoryIds = [];
        
        if (isset($fields['career_categories']) && is_array($fields['career_categories'])) {
            foreach ($fields['career_categories'] as $category) {
                if (isset($category['id'])) {
                    $providerCategoryIds[] = (string) $category['id']; // external identifiers from ReliefWeb
                }
            }
        }
        
        return $providerCategoryIds;
    }

    /**
     * Translate ReliefWeb external provider identifiers to our DB provider category IDs.
     */
    protected function mapProviderIdentifiersToIds(array $providerIdentifiers): array
    {
        if (empty($providerIdentifiers)) {
            return [];
        }

        $rows = ProviderJobCategory::where('provider_name', 'reliefweb')
            ->whereIn('provider_identifier', $providerIdentifiers)
            ->pluck('id')
            ->toArray();

        return array_map('intval', $rows);
    }

    /**
     * Parse date from ReliefWeb format to Laravel date format.
     */
    protected function parseDate(?string $dateString): ?string
    {
        if (!$dateString) {
            return null;
        }

        try {
            return Carbon::parse($dateString)->toDateString();
        } catch (Exception $e) {
            Log::warning('Failed to parse ReliefWeb date', [
                'date_string' => $dateString,
                'error' => $e->getMessage(),
                'trace_id' => $this->traceId,
            ]);
            return null;
        }
    }

    /**
     * Initialize statistics array for tracking.
     */
    protected function initializeStats(): array
    {
        return [
            'success' => false,
            'jobs_fetched' => 0,
            'created' => 0,
            'updated' => 0,
            'errors' => 0,
            'skipped' => 0,
            'processed' => 0,
            'api_response_time' => 0,
            'execution_time_seconds' => 0,
            'jobs_by_category' => [],
            'error_types' => [],
            'categories_processed' => 0,
        ];
    }

    /**
     * Build job payload for notification system.
     */
    protected function buildJobPayloadForNotification(object $job, array $providerCategoryIds): array
    {
        return [
            'id' => $job->id,
            'position' => $job->position,
            'company_name' => $job->company_name,
            'description' => $job->description,
            'locations' => $job->locations,
            'publish_date' => $job->publish_date,
            'expire_date' => $job->expire_date,
            'source' => $job->source,
            'provider_category_ids' => $providerCategoryIds, // CRITICAL: For category mapping
        ];
    }

    /**
     * Format execution statistics for health monitoring.
     */
    public function formatExecutionStats(array $stats): array
    {
        return [
            'jobs_fetched' => $stats['jobs_fetched'] ?? 0,
            'jobs_by_category' => $stats['jobs_by_category'] ?? [],
            'error_type' => empty($stats['error_types']) ? null : array_keys($stats['error_types'])[0],
            'error_details' => $stats['error_types'] ?? [],
        ];
    }

    public function getProviderName(): string
    {
        return 'ReliefWeb';
    }

    public function getProviderCategories(): array
    {
        return ProviderJobCategory::where('provider_name', 'reliefweb')
            ->select('name', 'provider_identifier')
            ->get()
            ->toArray();
    }

}
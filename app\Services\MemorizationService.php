<?php

namespace App\Services;

use App\Models\Classes;
use App\Models\Student;
use Carbon\Carbon;

class MemorizationService
{
    /**
     * Retrieve the class with its students and their memorization reports/plans.
     */
    public function calculateClassMemorization(int $classId, Carbon $date)
    {
        // Eager load students with the relevant memorization relationships filtered by month/year.
        return \App\Classes::with(['students' => function($query) use ($date) {
            $query->with([
                'ijazasanad_memorization_plans' => function($q) use ($date) {
                    $q->whereMonth('created_at', $date->month)
                        ->whereYear('created_at', $date->year);
                },
                'ijazaMemorizationReport' => function($q) use ($date) {
                    $q->whereMonth('created_at', $date->month)
                        ->whereYear('created_at', $date->year);
                }
            ]);
        }])->findOrFail($classId);
    }

    /**
     * Calculate the memorization progress for a given student.
     */
    public function calculateProgress($student, Carbon $date)
    {
        // Find the active plan for the student during the given month.
        $plan = $student->ijazasanadMemorizationPlans
            ->where('start_date', '<=', $date)
            ->where('end_date', '>=', $date)
            ->first();

        if (!$plan || $plan->target_pages == 0) {
            return 0;
        }

        $completed = $student->ijazaMemorizationReport
            ->whereBetween('created_at', [$date->copy()->startOfMonth(), $date->copy()->endOfMonth()])
            ->sum('completed_pages');

        return ($completed / $plan->target_pages) * 100;
    }

    /**
     * Calculate revision progress for the entire class.
     *
     * This method loads the class with its students and the revision plans/reports.
     * Adjust the field names and logic to match your actual revision structure.
     */
//    public function calculateClassRevision(int $classId, Carbon $date)
//    {
//        $class = \App\Classes::with(['students' => function($query) use ($date) {
//            $query->with([
//                'ijazasanadRevisionPlans' => function($q) use ($date) {
//                    $q->whereMonth('created_at', $date->month)
//                        ->whereYear('created_at', $date->year);
//                },
//                'ijazaRevisionReport' => function($q) use ($date) {
//                    $q->whereMonth('created_at', $date->month)
//                        ->whereYear('created_at', $date->year);
//                }
//            ]);
//        }])->findOrFail($classId);
//
//        // For each student, calculate the revision percentage.
//        $totalPercentage = 0;
//        $studentCount = 0;
//        foreach ($class->students as $student) {
//            // Get the active revision plan for this student.
//            $plan = $student->ijazasanadRevisionPlans
//                ->where('start_date', '<=', $date)
//                ->where('end_date', '>=', $date)
//                ->first();
//
//            if (!$plan || $plan->target_lessons == 0) {
//                continue;
//            }
//
//            // Sum up completed lessons from the revision reports.
//            $completed = $student->ijazaRevisionReport
//                ->whereBetween('created_at', [$date->copy()->startOfMonth(), $date->copy()->endOfMonth()])
//                ->sum('completed_lessons'); // adjust the field name if needed
//
//            $percentage = ($completed / $plan->target_lessons) * 100;
//            $totalPercentage += $percentage;
//            $studentCount++;
//        }
//
//        return $studentCount > 0 ? $totalPercentage / $studentCount : 0;
//    }
}

<?php

namespace Modules\HumanResource\Http\Controllers;

use App\CenterTranslation;
use App\EmployeeSalary;
use App\LeaveDefine;
use App\LeaveType;
use App\Organization;
use App\Student;
use Carbon\Traits\Creator;
use Doctrine\DBAL\Cache\CacheException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Jenssegers\Agent\Agent;

use App\Employee;

use App\Authorizable;

use App\Attendance;
use Carbon\Carbon;
use App\LeaveRequest;
use App\Role;


class IndividualEmployeeMonthlyAttendanceController extends Controller
{
    // use Authorizable;
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {

        dd('reached new controller');

//        if (auth()->guard('employee')->user()->hasRole('supervisor_2_'))
//        {
//            dd(auth()->guard('employee')->user()->with('center')->first());
//            auth()->guard('employee')->user()->where('votes', '>', 100);
//        }


        // return ;
        // if (!auth()->user()->hasRole('enterprise') && ! auth()->user()->hasRole('finance-manager_1_')) {
        if (!auth()->user()->can('access attendance')) {
            return redirect('/workplace/humanresource/attendance/' . auth()->user()->id);
            $result = [auth()->user()];
        } else {
            $result = Employee::latest()->paginate();
        }

        return view('humanresource::attendance.list', compact('result'));
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        $roles = Role::pluck('description', 'name');

        return view('humanresource::employees.create', compact('roles'));
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // hash password
        $request->merge(['organization_id' => config('organization_id')]);

        if ($request->ajax()) {


            // self clock in/out
            $last_attendance_record = Attendance::where('employee_id', auth()->user()->id)->latest()->first();

            if ($last_attendance_record && $last_attendance_record->clock->format('Y-m-d') == date('Y-m-d')) {
                $type = $last_attendance_record->type == 'in' ? 'out' : 'in';
            } else {
                $type = 'in';
            }


//            if($type == 'out') {
//
//                $inTime = Carbon::parse($last_attendance_record->date)->format('Y-m-d H:i:s');
//
//                $outTime = Carbon::now('Asia/Kuala_Lumpur');
//
//
//                $outTimeLessThanIn = $outTime->lt($inTime);
//                $outTimeEqIn = $outTime->equalTo($inTime);
//
//
//                if ($outTimeEqIn == true) {
//
//                    return response()->json('Please be present here for few seconds', 422);
//
//                }
//            }


            $agent = new Agent();
            $attendance = Attendance::create([
                'employee_id' => auth()->user()->id,
                'organization_id' => config('organization_id'),
                'clock' => date('Y-m-d H:i:s'),
                'type' => $type,
//                'location' => $request->location ?? 'NOT',
                'location' => 'currently disabled as per requested by HR department',
                'device' => $agent->device(),
                'ip' => $request->ip(),
                'note' => $request->note,
                'created_by' => auth()->user()->id,
            ]);

//            return response()->json(['status' => 'success', 'type' => ucfirst($type == "in" ? "Clock Out" : "Clock In"), 'clock' => $attendance->clock->format('d/M/Y g:i a')], 200);
//            return response()->json(['status' => 'success', 'nextType' => ucfirst($type == "in" ? "Out" : "In"), 'clock' => $attendance->clock->format('d/M/Y g:i a')], 200);
            return response()->json(['status' => 'success', 'nextType' => ucfirst($type == "in" ? "Out" : "In"), 'clock' => $attendance->clock->diffForHumans()], 200);
        }
        // add record by admin

        return redirect()->route('employees.index');
    }

    public function storeInRecord(Request $request)
    {
        // hash password
        $request->merge(['organization_id' => config('organization_id')]);

        if ($request->ajax()) {

            try {

                \DB::beginTransaction();


                if (!auth()->user()->can('add attendance')) {
                    return response()->json(['error' => 'not_autorized'], 300);
                }


                if (!$request->exists('in') && $request->exists('out')) {
                    return response()->json('Please complete the details', 422);
                }


                $inTime = Carbon::parse($request->get('in'));
                $inNote = $request->get('inNote');


                $agent = new Agent();
                $inAttendance = Attendance::create([
                    'employee_id' => $request->get("employee_id"),
                    'organization_id' => config('organization_id'),
                    'clock' => $inTime,
                    'type' => 'in',
                    'location' => $request->location ?? 'NOT',
                    'device' => $agent->device(),
                    'ip' => $request->ip(),
                    'note' => $inNote,
                    'created_by' => auth()->user()->id,
                ]);

                \DB::commit();
                return response()->json(['status' => 'success'], 200);

            } catch (\Exception $exception) {
                \Log::error($exception);
                \DB::rollBack();

                $errorMessage = $exception->getMessage();
                return response()->json(compact('errorMessage'));
            }
        }
        // add record by admin

        return redirect()->route('employees.index');
    }

    /**
     * @param Request $request
     * @return JsonResponse|RedirectResponse
     * @throws \Throwable
     *  IT IS USED BY hr PERSONNEL TO UPDATE THE EMPLOYEE TIME IN AND TIMEOUT
     */
    public function hrstoreInRecord(Request $request)
    {

        $request->merge(['organization_id' => config('organization_id')]);

        if ($request->ajax()) {
            try {
                DB::beginTransaction();

                if (!auth()->user()->can('add attendance')) {
                    return response()->json(['error' => 'not_authorized'], 300);
                }

                if (!$request->exists('in')) {
                    return response()->json('Please complete the details', 422);
                }

                $inTime = Carbon::parse($request->get('in'));

                $agent = new Agent();
                $attendance = Attendance::find($request->get('attendance_id'));
                if ($attendance) {
                    $attendance->update([
                        'clock' => $inTime,
                        'device' => $agent->device(),
                        'ip' => $request->ip(),
                    ]);

                    DB::commit();
                    return response()->json(['status' => 'success'], 200);
                } else {
                    return response()->json(['error' => 'Attendance record not found'], 404);
                }

            } catch (\Exception $exception) {
                \Log::error($exception);
                DB::rollBack();

                $errorMessage = $exception->getMessage();
                return response()->json(compact('errorMessage'));
            }
        }

        return redirect()->route('employees.index');
    }
    public function hrstoreOutRecord(Request $request)
    {
        $request->merge(['organization_id' => config('organization_id')]);

        if ($request->ajax()) {
            try {
                DB::beginTransaction();

                if (!auth()->user()->can('add attendance')) {
                    return response()->json(['error' => 'not_authorized'], 300);
                }

                if (!$request->exists('out')) {
                    return response()->json('Please complete the details', 422);
                }

                $outTime = Carbon::parse($request->get('out'));
                $agent = new Agent();
                $attendance = Attendance::find($request->get('attendance_id'));
                if ($attendance) {
                    $attendance->update([
                        'clock' => $outTime,
                        'location' => $request->location ?? 'NOT',
                        'device' => $agent->device(),
                        'ip' => $request->ip(),
                    ]);

                    DB::commit();
                    return response()->json(['status' => 'success'], 200);
                } else {
                    return response()->json(['error' => 'Attendance record not found'], 404);
                }

            } catch (\Exception $exception) {
                \Log::error($exception);
                DB::rollBack();

                $errorMessage = $exception->getMessage();
                return response()->json(compact('errorMessage'));
            }
        }

        return redirect()->route('employees.index');
    }
    public function storeOutRecord(Request $request)
    {
        // hash password
        $request->merge(['organization_id' => config('organization_id')]);

        if ($request->ajax()) {

            try {


                \DB::beginTransaction();


                if (!auth()->user()->can('add attendance')) {
                    return response()->json(['error' => 'not_autorized'], 300);
                }


                $outNote = $request->get('outNote');

                $outTime = Carbon::parse($request->get('out'));

//                $outTimeGreater = $outTime->gt($inTime);
//                $outTimeLessThanIn = $outTime->lt($inTime);
//                $outTimeEqIn = $outTime->equalTo($inTime);
//                $InTimeEqOut = $inTime->equalTo($outTime);
//
//
//                if ($outTimeLessThanIn == true) {
//
//                    return response()->json('The OUT time can not be less than the IN time', 422);
//                }if ($outTimeEqIn == true OR $InTimeEqOut == true) {
//
//                    return response()->json('The OUT time can not be equal to the IN time', 422);
//                }
//                if ($outTimeGreater == false) {
//                    return response()->json('The OUT time can not be less than the IN time . Please select greater time for the OUT field', 422);
//                }


                $agent = new Agent();


                $outAttendance = Attendance::create([
                    'employee_id' => $request->get("employee_id"),
                    'organization_id' => config('organization_id'),
                    'clock' => $outTime,
                    'type' => 'out',
                    'location' => $request->location ?? 'NOT',
                    'device' => $agent->device(),
                    'ip' => $request->ip(),
                    'note' => $outNote,
                    'created_by' => auth()->user()->id,
                ]);

                \DB::commit();
                return response()->json(['status' => 'success'], 200);

            } catch (\Exception $exception) {
                \Log::error($exception);
                \DB::rollBack();

                $errorMessage = $exception->getMessage();
                return response()->json(compact('errorMessage'));
            }
        }
    }

    public function storePairRecord(Request $request)
    {
        // hash password
        $request->merge(['organization_id' => config('organization_id')]);

        if ($request->ajax()) {

            try {


                if (!auth()->user()->can('add attendance')) {
                    return response()->json(['error' => 'not_autorized'], 300);
                }


                if (!$request->exists('in') && $request->exists('out')) {
                    return response()->json('Please complete the details', 422);
                }


                $inTime = Carbon::parse($request->get('in'));
                $inNote = $request->get('inNote');
                $outNote = $request->get('outNote');

                $outTime = Carbon::parse($request->get('out'));

                $outTimeGreater = $outTime->gt($inTime);
                $outTimeLessThanIn = $outTime->lt($inTime);
                $outTimeEqIn = $outTime->equalTo($inTime);
                $InTimeEqOut = $inTime->equalTo($outTime);


                if ($outTimeLessThanIn == true) {

                    return response()->json('The OUT time can not be less than the IN time', 422);
                }
                if ($outTimeEqIn == true or $InTimeEqOut == true) {

                    return response()->json('The OUT time can not be equal to the IN time', 422);
                }
                if ($outTimeGreater == false) {
                    return response()->json('The OUT time can not be less than the IN time . Please select greater time for the OUT field', 422);
                }


                $agent = new Agent();
                $inAttendance = Attendance::create([
                    'employee_id' => $request->get("employee_id"),
                    'organization_id' => config('organization_id'),
                    'clock' => $inTime,
                    'type' => 'in',
                    'location' => $request->location ?? 'NOT',
                    'device' => $agent->device(),
                    'ip' => $request->ip(),
                    'note' => $inNote,
                    'created_by' => auth()->user()->id,
                ]);

                $outAttendance = Attendance::create([
                    'employee_id' => $request->get("employee_id"),
                    'organization_id' => config('organization_id'),
                    'clock' => $outTime,
                    'type' => 'out',
                    'location' => $request->location ?? 'NOT',
                    'device' => $agent->device(),
                    'ip' => $request->ip(),
                    'note' => $outNote,
                    'created_by' => auth()->user()->id,
                ]);

            } catch (\Exception $exception) {
                \Log::error($exception);
                $errorMessage = $exception->getMessage();
                return response()->json(compact('errorMessage'));
            }
            return response()->json(['status' => 'success'], 200);
        }
        // add record by admin

        return redirect()->route('employees.index');
    }


    /**
     * This function handles the display of an employee's attendance report.
     * It checks if the request is an AJAX call and returns the attendance data in a JSON format.
     * If not, it renders the attendance report view with the required data.
     *
     * @param Request $request The incoming HTTP request
     * @param int $id The ID of the employee
     * @param string|null $date The date for which the attendance report is being generated
     * @throws \Exception If an error occurs while processing the request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\View\View
     */
    public function show(Request $request, $id, $date = null)
    {

        try {

            if ($request->exists('monthFilter')) {
                $date = request()->get('monthFilter');
            } else {
                $date = Carbon::today()->toDateString();
            }
            $report = $this->report($id, $date);

            // to be refactored
            extract($report);
            $employee = Employee::findOrFail($id);

//            if ($employee->work_mood == 'per_month') {


            if ($employee->work_mood == \App\EmployeeWorkMood::where('slug', 'per_month')->first()->id) {


                if (!auth()->user()->can('access attendance')) {
                    // $id = auth()->user()->id;
                }



                if (request()->ajax()) {

                    return \Yajra\DataTables\DataTables::of($daily_record)
                        ->with('totalWorkingHours', '<span style="background: #ff8664 " class="badge badge-info">' . $total_working_hours . ' Hours</span>')
                        ->with('trackPerformedHours', '<span style="background: #ff8664 " class="badge badge-info">' . $trackPerformedHours . ' Hours</span>')
                        ->with('totalRequiredWorkingHours', '<span style="background: #57a7ff ; color: #fefefe; " class="badge badge-info">' . $total_required_hours . ' Hours</span>')
                        ->with('totalMissingWorkingHours', '<span style="background: #ff8664 " class="badge badge-info">' . $total_missed_hours . ' Hours</span>')
                        ->with('totalVolunteeredHours', '<span style="background: #ff8664 " class="badge badge-info">' . $total_volunteered_hours . ' Hours</span>')
                        ->with('totalSickLeaves', '<span style="background: #ff8664 " class="badge badge-info">' . $total_sick_leaves . '</span>')
                        ->addIndexColumn()
                        ->addColumn('date', function ($record) use ($daily_record) {
                            return $record['date'];
                        })
                        ->addColumn('dayandDate', function ($record) use ($daily_record) {


                            return '<span style="text-transform: capitalize">' . $record['day'] . '<br>' . $record['date'] . '</span>';
//                    return $record['date'];

                        })
                        ->addColumn('insandouts', function ($drecord) use ($daily_record) {


                            $str = '';


                            foreach ($drecord['attendance'] as $attendance) {


                                $str .= '<div class="ui equal width center aligned padded grid" style="height: 37px;">';

                                if (isset($attendance['in'])) {

                                    $inNote = $attendance['in']->note;
                                    $time = $attendance['in']->clock->format('h:iA');
                                    $inNoteStar = $attendance['in']->note == true ? '*' : '';
                                    $str .= '  <div class="four wide column attendance-record">
    <div class="ui input">
    <label for="attendanceTime" class="ui basic button">
            <small class="label label-success" style="border-radius: 6px;">in</small>
            
        </label>
        <input style=" padding-right: 1px;" class="attendanceTimeIn" type="text" id="attendanceTimeIn" placeholder="Select Time" data-tooltip="' . $inNote . '" data-value="'. $time .'" data-attendance-id="' . $attendance['in']->id . '" data-inverted="" data-position="top left">
       
    </div>
</div>';
                                }
                                else {
                                    $str .= '<div class="four wide column attendance-record "><button type="button" class="ui  basic button  " style="    width: 80px;" data-inverted="">
                                                   <small class="label label-warning" style="border-radius: 6px;bottom: -9px;"></small>
                                                   <div class="clock-note">
                                                     
                                                   </div>

                                               </button></div>';
                                }
                                if (isset($attendance['out'])) {
                                    $outNote = $attendance['out']->note;
                                    $time = $attendance['out']->clock->format('h:iA');
                                    $outNoteStar = $attendance['out']->note == true ? '*' : '';

                                    $str .= '<div class="four wide column attendance-record">
    <div class="ui input">
    <label for="attendanceTimeOut" class="ui basic button" >
            <small class="label label-danger" style="border-radius: 6px;">out</small>
         
        </label>
        <input class="attendanceTimeOut" type="text" id="attendanceTimeOut" placeholder="Select Time" data-tooltip="' . $outNote . '" data-value="' . $time . '" data-attendance-id="' . $attendance['out']->id . '" data-inverted="" data-position="top left">
        
    </div>
</div>';


                                }
                                else {

                                    $width = count($drecord['attendance']) > 1 ? '218px' : '80px'; // > 1 because each one contains a pair of in and out

                                    $str .= '<div class="four wide column attendance-record "><button type="button" class="ui  basic button  " style="width: ' . $width . ';" data-inverted="" data-position="top left" data-tooltip="employee is either working or forgot to punch out">
                                                   <small class="label label-warning" style="border-radius: 6px;bottom: -9px; color:black">Working / Forgot</small>
                                                   <div class="clock-note">
                                                     
                                                   </div>

                                               </button></div>';

                                }

                                if (isset($attendance['duration_in_hours'])) {

//                            $str .= ' <div class="four wide column" style="right: 63px;">
                                    $str .= ' <div class="four wide column" >
 <a class="ui green label" style="border-radius: 6px;">duration</a>' . $attendance["duration_in_hours"];
                                }

                                $str .= '</div></div></div>';

                            }


                            return $str;


                        })
                        ->addColumn('action', function ($record) use ($daily_record, $id) {


//                            $dateString = $record['date']; // Assuming $record['date'] has the value "2024-08-01"
//                            $dateString = "2024-08-25";
//                            $date = Carbon::parse($dateString);
//
//                            $totalHours = calculateDailyHours($id, $date);
//                            dd($totalHours);

                            $collection = collect($record['attendance']);

                            $total = $collection->reduce(function ($carry, $item) {


                                $hoursWorked = str_replace(':', '.', $item['duration_in_hours']);

                                return $carry + $hoursWorked;
                                return $carry + $hoursWorked;
                            });


                            $humanFriendlyHoursMinutes = collect($total)->map(function ($item, $key) {
                                $hoursWorked = explode('.', $item);

                                return $hoursWorked[0] . ' hours ' . $hoursWorked[1] . ' minutes';
                            });



                            $deletebtn = '<div class="or"></div><button  data-date="' . $record['date'] . '" class="ui button blue deleteModalTriggerBtn" id="deleteModalTriggerBtn" data-catid = ' . $record['attendance'] . ' data-toggle = "modal"
                                                                                                        data-target = "#delete" >Delete</button>';


                            $rh = $record["required_hours"] ?? 0;
                            $volunteeredHours = 0;


                            if ($total > $record["required_hours"]) {
                                $volunteeredHours = $total - $record["required_hours"];


                                if ($rh == 0 ) {

                                    $str = '<div class="ui list">
                      <div class="item">Required: ' . $rh . '</div>
                      <div class="item" title="' . $humanFriendlyHoursMinutes[0] . '" >Volunteer: ' . $volunteeredHours . '</div>
                     
                    </div></div><br>';

                                } else {



                                    $str = '<div class="ui list">
                      <div class="item">Required: ' . $rh . '</div>
                      <div class="item" title="' . $humanFriendlyHoursMinutes[0] . '" >Done: ' . $total . '</div>
                      <div class="item"  >Volunteer: ' . $volunteeredHours . '</div>
                     
                    </div></div><br>';
                                }


                            } else {
                                $str = '<div class="ui list">
                      <div class="item">Required: ' . $rh . '</div>';


                                if ($rh > 0) {

                                    if($total > 0)
                                    {
                                        $totalHours = $record["required_hours"] == true ? $total : $total . ' hours';

                                        $str .= '<div class="item" title="' . $humanFriendlyHoursMinutes[0] . '" >Done: ' . $total . '</div>
                      <div class="item"  >Volunteer: ' . $volunteeredHours . ' </div>
                     
                    </div></div><br>';

                                    }

                                }


                            }

                            if (\Auth::user()->can('update attendance')) {

                                $approvedLeaveRequests = LeaveRequest::where('employee_id', $id)->where('approve_status', 'A')->whereDate('leave_from', $record['date']);

                                if (!$approvedLeaveRequests->exists()) {

                                    $sickLeave = LeaveType::where('type', 'Sick Leave')->with('leaveDefine')->first();

                                    $leaveDefine = LeaveDefine::where('leave_type_id', $sickLeave->id)->where('employee_id', $id)->first();

                                    $sickLeaveBtn = '';

                                    $sickLeaveBtn = '
                                        <div class="or"></div>
                                        </div>
                                        <button 
                                        data-apply_date="' . $record['date'] . '" 
                                        data-leave_type="' . $sickLeave->id . '" 
                                        data-role_id="' . $leaveDefine->role_id . '"
                                        data-leave_define_id="' . $leaveDefine->id . '"
                                        data-leave_from="' . $record['date'] . '"
                                        data-leave_to="' . $record['date'] . '"
                                        class="ui labeled icon default button sickLeaveModalTriggerBtn" id="sickLeaveModalTriggerBtn" data-catid = ' . $record['attendance'] . ' data-toggle = "modal"
                                                                                                        data-target = "#sickLeave" ><i class="bed icon"></i>Sick</button>';

                                    $btns = '<div class="ui buttons">
                      <button class="ui button teal" data-toggle="modal" data-target="#editAttendanceModal"  data-date="' . $record['date'] . '" id="editAttendanceBtn">Edit</button>
                      ' . $deletebtn . $sickLeaveBtn;


                                } else {

                                    return '<a class="ui teal tag label">Sick Leave</a>';

                                }


                            }


//
                            return $str . $btns;
                        })
                        ->setRowClass(function ($record) use ($id) {
                            $approvedLeaveRequests = LeaveRequest::where('employee_id', $id)->where('approve_status', 'A')->whereDate('leave_from', $record['date']);

                            return $approvedLeaveRequests->exists() ? 'disabled ' : '';
                        })
                        ->rawColumns(['dayandDate', 'insandouts', 'action', 'totalWorkingHours', 'totalRequiredWorkingHours', 'totalMissingWorkingHours', 'totalVolunteeredHours'])
                        ->toJson();


                }
            }


            $first_working_month = Carbon::createFromFormat('Y - m - d', $employee->start_at);


            $roles = Role::pluck('description', 'name');


            return view(
                'humanresource::attendance.report',
                compact(
                    'roles',
                    'employee',
                    'attendance',
                    'daily_record',
                    'next_month',
                    'current_month',
                    'public_holidays',
                    'weekend',
                    'first_working_month',
                    'total_required_hours',
                    'total_working_hours'
                )
            );

        } catch
        (\Exception $e) {

            dd($e->getMessage());
            return response()->json($e->getMessage(), 500);
        }

    }

    public function daily_report(Request $request)
    {


        $employee = Employee::all();
        $role = Role::get();

        //  $attendance =Attendance::whereDate('created_at', date('Y - m - d '))->get();


        $attendance = Attendance::whereDate('created_at', date('Y-m-d '))->orderBy('employee_id');
//        $attendance = Attendance::whereDate('created_at', date('Y-m-d'))->orderBy('employee_id')->get();


        return view(
            'humanresource::attendance.daily_report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'role',
                'date'
            )
        );
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        $employee = Employee::find($id);
        $roles = Role::pluck('name', 'id');
        $permissions = Permission::all('name', 'id');

        return view('humanresource::employees . edit', compact('employee', 'roles', 'permissions'));
    }


    public function update(Request $request, $id)
    {


        if (!auth()->user()->can('update attendance')) {
            return response()->json(['error' => 'not_autorized'], 300);
        }


        if (!$request->exists('in') && $request->exists('out')) {
            return response()->json('Please complete the details', 422);
        }


        $inTime = new Carbon($request->get('in'));

        $outTime = new Carbon($request->get('out'));
        $outTimeGreater = $outTime->gt($inTime);
        $outTimeEqIn = $outTime->equalTo($inTime);
        $InTimeEqOut = $inTime->equalTo($outTime);


        if ($outTimeEqIn == true or $InTimeEqOut == true) {

            return response()->json('The OUT time can not be equal to the IN time', 422);
        }
        if ($outTimeGreater == false) {
            return response()->json('The OUT time can not be less than the IN time . Please select greater time for the OUT field', 422);
        }
        // --  validation logic ends here


        if (isset($record['in']['id'])) {
            $aRecord = Attendance::findOrFail($record['in']['id']);

            if ($aRecord->clock != $record['in']['clock']) {
                $aRecord->clock = $record['in']['clock'];
                $aRecord->type = $record['in']['type'];

                $aRecord->note = $aRecord->note . " \n Updated By " . auth()->user()->name;

                $aRecord->save();
            }
        } else {
            $agent = new Agent();


            Attendance::create([
                'employee_id' => $request->employee_id,
                'organization_id' => config('organization_id'),
                'clock' => $record['in']['clock'],
                'type' => $record['in']['type'],
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),
                'note' => 'Manual Entry',
                'created_by' => auth()->user()->id,
            ]);
        }

        if (isset($record['out']['id'])) {
            $aRecord = Attendance::findOrFail($record['out']['id']);

            if ($aRecord->clock != $record['out']['clock']) {
                $aRecord->clock = $record['out']['clock'];
                $aRecord->type = $record['out']['type'];
                $aRecord->note = $aRecord->note . "\n Updated By " . auth()->user()->name;

                $aRecord->save();
            }
        } else {
            $agent = new Agent();

            Attendance::create([
                'employee_id' => $request->employee_id,
                'organization_id' => config('organization_id'),
                'clock' => $record['out']['clock'],
                'type' => $record['out']['type'],
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),
                'note' => 'Manual Entry',
                'created_by' => auth()->user()->id,
            ]);
        }


        return $request->records;

        /*
        $this->validate($request, [
            'name' => 'bail | required | min:2',
            'email' => 'required | email | unique:employees,email,' . $id,
            'roles' => 'required | min:1'
        ]);

        // Get the user
        $user = Employee::findOrFail($id);

        // Update user
        $user->fill($request->except('roles', 'permissions', 'password'));

        // check for password change
        if ($request->get('password')) {
            $user->password = bcrypt($request->get('password'));
        }

        // Handle the user roles
        $this->syncPermissions($request, $user);

        $user->save();
        flash()->success('User has been updated . ');
        return redirect()->route('employees . index'); */
    }


    // per day deletion
    public function destroy(Request $request, $date)
    {


        Attendance::where(\DB::raw("date(clock)"), $date)->where("employee_id", $request->get("employee_id"))->delete();


        return response()->json('attendance deleted', 200);
    }


    // per entry deletion
    public function destroyIndividualRecord(Request $request, $id)
    {


        Attendance::where('id', $id)->delete();


        return response()->json('attendance deleted', 200);
    }

    private function syncPermissions(Request $request, $user)
    {
        // Get the submitted roles
        $roles = $request->get('roles', []);
        $permissions = $request->get('permissions', []);

        // Get the roles
        $roles = Role::find($roles);

        // check for current role changes
        if (!$user->hasAllRoles($roles)) {
            // reset all direct permissions for user
            $user->permissions()->sync([]);
        } else {
            // handle permissions
            $user->syncPermissions($permissions);
        }

        $user->syncRoles($roles);
        return $user;
    }

    public function mobile()
    {
        return view('humanresource::attendance . mobile');
    }


    private function calculateAttendanceDuration($attendance_list)
    {


        $attendance = [];

        $total_working_minutes = 0;

        $counter = 0;
        $steps = 1;


        foreach ($attendance_list as $item) {
            if ($item->type == 'in') {
                $counter++;
            }
            $attendance[$counter][$item->type] = $item;
        }
        $trackWorkingMinutes = [];

        foreach ($attendance as $i => $entry) {

            // count hours and minutes
            if (isset($entry['in']) && isset($entry['out'])) {
                $working_minutes = $entry['out']->clock->diffInRealMinutes($entry['in']->clock);

                $trackWorkingMinutes[] = $working_minutes;
//                $attendance[$i]['duration_in_hours'] = number_format($working_minutes / 60, 2);
                $attendance[$i]['duration_in_hours'] = intdiv($working_minutes, 60) . ':' . ($working_minutes % 60);
                $total_working_minutes += $working_minutes;
            } else {
                $attendance[$i]['duration_in_hours'] = 0;
            }
        }


        return ['attendance' => $attendance, 'duration' => $total_working_minutes, 'trackWorkingMinutes' => $trackWorkingMinutes];
    }

    public function report($id, $date)
    {


        $leave_requests = LeaveRequest::where(['employee_id' => $id, 'approve_status' => 'approved'])->get();

        // return $leave_requests;

        $on_leave = [];

        foreach ($leave_requests as $leave_request) {
            if ($leave_request->from_date->format('Y-m-d') == $leave_request->to_date->format('Y-m-d')) {
                $on_leave[$leave_request->from_date->format('Y-m-d')] = ['allowed_time' => $leave_request['allowed_time']];
            } else {
                for ($i = $leave_request->from_date; $i->format('Y-m-d') <= $leave_request->to_date->format('Y-m-d'); $i->addDay()) {
                    $on_leave[$i->format('Y-m-d')] = ['allowed_time' => $leave_request['allowed_time']];
                }
            }
        }


        // temp ;to be dynamicly loaded

        $weekend = ['Sat', 'Sun'];
        $public_holidays = ['2018-01-01', '2018-01-31'];

        // end of temp


        $report_date = false;

        if ($date) {
            $report_date = Carbon::parse($date)->format('Y-m');
        }


        if ($report_date !== false) {
            $start_date = $report_date . '-01';
        } else {
            $start_date = $date ?? date('Y-m-01');
        }


        $current_month = Carbon::createFromFormat('Y-m-d', $start_date);


        $next_month = $current_month->copy()->addMonth();

        $employee = Employee::findOrFail($id);


        /* NEW */
        $end_date = $current_month->copy()->addMonth();

        $daily_record = [];
        $total_working_minutes = 0;
        $total_required_hours = 0;
        $total_working_hours = 0;
        $total_missed_hours = 0;
        $total_volunteered_hours = 0;
        $total_sick_leaves = 0;
        $trackPerformedHours = 0;
//        $trackPerformedHours = [];
        $total_volunteer_working_hours = 0;

        $approvedLeaveRequests = LeaveRequest::where('employee_id', $id)->where('approve_status', 'A')
            ->whereMonth('leave_from', date('m'));
        if ($approvedLeaveRequests->exists()) {
            $total_sick_leaves = LeaveRequest::where('employee_id', $id)->where('approve_status', 'A')
                ->whereMonth('leave_from', date('m'))->count();
        }
        $date = Carbon::parse($date)->firstOfMonth();
        while ($date < $end_date) {
            $day = strtolower($date->format('D'));
            $record = [];
            $attendance_list = Attendance::where('employee_id', $employee->id)
                ->where('clock', '>=', $date->format('Y-m-d  00:00:00'))
                ->where('clock', '<=', $date->format('Y-m-d 23:59:59'))
//                ->orderBy('clock')
                ->orderBy('created_at')
                ->get();
            $attendance_duration = $this->calculateAttendanceDuration($attendance_list);
            $record['day'] = $day;
            $record['worked_hours'] = round(  // Round the following number to two decimal places
                floatval(  // Convert the following string to a float
                    fdiv($attendance_duration['duration'], 60) // Divide the duration by 60
                ), 2
            );
            $record['attendance'] = $attendance_duration['attendance'];
            $record['date'] = $date->format('Y-m-d');
            $required = $employee->timetable->where('day', $day)->first();
            $total_required_hours = $employee->hours_per_month;
            if ($required) {
                $countTotDaysperWeek = $employee->timetable->count();
                $daysPerMonth = $countTotDaysperWeek * 4.34;
                if ($employee->work_mood == 4/** per_month */) {



//                    if($employee->hours_per_month/$date->daysInMonth < 1){
                    if ($employee->hours_per_month / $daysPerMonth < 1) {

//                        $record['required_hours'] = round(($total_required_hours/$date->daysInMonth) * 60) .' minutes';
                        $record['required_hours'] = round(($total_required_hours / $daysPerMonth) * 60, 1) . ' minutes';

                    } else {


//                        $record['required_hours'] = round($total_required_hours/$date->daysInMonth) . ' hours';
                        $record['required_hours'] = round($total_required_hours / $daysPerMonth, 1) . ' hours';

                    }


                }


//                $trackPerformedHours[] = $record['worked_hours'];
                $trackPerformedHours = $record['worked_hours'];


//                if ($total_working_hours > $total_required_hours) {
                if ($record['worked_hours'] > $record['required_hours']) {
                    $total_working_hours += $record['required_hours'];

//                    $total_volunteer_working_hours += abs($total_working_hours - $total_required_hours);
                    $total_volunteer_working_hours += abs($record['worked_hours'] - $record['required_hours']);
                    $record['volunteered_hours'] = $total_volunteer_working_hours;
                    $total_missed_hours = 0;
                    $record['missed_hours'] = 0;

                }else{

                    $total_working_hours += $record['worked_hours'];


                }

                if ($total_working_hours < $total_required_hours) {
                    $total_volunteered_hours = 0;
                    $record['volunteered_hours'] = $total_volunteered_hours;
                    $total_missed_hours = round(abs($total_required_hours - $total_working_hours), 2);
                    $record['missed_hours'] = $total_missed_hours;

                }

            }
            // if volunteer
            if(!$required){


                $total_volunteer_working_hours += $record['worked_hours'];


                $record['volunteered_hours'] = $total_volunteer_working_hours;

            }


            $daily_record[$date->format('Y-m-d')] = $record;
            $date->addDay();

        }


        return [

            'employee' => $employee,
            'trackPerformedHours' => $trackPerformedHours,
            // 'attendance' => $attendance,
            'daily_record' => $daily_record,
            'next_month' => $next_month,
            'current_month' => $current_month,
            'public_holidays' => $public_holidays,
            'weekend' => $weekend,
            'total_required_hours' => $total_required_hours,
            'total_missed_hours' => $total_missed_hours,
//            'total_volunteered_hours' => $total_volunteered_hours,
            'total_volunteered_hours' => $total_volunteer_working_hours,
            'total_working_hours' => $total_working_hours,
            'total_sick_leaves' => $total_sick_leaves
        ];
    }

    public
    function dailyReport($id, $date)
    {


        try {


            $start_date = $date;


            $current_month = Carbon::createFromFormat('Y-m-d', $start_date);


            $next_month = $current_month;

            $employee = Employee::findOrFail($id);

            /* NEW */
            $end_date = $date;


            $daily_record = [];

            $total_working_minutes = 0;
            $total_required_hours = 0;
            $total_working_hours = 0;
            $total_missed_hours = 0;
            $total_volunteered_hours = 0;
            $trackPerformedHours = 0;


            while ($date <= $end_date) {
                $date = Carbon::parse($date);
                $day = strtolower($date->format('D'));
                $date->addDay();
                $record = [];
                $attendance_list = Attendance::whereDate('clock', $date)
                    ->orderBy('clock')
                    ->get();
                $attendance_duration = $this->calculateAttendanceDuration($attendance_list);

                $record['day'] = $day;
                $record['worked_hours'] = intdiv($attendance_duration['duration'], 60) . ':' . ($attendance_duration['duration'] % 60);
                $record['missed_hours'] = intdiv($attendance_duration['duration'], 60) . ':' . ($attendance_duration['duration'] % 60);
                $empHoursPerMonth = $employee->hours_per_month;
                $record['volunteered_hours'] = abs($empHoursPerMonth - $record['worked_hours']);
                $total_volunteered_hours = $record['volunteered_hours'];
                $record['attendance'] = $attendance_duration['attendance'];


//            if ($salary_on_this_date && $salary_on_this_date->work_mood == 'per_hour') {
                $total_required_hours = $employee->hours_per_month;

//            }

                if ($employee->timetable) {
                    $required = $employee->timetable->where('day', $day)->first();


                    if ($required) {
                        $trackPerformedHours[] = $record['worked_hours'];
                        $total_working_hours += $record['worked_hours'];
                        $total_missed_hours = abs($total_required_hours - $total_working_hours);


                        if ($employee->work_mood == 4/** per_month */) {

                            $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;


                            $total_required_hours += $required_hours;

//                            $record['required_hours'] = $required_hours;
                            $record['required_hours'] = $employee->hours_per_month / $date->daysInMonth;
                            $record['missed_hours'] = $total_missed_hours;
                            $record['volunteered_hours'] = $total_volunteered_hours;
                            $record['date'] = $date->format('Y-m-d');

                        }
                    } else {
                        $record['required_hours'] = 'off';
                    }
                } elseif ($employee->work_mood == 'per_hour') {
                    $total_working_hours += $record['worked_hours'];
                    $total_missed_hours = $total_missed_hours;
                    $total_volunteered_hours = $total_volunteered_hours;
                } else {
                    $total_working_hours += $record['worked_hours'];
                    $total_missed_hours = $total_missed_hours;
                    $total_volunteered_hours = $total_volunteered_hours;
                }

                $daily_record[$date->format('Y-m-d')] = $record;
//            $date->addDay();
            }


            return [
                'employee' => $employee,
                // 'attendance' => $attendance,
                'daily_record' => $daily_record,
                'next_month' => $next_month,
                'current_month' => $current_month,
                'public_holidays' => $public_holidays,
                'weekend' => $weekend,
                'total_required_hours' => $total_required_hours,
                'total_missed_hours' => $total_missed_hours,
                'total_volunteered_hours' => $total_volunteered_hours,
                'total_working_hours' => $total_working_hours
            ];

        } catch (\Exception $exception) {
            \Log::error($exception);

            return response()->json($exception->getMessage());
        }

    }

    public
    function perDateAttendanceReport($id, $date = null)
    {

        $date = new Carbon($date);


        // temp ;to be dynamicly loaded

        $weekend = ['Sat', 'Sun'];
        $public_holidays = ['2018-01-01', '2018-01-31'];

        // end of temp


        $start_date = $date;
        $current_month = $date->format('Y-m-d');


//        $current_month = $date;
        $employee = Employee::findOrFail($id);
        /* NEW */


        $total_required_hours = 0;
        $total_working_hours = 0;
        $trackPerformedHours = 0;


        $day = strtolower($date->format('D'));

//        $salary_on_this_date = $employee->salaryOn($date->format('Y-m-d'));


        $record = [];

        $attendance_list = Attendance::where('employee_id', $employee->id)
            ->where(DB::raw('date(clock)'), '=', $date->format('Y-m-d'))
            ->orderBy('clock')
            ->get();


        $attendance_duration = $this->calculateAttendanceDuration($attendance_list);


        $record['day'] = $day;

        $record['worked_hours'] = number_format($attendance_duration['duration'] / 60, 2);
        $record['attendance'] = $attendance_duration['attendance'];

        if ($employee->work_mood == 'per_hour') {
            $total_required_hours = $employee->hours_per_month;
        }

        if ($employee->timetable) {
            $required = $employee->timetable->where('day', $day)->first();


            if ($required) {
                $total_working_hours += $record['worked_hours'];
                $trackPerformedHours[] = $attendance_duration['trackWorkingMinutes'];
                if ($employee->work_mood == 4/** per_month */) {
                    // $diff = strtotime("2001-01-01 ".$required->clockin.":00")->diffInMinutes(strtotime("2001-01-01 ".$required->clockout.":00"));

                    $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;
                    $total_required_hours += $required_hours;
                    $record['required_hours'] = $required_hours;
                }

            } else {
                $record['required_hours'] = 'off';
            }
        } elseif ($employee->work_mood == 'per_hour') {
            $total_working_hours += $record['worked_hours'];
            $trackPerformedHours[] = $attendance_duration['trackWorkingMinutes'];
        }

        $daily_record[$date->format('Y-m-d')] = $record;
        $date->addDay();


        return $total_working_hours;

        return [


            'current_month' => $current_month,
            'total_required_hours' => $total_required_hours,
            'total_working_hours' => $total_working_hours,
            'trackPerformedHours' => $attendance_duration['trackWorkingMinutes']
        ];
    }

    public
    function searche(Request $request)
    {
        if ($request->ajax()) {
            $output = '';
            $query = $request->get('query');

            if ($query != '') {
                $data = Employee::where('name', 'like', '%' . $query . '%')
                    ->orWhere('email', 'like', '%' . $query . '%')
                    ->orWhere('full_name', 'like', '%' . $query . '%')
                    ->orWhere('full_name_trans', 'like', '%' . $query . '%')
                    ->orderBy('name', 'desc')
                    ->get();


            } else {
                $data = Employee::latest()->paginate();

            }
            $total_row = $data->count();


            if ($total_row > 0) {
                foreach ($data as $item) {

                    $output .= '<tr>
            <td> ' . $item->id . '</td>
           <td> ' . $item->name . ' </td>
           <td> ' . $item->roles->implode('description', ', ') . ' </td>
           <td class="text-center" >
               <a href = "' . route('individual.employee.monthly.attendance', $item->id) . '" class="btn btn-sm btn-primary" > View</a >
           </td>
        
           </tr>
                        ';
                }
            } else {
                $output = '
          <tr>
           <td align = "center" colspan = "5" > No Data Found </td>
          </tr>
                        ';
            }


            $data = array(
                'table_data' => $output,
                'total_data' => $total_row
            );

            echo json_encode($data);
        }
    }

    public
    function hashmatai(Request $request)
    {


        $employee = Employee::all();
        $role = Role::get();


        $attendance = Attendance::whereDate('created_at', date('Y-m-d '))->orderBy('employee_id');


        return view(
            'humanresource::attendance.daily_report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'role',
                'date'
            )
        );
    }

    public
    function daily_report_search(Request $request)
    {


        $id = $request->role;

        $date = $request->exists('employeeAttendanceDate') == true ? $request->get('employeeAttendanceDate') : date('Y-m-d');

        $date = Carbon::parse($date);


        if ($id == 0) {
            $attendance = Attendance::whereDate('clock', $date)->orderBy('employee_id');


        } else {
            $employee = Employee::whereHas('roles', function ($q) use ($id) {
                return $q->where('id', $id);
            })->get();
            $employeeIds = Employee::whereHas('roles', function ($q) use ($id) {
                return $q->where('id', $id);
            })->pluck('id')->toArray();
            $attendance = Attendance::whereDate('clock', $date)->whereIn("employee_id", $employeeIds)->orderBy('employee_id');

        }


        $role = Role::get();
        return view(
            'humanresource::attendance.daily_report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'role',
                'date'
            )
        );
    }

    public
    function monthly_report(Request $request, $date = null)
    {


        // to be refactored

        $employee = Employee::all();
        foreach ($employee as $item) {
            $report = $this->report($item->id, $date);
            extract($report);
            $items[] = $report;
        }


        $role = Role::get();
        $attendance = Attendance:: whereYear('created_at', '=', date('Y'))
            ->whereMonth('created_at', '=', date('m'))->orderBy('employee_id')
            ->get();

        //  $attendance =Attendance::whereDate('created_at', date('Y - m'))->orderBy('employee_id')->get();
        return view(
            'humanresource::attendance.monthly_report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'role',
                'date',
                'items'
            )
        );
    }

    public
    function monthly_report_search(Request $request)
    {
        $date = null;
        $id = $request->role;
        if ($id == '0') {
            return $this->monthly_report($request);
        }


        $employee = Employee::whereHas('roles', function ($q) use ($id) {

            return $q->where('id', $id);
        })->get();


        $role = Role::get();
        foreach ($employee as $item) {
            $report = $this->report($item->id, $date);
            extract($report);
            $items[] = $report;
        }


        $attendance = Attendance:: whereYear('created_at', '=', date('Y'))
            ->whereMonth('created_at', '=', date('m'))->orderBy('employee_id')
            ->get();


        return view(
            'humanresource::attendance.monthly_report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'role',
                'date',
                'items'
            )
        );


    }

    public
    function addNoteToAtttendance(Request $request)
    {

        try {


            $validationmessages = [
                'note.max' => 'The :attribute is more than 500 characters. Please limit to 500 characters.',
            ];
            $validation = \Validator::make($request->all(), [
                'note' => 'required|max:500'
            ], $validationmessages);


            $attendance = Attendance::where("id", $request->clockId)->update(['note' => $request->note]);

//        } catch(\Illuminate\Database\QueryException $ex){
        } catch (\Exception $ex) {
            return response()->json($ex->getMessage(), 500);


            // Note any method of class PDOException can be called on $ex.
        }
        return response()->json("note added to the attendance", 200);


    }

    public
    function updateAttendanceEntryPair(Request $request)
    {


        try {

            if (!auth()->user()->can('update attendance')) {
                return response()->json(['error' => 'not_autorized'], 300);
            }


            if (!$request->exists('in') && !$request->exists('out')) {
                return response()->json('Please complete the details', 422);
            }


            $inTime = Carbon::parse($request->get('in'));
            $inNote = $request->get('inNote');
            $outNote = $request->get('outNote');

            $outTime = Carbon::parse($request->get('out'));

//            $outTimeGreater = $outTime->gt($inTime);
//            $outTimeEqIn = $outTime->equalTo($inTime);
//            $InTimeEqOut = $inTime->equalTo($outTime);
//
//
//            if ($outTimeEqIn == true OR $InTimeEqOut == true) {
//
//                return response()->json('The OUT time can not be equal to the IN time', 422);
//            }
//            if ($outTimeGreater == false) {
//                return response()->json('The OUT time can not be less than the IN time . Please select greater time for the OUT field', 422);
//            }


            $agent = new Agent();
            $inAttendance = Attendance::updateOrInsert(['id' => $request->inId], [
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),

                'type' => 'in',
                'employee_id' => $request->employee_id,
                'clock' => $inTime,
                'note' => $inNote,

                'organization_id' => \Config::get('organization_id'),
                'created_by' => auth()->user()->id,
                'updated_by' => auth()->user()->id,
            ]);

            $outAttendance = Attendance::updateOrInsert(['id' => $request->outId], [
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),
                'type' => 'out',
                'employee_id' => $request->employee_id,
                'clock' => $outTime,
                'note' => $outNote,
                'created_by' => auth()->user()->id,
                'organization_id' => \Config::get('organization_id'),
                'updated_by' => auth()->user()->id,
            ]);

//        } catch(\Illuminate\Database\QueryException $ex){
        } catch (\Exception $ex) {
            return response()->json($ex->getMessage(), 500);


            // Note any method of class PDOException can be called on $ex.
        }
        return response()->json("note added to the attendance", 200);


    }

    public
    function getAttendanceNote($id)
    {


        $note = Attendance::where("id", $id)->first()->note;


        return response()->json($note);

    }

    public
    function getAttendancePair($employeeId, $id, $type)
    {

        $result = [];
        if ($type == 'in') {

            $attDate = Attendance::where("id", $id)->select(\DB::raw("date(clock) as date"))->first()->date;


            $nextRow = Attendance::where("employee_id", $employeeId)->where("id", '>', $id)->where(\DB::raw("date(clock)"), $attDate)->select(\DB::raw('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note'))->first();


            if (is_null($nextRow)) {

                $nextDate = new Carbon($nextRow->date);
                $currentRow = Attendance::where("id", $id)->select(\DB::raw('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note'))->first();
                $currentDate = new Carbon($currentRow->date);
                $currentTime = new Carbon($currentRow->time);

                if ($nextDate->gt($currentDate)) {

                    $attendance = Attendance::where("id", $id)->select(\DB::raw('time(clock) attTime,clock,type,employee_id,id,note'))->first()->toArray();
                    $systemGeneratedRecord = ['attTime' => $currentTime->addSeconds(44)->toTimeString(), 'clock' => $currentDate->toDateString() . ' ' . $currentTime->addSeconds(44)->toTimeString(), 'employee_id' => $currentRow->employee_id, 'id' => '', 'note' => '', 'type' => 'out'];

                    $result[] = $attendance;
                    $result[] = $systemGeneratedRecord;


                }
            } else {
                $result = Attendance::where(\DB::raw("date(clock)"), $attDate)->whereIn("id", [$id, $nextRow->id])->select(\DB::raw('time(clock) attTime,time(clock) as time,clock,type,employee_id,id,note'))->get();


            }


        } else {


            $attDate = Attendance::where("id", $id)->select(\DB::raw("date(clock) as date"))->first()->date;


            $previous = Attendance::where("employee_id", $employeeId)->where("id", '<', $id)->where(\DB::raw("date(clock)"), $attDate)->select(\DB::raw('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note'))->first();


            // if there is no other record before this record for the $attDate
            if (is_null($previous)) {


                $currentRow = Attendance::where("employee_id", $employeeId)->where("id", $id)->select(\DB::raw('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note'))->first();
                $currentDate = new Carbon($currentRow->date);
                $currentTime = new Carbon($currentRow->time);


                $attendance = Attendance::where("id", $id)->select(\DB::raw('time(clock) attTime,clock,type,employee_id,id,note'))->first()->toArray();
                $systemGeneratedRecord = ['attTime' => $currentTime->subSeconds(120)->toTimeString(), 'clock' => $currentDate->toDateString() . ' ' . $currentTime->subSeconds(44)->toTimeString(), 'employee_id' => $currentRow->employee_id, 'id' => '', 'note' => 'This is temporary system generated note and time, please proceed to edit the details here for the Punch in', 'type' => 'in'];

                $result[] = $attendance;
                $result[] = $systemGeneratedRecord;

            } else {
                $result = Attendance::where(\DB::raw("date(clock)"), $attDate)->whereIn("id", [$id, --$id])->select(\DB::raw('time(clock) attTime,time(clock) as time,clock,type,employee_id,id,note'))->get();


            }


            // original record before the edit
//            $result = Attendance::whereIn("id", [$id,])->select(\DB::raw('time(clock) attTime,clock,time(clock) as time, type,employee_id,id,note'))->get();

        }


        return response()->json($result);

    }


}

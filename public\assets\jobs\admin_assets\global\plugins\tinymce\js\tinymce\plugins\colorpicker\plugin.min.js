!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),n=tinymce.util.Tools.resolve("tinymce.util.Color"),i=function(e,n){e.find("#preview")[0].getEl().style.background=n},t=function(e,t){var l=n(t),a=l.toRgb();e.fromJSON({r:a.r,g:a.g,b:a.b,hex:l.toHex().substr(1)}),i(e,l.toHex())},l=function(e,n,l){var a=e.windowManager.open({title:"Color",items:{type:"container",layout:"flex",direction:"row",align:"stretch",padding:5,spacing:10,items:[{type:"colorpicker",value:l,onchange:function(){var e=this.rgb();a&&(a.find("#r").value(e.r),a.find("#g").value(e.g),a.find("#b").value(e.b),a.find("#hex").value(this.value().substr(1)),i(a,this.value()))}},{type:"form",padding:0,labelGap:5,defaults:{type:"textbox",size:7,value:"0",flex:1,spellcheck:!1,onchange:function(){var e,n,i=a.find("colorpicker")[0];if(e=this.name(),n=this.value(),"hex"===e)return t(a,n="#"+n),void i.value(n);n={r:a.find("#r").value(),g:a.find("#g").value(),b:a.find("#b").value()},i.value(n),t(a,n)}},items:[{name:"r",label:"R",autofocus:1},{name:"g",label:"G"},{name:"b",label:"B"},{name:"hex",label:"#",value:"000000"},{name:"preview",type:"container",border:1}]}]},onSubmit:function(){n("#"+a.toJSON().hex)}});t(a,l)};e.add("colorpicker",function(e){e.settings.color_picker_callback||(e.settings.color_picker_callback=function(n,i){l(e,n,i)})})}();
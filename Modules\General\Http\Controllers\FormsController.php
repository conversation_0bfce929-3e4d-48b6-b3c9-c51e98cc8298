<?php

namespace Modules\General\Http\Controllers;

use App\Form;
use App\FormReview;
use App\FormBuilder;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class FormsController extends Controller
{
    public function index()
    {
        $form_builder = FormBuilder::where('status', 1)->get();

        $user_forms = Form::where('created_by', auth()->user()->id)->get();

        $forms_waiting_action = Form::where('status', '!=' , 'approved' )->
                                where('status', '!=' , 'rejected' )->
                                get()->filter(function($f){
                                    // foreach
                                    return !!count(array_intersect($f->type->approvalFlow->pluck('role')->toArray() , auth()->user()->roles->pluck('name')->toArray()));
                                });

        return view('general::forms.index')->with(
            [
                'form_builders' => $form_builder,
                'user_forms' => $user_forms,
                'forms_waiting_action' => $forms_waiting_action
            ]
        );

        return [
            'form_builder' => $form_builder,
            'user_forms' => $user_forms,
            'forms_waiting_action' => $forms_waiting_action
        ];
    }
    

    public function show($id)
    {
        // check auth
        $form = $this->permittedToAccessForm($id);


        return view('general::forms.view', compact('form'));
    }
    public function create($builder_id)
    {
        $form_builder = $this->validatedBuilder($builder_id);

        return view('general::forms.create', compact('form_builder'));
    }

    public function store($builder_id, Request $request)
    {
        $form_builder =$this->validatedBuilder($builder_id);

        $validation = [];

        $validation["content"] = 'required|min:10';

        if ($form_builder->date_or_range == 'range') {
            $validation["from_date"] = 'required|date';
            $validation["to_date"] = 'required|date';
        }
        if ($form_builder->date_or_range == 'date') {
            $validation["from_date"] = 'required|date';
        }
        // if ($form_builder->target) {
        //     $validation["target_type"] = 'required|date';
        //     $validation["target_id"] = 'required|number';
        // }
        $this->validate($request, $validation);

        $form = new Form();

        $form->form_builder_id = $form_builder->id;

        $form->content = $request->content;

        if ($form_builder->date_or_range == 'range') {
            $form->from_date = $request->from_date;
            $form->to_date = $request->to_date;
        }
        if ($form_builder->date_or_range == 'date') {
            $form->from_date = $request->from_date;
        }
        // if ($form_builder->target) {
        //     $form->target_type = $request->target_type;
        //     $form->target_id = $request->target_id;
        // }
        $form->created_by = auth()->user()->id;
        
        $form->status = 'new_admission';

        $form->save();

        flash('Your Request/Form has been sent successfully');

        return redirect(route('general.forms'));
    }


    public function edit($id)
    {
        return view('general::department.edit', compact('department', 'messages'));
    }

    public function review(Request $request, $id)
    {
        $form = Form::findOrFail($id);

        $step = $form->type->approvalFlow->where('id', $request->step)->first();

        if (!$step || !auth()->user()->hasRole($step->role)) {
            return 'Err';
        }
        
        $response = $request->action;

        if ($response == 'approved' && !$step->has_final_approval) {
            $form->status = 'processing';
        } else {
            $form->status = $response;
        }
        $form->save();

        FormReview::create([
            'form_id' => $form->id,
            'action' =>  $request->action,
            'created_by' => auth()->user()->id,
            'step_order' => $step->step_order,
            'note' => $request->note ?? ''
        ]);

//        return redirect()->route('general.forms');
        return redirect()->action('Module\General\Http\FormsController@$this->index()');
    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
        return Request::all();
        // $department=Department::findOrFail($id);

        // $department->delete();

        // return redirect()->back();
    }

    public function delete($id)
    {
    }


    private function validatedBuilder($builder_id)
    {
        $form_builder = FormBuilder::findOrFail($builder_id);

        if (!auth()->user()->can('request form '.$form_builder->id)) {
            // NOT Authrized , return error
            echo "Not authrized"; //tmp
        }

        return $form_builder;
    }
    private function permittedToAccessForm($id)
    {
        $form = Form::find($id);
        
        if (!$form) {
            // send to error page
            return 'No Form';
        }
        if (!$form->created_by == auth()->user()->id && !auth()->user()->can('review form '.$form_builder->id)) {
            // NOT Authrized , return error
            echo "Not authrized"; //tmp
        }

        return $form;
    }
}

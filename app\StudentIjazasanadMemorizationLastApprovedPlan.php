<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentIjazasanadMemorizationLastApprovedPlan extends Model
{
    use HasFactory;
    protected $table = 'student_ijazasanad_memorization_last_approved_plans';

    protected $fillable = [
        'student_id',
        'approved_by',
        'plan_year_month_day',
        'from_surat',
        'from_ayat',
        'to_surat',
        'to_ayat',
        'talqeen_from_lesson',
        'talqeen_to_lesson',
        'revision_from_lesson',
        'revision_to_lesson',
        'jazariyah_from_lesson',
        'jazariyah_to_lesson',
        'seminars_from_lesson',
        'seminars_to_lesson',
        'supervisor_comment',
        'from_surat_juz_id',
        'to_surat_juz_id',
    ];

    /**
     * Get the student that owns the plan.
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    /**
     * Get the user who approved the plan.
     */
    public function approvedBy()
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }
}

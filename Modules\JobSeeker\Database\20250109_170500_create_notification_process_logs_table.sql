-- Context: Create comprehensive process logging table for <PERSON><PERSON>eeker notification debugging
-- Purpose: Track every step of the notification process to identify issues quickly in the future
-- Features: Stores execution traces, timing, success/failure states, and detailed context

CREATE TABLE notification_process_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    trace_id VARCHAR(255) NOT NULL,
    execution_id BIGINT UNSIGNED NULL,
    step_name VARCHAR(100) NOT NULL,
    step_type ENUM('start', 'process', 'decision', 'action', 'error', 'complete') NOT NULL,
    provider VARCHAR(50) NULL,
    status ENUM('pending', 'running', 'success', 'warning', 'error', 'skipped') NOT NULL DEFAULT 'pending',
    message TEXT NULL,
    context JSON NULL,
    timing_start TIMESTAMP(3) NULL,
    timing_end TIMESTAMP(3) NULL,
    duration_ms INT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_trace_id (trace_id),
    INDEX idx_execution_id (execution_id),
    INDEX idx_step_type (step_type),
    INDEX idx_provider (provider),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT = 'Comprehensive logging for JobSeeker notification process debugging and performance tracking';

-- Sample usage queries for debugging:
-- 1) Get all steps for a specific trace:
-- SELECT * FROM notification_process_logs WHERE trace_id = 'uuid' ORDER BY created_at;
-- 2) Find failed processes:
-- SELECT trace_id, COUNT(*) as steps, MAX(created_at) as last_step 
--    FROM notification_process_logs WHERE status = 'error' GROUP BY trace_id;
-- 3) Performance analysis:
-- SELECT step_name, AVG(duration_ms) as avg_duration, COUNT(*) as count
--    FROM notification_process_logs WHERE duration_ms IS NOT NULL GROUP BY step_name;

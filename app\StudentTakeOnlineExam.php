<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class StudentTakeOnlineExam extends Model
{
    public static function submittedAnswer($exam_id, $s_id){ 
		try {
			return StudentTakeOnlineExam::where('online_exam_id', $exam_id)->where('student_id', $s_id)->first();
		} catch (\Exception $e) {
			$data=[];
			return $data;
		} 
    }

    public function answeredQuestions(){
    	return $this->hasMany('App\StudentTakeOnlineExamQuestion', 'take_online_exam_id', 'id');
    }

    public function onlineExam(){
    	return $this->belongsTo('App\OnlineExam', 'online_exam_id', 'id');
    }

    
}

@startuml Database Schema (Entity-Relationship Diagram)

!theme vibrant

' ------Primary Entities------
entity "users" {
  * id: int
  --
  full_name: var<PERSON><PERSON>(250)
  display_name: var<PERSON>r(100)
  username: var<PERSON>r(250)
  email: varchar(250)
  password: varchar(100)
  usertype: varchar(210)
  remember_token: varchar(100)
  created_at: timestamp
  updated_at: timestamp
  access_status: int
  organization_id: int
  nationality: varchar(50)
  phone: varchar(191)
}

entity "students" {
  * id: int
  --
  user_id: int <<FK>>
  guardian_id: int <<FK>>
  full_name: varchar(255)
  display_name: varchar(255)
  student_number: varchar(255)
  gender: varchar(255)
  date_of_birth: date
  identity_number: varchar(255)
  nationality: varchar(255)
  mobile: varchar(255)
  email: varchar(255)
  password: varchar(255)
  organization_id: int
  status: varchar(255)
  admission_date: date
  roll_no: int
  created_at: timestamp
  updated_at: timestamp
  deleted_at: timestamp
}

entity "employees" {
  * id: int
  --
  user_id: int <<FK>>
  department_id: int <<FK>>
  manager_id: int <<FK>>
  full_name: varchar(255)
  display_name: varchar(255)
  email: varchar(255)
  password: varchar(255)
  employee_number: varchar(255)
  gender: varchar(255)
  date_of_birth: date
  identity_number: varchar(255)
  nationality: varchar(255)
  mobile: varchar(255)
  organization_id: int
  status: varchar(255)
  join_date: timestamp
  salary: int
  created_at: timestamp
  updated_at: timestamp
  deleted_at: timestamp
}

entity "employee_department" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "classes" {
  * id: int
  --
  class_code: varchar(255)
  center_id: int <<FK>>
  organization_id: int <<FK>>
  subject_id: int <<FK>>
  status: text
  classes_per_month: int
  created_at: timestamp
  updated_at: timestamp
  deleted_at: timestamp
}

entity "programs" {
  * id: int
  --
  name: varchar(255)
  code: varchar(255)
  type: varchar(255)
  organization_id: int
  status: text
}

entity "centers" {
  * id: int
  --
  name: varchar(255)
  code: varchar(255)
  organization_id: int
  status: text
}

entity "subjects" {
  * id: int
  --
  name: varchar(255)
  code: varchar(255)
  type: varchar(255)
  organization_id: int
  status: text
}

entity "class_students" {
  * id: int
  --
  class_id: int <<FK>>
  student_id: int <<FK>>
  organization_id: int
  academic_year_id: int <<FK>>
  status: text
}

entity "class_teachers" {
  * id: int
  --
  class_id: int <<FK>>
  employee_id: int <<FK>>
  organization_id: int
  status: text
}

entity "attendances" {
  * id: int
  --
  student_id: int <<FK>>
  employee_id: int <<FK>>
  class_id: int <<FK>>
  subject_id: int <<FK>>
  date: date
  status: varchar(255)
  notes: text
  organization_id: int
}

entity "guardians" {
  * id: int
  --
  user_id: int <<FK>>
  full_name: varchar(255)
  email: varchar(255)
  password: varchar(255)
  mobile: varchar(255)
  nationality: varchar(255)
  organization_id: int
  status: varchar(255)
}

entity "roles" {
  * id: int
  --
  name: varchar(255)
  guard_name: varchar(255)
  created_at: timestamp
  updated_at: timestamp
}

entity "permissions" {
  * id: int
  --
  name: varchar(255)
  guard_name: varchar(255)
  created_at: timestamp
  updated_at: timestamp
}

entity "model_has_roles" {
  * role_id: int <<FK>>
  * model_type: varchar(255)
  * model_id: int
}

entity "model_has_permissions" {
  * permission_id: int <<FK>>
  * model_type: varchar(255)
  * model_id: int
}

entity "role_has_permissions" {
  * permission_id: int <<FK>>
  * role_id: int <<FK>>
}

entity "academic_years" {
  * id: int
  --
  name: varchar(255)
  title: varchar(255)
  start_date: date
  end_date: date
  organization_id: int
  status: varchar(255)
}

entity "exams" {
  * id: int
  --
  exam_type_id: int <<FK>>
  subject_id: int <<FK>>
  class_id: int <<FK>>
  organization_id: int
  academic_year_id: int <<FK>>
  status: text
  exam_date: date
  start_time: time
  end_time: time
}

entity "exam_marks_registers" {
  * id: int
  --
  student_id: int <<FK>>
  exam_id: int <<FK>>
  class_id: int <<FK>>
  subject_id: int <<FK>>
  marks: decimal
  comments: text
  organization_id: int
}

entity "fees_masters" {
  * id: int
  --
  fees_type_id: int <<FK>>
  class_id: int <<FK>>
  amount: decimal
  organization_id: int
  academic_year_id: int <<FK>>
  description: text
}

entity "fees_assigns" {
  * id: int
  --
  fees_master_id: int <<FK>>
  student_id: int <<FK>>
  organization_id: int
  due_date: date
  amount: decimal
  payment_status: varchar(255)
}

entity "fees_payments" {
  * id: int
  --
  fees_assign_id: int <<FK>>
  student_id: int <<FK>>
  organization_id: int
  amount: decimal
  payment_method: varchar(255)
  payment_date: date
  payment_note: text
}

' ------Relationships------
users ||--o{ students : "has"
users ||--o{ employees : "has"
users ||--o{ guardians : "has"

students }o--|| guardians : "has guardian"
students }o--o{ class_students : "enrolled in"
students }o--o{ attendances : "has"
students }o--o{ fees_assigns : "assigned fees"
students }o--o{ fees_payments : "makes payment"
students }o--o{ exam_marks_registers : "takes exam"

employees }o--|| employee_department : "belongs to"
employees }o--o{ class_teachers : "teaches"
employees }o--o{ attendances : "has"

classes }o--o{ class_students : "has students"
classes }o--o{ class_teachers : "has teachers"
classes }o--|| centers : "belongs to"
classes }o--|| subjects : "has subject"
classes }o--o{ attendances : "has"
classes }o--o{ exams : "has"
classes }o--o{ fees_masters : "has fees"

roles }o--o{ model_has_roles : "assigned to"
permissions }o--o{ model_has_permissions : "assigned to"
permissions }o--o{ role_has_permissions : "granted to"
roles }o--o{ role_has_permissions : "has"

academic_years ||--o{ class_students : "has"
academic_years ||--o{ exams : "has"
academic_years ||--o{ fees_masters : "has"

exams ||--o{ exam_marks_registers : "has results"
fees_masters ||--o{ fees_assigns : "assigned to"
fees_assigns ||--o{ fees_payments : "has payments"

@enduml 
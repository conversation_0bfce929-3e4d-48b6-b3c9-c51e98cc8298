                            <div class=\"file-name\">Line 2</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:39:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 3</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:39:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 4</div>
                <div class=\"issue-item\">[2025-06-02 18:40:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:40:02  
</div>
                            <div class=\"file-name\">Line 5</div>
                <div class=\"issue-item\">[2025-06-02 18:40:03] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:40:03 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 6</div>
                <div class=\"issue-item\">[2025-06-02 18:40:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:40:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 7</div>
                <div class=\"issue-item\">[2025-06-02 18:41:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:41:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 8</div>
                <div class=\"issue-item\">[2025-06-02 18:41:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:41:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 9</div>
                <div class=\"issue-item\">[2025-06-02 18:42:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:42:02  
</div>
                            <div class=\"file-name\">Line 10</div>
                <div class=\"issue-item\">[2025-06-02 18:42:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:42:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 11</div>
                <div class=\"issue-item\">[2025-06-02 18:42:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:42:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 12</div>
                <div class=\"issue-item\">[2025-06-02 18:43:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:43:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 13</div>
                <div class=\"issue-item\">[2025-06-02 18:43:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:43:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 14</div>
                <div class=\"issue-item\">[2025-06-02 18:44:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:44:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 15</div>
                <div class=\"issue-item\">[2025-06-02 18:44:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:44:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 16</div>
                <div class=\"issue-item\">[2025-06-02 18:45:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:45:01  
</div>
                            <div class=\"file-name\">Line 17</div>
                <div class=\"issue-item\">[2025-06-02 18:45:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:45:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 18</div>
                <div class=\"issue-item\">[2025-06-02 18:45:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:45:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 19</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:46:02  
</div>
                            <div class=\"file-name\">Line 20</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:46:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 21</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:46:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 22</div>
                <div class=\"issue-item\">[2025-06-02 18:47:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:47:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 23</div>
                <div class=\"issue-item\">[2025-06-02 18:47:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:47:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 24</div>
                <div class=\"issue-item\">[2025-06-02 18:48:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:48:01  
</div>
                            <div class=\"file-name\">Line 25</div>
                <div class=\"issue-item\">[2025-06-02 18:48:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:48:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 26</div>
                <div class=\"issue-item\">[2025-06-02 18:48:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:48:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 27</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:49:02  
</div>
                            <div class=\"file-name\">Line 28</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:49:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 29</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:49:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 30</div>
                <div class=\"issue-item\">[2025-06-02 18:50:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:50:02  
</div>
                            <div class=\"file-name\">Line 31</div>
                <div class=\"issue-item\">[2025-06-02 18:50:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:50:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 32</div>
                <div class=\"issue-item\">[2025-06-02 18:50:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:50:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 33</div>
                <div class=\"issue-item\">[2025-06-02 18:51:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:51:01  
</div>
                            <div class=\"file-name\">Line 34</div>
                <div class=\"issue-item\">[2025-06-02 18:51:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:51:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 35</div>
                <div class=\"issue-item\">[2025-06-02 18:51:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:51:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 36</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:52:02  
</div>
                            <div class=\"file-name\">Line 37</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:52:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 38</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:52:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 39</div>
                <div class=\"issue-item\">[2025-06-02 18:53:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:53:02  
</div>
                            <div class=\"file-name\">Line 40</div>
                <div class=\"issue-item\">[2025-06-02 18:53:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:53:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 41</div>
                <div class=\"issue-item\">[2025-06-02 18:53:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:53:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 42</div>
                <div class=\"issue-item\">[2025-06-02 18:54:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:54:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 43</div>
                <div class=\"issue-item\">[2025-06-02 18:54:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:54:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 44</div>
                <div class=\"issue-item\">[2025-06-02 18:55:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:55:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 45</div>
                <div class=\"issue-item\">[2025-06-02 18:55:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:55:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 46</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:56:02  
</div>
                            <div class=\"file-name\">Line 47</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:56:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 48</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:56:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 49</div>
                <div class=\"issue-item\">[2025-06-02 18:57:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:57:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 50</div>
                <div class=\"issue-item\">[2025-06-02 18:57:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:57:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 51</div>
                <div class=\"issue-item\">[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:58:02  
</div>
                            <div class=\"file-name\">Line 52</div>
                <div class=\"issue-item\">[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:58:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 53</div>
                <div class=\"issue-item\">[2025-06-02 18:58:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:58:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 54</div>
                <div class=\"issue-item\">[2025-06-02 18:59:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:59:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 55</div>
                <div class=\"issue-item\">[2025-06-02 18:59:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:59:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 56</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:00:02  
</div>
                            <div class=\"file-name\">Line 57</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:00:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                                <h2>laravel.log (5 issues)</h2>
            
                            <div class=\"file-name\">Line 16</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:00:02  
</div>
                            <div class=\"file-name\">Line 19</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:00:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 20</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: The stream or file &quot;/var/www/html/itqanalquran/storage/logs/command_failures.log&quot; could not be opened in append mode: Failed to open stream: Permission denied
</div>
                            <div class=\"file-name\">Line 70</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: Call to undefined method Modules\\JobSeeker\\Services\\JobService::notifySubscribers() {&quot;exception&quot;:&quot;[object] (Error(code: 0): Call to undefined method Modules\\\\JobSeeker\\\\Services\\\\JobService::notifySubscribers() at /var/www/html/itqanalquran/Modules/General/Console/Commands/QueueNotifyJobSubscribers.php:51)
</div>
                            <div class=\"file-name\">Line 87</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:00:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            
        <div class=\"meta\">
            <p>This is an automated message from the Itqan AlQuran log monitoring system. Please investigate these issues promptly.</p>
            <p>To run a manual log check, run the command: <code>php artisan logs:monitor --date=2025-06-02</code></p>
        </div>
    </div>
</body>
</html> , 0, EmailService: Failed to log email to database: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'from' cannot be null (Connection: mysql, SQL: insert into `email_logs` (`from`, `to`, `subject`, `view`, `body`, `success`, `error_message`, `updated_at`, `created_at`) values (?, <EMAIL>, [ALERT] System Log Issues Detected on 2025-06-02 - Itqan AlQuran, emails.log_alert, <!DOCTYPE html>
<html>
<head>
    <meta charset=\"utf-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Log Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f7f7f7;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border: 1px solid #e4e4e4;
            border-radius: 5px;
        }
        .header {
            background-color: #cc2f2f;
            padding: 20px;
            color: white;
            text-align: center;
            margin: -20px -20px 20px -20px;
            border-radius: 5px 5px 0 0;
        }
        h1 {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        h2 {
            font-size: 18px;
            margin-top: 20px;
            color: #444;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .summary {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .issue-item {
            margin-bottom: 8px;
            padding: 10px;
            background-color: #fff4f4;
            border-left: 4px solid #cc2f2f;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            font-size: 13px;
        }
        .file-name {
            background-color: #3b3b3b;
            color: white;
            padding: 8px 12px;
            border-radius: 3px;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
            display: inline-block;
        }
        .meta {
            color: #666;
            margin-top: 25px;
            font-size: 12px;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        .date-badge {
            display: inline-block;
            background-color: #f57f17;
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            margin-right: 10px;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class=\"container\">
        <div class=\"header\">
            <h1>🚨 System Log Alert</h1>
        </div>
        
        <div class=\"summary\">
            <span class=\"date-badge\">2025-06-02</span>
            <strong>System:</strong> Itqan AlQuran<br>
            <strong>Server:</strong> ubuntu-8gb-hel1-1<br>
            <strong>Detected At:</strong> 2025-06-02 19:00:02<br>
            <strong>Total Issues Found:</strong> 62<br>
            <strong>Report Date:</strong> 2025-06-02
        </div>
        
        <p>The log monitoring system has detected potential issues in the application logs for <strong>2025-06-02</strong> that require your attention:</p>
        
                    <h2>command_failures.log (57 issues)</h2>
            
                            <div class=\"file-name\">Line 1</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:39:02  
</div>
                            <div class=\"file-name\">Line 2</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:39:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 3</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:39:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 4</div>
                <div class=\"issue-item\">[2025-06-02 18:40:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:40:02  
</div>
                            <div class=\"file-name\">Line 5</div>
                <div class=\"issue-item\">[2025-06-02 18:40:03] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:40:03 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 6</div>
                <div class=\"issue-item\">[2025-06-02 18:40:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:40:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 7</div>
                <div class=\"issue-item\">[2025-06-02 18:41:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:41:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 8</div>
                <div class=\"issue-item\">[2025-06-02 18:41:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:41:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 9</div>
                <div class=\"issue-item\">[2025-06-02 18:42:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:42:02  
</div>
                            <div class=\"file-name\">Line 10</div>
                <div class=\"issue-item\">[2025-06-02 18:42:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:42:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 11</div>
                <div class=\"issue-item\">[2025-06-02 18:42:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:42:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 12</div>
                <div class=\"issue-item\">[2025-06-02 18:43:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:43:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 13</div>
                <div class=\"issue-item\">[2025-06-02 18:43:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:43:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 14</div>
                <div class=\"issue-item\">[2025-06-02 18:44:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:44:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 15</div>
                <div class=\"issue-item\">[2025-06-02 18:44:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:44:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 16</div>
                <div class=\"issue-item\">[2025-06-02 18:45:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:45:01  
</div>
                            <div class=\"file-name\">Line 17</div>
                <div class=\"issue-item\">[2025-06-02 18:45:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:45:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 18</div>
                <div class=\"issue-item\">[2025-06-02 18:45:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:45:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 19</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:46:02  
</div>
                            <div class=\"file-name\">Line 20</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:46:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 21</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:46:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 22</div>
                <div class=\"issue-item\">[2025-06-02 18:47:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:47:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 23</div>
                <div class=\"issue-item\">[2025-06-02 18:47:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:47:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 24</div>
                <div class=\"issue-item\">[2025-06-02 18:48:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:48:01  
</div>
                            <div class=\"file-name\">Line 25</div>
                <div class=\"issue-item\">[2025-06-02 18:48:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:48:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 26</div>
                <div class=\"issue-item\">[2025-06-02 18:48:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:48:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 27</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:49:02  
</div>
                            <div class=\"file-name\">Line 28</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:49:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 29</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:49:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 30</div>
                <div class=\"issue-item\">[2025-06-02 18:50:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:50:02  
</div>
                            <div class=\"file-name\">Line 31</div>
                <div class=\"issue-item\">[2025-06-02 18:50:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:50:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 32</div>
                <div class=\"issue-item\">[2025-06-02 18:50:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:50:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 33</div>
                <div class=\"issue-item\">[2025-06-02 18:51:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:51:01  
</div>
                            <div class=\"file-name\">Line 34</div>
                <div class=\"issue-item\">[2025-06-02 18:51:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:51:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 35</div>
                <div class=\"issue-item\">[2025-06-02 18:51:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:51:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 36</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:52:02  
</div>
                            <div class=\"file-name\">Line 37</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:52:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 38</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:52:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 39</div>
                <div class=\"issue-item\">[2025-06-02 18:53:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:53:02  
</div>
                            <div class=\"file-name\">Line 40</div>
                <div class=\"issue-item\">[2025-06-02 18:53:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:53:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 41</div>
                <div class=\"issue-item\">[2025-06-02 18:53:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:53:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 42</div>
                <div class=\"issue-item\">[2025-06-02 18:54:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:54:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 43</div>
                <div class=\"issue-item\">[2025-06-02 18:54:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:54:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 44</div>
                <div class=\"issue-item\">[2025-06-02 18:55:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:55:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 45</div>
                <div class=\"issue-item\">[2025-06-02 18:55:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:55:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 46</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:56:02  
</div>
                            <div class=\"file-name\">Line 47</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:56:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 48</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:56:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 49</div>
                <div class=\"issue-item\">[2025-06-02 18:57:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:57:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 50</div>
                <div class=\"issue-item\">[2025-06-02 18:57:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:57:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 51</div>
                <div class=\"issue-item\">[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:58:02  
</div>
                            <div class=\"file-name\">Line 52</div>
                <div class=\"issue-item\">[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:58:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 53</div>
                <div class=\"issue-item\">[2025-06-02 18:58:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:58:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 54</div>
                <div class=\"issue-item\">[2025-06-02 18:59:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:59:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 55</div>
                <div class=\"issue-item\">[2025-06-02 18:59:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:59:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 56</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:00:02  
</div>
                            <div class=\"file-name\">Line 57</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:00:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                                <h2>laravel.log (5 issues)</h2>
            
                            <div class=\"file-name\">Line 16</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:00:02  
</div>
                            <div class=\"file-name\">Line 19</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:00:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 20</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: The stream or file &quot;/var/www/html/itqanalquran/storage/logs/command_failures.log&quot; could not be opened in append mode: Failed to open stream: Permission denied
</div>
                            <div class=\"file-name\">Line 70</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: Call to undefined method Modules\\JobSeeker\\Services\\JobService::notifySubscribers() {&quot;exception&quot;:&quot;[object] (Error(code: 0): Call to undefined method Modules\\\\JobSeeker\\\\Services\\\\JobService::notifySubscribers() at /var/www/html/itqanalquran/Modules/General/Console/Commands/QueueNotifyJobSubscribers.php:51)
</div>
                            <div class=\"file-name\">Line 87</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:00:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            
        <div class=\"meta\">
            <p>This is an automated message from the Itqan AlQuran log monitoring system. Please investigate these issues promptly.</p>
            <p>To run a manual log check, run the command: <code>php artisan logs:monitor --date=2025-06-02</code></p>
        </div>
    </div>
</body>
</html> , 0, Invalid address:  (From): , 2025-06-02 19:00:02, 2025-06-02 19:00:02)), 2025-06-02 19:00:02, 2025-06-02 19:00:02))"} 
[2025-06-02 19:00:02] production.INFO: Log monitoring detected issues for 2025-06-02 and sent alert email.  
[2025-06-02 19:00:02] production.INFO: Command logs:monitor for today's logs executed successfully at 2025-06-02 19:00:02  
[2025-06-02 19:00:03] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:00:03] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:00:03] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:00:03] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:00:03] production.ERROR: Command "general:process-notification-retries" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"general:process-notification-retries\" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers at /var/www/html/itqanalquran/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()
#1 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#3 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-02 19:00:03] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:00:03] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:00:03] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:00:03] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:00:03] production.ERROR: Command "general:notify-all-job-subscribers" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"general:notify-all-job-subscribers\" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers at /var/www/html/itqanalquran/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()
#1 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#3 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-02 19:00:03] production.INFO: Job notifications process started after job fetch at 2025-06-02 19:00:03  
[2025-06-02 19:00:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:00:03 | Error: Notification process failed to complete  
[2025-06-02 19:00:43] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:00:43] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:01:01] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:01:01] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:01:01] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:01:01] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:01:01] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:01:01] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:01:01] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:01:01] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:01:01] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 19:01:01] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 19:01:01] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 19:01:01] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 19:01:01] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:01:01] production.INFO: Starting job subscriber notifications at 2025-06-02 19:01:01  
[2025-06-02 19:01:01] production.INFO: Starting general:queue-notify-job-subscribers at 2025-06-02 19:01:01  
[2025-06-02 19:01:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:01:01  
[2025-06-02 19:01:01] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:01:01 {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:01:01 at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(114): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 19:01:01] production.INFO: Job notifications process started after job fetch at 2025-06-02 19:01:01  
[2025-06-02 19:01:01] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:01:01 | Error: Notification process failed to complete  
[2025-06-02 19:01:01] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:01:01 | Error: Notification process failed to complete {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:01:01 | Error: Notification process failed to complete at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(574): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 19:01:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:01:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:01:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:01:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:01:02] production.INFO: Starting job notification queue dispatch from command  
[2025-06-02 19:01:02] production.ERROR: Call to undefined method Modules\JobSeeker\Services\JobService::notifySubscribers() {"exception":"[object] (Error(code: 0): Call to undefined method Modules\\JobSeeker\\Services\\JobService::notifySubscribers() at /var/www/html/itqanalquran/Modules/General/Console/Commands/QueueNotifyJobSubscribers.php:51)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Modules\\General\\Console\\Commands\\QueueNotifyJobSubscribers->handle()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#6 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#8 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#9 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#10 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#12 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-06-02 19:01:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:01:02 | Error: Queue dispatch may be locked or failed to complete  
[2025-06-02 19:01:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:01:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:01:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:01:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:01:02] production.ERROR: Command "general:notify-all-job-subscribers" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"general:notify-all-job-subscribers\" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers at /var/www/html/itqanalquran/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()
#1 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#3 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-02 19:01:02] production.INFO: Job notifications process started after job fetch at 2025-06-02 19:01:02  
[2025-06-02 19:01:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:01:02 | Error: Notification process failed to complete  
[2025-06-02 19:02:01] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:02:01] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:02:01] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:02:01] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:02:01] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:02:01] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:02:01] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:02:01] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:02:02] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 19:02:02] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 19:02:02] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 19:02:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:02:02] production.INFO: Starting job subscriber notifications at 2025-06-02 19:02:02  
[2025-06-02 19:02:02] production.INFO: Starting general:queue-notify-job-subscribers at 2025-06-02 19:02:02  
[2025-06-02 19:02:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:02:02 | Error: Queue dispatch may be locked or failed to complete  
[2025-06-02 19:02:02] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:02:02 | Error: Queue dispatch may be locked or failed to complete {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:02:02 | Error: Queue dispatch may be locked or failed to complete at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(129): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 19:02:02] production.INFO: Job notifications process started after job fetch at 2025-06-02 19:02:02  
[2025-06-02 19:02:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:02:02 | Error: Notification process failed to complete  
[2025-06-02 19:02:02] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:02:02 | Error: Notification process failed to complete {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:02:02 | Error: Notification process failed to complete at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(574): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 19:02:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:02:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:02:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:02:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:02:02] production.ERROR: Command "jobseeker:notify-job-subscribers" is not defined.

Did you mean one of these?
    general:notify-job-seekers
    general:queue-notify-job-subscribers
    jobseeker:check-inactive-categories
    jobseeker:cleanup-device-tokens
    jobseeker:discover-acbar-categories
    jobseeker:fetch-jobs-af-descriptions
    jobseeker:sync-acbar-jobs
    jobseeker:sync-jobs-af
    jobseeker:test-fcm-notification
    jobseeker:test-refactored-fcm-notification {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"jobseeker:notify-job-subscribers\" is not defined.

Did you mean one of these?
    general:notify-job-seekers
    general:queue-notify-job-subscribers
    jobseeker:check-inactive-categories
    jobseeker:cleanup-device-tokens
    jobseeker:discover-acbar-categories
    jobseeker:fetch-jobs-af-descriptions
    jobseeker:sync-acbar-jobs
    jobseeker:sync-jobs-af
    jobseeker:test-fcm-notification
    jobseeker:test-refactored-fcm-notification at /var/www/html/itqanalquran/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()
#1 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#3 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-02 19:02:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:02:02  
[2025-06-02 19:02:02] production.INFO: Starting general:queue-notify-job-subscribers at 2025-06-02 19:02:02  
[2025-06-02 19:02:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:02:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:02:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:02:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:02:02] production.INFO: Starting job notification queue dispatch from command  
[2025-06-02 19:02:02] production.ERROR: Call to undefined method Modules\JobSeeker\Services\JobService::notifySubscribers() {"exception":"[object] (Error(code: 0): Call to undefined method Modules\\JobSeeker\\Services\\JobService::notifySubscribers() at /var/www/html/itqanalquran/Modules/General/Console/Commands/QueueNotifyJobSubscribers.php:51)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Modules\\General\\Console\\Commands\\QueueNotifyJobSubscribers->handle()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#6 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#8 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#9 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#10 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#12 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-06-02 19:02:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:02:02 | Error: Queue dispatch may be locked or failed to complete  
[2025-06-02 19:02:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:02:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:02:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:02:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:02:02] production.ERROR: Command "general:notify-all-job-subscribers" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"general:notify-all-job-subscribers\" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers at /var/www/html/itqanalquran/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()
#1 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#3 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}

<?php

namespace Modules\General\Http\Controllers;


use App\Admission;
use App\Center;
use App\CenterEmployee;
use App\Form;
use App\MissedClockOut;
use App\Role;
use App\Scopes\OrganizationScope;
use App\Student;
use Carbon\Carbon;
use Illuminate\Http\Request;

use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use PHPUnit\Exception;
use Yajra\DataTables\Facades\DataTables;


class MissedClockoutsDatatablesController extends Controller
{

    public function __invoke(Request $request)
    {


//        if ($request->ajax()) {


            try {


//                dd($request->all());
                $allMissedClockOutAttendance = MissedClockOut::onlyTrashed()->with('attendance')->with('employee')->with('employee.roles');
                $allMissedClockOutAttendance->when(!$request->filled('olderThanthisMonth'), function ($q) use ($request) {


                    $yesterday = Carbon::yesterday()->toDateString();
                    $q->whereDate('clock',$yesterday);


                });
                $allMissedClockOutAttendance->when($request->filled('gender'), function ($q) use ($request) {

                    $q->whereHas("employee", function ($q) use ($request) {
                        $q->where('employees.gender', $request->gender);
                    });
                });
                $allMissedClockOutAttendance->when($request->filled('employees'), function ($q) use ($request) {

                    $q->where('employee_id', $request->get('employees'));

                });
                $allMissedClockOutAttendance->when($request->filled('roles'), function ($q) use ($request) {

                    $q->whereHas("employee.roles", function ($query) use ($request) {
                        $query->whereIn('id', $request->get('roles'));
                    });
                });
                $allMissedClockOutAttendance->when($request->filled('yearMonth'), function ($q) use ($request) {

                    $explodedYearMonth = explode('-', $request->get('yearMonth'));
                    $year = $explodedYearMonth[0];
                    $month = $explodedYearMonth[1];


                    $q->whereYear("clock", $year)->whereMonth('clock', $month);

                });
                if ((auth()->user()->hasRole(['education-manager_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {


                    $allMissedClockOutAttendance->whereHas('employee.roles', function ($q) use ($request) {
                        $supervisorAndTeachersRolesIds = \DB::table('roles')->whereIn('description', ['Supervisor', 'Teacher'])->pluck('id');
                        $supervisorAndTeachersRolesIds = $supervisorAndTeachersRolesIds->toArray();
                        $q->whereIn('id', $supervisorAndTeachersRolesIds);
                    });
                }


                if ((auth()->user()->hasRole(['supervisor_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {
                    $supervisorCenId = auth()->user()->center()->get()->pluck('id');
                    $supervisorCenId = $supervisorCenId->toArray();


                    if ($request->filled('teacherCenters')) {

                        $allMissedClockOutAttendance->whereHas("employee.teacherCenter", function ($q) use ($request) {
                            $teacherCenIds = $request->get('teacherCenters');
                            $q->whereIn("cen_id", $teacherCenIds);
                        });
                    } else {



                        $allMissedClockOutAttendance->whereHas("employee.teacherCenter", function ($q) use ($supervisorCenId) {
                            $supervisorCenId = auth()->user()->center()->get()->pluck('id');
                            $supervisorCenId = $supervisorCenId->toArray();
                            $q->whereIn("cen_id", $supervisorCenId);
                        });
                    }

                }
                $yesterday = Carbon::yesterday()->toDateString();

//                $yesterday = date("Y-m-d", strtotime('-1 days'));
//                $countYesterday = MissedClockOut::withTrashed()->with('attendance')->whereDate('clock', $yesterday)->get()->groupBy(['employee_id', date('clock')])->count();
                $countYesterday = MissedClockOut::withTrashed()->with('attendance')->whereDate('clock', $yesterday)->get()->groupBy(['employee_id', date('clock')])->count();
                $countYesterday = $countYesterday == 0 ? 20 : $countYesterday;
//                $totalCount = floor($allMissedClockOutAttendance->count() / 2);
                $allMissedClockOutAttendance->orderByRaw('date(clock) desc, employee_id');
                $totalCount = $allMissedClockOutAttendance->distinct()->count();


                return DataTables::eloquent($allMissedClockOutAttendance->select())

                    ->withQuery('count', function ($row) {


                        $yesterday = date("Y-m-d", strtotime('-1 days'));
                        $countYesterday = MissedClockOut::withTrashed()->with('attendance')->whereDate('clock', $yesterday)->get()->groupBy(['employee_id', date('clock')])->count();
                        return $countYesterday == 0 ? 20 : $countYesterday;

                    })
//                }) ->skipTotalRecords()
//                    ->setFilteredRecords($totalCount)
//                    ->setTotalRecords($totalCount)
                    ->addColumn('full_name', function (MissedClockOut $row) {


                        $genderColor = $row->employee->gender == 'Male' || $row->employee->gender == 'Male (ذكر)' || $row->employee->gender == 'male' ? '#34b8bc;!important' : '#FA5661;!important';
                        $genderBasedDefaultImage = $row->employee->gender == 'Male' || $row->employee->gender == 'Male (ذكر)' || $row->employee->gender == 'male' ? asset('uploads/staff/demo/mstaff.jfif') : asset('uploads/staff/demo/fstaff.jpg');
                        if (file_exists($row->employee->image)) {

                            $image = asset($row->employee->image);
                        } elseif (Str::contains($row->employee->image, 'http')) {

                            $image = $row->employee->image;
                        } else {
                            $image = $genderBasedDefaultImage;


                        }


                        if (strlen($row->employee->full_name) > 22) {
                            $fullname = Str::limit(Str::title($row->employee->full_name), 19, ' ...');
                            return '<a target="_blank" href="' . route('employees.show', $row->employee_id) . '" data-missedClockOut_id="' . $row->id . '" data-employee_id="' . $row->employee_id . '" data-tooltip="' . Str::title($row->employee->full_name) . '"   style="color:' . $genderColor . '" class="ui image label">
                          <img src="' . $image . '" "' . asset($row->employee->image) . '">
                          ' . ucwords(strtolower($fullname)) . '
                        </a>';

                        } else {
                            $fullname = Str::title($row->employee->full_name);

                            return '<a  target="_blank" href="' . route('employees.show', $row->employee_id) . '" data-missedClockOut_id="' . $row->id . '" data-employee_id="' . $row->employee_id . '" style="color:' . $genderColor . '" class="ui image label">
                                          <img src="' . $image . '" "' . asset($row->employee->image) . '">
                                          ' . ucwords(strtolower($fullname)) . '
                                        </a>';

                        }


                        return '<span  data-missedClockOut_id="' . $row->id . '" data-employee_id="' . $row->employee_id . '">' . $row->employee->full_name . '</span>';
//


                    })
                    ->filterColumn('clock', function ($query, $keyword) {
                        $sql = "date(clock)  like ?";
                        $query->whereRaw($sql, ["%{$keyword}%"]);
                    })
                    ->addColumn('clock', function ($row) {


                        return Carbon::parse($row->clock)->toDateString();


                    })
                    ->addColumn('login', function ($row) use ($request) {


                        $impersonationRoute = route('multiAuthImpersonate', ['id' => $row->employee_id, 'guardName' => 'employee']);
                        return '<span class="badge badge-primary badge-pill"><a
                                style="color: white; padding: 0px; background: transparent "
                                href="' . $impersonationRoute . '">login</a></span>';

                    })
                    ->filterColumn('in', function ($query, $keyword) {
                        $sql = "time(clock)  like ?";
                        $query->whereRaw($sql, ["%{$keyword}%"]);
                    })
                    ->addColumn('in', function ($row) {
                        $html = '';
                        if ($row->type == 'in') {
                            $clock = Carbon::parse($row->clock);
                            $formattedTime = $clock->format('h:iA');
                            $html .= '<span class="" style="color: rgb(22, 199, 132); font-weight: bolder">' . $formattedTime . '</span>';
                            return $html;

                        }


                    })
                    ->filterColumn('out', function ($query, $keyword) {
                        $sql = "time(clock)  like ?";
                        $query->whereRaw($sql, ["%{$keyword}%"]);
                    })
                    ->addColumn('out', function ($row) {

                        $clockDate = Carbon::parse($row->clock)->toDateString(); // Clock date (base day)

                        // Attempt to find the `out` entry for the same employee on the same or next day
                        $out = MissedClockOut::withTrashed()
                            ->where('employee_id', $row->employee_id)
                            ->where(function ($query) use ($clockDate) {
                                $query->whereDate('clock', $clockDate) // Same day
                                ->orWhereDate('clock', Carbon::parse($clockDate)->addDay()->toDateString()); // Next day
                            })
                            ->where('type', 'out')
                            ->orderBy('clock', 'asc') // Ensure the earliest match is fetched
                            ->first();
                        if ($out) {

//                            $out = $out->first();
                            $html = ' <span class="" style="color: rgb(239, 97, 104); font-weight: bolder">' . Carbon::parse($out->clock)->format('h:i A'). '</span>';

                            return $html;
                        } else {
                            // Ensure clock is a Carbon instance
                            $clock = $row->clock instanceof Carbon ? $row->clock : Carbon::parse($row->clock);
// Now safely use toDateString()
                            return '<button class="ui green basic button" data-value="matt" data-toggle="modal" style="text-align-last: right;    font-size: 9px;
color: white !important;
font-weight: bolder;"
             id="addOutAttendanceRecordBtn"
             data-date="' . $clock->toDateString() . '"
             data-employee_id="' . $row->employee_id . '"
             data-in="' . $clock->format('g:i A') . '"
             data-target="#addMissedOutAttendanceRecord">
                 <i class="plus posit icon" style="color: rgb(22, 199, 132);"></i>
        </button>';
                        }
                    })
                    ->addColumn('numberofHours', function ($row) {

                        $dateString = Carbon::parse($row->clock)->toDateString();
                        $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                            ->whereDate('clock', $dateString)->where('type', 'out')->first();

                        if ($row->type == 'in') {

                            $in = is_null($row->clock) ? \Carbon\Carbon::now() : Carbon::parse($row->clock);


                        } else {

                            $in = \Carbon\Carbon::now();

                        }
                        if (optional($out)->exists()) {
                            $hours = $in->diffInHours($out->clock);
                            $seconds = $in->diffInMinutes($out->clock);
                            return $hours . ':' . $seconds % 60;
                        }

                    })
                    ->addColumn('clockoutReason', function ($row) {

                        $dateString = Carbon::parse($row->clock)->toDateString();
                        $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                            ->whereDate('clock', $dateString)->where('type', 'out')->first();

                        return optional($out)->clockout_reason;
                    })
                    ->addColumn('action', function ($row) {

                        $clockDate = Carbon::parse($row->clock)->toDateString();

                        // Fetch the 'out' entry for the same or next day
                        $out = \App\MissedClockOut::withTrashed()
                            ->where('employee_id', $row->employee_id)
                            ->where(function ($query) use ($clockDate) {
                                $query->whereDate('clock', $clockDate)
                                    ->orWhereDate('clock', Carbon::parse($clockDate)->addDay()->toDateString());
                            })
                            ->where('type', 'out')
                            ->first();
                        // Fetch the 'in' entry for the same day
                        $inDetails = \App\MissedClockOut::withTrashed()
                            ->where('employee_id', $row->employee_id)
                            ->whereDate('clock', $clockDate)
                            ->where('type', 'in')
                            ->first();

                        if ($out) {


                            $in = $inDetails ? $inDetails->id : 0; // Default to 0 if no 'in' entry found
                            $outId = $out->id;

                            if ($inDetails->type == 'in') {
//                            $in = $row->id;
                                $in = $inDetails->id;

                            }
                            if ($row->type == 'out') {
                                $out = $row->id;

                            }


                            return '<div class="ui large buttons">
  <button class="ui button positive deleteModalTriggerBtn"
   id="deleteModalTriggerBtn "
                 
                     data-toggle="modal"
                   data-missed_clockin_id="' . $in . '"
//                     data-missed_clockin_id="' . optional($in)->id . '"
//                     data-missed_clockout_id="' . optional($out)->id . '"
                     data-missed_clockout_id="' . $outId . '"
                     data-attendance_in_id="' . optional($row)->attendance_id . '"
                     data-employee_id="' . optional($row)->employee_id . '"
                     data-clock="' . optional($out)->clock . '"
                     data-clockout_reason="' . optional($out)->clockout_reason . '"
                      data-target="#validateMissedClockoutEntry">Validate</button>
  <div class="or"></div>
  <button class="ui button deleteModalTriggerBtn"
 id="deleteModalTriggerBtn "
                    
                     data-toggle="modal"
                     data-missed_clockin_id="' . $in . '"
//                     data-missed_clockout_id="' . optional($out)->id . '"
                     data-missed_clockout_id="' . $outId . '"
                     data-attendance_in_id="' . optional($row)->attendance_id . '"
                     data-employee_id="' . optional($row)->employee_id . '"
                     data-clock="' . optional($out)->clock . '"
                     data-clockout_reason="' . optional($out)->clockout_reason . '"
                      data-target="#revertMissedClockoutEntry">Revert</button>
</div>



';
                        }


                    })
                    ->rawColumns(['action', 'in', 'out', 'full_name', 'login'])
                    ->toJson();

            } catch (Exception $exception) {


                return response()->json($exception->getMessage());
            }


//        }


    }

}









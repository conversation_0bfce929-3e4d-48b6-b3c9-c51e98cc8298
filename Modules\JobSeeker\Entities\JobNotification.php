<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;

class JobNotification extends Model
{
    protected $table = 'job_notifications';
    protected $guarded = ['id'];
    protected $fillable = [
        'user_id',
        'name',
        'categories',
        'notification_type',
    ];

    protected $casts = [
        'categories' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(\App\User::class, 'user_id');
    }
} 
/* <PERSON><PERSON><PERSON>zier Transition */
/***
Pricing Table 1
***/
.pricing-content-1 {
  background-color: #fff; }
  .pricing-content-1:before,
  .pricing-content-1:after {
    content: " ";
    display: table; }
  .pricing-content-1:after {
    clear: both; }
  .pricing-content-1 .arrow-down {
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 15px solid;
    margin: auto; }
    .pricing-content-1 .arrow-down.arrow-blue {
      border-top-color: #3598DC; }
    .pricing-content-1 .arrow-down.arrow-green {
      border-top-color: #32C5D2; }
    .pricing-content-1 .arrow-down.arrow-dark {
      border-top-color: #2F353B; }
    .pricing-content-1 .arrow-down.arrow-grey {
      border-top-color: #f7f9fb; }
  .pricing-content-1 .price-ribbon {
    position: absolute;
    top: 75px;
    right: -4px;
    width: 90px;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 300;
    padding: 6px 20px 6px 15px;
    color: #fff;
    background-color: #D91E18;
    text-shadow: 0px 1px 2px #bbb;
    -webkit-box-shadow: 0px 2px 4px #888;
    -moz-box-shadow: 0px 2px 4px #888;
    box-shadow: 0px 2px 4px #888; }
    .pricing-content-1 .price-ribbon:after {
      content: ' ';
      position: absolute;
      width: 0;
      height: 0;
      right: 0px;
      top: 100%;
      border-width: 5px 10px;
      border-style: solid;
      border-color: #64120f transparent transparent #64120f; }
  .pricing-content-1 .pricing-title {
    border-bottom: 1px solid;
    border-color: #fff; }
    .pricing-content-1 .pricing-title > h1 {
      margin: 20px 0; }
  .pricing-content-1 .price-column-container {
    text-align: center;
    margin: 0;
    background-color: #fff; }
    .pricing-content-1 .price-column-container.border-active {
      border: 1px solid #e5e9ee; }
    .pricing-content-1 .price-column-container:first-child {
      margin-left: 0; }
    .pricing-content-1 .price-column-container:last-child {
      margin-right: 0; }
  .pricing-content-1 .price-table-head {
    color: #fff;
    padding: 20px 0; }
    .pricing-content-1 .price-table-head h2 {
      font-size: 26px; }
    .pricing-content-1 .price-table-head.price-1 {
      background-color: #3598DC; }
    .pricing-content-1 .price-table-head.price-2 {
      background-color: #32C5D2; }
    .pricing-content-1 .price-table-head.price-3 {
      background-color: #2F353B; }
  .pricing-content-1 .price-table-pricing > h3 {
    font-size: 60px;
    position: relative; }
    .pricing-content-1 .price-table-pricing > h3 > .price-sign {
      font-size: 24px;
      position: absolute;
      margin-left: -15px; }
  .pricing-content-1 .price-table-pricing > p {
    margin-top: 0; }
  .pricing-content-1 .price-table-content {
    background-color: #f7f9fb;
    color: #5c6d7e;
    font-weight: 600;
    font-size: 16px; }
    .pricing-content-1 .price-table-content .row {
      padding-top: 10px;
      padding-bottom: 10px; }
      .pricing-content-1 .price-table-content .row i {
        color: #6cade6; }
      .pricing-content-1 .price-table-content .row:first-child {
        padding-top: 20px; }
      .pricing-content-1 .price-table-content .row:last-child {
        padding-bottom: 20px; }
  .pricing-content-1 .price-table-footer {
    padding: 20px 0; }
    .pricing-content-1 .price-table-footer > .price-button {
      font-weight: bold;
      padding: 10px 20px; }

@media (max-width: 1024px) {
  .pricing-content-1 .mobile-padding {
    padding: 0;
    margin: 0; }
    .pricing-content-1 .mobile-padding > i {
      margin-right: 5px; }
  .pricing-content-1 .price-table-content {
    padding-left: 10px;
    padding-right: 10px; } }

@media (max-width: 1024px) {
  .pricing-content-1 .mobile-padding {
    padding: 0 15px;
    margin: 0 -15px; }
    .pricing-content-1 .mobile-padding > i {
      margin-right: 20px; }
  .pricing-content-1 .price-table-content {
    padding-left: 15px;
    padding-right: 15px; } }

/***
Pricing Table 2
***/
.pricing-content-2 {
  background-color: #fff; }
  .pricing-content-2 .no-padding {
    padding: 0; }
  .pricing-content-2 .text-left {
    text-align: left; }
  .pricing-content-2 .text-right {
    text-align: right; }
  .pricing-content-2.pricing-bg-dark {
    background-color: #2F353B; }
  .pricing-content-2 .pricing-title {
    border-color: #444; }
    .pricing-content-2 .pricing-title > h1 {
      color: #fff; }
  .pricing-content-2 .pricing-table-container {
    padding-top: 40px;
    padding-bottom: 40px; }
    .pricing-content-2 .pricing-table-container .padding-fix {
      padding-left: 15px;
      padding-right: 15px; }
    .pricing-content-2 .pricing-table-container .price-column-container {
      background-color: #fff;
      margin: 30px 0;
      padding: 60px 0;
      text-align: center;
      border-bottom: 4px solid #ccc; }
      .pricing-content-2 .pricing-table-container .price-column-container.border-right {
        border-right: 1px solid #ccc; }
      .pricing-content-2 .pricing-table-container .price-column-container.border-left {
        border-left: 1px solid #ccc; }
      .pricing-content-2 .pricing-table-container .price-column-container.border-top {
        border-top: 1px solid #ccc; }
      .pricing-content-2 .pricing-table-container .price-column-container.featured-price {
        margin: 0;
        padding: 89px 0;
        border: 1px solid;
        border-bottom: 4px solid;
        border-color: #ccc; }
        .pricing-content-2 .pricing-table-container .price-column-container.featured-price > .price-feature-label {
          position: absolute;
          top: 0;
          left: 50%;
          display: inline-block;
          width: 110px;
          margin: 0 0 0 -60px;
          padding: 7px 15px;
          color: #fff;
          font-weight: 300; }
      .pricing-content-2 .pricing-table-container .price-column-container > .price-table-head > h2 {
        letter-spacing: 1px;
        font-weight: 600;
        font-size: 18px;
        color: #ACB5C3; }
        .pricing-content-2 .pricing-table-container .price-column-container > .price-table-head > h2.opt-pricing-5 {
          padding: 7px 15px;
          display: inline;
          margin: 0 auto 20px auto;
          font-size: 16px; }
      .pricing-content-2 .pricing-table-container .price-column-container > .price-table-pricing > h3 {
        font-size: 60px;
        position: relative;
        font-weight: 600; }
        .pricing-content-2 .pricing-table-container .price-column-container > .price-table-pricing > h3 > .price-sign {
          font-size: 24px;
          position: absolute;
          margin-left: -15px; }
      .pricing-content-2 .pricing-table-container .price-column-container > .price-table-pricing > p {
        margin-top: 0; }
      .pricing-content-2 .pricing-table-container .price-column-container > .price-table-content {
        color: #333;
        font-weight: 300;
        font-size: 16px; }
        .pricing-content-2 .pricing-table-container .price-column-container > .price-table-content .row {
          padding-top: 20px;
          padding-bottom: 20px;
          border-bottom: 1px solid;
          border-color: #eee; }
          .pricing-content-2 .pricing-table-container .price-column-container > .price-table-content .row:first-child {
            border-top: 1px solid;
            border-color: #eee; }
      .pricing-content-2 .pricing-table-container .price-column-container > .price-table-footer {
        padding: 40px 0 0 0; }
        .pricing-content-2 .pricing-table-container .price-column-container > .price-table-footer > .featured-price {
          font-size: 20px;
          font-weight: 300;
          border-bottom: 3px solid #3FABA4; }

@media (max-width: 991px) {
  .pricing-content-2 .price-column-container {
    border-left: 1px solid;
    border-right: 1px solid;
    border-color: #ccc; } }

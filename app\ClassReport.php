<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\ClassReport
 *
 * @property int $id
 * @property int $class_id
 * @property int $program_id
 * @property int $subject_id
 * @property int $teacher_id
 * @property int $created_by
 * @property \Illuminate\Support\Carbon $class_time
 * @property string|null $notes
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property string|null $temp_data
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\StudentAttendance[] $attendace
 * @property-read int|null $attendace_count
 * @property-read \App\Classes|null $class
 * @property-read \App\Employee|null $creator
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\StudentHefzReport[] $hefz
 * @property-read int|null $hefz_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\LessonReport[] $lessons
 * @property-read int|null $lessons_count
 * @property-read \App\Program|null $program
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\StudentRevisionReport[] $revision
 * @property-read int|null $revision_count
 * @property-read \App\Subject|null $subject
 * @property-read \App\Employee|null $teacher
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport query()
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereClassId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereClassTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereSubjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereTeacherId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereTempData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassReport whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ClassReport extends Model
{
    protected $casts = [
        'temp_data' => 'array',
        'class_time' => 'datetime'
    ];
    protected $fillable =[
            'student_id' ,
            'program_id' ,
            'subject_id',
            'teacher_id',
            'id',
            'created_by',
            'notes',
            'class_time',
            'status',
        ];
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'class_reports';


    protected $with = [
        'teacher',
        'subject',
        'program',
        'creator'
    ];
    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    public function teacher()
    {
        return $this->hasOne('App\Employee', 'id', 'employee_id');
    }
    
    public function class()
    {
        return $this->hasOne('App\Classes', 'id', 'class_id');
    }
    
    public function creator()
    {
        return $this->hasOne('App\Employee', 'id', 'created_by');
    }
    public function subject()
    {
        return $this->hasOne('App\Subject', 'id', 'subject_id');
    }
    public function program()
    {
        return $this->hasOne('App\Program', 'id', 'program_id');
    }

    public function attendace()
    {
        return $this->hasMany('App\StudentAttendance', 'class_report_id', 'id');
    }

    public function lessons()
    {
        return $this->hasMany('App\LessonReport', 'class_report_id', 'id');
    }

    public function hefz()
    {
        return $this->hasMany('App\StudentHefzReport', 'class_report_id', 'id');
    }

    public function revision()
    {
        return $this->hasMany('App\StudentRevisionReport', 'class_report_id', 'id');
    }
}

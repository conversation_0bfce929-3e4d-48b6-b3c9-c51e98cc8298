@extends('layouts.hound')

@section('content')
<div class="col-sm-12">
    <div class="panel panel-default card-view">
        <div class="panel-heading">
            <div class="pull-left">
                <h6 class="panel-title txt-dark">{{ trans('common.edit') }} ProgramLevel #{{ $programlevel->id }}</h6>
            </div>
            <a href="{{ url('/workplace/education/program-levels') }}" class="pull-right" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
            <div class="clearfix"></div>
        </div>
        <div class="panel-wrapper collapse in">
            <div class="panel-body">
                @if ($errors->any())
                    <ul class="alert alert-danger">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                @endif

                {!! Form::model($programlevel, [
                    'method' => 'PATCH',
                    'url' => ['/workplace/education/program-levels', $programlevel->id],
                    'class' => 'form-horizontal',
                    'files' => true
                ]) !!}

                @include ('examinationcertification::program-levels.form', ['submitButtonText' => 'Update'])

                {!! Form::close() !!}

            </div>
        </div>
    </div>
</div>
@endsection

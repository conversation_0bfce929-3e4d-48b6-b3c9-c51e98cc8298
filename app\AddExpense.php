<?php

namespace App;

use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;

class AddExpense extends Model
{
    public function expenseHead(){
    	return $this->belongsTo('App\ExpenseHead', 'expense_head_id', 'id');
    }

    public function ACHead(){
    	return $this->belongsTo('App\ChartOfAccount','expense_head_id', 'id');
    }

    public function account(){
    	return $this->belongsTo('App\BankAccount', 'account_id', 'id');
    }

    public function paymentMethod(){
    	return $this->belongsTo('App\PaymentMethhod', 'payment_method_id', 'id');
    }

    public function scopeAddExpense($query,$date_from,$date_to,$payment_method){
    	return $query->where('date', '>=', $date_from)
        ->where('date', '<=', $date_to)
        ->where('active_status', 1)
        
        ->where('payment_method_id',$payment_method);
    }


}

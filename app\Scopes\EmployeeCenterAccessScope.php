<?php
// app/Scopes/EmployeeCenterAccessScope.php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class EmployeeCenterAccessScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        // Get the authenticated employee from the 'employee' guard.
        $employee = Auth::guard('employee')->user();
        if (!$employee) {
            return;
        }
        // For a managing director, do not apply any filtering.
        if ($employee->hasRole('managing-director_' . config('organization_id') . '_')) {
            return;
        }
        // Otherwise, filter to centers where the employee is attached.
        $builder->whereHas('employee', function ($query) use ($employee) {
            $query->where('id', $employee->id);
        });
    }
}

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers;

use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Modules\JobSeeker\Entities\JobSeeker;

final class EmailVerificationController extends Controller
{
    /**
     * Display the email verification notice.
     */
    public function notice(): View
    {
        $jobSeeker = Auth::guard('job_seeker')->user();
        
        return view('modules.jobseeker.auth.verify-email', [
            'jobSeeker' => $jobSeeker
        ]);
    }

    /**
     * Mark the authenticated job seeker's email address as verified.
     */
    public function verify(Request $request): RedirectResponse
    {
        $jobSeeker = JobSeeker::findOrFail($request->route('id'));

        // Verify the hash matches the job seeker's email
        if (! hash_equals((string) $request->route('hash'), sha1($jobSeeker->getEmailForVerification()))) {
            Log::warning('JobSeeker email verification failed - invalid hash', [
                'job_seeker_id' => $jobSeeker->id,
                'provided_hash' => $request->route('hash'),
                'expected_hash' => sha1($jobSeeker->getEmailForVerification())
            ]);
            
            return redirect()->route('jobseeker.login')
                ->withErrors(['error' => 'Invalid verification link. Please request a new verification email.']);
        }

        // Check if already verified
        if ($jobSeeker->hasVerifiedEmail()) {
            Log::info('JobSeeker email already verified', ['job_seeker_id' => $jobSeeker->id]);
            
            return redirect()->route('jobseeker.login')
                ->with('success', 'Your email is already verified. You can now log in.');
        }

        // Mark as verified
        if ($jobSeeker->markEmailAsVerified()) {
            event(new Verified($jobSeeker));
            
            Log::info('JobSeeker email successfully verified', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email
            ]);
            
            // Auto-login the user after verification
            Auth::guard('job_seeker')->login($jobSeeker);
            
            return redirect()->intended(route('jobseeker.dashboard'))
                ->with('success', 'Email verified successfully! Welcome to your dashboard.');
        }

        Log::error('Failed to mark JobSeeker email as verified', [
            'job_seeker_id' => $jobSeeker->id,
            'email' => $jobSeeker->email
        ]);
        
        return redirect()->route('jobseeker.login')
            ->withErrors(['error' => 'Failed to verify email. Please try again or contact support.']);
    }

    /**
     * Send a new email verification notification.
     */
    public function send(Request $request): RedirectResponse
    {
        $jobSeeker = Auth::guard('job_seeker')->user();
        
        if (!$jobSeeker) {
            return redirect()->route('jobseeker.login')
                ->withErrors(['error' => 'Please log in to resend verification email.']);
        }

        if ($jobSeeker->hasVerifiedEmail()) {
            return redirect()->route('jobseeker.dashboard')
                ->with('success', 'Your email is already verified.');
        }

        try {
            $jobSeeker->sendEmailVerificationNotification();
            
            Log::info('JobSeeker verification email resent', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email
            ]);
            
            return back()->with('success', 'Verification email sent! Please check your inbox.');
            
        } catch (\Exception $e) {
            Log::error('Failed to resend JobSeeker verification email', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
                'error' => $e->getMessage()
            ]);
            
            return back()->withErrors(['error' => 'Failed to send verification email. Please try again later.']);
        }
    }
} 
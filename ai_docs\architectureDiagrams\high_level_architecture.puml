@startuml High Level System Architecture

!theme vibrant

actor User
node "Web Browser" as Browser
cloud "Internet" as Internet

package "Laravel Application (Itqan)" {
    node "Web Server (Nginx/Apache)" as WebServer
    node "PHP-FPM" as PHP
    frame "Laravel Core" {
        component "HTTP Kernel" as Kernel
        component "Router" as Router
        component "Controllers" as Controllers
        component "Middleware" as Middleware
        component "Services" as Services
        component "Models" as Models
        folder "Modules" as Modules <<laravel-modules>>
    }
    database "Database (MySQL)" as DB
    node "Cache (Redis/Memcached)" as Cache
    node "Queue (Redis/DB)" as Queue
    node "File Storage" as Storage
}

User --> Browser : Interacts with
Browser -> Internet : HTTP Request
Internet -> WebServer : Receives Request

WebServer -> PHP : Forwards Request
PHP -> Kernel : Handles Request Lifecycle
Kernel -> Middleware : Processes Request/Response
Kernel -> Router : Routes Request
Router -> Controllers : Dispatches to Controller Action
Controllers -> Services : Uses Business Logic
Controllers -> Models : Interacts with DB through Eloquent
Services -> Models
Models -> DB : Reads/Writes Data
Models -> Cache : Reads/Writes Cache Data
Services -> Queue : Dispatches Jobs
Controllers -> Storage : Interacts with Files

Queue -> Kernel : Processes Background Jobs (via Worker)

@enduml 
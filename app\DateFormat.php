<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\DateFormat
 *
 * @property int $id
 * @property string|null $format
 * @property string|null $normal_view
 * @property int $active_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $organization_id
 * @method static \Illuminate\Database\Eloquent\Builder|DateFormat newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DateFormat newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DateFormat query()
 * @method static \Illuminate\Database\Eloquent\Builder|DateFormat whereActiveStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DateFormat whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DateFormat whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DateFormat whereFormat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DateFormat whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DateFormat whereNormalView($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DateFormat whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DateFormat whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DateFormat whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class DateFormat extends Model
{
    //
}

<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;

class LibraryMember extends Model
{
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);


    }
    public function roles(){
		return $this->belongsTo('App\Role', 'member_type', 'id');
	}
	public function studentDetails(){
		return $this->belongsTo('App\Student', 'documentable_id', 'user_id');
	}
	public function staffDetails(){
		return $this->belongsTo('App\Employee', 'documentable_id', 'user_id');
	}
	public function parentsDetails(){
		return $this->belongsTo('App\Guardian', 'documentable_id', 'user_id');
	}
	public function memberTypes(){
		return $this->belongsTo('App\Role', 'member_type', 'id');
	}
}

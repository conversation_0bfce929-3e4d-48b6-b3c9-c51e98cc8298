<?php

namespace Modules\MenuManage\Entities;

use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\RolePermission\Entities\InfixModuleStudentParentInfo;
use Spatie\Permission\Models\Role;

class UserMenu extends Model
{
    use HasFactory;

    protected $fillable = []; 

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function menuName(){
        return $this->belongsTo('Modules\MenuManage\Entities\Sidebar','parent_id','infix_module_id');
    }

    public static function childMenu($id){
        $user = Auth::user();
        $roleId = $user->roles->first()->id ?? null;
        
        return UserMenu::where('parent_id', $id)
                      ->where('module_id', '!=', $id)
                      ->where('active_status', 1)
                      ->where('user_id', $user->id)
                      ->where('role_id', $roleId)
                      ->orderBy('id', 'ASC')                        
                      ->get();
    }

    public function childMenuName(){
       return $this->belongsTo('Modules\MenuManage\Entities\Sidebar','module_id','infix_module_id');
   }

   public  function subModule(){
        return $this->belongsTo('Modules\RolePermission\Entities\InfixModuleInfo','module_id','id');

   }


   public static function studentParent($id){
        $user = Auth::user();
        $roleName = $user->roles->first()->name ?? '';
        
        $userType = match($roleName) {
            'Student' => 1,
            'Parent' => 2,
            default => null
        };
        
        if (!$userType) {
            return null;
        }
        
        return InfixModuleStudentParentInfo::where('module_id', $id)
                                          ->where('user_type', $userType)
                                          ->first();
    }

    public static function studentParentSubMenu($id){
        $user = Auth::user();
        $roleName = $user->roles->first()->name ?? '';
        
        $userType = match($roleName) {
            'Student' => 1,
            'Parent' => 2,
            default => null
        };
        
        if (!$userType) {
            return null;
        }
        
        return InfixModuleStudentParentInfo::where('id', $id)
                                          ->where('user_type', $userType)
                                          ->first();
    }

    public static function preAssignChild($parent_id){
        $user = Auth::user();
        $roleName = $user->roles->first()->name ?? '';
        
        $userType = match($roleName) {
            'Student' => 1,
            'Parent' => 2,
            default => null
        };
        
        if (!$userType) {
            return collect();
        }
        
        return InfixModuleStudentParentInfo::where('parent_id', $parent_id)
                                          ->where('route', '!=', '')
                                          ->where('module_id', '!=', 1)
                                          ->where('user_type', $userType)
                                          ->get();
    }
}

<script type="text/javascript">var plugin_path = '/assets/templates/itqan/plugins/';</script>
<script type="text/javascript" src="{{URL::to('assets/templates/itqan/plugins/jquery/jquery-2.2.3.min.js')}}"></script>

<script type="text/javascript" src="{{URL::to('assets/templates/itqan/js/scripts.js')}}"></script>


<!-- REVOLUTION SLIDER -->
<script type="text/javascript"
        src="{{URL::to('assets/templates/itqan/plugins/slider.revolution/js/jquery.themepunch.tools.min.js')}}"></script>
<script type="text/javascript"
        src="{{URL::to('assets/templates/itqan/plugins/slider.revolution/js/jquery.themepunch.revolution.min.js')}}"></script>
<script type="text/javascript" src="{{URL::to('assets/templates/itqan/js/view/demo.revolution_slider.js')}}"></script>


<!--AJAX ACTIONS-->
<script type="text/javascript" src="{{URL::to('assets/templates/itqan/ajax/newsletter.js')}}"></script>
<script type="text/javascript" src="{{URL::to('assets/templates/itqan/ajax/contact.js')}}"></script>


<script type="text/javascript" src="{{URL::to('global_assets/js/islamic_date.js')}}"></script>


<!--  WIZARD -->
<script src="{{URL::to('assets/templates/itqan/wizard/js/jquery.backstretch.min.js')}}"></script>
<script src="{{URL::to('assets/templates/itqan/wizard/js/retina-1.1.0.min.js')}}"></script>
<script src="{{URL::to('assets/templates/itqan/wizard/js/scripts.js')}}"></script>
<!--[if lt IE 10]>
<script src="{{URL::to('assets/templates/itqan/wizard/js/placeholder.js')}}"></script>
<![endif]-->


<!-- boost select -->
<script src="{{URL::to('assets/templates/itqan/boost_select/js/bootstrap-select.js')}}"></script>
<script src="{{URL::to('assets/templates/itqan/boost_select/js/i18n/defaults-ar_AR.min.js')}}"></script>

<!-- bootstrap notifiy -->
<script src="{{URL::to('assets/templates/itqan/notify/jquery.toaster.js')}}"></script>


<!--  custom scroll -->
<script src="{{URL::to('assets/templates/itqan/custom_scroll/jquery.mCustomScrollbar.concat.min.js')}}"></script>


<!-- Get date Arabic or English -->
<div data="{{App::getLocale()}}" id="locale_info"></div>
<script type="text/javascript">

    $(document).ready(function () {
        //* Get date Arabic or English *//
        var local = "";
        local = $("#locale_info").attr('data');
        $("#date_placeholder").text(writeIslamicDate(-1, local));
        @yield('test')

         $("#comments_filed").mCustomScrollbar({
            theme: "rounded-dots",
            scrollInertia: 400
        });
    });
</script>




<?php

namespace App;

use Illuminate\Database\Eloquent\Model;


class AdmissionInterview extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'admission_interviews';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['interview_time'
                            , 'location'
                            , 'admission_id'
                            , 'program_id'
                            , 'created_by'
                            , 'notes'
                            , 'interviewInvitationNotes'
                            , 'status'
                            , 'interview_invitation_token'
                            , 'confirmed_at'
                            , 'invitation_confirmed'
                            , 'confirmation_via',
        'updated_by'
                        ];

    public function interviewers()
    {
        return $this->belongsToMany('App\Employee' , 'admission_interviewers')->withPivot('id' , 'attended');
    }




    public function reports(){


        return $this->hasOne(AdmissionInterviewReport::class,'admission_interview_id');
    }

    public function program()
    {
        return $this->belongsTo('App\Program');     
    }    

}

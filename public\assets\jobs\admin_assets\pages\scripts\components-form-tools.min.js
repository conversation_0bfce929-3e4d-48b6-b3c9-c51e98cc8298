var ComponentsFormTools=function(){var e=function(){$("#maxlength_defaultconfig").maxlength({limitReachedClass:"label label-danger"}),$("#maxlength_thresholdconfig").maxlength({limitReachedClass:"label label-danger",threshold:20}),$("#maxlength_alloptions").maxlength({alwaysShow:!0,warningClass:"label label-success",limitReachedClass:"label label-danger",separator:" out of ",preText:"You typed ",postText:" chars available.",validate:!0}),$("#maxlength_textarea").maxlength({limitReachedClass:"label label-danger",alwaysShow:!0}),$("#maxlength_placement").maxlength({limitReachedClass:"label label-danger",alwaysShow:!0,placement:App.isRTL()?"top-right":"top-left"})},s=function(){var e=!1,s=$("#password_strength");s.keydown(function(){e===!1&&(s.pwstrength({raisePower:1.4,minChar:8,verdicts:["Weak","Normal","Medium","Strong","Very Strong"],scores:[17,26,40,50,60]}),s.pwstrength("addRule","demoRule",function(e,s,a){return s.match(/[a-z].[0-9]/)&&a},10,!0),e=!0)})},a=function(){var e=$("#username1_input");$("#username1_checker").click(function(s){var a=$(this);if(""===e.val())return e.closest(".form-group").removeClass("has-success").addClass("has-error"),a.popover("destroy"),a.popover({placement:App.isRTL()?"left":"right",html:!0,container:"body",content:"Please enter a username to check its availability."}),a.data("bs.popover").tip().addClass("error"),App.setLastPopedPopover(a),a.popover("show"),void s.stopPropagation();var o=$(this);o.attr("disabled",!0),e.attr("readonly",!0).attr("disabled",!0).addClass("spinner"),$.post("../demo/username_checker.php",{username:e.val()},function(s){o.attr("disabled",!1),e.attr("readonly",!1).attr("disabled",!1).removeClass("spinner"),"OK"==s.status?(e.closest(".form-group").removeClass("has-error").addClass("has-success"),a.popover("destroy"),a.popover({html:!0,placement:App.isRTL()?"left":"right",container:"body",content:s.message}),a.popover("show"),a.data("bs.popover").tip().removeClass("error").addClass("success")):(e.closest(".form-group").removeClass("has-success").addClass("has-error"),a.popover("destroy"),a.popover({html:!0,placement:App.isRTL()?"left":"right",container:"body",content:s.message}),a.popover("show"),a.data("bs.popover").tip().removeClass("success").addClass("error"),App.setLastPopedPopover(a))},"json")})},o=function(){$("#username2_input").change(function(){var e=$(this);return""===e.val()?(e.closest(".form-group").removeClass("has-error").removeClass("has-success"),void $(".fa-check, fa-warning",e.closest(".form-group")).remove()):(e.attr("readonly",!0).attr("disabled",!0).addClass("spinner"),void $.post("../demo/username_checker.php",{username:e.val()},function(s){e.attr("readonly",!1).attr("disabled",!1).removeClass("spinner"),"OK"==s.status?(e.closest(".form-group").removeClass("has-error").addClass("has-success"),$(".fa-warning",e.closest(".form-group")).remove(),e.before('<i class="fa fa-check"></i>'),e.data("bs.popover").tip().removeClass("error").addClass("success")):(e.closest(".form-group").removeClass("has-success").addClass("has-error"),$(".fa-check",e.closest(".form-group")).remove(),e.before('<i class="fa fa-warning"></i>'),e.popover("destroy"),e.popover({html:!0,placement:App.isRTL()?"left":"right",container:"body",content:s.message}),e.popover("show"),e.data("bs.popover").tip().removeClass("success").addClass("error"),App.setLastPopedPopover(e))},"json"))})};return{init:function(){e(),s(),a(),o()}}}();App.isAngularJsApp()===!1&&jQuery(document).ready(function(){ComponentsFormTools.init()});
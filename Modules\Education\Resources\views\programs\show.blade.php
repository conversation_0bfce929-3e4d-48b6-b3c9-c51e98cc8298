@extends('layouts.hound')
@section('mytitle', 'View Education Program')

@section('content')
    <div class="panel panel-default card-view">
        <div class="panel-heading clearfix">
        <h5 class="pull-left">Program {{ $program->title }}</h5>
        <div class="pull-right">
            <a href="{{ url('/workplace/education/programs') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
            <a href="{{ url('/workplace/education/programs/' . $program->id . '/edit') }}" title="Edit program"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>
            {!! Form::open([
                'method'=>'DELETE',
                'url' => ['workplace/education/programs', $program->id],
                'style' => 'display:inline'
            ]) !!}
                {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                        'type' => 'submit',
                        'class' => 'btn btn-danger btn-xs',
                        'title' => 'Delete program',
                        'onclick'=>'return confirm("Confirm delete?")'
                ))!!}
            {!! Form::close() !!}

        </div>  

        
        </div>
        <div class="panel-body">


            <div class="table-responsive">
                <table class="table table-borderless">
                    <tbody>
                        <tr>
                            <th>ID</th><td>{{ $program->id }}</td>
                        </tr>
                        <tr><th> Title </th><td> {{ $program->title }} </td></tr><tr><th> Description </th><td> {{ $program->description }} </td></tr><tr><th> Status </th><td> {{ $program->status }} </td></tr>
                    </tbody>
                </table>
            </div>

        </div>
    </div>

    <div class="panel panel-default card-view">
        <div class="panel-heading">
            <div class="pull-left">
                <h5>Program available in these Centers</h5>
            
            </div>
            <a data-toggle="modal" href='#editCenters' class="btn btn-success btn-sm pull-right"><i class="fa fa-edit" aria-hidden="true"></i>Edit Program Availality</a>
            <div class="clearfix"></div>
        </div>
        <div class="panel-wrapper collapse in">
            <div class="panel-body clearfix">
                <ul>
                    @foreach($program->centers as $center)
                    <li class="">
                        {{ $center->name }}
                    </li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>


    <div class="panel panel-default card-view">
    <div class="panel-heading">
        <div class="pull-left">
            <h5>Program Levels</h5>
        
        </div>
        <a data-toggle="modal" href='#addLevel' class="btn btn-success btn-sm pull-right"><i class="fa fa-plus" aria-hidden="true"></i>Add New Level</a>
        <div class="clearfix"></div>
        <hr>
    </div>
    <div class="panel-wrapper collapse in">
        <div class="panel-body">
            <div class="table-responsive">
                <table class="table table-borderless">
                    <thead>
                        <tr>
                            <th>ID</th><th>Title</th><th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    @foreach($program->levels as $item)
                        <tr>
                            <td>{{ $item->id }}</td>
                            <td>{{ $item->title }}</td>
                            <td>
                                <a href="{{ url('/workplace/education/program-levels/' . $item->id) }}" title="View ProgramLevel"><button class="btn btn-info btn-xs"><i class="fa fa-eye" aria-hidden="true"></i> View</button></a>
                                <a href="{{ url('/workplace/education/program-levels/' . $item->id . '/edit') }}" title="Edit ProgramLevel"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>
                                {!! Form::open([
                                    'method'=>'DELETE',
                                    'url' => ['/workplace/education/program-levels', $item->id],
                                    'style' => 'display:inline'
                                ]) !!}
                                    {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                                            'type' => 'submit',
                                            'class' => 'btn btn-danger btn-xs',
                                            'title' => 'Delete ProgramLevel',
                                            'onclick'=>'return confirm("Confirm delete?")'
                                    )) !!}
                                {!! Form::close() !!}
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="addLevel">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">Add New Level </h4>
            </div>
            <div class="modal-body">
                <div id="form_errors" class="error alert-danger">
                </div>
                {!! Form::open(['url' => '/workplace/education/program-levels', 'class' => 'form-horizontal', 'id' => 'addNewLevel', 'files' => false]) !!}
                
                @include ('education::program-levels.form' , ['program_id' => $program->id])
    
                {!! Form::close() !!}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>

</div>

<div class="modal fade" id="editCenters">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title"> {{ trans('education::actions.edit_program_availabilty_in_centers') }} </h4>
            </div>
            {!! Form::open(['url' => '/workplace/education/program-centers', 'class' => 'form-horizontal', 'id' => 'editProgramCenters', 'files' => false]) !!}
            <div class="modal-body">
                <div id="center_form_errors" class="error alert-danger">
                </div>
                <div class="clearfix">
                {!! Form::hidden('program_id' , $program->id ) !!}
                @foreach($centers as $center)
                    <label>
                    {!! Form::checkbox('program_centers[]' , $center->id , in_array($center->id , $program->centers->pluck('id')->toArray() ) , ['class' => ''])  !!}
                     {{$center->name}}</label>
                @endforeach
                </div>
            </div>
            <div class="modal-footer">
                {!! Form::submit(trans('common.save'), ['class' => 'btn btn-primary']) !!}
            
                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
            </div>
            {!! Form::close() !!}
            
        </div>
    </div>

</div>

@endsection
@section('js')
<script>
    $('form#addNewLevel').submit(function(e){
        e.preventDefault();

        $.ajax({
            type: "post",
            url: '/workplace/education/program-levels',
            data: $(this).serialize(),
            dataType: "json",
            success: function (response) {
                if(response.status = "success"){
                    window.location.reload();
                }
            }
        }).fail (function (req){
            $('#form_errors').text('');
            $('#form_errors').addClass('alert');
            $.each(req.responseJSON , function (index , value) {
                $.each(value, function (key , err) {
                    $('#form_errors').append(err+'<br>');
                })
            })
        });
    })

    $('form#editProgramCenters').submit(function(e){
        e.preventDefault();

        $.ajax({
            type: "post",
            url: '/workplace/education/program-centers',
            data: $(this).serialize(),
            dataType: "json",
            success: function (response) {
                if(response.status = "success"){
                    window.location.reload();
                }
            }
        }).fail (function (req){
            
            $('#center_form_errors').text('');
            $('#center_form_errors').addClass('alert');
            $.each(req.responseJSON , function (index , value) {
                $.each(value, function (key , err) {
                    $('#center_form_errors').append(err+'<br>');
                })
            })
        });
    })
    
    </script>
@endsection

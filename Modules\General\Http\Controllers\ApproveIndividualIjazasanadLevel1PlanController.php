<?php

declare(strict_types=1);

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\AdmissionInterview;
use App\Http\Controllers\Controller;
use App\IjazasanadMemorizationPlan;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

final class ApproveIndividualIjazasanadLevel1PlanController extends Controller
{
    public function __invoke(Request $request)
    {
        try {
            Log::info('Individual Ijazasanad Level 1 Plan Approval Started', [
                'user_id' => auth()->id(),
                'plan_id' => $request->plan_id
            ]);

            $request->validate([
                'plan_id' => 'required|integer|exists:ijazasanad_memorization_plans,id'
            ]);

            DB::beginTransaction();

            $plan = IjazasanadMemorizationPlan::with(['student', 'center'])->find($request->plan_id);

            if (!$plan) {
                return response()->json([
                    'success' => false,
                    'message' => 'Plan not found'
                ], 404);
            }

            // Validate plan data
            if (!$this->validatePlanData($plan)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Plan validation failed: Missing required lesson information'
                ], 400);
            }

            // Update the plan status
            $plan->status = 'active';
            $plan->approved_by = auth()->user()->id;
            $plan->updated_at = Carbon::now();
            $plan->save();

            // Update or create last approved plan for the same student, year and month
            \App\StudentIjazasanadMemorizationLastApprovedPlan::updateOrCreate(
                [
                    'student_id' => $plan->student_id,
                    'plan_year_month_day' => $plan->plan_year_and_month ? Carbon::parse($plan->plan_year_and_month)->format('Y-m-01') : $plan->start_date->format('Y-m-01')
                ],
                [
                    'approved_by' => $plan->approved_by,
                    'from_lesson' => $plan->from_lesson ?? null,
                    'to_lesson' => $plan->to_lesson ?? null,
                    'talqeen_from_lesson' => $plan->talqeen_from_lesson ?? null,
                    'talqeen_to_lesson' => $plan->talqeen_to_lesson ?? null,
                    'revision_from_lesson' => $plan->revision_from_lesson ?? null,
                    'revision_to_lesson' => $plan->revision_to_lesson ?? null,
                    'jazariyah_from_lesson' => $plan->jazariyah_from_lesson ?? null,
                    'jazariyah_to_lesson' => $plan->jazariyah_to_lesson ?? null,
                    'seminars_from_lesson' => $plan->seminars_from_lesson ?? null,
                    'seminars_to_lesson' => $plan->seminars_to_lesson ?? null,
                    'level_id' => $plan->level_id,
                    'updated_at' => Carbon::now(),
                    'plan_year_month_day' => $plan->plan_year_and_month ? Carbon::parse($plan->plan_year_and_month)->format('Y-m-01') : $plan->start_date->format('Y-m-01')
                ]
            );

            // Update admission interview status
            $admissionId = Admission::where('student_id', $plan->student_id)->first()->id;
            AdmissionInterview::where('admission_id', $admissionId)->update([
                'status' => 'interviewed',
                'confirmed_at' => Carbon::now(),
                'updated_by' => auth()->user()->id
            ]);

            DB::commit();

            // Calculate waiting approval counts
            $hefzPlansWaiting_approval = \App\StudentHefzPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();

            $RevisionPlanWaiting_approval = \App\StudentRevisionPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();

            $NouranyaPlanWaitingApproval = \App\StudentNouranyaPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())
                ->count();

            $ijazasanadMemorizationPlanWaitingApproval = \App\IjazasanadMemorizationPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();

            $plans_waiting_approval = $hefzPlansWaiting_approval + $RevisionPlanWaiting_approval + $NouranyaPlanWaitingApproval + $ijazasanadMemorizationPlanWaitingApproval;

            Log::info('Individual Ijazasanad Level 1 Plan Approval Completed Successfully', [
                'user_id' => auth()->id(),
                'plan_id' => $request->plan_id,
                'student_id' => $plan->student_id,
                'student_name' => $plan->student->full_name
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Ijazasanad Level 1 plan approved successfully for ' . $plan->student->full_name,
                'plansWaitingApprovalCountWidget' => $plans_waiting_approval,
                'successful_approvals' => 1,
                'failed_approvals' => 0,
                'successful_students' => [[
                    'student_id' => $plan->student_id,
                    'student_name' => $plan->student->full_name,
                    'plan_id' => $plan->id
                ]],
                'failed_students' => []
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            Log::error('Individual Ijazasanad Level 1 Plan Approval Failed', [
                'user_id' => auth()->id(),
                'plan_id' => $request->plan_id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while approving the plan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate plan data before approval
     */
    private function validatePlanData($plan): bool
    {
        // Check if the plan has the required lesson information based on level
        $hasBasicLessons = !empty($plan->from_lesson) && !empty($plan->to_lesson);
        $hasTalqeenLessons = !empty($plan->talqeen_from_lesson) && !empty($plan->talqeen_to_lesson);
        $hasRevisionLessons = !empty($plan->revision_from_lesson) && !empty($plan->revision_to_lesson);
        $hasJazariyahLessons = !empty($plan->jazariyah_from_lesson) && !empty($plan->jazariyah_to_lesson);
        $hasSeminarsLessons = !empty($plan->seminars_from_lesson) && !empty($plan->seminars_to_lesson);

        return $hasBasicLessons || $hasTalqeenLessons || $hasRevisionLessons || $hasJazariyahLessons || $hasSeminarsLessons;
    }
} 
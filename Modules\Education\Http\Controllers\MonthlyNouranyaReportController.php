<?php

declare(strict_types=1);

namespace Modules\Education\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use App\Classes;
use App\Student;
use App\StudentNouranyaPlan;
use App\StudentNouranyaReport;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

final class MonthlyNouranyaReportController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        try {
            $classId = (int)$request->input('classId');
            $monthYear = $request->input('classDate');

            if (!$classId || !$monthYear) {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            // Parse month and year
            $date = Carbon::createFromFormat('M Y', $monthYear);
            $month = $date->month;
            $year = $date->year;

            // Get students with the same ordering as MonthlyPlanController
            $students = Student::whereHas('joint_classes', function ($query) use ($classId) {
                $query->where('class_id', $classId);
            })
            ->where('status', 'active')
            ->orderBy('full_name', 'asc') // Same ordering as MonthlyPlanController
            ->get();

            $data = [];
            foreach ($students as $index => $student) {
                $attendanceData = $this->calculateAttendance($student->id, $classId, $month, $year);
                $achievement = $this->calculateAchievement($student->id, $classId, $month, $year);
                $entryData = $this->getEntryData($student->id, $classId, $month, $year);
                $monthlyPlan = $this->getMonthlyPlan($student->id, $month, $year);
                $monthlyAchievement = $this->getMonthlyAchievement($student->id, $classId, $month, $year);

                $data[] = [
                    'DT_RowIndex' => $index + 1,
                    'student_id' => $student->id, // Add student ID for row identification
                    'student' => $this->formatStudentName($student),
                    'entry1' => $entryData['entry1'],
                    'entry2' => $entryData['entry2'],
                    'entry3' => $entryData['entry3'],
                    'entry4' => $entryData['entry4'],
                    'monthlyPlan' => $monthlyPlan,
                    'monthlyAchievement' => $monthlyAchievement,
                    'attendancePercentage' => $this->formatProgressBarWithPopup($attendanceData['percentage'], '#1fff0f', $attendanceData['details']),
                    'achievementPercentage' => $this->formatProgressBarWithPopup($achievement, '#1fff0f', $this->getAchievementDetails($student->id, $classId, $month, $year))
                ];
            }

            return DataTables::of($data)
                ->rawColumns(['student', 'attendancePercentage', 'achievementPercentage', 'entry1', 'entry2', 'entry3', 'entry4', 'monthlyPlan', 'monthlyAchievement'])
                ->toJson();

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    private function getMonthlyPlan(int $studentId, int $month, int $year): string
    {
        // Get the student's Nouranya plan for the specified month/year
        $plan = StudentNouranyaPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year) {
                $query->whereYear('start_date', $year)
                      ->whereMonth('start_date', $month);
            })
            ->orWhere(function ($query) use ($month, $year) {
                $query->whereYear('created_at', $year)
                      ->whereMonth('created_at', $month);
            })
            ->first();

        if (!$plan) {
            return '—';
        }

        $planContent = '';

        // Build the plan content similar to nouranyaCreate.blade.php
        if (!empty($plan->from_lesson) && 
            !empty($plan->from_lesson_line_number) && 
            !empty($plan->to_lesson) && 
            !empty($plan->to_lesson_line_number)) {
            $planContent .= "<div>{$plan->from_lesson}.{$plan->from_lesson_line_number} - {$plan->to_lesson}.{$plan->to_lesson_line_number}</div>";
        }

        // Add Talaqqi information if available
        if (!empty($plan->talaqqi_from_lesson) && !empty($plan->talaqqi_to_lesson)) {
            $planContent .= "<div>Talaqqi: {$plan->talaqqi_from_lesson} - {$plan->talaqqi_to_lesson}</div>";
        }

        // Add Talqeen information if available
        if (!empty($plan->talqeen_from_lesson) && !empty($plan->talqeen_to_lesson)) {
            $talqeenLine = !empty($plan->talqeen_from_line_number) ? ":{$plan->talqeen_from_line_number}" : '';
            $planContent .= "<div>Talqeen: {$plan->talqeen_from_lesson}{$talqeenLine} - {$plan->talqeen_to_lesson}</div>";
        }

        return $planContent ?: '—';
    }

    private function getEntryData(int $studentId, int $classId, int $month, int $year): array
    {
        // Get the first and latest reports for the month to show progression
        $firstReport = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('created_at', 'asc')
            ->first();

        $latestReport = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('created_at', 'desc')
            ->first();

        // If we have reports, use the first for starting point and latest for ending point
        if ($firstReport && $latestReport) {
            return [
                'entry1' => $firstReport->from_lesson ?? '—',
                'entry2' => $firstReport->from_lesson_line_number ?? '—',
                'entry3' => $latestReport->to_lesson ?? '—',
                'entry4' => $latestReport->to_lesson_line_number ?? '—'
            ];
        }

        // Fallback to plan data if no reports
        $plan = StudentNouranyaPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year) {
                $query->whereYear('start_date', $year)
                      ->whereMonth('start_date', $month);
            })
            ->first();

        if ($plan) {
            return [
                'entry1' => $plan->from_lesson ?? '—',
                'entry2' => $plan->from_lesson_line_number ?? '—',
                'entry3' => $plan->to_lesson ?? '—',
                'entry4' => $plan->to_lesson_line_number ?? '—'
            ];
        }

        return [
            'entry1' => '—',
            'entry2' => '—',
            'entry3' => '—',
            'entry4' => '—'
        ];
    }

    private function getMonthlyAchievement(int $studentId, int $classId, int $month, int $year): string
    {
        // Get all reports for the student in the specified month/year
        $reports = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereNotNull('from_lesson')
            ->whereNotNull('to_lesson')
            ->get();

        if ($reports->isEmpty()) {
            return '—';
        }

        // Collect all unique lessons covered
        $uniqueLessons = collect();
        
        foreach ($reports as $report) {
            $fromLesson = (int) $report->from_lesson;
            $toLesson = (int) $report->to_lesson;
            
            // Ensure ascending order: swap if from > to
            if ($fromLesson > $toLesson) {
                $temp = $fromLesson;
                $fromLesson = $toLesson;
                $toLesson = $temp;
            }
            
            // Add all lessons in the range (inclusive)
            for ($lesson = $fromLesson; $lesson <= $toLesson; $lesson++) {
                $uniqueLessons->push($lesson);
            }
        }
        
        // Get unique lesson count
        $uniqueLessonCount = $uniqueLessons->unique()->count();
        
        // Get lesson range for display (ensuring ascending order)
        $firstReport = $reports->sortBy('created_at')->first();
        $lastReport = $reports->sortByDesc('created_at')->first();
        
        $achievementContent = '';
        
        if ($firstReport && $lastReport) {
            // Get the overall range from first to last report
            $overallStartLesson = (int) $firstReport->from_lesson;
            $overallEndLesson = (int) $lastReport->to_lesson;
            
            // If we have the same report (only one report), use its range
            if ($firstReport->id === $lastReport->id) {
                $overallStartLesson = (int) $firstReport->from_lesson;
                $overallEndLesson = (int) $firstReport->to_lesson;
            } else {
                // For multiple reports, find the actual min and max lessons covered
                $allLessonsArray = $uniqueLessons->unique()->sort()->values()->toArray();
                if (!empty($allLessonsArray)) {
                    $overallStartLesson = min($allLessonsArray);
                    $overallEndLesson = max($allLessonsArray);
                }
            }
            
            // Ensure ascending order for display
            if ($overallStartLesson > $overallEndLesson) {
                $temp = $overallStartLesson;
                $overallStartLesson = $overallEndLesson;
                $overallEndLesson = $temp;
            }
            
            // Format the display
            if ($overallStartLesson === $overallEndLesson) {
                $achievementContent = "Lesson {$overallStartLesson}";
            } else {
                $achievementContent = "Lessons {$overallStartLesson} - {$overallEndLesson}";
            }
            
            // Add unique lesson count prominently
            if ($uniqueLessonCount > 0) {
                $achievementContent = "<strong>{$uniqueLessonCount} lessons</strong><br><small class='text-muted'>{$achievementContent}</small>";
            }
        }

        return $achievementContent ?: '—';
    }

    private function calculateAttendance(int $studentId, int $classId, int $month, int $year): array
    {
        // Get total scheduled classes for the month
        $class = Classes::find($classId);
        if (!$class || !$class->timetable) {
            return [
                'percentage' => 0.0,
                'details' => [
                    'total_scheduled' => 0,
                    'attended' => 0,
                    'late' => 0,
                    'absent' => 0,
                    'error' => 'No class timetable found'
                ]
            ];
        }

        $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
        if ($totalClasses <= 0) {
            return [
                'percentage' => 0.0,
                'details' => [
                    'total_scheduled' => 0,
                    'attended' => 0,
                    'late' => 0,
                    'absent' => 0,
                    'error' => 'No scheduled classes for this month'
                ]
            ];
        }

        // Count attendance by type
        $attendanceStats = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->selectRaw('
                COUNT(CASE WHEN attendance_id = 2 THEN 1 END) as on_time,
                COUNT(CASE WHEN attendance_id = 1 THEN 1 END) as late,
                COUNT(CASE WHEN attendance_id = 3 THEN 1 END) as absent,
                COUNT(*) as total_reports
            ')
            ->first();

        $onTime = $attendanceStats->on_time ?? 0;
        $late = $attendanceStats->late ?? 0;
        $absent = $attendanceStats->absent ?? 0;
        $totalReports = $attendanceStats->total_reports ?? 0;
        
        // Calculate attended (on-time + late)
        $attended = $onTime + $late;
        
        // Calculate percentage
        $percentage = $totalClasses > 0 ? min(100.0, ($attended / $totalClasses) * 100) : 0.0;

        return [
            'percentage' => $percentage,
            'details' => [
                'total_scheduled' => $totalClasses,
                'attended' => $attended,
                'on_time' => $onTime,
                'late' => $late,
                'absent' => $absent,
                'total_reports' => $totalReports,
                'calculation' => "({$attended} attended ÷ {$totalClasses} scheduled) × 100 = " . round($percentage, 2) . "%"
            ]
        ];
    }

    private function calculateAchievement(int $studentId, int $classId, int $month, int $year): float
    {
        // Get the student's plan for the month
        $plan = StudentNouranyaPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year) {
                $query->whereYear('start_date', $year)
                      ->whereMonth('start_date', $month);
            })
            ->first();

        if (!$plan) {
            return 0.0;
        }

        // Get actual achievements from reports
        $reports = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get();

        if ($reports->isEmpty()) {
            return 0.0;
        }

        // Simple calculation: if student has reports, they're making progress
        // This can be enhanced based on specific business logic
        $totalReports = $reports->count();
        $expectedReports = 20; // Assuming ~20 working days per month

        return min(100.0, ($totalReports / $expectedReports) * 100);
    }

    private function formatStudentName(Student $student): string
    {
        $studentName = ucfirst($student->full_name);
        $studentProfileUrl = route('students.show', ['id' => $student->user_id]);
        return '<a style="color:#b4eeb0; padding: 2px; display: inline-block; transition: all 0.3s ease;" data-id="'.$student->id.'" class="section class-link" target="_blank" href="' . $studentProfileUrl . '">' . $studentName . '</a>';
    }

    private function formatProgressBar(float $percentage, string $color): string
    {
        $result = round($percentage, 2);
        return '<div class="progress" style="position: relative;">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: ' . $result . '%; background-color: ' . $color . ';" aria-valuenow="' . $result . '" aria-valuemin="0" aria-valuemax="100"></div>
            <span style="position: absolute; width: 100%; text-align: center; line-height: 20px; color: #333; font-weight: bold; font-size: 12px;">' . $result . '%</span>
        </div>';
    }

    private function formatProgressBarWithPopup(float $percentage, string $color, array $details): string
    {
        $result = round($percentage, 2);
        $detailsJson = htmlspecialchars(json_encode($details), ENT_QUOTES, 'UTF-8');
        
        // Determine if this is achievement or attendance based on details structure
        $progressClass = isset($details['total_reports']) ? 'achievement-progress' : 'attendance-progress';
        $dataAttribute = isset($details['total_reports']) ? 'data-achievement-details' : 'data-attendance-details';
        
        return '<div class="progress ' . $progressClass . '" style="position: relative; cursor: pointer;" 
                     data-toggle="tooltip" 
                     data-placement="top" 
                     data-html="true"
                     ' . $dataAttribute . '=\'' . $detailsJson . '\'>
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" 
                 style="width: ' . $result . '%; background-color: ' . $color . ';" 
                 aria-valuenow="' . $result . '" aria-valuemin="0" aria-valuemax="100">
                ' . $result . '%
            </div>
          </div>';
    }

    private function getAchievementDetails(int $studentId, int $classId, int $month, int $year): array
    {
        // Get all reports for the student in the specified month/year
        $reports = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get();

        // Get the student's plan for comparison
        $plan = StudentNouranyaPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year) {
                $query->whereYear('start_date', $year)
                      ->whereMonth('start_date', $month);
            })
            ->first();

        $totalReports = $reports->count();
        $completedReports = $reports->where('from_lesson', '!=', null)
                                  ->where('to_lesson', '!=', null)
                                  ->count();

        // Calculate unique lessons
        $uniqueLessons = collect();
        foreach ($reports as $report) {
            if ($report->from_lesson && $report->to_lesson) {
                $fromLesson = (int) $report->from_lesson;
                $toLesson = (int) $report->to_lesson;
                
                // Ensure ascending order
                if ($fromLesson > $toLesson) {
                    $temp = $fromLesson;
                    $fromLesson = $toLesson;
                    $toLesson = $temp;
                }
                
                for ($lesson = $fromLesson; $lesson <= $toLesson; $lesson++) {
                    $uniqueLessons->push($lesson);
                }
            }
        }
        $uniqueLessonCount = $uniqueLessons->unique()->count();

        // Expected reports (assuming ~20 working days per month)
        $expectedReports = 20;
        $achievementPercentage = $totalReports > 0 ? min(100.0, ($totalReports / $expectedReports) * 100) : 0.0;

        // Get lesson range
        $minLesson = $uniqueLessons->unique()->min();
        $maxLesson = $uniqueLessons->unique()->max();
        $lessonRange = ($minLesson && $maxLesson) ? 
            ($minLesson === $maxLesson ? "Lesson {$minLesson}" : "Lessons {$minLesson} - {$maxLesson}") : 
            'No lessons recorded';

        // Plan comparison
        $planInfo = 'No plan found';
        if ($plan) {
            $planStart = $plan->from_lesson ?? '—';
            $planEnd = $plan->to_lesson ?? '—';
            $planInfo = "Planned: Lessons {$planStart} - {$planEnd}";
        }

        return [
            'total_reports' => $totalReports,
            'completed_reports' => $completedReports,
            'incomplete_reports' => $totalReports - $completedReports,
            'unique_lessons' => $uniqueLessonCount,
            'lesson_range' => $lessonRange,
            'expected_reports' => $expectedReports,
            'achievement_percentage' => round($achievementPercentage, 2),
            'plan_info' => $planInfo,
            'calculation' => "({$totalReports} reports ÷ {$expectedReports} expected) × 100 = " . round($achievementPercentage, 2) . "%"
        ];
    }
} 
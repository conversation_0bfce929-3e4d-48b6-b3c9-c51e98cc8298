<?php

declare(strict_types=1);

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * Strong Password Validation Rule
 * 
 * Enforces strong password requirements including:
 * - Minimum length
 * - Character variety
 * - No common passwords
 * - No personal information
 */
final class StrongPassword implements Rule
{
    private array $personalInfo;
    private array $errors = [];

    /**
     * Create a new rule instance.
     */
    public function __construct(array $personalInfo = [])
    {
        $this->personalInfo = array_filter(array_map('strtolower', $personalInfo));
    }

    /**
     * Determine if the validation rule passes.
     */
    public function passes($attribute, $value): bool
    {
        $this->errors = [];
        
        if (!is_string($value)) {
            $this->errors[] = 'Password must be a string.';
            return false;
        }

        // Check minimum length
        if (strlen($value) < 8) {
            $this->errors[] = 'Password must be at least 8 characters long.';
        }

        // Check for uppercase letter
        if (!preg_match('/[A-Z]/', $value)) {
            $this->errors[] = 'Password must contain at least one uppercase letter.';
        }

        // Check for lowercase letter
        if (!preg_match('/[a-z]/', $value)) {
            $this->errors[] = 'Password must contain at least one lowercase letter.';
        }

        // Check for number
        if (!preg_match('/[0-9]/', $value)) {
            $this->errors[] = 'Password must contain at least one number.';
        }

        // Check for special character
        if (!preg_match('/[^A-Za-z0-9]/', $value)) {
            $this->errors[] = 'Password must contain at least one special character.';
        }

        // Check against common passwords
        if ($this->isCommonPassword($value)) {
            $this->errors[] = 'Password is too common. Please choose a more unique password.';
        }

        // Check against personal information
        if ($this->containsPersonalInfo($value)) {
            $this->errors[] = 'Password cannot contain your personal information.';
        }

        // Check for sequential characters
        if ($this->hasSequentialCharacters($value)) {
            $this->errors[] = 'Password cannot contain sequential characters (e.g., 123, abc).';
        }

        // Check for repeated characters
        if ($this->hasRepeatedCharacters($value)) {
            $this->errors[] = 'Password cannot contain more than 2 consecutive identical characters.';
        }

        return empty($this->errors);
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return implode(' ', $this->errors);
    }

    /**
     * Check if password is commonly used.
     */
    private function isCommonPassword(string $password): bool
    {
        $commonPasswords = [
            'password', 'password123', '123456', '123456789', 'qwerty',
            'abc123', 'password1', 'admin', 'letmein', 'welcome',
            'monkey', '1234567890', 'dragon', 'master', 'sunshine',
            'princess', 'football', 'charlie', 'aa123456', 'donald',
            'password12', 'qwerty123', 'welcome123', 'admin123',
            '12345678', '1234567', '12345', '1234', '123',
        ];

        return in_array(strtolower($password), $commonPasswords, true);
    }

    /**
     * Check if password contains personal information.
     */
    private function containsPersonalInfo(string $password): bool
    {
        $passwordLower = strtolower($password);
        
        foreach ($this->personalInfo as $info) {
            if (empty($info) || strlen($info) < 3) {
                continue; // Skip empty or very short personal info
            }
            
            // Check if personal info is contained in password
            if (str_contains($passwordLower, $info)) {
                return true;
            }
            
            // Check if password is contained in personal info (reverse check)
            if (str_contains($info, $passwordLower)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for sequential characters.
     */
    private function hasSequentialCharacters(string $password): bool
    {
        $sequences = [
            '0123456789', 'abcdefghijklmnopqrstuvwxyz', 'qwertyuiop',
            'asdfghjkl', 'zxcvbnm', '9876543210', 'zyxwvutsrqponmlkjihgfedcba',
        ];

        $passwordLower = strtolower($password);
        
        foreach ($sequences as $sequence) {
            // Check for 3+ consecutive characters from any sequence
            for ($i = 0; $i <= strlen($sequence) - 3; $i++) {
                $substr = substr($sequence, $i, 3);
                if (str_contains($passwordLower, $substr)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check for repeated characters.
     */
    private function hasRepeatedCharacters(string $password): bool
    {
        // Check for 3 or more consecutive identical characters
        return preg_match('/(.)\1{2,}/', $password) === 1;
    }

    /**
     * Get password strength score (0-100).
     */
    public static function getStrengthScore(string $password): int
    {
        $score = 0;
        
        // Length points (max 25)
        $length = strlen($password);
        if ($length >= 8) $score += 10;
        if ($length >= 12) $score += 10;
        if ($length >= 16) $score += 5;
        
        // Character variety points (max 40)
        if (preg_match('/[a-z]/', $password)) $score += 10;
        if (preg_match('/[A-Z]/', $password)) $score += 10;
        if (preg_match('/[0-9]/', $password)) $score += 10;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 10;
        
        // Complexity points (max 35)
        $charTypes = 0;
        if (preg_match('/[a-z]/', $password)) $charTypes++;
        if (preg_match('/[A-Z]/', $password)) $charTypes++;
        if (preg_match('/[0-9]/', $password)) $charTypes++;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $charTypes++;
        
        $score += $charTypes * 5; // 5 points per character type
        
        // Bonus for good length and variety combination
        if ($length >= 12 && $charTypes >= 3) $score += 15;
        
        return min(100, $score);
    }

    /**
     * Get password strength label.
     */
    public static function getStrengthLabel(int $score): string
    {
        return match (true) {
            $score >= 80 => 'Very Strong',
            $score >= 60 => 'Strong',
            $score >= 40 => 'Moderate',
            $score >= 20 => 'Weak',
            default => 'Very Weak'
        };
    }
} 
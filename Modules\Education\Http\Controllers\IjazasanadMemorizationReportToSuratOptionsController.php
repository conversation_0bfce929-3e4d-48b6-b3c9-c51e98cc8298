<?php

namespace Modules\Education\Http\Controllers;
use App\Http\Controllers\Controller;
use App\IjazasanadMemorizationPlan;
use App\MoshafSurah;
use App\StudentIjazasanadMemorizationReport;
use Illuminate\Http\Request;


class IjazasanadMemorizationReportToSuratOptionsController extends Controller
{


    public function getToSuratOptions($reportId, $hefzPlanId, Request $request)
    {
        $hefzPlan = IjazasanadMemorizationPlan::findOrFail($hefzPlanId);
        $monthlyPlanSurats = range($hefzPlan->start_from_surat, $hefzPlan->to_surat);
        $orderByDirection = $hefzPlan->study_direction == 'backward' ? 'desc' : 'asc';
        $allSurahs = MoshafSurah::whereIn('id', $monthlyPlanSurats)->orderBy('id', $orderByDirection)->get();
        $hefReport = StudentIjazasanadMemorizationReport::findOrFail($reportId);
        $options = '<option value="" selected>Select</option>';

        foreach ($allSurahs as $surat) {
            $isValidSurat = $hefzPlan->study_direction == 'backward'
                ? $surat->id <= $hefReport->hefz_from_surat
                : $surat->id >= $hefReport->hefz_from_surat;

            if ($isValidSurat) {
                $options .= "<option value=\"{$surat->id}\">{$surat->id}. {$surat->name}</option>";
            }
        }

        return $options;
    }






}
<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Holiday
 *
 * @property int $id
 * @property string|null $holiday_title
 * @property string|null $details
 * @property string|null $from_date
 * @property string|null $to_date
 * @property string|null $upload_image_file
 * @property int $active_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $organization_id
 * @property int|null $academic_id
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday query()
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereAcademicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereActiveStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereFromDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereHolidayTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereToDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Holiday whereUploadImageFile($value)
 * @mixin \Eloquent
 */
class Holiday extends Model
{
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);


    }
    //
}

<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CenterClassStudentsBackup extends Model
{
    protected $table = 'center_class_students_backups';

    const STATUS_CREATED = 'Created';
    const STATUS_RESTORED = 'Restored';
    const STATUS_FAILED = 'Failed';

    protected $fillable = [
        'center_id',
        'class_id',
        'file_path',
        'backup_date',
        'status',
        'restored_by',
        'restored_at',
        'file_size',
        'student_count',
        'created_by',
        'backup_type',
        'checksum',

    ];

    protected $casts = [
        'backup_date' => 'datetime',
        'restored_at' => 'datetime',
        'file_size' => 'integer',
        'student_count' => 'integer',
        // 'class_id' => 'json'
    ];

    public function setClassIdAttribute($value)
    {
        $this->attributes['class_id'] = is_array($value) || is_object($value) ? json_encode($value) : $value;
    }

    public function getClassIdAttribute($value)
    {
        if (is_string($value)) {
            // Handle legacy comma-separated strings
            if (strpos($value, ',') !== false && strpos($value, '[') !== 0) {
                return array_map('intval', explode(',', $value));
            }
            
            // Handle JSON strings
            $decoded = json_decode($value, true);
            
            // Properly handle JSON arrays and ensure all values are integers
            if (is_array($decoded)) {
                return array_map('intval', $decoded);
            }
            
            // If not a valid JSON array, return single value
            return [intval($value)];
        }
        
        // Handle already decoded arrays
        return is_array($value) ? array_map('intval', $value) : [];
    }

    public function setStatusAttribute($value)
    {
        if (!in_array($value, [self::STATUS_CREATED, self::STATUS_RESTORED, self::STATUS_FAILED])) {
            throw new \InvalidArgumentException('Invalid status value');
        }
        $this->attributes['status'] = $value;
    }

    public function getIsRestoredAttribute()
    {
        return $this->status === self::STATUS_RESTORED;
    }

    public function getIsCreatedAttribute()
    {
        return $this->status === self::STATUS_CREATED;
    }

    public function getIsFailedAttribute()
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Get the center associated with this backup.
     */
    public function center()
    {
        return $this->belongsTo(Center::class);
    }

    public function restoredBy()
    {
        return $this->belongsTo(User::class, 'restored_by');
    }

    public function restoreLogs()
    {
        return $this->hasMany(BackupRestoreLog::class, 'backup_id');
    }

    public function getFormattedFileSizeAttribute()
    {
        if ($this->file_size < 1024) {
            return $this->file_size . ' B';
        } elseif ($this->file_size < 1024 * 1024) {
            return round($this->file_size / 1024, 2) . ' KB';
        } else {
            return round($this->file_size / (1024 * 1024), 2) . ' MB';
        }
    }

    public function createdBy()
    {
        return $this->belongsTo(Employee::class, 'created_by');
    }

    public function classes()
    {
        return $this->belongsToMany(Classes::class, null, 'backup_id', 'class_id')
            ->withPivot(['restored_at', 'status'])
            ->withTimestamps();
    }

    public function getRestorableClasses()
    {
        return $this->classes()->where('pivot.status', '!=', self::STATUS_RESTORED)->get();
    }

    public function canRestoreClass($classId)
    {
        return $this->classes()
            ->where('classes.id', $classId)
            ->where('pivot.status', '!=', self::STATUS_RESTORED)
            ->exists();
    }

    public function getClassBackupDetails($classId)
    {
        return [
            'class' => $this->classes()->where('classes.id', $classId)->first(),
            'student_count' => $this->getClassStudentCount($classId),
            'status' => $this->getClassRestoreStatus($classId),
            'last_restored' => $this->getClassLastRestoreDate($classId)
        ];
    }

    protected function getClassStudentCount($classId)
    {
        if (!file_exists($this->file_path)) {
            return 0;
        }
        
        $content = file_get_contents($this->file_path);
        $pattern = "/INSERT INTO `class_students`.*class_id.*{$classId}/i";
        return preg_match_all($pattern, $content, $matches);
    }

    protected function getClassRestoreStatus($classId)
    {
        $class = $this->classes()->where('classes.id', $classId)->first();
        return $class ? $class->pivot->status : null;
    }

    protected function getClassLastRestoreDate($classId)
    {
        $class = $this->classes()->where('classes.id', $classId)->first();
        return $class ? $class->pivot->restored_at : null;
    }

    /**
     * Get the employee who created the backup
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'created_by');
    }

    public function backupFiles()
    {
        return $this->hasMany(CenterClassBackupFile::class, 'backup_id');
    }
    
    /**
     * Get the normalized absolute path to the backup ZIP file
     * 
     * Handles path normalization and ensures consistent access to backup files
     * regardless of how the file_path is stored in the database.
     * 
     * @return string The absolute path to the backup ZIP file
     */
    public function getBackupFilePath(): string
    {
        $zipFileName = "backup_{$this->id}_archive.zip";
        
        // First normalize the file_path to remove any storage paths
        $normalizedPath = $this->file_path;
        
        // Handle absolute paths
        if (preg_match('|^/.*storage/app/|', $normalizedPath)) {
            // Extract everything after storage/app/
            preg_match('|.*/storage/app/(.*)|', $normalizedPath, $matches);
            if (isset($matches[1])) {
                $normalizedPath = $matches[1];
            }
        } else {
            // Handle other common cases
            if (str_starts_with($normalizedPath, '/')) {
                $normalizedPath = ltrim($normalizedPath, '/');
            }
            if (str_starts_with($normalizedPath, 'storage/app/')) {
                $normalizedPath = substr($normalizedPath, strlen('storage/app/'));
            }
            if (str_starts_with($normalizedPath, storage_path('app') . '/')) {
                $normalizedPath = substr($normalizedPath, strlen(storage_path('app') . '/'));
            }
        }
        
        // Construct the final path
        $relativePath = $normalizedPath . DIRECTORY_SEPARATOR . $zipFileName;
        return storage_path('app' . DIRECTORY_SEPARATOR . $relativePath);
    }
    
    /**
     * Checks if the backup file exists in the file system
     * 
     * @return bool True if the file exists, false otherwise
     */
    public function fileExists(): bool
    {
        return file_exists($this->getBackupFilePath());
    }

}

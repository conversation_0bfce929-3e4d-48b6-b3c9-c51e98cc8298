<?php

namespace Modules\Education\Http\Controllers;

use App\AttendanceOption;
use App\Employee;
use App\EvaluationSchemaOption;
use App\Form;
use App\MissedClockOut;
use App\MoshafJuz;
use App\ProgramLevel;
use App\Role;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\YearCheck;
use Collective\Html\FormBuilder;
use http\Client\Curl\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Modules\Education\Http\Requests\ClassReportStaatisticsRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class GetProgramLevelsDataTableController extends Controller
{

    public function __invoke(Request $request)
    {


        if ($request->ajax()) {
            $programLevels = ProgramLevel::where('program_id',$request->id);


                return \Yajra\DataTables\DataTables::of($programLevels)
                    ->addColumn('arabic', function ($programLevel) use ($request) {


                        $arabicTitle = $programLevel->arabic[0]->title;
                        return $arabicTitle;

                    })
                    ->addColumn('from_surat', function ($programLevel) use ($request) {


                        $fromSurat = $programLevel->from_surat;
                        return $fromSurat;

                    })
                    ->addColumn('to_surat', function ($programLevel) use ($request) {


                        $toSurat = $programLevel->to_surat;
                        return $toSurat;

                    })


                    ->addColumn('actions', function ($programLevel) use ($request) {
                        $html = '';
                        $route1 = url('/workplace/education/program-levels/' . $programLevel->id);
                        $route2 = url('/workplace/education/program-levels/' . $programLevel->id . '/edit');
                       $html .=  '<a href="' . $route1 . '" title="View ProgramLevel">
                                            <button class="btn btn-info btn-xs"><i class="fa fa-eye"
                                                                                   aria-hidden="true"></i> View
                                            </button>
                                        </a>
                                        <a href="' . $route2 . '"
                                           title="Edit ProgramLevel">
                                            <button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o"
                                                                                      aria-hidden="true"></i> Edit
                                            </button>
                                        </a>';


                       return $html;



//                    })->rawColumns(['login','action'])
                    })->
                    rawColumns(['role', 'actions', 'login', 'status', 'full_name', 'image'])
                    ->make(true);





        }


        return response()->json([$programLevels]);
    }
}

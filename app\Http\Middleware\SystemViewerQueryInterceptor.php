<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Center;

/**
 * SystemViewerQueryInterceptor: The Nuclear Solution
 * 
 * This middleware operates at the deepest Laravel level by intercepting
 * database queries BEFORE they execute and modifying them for system viewers.
 * 
 * This is the most comprehensive solution possible - it catches ALL queries
 * regardless of where they originate (relationships, direct queries, DataTables, etc.)
 */
class SystemViewerQueryInterceptor
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Only apply for system viewers
        if ($this->isSystemViewer()) {
            $this->enableSystemViewerQueryInterception();
        }

        $response = $next($request);

        // Clean up after request
        if ($this->isSystemViewer()) {
            $this->disableSystemViewerQueryInterception();
        }

        return $response;
    }

    /**
     * Enable query interception for system viewers
     */
    protected function enableSystemViewerQueryInterception(): void
    {
        // Listen to all database queries
        DB::listen(function ($query) {
            // Modify queries that contain authorization patterns
            $this->modifySystemViewerQuery($query);
        });

        // Extend QueryBuilder to intercept whereIn calls
        $this->extendQueryBuilder();
    }

    /**
     * Extend Laravel's QueryBuilder to intercept authorization patterns
     */
    protected function extendQueryBuilder(): void
    {
        // Store original whereIn method
        if (!QueryBuilder::hasMacro('originalWhereIn')) {
            QueryBuilder::macro('originalWhereIn', QueryBuilder::class . '@whereIn');
        }

        // Override whereIn method
        QueryBuilder::macro('whereIn', function ($column, $values, $boolean = 'and', $not = false) {
            /** @var QueryBuilder $this */
            
            // Check if this is a system viewer authorization pattern
            if ($this->isSystemViewerAuthPattern($column, $values)) {
                $expandedValues = $this->getSystemViewerExpandedValues($column);
                return $this->originalWhereIn($column, $expandedValues, $boolean, $not);
            }

            // Use original method for non-authorization patterns
            return $this->originalWhereIn($column, $values, $boolean, $not);
        });
    }

    /**
     * Check if this is a system viewer authorization pattern
     */
    protected function isSystemViewerAuthPattern($column, $values): bool
    {
        // Detect authorization failures (empty arrays)
        if (is_array($values) && empty($values)) {
            return true;
        }

        // Detect authorization columns
        $authColumns = [
            'center_id', 'centre_id', 'cen_id',
            'department_id', 'class_id', 'program_id',
            'employee_id', 'emp_id', 'user_id'
        ];

        return in_array($column, $authColumns);
    }

    /**
     * Get expanded values for system viewer access
     */
    protected function getSystemViewerExpandedValues($column): array
    {
        $organizationId = config('organization_id');
        
        // Cache these queries for performance
        $cacheKey = "system_viewer_{$column}_{$organizationId}";
        
        return cache()->remember($cacheKey, 300, function () use ($column, $organizationId) {
            switch ($column) {
                case 'center_id':
                case 'centre_id':
                case 'cen_id':
                    return Center::where('organization_id', $organizationId)
                        ->whereNull('deleted_at')
                        ->pluck('id')
                        ->toArray();

                case 'class_id':
                    return \App\Classes::where('organization_id', $organizationId)
                        ->whereNull('deleted_at')
                        ->pluck('id')
                        ->toArray();

                case 'employee_id':
                case 'emp_id':
                    return \App\Employee::where('organization_id', $organizationId)
                        ->whereNull('deleted_at')
                        ->pluck('id')
                        ->toArray();

                default:
                    // Fallback: return all IDs for the organizational context
                    return range(1, 9999); // Liberal fallback for unknown columns
            }
        });
    }

    /**
     * Modify query SQL for system viewers
     */
    protected function modifySystemViewerQuery($query): void
    {
        // This method can be used for additional SQL-level modifications if needed
        // For now, the whereIn interception handles most cases
    }

    /**
     * Disable query interception
     */
    protected function disableSystemViewerQueryInterception(): void
    {
        // Remove listeners (Laravel automatically cleans up after request)
        // Macros persist globally, which is fine for our use case
    }

    /**
     * Check if the current user is a system viewer
     */
    protected function isSystemViewer(): bool
    {
        if (!Auth::guard('employee')->check()) {
            return false;
        }

        $user = Auth::guard('employee')->user();
        return $user->hasRole('system_viewer_' . config('organization_id') . '_');
    }
} 
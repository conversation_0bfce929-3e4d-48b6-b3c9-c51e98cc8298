<?php

namespace App\Models;

use App\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MenuUsageStatistic extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'menu_usage_statistics';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'menu_title',
        'menu_url',
        'parent_menu',
        'access_count',
        'last_accessed_at',
        'session_id',
        'ip_address',
        'user_agent',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'last_accessed_at' => 'datetime',
        'access_count' => 'integer',
    ];

    /**
     * Get the user that owns the statistic.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
} 
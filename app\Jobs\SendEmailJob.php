<?php

declare(strict_types=1);

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\ThrottlesExceptions;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Modules\JobSeeker\Entities\EmailSendingLog;
use Modules\JobSeeker\Entities\OutgoingEmail;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception as PHPMailerException;
use Mailtrap\MailtrapClient;
use Mailtrap\Mime\MailtrapEmail;
use Symfony\Component\Mime\Address;
use <PERSON>tra<PERSON>\Helper\ResponseHelper;
use Exception;

/**
 * Send Email Job - Dynamic Provider Email Sending with Transactional Outbox Integration
 * 
 * Handles asynchronous email sending using dynamic provider configuration.
 * Integrates with the transactional outbox pattern for ultimate data integrity.
 * Updates both log entries and outgoing email records with final status.
 */
final class SendEmailJob implements ShouldQueue, ShouldBeUniqueUntilProcessing
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of seconds after which the job's unique lock will be released.
     *
     * @var int
     */
    public $uniqueFor = 3600;

    public $tries = 5;
    public $backoff = [10, 30, 60, 120, 240]; // in seconds

    protected $emailData;

    /**
     * Create a new job instance.
     *
     * @param array $emailData Email data and configuration
     */
    public function __construct(array $emailData)
    {
        $this->emailData = $emailData;
    }

    public function middleware()
    {
        // Throttle the job to 5 attempts with a 60 second backoff
        return [(new ThrottlesExceptions(5, 1))->backoff(60)];
    }

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId()
    {
        // Generate a unique ID for the job based on the correlation ID or email data
        return $this->emailData['correlation_id'] ?? 'email_' . md5($this->emailData['to']['email'] . $this->emailData['subject']);
    }

    /**
     * Execute the job - Send email using configured provider with transactional outbox integration
     * 
     * @return void
     */
    public function handle()
    {
        $correlationId = $this->emailData['correlation_id'] ?? 'job_' . uniqid();
        $logEntryId = $this->emailData['log_entry_id'] ?? null;
        $outgoingEmailId = $this->emailData['outgoing_email_id'] ?? null;
        $provider = $this->emailData['provider'] ?? 'gmail';
        
        Log::channel('email')->info('SendEmailJob: Processing email job with transactional outbox', [
            'correlation_id' => $correlationId,
            'log_entry_id' => $logEntryId,
            'outgoing_email_id' => $outgoingEmailId,
            'provider' => $provider,
            'to' => $this->emailData['to']['email'],
            'subject' => $this->emailData['subject'],
            'attempt' => $this->attempts(),
        ]);

        // Get outgoing email record
        $outgoingEmail = $outgoingEmailId ? OutgoingEmail::find($outgoingEmailId) : null;

        try {
            // Mark sending started in outbox
            if ($outgoingEmail) {
                $outgoingEmail->markSendingStarted();
            }

            // Set locale if provided
            if (!empty($this->emailData['locale'])) {
                App::setLocale($this->emailData['locale']);
                Log::channel('email')->debug('SendEmailJob: Locale set', [
                    'correlation_id' => $correlationId,
                    'locale' => $this->emailData['locale'],
                ]);
            }

            // Send email using the configured provider
            $this->sendEmail($provider, $correlationId);

            // Update both outgoing email and log entry to success
            if ($outgoingEmail) {
                $outgoingEmail->markAsSent();
            }
            $this->updateLogEntry($logEntryId, 'success', null, $correlationId);

            Log::channel('email')->info('SendEmailJob: Email sent successfully with transactional outbox', [
                'correlation_id' => $correlationId,
                'log_entry_id' => $logEntryId,
                'outgoing_email_id' => $outgoingEmailId,
                'provider' => $provider,
                'to' => $this->emailData['to']['email'],
                'attempt' => $this->attempts(),
            ]);

        } catch (Exception $e) {
            $errorMessage = $e->getMessage();
            
            Log::channel('email')->error('SendEmailJob: Email sending failed with transactional outbox', [
                'correlation_id' => $correlationId,
                'log_entry_id' => $logEntryId,
                'outgoing_email_id' => $outgoingEmailId,
                'provider' => $provider,
                'to' => $this->emailData['to']['email'],
                'error' => $errorMessage,
                'attempt' => $this->attempts(),
                'max_tries' => $this->tries,
            ]);

            // Update both outgoing email and log entry to failure
            if ($outgoingEmail) {
                $outgoingEmail->markSendFailed($errorMessage);
            }
            $this->updateLogEntry($logEntryId, 'failure', $errorMessage, $correlationId);

            // Rethrow the exception to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Generate email content using view or direct body
     */
    private function generateEmailContent(): string
    {
        if (!empty($this->emailData['view'])) {
            return View::make($this->emailData['view'], array_merge(
                $this->emailData['viewData'],
                ['body' => $this->emailData['body']]
            ))->render();
        }

        return $this->emailData['body'];
    }

    /**
     * Update the existing log entry with final status
     */
    private function updateLogEntry(?int $logEntryId, string $status, ?string $errorMessage, string $correlationId): void
    {
        if (!$logEntryId) {
            Log::channel('email')->warning('SendEmailJob: No log entry ID provided for update', [
                'correlation_id' => $correlationId,
                'status' => $status,
            ]);
            return;
        }

        try {
            $logEntry = EmailSendingLog::find($logEntryId);
            if ($logEntry) {
                $logEntry->update([
                    'status' => $status,
                    'error_message' => $errorMessage,
                ]);

                Log::channel('email')->debug('SendEmailJob: Log entry updated', [
                    'correlation_id' => $correlationId,
                    'log_entry_id' => $logEntryId,
                    'status' => $status,
                ]);
            } else {
                Log::channel('email')->warning('SendEmailJob: Log entry not found for update', [
                    'correlation_id' => $correlationId,
                    'log_entry_id' => $logEntryId,
                ]);
            }
        } catch (Exception $e) {
            Log::channel('email')->error('SendEmailJob: Failed to update log entry', [
                'correlation_id' => $correlationId,
                'log_entry_id' => $logEntryId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle job failure - Update log entry to failure status
     */
    public function failed(\Throwable $exception): void
    {
        $correlationId = $this->emailData['correlation_id'] ?? 'job_failed_' . uniqid();
        $logEntryId = $this->emailData['log_entry_id'] ?? null;

        Log::channel('email')->error('SendEmailJob: Job failed after all retries', [
            'correlation_id' => $correlationId,
            'log_entry_id' => $logEntryId,
            'to' => $this->emailData['to']['email'],
            'subject' => $this->emailData['subject'],
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // Update log entry to final failure status
        $this->updateLogEntry($logEntryId, 'failure', 'Job failed after all retries: ' . $exception->getMessage(), $correlationId);
    }

    /**
     * Send email using the specified provider
     */
    private function sendEmail(string $provider, string $correlationId): void
    {
        match ($provider) {
            'gmail' => $this->sendViaGmail($correlationId),
            'mailtrap' => $this->sendViaMailtrap($correlationId),
            default => throw new Exception("Unsupported email provider: {$provider}")
        };
    }

    /**
     * Send email via Gmail using PHPMailer
     */
    private function sendViaGmail(string $correlationId): void
    {
        Log::channel('email')->info('SendEmailJob: Sending via Gmail', [
            'correlation_id' => $correlationId,
        ]);

        $config = $this->emailData['provider_config'];

        if (empty($config['host']) || empty($config['username']) || empty($config['password'])) {
            throw new Exception('Gmail SMTP configuration is incomplete');
        }

        // Generate email content
        $htmlContent = $this->generateEmailContent();

        // Create PHPMailer instance
        $mail = new PHPMailer(true);

        // Gmail SMTP configuration
        $mail->isSMTP();
        $mail->Host = $config['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $config['username'];
        $mail->Password = $config['password'];
        $mail->SMTPSecure = $config['encryption'];
        $mail->Port = (int)$config['port'];

        // Set sender and recipient
        $mail->setFrom($this->emailData['from']['email'], $this->emailData['from']['name']);
        $mail->addAddress($this->emailData['to']['email'], $this->emailData['to']['name'] ?? '');

        // Add CC recipients
        foreach ($this->emailData['cc'] as $ccEmail) {
            $mail->addCC(is_array($ccEmail) ? $ccEmail['email'] : $ccEmail);
        }

        // Add attachments
        $tempFilesToCleanup = [];
        foreach ($this->emailData['attachments'] as $attachment) {
            if (isset($attachment['path']) && file_exists($attachment['path'])) {
                $mail->addAttachment($attachment['path'], $attachment['name'] ?? basename($attachment['path']));
                
                if (isset($attachment['delete_after_send']) && $attachment['delete_after_send']) {
                    $tempFilesToCleanup[] = $attachment['path'];
                }
            }
        }

        // Set email content
        $mail->isHTML(true);
        $mail->Subject = $this->emailData['subject'];
        $mail->Body = $htmlContent;
        $mail->AltBody = strip_tags($htmlContent);

        // Add correlation ID to headers
        $mail->addCustomHeader('X-Correlation-ID', $correlationId);

        $mail->send();

        // Clean up temporary files
        $this->cleanupTempFiles($tempFilesToCleanup, $correlationId);
    }

    /**
     * Send email via Mailtrap using official SDK
     */
    private function sendViaMailtrap(string $correlationId): void
    {
        Log::channel('email')->info('SendEmailJob: Sending via Mailtrap', [
            'correlation_id' => $correlationId,
        ]);

        $config = $this->emailData['provider_config'];

        if (empty($config['api_key'])) {
            throw new Exception('Mailtrap API configuration is incomplete');
        }

        // Generate email content
        $htmlContent = $this->generateEmailContent();

        // Create Mailtrap client
        $mailtrap = MailtrapClient::initSendingEmails(apiKey: $config['api_key']);

        // Create email
        $email = (new MailtrapEmail())
            ->from(new Address($this->emailData['from']['email'], $this->emailData['from']['name']))
            ->to(new Address($this->emailData['to']['email'], $this->emailData['to']['name'] ?? ''))
            ->subject($this->emailData['subject'])
            ->html($htmlContent)
            ->text(strip_tags($htmlContent));

        // Add CC recipients
        foreach ($this->emailData['cc'] as $ccEmail) {
            $email->addCc(is_array($ccEmail) ? $ccEmail['email'] : $ccEmail);
        }

        // Add attachments
        $tempFilesToCleanup = [];
        foreach ($this->emailData['attachments'] as $attachment) {
            if (isset($attachment['path']) && file_exists($attachment['path'])) {
                $email->attachFromPath($attachment['path'], $attachment['name'] ?? basename($attachment['path']));
                
                if (isset($attachment['delete_after_send']) && $attachment['delete_after_send']) {
                    $tempFilesToCleanup[] = $attachment['path'];
                }
            }
        }

        // Add custom headers
        $email->getHeaders()->addTextHeader('X-Correlation-ID', $correlationId);

        // Send email
        $response = $mailtrap->send($email);

        // Clean up temporary files
        $this->cleanupTempFiles($tempFilesToCleanup, $correlationId);
    }

    /**
     * Clean up temporary attachment files after sending
     */
    protected function cleanupTempFiles(array $tempFiles, string $correlationId): void
    {
        foreach ($tempFiles as $tempFile) {
            try {
                if (file_exists($tempFile) && unlink($tempFile)) {
                    Log::channel('email')->debug('SendEmailJob: Temporary file cleaned up', [
                        'correlation_id' => $correlationId,
                        'file' => $tempFile,
                    ]);
                }
            } catch (Exception $e) {
                Log::channel('email')->warning('SendEmailJob: Failed to cleanup temporary file', [
                    'correlation_id' => $correlationId,
                    'file' => $tempFile,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }
}

<?php

namespace Modules\Admission\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StudentAdmissionAPIRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {


        return [
            'centerId' => 'required',
            'status' => 'required',
            'admissionId' => 'required|integer|exists:admissions,id',
            'classId' => 'required|integer|exists:classes,id',
            'programId' => 'required|integer|exists:programs,id',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {


        return  [
                'centerId.required' => 'Please assign the student to a center.',

                'status.required' => 'The Status field cannot be left empty. Please provide a status.',

                'admissionId.required' => 'Admission ID must be provided.',
                'admissionId.integer' => 'The Admission ID should be an integer value.',
                'admissionId.exists' => 'The provided Admission ID cannot be found. Please make sure to provide a valid Admission ID.',

                'classId.required' => 'Please assign the student to a class.',
                'classId.integer' => 'The Class ID should be an integer value.',
                'classId.exists' => 'The provided Class ID does not exist. Please provide a valid Class ID.',

                'programId.required' => 'Please assign the student to a program.',
                'programId.integer' => 'The Program ID should be an integer value.',
                'programId.exists' => 'The provided Program does not exist. Please assign to a valid program.',
        ];
    }
}

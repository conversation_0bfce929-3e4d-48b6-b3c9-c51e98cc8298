@startuml System Context Diagram (Level 1 - Itqan)

!theme vibrant

title System Context Diagram for Itqan

' Actors
actor "Administrator" as Admin
actor "Staff / Teacher" as Staff
actor "Student / Parent" as Student

' External Systems (Assumed - adjust if needed)
system "Email Service (e.g., Mailgun, SES)" as Email
system "Payment Gateway (e.g., Stripe, PayPal)" as PaymentGateway <<External>> #lightblue
system "SMS Gateway" as SMS <<External>> #lightblue

' The System
system "Itqan ERP System" as Itqan #white {
    component "Web Application" as WebApp
}

' Relationships
Admin -> Itqan : Manages System Settings, Users, Modules
Staff -> Itqan : Manages Courses, Students, Attendance, Grades, etc.
Student -> Itqan : Accesses Profile, Grades, Fees, Communication

Itqan -> Email : Sends Notifications, Reports, Communication
Itqan -> SMS : Sends SMS Notifications
Itqan -> PaymentGateway : Processes Online Fee Payments
PaymentGateway -> Itqan : Confirms Payment Status

@enduml 
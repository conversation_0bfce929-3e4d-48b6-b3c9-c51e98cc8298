<?php

namespace Modules\EducationalReports\Http\Controllers;

use App\BaseSetup;
use App\Center;
use App\Classes;
use App\EmailSetting;
use App\Employee;
use App\GeneralSettings;
use App\Guardian;
use App\Organization;
use App\Program;
use App\ProgramTranslation;
use App\Role;
use App\Section;
use App\UserLog;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Dompdf\Dompdf;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Admission;
use App\AdmissionInterview;
use App\AdmissionInterviewer;
use App\Student;
use App\StudentHefzPlan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;
use Modules\ApplicationCenter\Entities\RegistrationSetting;


class ClassesPerWeekController extends Controller
{

    public function getdaysCount($classId)
    {
        $class = Classes::find($classId);

        $timetable = $class->timetable;

        $daysCount = 0;

        if($timetable) {
            foreach(['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'] as $day) {
                if(!is_null($timetable->$day) && $timetable->$day !== '') {
                    $daysCount++;
                }
            }
        }
        return response()->json($daysCount);
    }

}

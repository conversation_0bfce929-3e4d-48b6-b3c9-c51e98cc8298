<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

/**
 * ThemeService handles theme-related operations.
 *
 * Purpose: Manage theme configurations and settings for the application.
 * Side effects: None - this is a utility service.
 * Errors: Logs issues and provides fallback behavior.
 * Performance: Lightweight utility class.
 */
class ThemeService
{
    /**
     * Get the current theme configuration.
     *
     * @return array<string, mixed>
     */
    public function getCurrentTheme(): array
    {
        $theme = config('website_theme', 'wajeha');
        
        Log::debug('Getting current theme', ['theme' => $theme]);
        
        return [
            'name' => $theme,
            'path' => "site::templates.{$theme}",
        ];
    }
    
    /**
     * Get theme path for views.
     *
     * @param string $view
     * @return string
     */
    public function getThemePath(string $view = 'home'): string
    {
        $theme = config('website_theme', 'wajeha');
        return "site::templates.{$theme}.{$view}";
    }
}
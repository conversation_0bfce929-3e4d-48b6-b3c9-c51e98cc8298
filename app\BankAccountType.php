<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BankAccountType extends Model
{


    use SoftDeletes;


    protected $table = 'bank_account_types';

    protected $fillable = [
        'name',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
        'organization_id'
    ];




    public function bankAccount()
    {
        return $this->hasMany(BankAccount::class);
    }



}

<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Brian2694\Toastr\Facades\Toastr;

/**
 * Viewer Management Controller
 * 
 * Handles special features for system viewer accounts
 * - Demo data mode toggle
 * - Session management
 * - Activity monitoring
 */
final class ViewerManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware(['web', 'employee', 'auth:employee']);
    }

    /**
     * Toggle between real and demo data mode for viewers
     */
    public function toggleDemoMode(Request $request)
    {
        // Only allow for system viewers
        $user = Auth::guard('employee')->user();
        if (!$user->hasRole('system_viewer_' . config('organization_id') . '_')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action'
            ], 403);
        }

        $demoMode = $request->boolean('demo_mode');
        
        // Store in session
        Session::put('viewer_demo_mode', $demoMode);
        
        // Log the mode change
        Log::info('Viewer Demo Mode Changed', [
            'user_id' => $user->id,
            'demo_mode' => $demoMode,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        $message = $demoMode 
            ? 'Demo data mode enabled. Viewing anonymized demonstration data.'
            : 'Real data mode enabled. Viewing actual system data.';

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'demo_mode' => $demoMode
            ]);
        }

        Toastr::success($message);
        return redirect()->back();
    }

    /**
     * Get current viewer status and preferences
     */
    public function getViewerStatus(Request $request)
    {
        $user = Auth::guard('employee')->user();
        
        if (!$user->hasRole('system_viewer_' . config('organization_id') . '_')) {
            return response()->json([
                'success' => false,
                'message' => 'Not a viewer account'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'demo_mode' => Session::get('viewer_demo_mode', false),
                'user_id' => $user->id,
                'role' => 'system_viewer',
                'session_id' => Session::getId()
            ]
        ]);
    }

    /**
     * Display viewer dashboard/controls (for managing directors)
     */
    public function viewerDashboard()
    {
        $user = Auth::guard('employee')->user();
        
        // Only managing directors can access viewer management
        if (!$user->hasRole('managing-director_' . config('organization_id') . '_')) {
            abort(403, 'Access denied');
        }

        // Get viewer statistics and session information
        $viewerStats = $this->getViewerStatistics();

        return view('viewer.dashboard', compact('viewerStats'));
    }

    /**
     * Get viewer statistics for management dashboard
     */
    private function getViewerStatistics(): array
    {
        // This would typically query your database for viewer activity
        // For now, return basic statistics
        return [
            'active_sessions' => 0, // You'll implement this based on your session storage
            'total_page_views' => 0,
            'demo_mode_usage' => 0,
            'blocked_operations' => 0
        ];
    }

    /**
     * Get active viewer session count (Admin only).
     */
    public function getActiveSessions()
    {
        try {
            // Only managing directors can access this
            if (!Auth::guard('employee')->user()->hasRole('managing-director_' . config('organization_id') . '_')) {
                return response()->json(['error' => 'Unauthorized', 'count' => 0], 403);
            }

            // Count active viewer sessions
            $count = DB::table('viewer_sessions')
                ->where('organization_id', config('organization_id'))
                ->where('is_active', true)
                ->where('last_activity', '>=', now()->subMinutes(30))
                ->count();

            return response()->json(['count' => $count, 'status' => 'success']);
        } catch (\Exception $e) {
            Log::error('Error getting active sessions: ' . $e->getMessage());
            return response()->json(['error' => 'Database error', 'count' => 0], 500);
        }
    }

    /**
     * Get total page views by viewers (Admin only).
     */
    public function getPageViews()
    {
        try {
            // Only managing directors can access this
            if (!Auth::guard('employee')->user()->hasRole('managing-director_' . config('organization_id') . '_')) {
                return response()->json(['error' => 'Unauthorized', 'count' => 0], 403);
            }

            // Count total page views from new detailed tracking table first
            $count = DB::table('viewer_page_views')
                ->where('organization_id', config('organization_id'))
                ->count();

            // Fallback to activity logs if page views table is empty
            if ($count == 0) {
                $count = DB::table('viewer_activity_logs')
                    ->where('organization_id', config('organization_id'))
                    ->whereDate('created_at', today())
                    ->count();
            }

            return response()->json(['count' => $count, 'status' => 'success']);
        } catch (\Exception $e) {
            Log::error('Error getting page views: ' . $e->getMessage());
            return response()->json(['error' => 'Database error', 'count' => 0], 500);
        }
    }

    /**
     * Get detailed page views for admin analysis (clickable)
     */
    public function getDetailedPageViews()
    {
        try {
            // Only managing directors can access this
            if (!Auth::guard('employee')->user()->hasRole('managing-director_' . config('organization_id') . '_')) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $organizationId = config('organization_id', 2);
            
            // Get detailed page views with viewer information
            $pageViews = DB::table('viewer_page_views as vpv')
                ->join('employees as e', 'vpv.employee_id', '=', 'e.id')
                ->leftJoin('viewer_sessions as vs', 'vpv.viewer_session_id', '=', 'vs.id')
                ->where('vpv.organization_id', $organizationId)
                ->select([
                    'vpv.id',
                    'vpv.page_url',
                    'vpv.page_title',
                    'vpv.http_method',
                    'vpv.viewed_at',
                    DB::raw("TIMESTAMPDIFF(SECOND, vpv.viewed_at, LEAD(vpv.viewed_at, 1) OVER (PARTITION BY vpv.viewer_session_id ORDER BY vpv.viewed_at)) as time_spent_seconds"),
                    'vs.ip_address',
                    'vs.user_agent',
                    'e.full_name as first_name',
                    DB::raw("'' as last_name"),
                    'e.email',
                    'vs.login_time as session_start'
                ])
                ->orderBy('vpv.viewed_at', 'desc')
                ->limit(100)
                ->get();
            
            return response()->json([
                'success' => true,
                'data' => $pageViews,
                'total_count' => $pageViews->count()
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error fetching detailed page views: ' . $e->getMessage());
            
            $errorMessage = 'Unable to fetch detailed page views. An internal error occurred.';
            if (config('app.debug')) {
                $errorMessage = 'Error fetching detailed page views: ' . $e->getMessage();
            }

            return response()->json([
                'success' => false,
                'error' => $errorMessage,
                'data' => []
            ], 500);
        }
    }

    /**
     * Get viewer interests analysis
     */
    public function getViewerInterests()
    {
        try {
            // Only managing directors can access this
            if (!Auth::guard('employee')->user()->hasRole('managing-director_' . config('organization_id') . '_')) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $organizationId = config('organization_id', 2);
            
            // Get viewer interests analysis
            $interests = DB::table('viewer_interests as vi')
                ->join('employees as e', 'vi.employee_id', '=', 'e.id')
                ->where('vi.organization_id', $organizationId)
                ->select([
                    'vi.module_name',
                    'vi.page_category',
                    'vi.total_views',
                    'vi.total_time_spent',
                    'vi.last_viewed_at',
                    'e.first_name',
                    'e.last_name',
                    'e.email'
                ])
                ->orderBy('vi.total_views', 'desc')
                ->get();
            
            return response()->json([
                'success' => true,
                'data' => $interests,
                'summary' => [
                    'most_viewed_module' => $interests->first()->module_name ?? 'N/A',
                    'total_modules_accessed' => $interests->unique('module_name')->count(),
                    'total_time_spent' => $interests->sum('total_time_spent')
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error fetching viewer interests: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => 'Unable to fetch viewer interests',
                'data' => [],
                'summary' => [
                    'most_viewed_module' => 'N/A',
                    'total_modules_accessed' => 0,
                    'total_time_spent' => 0
                ]
            ], 500);
        }
    }

    /**
     * Get blocked operations count for viewers (Admin only).
     */
    public function getBlockedOperations()
    {
        try {
            // Only managing directors can access this
            if (!Auth::guard('employee')->user()->hasRole('managing-director_' . config('organization_id') . '_')) {
                return response()->json(['error' => 'Unauthorized', 'count' => 0], 403);
            }

            // Count blocked operations today - check if the column exists first
            $count = DB::table('viewer_activity_logs')
                ->where('organization_id', config('organization_id'))
                ->where('was_blocked', true)
                ->whereDate('created_at', today())
                ->count();

            return response()->json(['count' => $count, 'status' => 'success']);
        } catch (\Exception $e) {
            Log::error('Error getting blocked operations: ' . $e->getMessage());
            return response()->json(['error' => 'Database error', 'count' => 0], 500);
        }
    }

    /**
     * Public viewer statistics endpoint (for viewers themselves).
     */
    public function getPublicViewerStats()
    {
        // Only system viewers can access this
        if (!Auth::guard('employee')->user()->hasRole('system_viewer_' . config('organization_id') . '_')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return response()->json([
            'demo_mode' => session('viewer_demo_mode', false),
            'session_active' => true,
            'last_activity' => now()->toISOString()
        ]);
    }

    /**
     * Get the current demo mode status.
     */
    public function getDemoModeStatus()
    {
        return response()->json([
            'demo_mode' => Session::get('viewer_demo_mode', false)
        ]);
    }

    /**
     * Update the viewer account credentials.
     */
    public function updateViewerAccount(Request $request)
    {
        $request->validate([
            'viewer_id' => 'required|exists:employees,id',
            'email' => 'required|email|unique:employees,email,' . $request->viewer_id,
            'password' => 'nullable|min:8|confirmed',
        ]);

        $viewer = \App\Employee::find($request->viewer_id);

        // Double-check if this user is indeed a system_viewer
        if (!$viewer->hasRole('system_viewer_' . config('organization_id') . '_')) {
            return response()->json(['message' => 'This user is not a system viewer.'], 403);
        }

        $updated = false;
        $updatedFields = [];

        // Update email if changed
        if ($request->email !== $viewer->email) {
            $viewer->email = $request->email;
            $updatedFields[] = 'email';
            $updated = true;
        }

        // Update password if provided
        if ($request->filled('password')) {
            $viewer->password = bcrypt($request->password);
            $updatedFields[] = 'password';
            $updated = true;
        }

        if ($updated) {
            $viewer->save();

            Log::info('System Viewer account updated by admin.', [
                'admin_user_id' => Auth::guard('employee')->id(),
                'viewer_user_id' => $viewer->id,
                'updated_fields' => $updatedFields
            ]);

            return response()->json([
                'message' => 'Viewer account updated successfully.',
                'updated_email' => $viewer->email
            ]);
        }

        return response()->json(['message' => 'No changes were made.']);
    }

    /**
     * Reset viewer account to default state
     */
    public function resetViewerAccount(Request $request)
    {
        $user = Auth::guard('employee')->user();
        
        // Only managing directors can reset viewer accounts
        if (!$user->hasRole('managing-director_' . config('organization_id') . '_')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action'
            ], 403);
        }

        // Reset session data
        Session::forget('viewer_demo_mode');
        
        Log::info('Viewer Account Reset', [
            'admin_user_id' => $user->id,
            'ip' => $request->ip()
        ]);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Viewer account reset to default state'
            ]);
        }

        Toastr::success('Viewer account reset successfully');
        return redirect()->back();
    }
} 
<?php

namespace Modules\Education\Http\Controllers;

use App\Attendance;
use App\AttendanceOption;
use App\Cen_Emp;
use App\ClassProgram;
use App\MoshafJuz;
use App\Student;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Modules\EducationalReports\Http\Requests\CreateHefzPlanRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentAttendance;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class MonthlyPlanController extends Controller
{


    public function __construct()
    {
//        $this->middleware('writeCurrentClassReportOnly', ['only' => ['show']]);

        // User::checkAuth();
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request, $id)
    {
        $current_day = date('d');

        $surats = MoshafSurah::all();
        $hefzEvaluationOptions = EvaluationSchema::where('target', 'hefz')->first()->options;
        // return $this->studentReport(1);
        $request = request();


        $class = Classes::withTrashed()->find($id);


        // handling the wrong $id
        if (is_null($class)) {
            flash('Class ' . $id . ' does not exist');
            return redirect()->to("workplace/education/classes");
        }

        if (!is_null($class->deleted_at)) {

            // return redirect back and flush a message stating that the $class->class_code is archived
            flash('Class ' . $class->class_code . ' is archived and is not accessable. please try another class');
            return redirect()->to("workplace/education/classes");

        }
        $class = Classes::with('programs.settings')->findOrFail($id);
//        $class =  Classes::findOrFail($id);

        if ($request->from_date) {
            $from_date = Carbon::parse($request->from_date);
        } else {
            $from_date = Carbon::create(date('Y'), date('m'), 01);
        }

        if ($request->to_date) {
            $to_date = Carbon::parse($request->to_date);
        } else {
            $to_date = $from_date->copy()->addMonth();
        }
        $class_teachers = ClassTeacher::where('class_id', $class->id)
            ->where('end_date', null)->get();

        $class_programs = [];

        foreach ($class->programs as $key => $program) {
            $data = [];
            $data['info'] = $program;


            if (isset($program->setting['special_program_code']) && $program->setting['special_program_code'] == 'hefz') {
                $teacher = $class_teachers->filter(function ($teacher) use ($program) {
                    return (count($teacher->subjects) && $teacher->subjects->filter(function ($subject) use ($program) {
                            return $subject->program_id == $program->id;
                        })->count());
                })->first();

                if (!$teacher) {
                    return $this->errorNoTeacher($class_id);
                }

                if (!auth()->user()->can('view class_reports') && $teacher && $teacher->employee_id != auth()->user()->id) {
                    continue;
                }
                $data['teacher'] = $teacher;
                $data['type'] = 'program';

                if ($teacher) {
                    $data['timetable'] = $teacher->subjects()->where('program_id', $program->id)->first()->timetable;
                    if (!$data['timetable']) {
                        return $this->errorNoTimetable($class->id);
                    }
                    $data['class_teacher_subject_id'] = $teacher->subjects()->where('program_id', $program->id)->first()->id;

                    $last_report = ClassReport::where('class_id', $class->id)
                        ->where('program_id', $program->id)
                        ->where('status', 'completed')
                        ->get()->last();

                    $data['next_report_date'] = $this->getNextReportTime($last_report->class_time ?? null, $data['timetable'], $class);
                }
            } else {
                $data['type'] = 'subjects';
                if (isset($program->levels->find($program->pivot->program_level_id)->subjects)) {
                    foreach ($program->levels->find($program->pivot->program_level_id)->subjects as $index => $subject) {
                        $teacher = $class_teachers->filter(function ($teacher) use ($subject) {
                            return (count($teacher->subjects) && in_array($subject->id, $teacher->subjects->pluck('subject_id')->toArray()));
                        })->first();


                        if (!$teacher) {
                            return $this->errorNoTeacher($class->id);
                        }

                        if (!auth()->user()->can('view class_reports') && $teacher->employee_id != auth()->user()->id) {
                            continue;
                        }
                        $data['class_subjects'][$index] = $subject;
                        $data['class_subjects'][$index]['teacher'] = $teacher;
                        if ($teacher) {
                            $data['class_subjects'][$index]['timetable'] = $teacher->subjects()->where('subject_id', $subject->id)->first()->timetable;
                            if (!$data['class_subjects'][$index]['timetable']) {
                                return $this->errorNoTimetable($class->id);
                            }
                            $data['class_subjects'][$index]['class_teacher_subject_id'] = $teacher->subjects()->where('subject_id', $subject->id)->first()->id;
                            $last_report = ClassReport::where('class_id', $class->id)
                                ->where('subject_id', $subject->id)
                                ->where('status', 'completed')
                                ->get()->last();
                            $data['next_report_date'] = $this->getNextReportTime($last_report->class_time ?? null, $data['class_subjects'][$index]['timetable'], $class);
                        }
                    }
                }
                if (!isset($data['class_subjects']) || !count($data['class_subjects'])) {
                    continue;
                }
            }

            $class_programs[$key] = $data;
        }

        $report_summery = [];

        $class_subjects = [];

        $class_reports = ClassReport::where('class_id', $class->id)
            ->where('class_time', ">=", $from_date)
            ->where('class_time', "<=", $to_date)
            ->get();

        $classReportsIds = $class_reports->pluck('id')->toArray();
        for ($i = $from_date->copy(); $i <= date('Y/m/d') && $i <= $to_date->copy(); $i->addDay()) {
            $report_summery[$i->format("Y/m/d")]["y"] = $i->format("Y/m/d");
        }

        $class_subjects_reports = [];

        foreach ($class_reports as $key => $report) {
            $report_summery[$report->class_time->format("Y/m/d")]["y"] = $report->class_time->format("Y/m/d");
            if ($report->subject_id) {
                $class_subjects_reports['subject_' . $report->subject_id][$report->class_time->format("Y/m/d")] = $report;
                $report_summery[$report->class_time->format("Y/m/d")][$report->subject->title] = $report->attendace->where('attendance', "!=", "absent")->where('attendance', "!=", "excused")->count();
                if (!in_array($report->subject->title, $class_subjects)) {
                    $class_subjects[] = $report->subject->title;
                }
            }
            if ($report->program_id) {
                $class_subjects_reports['program_' . $report->program_id][$report->class_time->format("Y/m/d")] = $report;
                $report_summery[$report->class_time->format("Y/m/d")][$report->program->title] = $report->attendace->where('attendance', "!=", "absent")->where('attendance', "!=", "excused")->count();
                if (!in_array($report->program->title, $class_subjects)) {
                    $class_subjects[] = $report->program->title;
                }
            }
        }
        $teacher = null;
        $teacher_timetable = null;
        if ($class->teachers->where('employee_id', auth()->user()->id)->first()) {
            $teacher = $class->teachers->where('employee_id', auth()->user()->id)->first();
        }


        //Student Attendance

        $students = Student::whereHas('class', function ($q) use ($id) {
            $q->where('class_id', $id);
        })->pluck('id')->toArray();

        $students_full_details = Student::whereHas('class', function ($q) use ($id) {
            $q->where('class_id', $id);
        })->get();


        $year = Carbon::parse($request->from_date)->year;
        $month = Carbon::parse($request->from_date)->month;
        $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);


        $attendances = [];

        $attendance = StudentAttendance::whereIn('student_id', $students)
            ->whereIn('class_report_id', $classReportsIds)
            ->whereYear('class_time', '=', $year)
            ->whereMonth('class_time', '=', $month)
            ->get();

        if (count($attendance) != 0) {
            $attendances[] = $attendance;
        }


        // return $class_programs;
        return view('education::classes.reports.index', compact('attendances', 'days', 'year', 'month', 'current_day', 'class', 'teacher_timetable', 'teacher', 'from_date', 'to_date', 'report_summery', 'class_programs', 'class_subjects_reports', 'class_subjects', 'students_full_details', 'surats', 'hefzEvaluationOptions'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create(Request $request, $id)
    {


        $class = Classes::findOrFail($id)->with('students.hefz_plans');

        $students = $class->students()->get();
        $report_id = $request->get('report_id');
        $from_date = $request->from_date;
        $surats = MoshafSurah::all();
        $teachers = $class->teachers()->pluck('employees.name', 'employees.id');
        $hefz_evaluation_schema = EvaluationSchema::where('target', 'hefz')->first();
        $revision_evaluation_schema = EvaluationSchema::where('target', 'revision')->first();
        $hefz_valuation_options = $hefz_evaluation_schema->options()->get();
        $revision_valuation_options = $revision_evaluation_schema->options()->get();

//        if (isset($report_id)){
        if (isset($from_date)) {
            foreach ($students as $student) {
//                $student->hefz_report = $student->hefz()->where('class_report_id',$report_id)->first();
                $student->hefz_report = $student->hefz()->whereDate('created_at', '=', Carbon::parse($from_date)->toDate())->first();


                $student->hefz_plans->start_from_ayat = $this->getAyatListBySurat($student->hefz_plans->hefz_from_surat, $student->hefz_plans->start_from_ayat);
                $student->hefz_plans->to_ayat = $this->getAyatListBySurat($student->hefz_plans->hefz_to_surat, $student->hefz_plans->to_ayat);

//                $student->revision_report = $student->revision()->where('class_report_id',$report_id)->first();
                $student->revision_report = $student->revision()->whereDate('created_at', Carbon::parse($from_date)->toDate())->first();

                $student->revision_report->revision_from_ayat = $this->getAyatListBySurat($student->revision_report->revision_from_surat, $student->revision_report->revision_from_ayat);
                $student->revision_report->revision_to_ayat = $this->getAyatListBySurat($student->revision_report->revision_to_surat, $student->revision_report->revision_to_ayat);
            }
        }

        //dd($hefz_valuation_options);
        return view('education::classes.reports.create', compact('students', 'class', 'from_date', 'surats', 'teachers', 'hefz_valuation_options', 'revision_valuation_options'));


        // Old stuffs
        if (in_array(auth()->user()->id, $class->teachers()->pluck('employee_id')->toArray())) {
            //$teachers = [auth()->user()->id => auth()->user()->full_name];
        } else {
            //$teachers = $class->teachers()->pluck('full_name', 'employee_id');
        }

        $subjects = [];

        foreach ($class->programs as $program) {
            if ($program->pivot->program_level_id == 0) {
                $subjects['p' . $program->id] = $program->title . ' Program [All levels & Subjects]';
            } else {
                foreach ($program->levels as $level) {
                    if ($level->id == $program->pivot->program_level_id) {
                        foreach ($level->subjects as $subject) {
                            $subjects[$subject->id] = $subject->title;
                        }
                    }
                }
            }
        }

        // $subjects = $class->programs[0]->subjects;

        // return $class->programs[1]->levels[1]->subjects;

        return view('education::classes.reports.create', compact('class', 'teachers', 'subjects'));
    }

    /**
     * Get list of ayats based on surah
     *
     * @param $surah_id
     * @param $ayat_num
     * @return string
     */
    public function getAyatListBySurat($surah_id, $ayat_num): string
    {
        $surah = MoshafSurah::find($surah_id);
        $options = '<option value="" selected>Select</option>';

        if (isset($surah)) {
            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {
                if ($i === $ayat_num) {
                    $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                } else {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';
                }
            }
        }

        return $options;
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        


        $field = $request->get('field');
        $field_value = $request->get('field_value');
        $organization_id = $request->get('organization_id');
        $table = $request->get('table');
        $teacher_id = $request->get('teacher_id');
        $subject_id = $request->get('subject_id');
        $report_id = $request->get('report_id');
        $studyDirection = $request->get('studyDirection');
        $planYearMonth = $request->get('from_date');
        $dateMonthArray = explode('-', $planYearMonth);
        $year = $dateMonthArray[0];
        $month = $dateMonthArray[1];
        $planYearMonth = Carbon::createFromDate($year, $month, 1);
        $planYearMonth = $planYearMonth->format('Y-m');

        $hefz_from_surat = $request->get('start_from_surat');
        $start_from_ayat = $request->get('start_from_ayat');
        $to_surat = $request->get('to_surat');
        $to_ayat = $request->get('to_ayat');
        $from_surat_juz_id = DB::select("SELECT * FROM moshaf_juz WHERE :startSuratId BETWEEN start_surah AND end_surah;", array(
            'startSuratId' => $hefz_from_surat,
        ));

        $to_surat_juz_id = DB::select("SELECT * FROM moshaf_juz WHERE :lastSurahId BETWEEN start_surah AND end_surah;", array(
            'lastSurahId' => $to_surat,
        ));


        $from_surat_juz_id = collect($from_surat_juz_id)->first()->juz;
        $to_surat_juz_id = collect($to_surat_juz_id)->last()->juz;

        $student = Student::whereHas('joint_classes', function ($query) use ($request) {
            $query->where('classes.id', $request->get('class_id'));
        })->where('id', $request->get('student_id'))->first();





        // get center id
        $supervisorCenterId = Classes::where('id', $request->get('class_id'))->first()->center_id;
        // get supervisor id

        //$report = ClassReport::where('class_id',$request->get('class_id'));

        $studyDirection = $this->determineStudyDirection($hefz_from_surat, $to_surat, $start_from_ayat, $to_ayat);

        if ($studyDirection == 'backward') {

            $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                $hefz_from_surat,
                $start_from_ayat,
                $to_surat,
                $to_ayat
            ]);


            $numberofPages = $numberofPages[0]->numberofPagesSum;



        }
        else {


            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                $hefz_from_surat,
                $start_from_ayat,
                $to_surat,
                $to_ayat
            ]);

            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
            $numberofPages = $results[0]->number_of_pages_sum;
        }


        // status column default value is waiting_for_approval which is set in the DB side.
        $plan = $student->hefz_plans()->updateOrCreate(
            [
                'class_id' => $request->get('class_id'),
                'student_id' => $student->id,
                "plan_year_and_month" => $planYearMonth
            ],
            [

                'organization_id' => $organization_id,
                'deleted_at' => NULL,
//                'class_id' => $request->get('class_id'),
                "start_from_surat" => !empty($hefz_from_surat) ? $hefz_from_surat : null,
                "start_from_ayat" => !empty($start_from_ayat) ? $start_from_ayat : null,
                "to_surat" => !empty($to_surat) ? $to_surat : null,
                "to_ayat" => !empty($to_ayat) ? $to_ayat : NULL,
                "start_date" => $request->get('from_date'),
                'level_id' => studentLevel($hefz_from_surat, $to_surat),
                'center_id' => $supervisorCenterId,
                "from_surat_juz_id" => $from_surat_juz_id,
                "to_surat_juz_id" => $to_surat_juz_id,
                "study_direction" => $studyDirection
            ]
        );
        $checkIfQuranRelatedColumnsHaveValues = ($plan->start_from_surat && $plan->start_from_ayat && $plan->to_surat && $plan->to_ayat);

        if ($checkIfQuranRelatedColumnsHaveValues == true) {
            $plan->status = 'waiting_for_approval';
        } else {
            $plan->status = '';
        }
        $plan->save();

        $checkIfAllRequiredColumnsHaveValues = (is_null($plan->study_direction) || is_null($plan->start_from_surat) || is_null($plan->start_from_ayat) || is_null($plan->to_surat) || is_null($plan->to_ayat)) == true ? false : true;

        $statusText = '';
        $faIconClass = 'fa fa-question-circle'; // Default icon
        $faIconColorClass = 'text-secondary'; // Default color
        $iconTitle = 'Status unknown'; // Default title

        if($checkIfAllRequiredColumnsHaveValues) {
            $revisionPlan = $student->revision_plans()->where('plan_year_and_month', $planYearMonth)->first();
            
            // Check if memorization plan is truly active (status = 'active' AND all required fields not null)
            $isMemorizationPlanActive = $plan->status === 'active' && $checkIfAllRequiredColumnsHaveValues;
            
            // Check if revision plan is truly active (status = 'active' AND all required fields not null)
            $isRevisionPlanActive = false;
            if ($revisionPlan) {
                $isRevisionPlanActive = $revisionPlan->status === 'active' 
                    && !is_null($revisionPlan->start_from_surat) 
                    && !is_null($revisionPlan->start_from_ayat) 
                    && !is_null($revisionPlan->to_surat) 
                    && !is_null($revisionPlan->to_ayat);
            }
            
            // Determine status based on refined logic
            if($isMemorizationPlanActive && $isRevisionPlanActive) {
                $statusText = 'Both plans active';
                $faIconClass = 'fa fa-check-circle';
                $faIconColorClass = 'text-success';
                $iconTitle = 'Both plans active';
            }
            elseif($isMemorizationPlanActive) {
                if(!$revisionPlan) {
                    $statusText = 'Memorization plan active, No revision plan';
                    $faIconClass = 'fa fa-book';
                    $faIconColorClass = 'text-success';
                    $iconTitle = 'Memorization plan active, No revision plan';
                } elseif($revisionPlan->status == 'waiting_for_approval') {
                    $statusText = 'Memorization plan active, Revision plan pending approval';
                    $faIconClass = 'fa fa-exclamation-circle';
                    $faIconColorClass = 'text-warning';
                    $iconTitle = 'Memorization plan active, Revision plan pending approval';
                } else {
                    $statusText = 'Memorization plan active, Revision plan inactive';
                    $faIconClass = 'fa fa-exclamation-circle';
                    $faIconColorClass = 'text-warning';
                    $iconTitle = 'Memorization plan active, Revision plan inactive';
                }
            }
            elseif($isRevisionPlanActive) {
                if($plan->status == 'waiting_for_approval') {
                    $statusText = 'Revision plan active, Memorization plan pending approval';
                    $faIconClass = 'fa fa-exclamation-circle';
                    $faIconColorClass = 'text-warning';
                    $iconTitle = 'Revision plan active, Memorization plan pending approval';
                } else {
                    $statusText = 'Revision plan active, Memorization plan inactive';
                    $faIconClass = 'fa fa-exclamation-circle';
                    $faIconColorClass = 'text-warning';
                    $iconTitle = 'Revision plan active, Memorization plan inactive';
                }
            }
            elseif($plan->status == 'waiting_for_approval') {
                if(!$revisionPlan) {
                    $statusText = 'Memorization plan pending approval, No revision plan';
                    $faIconClass = 'fa fa-hourglass-half';
                    $faIconColorClass = 'text-primary';
                    $iconTitle = 'Memorization plan awaiting approval, No revision plan';
                } elseif($revisionPlan->status == 'waiting_for_approval') {
                    $statusText = 'Both plans pending approval';
                    $faIconClass = 'fa fa-hourglass-half';
                    $faIconColorClass = 'text-primary';
                    $iconTitle = 'Both plans awaiting approval';
                } else {
                    $statusText = 'Memorization plan pending approval, Revision plan inactive';
                    $faIconClass = 'fa fa-hourglass-half';
                    $faIconColorClass = 'text-primary';
                    $iconTitle = 'Memorization plan awaiting approval, Revision plan inactive';
                }
            }
            else {
                if(!$revisionPlan) {
                    $statusText = 'Memorization plan inactive, No revision plan';
                    $faIconClass = 'fa fa-book';
                    $faIconColorClass = 'text-muted';
                    $iconTitle = 'Memorization plan inactive, No revision plan';
                } else {
                    $statusText = 'All plans inactive';
                    $faIconClass = 'fa fa-times-circle';
                    $faIconColorClass = 'text-danger';
                    $iconTitle = 'All plans inactive';
                }
            }
        } else {
            $faIconClass = 'fa fa-folder-open';
            $faIconColorClass = 'text-secondary';
            $iconTitle = 'No plans exist';
            $statusText = 'No plans created';
        }

        return response()->json([
            'message' => 'success', 
            'plan' => $plan, 
            'statusText' => $statusText,
            'iconInfo' => [
                'class' => $faIconClass,
                'colorClass' => $faIconColorClass,
                'title' => $iconTitle
            ],
            'numberofPages' => $numberofPages, 
            'allRequiredFieldsFilled' => $checkIfAllRequiredColumnsHaveValues, 
            'table' => $table
        ], 200);


    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function show(Request $request, $id, $date)
    {





        $fromDate = Carbon::parse($date);
        $from_date_year = $fromDate->year;
        $from_date_month = $fromDate->month;



        // Fetch program details once
        // Cache program details
        $programId = \Cache::remember("class_program_{$id}", 3600, function () use ($id) {
            return ClassProgram::where('class_id', $id)->value('program_id');
        });
        $classProgramDetails = \Cache::remember("program_details_{$programId}", 3600, function () use ($programId) {
            return Program::find($programId);
        });
        $programTitle = strtolower($classProgramDetails->title);

// Fetch valuation options and surats once, possibly from cache
        $surats = \Cache::remember('surats', 3600, function () {
            return MoshafSurah::all();
        });
        $hefz_valuation_options = \Cache::remember('hefz_valuation_options', 3600, function () {
            return EvaluationSchema::where('target', 'hefz')->first()->options;
        });
        $revision_valuation_options = \Cache::remember('revision_valuation_options', 3600, function () {
            return EvaluationSchema::where('target', 'revision')->first()->options;
        });



       
        if ($programTitle === 'ijazah and sanad') {


            $class = Classes::with([
                'students' => function ($query) {
                    $query->where('status', 'active')
                        ->orderBy('full_name', 'asc'); // order students by full_name
                },
                'students.user',
                'students.joint_classes' => function ($q) use ($id) {
                    $q->where('class_id', $id);
                },

                // Eager load ijazasanad-related relationships
                'students.ijazasanad_memorization_plans' => function ($query) use ($from_date_year, $from_date_month) {
                    $query->whereYear('start_date', $from_date_year)
                        ->whereMonth('start_date', $from_date_month);
                },
                'students.ijazasanad_revision_plans' => function ($query) use ($from_date_year, $from_date_month) {
                    $query->whereYear('start_date', $from_date_year)
                        ->whereMonth('start_date', $from_date_month);
                },
                'students.ijazaMemorizationReport' => function ($query) use ($fromDate) {
                    $query->whereDate('created_at', $fromDate);
                },
                'students.ijazaRevisionReport' => function ($query) use ($fromDate) {
                    $query->whereDate('created_at', $fromDate);
                },
            ])->find($id);

            if (!$class) {
                return response()->json(['message' => 'Class not found'], 404);
            }



                    // Process students
                     $students = $class->students;

                // Add counts and additional relationships
                   $students->loadCount([
                'ijazasanad_memorization_plans as ijazasanadMemorizationPlan_with_non_null_fields_count' => function ($q) {
                    $q->whereNotNull('start_from_surat')
                        ->whereNotNull('start_from_ayat')
                        ->whereNotNull('to_surat')
                        ->whereNotNull('to_ayat');
                },
                'ijazasanad_revision_plans as ijazasanadRevisionPlan_with_non_null_fields_count' => function ($q) {
                    $q->whereNotNull('start_from_surat')
                        ->whereNotNull('start_from_ayat')
                        ->whereNotNull('to_surat')
                        ->whereNotNull('to_ayat');
                },
            ]);

                // Process students data
            $students->each(function ($student) use ($fromDate) {

                // Process ijazasanad memorization plans
                $student->ijazasanad_memorization_plans->each(function ($iajza_plan) {
                    $iajza_plan->start_from_ayat_select_dropdown = $this->getAyatListBySurat(
                        $iajza_plan->start_from_surat,
                        $iajza_plan->start_from_ayat
                    );
                    $iajza_plan->to_ayat_select_dropdown = $this->getAyatListBySurat(
                        $iajza_plan->to_surat,
                        $iajza_plan->to_ayat
                    );
                });


                // Access the preloaded ijazasanad revision report
                $student->ijazasanad_revision_report = $student->ijazaRevisionReport->first();


                if ($student->ijazasanad_revision_report) {
                    $student->ijazasanad_revision_report->revision_from_ayat = $this->getAyatListBySurat(
                        $student->ijazasanad_revision_report->revision_from_surat,
                        $student->ijazasanad_revision_report->revision_from_ayat
                    );
                    $student->ijazasanad_revision_report->revision_to_ayat = $this->getAyatListBySurat(
                        $student->ijazasanad_revision_report->revision_to_surat,
                        $student->ijazasanad_revision_report->revision_to_ayat
                    );
                }
            });






            $view = view('education::monthlyPlan.createIjazasanad', compact('classProgramDetails', 'students', 'id', 'class', 'date','fromDate', 'surats', 'hefz_valuation_options', 'revision_valuation_options'));
        }
        elseif (Str::contains(strtolower($programTitle), ['nuraniyah', 'nouranya'])) {





            $class = Classes::with([
                'students' => function ($query) {
                    $query->where('status', 'active')
                    ->orderBy('full_name', 'asc'); // order students by full_name

                },
                'students.user',
                'students.joint_classes' => function ($q) use ($id) {
                    $q->where('class_id', $id);
                },

                // Eager load ijazasanad-related relationships
                'students.nouranya_plans' => function ($query) use ($from_date_year, $from_date_month) {
                    $query->whereYear('start_date', $from_date_year)
                        ->whereMonth('start_date', $from_date_month);
                },
                'students.nouranya' => function ($query) use ($fromDate) {
                    $query->whereDate('created_at', $fromDate);
                },
            ])->find($id);



            if (!$class) {
                return response()->json(['message' => 'Class not found'], 404);
            }



                // Process students
                            $students = $class->students;

                //            dd($students);






            $view = view('education::monthlyPlan.nouranyaCreate', compact('classProgramDetails', 'students', 'id', 'class', 'fromDate', 'surats', 'hefz_valuation_options', 'revision_valuation_options'));
        }

        else {


           

           
            $class = Classes::with([
                'students' => function ($query) {
                    $query->where('status', 'active')
                          ->orderBy('full_name', 'asc');
                },
                'students.user',
                'students.joint_classes' => function ($q) use ($id) {
                    $q->where('class_id', $id);
                },
                'students.hefz_plans' => function ($query) use ($fromDate) {
                    $planYearMonth = \Carbon\Carbon::parse($fromDate)->format('Y-m');
                    $query->where('plan_year_and_month', $planYearMonth);
                },
                'students.revision_plans' => function ($query) use ($fromDate) {
                    $planYearMonth = \Carbon\Carbon::parse($fromDate)->format('Y-m');
                    $query->where('plan_year_and_month', $planYearMonth);
                },
                'students.revision' => function ($query) use ($fromDate) {
                    $query->whereDate('created_at', $fromDate);
                },
            ])->find($id);




            if (!$class) {
                // Handle the case where the class is not found
                return response()->json(['message' => 'Class not found'], 404);
            }

            // Process students
            $students = $class->students->unique('id');


            // Add counts and additional relationships
            $students->loadCount([
                'hefz_plans as memorizationPlan_with_non_null_fields_count' => function ($q) use ($fromDate) {
                    $planYearMonth = \Carbon\Carbon::parse($fromDate)->format('Y-m');
                    $q->where('plan_year_and_month', $planYearMonth)
                      ->whereNotNull('start_from_surat')
                      ->whereNotNull('start_from_ayat')
                      ->whereNotNull('to_surat')
                      ->whereNotNull('to_ayat');
                },
                'revision_plans as revisionPlan_with_non_null_fields_count' => function ($q) use ($fromDate) {
                    $planYearMonth = \Carbon\Carbon::parse($fromDate)->format('Y-m');
                    $q->where('plan_year_and_month', $planYearMonth)
                      ->whereNotNull('start_from_surat')
                      ->whereNotNull('start_from_ayat')
                      ->whereNotNull('to_surat')
                      ->whereNotNull('to_ayat');
                },
                'hefz_plans as memorizationPlan_active_count' => function ($q) use ($fromDate) {
                    $planYearMonth = \Carbon\Carbon::parse($fromDate)->format('Y-m');
                    $q->where('plan_year_and_month', $planYearMonth)
                      ->where('status', 'active')
                      ->whereNotNull('start_from_surat')
                      ->whereNotNull('start_from_ayat')
                      ->whereNotNull('to_surat')
                      ->whereNotNull('to_ayat');
                },
                'revision_plans as revisionPlan_active_count' => function ($q) use ($fromDate) {
                    $planYearMonth = \Carbon\Carbon::parse($fromDate)->format('Y-m');
                    $q->where('plan_year_and_month', $planYearMonth)
                      ->where('status', 'active')
                      ->whereNotNull('start_from_surat')
                      ->whereNotNull('start_from_ayat')
                      ->whereNotNull('to_surat')
                      ->whereNotNull('to_ayat');
                }
            ]);

            // Process students data
            $students->each(function ($student) use ($fromDate) {
                // Process hefz plans
                $student->hefz_plans->each(function ($hefz_plan) {
                    $hefz_plan->start_from_ayat_select_dropdown = $this->getAyatListBySurat(
                        $hefz_plan->start_from_surat,
                        $hefz_plan->start_from_ayat
                    );
                    $hefz_plan->to_ayat_select_dropdown = $this->getAyatListBySurat(
                        $hefz_plan->to_surat,
                        $hefz_plan->to_ayat
                    );
                });

                // Access the preloaded revision report
                $student->revision_report = $student->revision->first();

                if ($student->revision_report) {
                    $student->revision_report->revision_from_ayat = $this->getAyatListBySurat(
                        $student->revision_report->revision_from_surat,
                        $student->revision_report->revision_from_ayat
                    );
                    $student->revision_report->revision_to_ayat = $this->getAyatListBySurat(
                        $student->revision_report->revision_to_surat,
                        $student->revision_report->revision_to_ayat
                    );
                }
            });



            $view = view('education::monthlyPlan.create', compact('classProgramDetails', 'students', 'id', 'class', 'fromDate', 'surats', 'hefz_valuation_options', 'revision_valuation_options'));
        }

        return $view;
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($class_id, $report_id)
    {
        $surats = MoshafSurah::all();

        $report = ClassReport::findOrFail($report_id);

        $class = Classes::findOrFail($report->class_id);
        $subject = [];
        $special_program_data = [];

        if ($report->subject_id == 0) {
            if ($report->program->setting['special_program_code']) {
                if ($report->program->setting['special_program_code'] = 'hefz') {
                    $special_program_data['data'] = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->evaluation($report);

                    $special_program_data['hefz_evaluation_schema'] = EvaluationSchema::where('target', 'hefz')->first();

                    $special_program_data['revision_evaluation_schema'] = EvaluationSchema::where('target', 'revision')->first();
                }
            }
        } else {
            $subject = Subject::findOrFail($report->subject_id);
        }

        $students = [];
        // return $special_program_data;
        // return $subject->contents;

        return view('education::classes.reports.edit', compact('class', 'report', 'students', 'subject', 'special_program_data', 'surats'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request)
    {
        $requestData = $request->all();
        // return $requestData;

        $report = ClassReport::findOrFail($request->report_id);
        if (isset($request->teacher_attendance) && $request->teacher_attendance == 'absent') {
            $report->status = 'completed';
            $report->notes = 'teacher is absent';
            $report->save();

            return redirect('workplace/education/classes/' . $report->class_id . '/reports');
        } elseif (isset($request->student_attendance)) {
            foreach ($request->student_attendance as $key => $value) {
                $attendance = new StudentAttendance();

                $attendance->organization_id = config('organization_id');
                $attendance->class_report_id = $report->id;
                $attendance->student_id = $key;
                $attendance->class_time = $report->class_time;
                $attendance->attendance = $value;
                $attendance->created_by = auth()->user()->id;

                $attendance->save();
                // $attendance->note = $report->;
            }


            $report->status = 'attendance_submited';

            $report->save();
        } elseif (isset($request->student_performance)) {
            if ($report->subject_id == 0) {
                if ($report->program->setting['special_program_code']) {
                    if ($report->program->setting['special_program_code'] = 'hefz') {
                        $report_data = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->evaluation($report);
                        foreach ($request->student_performance as $student_id => $result) {
                            if (isset($report_data[$student_id])) {
                                if (isset($report_data[$student_id]['hefz']) && $report_data[$student_id]['hefz'] && $result['hefz']) {
                                    $hefz_report = new StudentHefzReport();

                                    $hefz_plans->student_id = $student_id;
                                    $hefz_plans->organization_id = config('organization_id');
                                    $hefz_plans->class_id = $report->class_id;
                                    $hefz_plans->class_time = $report->class_time;
                                    $hefz_plans->created_by = auth()->user()->id;
                                    // $hefz_plans->hefz_from_surat = $report_data[$student_id]['hefz']['from_surat'];
                                    // $hefz_plans->start_from_ayat = $report_data[$student_id]['hefz']['from_ayat'];
                                    // $hefz_plans->hefz_to_surat = $report_data[$student_id]['hefz']['to_surat'];
                                    // $hefz_plans->to_ayat = $report_data[$student_id]['hefz']['to_ayat'];

                                    $hefz_plans->hefz_from_surat = $requestData['report'][$student_id]['hefz']['from_surat'];
                                    $hefz_plans->start_from_ayat = $requestData['report'][$student_id]['hefz']['from_ayat'];
                                    $hefz_plans->hefz_to_surat = $requestData['report'][$student_id]['hefz']['to_surat'];
                                    $hefz_plans->to_ayat = $requestData['report'][$student_id]['hefz']['to_ayat'];

                                    $hefz_plans->hefz_evaluation_id = $result['hefz'];

                                    $hefz_plans->class_report_id = $report->id;


                                    $hefz_plans->save();
                                }
                                if (isset($report_data[$student_id]['revision']) && $report_data[$student_id]['revision'] && $result['revision']) {
                                    $revision_report = new StudentRevisionReport();

                                    $revision_report->student_id = $student_id;
                                    $revision_report->organization_id = config('organization_id');
                                    $revision_report->class_id = $report->class_id;
                                    $revision_report->created_by = auth()->user()->id;
                                    // $revision_report->revision_from_surat = $report_data[$student_id]['revision']['from_surat'];
                                    // $revision_report->revision_from_ayat = $report_data[$student_id]['revision']['from_ayat'];
                                    // $revision_report->revision_to_surat = $report_data[$student_id]['revision']['to_surat'];
                                    // $revision_report->revision_to_ayat = $report_data[$student_id]['revision']['to_ayat'];

                                    $revision_report->revision_from_surat = $requestData['report'][$student_id]['revision']['from_surat'];
                                    $revision_report->revision_from_ayat = $requestData['report'][$student_id]['revision']['from_ayat'];
                                    $revision_report->revision_to_surat = $requestData['report'][$student_id]['revision']['to_surat'];
                                    $revision_report->revision_to_ayat = $requestData['report'][$student_id]['revision']['to_ayat'];

                                    if (isset($requestData['report'][$student_id]['revision']['revision_note'])) {
                                        $revision_report->revision_evaluation_note = $requestData['report'][$student_id]['revision']['revision_note'];
                                    }
                                    if (isset($requestData['report'][$student_id]['revision']['revision_type'])) {
                                        $revision_report->revision_type = $requestData['report'][$student_id]['revision']['revision_type'];
                                    }


                                    $revision_report->revision_evaluation_id = $result['revision'];

                                    $revision_report->class_report_id = $report->id;

                                    $revision_report->save();
                                }
                            }
                        }

                        $report->status = 'completed';

                        $report->save();
                    }
                }
            } else {
            }
        }

        // return $requestData;

        Session::flash('flash_message', 'Class updated!');

        return redirect('workplace/education/classes/' . $report->class_id . '/reports/' . $report->id . '/edit');
    }


    /**
     * Update programs availabilty in class.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function programs(Request $request)
    {
        auth()->user()->can('edit class_programs');

        $id = $request->class_id;

        $class = Classes::findOrFail($id);

        $class->programs()->sync($request->class_programs);

        Session::flash('flash_message', 'Program updated!');

        if ($request->ajax()) {
            return response()->json(['status' => 'success']);
        }

        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Classes::destroy($id);

        Session::flash('flash_message', 'Class deleted!');

        return redirect('workplace/education/classes');
    }


    public function studentReport($student_id)
    {
        return DB::table('student_attendances')
            ->leftJoin('class_reports', 'student_attendances.class_report_id', 'class_reports.id')
            ->leftJoin('student_hefz_report', 'student_attendances.class_report_id', '=', 'student_hefz_report.class_report_id')
            ->leftJoin('evaluation_schema_options as hefz_evaluation', 'hefz_evaluation.id', '=', 'student_hefz_report.hefz_evaluation_id')
            ->leftJoin('student_revision_report', 'student_attendances.class_report_id', '=', 'student_revision_report.class_report_id')
            ->leftJoin('evaluation_schema_options as revision_evaluation', 'revision_evaluation.id', '=', 'student_revision_report.revision_evaluation_id')
            ->select(
                'student_attendances.attendance',
                'student_attendances.note as student_attendance_note',
                'class_reports.*',
                'student_hefz_report.*',
                'hefz_evaluation.title as hefz_evaluation_title',
                'student_revision_report.*',
                'revision_evaluation.title as revision_evaluation_title'
            )
            ->where('student_attendances.student_id', $student_id)
            ->orderBy('class_reports.class_time')
            // ->select('users.*', 'contacts.phone', 'orders.price')
            ->get();
    }

    public function studentExpectedProgressPlan($student_id) // Hefz and Morja'ah Program
    {
    }

    private function getNextReportTime($last_report_time, $timetable, $class)
    {

        if (!$last_report_time) {
            $class_date = $timetable->start_at;
        } else {
            $class_date = Carbon::parse($last_report_time);
            $class_date = $class_date->addDay();
//            $class_date = $last_report_time->addDay();
        }

        while ($class->studentsAtDate($class_date)->count() < 1 && $class_date < Carbon::now()) {
            $class_date = $class_date->addDay();
        }

        while (!$timetable[strtolower($class_date->format('D'))]) {
            $class_date = $class_date->addDay();
        }
        $class_date = $class_date->addDay();
        // $class_date = $class_date->addDay();
        // dump($class_date);

        return $class_date;
    }

    private function errorNoTeacher($class_id)
    {
        $error_msg = "Opps! . Some Subjects In the Class Don't have Teacher!!";

        return view('education::classes.errors', compact('class_id', 'error_msg'));
    }

    private function errorNoTimetable($class_id)
    {
        $error_msg = "Opps! . Some Subjects In the Class Don't have Timetable!!";

        return view('education::classes.errors', compact('class_id', 'error_msg'));
    }


    // V2

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function prepareReport($class_id, $report_id)
    {
        $report = ClassReport::findOrFail($report_id);


        if ($report->status == 'completed') {
            return redirect('workplace/education/classes/' . $report->class_id . '/reports/' . $report->id . '/edit');
        }
        $surats = MoshafSurah::all();

        $suar = $surats->map(function ($surah) {
            return [
                'text' => $surah->name,
                'value' => $surah->id,
                'num_ayat' => $surah->num_ayat
            ];
        });


        $class = Classes::with('students.hefz')->with('students.revision')->findOrFail($report->class_id);


        $subject = [];
        $special_program_data = [];
        $special_program = null;

        $hefzEvaluationOptions = [];
        $revisionEvaluationOptions = [];

        if ($report->subject_id == 0 && $report->program->setting['special_program_code'] && $report->program->setting['special_program_code'] = 'hefz') {
            $special_program_data = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->studentsEvaluation($report);

            $hefzEvaluationOptions = EvaluationSchema::where('target', 'hefz')->first()->options->map(function ($option) {
                return ['text' => $option->code . ' - ' . $option->title, 'value' => $option->id];
            });

            $revisionEvaluationOptions = EvaluationSchema::where('target', 'revision')->first()->options->map(function ($option) {
                return ['text' => $option->code . ' - ' . $option->title, 'value' => $option->id];
            });

            $special_program = 'hefz';
        } else {
            $subject = Subject::findOrFail($report->subject_id);
        }
        // dd($special_program_data);
        $students = [];
        $lessons = [];
        // $class->students->map(function($student){
        //     return [

        //     ]
        // });

        foreach ($class->students as $student) {
            if ($special_program
                && $special_program == 'hefz'
                && isset($special_program_data[$student->id])
            ) {
                $students[$student->id] = $special_program_data[$student->id];
                $students[$student->id]['attandance'] = null;
                $students[$student->id]['evaluation'] = null;
            } else {
                $students[$student->id] = [
                    'attandance' => null,
                    'evaluation' => null,
                    'hefz' => [],
                    'revision' => [],
                    'lesson' => [
                        'id' => null,
                        'evaluations' => json_decode('{}')
                    ]
                ];
                if ($subject) {
                    $lessons = $subject->contents->map(function ($lesson) {
                        $evaluation_schems = [];
                        foreach ($lesson->evaluation_schemas as $evaluation) {
                            foreach ($evaluation->options as $option) {
                                $evaluation_schems[$evaluation->title][] = [
                                    'text' => $option->title,
                                    'value' => $option->id
                                ];
                            }
                        }

                        return [
                            'text' => $lesson->title,
                            'value' => $lesson->id,
                            'evaluation_schems' => $evaluation_schems
                        ];
                    });
                }
            }
        }


        return view('education::classes.reports.v3.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
//        return view('education::classes.reports.v2.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
//        return view('education::classes.reportsBackup.v2.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
    }

    public function storeTempReport($class_id, $report_id)
    {

        $report = ClassReport::findOrFail($report_id);
        $report->temp_data = json_encode(request()->except('_token'));
        $report->save();
    }

    public function storeFinalReport(Request $request, $class_id, $report_id)
    {
        // dd($request->all());
        $this->validate($request, [
            'students.*' => 'required',
            'teacher_attended' => 'required'
        ]);

        $report = ClassReport::findOrFail($report_id);

        StudentAttendance::where('class_report_id', $report->id)->delete();
        StudentHefzReport::where('class_report_id', $report->id)->delete();
        StudentRevisionReport::where('class_report_id', $report->id)->delete();


        $requestData = $request->all();

        if (!$request->teacher_attended) {
            $report->status = 'completed';
            $report->notes = 'teacher is absent';
            $report->temp_data = '';
            $report->save();

            return response()->json(['status' => 'completed'], 200);
        }

        foreach ($request->students as $studentID => $studentReport) {
            $attendance = new StudentAttendance();

            $attendance->organization_id = config('organization_id');
            $attendance->class_report_id = $report->id;
            $attendance->student_id = $studentID;
            $attendance->class_time = $report->class_time;
            $attendance->attendance = $studentReport['attandance'];
            $attendance->created_by = auth()->user()->id;
            $attendance->save();

            if (in_array($attendance->attendance, ['on_time', 'late'])) {
                if ($report->subject_id == 0 && $report->program->setting['special_program_code'] && $report->program->setting['special_program_code'] = 'hefz') {
                    if (isset($studentReport['hefz']) && $studentReport['hefz']) {
                        $hefz_report = new StudentHefzReport();
                        $hefz_plans->student_id = $studentID;
                        $hefz_plans->organization_id = config('organization_id');
                        $hefz_plans->class_id = $report->class_id;
                        $hefz_plans->class_time = $report->class_time;
                        $hefz_plans->created_by = auth()->user()->id;

                        $hefz_plans->hefz_from_surat = $studentReport['hefz']['from_surat'];
                        $hefz_plans->start_from_ayat = $studentReport['hefz']['from_ayat'];
                        $hefz_plans->hefz_to_surat = $studentReport['hefz']['to_surat'];
                        $hefz_plans->to_ayat = $studentReport['hefz']['to_ayat'];

                        $hefz_plans->hefz_evaluation_id = $studentReport['hefz']['evaluation'];
                        $hefz_plans->class_report_id = $report->id;

                        $hefz_plans->save();
                    }
                    if (isset($studentReport['revision']) && $studentReport['revision']) {
                        $revision_report = new StudentRevisionReport();

                        $revision_report->student_id = $studentID;
                        $revision_report->organization_id = config('organization_id');
                        $revision_report->class_id = $report->class_id;
                        $revision_report->created_by = auth()->user()->id;


                        $revision_report->revision_from_surat = $studentReport['revision']['from_surat'];
                        $revision_report->revision_from_ayat = $studentReport['revision']['from_ayat'];
                        $revision_report->revision_to_surat = $studentReport['revision']['to_surat'];
                        $revision_report->revision_to_ayat = $studentReport['revision']['to_ayat'];

                        if (isset($studentReport['revision']['revision_note'])) {
                            $revision_report->revision_evaluation_note = $studentReport['revision']['revision_note'];
                        }
                        if (isset($studentReport['revision']['revision_type'])) {
                            $revision_report->revision_type = $studentReport['revision']['revision_type'];
                        }


                        $revision_report->revision_evaluation_id = $studentReport['revision']['evaluation'];

                        $revision_report->class_report_id = $report->id;

                        $revision_report->save();
                    }
                } else {
                    // Todo: ADD REPORT OF NORMAL PROGRAM
                    if (isset($studentReport['lesson']) && $studentReport['lesson']) {
                        $lesson_report = new LessonReport();

                        $lesson_report->student_id = $studentID;
                        $lesson_report->organization_id = config('organization_id');
                        $lesson_report->class_id = $report->class_id;
                        $lesson_report->created_by = auth()->user()->id;
                        $lesson_report->class_time = $report->class_time;


                        $lesson_report->lesson_id = $studentReport['lesson']['id'];

                        if (isset($studentReport['lesson']['note'])) {
                            $lesson_report->note = $studentReport['revision']['note'];
                        }

                        $lesson_report->class_report_id = $report->id;
                        $lesson_report->save();
                        foreach ($studentReport['lesson']['evaluations'] as $label => $evaluation_option) {
                            $lesson_report->evaluations()->create(['evaluation_option_id' => $evaluation_option]);
                        }
                    }
                }
            }
        }
        $report->temp_data = '';
        $report->status = 'completed';
        $report->save();
        return response()->json(['status' => 'completed'], 200);
    }

    public static function determineStudyDirection($hefz_from_surat, $to_surat, $start_from_ayat, $to_ayat)
    {


        if (!empty($hefz_from_surat) && !empty($to_surat)) {

            if (($hefz_from_surat == $to_surat) && $start_from_ayat < $to_ayat) {


                return 'forward';

            }

            if (($hefz_from_surat == $to_surat) && $start_from_ayat > $to_ayat) {


                return 'backward';

            }


            if ($hefz_from_surat > $to_surat) {

                return 'backward';

            }

            if ($hefz_from_surat < $to_surat) {

                return 'forward';

            }
        }

    }



    public function getMonthsWithRecords($classId, $year)
    {
        try {
            $class = \App\Classes::with('programs')->findOrFail($classId);
            $program = $class->programs->first();

            // Determine which table to query based on program
            $months = [];
            
            if (str_contains(strtolower($program->title), 'ijazah and sanad')) {
                $months = \App\StudentIjazasanadMemorizationReport::where('class_id', $classId)
                    ->whereYear('created_at', $year)
                    ->selectRaw('MONTH(created_at) as month')
                    ->distinct()
                    ->pluck('month')
                    ->toArray();
            } 
            elseif (str_contains(strtolower($program->title), 'nuraniyah') || str_contains(strtolower($program->title), 'nouranya')) {
                $months = \App\StudentNouranyaReport::where('class_id', $classId)
                    ->whereYear('created_at', $year)
                    ->selectRaw('MONTH(created_at) as month')
                    ->distinct()
                    ->pluck('month')
                    ->toArray();
            }
            else {
                // Default to memorization program
                // StudentRevisionReport should also be taken care of

                $hefzMonths = \App\StudentHefzReport::where('class_id', $classId)
                    ->whereYear('created_at', $year)
                    ->selectRaw('MONTH(created_at) as month')
                    ->distinct()
                    ->pluck('month')
                    ->toArray();

                $revisionMonths = \App\StudentRevisionReport::where('class_id', $classId)
                    ->whereYear('created_at', $year)
                    ->selectRaw('MONTH(created_at) as month')
                    ->distinct()
                    ->pluck('month')
                    ->toArray();

                $months = array_unique(array_merge($hefzMonths, $revisionMonths));
                sort($months);
            }
            
            return response()->json($months);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

}

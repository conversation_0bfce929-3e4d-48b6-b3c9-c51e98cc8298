<?php

namespace Modules\Communicate\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Mail\Mailer;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class Send<PERSON>mail<PERSON>ob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $data = [];
    protected $details;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data, $details)
    {
        $this->data = $data;
        $this->details = $details;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(Mailer $mailer)
    {




        //  $email = new SendEmailTestMail();

        // Mail::to($this->details['email'])->send($email);

        // foreach($this->details as $detail){
        $mailer->send('modules.site.templates.wajeha.backEnd.emails.mail', ['data'=> $this->data], function ($message) {

            $message->from($this->data['system_email'], $this->data['organization_name']);

            $message->to($this->details)->subject($this->data['email_sms_title']);

        });
        // }

    }
}

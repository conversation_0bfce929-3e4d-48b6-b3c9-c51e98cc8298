/*----------------------------------------------------
@File: Default Styles
@Author: UOSdesign

This file contains the styling for the actual theme, this
is the file you need to edit to change the look of the
theme.
---------------------------------------------------- */
/*=====================================================================
@Template Name: sassco
@Developed By: UOSdesign

=====================================================================*/
/*----------------------------------------------------*/
@import url("https://fonts.googleapis.com/css?family=Nunito+Sans:300,300i,400,600,700,700i&display=swap");
/*----------------------------------------------------*/
/*----------------------------------------------------*/
/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* big desktop :1500px. */
/* Normal desktop :1200px. */
/* Normal desktop :992px. */
/* Tablet desktop :768px. */
/* small mobile :320px. */
/* Large Mobile :480px. */
/*----------------------------------------------------*/
/*----------------------------------------------------*/
@import url("https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700,800|Roboto:300,400,500,700&display=swap");
@import url("https://fonts.googleapis.com/css?family=Nunito+Sans:300,300i,400,600,700,700i&display=swap");
/* line 1, ../../../../Desktop/role_permission/scss/_extend.scss */
.brand_area_2 {
  background-image: -webkit-gradient(linear, left bottom, left top, from(#f6feff), to(#fbf5ff));
  background-image: -o-linear-gradient(bottom, #f6feff, #fbf5ff);
  background-image: linear-gradient(to top, #f6feff, #fbf5ff);
}

/* line 6, ../../../../Desktop/role_permission/scss/_extend.scss */
.features_area_2 .single_feature .icon {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#83da3a), to(#29c89c));
  background-image: -o-linear-gradient(top, #83da3a, #29c89c);
  background-image: linear-gradient(to bottom, #83da3a, #29c89c);
}

/* line 11, ../../../../Desktop/role_permission/scss/_extend.scss */
.features_area_2 .col-lg-3:nth-child(2) .single_feature .icon {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#df35f0), to(#4543de));
  background-image: -o-linear-gradient(top, #df35f0, #4543de);
  background-image: linear-gradient(to bottom, #df35f0, #4543de);
}

/* line 16, ../../../../Desktop/role_permission/scss/_extend.scss */
.features_area_2 .col-lg-3:nth-child(3) .single_feature .icon {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#e6495b), to(#ee33f0));
  background-image: -o-linear-gradient(top, #e6495b, #ee33f0);
  background-image: linear-gradient(to bottom, #e6495b, #ee33f0);
}

/* line 21, ../../../../Desktop/role_permission/scss/_extend.scss */
.features_area_2 .col-lg-3:nth-child(4) .single_feature .icon {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#fa8732), to(#f8be32));
  background-image: -o-linear-gradient(top, #fa8732, #f8be32);
  background-image: linear-gradient(to bottom, #fa8732, #f8be32);
}

/* line 27, ../../../../Desktop/role_permission/scss/_extend.scss */
.prising_area {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#fbf5ff));
  background-image: -o-linear-gradient(top, #ffffff, #fbf5ff);
  background-image: linear-gradient(to bottom, #ffffff, #fbf5ff);
}

/* line 32, ../../../../Desktop/role_permission/scss/_extend.scss */
.prising_area .single_prising.active {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#df35f0), to(#4543de));
  background-image: -o-linear-gradient(top, #df35f0, #4543de);
  background-image: linear-gradient(to bottom, #df35f0, #4543de);
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* 1. Theme default css */
/* line 4, ../../../../Desktop/role_permission/scss/_reset.scss */
body {
  font-family: "Nunito Sans", sans-serif;
  font-weight: normal;
  font-style: normal;
}

/* line 11, ../../../../Desktop/role_permission/scss/_reset.scss */
.img {
  max-width: 100%;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

/* line 15, ../../../../Desktop/role_permission/scss/_reset.scss */
a,
.button,
button {
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

/* line 20, ../../../../Desktop/role_permission/scss/_reset.scss */
a:focus,
.button:focus, button:focus {
  text-decoration: none;
  outline: none;
}

/* line 25, ../../../../Desktop/role_permission/scss/_reset.scss */
a:focus {
  text-decoration: none;
}

/* line 28, ../../../../Desktop/role_permission/scss/_reset.scss */
a:focus,
a:hover,
.portfolio-cat a:hover,
.footer -menu li a:hover {
  text-decoration: none;
}

/* line 35, ../../../../Desktop/role_permission/scss/_reset.scss */
input:focus, button:focus {
  outline: none;
}

/* line 38, ../../../../Desktop/role_permission/scss/_reset.scss */
a,
button {
  color: #fff;
  outline: medium none;
}

/* line 43, ../../../../Desktop/role_permission/scss/_reset.scss */
h1, h2, h3, h4, h5 {
  font-family: "Nunito Sans", sans-serif;
  color: #1c1c1c;
}

/* line 47, ../../../../Desktop/role_permission/scss/_reset.scss */
h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
  color: inherit;
}

/* line 56, ../../../../Desktop/role_permission/scss/_reset.scss */
ul {
  margin: 0px;
  padding: 0px;
}

/* line 60, ../../../../Desktop/role_permission/scss/_reset.scss */
li {
  list-style: none;
}

/* line 63, ../../../../Desktop/role_permission/scss/_reset.scss */
p {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.75;
  color: #505050;
  margin-bottom: 13px;
  font-family: "Nunito Sans", sans-serif;
}

/* line 72, ../../../../Desktop/role_permission/scss/_reset.scss */
label {
  color: #7e7e7e;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
}

/* line 78, ../../../../Desktop/role_permission/scss/_reset.scss */
*::-moz-selection {
  background: #444;
  color: #fff;
  text-shadow: none;
}

/* line 83, ../../../../Desktop/role_permission/scss/_reset.scss */
::-moz-selection {
  background: #444;
  color: #fff;
  text-shadow: none;
}

/* line 88, ../../../../Desktop/role_permission/scss/_reset.scss */
::selection {
  background: #444;
  color: #fff;
  text-shadow: none;
}

/* line 93, ../../../../Desktop/role_permission/scss/_reset.scss */
*::-webkit-input-placeholder {
  color: #cccccc;
  font-size: 14px;
  opacity: 1;
}

/* line 98, ../../../../Desktop/role_permission/scss/_reset.scss */
*:-ms-input-placeholder {
  color: #cccccc;
  font-size: 14px;
  opacity: 1;
}

/* line 103, ../../../../Desktop/role_permission/scss/_reset.scss */
*::-ms-input-placeholder {
  color: #cccccc;
  font-size: 14px;
  opacity: 1;
}

/* line 108, ../../../../Desktop/role_permission/scss/_reset.scss */
*::placeholder {
  color: #cccccc;
  font-size: 14px;
  opacity: 1;
}

/* button style */
/* line 116, ../../../../Desktop/role_permission/scss/_reset.scss */
.owl-carousel .owl-nav div {
  background: #f2fcff;
  height: 60px;
  left: 0px;
  line-height: 60px;
  position: absolute;
  text-align: center;
  top: 0;
  right: 0;
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  width: 60px;
  font-size: 12px;
  color: #c7cfd2;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}

/* line 151, ../../../../Desktop/role_permission/scss/_reset.scss */
.owl-carousel:hover .owl-nav div {
  opacity: 1;
  visibility: visible;
}

/* line 154, ../../../../Desktop/role_permission/scss/_reset.scss */
.owl-carousel:hover .owl-nav div:hover {
  color: #458efe;
}

/* line 163, ../../../../Desktop/role_permission/scss/_reset.scss */
.mb-85 {
  margin-bottom: 85px;
}

@media (max-width: 767px) {
  /* line 163, ../../../../Desktop/role_permission/scss/_reset.scss */
  .mb-85 {
    margin-bottom: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 163, ../../../../Desktop/role_permission/scss/_reset.scss */
  .mb-85 {
    margin-bottom: 30px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 163, ../../../../Desktop/role_permission/scss/_reset.scss */
  .mb-85 {
    margin-bottom: 40px;
  }
}

/* line 175, ../../../../Desktop/role_permission/scss/_reset.scss */
.mb-65 {
  margin-bottom: 65px;
}

@media (max-width: 767px) {
  /* line 175, ../../../../Desktop/role_permission/scss/_reset.scss */
  .mb-65 {
    margin-bottom: 20px;
  }
}

/* line 181, ../../../../Desktop/role_permission/scss/_reset.scss */
.mt-80 {
  margin-top: 80px;
}

/* line 185, ../../../../Desktop/role_permission/scss/_reset.scss */
.mb-40 {
  margin-bottom: 40px !important;
}

/* line 190, ../../../../Desktop/role_permission/scss/_reset.scss */
.home_2 #back-top a {
  background: #4543de;
  -webkit-box-shadow: 0px 10px 20px 0px rgba(69, 67, 222, 0.4);
  -moz-box-shadow: 0px 10px 20px 0px rgba(69, 67, 222, 0.4);
  box-shadow: 0px 10px 20px 0px rgba(69, 67, 222, 0.4);
}

/* line 196, ../../../../Desktop/role_permission/scss/_reset.scss */
#back-top {
  position: fixed;
  right: 20px;
  bottom: 30px;
  cursor: pointer;
  z-index: 9999999;
  display: none;
}

/* line 203, ../../../../Desktop/role_permission/scss/_reset.scss */
#back-top a {
  display: inline-block;
  height: 40px;
  width: 40px;
  text-align: center;
  display: block;
  color: #fff;
  line-height: 40px;
  background: #ff4357;
  font-size: 15px;
  border-radius: 30px;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  -webkit-box-shadow: 0px 10px 20px 0px rgba(255, 67, 87, 0.4);
  -moz-box-shadow: 0px 10px 20px 0px rgba(255, 67, 87, 0.4);
  box-shadow: 0px 10px 20px 0px rgba(255, 67, 87, 0.4);
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_btn.scss */
.boxed_btn {
  background: #ff4357;
  padding: 10px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  display: inline-block;
  font-size: 16px;
  color: #fff;
  font-weight: 600;
  padding: 12px 40px;
  border: 1px solid transparent;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_btn.scss */
  .boxed_btn {
    padding: 12px 30px;
  }
}

/* line 14, ../../../../Desktop/role_permission/scss/_btn.scss */
.boxed_btn:hover {
  color: #ff4357;
  border-color: #ff4357;
  background: transparent;
}

/* line 22, ../../../../Desktop/role_permission/scss/_btn.scss */
.boxed_btn_2 {
  padding: 10px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  display: inline-block;
  font-size: 16px;
  color: #ff4357;
  font-weight: 600;
  padding: 12px 40px;
  border: 1px solid #ff4357;
}

@media (max-width: 767px) {
  /* line 22, ../../../../Desktop/role_permission/scss/_btn.scss */
  .boxed_btn_2 {
    padding: 9px 20px;
  }
}

/* line 35, ../../../../Desktop/role_permission/scss/_btn.scss */
.boxed_btn_2:hover {
  color: #fff;
  background: #ff4357;
}

/* line 41, ../../../../Desktop/role_permission/scss/_btn.scss */
.line_btn {
  font-size: 16px;
  color: #fff;
  padding: 12px 41px;
  border: 1px solid #fff;
  display: inline-block;
  border-radius: 5px;
  color: #fff;
  font-weight: 600;
}

@media (max-width: 767px) {
  /* line 41, ../../../../Desktop/role_permission/scss/_btn.scss */
  .line_btn {
    padding: 12px 30px;
  }
}

/* line 53, ../../../../Desktop/role_permission/scss/_btn.scss */
.line_btn:hover {
  background: #fff;
  color: #000;
}

/* line 58, ../../../../Desktop/role_permission/scss/_btn.scss */
.blank_btn {
  font-size: 16px;
  border: 1px solid #fff;
  border-radius: 4px;
  padding: 12px 65px;
  display: inline-block;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  font-weight: 600;
}

@media (max-width: 767px) {
  /* line 58, ../../../../Desktop/role_permission/scss/_btn.scss */
  .blank_btn {
    padding: 12px 30px;
  }
}

/* line 70, ../../../../Desktop/role_permission/scss/_btn.scss */
.blank_btn:hover {
  color: #000;
  background: #fff;
}

/* line 76, ../../../../Desktop/role_permission/scss/_btn.scss */
.boxed_btn3 {
  background: #4543de;
  padding: 10px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  display: inline-block;
  font-size: 16px;
  color: #fff;
  font-weight: 600;
  padding: 12px 40px;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border: 1px solid transparent;
}

@media (max-width: 767px) {
  /* line 76, ../../../../Desktop/role_permission/scss/_btn.scss */
  .boxed_btn3 {
    padding: 12px 30px;
  }
}

/* line 90, ../../../../Desktop/role_permission/scss/_btn.scss */
.boxed_btn3:hover {
  color: #4543de;
  border-color: #4543de;
  background: transparent;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 3, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title.white_text h3 {
  color: #fff;
}

/* line 7, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title span {
  font-size: 16px;
  color: #8f8f8f;
  font-weight: 700;
  display: block;
  line-height: 16px;
  margin-bottom: 21px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 7, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title span {
    font-size: 14px;
    margin-top: 10px;
  }
}

@media (max-width: 767px) {
  /* line 7, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title span {
    font-size: 14px;
    margin-top: 10px;
  }
}

/* line 24, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title h3 {
  font-size: 42px;
  font-weight: 700;
  line-height: 54px;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 24, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title h3 {
    font-size: 38px;
    line-height: 50px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 24, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title h3 {
    font-size: 30px;
    line-height: 40px;
  }
}

@media (max-width: 767px) {
  /* line 37, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title h3 br {
    display: none;
  }
}

@media (max-width: 767px) {
  /* line 24, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title h3 {
    font-size: 26px;
    line-height: 36px;
  }
}

/* line 47, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title h4 {
  font-size: 34px;
  font-weight: 700;
  line-height: 1.35;
  color: #1c1c1c;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 47, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title h4 {
    font-size: 38px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 47, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title h4 {
    font-size: 25px;
  }
}

@media (max-width: 767px) {
  /* line 61, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title h4 br {
    display: none;
  }
}

@media (max-width: 767px) {
  /* line 47, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title h4 {
    font-size: 26px;
  }
}

/* line 72, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title.section_title2 h3 {
  margin-bottom: 18px;
  margin-bottom: 0;
  font-weight: 400;
}

/* line 77, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title.section_title2 p {
  line-height: 1.67;
  font-size: 18px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 8px;
  color: #505050;
}

@media (max-width: 767px) {
  /* line 84, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title.section_title2 p br {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 84, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title.section_title2 p br {
    display: none;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 84, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title.section_title2 p br {
    display: none;
  }
}

/* line 103, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title_2 h3 {
  font-size: 42px;
  font-weight: 400;
  line-height: 1.29;
}

/* line 107, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title_2 h3 span {
  color: #4543de;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 103, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_2 h3 {
    font-size: 38px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 103, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_2 h3 {
    font-size: 30px;
  }
}

@media (max-width: 767px) {
  /* line 117, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_2 h3 br {
    display: none;
  }
}

@media (max-width: 767px) {
  /* line 103, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_2 h3 {
    font-size: 26px;
    line-height: 36px;
  }
}

/* line 131, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title_3 h3 {
  font-size: 42px;
  font-weight: 400;
  line-height: 1.29;
}

/* line 135, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title_3 h3 span {
  color: #4543de;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 131, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_3 h3 {
    font-size: 38px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 131, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_3 h3 {
    font-size: 30px;
  }
}

@media (max-width: 767px) {
  /* line 145, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_3 h3 br {
    display: none;
  }
}

@media (max-width: 767px) {
  /* line 131, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_3 h3 {
    font-size: 26px;
    line-height: 36px;
  }
}

/* line 155, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title_3 p {
  font-size: 20px;
  line-height: 1.6;
  color: #505050;
  font-weight: 400;
  margin-top: 10px;
  margin-bottom: 24px;
}

@media (max-width: 767px) {
  /* line 162, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_3 p br {
    display: none;
  }
}

@media (max-width: 767px) {
  /* line 167, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_3 p br {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 167, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_3 p br {
    display: none;
  }
}

/* line 180, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title_4 h3 {
  font-size: 42px;
  font-weight: 600;
  line-height: 1.29;
  color: #0B0B21;
}

/* line 185, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title_4 h3 span {
  color: #4543de;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 180, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_4 h3 {
    font-size: 38px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 180, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_4 h3 {
    font-size: 30px;
  }
}

@media (max-width: 767px) {
  /* line 195, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_4 h3 br {
    display: none;
  }
}

@media (max-width: 767px) {
  /* line 180, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_4 h3 {
    font-size: 26px;
    line-height: 36px;
  }
}

/* line 205, ../../../../Desktop/role_permission/scss/_title.scss */
.section_title_4 p {
  font-size: 18px;
  line-height: 1.6;
  color: #505050;
  font-weight: 400;
  margin-top: 10px;
  margin-bottom: 24px;
}

@media (max-width: 767px) {
  /* line 212, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_4 p br {
    display: none;
  }
}

@media (max-width: 767px) {
  /* line 217, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_4 p br {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 217, ../../../../Desktop/role_permission/scss/_title.scss */
  .section_title_4 p br {
    display: none;
  }
}

/* line 228, ../../../../Desktop/role_permission/scss/_title.scss */
.pl-80 {
  padding-left: 80px;
}

@media (max-width: 767px) {
  /* line 228, ../../../../Desktop/role_permission/scss/_title.scss */
  .pl-80 {
    padding-left: 0px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 228, ../../../../Desktop/role_permission/scss/_title.scss */
  .pl-80 {
    padding-left: 30px;
  }
}

/* line 237, ../../../../Desktop/role_permission/scss/_title.scss */
.pl-100 {
  padding-left: 100px;
}

@media (max-width: 767px) {
  /* line 237, ../../../../Desktop/role_permission/scss/_title.scss */
  .pl-100 {
    padding-left: 20px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 237, ../../../../Desktop/role_permission/scss/_title.scss */
  .pl-100 {
    padding-left: 30px;
  }
}

/* line 247, ../../../../Desktop/role_permission/scss/_title.scss */
.mb-90 {
  margin-bottom: 82px;
}

@media (max-width: 767px) {
  /* line 247, ../../../../Desktop/role_permission/scss/_title.scss */
  .mb-90 {
    margin-bottom: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 247, ../../../../Desktop/role_permission/scss/_title.scss */
  .mb-90 {
    margin-bottom: 50px;
  }
}

/* line 257, ../../../../Desktop/role_permission/scss/_title.scss */
.pl-65 {
  padding-left: 65px;
}

@media (max-width: 767px) {
  /* line 257, ../../../../Desktop/role_permission/scss/_title.scss */
  .pl-65 {
    padding-left: 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 257, ../../../../Desktop/role_permission/scss/_title.scss */
  .pl-65 {
    padding-left: 0px;
  }
}

/* line 267, ../../../../Desktop/role_permission/scss/_title.scss */
.pb-180 {
  padding-bottom: 180px;
}

@media (max-width: 767px) {
  /* line 267, ../../../../Desktop/role_permission/scss/_title.scss */
  .pb-180 {
    padding-bottom: 80px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 267, ../../../../Desktop/role_permission/scss/_title.scss */
  .pb-180 {
    padding-bottom: 100px;
  }
}

/* line 277, ../../../../Desktop/role_permission/scss/_title.scss */
.mb-30 {
  margin-bottom: 30px;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
@media (max-width: 1200px) {
  /* line 3, ../../../../Desktop/role_permission/scss/_animation.scss */
  [class*="hero-ani-"] {
    display: none !important;
  }
}

/* line 8, ../../../../Desktop/role_permission/scss/_animation.scss */
.amination_custom {
  backface-visibility: hidden;
  animation: jumping 9s ease-in-out 2s infinite alternate;
  animation-delay: 1s;
  transition: all .9s ease 5s;
  user-select: none;
}

/* line 16, ../../../../Desktop/role_permission/scss/_animation.scss */
.amination_custom2 {
  backface-visibility: hidden;
  animation: jumping2 9s ease-in-out 2s infinite alternate;
  animation-delay: 1s;
  transition: all 1s ease 3s;
  user-select: none;
}

/* line 24, ../../../../Desktop/role_permission/scss/_animation.scss */
.amination_custom3 {
  backface-visibility: hidden;
  animation: jumping3 9s ease-in-out 2s infinite alternate;
  animation-delay: 1s;
  transition: all 7s ease 2s;
  user-select: none;
}

/* line 32, ../../../../Desktop/role_permission/scss/_animation.scss */
.amination_custom4 {
  backface-visibility: hidden;
  animation: jumping4 9s ease-in-out 2s infinite alternate;
  animation-delay: 1s;
  transition: all 8s ease 4s;
  user-select: none;
}

/* line 40, ../../../../Desktop/role_permission/scss/_animation.scss */
.amination_custom10 {
  backface-visibility: hidden;
  animation: jumping10 9s ease-in-out 2s infinite alternate;
  animation-delay: 1s;
  transition: all 8s ease 4s;
  user-select: none;
}

/* line 48, ../../../../Desktop/role_permission/scss/_animation.scss */
.amination_custom11 {
  backface-visibility: hidden;
  animation: jumping11 9s ease-in-out 2s infinite alternate;
  animation-delay: 1s;
  transition: all 8s ease 4s;
  user-select: none;
}

@keyframes jumping {
  0% {
    transform: translateY(0px) translateX(0) rotate(0) scale(1);
    opacity: .8;
  }
  25% {
    transform: translateY(-10px) translateX(-10px) rotate(20deg) scale(0.8);
    opacity: .9;
  }
  50% {
    transform: translateY(-15px) translateX(-15px) rotate(10deg) scale(0.9);
    opacity: .8;
  }
  75% {
    transform: translateY(-20px) translateX(-20px) rotate(20deg) scale(0.75);
    opacity: .6;
  }
  85% {
    transform: translateY(-25px) translateX(-25px) rotate(20deg) scale(0.9);
    opacity: .7;
  }
  100% {
    transform: translateY(-15px) translateX(-15px) rotate(0) scale(0.95);
    opacity: .85;
  }
}

@keyframes jumping2 {
  0% {
    transform: translateY(0px) translateX(0) rotate(0) scale(1);
    opacity: .5;
  }
  25% {
    transform: translateY(-30px) translateX(10px) rotate(20deg) scale(0.8);
    opacity: .8;
  }
  50% {
    transform: translateY(15px) translateX(-15px) rotate(10deg) scale(0.7);
    opacity: .8;
  }
  75% {
    transform: translateY(30px) translateX(20px) rotate(20deg) scale(0.75);
    opacity: .7;
  }
  100% {
    transform: translateY(-15px) translateX(15px) rotate(0) scale(0.75);
    opacity: .9;
  }
}

@keyframes jumping3 {
  0% {
    transform: translateY(10px) translateX(0) rotate(0) scale(1);
    opacity: .9;
  }
  20% {
    transform: translateY(20px) translateX(10px) rotate(-20deg) scale(0.8);
    opacity: .8;
  }
  40% {
    transform: translateY(15px) translateX(-15px) rotate(10deg) scale(0.75);
    opacity: .8;
  }
  40% {
    transform: translateY(-15px) translateX(-25px) rotate(10deg) scale(0.5);
    opacity: 1;
  }
  80% {
    transform: translateY(-30px) translateX(20px) rotate(-20deg) scale(0.75);
    opacity: .6;
  }
  100% {
    transform: translateY(15px) translateX(15px) rotate(0) scale(0.95);
    opacity: .7;
  }
}

@keyframes jumping4 {
  0% {
    transform: translateY(-30px) translateX(20px) rotate(0) scale(1.2);
    opacity: .7;
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(50deg) scale(0.6);
    opacity: .8;
  }
  50% {
    transform: translateY(15px) translateX(-15px) rotate(20deg) scale(0.5);
    opacity: .9;
  }
  75% {
    transform: translateY(30px) translateX(20px) rotate(50deg) scale(0.75);
    opacity: .7;
  }
  100% {
    transform: translateY(-15px) translateX(15px) rotate(0) scale(0.5);
    opacity: .9;
  }
}

@keyframes jumping10 {
  0% {
    transform: rotate(180deg);
    display: block;
  }
  100% {
    transform: rotate(30deg);
    display: block;
  }
}

@keyframes jumping11 {
  0% {
    transform: rotate(180deg) translate(-20px, 20px);
    display: block;
  }
  100% {
    transform: rotate(30deg) translate(0px, 0px);
    display: block;
  }
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area {
  padding: 0px 35px 0 35px;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 999;
}

/* line 8, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area.header_static {
  position: relative;
  padding: 0px 35px 0 35px;
}

@media (max-width: 767px) {
  /* line 8, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area.header_static {
    padding: 10px 0px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 8, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area.header_static {
    padding: 10px 0px;
  }
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area {
    padding: 10px 0px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area {
    padding: 10px 0px;
  }
}

/* line 25, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .header__wrapper {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  -webkit-justify-content: space-between;
  -moz-justify-content: space-between;
  -ms-justify-content: space-between;
  justify-content: space-between;
  -ms-flex-pack: space-between;
}

/* line 29, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .header__wrapper .header__left {
  -ms-flex-preferred-size: 30%;
  flex-basis: 30%;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 29, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .header__wrapper .header__left {
    -ms-flex-preferred-size: 20%;
    flex-basis: 20%;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 29, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .header__wrapper .header__left {
    -ms-flex-preferred-size: 30%;
    flex-basis: 30%;
  }
}

/* line 41, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .header__wrapper .header__right {
  -ms-flex-preferred-size: 30%;
  flex-basis: 30%;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 41, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .header__wrapper .header__right {
    -ms-flex-preferred-size: 20%;
    flex-basis: 20%;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 41, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .header__wrapper .header__right {
    -ms-flex-preferred-size: 30%;
    flex-basis: 30%;
  }
}

/* line 57, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .main_menu ul li {
  display: inline-block;
  position: relative;
}

/* line 60, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .main_menu ul li a {
  color: #505050;
  font-size: 16px;
  font-weight: 600;
  margin: 0 26px;
  padding: 40px 0;
  display: inline-block;
  text-transform: capitalize;
}

/* line 68, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .main_menu ul li a:hover {
  color: #ff4357;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 60, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .main_menu ul li a {
    font-size: 15px;
    margin: 0 20px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 60, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .main_menu ul li a {
    margin: 0 15px;
  }
}

/* line 80, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .main_menu ul li .submenu {
  width: 200px;
  position: absolute;
  left: 0;
  top: 120%;
  background: #fff;
  text-align: left;
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.04);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

/* line 91, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .main_menu ul li .submenu li {
  display: block;
  margin: 0;
}

/* line 95, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .main_menu ul li .submenu li a {
  font-size: 15px;
  line-height: 1.33;
  color: #505050;
  padding: 0;
  font-weight: 400;
  display: block;
  margin: 0;
  padding: 11px 20px;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  position: relative;
  text-transform: capitalize;
  left: 0;
}

/* line 108, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .main_menu ul li .submenu li a:hover {
  left: 5px;
  color: #ff4357;
}

/* line 116, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .main_menu ul li:hover > a {
  color: #ff4357;
}

/* line 119, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .main_menu ul li:hover .submenu {
  top: 100%;
  opacity: 1;
  visibility: visible;
}

/* line 128, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .contact_wrap {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

@media (max-width: 767px) {
  /* line 128, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .contact_wrap {
    display: none !important;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 128, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .contact_wrap {
    display: none !important;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 128, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .contact_wrap {
    display: none !important;
  }
}

/* line 142, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .contact_wrap .mobile_no p {
  color: #505050;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 0;
  font-family: "Nunito Sans", sans-serif;
}

/* line 149, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .contact_wrap .mobile_no p span a {
  color: #ff4357;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 142, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .contact_wrap .mobile_no p {
    font-size: 13px;
    margin-right: 8px;
  }
}

/* line 160, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .contact_wrap .signin a.sign_in {
  font-size: 16px;
  color: #505050;
  font-weight: 600;
  margin-right: 40px;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 160, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .contact_wrap .signin a.sign_in {
    margin-right: 15px;
  }
}

/* line 168, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .contact_wrap .signin a.sign_in:hover {
  color: #4543DE;
}

/* line 173, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area .contact_wrap .contact_btn {
  margin-left: 48px;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 173, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .contact_wrap .contact_btn {
    margin-left: 15px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 173, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .contact_wrap .contact_btn {
    margin-left: 0px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 181, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area .contact_wrap .contact_btn .boxed_btn {
    padding: 12px 30px;
  }
}

/* line 188, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area.navbar_fixed {
  background: #fff;
  z-index: 90;
  box-shadow: 0px 3px 16px 0px rgba(0, 0, 0, 0.1);
  position: fixed;
  width: 100%;
  top: -70px;
  left: 0;
  right: 0;
  transform: translateY(70px);
  transition: transform 500ms ease, background 500ms ease;
  -webkit-transition: transform 500ms ease, background 500ms ease;
}

/* line 207, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area.navbar_fixed .main_menu ul li a {
  padding: 30px 0;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 218, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area.header_area2 .main_menu ul li a {
    margin: 0 22px;
  }
}

/* line 222, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area.header_area2 .main_menu ul li a:hover {
  color: #4543de;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 226, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area.header_area2 .boxed_btn3 {
    font-size: 14px;
    padding: 12px 13px;
  }
}

/* line 237, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area.header_area2 .main_menu ul li:hover > a {
  color: #4543de;
}

/* line 245, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area.header_area2 .header__wrapper .header__left {
  -ms-flex-preferred-size: 15%;
  flex-basis: 15%;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 245, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area.header_area2 .header__wrapper .header__left {
    -ms-flex-preferred-size: 15%;
    flex-basis: 15%;
  }
}

/* line 253, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area.header_area2 .header__wrapper .main_menu {
  -ms-flex-preferred-size: 50%;
  flex-basis: 50%;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 253, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area.header_area2 .header__wrapper .main_menu {
    -ms-flex-preferred-size: 60%;
    flex-basis: 60%;
  }
}

/* line 261, ../../../../Desktop/role_permission/scss/_header.scss */
.header_area.header_area2 .header__wrapper .header__right {
  -ms-flex-preferred-size: 35%;
  flex-basis: 35%;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 261, ../../../../Desktop/role_permission/scss/_header.scss */
  .header_area.header_area2 .header__wrapper .header__right {
    -ms-flex-preferred-size: 25%;
    flex-basis: 25%;
  }
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_menu .slicknav_icon {
  margin-right: 6px;
  margin-top: 3px;
  padding-bottom: 3px;
  position: absolute;
  top: 0px;
}

/* line 9, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_nav a {
  font-size: 14px;
  color: #000;
  font-family: "Nunito Sans", sans-serif;
  text-transform: capitalize;
  font-weight: 400;
}

/* line 17, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_nav .slicknav_row, .slicknav_nav a {
  padding: 9.5px 15px;
  margin: 0;
}

/* line 22, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_arrow {
  position: absolute;
  right: 0;
  width: 40px;
  height: 37px;
  text-align: center;
  line-height: 40px;
  top: 0;
}

/* line 31, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_nav li {
  display: block;
  border-bottom: none;
  position: relative;
}

/* line 37, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.mobile_menu .slicknav_nav ul.submenu {
  list-style: none;
  overflow: hidden;
  padding: 0;
  margin: 0;
  background: #f7f7f7;
}

/* line 44, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.mobile_menu .slicknav_nav ul.submenu li a {
  font-size: 16px;
}

/* line 49, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.mobile_menu .slicknav_nav .slicknav_arrow {
  margin: 0;
}

/* line 54, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_nav .slicknav_row:hover {
  color: red;
}

/* line 57, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_nav a:hover {
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  border-radius: 0px;
  background: transparent;
  color: red;
}

/* line 65, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_menu .slicknav_icon-bar {
  background-color: #363e51;
  height: 2px;
  margin: 5px 0;
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  width: 25px;
  position: relative;
}

/* line 75, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_menu .slicknav_icon {
  margin-right: 0px;
}

/* line 78, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.mobile_menu {
  position: absolute;
  right: 0;
  width: 100%;
  z-index: 9999999;
}

/* line 84, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_btn {
  right: 24px;
}

/* line 87, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_nav {
  background: rgba(255, 255, 255, 0.96);
  box-shadow: 0 5px 7px 0 rgba(13, 0, 36, 0.07);
  padding: 15px 6px;
  margin-top: 10px;
}

/* line 94, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_nav .slicknav_row, .slicknav_nav a {
  padding: 6.5px 20px;
  margin: 0;
}

/* line 98, ../../../../Desktop/role_permission/scss/_mobile_menu.scss */
.slicknav_nav .slicknav_row, .slicknav_nav a {
  padding: 6.5px 20px;
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_menu_animation.scss */
.slicknav_btn {
  padding: 0px;
  border: 0px;
}

/* line 6, ../../../../Desktop/role_permission/scss/_menu_animation.scss */
.slicknav_btn .slicknav_icon-bar {
  background-color: #ff4357;
  -webkit-transition: all .5s linear;
  -o-transition: all .5s linear;
  transition: all .5s linear;
  display: block;
  width: 22px;
  height: 2px;
}

/* line 16, ../../../../Desktop/role_permission/scss/_menu_animation.scss */
.home_2 .slicknav_btn .slicknav_icon-bar {
  background-color: #4543de;
}

/* line 20, ../../../../Desktop/role_permission/scss/_menu_animation.scss */
.slicknav_btn .slicknav_icon-bar + .slicknav_icon-bar {
  margin-top: 5px;
}

/* line 24, ../../../../Desktop/role_permission/scss/_menu_animation.scss */
.slicknav_btn .slicknav_icon-bar:nth-child(2) {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

/* line 31, ../../../../Desktop/role_permission/scss/_menu_animation.scss */
.slicknav_btn.slicknav_open .slicknav_icon-bar:nth-child(2) {
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

/* line 37, ../../../../Desktop/role_permission/scss/_menu_animation.scss */
.slicknav_btn .slicknav_icon-bar:nth-child(3) {
  opacity: 1;
}

/* line 42, ../../../../Desktop/role_permission/scss/_menu_animation.scss */
.slicknav_btn.slicknav_open .slicknav_icon-bar:nth-child(3) {
  opacity: 0;
}

/* line 46, ../../../../Desktop/role_permission/scss/_menu_animation.scss */
.slicknav_btn .slicknav_icon-bar:nth-child(1) {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
  top: 0;
}

/* line 53, ../../../../Desktop/role_permission/scss/_menu_animation.scss */
.slicknav_btn.slicknav_open .slicknav_icon-bar:nth-child(1) {
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  top: 7px;
  position: relative;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_footer.scss */
footer {
  position: relative;
}

/* line 3, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .ilstrator_footer_img {
  position: absolute;
  right: 0;
  bottom: 0;
}

/* line 8, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .anim_icon_1 {
  position: absolute;
  top: 12%;
  right: 38%;
}

/* line 13, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .anim_icon_2 {
  position: absolute;
  bottom: 21%;
  left: 16%;
}

/* line 18, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .footer_top_area {
  padding-top: 150px;
  padding-bottom: 70px;
}

@media (max-width: 767px) {
  /* line 18, ../../../../Desktop/role_permission/scss/_footer.scss */
  footer .footer_top_area {
    padding-top: 50px;
    padding-bottom: 20px;
  }
}

/* line 25, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .footer_top_area .footer_widget {
  margin-bottom: 30px;
}

/* line 30, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .footer_top_area .footer_widget .social_links {
  margin-top: 100px;
}

@media (max-width: 767px) {
  /* line 30, ../../../../Desktop/role_permission/scss/_footer.scss */
  footer .footer_top_area .footer_widget .social_links {
    margin-top: 30px;
  }
}

/* line 36, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .footer_top_area .footer_widget .social_links ul li {
  display: inline-block;
  margin-right: 16px;
}

/* line 39, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .footer_top_area .footer_widget .social_links ul li a {
  width: 40px;
  height: 40px;
  color: #d2d2d2;
  border: 1px solid #d2d2d2;
  text-align: center;
  line-height: 40px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  display: inline-block;
  font-size: 16px;
}

/* line 49, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .footer_top_area .footer_widget .social_links ul li a:hover {
  color: #fff;
  background: #ff4357;
  border: 1px solid transparent;
}

/* line 59, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .footer_top_area .footer_widget .footer_title h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1c1c1c;
  margin-bottom: 35px;
}

/* line 68, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .footer_top_area .footer_widget .link_list li a {
  font-size: 16px;
  color: #919191;
  line-height: 42px;
}

/* line 72, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .footer_top_area .footer_widget .link_list li a:hover {
  color: #ff4357;
}

/* line 78, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .footer_top_area .footer_widget p {
  font-size: 16px;
  color: #919191;
  margin-bottom: 0;
  line-height: 42px;
}

/* line 83, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .footer_top_area .footer_widget p a {
  color: #919191;
}

/* line 85, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .footer_top_area .footer_widget p a:hover {
  color: #ff4357;
}

/* line 92, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .copyright_area {
  padding-bottom: 39px;
}

/* line 95, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .copyright_area .copy_right_text p {
  margin-bottom: 0;
  font-size: 16px;
  color: #505050;
  font-weight: 400;
}

/* line 102, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .copyright_area .copy_right_links {
  text-align: right;
}

@media (max-width: 767px) {
  /* line 102, ../../../../Desktop/role_permission/scss/_footer.scss */
  footer .copyright_area .copy_right_links {
    text-align: center;
  }
}

/* line 107, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .copyright_area .copy_right_links li {
  display: inline-block;
}

/* line 109, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .copyright_area .copy_right_links li a {
  font-size: 16px;
  color: #505050;
  margin-left: 57px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 109, ../../../../Desktop/role_permission/scss/_footer.scss */
  footer .copyright_area .copy_right_links li a {
    margin-left: 20px;
  }
}

@media (max-width: 767px) {
  /* line 109, ../../../../Desktop/role_permission/scss/_footer.scss */
  footer .copyright_area .copy_right_links li a {
    margin-left: 20px;
    margin: 0 10px;
    text-align: center;
  }
}

/* line 124, ../../../../Desktop/role_permission/scss/_footer.scss */
footer .copyright_area .footer_border {
  border-top: 2px solid #f4f4f4;
  margin-bottom: 40px;
}

/* line 130, ../../../../Desktop/role_permission/scss/_footer.scss */
.border_top_1px {
  border-top: 1px solid #dfdfdf;
}

/* line 135, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 {
  background: #1c1c37;
}

/* line 137, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .border_top_1px {
  border-top: 1px solid #000 !important;
}

/* line 140, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .footer_border {
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  margin-bottom: 40px;
}

/* line 146, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .footer_top_area .footer_widget .info_add {
  color: #CCCCCC;
  font-size: 16px;
  line-height: 26px;
  margin-top: 33px;
  margin-bottom: 36px;
}

/* line 153, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .footer_top_area .footer_widget .social_links {
  margin-top: 0;
}

/* line 157, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .footer_top_area .footer_widget .social_links ul li a {
  color: rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

/* line 160, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .footer_top_area .footer_widget .social_links ul li a:hover {
  background: #4543DE;
  border-color: #4543DE;
  color: #fff;
}

/* line 170, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .footer_top_area .footer_widget .footer_title h3 {
  font-size: 20px;
  font-weight: 600;
  color: #CCCCCC;
  margin-bottom: 35px;
}

/* line 179, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .footer_top_area .footer_widget .link_list li a {
  font-size: 16px;
  color: #CCCCCC;
  line-height: 42px;
}

/* line 183, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .footer_top_area .footer_widget .link_list li a:hover {
  color: #4543DE;
}

/* line 191, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .copyright_area {
  padding-bottom: 39px;
}

/* line 194, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .copyright_area .copy_right_text p {
  margin-bottom: 0;
  font-size: 16px;
  font-weight: 400;
  color: #CCCCCC;
}

/* line 199, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .copyright_area .copy_right_text p a {
  color: #4543DE;
}

/* line 204, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .copyright_area .copy_right_links {
  text-align: right;
}

@media (max-width: 767px) {
  /* line 204, ../../../../Desktop/role_permission/scss/_footer.scss */
  footer.footer_2 .copyright_area .copy_right_links {
    text-align: center;
  }
}

/* line 209, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .copyright_area .copy_right_links li {
  display: inline-block;
}

/* line 211, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .copyright_area .copy_right_links li a {
  font-size: 16px;
  color: #CCCCCC;
  margin-left: 57px;
}

/* line 215, ../../../../Desktop/role_permission/scss/_footer.scss */
footer.footer_2 .copyright_area .copy_right_links li a:hover {
  color: #4543DE;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 211, ../../../../Desktop/role_permission/scss/_footer.scss */
  footer.footer_2 .copyright_area .copy_right_links li a {
    margin-left: 20px;
  }
}

@media (max-width: 767px) {
  /* line 211, ../../../../Desktop/role_permission/scss/_footer.scss */
  footer.footer_2 .copyright_area .copy_right_links li a {
    margin-left: 20px;
    margin: 0 10px;
    text-align: center;
  }
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 2, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area {
  position: relative;
  height: 900px;
  height: 100vh;
  overflow-x: hidden;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  min-height: 650px;
}

@media (max-width: 767px) {
  /* line 2, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area {
    height: auto;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 2, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area {
    padding-top: 140px;
    height: auto;
  }
}

/* line 18, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .circle_icon_3 {
  position: absolute;
  bottom: 8%;
  left: 16%;
}

/* line 23, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .business_image {
  position: relative;
  left: 60px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 23, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image {
    margin-top: 100px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 23, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image {
    left: 0;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 23, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image {
    left: 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 23, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image {
    left: 0;
  }
}

@media (max-width: 767px) {
  /* line 23, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image {
    left: 0;
  }
}

@media (max-width: 767px) {
  /* line 23, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image {
    top: 0;
    left: 0;
    margin-top: 30px;
    margin-bottom: 30px;
  }
}

/* line 50, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .business_image .ilstration_fram img {
  width: 634px;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 50, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .ilstration_fram img {
    width: 460px;
  }
}

@media (min-width: 1501px) and (max-width: 1700px) {
  /* line 50, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .ilstration_fram img {
    width: 570px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 50, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .ilstration_fram img {
    width: 400px;
  }
}

@media (max-width: 767px) {
  /* line 50, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .ilstration_fram img {
    width: 100%;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 50, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .ilstration_fram img {
    width: 100%;
  }
}

/* line 69, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .business_image .man_img {
  position: absolute;
  bottom: 0;
  right: -44%;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 69, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .man_img {
    right: -14%;
  }
}

@media (min-width: 1501px) and (max-width: 1700px) {
  /* line 69, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .man_img {
    right: -14%;
  }
}

@media (min-width: 1501px) and (max-width: 1700px) {
  /* line 69, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .man_img {
    right: -14%;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 69, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .man_img {
    right: -14%;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 69, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .man_img {
    right: 4%;
  }
}

/* line 88, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .business_image .man_img img {
  width: 100%;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 88, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .man_img img {
    width: 78%;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 88, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .man_img img {
    width: 60%;
  }
}

@media (max-width: 767px) {
  /* line 88, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .man_img img {
    width: 40%;
  }
}

/* line 101, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .business_image .woman_img {
  position: absolute;
  bottom: 0;
  right: 7%;
}

@media (min-width: 1501px) and (max-width: 1700px) {
  /* line 101, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .woman_img {
    right: 35%;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 101, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .woman_img {
    right: 35%;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 101, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .woman_img {
    right: 32%;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 101, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .woman_img {
    right: 38%;
  }
}

/* line 117, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .business_image .woman_img img {
  width: 100%;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 117, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .woman_img img {
    width: 78%;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 117, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .woman_img img {
    width: 60%;
  }
}

@media (max-width: 767px) {
  /* line 117, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .woman_img img {
    width: 40%;
  }
}

/* line 130, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .business_image .circle_img {
  position: absolute;
  top: 0;
  right: 7%;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 130, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .circle_img {
    right: 30%;
  }
}

@media (min-width: 1501px) and (max-width: 1700px) {
  /* line 130, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .circle_img {
    right: 24%;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 130, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .circle_img {
    right: 25%;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 130, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .circle_img {
    right: 35%;
  }
}

/* line 146, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .business_image .circle_img img {
  width: 100%;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 146, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .circle_img img {
    width: 78%;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 146, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .circle_img img {
    width: 50%;
  }
}

@media (max-width: 767px) {
  /* line 146, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .circle_img img {
    width: 40%;
  }
}

/* line 159, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .business_image .line_img {
  position: absolute;
  top: 58%;
  left: 16%;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 159, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .line_img {
    left: -8%;
  }
}

@media (min-width: 1501px) and (max-width: 1700px) {
  /* line 159, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .line_img {
    left: -8%;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 159, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .line_img {
    left: -19%;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 159, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .line_img {
    left: 4%;
  }
}

@media (max-width: 767px) {
  /* line 159, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .line_img {
    left: 10%;
  }
}

/* line 178, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .business_image .line_img img {
  width: 100%;
}

@media (max-width: 767px) {
  /* line 178, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .business_image .line_img img {
    width: 40%;
  }
}

/* line 186, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .ilstrator_img {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 186, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .ilstrator_img {
    width: 400px;
  }
}

@media (max-width: 767px) {
  /* line 186, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .ilstrator_img {
    width: 200px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 186, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .ilstrator_img {
    width: 300px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 200, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .ilstrator_img img {
    width: 100%;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 200, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .ilstrator_img img {
    width: 100%;
  }
}

@media (max-width: 767px) {
  /* line 200, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .ilstrator_img img {
    width: 100%;
  }
}

/* line 212, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .ilstrator_img_2 {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: -1;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 212, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .ilstrator_img_2 {
    width: 650px;
  }
}

@media (max-width: 767px) {
  /* line 212, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .ilstrator_img_2 {
    width: 200px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 212, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .ilstrator_img_2 {
    width: 550px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 227, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .ilstrator_img_2 img {
    width: 100%;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 227, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .ilstrator_img_2 img {
    width: 100%;
  }
}

@media (max-width: 767px) {
  /* line 240, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .banner_text {
    margin-top: 120px;
  }
}

/* line 244, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .banner_text span {
  color: #ff4357;
  font-weight: 600;
  background-color: rgba(255, 67, 87, 0.059);
  padding: 8px 12px;
  display: inline-block;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  margin-bottom: 33px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 244, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .banner_text span {
    margin-bottom: 20px;
  }
}

@media (max-width: 767px) {
  /* line 244, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .banner_text span {
    margin-bottom: 20px;
  }
}

/* line 259, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .banner_text h3 {
  font-size: 60px;
  font-weight: 700;
  color: #1c1c1c;
  line-height: 70px;
  margin-bottom: 66px;
}

@media (max-width: 767px) {
  /* line 259, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .banner_text h3 {
    font-size: 30px;
    line-height: 40px;
    margin-bottom: 30px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 259, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .banner_text h3 {
    font-size: 40px;
    line-height: 50px;
    margin-bottom: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 259, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .banner_text h3 {
    font-size: 35px;
    line-height: 45px;
    margin-bottom: 20px;
  }
}

/* line 282, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .banner_text .site_link_form h4 {
  font-size: 20px;
  font-weight: #1c1c1c;
  font-weight: 600;
  margin-bottom: 23px;
}

/* line 289, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .banner_text .site_link_form form {
  display: inline-block;
}

/* line 291, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .banner_text .site_link_form form input {
  height: 60px;
  width: 215px;
  border: 1px solid #cccccc;
  padding-left: 25px;
  border-radius: 5px;
  margin-right: 15px;
  color: #000;
}

@media (max-width: 767px) {
  /* line 291, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .banner_text .site_link_form form input {
    height: 50px;
    margin-bottom: 20px;
    width: 100%;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 291, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .banner_text .site_link_form form input {
    width: 170px;
    height: 50px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 291, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .banner_text .site_link_form form input {
    width: 200px;
    height: 50px;
  }
}

/* line 312, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .banner_text .site_link_form form input::placeholder {
  color: #919191;
}

/* line 316, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .banner_text .site_link_form form button {
  background: #458efe;
  display: inline-block;
  padding: 18px 40px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid transparent;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 316, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .banner_text .site_link_form form button {
    padding: 13px 25px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 316, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .banner_text .site_link_form form button {
    padding: 13px 30px;
  }
}

@media (max-width: 767px) {
  /* line 316, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .banner_text .site_link_form form button {
    padding: 13px 40px;
    width: 100%;
  }
}

/* line 335, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .banner_text .site_link_form form button:hover {
  color: #458efe;
  border-color: #458efe;
  background: transparent;
}

/* line 347, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .circle_icon_3 {
  position: absolute;
  bottom: 8%;
  left: 16%;
}

/* line 352, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .circle_icon_2 {
  position: absolute;
  top: 15%;
  left: 53%;
}

@media (max-width: 767px) {
  /* line 352, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .circle_icon_2 {
    top: 10%;
    left: 30%;
  }
}

/* line 361, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .circle_icon_4 {
  position: absolute;
  right: 8.5%;
  top: 28%;
  z-index: 4;
}

@media (max-width: 767px) {
  /* line 361, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .circle_icon_4 {
    right: 8.5%;
    top: 28%;
  }
}

/* line 371, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .circle_icon_5 {
  position: absolute;
  right: 35%;
  bottom: 39%;
  z-index: 4;
}

@media (max-width: 767px) {
  /* line 371, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .circle_icon_5 {
    right: 35%;
    bottom: 39%;
  }
}

/* line 381, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area .circle_icon_1 {
  position: absolute;
  top: 28%;
  left: 3%;
}

@media (max-width: 767px) {
  /* line 381, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area .circle_icon_1 {
    top: 33%;
    left: 74%;
  }
}

/* line 393, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area_2 {
  padding-top: 150px;
  padding-bottom: 70px;
  position: relative;
}

@media (max-width: 767px) {
  /* line 393, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area_2 {
    padding-top: 100px;
    padding-bottom: 50px;
  }
}

/* line 402, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area_2 .banner_info h3 {
  font-size: 50px;
  color: #0b0b21;
  line-height: 1.24;
  font-weight: 600;
}

@media (max-width: 767px) {
  /* line 402, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area_2 .banner_info h3 {
    font-size: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 402, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area_2 .banner_info h3 {
    font-size: 35px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 402, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area_2 .banner_info h3 {
    font-size: 45px;
  }
}

/* line 417, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area_2 .banner_info p {
  line-height: 1.6;
  font-size: 20px;
  color: #505050;
  margin-bottom: 30px;
}

@media (max-width: 767px) {
  /* line 417, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area_2 .banner_info p {
    font-size: 16px;
  }
}

@media (max-width: 767px) {
  /* line 425, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area_2 .banner_info p br {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 425, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area_2 .banner_info p br {
    display: none;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 425, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area_2 .banner_info p br {
    display: none;
  }
}

/* line 437, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area_2 .banner_info .banner_thumb {
  margin-top: 70px;
  -webkit-box-shadow: 0 30px 30px rgba(80, 80, 80, 0.08);
  -moz-box-shadow: 0 30px 30px rgba(80, 80, 80, 0.08);
  box-shadow: 0 30px 30px rgba(80, 80, 80, 0.08);
}

@media (max-width: 767px) {
  /* line 437, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area_2 .banner_info .banner_thumb {
    margin-top: 40px;
  }
}

/* line 443, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area_2 .banner_info .banner_thumb img {
  width: 100%;
}

/* line 449, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area_2 .animation_icon .anim_icon_4, .banner_area_2 .animation_icon .anim_icon, .banner_area_2 .animation_icon .anim_icon_2, .banner_area_2 .animation_icon .anim_icon_3 {
  position: absolute;
}

/* line 452, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area_2 .animation_icon .anim_icon {
  left: 10%;
  top: 34%;
}

/* line 456, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area_2 .animation_icon .anim_icon_2 {
  left: 63%;
  top: 7%;
}

/* line 460, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area_2 .animation_icon .anim_icon_3 {
  right: 23%;
  top: 31%;
}

/* line 464, ../../../../Desktop/role_permission/scss/_banner.scss */
.banner_area_2 .animation_icon .anim_icon_4 {
  left: 2%;
  bottom: -13%;
}

@media (max-width: 767px) {
  /* line 464, ../../../../Desktop/role_permission/scss/_banner.scss */
  .banner_area_2 .animation_icon .anim_icon_4 {
    display: none;
  }
}

/* line 474, ../../../../Desktop/role_permission/scss/_banner.scss */
.layer {
  position: relative !important;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area {
  padding-top: 200px;
  padding-bottom: 100px;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_features.scss */
  .features_area {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

/* line 9, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area .features_title h3 {
  font-size: 22px;
  font-weight: 600;
  color: #1c1c1c;
  border-bottom: 1px solid #1c1c1c;
  padding-bottom: 0px;
  display: inline-block;
  margin-bottom: 85px;
}

@media (max-width: 767px) {
  /* line 9, ../../../../Desktop/role_permission/scss/_features.scss */
  .features_area .features_title h3 {
    margin-bottom: 30px;
    font-size: 15px;
  }
}

/* line 24, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area .brand_active .single_brand {
  text-align: center;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  -ms-justify-content: center;
  justify-content: center;
  -ms-flex-pack: center;
  height: 50px;
}

/* line 30, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area .brand_active .single_brand img {
  width: auto;
  display: inline-block;
}

/* line 35, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area .brand_active.owl-carousel .owl-item img {
  display: inline-block;
  width: auto;
}

@media (max-width: 767px) {
  /* line 35, ../../../../Desktop/role_permission/scss/_features.scss */
  .features_area .brand_active.owl-carousel .owl-item img {
    width: 100px;
  }
}

/* line 45, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area_2 {
  padding-top: 120px;
  padding-bottom: 140px;
  position: relative;
}

/* line 49, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area_2 .shape_icon {
  position: absolute;
  right: 10%;
  bottom: 10%;
}

@media (max-width: 767px) {
  /* line 49, ../../../../Desktop/role_permission/scss/_features.scss */
  .features_area_2 .shape_icon {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 56, ../../../../Desktop/role_permission/scss/_features.scss */
  .features_area_2 .shape_icon img {
    width: 230px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 56, ../../../../Desktop/role_permission/scss/_features.scss */
  .features_area_2 .shape_icon img {
    width: 230px;
  }
}

@media (max-width: 767px) {
  /* line 45, ../../../../Desktop/role_permission/scss/_features.scss */
  .features_area_2 {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 45, ../../../../Desktop/role_permission/scss/_features.scss */
  .features_area_2 {
    padding-top: 40px;
    padding-bottom: 60px;
  }
}

/* line 75, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area_2::before {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  content: '';
  top: -310px;
  z-index: -1;
  background-image: linear-gradient(to bottom, #ffffff, #fbf5ff);
}

/* line 86, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area_2 .single_feature {
  margin-bottom: 40px;
}

/* line 88, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area_2 .single_feature .icon {
  width: 98px;
  height: 98px;
  text-align: center;
  line-height: 98px;
  -webkit-border-radius: 25px;
  -moz-border-radius: 25px;
  border-radius: 25px;
  font-size: 37px;
  color: #fff;
  box-shadow: #505050;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  margin: auto;
}

/* line 100, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area_2 .single_feature .icon i {
  font-size: 37px;
  line-height: 98px;
}

/* line 105, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area_2 .single_feature h4 {
  font-size: 24px;
  font-weight: 600;
  color: #0b0b21;
  margin-bottom: 0;
  margin-top: 20px;
}

/* line 112, ../../../../Desktop/role_permission/scss/_features.scss */
.features_area_2 .single_feature:hover .icon {
  box-shadow: 0px 10px 10px rgba(80, 80, 80, 0.15);
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area {
  padding-top: 100px;
  padding-bottom: 100px;
  position: relative;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_about.scss */
  .about_area {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_about.scss */
  .about_area {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}

/* line 13, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .big_instrator_image {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 622px;
}

/* line 19, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .big_instrator_image img {
  width: 100%;
}

/* line 23, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .about_thumb {
  position: relative;
  clip-path: polygon(100% 0%, 100% 68%, 100% 100%, 1px 92.61%, 0px 0px);
}

/* line 26, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .about_thumb::before {
  position: absolute;
  content: "";
  left: 3px;
  border: 4px solid #9dc5fe;
  top: 53px;
  bottom: 30px;
  right: 30px;
  z-index: -1;
  -webkit-transform: skew(0, 5deg);
  -ms-transform: skew(0, 5deg);
  transform: skew(0, 5deg);
}

/* line 39, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .about_thumb::after {
  position: absolute;
  content: '';
}

/* line 44, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .about_thumb img {
  width: 100%;
  padding: 0 0px 30px 30px;
}

@media (max-width: 767px) {
  /* line 44, ../../../../Desktop/role_permission/scss/_about.scss */
  .about_area .about_thumb img {
    padding: 0;
  }
}

/* line 51, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .about_thumb a {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 85px;
  height: 85px;
  background: #ff4357;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  text-align: center;
  line-height: 85px;
}

/* line 62, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .about_thumb a i {
  position: relative;
  font-size: 20px;
  color: #fff;
  top: 2px;
  left: 2px;
}

/* line 71, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .about_info {
  padding-left: 40px;
}

/* line 72, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .about_info .section_title h3 {
  margin-bottom: 37px;
}

@media (max-width: 767px) {
  /* line 72, ../../../../Desktop/role_permission/scss/_about.scss */
  .about_area .about_info .section_title h3 {
    font-size: 25px;
    font-weight: 700;
    line-height: 35px;
    margin-top: 21px;
    margin-bottom: 37px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 71, ../../../../Desktop/role_permission/scss/_about.scss */
  .about_area .about_info {
    padding-left: 0;
  }
}

@media (max-width: 767px) {
  /* line 71, ../../../../Desktop/role_permission/scss/_about.scss */
  .about_area .about_info {
    padding-left: 0;
    margin-top: 30px;
  }
}

/* line 90, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .about_info p {
  font-size: 16px;
  color: #505050;
  line-height: 28px;
  font-weight: 400;
  margin-bottom: 34px;
}

/* line 98, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .about_info ul li {
  color: #505050;
  font-size: 16px;
  font-weight: 400;
  position: relative;
  padding-left: 35px;
  line-height: 26px;
  margin-bottom: 19px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 106, ../../../../Desktop/role_permission/scss/_about.scss */
  .about_area .about_info ul li br {
    display: none;
  }
}

@media (max-width: 767px) {
  /* line 106, ../../../../Desktop/role_permission/scss/_about.scss */
  .about_area .about_info ul li br {
    display: none;
  }
}

/* line 114, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area .about_info ul li::before {
  width: 15px;
  height: 15px;
  background: #458efe;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  position: absolute;
  content: "";
  left: 0;
  top: 6px;
}

/* line 131, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area_2 {
  padding: 180px 0;
  position: relative;
}

/* line 135, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area_2 .shapes .shap_1 {
  position: absolute;
  left: 40%;
  top: 24%;
}

/* line 139, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area_2 .shapes .shap_1 img {
  width: 100%;
}

@media (max-width: 767px) {
  /* line 131, ../../../../Desktop/role_permission/scss/_about.scss */
  .about_area_2 {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 131, ../../../../Desktop/role_permission/scss/_about.scss */
  .about_area_2 {
    padding-top: 100px;
    padding-bottom: 100px;
  }
}

@media (max-width: 767px) {
  /* line 152, ../../../../Desktop/role_permission/scss/_about.scss */
  .about_area_2 .about_thumb {
    margin-bottom: 30px;
  }
}

/* line 156, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area_2 .about_thumb img {
  width: 100%;
}

/* line 159, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area_2 .about_thumb.tab_margin {
  margin-bottom: 30px;
}

/* line 164, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area_2 .about_info .demo_links {
  font-size: 16px;
  color: #4543de;
  font-weight: 600;
  padding-bottom: 0;
  line-height: 16px;
}

/* line 170, ../../../../Desktop/role_permission/scss/_about.scss */
.about_area_2 .about_info .demo_links:hover {
  text-decoration: underline;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_service.scss */
.service_area {
  padding-top: 100px;
  padding-bottom: 70px;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_service.scss */
  .service_area {
    padding-top: 40px;
    padding-bottom: 20px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_service.scss */
  .service_area {
    padding-top: 75px;
  }
}

/* line 11, ../../../../Desktop/role_permission/scss/_service.scss */
.service_area .single_service {
  border: 1px solid transparent;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  padding: 35px 45px;
  margin-bottom: 30px;
  -webkit-box-shadow: 0px 10px 23px 0px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0px 10px 23px 0px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 10px 23px 0px rgba(0, 0, 0, 0.05);
}

/* line 16, ../../../../Desktop/role_permission/scss/_service.scss */
.service_area .single_service .service_icon {
  background: #ffecee;
  width: 100px;
  height: 100px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  text-align: center;
  line-height: 94px;
  margin: auto;
}

/* line 29, ../../../../Desktop/role_permission/scss/_service.scss */
.service_area .single_service.green .service_icon {
  background: #e5fdf7;
}

/* line 34, ../../../../Desktop/role_permission/scss/_service.scss */
.service_area .single_service.violate .service_icon {
  background: #f4eefe;
}

/* line 39, ../../../../Desktop/role_permission/scss/_service.scss */
.service_area .single_service.orange .service_icon {
  background: #fff4e6;
}

/* line 43, ../../../../Desktop/role_permission/scss/_service.scss */
.service_area .single_service h3 {
  font-size: 22px;
  font-weight: 700;
  color: #1c1c1c;
  margin-top: 46px;
  margin-bottom: 17px;
}

/* line 50, ../../../../Desktop/role_permission/scss/_service.scss */
.service_area .single_service p {
  font-size: 16px;
  line-height: 28px;
  color: #505050;
  margin-bottom: 0;
}

/* line 57, ../../../../Desktop/role_permission/scss/_service.scss */
.service_area .single_service:hover {
  border: 1px solid #ff4357;
}

/* line 60, ../../../../Desktop/role_permission/scss/_service.scss */
.service_area .single_service.green:hover {
  border: 1px solid #00ebac;
}

/* line 63, ../../../../Desktop/role_permission/scss/_service.scss */
.service_area .single_service.violate:hover {
  border: 1px solid #9657f6;
}

/* line 66, ../../../../Desktop/role_permission/scss/_service.scss */
.service_area .single_service.orange:hover {
  border: 1px solid #ff930e;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area {
  padding-top: 100px;
  padding-bottom: 70px;
  padding-left: 50px !important;
  padding-right: 50px !important;
  position: relative;
}

/* line 7, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .case_study_bg {
  position: absolute;
  top: 0;
  right: 0;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area {
    padding-top: 50px;
    padding-bottom: 90px;
  }
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

/* line 24, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .single-case {
  position: relative;
  padding-bottom: 155px;
  overflow: hidden;
  margin-bottom: 30px;
  -webkit-box-shadow: 0px 10px 23px 0px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0px 10px 23px 0px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 10px 23px 0px rgba(0, 0, 0, 0.05);
}

/* line 30, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .single-case .case_thumb {
  overflow: hidden;
}

/* line 32, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .single-case .case_thumb img {
  width: 100%;
}

/* line 36, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .single-case .hover_overlay {
  background: #fff;
  position: absolute;
  right: 0;
  left: 0;
  top: 0;
  height: 100%;
  padding: 35px;
  -webkit-transform: translateY(-155px);
  -moz-transform: translateY(-155px);
  -ms-transform: translateY(-155px);
  transform: translateY(-155px);
  -webkit-transition: 0.5s;
  -moz-transition: 0.5s;
  -ms-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
  top: 100%;
}

/* line 47, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .single-case .hover_overlay .case_heading {
  margin-top: 18px;
  padding-bottom: 20px;
}

/* line 53, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .single-case .hover_overlay .case_heading h3 {
  color: #1c1c1c;
  font-weight: 700;
  font-size: 24px;
  line-height: 34px;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 53, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .single-case .hover_overlay .case_heading h3 {
    font-size: 20px;
    line-height: 24px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 53, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .single-case .hover_overlay .case_heading h3 {
    font-size: 20px;
    line-height: 24px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 53, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .single-case .hover_overlay .case_heading h3 {
    font-size: 20px;
  }
}

@media (max-width: 767px) {
  /* line 53, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .single-case .hover_overlay .case_heading h3 {
    font-size: 17px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 72, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .single-case .hover_overlay .case_heading h3 br {
    display: none;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 72, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .single-case .hover_overlay .case_heading h3 br {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 72, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .single-case .hover_overlay .case_heading h3 br {
    display: none;
  }
}

@media (max-width: 767px) {
  /* line 72, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .single-case .hover_overlay .case_heading h3 br {
    display: none;
  }
}

/* line 88, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .single-case .hover_overlay .list {
  margin-top: 20px;
  margin-bottom: 60px;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 88, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .single-case .hover_overlay .list {
    margin-bottom: 30px;
  }
}

/* line 96, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .single-case .hover_overlay .list ul li {
  font-size: 16px;
  color: #fff;
  line-height: 34px;
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 96, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .single-case .hover_overlay .list ul li {
    font-size: 14px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 96, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .single-case .hover_overlay .list ul li {
    font-size: 14px;
  }
}

@media (max-width: 767px) {
  /* line 96, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .single-case .hover_overlay .list ul li {
    font-size: 14px;
  }
}

/* line 114, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .single-case:hover .hover_overlay {
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transform: translateY(0px);
  top: 0;
  background: #ff4357;
}

/* line 119, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .single-case:hover .case_heading {
  margin-top: 0px;
  padding-bottom: 0px;
}

/* line 122, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .single-case:hover .case_heading h3 {
  color: #fff;
}

/* line 129, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .owl-carousel .owl-nav div {
  background: #f2fcff;
  right: 540px;
  top: -145px;
  left: auto;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 129, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .owl-carousel .owl-nav div {
    right: 80px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 129, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .owl-carousel .owl-nav div {
    right: 80px;
  }
}

/* line 143, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .owl-carousel .owl-nav div.owl-next {
  right: 462px;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 143, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .owl-carousel .owl-nav div.owl-next {
    right: 0;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 143, ../../../../Desktop/role_permission/scss/_case.scss */
  .case_study_area .owl-carousel .owl-nav div.owl-next {
    right: 0;
  }
}

/* line 163, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .owl-carousel:hover .owl-nav div {
  opacity: 1;
  visibility: visible;
}

/* line 166, ../../../../Desktop/role_permission/scss/_case.scss */
.case_study_area .owl-carousel:hover .owl-nav div:hover {
  color: #458efe;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area {
  padding-top: 100px;
  padding-bottom: 100px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area {
    padding-top: 30px;
    padding-bottom: 44px;
  }
}

/* line 12, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_img {
  position: relative;
  right: 60px;
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 12, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area .chose_img {
    right: 0;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 12, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area .chose_img {
    right: 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 12, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area .chose_img {
    right: 0;
  }
}

@media (max-width: 767px) {
  /* line 12, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area .chose_img {
    right: 0;
  }
}

/* line 27, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_img img {
  width: 100%;
}

/* line 30, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_img .animated_img_1 {
  position: absolute;
  left: 0%;
  top: 12%;
}

/* line 35, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_img .animated_img_2 {
  position: absolute;
  right: 3%;
  bottom: 7%;
}

/* line 41, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_img .animated_img_3 {
  position: absolute;
  bottom: 18%;
  left: 0%;
}

/* line 47, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_img .animated_small_img img {
  width: auto;
}

@media (max-width: 767px) {
  /* line 52, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area .chose_info {
    margin-top: 30px;
  }
}

/* line 56, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_info .section_title {
  margin-bottom: 59px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 56, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area .chose_info .section_title {
    margin-bottom: 30px;
  }
}

@media (max-width: 767px) {
  /* line 56, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area .chose_info .section_title {
    margin-bottom: 20px;
  }
}

/* line 67, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_info .chose_list ul li {
  margin-bottom: 43px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 67, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area .chose_info .chose_list ul li {
    margin-bottom: 20px;
  }
}

@media (max-width: 767px) {
  /* line 67, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area .chose_info .chose_list ul li {
    margin-bottom: 20px;
  }
}

/* line 76, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_info .chose_list ul li .chose_mark span {
  width: 50px;
  height: 50px;
  background: #ff4357;
  border-radius: 50%;
  display: inline-block;
  text-align: center;
  line-height: 50px;
  font-weight: 700;
  color: #fff;
  font-weight: 16px;
}

/* line 87, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_info .chose_list ul li .chose_mark span.green {
  background: #00ebac !important;
}

/* line 90, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_info .chose_list ul li .chose_mark span.violate {
  background: #9657f6 !important;
}

/* line 95, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_info .chose_list ul li .chose_text {
  padding-left: 30px;
}

/* line 97, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_info .chose_list ul li .chose_text h3 {
  font-size: 22px;
  font-weight: 700;
  color: #1c1c1c;
  margin-bottom: 16px;
}

@media (max-width: 767px) {
  /* line 97, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area .chose_info .chose_list ul li .chose_text h3 {
    font-size: 18px;
    margin-bottom: 5px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 97, ../../../../Desktop/role_permission/scss/_chose.scss */
  .chose_area .chose_info .chose_list ul li .chose_text h3 {
    font-size: 18px;
    margin-bottom: 5px;
  }
}

/* line 112, ../../../../Desktop/role_permission/scss/_chose.scss */
.chose_area .chose_info .chose_list ul li .chose_text p {
  font-size: 16px;
  color: #505050;
  margin-bottom: 0;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_testimonial.scss */
.testmonial_area {
  padding-top: 160px;
  padding-bottom: 255px;
  background-image: url(../img/animated_icon/map.png);
  background-size: contain;
  background-position: center center;
  background-repeat: no-repeat;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_testimonial.scss */
  .testmonial_area {
    padding-top: 100px;
    padding-bottom: 170px;
  }
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_testimonial.scss */
  .testmonial_area {
    padding-top: 0px;
    padding-bottom: 96px;
  }
}

@media (max-width: 767px) {
  /* line 16, ../../../../Desktop/role_permission/scss/_testimonial.scss */
  .testmonial_area .testimonial_left {
    margin-bottom: 30px;
  }
}

/* line 24, ../../../../Desktop/role_permission/scss/_testimonial.scss */
.testmonial_area .testmonial_review .author {
  margin-bottom: 40px;
}

@media (max-width: 767px) {
  /* line 24, ../../../../Desktop/role_permission/scss/_testimonial.scss */
  .testmonial_area .testmonial_review .author {
    margin-bottom: 20px;
  }
}

/* line 32, ../../../../Desktop/role_permission/scss/_testimonial.scss */
.testmonial_area .testmonial_review .author .author_name {
  padding-left: 20px;
}

/* line 34, ../../../../Desktop/role_permission/scss/_testimonial.scss */
.testmonial_area .testmonial_review .author .author_name h4 {
  font-size: 22px;
  font-weight: 700;
  color: #1c1c1c;
  margin-bottom: 2px;
}

/* line 40, ../../../../Desktop/role_permission/scss/_testimonial.scss */
.testmonial_area .testmonial_review .author .author_name span {
  color: #919191;
  font-size: 16px;
  font-weight: 400;
}

/* line 47, ../../../../Desktop/role_permission/scss/_testimonial.scss */
.testmonial_area .testmonial_review p {
  font-size: 22px;
  line-height: 34px;
  font-weight: 400;
  color: #1c1c1c;
  margin-bottom: 0;
}

@media (max-width: 767px) {
  /* line 47, ../../../../Desktop/role_permission/scss/_testimonial.scss */
  .testmonial_area .testmonial_review p {
    font-size: 18px;
    line-height: 28px;
  }
}

/* line 61, ../../../../Desktop/role_permission/scss/_testimonial.scss */
.testmonial_area .testmonial_active.owl-carousel .owl-nav div {
  background: #458efe;
  height: 40px;
  left: 0px;
  line-height: 40px;
  position: absolute;
  text-align: center;
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  width: 40px;
  font-size: 8px;
  color: #81b3ff;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  bottom: -110px;
  left: 0;
  top: auto;
  font-size: 14px;
  font-weight: 700;
}

@media (max-width: 767px) {
  /* line 61, ../../../../Desktop/role_permission/scss/_testimonial.scss */
  .testmonial_area .testmonial_active.owl-carousel .owl-nav div {
    bottom: -65px;
  }
}

/* line 89, ../../../../Desktop/role_permission/scss/_testimonial.scss */
.testmonial_area .testmonial_active.owl-carousel .owl-nav div.owl-next {
  left: 53px;
}

/* line 92, ../../../../Desktop/role_permission/scss/_testimonial.scss */
.testmonial_area .testmonial_active.owl-carousel .owl-nav div.owl-next i {
  position: relative;
  left: 1px;
}

/* line 102, ../../../../Desktop/role_permission/scss/_testimonial.scss */
.testmonial_area .testmonial_active.owl-carousel .owl-nav div.owl-prev i {
  position: relative;
  right: 2px;
}

/* line 111, ../../../../Desktop/role_permission/scss/_testimonial.scss */
.testmonial_area .testmonial_active.owl-carousel:hover .owl-nav div {
  opacity: 1;
  visibility: visible;
}

/* line 114, ../../../../Desktop/role_permission/scss/_testimonial.scss */
.testmonial_area .testmonial_active.owl-carousel:hover .owl-nav div:hover {
  color: #fff !important;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area {
  padding-top: 100px;
  padding-bottom: 70px;
  position: relative;
}

/* line 5, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area.plus_padding {
  padding-bottom: 150px;
  padding-top: 80px;
}

@media (max-width: 767px) {
  /* line 5, ../../../../Desktop/role_permission/scss/_counter.scss */
  .counter_area.plus_padding {
    padding-top: 0px;
    padding-bottom: 20px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 5, ../../../../Desktop/role_permission/scss/_counter.scss */
  .counter_area.plus_padding {
    padding-top: 0px;
    padding-bottom: 70px;
  }
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_counter.scss */
  .counter_area {
    padding-top: 50px;
    padding-bottom: 20px;
  }
}

/* line 21, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area .counter_icon {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin: auto;
  text-align: center;
  line-height: 96px;
  background: #ffecee;
}

/* line 29, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area .counter_icon:nth-child(2) {
  background: #000;
}

/* line 33, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area .col-xl-4:nth-child(2) .single_counter .counter_icon {
  background: #e5fdf7;
}

/* line 36, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area .col-xl-4:nth-child(3) .single_counter .counter_icon {
  background: #f4eefe;
}

/* line 39, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area .couner_ilstrator_bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 660px;
  bottom: 0;
}

/* line 45, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area .couner_ilstrator_bg img {
  width: 100%;
}

/* line 49, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area .single_counter {
  padding: 60px 0;
  text-align: center;
  border: 1px solid transparent;
  -webkit-box-shadow: 0px 10px 22px 0px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0px 10px 22px 0px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 10px 22px 0px rgba(0, 0, 0, 0.05);
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  margin-bottom: 30px;
}

/* line 61, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area .single_counter h3 {
  font-size: 42px;
  font-weight: 700;
  color: #1c1c1c;
  margin-top: 26px;
  margin-bottom: 18px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 61, ../../../../Desktop/role_permission/scss/_counter.scss */
  .counter_area .single_counter h3 {
    font-size: 30px;
    margin-bottom: 20px;
  }
}

@media (max-width: 767px) {
  /* line 61, ../../../../Desktop/role_permission/scss/_counter.scss */
  .counter_area .single_counter h3 {
    font-size: 30px;
    margin-bottom: 20px;
  }
}

/* line 81, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area .single_counter span.conter_subtitle {
  color: #505050;
  font-size: 16px;
  font-weight: 400;
}

/* line 86, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area .single_counter:hover {
  border: 1px solid #ff4357;
}

/* line 90, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area .col-xl-4:nth-child(2) .single_counter:hover {
  border: 1px solid #00ebac !important;
}

/* line 93, ../../../../Desktop/role_permission/scss/_counter.scss */
.counter_area .col-xl-4:nth-child(3) .single_counter:hover {
  border: 1px solid #9657f6 !important;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_news.scss */
.news_area {
  padding-top: 100px;
  padding-bottom: 70px;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_news.scss */
  .news_area {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_news.scss */
  .news_area {
    padding-bottom: 60px;
    padding-top: 50px;
  }
}

/* line 12, ../../../../Desktop/role_permission/scss/_news.scss */
.news_area .single_news {
  margin-bottom: 30px;
  -webkit-box-shadow: 0px 10px 23px 0px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0px 10px 23px 0px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 10px 23px 0px rgba(0, 0, 0, 0.05);
}

/* line 15, ../../../../Desktop/role_permission/scss/_news.scss */
.news_area .single_news .news_thumb {
  overflow: hidden;
}

/* line 17, ../../../../Desktop/role_permission/scss/_news.scss */
.news_area .single_news .news_thumb img {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  width: 100%;
  -webkit-transition: 0.5s;
  -moz-transition: 0.5s;
  -ms-transition: 0.5s;
  -o-transition: 0.5s;
  transition: 0.5s;
}

/* line 23, ../../../../Desktop/role_permission/scss/_news.scss */
.news_area .single_news .news_content {
  padding: 30px 25px 34px 25px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 23, ../../../../Desktop/role_permission/scss/_news.scss */
  .news_area .single_news .news_content {
    padding: 20px 15px;
  }
}

/* line 28, ../../../../Desktop/role_permission/scss/_news.scss */
.news_area .single_news .news_content h3 {
  margin-bottom: 24px;
  font-size: 20px;
  font-weight: 700;
  line-height: 30px;
}

@media (max-width: 767px) {
  /* line 28, ../../../../Desktop/role_permission/scss/_news.scss */
  .news_area .single_news .news_content h3 {
    font-size: 18px;
    line-height: 28px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 37, ../../../../Desktop/role_permission/scss/_news.scss */
  .news_area .single_news .news_content h3 br {
    display: none;
  }
}

@media (max-width: 767px) {
  /* line 37, ../../../../Desktop/role_permission/scss/_news.scss */
  .news_area .single_news .news_content h3 br {
    display: none;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 37, ../../../../Desktop/role_permission/scss/_news.scss */
  .news_area .single_news .news_content h3 br {
    display: none;
  }
}

/* line 48, ../../../../Desktop/role_permission/scss/_news.scss */
.news_area .single_news .news_content h3 a {
  color: #1c1c1c;
}

/* line 52, ../../../../Desktop/role_permission/scss/_news.scss */
.news_area .single_news .news_content p {
  color: #8f8f8f;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  margin-bottom: 0;
}

/* line 63, ../../../../Desktop/role_permission/scss/_news.scss */
.news_area .single_news:hover .news_content h3 a {
  color: #ff4357;
}

/* line 68, ../../../../Desktop/role_permission/scss/_news.scss */
.news_area .single_news:hover img {
  -webkit-transform: scale(1.02);
  -moz-transform: scale(1.02);
  -ms-transform: scale(1.02);
  transform: scale(1.02);
  width: 100%;
}

/* line 75, ../../../../Desktop/role_permission/scss/_news.scss */
.bg {
  background-color: white;
  position: absolute;
  left: 405px;
  top: 6683px;
  width: 350px;
  height: 171px;
  z-index: 140;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_area {
  padding-top: 100px;
  padding-bottom: 222px;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_area {
    padding-top: 40px;
    padding-bottom: 75px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_area {
    padding-bottom: 160px;
    padding-top: 57px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_area {
    padding-bottom: 200px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_area {
    padding-bottom: 200px;
  }
}

/* line 28, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_area .contact_form_info form input {
  width: 100%;
  height: 60px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  border: none;
  border: 1px solid #cccccc;
  padding-left: 25px;
  margin-bottom: 30px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 28, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_area .contact_form_info form input {
    height: 40px;
    margin-bottom: 20px;
  }
}

@media (max-width: 767px) {
  /* line 28, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_area .contact_form_info form input {
    height: 40px;
    margin-bottom: 15px;
  }
}

/* line 44, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_area .contact_form_info form input::placeholder {
  color: #919191;
  font-size: 16px;
  font-weight: 400;
}

/* line 51, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_area .contact_form_info form textarea {
  width: 100%;
  height: 100px;
  margin-bottom: 60px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  border: none;
  border: 1px solid #cccccc;
  padding: 15px 0 0 25px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 51, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_area .contact_form_info form textarea {
    margin-bottom: 30px;
  }
}

@media (max-width: 767px) {
  /* line 51, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_area .contact_form_info form textarea {
    margin-bottom: 30px;
  }
}

/* line 65, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_area .contact_form_info form textarea::placeholder {
  color: #919191;
  font-size: 16px;
  font-weight: 400;
}

/* line 72, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_area .contact_form_info form button {
  border: none;
  cursor: pointer;
}

/* line 75, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_area .contact_form_info form button.boxed_btn {
  background: #ff4357;
  padding: 10px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  display: inline-block;
  font-size: 16px;
  color: #fff;
  font-weight: 600;
  padding: 13px 65px;
  border: 1px solid transparent;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

/* line 88, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_area .contact_form_info form button.boxed_btn:hover {
  border: 1px solid #ff4357;
  color: #ff4357 !important;
  background: transparent;
}

/* line 97, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_area .contact_thumb {
  position: relative;
}

@media (max-width: 767px) {
  /* line 97, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_area .contact_thumb {
    margin-top: 30px;
  }
}

/* line 113, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_area .contact_thumb img {
  width: 100%;
}

/* line 116, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_area .contact_thumb .thumb_inner {
  position: relative;
  -webkit-clip-path: polygon(100% 0, 100% 90%, 0 99%, 0 0);
  clip-path: polygon(100% 0, 100% 90%, 0 99%, 0 0);
}

/* line 121, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_area .contact_thumb::before {
  position: absolute;
  content: "";
  border: 4px solid #a2c6fe;
  z-index: 999;
  left: 30px;
  bottom: 10px;
  right: -25px;
  top: 60px;
  z-index: -1;
  -webkit-transform: skewY(-7deg);
  -moz-transform: skewY(-7deg);
  -ms-transform: skewY(-7deg);
  transform: skewY(-7deg);
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 121, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_area .contact_thumb::before {
    left: 15px;
    bottom: 15px;
    right: -15px;
    top: 40px;
  }
}

@media (max-width: 767px) {
  /* line 121, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_area .contact_thumb::before {
    display: none;
  }
}

/* line 146, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_expert {
  padding: 190px 0;
  position: relative;
  background: #458efe;
}

@media (max-width: 767px) {
  /* line 146, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_expert {
    padding: 80px 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 146, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_expert {
    padding: 100px 0;
  }
}

/* line 157, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_expert .contact_expert_text h3 {
  font-size: 42px;
  font-weight: 700;
  color: #ffffff;
}

@media (max-width: 767px) {
  /* line 157, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_expert .contact_expert_text h3 {
    font-size: 28px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 157, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_expert .contact_expert_text h3 {
    font-size: 35px;
  }
}

/* line 168, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_expert .contact_expert_text p {
  font-size: 22px;
  font-weight: 400;
  color: #fff;
  margin-top: 30px;
  margin-bottom: 60px;
}

@media (max-width: 767px) {
  /* line 168, ../../../../Desktop/role_permission/scss/_contact_form.scss */
  .contact_expert .contact_expert_text p {
    font-size: 15px;
    margin-top: 15px;
    margin-bottom: 20px;
  }
}

/* line 179, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_expert .contact_expert_text p a {
  text-decoration: underline;
}

/* line 181, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_expert .contact_expert_text p a:hover {
  color: #ff4357;
}

/* line 191, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_expert .animation_img .anim_1 {
  position: absolute;
  bottom: 23%;
  left: 4%;
}

/* line 196, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_expert .animation_img .anim_2 {
  position: absolute;
  top: 20%;
  left: 17%;
}

/* line 201, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_expert .animation_img .anim_3 {
  position: absolute;
  top: 17%;
  right: 8%;
}

/* line 206, ../../../../Desktop/role_permission/scss/_contact_form.scss */
.contact_expert .animation_img .anim_4 {
  position: absolute;
  right: 32%;
  bottom: 31%;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_bradcam.scss */
.bradcam_bg_1 {
  background-image: url(../img/bradcam/bradcam.png);
}

/* line 4, ../../../../Desktop/role_permission/scss/_bradcam.scss */
.bradcam_bg_2 {
  background-image: url(../img/bradcam/bradcam2.png);
}

/* line 7, ../../../../Desktop/role_permission/scss/_bradcam.scss */
.bradcam_bg_3 {
  background-image: url(../img/bradcam/bradcam3.png);
}

/* line 10, ../../../../Desktop/role_permission/scss/_bradcam.scss */
.bradcam_bg_5 {
  background-image: url(../img/bradcam/bradcam5.png);
}

/* line 13, ../../../../Desktop/role_permission/scss/_bradcam.scss */
.bradcam_bg_6 {
  background-image: url(../img/bradcam/bradcam6.png);
}

/* line 16, ../../../../Desktop/role_permission/scss/_bradcam.scss */
.bradcam_area {
  padding: 115px 0;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  position: relative;
  z-index: 2;
}

/* line 23, ../../../../Desktop/role_permission/scss/_bradcam.scss */
.bradcam_area::before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: #000;
  opacity: .2;
  z-index: -1;
}

/* line 35, ../../../../Desktop/role_permission/scss/_bradcam.scss */
.bradcam_area .bradcam_text h3 {
  font-size: 60px;
  font-weight: 700;
  color: #fff;
}

@media (max-width: 767px) {
  /* line 35, ../../../../Desktop/role_permission/scss/_bradcam.scss */
  .bradcam_area .bradcam_text h3 {
    font-size: 35px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 35, ../../../../Desktop/role_permission/scss/_bradcam.scss */
  .bradcam_area .bradcam_text h3 {
    font-size: 35px;
  }
}

/* line 46, ../../../../Desktop/role_permission/scss/_bradcam.scss */
.bradcam_area .bradcam_text p {
  color: #fff;
  font-size: 16px;
  font-weight: 400;
}

/* line 50, ../../../../Desktop/role_permission/scss/_bradcam.scss */
.bradcam_area .bradcam_text p a {
  color: #fff;
}

/* line 52, ../../../../Desktop/role_permission/scss/_bradcam.scss */
.bradcam_area .bradcam_text p a:hover {
  color: #ff4357;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 2, ../../../../Desktop/role_permission/scss/_about_page.scss */
.youtube_bg {
  background-image: url(../img/about_page/video/video_bg.png);
}

/* line 5, ../../../../Desktop/role_permission/scss/_about_page.scss */
.youtube_video_area {
  background-size: cover;
  background-position: center center;
  padding: 270px 0;
  text-align: center;
  background-repeat: no-repeat;
}

@media (max-width: 767px) {
  /* line 5, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .youtube_video_area {
    padding: 100px 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 5, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .youtube_video_area {
    padding: 100px 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 5, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .youtube_video_area {
    padding: 150px 0;
  }
}

/* line 20, ../../../../Desktop/role_permission/scss/_about_page.scss */
.youtube_video_area a {
  width: 110px;
  height: 110px;
  background: #ff4357;
  display: inline-block;
  text-align: center;
  line-height: 110px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  font-size: 16px;
  color: #fff;
}

@media (max-width: 767px) {
  /* line 20, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .youtube_video_area a {
    height: 70px;
    width: 70px;
    line-height: 70px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 20, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .youtube_video_area a {
    height: 70px;
    width: 70px;
    line-height: 70px;
  }
}

/* line 40, ../../../../Desktop/role_permission/scss/_about_page.scss */
.youtube_video_area a:hover {
  color: #fff;
}

/* line 46, ../../../../Desktop/role_permission/scss/_about_page.scss */
.about_info_area {
  padding-top: 180px;
  padding-bottom: 90px;
}

@media (max-width: 767px) {
  /* line 46, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .about_info_area {
    padding-top: 50px;
    padding-bottom: 25px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 46, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .about_info_area {
    padding-top: 95px;
    padding-bottom: 10px;
  }
}

@media (max-width: 767px) {
  /* line 57, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .about_info_area .info_thumb {
    margin-bottom: 30px;
  }
}

/* line 61, ../../../../Desktop/role_permission/scss/_about_page.scss */
.about_info_area .info_thumb img {
  width: 100%;
}

/* line 65, ../../../../Desktop/role_permission/scss/_about_page.scss */
.about_info_area .info_text {
  padding-left: 65px;
  padding-right: 75px;
}

@media (max-width: 767px) {
  /* line 65, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .about_info_area .info_text {
    padding: 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 65, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .about_info_area .info_text {
    padding: 0;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 65, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .about_info_area .info_text {
    padding: 0;
  }
}

/* line 82, ../../../../Desktop/role_permission/scss/_about_page.scss */
.about_info_area .info_text .info_text_1 {
  margin-top: 17px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 82, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .about_info_area .info_text .info_text_1 {
    margin-top: 0;
  }
}

/* line 89, ../../../../Desktop/role_permission/scss/_about_page.scss */
.about_info_area .info_text .info_text_2 {
  margin-bottom: 45px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 89, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .about_info_area .info_text .info_text_2 {
    margin-bottom: 10px;
  }
}

/* line 98, ../../../../Desktop/role_permission/scss/_about_page.scss */
.about_info_area2 {
  padding-top: 90px;
  padding-bottom: 90px;
}

@media (max-width: 767px) {
  /* line 98, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .about_info_area2 {
    padding-top: 25px;
    padding-bottom: 50px;
  }
}

/* line 110, ../../../../Desktop/role_permission/scss/_about_page.scss */
.about_info_area2 .info_thumb img {
  width: 100%;
}

/* line 114, ../../../../Desktop/role_permission/scss/_about_page.scss */
.about_info_area2 .info_text {
  padding-right: 75px;
}

@media (max-width: 767px) {
  /* line 114, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .about_info_area2 .info_text {
    padding-right: 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 114, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .about_info_area2 .info_text {
    padding-right: 0;
  }
}

/* line 128, ../../../../Desktop/role_permission/scss/_about_page.scss */
.about_info_area2 .info_text .info_text_1 {
  margin-top: 17px;
}

/* line 132, ../../../../Desktop/role_permission/scss/_about_page.scss */
.about_info_area2 .info_text .info_text_2 {
  margin-bottom: 45px;
}

/* line 141, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area {
  padding-top: 170px;
  padding-bottom: 150px;
}

@media (max-width: 767px) {
  /* line 141, ../../../../Desktop/role_permission/scss/_about_page.scss */
  .team_area {
    padding-top: 50px;
    padding-bottom: 20px;
  }
}

/* line 148, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area .single_team {
  margin-bottom: 30px;
}

/* line 150, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area .single_team .team_thumb {
  position: relative;
  z-index: 2;
  overflow: hidden;
}

/* line 154, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area .single_team .team_thumb img {
  width: 100%;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

/* line 159, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area .single_team .team_thumb .social_links {
  background: #fff;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 22px;
  -webkit-transform: translateY(70px);
  -moz-transform: translateY(70px);
  -ms-transform: translateY(70px);
  transform: translateY(70px);
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

/* line 168, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area .single_team .team_thumb .social_links ul {
  text-align: center;
}

/* line 170, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area .single_team .team_thumb .social_links ul li {
  display: inline-block;
}

/* line 172, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area .single_team .team_thumb .social_links ul li a {
  color: #ccc;
  margin: 0 16px;
}

/* line 175, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area .single_team .team_thumb .social_links ul li a:hover {
  color: #ff4357;
}

/* line 183, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area .single_team .team_content {
  padding: 21px 0;
}

/* line 185, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area .single_team .team_content h3 {
  font-size: 22px;
  font-weight: 700;
  color: #1c1c1c;
  margin-bottom: 8px;
}

/* line 191, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area .single_team .team_content p {
  font-size: 16px;
  color: #919191;
  margin-bottom: 0;
  line-height: 16px;
}

/* line 198, ../../../../Desktop/role_permission/scss/_about_page.scss */
.team_area .single_team:hover .social_links {
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transform: translateY(0px);
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_service_page.scss */
.service_wrap_area {
  padding-top: 80px;
  padding-bottom: 90px;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_service_page.scss */
  .service_wrap_area {
    padding-top: 0px;
    padding-bottom: 20px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_service_page.scss */
  .service_wrap_area {
    padding-top: 0;
  }
}

/* line 11, ../../../../Desktop/role_permission/scss/_service_page.scss */
.service_wrap_area .single_service_wrap {
  padding-bottom: 90px;
}

@media (max-width: 767px) {
  /* line 11, ../../../../Desktop/role_permission/scss/_service_page.scss */
  .service_wrap_area .single_service_wrap {
    padding-bottom: 30px;
  }
}

/* line 17, ../../../../Desktop/role_permission/scss/_service_page.scss */
.service_wrap_area .single_service_wrap .thumb img {
  width: 100%;
}

/* line 21, ../../../../Desktop/role_permission/scss/_service_page.scss */
.service_wrap_area .single_service_wrap:nth-child(4) {
  padding-bottom: 0;
}

@media (max-width: 767px) {
  /* line 24, ../../../../Desktop/role_permission/scss/_service_page.scss */
  .service_wrap_area .single_service_wrap:nth-child(1), .service_wrap_area .single_service_wrap:nth-child(3) {
    margin-bottom: 30px;
    padding-bottom: 0;
  }
}

@media (max-width: 767px) {
  /* line 30, ../../../../Desktop/role_permission/scss/_service_page.scss */
  .service_wrap_area .single_service_wrap:nth-child(1).single_service_wrap .thumb, .service_wrap_area .single_service_wrap:nth-child(3).single_service_wrap .thumb {
    margin-bottom: 30px;
  }
}

/* line 38, ../../../../Desktop/role_permission/scss/_service_page.scss */
.service_wrap_area .single_service_wrap .service_wrap_info {
  padding-left: 65px;
}

/* line 39, ../../../../Desktop/role_permission/scss/_service_page.scss */
.service_wrap_area .single_service_wrap .service_wrap_info.no_padding {
  padding-left: 0;
  margin-bottom: 30px;
}

@media (max-width: 767px) {
  /* line 38, ../../../../Desktop/role_permission/scss/_service_page.scss */
  .service_wrap_area .single_service_wrap .service_wrap_info {
    padding-left: 0px;
  }
}

@media (max-width: 767px) {
  /* line 38, ../../../../Desktop/role_permission/scss/_service_page.scss */
  .service_wrap_area .single_service_wrap .service_wrap_info {
    padding-left: 0px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 38, ../../../../Desktop/role_permission/scss/_service_page.scss */
  .service_wrap_area .single_service_wrap .service_wrap_info {
    padding-left: 0px;
  }
}

/* line 53, ../../../../Desktop/role_permission/scss/_service_page.scss */
.service_wrap_area .single_service_wrap .service_wrap_info .section_title {
  margin-bottom: 19px;
}

/* line 56, ../../../../Desktop/role_permission/scss/_service_page.scss */
.service_wrap_area .single_service_wrap .service_wrap_info p {
  color: #505050;
  line-height: 1.75;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 59, ../../../../Desktop/role_permission/scss/_service_page.scss */
  .service_wrap_area .single_service_wrap .service_wrap_info p br {
    display: none;
  }
}

/* line 65, ../../../../Desktop/role_permission/scss/_service_page.scss */
.service_wrap_area .single_service_wrap .service_wrap_info ul {
  margin-bottom: 51px;
}

@media (max-width: 767px) {
  /* line 65, ../../../../Desktop/role_permission/scss/_service_page.scss */
  .service_wrap_area .single_service_wrap .service_wrap_info ul {
    margin-bottom: 20px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 65, ../../../../Desktop/role_permission/scss/_service_page.scss */
  .service_wrap_area .single_service_wrap .service_wrap_info ul {
    margin-bottom: 30px;
  }
}

/* line 73, ../../../../Desktop/role_permission/scss/_service_page.scss */
.service_wrap_area .single_service_wrap .service_wrap_info ul li {
  color: #505050;
  line-height: 1.75;
  font-family: "Nunito Sans", sans-serif;
  font-size: 16px;
  font-weight: 600;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 {
  padding-top: 180px;
  padding-bottom: 100px;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_case_01.scss */
  .case_study_page_01 {
    padding-top: 50px;
    padding-bottom: 20px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_case_01.scss */
  .case_study_page_01 {
    padding-top: 80px;
    padding-bottom: 50px;
  }
}

/* line 12, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .portfolio-menu {
  margin-bottom: 95px;
}

@media (max-width: 767px) {
  /* line 12, ../../../../Desktop/role_permission/scss/_case_01.scss */
  .case_study_page_01 .portfolio-menu {
    margin-bottom: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 12, ../../../../Desktop/role_permission/scss/_case_01.scss */
  .case_study_page_01 .portfolio-menu {
    margin-bottom: 50px;
  }
}

/* line 20, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .portfolio-menu button {
  border: none;
  background: transparent;
  font-size: 16px;
  color: #505050;
  cursor: pointer;
  border: 1px solid transparent;
  padding: 5px 26px;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}

@media (max-width: 767px) {
  /* line 20, ../../../../Desktop/role_permission/scss/_case_01.scss */
  .case_study_page_01 .portfolio-menu button {
    padding: 5px 10px;
    font-size: 12px;
  }
}

/* line 34, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .portfolio-menu button.active {
  color: #ff4357;
  border: 1px solid #ff4357;
}

/* line 40, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .single_study {
  margin-bottom: 80px;
  -webkit-box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.05);
}

@media (max-width: 767px) {
  /* line 40, ../../../../Desktop/role_permission/scss/_case_01.scss */
  .case_study_page_01 .single_study {
    margin-bottom: 30px;
  }
}

/* line 46, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .single_study .thumb {
  overflow: hidden;
}

/* line 48, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .single_study .thumb img {
  width: 100%;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

/* line 54, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .single_study .study_bottom {
  padding: 35px;
}

/* line 56, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .single_study .study_bottom span {
  color: #919191;
  font-size: 14px;
  font-weight: 400;
}

/* line 61, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .single_study .study_bottom h3 {
  margin-bottom: 0;
}

/* line 63, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .single_study .study_bottom h3 a {
  color: #000000;
  font-size: 24px;
  line-height: 1.2;
  color: #1c1c1c;
  font-weight: 600;
}

@media (max-width: 767px) {
  /* line 63, ../../../../Desktop/role_permission/scss/_case_01.scss */
  .case_study_page_01 .single_study .study_bottom h3 a {
    font-size: 17px;
  }
}

@media tablet_device {
  /* line 63, ../../../../Desktop/role_permission/scss/_case_01.scss */
  .case_study_page_01 .single_study .study_bottom h3 a {
    font-size: 20px;
  }
}

/* line 80, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .single_study:hover .thumb img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}

/* line 85, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .single_study:hover .study_bottom h3 {
  margin-bottom: 0;
}

/* line 87, ../../../../Desktop/role_permission/scss/_case_01.scss */
.case_study_page_01 .single_study:hover .study_bottom h3 a {
  color: #ff4357;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_approach.scss */
.approach_bg_1 {
  background-image: url(../img/approach/approach_banner_1.png);
}

/* line 4, ../../../../Desktop/role_permission/scss/_approach.scss */
.approach_bg_2 {
  background-image: url(../img/approach/approach_banner_2.png);
}

/* line 7, ../../../../Desktop/role_permission/scss/_approach.scss */
.approach_bg_3 {
  background-image: url(../img/approach/approach_banner_3.png);
}

/* line 10, ../../../../Desktop/role_permission/scss/_approach.scss */
.approach_bg_4 {
  background-image: url(../img/approach/approach_banner_4.png);
}

/* line 13, ../../../../Desktop/role_permission/scss/_approach.scss */
.approach_bg_5 {
  background-image: url(../img/approach/approach_banner_5.png);
}

/* line 21, ../../../../Desktop/role_permission/scss/_approach.scss */
.approach_intro {
  padding-top: 172px;
  padding-bottom: 180px;
}

@media (max-width: 767px) {
  /* line 21, ../../../../Desktop/role_permission/scss/_approach.scss */
  .approach_intro {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 21, ../../../../Desktop/role_permission/scss/_approach.scss */
  .approach_intro {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}

/* line 32, ../../../../Desktop/role_permission/scss/_approach.scss */
.approach_intro .approach_info_title {
  margin-bottom: 85px;
}

/* line 35, ../../../../Desktop/role_permission/scss/_approach.scss */
.approach_intro .approach_text {
  padding-right: 95px;
}

@media (max-width: 767px) {
  /* line 35, ../../../../Desktop/role_permission/scss/_approach.scss */
  .approach_intro .approach_text {
    padding-right: 0;
    margin-bottom: 20px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 35, ../../../../Desktop/role_permission/scss/_approach.scss */
  .approach_intro .approach_text {
    padding-right: 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 35, ../../../../Desktop/role_permission/scss/_approach.scss */
  .approach_intro .approach_text {
    padding-right: 0;
  }
}

/* line 47, ../../../../Desktop/role_permission/scss/_approach.scss */
.approach_intro .approach_text span {
  color: #1c1c1c;
  font-weight: 600;
  font-size: 22px;
  margin-bottom: 13px;
  display: block;
}

/* line 54, ../../../../Desktop/role_permission/scss/_approach.scss */
.approach_intro .approach_text p {
  font-size: 16px;
  line-height: 28px;
  color: #505050;
  margin-bottom: 0;
}

/* line 62, ../../../../Desktop/role_permission/scss/_approach.scss */
.approach_intro .approch_thumb img {
  width: 100%;
}

/* line 69, ../../../../Desktop/role_permission/scss/_approach.scss */
.single_approach .approach_banner {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  height: 700px;
}

@media (max-width: 767px) {
  /* line 69, ../../../../Desktop/role_permission/scss/_approach.scss */
  .single_approach .approach_banner {
    height: 400px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 69, ../../../../Desktop/role_permission/scss/_approach.scss */
  .single_approach .approach_banner {
    height: 500px;
  }
}

/* line 85, ../../../../Desktop/role_permission/scss/_approach.scss */
.single_approach .section_title span {
  margin-bottom: 0;
  color: #00ebac;
  font-size: 14px;
  font-weight: 700;
  display: block;
}

/* line 94, ../../../../Desktop/role_permission/scss/_approach.scss */
.single_approach .section_title span.blue {
  color: #458efe !important;
}

/* line 101, ../../../../Desktop/role_permission/scss/_approach.scss */
.single_approach .section_title span.orange {
  color: #ff930e !important;
}

/* line 107, ../../../../Desktop/role_permission/scss/_approach.scss */
.single_approach .section_title span.violate {
  color: #9657f6 !important;
}

/* line 112, ../../../../Desktop/role_permission/scss/_approach.scss */
.single_approach .section_title h4 {
  margin-top: 7px;
  margin-bottom: 23px;
}

/* line 117, ../../../../Desktop/role_permission/scss/_approach.scss */
.single_approach .proach_inenr {
  padding-top: 80px;
  padding-bottom: 173px;
}

@media (max-width: 767px) {
  /* line 117, ../../../../Desktop/role_permission/scss/_approach.scss */
  .single_approach .proach_inenr {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 117, ../../../../Desktop/role_permission/scss/_approach.scss */
  .single_approach .proach_inenr {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

/* line 129, ../../../../Desktop/role_permission/scss/_approach.scss */
.single_approach .approach_info {
  padding-right: 95px;
}

@media (max-width: 767px) {
  /* line 129, ../../../../Desktop/role_permission/scss/_approach.scss */
  .single_approach .approach_info {
    padding-right: 0;
    margin-bottom: 20px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 129, ../../../../Desktop/role_permission/scss/_approach.scss */
  .single_approach .approach_info {
    padding-right: 0;
  }
}

/* line 138, ../../../../Desktop/role_permission/scss/_approach.scss */
.single_approach .approach_info p {
  color: #505050;
  font-size: 16px;
  line-height: 1.75;
  margin-bottom: 0;
}

/* line 147, ../../../../Desktop/role_permission/scss/_approach.scss */
.single_approach .approach_info_list ul li {
  color: #505050;
  font-size: 16px;
  line-height: 1.75;
  font-weight: 600;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_career.scss */
.carrer_area {
  padding-top: 180px;
  padding-bottom: 173px;
  word-wrap: break-word;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}

/* line 14, ../../../../Desktop/role_permission/scss/_career.scss */
.carrer_area .career_thumb img {
  width: 100%;
}

@media (max-width: 767px) {
  /* line 21, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area .col-lg-12:first-child .single_career_wrap .career_text h4 {
    margin-top: 30px;
  }
}

@media (max-width: 767px) {
  /* line 28, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area .col-lg-12:last-child .single_career_wrap {
    margin-top: 30px;
  }
}

/* line 33, ../../../../Desktop/role_permission/scss/_career.scss */
.carrer_area .single_career_wrap {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
}

@media (max-width: 767px) {
  /* line 33, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area .single_career_wrap {
    display: block;
  }
}

/* line 41, ../../../../Desktop/role_permission/scss/_career.scss */
.carrer_area .single_career_wrap .single_iteam {
  width: 50%;
}

@media (max-width: 767px) {
  /* line 41, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area .single_career_wrap .single_iteam {
    width: 100%;
  }
}

/* line 48, ../../../../Desktop/role_permission/scss/_career.scss */
.carrer_area .single_career_wrap .career_text h4 {
  color: #1c1c1c;
  font-size: 34px;
  font-weight: 700;
  margin-bottom: 9px;
}

@media (max-width: 767px) {
  /* line 48, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area .single_career_wrap .career_text h4 {
    font-size: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 48, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area .single_career_wrap .career_text h4 {
    font-size: 35px;
  }
}

/* line 60, ../../../../Desktop/role_permission/scss/_career.scss */
.carrer_area .single_career_wrap .career_text p {
  color: #505050;
  font-size: 16px;
  line-height: 28px;
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 60, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area .single_career_wrap .career_text p {
    font-size: 15px;
    margin-bottom: 0;
    padding-right: 15px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 60, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area .single_career_wrap .career_text p {
    font-size: 15px;
    margin-bottom: 0;
    padding-right: 15px;
  }
}

@media (max-width: 767px) {
  /* line 74, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area .single_career_wrap .career_text p br {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 74, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area .single_career_wrap .career_text p br {
    display: none;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 74, ../../../../Desktop/role_permission/scss/_career.scss */
  .carrer_area .single_career_wrap .career_text p br {
    display: none;
  }
}

/* line 90, ../../../../Desktop/role_permission/scss/_career.scss */
.career_list_area {
  padding-bottom: 150px;
}

@media (max-width: 767px) {
  /* line 90, ../../../../Desktop/role_permission/scss/_career.scss */
  .career_list_area {
    margin-bottom: 30px;
  }
}

/* line 96, ../../../../Desktop/role_permission/scss/_career.scss */
.career_list_area .carrer_header h3 {
  font-size: 34px;
  font-weight: 700;
  margin-bottom: 32px;
}

@media (max-width: 767px) {
  /* line 96, ../../../../Desktop/role_permission/scss/_career.scss */
  .career_list_area .carrer_header h3 {
    margin-bottom: 20px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 96, ../../../../Desktop/role_permission/scss/_career.scss */
  .career_list_area .carrer_header h3 {
    margin-bottom: 30px;
  }
}

@media (max-width: 767px) {
  /* line 108, ../../../../Desktop/role_permission/scss/_career.scss */
  .career_list_area .career_content {
    padding: 0;
  }
}

/* line 112, ../../../../Desktop/role_permission/scss/_career.scss */
.career_list_area .career_content .single_content {
  background: #f6faff;
  margin-bottom: 30px;
  padding: 30px;
}

@media (max-width: 767px) {
  /* line 112, ../../../../Desktop/role_permission/scss/_career.scss */
  .career_list_area .career_content .single_content {
    margin-bottom: 30px;
  }
}

/* line 119, ../../../../Desktop/role_permission/scss/_career.scss */
.career_list_area .career_content .single_content h4 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 15px;
}

/* line 125, ../../../../Desktop/role_permission/scss/_career.scss */
.career_list_area .career_content .single_content p {
  font-size: 16px;
  line-height: 1.75;
  margin-bottom: 0;
}

/* line 131, ../../../../Desktop/role_permission/scss/_career.scss */
.career_list_area .career_content .single_content a.readmore_Btn {
  color: #919191;
  font-size: 16px;
  font-weight: 700;
  display: inline-block;
  margin-top: 10px;
  border-bottom: 1px solid transparent;
}

/* line 138, ../../../../Desktop/role_permission/scss/_career.scss */
.career_list_area .career_content .single_content a.readmore_Btn:hover {
  border-bottom: 1px solid #919191;
}

/* line 142, ../../../../Desktop/role_permission/scss/_career.scss */
.career_list_area .career_content .single_content .lists {
  margin-top: 20px;
}

/* line 144, ../../../../Desktop/role_permission/scss/_career.scss */
.career_list_area .career_content .single_content .lists .single_list {
  margin-bottom: 20px;
}

/* line 146, ../../../../Desktop/role_permission/scss/_career.scss */
.career_list_area .career_content .single_content .lists .single_list h5 {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 0;
  line-height: 1.33;
  margin-bottom: 5px;
}

/* line 154, ../../../../Desktop/role_permission/scss/_career.scss */
.career_list_area .career_content .single_content .lists .single_list ul li {
  font-size: 16px;
  color: #505050;
  font-weight: 600;
  line-height: 1.75;
}

@media (max-width: 767px) {
  /* line 90, ../../../../Desktop/role_permission/scss/_career.scss */
  .career_list_area {
    padding-bottom: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 90, ../../../../Desktop/role_permission/scss/_career.scss */
  .career_list_area {
    padding-bottom: 0 !important;
  }
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area {
  padding-top: 180px;
  padding-bottom: 180px;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_blog.scss */
  .blog_area {
    padding-top: 50px;
    padding-bottom: 20px;
  }
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_blog.scss */
  .blog_area {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

/* line 12, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area .single_blog {
  margin-bottom: 80px;
}

@media (max-width: 767px) {
  /* line 12, ../../../../Desktop/role_permission/scss/_blog.scss */
  .blog_area .single_blog {
    margin-bottom: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 12, ../../../../Desktop/role_permission/scss/_blog.scss */
  .blog_area .single_blog {
    margin-bottom: 30px;
  }
}

/* line 20, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area .single_blog .blog_thumb {
  overflow: hidden;
}

/* line 23, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area .single_blog .blog_thumb a img {
  width: 100%;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

/* line 30, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area .single_blog .blog_meta {
  padding: 23px 23px;
  -webkit-box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.05);
}

/* line 33, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area .single_blog .blog_meta h4 {
  margin-bottom: 7px;
}

/* line 36, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area .single_blog .blog_meta h4 a {
  font-family: "Nunito Sans", sans-serif;
  color: #1c1c1c;
  line-height: 1.5;
  font-weight: 700;
  font-size: 20px;
}

@media (max-width: 767px) {
  /* line 36, ../../../../Desktop/role_permission/scss/_blog.scss */
  .blog_area .single_blog .blog_meta h4 a {
    font-size: 18px;
  }
}

/* line 45, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area .single_blog .blog_meta h4 a:hover {
  color: #ff4357;
}

/* line 51, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area .single_blog span {
  color: #8f8f8f;
  font-size: 14px;
  line-height: 1.36;
}

/* line 58, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area .single_blog:hover .blog_thumb img {
  -webkit-transform: scale(1.03);
  -moz-transform: scale(1.03);
  -ms-transform: scale(1.03);
  transform: scale(1.03);
}

/* line 64, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area form {
  margin-bottom: 60px;
}

@media (max-width: 767px) {
  /* line 64, ../../../../Desktop/role_permission/scss/_blog.scss */
  .blog_area form {
    margin-bottom: 30px;
  }
}

/* line 71, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area form .chose_catagori .nice-select {
  height: 50px;
  line-height: 50px;
  width: 100%;
  color: #919191;
  font-size: 16px;
  font-family: "Nunito Sans", sans-serif;
  border: 1px solid #dfdfdf;
  margin-bottom: 30px;
}

/* line 81, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area form .chose_catagori .nice-select .option {
  cursor: pointer;
  font-weight: 300;
  line-height: 40px;
  min-height: 40px;
}

/* line 88, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area form .chose_catagori .nice-select::after {
  color: #505050;
  font-size: 12px;
  top: 5px;
  right: 28px;
}

/* line 95, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area form .search_keyword {
  position: relative;
  margin-bottom: 30px;
}

/* line 98, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area form .search_keyword input {
  width: 100%;
  border: 1px solid #dfdfdf;
  height: 50px;
  padding-left: 20px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}

/* line 104, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area form .search_keyword input::placeholder {
  font-size: 16px;
  color: #919191;
  font-weight: 400;
  font-family: "Nunito Sans", sans-serif;
}

/* line 112, ../../../../Desktop/role_permission/scss/_blog.scss */
.blog_area form .search_keyword button {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  color: #505050;
  font-size: 17px;
  background: none;
  border: none;
  right: 14px;
  line-height: 50px;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_bg {
  background-image: url(../img/blog_details/bannner.png);
}

/* line 4, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_banner_wrap {
  padding: 214px 0 366px 0;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  position: relative;
}

@media (max-width: 767px) {
  /* line 4, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .blog_details_banner_wrap {
    padding: 150px 0 220px 0;
  }
}

/* line 15, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_banner_wrap .blog_text .section_title h3 {
  margin-bottom: 13px;
}

/* line 18, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_banner_wrap .blog_text .section_title p {
  font-size: 16px;
  color: #fff;
  font-weight: 400;
}

/* line 25, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_banner_wrap:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #000;
  content: '';
  opacity: .3;
}

/* line 38, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info {
  position: relative;
  margin-top: -160px;
}

/* line 41, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner {
  background: #fff;
}

/* line 43, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap {
  padding: 50px 60px;
}

@media (max-width: 767px) {
  /* line 43, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .blog_details_info .blog_details_inner .details_info_wrap {
    padding: 20px;
  }
}

/* line 49, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .quote_text p {
  font-size: 20px;
  line-height: 32px;
  color: #505050;
}

/* line 56, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .compo_list h4 {
  font-size: 30px;
  font-weight: 700;
  margin-top: 40px;
  margin-bottom: 20px;
}

@media (max-width: 767px) {
  /* line 56, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .blog_details_info .blog_details_inner .details_info_wrap .compo_list h4 {
    font-size: 25px;
  }
}

/* line 65, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .compo_list p {
  font-size: 16px;
  font-weight: 400;
}

/* line 70, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .compo_list .lists li {
  font-size: 16px;
  color: #505050;
  font-weight: 600;
  line-height: 1.75;
}

/* line 77, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .compo_list .img_details {
  margin-top: 34px;
  margin-bottom: 31px;
}

/* line 80, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .compo_list .img_details p {
  font-size: 20px;
  line-height: 1.6;
  font-weight: 600;
  color: #505050;
}

/* line 88, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .compo_list .thumb img {
  width: 100%;
}

/* line 93, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .compo_list .img_title h5 {
  font-size: 14px;
  color: #505050;
  font-style: italic;
  font-weight: 400;
  margin-top: 10px;
  margin-bottom: 42px;
}

/* line 103, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .compo_list .first_component h4 {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 19px;
}

@media (max-width: 767px) {
  /* line 103, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .blog_details_info .blog_details_inner .details_info_wrap .compo_list .first_component h4 {
    font-size: 25px;
  }
}

/* line 111, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .compo_list .first_component p {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.75;
}

/* line 117, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .compo_list .quote_marked {
  position: relative;
  padding-left: 25px;
  margin: 45px 0;
}

/* line 121, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .compo_list .quote_marked::before {
  height: 100%;
  width: 5px;
  left: 0;
  top: 0;
  background: #ff4357;
  content: '';
  position: absolute;
}

/* line 130, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .compo_list .quote_marked p {
  font-size: 20px;
  line-height: 1.6;
  font-weight: 600;
}

/* line 137, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .video_wrap {
  background-image: url(../img/blog_details/2.png);
  background-repeat: no-repeat;
  height: 550px;
  background-size: cover;
  margin-bottom: 42px;
}

@media (max-width: 767px) {
  /* line 137, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .blog_details_info .blog_details_inner .details_info_wrap .video_wrap {
    height: 350px;
  }
}

/* line 147, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .blog_details_inner .details_info_wrap .video_wrap .video_icon a {
  color: #FF0000;
  font-size: 98px;
}

@media (max-width: 767px) {
  /* line 147, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .blog_details_info .blog_details_inner .details_info_wrap .video_wrap .video_icon a {
    font-size: 55px;
  }
}

/* line 160, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .tags li {
  display: inline-block;
  margin-right: 6px;
}

/* line 163, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .tags li a {
  color: #505050;
  font-size: 14px;
  font-weight: 400;
  font-style: italic;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  border: 1px solid #cccccc;
  padding: 6px 12px;
  display: inline-block;
}

/* line 172, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .tags li a:hover {
  color: #F34A38;
}

/* line 178, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .social_links {
  text-align: right;
}

@media (max-width: 767px) {
  /* line 178, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .blog_details_info .feedback_wrap .social_links {
    text-align: left;
    margin-top: 30px;
  }
}

/* line 185, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .social_links ul li {
  display: inline-block;
  margin-left: 6px;
}

/* line 188, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .social_links ul li a {
  width: 40px;
  height: 40px;
  color: #d2d2d2;
  border: 1px solid #d2d2d2;
  text-align: center;
  line-height: 40px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  display: inline-block;
  font-size: 16px;
}

/* line 200, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .social_links ul li a:hover {
  color: #fff;
  background: #ff4357;
  border: 1px solid transparent;
}

/* line 209, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .author_feedback {
  border: 1px solid #dfdfdf;
  padding: 25px 30px;
  margin-top: 54px;
  margin-bottom: 25px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}

@media (max-width: 767px) {
  /* line 209, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .blog_details_info .feedback_wrap .author_feedback {
    display: block !important;
  }
}

/* line 218, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .author_feedback .thumb {
  width: 100px;
  height: 100px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}

/* line 222, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .author_feedback .thumb img {
  width: 100%;
}

/* line 226, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .author_feedback .info {
  padding-left: 22px;
}

@media (max-width: 767px) {
  /* line 226, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .blog_details_info .feedback_wrap .author_feedback .info {
    padding-left: 0;
    margin-top: 20px;
  }
}

/* line 232, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .author_feedback .info span {
  display: block;
  font-size: 18px;
  color: #505050;
  font-weight: 700;
}

/* line 238, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .author_feedback .info p {
  line-height: 1.75;
  margin-bottom: 0;
  margin-bottom: 5px;
  font-size: 16px;
}

/* line 245, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .author_feedback .info .links a {
  font-size: 16px;
  color: #D1D1D1;
  margin-right: 14px;
}

/* line 249, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .author_feedback .info .links a:hover {
  color: #F34A38;
}

/* line 256, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .next_prev_post {
  margin-bottom: 80px;
}

/* line 258, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .next_prev_post a {
  color: #919191;
  font-size: 16px;
  font-weight: 400;
  border: 1px solid #cccccc;
  padding: 9px 33px;
  display: inline-block;
}

/* line 265, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .feedback_wrap .next_prev_post a:hover {
  color: #F34A38;
}

/* line 274, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.blog_details_info .news_area .section_title h4 {
  font-size: 30px;
  margin-bottom: 39px;
}

@media (max-width: 767px) {
  /* line 274, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .blog_details_info .news_area .section_title h4 {
    font-size: 25px;
  }
}

/* line 288, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.coment_area {
  margin-top: 50px;
}

/* line 291, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.coment_area .section_title h4 {
  font-size: 30px;
  margin-bottom: 35px;
}

@media (max-width: 767px) {
  /* line 291, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .coment_area .section_title h4 {
    font-size: 25px;
  }
}

/* line 300, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.coment_area .comment_wrap .single_comment {
  margin-bottom: 25px;
}

@media (max-width: 767px) {
  /* line 300, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .coment_area .comment_wrap .single_comment {
    display: block !important;
  }
}

/* line 305, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.coment_area .comment_wrap .single_comment:last-child {
  margin-bottom: 0;
}

@media (max-width: 767px) {
  /* line 308, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .coment_area .comment_wrap .single_comment .comment_left {
    display: block !important;
  }
}

/* line 312, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.coment_area .comment_wrap .single_comment .comment_left .thumb {
  margin-right: 20px;
}

@media (max-width: 767px) {
  /* line 315, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .coment_area .comment_wrap .single_comment .comment_left .comnet_info {
    margin-top: 20px;
  }
}

/* line 319, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.coment_area .comment_wrap .single_comment .comment_left .comnet_info p.name_date {
  font-size: 18px;
  font-weight: 700;
  color: #1c1c1c;
  margin-bottom: 5px;
}

/* line 324, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.coment_area .comment_wrap .single_comment .comment_left .comnet_info p.name_date span {
  font-size: 14px;
  font-weight: 400;
  color: #919191;
}

/* line 330, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.coment_area .comment_wrap .single_comment .comment_left .comnet_info p.commented {
  font-size: 16px;
  color: #505050;
}

@media (max-width: 767px) {
  /* line 333, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .coment_area .comment_wrap .single_comment .comment_left .comnet_info p.commented br {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 333, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .coment_area .comment_wrap .single_comment .comment_left .comnet_info p.commented br {
    display: none;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 333, ../../../../Desktop/role_permission/scss/_blog_details.scss */
  .coment_area .comment_wrap .single_comment .comment_left .comnet_info p.commented br {
    display: none;
  }
}

/* line 349, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.coment_area .comment_wrap .single_comment .reply_btn a {
  color: #505050;
  font-weight: 400;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  border: 1px solid #ccc;
  display: inline-block;
  font-size: 14px;
  padding: 4px 12px;
}

/* line 357, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.coment_area .comment_wrap .single_comment .reply_btn a:hover {
  border-color: #ff4357;
  color: #ff4357;
}

/* line 370, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.contact_form_area.blog_contact {
  padding-top: 104px;
}

/* line 373, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.contact_form_area.blog_contact .section_title h4 {
  font-size: 30px;
  margin-bottom: 0;
}

/* line 385, ../../../../Desktop/role_permission/scss/_blog_details.scss */
.contact_blog_bg {
  background: #f6faff;
  padding: 35px 45px;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.case_details_banner {
  background-image: url(../img/study_details/study_details.png);
  height: 700px;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .case_details_banner {
    height: 400px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .case_details_banner {
    height: 500px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .case_details_banner {
    height: 600px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .case_details_banner {
    height: 600px;
  }
}

/* line 21, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.case_details_wrap {
  padding-top: 90px;
  padding-bottom: 55px;
  background: #F6FAFF;
}

@media (max-width: 767px) {
  /* line 21, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .case_details_wrap {
    padding-top: 60px;
    padding-bottom: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 21, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .case_details_wrap {
    padding-top: 60px;
    padding-bottom: 30px;
  }
}

/* line 34, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.case_details_wrap .section_title h3 {
  margin-bottom: 20px;
}

/* line 37, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.case_details_wrap .section_title .date {
  color: #1c1c1c;
  font-family: "Nunito Sans", sans-serif;
  font-weight: 600;
  margin-bottom: 0;
  position: relative;
  padding-bottom: 23px;
}

/* line 44, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.case_details_wrap .section_title .date::before {
  position: absolute;
  width: 80px;
  height: 2px;
  background-color: #ff4357;
  left: 50%;
  content: '';
  bottom: 0;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}

/* line 57, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.case_details_wrap .details_title h3 {
  font-size: 34px;
  line-height: 46px;
  font-weight: 400;
  margin-bottom: 45px;
}

@media (max-width: 767px) {
  /* line 57, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .case_details_wrap .details_title h3 {
    margin-bottom: 30px;
    font-size: 25px;
    line-height: 35px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 57, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .case_details_wrap .details_title h3 {
    font-size: 25px;
    line-height: 35px;
  }
}

/* line 73, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.case_details_wrap .col-lg-6:nth-child(2) .single_case .icon {
  background: #DDF9F7;
}

/* line 76, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.case_details_wrap .single_case {
  margin-bottom: 30px;
}

/* line 78, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.case_details_wrap .single_case .icon {
  width: 98px;
  height: 98px;
  background: #F7E8EE;
  text-align: center;
  line-height: 98px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  margin: auto;
}

/* line 87, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.case_details_wrap .single_case h3 {
  font-size: 22px;
  font-weight: 700;
  margin-top: 28px;
  margin-bottom: 10px;
}

/* line 93, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.case_details_wrap .single_case p {
  font-size: 16px;
  line-height: 1.75;
  color: #505050;
  margin-bottom: 0;
}

/* line 104, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.challenge_plan_area {
  padding-top: 80px;
}

@media (max-width: 767px) {
  /* line 104, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .challenge_plan_area {
    padding-top: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 104, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .challenge_plan_area {
    padding-top: 30px;
  }
}

/* line 112, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.challenge_plan_area .single_plane {
  margin-bottom: 85px;
}

@media (max-width: 767px) {
  /* line 112, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .challenge_plan_area .single_plane {
    margin-bottom: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 112, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .challenge_plan_area .single_plane {
    margin-bottom: 50px;
  }
}

/* line 120, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.challenge_plan_area .single_plane h4 {
  font-size: 34px;
  line-height: 1.35;
  font-weight: 700;
  color: #1c1c1c;
}

/* line 126, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.challenge_plan_area .single_plane p {
  font-size: 16px;
  color: #505050;
  line-height: 1.75;
  margin-bottom: 6px;
}

/* line 133, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.challenge_plan_area .single_plane ul li {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.75;
  color: #505050;
}

/* line 142, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.challenge_plan_area .challenge_banner img {
  width: 100%;
}

/* line 150, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area {
  padding-top: 79px;
  padding-bottom: 130px;
}

@media (max-width: 767px) {
  /* line 150, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .result_area {
    padding-bottom: 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 150, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .result_area {
    padding-bottom: 50px;
  }
}

/* line 159, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area h3 {
  font-size: 34px;
  font-weight: bold;
  line-height: 1.35;
}

/* line 164, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area p {
  font-size: 16px;
  color: #505050;
  line-height: 1.75;
  margin-bottom: 20px;
  margin-top: 15px;
}

/* line 171, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area .single_trafic {
  margin-bottom: 30px;
}

@media (max-width: 767px) {
  /* line 171, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .result_area .single_trafic {
    margin-bottom: 20px;
  }
}

/* line 176, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area .single_trafic h4 {
  font-size: 34px;
  color: #ff4357;
  font-weight: 700;
  font-family: "Nunito Sans", sans-serif;
  margin-bottom: 5px;
}

/* line 186, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area .single_trafic p {
  font-size: 16px;
  color: #707070;
  line-height: 1.38;
  margin: 0;
}

/* line 193, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area .bordered_top {
  margin-bottom: 30px;
  border-top: 1px solid #dfdfdf;
  padding-top: 40px;
  margin-top: 60px;
}

@media (max-width: 767px) {
  /* line 193, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .result_area .bordered_top {
    margin-top: 10px;
  }
}

/* line 201, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area .bordered_top .social_links {
  margin-bottom: 20px;
}

@media (max-width: 767px) {
  /* line 201, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .result_area .bordered_top .social_links {
    text-align: center;
  }
}

/* line 207, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area .bordered_top .social_links ul li {
  display: inline-block;
  margin-right: 8px;
}

/* line 210, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area .bordered_top .social_links ul li a {
  width: 40px;
  height: 40px;
  color: #d2d2d2;
  border: 1px solid #d2d2d2;
  text-align: center;
  line-height: 40px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  display: inline-block;
  font-size: 16px;
}

/* line 220, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area .bordered_top .social_links ul li a:hover {
  color: #fff;
  background: #ff4357;
  border: 1px solid transparent;
}

/* line 229, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area .bordered_top .next_prev_btn {
  text-align: right;
}

@media (max-width: 767px) {
  /* line 229, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .result_area .bordered_top .next_prev_btn {
    text-align: left;
  }
}

/* line 235, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area .bordered_top .next_prev_btn ul li {
  display: inline-block;
  margin-left: 10px;
  margin-bottom: 20px;
}

@media (max-width: 767px) {
  /* line 235, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
  .result_area .bordered_top .next_prev_btn ul li {
    margin-left: 0;
  }
}

/* line 242, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area .bordered_top .next_prev_btn ul li a {
  color: #919191;
  font-size: 16px;
  font-weight: 400;
  padding: 10px 31px;
  display: inline-block;
  border: 1px solid #ccc;
}

/* line 249, ../../../../Desktop/role_permission/scss/_case_study_details.scss */
.result_area .bordered_top .next_prev_btn ul li a:hover {
  border-color: transparent;
  background: #ff4357;
  color: #fff;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area {
  padding-bottom: 60px;
  padding-top: 180px;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_contact.scss */
  .main_contact_area {
    padding-top: 50px;
    padding-bottom: 30px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_contact.scss */
  .main_contact_area {
    padding-top: 50px;
    padding-bottom: 30px;
  }
}

/* line 12, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .contact_info {
  margin-bottom: 87px;
}

@media (max-width: 767px) {
  /* line 12, ../../../../Desktop/role_permission/scss/_contact.scss */
  .main_contact_area .contact_info {
    margin-bottom: 30px;
  }
}

/* line 17, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .contact_info h3 {
  font-size: 34px;
  font-weight: 700;
  line-height: 1.2;
}

@media (max-width: 767px) {
  /* line 17, ../../../../Desktop/role_permission/scss/_contact.scss */
  .main_contact_area .contact_info h3 {
    font-size: 26px;
  }
}

/* line 24, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .contact_info h3 a {
  color: #458efe;
  display: inline-block;
}

@media (max-width: 767px) {
  /* line 28, ../../../../Desktop/role_permission/scss/_contact.scss */
  .main_contact_area .contact_info h3 br {
    display: none;
  }
}

/* line 35, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .col-lg-4:nth-child(2) .single_contact_info .icon {
  background: #9657f6;
}

/* line 38, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .col-lg-4:nth-child(3) .single_contact_info .icon {
  background: #ff930e;
}

/* line 41, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .single_contact_info {
  margin-bottom: 20px;
}

/* line 43, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .single_contact_info .icon {
  width: 74px;
  height: 74px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  background: #00ebac;
  color: #fff;
  text-align: center;
  line-height: 74px;
  margin: auto;
  font-size: 30px;
}

/* line 57, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .single_contact_info h4 {
  font-size: 22px;
  font-weight: 700;
  color: #1c1c1c;
  margin-top: 24px;
  margin-bottom: 5px;
}

/* line 64, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .single_contact_info p {
  font-size: 16px;
  line-height: 1.75;
  color: #505050;
}

/* line 69, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .single_contact_info .social_links {
  margin-top: 16px;
}

@media (max-width: 767px) {
  /* line 69, ../../../../Desktop/role_permission/scss/_contact.scss */
  .main_contact_area .single_contact_info .social_links {
    text-align: center;
  }
}

/* line 75, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .single_contact_info .social_links ul li {
  display: inline-block;
  margin-right: 8px;
}

/* line 78, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .single_contact_info .social_links ul li a {
  width: 40px;
  height: 40px;
  color: #d2d2d2;
  border: 1px solid #d2d2d2;
  text-align: center;
  line-height: 40px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  display: inline-block;
  font-size: 16px;
}

/* line 88, ../../../../Desktop/role_permission/scss/_contact.scss */
.main_contact_area .single_contact_info .social_links ul li a:hover {
  color: #fff;
  background: #ff4357;
  border: 1px solid transparent;
}

/* line 100, ../../../../Desktop/role_permission/scss/_contact.scss */
#contact-map {
  height: 405px;
}

@media (max-width: 767px) {
  /* line 100, ../../../../Desktop/role_permission/scss/_contact.scss */
  #contact-map {
    height: 350px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 100, ../../../../Desktop/role_permission/scss/_contact.scss */
  #contact-map {
    height: 350px;
  }
}

/* line 109, ../../../../Desktop/role_permission/scss/_contact.scss */
.contact_form_area {
  padding-top: 90px;
  padding-bottom: 180px;
  border-bottom: 1px solid #dfdfdf;
}

@media (max-width: 767px) {
  /* line 109, ../../../../Desktop/role_permission/scss/_contact.scss */
  .contact_form_area {
    padding-top: 30px;
    padding-bottom: 60px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 109, ../../../../Desktop/role_permission/scss/_contact.scss */
  .contact_form_area {
    padding-top: 30px;
    padding-bottom: 40px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 109, ../../../../Desktop/role_permission/scss/_contact.scss */
  .contact_form_area {
    padding-top: 50px;
    padding-bottom: 60px;
  }
}

/* line 125, ../../../../Desktop/role_permission/scss/_contact.scss */
.contact_form_area .section_title {
  margin-bottom: 33px;
}

/* line 128, ../../../../Desktop/role_permission/scss/_contact.scss */
.contact_form_area .single_field {
  margin-bottom: 27px;
}

/* line 130, ../../../../Desktop/role_permission/scss/_contact.scss */
.contact_form_area .single_field input, .contact_form_area .single_field textarea {
  width: 100%;
  padding: 6.5px 0;
  border: none;
  border-bottom: 1px solid #ccc;
  font-size: 16px;
  color: #000;
  font-weight: 400;
  background: transparent;
}

/* line 140, ../../../../Desktop/role_permission/scss/_contact.scss */
.contact_form_area .single_field input::placeholder, .contact_form_area .single_field textarea::placeholder {
  font-size: 16px;
  color: #505050;
  font-weight: 400;
}

/* line 146, ../../../../Desktop/role_permission/scss/_contact.scss */
.contact_form_area .single_field textarea {
  height: 40px;
  margin-top: 49px;
  resize: none;
}

/* line 152, ../../../../Desktop/role_permission/scss/_contact.scss */
.contact_form_area .boxed_btn {
  width: 100%;
  text-align: center;
  cursor: pointer;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

/* line 164, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-dialog {
  max-width: 540px;
  margin: 1.75rem auto;
}

/* line 168, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-content {
  border-radius: 0;
}

/* line 173, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .login_heading {
  background: #f6feff;
  padding: 14px;
}

/* line 176, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .login_heading h3 {
  font-size: 24px;
  font-weight: bold;
  line-height: 2.25;
  text-align: center;
  color: #1c1c37;
  margin-bottom: 0;
}

/* line 185, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .main_content {
  padding: 35px 55px 38px 55px;
}

/* line 188, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form span {
  display: block;
  text-align: center;
  font-size: 16px;
  font-weight: normal;
  text-align: center;
  color: #1c1c37;
  text-transform: capitalize;
  margin-top: 14px;
  margin-bottom: 13px;
}

/* line 200, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .login_with_google a {
  font-size: 16px;
  font-weight: 600;
  line-height: 3.38;
  text-align: center;
  color: #1c1c37;
  height: 50px;
  border-radius: 4px;
  border: solid 1px #cccccc;
  display: block;
  line-height: 50px;
}

/* line 211, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .login_with_google a i {
  color: #4543de;
}

/* line 217, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .login .single_input {
  margin-bottom: 20px;
}

/* line 219, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .login .single_input input {
  width: 100%;
  height: 50px;
  border-radius: 4px;
  border: solid 1px #cccccc;
  font-size: 16px;
  color: #919191;
  padding: 14px 15px;
}

/* line 227, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .login .single_input input::placeholder {
  color: #919191;
  font-size: 16px;
}

/* line 231, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .login .single_input input:focus {
  outline: none;
}

/* line 240, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .forgot_pass {
  text-align: center;
  margin-top: 28px;
}

/* line 243, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .forgot_pass p {
  color: #919191;
  font-size: 16px;
  font-weight: normal;
  margin-bottom: 2px;
}

/* line 248, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .forgot_pass p a {
  color: #919191;
  font-weight: normal;
}

/* line 251, ../../../../Desktop/role_permission/scss/_contact.scss */
.custom_login_from .modal-body .login_form .forgot_pass p a.sing_up {
  color: #4543de;
}

/* line 261, ../../../../Desktop/role_permission/scss/_contact.scss */
.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background-color: #1c1c37;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_custom_input.scss */
.contact_form_area {
  /*= input focus effects css
=========================== */
  /* necessary to give position: relative to parent. */
}

/* line 4, ../../../../Desktop/role_permission/scss/_custom_input.scss */
.contact_form_area :focus {
  outline: none;
}

/* line 6, ../../../../Desktop/role_permission/scss/_custom_input.scss */
.contact_form_area .bordered_1 {
  position: relative;
}

/* line 7, ../../../../Desktop/role_permission/scss/_custom_input.scss */
.contact_form_area input[type="text"] {
  width: 100%;
  box-sizing: border-box;
}

/* line 9, ../../../../Desktop/role_permission/scss/_custom_input.scss */
.contact_form_area .effect-1,
.contact_form_area .effect-2,
.contact_form_area .effect-3 {
  border: 0;
  border-bottom: 1px solid #ccc;
}

/* line 12, ../../../../Desktop/role_permission/scss/_custom_input.scss */
.contact_form_area .effect-1 ~ .focus-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 1px;
  background-color: #ff4357;
  transition: 0.5s;
}

/* line 13, ../../../../Desktop/role_permission/scss/_custom_input.scss */
.contact_form_area .effect-1:focus ~ .focus-border {
  width: 100%;
  transition: 0.4s;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_plan.scss */
.plan_area {
  padding: 170px 0 180px 0;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_plan.scss */
  .plan_area {
    padding: 50px 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_plan.scss */
  .plan_area {
    padding: 90px 0 100px 0;
  }
}

/* line 10, ../../../../Desktop/role_permission/scss/_plan.scss */
.plan_area .section_title.section_title2 p {
  margin-bottom: 30px;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_brand.scss */
.brand_area_2 {
  padding: 180px 0 120px 0;
  position: relative;
}

/* line 5, ../../../../Desktop/role_permission/scss/_brand.scss */
.brand_area_2 .circle_shape {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 10, ../../../../Desktop/role_permission/scss/_brand.scss */
.brand_area_2 .circle_shape img {
  width: 100%;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_brand.scss */
  .brand_area_2 {
    padding: 60px 0 35px 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_brand.scss */
  .brand_area_2 {
    padding: 80px 0 20px 0;
  }
}

/* line 20, ../../../../Desktop/role_permission/scss/_brand.scss */
.brand_area_2 .brand_wrap {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  -ms-justify-content: center;
  justify-content: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  -ms-justify-content: center;
  justify-content: center;
  -ms-flex-pack: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 27, ../../../../Desktop/role_permission/scss/_brand.scss */
.brand_area_2 .brand_wrap .single_brand {
  -webkit-box-flex: 20%;
  -ms-flex: 20% 0 0px;
  flex: 20% 0 0;
  text-align: center;
  margin-bottom: 60px;
}

@media (max-width: 767px) {
  /* line 27, ../../../../Desktop/role_permission/scss/_brand.scss */
  .brand_area_2 .brand_wrap .single_brand {
    -webkit-box-flex: 100%;
    -ms-flex: 100% 0 0px;
    flex: 100% 0 0;
    margin-bottom: 25px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 27, ../../../../Desktop/role_permission/scss/_brand.scss */
  .brand_area_2 .brand_wrap .single_brand {
    -webkit-box-flex: 25%;
    -ms-flex: 25% 0 0px;
    flex: 25% 0 0;
  }
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_works_2.scss */
.work_section {
  padding-top: 180px;
  padding-bottom: 180px;
  position: relative;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .work_section {
    padding-top: 50px;
    padding-bottom: 60px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .work_section {
    padding-top: 100px;
    padding-bottom: 100px;
  }
}

@media (max-width: 767px) {
  /* line 13, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .work_section .work_info {
    margin-bottom: 30px;
  }
}

/* line 19, ../../../../Desktop/role_permission/scss/_works_2.scss */
.work_section .shapes .shap_1 {
  position: absolute;
  left: 10%;
  top: 10%;
}

@media (max-width: 767px) {
  /* line 19, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .work_section .shapes .shap_1 {
    display: none;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 26, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .work_section .shapes .shap_1 img {
    width: 250px;
    height: 250px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 26, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .work_section .shapes .shap_1 img {
    width: 300px;
    height: 300px;
  }
}

/* line 37, ../../../../Desktop/role_permission/scss/_works_2.scss */
.work_section .shapes .shap_2 {
  position: absolute;
  right: 15%;
  bottom: 7%;
}

@media (max-width: 767px) {
  /* line 37, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .work_section .shapes .shap_2 {
    display: none;
  }
}

/* line 46, ../../../../Desktop/role_permission/scss/_works_2.scss */
.work_section .video_works {
  position: relative;
  background-image: url(../img/home_2/video/video.png);
  height: 357px;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center center;
  -webkit-box-shadow: 0 30px 30px rgba(0, 0, 0, 0.08);
  -moz-box-shadow: 0 30px 30px rgba(0, 0, 0, 0.08);
  box-shadow: 0 30px 30px rgba(0, 0, 0, 0.08);
}

/* line 54, ../../../../Desktop/role_permission/scss/_works_2.scss */
.work_section .video_works a {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 85px;
  height: 85px;
  background: #ff4357;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  text-align: center;
  line-height: 85px;
}

/* line 65, ../../../../Desktop/role_permission/scss/_works_2.scss */
.work_section .video_works a i {
  position: relative;
  font-size: 20px;
  color: #fff;
  top: 2px;
  left: 2px;
}

/* line 76, ../../../../Desktop/role_permission/scss/_works_2.scss */
.multipol_manage_area {
  background-image: -webkit-gradient(linear, left bottom, left top, from(#f6feff), to(#fbf5ff));
  background-image: -o-linear-gradient(bottom, #f6feff, #fbf5ff);
  background-image: linear-gradient(to top, #f6feff, #fbf5ff);
  padding: 175px 0 180px 0;
  z-index: 2;
  position: relative;
}

@media (max-width: 767px) {
  /* line 76, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .multipol_manage_area {
    padding-top: 55px;
    padding-bottom: 60px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 76, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .multipol_manage_area {
    padding-top: 95px;
    padding-bottom: 100px;
  }
}

/* line 92, ../../../../Desktop/role_permission/scss/_works_2.scss */
.multipol_manage_area .shapes .shap_1 {
  position: absolute;
  left: 10%;
  top: 7%;
}

@media (max-width: 767px) {
  /* line 92, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .multipol_manage_area .shapes .shap_1 {
    display: none;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 99, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .multipol_manage_area .shapes .shap_1 img {
    width: 250px;
    height: 250px;
  }
}

@media (min-width: 1200px) and (max-width: 1500px) {
  /* line 99, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .multipol_manage_area .shapes .shap_1 img {
    width: 300px;
    height: 300px;
  }
}

/* line 111, ../../../../Desktop/role_permission/scss/_works_2.scss */
.multipol_manage_area .single_manage {
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  margin-bottom: 30px;
  position: relative;
  z-index: 9;
  border-bottom: 2px solid #4543de;
}

/* line 116, ../../../../Desktop/role_permission/scss/_works_2.scss */
.multipol_manage_area .single_manage h4 {
  font-size: 24px;
  font-weight: 600;
  color: #0B0B21;
  margin-bottom: 0;
}

@media (max-width: 767px) {
  /* line 116, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .multipol_manage_area .single_manage h4 {
    font-size: 20px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 116, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .multipol_manage_area .single_manage h4 {
    font-size: 20px;
  }
}

/* line 128, ../../../../Desktop/role_permission/scss/_works_2.scss */
.multipol_manage_area .single_manage p {
  font-size: 20px;
  line-height: 32px;
  margin-bottom: 0;
  margin-top: 20px;
  margin-bottom: 24px;
}

@media (max-width: 767px) {
  /* line 128, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .multipol_manage_area .single_manage p {
    font-size: 16px;
    line-height: 26px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 128, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .multipol_manage_area .single_manage p {
    font-size: 16px;
    line-height: 26px;
  }
}

/* line 144, ../../../../Desktop/role_permission/scss/_works_2.scss */
.multipol_manage_area .single_manage:hover {
  opacity: 1;
}

@media (max-width: 767px) {
  /* line 148, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .multipol_manage_area .manage_tabs {
    padding: 0 15px;
  }
}

/* line 152, ../../../../Desktop/role_permission/scss/_works_2.scss */
.multipol_manage_area .manage_tabs .nav {
  display: flex;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: space-between;
  -moz-justify-content: space-between;
  -ms-justify-content: space-between;
  justify-content: space-between;
  -ms-flex-pack: space-between;
}

/* line 156, ../../../../Desktop/role_permission/scss/_works_2.scss */
.multipol_manage_area .manage_tabs .nav li {
  width: 31.5%;
}

@media (max-width: 767px) {
  /* line 156, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .multipol_manage_area .manage_tabs .nav li {
    width: 100%;
  }
}

/* line 162, ../../../../Desktop/role_permission/scss/_works_2.scss */
.multipol_manage_area .manage_tabs .nav li a {
  padding: 0;
  opacity: .6;
  position: relative;
  z-index: 9;
}

/* line 167, ../../../../Desktop/role_permission/scss/_works_2.scss */
.multipol_manage_area .manage_tabs .nav li a.active {
  opacity: 1;
}

/* line 174, ../../../../Desktop/role_permission/scss/_works_2.scss */
.multipol_manage_area .manage_banner {
  margin-top: 60px;
  -webkit-box-shadow: 0 30px 30px #f6feff;
  -moz-box-shadow: 0 30px 30px #f6feff;
  box-shadow: 0 30px 30px #f6feff;
}

@media (max-width: 767px) {
  /* line 174, ../../../../Desktop/role_permission/scss/_works_2.scss */
  .multipol_manage_area .manage_banner {
    margin-top: 30px;
  }
}

/* line 180, ../../../../Desktop/role_permission/scss/_works_2.scss */
.multipol_manage_area .manage_banner img {
  width: 100%;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area {
  padding-top: 170px;
  padding-bottom: 150px;
  position: relative;
  z-index: 3;
  overflow-y: hidden;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area {
    padding-top: 100px;
    padding-bottom: 40px;
  }
}

/* line 12, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .shap_1 {
  position: absolute;
  left: 7%;
  top: 18%;
  z-index: 0;
}

@media (max-width: 767px) {
  /* line 12, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .shap_1 {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 12, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .shap_1 {
    width: 200px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 12, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .shap_1 {
    width: 200px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 26, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .shap_1 img {
    width: 100%;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 26, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .shap_1 img {
    width: 100%;
  }
}

/* line 35, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .shap_2 {
  position: absolute;
  right: 7%;
  bottom: 5%;
  z-index: 0;
}

@media (max-width: 767px) {
  /* line 35, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .shap_2 {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 35, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .shap_2 {
    width: 200px;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 35, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .shap_2 {
    width: 200px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 49, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .shap_2 img {
    width: 100%;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 49, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .shap_2 img {
    width: 100%;
  }
}

/* line 58, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .nav {
  text-align: center;
  margin-bottom: 90px;
}

@media (max-width: 767px) {
  /* line 58, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .nav {
    margin-bottom: 40px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 58, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .nav {
    margin-bottom: 40px;
  }
}

/* line 67, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .nav a {
  color: #000;
  display: inline-block;
  padding: 13px 60px;
  display: inline-block;
  background: #F2F1FD;
  border-radius: 30px;
  font-size: 16px;
  position: relative;
  z-index: 12;
}

@media (max-width: 767px) {
  /* line 67, ../../../../Desktop/role_permission/scss/_prising.scss */
  .prising_area .nav a {
    padding: 13px 30px;
  }
}

/* line 80, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .nav a.active {
  background-image: linear-gradient(to bottom, #9657f6, #2c62f8);
  color: #fff;
}

/* line 84, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .nav a:first-child {
  border-radius: 30px 0 0 30px;
}

/* line 87, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .nav a:last-child {
  border-radius: 0 30px 30px 0;
}

/* line 93, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .tab-pane {
  transition: .5s;
}

/* line 96, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .single_prising {
  background: #fff;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  border-radius: 15px;
  padding: 48px 0 35px 0;
  margin-bottom: 30px;
  position: relative;
  z-index: 10;
}

/* line 104, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .single_prising .prising_header span {
  font-size: 30px;
  font-weight: 400;
  display: block;
  color: #0B0B21;
  line-height: 1;
}

/* line 111, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .single_prising .prising_header h3 {
  font-size: 30px;
  color: #DF35F0;
  font-weight: 400;
  margin-bottom: 21px;
  margin-top: 19px;
}

/* line 118, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .single_prising .prising_header .prising_btn {
  font-size: 14px;
  font-weight: 600;
  padding: 6px 18px;
}

/* line 124, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .single_prising .prising_list {
  margin-top: 35px;
}

/* line 127, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .single_prising .prising_list ul li {
  font-size: 16px;
  font-weight: normal;
  line-height: 2.6;
  color: #0b0b21;
}

/* line 138, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .single_prising.active .prising_header span {
  color: #fff;
}

/* line 141, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .single_prising.active .prising_header h3 {
  color: #fff;
}

/* line 144, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .single_prising.active .prising_header .prising_btn {
  background: #83DA3A;
}

/* line 146, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .single_prising.active .prising_header .prising_btn:hover {
  border-color: #83DA3A;
  color: #fff;
  background: transparent;
}

/* line 155, ../../../../Desktop/role_permission/scss/_prising.scss */
.prising_area .single_prising.active .prising_list ul li {
  color: #FFFFFF;
}

/* line 164, ../../../../Desktop/role_permission/scss/_prising.scss */
.product_lisence_area {
  padding: 170px 0 124px 0;
}

@media (max-width: 767px) {
  /* line 164, ../../../../Desktop/role_permission/scss/_prising.scss */
  .product_lisence_area {
    padding-top: 60px;
    padding-bottom: 34px;
  }
}

/* line 170, ../../../../Desktop/role_permission/scss/_prising.scss */
.product_lisence_area .single_prodict {
  margin-bottom: 50px;
}

@media (max-width: 767px) {
  /* line 170, ../../../../Desktop/role_permission/scss/_prising.scss */
  .product_lisence_area .single_prodict {
    margin-bottom: 30px;
  }
}

/* line 178, ../../../../Desktop/role_permission/scss/_prising.scss */
.product_lisence_area .single_prodict .lisence_info {
  padding-left: 20px;
}

/* line 180, ../../../../Desktop/role_permission/scss/_prising.scss */
.product_lisence_area .single_prodict .lisence_info h3 {
  font-size: 22px;
  font-weight: 400;
  color: #4543DE;
  margin-bottom: 8px;
}

/* line 186, ../../../../Desktop/role_permission/scss/_prising.scss */
.product_lisence_area .single_prodict .lisence_info p {
  font-size: 18px;
  line-height: 30px;
  color: #505050;
  margin-bottom: 0;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_404_page.scss */
.page_not_found_wrap {
  padding: 150px 0;
  background: #F6FAFF;
}

@media (max-width: 767px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_404_page.scss */
  .page_not_found_wrap {
    padding: 60px 0;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_404_page.scss */
  .page_not_found_wrap {
    padding: 100px 0;
  }
}

@media (min-width: 992px) and (max-width: 1199.99px) {
  /* line 1, ../../../../Desktop/role_permission/scss/_404_page.scss */
  .page_not_found_wrap {
    padding: 100px 0;
  }
}

/* line 15, ../../../../Desktop/role_permission/scss/_404_page.scss */
.page_not_found_wrap .not_found_wrap .thumb img {
  width: 100%;
}

/* line 20, ../../../../Desktop/role_permission/scss/_404_page.scss */
.page_not_found_wrap .not_found_wrap .info_404 {
  margin-top: 40px;
}

/* line 22, ../../../../Desktop/role_permission/scss/_404_page.scss */
.page_not_found_wrap .not_found_wrap .info_404 h3 {
  font-size: 34px;
  font-weight: 700;
  margin-bottom: 0;
}

@media (max-width: 767px) {
  /* line 22, ../../../../Desktop/role_permission/scss/_404_page.scss */
  .page_not_found_wrap .not_found_wrap .info_404 h3 {
    font-size: 27px;
  }
}

/* line 30, ../../../../Desktop/role_permission/scss/_404_page.scss */
.page_not_found_wrap .not_found_wrap .info_404 p {
  font-size: 16px;
  color: #919191;
  margin-top: 5px;
  margin-bottom: 28px;
}

/*----------------------------------------------------*/
/*----------------------------------------------------*/
/* line 1, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.common-radio:empty {
  opacity: 0;
  visibility: hidden;
  position: relative;
  max-height: 0;
  display: block;
}

/* line 11, ../../../../Desktop/role_permission/scss/_role_permission.scss */
input::placeholder, textarea::placeholder {
  position: relative;
  bottom: -5px;
}

/* line 18, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.common-radio:empty ~ label {
  position: relative;
  float: left;
  line-height: 16px;
  text-indent: 28px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
}

/* line 33, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.common-radio:empty ~ label {
  line-height: 16px;
  text-indent: 28px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
}

/* line 41, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.common-radio:empty ~ label::before {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  content: "";
  width: 16px;
  height: 16px;
  background: transparent;
  border-radius: 50px;
  border: 1px solid #415094;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 57, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.common-radio:checked ~ label::before {
  content: "";
  text-indent: 1px;
  color: #415094;
  background-color: transparent;
  border: 1px solid #415094;
  border-top-color: #415094;
  -webkit-transform: rotate(65deg);
  -moz-transform: rotate(65deg);
  -ms-transform: rotate(65deg);
  -o-transform: rotate(65deg);
  transform: rotate(65deg);
  font-size: 12px;
  font-weight: 600;
  border-top-color: transparent;
}

/* line 74, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.common-radio:checked ~ label::after {
  content: "\e64c";
  font-family: "themify";
  position: absolute;
  display: block;
  top: -2px;
  left: 3px;
  text-indent: 1px;
  color: #415094;
  background-color: transparent;
  border: 0px;
  -webkit-transform: rotate(8deg);
  -moz-transform: rotate(8deg);
  -ms-transform: rotate(8deg);
  -o-transform: rotate(8deg);
  transform: rotate(8deg);
  font-size: 14px;
  font-weight: 600;
}

/* line 98, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 30px;
  row-gap: 20px;
}

@media (max-width: 767.98px) {
  /* line 98, ../../../../Desktop/role_permission/scss/_role_permission.scss */
  .erp_role_permission_area {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media (max-width: 991.98px) {
  /* line 98, ../../../../Desktop/role_permission/scss/_role_permission.scss */
  .erp_role_permission_area {
    grid-template-columns: repeat(1, 1fr);
  }
}

/* line 110, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_header {
  padding: 15px 25px;
  background-image: -moz-linear-gradient(0deg, #7c32ff 0%, #a235ec 70%, #c738d8 100%);
  background-image: -webkit-linear-gradient(0deg, #7c32ff 0%, #a235ec 70%, #c738d8 100%);
  background-image: -ms-linear-gradient(0deg, #7c32ff 0%, #a235ec 70%, #c738d8 100%);
}

/* line 115, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_header label {
  font-size: 13px;
  color: #ffffff;
  font-weight: 400;
}

/* line 120, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_header div {
  position: relative;
  top: -5px;
}

/* line 128, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_header .common-radio:empty ~ label::before {
  border: 1px solid #fff;
}

/* line 131, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_header .common-radio:checked ~ label::after {
  color: #fff;
  background-color: transparent;
}

/* line 135, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_header .common-radio:checked ~ label::before {
  border-top-color: transparent;
}

/* line 139, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body {
  background: #fff;
}

/* line 142, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body .submodule label {
  font-size: 15px;
  color: #7c32ff;
}

/* line 146, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body .submodule .common-radio:empty ~ label::before {
  border: 1px solid #7c32ff;
}

/* line 149, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body .submodule .common-radio:checked ~ label::after {
  color: #7c32ff;
  background-color: transparent;
}

/* line 153, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body .submodule .common-radio:checked ~ label::before {
  border-top-color: transparent;
}

/* line 158, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body > ul {
  border-top: 3px solid #d8e6ff;
}

/* line 160, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body > ul > li {
  border-bottom: 2px solid #d8e6ff;
  padding: 6px 25px 16px 25px;
  display: block;
}

/* line 165, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body > ul > li:last-child {
  border: 0;
}

/* line 168, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body > ul > li ul {
  display: grid;
  margin-left: 25px;
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 767.98px) {
  /* line 168, ../../../../Desktop/role_permission/scss/_role_permission.scss */
  .erp_role_permission_area .single_permission .permission_body > ul > li ul {
    grid-template-columns: repeat(2, 1fr);
    margin-left: 20px;
  }
}

/* line 176, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body > ul > li ul label {
  font-size: 15px;
  color: #415094;
  font-weight: 400;
}

/* line 181, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body > ul > li ul .common-radio:empty ~ label::before {
  border: 1px solid #415094;
}

/* line 184, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body > ul > li ul .common-radio:checked ~ label::after {
  color: #415094;
  background-color: transparent;
}

/* line 188, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body > ul > li ul .common-radio:checked ~ label::before {
  border-top-color: transparent;
}

/* line 191, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body > ul > li ul li {
  margin-right: 30px;
}

@media (max-width: 991.98px) {
  /* line 191, ../../../../Desktop/role_permission/scss/_role_permission.scss */
  .erp_role_permission_area .single_permission .permission_body > ul > li ul li {
    margin-right: 20px;
  }
}

@media (max-width: 1200px) {
  /* line 191, ../../../../Desktop/role_permission/scss/_role_permission.scss */
  .erp_role_permission_area .single_permission .permission_body > ul > li ul li {
    margin-right: 0px;
  }
}

/* line 200, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.erp_role_permission_area .single_permission .permission_body > ul > li ul li:last-child {
  margin-right: 0;
}

/* line 213, ../../../../Desktop/role_permission/scss/_role_permission.scss */
.role_permission_wrap .permission_title h4 {
  font-size: 18px;
  font-weight: 500;
  color: #415094;
  margin-bottom: 23px;
}

.erp_role_permission_area {
    display: block !important;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 30px;
    row-gap: 20px;
}
/*----------------------------------------------------*/

/*# sourceMappingURL=style.css.map */
var FormiCheck=function(){return{init:function(){$(".icheck-colors li").click(function(){var a=$(this);if(!a.hasClass("active")){a.siblings().removeClass("active");var i=a.closest(".skin"),c=a.attr("class")?"-"+a.attr("class"):"",e=i.data("color")?"-"+i.data("color"):"-grey",e="-black"===e?"":e;checkbox_default="icheckbox_minimal",radio_default="iradio_minimal",checkbox="icheckbox_minimal"+e,radio="iradio_minimal"+e,i.hasClass("skin-square")&&(checkbox_default="icheckbox_square",radio_default="iradio_square",checkbox="icheckbox_square"+e,radio="iradio_square"+e),i.hasClass("skin-flat")&&(checkbox_default="icheckbox_flat",radio_default="iradio_flat",checkbox="icheckbox_flat"+e,radio="iradio_flat"+e),i.hasClass("skin-line")&&(checkbox_default="icheckbox_line",radio_default="iradio_line",checkbox="icheckbox_line"+e,radio="iradio_line"+e),i.find(".icheck").each(function(){var a=$(this).hasClass("state")?$(this):$(this).parent(),i=a.attr("class").replace(checkbox,checkbox_default+c).replace(radio,radio_default+c);a.attr("class",i)}),i.data("color",a.attr("class")?a.attr("class"):"black"),a.addClass("active")}})}}}();jQuery(document).ready(function(){FormiCheck.init()});
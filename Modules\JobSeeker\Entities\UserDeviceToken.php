<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;

final class UserDeviceToken extends Model
{
    protected $table = 'user_device_tokens';
    
    protected $fillable = [
        'job_seeker_id',
        'device_token',
        'platform',
        'user_agent',
        'last_used_at',
        'token_hash',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'device_token' => 'encrypted',
        'last_used_at' => 'datetime',
    ];

    protected static function booted(): void
    {
        static::creating(function (UserDeviceToken $token) {
            if (empty($token->token_hash) && !empty($token->device_token)) {
                $token->token_hash = self::generateSecureHash($token->device_token);
            }
        });
    }

    /**
     * Get the job seeker that owns this device token.
     */
    public function jobSeeker(): BelongsTo
    {
        return $this->belongsTo(JobSeeker::class, 'job_seeker_id');
    }

    /**
     * Generate a secure hash for the device token.
     * Used for indexed searching if the raw token is encrypted or too long.
     */
    public static function generateSecureHash(string $deviceToken): string
    {
        // Use a fast, non-cryptographic hash if the goal is just indexing/uniqueness check
        // If security/prevention of token leakage from hash is a concern, use HMAC or bcrypt (slower)
        return hash('sha256', $deviceToken); // sha256 is a good general-purpose choice
    }

    /**
     * Accessor for a shortened preview of the token.
     */
    public function getDeviceTokenPreviewAttribute(): string
    {
        if (empty($this->device_token)) {
            return 'N/A';
        }
        return substr($this->device_token, 0, 15) . '...' . substr($this->device_token, -15);
    }

    /**
     * Generate a secure hash of the device token for logging purposes.
     * Returns a truncated SHA256 hash to avoid exposing sensitive data.
     *
     * @param string $deviceToken
     * @return string
     */
    public static function generateSecureHashForLogging(string $deviceToken): string
    {
        return substr(hash('sha256', $deviceToken), 0, 16);
    }

    /**
     * Get a secure hash of this device token for logging.
     *
     * @return string
     */
    public function getSecureHashAttribute(): string
    {
        return self::generateSecureHashForLogging($this->device_token);
    }
} 
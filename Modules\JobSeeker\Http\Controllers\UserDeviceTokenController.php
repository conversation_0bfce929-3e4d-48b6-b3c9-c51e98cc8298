<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Modules\JobSeeker\Entities\UserDeviceToken;

/**
 * UserDeviceTokenController
 * 
 * Handles device token registration and management for push notifications with enhanced security.
 * 
 * SECURITY FEATURES:
 * - Device tokens are encrypted at rest using <PERSON><PERSON>'s encrypted casting
 * - Raw tokens are never logged; only secure hashes are used for logging
 * - Platform validation is configurable through device_tokens.php config
 * - Token previews show only partial information for display
 * 
 * CONFIGURATION:
 * The controller uses configuration from jobseeker.device_tokens:
 * - supported_platforms: Array of allowed platform strings
 * - security.max_token_length: Maximum allowed token length
 * - security.hash_length: Length of secure hash for logging
 * 
 * USAGE:
 * POST /device-tokens/register
 * {
 *   "device_token": "encrypted_token_string",
 *   "platform": "android|ios|web|..." // Based on config
 * }
 * 
 * @package Modules\JobSeeker\Http\Controllers
 */
final class UserDeviceTokenController extends Controller
{
    /**
     * Register a new device token for the authenticated user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function register(Request $request): JsonResponse
    {
        try {
            // Get supported platforms from configuration
            $supportedPlatforms = config('jobseeker.device_tokens.supported_platforms', ['android', 'ios', 'web']);
            $maxTokenLength = config('jobseeker.device_tokens.security.max_token_length', 4096);
            
            $validator = Validator::make($request->all(), [
                'device_token' => ['required', 'string', "max:{$maxTokenLength}"],
                'platform' => ['nullable', 'string', 'in:' . implode(',', $supportedPlatforms)]
            ]);

            if ($validator->fails()) {
                Log::warning('UserDeviceTokenController: Validation failed for device token registration', [
                    'job_seeker_id' => Auth::guard('job_seeker')->id(),
                    'validation_errors' => $validator->errors()->toArray(),
                    'request_data' => $request->only(['platform']),
                    'supported_platforms' => $supportedPlatforms
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed.',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Get the authenticated job seeker ID
            $jobSeekerId = Auth::guard('job_seeker')->id();
            
            if (!$jobSeekerId) {
                Log::error('UserDeviceTokenController: Authentication failed - no job seeker ID found', [
                    'request_data' => $request->only(['platform']),
                    'auth_guard' => 'job_seeker',
                    'is_authenticated' => Auth::guard('job_seeker')->check()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required.'
                ], 401);
            }

            $deviceToken = $request->input('device_token');
            $platform = $request->input('platform');

            // Generate secure hash for logging (no raw token exposure)
            $tokenHash = UserDeviceToken::generateSecureHash($deviceToken);

            Log::info('UserDeviceTokenController: Processing device token registration', [
                'job_seeker_id' => $jobSeekerId,
                'platform' => $platform,
                'device_token_length' => strlen($deviceToken),
                'device_token_hash' => $tokenHash
            ]);

            // Store or update the device token
            $userDeviceToken = UserDeviceToken::updateOrCreate(
                [
                    'job_seeker_id' => $jobSeekerId,
                    'device_token' => $deviceToken
                ],
                [
                    'platform' => $platform,
                    'last_used_at' => now()
                ]
            );

            $wasRecentlyCreated = $userDeviceToken->wasRecentlyCreated;

            Log::info('UserDeviceTokenController: Device token registration successful', [
                'job_seeker_id' => $jobSeekerId,
                'device_token_id' => $userDeviceToken->id,
                'platform' => $platform,
                'was_newly_created' => $wasRecentlyCreated,
                'device_token_hash' => $tokenHash,
                'last_used_at' => $userDeviceToken->last_used_at?->toDateTimeString(),
                'created_at' => $userDeviceToken->created_at?->toDateTimeString(),
                'updated_at' => $userDeviceToken->updated_at?->toDateTimeString()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Device token registered successfully.',
                'data' => [
                    'device_token_id' => $userDeviceToken->id,
                    'platform' => $userDeviceToken->platform,
                    'last_used_at' => $userDeviceToken->last_used_at?->toISOString(),
                    'was_newly_created' => $wasRecentlyCreated
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error('UserDeviceTokenController: EXCEPTION - Error during device token registration', [
                'job_seeker_id' => Auth::guard('job_seeker')->id() ?? 'unknown',
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString(),
                'request_data' => $request->only(['platform']),
                'has_device_token' => $request->has('device_token'),
                'device_token_hash' => $request->has('device_token') ? UserDeviceToken::generateSecureHash($request->input('device_token')) : null
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while registering the device token. Please try again.'
            ], 500);
        }
    }

    /**
     * Get all device tokens for the authenticated user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $jobSeekerId = Auth::guard('job_seeker')->id();
            
            if (!$jobSeekerId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required.'
                ], 401);
            }

            $deviceTokens = UserDeviceToken::where('job_seeker_id', $jobSeekerId)
                ->orderBy('last_used_at', 'desc')
                ->get()
                ->map(function ($token) {
                    return [
                        'id' => $token->id,
                        'platform' => $token->platform,
                        'device_token_preview' => $token->token_preview,
                        'last_used_at' => $token->last_used_at?->toISOString(),
                        'created_at' => $token->created_at?->toISOString(),
                        'updated_at' => $token->updated_at?->toISOString()
                    ];
                });

            Log::info('UserDeviceTokenController: Retrieved device tokens for job seeker', [
                'job_seeker_id' => $jobSeekerId,
                'tokens_count' => $deviceTokens->count()
            ]);

            return response()->json([
                'success' => true,
                'data' => $deviceTokens
            ], 200);

        } catch (\Exception $e) {
            Log::error('UserDeviceTokenController: EXCEPTION - Error retrieving device tokens', [
                'job_seeker_id' => Auth::guard('job_seeker')->id() ?? 'unknown',
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving device tokens.'
            ], 500);
        }
    }

    /**
     * Delete a specific device token.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(Request $request, int $id): JsonResponse
    {
        try {
            $jobSeekerId = Auth::guard('job_seeker')->id();
            
            if (!$jobSeekerId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required.'
                ], 401);
            }

            $deviceToken = UserDeviceToken::where('id', $id)
                ->where('job_seeker_id', $jobSeekerId)
                ->first();

            if (!$deviceToken) {
                Log::warning('UserDeviceTokenController: Device token not found for deletion', [
                    'job_seeker_id' => $jobSeekerId,
                    'device_token_id' => $id
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Device token not found.'
                ], 404);
            }

            $deviceToken->delete();

            Log::info('UserDeviceTokenController: Device token deleted successfully', [
                'job_seeker_id' => $jobSeekerId,
                'device_token_id' => $id,
                'platform' => $deviceToken->platform
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Device token deleted successfully.'
            ], 200);

        } catch (\Exception $e) {
            Log::error('UserDeviceTokenController: EXCEPTION - Error deleting device token', [
                'job_seeker_id' => Auth::guard('job_seeker')->id() ?? 'unknown',
                'device_token_id' => $id,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the device token.'
            ], 500);
        }
    }
} 
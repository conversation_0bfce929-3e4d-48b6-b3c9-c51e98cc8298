<?php

namespace App;


use App\Center;
use App\Employee;
use Illuminate\Database\Eloquent\Model;

class CenterOperationPassword extends Model
{
    protected $table = 'center_operation_passwords';

    protected $fillable = [
        'center_id',
        'operation_password',
        'backup_encryption_key',
        'created_by',
        'updated_by',
        'password_history'
    ];

    protected $hidden = [
        'backup_encryption_key'
    ];

    public function center()
    {
        return $this->belongsTo(Center::class, 'center_id');
    }

    public function creator(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Employee::class, 'created_by');
    }

    /**
     * Get the backup encryption key for this center
     * If no key exists, generates and stores a new one
     */
    public function getOrCreateBackupKey(): string 
    {
        if (empty($this->backup_encryption_key)) {
            // Generate a secure random key
            $key = bin2hex(random_bytes(32)); // 64 character hex string
            
            // Store it
            $this->backup_encryption_key = $key;
            $this->save();
            
            return $key;
        }
        
        return $this->backup_encryption_key;
    }
}

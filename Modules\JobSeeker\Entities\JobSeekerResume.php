<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

/**
 * JobSeekerResume represents a single resume uploaded, imported, or recorded by a job seeker.
 * 
 * Purpose: Store resume data with deduplication and source tracking for AI tailoring.
 * Relationships: belongs to JobSeeker; used in many AI tailoring runs.
 * Business rules: max 5 per user; unique by sha256 hash per user; supports file storage or text content.
 * Sources: 'upload' (file), 'linkedin' (pasted text), 'voice' (transcribed text).
 * 
 * @property int $id
 * @property int $job_seeker_id
 * @property string $title User-provided resume title
 * @property string $source upload|linkedin|voice
 * @property string|null $storage_path Relative path for uploaded files
 * @property string|null $mime MIME type for uploaded files
 * @property int|null $size_bytes File size in bytes
 * @property string|null $content_text Full text content for linkedin/voice sources
 * @property string $sha256_hash Deduplication hash
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
final class JobSeekerResume extends Model
{
    protected $table = 'jobseeker_resumes';

    protected $fillable = [
        'job_seeker_id',
        'title',
        'source',
        'storage_path',
        'mime',
        'size_bytes',
        'content_text',
        'sha256_hash',
    ];

    protected $casts = [
        'job_seeker_id' => 'integer',
        'size_bytes' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $hidden = [
        'sha256_hash', // Don't expose hash in API responses
    ];

    /**
     * Get the job seeker who owns this resume
     */
    public function jobSeeker(): BelongsTo
    {
        return $this->belongsTo(JobSeeker::class, 'job_seeker_id');
    }

    /**
     * Get AI tailoring runs that used this resume
     */
    public function tailorRuns(): BelongsToMany
    {
        return $this->belongsToMany(
            JobSeekerAiTailorRun::class,
            'jobseeker_ai_tailor_run_resumes',
            'resume_id',
            'tailor_run_id'
        )->withPivot('position_order')->withTimestamps();
    }

    /**
     * Generate SHA256 hash for file content or text content
     * 
     * @param string $content File content or text content
     * @return string 64-character hex hash
     */
    public static function generateContentHash(string $content): string
    {
        return hash('sha256', $content);
    }

    /**
     * Check if this resume is a file-based upload
     */
    public function isFileUpload(): bool
    {
        return $this->source === 'upload' && !empty($this->storage_path);
    }

    /**
     * Check if this resume is text-based (LinkedIn or voice)
     */
    public function isTextBased(): bool
    {
        return in_array($this->source, ['linkedin', 'voice']) && !empty($this->content_text);
    }

    /**
     * Get file URL for download (only for uploaded files)
     * 
     * @return string|null Download URL or null if not a file upload
     */
    public function getFileUrl(): ?string
    {
        if (!$this->isFileUpload()) {
            return null;
        }

        try {
            return Storage::disk('local')->url($this->storage_path);
        } catch (\Exception $e) {
            Log::warning('Failed to generate resume file URL', [
                'resume_id' => $this->id,
                'storage_path' => $this->storage_path,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get formatted file size for display
     */
    public function getFormattedSizeAttribute(): string
    {
        if (!$this->size_bytes) {
            return 'Unknown size';
        }

        $bytes = $this->size_bytes;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 1) . ' ' . $units[$i];
    }

    /**
     * Get truncated content preview for display
     */
    public function getContentPreviewAttribute(): string
    {
        if ($this->isFileUpload()) {
            return "File: {$this->getFormattedSizeAttribute()}";
        }

        if ($this->content_text) {
            return \Illuminate\Support\Str::limit(strip_tags($this->content_text), 100);
        }

        return 'No content available';
    }

    /**
     * Scope to find resumes by job seeker with count limit check
     */
    public function scopeForJobSeeker($query, int $jobSeekerId)
    {
        return $query->where('job_seeker_id', $jobSeekerId)
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Scope to check if user has reached the 5-resume limit
     */
    public function scopeCountForJobSeeker($query, int $jobSeekerId): int
    {
        return $query->where('job_seeker_id', $jobSeekerId)->count();
    }

    /**
     * Delete the associated file when resume is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function (JobSeekerResume $resume) {
            if ($resume->isFileUpload()) {
                try {
                    Storage::disk('local')->delete($resume->storage_path);
                    Log::info('Deleted resume file during model deletion', [
                        'resume_id' => $resume->id,
                        'storage_path' => $resume->storage_path,
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to delete resume file during model deletion', [
                        'resume_id' => $resume->id,
                        'storage_path' => $resume->storage_path,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        });
    }
}

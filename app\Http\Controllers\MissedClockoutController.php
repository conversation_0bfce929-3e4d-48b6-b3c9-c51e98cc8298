<?php

namespace App\Http\Controllers;

use App\Attendance;
use App\MissedClockOut;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class MissedClockoutController extends Controller
{
    public function redirectFunction()
    {
        $employeeId = \Auth::guard("employee")->user()->id;
        
        try {
            // Get the most recent valid missed clockout record
            $validMissedClockout = $this->getValidMissedClockout($employeeId);
            
            if (!$validMissedClockout) {
                // No valid missed clockout found, redirect to dashboard
                Log::info("No valid missed clockout found for employee {$employeeId}, redirecting to dashboard");
                return redirect()->route('employee.dashboard');
            }
            
                         // Get the attendance record associated with this missed clockout
             $lastAttendanceClock = $validMissedClockout->clock;
             
             // Ensure we have a Carbon object, not a string
             if (is_string($lastAttendanceClock)) {
                 $lastAttendanceClock = Carbon::parse($lastAttendanceClock);
             }
             
             // If no clock time found, try to get from the related attendance record
             if (!$lastAttendanceClock && $validMissedClockout->attendance_id) {
                 $attendanceRecord = Attendance::withTrashed()->find($validMissedClockout->attendance_id);
                 if ($attendanceRecord && $attendanceRecord->clock) {
                     $lastAttendanceClock = $attendanceRecord->clock;
                     if (is_string($lastAttendanceClock)) {
                         $lastAttendanceClock = Carbon::parse($lastAttendanceClock);
                     }
                 }
             }
             
             // Fallback: get the last attendance record if still no clock found
             if (!$lastAttendanceClock) {
                 $attendanceRecord = Attendance::withTrashed()
                     ->where('employee_id', $employeeId)
                     ->latest('clock')
                     ->first();
                 if ($attendanceRecord && $attendanceRecord->clock) {
                     $lastAttendanceClock = $attendanceRecord->clock;
                     if (is_string($lastAttendanceClock)) {
                         $lastAttendanceClock = Carbon::parse($lastAttendanceClock);
                     }
                 }
             }
            
                         // Final safety check - if we still don't have a valid clock time, create a default one
             if (!$lastAttendanceClock || !($lastAttendanceClock instanceof Carbon)) {
                 $lastAttendanceClock = Carbon::now()->subDay(); // Default to yesterday
                 Log::warning("No valid clock time found for employee {$employeeId}, using default: " . $lastAttendanceClock->toDateTimeString());
             }
             
             Log::info("Showing missed clockout form for employee {$employeeId} with clock time: " . $lastAttendanceClock->toDateTimeString());
             
             return view('general::missed-clockout', compact('lastAttendanceClock'));
            
        } catch (\Exception $e) {
            Log::error("Error in MissedClockoutController::redirectFunction for employee {$employeeId}: " . $e->getMessage());
            
                         // Fallback to original logic if there's an error
             $attendanceRecord = Attendance::withTrashed()
                 ->where('employee_id', $employeeId)
                 ->get()
                 ->last();
             
                          $lastAttendanceClock = null;
             if ($attendanceRecord && $attendanceRecord->clock) {
                 $lastAttendanceClock = $attendanceRecord->clock;
                 if (is_string($lastAttendanceClock)) {
                     $lastAttendanceClock = Carbon::parse($lastAttendanceClock);
                 }
             }
             
             // Final safety check in fallback
             if (!$lastAttendanceClock || !($lastAttendanceClock instanceof Carbon)) {
                 $lastAttendanceClock = Carbon::now()->subDay(); // Default to yesterday
                 Log::warning("Fallback: No valid clock time found for employee {$employeeId}, using default: " . $lastAttendanceClock->toDateTimeString());
             }
                 
             return view('general::missed-clockout', compact('lastAttendanceClock'));
        }
    }
    
    /**
     * Get the most recent valid missed clockout record for the employee
     *
     * @param int $employeeId
     * @return MissedClockOut|null
     */
    private function getValidMissedClockout(int $employeeId): ?MissedClockOut
    {
        $missedClockouts = MissedClockOut::whereNull('deleted_at')
            ->where('employee_id', $employeeId)
            ->orderBy('clock', 'desc')
            ->get();
            
        foreach ($missedClockouts as $missedClockout) {
            if ($this->isMissedClockoutValid($missedClockout, $employeeId)) {
                return $missedClockout;
            }
        }
        
        return null;
    }
    
    /**
     * Check if a missed clockout is still valid
     *
     * @param MissedClockOut $missedClockout
     * @param int $employeeId
     * @return bool
     */
    private function isMissedClockoutValid(MissedClockOut $missedClockout, int $employeeId): bool
    {
        try {
            // Get the date of the missed clockout
            $missedDate = Carbon::parse($missedClockout->clock)->toDateString();
            
            // Check if the employee has already clocked out on that day
            $hasClockOut = Attendance::where('employee_id', $employeeId)
                ->whereDate('clock', $missedDate)
                ->where('type', 'out')
                ->exists();
                
            // If they have a clock out record for that day, this missed clockout is no longer valid
            if ($hasClockOut) {
                return false;
            }
            
            // Check if the missed clockout is from more than 3 days ago
            $missedDateTime = Carbon::parse($missedClockout->clock);
            $threeDaysAgo = Carbon::now()->subDays(3);
            
            if ($missedDateTime->lt($threeDaysAgo)) {
                return false; // Too old
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('Error validating missed clockout in controller: ' . $e->getMessage());
            return false;
        }
    }
}









<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\ContentEvaluationSchema
 *
 * @property int $id
 * @property int $content_id
 * @property int $evaluation_schema_id
 * @property string $status
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Content $content
 * @method static \Illuminate\Database\Eloquent\Builder|ContentEvaluationSchema newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContentEvaluationSchema newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContentEvaluationSchema query()
 * @method static \Illuminate\Database\Eloquent\Builder|ContentEvaluationSchema whereContentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContentEvaluationSchema whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContentEvaluationSchema whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContentEvaluationSchema whereEvaluationSchemaId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContentEvaluationSchema whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContentEvaluationSchema whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContentEvaluationSchema whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ContentEvaluationSchema extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'contents_evaluation_schemas';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['content_id', 'evaluation_schema_id', 'created_by', 'language', 'status'];


    public function content(){
        return $this->belongsTo('App\Content');
    }

    
}

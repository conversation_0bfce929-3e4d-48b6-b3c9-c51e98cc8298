<?php

namespace Modules\Education\Http\Controllers;

use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\AssignSubject;
use App\ClassRoutineUpdate;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStaatisticsRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Services\StudentImageService;

class ClassCalendarDataController extends Controller
{
    /**
     * Check if class has Nouranya program
     */
    private function hasNouranyaProgram($programTitles)
    {
        return collect($programTitles)->contains(function($title) {
            return stripos($title, 'Nouranya') !== false ||
                   stripos($title, 'Qaedah') !== false ||
                   stripos($title, 'Arabic Language') !== false;
        });
    }

    /**
     * Check if class has Memorization and Revision program
     */
    private function hasMemorizationRevisionProgram($programTitles)
    {
        return collect($programTitles)->contains(function($title) {
            return stripos($title, 'Memorization') !== false ||
                   stripos($title, 'Revision') !== false ||
                   stripos($title, 'Hefz') !== false;
        });
    }

    /**
     * Check if class has Ijazah and Sanad program
     */
    private function hasIjazahSanadProgram($programTitles)
    {
        return collect($programTitles)->contains(function($title) {
            return stripos($title, 'Ijaza') !== false ||
                   stripos($title, 'Sanad') !== false ||
                   stripos($title, 'Ijazah') !== false;
        });
    }

    public function __invoke(Request $request, $classId)
    {
        // Get the class and its associated programs with translations
        $class = Classes::with(['programs' => function($query) {
            $query->with(['programTranslations' => function($translationQuery) {
                $translationQuery->where('locale', 'en');
            }]);
        }])->find($classId);

        if (!$class) {
            return collect();
        }

        $results = collect();

        // Get date range filters from request
        $startDate = $request->get('start');
        $endDate = $request->get('end');

        // Get program titles for this class using translation trait
        $programTitles = $class->programs->map(function($program) {
            $translation = $program->programTranslations->first();
            return $translation ? $translation->title : '';
        })->filter()->toArray();

        // Debug logging (can be removed in production)
        \Log::info("ClassCalendarDataController - Class ID: $classId", [
            'program_titles' => $programTitles,
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);

        // Handle Nouranya programs
        if ($this->hasNouranyaProgram($programTitles)) {
            $nouranyaQuery = DB::table('student_nouranya_report')
                ->select(DB::raw('CONCAT("Nouranya: ", COUNT(*), " st reported") as title, date(created_at) as start'))
                ->where('class_id', $classId)
                ->where(function ($query) {
                    // Level 1: Check only 'from_lesson' and 'to_lesson'
                    $query->where(function ($level1Query) {
                        $level1Query->whereNotNull('from_lesson')
                            ->whereNotNull('to_lesson');
                    })
                        // Level 2: Check 'from_lesson', 'to_lesson', 'from_lesson_line_number', and 'to_lesson_line_number'
                        ->orWhere(function ($level2Query) {
                            $level2Query->whereNotNull('from_lesson')
                                ->whereNotNull('to_lesson')
                                ->whereNotNull('from_lesson_line_number')
                                ->whereNotNull('to_lesson_line_number');
                        })
                        // Level 3: Check 'talaqqi_from_lesson', 'talaqqi_to_lesson', 'talqeen_from_lesson', and 'talqeen_to_lesson'
                        ->orWhere(function ($level3Query) {
                            $level3Query->whereNotNull('talaqqi_from_lesson')
                                ->whereNotNull('talaqqi_to_lesson')
                                ->whereNotNull('talqeen_from_lesson')
                                ->whereNotNull('talqeen_to_lesson');
                        });
                });

            // Apply date range filters if provided
            if ($startDate) {
                $nouranyaQuery->where('created_at', '>=', $startDate . ' 00:00:00');
            }
            if ($endDate) {
                $nouranyaQuery->where('created_at', '<=', $endDate . ' 23:59:59');
            }

            $nouranya = $nouranyaQuery->groupBy(DB::raw('date(created_at)'))->get();
            $results = $results->merge($nouranya);
        }

        // Handle Memorization and Revision program
        if ($this->hasMemorizationRevisionProgram($programTitles)) {
            // Hefz (Memorization) reports
            $hefzQuery = DB::table('student_hefz_report')
                ->select(DB::raw('CONCAT("Memorization: ", COUNT(*), " st reported") as title, date(created_at) as start'))
                ->where('class_id', $classId)
                ->where(function ($query) {
                    $query->whereNotNull('hefz_from_surat')
                        ->whereNotNull('hefz_from_ayat')
                        ->whereNotNull('hefz_to_surat')
                        ->whereNotNull('hefz_to_ayat');
                });

            // Apply date range filters for hefz reports
            if ($startDate) {
                $hefzQuery->where('created_at', '>=', $startDate . ' 00:00:00');
            }
            if ($endDate) {
                $hefzQuery->where('created_at', '<=', $endDate . ' 23:59:59');
            }

            $hefzReports = $hefzQuery->groupBy(DB::raw('date(created_at)'))->get();

            // Revision reports
            $revisionQuery = DB::table('student_revision_report')
                ->select(DB::raw('CONCAT("Revision: ", COUNT(*), " st reported") as title, date(created_at) as start'))
                ->where('class_id', $classId)
                ->where(function ($query) {
                    $query->whereNotNull('revision_from_surat')
                        ->whereNotNull('revision_from_ayat')
                        ->whereNotNull('revision_to_surat')
                        ->whereNotNull('revision_to_ayat');
                });

            // Apply date range filters for revision reports
            if ($startDate) {
                $revisionQuery->where('created_at', '>=', $startDate . ' 00:00:00');
            }
            if ($endDate) {
                $revisionQuery->where('created_at', '<=', $endDate . ' 23:59:59');
            }

            $revisionReports = $revisionQuery->groupBy(DB::raw('date(created_at)'))->get();

            $results = $results->merge($hefzReports)->merge($revisionReports);
        }

        // Handle Ijazah and Sanad program
        if ($this->hasIjazahSanadProgram($programTitles)) {
            // Ijazasanad Memorization reports
            $ijazasanadMemorizationQuery = DB::table('student_ijazasanad_memorization_report')
                ->select(DB::raw('CONCAT("Ijaza Memorization: ", COUNT(*), " st reported") as title, date(created_at) as start'))
                ->where('class_id', $classId)
                ->where(function ($query) {
                    $query->where(function ($basicQuery) {
                        // Basic memorization: hefz_from_surat and hefz_to_surat
                        $basicQuery->whereNotNull('hefz_from_surat')
                            ->whereNotNull('hefz_to_surat');
                    })
                    ->orWhere(function ($advancedQuery) {
                        // Advanced: with ayat details
                        $advancedQuery->whereNotNull('hefz_from_surat')
                            ->whereNotNull('hefz_from_ayat')
                            ->whereNotNull('hefz_to_surat')
                            ->whereNotNull('hefz_to_ayat');
                    })
                    ->orWhere(function ($lessonQuery) {
                        // Lesson-based: talqeen, revision, jazariyah, or seminars
                        $lessonQuery->where(function ($talqeenQuery) {
                            $talqeenQuery->whereNotNull('talqeen_from_lesson')
                                ->whereNotNull('talqeen_to_lesson');
                        })
                        ->orWhere(function ($revisionQuery) {
                            $revisionQuery->whereNotNull('revision_from_lesson')
                                ->whereNotNull('revision_to_lesson');
                        })
                        ->orWhere(function ($jazariyahQuery) {
                            $jazariyahQuery->whereNotNull('jazariyah_from_lesson')
                                ->whereNotNull('jazariyah_to_lesson');
                        })
                        ->orWhere(function ($seminarsQuery) {
                            $seminarsQuery->whereNotNull('seminars_from_lesson')
                                ->whereNotNull('seminars_to_lesson');
                        });
                    });
                });

            // Apply date range filters for ijazasanad memorization reports
            if ($startDate) {
                $ijazasanadMemorizationQuery->where('created_at', '>=', $startDate . ' 00:00:00');
            }
            if ($endDate) {
                $ijazasanadMemorizationQuery->where('created_at', '<=', $endDate . ' 23:59:59');
            }

            $ijazasanadMemorization = $ijazasanadMemorizationQuery->groupBy(DB::raw('date(created_at)'))->get();

            // Ijazasanad Revision reports
            $ijazasanadRevisionQuery = DB::table('student_ijazasanad_revision_report')
                ->select(DB::raw('CONCAT("Ijaza Revision: ", COUNT(*), " st reported") as title, date(created_at) as start'))
                ->where('class_id', $classId)
                ->where(function ($query) {
                    $query->whereNotNull('revision_from_surat')
                        ->whereNotNull('revision_to_surat');
                });

            // Apply date range filters for ijazasanad revision reports
            if ($startDate) {
                $ijazasanadRevisionQuery->where('created_at', '>=', $startDate . ' 00:00:00');
            }
            if ($endDate) {
                $ijazasanadRevisionQuery->where('created_at', '<=', $endDate . ' 23:59:59');
            }

            $ijazasanadRevision = $ijazasanadRevisionQuery->groupBy(DB::raw('date(created_at)'))->get();

            $results = $results->merge($ijazasanadMemorization)->merge($ijazasanadRevision);
        }

        return $results;
    }

    /**
     * Get student details for a specific date and class, optionally filtered by report type
     */
    public function getStudentDetails(Request $request, $classId, $date, StudentImageService $studentImageService)
    {
        // Get report type filter from request
        $reportTypeFilter = $request->get('report_type');

        // Simple debug to verify endpoint is hit
        error_log("=== getStudentDetails called for class $classId on date $date ===");
        error_log("Request URL: " . $request->fullUrl());
        error_log("Report type filter: " . ($reportTypeFilter ?: 'none'));

        try {
            // Get the class and its associated programs
            $class = Classes::with(['programs' => function($query) {
                $query->with(['programTranslations' => function($translationQuery) {
                    $translationQuery->where('locale', 'en');
                }]);
            }])->find($classId);

            if (!$class) {
                return response()->json(['error' => 'Class not found'], 404);
            }

            // Parse the date
            $targetDate = Carbon::parse($date)->toDateString();

            // Get program titles for this class using translation trait
            $programTitles = $class->programs->map(function($program) {
                $translation = $program->programTranslations->first();
                return $translation ? $translation->title : '';
            })->filter()->toArray();

            $students = collect();

            // If report type filter is provided, only fetch that specific type
            if ($reportTypeFilter) {
                error_log("Filtering by report type: $reportTypeFilter");

                switch ($reportTypeFilter) {
                    case 'Nouranya':
                        if ($this->hasNouranyaProgram($programTitles)) {
                            $students = $this->getNouranyaStudents($classId, $targetDate);
                        }
                        break;

                    case 'Memorization':
                        if ($this->hasMemorizationRevisionProgram($programTitles)) {
                            $students = $this->getMemorizationStudents($classId, $targetDate);
                        }
                        break;

                    case 'Revision':
                        if ($this->hasMemorizationRevisionProgram($programTitles)) {
                            $students = $this->getRevisionStudents($classId, $targetDate);
                        }
                        break;

                    case 'Ijaza Memorization':
                        if ($this->hasIjazahSanadProgram($programTitles)) {
                            $students = $this->getIjazaMemorizationStudents($classId, $targetDate);
                        }
                        break;

                    case 'Ijaza Revision':
                        if ($this->hasIjazahSanadProgram($programTitles)) {
                            $students = $this->getIjazaRevisionStudents($classId, $targetDate);
                        }
                        break;

                    default:
                        error_log("Unknown report type filter: $reportTypeFilter");
                        break;
                }
            } else {
                // No filter provided, fetch all types (original behavior)
                error_log("No report type filter, fetching all types");
                $students = $this->getAllStudentsForDate($classId, $targetDate, $programTitles);
            }


            // Process the students for final output
            $groupedStudents = $this->processStudentsForOutput($students, $studentImageService, $reportTypeFilter);

            // Debug the final result
            error_log("=== Final result: " . $groupedStudents->count() . " students ===");
            foreach ($groupedStudents as $student) {
                error_log("Student: " . $student['full_name'] . " - " . $student['report_types']);
            }

            return response()->json([
                'success' => true,
                'date' => $targetDate,
                'class_id' => $classId,
                'students' => $groupedStudents
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching student details for calendar hover', [
                'class_id' => $classId,
                'date' => $date,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'Failed to fetch student details',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Nouranya students for a specific date
     */
    private function getNouranyaStudents($classId, $targetDate)
    {
        return DB::table('student_nouranya_report')
            ->join('students', 'student_nouranya_report.student_id', '=', 'students.id')
            ->leftJoin('users', 'students.user_id', '=', 'users.id')
            ->select(
                'students.id',
                'students.full_name',
                'students.email',
                'students.gender',
                'students.student_photo',
                'users.email as user_email',
                DB::raw('COUNT(*) as report_count'),
                DB::raw('"Nouranya" as report_type')
            )
            ->where('student_nouranya_report.class_id', $classId)
            ->whereDate('student_nouranya_report.created_at', $targetDate)
            ->where(function ($query) {
                $query->where(function ($level1Query) {
                    $level1Query->whereNotNull('from_lesson')
                        ->whereNotNull('to_lesson');
                })
                ->orWhere(function ($level2Query) {
                    $level2Query->whereNotNull('from_lesson')
                        ->whereNotNull('to_lesson')
                        ->whereNotNull('from_lesson_line_number')
                        ->whereNotNull('to_lesson_line_number');
                })
                ->orWhere(function ($level3Query) {
                    $level3Query->whereNotNull('talaqqi_from_lesson')
                        ->whereNotNull('talaqqi_to_lesson')
                        ->whereNotNull('talqeen_from_lesson')
                        ->whereNotNull('talqeen_to_lesson');
                });
            })
            ->groupBy('students.id', 'students.full_name', 'students.email', 'students.gender', 'students.student_photo', 'users.email')
            ->get();
    }

    /**
     * Get Memorization students for a specific date
     */
    private function getMemorizationStudents($classId, $targetDate)
    {
        return DB::table('student_hefz_report')
            ->join('students', 'student_hefz_report.student_id', '=', 'students.id')
            ->leftJoin('users', 'students.user_id', '=', 'users.id')
            ->select(
                'students.id',
                'students.full_name',
                'students.email',
                'students.gender',
                'students.student_photo',
                'users.email as user_email',
                DB::raw('COUNT(*) as report_count'),
                DB::raw('"Memorization" as report_type')
            )
            ->where('student_hefz_report.class_id', $classId)
            ->whereDate('student_hefz_report.created_at', $targetDate)
            ->where(function ($query) {
                $query->whereNotNull('hefz_from_surat')
                    ->whereNotNull('hefz_from_ayat')
                    ->whereNotNull('hefz_to_surat')
                    ->whereNotNull('hefz_to_ayat');
            })
            ->groupBy('students.id', 'students.full_name', 'students.email', 'students.gender', 'students.student_photo', 'users.email')
            ->get();
    }

    /**
     * Get Revision students for a specific date
     */
    private function getRevisionStudents($classId, $targetDate)
    {
        return DB::table('student_revision_report')
            ->join('students', 'student_revision_report.student_id', '=', 'students.id')
            ->leftJoin('users', 'students.user_id', '=', 'users.id')
            ->select(
                'students.id',
                'students.full_name',
                'students.email',
                'students.gender',
                'students.student_photo',
                'users.email as user_email',
                DB::raw('COUNT(*) as report_count'),
                DB::raw('"Revision" as report_type')
            )
            ->where('student_revision_report.class_id', $classId)
            ->whereDate('student_revision_report.created_at', $targetDate)
            ->where(function ($query) {
                $query->whereNotNull('revision_from_surat')
                    ->whereNotNull('revision_from_ayat')
                    ->whereNotNull('revision_to_surat')
                    ->whereNotNull('revision_to_ayat');
            })
            ->groupBy('students.id', 'students.full_name', 'students.email', 'students.gender', 'students.student_photo', 'users.email')
            ->get();
    }

    /**
     * Get Ijaza Memorization students for a specific date
     */
    private function getIjazaMemorizationStudents($classId, $targetDate)
    {
        return DB::table('student_ijazasanad_memorization_report')
            ->join('students', 'student_ijazasanad_memorization_report.student_id', '=', 'students.id')
            ->leftJoin('users', 'students.user_id', '=', 'users.id')
            ->select(
                'students.id',
                'students.full_name',
                'students.email',
                'students.gender',
                'students.student_photo',
                'users.email as user_email',
                DB::raw('COUNT(*) as report_count'),
                DB::raw('"Ijaza Memorization" as report_type')
            )
            ->where('student_ijazasanad_memorization_report.class_id', $classId)
            ->whereDate('student_ijazasanad_memorization_report.created_at', $targetDate)
            ->where(function ($query) {
                $query->where(function ($basicQuery) {
                    $basicQuery->whereNotNull('hefz_from_surat')
                        ->whereNotNull('hefz_to_surat');
                })
                ->orWhere(function ($advancedQuery) {
                    $advancedQuery->whereNotNull('hefz_from_surat')
                        ->whereNotNull('hefz_from_ayat')
                        ->whereNotNull('hefz_to_surat')
                        ->whereNotNull('hefz_to_ayat');
                });
            })
            ->groupBy('students.id', 'students.full_name', 'students.email', 'students.gender', 'students.student_photo', 'users.email')
            ->get();
    }

    /**
     * Get Ijaza Revision students for a specific date
     */
    private function getIjazaRevisionStudents($classId, $targetDate)
    {
        return DB::table('student_ijazasanad_revision_report')
            ->join('students', 'student_ijazasanad_revision_report.student_id', '=', 'students.id')
            ->leftJoin('users', 'students.user_id', '=', 'users.id')
            ->select(
                'students.id',
                'students.full_name',
                'students.email',
                'students.gender',
                'students.student_photo',
                'users.email as user_email',
                DB::raw('COUNT(*) as report_count'),
                DB::raw('"Ijaza Revision" as report_type')
            )
            ->where('student_ijazasanad_revision_report.class_id', $classId)
            ->whereDate('student_ijazasanad_revision_report.created_at', $targetDate)
            ->where(function ($query) {
                $query->whereNotNull('revision_from_surat')
                    ->whereNotNull('revision_to_surat');
            })
            ->groupBy('students.id', 'students.full_name', 'students.email', 'students.gender', 'students.student_photo', 'users.email')
            ->get();
    }

    /**
     * Get all students for a date (original behavior when no filter is provided)
     */
    private function getAllStudentsForDate($classId, $targetDate, $programTitles)
    {
        $students = collect();

        // Handle Nouranya programs
        if ($this->hasNouranyaProgram($programTitles)) {
            $nouranyaStudents = $this->getNouranyaStudents($classId, $targetDate);
            $students = $students->merge($nouranyaStudents);
        }

        // Handle Memorization and Revision program
        if ($this->hasMemorizationRevisionProgram($programTitles)) {
            $hefzStudents = $this->getMemorizationStudents($classId, $targetDate);
            $revisionStudents = $this->getRevisionStudents($classId, $targetDate);
            $students = $students->merge($hefzStudents)->merge($revisionStudents);
        }

        // Handle Ijazah and Sanad program
        if ($this->hasIjazahSanadProgram($programTitles)) {
            $ijazaMemStudents = $this->getIjazaMemorizationStudents($classId, $targetDate);
            $ijazaRevStudents = $this->getIjazaRevisionStudents($classId, $targetDate);
            $students = $students->merge($ijazaMemStudents)->merge($ijazaRevStudents);
        }

        return $students;
    }

    /**
     * Process students for final output format
     */
    private function processStudentsForOutput($students, $studentImageService, $reportTypeFilter = null)
    {
        // Convert to array first to avoid collection grouping issues with stdClass objects
        $studentsArray = $students->toArray();
        $groupedStudentsArray = [];

        error_log("Processing " . count($studentsArray) . " student records");

        // Group students manually to ensure proper handling
        foreach ($studentsArray as $student) {
            $studentId = $student->id;

            error_log("Processing student ID: $studentId, Name: " . $student->full_name . ", Report Type: " . $student->report_type);

            if (!isset($groupedStudentsArray[$studentId])) {
                $groupedStudentsArray[$studentId] = [
                    'student_data' => $student,
                    'report_types' => [],
                    'total_reports' => 0
                ];
            }

            // Add this report type and count
            $reportType = $student->report_type;
            $reportCount = (int)$student->report_count;

            if (!isset($groupedStudentsArray[$studentId]['report_types'][$reportType])) {
                $groupedStudentsArray[$studentId]['report_types'][$reportType] = 0;
            }
            $groupedStudentsArray[$studentId]['report_types'][$reportType] += $reportCount;
            $groupedStudentsArray[$studentId]['total_reports'] += $reportCount;
        }

        error_log("Grouped into " . count($groupedStudentsArray) . " unique students");

        // Convert back to the expected format
        $groupedStudents = collect();
        foreach ($groupedStudentsArray as $studentId => $groupedData) {
            $student = $groupedData['student_data'];

            // Format report types - if filtering by specific type, don't show type labels
            $reportTypesString = '';
            if ($reportTypeFilter) {
                // When filtering by specific report type, don't show redundant labels
                $reportTypesString = $reportTypeFilter;
            } else {
                // When showing all types, format with counts
                $reportTypesFormatted = [];
                foreach ($groupedData['report_types'] as $type => $count) {
                    $reportTypesFormatted[] = $type . ' (' . $count . ')';
                }
                $reportTypesString = implode(', ', $reportTypesFormatted);
            }

            $groupedStudents->push([
                'id' => $student->id,
                'full_name' => $student->full_name,
                'email' => $student->user_email ?: $student->email,
                'image_url' => $studentImageService->getStudentImageUrl($student->id),
                'report_types' => $reportTypesString,
                'total_reports' => $groupedData['total_reports'],
                'gender' => $student->gender
            ]);
        }

        return $groupedStudents;
    }
}

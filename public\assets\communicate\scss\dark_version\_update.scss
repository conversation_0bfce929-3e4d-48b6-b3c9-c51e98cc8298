/*********************************************
************* custom css {update} ************
*********************************************/
$extra_big: 'only screen and (min-width: 1440px) and (max-width: 1658px)';
$big_screen:'only screen and (min-width: 1200px) and (max-width: 1440px)';
$medium : 'only screen and (min-width: 992px) and (max-width: 1200px)';
$tab:'only screen and (min-width: 768px) and (max-width: 991px)';
$large: 'only screen and (min-width: 576px) and (max-width: 767px)';
$all_tab:'(max-width: 991px)';
$all_bg_tab:'(min-width: 991px)';
$small:'(max-width: 575px)';
$white: #fff;
$btn_bg: #000;
.mt-0{
    margin-top: 0px !important;
}
input:-internal-autofill-selected {
    color: $white !important;
}
::placeholder{
    color: $white !important;
}
.update_menu{
    .navbar .navbar-toggler {
        color: #ffffff;
        font-size: 20px;
        border: 1px solid #fff;
        border-radius: 0;
        padding: 7px 10px;
    }
    .ti-menu:before {
        content: "\e6c1";
        font-size: 20px;
        color: #fff;
    }
    .search-bar{
        .input-group{
            max-width: 305px;
            float: right;
        }
        input{
            padding-bottom: 6px;
            padding-left: 40px;
            ::placeholder{
                font-size: 12px;
                font-weight: 400;
                line-height: 14px;
            }
        }
        span{
            font-size: 20px;
            background-color: rgb(65, 80, 148);
            padding-top: 4px;
        }
    }
    @media #{$all_tab}{
        height: 80px;
        position: relative;
        padding: 10px !important;
        .light_logo{
            max-width: 100px;
        }
        .menu_nav {
            background-color: #415094;
        }
        .navbar .nav .nav-item .nav-link {
            padding: 10px 10px !important;
        }
    }
}
.academic-img{
    img{
        width: 100%;
    }
}
.client .header-area .navbar .search-bar input:focus {
    color: #828bb2 !important;
    font-weight: 300;
}
@media #{$all_tab}{
    .client.light .header-area .navbar .nav .nav-item .nav-link, .client.color .header-area .navbar .nav .nav-item .nav-link {
        color: $white !important;
    }
    .single-testimonial {
        padding-bottom: 0 !important;
    }
    .client .news-area {
        margin-bottom: 0;
    }
    .client .mapBox {
        height: 350px;
    }
    .client .section-gap-top {
        padding-top: 40px;
    }
    .contact_area {
        margin-bottom: 40px;
    }
    .client .banner-area {
        margin-top: 85px;
    }
    .events-area{
        .date{
            line-height: 18px; 
        }
    }
} 

@media #{$medium}{
    .update_menu{
        .update_menu .menu_nav {
            margin-left: 10% !important;
        }  
        .navbar-brand{
            img{
                max-width: 100px;
            }
        }
    }
    
}

@media #{$all_bg_tab}{
    .update_menu{
        .menu_nav{
            margin-left: 25%;
        }
        .btn-dark:hover {
            color: #fff;
            background-color: #23272b;
            border-color: transparent;
        }
    }
}
.login-area table td {
    padding: 0px 7px 0px 7px;
    width: 25% !important;
    @media #{$small}{
        width: 50% !important;
        display: inline-block;
    }
    @media #{$large}{
        width: 50% !important;
        display: inline-block;
    }
    .get-login-access{
        padding: 5% 10%;
        color: #415094;
        letter-spacing: 1px;
        font-family: "Poppins", sans-serif;
        font-size: 12px;
        outline: none !important;
        text-align: center;
        cursor: pointer;
        text-transform: uppercase;
        border: 0;
        border-radius: 5px;
        overflow: hidden;
        -webkit-transition: all 0.4s ease 0s;
        -moz-transition: all 0.4s ease 0s;
        -o-transition: all 0.4s ease 0s;
        transition: all 0.4s ease 0s;
        background-color: #ffffff;
        &:hover{
            background-color: $btn_bg;
            color: #ffffff;        
        }
    }
}
/************************************************
***************** login css *********************/
.login.admin.hight_100{
    .login-height {
        .input-group-addon{
            width: 0;
        }
        .form-group i {
            top: 7px;
            left: 4px;
        }
    }
    @media #{$small}{
        .login-height {
            .input-group-addon{
                width: 0;
            }
            .form-group i {
                top: 7px;
                left: 4px;
            }
            .form-wrap {
                padding: 50px 8px;
            }
            a{
                font-size: 12px;
            }
        }
    }
    @media #{$large}{
        .login-height {
            .input-group-addon{
                width: 0;
            }
            .form-group i {
                top: 7px;
                left: 4px;
            }
        }
    }
    @media #{$all_tab}{
        height: 100% !important; 
        overflow: visible;
        .login-height {
            .input-group-addon{
                width: 0;
            }
            .form-group i {
                top: 7px;
                left: 4px;
            }
        }
    }    
}
.hight_100{
    height: 100vh;
    @media #{$all_tab}{
        height: 100% !important; 
    }
    @media #{$big_screen}{
        height: 100% !important; 
    }   
    @media #{$medium}{
        height: 100% !important; 
    }  
}
@media #{$tab}{
    .login-area .login-height {
        min-height: auto;
    }
    .login-height{
        margin: 50px 0;
    }
}

/******************************************************/
/**************** dashboard css ******************/
/******************************************************/

//dashboard menu css
.main-title{
    @media #{$all_tab}{
        margin-top: 20px;
    } 
}
.white-box.single-summery{
    margin-top: 20px;
    @media #{$all_tab}{
        margin-top: 15px;
    } 
    @media #{$small}{
        padding: 10px 15px;
        h3{
            margin-bottom: 0;
        }
        .d-flex {
            display: block !important;
        }
    }
}
.nav_icon{
    @media #{$all_tab}{
        background: $btn_bg !important;
        border: 1px solid $btn_bg;
        i{
            font-size: 24px;
            padding: 4px 0px 0px;
            display: inline-block;
        
        }
        .ti-more{
            padding: 6px 0 0;
        }
    }
}
//dashboard sidebar css
#sidebar{
    @media #{$small}{
        max-width: 80%;
        margin-left: -80%;
        min-width: 80%;
        z-index: 9999 !important;
    }
}
#sidebar.active {
    z-index: 99999;
}
#close_sidebar{
    cursor: pointer;
}
.admin .navbar {
    @media #{$small}{
        z-index: 999;
    }
}
.update_sidebar{
    display: flex;
    justify-content: space-between;
    img{
        max-width: 100px !important;
    }
    i{
        font-size: 15px;
        color: #fff;
        background: $btn_bg !important;
        border: 1px solid $btn_bg;
        display: inline-block;
        height: 40px;
        width: 40px;
        text-align: center;
        line-height: 40px;
        border-radius: 5px;
    }
    .close_sidebar{
        display: none;
    }
}
.up_dashboard{
    @media #{$all_tab}{
        .main-title{
            h3{
                margin-top: 20px;
                line-height: 25px;
            }
        }
    }
}
.up_dash_menu{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    @media #{$all_tab}{
        width: auto;
    }
    @media #{$large}{
        width: 97%;
    }
    @media #{$tab}{
        width: 100%;
    }
    ul.nav.navbar-nav.mr-auto.nav-buttons{
        margin: 0 auto !important;
        text-align: center !important;
        @media #{$small}{
            text-align: left !important;
        }
    }
}
.btn-dark:not(:disabled):not(.disabled).active:focus,
.btn-dark:not(:disabled):not(.disabled):active:focus,
.show>.btn-dark.dropdown-toggle:focus {
    box-shadow: none;
}
.btn-dark:not(:disabled):not(.disabled).active,
.btn-dark:not(:disabled):not(.disabled):active,
.show>.btn-dark.dropdown-toggle {
    color: #fff;
    border-color: transparent
}
.search-bar {
    @media #{$all_tab}{
        margin-bottom: 0;
        padding-left: 18%;
    }
    @media #{$small}{
        padding-left: 0%;
        width: 58%;
        margin: 0 auto;
        text-align: center;
    }
    @media #{$large}{
        padding-left: 6%;
    } 
    @media #{$tab}{
        padding-left: 11%;
    }
    margin: 0 auto;
    text-align: center;
}
@media #{$all_tab}{
    .up_navbar{
        width: 97% !important;
        .btn-dark:hover, .btn-dark:focus {
            border-color: transparent;
            outline: 0;
            box-shadow: none;
        }
    }
    .up_dash_menu{
        .navbar-collapse {
            margin-top: 10px;
            padding: 30px;
            background-color: $btn_bg;
            position: absolute;
            width: 97%;
            top: 42px;
        }
    }
}
@media #{$small}{
    .up_navbar{
        width: 91% !important;
        .col-lg-12{
            padding-left: 0;
            padding-right: 0;
        }
    }
    .up_dash_menu{
        .navbar-collapse {
            width: 100%;
        }
    }
}
@media #{$large}{
    .up_navbar{
        width: 91% !important;
        .col-lg-12{
            padding-left: 0;
            padding-right: 0;
        }
    }
    .up_dash_menu{
        .navbar-collapse {
            width: 100%;
        }
    }
}
.up_ds_margin{
    @media #{$all_tab}{
        margin-bottom: 15px;
    }
    .ti-close{
        line-height: 30px;
    }
}
.up_buttom{
    display: flex;
    justify-content: space-between;
}
.up_toList{
    margin-bottom: 9px;
    @media #{$medium}{
        .text-right {
            text-align: right !important;
        }
    } 
    @media #{$all_tab}{
        margin-top: 20px;
        .main-title {
            margin-top: 0;
        }
    }

}

//breadcrumb css
.up_breadcrumb{
    @media #{$all_tab}{
        margin: 40px 0 20px;
    }
}

.up_admin_visitor{
    @media #{$all_tab}{
        .dataTables_filter > label{
            left: 47%;
            min-width: 280px;
            position: relative;
            top: -8px;        
        }
        div.dt-buttons{
            // bottom: 0;
            // text-align: center;
            // margin-bottom: 20px;
            display: none;
        }
        .main-title {
            margin: 40px 0 20px;
        }
    }
    @media #{$large}{
        .dataTables_filter > label{
            left: 1%;      
        }
    }
    @media #{$tab}{
        .dataTables_filter > label{
            left: -12%;      
        }
        
    }
    @media #{$medium}{
        .dataTables_wrapper .dataTables_filter input{
            width: 70%;
        }
        .dataTables_filter > label{
            left: 47%;   
        }
    }
}
.up_st_admin_visitor{
    @media #{$all_tab}{
        .dataTables_filter > label{
            left: 47%;
            min-width: 280px;
            position: relative;
            top: -8px;        
        }
        div.dt-buttons{
            // bottom: 0;
            // text-align: center;
            // margin-bottom: 20px;
            display: none;
        }
        .main-title {
            margin: 40px 0 20px;
        }
    }
    @media #{$large}{
        .dataTables_filter > label{
            left: 1%;      
        }
    }
    @media #{$tab}{
        .dataTables_filter > label{
            left: -12%;      
        }
        
    }
    @media #{$medium}{
        .dataTables_wrapper .dataTables_filter input{
            width: 70%;
        }
        .dataTables_filter > label {
            margin-bottom: 20px;
            position: relative;
            top: 0px;
            left: 20%;
            transform: translateX(-50%);
            min-width: 280px;
            border-bottom: 1px solid rgba(130, 139, 178, 0.4);
            margin-top: 20px;
        }
    }
    
}
.sms-breadcrumb{
    @media #{$small}{
        margin: 40px 0 20px;
    }
}
.fc-state-active, .fc-state-down {
    background-color: #ccc;
    background-image: none;
    box-shadow: none;
}
.main-title {
    @media #{$all_tab}{
        margin-top: 0;
    }
}
.fc .fc-button-group > * {
    float: left;
    margin: 0 0 10px 10px;
    border-radius: 30px;
    padding: 0px 8px;
}
.sms-breadcrumb{
    @media #{$all_tab}{
        margin: 30px 0 20px;
    }
}
.mb-40.up_dashboard {
    @media #{$all_tab}{
        margin-bottom: 20px;
    }
}

@media #{$small}{
    .fc-toolbar.fc-header-toolbar {
        .fc-left, .fc-right, .fc-center{
            display: block;
            width: 100%;
            text-align: center;
        }
    }
}
@media #{$all_tab}{
    .mt-40{
        margin-top: 15px;
    }
    .mb-30-lg {
        margin-bottom: 0;
    }
    .student-details{
        margin-top: 50px;
    }
}
.search_bar {
	position: absolute;
	margin: auto;
	top: 0;
	right: 0;
	bottom: 0;
	width: auto;
	height: 100px;
	z-index: 999;

	.search {
		position: absolute;
		margin: auto;
		top: 0;
		right: 0;
		bottom: 0;
		width: 40px;
		height: 40px;
		transition: all .5s;
		z-index: 4;
		font-size: 18px;

		&:hover {
			cursor: pointer;
		}

		&::before {
			content: "\e610";
			position: absolute;
			margin: auto;
			top: 10px;
			right: 15px;
			bottom: 0;
			width: 6px;
			transition: all .5s;
			font-family: 'themify';
		}
	}

	input {
		position: absolute;
        margin: auto;
        top: 25px;
        right: 0;
        width: 0px;
        height: 50px;
        outline: none;
        border: none;
		z-index: 99;
		// border-bottom: 1px solid rgba(255, 255, 255, 0.2);
		background: #fe005f;
		color: white;
		padding: 10px;
		transition: all .5s;
		opacity: 0;
		z-index: 5;
		font-weight: bolder;
		letter-spacing: 0.1em;
		&:hover {
			cursor: pointer;
		}

		&:focus {
			width: 280px;
			opacity: 1;
			cursor: text;
			padding-left: 15px;
		}

		&:focus~.search {
			right: 5px;
			background: #fff;
			z-index: 6;
			padding: 0 20px 0 20px;
			&::before {
				top: 8px;
                right: 20px;
				content: "\e646";
				font-family: 'themify';
				
			}
		}

		&::placeholder {
			color: white;
			opacity: 1;
			font-weight: bolder;
		}
	}
}
.empty_table_tab{
    .nav-link{
        background: #cad5f3;
        color: #2c7be5;
        border: 0;
        font-size: 12px;
        text-transform: uppercase;
        font-weight: 500;
        padding: 8px 25px;
        margin-right: 10px;
        border-radius: 0px;
    }
    .tab-content{
        width: 100%;
        display: block;
    }
    .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
        color: #2c7be5 !important;
    }
}

<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class HrPayrollGenerate extends Model
{
    public function staffs(){
		return $this->belongsTo('App\Employee', 'employee_id', 'id');
	}

	public static function getPayrollDetails($employee_id, $payroll_month, $payroll_year){
		try {
			$getPayrollDetails = HrPayrollGenerate::select('id','payroll_status')
								->where('employee_id', $employee_id)
								->where('payroll_month', $payroll_month)
								->where('payroll_year', $payroll_year)
								->first();

			if(isset($getPayrollDetails)){
				return $getPayrollDetails;
			}
			else{
				return false;
			}
		} catch (\Exception $e) {
			return false;
		}
    }

	public function staffDetails(){
		return $this->belongsTo('App\Employee', 'employee_id', 'id');
	}

	public static function getPaymentMode($id){
		
		try {
			$getPayrollDetails = PaymentMethhod::select('method')
									->where('id', $id)
									->first();
				if(isset($getPayrollDetails)){
					return $getPayrollDetails->method;
				}
				else{
					return false;
				}
		} catch (\Exception $e) {
			return false;
		}
	}

	public function paymentMethods(){
		return $this->belongsTo('App\PaymentMethhod', 'payment_mode', 'id');
	}

}

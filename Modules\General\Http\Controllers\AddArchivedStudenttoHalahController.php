<?php

namespace Modules\General\Http\Controllers;

use App\ClassStudent;
use App\Student;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Role;
use App\Permission;
use App\Authorizable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Validation\Rule;
use Modules\RolePermission\Entities\PermissionAssign;


class AddArchivedStudenttoHalahController extends Controller
{



    public function __invoke(Request $request)
    {

        $rule =  Rule::unique('class_students')->where(function ($query) use ($request) {
            return $query->where('student_id',$request->student_id)->where('class_id',$request->class_id);
        });

        $validator = $this->validate($request,
            ['halaqah' => 'required','student_id' =>
                [
                    'required']]);


//        if (!$validator->passes()) {
//            return response()->json(['error'=>$validator->errors()->all()],422 );
//        }

        try {
            \DB::beginTransaction();


            dd($request->student_id);

            // restore student
             Student::find($request->student_id)->withTrashed()->restore();


        $class_student = ClassStudent::updateOrInsert(
            [
                'student_id' => $request->student_id,
                'class_id'   => $request->halaqah],
            [
                'start_date'   => Carbon::now()->toDateString(),
                'created_at'   => Carbon::now()
            ]);
            \DB::commit();


        return response()->json(['message' => 'successfully added the student to this Halaqah'],201);



        } catch (\Exception $exception) {
            \Log::error($exception);

            \DB::rollBack();
            $errorMessage = $exception->getMessage();
            return response()->json(['message' => $exception->getMessage()]);
        }

    }



}

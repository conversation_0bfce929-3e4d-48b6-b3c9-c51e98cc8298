<?php
/**
 * Database Documentation Generator
 * 
 * This script connects to the database using <PERSON><PERSON>'s environment variables
 * and generates comprehensive documentation of all tables, columns, and relationships.
 * The documentation is saved as an HTML file.
 */

// Incorporate Laravel environment variables
require_once __DIR__ . '/../../vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../../');
$dotenv->load();

// Set up database connection parameters from Laravel .env
$host = $_ENV['DB_HOST'];
$database = $_ENV['DB_DATABASE'];
$username = $_ENV['DB_USERNAME'];
$password = $_ENV['DB_PASSWORD'];
$port = $_ENV['DB_PORT'] ?? 3306;

echo "Connecting to database: {$database} on {$host}...\n";

try {
    // Connect to database
    $pdo = new PDO(
        "mysql:host={$host};port={$port};dbname={$database}",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "Connection successful.\n";
    echo "Fetching table information...\n";
    
    // Get all tables
    $stmt = $pdo->query('SHOW TABLES');
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Found " . count($tables) . " tables.\n";
    
    // Store all table definitions
    $tableDefinitions = [];
    
    // Process each table
    foreach ($tables as $table) {
        echo "Processing table: {$table}...\n";
        
        $tableDefinition = [];
        $tableDefinition['name'] = $table;
        
        // Get columns details
        $columnsStmt = $pdo->query("SHOW FULL COLUMNS FROM `{$table}`");
        $tableDefinition['columns'] = $columnsStmt->fetchAll();
        
        // Get primary keys
        $primaryKeysStmt = $pdo->query("SHOW KEYS FROM `{$table}` WHERE Key_name = 'PRIMARY'");
        $tableDefinition['primary_keys'] = $primaryKeysStmt->fetchAll();
        
        // Get create table statement
        $createTableStmt = $pdo->query("SHOW CREATE TABLE `{$table}`");
        $createTable = $createTableStmt->fetch();
        $tableDefinition['create_table'] = $createTable['Create Table'] ?? '';
        
        // Extract table engine and collation from create table statement
        if (preg_match('/ENGINE=(\w+)/', $tableDefinition['create_table'], $engineMatches)) {
            $tableDefinition['engine'] = $engineMatches[1];
        }
        
        if (preg_match('/CHARSET=(\w+)/', $tableDefinition['create_table'], $charsetMatches)) {
            $tableDefinition['charset'] = $charsetMatches[1];
        }
        
        if (preg_match('/COLLATE=(\w+)/', $tableDefinition['create_table'], $collateMatches)) {
            $tableDefinition['collation'] = $collateMatches[1];
        }
        
        // Get foreign keys
        $stmt = $pdo->prepare("
            SELECT 
                COLUMN_NAME as column_name,
                REFERENCED_TABLE_NAME as referenced_table,
                REFERENCED_COLUMN_NAME as referenced_column,
                CONSTRAINT_NAME as constraint_name
            FROM 
                INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE 
                TABLE_SCHEMA = :database
                AND TABLE_NAME = :table
                AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        
        $stmt->execute([
            ':database' => $database,
            ':table' => $table
        ]);
        
        $tableDefinition['foreign_keys'] = $stmt->fetchAll();
        
        // Add to array of tables
        $tableDefinitions[$table] = $tableDefinition;
    }
    
    // Find reverse relationships (tables referencing this table)
    foreach ($tableDefinitions as $tableName => &$tableDefinition) {
        $tableDefinition['referenced_by'] = [];
        
        foreach ($tableDefinitions as $otherTable => $otherTableDefinition) {
            if ($otherTable === $tableName) continue;
            
            foreach ($otherTableDefinition['foreign_keys'] as $foreignKey) {
                if ($foreignKey['referenced_table'] === $tableName) {
                    $tableDefinition['referenced_by'][] = [
                        'table' => $otherTable,
                        'column' => $foreignKey['column_name'],
                        'references_column' => $foreignKey['referenced_column'],
                        'constraint_name' => $foreignKey['constraint_name']
                    ];
                }
            }
        }
    }
    
    echo "Generating HTML documentation...\n";
    
    // Generate HTML output
    $tablesHtml = '';
    
    // Sort tables alphabetically
    ksort($tableDefinitions);
    
    foreach ($tableDefinitions as $tableName => $tableDefinition) {
        $tablesHtml .= '<div class="table-container" id="table-' . htmlspecialchars($tableName) . '">' . PHP_EOL;
        $tablesHtml .= '    <h3>' . htmlspecialchars($tableName) . '</h3>' . PHP_EOL;
        
        // Table details
        $tablesHtml .= '    <p><strong>Engine:</strong> ' . htmlspecialchars($tableDefinition['engine'] ?? 'Unknown');
        $tablesHtml .= ' | <strong>Charset:</strong> ' . htmlspecialchars($tableDefinition['charset'] ?? 'Unknown');
        $tablesHtml .= ' | <strong>Collation:</strong> ' . htmlspecialchars($tableDefinition['collation'] ?? 'Unknown') . '</p>' . PHP_EOL;
        
        // Create array of primary key column names for easy checking
        $primaryKeyColumns = [];
        foreach ($tableDefinition['primary_keys'] as $primaryKey) {
            $primaryKeyColumns[] = $primaryKey['Column_name'];
        }
        
        // Create array of foreign key column names for easy checking
        $foreignKeyColumns = [];
        foreach ($tableDefinition['foreign_keys'] as $foreignKey) {
            $foreignKeyColumns[] = $foreignKey['column_name'];
        }
        
        // Table columns
        $tablesHtml .= '    <h4>Columns</h4>' . PHP_EOL;
        $tablesHtml .= '    <table>' . PHP_EOL;
        $tablesHtml .= '        <thead>' . PHP_EOL;
        $tablesHtml .= '            <tr>' . PHP_EOL;
        $tablesHtml .= '                <th>Column Name</th>' . PHP_EOL;
        $tablesHtml .= '                <th>Type</th>' . PHP_EOL;
        $tablesHtml .= '                <th>Nullable</th>' . PHP_EOL;
        $tablesHtml .= '                <th>Default</th>' . PHP_EOL;
        $tablesHtml .= '                <th>Extra</th>' . PHP_EOL;
        $tablesHtml .= '                <th>Comment</th>' . PHP_EOL;
        $tablesHtml .= '            </tr>' . PHP_EOL;
        $tablesHtml .= '        </thead>' . PHP_EOL;
        $tablesHtml .= '        <tbody>' . PHP_EOL;
        
        foreach ($tableDefinition['columns'] as $column) {
            $columnClass = '';
            if (in_array($column['Field'], $primaryKeyColumns)) {
                $columnClass = 'primary-key';
            } elseif (in_array($column['Field'], $foreignKeyColumns)) {
                $columnClass = 'foreign-key';
            }
            
            $tablesHtml .= '            <tr>' . PHP_EOL;
            $tablesHtml .= '                <td class="' . $columnClass . '">' . htmlspecialchars($column['Field']) . '</td>' . PHP_EOL;
            $tablesHtml .= '                <td>' . htmlspecialchars($column['Type']) . '</td>' . PHP_EOL;
            $tablesHtml .= '                <td>' . htmlspecialchars($column['Null']) . '</td>' . PHP_EOL;
            $tablesHtml .= '                <td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>' . PHP_EOL;
            $tablesHtml .= '                <td>' . htmlspecialchars($column['Extra']) . '</td>' . PHP_EOL;
            $tablesHtml .= '                <td>' . htmlspecialchars($column['Comment']) . '</td>' . PHP_EOL;
            $tablesHtml .= '            </tr>' . PHP_EOL;
        }
        
        $tablesHtml .= '        </tbody>' . PHP_EOL;
        $tablesHtml .= '    </table>' . PHP_EOL;
        
        // Foreign keys
        if (!empty($tableDefinition['foreign_keys'])) {
            $tablesHtml .= '    <h4>Foreign Keys</h4>' . PHP_EOL;
            $tablesHtml .= '    <ul>' . PHP_EOL;
            
            foreach ($tableDefinition['foreign_keys'] as $foreignKey) {
                $tablesHtml .= '        <li class="fk-relationship">' . PHP_EOL;
                $tablesHtml .= '            <strong class="foreign-key">' . htmlspecialchars($foreignKey['column_name']) . '</strong> → ';
                $tablesHtml .= '<a href="#table-' . htmlspecialchars($foreignKey['referenced_table']) . '">';
                $tablesHtml .= htmlspecialchars($foreignKey['referenced_table']) . '</a>.<strong>' . htmlspecialchars($foreignKey['referenced_column']) . '</strong>';
                $tablesHtml .= ' (Constraint: ' . htmlspecialchars($foreignKey['constraint_name']) . ')' . PHP_EOL;
                $tablesHtml .= '        </li>' . PHP_EOL;
            }
            
            $tablesHtml .= '    </ul>' . PHP_EOL;
        }
        
        // Referenced by other tables
        if (!empty($tableDefinition['referenced_by'])) {
            $tablesHtml .= '    <h4>Referenced By</h4>' . PHP_EOL;
            $tablesHtml .= '    <ul>' . PHP_EOL;
            
            foreach ($tableDefinition['referenced_by'] as $reference) {
                $tablesHtml .= '        <li class="fk-relationship">' . PHP_EOL;
                $tablesHtml .= '            <a href="#table-' . htmlspecialchars($reference['table']) . '">';
                $tablesHtml .= htmlspecialchars($reference['table']) . '</a>.<strong>' . htmlspecialchars($reference['column']) . '</strong>';
                $tablesHtml .= ' → <strong class="primary-key">' . htmlspecialchars($reference['references_column']) . '</strong>';
                $tablesHtml .= ' (Constraint: ' . htmlspecialchars($reference['constraint_name']) . ')' . PHP_EOL;
                $tablesHtml .= '        </li>' . PHP_EOL;
            }
            
            $tablesHtml .= '    </ul>' . PHP_EOL;
        }
        
        $tablesHtml .= '</div>' . PHP_EOL;
    }
    
    // Load the template
    $templatePath = __DIR__ . '/complete_database_documentation.html';
    $template = file_get_contents($templatePath);
    
    // Replace placeholder with table definitions
    // Look for the area between the <h2>Table Definitions</h2> and the closing </div>
    $output = preg_replace(
        '/<h2>Table Definitions<\/h2>\s*\n*\s*<!-- TABLES_PLACEHOLDER -->\s*\n*\s*<\/div>/',
        '<h2>Table Definitions</h2>' . PHP_EOL . $tablesHtml . PHP_EOL . '</div>',
        $template
    );
    
    // If the regex replacement didn't work, try a simpler approach
    if ($output === $template) {
        $output = str_replace('<!-- TABLES_PLACEHOLDER -->', $tablesHtml, $template);
    }
    
    // Save to file
    $outputPath = __DIR__ . '/complete_database_documentation_with_tables.html';
    file_put_contents($outputPath, $output);
    
    echo "Documentation successfully generated at: {$outputPath}\n";
    
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1); 
} 
@extends('layouts.hound')

@section('mytitle', 'Create Leave Request')

@section('content')
<div class="container">
    <div class="panel panel-default card-view">
        <div class="panel-heading">
            <div class="pull-left">
                <h6 class="panel-title txt-dark">{{ trans('common.create') }}  Leave Request</h6>
            </div>
            <a href="{{ route('leave-requests.index') }}" class="pull-right" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
            <div class="clearfix"></div>
        </div>
        <div class="panel-wrapper collapse in">
            @if ($errors->any())
                <ul class="alert alert-danger">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            @endif

            {!! Form::open(['route' => 'leave-requests.store']) !!}

            <div class="row"> 
                <div class="col-md-12">
                    <!-- Name Form Input -->
                    <div class="form-group @if ($errors->has('employee_id')) has-error @endif">
                        {!! Form::label('employee_id', 'Employee') !!}
                        {!! Form::select('employee_id', $employees , null, ['class' => 'form-control select2', 'placeholder' => 'Employee Name']) !!}
                        @if ($errors->has('employee_id')) <p class="help-block">{{ $errors->first('employee_id') }}</p> @endif
                    </div>

                    <!-- type Form Input -->
                    <div class="form-group @if ($errors->has('type')) has-error @endif">
                        {!! Form::label('type', 'Type') !!}
                        {!! Form::select('type', [ 'medical' => 'medical leave'] , null, ['class' => 'form-control']) !!}
                        @if ($errors->has('type')) <p class="help-block">{{ $errors->first('type') }}</p> @endif
                    </div>

                    
                    <div class="row">
                        <!-- from_date Form Input -->
                        <div class="form-group @if ($errors->has('from_date')) has-error @endif col-md-6">
                            {!! Form::label('from_date', 'From') !!}
                            {!! Form::text('from_date', null, ['class' => 'form-control datetime']) !!}
                            @if ($errors->has('from_date')) <p class="help-block">{{ $errors->first('from_date') }}</p> @endif
                        </div>

                        <!-- to_date Form Input -->
                        <div class="form-group @if ($errors->has('to_date')) has-error @endif col-md-6">
                            {!! Form::label('to_date', 'To') !!}
                            {!! Form::text('to_date', null, ['class' => 'form-control datetime']) !!}
                            @if ($errors->has('to_date')) <p class="help-block">{{ $errors->first('to_date') }}</p> @endif
                        </div>
                    </div>
                        

                    <!-- details Form Input -->
                    <div class="form-group @if ($errors->has('details')) has-error @endif">
                        {!! Form::label('details', 'Details') !!}
                        {!! Form::textarea('details', null, ['class' => 'form-control']) !!}
                        @if ($errors->has('details')) <p class="help-block">{{ $errors->first('details') }}</p> @endif
                    </div>
                    <div class="text-center">
                            {!! Form::submit(trans('common.create'), ['class' => 'btn btn-primary']) !!}
                    </div>
                    

                </div>

            </div>

            {!! Form::close() !!}

        </div>
    </div>
</div>
@endsection


@include('jssnippets.select2')

@include('jssnippets.flatpickr')

@section('js')
<script>
    $('document').ready(function(){
        flatpickr('#from_date', {
            enableTime: true,
            minDate: "today",
            defaultHour: 9
        });
        flatpickr('#to_date', {
            enableTime: true,
            minDate: "today",
            defaultHour: 18
        });
        $('.select2').select2();        
    })
</script>
@append
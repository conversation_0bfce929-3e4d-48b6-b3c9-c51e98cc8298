<?php

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\AdmissionInterview;
use App\Attendance;
use App\Http\Controllers\Controller;
use App\StudentHefzPlan;
use App\StudentLastApprovedPlan;
use App\StudentLastApprovedNouranyaPlan;
use App\StudentNouranyaPlan;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;
use App\Student;

class AttendanceController extends Controller
{

    /**
     * Returns the attendance data for the last seven days, including daily hours and total hours.
     *
     * @return \Illuminate\Http\JsonResponse
     */
//    public function lastSevenDaysAttendance()
//    {
//
//        $startDate = Carbon::today()->subDays(6); // Start date is 7 days ago including today
//        $endDate = Carbon::today(); // End date is today
//
//        // Fetch all 'in' and 'out' records for the last seven days, grouped by employee and date
//        $attendances = Attendance::select('employee_id', 'clock', 'type')
//            ->whereBetween('clock', [$startDate, $endDate->endOfDay()])
//            ->orderBy('employee_id')
//            ->orderBy('clock')
//            ->get();
//
//        $dailyHours = [];
//        $currentDay = $startDate->copy();
//
//        // Initialize dailyHours array
//        while ($currentDay->lessThanOrEqualTo($endDate)) {
//            $dailyHours[$currentDay->toDateString()] = 0;
//            $currentDay->addDay();
//        }
//
//        $lastInTime = null;
//
//        // Process the attendance records to calculate total hours
//        foreach ($attendances as $attendance) {
//            if ($attendance->type == 'in') {
//                $lastInTime = new \DateTime($attendance->clock);
//            } elseif ($attendance->type == 'out' && $lastInTime) {
//                $timeOut = new \DateTime($attendance->clock);
//                $diff = $lastInTime->diff($timeOut);
//                $totalMinutes = ($diff->h * 60) + $diff->i;
//                $hours = $totalMinutes / 60;
//
//                $dateKey = $lastInTime->format('Y-m-d');
//                if (array_key_exists($dateKey, $dailyHours)) {
//                    $dailyHours[$dateKey] += $hours;
//                }
//
//                // Reset lastInTime after processing the out record
//                $lastInTime = null;
//            }
//        }
//
//        // Sum up all daily hours to get total hours for the last 7 days
//        $totalHoursLastSevenDays = array_sum($dailyHours);
//
//        return response()->json([
//            'daily_hours' => $dailyHours,
//            'total_hours_last_seven_days' => $totalHoursLastSevenDays
//        ]);
//    }


    /**
     * This function calculates the total hours worked by employees for the last 7 days.
     * It fetches all 'in' and 'out' attendance records for the last 7 days,
     * grouped by employee and date, and then processes these records to calculate
     * the total hours worked for each day. The function returns a JSON response
     * containing the daily hours worked for each day and the total hours worked
     * for the last 7 days.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function lastSevenDaysAttendance()
    {
        $startDate = Carbon::today()->subDays(6); // Start date is 7 days ago including today
        $endDate = Carbon::today(); // End date is today

        // Fetch all 'in' and 'out' records for the last seven days, grouped by employee and date
        $attendances = Attendance::select('employee_id', 'clock', 'type')
            ->where('employee_id', auth()->user()->id)
            ->whereBetween('clock', [$startDate, $endDate->endOfDay()])
            ->orderBy('employee_id')
            ->orderBy('clock')
            ->get();

        $dailyHours = [];
        $currentDay = $startDate->copy();

        // Initialize dailyHours array
        while ($currentDay->lessThanOrEqualTo($endDate)) {
            $dailyHours[$currentDay->toDateString()] = 0;
            $currentDay->addDay();
        }

        $processedRanges = [];

        // Process the attendance records to calculate total hours
        foreach ($attendances as $attendance) {
            $attendanceDate = Carbon::parse($attendance->clock)->toDateString();
            $clockTime = new \DateTime($attendance->clock);

            if (!isset($processedRanges[$attendanceDate])) {
                $processedRanges[$attendanceDate] = [];
            }

            if ($attendance->type == 'in') {
                // Set the start time for the range
                $processedRanges[$attendanceDate][] = ['start' => $clockTime, 'end' => null];
            } elseif ($attendance->type == 'out') {
                // Find the last 'in' without an 'out' and pair it with this 'out'
                foreach ($processedRanges[$attendanceDate] as &$range) {
                    if ($range['end'] === null) {
                        $range['end'] = $clockTime;
                        break;
                    }
                }
            }
        }

        // Calculate the total hours considering overlapping ranges
        foreach ($processedRanges as $date => $ranges) {
            $mergedRanges = [];
            foreach ($ranges as $range) {
                if ($range['end'] !== null) {
                    $merged = false;
                    foreach ($mergedRanges as &$mergedRange) {
                        if ($range['start'] <= $mergedRange['end'] && $range['end'] >= $mergedRange['start']) {
                            // Merge overlapping ranges
                            $mergedRange['start'] = min($range['start'], $mergedRange['start']);
                            $mergedRange['end'] = max($range['end'], $mergedRange['end']);
                            $merged = true;
                            break;
                        }
                    }
                    if (!$merged) {
                        $mergedRanges[] = $range;
                    }
                }
            }

            // Calculate total hours for the day
            foreach ($mergedRanges as $mergedRange) {
                $diff = $mergedRange['start']->diff($mergedRange['end']);
                $totalMinutes = ($diff->h * 60) + $diff->i;
                $hours = $totalMinutes / 60;
                $dailyHours[$date] += $hours;
            }
        }

        // Sum up all daily hours to get total hours for the last 7 days
        $totalHoursLastSevenDays = array_sum($dailyHours);



        return response()->json([
            'daily_hours' => $dailyHours,
            'total_hours_last_seven_days' => round($totalHoursLastSevenDays, 2), // Round to 2 decimal places
        ]);
    }

    public function currentMonthAttendance()
    {
        $startDate = Carbon::now()->startOfMonth(); // Start date is the first day of the current month
        $endDate = Carbon::now(); // End date is today

        // Fetch all 'in' and 'out' records for the current month, grouped by employee and date
        $attendances = Attendance::select('employee_id', 'clock', 'type')
            ->where('employee_id', auth()->user()->id)
            ->whereBetween('clock', [$startDate, $endDate->endOfDay()])
            ->orderBy('employee_id')
            ->orderBy('clock')
            ->get();

        $dailyHours = [];
        $currentDay = $startDate->copy();

        // Initialize dailyHours array
        while ($currentDay->lessThanOrEqualTo($endDate)) {
            $dailyHours[$currentDay->toDateString()] = 0;
            $currentDay->addDay();
        }

        $processedRanges = [];

        // Process the attendance records to calculate total hours
        foreach ($attendances as $attendance) {
            $attendanceDate = Carbon::parse($attendance->clock)->toDateString();
            $clockTime = new \DateTime($attendance->clock);

            if (!isset($processedRanges[$attendanceDate])) {
                $processedRanges[$attendanceDate] = [];
            }

            if ($attendance->type == 'in') {
                // Set the start time for the range
                $processedRanges[$attendanceDate][] = ['start' => $clockTime, 'end' => null];
            } elseif ($attendance->type == 'out') {
                // Find the last 'in' without an 'out' and pair it with this 'out'
                foreach ($processedRanges[$attendanceDate] as &$range) {
                    if ($range['end'] === null) {
                        $range['end'] = $clockTime;
                        break;
                    }
                }
            }
        }

        // Calculate the total hours considering overlapping ranges
        foreach ($processedRanges as $date => $ranges) {
            $mergedRanges = [];
            foreach ($ranges as $range) {
                if ($range['end'] !== null) {
                    $merged = false;
                    foreach ($mergedRanges as &$mergedRange) {
                        if ($range['start'] <= $mergedRange['end'] && $range['end'] >= $mergedRange['start']) {
                            // Merge overlapping ranges
                            $mergedRange['start'] = min($range['start'], $mergedRange['start']);
                            $mergedRange['end'] = max($range['end'], $mergedRange['end']);
                            $merged = true;
                            break;
                        }
                    }
                    if (!$merged) {
                        $mergedRanges[] = $range;
                    }
                }
            }

            // Calculate total hours for the day
            foreach ($mergedRanges as $mergedRange) {
                $diff = $mergedRange['start']->diff($mergedRange['end']);
                $totalMinutes = ($diff->h * 60) + $diff->i;
                $hours = $totalMinutes / 60;
                $dailyHours[$date] += $hours;
            }
        }

        // Sum up all daily hours to get total hours for the current month
        $totalHoursCurrentMonth = array_sum($dailyHours);

        return response()->json([
            'daily_hours' => $dailyHours,
            'total_hours_current_month' => round($totalHoursCurrentMonth,2)
        ]);
    }


    /**
     * Get the total hours worked today by the current user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function todayAttendance()
    {
        $startDate = Carbon::today(); // Start date is today
        $endDate = Carbon::today()->endOfDay(); // End date is end of today

        // Fetch all 'in' and 'out' records for today, grouped by employee and time
        $attendances = Attendance::select('employee_id', 'clock', 'type')
            ->where('employee_id', auth()->user()->id)
            ->whereBetween('clock', [$startDate, $endDate])
            ->orderBy('employee_id')
            ->orderBy('clock')
            ->get();

        $dailyHours = 0; // Initialize the total hours for today
        $processedRanges = [];

        // Process the attendance records to calculate total hours
        foreach ($attendances as $attendance) {
            $clockTime = new \DateTime($attendance->clock);

            if ($attendance->type == 'in') {
                // Set the start time for the range
                $processedRanges[] = ['start' => $clockTime, 'end' => null];
            } elseif ($attendance->type == 'out') {
                // Find the last 'in' without an 'out' and pair it with this 'out'
                foreach ($processedRanges as &$range) {
                    if ($range['end'] === null) {
                        $range['end'] = $clockTime;
                        break;
                    }
                }
            }
        }

        // Calculate the total hours considering overlapping ranges
        $mergedRanges = [];
        foreach ($processedRanges as $range) {
            if ($range['end'] !== null) {
                $merged = false;
                foreach ($mergedRanges as &$mergedRange) {
                    if ($range['start'] <= $mergedRange['end'] && $range['end'] >= $mergedRange['start']) {
                        // Merge overlapping ranges
                        $mergedRange['start'] = min($range['start'], $mergedRange['start']);
                        $mergedRange['end'] = max($range['end'], $mergedRange['end']);
                        $merged = true;
                        break;
                    }
                }
                if (!$merged) {
                    $mergedRanges[] = $range;
                }
            }
        }

        // Calculate total hours for today
        foreach ($mergedRanges as $mergedRange) {
            $diff = $mergedRange['start']->diff($mergedRange['end']);
            $totalMinutes = ($diff->h * 60) + $diff->i;
            $hours = $totalMinutes / 60;
            $dailyHours += $hours;
        }

        return response()->json([
            'daily_hours' => round($dailyHours, 2), // Round to 2 decimal places
        ]);
    }




}
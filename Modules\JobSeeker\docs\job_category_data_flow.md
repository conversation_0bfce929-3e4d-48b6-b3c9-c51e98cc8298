# JobSeeker: Job Category Data Flow Diagram

**Purpose:** This document illustrates the data flow for how job categories are used when fetching jobs from external providers like `jobs.af` and `acbar`. It clarifies the role of Canonical Categories, Provider-Specific Categories, and Schedule Rules.

---

## Data Flow Diagram (Mermaid)

```mermaid
graph TD
    subgraph "A. System Trigger"
        A(Laravel Task Scheduler) --> B[Runs SyncJobsAfCommand];
    end

    subgraph "B. Rule & Category Selection"
        B --> C{1. Get Active Schedule Rules for 'jobs.af'};
        C --> DB1[(CommandScheduleRule Table)];
        C --> D{2. For each Rule...};
        D --> E[3. Get associated Canonical Categories];
        E --> DB2[(Canonical JobCategory Table)];
    end

    subgraph "C. The Critical Translation Step"
        E --> F[4. Translate Canonical Category to Provider-Specific Category];
        F --> DB3[(ProviderJobCategory Mapping Table)];
        DB3 --> G[5. Extract provider_category_id/name (e.g., 'IT-Software')];
    end

    subgraph "D. External Fetch & Internal Save"
        G --> H[6. Construct URL with Provider-Specific Category];
        H --> EXT((jobs.af External API));
        EXT --> I[7. Fetch Raw Job Data];
        I --> J[8. Normalize Data & Save Job with **Canonical Category ID**];
        J --> DB4[(Jobs Table)];
    end

    D --> K(End Loop);
```

---

## Explanation of the Data Flow

This diagram shows the step-by-step process the system uses to fetch jobs for a specific category from a provider. The core of the innovation is the **translation** between your clean, internal categories and the messy, varied categories of the outside world.

**Step 1 & 2: Get the Schedule**
*   The process begins when the Laravel Scheduler triggers a command (e.g., `SyncJobsAfCommand`).
*   The command queries the `command_schedule_rules` table to find what it's supposed to do. For example, it finds a rule named "Fetch Daily IT Jobs from Jobs.af".

**Step 3: Identify the Canonical Category**
*   The system looks at the categories associated with this rule. These are your internal, **Canonical Categories** (from the `job_categories` table). For instance, the rule is linked to your internal category #5, which is named "Information Technology".

**Step 4 & 5: The Translation (The Secret Sauce)**
*   This is the most important step. The system knows it needs to find "Information Technology" jobs, but it needs to know what `jobs.af` calls that category.
*   It queries the `provider_job_categories` table. This table acts as a **translation dictionary**. It finds the entry that links your Canonical Category #5 to the `jobs.af` provider.
*   From that entry, it extracts the provider-specific category name or ID, for example, `IT-Software` or `cat_id=123`.

**Step 6 & 7: Fetch the Data**
*   The system now uses the provider-specific category (`IT-Software`) to build the correct URL and makes a request to the `jobs.af` API.
*   It receives a list of raw job data that all belong to the `IT-Software` category on `jobs.af`.

**Step 8: Normalize and Save**
*   As the system processes each raw job it received, it creates a new record for your `jobs` table.
*   Crucially, when it saves this new job, it does **not** use the provider's category. It assigns your internal **Canonical Category ID** (#5 for "Information Technology").

### Conclusion

To answer your core question: The system is driven by your **Canonical Categories**. It uses them to decide *what* to fetch, translates them into the provider's language to make the API call, and then translates the results *back* into your canonical system for clean, standardized storage. This ensures that your internal data remains consistent and organized, regardless of how many different job providers you integrate with in the future.

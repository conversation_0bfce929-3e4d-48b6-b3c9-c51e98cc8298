<?php

declare(strict_types=1);

namespace Modules\Education\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use App\Classes;
use App\Student;
use App\IjazasanadMemorizationPlan;
use App\StudentIjazasanadMemorizationReport;
use App\MoshafSurah;
use App\AttendanceOption;
use App\ProgramLevelLesson;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

final class MonthlyIjazasanadReportController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        try {
            $classId = (int)$request->input('classId');
            $monthYear = $request->input('classDate');

            if (!$classId || !$monthYear) {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            // Parse month and year
            $date = Carbon::createFromFormat('M Y', $monthYear);
            $month = $date->month;
            $year = $date->year;

            // Get students with the same ordering as MonthlyPlanController
            $students = Student::whereHas('joint_classes', function ($query) use ($classId) {
                $query->where('class_id', $classId);
            })
            ->where('status', 'active')
            ->with(['studentProgramLevels.programlevel']) // Eager load for level detection
            ->orderBy('full_name', 'asc') // Same ordering as MonthlyPlanController
            ->get();

            $data = [];
            foreach ($students as $index => $student) {
                $attendanceData = $this->calculateAttendance($student->id, $classId, $month, $year);
                $achievement = $this->calculateAchievement($student, $classId, $month, $year);
                $entryData = $this->getEntryData($student->id, $classId, $month, $year);
                $monthlyPlan = $this->getMonthlyPlan($student->id, $month, $year);
                $monthlyAchievement = $this->getMonthlyAchievement($student->id, $classId, $month, $year);

                $data[] = [
                    'DT_RowIndex' => $index + 1,
                    'student_id' => $student->id, // Add student ID for row identification
                    'student' => $this->formatStudentName($student),
                    'entry1' => $entryData['entry1'],
                    'entry2' => $entryData['entry2'],
                    'entry3' => $entryData['entry3'],
                    'entry4' => $entryData['entry4'],
                    'monthlyPlan' => $monthlyPlan,
                    'monthlyAchievement' => $monthlyAchievement,
                    'attendancePercentage' => $this->formatProgressBarWithPopup($attendanceData['percentage'], '#28a745', $attendanceData['details']),
                    'achievementPercentage' => $this->formatAchievementProgressBarWithPopup($achievement, '#1fff0f', $this->getAchievementDetails($student, $classId, $month, $year))
                ];
            }

            return DataTables::of($data)
                ->rawColumns(['student', 'attendancePercentage', 'achievementPercentage', 'entry1', 'entry2', 'entry3', 'entry4', 'monthlyPlan', 'monthlyAchievement'])
                ->toJson();

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    private function getMonthlyPlan(int $studentId, int $month, int $year): string
    {
        // Get the student's Ijazasanad plan for the specified month/year
        $plan = IjazasanadMemorizationPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year) {
                $query->whereYear('start_date', $year)
                      ->whereMonth('start_date', $month);
            })
            ->orWhere(function ($query) use ($month, $year) {
                $query->whereYear('created_at', $year)
                      ->whereMonth('created_at', $month);
            })
            ->first();

        if (!$plan) {
            return '—';
        }

        $planContent = '';

        // Build the plan content based on Ijazasanad structure (Juz' based)
        if (!empty($plan->from_surat_juz_id) && !empty($plan->to_surat_juz_id)) {
            $planContent .= "<div>Juz' {$plan->from_surat_juz_id} - Juz' {$plan->to_surat_juz_id}</div>";
        }

        // Add Surah:Ayat information if available
        if (!empty($plan->start_from_surat) && !empty($plan->start_from_ayat) && 
            !empty($plan->to_surat) && !empty($plan->to_ayat)) {
            
            // Get surah names from database
            $fromSurah = MoshafSurah::where('id', $plan->start_from_surat)->value('eng_name');
            $toSurah = MoshafSurah::where('id', $plan->to_surat)->value('eng_name');
            
            if ($fromSurah && $toSurah) {
                $planContent .= "<div>{$fromSurah}:{$plan->start_from_ayat} - {$toSurah}:{$plan->to_ayat}</div>";
            }
        }

        return $planContent ?: '—';
    }

    private function getEntryData(int $studentId, int $classId, int $month, int $year): array
    {
        // Get all reports for the student in the specified month/year
        $reports = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereNotNull('hefz_from_surat')
            ->whereNotNull('hefz_from_ayat')
            ->whereNotNull('hefz_to_surat')
            ->whereNotNull('hefz_to_ayat')
            ->orderBy('created_at', 'asc')
            ->get();

        if ($reports->isEmpty()) {
        return [
            'entry1' => '—',
            'entry2' => '—',
            'entry3' => '—',
            'entry4' => '—'
        ];
        }

        // Find the earliest report (minimum values for entry1 and entry2)
        $earliestReport = $reports->first();
        
        // Find the latest report (maximum values for entry3 and entry4)
        $latestReport = $reports->last();

        // Get the minimum hefz_from values (start of the month)
        $minFromSurat = $reports->min('hefz_from_surat');
        $minFromAyat = $reports->where('hefz_from_surat', $minFromSurat)->min('hefz_from_ayat');
        $minFromReport = $reports->where('hefz_from_surat', $minFromSurat)
                                ->where('hefz_from_ayat', $minFromAyat)
                                ->first();

        // Get the maximum hefz_to values (end of the month)
        $maxToSurat = $reports->max('hefz_to_surat');
        $maxToAyat = $reports->where('hefz_to_surat', $maxToSurat)->max('hefz_to_ayat');
        $maxToReport = $reports->where('hefz_to_surat', $maxToSurat)
                              ->where('hefz_to_ayat', $maxToAyat)
                              ->first();

        // Format the daily report URL
        $dailyReportUrl = route('reports.index', ['id' => $classId]);

        return [
            'entry1' => $this->formatEntryWithTooltip(
                $this->getSurahName($minFromSurat),
                $minFromReport->created_at->format('Y-m-d'),
                $dailyReportUrl,
                'From Surah (Start of Month)'
            ),
            'entry2' => $this->formatEntryWithTooltip(
                $minFromAyat,
                $minFromReport->created_at->format('Y-m-d'),
                $dailyReportUrl,
                'From Ayat (Start of Month)'
            ),
            'entry3' => $this->formatEntryWithTooltip(
                $this->getSurahName($maxToSurat),
                $maxToReport->created_at->format('Y-m-d'),
                $dailyReportUrl,
                'To Surah (End of Month)'
            ),
            'entry4' => $this->formatEntryWithTooltip(
                $maxToAyat,
                $maxToReport->created_at->format('Y-m-d'),
                $dailyReportUrl,
                'To Ayat (End of Month)'
            )
        ];
    }

    private function formatEntryWithTooltip($value, string $date, string $url, string $description): string
    {
        // Handle null values
        if ($value === null) {
            $value = '—';
        }
        
        return sprintf(
            '<span class="entry-tooltip" data-entry-type="%s" data-entry-date="%s" data-daily-report-url="%s">%s</span>',
            htmlspecialchars($description),
            $date,
            $url,
            $value
        );
    }

    private function getMonthlyAchievement(int $studentId, int $classId, int $month, int $year): string
    {
        // Get all reports for the student in the specified month/year with pages_memorized
        $reports = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereNotNull('pages_memorized')
            ->where('pages_memorized', '>', 0)
            ->get();

        if ($reports->isEmpty()) {
            return '—';
        }

        // Calculate total pages memorized (sum of all pages)
        $totalPagesMemorized = $reports->sum('pages_memorized');
        
        // Get date range for display
        $firstReport = $reports->sortBy('created_at')->first();
        $lastReport = $reports->sortBy('created_at')->last();
        
        $achievementContent = '';
        
        // Display total pages memorized prominently
        $achievementContent .= "<div style='font-weight: bold; color: #009933;'>{$totalPagesMemorized} pages</div>";
        
        // Add date range if different dates
        if ($firstReport->created_at->format('Y-m-d') !== $lastReport->created_at->format('Y-m-d')) {
            $achievementContent .= "<div class='text-muted' style='font-size: 11px;'>";
            $achievementContent .= $firstReport->created_at->format('M j') . " - " . $lastReport->created_at->format('M j');
            $achievementContent .= "</div>";
        } else {
            $achievementContent .= "<div class='text-muted' style='font-size: 11px;'>";
            $achievementContent .= $firstReport->created_at->format('M j, Y');
            $achievementContent .= "</div>";
        }
        
        // Add number of reports for context
        $reportsCount = $reports->count();
        if ($reportsCount > 1) {
            $achievementContent .= "<div class='text-muted' style='font-size: 10px;'>({$reportsCount} reports)</div>";
        }

        return $achievementContent;
    }

    private function getJuzFromSurahAyat(?int $surahId, ?int $ayat): ?int
    {
        // Guard – if any part is missing we can't determine a Juz
        if (!$surahId || !$ayat) {
            return null;
        }

        // Very rough approximation – replace with real mapping when available
        return (int)ceil($surahId / 4);
    }

    private function calculateAttendance(int $studentId, int $classId, int $month, int $year): array
    {
        // Get total scheduled classes for the month
        $class = Classes::find($classId);
        if (!$class || !$class->timetable) {
            return ['percentage' => 0.0, 'details' => []];
        }

        $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
        if ($totalClasses <= 0) {
            return ['percentage' => 0.0, 'details' => []];
        }

        // Count attended classes (both on-time and late)
        $attendedClasses = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereIn('attendance_id', [1, 2]) // 1 = late, 2 = on time
            ->count();

        $absentClasses = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->where('attendance_id', 3) // 3 = absent
            ->count();

        $percentage = min(100.0, ($attendedClasses / $totalClasses) * 100);

        return [
            'percentage' => $percentage,
            'details' => [
                'total_classes' => $totalClasses,
                'attended' => $attendedClasses,
                'absent' => $absentClasses,
                'percentage' => number_format($percentage, 1)
            ]
        ];
    }

    private function calculateAchievement(Student $student, int $classId, int $month, int $year): float
    {
        // Get the student's plan for the month using the same query logic as other controllers
        $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
            ->where(function ($query) use ($month, $year, $classId) {
                $query->where(function ($q) use ($year, $month, $classId) {
                    $q->whereYear('created_at', $year)
                        ->whereMonth('created_at', $month)
                        ->where('class_id', $classId)
                        ->where('status', 'active');
                })->orWhere(function ($q2) use ($year, $month, $classId) {
                    $q2->whereYear('start_date', $year)
                        ->whereMonth('start_date', $month)
                        ->where('class_id', $classId)
                        ->where('status', 'active');
                });
            })
            ->first();

        if (!$plan) {
            return 0.0;
        }

        // Get actual achievements from reports
        $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get();

        if ($reports->isEmpty()) {
            return 0.0;
        }

        // Detect student level and calculate achievement accordingly
        $studentLevel = $this->detectStudentLevel($student);
        
        if ($studentLevel === 'level1') {
            $completionData = $this->calculateLevel1Completion($plan, $reports);
            return $completionData['completion_rate'];
        } else {
            // Level 2 or fallback calculation
            $completionData = $this->calculateLevel2Completion($plan, $reports);
            return $completionData['completion_rate'];
        }
    }

    private function calculateAchievedJuzFromReports($reports): float
    {
        $uniqueJuzSet = collect();

        foreach ($reports as $report) {
            $fromJuz = $this->getJuzFromSurahAyat($report->hefz_from_surat, $report->hefz_from_ayat);
            $toJuz = $this->getJuzFromSurahAyat($report->hefz_to_surat, $report->hefz_to_ayat);
            
            if ($fromJuz && $toJuz) {
                for ($juz = $fromJuz; $juz <= $toJuz; $juz++) {
                    $uniqueJuzSet->push($juz);
                }
            }
        }

        return $uniqueJuzSet->unique()->count();
    }

    private function formatStudentName(Student $student): string
    {
        return "<a href='" . route('students.show', $student->id) . "' class='studentProfileLink' target='_blank'>{$student->full_name}</a>";
    }

    private function formatProgressBar(float $percentage, string $color): string
    {
        return "<div class='ui progress' data-percent='{$percentage}' style='margin: 0;'>
                    <div class='bar' style='background-color: {$color}; width: {$percentage}%;'></div>
                    <div class='label'>" . number_format($percentage, 1) . "%</div>
                </div>";
    }

    private function formatProgressBarWithPopup(float $percentage, string $color, array $details): string
    {
        $detailsJson = htmlspecialchars(json_encode($details), ENT_QUOTES, 'UTF-8');
        $safePercentage = number_format($percentage, 1);
        
        return "<div class='ui progress attendance-progress' 
                     data-percent='{$safePercentage}' 
                     data-details='{$detailsJson}'
                     style='margin: 0; cursor: pointer; position: relative; background: #f0f0f0; border-radius: 4px; height: 20px;'
                     onclick='showAttendanceDetails(this)'>
                    <div class='bar' style='background-color: {$color}; width: {$safePercentage}%; height: 100%; border-radius: 4px; transition: width 0.3s ease;'></div>
                    <div class='label' style='position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; align-items: center; justify-content: center; color: #333; font-weight: bold; font-size: 11px; z-index: 2;'>{$safePercentage}%</div>
                </div>";
    }

    private function formatAchievementProgressBarWithPopup(float $percentage, string $color, array $details): string
    {
        $detailsJson = htmlspecialchars(json_encode($details), ENT_QUOTES, 'UTF-8');
        $safePercentage = number_format($percentage, 1);
        
        return "<div class='ui progress achievement-progress' 
                     data-percent='{$safePercentage}' 
                     data-hashmatWaziri='sssss'
                     data-details='{$detailsJson}'
                     style='margin: 0; cursor: pointer; position: relative; background: #f0f0f0; border-radius: 4px; height: 20px;'
                     onclick='showAchievementDetails(this)'>
                    <div class='bar' style='background-color: {$color}; width: {$safePercentage}%; height: 100%; border-radius: 4px; transition: width 0.3s ease;'></div>
                    <div class='label' style='position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; align-items: center; justify-content: center; color: #333; font-weight: bold; font-size: 11px; z-index: 2;'>{$safePercentage}%</div>
                </div>";
    }

    private function getAchievementDetails(Student $student, int $classId, int $month, int $year): array
    {
        $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
            ->where(function ($query) use ($month, $year, $classId) {
                $query->where(function ($q) use ($year, $month, $classId) {
                    $q->whereYear('created_at', $year)
                        ->whereMonth('created_at', $month)
                        ->where('class_id', $classId)
                        ->where('status', 'active');
                })->orWhere(function ($q2) use ($year, $month, $classId) {
                    $q2->whereYear('start_date', $year)
                        ->whereMonth('start_date', $month)
                        ->where('class_id', $classId)
                        ->where('status', 'active');
                });
            })
            ->first();

        $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get();

        if (!$plan) {
            return [
                'planned_pages' => 0,
                'achieved_pages' => 0,
                'total_reports' => $reports->count(),
                'reports_with_pages' => 0,
                'achievement_percentage' => '0.0'
            ];
        }

        // Detect student level and provide appropriate details
        $studentLevel = $this->detectStudentLevel($student);
        
        if ($studentLevel === 'level1') {
            $completionData = $this->calculateLevel1Completion($plan, $reports);
            return [
                'achievement_percentage' => number_format($completionData['completion_rate'], 1),
                'tooltip' => $completionData['tooltip'] ?? 'Level 1 completion calculation',
                'details' => $completionData,
                'type' => 'level1'
            ];
        } else {
            $completionData = $this->calculateLevel2Completion($plan, $reports);
            return [
                'planned_pages' => $completionData['planned_pages'],
                'achieved_pages' => $completionData['achieved_pages'],
                'total_reports' => $reports->count(),
                'reports_with_pages' => $reports->where('pages_memorized', '>', 0)->count(),
                'achievement_percentage' => number_format($completionData['completion_rate'], 1),
                'type' => 'level2'
            ];
        }
    }

    private function getSurahName(?int $surahId): string
    {
        if (!$surahId) {
            return '—';
        }

        $surah = MoshafSurah::where('id', $surahId)->first();
        
        if (!$surah) {
            return '—';
        }

        // Return format: "1. Al-Fatiha" (ID. English Name)
        return "{$surah->id}. {$surah->eng_name}";
    }

    private function getLessonDisplayName(?int $lessonId, string $courseType): string
    {
        if (!$lessonId) {
            return '—';
        }

        $lesson = ProgramLevelLesson::where('id', $lessonId)
            ->whereJsonContains('properties->course', $courseType)
            ->first();

        if (!$lesson || !$lesson->properties) {
            return '—';
        }

        $properties = $lesson->properties;
        $lessonNo = $properties['no'] ?? '';

        // Format based on course type
        switch ($courseType) {
            case 'ijazasanadtalqeen':
                $fromVerse = $properties['fromVerse'] ?? '';
                $toVerse = $properties['toVerse'] ?? '';
                return $fromVerse && $toVerse ? "{$lessonNo}. {$fromVerse} - {$toVerse}" : "Lesson {$lessonNo}";
                
            case 'ijazasanadrevision':
                $juzu = $properties['juzu'] ?? '';
                return $juzu ? "{$lessonNo}. {$juzu}" : "Lesson {$lessonNo}";
                
            case 'ijazasanadjazariyah':
                $jazariyah = $properties['jazariyah'] ?? '';
                return $jazariyah ? "{$lessonNo}. {$jazariyah}" : "Lesson {$lessonNo}";
                
            case 'ijazasanadseminars':
                $lessonName = $properties['lessonName'] ?? '';
                return $lessonName ? "{$lessonNo}. {$lessonName}" : "Lesson {$lessonNo}";
                
            default:
                return "Lesson {$lessonNo}";
        }
    }

    private function detectStudentLevel($studentDetails): ?string
    {
        // Eager load if not already loaded, though the main query should handle this.
        // Only call loadMissing if this is an Eloquent model
        if (method_exists($studentDetails, 'loadMissing')) {
            $studentDetails->loadMissing('studentProgramLevels.programlevel');
        }

        foreach ($studentDetails->studentProgramLevels as $studentProgramLevel) {
            if ($studentProgramLevel->programlevel) {
                $levelName = strtolower($studentProgramLevel->programlevel->title); // Use 'title' attribute from translations
                if (str_contains($levelName, 'level 1')) {
                    return 'level1';
                }
                if (str_contains($levelName, 'level 2')) {
                    return 'level2';
                }
            }
        }
        return null;
    }

    private function calculateLevel1Completion($plan, $reports): array
    {
        $components = [
            'talqeen',
            'revision',
            'jazariyah',
            'seminars'
        ];

        $componentDetails = [];
        $totalCompletion = 0;
        $validComponents = 0;

        foreach ($components as $componentName) {
            $fromField = "{$componentName}_from_lesson";
            $toField = "{$componentName}_to_lesson";

            if (!empty($plan->$fromField) && !empty($plan->$toField) && $plan->$fromField <= $plan->$toField) {
                $validComponents++;
                $plannedFrom = $plan->$fromField;
                $plannedTo = $plan->$toField;
                $plannedRangeCount = $plannedTo - $plannedFrom + 1;

                $completedLessons = $this->getUniqueCompletedLessonsForComponent($reports, $componentName);
                
                $plannedLessonsRange = range($plannedFrom, $plannedTo);
                $achievedInRange = count(array_intersect($completedLessons, $plannedLessonsRange));
                
                $componentProgress = ($plannedRangeCount > 0) ? ($achievedInRange / $plannedRangeCount) * 100 : 0;
                
                $totalCompletion += $componentProgress;
                $componentDetails[$componentName] = round($componentProgress, 2);
            } else {
                $componentDetails[$componentName] = 0;
            }
        }

        $overallCompletionRate = $validComponents > 0 ? ($totalCompletion / $validComponents) : 0;

        $tooltip = $validComponents === 0 ? "No valid lesson plans found" : null;
        if ($reports->count() === 0) {
            $tooltip = "No progress reports available";
        }

        return [
            'completion_rate' => round($overallCompletionRate, 2),
            'type' => 'level1',
            'components' => $componentDetails,
            'valid_components' => $validComponents,
            'tooltip' => $tooltip,
        ];
    }

    private function getUniqueCompletedLessonsForComponent($reports, string $componentName): array
    {
        $allLessons = [];
        $fromField = "{$componentName}_from_lesson";
        $toField = "{$componentName}_to_lesson";

        foreach ($reports as $report) {
            if (!empty($report->$fromField) && !empty($report->$toField) && $report->$fromField <= $report->$toField) {
                $allLessons = array_merge($allLessons, range($report->$fromField, $report->$toField));
            }
        }

        return array_unique($allLessons);
    }

    private function calculateLevel2Completion($plan, $reports): array
    {
        $plannedPages = 0;
        $tooltip = null;
        
        // Check if we have valid hefz plan coordinates
        if (!empty($plan->start_from_surat) && !empty($plan->start_from_ayat) && 
            !empty($plan->to_surat) && !empty($plan->to_ayat)) {
            
            try {
                // Use existing page calculation logic from the controller
                if ($plan->study_direction == 'backward') {
                    $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                        $plan->start_from_surat,
                        $plan->start_from_ayat,
                        $plan->to_surat,
                        $plan->to_ayat
                    ]);
                    $plannedPages = $numberofPages[0]->numberofPagesSum ?? 0;
                } else {
                    // Forward direction
                    DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                        $plan->start_from_surat,
                        $plan->start_from_ayat,
                        $plan->to_surat,
                        $plan->to_ayat
                    ]);
                    $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                    $plannedPages = $results[0]->number_of_pages_sum ?? 0;
                }
            } catch (\Exception $e) {
                \Log::error('Error calculating planned pages for Level 2 student: ' . $e->getMessage());
                $plannedPages = 0;
                $tooltip = "Error calculating planned pages";
            }
        } else {
            $tooltip = "Incomplete hefz plan coordinates";
        }
        
        // Calculate achieved pages from reports
        $achievedPages = $reports->sum('pages_memorized') ?? 0;
        
        // Cap achieved pages at planned pages maximum to prevent percentage > 100%
        if ($plannedPages > 0 && $achievedPages > $plannedPages) {
            $achievedPages = $plannedPages;
        }
        
        // Calculate completion percentage
        $percentage = $plannedPages > 0 ? round(($achievedPages / $plannedPages) * 100) : 0;
        
        // Set tooltip for edge cases
        if ($reports->count() === 0) {
            $tooltip = "No progress reports available";
        } elseif ($plannedPages === 0 && !$tooltip) {
            $tooltip = "No valid hefz plan found";
        }
        
        return [
            'completion_rate' => $percentage,
            'type' => 'level2',
            'planned_pages' => $plannedPages,
            'achieved_pages' => $achievedPages,
            'tooltip' => $tooltip,
        ];
    }
} 
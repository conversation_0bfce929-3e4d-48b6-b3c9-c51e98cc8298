<?php

namespace Modules\Education\Http\Controllers;

use App\Center;
use App\Classes;
use App\ClassTime;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;

class AcademicCalendarController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Request $request)
    {




        $classes = Classes::with('translations')->get()->toArray();
        $centers = Center::with('translations')->get()->toArray();
        return view('education::new-reporting-system.index',compact('classes','centers'));
    }


    

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view('education::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function store(Request $request)
    {


        switch ($request->type) {
            case 'add':
                $ClassTime = ClassTime::create([
                    'class_time_title' => $request->title,
                    'start_time' => $request->start,
                    'end_time' => $request->end,
                ]);

                return response()->json($ClassTime);
                break;

            case 'update':
                $ClassTime = ClassTime::find($request->id)->update([
                    'class_time_title' => $request->title,
                    'start_time' => $request->start,
                    'end_time' => $request->end,
                ]);

                return response()->json($ClassTime);
                break;

            case 'delete':
                $ClassTime = ClassTime::find($request->id)->delete();

                return response()->json($ClassTime);
                break;

            default:
                # code...
                break;
        }
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show()
    {

        return view('education::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit()
    {
        return view('education::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(Request $request)
    {
    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }
}

@extends('layouts.hound')

@section('content')

<div>
    <div class="container-fluid">
    
        <!-- Row -->
        <div class="row">
            <div class="col-md-4">
                <div class="panel panel-default card-view  pa-0">
                    <div class="panel-wrapper collapse in">
                        <div class="panel-body  pa-0">
                            <div class="profile-box">
                                <div class="profile-cover-pic">
                                </div>
                                <div class="profile-info text-center">
                                    <div class="profile-img-wrap"> 
                                        <img class="inline-block mb-10" src="{{ $guardian->image ? asset($guardian->image) : asset('avatar.jpg') }}" alt="user"/>
                                        <div class="fileupload btn btn-default">
                                            <span class="btn-text">edit</span>
                                            <input class="upload" type="file">
                                        </div>
                                    </div>	
                                    <h5 class="block mt-10 mb-5 weight-500 capitalize-font txt-danger">{{ $guardian->full_name }}</h5>
                                    <h6 class="block capitalize-font pb-20">{{ $guardian->status }}</h6>
                                </div>	
                                <div class="social-info">
                                    <button class="btn btn-default btn-block btn-outline btn-anim" data-toggle="modal" data-target="#myModal"><i class="fa fa-pencil"></i><span class="btn-text">edit profile</span></button>
                                    <div id="myModal" class="modal fade in" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                                                    <h5 class="modal-title" id="myModalLabel">Edit Profile</h5>
                                                </div>
                                                <div class="modal-body">
                                                    <!-- Row -->

                                                    {!! Form::model($guardian, [
                                                        'method' => 'PATCH',
                                                        'route' => ['admission.guardians.update', $guardian->id],
                                                        'files' => true
                                                    ]) !!}
                                                    {!! Form::hidden('update_profile', 1) !!}

                                                    @include('forms.guardian.profile')
                                                    {!! Form::close() !!}
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-default waves-effect" data-dismiss="modal">Cancel</button>
                                                </div>
                                            </div>
                                            <!-- /.modal-content -->
                                        </div>
                                        <!-- /.modal-dialog -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

          
            <div class="col-md-8">
                <div class="card-view pb-20">
                    <h4>Students</h4>
                </br>
                    <div class="row">
                        @foreach($guardian->students as $student)
                            <div class="col-md-4 ">
                                    <img src="{{ $student->image ? asset($student->image) : asset('avatar.jpg') }}" class="img-responsive img-circle padding-20" >

                                <div class="box-static box-border-top">
                                    <div class="box-title text-center">
                                        <h5>{{ $student->full_name }}</h5>
                                    </div>
                                   <a class="btn btn-primary btn-sm btn-block" href="{{ route('students.show', $student->user_id) }}">View Student Profile</a>
                                </div>
                
                            </div>
                        @endforeach
                        </div>
                   

                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@include('jssnippets.select2')

@include('jssnippets.flatpickr')

@section('js')

<script>

    flatpickr('.datetime', {
        enableTime: true,
        minDate: "today",
        "plugins": [new confirmDatePlugin({})]
    });

</script>
@append
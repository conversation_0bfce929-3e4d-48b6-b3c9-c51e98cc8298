<?php

namespace Modules\EducationalReports\Http\Controllers;


use App\EvaluationSchemaOption;
use App\MoshafSurah;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;


class StudentRevisionReportDatatablesController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function studentRecords(Request $request)
    {
        DB::connection()->enableQueryLog();
        if ($request->filled('studentId') || $request->filled('classDate')) {
            $planYearMonth = Carbon::parse($request->get('classDate'))->format('Y-m');
            $dateMonthArray =
            $year = Carbon::parse($request->get('classDate'))->year;
            $month = Carbon::parse($request->get('classDate'))->month;
            $studentHefzReport = StudentRevisionReport::where('student_id', $request->get('studentId'))
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->withTrashed()
                ->with('revisionPlan')
                ->orderBy('created_at', 'asc')  // Sort by 'created_at' in ascending order
                ->get();




            return \Yajra\DataTables\DataTables::of($studentHefzReport)
                ->addIndexColumn()

                ->addColumn('day', function ($reportDetails) use ($request) {

                    $day = $reportDetails->created_at->format('l');
                    $shortDay = substr($day, 0, 3);// this will return the first three letters of the $day. 0 is the starting point in the string and 3 represent the number of chars to show/extract from the string after 0 offset


                    return '<span style="color: #b4eeb0;">' . $shortDay . '</span>';

                    return $shortDay;


                })
                ->addColumn('date', function ($reportDetails) use ($request) {



                    return '<span style="color: #b4eeb0;">' . $reportDetails->created_at->format('d/m/Y') . '</span>';

                    return $reportDetails->created_at->format('d/m/Y');


                })
                ->addColumn('from_surat_and_ayat', function ($reportDetails) use ($request) {

                    $surats = MoshafSurah::all();
                    foreach ($surats as $key => $surat) {
                        if ($reportDetails->revision_from_surat == $surat->id) {
                            return  '<span style="color: #b4eeb0;">' . $surat->eng_name . ': ' . $reportDetails->revision_from_ayat. '</span> <span style="color: #1fff0f; font-weight: bolder">(' . $surat->id. ': ' . $reportDetails->revision_from_ayat . ')</span>';
                            return $surat->eng_name . ': ' . $reportDetails->revision_from_ayat . ' <span style="color: forestgreen; font-weight: bolder">(' . $surat->id. ': ' . $reportDetails->revision_from_ayat . ')</span>';


                        }
                    }



                })
                ->addColumn('to_surat_and_ayat', function ($reportDetails) use ($request) {
                    $surats = MoshafSurah::all();
                    foreach ($surats as $key => $surat) {
                        if ($reportDetails->revision_to_surat == $surat->id) {
                            return  '<span style="color: #b4eeb0;">' . $surat->eng_name . ': ' . $reportDetails->revision_to_ayat. '</span> <span style="color: #1fff0f; font-weight: bolder">(' . $surat->id. ': ' . $reportDetails->revision_to_ayat . ')</span>';

                            return $surat->eng_name . ': ' . $reportDetails->revision_to_ayat . ' <span style="color: forestgreen; font-weight: bolder">(' . $surat->id. ': ' . $reportDetails->revision_to_ayat . ')</span>';


                        }
                    }
                })
                ->addColumn('revisedPages', function ($reportDetails) use ($request) {

                    $numberofPagesSum = 0;
                    if ($reportDetails->count() > 0) {




                        $revisionPlan = $reportDetails->revisionPlan;
                        $revisionPlan = $revisionPlan[0];
                        if ($revisionPlan->study_direction == 'backward') {

                            $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                $reportDetails->revision_from_surat,
                                $reportDetails->revision_from_ayat,
                                $reportDetails->revision_to_surat,
                                $reportDetails->revision_to_ayat
                            ]);


                            $numberofPagesSum = $numberofPages[0]->numberofPagesSum;
                        }
                        else {

                            // Call the CountMemorizedNumberofPagesForward stored procedure with the provided input parameters
                            // and store the output parameter in a user-defined variable named @number_of_pages_sum
                            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                $reportDetails->revision_from_surat,
                                $reportDetails->revision_from_ayat,
                                $reportDetails->revision_to_surat,
                                $reportDetails->revision_to_ayat
                            ]);

                            // Retrieve the value of the @number_of_pages_sum user-defined variable and store it in the $results variable
                            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                            // Access the first element of the $results array and retrieve the value of the number_of_pages_sum property
                            // Store this value in the $plannedNumberofPages variable
                            $numberofPagesSum = $results[0]->number_of_pages_sum;
                        }

                    }
                    return '<h2 style="color: #1fff0f;
    font-weight: bolder;
    font-size: 24px;">' . $numberofPagesSum . '</h2>';
                    return $numberofPagesSum;
                })

                ->addColumn('grade', function ($reportDetails) use ($request) {
                    $evaluationTitle = EvaluationSchemaOption::where('id', $reportDetails->revision_evaluation_id)->first()->title;
                    return '<span style="color: #b4eeb0;">' . $evaluationTitle . '</span>';
                    return $evaluationTitle;


                })
                ->addColumn('attendance', function ($reportDetails) use ($request) {

                    // Check the attendance status based on the attendance_id
                    switch ($reportDetails->attendance_id) {
                        case 1:  // Late
                        case 2:  // On Time
                            // If attendance_id is 1 (Late) or 2 (On Time), return 'Y'
                            return '<span style="color: #b4eeb0;">Y</span>';

                        case 3:  // Absent
                        case 4:  // Excused
                            // If attendance_id is 3 (Absent) or 4 (Excused), return 'N'
                            return '<span style="color: #e74c3c;">N</span>';

                        default:
                            // Default case for Not Applicable or unknown attendance_id
                            return '<span style="color: #b4eeb0;">N/A</span>';
                    }



                })
                ->rawColumns(['from_surat_and_ayat','to_surat_and_ayat','grade','attendance','day','date','DT_RowIndex','revisedPages'])

                ->make(true);

        }

        dd('only ajax requests are allowed');


    }

}

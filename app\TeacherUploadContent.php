<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class TeacherUploadContent extends Model
{
 
	public function contentTypes()
	{
		return $this->belongsTo('App\ContentType', 'content_type', 'id');
	}

	public function roles()
	{
		return $this->belongsTo('Modules\RolePermission\Entities\Role', 'available_for', 'id');
	}

	public function classes()
	{
		return $this->belongsTo('App\Classes', 'class', 'id');
	}
	public function sections()
	{
		return $this->belongsTo('App\Section', 'section', 'id');
	}
}

<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class IjazasanadEmailLog extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'ijazasanad_email_log';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'employee_id',
        'email',
        'subject',
        'status',
        'error_message',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the employee that the log is associated with.
     */
    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Scope a query to only include successful emails.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'success');
    }

    /**
     * Scope a query to only include failed emails.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Log a successful email sending attempt.
     *
     * @param int|null $employeeId
     * @param string $email
     * @param string $subject
     * @return static
     */
    public static function logSuccess(?int $employeeId, string $email, string $subject): self
    {
        return self::create([
            'employee_id' => $employeeId,
            'email' => $email,
            'subject' => $subject,
            'status' => 'success',
        ]);
    }

    /**
     * Log a failed email sending attempt.
     *
     * @param int|null $employeeId
     * @param string $email
     * @param string $subject
     * @param string $errorMessage
     * @return static
     */
    public static function logFailure(?int $employeeId, string $email, string $subject, string $errorMessage): self
    {
        return self::create([
            'employee_id' => $employeeId,
            'email' => $email,
            'subject' => $subject,
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }
} 
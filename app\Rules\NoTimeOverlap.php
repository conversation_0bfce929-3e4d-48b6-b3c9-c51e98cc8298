<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Modules\Attendance\Entities\Attendance;
use Carbon\Carbon;

class NoTimeOverlap implements Rule
{
    private string $employeeId;
    private Carbon $clockOutTime;
    private string $timezone;
    private ?string $overlappingRange = null;

    public function __construct(string $employeeId, string $clockOutTime, string $timezone)
    {
        $this->employeeId = $employeeId;
        $this->clockOutTime = Carbon::parse($clockOutTime, $timezone); // Keep in the user's timezone
        $this->timezone = $timezone;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        $clockOutDate = Carbon::parse($value, $this->timezone)->setTimezone('UTC')->toDateString();

        $attendances = Attendance::where('employee_id', $this->employeeId)
            ->whereDate('clock', $clockOutDate)
            ->orderBy('clock', 'asc')
            ->get();

        $lastClockIn = null;

        // Find the last clock-in time
        foreach ($attendances as $attendance) {
            if ($attendance->type == 'in') {
                $lastClockIn = Carbon::parse($attendance->clock)->setTimezone('UTC');
            }
        }

        // If there is no clock-in record for the day, the clock-out is invalid
        if (!$lastClockIn) {
            $this->overlappingRange = 'There is no clock-in record for this day.';
            return false;
        }

        // The new clock-out time must be after the last clock-in time
        if ($this->clockOutTime->lessThanOrEqualTo($lastClockIn)) {
            $this->overlappingRange = 'Clock-out time must be after the last clock-in time of ' . $lastClockIn->setTimezone($this->timezone)->format('h:i A');
            return false;
        }

        // Check for overlaps with existing clock-out times
        foreach ($attendances as $attendance) {
            if ($attendance->type == 'out') {
                $existingClockOut = Carbon::parse($attendance->clock)->setTimezone('UTC');
                if ($this->clockOutTime->equalTo($existingClockOut)) {
                    $this->overlappingRange = 'This clock-out time of ' . $this->clockOutTime->setTimezone($this->timezone)->format('h:i A') . ' already exists.';
                    return false;
                }
            }
        }

        return true;
    }

    public function message(): string
    {
        return $this->overlappingRange ?? 'The provided clock-out time is invalid.';
    }
}
{!! Form::open(['url' => route('admission.update_admission', $student->current_admission->id) , 'method' => 'PUT', 'class' => '']) !!}

{!! Form::hidden('admission_id' , $student->current_admission->id ) !!} 

@if(isset($student))
    <div class="form-group">
        {!! Form::label('full_name', trans('common.student_full_name') , ['class' => 'control-label']) !!}
        {{ $student->full_name }}
        {!! Form::hidden('student_id', $student->id , ['class' => 'form-control']) !!}
        @if ($errors->has('student_id'))
            <div class="alert alert-danger error">
                <strong>{{ $errors->first('student_id') }}</strong>
            </div>
        @endif
    </div>
    <div class="form-group">
        {!! Form::label('program_id', trans('common.select_program') , ['class' => 'control-label']) !!}
        {!! Form::select('program_id', $programs , $student->current_admission->programs[0]->id , ['class' => 'form-control' , 'required' => 'required']) !!}
        @if ($errors->has('program_id'))
            <div class="alert alert-danger error">
                <strong>{{ $errors->first('program_id') }}</strong>
            </div>
        @endif
    </div>
 <?php $centers=\App\Center::all(); ?>

    <div class="form-group">
        {!! Form::label('center_id', trans('common.select_center') , ['class' => 'control-label']) !!}
        <select class="form-control" id="center_id" name="center_id" >
                <option  checked value="{{$student->current_admission->center->id}}">{{$student->current_admission->center->name}}</option> 
               @can('change student_center')
                @foreach ($centers as  $center)                                     
               <option value={{$center->id}}>{{$center->name}}</option>
               @endforeach
               @endcan
              </select>
        @if ($errors->has('center_id'))
            <div class="alert alert-danger error">
                <strong>{{ $errors->first('center_id') }}</strong>
            </div>
        @endif
    </div>

   
    <?php $class=\App\Classes::where('center_id','=',$student->current_admission->center->id)->get();
    ?>
 
    <div class="form-group">
        {!! Form::label('class_id', trans('common.select_class') , ['class' => 'control-label']) !!}
        <select class="form-control" id="class_id" name="class_id" >
                <option value="{{$student->current_admission->class->id}}">{{ $student->current_admission->class->name}}</option> 
                @foreach ($class as  $cl)
               <option value={{$cl->id}}>{{$cl->name}}</option>
               @endforeach
            </select>
                     
              @if ($errors->has('class_id'))
            <div class="alert alert-danger error">
                <strong>{{ $errors->first('class_id') }}</strong>
            </div>
        @endif
    </div>

    <div class="form-group">
        {!! Form::label('admission_status', trans('common.admission_status') , ['class' => 'control-label']) !!}
        {!! Form::select('admission_status', [$student->current_admission->status => $student->current_admission->status , 'new_admission' => 'Start From begining' ,'offered' => 'Offered' ,'waiting_for_payment' => 'Waiting for Payment' ,  'accepted' => 'Accepted'] , null , ['class' => 'form-control']) !!}
        @if ($errors->has('admission_status'))
            <div class="alert alert-danger error">
                <strong>{{ $errors->first('admission_status') }}</strong>
            </div>
        @endif
    </div>

    <div class="text-center">
    {!! Form::submit(trans('common.save'), ['class' => 'btn btn-danger bg-maroon btn-flat margin']) !!}
    </div>
    {!! Form::close() !!}
@endif

@section('js')
<script>
        $(document).ready(function() {
        
        $('#center_id').change(function(){
        var countryID = $(this).val();
        if(countryID){
        $.ajax({
        type:"GET",
        url:"{{url('getstatelist')}}?countryid="+countryID,
        success:function(res){
        if(res){
        $("#class_id").empty();
        $("#class_id").append('<option>Select</option>');
        $.each(res,function(key,value){
        $("#class_id").append('<option value="'+key+'">'+value+'</option>');
        });
        
        }else{
        $("#class_id").empty();
        }
        }
        });
        }else{
        $("#class_id").empty();
        }
        });
        
        });
        </script>
<script>
    var centersAndClasses = {};
    var getCentersAndClasses = function(){
        $.ajax({
            type: "get",
            url: "/{{ config('app.locale')}}/program-classes/"+$('#program_id').val(),
            dataType: "json",
            success: function (response) {
                centersAndClasses = response;
                $('#center_id').html('<option>{{ trans('common.select_center') }}</option>');
                $.each(response, function (index, value) { 
                    $('#center_id').append($('<option>' , {
                        value : value.center.id,
                        text : value.center.name
                    })
                    ) 
                });              
            }
        });
    }

    $(document).ready(function () {
        // getClasses();

        $('#program_id').change(function(){
            getCentersAndClasses();
        });
        $('#center_id').change(function(){
            $('#class_id').html('<option>{{ trans('common.select_class') }}</option>');

                $.each(centersAndClasses[$(this).val()]['classes_with_program'], function (index, value) { 
                    $('#class_id').append($('<option>' , {
                        value : value.id,
                        text : value.name
                    })
                    ) 
                })
 
        });
    });
</script>
@append

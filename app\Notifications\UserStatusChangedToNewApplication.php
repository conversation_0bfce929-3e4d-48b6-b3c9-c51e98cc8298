<?php

namespace App\Notifications;

use App\Center;
use App\Classes;
use App\ClassTranslation;
use App\Program;
use App\ProgramTranslation;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class UserStatusChangedToNewApplication extends Notification
{
    use Queueable;
    private $student;
    private $programId;
    private $centerId;
    private $classId;

    /**
     * Create a new notification instance.
     *
     * @param $student
     * @param $programId
     * @param $centerId
     * @param $classId
     */
    public function __construct($student,$programId,$centerId,$classId)
    {
        //
        $this->student = $student;
        $this->programId = $programId;
        $this->centerId = $centerId;
        $this->classId = $classId;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        $program = ProgramTranslation::where("program_id",$this->programId)->where("locale",'en')->first();
        $center = Center::find($this->centerId);
      $classCode =   Classes::find($this->classId);

      $classTranslation =   ClassTranslation::where('classes_id',$this->classId)->first();

        return (new MailMessage)
                    ->line('You are receiving this email because we changed your status to New Admission.
                     Details: Program: '.$program->title. ' Center: '. $center->location. ' Classes: '.$classCode->class_code. ' - '. $classTranslation->name)
                    ->action('Education Department will contact you soon',url("/"))
                    ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}

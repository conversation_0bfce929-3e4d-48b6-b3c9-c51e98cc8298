<?php
declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use App\Services\EmailService;
use Carbon\Carbon;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Entities\JobNotificationSentJob;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\JobNotificationSetup;
use Mo<PERSON>les\JobSeeker\Entities\JobSeekerSetting;
use Mo<PERSON>les\JobSeeker\Repositories\JobRepository;
use Modules\JobSeeker\Services\MissedCallNotificationService;
use Modules\JobSeeker\Services\NotificationProcessLogger;
use Mo<PERSON>les\JobSeeker\Services\JobNotificationMonitoringService;

/**
 * JobNotificationHub
 *
 * Centralized, provider-agnostic notification service with comprehensive monitoring
 * and decision audit trail. Processes jobs from any provider and delivers consolidated
 * email notifications to matched user setups.
 *
 * Purpose: Unified notification delivery with complete observability and traceability.
 * Inputs: Job arrays with canonical_category_ids, schedule context with trace_id/execution_id.
 * Outputs: Boolean success indicator, comprehensive process logs, health metrics.
 * Side effects: Sends emails via EmailService, logs to notification_process_logs,
 * tracks metrics in job_notification_health_metrics, records failures in job_notification_failures.
 *
 * Monitoring Infrastructure:
 * - NotificationProcessLogger: Step-by-step execution tracking with business context
 * - JobNotificationMonitoringService: Performance metrics, failure tracking, health monitoring
 * - Comprehensive error handling: All exceptions captured with full business context
 * - Decision audit trail: Category grouping, user matching, email delivery decisions logged
 *
 * Key Responsibilities:
 * - Match active JobNotificationSetup records by canonical categories with full logging
 * - Enforce idempotency using job_notification_sent_jobs with comprehensive tracking
 * - Send consolidated emails per recipient with timing and memory metrics
 * - Track setup processing performance and email delivery success rates
 * - Provide complete troubleshooting data via v_execution_troubleshooting view
 */
final class JobNotificationHub
{
    private EmailService $emailService;
    private MissedCallNotificationService $missedCallService;
    private JobRepository $jobRepository;
    private JobNotificationMonitoringService $monitoringService;
    private ?NotificationProcessLogger $processLogger = null;
    private array $categoryCache = [];

    public function __construct(
        EmailService $emailService,
        MissedCallNotificationService $missedCallService,
        JobRepository $jobRepository,
        JobNotificationMonitoringService $monitoringService
    ) {
        $this->emailService = $emailService;
        $this->missedCallService = $missedCallService;
        $this->jobRepository = $jobRepository;
        $this->monitoringService = $monitoringService;
    }

    /**
     * Initialize process logger for comprehensive step tracking.
     *
     * @param string $traceId Trace ID for correlation
     * @param int|null $executionId Execution ID for correlation
     * @return void
     */
    private function initializeProcessLogger(string $traceId, ?int $executionId): void
    {
        if ($this->processLogger === null) {
            $this->processLogger = new NotificationProcessLogger($traceId, $executionId);
        }
    }

    /**
     * Process and deliver job notifications to recipients.
     * 
     * Groups jobs by canonical categories, matches against user subscriptions,
     * builds email payloads, sends via EmailService, and records sent jobs
     * for idempotency tracking.
     * 
     * @param array $newJobs Array of new job arrays with canonical_category_ids
     * @param array $updatedJobs Array of updated job arrays with canonical_category_ids
     * @param array $missedJobs Array of missed job arrays with canonical_category_ids
     * @param array $scheduleContext Context including provider, trace_id, execution_id
     * @return bool True if any notifications were sent
     */
    public function processAndDeliverNotifications(array $newJobs, array $updatedJobs, array $missedJobs, array $scheduleContext = []): bool
    {
        $traceId = $scheduleContext['trace_id'] ?? \Illuminate\Support\Str::uuid()->toString();
        $executionId = isset($scheduleContext['execution_id']) && is_numeric($scheduleContext['execution_id'])
            ? (int) $scheduleContext['execution_id']
            : null;

        // Initialize comprehensive monitoring
        $this->initializeProcessLogger($traceId, $executionId);

        Log::info('START JobNotificationHub: Preparing to send job notifications', [
            'new_jobs' => count($newJobs),
            'updated_jobs' => count($updatedJobs),
            'missed_jobs' => count($missedJobs),
            'provider' => $scheduleContext['provider'] ?? null,
            'trace_id' => $traceId,
            'execution_id' => $executionId,
        ]);

        // Log the start of notification processing
        $this->processLogger->logStep('notification_hub_start', 'success', 'start', 'hub',
            'Started notification processing in JobNotificationHub', [
            'new_jobs_count' => count($newJobs),
            'updated_jobs_count' => count($updatedJobs),
            'missed_jobs_count' => count($missedJobs),
            'provider' => $scheduleContext['provider'] ?? null
        ]);

        $allJobs = array_merge($newJobs, $updatedJobs, $missedJobs);
        if (empty($allJobs)) {
            Log::info('END JobNotificationHub: No jobs to notify.', [
                'trace_id' => $traceId,
                'execution_id' => $executionId,
            ]);

            // Note: Missed call notification logic has been moved to MissedJobService
            // and is now handled explicitly by provider services

            return false;
        }

        // Load all active setups with required relations
        $setups = JobNotificationSetup::with(['categories', 'recipients', 'jobSeeker'])
            ->where('is_active', true)
            ->get();

        if ($setups->isEmpty()) {
            Log::info('JobNotificationHub: No active notification setups found.');
            return false;
        }

        // Pre-fetch existing notifications to enforce idempotency and avoid duplicates
        $allJobIds = array_column($allJobs, 'id');
        $setupIds = $setups->pluck('id')->all();
        $allRecipientEmails = $setups->flatMap(function ($setup) {
            $emails = $setup->recipients->pluck('email');
            if ($setup->jobSeeker) {
                $emails->push($setup->jobSeeker->email);
            }
            return $emails;
        })->unique()->filter()->all();

        $existingNotifications = JobNotificationSentJob::whereIn('setup_id', $setupIds)
            ->whereIn('job_id', $allJobIds)
            ->whereIn('recipient_email', $allRecipientEmails)
            ->get();

        $sentMap = [];
        foreach ($existingNotifications as $sent) {
            $key = $sent->setup_id . '-' . $sent->job_id . '-' . $sent->recipient_email;
            $sentMap[$key] = true;
        }

        $sentCount = 0;
        $notificationsToCreate = [];

        // Resolve provider name (normalized) from context
        $providerRaw = (string)($scheduleContext['provider'] ?? '');
        $provider = strtolower(trim($providerRaw));

        // Group jobs by canonical categories once (performance optimization)
        $this->processLogger->logStep('category_grouping', 'running', 'process', 'hub',
            'Grouping jobs by canonical categories', [
            'total_jobs' => count($allJobs)
        ]);

        $groups = $this->groupJobsByCanonicalCategory($allJobs);
        if (empty($groups)) {
            $this->processLogger->logStep('category_grouping', 'warning', 'decision', 'hub',
                'No canonical-category groups formed - no jobs have valid categories', [
                'jobs_analyzed' => count($allJobs)
            ]);
            Log::info('JobNotificationHub: No canonical-category groups formed', [
                'trace_id' => $traceId,
                'execution_id' => $executionId,
            ]);
            return false;
        }

        $this->processLogger->logStep('category_grouping', 'success', 'process', 'hub',
            'Successfully grouped jobs by canonical categories', [
            'groups_formed' => count($groups),
            'canonical_categories' => array_keys($groups)
        ]);

        // Log user matching process
        $this->processLogger->logStep('user_matching', 'running', 'process', 'hub',
            'Starting user setup matching against canonical categories', [
            'active_setups' => count($setups),
            'available_categories' => array_keys($groups)
        ]);

        $processedSetups = 0;
        $matchedSetups = 0;
        $totalNotificationsSent = 0;

        foreach ($setups as $setup) {
            $processedSetups++;
            $setupStartTime = microtime(true);
            $setupStartMemory = memory_get_usage(true);

            // Determine allowed canonical category IDs for this setup (direct matching)
            $canonicalIds = $setup->categories->pluck('id')->map(fn($v) => (int)$v)->all();
            
            if (empty($canonicalIds)) {
                Log::info('JobNotificationHub: Setup has no canonical categories configured', [
                    'setup_id' => $setup->id,
                    'trace_id' => $traceId,
                    'execution_id' => $executionId,
                ]);
                continue;
            }

            Log::info('JobNotificationHub: Setup canonical categories resolved', [
                'setup_id' => $setup->id,
                'canonical_ids' => $canonicalIds,
                'canonical_count' => count($canonicalIds),
                'trace_id' => $traceId,
                'execution_id' => $executionId,
            ]);

            // Build recipients list (owner + additional recipients), unique by email
            $recipients = [];
            if ($setup->jobSeeker && $setup->jobSeeker->email) {
                $recipients[] = [
                    'email' => $setup->jobSeeker->email,
                    'name' => $setup->jobSeeker->name ?? 'Job Seeker',
                ];
            }
            foreach ($setup->recipients as $recipient) {
                if ($recipient->email) {
                    $recipients[] = [
                        'email' => $recipient->email,
                        'name' => $recipient->name ?? 'Recipient',
                    ];
                }
            }

            $uniqueRecipients = [];
            $emailsSeen = [];
            foreach ($recipients as $recipient) {
                if (!isset($emailsSeen[$recipient['email']])) {
                    $uniqueRecipients[] = $recipient;
                    $emailsSeen[$recipient['email']] = true;
                }
            }
            $recipients = $uniqueRecipients;

            // NEW CONSOLIDATED APPROACH: One email per recipient per setup
            foreach ($recipients as $recipient) {
                $recipientEmail = $recipient['email'];
                
                Log::info('JobNotificationHub: Processing consolidated notification for recipient', [
                    'setup_id' => $setup->id,
                    'recipient' => $recipientEmail,
                    'trace_id' => $traceId,
                    'execution_id' => $executionId,
                ]);

                // Get job age setting (configurable N days)
                $jobAgeDays = (int) JobSeekerSetting::getValue('job_age_days', 7);
                
                // Build unified job list: current batch + backlog from last N days
                $unifiedJobs = $this->buildUnifiedJobListForRecipient(
                    $allJobs, 
                    $canonicalIds, 
                    $recipientEmail, 
                    $setup->id, 
                    $jobAgeDays,
                    $sentMap,
                    $traceId,
                    $executionId
                );

                if (empty($unifiedJobs)) {
                    Log::info('JobNotificationHub: No jobs for recipient after unified processing', [
                            'setup_id' => $setup->id,
                        'recipient' => $recipientEmail,
                        'trace_id' => $traceId,
                        ]);
                        continue;
                    }

                // Apply aggregation limits to the unified list (not per category)
                    $limits = app(EmailContentManagerService::class)->getAggregationLimits();
                $jobsToSend = $this->applyAggregationLimits($unifiedJobs, $limits, $recipientEmail, $setup->id);

                if (empty($jobsToSend)) {
                    Log::info('JobNotificationHub: No jobs after aggregation limits', [
                        'setup_id' => $setup->id,
                        'recipient' => $recipientEmail,
                        'original_count' => count($unifiedJobs),
                            ]);
                            continue;
                        }
                        
                // Send consolidated email with comprehensive error handling
                $this->processLogger->logStep('email_sending', 'running', 'action', 'hub',
                    'Sending consolidated email notification', [
                    'setup_id' => $setup->id,
                    'recipient_email' => $recipientEmail,
                    'jobs_count' => count($jobsToSend),
                    'job_ids' => array_column($jobsToSend, 'id')
                ]);

                try {
                    $emailStartTime = microtime(true);
                    $emailSuccess = $this->sendConsolidatedEmail(
                        $setup,
                        $recipient,
                        $jobsToSend,
                        $traceId,
                        $executionId
                    );
                    $emailDuration = (microtime(true) - $emailStartTime) * 1000;

                    // Track email metrics
                    $this->monitoringService->trackEmailMetrics($emailSuccess, (int)$emailDuration);

                } catch (\Throwable $e) {
                    $emailSuccess = false;

                    // Track the failure with full context
                    $this->monitoringService->trackFailure(
                        'email_send',
                        $e->getMessage(),
                        $setup->id,
                        $jobsToSend[0]['id'] ?? null,
                        $recipientEmail,
                        [
                            'jobs_count' => count($jobsToSend),
                            'job_ids' => array_column($jobsToSend, 'id'),
                            'error_class' => get_class($e)
                        ],
                        $e
                    );

                    $this->processLogger->logStep('email_sending', 'error', 'error', 'hub',
                        'Failed to send consolidated email', [
                        'setup_id' => $setup->id,
                        'recipient_email' => $recipientEmail,
                        'error_message' => $e->getMessage(),
                        'error_class' => get_class($e)
                    ]);

                    Log::error('JobNotificationHub: Email sending failed', [
                        'setup_id' => $setup->id,
                        'recipient' => $recipientEmail,
                        'error' => $e->getMessage(),
                        'trace_id' => $traceId,
                        'execution_id' => $executionId,
                    ]);
                }

                if ($emailSuccess) {
                    $matchedSetups++;
                    $totalNotificationsSent++;

                    $this->processLogger->logStep('email_sending', 'success', 'action', 'hub',
                        'Successfully sent consolidated email notification', [
                        'setup_id' => $setup->id,
                        'recipient_email' => $recipientEmail,
                        'jobs_sent' => count($jobsToSend)
                    ]);

                    // Record sent jobs
                    $now = Carbon::now();
                    foreach ($jobsToSend as $job) {
                        $notificationsToCreate[] = [
                            'setup_id' => $setup->id,
                            'job_id' => $job['id'],
                            'recipient_email' => $recipientEmail,
                            'sent_at' => $now,
                            'created_at' => $now,
                            'updated_at' => $now,
                        ];
                        $sentMap[$setup->id . '-' . $job['id'] . '-' . $recipientEmail] = true;
                        $sentCount++;
                    }

                    Log::info('JobNotificationHub: Consolidated notification sent successfully', [
                        'setup_id' => $setup->id,
                        'recipient' => $recipientEmail,
                        'jobs_sent' => count($jobsToSend),
                        'categories_included' => count($this->groupJobsByCategory($jobsToSend)),
                                'trace_id' => $traceId,
                                'execution_id' => $executionId,
                            ]);
                        } else {
                    Log::error('JobNotificationHub: Failed to send consolidated notification', [
                                'setup_id' => $setup->id,
                        'recipient' => $recipientEmail,
                        'jobs_count' => count($jobsToSend),
                                'trace_id' => $traceId,
                                'execution_id' => $executionId,
                            ]);
                }
            }

            // Track setup processing metrics
            $setupDuration = (microtime(true) - $setupStartTime) * 1000;
            $setupMemoryUsed = (memory_get_usage(true) - $setupStartMemory) / 1024 / 1024;

            $this->monitoringService->trackSetupProcessing(
                $setup->id,
                count($recipients),
                (int)$setupDuration,
                (int)$setupMemoryUsed
            );
        }

        if (!empty($notificationsToCreate)) {
            try {
                DB::table('job_notification_sent_jobs')->insertOrIgnore($notificationsToCreate);
                Log::info('JobNotificationHub: Database insert completed', [
                    'records_inserted' => count($notificationsToCreate),
                    'trace_id' => $traceId,
                    'execution_id' => $executionId,
                ]);
            } catch (\Throwable $e) {
                Log::error('JobNotificationHub: Database insert failed', [
                    'error' => $e->getMessage(),
                    'records_count' => count($notificationsToCreate),
                    'trace_id' => $traceId,
                    'execution_id' => $executionId,
                ]);
            }
        }

        // Log comprehensive summary of user matching and notification results
        $this->processLogger->logStep('user_matching', 'success', 'complete', 'hub',
            'Completed user setup processing and notification delivery', [
            'setups_processed' => $processedSetups,
            'setups_matched' => $matchedSetups,
            'notifications_sent' => $totalNotificationsSent,
            'total_jobs_sent' => $sentCount
        ]);

        $emailsSent = $sentCount > 0;
        Log::info("END JobNotificationHub: Total notifications sent: {$sentCount}", [
            'setups_processed' => $processedSetups,
            'setups_matched' => $matchedSetups,
            'notifications_sent' => $totalNotificationsSent,
            'trace_id' => $traceId,
            'execution_id' => $executionId,
        ]);

        // Final process completion log
        $this->processLogger->logStep('notification_hub_complete', 'success', 'complete', 'hub',
            'JobNotificationHub processing completed', [
            'emails_sent' => $emailsSent,
            'total_notifications' => $totalNotificationsSent,
            'total_jobs_sent' => $sentCount
        ]);

        // Note: Missed call notification logic has been moved to MissedJobService
        // and is now handled explicitly by provider services

        return $emailsSent;
    }

    /**
     * Group jobs by canonical categories for unified notification processing.
     *
     * Expects each job to have canonical_category_ids attached by JobNotificationService.
     * This method groups jobs by canonical categories to ensure consistent user experience
     * regardless of the original job provider.
     *
     * @param array<int,array<string,mixed>> $jobs Jobs with canonical_category_ids
     * @return array<int,array{category: array{id:int,name:string}, jobs: array<int,array>}>
     */
    private function groupJobsByCanonicalCategory(array $jobs): array
    {
        try {
            $groups = [];
            // Collect all canonical category IDs present
            $allCanonicalIds = [];
            foreach ($jobs as $job) {
                $cids = $job['canonical_category_ids'] ?? [];
                if (is_array($cids)) {
                    foreach ($cids as $cid) {
                        $allCanonicalIds[] = (int)$cid;
                    }
                }
            }

            $allCanonicalIds = array_values(array_unique(array_filter($allCanonicalIds)));
            if (empty($allCanonicalIds)) {
                Log::info('JobNotificationHub: No canonical_category_ids found in jobs payload');
                return [];
            }

            // Resolve category names (do not restrict to is_canonical to avoid empty names)
            $rows = DB::table('job_categories')
                ->whereIn('id', $allCanonicalIds)
                ->get(['id', 'name']);
            $nameMap = [];
            foreach ($rows as $row) {
                $nameMap[(int)$row->id] = (string)$row->name;
            }

            // Build groups by canonical categories
            foreach ($jobs as $job) {
                $cids = $job['canonical_category_ids'] ?? [];
                if (!is_array($cids) || empty($cids)) {
                    continue;
                }
                foreach ($cids as $cid) {
                    $cid = (int)$cid;
                    $groups[$cid]['category'] = ['id' => $cid, 'name' => $nameMap[$cid] ?? 'Category'];
                    $groups[$cid]['jobs'][] = $job;
                }
            }

            Log::info('JobNotificationHub: Grouped jobs by canonical categories', [
                'group_count' => count($groups),
                'canonical_categories' => array_keys($groups),
            ]);

            return $groups;
        } catch (\Throwable $e) {
            Log::error('JobNotificationHub: Error grouping by canonical category', [
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * Group jobs by canonical category using provider->canonical mapping only.
     *
     * Expects each job to have provider category IDs attached in one of:
     *  - provider_category_ids (array of provider_job_categories.id)
     *  - categories mapped already to canonical (then used directly)
     *
     * @param array<int,array<string,mixed>> $jobs
     * @return array<int,array{category: array{id:int,name:string}|array, jobs: array<int,array>}>
     */
    private function groupJobsByCanonicalCategoryStrict(array $jobs): array
    {
        try {
            $result = [];
            foreach ($jobs as $job) {
                $providerCategoryIds = $job['provider_category_ids'] ?? [];
                if (!is_array($providerCategoryIds) || empty($providerCategoryIds)) {
                    // If canonical categories are already attached, use them directly (validated to canonical only)
                    $candidateIds = $job['canonical_category_ids'] ?? $job['categories'] ?? [];
                    if (!is_array($candidateIds) || empty($candidateIds)) {
                        continue;
                    }

                    // Reduce to canonical IDs and resolve names in one query
                    $idNameMap = $this->getCanonicalIdNameMap(array_map('intval', $candidateIds));
                    if (empty($idNameMap)) {
                        continue;
                    }
                    foreach ($idNameMap as $cid => $cname) {
                        $result[$cid]['category'] = ['id' => (int)$cid, 'name' => (string)$cname];
                        $result[$cid]['jobs'][] = $job;
                    }
                    continue;
                }

                // Map provider IDs to canonical via DB
                $mappings = DB::table('provider_job_categories as pjc')
                    ->join('job_categories as jc', 'jc.id', '=', 'pjc.canonical_category_id')
                    ->whereIn('pjc.id', $providerCategoryIds)
                    ->select('jc.id as canonical_id', 'jc.name as canonical_name')
                    ->get();

                foreach ($mappings as $map) {
                    $cid = (int) $map->canonical_id;
                    $result[$cid]['category'] = ['id' => $cid, 'name' => (string) $map->canonical_name];
                    $result[$cid]['jobs'][] = $job;
                }
            }
            return $result;
        } catch (\Throwable $e) {
            Log::error('JobNotificationHub: Error grouping by canonical category', [
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * Group jobs by provider job category using provider_job_categories as source of truth.
     *
     * Expects each job to include provider_category_ids (array of provider_job_categories.id).
     * Falls back to canonical mapping only to resolve display names when necessary.
     *
     * @param array<int,array<string,mixed>> $jobs
     * @return array<int,array{category: array{id:int,name:string}|array, jobs: array<int,array>}>
     */
    private function groupJobsByProviderCategory(array $jobs): array
    {
        try {
            $groups = [];
            // Collect all provider category IDs present
            $allProviderIds = [];
            foreach ($jobs as $job) {
                $pids = $job['provider_category_ids'] ?? [];
                if (is_array($pids)) {
                    foreach ($pids as $pid) {
                        $allProviderIds[] = (int)$pid;
                    }
                }
            }

            $allProviderIds = array_values(array_unique(array_filter($allProviderIds)));
            if (empty($allProviderIds)) {
                Log::info('JobNotificationHub: No provider_category_ids found in jobs payload');
                return [];
            }

            // Resolve provider category names
            $rows = DB::table('provider_job_categories')
                ->whereIn('id', $allProviderIds)
                ->get(['id', 'name']);
            $nameMap = [];
            foreach ($rows as $row) {
                $nameMap[(int)$row->id] = (string)$row->name;
            }

            // Build groups
            foreach ($jobs as $job) {
                $pids = $job['provider_category_ids'] ?? [];
                if (!is_array($pids) || empty($pids)) {
                    continue;
                }
                foreach ($pids as $pid) {
                    $pid = (int)$pid;
                    $groups[$pid]['category'] = ['id' => $pid, 'name' => $nameMap[$pid] ?? 'Category'];
                    $groups[$pid]['jobs'][] = $job;
                }
            }

            Log::info('JobNotificationHub: Grouped jobs by provider categories', [
                'group_count' => count($groups),
            ]);

            return $groups;
        } catch (\Throwable $e) {
            Log::error('JobNotificationHub: Error grouping by provider category', [
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * Minimal canonical category metadata lookup (id, name).
     */
    private function getCanonicalCategoryMeta(int $id): array
    {
        $row = DB::table('job_categories')->where('id', $id)->first(['id', 'name']);
        return $row ? ['id' => (int) $row->id, 'name' => (string) $row->name] : ['id' => $id, 'name' => 'Category'];
    }

    /**
     * Build a map of canonical id => name for the provided IDs, filtering to canonical categories only.
     *
     * @param array<int,int> $candidateIds
     * @return array<int,string>
     */
    private function getCanonicalIdNameMap(array $candidateIds): array
    {
        if (empty($candidateIds)) {
            return [];
        }
        $rows = DB::table('job_categories')
            ->whereIn('id', $candidateIds)
            ->where('is_canonical', true)
            ->get(['id', 'name']);

        $map = [];
        foreach ($rows as $row) {
            $map[(int)$row->id] = (string)$row->name;
        }
        return $map;
    }

    /**
     * Format jobs for email template compatibility.
     *
     * @param array $jobs
     * @return array
     */
    private function formatJobsForEmail(array $jobs): array
    {
        $formatted = [];
        foreach ($jobs as $job) {
            $position = $job['position'] ?? $job['title'] ?? '';
            // Robust company fallback for Jobs.af payload variations
            $companyName = $job['company_name']
                ?? ($job['company']['name'] ?? null)
                ?? (is_string($job['company'] ?? null) ? $job['company'] : null)
                ?? ($job['organizationName'] ?? null)
                ?? '';
            // Provide both location and locations for template compatibility
            $locations = $job['locations'] ?? $job['location'] ?? '';
            $contractType = $job['contract_type'] ?? $job['contractType'] ?? $job['type'] ?? 'Full-time';
            $workType = $job['work_type'] ?? $job['workType'] ?? 'On-site';
            $publishDate = $job['publish_date'] ?? $job['publishDate'] ?? $job['published_at'] ?? Carbon::now()->subDays(3)->format('Y-m-d H:i:s');

            $formatted[] = [
                // Common
                'position' => $position ?: 'Unknown Position',
                'title' => $position ?: 'Unknown Position',
                'company_name' => $companyName ?: 'Unknown Company',
                'company' => $companyName ?: 'Unknown Company',
                'locations' => $locations ?: 'Kabul, Afghanistan',
                'location' => $locations ?: 'Kabul, Afghanistan',
                'contract_type' => $contractType,
                'work_type' => $workType,
                'publish_date' => $publishDate,
                'salary' => $job['salary'] ?? 'As per company salary scale',
                'experience' => $job['experience'] ?? null,
                'slug' => $job['slug'] ?? '',
                'updated_at' => $job['updated_at'] ?? Carbon::now()->format('Y-m-d H:i:s'),
                'source' => $job['source'] ?? null,
                'url' => $job['url'] ?? null,
            ];
        }

        return $formatted;
    }

    /**
     * Send missed call notification when no job seeker emails are dispatched.
     *
     * @param array $newJobs
     * @param array $updatedJobs
     * @param array $missedJobs
     * @param \Illuminate\Database\Eloquent\Collection $setups
     * @param array $scheduleContext
     * @return void
     *
     * Purpose: Alert admin immediately when scheduled job sync completes but zero 
     * job seekers receive notifications, providing visibility into notification gaps.
     *
     * Side effects: Sends email via MissedCallNotificationService, logs activity 
     * with correlation context; does not throw exceptions to avoid disrupting 
     * primary job sync flow.
     *
     * Context analysis: Determines reasons why no emails sent (no jobs, no setups, 
     * filters blocked all, threshold issues) and includes in notification.
     */
    private function sendMissedCallNotification(
        array $newJobs,
        array $updatedJobs,
        array $missedJobs,
        $setups,
        array $scheduleContext
    ): void {
        try {
            Log::info('JobNotificationHub: Preparing missed call notification context');

            // Build comprehensive context for the missed call notification
            $allJobs = array_merge($newJobs, $updatedJobs, $missedJobs);
            $totalJobs = count($allJobs);

            // Analyze why no emails were sent
            $reasonsNoEmails = $this->analyzeWhyNoEmailsSent($allJobs, $setups);

            // Extract categories that were considered
            $categoriesConsidered = $this->extractCategoriesConsidered($allJobs);

            // Build context for the notification
            $missedCallContext = array_merge($scheduleContext, [
                'total_jobs' => $totalJobs,
                'new_jobs' => count($newJobs),
                'updated_jobs' => count($updatedJobs),
                'missed_jobs' => count($missedJobs),
                'active_setups' => $setups->count(),
                'categories_considered' => $categoriesConsidered,
                'reasons_no_emails' => $reasonsNoEmails,
                'timestamp' => Carbon::now()->toDateTimeString(),
            ]);

            // Send the missed call notification
            $sent = $this->missedCallService->sendMissedCallNotification($missedCallContext);

            Log::info('JobNotificationHub: Missed call notification processing completed', [
                'sent' => $sent,
                'context_summary' => [
                    'total_jobs' => $totalJobs,
                    'active_setups' => $setups->count(),
                    'reasons_count' => count($reasonsNoEmails),
                ],
            ]);

        } catch (\Throwable $e) {
            Log::error('JobNotificationHub: Failed to send missed call notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Analyze why no emails were sent and return human-readable reasons.
     *
     * @param array $allJobs
     * @param \Illuminate\Database\Eloquent\Collection $setups
     * @return array
     */
    private function analyzeWhyNoEmailsSent(array $allJobs, $setups): array
    {
        $reasons = [];

        // Check basic conditions
        if (empty($allJobs)) {
            $reasons[] = 'No jobs were found from the provider';
        }

        if ($setups->isEmpty()) {
            $reasons[] = 'No active job notification setups are configured';
            return $reasons; // Early return if no setups
        }

        if (!empty($allJobs) && !$setups->isEmpty()) {
            // More detailed analysis needed
            $limits = app(EmailContentManagerService::class)->getAggregationLimits();
            $isEnabled = (bool)($limits['enabled'] ?? true);

            if ($isEnabled) {
                $minJobs = (int)($limits['min_jobs_per_email'] ?? 1);
                if (count($allJobs) < $minJobs) {
                    $reasons[] = "Jobs found (" . count($allJobs) . ") but below minimum threshold ({$minJobs} jobs per email)";
                } else {
                    $reasons[] = 'Jobs found and setups exist, but category filters or recipient configuration prevented email dispatch';
                }
            } else {
                $reasons[] = 'Aggregation limits disabled - jobs found and setups exist, but category filters or recipient configuration prevented email dispatch';
            }
        }

        return $reasons;
    }

    /**
     * Extract categories that were considered during processing.
     *
     * @param array $allJobs
     * @return array
     */
    private function extractCategoriesConsidered(array $allJobs): array
    {
        $categories = [];

        foreach ($allJobs as $job) {
            // Try to get category information from various possible structures
            $jobCategories = $job['categories'] ?? $job['canonical_category_ids'] ?? [];

            if (is_array($jobCategories)) {
                foreach ($jobCategories as $category) {
                    if (is_array($category) && isset($category['name'])) {
                        $categories[] = $category;
                    } elseif (is_numeric($category)) {
                        // Look up category name by ID
                        $categoryMeta = $this->getCanonicalCategoryMeta((int)$category);
                        if (!empty($categoryMeta['name'])) {
                            $categories[] = $categoryMeta;
                        }
                    }
                }
            }
        }

        // Remove duplicates by category ID
        $uniqueCategories = [];
        $seenIds = [];

        foreach ($categories as $category) {
            $categoryId = $category['id'] ?? null;
            if ($categoryId && !in_array($categoryId, $seenIds)) {
                $uniqueCategories[] = $category;
                $seenIds[] = $categoryId;
            }
        }

        return $uniqueCategories;
    }

    /**
     * Send missed call notification specifically for the empty jobs case.
     *
     * @param array $scheduleContext
     * @return void
     */
    private function sendMissedCallNotificationForEmptyJobs(array $scheduleContext): void
    {
        try {
            Log::info('JobNotificationHub: Preparing missed call notification for empty jobs case');

            // For empty jobs case, we analyze differently
            $reasonsNoEmails = ['No jobs were found from the provider'];
            $categoriesConsidered = [];

            // Get active setups count for context
            $activeSetups = JobNotificationSetup::where('is_active', true)->count();

            // Build context for the notification
            $missedCallContext = array_merge($scheduleContext, [
                'total_jobs' => 0,
                'new_jobs' => 0,
                'updated_jobs' => 0,
                'missed_jobs' => 0,
                'active_setups' => $activeSetups,
                'categories_considered' => $categoriesConsidered,
                'reasons_no_emails' => $reasonsNoEmails,
                'timestamp' => Carbon::now()->toDateTimeString(),
            ]);

            // Send the missed call notification
            $sent = $this->missedCallService->sendMissedCallNotification($missedCallContext);

            Log::info('JobNotificationHub: Empty jobs missed call notification completed', [
                'sent' => $sent,
                'active_setups' => $activeSetups,
            ]);

        } catch (\Throwable $e) {
            Log::error('JobNotificationHub: Failed to send empty jobs missed call notification', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Build unified job list for a recipient including current batch and backlog.
     * 
     * Consolidates jobs from the current provider batch with unsent jobs from 
     * the last N days across ALL providers for the subscribed canonical categories.
     *
     * @param array $currentBatchJobs Jobs from current provider run
     * @param array $canonicalIds Canonical category IDs for this setup  
     * @param string $recipientEmail Recipient to check sent logs against
     * @param int $setupId Setup ID for logging context
     * @param int $jobAgeDays Number of days to look back for backlog
     * @param array $sentMap Pre-built sent map for deduplication
     * @param string|null $traceId For logging correlation
     * @param string|null $executionId For logging correlation
     * @return array Unified job list sorted by date desc
     */
    private function buildUnifiedJobListForRecipient(
        array $currentBatchJobs,
        array $canonicalIds,
        string $recipientEmail,
        int $setupId,
        int $jobAgeDays,
        array $sentMap,
        ?string $traceId,
        ?int $executionId
    ): array {
        // Defensive programming: ensure types are as expected (prevent fatal errors)
        $executionId = is_null($executionId) ? null : (int) $executionId;

        $unifiedJobs = [];
        $jobIds = [];

        // Add current batch jobs that match canonical categories and aren't sent
        foreach ($currentBatchJobs as $job) {
            $jobCanonicalIds = $job['canonical_category_ids'] ?? [];
            $matchesCategory = false;
            
            foreach ($jobCanonicalIds as $cid) {
                if (in_array((int)$cid, $canonicalIds, true)) {
                    $matchesCategory = true;
                    break;
                }
            }
            
            if (!$matchesCategory) {
                continue;
            }

            $sentKey = $setupId . '-' . $job['id'] . '-' . $recipientEmail;
            if (!isset($sentMap[$sentKey])) {
                $unifiedJobs[] = $job;
                $jobIds[] = $job['id'];
            }
        }

        // Add backlog jobs from repository (last N days, unsent, mixed providers)
        try {
            $backlogJobs = $this->jobRepository->getUnsentJobsForRecipientByCanonicalCategories(
                $canonicalIds,
                $recipientEmail,
                $jobAgeDays
            );

            foreach ($backlogJobs as $backlogJob) {
                // Skip if already in current batch
                if (in_array($backlogJob->id, $jobIds)) {
                    continue;
                }

                // Convert Eloquent model to array format for consistency
                $backlogJobArray = $this->eloquentJobToArray($backlogJob);
                $unifiedJobs[] = $backlogJobArray;
                $jobIds[] = $backlogJob->id;
            }

            Log::info('JobNotificationHub: Built unified job list', [
                'setup_id' => $setupId,
                'recipient' => $recipientEmail,
                'current_batch_count' => count($currentBatchJobs),
                'current_batch_matched' => count($unifiedJobs) - $backlogJobs->count(),
                'backlog_count' => $backlogJobs->count(),
                'unified_total' => count($unifiedJobs),
                'job_age_days' => $jobAgeDays,
                'trace_id' => $traceId,
                'execution_id' => $executionId,
            ]);

        } catch (\Throwable $e) {
            Log::error('JobNotificationHub: Error fetching backlog jobs', [
                'setup_id' => $setupId,
                'recipient' => $recipientEmail,
                'error' => $e->getMessage(),
                'trace_id' => $traceId,
                'execution_id' => $executionId,
            ]);
        }

        // Sort by date desc (newest first) - use created_at or fallback with Carbon parsing
        usort($unifiedJobs, function ($a, $b) {
            $aDate = $a['created_at'] ?? $a['publish_date'] ?? '1970-01-01';
            $bDate = $b['created_at'] ?? $b['publish_date'] ?? '1970-01-01';
            
            try {
                $aTimestamp = Carbon::parse($aDate)->timestamp;
            } catch (\Exception $e) {
                $aTimestamp = 0; // Epoch fallback for invalid dates
            }
            
            try {
                $bTimestamp = Carbon::parse($bDate)->timestamp;
            } catch (\Exception $e) {
                $bTimestamp = 0; // Epoch fallback for invalid dates
            }
            
            return $bTimestamp <=> $aTimestamp; // Descending order (newest first)
        });

        return $unifiedJobs;
    }

    /**
     * Convert Eloquent Job model to array format for consistency with job arrays.
     *
     * @param \Modules\JobSeeker\Entities\Job $job
     * @return array
     */
    private function eloquentJobToArray($job): array
    {
        $jobArray = $job->toArray();
        
        // Add canonical category IDs for consistency
        $canonicalIds = $job->categories()->pluck('id')->toArray();
        $jobArray['canonical_category_ids'] = $canonicalIds;
        
        return $jobArray;
    }

    /**
     * Apply aggregation limits to unified job list.
     *
     * @param array $unifiedJobs Complete job list
     * @param array $limits Aggregation limits from EmailContentManagerService
     * @param string $recipientEmail For logging
     * @param int $setupId For logging  
     * @return array Jobs to send after applying limits
     */
    private function applyAggregationLimits(array $unifiedJobs, array $limits, string $recipientEmail, int $setupId): array
    {
        $isEnabled = (bool)($limits['enabled'] ?? true);
        
        if (!$isEnabled) {
            Log::info('JobNotificationHub: Aggregation limits disabled', [
                'setup_id' => $setupId,
                'recipient' => $recipientEmail,
                'total_jobs' => count($unifiedJobs),
            ]);
            return $unifiedJobs;
        }

        $minJobs = (int)($limits['min_jobs_per_email'] ?? 1);
        $maxJobs = (int)($limits['max_jobs_per_email'] ?? 20);

        if (count($unifiedJobs) < $minJobs) {
            Log::info('JobNotificationHub: Below minimum threshold, skipping', [
                'setup_id' => $setupId,
                'recipient' => $recipientEmail,
                'available_jobs' => count($unifiedJobs),
                'min_required' => $minJobs,
            ]);
            return [];
        }

        if (count($unifiedJobs) > $maxJobs) {
            $truncated = array_slice($unifiedJobs, 0, $maxJobs);
            Log::info('JobNotificationHub: Truncated jobs to maximum', [
                'setup_id' => $setupId,
                'recipient' => $recipientEmail,
                'original_count' => count($unifiedJobs),
                'truncated_count' => count($truncated),
                'max_allowed' => $maxJobs,
            ]);
            return $truncated;
        }

        return $unifiedJobs;
    }

    /**
     * Send consolidated email with all jobs grouped by canonical category.
     *
     * @param JobNotificationSetup $setup
     * @param array $recipient Recipient info (email, name)
     * @param array $jobsToSend Final job list to include in email
     * @param string|null $traceId
     * @param string|null $executionId
     * @return bool True if email sent successfully
     */
    private function sendConsolidatedEmail(
        JobNotificationSetup $setup,
        array $recipient,
        array $jobsToSend,
        ?string $traceId,
        ?int $executionId
    ): bool {
        try {
            // Group jobs by canonical category for display
            $categoryGroups = $this->groupJobsByCategory($jobsToSend);
            $categoryNames = array_keys($categoryGroups);
            $categoryCount = count($categoryGroups);

            // Build consolidated subject
            if ($categoryCount === 1) {
                $emailSubject = 'Job Alert: ' . count($jobsToSend) . ' new jobs in ' . $categoryNames[0];
            } else {
                $emailSubject = 'Job Alert: ' . count($jobsToSend) . ' jobs across ' . $categoryCount . ' categories';
            }

            $emailData = [
                'jobs' => $this->formatJobsForEmail($jobsToSend),
                'jobSeeker' => (object) $recipient,
                'setup' => $setup,
                'currentDate' => Carbon::now()->format('F j, Y'),
                'categoryGroups' => $categoryGroups, // For template grouping
                'consolidatedEmail' => true, // Flag for template to render grouped layout
            ];

            $emailView = 'modules.jobseeker.emails.jobs.jobseeker_notification_new';

            $emailSuccess = $this->emailService->send(
                $recipient['email'],
                $emailSubject,
                '',
                $emailData,
                $emailView,
                [],
                [],
                null,
                null
            );

            Log::info('JobNotificationHub: Consolidated email attempt', [
                'setup_id' => $setup->id,
                'recipient' => $recipient['email'],
                'subject' => $emailSubject,
                'jobs_count' => count($jobsToSend),
                'categories_count' => $categoryCount,
                'category_names' => $categoryNames,
                'success' => $emailSuccess,
                'trace_id' => $traceId,
                'execution_id' => $executionId,
            ]);

            return $emailSuccess;

        } catch (\Throwable $e) {
            Log::error('JobNotificationHub: Exception in consolidated email send', [
                'setup_id' => $setup->id,
                'recipient' => $recipient['email'],
                'error' => $e->getMessage(),
                'jobs_count' => count($jobsToSend),
                'trace_id' => $traceId,
                'execution_id' => $executionId,
            ]);
            return false;
        }
    }

    /**
     * Group jobs by their canonical category names for display.
     *
     * @param array $jobs Job arrays with canonical_category_ids
     * @return array Grouped by category name => [jobs]
     */
    private function groupJobsByCategory(array $jobs): array
    {
        $groups = [];
        
        foreach ($jobs as $job) {
            $canonicalIds = $job['canonical_category_ids'] ?? [];
            
            if (empty($canonicalIds)) {
                $groups['Uncategorized'][] = $job;
                continue;
            }

            // Add job to each of its canonical categories
            foreach ($canonicalIds as $cid) {
                $categoryName = $this->getCategoryNameById((int)$cid);
                $groups[$categoryName][] = $job;
            }
        }

        return $groups;
    }

    /**
     * Get category name by ID with caching.
     *
     * @param int $categoryId
     * @return string
     */
    private function getCategoryNameById(int $categoryId): string
    {        
        if (!isset($this->categoryCache[$categoryId])) {
            $category = DB::table('job_categories')->where('id', $categoryId)->first(['name']);
            $this->categoryCache[$categoryId] = $category ? $category->name : 'Category';
        }
        
        return $this->categoryCache[$categoryId];
    }
}

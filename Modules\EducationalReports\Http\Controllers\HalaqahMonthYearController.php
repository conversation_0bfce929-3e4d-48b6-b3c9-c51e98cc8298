<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Http\Controllers;

use App\Classes;
use App\EvaluationSchemaOption;
use App\MoshafSurah;
use App\Student;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use App\StudentIjazasanadMemorizationReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

final class HalaqahMonthYearController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function getMonthYears($classId, Request $request)
    {
        try {
            // Cast classId to integer and validate
            $classId = (int) $classId;
            if ($classId <= 0) {
                return response()->json([
                    'error' => 'Invalid class ID provided',
                    'message' => 'Class ID must be a positive integer'
                ], 400);
            }
            // Get dates from both hefz and ijazasanad memorization reports
            $hefzDates = StudentHefzReport::where('class_id', $classId)
                ->whereNotNull('hefz_from_surat')
                ->whereNotNull('hefz_from_ayat')
                ->whereNotNull('hefz_to_surat')
                ->whereNotNull('hefz_to_ayat');

            $ijazasanadDates = StudentIjazasanadMemorizationReport::where('class_id', $classId)
                ->whereNotNull('hefz_from_surat')
                ->whereNotNull('hefz_from_ayat')
                ->whereNotNull('hefz_to_surat')
                ->whereNotNull('hefz_to_ayat');

            // Apply student filter if provided
            if ($request->has('studentId') && !empty($request->studentId)) {
                $hefzDates->where('student_id', $request->studentId);
                $ijazasanadDates->where('student_id', $request->studentId);
            }

            // Get dates from both queries and combine them
            $hefzResults = $hefzDates->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year')
                ->groupBy('year', 'month')
                ->get();

            $ijazasanadResults = $ijazasanadDates->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year')
                ->groupBy('year', 'month')
                ->get();

            // Combine and deduplicate the results
            $allDates = $hefzResults->concat($ijazasanadResults)
                ->unique(function ($item) {
                    return $item->month . ' ' . $item->year;
                })
                ->sortByDesc('year')
                ->sortBy(function ($item) {
                    $monthOrder = ['January' => 1, 'February' => 2, 'March' => 3, 'April' => 4, 'May' => 5, 'June' => 6,
                                   'July' => 7, 'August' => 8, 'September' => 9, 'October' => 10, 'November' => 11, 'December' => 12];
                    return $monthOrder[$item->month] ?? 13;
                })
                ->values();

            return response()->json($allDates);

        } catch (\Exception $e) {
            \Log::error('Error fetching month-years for class: ' . $e->getMessage());

            return response()->json([
                'error' => 'Failed to fetch month-years',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}

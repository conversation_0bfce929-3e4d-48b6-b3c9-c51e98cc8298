<?php

namespace App;

use App\Scopes\OrganizationScope;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Notifications\Notifiable;
use App\Notifications\StudentResetPassword;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use \Askedio\SoftCascade\Traits\SoftCascadeTrait;
use Illuminate\Database\Eloquent\Builder;


//class Student extends Authenticatable
class StudentProgramLevel extends Model
{


    use SoftDeletes;


    const ATTENDANCE_LATE = 1;
    const ATTENDANCE_ON_TIME = 2;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */


    protected $fillable = [
         'student_id'
        , 'class_id'
        , 'status'
        , 'level_id'


    ];



    /**
     * Get the student that owns the plan.
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    public function class()
    {
        return $this->belongsTo('App\Classes');
    }
    public function programlevel()
    {
        return $this->belongsTo('App\ProgramLevel','level_id','id');
    }

    /**
     *
     *
     */
    public function ijazasanad_plan_level()
    {
        return $this->hasOne(IjazasanadMemorizationPlan::class);
    }










}

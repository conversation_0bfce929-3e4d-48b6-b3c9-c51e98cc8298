<?php

namespace App;

use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Model;
use App\Scopes\OrganizationScope;

class Menu extends Model
{
    use Translatable;

    public $translatedAttributes = array('content' , 'title');

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'menus';

    /**
     * The database primary key value.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['parent', 'type', 'slug', 'status' , 'organization_id'];


    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
    }


}

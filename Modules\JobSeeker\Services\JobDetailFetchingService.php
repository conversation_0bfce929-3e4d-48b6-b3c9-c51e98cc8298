<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Mo<PERSON>les\JobSeeker\Entities\Job;
use Modules\JobSeeker\Entities\JobProvider;
use Mo<PERSON>les\JobSeeker\Entities\JobDetailedInfo;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use DOMDocument;
use DOMXPath;

/**
 * JobDetailFetchingService
 * 
 * Fetches detailed job information from provider websites
 * Supports both Jobs.af and ACBAR providers with specific parsing logic
 */
final class JobDetailFetchingService
{
    private const USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    private const TIMEOUT = 30;
    private const MAX_RETRIES = 3;

    /**
     * Fetch detailed information for a job from provider
     */
    public function fetchJobDetails(Job $job): bool
    {
        $provider = $job->provider;

        if (!$provider) {
            Log::warning('JobDetailFetching: Job has no associated provider', ['job_id' => $job->id]);
            return false;
        }

        Log::info('JobDetailFetching: Starting fetch for job', [
            'job_id' => $job->id,
            'provider' => $provider->slug,
            'position' => $job->position
        ]);

        try {
            switch ($provider->slug) {
                case 'jobs-af':
                    return $this->fetchJobsAfDetails($job, $provider);
                case 'acbar':
                    return $this->fetchAcbarDetails($job, $provider);
                default:
                    Log::warning('JobDetailFetching: Unknown provider for job detail fetch', [
                        'job_id' => $job->id,
                        'provider' => $provider->slug
                    ]);
                    return false;
            }
        } catch (\Exception $e) {
            Log::error('JobDetailFetching: Failed to fetch job details', [
                'job_id' => $job->id,
                'provider' => $provider->slug,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Fetch Jobs.af job details using slug
     */
    private function fetchJobsAfDetails(Job $job, JobProvider $provider): bool
    {
        if (empty($job->slug)) {
            Log::warning('JobDetailFetching: Jobs.af job missing slug', ['job_id' => $job->id]);
            return $this->saveJobDetails($job, $provider, [], null, false, 'Missing job slug');
        }

        $url = "https://jobs.af/jobs/{$job->slug}";

        Log::info('JobDetailFetching: Fetching Jobs.af job details', [
            'job_id' => $job->id,
            'url' => $url
        ]);

        try {
            $response = $this->makeHttpRequest($url);

            if (!$response) {
                throw new \Exception('Failed to fetch job page');
            }

            $html = $response->body();
            $details = $this->parseJobsAfHtml($html, $job);

            return $this->saveJobDetails($job, $provider, $details, $url, true);

        } catch (\Exception $e) {
            Log::error('JobDetailFetching: Failed to fetch Jobs.af job details', [
                'job_id' => $job->id,
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            return $this->saveJobDetails($job, $provider, [], $url, false, $e->getMessage());
        }
    }

    /**
     * Parse Jobs.af HTML for job details
     */
    private function parseJobsAfHtml(string $html, Job $job): array
    {
        $dom = new DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
        $xpath = new DOMXPath($dom);

        $details = [];

        try {
            // Extract various fields using XPath selectors
            $fieldMappings = [
                'about_company' => [
                    '//div[contains(@class, "company-about")]',
                    '//div[contains(@class, "about-company")]',
                    '//section[contains(@class, "company-info")]//p',
                    '//*[contains(text(), "About") and contains(text(), "Company")]/following-sibling::*',
                ],
                'detailed_description' => [
                    '//div[contains(@class, "job-description")]',
                    '//div[contains(@class, "description")]',
                    '//section[contains(@class, "job-details")]//div',
                ],
                'duties_responsibilities' => [
                    '//div[contains(@class, "duties")]',
                    '//div[contains(@class, "responsibilities")]',
                    '//*[contains(text(), "Duties") or contains(text(), "Responsibilities")]/following-sibling::*',
                ],
                'job_requirements' => [
                    '//div[contains(@class, "requirements")]',
                    '//div[contains(@class, "qualifications")]',
                    '//*[contains(text(), "Requirements") or contains(text(), "Qualifications")]/following-sibling::*',
                ],
                'submission_guideline' => [
                    '//div[contains(@class, "application")]',
                    '//div[contains(@class, "how-to-apply")]',
                    '//*[contains(text(), "How to Apply") or contains(text(), "Application")]/following-sibling::*',
                ],
                'benefits' => [
                    '//div[contains(@class, "benefits")]',
                    '//div[contains(@class, "perks")]',
                    '//*[contains(text(), "Benefits") or contains(text(), "Perks")]/following-sibling::*',
                ],
                'working_hours' => [
                    '//span[contains(@class, "hours")]',
                    '//*[contains(text(), "Working Hours") or contains(text(), "Schedule")]/following-sibling::*',
                ],
                'company_size' => [
                    '//span[contains(@class, "company-size")]',
                    '//*[contains(text(), "Company Size")]/following-sibling::*',
                ],
            ];

            foreach ($fieldMappings as $field => $xpathQueries) {
                $content = $this->extractContentByXPath($xpath, $xpathQueries);
                if (!empty($content)) {
                    $details[$field] = $content;
                }
            }

            // Extract application email
            $emailNodes = $xpath->query('//a[contains(@href, "mailto:")]/@href');
            if ($emailNodes && $emailNodes->length > 0) {
                $email = str_replace('mailto:', '', $emailNodes->item(0)->textContent);
                if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $details['application_email'] = $email;
                }
            }

            // Extract application website
            $websiteNodes = $xpath->query('//a[contains(@class, "apply") or contains(@class, "application")]/@href');
            if ($websiteNodes && $websiteNodes->length > 0) {
                $website = $websiteNodes->item(0)->textContent;
                if (filter_var($website, FILTER_VALIDATE_URL)) {
                    $details['application_website'] = $website;
                }
            }

            // Extract salary if not already in job data
            if (empty($job->salary)) {
                $salaryNodes = $xpath->query('//span[contains(@class, "salary")] | //*[contains(text(), "Salary")]/following-sibling::*');
                if ($salaryNodes && $salaryNodes->length > 0) {
                    $salary = trim($salaryNodes->item(0)->textContent);
                    if (!empty($salary)) {
                        $details['salary'] = $salary;
                    }
                }
            }

            Log::info('JobDetailFetching: Parsed Jobs.af job details', [
                'job_id' => $job->id,
                'fields_extracted' => array_keys($details),
                'content_lengths' => array_map('strlen', $details)
            ]);

        } catch (\Exception $e) {
            Log::warning('JobDetailFetching: Error parsing Jobs.af HTML', [
                'job_id' => $job->id,
                'error' => $e->getMessage()
            ]);
        }

        return $details;
    }

    /**
     * Fetch ACBAR job details using job URL
     */
    private function fetchAcbarDetails(Job $job, JobProvider $provider): bool
    {
        $acbarJobId = $this->extractAcbarJobId($job);

        if (!$acbarJobId) {
            Log::warning('JobDetailFetching: Could not extract ACBAR job ID', ['job_id' => $job->id]);
            return $this->saveJobDetails($job, $provider, [], null, false, 'Could not extract ACBAR job ID');
        }

        $url = "https://www.acbar.org/jobs/{$acbarJobId}";

        Log::info('JobDetailFetching: Fetching ACBAR job details', [
            'job_id' => $job->id,
            'acbar_job_id' => $acbarJobId,
            'url' => $url
        ]);

        try {
            $response = $this->makeHttpRequest($url);

            if (!$response) {
                throw new \Exception('Failed to fetch ACBAR job page');
            }

            $html = $response->body();
            $details = $this->parseAcbarHtml($html, $job);

            return $this->saveJobDetails($job, $provider, $details, $url, true);

        } catch (\Exception $e) {
            Log::error('JobDetailFetching: Failed to fetch ACBAR job details', [
                'job_id' => $job->id,
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            return $this->saveJobDetails($job, $provider, [], $url, false, $e->getMessage());
        }
    }

    /**
     * Parse ACBAR HTML for job details
     */
    private function parseAcbarHtml(string $html, Job $job): array
    {
        $dom = new DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
        $xpath = new DOMXPath($dom);

        $details = [];

        try {
            // ACBAR uses table structure for job details
            $tableRows = $xpath->query('//table//tr | //div[contains(@class, "job-detail")]//tr');

            foreach ($tableRows as $row) {
                $cells = $xpath->query('.//td | .//th', $row);
                if ($cells->length >= 2) {
                    $label = trim($cells->item(0)->textContent);
                    $value = trim($cells->item(1)->textContent);

                    if (empty($label) || empty($value)) {
                        continue;
                    }

                    // Map ACBAR labels to our fields
                    $labelMappings = [
                        'About Company' => 'about_company',
                        'Company Information' => 'about_company',
                        'Organization Background' => 'about_company',
                        'Job Description' => 'detailed_description',
                        'Description' => 'detailed_description',
                        'Duties' => 'duties_responsibilities',
                        'Responsibilities' => 'duties_responsibilities',
                        'Duties and Responsibilities' => 'duties_responsibilities',
                        'Requirements' => 'job_requirements',
                        'Qualifications' => 'job_requirements',
                        'Required Qualifications' => 'job_requirements',
                        'How to Apply' => 'submission_guideline',
                        'Application Process' => 'submission_guideline',
                        'Application Instructions' => 'submission_guideline',
                        'Salary' => 'salary',
                        'Compensation' => 'salary',
                        'Experience' => 'preferred_experience_years',
                        'Experience Required' => 'preferred_experience_years',
                        'Education' => 'minimum_education',
                        'Educational Requirements' => 'minimum_education',
                        'Contract Type' => 'contract_type',
                        'Employment Type' => 'contract_type',
                        'Working Hours' => 'working_hours',
                        'Schedule' => 'working_hours',
                        'Benefits' => 'benefits',
                        'Contact Person' => 'contact_person',
                        'Contact' => 'contact_person',
                        'Phone' => 'contact_phone',
                        'Contact Number' => 'contact_phone',
                        'Email' => 'application_email',
                        'Contact Email' => 'application_email',
                        'Application Email' => 'application_email',
                    ];

                    $mappedField = null;
                    foreach ($labelMappings as $pattern => $field) {
                        if (stripos($label, $pattern) !== false) {
                            $mappedField = $field;
                            break;
                        }
                    }

                    if ($mappedField) {
                        // Special handling for experience years
                        if ($mappedField === 'preferred_experience_years') {
                            $years = $this->extractYearsFromText($value);
                            if ($years > 0) {
                                $details[$mappedField] = $years;
                            }
                        } else {
                            $details[$mappedField] = $this->cleanTextContent($value);
                        }
                    }
                }
            }

            // Try to extract email from any links
            if (!isset($details['application_email'])) {
                $emailLinks = $xpath->query('//a[contains(@href, "mailto:")]/@href');
                if ($emailLinks && $emailLinks->length > 0) {
                    $email = str_replace('mailto:', '', $emailLinks->item(0)->textContent);
                    if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $details['application_email'] = $email;
                    }
                }
            }

            Log::info('JobDetailFetching: Parsed ACBAR job details', [
                'job_id' => $job->id,
                'fields_extracted' => array_keys($details),
                'content_lengths' => array_map(function($v) { return is_string($v) ? strlen($v) : 0; }, $details)
            ]);

        } catch (\Exception $e) {
            Log::warning('JobDetailFetching: Error parsing ACBAR HTML', [
                'job_id' => $job->id,
                'error' => $e->getMessage()
            ]);
        }

        return $details;
    }

    /**
     * Extract content using multiple XPath queries
     */
    private function extractContentByXPath(DOMXPath $xpath, array $xpathQueries): ?string
    {
        foreach ($xpathQueries as $query) {
            try {
                $nodes = $xpath->query($query);
                if ($nodes && $nodes->length > 0) {
                    $content = '';
                    foreach ($nodes as $node) {
                        $content .= trim($node->textContent) . ' ';
                    }
                    $content = $this->cleanTextContent($content);
                    if (!empty($content)) {
                        return $content;
                    }
                }
            } catch (\Exception $e) {
                // Continue to next query if this one fails
                continue;
            }
        }

        return null;
    }

    /**
     * Clean text content for storage
     */
    private function cleanTextContent(string $text): string
    {
        // Remove extra whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        
        // Remove HTML entities
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
        
        // Trim and return
        return trim($text);
    }

    /**
     * Extract years from text (e.g., "3-5 years" -> 4, "minimum 2 years" -> 2)
     */
    private function extractYearsFromText(string $text): int
    {
        // Look for patterns like "3-5 years", "minimum 2 years", "2+ years"
        if (preg_match('/(\d+)\s*[-–]\s*(\d+)\s*years?/i', $text, $matches)) {
            // Return average of range
            return (int) (($matches[1] + $matches[2]) / 2);
        }
        
        if (preg_match('/(\d+)\+?\s*years?/i', $text, $matches)) {
            return (int) $matches[1];
        }
        
        if (preg_match('/minimum\s*(\d+)/i', $text, $matches)) {
            return (int) $matches[1];
        }

        return 0;
    }

    /**
     * Make HTTP request with retries
     */
    private function makeHttpRequest(string $url): ?\Illuminate\Http\Client\Response
    {
        for ($attempt = 1; $attempt <= self::MAX_RETRIES; $attempt++) {
            try {
                $response = Http::timeout(self::TIMEOUT)
                    ->withUserAgent(self::USER_AGENT)
                    ->withHeaders([
                        'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language' => 'en-US,en;q=0.5',
                        'Accept-Encoding' => 'gzip, deflate',
                        'DNT' => '1',
                        'Connection' => 'keep-alive',
                        'Upgrade-Insecure-Requests' => '1',
                    ])
                    ->get($url);

                if ($response->successful()) {
                    return $response;
                }

                Log::warning('JobDetailFetching: HTTP request failed', [
                    'url' => $url,
                    'attempt' => $attempt,
                    'status' => $response->status(),
                    'body_preview' => substr($response->body(), 0, 200)
                ]);

            } catch (\Exception $e) {
                Log::warning('JobDetailFetching: HTTP request exception', [
                    'url' => $url,
                    'attempt' => $attempt,
                    'error' => $e->getMessage()
                ]);
            }

            // Wait before retry (exponential backoff)
            if ($attempt < self::MAX_RETRIES) {
                sleep($attempt * 2);
            }
        }

        return null;
    }

    /**
     * Save job details to database
     */
    private function saveJobDetails(Job $job, JobProvider $provider, array $details, ?string $url, bool $success, ?string $error = null): bool
    {
        try {
            JobDetailedInfo::updateOrCreate(
                [
                    'job_id' => $job->id,
                    'provider_id' => $provider->id,
                ],
                array_merge($details, [
                    'provider_job_url' => $url,
                    'fetched_at' => now(),
                    'fetch_success' => $success,
                    'fetch_error' => $error,
                ])
            );

            Log::info('JobDetailFetching: Job details saved', [
                'job_id' => $job->id,
                'provider_id' => $provider->id,
                'success' => $success,
                'fields_count' => count($details)
            ]);

            return $success;

        } catch (\Exception $e) {
            Log::error('JobDetailFetching: Failed to save job details', [
                'job_id' => $job->id,
                'provider_id' => $provider->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Extract ACBAR job ID from job data
     */
    private function extractAcbarJobId(Job $job): ?string
    {
        // Try slug first
        if (!empty($job->slug)) {
            // Check if slug looks like ACBAR format
            if (preg_match('/^[\d]+/', $job->slug)) {
                return $job->slug;
            }
        }

        // Try to extract from job description HTML if it contains the link
        if (!empty($job->description)) {
            if (preg_match('/\/jobs\/(\d+)\//', $job->description, $matches)) {
                return $matches[1];
            }
        }

        // Try raw_data if available
        if (!empty($job->raw_data) && is_array($job->raw_data)) {
            if (isset($job->raw_data['acbar_id'])) {
                return (string) $job->raw_data['acbar_id'];
            }
            if (isset($job->raw_data['id'])) {
                return (string) $job->raw_data['id'];
            }
        }

        return null;
    }

    /**
     * Check if job needs detailed info fetch
     */
    public function needsDetailedFetch(Job $job): bool
    {
        $detailedInfo = JobDetailedInfo::where('job_id', $job->id)->first();

        if (!$detailedInfo) {
            return true;
        }

        // Check if fetch failed and should be retried
        if (!$detailedInfo->fetch_success) {
            // Retry failed fetches after 6 hours
            return $detailedInfo->fetched_at?->diffInHours(now()) > 6;
        }

        // Refresh successful fetches after 7 days
        return $detailedInfo->fetched_at?->diffInDays(now()) > 7;
    }
}

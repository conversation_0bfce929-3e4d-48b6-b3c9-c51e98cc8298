<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * JobSeekerAiTailorArtifact stores individual outputs from each step of the AI tailoring pipeline.
 * 
 * Purpose: Provide full audit trail and deterministic outputs for each pipeline step.
 * Relationships: belongs to a specific tailor run and represents one step's output.
 * Business rules: step_number defines order; artifact_type describes content format.
 * Content types: JSON, markdown, HTML, plain text with MIME type tracking.
 * 
 * @property int $id
 * @property int $tailor_run_id
 * @property int $step_number 1-10 for the pipeline steps
 * @property string $artifact_type Description of artifact (e.g., 'job_profile.json', 'resume_diff.md')
 * @property string|null $content The actual artifact content
 * @property string|null $content_mime MIME type of the content
 * @property int|null $tokens_input Input tokens for this step
 * @property int|null $tokens_output Output tokens for this step
 * @property int|null $processing_time_ms Processing time for this step in milliseconds
 * @property \Illuminate\Support\Carbon|null $created_at
 */
final class JobSeekerAiTailorArtifact extends Model
{
    protected $table = 'jobseeker_ai_tailor_artifacts';

    protected $fillable = [
        'tailor_run_id',
        'step_number',
        'artifact_type',
        'content',
        'content_mime',
        'tokens_input',
        'tokens_output',
        'processing_time_ms',
    ];

    protected $casts = [
        'tailor_run_id' => 'integer',
        'step_number' => 'integer',
        'tokens_input' => 'integer',
        'tokens_output' => 'integer',
        'processing_time_ms' => 'integer',
        'created_at' => 'datetime',
    ];

    // Disable updated_at since artifacts are immutable once created
    public $timestamps = false;
    protected $dates = ['created_at'];

    /**
     * Get the tailor run this artifact belongs to
     */
    public function tailorRun(): BelongsTo
    {
        return $this->belongsTo(JobSeekerAiTailorRun::class, 'tailor_run_id');
    }

    /**
     * Standard artifact types for the 10-step pipeline
     */
    public const ARTIFACT_TYPES = [
        1 => 'job_profile.json',
        2 => 'resume_profiles.json',
        3 => 'entities.json',
        4 => 'requirement_mapping.json',
        5 => 'gap_analysis.json',
        6 => 'improvements.json',
        7 => 'tailored_outline.json',
        8 => 'quantified_bullets.json',
        9 => 'resume_diff.md',
        10 => 'final_resume.html',
    ];

    /**
     * Get the expected artifact type for a given step number
     */
    public static function getExpectedArtifactType(int $stepNumber): string
    {
        return self::ARTIFACT_TYPES[$stepNumber] ?? "step_{$stepNumber}.unknown";
    }

    /**
     * Create an artifact for a specific pipeline step
     */
    public static function createForStep(
        int $tailorRunId,
        int $stepNumber,
        string $content,
        string $contentMime = 'application/json',
        int $tokensInput = null,
        int $tokensOutput = null,
        int $processingTimeMs = null
    ): self {
        return self::create([
            'tailor_run_id' => $tailorRunId,
            'step_number' => $stepNumber,
            'artifact_type' => self::getExpectedArtifactType($stepNumber),
            'content' => $content,
            'content_mime' => $contentMime,
            'tokens_input' => $tokensInput,
            'tokens_output' => $tokensOutput,
            'processing_time_ms' => $processingTimeMs,
        ]);
    }

    /**
     * Check if this artifact contains JSON content
     */
    public function isJson(): bool
    {
        return $this->content_mime === 'application/json' || 
               str_ends_with($this->artifact_type, '.json');
    }

    /**
     * Check if this artifact contains Markdown content
     */
    public function isMarkdown(): bool
    {
        return $this->content_mime === 'text/markdown' || 
               str_ends_with($this->artifact_type, '.md');
    }

    /**
     * Check if this artifact contains HTML content
     */
    public function isHtml(): bool
    {
        return $this->content_mime === 'text/html' || 
               str_ends_with($this->artifact_type, '.html');
    }

    /**
     * Get parsed JSON content (only for JSON artifacts)
     */
    public function getParsedJsonContent(): ?array
    {
        if (!$this->isJson() || empty($this->content)) {
            return null;
        }

        try {
            return json_decode($this->content, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            return null;
        }
    }

    /**
     * Get content with proper formatting for display
     */
    public function getFormattedContentAttribute(): string
    {
        if (empty($this->content)) {
            return 'No content available';
        }

        // For JSON, pretty-print it
        if ($this->isJson()) {
            $parsed = $this->getParsedJsonContent();
            if ($parsed !== null) {
                return json_encode($parsed, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            }
        }

        // For other content types, return as-is
        return $this->content;
    }

    /**
     * Get truncated content for preview
     */
    public function getContentPreviewAttribute(): string
    {
        if (empty($this->content)) {
            return 'Empty';
        }

        $preview = \Illuminate\Support\Str::limit($this->content, 100);
        
        // If it's JSON, try to show a meaningful preview
        if ($this->isJson()) {
            $parsed = $this->getParsedJsonContent();
            if ($parsed !== null && is_array($parsed)) {
                $keys = array_keys($parsed);
                $keyCount = count($keys);
                $keyPreview = implode(', ', array_slice($keys, 0, 3));
                
                if ($keyCount > 3) {
                    $keyPreview .= "... (+{" . ($keyCount - 3) . "} more)";
                }
                
                return "JSON with keys: {$keyPreview}";
            }
        }

        return $preview;
    }

    /**
     * Get formatted processing time for display
     */
    public function getFormattedProcessingTimeAttribute(): string
    {
        if (!$this->processing_time_ms) {
            return 'Unknown';
        }

        if ($this->processing_time_ms < 1000) {
            return $this->processing_time_ms . 'ms';
        }

        $seconds = $this->processing_time_ms / 1000;
        return round($seconds, 1) . 's';
    }

    /**
     * Scope to find artifacts for a specific step
     */
    public function scopeForStep($query, int $stepNumber)
    {
        return $query->where('step_number', $stepNumber);
    }

    /**
     * Scope to find artifacts by type
     */
    public function scopeByType($query, string $artifactType)
    {
        return $query->where('artifact_type', $artifactType);
    }

    /**
     * Scope to find JSON artifacts only
     */
    public function scopeJsonOnly($query)
    {
        return $query->where('content_mime', 'application/json')
                    ->orWhere('artifact_type', 'like', '%.json');
    }

    /**
     * Scope to find the final resume artifact (step 10)
     */
    public function scopeFinalResume($query)
    {
        return $query->where('step_number', 10);
    }

    /**
     * Set default created_at when creating
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function (JobSeekerAiTailorArtifact $artifact) {
            if (!$artifact->created_at) {
                $artifact->created_at = now();
            }
        });
    }
}

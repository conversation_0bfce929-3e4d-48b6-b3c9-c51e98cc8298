﻿!function(c,f,l,h){var g=c(f);c.fn.lazyload=function(a){function b(){var a=0;k.each(function(){var b=c(this);if(!(d.skip_invisible&&!b.is(":visible")||c.abovethetop(this,d)||c.leftofbegin(this,d)))if(c.belowthefold(this,d)||c.rightoffold(this,d)){if(++a>d.failure_limit)return!1}else b.trigger("appear"),a=0})}var e,k=this,d={threshold:0,failure_limit:0,event:"scroll",effect:"show",container:f,data_attribute:"original",skip_invisible:!1,appear:null,load:null,placeholder:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAANSURBVBhXYzh8+PB/AAffA0nNPuCLAAAAAElFTkSuQmCC"};
return a&&(h!==a.failurelimit&&(a.failure_limit=a.failurelimit,delete a.failurelimit),h!==a.effectspeed&&(a.effect_speed=a.effectspeed,delete a.effectspeed),c.extend(d,a)),e=d.container===h||d.container===f?g:c(d.container),0===d.event.indexOf("scroll")&&e.bind(d.event,function(){return b()}),this.each(function(){var a=this,b=c(a);a.loaded=!1;(b.attr("src")===h||!1===b.attr("src"))&&b.is("img")&&b.attr("src",d.placeholder);b.one("appear",function(){this.loaded||(d.appear&&d.appear.call(a,k.length,
d),c("\x3cimg /\x3e").bind("load",function(){var e=b.attr("data-"+d.data_attribute);b.hide();b.is("img")?b.attr("src",e):b.css("background-image","url('"+e+"')");b[d.effect](d.effect_speed);a.loaded=!0;e=c.grep(k,function(a){return!a.loaded});(k=c(e),d.load)&&d.load.call(a,k.length,d)}).attr("src",b.attr("data-"+d.data_attribute)))});0!==d.event.indexOf("scroll")&&b.bind(d.event,function(){a.loaded||b.trigger("appear")})}),g.bind("resize",function(){b()}),/(?:iphone|ipod|ipad).*os 5/gi.test(navigator.appVersion)&&
g.bind("pageshow",function(a){a.originalEvent&&a.originalEvent.persisted&&k.each(function(){c(this).trigger("appear")})}),c(l).ready(function(){b()}),this};c.belowthefold=function(a,b){var e;return e=b.container===h||b.container===f?(f.innerHeight?f.innerHeight:g.height())+g.scrollTop():c(b.container).offset().top+c(b.container).height(),e<=c(a).offset().top-b.threshold};c.rightoffold=function(a,b){var e;return e=b.container===h||b.container===f?g.width()+g.scrollLeft():c(b.container).offset().left+
c(b.container).width(),e<=c(a).offset().left-b.threshold};c.abovethetop=function(a,b){var e;return e=b.container===h||b.container===f?g.scrollTop():c(b.container).offset().top,e>=c(a).offset().top+b.threshold+c(a).height()};c.leftofbegin=function(a,b){var e;return e=b.container===h||b.container===f?g.scrollLeft():c(b.container).offset().left,e>=c(a).offset().left+b.threshold+c(a).width()};c.inviewport=function(a,b){return!(c.rightoffold(a,b)||c.leftofbegin(a,b)||c.belowthefold(a,b)||c.abovethetop(a,
b))};c.extend(c.expr[":"],{"below-the-fold":function(a){return c.belowthefold(a,{threshold:0})},"above-the-top":function(a){return!c.belowthefold(a,{threshold:0})},"right-of-screen":function(a){return c.rightoffold(a,{threshold:0})},"left-of-screen":function(a){return!c.rightoffold(a,{threshold:0})},"in-viewport":function(a){return c.inviewport(a,{threshold:0})},"above-the-fold":function(a){return!c.belowthefold(a,{threshold:0})},"right-of-fold":function(a){return c.rightoffold(a,{threshold:0})},
"left-of-fold":function(a){return!c.rightoffold(a,{threshold:0})}})}(jQuery,window,document);
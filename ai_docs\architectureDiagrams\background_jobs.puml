@startuml Background Job Processing

!theme vibrant

participant "Triggering Code (e.g., Controller, Service)" as Trigger
participant "Job Class" as Job
participant "Queue Facade/Bus" as QueueFacade
participant "Queue Connection (e.g., Redis, DB)" as QueueDriver
participant "Queue Worker" as Worker
participant "Job Handler (handle method)" as Handler
participant "Services / Database / etc." as Dependencies

activate Trigger
Trigger -> Job : new Job(data)
activate Job
Job --> Trigger : Job Instance Created
Trigger -> QueueFacade : dispatch(Job)
activate QueueFacade

QueueFacade -> QueueDriver : Push Job onto Queue
activate QueueDriver
QueueDriver --> QueueFacade : Job Queued
deactivate QueueDriver
QueueFacade --> Trigger : Job Dispatched
deactivate QueueFacade
deactivate Trigger

' ... Meanwhile, Worker process is running ...

activate Worker
Worker -> QueueDriver : Poll for Jobs
activate QueueDriver
QueueDriver --> Worker : Job Payload Found
deactivate QueueDriver

Worker -> Job : Deserialize Job from Payload
Job --> Worker : Job Instance Rehydrated

Worker -> Handler : Execute job->handle()
activate Handler
Handler -> Dependencies : Perform Task (e.g., DB query, API call)
activate Dependencies
Dependencies --> Handler : Task Result
deactivate Dependencies
Handler --> Worker : Job Execution Completed
deactivate Handler

Worker -> QueueDriver : Delete Job from Queue (on success)
activate QueueDriver
QueueDriver --> Worker : Job Deleted
deactivate QueueDriver

' Optional: Failure Handling
' alt Job Fails
' Handler -> Worker : Throws Exception
' Worker -> QueueDriver : Release Job back to Queue / Log Failure
' activate QueueDriver
' QueueDriver --> Worker : Job Released / Failure Logged
' deactivate QueueDriver
' end

deactivate Worker

@enduml 
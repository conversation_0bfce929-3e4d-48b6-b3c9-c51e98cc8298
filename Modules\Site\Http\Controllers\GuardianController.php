<?php

namespace Modules\Site\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Mail\GuardianCreated;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use App\Student;
use App\Guardian;

class GuardianController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        if(auth()->guard('guardian')->check()){
            $guardian = auth()->user();            
        }else{
            $guardian = [];
        }
        
        return view(theme_path("guardian.index"),compact('guardian'));
    }

    public function profile()
    {
        if(auth()->guard('guardian')->check()){
            $guardian = auth()->user();            
        }else{
            $guardian = [];
        }
        
        return view(theme_path("guardian.profile"),compact('guardian'));
    }


    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view(theme_path("create"));
    }

    /**
     * Show the form for registering  a new student.
     * @return Response
     */
     public function register($id = null)
     {
         if($id){
            $user = [];
            $student = Student::findOrFail($id);         
         }else{
             $user = [];
             $student = [];
         }
         return view(theme_path("guardian.register_student"),compact('user' , 'student'));
     }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $password = \Illuminate\Support\Str::random(8);
        if(auth()->guard('student')->check() || (auth()->guard('employee')->check() && auth()->user()->can("add guardian"))){
            if(!isset($request->password)){
                $request->merge(['password' => $password ]);
            }else{
                $password = $request->password;
            }

            if(!isset($request->name)){
                $request->merge(['name' => $request->full_name ]);
            }
            $request->merge(['organization_id' => config('organization_id') ]);
            
            $roles = [
                "name" => "required|min:3|max:64",
                "email" => "required|unique:guardians",
            ];
            // return $request->all();

            $this->validate_profile($request ,$roles);

            $request_data = $request->all();
            $request_data['password'] = bcrypt($password);

            $guardian = Guardian::create($request_data);

            if(auth()->guard('student')->check()){
                auth()->user()->guardian_id = $guardian->id;
                auth()->user()->status = 'profile_completed';
                auth()->user()->save();
            }

          //  Mail::to($guardian)->send(new GuardianCreated($request->all()));
            
        
            flash('Guardian Added !!');

        }else{
            flash('Error!!');

        }
        
        return redirect()->back();
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show()
    {
        return view(theme_path("home"));
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit()
    {
        return view(theme_path("guardian.update"));
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(Request $request, $id = null)
    { 
        if(
            (auth()->guard('guardian')->check() && auth()->user()->id == $id )
            || (auth()->guard('guardian')->check() )
        ){

//dd($request->update_guardian_profile);
        $roles = [];
        // "full_name": "Salah",
        // "full_name_trans": "Name",
        // "gender": "male",
        // "date_of_birth": "2017-08-16",
        // "nationality": "yem",
        // "identity_number": "43434",
        // "mobile": "172585816"
        if(isset($request->update_guardian_profile)){
           
            $this->validate_profile($request);
           // $id=$request->guardian_id;
            $guardian = Guardian::findOrFail($id);
            
            $request->merge(['status' => 'profile_completed']);
            
            $guardian->fill($request->all());

           
            $guardian->save();
            
            flash('Profile updated!!');
            return redirect()->back();
        }
            
        }
    }

    /**
    * Validate Guardian Profile data entry
    */

    public function validate_profile($request , $roles = [])
    {
        if(config("settings.guardian_form_full_name") == "required"){
            $roles["full_name"] = "required";
        }
        if(config("settings.guardian_form_full_name_trans") == "required"){
            $roles["full_name_trans"] = "required";
        }
        if(config("settings.guardian_form_full_name_language") == "required"){
            $roles["full_name_language"] = "required";
        }
        if(config("settings.guardian_form_gender") == "required"){
            $roles["gender"] = "required";
        }
        if(config("settings.guardian_form_date_of_birth") == "required"){
            $roles["date_of_birth"] = "required";
        }
        if(config("settings.guardian_form_occupation") == "required"){
            $roles["occupation"] = "required";
        }
        if(config("settings.guardian_form_nationality") == "required"){
            $roles["nationality"] = "required";
        }
        if(config("settings.guardian_form_mobile") == "required"){
            $roles["mobile"] = "required";
        }
        $this->validate($request, $roles);        

    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }
}

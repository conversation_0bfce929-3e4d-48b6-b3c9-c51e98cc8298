<?php

namespace Modules\Admission\Http\Controllers;

use App\Center;
use App\CenterTranslation;
use App\ClassStudent;
use App\Country;
use App\Notifications\SuperiorResetPassword;
use App\Notifications\UserStatusChangedToNewApplication;
use App\Organization;
use App\Program;
use App\Scopes\OrganizationScope;
use App\StudentAdmissionHefzPlan;
use App\StudentHefzReport;
use App\StudentRevisionPlan;
use App\StudentRevisionReport;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Faker\Factory;
use Faker\Provider\en_UG\PhoneNumber;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Mail\StudentCreated;
use App\Student;
use App\Guardian;
use App\Employee;
use App\Role;
use App\Cen_Emp;
use App\Admission;
use App\Classes;
use App\StudentHefzPlan;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Mail;
use PhpParser\Node\Stmt\Foreach_;
use Yajra\DataTables\Facades\DataTables;

class ShowAllStudentsToAddToClass extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function __invoke(Request $request)
    {



        DB::connection()->enableQueryLog();

        if ($request->ajax()) {


            $students = Admission::select('*')->whereRaw('admissions.organization_id = ' . config('organization_id'))
//                ->has('programs')
                ->with(['programs.programTranslations' => function ($query) {
                    $query->select('program_id', 'title');
                }])
                ->has('center')
                ->with('center.translations')
                ->has('student.user')
                ->with('student.user')
                ->with('student.hefz_plans') // includes softdeleted students
                ->with('interviews')
                ->orderByRaw('admissions.created_at desc');




            return DataTables::eloquent($students)
                ->filter(function ($query) use ($students, $request) {

                    if(($request->filled('status') && ($request->get('status') == 'archived'))) {


                        $query->onlyTrashed(); // includes softdeleted students

                        // includes softdeleted students
                    }


                        if(($request->filled('status') && ($request->get('status') == 'waiting_for_approval'))) {


                            $query->whereHas('student.hefz_plans', function ($q) {
                                $q->where('student_hefz_plans.status', 'waiting_for_approval');
                            });


                        }

                    if(($request->filled('status') && ($request->get('status') == 'waiting_for_interview'))) {

                        $query->whereHas('interviews', function ($query2) use ($request) {
//
                            $query2->where('admission_interviews.status', $request->get('status'));

                        });

                    }
                    if (request()->filled('programs')) {

                        $query->whereHas('programs', function ($query) {

                            $query->whereId(request('programs'));
                        });

                    }

                    if (request()->filled('centers')) {

                        $query->whereHas('center', function ($query) {

                            $query->whereId(request('centers'));
                        });

                    }
                    if (request()->filled('name')) {

                        $query->studentFullName(request('name'));
                    }

                    if (request()->filled('dateRange')) {
                        $range = explode(",", request('dateRange'));

                        $query->whereBetween('created_at', $range);
                    }

                    // TODO
                    if ($request->filled('minAge') && $request->filled('maxAge')) {

                        $minDate = Carbon::now()->subYear($request->get('minAge'))->toDateString();
                        $maxDate = Carbon::now()->subYear($request->get('maxAge'))->toDateString();

                        $query->ageBetween($maxDate, $minDate);
                    }



                    if ($request->filled('status') && ($request->get('status') == 'active')) {


                        // query scope inside Admission Model
                        $query->where('status', $request->get('status'));
                    }
                    if ($request->filled('status') && (($request->get('status') == 'new_admission') || $request->get('status') == 'New Admission')) {


                        // query scope inside Admission Model
                        $query->where('status', $request->get('status'));
                    }
                    if ($request->filled('status') && ($request->get('status') == 'rejected')) {


                        // query scope inside Admission Model
                        $query->where('status', $request->get('status'));
                    }
                    if ($request->filled('status') && ($request->get('status') == 'accepted')) {


                        // query scope inside Admission Model
                        $query->where('status', $request->get('status'));
                    }
                    if ($request->filled('status') && ($request->get('status') == 'waiting_for_payment')) {


                        // query scope inside Admission Model
                        $query->where('status', $request->get('status'));
                    }

                    if ($request->filled('status') && ($request->get('status') == 'offered')) {


                        // query scope inside Admission Model
                        $query->where('status', $request->get('status'));
                    }
                    if ($request->filled('status') && ($request->get('status') == 'reapplication')) {


                        // query scope inside Admission Model
                        $query->where('status', $request->get('status'));
                    }
//                    if ($request->filled('status') && ($request->get('status') == 'waiting_for_interview')) {
//                        $query->whereHas('interviews', function ($query2) use($request) {
//
//                            $query2->where('status',$request->get('status'));
//                        });
//                    }
                    if ($request->filled('status') && $request->get('status') == 'waiting_for_approval') {


                        $query->whereHas('student.hefz_plans', function ($q) use ($request) {
                            $q->where('status', $request->get('status'));
                        });


                        // query scope inside Admission Model
//                            $query->planWaitingApproval($request->get('status'));
                    }


                    if (request()->filled('gender')) {
                        $query->whereHas('student', function ($query) {

                            $query->where('gender', request('gender'));
                        });
                    }


                }, true)
                ->addIndexColumn()
//                ->addColumn('login', function ($row) use ($request) {
//
//
//                    if (is_null($row->deleted_at)) {
//                        $impersonationRoute = route('multiAuthImpersonate', ['id' => $row->student()->first()->user_id, 'guardName' => 'web']);
//                        return '<span class="badge badge-primary badge-pill"><a
//                                style="color: white; padding: 0px; background: transparent "
//                                href="' . $impersonationRoute . '">login</a></span>';
//                    }
//
//
//                })
                ->addColumn('identity_number', function ($row) use ($request) {

                    return Str::upper($row->student->identity_number);


                })
                ->addColumn('nationality', function ($row) use ($request) {

                    return Str::upper($row->student->nationality);


                })
                ->addColumn('status', function ($row) use ($request) {
//                    switch ($request->get('status')) {
//                        case "waiting_for_interview":
//                            $status = "Waiting for Interview";
//                            break;
//                        default:
//                            $status = $row->status;
//                    }


                    return $row->status;


                })
                ->addColumn('full_name', function ($row) use ($request) {





                    return $row->student->full_name;


                })
                ->addColumn('email', function ($row) use ($request) {





                    return $row->student->email;


                })
                ->addColumn('mobile', function ($row) use ($request) {


                    return  $row->student->mobile ;




                })

//                ->addColumn('email', function ($row) use ($request) {
//
//
//                    $details = '';
//
//                    $email = Str::title($row->student->email);
//                    if (strlen($row->student->email) > 24) {
//                        $email = Str::limit(Str::title($email), 24, ' ...');
//
//                        $details .= '<strong  data-tooltip="' . Str::title($row->student->email) . '" style="color: #34b8bc">' . $email . '</strong><br>';
//                    } else {
//                        $details .= '<strong  style="color: #34b8bc">' . $email . '</strong><br>';
//
//                    }
//                    $details .= $row->student->mobile . '<br>';
//
//                    return $details;
//                })
                ->addColumn('full_name_trans', function ($row) use ($request) {


                    $stShowRoute = route('students.show', ['id' => $row->student->user->id]);
                    $genderColor = $row->student->gender == 'Male' || $row->student->gender == 'male' ? '#34b8bc;!important' : '#FA5661;!important';

                    if (strlen($row->student->full_name_trans) > 13) {
//                            $fullname = Str::limit(Str::title($row->student->full_name),13,' ...');
                        $fullname = Str::title($row->student->full_name_trans);


                        return '<a style="color:' . $genderColor . '" target="_blank" href="' . $stShowRoute . '" ><strong data-tooltip="' . Str::title($row->student->full_name_trans) . '" >' . $fullname . '</strong></a>';
                    } else {
                        $fullname = Str::title($row->student->full_name_trans);
                        return '<a style="color:' . $genderColor . '" target="_blank" href="' . $stShowRoute . '" ><strong data-tooltip="' . Str::title($row->student->full_name_trans) . '" style="color:"' . $genderColor . '">' . $fullname . '</strong></a>';
//                            return '<a target="_blank" href="' . $stShowRoute . '" ><strong  style="color: #34b8bc">' .$fullname. '</strong></a>';
                    }

//                        return  '<span  style="color: white;> '.$row->student->full_name_trans.'</span>';

                })
                ->editColumn('date_of_birth', function ($row) use ($request) {
                    $ageGroupMetaData = '<div style="font-size: xx-small;" class="ui icon button" data-tooltip="' . optional($row->student->date_of_birth)->toDateString() . '" data-inverted="">
                        ' . optional($row->student->date_of_birth)->toDateString() . '
                            </div>';
                    return $ageGroupMetaData;

//                        return $row->student->date_of_birth->toDateString();

                })
                ->addColumn('image', function ($row) use ($request) {


                    $stShowRoute = route('students.show', ['id' => $row->student->user->id]);
                    $genderColor = $row->student->gender == 'Male' || $row->student->gender == 'male' ? '#34b8bc;!important' : '#FA5661;!important';
                    $fulllnameHtml = '';
                    if (strlen($row->student->full_name) > 13) {
                        $fullname = Str::title($row->student->full_name);


                        $fulllnameHtml = '<a style="color:' . $genderColor . '" target="_blank" href="' . $stShowRoute . '" ><strong  style="font-size: 16px;" data-tooltip="' . Str::title($row->student->full_name) . '" >' . $fullname . '</strong></a>';
                    } else {
                        $fullname = Str::title($row->student->full_name);
                        $fulllnameHtml = '<a style="color:' . $genderColor . '" target="_blank" href="' . $stShowRoute . '" ><strong style="font-size: 16px;" data-tooltip="' . Str::title($row->student->full_name) . '" style="color:"' . $genderColor . '">' . $fullname . '</strong></a>';
//                            return '<a target="_blank" href="' . $stShowRoute . '" ><strong  style="color: #34b8bc">' .$fullname. '</strong></a>';
                    }

                    $emailDetails = '';

                    $email = Str::title($row->student->email);
                    $emailHtml = '';
                    if (strlen($row->student->email) > 24) {
                        $email = Str::limit(Str::title($email), 24, ' ...');

                        $emailHtml .= '<strong  data-tooltip="' . Str::title($row->student->email) . '" style="color: #34b8bc ;    top: 1px;">' . $email . '</strong><br>';
                    } else {
                        $emailHtml .= '<strong  style="color: #34b8bc;    top: 10px;">' . $email . '</strong><br>';

                    }

                    $genderBasedDefaultImage = $row->student->gender == 'Male' || $row->student->gender == 'male' ? asset('uploads/staff/demo/mstaff.jfif') : asset('uploads/staff/demo/fstaff.jpg');
                    $applicationDateHtml = value($row['created_at'])->diffForHumans();
                    $image = file_exists($row->student->student_photo) ? asset($row->student->student_photo) : $genderBasedDefaultImage;

                    $result = ' <div class="row">
      <div class="col-md-4" style="
         margin-top: 39px;
         "><img style="border-radius: 50%" width="100" height="100" src="' . $image . '"></div>
   </div>
   <div class="row" style="margin-top: -20px;">
      <div class="col-md-12" style="
         font-weight: bolder;
         font-size: 21px;padding-bottom: 8px;
         ">' . $fulllnameHtml . '</div>
   </div>
   <div class="row">
      <div class="col-md-12" style="color: #34b8bc;"><i class="envelope icon"></i>: ' . $emailHtml . '</div>
   </div>
   <div class="row">
      <div class="col-md-12" style="color: #34b8bc "><i class="phone icon"></i>: ' . $row->student->mobile . '</div>
   </div>
   <div class="row">
      <div style="color: #34b8bc " data-tooltip= ' . value($row['created_at'])->format('Y-d-M  g:i a') . ' class="col-md-12"><i class="calendar alternate outline icon"></i>: Joined ' . $applicationDateHtml . '</div>
   </div>';
                    return $result;


                    return;
                    return '<img class=""  src="' . file_exists($row->student->student_photo) ? asset($row->student->student_photo) : asset('public/uploads/staff/demo/staff.jpg') . '" width="100" height="100" alt="' . asset($row->student->student_photo) . '">';


                })
//                ->addColumn('created_at', function ($row) {
//
//                    return '<div class="ui icon button" data-tooltip="' . value($row['created_at'])->format('Y-d-M  g:i a') . '">' . value($row['created_at'])->diffForHumans() . '</div>';
//                    return value($row['created_at'])->diffForHumans();
//                })
                ->addColumn('center', function ($row) {


                    return $row->center->translations->filter(function ($value, $key) {
                        return $value->locale == 'en';
                    })->map(function ($center) {
                        return Str::limit($center->name, 30, '...');
                    })->implode('<br>');
                })
                ->addColumn('program', function ($row) {
                    return $row->programs->map(function ($program) {

                        return Str::limit($program->programTranslations->first()->title, 30, '...');
                    })->implode('<br>');
                })
                ->addColumn('mobile', function ($row) {
                    $mobileNumbers = '';
                    if ($row->student->mobile == true) {
                        $mobileNumbers .= '<a class="ui label">' . $row->student->mobile . '</a>&nbsp;';
                    }
                    if ($row->student->mobile_2 == true) {
                        $mobileNumbers .= '<a class="ui  label">' . $row->student->mobile_2 . '</a>&nbsp;';
                    }
                    return $mobileNumbers;
                })
                ->addColumn('username', function ($row) {

                    return $row->student->user->username;

                })
                ->addColumn('action', function ($row) {


                    $route = route('students.show', [$row->student->user->id]);

                    $btn = '<a class="ui button teal" target="_blank" href="' . $route . '" >Add</a>';

                    return $btn;
                })
                ->rawColumns(['center', 'action', 'login', 'mobile', 'created_at', 'full_name', 'full_name_trans', 'email', 'image', 'date_of_birth'])
                ->make(true);

        }


        $filter_name = $request->filter_name;
        $filter = $request->filter;

        $perPage = 25;
        $students = Student::orderBy('id', 'DESC');
        // chackif user is supervisor

        $super = Employee::whereHas('roles', function ($q) {
            return $q->where('name', 'like', 'supervisor_%_');
        })
            ->where('id', '=', auth()->user()->id)->first();

        ///////////////////////


        if (isset($request->filter_name)) {
            $students->where('display_name', 'like', '%' . $request->filter_name . '%')
                ->orWhere('full_name', 'like', '%' . $request->filter_name . '%');
        } elseif (isset($request->filter) && $request->filter == "application_waiting_approval") {
            $students->where('status', '!=', 'active')
                ->where('status', '!=', 'suspended')
                ->where('status', '!=', 'graduated');
        } elseif (isset($request->filter) && $request->filter == "plan_waiting_approval") {
            $students->whereHas('hefz_plans', function ($query) {
                $query->where('status', 'waiting_for_approval');
            });
        } else {
        }

        //////////////  supervisor////////////////////////////////////
        if (isset($super)) {

            $cen_emp = DB::table('cen_emps')->where('emp_id', '=', auth()->user()->id)->first();
            if (isset($cen_emp)) {
                $center = $cen_emp->cen_id;
                $students = null;
                $addmision = Admission::where('center_id', '=', $center)->pluck('student_id');

                $students = Student::whereIn('id', $addmision);
                if (isset($request->filter_name)) {
                    $students->where('display_name', 'like', '%' . $request->filter_name . '%')
                        ->orWhere('full_name', 'like', '%' . $request->filter_name . '%')
                        ->whereIn('id', $addmision);
                } elseif (isset($request->filter) && $request->filter == "application_waiting_approval") {

                    $students->where('status', '!=', 'active')
                        ->where('status', '!=', 'suspended')
                        ->where('status', '!=', 'graduated')
                        ->whereIn('id', $addmision);
                } elseif (isset($request->filter) && $request->filter == "plan_waiting_approval") {
                    $students->whereHas('hefz_plans', function ($query) {
                        $query->where('status', 'waiting_for_approval');
                    })->whereIn('id', $addmision);
                }
            }
//                else {
//                    flash('You Do Not Have Permission To Access');
//                    return redirect()->back();
//                }
        }
        //////////////  end of supervisor////////////////////////////////////


        $centers = DB::select("SELECT center_translations.name AS 'name', centers.id AS 'value'
            FROM centers, center_translations
            WHERE centers.id = center_translations.center_id AND center_translations.locale ='en' AND
             deleted_at IS NULL
            ORDER BY center_translations.name ASC");
        $programs = DB::select("SELECT programs.id AS value, program_translations.title AS name FROM programs,program_translations WHERE programs.id = program_translations.program_id  and program_translations.locale= 'en'");


        $studentsAgeGroups = DB::select("select ageGroup, count(age) ageGroupCount from (select case 
                                            when age >= 18 and age <= 30 then 'Young adults'
                                            when age >= 31 and age <= 45 then 'Middle-aged Adults'
                                            when age >=45 then 'Old-aged Adults'
                                            else 'youth' end as ageGroup,age
                                        from (select YEAR(CURRENT_TIMESTAMP) - YEAR(date_of_birth)
                                                         - (RIGHT(CURRENT_TIMESTAMP, 5) < RIGHT(date_of_birth, 5)) as age
                                              from employees) a
                                        
                                        
                                        order by ageGroup) b group by ageGroup order by ageGroupCount desc"
        );

        $studentsAgeGroups = collect($studentsAgeGroups)->map(function ($details) {

            if ($details->ageGroup == 'Young adults') {
                $details->ageTip = 'age 18 to 30';

            } else if ($details->ageGroup == 'Middle-aged Adults') {


                $details->ageTip = 'age 31 to 45';

            } else if ($details->ageGroup == 'Old-aged Adults') {


                $details->ageTip = 'age >=45';

            } else {


                $details->ageTip = 'age <31';

            }


            return $details;

        });


        return view("admission::student.index", compact('students', 'filter_name', 'filter', 'items', 'statuses', 'centers', 'programs', 'studentsAgeGroups'));


    }



}
<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;

class LeaveRequest extends Model
{

//    use SoftDeletes;

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
    }

    public function leaveType()
	{
	  return $this->belongsTo('App\LeaveType', 'type_id');
	}

	public function leaveDefine()
	{
	  return $this->belongsTo('App\LeaveDefine', 'leave_define_id', 'id');
	}

	public function employees()
	{
	  return $this->belongsTo('App\Employee', 'employee_id');
	}
	public function student()
	{
	  return $this->belongsTo('App\Student', 'employee_id', 'user_id');
	}

	public static function approvedLeave($type_id ){
		
		try {
			$user = Auth::user();
				$leaves = LeaveRequest::where('role_id', getMaxLeaveDaysRoleId($user->id))->where('employee_id', $user->id)->where('leave_define_id', $type_id)->where('approve_status', "A")->count();


//				$approved_days = 0;
//				foreach($leaves as $leave){
//					$start = strtotime($leave->leave_from);
//					$end = strtotime($leave->leave_to);
//					$days_between = ceil(abs($end - $start) / 86400);
//					$days = $days_between + 1;
//					$approved_days += $days;
//				}

				return $leaves;
//				return $approved_days;
		} catch (\Exception $e) {
			$data=[];
			return $data;
		}
	}

	public static function approvedLeaveModal($type_id, $role_id, $employee_id){


		try {
			$leaves = LeaveRequest::where('role_id', $role_id)->where('employee_id', $employee_id)->where('leave_define_id', $type_id)->where('approve_status', "A")->get();
				$approved_days = 0;
				foreach($leaves as $leave){
					$start = strtotime($leave->leave_from);
					$end = strtotime($leave->leave_to);
					$days_between = ceil(abs($end - $start) / 86400);
					$days = $days_between + 1;
					$approved_days += $days;
				}
				return $approved_days;
		} catch (\Exception $e) {
			$data=[];
			return $data;
		}
	}
}

!function(){"use strict";var e=function(t){var n=t,r=function(){return n};return{get:r,set:function(e){n=e},clone:function(){return e(r())}}},t=tinymce.util.Tools.resolve("tinymce.PluginManager"),n=function(e){return!(!/(^|[ ,])powerpaste([, ]|$)/.test(e.settings.plugins)||!t.get("powerpaste")||("undefined"!=typeof window.console&&window.console.log&&window.console.log("PowerPaste is incompatible with Paste plugin! Remove 'paste' from the 'plugins' option."),0))},r=function(e,t){return{clipboard:e,quirks:t}},a=function(e,t,n,r){return e.fire("PastePreProcess",{content:t,internal:n,wordContent:r})},i=function(e,t,n,r){return e.fire("PastePostProcess",{node:t,internal:n,wordContent:r})},o=function(e,t){return e.fire("PastePlainTextToggle",{state:t})},s=function(e,t){return e.fire("paste",{ieFake:t})},l={shouldPlainTextInform:function(e){return e.getParam("paste_plaintext_inform",!0)},shouldBlockDrop:function(e){return e.getParam("paste_block_drop",!1)},shouldPasteDataImages:function(e){return e.getParam("paste_data_images",!1)},shouldFilterDrop:function(e){return e.getParam("paste_filter_drop",!0)},getPreProcess:function(e){return e.getParam("paste_preprocess")},getPostProcess:function(e){return e.getParam("paste_postprocess")},getWebkitStyles:function(e){return e.getParam("paste_webkit_styles")},shouldRemoveWebKitStyles:function(e){return e.getParam("paste_remove_styles_if_webkit",!0)},shouldMergeFormats:function(e){return e.getParam("paste_merge_formats",!0)},isSmartPasteEnabled:function(e){return e.getParam("smart_paste",!0)},isPasteAsTextEnabled:function(e){return e.getParam("paste_as_text",!1)},getRetainStyleProps:function(e){return e.getParam("paste_retain_style_properties")},getWordValidElements:function(e){return e.getParam("paste_word_valid_elements","-strong/b,-em/i,-u,-span,-p,-ol,-ul,-li,-h1,-h2,-h3,-h4,-h5,-h6,-p/div,-a[href|name],sub,sup,strike,br,del,table[width],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody")},shouldConvertWordFakeLists:function(e){return e.getParam("paste_convert_word_fake_lists",!0)},shouldUseDefaultFilters:function(e){return e.getParam("paste_enable_default_filters",!0)}},u=function(e,t,n){var r,a,i;"text"===t.pasteFormat.get()?(t.pasteFormat.set("html"),o(e,!1)):(t.pasteFormat.set("text"),o(e,!0),i=e,!1===n.get()&&l.shouldPlainTextInform(i)&&(a="Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.",(r=e).notificationManager.open({text:r.translate(a),type:"info"}),n.set(!0))),e.focus()},c=function(e,t,n){e.addCommand("mceTogglePlainTextPaste",function(){u(e,t,n)}),e.addCommand("mceInsertClipboardContent",function(e,n){n.content&&t.pasteHtml(n.content,n.internal),n.text&&t.pasteText(n.text)})},f=tinymce.util.Tools.resolve("tinymce.Env"),d=tinymce.util.Tools.resolve("tinymce.util.Delay"),m=tinymce.util.Tools.resolve("tinymce.util.Tools"),p=tinymce.util.Tools.resolve("tinymce.util.VK"),g="x-tinymce/html",v="\x3c!-- "+g+" --\x3e",h={mark:function(e){return v+e},unmark:function(e){return e.replace(v,"")},isMarked:function(e){return-1!==e.indexOf(v)},internalHtmlMime:function(){return g}},y=tinymce.util.Tools.resolve("tinymce.html.Entities"),b=function(e){return e.replace(/\r?\n/g,"<br>")},x=function(e,t,n){var r=e.split(/\n\n/),a=function(e,t){var n,r=[],a="<"+e;if("object"==typeof t){for(n in t)t.hasOwnProperty(n)&&r.push(n+'="'+y.encodeAllRaw(t[n])+'"');r.length&&(a+=" "+r.join(" "))}return a+">"}(t,n),i="</"+t+">",o=m.map(r,function(e){return e.split(/\n/).join("<br />")});return 1===o.length?o[0]:m.map(o,function(e){return a+e+i}).join("")},P={isPlainText:function(e){return!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(e)},convert:function(e,t,n){return t?x(e,t,n):b(e)},toBRs:b,toBlockElements:x},w=tinymce.util.Tools.resolve("tinymce.html.DomParser"),T=tinymce.util.Tools.resolve("tinymce.html.Node"),_=tinymce.util.Tools.resolve("tinymce.html.Schema"),C=tinymce.util.Tools.resolve("tinymce.html.Serializer");function D(e,t){return m.each(t,function(t){e=t.constructor===RegExp?e.replace(t,""):e.replace(t[0],t[1])}),e}var k={filter:D,innerText:function(e){var t=_(),n=w({},t),r="",a=t.getShortEndedElements(),i=m.makeMap("script noscript style textarea video audio iframe object"," "),o=t.getBlockElements();return e=D(e,[/<!\[[^\]]+\]>/g]),function s(e){var t=e.name,n=e;if("br"!==t)if(a[t]&&(r+=" "),i[t])r+=" ";else{if(3===e.type&&(r+=e.value),!e.shortEnded&&(e=e.firstChild))for(;s(e),e=e.next;);o[t]&&n.next&&(r+="\n","p"===t&&(r+="\n"))}else r+="\n"}(n.parse(e)),r},trimHtml:function(e){return e=D(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,function(e,t,n){return t||n?"\xa0":" "}],/<br class="Apple-interchange-newline">/g,/<br>$/i])},createIdGenerator:function(e){var t=0;return function(){return e+t++}},isMsEdge:function(){return-1!==navigator.userAgent.indexOf(" Edge/")}};function R(e){var t,n;return n=[/^[IVXLMCD]{1,2}\.[ \u00a0]/,/^[ivxlmcd]{1,2}\.[ \u00a0]/,/^[a-z]{1,2}[\.\)][ \u00a0]/,/^[A-Z]{1,2}[\.\)][ \u00a0]/,/^[0-9]+\.[ \u00a0]/,/^[\u3007\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d]+\.[ \u00a0]/,/^[\u58f1\u5f10\u53c2\u56db\u4f0d\u516d\u4e03\u516b\u4e5d\u62fe]+\.[ \u00a0]/],e=e.replace(/^[\u00a0 ]+/,""),m.each(n,function(n){if(n.test(e))return t=!0,!1}),t}function E(e){var t,n,r=1;function a(e){var t="";if(3===e.type)return e.value;if(e=e.firstChild)for(;t+=a(e),e=e.next;);return t}function i(e,t){if(3===e.type&&t.test(e.value))return e.value=e.value.replace(t,""),!1;if(e=e.firstChild)do{if(!i(e,t))return!1}while(e=e.next);return!0}function o(e,a,o){var s=e._listLevel||r;s!==r&&(s<r?t&&(t=t.parent.parent):(n=t,t=null)),t&&t.name===a?t.append(e):(n=n||t,t=new T(a,1),o>1&&t.attr("start",""+o),e.wrap(t)),e.name="li",s>r&&n&&n.lastChild.append(t),r=s,function l(e){if(e._listIgnore)e.remove();else if(e=e.firstChild)for(;l(e),e=e.next;);}(e),i(e,/^\u00a0+/),i(e,/^\s*([\u2022\u00b7\u00a7\u25CF]|\w+\.)/),i(e,/^\u00a0+/)}for(var s=[],l=e.firstChild;null!=l;)if(s.push(l),null!==(l=l.walk()))for(;void 0!==l&&l.parent!==e;)l=l.walk();for(var u=0;u<s.length;u++)if("p"===(e=s[u]).name&&e.firstChild){var c=a(e);if(/^[\s\u00a0]*[\u2022\u00b7\u00a7\u25CF]\s*/.test(c)){o(e,"ul");continue}if(R(c)){var f=/([0-9]+)\./.exec(c),d=1;f&&(d=parseInt(f[1],10)),o(e,"ol",d);continue}if(e._listLevel){o(e,"ul",1);continue}t=null}else n=t,t=null}function M(e,t,n,r){var a,i={},o=e.dom.parseStyle(r);return m.each(o,function(o,s){switch(s){case"mso-list":(a=/\w+ \w+([0-9]+)/i.exec(r))&&(n._listLevel=parseInt(a[1],10)),/Ignore/i.test(o)&&n.firstChild&&(n._listIgnore=!0,n.firstChild._listIgnore=!0);break;case"horiz-align":s="text-align";break;case"vert-align":s="vertical-align";break;case"font-color":case"mso-foreground":s="color";break;case"mso-background":case"mso-highlight":s="background";break;case"font-weight":case"font-style":return void("normal"!==o&&(i[s]=o));case"mso-element":if(/^(comment|comment-list)$/i.test(o))return void n.remove()}0!==s.indexOf("mso-comment")?0!==s.indexOf("mso-")&&("all"===l.getRetainStyleProps(e)||t&&t[s])&&(i[s]=o):n.remove()}),/(bold)/i.test(i["font-weight"])&&(delete i["font-weight"],n.wrap(new T("b",1))),/(italic)/i.test(i["font-style"])&&(delete i["font-style"],n.wrap(new T("i",1))),(i=e.dom.serializeStyle(i,n.name))||null}var S={preProcess:function(e,t){return l.shouldUseDefaultFilters(e)?function(e,t){var n,r;(n=l.getRetainStyleProps(e))&&(r=m.makeMap(n.split(/[, ]/))),t=k.filter(t,[/<br class="?Apple-interchange-newline"?>/gi,/<b[^>]+id="?docs-internal-[^>]*>/gi,/<!--[\s\S]+?-->/gi,/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|img|meta|link|style|\w:\w+)(?=[\s\/>]))[^>]*>/gi,[/<(\/?)s>/gi,"<$1strike>"],[/&nbsp;/gi,"\xa0"],[/<span\s+style\s*=\s*"\s*mso-spacerun\s*:\s*yes\s*;?\s*"\s*>([\s\u00a0]*)<\/span>/gi,function(e,t){return t.length>0?t.replace(/./," ").slice(Math.floor(t.length/2)).split("").join("\xa0"):""}]]);var a=l.getWordValidElements(e),i=_({valid_elements:a,valid_children:"-li[p]"});m.each(i.elements,function(e){e.attributes["class"]||(e.attributes["class"]={},e.attributesOrder.push("class")),e.attributes.style||(e.attributes.style={},e.attributesOrder.push("style"))});var o=w({},i);o.addAttributeFilter("style",function(t){for(var n,a=t.length;a--;)(n=t[a]).attr("style",M(e,r,n,n.attr("style"))),"span"===n.name&&n.parent&&!n.attributes.length&&n.unwrap()}),o.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)n=(t=e[r]).attr("class"),/^(MsoCommentReference|MsoCommentText|msoDel)$/i.test(n)&&t.remove(),t.attr("class",null)}),o.addNodeFilter("del",function(e){for(var t=e.length;t--;)e[t].remove()}),o.addNodeFilter("a",function(e){for(var t,n,r,a=e.length;a--;)if(n=(t=e[a]).attr("href"),r=t.attr("name"),n&&-1!==n.indexOf("#_msocom_"))t.remove();else if(n&&0===n.indexOf("file://")&&(n=n.split("#")[1])&&(n="#"+n),n||r){if(r&&!/^_?(?:toc|edn|ftn)/i.test(r)){t.unwrap();continue}t.attr({href:n,name:r})}else t.unwrap()});var s=o.parse(t);return l.shouldConvertWordFakeLists(e)&&E(s),t=C({validate:e.settings.validate},i).serialize(s)}(e,t):t},isWordContent:function(e){return/<font face="Times New Roman"|class="?Mso|style="[^"]*\bmso-|style='[^'']*\bmso-|w:WordDocument/i.test(e)||/class="OutlineElement/.test(e)||/id="?docs\-internal\-guid\-/.test(e)}},F=function(e,t){return{content:e,cancelled:t}},I=function(e,t,n,r){var o,s,l,u,c,f,d=a(e,t,n,r);return e.hasEventListeners("PastePostProcess")&&!d.isDefaultPrevented()?(o=e,s=d.content,l=n,u=r,c=o.dom.create("div",{style:"display:none"},s),f=i(o,c,l,u),F(f.node.innerHTML,f.isDefaultPrevented())):F(d.content,d.isDefaultPrevented())},O=function(e,t,n){var r=S.isWordContent(t),a=r?S.preProcess(e,t):t;return I(e,a,n,r)},A=function(e,t){return e.insertContent(t,{merge:l.shouldMergeFormats(e),paste:!0}),!0},B=function(e){return/^https?:\/\/[\w\?\-\/+=.&%@~#]+$/i.test(e)},H=function(e){return B(e)&&/.(gif|jpe?g|png)$/.test(e)},N=function(e,t,n){return!(!1!==e.selection.isCollapsed()||!B(t)||(a=t,i=n,(r=e).undoManager.extra(function(){i(r,a)},function(){r.execCommand("mceInsertLink",!1,a)}),0));var r,a,i},L=function(e,t,n){return!!H(t)&&(a=t,i=n,(r=e).undoManager.extra(function(){i(r,a)},function(){r.insertContent('<img src="'+a+'">')}),!0);var r,a,i},$=function(e,t){var n,r;!1===l.isSmartPasteEnabled(e)?A(e,t):(n=e,r=t,m.each([N,L,A],function(e){return!0!==e(n,r,A)}))},W=function(e,t,n){var r=n||h.isMarked(t),a=O(e,h.unmark(t),r);!1===a.cancelled&&$(e,a.content)},j=function(e,t){t=e.dom.encode(t).replace(/\r\n/g,"\n"),t=P.convert(t,e.settings.forced_root_block,e.settings.forced_root_block_attrs),W(e,t,!1)},V=function(e){var t={};if(e){if(e.getData){var n=e.getData("Text");n&&n.length>0&&-1===n.indexOf("data:text/mce-internal,")&&(t["text/plain"]=n)}if(e.types)for(var r=0;r<e.types.length;r++){var a=e.types[r];try{t[a]=e.getData(a)}catch(i){t[a]=""}}}return t},z=function(e,t){return t in e&&e[t].length>0},K=function(e){return z(e,"text/html")||z(e,"text/plain")},U=function(e,t,n,r){var a=k.createIdGenerator("mceclip");t&&(e.selection.setRng(t),t=null);var i,o,s,l,u,c,f,d=n.result,m=-1!==(o=(i=d).indexOf(","))?i.substr(o+1):null,p=a(),g=e.settings.images_reuse_filename&&r.name?(s=e,l=r.name,(u=l.match(/([\s\S]+?)\.(?:jpeg|jpg|png|gif)$/i))?s.dom.encode(u[1]):null):p,v=new Image;if(v.src=d,c=e.settings,f=v,!c.images_dataimg_filter||c.images_dataimg_filter(f)){var h,y=e.editorUpload.blobCache,b=void 0;(h=y.findFirst(function(e){return e.base64()===m}))?b=h:(b=y.create(p,r,m,g),y.add(b)),W(e,'<img src="'+b.blobUri()+'">',!1)}else W(e,'<img src="'+d+'">',!1)},G=function(e,t,n){var r="paste"===t.type?t.clipboardData:t.dataTransfer;function a(r){var a,i,o,s=!1;if(r)for(a=0;a<r.length;a++)if(i=r[a],/^image\/(jpeg|png|gif|bmp)$/.test(i.type)){var l=i.getAsFile?i.getAsFile():i;(o=new window.FileReader).onload=U.bind(null,e,n,o,l),o.readAsDataURL(l),t.preventDefault(),s=!0}return s}if(e.settings.paste_data_images&&r)return a(r.items)||a(r.files)},X=function(e){return p.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode},q=function(e,t,n){var r,a=0;function i(n,r,a,i){var o,s;z(n,"text/html")?o=n["text/html"]:(o=t.getHtml(),i=i||h.isMarked(o),t.isDefaultContent(o)&&(a=!0)),o=k.trimHtml(o),t.remove(),s=!1===i&&P.isPlainText(o),o.length&&!s||(a=!0),a&&(o=z(n,"text/plain")&&s?n["text/plain"]:k.innerText(o)),t.isDefaultContent(o)?r||e.windowManager.alert("Please use Ctrl+V/Cmd+V keyboard shortcuts to paste contents."):a?j(e,o):W(e,o,i)}e.on("keydown",function(n){function i(e){X(e)&&!e.isDefaultPrevented()&&t.remove()}if(X(n)&&!n.isDefaultPrevented()){if((r=n.shiftKey&&86===n.keyCode)&&f.webkit&&-1!==navigator.userAgent.indexOf("Version/"))return;if(n.stopImmediatePropagation(),a=(new Date).getTime(),f.ie&&r)return n.preventDefault(),void s(e,!0);t.remove(),t.create(),e.once("keyup",i),e.once("paste",function(){e.off("keyup",i)})}}),e.on("paste",function(o){var s,l,u,c=(new Date).getTime(),p=(s=e,l=V(o.clipboardData||s.getDoc().dataTransfer),k.isMsEdge()?m.extend(l,{"text/html":""}):l),g=(new Date).getTime()-c,v=(new Date).getTime()-a-g<1e3,y="text"===n.get()||r,b=z(p,h.internalHtmlMime());r=!1,o.isDefaultPrevented()||(u=o.clipboardData,-1!==navigator.userAgent.indexOf("Android")&&u&&u.items&&0===u.items.length)?t.remove():K(p)||!G(e,o,t.getLastRng()||e.selection.getRng())?(v||o.preventDefault(),!f.ie||v&&!o.ieFake||z(p,"text/html")||(t.create(),e.dom.bind(t.getEl(),"paste",function(e){e.stopPropagation()}),e.getDoc().execCommand("Paste",!1,null),p["text/html"]=t.getHtml()),z(p,"text/html")?(o.preventDefault(),b||(b=h.isMarked(p["text/html"])),i(p,v,y,b)):d.setEditorTimeout(e,function(){i(p,v,y,b)},0)):t.remove()})},Y=function(e){return e.dom.get("mcepastebin")},Z=function(e,t){return t===e},J=function(t){var n=e(null),r="%MCEPASTEBIN%";return{create:function(){return function(e,t,n){var r,a,i=e.dom,o=e.getBody(),s=e.dom.getViewPort(e.getWin()).y,l=20;t.set(e.selection.getRng());var u=t.get();if(e.inline&&(a=e.selection.getScrollContainer())&&a.scrollTop>0&&(s=a.scrollTop),u.getClientRects){var c=function(e){var t,n,r,a=e.startContainer;if((t=e.getClientRects()).length)return t[0];if(e.collapsed&&1===a.nodeType){for(r=a.childNodes[u.startOffset];r&&3===r.nodeType&&!r.data.length;)r=r.nextSibling;if(r)return"BR"===r.tagName&&(n=i.doc.createTextNode("\ufeff"),r.parentNode.insertBefore(n,r),(e=i.createRng()).setStartBefore(n),e.setEndAfter(n),t=e.getClientRects(),i.remove(n)),t.length?t[0]:void 0}}(u);if(c)l=s+(c.top-i.getPos(o).y);else{l=s;var d=u.startContainer;d&&(3===d.nodeType&&d.parentNode!==o&&(d=d.parentNode),1===d.nodeType&&(l=i.getPos(d,a||o).y))}}r=e.dom.add(e.getBody(),"div",{id:"mcepastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: absolute; top: "+l+"px; width: 10px; height: 10px; overflow: hidden; opacity: 0"},n),(f.ie||f.gecko)&&i.setStyle(r,"left","rtl"===i.getStyle(o,"direction",!0)?65535:-65535),i.bind(r,"beforedeactivate focusin focusout",function(e){e.stopPropagation()}),r.focus(),e.selection.select(r,!0)}(t,n,r)},remove:function(){return function(e,t){if(Y(e)){for(var n=void 0,r=t.get();n=e.dom.get("mcepastebin");)e.dom.remove(n),e.dom.unbind(n);r&&e.selection.setRng(r)}t.set(null)}(t,n)},getEl:function(){return Y(t)},getHtml:function(){return function(e){var t,n,r,a,i,o=function(t,n){t.appendChild(n),e.dom.remove(n,!0)};for(n=m.grep(e.getBody().childNodes,function(e){return"mcepastebin"===e.id}),t=n.shift(),m.each(n,function(e){o(t,e)}),r=(a=e.dom.select("div[id=mcepastebin]",t)).length-1;r>=0;r--)i=e.dom.create("div"),t.insertBefore(i,a[r]),o(i,a[r]);return t?t.innerHTML:""}(t)},getLastRng:function(){return n.get()},isDefault:function(){return e=r,a=Y(t),(n=a)&&"mcepastebin"===n.id&&Z(e,a.innerHTML);var e,n,a},isDefaultContent:function(e){return Z(r,e)}}},Q=function(e,t){var n=J(e);return e.on("preInit",function(){return q(r=e,n,t),void r.parser.addNodeFilter("img",function(e,t,n){var i,o=function(e){e.attr("data-mce-object")||a===f.transparentSrc||e.remove()};if(!r.settings.paste_data_images&&(i=n).data&&!0===i.data.paste)for(var s=e.length;s--;)(a=e[s].attributes.map.src)&&(0===a.indexOf("webkit-fake-url")?o(e[s]):r.settings.allow_html_data_urls||0!==a.indexOf("data:")||o(e[s]))});var r,a}),{pasteFormat:t,pasteHtml:function(t,n){return W(e,t,n)},pasteText:function(t){return j(e,t)},pasteImageData:function(t,n){return G(e,t,n)},getDataTransferItems:V,hasHtmlOrText:K,hasContentType:z}},ee=function(){},te=function(e,t,n){if(r=e,!1!==f.iOS||r===undefined||"function"!=typeof r.setData||!0===k.isMsEdge())return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(h.internalHtmlMime(),t),!0}catch(a){return!1}var r},ne=function(e,t,n,r){te(e.clipboardData,t.html,t.text)?(e.preventDefault(),r()):n(t.html,r)},re=function(e){return function(t,n){var r=h.mark(t),a=e.dom.create("div",{contenteditable:"false","data-mce-bogus":"all"}),i=e.dom.create("div",{contenteditable:"true"},r);e.dom.setStyles(a,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),a.appendChild(i),e.dom.add(e.getBody(),a);var o=e.selection.getRng();i.focus();var s=e.dom.createRng();s.selectNodeContents(i),e.selection.setRng(s),setTimeout(function(){e.selection.setRng(o),a.parentNode.removeChild(a),n()},0)}},ae=function(e){return{html:e.selection.getContent({contextual:!0}),text:e.selection.getContent({format:"text"})}},ie=function(e){var t,n;e.on("cut",(t=e,function(e){!1===t.selection.isCollapsed()&&ne(e,ae(t),re(t),function(){setTimeout(function(){t.execCommand("Delete")},0)})})),e.on("copy",(n=e,function(e){!1===n.selection.isCollapsed()&&ne(e,ae(n),re(n),ee)}))},oe=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),se=function(e,t){return oe.getCaretRangeFromPoint(t.clientX,t.clientY,e.getDoc())},le=function(e,t){e.focus(),e.selection.setRng(t)},ue=function(e,t,n){l.shouldBlockDrop(e)&&e.on("dragend dragover draggesture dragdrop drop drag",function(e){e.preventDefault(),e.stopPropagation()}),l.shouldPasteDataImages(e)||e.on("drop",function(e){var t=e.dataTransfer;t&&t.files&&t.files.length>0&&e.preventDefault()}),e.on("drop",function(r){var a,i;if(i=se(e,r),!r.isDefaultPrevented()&&!n.get()){a=t.getDataTransferItems(r.dataTransfer);var o,s=t.hasContentType(a,h.internalHtmlMime());if((t.hasHtmlOrText(a)&&(!(o=a["text/plain"])||0!==o.indexOf("file://"))||!t.pasteImageData(r,i))&&i&&l.shouldFilterDrop(e)){var u=a["mce-internal"]||a["text/html"]||a["text/plain"];u&&(r.preventDefault(),d.setEditorTimeout(e,function(){e.undoManager.transact(function(){a["mce-internal"]&&e.execCommand("Delete"),le(e,i),u=k.trimHtml(u),a["text/html"]?t.pasteHtml(u,s):t.pasteText(u)})}))}}}),e.on("dragstart",function(e){n.set(!0)}),e.on("dragover dragend",function(t){l.shouldPasteDataImages(e)&&!1===n.get()&&(t.preventDefault(),le(e,se(e,t))),"dragend"===t.type&&n.set(!1)})},ce=function(e){var t=e.plugins.paste,n=l.getPreProcess(e);n&&e.on("PastePreProcess",function(e){n.call(t,t,e)});var r=l.getPostProcess(e);r&&e.on("PastePostProcess",function(e){r.call(t,t,e)})};function fe(e,t){e.on("PastePreProcess",function(n){n.content=t(e,n.content,n.internal,n.wordContent)})}function de(e,t){if(!S.isWordContent(t))return t;var n=[];m.each(e.schema.getBlockElements(),function(e,t){n.push(t)});var r=new RegExp("(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*(<\\/?("+n.join("|")+")[^>]*>)(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*","g");return t=k.filter(t,[[r,"$1"]]),t=k.filter(t,[[/<br><br>/g,"<BR><BR>"],[/<br>/g," "],[/<BR><BR>/g,"<br>"]])}function me(e,t,n,r){if(r||n)return t;var a,i=l.getWebkitStyles(e);if(!1===l.shouldRemoveWebKitStyles(e)||"all"===i)return t;if(i&&(a=i.split(/[, ]/)),a){var o=e.dom,s=e.selection.getNode();t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,function(e,t,n,r){var i=o.parseStyle(o.decode(n),"span"),l={};if("none"===a)return t+r;for(var u=0;u<a.length;u++){var c=i[a[u]],f=o.getStyle(s,a[u],!0);/color/.test(a[u])&&(c=o.toHex(c),f=o.toHex(f)),f!==c&&(l[a[u]]=c)}return(l=o.serializeStyle(l,"span"))?t+' style="'+l+'"'+r:t+r})}else t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return t=t.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,function(e,t,n,r){return t+' style="'+n+'"'+r})}function pe(e,t){e.$("a",t).find("font,u").each(function(t,n){e.dom.remove(n,!0)})}var ge=function(e){var t,n;f.webkit&&fe(e,me),f.ie&&(fe(e,de),n=pe,(t=e).on("PastePostProcess",function(e){n(t,e.node)}))},ve=function(e){return function(){return e}},he=(ve(!1),ve(!0),function(e){for(var t=new Array(arguments.length-1),n=1;n<arguments.length;n++)t[n-1]=arguments[n];return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];var a=t.concat(n);return e.apply(null,a)}}),ye=function(e,t,n){var r=n.control;r.active("text"===t.pasteFormat.get()),e.on("PastePlainTextToggle",function(e){r.active(e.state)})},be=function(e,t){var n=he(ye,e,t);e.addButton("pastetext",{active:!1,icon:"pastetext",tooltip:"Paste as text",cmd:"mceTogglePlainTextPaste",onPostRender:n}),e.addMenuItem("pastetext",{text:"Paste as text",selectable:!0,active:t.pasteFormat,cmd:"mceTogglePlainTextPaste",onPostRender:n})};t.add("paste",function(t){if(!1===n(t)){var a=e(!1),i=e(!1),o=e(l.isPasteAsTextEnabled(t)?"text":"html"),s=Q(t,o),u=ge(t);return be(t,s),c(t,s,a),ce(t),ie(t),ue(t,s,i),r(s,u)}})}();
<?php

namespace Modules\HumanResource\Http\Controllers;

use Barryvdh\DomPDF\Facade\Pdf;
use App\Employee;
use App\Http\Controllers\Controller;

class EmployeesTablesPDFController extends Controller
{
    public function downloadPDF($monthYear = null)
    {
        try {
            // Sample data

        // Fetch all employees ordered by ID
        $employees = Employee::orderBy('full_name')->get();

        // Set the number of employees per page
        $employeesPerPage = 6;

        // Calculate the total number of pages needed
        $totalEmployees = $employees->count();
        $totalPages = ceil($totalEmployees / $employeesPerPage);
        // Get the authenticated user's name
        $printedBy = auth()->user()->full_name;
        // Generate the PDF with the calculated data
        $pdf = PDF::loadView('humanresource::employees.pdf.all', [
            'employees' => $employees,
            'employeesPerPage' => $employeesPerPage,
            'totalPages' => $totalPages,
            'printedBy' => $printedBy
        ])->setPaper('a4', 'landscape');

        // Stream the generated PDF
        return $pdf->stream('employees.pdf');


        return $pdf->stream('employees.pdf');
        } catch (\Exception $e) {
            \Log::error('PDF Generation Error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to generate PDF'], 500);
        }
    }
}

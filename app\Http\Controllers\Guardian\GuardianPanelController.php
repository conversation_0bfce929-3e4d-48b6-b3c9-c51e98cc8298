<?php

namespace App\Http\Controllers\Guardian;

use App\BaseSetup;
use App\Classes;
use App\Organization;
use App\ProgramTranslation;
use App\Role;
use App\Book;
use App\Exam;
use App\Guardian;
use App\Holiday;
use App\Event;
use App\Student;
use App\Subject;
use App\Vehicle;
use App\Weekend;
use App\YearCheck;
use App\ExamType;
use App\Homework;
use App\RoomList;
use App\RoomType;
use App\BookIssue;
use App\ClassTime;
use App\LeaveType;
use App\FeesAssign;
use App\MarksGrade;
use App\OnlineExam;
use App\ApiBaseMethod;
use App\FeesPayment;
use App\LeaveDefine;
use App\NoticeBoard;
use App\AcademicYear;
use App\ExamSchedule;
use App\LeaveRequest;
use App\AssignSubject;
use App\AssignVehicle;
use App\DormitoryList;
use App\LibraryMember;
use Barryvdh\DomPDF\Facade as PDF;
use App\Document;
use App\StudentTimeline;
use App\StudentAttendance;
use Illuminate\Http\Request;
use App\FeesAssignDiscount;
use App\StudentTakeOnlineExam;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Modules\ApplicationCenter\Entities\RegistrationSetting;


class GuardianPanelController extends Controller
{

    public function __construct()
    {
//        $this->middleware('PM');
//         User::checkAuth();
    }

    public function parentDashboard()
    {
        try {




            $holidays = Holiday::get();


            $events = Event::where('active_status', 1)
//                ->where('academic_id', YearCheck::getAcademicId())
                
                ->where(function ($q) {
                    $q->where('for_whom', 'All')->orWhere('for_whom', 'Parents');
                })
                ->get();
          

//            return view('modules.site.templates.wajeha.guardian.parent_dashboard', compact('holidays', 'events'));
            return view('modules.site.templates.wajeha.backEnd.parentPanel.parent_dashboard', compact('holidays', 'events'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentApplication()
    {



            try {
                $organizations = Organization::all();
                $classes = Classes::all();
                $programs = ProgramTranslation::where("locale","en")->get();

                $genders = BaseSetup::where('base_group_id', '=', '1')->get();
                $reg_setting = RegistrationSetting::find(1);

                return view('modules.site.templates.wajeha.backEnd.parentPanel.student_application', compact('organizations', 'classes', 'programs', 'genders', 'reg_setting'));
            } catch (\Exception $e) {


                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }

    }

    public function myChildren($id)
    {


        // return $id;
        $parent_info = Guardian::where('user_id', Auth::user()->id)->first();
        // return $parent_info;


        try {
            $student_detail = Student::where('id', $id)->where('guardian_id', $parent_info->id)->with('admissions.programs')->with('admissions.center')->with('admissions.class')->with('parents')->first();

            if ($student_detail) {

                $fees_assigneds = FeesAssign::where('student_id', $student_detail->id)->get();

                $fees_discounts = FeesAssignDiscount::where('student_id', $student_detail->id)->get();
                $documents = Document::where('documentable_id', $student_detail->id)->where('type', 'stu')->get();
                $timelines = StudentTimeline::where('staff_student_id', $student_detail->id)->where('type', 'stu')->where('visible_to_student', 1)->get();
                $classIds = $student_detail->joint_classes()->get();


                $studentClasses = collect($classIds)->map(function ($item, $key) {

                    return $item->id;
                });
                $studentClasses = $studentClasses->toArray();

                $exams = ExamSchedule::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();

                $grades = MarksGrade::get();
                $academic_year = AcademicYear::where('id', $student_detail->session_id)->first();
                $exam_terms = ExamType::all();
                // $exam_terms = [];



                return view('modules.site.templates.wajeha.backEnd.parentPanel.my_children', compact('student_detail', 'fees_assigneds', 'driver', 'fees_discounts', 'exams', 'documents', 'timelines', 'grades', 'exam_terms','academic_year'));
            } else {
                Toastr::warning('Invalid Student ID', 'Invalid');
                return redirect()->back();
            }
        } catch (\Exception $e) {
//            Toastr::error('Operation Failed', 'Failed');
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }

    public function onlineExamination($id)
    {

        try {
            // $student = Auth::user()->student;
            $student = Student::findOrfail($id);
            $now = date('H:i:s');
            // ->where('start_time', '<', $now)
            $online_exams = OnlineExam::where('status', 1)->where('class_id', $student->class_id)->where('section_id', $student->section_id)->get();
            // return $online_exams;
            $marks_assigned = [];
            foreach ($online_exams as $online_exam) {
                $exam = StudentTakeOnlineExam::where('online_exam_id', $online_exam->id)->where('student_id', $student->id)->where('status', 2)->first();
                if ($exam != "") {
                    $marks_assigned[] = $exam->online_exam_id;
                }
            }
            return view('modules.site.templates.wajeha.backEnd.parentPanel.parent_online_exam', compact('online_exams', 'marks_assigned', 'now', 'student'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function parentLeave($id)
    {

        try {
            // return $id;
            $student = Student::findOrfail($id);
            $apply_leaves = LeaveRequest::where('employee_id', '=', $student->user_id)
                ->join('leave_defines', 'leave_defines.id', '=', 'leave_requests.leave_define_id')
                ->join('leave_types', 'leave_types.id', '=', 'leave_defines.type_id')
                ->where('leave_requests.academic_id', YearCheck::getAcademicId())
                ->where('leave_requests.organization_id', Auth::user()->organization_id)->get();
            // return $apply_leaves;

            return view('modules.site.templates.wajeha.backEnd.parentPanel.parent_leave', compact('apply_leaves', 'student'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    function leaveApply(Request $request)
    {
        try {
            $user = Auth::user();
            if ($user) {
                $my_leaves = LeaveDefine::where('role_id', 2)->get();
                $apply_leaves = LeaveRequest::where('role_id', 2)->all();
                $leave_types = LeaveDefine::where('role_id', 2)->all();
            } else {
                $my_leaves = LeaveDefine::where('role_id', $request->role_id)->get();
                $apply_leaves = LeaveRequest::where('role_id', $request->role_id)->all();
                $leave_types = LeaveDefine::where('role_id', $request->role_id)->all();
            }

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['my_leaves'] = $my_leaves->toArray();
                $data['apply_leaves'] = $apply_leaves->toArray();
                $data['leave_types'] = $leave_types->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('modules.site.templates.wajeha.backEnd.parentPanel.apply_leave', compact('apply_leaves', 'leave_types', 'my_leaves', 'user'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function leaveStore(Request $request)
    {
        // dd($request->all());
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $input = $request->all();
        if (ApiBaseMethod::checkUrl($request->fullUrl())) {
            $validator = Validator::make($input, [
                'student_id' => "required",
                'apply_date' => "required",
                'leave_type' => "required",
                'leave_from' => 'required|before_or_equal:leave_to',
                'leave_to' => "required",
                'login_id' => "required",
                'role_id' => "required"
            ]);
        } else {
            $validator = Validator::make($input, [
                'student_id' => "required",
                'apply_date' => "required",
                'leave_type' => "required",
                'leave_from' => 'required|before_or_equal:leave_to',
                'leave_to' => "required"
            ]);
        }
        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $input = $request->all();
            $fileName = "";
            if ($request->file('attach_file') != "") {
                $file = $request->file('attach_file');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/leave_request/', $fileName);
                $fileName = 'public/uploads/leave_request/' . $fileName;
            }
            $apply_leave = new LeaveRequest();
            $apply_leave->employee_id = $request->student_id;
            $apply_leave->$role_id == 23;
            $apply_leave->apply_date = date('Y-m-d', strtotime($request->apply_date));
            $apply_leave->leave_define_id = $request->leave_type;
            $apply_leave->type_id = $request->leave_type;
            $apply_leave->leave_from = date('Y-m-d', strtotime($request->leave_from));
            $apply_leave->leave_to = date('Y-m-d', strtotime($request->leave_to));
            $apply_leave->approve_status = 'P';
            $apply_leave->reason = $request->reason;
            $apply_leave->file = $fileName;
            $apply_leave->organization_id = Auth::user()->organization_id;
            $apply_leave->academic_id = YearCheck::getAcademicId();
            $result = $apply_leave->save();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($result) {
                    return ApiBaseMethod::sendResponse(null, 'Leave Request has been created successfully.');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
                }
            } else {
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function viewLeaveDetails(Request $request, $id)
    {
        try {
            $leaveDetails = LeaveRequest::find($id);
            $apply = "";
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['leaveDetails'] = $leaveDetails->toArray();
                $data['apply'] = $apply;
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('modules.site.templates.wajeha.backEnd.parentPanel.viewLeaveDetails', compact('leaveDetails', 'apply'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    function leaveEdit($id)
    {
    }

    public function pendingLeave(Request $request)
    {

        try {
            $apply_leaves = LeaveRequest::where([['active_status', 1], ['approve_status', 'P']])->get();
            $leave_types = LeaveType::whereOr(['organization_id', Auth::user()->organization_id], ['organization_id', 1])->get();
            $roles = Role::where('id', 2)->where(function ($q) {
                $q->orWhere('type', 'System');
            })->get();
            $pendingRequest = LeaveRequest::where('leave_requests.active_status', 1)
                ->select('leave_requests.id', 'full_name', 'apply_date', 'leave_from', 'leave_to', 'reason', 'file', 'leave_types.type', 'approve_status')
                ->join('leave_defines', 'leave_requests.leave_define_id', '=', 'leave_defines.id')
                ->join('employees', 'leave_requests.employee_id', '=', 'employees.id')
                ->leftjoin('leave_types', 'leave_requests.type_id', '=', 'leave_types.id')
                ->where('leave_requests.approve_status', '=', 'P')
                ->where('leave_requests.academic_id', YearCheck::getAcademicId())
                ->where('leave_requests.organization_id', Auth::user()->organization_id)->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['pending_request'] = $pendingRequest->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('modules.site.templates.wajeha.backEnd.parentPanel.pending_leave', compact('apply_leaves', 'leave_types', 'roles'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function parentLeaveEdit(request $request, $id)
    {

        try {
            $user = Auth::user();
            if ($user) {
                $my_leaves = LeaveDefine::where('role_id', 2)->get();
                $apply_leaves = LeaveRequest::where('role_id', 2)->all();
                $leave_types = LeaveDefine::where('role_id', 2)->all();
            } else {
                $my_leaves = LeaveDefine::where('role_id', $request->role_id)->get();
                $apply_leaves = LeaveRequest::where('role_id', $request->role_id)->all();
                $leave_types = LeaveDefine::where('role_id', $request->role_id)->all();
            }
            $apply_leave = LeaveRequest::find($id);

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['my_leaves'] = $my_leaves->toArray();
                $data['apply_leaves'] = $apply_leaves->toArray();
                $data['leave_types'] = $leave_types->toArray();
                $data['apply_leave'] = $apply_leave->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('modules.site.templates.wajeha.backEnd.parentPanel.apply_leave', compact('apply_leave', 'apply_leaves', 'leave_types', 'my_leaves', 'user'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function update(Request $request)
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        $input = $request->all();
        if (ApiBaseMethod::checkUrl($request->fullUrl())) {
            $validator = Validator::make($input, [
                'id' => "required",
                'apply_date' => "required",
                'leave_type' => "required",
                'leave_from' => 'required|before_or_equal:leave_to',
                'leave_to' => "required",
                'login_id' => "required",
                'role_id' => "required"
            ]);
        } else {
            $validator = Validator::make($input, [
                'apply_date' => "required",
                'leave_type' => "required",
                'leave_from' => 'required|before_or_equal:leave_to',
                'leave_to' => "required"
            ]);
        }

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $fileName = "";
            if ($request->file('file') != "") {
                $apply_leave = LeaveRequest::find($request->id);
                if (file_exists($apply_leave->file)) unlink($apply_leave->file);
                $file = $request->file('file');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/leave_request/', $fileName);
                $fileName = 'public/uploads/leave_request/' . $fileName;
            }

            $user = Auth()->user();
            $apply_leave = LeaveRequest::find($request->id);
            $apply_leave->employee_id = $request->student_id;
            $apply_leave->$role_id == 23;
            $apply_leave->apply_date = date('Y-m-d', strtotime($request->apply_date));
            $apply_leave->leave_define_id = $request->leave_type;
            $apply_leave->leave_from = date('Y-m-d', strtotime($request->leave_from));
            $apply_leave->leave_to = date('Y-m-d', strtotime($request->leave_to));
            $apply_leave->approve_status = 'P';
            $apply_leave->reason = $request->reason;
            if ($fileName != "") {
                $apply_leave->file = $fileName;
            }
            $result = $apply_leave->save();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($result) {
                    return ApiBaseMethod::sendResponse(null, 'Leave Request has been updated successfully');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
                }
            } else {
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect('apply-leave');
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    function DeleteLeave(Request $request, $id)
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        try {
            $apply_leave = LeaveRequest::find($id);
            if ($apply_leave->file != "") {
                if (file_exists($apply_leave->file)) unlink($apply_leave->file);
            }
            $result = $apply_leave->delete();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($result) {
                    return ApiBaseMethod::sendResponse(null, 'Request has been deleted successfully');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
                }
            } else {
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect('parent-apply-leave');
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function classRoutine($id)
    {
        try {
            $student_detail = Student::where('id', $id)->with('admissions.programs')->first();
            $class_id = $student_detail->class_id;
            $section_id = $student_detail->section_id;
            $weekends = Weekend::orderBy('order', 'ASC')->all();
            $class_times = ClassTime::where('type', 'class')->get();
            return view('modules.site.templates.wajeha.backEnd.parentPanel.class_routine', compact('class_times', 'class_id', 'section_id', 'weekends', 'student_detail'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function attendance($id)
    {
        try {
            $student_detail = Student::where('id', $id)->first();
            $academic_years = AcademicYear::get();
            return view('modules.site.templates.wajeha.backEnd.parentPanel.attendance', compact('student_detail', 'academic_years'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function attendanceSearch(Request $request)
    {
        $this->validate($request,[
            'month' => 'required',
            'year' => 'required'
        ]);

        try {
            $student_detail = Student::where('id', $request->student_id)->first();
            $year = $request->year;
            $month = $request->month;
            $current_day = date('d');
            $days = cal_days_in_month(CAL_GREGORIAN, $request->month, $request->year);
            //$students = Student::where('class_id', $request->class)->where('section_id', $request->section)->get();
            // $academic_years = AcademicYear::get();
            // $attendances = StudentAttendance::/* where('student_id', $student_detail->id)->where('class_time', 'LIKE', '%'.$request->year . '-' . $request->month . '%')-> */get();
            
            $attendances = StudentAttendance::where('student_id', $student_detail->id)->where('class_time', 'like', $request->year . '-' . $request->month . '%')->get();
            $academic_years = AcademicYear::all();
            // dd($attendances);
            return view('modules.site.templates.wajeha.backEnd.parentPanel.attendance', compact('attendances', 'days', 'year', 'month', 'current_day', 'student_detail', 'academic_years'));
        } catch (\Exception $e) {
            // dd($e);
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function attendancePrint($student_id, $month, $year)
    {
        try {
            $student_detail = Student::where('id', $student_id)->first();
            $current_day = date('d');
            $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);
            //$students = Student::where('class_id', $request->class)->where('section_id', $request->section)->get();
            $attendances = StudentAttendance::where('student_id', $student_detail->id)->where('class_time', 'like', $year . '-' . $month . '%')->get();
            $customPaper = array(0, 0, 700.00, 1000.80);
            $pdf = PDF::loadView(
                'backEnd.parentPanel.attendance_print',
                [
                    'attendances' => $attendances,
                    'days' => $days,
                    'year' => $year,
                    'month' => $month,
                    'current_day' => $current_day,
                    'student_detail' => $student_detail
                ]
            )->setPaper('A4', 'landscape');
            return $pdf->stream('my_child_attendance.pdf');
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function examinationSchedule($id)
    {

        try {
            // return $id;
            $user = Auth::user();
            $parent = Guardian::where('user_id', $user->id)->first();
            $student_detail = Student::where('id', $id)->first();
            $student_id = $student_detail->id;
            // return $student_detail;
            $exam_types = ExamType::get();

            return view('modules.site.templates.wajeha.backEnd.parentPanel.parent_exam_schedule', compact('exam_types', 'student_id'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function parentBookList()
    {

        try {
            $books = Book::where('active_status', 1)
                ->orderBy('id', 'DESC')
                ->get();
            return view('modules.site.templates.wajeha.backEnd.parentPanel.parentBookList', compact('books'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function parentBookIssue()
    {
        try {
            $user = Auth::user();
            $parent_detail = Guardian::where('user_id', $user->id)->first();
            $books = Book::select('id', 'book_title')->all();
            $subjects = Subject::select('id', 'subject_name')->all();


            $library_member = LibraryMember::where('member_type', 3)->where('documentable_id', $parent_detail->user_id)->first();
            // return $library_member;
            if (empty($library_member)) {
                Toastr::error('You are not library member ! Please contact with librarian', 'Failed');
                return redirect()->back();
                // return redirect()->back()->with('message-danger', 'You are not library member ! Please contact with librarian');
            }
            $issueBooks = BookIssue::where('member_id', $library_member->documentable_id)->where('issue_status', 'I')->get();

            return view('modules.site.templates.wajeha.backEnd.parentPanel.parentBookIssue', compact('books', 'subjects', 'issueBooks'));
        } catch (\Exception $e) {
//            Toastr::error('Operation Failed', 'Failed');
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }
    public function examinationScheduleSearch(Request $request)
    {
        // return $request;
        $this->validate($request,[
            'exam' => 'required',
        ]);
        try {
            $user = Auth::user();
            $parent = Guardian::where('user_id', $user->id)->first();
            $student_detail = Student::where('id', $request->student_id)->first();
            $student_id = $student_detail->id;
            $classIds = $student_detail->joint_classes()->get();


            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $assign_subjects = AssignSubject::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();

            // return  $assign_subjects;
            if ($assign_subjects->count() == 0) {
                Toastr::error('No Subject Assigned. Please assign subjects in this class.', 'Failed');
                return redirect()->back();
                // return redirect()->back()->with('message-danger', 'No Subject Assigned. Please assign subjects in this class.');
            }

            $exams = Exam::get();
            $class_id = $student_detail->class_id;
            $section_id = $student_detail->section_id;
            $exam_id = $request->exam;

            $exam_types = ExamType::get();
            $exam_periods = ClassTime::where('type', 'exam')->get();
            $exam_schedule_subjects = "";
            $assign_subject_check = "";

            return view('modules.site.templates.wajeha.backEnd.parentPanel.parent_exam_schedule', compact('exams', 'assign_subjects', 'class_id', 'section_id', 'exam_id', 'exam_schedule_subjects', 'assign_subject_check', 'exam_types', 'exam_periods', 'student_id'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function examination($id)
    {
        try {
            $student_detail = Student::where('id', $id)->first();
            $classIds = $student_detail->joint_classes()->get();


            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $exams = ExamSchedule::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();
            $grades = MarksGrade::get();
            $exam_terms = ExamType::get();

            return view('modules.site.templates.wajeha.backEnd.parentPanel.student_result', compact('student_detail', 'exams', 'grades','exam_terms'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function subjects($id)
    {
        try {
            $student_detail = Student::where('id', $id)->first();
            $classIds = $student_detail->joint_classes()->get();


            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $assignSubjects = AssignSubject::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();
            return view('modules.site.templates.wajeha.backEnd.parentPanel.subject', compact('assignSubjects', 'student_detail'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function teacherList($id)
    {
        try {
            $student_detail = Student::where('id', $id)->first();
            $classIds = $student_detail->joint_classes()->get();


            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $teachers = AssignSubject::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get()->unique('employee_id');
            return view('modules.site.templates.wajeha.backEnd.parentPanel.teacher_list', compact('teachers', 'student_detail'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function transport($id)
    {
        try {
            $student_detail = Student::where('id', $id)->first();
            $routes = AssignVehicle::join('vehicles', 'assign_vehicles.vehicle_id', 'vehicles.id')
            ->join('students', 'vehicles.id', 'students.vechile_id')
            ->where('assign_vehicles.active_status', 1)
            ->where('students.guardian_id', Auth::user()->id)
            ->where('assign_vehicles.organization_id',Auth::user()->organization_id)
            ->get();
            // $routes = AssignVehicle::where('academic_id', YearCheck::getAcademicId())->get();
            return view('modules.site.templates.wajeha.backEnd.parentPanel.transport', compact('routes', 'student_detail'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function dormitory($id)
    {
        try {
            $student_detail = Student::where('id', $id)->first();
            // return $student_detail;
            $room_lists = RoomList::where('id', $student_detail->room_id)->get();
            $room_lists = $room_lists->groupBy('dormitory_id');
            $room_types = RoomType::get();
            $dormitory_lists = DormitoryList::where('id', $student_detail->dormitory_id)->get();
            return view('modules.site.templates.wajeha.backEnd.parentPanel.dormitory', compact('room_lists', 'room_types', 'dormitory_lists', 'student_detail'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function homework($id)
    {
        try {




            $student_detail = Student::where('id', $id)->first();
            $studentClasses = $student_detail->toArray();
            $classIds = $student_detail->joint_classes()->get();


            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $homeworkLists = Homework::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();
            return view('modules.site.templates.wajeha.backEnd.parentPanel.homework', compact('homeworkLists', 'student_detail'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function homeworkView($class_id, $section_id, $homework_id)
    {
        try {
            $homeworkDetails = Homework::where('class_id', '=', $class_id)->where('section_id', '=', $section_id)->where('id', '=', $homework_id)->first();
            return view('modules.site.templates.wajeha.backEnd.parentPanel.homeworkView', compact('homeworkDetails', 'homework_id'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function parentNoticeboard()
    {
        try {
            $allNotices = NoticeBoard::where('inform_to', 'LIKE', '%3%')
                ->orderBy('id', 'DESC')

                ->get();

            return view('modules.site.templates.wajeha.backEnd.parentPanel.parentNoticeboard', compact('allNotices'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function childListApi(Request $request, $id)
    {
        try {
            $parent = Guardian::where('user_id', $id)->first();
            $student_info = DB::table('students')
                ->join('classes', 'classes.id', '=', 'students.class_id')
                ->join('sections', 'sections.id', '=', 'students.section_id')
                // ->join('exams','exams.id','=','exam_types.id' )
                // ->join('subjects','subjects.id','=','result_stores.subject_id' )

                ->where('students.guardian_id', '=', $parent->id)


                ->select('students.user_id', 'student_photo', 'students.full_name as student_name', 'class_name', 'section_name', 'roll_no')

                ->where('students.organization_id', Auth::user()->organization_id)->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {


                return ApiBaseMethod::sendResponse($student_info, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function childProfileApi(Request $request, $id)
    {
        try {
            $student_detail = Student::where('id', $id)->first();
            $siblings = Student::where('guardian_id', $student_detail->guardian_id)->all();
            $fees_assigneds = FeesAssign::where('student_id', $student_detail->id)->get();
            $fees_discounts = FeesAssignDiscount::where('student_id', $student_detail->id)->get();
            $documents = Document::where('documentable_id', $student_detail->id)->where('type', 'stu')->get();
            $timelines = StudentTimeline::where('staff_student_id', $student_detail->id)->where('type', 'stu')->where('visible_to_student', 1)->get();
            $classIds = $student_detail->joint_classes()->get();


            $studentClasses = collect($classIds)->map(function ($item, $key) {

                return $item->id;
            });
            $studentClasses = $studentClasses->toArray();

            $exams = ExamSchedule::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();
            $grades = MarksGrade::get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['student_detail'] = $student_detail->toArray();
                $data['fees_assigneds'] = $fees_assigneds->toArray();
                $data['fees_discounts'] = $fees_discounts->toArray();
                $data['exams'] = $exams->toArray();
                $data['documents'] = $documents->toArray();
                $data['timelines'] = $timelines->toArray();
                $data['siblings'] = $siblings->toArray();
                $data['grades'] = $grades->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }

            //return view('modules.site.templates.wajeha.backEnd.studentPanel.my_profile', compact('student_detail', 'fees_assigneds', 'fees_discounts', 'exams', 'documents', 'timelines', 'siblings', 'grades'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function collectFeesChildApi(Request $request, $id)
    {
        try {
            $student = Student::where('id', $id)->first();
            $fees_assigneds = FeesAssign::where('student_id', $id)->orderBy('id', 'desc')->get();

            $fees_assigneds2 = DB::table('fees_assigns')
                ->select('fees_types.id as fees_type_id', 'fees_types.name', 'fees_masters.date as due_date', 'fees_masters.amount as amount')
                ->join('fees_masters', 'fees_masters.id', '=', 'fees_assigns.fees_master_id')
                ->join('fees_types', 'fees_types.id', '=', 'fees_masters.fees_type_id')
                ->join('fees_payments', 'fees_payments.fees_type_id', '=', 'fees_masters.fees_type_id')
                ->where('fees_assigns.student_id', $student->id)
                //->where('fees_payments.student_id', $student->id)
                ->where('fees_assigns.organization_id', Auth::user()->organization_id)->get();
            $i = 0;
            return $fees_assigneds2;
            foreach ($fees_assigneds2 as $row) {
                $d[$i]['fees_name'] = $row->name;
                $d[$i]['due_date'] = $row->due_date;
                $d[$i]['amount'] = $row->amount;
                $d[$i]['paid'] = DB::table('fees_payments')->where('fees_type_id', $row->fees_type_id)->sum('amount');
                $d[$i]['fine'] = DB::table('fees_payments')->where('fees_type_id', $row->fees_type_id)->sum('fine');
                $d[$i]['discount_amount'] = DB::table('fees_payments')->where('fees_type_id', $row->fees_type_id)->sum('discount_amount');
                $d[$i]['balance'] = ((float) $d[$i]['amount'] + (float) $d[$i]['fine'])  - ((float) $d[$i]['paid'] + (float) $d[$i]['discount_amount']);
                $i++;
            }

            //dd($fees_assigneds2);
            //, DB::raw("SUM(fees_payments.amount) as total_paid where fees_payments.fees_type_id==")
            $fees_discounts = FeesAssignDiscount::where('student_id', $id)->get();

            $applied_discount = [];
            foreach ($fees_discounts as $fees_discount) {
                $fees_payment = FeesPayment::select('fees_discount_id')->where('fees_discount_id', $fees_discount->id)->first();
                if (isset($fees_payment->fees_discount_id)) {
                    $applied_discount[] = $fees_payment->fees_discount_id;
                }
            }

            //dd($fees_discounts);


            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                // $data['student'] = $student;
                $data['fees'] = $d;
                return ApiBaseMethod::sendResponse($fees_assigneds2, null);
            }

            return view('modules.site.templates.wajeha.backEnd.feesCollection.collect_fees_student_wise', compact('student', 'fees_assigneds', 'fees_discounts', 'applied_discount'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function classRoutineApi(Request $request, $id)
    {
        try {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $user_id = $id;
            } else {
                $user = Auth::user();

                if ($user) {
                    $user_id = $user->id;
                } else {
                    $user_id = $request->user_id;
                }
            }

            $student_detail = Student::where('id', $id)->first();
            //return $student_detail;
            $class_id = $student_detail->class_id;
            $section_id = $student_detail->section_id;

            $weekends = Weekend::orderBy('order', 'ASC')->all();
            $class_times = ClassTime::where('type', 'class')->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['student_detail'] = $student_detail->toArray();
                // $data['class_id'] = $class_id;
                // $data['section_id'] = $section_id;
                // $data['weekends'] = $weekends->toArray();
                // $data['class_times'] = $class_times->toArray();

                $weekenD = Weekend::get();

                foreach ($weekenD as $row) {
                    $data[$row->name] = DB::table('class_routine_updates')
                        ->select('class_times.period', 'class_times.start_time', 'class_times.end_time', 'subjects.subject_name', 'class_rooms.room_no')
                        ->join('classes', 'classes.id', '=', 'class_routine_updates.class_id')
                        ->join('sections', 'sections.id', '=', 'class_routine_updates.section_id')
                        ->join('class_times', 'class_times.id', '=', 'class_routine_updates.class_period_id')
                        ->join('subjects', 'subjects.id', '=', 'class_routine_updates.subject_id')
                        ->join('class_rooms', 'class_rooms.id', '=', 'class_routine_updates.room_id')

                        ->where([
                            ['class_routine_updates.class_id', $class_id], ['class_routine_updates.section_id', $section_id], ['class_routine_updates.day', $row->id],
                        ])->where('classes.organization_id', Auth::user()->organization_id)->get();
                }

                return ApiBaseMethod::sendResponse($data, null);
            }

            //return view('modules.site.templates.wajeha.backEnd.studentPanel.class_routine', compact('class_times', 'class_id', 'section_id', 'weekends'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function childHomework(Request $request, $id)
    {
        try {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $student_detail = Student::where('id', $id)->first();

                $class_id = $student_detail->class->id;
                $subject_list = AssignSubject::where([['class_id', $class_id], ['section_id', $student_detail->section_id]])->get();

                $i = 0;
                foreach ($subject_list as $subject) {
                    $homework_subject_list[$subject->subject->subject_name] = $subject->subject->subject_name;
                    $classIds = $student_detail->joint_classes()->get();


                    $studentClasses = collect($classIds)->map(function ($item, $key) {

                        return $item->id;
                    });
                    $studentClasses = $studentClasses->toArray();

                    $allList[$subject->subject->subject_name] =
                        DB::table('homeworks')
                        ->select('homeworks.description', 'subjects.subject_name', 'homeworks.homework_date', 'homeworks.submission_date', 'homeworks.evaluation_date', 'homeworks.file', 'homeworks.marks', 'homework_students.complete_status as status')
                        ->leftjoin('homework_students', 'homework_students.homework_id', '=', 'homeworks.id')
                        ->leftjoin('subjects', 'subjects.id', '=', 'homeworks.subject_id')
                        ->whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->where('subject_id', $subject->subject_id)->where('homeworks.organization_id', Auth::user()->organization_id)->get();
                }
                //return $h;
                $classIds = $student_detail->joint_classes()->get();


                $studentClasses = collect($classIds)->map(function ($item, $key) {

                    return $item->id;
                });
                $studentClasses = $studentClasses->toArray();

                $homeworkLists = Homework::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();
            }
            // dd($allList);
            $data = [];

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                foreach ($allList as $r) {
                    foreach ($r as $s) {
                        $data[] = $s;
                    }
                }
                return ApiBaseMethod::sendResponse($data, null);
            }
            // return view('modules.site.templates.wajeha.backEnd.studentPanel.student_homework', compact('homeworkLists', 'student_detail'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function childAttendanceAPI(Request $request, $id)
    {

        $input = $request->all();

        $validator = Validator::make($input, [
            'month' => "required",
            'year' => "required",
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }
        try {
            $student_detail = Student::where('id', $id)->first();

            $year = $request->year;
            $month = $request->month;
            if ($month < 10) {
                $month = '0' . $month;
            }
            $current_day = date('d');

            $days = cal_days_in_month(CAL_GREGORIAN, $month, $request->year);
            $days2 = cal_days_in_month(CAL_GREGORIAN, $month - 1, $request->year);
            $previous_month = $month - 1;
            $previous_date = $year . '-' . $previous_month . '-' . $days2;
            $previousMonthDetails['date'] = $previous_date;
            $previousMonthDetails['day'] = $days2;
            $previousMonthDetails['week_name'] = date('D', strtotime($previous_date));
            $attendances = StudentAttendance::where('student_id', $student_detail->id)
                ->where('class_time', 'like', '%' . $request->year . '-' . $month . '%')
                ->select('attendance_type', 'class_time')
                ->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data['attendances'] = $attendances;
                $data['previousMonthDetails'] = $previousMonthDetails;
                $data['days'] = $days;
                $data['year'] = $year;
                $data['month'] = $month;
                $data['current_day'] = $current_day;
                $data['status'] = 'Present: P, Late: L, Absent: A, Holiday: H, Half Day: F';
                return ApiBaseMethod::sendResponse($data, null);
            }
            //Test
            //return view('modules.site.templates.wajeha.backEnd.studentPanel.student_attendance', compact('attendances', 'days', 'year', 'month', 'current_day'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function aboutApi(request $request)
    {
        try {
            $about = DB::table('general_settings')
                ->join('languages', 'general_settings.language_id', '=', 'languages.id')
                ->join('academic_years', 'general_settings.session_id', '=', 'academic_years.id')
                ->join('about_pages', 'general_settings.organization_id', '=', 'about_pages.organization_id')
                ->select('main_description', 'organization_name', 'site_title', 'school_code', 'address', 'phone', 'email', 'logo', 'languages.language_name', 'year as session', 'copyright_text')
                ->where('general_settings.organization_id', Auth::user()->organization_id)->first();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {

                return ApiBaseMethod::sendResponse($about, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    function StudentDownload($file_name)
    {
        try {
            $file = public_path() . '/uploads/student/timeline/' . $file_name;
            if (file_exists($file)) {
                return Response::download($file);
            }
            return redirect()->back();
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}
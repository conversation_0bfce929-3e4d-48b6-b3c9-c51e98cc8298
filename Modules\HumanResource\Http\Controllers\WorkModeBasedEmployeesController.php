<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;

class WorkModeBasedEmployeesController extends Controller
{

    public function __invoke(Request $request)
    {


        $repo = new UserRepository();
        $users = $repo->workModeUsers($request->role_id);

        if (count($users) > 0)
        {
            $output ='<option value="">'.trans('common.Select One').'</option>';
            foreach ($users as $user)
            {
                $output .= '<option value="'.$user->id.'">'.$user->name.'</option>';
            }
        }
        else
            $output = '<option>'.trans('common.No data Found').'</option>';

        return $output;
    }
}

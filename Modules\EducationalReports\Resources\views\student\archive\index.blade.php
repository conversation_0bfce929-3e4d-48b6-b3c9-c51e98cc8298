@extends('layouts.hound')
@section('mytitle' , trans('units.students'))
@section('content')
<!-- Row -->
{{$student=""}}

<div class="row">
        <h5> Studens Archive</h5>

            <div class="col-sm-12">
            
            <div class="panel panel-default card-view">

            <div class="panel-wrapper collapse in">
                <div class="panel-body row">
                     
                    <div class="table-wrap">
                        <div class="table-responsive">
                            <div class="col-sm-13">
                            
                                
                              
                            </div>

                            <table class="table display responsive product-overview mb-30" id="myTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th> Name </th>
                                        <th> Center </th>
                                        <th>Last Status</th>
                                        <th>Deleted Date</th>
                                        <th>Deleted Reason</th>
                                        
                                        
                                        <th>Actions</th>
                                        
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($students as $student)
                                    <tr>
                                        <td>{{ $student->id }}</td>
                                        <td>{{ $student->full_name }}</td>
                                        <td>{{ $student->current_class->center->name  ?? '' }}</td>
                                        <td>{{ $student->current_admission ? ( ($student->current_hefz_plan && $student->current_hefz_plan->status != 'active') ? $student->current_hefz_plan->status : $student->current_admission->status ): 'not registered' }}</td>
                                        <td>{{ $student->deleted_at ?? '' }}</td>
                                        <td>{{ $student->delete_reason ?? '' }}</td>
                                       
                                        <td>
                                            <a href="{{ route('admission.students.archive.show', $student->id) }}" class="btn btn-success btn-xs" title="View Student">Restore</a>
                                            {{-- <a href="{{ route('admission.students.edit',$student->id) }}" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a> --}}
                                                

                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                            <div class="text-center">
                            </div>
                        </div>
                    </div>	
                </div>	
            </div>
        </div>
    </div>
</div>
<!-- /Row -->
<!-- Modal -->


      <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.1.0/jquery.min.js"></script>
      <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
      <script>

            
          </script>
@endsection
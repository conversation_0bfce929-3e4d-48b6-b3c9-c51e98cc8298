<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class JobNotificationFailure extends Model
{
    protected $table = 'job_notification_failures';
    
    protected $fillable = [
        'setup_id',
        'job_id',
        'recipient_email',
        'error_type',
        'error_message',
        'retry_count',
        'last_retry_at',
        'status',
        'stack_trace',
        'additional_data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'retry_count' => 'integer',
        'last_retry_at' => 'datetime',
        'additional_data' => 'array',
    ];

    /**
     * Get the notification setup associated with this failure.
     */
    public function setup(): BelongsTo
    {
        return $this->belongsTo(JobNotificationSetup::class, 'setup_id');
    }
    
    /**
     * Get the job associated with this failure.
     */
    public function job(): BelongsTo
    {
        return $this->belongsTo(Job::class, 'job_id');
    }
    
    /**
     * Mark this failure as being retried
     * 
     * @return bool
     */
    public function markAsRetrying(): bool
    {
        $this->status = 'retrying';
        $this->retry_count += 1;
        $this->last_retry_at = now();
        return $this->save();
    }
    
    /**
     * Mark this failure as resolved
     * 
     * @return bool
     */
    public function markAsResolved(): bool
    {
        $this->status = 'resolved';
        return $this->save();
    }
    
    /**
     * Mark this failure as permanently failed after max retries
     * 
     * @return bool
     */
    public function markAsFailed(): bool
    {
        $this->status = 'failed';
        return $this->save();
    }
    
    /**
     * Scope query to pending failures
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }
    
    /**
     * Scope query to retrying failures
     */
    public function scopeRetrying($query)
    {
        return $query->where('status', 'retrying');
    }
    
    /**
     * Scope query to failed failures
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }
    
    /**
     * Scope query to resolved failures
     */
    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }
} 
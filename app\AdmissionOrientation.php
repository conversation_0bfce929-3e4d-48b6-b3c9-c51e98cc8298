<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\AdmissionOrientation
 *
 * @property int $id
 * @property int $organization_id
 * @property int $admission_id
 * @property int $employee_id
 * @property string $orientation_time
 * @property string $location
 * @property string $note
 * @property int $created_by
 * @property string $status
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionOrientation whereAdmissionId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionOrientation whereCreatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionOrientation whereCreatedBy($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionOrientation whereEmployeeId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionOrientation whereId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionOrientation whereLocation($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionOrientation whereNote($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionOrientation whereOrganizationId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionOrientation whereOrientationTime($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionOrientation whereStatus($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionOrientation whereUpdatedAt($value)
 * @mixin \Eloquent
 * @method static \Illuminate\Database\Eloquent\Builder|AdmissionOrientation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdmissionOrientation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdmissionOrientation query()
 */
class AdmissionOrientation extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'admission_orientation';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['interview_time'
                            ,'organization_id'
                            ,'admission_id'
                            ,'employee_id'
                            ,'orientation_time'
                            ,'location'
                            ,'created_by'
                            ,'note'
                            ,'status'
                        ];


}

# JobSeeker Module - Category Flow Diagram

## Current Implementation - How Categories Flow from Configuration to Job Providers

```mermaid
graph TB
    subgraph "Configuration Layer"
        Config[config/jobseeker.php]
        Config -->|Jobs.af Categories| JobsAfCat["Jobs.af Categories<br/>['IT - Software', 'Management', ...]"]
        Config -->|ACBAR Mapping| AcbarMap["ACBAR Category Mapping<br/>[5 => 12, 6 => 7, ...]"]
    end

    subgraph "Database Layer"
        JobCat[JobCategory Table<br/>Canonical Categories]
        ProvCat[ProviderJobCategory Table<br/>Provider-specific Categories]
        CSR[CommandScheduleRule Table<br/>provider_category_ids: JSON]
        CSF[CommandScheduleFilter Table<br/>categories: JSON<br/>locations: JSON]
    end

    subgraph "Service Layer"
        FilterRepo[FilterRepository]
        TransService[FilterTranslationService]
        JobsAfService[JobsAfService]
        AcbarService[AcbarJobService]
    end

    subgraph "Command Layer"
        SyncJobsAf[jobseeker:sync-jobs-af]
        SyncAcbar[jobseeker:sync-acbar-jobs]
    end

    subgraph "API Layer"
        JobsAfAPI[Jobs.af API]
        AcbarAPI[ACBAR.org API]
    end

    %% Current Flow for Jobs.af
    SyncJobsAf -->|"schedule_rule_id"| FilterRepo
    FilterRepo -->|"Get filters for rule"| CSF
    CSF -->|"canonical_category_ids"| TransService
    TransService -->|"Translate to Jobs.af"| JobsAfCat
    JobsAfCat -->|"Category names"| JobsAfService
    JobsAfService -->|"API Request"| JobsAfAPI

    %% Current Flow for ACBAR
    SyncAcbar -->|"schedule_rule_id"| FilterRepo
    FilterRepo -->|"Get filters for rule"| CSF
    CSF -->|"canonical_category_ids"| TransService
    TransService -->|"Translate to ACBAR"| AcbarMap
    AcbarMap -->|"ACBAR IDs"| AcbarService
    AcbarService -->|"API Request"| AcbarAPI

    %% Relationships
    JobCat -.->|"Referenced by"| CSF
    ProvCat -.->|"Not currently used"| CSR

    style Config fill:#f9f,stroke:#333,stroke-width:2px
    style CSR fill:#ff9,stroke:#333,stroke-width:2px
    style ProvCat fill:#9ff,stroke:#333,stroke-width:2px
```

## Key Points - Current Implementation

1. **Configuration-Based Translation**:
   - Jobs.af uses hardcoded category names from `config/jobseeker.php`
   - ACBAR uses hardcoded ID mapping from `config/jobseeker.php`

2. **CommandScheduleRule**:
   - Has `provider_category_ids` column (JSON) but it's **NOT USED**
   - Categories are actually stored in `CommandScheduleFilter` table

3. **Translation Flow**:
   - Canonical category IDs → FilterTranslationService → Provider-specific format
   - Jobs.af: IDs → Category names (from config)
   - ACBAR: IDs → ACBAR category IDs (from config mapping)

4. **ProviderJobCategory Table**:
   - Exists but is **NOT USED** in the current flow
   - Contains provider-specific category mappings

## The Problem

The system is using hardcoded mappings in configuration files instead of the dynamic `ProviderJobCategory` table and the `provider_category_ids` field in `CommandScheduleRule`.

## Laravel Models Used

- **JobCategory**: The canonical (master) categories
- **ProviderJobCategory**: Provider-specific category mappings (not currently used)
- **CommandScheduleRule**: Schedule rules with provider_category_ids (not currently used)
- **CommandScheduleFilter**: Stores canonical category IDs for filtering

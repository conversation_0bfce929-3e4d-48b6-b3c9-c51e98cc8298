<?php

namespace Modules\Admission\Http\Controllers;

use App\Center;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;

class CenterController extends Controller
{
    /**
     * Fetch centers with translations.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function fetchCenters(Request $request)
    {

        // In your controller method
        $employee = auth()->guard('employee')->user();

        $centers = $employee->center()
            ->withCount('admissions')  // adds an "admissions_count" attribute to each center

//            ->withTranslation('en') // loads the translation with locale 'en'
//            ->orderBy('title', 'asc')
            ->get()
            ->map(function ($center) {
                return [
                    'name'  => $center->name . ' (' . $center->admissions_count . ' stds)',
                    'value' => $center->id,
                ];
            });

// Return the centers as a JSON response
        return response()->json($centers);



        // Fetch centers with translations using a raw query
        $centers = DB::select("
            SELECT center_translations.name AS 'name', centers.id AS 'value'
            FROM centers
            JOIN center_translations ON centers.id = center_translations.center_id
            WHERE center_translations.locale = 'en' AND centers.deleted_at IS NULL
            ORDER BY center_translations.name ASC
        ");

        // Return the centers as a JSON response
        return response()->json($centers);
    }

    public function fetchSupervisorCenters(Request $request)
    {

        // Get the authenticated employee
        $employee = auth()->guard('employee')->user();


        // Check if the employee is a managing director
        if ($employee->hasRole('managing-director_' . config('organization_id') . '_')) {
            // Managing directors get all centers (with English translations)
            $centers = Center::all()
                ->map(function ($center) {
                    return [
                        'name'  => $center->name,
                        'value' => $center->id,
                    ];
                });


        } else {

            // For supervisors, only return centers attached via the pivot table (cen_emps)
            $centers = Center::whereIn('centers.id', function ($query) use ($employee) {
                    $query->select('cen_id')
                        ->from('cen_emps')
                        ->where('emp_id', $employee->id);
                })
                ->get()
                ->map(function ($center) {
                    return [
                        'name'  => $center->name,
                        'value' => $center->id,
                    ];
                });

        }

        return response()->json($centers);



        // Get the logged-in supervisor's (employee) ID
        $employeeId = auth()->guard('employee')->id();

        // Fetch centers assigned to this supervisor
        $centers = DB::select("
        SELECT center_translations.name AS name, centers.id AS value
        FROM centers
        JOIN center_translations ON centers.id = center_translations.center_id
        WHERE center_translations.locale = 'en'
          AND centers.deleted_at IS NULL 
          AND centers.id IN (SELECT cen_id FROM cen_emps WHERE emp_id = ?)
        ORDER BY center_translations.name ASC
    ", [$employeeId]);

        return response()->json($centers);
    }

//    public function fetchClassesForCenter(Request $request, $centerId)
//    {
//
//        $currentClassId = $request->input('current_class_id');
//
//        // Retrieve classes for the given center using the Classes model.
//        $classes = \App\Classes::where('center_id', $centerId)
//            ->whereNull('deleted_at')
//            ->with('programs')
//            ->get()
//            ->sortBy(function($class) {
//                // Get the first associated program (assume one per class) and its English title.
//                $program = $class->programs->first();
//                $programTitle = $program && $program->programTranslations->first()
//                    ? $program->programTranslations->first()->title
//                    : '';
//                // Sort by concatenated program title and class name.
//                return $programTitle . $class->name;
//            });
//
//        // Group the classes by the program title.
//        $grouped = $classes->groupBy(function($class) {
//            $program = $class->programs->first();
//            return $program && $program->programTranslations->first()
//                ? $program->programTranslations->first()->title
//                : 'No Program';
//        })->map(function($group) use ($currentClassId) {
//            return $group->map(function($class) use ($currentClassId)  {
//
//                return [
//                    'id'   => $class->id,
//                    'program_id'   => $class->programs->first()->id,
//                    'name' => $class->name,
//                    'default'    => ($class->id == $currentClassId) ? true : false,
//
//                ];
//            })->values();
//        });
//
//        return response()->json($grouped);
//    }


    public function fetchClassesForCenter(Request $request, $centerId)
    {
        $employee = auth()->guard('employee')->user();
        $currentClassId = $request->input('current_class_id');

        // For non-managing directors, ensure the employee is attached to the center.
        if (!$employee->hasRole('managing-director_' . config('organization_id') . '_')) {
            if (!$employee->center()->where('centers.id', $centerId)->exists()) {
                return response()->json(['error' => 'Unauthorized access to center'], 403);
            }
        }

        // Retrieve classes for the given center.
        // Eager load programs and their translations to optimize queries.
        $classes = \App\Classes::where('center_id', $centerId)
            ->whereNull('deleted_at')
            ->with('programs.programTranslations')
            ->get()
            ->sortBy(function ($class) {
                // Assume one program per class: get the first program’s English title.
                $program = $class->programs->first();
                $programTitle = $program && $program->programTranslations->first()
                    ? $program->programTranslations->first()->title
                    : '';
                // Sort by concatenated program title and class name.
                return $programTitle . $class->name;
            });

        // Group the classes by the program title.
        $grouped = $classes->groupBy(function ($class) {
            $program = $class->programs->first();
            return $program && $program->programTranslations->first()
                ? $program->programTranslations->first()->title
                : 'No Program';
        })
            ->map(function ($group) use ($currentClassId) {
                return $group->map(function ($class) use ($currentClassId) {
                    return [
                        'id'         => $class->id,
                        'program_id' => $class->programs->first() ? $class->programs->first()->id : null,
                        'name'       => $class->name,
                        'default'    => $class->id == $currentClassId,
                    ];
                })->values();
            });

        return response()->json($grouped);
    }


}

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Tests\Unit;

use Tests\TestCase;
use Carbon\Carbon;

/**
 * Unit tests for CommandSchedule time offset calculation logic
 *
 * Tests the time offset calculation logic in isolation to ensure
 * correct time calculations for different schedule types.
 */
class CommandScheduleTimeOffsetTest extends TestCase
{
    /**
     * Replicate the addTimeOffset logic for testing
     */
    private function addTimeOffset(string $scheduleExpression, string $scheduleType, int $offsetMinutes): string
    {
        try {
            switch ($scheduleType) {
                case 'daily_at':
                    // Handle time format like "09:00"
                    $time = Carbon::createFromFormat('H:i', $scheduleExpression);
                    $newTime = $time->addMinutes($offsetMinutes);
                    return $newTime->format('H:i');

                case 'weekly_at':
                    // Handle format like "1 09:00" (day_of_week time)
                    $parts = explode(' ', $scheduleExpression);
                    if (count($parts) === 2) {
                        $dayOfWeek = $parts[0];
                        $time = Carbon::createFromFormat('H:i', $parts[1]);
                        $newTime = $time->addMinutes($offsetMinutes);
                        return $dayOfWeek . ' ' . $newTime->format('H:i');
                    }
                    break;

                case 'cron':
                    // Handle cron expression like "0 9 * * *"
                    $parts = explode(' ', $scheduleExpression);
                    if (count($parts) >= 5) {
                        $minute = (int) $parts[0];
                        $hour = (int) $parts[1];

                        // Convert to total minutes and add offset
                        $totalMinutes = ($hour * 60) + $minute + $offsetMinutes;

                        // Handle day overflow
                        $newHour = intval($totalMinutes / 60) % 24;
                        $newMinute = $totalMinutes % 60;

                        $parts[0] = (string) $newMinute;
                        $parts[1] = (string) $newHour;

                        return implode(' ', $parts);
                    }
                    break;
            }

            return $scheduleExpression;

        } catch (\Exception $e) {
            return $scheduleExpression;
        }
    }

    /**
     * Test daily_at schedule type with various time offsets
     */
    public function test_daily_at_schedule_time_offset(): void
    {
        // Test basic 15-minute offset
        $result = $this->addTimeOffset('09:00', 'daily_at', 15);
        $this->assertEquals('09:15', $result);

        // Test 30-minute offset
        $result = $this->addTimeOffset('14:30', 'daily_at', 30);
        $this->assertEquals('15:00', $result);

        // Test 60-minute offset
        $result = $this->addTimeOffset('08:00', 'daily_at', 60);
        $this->assertEquals('09:00', $result);
    }

    /**
     * Test weekly_at schedule type with time offsets
     */
    public function test_weekly_at_schedule_time_offset(): void
    {
        // Test 15-minute offset on Monday
        $result = $this->addTimeOffset('1 10:00', 'weekly_at', 15);
        $this->assertEquals('1 10:15', $result);

        // Test 45-minute offset on Friday
        $result = $this->addTimeOffset('5 16:30', 'weekly_at', 45);
        $this->assertEquals('5 17:15', $result);
    }

    /**
     * Test cron schedule type with complex expressions
     */
    public function test_cron_schedule_time_offset(): void
    {
        // Test simple cron with 30-minute offset
        $result = $this->addTimeOffset('0 8 * * 1-5', 'cron', 30);
        $this->assertEquals('30 8 * * 1-5', $result);

        // Test with existing minutes and 15-minute offset
        $result = $this->addTimeOffset('45 9 * * *', 'cron', 15);
        $this->assertEquals('0 10 * * *', $result);
    }

    /**
     * Test hour overflow scenarios
     */
    public function test_hour_overflow_scenarios(): void
    {
        // Test minute overflow to next hour
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '50 23 * * *',
            'daily_at',
            30
        );
        $this->assertEquals('20 0 * * *', $result);

        // Test multiple hour overflow
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '30 22 * * *',
            'daily_at',
            90 // 1.5 hours
        );
        $this->assertEquals('0 0 * * *', $result);

        // Test 24-hour overflow (should wrap to next day)
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '0 23 * * *',
            'daily_at',
            120 // 2 hours
        );
        $this->assertEquals('0 1 * * *', $result);
    }

    /**
     * Test edge cases with zero and large offsets
     */
    public function test_edge_case_offsets(): void
    {
        // Test zero offset (should return original)
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '30 15 * * *',
            'daily_at',
            0
        );
        $this->assertEquals('30 15 * * *', $result);

        // Test large offset (multiple hours)
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '0 6 * * *',
            'daily_at',
            300 // 5 hours
        );
        $this->assertEquals('0 11 * * *', $result);

        // Test very large offset with overflow
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '0 20 * * *',
            'daily_at',
            480 // 8 hours
        );
        $this->assertEquals('0 4 * * *', $result);
    }

    /**
     * Test complex cron expressions with multiple time components
     */
    public function test_complex_cron_expressions(): void
    {
        // Test expression with multiple hours and specific days
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '15 6,10,14,18,22 * * 1,3,5',
            'cron',
            45
        );
        $this->assertEquals('0 7,11,15,19,23 * * 1,3,5', $result);

        // Test expression with hour range
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '30 9-17 * * 1-5',
            'cron',
            30
        );
        $this->assertEquals('0 10-18 * * 1-5', $result);
    }

    /**
     * Test invalid schedule expressions
     */
    public function test_invalid_schedule_expressions(): void
    {
        // Test malformed cron expression
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            'invalid-cron',
            'daily_at',
            15
        );
        $this->assertEquals('invalid-cron', $result); // Should return original on error

        // Test empty expression
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '',
            'daily_at',
            15
        );
        $this->assertEquals('', $result);

        // Test expression with missing parts
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '0 9',
            'daily_at',
            15
        );
        $this->assertEquals('0 9', $result); // Should return original on error
    }

    /**
     * Test negative offset handling
     */
    public function test_negative_offset_handling(): void
    {
        // Test negative offset (should subtract time)
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '30 10 * * *',
            'daily_at',
            -15
        );
        $this->assertEquals('15 10 * * *', $result);

        // Test negative offset with hour underflow
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '15 1 * * *',
            'daily_at',
            -30
        );
        $this->assertEquals('45 0 * * *', $result);

        // Test negative offset with day underflow
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '0 0 * * *',
            'daily_at',
            -60
        );
        $this->assertEquals('0 23 * * *', $result);
    }

    /**
     * Test minute boundary conditions
     */
    public function test_minute_boundary_conditions(): void
    {
        // Test exactly 60-minute offset
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '0 12 * * *',
            'daily_at',
            60
        );
        $this->assertEquals('0 13 * * *', $result);

        // Test 59-minute offset
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '1 12 * * *',
            'daily_at',
            59
        );
        $this->assertEquals('0 13 * * *', $result);

        // Test 61-minute offset
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '0 12 * * *',
            'daily_at',
            61
        );
        $this->assertEquals('1 13 * * *', $result);
    }

    /**
     * Test preservation of non-time cron components
     */
    public function test_preservation_of_cron_components(): void
    {
        // Test that day, month, and day-of-week are preserved
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '30 14 15 6 2',
            'cron',
            30
        );
        $this->assertEquals('0 15 15 6 2', $result);

        // Test with wildcards and ranges
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '0 8 1-15 * 1-5',
            'cron',
            45
        );
        $this->assertEquals('45 8 1-15 * 1-5', $result);

        // Test with step values
        $result = $this->addTimeOffsetMethod->invoke(
            $this->controller,
            '15 */2 * * *',
            'cron',
            15
        );
        $this->assertEquals('30 */2 * * *', $result);
    }
}

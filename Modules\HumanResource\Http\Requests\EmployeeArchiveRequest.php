<?php

namespace Modules\HumanResource\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EmployeeArchiveRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        






        return [
            'archive_reason' => 'required',
            'otherReason' => 'required_if:archive_reason,0',
        ];


    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {

        return [
            'otherReason.required_if' => 'Other reasons is/are required when archive reason is Others'
        ];

        return parent::messages(); // TODO: Change the autogenerated stub
    }
}

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use <PERSON><PERSON>les\JobSeeker\Services\EmailContentManagerService;
use Mo<PERSON>les\JobSeeker\Services\JobDetailFetchingService;
use Mo<PERSON>les\JobSeeker\Entities\JobProvider;
use Modules\JobSeeker\Entities\EmailContentSetting;
use Modules\JobSeeker\Entities\JobDetailedInfo;
use Mo<PERSON>les\JobSeeker\Entities\Job;
use App\Services\EmailService;

/**
 * TestEmailContentSystemCommand
 * 
 * Comprehensive testing command for the Email Content Management System
 * Tests the complete journey from Kernel scheduler to email delivery
 */
final class TestEmailContentSystemCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobseeker:test-email-content-system 
                            {--step=all : Which step to test (all, database, services, email, integration)}
                            {--email= : Email address to send test email to}
                            {--detail : Show detailed output}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the complete Email Content Management System from Kernel to email delivery';

    /**
     * Test results tracking
     */
    private array $testResults = [];
    private int $passedTests = 0;
    private int $failedTests = 0;

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Starting Email Content Management System Testing');
        $this->info('========================================================');
        
        $step = $this->option('step');
        $detail = $this->option('detail');
        
        try {
            switch ($step) {
                case 'database':
                    $this->testDatabaseStructure();
                    break;
                case 'services':
                    $this->testServices();
                    break;
                case 'email':
                    $this->testEmailGeneration();
                    break;
                case 'integration':
                    $this->testIntegrationFlow();
                    break;
                case 'all':
                default:
                    $this->testDatabaseStructure();
                    $this->testServices();
                    $this->testEmailGeneration();
                    $this->testIntegrationFlow();
                    break;
            }
            
            $this->displayResults();
            
            return $this->failedTests === 0 ? 0 : 1;
            
        } catch (\Exception $e) {
            $this->error('❌ Testing failed with exception: ' . $e->getMessage());
            if ($detail) {
                $this->error($e->getTraceAsString());
            }
            return 1;
        }
    }

    /**
     * Test database structure and connectivity
     */
    private function testDatabaseStructure(): void
    {
        $this->info('📊 Testing Database Structure...');
        
        // Test 1: Check if new tables exist
        $this->runTest('jobseeker_job_providers table exists', function () {
            return $this->tableExists('jobseeker_job_providers');
        });
        
        $this->runTest('jobseeker_email_content_settings table exists', function () {
            return $this->tableExists('jobseeker_email_content_settings');
        });
        
        $this->runTest('jobseeker_job_detailed_info table exists', function () {
            return $this->tableExists('jobseeker_job_detailed_info');
        });
        
        // Test 2: Check if existing tables have provider_id column
        $this->runTest('jobs table has provider_id column', function () {
            return $this->columnExists('jobs', 'provider_id');
        });
        
        $this->runTest('provider_job_categories has provider_id column', function () {
            return $this->columnExists('provider_job_categories', 'provider_id');
        });
        
        $this->runTest('provider_job_locations has provider_id column', function () {
            return $this->columnExists('provider_job_locations', 'provider_id');
        });
        
        // Test 3: Check data integrity
        $this->runTest('Provider data populated', function () {
            if (!$this->tableExists('jobseeker_job_providers')) {
                return false;
            }
            $count = DB::table('jobseeker_job_providers')->count();
            return $count >= 2; // Should have Jobs.af and ACBAR
        });
        
        // Test 4: Check email content settings
        $this->runTest('Email content settings populated', function () {
            if (!$this->tableExists('jobseeker_email_content_settings')) {
                return false;
            }
            $count = DB::table('jobseeker_email_content_settings')->count();
            return $count >= 20; // Should have at least 20 fields
        });
    }

    /**
     * Test service classes and their functionality
     */
    private function testServices(): void
    {
        $this->info('⚙️ Testing Service Classes...');
        
        // Test 1: EmailContentManagerService instantiation
        $this->runTest('EmailContentManagerService instantiates', function () {
            $service = app(EmailContentManagerService::class);
            return $service instanceof EmailContentManagerService;
        });
        
        // Test 2: JobDetailFetchingService instantiation
        $this->runTest('JobDetailFetchingService instantiates', function () {
            $service = app(JobDetailFetchingService::class);
            return $service instanceof JobDetailFetchingService;
        });
        
        // Test 3: EmailService integration
        $this->runTest('EmailService accessible', function () {
            $service = app(EmailService::class);
            return $service instanceof EmailService;
        });
        
        // Test 4: Service methods work with mock data
        $this->runTest('EmailContentManagerService methods work', function () {
            $service = app(EmailContentManagerService::class);
            
            // Test sample data generation
            $sampleData = $service->getSampleJobData();
            if (!is_array($sampleData) || empty($sampleData['position'])) {
                return false;
            }
            
            // Test available fields
            $availableFields = $service->getAvailableFields();
            if (!is_array($availableFields) || !isset($availableFields['basic'])) {
                return false;
            }
            
            // Test presets
            $presets = $service->getPresets();
            if (!is_array($presets) || !isset($presets['standard'])) {
                return false;
            }
            
            return true;
        });
        
        // Test 5: Cache functionality
        $this->runTest('Cache system works', function () {
            Cache::put('test_email_content', 'test_value', 60);
            $value = Cache::get('test_email_content');
            Cache::forget('test_email_content');
            return $value === 'test_value';
        });
    }

    /**
     * Test email template generation and rendering
     */
    private function testEmailGeneration(): void
    {
        $this->info('📧 Testing Email Generation...');
        
        // Test 1: Email template exists
        $this->runTest('New email template exists', function () {
            $templatePath = resource_path('views/modules/jobseeker/emails/jobs/jobseeker_notification_new.blade.php');
            return file_exists($templatePath);
        });
        
        // Test 2: Email template renders
        $this->runTest('Email template renders without errors', function () {
            try {
                $service = app(EmailContentManagerService::class);
                $sampleJob = $service->getSampleJobData();
                $sampleJobSeeker = (object) ['name' => 'Test User', 'email' => '<EMAIL>'];
                $sampleSetup = (object) ['name' => 'Test Notification Setup'];
                
                $html = view('modules.jobseeker.emails.jobs.jobseeker_notification_new', [
                    'jobs' => [$sampleJob],
                    'jobSeeker' => $sampleJobSeeker,
                    'setup' => $sampleSetup,
                ])->render();
                
                return !empty($html) && strlen($html) > 1000; // Should be substantial HTML
            } catch (\Exception $e) {
                $this->warn('Email template render error: ' . $e->getMessage());
                return false;
            }
        });
        
        // Test 3: Admin interface template exists
        $this->runTest('Admin interface template exists', function () {
            $templatePath = resource_path('views/modules/jobseeker/admin/email-content-manager/index.blade.php');
            return file_exists($templatePath);
        });
        
        // Test 4: MVP preview exists
        $this->runTest('MVP preview file exists', function () {
            $previewPath = resource_path('mvp_email_preview.html');
            return file_exists($previewPath);
        });
        
        // Test 5: Email service integration
        if ($this->option('email')) {
            $this->runTest('Test email sending', function () {
                try {
                    $emailService = app(EmailService::class);
                    $service = app(EmailContentManagerService::class);
                    $sampleJob = $service->getSampleJobData();
                    
                    $result = $emailService->sendEmail(
                        ['email' => $this->option('email'), 'name' => 'Test User'],
                        'Test: Email Content Management System',
                        'modules.jobseeker.emails.jobs.jobseeker_notification_new',
                        [
                            'jobs' => [$sampleJob],
                            'jobSeeker' => (object) ['name' => 'Test User'],
                            'setup' => (object) ['name' => 'System Test'],
                        ]
                    );
                    
                    return $result['success'] ?? false;
                } catch (\Exception $e) {
                    $this->warn('Email sending error: ' . $e->getMessage());
                    return false;
                }
            });
        }
    }

    /**
     * Test complete integration flow
     */
    private function testIntegrationFlow(): void
    {
        $this->info('🔄 Testing Integration Flow...');
        
        // Test 1: Route registration
        $this->runTest('Admin routes registered', function () {
            $routes = collect(\Illuminate\Support\Facades\Route::getRoutes())
                ->filter(function ($route) {
                    return str_contains($route->getName() ?? '', 'email_content_manager');
                });
            return $routes->count() >= 8; // Should have at least 8 routes
        });
        
        // Test 2: Middleware configuration
        $this->runTest('Middleware properly configured', function () {
            $route = \Illuminate\Support\Facades\Route::getRoutes()
                ->getByName('admin.jobseeker.email_content_manager.index');
            
            if (!$route) {
                return false;
            }
            
            $middleware = $route->middleware();
            return in_array('jobseeker.admin', $middleware) || 
                   in_array('auth:job_seeker', $middleware);
        });
        
        // Test 3: Controller accessibility
        $this->runTest('Controller class exists and is loadable', function () {
            try {
                $controller = app(\Modules\JobSeeker\Http\Controllers\Admin\EmailContentManagerController::class);
                return $controller instanceof \Modules\JobSeeker\Http\Controllers\Admin\EmailContentManagerController;
            } catch (\Exception $e) {
                $this->warn('Controller error: ' . $e->getMessage());
                return false;
            }
        });
        
        // Test 4: Job classes exist
        $this->runTest('Background job classes exist', function () {
            return class_exists(\Modules\JobSeeker\Jobs\FetchJobDetailsJob::class);
        });
        
        // Test 5: Notification system integration
        $this->runTest('Notification system updated', function () {
            $notificationClass = \Modules\JobSeeker\Notifications\JobAlertNotification::class;
            if (!class_exists($notificationClass)) {
                return false;
            }
            
            // Check if the template reference is updated (this is a simple check)
            $reflection = new \ReflectionClass($notificationClass);
            $source = file_get_contents($reflection->getFileName());
            return str_contains($source, 'jobseeker_notification_new');
        });
        
        // Test 6: Queue configuration
        $this->runTest('Queue system accessible', function () {
            try {
                $queueManager = app('queue');
                return $queueManager !== null;
            } catch (\Exception $e) {
                return false;
            }
        });
        
        // Test 7: Command scheduler integration
        $this->runTest('Command scheduler accessible', function () {
            try {
                // Run schedule:list to ensure scheduler is wired correctly
                $exitCode = \Illuminate\Support\Facades\Artisan::call('schedule:list');
                if ($exitCode !== 0) {
                    return false;
                }

                // Optionally validate output contains at least one JobSeeker scheduled command
                $output = \Illuminate\Support\Facades\Artisan::output();
                return is_string($output) && (str_contains($output, 'jobseeker:sync-jobs-af') || str_contains($output, 'jobseeker:sync-acbar-jobs'));
            } catch (\Exception $e) {
                return false;
            }
        });
    }

    /**
     * Helper method to run a test
     */
    private function runTest(string $testName, callable $test): void
    {
        try {
            $result = $test();
            
            if ($result) {
                $this->testResults[] = ['name' => $testName, 'status' => 'PASS'];
                $this->passedTests++;
                
                if ($this->option('detail')) {
                    $this->line("  ✅ {$testName}");
                }
            } else {
                $this->testResults[] = ['name' => $testName, 'status' => 'FAIL'];
                $this->failedTests++;
                
                $this->line("  ❌ {$testName}");
            }
        } catch (\Exception $e) {
            $this->testResults[] = ['name' => $testName, 'status' => 'ERROR', 'error' => $e->getMessage()];
            $this->failedTests++;
            
            $this->line("  ❌ {$testName} (Error: {$e->getMessage()})");
        }
    }

    /**
     * Check if table exists
     */
    private function tableExists(string $tableName): bool
    {
        try {
            return DB::getSchemaBuilder()->hasTable($tableName);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Check if column exists in table
     */
    private function columnExists(string $tableName, string $columnName): bool
    {
        try {
            if (!$this->tableExists($tableName)) {
                return false;
            }
            return DB::getSchemaBuilder()->hasColumn($tableName, $columnName);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Display test results summary
     */
    private function displayResults(): void
    {
        $this->info('');
        $this->info('📊 Test Results Summary');
        $this->info('========================');
        
        $totalTests = $this->passedTests + $this->failedTests;
        
        $this->info("Total Tests: {$totalTests}");
        $this->info("✅ Passed: {$this->passedTests}");
        
        if ($this->failedTests > 0) {
            $this->error("❌ Failed: {$this->failedTests}");
            
            $this->info('');
            $this->info('Failed Tests:');
            foreach ($this->testResults as $result) {
                if ($result['status'] !== 'PASS') {
                    $error = isset($result['error']) ? " ({$result['error']})" : '';
                    $this->line("  ❌ {$result['name']}{$error}");
                }
            }
        } else {
            $this->info("🎉 All tests passed!");
        }
        
        $this->info('');
        
        if ($this->failedTests === 0) {
            $this->info('✅ Email Content Management System is working correctly!');
            $this->info('🚀 Ready for production deployment.');
        } else {
            $this->warn('⚠️  Some tests failed. Please review the issues above.');
            $this->info('💡 Most failures are likely due to database tables not being created yet.');
            $this->info('📝 Execute the SQL files in Modules/JobSeeker/Database/ to fix database-related failures.');
        }
    }
}

@extends('layouts.hound')

@section('content')

<div class="panel panel-default card-view">
    <div class="panel-heading">
    <h4>Programs
        <a href="{{ url('/workplace/education/programs/create') }}" class="btn btn-success btn-sm pull-right" title="Add New program">
            <i class="fa fa-plus" aria-hidden="true"></i> Add New
        </a>

        {!! Form::open(['method' => 'GET', 'url' => '/workplace/education/programs', 'class' => 'navbar-form navbar-right', 'role' => 'search' , 'style' => 'margin:0;'])  !!}
        <div class="input-group">
            <input type="text" class="form-control" name="search" placeholder="Search...">
            <span class="input-group-btn">
                <button class="btn btn-default" type="submit">
                    <i class="fa fa-search"></i>
                </button>
            </span>
        </div>
        {!! Form::close() !!}
    
    </h4>

    </div>
    <div class="panel-body">

        <br/>
        <br/>
        <div class="table-responsive">
            <table class="table table-borderless">
                <thead>
                    <tr>
                        <th>ID</th><th>Title</th><th>Status</th><th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                @foreach($programs as $item)
                    <tr>
                        <td>{{ $item->id }}</td>
                        <td>{{ $item->title }}</td>
                        <td>{{ $item->status }}</td>
                        <td>
                            {{--  @isset($item->setting['special_program_code'])
                            <a href="{{ url('/workplace/education/' . $item->setting['special_program_code']) }}-program" title="View program"><button class="btn btn-info btn-xs"><i class="fa fa-eye" aria-hidden="true"></i> View</button></a>
                            <a href="{{ url('/workplace/education/' . $item->setting['special_program_code'] . '-program/edit') }}" title="Edit program"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>
                            @else  --}}
                            <a href="{{ url('/workplace/education/programs/' . $item->id) }}" title="View program"><button class="btn btn-info btn-xs"><i class="fa fa-eye" aria-hidden="true"></i> View</button></a>
                            <a href="{{ url('/workplace/education/programs/' . $item->id . '/edit') }}" title="Edit program"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>
                            {!! Form::open([
                                'method'=>'DELETE',
                                'url' => ['/workplace/education/programs', $item->id],
                                'style' => 'display:inline'
                            ]) !!}
                                {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                                        'type' => 'submit',
                                        'class' => 'btn btn-danger btn-xs',
                                        'title' => 'Delete program',
                                        'onclick'=>'return confirm("Confirm delete?")'
                                )) !!}
                            {!! Form::close() !!}
                            {{--  @endisset  --}}
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
            <div class="pagination-wrapper"> {!! $programs->appends(['search' => Request::get('search')])->render() !!} </div>
        </div>

    </div>
</div>

@endsection

<?php

namespace Modules\Education\Http\Controllers;


use App\Classes;

use App\IjazasanadMemorizationPlan;
use App\ProgramLevelLesson;
use App\StudentIjazasanadMemorizationLastApprovedPlan;
use App\StudentIjazasanadMemorizationReport;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;


class ClassIjazasanadLevel1TalqeenReportsDatatablesController extends Controller
{


    /**
     * @param Request $request
     * @return JsonResponse|void
     * @throws \Exception
     */
    public function getRecords(Request $request)
    {

        DB::connection()->enableQueryLog();

        if ($request->filled('studentId') || $request->filled('classDate')) {

            $year = Carbon::parse($request->get('classDate'))->year;
            $month = Carbon::parse($request->get('classDate'))->month;
            $classId = $request->get('classId');
            $classDate = $request->get('classDate');




            $substrings = ['level 1', 'level1'];

            $StudentIjazasanadMemorizationReports = StudentIjazasanadMemorizationReport::where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereNull('hefz_from_surat')
                ->whereNull('hefz_from_ayat')
                ->whereNull('hefz_to_surat')
                ->whereNull('hefz_to_ayat')
                ->with(['student', 'ijazasanadMemorizationPlan'])
                ->get()
                ->groupBy('student_id')
                ->map(function ($records) {
                    $firstReport = $records->first();
                    $lastReport = $records->last();

                    // Consolidate ranges for Talqeen lessons
                    $from_talqeen = $records->min('talqeen_from_lesson');
                    $to_talqeen = $records->max('talqeen_to_lesson');

                    // Sum of planned lessons for Talqeen based on the first report's plan
                    $plannedLessons = $firstReport->ijazasanadMemorizationPlan
                        ? ($firstReport->ijazasanadMemorizationPlan->talqeen_to_lesson - $firstReport->ijazasanadMemorizationPlan->talqeen_from_lesson) + 1
                        : 0;

                    // Calculate completed lessons within the Talqeen range
                    $completedLessons = ($to_talqeen - $from_talqeen) + 1;

                    return [
                        'student' => $firstReport->student,
                        'planned_lessons' => $plannedLessons,
                        'completed_lessons' => $completedLessons,
                        'from_talqeen' => $from_talqeen,
                        'to_talqeen' => $to_talqeen,
                    ];
                });



            return \Yajra\DataTables\DataTables::of($StudentIjazasanadMemorizationReports)
                ->addIndexColumn()

                // Student Column
                ->addColumn('student', function ($reportDetails) {
                    $studentName = ucfirst($reportDetails['student']->full_name);
                    $studentProfileUrl = route('students.show', ['id' => $reportDetails['student']->user_id]);
                    return '<a style="color:#b4eeb0; padding: 2px; display: inline-block; transition: all 0.3s ease;" data-id=' . $reportDetails['student']->id . ' class="section class-link" target="_blank" href="' . $studentProfileUrl . '">' . $studentName . '</a>';
                })

                // Monthly Talqeen Plan Column
                ->addColumn('monthlyTalqeenPlan', function ($reportDetails) {
                    $fromLesson = ProgramLevelLesson::find($reportDetails['from_talqeen']);
                    $toLesson = ProgramLevelLesson::find($reportDetails['to_talqeen']);

                    $fromLessonTitle = $fromLesson ? ($fromLesson->properties['fromVerse'] . ' to ' . $fromLesson->properties['toVerse']) : 'N/A';
                    $toLessonTitle = $toLesson ? ($toLesson->properties['fromVerse'] . ' to ' . $toLesson->properties['toVerse']) : 'N/A';

                    return '<span style="color: #b4eeb0;">' . $fromLessonTitle . '<br>To:<br>' . $toLessonTitle . '</span>';
                })

                // Monthly Talqeen Report Column
                ->addColumn('monthlyTalqeenReport', function ($reportDetails) {
                    $fromLesson = ProgramLevelLesson::find($reportDetails['from_talqeen']);
                    $toLesson = ProgramLevelLesson::find($reportDetails['to_talqeen']);

                    $fromLessonTitle = $fromLesson ? ($fromLesson->properties['fromVerse'] . ' to ' . $fromLesson->properties['toVerse']) : 'N/A';
                    $toLessonTitle = $toLesson ? ($toLesson->properties['fromVerse'] . ' to ' . $toLesson->properties['toVerse']) : 'N/A';

                    return '<span style="color: #b4eeb0;">' . $fromLessonTitle . '<br>To:<br>' . $toLessonTitle . '</span>';
                })

                // Total Lessons Column
                ->addColumn('totalLessons', function ($reportDetails) {
                    $totalLessons = ($reportDetails['to_talqeen'] - $reportDetails['from_talqeen']) + 1;
                    return '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalLessons . '</h2>';
                })

                // Total Planned Lessons Column
                ->addColumn('totalPlannedLessons', function ($reportDetails) {
                    $totalPlannedLessons = $reportDetails['planned_lessons'];
                    return '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalPlannedLessons . '</h2>';
                })

                // Attendance Days Percentage Column
                ->addColumn('attendanceDaysPercentage', function ($reportDetails) use ($classId, $year, $month) {
                    $classTimetable = Classes::find($classId)->timetable;
                    if (!$classTimetable) {
                        return '0%';
                    }

                    $totalClasses = 0;
                    $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);

                    for ($day = 1; $day <= $daysInMonth; $day++) {
                        $dayOfWeek = strtolower(date('D', strtotime("$year-$month-$day")));
                        if (!is_null($classTimetable->$dayOfWeek)) {
                            $totalClasses++;
                        }
                    }

                    $attendedClasses = StudentIjazasanadMemorizationReport::where('student_id', $reportDetails['student']->id)
                        ->where('class_id', $classId)
                        ->whereYear('created_at', $year)
                        ->whereMonth('created_at', $month)
                        ->whereIn('attendance_id', [1, 2])
                        ->count();

                    $result = $totalClasses > 0 ? round(($attendedClasses / $totalClasses) * 100, 2) : 0;

                    return '<div class="progress" style="position: relative;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' . $result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%;background-color: #1fff0f;">
                        <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
                    </div>
                </div>';
                })

                // Talqeen Achievement Compared to Talqeen Plan Column
                ->addColumn('talqeenAchievementComparedtoTalqeenPlan', function ($reportDetails) {
                    $plannedLessons = $reportDetails['planned_lessons'];
                    $completedLessons = $reportDetails['completed_lessons'];
                    $result = $plannedLessons > 0 ? round(($completedLessons / $plannedLessons) * 100, 2) : 0;

                    return '<div class="progress" style="position: relative;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' . $result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%;background-color: #1fff0f;">
                        <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
                    </div>
                </div>';
                })

                ->rawColumns(['student', 'monthlyTalqeenPlan', 'monthlyTalqeenReport', 'totalLessons', 'totalPlannedLessons', 'attendanceDaysPercentage', 'talqeenAchievementComparedtoTalqeenPlan'])
                ->make(true);


        }

    }


}

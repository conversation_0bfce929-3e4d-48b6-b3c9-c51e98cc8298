(function(){
  'use strict';

  var cfg = (window.__copytools_cfg || { enable_per_panel: true, enable_master_copy: true });
  var BOOTED_KEY = '__dbg_copy_booted__';

  function safe(fn){ try { fn(); } catch(e) { /* swallow */ } }

  function whenReady(cb){
    if (document.readyState === 'complete' || document.readyState === 'interactive') return cb();
    document.addEventListener('DOMContentLoaded', cb, { once: true });
  }

  function selectHeader(panel){
    return panel.querySelector('.phpdebugbar-header, .phpdebugbar-panel-header, h2, h3, .header');
  }

  function extractPanel(panel){
    try {
      // Attempt to prefer main content regions if present
      var body = panel.querySelector('.phpdebugbar-panel, .phpdebugbar-widgets-list, .phpdebugbar-panel-body');
      var el = body || panel;
      return (el.innerText || el.textContent || '').trim();
    } catch (e) { return ''; }
  }

  function extractAll(){
    var out = {};
    document.querySelectorAll('.phpdebugbar-panel').forEach(function(panel){
      var header = selectHeader(panel);
      var title = header ? (header.innerText || header.textContent || '').split('\n')[0].trim() : 'Panel';
      if (!title) title = 'Panel';
      out[title] = extractPanel(panel);
    });
    try { return JSON.stringify(out, null, 2); } catch(e){ return String(out); }
  }

  function copyToClipboard(text){
    return new Promise(function(resolve){
      if (!text) text = '';
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text).then(resolve).catch(function(){
          fallbackCopy(text); resolve();
        });
      } else { fallbackCopy(text); resolve(); }
    });
  }

  function fallbackCopy(text){
    var ta = document.createElement('textarea');
    ta.value = text;
    ta.setAttribute('readonly','');
    ta.style.position = 'fixed';
    ta.style.left = '-9999px';
    document.body.appendChild(ta);
    ta.select();
    try { document.execCommand('copy'); } catch(e){}
    ta.remove();
  }

  function flash(el){
    if (!el) return;
    el.classList.add('dbg-copied');
    setTimeout(function(){ el.classList.remove('dbg-copied'); }, 900);
  }

  function addPerPanelButtons(root){
    if (!cfg.enable_per_panel) return;
    (root || document).querySelectorAll('.phpdebugbar-panel').forEach(function(panel){
      if (panel.__copy_btn_added__) return; // guard
      var header = selectHeader(panel);
      if (!header) return;
      panel.__copy_btn_added__ = true;

      var btn = document.createElement('button');
      btn.type = 'button';
      btn.className = 'dbg-copy-btn';
      btn.title = 'Copy this panel';
      btn.textContent = 'Copy';
      btn.addEventListener('click', function(){
        var text = extractPanel(panel);
        copyToClipboard(text).then(function(){ flash(btn); });
      });

      // Insert at header end
      header.appendChild(btn);
    });
  }

  function addMasterCopyButton(){
    if (!cfg.enable_master_copy) return;
    if (document.getElementById('dbg-master-copy')) return;

    // Prefer to place inside our Copy Tools widget if present
    var target = document.querySelector('.phpdebugbar .phpdebugbar-widgets .phpdebugbar-widget[data-widget="copytools"], .phpdebugbar .phpdebugbar-widgets');
    if (!target) target = document.querySelector('.phpdebugbar'); // last fallback

    var btn = document.createElement('button');
    btn.id = 'dbg-master-copy';
    btn.type = 'button';
    btn.className = 'dbg-copy-master-btn';
    btn.title = 'Copy all panels';
    btn.textContent = 'Copy All';
    btn.addEventListener('click', function(){
      var payload = extractAll();
      copyToClipboard(payload).then(function(){ flash(btn); });
    });

    // Prefer to prepend so it's visible at top
    if (target.firstChild) target.insertBefore(btn, target.firstChild); else target.appendChild(btn);
  }

  function observe(){
    var bar = document.querySelector('.phpdebugbar');
    if (!bar || bar[BOOTED_KEY]) return;
    bar[BOOTED_KEY] = true;

    addPerPanelButtons(bar);
    addMasterCopyButton();

    // Observe dynamic changes (ajax/history)
    var mo = new MutationObserver(function(muts){
      safe(function(){ addPerPanelButtons(bar); });
      safe(addMasterCopyButton);
    });
    mo.observe(bar, { childList: true, subtree: true });
  }

  whenReady(function(){ safe(observe); });
  // Also run again after load, in case Debugbar is injected late
  window.addEventListener('load', function(){ safe(observe); }, { once: true });
})();

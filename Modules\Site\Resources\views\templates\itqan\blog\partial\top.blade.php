<!-- LEFT -->

<!-- INLINE SEARCH -->
<div class="inline-search clearfix margin-bottom-30">
    <form action="{{url('home/search')}}" method="get" class="widget_search">
        <input type="search" placeholder="{{trans('home_content.search')}}" name="query"
               class="serch-input" style="width:75% !important"/>
        <button type="submit" @if(App::getLocale()=="en") style="margin-right: 25px;" @endif>
            <i class="fa fa-search"></i>
        </button>

    </form>
</div>

<!-- /INLINE SEARCH -->

<hr/>

<!-- side navigation -->
<div class="side-nav margin-bottom-60 margin-top-30">

    <div class="side-nav-head">
        <button class="fa fa-bars"></button>
        <h4>{{trans('home_content.categories')}}</h4>
    </div>
    <ul class="list-group list-group-bordered list-group-noicon uppercase">
        @foreach($categories as $categorie)
            <li class="list-group-item">
                <a href="{{url('home/search?categories_query='.$categorie->id)}}">
                                        <span class="size-11 text-muted pull-right">({{$categorie->count_posts_on_this_categ}}
                                            )</span> <?php echo $categorie->{'categ_title_' . App::getLocale()}?>
                </a>
            </li>
        @endforeach
    </ul>
    <!-- /side navigation -->
</div>


<!-- JUSTIFIED TAB -->
<div class="tabs nomargin-top hidden-xs margin-bottom-60">
    <!-- tabs -->
    <ul class="nav nav-tabs nav-bottom-border nav-justified">
        <li class="active">
            <a href="#tab_1" data-toggle="tab">
                {{trans('home_content.popular')}}
            </a>
        </li>
        <li>
            <a href="#tab_2" data-toggle="tab">
                {{trans('home_content.recent')}}
            </a>
        </li>
    </ul>

    <!-- tabs content -->
    <div class="tab-content margin-bottom-60 margin-top-30">

        <!-- POPULAR -->
        <div id="tab_1" class="tab-pane active">
            @foreach($popular_posts as $popular_post)
                <div class="row tab-post"><!-- post -->
                    <div class="col-md-3 col-sm-3 col-xs-3">
                        <a href="{{url('home/blog/post/'.$popular_post->id.'/'.str_replace(' ','_',$popular_post->{'post_title_'.App::getLocale()}))}}">
                            <?php /*counter */$count_displayed_pics = 0; ?>
                            @foreach($popular_post->attachments as $att)
                                    <?php /*get file extention */$ext = pathinfo($att->post_attachment, PATHINFO_EXTENSION);?>
                                    @if($ext=="jpeg" || $ext=="jpg" || $ext=="png" || $ext=="gif")
                                    <?php $count_displayed_pics++; ?>
                                        <img src="{{URL::to($att->post_attachment)}}" width="50" alt="<?php echo $popular_post->{'post_title_' . App::getLocale()} ?>"/>
                                    @endif
                                @break
                            @endforeach
                            <!-- if all attachment files are not image typed (show default image from 2 default images 1.jpg and 2.jpg  randomly) -->
                                @if($count_displayed_pics==0)
                                    <img src="{{URL::to('home_style/images/default_blog_pic/'.random_int(1,3).'.jpg')}}"
                                         width="50"
                                         alt="<?php echo $popular_post->{'post_title_' . App::getLocale()} ?>"/>
                                @endif
                        </a>
                    </div>
                    <div class="col-md-9 col-sm-9 col-xs-9">
                        <a href="{{url('home/blog/post/'.$popular_post->id.'/'.str_replace(' ','_',$popular_post->{'post_title_'.App::getLocale()}))}}"
                           class="tab-post-link"><?php echo $popular_post->{'post_title_' . App::getLocale()} ?></a>
                        <small>{{date("M d, Y", strtotime($popular_post->created_at))}}</small>
                    </div>
                </div><!-- /post -->
            @endforeach
        </div>
        <!-- /POPULAR -->


        <!-- RECENT -->
        <div id="tab_2" class="tab-pane">

            @foreach($last_added_posts as $last_added_post)
                <div class="row tab-post"><!-- post -->
                    <div class="col-md-3 col-sm-3 col-xs-3">
                        <a href="{{url('home/blog/post/'.$last_added_post->id.'/'.str_replace(' ','_',$last_added_post->{'post_title_'.App::getLocale()}))}}">
                        <?php /*counter */$count_displayed_pics = 0; ?>
                        @foreach($last_added_post->attachments as $att)
                            <!--show one of post images or show default post image-->
                                <?php /*get file extention */$ext = pathinfo($att->post_attachment, PATHINFO_EXTENSION);?>
                                @if($ext=="jpeg" || $ext=="jpg" || $ext=="png" || $ext=="gif")
                                    <?php $count_displayed_pics++; ?>
                                    <img src="{{URL::to($att->post_attachment)}}" width="50"
                                         alt="<?php echo $last_added_post->{'post_title_' . App::getLocale()} ?>"/>
                                @endif
                                @break
                            @endforeach

                        <!-- if all attachment files are not image typed (show default image from 2 default images 1.jpg and 2.jpg  randomly) -->
                            @if($count_displayed_pics==0)
                                <img src="{{URL::to('home_style/images/default_blog_pic/'.random_int(1,3).'.jpg')}}"
                                     width="50"
                                     alt="<?php echo $last_added_post->{'post_title_' . App::getLocale()} ?>"/>
                            @endif


                        </a>
                    </div>
                    <div class="col-md-9 col-sm-9 col-xs-9">
                        <a href="{{url('home/blog/post/'.$last_added_post->id.'/'.str_replace(' ','_',$last_added_post->{'post_title_'.App::getLocale()}))}}"
                           class="tab-post-link">
                            <?php echo $last_added_post->{'post_title_' . App::getLocale()} ?>
                        </a>

                        <small>{{date("F d, Y", strtotime($last_added_post->created_at))}}</small>
                    </div>
                </div><!-- /post -->
            @endforeach

        </div>
        <!-- /RECENT -->

    </div>

</div>
<!-- JUSTIFIED TAB -->




<hr/>


<!-- SOCIAL ICONS -->
<div class="hidden-xs margin-top-30 margin-bottom-60">

    <a href="{{cache('fb_page')}}" class="social-icon social-icon-border social-facebook pull-left"
       data-toggle="tooltip" data-placement="top" title="Facebook">
        <i class="icon-facebook"></i>
        <i class="icon-facebook"></i>
    </a>

    <a href="{{cache('yt_chanel')}}" class="social-icon social-icon-border social-youtube pull-left"
       data-toggle="tooltip" data-placement="top" title="Youtube">
        <i class="icon-youtube"></i>
        <i class="icon-youtube"></i>
    </a>

    <a href="{{cache('instagram')}}"
       class="social-icon social-icon-border social-instagram pull-left"
       data-toggle="tooltip" data-placement="top" title="Instagram">
        <i class="icon-instagram"></i>
        <i class="icon-instagram"></i>
    </a>

    <a href="{{cache('lin')}}" class="social-icon social-icon-border social-linkedin pull-left"
       data-toggle="tooltip" data-placement="top" title="Linkedin">
        <i class="icon-linkedin"></i>
        <i class="icon-linkedin"></i>
    </a>

    <a href="{{cache('tw')}}" class="social-icon social-icon-border social-twitter pull-left"
       data-toggle="tooltip" data-placement="top" title="Twitter">
        <i class="icon-twitter"></i>
        <i class="icon-twitter"></i>
    </a>

</div>



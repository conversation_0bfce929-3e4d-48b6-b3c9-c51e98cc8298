@extends('layouts.hound')

@section('content')
    <div class="container">
        <div class="row">
           

            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">content {{ $content->id }}</div>
                    <div class="panel-body">

                        <a href="{{ url('/workplace/curriculum/contents') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
                        <a href="{{ url('/workplace/curriculum/contents/' . $content->id . '/edit') }}" title="Edit content"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>
                        {!! Form::open([
                            'method'=>'DELETE',
                            'url' => ['workplace/curriculum/contents', $content->id],
                            'style' => 'display:inline'
                        ]) !!}
                            {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                                    'type' => 'submit',
                                    'class' => 'btn btn-danger btn-xs',
                                    'title' => 'Delete content',
                                    'onclick'=>'return confirm("Confirm delete?")'
                            ))!!}
                        {!! Form::close() !!}
                        <br/>
                        <br/>

                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <th>ID</th><td>{{ $content->id }}</td>
                                    </tr>
                                    <tr><th> Title </th><td> {{ $content->title }} </td></tr><tr><th> Content </th><td> {{ $content->content }} </td></tr><tr><th> Content Category Id </th><td> {{ $content->content_category_id }} </td></tr>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

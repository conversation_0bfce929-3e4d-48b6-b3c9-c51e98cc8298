<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Attendance;
use App\AttendanceOption;
use App\Cen_Emp;
use App\Employee;
use App\MoshafJuz;
use App\Role;
use App\Student;
use App\StudentHefzPlan;
use App\YearCheck;
use Brian2694\Toastr\Facades\Toastr;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\EducationalReports\Http\Requests\CreateHefzPlanRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentAttendance;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class EmployeeApplicableLeaveDateController extends Controller
{



    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $employees = Employee::with('roles')
            ->orderBy('full_name')
            ->get();

        $departments = \App\Department::all()->sortBy('department')->pluck('department', 'id')->prepend('select department','');

        // return $class_programs;
        return view('humanresource::employees.employeeApplicableLeaveDate', compact( 'employees','departments'));
    }


    public function update(Request $request)
    {

        DB::beginTransaction();
        try {
            // create or update a record for the teacher center
            $applicableLeaveDate = $request->get('applicableLeaveDate');
            $employee = Employee::find($request->get('employee_id'))->update(['leave_applicable_date' => Carbon::parse($applicableLeaveDate)->format('Y-m-d') ]);

            DB::commit();


            return response()->json([

                'message' => 'Employee applicable leave date assigned/updated'
            ], 201);

        }catch (\Exception $e) {
                \Log::error($e);
                dd($e->getMessage());
                return redirect('student-list');
            }


    }

}

<?php

namespace App\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Organization;
use App\Role;
use App\Employee;

use Illuminate\Http\Request;
use Session;

class ClientsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $keyword = $request->get('search');
        $perPage = 25;

        if (!empty($keyword)) {
            $clients = Organization::where('name', 'LIKE', "%$keyword%")
				->orWhere('content', 'LIKE', "%$keyword%")
				->paginate($perPage);
        } else {
            $clients = Organization::paginate($perPage);
        }

        return view('clients.index', compact('clients'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('clients.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        
        $requestData = $request->all();
        
        Organization::create($requestData);

        Session::flash('flash_message', 'Organization added!');

        return redirect('clients');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $client = Organization::findOrFail($id);

        return view('clients.show', compact('client'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $client = Organization::findOrFail($id);


        $admin_roles = Role::where('organization_id', '=' , 0)
                            ->get()->pluck('name');

        $admins = $client->employees->filter(function ($value , $key) use($admin_roles)
        {
            return count(array_intersect($value->roles->pluck('name')->toArray() , $admin_roles->toArray()));
        });
                            

        return view('clients.edit', compact('client' , 'admins' , 'admin_roles'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {


        foreach($request->admins as $eid => $admin){
            $employee = Employee::findOrFail($eid);
            $employee->syncRoles([$admin['role']]);

        }
        
        // $requestData = $request->all();
        
        // $client = Organization::findOrFail($id);
        // $client->update($requestData);

        Session::flash('flash_message', 'Organization updated!');

        return redirect(route('superior.clients.index'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Organization::destroy($id);

        Session::flash('flash_message', 'Organization deleted!');

        return redirect('clients');
    }
}

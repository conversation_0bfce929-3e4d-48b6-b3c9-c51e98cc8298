<?php

namespace Modules\Admission\Http\Controllers;

use App\BaseSetup;
use App\Center;
use App\Classes;
use App\EmailSetting;
use App\Employee;
use App\GeneralSettings;
use App\Http\Requests\RequestStudentInterviewRequest;
use App\Http\Requests\SetStudentInterviewAssessmentRequest;
use App\Http\Requests\StudentApproveRequest;
use App\Services\EmailService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Organization;
use App\Program;
use App\ProgramTranslation;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Admission;
use App\AdmissionInterview;
use App\AdmissionInterviewer;
use App\Student;
use App\StudentHefzPlan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;
use Modules\Admission\Notifications\DependantStudentAccepted;
use Modules\ApplicationCenter\Entities\RegistrationSetting;


class AdmissionInterviewAssessmentController extends Controller
{
    protected $emailService;



    /**
     * @param SetStudentInterviewAssessmentRequest $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function setInterviewAssessment(SetStudentInterviewAssessmentRequest $request)
    {

        try {

            DB::beginTransaction();



            $admission = Admission::findOrFail($request->admission_id);
            $student = $admission->student;
            $interviewTime = $request->get('interview_time');
            $hoursToInterview = $this->calculateHoursToInterview($interviewTime);


            $userInfo = [];

//            $waiting_for_interview = false;
            $waiting_for_interview = false;
            $approved_programs = 0;
            // get admission programs
            foreach ($admission->programs as $program) {
                // check if the admission aproved
                $userInfo[0]["email"] = $admission->student_email;
                $userInfo[0]["studentName"] = Student::find($admission->student_id)->full_name;

                if ($request->interview[$program->id]['approve']) {


                    $approved_programs++;
                    // check if an interview is required
                    if ($program->require_interview) {
                        $waiting_for_interview = true;
                        // create a new interview record
                        $interview_details = $request->interview[$program->id];
                        $interview_details['admission_id'] = $request->admission_id;
                        $interview_details['program_id'] = $program->id;
                        $interview_details['status'] = 'waiting_for_interview';

                        $userInfo[] = array('interviewLocation' => $interview_details['location'], 'interview_duration' => '1 hour',
                            'interviewDateTime' => $interview_details['interview_time'], 'email' => $admission->student_email,
                            'id' => $student->id, 'slug' => 'student', 'interviewCommitteeIds' => $interview_details['committee'],
                            'interviewDetails' => $interview_details['notes'], 'programTitle' => $program->programTranslations->first()->title);
                        $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);
                        $systemEmail = EmailSetting::find(1);
                        $system_email = $systemEmail->from_email;
                        $organization_name = $systemSetting->organization_name;
                        $sender['system_email'] = $system_email;
                        $sender['organization_name'] = $organization_name;
                        if ($admission->creator_role == 'parent') {
                            $userInfo[0]["guardian_email"] = $admission->guardian_email;
                        }

                        $userInfo[0]["channel"] = "email";
                        $userInfo[0]["interviewConfirmationExpiryHours"] = $hoursToInterview;

                        $userInfo[0]["admissionId"] = $request->admission_id;

                        $userInfo[0]["phone"] = GeneralSettings::where("organization_id", config('organization_id'))->first()->phone;

                        $userInfo[0]["subjectInterviewParticipants"] = implode(", ", Employee::whereIn("id", $interview_details['committee'])->pluck("name")->toArray()) . " & " . $student->display_name;


                        $userInfo[0]["InterviewerEmails"] = Employee::whereIn("id", $interview_details['committee'])->pluck("email")->toArray();
                        // save the interview details
                        $userInfo[0]["interviewCommitteeIds"] = $interview_details['committee'];



                        $interview = AdmissionInterview::create($interview_details);
                        // set interview committee
                        $interview_committee = $interview_details['committee'];

                        $this->createInterviewCommitteeMembers($interview_committee, $interview->id);

                        $this->sendInterviewInvitationEmail($userInfo, $sender);

                    }
                }
            }




            if ($waiting_for_interview) {
                $admission->status = 'waiting_for_interview';
                $student->status = 'waiting_for_interview';
                $message = 'application waiting for interview';
            } else if ($approved_programs == $admission->programs->count()) {
                $admission->status = 'offer';
                $student->status = 'offer';
                $message = 'application has been offered';
            } else if ($approved_programs > 0) {
                $admission->status = 'conditional_offer';
                $message = 'application has been approved';

            } else {

                $admission->status = 'rejected';
                $admission->rejected_status_note = $request->interview[$program->id]['notes'];
                $student->status = 'rejected';
                $message = 'application has been rejected and student has been sent an email notification';
                $this->sendInterviewRejectionEmail($userInfo, $sender);

            }
            $admission->save();
            $student->save();
            DB::commit();

            return $this->respondAjaxRequest('success',$message);

        }
        catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            \Log::error('Failed to set up interview or send email: ' . $e->getMessage());
            return $this->respondAjaxRequest('error', $e->getMessage());

            // Handle the error
        }

    }
    private function respondAjaxRequest($status, $message = '') {
        if ($status === 'error') {
            return response()->json(["status" => "error", "message" => $message],500);
        }
        return response()->json(["status" => "success", "message" => $message],200);
    }

    public function setOrientation()
    {
        $request = request();

        $admission = Admission::findOrFail($request->admission_id);

        $this->validate($request, [
            'admission_id' => 'required | numeric',
            'employee_id' => 'required | numeric',
            'orientation_time' => 'required | date',
            'location' => 'required'
        ]);

        $requestData = $request->all();

        $requestData['organization_id'] = config('organization_id');
        $requestData['created_by'] = auth()->user()->id;

        \App\AdmissionOrientation::create($requestData);
        $admission->status = "waiting_for_orientation";

        $admission->save();

        flash('Orintation Session Has Been Set . ');

        return redirect()->back();

    }

    public function finalize(Request $request)
    {
        $admission = Admission::findOrFail($request->admission_id);

        $admission->status = 'active';
        $admission->start_date = date('Y-m-d');

        $admission->save();

        $student = Student::findOrFail($admission->student_id);
        $student->status = 'active';
        $student->save();
        // Check if the program is Nuraniyah
        $program = Program::find($admission->program_id);
        if (\Illuminate\Support\Str::contains(strtolower($program->title), ['nuraniyah', 'nouranya'])) {
            // Fetch the ProgramLevel where the translated title contains 'level 1' or 'level1'
            $levelId = \App\ProgramLevel::whereTranslationLike('title', 'LIKE', '%level 1%')
                    ->orWhereTranslationLike('title', 'LIKE', '%level1%')->first()->id;
            // Set student level to 1 if it belongs to a Nuraniyah program
            $studentProgramLevel = $student->studentProgramLevels()->updateOrCreate(
                ['student_id' => $student->id, 'class_id' => $admission->class_id],
                ['level_id' => $levelId, 'status' => 'active']
            );
        }
        if (\Illuminate\Support\Str::contains(strtolower($program->title), ['Ijazah and Sanad', 'ijazah and sanad'])) {
            // Fetch the ProgramLevel where the translated title contains 'level 1' or 'level1'
            $levelId = \App\ProgramLevel::whereTranslationLike('title', 'LIKE', '%Level 1: Preparation Course%')->first()->id;
            // Set student level to 1 if it belongs to a Nuraniyah program
            $studentProgramLevel = $student->studentProgramLevels()->updateOrCreate(
                ['student_id' => $student->id, 'class_id' => $admission->class_id],
                ['level_id' => $levelId, 'status' => 'active']
            );
        }

        if (!\App\ClassStudent::where('class_id', $admission->class_id)->where('student_id', $admission->student_id)->first()) {

            $join_class = new \App\ClassStudent();

            $join_class->student_id = $admission->student_id;
            $join_class->class_id = $admission->class_id;
            $join_class->start_date = date('Y - m - d');

            $join_class->save();
            flash('Student was added to  class successfully.');
        } else {
            flash('Student already has joined the class.');
        }


        return redirect()->back();

    }


    function createInterviewCommitteeMembers($interview_committee, $interview_id) {
        foreach ($interview_committee as $interviewer) {
            // create a new interview committee member record
            AdmissionInterviewer::create([
                'admission_interview_id' => $interview_id,
                'employee_id' => $interviewer
            ]);
        }
    }

    function calculateHoursToInterview($interviewTime) {
        $date = strtotime($interviewTime); // Convert to a PHP date (a second count)
        $diff = $date - time(); // time() returns current time in seconds
        $days = floor($diff / (60 * 60 * 24)); // seconds/minute*minutes/hour*hours/day)
        $hoursToInterview = round(($diff - $days * 60 * 60 * 24) / (60 * 60));
        return (int) $hoursToInterview;
    }

    function sendInterviewInvitationEmail($userInfo, $sender)
    {


            $userInfo = $userInfo[0];

            $committeeMembers = Employee::whereIn('id', $userInfo['interviewCommitteeIds'])
                ->get(['email', 'full_name']);

            $ccRecipients = $committeeMembers->map(function ($member) {
                return [
                    'email' => $member->email,
                    'name' => $member->full_name
                ];
            })->all();

            $emailService = app(\App\Services\EmailService::class);
            $emailService->sendEmail(
                ['email' => $userInfo['email'], 'name' => $userInfo['studentName']],
                'Interview at ITQAN for the ' . $userInfo['programTitle'] . ' program',
                'modules.site.templates.wajeha.backEnd.studentInformation.student_interview', // Update this with the correct view path
                ['data' => $userInfo],
                [], // Default 'from' address or specify if needed
                $ccRecipients // CC recipients
            );

//            $email->is_sent = true;
//            $email->save();

        }



    /**
     * @throws \Exception
     */
    function sendInterviewRejectionEmail($userInfo, $sender)
    {


        $userInfo = $userInfo[0];

        $emailService = app(\App\Services\EmailService::class);
        $emailService->sendEmail(
            ['email' => $userInfo['email'], 'name' => $userInfo['studentName']],
            'Your Application to ITQAN for the ' . $userInfo['programTitle'] . ' program',
            'modules.site.templates.wajeha.backEnd.studentInformation.rejection_notification', // Updated view path
            ['data' => $userInfo] // Passing necessary data to the view
        );



//        $userInfo =  $userInfo[0];
//        $mainRecipient = [
//            [
//                'email' => $userInfo['email'],
//                'name' => $userInfo['studentName']
//            ]
//        ];
//        $emailHtmlContent = view('modules.site.templates.wajeha.backEnd.studentInformation.rejection_notification', ['data' => $userInfo])->render();
//        $email = $this->saveEmailDetails($userInfo['email'],$emailHtmlContent);
        // Mailjet client setup
//        $mj = new \Mailjet\Client(env('MAILJETAPIKEY'), env('MAILJETAPISECRET'),true,['version' => 'v3.1']);

// Preparing the email body
//        $body = [
//            'Messages' => [
//                [
//                    'From' => [
//                        'Email' => "<EMAIL>",
//                        'Name' => "ITQAN"
//                    ],
//                    'To' => $mainRecipient,
//
//                    'Subject' => 'Your Application to ITQAN for the '.$userInfo['programTitle'].' program',
//                    'HTMLPart' => $emailHtmlContent
//                ]
//            ]
//        ];
//
//// Sending the email
//        $response = $mj->post(Resources::$Email, ['body' => $body]);

//        if (!$response->success()) {
//            throw new \Exception("Failed to send email: " . json_encode($response->getData()));
//        }


//        // If email is sent successfully, update the is_sent field in the database
//        $email->is_sent = true;
//        $email->save();


    }

    private function saveEmailDetails($studentEmail,$emailHtmlContent)
    {


        return \App\Email::create([
            'email' => $studentEmail,
            'subject' => 'Your ( '.$studentEmail.' ) Interview at ITQAN for the' ,
            'message' => $emailHtmlContent
        ]);
    }


}

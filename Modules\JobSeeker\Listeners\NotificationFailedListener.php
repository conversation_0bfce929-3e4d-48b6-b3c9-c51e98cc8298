<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Listeners;

use Illuminate\Notifications\Events\NotificationFailed;
use Mo<PERSON>les\JobSeeker\Services\JobNotificationMonitoringService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

final class NotificationFailedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @param JobNotificationMonitoringService $monitoringService
     * @return void
     */
    public function __construct(
        private readonly JobNotificationMonitoringService $monitoringService
    ) {}

    /**
     * Handle the event.
     *
     * @param NotificationFailed $event
     * @return void
     */
    public function handle(NotificationFailed $event): void
    {
        try {
            $notifiable = $event->notifiable;
            $notification = $event->notification;
            $channel = $event->channel;
            
            // Safely extract error message from event data
            $errorMessage = $this->extractErrorMessage($event);
            
            // Get recipient email safely
            $recipientEmail = $this->getRecipientEmail($notifiable);
            
            Log::warning('Job notification failed', [
                'channel' => $channel,
                'recipient' => $recipientEmail,
                'error' => $errorMessage,
                'notification_class' => get_class($notification)
            ]);
            
            // Extract setup and job IDs
            $setupId = $this->extractSetupId($notification);
            $jobId = $this->extractJobId($notification);
            
            // Track the failure using the monitoring service
            $this->monitoringService->trackFailure(
                $setupId,
                $recipientEmail,
                $channel,
                $errorMessage,
                $jobId
            );
            
        } catch (\Exception $e) {
            Log::error('Error processing notification failure', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'notification_class' => $event->notification ? get_class($event->notification) : 'unknown'
            ]);
        }
    }

    /**
     * Safely extract error message from event data
     *
     * @param NotificationFailed $event
     * @return string
     */
    private function extractErrorMessage(NotificationFailed $event): string
    {
        if (!isset($event->data) || !is_array($event->data)) {
            return 'Unknown error (no data available)';
        }

        return $event->data['message'] ?? 'Unknown error (no message provided)';
    }

    /**
     * Safely get recipient email
     *
     * @param mixed $notifiable
     * @return string
     */
    private function getRecipientEmail($notifiable): string
    {
        if (!$notifiable) {
            return 'unknown (no notifiable)';
        }

        if (is_object($notifiable) && method_exists($notifiable, 'routeNotificationFor')) {
            $email = $notifiable->routeNotificationFor('mail');
            if ($email) {
                return $email;
            }
        }

        return $notifiable->email ?? 'unknown (no email property)';
    }

    /**
     * Extract setup ID from notification
     *
     * @param mixed $notification
     * @return int|null
     */
    private function extractSetupId($notification): ?int
    {
        if (!$notification) {
            return null;
        }

        try {
            if (method_exists($notification, 'getSetupId')) {
                return $notification->getSetupId();
            }

            if (isset($notification->setup)) {
                if (method_exists($notification->setup, 'getAttribute')) {
                    return $notification->setup->getAttribute('id');
                }
                
                if (isset($notification->setup->id)) {
                    return $notification->setup->id;
                }
            }
        } catch (\Exception $e) {
            Log::debug('Failed to extract setup ID', [
                'error' => $e->getMessage(),
                'notification_class' => get_class($notification)
            ]);
        }

        return null;
    }

    /**
     * Extract job ID from notification
     *
     * @param mixed $notification
     * @return int|null
     */
    private function extractJobId($notification): ?int
    {
        if (!$notification) {
            return null;
        }

        try {
            if (method_exists($notification, 'getJobId')) {
                return $notification->getJobId();
            }

            if (isset($notification->job)) {
                if (method_exists($notification->job, 'getAttribute')) {
                    return $notification->job->getAttribute('id');
                }
                
                if (isset($notification->job->id)) {
                    return $notification->job->id;
                }
            }
        } catch (\Exception $e) {
            Log::debug('Failed to extract job ID', [
                'error' => $e->getMessage(),
                'notification_class' => get_class($notification)
            ]);
        }

        return null;
    }
} 
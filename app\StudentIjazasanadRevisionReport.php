<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class StudentIjazasanadRevisionReport extends Model
{

    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'student_ijazasanad_revision_report';

    /**
     * The fillable columns
     */
    protected $fillable = [
        'created_at',
        'updated_at',
        'class_id',
        'organization_id',
        'student_id',
        'class_report_id',
        'revision_from_surat',
        'revision_from_ayat',
        'revision_to_surat',
        'revision_to_ayat',
        'revision_evaluation_id',
        'revision_evaluation_note',
        'class_time',
        'program_id',
        'teacher_id',
        'revision_plan_id',
        'attendance_id',
        'from_surat_juz_id',
        'to_surat_juz_id',
        'delete_reason',
        'deleted_at',
        'pages_revised'
    ];
    // Define a local scope in the StudentHefzReport model
    public function scopeDistinctMonthsYears($query) {
        return $query->selectRaw('DISTINCT MONTHNAME(created_at) as month, YEAR(created_at) as year')
            ->orderBy('created_at', 'desc');
    }

    public function scopeCurrentMonthAndYear($query)
    {
        $currentMonth = now()->month;
        $currentYear = now()->year;

        $query->where(function($q) use ($currentMonth, $currentYear) {
            $q->whereYear('created_at', $currentYear)
                ->whereMonth('created_at', $currentMonth)

            ;
        });
    }

    protected $primaryKey = 'id';


    public function classes()
    {

        return $this->belongsTo(Classes::class,'class_id','id');
    }

    public function getPagesRevisedAttribute()
    {

        if ($this->revisionPlan['study_direction'] == 'backward') {

            $memorizedNumberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                $this->revision_from_surat,
                $this->revision_from_ayat,
                $this->revision_to_surat,
                $this->revision_to_ayat
            ]);


            $revisedNumberofPages = $memorizedNumberofPages[0]->numberofPagesSum;

        }

        else {
            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                $this->revision_from_surat,
                $this->revision_from_ayat,
                $this->revision_to_surat,
                $this->revision_to_ayat
            ]);

            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
            $revisedNumberofPages = $results[0]->number_of_pages_sum;

        }


        $revisedNumberofPages = isset($revisedNumberofPages) ? $revisedNumberofPages : 0;


        return $revisedNumberofPages;

    }

    public function revisionPlan()
    {

        return $this->belongsTo(IjazasanadRevisionPlan::class, 'revision_plan_id', 'id');
    }


    public function fromSurat()
    {


        return $this->belongsTo(MoshafSurah::class, 'revision_from_surat', 'id');
    }

    public function toSurat()
    {


        return $this->belongsTo(MoshafSurah::class, 'revision_to_surat', 'id');
    }


    public function result()
    {
        return $this->hasOne('App\EvaluationSchemaOption', 'id', 'revision_evaluation_id');
    }

    public function student()
    {
        return $this->belongsTo('App\Student');
    }
}

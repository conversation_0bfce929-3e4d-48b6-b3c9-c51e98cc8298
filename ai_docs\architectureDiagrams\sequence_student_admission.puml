@startuml sequence_student_admission
!theme vibrant

title Student Admission Application Submission Sequence Diagram

actor Applicant
participant Browser
participant AdmissionFormController << C >> #LightBlue
participant AdmissionRequest << R >> #LightGreen
participant AdmissionService << S >> #Pink
participant Student << M, Admission >> #LightYellow
participant User << M, General >> #LightYellow
participant Account << M, Account >> #LightYellow
participant AdmissionNotification << N >> #Orange
participant Database << DB >> #Gray

Applicant -> Browser : Fills Admission Form & Submits
Browser -> AdmissionFormController : POST /admission/apply (formData)
activate AdmissionFormController

AdmissionFormController -> AdmissionRequest : validate(formData)
activate AdmissionRequest
AdmissionRequest --> AdmissionFormController : Validation Passed/Failed
deactivate AdmissionRequest

alt Validation Failed
    AdmissionFormController -> Browser : Redirect Back (with errors)
else Validation Passed
    AdmissionFormController -> AdmissionService : createAdmissionApplication(validatedData)
    activate AdmissionService

    AdmissionService -> Database : Start Transaction
    activate Database #DarkGray

    ' Assuming User record is created first (or linked if exists)
    AdmissionService -> User : createUser(data)
    activate User
    User -> Database : INSERT INTO users ...
    Database --> User : New User ID
    User --> AdmissionService : User Instance
    deactivate User

    ' Create Student record in Admission module
    AdmissionService -> Student : createStudent(data, userId)
    activate Student
    Student -> Database : INSERT INTO students (admission_module_table) ...
    Database --> Student : New Student ID
    Student --> AdmissionService : Student Instance
    deactivate Student

    ' Create associated Account in Account module (example interaction)
    AdmissionService -> Account : createInitialAccount(studentId)
    activate Account
    Account -> Database : INSERT INTO accounts ...
    Database --> Account : New Account ID
    Account --> AdmissionService : Account Instance
    deactivate Account

    AdmissionService -> Database : Commit Transaction
    deactivate Database #DarkGray

    ' Send Notification (e.g., to admin or applicant)
    AdmissionService -> AdmissionNotification : notify(applicationDetails)
    activate AdmissionNotification #DarkOrange
    ' (Notification logic, e.g., queuing email)
    AdmissionNotification --> AdmissionService
    deactivate AdmissionNotification #DarkOrange

    AdmissionService --> AdmissionFormController : Application Created (Success)
    deactivate AdmissionService

    AdmissionFormController -> Browser : Redirect to Success Page / Dashboard
end

deactivate AdmissionFormController
Browser -> Applicant : Display Success Page / Form with Errors

@enduml 
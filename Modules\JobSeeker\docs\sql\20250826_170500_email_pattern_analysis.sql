-- Context: Email notification pattern analysis for JobSeeker module
-- Purpose: Document identified problems and patterns in email delivery system
-- Created: 2025-08-26 17:05:00

-- =====================================================
-- CRITICAL ISSUES IDENTIFIED
-- =====================================================

-- ISSUE 1: Template Rendering Error (CRITICAL)
-- Route [jobseeker.ai.tailor.show] not defined in email template
-- Affects: All emails using jobseeker_notification_new.blade.php
-- Impact: 100% failure rate when this route is referenced
-- Evidence: 2 failed emails on Aug 15 and Aug 17, 2025

SELECT 
    id,
    recipient,
    subject,
    last_error_message,
    created_at
FROM outgoing_emails 
WHERE status = 'failed'
    AND last_error_message LIKE '%jobseeker.ai.tailor.show%'
ORDER BY created_at DESC;

-- ISSUE 2: Massive Volume Drop (HIGH PRIORITY)
-- Email volume dropped from 200+ per day to 1-2 per day
-- Peak: Aug 8-10 (200+ emails/day, 100% success)
-- Current: 1-2 emails/day
-- Evidence: Volume analysis shows dramatic decline

SELECT 
    DATE(created_at) as email_date,
    COUNT(*) as total_emails,
    COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_emails,
    ROUND(COUNT(CASE WHEN status = 'sent' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM outgoing_emails 
WHERE subject LIKE '%Job Alert%'
    AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY email_date DESC;

-- ISSUE 3: Notification Tracking Disconnect (CRITICAL)
-- Emails are being sent but not recorded in job_notification_sent_jobs
-- Evidence: Hundreds of emails sent but only 1 notification record in 60 days
-- Impact: No idempotency protection, potential duplicate notifications

SELECT 
    'Emails Sent' as metric,
    COUNT(*) as count
FROM outgoing_emails 
WHERE subject LIKE '%Job Alert%'
    AND created_at >= DATE_SUB(NOW(), INTERVAL 60 DAY)
    AND status = 'sent'

UNION ALL

SELECT 
    'Notifications Recorded' as metric,
    COUNT(*) as count
FROM job_notification_sent_jobs
WHERE sent_at >= DATE_SUB(NOW(), INTERVAL 60 DAY);

-- ISSUE 4: Jobs Fetched But No Notifications (HIGH PRIORITY)
-- Recent executions fetch jobs but don't generate notifications
-- Evidence: Multiple executions with jobs_fetched > 0 but no emails/notifications

SELECT 
    cse.id,
    cse.command,
    cse.jobs_fetched,
    COUNT(jnsj.id) as notifications_recorded,
    COUNT(oe.id) as emails_sent,
    CASE 
        WHEN cse.jobs_fetched > 0 AND COUNT(oe.id) = 0 THEN 'Jobs fetched but no emails'
        WHEN COUNT(oe.id) > 0 AND COUNT(jnsj.id) = 0 THEN 'Emails sent but not tracked'
        ELSE 'Normal'
    END as issue_type
FROM command_schedule_executions cse
LEFT JOIN job_notification_sent_jobs jnsj ON jnsj.sent_at BETWEEN cse.started_at AND COALESCE(cse.completed_at, NOW())
LEFT JOIN outgoing_emails oe ON oe.created_at BETWEEN cse.started_at AND COALESCE(cse.completed_at, NOW())
    AND oe.subject LIKE '%Job Alert%'
WHERE cse.started_at >= DATE_SUB(NOW(), INTERVAL 15 DAY)
GROUP BY cse.id, cse.command, cse.jobs_fetched
HAVING issue_type != 'Normal'
ORDER BY cse.started_at DESC;

-- =====================================================
-- PATTERN ANALYSIS
-- =====================================================

-- Pattern 1: Single User Dominance
-- Recent emails mostly go to one recipient: <EMAIL>
-- Suggests other users may not have active setups or matching categories

SELECT 
    recipient,
    COUNT(*) as email_count,
    MIN(created_at) as first_email,
    MAX(created_at) as last_email
FROM outgoing_emails 
WHERE subject LIKE '%Job Alert%'
    AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY recipient
ORDER BY email_count DESC;

-- Pattern 2: Retry Behavior
-- Failed emails always reach max_attempts (3)
-- Successful emails usually send on first attempt

SELECT 
    send_attempts,
    status,
    COUNT(*) as email_count
FROM outgoing_emails 
WHERE subject LIKE '%Job Alert%'
    AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY send_attempts, status
ORDER BY send_attempts, status;

-- Pattern 3: Category Mapping Issues
-- Many executions show categories_processed = 0
-- Suggests category mapping problems preventing notifications

SELECT 
    cse.id,
    cse.command,
    cse.jobs_fetched,
    JSON_EXTRACT(cse.error_details, '$.categories_processed') as categories_processed,
    JSON_EXTRACT(cse.error_details, '$.skipped_no_category_map') as skipped_no_category_map
FROM command_schedule_executions cse
WHERE cse.started_at >= DATE_SUB(NOW(), INTERVAL 15 DAY)
    AND cse.jobs_fetched > 0
    AND (
        JSON_EXTRACT(cse.error_details, '$.categories_processed') = 0 OR
        JSON_EXTRACT(cse.error_details, '$.skipped_no_category_map') > 0
    )
ORDER BY cse.started_at DESC;

-- =====================================================
-- ROOT CAUSE INDICATORS
-- =====================================================

-- Indicator 1: Active Setup Analysis
-- Check if users have properly configured notification setups

SELECT 
    jns.id,
    jns.name,
    jns.is_active,
    js.email,
    COUNT(jnc.category_id) as category_count,
    jns.last_notified_at
FROM job_notification_setups jns
JOIN job_seekers js ON jns.job_seeker_id = js.id
LEFT JOIN job_notification_category jnc ON jns.id = jnc.setup_id
GROUP BY jns.id, jns.name, jns.is_active, js.email, jns.last_notified_at
ORDER BY jns.is_active DESC, category_count DESC;

-- Indicator 2: Category Mapping Coverage
-- Check if recent jobs have proper category mappings

SELECT 
    'Recent Jobs' as metric,
    COUNT(DISTINCT j.id) as count
FROM jobs j
WHERE j.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

UNION ALL

SELECT 
    'Jobs with Categories' as metric,
    COUNT(DISTINCT j.id) as count
FROM jobs j
JOIN job_category_pivot jcp ON j.id = jcp.job_id
WHERE j.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)

UNION ALL

SELECT 
    'Jobs with Canonical Categories' as metric,
    COUNT(DISTINCT j.id) as count
FROM jobs j
JOIN job_category_pivot jcp ON j.id = jcp.job_id
JOIN job_categories jc ON jcp.category_id = jc.id
WHERE j.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    AND jc.is_canonical = 1;

# GitLab ➜ GitHub Migration Guide for **itqan**

> **⚠️ CRITICAL WARNING:** This guide details how to completely **overwrite** the existing GitHub repository at `github.com/HashmatWaziri/itqan`. The history on that repository from 2022 will be permanently replaced by your current GitLab repository's history. This action is irreversible.

> Goal: move the `itqan` monorepo (branch **CommunicationModule**) from GitLab to GitHub with **zero downtime**  
> Reason: unlock Cursor AI “Background Agents” & “Bugbot”, which require GitHub.

---

## 1. Pre-flight Checklist

| Item | Action |
|------|--------|
| **Full backup** | `sudo tar -czf /var/backups/itqan_$(date +%F).tgz /var/www/html/itqanalquran/.git` |
| **Existing GitHub Repo** | Confirm you have admin access to `github.com/HashmatWaziri/itqan` |
| **GitHub PAT** | Create a [Personal Access Token](https://github.com/settings/tokens) with `repo` + `workflow` scopes |
| **Production SSH Key** | `ssh-keygen -t ed25519 -C "deploy@itqan"`<br>• Add *public* key as **Deploy key (RO)** in GitHub repo settings<br>• Keep *private* key on `/home/<USER>/.ssh/id_ed25519` |
| **Secrets** | In GitHub → Repo → ⚙ Settings → Secrets → Actions:<br>`PROD_SSH_KEY`, `PROD_HOST`, `PROD_PATH`, `PROD_USER` |
| **Spreadsheet (optional)** | List branches/tags for verification & rollback refs |

---

## 2. Overwrite GitHub Repo with GitLab History

This procedure will make the GitHub repository an exact mirror of your GitLab repository, overwriting any existing history on GitHub.

```bash
# On your local machine (run in a new, empty directory)
git clone --mirror <YOUR_GITLAB_REPO_URL> itqan-mirror
cd itqan-mirror
git remote set-<NAME_EMAIL>:HashmatWaziri/itqan.git

# THIS IS THE DESTRUCTIVE STEP. IT OVERWRITES THE GITHUB REPO.
# It will force-push all branches and tags from GitLab to GitHub.
git push --mirror origin
```

*This command ensures that **all refs** (branches, tags) from GitLab now exist on GitHub, and any refs that were only on GitHub are deleted.*

---

## 3. Switch Production to GitHub (zero-downtime)

```bash
ssh deployment_user@prod "
  cd /var/www/html/itqanalquran &&
  git remote set-<NAME_EMAIL>:HashmatWaziri/itqan.git &&
  git fetch origin &&
  git checkout CommunicationModule
"
```

*Rollback:* `git remote set-url origin <YOUR_GITLAB_REPO_URL>`

---

## 4. GitHub Actions → `.github/workflows/deploy.yml`

This replicates your current `.gitlab-ci.yml`. Create this file in your repository.

```yaml
name: Deploy to Production
on:
  push:
    branches: [ CommunicationModule ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.PROD_SSH_KEY }}

      - name: Remote Deploy
        env:
          PROD_HOST: ${{ secrets.PROD_HOST }}
          PROD_USER: ${{ secrets.PROD_USER }}
          PROD_PATH: ${{ secrets.PROD_PATH }}
        run: |
          ssh -o StrictHostKeyChecking=no $PROD_USER@$PROD_HOST <<'SSH'
          cd $PROD_PATH
          sudo chown -R deployment_user:www-data public/students public/temp/users public/temp/guardians
          git add .
          git commit -m "Preserve local changes (e.g., user-uploaded images)" || true
          git fetch origin
          if ! git merge origin/CommunicationModule -X theirs; then
            echo "Merge conflicts detected. Accepting remote changes…"
            git diff --name-only --diff-filter=U | xargs git checkout --theirs
            git add .
            git commit -m "Resolved merge conflicts by accepting remote changes"
          fi
          CHANGED=$(git diff --name-only ${{ github.event.before }} ${{ github.sha }} | grep -E 'composer\.json|composer\.lock' || true)
          if [ -n "$CHANGED" ]; then
            echo "composer.json/lock changed → running composer install"
            composer install --no-dev --optimize-autoloader
          else
            echo "No composer.lock changes → skipping composer install"
          fi
          SSH
```

*Key points*  
• Trigger identical to GitLab: push to **CommunicationModule**  
• Executes the exact shell commands already proven in production  
• `webfactory/ssh-agent` injects the deploy key

---

## 5. Update Developer Remotes

All developers must run this command in their local repository to point to GitHub.

```bash
git remote set-<NAME_EMAIL>:HashmatWaziri/itqan.git
```

*(Generate one-liner list for team via spreadsheet if many repos.)*

---

## 6. Branch Protection & CI Required Status Checks

1. GitHub → Repo → Settings → Branches → “Add rule”  
2. Pattern: `CommunicationModule`  
3. Require pull-request review + status check `deploy` to prevent broken mainline.

---

## 7. Validation

| Step | Command |
|------|---------|
| **Commit SHA Parity** | `git ls-remote <YOUR_GITLAB_REPO_URL> CommunicationModule` <br> `git ls-remote **************:HashmatWaziri/itqan.git CommunicationModule` <br> *(The commit hashes should be identical)* |
| **Pipeline run** | Ensure first GitHub Actions workflow reaches ✅ |
| **App health** | Hit a known application endpoint or run a UI smoke test |

---

## 8. Post-Cutover Cleanup

* Archive GitLab repo or set to **read-only**  
* Delete legacy GitLab runners to avoid confusion  
* Update any webhooks (Sentry, Slack) to GitHub equivalents  

---

## 9. Rollback Plan

**Note:** This plan restores the production deployment source. It does **not** restore the pre-2022 history of the GitHub repository, which was overwritten in Step 2.

1. `git remote set-url origin <YOUR_GITLAB_REPO_URL>` on the production server.
2. Disable GitHub Action → Settings → Actions → Disable workflow.
3. Notify team to repoint remotes back to GitLab.

---

### References

* GitHub Docs: [Managing remote repositories](https://docs.github.com/en/get-started/getting-started-with-git/managing-remote-repositories)
* GitHub Docs: [Using SSH deploy keys](https://docs.github.com/en/developers/overview/managing-deploy-keys)  
* Medium: “Migrating from GitLab to GitHub” – tips on PATs & bulk mirror [link](https://duncan-mcardle.medium.com/migrating-from-gitlab-**********************)  

---

**Done!** You now have Bugbot-compatible GitHub repo, identical CI/CD, and a clear rollback path.
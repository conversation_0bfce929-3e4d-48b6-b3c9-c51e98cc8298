<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\FormBuilderTranslation
 *
 * @property int $id
 * @property int $form_builder_id
 * @property string $locale
 * @property string $title
 * @property string $date_label
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderTranslation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderTranslation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderTranslation query()
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderTranslation whereDateLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderTranslation whereFormBuilderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderTranslation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderTranslation whereLocale($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderTranslation whereTitle($value)
 * @mixin \Eloquent
 */
class FormBuilderTranslation extends Model
{

    public $timestamps = false;
    
    protected $fillable = ['title' , 'date_label'];

}

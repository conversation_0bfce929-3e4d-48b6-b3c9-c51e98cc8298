<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use <PERSON><PERSON>les\JobSeeker\Entities\JobSeeker;
use Modules\JobSeeker\Entities\JobSeekerPasswordHistory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

/**
 * Password History Service for JobSeeker
 * 
 * Manages password history to prevent reuse of recent passwords
 * and enforces password change policies.
 */
final class PasswordHistoryService
{
    /**
     * Number of previous passwords to remember
     */
    private const PASSWORD_HISTORY_COUNT = 5;

    /**
     * Minimum hours between password changes
     */
    private const MIN_PASSWORD_CHANGE_HOURS = 1;

    /**
     * Maximum password history entries
     */
    private const MAX_PASSWORD_HISTORY = 5;

    /**
     * Check if password change is allowed based on timing restrictions
     */
    public function canChangePassword(JobSeeker $jobSeeker): bool
    {
        $lastPasswordChange = JobSeekerPasswordHistory::where('job_seeker_id', $jobSeeker->id)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$lastPasswordChange) {
            return true; // No previous password changes
        }

        $hoursSinceLastChange = now()->diffInHours($lastPasswordChange->created_at);
        
        return $hoursSinceLastChange >= self::MIN_PASSWORD_CHANGE_HOURS;
    }

    /**
     * Check if a password was recently used
     */
    public function isPasswordRecentlyUsed(JobSeeker $jobSeeker, string $newPassword): bool
    {
        $recentPasswords = JobSeekerPasswordHistory::where('job_seeker_id', $jobSeeker->id)
            ->orderBy('created_at', 'desc')
            ->take(self::PASSWORD_HISTORY_COUNT)
            ->get();

        foreach ($recentPasswords as $passwordHistory) {
            if (Hash::check($newPassword, $passwordHistory->password_hash)) {
                Log::info('Password reuse attempt detected', [
                    'job_seeker_id' => $jobSeeker->id,
                    'email' => $jobSeeker->email,
                    'password_history_id' => $passwordHistory->id,
                ]);
                
                return true;
            }
        }

        return false;
    }

    /**
     * Record a password change in history
     */
    public function recordPasswordChange(JobSeeker $jobSeeker, string $oldPasswordHash): void
    {
        try {
            // Store the old password in history
            JobSeekerPasswordHistory::create([
                'job_seeker_id' => $jobSeeker->id,
                'password_hash' => $oldPasswordHash,
                'changed_at' => now(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            // Clean up old password history entries beyond the limit
            $this->cleanupOldPasswordHistory($jobSeeker);

            Log::info('Password change recorded in history', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
                'ip_address' => request()->ip(),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to record password change in history', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get password history for a job seeker
     */
    public function getPasswordHistory(JobSeeker $jobSeeker, int $limit = 10): array
    {
        return JobSeekerPasswordHistory::where('job_seeker_id', $jobSeeker->id)
            ->orderBy('created_at', 'desc')
            ->take($limit)
            ->get()
            ->map(function ($history) {
                return [
                    'id' => $history->id,
                    'changed_at' => $history->changed_at,
                    'ip_address' => $history->ip_address,
                    'user_agent' => $history->user_agent,
                    'days_ago' => $history->changed_at->diffInDays(now()),
                ];
            })
            ->toArray();
    }

    /**
     * Get the time until next password change is allowed
     */
    public function getTimeUntilNextPasswordChange(JobSeeker $jobSeeker): ?int
    {
        if ($this->canChangePassword($jobSeeker)) {
            return null; // Can change now
        }

        $lastPasswordChange = JobSeekerPasswordHistory::where('job_seeker_id', $jobSeeker->id)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$lastPasswordChange) {
            return null;
        }

        $nextAllowedTime = $lastPasswordChange->created_at->addHours(self::MIN_PASSWORD_CHANGE_HOURS);
        
        return max(0, now()->diffInMinutes($nextAllowedTime, false));
    }

    /**
     * Clean up old password history entries
     */
    private function cleanupOldPasswordHistory(JobSeeker $jobSeeker): void
    {
        $entriesToKeep = JobSeekerPasswordHistory::where('job_seeker_id', $jobSeeker->id)
            ->orderBy('created_at', 'desc')
            ->take(self::PASSWORD_HISTORY_COUNT)
            ->pluck('id');

        $deletedCount = JobSeekerPasswordHistory::where('job_seeker_id', $jobSeeker->id)
            ->whereNotIn('id', $entriesToKeep)
            ->delete();

        if ($deletedCount > 0) {
            Log::info('Cleaned up old password history entries', [
                'job_seeker_id' => $jobSeeker->id,
                'deleted_count' => $deletedCount,
            ]);
        }
    }

    /**
     * Force record current password (for initial setup)
     */
    public function recordCurrentPassword(JobSeeker $jobSeeker): void
    {
        try {
            JobSeekerPasswordHistory::create([
                'job_seeker_id' => $jobSeeker->id,
                'password_hash' => $jobSeeker->password,
                'changed_at' => $jobSeeker->created_at ?? now(),
                'ip_address' => request()->ip() ?? '127.0.0.1',
                'user_agent' => request()->userAgent() ?? 'System',
            ]);

            Log::info('Current password recorded in history for initial setup', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to record current password in history', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if password meets history requirements
     */
    public function validatePasswordHistory(JobSeeker $jobSeeker, string $newPassword): array
    {
        $errors = [];

        // Check if password change timing is valid
        if (!$this->canChangePassword($jobSeeker)) {
            $minutesUntilNext = $this->getTimeUntilNextPasswordChange($jobSeeker);
            $errors[] = "You must wait {$minutesUntilNext} minutes before changing your password again.";
        }

        // Check if password was recently used
        if ($this->isPasswordRecentlyUsed($jobSeeker, $newPassword)) {
            $errors[] = 'You cannot reuse one of your recent passwords. Please choose a different password.';
        }

        return $errors;
    }

    /**
     * Get password history statistics
     */
    public function getPasswordHistoryStats(JobSeeker $jobSeeker): array
    {
        $historyCount = JobSeekerPasswordHistory::where('job_seeker_id', $jobSeeker->id)->count();
        
        $lastChange = JobSeekerPasswordHistory::where('job_seeker_id', $jobSeeker->id)
            ->orderBy('created_at', 'desc')
            ->first();

        return [
            'total_password_changes' => $historyCount,
            'last_password_change' => $lastChange?->changed_at,
            'days_since_last_change' => $lastChange ? $lastChange->changed_at->diffInDays(now()) : null,
            'can_change_now' => $this->canChangePassword($jobSeeker),
            'minutes_until_next_change' => $this->getTimeUntilNextPasswordChange($jobSeeker),
        ];
    }

    /**
     * Check if a password has been used recently.
     */
    public function isPasswordReused(JobSeeker $user, string $password): bool
    {
        $recentPasswords = DB::table('job_seeker_password_history')
            ->where('job_seeker_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(self::MAX_PASSWORD_HISTORY)
            ->pluck('password_hash');

        foreach ($recentPasswords as $hashedPassword) {
            if (Hash::check($password, $hashedPassword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Store a password in the history.
     */
    public function storePasswordHistory(JobSeeker $user, string $password): void
    {
        // Add new password to history
        DB::table('job_seeker_password_history')->insert([
            'job_seeker_id' => $user->id,
            'password_hash' => Hash::make($password),
            'created_at' => now(),
        ]);

        // Clean up old password history (keep only the last MAX_PASSWORD_HISTORY entries)
        $this->cleanupOldPasswords($user);
    }

    /**
     * Clean up old password history entries.
     */
    private function cleanupOldPasswords(JobSeeker $user): void
    {
        $keepIds = DB::table('job_seeker_password_history')
            ->where('job_seeker_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(self::MAX_PASSWORD_HISTORY)
            ->pluck('id');

        if ($keepIds->isNotEmpty()) {
            DB::table('job_seeker_password_history')
                ->where('job_seeker_id', $user->id)
                ->whereNotIn('id', $keepIds)
                ->delete();
        }
    }

    /**
     * Get password history count for a user.
     */
    public function getPasswordHistoryCount(JobSeeker $user): int
    {
        return DB::table('job_seeker_password_history')
            ->where('job_seeker_id', $user->id)
            ->count();
    }

    /**
     * Clear all password history for a user.
     */
    public function clearPasswordHistory(JobSeeker $user): void
    {
        DB::table('job_seeker_password_history')
            ->where('job_seeker_id', $user->id)
            ->delete();
    }
} 
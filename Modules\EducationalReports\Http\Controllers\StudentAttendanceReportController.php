<?php
namespace Modules\EducationalReports\Http\Controllers;


use App\Events\StudentPromotion;
use App\Events\StudentPromotionGroupDisable;
use App\StudentHefzReport;
use Mail;
use Modules\Chat\Entities\BlockUser;
use Twilio;
use App\User;
use DataTables;
use App\Classes;
use App\Route;
use App\Employee;
use App\Guardian;
use LaravelMsg91;
use App\Section;
use App\Student;
use App\UserLog;
use App\Vehicle;
use App\tableList;
use App\YearCheck;
use App\ExamType;
use App\RoomList;
use App\BaseSetup;
use App\MarkStore;
use App\sTemplate;
use App\FeesAssign;
use App\MarksGrade;
use App\SmsGateway;
use App\ApiBaseMethod;
use App\AcademicYear;
use App\ClassSection;
use App\EmailSetting;
use App\ExamSchedule;
use App\LeaveRequest;
use App\StudentGroup;
use App\AssignSubject;
use App\AssignVehicle;
use App\DormitoryList;
use App\LibraryMember;
use App\GeneralSettings;
use App\StudentCategory;
use App\StudentDocument;
use App\StudentTimeline;
use App\InfixModuleManager;
use App\StudentAttendance;
use Illuminate\Http\Request;
use App\FeesAssignDiscount;
use App\StudentBulkTemporary;
use Illuminate\Support\Carbon;
use App\Imports\StudentsImport;
use App\ClassOptionalSubject;
use App\OptionalSubjectAssign;
use App\Exports\AllStudentExport;
use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Facades\Excel;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;

class StudentAttendanceReportController extends Controller
{

    private $User;
    private $GeneralSettings;
    private $UserLog;
    private $InfixModuleManager;
    private $URL;

    public function __construct()
    {
//        $this->middleware('PM');
//        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
//
//        $this->User                 = json_encode(User::find(1));
//        $this->GeneralSettings    = json_encode(GeneralSettings::find(1));
//        $this->UserLog            = json_encode(UserLog::find(1));
//        $this->InfixModuleManager   = json_encode(InfixModuleManager::find(1));
//        $this->URL                  = url('/');
    }



    //student report search modified by jmrashed
    public function studentReportSearch(Request $request)
    {
        $request->validate([
            'class' => 'required'
        ]);
        try {
            $students = Student::query();

            $students->where('academic_id', getAcademicId())->where('active_status', 1);

            //if no class is selected
            if ($request->class != "") {
                $students->where('class_id', $request->class);
            }
            //if no section is selected
            if ($request->section != "") {
                $students->where('section_id', $request->section);
            }
            //if no student is category selected
            if ($request->type != "") {
                $students->where('student_category_id', $request->type);
            }

            //if no gender is selected
            if ($request->gender != "") {
                $students->where('gender_id', $request->gender);
            }
            $students = $students->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            $classes = Classes::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();
            $types = StudentCategory::where('school_id', Auth::user()->school_id)->get();
            $genders = SmBaseSetup::where('active_status', '=', '1')->where('base_group_id', '=', '1')->get();

            $class_id = $request->class;
            $type_id = $request->type;
            $gender_id = $request->gender;

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['students'] = $students->toArray();
                $data['classes'] = $classes->toArray();
                $data['types'] = $types->toArray();
                $data['genders'] = $genders->toArray();
                $data['class_id'] = $class_id;
                $data['type_id'] = $type_id;
                $data['gender_id'] = $gender_id;
                return ApiBaseMethod::sendResponse($data, null);
            }
            $class = Classes::find($request->class);
            return view('backEnd.studentInformation.student_report', compact('students', 'classes', 'types', 'genders', 'class_id', 'type_id', 'gender_id', 'class'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentAttendanceReport(Request $request)
    {

        try {

            if (teacherAccess()) {
                $teacher_info=Employee::where('user_id',Auth::user()->id)->first();
               $classes= SmAssignSubject::where('teacher_id',$teacher_info->id)->join('sm_classes','sm_classes.id','sm_assign_subjects.class_id')
               ->where('sm_assign_subjects.academic_id', getAcademicId())
               ->where('sm_assign_subjects.active_status', 1)
               ->where('sm_assign_subjects.school_id',Auth::user()->school_id)
               ->select('sm_classes.id','class_name')
                ->groupBy('sm_classes.id')
               ->get();
            } else {
                $classes = Classes::all();
            }
            $genders = BaseSetup::where('active_status', '=', '1')->where('base_group_id', '=', '1')->get();


            return view('educationalreports::studentAttendance.student_attendance_report', compact('classes', 'genders'));
        } catch (\Exception $e) {
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentAttendanceReportSearch(Request $request)
    {

        $input = $request->all();
        $validator = Validator::make($input, [
            'class' => 'required',
            'month' => 'required',
            'year' => 'required'
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $year = $request->year;
            $month = $request->month;
            $class_id = $request->class;
            $current_day = date('d');
            $class = Classes::findOrFail($request->class);
            $days = cal_days_in_month(CAL_GREGORIAN, $request->month, $request->year);
            if (teacherAccess()) {
                $teacher_info=Employee::where('user_id',Auth::user()->id)->first();
               $classes= SmAssignSubject::where('teacher_id',$teacher_info->id)->join('sm_classes','sm_classes.id','sm_assign_subjects.class_id')
               ->where('sm_assign_subjects.academic_id', getAcademicId())
               ->where('sm_assign_subjects.active_status', 1)
               ->where('sm_assign_subjects.school_id',Auth::user()->school_id)
               ->select('sm_classes.id','class_name')
                ->groupBy('sm_classes.id')
               ->get();
            } else {
                $classes = Classes::all();
            }





//            dd(StudentHefzReport::where('class_id','=', $request->class)
//                ->whereYear('class_time','=', $request->year)
//                ->whereMonth('class_time','=',sprintf("%02d", $request->month))->get());

            $students = Student::whereHas('reports', function ($q) use ($request) {
                $q->where('class_id','=', $request->class)
                    ->whereYear('class_time','=',$request->year)
                    ->whereMonth('class_time','=',sprintf("%02d", $request->month) );
            })->with(['reports' => function($query) use ($request) {

                $query->where('class_id','=', $request->class)
                    ->whereYear('class_time','=', $request->year)
                    ->whereMonth('class_time','=',sprintf("%02d", $request->month));

            }]);





            $attendances = [];

//            foreach ($students as $key => $student) {
//
//
//
//                $attendance = StudentHefzReport::where('student_id', $student->id)->whereYear('class_time', $request->year)->whereMonth('class_time',$request->month)->get();
////                if (count($attendance) != 0) {
//                    $attendances[] = $attendance;
//
////                }
//            }





//            return view('reports::studentAttendance.student_attendance_report', compact('classes','attendances','students', 'days', 'year', 'month', 'current_day',
//                'class_id', 'class'));
            return view('educationalreports::studentAttendance.student_attendance_report', compact('classes','students', 'days', 'year', 'month', 'current_day',
                'class_id', 'class'));
        } catch (\Exception $e) {
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function studentAttendanceReportPrint($class_id, $month, $year)
    {

        set_time_limit(2700);
        try {
            $current_day = date('d');
            $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);


            $attendances = Student::whereHas('reports', function ($q) use ($class_id,$year,$month) {
                $q->where('class_id','=', $class_id)
                    ->whereYear('class_time','=',$year)
                    ->whereMonth('class_time','=',sprintf("%02d", $month) );
            })->with(['reports' => function($query) use ($class_id,$year,$month) {
                $query->where('class_id','=', $class_id)
                    ->whereYear('class_time','=', $year)
                    ->whereMonth('class_time','=',sprintf("%02d", $month));
            }])->get();


//            $attendances = [];
//            foreach ($students as $student) {
//                $attendance = StudentHefzReport::where('student_id', $student->id)
//                ->where('class_time', 'like', $year . '-' . $month . '%')
//                ->get();
//
//                if ($attendance) {
//                    $attendances[] = $attendance;
//                }
//            }

            // $pdf = PDF::loadView(
            //     'backEnd.studentInformation.student_attendance_print',
            //     [
            //         'attendances' => $attendances,
            //         'days' => $days,
            //         'year' => $year,
            //         'month' => $month,
            //         'class_id' => $class_id,
            //         'section_id' => $section_id,
            //         'class' => Classes::find($class_id),
            //         'section' => SmSection::find($section_id),
            //     ]
            // )->setPaper('A4', 'landscape');
            // return $pdf->stream('student_attendance.pdf');

            $class = Classes::find($class_id);
            return view('educationalreports::studentAttendance.student_attendance_print', compact('class', 'attendances', 'days', 'year', 'month', 'current_day', 'class_id'));
        } catch (\Exception $e) {
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function importStudent()
    {
        try {
            // start check student limitation for subscription
            if(moduleStatusCheck('SaasSubscription')== TRUE){

                $active_student = Student::where('school_id', Auth::user()->school_id)->where('active_status', 1)->count();

                if(\Modules\SaasSubscription\Entities\SmPackagePlan::student_limit() <= $active_student){

                    Toastr::error('Your student limit has been crossed.', 'Failed');
                    return redirect()->back();
                }
            }
            // End check student limitation for subscription


            $classes = Classes::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();
            $genders = SmBaseSetup::where('base_group_id', 1)->get();
            $blood_groups = SmBaseSetup::where('base_group_id', 3)->get();
            $religions = SmBaseSetup::where('base_group_id', 2)->get();
            $sessions = SmAcademicYear::where('school_id', Auth::user()->school_id)->get();
            return view('backEnd.studentInformation.import_student', compact('classes', 'genders', 'blood_groups', 'religions', 'sessions'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function downloadStudentFile()
    {
        try {
            $studentsArray = ['admission_number', 'roll_no', 'first_name', 'last_name', 'date_of_birth', 'religion', 'gender', 'caste', 'mobile', 'email', 'admission_date', 'blood_group', 'height', 'weight', 'father_name', 'father_phone', 'father_occupation', 'mother_name', 'mother_phone', 'mother_occupation', 'guardian_name', 'guardian_relation', 'guardian_email', 'guardian_phone', 'guardian_occupation', 'guardian_address', 'current_address', 'permanent_address', 'bank_account_no', 'bank_name', 'national_identification_no', 'local_identification_no', 'previous_school_details', 'note'];

            return Excel::create('students', function ($excel) use ($studentsArray) {
                $excel->sheet('students', function ($sheet) use ($studentsArray) {
                    $sheet->fromArray($studentsArray);
                });
            })->download('xlsx');
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function studentBulkStore(Request $request)
    {
        
        $request->validate(
            [
                'session' => 'required',
                'class' => 'required',
                'section' => 'required',
                'file' => 'required'
            ],
            [
                'session.required' => 'Academic year field is required.'
            ]
        );



        $file_type = strtolower($request->file->getClientOriginalExtension());
        if ($file_type <> 'csv' && $file_type <> 'xlsx' && $file_type <> 'xls') {
            Toastr::warning('The file must be a file of type: xlsx, csv or xls', 'Warning');
            return redirect()->back();
        } else {
            try {
                DB::beginTransaction();
                $path = $request->file('file');
                Excel::import(new StudentsImport, $request->file('file'), 's3', \Maatwebsite\Excel\Excel::XLSX);
                $data = StudentBulkTemporary::where('user_id', Auth::user()->id)->get();

                /*   $usersUnique = $data->unique('admission_number');
                $usersDupes = $data->diff($usersUnique);
                if (sizeof($usersDupes) > sizeof($data)) {
                    return redirect()->back()->with("message-danger","Admission number required");
                 }
                if (sizeof($usersDupes) >= 1) {
                   return redirect()->back()->with("message-danger","Admission number should be unique");
                } */


                $shcool_details = GeneralSettings::find(1);
                $school_name = explode(' ', $shcool_details->school_name);
                $short_form = '';
                foreach ($school_name as $value) {
                    $ch = str_split($value);
                    $short_form = $short_form . '' . $ch[0];
                }

                if (!empty($data)) {
                    foreach ($data as $key => $value) {
                        if(moduleStatusCheck('SaasSubscription')== TRUE){

                            $active_student = Student::where('school_id', Auth::user()->school_id)->where('active_status', 1)->count();

                            if(\Modules\SaasSubscription\Entities\SmPackagePlan::student_limit() <= $active_student){

                                DB::commit();
                                StudentBulkTemporary::where('user_id', Auth::user()->id)->delete();
                                Toastr::error('Your student limit has been crossed.', 'Failed');
                                return redirect('student-list');

                            }
                        }


                        $ad_check = Student::where('admission_no', (int) $value->admission_number)->where('school_id', Auth::user()->school_id)->get();
                        //  return $ad_check;

                        if ($ad_check->count() > 0) {
                            DB::rollback();
                            StudentBulkTemporary::where('user_id', Auth::user()->id)->delete();
                            Toastr::error('Admission number should be unique.', 'Failed');
                            return redirect()->back();
                        }

                        if ($value->email != "") {
                            $chk =  DB::table('sm_students')->where('email', $value->email)->where('school_id', Auth::user()->school_id)->count();
                            if ($chk >= 1) {
                                DB::rollback();
                                StudentBulkTemporary::where('user_id', Auth::user()->id)->delete();
                                Toastr::error('Student Email address should be unique.', 'Failed');
                                return redirect()->back();
                            }
                        }

                        if ($value->guardian_email != "") {
                            $chk =  DB::table('sm_parents')->where('guardians_email', $value->guardian_email)->where('school_id', Auth::user()->school_id)->count();
                            if ($chk >= 1) {
                                DB::rollback();
                                StudentBulkTemporary::where('user_id', Auth::user()->id)->delete();
                                Toastr::error('Guardian Email address should be unique.', 'Failed');
                                return redirect()->back();
                            }
                        }


                        try {
                            
                            if($value->admission_number==null){
                                continue;
                            }else{
                                
                            }
                            $academic_year = SmAcademicYear::find($request->session);


                            $user_stu = new User();
                            $user_stu->role_id = 2;
                            $user_stu->full_name = $value->first_name . ' ' . $value->last_name;

                            if (empty($value->email)) {
                                $user_stu->username = $value->admission_number;
                            }else{
                                $user_stu->username = $value->email;
                            }

                            $user_stu->email = $value->email;

                            $user_stu->school_id = Auth::user()->school_id;

                            $user_stu->password = Hash::make(123456);

                            $user_stu->created_at = $academic_year->year . '-01-01 12:00:00';

                            $user_stu->save();

                            $user_stu->toArray();

                            try {

                                $user_parent = new User();
                                $user_parent->role_id = 3;
                                $user_parent->full_name = $value->father_name;

                                if (empty($value->guardian_email)) {
                                    $data_parent['email'] = 'par_' . $value->admission_number;

                                    $user_parent->username  = 'par_' . $value->admission_number;
                                } else {

                                    $data_parent['email'] = $value->guardian_email;

                                    $user_parent->username = $value->guardian_email;
                                }

                                $user_parent->email = $value->guardian_email;

                                $user_parent->password = Hash::make(123456);
                                $user_parent->school_id = Auth::user()->school_id;

                                $user_parent->created_at = $academic_year->year . '-01-01 12:00:00';

                                $user_parent->save();
                                $user_parent->toArray();

                                try {

                                    $parent = new Guardian();

                                    if (
                                        $value->relation == 'F' ||
                                        $value->guardians_relation == 'F' ||
                                        $value->guardian_relation == 'F' ||
                                        strtolower($value->guardian_relation) == 'father' ||
                                        strtolower($value->guardians_relation) == 'father'
                                    ) {
                                        $relationFull = 'Father';
                                        $relation = 'F';
                                    } elseif (
                                        $value->relation == 'M' ||
                                        $value->guardians_relation == 'M' ||
                                        $value->guardian_relation == 'M' ||
                                        strtolower($value->guardian_relation) == 'mother' ||
                                        strtolower($value->guardians_relation) == 'mother'
                                    ) {
                                        $relationFull = 'Mother';
                                        $relation = 'M';
                                    } else {
                                        $relationFull = 'Other';
                                        $relation = 'O';
                                    }
                                    $parent->guardians_relation = $relationFull;
                                    $parent->relation = $relation;

                                    $parent->user_id = $user_parent->id;
                                    $parent->fathers_name = $value->father_name;
                                    $parent->fathers_mobile = $value->father_phone;
                                    $parent->fathers_occupation = $value->fathe_occupation;
                                    $parent->mothers_name = $value->mother_name;
                                    $parent->mothers_mobile = $value->mother_phone;
                                    $parent->mothers_occupation = $value->mother_occupation;
                                    $parent->guardians_name = $value->guardian_name;
                                    $parent->guardians_mobile = $value->guardian_phone;
                                    $parent->guardians_occupation = $value->guardian_occupation;
                                    $parent->guardians_address = $value->guardian_address;
                                    $parent->guardians_email = $value->guardian_email;
                                    $parent->school_id = Auth::user()->school_id;
                                    $parent->academic_id = $request->session;

                                    $parent->created_at = $academic_year->year . '-01-01 12:00:00';

                                    $parent->save();
                                    $parent->toArray();

                                    try {
                                        $student = new Student();
                                        // $student->siblings_id = $value->sibling_id;
                                        $student->class_id = $request->class;
                                        $student->section_id = $request->section;
                                        $student->session_id = $request->session;
                                        $student->user_id = $user_stu->id;

                                        $student->parent_id = $parent->id;
                                        $student->role_id = 2;

                                        $student->admission_no = $value->admission_number;
                                        $student->roll_no = $value->roll_no;
                                        $student->first_name = $value->first_name;
                                        $student->last_name = $value->last_name;
                                        $student->full_name = $value->first_name . ' ' . $value->last_name;
                                        $student->gender_id = $value->gender;
                                        $student->date_of_birth = date('Y-m-d', strtotime($value->date_of_birth));
                                        $student->caste = $value->caste;
                                        $student->email = $value->email;
                                        $student->mobile = $value->mobile;
                                        $student->admission_date = date('Y-m-d', strtotime($value->admission_date));
                                        $student->bloodgroup_id = $value->blood_group;
                                        $student->religion_id = $value->religion;
                                        $student->height = $value->height;
                                        $student->weight = $value->weight;
                                        $student->current_address = $value->current_address;
                                        $student->permanent_address = $value->permanent_address;
                                        $student->national_id_no = $value->national_identification_no;
                                        $student->local_id_no = $value->local_identification_no;
                                        $student->bank_account_no = $value->bank_account_no;
                                        $student->bank_name = $value->bank_name;
                                        $student->previous_school_details = $value->previous_school_details;
                                        $student->aditional_notes = $value->note;
                                        $student->school_id = Auth::user()->school_id;
                                        $student->academic_id = $request->session;

                                        $student->created_at = $academic_year->year . '-01-01 12:00:00';

                                        $student->save();

                                        $user_info = [];

                                        if ($value->email != "") {
                                            $user_info[] =  array('email' => $value->email, 'username' => $value->email);
                                        }


                                        if ($value->guardian_email != "") {
                                            $user_info[] =  array('email' =>  $value->guardian_email, 'username' => $data_parent['email']);
                                        }
                                    } catch (\Illuminate\Database\QueryException $e) {

                                        DB::rollback();
                                        Toastr::error('Operation Failed', 'Failed');
                                        return redirect()->back();
                                    } catch (\Exception $e) {

                                        DB::rollback();
                                        Toastr::error('Operation Failed', 'Failed');
                                        return redirect()->back();
                                    }
                                } catch (\Exception $e) {
                                    DB::rollback();
                                    Toastr::error('Operation Failed', 'Failed');
                                    return redirect()->back();
                                }
                            } catch (\Exception $e) {
                                DB::rollback();
                                Toastr::error('Operation Failed', 'Failed');
                                return redirect()->back();
                            }
                        } catch (\Exception $e) {
                            DB::rollback();
                            Toastr::error('Operation Failed', 'Failed');
                            return redirect()->back();
                        }
                    }

                    StudentBulkTemporary::where('user_id', Auth::user()->id)->delete();

                    DB::commit();
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                }
            } catch (\Exception $e) {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        }
    }


    public function guardianReport(Request $request)
    {
        try {
            $students = Student::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();
            $classes = Classes::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['students'] = $students->toArray();
                $data['classes'] = $classes->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('backEnd.studentInformation.guardian_report', compact('classes'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function guardianReportSearch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'class' => 'required'
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $students = Student::query();
            $students->where('academic_id', getAcademicId())->where('active_status', 1);
            $students->where('class_id', $request->class);
            if ($request->section != "") {
                $students->where('section_id', $request->section);
            }
            $students = $students->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            $classes = Classes::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();


            $class_id = $request->class;

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['students'] = $students->toArray();
                $data['classes'] = $classes->toArray();
                $data['class_id'] = $class_id;
                return ApiBaseMethod::sendResponse($data, null);
            }
            $class = Classes::find($request->class);
            return view('backEnd.studentInformation.guardian_report', compact('students', 'classes', 'class_id', 'class'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentLoginReport(Request $request)
    {
        try {
            $students = Student::where('school_id', Auth::user()->school_id)->get();
            $classes = Classes::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['students'] = $students->toArray();
                $data['classes'] = $classes->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('backEnd.studentInformation.login_info', compact('classes'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function studentLoginSearch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'class' => 'required'
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $students = Student::query();
            $students->where('academic_id', getAcademicId())->where('active_status', 1);
            $students->where('class_id', $request->class);
            if ($request->section != "") {
                $students->where('section_id', $request->section);
            }
            $students = $students->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            $classes = Classes::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();
            $class_id = $request->class;

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['students'] = $students->toArray();
                $data['classes'] = $classes->toArray();
                $data['class_id'] = $class_id;
                return ApiBaseMethod::sendResponse($data, null);
            }
            $class = Classes::find($request->class);
            return view('backEnd.studentInformation.login_info', compact('students', 'classes', 'class_id', 'class'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function disabledStudent(Request $request)
    {
        try {
            $students = Student::where('active_status', 0)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            $classes = Classes::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['students'] = $students->toArray();
                $data['classes'] = $classes->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('backEnd.studentInformation.disabled_student', compact('students', 'classes'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function disabledStudentSearch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'class' => 'required'
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $students = Student::query();
            $students->where('academic_id', getAcademicId())->where('active_status', 0);
            if ($request->class != "") {
                $students->where('class_id', $request->class);
            }
            if ($request->section != "") {
                $students->where('section_id', $request->section);
            }
            if ($request->name != "") {
                $students->where('full_name', 'like', '%' . $request->name . '%');
            }
            if ($request->roll_no != "") {
                $students->where('roll_no', 'like', '%' . $request->roll_no . '%');
            }
            $students = $students->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            $classes = Classes::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            $class_id = $request->class;
            $section_id = $request->section;
            $name = $request->name;
            $roll_no = $request->roll_no;


            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['students'] = $students->toArray();
                $data['classes'] = $classes->toArray();
                $data['class_id'] = $class_id;
                $data['section_id'] = $section_id;
                $data['name'] = $name;
                $data['roll_no'] = $roll_no;
                return ApiBaseMethod::sendResponse($data, null);
            }

            return view('backEnd.studentInformation.disabled_student', compact('students', 'classes', 'class_id', 'section_id', 'name', 'roll_no'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }



    public function disabledStudentDelete1(Request $request)
    {
        try {

            $student_detail = Student::find($request->id);
            $parent_user = @$student_detail->parents->user_id;


            $siblings = Student::where('parent_id', $student_detail->parent_id)->where('school_id', Auth::user()->school_id)->get();


            DB::beginTransaction();


            if ($student_detail->student_photo != "") {
                if (file_exists($student_detail->student_photo)) {
                    unlink($student_detail->student_photo);
                }
            }

            Student::destroy($request->id);


            if (count($siblings) == 1) {
                $parent = Guardian::find($student_detail->parent_id);

                if ($parent->fathers_photo != "") {
                    if (file_exists($parent->fathers_photo)) {
                        unlink($parent->fathers_photo);
                    }
                }
                if ($parent->mothers_photo != "") {
                    if (file_exists($parent->mothers_photo)) {
                        unlink($parent->mothers_photo);
                    }
                }
                if ($parent->guardians_photo != "") {
                    if (file_exists($parent->guardians_photo)) {
                        unlink($parent->guardians_photo);
                    }
                }

                $parent->delete();
            }



            $student_user = User::find($student_detail->user_id);
            $student_user->delete();

            if (count($siblings) == 1) {

                $parent_user = User::find($parent_user);
                $parent_user->delete();
            }

            DB::commit();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendResponse(null, 'Student has been disabled successfully');
            }

            Toastr::success('Operation successful', 'Success');
            return redirect()->back();
        } catch (\Exception $e) {
            DB::rollback();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Something went wrong, please try again');
            }
            
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function disabledStudentDelete(Request $request)
    {
        try {
            $tables = \App\tableList::getTableList('student_id', $request->id);
            try {
                $single_data = 0;

                 if (checkAdmin()) {
                    $student_detail = Student::find($request->id);
                }else{
                    $student_detail = Student::where('id',$request->id)->first();
                }
                $parent_user = @$student_detail->parents->user_id;
                $siblings = Student::where('parent_id', $student_detail->parent_id)->where('school_id', Auth::user()->school_id)->get();
                DB::beginTransaction();
                if ($student_detail->student_photo != "") {
                    if (file_exists($student_detail->student_photo)) {
                        unlink($student_detail->student_photo);
                    }
                }

                Student::destroy($request->id);


                if (count($siblings) == 1) {
                    $parent = Guardian::find($student_detail->parent_id);

                    if ($parent->fathers_photo != "") {
                        if (file_exists($parent->fathers_photo)) {
                            unlink($parent->fathers_photo);
                        }
                    }
                    if ($parent->mothers_photo != "") {
                        if (file_exists($parent->mothers_photo)) {
                            unlink($parent->mothers_photo);
                        }
                    }
                    if ($parent->guardians_photo != "") {
                        if (file_exists($parent->guardians_photo)) {
                            unlink($parent->guardians_photo);
                        }
                    }

                    $parent->delete();
                }
                $student_user = User::find($student_detail->user_id);
                $student_user->delete();

                if (count($siblings) == 1) {

                    $parent_user = User::find($parent_user);
                    $parent_user->delete();
                }
                $table_list=\App\tableList::ONLY_TABLE_LIST('student_id');
                foreach ($table_list as $key => $table) {
                    $table_data=DB::table($table)->where('student_id',$request->id)->get();
                    foreach ($table_data as $key => $data) {
                            $single_data==DB::table($table)->where('id',$data->id)->delete();
                    }
                }

                foreach ($table_list as $key => $table) {
                $table_data=DB::table($table)->where('student_id',$request->id)->get();
                    foreach ($table_data as $key => $data) {
                    $single_data==DB::table($table)->where('id',$data->id)->delete();
                    }
                }

                DB::commit();

                if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                    return ApiBaseMethod::sendResponse(null, 'Student has been disabled successfully');
                }

                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
            } catch (\Illuminate\Database\QueryException $e) {
                DB::rollback();
                $msg = 'This data already used in  : ' . $tables . ' Please remove those data first';
                Toastr::error($msg, 'Failed');
                return redirect()->back();
            } catch (\Exception $e) {
                DB::rollback();
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            DB::rollback();
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function enableStudent(Request $request)
    {
        try {
            if(moduleStatusCheck('SaasSubscription')== TRUE){
                $active_student = Student::where('school_id', Auth::user()->school_id)->where('active_status', 1)->count();
                if(\Modules\SaasSubscription\Entities\SmPackagePlan::student_limit() <= $active_student){
                    Toastr::error('Your student limit has been crossed.', 'Failed');
                    return redirect()->back();
                }
            }

            DB::beginTransaction();
            // $student_detail = Student::find($request->id);
             if (checkAdmin()) {
                $student_detail = Student::find($request->id);
            }else{
                $student_detail = Student::where('id',$request->id)->first();
            }

            $student_detail->active_status = 1;
            // $student_detail->save();


            $parent = Guardian::find($student_detail->parent_id);
            $parent->active_status = 1;
            $parent->save();

            $student_user = User::find($student_detail->user_id);
            $student_user->active_status = 1;
            $student_user->save();

            $parent_user = User::find(@$student_detail->parents->user_id);
            $parent_user->active_status = 1;
            $parent_user->save();

            $student_detail->save();

            DB::commit();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendResponse(null, 'Student has been enabled successfully');
            }

            Toastr::success('Operation successful', 'Success');
            return redirect()->back();
        } catch (\Exception $e) {
            DB::rollback();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Something went wrong, please try again');
            }
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function studentHistory(Request $request)
    {
        try {
            $classes = Classes::where('active_status', 1)
                    ->where('academic_id', getAcademicId())
                    ->where('school_id', Auth::user()->school_id)
                    ->get();

            $students = Student::where('active_status', 1)
                    ->where('academic_id', getAcademicId())
                    ->where('school_id', Auth::user()->school_id)
                    ->get();

            $years = Student::select('admission_date')->where('active_status', 1)
                ->where('academic_id', getAcademicId())->get()
                ->groupBy(function ($val) {
                return Carbon::parse($val->admission_date)->format('Y');
                });
                
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['classes'] = $classes->toArray();
                $data['students'] = $students->toArray();
                $data['years'] = $years->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }

            return view('backEnd.studentInformation.student_history', compact('classes', 'years'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function studentHistorySearch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'class' => 'required'
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $classes = Classes::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();
            $students = Student::query();
            $students->where('academic_id', getAcademicId())->where('active_status', 1);
            $students->where('class_id', $request->class);
            $students->where('active_status', 1);
            if ($request->admission_year != "") {
                $students->where('admission_date', 'like',  $request->admission_year . '%');
            }

            $students = $students->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            $years = Student::select('admission_date')->where('active_status', 1)
                ->where('academic_id', getAcademicId())->get()
                ->groupBy(function ($val) {
                    return Carbon::parse($val->admission_date)->format('Y');
                });

            $class_id = $request->class;
            $year = $request->admission_year;

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['students'] = $students->toArray();
                $data['classes'] = $classes->toArray();
                $data['years'] = $years->toArray();
                $data['class_id'] = $class_id;
                $data['year'] = $year;
                return ApiBaseMethod::sendResponse($data, null);
            }
            $class = Classes::find($request->class);
            return view('backEnd.studentInformation.student_history', compact('students', 'classes', 'years', 'class_id', 'year', 'class'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function view_academic_performance(Request $request, $id)
    {
        if (ApiBaseMethod::checkUrl($request->fullUrl())) {
            return ApiBaseMethod::sendResponse($id, null);
        }
        return $id;
    }

    function previousRecord()
    {
        try {
            $academic_years = SmAcademicYear::where('school_id', Auth::user()->school_id)->get();
            $exam_types = SmExamType::where('school_id', Auth::user()->school_id)->get();

            $classes = Classes::where('active_status', 1)
                    ->where('academic_id', getAcademicId())
                    ->where('school_id', Auth::user()->school_id)
                    ->get();
            // return $classes;
            // return getAcademicId();
            return view('backEnd.examination.previous_record', compact('classes', 'exam_types', 'academic_years'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    function previousRecordSearch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'promote_session' => 'required',
            'promote_class' => 'required',
            'promote_section' => 'required'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $yearCh = SmAcademicYear::find($request->promote_session);
            $students = StudentPromotion::where('created_at', 'LIKE', '%' . $yearCh->year . '%');
            if ($request->promote_class != "") {
                $students->where('previous_class_id', $request->promote_class);
            }
            if ($request->promote_section != "") {
                $students->where('previous_section_id', $request->promote_section);
            }
            $year = $request->promote_session;
            $students = $students->where('school_id', Auth::user()->school_id)->get();

            $academic_years = SmAcademicYear::where('school_id', Auth::user()->school_id)->get();
            $exam_types = SmExamType::where('school_id', Auth::user()->school_id)->get();
            $classes = Classes::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();
            $class = Classes::find($request->promote_class);
            $sec = SmSection::find($request->promote_section);
            return view('backEnd.examination.previous_record', compact('classes', 'exam_types', 'academic_years', 'students', 'year', 'class', 'sec'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function allStudentExport()
    {
        try{
            return view('backEnd.studentInformation.allStudentExport');
        }catch(\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function allStudentExportExcel()
    {
        try{
            return Excel::download(new AllStudentExport, 'all_student_export.csv');
        }catch(\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function allStudentExportPdf()
    {
        try{
            $students = Student::where('school_id',Auth::user()->school_id)
                    ->where('academic_id',getAcademicId())
                    ->orderBy('class_id','asc')
                    ->get();

        return view('backEnd.studentInformation.allStudentExportPdfPrint', compact('students'));

        // $pdf = PDF::loadView('backEnd.studentInformation.allStudentExportPdfPrint',
        //         ['students' => $students])->setPaper('A4', 'landscape');
        //     return $pdf->stream('all_student.pdf');
        }catch(\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}
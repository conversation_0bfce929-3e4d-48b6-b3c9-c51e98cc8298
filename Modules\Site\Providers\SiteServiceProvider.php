<?php

namespace Modules\Site\Providers;

use He<PERSON>\MultiAuth\MultiAuthServiceProvider;
use Illuminate\Support\ServiceProvider;
use App\Menu;
use App\Slider;
use View;
use App;

class SiteServiceProvider extends ServiceProvider
{
    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = false;

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        require(base_path('Modules/Site/Http/helpers.php'));

        $theme = config('website_theme');


        view()->composer('*templates.*.header', function ($view) {
            $menu_elements = [];
            
            // Get the current locale from the session or app config
            $currentLocale = session('locale', config('app.locale'));
            
            $menu = Menu::translatedIn($currentLocale)->orderBy('order')->get();
            /*
            WORK ROUND FIX - UNKNOWN TRANSLATION ERROR ON LIVE SERVER
            */
            $menu_items = [];
            foreach ($menu as $key => $value) {
                $menu_items[$key] = $value->toArray();
                $menu_items[$key]['title'] = $value->translate($currentLocale)->title;
            }

            foreach ($menu_items as $key => $element) {
                // $element->title = $element->translate($currentLocale)->title;
                if ($element['parent'] == 0) {
                    if (isset($menu_elements[$element['id']])) {
                        $children = $menu_elements[$element['id']]['children'];
                    }
                    $menu_elements[$element['id']] = $element;
                    if (isset($children)) {
                        $menu_elements[$element['id']]['children'] = $children;
                    }
                } else {
                    $menu_elements[$element['parent']]['children'][] = $element;
                }
            }

            // dd($menu_elements , collect($menu_elements));



            $view->with('menu_elements', $menu_elements);
        });

        view()->composer('*templates.*.components.sliders.*', function ($view) {
            // $slider = [];
            
            $slider = Slider::orderBy('slide_order')->translatedIn(config('app.locale'))->get();

            $view->with('slider', $slider);
        });

        view()->composer('*templates*', function ($view) {
            $view->with('theme', config('website_theme', 'default'));
        });



        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
//        $this->app->register(App\Providers\AuthServiceProvider::class);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            __DIR__.'/../Config/config.php' => config_path('site.php'),
        ], 'config');
        $this->mergeConfigFrom(
            __DIR__.'/../Config/config.php',
            'site'
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = base_path('resources/views/modules/site');

        $sourcePath = __DIR__.'/../Resources/views';

        $this->publishes([
            $sourcePath => $viewPath
        ]);

        $this->loadViewsFrom(array_merge(array_map(function ($path) {
            return $path . '/modules/site';
        }, \Config::get('view.paths')), [$sourcePath]), 'site');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = base_path('resources/lang/modules/site');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'site');
        } else {
            $this->loadTranslationsFrom(__DIR__ .'/../Resources/lang', 'site');
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}

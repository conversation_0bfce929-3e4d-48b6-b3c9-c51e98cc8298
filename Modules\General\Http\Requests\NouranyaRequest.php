<?php

namespace Modules\General\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NouranyaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Or use your own authorization logic
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {




        return [
            // Settings
            'frequency'    => 'nullable|in:daily,weekly,monthly',
            'daily_time'   => 'nullable|date_format:H:i',
            'weekly_day'   => 'nullable|in:Mon,Tue,Wed,Thu,Fri,Sat,Sun',
            'monthly_day'  => 'nullable|integer|min:1|max:31',

            // Adding new exclusions (multiple emails)
            'new_exclusions'      => 'nullable|array',
            'new_exclusions.*'    => 'email',

            // Removing exclusions (IDs of existing records)
            'remove_exclusions'   => 'nullable|array',
            'remove_exclusions.*' => 'integer',

            // Resend logs
            'resend_logs'         => 'nullable|array',
            'resend_logs.*'       => 'integer',
        ];
    }

    protected function prepareForValidation()
    {
        if ($this->filled('daily_time')) {
            $dailyTime = date('H:i', strtotime($this->input('daily_time')));
            $this->merge([
                'daily_time' => $dailyTime,
            ]);
        }
    }


    public function messages()
    {
        return [
            'frequency.in'              => 'Frequency must be daily, weekly, or monthly.',
            'daily_time.date_format'    => 'Daily time must be in 24-hour format (HH:MM).',
            'weekly_day.in'            => 'Weekly day must be one of Mon, Tue, Wed, Thu, Fri, Sat, Sun.',
            'monthly_day.min'          => 'Monthly day must be at least 1.',
            'monthly_day.max'          => 'Monthly day cannot exceed 31.',
            'new_exclusions.*.email'   => 'Each exclusion must be a valid email address.',
            'remove_exclusions.*.integer' => 'Invalid exclusion ID.',
            'resend_logs.*.integer'    => 'Invalid log ID.',
        ];
    }
}

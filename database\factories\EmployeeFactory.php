<?php

namespace Database\Factories;

use App\Employee;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class EmployeeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Employee::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'full_name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'password' => bcrypt('password'), // default password
            'organization_id' => 1, // Assuming default org ID
            'status' => 'active',
            // Add other essential fields based on your Employee model constraints
            'employee_number' => $this->faker->unique()->randomNumber(),
            'gender' => $this->faker->randomElement(['Male', 'Female']),
            'mobile' => $this->faker->phoneNumber(),
            'date_of_birth' => $this->faker->date(),
        ];
    }
} 
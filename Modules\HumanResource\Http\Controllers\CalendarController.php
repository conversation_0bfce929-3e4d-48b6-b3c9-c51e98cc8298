<?php

namespace Modules\HumanResource\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Event;
use Carbon\Carbon;
use App\EventMember;
use Illuminate\Support\Facades\Session;

class CalendarController extends Controller
{
    private $member_types;
    private $event_types;

    public function __construct()
    {
        $this->member_types = ['all' => 'Everyone', 'center' => 'Center', 'class' => 'Class', 'employee' => 'Employee'];

        // need validate allowed types
        $event_types = ['vacation', 'activity', 'meeting'];

        foreach($event_types as $type){
            $this->event_types[$type] = trans('events.'.$type); 
        }
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $keyword = $request->get('search');
        $perPage = 25;

        if (!empty($keyword)) {
            $events = Event::where('organization_id', 'LIKE', "%$keyword%")
                ->orWhere('event_time', 'LIKE', "%$keyword%")
                ->orWhere('duration', 'LIKE', "%$keyword%")
                ->orWhere('title', 'LIKE', "%$keyword%")
                ->orWhere('event_type', 'LIKE', "%$keyword%")
                ->orWhere('created', 'LIKE', "%$keyword%")
                ->paginate($perPage);
        } else {
            $events = Event::paginate($perPage);
        }

        return view('humanresource::events.index', compact('events'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $member_types =  $this->member_types;
        $event_types =  $this->event_types;
        
        return view('humanresource::events.create' , compact('member_types' , 'event_types'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
       
        $requestData = $request->all();
        
        if($request->one_day == 'one_day'){
            $requestData['duration'] = 'one_day';
        }else{
            $this->validate($request, [
            'event_end' => 'required|date|after:event_time'
            ]);
            $requestData['duration'] = $request->event_end;            
        }
        $requestData['organization_id'] = config('organization_id');
        $requestData['created_by'] = auth()->user()->id;

        $event = Event::create($requestData);

        if($request->member_type == 'all'){

            EventMember::create([
                'event_id' => $event->id,
                'member_type' => 'all'
            ]);

        }else{
           
            foreach ($request->members as $key => $value) {
                EventMember::create([
                    'event_id' => $event->id,
                    'member_type' => $request->member_type,
                    'member_id' => $value
                ]);
            }

        }
            
        // Session::flash('flash_message', 'Event added!');

        return redirect('workplace/humanresource/calendar');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $event = Event::findOrFail($id);

        $member_types = $this->member_types;
        $event_types = $this->event_types;
        
        
        return view('humanresource::events.show', compact('event' , 'member_types' , 'event_types'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $event = Event::findOrFail($id);

        $member_types = $this->member_types;
        $event_types = $this->event_types;



        $event_members = [];

        if($event->members_type != 'all'){
            foreach ($event->members as $member ) {
                $event_members[] = (int) $member->member_id;
            }
        }
    
        return view('humanresource::events.edit', compact('event' , 'event_members' , 'member_types' , 'event_types'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {
        $event = Event::findOrFail($id);

        $requestData = $request->all();

        if ($request->one_day == 'one_day') {
            $requestData['duration'] = 'one_day';
        } else {
            $this->validate($request, [
                'event_end' => 'required|date|after:event_time'
            ]);
            $requestData['duration'] = $request->event_end;
        }
        $requestData['last_update_by'] = auth()->user()->id;
// return $requestData;
        $event->fill($requestData);
        $event->save();

        EventMember::where('event_id' , $id)->delete();

        if ($request->member_type == 'all') {

            EventMember::create([
                'event_id' => $event->id,
                'member_type' => 'all'
            ]);

        } else {

            foreach ($request->members as $key => $value) {
                EventMember::create([
                    'event_id' => $event->id,
                    'member_type' => $request->member_type,
                    'member_id' => $value
                ]);
            }

        }
        Session::flash('flash_message', 'Event updated!');

        return redirect('workplace/humanresource/calendar');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Event::destroy($id);
        EventMember::where('event_id', $id)->delete();

        Session::flash('flash_message', 'Event deleted!');

        return redirect('workplace/humanresource/calendar');
    }

    public function members($type)
    {
       if($type == 'center'){ // && auth()->user()->can('access centers')
        return \App\Center::all()->pluck('name' , 'id');
       }
        if ($type == 'class') { // && auth()->user()->can('access class')
            return \App\Classes::all()->pluck('name', 'id');
        }
        if ($type == 'employee') { // && auth()->user()->can('access employee')
            return \App\Employee::all()->pluck('name', 'id');
        }

        return [];
    }
}

# **Task List for Fixing Mobile View Behaviors**

**Objective:** Refactor the JavaScript for the mobile view (\#mobileReportView) to ensure its interactive elements (dropdowns, modals) correctly mimic the dynamic, interconnected AJAX behaviors of the desktop table view.

### **✅ Task 1: Fix the "From Lesson" \-\> "To Lesson" Population Chain**

**Problem:** The mobile "To Lesson" dropdowns are not automatically populated after a "From Lesson" is selected, breaking the primary workflow.

**File to Edit:** The script section within the Blade view.

**Steps:**

1. **Locate the Mobile "From Lesson" Event Listeners:**  
   * Find the change event listener for .mobile-talaqqifromLessonClass.  
   * Find the change event listener for .mobile-talqeenfromLessonClass.  
   * Find the change event listener for the general .mobile-hefz-from-surat-dropdown.  
2. **Replicate the Desktop's AJAX Logic:**  
   * Inside each of these listeners, immediately after the existing mobileUpdateReport() or addOrUpdateDailyNouranyaReport() call, add a new AJAX GET request.  
   * **For Talaqqi:** The AJAX call should mirror the desktop's updateTalaqqiToLessonDropdown function.  
     * **URL:** base\_url \+ '/workplace/education/talaqqi-get-to-lesson/from-lesson/' \+ fromLessonId \+ '/nouranya-plan/' \+ nouranya\_plan\_id  
     * **On Success:** Use the response to populate the options of the .mobile-talaqqitoLessonClass dropdown within the *same student accordion*.  
   * **For Talqeen:** The AJAX call should mirror the desktop's updateTalqeenToLessonDropdown function.  
     * **URL:** base\_url \+ '/workplace/education/talqeen-get-to-lesson/from-lesson/' \+ from\_lesson\_id \+ '/nouranya-plan/' \+ nouranya\_plan\_id  
     * **On Success:** Populate the options of the .mobile-talqeentoLessonClass dropdown in the same accordion.  
   * **For General Hefz (Level 1 & 2):** The AJAX call should mirror the desktop's updateToLessonDropdown function.  
     * **URL:** base\_url \+ '/workplace/education/get-to-lesson/from-lesson/' \+ from\_lesson\_id \+ '/nouranya-plan/' \+ nouranya\_plan\_id  
     * **On Success:** Populate the options of the .mobile-hefz-to-surat-dropdown in the same accordion.

### **✅ Task 2: Fix the Line Number Population for Level 2 Students**

**Problem:** For Level 2 students, the "From Line" and "To Line" dropdowns are not being populated correctly after a lesson is selected.

**File to Edit:** The script section within the Blade view.

**Steps:**

1. **Modify the "From Lesson" Listener:**  
   * Locate the change listener for the .fromLessonClass (this class seems to be used on mobile as well, but ensure you're targeting the logic within the mobile view context).  
   * After a "From Lesson" is selected, and after the report is saved via AJAX, add a **new AJAX call** to fetch the corresponding line numbers.  
   * **URL:** {{ route("nouranya.report.get.lesson.lines", ":studentId") }}  
   * **Data:** Pass the lesson\_id, programLevelId, and from\_date.  
   * **On Success:** Use the response.line\_number\_range to populate the .lineNumberClass select dropdown within the same student accordion.  
2. **Modify the "To Lesson" Listener:**  
   * Locate the change listener for the .toLessonClass on mobile.  
   * After a "To Lesson" is selected, add a **new AJAX call**.  
   * **URL:** {{ route("nouranya.report.get.to.lesson.lines", ":studentId") }}  
   * **Data:** Pass the toLessonId, programLevelId, and the current date.  
   * **On Success:** Use the response to populate the .toLessonLineNumberClass dropdown in the same accordion, making sure to implement the logic from generateToLineOptions to show the correct range.

### **✅ Task 3: Strengthen State Management and Remove localStorage Reliance**

**Problem:** The mobile view's reliance on localStorage for report-id and revision-id is fragile and can lead to data being saved against the wrong records. The data-\* attributes should be the single source of truth.

**File to Edit:** The script section within the Blade view.

**Steps:**

1. **Analyze the mobileUpdateReport Function:**  
   * Find the success callback within the main AJAX call in this function.  
   * **On Success (Hefz):** When a response.hefzReport.id is returned, ensure this new ID is **explicitly written** to the data-report-id attribute of the parent .mobile-nouranya-section and the .mobile-nouranya-remarks-btn.  
   * **On Success (Revision):** When a response.hefzRevision.id is returned, ensure this new ID is **explicitly written** to the data-revision-id attribute of the parent .mobile-revision-section and the .mobile-revision-remarks-btn.  
2. **Remove localStorage Fallbacks:**  
   * In the click handlers for the remarks buttons (.mobile-nouranya-remarks-btn and .mobile-revision-remarks-btn) and any other functions that retrieve IDs, **remove the code that reads from localStorage**.  
   * Refactor the logic to only get the report-id or revision-id directly from the data-\* attribute of the relevant section (mobile-nouranya-section or mobile-revision-section). This ensures the most up-to-date ID is always used.

### **✅ Task 4: Ensure Correct Behavior on Attendance Change**

**Problem:** The logic to disable/enable report sections when attendance is marked "Absent" or "Excused" needs to be robust for mobile.

**File to Edit:** The script section within the Blade view.

**Steps:**

1. **Review the .mobile-hefz-attendance-dropdown change Listener:**  
   * The current logic correctly adds the .mobile-section-disabled class.  
   * **Confirm:** Ensure that when the attendance is changed back to "Present," the mobile-section-disabled class is correctly **removed**, and all input fields within the sections become interactive again.  
   * **Important:** After changing the attendance, the addOrUpdateDailyNouranyaReport() or mobileUpdateReport() function is called. Verify its success callback doesn't cause any unintended side effects on the now-disabled fields. The fields should remain disabled until the attendance is changed back.
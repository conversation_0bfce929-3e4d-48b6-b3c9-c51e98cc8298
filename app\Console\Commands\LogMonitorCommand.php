<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Services\EmailService;
use Carbon\Carbon;
use Modules\JobSeeker\Entities\JobSeekerSetting;

class LogMonitorCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:monitor
                            {--last-lines=100 : Number of lines to check from the end of each log file}
                            {--date=today : Specific date to check logs for (format: Y-m-d or "today")}
                            {--dry-run : Test mode - analyze logs but do not send alerts}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor log files for errors and send email notification if issues are found';

    /**
     * Log monitoring configuration loaded from database
     * 
     * @var array
     */
    protected array $config = [];
    
    /**
     * Keywords that indicate problematic issues in logs
     * 
     * @var array
     */
    protected array $problemKeywords = [];
    
    /**
     * Patterns to exclude from monitoring (operational logs)
     * 
     * @var array
     */
    protected array $excludedPatterns = [];
    
    /**
     * Whether to include INFO level logs in monitoring
     * 
     * @var bool
     */
    protected bool $includeInfoLogs = false;
    
    /**
     * Whether to use context analysis for smarter detection
     * 
     * @var bool
     */
    protected bool $contextAnalysisEnabled = true;

    /**
     * Target date for the command
     *
     * @var string
     */
    private $targetDate;

    /**
     * Load log monitoring configuration from database
     *
     * Loads configuration from jobseeker_settings table to control log monitoring
     * behavior including keywords, exclusion patterns, and sensitivity levels.
     * 
     * Purpose: Enable centralized control of log monitoring from Email Control Board
     * Side effects: Updates instance properties with configuration values
     * Errors: Falls back to default configuration if database settings unavailable
     */
    protected function loadConfiguration(): void
    {
        try {
            $this->config = [
                'enabled' => JobSeekerSetting::getValue('log_monitoring_enabled', 'true') === 'true',
                'sensitivity' => JobSeekerSetting::getValue('log_monitoring_sensitivity', 'normal'),
                'keywords' => json_decode(JobSeekerSetting::getValue('log_monitoring_keywords', '[]'), true),
                'excluded_patterns' => json_decode(JobSeekerSetting::getValue('log_monitoring_excluded_patterns', '[]'), true),
                'include_info_logs' => JobSeekerSetting::getValue('log_monitoring_include_info_logs', 'false') === 'true',
                'context_analysis' => JobSeekerSetting::getValue('log_monitoring_context_analysis', 'true') === 'true',
                'alert_recipient' => JobSeekerSetting::getValue('log_monitoring_alert_recipient', '<EMAIL>'),
            ];
            
            // Set instance properties from configuration
            $this->problemKeywords = $this->config['keywords'] ?: [
                'error', 'exception', 'fatal', 'failed', 'critical', 
                'emergency', 'alert', 'unexpected', 'crash', 'timeout'
            ];
            
            $this->excludedPatterns = $this->config['excluded_patterns'] ?: [
                'legacy_email_', 'transactional outbox', 'Email sent successfully',
                'Starting email send process', 'Created in transactional outbox'
            ];
            
            $this->includeInfoLogs = $this->config['include_info_logs'];
            $this->contextAnalysisEnabled = $this->config['context_analysis'];
            
            Log::info('LogMonitorCommand: Configuration loaded from database', [
                'enabled' => $this->config['enabled'],
                'sensitivity' => $this->config['sensitivity'],
                'keywords_count' => count($this->problemKeywords),
                'excluded_patterns_count' => count($this->excludedPatterns),
                'include_info_logs' => $this->includeInfoLogs,
                'context_analysis' => $this->contextAnalysisEnabled,
            ]);
            
        } catch (\Exception $e) {
            Log::warning('LogMonitorCommand: Failed to load configuration from database, using defaults', [
                'error' => $e->getMessage()
            ]);
            
            // Fallback to default configuration
            $this->problemKeywords = ['error', 'exception', 'fatal', 'failed', 'critical', 'emergency', 'unexpected', 'crash', 'timeout'];
            $this->excludedPatterns = ['legacy_email_', 'transactional outbox', 'Email sent successfully'];
            $this->includeInfoLogs = false;
            $this->contextAnalysisEnabled = true;
            $this->config = ['enabled' => true, 'alert_recipient' => '<EMAIL>'];
        }
    }

    /**
     * Display issues summary for dry-run mode
     *
     * @param array $issues
     * @return void
     */
    protected function displayIssuesSummary(array $issues): void
    {
        $this->info("\n=== ISSUES SUMMARY (DRY-RUN) ===");
        foreach ($issues as $filename => $fileIssues) {
            $this->info("\nFile: {$filename} ({" . count($fileIssues) . "} issues)");
            foreach (array_slice($fileIssues, 0, 3) as $issue) { // Show first 3 issues per file
                $this->line("  Line {$issue['line']}: " . substr($issue['content'], 0, 100) . "...");
            }
            if (count($fileIssues) > 3) {
                $this->line("  ... and " . (count($fileIssues) - 3) . " more issues");
            }
        }
        $this->info("=== END SUMMARY ===\n");
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $this->info('Starting log monitoring...');
            
            // Load configuration from database
            $this->loadConfiguration();
            
            // Check if monitoring is enabled
            if (!$this->config['enabled']) {
                $this->info('Log monitoring is disabled in configuration. Exiting.');
                Log::info('LogMonitorCommand: Monitoring disabled, exiting');
                return Command::SUCCESS;
            }
            
            // Check for dry-run mode
            $isDryRun = $this->option('dry-run');
            if ($isDryRun) {
                $this->info('Running in DRY-RUN mode - no alerts will be sent');
            }
            
            // Initialize the target date
            $dateOption = $this->option('date');
            if ($dateOption === 'today') {
                $this->targetDate = Carbon::today()->format('Y-m-d');
            } else {
                // Validate date format
                if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $dateOption)) {
                    $this->error('Invalid date format. Please use Y-m-d format (e.g., 2025-04-14)');
                    return Command::FAILURE;
                }
                $this->targetDate = $dateOption;
            }
            
            Log::info("LogMonitorCommand: Starting monitoring for date: {$this->targetDate}");
            $this->info("Monitoring logs for date: {$this->targetDate}");
            $this->info("Sensitivity level: {$this->config['sensitivity']}");
            $this->info("Keywords: " . count($this->problemKeywords) . " configured");
            $this->info("Exclusion patterns: " . count($this->excludedPatterns) . " configured");
            $this->info("Include INFO logs: " . ($this->includeInfoLogs ? 'Yes' : 'No'));
            $this->info("Context analysis: " . ($this->contextAnalysisEnabled ? 'Enabled' : 'Disabled'));
            
            $lastLines = $this->option('last-lines');
            $logPath = storage_path('logs');
            
            // Get all log files
            $allLogFiles = glob($logPath . '/*.log');
            $this->info("Found " . count($allLogFiles) . " total log files in logs directory");
            
            // Filter to only include today's dated log files and continuous logs
            $logFiles = $this->filterRelevantLogFiles($allLogFiles);
            
            if (empty($logFiles)) {
                $this->info("No relevant log files found for {$this->targetDate}.");
                Log::info("No relevant log files found for {$this->targetDate}.");
                return Command::SUCCESS;
            }
            
            $this->info("Found " . count($logFiles) . " log files to scan for {$this->targetDate}");
            
            // List the files being scanned
            foreach ($logFiles as $logFile) {
                $this->line("- " . basename($logFile));
            }
            
            $issues = $this->scanLogFiles($logFiles, $lastLines);
            
            $this->info("Found " . $this->countTotalIssues($issues) . " issues across " . count($issues) . " files");
            
            if (empty($issues)) {
                $this->info("No issues detected in log files for {$this->targetDate}.");
                Log::info("LogMonitorCommand: No issues found for {$this->targetDate}.", [
                    'date' => $this->targetDate,
                    'files_checked' => count($logFiles),
                    'dry_run' => $isDryRun,
                ]);
                return Command::SUCCESS;
            }
            
            if ($isDryRun) {
                $this->info("DRY-RUN: Would send alert for " . $this->countTotalIssues($issues) . " issues, but not actually sending.");
                $this->displayIssuesSummary($issues);
            } else {
                // Issues detected, send email notification
                $this->sendAlertEmail($issues);
                $this->info("Issues detected for {$this->targetDate} and alert email sent. Check your inbox.");
            }
            
            Log::info("LogMonitorCommand: Monitoring completed", [
                'date' => $this->targetDate,
                'issues_found' => $this->countTotalIssues($issues),
                'files_with_issues' => count($issues),
                'dry_run' => $isDryRun,
                'alert_sent' => !$isDryRun && !empty($issues),
            ]);
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            Log::error('Log monitoring command failed: ' . $e->getMessage());
            $this->error('Command failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
    
    /**
     * Scan log files for potential issues
     *
     * @param array $logFiles
     * @param int $lastLines
     * @return array
     */
    private function scanLogFiles(array $logFiles, int $lastLines): array
    {
        $issues = [];
        $totalLinesChecked = 0;
        $totalLinesMatched = 0;
        
        foreach ($logFiles as $logFile) {
            $filename = basename($logFile);
            $this->info("Scanning {$filename}...");
            
            // Read the last N lines of the log file
            $lines = $this->getLastLines($logFile, $lastLines);
            $linesCount = count($lines);
            $totalLinesChecked += $linesCount;
            
            $this->info("- Read {$linesCount} lines from {$filename}");
            
            $fileIssues = [];
            $issueCount = 0;
            $dateMatchCount = 0;
            
            // Check each line for today's date and problem keywords
            foreach ($lines as $lineNum => $line) {
                // Only process lines from the target date
                if ($this->isFromTargetDate($line)) {
                    $dateMatchCount++;
                    
                    if ($this->containsProblemKeywords($line)) {
                        $fileIssues[] = [
                            'line' => $lineNum + 1,
                            'content' => $line
                        ];
                        $issueCount++;
                    }
                }
            }
            
            $totalLinesMatched += $dateMatchCount;
            $this->info("- Found {$dateMatchCount} lines from {$this->targetDate} in {$filename}");
            
            if (!empty($fileIssues)) {
                $this->info("- Detected {$issueCount} issues in {$filename}");
                $issues[$filename] = $fileIssues;
            } else {
                $this->info("- No issues found in {$filename}");
            }
        }
        
        $this->info("Total lines checked: {$totalLinesChecked}, lines matched date {$this->targetDate}: {$totalLinesMatched}");
        
        return $issues;
    }
    
    /**
     * Get the last N lines from a file
     *
     * @param string $filePath
     * @param int $n
     * @return array
     */
    private function getLastLines(string $filePath, int $n): array
    {
        $lines = [];
        
        // Check if file exists and is readable
        if (!file_exists($filePath) || !is_readable($filePath)) {
            return $lines;
        }
        
        // Get file size
        $filesize = filesize($filePath);
        if ($filesize === 0) {
            return $lines;
        }
        
        // Open the file
        $file = fopen($filePath, 'r');
        if ($file === false) {
            return $lines;
        }
        
        // If the file is large, seek to a position near the end
        $seekPos = max(0, $filesize - ($n * 500)); // Estimate ~500 bytes per line
        if ($seekPos > 0) {
            fseek($file, $seekPos);
            // Discard first incomplete line
            fgets($file);
        }
        
        // Read all remaining lines
        while (!feof($file)) {
            $lines[] = fgets($file);
        }
        
        fclose($file);
        
        // Return only the last n lines
        return array_slice($lines, -$n);
    }
    
    /**
     * Check if a line contains any problem keywords with intelligent filtering
     *
     * Implements smart detection that considers log levels, exclusion patterns,
     * and context analysis to reduce false positives from operational logs.
     * 
     * @param string $line Log line to analyze
     * @return bool True if line contains genuine issues, false for operational logs
     * 
     * Purpose: Distinguish between real problems and normal operational messages
     * Side effects: None - pure analysis function
     * Errors: Returns false on analysis errors to avoid false positives
     */
    private function containsProblemKeywords(string $line): bool
    {
        $originalLine = $line;
        $line = strtolower($line);
        
        // Skip INFO logs if not enabled (reduces noise from operational logs)
        if (!$this->includeInfoLogs && strpos($line, '.info:') !== false) {
            return false;
        }
        
        // Apply sensitivity-based filtering
        if ($this->config['sensitivity'] === 'strict') {
            // Strict mode: Only ERROR and CRITICAL
            if (!preg_match('/\.(error|critical):/i', $originalLine)) {
                return false;
            }
        } elseif ($this->config['sensitivity'] === 'normal') {
            // Normal mode: ERROR, WARNING, CRITICAL
            if (!preg_match('/\.(error|warning|critical):/i', $originalLine)) {
                // Allow if contains problem keywords but not INFO level
                if (strpos($line, '.info:') !== false) {
                    return false;
                }
            }
        }
        // Verbose mode: Check all levels
        
        // Check exclusion patterns first (most important for EmailService operational logs)
        foreach ($this->excludedPatterns as $pattern) {
            if (strpos($line, strtolower($pattern)) !== false) {
                return false;
            }
        }
        
        // Check for problem keywords
        $foundKeyword = null;
        foreach ($this->problemKeywords as $keyword) {
            if (strpos($line, $keyword) !== false) {
                $foundKeyword = $keyword;
                break;
            }
        }
        
        if (!$foundKeyword) {
            return false;
        }
        
        // Apply context analysis if enabled (reduces false positives)
        if ($this->contextAnalysisEnabled) {
            return $this->analyzeContext($line, $foundKeyword);
        }
        
        return true;
    }
    
    /**
     * Analyze log line context to distinguish success from failure messages
     *
     * Examines surrounding context and success indicators to prevent false alerts
     * from success messages that happen to contain problem keywords.
     * 
     * @param string $line Log line in lowercase
     * @param string $keyword The problem keyword that was found
     * @return bool True if this is a genuine issue, false if it's a success message
     * 
     * Purpose: Prevent false alerts from success messages containing keywords like "alert"
     * Side effects: None - pure analysis function
     * Errors: Returns true on analysis errors to avoid missing real issues
     */
    private function analyzeContext(string $line, string $keyword): bool
    {
        try {
            // Success indicators that suggest this is not a real problem
            $successIndicators = [
                'successfully', 'completed', 'sent successfully', 'delivered',
                'executed successfully', 'finished', 'success', 'ok',
                'email sent successfully', 'alert email sent', 'notification sent'
            ];
            
            // Check if this is actually a success message
            foreach ($successIndicators as $indicator) {
                if (strpos($line, $indicator) !== false) {
                    // This appears to be a success message, not a real problem
                    return false;
                }
            }
            
            // Special case for "alert" keyword in email subjects/content
            if ($keyword === 'alert') {
                // Check if this is about sending alert emails (operational)
                if (strpos($line, 'system log issues') !== false || 
                    strpos($line, 'alert email') !== false ||
                    strpos($line, 'log alert') !== false) {
                    return false;
                }
            }
            
            // Special case for EmailService correlation IDs (operational logs)
            if (preg_match('/correlation_id.*legacy_email_/', $line)) {
                return false;
            }
            
            // If we reach here, treat as genuine issue
            return true;
            
        } catch (\Exception $e) {
            // On analysis error, err on the side of reporting (avoid missing real issues)
            Log::warning('LogMonitorCommand: Context analysis failed, treating as issue', [
                'error' => $e->getMessage(),
                'line_preview' => substr($line, 0, 100),
            ]);
            return true;
        }
    }
    
    /**
     * Send alert email about detected issues using configured recipient
     *
     * Sends alert email to the configured recipient with detailed issue information
     * including log analysis results and configuration context.
     * 
     * @param array $issues Detected issues grouped by log file
     * @return void
     * 
     * Purpose: Notify administrators of genuine log issues requiring attention
     * Side effects: Sends email via EmailService; logs alert delivery status
     * Errors: Logs email sending failures; does not throw to avoid command failure
     */
    private function sendAlertEmail(array $issues): void
    {
        try {
            $emailService = app(\App\Services\EmailService::class);
            
            // Use configured alert recipient
            $alertRecipient = $this->config['alert_recipient'] ?? '<EMAIL>';
            $to = [
                'email' => $alertRecipient,
                'name' => 'IT Department',
            ];
            
            $subject = "[ALERT] System Log Issues Detected on {$this->targetDate} - " . config('app.name');
            $view = 'emails.log_alert';
            
            $viewData = [
                'issues' => $issues,
                'totalIssues' => $this->countTotalIssues($issues),
                'systemName' => config('app.name'),
                'detectedAt' => now()->format('Y-m-d H:i:s'),
                'serverName' => gethostname(),
                'targetDate' => $this->targetDate,
                // Add configuration context for better alerts
                'monitoringConfig' => [
                    'sensitivity' => $this->config['sensitivity'] ?? 'normal',
                    'keywords_count' => count($this->problemKeywords),
                    'exclusions_count' => count($this->excludedPatterns),
                    'context_analysis' => $this->contextAnalysisEnabled,
                    'include_info_logs' => $this->includeInfoLogs,
                ],
            ];
            
            $emailService->sendEmail($to, $subject, $view, $viewData);
            
            $this->info("Alert email sent to {$alertRecipient}");
            
            Log::info('LogMonitorCommand: Alert email sent successfully', [
                'recipient' => $alertRecipient,
                'issues_count' => $this->countTotalIssues($issues),
                'files_affected' => count($issues),
                'target_date' => $this->targetDate,
            ]);
            
        } catch (\Exception $e) {
            $this->error("Failed to send alert email: {$e->getMessage()}");
            Log::error('LogMonitorCommand: Failed to send alert email', [
                'error' => $e->getMessage(),
                'issues_count' => $this->countTotalIssues($issues),
                'target_date' => $this->targetDate,
            ]);
        }
    }
    
    /**
     * Count total number of issues detected
     *
     * @param array $issues
     * @return int
     */
    private function countTotalIssues(array $issues): int
    {
        $count = 0;
        
        foreach ($issues as $fileIssues) {
            $count += count($fileIssues);
        }
        
        return $count;
    }

    /**
     * Filter log files to only include those relevant for the target date
     *
     * @param array $allLogFiles
     * @return array
     */
    private function filterRelevantLogFiles(array $allLogFiles): array
    {
        $relevantFiles = [];
        
        foreach ($allLogFiles as $file) {
            $filename = basename($file);
            
            // Case 1: Daily logs with date in filename (e.g., laravel-2025-04-14.log)
            if (preg_match('/-' . preg_quote($this->targetDate) . '\.log$/', $filename)) {
                $this->info("Including dated log file: {$filename} (matches target date)");
                $relevantFiles[] = $file;
                continue;
            }
            
            // Case 2: Generic logs without dates (e.g. laravel.log, email.log)
            if (!preg_match('/\d{4}-\d{2}-\d{2}/', $filename)) {
                $this->info("Including continuous log file: {$filename}");
                $relevantFiles[] = $file;
                continue;
            }
            
            // Case 3: Logs from other dates - skip these
            $this->info("Skipping log file from different date: {$filename}");
        }
        
        return $relevantFiles;
    }

    /**
     * Check if the log line is from the target date
     *
     * @param string $line
     * @return bool
     */
    private function isFromTargetDate(string $line): bool
    {
        // Extract date from log line (format: [2025-04-14 13:45:22])
        if (preg_match('/\[(\d{4}-\d{2}-\d{2})/', $line, $matches)) {
            $logDate = $matches[1];
            return $logDate === $this->targetDate;
        }
        
        // If no date found in the line, consider it not relevant
        return false;
    }
} 
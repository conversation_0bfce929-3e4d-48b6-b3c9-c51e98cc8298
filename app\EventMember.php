<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\EventMember
 *
 * @property int $id
 * @property int $event_id
 * @property string $member_type
 * @property int $member_id
 * @method static \Illuminate\Database\Eloquent\Builder|EventMember newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventMember newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventMember query()
 * @method static \Illuminate\Database\Eloquent\Builder|EventMember whereEventId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventMember whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventMember whereMemberId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventMember whereMemberType($value)
 * @mixin \Eloquent
 */
class EventMember extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'event_members';

    public $timestamps = false;

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['event_id' , 'member_type', 'member_id'];


    
}

<!DOCTYPE html>
<html>

<head>
    <title>ITQAN Platform - Class Reports Unit</title>
    <link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@4.x/css/materialdesignicons.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/vuetify@2.x/dist/vuetify.min.css" rel="stylesheet" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, minimal-ui" />
    <style>
        [v-cloak] {
            display: none;
        }
        @media print {
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <!-- App.vue -->

        <v-app v-cloak>
            <v-app-bar app color="blue darken-4" dark>
                <v-app-bar-nav-icon @click="goBack()">
                    <v-icon>mdi-arrow-left</v-icon>
                </v-app-bar-nav-icon>

                <v-toolbar-title>
                    ITQAN
                </v-toolbar-title>
                <div class="flex-grow-1"></div>
                <v-subheader>
                    Education Reports
                </v-subheader>
            </v-app-bar>
            <!-- Sizes your content based upon application components -->
            <v-content>
                <!-- Provides the application the proper gutter -->
                <v-container fluid>
                    <v-card class="mx-auto px-5 no-print" outlined >
                        <v-row>
                            <v-list-item-subtitle class="col-12">
                                <v-select :items="centers" label="Select Center" multiple v-model="selectedCenters"
                                    @blur="getClasses()">
                                </v-select>
                            </v-list-item-subtitle>
                        </v-row>
                        <v-row>
                            <v-list-item-subtitle class="col-sm">
                                <v-select v-model="selectedClasses" :items="classes" label="Select Class" multiple
                                    :disabled="!classes.length">
                                </v-select>
                            </v-list-item-subtitle>
                            <v-list-item-subtitle class="col-sm-3">
                                <strong>
                                    Report Date:
                                </strong>
                                <v-dialog ref="dialog" v-model="modal" width="290px" :close-on-content-click="false"
                                    :nudge-right="40">
                                    <template v-slot:activator="{ on }">
                                        <v-text-field readonly v-model="reportDate" v-on="on"></v-text-field>
                                    </template>
                                    <v-date-picker v-model="reportDate" scrollable @change="modal = false">
                                    </v-date-picker>
                                </v-dialog>
                            </v-list-item-subtitle>
                        </v-row>

                        <v-row justify="center" class="mb-2">
                            <v-btn color="primary" @click="getReport()" :disabled="!selectedClasses || !selectedClasses.length || isLoading" :loading="isLoading">
                                Get Report
                            </v-btn>
                        </v-row>
                    </v-card>
                    <v-divider></v-divider>
                    <div class="flex-grow-1 mt-3" v-if="details">
                        <v-data-iterator :items="details" :items-per-page.sync="classes.length" 
                            hide-default-footer>
                            <template v-slot:header>
                                <v-toolbar class="" color="indigo darken-5" dark flat>
                                    <v-toolbar-title>Report: @{{ reportDate }}</v-toolbar-title>

                                    <v-spacer></v-spacer>

                                    <v-btn icon @click="print()">
                                        <v-icon>mdi-printer</v-icon>
                                    </v-btn>
                                    <v-btn icon>
                                        <v-icon>mdi-download</v-icon>
                                    </v-btn>
      
                                </v-toolbar>
                            </template>
                            <template v-slot:default="props">
                                <v-row>
                                    <v-col v-for="item in props.items" :key="item.name" cols="12" sm="12">
                                        <v-card>
                                            <v-card-title class="subheading font-weight-bold">
                                                <v-col>
                                                    @{{ item.name }} - @{{ item.center }}
                                                </v-col>
                                                <v-col>
                                                    <ul style="font-size: 14px;">
                                                        <li v-for="teacher in item.teachers">
                                                            @{{ teacher }}
                                                        </li>
                                                    </ul>
                                                </v-col>
                                            </v-card-title>
                                            <v-divider></v-divider>

                                            <div dense v-for="program of item.programs" :key="program.id" class="pa-3">
                                                <h4>@{{ program.title }}</h4>
                                                <v-list dense>
                                                    {{-- <v-list dense v-for="subject of program.subjects" :key="subject.id"> --}}
                                                {{-- <h5>@{{ subject.title }}</h5> --}}
                                                <v-simple-table>
                                                    <template v-slot:default>
                                                        <thead>
                                                            <tr>
                                                                <th>Student Name</th>
                                                                <th>Age</th>
                                                                <th>Nationality</th>
                                                                <th>Last Lesson</th>
                                                                <th>Level</th>
                                                                <th>Note</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr v-for="student in program.students" :key="student.id">
                                                                <td>@{{ student.full_name }}</td>
                                                                <td>@{{ student.age }}</td>
                                                                <td>@{{ student.nationality }}</td>
                                                                <td>@{{ student.last_lesson }}</td>
                                                                <td>@{{ student.level }}</td>
                                                                <td>@{{ student.note }}</td>
                                                            </tr>
                                                        </tbody>
                                                    </template>
                                                </v-simple-table>
                                            </v-list>
                                            </div>
                                        </v-card>
                                    </v-col>
                                </v-row>
                            </template>

                            <template v-slot:footer>
                                <v-toolbar class="mt-2" color="indigo" dark flat>
                                    <v-toolbar-title class="subheading"></v-toolbar-title>
                                </v-toolbar>
                            </template>
                        </v-data-iterator>

                    </div>

                </v-container>
            </v-content>

            <v-fab-transition>
                <v-btn v-show="loading" fab small fixed bottom right>
                    <v-progress-circular :size="42" color="amber" indeterminate small></v-progress-circular>
                </v-btn>
            </v-fab-transition>

            <v-footer app>
                <!-- -->
            </v-footer>
        </v-app>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.x/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vuetify@2.x/dist/vuetify.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.19.0/axios.min.js"
        integrity="sha256-S1J4GVHHDMiirir9qsXWc8ZWw74PHHafpsHp5PXtjTs=" crossorigin="anonymous"></script>
    <script>
        new Vue({
            el: "#app",
            vuetify: new Vuetify({
                rtl: false
            }),
            data: function () {
                return {
                    dialog: false,
                    loading: false,
                    selectedCenters: null,
                    selectedClasses: null,
                    // centers: [],
                    centers: {!!  json_encode($centers) !!},
                    classes: [],
                    modal: false,
                    reportDate: '{{ date("Y-m-d") }}',
                    details: null,
                    isLoading:false,
                    report: {
                    },
                };
            },
            watch: {},
            methods: {
                goBack() {
                    window.history.back();
                    // window.location = '{{ url("workplace/education/reports") }}';
                },
                getClasses() {
                    var self = this;
                    axios.post('{{route("examinationcertification::reports.classes")}}', { centers: this.selectedCenters, _token: '{{ csrf_token() }}' }).then(function (res) {
                        console.log({ res });

                        self.classes = res.data;
                    })
                },
                getReport() {
                    this.isLoading=true;
                    self = this;
                    const data = {
                        classes: this.selectedClasses,
                        reportDate: this.reportDate,
                        _token: '{{ csrf_token() }}'
                    };
                    axios.post('{{route("examinationcertification::reports.generate")}}', data).then(function (res) {
                        console.log({ res });
                        self.details = res.data.details;
                    }).finally(function(){
                        self.isLoading = false;
                    })
                },
                print(){
                    window.print();
                }
            },
            created() {

            },
        });
    </script>
</body>

</html>
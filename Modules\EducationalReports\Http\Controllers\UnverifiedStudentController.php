<?php

namespace Modules\EducationalReports\Http\Controllers;

use App\Center;
use App\CenterTranslation;
use App\Country;
use App\Notifications\SuperiorResetPassword;
use App\Notifications\UserStatusChangedToNewApplication;
use App\Organization;
use App\Program;
use App\Scopes\OrganizationScope;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Faker\Factory;
use Faker\Provider\en_UG\PhoneNumber;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Mail\StudentCreated;
use App\Student;
use App\Guardian;
use App\Employee;
use App\Role;
use App\Cen_Emp;
use App\Admission;
use App\Classes;
use App\StudentHefzPlan;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Mail;
use PhpParser\Node\Stmt\Foreach_;
use Yajra\DataTables\Facades\DataTables;

class UnverifiedStudentController extends Controller
{


    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function __invoke(Request $request)
    {


        DB::connection()->enableQueryLog();

        if ($request->ajax()) {
            try {


                $unverifiedUsersQuery = User::whereNull('email_verified_at')->orderBy('created_at', 'desc')->get();


                $trxDatatables = $unverifiedUsersQuery;


                return \Yajra\DataTables\DataTables::of($trxDatatables)
                    ->addIndexColumn()
                    ->addColumn('created_at', function ($row) {

                        return value($row->created_at)->diffForHumans();
//                    return $row->created_at->diffForHumans();
                    })
                    ->addColumn('action', function ($row) use ($request) {

                        $input = '<br><div class="ui left floated compact segment">
                                  <div class="ui fitted toggle checkbox">
                                    <input data-stname="' . $row->full_name . '" data-userid="' . $row->id . '" class="archivedStatusCheckbox" id="unverifiedStatusCheckbox" type="checkbox">
                                    <label></label>
                                  </div>
                                </div>';
                        return $input;
                    })
                    ->rawColumns(['action'])
                    ->make(true);

            }
            catch
            (\Exception $e) {

//                Toastr::error('Operation Failed', 'Failed');
                return response()->json($e->getMessage());
            }
}


    }


}
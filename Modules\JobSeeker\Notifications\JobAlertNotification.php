<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Collection;
use Modules\JobSeeker\Entities\Job;
use Mo<PERSON>les\JobSeeker\Entities\JobNotificationSetup;
use Illuminate\Support\Facades\Log;
use App\Services\EmailService;

final class JobAlertNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @param Collection<int, Job> $jobs Collection of Job models relevant to the recipient
     * @param JobNotificationSetup $setup The notification setup instance
     * @return void
     */
    public function __construct(
        private readonly Collection $jobs,
        private readonly JobNotificationSetup $setup
    ) {
        $this->queue = 'job_notifications';
    }

    /**
     * Policy: JobSeeker emails are sent exclusively via EmailService in services.
     * This notification is kept for compatibility/logging and returns no channels.
     */
    public function via($notifiable): array
    {
        Log::info('JobAlertNotification: No channels (EmailService handles sending)', [
            'notifiable_id' => $notifiable->id ?? 'unknown',
            'notifiable_type' => get_class($notifiable),
            'setup_id' => $this->setup->id,
            'jobs_count' => $this->jobs->count()
        ]);
        return [];
    }

    // No toMail(): EmailService handles all sends from services by policy.

    /**
     * Format jobs for email display.
     *
     * @return array
     */
    private function formatJobsForEmail(): array
    {
        return $this->jobs->map(function (Job $job) {
            return [
                'id' => $job->id,
                'title' => $job->title ?? $job->position,
                'position' => $job->position,
                'company' => $job->company ?? $job->company_name,
                'company_name' => $job->company_name,
                'location' => $job->location ?? $job->locations,
                'locations' => $job->locations,
                'publish_date' => $job->publish_date?->format('Y-m-d'),
                'salary' => $job->salary,
                'contract_type' => $job->contract_type,
                'work_type' => $job->work_type,
                'slug' => $job->slug,
                'url' => $job->url,
                'source' => $job->source,
            ];
        })->toArray();
    }

    /**
     * Get the email subject for this notification.
     *
     * @return string
     */
    private function getEmailSubject(): string
    {
        $jobCount = $this->jobs->count();

        // For single job notifications, include job title and company name
        if ($jobCount === 1) {
            $job = $this->jobs->first();
            $jobTitle = $job->position ?? 'Job Position';
            $companyName = $job->company_name ?? 'Company';
            return "Job Opportunity: {$jobTitle} at {$companyName}";
        }

        // For multiple jobs, use generic alert format
        $pluralJobs = $jobCount > 1 ? 'jobs' : 'job';
        return "Job Alert: {$jobCount} new {$pluralJobs} matching your criteria";
    }

    /**
     * Get the setup ID for this notification.
     *
     * @return int|null
     */
    public function getSetupId(): ?int
    {
        return $this->setup->id;
    }

    /**
     * Get the job IDs for this notification.
     *
     * @return array
     */
    public function getJobIds(): array
    {
        return $this->jobs->pluck('id')->toArray();
    }
} 
<?php

namespace App\Observers;

use App\Classes;
use App\ClassSubjectTimetable;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

class TeacherTimetableObserver
{
    /**
     * Handle the ClassSubjectTimetable "created" event.
     */
    public function creating(ClassSubjectTimetable $classSubjectTimetable): void
    {


        // Count the number of non null values in the columns
        $count = 0;
        $columns = ['sat', 'sun', 'mon', 'tue', 'wed', 'thu', 'fri'];
        foreach ($columns as $column) {
            if ($classSubjectTimetable->$column !== null) {
                $count++;
            }
        }


        $classSubjectTimetable->days_count = $count;
    }

    /**
     * This observer method is used to update a column called `days_count` in a `ClassSubjectTimetable` table in a Laravel application whenever a non-null value is inserted or updated into the columns `[sat, sun, mon, tue, wed, thu, fri]`.

    The method first counts the number of non-null values in the columns and stores it in the `$count` variable. If the request has a `class_id`, it finds the corresponding record in the `Classes` table and updates its `classes_per_month` column with the `$count` value. Finally, the method sets the `$count` value to the `days_count` column in the `ClassSubjectTimetable` table.

    The comment for the method is clear and provides a good understanding of what the method does. It explains the purpose of the method and the logic behind the code.
     */
    public function updating(ClassSubjectTimetable $classSubjectTimetable): void
    {


        // Count the number of non null values in the columns
        $count = 0;
        $columns = ['sat', 'sun', 'mon', 'tue', 'wed', 'thu', 'fri'];
        foreach ($columns as $column) {
            if ($classSubjectTimetable->$column !== null) {
                $count++;
            }
        }

        if (request()->has('class_id')) {
            $class = Classes::find(request('class_id'));

            if ($class) {
                $currentMonth = Carbon::now();
                $days_in_month = $currentMonth->daysInMonth;
                $numberOfWeeksinMonth = ceil($days_in_month / 7);

                $classCountPerMonth = $numberOfWeeksinMonth*$count;
                // Update the classes table here
                $class->update(['classes_per_month' => $classCountPerMonth]);
            }
        }

        $classSubjectTimetable->days_count = $count;

    }

    /**
     * Handle the ClassSubjectTimetable "deleted" event.
     */
    public function deleted(ClassSubjectTimetable $classSubjectTimetable): void
    {
        //
    }

    /**
     * Handle the ClassSubjectTimetable "restored" event.
     */
    public function restored(ClassSubjectTimetable $classSubjectTimetable): void
    {
        //
    }

    /**
     * Handle the ClassSubjectTimetable "force deleted" event.
     */
    public function forceDeleted(ClassSubjectTimetable $classSubjectTimetable): void
    {
        //
    }
}

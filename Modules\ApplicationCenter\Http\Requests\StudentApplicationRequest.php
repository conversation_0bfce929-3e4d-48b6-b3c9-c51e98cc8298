<?php

namespace Modules\ApplicationCenter\Http\Requests;

use App\Rules\CheckDependentEmail;
use App\Rules\CheckIfStringIsArabic;
use App\Rules\CheckIfStringIsEnglish;
use App\Student;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use function PHPUnit\Framework\returnArgument;
//StudentRegistrationByEmployeeRequest
class StudentApplicationRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {


        
        // Store selected values in session
        session()->forget(['program','center','classes']);
        if (request()->filled('program')){
            session()->put(['program_id' => request()->get('program')]);
        }

        if (request()->filled('center')){
            session()->put(['center_id' => request()->get('center')]);
        }
        if (request()->filled('classes')){
            session()->put(['class_id' => request()->get('classes')]);
        }

        // Determine if a student photo exists in the database
        $studentPhotoExistsInDB = auth()->check()
            && auth()->user()->student
            && !empty(auth()->user()->student->student_photo);

        // Check if a photo has been staged in the temp folder on disk
        $userId = auth()->id();
        $stagedPhotoDir = "temp/users/{$userId}/profile_picture";
        $stagedPhotoExists = $userId
            ? (bool) Storage::disk('public')->exists($stagedPhotoDir)
            : false;

        // Require photo only if none exists in DB or staged
        $photoIsRequired = !$studentPhotoExistsInDB && !$stagedPhotoExists;

        
        return [
            'photo' => $photoIsRequired ? 'required|image|mimes:jpg,png,gif|max:2048' : 'nullable|image|mimes:jpg,png,gif|max:2048',
            'country_dial_code' => 'required_with:student_mobile',
            'student_mobile'    => 'required_with:country_dial_code',
            'national_id_number' => 'sometimes|required',
            'student_admission_pic_input_holder' => 'student_admission_pic',
            'program' => 'required',
            'center' => 'required',
            'classes' => 'required',
            'gender' => 'sometimes|required',
            'full_name_trans' => ['nullable', new CheckIfStringIsArabic],
            'date_of_birth' => 'sometimes|required|date|before:' . Carbon::now()->subYears(3),
            'document_title_1' => 'required_with:document_file_1',
            'document_title_2' => 'required_with:document_file_2',
            'document_title_3' => 'required_with:document_file_3',
            'document_title_4' => 'required_with:document_file_4',
            'document_file_1' => 'sometimes|required_with:document_title_1|mimes:jpg,png,gif,pdf|max:2000', /** 2 mb */
            'document_file_2' => 'sometimes|required_with:document_title_2|mimes:jpg,png,gif,pdf|max:2000',
            'document_file_3' => 'sometimes|required_with:document_title_3|mimes:jpg,png,gif,pdf|max:2000',
            'document_file_4' => 'sometimes|required_with:document_title_4|mimes:jpg,png,gif,pdf|max:2000',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            'photo.image' => 'The photo must be an image file.',
            'photo.mimes' => 'The photo must be a file of type: jpg, png, gif.',
            'photo.max' => 'The photo may not be greater than 2MB.',
            'photo.required' => 'Student photo is required. Please upload your image.',
            'student_mobile' => 'The :attribute field contains an invalid number.',
            'date_of_birth.before' => 'A student should be at least 3 years old',
            'file.required' => 'Please upload your image',
            'student_mobile.phone' => 'prefix phone number with a + sign, e.g. +60 ....',
        ];
    }
}

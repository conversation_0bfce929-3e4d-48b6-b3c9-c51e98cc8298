<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Translatable;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Relations\Pivot;

class ClassProgram extends Pivot
{
//    use Translatable;

//    public $translatedAttributes = array('description', 'title');

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'class_programs';

    /**
     * The database primary key value.
     *
     * @var string
     */
//    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['class_id', 'program_id', 'program_level_id'];

// You can add timestamps if your pivot table includes created_at and updated_at
    public $timestamps = true;


//    protected static function boot()
//    {
//        parent::boot();
//
//        static::addGlobalScope(new OrganizationScope);
//    }
}

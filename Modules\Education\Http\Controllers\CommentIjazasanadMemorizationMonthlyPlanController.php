<?php

namespace Modules\Education\Http\Controllers;


use App\IjazasanadMemorizationPlan;

use App\StudentRevisionPlan;

use Modules\EducationalReports\Http\Requests\CreateHefzPlanRequest;

use Illuminate\Http\Request;

use App\Http\Controllers\Controller;

class CommentIjazasanadMemorizationMonthlyPlanController extends Controller
{

    public  function update(Request $request)
    {

        $plan = IjazasanadMemorizationPlan::where('id',$request->get('id'))->update([
            'supervisor_comment' => $request->comment
        ]);

        return response()->json(['message' => 'comment added to the plan']);




            }


    public function show(Request $request,$planId)
    {




        $plan = StudentRevisionPlan::where('id', $planId)->first()->supervisor_comment;
        return response()->json(['data' => $plan]);


    }
}

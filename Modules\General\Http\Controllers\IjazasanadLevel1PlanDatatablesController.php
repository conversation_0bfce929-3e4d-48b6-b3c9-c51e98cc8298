<?php

namespace Modules\General\Http\Controllers;


use App\Admission;
use App\Center;
use App\CenterEmployee;
use App\MoshafSurah;
use App\ProgramLevelLesson;
use App\Scopes\OrganizationScope;
use App\Student;
use App\IjazasanadMemorizationPlan;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use App\Services\StudentImageService;

class IjazasanadLevel1PlanDatatablesController extends Controller
{


    protected $studentImageService;

    public function __construct(StudentImageService $studentImageService)
    {
        $this->studentLevelService = $studentLevelService;
        $this->studentImageService = $studentImageService;
    }



    public function getPlansNeedApproval(Request $request)
    {

        if ($request->ajax()) {

            $queryPart = ' AND student_hefz_plans.status = "waiting_for_approval"';




            $classroomIds = $request->input('classroomId'); // Get classroom IDs from the request




            if (auth()->user()->hasRole(["managing-director_2_"])) {
                \DB::enableQueryLog();

                $studentsApprovalQuery = IjazasanadMemorizationPlan::where('status', '=', 'waiting_for_approval')
                    ->where(function ($query) {
                        $query->whereNotNull('talqeen_from_lesson')
                            ->orWhereNotNull('talqeen_to_lesson')
                            ->orWhereNotNull('revision_from_lesson')
                            ->orWhereNotNull('revision_to_lesson')
                            ->orWhereNotNull('jazariyah_from_lesson')
                            ->orWhereNotNull('jazariyah_to_lesson')
                            ->orWhereNotNull('seminars_from_lesson')
                            ->orWhereNotNull('seminars_to_lesson');
                    })
                    ->whereNotNull('class_id')
                    ->when($classroomIds, function ($query) use ($classroomIds) {
                        return $query->whereIn('class_id', $classroomIds);
                    })
                    ->whereHas('student.studentProgramLevels.programlevel', function ($query) {
                        $query->whereTranslation('title', 'Level 1: Preparation Course');
                    })
                    ->with(['student', 'halaqah', 'center'])
                    ->orderBy('updated_at', 'DESC');
            } else {
                $studentsApprovalQuery = IjazasanadMemorizationPlan::where('status', '=', 'waiting_for_approval')
                    ->where(function ($query) {
                        $query->whereNotNull('talqeen_from_lesson')
                            ->orWhereNotNull('talqeen_to_lesson')
                            ->orWhereNotNull('revision_from_lesson')
                            ->orWhereNotNull('revision_to_lesson')
                            ->orWhereNotNull('jazariyah_from_lesson')
                            ->orWhereNotNull('jazariyah_to_lesson')
                            ->orWhereNotNull('seminars_from_lesson')
                            ->orWhereNotNull('seminars_to_lesson');
                    })
                    ->whereNotNull('class_id')
//                    ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())
                    ->when($classroomIds, function ($query) use ($classroomIds) {
                        return $query->whereIn('class_id', $classroomIds);
                    })
                    ->whereHas('student.studentProgramLevels.programlevel', function ($query) {
                        $query->whereTranslation('title', 'Level 1: Preparation Course');
                    })
                    ->with(['student', 'halaqah', 'center'])
                    ->orderBy('updated_at', 'DESC');
            }



            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)

                ->addColumn('select', function ($row) {
                    return '<input type="checkbox" class="checkbox" name="id[]" value="'.$row->id.'">';
                })
                ->addColumn('centerName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('studentName', function ($row) {
                    // Extract the student relationship
                    $student = $row->student;

                    // Ensure the student relationship exists
                    if (!$student) {
                        return 'N/A';
                    }

                    // Determine the gender and set the appropriate default image
                    $gender = strtolower($student->gender);
                    $femalePlaceholder = asset('assets/workplace/img/female student profile picture placeholderr.png');
                    $malePlaceholder = asset('assets/workplace/img/male profile picture placeholder.png');
                    $genderBasedDefaultImage = ($gender === 'female') ? $femalePlaceholder : $malePlaceholder;

                    // Define the path to the student's photo
                    $studentPhotoPath = public_path($student->student_photo);

                    // Determine if the student's photo exists and is not empty
                    $imageUrl = (!empty($student->student_photo) && file_exists($studentPhotoPath))
                        ? asset($student->student_photo)
                        : $genderBasedDefaultImage;

                    // Calculate age from date of birth
                    try {
                        $age = Carbon::parse($student->date_of_birth)->age;
                    } catch (\Exception $e) {
                        $age = 'N/A';
                    }

                    // Truncate the name if it is longer than 10 characters
                    $truncatedName = (strlen($student->full_name) > 10)
                        ? substr($student->full_name, 0, 10) . '...'
                        : $student->full_name;

                    // Sanitize the full name for safe HTML output
                    $safeFullName = htmlspecialchars($student->full_name, ENT_QUOTES, 'UTF-8');
                    $safeTruncatedName = htmlspecialchars($truncatedName, ENT_QUOTES, 'UTF-8');

                    // Create the HTML for the student's name with tooltip and age
                    $nameWithTooltip = '<span data-toggle="tooltip" title="' . $safeFullName . '">' . $safeTruncatedName . '</span>';


                    $actionButtons = '
                    <div class="row-action-buttons" style="margin-top: 8px; display: flex; gap: 4px; flex-wrap: wrap;">
                        <button type="button" class="btn btn-success ijazasanad-level1-individual-approve-btn" 
                                data-ijazasanad-level1-plan-id="' . $row->id . '" 
                                data-student-name="' . $safeFullName . '"
                                title="Approve Plan"
                                style="padding: 2px 6px; font-size: 10px; line-height: 1.2; border-radius: 3px; min-width: auto;">
                            <i class="glyphicon glyphicon-ok" style="font-size: 9px;"></i> Approve
                        </button>
                            <a href="' . $stEditMonthlyPlanRoute . '" target="_blank" 
                           class="btn btn-primary" title="Edit Plan"
                           style="padding: 2px 6px; font-size: 10px; line-height: 1.2; border-radius: 3px; min-width: auto; text-decoration: none; display: inline-block;">
                            <i class="glyphicon glyphicon-edit" style="font-size: 9px;"></i> Edit
                        </a>
                        <button type="button" class="btn btn-warning commentIjazasanadLevel1ModalTriggerBtn" 
                                data-ijazasanad-level1-plan_id="' . $row->id . '"
                                data-ijazasanad-level1-supervisor_comment="' . optional($row)->supervisor_comment . '"
                                data-ijazasanad-level1-student_id="' . $row->student_id . '"
                                data-catid="' . $row->id . '" 
                                data-toggle="modal"
                                data-target="#commentIjazasanadLevel1Plan"
                                title="Add Comment"
                                style="padding: 2px 6px; font-size: 10px; line-height: 1.2; border-radius: 3px; min-width: auto;">
                            <i class="glyphicon glyphicon-comment" style="font-size: 9px;"></i> Comment
                        </button>
                    </div>';


                    // Combine everything into an HTML block (with image on the left and text on the right)
                    // Combine everything into an HTML block (with image on the left and text on the right)
                    $image = $student ? $this->studentImageService->getStudentImageUrl($student) : asset('maleStudentProfilePicture.png');

                    // Combine everything into an HTML block (with image on the left and text on the right)
                    $html = '
                                                <div class="student-container">
                                <div style="flex-shrink: 0;">
                                                                <img class="studentImage" style="border-radius: 50%; width: 60px; height: 60px; object-fit: cover;" src="' . $image . '">
                                    </div>
                                    <div class="student-details">
                                        ' . $nameWithTooltip . '
                                        ' . $actionButtons . '
                                    </div>
                                </div>';

                    return $html;
                })
                ->addColumn('fromtoLesson', function ($row) {

                    // Check Talqeen lesson values
                    $talqeenFromLesson = ProgramLevelLesson::where('id',$row->talqeen_from_lesson)->first()['properties']?? null;
                    $talqeenToLesson = ProgramLevelLesson::where('id',$row->talqeen_to_lesson)->first()['properties'] ?? null;


                    // Check Revision lesson values
                    $revisionFromLesson = ProgramLevelLesson::where('id',$row->revision_from_lesson)->first()['properties']?? null;
                    $revisionToLesson = ProgramLevelLesson::where('id',$row->revision_to_lesson)->first()['properties'] ?? null;

                    // Check Jazaria lesson values
                    $jazariaFromLesson =  ProgramLevelLesson::where('id',$row->jazariyah_from_lesson)->first()['properties']  ?? null;
                    $jazariaToLesson = ProgramLevelLesson::where('id',$row->jazariyah_to_lesson)->first()['properties']  ?? null;

                    // Check Seminars lesson values
                    $seminarsFromLesson = ProgramLevelLesson::where('id',$row->seminars_from_lesson)->first()['properties']  ?? null;
                    $seminarsToLesson = ProgramLevelLesson::where('id',$row->seminars_to_lesson)->first()['properties']  ?? null;

                    // Initialize lesson display string
                    $lessonDisplay = '';

                    // Display Talqeen lessons if available
                    if ($talqeenFromLesson && $talqeenToLesson) {

                        $talqeenFromLessonNo = $talqeenFromLesson['no'] ?? null;
                        $talqeenToLessonNo = $talqeenToLesson['no'] ?? null;

                        // Get the from-verse and to-verse from Talqeen properties
                        $fromVerse = $talqeenFromLesson['fromVerse'] ?? 'N/A';
                        $toVerse = $talqeenFromLesson['toVerse'] ?? 'N/A';
                        $fromVerseTo = $talqeenToLesson['fromVerse'] ?? 'N/A';
                        $toVerseTo = $talqeenToLesson['toVerse'] ?? 'N/A';


                        $lessonDisplay .= '<div><strong>Talqeen From:</strong> <a href="#" data-toggle="tooltip" title="From: ' . $fromVerse . ' : ' . $toVerse . '">' . $talqeenFromLessonNo . '</a> - ';
                        $lessonDisplay .= '<strong>Talqeen To:</strong> <a href="#" data-toggle="tooltip" title="From: ' . $fromVerseTo . ' : ' . $toVerseTo . '">' . $talqeenToLessonNo . '</a></div>';
                    }

                    // Display Revision lessons if available
                    if ($revisionFromLesson && $revisionToLesson) {

                        $revisionFromLessonNo = $revisionFromLesson['no'] ?? null;
                        $revisionToLessonNo = $revisionToLesson['no']  ?? null;

                        $fromJuz = $revisionFromLesson['juzu'] ?? 'N/A';
                        $toJuz = $revisionToLesson['juzu'] ?? 'N/A';

                        // Show tooltip with Juz information
                        $lessonDisplay .= '<div><strong>Revision From:</strong> <a href="#" data-toggle="tooltip" title="Juz: ' . $fromJuz . '">' . $revisionFromLessonNo . '</a> - ';
                        $lessonDisplay .= '<strong>Revision To:</strong> <a href="#" data-toggle="tooltip" title="Juz: ' . $toJuz . '">' . $revisionToLessonNo . '</a></div>';
                    }

                    // Display Jazaria lessons if available
                    if ($jazariaFromLesson && $jazariaToLesson) {
                        // Get the Jazariya from properties
                        $jazariaFromLessonNo = $jazariaFromLesson['no'];
                        $jazariaToLessonNo = $jazariaToLesson['no'];
                        $jazariyaFrom = $jazariaFromLesson['jazariyah'] ?? 'N/A';
                        $jazariyaTo = $jazariaToLesson['jazariyah'] ?? 'N/A';

                        // Show tooltip with Jazariya information
                        $lessonDisplay .= '<div><strong>Jazariyah From:</strong> <a href="#" data-toggle="tooltip" title="Jazariya: ' . $jazariyaFrom . '">' . $jazariaFromLessonNo . '</a> - ';
                        $lessonDisplay .= '<strong>Jazariyah To:</strong> <a href="#" data-toggle="tooltip" title="Jazariya: ' . $jazariyaTo . '">' . $jazariaToLessonNo . '</a></div>';
                    }

                    // Display Seminars lessons if available
                    if ($seminarsFromLesson && $seminarsToLesson) {
                        $seminarsFromLessonNo =$seminarsFromLesson['no'] ;
                         $seminarsToLessoNo = $seminarsToLesson['no'];

                        // Get the Lesson Name from Seminars properties
                        $fromLessonName = $seminarsFromLesson['lessonName'] ?? 'N/A';
                        $toLessonName = $seminarsToLesson['lessonName'] ?? 'N/A';

                        // Show tooltip with lesson name
                        $lessonDisplay .= '<div><strong>Seminars From:</strong> <a href="#" data-toggle="tooltip" title="Lesson Name: ' . $fromLessonName . '">' . $seminarsFromLessonNo . '</a> - ';
                        $lessonDisplay .= '<strong>Seminars To:</strong> <a href="#" data-toggle="tooltip" title="Lesson Name: ' . $toLessonName . '">' . $seminarsToLessoNo . '</a></div>';
                    }

                    return $lessonDisplay ?: 'No lessons assigned';
                })
                ->addColumn('planUpdateDate', function ($row) {


                    return $row->updated_at->diffForHumans();


                })
                ->addColumn('createdByUpdatedBy', function ($row) {


                    if( $row->created_by === $row->updated_by){



//                        $stShowRoute = route('admission.students.show', $row->allStudents->user->id);
                        $genderColor = $row->creator['gender'] == 'male' ? '#34b8bc;!important' : '#FA5661;!important';

                        if (strlen($row->creator['full_name']) > 22) {
                            $fullname = Str::limit(Str::title($row->creator['full_name']),22,' ...');
//                            $fullname = Str::title($row->creator['full_name']);


                            return '<a style="color:' . $genderColor . '" target="_blank" href="#" ><strong data-tooltip="' . Str::title($row->creator['full_name']) . '" >' . $fullname . '</strong></a>';
                        } else {
                            $fullname = Str::title($row->creator['full_name']);
                            return '<a style="color:' . $genderColor . '" target="_blank" href="#" ><strong data-tooltip="' . Str::title($row->creator['full_name']) . '" style="color:"' . $genderColor . '">' . $fullname . '</strong></a>';
                        }



//                        return  $row->creator['full_name'];
                    }
                    else{

                        $genderColor = $row->creator['gender'] == 'male' ? '#34b8bc;!important' : '#FA5661;!important';



                        if (strlen($row->creator['full_name']) > 22) {
                            $creatorFullname = Str::limit(Str::title($row->creator['full_name']),22,' ...');


                        } else {
                            $creatorFullname = Str::title($row->creator['full_name']);

                        }
                        if (strlen($row->updator['full_name']) > 22) {
                            $updatorFullname = Str::limit(Str::title($row->updator['full_name']),22,' ...');


                        } else {
                            $updatorFullname = Str::title($row->updator['full_name']);

                        }

                        return '<a style="color:' . $genderColor . '" target="_blank" href="#" ><strong data-tooltip="' . Str::title($row->creator['full_name'].'/'.$row->updator['full_name']) . '" >' .  $creatorFullname.' / '. $updatorFullname . '</strong></a>';




                    }




//                        if( $row->created_by === $row->updated_by){
//
//                            return  $row->creator['full_name'];
//                        }
//                        else{
//                            return   $row->creator['full_name'].' / '. $row->updator['full_name'];
//
//
//                        }




                })
                ->addColumn('planDate', function ($row) {

                        return Carbon::parse($row->start_date)->format('F Y');

                })
                ->addColumn('created_at', function ($row) {

                    return value($row['created_at'])->diffForHumans();
                })
                ->addColumn('halaqah', function ($row) {

// Assuming $row is coming from a query or model.
                    $halaqahName = $row->halaqah->name;
                    $halaqahId = $row->halaqah->id; // Assuming halaqah has an id property for the URL

// Truncate the name if it is longer than 13 characters
                    $truncatedName = strlen($halaqahName) > 13 ? substr($halaqahName, 0, 13) . '...' : $halaqahName;

// Generate the class view URL (modify the route name and parameters as necessary)
                    $classViewUrl = route('classes.show', ['id' => $halaqahId]);

// Create the HTML anchor element with tooltip and URL
                    $span = '<a href="' . htmlspecialchars($classViewUrl, ENT_QUOTES, 'UTF-8') . '" target="_blank" ' .
                        'data-toggle="tooltip" title="' . htmlspecialchars($halaqahName, ENT_QUOTES, 'UTF-8') . '">' .
                        htmlspecialchars($truncatedName, ENT_QUOTES, 'UTF-8') .
                        '</a>';

// Return the span string (You can return it as part of a JSON response, or however it's needed)
                    return $span;



                    return $row->halaqah->name;

                })

                ->addColumn('check', function ($row) {



                    $stEditMonthlyPlanRoute = route('monthly-plan.show',[$row->class_id,$row->created_at->format('Y-m-d')]);

                    $stEditRoute = route('students.edit', $row->student_id);
                    $stShowRoute = route('students.show', $row->student_id);

                    $btns = '<div class="ui vertical icon menu">
  <a target="_blank" href="' . $stEditMonthlyPlanRoute . '" title="Edit Monthly Plan" class="item">
    <i class="edit icon"></i>Edit
  </a>
  <a 
  class="item commentIjazasanadMemorizationModalTriggerBtn" 
  id="commentIjazasanadMemorizationModalTriggerBtn"
   style=" cursor:pointer;"
   data-hefz-plan_id = "' . $row->id . '"
   data-hefz-supervisor_comment = "' . optional($row)->supervisor_comment . '"
   data-hefz-student_id = "' . $row->student->id . '"
    data-catid=' . $row->id . ' data-toggle="modal"
     data-target="#commentHefzPlan" >
    <i class="comment alternate icon"></i>Comment
  </a>
  <a class="item approveModalTriggerBtn"
  id="approvalModalTriggerBtn "
                    
                     style=" cursor:pointer;"
                    data-hefz-plan_id = "' . $row->id . '"
                    data-hefz-student_id = "' . $row->student->id . '"
                    data-catid=' . $row->id . ' data-toggle="modal"
                    data-target="#approve">
    <i class="check icon"></i>Approve</a>
</div>
                                                                             

                                                                            
                                                                                                                ';

//
                    return $btns;
                })
                ->addColumn('studentAge', function ($row) {

                    return $row->student->age;

                })
                ->setRowAttr([
                    'data-memorizationPlan_id' => function($row) {
                        return $row->id; // Assuming $row->id is your report ID
                    },
                    'data-studentName' => function($row) {
                        return $row->student->full_name; // Assuming $row->id is your report ID
                    },

            'data-studentMonthlyMemorizationPlanRoute' => function($row) {
                $stEditMonthlyPlanRoute = route('monthly-plan.show',[$row->class_id,$row->created_at->format('Y-m-d')]);

                return $stEditMonthlyPlanRoute; // Assuming $row->id is your report ID
                    },
                    'data-memorizationPlan-student_id' => function($row) {
                        return $row->student->id; // Assuming $row->student->id is your student ID
                    }
                ])
                ->rawColumns(['select','studentName','studentAge','createdByUpdatedBy','select','halaqah','fromtoLesson'])
                ->toJson();
        }


    }

    public function getHefzMonthlyPlanDetails(Request $request)
    {


        if ($request->ajax()) {
            $statusColumn = 'student_hefz_plans.status';
            $hefzTable = ',student_hefz_plans';
            $hefzTableJoinQuery = ' AND students.id = student_hefz_plans.student_id';
            $queryPart = ' AND student_hefz_plans.status = "waiting_for_approval"';
            $planWaitingApprovalStatus_condition = $queryPart;
            $archivedStatus = ' and students.deleted_at is null';


//            $application_need_action = auth()->user()->hasRole(
//                ["curriculum-specialist_2_","programs-specialist_2_","managing-director_2_","it-officer_2_","education-manager_2_"])

            $studentHefzMonthlyPlanDetails = IjazasanadMemorizationPlan::where('id', $request->get('hefz_plans_id'))->select();


            return \Yajra\DataTables\DataTables::of($studentHefzMonthlyPlanDetails)
                ->addColumn('teacherName', function ($row) {
                    return $row->center->name;

                })
               
                ->rawColumns('')
                ->toJson();

        }


    }

    public function getApplicationsWaitingApprovals(Request $request)
    {


        if ($request->ajax()) {

//            $StudentsTableStatusqueryPart = ' AND students.status != "active" AND students.status != "suspended" AND students.status !="graduated" AND students.status !="rejected"';
            $StudentsTableStatusqueryPart = ' ';

            $centerFilter = auth()->user()->hasRole(
                ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "education-manager_2_", "administrative_2_"]) ? '' :
                "AND centers.id in ( select cen_id from cen_emps where emp_id = '" . \Auth::user()->id . "') ";


            auth()->user()->hasRole(
                ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "education-manager_2_"]) ?

                $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                    ->whereRaw('admissions.status = "new_admission"')
                    ->has('programs')
                    ->with('programs')
                    ->has('center')
                    ->with('center')
                    ->has('student')
                    ->with('student')
                    ->withoutGlobalScope(OrganizationScope::class)
                    ->select()
                :

                $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                    ->whereRaw('admissions.status = "new_admission"')
                    ->whereIn('center_id', CenterEmployee::where('emp_id', auth()->user()->id)->pluck('cen_id')->toArray())
                    ->has('programs')
                    ->with('programs')
                    ->has('center')
                    ->with('center')
                    ->has('student')
                    ->with('student')
                    ->withoutGlobalScope(OrganizationScope::class)
                    ->select();


            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
                ->addColumn('programTitle', function ($row) {
                    return $row->programs->map(function ($program) {
                        return $program->title;
                    })->implode('<br>');
                })
                ->addColumn('centerName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('created_at', function ($row) {

                    return value($row['created_at'])->diffForHumans();
                })
              
                ->rawColumns(['action'])
                ->toJson();

        }
    }

    public function getMissedClockOuts(Request $request)
    {


        if ($request->ajax()) {

//            $StudentsTableStatusqueryPart = ' AND students.status != "active" AND students.status != "suspended" AND students.status !="graduated" AND students.status !="rejected"';
            $StudentsTableStatusqueryPart = ' ';

            $centerFilter = auth()->user()->hasRole(
                ["update-employee-clockout-details_2_"]) ? '' :

                $application_need_action = auth()->user()->hasRole(
                    ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "it-officer_2_", "education-manager_2_", "administrative_2_"]) ?

                    $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                        ->whereRaw('admissions.status = "new_admission"')
                        ->has('programs')
                        ->with('programs')
                        ->has('center')
                        ->with('center')
                        ->has('student')
                        ->with('student')
                        ->withoutGlobalScope(OrganizationScope::class)
                        ->select()
                    :

                    $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                        ->whereRaw('admissions.status = "new_admission"')
                        ->whereIn('center_id', CenterEmployee::where('emp_id', auth()->user()->id)->pluck('cen_id')->toArray())
                        ->has('programs')
                        ->with('programs')
                        ->has('center')
                        ->with('center')
                        ->has('student')
                        ->with('student')
                        ->withoutGlobalScope(OrganizationScope::class)
                        ->select();


            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
                ->addColumn('programTitle', function ($row) {
                    return $row->programs->map(function ($program) {
                        return $program->title;
                    })->implode('<br>');
                })
                ->addColumn('centerName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('created_at', function ($row) {

                    return value($row['created_at'])->diffForHumans();
                })
                ->addColumn('action', function ($row) {


                    $stEditRoute = route('students.edit', $row->student_id);
                    $stShowRoute = route('students.show', $row->student_id);

                    $btns = ' <a href="' . $stShowRoute . '" 
                                                                               class="btn btn-success btn-xs" title="View Center"><span
                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                                                                                    ';
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Update Student details">
//                                                                             <span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>

//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//


//
                    return $btns;
                })
                ->rawColumns(['action'])
                ->toJson();


//            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
//                ->addColumn('action', function ($row) {
//
//                    $stEditRoute = route('admission.students.edit', $row->id);
//                    $stShowRoute = route('admission.students.show', $row->id);
//
//                    $btns = ' <a href="' . $stShowRoute . '"
//                                                                               class="btn btn-success btn-xs" title="View Center"><span
//                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>
//
//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//                                                                                                                 ';
//
//
////
//                    return $btns;
//                })
//                ->rawColumns(['action'])
//                ->make(true);

        }
    }
}


//            }







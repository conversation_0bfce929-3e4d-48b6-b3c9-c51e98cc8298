<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\JobSeeker\Events\JobCategoryModifiedEvent;

/**
 * JobCategory Model
 * 
 * This model represents job categories and handles category-related operations.
 * 
 * USAGE FOR REPLACEMENT OPERATIONS:
 * 
 * // Archive a category with replacement notification
 * $category->archiveWithReplacement($replacementCategoryId);
 * 
 * // Delete a category with replacement notification  
 * $category->deleteWithReplacement($replacementCategoryId);
 * 
 * // Update a category with replacement context
 * $category->updateWithReplacement($attributes, $replacementCategoryId);
 * 
 * // Standard operations (no replacement events)
 * $category->save();
 * $category->delete();
 * 
 * @package Modules\JobSeeker\Entities
 */
final class JobCategory extends Model
{
    /**
     * The table associated with the model.
     * This is our master table for job categories.
     *
     * @var string
     */
    protected $table = 'job_categories';
    
    /**
     * The attributes that are mass assignable.
     * Only basic, safe fields are mass assignable.
     * 
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_active',
        'is_canonical',
        'is_archived',
        'parent_id'
    ];

    /**
     * The attributes that are not mass assignable.
     * These fields require explicit assignment through service layer.
     * 
     * @var array<int, string>
     */
    protected $guarded = [
        'source',
        'source_id'
    ];

    /**
     * Validation rules for category attributes
     * 
     * @var array<string, string>
     */
    public static $validationRules = [
        'name' => 'required|string|max:255',
        'slug' => 'required|string|max:255|unique:job_categories,slug',
        'description' => 'nullable|string',
        'is_active' => 'boolean',
        'is_archived' => 'boolean',
        'parent_id' => 'nullable|exists:job_categories,id',
        'is_canonical' => 'boolean',
        'source' => 'nullable|string|max:50',
        'source_id' => 'nullable|string|max:100'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'is_canonical' => 'boolean',
        'is_archived' => 'boolean',
    ];

    /**
     * The event map for the model.
     *
     * @var array
     */
    protected $dispatchesEvents = [];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($model) {
            event(new JobCategoryModifiedEvent(
                $model,
                $model->wasRecentlyCreated ? 'created' : 'updated'
            ));
        });

        static::deleted(function ($model) {
            event(new JobCategoryModifiedEvent($model, 'deleted'));
        });
    }

    /**
     * Safely update protected attributes through the service layer.
     * This method should only be called after proper validation and authorization.
     *
     * @param array<string, mixed> $attributes
     * @return bool
     */
    public function updateProtectedAttributes(array $attributes): bool
    {
        $allowedProtected = array_intersect_key($attributes, array_flip($this->guarded));
        
        if (!empty($allowedProtected)) {
            return $this->forceFill($allowedProtected)->save();
        }

        return false;
    }

    /**
     * Get the parent category if this is a mapped category.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(JobCategory::class, 'parent_id');
    }

    /**
     * Get all child categories mapped to this canonical category.
     */
    public function children(): HasMany
    {
        return $this->hasMany(JobCategory::class, 'parent_id');
    }

    /**
     * Get the jobs associated with this category.
     * Uses the pivot table job_category_pivot for the relationship.
     */
    public function jobs(): BelongsToMany
    {
        return $this->belongsToMany(
            Job::class,
            'job_category_pivot', // Using our clearly named pivot table
            'category_id',
            'job_id'
        )->withTimestamps();
    }

    /**
     * The notification setups that belong to this category.
     */
    public function notificationSetups(): BelongsToMany
    {
        return $this->belongsToMany(
            JobNotificationSetup::class,
            'job_notification_category',
            'category_id',
            'setup_id'
        );
    }

    /**
     * Scope a query to only include canonical categories.
     */
    public function scopeCanonical($query)
    {
        return $query->where('is_canonical', true);
    }

    /**
     * Scope a query to include only active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to exclude archived categories.
     */
    public function scopeNotArchived($query)
    {
        return $query->where('is_archived', false);
    }

    /**
     * Find a category by its source and source_id.
     */
    public static function findBySource(string $source, string $sourceId)
    {
        return self::where('source', $source)
            ->where('source_id', $sourceId)
            ->first();
    }

    /**
     * Get the action for the current event
     * 
     * @return string
     */
    protected function getEventAction(): string
    {
        if ($this->wasRecentlyCreated) {
            return 'created';
        }
        
        if ($this->wasChanged(['is_archived']) && $this->is_archived) {
            return 'archived';
        }
        
        if ($this->wasChanged(['is_active', 'is_canonical', 'name'])) {
            return 'updated';
        }
        
        return 'deleted';
    }

    /**
     * Override the dispatchEvent method to include the action
     *
     * @param string $event
     * @return void
     */
    protected function dispatchEvent($event)
    {
        if (isset($this->dispatchesEvents[$event])) {
            $eventClass = $this->dispatchesEvents[$event];
            $action = $this->getEventAction();
            
            // Get additional context
            $context = $this->getEventContext();
            
            // Fire event without replacement category ID (standard behavior)
            event(new $eventClass($this, $action, null, $context));
        }
    }

    /**
     * Get additional context for the event
     * 
     * @return array
     */
    protected function getEventContext(): array
    {
        return [
            'changed_attributes' => $this->getChanges(),
            'original_attributes' => $this->getOriginal(),
        ];
    }

    /**
     * Archive the category with a replacement category ID
     * This method explicitly handles archival with replacement notification
     * 
     * @param int|null $replacementCategoryId ID of the replacement category
     * @return bool
     */
    public function archiveWithReplacement(?int $replacementCategoryId = null): bool
    {
        $this->is_archived = true;
        $result = $this->save();
        
        if ($result && $replacementCategoryId) {
            // Fire a custom event with the replacement category ID
            $this->fireArchiveWithReplacementEvent($replacementCategoryId);
        }
        
        return $result;
    }

    /**
     * Delete the category with a replacement category ID
     * This method explicitly handles deletion with replacement notification
     * 
     * @param int|null $replacementCategoryId ID of the replacement category
     * @return bool
     */
    public function deleteWithReplacement(?int $replacementCategoryId = null): bool
    {
        if ($replacementCategoryId) {
            // Fire a custom event before deletion with the replacement category ID
            $this->fireDeleteWithReplacementEvent($replacementCategoryId);
        }
        
        return $this->delete();
    }

    /**
     * Fire archive event with replacement category ID
     * 
     * @param int $replacementCategoryId
     * @return void
     */
    protected function fireArchiveWithReplacementEvent(int $replacementCategoryId): void
    {
        $context = $this->getEventContext();
        $context['has_replacement'] = true;
        
        event(new JobCategoryModifiedEvent($this, 'archived', $replacementCategoryId, $context));
    }

    /**
     * Fire delete event with replacement category ID
     * 
     * @param int $replacementCategoryId
     * @return void
     */
    protected function fireDeleteWithReplacementEvent(int $replacementCategoryId): void
    {
        $context = $this->getEventContext();
        $context['has_replacement'] = true;
        
        event(new JobCategoryModifiedEvent($this, 'deleted', $replacementCategoryId, $context));
    }

    /**
     * Update the category and optionally fire event with replacement context
     * 
     * @param array $attributes Attributes to update
     * @param int|null $replacementCategoryId Optional replacement category for complex updates
     * @return bool
     */
    public function updateWithReplacement(array $attributes, ?int $replacementCategoryId = null): bool
    {
        $this->fill($attributes);
        $result = $this->save();
        
        if ($result && $replacementCategoryId) {
            // Fire a custom event with the replacement category ID
            $this->fireUpdateWithReplacementEvent($replacementCategoryId);
        }
        
        return $result;
    }

    /**
     * Fire update event with replacement category ID
     * 
     * @param int $replacementCategoryId
     * @return void
     */
    protected function fireUpdateWithReplacementEvent(int $replacementCategoryId): void
    {
        $context = $this->getEventContext();
        $context['has_replacement'] = true;
        
        event(new JobCategoryModifiedEvent($this, 'updated', $replacementCategoryId, $context));
    }
} 
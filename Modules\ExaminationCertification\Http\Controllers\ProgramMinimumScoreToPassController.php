<?php

namespace Modules\ExaminationCertification\Http\Controllers;

use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\ExaminationCertification\Http\Requests\ClassReportStaatisticsRequest;
use Modules\ExaminationCertification\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class ProgramMinimumScoreToPassController extends Controller
{

    public function __construct()
    {
        $this->middleware('writeCurrentClassReportOnly', ['only' => ['create']]);
    }



    public function __invoke(ClassReportStaatisticsRequest $request, $id)
    {

        try {


            if ($id) {

                $planYearMonth = Carbon::parse($request->get('classDate'))->format('Y-m');


                $dateMonthArray = explode('-', $planYearMonth);
                $year = $dateMonthArray[0];
                $month = $dateMonthArray[1];

                $classTotalDays = StudentHefzReport::where('student_id', $request->get('studentId'))
                    ->where('class_id', $id)
                    ->whereYear('class_time', $year)
                    ->whereMonth('class_time', $month)->count();

                $attendanceDaysCount = StudentHefzReport::where('student_id', $request->get('studentId'))
                    ->where('class_id', $id)
                    ->whereYear('class_time', $year)
                    ->whereMonth('class_time', $month)
                    ->whereIn('attendance_id', [2/** on-time */, 1/** late */])->count();

                if ($attendanceDaysCount == 0 || $classTotalDays == 0) {
                    $attendancePercentage = '';
                } else {

                    $attendancePercentage = round($attendanceDaysCount / $classTotalDays * 100);

                }


                $lastPageNumberMemorized = StudentHefzReport::
                where('class_id', $id)
                    ->where('student_id', $request->get('studentId'))
                    ->whereYear('class_time', $year)
                    ->whereMonth('class_time', $month)->orderBy('hefz_to_surat', 'desc')->limit(1)->get(['hefz_to_surat', 'hefz_to_ayat'])->first();


                $lastPageNumberMemorized = DB::select(DB::raw("
select page_number
from moshaf_pages
where surah_id = :startSurahId
  and (first_ayah >= :lastAyah or last_ayah >= :lastAyah2)
limit 1;"), array(
                    'startSurahId' => $lastPageNumberMemorized->hefz_to_surat,
                    'lastAyah' => $lastPageNumberMemorized->hefz_to_ayat,
                    'lastAyah2' => $lastPageNumberMemorized->hefz_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                ));

                $lastPageNumberMemorized = $lastPageNumberMemorized[0]->page_number;


                return response()->json(
                    ['lastPageNumberMemorized' => $lastPageNumberMemorized, 'attendancePercentage' => $attendancePercentage, 'attendanceDaysCount' => $attendanceDaysCount]);


            }


        } catch (\Exception $exception) {
            \Log::error($exception);
            return response()->json($exception->getMessage());

        }


        dd('only ajax requests are allowed');


    }


}

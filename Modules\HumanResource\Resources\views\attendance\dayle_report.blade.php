@extends('layouts.hound')

@section('mytitle', 'Daily Attendance Report')

@section('content')

    <div class="row">
        <div class="col-md-12">
        </div>
    </div>

    <div class="panel-heading">
        <h4>
            <a class=" pull-right" href="{{ url('/workplace/humanresource/attendance') }}" title="Back">
                <button class="btn btn-warning btn-xs txt-light"><i class="fa fa-arrow-left txt-light"
                                                                    aria-hidden="true"></i> Back
                </button>
            </a>
            </br>
        </h4>
    </div>

    <div class="col-sm-4">

    </div>


    {!! Form::open(['url' => route('attendance.daily_report_search'), 'class' => 'form-horizontal', 'method' => 'get']) !!}
    <div class="col-sm-3">
        <select name="role" id="role" class="form-control">


            <option value="0">All Rolles</option>
            @foreach ($role as $item)
                <option value="{{$item->id}}">{{$item->name}}</option>
            @endforeach

        </select>
    </div>

    <div class="col-sm-4  mb-15">
        <div class="input-group">

            <button type="submit" class="btn btn-success btn-anim"><i class="icon-rocket"></i><span class="btn-text">Search</span>
            </button>

        </div>

    </div>

    {!! Form::close() !!}




    <div class="result-set">

        <table class="table table-bordered table-striped table-hover" id="data-table">
            <thead>
            <tr>

                <th> Employee Name</th>
                <th>In</th>
                <th>Out</th>
                <th>Number of Hours</th>


            </tr>

            </thead>
            <tbody>
            <?php $employeeid = null;?>

            @if(isset($attendance))
                @foreach($attendance as $item)
                    <tr>
                        <td>
                            <?php if ($employeeid <> $item->employee_id){?>
                            {{ \App\Employee::where(['id'=>$item->employee_id])->pluck('name')->first() }}
                            <?php }
                            $employeeid = $item->employee_id; ?>
                        </td>
                        <td>@if($item->type=='in') <span
                                    class="btn-success">  *{{$item->clock->format('h:iA') }}</span><?php $in = Carbon\Carbon::parse($item->clock); ?> @endif
                        </td>
                        <td>@if($item->type=='out') <span
                                    class="btn-warning">* {{$item->clock->format('h:iA') }}</span><?php $out = Carbon\Carbon::parse($item->clock); ?>
                        </td>
                        <td> <?php $hours = $in->diffInHours($out);
                            $seconds = $in->diffInMinutes($out);
                            echo $hours . ':' . $seconds % 60; ?> </td>
                        @endif

                    </tr>
                @endforeach
            @else

                @foreach ($employee as $it)
                    <?php $attendance = \App\Attendance::where('employee_id', '=', $it->id)->whereDate('created_at', date('Y-m-d'))->orderBy('employee_id')->get();?>
                    @foreach($attendance as $item)
                        <tr>
                            <td>
                                <?php if ($employeeid <> $item->employee_id){?>
                                {{ \App\Employee::where(['id'=>$item->employee_id])->pluck('name')->first() }}
                                <?php }
                                $employeeid = $item->employee_id; ?>
                            </td>
                            <td>@if($item->type=='in') <span
                                        class="btn-success">  *{{$item->clock->format('h:iA') }}</span><?php $in = Carbon\Carbon::parse($item->clock); ?> @endif
                            </td>
                            <td>@if($item->type=='out') <span
                                        class="btn-warning">* {{$item->clock->format('h:iA') }}</span><?php $out = Carbon\Carbon::parse($item->clock); ?>
                            </td>
                            <td> <?php $hours = $in->diffInHours($out);
                                $seconds = $in->diffInMinutes($out);
                                echo $hours . ':' . $seconds % 60; ?> </td>
                            @endif

                        </tr>
                    @endforeach
                @endforeach
            @endif
            </tbody>
        </table>

        <div class="text-center">
        </div>
    </div>


@endsection


   
    
<?php

declare(strict_types=1);

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static string getStudentImageUrl($student, ?string $size = null)
 * @method static string getStudentImageHtml($student, array $attributes = [])
 * @method static \App\Services\StudentImageService setDefaultImagePath(string $path)
 * 
 * @see \App\Services\StudentImageService
 */
class StudentImage extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return 'student-image';
    }
} 
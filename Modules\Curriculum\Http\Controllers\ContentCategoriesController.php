<?php

namespace Modules\Curriculum\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\ContentCategory;
use Illuminate\Http\Request;
use Session;

class ContentCategoriesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $keyword = $request->get('search');
        $perPage = 25;

        if (!empty($keyword)) {
            $contentcategories = ContentCategory::where('title', 'LIKE', "%$keyword%")
				->orWhere('status', 'LIKE', "%$keyword%")
				->paginate($perPage);
        } else {
            $content_categories = ContentCategory::paginate($perPage);
        }

        return view('curriculum::content_categories.index', compact('content_categories'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('curriculum::content_categories.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        
        $requestData = $request->all();
        
        ContentCategory::create($requestData);

        Session::flash('flash_message', 'ContentCategory added!');

        return redirect('workplace/curriculum/content_categories');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $content_category = ContentCategory::findOrFail($id);

        return view('curriculum::content_categories.show', compact('content_category'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $content_category = ContentCategory::findOrFail($id);

        return view('curriculum::content_categories.edit', compact('content_category'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {
        
        $requestData = $request->all();
        
        $contentcategory = ContentCategory::findOrFail($id);
        $contentcategory->update($requestData);

        Session::flash('flash_message', 'ContentCategory updated!');

        return redirect('workplace/curriculum/content_categories');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        ContentCategory::destroy($id);

        Session::flash('flash_message', 'ContentCategory deleted!');

        return redirect('workplace/curriculum/content-categories');
    }
}

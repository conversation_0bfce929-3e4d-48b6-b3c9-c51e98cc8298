<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;
use Modules\Leave\Entities\LeaveDefine;

class LeaveType extends Model
{

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
    }


    public function leaveDefine()
    {
        return $this->hasMany(LeaveDefine::class,'leave_type_id','id');
    }

    function leaveRequest()
    {


        return $this->hasMany(LeaveRequest::class,'type_id');
    }

    public function employee()
    {
        return $this->belongsToMany(Employee::class, 'leave_defines','leave_type_id', 'employee_id');
    }
    //
}

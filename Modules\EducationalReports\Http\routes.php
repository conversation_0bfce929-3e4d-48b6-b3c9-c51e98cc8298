<?php

Route::group(['middleware' => ['web', 'auth:employee','missedClockOutMiddleware'], 'prefix' => 'workplace/educationalreports', 'namespace' => 'Modules\EducationalReports\Http\Controllers'], function () {



    Route::get('student-tables-pdf/student/{studentId?}/class/{classId?}/date/{monthYear?}', 'StudentTablesPDFController@downloadPDF')->name('student-tables-pdf');
    Route::get('student-table/memorization-pdf/student/{studentId?}/class/{classId?}/date/{monthYear?}', 'StudentTableMemorizationPDFController@downloadPDF')->name('student-memorization-table-pdf');
    Route::get('student-table/revision-pdf/student/{studentId?}/class/{classId?}/date/{monthYear?}', 'StudentTableRevisionPDFController@downloadPDF')->name('student-revision-table-pdf');
    Route::get('student-table/month-end-summary-pdf/student/{studentId?}/class/{classId?}/date/{monthYear?}', 'StudentTableMonthEndPDFController@downloadPDF')->name('student-month-end-summary-table-pdf');
    Route::get('center-tables-pdf/center/{centerId?}/date/{monthYear?}', 'CenterTablesPDFController@downloadPDF')->name('center-tables-pdf');
    Route::get('class-tables-pdf/class/{classId?}/center/{centerId?}/date/{monthYear?}', 'ClassTablesPDFController@downloadPDF')->name('class-tables-pdf');
    Route::get('itqan-tables-pdf/date/{monthYear?}', 'ItqanTablesPDFController@downloadPDF')->name('itqan-tables-pdf');



    // student login report
    Route::get('studentsloginreport', ['as' => 'student_login_report', 'uses' => 'StudentLoginReportController@studentLoginReport'])->middleware('userRolePermission:379');
    Route::post('studentsloginreport', ['as' => 'student_login_search', 'uses' => 'StudentLoginReportController@studentLoginSearch']);
    Route::get('studentsloginreport', ['as' => 'student_login_search', 'uses' => 'StudentLoginReportController@studentLoginReport']);
    Route::post('student-login-search', ['as' => 'student_login_search', 'uses' => 'StudentLoginReportController@studentLoginSearch']);
    Route::get('student-login-search', ['as' => 'student_login_search', 'uses' => 'StudentLoginReportController@studentLoginReport']);
    Route::post('reset-student-password', 'ResetStudentPasswordController@resetStudentPassword')->name('reset-student-password');


    //        //guardian report
        Route::get('guardianreport', ['as' => 'guardian_report', 'uses' => 'GuardianReportController@guardianReport']);
        Route::post('guardian-report-search', ['as' => 'guardian_report_search', 'uses' => 'GuardianReportController@guardianReportSearch']);
        Route::get('guardian-report-search', ['as' => 'guardian_report_search', 'uses' => 'GuardianReportController@guardianReport']);

    Route::get('studentattendancereport', 'StudentAttendanceReportController@studentAttendanceReport')->name('reports.studentattendancereport');
    Route::get('halaqahreport', 'HalaqahReportsController@getReport')->name('halaqah.reports');
    Route::post('student-attendance-report-search', ['as' => 'reports.student_attendance_report_search', 'uses' => 'StudentAttendanceReportController@studentAttendanceReportSearch']);
    Route::get('student-attendance/print/{class_id}/{month}/{year}', 'StudentAttendanceReportController@studentAttendanceReportPrint')->name('student-attendance-print');


    //statistics
    Route::get('halaqahreport-statistics/{class_id?}/', 'HalaqahReportsController@hefzLevelsRecordsStatistics');

});




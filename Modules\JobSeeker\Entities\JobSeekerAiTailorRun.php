<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * JobSeekerAiTailorRun tracks a single AI resume tailoring session from start to completion.
 * 
 * Purpose: Orchestrate and audit the 10-step LLM pipeline for resume tailoring.
 * Relationships: belongs to JobSeeker and Job; has many artifacts; links to selected resumes.
 * Business rules: unique correlation_id for tracing; status progression; guardrail flag storage.
 * Performance: tracks token usage and processing time for optimization.
 * 
 * @property int $id
 * @property int $job_seeker_id
 * @property int|null $job_id
 * @property string|null $job_slug
 * @property string $correlation_id Unique identifier for request tracing
 * @property string|null $model LLM model used (e.g., 'gpt-4', 'claude-3')
 * @property string $status pending|running|succeeded|failed
 * @property array|null $guardrail_flags JSON array of safety flags raised
 * @property int|null $total_tokens_input Total input tokens across all steps
 * @property int|null $total_tokens_output Total output tokens across all steps
 * @property int|null $processing_time_ms Total processing time in milliseconds
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
final class JobSeekerAiTailorRun extends Model
{
    protected $table = 'jobseeker_ai_tailor_runs';

    protected $fillable = [
        'job_seeker_id',
        'job_id',
        'job_slug',
        'correlation_id',
        'model',
        'status',
        'guardrail_flags',
        'total_tokens_input',
        'total_tokens_output',
        'processing_time_ms',
    ];

    protected $casts = [
        'job_seeker_id' => 'integer',
        'job_id' => 'integer',
        'guardrail_flags' => 'array',
        'total_tokens_input' => 'integer',
        'total_tokens_output' => 'integer',
        'processing_time_ms' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'pending',
    ];

    /**
     * Get the job seeker who initiated this run
     */
    public function jobSeeker(): BelongsTo
    {
        return $this->belongsTo(JobSeeker::class, 'job_seeker_id');
    }

    /**
     * Get the target job for this tailoring run
     */
    public function job(): BelongsTo
    {
        return $this->belongsTo(Job::class, 'job_id');
    }

    /**
     * Get the resumes selected for this tailoring run
     */
    public function resumes(): BelongsToMany
    {
        return $this->belongsToMany(
            JobSeekerResume::class,
            'jobseeker_ai_tailor_run_resumes',
            'tailor_run_id',
            'resume_id'
        )->withPivot('position_order')->withTimestamps()->orderBy('position_order');
    }

    /**
     * Get all artifacts generated during this run
     */
    public function artifacts(): HasMany
    {
        return $this->hasMany(JobSeekerAiTailorArtifact::class, 'tailor_run_id')
                    ->orderBy('step_number');
    }

    /**
     * Generate a unique correlation ID for request tracing
     */
    public static function generateCorrelationId(): string
    {
        return 'tailor_' . Str::uuid()->toString();
    }

    /**
     * Mark the run as started
     */
    public function markAsRunning(): void
    {
        $this->update(['status' => 'running']);
        
        Log::info('AI tailor run marked as running', [
            'run_id' => $this->id,
            'correlation_id' => $this->correlation_id,
            'job_seeker_id' => $this->job_seeker_id,
        ]);
    }

    /**
     * Mark the run as successfully completed
     */
    public function markAsSucceeded(int $totalTokensInput = null, int $totalTokensOutput = null, int $processingTimeMs = null): void
    {
        $updates = ['status' => 'succeeded'];
        
        if ($totalTokensInput !== null) {
            $updates['total_tokens_input'] = $totalTokensInput;
        }
        
        if ($totalTokensOutput !== null) {
            $updates['total_tokens_output'] = $totalTokensOutput;
        }
        
        if ($processingTimeMs !== null) {
            $updates['processing_time_ms'] = $processingTimeMs;
        }
        
        $this->update($updates);
        
        Log::info('AI tailor run completed successfully', [
            'run_id' => $this->id,
            'correlation_id' => $this->correlation_id,
            'job_seeker_id' => $this->job_seeker_id,
            'total_tokens_input' => $this->total_tokens_input,
            'total_tokens_output' => $this->total_tokens_output,
            'processing_time_ms' => $this->processing_time_ms,
        ]);
    }

    /**
     * Mark the run as failed with error details
     */
    public function markAsFailed(string $errorMessage, array $guardrailFlags = []): void
    {
        $updates = [
            'status' => 'failed',
            'guardrail_flags' => array_merge($this->guardrail_flags ?? [], $guardrailFlags),
        ];
        
        $this->update($updates);
        
        Log::error('AI tailor run failed', [
            'run_id' => $this->id,
            'correlation_id' => $this->correlation_id,
            'job_seeker_id' => $this->job_seeker_id,
            'error' => $errorMessage,
            'guardrail_flags' => $updates['guardrail_flags'],
        ]);
    }

    /**
     * Add guardrail flags without changing status
     */
    public function addGuardrailFlags(array $flags): void
    {
        $currentFlags = $this->guardrail_flags ?? [];
        $updatedFlags = array_merge($currentFlags, $flags);
        
        $this->update(['guardrail_flags' => $updatedFlags]);
        
        Log::warning('Guardrail flags added to AI tailor run', [
            'run_id' => $this->id,
            'correlation_id' => $this->correlation_id,
            'new_flags' => $flags,
            'all_flags' => $updatedFlags,
        ]);
    }

    /**
     * Check if the run is in a terminal state
     */
    public function isTerminal(): bool
    {
        return in_array($this->status, ['succeeded', 'failed']);
    }

    /**
     * Check if the run is currently processing
     */
    public function isProcessing(): bool
    {
        return $this->status === 'running';
    }

    /**
     * Check if the run completed successfully
     */
    public function isSuccessful(): bool
    {
        return $this->status === 'succeeded';
    }

    /**
     * Check if the run failed
     */
    public function hasFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Get formatted processing time for display
     */
    public function getFormattedProcessingTimeAttribute(): string
    {
        if (!$this->processing_time_ms) {
            return 'Unknown';
        }

        $seconds = $this->processing_time_ms / 1000;
        
        if ($seconds < 60) {
            return round($seconds, 1) . 's';
        }
        
        $minutes = floor($seconds / 60);
        $remainingSeconds = $seconds % 60;
        
        return "{$minutes}m " . round($remainingSeconds, 1) . 's';
    }

    /**
     * Get the cost estimate based on token usage (rough estimate)
     */
    public function getEstimatedCostAttribute(): float
    {
        if (!$this->total_tokens_input || !$this->total_tokens_output) {
            return 0.0;
        }

        // Rough cost estimates (these would be configurable in a real implementation)
        $inputCostPer1k = 0.01;  // $0.01 per 1k input tokens
        $outputCostPer1k = 0.03; // $0.03 per 1k output tokens
        
        $inputCost = ($this->total_tokens_input / 1000) * $inputCostPer1k;
        $outputCost = ($this->total_tokens_output / 1000) * $outputCostPer1k;
        
        return round($inputCost + $outputCost, 4);
    }

    /**
     * Scope to find runs by job seeker
     */
    public function scopeForJobSeeker($query, int $jobSeekerId)
    {
        return $query->where('job_seeker_id', $jobSeekerId)
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Scope to find recent runs (last 30 days)
     */
    public function scopeRecent($query)
    {
        return $query->where('created_at', '>=', now()->subDays(30));
    }

    /**
     * Scope to find successful runs only
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'succeeded');
    }

    /**
     * Scope to find failed runs only
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }
}

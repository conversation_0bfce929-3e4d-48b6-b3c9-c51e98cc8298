# JobSeeker: Unit Economics & Profitability Model

**Document Purpose:** To provide a foundational model for understanding the unit economics, costs, and potential profitability of the JobSeeker product. This is a strategic tool for financial planning and decision-making.

**Disclaimer:** This model is based on a series of informed assumptions. All figures should be treated as estimates to be refined as real-world data becomes available.

---

## 1. Revenue Model: The Freemium Subscription

Our business model is Freemium. The core value is split between free and paid tiers.

*   **Free Tier:** Access to the job board, creation of one basic notification alert. This drives user acquisition.
*   **Premium Tier ("Pro"):** The primary revenue driver. This unlocks the true value of the platform.
    *   Unlimited, highly-customizable Notification Setups.
    *   Access to the **AI Resume Optimizer ("Intelli-Resume")**.
    *   Company Watchlist.
    *   Advanced analytics and insights (Future Horizon).

**Assumption: Pricing**
*   We will set the price for the **Pro Tier at $5.00 USD per month**. This price point is chosen to be accessible while reflecting the high value provided.

---

## 2. Unit Economics: The Single Pro Subscriber

Here, we analyze the profitability of a single paying customer over one month.

| Line Item                  | Amount (USD) | Notes                                                                                                                            |
| -------------------------- | :----------: | -------------------------------------------------------------------------------------------------------------------------------- | 
| **Monthly Subscription Fee** |   **$5.00**  | Our gross revenue per user.                                                                                                      |
| **Less: Cost of Goods Sold (COGS)** |              | Costs directly tied to delivering the service to this one user.                                                                  |
| *Payment Gateway Fee*      |   ($0.13)    | **Assumption:** A blended rate of **2.5%** of the transaction value, based on research of the Afghan financial landscape.          |
| *AI API Costs*             |   ($0.15)    | **Assumption:** A Pro user generates 5 AI resumes per month. At an estimated $0.03/resume, this is a key variable cost.        |
| *Server/Infrastructure Costs* |   ($0.05)    | **Assumption:** Pro-rated cost of servers, databases, and email delivery services (e.g., AWS, SendGrid) per user.             |
| **Gross Profit per User**  |   **$4.67**  | Profit after delivering the service.                                                                                             |
| **Gross Margin**           |   **93.4%**  | (Gross Profit / Revenue). A very healthy margin, typical for software products.                                                  |

---

## 3. Business Profitability: A Monthly Projection

This section scales the unit economics to a business-level view, incorporating fixed operational costs. This model is based on reaching a target of **1,000 Pro Subscribers**.

| Line Item                  | Monthly Cost (USD) | Notes                                                                                                                            |
| -------------------------- | :----------------: | -------------------------------------------------------------------------------------------------------------------------------- | 
| **Total Revenue**          |     **$5,000**     | 1,000 Pro Subscribers @ $5.00/month.                                                                                             |
| **Less: Total COGS**       |      **($330)**     | 1,000 users * ($0.13 + $0.15 + $0.05).                                                                                             |
| **Gross Profit**           |     **$4,670**     | The total profit from all paying customers.                                                                                      |
|                            |                    |                                                                                                                                  |
| **Less: Operating Expenses (OPEX)** |                    | Fixed costs required to run the business, regardless of user count.                                                              |
| *Salaries*                 |     ($3,000)       | **Assumption:** A lean team (e.g., 1-2 core members). This is the largest and most critical expense.                            |
| *Marketing & Sales*        |      ($500)        | **Assumption:** Initial budget for digital marketing, social media campaigns, and content creation to drive user acquisition.      |
| *Miscellaneous*            |      ($250)        | **Assumption:** Covers software licenses, legal, accounting, and other administrative overhead.                                  |
| **Total OPEX**             |     **($3,750)**     |                                                                                                                                  |
|                            |                    |                                                                                                                                  |
| **Net Profit / (Loss)**    |      **$920**      | **(Gross Profit - Total OPEX).** This is the final, bottom-line profitability of the business for the month.                     |

---

## 4. Key Takeaways & Strategic Implications

1.  **High Gross Margin:** The core product is highly profitable on a per-user basis (93.4%). This is excellent news and typical of a strong SaaS business.

2.  **Salaries are the Key Driver:** Profitability is overwhelmingly dependent on managing the fixed cost of salaries. The business can achieve breakeven and profitability with a lean, focused team.

3.  **Scalability is High:** Because the gross margin is so high, each new subscriber after the breakeven point contributes significantly to net profit. The primary challenge is not cost, but **user acquisition**.

4.  **Variable Costs to Monitor:**
    *   **AI API Costs:** This is the most significant variable cost. If users generate more resumes than expected, this cost will rise. Usage must be monitored closely.
    *   **Payment Gateway Fees:** While modeled at 2.5%, securing a lower rate through negotiation with a local provider like HesabPay or AIB could directly improve the bottom line.

**Conclusion:** The JobSeeker business model is financially viable and has a clear path to profitability. The primary strategic focus must be on **acquiring and retaining Pro subscribers** while maintaining a lean operational structure. The unit economics are strong, providing a solid foundation for sustainable growth.

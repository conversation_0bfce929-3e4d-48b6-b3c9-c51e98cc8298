<?php

namespace App\Http\Middleware;

use Auth;
use Closure;
use Session;
use App\User;

class ParentMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {



//        dd( User::where('verified', 1)->role('parent')->get());
//        \Auth::guard("web")->user()->hasRole("parent") == false
//        dd(Auth::user()->roles()->get());

//        Session::flush();
//        session_start();
//        $role_id = Session::get('role_id');
        session(['role_id' => Auth::user()->roles->where("name","parent")->pluck('id')->toArray()[0]]);
        $role_id = $request->session()->get('role_id');





        if (\Auth::guard("web")->user()->hasRole("parent")) {
            return $next($request);
        } elseif (\Auth::guard("web")->user()->hasRole("student") /* 23 = student */ AND !is_null(session("impersonated_by"))) { // this condition is specially used when a parent impersonate a student
            return redirect('student-dashboard');
//            return $next($request);
        }elseif ($role_id != "") {
            return redirect('parent-dashboard');
        } else {
            return redirect('login');
        }
    }
}

# Itqan Al Quran - Modules Overview

This document provides detailed information about each module in the Itqan Al Quran system, their purpose, key features, and important components.

## Module Structure

The Itqan Al Quran system is built using the nwidart/laravel-modules package, which provides a modular architecture for Laravel applications. Each module is a self-contained unit with its own controllers, models, views, and routes.

## Key Modules

### Admission Module

**Purpose:** Manages the student admission process.

**Key Features:**
- Student registration and enrollment
- Application processing
- Document management
- Interview scheduling
- Admission status tracking

**Important Components:**
- `Entities/Student.php` - Student model
- `Entities/AdmissionApplication.php` - Application model
- `Http/Controllers/AdmissionController.php` - Main controller
- `Services/AdmissionService.php` - Business logic

### Authentication Module

**Purpose:** Handles user authentication and authorization.

**Key Features:**
- User login/logout
- Password reset
- Role and permission management
- Session management
- Security features

**Important Components:**
- `Entities/User.php` - User model
- `Entities/Role.php` - Role model
- `Entities/Permission.php` - Permission model
- `Http/Controllers/AuthController.php` - Authentication controller

### Communication Module

**Purpose:** Manages communication between users, including notifications and messaging.

**Key Features:**
- Email notifications
- SMS messaging
- Internal messaging system
- Announcement broadcasts
- Communication preferences

**Important Components:**
- `Entities/Message.php` - Message model
- `Entities/Notification.php` - Notification model
- `Services/EmailService.php` - Email handling service
- `Services/SmsService.php` - SMS handling service

### Education Module

**Purpose:** Core module for educational features and student learning management.

**Key Features:**
- Course management
- Curriculum tracking
- Grading and assessment
- Student progress monitoring
- Attendance tracking
- Reports and analytics

**Important Components:**
- `Entities/Course.php` - Course model
- `Entities/Lesson.php` - Lesson model
- `Entities/Grade.php` - Grade model
- `Entities/Attendance.php` - Attendance model
- `Services/ReportService.php` - Reporting service

### General Module

**Purpose:** Provides common functionality used across the system.

**Key Features:**
- Settings management
- System configuration
- Dashboard components
- Shared utilities
- Common interfaces

**Important Components:**
- `Entities/Setting.php` - Setting model
- `Services/SettingService.php` - Settings management
- `Http/Controllers/DashboardController.php` - Dashboard controller

### HumanResource Module

**Purpose:** Manages staff, teachers, and HR functions.

**Key Features:**
- Employee management
- Payroll
- Attendance tracking
- Performance reviews
- Contract management

**Important Components:**
- `Entities/Employee.php` - Employee model
- `Entities/Contract.php` - Contract model
- `Entities/PayrollRecord.php` - Payroll record model
- `Services/EmployeeService.php` - Employee management service

### JobSeeker Module

**Purpose:** Manages job seekers, job postings, and employment applications.

**Key Features:**
- Job seeker profiles
- Job postings and applications
- Resume/CV management
- Interview scheduling
- Email notifications for new jobs
- Admin control board for email management

**Important Components:**
- `Entities/JobSeeker.php` - JobSeeker model
- `Entities/Job.php` - Job model
- `Entities/JobApplication.php` - Job application model
- `Http/Controllers/JobController.php` - Job management controller
- `Http/Controllers/EmailControlBoardController.php` - Email control and administration

### MajorTrack Module

**Purpose:** Tracks student major specializations and academic pathways.

**Key Features:**
- Major selection and tracking
- Academic pathway management
- Specialization requirements
- Student academic planning
- Major change management

**Important Components:**
- `Entities/Major.php` - Major model
- `Entities/AcademicPathway.php` - Academic pathway model
- `Services/MajorTrackingService.php` - Major tracking service

### Transportation Module

**Purpose:** Manages transportation services for students and staff.

**Key Features:**
- Route management
- Vehicle tracking
- Schedule management
- Driver assignments
- Transportation requests

**Important Components:**
- `Entities/Route.php` - Route model
- `Entities/Vehicle.php` - Vehicle model
- `Entities/Driver.php` - Driver model
- `Entities/Schedule.php` - Schedule model
- `Services/RouteService.php` - Route management service

## Module Interactions

Modules interact through well-defined service interfaces and Laravel's service container. These interactions follow these principles:

1. **Loose Coupling** - Modules are designed to be loosely coupled to ensure maintainability
2. **Service Interfaces** - Modules expose services through interfaces
3. **Event-Driven Communication** - Modules communicate through Laravel events when appropriate
4. **Dependency Injection** - Services are injected where needed rather than directly instantiated

## View Management

A critical convention in this project is that module views are NOT stored in the standard Laravel Modules location. Instead, they are located in:
```
resources/views/modules/{module-name-lowercase}/
```

Each module's service provider is configured to load views from this custom path. 
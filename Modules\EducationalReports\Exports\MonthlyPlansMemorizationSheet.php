<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\Student;
use App\StudentHefzPlan;
use App\Classes;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

final class MonthlyPlansMemorizationSheet implements WithTitle, WithStyles, WithEvents
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get memorization plans data - includes ALL students with active plans
     */
    private function getMemorizationPlans(): Collection
    {
		$classIds = $this->filters['classIds'] ?? [$this->filters['classId']];
		$year = $this->filters['year'];
		$month = $this->filters['month'];
		$studentId = $this->filters['studentId'] ?? null;

		$planYearMonth = sprintf('%d-%02d', $year, $month);

		// Map classes for quick lookup (center, name, teachers, program)
		$classes = Classes::with(['center', 'programs', 'teachers'])
			->whereIn('id', $classIds)
			->get()
			->keyBy('id');

		// Get all enrolled students per class (active membership)
		$enrollmentsQuery = DB::table('class_students as cs')
			->join('students as s', 's.id', '=', 'cs.student_id')
			->whereIn('cs.class_id', $classIds)
			->whereNull('cs.deleted_at')
			->whereNull('cs.end_date')
			->select('cs.class_id', 's.id as student_id', 's.full_name');
		if ($studentId) {
			$enrollmentsQuery->where('s.id', (int) $studentId);
		}
		$enrollments = $enrollmentsQuery->get();

		// Prefetch all active plans for month for these students/classes
		$plans = StudentHefzPlan::with(['halaqah.center', 'halaqah.programs', 'halaqah.teachers', 'fromSurat', 'toSurat'])
			->whereIn('class_id', $classIds)
			->where('plan_year_and_month', $planYearMonth)
			->where('status', 'active')
			->when($studentId, function ($q) use ($studentId) {
				$q->where('student_id', (int) $studentId);
			})
			->get();

		$planByKey = [];
		foreach ($plans as $p) {
			$planByKey[$p->student_id . ':' . $p->class_id] = $p;
		}

		$results = collect();

		foreach ($enrollments as $row) {
			$classId = (int) $row->class_id;
			$studentIdVal = (int) $row->student_id;
			$studentName = $row->full_name ?? 'N/A';
			$cls = $classes->get($classId);
			$classProgram = $cls?->programs?->first()?->title ?? 'N/A';
			$teacherNames = $cls?->teachers?->pluck('full_name')?->join(', ') ?: 'N/A';
			$className = $cls?->name ?? ($cls?->class_code ?? 'N/A');

			$key = $studentIdVal . ':' . $classId;
			$plan = $planByKey[$key] ?? null;

			if ($plan) {
				$numberOfPages = $this->calculatePages($plan);
				$results->push([
					'centre_id' => $plan->halaqah->center->id ?? ($cls?->center?->id ?? 'N/A'),
					'centre_name' => $plan->halaqah->center->name ?? ($cls?->center?->name ?? 'N/A'),
					'class_id' => $classId,
					'class_name' => $className,
					'class_program' => $classProgram,
					'teacher_name' => $teacherNames,
					'month' => $this->filters['monthName'] . ' ' . $this->filters['year'],
					'student_id' => $studentIdVal,
					'student_name' => $studentName,
					'from_surah' => $plan->fromSurat->name ?? 'N/A',
					'from_verse' => $plan->start_from_ayat ?? 'N/A',
					'to_surah' => $plan->toSurat->name ?? 'N/A',
					'to_verse' => $plan->to_ayat ?? 'N/A',
					'no_of_pages' => $numberOfPages,
					'status' => $plan->status ?? 'N/A',
					'study_direction' => $plan->study_direction ?? 'N/A',
				]);
			} else {
				// No plan assigned: placeholder row
				$results->push([
					'centre_id' => $cls?->center?->id ?? 'N/A',
					'centre_name' => $cls?->center?->name ?? 'N/A',
					'class_id' => $classId,
					'class_name' => $className,
					'class_program' => $classProgram,
					'teacher_name' => $teacherNames,
					'month' => $this->filters['monthName'] . ' ' . $this->filters['year'],
					'student_id' => $studentIdVal,
					'student_name' => $studentName,
					'from_surah' => 'N/A',
					'from_verse' => 'N/A',
					'to_surah' => 'N/A',
					'to_verse' => 'N/A',
					'no_of_pages' => 0,
					'status' => 'No Plan',
					'study_direction' => '',
				]);
			}
		}

		return $results;
    }

    /**
     * Calculate number of pages using stored procedure for memorization
     */
    private function calculatePages(StudentHefzPlan $plan): int
    {
        try {
            if ($plan->study_direction === 'backward') {
                $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $plan->start_from_surat,
                    $plan->start_from_ayat,
                    $plan->to_surat,
                    $plan->to_ayat
                ]);

                return $result[0]->numberofPagesSum ?? 0;
            } else {
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $plan->start_from_surat,
                    $plan->start_from_ayat,
                    $plan->to_surat,
                    $plan->to_ayat
                ]);

                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                return $results[0]->number_of_pages_sum ?? 0;
            }
        } catch (\Exception $e) {
            \Log::error('Error calculating pages for memorization plan: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get table headings
     */
    private function getTableHeadings(): array
    {
		return [
			'Centre ID',
			'Centre Name',
			'Class ID',
			'Class Name',
			'Class Program',
			'Teacher Name',
			'Month',
			'Student ID',
			'Student Name',
			'From Surah',
			'From Verse',
			'To Surah',
			'To Verse',
			'No. of Pages',
			'Status',
			'Study Direction',
		];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Memorization Plans';
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [];
    }

    /**
     * Register events for creating the table
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $this->createTable($event->sheet);
            },
        ];
    }

    /**
     * Create table on the worksheet
     */
    private function createTable($sheet)
    {
        $worksheet = $sheet->getDelegate();

        // Get data
        $memorizationData = $this->getMemorizationPlans();
        $headings = $this->getTableHeadings();

        // Set main title
        $monthName = $this->filters['monthName'];
        $year = $this->filters['year'];
        $classNames = collect($this->filters['classes'] ?? [])->pluck('name')->join(', ');

		$worksheet->setCellValue('A1', "MEMORIZATION PLANS - {$classNames} - {$monthName} {$year}");
		$worksheet->mergeCells('A1:Q1');

        // Style main title
		$worksheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 16],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'E8F4F8']]
        ]);

        $currentRow = 3;

        // Create Table
		$this->createDataTable($worksheet, $currentRow, 'MEMORIZATION PLANS', $headings, $memorizationData);

        // Auto-size columns
		foreach (range('A', 'Q') as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }
    }

    /**
     * Create a single table with title, headers, and data
     */
    private function createDataTable($worksheet, $startRow, $title, $headings, $data): int
    {
        // Set table title
		$worksheet->setCellValue("A{$startRow}", $title);
		$worksheet->mergeCells("A{$startRow}:Q{$startRow}");

        // Style table title
		$worksheet->getStyle("A{$startRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2E7D32']]
        ]);

        $headerRow = $startRow + 1;

        // Set headers
        $col = 'A';
        foreach ($headings as $heading) {
            $worksheet->setCellValue($col . $headerRow, $heading);
            $col++;
        }

        // Style headers
		$worksheet->getStyle("A{$headerRow}:Q{$headerRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '4CAF50']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $dataStartRow = $headerRow + 1;
        $currentDataRow = $dataStartRow;

        // Add data rows
		foreach ($data as $row) {
            $col = 'A';
            foreach ($row as $value) {
                $worksheet->setCellValue($col . $currentDataRow, $value);
                $col++;
            }
            $currentDataRow++;
        }

        // Style data rows
        if ($data->count() > 0) {
            $dataEndRow = $currentDataRow - 1;
			$worksheet->getStyle("A{$dataStartRow}:Q{$dataEndRow}")->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ]);

            // Alternate row colors
            for ($row = $dataStartRow; $row <= $dataEndRow; $row++) {
                if (($row - $dataStartRow) % 2 == 1) {
					$worksheet->getStyle("A{$row}:Q{$row}")->applyFromArray([
                        'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'F5F5F5']]
                    ]);
                }
            }

            return $dataEndRow;
        }

        // If no data, add "No data available" message
		$worksheet->setCellValue("A{$dataStartRow}", 'No data available');
		$worksheet->mergeCells("A{$dataStartRow}:Q{$dataStartRow}");
        $worksheet->getStyle("A{$dataStartRow}")->applyFromArray([
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'font' => ['italic' => true],
			'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        return $dataStartRow;
    }
}
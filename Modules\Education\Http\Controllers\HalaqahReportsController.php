<?php

namespace Modules\Education\Http\Controllers;


use App\Classes;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;



class HalaqahReportsController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @param $classId
     * @return Response
     */
    public function __invoke($classId)
    {
        $class = Classes::find($classId);
        $supervisors = $class->center->employee; // Get all employees for the center
        $supervisorList = $supervisors->pluck('name')->join(', ');

        $center = $class->center->name;
        $centerDetails = $class->center;
        $className = Classes::find($classId)->name;
        $classTeachers = $class->teachers()->pluck('full_name')->toArray();


        $classPrograms = $class->programs()->get()->pluck('title')->toArray()[0];


        

        // Check if the class's program contains "Ijazah and Sanad"
        if (str_contains(strtolower($classPrograms), 'ijazah and sanad')) {
            $monthlyHalaqahReportMonthYearList =  \App\StudentIjazasanadMemorizationReport::where('class_id', $classId)
                ->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year')
                ->groupBy('year', 'month')
                ->orderByDesc('year')
                ->orderByRaw("FIELD(month, 'December', 'November', 'October', 'September', 'August', 'July', 'June', 'May', 'April', 'March', 'February', 'January')")
                ->get();
                
            // Load a different view if the class is part of the "Ijazah and Sanad" program
            return view('education::classes.reports.class.ijazasanadHalaqah', compact(
                'classTeachers', 'className', 'monthlyHalaqahReportMonthYearList', 'center', 'supervisorList', 'centerDetails','classId'
            ));
        } 
        else if (str_contains(strtolower($classPrograms), 'nouranya') || str_contains(strtolower($classPrograms), 'nuraniyah')) {
            
            $monthlyHalaqahReportMonthYearList =  \App\StudentNouranyaReport::where('class_id', $classId)
                ->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year')
                ->groupBy('year', 'month')
                ->orderByDesc('year')
                ->orderByRaw("FIELD(month, 'December', 'November', 'October', 'September', 'August', 'July', 'June', 'May', 'April', 'March', 'February', 'January')")
                ->get();

            return view('education::classes.reports.class.nouranya', compact(
                'classTeachers', 'className', 'monthlyHalaqahReportMonthYearList', 'center', 'supervisorList', 'centerDetails','classId'
            ));
        }
        
        else{

            $monthlyHalaqahReportMonthYearList =  \App\StudentHefzReport::where('class_id', $classId)
                ->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year')
                ->groupBy('year', 'month')
                ->orderByDesc('year')
                ->orderByRaw("FIELD(month, 'December', 'November', 'October', 'September', 'August', 'July', 'June', 'May', 'April', 'March', 'February', 'January')")
                ->get();


            return view('education::classes.reports.class.halaqah',compact('classTeachers', 'className','monthlyHalaqahReportMonthYearList','center','supervisorList','centerDetails'));
        }

    }

}

---
title: Module View Location Convention
---

In this project, Blade views for Laravel modules (managed by `

When working with module views, always look in `resources/views/modules/` first.

**This convention MUST be strictly followed when creating or editing any Laravel Blade views or Blade partials for a module. Under no circumstances should these files be placed in, or moved to, the `Modules/<ModuleName>/Resources/views/` directory. Always use `[resources/views/modules](mdc:resources/views/modules)/<module-name-lowercase>/` for all module-related Blade files.**

<?php

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\AdmissionInterview;
use App\CenterEmployee;
use App\Employee;
use App\IjazasanadMemorizationPlan;
use App\MissedClockOut;
use App\Scopes\OrganizationScope;
use App\StudentHefzPlan;
use Carbon\Carbon;
use Doctrine\DBAL\Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use App\Student;
use Module;
use Auth;
use App\Center;

use App\Classes;

class GeneralDashboardWidgetCounterController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function __invoke(Request $request)
    {



        $application_need_action = auth()->user()->hasRole(
            ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "it-officer_2_", "education-manager_2_", 'administrative_2_']) ?
            $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                ->whereRaw('admissions.status = "new_admission"')
                ->has('programs')
                ->with('programs')
                ->has('center')
                ->with('center')
                ->has('student')
                ->with('student')
                ->withoutGlobalScope(OrganizationScope::class)
                ->count()
            :
            $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                ->whereRaw('admissions.status = "new_admission"')
                ->whereIn('center_id', CenterEmployee::where('emp_id', Auth::user()->id)->pluck('cen_id')->toArray())
                ->has('programs')
                ->with('programs')
                ->has('center')
                ->with('center')
                ->has('student')
                ->with('student')
                ->withoutGlobalScope(OrganizationScope::class)
                ->count();


        $interviews_not_confirmed_yet =  Admission::where('admissions.status', 'waiting_for_interview')
            ->whereHas('interviews', function($query) {
                $query->where('admission_interviews.status', 'waiting_for_interview');
            })
            ->has('programs')
            ->has('center')
            ->with('center')
            ->has('student')
            ->with('student')
            ->with('interviews')->count();

        $students_with_no_study_plan = AdmissionInterview::whereNull('confirmed_at')
            ->count();
        $yesterday = date("Y-m-d", strtotime('-1 days'));
        $missedClockouts_need_action = 0;
        if ((auth()->user()->hasRole(['education-manager_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {

            // show the count for the supervisors only

            $missedClockouts_need_action = MissedClockOut::onlyTrashed()->whereHas("employee.roles", function ($q) use ($request) {
                return $q->where('name', 'supervisor_' . config('organization_id') . '_');
            })->whereDate('clock', $yesterday)->distinct()->count('employee_id');

        } elseif ((auth()->user()->hasRole(['supervisor_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {

//            show the count for the teachers only
            $missedClockouts_need_action = MissedClockOut::onlyTrashed()->whereHas("employee.teacherCenter", function ($q) use ($request) {
                $supervisorCenId = auth()->user()->center()->get()->pluck('id');
                $supervisorCenId = $supervisorCenId->toArray();
                $q->whereIn('cen_id', $supervisorCenId);
            })->whereDate('clock', $yesterday)->distinct()->count('employee_id');


        } else {

            $missedClockouts_need_action = MissedClockOut::onlyTrashed()->whereDate('clock', $yesterday)->distinct()->count('employee_id');
            $missedClockouts_need_action = floor(MissedClockOut::withTrashed()->whereDate('clock', $yesterday)->count() / 2);
            // refactor this
            $missedClockouts_need_action = \DB::select("select count(a.employee_id) as count from (
                select employee_id from missed_clockouts where date(clock) = subdate(curdate(), 1)  and deleted_at is not null group by employee_id,attendance_id having attendance_id is not null) as a");

            $missedClockouts_need_action = $missedClockouts_need_action[0]->count;
        }
        $class = Classes::count();
        $center = Center::count();
        $studentno = Student::count();
        $employees = Employee::with('roles')->get();
        $teacherno = $employees->filter(function ($employees, $key) {
            return $employees->hasRole('teacher_2_');
        })->count();



        if (auth()->user()->hasRole(
            ["managing-director_2_"])) {

            $hefzPlansWaiting_approval = \App\StudentHefzPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();
            $RevisionPlanWaiting_approval = \App\StudentRevisionPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();


            $ijazasanadMemorizationPlanWaitingApproval = IjazasanadMemorizationPlan::has('student')
                ->has('center')
                ->where('status', 'waiting_for_approval')
                ->where(function ($query) {
                    // Group the first set of conditions
                    $query->where(function ($subQuery) {
                        $subQuery->whereNotNull('start_from_surat')
                            ->whereNotNull('start_from_ayat')
                            ->whereNotNull('to_surat')
                            ->whereNotNull('to_ayat');
                    })
                        // Combine with the second set using OR
                        ->orWhere(function ($subQuery) {
                            $subQuery->whereNotNull('talqeen_from_lesson')
                                ->orWhereNotNull('talqeen_to_lesson')
                                ->orWhereNotNull('revision_from_lesson')
                                ->orWhereNotNull('revision_to_lesson')
                                ->orWhereNotNull('jazariyah_from_lesson')
                                ->orWhereNotNull('jazariyah_to_lesson')
                                ->orWhereNotNull('seminars_from_lesson')
                                ->orWhereNotNull('seminars_to_lesson');
                        });
                })
                ->count();

            $NouranyaPlanWaitingApproval = \App\StudentNouranyaPlan::has('student')  // Ensure the plan is associated with a student
            ->has('center')  // Ensure the plan is associated with a center
            ->where('status', 'waiting_for_approval')  // Filter by status 'waiting_for_approval'
            ->where(function ($query) {
                // Level 1: Check only 'from_lesson' and 'to_lesson'
                $query->where(function ($level1Query) {
                    $level1Query->whereNotNull('from_lesson')
                        ->whereNotNull('to_lesson');
                })
                    // Level 2: Check 'from_lesson', 'to_lesson', 'from_lesson_line_number', and 'to_lesson_line_number'
                    ->orWhere(function ($level2Query) {
                        $level2Query->whereNotNull('from_lesson')
                            ->whereNotNull('to_lesson')
                            ->whereNotNull('from_lesson_line_number')
                            ->whereNotNull('to_lesson_line_number');
                    })
                    // Level 3: Check 'talaqqi_from_lesson', 'talaqqi_to_lesson', 'talqeen_from_lesson', and 'talqeen_to_lesson'
                    ->orWhere(function ($level3Query) {
                        $level3Query->whereNotNull('talaqqi_from_lesson')
                            ->whereNotNull('talaqqi_to_lesson')
                            ->whereNotNull('talqeen_from_lesson')
                            ->whereNotNull('talqeen_to_lesson');
                    });
            })
                ->count();  // Get the count of such records

            $plans_waiting_approval = $hefzPlansWaiting_approval + $RevisionPlanWaiting_approval+$NouranyaPlanWaitingApproval+$ijazasanadMemorizationPlanWaitingApproval;



        } else {

            // Initialize all count variables to 0
            $hefzPlansWaiting_approval = 0;
            $ijazasanadMemorizationPlanWaitingApproval = 0;
            $RevisionPlanWaiting_approval = 0;
            $NouranyaPlanWaitingApproval = 0;



            if (auth()->user()->can('approve_hefz_plan')) {


                if (auth()->user()->center) {
                    $hefzPlansWaiting_approval = \App\StudentHefzPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                        ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())
                        ->whereNotNull('start_from_surat')
                        ->whereNotNull('start_from_ayat')
                        ->whereNotNull('to_surat')
                        ->whereNotNull('to_ayat')
                        ->count();
                }

            }

            if (auth()->user()->can('approve_ijazasanad_plan')) {

                if (auth()->user()->centers) {
                    $ijazasanadMemorizationPlanWaitingApproval = IjazasanadMemorizationPlan::has('student')
                        ->has('center')
                        ->where('status', 'waiting_for_approval')
                        ->whereIn('center_id', auth()->user()->centers->pluck('id')->toArray())
                        ->where(function ($query) {
                            $query->where(function ($subQuery) {
                                $subQuery->whereNotNull('start_from_surat')
                                    ->whereNotNull('start_from_ayat')
                                    ->whereNotNull('to_surat')
                                    ->whereNotNull('to_ayat');
                            })
                                ->orWhere(function ($subQuery) {
                                    $subQuery->whereNotNull('talqeen_from_lesson')
                                        ->orWhereNotNull('talqeen_to_lesson')
                                        ->orWhereNotNull('revision_from_lesson')
                                        ->orWhereNotNull('revision_to_lesson')
                                        ->orWhereNotNull('jazariyah_from_lesson')
                                        ->orWhereNotNull('jazariyah_to_lesson')
                                        ->orWhereNotNull('seminars_from_lesson')
                                        ->orWhereNotNull('seminars_to_lesson');
                                });
                        })
                        ->count();
                }
            }
            if (auth()->user()->can('approve_revision_plan')) {

                if (auth()->user()->center) {
                    $RevisionPlanWaiting_approval = \App\StudentRevisionPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                        ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())
                        ->whereNotNull('start_from_surat')
                        ->whereNotNull('start_from_ayat')
                        ->whereNotNull('to_surat')
                        ->whereNotNull('to_ayat')
                        ->count();
                }

            }

            if (auth()->user()->can('approve_nouranya_plan')) {

                $NouranyaPlanWaitingApproval = \App\StudentNouranyaPlan::has('student')  // Ensure the plan is associated with a student
                ->has('center')  // Ensure the plan is associated with a center
                ->where('status', 'waiting_for_approval')  // Filter by status 'waiting_for_approval'
                ->where(function ($query) {
                    // Level 1: Check only 'from_lesson' and 'to_lesson'
                    $query->where(function ($level1Query) {
                        $level1Query->whereNotNull('from_lesson')
                            ->whereNotNull('to_lesson');
                    })
                        // Level 2: Check 'from_lesson', 'to_lesson', 'from_lesson_line_number', and 'to_lesson_line_number'
                        ->orWhere(function ($level2Query) {
                            $level2Query->whereNotNull('from_lesson')
                                ->whereNotNull('to_lesson')
                                ->whereNotNull('from_lesson_line_number')
                                ->whereNotNull('to_lesson_line_number');
                        })
                        ->orWhere(function ($level3Query) {
                            $level3Query->whereNotNull('talaqqi_from_lesson')
                                ->whereNotNull('talaqqi_to_lesson')
                                ->whereNotNull('talqeen_from_lesson')
                                ->whereNotNull('talqeen_to_lesson');
                        });
                })
                    ->count();  // Get the count of such records
            }
                $plans_waiting_approval = $hefzPlansWaiting_approval + $RevisionPlanWaiting_approval + $NouranyaPlanWaitingApproval + $ijazasanadMemorizationPlanWaitingApproval;


        }


        return response()->json([

            'studentswithNoHefzPlanCountWidget' => $students_with_no_study_plan,
            'interviewsNotConfirmedCountWidget' => $interviews_not_confirmed_yet,
            'missedClockoutsNeedActionCountWidget' => $missedClockouts_need_action,
            'plansWaitingApprovalCountWidget' => $plans_waiting_approval,
            'applicationNeedsActionCountWidget' => $application_need_action,
            'class' => $class,
            'center' => $center,
            'studentno' => $studentno,
            'teacherno' => $teacherno,
        ]);

    }
}

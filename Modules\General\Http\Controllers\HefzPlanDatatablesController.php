<?php

namespace Modules\General\Http\Controllers;


use App\Admission;
use App\Center;
use App\CenterEmployee;
use App\Scopes\OrganizationScope;
use App\Student;
use App\StudentHefzPlan;
use App\StudentRevisionPlan;
use Carbon\Carbon;
use Illuminate\Http\Request;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Services\StudentImageService;


class HefzPlanDatatablesController extends Controller
{

    protected $studentImageService;

    public function __construct(StudentImageService $studentImageService)
    {
        $this->studentLevelService = $studentLevelService;
        $this->studentImageService = $studentImageService;
    }

    public function getPlansNeedApproval(Request $request)
    {

        if ($request->ajax()) {
            $statusColumn = 'student_hefz_plans.status';
            $hefzTable = ',student_hefz_plans';
            $hefzTableJoinQuery = ' AND students.id = student_hefz_plans.student_id';
            $queryPart = ' AND student_hefz_plans.status = "waiting_for_approval"';
            $planWaitingApprovalStatus_condition = $queryPart;
            $archivedStatus = ' and students.deleted_at is null';



            $classroomIds = $request->input('classroomId'); // Get classroom IDs from the request


            if (auth()->user()->hasRole(["managing-director_2_"])) {
                $studentsApprovalQuery = StudentHefzPlan::where('status', '=', 'waiting_for_approval')
                    ->whereNotNull('start_from_surat')
                    ->whereNotNull('start_from_ayat')
                    ->whereNotNull('class_id')
                    ->whereNotNull('to_surat')
                    ->whereNotNull('to_ayat')
                    ->when($classroomIds, function ($query) use ($classroomIds) {
                        return $query->whereIn('class_id', $classroomIds);
                    })
                    ->with('student')->with('halaqah')->with('center')->orderBy('updated_at','DESC')->select();
            } else {
                // SYSTEM VIEWER FIX: This code now works transparently for system viewers!
                // The auth()->user()->center relationship automatically returns all centers for system viewers
                // due to the overridden center() method in the Employee model.
                // 
                // OLD PROBLEM: auth()->user()->center->pluck('id')->toArray() returned [] for viewers
                // NEW SOLUTION: Now returns all center IDs for system_viewer_2_ role automatically
                //
                // OPTIONAL OPTIMIZATION: You could also use auth()->user()->getAccessibleCenterIds() 
                // for better performance and clearer intent, but the existing code works fine now.
                
                $studentsApprovalQuery = StudentHefzPlan::where('status', '=', 'waiting_for_approval')
                    ->whereNotNull('start_from_surat')
                    ->whereNotNull('start_from_ayat')
                    ->whereNotNull('class_id')
                    ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())

                    ->whereNotNull('to_surat')
                    ->whereNotNull('to_ayat')
                    ->when($classroomIds, function ($query) use ($classroomIds) {
                        return $query->whereIn('class_id', $classroomIds);
                    })
                    ->with('student')->with('halaqah')->with('center')->orderBy('updated_at','DESC')->select();
            }


            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)

                ->addColumn('select', function ($row) {
                    return '<input type="checkbox" class="checkbox" name="id[]" value="'.$row->id.'">';
                })
                ->addColumn('centerName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('studentName', function ($row) {
                    // Extract the student relationship
                    $student = $row->student;

                    // Ensure the student relationship exists
                    if (!$student) {
                        return '<span class="student-name">N/A</span>';
                    }

                    // Clean and format the student's full name
                    $cleanName = str_replace(['"', "'"], '', $student->full_name);
                    $studentName = ucwords(strtolower($cleanName));
                    $studentNameLimited = Str::limit($studentName, 15, '...');

                    // Determine the gender for placeholder image
                    $gender = strtolower($student->gender);
                    $femalePlaceholder = asset('assets/workplace/img/female student profile picture placeholderrr.png');
                    $malePlaceholder = asset('assets/workplace/img/male profile picture placeholder.png');
                    $genderBasedDefaultImage = ($gender === 'female') ? $femalePlaceholder : $malePlaceholder;

                    // Determine the image URL using Laravel's Storage facade
                    $imageUrl = null;
                    if (!empty($student->student_photo)) {
                        // Remove 'public/' from the path if present
                        $studentPhotoPath = Str::startsWith($student->student_photo, 'public/')
                            ? Str::replaceFirst('public/', '', $student->student_photo)
                            : $student->student_photo;

                        // Check if the student's photo exists in the 'public' disk
                        if (Storage::disk('public')->exists($studentPhotoPath)) {
                            $imageUrl = Storage::url($studentPhotoPath);
                        }
                    }

                    // Use gender-based placeholder if image doesn't exist
                    if (!$imageUrl) {
                        $imageUrl = $genderBasedDefaultImage;
                    }

                    // Sanitize the full and truncated names for safe HTML output
                    $safeFullName = e($studentName);
                    $safeTruncatedName = e($studentNameLimited);

                    // Create the HTML for the student's name with tooltip
                    $nameWithTooltip = '<span class="student-name-tooltip" data-toggle="tooltip" title="' . $safeFullName . '">' . $safeTruncatedName . '</span>';

                    $actionButtons = '
                    <div class="row-action-buttons" style="margin-top: 8px; display: flex; gap: 4px; flex-wrap: wrap;">
                        <button type="button" class="btn btn-success revision-individual-approve-btn" 
                                data-revision-plan-id="' . $row->id . '" 
                                data-student-name="' . $safeFullName . '"
                                title="Approve Plan"
                                style="padding: 2px 6px; font-size: 10px; line-height: 1.2; border-radius: 3px; min-width: auto;">
                            <i class="glyphicon glyphicon-ok" style="font-size: 9px;"></i> Approve
                        </button>
                        <a href="' . $stEditMonthlyPlanRoute . '" target="_blank" 
                           class="btn btn-primary" title="Edit Plan"
                           style="padding: 2px 6px; font-size: 10px; line-height: 1.2; border-radius: 3px; min-width: auto; text-decoration: none; display: inline-block;">
                            <i class="glyphicon glyphicon-edit" style="font-size: 9px;"></i> Edit
                        </a>
                        <button type="button" class="btn btn-warning commentHefzModalTriggerBtn" 
                                data-hefz-plan_id="' . $row->id . '"
                                data-hefz-supervisor_comment="' . htmlspecialchars($row->supervisor_comment ?? '', ENT_QUOTES, 'UTF-8') . '"
                                data-hefz-student_id="' . $row->student_id . '"
                                data-catid="' . $row->id . '" 
                                data-toggle="modal"
                                data-target="#commentHefzPlan"
                                title="Add Comment"
                                style="padding: 2px 6px; font-size: 10px; line-height: 1.2; border-radius: 3px; min-width: auto;">
                            <i class="glyphicon glyphicon-comment" style="font-size: 9px;"></i> Comment
                        </button>
                    </div>';


                    $image = $student ? $this->studentImageService->getStudentImageUrl($student) : asset('maleStudentProfilePicture.png');

                    // Combine everything into an HTML block (with image on the left and text on the right)
                    $html = '
                                                <div class="student-container">
                                <div style="flex-shrink: 0;">
                                                                <img class="studentImage" style="border-radius: 50%; width: 60px; height: 60px; object-fit: cover;" src="' . $image . '">
                                    </div>
                                    <div class="student-details">
                                        ' . $nameWithTooltip . '
                                        ' . $actionButtons . '
                                    </div>
                                </div>
            ';

                    return $html;
                })
                ->addColumn('studentAge', function ($row) {

                    return $row->student->date_of_birth->age;

                })
                ->addColumn('noOfPages', function ($row) {


                    if ($row->study_direction == 'backward') {

                        if(abs($row->start_from_surat-$row->to_surat) == 1) {

                            $numberofPages = \DB::select("SELECT aa.fromSuratCount + bb.toSuratCount AS pageCount
FROM (
         SELECT COUNT(*) AS fromSuratCount
         FROM (
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :startSurahId AND (first_ayah <= :startAyah1 OR last_ayah <= :startAyah2) UNION
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :startSurahId2 AND (first_ayah >= (
                      SELECT last_ayah
                      FROM moshaf_pages
                      WHERE surah_id = :startSurahId3
                      ORDER BY last_ayah DESC
                      LIMIT 1) OR last_ayah <= (
                      SELECT last_ayah
                      FROM moshaf_pages
                      WHERE surah_id = :startSurahId4
                      ORDER BY last_ayah DESC
                      LIMIT 1))
                  ORDER BY page_number DESC) a) aa,


     (
         SELECT COUNT(*) AS toSuratCount
         FROM (
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :lastSurahId1 AND first_ayah = 1
                  UNION
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :lastSurahId2 AND (first_ayah >= :lastAyah1 OR last_ayah <= :lastAyah2)) a
         ORDER BY page_number DESC) bb", array(
                                'startSurahId' => $row->start_from_surat,
                                'startSurahId2' => $row->start_from_surat,
                                'startSurahId3' => $row->start_from_surat,
                                'startSurahId4' => $row->start_from_surat,
                                'startAyah1' => optional($row)->pluck('start_from_ayat')[0],
                                'startAyah2' => optional($row)->pluck('start_from_ayat')[0],
                                'lastSurahId1' => $row->to_surat,
                                'lastSurahId2' => $row->to_surat,
                                'lastAyah1' => optional($row)->to_ayat,
                                'lastAyah2' => optional($row)->to_ayat,



                            ));

                            $numberofPages = $numberofPages[0]->pageCount+1;



                        }

                        else{
                            $numberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                'startSurahId' => $row->start_from_surat,
                                'startAyah' => optional($row)->pluck('start_from_ayat')[0],
                                'lastSurahId' => $row->to_surat,
                                'lastAyah' => optional($row)->to_ayat,
                                'lastAyah2' => optional($row)->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                            ));
                            $numberofPages = $numberofPages[0]->pageCount;

                        }





                    }
                    else{

                        if ($row->start_from_surat == $row->to_surat) {
                            $numberofPages = \DB::select("select *
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                'startSurahId' => $row->start_from_surat,
                                'startAyah' => optional($row)->pluck('start_from_ayat')[0],
                                'lastSurahId' => $row->to_surat,
                                'lastAyah' => optional($row)->to_ayat,
                                'lastAyah2' => optional($row)->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                            ));


                            if (($numberofPages[0]->first_page == $numberofPages[0]->last_page) && ($numberofPages[0]->first_page > 0 && $numberofPages[0]->last_page > 0)) {
                                $numberofPages = 1;

                            } else {

                                $numberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                    'startSurahId' => $row->start_from_surat,
                                    'startAyah' => optional($row)->pluck('start_from_ayat')[0],
                                    'lastSurahId' => $row->to_surat,
                                    'lastAyah' => optional($row)->to_ayat,
                                    'lastAyah2' => optional($row)->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                ));


                                $numberofPages = $numberofPages[0]->pageCount;

                            }

                        }
                        else {


                            $numberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                'startSurahId' => $row->start_from_surat,
                                'startAyah' => optional($row)->pluck('start_from_ayat')[0],
                                'lastSurahId' => $row->to_surat,
                                'lastAyah' => optional($row)->to_ayat,
                                'lastAyah2' => optional($row)->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                            ));
                            $numberofPages = $numberofPages[0]->pageCount;


                        }
                    }



                    return $numberofPages;

                })
                ->addColumn('planUpdateDate', function ($row) {


                    return $row->updated_at->diffForHumans();


                })
                ->addColumn('createdByUpdatedBy', function ($row) {


                    if( $row->created_by === $row->updated_by){



//                        $stShowRoute = route('admission.students.show', $row->allStudents->user->id);
                        $genderColor = $row->creator['gender'] == 'male' ? '#34b8bc;!important' : '#FA5661;!important';

                        if (strlen($row->creator['full_name']) > 22) {
                            $fullname = Str::limit(Str::title($row->creator['full_name']),22,' ...');
//                            $fullname = Str::title($row->creator['full_name']);


                            return '<a style="color:' . $genderColor . '" target="_blank" href="#" ><strong data-tooltip="' . Str::title($row->creator['full_name']) . '" >' . $fullname . '</strong></a>';
                        } else {
                            $fullname = Str::title($row->creator['full_name']);
                            return '<a style="color:' . $genderColor . '" target="_blank" href="#" ><strong data-tooltip="' . Str::title($row->creator['full_name']) . '" style="color:"' . $genderColor . '">' . $fullname . '</strong></a>';
                        }



//                        return  $row->creator['full_name'];
                    }
                    else{

                        $genderColor = $row->creator['gender'] == 'male' ? '#34b8bc;!important' : '#FA5661;!important';



                        if (strlen($row->creator['full_name']) > 22) {
                            $creatorFullname = Str::limit(Str::title($row->creator['full_name']),22,' ...');


                        } else {
                            $creatorFullname = Str::title($row->creator['full_name']);

                        }
                        if (strlen($row->updator['full_name']) > 22) {
                            $updatorFullname = Str::limit(Str::title($row->updator['full_name']),22,' ...');


                        } else {
                            $updatorFullname = Str::title($row->updator['full_name']);

                        }

                        return '<a style="color:' . $genderColor . '" target="_blank" href="#" ><strong data-tooltip="' . Str::title($row->creator['full_name'].'/'.$row->updator['full_name']) . '" >' .  $creatorFullname.' / '. $updatorFullname . '</strong></a>';




                    }




//                        if( $row->created_by === $row->updated_by){
//
//                            return  $row->creator['full_name'];
//                        }
//                        else{
//                            return   $row->creator['full_name'].' / '. $row->updator['full_name'];
//
//
//                        }




                })
                ->addColumn('fromSuratAyat', function ($row) {


                        return   '<a data-placement="top" href="#" data-toggle="tooltip" title="'.$row->start_from_surat.'">'.$row->fromSurat->name  .' - </a><a data-placement="top" href="#" data-toggle="tooltip" title="'.$row->fromSurat->name.'">'.$row->start_from_ayat.' </a>';



                })
                ->addColumn('toSuratAyat', function ($row) {





                        return   '<a data-placement="top" href="#" data-toggle="tooltip" title="'.$row->to_surat.'">'.$row->toSurat->name.' - </a><a data-placement="top" href="#" data-toggle="tooltip" title="'.$row->toSurat->name.'">'.$row->to_ayat .' </a>' ;




                })
                ->addColumn('planDate', function ($row) {

                        return Carbon::parse($row->start_date)->format('F Y');

                })
                ->addColumn('created_at', function ($row) {

                    return value($row['created_at'])->diffForHumans();
                })
                ->addColumn('halaqah', function ($row) {



                    return $row->halaqah->name;

                })
                
                ->addColumn('check', function ($row) {



                    $stEditMonthlyPlanRoute = route('monthly-plan.show',[$row->class_id,$row->created_at->format('Y-m-d')]);

                    $stEditRoute = route('students.edit', $row->student_id);
                    $stShowRoute = route('students.show', $row->student_id);

                    $btns = '<div class="ui vertical icon menu">
  <a target="_blank" href="' . $stEditMonthlyPlanRoute . '" title="Edit Monthly Plan" class="item">
    <i class="edit icon"></i>Edit
  </a>
  <a 
  class="item commentHefzModalTriggerBtn" 
  id="commentHefzModalTriggerBtn"
   style=" cursor:pointer;"
   data-hefz-plan_id = "' . $row->id . '"
   data-hefz-supervisor_comment = "' . optional($row)->supervisor_comment . '"
   data-hefz-student_id = "' . $row->student->id . '"
    data-catid=' . $row->id . ' data-toggle="modal"
     data-target="#commentHefzPlan" >
    <i class="comment alternate icon"></i>Comment
  </a>
  <a class="item approveModalTriggerBtn"
  id="approvalModalTriggerBtn "
                    
                     style=" cursor:pointer;"
                    data-hefz-plan_id = "' . $row->id . '"
                    data-hefz-student_id = "' . $row->student->id . '"
                    data-catid=' . $row->id . ' data-toggle="modal"
                    data-target="#approve">
    <i class="check icon"></i>Approve</a>
</div>
                                                                             

                                                                            
                                                                                                                ';

//
                    return $btns;
                })
                ->setRowAttr([
                    'data-memorizationPlan_id' => function($row) {
                        return $row->id; // Assuming $row->id is your report ID
                    },
                    'data-studentName' => function($row) {
                        return $row->student->full_name; // Assuming $row->id is your report ID
                    },

            'data-studentMonthlyMemorizationPlanRoute' => function($row) {
                $stEditMonthlyPlanRoute = route('monthly-plan.show',[$row->class_id,$row->created_at->format('Y-m-d')]);

                return $stEditMonthlyPlanRoute; // Assuming $row->id is your report ID
                    },
                    'data-memorizationPlan-student_id' => function($row) {
                        return $row->student->id; // Assuming $row->student->id is your student ID
                    }
                ])
                ->rawColumns(['select','studentName','fromSuratAyat','toSuratAyat','createdByUpdatedBy','select'])
                ->toJson();
        }


    }

    public function getHefzMonthlyPlanDetails(Request $request)
    {


        if ($request->ajax()) {
            $statusColumn = 'student_hefz_plans.status';
            $hefzTable = ',student_hefz_plans';
            $hefzTableJoinQuery = ' AND students.id = student_hefz_plans.student_id';
            $queryPart = ' AND student_hefz_plans.status = "waiting_for_approval"';
            $planWaitingApprovalStatus_condition = $queryPart;
            $archivedStatus = ' and students.deleted_at is null';


//            $application_need_action = auth()->user()->hasRole(
//                ["curriculum-specialist_2_","programs-specialist_2_","managing-director_2_","it-officer_2_","education-manager_2_"])

            $studentHefzMonthlyPlanDetails = StudentHefzPlan::where('id', $request->get('hefz_plans_id'))->select();


            return \Yajra\DataTables\DataTables::of($studentHefzMonthlyPlanDetails)
                ->addColumn('teacherName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('action', function ($row) {

                    $approveRoute = route('students.edit', $row->student->id);

                    $btns = ' <a href="' . $approveRoute . '" 
                                                                               class="btn btn-success btn-xs" title="Approve Plan"><span
                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                                                                                                                 ';


//
                    return $btns;
                })->rawColumns(['action'])
                ->toJson();

        }


    }

    public function getApplicationsWaitingApprovals(Request $request)
    {


        if ($request->ajax()) {

//            $StudentsTableStatusqueryPart = ' AND students.status != "active" AND students.status != "suspended" AND students.status !="graduated" AND students.status !="rejected"';
            $StudentsTableStatusqueryPart = ' ';

            $centerFilter = auth()->user()->hasRole(
                ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "education-manager_2_", "administrative_2_"]) ? '' :
                "AND centers.id in ( select cen_id from cen_emps where emp_id = '" . \Auth::user()->id . "') ";


            auth()->user()->hasRole(
                ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "education-manager_2_"]) ?

                $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                    ->whereRaw('admissions.status = "new_admission"')
                    ->has('programs')
                    ->with('programs')
                    ->has('center')
                    ->with('center')
                    ->has('student')
                    ->with('student')
                    ->withoutGlobalScope(OrganizationScope::class)
                    ->select()
                :

                $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                    ->whereRaw('admissions.status = "new_admission"')
                    ->whereIn('center_id', CenterEmployee::where('emp_id', auth()->user()->id)->pluck('cen_id')->toArray())
                    ->has('programs')
                    ->with('programs')
                    ->has('center')
                    ->with('center')
                    ->has('student')
                    ->with('student')
                    ->withoutGlobalScope(OrganizationScope::class)
                    ->select();


            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
                ->addColumn('programTitle', function ($row) {
                    return $row->programs->map(function ($program) {
                        return $program->title;
                    })->implode('<br>');
                })
                ->addColumn('centerName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('created_at', function ($row) {

                    return value($row['created_at'])->diffForHumans();
                })
                ->addColumn('action', function ($row) {


                    $stEditRoute = route('students.edit', $row->student_id);
                    $stShowRoute = route('students.show', $row->student_id);

                    $btns = ' <a target="_blank" href="' . $stShowRoute . '" 
                                                                               class="btn btn-success btn-xs" title="View Center"><span
                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                                                                                    ';
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Update Student details">
//                                                                             <span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>

//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//


//
                    return $btns;
                })
                ->rawColumns(['action'])
                ->toJson();


//            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
//                ->addColumn('action', function ($row) {
//
//                    $stEditRoute = route('admission.students.edit', $row->id);
//                    $stShowRoute = route('admission.students.show', $row->id);
//
//                    $btns = ' <a href="' . $stShowRoute . '"
//                                                                               class="btn btn-success btn-xs" title="View Center"><span
//                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>
//
//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//                                                                                                                 ';
//
//
////
//                    return $btns;
//                })
//                ->rawColumns(['action'])
//                ->make(true);

        }
    }

    public function getMissedClockOuts(Request $request)
    {


        if ($request->ajax()) {

//            $StudentsTableStatusqueryPart = ' AND students.status != "active" AND students.status != "suspended" AND students.status !="graduated" AND students.status !="rejected"';
            $StudentsTableStatusqueryPart = ' ';

            $centerFilter = auth()->user()->hasRole(
                ["update-employee-clockout-details_2_"]) ? '' :

                $application_need_action = auth()->user()->hasRole(
                    ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "it-officer_2_", "education-manager_2_", "administrative_2_"]) ?

                    $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                        ->whereRaw('admissions.status = "new_admission"')
                        ->has('programs')
                        ->with('programs')
                        ->has('center')
                        ->with('center')
                        ->has('student')
                        ->with('student')
                        ->withoutGlobalScope(OrganizationScope::class)
                        ->select()
                    :

                    $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                        ->whereRaw('admissions.status = "new_admission"')
                        ->whereIn('center_id', CenterEmployee::where('emp_id', auth()->user()->id)->pluck('cen_id')->toArray())
                        ->has('programs')
                        ->with('programs')
                        ->has('center')
                        ->with('center')
                        ->has('student')
                        ->with('student')
                        ->withoutGlobalScope(OrganizationScope::class)
                        ->select();


            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
                ->addColumn('programTitle', function ($row) {
                    return $row->programs->map(function ($program) {
                        return $program->title;
                    })->implode('<br>');
                })
                ->addColumn('centerName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('created_at', function ($row) {

                    return value($row['created_at'])->diffForHumans();
                })
                ->addColumn('action', function ($row) {


                    $stEditRoute = route('students.edit', $row->student_id);
                    $stShowRoute = route('students.show', $row->student_id);

                    $btns = ' <a href="' . $stShowRoute . '" 
                                                                               class="btn btn-success btn-xs" title="View Center"><span
                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                                                                                    ';
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Update Student details">
//                                                                             <span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>

//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//


//
                    return $btns;
                })
                ->rawColumns(['action'])
                ->toJson();


//            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
//                ->addColumn('action', function ($row) {
//
//                    $stEditRoute = route('admission.students.edit', $row->id);
//                    $stShowRoute = route('admission.students.show', $row->id);
//
//                    $btns = ' <a href="' . $stShowRoute . '"
//                                                                               class="btn btn-success btn-xs" title="View Center"><span
//                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>
//
//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//                                                                                                                 ';
//
//
////
//                    return $btns;
//                })
//                ->rawColumns(['action'])
//                ->make(true);

        }
    }
}


//            }







<?php

namespace App\Services;

use App\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * SettingsService handles application settings with caching.
 *
 * Purpose: Provide cached access to application settings.
 * Side effects: Reads from database and cache.
 * Errors: Logs issues and provides fallback behavior.
 * Performance: Uses caching to minimize database queries.
 */
class SettingsService
{
    /**
     * Get all settings with caching.
     *
     * @return array<string, mixed>
     */
    public function getAllSettings(): array
    {
        $organizationId = config('organization_id');
        $cacheKey = "settings_{$organizationId}";
        
        return Cache::remember($cacheKey, 3600, function () {
            Log::debug('Loading settings from database');
            
            return Setting::all()->pluck('value', 'name')->toArray();
        });
    }
    
    /**
     * Get a specific setting value.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getSetting(string $key, $default = null)
    {
        $settings = $this->getAllSettings();
        return $settings[$key] ?? $default;
    }
    
    /**
     * Clear settings cache.
     *
     * @return void
     */
    public function clearCache(): void
    {
        $organizationId = config('organization_id');
        $cacheKey = "settings_{$organizationId}";
        
        Cache::forget($cacheKey);
        Log::info('Settings cache cleared', ['organization_id' => $organizationId]);
    }
}
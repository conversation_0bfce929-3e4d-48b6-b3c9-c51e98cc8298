<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services\Ai;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\Job;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\JobSeeker;
use <PERSON><PERSON>les\JobSeeker\Entities\JobSeekerAiTailorRun;
use Modules\JobSeeker\Entities\JobSeekerAiTailorArtifact;
use Modules\JobSeeker\Entities\JobSeekerResume;

/**
 * AiTailorService orchestrates the 10-step AI resume tailoring pipeline.
 * 
 * Purpose: Execute deterministic, auditable LLM processing to tailor resumes to specific jobs.
 * Pipeline: Job analysis → Resume profiling → Gap identification → Content enhancement → Final assembly.
 * Business rules: Non-destructive; guardrails for safety; comprehensive logging; artifact persistence.
 * Performance: Token tracking; processing time measurement; parallel step optimization.
 * Security: Content sanitization; guardrail enforcement; user context isolation.
 * 
 * Note: This service coordinates the pipeline but delegates LLM calls to a separate provider service.
 * The 10-step algorithm will be implemented based on user specifications (to be provided later).
 */
final class AiTailorService
{
    private const PIPELINE_STEPS = 10;
    private const MAX_PROCESSING_TIME_MS = 300000; // 5 minutes timeout
    private const DEFAULT_MODEL = 'gpt-4'; // Configuration would normally come from config

    /**
     * Execute the full AI tailoring pipeline for selected resumes and a target job
     * 
     * @param JobSeeker $user The job seeker requesting the tailoring
     * @param Job $job The target job to tailor resumes for
     * @param Collection<JobSeekerResume> $resumes Selected resumes to process
     * @param array $constraints Optional user constraints for tailoring
     * @param string|null $idempotencyKey Optional key to prevent duplicate runs
     * @return JobSeekerAiTailorRun The tailoring run with final results
     * @throws \Exception If pipeline execution fails
     */
    public function executeTailoringPipeline(
        JobSeeker $user,
        Job $job,
        Collection $resumes,
        array $constraints = [],
        ?string $idempotencyKey = null
    ): JobSeekerAiTailorRun {
        $correlationId = JobSeekerAiTailorRun::generateCorrelationId();

        Log::info('AiTailorService: Starting tailoring pipeline execution', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'job_id' => $job->id,
            'job_slug' => $job->slug,
            'resume_count' => $resumes->count(),
            'resume_ids' => $resumes->pluck('id')->toArray(),
            'constraints' => $constraints,
            'idempotency_key' => $idempotencyKey,
        ]);

        $startTime = microtime(true);

        try {
            // Check for duplicate run if idempotency key provided
            if ($idempotencyKey) {
                $existingRun = $this->findExistingRun($user->id, $job->id, $idempotencyKey);
                if ($existingRun) {
                    Log::info('AiTailorService: Returning existing run due to idempotency key', [
                        'correlation_id' => $correlationId,
                        'existing_run_id' => $existingRun->id,
                        'idempotency_key' => $idempotencyKey,
                    ]);
                    return $existingRun;
                }
            }

            // Create the tailor run record
            $tailorRun = JobSeekerAiTailorRun::create([
                'job_seeker_id' => $user->id,
                'job_id' => $job->id,
                'job_slug' => $job->slug,
                'correlation_id' => $correlationId,
                'model' => self::DEFAULT_MODEL,
                'status' => 'pending',
            ]);

            // Link selected resumes to the run
            $this->linkResumesToRun($tailorRun, $resumes);

            // Mark run as running
            $tailorRun->markAsRunning();

            // Execute the 10-step pipeline
            $this->executePipelineSteps($tailorRun, $job, $resumes, $constraints);

            // Calculate final metrics
            $processingTimeMs = (int) ((microtime(true) - $startTime) * 1000);
            $totalTokensInput = $this->calculateTotalTokensInput($tailorRun);
            $totalTokensOutput = $this->calculateTotalTokensOutput($tailorRun);

            // Mark run as succeeded
            $tailorRun->markAsSucceeded($totalTokensInput, $totalTokensOutput, $processingTimeMs);

            Log::info('AiTailorService: Successfully completed tailoring pipeline', [
                'correlation_id' => $correlationId,
                'tailor_run_id' => $tailorRun->id,
                'processing_time_ms' => $processingTimeMs,
                'total_tokens_input' => $totalTokensInput,
                'total_tokens_output' => $totalTokensOutput,
            ]);

            return $tailorRun->fresh(['artifacts', 'resumes']);

        } catch (\Exception $e) {
            $processingTimeMs = (int) ((microtime(true) - $startTime) * 1000);

            if (isset($tailorRun)) {
                $tailorRun->markAsFailed($e->getMessage());
            }

            Log::error('AiTailorService: Tailoring pipeline failed', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'job_id' => $job->id,
                'processing_time_ms' => $processingTimeMs,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Get a tailoring run by ID with ownership verification
     * 
     * @param int $runId The run ID to retrieve
     * @param int $userId The requesting user ID for ownership check
     * @return JobSeekerAiTailorRun|null The run if found and owned by user
     */
    public function getTailoringRun(int $runId, int $userId): ?JobSeekerAiTailorRun
    {
        return JobSeekerAiTailorRun::where('id', $runId)
            ->where('job_seeker_id', $userId)
            ->with(['artifacts', 'resumes', 'job'])
            ->first();
    }

    /**
     * List recent tailoring runs for a user
     * 
     * @param int $userId The job seeker's ID
     * @param int $limit Maximum number of runs to return
     * @return Collection<JobSeekerAiTailorRun> Recent runs
     */
    public function getRecentRuns(int $userId, int $limit = 10): Collection
    {
        return JobSeekerAiTailorRun::forJobSeeker($userId)
            ->with(['job:id,title,company,slug'])
            ->limit($limit)
            ->get();
    }

    /**
     * Get detailed diff and final resume content for a successful run
     * 
     * @param JobSeekerAiTailorRun $run The completed tailoring run
     * @return array Array with 'diff' and 'final_resume' content
     * @throws \Exception If run is not successful or artifacts missing
     */
    public function getRunResults(JobSeekerAiTailorRun $run): array
    {
        if (!$run->isSuccessful()) {
            throw new \Exception('Cannot get results for unsuccessful run');
        }

        $diffArtifact = $run->artifacts()->forStep(9)->first(); // Step 9: resume_diff.md
        $finalArtifact = $run->artifacts()->forStep(10)->first(); // Step 10: final_resume.html

        if (!$diffArtifact || !$finalArtifact) {
            throw new \Exception('Results artifacts are missing');
        }

        return [
            'diff' => $diffArtifact->content,
            'final_resume' => $finalArtifact->content,
            'diff_artifact' => $diffArtifact,
            'final_artifact' => $finalArtifact,
        ];
    }

    /**
     * Execute all 10 steps of the tailoring pipeline
     * 
     * @param JobSeekerAiTailorRun $run The run to execute
     * @param Job $job The target job
     * @param Collection<JobSeekerResume> $resumes Selected resumes
     * @param array $constraints User constraints
     * @throws \Exception If any step fails
     */
    private function executePipelineSteps(
        JobSeekerAiTailorRun $run,
        Job $job,
        Collection $resumes,
        array $constraints
    ): void {
        Log::info('AiTailorService: Starting pipeline step execution', [
            'correlation_id' => $run->correlation_id,
            'tailor_run_id' => $run->id,
            'total_steps' => self::PIPELINE_STEPS,
        ]);

        // NOTE: The 10-step algorithm implementation will be added when the user provides it
        // For now, we'll create placeholder artifacts that demonstrate the structure

        for ($step = 1; $step <= self::PIPELINE_STEPS; $step++) {
            $this->executeStep($run, $step, $job, $resumes, $constraints);
        }
    }

    /**
     * Execute a single pipeline step
     * 
     * @param JobSeekerAiTailorRun $run The run to execute
     * @param int $step Step number (1-10)
     * @param Job $job The target job
     * @param Collection<JobSeekerResume> $resumes Selected resumes
     * @param array $constraints User constraints
     * @throws \Exception If step execution fails
     */
    private function executeStep(
        JobSeekerAiTailorRun $run,
        int $step,
        Job $job,
        Collection $resumes,
        array $constraints
    ): void {
        $stepStartTime = microtime(true);

        Log::debug('AiTailorService: Executing pipeline step', [
            'correlation_id' => $run->correlation_id,
            'tailor_run_id' => $run->id,
            'step' => $step,
        ]);

        try {
            // Generate step content based on step number
            $stepResult = $this->generateStepContent($step, $job, $resumes, $constraints);

            // Calculate step processing time
            $stepProcessingTime = (int) ((microtime(true) - $stepStartTime) * 1000);

            // Create artifact for this step
            JobSeekerAiTailorArtifact::createForStep(
                $run->id,
                $step,
                $stepResult['content'],
                $stepResult['mime'],
                $stepResult['tokens_input'] ?? null,
                $stepResult['tokens_output'] ?? null,
                $stepProcessingTime
            );

            Log::debug('AiTailorService: Completed pipeline step', [
                'correlation_id' => $run->correlation_id,
                'tailor_run_id' => $run->id,
                'step' => $step,
                'processing_time_ms' => $stepProcessingTime,
                'content_length' => strlen($stepResult['content']),
            ]);

        } catch (\Exception $e) {
            Log::error('AiTailorService: Pipeline step failed', [
                'correlation_id' => $run->correlation_id,
                'tailor_run_id' => $run->id,
                'step' => $step,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception("Pipeline step {$step} failed: " . $e->getMessage());
        }
    }

    /**
     * Generate placeholder content for pipeline steps
     * (This will be replaced with actual LLM integration based on user's 10-step algorithm)
     * 
     * @param int $step Step number
     * @param Job $job Target job
     * @param Collection<JobSeekerResume> $resumes Selected resumes
     * @param array $constraints User constraints
     * @return array Step result with content, MIME type, and token counts
     */
    private function generateStepContent(int $step, Job $job, Collection $resumes, array $constraints): array
    {
        // Placeholder implementation - will be replaced with actual LLM calls
        switch ($step) {
            case 1: // Job profile analysis
                return [
                    'content' => json_encode([
                        'job_title' => $job->title,
                        'company' => $job->company,
                        'requirements' => ['Placeholder requirement analysis'],
                        'keywords' => ['placeholder', 'keywords'],
                        'analysis_timestamp' => now()->toISOString(),
                    ], JSON_PRETTY_PRINT),
                    'mime' => 'application/json',
                    'tokens_input' => 150,
                    'tokens_output' => 75,
                ];

            case 9: // Resume diff (markdown)
                return [
                    'content' => "# Resume Tailoring Diff\n\n## Changes Made\n- Placeholder diff content\n- Added relevant keywords\n- Enhanced job-specific experience",
                    'mime' => 'text/markdown',
                    'tokens_input' => 200,
                    'tokens_output' => 100,
                ];

            case 10: // Final resume (HTML)
                return [
                    'content' => "<html><body><h1>Tailored Resume</h1><p>Placeholder final resume content</p></body></html>",
                    'mime' => 'text/html',
                    'tokens_input' => 300,
                    'tokens_output' => 150,
                ];

            default: // Other steps (JSON artifacts)
                return [
                    'content' => json_encode([
                        'step' => $step,
                        'placeholder' => true,
                        'timestamp' => now()->toISOString(),
                    ], JSON_PRETTY_PRINT),
                    'mime' => 'application/json',
                    'tokens_input' => 100,
                    'tokens_output' => 50,
                ];
        }
    }

    /**
     * Link selected resumes to the tailoring run
     * 
     * @param JobSeekerAiTailorRun $run The run to link resumes to
     * @param Collection<JobSeekerResume> $resumes Selected resumes
     */
    private function linkResumesToRun(JobSeekerAiTailorRun $run, Collection $resumes): void
    {
        $resumeIds = [];
        $position = 1;

        foreach ($resumes as $resume) {
            $resumeIds[$resume->id] = ['position_order' => $position++];
        }

        $run->resumes()->sync($resumeIds);

        Log::debug('AiTailorService: Linked resumes to run', [
            'correlation_id' => $run->correlation_id,
            'tailor_run_id' => $run->id,
            'resume_count' => count($resumeIds),
            'resume_ids' => array_keys($resumeIds),
        ]);
    }

    /**
     * Find existing run by idempotency key
     * 
     * @param int $userId User ID
     * @param int $jobId Job ID
     * @param string $idempotencyKey Idempotency key
     * @return JobSeekerAiTailorRun|null Existing run if found
     */
    private function findExistingRun(int $userId, int $jobId, string $idempotencyKey): ?JobSeekerAiTailorRun
    {
        // For this implementation, we'll store the idempotency key in guardrail_flags
        // In a production system, you might want a dedicated column
        return JobSeekerAiTailorRun::where('job_seeker_id', $userId)
            ->where('job_id', $jobId)
            ->whereJsonContains('guardrail_flags', ['idempotency_key' => $idempotencyKey])
            ->where('created_at', '>', now()->subHours(24)) // Only check last 24 hours
            ->first();
    }

    /**
     * Calculate total input tokens across all artifacts
     * 
     * @param JobSeekerAiTailorRun $run The run to calculate for
     * @return int Total input tokens
     */
    private function calculateTotalTokensInput(JobSeekerAiTailorRun $run): int
    {
        return $run->artifacts()->sum('tokens_input') ?? 0;
    }

    /**
     * Calculate total output tokens across all artifacts
     * 
     * @param JobSeekerAiTailorRun $run The run to calculate for
     * @return int Total output tokens
     */
    private function calculateTotalTokensOutput(JobSeekerAiTailorRun $run): int
    {
        return $run->artifacts()->sum('tokens_output') ?? 0;
    }
}

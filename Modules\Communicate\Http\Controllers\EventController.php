<?php

namespace Modules\Communicate\Http\Controllers;

use Illuminate\Routing\Controller;

use App\User;
use App\Event;
use App\Employee;
use App\Student;
use App\Guardian;
use App\YearCheck;
use App\Notification;
use App\ApiBaseMethod;
use Illuminate\Http\Request;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class EventController extends Controller
{
    public function __construct()
	{
//        $this->middleware('PM');
        // User::checkAuth();
	}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $events = Event::get();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendResponse($events, null);
            }
            return view('modules.site.templates.wajeha.backEnd.events.eventsList', compact('events'));
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function store(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'event_title' => "required",
            'for_whom' => "required",
            'from_date' => "required",
//            'to_date' => "required",
            'event_des' => "required",
            'event_location' => 'required',
            'upload_file_name' => "sometimes|nullable|mimes:pdf,doc,docx,jpg,jpeg,png|max:10000",
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $fileName = "";
            if ($request->file('upload_file_name') != "") {
                $file = $request->file('upload_file_name');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/events/', $fileName);
                $fileName = 'public/uploads/events/' . $fileName;
            }
            $user = Auth()->user();

            if ($user) {
                $login_id = $user->id;
            } else {
                $login_id = $request->login_id;
            }

            $eventDates = explode(',',$request->from_date);
            $startDate = $eventDates[0];
            $endDate = $eventDates[1];




            $events = new Event();
            $events->event_title = $request->event_title;
            $events->for_whom = $request->for_whom;
            $events->event_des = $request->event_des;
            $events->event_location = $request->event_location;
            $events->from_date = date('Y-m-d H:i', strtotime($startDate)) ;
            $events->to_date = date('Y-m-d H:i', strtotime($endDate));
            $events->created_by = $login_id;
            $events->uplad_image_file = $fileName;
            $events->organization_id = Auth::user()->organization_id;
//            $events->academic_id = YearCheck::getAcademicId();
            $results = $events->save();



            if ($request->for_whom == 'All') {

                $users = User::all();


                foreach ($users as $value) {

                    $notification = new Notification;
                    $notification->user_id = $value->id;
                    $notification->role_id = $value->role_id;
                    $notification->date = date('Y-m-d');
                    $notification->message = $request->event_title;
                    $notification->organization_id = Auth::user()->organization_id;
                    $notification->save();
                }
            }
            elseif ($request->for_whom == 'Teacher') {

                $users = User::where('role_id', 3 /** teacher **/)->get();


                foreach ($users as $value) {

                    $notification = new Notification;
                    $notification->user_id = $value->id;
                    $notification->role_id = $value->role_id;
                    $notification->date = date('Y-m-d');
                    $notification->message = $request->event_title;
                    $notification->organization_id = Auth::user()->organization_id;
                    $notification->save();
                }
            }
            elseif ($request->for_whom == 'Student') {
                $users =  User::whereHas("roles", function($q){ $q->where("name", "student"); })->get();
                dd($users);
//                $users = User::where('active_status', 1)->where('role_id', 2)->get();


                foreach ($users as $value) {

                    $notification = new Notification;
                    $notification->user_id = $value->id;
                    $notification->role_id = $value->role_id;
                    $notification->date = date('Y-m-d');
                    $notification->message = $request->event_title;
                    $notification->organization_id = Auth::user()->organization_id;
                    $notification->save();
                }
            } elseif ($request->for_whom == 'Parents') {

                $users = User::role('parent')->get();


                foreach ($users as $value) {

                    $notification = new Notification;
                    $notification->user_id = $value->id;
                    $notification->role_id = $value->role_id;
                    $notification->date = date('Y-m-d');
                    $notification->message = $request->event_title;
                    $notification->organization_id = Auth::user()->organization_id;
                    $notification->save();
                }
            }





            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($results) {
                    return ApiBaseMethod::sendResponse(null, 'New Event has been added successfully');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
                }
            } else {
                if ($results) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function edit(Request $request, $id)
    {
        try {
            $editData = Event::find($id);
            $events = Event::get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['editData'] = $editData->toArray();
                $data['events'] = $events->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('modules.site.templates.wajeha.backEnd.events.eventsList', compact('editData', 'events'));
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function update(Request $request, $id)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'event_title' => "required",
            'for_whom' => "required",
            'from_date' => "required",
            'to_date' => "required",
            'event_des' => "required",
            'event_location' => "required",
            'upload_file_name' => "sometimes|nullable|mimes:pdf,doc,docx,jpg,jpeg,png|max:10000",

        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $fileName = "";
            if ($request->file('upload_file_name') != "") {
                $eventFile = Event::find($id);
                if ($eventFile->uplad_image_file != "") {
                    unlink($eventFile->uplad_image_file);
                }

                $file = $request->file('upload_file_name');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/events/', $fileName);
                $fileName = 'public/uploads/events/' . $fileName;
            }

            $user = Auth()->user();

            if ($user) {
                $login_id = $user->id;
            } else {
                $login_id = $request->login_id;
            }

            $events = Event::find($id);
            $events->event_title = $request->event_title;
            $events->for_whom = $request->for_whom;
            $events->event_des = $request->event_des;
            $events->event_location = $request->event_location;
            $events->from_date = date('Y-m-d', strtotime($request->from_date));
            $events->to_date = date('Y-m-d', strtotime($request->to_date));
            $events->updated_by = $login_id;
            $events->uplad_image_file = $fileName;
            $results = $events->update();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($results) {
                    return ApiBaseMethod::sendResponse(null, 'Event has been updated successfully');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
                }
            } else {
                if ($results) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect('event');
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function deleteEventView(Request $request, $id)
    {
        try {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {

                return ApiBaseMethod::sendResponse($id, null);
            }
            return view('modules.site.templates.wajeha.backEnd.events.deleteEventView', compact('id'));
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function deleteEvent(Request $request, $id)
    {

        try {
            $result = Event::destroy($id);

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($result) {
                    return ApiBaseMethod::sendResponse(null, 'Event has been deleted successfully');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
                }
            } else {
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect('event');
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}
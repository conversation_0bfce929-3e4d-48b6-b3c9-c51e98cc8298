# Job Notification System: Monitoring, Logging, and Error Handling

This document describes the robust monitoring, logging, and error handling mechanisms implemented for the job notification system.

## Architecture Overview

The job notification monitoring system consists of:

1. **Failure Tracking Database**: Stores detailed records of failures for analysis and retries.
2. **Health Metrics**: Collects performance and reliability metrics over time.
3. **Monitoring Service**: Centralized service for tracking issues and coordinating recovery.
4. **Automatic Retry Mechanism**: Exponential backoff-based retry system for failed operations.
5. **Admin Reporting**: Email notifications and health reports for system administrators.
6. **Command-line Tools**: For monitoring, troubleshooting, and manual interventions.

## Database Tables

### 1. job_notification_failures

Stores detailed information about each failure in the system:

- `id`: Unique identifier
- `setup_id`: Related notification setup ID
- `job_id`: Related job ID
- `recipient_email`: Recipient email address
- `error_type`: Type of error (email_send, processing, database, etc.)
- `error_message`: Detailed error message
- `retry_count`: Number of retry attempts
- `last_retry_at`: Timestamp of the last retry
- `status`: Current status (pending, retrying, failed, resolved)
- `stack_trace`: Stack trace for debugging
- `additional_data`: JSON data with contextual information
- `created_at`/`updated_at`: Timestamps

### 2. job_notification_health_metrics

Tracks system health metrics over time:

- `id`: Unique identifier
- `metric_date`: Date of metrics
- `metric_hour`: Hour of metrics (0-23)
- `setups_processed`: Number of setups processed
- `recipients_processed`: Number of recipients processed
- `emails_sent`: Number of emails sent successfully
- `emails_failed`: Number of emails that failed
- `processing_time_ms`: Total processing time in milliseconds
- `avg_email_time_ms`: Average email sending time in milliseconds
- `max_memory_used_mb`: Maximum memory usage in MB
- `created_at`/`updated_at`: Timestamps

## Core Components

### JobNotificationMonitoringService

Central service responsible for:

- Tracking failures and errors
- Scheduling retries with exponential backoff
- Collecting and analyzing system health metrics
- Sending alerts and reports to administrators

Key methods:
- `trackFailure()`: Records failure details
- `processRetries()`: Processes pending retries
- `trackEmailMetrics()`: Records email performance metrics
- `getSystemHealth()`: Retrieves health overview
- `sendSystemAlert()`: Sends immediate alerts for serious issues
- `sendSystemHealthReport()`: Sends detailed periodic reports

### RetryFailedNotificationJob

Laravel job for retrying failed operations:

- Intelligent retry strategies based on error type
- Exponential backoff for retry timing
- Detailed logging for tracking retry progress
- Automatic determination of when to abandon retries

## Error Handling Strategy

### Error Classification

Errors are classified by type to determine appropriate handling:

1. **Transient Errors** (network issues, timeouts):
   - Automatic retries with exponential backoff
   - Up to 3 retry attempts (15 min, 1 hour, 4 hours)

2. **Permanent Errors** (invalid data, quota exceeded):
   - No automatic retries
   - Admin notification for manual intervention

3. **System Errors** (database failures, memory issues):
   - Logged with high priority
   - Immediate admin notification

### Monitoring Thresholds

The system triggers alerts based on:

- Absolute error count: Alert if more than 5 failures occur within an hour
- Error rate: Alert if error rate exceeds 10% of total operations

## Scheduled Tasks

The following tasks are scheduled to maintain system health:

1. **Retry Processing** (every 30 minutes):
   ```
   general:process-notification-retries --limit=100
   ```
   Processes pending retries with a limit of 100 per run

2. **Daily Health Report** (8:00 AM daily):
   ```
   general:monitor-job-notifications --days=1 --send-report
   ```
   Generates and emails a comprehensive health report to administrators

3. **Hourly Health Check** (15 minutes past each hour):
   ```
   general:monitor-job-notifications --days=1
   ```
   Performs quick health check without sending email reports

## Logging Strategy

The system implements extensive logging:

1. **Error Logs**:
   - Detailed error messages
   - Stack traces for debugging
   - Contextual information (setup ID, job ID, recipient)
   - Related metrics for troubleshooting

2. **Processing Logs**:
   - Entry/exit points for key operations
   - Performance metrics (timing, memory usage)
   - Volume indicators (emails sent, recipients processed)

3. **Retry Logs**:
   - Retry attempts with timing information
   - Success/failure status of retries
   - Final disposition of failed operations

## Administrator Reports

### System Alerts

Immediate notifications sent for critical issues:

- High failure rates
- System resource issues
- Persistent failures with specific setup/job

### Health Reports

Daily reports with comprehensive system statistics:

- Processing volume (setups, recipients, emails)
- Performance metrics (timing, memory usage)
- Error rates and patterns
- Failure summaries by error type

## Manual Intervention Tools

For cases requiring administrator intervention:

1. **Monitoring Command**:
   ```
   php artisan general:monitor-job-notifications --days=7
   ```
   Shows detailed system health data for the past week

2. **Retry Processing**:
   ```
   php artisan general:process-notification-retries --limit=200
   ```
   Manually process a larger batch of retries

## Best Practices for Maintenance

1. **Regular Monitoring**:
   - Review daily health reports
   - Watch for patterns in failure types

2. **Database Maintenance**:
   - Clean up old failure records (older than 30 days)
   - Monitor database size growth

3. **Performance Tuning**:
   - Adjust retry intervals based on observed recovery rates
   - Optimize queue worker configuration for retry processing

4. **Alert Configuration**:
   - Update admin email list in configuration
   - Adjust alerting thresholds based on system volume

## Conclusion

This comprehensive monitoring, logging, and error handling system provides:

1. **Reliability**: Automatic recovery from transient failures
2. **Visibility**: Detailed metrics and logs for troubleshooting
3. **Proactive Management**: Early alerts about potential issues
4. **Accountability**: Complete audit trail of system operations

The system is designed to scale with notification volume while maintaining robustness through intelligent error handling and recovery mechanisms. 
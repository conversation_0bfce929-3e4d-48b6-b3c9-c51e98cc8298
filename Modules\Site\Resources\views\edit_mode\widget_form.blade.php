<form id="update_widget">
{{ csrf_field() }}
@if(isset($widget) && isset($widget['type']))

    <div id="widget_settings">
    @foreach($elements['settings'] as $element)
            @include('site::edit_mode.form_components.'.$element['field'] , $element['setting'])  
    @endforeach
    </div>

    @if(isset($elements['number_of_blocks']))
        @include('site::edit_mode.form_components.'.$elements['number_of_blocks']['field'] , $elements['number_of_blocks']['setting'])    
    @endif

    <div id="widget_blocks">
    @isset($elements['block_elements'])
        @include('site::edit_mode.widget_blocks', $elements['block_elements'])        
    @endisset
    </div>

@endif

<button type="button" class="btn btn-danger pull-right" onclick="updateWidget()">Save</button>
</form>

<link rel="stylesheet" href="{{ asset('assets/common/fontawesome-iconpicker/css/fontawesome-iconpicker.min.css')}}">
<script src="{{ asset('assets/lfm/js/lfm.js') }}"></script>
<script src="{{ asset('assets/common/fontawesome-iconpicker/js/fontawesome-iconpicker.min.js')}}"></script>

<script>

var updateWidget = function(){
    $.ajax({
        type: "post",
        url: "{{ route('update_widget') }}",
        data: $('#update_widget').serialize(),
        dataType: "json",
        success: function (response) {
            if(response.status == 'success'){
                window.location.reload();
            }
            console.log(response);            
        }
    });
}

var getBlocks = function(){
    var number_of_blocks = $('#{{ $widget['name'] }}_number_of_blocks').val();
    var current_blocks = $('#widget_blocks').children().length;
    var data = {
        _token : '{{ csrf_token()}}',
        starting_block : current_blocks+1,
        last_block : number_of_blocks,
        widget_name : '{{ $widget['name'] }}'
    };
    $.ajax({
        type: "post",
        url: "{{ '/' . app()->getLocale() . '/edit-mode/widget-blocks' }}",
        data: data,
        beforeSend: function(){
            $('#update_widget').append('<div id="loading_bg"><i class="fa fa-spinner fa-spin"></i></div>');
        },
        complete:function(){
            $('#loading_bg').remove();
        },
        success: function (response) {
            $('#widget_blocks').append(response);
            {{--  if(response.status == 'success'){
                window.location.reload();
            }  --}}
            {{--  console.log(response);              --}}
        }
    });
}
$('#{{ $widget['name'] }}_number_of_blocks').change(function(){
    var current_blocks = $('#widget_blocks').children().length;
    var number_of_blocks = $(this).val();
    if(number_of_blocks <= current_blocks ){
        for(var x = (parseInt(number_of_blocks) + 1); x <= current_blocks ; x++){
            $('#block'+x).remove();
        }
    }else{
        getBlocks();
    }
});
@if(isset($widget) && isset($widget['type']) && $widget['type'] == 'blocks')

$(document).ready(function(){
    getBlocks();
});
@endif
</script>

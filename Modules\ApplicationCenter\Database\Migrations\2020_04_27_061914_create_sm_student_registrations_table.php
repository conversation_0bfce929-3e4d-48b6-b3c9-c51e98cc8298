<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateStudentRegistrationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('application_center', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();

            $table->integer('class_id')->unsigned();
            $table->foreign('class_id')->references('id')->on('classes')->onDelete('cascade');

            $table->integer('section_id')->unsigned();
//            $table->foreign('section_id')->references('id')->on('sections')->onDelete('cascade');

            $table->date('date_of_birth')->nullable();

            $table->string('age')->nullable();

            $table->integer('academic_year')->nullable();

            $table->integer('gender_id')->nullable()->unsigned();
//            $table->foreign('gender_id')->references('id')->on('base_setups')->onDelete('cascade');

            $table->string('student_email')->nullable();
            $table->string('student_mobile')->nullable();

            $table->string('guardian_name')->nullable();
            $table->string('guardian_mobile')->nullable();
            $table->string('guardian_email')->nullable();
            $table->string('guardian_relation')->nullable()->comment('F father, M mother, O other');

            $table->text('how_do_know_us')->nullable();

            $table->integer('created_by')->nullable()->default(1)->unsigned();
            $table->integer('updated_by')->nullable()->default(1)->unsigned();
            $table->integer('organization_id')->nullable()->default(1)->unsigned();

            $table->integer('academic_id')->nullable()->default(1)->unsigned();
            $table->foreign('academic_id')->references('id')->on('academic_years')->onDelete('cascade');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('application_center');
    }
}

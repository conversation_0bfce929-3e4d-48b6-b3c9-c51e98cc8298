<?php

declare(strict_types=1);

namespace App\Providers;

use App\Services\StudentImageService;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class StudentImageServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        $this->app->singleton(StudentImageService::class, function ($app) {
            return new StudentImageService();
        });

        // Register the facade alias
        $this->app->alias(StudentImageService::class, 'student-image');
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        // Register blade directive
        Blade::directive('studentPhoto', function ($expression) {
            return "<?php echo app('student-image')->getStudentImageUrl($expression); ?>";
        });
        
        Blade::directive('studentPhotoHtml', function ($expression) {
            return "<?php echo app('student-image')->getStudentImageHtml($expression); ?>";
        });
    }
} 
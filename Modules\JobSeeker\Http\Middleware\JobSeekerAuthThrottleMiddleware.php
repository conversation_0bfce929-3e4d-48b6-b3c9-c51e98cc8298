<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Middleware;

use Illuminate\Routing\Middleware\ThrottleRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Modules\JobSeeker\Entities\JobSeekerSetting;
use Closure;

/**
 * JobSeeker Authentication Rate Limiting Middleware
 * 
 * Implements rate limiting for authentication-related endpoints with:
 * - 5 login attempts per 10 minutes
 * - 3 registration attempts per 30 minutes
 * - Exponential backoff for repeated violations
 */
final class JobSeekerAuthThrottleMiddleware extends ThrottleRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  mixed  ...$parameters
     * @return mixed
     */
    public function handle($request, Closure $next, ...$parameters)
    {
        // Check if rate limiting is enabled
        $rateLimitingEnabled = JobSeekerSetting::getValue('login_rate_limiting_enabled', 'true') === 'true';
        if (!$rateLimitingEnabled) {
            Log::info('JobSeeker rate limiting is disabled, allowing request through', [
                'ip' => $request->ip(),
                'route' => $request->route()?->getName(),
            ]);
            return $next($request);
        }

        $route = $request->route();
        $routeName = $route ? $route->getName() : '';
        $routeUri = $request->getRequestUri();

        // Determine rate limit based on the action type
        $rateLimits = $this->getRateLimitsForAction($request, $routeName, $routeUri);

        if (!$rateLimits) {
            return $next($request);
        }
        
        $maxAttempts = $rateLimits['max_attempts'];
        $decayMinutes = $rateLimits['decay_minutes'];
        $action = $rateLimits['action'];
        
        // Create a unique key for this client and action
        $key = $this->buildRateLimitKey($request, $action);
        
        // Check for exponential backoff
        $backoffKey = $key . ':backoff';
        $backoffUntil = Cache::get($backoffKey);
        
        if ($backoffUntil && $backoffUntil > now()) {
            Log::warning('JobSeeker authentication attempt blocked due to exponential backoff', [
                'action' => $action,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'backoff_until' => $backoffUntil,
                'login_field' => $request->input('login', $request->input('email'))
            ]);
            
            return $this->buildException($request, $key, $maxAttempts, $backoffUntil->diffInSeconds(now()));
        }
        
        // Apply the rate limiting
        $response = parent::handle($request, $next, $maxAttempts, $decayMinutes, $key);
        
        // If the response indicates rate limiting was hit, implement exponential backoff
        if ($response->getStatusCode() === 429) {
            $this->implementExponentialBackoff($key, $action, $request);
        }
        
        return $response;
    }

    /**
     * Get rate limits for the specific action
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $routeName
     * @param  string  $routeUri
     * @return array|null
     */
    protected function getRateLimitsForAction(Request $request, string $routeName, string $routeUri): ?array
    {
        // Login endpoints
        if ($this->isLoginAction($request, $routeName, $routeUri)) {
            return [
                'max_attempts' => 5,
                'decay_minutes' => 10,
                'action' => 'login'
            ];
        }
        
        // Registration endpoints
        if ($this->isRegistrationAction($request, $routeName, $routeUri)) {
            return [
                'max_attempts' => 3,
                'decay_minutes' => 30,
                'action' => 'registration'
            ];
        }
        
        // Social login endpoints
        if ($this->isSocialLoginAction($request, $routeName, $routeUri)) {
            return [
                'max_attempts' => 5,
                'decay_minutes' => 10,
                'action' => 'social_login'
            ];
        }
        
        // Password reset endpoints
        if ($this->isPasswordResetAction($request, $routeName, $routeUri)) {
            return [
                'max_attempts' => 3,
                'decay_minutes' => 15,
                'action' => 'password_reset'
            ];
        }
        
        return null;
    }

    /**
     * Check if this is a login action
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $routeName
     * @param  string  $routeUri
     * @return bool
     */
    protected function isLoginAction(Request $request, string $routeName, string $routeUri): bool
    {
        return $request->isMethod('POST') && 
               (str_contains($routeUri, '/jlogin') || 
                $routeName === 'jobseeker.login' ||
                str_contains($routeUri, '/login'));
    }

    /**
     * Check if this is a registration action
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $routeName
     * @param  string  $routeUri
     * @return bool
     */
    protected function isRegistrationAction(Request $request, string $routeName, string $routeUri): bool
    {
        return $request->isMethod('POST') && 
               (str_contains($routeUri, '/jregister') || 
                $routeName === 'jobseeker.register' ||
                str_contains($routeUri, '/register'));
    }

    /**
     * Check if this is a social login action
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $routeName
     * @param  string  $routeUri
     * @return bool
     */
    protected function isSocialLoginAction(Request $request, string $routeName, string $routeUri): bool
    {
        return str_contains($routeUri, '/jauth/google') || 
               $routeName === 'jobseeker.auth.google' ||
               $routeName === 'jobseeker.auth.google.callback';
    }

    /**
     * Check if this is a password reset action
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $routeName
     * @param  string  $routeUri
     * @return bool
     */
    protected function isPasswordResetAction(Request $request, string $routeName, string $routeUri): bool
    {
        return $request->isMethod('POST') && 
               (str_contains($routeUri, '/jpr') || 
                $routeName === 'jobseeker.password.request' ||
                str_contains($routeUri, '/password/'));
    }

    /**
     * Build rate limit key for the specific action
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $action
     * @return string
     */
    protected function buildRateLimitKey(Request $request, string $action): string
    {
        $identifier = $request->ip();
        
        // For login attempts, also include the login field if provided
        if (in_array($action, ['login', 'registration'])) {
            $login = $request->input('login', $request->input('email'));
            if ($login) {
                $identifier .= '|' . hash('sha256', strtolower($login));
            }
        }
        
        return 'jobseeker_auth_throttle:' . $action . ':' . $identifier;
    }

    /**
     * Resolve request signature for rate limiting (parent method override)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string
     */
    protected function resolveRequestSignature($request)
    {
        // Determine the action based on the request
        $action = $this->getActionFromRequest($request);
        
        return $this->buildRateLimitKey($request, $action);
    }

    /**
     * Determine the action from the current request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string
     */
    protected function getActionFromRequest(Request $request): string
    {
        $route = $request->route();
        $routeName = $route ? $route->getName() : '';
        $routeUri = $request->getRequestUri();
        
        if ($this->isLoginAction($request, $routeName, $routeUri)) {
            return 'login';
        }
        
        if ($this->isRegistrationAction($request, $routeName, $routeUri)) {
            return 'registration';
        }
        
        if ($this->isSocialLoginAction($request, $routeName, $routeUri)) {
            return 'social_login';
        }
        
        if ($this->isPasswordResetAction($request, $routeName, $routeUri)) {
            return 'password_reset';
        }
        
        return 'general';
    }

    /**
     * Implement exponential backoff for repeated violations
     *
     * @param  string  $key
     * @param  string  $action
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    protected function implementExponentialBackoff(string $key, string $action, Request $request): void
    {
        $backoffKey = $key . ':backoff';
        $violationsKey = $key . ':violations';
        
        // Get current violation count
        $violations = Cache::get($violationsKey, 0) + 1;
        
        // Calculate backoff time: 2^violations minutes, max 24 hours
        $backoffMinutes = min(pow(2, $violations), 1440);
        $backoffUntil = now()->addMinutes($backoffMinutes);
        
        // Store the backoff period and violation count
        Cache::put($backoffKey, $backoffUntil, $backoffUntil);
        Cache::put($violationsKey, $violations, now()->addDay());
        
        Log::warning('JobSeeker authentication exponential backoff applied', [
            'action' => $action,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'violations' => $violations,
            'backoff_minutes' => $backoffMinutes,
            'backoff_until' => $backoffUntil,
            'login_field' => $request->input('login', $request->input('email'))
        ]);
    }
} 
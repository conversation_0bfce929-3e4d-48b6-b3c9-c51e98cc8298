<?php

namespace App\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class ClearStudentsCache
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        // Assuming class_id is available in the event
        $classId = $event->class_id;

        $cacheKey = "students_in_class_{$classId}";
        \Cache::forget($cacheKey);
    }
}


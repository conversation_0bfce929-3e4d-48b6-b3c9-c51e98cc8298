<?php

namespace Modules\Education\Http\Controllers;

use App\Classes;
use App\Employee;
use App\Student;
use App\StudentRevisionReport;
use App\Weekend;
use App\ClassRoom;
use App\YearCheck;
use App\ApiBaseMethod;
use Illuminate\Http\Request;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use PDF; // assuming you have the alias set for DOMPDF

class StudentTableNouranyaPDFController extends Controller
{

    public function __construct()
	{
//        $this->middleware('PM');
        // User::checkAuth();
	}

    public function downloadPDF($studentId,$classId,$monthYear)
    {
        // Convert the monthYear string to a Carbon instance
        $date = \Carbon\Carbon::createFromFormat('F Y', $monthYear);

        // Extract the month and year separately
        $month = $date->format('m'); // Numeric representation of the month (e.g., 06)
        $year = $date->format('Y'); // 4-digit year (e.g., 2023)
        $letterHead = asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg");
        $letterFooter = asset("uploads/settings/footer.png");
        $student = Student::find($studentId);
        $fullName = $student->full_name; // Assuming the column name is 'full_name'
        $class = Classes::find($classId);
        $centerName = $class->center->name;
        $className = Classes::find($classId)->name;
        $classTeachers = $class->teachers()->pluck('full_name')->toArray();
        $data = [
            'centerName' => $centerName,
            'letterHead' => $letterHead,
            'letterFooter' => $letterFooter,
            'studentName' => $fullName,
            'classTeachers' => $classTeachers,
            'monthYear' => $date,
            'studentId' =>  $studentId,  // Retrieve and assign the required data
            'classId' => $classId ,  // Retrieve and assign the required data
            'year' => $year ,  // Retrieve and assign the required data
            'month' => $month ,  // Retrieve and assign the required data
            'className' => $className ,   // Retrieve and assign the required data
            'classTeacher' => $class->teachers()->pluck('full_name')->toArray() ,  // Retrieve and assign the required data
            // Add other required data
        ];

        view()->share('education::classes.reports.pdf.student.nouranya',$data);

        $pdf = PDF::loadView('education::classes.reports.pdf.student.nouranya', $data)->setPaper('a4');
        
        // Define file path and name without the time component
        $filename = "student_nouranya_report_{$month}_{$year}.pdf";

        return $pdf->download($filename);
    }
} 
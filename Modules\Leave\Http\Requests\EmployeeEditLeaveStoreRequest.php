<?php

namespace Modules\Leave\Http\Requests;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class EmployeeEditLeaveStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {






        $this->merge([

            'apply_date' => Carbon::parse($this->apply_date)->format('Y-m-d'),
            'start_date' => Carbon::parse($this->start_date)->format('Y-m-d'),
            'end_date' => Carbon::parse($this->end_date)->format('Y-m-d'),
            'makeup_date' => Carbon::parse($this->makeup_date)->format('Y-m-d'),
        ]);







        return [

            'leave_type_id' => 'required',
            'reason' => 'required|max:255',
            'attachment' => 'nullable|mimes:jpeg,jep,png,docx,txt,pdf',
            'apply_date' => 'required',
            'start_date' => 'required',
            'day' => 'required',
            'from_day' => 'required_if:day,==,0',
            'end_date' => 'required_if:day,==,2',
        ];
    }

    public function messages()
    {
        return [
            'leave_type_id.required' => 'Please select the leave type',
            'from_day.required_if' => 'This field is required when day is half.',
            'end_date.required_if' => 'This field is required when day is half.',
            'apply_date.required' => 'Please select the leave application date.',
            'start_date.required' => 'Please select the leave from date.',
            'reason.required' => 'Please provide a valid reason.',



        ];

    }

    public function attributes()
    {
        return [
            'leave_type_id' => 'Leave Type',
            'reason' => 'Reason',
            'apply_date' => 'Apply Date',
        ];
    }



    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}

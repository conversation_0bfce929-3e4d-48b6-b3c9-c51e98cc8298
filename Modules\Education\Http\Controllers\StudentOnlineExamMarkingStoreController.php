<?php

namespace Modules\Education\Http\Controllers;

use App\Classes;
use App\Employee;
use App\Guardian;
use App\Section;
use App\ExamAttendance;
use App\Student;
use App\Subject;
use App\YearCheck;
use App\OnlineExam;
use App\ApiBaseMethod;
use App\Notification;
use App\QuestionBank;
use App\AssignSubject;
use App\OnlineExamMark;
use App\GeneralSettings;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\OnlineExamQuestion;
use App\StudentTakeOnlineExam;
use Illuminate\Support\Facades\DB;
use App\OnlineExamQuestionAssign;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use App\OnlineExamQuestionMuOption;
use Illuminate\Support\Facades\Schema;
use App\OnlineExamStudentAnswerMarking;
use Illuminate\Support\Facades\Validator;

use App\Http\Controllers\Controller;


class StudentOnlineExamMarkingStoreController extends Controller
{


    public function __invoke(Request $request)
    {
        try{

            $class_id = $request->class_id;
            $exam_id = $request->exam_id;

            $marks_register = new MarkStore();
            $marks_register->exam_term_id           =       $exam_id;
            $marks_register->class_id               =       $class_id;
            $marks_register->section_id             =       $section_id;
            $marks_register->subject_id             =       $subject_id;
            $marks_register->student_id             =       $sid;
            $marks_register->created_at = YearCheck::getYear() . '-' . date('m-d h:i:s');
            $marks_register->total_marks            =       $mark_by_exam_part;
            $marks_register->exam_setup_id          =       $exam_setup_id;
            if (isset($request->absent_students)) {
                if (in_array($sid, $request->absent_students)) {
                    $marks_register->is_absent              =       1;
                } else {
                    $marks_register->is_absent              =       0;
                }
            }

            $marks_register->teacher_remarks          =       $request->teacher_remarks[$sid][$subject_id];


            $marks_register->created_at = YearCheck::getYear() . '-' . date('m-d h:i:s');
            $marks_register->school_id = Auth::user()->school_id;
            $marks_register->academic_id = getAcademicId();

            $marks_register->save();
            $marks_register->toArray();

                $exam_attendance = new ExamAttendance();
           

            $exam_attendance->exam_id = $request->exam_id;
            $exam_attendance->class_id = $request->class_id;
            $exam_attendance->created_at = YearCheck::getYear() . '-' . date('m-d h:i:s');
            $exam_attendance->organization_id = config('organization_id');
            $exam_attendance->save();
            $exam_attendance->toArray();

            $take_online_exam = new StudentTakeOnlineExam();
            $take_online_exam->online_exam_id = $request->online_exam_id;
            $take_online_exam->student_id = $student->id;
            $take_online_exam->status = 1;
            $take_online_exam->organization_id = Auth::user()->organization_id;
            $take_online_exam->academic_id =  YearCheck::getAcademicId();
            $take_online_exam->save();
            $take_online_exam->toArray();

            $online_take_exam_mark = StudentTakeOnlineExam::where('online_exam_id', $request->online_exam_id)->where('student_id', $request->student_id)->where('academic_id', getAcademicId())->first();

            $online_take_exam_mark->total_marks=$request->marks;
            $online_take_exam_mark->status=2;
            $online_take_exam_mark->save();

            $wrong=OnlineExamStudentAnswerMarking::where('user_answer','=','')->delete();

            Toastr::success('Operation successful', 'Success');
            return redirect('online-exam-marks-register/' . $request->online_exam_id);
        }catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

}
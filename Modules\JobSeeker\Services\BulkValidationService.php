<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Exception;
use Modules\JobSeeker\Entities\JobProvider;

/**
 * BulkValidationService
 * 
 * Handles comprehensive validation for bulk operations on command schedule rules
 */
final class BulkValidationService
{
    /**
     * Maximum number of rules allowed per bulk operation
     */
    const MAX_BULK_RULES = 50;

    /**
     * Allowed fields for bulk editing
     */
    const ALLOWED_BULK_FIELDS = [
        'name',
        'schedule_expression',
        'schedule_time',
        'schedule_type',
        'day_of_week',
        'priority',
        'is_active',
        'description',
        'provider_category_ids',
        'provider_location_ids',
        // Filter fields
        'categories',
        'locations',
        'companies',
        'experience_levels',
        'search_term',
        'work_type',
        'allow_non_english'
    ];

    /**
     * Allowed time offset bounds (in minutes)
     */
    const MIN_TIME_OFFSET = 1;
    const MAX_TIME_OFFSET = 1440; // 24 hours

    /**
     * Dynamically resolve allowed providers (active provider slugs)
     */
    private function allowedProviderSlugs(): array
    {
        try {
            $base = JobProvider::query()
                ->active()
                ->pluck('slug')
                ->map(fn($s) => strtolower((string) $s))
                ->unique()
                ->values()
                ->all();

            // Add lenient variants (remove dots, dashes, underscores) to accept common synonyms like 'jobsaf'
            $variants = [];
            foreach ($base as $slug) {
                $variants[] = $slug;
                $compact = preg_replace('/[^a-z0-9]+/i', '', $slug);
                if ($compact && $compact !== $slug) {
                    $variants[] = strtolower($compact);
                }
            }

            return array_values(array_unique($variants));
        } catch (Exception $e) {
            Log::warning('BulkValidationService: Failed to resolve providers from DB, falling back to defaults', [
                'error' => $e->getMessage()
            ]);
            return ['jobsaf', 'acbar'];
        }
    }

    /**
     * Validate inline field update request
     *
     * @param array $data
     * @return array
     */
    public function validateFieldUpdate(array $data): array
    {
        try {
            $validator = Validator::make($data, [
                'rule_id' => 'required|integer|exists:command_schedule_rules,id',
                'field' => ['required', 'string', Rule::in(self::ALLOWED_BULK_FIELDS)],
                'value' => 'present' // Allow empty values for clearing fields
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'errors' => $validator->errors()->toArray(),
                    'message' => 'Validation failed',
                    'error_type' => 'validation'
                ];
            }

            // Field-specific validation (ensure integer rule id)
            $fieldValidation = $this->validateSpecificField($data['field'], $data['value'], (int) $data['rule_id']);
            
            if (!$fieldValidation['success']) {
                return $fieldValidation;
            }

            return [
                'success' => true,
                'validated_data' => [
                    'rule_id' => (int) $data['rule_id'],
                    'field' => $data['field'],
                    'value' => $fieldValidation['processed_value']
                ]
            ];

        } catch (Exception $e) {
            Log::error('BulkValidationService: Error validating field update', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Validation error: ' . $e->getMessage(),
                'error_type' => 'system'
            ];
        }
    }

    /**
     * Validate clone rules request
     *
     * @param array $data
     * @return array
     */
    public function validateCloneRequest(array $data): array
    {
        try {
            $providerSlugs = $this->allowedProviderSlugs();
            $validator = Validator::make($data, [
                'from_provider' => ['required', 'string', Rule::in($providerSlugs)],
                // Allow same-provider cloning as well as cross-provider
                'to_provider' => ['required', 'string', Rule::in($providerSlugs)],
                'time_offset_minutes' => ['required', 'integer', 'min:'.self::MIN_TIME_OFFSET, 'max:'.self::MAX_TIME_OFFSET],
                'rule_ids' => 'nullable|array|max:' . self::MAX_BULK_RULES,
                'rule_ids.*' => 'integer|exists:command_schedule_rules,id'
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'errors' => $validator->errors()->toArray(),
                    'message' => 'Validation failed'
                ];
            }

            // Additional business logic validation
            $businessValidation = $this->validateCloneBusinessRules($data);
            
            if (!$businessValidation['success']) {
                return $businessValidation;
            }

            return [
                'success' => true,
                'validated_data' => $data
            ];

        } catch (Exception $e) {
            Log::error('BulkValidationService: Error validating clone request', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Validation error: ' . $e->getMessage(),
                'error_type' => 'system'
            ];
        }
    }

    /**
     * Validate specific field based on field type
     *
     * @param string $field
     * @param mixed $value
     * @param int $ruleId
     * @return array
     */
    private function validateSpecificField(string $field, $value, int $ruleId): array
    {
        switch ($field) {
            case 'name':
                return $this->validateNameField($value, $ruleId);
                
            case 'schedule_time':
                return $this->validateScheduleTimeField($value);
                
            case 'priority':
                return $this->validatePriorityField($value);
                
            case 'is_active':
                return $this->validateBooleanField($value);
                
            case 'description':
                return $this->validateDescriptionField($value);
                
            case 'provider_category_ids':
            case 'provider_location_ids':
                return $this->validateArrayField($value, $field);

            // Filter fields
            case 'categories':
            case 'locations':
            case 'companies':
            case 'experience_levels':
                return $this->validateFilterArrayField($value, $field);

            case 'search_term':
                return $this->validateSearchTermField($value);

            case 'work_type':
                return $this->validateWorkTypeField($value);

            case 'allow_non_english':
                return $this->validateBooleanField($value);

            case 'schedule_expression':
                return $this->validateScheduleExpressionField($value);

            case 'schedule_type':
                return $this->validateScheduleTypeField($value);

            case 'day_of_week':
                return $this->validateDayOfWeekField($value);

            case 'timezone':
                return $this->validateTimezoneField($value);

            default:
                return [
                    'success' => false,
                    'message' => "Field '{$field}' is not supported for bulk editing",
                    'error_type' => 'validation'
                ];
        }
    }

    /**
     * Validate name field
     *
     * @param mixed $value
     * @param int $ruleId
     * @return array
     */
    private function validateNameField($value, int $ruleId): array
    {
        $validator = Validator::make(['name' => $value], [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('command_schedule_rules', 'name')->ignore($ruleId)
            ]
        ]);

        if ($validator->fails()) {
            return [
                'success' => false,
                'errors' => $validator->errors()->toArray(),
                'message' => 'Name validation failed'
            ];
        }

        return [
            'success' => true,
            'processed_value' => trim($value)
        ];
    }

    /**
     * Validate schedule time field
     *
     * @param mixed $value
     * @return array
     */
    private function validateScheduleTimeField($value): array
    {
        // Allow both 24-hour (HH:MM) and 12-hour (HH:MM AM/PM) formats
        $timeValue = trim($value);

        // Check 24-hour format (HH:MM)
        if (preg_match('/^(\d{1,2}):(\d{2})$/', $timeValue, $matches)) {
            $hour = (int) $matches[1];
            $minute = (int) $matches[2];

            if ($hour >= 0 && $hour <= 23 && $minute >= 0 && $minute <= 59) {
                return [
                    'success' => true,
                    'processed_value' => $value
                ];
            }
        }

        // Check 12-hour format (HH:MM AM/PM)
        if (preg_match('/^(\d{1,2}):(\d{2})\s*(AM|PM)$/i', $timeValue, $matches)) {
            $hour = (int) $matches[1];
            $minute = (int) $matches[2];

            if ($hour >= 1 && $hour <= 12 && $minute >= 0 && $minute <= 59) {
                return [
                    'success' => true,
                    'processed_value' => $value
                ];
            }
        }

        return [
            'success' => false,
            'message' => 'Schedule time must be in HH:MM format (24-hour) or HH:MM AM/PM format (12-hour)',
            'error_type' => 'validation'
        ];
    }

    /**
     * Validate priority field
     *
     * @param mixed $value
     * @return array
     */
    private function validatePriorityField($value): array
    {
        $validator = Validator::make(['priority' => $value], [
            'priority' => 'required|integer|min:1|max:1000'
        ]);

        if ($validator->fails()) {
            return [
                'success' => false,
                'errors' => $validator->errors()->toArray(),
                'message' => 'Priority must be between 1 and 1000'
            ];
        }

        return [
            'success' => true,
            'processed_value' => (int) $value
        ];
    }

    /**
     * Validate boolean field
     *
     * @param mixed $value
     * @return array
     */
    private function validateBooleanField($value): array
    {
        $boolValue = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
        
        if ($boolValue === null) {
            return [
                'success' => false,
                'message' => 'Value must be true or false',
                'error_type' => 'validation'
            ];
        }

        return [
            'success' => true,
            'processed_value' => $boolValue
        ];
    }

    /**
     * Validate description field
     *
     * @param mixed $value
     * @return array
     */
    private function validateDescriptionField($value): array
    {
        $validator = Validator::make(['description' => $value], [
            'description' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return [
                'success' => false,
                'errors' => $validator->errors()->toArray(),
                'message' => 'Description validation failed'
            ];
        }

        return [
            'success' => true,
            'processed_value' => $value ? trim($value) : null
        ];
    }

    /**
     * Validate array field (categories/locations)
     *
     * @param mixed $value
     * @param string $field
     * @return array
     */
    private function validateArrayField($value, string $field): array
    {
        if (!is_array($value)) {
            $value = [$value];
        }

        $tableName = $field === 'provider_category_ids' ? 'provider_job_categories' : 'provider_job_locations';
        
        $validator = Validator::make(['items' => $value], [
            'items' => 'array|max:50',
            'items.*' => "integer|exists:{$tableName},id"
        ]);

        if ($validator->fails()) {
            return [
                'success' => false,
                'errors' => $validator->errors()->toArray(),
                'message' => ucfirst(str_replace('_', ' ', $field)) . ' validation failed'
            ];
        }

        return [
            'success' => true,
            'processed_value' => array_map('intval', $value)
        ];
    }

    /**
     * Validate clone business rules
     *
     * @param array $data
     * @return array
     */
    private function validateCloneBusinessRules(array $data): array
    {
        // Check if source provider has rules to clone
        $commandMap = [
            'jobsaf' => 'jobseeker:sync-jobs-af',
            'acbar' => 'jobseeker:sync-acbar-jobs'
        ];

        $fromCommand = $commandMap[$data['from_provider']];
        
        $query = DB::table('command_schedule_rules')->where('command', $fromCommand);
        
        if (isset($data['rule_ids']) && !empty($data['rule_ids'])) {
            $query->whereIn('id', $data['rule_ids']);
        }

        $sourceRulesCount = $query->count();

        if ($sourceRulesCount === 0) {
            return [
                'success' => false,
                'message' => 'No rules found to clone from ' . strtoupper($data['from_provider']),
                'error_type' => 'business'
            ];
        }

        // Check if cloning would exceed reasonable limits
        if ($sourceRulesCount > self::MAX_BULK_RULES) {
            return [
                'success' => false,
                'message' => "Cannot clone more than " . self::MAX_BULK_RULES . " rules at once",
                'error_type' => 'business'
            ];
        }

        return ['success' => true];
    }

    /**
     * Get validation rules for different contexts
     *
     * @param string $context
     * @return array
     */
    public function getValidationRules(string $context): array
    {
        switch ($context) {
            case 'field_update':
                return [
                    'rule_id' => 'required|integer|exists:command_schedule_rules,id',
                    'field' => ['required', 'string', Rule::in(self::ALLOWED_BULK_FIELDS)],
                    'value' => 'required'
                ];
                
            case 'clone_rules':
                return [
                    'from_provider' => ['required', 'string', Rule::in(self::ALLOWED_PROVIDERS)],
                    'to_provider' => ['required', 'string', Rule::in(self::ALLOWED_PROVIDERS), 'different:from_provider'],
                    'time_offset_minutes' => ['required', 'integer', Rule::in(self::ALLOWED_TIME_OFFSETS)],
                    'rule_ids' => 'nullable|array|max:' . self::MAX_BULK_RULES,
                    'rule_ids.*' => 'integer|exists:command_schedule_rules,id'
                ];
                
            default:
                return [];
        }
    }

    /**
     * Format validation errors for API response
     *
     * @param array $errors
     * @return array
     */
    public function formatValidationErrors(array $errors): array
    {
        $formatted = [];
        
        foreach ($errors as $field => $messages) {
            $formatted[$field] = is_array($messages) ? $messages : [$messages];
        }
        
        return $formatted;
    }

    /**
     * Validate filter array field (categories, locations, companies, experience_levels)
     *
     * @param mixed $value
     * @param string $field
     * @return array
     */
    private function validateFilterArrayField($value, string $field): array
    {
        // Allow empty values for clearing filters
        if (empty($value)) {
            return [
                'success' => true,
                'processed_value' => []
            ];
        }

        // If it's a string, it should be comma-separated values
        if (is_string($value)) {
            return [
                'success' => true,
                'processed_value' => $value
            ];
        }

        // If it's an array, it should be valid
        if (is_array($value)) {
            return [
                'success' => true,
                'processed_value' => $value
            ];
        }

        return [
            'success' => false,
            'message' => "Field '{$field}' must be a string or array",
            'error_type' => 'validation'
        ];
    }

    /**
     * Validate search term field
     *
     * @param mixed $value
     * @return array
     */
    private function validateSearchTermField($value): array
    {
        // Allow empty values
        if (empty($value)) {
            return [
                'success' => true,
                'processed_value' => null
            ];
        }

        $validator = Validator::make(['search_term' => $value], [
            'search_term' => 'string|max:255'
        ]);

        if ($validator->fails()) {
            return [
                'success' => false,
                'errors' => $validator->errors()->toArray(),
                'error_type' => 'validation'
            ];
        }

        return [
            'success' => true,
            'processed_value' => trim($value)
        ];
    }

    /**
     * Validate work type field
     *
     * @param mixed $value
     * @return array
     */
    private function validateWorkTypeField($value): array
    {
        // Allow empty values
        if (empty($value)) {
            return [
                'success' => true,
                'processed_value' => null
            ];
        }

        $allowedWorkTypes = ['full-time', 'part-time', 'contract', 'freelance', 'internship'];

        $validator = Validator::make(['work_type' => $value], [
            'work_type' => ['string', Rule::in($allowedWorkTypes)]
        ]);

        if ($validator->fails()) {
            return [
                'success' => false,
                'errors' => $validator->errors()->toArray(),
                'error_type' => 'validation'
            ];
        }

        return [
            'success' => true,
            'processed_value' => $value
        ];
    }

    /**
     * Validate schedule expression field
     *
     * @param mixed $value
     * @return array
     */
    private function validateScheduleExpressionField($value): array
    {
        if (empty($value)) {
            return [
                'success' => false,
                'message' => 'Schedule expression cannot be empty',
                'error_type' => 'validation'
            ];
        }

        // Basic validation - should be a string
        if (!is_string($value)) {
            return [
                'success' => false,
                'message' => 'Schedule expression must be a string',
                'error_type' => 'validation'
            ];
        }

        return [
            'success' => true,
            'processed_value' => trim($value)
        ];
    }

    /**
     * Validate schedule type field
     *
     * @param mixed $value
     * @return array
     */
    private function validateScheduleTypeField($value): array
    {
        $allowedTypes = ['cron', 'daily_at', 'weekly_at', 'custom'];

        if (!in_array($value, $allowedTypes)) {
            return [
                'success' => false,
                'message' => 'Schedule type must be one of: ' . implode(', ', $allowedTypes),
                'error_type' => 'validation'
            ];
        }

        return [
            'success' => true,
            'processed_value' => $value
        ];
    }

    /**
     * Validate day of week field
     *
     * @param mixed $value
     * @return array
     */
    private function validateDayOfWeekField($value): array
    {
        // Allow empty value for clearing
        if (empty($value)) {
            return [
                'success' => true,
                'processed_value' => null
            ];
        }

        $dayOfWeek = (int) $value;

        if ($dayOfWeek < 0 || $dayOfWeek > 6) {
            return [
                'success' => false,
                'message' => 'Day of week must be between 0 (Sunday) and 6 (Saturday)',
                'error_type' => 'validation'
            ];
        }

        return [
            'success' => true,
            'processed_value' => $dayOfWeek
        ];
    }

    /**
     * Validate timezone field
     *
     * @param mixed $value
     * @return array
     */
    private function validateTimezoneField($value): array
    {
        $allowedTimezones = [
            'Asia/Kabul',
            'UTC',
            'Asia/Dubai',
            'Europe/London',
            'America/New_York'
        ];

        if (!in_array($value, $allowedTimezones)) {
            return [
                'success' => false,
                'message' => 'Timezone must be one of: ' . implode(', ', $allowedTimezones),
                'error_type' => 'validation'
            ];
        }

        return [
            'success' => true,
            'processed_value' => $value
        ];
    }
}

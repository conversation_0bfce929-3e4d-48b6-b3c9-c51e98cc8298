<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Center;
use App\CenterTranslation;
use App\EmployeeSalary;
use App\Exports\EmployeesAttendanceExport;
use App\LeaveDefine;
use App\LeaveType;
use App\Organization;
use App\Student;
use Carbon\Traits\Creator;
use Doctrine\DBAL\Cache\CacheException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Jenssegers\Agent\Agent;

use App\Employee;

use App\Authorizable;

use App\Attendance;
use Carbon\Carbon;
use App\LeaveRequest;
use App\Role;
use Maatwebsite\Excel\Excel;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\PhpWord;


class PerEmployeeAttendanceReportController extends Controller
{


        public function __invoke(Request $request,$employeeId = null,$month = null,$year = null, $date = null)
        {







            $hasSpecialRole = auth()->user()->hasAnyRole(['supervisor_'. config('organization_id') . '_', 'managing-director'. config('organization_id') . '_', 'human-resource_'. config('organization_id') . '_', 'it-officer_'. config('organization_id') . '_']);


            if($hasSpecialRole){

            // Get filters from the request with sensible defaults
            $roles = $request->filled('role_id')
                ? $request->get('role_id')
                : Role::where('type', 'regular_user')->pluck('id')->toArray();

            $m = $request->filled('month') ? $request->get('month') : $month;
            $y = $request->filled('year') ? $request->get('year') : $year;
            $employeeId = $request->filled('employee_id') ? $request->get('employee_id') : $employeeId;
            $teacherCenters = $request->teacherCenters;

            $teacherCenters =  is_array($teacherCenters) ? implode(',', $teacherCenters) : null;



            // Turn array of roles into a comma separated string
            $r = implode(',', $roles);

            /*
             * Adjusting the stored procedure is required. Here we call an adjusted stored
             * procedure "GetEmployeeDailyAttendanceReportByYearMonthRole" that returns daily
             * attendance details for each employee.
             */

            // Prepare parameters array. Additional filtering can be done within the procedure,
            // e.g., by employee id if given, and teacher centers when applicable.
            if ($employeeId) {


                $employeeParam = (is_array($employeeId) && !empty($employeeId)) ? implode(',', $employeeId) : $employeeId;


    //            dd([$m, $y, $r, $employeeParam, is_array($teacherCenters) ? implode(',', $teacherCenters) : null]);
                $attendanceRaw = DB::select(
                    'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
                    [$m, $y, $r, $employeeParam,$teacherCenters ]
                );

            } else {
                // Get records for all matching employees
                $attendanceRaw = DB::select(
                    'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
                    [$m, $y, $r, null, $teacherCenters]
                );
            }




            $attendance = $this->formatAttendanceData($attendanceRaw);

            // Prepare months array for the dropdown
            $months = [
                1  => 'January', 2  => 'February', 3  => 'March', 4  => 'April',
                5  => 'May',     6  => 'June',     7  => 'July',   8  => 'August',
                9  => 'September',10 => 'October', 11 => 'November', 12 => 'December'
            ];
            // Query employees with attendance records based on the filters
            $filteredEmployees = Employee::whereHas('attendance', function ($query) use ($m, $y) {

                $query->whereMonth('clock', $m)
                    ->whereYear('clock', $y);
            })
                ->when($roles, function ($query) use ($roles) {
                    $query->whereHas('roles', function ($roleQuery) use ($roles) {
                        $roleQuery->whereIn('roles.id', $roles);
                    });
                })
                ->when($teacherCenters, function ($query) use ($teacherCenters,$request) {
                    $query->whereHas('teacherCenter', function ($centerQuery) use ($teacherCenters,$request) {

                        $centerQuery->whereIn('cen_teachers.cen_id', $request->get('teacherCenters'));
                    });
                })
                ->orderBy('full_name')
                ->pluck('full_name', 'id')
                ->toArray();




                return view('humanresource::attendance.individual_employee_monthly_report_latest', compact('attendance', 'months', 'm', 'y', 'teacherCenters','hasSpecialRole','filteredEmployees'));

            }
            // for normal employees
            else{



                $m = $request->filled('month') ? $request->get('month') : $month;
                $y = $request->filled('year') ? $request->get('year') : $year;
                $employeeId = auth()->user()->id;
                    $attendanceRaw = DB::select(
                        'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
                        [$m, $y, null, $employeeId,null ]
                    );




                $attendance = $this->formatAttendanceData($attendanceRaw);

                // Prepare months array for the dropdown
                $months = [
                    1  => 'January', 2  => 'February', 3  => 'March', 4  => 'April',
                    5  => 'May',     6  => 'June',     7  => 'July',   8  => 'August',
                    9  => 'September',10 => 'October', 11 => 'November', 12 => 'December'
                ];



                return view('humanresource::attendance.self_individual_employee_monthly_report', compact('attendance', 'months', 'm', 'y'));


            }

        }
    public
    function monthly_report_search(Request $request)
    {

        $date = null;
        $id = $request->role;
        if ($id == '0') {
            return $this->monthly_report($request);
        }


        $employee = Employee::whereHas('roles', function ($q) use ($id) {

            return $q->where('id', $id);
        })->get();


        $role = Role::get();
        foreach ($employee as $item) {
            $report = $this->report($item->id, $date);
            extract($report);
            $items[] = $report;
        }


        $attendance = Attendance:: whereYear('created_at', '=', date('Y'))
            ->whereMonth('created_at', '=', date('m'))->orderBy('employee_id')
            ->get();


        return view(
            'humanresource::attendance.monthly_report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'role',
                'date',
                'items'
            )
        );


    }

    public function exportExcel(Request $request)
    {



        $hasSpecialRole = auth()->user()->hasAnyRole(['supervisor_'. config('organization_id') . '_', 'managing-director'. config('organization_id') . '_', 'human-resource_'. config('organization_id') . '_', 'it-officer_'. config('organization_id') . '_']);


        if($hasSpecialRole){



        // Collect filters
        $roles = $request->filled('role_id')
            ? implode(',', $request->get('role_id'))
//            : Role::where('type', 'regular_user')->pluck('id')->toArray();
            : null;

        // Do not default month/year now (placeholders used); they may be null if not provided.
        $m = $request->filled('month') ? $request->get('month') : null;
        $y = $request->filled('year') ? $request->get('year') : null;

        $employeeIds = $request->filled('employee_id') ? (array)$request->get('employee_id') : [];
        $teacherCenters = $request->filled('teacherCenters') ? (array)$request->get('teacherCenters') : [];

        $employeeParam = !empty($employeeIds) ? implode(',', $employeeIds) : null;
        $teacherCentersParam = !empty($teacherCenters) ? implode(',', $teacherCenters) : null;


        // Fetch the data via the stored procedure.
        $attendanceRaw = DB::select(
            'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
            [$m, $y, $roles, $employeeParam, $teacherCentersParam]
        );

        // Format raw data into the expected structure.
        $data = $this->formatAttendanceData($attendanceRaw);


        // Use your custom export class for Excel export.
        return \Maatwebsite\Excel\Facades\Excel::download(new EmployeesAttendanceExport($data), "attendance_report_{$m}_{$y}.xlsx");
        }

        // serving normal employees
        else{



            // Do not default month/year now (placeholders used); they may be null if not provided.
            $m = $request->filled('month') ? $request->get('month') : null;
            $y = $request->filled('year') ? $request->get('year') : null;

            $employeeIds = auth()->user()->id;
            // Fetch the data via the stored procedure.
            $attendanceRaw = DB::select(
                'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
                [$m, $y, null, $employeeIds, null]
            );

            // Format raw data into the expected structure.
            $data = $this->formatAttendanceData($attendanceRaw);


            // Use your custom export class for Excel export.
            return \Maatwebsite\Excel\Facades\Excel::download(new EmployeesAttendanceExport($data), "attendance_report_{$m}_{$y}.xlsx");

        }
    }


    public function exportWord(Request $request)
    {

        // 1) Identify if the user has a "special role"
        $hasSpecialRole = auth()->user()->hasAnyRole([
            'supervisor_' . config('organization_id') . '_',
            'managing-director' . config('organization_id') . '_',
            'human-resource_' . config('organization_id') . '_',
            'it-officer_' . config('organization_id') . '_'
        ]);


        // Example: gather year/month from request
        $m = $request->filled('month') ? $request->get('month') : null;
        $y = $request->filled('year') ? $request->get('year') : null;

        $teacherCenters = $request->input('teacherCenters', []);
        $locale = app()->getLocale();

        // Suppose you also get 'center_name' from the request or deduce it
        // Retrieve the centers, pluck the names, capitalize each name, and join them with commas
        $centerNames = Center::join('center_translations', function ($join) use ($locale) {
            $join->on('centers.id', '=', 'center_translations.center_id')
                ->where('center_translations.locale', '=', $locale);
        })
            ->whereIn('centers.id', $teacherCenters)
            ->pluck('center_translations.name')
            ->map(function ($name) {
                return ucwords(strtolower($name));
            })
            ->implode(', ');


        // 4) Build an array of employeeIds based on role or normal user



            // 4) Build an array of employeeIds based on role or normal user
            if ($hasSpecialRole) {
                // If special role, gather from request
                $roles = $request->filled('role_id')
                    ? implode(',', $request->get('role_id'))
                    : null;

                $employeeIds = $request->filled('employee_id') ? (array)$request->get('employee_id') : [];
                $teacherCentersArr = $request->filled('teacherCenters') ? (array)$request->get('teacherCenters') : [];

                $employeeParam = !empty($employeeIds) ? implode(',', $employeeIds) : null;
                $teacherCentersParam = !empty($teacherCentersArr) ? implode(',', $teacherCentersArr) : null;

                // Stored procedure call
                $attendanceRaw = \DB::select(
                    'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
                    [$m, $y, $roles, $employeeParam, $teacherCentersParam]
                );
            } else {
                // Normal employee: single user
                $employeeParam = auth()->user()->id;
                $attendanceRaw = \DB::select(
                    'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
                    [$m, $y, null, $employeeParam, null]
                );
                // For normal user, $employeeIds is just the single ID
                $employeeIds = [$employeeParam];
            }

        $data = $this->formatAttendanceData($attendanceRaw);




        // 6) Build "employees" string from the final $employeeIds
        $employees = \App\Employee::whereIn('id', $employeeIds)
            ->pluck('full_name')
            ->map(function ($name) {
                return ucwords(strtolower($name));
            })
            ->implode(', ');

        // Example month name logic (English):
        $monthNames = [
            1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
            5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
            9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
        ];
        $monthName = isset($monthNames[$m]) ? $monthNames[$m] : $m;


        // Use local file paths to avoid "Invalid image" error
        $letterHead   = public_path("uploads/settings/EducationDivisionLetterheadersEDU.jpg");
        $letterFooter = public_path("uploads/settings/footer.png");
//        $logo         = public_path("uploads/settings/logo.png");

        // Initialize PhpWord
        $phpWord = new \PhpOffice\PhpWord\PhpWord();

        // 1) Define a default "Header" so letterhead appears on every page
        //    (instead of adding it to the first section only).
        $phpWord->getSettings()->setUpdateFields(true);
        $sectionStyle = [
            'headerHeight' => \PhpOffice\PhpWord\Shared\Converter::cmToTwip(3.0),
            // adjust if needed
        ];
        $section = $phpWord->addSection($sectionStyle);

        // Create a "Header" object in that section
        $header = $section->addHeader();
        $header->addImage($letterHead, [
            'width'     => 600,
            'alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER,
        ]);

        // 2) Add the "footer" on every page
        $footer = $section->addFooter();
        $footer->addImage($letterFooter, [
            'width'     => 600,
            'alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER,
        ]);

        // 4) A mini-table at the top to show Year, Month, Center, Employees
        //    Define table style
        $phpWord->addTableStyle('MiniTable', [
            'borderSize' => 6,
            'borderColor' => '999999',
            'cellMargin' => 50
        ]);
        $miniTable = $section->addTable('MiniTable');

        // Single row with multiple columns, or multiple rows—your choice
        $miniTable->addRow();
        $miniTable->addCell(2000)->addText("Year: {$y}");
        $miniTable->addCell(2000)->addText("Month: {$monthName}");
        $miniTable->addCell(3000)->addText("Center: {$centerNames}");
        $miniTable->addCell(3000)->addText("Employees: {$employees}");

        $section->addTextBreak(1);

        // 5) For each "employee" block in $data, create a daily records table
        //    with borders
        //    First define a new style for the main daily records table:
        $phpWord->addTableStyle('DailyRecordsTable', [
            'borderSize' => 6,
            'borderColor' => '999999',
            'cellMargin' => 50
        ]);

        foreach ($data as $empId => $employeeData) {
            $emp = $employeeData['employee'];

            // Title or heading
            $section->addText("Employee: {$emp['full_name']} (ID: {$emp['id']})", ['bold' => true, 'size' => 12]);

            // Add a table with the "DailyRecordsTable" style
            $table = $section->addTable('DailyRecordsTable');

            // Table header
            $table->addRow();
            $table->addCell(1500)->addText("Date", ['bold' => true]);
            $table->addCell(1000)->addText("Day", ['bold' => true]);
            $table->addCell(1000)->addText("Status", ['bold' => true]);
            $table->addCell(1500)->addText("In Time", ['bold' => true]);
            $table->addCell(1500)->addText("Out Time", ['bold' => true]);
            $table->addCell(1200)->addText("Hours Worked", ['bold' => true]);
            $table->addCell(1200)->addText("Volunteer Hours", ['bold' => true]);
            $table->addCell(1200)->addText("Salary %", ['bold' => true]);

            // Daily records
            foreach ($employeeData['details'] as $daily) {
                $table->addRow();
                $table->addCell(1500)->addText($daily['formatted_date']);
                $table->addCell(1000)->addText($daily['day_name']);
                $table->addCell(1000)->addText($daily['status']);
                $table->addCell(1500)->addText($daily['in_time']);
                $table->addCell(1500)->addText($daily['out_time']);
                $table->addCell(1200)->addText($daily['hours_worked']);
                $table->addCell(1200)->addText($daily['volunteer_hours']);
                $table->addCell(1200)->addText($daily['salary_percentage']);
            }

            // Optional summary row
            $section->addTextBreak(1);
        }

        // Save to a temporary file & return as download
        $tempFile = tempnam(sys_get_temp_dir(), 'word_');
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
        $objWriter->save($tempFile);

        return response()->download($tempFile, "attendance_report_{$m}_{$y}.docx")->deleteFileAfterSend(true);
    }



    /**
     * Helper to format attendance raw data into a grouped array.
     *
     * Expected format:
     * [
     *    employee_id => [
     *         'employee' => { id, full_name },
     *         'details'  => [
     *                    { formatted_date, day, status, in_time, out_time, hours_worked, volunteer_hours, salary_percentage },
     *                    ...
     *         ],
     *         'summary'  => { ... } // optional summary row calculations
     *    ],
     *    ...
     * ]
     */
    protected function formatAttendanceData($attendanceRaw)
    {
        $formatted = [];

        foreach ($attendanceRaw as $record) {
            $employeeId = $record->employee_id;

            if (!isset($formatted[$employeeId])) {
                $formatted[$employeeId] = [
                    'employee' => [
                        'id' => $record->employee_id,
                        'full_name' => $record->full_name,
                    ],
                    'details' => [],
                    'summary' => [
                        'total_hours_worked' => 0,
                        'total_volunteer_hours' => 0,
                        'overall_salary_percentage' => 0,
                    ],
                ];
            }

            $detail = [
                'date' => $record->date,
                'formatted_date' => $record->formatted_date,
                'day_name' => $record->day_name,
                'status' => $record->status,
                'in_time' => $record->in_time,
                'out_time' => $record->out_time ?? '-',  // Display '-' if null
                'hours_worked' => $record->hours_worked,
                'volunteer_hours' => $record->volunteer_hours,
                'salary_percentage' => $record->salary_percentage,
            ];

            $formatted[$employeeId]['details'][] = $detail;

            // Update summary
            $formatted[$employeeId]['summary']['total_hours_worked'] += floatval($record->hours_worked);
            $formatted[$employeeId]['summary']['total_volunteer_hours'] += floatval($record->volunteer_hours);
            $formatted[$employeeId]['summary']['overall_salary_percentage'] += floatval($record->salary_percentage);
        }

        // Calculate averages for summary
        foreach ($formatted as $employeeId => &$data) {
            $count = count($data['details']);
            $data['summary']['overall_salary_percentage'] = $count > 0 ? round($data['summary']['overall_salary_percentage'] / $count, 2) : 0;
        }

        return $formatted;
    }
    public function exportPdf(Request $request)
    {
        $hasSpecialRole = auth()->user()->hasAnyRole([
            'supervisor_' . config('organization_id') . '_',
            'managing-director' . config('organization_id') . '_',
            'human-resource_' . config('organization_id') . '_',
            'it-officer_' . config('organization_id') . '_'
        ]);

        if ($hasSpecialRole) {
            // Collect filters
            $roles = $request->filled('role_id')
                ? implode(',', $request->get('role_id'))
                : null;

            $m = $request->filled('month') ? $request->get('month') : null;
            $y = $request->filled('year') ? $request->get('year') : null;

            $employeeIds = $request->filled('employee_id') ? (array) $request->get('employee_id') : [];
            $teacherCenters = $request->filled('teacherCenters') ? (array)$request->get('teacherCenters') : [];

            $employeeParam = !empty($employeeIds) ? implode(',', $employeeIds) : null;
            $teacherCentersParam = !empty($teacherCenters) ? implode(',', $teacherCenters) : null;

            // Stored procedure
            $attendanceRaw = DB::select(
                'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
                [$m, $y, $roles, $employeeParam, $teacherCentersParam]
            );
        } else {
            // Normal employee
            $m = $request->filled('month') ? $request->get('month') : null;
            $y = $request->filled('year') ? $request->get('year') : null;
            $employeeParam = auth()->user()->id;

            $attendanceRaw = DB::select(
                'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
                [$m, $y, null, $employeeParam, null]
            );
        }

        // Format data
        $data = $this->formatAttendanceData($attendanceRaw);
        $letterHead   = asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg");
        $letterFooter = asset("uploads/settings/footer.png");
        $logo         = asset("uploads/settings/logo.png");
        $requester    = auth()->user()->full_name;

        // Load your PDF view and pass letterHead, footer, logo, etc.
        $pdf = \PDF::loadView('humanresource::attendance.export_pdf', compact('data', 'm', 'y', 'requester', 'letterHead', 'letterFooter', 'logo'))
            ->setPaper('a4')
//            ->setOption('footer-html', view('humanresource::attendance.pdf_footer', compact('letterFooter')))
            ->setOption('footer-spacing', 10);

        // Return as download
        return $pdf->download("attendance_report_{$m}_{$y}.pdf");
    }


}

<div class="col-md-12">
    <!-- <h3>{{ucfirst(trans('registration'))}}</h3> -->

    @if($guardian->status == null || $guardian->status == "update_profile")
        <h3>{{ trans('common.update_profile_data') }}</h3>
        {!! Form::model($guardian, [
            'method' => 'PATCH',
            'route' => ['guardians.update', $guardian->id],
            'class' => 'form-horizontal',
            'files' => true
        ]) !!}
        
        {!! Form::hidden('update_guardian_profile', 1) !!}
        @include('forms.guardian.profile')    
        
        {!! Form::close() !!}
        
        
    @else
    <div class="box-icon box-icon-center box-icon-round box-icon-transparent box-icon-large box-icon-content padding-10 margin-top-0    ">
        <a href="{{ route('guardian.register_student')}}" class="btn btn-success brn-lg pull-right margin-right-10">{{ trans('common.register_new_student') }}</a>
        <h2>Welcome</h2>
        <div class="row">
        @foreach(Auth::user()->students as $student)
            <div class="col-md-4">
                <div class="box-static box-border-top">
                    <div class="box-title text-center">
                        <h4>{{ $student->full_name }}</h4>
                        <img src="{{ $student->image ? asset($student->image) : asset('avatar.jpg') }}" class="img-responsive img-circle padding-20" >
                    </div>
                    @if($student->status=='active')
                   <a class="btn btn-primary btn-lg btn-block" href="hefz/{{$student->id}}">View Report</a>
           @else
           <a class="btn btn-primary btn-lg btn-block" href="hefz/{{$student->id}}">Complete Registration</a>

           @endif
                </div>

            </div>
        @endforeach
        </div>
    </div>
    {{--  @if(Auth::user()->students)
    @endif  --}}
    @endif
    
</div>
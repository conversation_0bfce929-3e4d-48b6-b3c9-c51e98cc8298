<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Http\Controllers;

use App\EvaluationSchemaOption;
use App\MoshafSurah;
use App\Student;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

final class ClassMonthYearController extends Controller
{

    /**
     * Get month-years for single or multiple classes
     * Supports both single class ID and comma-separated multiple class IDs
     *
     * @param string $classIds Single class ID or comma-separated class IDs
     * @param Request $request
     * @return JsonResponse
     */
    public function getMonthYears(string $classIds, Request $request): JsonResponse
    {
        // Parse class IDs - support both single ID and comma-separated multiple IDs
        $classIdArray = array_filter(array_map('trim', explode(',', $classIds)));

        // Also check for classId parameter in query string for backward compatibility
        if ($request->has('classId')) {
            $queryClassId = $request->get('classId');
            if (!empty($queryClassId) && !in_array($queryClassId, $classIdArray)) {
                $classIdArray[] = $queryClassId;
            }
        }

        // Validate that we have at least one class ID
        if (empty($classIdArray)) {
            return response()->json([]);
        }

        // Convert to integers and filter out invalid IDs
        $classIdArray = array_filter(array_map('intval', $classIdArray), function($id) {
            return $id > 0;
        });

        if (empty($classIdArray)) {
            return response()->json([]);
        }

        // Query month-years for all specified classes
        $dates = StudentHefzReport::whereIn('class_id', $classIdArray)
            ->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year')
            ->groupBy('year', 'month')
            ->orderByDesc('year')
            ->orderByRaw("FIELD(month, 'December', 'November', 'October', 'September', 'August', 'July', 'June', 'May', 'April', 'March', 'February', 'January')")
            ->get();

        return response()->json($dates);
    }
}

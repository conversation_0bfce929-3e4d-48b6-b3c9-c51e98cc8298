<?php

namespace Modules\HumanResource\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Employee;

class ProfileController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index($employee_id = null)

    {
        if($employee_id){
            $employee    = Employee::findOrFail($employee_id);
        }else{
            $employee     = auth()->user();
        }


        return view('humanresource::employees.profile.index', compact('employee'));    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view('humanresource::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function store(Request $request)
    {
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show()
    {
        return view('humanresource::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit()
    {
        return view('humanresource::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(Request $request, $id)
    {
        
        // Get the user
        $user = Employee::findOrFail($id);

        // Update user
        

        // check for password change
        if($request->get('password')) {
            $user->password = bcrypt($request->get('password'));
        }
        if ($request->get('image')) {
            $user->image = $request->get('image');
        }

        // Handle the user roles

        $user->save();
        flash()->success('User has been updated.');
        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }
}

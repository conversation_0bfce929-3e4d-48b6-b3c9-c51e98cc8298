<?php

namespace Modules\HumanResource\Http\Controllers;

use App\CenterTranslation;
use App\Organization;
use App\Student;
use Doctrine\DBAL\Cache\CacheException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;

use Illuminate\Validation\ValidationException;
use Jenssegers\Agent\Agent;

use App\Employee;

use App\Authorizable;

use App\Attendance;
use Carbon\Carbon;
use App\LeaveRequest;
use App\Role;


class ProfileImageController extends Controller
{


    public function update(Request $request)
    {
        $request->validate([
            'image' => 'required|image|max:9048', // 2MB Max
        ]);

        $user = auth()->user(); // Get the authenticated user

        $image = $request->file('image');
        $imageName = time().'.'.$image->getClientOriginalExtension();
        $destinationPath = public_path('/images/employees');
        $image->move($destinationPath, $imageName);

        // Update user's image path in the database
        $user->image = '/images/employees/'.$imageName;
        $user->save();

        return response()->json(['success' => true, 'imagePath' => asset($user->image)]);
    }


}

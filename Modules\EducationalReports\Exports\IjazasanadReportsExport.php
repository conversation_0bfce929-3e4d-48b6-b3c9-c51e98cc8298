<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use Maatwebsite\Excel\Concerns\WithMultipleSheets;

/**
 * IjazasanadReportsExport creates a two-sheet workbook for Ijazah & Sanad reports.
 * 
 * Purpose: Export monthly Ijazah & Sanad reports across multiple classes in a structured Excel workbook.
 * Sheets: Student Monthly Progress (per-student rows) and Class Summary (per-class aggregates).
 * Context: Part of program-specific export system; mirrors existing aggregated DataTables structure.
 * Data flow: Delegates to individual sheet classes for Student Monthly and Class Summary data.
 */
final class IjazasanadReportsExport implements WithMultipleSheets
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        // Only Student Monthly Progress sheet (align with request to remove Class Summary)
        return [
            new IjazasanadStudentMonthlySheet($this->filters),
        ];
    }
}

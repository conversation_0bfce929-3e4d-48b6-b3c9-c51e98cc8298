<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\ExamSetup
 *
 * @property int $id
 * @property string|null $exam_title
 * @property float|null $exam_mark
 * @property int $active_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $exam_id
 * @property int|null $class_id
 * @property int|null $subject_id
 * @property int|null $section_id
 * @property int|null $exam_term_id
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $organization_id
 * @property int|null $academic_id
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup query()
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereAcademicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereActiveStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereClassId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereExamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereExamMark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereExamTermId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereExamTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereSchoolId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereSectionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereSubjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamSetup whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class ExamSetup extends Model
{
    //
}

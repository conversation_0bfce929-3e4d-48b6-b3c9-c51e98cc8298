<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\FormBuilderApprovalFlow
 *
 * @property int $id
 * @property int $form_builder_id
 * @property int $step_order
 * @property int $can_reject
 * @property int $can_approve
 * @property int $can_request_clearfication
 * @property int $has_final_approval
 * @property string $role
 * @property-read \App\Role $reviewer
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderApprovalFlow newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderApprovalFlow newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderApprovalFlow query()
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderApprovalFlow whereCanApprove($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderApprovalFlow whereCanReject($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderApprovalFlow whereCanRequestClearfication($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderApprovalFlow whereFormBuilderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderApprovalFlow whereHasFinalApproval($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderApprovalFlow whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderApprovalFlow whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormBuilderApprovalFlow whereStepOrder($value)
 * @mixin \Eloquent
 */
class FormBuilderApprovalFlow extends Model
{
    protected $table = 'form_builder_approval_flow';

    protected $fillable = [
            'form_builder_id'
            ,'step_order'
            ,'role'
            ,'can_reject'
            ,'can_approve'
            ,'can_request_clearfication'
            ,'has_final_approval'
        ];

    public $timestamps = false;
    public function reviewer()
    {
        return $this->belongsTo('App\Role', 'role', 'name');
    }
}

<?php
declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Mo<PERSON>les\JobSeeker\Entities\Job;
use Mo<PERSON>les\JobSeeker\Services\MissedCallNotificationService;

/**
 * A unified, provider-agnostic service to handle missed job tracking.
 * This includes both a catch-up mechanism for recently missed jobs
 * and an admin alert system for when a sync run produces zero notifications.
 */
final class MissedJobService
{
    protected MissedCallNotificationService $missedCallService;

    public function __construct(MissedCallNotificationService $missedCallService)
    {
        $this->missedCallService = $missedCallService;
    }

    /**
     * Finds jobs from a specific provider that may have been missed by previous runs.
     *
     * @param string $providerName The name of the job provider (e.g., 'jobs.af', 'ACBAR').
     * @param int $lookbackHours The number of hours to look back for missed jobs.
     * @return Collection A collection of missed job entities.
     */
    public function findMissedJobs(string $providerName, int $lookbackHours): Collection
    {
        // Check if the feature is enabled (using real-time check for consistency)
        if (!$this->isFeatureEnabledRealTime()) {
            Log::info('MissedJobService: Feature is disabled (real-time check), returning empty collection', [
                'provider' => $providerName,
                'toggle_check' => 'real_time_db'
            ]);
            return collect();
        }

        $lookbackHours = $this->getLookbackHours($lookbackHours);

        Log::info('MissedJobService: Finding missed jobs', [
            'provider' => $providerName,
            'lookback_hours' => $lookbackHours
        ]);

        try {
            // Find recently published jobs that might have been missed
            $missedJobs = Job::where('publish_date', '>=', Carbon::now()->subHours($lookbackHours))
                ->whereRaw('LOWER(source) = ?', [strtolower($providerName)])
                ->orderBy('publish_date', 'desc')
                ->limit(20)
                ->get();

            Log::info('MissedJobService: Found potential missed jobs', [
                'provider' => $providerName,
                'count' => $missedJobs->count(),
                'lookback_hours' => $lookbackHours
            ]);

            return $missedJobs;

        } catch (\Throwable $e) {
            Log::error('MissedJobService: Error finding missed jobs', [
                'provider' => $providerName,
                'lookback_hours' => $lookbackHours,
                'error' => $e->getMessage()
            ]);
            return collect();
        }
    }

    /**
     * Triggers a "missed call" alert to administrators.
     *
     * @param array $context Contextual information about the sync run for the alert.
     * @return bool True if alert was sent successfully
     */
    public function triggerMissedCallAlert(array $context): bool
    {
        // Real-time check to ensure immediate toggle effect
        if (!$this->isFeatureEnabledRealTime()) {
            Log::info('MissedJobService: Feature is disabled (real-time check), skipping missed call alert', [
                'context_provider' => $context['provider'] ?? 'unknown',
                'toggle_check' => 'real_time_db',
            ]);
            return false;
        }

        try {
            Log::info('MissedJobService: Triggering missed call alert', [
                'context_keys' => array_keys($context)
            ]);

            // Enhance context with timestamp
            $enhancedContext = array_merge($context, [
                'timestamp' => Carbon::now()->toDateTimeString(),
                'alert_triggered_by' => 'MissedJobService'
            ]);

            // Send the missed call notification
            $sent = $this->missedCallService->sendMissedCallNotification($enhancedContext);

            Log::info('MissedJobService: Missed call alert processing completed', [
                'sent' => $sent,
                'provider' => $context['provider'] ?? 'unknown'
            ]);

            return $sent;

        } catch (\Throwable $e) {
            // Log with sanitized context to avoid PII exposure
            $safeContext = [
                'provider' => $context['provider'] ?? 'unknown',
                'missed_jobs_count' => count($context['missed_jobs'] ?? []),
                'execution_id' => $context['execution_id'] ?? null,
                'trace_id' => $context['trace_id'] ?? null
            ];
            
            Log::error('MissedJobService: Failed to trigger missed call alert', [
                'error' => $e->getMessage(),
                'safe_context' => $safeContext
            ]);
            return false;
        }
    }

    /**
     * Check if the missed job tracking feature is enabled.
     *
     * @return bool
     */
    protected function isFeatureEnabled(): bool
    {
        // Read from settings - default to enabled for backward compatibility
        return $this->getSetting('missed_job_tracking_enabled', true);
    }

    /**
     * Real-time check bypassing cache to ensure immediate toggle effect.
     *
     * This method reads directly from the database to guarantee that
     * when an admin toggles the missed call feature OFF, it takes effect
     * immediately even if there are cached values.
     *
     * @return bool True if feature is enabled, false if disabled
     */
    public function isFeatureEnabledRealTime(): bool
    {
        try {
            $setting = DB::table('jobseeker_settings')
                ->where('key', 'missed_job_tracking_enabled')
                ->first();
                
            if ($setting) {
                $rawValue = $setting->value;
                $parsedValue = filter_var($rawValue, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
                
                // Handle ambiguous cases: null means unparseable, fallback to enabled when setting exists
                $enabled = $parsedValue === null ? true : $parsedValue;
                
                Log::debug('MissedJobService: Real-time toggle check', [
                    'raw_db_value' => $rawValue,
                    'parsed_boolean_result' => $parsedValue,
                    'final_enabled' => $enabled,
                    'check_time' => now()->format('Y-m-d H:i:s'),
                    'was_ambiguous' => $parsedValue === null
                ]);
                
                return $enabled;
            }

            // Default to enabled if no setting found (backward compatibility)
            Log::debug('MissedJobService: No toggle setting found, defaulting to enabled');
            return true;
            
        } catch (\Throwable $e) {
            Log::error('MissedJobService: Error in real-time toggle check', [
                'error' => $e->getMessage(),
            ]);
            // Fall back to cached check if DB fails
            return $this->isFeatureEnabled();
        }
    }

    /**
     * Get the lookback hours setting, with fallback to provided default.
     *
     * @param int $default Default lookback hours
     * @return int
     */
    protected function getLookbackHours(int $default = 48): int
    {
        return (int) $this->getSetting('missed_job_catchup_hours', $default);
    }

    /**
     * Get admin alert recipients.
     *
     * @return array Array of email addresses
     */
    protected function getAdminAlertRecipients(): array
    {
        $recipients = $this->getSetting('missed_job_admin_recipients', '');

        if (empty($recipients)) {
            return [];
        }

        // Parse comma-separated email addresses
        $emails = array_map('trim', explode(',', $recipients));
        return array_filter($emails, function($email) {
            return filter_var($email, FILTER_VALIDATE_EMAIL);
        });
    }

    /**
     * Get a setting value from the persistent store.
     *
     * @param string $key Setting key
     * @param mixed $default Default value
     * @return mixed
     */
    protected function getSetting(string $key, $default = null)
    {
        try {
            // Try to get from jobseeker_settings table first for consistency with UI
            $setting = DB::table('jobseeker_settings')->where('key', $key)->first();
            if ($setting) {
                $value = $setting->value;
                // Handle boolean values
                if ($value === 'true') return true;
                if ($value === 'false') return false;
                // Try to decode JSON, fall back to original value
                return json_decode($value, true) ?? $value;
            }

            // Fallback to config
            return config("jobseeker.missed_job_service.{$key}", $default);
        } catch (\Throwable $e) {
            Log::warning('MissedJobService: Error reading setting', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return $default;
        }
    }

    /**
     * Save a setting value to the persistent store.
     *
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @return bool
     */
    public function saveSetting(string $key, $value): bool
    {
        try {
            // Convert value to string for consistency with UI expectations
            $stringValue = is_bool($value) ? ($value ? 'true' : 'false') : 
                          (is_array($value) || is_object($value) ? json_encode($value) : (string)$value);

            DB::table('jobseeker_settings')->updateOrInsert(
                ['key' => $key],
                [
                    'value' => $stringValue,
                    'updated_at' => Carbon::now(),
                    'created_at' => Carbon::now()
                ]
            );

            // Clear any cached settings to ensure immediate effect
            $this->clearSettingsCache($key);

            Log::info('MissedJobService: Setting saved with cache cleared', [
                'key' => $key,
                'value_type' => gettype($value),
                'stored_value' => $stringValue
            ]);

            return true;
        } catch (\Throwable $e) {
            Log::error('MissedJobService: Error saving setting', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Clear cached settings to ensure immediate effect of changes.
     *
     * @param string $key Setting key that was updated
     * @return void
     */
    private function clearSettingsCache(string $key): void
    {
        try {
            // Attempt tag-based invalidation first (if cache driver supports it)
            try {
                Cache::tags(['jobseeker_settings'])->flush();
                Log::debug('MissedJobService: Cleared settings cache via tags', [
                    'setting_key' => $key,
                    'method' => 'tag_based'
                ]);
                return;
            } catch (\BadMethodCallException $e) {
                // Tags not supported by cache driver, fall back to key-based clearing
            }
            
            // Centralized key derivation for known settings cache keys
            $cacheKeys = $this->getKnownCacheKeys($key);
            
            foreach ($cacheKeys as $cacheKey) {
                Cache::forget($cacheKey);
            }
            
            Log::debug('MissedJobService: Cleared settings cache via keys', [
                'setting_key' => $key,
                'cache_keys_cleared' => $cacheKeys,
                'method' => 'key_based'
            ]);
            
        } catch (\Throwable $e) {
            Log::warning('MissedJobService: Error clearing settings cache', [
                'setting_key' => $key,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get all known cache keys for JobSeeker settings.
     * Centralized to keep cache clearing in sync with cache writers.
     *
     * @param string $key The specific setting key
     * @return array Array of cache keys to clear
     */
    private function getKnownCacheKeys(string $key): array
    {
        return [
            // Generic settings caches
            'missed_job_settings',
            'jobseeker_settings_' . $key,
            
            // Specific feature caches
            'missed_job_tracking_enabled',
            'missed_job_lookback_hours',
            'job_alerts_email_delivery_mode',
            
            // UI control caches  
            'email_control_board_settings',
            'notification_window_days',
            
            // Provider-specific caches
            'provider_settings_reliefweb',
            'provider_settings_jobs_af',
            'provider_settings_acbar',
        ];
    }

    /**
     * Get all missed job service settings.
     *
     * @return array
     */
    public function getSettings(): array
    {
        return [
            'missed_job_tracking_enabled' => $this->isFeatureEnabled(),
            'missed_job_catchup_hours' => $this->getLookbackHours(),
            'missed_job_admin_recipients' => implode(', ', $this->getAdminAlertRecipients())
        ];
    }

    /**
     * Save multiple settings at once.
     *
     * @param array $settings Array of key-value pairs
     * @return bool
     */
    public function saveSettings(array $settings): bool
    {
        try {
            DB::beginTransaction();

            foreach ($settings as $key => $value) {
                $this->saveSetting($key, $value);
            }

            DB::commit();

            Log::info('MissedJobService: Multiple settings saved', [
                'settings_count' => count($settings),
                'keys' => array_keys($settings)
            ]);

            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('MissedJobService: Error saving multiple settings', [
                'error' => $e->getMessage(),
                'settings_count' => count($settings)
            ]);
            return false;
        }
    }
}

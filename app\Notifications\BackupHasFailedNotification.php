<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Services\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Spatie\Backup\Events\BackupHasFailed;

class BackupHasFailedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected BackupHasFailed $event;

    public function __construct(BackupHasFailed $event)
    {
        $this->event = $event;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $correlationId = Str::uuid()->toString();
        
        // Send via EmailService asynchronously
        $this->sendViaEmailService($notifiable, $correlationId);
        
        // Return a basic MailMessage for compatibility (won't be used due to EmailService)
        return (new MailMessage)
            ->subject('Backup Failed')
            ->line('The backup process has failed.')
            ->line('Error: ' . $this->event->exception->getMessage());
    }

    protected function sendViaEmailService($notifiable, string $correlationId): void
    {
        try {
            $emailService = app(EmailService::class);
            
            $subject = 'Critical: Database Backup Failed - ' . config('app.name');
            $recipient = $notifiable->routeNotificationFor('mail');
            
            $htmlContent = $this->generateHtmlContent();
            
            $emailService->sendEmail(
                to: $recipient,
                subject: $subject,
                htmlBody: $htmlContent,
                textBody: strip_tags($htmlContent),
                correlationId: $correlationId,
                tags: ['backup', 'failure', 'critical'],
                isAsync: true
            );
            
            Log::info('Backup failure notification sent via EmailService', [
                'correlation_id' => $correlationId,
                'recipient' => $recipient,
                'error' => $this->event->exception->getMessage()
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send backup failure notification via EmailService', [
                'correlation_id' => $correlationId,
                'error' => $e->getMessage(),
                'original_backup_error' => $this->event->exception->getMessage()
            ]);
        }
    }

    protected function generateHtmlContent(): string
    {
        $exception = $this->event->exception;
        $errorMessage = $exception->getMessage();
        $errorTrace = $exception->getTraceAsString();
        $timestamp = now()->format('Y-m-d H:i:s T');
        $serverInfo = $this->getServerInfo();
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='utf-8'>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f4f4f4; }
                .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .header { background-color: #dc3545; color: white; padding: 20px; border-radius: 8px 8px 0 0; margin: -30px -30px 30px -30px; }
                .header h1 { margin: 0; font-size: 24px; }
                .alert { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
                .info-section { margin-bottom: 25px; }
                .info-section h3 { color: #333; border-bottom: 2px solid #dc3545; padding-bottom: 8px; }
                .error-box { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 14px; white-space: pre-wrap; }
                .server-info { background-color: #e9ecef; padding: 15px; border-radius: 4px; }
                .server-info table { width: 100%; border-collapse: collapse; }
                .server-info td { padding: 8px; border-bottom: 1px solid #dee2e6; }
                .server-info td:first-child { font-weight: bold; width: 30%; }
                .troubleshooting { background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 4px; }
                .troubleshooting ul { margin: 10px 0; padding-left: 20px; }
                .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>🚨 Database Backup Failed</h1>
                    <p style='margin: 10px 0 0 0;'>Critical system notification from " . config('app.name') . "</p>
                </div>
                
                <div class='alert'>
                    <strong>⚠️ Action Required:</strong> The automated database backup process has failed and requires immediate attention.
                </div>
                
                <div class='info-section'>
                    <h3>📋 Failure Details</h3>
                    <p><strong>Timestamp:</strong> {$timestamp}</p>
                    <p><strong>Application:</strong> " . config('app.name') . "</p>
                    <p><strong>Environment:</strong> " . config('app.env') . "</p>
                    <p><strong>Database:</strong> " . config('database.connections.mysql.database') . "</p>
                </div>
                
                <div class='info-section'>
                    <h3>🔍 Error Message</h3>
                    <div class='error-box'>{$errorMessage}</div>
                </div>
                
                <div class='info-section'>
                    <h3>💻 Server Information</h3>
                    <div class='server-info'>
                        <table>
                            <tr><td>PHP Version</td><td>{$serverInfo['php_version']}</td></tr>
                            <tr><td>Laravel Version</td><td>{$serverInfo['laravel_version']}</td></tr>
                            <tr><td>Memory Usage</td><td>{$serverInfo['memory_usage']}</td></tr>
                            <tr><td>Disk Space</td><td>{$serverInfo['disk_space']}</td></tr>
                            <tr><td>MySQL Version</td><td>{$serverInfo['mysql_version']}</td></tr>
                        </table>
                    </div>
                </div>
                
                <div class='info-section'>
                    <h3>🛠️ Troubleshooting Steps</h3>
                    <div class='troubleshooting'>
                        <p><strong>Immediate Actions:</strong></p>
                        <ul>
                            <li>Check if the backup storage directory exists and is writable</li>
                            <li>Verify MySQL dump binary path is correct</li>
                            <li>Ensure sufficient disk space for backup files</li>
                            <li>Check database connection and permissions</li>
                        </ul>
                        
                        <p><strong>Diagnostic Commands:</strong></p>
                        <ul>
                            <li><code>php artisan backup:run --only-db --dry-run</code> - Test backup process</li>
                            <li><code>php artisan test-backup --mysql</code> - Test MySQL dump binary</li>
                            <li><code>df -h</code> - Check disk space</li>
                        </ul>
                    </div>
                </div>
                
                <div class='info-section'>
                    <h3>📞 Next Steps</h3>
                    <p>1. <strong>Investigate the error</strong> using the information above</p>
                    <p>2. <strong>Fix the underlying issue</strong> preventing the backup</p>
                    <p>3. <strong>Run a manual backup</strong> to verify the fix: <code>php artisan backup:run --only-db</code></p>
                    <p>4. <strong>Monitor subsequent backups</strong> to ensure the issue is resolved</p>
                </div>
                
                <div class='footer'>
                    <p>This notification was sent automatically by the backup monitoring system.</p>
                    <p>Server: " . gethostname() . " | Time: {$timestamp}</p>
                </div>
            </div>
        </body>
        </html>";
    }

    protected function getServerInfo(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'memory_usage' => $this->formatBytes(memory_get_usage(true)),
            'disk_space' => $this->getDiskSpace(),
            'mysql_version' => $this->getMysqlVersion(),
        ];
    }

    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    protected function getDiskSpace(): string
    {
        try {
            $backupPath = storage_path('dbBackups');
            $freeSpace = disk_free_space($backupPath);
            $totalSpace = disk_total_space($backupPath);
            
            if ($freeSpace && $totalSpace) {
                $usedSpace = $totalSpace - $freeSpace;
                $usedPercent = round(($usedSpace / $totalSpace) * 100, 1);
                return $this->formatBytes($freeSpace) . ' free / ' . $this->formatBytes($totalSpace) . ' total (' . $usedPercent . '% used)';
            }
        } catch (\Exception $e) {
            return 'Unable to determine disk space';
        }
        
        return 'Unknown';
    }

    protected function getMysqlVersion(): string
    {
        try {
            $pdo = \DB::connection()->getPdo();
            $version = $pdo->getAttribute(\PDO::ATTR_SERVER_VERSION);
            return $version;
        } catch (\Exception $e) {
            return 'Unable to determine MySQL version';
        }
    }
} 
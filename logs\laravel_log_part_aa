[2025-06-02 18:39:41] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:39:41] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:39:43] production.ERROR: max(): Argument #1 ($value) must contain at least one element {"view":{"view":"/var/www/html/itqanalquran/resources/views/modules/education/monthlyPlan/nouranyaCreate.blade.php","data":[]},"userId":148,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): max(): Argument #1 ($value) must contain at least one element at /var/www/html/itqanalquran/resources/views/modules/education/monthlyPlan/nouranyaCreate.blade.php:1203)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#3 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()
#5 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Router->prepareResponse()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#17 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#19 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#21 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#23 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#49 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#63 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#65 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#66 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#67 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#68 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#69 {main}

[previous exception] [object] (ValueError(code: 0): max(): Argument #1 ($value) must contain at least one element at /var/www/html/itqanalquran/storage/framework/views/b1119c78e0286edfab1e5e4434a4e7f1.php:1203)
[stacktrace]
#0 /var/www/html/itqanalquran/storage/framework/views/b1119c78e0286edfab1e5e4434a4e7f1.php(1203): max()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#4 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()
#6 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Router->prepareResponse()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#18 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#20 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#22 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#24 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#48 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#50 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#64 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#66 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#68 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#69 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#70 {main}
"} 
[2025-06-02 18:39:55] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:39:55] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:39:57] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:39:57] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:39:59] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:39:59] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:00] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:00] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:01] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:01] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:02] production.ERROR: max(): Argument #1 ($value) must contain at least one element {"view":{"view":"/var/www/html/itqanalquran/resources/views/modules/education/monthlyPlan/nouranyaCreate.blade.php","data":[]},"userId":148,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): max(): Argument #1 ($value) must contain at least one element at /var/www/html/itqanalquran/resources/views/modules/education/monthlyPlan/nouranyaCreate.blade.php:1203)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#3 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()
#5 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Router->prepareResponse()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#17 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#19 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#21 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#23 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#49 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#63 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#65 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#66 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#67 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#68 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#69 {main}

[previous exception] [object] (ValueError(code: 0): max(): Argument #1 ($value) must contain at least one element at /var/www/html/itqanalquran/storage/framework/views/b1119c78e0286edfab1e5e4434a4e7f1.php:1203)
[stacktrace]
#0 /var/www/html/itqanalquran/storage/framework/views/b1119c78e0286edfab1e5e4434a4e7f1.php(1203): max()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('...')
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#4 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()
#6 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Router->prepareResponse()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#18 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#20 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#22 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#24 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#48 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#50 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#64 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#66 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#68 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#69 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#70 {main}
"} 
[2025-06-02 18:40:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:40:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:40:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:40:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:40:02] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 18:40:02] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.INFO: Starting job subscriber notifications at 2025-06-02 18:40:02  
[2025-06-02 18:40:02] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 18:40:02] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 18:40:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:40:02] production.INFO: Starting general:queue-notify-job-subscribers at 2025-06-02 18:40:02  
[2025-06-02 18:40:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:40:02 | Error: Queue dispatch may be locked or failed to complete  
[2025-06-02 18:40:02] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:40:02 | Error: Queue dispatch may be locked or failed to complete {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:40:02 | Error: Queue dispatch may be locked or failed to complete at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(129): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 18:40:02] production.INFO: Job notifications process started after job fetch at 2025-06-02 18:40:02  
[2025-06-02 18:40:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:40:02 | Error: Notification process failed to complete  
[2025-06-02 18:40:02] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:40:02 | Error: Notification process failed to complete {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:40:02 | Error: Notification process failed to complete at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(574): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 18:40:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:40:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:40:02] production.ERROR: Command "jobseeker:notify-job-subscribers" is not defined.

Did you mean one of these?
    general:notify-job-seekers
    general:queue-notify-job-subscribers
    jobseeker:check-inactive-categories
    jobseeker:cleanup-device-tokens
    jobseeker:discover-acbar-categories
    jobseeker:fetch-jobs-af-descriptions
    jobseeker:sync-acbar-jobs
    jobseeker:sync-jobs-af
    jobseeker:test-fcm-notification
    jobseeker:test-refactored-fcm-notification {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"jobseeker:notify-job-subscribers\" is not defined.

Did you mean one of these?
    general:notify-job-seekers
    general:queue-notify-job-subscribers
    jobseeker:check-inactive-categories
    jobseeker:cleanup-device-tokens
    jobseeker:discover-acbar-categories
    jobseeker:fetch-jobs-af-descriptions
    jobseeker:sync-acbar-jobs
    jobseeker:sync-jobs-af
    jobseeker:test-fcm-notification
    jobseeker:test-refactored-fcm-notification at /var/www/html/itqanalquran/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()
#1 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#3 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-02 18:40:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:40:02  
[2025-06-02 18:40:02] production.INFO: Starting general:queue-notify-job-subscribers at 2025-06-02 18:40:02  
[2025-06-02 18:40:03] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:03] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:03] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:40:03] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:40:03] production.INFO: Starting job notification queue dispatch from command  
[2025-06-02 18:40:03] production.ERROR: Call to undefined method Modules\JobSeeker\Services\JobService::notifySubscribers() {"exception":"[object] (Error(code: 0): Call to undefined method Modules\\JobSeeker\\Services\\JobService::notifySubscribers() at /var/www/html/itqanalquran/Modules/General/Console/Commands/QueueNotifyJobSubscribers.php:51)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Modules\\General\\Console\\Commands\\QueueNotifyJobSubscribers->handle()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#6 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#8 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#9 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#10 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#12 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-06-02 18:40:03] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:40:03 | Error: Queue dispatch may be locked or failed to complete  
[2025-06-02 18:40:03] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:03] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:03] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:03] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:03] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:40:03] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:40:03] production.ERROR: Command "general:notify-all-job-subscribers" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"general:notify-all-job-subscribers\" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers at /var/www/html/itqanalquran/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()
#1 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#3 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-02 18:40:03] production.INFO: Job notifications process started after job fetch at 2025-06-02 18:40:03  
[2025-06-02 18:40:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:40:03 | Error: Notification process failed to complete  
[2025-06-02 18:40:05] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:05] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:07] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:07] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:09] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:09] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:10] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:10] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:12] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:12] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:12] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:12] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:14] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:14] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:15] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:15] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:16] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:16] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:16] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:16] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:16] production.ERROR: PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'class_time' in 'where clause' in /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php:423
Stack trace:
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): PDO->prepare()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\Database\Connection->Illuminate\Database\{closure}()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\Database\Connection->runQueryCallback()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(431): Illuminate\Database\Connection->run()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2914): Illuminate\Database\Connection->select()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2903): Illuminate\Database\Query\Builder->onceWithColumns()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3383): Illuminate\Database\Query\Builder->get()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3311): Illuminate\Database\Query\Builder->aggregate()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1982): Illuminate\Database\Query\Builder->count()
#11 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/ClassReportController.php(600): Illuminate\Database\Eloquent\Builder->__call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\Education\Http\Controllers\ClassReportController->studentRecordsStatistics()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\Routing\ControllerDispatcher->dispatch()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\Routing\Route->runController()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\Routing\Route->run()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}()
#18 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\Http\Middleware\CheckMissedClockOutMiddleware->handle()
#20 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle()
#22 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\Http\Middleware\SetLocale->handle()
#24 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\Http\Middleware\ShareAuthDataWithViews->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Auth\Middleware\Authenticate->handle()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\View\Middleware\ShareErrorsFromSession->handle()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Session\Middleware\StartSession->handle()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Cookie\Middleware\EncryptCookies->handle()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\Pipeline\Pipeline->then()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\Routing\Router->runRouteWithinStack()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\Routing\Router->runRoute()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\Routing\Router->dispatchToRoute()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\Routing\Router->dispatch()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}()
#48 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\Features\SupportDisablingBackButtonCache\DisableBackButtonCacheMiddleware->handle()
#50 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#63 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle()
#64 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#65 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle()
#66 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#67 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\Pipeline\Pipeline->then()
#68 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter()
#69 /var/www/html/itqanalquran/public/index.php(52): Illuminate\Foundation\Http\Kernel->handle()
#70 {main}

Next Illuminate\Database\QueryException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'class_time' in 'where clause' (Connection: mysql, SQL: select count(*) as aggregate from `student_hefz_report` where `student_id` is null and `class_id` = 22 and year(`class_time`) = 2025 and month(`class_time`) = 06 and `student_hefz_report`.`deleted_at` is null) in /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829
Stack trace:
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\Database\Connection->runQueryCallback()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(431): Illuminate\Database\Connection->run()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2914): Illuminate\Database\Connection->select()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2903): Illuminate\Database\Query\Builder->onceWithColumns()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3383): Illuminate\Database\Query\Builder->get()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3311): Illuminate\Database\Query\Builder->aggregate()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1982): Illuminate\Database\Query\Builder->count()
#9 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/ClassReportController.php(600): Illuminate\Database\Eloquent\Builder->__call()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\Education\Http\Controllers\ClassReportController->studentRecordsStatistics()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\Routing\ControllerDispatcher->dispatch()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\Routing\Route->runController()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\Routing\Route->run()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}()
#16 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\Http\Middleware\CheckMissedClockOutMiddleware->handle()
#18 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle()
#20 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\Http\Middleware\SetLocale->handle()
#22 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\Http\Middleware\ShareAuthDataWithViews->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Auth\Middleware\Authenticate->handle()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\View\Middleware\ShareErrorsFromSession->handle()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Session\Middleware\StartSession->handle()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Cookie\Middleware\EncryptCookies->handle()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\Pipeline\Pipeline->then()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\Routing\Router->runRouteWithinStack()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\Routing\Router->runRoute()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\Routing\Router->dispatchToRoute()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\Routing\Router->dispatch()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}()
#46 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\Features\SupportDisablingBackButtonCache\DisableBackButtonCacheMiddleware->handle()
#48 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#63 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle()
#64 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#65 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\Pipeline\Pipeline->then()
#66 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter()
#67 /var/www/html/itqanalquran/public/index.php(52): Illuminate\Foundation\Http\Kernel->handle()
#68 {main}  
[2025-06-02 18:40:17] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:17] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:17] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:17] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:17] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:17] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:17] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:17] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:17] production.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'COUNT(*)' in 'field list' (Connection: mysql, SQL: select `COUNT(*)` as `count`, `attendance_options`.`title` as `attendance` from `student_hefz_report` inner join `attendance_options` on `student_hefz_report`.`attendance_id` = `attendance_options`.`id` where `attendance_id` is not null and `student_hefz_report`.`deleted_at` is null group by `attendance_options`.`title` order by `attendance_options`.`title` asc) {"userId":124,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'COUNT(*)' in 'field list' (Connection: mysql, SQL: select `COUNT(*)` as `count`, `attendance_options`.`title` as `attendance` from `student_hefz_report` inner join `attendance_options` on `student_hefz_report`.`attendance_id` = `attendance_options`.`id` where `attendance_id` is not null and `student_hefz_report`.`deleted_at` is null group by `attendance_options`.`title` order by `attendance_options`.`title` asc) at /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(431): Illuminate\\Database\\Connection->run()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2914): Illuminate\\Database\\Connection->select()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(739): Illuminate\\Database\\Query\\Builder->get()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/ReportsTabStudentAttendanceController.php(56): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\ReportsTabStudentAttendanceController->index()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#17 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#19 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#21 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#45 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#47 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#63 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#65 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'COUNT(*)' in 'field list' at /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php:423)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): PDO->prepare()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(431): Illuminate\\Database\\Connection->run()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2914): Illuminate\\Database\\Connection->select()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(739): Illuminate\\Database\\Query\\Builder->get()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/ReportsTabStudentAttendanceController.php(56): Illuminate\\Database\\Eloquent\\Builder->get()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\ReportsTabStudentAttendanceController->index()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#17 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#19 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#21 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#23 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#49 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#63 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#65 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#66 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#67 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#68 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#69 {main}
"} 
[2025-06-02 18:40:18] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:18] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:18] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:40:18] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:40:18] production.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'COUNT(*)' in 'field list' (Connection: mysql, SQL: select `COUNT(*)` as `count`, `level_id` as `level` from `student_hefz_plans` where `level_id` is not null and `student_hefz_plans`.`deleted_at` is null group by `level` order by `level` asc) {"userId":124,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'COUNT(*)' in 'field list' (Connection: mysql, SQL: select `COUNT(*)` as `count`, `level_id` as `level` from `student_hefz_plans` where `level_id` is not null and `student_hefz_plans`.`deleted_at` is null group by `level` order by `level` asc) at /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(431): Illuminate\\Database\\Connection->run()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2914): Illuminate\\Database\\Connection->select()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(739): Illuminate\\Database\\Query\\Builder->get()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/ReportsTabStudentLevelController.php(52): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\ReportsTabStudentLevelController->index()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#17 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#19 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#21 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#45 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#47 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#63 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#65 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'COUNT(*)' in 'field list' at /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php:423)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): PDO->prepare()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Connection.php(431): Illuminate\\Database\\Connection->run()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2914): Illuminate\\Database\\Connection->select()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2903): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(739): Illuminate\\Database\\Query\\Builder->get()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/ReportsTabStudentLevelController.php(52): Illuminate\\Database\\Eloquent\\Builder->get()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\ReportsTabStudentLevelController->index()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#17 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#19 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#21 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#23 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#49 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()

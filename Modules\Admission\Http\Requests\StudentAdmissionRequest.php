<?php

namespace Modules\Admission\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StudentAdmissionRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {


        return [
            'classId' => 'required|integer',
            'student_id' => 'nullable|integer|exists:students,id',
            'user_id' => 'required_without:student_id|integer|exists:users,id',
            'program_id' => 'required|integer',
            'center_id' => 'required|integer',
            'identity_number' => 'required_without:student_id|alpha_num',
            'gender' => 'required_without:student_id|in:MALE,FEMALE,male,female,Male,Female',
            'dob' => 'required_without:student_id|date',
            'mobile' => 'required_without:student_id|numeric'
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}

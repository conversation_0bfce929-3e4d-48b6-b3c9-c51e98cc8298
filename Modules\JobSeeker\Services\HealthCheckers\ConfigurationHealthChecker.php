<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services\HealthCheckers;

use Exception;
use Mo<PERSON>les\JobSeeker\Entities\SystemHealthCheck;
use Illuminate\Support\Facades\File;

/**
 * Configuration Health Checker
 * 
 * Monitors critical configuration settings to prevent issues like
 * the JOBSEEKER_DISABLE_EVENT_NOTIFICATIONS that caused the 46-day outage.
 */
final class Configuration<PERSON>ealthChecker implements HealthCheckerInterface
{
    /**
     * Critical configuration keys that must be properly set
     */
    private const CRITICAL_CONFIGS = [
        'jobseeker.disable_event_notifications' => [
            'expected' => false,
            'description' => 'Event notifications must be enabled',
        ],
        'mail.default' => [
            'required' => true,
            'description' => 'Mail driver must be configured',
        ],
        'mail.from.address' => [
            'required' => true,
            'description' => 'From email address must be configured',
        ],
        'jobseeker.admin_notification_email' => [
            'required' => true,
            'description' => 'Admin notification email must be configured',
        ],
        'database.default' => [
            'required' => true,
            'description' => 'Database connection must be configured',
        ],
        'queue.default' => [
            'required' => true,
            'description' => 'Queue driver must be configured',
        ],
    ];

    /**
     * Important configuration keys that should be monitored
     */
    private const IMPORTANT_CONFIGS = [
        'app.debug' => [
            'expected' => false,
            'description' => 'Debug mode should be disabled in production',
            'production_only' => true,
        ],
        'app.env' => [
            'description' => 'Application environment',
        ],
        'logging.default' => [
            'required' => true,
            'description' => 'Logging channel must be configured',
        ],
    ];

    public function getName(): string
    {
        return 'configuration_validation';
    }

    public function getType(): string
    {
        return SystemHealthCheck::TYPE_CRITICAL;
    }

    public function check(): array
    {
        try {
            $metrics = [];
            $issues = [];
            $warnings = [];

            // Check critical configurations
            foreach (self::CRITICAL_CONFIGS as $key => $requirements) {
                $result = $this->checkConfigValue($key, $requirements);
                $metrics[$key] = $result['value'];
                
                if (!$result['valid']) {
                    $issues[] = $result['message'];
                }
            }

            // Check important configurations
            foreach (self::IMPORTANT_CONFIGS as $key => $requirements) {
                // Skip production-only checks if not in production
                if (isset($requirements['production_only']) && 
                    $requirements['production_only'] && 
                    !app()->environment('production')) {
                    continue;
                }

                $result = $this->checkConfigValue($key, $requirements);
                $metrics[$key] = $result['value'];
                
                if (!$result['valid']) {
                    $warnings[] = $result['message'];
                }
            }

            // Check .env file integrity
            $envCheck = $this->checkEnvFileIntegrity();
            $metrics['env_file'] = $envCheck['metrics'];
            
            if (!$envCheck['healthy']) {
                $issues = array_merge($issues, $envCheck['issues']);
            }

            // Check for dangerous configuration combinations
            $dangerousCheck = $this->checkDangerousConfigurations();
            $metrics['dangerous_configs'] = $dangerousCheck['metrics'];
            
            if (!$dangerousCheck['healthy']) {
                $issues = array_merge($issues, $dangerousCheck['issues']);
            }

            // Determine overall status
            if (!empty($issues)) {
                return [
                    'status' => SystemHealthCheck::STATUS_CRITICAL,
                    'message' => 'Critical configuration issues: ' . implode(', ', $issues),
                    'metrics' => $metrics,
                ];
            }

            if (!empty($warnings)) {
                return [
                    'status' => SystemHealthCheck::STATUS_WARNING,
                    'message' => 'Configuration warnings: ' . implode(', ', $warnings),
                    'metrics' => $metrics,
                ];
            }

            return [
                'status' => SystemHealthCheck::STATUS_HEALTHY,
                'message' => 'All critical configurations are valid',
                'metrics' => $metrics,
            ];

        } catch (Exception $e) {
            return [
                'status' => SystemHealthCheck::STATUS_CRITICAL,
                'message' => 'Configuration health check failed: ' . $e->getMessage(),
                'metrics' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * Check a specific configuration value
     */
    private function checkConfigValue(string $key, array $requirements): array
    {
        $value = config($key);
        $valid = true;
        $message = '';

        // Check if required value is present
        if (isset($requirements['required']) && $requirements['required']) {
            if (empty($value)) {
                $valid = false;
                $message = "{$requirements['description']}: value is empty";
            }
        }

        // Check if value matches expected value
        if (isset($requirements['expected']) && $valid) {
            if ($value !== $requirements['expected']) {
                $valid = false;
                $expectedStr = is_bool($requirements['expected']) ? 
                    ($requirements['expected'] ? 'true' : 'false') : 
                    $requirements['expected'];
                $actualStr = is_bool($value) ? ($value ? 'true' : 'false') : $value;
                $message = "{$requirements['description']}: expected '{$expectedStr}', got '{$actualStr}'";
            }
        }

        return [
            'value' => $value,
            'valid' => $valid,
            'message' => $message,
        ];
    }

    /**
     * Check .env file integrity
     */
    private function checkEnvFileIntegrity(): array
    {
        $metrics = [];
        $issues = [];
        $healthy = true;

        try {
            $envPath = base_path('.env');
            
            // Check if .env file exists
            if (!File::exists($envPath)) {
                $issues[] = '.env file does not exist';
                $healthy = false;
                $metrics['exists'] = false;
            } else {
                $metrics['exists'] = true;
                
                // Check if .env file is readable
                if (!File::isReadable($envPath)) {
                    $issues[] = '.env file is not readable';
                    $healthy = false;
                    $metrics['readable'] = false;
                } else {
                    $metrics['readable'] = true;
                    
                    // Check file size (should not be empty)
                    $fileSize = File::size($envPath);
                    $metrics['size_bytes'] = $fileSize;
                    
                    if ($fileSize === 0) {
                        $issues[] = '.env file is empty';
                        $healthy = false;
                    }
                    
                    // Check last modified time
                    $lastModified = File::lastModified($envPath);
                    $metrics['last_modified'] = date('Y-m-d H:i:s', $lastModified);
                }
            }

        } catch (Exception $e) {
            $issues[] = 'Error checking .env file: ' . $e->getMessage();
            $healthy = false;
            $metrics['error'] = $e->getMessage();
        }

        return [
            'healthy' => $healthy,
            'issues' => $issues,
            'metrics' => $metrics,
        ];
    }

    /**
     * Check for dangerous configuration combinations
     */
    private function checkDangerousConfigurations(): array
    {
        $metrics = [];
        $issues = [];
        $healthy = true;

        try {
            // Check for debug mode in production
            if (app()->environment('production') && config('app.debug')) {
                $issues[] = 'Debug mode is enabled in production environment';
                $healthy = false;
            }
            $metrics['debug_in_production'] = app()->environment('production') && config('app.debug');

            // Check for missing APP_KEY
            if (empty(config('app.key'))) {
                $issues[] = 'Application key (APP_KEY) is not set';
                $healthy = false;
            }
            $metrics['app_key_set'] = !empty(config('app.key'));

            // Check for default database passwords in production
            if (app()->environment('production')) {
                $dbPassword = config('database.connections.mysql.password');
                if (empty($dbPassword) || in_array($dbPassword, ['', 'password', 'secret'])) {
                    $issues[] = 'Weak or default database password in production';
                    $healthy = false;
                }
            }
            $metrics['secure_db_password'] = !empty(config('database.connections.mysql.password'));

            // Check for disabled notifications (the issue that caused the 46-day outage)
            if (config('jobseeker.disable_event_notifications')) {
                $issues[] = 'CRITICAL: Event notifications are disabled - this caused the 46-day outage';
                $healthy = false;
            }
            $metrics['notifications_enabled'] = !config('jobseeker.disable_event_notifications');

        } catch (Exception $e) {
            $issues[] = 'Error checking dangerous configurations: ' . $e->getMessage();
            $healthy = false;
            $metrics['error'] = $e->getMessage();
        }

        return [
            'healthy' => $healthy,
            'issues' => $issues,
            'metrics' => $metrics,
        ];
    }
}

-- Context: Quick Add Employee functionality testing for role management
-- Purpose: Verify database operations for available employees and bulk attach features
-- Date: 2025-01-28
-- Feature: Employee quick assignment to roles with bulk operations

-- Test Case 1: Check available employees for role 38
-- This simulates the getAvailableEmployees endpoint
SELECT e.id, e.name, e.email, e.full_name, e.display_name, e.gender
FROM employees e
LEFT JOIN model_has_roles mhr ON e.id = mhr.model_id 
    AND mhr.role_id = 38 
    AND mhr.model_type = 'App\\Employee'
WHERE mhr.model_id IS NULL 
    AND e.deleted_at IS NULL
LIMIT 5;

-- Test Case 2: Bulk attach employee to role (simulate bulkAttachEmployees)
-- First check if employee 46 is available
SELECT EXISTS(
    SELECT 1 FROM model_has_roles 
    WHERE role_id = 38 AND model_id = 46 AND model_type = 'App\\Employee'
) as employee_already_assigned;

-- Add employee 46 to role 38
INSERT INTO model_has_roles (role_id, model_type, model_id) 
VALUES (38, 'App\\Employee', 46);

-- Verify the addition
SELECT COUNT(*) as current_count FROM model_has_roles 
WHERE role_id = 38 AND model_type = 'App\\Employee';

-- Test Case 3: Verify employee 46 is no longer available
-- This should return no results since employee is now assigned
SELECT e.id, e.name, e.email, e.full_name
FROM employees e
LEFT JOIN model_has_roles mhr ON e.id = mhr.model_id 
    AND mhr.role_id = 38 
    AND mhr.model_type = 'App\\Employee'
WHERE mhr.model_id IS NULL 
    AND e.deleted_at IS NULL
    AND e.id = 46;

-- Test Case 4: Clean up test data
DELETE FROM model_has_roles 
WHERE role_id = 38 AND model_id = 46 AND model_type = 'App\\Employee';

-- Verify clean state
SELECT COUNT(*) as final_count FROM model_has_roles 
WHERE role_id = 38 AND model_type = 'App\\Employee';

-- Test Case 5: Check table structure for model_has_roles
-- Verify no timestamp columns exist
DESCRIBE model_has_roles;


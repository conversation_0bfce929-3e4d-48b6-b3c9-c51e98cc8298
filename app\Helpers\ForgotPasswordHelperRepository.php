<?php


namespace App\Helpers;


use Carbon\Carbon;

class ForgotPasswordHelperRepository extends \Illuminate\Auth\Passwords\DatabaseTokenRepository
{

    /**
     * Build the record payload for the table.
     * I wanted to add an extra column organization_id
     * organizationId() is a helper method I created
     * @param string $email
     * @param string $token
     * @return array
     */
    protected function getPayload($email, $token)
    {
        return ['email' => $email, 'token' => $this->hasher->make($token), 'created_at' => new Carbon,'organization_id' => organizationId()];
    }

}
/**
 * Created by it4om on 15/02/2017.
 */


$(document).ready(function () {


    $('#newsletter').on('submit', function (e) {
        e.preventDefault();
        var email_val = $("#email_newsletter_footer_input").val();
        $('#state_change').html('<i class="fa fa-refresh fa-spin"></i>');


        $.ajax({
            type: "POST",
            url: '/home/<USER>',
            data: {email: email_val},
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function (msg) {
                //alert(msg);

                if (msg == "403") {
                    _toastr("Email Not valid", "bottom-right", "error", false);
                    $('#state_change').html('<i class="fa fa-frown-o"></i>');
                }
                else if (msg == "duplicated") {
                    _toastr("Email already exist ", "bottom-right", "error", false);
                    $('#state_change').html('<i class="fa fa-frown-o"></i>');
                }
                else {

                    $('#state_change').html('<i class="fa fa-check-square-o"></i>');
                    $('#state_change').css('width', '50px');
                    $('#email_newsletter_footer_input').hide();
                    $('#submith_newsletter').hide();
                    _toastr("Welcome " + msg, "bottom-center", "success", false);
                }


            }

        });
    });


});
    


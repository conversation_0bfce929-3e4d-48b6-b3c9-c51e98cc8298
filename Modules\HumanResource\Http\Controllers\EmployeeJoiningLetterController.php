<?php

namespace Modules\HumanResource\Http\Controllers;

use App\CenterTranslation;
use App\Organization;
use App\Student;
use Doctrine\DBAL\Cache\CacheException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;

use Illuminate\Validation\ValidationException;
use Jenssegers\Agent\Agent;

use App\Employee;

use App\Authorizable;

use App\Attendance;
use Carbon\Carbon;
use App\LeaveRequest;
use App\Role;


class EmployeeJoiningLetterController extends Controller
{

    function __invoke(Request $r)
    {
        try {


            dd($r->get('file'));
            $validator = \Validator::make($r->all(), [
                'logo_pic' => 'sometimes|required|mimes:jpg,png|max:40000',
            ]);
            if ($validator->fails()) {
                return response()->json(['error' => 'error'], 201);
            }
            if ($r->hasFile('logo_pic')) {
                $file = $r->file('logo_pic');
                $images = \Image::make($file)->insert($file);
                $pathImage = 'public/uploads/student/';
                if (!file_exists($pathImage)) {
                    mkdir($pathImage, 0777, true);

                    $joining_letterTitle = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                    $joining_letter = 'public/uploads/staff/staff_joining_letter/' . $joining_letterTitle;

//                    $images->save('public/uploads/student/' . $name);
                    $images->save('uploads/staff/staff_joining_letter/', $joining_letterTitle);
//                    $imageName = 'public/uploads/student/' . $name;
//                    \Session::put('student_photo', $imageName);
                    \Session::put('student_photo', $joining_letter);
                } else {
                    $joining_letterTitle = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                    if (file_exists(\Session::get('student_photo'))) {
                        \File::delete(\Session::get('student_photo'));
                    }
                    $images->save('public/uploads/staff/staff_joining_letter/' . $joining_letterTitle);
                    $imageName = 'public/uploads/staff/staff_joining_letter/' . $joining_letterTitle;
                    // $data->student_photo =  $imageName;
                    \Session::put('student_photo', $imageName);
                }
            }




            return response()->json('success', 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'error'], 201);
        }
    }


}

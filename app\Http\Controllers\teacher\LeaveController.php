<?php

namespace App\Http\Controllers\teacher;

use App\Employee;
use App\YearCheck;
use App\ApiBaseMethod;
use App\LeaveRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Validator;

class LeaveController extends Controller
{
    public function __construct()
	{
        $this->middleware('PM');
        // User::checkAuth();
	}

    public function leaveTypeList(Request $request)
    {
        try {
            $leave_type = DB::table('leave_defines')
                ->where('role_id', 3 /** teacher **/)
                ->join('leave_types', 'leave_types.id', '=', 'leave_defines.type_id')
                ->where('leave_defines.active_status', 1)
                ->select('leave_types.id', 'type', 'total_days')
                ->distinct('leave_defines.type_id')
               ->where('leave_defines.organization_id',Auth::user()->organization_id) ->get();

            //return $leave_type;
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendResponse($leave_type, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function staffLeaveList(Request $request, $id)
    {
        try {
            $teacher = Employee::where('id', '=', $id)->first();
            $employee_id = $teacher->id;

            $leave_list = LeaveRequest::where('employee_id', '=', $employee_id)
                ->join('leave_defines', 'leave_defines.id', '=', 'leave_requests.leave_define_id')
                ->join('leave_types', 'leave_types.id', '=', 'leave_defines.type_id')
               ->where('leave_defines.organization_id',Auth::user()->organization_id) ->get();
            $status = 'P for Pending, A for Approve, R for reject';
            $data = [];
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data['leave_list'] = $leave_list->toArray();
                $data['status'] = $status;
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function applyLeave(Request $request)
    {
        $input = $request->all();
        if (ApiBaseMethod::checkUrl($request->fullUrl())) {
            $validator = Validator::make($input, [
                'apply_date' => "required",
                'leave_type' => "required",
                'leave_from' => 'required|before_or_equal:leave_to',
                'leave_to' => "required",
                'employee_id' => "required",
                'reason' => "required",
            ]);
        }
        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
        }
        try {
            $fileName = "";
            if ($request->file('attach_file') != "") {
                $file = $request->file('attach_file');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/leave_request/', $fileName);
                $fileName = 'public/uploads/leave_request/' . $fileName;
            }

            $apply_leave = new LeaveRequest();
            $apply_leave->employee_id = $request->input('employee_id');
            $apply_leave->role_id = 4;
            $apply_leave->apply_date = date('Y-m-d');
            $apply_leave->leave_define_id = $request->input('leave_type');
            $apply_leave->type_id = $request->input('leave_type');
            $apply_leave->leave_from = $request->input('leave_from');
            $apply_leave->leave_to = $request->input('leave_to');
            $apply_leave->approve_status = 'P';
            $apply_leave->reason = $request->input('reason');
            $apply_leave->organization_id = Auth::user()->organization_id;
            $apply_leave->academic_id = YearCheck::getAcademicId();
            //return $request->teacher_id;
            if ($fileName != "") {
                $apply_leave->file = $fileName;
            }

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {

                $result = $apply_leave->save();

                return ApiBaseMethod::sendResponse($result, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}

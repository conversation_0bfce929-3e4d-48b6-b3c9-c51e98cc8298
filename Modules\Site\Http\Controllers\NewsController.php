<?php

namespace Modules\Site\Http\Controllers;

use App\Employee;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\News;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Modules\Site\Http\Requests\NewsUpdateRequest;
use Session;
use Yajra\DataTables\Facades\DataTables;

class NewsController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view news|create news|edit news|delete news', ['only' => ['index','show']]);
        $this->middleware('permission:create news', ['only' => ['create','store']]);
        $this->middleware('permission:edit news', ['only' => ['edit','update', 'reorder']]);
        $this->middleware('permission:delete news', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Request $request)
    {

        if ($request->ajax()) {

            $newsDatatables = News::orderBy('position')->select();

//            $newsDatatables = \DB::select('SELECT * FROM news, news_translations WHERE news.id=news_translations.news_id AND news_translations.locale = "en"');

            return DataTables::eloquent($newsDatatables)
                ->setRowClass(function ($user) {
                    return 'newsRow';
                })
                ->setRowAttr([
                    'data-id' => function($news) {
                        return $news->id;
                    },
                ])
                ->addColumn('view', function ($row) use ($request) {
                    $btns = '';
                    $viewUrl = url(App::getLocale().'/news/'.$row->slug);
                    $btns .= '<a target="_blank" href="' . $viewUrl . '" title="View News"><button class="btn btn-primary btn-xs"><i class="fa fa-icon-eye" aria-hidden="true"></i>View</button></a>';




                        return $btns;



                })
                ->addColumn('position', function ($row) use ($request) {




                    return $row->position;



                })

                ->addColumn('action', function ($row) use ($request) {
                    $btns = '';
                    $editUrl = url('/workplace/site/news/' .$row->id .'/edit');
                    $btns .= '<a target="_blank" href="' . $editUrl . '" title="Edit Testimonial"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>';


                    $deleteBtnTitle = "delete " . $row->title;

//                    <button  type="submit"  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs  " onclick="return confirm('.$confirmMessage.')"><span class="glyphicon glyphicon-trash"
//                    /> Delete</button>
                    $confirmMessage = 'confirm delete?';
                    $btns .= '<button title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn "
                                                                                    data-id="'.$row->id.'" data-toggle="modal" id="getDeleteId"
                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
                                                                                                               title="' . $deleteBtnTitle . '"/></button>';






                        return $btns;



                })->rawColumns(['action','view'])
                ->toJson();
        }


        $news = News::paginate(25);
        

        if(isset($edit_mode)){
            return view(theme_path('news.index'));

        }else{
            return view('site::news.index', compact('news' , 'news_elements'));
        }
    }

    public function reorder(Request $request)
    {

        foreach($request->get('news') as $row)
        {

            News::find($row['id'])->update([
                'position' => $row['position']
            ]);
        }

        return response()->noContent();
    }
    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {

        $languages = config('app.locales');


        if(isset($edit_mode)){
            return view(theme_path('news_page'));
        }

        return view('site::news.create' , compact('languages'));
    }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
     public function store(Request $request)
     {
        if($request->type == 3){
            $request->slug= "";
        }
        if(!$request->parent){
            $request->parent = 0;
        }

        $request->slug = \Illuminate\Support\Str::slug($request->slug);
        
        $this->validateNews($request);
         $requestData = $request->all();
         
         $news =  new News;
 
         $news->image = $request->image;
         $news->status = $request->status;
         $news->slug = $request->slug;
        $news->featured = $request->featured;
        $news->created_at = $request->created_at;

        //  $news->type = $request->type;
         $news->organization_id  = config('organization_id');
 
 
         foreach ($request->translate as $code => $translate) {
             $news->translateOrNew($code)->title = $translate['title'];
             $news->translateOrNew($code)->content = $translate['content'];
         }
 
         $news->save();
 
        flash('News added!');
 
         return redirect(route('news.index'));
 
     }

 
     
    /**
     * Show the specified resource.
     * @return Response
     */
    public function show($slug)
    {

        $news = News::where('slug' , '=' , \Illuminate\Support\Str::slug($slug))->first();

        if($news){
            return view(theme_path('news_page') , compact('news'));
        }

        return 'Opps!!No Page';
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        


        $languages = config('app.locales');

        $news = News::findOrFail($id);


        if(isset($edit_mode)){
            return view(theme_path('news_page'));
        }

        return view('site::news.edit' , compact('languages' ,'news'));
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(NewsUpdateRequest $request , News $news)
    {



        $modifiedUserIdHere = Carbon::parse($request->created_at)->format('Y-m-d H:i:s');

        $news->image = $request->image;
        $news->featured = $request->featured;
        $news->created_at = $modifiedUserIdHere;
        $news->slug = $request->slug;
        $news->status = $request->status;

        foreach ($request->translate as $code => $translate) {
            $news->translateOrNew($code)->title = $translate['title'];
            $news->translateOrNew($code)->content = $translate['content'];
        }

        $news->save();
        
        flash('News updated!');

        return redirect(route('news.index'));

    }

    public function updateInplace(Request $request , $id)
    {
        $request->id = $id;

        $request->slug = \Illuminate\Support\Str::slug($request->slug);

        $this->validate($request , [
            "title" => 'required | min:3',
            "content" => 'required | min:20',
            "language" => 'required'
        ]);
        
        $news = News::findOrFail($id);

        $news->translateOrNew($request->language)->title = $request->title;
        $news->translateOrNew($request->language)->content = $request->content;

        $news->save();

       flash('News updated!');

        return response()->json([
            "status" => "success"
        ]);

    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy(Request $request, $id)
    {

        News::destroy($id);

//       flash('News deleted!');
        return response()->json(['success'=>'News deleted successfully']);

//        return redirect(route('news.index'));

    }

    public function widget_data()
    {
        $featured = News::where('featured',1)->orderBy('id' , 'DESC')->first();

        if(!$featured){
            $featured = News::orderBy('id','DESC')->first();
        }
        if($featured){

            $news = News::where('id' , '!=' , $featured->id )->orderBy('id','DESC')->take(4)->get();

        }else{
            $news = News::orderBy('id' ,'DESC')->take(4)->get();
        }



        //        $featured = News::where('featured' , 1 )->orderBy('id' , 'DESC')->first();
//        $featured = News::where('featured' , 1 )->orderBy('id' , 'DESC')->take(4)->get();
//
//
//        if(!$featured){
//            $news = News::whereNotIn('id',$featured->pluck('id'))->orderBy('id' , 'DESC')->take(4)->get();
//        }
//        if($featured){
//            $news = News::where('id' , ' != ' , $featured->id )->orderBy('id' , 'DESC')->take(4)->get();
//        }else{
//            $news = News::orderBy('id' , 'DESC')->take(4)->get();
//        }

        return [
            'featured' => $featured,
            'news' => $news
        ];
    }

    private function validateNews($request){
        $rules = [];
        $ignore = '';

        if(isset($request->id)){
            $ignore = ','.$request->id;
        }

//        $modifiedUserIdHere = Carbon::parse($request->created_at)->format('Y - m - d H:i:s');

//        change value of a request parameter for the created_at input
//        $request->merge([
////            'created_at' => $modifiedUserIdHere,
//        ]);


        $rules['image'] = 'required';                
        $rules['created_at'] = 'required | date';                
        
        $rules['translate .*.title'] = 'required | min:3';
        
        $rules['slug'] = 'required | alpha_dash | unique:news,slug'.$ignore;                

        // dd($request->all() , $rules);
        
        $this->validate($request , $rules);
    }


}

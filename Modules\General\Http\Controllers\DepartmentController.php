<?php

namespace Modules\General\Http\Controllers;

use App\Department;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
// use App\Message;
use App\Http\Requests;

class DepartmentController extends Controller
{

    public function index(){
        $departments    =   Department::all();
        $messages       =  []; //Message::limit(5)->get();
        return view('general::department.index',compact('messages','departments'));
    }
    
    public function create(){

        $messages   =   [];// Message::limit(5)->get();
        return view('general::department.create',compact('messages'));

    }

    public function store(Request $request){
        $this->validate($request,[
            "department" => "required",
        ]);
        $request->merge(["organization_id" => config("organization_id")]);

        $department=Department::create($request->all());

        return redirect(route('departments'));

    }


    public function edit($id){
        $department    =   Department::find($id);
        $messages       =  [];// Message::limit(5)->get();
        return view('general::department.edit',compact('department','messages'));
    }

    public function update(Request $request,$id){

        $this->validate($request,[
            "department" => "required",
        ]);

        Department::find($id)->update($request->all());

        return redirect(route('departments'));

    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
        return Request::all();
        // $department=Department::findOrFail($id);

        // $department->delete();

        // return redirect()->back();
    }

    public function delete($id){
    }


}

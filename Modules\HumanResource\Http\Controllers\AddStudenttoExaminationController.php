<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Admission;
use App\ApiBaseMethod;
use App\Attendance;
use App\BankAccount;
use App\BankAccountType;
use App\BaseSetup;
use App\Center;
use App\CenterEmployee;
use App\CenterTeacher;
use App\CenterTranslation;
use App\Classes;
use App\ClassTeacher;
use App\GeneralSettings;
use App\LeaveRequest;
use App\LeaveType;
use App\MissedClockOut;
use App\Notifications\UserStatusChangedToNewApplication;
use App\Notifications\WelcomeMailtoNewEmployeeNotification;
use App\PublicHoliday;
use App\Role;
use App\Employee;

use App\Student;
use App\Document;
use App\Traits\Notification;
use App\User;
use App\WeekDay;
use App\Weekend;
use App\YearCheck;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use DB;
use App\Permission;
use App\EmployeeSalary;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\EmployeeTimetable;
use App\Http\Controllers\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Modules\Admission\Notifications\StudentOfffered;
use Modules\HumanResource\Http\Requests\EmployeeStoreRequest;
use Modules\HumanResource\Http\Requests\EmployeeUpdateRequest;
use Modules\UserActivityLog\Traits\LogActivity;
use PhpOffice\PhpSpreadsheet\Calculation\DateTimeExcel\WorkDay;
use function PHPUnit\Framework\isNull;


class AddStudenttoExaminationController extends Controller
{

    use Notification;

    /**
     * @var Attendance
     */
    private $attendanceModal;


    public function __invoke(Request $request)
    {

        try {
            Student::where('user_id',$request->get('userId'))->update(['ready_for_exam' => 'on','exam_readiness_date' => NULL]);
            LogActivity::successLog($request->username . '- added to examination list.');
            Toastr::success(__('common.Staff info has been updated Successfully'));
            return response()->json('Student added to examination list',200);
        } catch (\Exception $e) {
            LogActivity::errorLog($e->getMessage());
            Toastr::error(__('common.Something Went Wrong'));
            return back();
        }

    }




}

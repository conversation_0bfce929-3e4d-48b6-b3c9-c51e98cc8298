<?php

namespace Modules\JobSeeker\Events;

use Illuminate\Queue\SerializesModels;
use App\CenterClassStudentsBackup;

class BackupCreated
{
    use SerializesModels;

    public $backup;
    public $centerName;
    public $centerId;

    public function __construct(CenterClassStudentsBackup $backup, string $centerName, int $centerId)
    {
        $this->backup = $backup;
        $this->centerName = $centerName;
        $this->centerId = $centerId;
    }
} 
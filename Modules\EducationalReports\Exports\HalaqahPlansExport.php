<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use Maatwebsite\Excel\Concerns\WithMultipleSheets;

final class HalaqahPlansExport implements WithMultipleSheets
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        return [
            new MonthlyPlansMemorizationSheet($this->filters),
            new MonthlyPlansRevisionSheet($this->filters),
        ];
    }
}
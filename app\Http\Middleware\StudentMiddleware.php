<?php

namespace App\Http\Middleware;

use Auth;
use Closure;
use Session;
use App\User;

class StudentMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {



        // if user is unauthenticated, redirect to login
        if(is_null(Auth::user())){
        session()->put(['role_id' => '']);
        return redirect('login');
        }



//        dd(\Auth::guard("web")->user()->roles()->get());
        session(['role_id' => Auth::user()->roles->where("name","student")->pluck('id')->toArray()[0]]);




        $role_id = Session::get('role_id');

        if (\Auth::guard("web")->user()->hasRole("student")) {
            return $next($request);
        } elseif ($role_id != "") {
            return redirect('admin-dashboard');
        } else {
            return redirect('login');
        }
    }
}

<?php

declare(strict_types=1);

namespace Modules\Education\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use App\Classes;
use App\Student;
use App\StudentNouranyaPlan;
use App\StudentNouranyaReport;
use Carbon\Carbon;

final class MonthEndNouranyaSummaryController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        try {
            $classId = (int)$request->input('classId');
            $monthYear = $request->input('classDate');

            if (!$classId || !$monthYear) {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            // Parse month and year using the same logic as MonthlyNouranyaReportController
            $date = Carbon::createFromFormat('M Y', $monthYear);
            $month = $date->month;
            $year = $date->year;

            // Get students with the same ordering as MonthlyPlanController
            $students = Student::whereHas('joint_classes', function ($query) use ($classId) {
                $query->where('class_id', $classId);
            })
            ->where('status', 'active')
            ->orderBy('full_name', 'asc') // Same ordering as MonthlyPlanController
            ->get();

            // Calculate summary metrics
            $studentCount = $students->count();
            $avgAttendance = $this->calculateAverageAttendance($students, $classId, $month, $year);
            $avgAchievement = $this->calculateAverageAchievement($students, $classId, $month, $year);
            $totalPlannedLessons = $this->calculateTotalPlannedLessons($students, $month, $year);
            $totalCompletedLessons = $this->calculateTotalCompletedLessons($students, $classId, $month, $year);

            $data = [[
                'noOfStudents' => $studentCount,
                'avgAttendance' => number_format($avgAttendance, 1) . '%',
                'avgAchievement' => number_format($avgAchievement, 1) . '%',
                'totalPlannedLessons' => $totalPlannedLessons,
                'totalCompletedLessons' => $totalCompletedLessons
            ]];

            return DataTables::of($data)->toJson();

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    private function calculateAverageAttendance($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) {
            return 0.0;
        }

        // Get total scheduled classes for the month
        $class = Classes::find($classId);
        if (!$class || !$class->timetable) {
            return 0.0;
        }

        $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
        if ($totalClasses <= 0) {
            return 0.0;
        }

        $totalAttendancePercentage = 0;
        $validStudents = 0;

        foreach ($students as $student) {
            // Count attended classes (both on-time and late)
            $attendedClasses = StudentNouranyaReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereIn('attendance_id', [1, 2]) // 1 = late, 2 = on time
                ->count();

            $attendancePercentage = min(100.0, ($attendedClasses / $totalClasses) * 100);
            $totalAttendancePercentage += $attendancePercentage;
            $validStudents++;
        }

        return $validStudents > 0 ? $totalAttendancePercentage / $validStudents : 0.0;
    }

    private function calculateAverageAchievement($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) {
            return 0.0;
        }

        $totalAchievementPercentage = 0;
        $validStudents = 0;

        foreach ($students as $student) {
            // Get the student's plan for the month
            $plan = StudentNouranyaPlan::where('student_id', $student->id)
                ->where(function ($query) use ($month, $year) {
                    $query->whereYear('start_date', $year)
                          ->whereMonth('start_date', $month);
                })
                ->first();

            if (!$plan) {
                continue;
            }

            // Get actual achievements from reports
            $reports = StudentNouranyaReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            if ($reports->isEmpty()) {
                continue;
            }

            // Simple calculation: if student has reports, they're making progress
            $totalReports = $reports->count();
            $expectedReports = 20; // Assuming ~20 working days per month
            $achievementPercentage = min(100.0, ($totalReports / $expectedReports) * 100);

            $totalAchievementPercentage += $achievementPercentage;
            $validStudents++;
        }

        return $validStudents > 0 ? $totalAchievementPercentage / $validStudents : 0.0;
    }

    private function calculateTotalPlannedLessons($students, int $month, int $year): int
    {
        if ($students->isEmpty()) {
            return 0;
        }

        $totalPlannedLessons = 0;

        foreach ($students as $student) {
            $plan = StudentNouranyaPlan::where('student_id', $student->id)
                ->where(function ($query) use ($month, $year) {
                    $query->whereYear('start_date', $year)
                          ->whereMonth('start_date', $month);
                })
                ->first();

            if ($plan) {
                // Count planned lessons based on from_lesson to to_lesson
                if ($plan->from_lesson && $plan->to_lesson) {
                    $fromLesson = (int)$plan->from_lesson;
                    $toLesson = (int)$plan->to_lesson;
                    $plannedLessons = max(0, $toLesson - $fromLesson + 1);
                    $totalPlannedLessons += $plannedLessons;
                }
            }
        }

        return $totalPlannedLessons;
    }

    private function calculateTotalCompletedLessons($students, int $classId, int $month, int $year): int
    {
        if ($students->isEmpty()) {
            return 0;
        }

        $totalCompletedLessons = 0;

        foreach ($students as $student) {
            $reports = StudentNouranyaReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            if ($reports->isNotEmpty()) {
                $firstReport = $reports->first();
                $lastReport = $reports->last();

                // Calculate completed lessons from first to last report
                if ($firstReport->from_lesson && $lastReport->to_lesson) {
                    $fromLesson = (int)$firstReport->from_lesson;
                    $toLesson = (int)$lastReport->to_lesson;
                    $completedLessons = max(0, $toLesson - $fromLesson + 1);
                    $totalCompletedLessons += $completedLessons;
                }
            }
        }

        return $totalCompletedLessons;
    }
} 
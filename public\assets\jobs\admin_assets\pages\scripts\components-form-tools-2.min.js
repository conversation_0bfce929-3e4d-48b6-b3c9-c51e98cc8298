var ComponentsFormTools=function(){var e=function(){var e=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.num)},queryTokenizer:Bloodhound.tokenizers.whitespace,local:[{num:"metronic"},{num:"keenthemes"},{num:"metronic theme"},{num:"metronic template"},{num:"keenthemes team"}]});e.initialize(),App.isRTL()&&$("#typeahead_example_1").attr("dir","rtl"),$("#typeahead_example_1").typeahead(null,{displayKey:"num",hint:App.isRTL()?!1:!0,source:e.ttAdapter()});var a=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.name)},queryTokenizer:Bloodhound.tokenizers.whitespace,limit:10,prefetch:{url:"../demo/typeahead_countries.json",filter:function(e){return $.map(e,function(e){return{name:e}})}}});a.initialize(),App.isRTL()&&$("#typeahead_example_2").attr("dir","rtl"),$("#typeahead_example_2").typeahead(null,{name:"typeahead_example_2",displayKey:"name",hint:App.isRTL()?!1:!0,source:a.ttAdapter()});var t=new Bloodhound({datumTokenizer:function(e){return e.tokens},queryTokenizer:Bloodhound.tokenizers.whitespace,remote:"../demo/typeahead_custom.php?query=%QUERY"});t.initialize(),App.isRTL()&&$("#typeahead_example_3").attr("dir","rtl"),$("#typeahead_example_3").typeahead(null,{name:"datypeahead_example_3",displayKey:"value",source:t.ttAdapter(),hint:App.isRTL()?!1:!0,templates:{suggestion:Handlebars.compile(['<div class="media">','<div class="pull-left">','<div class="media-object">','<img src="{{img}}" width="50" height="50"/>',"</div>","</div>",'<div class="media-body">','<h4 class="media-heading">{{value}}</h4>',"<p>{{desc}}</p>","</div>","</div>"].join(""))}});var o=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.team)},queryTokenizer:Bloodhound.tokenizers.whitespace,prefetch:"../demo/typeahead_nba.json"}),n=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.team)},queryTokenizer:Bloodhound.tokenizers.whitespace,prefetch:"../demo/typeahead_nhl.json"});o.initialize(),n.initialize(),App.isRTL()&&$("#typeahead_example_4").attr("dir","rtl"),$("#typeahead_example_4").typeahead({hint:App.isRTL()?!1:!0,highlight:!0},{name:"nba",displayKey:"team",source:o.ttAdapter(),templates:{header:"<h3>NBA Teams</h3>"}},{name:"nhl",displayKey:"team",source:n.ttAdapter(),templates:{header:"<h3>NHL Teams</h3>"}})},a=function(){var e=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.num)},queryTokenizer:Bloodhound.tokenizers.whitespace,local:[{num:"metronic"},{num:"keenthemes"},{num:"metronic theme"},{num:"metronic template"},{num:"keenthemes team"}]});e.initialize(),App.isRTL()&&$("#typeahead_example_modal_1").attr("dir","rtl"),$("#typeahead_example_modal_1").typeahead(null,{displayKey:"num",hint:App.isRTL()?!1:!0,source:e.ttAdapter()});var a=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.name)},queryTokenizer:Bloodhound.tokenizers.whitespace,limit:10,prefetch:{url:"../demo/typeahead_countries.json",filter:function(e){return $.map(e,function(e){return{name:e}})}}});a.initialize(),App.isRTL()&&$("#typeahead_example_modal_2").attr("dir","rtl"),$("#typeahead_example_modal_2").typeahead(null,{name:"typeahead_example_modal_2",displayKey:"name",hint:App.isRTL()?!1:!0,source:a.ttAdapter()});var t=new Bloodhound({datumTokenizer:function(e){return e.tokens},queryTokenizer:Bloodhound.tokenizers.whitespace,remote:"../demo/typeahead_custom.php?query=%QUERY"});t.initialize(),App.isRTL()&&$("#typeahead_example_modal_3").attr("dir","rtl"),$("#typeahead_example_modal_3").typeahead(null,{name:"datypeahead_example_modal_3",displayKey:"value",hint:App.isRTL()?!1:!0,source:t.ttAdapter(),templates:{suggestion:Handlebars.compile(['<div class="media">','<div class="pull-left">','<div class="media-object">','<img src="{{img}}" width="50" height="50"/>',"</div>","</div>",'<div class="media-body">','<h4 class="media-heading">{{value}}</h4>',"<p>{{desc}}</p>","</div>","</div>"].join(""))}});var o=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.team)},queryTokenizer:Bloodhound.tokenizers.whitespace,limit:3,prefetch:"../demo/typeahead_nba.json"}),n=new Bloodhound({datumTokenizer:function(e){return Bloodhound.tokenizers.whitespace(e.team)},queryTokenizer:Bloodhound.tokenizers.whitespace,limit:3,prefetch:"../demo/typeahead_nhl.json"});o.initialize(),n.initialize(),$("#typeahead_example_modal_4").typeahead({hint:App.isRTL()?!1:!0,highlight:!0},{name:"nba",displayKey:"team",source:o.ttAdapter(),templates:{header:"<h3>NBA Teams</h3>"}},{name:"nhl",displayKey:"team",source:n.ttAdapter(),templates:{header:"<h3>NHL Teams</h3>"}})},t=function(){$(".switch-radio1").on("switch-change",function(){$(".switch-radio1").bootstrapSwitch("toggleRadioState")}),$(".switch-radio1").on("switch-change",function(){$(".switch-radio1").bootstrapSwitch("toggleRadioStateAllowUncheck")}),$(".switch-radio1").on("switch-change",function(){$(".switch-radio1").bootstrapSwitch("toggleRadioStateAllowUncheck",!1)})},o=function(){$("#touchspin_demo1").TouchSpin({buttondown_class:"btn green",buttonup_class:"btn green",min:-1e9,max:1e9,stepinterval:50,maxboostedstep:1e7,prefix:"$"}),$("#touchspin_demo2").TouchSpin({buttondown_class:"btn blue",buttonup_class:"btn blue",min:0,max:100,step:.1,decimals:2,boostat:5,maxboostedstep:10,postfix:"%"}),$("#touchspin_demo3").TouchSpin({buttondown_class:"btn green",buttonup_class:"btn green",prefix:"$",postfix:"%"})},n=function(){$("#maxlength_defaultconfig").maxlength({limitReachedClass:"label label-danger"}),$("#maxlength_thresholdconfig").maxlength({limitReachedClass:"label label-danger",threshold:20}),$("#maxlength_alloptions").maxlength({alwaysShow:!0,warningClass:"label label-success",limitReachedClass:"label label-danger",separator:" out of ",preText:"You typed ",postText:" chars available.",validate:!0}),$("#maxlength_textarea").maxlength({limitReachedClass:"label label-danger",alwaysShow:!0}),$("#maxlength_placement").maxlength({limitReachedClass:"label label-danger",alwaysShow:!0,placement:App.isRTL()?"top-right":"top-left"})},s=function(){$("#spinner1").spinner(),$("#spinner2").spinner({disabled:!0}),$("#spinner3").spinner({value:0,min:0,max:10}),$("#spinner4").spinner({value:0,step:5,min:0,max:200})},i=function(){jQuery().tagsInput&&($("#tags_1").tagsInput({width:"auto",onAddTag:function(){}}),$("#tags_2").tagsInput({width:300}))},r=function(){$("#mask_date").inputmask("d/m/y",{autoUnmask:!0}),$("#mask_date1").inputmask("d/m/y",{placeholder:"*"}),$("#mask_date2").inputmask("d/m/y",{placeholder:"dd/mm/yyyy"}),$("#mask_phone").inputmask("mask",{mask:"(*************"}),$("#mask_tin").inputmask({mask:"99-9999999",placeholder:""}),$("#mask_number").inputmask({mask:"9",repeat:10,greedy:!1}),$("#mask_decimal").inputmask("decimal",{rightAlignNumerics:!1}),$("#mask_currency").inputmask("€ 999.999.999,99",{numericInput:!0}),$("#mask_currency2").inputmask("€ 999,999,999.99",{numericInput:!0,rightAlignNumerics:!1,greedy:!1}),$("#mask_ssn").inputmask("***********",{placeholder:" ",clearMaskOnLostFocus:!0})},d=function(){$("#input_ipv4").ipAddress(),$("#input_ipv6").ipAddress({v:6})},l=function(){var e=!1,a=$("#password_strength");a.keydown(function(){e===!1&&(a.pwstrength({raisePower:1.4,minChar:8,verdicts:["Weak","Normal","Medium","Strong","Very Strong"],scores:[17,26,40,50,60]}),a.pwstrength("addRule","demoRule",function(e,a,t){return a.match(/[a-z].[0-9]/)&&t},10,!0),e=!0)})},p=function(){var e=$("#username1_input");$("#username1_checker").click(function(a){var t=$(this);if(""===e.val())return e.closest(".form-group").removeClass("has-success").addClass("has-error"),t.popover("destroy"),t.popover({placement:App.isRTL()?"left":"right",html:!0,container:"body",content:"Please enter a username to check its availability."}),t.data("bs.popover").tip().addClass("error"),App.setLastPopedPopover(t),t.popover("show"),void a.stopPropagation();var o=$(this);o.attr("disabled",!0),e.attr("readonly",!0).attr("disabled",!0).addClass("spinner"),$.post("../demo/username_checker.php",{username:e.val()},function(a){o.attr("disabled",!1),e.attr("readonly",!1).attr("disabled",!1).removeClass("spinner"),"OK"==a.status?(e.closest(".form-group").removeClass("has-error").addClass("has-success"),t.popover("destroy"),t.popover({html:!0,placement:App.isRTL()?"left":"right",container:"body",content:a.message}),t.popover("show"),t.data("bs.popover").tip().removeClass("error").addClass("success")):(e.closest(".form-group").removeClass("has-success").addClass("has-error"),t.popover("destroy"),t.popover({html:!0,placement:App.isRTL()?"left":"right",container:"body",content:a.message}),t.popover("show"),t.data("bs.popover").tip().removeClass("success").addClass("error"),App.setLastPopedPopover(t))},"json")})},m=function(){$("#username2_input").change(function(){var e=$(this);return""===e.val()?(e.closest(".form-group").removeClass("has-error").removeClass("has-success"),void $(".fa-check, fa-warning",e.closest(".form-group")).remove()):(e.attr("readonly",!0).attr("disabled",!0).addClass("spinner"),void $.post("../demo/username_checker.php",{username:e.val()},function(a){e.attr("readonly",!1).attr("disabled",!1).removeClass("spinner"),"OK"==a.status?(e.closest(".form-group").removeClass("has-error").addClass("has-success"),$(".fa-warning",e.closest(".form-group")).remove(),e.before('<i class="fa fa-check"></i>'),e.data("bs.popover").tip().removeClass("error").addClass("success")):(e.closest(".form-group").removeClass("has-success").addClass("has-error"),$(".fa-check",e.closest(".form-group")).remove(),e.before('<i class="fa fa-warning"></i>'),e.popover("destroy"),e.popover({html:!0,placement:App.isRTL()?"left":"right",container:"body",content:a.message}),e.popover("show"),e.data("bs.popover").tip().removeClass("success").addClass("error"),App.setLastPopedPopover(e))},"json"))})};return{init:function(){e(),a(),t(),o(),n(),s(),i(),r(),d(),l(),p(),m()}}}();
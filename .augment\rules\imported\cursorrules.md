---
type: "manual"
---

You are an expert in Laravel, PHP, and related web development technologies.

Core Principles
- Write concise, technical responses with accurate PHP/Laravel examples.
- Prioritize SOLID principles for object-oriented programming and clean architecture.
- Follow P<PERSON> and <PERSON><PERSON> best practices, ensuring consistency and readability.
- Design for scalability and maintainability, ensuring the system can grow with ease.
- Prefer iteration and modularization over duplication to promote code reuse.
- Use consistent and descriptive names for variables, methods, and classes to improve readability.

Dependencies
- Composer for dependency management
- PHP 8.1+
- <PERSON>vel 10.0+

PHP and Laravel Standards
- Leverage PHP 8.1+ features when appropriate (e.g., typed properties, match expressions).
- Adhere to PSR-12 coding standards for consistent code style.
- Always use strict typing: declare(strict_types=1);
- Utilize Laravel's built-in features and helpers to maximize efficiency.
- Follow <PERSON><PERSON>'s directory structure and file naming conventions.
- Implement robust error handling and logging:
  > Use Laravel's exception handling and logging features.
  > Strategically use log levels (`debug`, `info`, `warning`, `error`, `critical`) to control verbosity and filter effectively.
  > Include specific context in logs (e.g., user IDs, relevant model IDs, request data snippets, state variables) to aid tracing.
  > Create custom exceptions when necessary.
- Use Laravel's validation features for form and request data.
- Implement middleware for request filtering and modification.
- Utilize Laravel's Eloquent ORM for database interactions.
- Use Laravel's query builder for complex database operations.
- Create and maintain proper database seeders.

Laravel Best Practices
- Use Eloquent ORM and Query Builder over raw SQL queries when possible
- Implement Repository and Service patterns for better code organization and reusability
- Utilize Laravel's built-in authentication and authorization features (Sanctum, Policies)
- Leverage Laravel's caching mechanisms (Redis, Memcached) for improved performance
- Use job queues and Laravel Horizon for handling long-running tasks and background processing
- Implement comprehensive testing using PHPUnit and Laravel Dusk for unit, feature, and browser tests
- Use API resources and versioning for building robust and maintainable APIs
- Implement proper error handling and logging using Laravel's exception handler and logging facade
- Utilize Laravel's validation features, including Form Requests, for data integrity
- Implement database indexing and use Laravel's query optimization features for better performance
- Use Laravel Telescope for debugging and performance monitoring in development
- Leverage Laravel Nova or Filament for rapid admin panel development
- Implement proper security measures, including CSRF protection, XSS prevention, and input sanitization

## Laravel Modules Structure (nwidart/laravel-modules)
This project uses the nwidart/laravel-modules package for modular architecture. Follow these strict organization patterns:

### Module Directory Structure
- **Controllers**: `Modules/{ModuleName}/Http/Controllers/`
  - Controllers should be final classes and read-only
  - Use method injection for dependencies or service classes
  - Example: `Modules/JobSeeker/Http/Controllers/UserDeviceTokenController.php`

- **Entities (Models)**: `Modules/{ModuleName}/Entities/`
  - Models should be final classes to ensure data integrity
  - Place all Eloquent models in the Entities directory
  - Example: `Modules/JobSeeker/Entities/JobSeeker.php`

- **Routes**: 
  - Web routes: `Modules/{ModuleName}/Http/routes.php`
  - API routes: `Modules/{ModuleName}/Http/api.php`
  - Ensure proper middleware and authentication configuration

- **Console Commands**: `Modules/{ModuleName}/Console/Commands/`
  - All custom Artisan commands for the module
  - Must be registered in the module's ServiceProvider
  - Example: `Modules/JobSeeker/Console/Commands/CleanupStaleDeviceTokensCommand.php`

- **Configuration**: `Modules/{ModuleName}/Config/`
  - Module-specific configuration files
  - Third-party service configurations (Firebase, etc.)
  - Example: `Modules/JobSeeker/Config/firebase-service-account.json`

- **Views**: `resources/views/modules/{module-name-lowercase}/`
  - **CRITICAL**: Views are NOT stored in `Modules/{ModuleName}/Resources/views/`
  - All Blade templates must be placed in the main resources directory
  - Use lowercase module names for directory structure
  - Example: `resources/views/modules/jobseeker/notifications.blade.php`

### Module ServiceProvider Registration
- Always register new console commands in the module's ServiceProvider
- Add commands to the `$commands` array in the `register()` method
- Example: Add `CleanupStaleDeviceTokensCommand::class` to the commands array

### Module-Specific Patterns
- Each module should be self-contained with its own business logic
- Use proper namespacing following the module structure
- Maintain consistent naming conventions across all modules
- Document module dependencies and interactions

### Integration Points
- Modules can interact through well-defined service interfaces
- Use Laravel's service container for cross-module dependencies
- Maintain loose coupling between modules for better maintainability

Key points
- Follow Laravel's MVC architecture for clear separation of business logic, data, and presentation layers.
- Implement request validation using Form Requests to ensure secure and validated data inputs.
- Use Laravel's built-in authentication system, including Laravel Sanctum for API token management.
- Ensure the REST API follows Laravel standards, using API Resources for structured and consistent responses.
- Leverage task scheduling and event listeners to automate recurring tasks and decouple logic.
- Implement database transactions using Laravel's database facade to ensure data consistency.
- Use Eloquent ORM for database interactions, enforcing relationships and optimizing queries.
- Implement API versioning for maintainability and backward compatibility.
- Optimize performance with caching mechanisms like Redis and Memcached.
- Ensure robust error handling and logging using Laravel's exception handler and logging features.

You are an expert in Bootstrap and modern web application development.

    Key Principles
    - Write clear, concise, and technical responses with precise Bootstrap examples.
    - Utilize Bootstrap's components and utilities to streamline development and ensure responsiveness.
    - Prioritize maintainability and readability; adhere to clean coding practices throughout your HTML and CSS.
    - Use descriptive class names and structure to promote clarity and collaboration among developers.

    Bootstrap Usage
    - Leverage Bootstrap's grid system for responsive layouts; use container, row, and column classes to structure content.
    - Utilize Bootstrap components (e.g., buttons, modals, alerts) to enhance user experience without extensive custom CSS.
    - Apply Bootstrap's utility classes for quick styling adjustments, such as spacing, typography, and visibility.
    - Ensure all components are accessible; use ARIA attributes and semantic HTML where applicable.

    Error Handling and Validation
    - Implement form validation using Bootstrap's built-in styles and classes to enhance user feedback.
    - Use Bootstrap's alert component to display error messages clearly and informatively.
    - Structure forms with appropriate labels, placeholders, and error messages for a better user experience.

    Dependencies
    - Bootstrap (latest version, CSS and JS)
    - Any JavaScript framework (like jQuery, if required) for interactive components.

    Bootstrap-Specific Guidelines
    - Customize Bootstrap's Sass variables and mixins to create a unique theme without overriding default styles.
    - Utilize Bootstrap's responsive utilities to control visibility and layout on different screen sizes.
    - Keep custom styles to a minimum; use Bootstrap's classes wherever possible for consistency.
    - Use the Bootstrap documentation to understand component behavior and customization options.

    Performance Optimization
    - Minimize file sizes by including only the necessary Bootstrap components in your build process.
    - Use a CDN for Bootstrap resources to improve load times and leverage caching.
    - Optimize images and other assets to enhance overall performance, especially for mobile users.

    Key Conventions
    1. Follow Bootstrap's naming conventions and class structures to ensure consistency across your project.
    2. Prioritize responsiveness and accessibility in every stage of development.
    3. Maintain a clear and organized file structure to enhance maintainability and collaboration.

    Refer to the Bootstrap documentation for best practices and detailed examples of usage patterns.

Database Schema Management
- NEVER create or provide Laravel migration files under any circumstances. ALWAYS provide direct MySQL SQL statements instead.
- All database schema changes must be written as SQL files in the database/sql_files/ directory with clear naming conventions
- Each SQL file should include comments explaining what the changes are doing and why
- Include proper MySQL indexing and foreign key constraints
- When any user requests to create migrations, database changes, or schema modifications, automatically convert these to SQL statements
- Under no circumstances should you generate code for Laravel migration files, even if explicitly requested
- If asked to create or modify database structure, always interpret this as a request for direct SQL statements
- All SQL files should be prefixed with a timestamp in format YYYYMMDD_HHMMSS_ followed by a descriptive name of the change   

### Mobile/Responsive Implementation Standards
When creating mobile or alternative versions of existing desktop functionality:

**Pre-Implementation Analysis (MANDATORY)**
- ALWAYS examine ALL existing AJAX endpoints, payload structures, and URL patterns before starting
- Maintain exact parameter names, field structures, and URL patterns from desktop versions
- Ask about specific device breakpoint requirements - never assume standard breakpoints (768px, etc.)
- Identify all data attributes, form fields, and JavaScript functions used in original implementation

**Payload Structure Consistency (CRITICAL)**
- Examine existing AJAX payload structures line-by-line before implementing alternatives
- Maintain exact field names (e.g., `hefz_plans_id` not `hefzplan_id`)
- Preserve all data relationships and parameter structures
- Never modify parameter names without explicit user requirements
- Test payload compatibility with existing backend endpoints

**URL Pattern Consistency (CRITICAL)**
- Maintain exact URL structures including all path parameters from original implementations
- Never omit required URL parameters when creating alternative versions (e.g., `/from-surah/28/get-ayats/12655` not `/from-surah/28/get-ayats`)
- Verify all AJAX endpoints match existing patterns exactly
- Include all route parameters that desktop version uses

### Multi-Issue Resolution Protocol
When users provide multiple specific issues to fix:

**Complete Resolution Requirement**
- Address ALL issues completely in a single response, not partial solutions
- Don't provide incremental fixes that require follow-up iterations
- Verify each issue is fully resolved before responding
- If unable to fix all issues, explicitly state which ones remain and why

**Issue Tracking**
- List each issue clearly at the start of response
- Provide specific solutions for each numbered issue
- Confirm resolution of each issue at the end of response
- Never leave issues partially addressed without explicit explanation

### Search/Filter Functionality Standards
When adding search or filter functionality to existing UI components:

**Layout Impact Assessment**
- Test search functionality impact on accordion layouts, positioning, and animations
- Use CSS transitions instead of show/hide for smoother user experience
- Implement search with classes like `search-hidden` rather than display manipulation
- Ensure search doesn't disturb existing component positioning or behavior

**User Experience Consistency**
- Maintain smooth animations and transitions during search operations
- Preserve accordion states and user interactions during filtering
- Test search functionality across all target devices and breakpoints

### Comprehensive Solution Delivery Standards

**Production-Ready Implementation**
- Provide complete, thoroughly tested solutions rather than partial implementations
- Consider all side effects of new functionality on existing UI components
- Include error handling, loading states, and user feedback mechanisms
- Test functionality across all specified devices and breakpoints

**Code Quality Assurance**
- Ensure all AJAX calls include proper error handling and loading states
- Maintain consistent coding patterns with existing codebase
- Include all necessary CSS animations and transitions
- Verify JavaScript functions don't conflict with existing code

**Documentation and Clarity**
- Clearly explain what each code section does and why
- Highlight any changes from original implementation patterns
- Provide specific line numbers and file locations for changes
- Include testing recommendations for implemented functionality

### Critical Failure Prevention
These rules exist to prevent:
- Repeated back-and-forth iterations due to incomplete solutions
- Payload structure mismatches causing AJAX failures
- URL pattern inconsistencies breaking existing functionality
- Search/filter features disrupting UI component behavior
- Partial implementations requiring multiple fix cycles

**ENFORCEMENT**: These rules are MANDATORY for all mobile/responsive implementations, multi-issue resolutions, and alternative functionality development. Violation of these rules leads to user frustration and development inefficiency.   
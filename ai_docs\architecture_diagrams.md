# Itqan Al Quran - Architecture Diagrams

This document contains architectural diagrams representing the structure and flow of the Itqan Al Quran system.

## System Architecture Overview

The following diagram illustrates the high-level architecture of the Itqan Al Quran system.

```mermaid
graph TD
    A[Client Browser] --> B[Web Server]
    B --> C[Laravel Application]
    
    subgraph "Laravel Framework"
    C --> D[Route Handling]
    D --> E[Middleware Pipeline]
    E --> F[Controller]
    F --> G1[Service Layer]
    G1 --> H[Repository Layer]
    H --> I[Eloquent ORM]
    I --> J[Database]
    end
    
    subgraph "Modular Structure"
    F --> M1[Admission Module]
    F --> M2[Education Module]
    F --> M3[JobSeeker Module]
    F --> M4[Communication Module]
    F --> M5[Other Modules...]
    end
    
    G1 --> K[Blade Templates]
    K --> L[View Rendering]
    L --> A
```

## Module Interaction Diagram

The following diagram shows how different modules interact with each other.

```mermaid
graph LR
    A[Education Module] --> B[Communication Module]
    C[JobSeeker Module] --> B
    D[Admission Module] --> B
    D --> A
    E[HumanResource Module] --> B
    E --> A
    F[Transportation Module] --> B
    G[MajorTrack Module] --> A
```

## Request Flow Diagram

The following diagram illustrates the flow of a request through the system.

```mermaid
sequenceDiagram
    participant Client
    participant Router
    participant Middleware
    participant Controller
    participant Service
    participant Repository
    participant Model
    participant Database
    participant View
    
    Client->>Router: HTTP Request
    Router->>Middleware: Route Match
    Middleware->>Controller: Processed Request
    Controller->>Service: Method Call
    Service->>Repository: Data Operation
    Repository->>Model: Query Building
    Model->>Database: SQL Query
    Database->>Model: Query Result
    Model->>Repository: Eloquent Collection/Model
    Repository->>Service: Data Response
    Service->>Controller: Business Logic Result
    Controller->>View: Data for Rendering
    View->>Client: HTTP Response
```

## Module Structure Diagram

The following diagram shows the internal structure of a typical module.

```mermaid
graph TD
    A[Module] --> B[Http]
    A --> C[Entities]
    A --> D[Services]
    A --> E[Repositories]
    A --> F[Providers]
    A --> G[Events]
    A --> H[Listeners]
    A --> I[Console]
    A --> J[Config]
    
    B --> B1[Controllers]
    B --> B2[Middleware]
    B --> B3[Requests]
    B --> B4[routes.php]
    B --> B5[api.php]
    
    C --> C1[Model Classes]
    
    D --> D1[Service Classes]
    
    E --> E1[Repository Interfaces]
    E --> E2[Repository Implementations]
    
    F --> F1[Service Providers]
    
    G --> G1[Event Classes]
    
    H --> H1[Listener Classes]
    
    I --> I1[Command Classes]
```

## Database Relationship Overview

The following diagram provides a simplified view of key database relationships.

```mermaid
erDiagram
    STUDENT ||--o{ ENROLLMENT : has
    COURSE ||--o{ ENROLLMENT : includes
    TEACHER ||--o{ COURSE : teaches
    STUDENT ||--o{ ATTENDANCE : records
    COURSE ||--o{ LESSON : contains
    LESSON ||--o{ GRADE : has
    STUDENT ||--o{ GRADE : receives
    JOB_SEEKER ||--o{ JOB_APPLICATION : submits
    JOB ||--o{ JOB_APPLICATION : receives
    EMPLOYEE }|--|| HUMAN_RESOURCE : manages
```

## View Location Structure

The following diagram illustrates the unique view structure used in the project.

```mermaid
graph TD
    A[resources/views/] --> B[modules/]
    B --> C1[admission/]
    B --> C2[education/]
    B --> C3[jobseeker/]
    B --> C4[communication/]
    B --> C5[Other module views...]
    
    A --> D[layouts/]
    D --> D1[app.blade.php]
    D --> D2[master.blade.php]
    D --> D3[Other layouts...]
    
    A --> E[partials/]
    E --> E1[header.blade.php]
    E --> E2[footer.blade.php]
    E --> E3[Other partials...]
```

## Authentication Flow

The following diagram shows the authentication flow in the system.

```mermaid
sequenceDiagram
    participant User
    participant LoginForm
    participant AuthController
    participant AuthService
    participant Database
    participant Session
    
    User->>LoginForm: Enter Credentials
    LoginForm->>AuthController: Submit Credentials
    AuthController->>AuthService: Authenticate
    AuthService->>Database: Validate Credentials
    Database->>AuthService: User Data
    AuthService->>Session: Store Authentication
    Session->>AuthController: Authentication Result
    AuthController->>User: Redirect to Dashboard/Error
```

## JobSeeker Module Flow

The following diagram shows the flow in the JobSeeker module.

```mermaid
graph TD
    A[JobSeeker Registration] --> B[Profile Creation]
    B --> C[Browse Jobs]
    C --> D[Apply for Job]
    D --> E[Submit Application]
    E --> F[Receive Notification]
    
    G[Employer] --> H[Create Job]
    H --> I[Review Applications]
    I --> J[Schedule Interview]
    J --> K[Make Decision]
    
    L[System] --> M[Send Email Notifications]
    L --> N[Process Applications]
    L --> O[Track Statistics]
```

These diagrams provide a visual representation of the Itqan Al Quran system architecture and can be used for reference and onboarding new developers. 
/*
Version: 3.2 Timestamp: Mon Sep 10 10:38:04 PDT 2012
*/
.select2-container {
    position: relative;
    display: inline-block;
    /* inline-block for ie7 */
    zoom: 1;
    *display: inline;
    vertical-align: top;
}

.select2-container,
.select2-drop,
.select2-search,
.select2-search input{
  /*
    Force border-box so that % widths fit the parent
    container without overlap because of margin/padding.

    More Info : http://www.quirksmode.org/css/box.html
  */
  -moz-box-sizing: border-box;    /* firefox */
  -ms-box-sizing: border-box;     /* ie */
  -webkit-box-sizing: border-box; /* webkit */
  -khtml-box-sizing: border-box;  /* konqueror */
  box-sizing: border-box;         /* css3 */
}

.select2-container .select2-choice {
    background-color: #fff;
    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eeeeee), color-stop(0.5, white));
    background-image: -webkit-linear-gradient(center bottom, #eeeeee 0%, white 50%);
    background-image: -moz-linear-gradient(center bottom, #eeeeee 0%, white 50%);
    background-image: -o-linear-gradient(bottom, #eeeeee 0%, #ffffff 50%);
    background-image: -ms-linear-gradient(top, #eeeeee 0%, #ffffff 50%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr = '#eeeeee', endColorstr = '#ffffff', GradientType = 0);
    background-image: linear-gradient(top, #eeeeee 0%, #ffffff 50%);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #aaa;
    display: block;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    height: 26px;
    line-height: 26px;
    padding: 0 0 0 8px;
    color: #444;
    text-decoration: none;
}

.select2-container.select2-drop-above .select2-choice
{
    border-bottom-color: #aaa;
    -webkit-border-radius:0px 0px 4px 4px;
    -moz-border-radius:0px 0px 4px 4px;
    border-radius:0px 0px 4px 4px;
    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eeeeee), color-stop(0.9, white));
    background-image: -webkit-linear-gradient(center bottom, #eeeeee 0%, white 90%);
    background-image: -moz-linear-gradient(center bottom, #eeeeee 0%, white 90%);
    background-image: -o-linear-gradient(bottom, #eeeeee 0%, white 90%);
    background-image: -ms-linear-gradient(top, #eeeeee 0%,#ffffff 90%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#eeeeee', endColorstr='#ffffff',GradientType=0 );
    background-image: linear-gradient(top, #eeeeee 0%,#ffffff 90%);
}

.select2-container .select2-choice span {
    margin-right: 26px;
    display: block;
    overflow: hidden;
    white-space: nowrap;
    -o-text-overflow: ellipsis;
    -ms-text-overflow: ellipsis;
    text-overflow: ellipsis;
}

.select2-container .select2-choice abbr {
  display: block;
  position: absolute;
  right: 26px;
  top: 8px;
  width: 12px;
  height: 12px;
  font-size: 1px;
  background: url('select2.png') right top no-repeat;
  cursor: pointer;
  text-decoration: none;
  border:0;
  outline: 0;
}
.select2-container .select2-choice abbr:hover {
  background-position: right -11px;
  cursor: pointer;
}

.select2-drop {
    background: #fff;
    color: #000;
    border: 1px solid #aaa;
    border-top: 0;
    position: absolute;
    top: 100%;
    -webkit-box-shadow: 0 4px 5px rgba(0, 0, 0, .15);
    -moz-box-shadow: 0 4px 5px rgba(0, 0, 0, .15);
    -o-box-shadow: 0 4px 5px rgba(0, 0, 0, .15);
    box-shadow: 0 4px 5px rgba(0, 0, 0, .15);
    z-index: 9999;
    width:100%;
    margin-top:-1px;

  -webkit-border-radius: 0 0 4px 4px;
  -moz-border-radius: 0 0 4px 4px;
  border-radius: 0 0 4px 4px;
}

.select2-drop.select2-drop-above {
    -webkit-border-radius: 4px 4px 0px 0px;
    -moz-border-radius: 4px 4px 0px 0px;
    border-radius: 4px 4px 0px 0px;
    margin-top:1px;
    border-top: 1px solid #aaa;
    border-bottom: 0;

    -webkit-box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);
    -moz-box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);
    -o-box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);
    box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);
}

.select2-container .select2-choice div {
    -webkit-border-radius: 0 4px 4px 0;
    -moz-border-radius: 0 4px 4px 0;
    border-radius: 0 4px 4px 0;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    background: #ccc;
    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #ccc), color-stop(0.6, #eee));
    background-image: -webkit-linear-gradient(center bottom, #ccc 0%, #eee 60%);
    background-image: -moz-linear-gradient(center bottom, #ccc 0%, #eee 60%);
    background-image: -o-linear-gradient(bottom, #ccc 0%, #eee 60%);
    background-image: -ms-linear-gradient(top, #cccccc 0%, #eeeeee 60%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr = '#cccccc', endColorstr = '#eeeeee', GradientType = 0);
    background-image: linear-gradient(top, #cccccc 0%, #eeeeee 60%);
    border-left: 1px solid #aaa;
    position: absolute;
    right: 0;
    top: 0;
    display: block;
    height: 100%;
    width: 18px;
}

.select2-container .select2-choice div b {
    background: url('select2.png') no-repeat 0 1px;
    display: block;
    width: 100%;
    height: 100%;
}

.select2-search {
  display: inline-block;
    white-space: nowrap;
    z-index: 10000;
  min-height: 26px;
  width: 100%;
  margin: 0;
  padding-left: 4px;
  padding-right: 4px;
}

.select2-search-hidden {
  display: block;
  position: absolute;
  left: -10000px;
}

.select2-search input {
    background: #fff url('select2.png') no-repeat 100% -22px;
    background: url('select2.png') no-repeat 100% -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, white), color-stop(0.99, #eeeeee));
    background: url('select2.png') no-repeat 100% -22px, -webkit-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url('select2.png') no-repeat 100% -22px, -moz-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url('select2.png') no-repeat 100% -22px, -o-linear-gradient(bottom, white 85%, #eeeeee 99%);
    background: url('select2.png') no-repeat 100% -22px, -ms-linear-gradient(top, #ffffff 85%, #eeeeee 99%);
    background: url('select2.png') no-repeat 100% -22px, linear-gradient(top, #ffffff 85%, #eeeeee 99%);
    padding: 4px 20px 4px 5px;
    outline: 0;
    border: 1px solid #aaa;
    font-family: sans-serif;
    font-size: 1em;
    width:100%;
    margin:0;
    height:auto !important;
    min-height: 26px;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
}

.select2-drop.select2-drop-above .select2-search input
{
    margin-top:4px;
}

.select2-search input.select2-active {
    background: #fff url('spinner.gif') no-repeat 100%;
    background: url('spinner.gif') no-repeat 100%, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, white), color-stop(0.99, #eeeeee));
    background: url('spinner.gif') no-repeat 100%, -webkit-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url('spinner.gif') no-repeat 100%, -moz-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url('spinner.gif') no-repeat 100%, -o-linear-gradient(bottom, white 85%, #eeeeee 99%);
    background: url('spinner.gif') no-repeat 100%, -ms-linear-gradient(top, #ffffff 85%, #eeeeee 99%);
    background: url('spinner.gif') no-repeat 100%, linear-gradient(top, #ffffff 85%, #eeeeee 99%);
}


.select2-container-active .select2-choice,
.select2-container-active .select2-choices {
    -webkit-box-shadow: 0 0 5px rgba(0,0,0,.3);
    -moz-box-shadow   : 0 0 5px rgba(0,0,0,.3);
    -o-box-shadow     : 0 0 5px rgba(0,0,0,.3);
    box-shadow        : 0 0 5px rgba(0,0,0,.3);
    border: 1px solid #5897fb;
    outline: none;
}

.select2-dropdown-open .select2-choice {
  border: 1px solid #aaa;
  border-bottom-color: transparent;
  -webkit-box-shadow: 0 1px 0 #fff inset;
  -moz-box-shadow   : 0 1px 0 #fff inset;
  -o-box-shadow     : 0 1px 0 #fff inset;
  box-shadow        : 0 1px 0 #fff inset;
  background-color: #eee;
  background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, white), color-stop(0.5, #eeeeee));
  background-image: -webkit-linear-gradient(center bottom, white 0%, #eeeeee 50%);
  background-image: -moz-linear-gradient(center bottom, white 0%, #eeeeee 50%);
  background-image: -o-linear-gradient(bottom, white 0%, #eeeeee 50%);
  background-image: -ms-linear-gradient(top, #ffffff 0%,#eeeeee 50%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#eeeeee',GradientType=0 );
  background-image: linear-gradient(top, #ffffff 0%,#eeeeee 50%);
  -webkit-border-bottom-left-radius : 0;
  -webkit-border-bottom-right-radius: 0;
  -moz-border-radius-bottomleft : 0;
  -moz-border-radius-bottomright: 0;
  border-bottom-left-radius : 0;
  border-bottom-right-radius: 0;
}

.select2-dropdown-open .select2-choice div {
  background: transparent;
  border-left: none;
}
.select2-dropdown-open .select2-choice div b {
  background-position: -18px 1px;
}

/* results */
.select2-results {
  margin: 4px 4px 4px 0;
  padding: 0 0 0 4px;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 200px;
}

.select2-results ul.select2-result-sub {
  margin: 0 0 0 0;
}

.select2-results ul.select2-result-sub > li .select2-result-label { padding-left: 20px }
.select2-results ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 40px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 60px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 80px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 100px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 110px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 120px }

.select2-results li {
  list-style: none;
  display: list-item;
}

.select2-results li.select2-result-with-children > .select2-result-label {
  font-weight: bold;
}

.select2-results .select2-result-label {
  padding: 3px 7px 4px;
  margin: 0;
  cursor: pointer;
}

.select2-results .select2-highlighted {
  background: #3875d7;
  color: #fff;
}
.select2-results li em {
  background: #feffde;
  font-style: normal;
}
.select2-results .select2-highlighted em {
  background: transparent;
}
.select2-results .select2-no-results,
.select2-results .select2-searching,
.select2-results .select2-selection-limit {
  background: #f4f4f4;
  display: list-item;
}

/*
disabled look for already selected choices in the results dropdown
.select2-results .select2-disabled.select2-highlighted {
    color: #666;
    background: #f4f4f4;
    display: list-item;
    cursor: default;
}
.select2-results .select2-disabled {
  background: #f4f4f4;
  display: list-item;
  cursor: default;
}
*/
.select2-results .select2-disabled {
    display: none;
}

.select2-more-results.select2-active {
    background: #f4f4f4 url('spinner.gif') no-repeat 100%;
}

.select2-more-results {
  background: #f4f4f4;
  display: list-item;
}

/* disabled styles */

.select2-container.select2-container-disabled .select2-choice {
    background-color: #f4f4f4;
    background-image: none;
    border: 1px solid #ddd;
    cursor: default;
}

.select2-container.select2-container-disabled .select2-choice div {
    background-color: #f4f4f4;
    background-image: none;
    border-left: 0;
}


/* multiselect */

.select2-container-multi .select2-choices {
    background-color: #fff;
      background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(1%, #eeeeee), color-stop(15%, #ffffff));
      background-image: -webkit-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
      background-image: -moz-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
      background-image: -o-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
      background-image: -ms-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
      background-image: linear-gradient(top, #eeeeee 1%, #ffffff 15%);
      border: 1px solid #aaa;
      margin: 0;
      padding: 0;
      cursor: text;
      overflow: hidden;
      height: auto !important;
      height: 1%;
      position: relative;
}

.select2-container-multi .select2-choices {
    min-height: 26px;
}

.select2-container-multi.select2-container-active .select2-choices {
    -webkit-box-shadow: 0 0 5px rgba(0,0,0,.3);
    -moz-box-shadow   : 0 0 5px rgba(0,0,0,.3);
    -o-box-shadow     : 0 0 5px rgba(0,0,0,.3);
    box-shadow        : 0 0 5px rgba(0,0,0,.3);
    border: 1px solid #5897fb;
    outline: none;
}
.select2-container-multi .select2-choices li {
  float: left;
  list-style: none;
}
.select2-container-multi .select2-choices .select2-search-field {
  white-space: nowrap;
  margin: 0;
  padding: 0;
}

.select2-container-multi .select2-choices .select2-search-field input {
  color: #666;
  background: transparent !important;
  font-family: sans-serif;
  font-size: 100%;
  height: 15px;
  padding: 5px;
  margin: 1px 0;
  outline: 0;
  border: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow   : none;
  -o-box-shadow     : none;
  box-shadow        : none;
}

.select2-container-multi .select2-choices .select2-search-field input.select2-active {
    background: #fff url('spinner.gif') no-repeat 100% !important;
}

.select2-default {
  color: #999 !important;
}

.select2-container-multi .select2-choices .select2-search-choice {
  -webkit-border-radius: 3px;
  -moz-border-radius   : 3px;
  border-radius        : 3px;
  -moz-background-clip   : padding;
  -webkit-background-clip: padding-box;
  background-clip        : padding-box;
  background-color: #e4e4e4;
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f4f4f4', endColorstr='#eeeeee', GradientType=0 );
  background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eeeeee));
  background-image: -webkit-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: -moz-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: -o-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: -ms-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  -webkit-box-shadow: 0 0 2px #ffffff inset, 0 1px 0 rgba(0,0,0,0.05);
  -moz-box-shadow   : 0 0 2px #ffffff inset, 0 1px 0 rgba(0,0,0,0.05);
  box-shadow        : 0 0 2px #ffffff inset, 0 1px 0 rgba(0,0,0,0.05);
  color: #333;
  border: 1px solid #aaaaaa;
  line-height: 13px;
  padding: 3px 5px 3px 18px;
  margin: 3px 0 3px 5px;
  position: relative;
  cursor: default;
}
.select2-container-multi .select2-choices .select2-search-choice span {
  cursor: default;
}
.select2-container-multi .select2-choices .select2-search-choice-focus {
  background: #d4d4d4;
}

.select2-search-choice-close {
  display: block;
  position: absolute;
  right: 3px;
  top: 4px;
  width: 12px;
  height: 13px;
  font-size: 1px;
  background: url('select2.png') right top no-repeat;
  outline: none;
}

.select2-container-multi .select2-search-choice-close {
  left: 3px;
}


.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover {
  background-position: right -11px;
}
.select2-container-multi .select2-choices .select2-search-choice-focus .select2-search-choice-close {
  background-position: right -11px;
}

/* disabled styles */

.select2-container-multi.select2-container-disabled .select2-choices{
    background-color: #f4f4f4;
    background-image: none;
    border: 1px solid #ddd;
    cursor: default;
}

.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice {
    background-image: none;
    background-color: #f4f4f4;
    border: 1px solid #ddd;
    padding: 3px 5px 3px 5px;
}

.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close {
    display: none;
}
/* end multiselect */

.select2-result-selectable .select2-match,
.select2-result-unselectable .select2-result-selectable .select2-match { text-decoration: underline; }
.select2-result-unselectable .select2-match { text-decoration: none; }

.select2-offscreen { position: absolute; left: -10000px; }

/* Retina-ize icons */

@media only screen and (-webkit-min-device-pixel-ratio: 1.5) {
	.select2-search input, .select2-search-choice-close, .select2-container .select2-choice abbr, .select2-container .select2-choice div b {
		background-image: url(select2x2.png) !important;
		background-repeat: no-repeat !important;
		background-size: 60px 40px !important;
	}
	.select2-search input {
		background-position: 100% -21px !important;
	}
}

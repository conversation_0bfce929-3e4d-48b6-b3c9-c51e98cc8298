<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * JobSyncAlertEvent Entity
 * 
 * Tracks individual alert events and their lifecycle
 * 
 * @property int $id
 * @property int $rule_id
 * @property int|null $execution_id
 * @property string $alert_type
 * @property string $alert_message
 * @property array $alert_data
 * @property string $status
 * @property \Illuminate\Support\Carbon $fired_at
 * @property \Illuminate\Support\Carbon|null $acknowledged_at
 * @property \Illuminate\Support\Carbon|null $resolved_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read JobSyncAlertRule $alertRule
 * @property-read JobSyncAlertRule $rule
 * @property-read CommandScheduleExecution|null $execution
 * @property-read string $status_badge
 * @property-read string $duration_since_fired
 */
final class JobSyncAlertEvent extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'job_sync_alert_events';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'rule_id',
        'execution_id',
        'alert_type',
        'alert_message',
        'alert_data',
        'status',
        'fired_at',
        'acknowledged_at',
        'resolved_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rule_id' => 'integer',
        'execution_id' => 'integer',
        'alert_data' => 'array',
        'fired_at' => 'datetime',
        'acknowledged_at' => 'datetime',
        'resolved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Status constants
     */
    public const STATUS_NEW = 'new';
    public const STATUS_ACKNOWLEDGED = 'acknowledged';
    public const STATUS_RESOLVED = 'resolved';

    /**
     * Get the alert rule that owns this event
     *
     * @return BelongsTo
     */
    public function alertRule(): BelongsTo
    {
        return $this->belongsTo(JobSyncAlertRule::class, 'rule_id');
    }

    /**
     * Alias for alertRule for backward compatibility
     *
     * @return BelongsTo
     */
    public function rule(): BelongsTo
    {
        return $this->alertRule();
    }

    /**
     * Get the command schedule execution that triggered this alert
     *
     * @return BelongsTo
     */
    public function execution(): BelongsTo
    {
        return $this->belongsTo(CommandScheduleExecution::class, 'execution_id');
    }

    /**
     * Get status badge HTML
     *
     * @return string
     */
    public function getStatusBadgeAttribute(): string
    {
        switch ($this->status) {
            case self::STATUS_NEW:
                return '<span class="badge bg-danger">New</span>';
            case self::STATUS_ACKNOWLEDGED:
                return '<span class="badge bg-warning">Acknowledged</span>';
            case self::STATUS_RESOLVED:
                return '<span class="badge bg-success">Resolved</span>';
            default:
                return '<span class="badge bg-secondary">Unknown</span>';
        }
    }

    /**
     * Get duration since alert was fired
     *
     * @return string
     */
    public function getDurationSinceFiredAttribute(): string
    {
        return $this->fired_at->diffForHumans();
    }

    /**
     * Acknowledge the alert
     *
     * @return bool
     */
    public function acknowledge(): bool
    {
        if ($this->status !== self::STATUS_NEW) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_ACKNOWLEDGED,
            'acknowledged_at' => Carbon::now(),
        ]);

        Log::info('JobSyncAlertEvent: Alert acknowledged', [
            'alert_event_id' => $this->id,
            'rule_id' => $this->rule_id,
            'acknowledged_at' => $this->acknowledged_at->toDateTimeString(),
        ]);

        return true;
    }

    /**
     * Resolve the alert
     *
     * @return bool
     */
    public function resolve(): bool
    {
        if ($this->status === self::STATUS_RESOLVED) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_RESOLVED,
            'resolved_at' => Carbon::now(),
        ]);

        Log::info('JobSyncAlertEvent: Alert resolved', [
            'alert_event_id' => $this->id,
            'rule_id' => $this->rule_id,
            'resolved_at' => $this->resolved_at->toDateTimeString(),
        ]);

        return true;
    }

    /**
     * Check if alert is new
     *
     * @return bool
     */
    public function isNew(): bool
    {
        return $this->status === self::STATUS_NEW;
    }

    /**
     * Check if alert is acknowledged
     *
     * @return bool
     */
    public function isAcknowledged(): bool
    {
        return $this->status === self::STATUS_ACKNOWLEDGED;
    }

    /**
     * Check if alert is resolved
     *
     * @return bool
     */
    public function isResolved(): bool
    {
        return $this->status === self::STATUS_RESOLVED;
    }

    /**
     * Scope for new alerts
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNew($query)
    {
        return $query->where('status', self::STATUS_NEW);
    }

    /**
     * Scope for acknowledged alerts
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAcknowledged($query)
    {
        return $query->where('status', self::STATUS_ACKNOWLEDGED);
    }

    /**
     * Scope for resolved alerts
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeResolved($query)
    {
        return $query->where('status', self::STATUS_RESOLVED);
    }

    /**
     * Scope for recent alerts within specified timeframe
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $hours
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $hours = 24)
    {
        $since = Carbon::now()->subHours($hours);
        return $query->where('fired_at', '>=', $since);
    }

    /**
     * Scope for specific alert rule
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $ruleId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForRule($query, int $ruleId)
    {
        return $query->where('rule_id', $ruleId);
    }

    /**
     * Create a new alert event
     *
     * @param JobSyncAlertRule $rule
     * @param array $alertData
     * @param CommandScheduleExecution|null $execution
     * @return static
     */
    public static function createAlert(JobSyncAlertRule $rule, array $alertData, ?CommandScheduleExecution $execution = null): self
    {
        $alertEvent = static::create([
            'rule_id' => $rule->id,
            'execution_id' => $execution?->id,
            'alert_data' => $alertData,
            'status' => self::STATUS_NEW,
            'fired_at' => Carbon::now(),
        ]);

        Log::warning('JobSyncAlertEvent: New alert created', [
            'alert_event_id' => $alertEvent->id,
            'rule_id' => $rule->id,
            'alert_type' => $rule->alert_type,
            'command' => $rule->command,
            'execution_id' => $execution?->id,
            'fired_at' => $alertEvent->fired_at->toDateTimeString(),
            'alert_data' => $alertData,
        ]);

        return $alertEvent;
    }

    /**
     * Get count of unresolved alerts
     *
     * @return int
     */
    public static function getUnresolvedCount(): int
    {
        return static::whereIn('status', [self::STATUS_NEW, self::STATUS_ACKNOWLEDGED])->count();
    }

    /**
     * Get recent alerts for dashboard display
     *
     * @param int $hours
     * @param int $limit
     * @return Collection
     */
    public static function getRecentAlerts(int $hours = 24, int $limit = 50): Collection
    {
        return static::recent($hours)
            ->with(['alertRule', 'execution'])
            ->orderBy('fired_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get alert summary for a specific command
     *
     * @param string $command
     * @param int $days
     * @return array
     */
    public static function getCommandAlertSummary(string $command, int $days = 7): array
    {
        $since = Carbon::now()->subDays($days);

        $alerts = static::whereHas('alertRule', function ($query) use ($command) {
            $query->where('command', $command);
        })
        ->where('fired_at', '>=', $since)
        ->get();

        $total = $alerts->count();
        $new = $alerts->where('status', self::STATUS_NEW)->count();
        $acknowledged = $alerts->where('status', self::STATUS_ACKNOWLEDGED)->count();
        $resolved = $alerts->where('status', self::STATUS_RESOLVED)->count();

        // Group by alert type
        $byType = $alerts->groupBy(function ($alert) {
            return $alert->alertRule->alert_type;
        })->map(function ($group) {
            return $group->count();
        })->toArray();

        return [
            'total' => $total,
            'new' => $new,
            'acknowledged' => $acknowledged,
            'resolved' => $resolved,
            'by_type' => $byType,
            'period_days' => $days,
            'since' => $since->toDateTimeString()
        ];
    }

    /**
     * Get provider name from alert rule
     *
     * @return string
     */
    public function getProviderName(): string
    {
        return $this->alertRule?->getProviderName() ?? 'Unknown';
    }

    /**
     * Get formatted alert data for display
     *
     * @return array
     */
    public function getFormattedAlertData(): array
    {
        $data = $this->alert_data ?? [];
        
        // Add common formatting
        if (isset($data['duration_seconds'])) {
            $data['duration_formatted'] = $this->formatDuration($data['duration_seconds']);
        }
        
        if (isset($data['jobs_fetched'])) {
            $data['jobs_fetched_formatted'] = number_format($data['jobs_fetched']);
        }
        
        if (isset($data['error_count'])) {
            $data['error_count_formatted'] = number_format($data['error_count']);
        }

        return $data;
    }

    /**
     * Format duration in seconds to human readable format
     *
     * @param int $seconds
     * @return string
     */
    private function formatDuration(int $seconds): string
    {
        if ($seconds < 60) {
            return "{$seconds}s";
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return "{$minutes}m {$remainingSeconds}s";
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            $remainingSeconds = $seconds % 60;
            return "{$hours}h {$minutes}m {$remainingSeconds}s";
        }
    }
} 
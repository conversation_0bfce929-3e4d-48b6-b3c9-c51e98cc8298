<?php

namespace Modules\ExaminationCertification\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateMarksGradeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {

        return [
            'program_id' => 'required',
            'grade_name' => 'required|max:50',
            'gpa' => 'required|max:4',
            'percent_from' => 'required|integer|min:0',
            'percent_upto' => 'required|integer',
            'grade_from' => 'required|max:4|min:0',
            'grade_upto' => 'required|max:4',
        ];
    }

    public function messages()
    {
        return [
            'grade_name.unique' => 'Duplicate name found!',
            'gpa.unique' => 'Duplicate GPA found!',
        ];
    }
}

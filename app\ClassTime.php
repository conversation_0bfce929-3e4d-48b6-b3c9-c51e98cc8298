<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

/**
 * App\ClassTime
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTime newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTime newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTime query()
 * @mixin \Eloquent
 */
class ClassTime extends Model
{


    protected $fillable = [
        'type',
        'period',
        'start_time',
        'end_time',
        'organization_id',
        'descriptions',
        'class_id',
        'class_time_title',
    ];

    protected $casts = [
        'start_time' => 'time:h:i A',
        'end_time' => 'time:h:i A',
    ];


    public function classes(){

        return $this->belongsTo(Classes::class);
    }


    public function getClassName(){

        return $this->classes()->first()->name;
    }


    public function getClassTimeTitle($value){

        return Str::title($value);

    }
    
}

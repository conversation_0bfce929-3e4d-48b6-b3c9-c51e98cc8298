<?php

namespace Modules\General\Http\Controllers;


use App\Admission;
use App\Center;
use App\CenterEmployee;
use App\Form;
use App\MissedClockOut;
use App\Role;
use App\Scopes\OrganizationScope;
use App\Student;
use Carbon\Carbon;
use Illuminate\Http\Request;

use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;


class IndividualEmployeeMissedClockoutsDatatablesController extends Controller
{

    public function __invoke(Request $request)
    {









        if ($request->ajax()) {

            if(!$request->filled('yearMonth'))

            {


                    $allMissedClockOutAttendance = MissedClockOut::withTrashed()->where('employee_id',$request->get('employee'))->with('attendance')->with('employee')->whereRaw('year(clock) = YEAR(CURRENT_DATE()) and month(clock) = MONTH(CURRENT_DATE())');



            }

            if($request->filled('yearMonth'))

            {
                $explodedYearMonth = explode('-', $request->get('yearMonth'));
                $year = $explodedYearMonth[0];
                $month = $explodedYearMonth[1];

                $allMissedClockOutAttendance = MissedClockOut::withTrashed()->where('employee_id',$request->get('employee'))->with('attendance')->with('employee')->whereYear("clock", $year)->whereMonth('clock', $month);



            }

            $allMissedClockOutAttendance->orderByRaw('date(clock) desc, employee_id');
            return DataTables::eloquent($allMissedClockOutAttendance->select())
                ->filter(function ($query) use ($request) {

                    if (request()->has('gender') && !empty(request()->gender)) {


                        $query->whereHas("employee", function ($q) use ($request) {
                            $q->where('employees.gender', $request->gender);
                        });

                    }

                    if (($request->filled('yearMonth'))) {

                        $explodedYearMonth = explode('-',$request->get('yearMonth'));
                        $year = $explodedYearMonth[0];
                        $month = $explodedYearMonth[1];

                        $query->whereYear("clock", $year)->whereMonth('clock',$month);
                    }
                },true)
                ->withQuery('count', function($row) {


                    $yesterday = date("Y-m-d", strtotime( '-1 days' ) );
                    $countYesterday = MissedClockOut::withTrashed()->with('attendance')->whereDate('clock', $yesterday )->get()->groupBy(['employee_id',date('clock')])->count();
                    return $countYesterday == 0 ? 20 : $countYesterday;

                })
                ->addColumn('full_name', function (MissedClockOut $row) {



                    $genderColor = $row->employee->gender == 'Male' || $row->employee->gender == 'Male (ذكر)' || $row->employee->gender == 'male' ? '#34b8bc;!important' : '#FA5661;!important';
                    $genderBasedDefaultImage = $row->employee->gender == 'Male' || $row->employee->gender == 'Male (ذكر)' || $row->employee->gender == 'male' ? asset('uploads/staff/demo/mstaff.jfif') : asset('uploads/staff/demo/fstaff.jpg');
                        if (file_exists($row->employee->image)) {

                            $image = asset($row->employee->image);
                        } elseif (Str::contains($row->employee->image, 'http')) {

                            $image = $row->employee->image;
                        } else {
                            $image = $genderBasedDefaultImage;


                        }


                    if (strlen($row->employee->full_name) > 22) {
                        $fullname = Str::limit(Str::title($row->employee->full_name),19,' ...');
                        return '<a target="_blank" href="'. route('employees.show', $row->employee_id).'" data-missedClockOut_id="' . $row->id . '" data-employee_id="' . $row->employee_id . '" data-tooltip="' . Str::title($row->employee->full_name) . '"   style="color:' . $genderColor . '" class="ui image label">
                          <img src="' . $image . '" "' . asset($row->employee->image) . '">
                          ' . ucwords(strtolower($fullname)) . '
                        </a>';

                    } else {
                        $fullname = Str::title($row->employee->full_name);

                        return '<a  target="_blank" href="'. route('employees.show', $row->employee_id).'" data-missedClockOut_id="' . $row->id . '" data-employee_id="' . $row->employee_id . '" style="color:' . $genderColor . '" class="ui image label">
                                          <img src="' . $image . '" "' . asset($row->employee->image) . '">
                                          ' . ucwords(strtolower($fullname)) . '
                                        </a>';

                    }




                    return '<span  data-missedClockOut_id="' . $row->id . '" data-employee_id="' . $row->employee_id . '">' . $row->employee->full_name . '</span>';
//


                })
                ->filterColumn('clock', function($query, $keyword) {
                    $sql = "date(clock)  like ?";
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->addColumn('clock', function ($row) {


                    $clock = Carbon::parse($row->clock);
                    return $clock->toDateString();

                })
                ->addColumn('login', function ($row) use ($request) {


                    $impersonationRoute = route('multiAuthImpersonate', ['id' => $row->employee_id, 'guardName' => 'employee']);
                    return '<span class="badge badge-primary badge-pill"><a
                                style="color: white; padding: 0px; background: transparent "
                                href="' . $impersonationRoute . '">login</a></span>';

                })
                ->filterColumn('in', function($query, $keyword) {
                    $sql = "time(clock)  like ?";
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->addColumn('in', function ($row) {
                    $html = '';
                    if ($row->type == 'in') {
                        $clock = Carbon::parse($row->clock);

                        $html .= '<span class="" style="color: rgb(22, 199, 132); font-weight: bolder">' . $clock->format('h:iA') . '</span>';

                        return $html;
                    }


                })
                ->filterColumn('out', function($query, $keyword) {
                    $sql = "time(clock)  like ?";
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->addColumn('out', function ($row) {

                    $clock = Carbon::parse($row->clock);

                    $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                        ->whereDate('clock', $clock->toDateString())->where('type', 'out');

                    if (optional($out)->exists()) {
                        $out = $out->first();
                        $html = ' <span class="" style="color: rgb(239, 97, 104); font-weight: bolder">' . optional(optional($out)->clock)->format('h:iA') . '</span>';

                        return $html;
                    } else {

                        // show the modal trigger button
                        return '<button class="ui green basic button" data-value="matt" data-toggle="modal" style="text-align-last: right;    font-size: 9px;
    color: white !important;
    font-weight: bolder;"
                                             id="addOutAttendanceRecordBtn"
                                             data-date="' . $clock->toDateString() . '"
                                             data-employee_id="' . $row->employee_id . '"
                                             data-in="' . $clock->format('g:i A') . '"
                                             data-target="#addMissedOutAttendanceRecord">
                                                 <i class="plus posit icon" style="color: rgb(22, 199, 132);"></i>
                                        </button>';

                    }


                })
                ->addColumn('numberofHours', function ($row) {
                    $clock = Carbon::parse($row->clock);

                    $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                        ->whereDate('clock', $clock->toDateString())->where('type', 'out')->first();
                    if ($row->type == 'in') {

                        $in = is_null($row->clock) ? \Carbon\Carbon::now() : $clock;


                    } else {

                        $in = \Carbon\Carbon::now();

                    }
                    if (optional($out)->exists()) {
                        $hours = $in->diffInHours($out->clock);
                        $seconds = $in->diffInMinutes($out->clock);
                        return $hours . ':' . $seconds % 60;
                    }

                })

                ->addColumn('clockoutReason', function ($row) {
                    $clock = Carbon::parse($row->clock);

                    $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                        ->whereDate('clock', $clock->toDateString())->where('type', 'out')->first();
                    return optional($out)->clockout_reason;
                })
                ->addColumn('action', function ($row) {
                    $clock = Carbon::parse($row->clock);

                    $clockDate = $clock->toDateString();


                    $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                        ->whereDate('clock', $clockDate)->where('type', 'out');

                    if ($out->exists()) {

                        $inDetails = \App\MissedClockOut::onlyTrashed()->where('employee_id', $row->employee_id)
                            ->whereDate('clock', $clockDate)->where('type', 'in')->first();

//                        $in = $in->first();



                        $out = $out->first();


                        $in = 0;
                        if($inDetails->type == 'in' ){
                            $in = $inDetails->id;

                        }
                        if($row->type == 'out' ){
                            $out = $row->id;

                        }




                        return '<div class="ui large buttons">
  <button class="ui button positive deleteModalTriggerBtn"
   id="deleteModalTriggerBtn "
                 
                     data-toggle="modal"
                   data-missed_clockin_id="' .$in. '"
//                     data-missed_clockin_id="' . optional($in)->id . '"
//                     data-missed_clockout_id="' . optional($out)->id . '"
                     data-missed_clockout_id="' . $out . '"
                     data-attendance_in_id="' . optional($row)->attendance_id . '"
                     data-employee_id="' . optional($row)->employee_id . '"
                     data-clock="' . optional($out)->clock . '"
                     data-clockout_reason="' . optional($out)->clockout_reason . '"
                      data-target="#validateMissedClockoutEntry">Validate</button>
  <div class="or"></div>
  <button class="ui button deleteModalTriggerBtn"
 id="deleteModalTriggerBtn "
                    
                     data-toggle="modal"
                     data-missed_clockin_id="' . $in. '"
//                     data-missed_clockout_id="' . optional($out)->id . '"
                     data-missed_clockout_id="' .$out. '"
                     data-attendance_in_id="' . optional($row)->attendance_id . '"
                     data-employee_id="' . optional($row)->employee_id . '"
                     data-clock="' . optional($out)->clock . '"
                     data-clockout_reason="' . optional($out)->clockout_reason . '"
                      data-target="#revertMissedClockoutEntry">Revert</button>
</div>



';
                    }


                })->rawColumns(['action', 'in', 'out','full_name','login'])
                ->toJson();

        }


    }

}









var UIBootbox=function(){var o=function(){$("#demo_1").click(function(){bootbox.alert("Hello world!")}),$("#demo_2").click(function(){bootbox.alert("Hello world!",function(){alert("Hello world callback")})}),$("#demo_3").click(function(){bootbox.confirm("Are you sure?",function(o){alert("Confirm result: "+o)})}),$("#demo_4").click(function(){bootbox.prompt("What is your name?",function(o){null===o?alert("Prompt dismissed"):alert("Hi <b>"+o+"</b>")})}),$("#demo_5").click(function(){bootbox.dialog({message:"I am a custom dialog",title:"Custom title",buttons:{success:{label:"Success!",className:"green",callback:function(){alert("great success")}},danger:{label:"Danger!",className:"red",callback:function(){alert("uh oh, look out!")}},main:{label:"Click ME!",className:"blue",callback:function(){alert("Primary button")}}}})})};return{init:function(){o()}}}();jQuery(document).ready(function(){UIBootbox.init()});
{{--
 * Created by PhpStorm.
 * User: it4om
 * Date: 15/02/2017
 * Time: 08:06
--}}

@extends('home.layouts.home')
@section('page_title') <?php echo $master_post->{'post_title_' . App::getLocale()} ?>   @endsection

@section('content')

{{-- Get current article link to share in the social media  --}}
<?php
$current_link = 'https://'.$_SERVER['HTTP_HOST'].$_SERVER['PHP_SELF'];
?>

    <section class="page-header dark page-header-xs">
        <div class="container">
            <h1>{{trans('home_content.blog')}}</h1>
        </div>
    </section>




<div class="flexslider">
    <ul class="slides">

    </ul>
</div>

    <!-- REVOLUTION SLIDER -->
    <div class="flexslider">
        <ul class="slides">
                <?php /*counter */$count_displayed_pics = 0; ?>
                @foreach($master_post_attachments as $att)
                    <?php /*get file extention */$ext = pathinfo($att->post_attachment, PATHINFO_EXTENSION);?>
                    @if($ext=="jpeg" || $ext=="jpg" || $ext=="png" || $ext=="gif")
                    <?php $count_displayed_pics++; ?>
                        <li>
                            <a>
                                <img style="height: 729px;max-height: 729px;min-height: 729px;"
                                     src="{{URL::to($att->post_attachment)}}"
                                     alt="<?php echo $master_post->{'post_title_' . App::getLocale()} ?>">
                                <div class="flex-caption"><?php echo $master_post->{'post_title_' . App::getLocale()} ?></div>
                            </a>
                        </li>
                    @endif
                @endforeach
                @if($count_displayed_pics==0)
                    <li>
                        <a>
                            <img style="height: 241px;max-height: 241px;min-height: 241px;"
                                 src="{{URL::to('home_style/images/default_blog_pic/'.random_int(1,3).'.jpg')}}"
                                 alt="<?php echo $master_post->{'post_title_' . App::getLocale()} ?>">
                            <div class="flex-caption"><?php echo $master_post->{'post_title_' . App::getLocale()} ?></div>
                        </a>
                    </li>
                @endif
        </ul>
    </div>
    <!-- /REVOLUTION SLIDER -->






    <!-- -->
    <section>
        <div class="container">

            <div class="row">

                <!-- LEFT -->
                <div class="col-md-9 col-sm-9">

                    <h1 class=""><?php echo $master_post->{'post_title_' . App::getLocale()} ?></h1>
                    <h5 class=""><?php echo $master_post->{'post_subtitle_' . App::getLocale()} ?></h5>
                    <ul class="blog-post-info list-inline">
                        <li>
                            <a>
                                <i class="fa fa-clock-o"></i>
                                <span class="font-lato">
                                    {{getArabicDate($master_post->created_at)}}
                                </span>
                            </a>
                        </li>
                        <li>
                            <a>
                                <i class="fa fa-comment-o"></i>
                                <span class="font-lato"> ({{count($master_post_comments)}}){{trans('home_content.comments')}}</span>
                            </a>
                        </li>
                        <li>
                            <a>
                                <i class="fa fa-eye"></i>
                                <span class="font-lato"> ({{$master_post->post_views_count}}){{trans('home_content.visits')}}</span>
                            </a>
                        </li>
                    </ul>


                    <!-- article content -->
                    <p class="">
                        <?php echo htmlspecialchars_decode($master_post->{'post_content_' . App::getLocale()}); ?>
                    </p>

                    <!-- /article content -->


                    <div class="divider divider-dotted"><!-- divider --></div>

                    <fieldset class="">
                        <legend>{{trans('home_content.attachments')}}</legend>

                            <?php /*counter */$count_displayed_pics = 0; ?>

                            @foreach($master_post_attachments as $att)
                                <?php /*get file extention */$ext = pathinfo($att->post_attachment, PATHINFO_EXTENSION);?>
                                @if($ext!="jpeg" && $ext!="jpg" && $ext!="png" && $ext!="gif")
                                    <?php $count_displayed_pics++; ?>
                                    <div class="col-md-2">
                                        <a href="{{URL::to($att->post_attachment)}}" target="_blank">
                                            <i class="fa fa-file fa-3x"></i>
                                            <p>{{getFullFilenameFromPath($att->post_attachment)}}</p>
                                        </a>
                                    </div>
                                @endif
                            @endforeach

                            @if($count_displayed_pics==0)
                                <span style="padding: 1px;" class="alert alert-danger">
                                   {{trans('home_content.no_file_existe')}}
                                </span>
                            @endif
                    </fieldset>

                    <div class="divider divider-dotted"><!-- divider --></div>



                    <!-- SHARE POST -->
                    <div class="clearfix margin-top-30">


                        <div class="col-md-6 text-center">
                            <div style="margin-top:10px ;">{{trans('home_content.share_post')}}</div>
                        </div>

                        <div class="col-md-6 center-block">

                            <a onclick="window.open(this.href, 'Facebook Share','left=20,top=20,width=500,height=500,toolbar=1,resizable=0'); return false;"
                               href="https://www.facebook.com/sharer.php?u={{$current_link}}"
                               class="social-icon social-icon-sm social-icon-transparent social-facebook"
                               data-toggle="tooltip" data-placement="top" title="Facebook" target="_blank">
                                <i class="icon-facebook"></i>
                                <i class="icon-facebook"></i>
                            </a>

                            <a onclick="window.open(this.href, 'Twitter Share','left=20,top=20,width=500,height=500,toolbar=1,resizable=0'); return false;"
                               href="https://twitter.com/share?url={{$current_link}}&amp;text=//&amp;hashtags={{str_replace(' ','_',cache('title_ar'))}}"
                               class="social-icon social-icon-sm social-icon-transparent social-twitter"
                               data-toggle="tooltip" data-placement="top" title="Twitter">
                                <i class="icon-twitter"></i>
                                <i class="icon-twitter"></i>
                            </a>


                            <a onclick="window.open(this.href, 'Linkedin Share','left=20,top=20,width=500,height=500,toolbar=1,resizable=0'); return false;"
                               href="https://www.linkedin.com/shareArticle?mini=true&amp;url={{$current_link}}"
                               class="social-icon social-icon-sm social-icon-transparent social-linkedin"
                               data-toggle="tooltip" data-placement="top" title="Linkedin">
                                <i class="icon-linkedin"></i>
                                <i class="icon-linkedin"></i>
                            </a>

                            <a onclick="window.open(this.href, 'Email Share','left=20,top=20,width=500,height=500,toolbar=1,resizable=0'); return false;"
                               href="https://plus.google.com/share?url={{$current_link}}" class="social-icon social-icon-sm social-icon-transparent social-gplus"
                               data-toggle="tooltip" data-placement="top" title="Email">
                                <i class="icon-gplus"></i>
                                <i class="icon-gplus"></i>
                            </a>

                        </div>


                    </div>
                    <!-- /SHARE POST -->


                    <div class="divider"><!-- divider --></div>

                    <!-- COMMENTS -->
                    <div id="comments" class="comments">

                        <h4 class="page-header margin-bottom-60 size-20">
                            <span>{{count($master_post_comments)}}</span> {{trans('home_content.comments')}}
                        </h4>

                        <!-- comment item -->

                        <div class="mCustomScrollbar" id="comments_filed" data-mcs-theme="dark">
                            @foreach($master_post_comments as $master_post_comment)
                                <div class="comment-item">
                                    <!-- user-avatar -->
                                    <span class="user-avatar">
								<img class="pull-left media-object" src="{{URL::to('home_style/images/sys_user/default_avatar.png')}}" width="64" height="64" alt="">
							</span>

                                    <div class="media-body">
                                        <h4 class="media-heading bold">{{$master_post_comment->comment_full_name}}</h4>
                                        <small class="block">{{getArabicDate($master_post_comment->created_at)}}</small>
                                        {{$master_post_comment->comment_text}}
                                    </div>
                                </div>
                            @endforeach
                        </div>




                        <h4 class="page-header size-20 margin-bottom-60 margin-top-100">
                            {{trans('home_content.leavcomment')}}
                        </h4>

                        <!-- Form -->
                        <form action="{{url('home/blog/leave_comment')}}" method="post">
                            {{ csrf_field() }}
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-md-4">
                                        <label>{{trans('home_content.fullname')}}</label>
                                        <input required="required" type="text" value="" maxlength="100" class="form-control input-lg" name="author" id="author">
                                        <input required="required" value="{{$master_post->id}}" type="hidden" name="post_id" id="post_id">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="form-group">
                                    <div class="col-md-12">
                                        <label>{{trans('home_content.comment')}}</label>
                                        <textarea required="required" maxlength="5000" rows="5" class="form-control" name="comment" id="comment"></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">

                                    <button class="btn btn-3d btn-lg btn-reveal btn-black">
                                        <i class="fa fa-check"></i>
                                        <span>{{trans('home_content.send')}}</span>
                                    </button>

                                </div>
                            </div>

                        </form>
                        <!-- /Form -->

                    </div>
                    <!-- /COMMENTS -->


                </div>
                <div class="col-md-3 col-sm-3" style="border-right: 1px dashed #DBDCDB;">
                    @include('home.blog.partial.top')
                </div>
            </div>


        </div>
    </section>
    <!-- / -->
<script>

</script>
@endsection


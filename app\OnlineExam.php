<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class OnlineExam extends Model
{

    protected $fillable = [
        'title',
        'class_id',
        'date',
        'start_time',
        'end_time',
        'end_date_time',
        'percentage',
        'details',

    ];

    public function scopeWhereDateBetween($query, $column, $start_date, $end_date)
    {


        return $query->whereBetween($column, [
            Carbon::parse($start_date),
            Carbon::parse($end_date)
        ]);
    }


    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);


    }
    public function examiner()
    {
        return $this->belongsTo(Employee::class,'examiner_id','id');
    }
    public function studentInfo(){
    	return $this->belongsTo('App\Student', 'student_id', 'id');
    }

    public function class(){
    	return $this->belongsTo('App\Classes', 'class_id', 'id');
    }

    public function center(){
        return $this->belongsTo('App\Center', 'center_id', 'id');
    }


    public function program(){
        return $this->belongsTo('App\Program', 'program_id', 'id');
    }

    public function marksGrades(){
        return $this->belongsTo(MarksGrade::class, 'marks_grades_id', 'id');
    }

    public function assignQuestions(){
        return $this->hasMany('App\OnlineExamQuestionAssign', 'online_exam_id', 'id');
    }

    public static function obtainedMarks($exam_id, $student_id){
    	
        try {
            $marks = StudentTakeOnlineExam::select('status', 'total_marks')->where('online_exam_id', $exam_id)->where('student_id', $student_id)->first();
                return $marks;
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    }

}

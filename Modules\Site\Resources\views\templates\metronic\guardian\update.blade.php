@extends(theme_path('guardian.layout.guardian'))

@section('side_content')




<div class="box-icon box-icon-center box-icon-round box-icon-transparent box-icon-large box-icon-content padding-10 margin-top-0    ">
<h2>Update Profile</h2>

<div class="tab-content margin-top-20">

<div class="tab-pane fade in active" id="info">
@if ($errors->any())
            <ul class="alert alert-danger">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        @endif
        <form id="myform" method="post" action="{{ route('guardian.update', Auth::user()->id) }}" >
  {{ csrf_field() }}


        {!! Form::hidden('update_guardian_profile', 10) !!}
        {!! Form::hidden('guardian_id', Auth::user()->id) !!}
        @if(config('settings.guardian_form_full_name') )
        <div class="form-group">
            {!! Form::label('full_name', trans('common.guardian_full_name') , ['class' => 'control-lable']) !!}
            {!! Form::text('full_name', Auth::user()->full_name ?? '', ['class' => 'form-control']) !!}
            @if ($errors->has('full_name'))
                <div class="alert alert-danger error">
                    <strong>{{ $errors->first('full_name') }}</strong>
                </div>
            @endif
        </div>
    @endif
    @if(config('settings.guardian_form_full_name_trans') )
        <div class="form-group">
            {!! Form::label('full_name_trans', trans('common.full_name') .' ['. config('settings.guardian_form_full_name_language') .']', ['class' => 'control-lable']) !!}
            {!! Form::text('full_name_trans', Auth::user()->full_name_trans ?? '', ['class' => 'form-control']) !!}
            @if ($errors->has('full_name_trans'))
                <div class="alert alert-danger error">
                    <strong>{{ $errors->first('full_name_trans') }}</strong>
                </div>
            @endif
        </div>
    @endif

   

    @if(config('settings.guardian_form_gender') )
        <div class="form-group">            
            {!! Form::label('gender', trans('common.gender') , ['class' => 'control-lable']) !!}
            {!! Form::select('gender', ['male' => trans('common.male'),'female' => trans('common.female') ], $guardian->gender ?? '', ['class' => 'form-control']) !!}
            @if ($errors->has('gender'))
                <div class="alert alert-danger error">
                    <strong>{{ $errors->first('gender') }}</strong>
                </div>
            @endif
        </div>
    @endif
    
    @if(config('settings.guardian_form_date_of_birth') )
        <div class="form-group">            
            {!! Form::label('date_of_birth', trans('common.date_of_birth') , ['class' => 'control-lable']) !!}            
            {!! Form::text('date_of_birth', Auth::user()->date_of_birth ?? '', ['class' => 'form-control']) !!}
            @if ($errors->has('date_of_birth'))
                <div class="alert alert-danger error">
                    <strong>{{ $errors->first('date_of_birth') }}</strong>
                </div>
            @endif
        </div>
    @endif

    @if(config('settings.guardian_form_nationality') )
        <div class="form-group">
            {!! Form::label('nationality', trans('common.nationality') .' ['. config('settings.guardian_form_full_name_language') .']', ['class' => 'control-lable']) !!}
            {!! Form::text('nationality', Auth::user()->nationality ?? '', ['class' => 'form-control']) !!}
            @if ($errors->has('nationality'))
                <div class="alert alert-danger error">
                    <strong>{{ $errors->first('nationality') }}</strong>
                </div>
            @endif
        </div>
    @endif

    @if(config('settings.guardian_form_occupation') )
        <div class="form-group">            
            {!! Form::label('occupation', trans('common.occupation') , ['class' => 'control-lable']) !!}            
            {!! Form::text('occupation',Auth::user()->occupation ?? '', ['class' => 'form-control']) !!}
            @if ($errors->has('occupation'))
                <div class="alert alert-danger error">
                    <strong>{{ $errors->first('occupation') }}</strong>
                </div>
            @endif
        </div>
    @endif
   
    @if(config('settings.guardian_form_mobile') )
        <div class="form-group">            
            {!! Form::label('mobile', trans('common.mobile') , ['class' => 'control-lable']) !!}            
            {!! Form::text('mobile', Auth::user()->mobile ?? '', ['class' => 'form-control']) !!}
            @if ($errors->has('mobile'))
                <div class="alert alert-danger error">
                    <strong>{{ $errors->first('mobile') }}</strong>
                </div>
            @endif

        </div>
    @endif
    <div class="text-center">
    {!! Form::submit(trans('common.save'), ['class' => 'btn btn-danger bg-maroon btn-flat margin']) !!}
    </div>
            
    
    

      </form>
</div>



</div>
</div>




 @endsection
         
@include('jssnippets.select2')
@include('jssnippets.flatpickr')

@section('js')
<script>
    flatpickr('#date_of_birth', {
        maxDate: '{{date('Y')-19}}-12-31'
    });
</script>
@append










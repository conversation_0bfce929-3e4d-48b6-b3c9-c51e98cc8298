# Prompt for Refactoring Job Notification Setup Logic

## Background

The current implementation in `Modules/JobSeeker/Http/Controllers/JobsController.php` contains all the job notification setup logic, including methods like `storeNotificationSetup` (line 610) and others handling notification creation, updates, and deletion. Additionally, the `JobNotificationSetup` model in `Modules/JobSeeker/Entities/JobNotificationSetup.php` (line 62) currently uses the `categories` relationship to store canonical categories directly.

## Objectives

1. **Controller Refactor**
   - **Move Job Notification Logic**: Transfer all job notification setup-related logic into its own dedicated controller (e.g., `JobNotificationController`).
   - **Preserve Route URLs**: Ensure that the route URLs remain unchanged, but update the controller and method references to point to the new controller.

2. **Category Mapping Process**
   - **Input Processing**: The input request delivers a canonical category (e.g., "technology").
   - **Mapping Service**: Implement a central service (e.g., `CategoryMappingService`) that maps the canonical category to the corresponding provider job categories. This service should return the IDs of the provider job categories associated with the given canonical category.
   - **Relationship Update**: Modify the `JobNotificationSetup` model to use the `providerCategories` relationship instead of the `categories` (canonical category) relationship. This ensures that job notifications are sent based on provider job category IDs rather than canonical categories.

3. **Maintainability and Extensibility**
   - **Centralization**: Centralize the mapping logic in a service so that other parts of the system can reuse this capability.
   - **Suggested Improvements**: Consider using methods like `syncWithoutDetaching` if non-destructive updates are needed. Also, if extra pivot attributes are necessary, ensure the mapping service and associated model logic accommodate these attributes.

## Framework Reference

This prompt has been structured based on the guidelines in the `claude_prompt_improver_template.md` file. Ensure that all needed references (file names, method names, and line numbers where applicable) are included for clarity.

## Detailed Steps and Requirements

1. **Extract Notification Logic**
   - Identify and extract methods such as `storeNotificationSetup`, `updateNotificationSetup`, and `deleteNotificationSetup` from `JobsController.php` (e.g., line 610).
   - Create a new controller (e.g., `JobNotificationController`) and refactor these methods while preserving the existing route URLs.

2. **Implement Central Mapping Service**
   - Develop a service (e.g., `CategoryMappingService`) that takes a canonical category as input and returns the associated provider job category IDs.
   - In the refactored controller, replace the direct usage of `JobCategory` with a call to this mapping service.
   - Update the `JobNotificationSetup` model to use the `providerCategories` relationship (line 62) rather than the `categories` relationship.

3. **Transaction and Data Integrity**
   - Ensure that all database operations remain within a transaction block to maintain data integrity during the refactor.

4. **Include Suggestions for Improvement**
   - Evaluate the possibility of using non-destructive sync methods (e.g., `syncWithoutDetaching`), and consider improved error handling.
   - Consider validating that the mapping service returns valid provider category IDs and that the pivot table is updated correctly.

## Final Instructions for Developer

Using this prompt, refactor the job notification setup logic by performing the controller extraction and updating the category mapping process as described above. Reference the original implementation in `JobsController.php` (e.g., around line 610 for `storeNotificationSetup`) and ensure that all changes are documented for future maintainability and ease of extension. Additionally, update the `JobNotificationSetup` model (e.g., line 62) to reflect the new relationship with provider categories.

**Additional Suggestions**:
- Validate the mapping service output rigorously.
- Use appropriate syncing methods for relationships if partial updates are required.
- Consider future system integrations that might use the centralized mapping service.

---

*End of Prompt*

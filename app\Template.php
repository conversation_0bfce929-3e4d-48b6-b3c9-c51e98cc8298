<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Modules\ApplicationCenter\Entities\StudentRegistration;
use Auth;

class Template extends Model
{
	protected $table = "sms_templates";

	public static function getValueByStringTest($data, $str)
	{


		if ($str == 'password') {
            return $data["password"];
//			return '123456';
		} elseif ($str == 'organization_name') {

//			if (Setting::isModule('Saas') == TRUE) {
//
//				$student_info = StudentRegistration::find(@$data['id']);
//
//				return @$student_info->school->company_name;
//			} else {
				$general_setting = GeneralSettings::find(1);
				return @$general_setting->organization_name;
//			}
		}



		if ($data['slug'] == 'student') {

			$student_info = Student::find(@$data['id']);


            if ($str == 'program_name') {

                return @$data['programTitle'];
            }

            elseif ($str == 'interview_location') {

                return @$data['interviewLocation'];
            }

            elseif ($str == 'interviewer_names') {

                return  implode(", ",Employee::whereIn("id",$data['interviewCommitteeIds'])->pluck("email")->toArray());

            }
            elseif ($str == 'interview_duration') {

                return @$data['interview_duration'];
            }
            elseif ($str == 'interview_date_time') {

                return @$data['interviewDateTime'];
            }
            elseif ($str == 'invitation_sender_name') {

                return Auth::user()->full_name;
            }
            elseif ($str == 'contact_information') {

                return Auth::user()->email;
            }
            elseif ($str == 'student_name') {

				return @$student_info->full_name;
			} elseif ($str == 'father_name') {

				$parent_info = Guardian::find(@$student_info->guardian_id);

				return @$parent_info->fathers_name;
			} elseif ($str == 'username') {

				$user_info = User::find(@$student_info->user_id);

//				return @$user_info->username . '/' . @$user_info->email;
				return @$user_info->email;
			}
		} elseif ($data['slug'] == 'parent') {
			$parent_info = Student::find(@$data['id']);

			if ($str == 'name') {
				return @$parent_info->fathers_name;
			} elseif ($str == 'student_name') {
				$student_info = Student::where('guardian_id', $parent_info->id)->first();
				return @$student_info->full_name;
			} elseif ($str == 'username') {

				$user_info = User::find(@$parent_info->user_id);

				return @$user_info->username;
			}
		} else {

			$employee_info = Employee::find(@$data['id']);

			$user_info = User::find(@$employee_info->user_id);

			if ($str == 'name') {

				return @$user_info->full_name;
			} elseif ($str == 'username') {

				return @$user_info->username;
			}
		}
	}

	public static function getValueByStringTestReset($data, $str)
	{

		if ($str == 'organization_name') {

			$general_setting = Setting::find(1);
			return @$general_setting->organization_name;
		} elseif ($str == 'name') {
//			$user = User::where('email', $data['email'])->first();
			$user = User::where('username', $data['username'])->first();
			return @$user->full_name;
		}
	}

	public static function getValueByStringTestRegistration($data, $str)
	{

		if ($str == 'password') {

			return '123456';
		} elseif ($str == 'organization_name') {

			if (Setting::isModule('Saas') == TRUE) {

				$student_info = StudentRegistration::find(@$data['id']);

				return @$student_info->school->organization_name;
			} else {
				$general_setting = GeneralSettings::find(1);
				return @$general_setting->organization_name;
			}
		}



		if ($data['slug'] == 'student') {

			$student_info = StudentRegistration::find(@$data['id']);

			if ($str == 'name') {

				return @$student_info->first_name . ' ' . @$student_info->last_name;
			} elseif ($str == 'guardian_name') {


				return @$parent_info->guardian_name;
			} elseif ($str == 'class') {


				return @$student_info->class->class_name;
			} elseif ($str == 'section') {


				return @$student_info->section->section_name;
			}
		} elseif ($data['slug'] == 'parent') {

			$parent_info = StudentRegistration::find(@$data['id']);

			if ($str == 'name') {
				return @$parent_info->guardian_name;
			} elseif ($str == 'student_name') {

				return @$student_info->first_name . ' ' . @$student_info->last_name;
			}
		}
	}

	public static function getValueByStringTestApprove($data, $str)
	{

		if ($str == 'password') {

			return '123456';
		} elseif ($str == 'organization_name') {

			if (Setting::isModule('Saas') == TRUE) {

				if ($data['slug'] == 'student') {
					$student_info = Student::find(@$data['id']);

					return @$student_info->school->organization_name;
				} else {
					$student_info = Guardian::find(@$data['id']);

					return @$student_info->school->organization_name;
				}
			} else {

				$general_setting = GeneralSettings::find(1);
				return @$general_setting->organization_name;
			}
		}



		if ($data['slug'] == 'student') {

			$student_info = Student::find(@$data['id']);

			if ($str == 'student_name') {

				return @$student_info->full_name;
			} elseif ($str == 'father_name') {
				$parent_info = Guardian::find(@$student_info->guardian_id);

				return @$parent_info->guardians_name;
			} elseif ($str == 'username') {

				$user_info = User::find(@$student_info->user_id);

				return @$user_info->username . '/' . @$user_info->email;
			}
		} elseif ($data['slug'] == 'parent') {
			$parent_info = Guardian::find(@$data['id']);

			if ($str == 'name') {
				return @$parent_info->guardians_name;
			} elseif ($str == 'student_name') {
				$student_info = Student::where('guardian_id', $parent_info->id)->first();
				return @$student_info->full_name;
			} elseif ($str == 'username') {

				$user_info = User::find(@$parent_info->guardians_email);

				return @$user_info->username;
			}
		}
	}

	public static function getValueByStringDuesFees($student_detail, $str, $fees_info){

		if($str == 'student_name'){

			return @$student_detail->full_name;

		}elseif($str == 'parent_name'){

			$parent_info = Guardian::find($student_detail->guardian_id);
			return @$parent_info->fathers_name;

		}elseif($str == 'due_amount'){

			return @$fees_info['dues_fees'];
			
		}elseif($str == 'due_date'){

			$fees_master = FeesMaster::find($fees_info['fees_master']);
			

			return @$fees_master->date;
			
		}elseif($str == 'organization_name'){

			return @Auth::user()->school->organization_name;

		}elseif($str == 'fees_name'){

			$fees_master = FeesMaster::find($fees_info['fees_master']);

			return $fees_master->feesTypes->name;
		}
	}

	public static function getValueByStringAdmissionSms($s_id, $str){

		$student = Student::find($s_id);
		$student_user = User::find(@$student->user_id);

		$parent = Guardian::find(@$student->guardian_id);
		$parent_user = User::find(@$parent->user_id);


		if($str == 'student_name'){

			return @$student->full_name;

		}elseif($str == 'student_username'){

			return @$student_user->username;

		}elseif($str == 'student_email'){

			return @$student_user->email;
			
		}elseif($str == 'student_password'){

			return '123456';
			
		}elseif($str == 'guardian_name'){

			return @$parent->guardians_name;
			
		}elseif($str == 'guardian_email'){

			return @$parent->guardians_email;
			
		}elseif($str == 'guardian_password'){

			return '123456';
			
		}elseif($str == 'admission_number'){

			return @$student->student_number;
			
		}elseif($str == 'organization_name'){
			return @$student->school->organization_name;
		}
	}
}

<?php

namespace App;

use App\Notifications\EmployeeResetPassword;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;

use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\SoftDeletes;

class CenterTeacher extends Pivot
{

    protected $table = 'cen_teachers';
    public $incrementing = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */





    public function hefzPlan(){
        return $this->hasMany(StudentHefzPlan::class,'center_id','cen_id');
    }










}

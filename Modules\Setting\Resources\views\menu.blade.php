@if(permissionCheck('setting.index'))
    @php
        $setting = false;
        if(request()->is('setting/*') or request()->is('setup/*') or request()->is('localization'))
        {
            $setting = true;
        }
    @endphp

    <li class="nav-item nav-group {{ $setting ? 'show' : '' }}">
        <button class="nav-link nav-group-toggle" 
                type="button"
                data-bs-toggle="collapse" 
                data-bs-target="#settingGroup"
                aria-expanded="{{ $setting ? 'true' : 'false' }}"
                aria-controls="settingGroup">
            <i class="nav-icon fas fa-store"></i>
            <span class="nav-link-text">{{ __('setting.System Settings') }}</span>
            <i class="nav-group-arrow fas fa-chevron-right ms-auto"></i>
        </button>
        <div class="collapse nav-group-items {{ $setting ? 'show' : '' }}" id="settingGroup">
            <ul class="nav flex-column">
                @if(permissionCheck('setting.index'))
                    <li class="nav-item">
                        <a href="{{url('setting')}}" class="nav-link">
                            <span class="nav-link-text">{{ __('setting.Settings') }}</span>
                        </a>
                    </li>
                @endif
                @if(permissionCheck('pdf_fonts.index'))
                    <li class="nav-item">
                        <a href="{{route('pdf_fonts.index')}}" class="nav-link">
                            <span class="nav-link-text">{{ __('setting.Pdf Fonts') }}</span>
                        </a>
                    </li>
                @endif
                @if(permissionCheck('payment-method-settings'))
                    <li class="nav-item">
                        <a href="{{route('payment-method-settings')}}" class="nav-link">
                            <span class="nav-link-text">{{__('setting.Payment Method Setting')}}</span>
                        </a>
                    </li>
                @endif

                @includeIf('setup::menu', ['include_from' => 'setting'])
                @if(permissionCheck('modulemanager.index'))
                    <li class="nav-item">
                        <a href="{{ route('modulemanager.index') }}" class="nav-link {{ spn_active_link('modulemanager.index', 'active') }}">
                            <span class="nav-link-text">{{ __('common.Module Manager') }}</span>
                        </a>
                    </li>
                @endif

                @if(permissionCheck('setting.updatesystem'))
                    <li class="nav-item">
                        <a href="{{ route('setting.updatesystem') }}" class="nav-link {{ spn_active_link('setting.updatesystem', 'active') }}">
                            <span class="nav-link-text">{{ __('setting.Update') }}</span>
                        </a>
                    </li>
                @endif
            </ul>
        </div>
    </li>
@endif

@if(permissionCheck('style.index'))
    @php
        $style = false;
        if(request()->is('style/*'))
        {
            $style = true;
        }
    @endphp

    <li class="nav-item nav-group {{ $style ? 'show' : '' }}">
        <button class="nav-link nav-group-toggle" 
                type="button"
                data-bs-toggle="collapse" 
                data-bs-target="#styleGroup"
                aria-expanded="{{ $style ? 'true' : 'false' }}"
                aria-controls="styleGroup">
            <i class="nav-icon fas fa-palette"></i>
            <span class="nav-link-text">{{ __('setting.Styles') }}</span>
            <i class="nav-group-arrow fas fa-chevron-right ms-auto"></i>
        </button>
        <div class="collapse nav-group-items {{ $style ? 'show' : '' }}" id="styleGroup">
            <ul class="nav flex-column">
                @if(permissionCheck('guest-background'))
                    <li class="nav-item">
                        <a href="{{ route('guest-background') }}" class="nav-link {{ spn_active_link('guest-background', 'active') }}">
                            <span class="nav-link-text">{{ __('setting.Background') }}</span>
                        </a>
                    </li>
                @endif
                @if(permissionCheck('themes.index'))
                    <li class="nav-item">
                        <a href="{{ route('themes.index') }}" class="nav-link {{ spn_active_link('themes.index', 'active') }}">
                            <span class="nav-link-text">{{ __('setting.Theme Customization') }}</span>
                        </a>
                    </li>
                @endif
                @if(permissionCheck('themes.change_view'))
                    <li class="nav-item">
                        <a href="{{ route('themes.change_view') }}" class="nav-link {{ spn_active_link('themes.change_view', 'active') }}">
                            <span class="nav-link-text">{{ __('common.Change View') }}</span>
                        </a>
                    </li>
                @endif
            </ul>
        </div>
    </li>
@endif
@if(permissionCheck('utilities'))
    <li class="nav-item">
        <a href="{{ route('utilities') }}" class="nav-link {{ request()->is('utilities') ? 'active' : '' }}">
            <i class="nav-icon fas fa-tools"></i>
            <span class="nav-link-text">{{ __('setting.Utilities') }}</span>
        </a>
    </li>
@endif

@extends('layouts.hound')

@section('content')

<div class="panel panel-default card-view">
    <div class="panel-heading">
        <h4>Edit program [{{ $program->code }}]
           <a href="{{ url('/workplace/education/programs') }}" title="Back" class="pull-right"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
        </h4>
    </div>
    <div class="panel-body">
        @if ($errors->any())
            <ul class="alert alert-danger">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        @endif

        {!! Form::model($program, [
            'method' => 'PATCH',
            'url' => ['/workplace/education/programs', $program->id],
            'class' => 'form-horizontal',
            'files' => true
        ]) !!}

        @include ('examinationcertification::programs.form', ['submitButtonText' => 'Update'])

        {!! Form::close() !!}

    </div>
</div>
@endsection

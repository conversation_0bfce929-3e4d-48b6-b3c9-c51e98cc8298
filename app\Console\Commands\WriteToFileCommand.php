<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class WriteToFileCommand extends Command
{
    protected $signature = 'file:write';
    protected $description = 'Write timestamp to abc.md file every second';

    public function handle()
    {
        try {
            $content = "123 " . Carbon::now()->format('Y-m-d H:i:s') . "\n";
            file_put_contents(storage_path('abc.md'), $content, FILE_APPEND);
            $this->info('Written to file: ' . $content);
            
            // Log success
            \Log::info("Successfully wrote to abc.md at " . now());
        } catch (\Exception $e) {
            // Log failure
            $this->error('Failed to write to file: ' . $e->getMessage());
            \Log::error("Failed to write to abc.md: " . $e->getMessage());
        }
    }
} 
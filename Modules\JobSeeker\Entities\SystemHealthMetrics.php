<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

/**
 * System Health Metrics Entity
 * 
 * Stores system health metrics over time for monitoring and trend analysis
 */
final class SystemHealthMetrics extends Model
{
    use HasFactory;

    protected $table = 'system_health_metrics';

    protected $fillable = [
        'metric_type',
        'metric_name',
        'value',
        'status',
        'threshold',
        'metadata',
        'recorded_at',
    ];

    protected $casts = [
        'value' => 'float',
        'threshold' => 'float',
        'metadata' => 'array',
        'recorded_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Metric types
     */
    public const TYPE_EMAIL_NOTIFICATIONS = 'email_notifications';
    public const TYPE_JOB_FETCHING = 'job_fetching';
    public const TYPE_API_PERFORMANCE = 'api_performance';
    public const TYPE_DATABASE_HEALTH = 'database_health';
    public const TYPE_QUEUE_HEALTH = 'queue_health';
    public const TYPE_SYSTEM_RESOURCES = 'system_resources';
    public const TYPE_ERROR_RATES = 'error_rates';

    /**
     * Health statuses
     */
    public const STATUS_HEALTHY = 'healthy';
    public const STATUS_WARNING = 'warning';
    public const STATUS_CRITICAL = 'critical';
    public const STATUS_UNKNOWN = 'unknown';

    /**
     * Record a health metric
     */
    public static function record(
        string $metricType,
        string $metricName,
        float $value,
        ?float $threshold = null,
        array $metadata = []
    ): self {
        $status = self::determineStatus($value, $threshold, $metricType);

        return self::create([
            'metric_type' => $metricType,
            'metric_name' => $metricName,
            'value' => $value,
            'status' => $status,
            'threshold' => $threshold,
            'metadata' => $metadata,
            'recorded_at' => now(),
        ]);
    }

    /**
     * Get latest metrics by type
     */
    public static function getLatestByType(string $metricType, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('metric_type', $metricType)
            ->orderBy('recorded_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get health summary for dashboard
     */
    public static function getHealthSummary(): array
    {
        $summary = [];
        
        $metricTypes = [
            self::TYPE_EMAIL_NOTIFICATIONS,
            self::TYPE_JOB_FETCHING,
            self::TYPE_API_PERFORMANCE,
            self::TYPE_DATABASE_HEALTH,
            self::TYPE_QUEUE_HEALTH,
            self::TYPE_SYSTEM_RESOURCES,
            self::TYPE_ERROR_RATES,
        ];

        foreach ($metricTypes as $type) {
            $latest = self::where('metric_type', $type)
                ->orderBy('recorded_at', 'desc')
                ->first();

            $summary[$type] = [
                'status' => $latest->status ?? self::STATUS_UNKNOWN,
                'last_check' => $latest->recorded_at ?? null,
                'value' => $latest->value ?? null,
                'threshold' => $latest->threshold ?? null,
            ];
        }

        return $summary;
    }

    /**
     * Get metrics for time period
     */
    public static function getMetricsForPeriod(
        string $metricType,
        Carbon $startDate,
        Carbon $endDate
    ): \Illuminate\Database\Eloquent\Collection {
        return self::where('metric_type', $metricType)
            ->whereBetween('recorded_at', [$startDate, $endDate])
            ->orderBy('recorded_at', 'asc')
            ->get();
    }

    /**
     * Get critical alerts count
     */
    public static function getCriticalAlertsCount(int $hours = 24): int
    {
        return self::where('status', self::STATUS_CRITICAL)
            ->where('recorded_at', '>=', now()->subHours($hours))
            ->count();
    }

    /**
     * Clean up old metrics (keep last 30 days)
     */
    public static function cleanup(): int
    {
        return self::where('recorded_at', '<', now()->subDays(30))->delete();
    }

    /**
     * Determine health status based on value and threshold
     */
    private static function determineStatus(float $value, ?float $threshold, string $metricType): string
    {
        if ($threshold === null) {
            return self::STATUS_HEALTHY;
        }

        // Different logic for different metric types
        switch ($metricType) {
            case self::TYPE_EMAIL_NOTIFICATIONS:
                // For email notifications, 0 is bad, > 0 is good
                return $value > 0 ? self::STATUS_HEALTHY : self::STATUS_CRITICAL;

            case self::TYPE_JOB_FETCHING:
                // For job fetching, 0 might be warning, negative is critical
                if ($value < 0) return self::STATUS_CRITICAL;
                if ($value === 0.0) return self::STATUS_WARNING;
                return self::STATUS_HEALTHY;

            case self::TYPE_API_PERFORMANCE:
                // For API performance (response time), higher is worse
                if ($value > $threshold * 2) return self::STATUS_CRITICAL;
                if ($value > $threshold) return self::STATUS_WARNING;
                return self::STATUS_HEALTHY;

            case self::TYPE_DATABASE_HEALTH:
                // For database health, higher connection count might be warning
                if ($value > $threshold * 1.5) return self::STATUS_CRITICAL;
                if ($value > $threshold) return self::STATUS_WARNING;
                return self::STATUS_HEALTHY;

            case self::TYPE_QUEUE_HEALTH:
                // For queue health, too many jobs is warning/critical
                if ($value > $threshold * 2) return self::STATUS_CRITICAL;
                if ($value > $threshold) return self::STATUS_WARNING;
                return self::STATUS_HEALTHY;

            case self::TYPE_SYSTEM_RESOURCES:
                // For system resources (memory, CPU), higher is worse
                if ($value > $threshold * 0.9) return self::STATUS_CRITICAL;
                if ($value > $threshold * 0.7) return self::STATUS_WARNING;
                return self::STATUS_HEALTHY;

            case self::TYPE_ERROR_RATES:
                // For error rates, any errors are concerning
                if ($value > $threshold * 2) return self::STATUS_CRITICAL;
                if ($value > $threshold) return self::STATUS_WARNING;
                return self::STATUS_HEALTHY;

            default:
                return self::STATUS_HEALTHY;
        }
    }

    /**
     * Get status color for UI
     */
    public function getStatusColor(): string
    {
        return match ($this->status) {
            self::STATUS_HEALTHY => '#28a745',
            self::STATUS_WARNING => '#ffc107',
            self::STATUS_CRITICAL => '#dc3545',
            self::STATUS_UNKNOWN => '#6c757d',
            default => '#6c757d',
        };
    }

    /**
     * Get status icon for UI
     */
    public function getStatusIcon(): string
    {
        return match ($this->status) {
            self::STATUS_HEALTHY => 'fas fa-check-circle',
            self::STATUS_WARNING => 'fas fa-exclamation-triangle',
            self::STATUS_CRITICAL => 'fas fa-times-circle',
            self::STATUS_UNKNOWN => 'fas fa-question-circle',
            default => 'fas fa-question-circle',
        };
    }

    /**
     * Check if metric indicates a problem
     */
    public function isProblem(): bool
    {
        return in_array($this->status, [self::STATUS_WARNING, self::STATUS_CRITICAL]);
    }

    /**
     * Check if metric is critical
     */
    public function isCritical(): bool
    {
        return $this->status === self::STATUS_CRITICAL;
    }

    /**
     * Get formatted value for display
     */
    public function getFormattedValue(): string
    {
        switch ($this->metric_type) {
            case self::TYPE_API_PERFORMANCE:
                return round($this->value, 2) . 's';
            case self::TYPE_SYSTEM_RESOURCES:
                return round($this->value, 1) . '%';
            case self::TYPE_ERROR_RATES:
                return round($this->value * 100, 2) . '%';
            default:
                return (string) $this->value;
        }
    }
}

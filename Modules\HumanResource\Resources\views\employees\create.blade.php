@extends('layouts.hound')
@section('mytitle', 'Create Employee')

@section('content')

    <div class="row">
        <div class="col-md-5">
            <h3>Create</h3>
        </div>
        <div class="col-md-7 page-action text-right">
            <a href="{{ route('employees.index') }}" class="btn btn-default btn-sm"> <i class="fa fa-arrow-left"></i> Back</a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            {!! Form::open(['route' => ['employees.store'] ]) !!}

<div class="container"> 
    <div class="col-md-8">
        <div class="form-group @if ($errors->has('display_name')) has-error @endif">
                {!! Form::label('display_name', 'Display Name') !!}
                {!! Form::text('name', null, ['class' => 'form-control', 'placeholder' => 'Display Name']) !!}
                @if ($errors->has('name')) <p class="help-block">{{ $errors->first('name') }}</p> @endif
        </div>

        <!-- Name Form Input -->
        <div class="form-group @if ($errors->has('full_name')) has-error @endif">
            {!! Form::label('full_name', 'Name') !!}
            {!! Form::text('full_name', null, ['class' => 'form-control', 'placeholder' => 'Full Name']) !!}
            @if ($errors->has('full_name')) <p class="help-block">{{ $errors->first('full_name') }}</p> @endif
        </div>


        <!-- email Form Input -->
        <div class="form-group @if ($errors->has('email')) has-error @endif">
            {!! Form::label('email', 'Email') !!}
            {!! Form::text('email', null, ['class' => 'form-control', 'placeholder' => 'Email']) !!}
            @if ($errors->has('email')) <p class="help-block">{{ $errors->first('email') }}</p> @endif
        </div>

        <div class="form-group @if ($errors->has('email')) has-error @endif">
            {!! Form::label('gender', 'Gender') !!}
            {!! Form::radio('gender', 'male' , null, [])!!} Male
            {!! Form::radio('gender', 'female' , null, [])!!} Female
             
            @if ($errors->has('gender'))
            <p class="help-block">{{ $errors->first('gender') }}</p> @endif
        </div>
        
        <!-- password Form Input -->
        <div class="form-group @if ($errors->has('password')) has-error @endif">
            {!! Form::label('password', 'Password') !!}
            {!! Form::password('password', ['class' => 'form-control', 'placeholder' => 'Password']) !!}
            @if ($errors->has('password')) <p class="help-block">{{ $errors->first('password') }}</p> @endif
        </div>
        <div class="form-group @if ($errors->has('date_of_birth')) has-error @endif">
            {!! Form::label('date_of_birth', 'Date of Birth') !!}
            {!! Form::text('date_of_birth', null ,  ['class' => 'date form-control', 'placeholder' => 'Date of Birth']) !!}
            @if ($errors->has('date_of_birth')) <p class="help-block">{{ $errors->first('date_of_birth') }}</p> @endif
        </div>


        <div class="form-group @if ($errors->has('nationality')) has-error @endif">
            {!! Form::label('nationality', 'Nationality') !!}
            {!! Form::select('nationality', Countries::lookup(config('app.locale')) ,$student->nationality ?? '' , ['class' => 'select2 form-control']) !!}
            @if ($errors->has('nationality')) <p class="help-block">{{ $errors->first('Nationality') }}</p> @endif
        </div>
        <!-- Roles Form Input -->
        <div class="form-group @if ($errors->has('roles')) has-error @endif">
            {!! Form::label('roles[]', 'Roles') !!}
            {!! Form::select('roles[]', $roles, isset($user) ? $user->roles->pluck('id')->toArray() : null,  ['class' => 'select2 form-control', 'multiple']) !!}
            @if ($errors->has('roles')) <p class="help-block">{{ $errors->first('roles') }}</p> @endif
        </div>

        <!-- Permissions -->
        @if(isset($employee))
            {{--  @include('humanresource::shared._permissions', ['closed' => 'true', 'model' => $employee ])  --}}
        @endif    
    </div>

</div>
                <!-- Submit Form Button -->
                {!! Form::submit('Create', ['class' => 'btn btn-primary']) !!}
            {!! Form::close() !!}
        </div>
    </div>
@endsection


@include('jssnippets.select2')

@include('jssnippets.flatpickr')

@section('js')
<script>
    flatpickr('.date', {
        maxDate: '{{date('Y')-19}}-12-31'
    });
</script>
@append

!function(){"use strict";var t=function(e){var r=e,n=function(){return r};return{get:n,set:function(t){r=t},clone:function(){return t(n())}}},e=tinymce.util.Tools.resolve("tinymce.PluginManager"),r=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),n=tinymce.util.Tools.resolve("tinymce.util.Tools"),a=function(t){return t.fire("RestoreDraft")},o=function(t){return t.fire("StoreDraft")},i=function(t){return t.fire("RemoveDraft")},s=function(t,e){return((t=/^(\d+)([ms]?)$/.exec(""+(t||e)))[2]?{s:1e3,m:6e4}[t[2]]:1)*parseInt(t,10)},u=function(t){return t.getParam("autosave_ask_before_unload",!0)},f=function(t){var e=t.getParam("autosave_prefix","tinymce-autosave-{path}{query}{hash}-{id}-");return e=(e=(e=(e=e.replace(/\{path\}/g,document.location.pathname)).replace(/\{query\}/g,document.location.search)).replace(/\{hash\}/g,document.location.hash)).replace(/\{id\}/g,t.id)},c=function(t){return s(t.settings.autosave_interval,"30s")},l=function(t){return s(t.settings.autosave_retention,"20m")},m=function(t,e){var r=t.settings.forced_root_block;return""===(e=n.trim(void 0===e?t.getBody().innerHTML:e))||new RegExp("^<"+r+"[^>]*>((\xa0|&nbsp;|[ \t]|<br[^>]*>)+?|)</"+r+">|<br>$","i").test(e)},v=function(t){var e=parseInt(r.getItem(f(t)+"time"),10)||0;return!((new Date).getTime()-e>l(t)&&(d(t,!1),1))},d=function(t,e){var n=f(t);r.removeItem(n+"draft"),r.removeItem(n+"time"),!1!==e&&i(t)},D=function(t){var e=f(t);!m(t)&&t.isDirty()&&(r.setItem(e+"draft",t.getContent({format:"raw",no_events:!0})),r.setItem(e+"time",(new Date).getTime().toString()),o(t))},g=function(t){var e=f(t);v(t)&&(t.setContent(r.getItem(e+"draft"),{format:"raw"}),a(t))},y={isEmpty:m,hasDraft:v,removeDraft:d,storeDraft:D,restoreDraft:g,startStoreDraft:function(t,e){var r=c(t);e.get()||(setInterval(function(){t.removed||D(t)},r),e.set(!0))},restoreLastDraft:function(t){t.undoManager.transact(function(){g(t),d(t)}),t.focus()}},p=function(t,e){return function(){var r=Array.prototype.slice.call(arguments);return t.apply(null,[e].concat(r))}},h=function(t){return{hasDraft:p(y.hasDraft,t),storeDraft:p(y.storeDraft,t),restoreDraft:p(y.restoreDraft,t),removeDraft:p(y.removeDraft,t),isEmpty:p(y.isEmpty,t)}},_=tinymce.util.Tools.resolve("tinymce.EditorManager");_._beforeUnloadHandler=function(){var t;return n.each(_.get(),function(e){e.plugins.autosave&&e.plugins.autosave.storeDraft(),!t&&e.isDirty()&&u(e)&&(t=e.translate("You have unsaved changes are you sure you want to navigate away?"))}),t};var b=function(t){window.onbeforeunload=_._beforeUnloadHandler},I=function(t,e){return function(r){var n=r.control;n.disabled(!y.hasDraft(t)),t.on("StoreDraft RestoreDraft RemoveDraft",function(){n.disabled(!y.hasDraft(t))}),y.startStoreDraft(t,e)}},w=function(t,e){t.addButton("restoredraft",{title:"Restore last draft",onclick:function(){y.restoreLastDraft(t)},onPostRender:I(t,e)}),t.addMenuItem("restoredraft",{text:"Restore last draft",onclick:function(){y.restoreLastDraft(t)},onPostRender:I(t,e),context:"file"})};e.add("autosave",function(e){var r=t(!1);return b(e),w(e,r),h(e)})}();
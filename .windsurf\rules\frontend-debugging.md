---
trigger: manual
---

- **Zero Tolerance Quality Standard**
  - Every debugging session MUST result in a completely resolved issue
  - No partial fixes or workarounds allowed
  - All root causes MUST be identified and permanently addressed
  - Comprehensive testing MUST verify the complete resolution

- **Systematic Approach**
  - Read the exact error message with complete attention to detail
  - Document every step of the debugging process
  - Identify the precise file and line number
  - Look for **common patterns** with zero tolerance for assumptions
  - Maintain a detailed debugging log for future reference

- **Common Issue Patterns**
  - **CORS errors** → Implement bulletproof CORS configuration
    - Document all headers and configurations
    - Test across all environments
    - Verify security implications
  - **Hydration mismatches** → Achieve perfect server/client synchronization
    - Comprehensive component testing
    - Full state management verification
    - Cross-browser validation
  - **Routing problems** → Implement foolproof routing logic
    - 100% test coverage for routes
    - Complete navigation testing
    - Perfect error handling
  - **State Management** → Zero tolerance for state inconsistencies
    - Complete state flow documentation
    - Comprehensive state testing
    - Perfect error recovery

- **Debugging Hierarchy**
  1. **Console errors** – Must be completely eliminated
  2. **Network failures** – Zero tolerance for unhandled requests
  3. **Logic errors** – Perfect behavioral consistency required
  4. **UI/UX issues** – Flawless visual implementation
  5. **Performance optimizations** – Meet all performance metrics

- **Advanced Browser Automation Tools (MANDATORY)**
  - **Navigation & Session Management**
    - `browser_navigate` - Navigate to specific URLs for testing
    - `browser_navigate_back/forward` - Test browser history functionality
    - `browser_tab_new/select/close/list` - Multi-tab testing scenarios
    - `browser_close` - Clean session termination
  
  - **Interactive Testing & Debugging**
    - `browser_click/hover/drag` - Test user interactions and event handlers
    - `browser_type/press_key` - Keyboard input testing and form validation
    - `browser_select_option` - Dropdown and select element testing
    - `browser_handle_dialog` - Alert, confirm, and prompt dialog testing
    - `browser_file_upload` - File input testing and validation
  
  - **Comprehensive Monitoring & Analysis**
    - `browser_console_messages` - **MANDATORY for every debugging session**
    - `browser_network_requests` - Monitor API calls, failed requests, CORS issues
    - `browser_snapshot` - Capture accessibility tree and DOM structure
    - `browser_take_screenshot` - Visual regression testing and documentation
    - `browser_evaluate` - Execute JavaScript for state inspection and testing
  
  - **Environment & Performance Testing**
    - `browser_resize` - Responsive design testing across breakpoints
    - `browser_wait_for` - Async operation testing and loading state verification
    - `browser_install` - Ensure proper browser setup for testing

- **Browser Automation Debugging Protocol (MANDATORY)**
  - **Step 1: Environment Setup**
    - Use `browser_install` to ensure testing environment is ready
    - Use `browser_resize` to test at mobile (375px), tablet (768px), and desktop (1920px) breakpoints
  
  - **Step 2: Navigation & Initial State**
    - Use `browser_navigate` to reach the problematic page/component
    - Use `browser_console_messages` to capture initial console state
    - Use `browser_snapshot` to document initial DOM/accessibility state
  
  - **Step 3: Issue Reproduction**
    - Use `browser_click/type/hover` to reproduce the exact user interaction
    - Use `browser_console_messages` after each interaction to track errors
    - Use `browser_network_requests` to monitor API calls and failures
    - Use `browser_take_screenshot` to document visual issues
  
  - **Step 4: Deep Analysis**
    - Use `browser_evaluate` to inspect component state, props, and context
    - Use `browser_console_messages` to verify error patterns and stack traces
    - Use `browser_snapshot` to analyze accessibility violations
    - Use multiple `browser_tab_new` sessions to test different scenarios
  
  - **Step 5: Solution Verification**
    - After implementing fixes, use `browser_navigate` to reload
    - Use `browser_console_messages` to verify zero console errors
    - Use `browser_click/type/hover` to re-test all interactions
    - Use `browser_resize` to verify responsive behavior
    - Use `browser_take_screenshot` for before/after comparison

- **Traditional Tools & Techniques**
  - Implement **comprehensive logging strategy**
  - Use **all available debugging tools** (including browser automation above)
  - Maintain **complete test coverage**
  - Perform **exhaustive cross-browser testing**
  - Document **all debugging findings**

- **Progressive Problem Solving**
  - Start with **thorough problem analysis**
  - Create **complete reproduction steps**
  - Implement **permanent solutions only**
  - Verify fix with **comprehensive testing**
  - Document **resolution and prevention**

- **Quality Assurance**
  - **100% resolution verification**
  - **Cross-browser compatibility**
  - **Performance impact analysis**
  - **Security implications review**
  - **Accessibility compliance check**

- **Documentation Requirements**
  - **Complete issue description**
  - **Detailed resolution steps**
  - **Prevention measures**
  - **Testing procedures**
  - **Future recommendations**

- **Post-Resolution Protocol**
  - **Verify in all environments**
  - **Update related documentation**
  - **Share knowledge with team**
  - **Implement preventive measures**
  - **Monitor for recurrence**


  - **Don't over-engineer** solutions.
  - Break complex problems into **smaller parts**.
  - **Always verify** the fix actually works before moving on.


<?php

namespace Modules\HumanResource\Http\Controllers;

use App\CenterTranslation;
use App\EmployeeSalary;
use App\LeaveDefine;
use App\LeaveType;
use App\Organization;
use App\Student;
use Carbon\Traits\Creator;
use Dflydev\DotAccessData\Data;
use Doctrine\DBAL\Cache\CacheException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;

use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Jenssegers\Agent\Agent;

use App\Employee;

use App\Authorizable;

use App\Attendance;
use Carbon\Carbon;
use App\LeaveRequest;
use App\Role;


class AttendanceController extends Controller
{

    public function getMonthlyAttendanceData(Request $request)
    {

        // Validate the year and month in the request
        $request->validate([
            'year' => 'required|integer',
            'month' => 'required|integer|min:1|max:12',
        ]);

        // Extract year and month
        $year = $request->input('year');
        $month = $request->input('month');

        // Get distinct days where attendance exists for the given month and year
        $attendanceDays = Attendance::withTrashed()->whereYear('clock', $year)
            ->whereMonth('clock', $month)
            ->selectRaw('DATE(clock) as date')  // Ensure we are selecting the full date
            ->distinct()                      // Get unique days
            ->pluck('date');

        // Return the attendance days as a JSON array (list of days where attendance exists)
        return response()->json($attendanceDays);
    }



    // use Authorizable;
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {


//        if (auth()->guard('employee')->user()->hasRole('supervisor_2_'))
//        {
//            dd(auth()->guard('employee')->user()->with('center')->first());
//            auth()->guard('employee')->user()->where('votes', '>', 100);
//        }


        // return ;
        // if (!auth()->user()->hasRole('enterprise') && ! auth()->user()->hasRole('finance-manager_1_')) {
        if (!auth()->user()->can('access attendance')) {
            return redirect('/workplace/humanresource/attendance/' . auth()->user()->id);
            $result = [auth()->user()];
        } else {
            $result = Employee::latest()->paginate();
        }

        return view('humanresource::attendance.list', compact('result'));
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        $roles = Role::pluck('description', 'name');

        return view('humanresource::employees.create', compact('roles'));
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // hash password
        $request->merge(['organization_id' => config('organization_id')]);

        if ($request->ajax()) {


            // self clock in/out
            $last_attendance_record = Attendance::where('employee_id', auth()->user()->id)->latest()->first();

            if ($last_attendance_record && $last_attendance_record->clock->format('Y-m-d') == date('Y-m-d')) {
                $type = $last_attendance_record->type == 'in' ? 'out' : 'in';
            } else {
                $type = 'in';
            }


//            if($type == 'out') {
//
//                $inTime = Carbon::parse($last_attendance_record->date)->format('Y-m-d H:i:s');
//
//                $outTime = Carbon::now('Asia/Kuala_Lumpur');
//
//
//                $outTimeLessThanIn = $outTime->lt($inTime);
//                $outTimeEqIn = $outTime->equalTo($inTime);
//
//
//                if ($outTimeEqIn == true) {
//
//                    return response()->json('Please be present here for few seconds', 422);
//
//                }
//            }


            $agent = new Agent();
            $attendance = Attendance::create([
                'employee_id' => auth()->user()->id,
                'organization_id' => config('organization_id'),
                'clock' => date('Y-m-d H:i:s'),
                'type' => $type,
//                'location' => $request->location ?? 'NOT',
                'location' => 'currently disabled as per requested by HR department',
                'device' => $agent->device(),
                'ip' => $request->ip(),
                'note' => $request->note,
                'created_by' => auth()->user()->id,
            ]);

//            return response()->json(['status' => 'success', 'type' => ucfirst($type == "in" ? "Clock Out" : "Clock In"), 'clock' => $attendance->clock->format('d/M/Y g:i a')], 200);
//            return response()->json(['status' => 'success', 'nextType' => ucfirst($type == "in" ? "Out" : "In"), 'clock' => $attendance->clock->format('d/M/Y g:i a')], 200);
            return response()->json(['status' => 'success', 'nextType' => ucfirst($type == "in" ? "Out" : "In"), 'clock' => $attendance->clock->diffForHumans()], 200);
        }
        // add record by admin

        return redirect()->route('employees.index');
    }

    public function storeInRecord(Request $request)
    {
        // hash password
        $request->merge(['organization_id' => config('organization_id')]);

        if ($request->ajax()) {

            try {

                \DB::beginTransaction();


                if (!auth()->user()->can('add attendance')) {
                    return response()->json(['error' => 'not_autorized'], 300);
                }


                if (!$request->exists('in') && $request->exists('out')) {
                    return response()->json('Please complete the details', 422);
                }


                $inTime = Carbon::parse($request->get('in'));
                $inNote = $request->get('inNote');


                $agent = new Agent();
                $inAttendance = Attendance::create([
                    'employee_id' => $request->get("employee_id"),
                    'organization_id' => config('organization_id'),
                    'clock' => $inTime,
                    'type' => 'in',
                    'location' => $request->location ?? 'NOT',
                    'device' => $agent->device(),
                    'ip' => $request->ip(),
                    'note' => $inNote,
                    'created_by' => auth()->user()->id,
                ]);

                \DB::commit();
                return response()->json(['status' => 'success'], 200);

            } catch (\Exception $exception) {
                \Log::error($exception);
                \DB::rollBack();

                $errorMessage = $exception->getMessage();
                return response()->json(compact('errorMessage'));
            }
        }
        // add record by admin

        return redirect()->route('employees.index');
    }

    public function storeOutRecord(Request $request)
    {
        // hash password
        $request->merge(['organization_id' => config('organization_id')]);

        if ($request->ajax()) {

            try {


                \DB::beginTransaction();


                if (!auth()->user()->can('add attendance')) {
                    return response()->json(['error' => 'not_autorized'], 300);
                }


                $outNote = $request->get('outNote');

                $outTime = Carbon::parse($request->get('out'));

//                $outTimeGreater = $outTime->gt($inTime);
//                $outTimeLessThanIn = $outTime->lt($inTime);
//                $outTimeEqIn = $outTime->equalTo($inTime);
//                $InTimeEqOut = $inTime->equalTo($outTime);
//
//
//                if ($outTimeLessThanIn == true) {
//
//                    return response()->json('The OUT time can not be less than the IN time', 422);
//                }if ($outTimeEqIn == true OR $InTimeEqOut == true) {
//
//                    return response()->json('The OUT time can not be equal to the IN time', 422);
//                }
//                if ($outTimeGreater == false) {
//                    return response()->json('The OUT time can not be less than the IN time . Please select greater time for the OUT field', 422);
//                }


                $agent = new Agent();


                $outAttendance = Attendance::create([
                    'employee_id' => $request->get("employee_id"),
                    'organization_id' => config('organization_id'),
                    'clock' => $outTime,
                    'type' => 'out',
                    'location' => $request->location ?? 'NOT',
                    'device' => $agent->device(),
                    'ip' => $request->ip(),
                    'note' => $outNote,
                    'created_by' => auth()->user()->id,
                ]);

                \DB::commit();
                return response()->json(['status' => 'success'], 200);

            } catch (\Exception $exception) {
                \Log::error($exception);
                \DB::rollBack();

                $errorMessage = $exception->getMessage();
                return response()->json(compact('errorMessage'));
            }
        }
    }

    public function storePairRecord(Request $request)
    {
        // hash password
        $request->merge(['organization_id' => config('organization_id')]);

        if ($request->ajax()) {

            try {


                if (!auth()->user()->can('add attendance')) {
                    return response()->json(['error' => 'not_autorized'], 300);
                }

                if (!$request->exists('in') && $request->exists('out')) {
                    return response()->json('Please complete the details', 422);
                }

                $inTime = Carbon::parse($request->get('in'));
                $inNote = $request->get('inNote');
                $outNote = $request->get('outNote');

                $outTime = Carbon::parse($request->get('out'));
                $outTimeGreater = $outTime->gt($inTime);
                $outTimeLessThanIn = $outTime->lt($inTime);
                $outTimeEqIn = $outTime->equalTo($inTime);
                $InTimeEqOut = $inTime->equalTo($outTime);


                if ($outTimeLessThanIn == true) {

                    return response()->json('The OUT time can not be less than the IN time', 422);
                }
                if ($outTimeEqIn == true or $InTimeEqOut == true) {

                    return response()->json('The OUT time can not be equal to the IN time', 422);
                }
                if ($outTimeGreater == false) {
                    return response()->json('The OUT time can not be less than the IN time . Please select greater time for the OUT field', 422);
                }


                $agent = new Agent();
                $inAttendance = Attendance::create([
                    'employee_id' => $request->get("employee_id"),
                    'organization_id' => config('organization_id'),
                    'clock' => $inTime,
                    'type' => 'in',
                    'location' => $request->location ?? 'NOT',
                    'device' => $agent->device(),
                    'ip' => $request->ip(),
                    'note' => $inNote,
                    'created_by' => auth()->user()->id,
                    'action_trigerred_from_url' => $request->actionTrigerredFromURL,
                ]);

                $outAttendance = Attendance::create([
                    'employee_id' => $request->get("employee_id"),
                    'organization_id' => config('organization_id'),
                    'clock' => $outTime,
                    'type' => 'out',
                    'location' => $request->location ?? 'NOT',
                    'device' => $agent->device(),
                    'ip' => $request->ip(),
                    'note' => $outNote,
                    'created_by' => auth()->user()->id,
                    'action_trigerred_from_url' => $request->actionTrigerredFromURL,

                ]);

            } catch (\Exception $exception) {
                \Log::error($exception);
                $errorMessage = $exception->getMessage();
                return response()->json(compact('errorMessage'));
            }
            return response()->json(['status' => 'success'], 200);
        }
        // add record by admin

        return redirect()->route('employees.index');
    }

    /**
     * Show the specified resource.
     * @return Response
     */
//    public function show(Request $request, $id, $date = null)
//    {
//
//
//
//
//
//
//
//        try {
//
//            if ($request->exists('monthFilter')) {
//                $date = request()->get('monthFilter');
//            } else {
//                $date = Carbon::today()->toDateString();
//            }
//            $report = $this->report($id, $date);
//            // to be refactored
//            extract($report);
//            $employee = Employee::findOrFail($id);
//
//            if ($employee->work_mood == 'per_month') {
//
//                if (!auth()->user()->can('access attendance')) {
//                    // $id = auth()->user()->id;
//                }
//
//
//
//
//                if (request()->ajax()) {
//
//
////                    if($record['worked_hours'] > 0){
//
//
//                    return \Yajra\DataTables\DataTables::of($daily_record)
//                        ->with('totalWorkingHours', '<span style="background: #ff8664 " class="badge badge-info">' . $total_working_hours . ' Hours</span>')
//                        ->with('totalRequiredWorkingHours', '<span style="background: #57a7ff ; color: #fefefe; " class="badge badge-info">' . $total_required_hours . ' Hours</span>')
//                        ->with('totalMissingWorkingHours', '<span style="background: #ff8664 " class="badge badge-info">' . $total_missed_hours . ' Hours</span>')
//                        ->with('totalVolunteeredHours', '<span style="background: #ff8664 " class="badge badge-info">' . $total_volunteered_hours . ' Hours</span>')
//                        ->with('totalSickLeaves', '<span style="background: #ff8664 " class="badge badge-info">' . $total_sick_leaves . '</span>')
//                        ->addIndexColumn()
//                        ->addColumn('date', function ($record) use ($daily_record) {
//
//
//                            return $record['date'];
//
//
//                        })
//                        ->addColumn('dayandDate', function ($record) use ($daily_record) {
//
//
//                            return '<span style="text-transform: capitalize">' . $record['day'] . '<br>' . $record['date'] . '</span>';
////                    return $record['date'];
//
//                        })
//                        ->addColumn('insandouts', function ($drecord) use ($daily_record) {
//
//
//
//
//                            $str = '';
//
//
//                            foreach ($drecord['attendance'] as $attendance) {
//
//                                $str .= '<div class="ui equal width center aligned padded grid" style="height: 37px;">';
//
//                                if (isset($attendance['in'])) {
//
//                                    $inNote = $attendance['in']->note;
//                                    $time = $attendance['in']->clock->format('h:iA');
//                                    $inNoteStar = $attendance['in']->note == true ? '*' : '';
//                                    $str .= '  <div class="four wide column attendance-record">
//                        <button type="button" class="ui  basic button" title=""  style="    width: 80px;" data-inverted="" data-position="top left"  data-tooltip="' . $inNote . '">
//                                                   <small class="label label-success" style="border-radius: 6px;">in</small>' . $time . $inNoteStar . '
//                                                   <div class="clock-note">
//                                                      ' . $inNote . '
//                                                   </div>
//                                               </button></div>';
//                                } else {
//                                    $str .= '<div class="four wide column attendance-record "><button type="button" class="ui  basic button  " style="    width: 80px;" data-inverted="">
//                                                   <small class="label label-warning" style="border-radius: 6px;bottom: -9px;"></small>
//                                                   <div class="clock-note">
//
//                                                   </div>
//
//                                               </button></div>';
//                                }
//                                if (isset($attendance['out'])) {
//                                    $outNote = $attendance['out']->note;
//                                    $time = $attendance['out']->clock->format('h:iA');
//                                    $outNoteStar = $attendance['out']->note == true ? '*' : '';
//
//                                    $str .= '<div class="four wide column attendance-record ">
//                                <button type="button" class="ui  basic button"  title="" data-inverted="" data-position="top left" style="    width: 80px;" data-tooltip="' . $outNote . '">
//                                                   <small class="label label-warning" style="border-radius: 6px;bottom: -9px;">out</small>' . $time . $outNoteStar . '
//                                                   <div class="clock-note">
//                                                      ' . $outNote . '
//                                                   </div>
//
//                                               </button></div>';
//                                } else {
//                                    $str .= '<div class="four wide column attendance-record "><button type="button" class="ui  basic button  " style="    width: 80px;" data-inverted="" data-position="top left" data-tooltip="employee is either working or forgot to punch out">
//                                                   <small class="label label-warning" style="border-radius: 6px;bottom: -9px;">Working</small>
//                                                   <div class="clock-note">
//
//                                                   </div>
//
//                                               </button></div>';
//                                }
//
//                                if (isset($attendance['duration_in_hours'])) {
//
////                            $str .= ' <div class="four wide column" style="right: 63px;">
//                                    $str .= ' <div class="four wide column" >
// <a class="ui green label" style="border-radius: 6px;">duration</a>' . $attendance["duration_in_hours"];
//                                }
//
//                                $str .= '</div></div></div>';
//
//                            }
//
//
//                            return $str;
//
//
//                        })
//                        ->addColumn('action', function ($record) use ($daily_record, $id) {
//
//                            $deletebtn = '<div class="or"></div><button  data-date="' . $record['date'] . '" class="ui button blue deleteModalTriggerBtn" id="deleteModalTriggerBtn" data-catid = ' . $record['attendance'] . ' data-toggle = "modal"
//                                                                                                        data-target = "#delete" >Delete</button>';
//
//
//
//
//
//                            $rh = $record["required_hours"] ?? '';
//
//                            $str = '<div class="ui list">
//                      <div class="item">Required: ' . $rh . '</div>
//                      <div class="item">Done: ' . $record["worked_hours"] . '</div>
//
//                    </div></div><br>';
//                            if (\Auth::user()->can('update attendance')) {
//
//                                $approvedLeaveRequests = LeaveRequest::where('employee_id', $id)->where('approve_status', 'A')->whereDate('leave_from', $record['date']);
//
//                                if (!$approvedLeaveRequests->exists()) {
//
//                                    $sickLeave = LeaveType::where('type', 'Sick Leave')->with('leaveDefine')->first();
//
//                                    $leaveDefine = LeaveDefine::where('type_id', $sickLeave->id)->where('user_id', $id)->first();
//
//                                    $sickLeaveBtn = '';
//
//                                    $sickLeaveBtn = '
//                                        <div class="or"></div>
//                                        </div>
//                                        <button
//                                        data-apply_date="' . $record['date'] . '"
//                                        data-leave_type="' . $sickLeave->id . '"
//                                        data-role_id="' . $leaveDefine->role_id . '"
//                                        data-leave_define_id="' . $leaveDefine->id . '"
//                                        data-leave_from="' . $record['date'] . '"
//                                        data-leave_to="' . $record['date'] . '"
//                                        class="ui labeled icon default button sickLeaveModalTriggerBtn" id="sickLeaveModalTriggerBtn" data-catid = ' . $record['attendance'] . ' data-toggle = "modal"
//                                                                                                        data-target = "#sickLeave" ><i class="bed icon"></i>Sick</button>';
//
//                                    $btns = '<div class="ui buttons">
//                      <button class="ui button teal" data-toggle="modal" data-target="#editAttendanceModal"  data-date="' . $record['date'] . '" id="editAttendanceBtn">Edit</button>
//                      ' . $deletebtn . $sickLeaveBtn;
//
//
//                                }else{
//
//                                    return '<a class="ui teal tag label">Sick Leave</a>';
//
//                                }
//
//
//
//
//                            }
//
//
////
//                            return $str . $btns;
//                        })
//                        ->setRowClass(function ($record) use ($id) {
//                            $approvedLeaveRequests = LeaveRequest::where('employee_id', $id)->where('approve_status', 'A')->whereDate('leave_from', $record['date']);
//
//                            return $approvedLeaveRequests->exists() ? 'disabled ': ''  ;
//                        })
//                        ->rawColumns(['dayandDate', 'insandouts', 'action', 'totalWorkingHours', 'totalRequiredWorkingHours', 'totalMissingWorkingHours', 'totalVolunteeredHours'])
//                        ->toJson();
//
//
//                }
//            }
//
//
//                $first_working_month = Carbon::createFromFormat('Y - m - d', $employee->start_at);
//
////         $attendance_list = Attendance::where('employee_id',$employee->id)
////                                 ->where('clock' , ' >= ' , $current_month->format('Y - m - d'))
////                                 ->where('clock', ' < ' , $next_month->format('Y - m - d'))
////                                 ->get();
//
//                // // return $attendance_list;
//
////         $attendance = [];
//
////         $counter = 0;
////         $steps = 1;
//
////         foreach($attendance_list as $item){
////             if($item->type == 'in'){
////                 $counter++;
////             }
////             $attendance[$item->clock->format('Y - m - d')][$counter][$item->type] = $item;
////         }
////         $total_working_minutes = 0;
//
////         foreach ($attendance as $key => $record) {
////             foreach($record as $i => $entry){
////                 // check if it is public holiday
//
////                 // check if on leave
//
////                 // count hours and minutes
////                 if(isset($entry['in']) && isset($entry['out'])){
////                     $working_minutes = $entry['out']->clock->diffInMinutes($entry['in']->clock);
////                     $attendance[$key][$i]['duration_in_hours'] = number_format($working_minutes/60 , 2);
////                     $total_working_minutes += $working_minutes;
////                 }else{
////                     $attendance[$key][$i]['duration_in_hours'] = 'Error';
////                 }
//
////             }
////         }
////         $attendance = (array_merge_recursive($attendance , $on_leave));
//
////         $total_working_hours = number_format($total_working_minutes/60 , 2);
////         // return $attendance;
////         // $attendance = [];
////         // for ($loop = 0 ; $loop < $attendance->count() ; $loop+=2) {
//
////         //     $attendance[$attendance[$loop]->clock->format('Y - m - d')][] = $item;
////         // }
//
////         // return $attendance;
//
////         // return $current_month->format('Y - m - d');
////         // return $employee;
//                $roles = Role::pluck('description', 'name');
//
//
//                return view(
//                    'humanresource::attendance.report',
//                    compact(
//                        'roles',
//                        'employee',
//                        'attendance',
//                        'daily_record',
//                        'next_month',
//                        'current_month',
//                        'public_holidays',
//                        'weekend',
//                        'first_working_month',
//                        'total_required_hours',
//                        'total_working_hours'
//                    )
//                );
//
//            }
//        catch
//            (\Exception $e) {
//
//                dd($e->getMessage());
//                return response()->json($e->getMessage(), 500);
//            }
//
//    }

//    public function daily_report(Request $request)
//    {
//
//        $employee = Employee::all();
//        $role = Role::get();
//        $supervisorsList = Employee::whereHas('roles', function ($q) {
//
//
//            return $q->where('id', 9);
//        })->pluck('full_name', 'id');
//
////        $pairedAttendance = Attendance::withTrashed()->whereDate('clock', Carbon::now()->toDateString());
//        $pairedAttendance = Attendance::withTrashed()
//            ->with('employee') // Eager load the employee relationship
//
//            ->whereYear('clock', Carbon::now()->year)
//            ->whereMonth('clock', Carbon::now()->month);
//        if ((auth()->user()->hasRole(['supervisor_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {
//
//
//
//            $pairedAttendance->whereHas("employee.teacherCenter", function ($q) {
//                $supervisorCenId = auth()->user()->center()->get()->pluck('id');
//
//                $supervisorCenId = $supervisorCenId->toArray();
//                $q->whereIn("cen_id", $supervisorCenId);
//            });
//
//        }
//        $pairedAttendance = $pairedAttendance->orderByRaw('date(clock) desc, employee_id')->paginate(50);
//
//
//        return view(
//            'humanresource::attendance.daily_report',
//            compact(
//                'employee',
//                'pairedAttendance',
//                'supervisorsList',
//                'daily_record',
//                'role',
//                'date'
//            )
//        );
//    }

    public function daily_report(Request $request)
    {

        // Get all employees and roles (for other view needs)
        $employee = Employee::all();
        $role = Role::get();
        $supervisorsList = Employee::whereHas('roles', function ($q) {
            return $q->where('id', 9);
        })->pluck('full_name', 'id');

        // Build the attendance query for the current year and month.
        $query = Attendance::withTrashed()
            ->with('employee') // eager load the employee relationship
            ->whereYear('clock', Carbon::now()->year)
            ->whereMonth('clock', Carbon::now()->month)
            ->whereDay('clock', Carbon::now()->day);

        // If the logged-in user is a supervisor (but not HR, website editor, or MD),
        // limit results based on the teacherCenter relationship.
        if (auth()->guard('employee')->user()->hasRole(['supervisor_' . config('organization_id') . '_'])
            && !auth()->guard('employee')->user()->hasRole([
                'human-resource_' . config('organization_id') . '_',
                'website-editor_' . config('organization_id') . '_',
                'managing-director_' . config('organization_id') . '_'
            ])
        ) {
            $query->whereHas('employee.teacherCenter', function ($q) {
                $supervisorCenId = auth()->user()->center()->pluck('centers.id')->toArray();
                $q->whereIn('cen_id', $supervisorCenId);
            });
        }

        // Order the records and get the full collection.
        $attendances = $query->orderByRaw('date(clock) desc, employee_id')->get();

        // Group the attendances by employee and date (format: employeeID_YYYY-MM-DD)
        $grouped = $attendances->groupBy(function ($item) {
            return $item->employee_id . '_' . \Carbon\Carbon::parse($item->clock)->format('Y-m-d');
        });

        // Pair records in each group.
        // For simplicity, we take the first "in" record (if any) and the last "out" record.
        $pairedAttendance = [];
        foreach ($grouped as $group) {
            // Sort group chronologically.
            $group = $group->sortBy('clock')->values();
            $pair = ['in' => null, 'out' => null];

            foreach ($group as $record) {
                if ($record->type === 'in' && is_null($pair['in'])) {
                    $pair['in'] = $record;
                }
                if ($record->type === 'out') {
                    $pair['out'] = $record;
                }
            }
            $pairedAttendance[] = $pair;
        }

        // Pass the data to the view. (Removed $daily_record and $date if unused.)
        return view('humanresource::attendance.daily_report', compact(
            'employee',
            'pairedAttendance',
            'supervisorsList',
            'role'
        ));
    }


    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        $employee = Employee::find($id);
        $roles = Role::pluck('name', 'id');
        $permissions = Permission::all('name', 'id');

        return view('humanresource::employees . edit', compact('employee', 'roles', 'permissions'));
    }


    public function update(Request $request, $id)
    {


        if (!auth()->user()->can('update attendance')) {
            return response()->json(['error' => 'not_autorized'], 300);
        }


        if (!$request->exists('in') && $request->exists('out')) {
            return response()->json('Please complete the details', 422);
        }


        $inTime = new Carbon($request->get('in'));

        $outTime = new Carbon($request->get('out'));
        $outTimeGreater = $outTime->gt($inTime);
        $outTimeEqIn = $outTime->equalTo($inTime);
        $InTimeEqOut = $inTime->equalTo($outTime);


        if ($outTimeEqIn == true or $InTimeEqOut == true) {

            return response()->json('The OUT time can not be equal to the IN time', 422);
        }
        if ($outTimeGreater == false) {
            return response()->json('The OUT time can not be less than the IN time . Please select greater time for the OUT field', 422);
        }
        // --  validation logic ends here


        if (isset($record['in']['id'])) {
            $aRecord = Attendance::findOrFail($record['in']['id']);

            if ($aRecord->clock != $record['in']['clock']) {
                $aRecord->clock = $record['in']['clock'];
                $aRecord->type = $record['in']['type'];

                $aRecord->note = $aRecord->note . " \n Updated By " . auth()->user()->name;

                $aRecord->save();
            }
        } else {
            $agent = new Agent();


            Attendance::create([
                'employee_id' => $request->employee_id,
                'organization_id' => config('organization_id'),
                'clock' => $record['in']['clock'],
                'type' => $record['in']['type'],
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),
                'note' => 'Manual Entry',
                'created_by' => auth()->user()->id,
            ]);
        }

        if (isset($record['out']['id'])) {
            $aRecord = Attendance::findOrFail($record['out']['id']);

            if ($aRecord->clock != $record['out']['clock']) {
                $aRecord->clock = $record['out']['clock'];
                $aRecord->type = $record['out']['type'];
                $aRecord->note = $aRecord->note . "\n Updated By " . auth()->user()->name;

                $aRecord->save();
            }
        } else {
            $agent = new Agent();

            Attendance::create([
                'employee_id' => $request->employee_id,
                'organization_id' => config('organization_id'),
                'clock' => $record['out']['clock'],
                'type' => $record['out']['type'],
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),
                'note' => 'Manual Entry',
                'created_by' => auth()->user()->id,
            ]);
        }


        return $request->records;

        /*
        $this->validate($request, [
            'name' => 'bail | required | min:2',
            'email' => 'required | email | unique:employees,email,' . $id,
            'roles' => 'required | min:1'
        ]);

        // Get the user
        $user = Employee::findOrFail($id);

        // Update user
        $user->fill($request->except('roles', 'permissions', 'password'));

        // check for password change
        if ($request->get('password')) {
            $user->password = bcrypt($request->get('password'));
        }

        // Handle the user roles
        $this->syncPermissions($request, $user);

        $user->save();
        flash()->success('User has been updated . ');
        return redirect()->route('employees . index'); */
    }


    // per day deletion
    public function destroy(Request $request, $date)
    {


        Attendance::where(\DB::raw("date(clock)"), $date)->where("employee_id", $request->get("employee_id"))->delete();


        return response()->json('attendance deleted', 200);
    }


    // per entry deletion
    public function destroyIndividualRecord(Request $request, $id)
    {


        Attendance::where('id', $id)->delete();


        return response()->json('attendance deleted', 200);
    }

    private function syncPermissions(Request $request, $user)
    {
        // Get the submitted roles
        $roles = $request->get('roles', []);
        $permissions = $request->get('permissions', []);

        // Get the roles
        $roles = Role::find($roles);

        // check for current role changes
        if (!$user->hasAllRoles($roles)) {
            // reset all direct permissions for user
            $user->permissions()->sync([]);
        } else {
            // handle permissions
            $user->syncPermissions($permissions);
        }

        $user->syncRoles($roles);
        return $user;
    }

    public function mobile()
    {
        return view('humanresource::attendance . mobile');
    }


    private function calculateAttendanceDuration($attendance_list)
    {


        $attendance = [];

        $total_working_minutes = 0;

        $counter = 0;
        $steps = 1;


        foreach ($attendance_list as $item) {
            if ($item->type == 'in') {
                $counter++;
            }
            $attendance[$counter][$item->type] = $item;
        }


        foreach ($attendance as $i => $entry) {

            // count hours and minutes
            if (isset($entry['in']) && isset($entry['out'])) {
                $working_minutes = $entry['out']->clock->diffInRealMinutes($entry['in']->clock);

//                $attendance[$i]['duration_in_hours'] = number_format($working_minutes / 60, 2);
                $attendance[$i]['duration_in_hours'] = intdiv($working_minutes, 60) . ':' . ($working_minutes % 60);
                $total_working_minutes += $working_minutes;
            } else {
                $attendance[$i]['duration_in_hours'] = 'Error';
            }
        }


        return ['attendance' => $attendance, 'duration' => $total_working_minutes];
    }

    public function report($id, $date)
    {


        $leave_requests = LeaveRequest::where(['employee_id' => $id, 'approve_status' => 'approved'])->get();

        // return $leave_requests;

        $on_leave = [];

        foreach ($leave_requests as $leave_request) {
            if ($leave_request->from_date->format('Y-m-d') == $leave_request->to_date->format('Y-m-d')) {
                $on_leave[$leave_request->from_date->format('Y-m-d')] = ['allowed_time' => $leave_request['allowed_time']];
            } else {
                for ($i = $leave_request->from_date; $i->format('Y-m-d') <= $leave_request->to_date->format('Y-m-d'); $i->addDay()) {
                    $on_leave[$i->format('Y-m-d')] = ['allowed_time' => $leave_request['allowed_time']];
                }
            }
        }


        // temp ;to be dynamicly loaded

        $weekend = ['Sat', 'Sun'];
        $public_holidays = ['2018-01-01', '2018-01-31'];

        // end of temp


        $report_date = false;

        if ($date) {
            $report_date = Carbon::parse($date)->format('Y-m');
        }


        if ($report_date !== false) {
            $start_date = $report_date . '-01';
        } else {
            $start_date = $date ?? date('Y-m-01');
        }


        $current_month = Carbon::createFromFormat('Y-m-d', $start_date);


        $next_month = $current_month->copy()->addMonth();

        $employee = Employee::findOrFail($id);


        /* NEW */
        $end_date = $current_month->copy()->addMonth();

        $daily_record = [];
        $total_working_minutes = 0;
        $total_required_hours = 0;
        $total_working_hours = 0;
        $total_missed_hours = 0;
        $total_volunteered_hours = 0;
        $total_sick_leaves = 0;

        $approvedLeaveRequests = LeaveRequest::where('employee_id', $id)->where('approve_status', 'A')
            ->whereMonth('leave_from', date('m'));

        if ($approvedLeaveRequests->exists()) {
            $total_sick_leaves = LeaveRequest::where('employee_id', $id)->where('approve_status', 'A')
                ->whereMonth('leave_from', date('m'))->count();

        }
        $date = Carbon::parse($date)->firstOfMonth();


        while ($date < $end_date) {
            $day = strtolower($date->format('D'));
            $record = [];
            $attendance_list = Attendance::where('employee_id', $employee->id)
                ->where('clock', '>=', $date->format('Y-m-d  00:00:00'))
                ->where('clock', '<=', $date->format('Y-m-d 23:59:59'))
//                ->orderBy('clock')
                ->orderBy('created_at')
                ->get();
            $attendance_duration = $this->calculateAttendanceDuration($attendance_list);
            $record['day'] = $day;

            $record['worked_hours'] = round(fdiv($attendance_duration['duration'], 60) . ':' . ($attendance_duration['duration'] % 60), 2);
            $empHoursPerMonth = $employee->hours_per_month;
//            $record['volunteered_hours'] = abs($empHoursPerMonth - $record['worked_hours']);
//            $record['volunteered_hours'] = round(fdiv($attendance_duration['duration'], 60) . ':' . ($attendance_duration['duration'] % 60), 2);

            $record['attendance'] = $attendance_duration['attendance'];
            $record['date'] = $date->format('Y-m-d');
//


            if ($employee->work_mood == \App\EmployeeWorkMood::where('slug', 'per_month')->first()->id) {
                $total_required_hours = $employee->hours_per_month;

                if ($employee->hours_per_month / $date->daysInMonth < 1) {

                    $record['required_hours'] = round(($employee->hours_per_month / $date->daysInMonth) * 60) . ' minutes';

                } else {


                    $record['required_hours'] = round($employee->hours_per_month / $date->daysInMonth) . ' hours';

                }


            }


            $required = $employee->timetable->where('day', $day)->first();


            if ($required) {
//                $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;
//                $total_required_hours += $required_hours;
//                $record['required_hours'] = $required_hours;

                $total_working_hours += $record['worked_hours'];
//                    total missed hours calculation
                if ($total_working_hours > $total_required_hours) {
                    $total_volunteered_hours = abs($total_working_hours - $total_required_hours);
                    $record['volunteered_hours'] = $total_volunteered_hours;
                    $total_missed_hours = 0;
                    $record['missed_hours'] = 0;

                }

                if ($total_working_hours < $total_required_hours) {
                    $total_volunteered_hours = 0;
                    $record['volunteered_hours'] = $total_volunteered_hours;
                    $total_missed_hours = round(abs($total_required_hours - $total_working_hours), 2);
                    $record['missed_hours'] = $total_missed_hours;

                }
//                        $record['required_hours'] = $total_required_hours/20;

            }

            $daily_record[$date->format('Y-m-d')] = $record;
            $date->addDay();

        }


        return [
            'employee' => $employee,
            // 'attendance' => $attendance,
            'daily_record' => $daily_record,
            'next_month' => $next_month,
            'current_month' => $current_month,
            'public_holidays' => $public_holidays,
            'weekend' => $weekend,
            'total_required_hours' => $total_required_hours,
            'total_missed_hours' => $total_missed_hours,
            'total_volunteered_hours' => $total_volunteered_hours,
            'total_working_hours' => $total_working_hours,
            'total_sick_leaves' => $total_sick_leaves
        ];
    }

    public
    function dailyReport($id, $date)
    {


        try {


            $start_date = $date;


            $current_month = Carbon::createFromFormat('Y-m-d', $start_date);


            $next_month = $current_month;

            $employee = Employee::findOrFail($id);

            /* NEW */
            $end_date = $date;


            $daily_record = [];

            $total_working_minutes = 0;
            $total_required_hours = 0;
            $total_working_hours = 0;
            $total_missed_hours = 0;
            $total_volunteered_hours = 0;


            while ($date <= $end_date) {
                $date = Carbon::parse($date);
                $day = strtolower($date->format('D'));
                $date->addDay();
                $record = [];
                $attendance_list = Attendance::whereDate('clock', $date)
                    ->orderBy('clock')
                    ->get();
                $attendance_duration = $this->calculateAttendanceDuration($attendance_list);

                $record['day'] = $day;
                $record['worked_hours'] = intdiv($attendance_duration['duration'], 60) . ':' . ($attendance_duration['duration'] % 60);
                $record['missed_hours'] = intdiv($attendance_duration['duration'], 60) . ':' . ($attendance_duration['duration'] % 60);
                $empHoursPerMonth = $employee->hours_per_month;
                $record['volunteered_hours'] = abs($empHoursPerMonth - $record['worked_hours']);
                $total_volunteered_hours = $record['volunteered_hours'];
                $record['attendance'] = $attendance_duration['attendance'];


//            if ($salary_on_this_date && $salary_on_this_date->work_mood == 'per_hour') {
                $total_required_hours = $employee->hours_per_month;

//            }

                if ($employee->timetable) {
                    $required = $employee->timetable->where('day', $day)->first();


                    if ($required) {
                        $total_working_hours += $record['worked_hours'];
                        $total_missed_hours = abs($total_required_hours - $total_working_hours);


                        if ($employee->work_mood == 4/** per_month */) {

                            $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;


                            $total_required_hours += $required_hours;

//                            $record['required_hours'] = $required_hours;
                            $record['required_hours'] = $employee->hours_per_month / $date->daysInMonth;
                            $record['missed_hours'] = $total_missed_hours;
                            $record['volunteered_hours'] = $total_volunteered_hours;
                            $record['date'] = $date->format('Y-m-d');

                        }
                    } else {
                        $record['required_hours'] = 'off';
                    }
                } elseif ($employee->work_mood == 'per_hour') {
                    $total_working_hours += $record['worked_hours'];
                    $total_missed_hours = $total_missed_hours;
                    $total_volunteered_hours = $total_volunteered_hours;
                } else {
                    $total_working_hours += $record['worked_hours'];
                    $total_missed_hours = $total_missed_hours;
                    $total_volunteered_hours = $total_volunteered_hours;
                }

                $daily_record[$date->format('Y-m-d')] = $record;
//            $date->addDay();
            }


            return [
                'employee' => $employee,
                // 'attendance' => $attendance,
                'daily_record' => $daily_record,
                'next_month' => $next_month,
                'current_month' => $current_month,
                'public_holidays' => $public_holidays,
                'weekend' => $weekend,
                'total_required_hours' => $total_required_hours,
                'total_missed_hours' => $total_missed_hours,
                'total_volunteered_hours' => $total_volunteered_hours,
                'total_working_hours' => $total_working_hours
            ];

        } catch (\Exception $exception) {
            \Log::error($exception);

            return response()->json($exception->getMessage());
        }

    }

    public
    function perDateAttendanceReport($id, $date = null)
    {

        $date = new Carbon($date);


        // temp ;to be dynamicly loaded

        $weekend = ['Sat', 'Sun'];
        $public_holidays = ['2018-01-01', '2018-01-31'];

        // end of temp


        $start_date = $date;
        $current_month = $date->format('Y-m-d');


//        $current_month = $date;
        $employee = Employee::findOrFail($id);
        /* NEW */


        $total_required_hours = 0;
        $total_working_hours = 0;


        $day = strtolower($date->format('D'));

//        $salary_on_this_date = $employee->salaryOn($date->format('Y-m-d'));


        $record = [];

        $attendance_list = Attendance::where('employee_id', $employee->id)
            ->where(DB::raw('date(clock)'), '=', $date->format('Y-m-d'))
            ->orderBy('clock')
            ->get();


        $attendance_duration = $this->calculateAttendanceDuration($attendance_list);


        $record['day'] = $day;

        $record['worked_hours'] = number_format($attendance_duration['duration'] / 60, 2);
        $record['attendance'] = $attendance_duration['attendance'];

        if ($employee->work_mood == 'per_hour') {
            $total_required_hours = $employee->hours_per_month;
        }

        if ($employee->timetable) {
            $required = $employee->timetable->where('day', $day)->first();


            if ($required) {
                $total_working_hours += $record['worked_hours'];

                if ($employee->work_mood == 4/** per_month */) {
                    // $diff = strtotime("2001-01-01 ".$required->clockin.":00")->diffInMinutes(strtotime("2001-01-01 ".$required->clockout.":00"));

                    $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;
                    $total_required_hours += $required_hours;
                    $record['required_hours'] = $required_hours;
                }

            } else {
                $record['required_hours'] = 'off';
            }
        } elseif ($employee->work_mood == 'per_hour') {
            $total_working_hours += $record['worked_hours'];
        }

        $daily_record[$date->format('Y-m-d')] = $record;
        $date->addDay();


        return $total_working_hours;

        return [
            'current_month' => $current_month,
            'total_required_hours' => $total_required_hours,
            'total_working_hours' => $total_working_hours
        ];
    }

    public
    function searche(Request $request)
    {

        if ($request->ajax()) {
            $output = '';
            $query = $request->get('query');

            if ($query != '') {
                $data = Employee::where('name', 'like', '%' . $query . '%')
                    ->orWhere('email', 'like', '%' . $query . '%')
                    ->orWhere('full_name', 'like', '%' . $query . '%')
                    ->orWhere('full_name_trans', 'like', '%' . $query . '%')
                    ->orderBy('name', 'desc')
                    ->get();


            } else {
                $data = Employee::latest()->paginate();

            }
            $total_row = $data->count();


            if ($total_row > 0) {
                foreach ($data as $item) {

                    $output .= '<tr>
            <td> ' . $item->id . '</td>
           <td> ' . $item->name . ' </td>
           <td> ' . $item->roles->implode('description', ', ') . ' </td>
           <td class="text-center" >
               <a href = "' . route('individual.employee.monthly.attendance', $item->id) . '" class="btn btn-sm btn-primary" > View</a >
           </td>
        
           </tr>
                        ';
                }
            } else {
                $output = '
          <tr>
           <td align = "center" colspan = "5" > No Data Found </td>
          </tr>
                        ';
            }


            $data = array(
                'table_data' => $output,
                'total_data' => $total_row
            );

            echo json_encode($data);
        }
    }
    public
    function daily_report_search(Request $request)
    {




        // it is not showing all the entries , for example, for the january if i have selected the monthYear to be 2025 january. let me share the data:[] of the db with you so you understand the context. right now it only returns one record while there should have been multiple as you saw in the db data records that i have shared with you.
        // Second issue is that the related Action is not shown accordingly.
        $date = Carbon::parse($request->get('employeeAttendanceDate'));
        if ((auth()->user()->hasRole(['education-manager_' . config('organization_id') . '_']))) {
            \DB::enableQueryLog();
            // 1) Apply your existing filters
            $query = Attendance::withTrashed()
                ->when($request->filled('roles'), function ($q) use ($request) {
                    $q->whereHas('employee.roles', function ($qr) use ($request) {
                        $qr->whereIn('id', $request->get('roles'));
                    });
                })
                ->when($request->filled('employeeAttendanceDate'), function ($q) use ($request) {
                    $date = $request->get('employeeAttendanceDate');
                    return $q->whereDate('clock', $date);
                })
                ->when($request->filled('monthYear'), function ($q) use ($request) {
                    $explodedYearMonth = explode('-', $request->get('monthYear'));
                    $year  = $explodedYearMonth[0];
                    $month = $explodedYearMonth[1];
                    $q->whereYear('clock', $year)
                        ->whereMonth('clock', $month);
                });

            // 2) First, get all logs in ascending chronological order for pairing
            $allAttendance = $query
//                ->orderBy('employee_id')
                ->orderBy('clock', 'asc')
                ->get();

            // 3) Group by employee to pair “in” and “out”
            $groupedByEmployee = $allAttendance->groupBy('employee_id');

            $finalRecords = collect();  // each element = ['in' => Attendance|null, 'out' => Attendance|null]
            foreach ($groupedByEmployee as $employeeId => $records) {
                // Sort chronologically so pairing is consistent
                $records = $records->sortBy('clock');

                $pendingIn = null;
                foreach ($records as $log) {
                    if ($log->type === 'in') {
                        // If we already had an unpaired 'in', push it as unpaired
                        // before overriding with a new 'in'
                        if ($pendingIn) {
                            $finalRecords->push([
                                'in'  => $pendingIn,
                                'out' => null, // no out
                            ]);
                        }
                        $pendingIn = $log;
                    } else {
                        // It's an "out"
                        if ($pendingIn && $log->clock->gte($pendingIn->clock)) {
                            // Pair it
                            $finalRecords->push([
                                'in'  => $pendingIn,
                                'out' => $log,
                            ]);
                            $pendingIn = null;
                        } else {
                            // No valid 'in' to pair with => treat as unpaired "out"
                            $finalRecords->push([
                                'in'  => null,
                                'out' => $log,
                            ]);
                        }
                    }
                }

                // If at the end we still have an unpaired 'in', push it
                if ($pendingIn) {
                    $finalRecords->push([
                        'in'  => $pendingIn,
                        'out' => null,
                    ]);
                    $pendingIn = null;
                }
            }

            // 4) Now sort *all final records* in descending order by whichever clock is present
            $finalRecords = $finalRecords->sort(function ($a, $b) {
                // If 'out' is present, we can compare out->clock; else use in->clock
                $aClock = $a['out'] ? $a['out']->clock : ($a['in'] ? $a['in']->clock : null);
                $bClock = $b['out'] ? $b['out']->clock : ($b['in'] ? $b['in']->clock : null);
                if (!$aClock || !$bClock) {
                    // fallback if something is missing
                    return 0;
                }
                // For DESC: return negative if aClock > bClock
                return $aClock->lt($bClock) ? 1 : -1;
            })->values();

            // 5) Paginate so you can use ->links()
            $page    = $request->input('page', 1);
            $perPage = 50;
            $offset  = ($page - 1) * $perPage;

            $pagedItems = $finalRecords->slice($offset, $perPage)->values();
            $paginator  = new LengthAwarePaginator(
                $pagedItems,
                $finalRecords->count(),
                $perPage,
                $page,
                [
                    'path'  => $request->url(),
                    'query' => $request->query(),
                ]
            );




            \Log::info(\DB::getQueryLog());

        } elseif (auth()->guard('employee')->user()->hasRole(['supervisor_' . config('organization_id') . '_'])
            && !auth()->guard('employee')->user()->hasRole([
                'human-resource_' . config('organization_id') . '_',
                'website-editor_' . config('organization_id') . '_',
                'managing-director_' . config('organization_id') . '_'
            ])) {


//            $attendance = Attendance::withTrashed()->whereHas('employee.teacherCenter', function ($q) use ($request) {
//                $centerIds = \DB::table('cen_emps')->where('emp_id', auth()->user()->id)->pluck('cen_emps.cen_id');
//                $supervisorCenId = $centerIds->toArray();
//
//                return $q->whereIn('cen_id', $supervisorCenId);
//            })->when($request->filled('employeeAttendanceDate'), function ($query) use ($request) {
//
//                $date = $request->exists('employeeAttendanceDate');
//                $query->whereDate("clock", $date);
//            })->when($request->filled('monthYear'), function ($query) use ($request) {
//
//                $explodedYearMonth = explode('-', $request->get('monthYear'));
//                $year = $explodedYearMonth[0];
//                $month = $explodedYearMonth[1];
//
//
//                $query->whereYear("clock", $year)->whereMonth('clock', $month);
//            })->orderByRaw('date(clock) desc, employee_id')->paginate(50);
////                ->orderBy('employee_id');
///
///



            try {
                // 1) Build the base query with teacher center filter
                $query = Attendance::withTrashed()
                    ->whereHas('employee.teacherCenter', function ($q) use ($request) {
                        // Fetch the supervisor's center IDs from the 'cen_emps' table
                        $centerIds = DB::table('cen_emps')
                            ->where('emp_id', auth()->user()->id)
                            ->pluck('cen_id');
                        $supervisorCenId = $centerIds->toArray();

                        // Filter teacher centers that belong to the supervisor
                        $q->whereIn('cen_id', $supervisorCenId);
                    })
                    ->when($request->filled('employeeAttendanceDate'), function ($q) use ($request) {
                        // Filter by a specific attendance date
                        $date = $request->get('employeeAttendanceDate');
                        return $q->whereDate('clock', $date);
                    })
                    ->when($request->filled('monthYear'), function ($q) use ($request) {
                        // Filter by month and year (expects format "YYYY-MM")
                        $explodedYearMonth = explode('-', $request->get('monthYear'));
                        $year  = $explodedYearMonth[0];
                        $month = $explodedYearMonth[1];
                        return $q->whereYear('clock', $year)
                            ->whereMonth('clock', $month);
                    });

                // 2) Retrieve all attendance logs in ascending order (for pairing)
                $allAttendance = $query->orderBy('clock', 'asc')->get();

                // 3) Group logs by employee_id and pair "in" and "out" records
                $groupedByEmployee = $allAttendance->groupBy('employee_id');
                $finalRecords = collect(); // Each element: ['in' => Attendance|null, 'out' => Attendance|null]

                foreach ($groupedByEmployee as $employeeId => $records) {
                    // Ensure logs are in chronological order
                    $records = $records->sortBy('clock');

                    $pendingIn = null;
                    foreach ($records as $log) {
                        if ($log->type === 'in') {
                            // If an unpaired 'in' already exists, push it before assigning a new one
                            if ($pendingIn) {
                                $finalRecords->push([
                                    'in'  => $pendingIn,
                                    'out' => null, // unpaired
                                ]);
                            }
                            $pendingIn = $log;
                        } else { // $log->type === 'out'
                            if ($pendingIn && $log->clock->gte($pendingIn->clock)) {
                                // Valid pair: pair the pending 'in' with the current 'out'
                                $finalRecords->push([
                                    'in'  => $pendingIn,
                                    'out' => $log,
                                ]);
                                $pendingIn = null;
                            } else {
                                // Unpaired 'out' record
                                $finalRecords->push([
                                    'in'  => null,
                                    'out' => $log,
                                ]);
                            }
                        }
                    }
                    // If an unpaired 'in' remains, add it as unpaired
                    if ($pendingIn) {
                        $finalRecords->push([
                            'in'  => $pendingIn,
                            'out' => null,
                        ]);
                    }
                }

                // 4) Sort the final records in descending order using the available clock (out preferred)
                $finalRecords = $finalRecords->sort(function ($a, $b) {
                    $aClock = $a['out'] ? $a['out']->clock : ($a['in'] ? $a['in']->clock : null);
                    $bClock = $b['out'] ? $b['out']->clock : ($b['in'] ? $b['in']->clock : null);
                    if (!$aClock || !$bClock) {
                        return 0;
                    }
                    // For descending order: if aClock is less than bClock, return 1; else -1
                    return $aClock->lt($bClock) ? 1 : -1;
                })->values();

                // 5) Manual pagination (using LengthAwarePaginator)
                $page    = $request->input('page', 1);
                $perPage = 50;
                $offset  = ($page - 1) * $perPage;
                $pagedItems = $finalRecords->slice($offset, $perPage)->values();

                $paginator = new LengthAwarePaginator(
                    $pagedItems,
                    $finalRecords->count(),
                    $perPage,
                    $page,
                    [
                        'path'  => $request->url(),
                        'query' => $request->query(),
                    ]
                );
            } catch (\Exception $e) {
                \Log::error('Error retrieving attendance records: ' . $e->getMessage(), [
                    'exception' => $e,
                    'request'   => $request->all(),
                ]);

                return response()->json([
                    'message' => 'An error occurred while fetching attendance records.',
                ], 500);
            }





        }
        elseif ((auth()->user()->hasRole(['education-manager_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {


//            $attendance = Attendance::withTrashed()->whereHas('employee.roles', function ($q) use ($request) {
//                $supervisorAndTeachersRolesIds = \DB::table('roles')->whereIn('description', ['Supervisor', 'Teacher'])->pluck('id');
//                $supervisorAndTeachersRolesIds = $supervisorAndTeachersRolesIds->toArray();
//
//                return $q->whereIn('cen_id', $supervisorAndTeachersRolesIds);
//            })->when($request->filled('employeeAttendanceDate'), function ($query) use ($request) {
//
//                $date = $request->exists('employeeAttendanceDate');
//                $query->whereDate("clock", $date);
//            })->when($request->filled('monthYear'), function ($query) use ($request) {
//
//                $explodedYearMonth = explode('-', $request->get('monthYear'));
//                $year = $explodedYearMonth[0];
//                $month = $explodedYearMonth[1];
//
//
//                $query->whereYear("clock", $year)->whereMonth('clock', $month);
//            })->orderByRaw('date(clock) desc, employee_id')->paginate(50);

            try {
                // 1) Build the base query with roles filter for "Supervisor" and "Teacher"
                $query = Attendance::withTrashed()
                    ->whereHas('employee.roles', function ($q) {
                        // Retrieve the role IDs for "Supervisor" and "Teacher"
                        $supervisorAndTeachersRolesIds = DB::table('roles')
                            ->whereIn('description', ['Supervisor', 'Teacher'])
                            ->pluck('id')
                            ->toArray();

                        // Filter the employee's roles to include only these two
                        $q->whereIn('id', $supervisorAndTeachersRolesIds);
                    })
                    ->when($request->filled('employeeAttendanceDate'), function ($q) use ($request) {
                        // Filter by a specific attendance date
                        $date = $request->get('employeeAttendanceDate');
                        return $q->whereDate('clock', $date);
                    })
                    ->when($request->filled('monthYear'), function ($q) use ($request) {
                        // Filter by month and year (expects format "YYYY-MM")
                        $explodedYearMonth = explode('-', $request->get('monthYear'));
                        $year  = $explodedYearMonth[0];
                        $month = $explodedYearMonth[1];
                        return $q->whereYear('clock', $year)
                            ->whereMonth('clock', $month);
                    });

                // 2) Retrieve all attendance logs in ascending order (for pairing)
                $allAttendance = $query->orderBy('clock', 'asc')->get();

                // 3) Group logs by employee_id and pair "in" and "out" records
                $groupedByEmployee = $allAttendance->groupBy('employee_id');
                $finalRecords = collect(); // Each element: ['in' => Attendance|null, 'out' => Attendance|null]

                foreach ($groupedByEmployee as $employeeId => $records) {
                    // Ensure logs are in chronological order
                    $records = $records->sortBy('clock');

                    $pendingIn = null;
                    foreach ($records as $log) {
                        if ($log->type === 'in') {
                            // If an unpaired 'in' already exists, push it before assigning a new one
                            if ($pendingIn) {
                                $finalRecords->push([
                                    'in'  => $pendingIn,
                                    'out' => null, // unpaired
                                ]);
                            }
                            $pendingIn = $log;
                        } else { // $log->type === 'out'
                            if ($pendingIn && $log->clock->gte($pendingIn->clock)) {
                                // Valid pair: pair the pending 'in' with the current 'out'
                                $finalRecords->push([
                                    'in'  => $pendingIn,
                                    'out' => $log,
                                ]);
                                $pendingIn = null;
                            } else {
                                // Unpaired 'out' record
                                $finalRecords->push([
                                    'in'  => null,
                                    'out' => $log,
                                ]);
                            }
                        }
                    }

                    // If an unpaired 'in' remains, add it as unpaired
                    if ($pendingIn) {
                        $finalRecords->push([
                            'in'  => $pendingIn,
                            'out' => null,
                        ]);
                    }
                }

                // 4) Sort the final records in descending order using whichever clock is present (prefer 'out')
                $finalRecords = $finalRecords->sort(function ($a, $b) {
                    $aClock = $a['out'] ? $a['out']->clock : ($a['in'] ? $a['in']->clock : null);
                    $bClock = $b['out'] ? $b['out']->clock : ($b['in'] ? $b['in']->clock : null);

                    // If either is missing a clock, treat them as equal for sorting
                    if (!$aClock || !$bClock) {
                        return 0;
                    }
                    // For descending order: if aClock < bClock, return 1; else -1
                    return $aClock->lt($bClock) ? 1 : -1;
                })->values();

                // 5) Manual pagination (using LengthAwarePaginator)
                $page    = $request->input('page', 1);
                $perPage = 50;
                $offset  = ($page - 1) * $perPage;
                $pagedItems = $finalRecords->slice($offset, $perPage)->values();

                $paginator = new LengthAwarePaginator(
                    $pagedItems,
                    $finalRecords->count(),
                    $perPage,
                    $page,
                    [
                        'path'  => $request->url(),
                        'query' => $request->query(),
                    ]
                );


            } catch (\Exception $e) {
                // Log the error and return a 500 response
                Log::error('Error retrieving attendance records: ' . $e->getMessage(), [
                    'exception' => $e,
                    'request'   => $request->all(),
                ]);

                return response()->json([
                    'message' => 'An error occurred while fetching attendance records.',
                ], 500);
            }



        }
        else {



//            $attendance = Attendance::withTrashed()->when($request->filled("roles"), function ($query) use ($request, $date) {
//
//                $query->whereHas('employee.roles', function ($q) use ($request) {
//                    return $q->whereIn('roles.id', $request->get('roles'));
//                });
//
//
//            })
//                ->when($request->filled('monthYear'), function ($query) use ($request) {
//
//                    $explodedYearMonth = explode('-', $request->get('monthYear'));
//                    $year = $explodedYearMonth[0];
//                    $month = $explodedYearMonth[1];
//
//                    $query->whereYear("clock", $year)->whereMonth('clock', $month);
//                })
//                ->when($request->filled('employeeAttendanceDate'), function ($query) use ($request, $date) {
//
//
//                    $query->whereDate("clock", $date);
//                })
//                ->orderByRaw('date(clock) desc, employee_id')->paginate(50);

            try {
                // Pre-calculate the attendance date if provided
                $date = $request->get('employeeAttendanceDate');

                // 1) Build the base query with roles filter
                $query = Attendance::withTrashed()
                    ->when($request->filled('roles'), function ($q) use ($request) {
                        // Filter by employee roles based on provided role IDs
                        $q->whereHas('employee.roles', function ($q2) use ($request) {
                            return $q2->whereIn('roles.id', $request->get('roles'));
                        });
                    })
                    ->when($request->filled('employeeAttendanceDate'), function ($q) use ($request, $date) {
                        // Filter by a specific attendance date
                        return $q->whereDate('clock', $date);
                    })
                    ->when($request->filled('monthYear'), function ($q) use ($request) {
                        // Filter by month and year (expects format "YYYY-MM")
                        $explodedYearMonth = explode('-', $request->get('monthYear'));
                        $year  = $explodedYearMonth[0];
                        $month = $explodedYearMonth[1];
                        return $q->whereYear('clock', $year)
                            ->whereMonth('clock', $month);
                    });

                // 2) Retrieve all attendance logs in ascending order (for pairing)
                $allAttendance = $query->orderBy('clock', 'asc')->get();

                // 3) Group logs by employee_id and pair "in" and "out" records
                $groupedByEmployee = $allAttendance->groupBy('employee_id');
                $finalRecords = collect(); // Each element: ['in' => Attendance|null, 'out' => Attendance|null]

                foreach ($groupedByEmployee as $employeeId => $records) {
                    // Ensure logs are in chronological order
                    $records = $records->sortBy('clock');

                    $pendingIn = null;
                    foreach ($records as $log) {
                        if ($log->type === 'in') {
                            // If an unpaired 'in' already exists, push it before assigning a new one
                            if ($pendingIn) {
                                $finalRecords->push([
                                    'in'  => $pendingIn,
                                    'out' => null,
                                ]);
                            }
                            $pendingIn = $log;
                        } else { // $log->type === 'out'
                            if ($pendingIn && $log->clock->gte($pendingIn->clock)) {
                                // Valid pair: pair the pending 'in' with the current 'out'
                                $finalRecords->push([
                                    'in'  => $pendingIn,
                                    'out' => $log,
                                ]);
                                $pendingIn = null;
                            } else {
                                // Unpaired 'out' record
                                $finalRecords->push([
                                    'in'  => null,
                                    'out' => $log,
                                ]);
                            }
                        }
                    }

                    // If an unpaired 'in' remains, add it as unpaired
                    if ($pendingIn) {
                        $finalRecords->push([
                            'in'  => $pendingIn,
                            'out' => null,
                        ]);
                    }
                }

                // 4) Sort the final records in descending order using whichever clock is present (prefer 'out')
                $finalRecords = $finalRecords->sort(function ($a, $b) {
                    $aClock = $a['out'] ? $a['out']->clock : ($a['in'] ? $a['in']->clock : null);
                    $bClock = $b['out'] ? $b['out']->clock : ($b['in'] ? $b['in']->clock : null);
                    if (!$aClock || !$bClock) {
                        return 0;
                    }
                    // For descending order: if aClock is less than bClock, return 1; else -1
                    return $aClock->lt($bClock) ? 1 : -1;
                })->values();

                // 5) Manual pagination (using LengthAwarePaginator)
                $page    = $request->input('page', 1);
                $perPage = 50;
                $offset  = ($page - 1) * $perPage;
                $pagedItems = $finalRecords->slice($offset, $perPage)->values();

                $paginator = new LengthAwarePaginator(
                    $pagedItems,
                    $finalRecords->count(),
                    $perPage,
                    $page,
                    [
                        'path'  => $request->url(),
                        'query' => $request->query(),
                    ]
                );
            } catch (\Exception $e) {
                Log::error('Error retrieving attendance records: ' . $e->getMessage(), [
                    'exception' => $e,
                    'request'   => $request->all(),
                ]);

                return response()->json([
                    'message' => 'An error occurred while fetching attendance records.',
                ], 500);
            }




        }
        $monthYear = $request->get('monthYear');
        $employeeAttendanceDate = $request->get('employeeAttendanceDate');
        $yearMonth = '';
        if ($request->filled('monthYear')) {

            $yearMonth = preg_split("/-/", $monthYear)[0] . '-' . preg_split("/-/", $monthYear)[1];
        }
        if ($request->filled('employeeAttendanceDate')) {

            $yearMonth = preg_split("/-/", $employeeAttendanceDate)[0] . '-' . preg_split("/-/", $employeeAttendanceDate)[1];
        }


//        $monthYear = preg_split("/-/", $monthYear)[1].'-'.preg_split("/-/", $monthYear)[0];

        return view(
            'humanresource::attendance.daily_report',
            [
                'pairedAttendance'      => $paginator,
                'employeeAttendanceDate' => $request->get('employeeAttendanceDate'),
                'monthYear'             => $request->get('monthYear'),
                'role'                  => $request->get('roles'),
                'date'                  => $request->get('employeeAttendanceDate'),
                'year'                  => $request->filled('monthYear')
                    ? explode('-', $request->get('monthYear'))[0]
                    : null,
                'month'                 => $request->filled('monthYear')
                    ? explode('-', $request->get('monthYear'))[1]
                    : null,
                'yearMonth'             => $request->get('monthYear'),
                // plus other variables you need (supervisorsList, etc.)
            ]
        );
    }

    public
    function monthly_report(Request $request, $date = null)
    {


        // to be refactored

        $employee = Employee::all();
        foreach ($employee as $item) {
            $report = $this->report($item->id, $date);
            extract($report);
            $items[] = $report;
        }


        $role = Role::get();
        $attendance = Attendance:: whereYear('created_at', '=', date('Y'))
            ->whereMonth('created_at', '=', date('m'))->orderBy('employee_id')
            ->get();

        //  $attendance =Attendance::whereDate('created_at', date('Y - m'))->orderBy('employee_id')->get();
        return view(
            'humanresource::attendance.monthly_report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'role',
                'date',
                'items'
            )
        );
    }

    public
    function monthly_report_search(Request $request)
    {

        $date = null;
        $id = $request->role;
        if ($id == '0') {
            return $this->monthly_report($request);
        }


        $employee = Employee::whereHas('roles', function ($q) use ($id) {

            return $q->where('id', $id);
        })->get();


        $role = Role::get();
        foreach ($employee as $item) {
            $report = $this->report($item->id, $date);
            extract($report);
            $items[] = $report;
        }


        $attendance = Attendance:: whereYear('created_at', '=', date('Y'))
            ->whereMonth('created_at', '=', date('m'))->orderBy('employee_id')
            ->get();


        return view(
            'humanresource::attendance.monthly_report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'role',
                'date',
                'items'
            )
        );


    }

    public
    function addNoteToAtttendance(Request $request)
    {

        try {


            $validationmessages = [
                'note.max' => 'The :attribute is more than 500 characters. Please limit to 500 characters.',
            ];
            $validation = \Validator::make($request->all(), [
                'note' => 'required|max:500'
            ], $validationmessages);


            $attendance = Attendance::where("id", $request->clockId)->update(['note' => $request->note]);

//        } catch(\Illuminate\Database\QueryException $ex){
        } catch (\Exception $ex) {
            return response()->json($ex->getMessage(), 500);


            // Note any method of class PDOException can be called on $ex.
        }
        return response()->json("note added to the attendance", 200);


    }

    public
    function updateAttendanceEntryPair(Request $request)
    {


        try {

            if (!auth()->user()->can('update attendance')) {
                return response()->json(['error' => 'not_autorized'], 300);
            }


            if (!$request->exists('in') && !$request->exists('out')) {
                return response()->json('Please complete the details', 422);
            }


            $inTime = Carbon::parse($request->get('in'));
            $inNote = $request->get('inNote');
            $outNote = $request->get('outNote');

            $outTime = Carbon::parse($request->get('out'));

//            $outTimeGreater = $outTime->gt($inTime);
//            $outTimeEqIn = $outTime->equalTo($inTime);
//            $InTimeEqOut = $inTime->equalTo($outTime);
//
//
//            if ($outTimeEqIn == true OR $InTimeEqOut == true) {
//
//                return response()->json('The OUT time can not be equal to the IN time', 422);
//            }
//            if ($outTimeGreater == false) {
//                return response()->json('The OUT time can not be less than the IN time . Please select greater time for the OUT field', 422);
//            }


            $agent = new Agent();
            $inAttendance = Attendance::updateOrInsert(['id' => $request->inId], [
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),

                'type' => 'in',
                'employee_id' => $request->employee_id,
                'clock' => $inTime,
                'note' => $inNote,

                'organization_id' => \Config::get('organization_id'),
                'created_by' => auth()->user()->id,
                'updated_by' => auth()->user()->id,
            ]);

            $outAttendance = Attendance::updateOrInsert(['id' => $request->outId], [
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),
                'type' => 'out',
                'employee_id' => $request->employee_id,
                'clock' => $outTime,
                'note' => $outNote,
                'created_by' => auth()->user()->id,
                'organization_id' => \Config::get('organization_id'),
                'updated_by' => auth()->user()->id,
            ]);

//        } catch(\Illuminate\Database\QueryException $ex){
        } catch (\Exception $ex) {
            return response()->json($ex->getMessage(), 500);


            // Note any method of class PDOException can be called on $ex.
        }
        return response()->json("note added to the attendance", 200);


    }

    public
    function getAttendanceNote($id)
    {


        $note = Attendance::where("id", $id)->first()->note;


        return response()->json($note);

    }

    public
    function getAttendancePair($employeeId, $id, $type)
    {

        $result = [];
        if ($type == 'in') {
            try {
                $attDate = Attendance::where("id", $id)->select(DB::raw("date(clock) as date"))->first()->date;
                $nextRow = Attendance::where("employee_id", $employeeId)->where("id", '>', $id)->where(\DB::raw("date(clock)"), $attDate)->select(DB::raw('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note'))->first();


                if (is_null($nextRow)) {

                    $nextDate = new Carbon($nextRow->date);
                    $currentRow = Attendance::where("id", $id)->select(\DB::raw('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note'))->first();
                    $currentDate = new Carbon($currentRow->date);
                    $currentTime = new Carbon($currentRow->time);

                    if ($nextDate->gt($currentDate)) {

                        $attendance = Attendance::where("id", $id)->select(\DB::raw('time(clock) attTime,clock,type,employee_id,id,note'))->first()->toArray();
                        $systemGeneratedRecord = ['attTime' => $currentTime->addSeconds(44)->toTimeString(), 'clock' => $currentDate->toDateString() . ' ' . $currentTime->addSeconds(44)->toTimeString(), 'employee_id' => $currentRow->employee_id, 'id' => '', 'note' => '', 'type' => 'out'];

                        $result[] = $attendance;
                        $result[] = $systemGeneratedRecord;


                    }
                } else {
                    $result = Attendance::where(\DB::raw("date(clock)"), $attDate)->whereIn("id", [$id, $nextRow->id])->select(\DB::raw('time(clock) attTime,time(clock) as time,clock,type,employee_id,id,note'))->get();


                }
            } catch (\Exception $e) {
                return response()->json([
                    'error' => $e->getMessage(),
                ], 500);
            }


        } else {


            $attDate = Attendance::where("id", $id)->select(\DB::raw("date(clock) as date"))->first()->date;


            $previous = Attendance::where("employee_id", $employeeId)->where("id", '<', $id)->where(\DB::raw("date(clock)"), $attDate)->select(\DB::raw('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note'))->first();


            // if there is no other record before this record for the $attDate
            if (is_null($previous)) {


                $currentRow = Attendance::where("employee_id", $employeeId)->where("id", $id)->select(\DB::raw('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note'))->first();
                $currentDate = new Carbon($currentRow->date);
                $currentTime = new Carbon($currentRow->time);


                $attendance = Attendance::where("id", $id)->select(\DB::raw('time(clock) attTime,clock,type,employee_id,id,note'))->first()->toArray();
                $systemGeneratedRecord = ['attTime' => $currentTime->subSeconds(120)->toTimeString(), 'clock' => $currentDate->toDateString() . ' ' . $currentTime->subSeconds(44)->toTimeString(), 'employee_id' => $currentRow->employee_id, 'id' => '', 'note' => 'This is temporary system generated note and time, please proceed to edit the details here for the Punch in', 'type' => 'in'];

                $result[] = $attendance;
                $result[] = $systemGeneratedRecord;

            } else {
                $result = Attendance::where(\DB::raw("date(clock)"), $attDate)->whereIn("id", [$id, --$id])->select(\DB::raw('time(clock) attTime,time(clock) as time,clock,type,employee_id,id,note'))->get();


            }


            // original record before the edit
//            $result = Attendance::whereIn("id", [$id,])->select(\DB::raw('time(clock) attTime,clock,time(clock) as time, type,employee_id,id,note'))->get();

        }


        return response()->json($result);

    }


}

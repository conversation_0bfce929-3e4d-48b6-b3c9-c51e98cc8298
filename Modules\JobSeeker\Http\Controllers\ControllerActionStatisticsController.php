<?php

namespace Modules\JobSeeker\Http\Controllers;

use App\Department;
use App\ExecutionTime;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
// use App\Message;
use App\Http\Requests;

class ControllerActionStatisticsController extends Controller
{

    public function index()
    {
        // Calculate the average execution time for all requests
        $averageTime = ExecutionTime::avg('time');

        // Calculate the average execution time for each controller action
        $averageTimesByAction = ExecutionTime::select('controller', 'action', DB::raw('AVG(time) as avg_time'))
            ->groupBy('controller', 'action')
            ->get();

        // Calculate the number of requests for each route
        $requestCountsByRoute = ExecutionTime::select('route', DB::raw('COUNT(*) as count'))
            ->groupBy('route')
            ->get();

        // Find the slowest request
        $slowestRequest = ExecutionTime::orderByDesc('time')->first();

        return view('generall::dashboards.controller_action_statistics', [
            'averageTime' => $averageTime,
            'averageTimesByAction' => $averageTimesByAction,
            'requestCountsByRoute' => $requestCountsByRoute,
            'slowestRequest' => $slowestRequest,
        ]);
    }
}

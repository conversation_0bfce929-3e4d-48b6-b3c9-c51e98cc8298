<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;

class FeesAssign extends Model
{
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);


    }
    public function feesGroupMaster(){
    	return $this->belongsTo('App\FeesMaster', 'fees_master_id', 'id');
    }

    public function feesPayments(){
    	return $this->hasMany('App\FeesPayment', 'id', 'fees_type_id');
    }

    public static function discountSum($student_id, $type_id, $perpose){
        try {
            $sum = FeesPayment::where('student_id', $student_id)->where('fees_type_id', $type_id)->sum($perpose);
                return $sum;
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    }

    public static function createdBy($student_id, $discount_id){
    	
        try {
            $created_by = FeesPayment::where('student_id', $student_id)->where('fees_discount_id', $discount_id)->first();
                return $created_by;
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    }

    public static function feesPayment($type_id, $student_id){
        try {
            $payments = FeesPayment::where('fees_type_id', $type_id)->where('student_id', $student_id)->get();
                return $payments;
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    }

    public function studentInfo(){
        return $this->belongsTo('App\Student', 'student_id', 'id');
    }
}

<?php

namespace Modules\EducationalReports\Http\Controllers;


use App\EvaluationSchemaOption;
use App\MoshafSurah;
use App\StudentHefzReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;


class EducationalStudentReportsDatatablesController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function studentRecords(Request $request)
    {




        DB::connection()->enableQueryLog();

        if ($request->filled('studentId') || $request->filled('classDate')) {

            $planYearMonth = Carbon::parse($request->get('classDate'))->format('Y-m');


            $dateMonthArray =
            $year = Carbon::parse($request->get('classDate'))->year;
            $month = Carbon::parse($request->get('classDate'))->month;


//            $studentHefzReport = StudentHefzReport::where('student_id', $request->get('studentId'))
//                ->whereYear('created_at', $year)
//                ->whereMonth('created_at', $month)
//                ->with('hefzPlan')
//                ->get();

            if (app()->environment('production')) {

                $studentHefzReport = \Cache::tags('StudentHefzReport')->remember("studentHefzReport-{$request->get('studentId')}-{$year}-{$month}", 60, function () use ($request, $year, $month) {
                    return StudentHefzReport::where('student_id', $request->get('studentId'))
                        ->whereYear('created_at', $year)
                        ->whereMonth('created_at', $month)
                        ->with('hefzPlan')
                        ->orderBy('created_at', 'asc')  // Sort by 'created_at' in ascending order
                        ->get();
                });

            } else {
                $studentHefzReport = StudentHefzReport::where('student_id', $request->get('studentId'))
                    ->whereYear('created_at', $year)
                    ->whereMonth('created_at', $month)
                    ->with('hefzPlan')
                    ->orderBy('created_at', 'asc')  // Sort by 'created_at' in ascending order
                    ->get();
            }


            $surats = \Cache::remember('surats', 60, function () {
                return MoshafSurah::all();
            });

            return \Yajra\DataTables\DataTables::of($studentHefzReport)
                ->addIndexColumn()
                ->addColumn('day', function ($reportDetails) use ($request) {

                    $day = $reportDetails->created_at->format('l');
                    $shortDay = substr($day, 0, 3);// this will return the first three letters of the $day. 0 is the starting point in the string and 3 represent the number of chars to show/extract from the string after 0 offset

                    return '<span title="' . $shortDay . '" style="color: #b4eeb0;">' . $shortDay . '</span>';

                    return $shortDay;


                })
                ->addColumn('date', function ($reportDetails) use ($request) {


                    return '<span style="color: #b4eeb0;">' . $reportDetails->created_at->format('d/m/Y') . '</span>';


                    return $reportDetails->created_at->format('d/m/Y');


                })
                ->addColumn('from_surat_and_ayat', function ($reportDetails) use ($request, $surats) {


                    foreach ($surats as $key => $surat) {
                        if ($reportDetails->hefz_from_surat == $surat->id) {

                            return  '<span style="color: #b4eeb0;">' . $surat->eng_name . ': ' . $reportDetails->hefz_from_ayat. '</span> <span style="color: #1fff0f; font-weight: bolder">(' . $surat->id. ': ' . $reportDetails->hefz_from_ayat . ')</span>';

                        }
                    }



                })
                ->addColumn('to_surat_and_ayat', function ($reportDetails) use ($request, $surats) {

                    foreach ($surats as $key => $surat) {
                        if ($reportDetails->hefz_to_surat == $surat->id) {
                            return  '<span style="color: #b4eeb0;">' . $surat->eng_name . ': ' . $reportDetails->hefz_to_ayat. '</span> <span style="color: #1fff0f; font-weight: bolder">(' . $surat->id. ': ' . $reportDetails->hefz_to_ayat . ')</span>';

                        }
                    }


                })
                ->addColumn('memorizedPages', function ($reportDetails) use ($request) {

                    $numberofPagesSum = 0;
                    if ($reportDetails->count() > 0) {




                        $hefzPlan = $reportDetails->hefzPlan;
                        $hefzPlan = $hefzPlan[0];
                        if ($hefzPlan->study_direction == 'backward') {

                            $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                $reportDetails->hefz_from_surat,
                                $reportDetails->hefz_from_ayat,
                                $reportDetails->hefz_to_surat,
                                $reportDetails->hefz_to_ayat
                            ]);


                            $numberofPagesSum = $numberofPages[0]->numberofPagesSum;
                        }
                        else {

                            // Call the CountMemorizedNumberofPagesForward stored procedure with the provided input parameters
                            // and store the output parameter in a user-defined variable named @number_of_pages_sum
                            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                $reportDetails->hefz_from_surat,
                                $reportDetails->hefz_from_ayat,
                                $reportDetails->hefz_to_surat,
                                $reportDetails->hefz_to_ayat
                            ]);

                            // Retrieve the value of the @number_of_pages_sum user-defined variable and store it in the $results variable
                            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                            // Access the first element of the $results array and retrieve the value of the number_of_pages_sum property
                            // Store this value in the $plannedNumberofPages variable
                            $numberofPagesSum = $results[0]->number_of_pages_sum;
                        }

                    }
                    return '<h2 style="color: #1fff0f;
    font-weight: bolder;
    font-size: 24px;">' . $numberofPagesSum . '</h2>';
                    return $numberofPagesSum;
                })

                ->addColumn('grade', function ($reportDetails) use ($request) {
                    $evaluationTitle = EvaluationSchemaOption::where('id', $reportDetails->hefz_evaluation_id)->first()->title;
                    return '<span style="color: #b4eeb0;">' . $evaluationTitle . '</span>';
                    return $evaluationTitle;


                })
                ->addColumn('attendance', function ($reportDetails) use ($request) {

                    // Check the attendance status based on the attendance_id
                    switch ($reportDetails->attendance_id) {
                        case 1:  // Late
                        case 2:  // On Time
                            // If attendance_id is 1 (Late) or 2 (On Time), return 'Y'
                            return '<span style="color: #b4eeb0;">Y</span>';

                        case 3:  // Absent
                        case 4:  // Excused
                            // If attendance_id is 3 (Absent) or 4 (Excused), return 'N'
                            return '<span style="color: #e74c3c;">N</span>';

                        default:
                            // Default case for Not Applicable or unknown attendance_id
                            return '<span style="color: #b4eeb0;">N/A</span>';
                    }




                })
                ->rawColumns(['from_surat_and_ayat','to_surat_and_ayat','attendance','grade','date','day','DT_RowIndex','memorizedPages'])
                ->make(true);

        }

        dd('only ajax requests are allowed');


    }


}

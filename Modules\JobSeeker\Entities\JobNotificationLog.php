<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

/**
 * JobNotificationLog Model
 * 
 * Tracks sent job notifications to ensure idempotency and prevent duplicate notifications.
 * This model supports Task 3: Ensure Idempotency in Notification Jobs
 * 
 * @property int $id
 * @property int $job_notification_setup_id
 * @property int $job_id
 * @property Carbon $sent_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * 
 * @property-read JobNotificationSetup $setup
 * @property-read \Modules\Jobs\Entities\Job $job
 */
final class JobNotificationLog extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'job_notification_log';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'job_notification_setup_id',
        'job_id',
        'sent_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'job_notification_setup_id' => 'integer',
        'job_id' => 'integer',
        'sent_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the notification setup that this log entry belongs to.
     *
     * @return BelongsTo
     */
    public function setup(): BelongsTo
    {
        return $this->belongsTo(JobNotificationSetup::class, 'job_notification_setup_id');
    }

    /**
     * Get the job that this notification was sent for.
     *
     * @return BelongsTo
     */
    public function job(): BelongsTo
    {
        return $this->belongsTo(\Modules\Jobs\Entities\Job::class, 'job_id');
    }

    /**
     * Check if a notification has already been sent for a specific setup and job combination.
     *
     * @param int $setupId
     * @param int $jobId
     * @return bool
     */
    public static function hasBeenSent(int $setupId, int $jobId): bool
    {
        return self::where('job_notification_setup_id', $setupId)
            ->where('job_id', $jobId)
            ->exists();
    }

    /**
     * Log a sent notification to ensure idempotency.
     *
     * @param int $setupId
     * @param int $jobId
     * @return self
     */
    public static function logSentNotification(int $setupId, int $jobId): self
    {
        return self::create([
            'job_notification_setup_id' => $setupId,
            'job_id' => $jobId,
            'sent_at' => now(),
        ]);
    }

    /**
     * Get the count of notifications sent for a specific setup.
     *
     * @param int $setupId
     * @return int
     */
    public static function getNotificationCountForSetup(int $setupId): int
    {
        return self::where('job_notification_setup_id', $setupId)->count();
    }

    /**
     * Clean up old log entries (optional maintenance method).
     *
     * @param int $daysToKeep
     * @return int Number of deleted records
     */
    public static function cleanupOldLogs(int $daysToKeep = 90): int
    {
        // ** Task 8 Fix: Add error handling for database operations **
        try {
            $cutoffDate = now()->subDays($daysToKeep);
            
            $deletedCount = self::where('sent_at', '<', $cutoffDate)->delete();

            \Illuminate\Support\Facades\Log::info('Successfully cleaned up old job notification logs.', [
                'deleted_count' => $deletedCount,
                'days_kept' => $daysToKeep,
            ]);

            return $deletedCount;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to clean up old job notification logs.', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Return 0 to indicate that no records were deleted due to an error
            return 0;
        }
    }
} 
<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Admission;
use App\ApiBaseMethod;
use App\Attendance;
use App\BankAccount;
use App\BankAccountType;
use App\BaseSetup;
use App\Center;
use App\CenterEmployee;
use App\CenterTeacher;
use App\CenterTranslation;
use App\Classes;
use App\ClassTeacher;
use App\GeneralSettings;
use App\LeaveRequest;
use App\LeaveType;
use App\MissedClockOut;
use App\Notifications\UserStatusChangedToNewApplication;
use App\Notifications\WelcomeMailtoNewEmployeeNotification;
use App\PublicHoliday;
use App\Role;
use App\Employee;

use App\Student;
use App\Document;
use App\Traits\Notification;
use App\User;
use App\WeekDay;
use App\Weekend;
use App\YearCheck;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use DB;
use App\Permission;
use App\EmployeeSalary;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\EmployeeTimetable;
use App\Http\Controllers\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Modules\Admission\Notifications\StudentOfffered;
use Modules\HumanResource\Http\Requests\EmployeeStoreRequest;
use Modules\HumanResource\Http\Requests\EmployeeUpdateRequest;
use Modules\UserActivityLog\Traits\LogActivity;
use PhpOffice\PhpSpreadsheet\Calculation\DateTimeExcel\WorkDay;
use function PHPUnit\Framework\isNull;


class GenderBasedMissedClockoutEmployeesController extends Controller
{

    use Notification;

    /**
     * @var Attendance
     */
    private $attendanceModal;

    public function __construct(Attendance $attendance)
    {

        $this->attendanceModal = $attendance;


    }
    function getGenderBasedEmployees(Request $request)
    {



        $results = [];

            $query =  \DB::select('select
                                            distinct(e.name),
                                            e.id
                                        from
                                            missed_clockouts mc ,
                                            employees e
                                        where
                                            e.id = mc.employee_id
                                            and e.deleted_at is null
                                        and e.gender = ? 

order by e.name
                                            ',[$request->q])     ;


            $query = collect($query)->map(function($value, $key){

            $results['name'] = $value->name;
            $results['value'] = $value->id;
            $results['text'] = $value->name;
                return $results;

            });




            return response()->json(["success" => true,'results' => $query], 200);




        return response()->json(["success" => true,'results' => $leaveTypes], 200);

    }
    public
    function saveUploadDocument(Request $request)
    {

        try {
            if ($request->file('staff_upload_document') != "" && $request->title != "") {
                $document_photo = "";
                if ($request->file('staff_upload_document') != "") {
                    $file = $request->file('staff_upload_document');
//                    $document_photo = 'staff-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                    $document_photo = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                    $file->move('uploads/staff/document/', $document_photo);
                    $document_photo = 'public/uploads/staff/document/' . $document_photo;
                }

                $document = new Document();
                $document->title = $request->title;
                $document->documentable_id = $request->employee_id;
                $document->type = 'stf';
                $document->file = $document_photo;
                $document->created_by = Auth()->user()->id;
                $document->organization_id = config('organization_id');
                $results = $document->save();
            }

            if ($results) {
                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            \Log::error($e);
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public
    function show(Request $request, Employee $employee)
    {





        $employee = $employee->loadMissing(['teacherCenter', 'classes', 'bankAccounts.bank', 'bankAccounts.bankAccountType']);

        // decision made on this date that : any new employeee after this date should have only mon-friday working
        if ($employee->created_at < Carbon::parse('2020-12-01')) {
            $newWorkTimeTable = 0;
        } else {
            $newWorkTimeTable = 1;
        }

        $todayAttendance = $this->attendanceModal->dailyAttendanceReport($employee);
        $weeklyAttendance = $this->attendanceModal->WeeklyAttendanceReport($employee);
        $monthlyAttendance = $this->attendanceModal->monthlyAttendanceReport($employee);

        $employeeDocumentsDetails = Document::where('documentable_id', $employee->id)->where('type', '=', 'stf')->get();
        $empAttMonths = DB::select("SELECT DISTINCT (MONTH(clock)) AS months FROM attendances WHERE employee_id = ? ORDER BY months ASC", [$id]);
        $employeeAttYears = DB::select("SELECT DISTINCT (year(clock)) AS years FROM attendances WHERE employee_id = ? ORDER BY years ASC", [$id]);
        $roles = Role::cursor()->pluck('description', 'name');
//        $classes = Classes::where('center_id', $employee->teacherCenter()->first()->id)->get()->pluck('name', 'id');
        $classes = Classes::all()->pluck('name', 'id');
        $bankAccTypes = BankAccountType::all()->pluck('name', 'id');


        $timetable = EmployeeTimetable::where('employee_id', $employee->id)->orderBy('day_order', 'asc')->pluck('day', 'id');
        $days = EmployeeTimetable::where('employee_id', $employee->id)->orderBy('day_order', 'asc')->get();
        $workDays = WeekDay::pluck('name', 'slug');


        $centers = CenterTranslation::cursor()->where('locale', 'en')->pluck('name', 'name');
        $centers = \App\Center::all()->pluck('location', 'id')->sortBy('location')->prepend('Select Sup Center...', "");


        $employeeContainsSupervisorRole = $employee->roles->contains(function ($item, $key) {
            return $item->name == 'supervisor_2_';

        });
        $employeeContainsTeacherRole = $employee->roles->contains(function ($item, $key) {
            return $item->name == 'teacher_'.config('organization_id').'_';

        });

        $empDayCount = $employee->loadCount('timetable');

        $empDayCount = $empDayCount['timetable_count'];


        $permissions = Permission::all('name', 'id');

       if($employee->hasRole('teacher_2_')){

           $classes = Classes::whereDoesntHave('teachers',function($query) use($employee){


               $query->where('employee_id',$employee);
           })->with('programs')->get();

           $teacherClasses = Classes::whereHas('teachers',function($query) use($employee){


               $query->where('employee_id',$employee->id);
           })->get()->pluck('name','id');





        }






        return view('humanresource::employees.show', compact('employeeContainsTeacherRole','bankAccTypes', 'classes', 'days', 'workDays', 'timetable', 'employeeContainsSupervisorRole', 'centers', 'teacherClasses', 'newWorkTimeTable', 'employee', 'roles', 'permissions', 'empAttMonths', 'employeeAttYears', 'todayAttendance', 'weeklyAttendance', 'monthlyAttendance', 'empDayCount'));
    }


    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        $employee = Employee::find($id);


        $roles = Role::pluck('description', 'name');

        $permissions = Permission::all('name', 'id');

        return view('humanresource::employees.edit', compact('employee', 'roles', 'permissions'));
    }

    public function update(EmployeeUpdateRequest $request, Employee $employee)
    {



        \Illuminate\Support\Facades\DB::beginTransaction();
        try {


            // if request has supervisor role, then sync the record in the cen_emps many to many table
            if (collect($request->get('roles'))->contains('supervisor_' . config('organization_id') . '_')) {
                $employee->center()->sync($request->get('supervisorCenters'));
            } else {
//                if an employe had supervisor role previously, remove the center attachment
                if (DB::table('cen_emps')->where('emp_id', $employee->id)->exists()) {
                    $centerIds = DB::table('cen_emps')->where('emp_id', $employee->id)->pluck('id');
                    $employee->center()->detach($centerIds);

                }
            }
            $existingTeacherClassesIds = $employee->classes()->pluck('classes.id');
            if (collect($request->get('roles'))->contains('teacher_' . config('organization_id') . '_')) {
                if (collect($request->get('teacherClasses'))->isNotEmpty()) {
                    // TODO: in the frontend add the class start date feature for each class
                    $startDate = [];
                    for ($i = 0; $i < count($request->get('teacherClasses')); $i++) {
                        $startDate[$i] = date('Y-m-d');
                    }
                    // detach all the relationships
                    $employee->classes()->detach($existingTeacherClassesIds);
                    foreach ($request->get('teacherClasses') as $key => $teacherClass) {
                        $employee->classes()->attach($teacherClass, ['start_date' => $startDate[$key]]);
                    }

                    // here we extract center IDs based on the classes selected
                    $centerIds  = Center::whereHas('classes', function ($q) use($request) {
                        $q->whereIn('id',$request->get('teacherClasses'));
                    })->get()->pluck('id');
                    // if teacher role is taken from an employee, remove the centers that are assigned to them.
                    $employee->teacherCenter()->sync($centerIds);
                }
            } else {
                $centerIds = CenterTeacher::where('emp_id', $employee->id)->pluck('cen_id');
                $employee->classes()->detach($existingTeacherClassesIds);
                $employee->teacherCenter()->detach($centerIds);
            }
            // check for password change
            if ($request->get('password')) {
                $employee->password = bcrypt($request->get('password'));
            }

            if ($request->get('start_at')) {
                $employee->start_at = $request->start_at;
            }
            if ($request->get('image')) {
                $employee->image = $request->get('image');
            }
            $employee->employee_number = $request->get('employee_number');
            if ($request->get('mobile')) {
                $employee->mobile = $request->get('mobile');
            }
            if ($request->get('identity')) {
                $employee->identity_type = $request->get('identity');
            }
            $employee->identity_number = $request->get('identity_number');

            if ($request->get('address_1')) {
                $employee->address_1 = $request->get('address_1');
            }
            if ($request->get('address_2')) {
                $employee->address_2 = $request->get('address_2');
            }
            if ($request->get('address_country')) {
                $employee->address_country = $request->get('address_country');
            }
            if ($request->get('address_state')) {
                $employee->address_state = $request->get('address_state');
            }
            if ($request->get('address_city')) {
                $employee->address_city = $request->get('address_city');
            }
            $employee->gender = $request->get('gender');
            $employee->marital_status = $request->get('marital_status');
            $employee->full_name = $request->get('full_name');
            if ($request->get('full_name_trans')) {
                $employee->full_name_trans = $request->get('full_name_trans');
            }
            if ($request->get('name')) {
                $employee->name = $request->get('name');
            }
            $employee->email = $request->get('email');
            $employee->date_of_birth = $request->get('date_of_birth');
            if ($request->get('work_mood')) {
                $employee->work_mood = $request->get('work_mood');
            }
            if ($request->get('department_id')) {
                $employee->department_id = $request->get('department_id');
            }
            if ($request->get('date_of_joining')) {
                $employee->start_at = Carbon::parse($request->get('date_of_joining'))->format('Y-m-d') ;
            }
            if ($request->get('basic_salary')) {
                $employee->salary = $request->get('basic_salary');
            }
            if ($request->get('employment_type')) {
                $employee->contract_type = $request->get('employment_type');
            }
            if ($request->get('provisional_months')) {
                $employee->provisional_months = $request->get('provisional_months');
            }
            // if wrk_mood is task_based, then make hours_per_month null else populate with its value
            if ($request->get('work_mood') == \App\EmployeeWorkMood::where('slug','per_month')->first()->id) {
                if ($request->get('hours_per_month')) {
                    $employee->hours_per_month = $request->get('hours_per_month');
                }
                if ($request->has('days') && $request->filled('days')) {
                    $employee->syncTimeTable($request, $employee->id);
                }
            } else {
                $employee->hours_per_month = null;
            }
            $employee->nationality = $request->get('nationality');
            $employee->updated_at = Carbon::now();
            $employee->update();
            // Handle the user roles
            $employee->syncRoles($request->roles);
            if ($request->has('bank_name') || $request->filled('bank_name')) {


                if ($request->has('account_id') || $request->filled('account_id')) {


                    // delete those bank accounts that are not available in the request array
                    $deleteBankAccount = BankAccount::whereNotIn('id', $request->get('account_id'))->where('emp_id', $employee->id)->delete();
                    // update bank account details
                    foreach ($request->get('account_id') as $key => $bankAccountId) {


                        $accName = $request->get('bank_account_name')[$key];
                        $accNumber = $request->get('bank_account_no')[$key];
                        $accType = $request->get('bank_account_type')[$key];
                        $accNote = $request->get('bank_notes')[$key];
                        $bankId = $request->get('bank_name')[$key];


                        BankAccount::updateOrCreate([
                            'id' => $bankAccountId
                        ], [
                            'account_name' => strtoupper($accName),
                            'account_number' => $accNumber,
                            'account_type_id' => $accType,
                            'bank_id' => $bankId,
                            'note' => $accNote,
                            'emp_id' => $employee->id,
                        ]);
                    }
                } else {


                    //if no bank details available , then create

                    foreach ($request->get('bank_name') as $key => $bankNameId) {

                        $accName = $request->get('bank_account_name')[$key];
                        $accNumber = $request->get('bank_account_no')[$key];
                        $accType = $request->get('bank_account_type')[$key];
                        $accNote = $request->get('bank_notes')[$key];
                        BankAccount::create([
                            'account_name' => strtoupper($accName),
                            'account_number' => $accNumber,
                            'account_type_id' => $accType,
                            'note' => $accNote,
                            'bank_id' => $bankNameId,
                            'emp_id' => $employee->id,

                        ]);

                    }

                }

            }
            $users=Employee::where('id',$employee->id)
                ->where('id','!=',auth()->user()->id)
                ->with(['roles' => function ($query) {
                    $query->select('id');
                }])
                ->get(['id']);

            $created_by = \Illuminate\Support\Facades\Auth::user()->name;
            $company = GeneralSettings::find(1)->organization_name;
            $content = __('notification.Your info has been updated as a Staff by') . $created_by .  __('notification.for')  . $company . ' ';
            $number = $employee->mobile;
            $subject = __('notification.Staff Added');
            $message = __('notification.Your info Have Been updated by') . $created_by .  __('notification.as a Staff for') . $company . ' ';
            $this->sendNotification($employee, $employee->email, $subject, $content, $number, $message,$users);

            \Illuminate\Support\Facades\DB::commit();
            LogActivity::successLog($request->username . '- profile has been updated.');
            Toastr::success(__('common.Staff info has been updated Successfully'));
            flash()->success(ucfirst($employee->name) . ' has been updated.');
            return redirect()->route('employees.index');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\DB::rollBack();
            LogActivity::errorLog($e->getMessage());
            Toastr::error(__('common.Something Went Wrong'));
            return back();
        }

    }

    public function destroy($id)
    {

        try {
            \Illuminate\Support\Facades\DB::beginTransaction();


            $employee = Employee::where("id", $id)->whereHas('roles', function ($q) {

                $q->where('name', 'teacher_' . config('organization_id') . '_');

            });


            if ($employee->exists()) {
                // proceed with detaching the record for the related classes and centers
                $centerIds = DB::table('cen_emps')->where('emp_id', $id)->pluck('id');
                $employee->first()->teacherCenter()->detach($centerIds);
                $existingTeacherClassesIds = $employee->first()->classes()->pluck('classes.id');
                $employee->first()->classes()->detach($existingTeacherClassesIds);


            }
            // permanantly delete an employee
            $employee->first()->forceDelete();


//        \Toastr::success('Employee Deleted', 'Title', ["positionClass" => "toast-top-center"]);
            \Illuminate\Support\Facades\DB::commit();

            return response()->json(["message" => 'Employee Deleted !!']);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\DB::rollback();
            return response()->json(['error' => $e->getMessage()], 201);

        }
    }

    public function getEmployeesJsonFormat(Request $request)
    {
        $requestedName = '%' . $request->q . '%';
        $name_condition = "name LIKE " . "'" . $requestedName . "'" . " OR display_name LIKE " . "'" . $requestedName . "'" . " OR full_name LIKE  " . "'" . $requestedName . "'" . " OR full_name_trans LIKE " . "'" . $requestedName . "'";


        $my_query = "select * from employees where " . $name_condition;


        $employee = \DB::select($my_query, array($request->q));
        $totalCounts = \DB::select($my_query, array($request->q));
        $totalCounts = count($totalCounts);

        $searchLang = is_arabic($request->q) == true ? "Arabic" : "English";


        return response()->json(["total_count" => $totalCounts, "incomplete_results" => false, "items" => $employee, 'language' => $searchLang], 200);

    }

    public function activate(Request $request, $employeeId = null)
    {

        try {
            if ($request->ajax()) {

                $validation = \Validator::make($request->all(), [
                    'id' => 'required',
                ]);
                if ($validation->passes()) {

                    $creator_role = 'employee';
                    $employeeId = $request->id;


                    $employee = Employee::withTrashed()->findOrfail($employeeId);

                    $employee->status = "active";
                    $employee->deleted_at = NULL;
                    $employee->save();


                    // send email to the user

//                    $employee->notify(new UserStatusChangedToNewApplication($employee, $request->program_id, $request->center_id, $request->class_id));

                    return response()->json("successfully activated the employee");


                }
            }


            return response()->json(['error' => $validation->errors()->all()], 422);


        } catch (\Exception $e) {

            dd($e->getMessage());
            return response()->json($e->getMessage(), 500);
        }
    }

}

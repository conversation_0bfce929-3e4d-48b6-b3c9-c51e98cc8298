<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Modules\JobSeeker\Entities\JobAfScheduleRule;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;
/**
 * Admin controller for managing Jobs.af schedule rules
 * 
 * Provides CRUD operations and management interface for dynamic scheduling
 * of jobs.af scraping commands with Afghanistan timezone awareness
 */
final class JobsAfScheduleController extends Controller
{
    /**
     * Constructor - Apply admin middleware
     */
    public function __construct()
    {
        // Middleware is now handled in routes
        // Authentication is handled by jobseeker.admin middleware
        
        Log::info('JobsAfScheduleController: Initialized for admin schedule management');
    }

    /**
     * Display the schedule rules management interface
     *
     * @param Request $request
     * @return View
     */
    public function index(Request $request): View
    {
        // Use job_seeker guard for authentication
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('JobsAfScheduleController: Accessing schedule rules management', [
            'admin_user' => $user->email ?? 'unknown',
            'user_id' => $user->id ?? 'unknown',
            'ip' => $request->ip()
        ]);

        $stats = [
            'total_rules' => JobAfScheduleRule::count(),
            'active_rules' => JobAfScheduleRule::where('is_active', true)->count(),
            'sync_rules' => JobAfScheduleRule::where('command', 'jobseeker:sync-jobs-af')->count(),
            'fetch_rules' => JobAfScheduleRule::where('command', 'like', '%fetch%')->count(),
        ];

        // Add debug information for DataTables troubleshooting
        $debugInfo = [
            'route_url' => route('admin.jobseeker.jobsaf_schedule.data'),
            'csrf_token' => csrf_token(),
            'user_authenticated' => Auth::guard('job_seeker')->check(),
            'user_email' => $user->email ?? 'N/A',
            'current_url' => $request->url(),
        ];

        return view('modules.jobseeker.admin.jobsaf_schedule.index', [
            'stats' => $stats,
            'timezones' => $this->getAvailableTimezones(),
            'debug' => $debugInfo
        ]);
    }

    /**
     * Get schedule rules data for DataTables with advanced features
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getData(Request $request)
    {
        // Use job_seeker guard for authentication (if available)
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('JobsAfScheduleController: Fetching schedule rules data for DataTables', [
            'user_id' => $user->id ?? 'unknown',
            'user_email' => $user->email ?? 'unknown',
            'request_params' => $request->all(),
            'route_name' => $request->route()->getName()
        ]);

        try {
            // Build base query - simple query since we're doing client-side processing
            $query = JobAfScheduleRule::query()
                ->orderBy('priority')
                ->orderBy('created_at');
            
            Log::info('JobsAfScheduleController: Building DataTables query', [
                'query_count' => $query->count(),
                'user_email' => $user->email ?? 'unknown'
            ]);

            // For client-side processing, return all data
            return DataTables::collection($query->get())
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="row-checkbox form-check-input" value="' . $row->id . '">';
                })
                ->addColumn('name', function ($row) {
                    return '<span class="fw-medium">' . e($row->name) . '</span>';
                })
                ->editColumn('command', function ($row) {
                    // Make command clickable for stats modal
                    return '<a href="#" class="command-stats-trigger text-decoration-none" data-command-name="' . 
                           e($row->command) . '">' .
                           '<code class="bg-light p-1 rounded">' . e($row->command) . '</code>' .
                           '</a>';
                })
                ->addColumn('schedule_info', function ($row) {
                    return $row->human_readable_schedule_info;
                })
                ->addColumn('next_run', function ($row) {
                    // Return raw data for frontend processing
                    return [
                        'expression' => $row->schedule_expression,
                        'timezone' => $row->timezone,
                        'is_active' => $row->is_active
                    ];
                })
                ->editColumn('status', function ($row) {
                    return $row->status_badge;
                })
                ->addColumn('priority', function ($row) {
                    return '<span class="badge bg-info">' . $row->priority . '</span>';
                })
                ->addColumn('dependency', function ($row) {
                    return $row->dependency_info;
                })
                ->addColumn('timezone', function ($row) {
                    return '<small class="text-muted">' . e($row->timezone) . '</small>';
                })

                ->addColumn('actions', function ($row) {
                    $actions = '<div class="btn-group" role="group">';
                    
                    // Edit button
                    $actions .= '<button type="button" class="btn btn-sm btn-outline-primary edit-rule" data-id="' . $row->id . '" title="Edit">';
                    $actions .= '<i class="fas fa-edit"></i>';
                    $actions .= '</button>';

                    // Toggle button
                    if ($row->is_active) {
                        $actions .= '<button type="button" class="btn btn-sm btn-outline-warning toggle-rule" data-id="' . $row->id . '" data-action="disable" title="Disable">';
                        $actions .= '<i class="fas fa-pause"></i>';
                        $actions .= '</button>';
                    } else {
                        $actions .= '<button type="button" class="btn btn-sm btn-outline-success toggle-rule" data-id="' . $row->id . '" data-action="enable" title="Enable">';
                        $actions .= '<i class="fas fa-play"></i>';
                        $actions .= '</button>';
                    }

                    // Delete button (soft delete)
                    $actions .= '<button type="button" class="btn btn-sm btn-outline-danger delete-rule" data-id="' . $row->id . '" title="Delete">';
                    $actions .= '<i class="fas fa-trash"></i>';
                    $actions .= '</button>';
                    
                    $actions .= '</div>';
                    return $actions;
                })
                ->rawColumns(['checkbox', 'name', 'command', 'schedule_info', 'next_run', 'status', 'priority', 'dependency', 'timezone', 'actions'])
                ->make(true);

        } catch (\Exception $e) {
            Log::error('JobsAfScheduleController: Error in getData method', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_email' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'error' => 'Failed to fetch schedule rules data',
                'message' => $e->getMessage(),
                'debug' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'user_authenticated' => Auth::guard('job_seeker')->check(),
                    'route_exists' => route('admin.jobseeker.jobsaf_schedule.data')
                ]
            ], 500);
        }
    }

    /**
     * Get command statistics for modal display
     *
     * @param Request $request
     * @param string $command
     * @return JsonResponse
     */
    public function getCommandStats(Request $request, string $command): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('JobsAfScheduleController: Fetching command statistics', [
            'command' => $command,
            'user_email' => $user->email ?? 'unknown'
        ]);

        try {
            $stats = JobAfScheduleRule::getCommandStats($command);
            
            // Get recent executions (if log table exists)
            $recentRuns = [];
            
            // Get next scheduled runs (simplified without getNextExecution)
            $nextRuns = JobAfScheduleRule::where('command', $command)
                ->where('is_active', true)
                ->get()
                ->map(function ($rule) {
                    return [
                        'rule_name' => $rule->name,
                        'schedule_expression' => $rule->schedule_expression,
                        'timezone' => $rule->timezone,
                        'description' => $rule->getScheduleDescription(),
                    ];
                })
                ->take(5);

            return response()->json([
                'success' => true,
                'command' => $command,
                'stats' => $stats,
                'next_runs' => $nextRuns,
                'recent_runs' => $recentRuns
            ]);

        } catch (\Exception $e) {
            Log::error('JobsAfScheduleController: Error fetching command stats', [
                'command' => $command,
                'error' => $e->getMessage(),
                'user_email' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch command statistics'
            ], 500);
        }
    }

    /**
     * Store a new schedule rule
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('JobsAfScheduleController: Creating new schedule rule', [
            'admin_user' => $user->email ?? 'unknown',
            'request_data' => $request->except(['_token'])
        ]);

        try {
            $validator = $this->validateScheduleRule($request);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Convert to cron expression before storing
            $type = $request->input('schedule_type', 'cron');
            $cronExpr = $this->convertToCronExpression($type, $request->all());
            $scheduleRule = JobAfScheduleRule::create([
                'name' => $request->input('name'),
                'command' => $request->input('command'),
                'schedule_expression' => $cronExpr,
                'schedule_type' => $type,
                'days_of_week' => $this->parseJsonField($request->input('days_of_week')),
                // 'time_slots' => $this->parseJsonField($request->input('time_slots')),
                'timezone' => $request->input('timezone', 'Asia/Kabul'),
                'is_active' => $request->boolean('is_active', true),
                'priority' => $request->input('priority', 100),
                'description' => $request->input('description'),
                'depends_on_command' => $request->input('depends_on_command'),
                'delay_after_dependency' => $request->input('delay_after_dependency', 900),
                'max_execution_time' => $request->input('max_execution_time', 3600),
                'created_by' => $user->email ?? 'admin',
                'updated_by' => $user->email ?? 'admin',
            ]);

            DB::commit();

            Log::info('JobsAfScheduleController: Schedule rule created successfully', [
                'rule_id' => $scheduleRule->id,
                'rule_name' => $scheduleRule->name,
                'command' => $scheduleRule->command,
                'created_by' => $user->email
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Schedule rule created successfully',
                'rule' => $scheduleRule
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('JobsAfScheduleController: Error creating schedule rule', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'admin_user' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create schedule rule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified schedule rule
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $rule = JobAfScheduleRule::findOrFail($id);

            Log::debug('JobsAfScheduleController: Fetching schedule rule details', [
                'rule_id' => $id,
                'rule_name' => $rule->name
            ]);

            return response()->json([
                'success' => true,
                'rule' => $rule
            ]);

        } catch (\Exception $e) {
            Log::error('JobsAfScheduleController: Error fetching schedule rule', [
                'rule_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Schedule rule not found'
            ], 404);
        }
    }

    /**
     * Update the specified schedule rule
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('JobsAfScheduleController: Updating schedule rule', [
            'rule_id' => $id,
            'admin_user' => $user->email ?? 'unknown',
            'request_data' => $request->except(['_token', '_method'])
        ]);

        try {
            $rule = JobAfScheduleRule::findOrFail($id);

            $validator = $this->validateScheduleRule($request, $id);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Convert to cron expression before updating
            $type = $request->input('schedule_type', 'cron');
            $cronExpr = $this->convertToCronExpression($type, $request->all());
            $rule->update([
                'name' => $request->input('name'),
                'command' => $request->input('command'),
                'schedule_expression' => $cronExpr,
                'schedule_type' => $type,
                'days_of_week' => $this->parseJsonField($request->input('days_of_week')),
                // 'time_slots' => $this->parseJsonField($request->input('time_slots')),
                'timezone' => $request->input('timezone', 'Asia/Kabul'),
                'is_active' => $request->boolean('is_active'),
                'priority' => $request->input('priority', 100),
                'description' => $request->input('description'),
                'depends_on_command' => $request->input('depends_on_command'),
                'delay_after_dependency' => $request->input('delay_after_dependency', 900),
                'max_execution_time' => $request->input('max_execution_time', 3600),
                'updated_by' => $user->email ?? 'admin',
            ]);

            DB::commit();

            Log::info('JobsAfScheduleController: Schedule rule updated successfully', [
                'rule_id' => $rule->id,
                'rule_name' => $rule->name,
                'updated_by' => $user->email
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Schedule rule updated successfully',
                'rule' => $rule->fresh()
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('JobsAfScheduleController: Error updating schedule rule', [
                'rule_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'admin_user' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update schedule rule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Soft delete a schedule rule
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('JobsAfScheduleController: Attempting to soft delete schedule rule', [
            'rule_id' => $id,
            'admin_user' => $user->email ?? 'unknown'
        ]);

        try {
            $rule = JobAfScheduleRule::findOrFail($id);
            
            Log::info('JobsAfScheduleController: Found rule for soft deletion', [
                'rule_id' => $id,
                'rule_name' => $rule->name,
                'rule_command' => $rule->command,
                'admin_user' => $user->email ?? 'unknown'
            ]);

            // Store info for logging before deletion
            $ruleName = $rule->name;
            $ruleCommand = $rule->command;

            // Soft delete the rule
            $rule->delete();

            Log::info('JobsAfScheduleController: Successfully soft deleted schedule rule', [
                'rule_id' => $id,
                'rule_name' => $ruleName,
                'rule_command' => $ruleCommand,
                'admin_user' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => true,
                'message' => "Schedule rule '{$ruleName}' has been deleted successfully"
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            Log::warning('JobsAfScheduleController: Schedule rule not found for deletion', [
                'rule_id' => $id,
                'admin_user' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Schedule rule not found'
            ], 404);

        } catch (\Exception $e) {
            Log::error('JobsAfScheduleController: Error deleting schedule rule', [
                'rule_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'admin_user' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete schedule rule'
            ], 500);
        }
    }

    /**
     * Toggle the status of a schedule rule (enable/disable)
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function toggle(Request $request, int $id): JsonResponse
    {
        $action = $request->input('action', 'toggle');
        $user = Auth::guard('job_seeker')->user();
        
        Log::info('JobsAfScheduleController: Toggling schedule rule status', [
            'rule_id' => $id,
            'action' => $action,
            'admin_user' => $user->email ?? 'unknown'
        ]);

        try {
            $rule = JobAfScheduleRule::findOrFail($id);
            $adminUser = $user->email ?? 'admin';

            switch ($action) {
                case 'enable':
                    $result = $rule->enable($adminUser);
                    $message = 'Schedule rule enabled successfully';
                    break;
                    
                case 'disable':
                    $result = $rule->disable($adminUser);
                    $message = 'Schedule rule disabled successfully';
                    break;
                    
                default:
                    // Toggle current status
                    if ($rule->is_active) {
                        $result = $rule->disable($adminUser);
                        $message = 'Schedule rule disabled successfully';
                    } else {
                        $result = $rule->enable($adminUser);
                        $message = 'Schedule rule enabled successfully';
                    }
                    break;
            }

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'rule' => $rule->fresh()
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update schedule rule status'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('JobsAfScheduleController: Error toggling schedule rule status', [
                'rule_id' => $id,
                'action' => $action,
                'error' => $e->getMessage(),
                'admin_user' => $user->email ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle schedule rule status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk delete schedule rules
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkDelete(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'rule_ids' => 'required|array|min:1',
            'rule_ids.*' => 'integer|exists:job_af_schedule_rules,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $ruleIds = $request->input('rule_ids');
        $user = Auth::guard('job_seeker')->user();
        $adminUser = $user->email ?? 'admin';

        Log::info('JobsAfScheduleController: Bulk deleting schedule rules', [
            'rule_ids' => $ruleIds,
            'admin_user' => $adminUser
        ]);

        try {
            DB::beginTransaction();

            // Get rule names for logging before deletion
            $rulesToDelete = JobAfScheduleRule::whereIn('id', $ruleIds)->get(['id', 'name', 'command']);
            $ruleNames = $rulesToDelete->pluck('name')->toArray();

            // Soft delete the rules
            $deletedCount = JobAfScheduleRule::whereIn('id', $ruleIds)->delete();

            DB::commit();

            Log::info('JobsAfScheduleController: Bulk delete completed', [
                'deleted_count' => $deletedCount,
                'rule_names' => $ruleNames,
                'admin_user' => $adminUser
            ]);

            return response()->json([
                'success' => true,
                'message' => "Successfully deleted {$deletedCount} schedule rule(s)",
                'deleted_count' => $deletedCount
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('JobsAfScheduleController: Error in bulk delete operation', [
                'rule_ids' => $ruleIds,
                'error' => $e->getMessage(),
                'admin_user' => $adminUser
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete schedule rules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk enable/disable schedule rules
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkToggle(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'rule_ids' => 'required|array|min:1',
            'rule_ids.*' => 'integer|exists:job_af_schedule_rules,id',
            'action' => 'required|in:enable,disable'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $ruleIds = $request->input('rule_ids');
        $action = $request->input('action');
        $user = Auth::guard('job_seeker')->user();
        $adminUser = $user->email ?? 'admin';

        Log::info('JobsAfScheduleController: Bulk toggling schedule rules', [
            'rule_ids' => $ruleIds,
            'action' => $action,
            'admin_user' => $adminUser
        ]);

        try {
            DB::beginTransaction();

            $updatedCount = JobAfScheduleRule::whereIn('id', $ruleIds)
                ->update([
                    'is_active' => $action === 'enable',
                    'updated_by' => $adminUser,
                    'updated_at' => now()
                ]);

            DB::commit();

            Log::info('JobsAfScheduleController: Bulk toggle completed', [
                'updated_count' => $updatedCount,
                'action' => $action,
                'admin_user' => $adminUser
            ]);

            return response()->json([
                'success' => true,
                'message' => "Successfully {$action}d {$updatedCount} schedule rule(s)",
                'updated_count' => $updatedCount
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('JobsAfScheduleController: Error in bulk toggle operation', [
                'rule_ids' => $ruleIds,
                'action' => $action,
                'error' => $e->getMessage(),
                'admin_user' => $adminUser
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update schedule rules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate schedule rule input data
     *
     * @param Request $request
     * @param int|null $excludeId ID to exclude from unique validation
     * @return \Illuminate\Contracts\Validation\Validator
     */
    private function validateScheduleRule(Request $request, ?int $excludeId = null): \Illuminate\Contracts\Validation\Validator
    {
        $rules = [
            'name' => 'required|string|max:255',
            'command' => [
                'required',
                'string',
                'max:255',
                Rule::unique('job_af_schedule_rules', 'command')
                    ->where('schedule_expression', $request->input('schedule_expression'))
                    ->ignore($excludeId)
            ],
            'schedule_expression' => 'required|string|max:255',
            'schedule_type' => 'required|in:cron,daily_at,weekly_at,custom',
            'days_of_week' => 'nullable|array',
            'days_of_week.*' => 'integer|between:0,6',
            'timezone' => 'required|string|max:50',
            'priority' => 'required|integer|min:1|max:1000',
            'description' => 'nullable|string|max:1000',
            'depends_on_command' => 'nullable|string|max:255',
            'delay_after_dependency' => 'required_with:depends_on_command|integer|min:0|max:86400',
            'max_execution_time' => 'required|integer|min:60|max:86400',
        ];

        $validator = Validator::make($request->all(), $rules, [
            // 'time_slots.*.start.regex' => 'Start time must be in HH:MM format',
            // 'time_slots.*.end.regex' => 'End time must be in HH:MM format',
            'command.unique' => 'A rule with this command and schedule expression already exists',
        ]);

        

        return $validator;
    }

    /**
     * Parse JSON field from request input
     *
     * @param mixed $input
     * @return array|null
     */
    private function parseJsonField($input): ?array
    {
        if (is_null($input) || $input === '') {
            return null;
        }
        
        if (is_array($input)) {
            return $input;
        }
        
        if (is_string($input)) {
            $decoded = json_decode($input, true);
            return is_array($decoded) ? $decoded : null;
        }
        
        return null;
    }

    /**
     * Get available timezones for the form
     *
     * @return array
     */
    private function getAvailableTimezones(): array
    {
        return [
            'Asia/Kabul' => 'Afghanistan Time (Asia/Kabul)',
            'UTC' => 'UTC',
            'Asia/Tehran' => 'Iran Time (Asia/Tehran)',
            'Asia/Karachi' => 'Pakistan Time (Asia/Karachi)',
            'Asia/Dubai' => 'UAE Time (Asia/Dubai)',
            'Europe/London' => 'London Time (Europe/London)',
            'America/New_York' => 'New York Time (America/New_York)',
        ];
    }

    /**
     * Converts user-friendly schedule inputs into a standard cron expression.
     *
     * @param string $scheduleType
     * @param array $data
     * @return string
     */
    private function convertToCronExpression(string $scheduleType, array $data): string
    {
        $expr = '';
        if ($scheduleType === 'daily_at') {
            // Expect HH:MM
            if (preg_match('/^(\d{1,2}):(\d{2})$/', ($data['schedule_expression'] ?? ''), $m)) {
                $hour = (int)$m[1];
                $minute = (int)$m[2];
                return sprintf('%d %d * * *', $minute, $hour);
            }
        }
        if ($scheduleType === 'weekly_at') {
            // Expect "day HH:MM"
            if (preg_match('/^(\d)\s+(\d{1,2}):(\d{2})$/', ($data['schedule_expression'] ?? ''), $m)) {
                $day = (int)$m[1];
                $hour = (int)$m[2];
                $minute = (int)$m[3];
                return sprintf('%d %d * * %d', $minute, $hour, $day);
            }
        }
        if ($scheduleType === 'cron') {
            // Assume valid cron
            return $data['schedule_expression'] ?? '* * * * *';
        }
        // Fallback
        return $data['schedule_expression'] ?? '* * * * *';
    }
} 
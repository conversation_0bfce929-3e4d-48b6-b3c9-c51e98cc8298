```mermaid
graph TD
    subgraph User Interaction
        A[User visits /login page and submits credentials] --> B[POST /login];
    end

    subgraph Routing & Controller
        B --> C{Route handled by Auth\LoginController@login};
    end

    subgraph Authentication Logic in LoginController
        C --> D{1. validateLogin()};
        D -- Valid --> E{2. hasTooManyLoginAttempts?};
        D -- Invalid --> Z[Show validation errors];

        E -- No --> F{3. attemptLogin() using 'web' guard};
        E -- Yes --> Y[Send Lockout Response];

        F -- Success --> G{4. Is user's email verified?};
        F -- Failure --> X[sendFailedLoginResponse()];

        G -- No --> W[Logout user and show 'verification required' message];
        G -- Yes --> H[5. Log user activity to UserLog];
    end

    subgraph Redirection Logic
        H --> I[6. sendLoginResponse() is called];
        I --> J[7. Framework calls redirectPath() method];
        J --> K{Check Role: 'parent'?};
        K -- Yes --> L[Redirect to /parent-dashboard];
        K -- No --> M{Check Role: 'student'?};
        M -- Yes --> N[Redirect to /student-dashboard];
        M -- No --> O{Check Role: 'member'?};
        O -- Yes --> P[Redirect to student.application.form];
        O -- No --> Q[Default Redirect to student.application.form];
    end

    subgraph Final States
        L --> R((User lands on Parent Dashboard));
        N --> S((User lands on Student Dashboard));
        P --> T((User lands on Application Form));
        Q --> T;
        W --> U((Redirected back to Login));
        X --> U;
        Y --> U;
        Z --> U;
    end

    %% Styling
    classDef userAction fill:#b3e0ff,stroke:#007bff,stroke-width:2px;
    class A,B userAction;

    classDef routingController fill:#d4edda,stroke:#28a745,stroke-width:2px;
    class C routingController;

    classDef authLogic fill:#ffe0b3,stroke:#ff8c00,stroke-width:2px;
    class D,E,F,G,H authLogic;

    classDef redirectLogic fill:#e6ccff,stroke:#800080,stroke-width:2px;
    class I,J,K,L,M,N,O,P,Q redirectLogic;

    classDef successState fill:#c3e6cb,stroke:#28a745,stroke-width:2px;
    class R,S,T successState;

    classDef failureState fill:#f8d7da,stroke:#dc3545,stroke-width:2px;
    class U,W,X,Y,Z failureState;
```
<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\BackgroundSetting
 *
 * @property int $id
 * @property string $title
 * @property string $type
 * @property string $image
 * @property string $color
 * @property int $is_default
 * @property int $organization_id
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @method static \Illuminate\Database\Query\Builder|\App\BackgroundSetting whereColor($value)
 * @method static \Illuminate\Database\Query\Builder|\App\BackgroundSetting whereCreatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|\App\BackgroundSetting whereId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\BackgroundSetting whereImage($value)
 * @method static \Illuminate\Database\Query\Builder|\App\BackgroundSetting whereIsDefault($value)
 * @method static \Illuminate\Database\Query\Builder|\App\BackgroundSetting whereOrganizationId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\BackgroundSetting whereTitle($value)
 * @method static \Illuminate\Database\Query\Builder|\App\BackgroundSetting whereType($value)
 * @method static \Illuminate\Database\Query\Builder|\App\BackgroundSetting whereUpdatedAt($value)
 * @mixin \Eloquent
 * @method static \Illuminate\Database\Eloquent\Builder|BackgroundSetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BackgroundSetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BackgroundSetting query()
 */
class BackgroundSetting extends Model
{
    //
}

<?php

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\AdmissionInterview;
use App\Http\Controllers\Controller;
use App\IjazasanadMemorizationPlan;
use App\StudentHefzPlan;
use App\StudentLastApprovedPlan;
use App\StudentLastApprovedNouranyaPlan;
use App\StudentNouranyaPlan;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;
use App\Student;

class ApproveIjazasanadLevel1PlanController extends Controller
{

    public function __invoke(Request $request)
    {


        try {
            DB::beginTransaction();

            // Ensure 'id' is an array
            $ids = $request->id;
            if (!is_array($ids)) {
                throw new \Exception('Invalid data format.');
            }
            foreach ($ids as $id) {
                $plan = IjazasanadMemorizationPlan::find($id);

                if (!$plan) {
                    throw new ModelNotFoundException('Plan not found for ID: ' . $id);
                }

                $plan->status = 'active';
                $plan->approved_by = auth()->user()->id;
                $plan->updated_at = Carbon::now(); // Explicitly updating the 'updated_at' field

                $plan->save();

                $year = $plan->start_date->year;
                $month = $plan->start_date->month;


                // Soft delete existing plan for the same student, year, and month before creating a new one
                \App\StudentIjazasanadMemorizationLastApprovedPlan::where('student_id', $plan->student_id)
                    ->whereYear('plan_year_month_day', $year)
                    ->whereMonth('plan_year_month_day', $month)
                    ->delete(); // Soft delete the existing records




                // Create a new record
                \App\StudentIjazasanadMemorizationLastApprovedPlan::create([
                    'student_id' => $plan->student_id,
                    'plan_year_month_day' => \Carbon\Carbon::parse($plan->start_date)->format('Y-m-01'), // Set to the first day of the month
                    'approved_by' => $plan->approved_by,
                    'from_lesson' => $plan->from_lesson ?? null,
                    'to_lesson' => $plan->to_lesson ?? null,
                    'talqeen_from_lesson' => $plan->talqeen_from_lesson ?? null,
                    'talqeen_to_lesson' => $plan->talqeen_to_lesson ?? null,
                    'revision_from_lesson' => $plan->revision_from_lesson ?? null,
                    'revision_to_lesson' => $plan->revision_to_lesson ?? null,
                    'jazariyah_from_lesson' => $plan->jazariyah_from_lesson ?? null,
                    'jazariyah_to_lesson' => $plan->jazariyah_to_lesson ?? null,
                    'seminars_from_lesson' => $plan->seminars_from_lesson ?? null,
                    'seminars_to_lesson' => $plan->seminars_to_lesson ?? null,
                    'level_id' => $plan->level_id,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
                $admissionId = Admission::where('student_id', $plan->student_id)->first()->id;
                AdmissionInterview::where('admission_id', $admissionId)->update([
                    'status' => 'interviewed',
                    'confirmed_at' => Carbon::now(),
                    'updated_by' => auth()->user()->id
                ]);
            }

            DB::commit();

            Toastr::success('Selected Nouranya Plans Approved!', 'Success');






            $hefzPlansWaiting_approval = \App\StudentHefzPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();
            $RevisionPlanWaiting_approval = \App\StudentRevisionPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();


            $NouranyaPlanWaitingApproval = \App\StudentNouranyaPlan::has('student')  // Ensure the plan is associated with a student
            ->has('center')  // Ensure the plan is associated with a center
            ->where('status', 'waiting_for_approval')  // Filter by status 'waiting_for_approval'
            ->where(function ($query) {
                // Level 1: Check only 'from_lesson' and 'to_lesson'
                $query->where(function ($level1Query) {
                    $level1Query->whereNotNull('from_lesson')
                        ->whereNotNull('to_lesson');
                })
                    // Level 2: Check 'from_lesson', 'to_lesson', 'from_lesson_line_number', and 'to_lesson_line_number'
                    ->orWhere(function ($level2Query) {
                        $level2Query->whereNotNull('from_lesson')
                            ->whereNotNull('to_lesson')
                            ->whereNotNull('from_lesson_line_number')
                            ->whereNotNull('to_lesson_line_number');
                    })
                    // Level 3: Check 'talaqqi_from_lesson', 'talaqqi_to_lesson', 'talqeen_from_lesson', and 'talqeen_to_lesson'
                    ->orWhere(function ($level3Query) {
                        $level3Query->whereNotNull('talaqqi_from_lesson')
                            ->whereNotNull('talaqqi_to_lesson')
                            ->whereNotNull('talqeen_from_lesson')
                            ->whereNotNull('talqeen_to_lesson');
                    });
            })
                ->count();  // Get the count of such records

            $ijazasanadMemorizationPlanWaitingApproval = \App\IjazasanadMemorizationPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())

//                ->where('center_id', $supervisorCenterId)
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();

            $plans_waiting_approval = $hefzPlansWaiting_approval + $RevisionPlanWaiting_approval+$NouranyaPlanWaitingApproval+$ijazasanadMemorizationPlanWaitingApproval;


            return response()->json(['message' => 'Selected Nouranya Plans Approved!','plansWaitingApprovalCountWidget' => $plans_waiting_approval,]);

        } catch (\Exception $e) {

            DB::rollback();

            return response()->json(['message' => $e->getMessage()], 500);
        }
    }

}

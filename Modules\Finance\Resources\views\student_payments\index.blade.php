@extends('layouts.hound')

@section('mytitle', 'Student Payments')

@section('content')

    <div class="panel panel-default card-view">
        <div class="panel-heading">Student Payment</div>
        <div class="panel-body">

            <a href="{{ route('student_payments.create') }}" class="btn btn-primary btn-xs" title="Add New Menu"><span class="glyphicon glyphicon-plus" aria-hidden="true"/></a>
            <br/>
            <br/>
            <div class="table-responsive">
            <table class="table table-responsive table-striped table-bordered">
            <thead>
                <tr>
                    <th>Student Name</th>
                    <th>Amount</th>
                    <th>Added By</th>
                    <th>Payment Category</th>
                    <th>Approved By</th>
                    <th>Added On</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
            @foreach($student_payments as $payment)
                <tr>
                    <td>{{ $payment->student->full_name }}</td>
                    <td>{{ $payment->amount }}</td>
                    <td>{{ $payment->creator_role }}</td>
                    <td>{{ $payment->payment_category }}</td>
                    <td class="text-center">
                        @if($payment->verified_by)
                            {{ $payment->verifier->name }}
                        @else
                            <a href="{{ route('student_payments.edit' , $payment->id ) }}" class="btn btn-success btn-xs">Verify</a>
                        @endif
                    </td>
                    <td>{{ $payment->created_at }}</td>
                    
                    <td class="text-center">
                        @if($payment->approved == null)
                        <div class="alert-danger pa-5 text-center">
                            Waiting Verification
                        </div>
                        @elseif($payment->approved == 0)
                        Not Valid
                        @elseif($payment->approved == 1)
                        Verified
                        @endif
                    </td>
                </tr>
            @endforeach                    
            </tbody>
                </table>
            </div>

        </div>
    </div>
@endsection

@section('js')

@endsection
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {


//
        if (Auth::guard($guard)->check()) {

//            Auth::logout();

            session(['role_id' => Auth::guard("web")->user()->with('role')->pluck('id')]);
            $role_id = $request->session()->get('role_id');





//            dd($role_id,Auth::user()->role_id,auth()->guard('web')->check());


            if (\Auth::guard("web")->user()->hasRole("student")) {
                return redirect('student-dashboard');
            }
            if (\Auth::guard("web")->user()->hasRole("parent")) {
                return redirect('parent-dashboard');
            }

            if (\Auth::guard("web")->user()->hasRole("member")) {
                return redirect(route('student.application.form'));
            }
           /* elseif ($role_id == 10) {
                return redirect('customer-dashboard');
            } elseif ($role_id == "") {
                return redirect('login');*/
//             else {
//
//                return redirect('admin-dashboard');
//            }


            return redirect('/home');
        }

        return $next($request);
    }
}

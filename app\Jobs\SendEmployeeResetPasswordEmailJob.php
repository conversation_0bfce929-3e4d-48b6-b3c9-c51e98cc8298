<?php

namespace App\Jobs;

use App\Services\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Mail\SendEmailTest;
use Illuminate\Support\Facades\Log;
use Mail;
use Illuminate\Contracts\Mail\Mailer;

class SendEmployeeResetPasswordEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    protected $supervisor_info = [];
    protected $halaqahName = '';
    protected $hefzPlanCount = 0;
    protected $employeeCent = '';
    protected $sender;
    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;
    private $token;
    private $email;
    private $name;
    /**
     * Create a new job instance.
     *
     * @return void
     */
//    public function __construct($supervisor_info, $sender)
    public function __construct($token,$email,$name)
    {
        $this->token = $token;
        $this->email = $email;
        $this->name = $name;
    }


    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        //  $email = new SendEmailTestMail();

        // Mail::to($this->details['email'])->send($email);


        $emailService = app(\App\Services\EmailService::class);
        $emailService->sendEmail(
            ['email' => $this->email, 'name' => $this->name],
            'Reset Password',
            'emails.employee_password_reset', // Updated view path
            ['token' => $this->token] // Passing the token to the view
        );


//        $getDepartmentEmail = getDepartmentEmail($this->data['department']);
//        try {
//            // TODO: this email-sending feature should be implemented in a separate class, not here
//            $mj = new \Mailjet\Client(env('MAILJETAPIKEY'), env('MAILJETAPISECRET'), true, ['version' => 'v3.1']);
//            $body = [
//                'Messages' => [
//                    [
//                        'From' => [
//                            'Email' => "<EMAIL>",
//                            'Name' => "ITQAN"
//                        ],
//                        'To' => [
//                            [
//                                'Email' => $this->email,
//                                'Name' => $this->name
//                            ]
//                        ],
//                        'Subject' => 'Reset Password',
//                        'TextPart' => "You are receiving this email because we received a password reset request for your account.\n\nIf you did not request a password reset, no further action is required.",
//                        'HTMLPart' => "<p>You are receiving this email because we received a password reset request for your account.</p><p><a href='".url('workplace/password/reset', $this->token)."'>Reset Password</a></p><p>If you did not request a password reset, no further action is required.</p>",
//
//                    ]
//                ]
//            ];
//            $response = $mj->post(Resources::$Email, ['body' => $body]);
//        } catch (\Exception $exception) {
//            Log::info($exception->getMessage());
//
//
//        }
    }
}

<?php

namespace Modules\EducationalReports\Http\Controllers;

use App\Center;
use App\Classes;
use App\Employee;
use App\Student;
use App\StudentRevisionReport;
use App\Weekend;
use App\ClassRoom;
use App\YearCheck;
use App\ApiBaseMethod;
use Illuminate\Http\Request;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use PDF; // assuming you have the alias set for DOMPDF

class CenterTablesPDFController extends Controller
{

    public function __construct()
	{
//        $this->middleware('PM');
        // User::checkAuth();
	}

    public function downloadPDF($centerId,$monthYear)
    {


        // Convert the monthYear string to a Carbon instance
        $date = \Carbon\Carbon::createFromFormat('F Y', $monthYear);

// Extract the month and year separately
        $month = $date->format('m'); // Numeric representation of the month (e.g., 06)
        $year = $date->format('Y'); // 4-digit year (e.g., 2023)
        $letterHead = asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg");
        $center = Center::find($centerId);
        $centerName = $center->name; // Assuming the column name is 'full_name'
        $supervisors = $center->employee; // Assuming this returns the supervisor information

        // Format the data as needed
        $supervisorInfo = $supervisors->pluck('name')->toArray();


        $data = [
            'centerName' => $centerName,
            'letterHead' => $letterHead,
            'supervisors' => $supervisorInfo,
            'monthYear' => $date,
            'centerId' =>  $centerId,  // Retrieve and assign the required data
            'year' => $year ,  // Retrieve and assign the required data
            'month' => $month ,  // Retrieve and assign the required data
    ];


        view()->share('educationalreports::reports.pdf.center.all',$data);
        $pdf = PDF::loadView('educationalreports::reports.pdf.center.all', $data);
        return $pdf->download("CenterReport_{$center->name}_{$year}_{$month}.pdf");


        
    }

}
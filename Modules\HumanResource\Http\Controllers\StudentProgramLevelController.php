<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Admission;
use App\ApiBaseMethod;
use App\Attendance;
use App\BankAccount;
use App\BankAccountType;
use App\BaseSetup;
use App\Center;
use App\CenterEmployee;
use App\CenterTeacher;
use App\CenterTranslation;
use App\Classes;
use App\ClassTeacher;
use App\GeneralSettings;
use App\LeaveRequest;
use App\LeaveType;
use App\MissedClockOut;
use App\Notifications\UserStatusChangedToNewApplication;
use App\Notifications\WelcomeMailtoNewEmployeeNotification;
use App\PublicHoliday;
use App\Role;
use App\Employee;

use App\Student;
use App\Document;
use App\Traits\Notification;
use App\User;
use App\WeekDay;
use App\Weekend;
use App\YearCheck;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use DB;
use App\Permission;
use App\EmployeeSalary;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\EmployeeTimetable;
use App\Http\Controllers\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Modules\Admission\Notifications\StudentOfffered;
use Modules\HumanResource\Http\Requests\EmployeeStoreRequest;
use Modules\HumanResource\Http\Requests\EmployeeUpdateRequest;
use Modules\UserActivityLog\Traits\LogActivity;
use PhpOffice\PhpSpreadsheet\Calculation\DateTimeExcel\WorkDay;
use function PHPUnit\Framework\isNull;


class StudentProgramLevelController extends Controller
{

    use Notification;

    /**
     * @var Attendance
     */
    private $attendanceModal;

    public function __construct(Attendance $attendance)
    {

        $this->attendanceModal = $attendance;


    }
    function getGenderBasedEmployees(Request $request)
    {



        $results = [];

            $query =  \DB::select('select
                                            distinct(e.name),
                                            e.id
                                        from
                                            missed_clockouts mc ,
                                            employees e
                                        where
                                            e.id = mc.employee_id
                                            and e.deleted_at is null
                                        and e.gender = ? 

order by e.name
                                            ',[$request->q])     ;


            $query = collect($query)->map(function($value, $key){

            $results['name'] = $value->name;
            $results['value'] = $value->id;
            $results['text'] = $value->name;
                return $results;

            });




            return response()->json(["success" => true,'results' => $query], 200);




        return response()->json(["success" => true,'results' => $leaveTypes], 200);

    }
    public
    function saveUploadDocument(Request $request)
    {

        try {
            if ($request->file('staff_upload_document') != "" && $request->title != "") {
                $document_photo = "";
                if ($request->file('staff_upload_document') != "") {
                    $file = $request->file('staff_upload_document');
//                    $document_photo = 'staff-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                    $document_photo = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                    $file->move('uploads/staff/document/', $document_photo);
                    $document_photo = 'public/uploads/staff/document/' . $document_photo;
                }

                $document = new Document();
                $document->title = $request->title;
                $document->documentable_id = $request->employee_id;
                $document->type = 'stf';
                $document->file = $document_photo;
                $document->created_by = Auth()->user()->id;
                $document->organization_id = config('organization_id');
                $results = $document->save();
            }

            if ($results) {
                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            \Log::error($e);
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public
    function show(Request $request, Employee $employee)
    {





        $employee = $employee->loadMissing(['teacherCenter', 'classes', 'bankAccounts.bank', 'bankAccounts.bankAccountType']);

        // decision made on this date that : any new employeee after this date should have only mon-friday working
        if ($employee->created_at < Carbon::parse('2020-12-01')) {
            $newWorkTimeTable = 0;
        } else {
            $newWorkTimeTable = 1;
        }

        $todayAttendance = $this->attendanceModal->dailyAttendanceReport($employee);
        $weeklyAttendance = $this->attendanceModal->WeeklyAttendanceReport($employee);
        $monthlyAttendance = $this->attendanceModal->monthlyAttendanceReport($employee);

        $employeeDocumentsDetails = Document::where('documentable_id', $employee->id)->where('type', '=', 'stf')->get();
        $empAttMonths = DB::select("SELECT DISTINCT (MONTH(clock)) AS months FROM attendances WHERE employee_id = ? ORDER BY months ASC", [$id]);
        $employeeAttYears = DB::select("SELECT DISTINCT (year(clock)) AS years FROM attendances WHERE employee_id = ? ORDER BY years ASC", [$id]);
        $roles = Role::cursor()->pluck('description', 'name');
//        $classes = Classes::where('center_id', $employee->teacherCenter()->first()->id)->get()->pluck('name', 'id');
        $classes = Classes::all()->pluck('name', 'id');
        $bankAccTypes = BankAccountType::all()->pluck('name', 'id');


        $timetable = EmployeeTimetable::where('employee_id', $employee->id)->orderBy('day_order', 'asc')->pluck('day', 'id');
        $days = EmployeeTimetable::where('employee_id', $employee->id)->orderBy('day_order', 'asc')->get();
        $workDays = WeekDay::pluck('name', 'slug');


        $centers = CenterTranslation::cursor()->where('locale', 'en')->pluck('name', 'name');
        $centers = \App\Center::all()->pluck('location', 'id')->sortBy('location')->prepend('Select Sup Center...', "");


        $employeeContainsSupervisorRole = $employee->roles->contains(function ($item, $key) {
            return $item->name == 'supervisor_2_';

        });
        $employeeContainsTeacherRole = $employee->roles->contains(function ($item, $key) {
            return $item->name == 'teacher_'.config('organization_id').'_';

        });

        $empDayCount = $employee->loadCount('timetable');

        $empDayCount = $empDayCount['timetable_count'];


        $permissions = Permission::all('name', 'id');

       if($employee->hasRole('teacher_2_')){

           $classes = Classes::whereDoesntHave('teachers',function($query) use($employee){


               $query->where('employee_id',$employee);
           })->with('programs')->get();

           $teacherClasses = Classes::whereHas('teachers',function($query) use($employee){


               $query->where('employee_id',$employee->id);
           })->get()->pluck('name','id');





        }






        return view('humanresource::employees.show', compact('employeeContainsTeacherRole','bankAccTypes', 'classes', 'days', 'workDays', 'timetable', 'employeeContainsSupervisorRole', 'centers', 'teacherClasses', 'newWorkTimeTable', 'employee', 'roles', 'permissions', 'empAttMonths', 'employeeAttYears', 'todayAttendance', 'weeklyAttendance', 'monthlyAttendance', 'empDayCount'));
    }


    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        $employee = Employee::find($id);


        $roles = Role::pluck('description', 'name');

        $permissions = Permission::all('name', 'id');

        return view('humanresource::employees.edit', compact('employee', 'roles', 'permissions'));
    }

    public function update(Request $request)
    {



        try {


            Student::where('user_id',$request->get('userId'))->update(['level' => $request->get('levelId')]);






            LogActivity::successLog($request->username . '- profile has been updated.');
            Toastr::success(__('common.Staff info has been updated Successfully'));
            return response()->json('student program level successfully updated',200);
        } catch (\Exception $e) {
            LogActivity::errorLog($e->getMessage());
            Toastr::error(__('common.Something Went Wrong'));
            return back();
        }

    }




}

<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\FormReview
 *
 * @property int $id
 * @property int $form_id
 * @property int $created_by
 * @property int $step_order
 * @property string $action
 * @property string $note
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Employee|null $reviewedBy
 * @method static \Illuminate\Database\Eloquent\Builder|FormReview newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormReview newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormReview query()
 * @method static \Illuminate\Database\Eloquent\Builder|FormReview whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormReview whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormReview whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormReview whereFormId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormReview whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormReview whereNote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormReview whereStepOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormReview whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class FormReview extends Model
{
    protected $fillable = ['form_id','action','created_by','step_order','note'];

    public function reviewedBy()
    {
        return $this->hasOne('App\Employee', 'id', 'created_by');
    }
}

<?php
namespace Modules\EducationalReports\Http\Controllers;


use App\Events\StudentPromotion;
use App\Events\StudentPromotionGroupDisable;

use Twilio;

use App\Classes;
use App\Route;

use LaravelMsg91;
use App\Student;

use App\sTemplate;

use App\ApiBaseMethod;

use App\ClassSection;

use App\StudentGroup;

use App\AssignVehicle;
use App\DormitoryList;

use App\StudentCategory;
use App\StudentDocument;

use App\InfixModuleManager;

use Illuminate\Http\Request;

use App\StudentBulkTemporary;

use App\Imports\StudentsImport;
use App\ClassOptionalSubject;

use App\Exports\AllStudentExport;

use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;

use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;

class GuardianReportController extends Controller
{

//    private $User;
//    private $GeneralSettings;
//    private $UserLog;
//    private $InfixModuleManager;
//    private $URL;

    public function __construct()
    {
//        $this->middleware('PM');
//        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
//
//        $this->User                 = json_encode(User::find(1));
//        $this->GeneralSettings    = json_encode(GeneralSettings::find(1));
//        $this->UserLog            = json_encode(UserLog::find(1));
//        $this->InfixModuleManager   = json_encode(InfixModuleManager::find(1));
//        $this->URL                  = url('/');
    }








    public function guardianReport(Request $request)
    {
        try {
            $students = Student::all();

             // TODO: get only classes if they have students
            $classes = Classes::all();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['students'] = $students->toArray();
                $data['classes'] = $classes->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('educationalreports::guardian.guardian_report', compact('classes'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function guardianReportSearch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'class' => 'required'
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $students = Student::query();
            $students->whereHas('joint_classes', function ($q) use ($request) {
                $q->where('class_id', $request->class);
            })->get();


            $classes = Classes::all();


            $class_id = $request->class;

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['students'] = $students->toArray();
                $data['classes'] = $classes->toArray();
                $data['class_id'] = $class_id;
                return ApiBaseMethod::sendResponse($data, null);
            }
            $class = Classes::find($request->class);
            return view('educationalreports::guardian.guardian_report', compact('students', 'classes', 'class_id', 'class'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }



}
(function($) {
    $.fn.filemanager = function(type, options) {
        type = type || 'file';

        this.on('click', function(e) {
            e.preventDefault();

            var routePrefix = options && options.prefix ? options.prefix : '/en/manage/uploader';
            localStorage.setItem('target_input', $(this).data('input'));
            localStorage.setItem('target_preview', $(this).data('preview'));

            // Dynamically get the base URL
            var baseUrl = window.location.origin;

            var windowOptions = 'width=900,height=600';
            var targetUrl = baseUrl + routePrefix + '?type=' + type;
            window.open(targetUrl, 'FileManager', windowOptions);

            window.SetUrl = function(url, file_path) {
                if (!Array.isArray(url) || !url.length || !url[0].hasOwnProperty('url')) {
                    console.error('Invalid URL format');
                    return;
                }

                var originalUrl = url[0].url;
                var targetInput = $('#' + localStorage.getItem('target_input'));
                var targetPreview = $('#' + localStorage.getItem('target_preview'));

                targetInput.val(originalUrl).trigger('change');
                targetPreview.attr('src', originalUrl).trigger('change');
                $('#thumbnail').val(originalUrl);
            };
        });
    }
})(jQuery);

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Collection;
use Modules\JobSeeker\Entities\Job;
use Mo<PERSON><PERSON>\JobSeeker\Entities\JobNotificationSetup;
use Illuminate\Support\Facades\Log;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;
use NotificationChannels\Fcm\Resources\Notification as FcmNotificationResource;

final class JobAlertFcmNotification extends Notification implements ShouldQueue
{
    use Queueable;

    private string $deviceToken;

    /**
     * Create a new notification instance.
     *
     * @param Collection<int, Job> $jobs Collection of Job models relevant to the recipient
     * @param JobNotificationSetup $setup The notification setup instance
     * @return void
     */
    public function __construct(
        private readonly Collection $jobs,
        private readonly JobNotificationSetup $setup
    ) {
        $this->queue = 'fcm_notifications';
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array<int, string>
     */
    public function via($notifiable): array
    {
        // Only FCM channel - completely isolated from email
        if ($notifiable instanceof \Modules\JobSeeker\Entities\JobSeeker) {
            // First check if this specific setup has push notifications enabled
            if (!$this->setup->receive_push_notifications) {
                Log::info('JobAlertFcmNotification: FCM channel disabled - setup has push notifications disabled', [
                    'notifiable_id' => $notifiable->id,
                    'setup_id' => $this->setup->id,
                    'setup_push_enabled' => $this->setup->receive_push_notifications
                ]);
                return [];
            }
            
            // Check if setup is active
            if (!$this->setup->is_active) {
                Log::info('JobAlertFcmNotification: FCM channel disabled - setup is inactive', [
                    'notifiable_id' => $notifiable->id,
                    'setup_id' => $this->setup->id,
                    'setup_active' => $this->setup->is_active
                ]);
                return [];
            }
                
            $deviceTokens = $notifiable->getDeviceTokens();
            $hasDeviceTokens = !empty($deviceTokens);
                
            if ($hasDeviceTokens) {
                Log::info('JobAlertFcmNotification: FCM channel enabled', [
                    'notifiable_id' => $notifiable->id,
                    'device_tokens_count' => count($deviceTokens),
                    'setup_id' => $this->setup->id,
                    'setup_push_enabled' => $this->setup->receive_push_notifications,
                    'setup_active' => $this->setup->is_active,
                    'jobs_count' => $this->jobs->count()
                ]);
                
                return [FcmChannel::class];
            }
        }
        
        Log::info('JobAlertFcmNotification: FCM channel disabled - no valid tokens or invalid notifiable', [
            'notifiable_id' => $notifiable->id ?? 'unknown',
            'notifiable_type' => get_class($notifiable),
            'setup_id' => $this->setup->id,
            'setup_push_enabled' => $this->setup->receive_push_notifications ?? false,
            'setup_active' => $this->setup->is_active ?? false
        ]);
        
        return []; // No channels if FCM is not available
    }

    /**
     * Get the FCM representation of the notification.
     *
     * @param mixed $notifiable
     * @return FcmMessage
     */
    public function toFcm($notifiable): FcmMessage
    {
        $title = 'New Job Alert: ' . $this->setup->name;
        $body = 'You have new job matches!';

        if ($this->jobs->count() === 1) {
            /** @var Job $firstJob */
            $firstJob = $this->jobs->first();
            $body = $firstJob->title . ' at ' . $firstJob->company_name;
        } elseif ($this->jobs->count() > 1) {
            $body = 'You have ' . $this->jobs->count() . ' new job matches for setup: ' . $this->setup->name;
        }

        // Customize the notification payload
        $fcmNotification = new FcmNotificationResource(
            title: $title,
            body: $body,
            // You can add an image URL if you have one for the setup or a default one
            // image: 'http://example.com/url-to-image-here.png'
        );

        return FcmMessage::create()
            ->setNotification($fcmNotification)
            ->setData([
                'setup_id' => (string) $this->setup->id,
                'setup_name' => $this->setup->name,
                'jobs_count' => (string) $this->jobs->count(),
                // Optionally, include a deep link URL or other relevant data
                'click_action' => route('jobseeker.jobs.notifications', [], false), // Example, adjust as needed
            ])
            ->setCustom([
                'android' => [
                    'notification' => [
                        'color' => '#4638C2', // Your app's theme color
                        'sound' => 'default',
                    ],
                    'fcm_options' => [
                        'analytics_label' => 'job_alert',
                    ],
                ],
                'apns' => [
                    'fcm_options' => [
                        'analytics_label' => 'job_alert',
                    ],
                    'payload' => [
                        'aps' => [
                            'sound' => 'default',
                        ],
                    ],
                ],
                'webpush' => [
                    'fcm_options' => [
                        'link' => route('jobseeker.notifications', [], false),
                    ],
                    'notification' => [
                         // You can add an icon for web push notifications
                        'icon' => asset('images/itqan_logo_icon.png'), // Example path
                        'badge' => asset('images/itqan_notification_badge.png'), // Example path for badge
                        // Add actions if needed
                        // 'actions' => [
                        //     ['action' => 'view_jobs', 'title' => 'View Jobs'],
                        // ],
                    ],
                ],
            ]);
    }

    /**
     * Format jobs for display in notification.
     *
     * @return array
     */
    private function formatJobsForNotification(): array
    {
        return $this->jobs->map(function (Job $job) {
            return [
                'id' => $job->id,
                'title' => $job->title,
                'company' => $job->company,
                'location' => $job->location,
                'publish_date' => $job->publish_date?->format('Y-m-d'),
            ];
        })->toArray();
    }

    /**
     * Specifies the token to send the notification to.
     * This method is not standard for FcmChannel but can be used internally if needed.
     * The channel itself uses routeNotificationForFcm() on the notifiable.
     */
    public function setToken(string $token): self
    {
        $this->deviceToken = $token;
        return $this;
    }
} 
<?php

return array(

	'Select' => 'Seleziona',
	'Erase' => 'Cancella',
	'Open' => 'Apri',
	'Confirm_del' => 'Sei sicuro di volere cancellare questo file?',
	'All' => 'Tutti',
	'Files' => 'File',
	'Images' => 'Immagini',
	'Archives' => 'Archivi',
	'Error_Upload' => 'Il file caricato supera i limiti imposti.',
	'Error_extension' => 'Il tipo del file caricato non è permesso.',
	'Upload_file' => 'Carica',
	'Filters' => 'Filtri',
	'Videos' => 'Video',
	'Music' => 'Musica',
	'New_Folder' => 'Nuova Cartella',
	'Folder_Created' => 'Cartella creata correttamente',
	'Existing_Folder' => 'Cartella già esistente',
	'Confirm_Folder_del' => 'Sei sicuro di voler cancellare la cartella e tutti i file in essa contenuti?',
	'Return_Files_List' => 'Ritorna alla lista dei file',
	'Preview' => 'Anteprima',
	'Download' => 'Download',
	'Insert_Folder_Name' => 'Inserisci il nome della cartella:',
	'Root' => 'base',
	'Rename' => 'Rinomina',
	'Back' => 'indietro',
	'View' => 'Vista',
	'View_list' => 'Vista a lista',
	'View_columns_list' => 'Vista a colonne',
	'View_boxes' => 'Vista a box',
	'Toolbar' => 'Toolbar',
	'Actions' => 'Azioni',
	'Rename_existing_file' => 'Il file esiste già',
	'Rename_existing_folder' => 'La cartella esiste già',
	'Empty_name' => 'Il nome è vuoto',
	'Text_filter' => 'filtro di testo',
	'Swipe_help' => 'Esegui uno Swipe sul nome del file/cartella per mostrare le opzioni',
	'Upload_base' => 'Upload Base',
	'Upload_add_files' => 'Aggiungi file',
	'Upload_start' => "Esegui l'upload",
	'Upload_base_help' => "Trascina i file nell'area superiore (per i moderni browser) o clicca sul bottone \"Aggiungi file\" e clicca sul bottone \"Esegui l'upload\".  Quando il caricamento dei file è terminato clicca sul bottone di ritorno in alto.",
	'Upload_error_messages' =>array(
        1 => 'The uploaded file exceeds the upload_max_filesize directive in php.ini',
        2 => 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form',
        3 => 'The uploaded file was only partially uploaded',
        4 => 'No file was uploaded',
        6 => 'Missing a temporary folder',
        7 => 'Failed to write file to disk',
        8 => 'A PHP extension stopped the file upload',
        'post_max_size' => 'The uploaded file exceeds the post_max_size directive in php.ini',
        'max_file_size' => 'File is too big',
        'min_file_size' => 'File is too small',
        'accept_file_types' => 'Filetype not allowed',
        'max_number_of_files' => 'Maximum number of files exceeded',
        'max_width' => 'Image exceeds maximum width',
        'min_width' => 'Image requires a minimum width',
        'max_height' => 'Image exceeds maximum height',
        'min_height' => 'Image requires a minimum height',
        'abort' => 'File upload aborted',
        'image_resize' => 'Failed to resize image'
    ),
	'Upload_url' => 'URL',
	'Type_dir' => 'dir',
	'Type' => 'Tipo',
	'Dimension' => 'Dimensione',
	'Size' => 'Peso',
	'Date' => 'Data',
	'Filename' => 'Nome',
	'Operations' => 'Operazioni',
	'Date_type' => 'd/m/y',
	'OK' => 'OK',
	'Cancel' => 'Annulla',
	'Sorting' => 'ordina',
	'Show_url' => 'Mostra URL',
	'Extract' => 'estrai qui',
	'File_info' => 'informazioni file',
	'Edit_image' => 'Modifica immagine',
	'Duplicate' => 'Duplica',
	'Folders' => 'Cartelle',
	'Copy' => 'Copia',
	'Cut' => 'Taglia',
	'Paste' => 'Incolla',
	'CB' => 'CB', // clipboard
	'Paste_Here' => 'Incolla su questa cartella',
	'Paste_Confirm' => 'Sei sicuro di voler incollare in questa cartella? Questo file sovrascriverà i file/cartelle esistenti qual\'ora ci fossero.',
	'Paste_Failed' => 'Errore nell\'incollare il/i file',
	'Clear_Clipboard' => 'Pulisci clipboard',
	'Clear_Clipboard_Confirm' => 'Sei sicuro di voler cancellare la clipboard?',
	'Files_ON_Clipboard' => 'Ci sono file nella clipboard.',
	'Copy_Cut_Size_Limit' => 'I file o cartelle selezionati sono troppo grandi per %s. Il limite è: %d MB/operazione', // %s = cut or copy
	'Copy_Cut_Count_Limit' => 'Hai selezionato troppi file/cartelle da %s. Il limite è: %d file/operazione', // %s = cut or copy
	'Copy_Cut_Not_Allowed' => 'Non hai i permessi per %s %s.', // %s(1) = cut or copy, %s(2) = files or folders
	'Aviary_No_Save' => 'Non è stato possibile salvare l\'immagine',
	'Zip_No_Extract' => 'Non si può estrarre il pacchetto perchè sembra corrotto',
	'Zip_Invalid' => 'Questa estensione non è supportata. Le estensioni valide sono: zip, gz, tar.',
	'Dir_No_Write' => 'La cartella selezionata non è scrivibile.',
	'Function_Disabled' => 'La funzione %s è stata disabilitata.', // %s = cut or copy
	'File_Permission' => 'Permessi file',
	'File_Permission_Not_Allowed' => 'Il cambiamento dei permessi di %s non è permesso.', // %s = files or folders
	'File_Permission_Recursive' => 'Applica ricorsivamente?',
	'File_Permission_Wrong_Mode' => "La modalità di autorizzazione non è corretta.",
	'User' => 'Utente',
	'Group' => 'Gruppo',
	'Yes' => 'Si',
	'No' => 'No',
	'Lang_Not_Found' => 'La lingua non è stata trovata.',
	'Lang_Change' => 'Cambia la lingua',
	'File_Not_Found' => 'Il file non è stato trovato.',
	'File_Open_Edit_Not_Allowed' => 'Non hai il permesso di %s questo file.', // %s = open or edit
	'Edit' => 'Modifica',
	'Edit_File' => "Modifica il contenuto di questo file",
	'File_Save_OK' => "Il file è stato salvato con successo.",
	'File_Save_Error' => "C'è stato un errore nel salvataggio del file.",
	'New_File' => 'Nuovo file',
	'No_Extension',"Non hai inserito l'estensione del file.",
	'Valid_Extensions' => 'Estensioni valide: %s', // %s = txt,log etc.
	'Upload_message' => "Trascina qui i file per l'upload",

	'SERVER ERROR' => "SERVER ERROR",
	'forbiden' => "Forbiden",
	'wrong path' => "Wrong path",
	'wrong name' => "Wrong name",
	'wrong extension' => "Wrong extension",
	'wrong option' => "Wrong option",
	'wrong data' => "Wrong data",
	'wrong action' => "Wrong action",
	'wrong sub-action' => "Wrong sub-actio",
	'no action passed' => "No action passed",
	'no path' => "No path",
	'no file' => "No file",
	'view type number missing' => "View type number missing",
	'Not enough Memory' => "Not enough Memory",
	'max_size_reached' => "Your image folder has reach its maximale size of %d MB.", //%d = max overall size
	'B' => "B",
	'KB' => "KB",
	'MB' => "MB",
	'GB' => "GB",
	'TB' => "TB",
	'total size' => "Total size"
);


{!! Form::open(['url' => route('admission.approve'), 'id' => 'approve_admission' , 'class' => 'form-horizontal', 'files' => true]) !!}
@foreach($student->current_admission->programs as $program)
    <h5>Program: {{ $program->title }}</h5>
    {!! Form::hidden('admission_id' , $student->current_admission->id ) !!} 
        
    <div class="text-center">
    Approved  {!! Form::radio('interview['.$program->id.'][approve]' , 1 , null ,['class' => 'approve_btn' , 'program-id' => $program->id , 'checked' => 'checked' ]) !!} 
    Rejected  {!! Form::radio('interview['.$program->id.'][approve]' , 0 , null ,['class' => 'approve_btn' , 'program-id' => $program->id ]) !!} 
    </div>
    <div id="interview_form_{{$program->id}}">
    @if($program->require_interview)
        <div class="form-group {{ $errors->has('interview_time') ? 'has-error' : ''}}">
            {!! Form::label('interview_time', 'Interview Date/Time', ['class' => 'col-md-4 control-label']) !!}
            <div class="col-md-6">
                {!! Form::text('interview['.$program->id.'][interview_time]', null, ['class' => 'datetime form-control']) !!}
                <span class="alert-danger" id='error_interview_{{$program->id}}_interview_time'></span>
            </div>
        </div>
        <div class="form-group {{ $errors->has('location') ? 'has-error' : ''}}">
            {!! Form::label('location', 'Interview Location', ['class' => 'col-md-4 control-label']) !!}
            <div class="col-md-6">
                {!! Form::text('interview['.$program->id.'][location]', null, ['class' => 'form-control']) !!}
                <span class="alert-danger" id='error_interview_{{$program->id}}_location'></span>
            </div>
        </div>
        <div class="form-group {{ $errors->has('committee') ? 'has-error' : ''}}">
            {!! Form::label('committee', 'Interview Committee', ['class' => 'col-md-4 control-label']) !!}
            <div class="col-md-6">
                {!! Form::select('interview['.$program->id.'][committee][]', $interviewers , null, ['id' => 'select_committee' ,'multiple']) !!}
                <span class="alert-danger" id='error_interview_{{$program->id}}_committee'></span>
            </div>
        </div>

    @endif
    </div>
    <div class="form-group {{ $errors->has('notes') ? 'has-error' : ''}}">
        {!! Form::label('notes', 'Notes/Reason ', ['class' => 'col-md-4 control-label']) !!}
        <div class="col-md-6">
            {!! Form::textarea('interview['.$program->id.'][notes]', null, ['class' => 'form-control']) !!}
            <span class="alert-danger" id='error_notes_{{$program->id}}'></span>
        </div>
    </div>
@endforeach
<div class="text-center">
        <button type="submit" class="btn btn-primary">Save changes</button>        
</div>

{!! Form::close() !!}
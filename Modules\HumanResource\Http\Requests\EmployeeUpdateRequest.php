<?php

namespace Modules\HumanResource\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EmployeeUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        


        return [
            'bank_name' => 'sometimes|nullable',
            'work_mood' => 'required',
            'hours_per_month' => "required_if:work_mood,==,4", /** 4 = per_month (full time) */
            'bank_account_name' => 'required_with:bank_name',
            'bank_account_no' => 'required_with:bank_name',

            'full_name' => 'bail|required|min:2',
            'email' => 'required|email|unique:employees,email,' . $this->request->get('employee_id'),
            'roles' => 'required|min:1',

//            'days' => 'required_if:work_mood,per_month',
            'supervisorCenters' => Rule::requiredIf(function () {
                return collect(request()->get('roles'))->contains('supervisor_2_');
            })

        ];


    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {

        return [
            'centers.required' => 'Supervisor must have at least one center assigned',
            'hours_per_month.required_if' => 'Hours per Month are required when work mood is monthly'
        ];

        return parent::messages(); // TODO: Change the autogenerated stub
    }
}

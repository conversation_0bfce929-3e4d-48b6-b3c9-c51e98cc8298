<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StudentApproveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    /**
     * @return array
     */
    public function rules()
    {
        return [
            'admission_id' => 'required',
            'interview.*.interview_time' => function ($attribute, $value, $fail) {
                if ((is_null($value) || $value === '')) {
                    $fail('Please specify the interview time.');
                }
            },
            'interview.*.location' => function ($attribute, $value, $fail) {
                if ((is_null($value) || $value === '')) {
                    $fail('Please specify the interview location.');
                }
            },
            'interview.*.committee' => function ($attribute, $value, $fail) {
                if ((is_null($value) || $value === '')) {
                    $fail('Please select at least one interviewer.');
                }
            },
        ];
    }


    public function messages()
    {
        return [
            'interview.*.location.required' => 'Please specify the interview location',
            'interview.*.interview_time.required' => 'Please specify the interview time',
            'interview.*.committee.required' => 'Please select at least one interviewer',


        ];

    }

    public function attributes()
    {
        return [
            'interview.*.interview_time' => 'Interview Time',
            'interview.*.location' => 'Interview Location',
            'interview.*.committee' => 'Interview Committee',

        ];
    }
}

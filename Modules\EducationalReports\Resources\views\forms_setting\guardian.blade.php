@extends('layouts.hound')

@section('mytitle', 'Guardian Form Settings')

@section('content')
<div class="box box-primary">
<div class="box-header with-border">
    <h3 class="box-title">
        Guardian Registeration Form Settings
    </h3>
</div>
<!-- /.box-header -->
<!-- form start -->
{!! Form::open(['url' => route('forms_setting.update'), 'class' => 'form-horizontal', 'files' => true]) !!}

    <div class="box-body">
    <div class="clearfix setting_row">
        <div class="col-sm-3">
        {{ trans('common.display_name')}}
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_display_name', 'required' ,config('settings.guardian_form_display_name') == 'required' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.required')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_display_name', 'optional' ,config('settings.guardian_form_display_name')   == 'optional' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.optional')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_display_name', 0 ,config('settings.guardian_form_display_name')   == "0" ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.disable')}}
        
        </div>
    </div>

    <div class="clearfix setting_row">
        <div class="col-sm-3">
        {{ trans('common.full_name')}}
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_full_name', 'required' ,config('settings.guardian_form_full_name') == 'required' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.required')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_full_name', 'optional' ,config('settings.guardian_form_full_name')  == 'optional' ? 'checked' : ''  , ['class' => 'form']) !!}
        {{ trans('admission::settings.optional')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_full_name', 0 ,config('settings.guardian_form_full_name')  == "0" ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.disable')}}
        
        </div>
    </div>


    <div class="clearfix setting_row">
        <div class="col-sm-3">
        {{ trans('admission::settings.full_name_trans')}}
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_full_name_trans', 'required' ,config('settings.guardian_form_full_name_trans') == 'required' ? 'checked' : '' == 'required' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.required')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_full_name_trans', 'optional' ,config('settings.guardian_form_full_name_trans')  == 'optional' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.optional')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_full_name_trans', 0 ,config('settings.guardian_form_full_name_trans')   == "0" ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.disable')}}
        
        </div>
    </div>


    <div class="clearfix setting_row">
        <div class="col-sm-3">
        {{ trans('admission::settings.full_name_language')}}
        </div>
        <div class="col-sm-9">
        {{--  ['arabic' => 'Arabic' , 'english' => 'English' , 'Urdu' , 'Hindi', 'Thai']  --}}
        {!! Form::select('guardian_form_full_name_language', \Illuminate\Support\Arr::pluck(config('translator.languages') , 'local' , 'name') , config('settings.guardian_form_full_name_language'), ['class' => 'form-control']) !!}
                
        </div>
    </div>


    <div class="clearfix setting_row">
        <div class="col-sm-3">
        {{ trans('common.gender')}}
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_gender', 'required' ,config('settings.guardian_form_gender')== 'required' ? 'checked' : ''  == 'required' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.required')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_gender', 'optional' ,config('settings.guardian_form_gender')  == 'optional' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.optional')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_gender', 0 ,config('settings.guardian_form_gender')   == "0" ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.disable')}}
        
        </div>
    </div>


    <div class="clearfix setting_row">
        <div class="col-sm-3">
        {{ trans('common.date_of_birth')}}
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_date_of_birth', 'required' ,config('settings.guardian_form_date_of_birth') == 'required' ? 'checked' : '' == 'required' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.required')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_date_of_birth', 'optional' ,config('settings.guardian_form_date_of_birth')  == 'optional' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.optional')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_date_of_birth', 0 ,config('settings.guardian_form_date_of_birth')   == "0" ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.disable')}}
        
        </div>
    </div>


    <div class="clearfix setting_row">
        <div class="col-sm-3">
        {{ trans('common.nationality')}}
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_nationality', 'required' ,config('settings.guardian_form_nationality') == 'required' ? 'checked' : '' == 'required' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.required')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_nationality', 'optional' ,config('settings.guardian_form_nationality')  == 'optional' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.optional')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_nationality', 0 ,config('settings.guardian_form_nationality')   == "0" ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.disable')}}
        
        </div>
    </div>
    <div class="clearfix setting_row">
        <div class="col-sm-3">
        {{ trans('common.occupation')}}
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_occupation', 'required' ,config('settings.guardian_form_occupation') == 'required' ? 'checked' : '' == 'required' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.required')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_occupation', 'optional' ,config('settings.guardian_form_occupation')  == 'optional' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.optional')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_occupation', 0 ,config('settings.guardian_form_occupation')   == "0" ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.disable')}}
        
        </div>
    </div>
    <div class="clearfix setting_row">
        <div class="col-sm-3">
        {{ trans('common.mobile')}}
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_mobile', 'required' ,config('settings.guardian_form_mobile') == 'required' ? 'checked' : '' == 'required' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.required')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_mobile', 'optional' ,config('settings.guardian_form_mobile')  == 'optional' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.optional')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_mobile', 0 ,config('settings.guardian_form_mobile')   == "0" ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.disable')}}
        
        </div>
    </div>
    <div class="clearfix setting_row">
        <div class="col-sm-3">
        {{ trans('common.image')}}
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_image', 'required' ,config('settings.guardian_form_image') == 'required' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.required')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_image', 'optional' ,config('settings.guardian_form_image')  == 'optional' ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.optional')}}
        
        </div>
        <div class="col-sm-3">
        
        {!! Form::radio('guardian_form_image', 0 ,config('settings.guardian_form_image')   == "0" ? 'checked' : '' , ['class' => 'form']) !!}
        {{ trans('admission::settings.disable')}}
        
        </div>
    </div>

    </div>
    <!-- /.box-body -->

    <div class="box-footer text-center">
    
    {!! Form::submit(trans('common.save'), ['class' => 'btn btn-danger']) !!}
    
    </div>
    
    {!! Form::close() !!}
    
</div>
@endsection
@section('js')

@endsection
@section('css')
<style>
.setting_row {
    padding: 5px;
    background: #eeeeee;
    margin: 5px;
    box-shadow: 1px 1px 3px #828282;
}
</style>
@endsection
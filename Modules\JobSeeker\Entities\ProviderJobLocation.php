<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Log;

/**
 * ProviderJobLocation Entity
 * 
 * Manages mappings between provider-specific job location identifiers and canonical locations.
 * This enables dynamic location translation for admin interface without hardcoded configuration.
 * Focus: Provider-specific location mappings for job sync commands.
 * 
 * @property int $id
 * @property string $provider_name
 * @property string $location_name
 * @property string $provider_identifier
 * @property int $canonical_location_id
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
final class ProviderJobLocation extends Model
{
    protected $table = 'provider_job_locations';
    
    protected $fillable = [
        'provider_name',
        'location_name',
        'provider_identifier',
        'canonical_location_id',
        'is_active'
    ];

    protected $casts = [
        'canonical_location_id' => 'integer',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the canonical location that this provider location maps to
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function canonicalLocation(): BelongsTo
    {
        return $this->belongsTo(JobLocation::class, 'canonical_location_id');
    }

    /**
     * Scope to get locations for a specific provider
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $providerName
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForProvider($query, string $providerName)
    {
        return $query->where('provider_name', $providerName);
    }

    /**
     * Scope to get only active locations
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get provider identifiers for given provider job location IDs and provider
     * CORRECTED: Now works with provider_job_location IDs directly (for admin interface)
     *
     * @param array $providerJobLocationIds Provider job location IDs from CommandScheduleRule.location_ids
     * @param string $providerName
     * @return array
     */
    public static function getProviderIdentifiers(array $providerJobLocationIds, string $providerName): array
    {
        try {
            if (empty($providerJobLocationIds)) {
                Log::info('ProviderJobLocation: No provider job location IDs provided', [
                    'provider_name' => $providerName
                ]);
                return [];
            }

            $identifiers = self::whereIn('id', $providerJobLocationIds)
                ->forProvider($providerName)
                ->active()
                ->pluck('provider_identifier')
                ->toArray();

            Log::info('ProviderJobLocation: Retrieved provider identifiers', [
                'provider_name' => $providerName,
                'provider_job_location_ids' => $providerJobLocationIds,
                'identifiers_count' => count($identifiers),
                'identifiers' => $identifiers
            ]);

            return $identifiers;
        } catch (\Exception $e) {
            Log::error('ProviderJobLocation: Error retrieving provider identifiers', [
                'provider_name' => $providerName,
                'provider_job_location_ids' => $providerJobLocationIds,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get provider job locations formatted for Select2 dropdown (admin interface)
     * Returns provider job location IDs for CommandScheduleRule.location_ids
     *
     * @param string $providerName
     * @return array
     */
    public static function getSelect2Options(string $providerName): array
    {
        try {
            return self::forProvider($providerName)
                ->active()
                ->with('canonicalLocation')
                ->orderBy('location_name')
                ->get()
                ->map(function ($location) {
                    return [
                        'id' => $location->id, // This goes into CommandScheduleRule.location_ids
                        'text' => $location->location_name,
                        'provider_identifier' => $location->provider_identifier,
                        'canonical_location' => $location->canonicalLocation?->name
                    ];
                })
                ->toArray();
        } catch (\Exception $e) {
            Log::error('ProviderJobLocation: Error getting Select2 options', [
                'provider_name' => $providerName,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get all active providers
     *
     * @return array
     */
    public static function getActiveProviders(): array
    {
        try {
            return self::active()
                ->distinct()
                ->pluck('provider_name')
                ->toArray();
        } catch (\Exception $e) {
            Log::error('ProviderJobLocation: Error getting active providers', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Check if a provider has job location mappings
     *
     * @param string $providerName
     * @return bool
     */
    public static function hasProviderMappings(string $providerName): bool
    {
        try {
            return self::forProvider($providerName)
                ->active()
                ->exists();
        } catch (\Exception $e) {
            Log::error('ProviderJobLocation: Error checking provider mappings', [
                'provider_name' => $providerName,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get provider job location statistics for debugging
     *
     * @return array
     */
    public static function getLocationStats(): array
    {
        try {
            $stats = [
                'total_provider_job_locations' => self::count(),
                'active_provider_job_locations' => self::active()->count(),
                'by_provider' => self::selectRaw('provider_name, COUNT(*) as total_count, COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_count')
                    ->groupBy('provider_name')
                    ->orderBy('provider_name')
                    ->get()
                    ->toArray(),
                'canonical_locations_mapped' => self::active()
                    ->distinct()
                    ->count('canonical_location_id')
            ];

            Log::debug('ProviderJobLocation: Location statistics retrieved', $stats);
            return $stats;
        } catch (\Exception $e) {
            Log::error('ProviderJobLocation: Error getting location statistics', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
} 
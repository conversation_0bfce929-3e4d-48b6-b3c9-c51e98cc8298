#!/bin/bash

# <PERSON>ript to generate comprehensive database documentation in HTML format
# This includes ALL tables, their columns, and relationships

DOCS_DIR="ai_docs/architectureDiagrams"
OUTPUT_FILE="$DOCS_DIR/complete_database_documentation.html"

# Generate SQL query to get all tables
cat > "$DOCS_DIR/extract_tables.sql" << 'EOL'
SELECT 
    TABLE_NAME 
FROM 
    INFORMATION_SCHEMA.TABLES 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_TYPE = 'BASE TABLE'
ORDER BY 
    TABLE_NAME;
EOL

# Generate SQL query to get columns for a specific table
cat > "$DOCS_DIR/extract_columns.sql" << 'EOL'
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_KEY,
    COLUMN_DEFAULT,
    EXTRA
FROM 
    INFORMATION_SCHEMA.COLUMNS
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = '{{TABLE_NAME}}'
ORDER BY 
    ORDINAL_POSITION;
EOL

# Generate SQL query to find foreign keys for a specific table
cat > "$DOCS_DIR/extract_foreign_keys.sql" << 'EOL'
SELECT
    k.COLUMN_NAME,
    k.REFERENCED_TABLE_NAME,
    k.REFERENCED_COLUMN_NAME
FROM
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE k
WHERE
    k.TABLE_SCHEMA = DATABASE()
    AND k.TABLE_NAME = '{{TABLE_NAME}}'
    AND k.REFERENCED_TABLE_NAME IS NOT NULL;
EOL

# Generate HTML header
cat > "$OUTPUT_FILE" << 'EOL'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Itqan Database Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            color: #3498db;
            margin-top: 30px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        .module {
            background-color: #f9f9f9;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            border-left: 5px solid #3498db;
        }
        .table-card {
            border: 1px solid #ddd;
            margin: 15px 0;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        .table-header {
            background-color: #f5f7fa;
            padding: 10px 15px;
            border-bottom: 1px solid #ddd;
            position: sticky;
            top: 0;
        }
        .table-name {
            font-weight: bold;
            font-size: 1.2em;
            color: #2c3e50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background-color: #f5f7fa;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .pk {
            color: #e74c3c;
            font-weight: bold;
        }
        .fk {
            color: #2980b9;
            font-weight: bold;
        }
        .fk-relationship {
            margin: 5px 0;
            font-style: italic;
            color: #7f8c8d;
        }
        .module-nav {
            position: sticky;
            top: 0;
            background: white;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
            z-index: 100;
        }
        .nav-item {
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 5px;
        }
        .nav-item a {
            color: #3498db;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 3px;
            background-color: #f5f7fa;
        }
        .nav-item a:hover {
            background-color: #3498db;
            color: white;
        }
        .table-list {
            margin-bottom: 20px;
        }
        .table-link {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 3px 8px;
            background-color: #f5f7fa;
            border-radius: 3px;
            text-decoration: none;
            color: #333;
            font-size: 0.9em;
        }
        .table-link:hover {
            background-color: #e1e5eb;
        }
        #search-box {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Itqan Database Documentation</h1>
    <p>Complete documentation of all tables in the Itqan database, organized by functional modules.</p>
    
    <input type="text" id="search-box" placeholder="Search for tables...">
    
    <div class="module-nav">
        <div class="nav-item"><a href="#core-system">Core System</a></div>
        <div class="nav-item"><a href="#user-management">User Management</a></div>
        <div class="nav-item"><a href="#academic-management">Academic Management</a></div>
        <div class="nav-item"><a href="#teaching-faculty">Teaching & Faculty</a></div>
        <div class="nav-item"><a href="#attendance-scheduling">Attendance & Scheduling</a></div>
        <div class="nav-item"><a href="#centers-programs">Centers & Programs</a></div>
        <div class="nav-item"><a href="#quran-studies">Quran Studies</a></div>
        <div class="nav-item"><a href="#ijazasanad">Ijazasanad</a></div>
        <div class="nav-item"><a href="#nouranya">Nouranya</a></div>
        <div class="nav-item"><a href="#admissions">Admissions</a></div>
        <div class="nav-item"><a href="#finance">Finance</a></div>
        <div class="nav-item"><a href="#hr">Human Resources</a></div>
        <div class="nav-item"><a href="#leave">Leave Management</a></div>
        <div class="nav-item"><a href="#communications">Communications</a></div>
        <div class="nav-item"><a href="#exams">Exams & Assessment</a></div>
        <div class="nav-item"><a href="#homework">Homework</a></div>
        <div class="nav-item"><a href="#reports">Lessons & Reports</a></div>
        <div class="nav-item"><a href="#library">Library</a></div>
        <div class="nav-item"><a href="#content">Content Management</a></div>
        <div class="nav-item"><a href="#job-portal">Job Portal</a></div>
        <div class="nav-item"><a href="#profiles">Profiles</a></div>
        <div class="nav-item"><a href="#forms">Forms & Documentation</a></div>
        <div class="nav-item"><a href="#activities">Activities</a></div>
        <div class="nav-item"><a href="#misc">Miscellaneous</a></div>
    </div>

    <h2 id="table-index">Table Index</h2>
    <div class="table-list">
EOL

# Use the list of tables from the database_schema_complete.md file to create the module sections
cat "$DOCS_DIR/database_schema_complete.md" | grep -E "^- \`[a-z_]+\` -" | awk '{print $2}' | tr -d '`' | sort | while read table; do
    echo "        <a href=\"#table-$table\" class=\"table-link\">$table</a>" >> "$OUTPUT_FILE"
done

# Add HTML for each module section
echo "    </div>" >> "$OUTPUT_FILE"
echo "" >> "$OUTPUT_FILE"

# Extract module sections from the complete database schema document
awk '/^## /{flag=1; print; next} /^$/{if(flag) print; flag=0}' "$DOCS_DIR/database_schema_complete.md" | while read line; do
    if [[ $line == \#\#* ]]; then
        # It's a module title
        module_name=${line#\#\# }
        module_id=$(echo "$module_name" | tr '[:upper:]' '[:lower:]' | tr ' ' '-')
        echo "<div class=\"module\" id=\"$module_id\">" >> "$OUTPUT_FILE"
        echo "    <h2>$module_name</h2>" >> "$OUTPUT_FILE"
        echo "    <div class=\"table-list\">" >> "$OUTPUT_FILE"
    elif [[ $line == -* ]]; then
        # It's a table entry
        table_name=$(echo "$line" | grep -o '`[^`]*`' | head -1 | tr -d '`')
        table_desc=$(echo "$line" | sed -E 's/- `[^`]*` - //')
        echo "        <a href=\"#table-$table_name\" class=\"table-link\">$table_name</a>" >> "$OUTPUT_FILE"
    elif [[ -z $line ]]; then
        # End of a module section
        echo "    </div>" >> "$OUTPUT_FILE"
        echo "</div>" >> "$OUTPUT_FILE"
        echo "" >> "$OUTPUT_FILE"
    fi
done

# Placeholder for the tables content
echo "    <h2 id=\"all-tables\">All Tables</h2>" >> "$OUTPUT_FILE"
echo "    <p>Below is the complete documentation for every table in the database, including columns and relationships.</p>" >> "$OUTPUT_FILE"
echo "" >> "$OUTPUT_FILE"

# Append placeholder for dynamic content that will be filled by JavaScript
# (We'll use JavaScript to dynamically load table definitions)
cat >> "$OUTPUT_FILE" << 'EOL'
    <div id="tables-container">
        <!-- Table definitions will be inserted here -->
        <p>Loading table definitions...</p>
    </div>

    <script>
        // Search functionality
        document.getElementById('search-box').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableLinks = document.querySelectorAll('.table-link');
            
            tableLinks.forEach(link => {
                const tableName = link.textContent.toLowerCase();
                if (tableName.includes(searchTerm)) {
                    link.style.display = 'inline-block';
                } else {
                    link.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
EOL

echo "HTML documentation with all tables has been generated at $OUTPUT_FILE"
echo "To complete the documentation with actual table definitions:"
echo "1. Run the MySQL queries from your application"
echo "2. Use the results to populate the HTML file with table definitions" 
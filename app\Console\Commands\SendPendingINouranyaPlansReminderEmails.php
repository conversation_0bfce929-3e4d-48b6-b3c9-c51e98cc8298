<?php

namespace App\Console\Commands;

use App\StudentNouranyaPlan;
use Illuminate\Console\Command;
use App\Services\EmailService;
use App\Employee;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SendPendingINouranyaPlansReminderEmails extends Command
{
    protected $signature = 'email:send-pending-nouranya-plans-reminder';
    protected $description = 'Send reminder emails to employees with pending nouranya plans awaiting approval.';

    public function handle(): int
    {

// Log a sample success message
        Log::info('Sample success log triggered as scheduled. hashmat waziri');

        // Load Nouranya settings (assumed to be in \App\NouranyaEmailSetting)
        $settings = \App\NouranyaEmailSetting::first();

        $today = Carbon::now()->timezone('Asia/Kuala_Lumpur');
        $run = false;
        if ($settings) {
            switch ($settings->frequency) {
                case 'daily':
                    $run = true;
                    break;
                case 'weekly':
                    if ($today->format('D') === $settings->weekly_day) {
                        $run = true;
                    }
                    break;
                case 'monthly':
                    if ($today->format('j') == $settings->monthly_day) {
                        $run = true;
                    }
                    break;
                default:
                    $run = true;
                    break;
            }
        } else {
            // If no settings exist, run by default.
            $run = true;
        }

        if (!$run) {
            Log::info("Skipping Nouranya reminder as frequency does not match today.");
            return Command::SUCCESS;
        }

        // Retrieve excluded emails from nouranya_email_exclusions table.
        $excludedEmails = \DB::table('nouranya_email_exclusions')->pluck('email')->toArray();

        // Retrieve employees with the permission "approve_nouranya_plan"
        $employees = Employee::permission('approve_nouranya_plan')->get();



        if ($employees->isEmpty()) {
            $this->info('No employees found with permission: approve_nouranya_plan');
            Log::info('No employees found with permission: approve_nouranya_plan');
            return Command::SUCCESS;
        }

        // Count pending nouranya plans with status "waiting_for_approval"
        $nouranyaCount = StudentNouranyaPlan::where('status', 'waiting_for_approval')
            ->where(function ($query) {
                $query->whereNotNull('from_lesson')
                    ->orWhereNotNull('to_lesson');
            })
            ->whereNotNull('class_id')
            ->count();


        if ($nouranyaCount === 0) {
            Log::info('No pending nouranya plans awaiting approval.');
            return Command::SUCCESS;
        }

        $emailService = app(\App\Services\EmailService::class);


        foreach ($employees as $employee) {
            // Skip employee if email is in the exclusions list.
            if (in_array($employee->email, $excludedEmails)) {
                Log::info("Skipping excluded email: {$employee->email}");
                continue;
            }

            $to = [
                'email' => $employee->email,
                'name'  => $employee->name,
            ];

            $subject = 'Pending Nouranya Plans Awaiting Your Approval';
            $view = 'emails.pending_plans_reminder'; // Adjust view if needed.
            $viewData = [
                'employee' => $employee,
                'count'    => $nouranyaCount,
            ];


            $emailService->sendEmail($to, $subject, $view, $viewData);
        }

        Log::info("Pending Nouranya Plans reminder emails dispatched.");
        return Command::SUCCESS;
    }
}

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Middleware;

use Illuminate\Routing\Middleware\ThrottleRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Illuminate\Cache\RateLimiter;
use Closure;

/**
 * JobSeeker Search Query Rate Limiting Middleware
 * 
 * Implements rate limiting for job search endpoints with:
 * - Search-specific rate limits
 * - Caching for identical search queries
 * - Detection of unusual search patterns
 * - Logarithmic backoff for search abuse
 */
final class JobSeekerSearchThrottleMiddleware extends ThrottleRequests
{
    /**
     * Search rate limits configuration
     */
    private const SEARCH_RATE_LIMITS = [
        'authenticated' => [
            'max_attempts' => 100,    // 100 searches per hour for authenticated users
            'decay_minutes' => 60
        ],
        'unauthenticated' => [
            'max_attempts' => 30,     // 30 searches per hour for unauthenticated users
            'decay_minutes' => 60
        ],
        'identical_query' => [
            'max_attempts' => 50,      // 50 identical queries per 15 minutes
            'decay_minutes' => 15
        ],
        'suspicious_patterns' => [
            'max_attempts' => 100,     // 100 suspicious patterns per 30 minutes
            'decay_minutes' => 30
        ]
    ];

    /**
     * Pattern detection thresholds
     */
    private const PATTERN_THRESHOLDS = [
        'rapid_searches' => 10,       // 10 searches in 5 minutes triggers pattern detection
        'identical_queries' => 3,     // 3 identical queries in 5 minutes
        'empty_queries' => 5,         // 5 empty queries in 10 minutes
        'suspicious_terms' => 2       // 2 suspicious search terms in 30 minutes
    ];

    /**
     * Suspicious search patterns
     */
    private const SUSPICIOUS_PATTERNS = [
        'sql_injection' => ['union', 'select', 'insert', 'delete', 'drop', 'alter'],
        'xss_attempts' => ['<script>', 'javascript:', 'onload=', 'onerror='],
        'excessive_wildcards' => ['%', '*', '?'],
        'automated_patterns' => ['test', 'bot', 'crawler', 'spider']
    ];

    /**
     * Create a new request throttler.
     *
     * @param  \Illuminate\Cache\RateLimiter  $limiter
     * @return void
     */
    public function __construct(RateLimiter $limiter)
    {
        parent::__construct($limiter);
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  mixed  ...$parameters
     * @return mixed
     */
    public function handle($request, Closure $next, ...$parameters)
    {
        // Only apply to search-related endpoints
        if (!$this->isSearchRequest($request)) {
            return $next($request);
        }

        $isAuthenticated = Auth::guard('job_seeker')->check();
        $searchQuery = $this->extractSearchQuery($request);
        
        // Apply general search rate limiting
        $generalKey = $this->resolveGeneralSearchKey($request, $isAuthenticated);
        $generalLimits = self::SEARCH_RATE_LIMITS[$isAuthenticated ? 'authenticated' : 'unauthenticated'];
        
        $response = parent::handle(
            $request, 
            function ($req) use ($next, $searchQuery, $isAuthenticated) {
                return $this->handleSearchSpecificLogic($req, $next, $searchQuery, $isAuthenticated);
            }, 
            $generalLimits['max_attempts'], 
            $generalLimits['decay_minutes'], 
            $generalKey
        );

        return $response;
    }

    /**
     * Handle search-specific logic including caching and pattern detection
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $searchQuery
     * @param  bool  $isAuthenticated
     * @return mixed
     */
    protected function handleSearchSpecificLogic(Request $request, Closure $next, string $searchQuery, bool $isAuthenticated)
    {
        // Check for identical query throttling
        $identicalKey = $this->resolveIdenticalQueryKey($request, $searchQuery);
        $identicalLimits = self::SEARCH_RATE_LIMITS['identical_query'];

        if ($this->limiter->tooManyAttempts($identicalKey, $identicalLimits['max_attempts'])) {
            Log::warning('JobSeeker identical search query rate limit exceeded', [
                'ip' => $request->ip(),
                'user_id' => $isAuthenticated ? Auth::guard('job_seeker')->id() : null,
                'search_query' => $searchQuery,
                'user_agent' => $request->userAgent()
            ]);

            throw $this->buildException($request, $identicalKey, $identicalLimits['max_attempts']);
        }

        // Detect suspicious search patterns
        $this->detectSuspiciousPatterns($request, $searchQuery, $isAuthenticated);

        // Check cache for identical recent queries (within 5 minutes)
        $cacheKey = $this->buildCacheKey($searchQuery, $request);
        $cachedResult = Cache::get($cacheKey);

        if ($cachedResult && $this->validateCachedResult($cachedResult, $request)) {
            Log::info('JobSeeker search served from cache', [
                'search_query' => $searchQuery,
                'cache_key' => $cacheKey,
                'ip' => $request->ip(),
                'user_id' => $isAuthenticated ? Auth::guard('job_seeker')->id() : null
            ]);

            // Still count this as an attempt for identical query throttling
            $this->limiter->hit($identicalKey, $identicalLimits['decay_minutes'] * 60);

            // Return cached response if available
            return response()->json($cachedResult);
        } elseif ($cachedResult && !$this->validateCachedResult($cachedResult, $request)) {
            // Invalid cached data detected - remove from cache and log warning
            Cache::forget($cacheKey);
            Log::warning('JobSeeker invalid cached search result detected and removed', [
                'search_query' => $searchQuery,
                'cache_key' => $cacheKey,
                'ip' => $request->ip(),
                'user_id' => $isAuthenticated ? Auth::guard('job_seeker')->id() : null,
                'cached_data_type' => gettype($cachedResult),
                'user_agent' => $request->userAgent()
            ]);
        }

        // Increment counters
        $this->limiter->hit($identicalKey, $identicalLimits['decay_minutes'] * 60);

        // Process the request
        $response = $next($request);

        // Cache successful search results
        if ($response->getStatusCode() === 200 && $this->shouldCacheResponse($request, $searchQuery)) {
            $this->cacheSearchResult($cacheKey, $response);
        }

        // Log search activity
        $this->logSearchActivity($request, $searchQuery, $isAuthenticated);

        return $response;
    }

    /**
     * Check if this is a search request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function isSearchRequest(Request $request): bool
    {
        $uri = $request->getRequestUri();
        $route = $request->route()?->getName();

        return str_contains($uri, '/jobs/data') ||
               str_contains($uri, '/public-jobs') ||
               str_contains($uri, '/jobs/search') ||
               str_contains($uri, '/companies/search') ||
               str_contains($route, 'search') ||
               str_contains($route, 'data') ||
               $request->has('search') ||
               $request->has('q') ||
               $request->has('query');
    }

    /**
     * Extract search query from request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string
     */
    protected function extractSearchQuery(Request $request): string
    {
        $searchQuery = $request->input('search') ?? $request->input('q') ?? $request->input('query') ?? '';
        
        // Ensure we always return a string, never null
        return is_string($searchQuery) ? $searchQuery : '';
    }

    /**
     * Resolve general search rate limiting key
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  bool  $isAuthenticated
     * @return string
     */
    protected function resolveGeneralSearchKey(Request $request, bool $isAuthenticated): string
    {
        $identifier = $request->ip();
        
        if ($isAuthenticated) {
            $userId = Auth::guard('job_seeker')->id();
            $identifier .= '|user:' . $userId;
        }
        
        return 'jobseeker_search_throttle:general:' . $identifier;
    }

    /**
     * Resolve identical query rate limiting key
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $searchQuery
     * @return string
     */
    protected function resolveIdenticalQueryKey(Request $request, string $searchQuery): string
    {
        $identifier = $request->ip();
        $queryHash = hash('sha256', strtolower(trim($searchQuery)));
        
        return 'jobseeker_search_throttle:identical:' . $identifier . ':' . $queryHash;
    }

    /**
     * Build cache key for search results
     *
     * @param  string  $searchQuery
     * @param  \Illuminate\Http\Request  $request
     * @return string
     */
    protected function buildCacheKey(string $searchQuery, Request $request): string
    {
        $params = $request->except(['_token', 'page']);
        $paramsHash = hash('sha256', serialize($params));
        
        return 'jobseeker_search_cache:' . $paramsHash;
    }

    /**
     * Detect suspicious search patterns
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $searchQuery
     * @param  bool  $isAuthenticated
     * @return void
     */
    protected function detectSuspiciousPatterns(Request $request, string $searchQuery, bool $isAuthenticated): void
    {
        $lowerQuery = strtolower($searchQuery);
        $suspiciousFlags = [];

        // Check for SQL injection patterns
        foreach (self::SUSPICIOUS_PATTERNS['sql_injection'] as $pattern) {
            if (str_contains($lowerQuery, $pattern)) {
                $suspiciousFlags[] = 'sql_injection:' . $pattern;
            }
        }

        // Check for XSS patterns
        foreach (self::SUSPICIOUS_PATTERNS['xss_attempts'] as $pattern) {
            if (str_contains($lowerQuery, $pattern)) {
                $suspiciousFlags[] = 'xss_attempt:' . $pattern;
            }
        }

        // Check for excessive wildcards
        $wildcardCount = 0;
        foreach (self::SUSPICIOUS_PATTERNS['excessive_wildcards'] as $wildcard) {
            $wildcardCount += substr_count($lowerQuery, $wildcard);
        }
        if ($wildcardCount > 5) {
            $suspiciousFlags[] = 'excessive_wildcards:' . $wildcardCount;
        }

        // Check for automated patterns
        foreach (self::SUSPICIOUS_PATTERNS['automated_patterns'] as $pattern) {
            if (str_contains($lowerQuery, $pattern)) {
                $suspiciousFlags[] = 'automated_pattern:' . $pattern;
            }
        }

        // Log suspicious activity
        if (!empty($suspiciousFlags)) {
            Log::warning('JobSeeker suspicious search pattern detected', [
                'ip' => $request->ip(),
                'user_id' => $isAuthenticated ? Auth::guard('job_seeker')->id() : null,
                'search_query' => $searchQuery,
                'suspicious_flags' => $suspiciousFlags,
                'user_agent' => $request->userAgent(),
                'route' => $request->route()?->getName(),
                'uri' => $request->getRequestUri()
            ]);

            // Increment suspicious pattern counter
            $suspiciousKey = 'jobseeker_search_suspicious:' . $request->ip();
            $suspiciousCount = Cache::increment($suspiciousKey, 1);
            if ($suspiciousCount === 1) {
                Cache::put($suspiciousKey, 1, now()->addMinutes(60));
            }
        }

        // Check for rapid empty queries
        if (empty(trim($searchQuery))) {
            $emptyKey = 'jobseeker_search_empty:' . $request->ip();
            $emptyCount = Cache::increment($emptyKey, 1);
            if ($emptyCount === 1) {
                Cache::put($emptyKey, 1, now()->addMinutes(10));
            }

            if ($emptyCount >= self::PATTERN_THRESHOLDS['empty_queries']) {
                Log::warning('JobSeeker excessive empty search queries detected', [
                    'ip' => $request->ip(),
                    'user_id' => $isAuthenticated ? Auth::guard('job_seeker')->id() : null,
                    'empty_query_count' => $emptyCount,
                    'user_agent' => $request->userAgent()
                ]);
            }
        }
    }

    /**
     * Check if response should be cached
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $searchQuery
     * @return bool
     */
    protected function shouldCacheResponse(Request $request, string $searchQuery): bool
    {
        // Don't cache empty queries or queries with suspicious patterns
        if (empty(trim($searchQuery))) {
            return false;
        }

        // Don't cache very short queries (likely typos or incomplete)
        if (strlen(trim($searchQuery)) < 3) {
            return false;
        }

        // Don't cache if contains suspicious patterns
        $lowerQuery = strtolower($searchQuery);
        foreach (self::SUSPICIOUS_PATTERNS as $patterns) {
            foreach ($patterns as $pattern) {
                if (str_contains($lowerQuery, $pattern)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Cache search result
     *
     * @param  string  $cacheKey
     * @param  mixed  $response
     * @return void
     */
    protected function cacheSearchResult(string $cacheKey, $response): void
    {
        // Cache for 5 minutes for frequently searched terms
        if ($response->headers->get('content-type') === 'application/json') {
            $content = $response->getContent();
            $decodedContent = json_decode($content, true);
            
            // Add timestamp for freshness validation
            if (is_array($decodedContent)) {
                $decodedContent['cached_at'] = date('Y-m-d H:i:s');
                Cache::put($cacheKey, $decodedContent, now()->addMinutes(5));
            }
        }
    }

    /**
     * Validate cached search result to prevent cache poisoning
     *
     * @param  mixed  $cachedResult
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function validateCachedResult($cachedResult, Request $request): bool
    {
        // Basic type validation - must be an array
        if (!is_array($cachedResult)) {
            return false;
        }

        // Check for maximum reasonable size (prevent memory exhaustion)
        $serializedSize = strlen(serialize($cachedResult));
        if ($serializedSize > 1048576) { // 1MB limit
            return false;
        }

        // Validate expected structure for job search results
        if (!$this->validateSearchResultStructure($cachedResult)) {
            return false;
        }

        // Check for suspicious content in cached data
        if ($this->containsSuspiciousContent($cachedResult)) {
            return false;
        }

        // Validate data freshness (additional check beyond cache TTL)
        if (isset($cachedResult['cached_at'])) {
            $cachedTime = strtotime($cachedResult['cached_at']);
            if ($cachedTime && (time() - $cachedTime) > 600) { // 10 minutes max
                return false;
            }
        }

        return true;
    }

    /**
     * Validate the structure of cached search results
     *
     * @param  array  $cachedResult
     * @return bool
     */
    protected function validateSearchResultStructure(array $cachedResult): bool
    {
        // Expected keys for job search results
        $expectedKeys = ['data', 'meta', 'links'];
        $hasValidStructure = false;

        // Check if it has typical Laravel API resource structure
        if (isset($cachedResult['data']) && is_array($cachedResult['data'])) {
            $hasValidStructure = true;
        }

        // Alternative: Check if it's a direct array of results
        if (!$hasValidStructure && is_array($cachedResult) && !empty($cachedResult)) {
            // Check if first element looks like a job/company record
            $firstItem = reset($cachedResult);
            if (is_array($firstItem) && (
                isset($firstItem['id']) || 
                isset($firstItem['title']) || 
                isset($firstItem['name']) ||
                isset($firstItem['company_name'])
            )) {
                $hasValidStructure = true;
            }
        }

        // Allow empty results (valid search with no matches)
        if (!$hasValidStructure && empty($cachedResult)) {
            $hasValidStructure = true;
        }

        return $hasValidStructure;
    }

    /**
     * Check for suspicious content in cached data
     *
     * @param  array  $cachedResult
     * @return bool
     */
    protected function containsSuspiciousContent(array $cachedResult): bool
    {
        $serializedData = serialize($cachedResult);
        $lowerData = strtolower($serializedData);

        // Check for script injection attempts
        $suspiciousPatterns = [
            '<script',
            'javascript:',
            'onload=',
            'onerror=',
            'eval(',
            'function(',
            'document.cookie',
            'window.location',
            'alert(',
            'confirm(',
            'prompt(',
            'setTimeout(',
            'setInterval('
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (str_contains($lowerData, $pattern)) {
                return true;
            }
        }

        // Check for SQL injection patterns
        $sqlPatterns = [
            'union select',
            'drop table',
            'delete from',
            'insert into',
            'update set',
            'alter table',
            'create table',
            'truncate table'
        ];

        foreach ($sqlPatterns as $pattern) {
            if (str_contains($lowerData, $pattern)) {
                return true;
            }
        }

        // Check for excessive nesting (potential DoS)
        if (substr_count($serializedData, 'a:') > 100) { // Too many arrays
            return true;
        }

        return false;
    }

    /**
     * Log search activity
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $searchQuery
     * @param  bool  $isAuthenticated
     * @return void
     */
    protected function logSearchActivity(Request $request, string $searchQuery, bool $isAuthenticated): void
    {
        // Log every search for analytics (with sampling for high volume)
        if (mt_rand(1, 5) === 1) { // Log 20% of searches
            Log::info('JobSeeker search activity', [
                'search_query' => $searchQuery,
                'ip' => $request->ip(),
                'user_id' => $isAuthenticated ? Auth::guard('job_seeker')->id() : null,
                'user_agent' => $request->userAgent(),
                'route' => $request->route()?->getName(),
                'parameters' => $request->except(['_token', 'password'])
            ]);
        }
    }
} 
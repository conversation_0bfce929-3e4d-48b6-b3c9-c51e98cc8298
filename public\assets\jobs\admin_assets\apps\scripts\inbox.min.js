var AppInbox=function(){var t=$(".inbox-content"),n="",o=function(o,e){var i="app_inbox_inbox.html",a=o.attr("data-title");n=e,App.blockUI({target:t,overlayColor:"none",animate:!0}),p(o),$.ajax({type:"GET",cache:!1,url:i,dataType:"html",success:function(n){p(o),App.unblockUI(".inbox-content"),$(".inbox-nav > li.active").removeClass("active"),o.closest("li").addClass("active"),$(".inbox-header > h1").text(a),t.html(n),Layout.fixContentHeight&&Layout.fixContentHeight(),App.initUniform()},error:function(t,n,e){p(o)},async:!1}),jQuery("body").on("change",".mail-group-checkbox",function(){var t=jQuery(".mail-checkbox"),n=jQuery(this).is(":checked");jQuery(t).each(function(){$(this).attr("checked",n)}),jQuery.uniform.update(t)})},e=function(n,o,e){var i="app_inbox_view.html";App.blockUI({target:t,overlayColor:"none",animate:!0}),p(n);var a=n.parent("tr").attr("data-messageid");$.ajax({type:"GET",cache:!1,url:i,dataType:"html",data:{message_id:a},success:function(o){App.unblockUI(t),p(n),e&&$(".inbox-nav > li.active").removeClass("active"),$(".inbox-header > h1").text("View Message"),t.html(o),Layout.fixContentHeight(),App.initUniform()},error:function(t,o,e){p(n)},async:!1})},i=function(){$(".inbox-wysihtml5").wysihtml5({stylesheets:["../assets/global/plugins/bootstrap-wysihtml5/wysiwyg-color.css"]})},a=function(){$("#fileupload").fileupload({url:"../assets/global/plugins/jquery-file-upload/server/php/",autoUpload:!0}),$.support.cors&&$.ajax({url:"../assets/global/plugins/jquery-file-upload/server/php/",type:"HEAD"}).fail(function(){$('<span class="alert alert-error"/>').text("Upload server currently unavailable - "+new Date).appendTo("#fileupload")})},c=function(n){var o="app_inbox_compose.html";App.blockUI({target:t,overlayColor:"none",animate:!0}),p(n),$.ajax({type:"GET",cache:!1,url:o,dataType:"html",success:function(o){App.unblockUI(t),p(n),$(".inbox-nav > li.active").removeClass("active"),$(".inbox-header > h1").text("Compose"),t.html(o),a(),i(),$(".inbox-wysihtml5").focus(),Layout.fixContentHeight(),App.initUniform()},error:function(t,o,e){p(n)},async:!1})},l=function(n){var o=($(n).attr("data-messageid"),"app_inbox_reply.html");App.blockUI({target:t,overlayColor:"none",animate:!0}),p(n),$.ajax({type:"GET",cache:!1,url:o,dataType:"html",success:function(o){App.unblockUI(t),p(n),$(".inbox-nav > li.active").removeClass("active"),$(".inbox-header > h1").text("Reply"),t.html(o),$('[name="message"]').val($("#reply_email_content_body").html()),r(),a(),i(),Layout.fixContentHeight(),App.initUniform()},error:function(t,o,e){p(n)},async:!1})},r=function(){var t=$(".inbox-compose .mail-to .inbox-cc"),n=$(".inbox-compose .input-cc");t.hide(),n.show(),$(".close",n).click(function(){n.hide(),t.show()})},s=function(){var t=$(".inbox-compose .mail-to .inbox-bcc"),n=$(".inbox-compose .input-bcc");t.hide(),n.show(),$(".close",n).click(function(){n.hide(),t.show()})},p=function(t){"undefined"!=typeof t&&(t.attr("disabled")?t.attr("disabled",!1):t.attr("disabled",!0))};return{init:function(){$(".inbox").on("click",".compose-btn",function(){c($(this))}),$(".inbox").on("click",".inbox-discard-btn",function(t){t.preventDefault(),o($(this),n)}),$(".inbox").on("click",".reply-btn",function(){l($(this))}),$(".inbox").on("click",".view-message",function(){e($(this))}),$(".inbox-nav > li > a").click(function(){o($(this),"inbox")}),$(".inbox-content").on("click",".mail-to .inbox-cc",function(){r()}),$(".inbox-content").on("click",".mail-to .inbox-bcc",function(){s()}),"view"===App.getURLParameter("a")?e():"compose"===App.getURLParameter("a")?c():$(".inbox-nav > li:first > a").click()}}}();jQuery(document).ready(function(){AppInbox.init()});
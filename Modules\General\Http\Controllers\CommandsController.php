<?php

namespace Modules\General\Http\Controllers;

use App\IjazasanadEmailExclusion;
use App\IjazasanadEmailSetting;
use App\IjazasanadEmailLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Services\EmailService;
use App\Employee;
// Models might differ depending on your app structure
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Modules\General\Http\Requests\IjazasanadRequest;

class CommandsController extends Controller
{

    public function home()
    {


        return view('general::commands.home');
    }

    /**
     * Show Ijazasanad Email Command settings (frequency, exclusions, logs).
     */
    public function showIjazasanadSettings()
    {
        // Load the single settings row (assuming there's only one row in table)
        $settings = IjazasanadEmailSetting::first();

        // Fetch all exclusions
        $exclusions = IjazasanadEmailExclusion::all();

        // Fetch logs - only failed ones for potential resending
        $failedLogs = IjazasanadEmailLog::failed()
            ->orderBy('created_at', 'desc')
            ->take(50)
            ->get();
            
        // Get recent successful logs for audit trail
        $recentSuccessfulLogs = IjazasanadEmailLog::successful()
            ->orderBy('created_at', 'desc')
            ->take(20)
            ->get();
            
        // Get execution stats
        $executionStats = [
            'total_sent' => IjazasanadEmailLog::successful()->count(),
            'total_failed' => IjazasanadEmailLog::failed()->count(),
            'last_execution' => IjazasanadEmailLog::orderBy('created_at', 'desc')->first()?->created_at,
            'excluded_count' => $exclusions->count(),
        ];

        // Get last command execution from logs - using the email logs table directly
        // instead of trying to use monitored_scheduled_task_log_items
        $lastRunTime = IjazasanadEmailLog::orderBy('created_at', 'desc')->first()?->created_at;

        return view('general::commands.ijazasanad', compact(
            'settings', 
            'exclusions', 
            'failedLogs',
            'recentSuccessfulLogs',
            'executionStats',
            'lastRunTime'
        ));
    }


    public function showMemorization()
    {
        return view('general::commands.memorization');
    }

    /**
     * Display the Nouranya Plans Reminder management page.
     */
    public function showNouranya()
    {
        return view('general::commands.nouranya');
    }

    /**
     * Display the Trash Missed Clockouts management page.
     */
    public function showTrashClockOut()
    {
        return view('general::commands.trashClockOut');
    }

    /**
     * Display the Cache Trashed Students management page.
     */
    public function showCacheTrashed()
    {
        return view('general::commands.cacheTrashed');
    }

    /**
     * Display the Attendance Email Sponsors management page.
     */
    public function showAttendanceSponsors()
    {
        return view('general::commands.attendanceSponsors');
    }


    /**
     * Save Ijazasanad Email Command settings or add new exclusion.
     */
    public function updateAll(IjazasanadRequest $request)
    {
        try {
            DB::beginTransaction();
            
            // 1) Update or Create Settings if frequency is present
            if ($request->filled('frequency')) {
                $settingId = $request->input('setting_id');
                $setting = $settingId
                    ? IjazasanadEmailSetting::find($settingId)
                    : new IjazasanadEmailSetting();

                if (!$setting) {
                    $setting = new IjazasanadEmailSetting();
                }

                $setting->frequency = $request->input('frequency');
                $setting->timezone = $request->input('timezone');

                if ($setting->frequency === 'daily') {
                    $setting->daily_time = $request->input('daily_time') ?: '23:58';
                    $setting->weekly_day = null;
                    $setting->monthly_day = null;
                } elseif ($setting->frequency === 'weekly') {
                    $setting->daily_time = null;
                    $setting->weekly_day = $request->input('weekly_day');
                    $setting->monthly_day = null;
                } elseif ($setting->frequency === 'monthly') {
                    $setting->daily_time = null;
                    $setting->weekly_day = null;
                    $setting->monthly_day = $request->input('monthly_day');
                }

                $setting->save();
                
                // Log the changes to make the audit trail
                Log::info("[IJAZASANAD SETTINGS] Updated email frequency settings: " . json_encode([
                    'frequency' => $setting->frequency,
                    'timezone' => $setting->timezone,
                    'daily_time' => $setting->daily_time,
                    'weekly_day' => $setting->weekly_day,
                    'monthly_day' => $setting->monthly_day,
                ]));
            }

            // 2) Add new exclusions if new_exclusions is present
            if ($request->filled('new_exclusions')) {
                $addedExclusions = [];
                
                foreach ($request->input('new_exclusions') as $email) {
                    $exclusion = IjazasanadEmailExclusion::updateOrCreate(
                        ['email' => $email],
                        ['reason' => 'Added via admin interface on ' . now()->format('Y-m-d H:i:s')]
                    );
                    
                    $addedExclusions[] = $email;
                }
                
                if (count($addedExclusions) > 0) {
                    Log::info("[IJAZASANAD SETTINGS] Added email exclusions: " . implode(', ', $addedExclusions));
                }
            }

            // 3) Remove exclusions if remove_exclusions is present
            if ($request->filled('remove_exclusions')) {
                $removedIds = $request->input('remove_exclusions');
                $removedExclusions = IjazasanadEmailExclusion::whereIn('id', $removedIds)->pluck('email')->toArray();
                
                IjazasanadEmailExclusion::whereIn('id', $removedIds)->delete();
                
                if (count($removedExclusions) > 0) {
                    Log::info("[IJAZASANAD SETTINGS] Removed email exclusions: " . implode(', ', $removedExclusions));
                }
            }

            // 4) Resend failed logs
            if ($request->filled('resend_logs')) {
                $emailService = app(\App\Services\EmailService::class);
                $resendCount = 0;
                $failCount = 0;
                
                foreach ($request->input('resend_logs') as $logId) {
                    $log = IjazasanadEmailLog::find($logId);
                    if (!$log || $log->status !== 'failed') {
                        continue;
                    }
                    
                    try {
                        // Find the employee for this email
                        $employee = Employee::where('email', $log->email)->first();
                        
                        if (!$employee) {
                            throw new \Exception("Employee not found for email: {$log->email}");
                        }
                        
                        $to = [
                            'email' => $log->email,
                            'name' => $employee->name,
                        ];
                        
                        // Fetch current counts for accurate numbers in the email
                        $level1Count = IjazasanadMemorizationPlan::has('student')
                            ->has('center')
                            ->where('status', 'waiting_for_approval')
                            ->where(function ($query) {
                                $query->whereNotNull('talqeen_from_lesson')
                                    ->whereNotNull('talqeen_to_lesson');
                            })->count();
                            
                        $level2Count = IjazasanadMemorizationPlan::has('student')
                            ->has('center')
                            ->where('status', 'waiting_for_approval')
                            ->whereNotNull('start_from_surat')
                            ->whereNotNull('to_surat')
                            ->count();
                        
                        $viewData = [
                            'employee' => $employee,
                            'level1Count' => $level1Count,
                            'level2Count' => $level2Count,
                            'isResend' => true,
                            'originalSentDate' => $log->created_at,
                        ];
                        
                        $emailService->sendEmail($to, $log->subject, 'emails.pending_plans_reminder', $viewData);
                        
                        // Update the log to show success
                        $log->status = 'success';
                        $log->error_message = null;
                        $log->updated_at = now();
                        $log->save();
                        
                        $resendCount++;
                        
                        Log::info("[IJAZASANAD RESEND] Successfully resent email to {$log->email} (Log ID: {$log->id})");
                    } catch (\Exception $e) {
                        // Update the log with the new error
                        $log->error_message = $e->getMessage();
                        $log->updated_at = now();
                        $log->save();
                        
                        $failCount++;
                        
                        Log::error("[IJAZASANAD RESEND] Failed to resend email to {$log->email}: " . $e->getMessage());
                    }
                }
                
                if ($resendCount > 0) {
                    $message = "Successfully resent {$resendCount} emails.";
                    if ($failCount > 0) {
                        $message .= " Failed to resend {$failCount} emails.";
                    }
                    session()->flash('success', $message);
                } else if ($failCount > 0) {
                    session()->flash('error', "Failed to resend {$failCount} emails. Check logs for details.");
                }
            }
            
            DB::commit();
            
            return redirect()
                ->route('general.commands.ijazasanad.show')
                ->with('success', 'All changes saved successfully!');
                
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("[IJAZASANAD SETTINGS] Error updating settings: " . $e->getMessage());
            
            return redirect()
                ->route('general.commands.ijazasanad.show')
                ->with('error', 'Error saving changes: ' . $e->getMessage());
        }
    }

    /**
     * Resend a failed email attempt from the log.
     */
    public function resendFailedEmail(Request $request, EmailService $emailService)
    {
        $request->validate([
            'log_id' => 'required|integer',
        ]);

        $log = IjazasanadEmailLog::find($request->input('log_id'));
        if (!$log) {
            return redirect()
                ->route('general.commands.ijazasanad.show')
                ->with('error', 'Log entry not found.');
        }

        // Attempt to resend
        try {
            $employee = Employee::where('email', $log->email)->first();
            
            $to = [
                'email' => $log->email,
                'name' => $employee ? $employee->name : 'Recipient',
            ];
            
            $subject = $log->subject;
            $view = 'emails.pending_plans_reminder';
            
            // Get current counts for accuracy
            $level1Count = IjazasanadMemorizationPlan::has('student')
                ->has('center')
                ->where('status', 'waiting_for_approval')
                ->where(function ($query) {
                    $query->whereNotNull('talqeen_from_lesson')
                        ->whereNotNull('talqeen_to_lesson');
                })->count();
                
            $level2Count = IjazasanadMemorizationPlan::has('student')
                ->has('center')
                ->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('to_surat')
                ->count();
            
            $viewData = [
                'employee' => $employee,
                'level1Count' => $level1Count,
                'level2Count' => $level2Count,
                'isResend' => true,
                'originalSentDate' => $log->created_at,
            ];

            $emailService = app(\App\Services\EmailService::class);
            $emailService->sendEmail($to, $subject, $view, $viewData);

            // Update the log status
            $log->status = 'success';
            $log->error_message = null;
            $log->updated_at = now();
            $log->save();
            
            Log::info("[IJAZASANAD RESEND] Successfully resent email to {$log->email} (Log ID: {$log->id})");

            return redirect()
                ->route('general.commands.ijazasanad.show')
                ->with('success', 'Email resent successfully!');
        } catch (\Exception $e) {
            // If it still fails, update the log
            $log->error_message = $e->getMessage();
            $log->updated_at = now();
            $log->save();
            
            Log::error("[IJAZASANAD RESEND] Failed to resend email to {$log->email}: " . $e->getMessage());

            return redirect()
                ->route('general.commands.ijazasanad.show')
                ->with('error', 'Failed to resend: ' . $e->getMessage());
        }
    }
    
    /**
     * Run the command manually for testing purposes
     */
    public function runIjazasanadCommand()
    {
        try {
            // Record start time for performance tracking
            $startTime = microtime(true);
            
            // Log that this was manually triggered
            Log::info("[IJAZASANAD COMMAND] *** Manually triggered by user ID: " . auth()->id() . " ***");
            
            // Call the command
            $exitCode = \Artisan::call('email:send-pending-ijazasanad-plans-reminder');
            
            // Calculate execution time
            $executionTime = round(microtime(true) - $startTime, 2);
            
            // Get the command output
            $output = \Artisan::output();
            
            // Determine success based on exit code
            $success = ($exitCode === 0);
            
            // Log results
            if ($success) {
                Log::info("[IJAZASANAD COMMAND] Manually triggered command completed successfully in {$executionTime}s");
                
                // Get the log entry to display recent results
                $recentLogs = IjazasanadEmailLog::orderBy('created_at', 'desc')
                    ->take(5)
                    ->get();
                    
                $sentCount = $recentLogs->where('status', 'success')->count();
                $failedCount = $recentLogs->where('status', 'failed')->count();
                
                $message = "Command executed successfully in {$executionTime}s.";
                
                if ($sentCount > 0 || $failedCount > 0) {
                    $message .= " Recent results: {$sentCount} sent, {$failedCount} failed.";
                } else {
                    $message .= " No emails were processed. See logs for details.";
                }
                
                return redirect()
                    ->route('general.commands.ijazasanad.show')
                    ->with('success', $message);
            } else {
                Log::error("[IJAZASANAD COMMAND] Manually triggered command failed with exit code: {$exitCode}");
                Log::error("[IJAZASANAD COMMAND] Command output: " . $output);
                
                return redirect()
                    ->route('general.commands.ijazasanad.show')
                    ->with('error', "Command failed with exit code {$exitCode}. Check logs for details.");
            }
        } catch (\Exception $e) {
            Log::error("[IJAZASANAD COMMAND] Error running command: " . $e->getMessage());
            Log::error("[IJAZASANAD COMMAND] Exception trace: " . $e->getTraceAsString());
            
            return redirect()
                ->route('general.commands.ijazasanad.show')
                ->with('error', 'Error running command: ' . $e->getMessage());
        }
    }

    /**
     * Test the Ijazasanad Command with a specific email (AJAX request).
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testIjazasanadCommand(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid email address provided.',
                'error' => $validator->errors()->first('email')
            ]);
        }

        try {
            // Get the email address from request
            $testEmail = $request->input('email');
            
            // Find employee by email
            $employee = Employee::where('email', $testEmail)->first();
            
            if (!$employee) {
                return response()->json([
                    'success' => false,
                    'message' => 'Employee not found with this email. The email must belong to an existing employee to test.',
                    'error' => 'No employee record found with email: ' . $testEmail
                ]);
            }
            
            // Check if the employee has the required permission
            if (!$employee->hasPermissionTo('approve_ijazasanad_plan')) {
                return response()->json([
                    'success' => false,
                    'message' => 'This employee does not have the "approve_ijazasanad_plan" permission required to receive these emails.',
                    'error' => 'Employee lacks required permission: approve_ijazasanad_plan'
                ]);
            }
            
            // Check if email is excluded
            $isExcluded = IjazasanadEmailExclusion::where('email', $testEmail)->exists();
            if ($isExcluded) {
                return response()->json([
                    'success' => false,
                    'message' => 'This email is currently in the exclusion list. Remove it from exclusions to test.',
                    'error' => 'Email is excluded: ' . $testEmail
                ]);
            }
            
            // Calculate pending plans (same logic as in the command)
            $level2Count = \App\IjazasanadMemorizationPlan::has('student')
                ->has('center')
                ->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();

            $level1Count = \App\IjazasanadMemorizationPlan::has('student')
                ->has('center')
                ->where('status', 'waiting_for_approval')
                ->where(function ($query) {
                    $query->where(function ($subQuery) {
                        $subQuery->whereNotNull('talqeen_from_lesson')
                            ->whereNotNull('talqeen_to_lesson');
                    })
                        ->orWhere(function ($subQuery) {
                            $subQuery->whereNotNull('revision_from_lesson')
                                ->whereNotNull('revision_to_lesson');
                        })
                        ->orWhere(function ($subQuery) {
                            $subQuery->whereNotNull('jazariyah_from_lesson')
                                ->whereNotNull('jazariyah_to_lesson');
                        })
                        ->orWhere(function ($subQuery) {
                            $subQuery->whereNotNull('seminars_from_lesson')
                                ->whereNotNull('seminars_to_lesson');
                        });
                })
                ->count();

            $totalPending = $level1Count + $level2Count;
            
            if ($totalPending === 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'There are currently no pending plans awaiting approval. Nothing to remind about.',
                    'error' => 'No pending plans found in the system'
                ]);
            }
            
            // Now actually send the test email
            $emailService = app(\App\Services\EmailService::class);
            
            $to = [
                'email' => $employee->email,
                'name' => $employee->name,
            ];
            
            $subject = '[TEST] Ijazasanad Pending Plans Awaiting Your Approval';
            $view = 'emails.pending_plans_reminder';
            $viewData = [
                'employee' => $employee,
                'level1Count' => $level1Count,
                'level2Count' => $level2Count,
                'isTestEmail' => true, // Flag to indicate this is a test email
            ];
            
            // Attempt to send the email
            $emailService->sendEmail($to, $subject, $view, $viewData);
            
            // Log the successful test email
            $logEntry = IjazasanadEmailLog::logSuccess(
                $employee->id,
                $employee->email,
                $subject
            );
            
            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully! Check the inbox for ' . $testEmail,
                'subject' => $subject,
                'timestamp' => $logEntry->created_at->format('Y-m-d H:i:s'),
                'plans' => [
                    'level1' => $level1Count,
                    'level2' => $level2Count,
                    'total' => $totalPending
                ]
            ]);
            
        } catch (\Exception $e) {
            // Log the error
            Log::error("[IJAZASANAD TEST] Failed to send test email: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            // Log the failure
            if (isset($employee)) {
                IjazasanadEmailLog::logFailure(
                    $employee->id ?? null,
                    $testEmail,
                    $subject ?? '[TEST] Ijazasanad Pending Plans',
                    $e->getMessage()
                );
            }
            
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email.',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Generate database documentation manually
     * 
     * @return \Illuminate\Http\RedirectResponse
     */
    public function generateDatabaseDocs()
    {
        try {
            // Record start time for performance tracking
            $startTime = microtime(true);
            
            // Log that this was manually triggered
            Log::info("[DB DOCS] *** Manually triggered database documentation generation by user ID: " . auth()->id() . " ***");
            
            // Execute the database documentation command
            $exitCode = \Artisan::call('db:docs');
            
            // Also run the fix script to ensure all tables are included
            $fixScript = base_path('ai_docs/architectureDiagrams/fix_db_docs.php');
            if (file_exists($fixScript)) {
                Log::info("[DB DOCS] Running additional fix script to ensure all tables are included");
                shell_exec('php ' . $fixScript . ' 2>&1');
            }
            
            // Calculate execution time
            $executionTime = round(microtime(true) - $startTime, 2);
            
            // Get the command output
            $output = \Artisan::output();
            
            if ($exitCode === 0) {
                $message = "Database documentation generated successfully in {$executionTime}s.";
                Log::info("[DB DOCS] " . $message);
                
                // Redirect back with success message and tab identifier
                return redirect()
                    ->route('general.commands.dbdocs.view')
                    ->with('success', $message);
            } else {
                $message = "Database documentation generation failed with exit code {$exitCode}.";
                Log::error("[DB DOCS] " . $message);
                Log::error("[DB DOCS] Command output: " . $output);
                
                return redirect()
                    ->route('general.commands.home')
                    ->with('error', $message . " Check logs for details.")
                    ->with('active_tab', 'dbDocs');
            }
        } catch (\Exception $e) {
            $message = "Error generating database documentation: " . $e->getMessage();
            Log::error("[DB DOCS] " . $message);
            Log::error("[DB DOCS] Exception trace: " . $e->getTraceAsString());
            
            return redirect()
                ->route('general.commands.home')
                ->with('error', $message)
                ->with('active_tab', 'dbDocs');
        }
    }
    
    /**
     * View the database documentation within the Laravel app
     * 
     * @return \Illuminate\View\View
     */
    public function viewDatabaseDocs()
    {
        try {
            $docPath = base_path('ai_docs/architectureDiagrams/complete_database_documentation_with_tables.html');
            
            // Check if documentation exists
            if (!file_exists($docPath)) {
                return redirect()
                    ->route('general.commands.home')
                    ->with('error', 'Database documentation not found. Please generate it first.')
                    ->with('active_tab', 'dbDocs');
            }
            
            // Get the last modified time of the documentation
            $lastUpdated = date('Y-m-d H:i:s', filemtime($docPath));
            
            // Read the HTML content
            $htmlContent = file_get_contents($docPath);
            
            // Get current database tables directly from database
            $tableLinks = [];
            try {
                // Get database connection details from config
                $connection = config('database.default');
                $database = config("database.connections.{$connection}.database");
                
                // Query database for all tables
                $tables = DB::select('SHOW TABLES');
                $columnName = "Tables_in_" . $database;
                
                foreach ($tables as $table) {
                    $tableName = $table->$columnName;
                    $tableLinks["table-{$tableName}"] = $tableName;
                }
                
                ksort($tableLinks); // Sort alphabetically
                
                Log::info("[DB DOCS] Retrieved " . count($tableLinks) . " tables directly from database");
            } catch (\Exception $e) {
                Log::error("[DB DOCS] Error getting tables from database: " . $e->getMessage());
                
                // Fallback: Extract table links from the HTML content if direct DB query fails
                if (preg_match_all('/<div class="table-container" id="(table-[^"]+)">\\s*<h3>([^<]+)<\/h3>/i', $htmlContent, $matches, PREG_SET_ORDER)) {
                    foreach ($matches as $match) {
                        $tableLinks[$match[1]] = $match[2];
                    }
                }
            }
            
            // Extract table content
            $tableContent = '';
            if (preg_match('/<div id="tables-section">.*?<h2[^>]*>Table Definitions<\/h2>(.*?)<\/div>\\s*<\/div>\\s*<\/div>/s', $htmlContent, $contentMatch)) {
                $tableContent = $contentMatch[1];
            } else if (preg_match('/<div id="tables-container">(.*?)<\/div>\\s*<script>/s', $htmlContent, $contentMatch)) {
                $tableContent = $contentMatch[1];
            } else {
                // If we can't extract the content, use the whole file but strip the header/footer
                $tableContent = preg_replace('/<html.*?<body[^>]*>/s', '', $htmlContent);
                $tableContent = preg_replace('/<\/body>.*?<\/html>/s', '', $tableContent);
                $tableContent = preg_replace('/<script.*?<\/script>/s', '', $tableContent);
            }
            
            // Render the documentation view
            return view('general::commands.database_documentation', [
                'tableLinks' => $tableLinks,
                'tableContent' => $tableContent,
                'lastUpdated' => $lastUpdated
            ]);
        } catch (\Exception $e) {
            Log::error("[DB DOCS] Error viewing database documentation: " . $e->getMessage());
            
            return redirect()
                ->route('general.commands.home')
                ->with('error', 'Error viewing database documentation: ' . $e->getMessage())
                ->with('active_tab', 'dbDocs');
        }
    }
}

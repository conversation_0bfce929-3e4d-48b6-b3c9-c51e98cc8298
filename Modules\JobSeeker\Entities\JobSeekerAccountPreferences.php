<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

final class JobSeekerAccountPreferences extends Model
{
    use HasFactory;

    protected $table = 'jobseeker_account_preferences';

    protected $fillable = [
        'jobseeker_id',
        'theme_preference',
        'language',
        'timezone',
        'date_format',
        'time_format',
        'currency_display',
        'sidebar_collapsed',
        'dashboard_layout',
        'notification_frequency',
        'email_digest_frequency',
        'auto_save_applications',
        'show_salary_in_listings',
        'show_company_ratings',
        'job_search_radius',
        'default_job_search_filters',
        'profile_completion_reminders',
        'application_deadline_reminders',
        'interview_reminders',
    ];

    protected $casts = [
        'sidebar_collapsed' => 'boolean',
        'dashboard_layout' => 'array',
        'auto_save_applications' => 'boolean',
        'show_salary_in_listings' => 'boolean',
        'show_company_ratings' => 'boolean',
        'job_search_radius' => 'integer',
        'default_job_search_filters' => 'array',
        'profile_completion_reminders' => 'boolean',
        'application_deadline_reminders' => 'boolean',
        'interview_reminders' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the jobseeker that owns the account preferences.
     */
    public function jobSeeker(): BelongsTo
    {
        return $this->belongsTo(JobSeeker::class, 'jobseeker_id');
    }

    /**
     * Get formatted date according to user preference.
     */
    public function formatDate(\DateTime $date): string
    {
        return $date->format($this->date_format ?? 'Y-m-d');
    }

    /**
     * Get formatted time according to user preference.
     */
    public function formatTime(\DateTime $time): string
    {
        $format = $this->time_format === '12h' ? 'g:i A' : 'H:i';
        return $time->format($format);
    }

    /**
     * Get formatted datetime according to user preferences.
     */
    public function formatDateTime(\DateTime $datetime): string
    {
        return $this->formatDate($datetime) . ' ' . $this->formatTime($datetime);
    }

    /**
     * Check if user prefers dark theme.
     */
    public function prefersDarkTheme(): bool
    {
        return $this->theme_preference === 'dark';
    }

    /**
     * Check if user prefers system theme.
     */
    public function prefersSystemTheme(): bool
    {
        return $this->theme_preference === 'system';
    }

    /**
     * Get the effective theme (resolving 'system' preference).
     */
    public function getEffectiveTheme(): string
    {
        if ($this->theme_preference === 'system') {
            // In a real application, you might detect system preference
            // For now, default to light
            return 'light';
        }
        
        return $this->theme_preference ?? 'light';
    }

    /**
     * Scope to filter by theme preference.
     */
    public function scopeTheme($query, string $theme)
    {
        return $query->where('theme_preference', $theme);
    }

    /**
     * Scope to filter by language.
     */
    public function scopeLanguage($query, string $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Scope to filter by timezone.
     */
    public function scopeTimezone($query, string $timezone)
    {
        return $query->where('timezone', $timezone);
    }
} 
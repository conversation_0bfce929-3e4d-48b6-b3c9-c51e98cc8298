#!/bin/bash

# Script to generate modular ERD PlantUML files for different database modules

DIAGRAMS_DIR="ai_docs/architectureDiagrams"

# Core System Module
cat > "$DIAGRAMS_DIR/db_module_core.puml" << 'EOL'
@startuml Core System Module Schema

!theme vibrant

entity "users" {
  * id: int
  --
  full_name: varchar(250)
  email: varchar(250)
  password: varchar(100)
  usertype: varchar(210)
  organization_id: int
}

entity "roles" {
  * id: int
  --
  name: varchar(255)
  guard_name: varchar(255)
}

entity "permissions" {
  * id: int
  --
  name: varchar(255)
  guard_name: varchar(255)
}

entity "model_has_roles" {
  * role_id: int <<FK>>
  * model_type: varchar(255)
  * model_id: int
}

entity "model_has_permissions" {
  * permission_id: int <<FK>>
  * model_type: varchar(255)
  * model_id: int
}

entity "role_has_permissions" {
  * permission_id: int <<FK>>
  * role_id: int <<FK>>
}

entity "settings" {
  * id: int
  --
  key: varchar(255)
  value: text
}

entity "modules" {
  * id: int
  --
  name: varchar(255)
  status: tinyint
}

entity "module_permissions" {
  * id: int
  --
  module_id: int <<FK>>
  name: varchar(255)
}

users ||--o{ model_has_roles : "has"
users ||--o{ model_has_permissions : "has"
roles ||--o{ model_has_roles : "assigned to"
roles ||--o{ role_has_permissions : "has"
permissions ||--o{ model_has_permissions : "assigned to"
permissions ||--o{ role_has_permissions : "granted to"
modules ||--o{ module_permissions : "has"

@enduml
EOL

# Academic Module
cat > "$DIAGRAMS_DIR/db_module_academic.puml" << 'EOL'
@startuml Academic Module Schema

!theme vibrant

entity "students" {
  * id: int
  --
  user_id: int <<FK>>
  guardian_id: int <<FK>>
  full_name: varchar(255)
  student_number: varchar(255)
  email: varchar(255)
  organization_id: int
}

entity "guardians" {
  * id: int
  --
  user_id: int <<FK>>
  full_name: varchar(255)
  email: varchar(255)
  organization_id: int
}

entity "academic_years" {
  * id: int
  --
  name: varchar(255)
  start_date: date
  end_date: date
  organization_id: int
}

entity "classes" {
  * id: int
  --
  class_code: varchar(255)
  center_id: int <<FK>>
  subject_id: int <<FK>>
  organization_id: int
}

entity "subjects" {
  * id: int
  --
  name: varchar(255)
  code: varchar(255)
  organization_id: int
}

entity "class_students" {
  * id: int
  --
  class_id: int <<FK>>
  student_id: int <<FK>>
  academic_year_id: int <<FK>>
  organization_id: int
}

entity "centers" {
  * id: int
  --
  name: varchar(255)
  code: varchar(255)
  organization_id: int
}

entity "programs" {
  * id: int
  --
  name: varchar(255)
  code: varchar(255)
  organization_id: int
}

entity "center_programs" {
  * id: int
  --
  center_id: int <<FK>>
  program_id: int <<FK>>
}

entity "class_programs" {
  * id: int
  --
  class_id: int <<FK>>
  program_id: int <<FK>>
}

entity "program_levels" {
  * id: int
  --
  program_id: int <<FK>>
  name: varchar(255)
}

entity "student_program_levels" {
  * id: int
  --
  student_id: int <<FK>>
  program_level_id: int <<FK>>
}

students }o--|| guardians : "has guardian"
classes }o--|| centers : "belongs to"
classes }o--|| subjects : "has subject"
students }o--o{ class_students : "enrolled in"
classes }o--o{ class_students : "has students"
academic_years ||--o{ class_students : "has"

centers }o--o{ center_programs : "offers"
programs }o--o{ center_programs : "offered at"
classes }o--o{ class_programs : "has"
programs }o--o{ class_programs : "included in"
programs ||--o{ program_levels : "has"
students }o--o{ student_program_levels : "enrolled in"
program_levels ||--o{ student_program_levels : "has students"

@enduml
EOL

# Teaching Module
cat > "$DIAGRAMS_DIR/db_module_teaching.puml" << 'EOL'
@startuml Teaching Module Schema

!theme vibrant

entity "employees" {
  * id: int
  --
  user_id: int <<FK>>
  department_id: int <<FK>>
  full_name: varchar(255)
  employee_number: varchar(255)
  email: varchar(255)
  organization_id: int
}

entity "employee_department" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "class_teachers" {
  * id: int
  --
  class_id: int <<FK>>
  employee_id: int <<FK>>
  organization_id: int
}

entity "class_teacher_subjects" {
  * id: int
  --
  class_teacher_id: int <<FK>>
  subject_id: int <<FK>>
}

entity "classes" {
  * id: int
  --
  class_code: varchar(255)
  subject_id: int <<FK>>
  organization_id: int
}

entity "subjects" {
  * id: int
  --
  name: varchar(255)
  code: varchar(255)
  organization_id: int
}

entity "employee_timetables" {
  * id: int
  --
  employee_id: int <<FK>>
  day: varchar(255)
  start_time: time
  end_time: time
}

entity "class_timetable" {
  * id: int
  --
  class_id: int <<FK>>
  day: varchar(255)
  start_time: time
  end_time: time
}

employees }o--|| employee_department : "belongs to"
employees }o--o{ class_teachers : "teaches"
classes }o--o{ class_teachers : "has teachers"
classes }o--|| subjects : "has subject"
class_teachers }o--o{ class_teacher_subjects : "teaches subject"
employees ||--o{ employee_timetables : "has schedule"
classes ||--o{ class_timetable : "has schedule"

@enduml
EOL

# Attendance Module
cat > "$DIAGRAMS_DIR/db_module_attendance.puml" << 'EOL'
@startuml Attendance Module Schema

!theme vibrant

entity "attendances" {
  * id: int
  --
  student_id: int <<FK>>
  employee_id: int <<FK>>
  class_id: int <<FK>>
  subject_id: int <<FK>>
  date: date
  status: varchar(255)
  organization_id: int
}

entity "attendance_options" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "student_attendances" {
  * id: int
  --
  student_id: int <<FK>>
  attendance_type: varchar(255)
  date: date
  notes: text
}

entity "students" {
  * id: int
  --
  full_name: varchar(255)
  organization_id: int
}

entity "employees" {
  * id: int
  --
  full_name: varchar(255)
  organization_id: int
}

entity "classes" {
  * id: int
  --
  class_code: varchar(255)
  organization_id: int
}

entity "subjects" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "holidays" {
  * id: int
  --
  title: varchar(255)
  from_date: date
  to_date: date
  organization_id: int
}

entity "public_holidays" {
  * id: int
  --
  title: varchar(255)
  date: date
  organization_id: int
}

attendances }o--|| students : "for student"
attendances }o--|| employees : "by employee"
attendances }o--|| classes : "in class"
attendances }o--|| subjects : "for subject"
student_attendances }o--|| students : "for student"

@enduml
EOL

# Finance Module
cat > "$DIAGRAMS_DIR/db_module_finance.puml" << 'EOL'
@startuml Finance Module Schema

!theme vibrant

entity "fees_masters" {
  * id: int
  --
  fees_type_id: int <<FK>>
  class_id: int <<FK>>
  amount: decimal
  academic_year_id: int <<FK>>
  organization_id: int
}

entity "fees_types" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "fees_assigns" {
  * id: int
  --
  fees_master_id: int <<FK>>
  student_id: int <<FK>>
  due_date: date
  amount: decimal
  organization_id: int
}

entity "fees_discounts" {
  * id: int
  --
  name: varchar(255)
  amount: decimal
  organization_id: int
}

entity "fees_assign_discounts" {
  * id: int
  --
  fees_assign_id: int <<FK>>
  fees_discount_id: int <<FK>>
  organization_id: int
}

entity "fees_payments" {
  * id: int
  --
  fees_assign_id: int <<FK>>
  student_id: int <<FK>>
  amount: decimal
  payment_date: date
  organization_id: int
}

entity "students" {
  * id: int
  --
  full_name: varchar(255)
  organization_id: int
}

entity "classes" {
  * id: int
  --
  class_code: varchar(255)
  organization_id: int
}

entity "academic_years" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "chart_accounts" {
  * id: int
  --
  name: varchar(255)
  type: varchar(255)
  organization_id: int
}

fees_masters }o--|| fees_types : "has type"
fees_masters }o--|| classes : "for class"
fees_masters }o--|| academic_years : "for academic year"
fees_masters ||--o{ fees_assigns : "assigned to students"
fees_assigns }o--|| students : "for student"
fees_assigns ||--o{ fees_assign_discounts : "has discounts"
fees_discounts ||--o{ fees_assign_discounts : "applied to"
fees_assigns ||--o{ fees_payments : "has payments"
fees_payments }o--|| students : "by student"

@enduml
EOL

# HR Module
cat > "$DIAGRAMS_DIR/db_module_hr.puml" << 'EOL'
@startuml HR Module Schema

!theme vibrant

entity "employees" {
  * id: int
  --
  user_id: int <<FK>>
  department_id: int <<FK>>
  full_name: varchar(255)
  employee_number: varchar(255)
  email: varchar(255)
  organization_id: int
}

entity "employee_department" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "employee_salaries" {
  * id: int
  --
  employee_id: int <<FK>>
  amount: decimal
  effective_date: date
  organization_id: int
}

entity "payrolls" {
  * id: int
  --
  employee_id: int <<FK>>
  amount: decimal
  payment_date: date
  organization_id: int
}

entity "payroll_earn_deducs" {
  * id: int
  --
  payroll_id: int <<FK>>
  type: varchar(255)
  amount: decimal
  organization_id: int
}

entity "leave_types" {
  * id: int
  --
  name: varchar(255)
  total_days: int
  organization_id: int
}

entity "leave_defines" {
  * id: int
  --
  employee_id: int <<FK>>
  leave_type_id: int <<FK>>
  days: int
  organization_id: int
}

entity "apply_leaves" {
  * id: int
  --
  employee_id: int <<FK>>
  leave_type_id: int <<FK>>
  from_date: date
  to_date: date
  reason: text
  status: varchar(255)
  organization_id: int
}

employees }o--|| employee_department : "belongs to"
employees ||--o{ employee_salaries : "has"
employees ||--o{ payrolls : "receives"
payrolls ||--o{ payroll_earn_deducs : "has components"
leave_types ||--o{ leave_defines : "defined for"
leave_defines }o--|| employees : "for employee"
apply_leaves }o--|| employees : "requested by"
apply_leaves }o--|| leave_types : "of type"

@enduml
EOL

# Quran Studies Module
cat > "$DIAGRAMS_DIR/db_module_quran.puml" << 'EOL'
@startuml Quran Studies Module Schema

!theme vibrant

entity "students" {
  * id: int
  --
  full_name: varchar(255)
  organization_id: int
}

entity "hefz_levels" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "student_hefz_plans" {
  * id: int
  --
  student_id: int <<FK>>
  surah_id: int <<FK>>
  start_verse: int
  end_verse: int
  organization_id: int
}

entity "student_hefz_report" {
  * id: int
  --
  student_id: int <<FK>>
  plan_id: int <<FK>>
  report_date: date
  grade: varchar(255)
  notes: text
  organization_id: int
}

entity "student_revision_plans" {
  * id: int
  --
  student_id: int <<FK>>
  surah_id: int <<FK>>
  start_verse: int
  end_verse: int
  organization_id: int
}

entity "student_revision_report" {
  * id: int
  --
  student_id: int <<FK>>
  plan_id: int <<FK>>
  report_date: date
  grade: varchar(255)
  notes: text
  organization_id: int
}

entity "moshaf" {
  * id: int
  --
  name: varchar(255)
  type: varchar(255)
  organization_id: int
}

entity "moshaf_surah" {
  * id: int
  --
  moshaf_id: int <<FK>>
  name: varchar(255)
  number: int
  verses_count: int
}

entity "moshaf_juz" {
  * id: int
  --
  moshaf_id: int <<FK>>
  number: int
  name: varchar(255)
}

entity "moshaf_pages" {
  * id: int
  --
  moshaf_id: int <<FK>>
  page_number: int
}

entity "surah" {
  * id: int
  --
  name: varchar(255)
  number: int
  verses_count: int
}

students ||--o{ student_hefz_plans : "has"
students ||--o{ student_revision_plans : "has"
student_hefz_plans ||--o{ student_hefz_report : "evaluated in"
student_revision_plans ||--o{ student_revision_report : "evaluated in"
moshaf ||--o{ moshaf_surah : "contains"
moshaf ||--o{ moshaf_juz : "contains"
moshaf ||--o{ moshaf_pages : "has"
moshaf_surah }o--|| surah : "references"
student_hefz_plans }o--|| surah : "memorizes"
student_revision_plans }o--|| surah : "revises"

@enduml
EOL

echo "Modular ERD PlantUML files have been generated in $DIAGRAMS_DIR/"
echo "You can now use a PlantUML renderer to convert these files to diagrams." 
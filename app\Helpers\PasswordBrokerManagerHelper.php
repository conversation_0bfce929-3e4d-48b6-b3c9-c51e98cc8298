<?php


namespace App\Helpers;


use Closure;
use Illuminate\Auth\Passwords\DatabaseTokenRepository;
use Illuminate\Auth\Passwords\PasswordBroker;
use Illuminate\Support\Str;

class PasswordBrokerManagerHelper extends \Illuminate\Auth\Passwords\PasswordBrokerManager
{

    /**
     * @param array $credentials
     * @param Closure|null $callback
     * @return void
     */
    public function sendResetLink(array $credentials, Closure $callback = null)
    {
        // TODO: Implement sendResetLink() method.
    }

    /**
     * @inheritDoc
     */
    public function reset(array $credentials, Closure $callback)
    {
        // TODO: Implement reset() method.
    }

    /**
     * @inheritDoc
     */
    public function validator(Closure $callback)
    {
        // TODO: Implement validator() method.
    }

    /**
     * @inheritDoc
     */
    public function validateNewPassword(array $credentials)
    {
        // TODO: Implement validateNewPassword() method.
    }

    /**
     * Resolve the given broker.
     *
     * @param  string  $name
     * @return \Illuminate\Contracts\Auth\PasswordBroker
     *
     * @throws \InvalidArgumentException
     */
    protected function resolve($name)
    {

        $config = $this->getConfig($name);

        if (is_null($config)) {
            throw new \InvalidArgumentException("Password resetter [{$name}] is not defined.");
        }

        // The password broker uses a token repository to validate tokens and send user
        // password e-mails, as well as validating that password reset process as an
        // aggregate service of sorts providing a convenient interface for resets.
        return new PasswordBroker(
            $this->createTokenRepository($config),
            $this->app['auth']->createUserProvider($config['provider'])
        );
    }



    protected function createTokenRepository(array $config)
    {
        $key = $this->app['config']['app.key'];

        if (Str::startsWith($key, 'base64:')) {
            $key = base64_decode(substr($key, 7));
        }


        $connection = isset($config['connection']) ? $config['connection'] : null;

//        return new DatabaseTokenRepository(
        return new ForgotPasswordHelperRepository(
            $this->app['db']->connection($connection),
            $this->app['hash'],
            $config['table'],
            $key,
            $config['expire']
        );
    }
}
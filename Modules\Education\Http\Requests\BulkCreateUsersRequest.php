<?php

namespace Modules\Education\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BulkCreateUsersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'student_ids' => 'required|array|min:1',
            'student_ids.*' => 'required|integer|exists:students,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'student_ids.required' => 'Please select at least one student.',
            'student_ids.array' => 'Student IDs must be provided as an array.',
            'student_ids.min' => 'Please select at least one student.',
            'student_ids.*.required' => 'Each student ID is required.',
            'student_ids.*.integer' => 'Student IDs must be integers.',
            'student_ids.*.exists' => 'One or more selected students do not exist.',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Additional validation can be added here if needed
            // For example, checking if students have required data for user creation
            $studentIds = $this->student_ids ?? [];
            
            if (!empty($studentIds)) {
                $studentsWithoutEmail = \App\Student::whereIn('id', $studentIds)
                    ->where(function($query) {
                        $query->whereNull('email')
                              ->orWhere('email', '');
                    })
                    ->count();
                
                if ($studentsWithoutEmail > 0) {
                    $validator->errors()->add('students', "Cannot create users for {$studentsWithoutEmail} student(s) without email addresses.");
                }
            }
        });
    }
}

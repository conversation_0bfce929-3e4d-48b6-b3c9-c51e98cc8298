<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Cache;

/**
 * JobSeeker Setting Model
 * 
 * Stores dynamic configuration settings for the JobSeeker module,
 * allowing runtime configuration changes without code deployments.
 */
final class JobSeekerSetting extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'jobseeker_settings';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'key',
        'value',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Cache duration for settings (in minutes)
     * 
     * @var int
     */
    protected static int $cacheDuration = 60;

    /**
     * Get a setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getValue(string $key, $default = null)
    {
        $cacheKey = "jobseeker_setting_{$key}";
        
        return Cache::remember($cacheKey, self::$cacheDuration, function () use ($key, $default) {
            $setting = self::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * Set a setting value by key
     *
     * @param string $key
     * @param mixed $value
     * @return self
     */
    public static function setValue(string $key, $value): self
    {
        $setting = self::updateOrCreate(
            ['key' => $key],
            ['value' => $value]
        );

        // Clear cache for this setting
        Cache::forget("jobseeker_setting_{$key}");

        return $setting;
    }

    /**
     * Get multiple settings by keys
     *
     * @param array $keys
     * @return array
     */
    public static function getMultiple(array $keys): array
    {
        $settings = [];
        foreach ($keys as $key) {
            $settings[$key] = self::getValue($key);
        }
        return $settings;
    }

    /**
     * Get all email provider settings
     *
     * @return array
     */
    public static function getEmailProviderSettings(): array
    {
        return self::getMultiple([
            'email_provider',
            'email_sending_mode',
            'gmail_host',
            'gmail_port',
            'gmail_encryption',
            'gmail_username',
            'gmail_password',
            'mailtrap_api_key',
            'mailtrap_inbox_id',
        ]);
    }

    /**
     * Get current email provider
     *
     * @return string
     */
    public static function getCurrentEmailProvider(): string
    {
        return self::getValue('email_provider', 'gmail');
    }

    /**
     * Get current email sending mode
     *
     * @return string
     */
    public static function getCurrentEmailMode(): string
    {
        return self::getValue('email_sending_mode', 'sync');
    }

    /**
     * Check if current provider is Gmail
     *
     * @return bool
     */
    public static function isGmailProvider(): bool
    {
        return self::getCurrentEmailProvider() === 'gmail';
    }

    /**
     * Check if current provider is Mailtrap
     *
     * @return bool
     */
    public static function isMailtrapProvider(): bool
    {
        return self::getCurrentEmailProvider() === 'mailtrap';
    }

    /**
     * Check if current mode is synchronous
     *
     * @return bool
     */
    public static function isSyncMode(): bool
    {
        return self::getCurrentEmailMode() === 'sync';
    }

    /**
     * Check if current mode is asynchronous
     *
     * @return bool
     */
    public static function isAsyncMode(): bool
    {
        return self::getCurrentEmailMode() === 'async';
    }

    /**
     * Get Gmail configuration
     *
     * @return array
     */
    public static function getGmailConfig(): array
    {
        return [
            'host' => self::getValue('gmail_host', 'smtp.gmail.com'),
            'port' => (int) self::getValue('gmail_port', 587),
            'encryption' => self::getValue('gmail_encryption', 'tls'),
            'username' => self::getValue('gmail_username', ''),
            'password' => self::getValue('gmail_password', ''),
        ];
    }

    /**
     * Get Mailtrap configuration
     *
     * @return array
     */
    public static function getMailtrapConfig(): array
    {
        return [
            'api_key' => self::getValue('mailtrap_api_key', ''),
            'inbox_id' => self::getValue('mailtrap_inbox_id', ''),
        ];
    }

    /**
     * Get the email configuration from the new JSON format
     *
     * @return array
     */
    public static function getEmailConfiguration(): array
    {
        $configJson = self::getValue('email_configuration');
        
        if (empty($configJson)) {
            // Fallback to legacy individual settings if JSON config doesn't exist
            return [
                'mode' => self::getValue('email_sending_mode', 'sync'),
                'providers' => [self::getValue('email_provider', 'gmail')],
            ];
        }

        try {
            $config = json_decode($configJson, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Invalid JSON in email configuration');
            }

            // Ensure required fields exist with defaults
            return [
                'mode' => $config['mode'] ?? 'sync',
                'providers' => $config['providers'] ?? ['gmail'],
            ];
        } catch (\Exception $e) {
            // Log error and return safe defaults
            \Log::warning('JobSeekerSetting: Failed to parse email configuration JSON', [
                'config_value' => $configJson,
                'error' => $e->getMessage(),
            ]);

            return [
                'mode' => 'sync',
                'providers' => ['gmail'],
            ];
        }
    }

    /**
     * Update the email configuration in JSON format
     *
     * @param string $mode
     * @param array $providers
     * @return self
     */
    public static function setEmailConfiguration(string $mode, array $providers): self
    {
        $config = [
            'mode' => $mode,
            'providers' => array_values($providers), // Ensure sequential array
        ];

        $configJson = json_encode($config, JSON_UNESCAPED_SLASHES);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Failed to encode email configuration to JSON: ' . json_last_error_msg());
        }

        return self::setValue('email_configuration', $configJson);
    }

    /**
     * Get the current email sending mode from new configuration
     *
     * @return string
     */
    public static function getCurrentEmailModeFromConfig(): string
    {
        $config = self::getEmailConfiguration();
        return $config['mode'] ?? 'sync';
    }

    /**
     * Get the prioritized list of email providers
     *
     * @return array
     */
    public static function getEmailProvidersPriority(): array
    {
        $config = self::getEmailConfiguration();
        return $config['providers'] ?? ['gmail'];
    }

    /**
     * Get the primary (highest priority) email provider
     *
     * @return string
     */
    public static function getPrimaryEmailProvider(): string
    {
        $providers = self::getEmailProvidersPriority();
        return $providers[0] ?? 'gmail';
    }

    /**
     * Update provider priority order
     *
     * @param array $providers
     * @return self
     */
    public static function updateProviderPriority(array $providers): self
    {
        $currentConfig = self::getEmailConfiguration();
        return self::setEmailConfiguration($currentConfig['mode'], $providers);
    }

    /**
     * Check if the new JSON configuration format is being used
     *
     * @return bool
     */
    public static function isUsingJsonConfiguration(): bool
    {
        $configJson = self::getValue('email_configuration');
        return !empty($configJson);
    }

    /**
     * Get the configuration schema version
     *
     * @return string
     */
    public static function getConfigurationSchemaVersion(): string
    {
        return self::getValue('email_config_schema_version', '1.0');
    }

    /**
     * Clear all settings cache
     *
     * @return void
     */
    public static function clearCache(): void
    {
        $keys = [
            'email_provider',
            'email_sending_mode',
            'email_configuration',
            'email_config_schema_version',
            'gmail_host',
            'gmail_port',
            'gmail_encryption',
            'gmail_username',
            'gmail_password',
            'mailtrap_api_key',
            'mailtrap_inbox_id',
        ];

        foreach ($keys as $key) {
            Cache::forget("jobseeker_setting_{$key}");
        }
    }

    /**
     * Boot the model and register event listeners
     *
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        // Clear cache when a setting is updated or deleted
        static::updated(function ($model) {
            Cache::forget("jobseeker_setting_{$model->key}");
        });

        static::deleted(function ($model) {
            Cache::forget("jobseeker_setting_{$model->key}");
        });
    }
} 
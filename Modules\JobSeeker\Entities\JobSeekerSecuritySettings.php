<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Carbon;

final class JobSeekerSecuritySettings extends Model
{
    use HasFactory;

    protected $table = 'jobseeker_security_settings';

    protected $fillable = [
        'jobseeker_id',
        'two_factor_enabled',
        'login_notifications',
        'email_notifications',
        'push_notifications',
        'job_alert_notifications',
        'session_timeout',
        'last_password_change',
        'failed_login_attempts',
        'last_login_ip',
        'last_login_user_agent',
    ];

    protected $casts = [
        'two_factor_enabled' => 'boolean',
        'login_notifications' => 'boolean',
        'email_notifications' => 'boolean',
        'push_notifications' => 'boolean',
        'job_alert_notifications' => 'boolean',
        'session_timeout' => 'integer',
        'last_password_change' => 'datetime',
        'failed_login_attempts' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the jobseeker that owns the security settings.
     */
    public function jobSeeker(): BelongsTo
    {
        return $this->belongsTo(JobSeeker::class, 'jobseeker_id');
    }

    /**
     * Update last login information.
     */
    public function updateLastLogin(string $ipAddress, string $userAgent): void
    {
        $this->update([
            'last_login_ip' => $ipAddress,
            'last_login_user_agent' => $userAgent,
            'failed_login_attempts' => 0,
        ]);
    }

    /**
     * Reset failed login attempts.
     */
    public function resetFailedAttempts(): void
    {
        $this->update(['failed_login_attempts' => 0]);
    }
} 
@startuml HR Module Schema

!theme vibrant

entity "employees" {
  * id: int
  --
  user_id: int <<FK>>
  department_id: int <<FK>>
  full_name: varchar(255)
  employee_number: varchar(255)
  email: varchar(255)
  organization_id: int
}

entity "employee_department" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "employee_salaries" {
  * id: int
  --
  employee_id: int <<FK>>
  amount: decimal
  effective_date: date
  organization_id: int
}

entity "payrolls" {
  * id: int
  --
  employee_id: int <<FK>>
  amount: decimal
  payment_date: date
  organization_id: int
}

entity "payroll_earn_deducs" {
  * id: int
  --
  payroll_id: int <<FK>>
  type: varchar(255)
  amount: decimal
  organization_id: int
}

entity "leave_types" {
  * id: int
  --
  name: varchar(255)
  total_days: int
  organization_id: int
}

entity "leave_defines" {
  * id: int
  --
  employee_id: int <<FK>>
  leave_type_id: int <<FK>>
  days: int
  organization_id: int
}

entity "apply_leaves" {
  * id: int
  --
  employee_id: int <<FK>>
  leave_type_id: int <<FK>>
  from_date: date
  to_date: date
  reason: text
  status: varchar(255)
  organization_id: int
}

employees }o--|| employee_department : "belongs to"
employees ||--o{ employee_salaries : "has"
employees ||--o{ payrolls : "receives"
payrolls ||--o{ payroll_earn_deducs : "has components"
leave_types ||--o{ leave_defines : "defined for"
leave_defines }o--|| employees : "for employee"
apply_leaves }o--|| employees : "requested by"
apply_leaves }o--|| leave_types : "of type"

@enduml

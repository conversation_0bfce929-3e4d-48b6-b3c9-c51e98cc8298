<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ItemSell extends Model
{
    public function roles(){
    	return $this->belongsTo('Modules\RolePermission\Entities\InfixRole', 'role_id', 'id');
    }

    public function staffDetails(){
    	return $this->belongsTo('App\Employee', 'student_employee_id', 'id');
    }

    public function parentsDetails(){
    	return $this->belongsTo('App\Parent', 'student_employee_id', 'id');
    }

    public function studentDetails(){
    	return $this->belongsTo('App\Student', 'student_employee_id', 'id');
    }

    public function paymentMethodName(){
        return $this->belongsTo('App\PaymentMethhod','payment_method','id');
    }

    public function bankName(){
        return $this->belongsTo('App\BankAccount','account_id','id');
    }
}

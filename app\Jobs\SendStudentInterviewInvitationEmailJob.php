<?php

namespace App\Jobs;

use App\Mail\OrderShipped;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Mail\SendEmailTest;
use Mail;
use Illuminate\Contracts\Mail\Mailer;

class SendStudentInterviewInvitation<PERSON>mailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    protected $user_info = [];
    protected $sender;
    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($token)
    {
        $this->token = $token;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
//    public function handle(\Illuminate\Support\Facades\Mail $mailer)
    public function handle()
    {

        // TODO: this email-sending feature should be implemented in a separate class, not here
//        $mj = new \Mailjet\Client(env('MAILJETAPIKEY'), env('MAILJETAPISECRET'), true, ['version' => 'v3.1']);
//        $body = [
//            'Messages' => [
//                [
//                    'From' => [
//                        'Email' => "<EMAIL>",
//                        'Name' => "ITQAN"
//                    ],
//                    'To' => [
//                        [
//                            'Email' => $this->user_info['email'],
//                            'Name' => $this->user_info['studentName']
//                        ]
//                    ],
//                    'Subject' => 'Interview Invitation',
//                    'TextPart' => "You are receiving this email because we received a password reset request for your account.\n\nIf you did not request a password reset, no further action is required.",
//                    'HTMLPart' => "<p>You are receiving this email because we received a password reset request for your account.</p><p><a href='".url('workplace/password/reset', $this->token)."'>Reset Password</a></p><p>If you did not request a password reset, no further action is required.</p>",
//
//                ]
//            ]
//        ];
//        $response = $mj->post(Resources::$Email, ['body' => $body]);





    }


}

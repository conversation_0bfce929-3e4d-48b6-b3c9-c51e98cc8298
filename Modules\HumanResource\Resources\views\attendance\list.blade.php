@extends('layouts.hound')

@section('mytitle', 'Users')

@section('content')

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.1.0/jquery.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <div class="row">
        <div class="col-md-12">
            <h3 class="modal-title"> {{ \Illuminate\Support\Str::plural('Employee', $result->count()) }} :</h3> <h3  id="total_records">{{ $result->total() }}</h3>
        </div>
    </div>

    <div class="pull-right">
             
            <a href="{{ route('attendance.daily_report') }}" title="Edit class"><button class="btn btn-success btn-xs txt-light">
                  Daily Attendance </button></a>
            <a href="{{ route('attendance.monthly_report') }}" title="Edit class"><button class="btn btn-success btn-xs txt-light">
                    Monthly Attendance </button></a>
                                                                  
                     
        </div>
    <div class="panel-heading ">
       
         
        
    </br>
        </div>

        
    <div class="result-set">
    <div class="form-group">
      <input type="text" name="search" id="search" class="form-control" placeholder="Search For Employee" />
     </div>
        <table class="table table-bordered table-striped table-hover" id="data-table">
            <thead>
            <tr>
                <th>Id</th>
                <th>Name</th>
                <th>Disignation</th>
                <th>Created At</th>
                @can('update employee', 'delete employee')
                <th class="text-center">Actions</th>
                @endcan
            </tr>
            </thead>
            <tbody>
            @foreach($result as $item)
                <tr>
                    <td>{{ $item->id }}</td>
                    <td>{{ $item->full_name ?? $item->name }}</td>
                    <td>{{ $item->roles->implode('description', ', ') }}</td>
                    {{--  @can('view attendance')  --}}
                    <td class="text-center">
                        <a href="{{ route('individual.employee.monthly.attendance', $item->id)}}" class="btn btn-sm btn-primary">View</a>
                    </td>
                    {{--  @endcan  --}}
                </tr>
            @endforeach
            </tbody>
        </table>

        <div class="text-center">
            {{ $result->links() }}
        </div>
    </div>
    <script>
$(document).ready(function(){


 function fetch_customer_data(query = '')
 {
  $.ajax({
   url:"{{ route('attendance.searche') }}",
   method:'GET',
   data:{query:query},
   dataType:'json',
   success:function(data)
   {
    $('tbody').html(data.table_data);
    $('#total_records').text(data.total_data);
   }
  })
 }

 $(document).on('keyup', '#search', function(){
  var query = $(this).val();
  fetch_customer_data(query);
 });
});
</script>

@endsection
<?php

namespace App\Exports;

use App\Student;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\Exportable;

class StudentsWithMissingDataExport implements FromQuery, WithHeadings, WithMapping
{
    use Exportable;

    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
    }

    /**
     * Return the query for students with missing data.
     */
    public function query()
    {
        $query = Student::with(['user', 'admissions.program', 'admissions.center', 'admissions.class'])
            ->orderBy('full_name');

        // Apply the same filters as the DataTable
        // Check if any admission-related filters are applied
        $hasAdmissionFilters = ($this->request && (
            $this->request->filled('program_filter') ||
            $this->request->filled('center_filter') ||
            $this->request->filled('class_filter')
        ));

        if ($hasAdmissionFilters) {
            $query->whereHas('admissions', function ($q) {
                if ($this->request->filled('program_filter')) {
                    $q->where('program_id', $this->request->input('program_filter'));
                }
                if ($this->request->filled('center_filter')) {
                    $q->where('center_id', $this->request->input('center_filter'));
                }
                if ($this->request->filled('class_filter')) {
                    $q->where('class_id', $this->request->input('class_filter'));
                }
            });
        } else {
            // Default behavior: show students without admissions OR students without user accounts
            $query->where(function($q) {
                $q->doesntHave("admissions")
                  ->orWhereDoesntHave('user');
            });
        }

        return $query;
    }

    /**
     * Define the headings for the columns.
     */
    public function headings(): array
    {
        return [
            'Full Name',
            'Email',
            'Gender',
            'Date of Birth',
            'Age',
            'Mobile',
            'Program',
            'Center',
            'Class',
            'Username',
            'Has User Account',
            'Missing Data',
            'Created At'
        ];
    }

    /**
     * Map each student to the corresponding row in the Excel sheet.
     */
    public function map($student): array
    {
        // Get the latest admission
        $admission = $student->admissions->first();
        
        // Calculate age
        $age = $student->date_of_birth ? 
            \Carbon\Carbon::parse($student->date_of_birth)->age : 'N/A';

        // Determine missing data
        $missingData = [];
        if (!$student->gender && !$student->gender_id) {
            $missingData[] = 'Gender';
        }
        if (!$student->date_of_birth) {
            $missingData[] = 'Date of Birth';
        }
        if (!$student->user) {
            $missingData[] = 'User Account';
        }
        if (!$admission) {
            $missingData[] = 'Admission Data';
        }

        return [
            $student->full_name ?? $student->display_name,
            $student->email ?? 'N/A',
            $student->gender ?? ($student->gender_id ? 'Set' : 'Missing'),
            $student->date_of_birth ? 
                \Carbon\Carbon::parse($student->date_of_birth)->format('Y-m-d') : 'Missing',
            $age,
            $student->mobile ?? 'N/A',
            $admission && $admission->program ? $admission->program->name : 'N/A',
            $admission && $admission->center ? $admission->center->name : 'N/A',
            $admission && $admission->class ? $admission->class->name : 'N/A',
            $student->user ? $student->user->username : 'No User',
            $student->user ? 'Yes' : 'No',
            !empty($missingData) ? implode(', ', $missingData) : 'Complete',
            $student->created_at ? $student->created_at->format('Y-m-d H:i:s') : 'N/A'
        ];
    }
}

<section>

	<div id="map2" class="contact-over-map"></div>


	<div class="container">
		<div class="contact-over-box pull-right">
			@if(isset($errors) && $errors->all() )
			<div class="alert alert-danger">
				<ul>
				@foreach($errors->all() as $error)
					<li>{{ $error }}</li>
				@endforeach
				</ul>
			</div>
			@endif
			@include('flash::message')
			{!! Form::open(['method' => 'POST','route' => 'sendContact' ]) !!}
			<fieldset>
				<input type="hidden" name="action" value="contact_send" />

				<div class="row">
					<div class="col-md-12 margin-bottom-20">
						<label for="contact:name">{{ trans('common.full_name') }} *</label>
						<input required type="text" value="" class="form-control" name="name" id="contact:name">
					</div>
					<div class="col-md-12 margin-bottom-20">
						<label for="contact:email">{{ trans('common.email') }} *</label>
						<input required type="email" value="" class="form-control" name="email" id="contact:email">
					</div>
					<div class="col-md-12 margin-bottom-20">
						<label for="contact:phone">{{ trans('common.mobile') }}</label>
						<input type="text" value="" class="form-control" name="phone" id="contact:phone">
					</div>

					<div class="col-md-12 margin-bottom-20">
						<label for="contact:subject">{{ trans('common.subject') }} *</label>
						<input required type="text" value="" class="form-control" name="subject" id="contact:subject">
					</div>
					<div class="col-md-12 margin-bottom-20">
						<label for="contact_department">{{ trans('common.department') }}</label>
						<select class="form-control pointer" name="department">
							
							<option value="{{ trans('common.general') }}">{{ trans('common.general') }}</option> <option value="{{ trans('common.management') }}">{{ trans('common.management') }}</option> <option value="{{ trans('common.education') }}">{{ trans('common.education') }}</option> <option value="{{ trans('common.finance') }}">{{ trans('common.finance') }}</option> <option value="{{ trans('common.technical_support') }}">{{ trans('common.technical_support') }}</option>
							 
							
						</select>
					</div>

					<div class="col-md-12 margin-bottom-20">
						<label for="contact:message">{{ trans('common.message') }} *</label>
						<textarea required maxlength="10000" rows="6" class="form-control" name="message" id="contact:message"></textarea>
					</div>
				</div>

			</fieldset>

			<div class="row">
				<div class="col-md-12">
					<button type="submit" class="btn btn-primary">
						<i class="fa fa-check"></i> {{ trans('common.send') }}</button>
				</div>
			</div>
			{!! Form::close() !!}


		</div>

	</div>
</section>
<!-- / -->
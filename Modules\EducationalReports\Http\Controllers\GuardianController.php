<?php

namespace Modules\EducationalReports\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Mail\GuardianCreated;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use App\Student;
use App\Guardian;

class GuardianController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        $guardians = Guardian::with('students')->get();
        
        return view(("admission::guardian.index"),compact('guardians'));
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view(("admission::guardian.create"));
    }

    /**
     * Show the form for registering  a new student.
     * @return Response
     */
     public function register($id = null)
     {
         if($id){
            $user = [];            
         }else{
             $user = [];
         }
         return view(("admission::guardian.register_student"),compact('user'));
     }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $password = \Illuminate\Support\Str::random(8);
        if(auth()->guard('student')->check() || (auth()->guard('employee')->check() && auth()->user()->can("add guardian"))){
            if(!isset($request->password)){
                $request->merge(['password' => $password ]);
            }else{
                $password = $request->password;
            }

            if(!isset($request->name)){
                $request->merge(['name' => $request->full_name ]);
            }
            $request->merge(['organization_id' => config('organization_id') ]);
            
            $roles = [
                "name" => "required|min:3|max:64",
                "email" => "required|unique:guardians",
            ];
            // return $request->all();

            $this->validate_profile($request ,$roles);

            $request_data = $request->all();
            $request_data['password'] = bcrypt($password);

            $guardian = Guardian::create($request_data);

            if(auth()->guard('student')->check()){
                auth()->user()->guardian_id = $guardian->id;
                auth()->user()->status = 'profile_completed';
                auth()->user()->save();
            }

            Mail::to($guardian)->send(new GuardianCreated($request->all()));
            
        
            flash('Guardian Added !!');

        }else{
            flash('Error!!');

        }
        
        return redirect()->back();
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show($id)
    {
        $guardian = Guardian::findOrFail($id);
        
        return view("admission::guardian.show", compact('guardian'));
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        $guardian = Guardian::findOrFail($id);
        return view("admission::guardian.edit", compact('guardian'));
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(Request $request, $id = null)
    {

        $guardian = Guardian::findOrFail($id);
        
        $request->merge(['status' => 'profile_completed']);
        
        $guardian->fill($request->all());


        $guardian->save();

        flash('Profile updated!!');

        return redirect()->back();

    }

    /**
    * Validate Guardian Profile data entry
    */

    public function validate_profile($request , $roles = [])
    {
        if(config("settings.guardian_form_full_name") == "required"){
            $roles["full_name"] = "required";
        }
        if(config("settings.guardian_form_full_name_trans") == "required"){
            $roles["full_name_trans"] = "required";
        }
        if(config("settings.guardian_form_full_name_language") == "required"){
            $roles["full_name_language"] = "required";
        }
        if(config("settings.guardian_form_gender") == "required"){
            $roles["gender"] = "required";
        }
        if(config("settings.guardian_form_date_of_birth") == "required"){
            $roles["date_of_birth"] = "required";
        }
        if(config("settings.guardian_form_occupation") == "required"){
            $roles["occupation"] = "required";
        }
        if(config("settings.guardian_form_nationality") == "required"){
            $roles["nationality"] = "required";
        }
        if(config("settings.guardian_form_mobile") == "required"){
            $roles["mobile"] = "required";
        }
        $this->validate($request, $roles);        

    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }

    public function live_search(Request $request)
    { 
        if($request->ajax())
        {
          
         $output = '';
         $query = $request->get('query');
         if($query != '')
         {
          $data = Guardian::
            where('full_name', 'like', '%'.$query.'%')
            ->orWhere('email', 'like', '%'.$query.'%')
            ->orWhere('mobile', 'like', '%'.$query.'%')
            ->orWhere('nationality', 'like', '%'.$query.'%')
            ->orderBy('full_name', 'desc')
            ->get();
            
         }
         else
         {
          $data = Guardian::latest()->paginate();

         }
         $total_row = $data->count();
         if($total_row > 0)
         {
          foreach($data as $item)
          {
           $output .= '
           <tr>
           
           <td>'.$item->full_name.' </td>
           <td>'.$item->students->count().' </td>
           <td>'.$item->mobile.' </td>
           <td>'.$item->email.' </td>
           <td>'.$item->nationality.' </td>
           <td>
               <a href="'.route('admission.guardians.show', $item->id).' " class="btn btn-success btn-xs" title="View Center"><span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
               <a href="'.route('admission.guardians.edit',$item->id).' " class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>
               <a href="'.route('admission.guardians.edit',$item->id).' " class="btn btn-danger btn-xs" title="Delete Center"><span class="glyphicon glyphicon-trash" aria-hidden="true" title="Delete Center" /></a>

               
           </td>
       </tr>
           
           ';
          }
         }
         else
         {
          $output = '
          <tr>
           <td align="center" colspan="5">No Data Found</td>
          </tr>
          ';
         }
         $data = array(
          'table_data'  => $output,
          'total_data'  => $total_row
         );
   
         echo json_encode($data);
        }
    }




}



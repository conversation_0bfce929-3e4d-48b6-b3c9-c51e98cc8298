<div class="col-md-12">
@if(isset($payment))
<div class="form-group">
{!! Form::label('name', 'Student Name', ['class' => 'control-label']) !!}
<h4>{{ $payment->student->full_name }}</h4>
<p class="pull-right">Payment Added By <strong>{{ $payment->creator_role }}</strong> for <strong>{{ $payment->payment_category }}</strong> </p>
</div>
@endif
<div class="form-group {{ $errors->has('amount') ? 'has-error' : ''}} link">
    {!! Form::label('amount', 'Paid Amount', ['class' => 'control-label']) !!}
    
        {!! Form::number('amount', null, ['class' => 'form-control' , 'steps' => '0.01']) !!}
        {!! $errors->first('amount', '
        <p class="help-block">
            :message
        </p>
        ') !!}
</div>

<div class="form-group {{ $errors->has('payment_proof') ? 'has-error' : ''}}">
    {!! Form::label('payment_proof', 'Update Proof of Payment', ['class' => 'control-label']) !!}
    <div class="row">
        <div class="col-md-6">
            {!! Form::file('payment_proof' , ['class' => 'form-control']) !!}
        </div>
        <div class="col-md-6">
            @if(isset($payment) && $payment->payment_proof )
            <a id="payment_proof"  href="{{ asset($payment->payment_proof) }}" target="_blank" class="btn btn-primary">
            <i class="fa fa-picture-o"></i> View Proof Of Payment
            </a>
            @endif
        </div>
    </div>
</div>
<div class="form-group {{ $errors->has('transaction_id') ? 'has-error' : ''}} link">
    {!! Form::label('transaction_id', 'Transaction ID', ['class' => 'control-label']) !!}
    
        {!! Form::text('transaction_id', null, ['class' => 'form-control']) !!}
        {!! $errors->first('transaction_id', '
        <p class="help-block">
            :message
        </p>
        ') !!}
</div>

    <div class="form-group {{ $errors->has('method_of_payment') ? 'has-error' : ''}}">
        {!! Form::label('method_of_payment', 'Payment Method', ['class' => 'control-label']) !!}
        
            {!! Form::text('method_of_payment' , null, ['class' => 'form-control']) !!}
            {!! $errors->first('method_of_payment', '
            <p class="help-block alert-danger">
                :message
            </p>
            ') !!}
    </div>

    <div class="form-group {{ $errors->has('approved') ? 'has-error' : ''}}">
        {!! Form::label('approved', 'Verify Payment', ['class' => 'control-label']) !!}
        
            {!! Form::select('approved', [null => '' , 0 => 'Not Valid' , 1 => 'Valid'] , null, ['class' => 'form-control']) !!}
            {!! $errors->first('approved', '
            <p class="help-block alert-danger">
                :message
            </p>
            ') !!}
    </div>
    <div class="form-group {{ $errors->has('notes') ? 'has-error' : ''}} link">
    {!! Form::label('notes', 'Notes', ['class' => 'control-label']) !!}
    
        {!! Form::textarea('notes', null, ['class' => 'form-control']) !!}
        {!! $errors->first('notes', '
        <p class="help-block">
            :message
        </p>
        ') !!}
    </div>
    <div class="form-group">
        <div class="col-md-offset-4" >
            {!! Form::submit(isset($submitButtonText) ? $submitButtonText : 'Create', ['class' => 'btn btn-primary']) !!}
        </div>
    </div>
</div>

@section('js')

<script src="{{ asset('assets/lfm/js/lfm.js') }}"></script>
<script>
    
    $(document).ready(function() {
      $('#image').filemanager('image');
    });

</script>
@endsection
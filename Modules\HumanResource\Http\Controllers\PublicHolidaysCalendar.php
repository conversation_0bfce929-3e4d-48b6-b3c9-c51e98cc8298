<?php

namespace Modules\HumanResource\Http\Controllers;

use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStaatisticsRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class PublicHolidaysCalendar extends Controller
{


    public function __invoke(Request $request)
    {

//        $publicHolidays = collect(DB::select( DB::raw("SELECT a.title, date as start
//from (select concat(year,'-',month_no,'-',day) as date,holiday as title from public_holidays where holiday is not null group by month_name,month_no,day,holiday,year) a
//order by date
//
//")));


        $publicHolidays = collect(DB::select( "SELECT * FROM (SELECT id,a.title, DATE AS start, DATE AS END, holiday_name AS description
FROM (
SELECT holiday_name,id,CONCAT(YEAR,'-',month_no,'-', DAY) AS DATE,
 (
    CASE 
        WHEN holiday_name IS NOT NULL THEN   CONCAT(holiday,' - ',holiday_name)
        ELSE ''
    END) AS title
	
FROM public_holidays
WHERE holiday IS NOT NULL
GROUP BY id,month_name,month_no, DAY,holiday,holiday_name, YEAR) a
ORDER BY DATE) a WHERE title !='' ORDER BY start

"));
        return  response()->json($publicHolidays);




    }
}

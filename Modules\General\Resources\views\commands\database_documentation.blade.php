@extends('layouts.hound')

@section('mytitle', 'Database Documentation')

@section("css")
<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
    }
    
    .container {
        margin: 0 auto;
    }
    
    .table-container {
        background-color: white;
        margin-bottom: 2rem;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
    }
    
    .table-container h3 {
        margin-top: 0;
        color: #2c3e50;
        border-bottom: 2px solid #3498db;
        padding-bottom: 0.5rem;
    }
    
    .table-container h4 {
        margin: 1.5rem 0 0.75rem;
        color: #3498db;
    }
    
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
    }
    
    th, td {
        padding: 0.75rem;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    
    th {
        background-color: #f1f5f9;
        color: #2c3e50;
        font-weight: 600;
    }
    
    tr:hover {
        background-color: #f9f9f9;
    }
    
    .primary-key {
        font-weight: bold;
        color: #e74c3c;
    }
    
    .foreign-key {
        font-weight: bold;
        color: #3498db;
    }
    
    .tables-index {
        margin-bottom: 1rem;
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #eee;
        padding: 10px;
        border-radius: 4px;
    }
    
    #search-box {
        width: 100%;
        padding: 8px;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    .table-link {
        display: inline-block;
        margin: 3px 5px;
        padding: 2px 8px;
        border-radius: 3px;
        text-decoration: none;
        color: #333;
        background-color: #f5f7fa;
    }
    
    .table-link:hover {
        background-color: #3498db;
        color: white;
    }
</style>
@endsection

@section('content')
    {{-- Breadcrumb --}}
    <div class="pull-right">
        <ol class="breadcrumb custom-breadcrumb">
            <li><a href="{{ url('workplace') }}">Dashboard</a></li>
            <li><a href="{{ route('general.commands.home') }}">Commands Home</a></li>
            <li class="active">Database Documentation</li>
        </ol>
    </div>

    <div class="panel-heading">
        <div class="row">
            <div class="col-md-6">
                <h3>Database Documentation</h3>
            </div>
            <div class="col-md-6 text-right">
                <form action="{{ route('general.commands.dbdocs.generate') }}" method="POST" class="form-inline">
                    @csrf
                    <button type="submit" class="btn btn-success">
                        <i class="fa fa-refresh"></i> Regenerate Documentation
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="panel-body">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                {{ session('error') }}
            </div>
        @endif

        <div class="row">
            <div class="col-md-3">
                <div class="well">
                    <h4>Table Index</h4>
                    <input type="text" id="search-box" placeholder="Search tables...">
                    <div class="tables-index" id="tablesIndex">
                        @foreach($tableLinks as $tableId => $tableName)
                            <a href="#{{ $tableId }}" class="table-link">{{ $tableName }}</a>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="col-md-9">
                <div id="database-structure">
                    <h4>Database Structure</h4>
                    <p>
                        This document provides comprehensive documentation of the Itqan database.
                        Each table is presented with its structure, key relationships, and linked references.
                    </p>
                    <p>
                        Primary keys are marked in <span class="primary-key">red</span>, while foreign keys are marked in <span class="foreign-key">blue</span>.
                    </p>
                    <p>
                        <strong>Last Updated:</strong> {{ $lastUpdated }}
                    </p>
                </div>

                <div id="tables-content">
                    <h4>Table Definitions</h4>
                    {!! $tableContent !!}
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js')
<script>
    $(document).ready(function(){
        // Search functionality
        $('#search-box').on('input', function() {
            const searchTerm = $(this).val().toLowerCase();
            $('.table-link').each(function() {
                const tableName = $(this).text().toLowerCase();
                if (tableName.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });

        // Smooth scrolling for anchor links
        $('.table-link').click(function(e) {
            e.preventDefault();
            const target = $($(this).attr('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 500);
            }
        });
    });
</script>
@endsection 
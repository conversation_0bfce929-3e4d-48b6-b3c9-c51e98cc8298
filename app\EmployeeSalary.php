<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\EmployeeSalary
 *
 * @property int $id
 * @property int $employee_id
 * @property float $basic_salary
 * @property string $work_mood
 * @property string $start_at
 * @property string|null $end_at
 * @property string|null $hours_per_month
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\EmployeeTimetable|null $timetable
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalary newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalary newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalary query()
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalary whereBasicSalary($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalary whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalary whereEmployeeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalary whereEndAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalary whereHoursPerMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalary whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalary whereStartAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalary whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalary whereWorkMood($value)
 * @mixin \Eloquent
 */
class EmployeeSalary extends Model
{
    public $fillable =[
        'employee_id' ,
        'start_at' ,
        'work_mood' ,
        'basic_salary' ,
        'hours_per_month',
        'hours_per_day',
    ];


    public function timetable()
    {
        return $this->hasOne('App\EmployeeTimetable');
    }


    public function employee(){


        return $this->belongsTo(Employee::class);
    }
}

@if($special_program_data['data'])
@foreach($special_program_data['data'] as $attendant)
<div class="panel panel-primary card-view student-card">
      <div class="panel-heading">
            <h3 class="panel-title">
            {{ $attendant['info']->student->full_name }}    
            @isset($attendant['debug'])
        
            <a class="btn btn-danger btn-xs pull-right" style="display:none" data-toggle="modal" href='#modal-{{ $loop->index }}'>Show Debuging Data</a>
            <div class="modal fade" id="modal-{{ $loop->index }}">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                            <h4 class="modal-title">Debuging Records</h4>
                        </div>
                        <div class="modal-body">
                            <pre><?php print_r($attendant['debug']) ?></pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary">Save changes</button>
                        </div>
                    </div>
                </div>
            </div>
            
            @endisset
            </h3>
      </div>
      <div class="panel-body">

        @if($attendant['hefz'])
        <div class="row">
            <div class="col-xs-12">
            <strong>Memorization</strong>
            </div>
            <div class="col-md-6 col-sm-12 col-xs-12">
                {{--      @if($attendant['hefz']['from_surat'] == $attendant['hefz']['to_surat'])
                    سورة {{ $attendant['hefz']['from_surat_name'] }}  من الآية {{ $attendant['hefz']['from_ayat'] }} الى الآية {{ $attendant['hefz']['to_ayat'] }} 
                    @else
                    من {{ $attendant['hefz']['from_surat_name'] }}   {{ $attendant['hefz']['from_ayat'] }}
                    الى  
                    {{ $attendant['hefz']['to_surat_name'] }} - {{ $attendant['hefz']['to_ayat'] }} 
                    @endif
                   
                <select name="" class="form-control" id="" dir="rtl">
                    <option value="">
                        @if($attendant['hefz']['from_surat'] == $attendant['hefz']['to_surat'])
                        سورة {{ $attendant['hefz']['from_surat_name'] }}  من الآية {{ $attendant['hefz']['from_ayat'] }} الى الآية {{ $attendant['hefz']['to_ayat'] }} 
                        @else
                        من {{ $attendant['hefz']['from_surat_name'] }}   {{ $attendant['hefz']['from_ayat'] }}
                        الى  
                        {{ $attendant['hefz']['to_surat_name'] }} - {{ $attendant['hefz']['to_ayat'] }} 
                        @endif
                    </option>
                </select> --}}
                <div class="col-sm-12 pa-0 col-md-6 col-xs-12">
                    From : 
                    <v-surah-ayah-picker 
                            current_surah_id="{{ $attendant['hefz']['from_surat'] }}" 
                            current_ayah="{{ $attendant['hefz']['from_ayat'] }}" 
                            student_id="{{$attendant['info']->student->id}}" 
                            report_type="hefz"
                            field_name="from"
                        />
                </div>
                <div class="col-sm-12 pa-0 col-md-6 col-xs-12">
                    To:
                    <v-surah-ayah-picker 
                            current_surah_id="{{ $attendant['hefz']['to_surat'] }}" 
                            current_ayah="{{ $attendant['hefz']['to_ayat'] }}"
                            student_id="{{$attendant['info']->student->id}}" 
                            report_type="hefz"
                            field_name="to"
                            ></v-surah-ayah-picker>
                </div>
            </div>
            <div class="col-md-2 col-sm-6 col-xs-12">
                @if($special_program_data['hefz_evaluation_schema']->options)
                <span class="mb-0 mt-5">Grade</span>
                <select name="student_performance[{{$attendant['info']->student->id}}][hefz]" class="form-control" id="" required>
                    <option value=""> Select </option>
                @foreach($special_program_data['hefz_evaluation_schema']->options as $option)
                    <option value="{{ $option->id }}">{{ $option->code  }} - {{ $option->title }}</option>
                @endforeach
                </select>
                @endif
            </div>
            <div class="col-md-3 col-sm-6 col-xs-12">
                <span>Notes</span>
                <textarea name="student_performance[{{$attendant['info']->student->id}}][hefz_note]" class="form-control" cols="30" rows="1" placeholder="Notes"></textarea>
            </div>
        </div>
        @endif
        
        @if($attendant['revision'])
        <hr>
        <div class="row">
            <div class="col-xs-12">
                <strong>
                    Revision
                </strong>
            </div>
            <div class="col-md-6 col-sm-12 col-xs-12">
                <!-- @if($attendant['revision']['from_surat'] == $attendant['revision']['to_surat'])
                سورة {{ $attendant['revision']['from_surat_name'] }}  من الآية {{ $attendant['revision']['from_ayat'] }} الى الآية {{ $attendant['revision']['to_ayat'] }} 
                @else
                من {{ $attendant['revision']['from_surat_name'] }}   {{ $attendant['revision']['from_ayat'] }}
                الى  
                {{ $attendant['revision']['to_surat_name'] }} - {{ $attendant['revision']['to_ayat'] }} 
                @endif -->
                <div class="col-md-6 pa-0 col-sm-12">
                        From : 
                        <v-surah-ayah-picker 
                                current_surah_id="{{ $attendant['revision']['from_surat'] }}" 
                                current_ayah="{{ $attendant['revision']['from_ayat'] }}" 
                                student_id="{{$attendant['info']->student->id}}" 
                                report_type="revision"
                                field_name="from"
                            />
                    </div>
                    <div class="col-md-6 pa-0 col-sm-12">
                        To:
                        <v-surah-ayah-picker 
                                current_surah_id="{{ $attendant['revision']['to_surat'] }}" 
                                current_ayah="{{ $attendant['revision']['to_ayat'] }}"
                                student_id="{{$attendant['info']->student->id}}" 
                                report_type="revision"
                                field_name="to"
                                ></v-surah-ayah-picker>
                    </div>
    
                
            </div>
            <div class="col-md-2 col-sm-6 col-xs-12">
                <span class="mb-0 mt-5">Grade</span>
                <select name="student_performance[{{$attendant['info']->student->id}}][revision]" class="form-control" id="" required >
                    <option value=""> Select </option>
                @foreach($special_program_data['revision_evaluation_schema']->options as $option)
                    <option value="{{ $option->id }}">{{ $option->code  }} - {{ $option->title }}</option>
                @endforeach
                </select>
            </div>
            <div class="col-md-3 col-sm-6 col-xs-12">
                    <input type="hidden" name="report[{{$attendant['info']->student->id}}][revision][revision_type]" 
                    @if(isset($attendant['revision']['revision_type']) && $attendant['revision']['revision_type']=='step_surah')
                    value="step_surah"
                    @endif
                    >
                    <span>Notes</span>
                    <textarea name="report[{{$attendant['info']->student->id}}][revision][revision_note]" class="form-control" cols="30" rows="1" placeholder="Notes"></textarea>
            </div>
        </div>
        @endif
    </div>
</div>
@endforeach
@else

<div class="alert alert-danger text-center">
    <strong>All Students Are Absent</strong>
</div>

<input type="hidden" name="student_performance[]" value="allAbsents">

@endif
@section('css')
<style>
select.form-control {
    height: 34px;
}
.student-card {
    margin: 5px;
}
.student-card .panel-heading {
    padding: 10px !important;
    margin: 0;
}
.student-card .panel-heading h3{
    color: #fff;
}
.student-card hr{
    margin: 10px 0;
    border-color: #2879ff;
}
</style>    
@append

@section('js')
<!-- <script src="https://cdn.jsdelivr.net/npm/vue"></script> -->
<script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js"></script>

<template id="surahPicker">
    <div class="clearfix">
        <div class="col-xs-8 pa-0">
            {{-- <label for="">Surah</label> --}}
            <select :name="input_surah" id="input" class="form-control" required="required" v-model="current_surah">
                <option :value="surah.id" v-for="(surah, index) in surats" :key="index">@{{surah.name}}</option>
            </select>
        </div>
        <div class="col-xs-4 pa-0">
            {{-- <label for="">Ayah</label> --}}
            <input type="number" :name="input_ayah" class="form-control" value="" required="required" min="1" :max="num_ayat" v-model="current_ayah" style="height: 34px;">
        </div>        
    </div>
    
</template>
<script>
    var app = new Vue({
        el: '#vueApp',
        components:{
            "v-surah-ayah-picker" : {
                'template' : '#surahPicker',
                'data': function(){
                    return {
                        'surats' : this.$parent.surats,
                        'current_surah' : this.current_surah_id
                    }
                },
                props : ['current_surah_id' , 'current_ayah' , 'student_id' , 'report_type', 'field_name'],
                computed : {
                    num_ayat : function(){
                        return this.surats[(this.current_surah -1)].num_ayat || '';
                    },
                    input_surah : function () { 
                        return 'report['+this.student_id+']['+this.report_type+']['+this.field_name+'_surat]';
                     },
                     input_ayah : function () { 
                        return 'report['+this.student_id+']['+this.report_type+']['+this.field_name+'_ayat]';
                     }
                },
                watch : {
                    'current_ayah' : function (value) {
                        if(value > this.num_ayat){
                            alert("Number of ayat in this surah is "+ this.num_ayat);
                            this.current_ayah = this.num_ayat;
                        }
                    }
                }
            }
        },
        data: {
            'surats' : {!! $surats !!}
        }
    })
</script>
@append
<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Http\Controllers;

use App\Classes;
use App\Http\Controllers\Controller;
use App\Student;
use App\StudentNouranyaPlan;
use App\StudentNouranyaReport;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;

/**
 * Aggregated month-end Nouranya summary across MULTIPLE classes.
 * Returns one row per class with the same metrics as the single-class controller.
 */
final class MonthEndNouranyaSummaryAggregatedController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        try {
            $classIds = $this->parseClassIds($request->input('classId'));
            $monthYear = (string) $request->input('classDate');

            if (empty($classIds) || $monthYear === '') {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            $date = Carbon::createFromFormat('M Y', $monthYear);
            $month = (int) $date->month;
            $year  = (int) $date->year;

            $rows = [];
            foreach ($classIds as $classId) {
                $class = Classes::find($classId);
                if (!$class) { continue; }

                $students = Student::whereHas('joint_classes', function ($query) use ($classId) {
                        $query->where('class_id', $classId);
                    })
                    ->where('status', 'active')
                    ->orderBy('full_name', 'asc')
                    ->get();

                $studentCount         = $students->count();
                $avgAttendance        = $this->calculateAverageAttendance($students, $classId, $month, $year);
                $avgAchievement       = $this->calculateAverageAchievement($students, $classId, $month, $year);
                $totalPlannedLessons  = $this->calculateTotalPlannedLessons($students, $month, $year);
                $totalCompletedLessons= $this->calculateTotalCompletedLessons($students, $classId, $month, $year);

                $rows[] = [
                    'class_id'             => (int) $classId,
                    'class_name'           => $class->class_code ?? (string) $class->name,
                    'noOfStudents'         => $studentCount,
                    'avgAttendance'        => number_format($avgAttendance, 1) . '%',
                    'avgAchievement'       => number_format($avgAchievement, 1) . '%',
                    'totalPlannedLessons'  => $totalPlannedLessons,
                    'totalCompletedLessons'=> $totalCompletedLessons,
                ];
            }

            return DataTables::of($rows)->toJson();
        } catch (\Throwable $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /** @return int[] */
    private function parseClassIds($input): array
    {
        if (is_array($input)) { $ids = $input; }
        elseif (is_string($input)) { $ids = array_filter(array_map('trim', explode(',', $input))); }
        else { $ids = []; }
        $ids = array_values(array_unique(array_map('intval', $ids)));
        return array_values(array_filter($ids, static fn (int $id) => $id > 0));
    }

    private function calculateAverageAttendance($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) return 0.0;
        $class = Classes::find($classId);
        if (!$class || !$class->timetable) return 0.0;
        $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
        if ($totalClasses <= 0) return 0.0;

        $sum = 0; $cnt = 0;
        foreach ($students as $student) {
            $attendedClasses = StudentNouranyaReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereIn('attendance_id', [1, 2])
                ->count();
            $sum += min(100.0, ($attendedClasses / $totalClasses) * 100); $cnt++;
        }
        return $cnt>0 ? $sum/$cnt : 0.0;
    }

    private function calculateAverageAchievement($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) return 0.0;
        $sum = 0; $cnt = 0;
        foreach ($students as $student) {
            $plan = StudentNouranyaPlan::where('student_id', $student->id)
                ->where(function ($query) use ($month, $year) {
                    $query->whereYear('start_date', $year)
                          ->whereMonth('start_date', $month);
                })
                ->first();
            if (!$plan) continue;

            $reports = StudentNouranyaReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->count();
            if ($reports === 0) continue;
            $sum += min(100.0, ($reports / 20) * 100); $cnt++;
        }
        return $cnt>0 ? $sum/$cnt : 0.0;
    }

    private function calculateTotalPlannedLessons($students, int $month, int $year): int
    {
        if ($students->isEmpty()) return 0;
        $total = 0;
        foreach ($students as $student) {
            $plan = StudentNouranyaPlan::where('student_id', $student->id)
                ->where(function ($query) use ($month, $year) {
                    $query->whereYear('start_date', $year)
                          ->whereMonth('start_date', $month);
                })
                ->first();
            if ($plan && $plan->from_lesson && $plan->to_lesson) {
                $from = (int)$plan->from_lesson; $to = (int)$plan->to_lesson; $total += max(0, $to - $from + 1);
            }
        }
        return $total;
    }

    private function calculateTotalCompletedLessons($students, int $classId, int $month, int $year): int
    {
        if ($students->isEmpty()) return 0;
        $total = 0;
        foreach ($students as $student) {
            $reports = StudentNouranyaReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();
            if ($reports->isNotEmpty()) {
                $first = $reports->first(); $last = $reports->last();
                if ($first->from_lesson && $last->to_lesson) {
                    $from = (int)$first->from_lesson; $to = (int)$last->to_lesson; $total += max(0, $to - $from + 1);
                }
            }
        }
        return $total;
    }
}



<?php

namespace Modules\Education\Http\Controllers;

use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\MoshafJuz;
use App\ProgramLevel;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStaatisticsRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class UpdateProgramLevelOrderController extends Controller
{

    public function __invoke(Request $request)
    {




        foreach($request->input('rows', []) as $row)
        {
            ProgramLevel::find($row['id'])->update([
                'program_level_order' => $row['position']
            ]);
        }

        return response()->noContent();


        $id = $request->get('program_level_id');
        $order = $request->get('order');
        $programLevel = ProgramLevel::find($id);
        $programLevel->program_level_order = $order;
        $programLevel->updated_at = Carbon::now();
        $data = $programLevel->update();
        $programLevels = ProgramLevel::where('program_id',$programLevel->program_id)->orderBy('program_level_order');

       $programLevelsDataTableData = \Yajra\DataTables\DataTables::of($programLevels)
            ->addColumn('arabic', function ($programLevel) use ($request) {


                $arabicTitle = $programLevel->arabic[0]->title;
                return $arabicTitle;

            })
            ->addColumn('from_surat', function ($programLevel) use ($request) {


                $fromSurat = $programLevel->from_surat;
                return $fromSurat;

            })
            ->addColumn('to_surat', function ($programLevel) use ($request) {


                $toSurat = $programLevel->to_surat;
                return $toSurat;

            })


            ->addColumn('actions', function ($programLevel) use ($request) {
                $html = '';
                $route1 = url('/workplace/education/program-levels/' . $programLevel->id);
                $route2 = url('/workplace/education/program-levels/' . $programLevel->id . '/edit');
                $html .=  '<a href="' . $route1 . '" title="View ProgramLevel">
                                            <button class="btn btn-info btn-xs"><i class="fa fa-eye"
                                                                                   aria-hidden="true"></i> View
                                            </button>
                                        </a>
                                        <a href="' . $route2 . '"
                                           title="Edit ProgramLevel">
                                            <button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o"
                                                                                      aria-hidden="true"></i> Edit
                                            </button>
                                        </a>';


                return $html;



//                    })->rawColumns(['login','action'])
            })->
            rawColumns(['role', 'actions', 'login', 'status', 'full_name', 'image'])
            ->make(true);


        return response()->json([
            'programLevelsDataTableData' => $programLevelsDataTableData,
            'code' => ($data == true) ? '200' : '500',
            'status' => ($data == true) ? 'Success Update Data' : 'Failed Update Data'
        ]);



    }
}

<?php

namespace Modules\HumanResource\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class OnlyAjaxMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {

        if(!$request->ajax()){
            abort(404);
        }

        return $next($request);
    }
}

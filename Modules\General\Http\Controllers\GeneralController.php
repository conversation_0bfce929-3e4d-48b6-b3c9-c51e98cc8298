<?php

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\AdmissionInterview;
use App\CenterEmployee;
use App\Employee;
use App\MissedClockOut;
use App\Scopes\OrganizationScope;
use App\StudentHefzPlan;
use App\WebsiteVisitor;
use Carbon\Carbon;
use Doctrine\DBAL\Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use App\Student;
use Module;
use Auth;
use App\Center;

use App\Classes;
use App\Employee as HREmployee;
use Spatie\Permission\Models\Role;

class GeneralController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        // Check if a fresh login exists and clear it
        $justLoggedIn = session('just_logged_in', false);
        if ($justLoggedIn) {
            // Clear the flag so it's only used once
            session()->forget('just_logged_in');
        }

        $dashboards = [];

        if (auth()->user()->can('have_teacher_dashboard')) {
            $dashboards[] = 'teacher_dashboard';
        }
        if (auth()->user()->can('have_supervisor_dashboard')) {
            $dashboards[] = 'supervisor_dashboard';
        }
        if (auth()->user()->can('have_finance_dashboard')) {
            $dashboards[] = 'finance_dashboard';
        }
        if (auth()->user()->can('have_human_resource_dashboard')) {
            $dashboards[] = 'human_resource_dashboard';
        }

        $dashboards[] = 'general_dashboard';

        if (isset(request()->dash) && in_array(request()->dash, $dashboards)) {
            $dashboard = request()->dash;
        } elseif (count($dashboards) > 2) {
            $dashboard = 'general_dashboard';
        } else {
            $dashboard = $dashboards[0];
        }


        // special case for the teacher dashboard

        if(auth()->user()->hasRole(['teacher_2_']) && !auth()->user()->hasRole(['managing-director_2_']))

        {
            $dashboard = 'teacher_dashboard';


        }


        // get timetable if employee is teacher
        $timetable = [];
        // if(auth()->user()->can('have_teacher_dashboard')){
//        foreach (auth()->user()->classes as $myclass) {
//
//
//            foreach ($myclass->subjects as $subject) {
//                if ($subject->teacher->data->id  == auth()->user()->id && $subject->timetable) {
//                    // dd ($myclass->class_info);
//                    $table_info = [
//                            'class_name'    => $myclass->class_info->name,
//                            'subject_title' => ''
//                        ];
//
//                    if ($subject->timetable->sat) {
//                        $timetable['sat'][$subject->timetable->sat] = $table_info;
//                    }
//                    if ($subject->timetable->sun) {
//                        $timetable['sun'][$subject->timetable->sun] = $table_info;
//                    }
//                    if ($subject->timetable->mon) {
//                        $timetable['mon'][$subject->timetable->mon] = $table_info;
//                    }
//                    if ($subject->timetable->tue) {
//                        $timetable['tue'][$subject->timetable->tue] = $table_info;
//                    }
//                    if ($subject->timetable->wed) {
//                        $timetable['wed'][$subject->timetable->wed] = $table_info;
//                    }
//                    if ($subject->timetable->thu) {
//                        $timetable['thu'][$subject->timetable->thu] = $table_info;
//                    }
//                    if ($subject->timetable->fri) {
//                        $timetable['fri'][$subject->timetable->fri] = $table_info;
//                    }
//                }
//            }
//        }
        // }





//        $missedClockoutsActions =  collect(['validate'])->all();
//        $allMissedClockOutAttendance = MissedClockOut::withTrashed()->with('employee')->with('attendance');
        $visitors = WebsiteVisitor::select('date', \DB::raw('count(*) as total'))->where('date', '>', today()->subMonth())->groupBy('date')->get();
        $chart_data = array();
        foreach ($visitors as $data)
        {
            array_push($chart_data, array($data->date->format('d.m.Y'), $data->total));
        }

        $viewerAccount = null;
        if (auth()->guard('employee')->user()->hasRole('managing-director_' . config('organization_id') . '_')) {
            $viewerRole = Role::findByName('system_viewer_' . config('organization_id') . '_', 'employee');
            if ($viewerRole) {
                $viewerAccount = HREmployee::role($viewerRole)->first();
            }
        }


        return view('general::dashboards.'.$dashboard,
            compact(
                'visitors', 'chart_data',
                'dashboards', 'timetable',
                'viewerAccount'
//                'missedClockoutsActions',
//                'allMissedClockOutAttendance'
            ));
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view('general::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function store(Request $request)
    {
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show()
    {
        return view('general::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit()
    {
        return view('general::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(Request $request)
    {
    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }
}

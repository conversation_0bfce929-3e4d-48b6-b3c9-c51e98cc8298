{!! Form::open(['url' => route('admission-interviews.report'), 'id' => 'interview_report' , 'class' => 'form-horizontal', 'files' => true]) !!}
<div class="row">
    <div class="col-md-6"> 
        <h5>Student Name: </h5> <p> {{ $student->full_name }}</p>
    </div>
    <div class="col-md-6"> 
        <h5>Program:</h5> <p> {{ $interview->program->title }} </p>
    </div>
</div>
<br>
<div class="clearfix">
    <h5>Interview Committee</h5>
    @if($interview->interviewers)
    @foreach($interview->interviewers as $interviewer)
    <div class="col-md-4">
        {{ $interviewer->name }}
    </div>
    <div class="col-md-8">
        Attended  {!! Form::radio('interviewer['.$interviewer->id.'][attended]' , 1 , null ,['class' => 'approve_btn' , 'program-id' => $interview->id , 'checked' => 'checked' ]) !!} 
        Absent   {!! Form::radio('interviewer['.$interviewer->id.'][attended]' , 0 , null ,['class' => 'approve_btn' , 'program-id' => $interview->id ]) !!} 

    </div>                
    @endforeach
    @endif
</div>
<br>

<h5>Interview Report  </h4>
{!! Form::hidden('interview[interview_id]' , $interview->id) !!}
<div class="form-group {{ $errors->has('result') ? 'has-error' : ''}}">
    {!! Form::label('level', 'Student Result', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        Approved  {!! Form::radio('interview[approve]' , 1 , null ,['class' => 'interview_approve_btn' , 'interview-id' => $interview->id , 'checked' => 'checked' ]) !!} 
        Rejected  {!! Form::radio('interview[approve]' , 0 , null ,['class' => 'interview_approve_btn' , 'interview-id' => $interview->id ]) !!} 
        <span class="alert-danger" id='error_result_{{$interview->id}}'></span>
    </div>
</div>

<div id="programLevels{{ $interview->id }}">
        @if(!isset($interview->program->setting['program_levels']) && !$interview->program->setting['program_levels']== 'special' )
    <div class="form-group {{ $errors->has('level') ? 'has-error' : ''}}">
        {!! Form::label('level', 'Program Level Placement', ['class' => 'col-md-4 control-label']) !!}
        <div class="col-md-6">
            {!! Form::select('interview[level]', $interview->program->levels->pluck('title' , 'id') , null, ['class' => 'form-control']) !!}
            <span class="alert-danger" id='error_level_{{$interview->id}}'></span>
        </div>
    </div>
    @endif

    @isset($interview->program->setting['special_program_code'])
        @if($interview->program->setting['special_program_code'] == 'hefz')
        @include('admission::student.forms.hefz_plan')
        @endif
    @endisset
</div>
<br>
<div class="form-group {{ $errors->has('attachement') ? 'has-error' : ''}}">
    {!! Form::label('attachement', 'Attachement', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::file('attachement' ,['class' => 'form-control']) !!} 
        <span class="alert-danger" id='error_attachement_{{$interview->id}}'></span>
    </div>
</div>
<br>
<div class="form-group {{ $errors->has('note') ? 'has-error' : ''}}">
    {!! Form::label('note', 'Notes', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::textarea('interview[notes]' , null ,['class' => 'form-control']) !!} 
        <span class="alert-danger" id='error_note_{{$interview->id}}'></span>
    </div>
</div>
<br>
<div class="text-center">
    <button type="submit" class="btn btn-primary">{{ trans('common.save') }}</button>
</div>
{!! Form::close() !!}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class RedirectIfOrganization
{
	/**
	 * Handle an incoming request.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  \Closure  $next
	 * @param  string|null  $guard
	 * @return mixed
	 */
	public function handle($request, Closure $next, $guard = 'organization')
	{
	    if (Auth::guard($guard)->check()) {
	        return redirect(config('app.locale').'/'.'home');
	    }

	    return $next($request);
	}
}
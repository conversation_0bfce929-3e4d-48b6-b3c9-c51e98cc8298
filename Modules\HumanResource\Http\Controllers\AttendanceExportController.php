<?php
// CONTROLLER: Modules/HumanResource/Http/Controllers/AttendanceExportController.php

namespace Modules\HumanResource\Http\Controllers;

use App\Employee;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Barryvdh\Snappy\Facades\SnappyPdf as PDF;
use Maatwebsite\Excel\Facades\Excel;
use Modules\HumanResource\Exports\EmployeesAttendanceExport;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;

class AttendanceExportController extends Controller
{
    /**
     * PDF Export for a single employee (by ID), given month & year.
     * Deploys the letter head, logo, and footer in the PDF layout.
     */
    public function exportPdf(Request $request, $employeeId)
    {
        $m         = $request->input('month');
        $y         = $request->input('year');
        $employee  = Employee::findOrFail($employeeId);
        $requester = auth()->user()->full_name;

        // Assets
        $logo        = asset("uploads/settings/logo.png");
        $letterHead  = asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg");
        $letterFooter= asset("uploads/settings/footer.png");

        // Fetch attendance data
        $attendanceRaw = DB::select(
            'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
            [$m, $y, null, $employeeId, null]
        );

        $data = $this->formatAttendanceData($attendanceRaw);

        // Unique file name
        $filename = 'attendance_report_'
            . str_replace(' ', '_', $employee->full_name)
            . '_' . $m
            . '_' . $y
            . '_' . time()
            . '.pdf';

        // Generate PDF
        $pdf = PDF::loadView('humanresource::attendance.per_employee_pdf', [
            'data'         => $data,
            'm'            => $m,
            'y'            => $y,
            'requester'    => $requester,
            'logo'         => $logo,
            'letterHead'   => $letterHead,
            'letterFooter' => $letterFooter,
        ])->setPaper('A4', 'landscape');

        return $pdf->download($filename);
    }

    /**
     * Excel Export for a single employee (by ID), given month & year.
     * Deploys letter head, logo, and footer via WithDrawings in the Export class.
     */
    public function exportExcel(Request $request, $employeeId)
    {
        $m         = $request->input('month');
        $y         = $request->input('year');
        $employee  = Employee::findOrFail($employeeId);
        $requester = auth()->user()->full_name;

        $attendanceRaw = DB::select(
            'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
            [$m, $y, null, $employeeId, null]
        );

        $data = $this->formatAttendanceData($attendanceRaw);

        $filename = 'attendance_report_'
            . str_replace(' ', '_', $employee->full_name)
            . '_' . $m
            . '_' . $y
            . '_' . time()
            . '.xlsx';

        // Pass in needed info to incorporate letterhead/logo/footer as drawings
        return Excel::download(
            new EmployeesAttendanceExport($data, $requester, $m, $y, $employee->full_name),
            $filename
        );
    }

    /**
     * Word Export for a single employee (by ID), given month & year.
     * Deploys the letter head, logo, and footer in the Word document.
     */
    public function exportWord(Request $request, $employeeId)
    {
        $m         = $request->input('month');
        $y         = $request->input('year');
        $employee  = Employee::findOrFail($employeeId);
        $requester = auth()->user()->full_name;

        // Assets
        $letterHead   = public_path("uploads/settings/EducationDivisionLetterheadersEDU.jpg");
        $logo         = public_path("uploads/settings/logo.png");
        $letterFooter = public_path("uploads/settings/footer.png");

        $attendanceRaw = DB::select(
            'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
            [$m, $y, null, $employeeId, null]
        );

        $data = $this->formatAttendanceData($attendanceRaw);

        $filename = 'attendance_report_'
            . str_replace(' ', '_', $employee->full_name)
            . '_' . $m
            . '_' . $y
            . '_' . time()
            . '.docx';

        $phpWord = new PhpWord();

        // Add letter head at top (insert as an image)
        // Create a Section
        $section = $phpWord->addSection();


        // Add the letter head
        // Adjust width/height to suit your letterhead image
        $section->addImage($letterHead, [
            // 'width' => 600,
            // 'height' => 80,
            // Instead of forcing exact dimensions, you can do:
            'width'     => 600,  // or set something smaller to avoid stretch
            'alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER,
        ]);

        // Optionally add the main logo below letter head
        $section->addTextBreak(1);
//        $section->addImage($logo, [
//            'width'     => 80,
//            'alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER,
//        ]);

        // Title & "requested by"
        $section->addText("Attendance Report for {$employee->full_name} - {$m}/{$y}", ['bold' => true, 'size' => 16]);
        $section->addText("This report was requested by {$requester}. Thank you!", ['italic' => true, 'size' => 10]);
        $section->addTextBreak(1);

        // Loop data
        foreach ($data as $empId => $employeeData) {
            $emp = $employeeData['employee'];
            $section->addText("Employee: {$emp['full_name']} (ID: {$emp['id']})", ['bold' => true, 'size' => 12]);

            $table = $section->addTable();
            $table->addRow();
            $table->addCell(1500)->addText("Date");
            $table->addCell(1000)->addText("Day");
            $table->addCell(1000)->addText("Status");
            $table->addCell(1500)->addText("In Time");
            $table->addCell(1500)->addText("Out Time");
            $table->addCell(1200)->addText("Hours Worked");
            $table->addCell(1200)->addText("Volunteer Hours");
            $table->addCell(1200)->addText("Salary %");

            foreach ($employeeData['details'] as $daily) {
                $table->addRow();
                $table->addCell(1500)->addText($daily['formatted_date']);
                $table->addCell(1000)->addText($daily['day_name']);
                $table->addCell(1000)->addText($daily['status']);
                $table->addCell(1500)->addText($daily['in_time']);
                $table->addCell(1500)->addText($daily['out_time']);
                $table->addCell(1200)->addText($daily['hours_worked']);
                $table->addCell(1200)->addText($daily['volunteer_hours']);
                $table->addCell(1200)->addText($daily['salary_percentage']);
            }

            // Summary row
            $table->addRow();
            $table->addCell(1500)->addText("Monthly Summary:", ['bold' => true]);
            $table->addCell(1000, ['gridSpan' => 3])->addText(
                "Total Hours: {$employeeData['summary']['total_hours_worked']} | ".
                "Vol. Hours: {$employeeData['summary']['total_volunteer_hours']} | ".
                "Avg Salary %: {$employeeData['summary']['overall_salary_percentage']}%",
                ['bold' => true]
            );
            $table->addCell(1000)->addText('');
            $table->addCell(1200)->addText('');
            $table->addCell(1200)->addText('');
            $table->addCell(1200)->addText('');

            $section->addTextBreak(1);
        }

        // Create a Footer section and add letterFooter
        $footer = $section->addFooter();
        $footer->addImage($letterFooter, [
            'width'     => 600,
            'alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER,
        ]);

        // Save
        $tempFile = tempnam(sys_get_temp_dir(), 'word_');
        $objWriter = IOFactory::createWriter($phpWord, 'Word2007');
        $objWriter->save($tempFile);

        return response()->download($tempFile, $filename)->deleteFileAfterSend(true);
    }

    /**
     * Convert raw DB records into a structured array and deduplicate by date if multiple rows appear.
     */
    protected function formatAttendanceData($attendanceRaw)
    {
        $formatted = [];

        foreach ($attendanceRaw as $record) {
            $empId   = $record->employee_id;
            $theDate = $record->date;

            if (!isset($formatted[$empId])) {
                $formatted[$empId] = [
                    'employee' => [
                        'id'        => $record->employee_id,
                        'full_name' => $record->full_name,
                    ],
                    'details' => [],
                    'summary' => [
                        'total_hours_worked'        => 0,
                        'total_volunteer_hours'     => 0,
                        'overall_salary_percentage' => 0,
                    ],
                    '_used_dates' => [],
                ];
            }

            // Avoid duplicates
            if (in_array($theDate, $formatted[$empId]['_used_dates'], true)) {
                continue;
            }
            $formatted[$empId]['_used_dates'][] = $theDate;

            $detail = [
                'date'              => $theDate,
                'formatted_date'    => $record->formatted_date,
                'day_name'          => $record->day_name,
                'status'            => $record->status,
                'in_time'           => $record->in_time,
                'out_time'          => $record->out_time ?? '-',
                'hours_worked'      => $record->hours_worked,
                'volunteer_hours'   => $record->volunteer_hours,
                'salary_percentage' => $record->salary_percentage,
            ];
            $formatted[$empId]['details'][] = $detail;

            $formatted[$empId]['summary']['total_hours_worked']        += (float) $record->hours_worked;
            $formatted[$empId]['summary']['total_volunteer_hours']     += (float) $record->volunteer_hours;
            $formatted[$empId]['summary']['overall_salary_percentage'] += (float) $record->salary_percentage;
        }

        // Compute average
        foreach ($formatted as $id => &$info) {
            $count = count($info['details']);
            if ($count > 0) {
                $info['summary']['overall_salary_percentage'] = round(
                    $info['summary']['overall_salary_percentage'] / $count,
                    2
                );
            }
            unset($info['_used_dates']);
        }

        return $formatted;
    }
}

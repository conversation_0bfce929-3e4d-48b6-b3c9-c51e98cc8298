@extends('layouts.hound')
@section('mytitle', 'Education Centers')

@section('content')
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-default">
                    <div class="panel-heading">Centers</div>
                    <div class="panel-body">

                        <a href="{{ route('centers.create') }}" class="btn btn-primary btn-xs" title="Add New Center"><span class="glyphicon glyphicon-plus" aria-hidden="true"/></a>
                        <br/>
                        <br/>
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th> Name </th>
                                        <th>Number of Classes</th>
                                        <th>Number of Students</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                @foreach($centers as $item)
                                    <tr>
                                        <td>{{ $item->id }}</td>
                                        <td>{{ $item->name }}</td>
                                        <td> {{ \App\Classes::where('center_id','=',$item->id)->count() }}
                                        </td>
                                        <td>{{ \App\Admission::where('center_id','=',$item->id)->where('status','=','active')->count() }}</td>
                                        <td>
                                            {{--  <a href="{{route('centers.show',$item->id) }}" class="btn btn-success btn-xs" title="View Center"><span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>  --}}
                                            <a href="{{route('centers.edit',$item->id) }}" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>
                                            {!! Form::open([
                                                'method'=>'DELETE',
                                                'route' => ['centers.destroy', $item->id],
                                                'style' => 'display:inline'
                                            ]) !!}
                                                {!! Form::button('<span class="glyphicon glyphicon-trash" aria-hidden="true" title="Delete Center" />', array(
                                                        'type' => 'submit',
                                                        'class' => 'btn btn-danger btn-xs',
                                                        'title' => 'Delete Center',
                                                        'onclick'=>'return confirm("Confirm delete?")'
                                                )) !!}
                                            {!! Form::close() !!}
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
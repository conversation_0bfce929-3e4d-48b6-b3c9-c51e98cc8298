<?php

namespace Modules\Leave\Http\Controllers;

use App\Employee;
use App\GeneralSettings;
use App\User;
use Carbon\Carbon;
use App\Traits\PdfGenerate;
use App\Traits\Notification;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use App\Repositories\UserRepository;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Modules\Leave\Http\Requests\EmployeeApplyLeaveStoreRequest;
use Modules\Leave\Http\Requests\EmployeeEditLeaveStoreRequest;
use Modules\Leave\Repositories\LeaveRepository;
use Modules\UserActivityLog\Traits\LogActivity;
use Modules\Leave\Repositories\LeaveTypeRepository;
use Modules\Setup\Repositories\DepartmentRepository;

class LeaveRequestAttachmentController extends Controller
{
    use Notification, PdfGenerate;

    private $leaveRepository, $userRepository, $deptRepo,$leaveTypeRepository;

    public function __construct(LeaveRepository $leaveRepository, UserRepository $userRepository,DepartmentRepository $deptRepo,LeaveTypeRepository $leaveTypeRepository)
    {
        $this->leaveRepository = $leaveRepository;
        $this->userRepository = $userRepository;
        $this->deptRepo = $deptRepo;
        $this->leaveTypeRepository = $leaveTypeRepository;
    }



    public function downloadLeaveApplicationAttachment($id)
    {
        try {
            $apply_leave = $this->leaveRepository->find($id);
            $title = $apply_leave->attachment;
            $file = public_path() . '/public/uploads/document/' . $title;
            if (file_exists($file)) {
                return response()->download($file);
            }
        } catch (\Exception $e) {


            Toastr::error(__('common.Something Went Wrong'), __('common.Error'));
            return back();
        }
    }
}

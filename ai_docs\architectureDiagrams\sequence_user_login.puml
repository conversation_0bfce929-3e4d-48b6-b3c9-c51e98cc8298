@startuml sequence_user_login
!theme vibrant

title User Login Sequence Diagram

actor User
participant Browser
participant LoginController << C >>
participant AuthService << S >>
participant User << M >>
participant Database << DB >>

User -> Browser : Enters Credentials & Submits Login Form
Browser -> LoginController : POST /login (email, password)
activate LoginController

LoginController -> AuthService : attemptLogin(credentials)
activate AuthService

AuthService -> User : findByCredentials(email)
activate User
User -> Database : SELECT * FROM users WHERE email = ?
activate Database
Database --> User : User Record (or null)
deactivate Database
User --> AuthService : User Instance (or null)
deactivate User

alt User Found & Password Matches
    AuthService -> AuthService : generateSessionToken()
    AuthService -> LoginController : Login Success (User Instance)
    deactivate AuthService
    LoginController -> Browser : Redirect to Dashboard (with session)
else User Not Found or Password Mismatch
    AuthService -> LoginController : Login Failed
    deactivate AuthService
    LoginController -> Browser : Redirect Back to Login (with error)
end

deactivate LoginController
Browser -> User : Display Dashboard / Login Page

@enduml 
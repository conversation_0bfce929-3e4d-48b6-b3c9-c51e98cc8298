[2025-06-02 19:26:02] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 19:26:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:26:02] production.INFO: Starting job subscriber notifications at 2025-06-02 19:26:02  
[2025-06-02 19:26:02] production.INFO: Starting general:queue-notify-job-subscribers at 2025-06-02 19:26:02  
[2025-06-02 19:26:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:26:02  
[2025-06-02 19:26:02] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:26:02 {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:26:02 at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(114): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 19:26:02] production.INFO: Job notifications process started after job fetch at 2025-06-02 19:26:02  
[2025-06-02 19:26:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:26:02 | Error: Notification process failed to complete  
[2025-06-02 19:26:02] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:26:02 | Error: Notification process failed to complete {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:26:02 | Error: Notification process failed to complete at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(574): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 19:26:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:26:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:26:02] production.INFO: Starting job notification queue dispatch from command  
[2025-06-02 19:26:02] production.ERROR: Call to undefined method Modules\JobSeeker\Services\JobService::notifySubscribers() {"exception":"[object] (Error(code: 0): Call to undefined method Modules\\JobSeeker\\Services\\JobService::notifySubscribers() at /var/www/html/itqanalquran/Modules/General/Console/Commands/QueueNotifyJobSubscribers.php:51)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Modules\\General\\Console\\Commands\\QueueNotifyJobSubscribers->handle()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#6 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#8 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#9 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#10 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#12 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-06-02 19:26:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:26:02 | Error: Queue dispatch may be locked or failed to complete  
[2025-06-02 19:26:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:26:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:26:02] production.ERROR: Command "general:notify-all-job-subscribers" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"general:notify-all-job-subscribers\" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers at /var/www/html/itqanalquran/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()
#1 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#3 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-02 19:26:02] production.INFO: Job notifications process started after job fetch at 2025-06-02 19:26:02  
[2025-06-02 19:26:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:26:02 | Error: Notification process failed to complete  
[2025-06-02 19:26:03] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:03] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:05] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:05] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:06] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:06] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:07] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:07] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:09] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:09] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:14] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:14] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:20] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:20] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3614,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3614,"student_photo":"public/uploads/student/108990d49f19901e04786ff31e331c52.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/108990d49f19901e04786ff31e331c52.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4160,"size":null} 
[2025-06-02 19:26:21] production.INFO: → falling back to gender default {"student_id":4160,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4225,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4225,"student_photo":"public/uploads/student/fd525484ae81ec385ef30219a5d04ba4.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4225,"checked_path":"public/uploads/student/fd525484ae81ec385ef30219a5d04ba4.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4031,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4031,"student_photo":"public/uploads/student/54a0cf91771e0fa65cd5b532d53a567a.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/54a0cf91771e0fa65cd5b532d53a567a.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3696,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3696,"student_photo":"public/uploads/student/d2c4fb961a0824dae5b04a4954813560.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/d2c4fb961a0824dae5b04a4954813560.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":2657,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2657,"student_photo":"public/uploads/student/c0239cd576404293a5088892873392ad.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2657,"checked_path":"public/uploads/student/c0239cd576404293a5088892873392ad.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4274,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4274,"student_photo":"/images/student/1747740477.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747740477.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4245,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4245,"student_photo":"students/4245/profile_picture/1745966625_blob.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"students/4245/profile_picture/1745966625_blob.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3343,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3343,"student_photo":"public/uploads/student/cf56aae349b619820d0d4ae0824a7808.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3343,"checked_path":"public/uploads/student/cf56aae349b619820d0d4ae0824a7808.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3371,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3371,"student_photo":"public/uploads/student/1add2e1e38ce7f8aca78cfcafdb38d0a.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/1add2e1e38ce7f8aca78cfcafdb38d0a.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3469,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3469,"student_photo":"public/uploads/student/6368cdd585fe098b112f62b67efe13a5.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6368cdd585fe098b112f62b67efe13a5.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3697,"size":null} 
[2025-06-02 19:26:21] production.INFO: → falling back to gender default {"student_id":3697,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3467,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3467,"student_photo":"public/uploads/student/fcefc437cf2ad6dc565e41e4ec518554.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/fcefc437cf2ad6dc565e41e4ec518554.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4275,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4275,"student_photo":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":2855,"size":null} 
[2025-06-02 19:26:21] production.INFO: → falling back to gender default {"student_id":2855,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4089,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4089,"student_photo":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4227,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4227,"student_photo":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4227,"checked_path":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4230,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4230,"student_photo":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4230,"checked_path":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":2856,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2856,"student_photo":"public/uploads/student/ea2e985e485592c48af674807a543771.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2856,"checked_path":"public/uploads/student/ea2e985e485592c48af674807a543771.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4277,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4277,"student_photo":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4276,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4276,"student_photo":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4228,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4228,"student_photo":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4228,"checked_path":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":2545,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2545,"student_photo":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2545,"checked_path":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3349,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3349,"student_photo":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3349,"checked_path":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3614,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3614,"student_photo":"public/uploads/student/108990d49f19901e04786ff31e331c52.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/108990d49f19901e04786ff31e331c52.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4160,"size":null} 
[2025-06-02 19:26:21] production.INFO: → falling back to gender default {"student_id":4160,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4225,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4225,"student_photo":"public/uploads/student/fd525484ae81ec385ef30219a5d04ba4.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4225,"checked_path":"public/uploads/student/fd525484ae81ec385ef30219a5d04ba4.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4031,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4031,"student_photo":"public/uploads/student/54a0cf91771e0fa65cd5b532d53a567a.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/54a0cf91771e0fa65cd5b532d53a567a.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3696,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3696,"student_photo":"public/uploads/student/d2c4fb961a0824dae5b04a4954813560.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/d2c4fb961a0824dae5b04a4954813560.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":2657,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2657,"student_photo":"public/uploads/student/c0239cd576404293a5088892873392ad.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2657,"checked_path":"public/uploads/student/c0239cd576404293a5088892873392ad.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4274,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4274,"student_photo":"/images/student/1747740477.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747740477.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4245,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4245,"student_photo":"students/4245/profile_picture/1745966625_blob.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"students/4245/profile_picture/1745966625_blob.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3343,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3343,"student_photo":"public/uploads/student/cf56aae349b619820d0d4ae0824a7808.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3343,"checked_path":"public/uploads/student/cf56aae349b619820d0d4ae0824a7808.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3371,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3371,"student_photo":"public/uploads/student/1add2e1e38ce7f8aca78cfcafdb38d0a.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/1add2e1e38ce7f8aca78cfcafdb38d0a.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3469,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3469,"student_photo":"public/uploads/student/6368cdd585fe098b112f62b67efe13a5.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6368cdd585fe098b112f62b67efe13a5.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3697,"size":null} 
[2025-06-02 19:26:21] production.INFO: → falling back to gender default {"student_id":3697,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3467,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3467,"student_photo":"public/uploads/student/fcefc437cf2ad6dc565e41e4ec518554.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/fcefc437cf2ad6dc565e41e4ec518554.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4275,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4275,"student_photo":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":2855,"size":null} 
[2025-06-02 19:26:21] production.INFO: → falling back to gender default {"student_id":2855,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4089,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4089,"student_photo":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4227,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4227,"student_photo":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4227,"checked_path":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4230,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4230,"student_photo":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4230,"checked_path":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":2856,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2856,"student_photo":"public/uploads/student/ea2e985e485592c48af674807a543771.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2856,"checked_path":"public/uploads/student/ea2e985e485592c48af674807a543771.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4277,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4277,"student_photo":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4276,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4276,"student_photo":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":4228,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4228,"student_photo":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4228,"checked_path":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":2545,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2545,"student_photo":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2545,"checked_path":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:26:21] production.DEBUG: getStudentImageUrl {"student":3349,"size":null} 
[2025-06-02 19:26:21] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3349,"student_photo":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:26:21] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3349,"checked_path":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:26:25] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:25] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:25] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:25] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:25] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:25] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:26] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:26] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:26] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:26] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:27] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:27] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:28] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:28] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:29] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:29] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:30] production.DEBUG: Plan: from_lesson=3, to_lesson=5, from_line=3, to_line=3  
[2025-06-02 19:26:30] production.DEBUG: Daily: from_lesson=3, to_lesson=3, from_line=  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:30] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:31] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:31] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:33] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:33] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:33] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:33] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:34] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:34] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:34] production.DEBUG: Plan: from_lesson=3, to_lesson=6, from_line=2, to_line=4  
[2025-06-02 19:26:34] production.DEBUG: Daily: from_lesson=3, to_lesson=3, from_line=  
[2025-06-02 19:26:37] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:37] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:37] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:37] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:37] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:37] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:37] production.DEBUG: Plan: from_lesson=4, to_lesson=7, from_line=1, to_line=2  
[2025-06-02 19:26:37] production.DEBUG: Daily: from_lesson=4, to_lesson=4, from_line=  
[2025-06-02 19:26:39] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:39] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:41] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:41] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:44] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:44] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:44] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:44] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:44] production.DEBUG: Plan: from_lesson=3, to_lesson=6, from_line=2, to_line=4  
[2025-06-02 19:26:44] production.DEBUG: Daily: from_lesson=3, to_lesson=4, from_line=2  
[2025-06-02 19:26:45] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:45] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:45] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:45] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:45] production.DEBUG: Plan: from_lesson=4, to_lesson=7, from_line=1, to_line=2  
[2025-06-02 19:26:45] production.DEBUG: Daily: from_lesson=4, to_lesson=5, from_line=3  
[2025-06-02 19:26:47] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:47] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:48] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:48] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:51] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:51] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:53] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:53] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:53] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:53] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:53] production.DEBUG: Plan: from_lesson=3, to_lesson=5, from_line=3, to_line=3  
[2025-06-02 19:26:53] production.DEBUG: Daily: from_lesson=3, to_lesson=4, from_line=3  
[2025-06-02 19:26:54] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:54] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:57] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:26:57] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":3614,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3614,"student_photo":"public/uploads/student/108990d49f19901e04786ff31e331c52.png"} 
[2025-06-02 19:26:57] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/108990d49f19901e04786ff31e331c52.png"} 
[2025-06-02 19:26:57] production.DEBUG: Plan: from_lesson=3, to_lesson=5, from_line=3, to_line=3  
[2025-06-02 19:26:57] production.DEBUG: Daily: from_lesson=3, to_lesson=4, to_line=3  
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":4160,"size":null} 
[2025-06-02 19:26:57] production.INFO: → falling back to gender default {"student_id":4160,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":4225,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4225,"student_photo":"public/uploads/student/fd525484ae81ec385ef30219a5d04ba4.png"} 
[2025-06-02 19:26:57] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4225,"checked_path":"public/uploads/student/fd525484ae81ec385ef30219a5d04ba4.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":4031,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4031,"student_photo":"public/uploads/student/54a0cf91771e0fa65cd5b532d53a567a.png"} 
[2025-06-02 19:26:57] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/54a0cf91771e0fa65cd5b532d53a567a.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":3696,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3696,"student_photo":"public/uploads/student/d2c4fb961a0824dae5b04a4954813560.png"} 
[2025-06-02 19:26:57] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/d2c4fb961a0824dae5b04a4954813560.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":2657,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2657,"student_photo":"public/uploads/student/c0239cd576404293a5088892873392ad.png"} 
[2025-06-02 19:26:57] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2657,"checked_path":"public/uploads/student/c0239cd576404293a5088892873392ad.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":4274,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4274,"student_photo":"/images/student/1747740477.jpeg"} 
[2025-06-02 19:26:57] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747740477.jpeg"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":4245,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4245,"student_photo":"students/4245/profile_picture/1745966625_blob.png"} 
[2025-06-02 19:26:57] production.DEBUG: → serving student_photo from storage {"path":"students/4245/profile_picture/1745966625_blob.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":3343,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3343,"student_photo":"public/uploads/student/cf56aae349b619820d0d4ae0824a7808.png"} 
[2025-06-02 19:26:57] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3343,"checked_path":"public/uploads/student/cf56aae349b619820d0d4ae0824a7808.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":3371,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3371,"student_photo":"public/uploads/student/1add2e1e38ce7f8aca78cfcafdb38d0a.png"} 
[2025-06-02 19:26:57] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/1add2e1e38ce7f8aca78cfcafdb38d0a.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":3469,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3469,"student_photo":"public/uploads/student/6368cdd585fe098b112f62b67efe13a5.png"} 
[2025-06-02 19:26:57] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6368cdd585fe098b112f62b67efe13a5.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":3697,"size":null} 
[2025-06-02 19:26:57] production.INFO: → falling back to gender default {"student_id":3697,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":3467,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3467,"student_photo":"public/uploads/student/fcefc437cf2ad6dc565e41e4ec518554.png"} 
[2025-06-02 19:26:57] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/fcefc437cf2ad6dc565e41e4ec518554.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":4275,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4275,"student_photo":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:26:57] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":2855,"size":null} 
[2025-06-02 19:26:57] production.INFO: → falling back to gender default {"student_id":2855,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":4089,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4089,"student_photo":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:26:57] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":4227,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4227,"student_photo":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:26:57] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4227,"checked_path":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":4230,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4230,"student_photo":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:26:57] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4230,"checked_path":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":2856,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2856,"student_photo":"public/uploads/student/ea2e985e485592c48af674807a543771.png"} 
[2025-06-02 19:26:57] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2856,"checked_path":"public/uploads/student/ea2e985e485592c48af674807a543771.png"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":4277,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4277,"student_photo":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:26:57] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":4276,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4276,"student_photo":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:26:57] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:26:57] production.DEBUG: getStudentImageUrl {"student":4228,"size":null} 
[2025-06-02 19:26:57] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4228,"student_photo":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:26:57] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4228,"checked_path":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":2545,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2545,"student_photo":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:26:58] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2545,"checked_path":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:26:58] production.DEBUG: Plan: from_lesson=4, to_lesson=7, from_line=1, to_line=2  
[2025-06-02 19:26:58] production.DEBUG: Daily: from_lesson=4, to_lesson=5, to_line=4  
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":3349,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3349,"student_photo":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:26:58] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3349,"checked_path":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:26:58] production.DEBUG: Plan: from_lesson=3, to_lesson=6, from_line=2, to_line=4  
[2025-06-02 19:26:58] production.DEBUG: Daily: from_lesson=3, to_lesson=4, to_line=4  
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":3614,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3614,"student_photo":"public/uploads/student/108990d49f19901e04786ff31e331c52.png"} 
[2025-06-02 19:26:58] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/108990d49f19901e04786ff31e331c52.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":4160,"size":null} 
[2025-06-02 19:26:58] production.INFO: → falling back to gender default {"student_id":4160,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":4225,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4225,"student_photo":"public/uploads/student/fd525484ae81ec385ef30219a5d04ba4.png"} 
[2025-06-02 19:26:58] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4225,"checked_path":"public/uploads/student/fd525484ae81ec385ef30219a5d04ba4.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":4031,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4031,"student_photo":"public/uploads/student/54a0cf91771e0fa65cd5b532d53a567a.png"} 
[2025-06-02 19:26:58] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/54a0cf91771e0fa65cd5b532d53a567a.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":3696,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3696,"student_photo":"public/uploads/student/d2c4fb961a0824dae5b04a4954813560.png"} 
[2025-06-02 19:26:58] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/d2c4fb961a0824dae5b04a4954813560.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":2657,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2657,"student_photo":"public/uploads/student/c0239cd576404293a5088892873392ad.png"} 
[2025-06-02 19:26:58] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2657,"checked_path":"public/uploads/student/c0239cd576404293a5088892873392ad.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":4274,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4274,"student_photo":"/images/student/1747740477.jpeg"} 
[2025-06-02 19:26:58] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747740477.jpeg"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":4245,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4245,"student_photo":"students/4245/profile_picture/1745966625_blob.png"} 
[2025-06-02 19:26:58] production.DEBUG: → serving student_photo from storage {"path":"students/4245/profile_picture/1745966625_blob.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":3343,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3343,"student_photo":"public/uploads/student/cf56aae349b619820d0d4ae0824a7808.png"} 
[2025-06-02 19:26:58] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3343,"checked_path":"public/uploads/student/cf56aae349b619820d0d4ae0824a7808.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":3371,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3371,"student_photo":"public/uploads/student/1add2e1e38ce7f8aca78cfcafdb38d0a.png"} 
[2025-06-02 19:26:58] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/1add2e1e38ce7f8aca78cfcafdb38d0a.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":3469,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3469,"student_photo":"public/uploads/student/6368cdd585fe098b112f62b67efe13a5.png"} 
[2025-06-02 19:26:58] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6368cdd585fe098b112f62b67efe13a5.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":3697,"size":null} 
[2025-06-02 19:26:58] production.INFO: → falling back to gender default {"student_id":3697,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":3467,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3467,"student_photo":"public/uploads/student/fcefc437cf2ad6dc565e41e4ec518554.png"} 
[2025-06-02 19:26:58] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/fcefc437cf2ad6dc565e41e4ec518554.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":4275,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4275,"student_photo":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:26:58] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":2855,"size":null} 
[2025-06-02 19:26:58] production.INFO: → falling back to gender default {"student_id":2855,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":4089,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4089,"student_photo":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:26:58] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":4227,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4227,"student_photo":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:26:58] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4227,"checked_path":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":4230,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4230,"student_photo":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:26:58] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4230,"checked_path":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":2856,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2856,"student_photo":"public/uploads/student/ea2e985e485592c48af674807a543771.png"} 
[2025-06-02 19:26:58] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2856,"checked_path":"public/uploads/student/ea2e985e485592c48af674807a543771.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":4277,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4277,"student_photo":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:26:58] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":4276,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4276,"student_photo":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:26:58] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":4228,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4228,"student_photo":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:26:58] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4228,"checked_path":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":2545,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2545,"student_photo":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:26:58] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2545,"checked_path":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:26:58] production.DEBUG: getStudentImageUrl {"student":3349,"size":null} 
[2025-06-02 19:26:58] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3349,"student_photo":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:26:58] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3349,"checked_path":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:27:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:27:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:27:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:27:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:27:02] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 19:27:02] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 19:27:02] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 19:27:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:27:02] production.INFO: Starting job subscriber notifications at 2025-06-02 19:27:02  
[2025-06-02 19:27:02] production.INFO: Starting general:queue-notify-job-subscribers at 2025-06-02 19:27:02  
[2025-06-02 19:27:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:27:02  
[2025-06-02 19:27:02] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:27:02 {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:27:02 at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(114): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 19:27:02] production.INFO: Job notifications process started after job fetch at 2025-06-02 19:27:02  
[2025-06-02 19:27:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:27:02 | Error: Notification process failed to complete  
[2025-06-02 19:27:02] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:27:02 | Error: Notification process failed to complete {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:27:02 | Error: Notification process failed to complete at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(574): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 19:27:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:27:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:27:02] production.INFO: Starting job notification queue dispatch from command  
[2025-06-02 19:27:02] production.ERROR: Call to undefined method Modules\JobSeeker\Services\JobService::notifySubscribers() {"exception":"[object] (Error(code: 0): Call to undefined method Modules\\JobSeeker\\Services\\JobService::notifySubscribers() at /var/www/html/itqanalquran/Modules/General/Console/Commands/QueueNotifyJobSubscribers.php:51)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Modules\\General\\Console\\Commands\\QueueNotifyJobSubscribers->handle()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#6 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#8 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#9 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#10 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#12 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-06-02 19:27:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:27:02 | Error: Queue dispatch may be locked or failed to complete  
[2025-06-02 19:27:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:27:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:27:02] production.ERROR: Command "general:notify-all-job-subscribers" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"general:notify-all-job-subscribers\" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers at /var/www/html/itqanalquran/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()
#1 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#3 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-02 19:27:02] production.INFO: Job notifications process started after job fetch at 2025-06-02 19:27:02  
[2025-06-02 19:27:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:27:02 | Error: Notification process failed to complete  
[2025-06-02 19:27:03] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:03] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:03] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:03] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:03] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:03] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:03] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:03] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:04] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:04] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:05] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:05] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:06] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:06] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:07] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:07] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:08] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:08] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:08] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:08] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:08] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:08] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:08] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:08] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:08] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:08] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:27:08] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:27:08] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:28:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:28:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:28:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:28:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:28:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:28:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:28:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:28:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:28:02] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 19:28:02] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 19:28:02] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 19:28:02] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 19:28:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 19:28:02] production.INFO: Starting job subscriber notifications at 2025-06-02 19:28:02  
[2025-06-02 19:28:02] production.INFO: Starting general:queue-notify-job-subscribers at 2025-06-02 19:28:02  
[2025-06-02 19:28:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:28:02 | Error: Queue dispatch may be locked or failed to complete  
[2025-06-02 19:28:02] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:28:02 | Error: Queue dispatch may be locked or failed to complete {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:28:02 | Error: Queue dispatch may be locked or failed to complete at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(129): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 19:28:02] production.INFO: Job notifications process started after job fetch at 2025-06-02 19:28:02  
[2025-06-02 19:28:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:28:02 | Error: Notification process failed to complete  
[2025-06-02 19:28:02] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:28:02 | Error: Notification process failed to complete {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:28:02 | Error: Notification process failed to complete at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(574): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 19:28:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:28:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:28:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:28:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 19:28:02] production.ERROR: Command "jobseeker:notify-job-subscribers" is not defined.

Did you mean one of these?
    general:notify-job-seekers
    general:queue-notify-job-subscribers
    jobseeker:check-inactive-categories
    jobseeker:cleanup-device-tokens
    jobseeker:discover-acbar-categories
    jobseeker:fetch-jobs-af-descriptions
    jobseeker:sync-acbar-jobs
    jobseeker:sync-jobs-af
    jobseeker:test-fcm-notification
    jobseeker:test-refactored-fcm-notification {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"jobseeker:notify-job-subscribers\" is not defined.

Did you mean one of these?
    general:notify-job-seekers
    general:queue-notify-job-subscribers
    jobseeker:check-inactive-categories
    jobseeker:cleanup-device-tokens
    jobseeker:discover-acbar-categories
    jobseeker:fetch-jobs-af-descriptions
    jobseeker:sync-acbar-jobs
    jobseeker:sync-jobs-af
    jobseeker:test-fcm-notification
    jobseeker:test-refactored-fcm-notification at /var/www/html/itqanalquran/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()

<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\Classes;
use App\IjazasanadMemorizationPlan;
use App\MoshafSurah;
use App\Student;
use App\StudentIjazasanadMemorizationReport;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * IjazasanadStudentMonthlySheet creates the Student Monthly Progress sheet for Ijazah & Sanad reports.
 * 
 * Purpose: Export per-student monthly Ijazah & Sanad report data aggregated across multiple classes.
 * Data source: student_ijazasanad_memorization_report with attendance and achievement calculations.
 * Calculations: Level detection, attendance percentages, achievement metrics, entry data (min/max ranges).
 * Context: Mirrors the aggregated DataTables structure from MonthlyIjazasanadReportAggregatedController.
 * Output: Single sheet with per-student rows including class grouping context for readability.
 */
final class IjazasanadStudentMonthlySheet implements WithTitle, WithEvents
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get student monthly data aggregated across all classes
     */
    private function getStudentMonthlyData(): array
    {
        $classIds = $this->filters['classIds'];
        $month = $this->filters['month'];
        $year = $this->filters['year'];
        $studentIds = $this->filters['studentIds'] ?? [];

        $results = [];

        foreach ($classIds as $classId) {
            $class = Classes::find($classId);
            if (!$class) {
                continue;
            }

            // Load active students for this class
            $query = Student::whereHas('joint_classes', function ($q) use ($classId) {
                $q->where('class_id', $classId);
            })
            ->where('status', 'active')
            ->with(['studentProgramLevels.programlevel'])
            ->orderBy('full_name', 'asc');

            if (!empty($studentIds)) {
                $query->whereIn('id', $studentIds);
            }

            $students = $query->get();

            $teacherNames = $class->teachers ? $class->teachers->pluck('full_name')->join(', ') : '';
            $programTitle = $class->programs ? optional($class->programs->first())->title : null;

            foreach ($students as $student) {
                $attendanceData = $this->calculateAttendance($student->id, $classId, $month, $year);
                $achievementPercent = $this->calculateAchievement($student, $classId, $month, $year);
                $entryData = $this->getEntryData($student->id, $classId, $month, $year);
                $monthlyPlan = $this->getMonthlyPlan($student->id, $month, $year);
                $monthlyAchievement = $this->getMonthlyAchievement($student->id, $classId, $month, $year);

                $results[] = [
                    'center_name' => $class->center->name ?? 'Unknown Center',
                    'class_name' => $class->name ?? 'Unknown Class',
                    'student_name' => $student->full_name ?? 'Unknown Student',
                    'class_program' => $programTitle ?? 'N/A',
                    'teacher_name' => $teacherNames ?: 'N/A',
                    'entry1' => $entryData['entry1'],
                    'entry2' => $entryData['entry2'],
                    'entry3' => $entryData['entry3'],
                    'entry4' => $entryData['entry4'],
                    'monthly_plan' => $monthlyPlan,
                    'monthly_achievement' => $monthlyAchievement,
                    'attendance_percentage' => number_format($attendanceData['percentage'], 1) . '%',
                    'achievement_percentage' => number_format($achievementPercent, 1) . '%',
                    'attendance_details' => $attendanceData['details'],
                ];
            }
        }

        return $results;
    }

    /**
     * Calculate attendance for a student in a class for the month
     */
    private function calculateAttendance(int $studentId, int $classId, int $month, int $year): array
    {
        $class = Classes::find($classId);
        if (!$class || !$class->timetable) {
            return ['percentage' => 0.0, 'details' => 'No timetable found'];
        }

        $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
        if ($totalClasses <= 0) {
            return ['percentage' => 0.0, 'details' => 'No scheduled classes'];
        }

        $attended = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereIn('attendance_id', [1, 2]) // Late and On Time
            ->count();

        $absent = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->where('attendance_id', 3) // Absent
            ->count();

        $percentage = min(100.0, ($attended / $totalClasses) * 100);

        return [
            'percentage' => $percentage,
            'details' => "Attended: {$attended}/{$totalClasses}, Absent: {$absent}"
        ];
    }

    /**
     * Calculate achievement for a student (mirrors controller logic)
     */
    private function calculateAchievement(Student $student, int $classId, int $month, int $year): float
    {
        $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
            ->where(function ($query) use ($month, $year, $classId) {
                $query->where(function ($q) use ($year, $month, $classId) {
                    $q->whereYear('created_at', $year)
                      ->whereMonth('created_at', $month)
                      ->where('class_id', $classId)
                      ->where('status', 'active');
                })->orWhere(function ($q2) use ($year, $month, $classId) {
                    $q2->whereYear('start_date', $year)
                       ->whereMonth('start_date', $month)
                       ->where('class_id', $classId)
                       ->where('status', 'active');
                });
            })
            ->first();

        if (!$plan) {
            return 0.0;
        }

        $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get();

        if ($reports->isEmpty()) {
            return 0.0;
        }

        $level = $this->detectStudentLevel($student);
        if ($level === 'level1') {
            $data = $this->calculateLevel1Completion($plan, $reports);
            return $data['completion_rate'];
        }

        $data = $this->calculateLevel2Completion($plan, $reports);
        return $data['completion_rate'];
    }

    /**
     * Get entry data (min from, max to across month)
     */
    private function getEntryData(int $studentId, int $classId, int $month, int $year): array
    {
        $reports = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereNotNull('hefz_from_surat')
            ->whereNotNull('hefz_from_ayat')
            ->whereNotNull('hefz_to_surat')
            ->whereNotNull('hefz_to_ayat')
            ->orderBy('created_at', 'asc')
            ->get();

        if ($reports->isEmpty()) {
            return ['entry1' => '—', 'entry2' => '—', 'entry3' => '—', 'entry4' => '—'];
        }

        $minFromSurat = $reports->min('hefz_from_surat');
        $minFromAyat = $reports->where('hefz_from_surat', $minFromSurat)->min('hefz_from_ayat');
        $maxToSurat = $reports->max('hefz_to_surat');
        $maxToAyat = $reports->where('hefz_to_surat', $maxToSurat)->max('hefz_to_ayat');

        return [
            'entry1' => $this->getSurahName($minFromSurat),
            'entry2' => $minFromAyat,
            'entry3' => $this->getSurahName($maxToSurat),
            'entry4' => $maxToAyat,
        ];
    }

    /**
     * Get monthly plan display
     */
    private function getMonthlyPlan(int $studentId, int $month, int $year): string
    {
        $plan = IjazasanadMemorizationPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year) {
                $query->whereYear('start_date', $year)->whereMonth('start_date', $month);
            })
            ->orWhere(function ($query) use ($month, $year) {
                $query->whereYear('created_at', $year)->whereMonth('created_at', $month);
            })
            ->first();

        if (!$plan) {
            return '—';
        }

        $planContent = '';
        if (!empty($plan->from_surat_juz_id) && !empty($plan->to_surat_juz_id)) {
            $planContent .= "Juz {$plan->from_surat_juz_id} - Juz {$plan->to_surat_juz_id}";
        }
        if (!empty($plan->start_from_surat) && !empty($plan->start_from_ayat) && !empty($plan->to_surat) && !empty($plan->to_ayat)) {
            if ($planContent) $planContent .= '; ';
            $fromSurah = $this->getSurahName($plan->start_from_surat);
            $toSurah = $this->getSurahName($plan->to_surat);
            $planContent .= "{$fromSurah}:{$plan->start_from_ayat} - {$toSurah}:{$plan->to_ayat}";
        }

        return $planContent ?: '—';
    }

    /**
     * Get monthly achievement display
     */
    private function getMonthlyAchievement(int $studentId, int $classId, int $month, int $year): string
    {
        $reports = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereNotNull('pages_memorized')
            ->where('pages_memorized', '>', 0)
            ->get();

        if ($reports->isEmpty()) {
            return '—';
        }

        $totalPages = $reports->sum('pages_memorized');
        $count = $reports->count();

        return "{$totalPages} pages ({$count} reports)";
    }

    /**
     * Helper methods (simplified versions of controller methods)
     */
    private function detectStudentLevel($studentDetails): ?string
    {
        foreach ($studentDetails->studentProgramLevels as $spl) {
            if ($spl->programlevel) {
                $levelName = strtolower($spl->programlevel->title);
                if (str_contains($levelName, 'level 1')) return 'level1';
                if (str_contains($levelName, 'level 2')) return 'level2';
            }
        }
        return null;
    }

    private function calculateLevel1Completion($plan, $reports): array
    {
        // Simplified level 1 calculation
        $components = ['talqeen', 'revision', 'jazariyah', 'seminars'];
        $total = 0;
        $valid = 0;

        foreach ($components as $name) {
            $fromField = "{$name}_from_lesson";
            $toField = "{$name}_to_lesson";
            if (!empty($plan->$fromField) && !empty($plan->$toField) && $plan->$fromField <= $plan->$toField) {
                $valid++;
                $total += 25; // Simplified: equal weight per component
            }
        }

        $overall = $valid > 0 ? ($total / $valid) : 0;
        return ['completion_rate' => round($overall, 2)];
    }

    private function calculateLevel2Completion($plan, $reports): array
    {
        $achievedPages = $reports->sum('pages_memorized') ?? 0;
        $plannedPages = $this->calculatePlannedPages($plan);
        
        $percentage = $plannedPages > 0 ? round(($achievedPages / $plannedPages) * 100) : 0;
        return ['completion_rate' => $percentage];
    }

    private function calculatePlannedPages($plan): int
    {
        if (!$plan->start_from_surat || !$plan->start_from_ayat || !$plan->to_surat || !$plan->to_ayat) {
            return 0;
        }

        try {
            if ($plan->study_direction === 'backward') {
                $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $plan->start_from_surat, $plan->start_from_ayat, $plan->to_surat, $plan->to_ayat
                ]);
                return $result[0]->numberofPagesSum ?? 0;
            } else {
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $plan->start_from_surat, $plan->start_from_ayat, $plan->to_surat, $plan->to_ayat
                ]);
                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                return $results[0]->number_of_pages_sum ?? 0;
            }
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getSurahName(int $surahId): string
    {
        $surah = MoshafSurah::where('id', $surahId)->first();
        return $surah ? "{$surah->id}. {$surah->eng_name}" : "Surah {$surahId}";
    }

    /**
     * Get table headings
     */
    private function getTableHeadings(): array
    {
        // Align with row order (and Mem & Rev): include Class Program and Teacher; omit detail column
        return [
            'Center',
            'Class',
            'Student',
            'Class Program',
            'Teacher',
            'Entry 1 (From Surah)',
            'Entry 2 (From Ayat)',
            'Entry 3 (To Surah)',
            'Entry 4 (To Ayat)',
            'Monthly Plan',
            'Monthly Achievement',
            'Attendance %',
            'Achievement %'
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Student Monthly Progress';
    }

    /**
     * Register events for creating the styled table
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $this->createStyledTable($event->sheet);
            },
        ];
    }

    /**
     * Create styled table with data
     */
    private function createStyledTable($sheet)
    {
        $worksheet = $sheet->getDelegate();

        // 1) Performance dashboard at the top (to mirror Mem & Rev)
        $analytics = $this->getAnalyticsData();
        $currentRow = $this->createPerformanceDashboard($worksheet, 1, $analytics) + 2;

        // 2) Title below dashboard
        $studentData = $this->getStudentMonthlyData();
        $headings = $this->getTableHeadings();
        $monthName = $this->filters['monthName'];
        $year = $this->filters['year'];
        $classNames = collect($this->filters['classes'])->pluck('class_code')->join(', ');
        $title = "IJAZAH & SANAD STUDENT MONTHLY PROGRESS - {$classNames} - {$monthName} {$year}";
        $worksheet->setCellValue("A{$currentRow}", $title);
        $worksheet->mergeCells("A{$currentRow}:M{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 16, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '1f4e79']]
        ]);
        $currentRow += 2;

        // Set headers
        $col = 'A';
        foreach ($headings as $heading) {
            $worksheet->setCellValue($col . $currentRow, $heading);
            $col++;
        }

        // Style headers
        $worksheet->getStyle("A{$currentRow}:M{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2f75b5']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $dataStartRow = $currentRow + 1;
        $currentDataRow = $dataStartRow;

        // Add data rows
        foreach ($studentData as $row) {
            $col = 'A';
            foreach ($row as $key => $value) {
                if ($key !== 'attendance_details') { // Skip details column for main display
                    $worksheet->setCellValue($col . $currentDataRow, $value);
                    $col++;
                }
            }
            $currentDataRow++;
        }

        // Style data rows
        if (count($studentData) > 0) {
            $dataEndRow = $currentDataRow - 1;
            $worksheet->getStyle("A{$dataStartRow}:M{$dataEndRow}")->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ]);

            // Center-align percentage columns (L, M)
            $worksheet->getStyle("L{$dataStartRow}:M{$dataEndRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        } else {
            // No data message
            $worksheet->setCellValue("A{$dataStartRow}", 'No Ijazah & Sanad student data found for the specified criteria');
            $worksheet->mergeCells("A{$dataStartRow}:L{$dataStartRow}");
            $worksheet->getStyle("A{$dataStartRow}")->applyFromArray([
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'font' => ['italic' => true],
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ]
            ]);
        }

        // Auto-size columns
        foreach (range('A', 'L') as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }
    }

    /**
     * Analytics and dashboard (mirror Mem & Rev structure; IJ&S data)
     */
    private function getAnalyticsData(): array
    {
        $classIds = $this->filters['classIds'];
        $year = $this->filters['year'];
        $month = $this->filters['month'];

        $studentFilter = !empty($this->filters['studentIds']) ?
            (" AND simr.student_id IN (" . implode(',', array_map('intval', $this->filters['studentIds'])) . ")") : "";

        // Daily trends per class
        $dailyTrends = \DB::select("
            SELECT 
                DATE(simr.created_at) AS report_date,
                simr.class_id,
                c.class_code AS class_name,
                COUNT(DISTINCT cs.student_id) AS active_students,
                COUNT(*) AS total_sessions,
                COUNT(CASE WHEN ao.title IN ('late','on_time') THEN 1 END) AS present_count,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) AS avg_score,
                SUM(COALESCE(simr.pages_memorized,0)) AS pages_memorized
            FROM student_ijazasanad_memorization_report simr
            JOIN classes c ON simr.class_id = c.id
            JOIN class_students cs ON simr.student_id = cs.student_id AND simr.class_id = cs.class_id
            LEFT JOIN attendance_options ao ON simr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON simr.ijazasanad_evaluation_id = eso.id
            WHERE simr.class_id IN (" . implode(',', $classIds) . ")
              AND YEAR(simr.created_at) = ?
              AND MONTH(simr.created_at) = ?
              AND cs.deleted_at IS NULL
              {$studentFilter}
            GROUP BY DATE(simr.created_at), simr.class_id, c.class_code
            ORDER BY report_date DESC, simr.class_id
            LIMIT 50
        ", [$year, $month]);

        // Map translatable class names
        if (!empty($dailyTrends)) {
            $trendClassIds = array_values(array_unique(array_map(function($t){ return (int)$t->class_id; }, $dailyTrends)));
            $classMap = collect(\App\Classes::whereIn('id', $trendClassIds)->get())->keyBy('id');
            foreach ($dailyTrends as $t) {
                $t->class_name = optional($classMap->get((int)$t->class_id))->name ?? ($t->class_name ?? 'N/A');
            }
        }

        // At-risk students
        $atRiskStudents = \DB::select("
            SELECT 
                s.full_name,
                COUNT(*) AS total_sessions,
                COUNT(CASE WHEN ao.title IN ('late','on_time') THEN 1 END) AS attended_sessions,
                ROUND((COUNT(CASE WHEN ao.title IN ('late','on_time') THEN 1 END)/COUNT(*))*100,1) AS attendance_rate,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) AS avg_score
            FROM student_ijazasanad_memorization_report simr
            JOIN students s ON simr.student_id = s.id
            JOIN class_students cs ON simr.student_id = cs.student_id AND simr.class_id = cs.class_id
            LEFT JOIN attendance_options ao ON simr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON simr.ijazasanad_evaluation_id = eso.id
            WHERE simr.class_id IN (" . implode(',', $classIds) . ")
              AND YEAR(simr.created_at) = ?
              AND MONTH(simr.created_at) = ?
              AND cs.deleted_at IS NULL
              {$studentFilter}
            GROUP BY simr.student_id, s.full_name
            HAVING attendance_rate < 75 OR avg_score < 0.5
            ORDER BY attendance_rate ASC, avg_score ASC
            LIMIT 15
        ", [$year, $month]);

        // Teacher performance
        $teacherPerformance = \DB::select("
            SELECT 
                simr.class_id,
                c.class_code,
                GROUP_CONCAT(DISTINCT u.full_name SEPARATOR ', ') AS teacher_name,
                COUNT(DISTINCT simr.student_id) AS unique_students,
                COUNT(DISTINCT DATE(simr.created_at)) AS actual_sessions,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) AS avg_evaluation,
                ROUND((COUNT(CASE WHEN ao.title IN ('late','on_time') THEN 1 END)/NULLIF(COUNT(*),0))*100,1) AS class_attendance_rate,
                SUM(COALESCE(simr.pages_memorized,0)) AS total_pages_taught
            FROM student_ijazasanad_memorization_report simr
            JOIN classes c ON simr.class_id = c.id
            JOIN class_teachers ct ON c.id = ct.class_id
            JOIN employees u ON ct.employee_id = u.id
            LEFT JOIN attendance_options ao ON simr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON simr.ijazasanad_evaluation_id = eso.id
            WHERE simr.class_id IN (" . implode(',', $classIds) . ")
              AND YEAR(simr.created_at) = ?
              AND MONTH(simr.created_at) = ?
            GROUP BY simr.class_id, c.class_code
            ORDER BY avg_evaluation DESC, class_attendance_rate DESC
        ", [$year, $month]);

        return [
            'daily_trends' => $dailyTrends,
            'at_risk_students' => $atRiskStudents,
            'teacher_performance' => $teacherPerformance,
        ];
    }

    private function createPerformanceDashboard($worksheet, int $startRow, array $analytics): int
    {
        $dailyTrends = $analytics['daily_trends'];
        $atRiskStudents = $analytics['at_risk_students'];
        $teacherPerformance = $analytics['teacher_performance'];

        // Title
        $worksheet->setCellValue("A{$startRow}", "\xF0\x9F\x93\x8C PERFORMANCE DASHBOARD - DAILY INSIGHTS (Ijazah & Sanad)");
        $worksheet->mergeCells("A{$startRow}:Q{$startRow}");
        $worksheet->getStyle("A{$startRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '1f4e79']]
        ]);

        $currentRow = $startRow + 2;

        // Recent Performance Trends
        $worksheet->setCellValue("A{$currentRow}", "\xF0\x9F\x93\x88 RECENT PERFORMANCE TRENDS (All Selected Classes)");
        $worksheet->mergeCells("A{$currentRow}:F{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2E7D32']]
        ]);
        $currentRow++;

        $trendHeaders = ['Date', 'Class ID', 'Class Name', 'Students', 'Sessions', 'Avg Score', 'Attendance %', 'Pages'];
        foreach ($trendHeaders as $i => $h) {
            $col = chr(65 + $i);
            $worksheet->setCellValue("{$col}{$currentRow}", $h);
        }
        $currentRow++;

        foreach (array_slice($dailyTrends, 0, 30) as $trend) {
            $attendanceRate = $trend->total_sessions > 0 ? round(((float)$trend->present_count / (float)$trend->total_sessions) * 100, 1) : 0;
            $worksheet->setCellValue("A{$currentRow}", $trend->report_date);
            $worksheet->setCellValue("B{$currentRow}", $trend->class_id);
            $worksheet->setCellValue("C{$currentRow}", $trend->class_name);
            $worksheet->setCellValue("D{$currentRow}", $trend->active_students);
            $worksheet->setCellValue("E{$currentRow}", $trend->total_sessions);
            $worksheet->setCellValue("F{$currentRow}", round((float)($trend->avg_score ?? 0) * 100, 1) . '%');
            $worksheet->setCellValue("G{$currentRow}", $attendanceRate . '%');
            $worksheet->setCellValue("H{$currentRow}", $trend->pages_memorized);
            $currentRow++;
        }

        $currentRow += 2;

        // At-Risk Students
        $worksheet->setCellValue("A{$currentRow}", "\xF0\x9F\x9A\xA8 AT-RISK STUDENTS (Immediate Attention Required - All Classes)");
        $worksheet->mergeCells("A{$currentRow}:Q{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'FF5722']]
        ]);
        $currentRow++;

        if (count($atRiskStudents) > 0) {
            $riskHeaders = ['Student Name', 'Sessions', 'Attended', 'Attendance %', 'Avg Score', 'Risk Level'];
            foreach ($riskHeaders as $i => $h) { $col = chr(65 + $i); $worksheet->setCellValue("{$col}{$currentRow}", $h); }
            $currentRow++;
            foreach (array_slice($atRiskStudents, 0, 15) as $s) {
                $riskLevel = '🔴 Critical';
                if ($s->attendance_rate >= 50 && $s->avg_score >= 0.3) { $riskLevel = '⚠️ Moderate'; }
                $worksheet->setCellValue("A{$currentRow}", $s->full_name);
                $worksheet->setCellValue("B{$currentRow}", $s->total_sessions);
                $worksheet->setCellValue("C{$currentRow}", $s->attended_sessions);
                $worksheet->setCellValue("D{$currentRow}", $s->attendance_rate . '%');
                $worksheet->setCellValue("E{$currentRow}", round((float)($s->avg_score ?? 0) * 100, 1) . '%');
                $worksheet->setCellValue("F{$currentRow}", $riskLevel);
                $currentRow++;
            }
        } else {
            $worksheet->setCellValue("A{$currentRow}", "✅ No at-risk students identified across all classes");
            $currentRow++;
        }

        $currentRow += 2;

        // Teacher Performance Summary
        $worksheet->setCellValue("A{$currentRow}", "👨‍🏫 TEACHER PERFORMANCE SUMMARY (All Classes)");
        $worksheet->mergeCells("A{$currentRow}:Q{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '1565C0']]
        ]);
        $currentRow++;

        if (count($teacherPerformance) > 0) {
            $headers = ['Class', 'Class Name', 'Teacher', 'Students', 'Sessions', 'Avg Score', 'Attendance %', 'Pages Taught'];
            foreach ($headers as $i => $h) { $col = chr(65 + $i); $worksheet->setCellValue("{$col}{$currentRow}", $h); }
            $currentRow++;

            $classIdList = array_values(array_unique(array_map(function($t){ return (int)$t->class_id; }, $teacherPerformance)));
            $classNameMap = collect(\App\Classes::whereIn('id', $classIdList)->get())->keyBy('id');
            foreach ($teacherPerformance as $t) {
                $className = optional($classNameMap->get((int)$t->class_id))->name ?? 'N/A';
                $worksheet->setCellValue("A{$currentRow}", $t->name);
                $worksheet->setCellValue("B{$currentRow}", $className);
                $worksheet->setCellValue("C{$currentRow}", $t->teacher_name);
                $worksheet->setCellValue("D{$currentRow}", $t->unique_students);
                $worksheet->setCellValue("E{$currentRow}", $t->actual_sessions);
                $worksheet->setCellValue("F{$currentRow}", round((float)($t->avg_evaluation ?? 0) * 100, 1) . '%');
                $worksheet->setCellValue("G{$currentRow}", $t->class_attendance_rate . '%');
                $worksheet->setCellValue("H{$currentRow}", $t->total_pages_taught);
                $currentRow++;
            }
        } else {
            $worksheet->setCellValue("A{$currentRow}", "No teacher performance data available");
            $currentRow++;
        }

        return $currentRow + 2;
    }
}

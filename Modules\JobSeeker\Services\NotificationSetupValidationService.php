<?php

namespace Modules\JobSeeker\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Mo<PERSON><PERSON>\JobSeeker\Entities\JobCategory;
use Mo<PERSON><PERSON>\JobSeeker\Entities\JobNotificationSetup;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\JobNotificationRecipientEmail;
use Mo<PERSON>les\JobSeeker\Entities\JobSeekerPersonalContact;

class NotificationSetupValidationService
{
    /**
     * Validation steps for notification setup
     */
    const STEP_BASIC_INFO = 'basic_info';
    const STEP_CATEGORIES = 'categories';
    const STEP_RECIPIENTS = 'recipients';
    const STEP_PREFERENCES = 'preferences';
    const STEP_DUPLICATE_CHECK = 'duplicate_check';
    const STEP_FINAL_VALIDATION = 'final_validation';

    /**
     * All validation steps in order
     */
    const VALIDATION_STEPS = [
        self::STEP_BASIC_INFO,
        self::STEP_CATEGORIES,
        self::STEP_RECIPIENTS,
        self::STEP_PREFERENCES,
        self::STEP_DUPLICATE_CHECK,
        self::STEP_FINAL_VALIDATION
    ];

    /**
     * Perform comprehensive multi-step validation for notification setup
     *
     * @param Request $request
     * @param int|null $excludeSetupId For updates, exclude this setup from duplicate checks
     * @return array Validation result with step-by-step feedback
     */
    public function validateNotificationSetup(Request $request, ?int $excludeSetupId = null): array
    {
        Log::info('NotificationSetupValidationService: Starting multi-step validation', [
            'exclude_setup_id' => $excludeSetupId,
            'request_data_keys' => array_keys($request->all())
        ]);

        $validationResult = [
            'success' => false,
            'current_step' => null,
            'completed_steps' => [],
            'failed_step' => null,
            'errors' => [],
            'warnings' => [],
            'suggestions' => [],
            'validated_data' => [],
            'next_step' => null,
            'progress_percentage' => 0,
            'duplicate_info' => null
        ];

        try {
            // Step 1: Basic Information Validation
            $basicInfoResult = $this->validateBasicInfo($request);
            if (!$basicInfoResult['success']) {
                return $this->buildFailedResult($validationResult, self::STEP_BASIC_INFO, $basicInfoResult);
            }
            $validationResult['completed_steps'][] = self::STEP_BASIC_INFO;
            $validationResult['validated_data'] = array_merge($validationResult['validated_data'], $basicInfoResult['data']);

            // Step 2: Categories Validation
            $categoriesResult = $this->validateCategories($request);
            if (!$categoriesResult['success']) {
                return $this->buildFailedResult($validationResult, self::STEP_CATEGORIES, $categoriesResult);
            }
            $validationResult['completed_steps'][] = self::STEP_CATEGORIES;
            $validationResult['validated_data'] = array_merge($validationResult['validated_data'], $categoriesResult['data']);
            $validationResult['warnings'] = array_merge($validationResult['warnings'], $categoriesResult['warnings'] ?? []);

            // Step 3: Recipients Validation
            $recipientsResult = $this->validateRecipients($request);
            if (!$recipientsResult['success']) {
                return $this->buildFailedResult($validationResult, self::STEP_RECIPIENTS, $recipientsResult);
            }
            $validationResult['completed_steps'][] = self::STEP_RECIPIENTS;
            $validationResult['validated_data'] = array_merge($validationResult['validated_data'], $recipientsResult['data']);
            $validationResult['suggestions'] = array_merge($validationResult['suggestions'], $recipientsResult['suggestions'] ?? []);

            // Step 4: Preferences Validation
            $preferencesResult = $this->validatePreferences($request);
            if (!$preferencesResult['success']) {
                return $this->buildFailedResult($validationResult, self::STEP_PREFERENCES, $preferencesResult);
            }
            $validationResult['completed_steps'][] = self::STEP_PREFERENCES;
            $validationResult['validated_data'] = array_merge($validationResult['validated_data'], $preferencesResult['data']);

            // Step 5: Duplicate Check (unless bypassed)
            $bypassDuplicateCheck = $request->boolean('bypass_duplicate_check', false);
            if (!$bypassDuplicateCheck) {
                $duplicateResult = $this->validateDuplicates($validationResult['validated_data'], $excludeSetupId);
                if (!$duplicateResult['success']) {
                    $validationResult['duplicate_info'] = $duplicateResult['duplicate_info'];
                    return $this->buildFailedResult($validationResult, self::STEP_DUPLICATE_CHECK, $duplicateResult);
                }
            } else {
                Log::info('NotificationSetupValidationService: Duplicate check bypassed by user request');
            }
            $validationResult['completed_steps'][] = self::STEP_DUPLICATE_CHECK;

            // Step 6: Final Validation
            $finalResult = $this->validateFinalChecks($validationResult['validated_data']);
            if (!$finalResult['success']) {
                return $this->buildFailedResult($validationResult, self::STEP_FINAL_VALIDATION, $finalResult);
            }
            $validationResult['completed_steps'][] = self::STEP_FINAL_VALIDATION;

            // All steps completed successfully
            $validationResult['success'] = true;
            $validationResult['current_step'] = self::STEP_FINAL_VALIDATION;
            $validationResult['progress_percentage'] = 100;

            Log::info('NotificationSetupValidationService: Multi-step validation completed successfully', [
                'completed_steps' => $validationResult['completed_steps'],
                'warnings_count' => count($validationResult['warnings']),
                'suggestions_count' => count($validationResult['suggestions'])
            ]);

            return $validationResult;

        } catch (\Exception $e) {
            Log::error('NotificationSetupValidationService: Exception during validation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'current_step' => $validationResult['current_step']
            ]);

            $validationResult['success'] = false;
            $validationResult['errors'] = ['An unexpected error occurred during validation: ' . $e->getMessage()];
            return $validationResult;
        }
    }

    /**
     * Validate basic information step
     *
     * @param Request $request
     * @return array
     */
    private function validateBasicInfo(Request $request): array
    {
        Log::debug('NotificationSetupValidationService: Validating basic info step');

        try {
            $validator = Validator::make($request->all(), [
                'job_seeker_id' => 'required|integer|exists:job_seekers,id',
                'name' => 'required|string|max:255|min:3',
            ], [
                'job_seeker_id.required' => 'Job seeker identification is required.',
                'job_seeker_id.exists' => 'Invalid job seeker account.',
                'name.required' => 'Setup name is required.',
                'name.min' => 'Setup name must be at least 3 characters long.',
                'name.max' => 'Setup name cannot exceed 255 characters.',
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'errors' => $validator->errors()->all(),
                    'field_errors' => $validator->errors()->toArray()
                ];
            }

            $validatedData = $validator->validated();

            // Additional business logic checks
            $suggestions = [];
            $warnings = [];

            // Check if name is descriptive enough
            if (strlen(trim($validatedData['name'])) < 10) {
                $suggestions[] = 'Consider using a more descriptive name for easier identification later.';
            }

            // Check for common naming patterns
            $commonPatterns = ['test', 'temp', 'default', 'new setup'];
            $nameLower = strtolower($validatedData['name']);
            foreach ($commonPatterns as $pattern) {
                if (strpos($nameLower, $pattern) !== false) {
                    $warnings[] = "The name contains '{$pattern}' which might not be descriptive enough.";
                    break;
                }
            }

            return [
                'success' => true,
                'data' => $validatedData,
                'suggestions' => $suggestions,
                'warnings' => $warnings
            ];

        } catch (\Exception $e) {
            Log::error('NotificationSetupValidationService: Error in basic info validation', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'errors' => ['Failed to validate basic information: ' . $e->getMessage()]
            ];
        }
    }

    /**
     * Validate categories step
     *
     * @param Request $request
     * @return array
     */
    private function validateCategories(Request $request): array
    {
        Log::debug('NotificationSetupValidationService: Validating categories step');

        try {
            $validator = Validator::make($request->all(), [
                'categories' => 'required|array|min:1|max:10',
                'categories.*' => 'integer|exists:job_categories,id',
            ], [
                'categories.required' => 'At least one job category must be selected.',
                'categories.min' => 'Please select at least one job category.',
                'categories.max' => 'You can select up to 10 job categories maximum.',
                'categories.*.exists' => 'One or more selected categories are invalid.',
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'errors' => $validator->errors()->all(),
                    'field_errors' => $validator->errors()->toArray()
                ];
            }

            $validatedData = $validator->validated();
            $categoryIds = $validatedData['categories'];

            // Additional category validation
            $categories = JobCategory::whereIn('id', $categoryIds)->get();
            $warnings = [];
            $suggestions = [];

            // Check for inactive categories
            $inactiveCategories = $categories->where('is_active', false);
            if ($inactiveCategories->isNotEmpty()) {
                return [
                    'success' => false,
                    'errors' => ['Some selected categories are no longer active: ' . $inactiveCategories->pluck('name')->implode(', ')]
                ];
            }

            // Check for archived categories
            $archivedCategories = $categories->where('is_archived', true);
            if ($archivedCategories->isNotEmpty()) {
                $warnings[] = 'Some selected categories are archived and may have limited job activity: ' . 
                             $archivedCategories->pluck('name')->implode(', ');
            }

            // Suggest related categories
            if (count($categoryIds) < 3) {
                $suggestions[] = 'Consider adding more related categories to increase your job matching opportunities.';
            }

            // Check for overly broad selection
            if (count($categoryIds) > 7) {
                $warnings[] = 'Selecting many categories might result in too many notifications. Consider focusing on your most relevant interests.';
            }

            return [
                'success' => true,
                'data' => $validatedData,
                'warnings' => $warnings,
                'suggestions' => $suggestions,
                'category_details' => $categories->map(function($cat) {
                    return [
                        'id' => $cat->id,
                        'name' => $cat->name,
                        'is_archived' => $cat->is_archived ?? false
                    ];
                })->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('NotificationSetupValidationService: Error in categories validation', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'errors' => ['Failed to validate categories: ' . $e->getMessage()]
            ];
        }
    }

    /**
     * Validate recipients step
     *
     * @param Request $request
     * @return array
     */
    private function validateRecipients(Request $request): array
    {
        Log::debug('NotificationSetupValidationService: Validating recipients step');

        try {
            $validator = Validator::make($request->all(), [
                'recipients' => 'required|array|min:1|max:20',
                'recipients.*.email' => 'required|email|max:255',
                'recipients.*.name' => 'nullable|string|max:255',
            ], [
                'recipients.required' => 'At least one recipient email is required.',
                'recipients.min' => 'Please provide at least one recipient email.',
                'recipients.max' => 'You can add up to 20 recipients maximum.',
                'recipients.*.email.required' => 'Each recipient must have a valid email address.',
                'recipients.*.email.email' => 'Please provide valid email addresses for all recipients.',
                'recipients.*.name.max' => 'Recipient names cannot exceed 255 characters.',
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'errors' => $validator->errors()->all(),
                    'field_errors' => $validator->errors()->toArray()
                ];
            }

            $validatedData = $validator->validated();
            $recipients = $validatedData['recipients'];

            // Additional recipient validation
            $warnings = [];
            $suggestions = [];
            $processedEmails = [];
            $duplicateEmails = [];

            foreach ($recipients as $index => $recipient) {
                $email = strtolower(trim($recipient['email']));
                
                // Check for duplicate emails within the request
                if (in_array($email, $processedEmails)) {
                    $duplicateEmails[] = $email;
                } else {
                    $processedEmails[] = $email;
                }

                // Validate email domain
                $domain = substr(strrchr($email, "@"), 1);
                if ($this->isDisposableEmailDomain($domain)) {
                    $warnings[] = "Email {$email} appears to be from a disposable email service.";
                }

                // Check if email exists in personal contacts
                $jobSeekerId = $request->input('job_seeker_id');
                if ($jobSeekerId) {
                    $existingContact = JobSeekerPersonalContact::whereHas('recipientEmail', function($query) use ($email) {
                        $query->where('email', $email);
                    })->where('job_seeker_id', $jobSeekerId)->first();

                    if ($existingContact) {
                        $suggestions[] = "Email {$email} is already in your personal contacts.";
                    }
                }
            }

            // Handle duplicate emails
            if (!empty($duplicateEmails)) {
                return [
                    'success' => false,
                    'errors' => ['Duplicate email addresses found: ' . implode(', ', $duplicateEmails)]
                ];
            }

            // Normalize recipients data
            $normalizedRecipients = [];
            foreach ($recipients as $recipient) {
                $normalizedRecipients[] = [
                    'email' => strtolower(trim($recipient['email'])),
                    'name' => !empty($recipient['name']) ? trim($recipient['name']) : null
                ];
            }

            $validatedData['recipients'] = $normalizedRecipients;

            // Suggestions based on recipient count
            if (count($recipients) === 1) {
                $suggestions[] = 'Consider adding additional recipients to ensure you don\'t miss important job notifications.';
            } elseif (count($recipients) > 10) {
                $warnings[] = 'Large recipient lists may result in higher email delivery costs and potential spam issues.';
            }

            return [
                'success' => true,
                'data' => $validatedData,
                'warnings' => $warnings,
                'suggestions' => $suggestions,
                'recipient_count' => count($normalizedRecipients)
            ];

        } catch (\Exception $e) {
            Log::error('NotificationSetupValidationService: Error in recipients validation', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'errors' => ['Failed to validate recipients: ' . $e->getMessage()]
            ];
        }
    }

    /**
     * Validate preferences step
     *
     * @param Request $request
     * @return array
     */
    private function validatePreferences(Request $request): array
    {
        Log::debug('NotificationSetupValidationService: Validating preferences step');

        try {
            $validator = Validator::make($request->all(), [
                'receive_push_notifications' => 'sometimes|boolean',
                'notification_frequency' => 'sometimes|string|in:immediate,daily,weekly',
                'include_expired_jobs' => 'sometimes|boolean',
            ], [
                'notification_frequency.in' => 'Notification frequency must be immediate, daily, or weekly.',
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'errors' => $validator->errors()->all(),
                    'field_errors' => $validator->errors()->toArray()
                ];
            }

            $validatedData = $validator->validated();
            $suggestions = [];

            // Set defaults for missing preferences
            $validatedData['receive_push_notifications'] = $validatedData['receive_push_notifications'] ?? false;
            $validatedData['notification_frequency'] = $validatedData['notification_frequency'] ?? 'immediate';
            $validatedData['include_expired_jobs'] = $validatedData['include_expired_jobs'] ?? false;

            // Provide suggestions based on preferences
            if (!$validatedData['receive_push_notifications']) {
                $suggestions[] = 'Enable push notifications to get instant alerts on your mobile device.';
            }

            if ($validatedData['notification_frequency'] === 'weekly') {
                $suggestions[] = 'Weekly notifications might cause you to miss time-sensitive job opportunities. Consider daily or immediate notifications.';
            }

            return [
                'success' => true,
                'data' => $validatedData,
                'suggestions' => $suggestions
            ];

        } catch (\Exception $e) {
            Log::error('NotificationSetupValidationService: Error in preferences validation', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'errors' => ['Failed to validate preferences: ' . $e->getMessage()]
            ];
        }
    }

    /**
     * Validate for duplicates
     *
     * @param array $validatedData
     * @param int|null $excludeSetupId
     * @return array
     */
    private function validateDuplicates(array $validatedData, ?int $excludeSetupId = null): array
    {
        Log::debug('NotificationSetupValidationService: Validating duplicates step', [
            'exclude_setup_id' => $excludeSetupId
        ]);

        try {
            $jobSeekerId = $validatedData['job_seeker_id'];
            $categoryIds = $validatedData['categories'];
            $recipientEmails = array_column($validatedData['recipients'], 'email');

            // Check for exact duplicates
            $duplicateInfo = $this->checkForExactDuplicate($jobSeekerId, $categoryIds, $recipientEmails, $excludeSetupId);
            
            if ($duplicateInfo) {
                return [
                    'success' => false,
                    'errors' => [
                        "A similar notification setup named '{$duplicateInfo['name']}' already exists with the same categories and recipients."
                    ],
                    'duplicate_info' => $duplicateInfo,
                    'allow_override' => true
                ];
            }

            // Check for partial duplicates (same categories, different recipients)
            $partialDuplicates = $this->checkForPartialDuplicates($jobSeekerId, $categoryIds, $recipientEmails, $excludeSetupId);
            
            $warnings = [];
            if (!empty($partialDuplicates)) {
                foreach ($partialDuplicates as $duplicate) {
                    $warnings[] = "Setup '{$duplicate['name']}' has similar categories but different recipients.";
                }
            }

            return [
                'success' => true,
                'data' => [],
                'warnings' => $warnings,
                'partial_duplicates' => $partialDuplicates
            ];

        } catch (\Exception $e) {
            Log::error('NotificationSetupValidationService: Error in duplicate validation', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'errors' => ['Failed to check for duplicates: ' . $e->getMessage()]
            ];
        }
    }

    /**
     * Perform final validation checks
     *
     * @param array $validatedData
     * @return array
     */
    private function validateFinalChecks(array $validatedData): array
    {
        Log::debug('NotificationSetupValidationService: Performing final validation checks');

        try {
            $warnings = [];
            $suggestions = [];

            // Check total setup count for job seeker
            $jobSeekerId = $validatedData['job_seeker_id'];
            $existingSetupsCount = JobNotificationSetup::where('job_seeker_id', $jobSeekerId)
                ->where('is_active', true)
                ->count();

            if ($existingSetupsCount >= 10) {
                $warnings[] = 'You have many active notification setups. Consider consolidating similar setups for better management.';
            }

            // Validate category-recipient combination efficiency
            $categoryCount = count($validatedData['categories']);
            $recipientCount = count($validatedData['recipients']);
            
            if ($categoryCount > 5 && $recipientCount > 5) {
                $warnings[] = 'This setup has many categories and recipients, which may generate a high volume of notifications.';
            }

            // Check for optimal setup configuration
            if ($categoryCount === 1 && $recipientCount === 1) {
                $suggestions[] = 'This is a focused setup. Consider creating additional setups for broader job coverage.';
            }

            return [
                'success' => true,
                'data' => [],
                'warnings' => $warnings,
                'suggestions' => $suggestions
            ];

        } catch (\Exception $e) {
            Log::error('NotificationSetupValidationService: Error in final validation', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'errors' => ['Failed to complete final validation: ' . $e->getMessage()]
            ];
        }
    }

    /**
     * Check for exact duplicate setups
     *
     * @param int $jobSeekerId
     * @param array $categoryIds
     * @param array $recipientEmails
     * @param int|null $excludeSetupId
     * @return array|null
     */
    private function checkForExactDuplicate(int $jobSeekerId, array $categoryIds, array $recipientEmails, ?int $excludeSetupId = null): ?array
    {
        $existingSetupsQuery = JobNotificationSetup::with(['categories', 'recipients'])
            ->where('job_seeker_id', $jobSeekerId)
            ->where('is_active', true);

        if ($excludeSetupId) {
            $existingSetupsQuery->where('id', '!=', $excludeSetupId);
        }

        $existingSetups = $existingSetupsQuery->get();
        $normalizedRecipients = array_map('strtolower', $recipientEmails);

        foreach ($existingSetups as $setup) {
            $existingCategoryIds = $setup->categories->pluck('id')->map('intval')->toArray();
            $existingRecipientEmails = $setup->recipients->pluck('email')->map('strtolower')->toArray();

            if (empty($existingRecipientEmails) && $setup->jobSeeker) {
                $existingRecipientEmails = [strtolower($setup->jobSeeker->email)];
            }

            $newCategoryIds = array_map('intval', $categoryIds);

            $isExactCategoryMatch = count($newCategoryIds) === count($existingCategoryIds) && 
                                   count(array_intersect($newCategoryIds, $existingCategoryIds)) === count($newCategoryIds);
            $isExactRecipientMatch = count($normalizedRecipients) === count($existingRecipientEmails) && 
                                    count(array_intersect($normalizedRecipients, $existingRecipientEmails)) === count($normalizedRecipients);

            if ($isExactCategoryMatch && $isExactRecipientMatch) {
                return [
                    'id' => $setup->id,
                    'name' => $setup->name,
                    'created_at' => $setup->created_at->format('Y-m-d H:i:s'),
                    'categories' => $setup->categories->pluck('name')->toArray(),
                    'recipients' => $existingRecipientEmails
                ];
            }
        }

        return null;
    }

    /**
     * Check for partial duplicate setups
     *
     * @param int $jobSeekerId
     * @param array $categoryIds
     * @param array $recipientEmails
     * @param int|null $excludeSetupId
     * @return array
     */
    private function checkForPartialDuplicates(int $jobSeekerId, array $categoryIds, array $recipientEmails, ?int $excludeSetupId = null): array
    {
        $existingSetupsQuery = JobNotificationSetup::with(['categories', 'recipients'])
            ->where('job_seeker_id', $jobSeekerId)
            ->where('is_active', true);

        if ($excludeSetupId) {
            $existingSetupsQuery->where('id', '!=', $excludeSetupId);
        }

        $existingSetups = $existingSetupsQuery->get();
        $partialDuplicates = [];
        $newCategoryIds = array_map('intval', $categoryIds);

        foreach ($existingSetups as $setup) {
            $existingCategoryIds = $setup->categories->pluck('id')->map('intval')->toArray();
            
            // Check for significant category overlap (>= 70%)
            $intersection = array_intersect($newCategoryIds, $existingCategoryIds);
            $union = array_unique(array_merge($newCategoryIds, $existingCategoryIds));
            $overlapPercentage = count($intersection) / count($union) * 100;

            if ($overlapPercentage >= 70) {
                $partialDuplicates[] = [
                    'id' => $setup->id,
                    'name' => $setup->name,
                    'overlap_percentage' => round($overlapPercentage, 1),
                    'common_categories' => $setup->categories->whereIn('id', $intersection)->pluck('name')->toArray()
                ];
            }
        }

        return $partialDuplicates;
    }

    /**
     * Check if email domain is from a disposable email service
     *
     * @param string $domain
     * @return bool
     */
    private function isDisposableEmailDomain(string $domain): bool
    {
        $disposableDomains = [
            '10minutemail.com', 'tempmail.org', 'guerrillamail.com', 'mailinator.com',
            'throwaway.email', 'temp-mail.org', 'getnada.com', 'maildrop.cc'
        ];

        return in_array(strtolower($domain), $disposableDomains);
    }

    /**
     * Build failed validation result
     *
     * @param array $validationResult
     * @param string $failedStep
     * @param array $stepResult
     * @return array
     */
    private function buildFailedResult(array $validationResult, string $failedStep, array $stepResult): array
    {
        $validationResult['success'] = false;
        $validationResult['failed_step'] = $failedStep;
        $validationResult['current_step'] = $failedStep;
        $validationResult['errors'] = $stepResult['errors'] ?? [];
        $validationResult['field_errors'] = $stepResult['field_errors'] ?? [];
        
        // Calculate progress percentage
        $stepIndex = array_search($failedStep, self::VALIDATION_STEPS);
        $validationResult['progress_percentage'] = $stepIndex !== false ? 
            round(($stepIndex / count(self::VALIDATION_STEPS)) * 100) : 0;

        // Determine next step
        $nextStepIndex = $stepIndex + 1;
        $validationResult['next_step'] = isset(self::VALIDATION_STEPS[$nextStepIndex]) ? 
            self::VALIDATION_STEPS[$nextStepIndex] : null;

        // Include any warnings or suggestions from the failed step
        if (isset($stepResult['warnings'])) {
            $validationResult['warnings'] = array_merge($validationResult['warnings'], $stepResult['warnings']);
        }
        if (isset($stepResult['suggestions'])) {
            $validationResult['suggestions'] = array_merge($validationResult['suggestions'], $stepResult['suggestions']);
        }

        // Include duplicate info if available
        if (isset($stepResult['duplicate_info'])) {
            $validationResult['duplicate_info'] = $stepResult['duplicate_info'];
        }

        Log::warning('NotificationSetupValidationService: Validation failed at step', [
            'failed_step' => $failedStep,
            'errors' => $validationResult['errors'],
            'progress_percentage' => $validationResult['progress_percentage']
        ]);

        return $validationResult;
    }

    /**
     * Get validation step information
     *
     * @param string $step
     * @return array
     */
    public function getStepInfo(string $step): array
    {
        $stepInfo = [
            self::STEP_BASIC_INFO => [
                'title' => 'Basic Information',
                'description' => 'Provide a name and basic details for your notification setup',
                'fields' => ['name', 'job_seeker_id'],
                'tips' => ['Use a descriptive name that helps you identify this setup later']
            ],
            self::STEP_CATEGORIES => [
                'title' => 'Job Categories',
                'description' => 'Select the job categories you want to receive notifications for',
                'fields' => ['categories'],
                'tips' => ['Select 2-5 categories for optimal results', 'Avoid selecting too many categories to prevent notification overload']
            ],
            self::STEP_RECIPIENTS => [
                'title' => 'Recipients',
                'description' => 'Add email addresses that should receive job notifications',
                'fields' => ['recipients'],
                'tips' => ['Add 1-5 recipients for best delivery', 'Use professional email addresses', 'Avoid disposable email services']
            ],
            self::STEP_PREFERENCES => [
                'title' => 'Notification Preferences',
                'description' => 'Configure how and when you want to receive notifications',
                'fields' => ['receive_push_notifications', 'notification_frequency', 'include_expired_jobs'],
                'tips' => ['Enable push notifications for instant alerts', 'Choose immediate frequency for time-sensitive opportunities']
            ],
            self::STEP_DUPLICATE_CHECK => [
                'title' => 'Duplicate Check',
                'description' => 'Verify this setup doesn\'t duplicate existing configurations',
                'fields' => [],
                'tips' => ['Review similar setups to avoid redundancy', 'Consider consolidating overlapping setups']
            ],
            self::STEP_FINAL_VALIDATION => [
                'title' => 'Final Review',
                'description' => 'Final validation and optimization suggestions',
                'fields' => [],
                'tips' => ['Review all settings before saving', 'Consider the overall notification volume']
            ]
        ];

        return $stepInfo[$step] ?? [
            'title' => 'Unknown Step',
            'description' => 'Step information not available',
            'fields' => [],
            'tips' => []
        ];
    }

    /**
     * Get all validation steps with their information
     *
     * @return array
     */
    public function getAllStepsInfo(): array
    {
        $stepsInfo = [];
        foreach (self::VALIDATION_STEPS as $index => $step) {
            $stepsInfo[$step] = array_merge($this->getStepInfo($step), [
                'step_number' => $index + 1,
                'step_key' => $step,
                'is_required' => true
            ]);
        }
        return $stepsInfo;
    }

    /**
     * Validate a single step (for AJAX validation)
     *
     * @param Request $request
     * @param string $step
     * @param int|null $excludeSetupId
     * @return array
     */
    public function validateSingleStep(Request $request, string $step, ?int $excludeSetupId = null): array
    {
        Log::info('NotificationSetupValidationService: Validating single step', [
            'step' => $step,
            'exclude_setup_id' => $excludeSetupId
        ]);

        if (!in_array($step, self::VALIDATION_STEPS)) {
            return [
                'success' => false,
                'errors' => ['Invalid validation step specified.']
            ];
        }

        try {
            switch ($step) {
                case self::STEP_BASIC_INFO:
                    return $this->validateBasicInfo($request);
                
                case self::STEP_CATEGORIES:
                    return $this->validateCategories($request);
                
                case self::STEP_RECIPIENTS:
                    return $this->validateRecipients($request);
                
                case self::STEP_PREFERENCES:
                    return $this->validatePreferences($request);
                
                case self::STEP_DUPLICATE_CHECK:
                    // For single step validation, we need to get validated data from request
                    $basicData = $this->validateBasicInfo($request);
                    $categoriesData = $this->validateCategories($request);
                    $recipientsData = $this->validateRecipients($request);
                    
                    if (!$basicData['success'] || !$categoriesData['success'] || !$recipientsData['success']) {
                        return [
                            'success' => false,
                            'errors' => ['Previous steps must be valid before checking for duplicates.']
                        ];
                    }
                    
                    $combinedData = array_merge(
                        $basicData['data'],
                        $categoriesData['data'],
                        $recipientsData['data']
                    );
                    
                    return $this->validateDuplicates($combinedData, $excludeSetupId);
                
                case self::STEP_FINAL_VALIDATION:
                    // Similar to duplicate check, need all previous data
                    $allStepsValid = true;
                    $combinedData = [];
                    
                    foreach ([self::STEP_BASIC_INFO, self::STEP_CATEGORIES, self::STEP_RECIPIENTS, self::STEP_PREFERENCES] as $prevStep) {
                        $stepResult = $this->validateSingleStep($request, $prevStep, $excludeSetupId);
                        if (!$stepResult['success']) {
                            $allStepsValid = false;
                            break;
                        }
                        $combinedData = array_merge($combinedData, $stepResult['data'] ?? []);
                    }
                    
                    if (!$allStepsValid) {
                        return [
                            'success' => false,
                            'errors' => ['All previous steps must be valid before final validation.']
                        ];
                    }
                    
                    return $this->validateFinalChecks($combinedData);
                
                default:
                    return [
                        'success' => false,
                        'errors' => ['Unknown validation step.']
                    ];
            }
        } catch (\Exception $e) {
            Log::error('NotificationSetupValidationService: Error in single step validation', [
                'step' => $step,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'errors' => ['Validation error: ' . $e->getMessage()]
            ];
        }
    }

    /**
     * Get validation progress for a request
     *
     * @param Request $request
     * @param int|null $excludeSetupId
     * @return array
     */
    public function getValidationProgress(Request $request, ?int $excludeSetupId = null): array
    {
        $progress = [
            'completed_steps' => [],
            'current_step' => null,
            'next_step' => null,
            'progress_percentage' => 0,
            'step_results' => []
        ];

        foreach (self::VALIDATION_STEPS as $index => $step) {
            $stepResult = $this->validateSingleStep($request, $step, $excludeSetupId);
            $progress['step_results'][$step] = $stepResult;

            if ($stepResult['success']) {
                $progress['completed_steps'][] = $step;
            } else {
                $progress['current_step'] = $step;
                $progress['next_step'] = isset(self::VALIDATION_STEPS[$index + 1]) ? 
                    self::VALIDATION_STEPS[$index + 1] : null;
                break;
            }
        }

        $progress['progress_percentage'] = round((count($progress['completed_steps']) / count(self::VALIDATION_STEPS)) * 100);

        return $progress;
    }

    /**
     * Generate validation summary for display
     *
     * @param array $validationResult
     * @return array
     */
    public function generateValidationSummary(array $validationResult): array
    {
        $summary = [
            'status' => $validationResult['success'] ? 'success' : 'failed',
            'progress' => $validationResult['progress_percentage'] ?? 0,
            'completed_steps_count' => count($validationResult['completed_steps'] ?? []),
            'total_steps_count' => count(self::VALIDATION_STEPS),
            'has_warnings' => !empty($validationResult['warnings']),
            'has_suggestions' => !empty($validationResult['suggestions']),
            'warnings_count' => count($validationResult['warnings'] ?? []),
            'suggestions_count' => count($validationResult['suggestions'] ?? []),
            'errors_count' => count($validationResult['errors'] ?? []),
            'can_proceed' => $validationResult['success'] || (!empty($validationResult['duplicate_info']) && ($validationResult['duplicate_info']['allow_override'] ?? false)),
            'requires_confirmation' => !empty($validationResult['duplicate_info']),
            'next_action' => $this->determineNextAction($validationResult)
        ];

        return $summary;
    }

    /**
     * Determine the next action based on validation result
     *
     * @param array $validationResult
     * @return string
     */
    private function determineNextAction(array $validationResult): string
    {
        if ($validationResult['success']) {
            return 'save';
        }

        if (!empty($validationResult['duplicate_info'])) {
            return 'confirm_duplicate';
        }

        if ($validationResult['failed_step'] === self::STEP_BASIC_INFO) {
            return 'fix_basic_info';
        }

        if ($validationResult['failed_step'] === self::STEP_CATEGORIES) {
            return 'select_categories';
        }

        if ($validationResult['failed_step'] === self::STEP_RECIPIENTS) {
            return 'add_recipients';
        }

        if ($validationResult['failed_step'] === self::STEP_PREFERENCES) {
            return 'set_preferences';
        }

        return 'review_errors';
    }

    /**
     * Format validation errors for API response
     *
     * @param array $validationResult
     * @return array
     */
    public function formatValidationErrors(array $validationResult): array
    {
        $formattedErrors = [
            'general_errors' => $validationResult['errors'] ?? [],
            'field_errors' => $validationResult['field_errors'] ?? [],
            'warnings' => $validationResult['warnings'] ?? [],
            'suggestions' => $validationResult['suggestions'] ?? [],
            'step_info' => null
        ];

        if ($validationResult['failed_step'] ?? null) {
            $formattedErrors['step_info'] = $this->getStepInfo($validationResult['failed_step']);
        }

        return $formattedErrors;
    }

    /**
     * Check if validation allows bypass (for duplicate scenarios)
     *
     * @param array $validationResult
     * @return bool
     */
    public function canBypassValidation(array $validationResult): bool
    {
        return !$validationResult['success'] && 
               $validationResult['failed_step'] === self::STEP_DUPLICATE_CHECK &&
               !empty($validationResult['duplicate_info']);
    }

    /**
     * Get validation statistics for monitoring
     *
     * @return array
     */
    public function getValidationStatistics(): array
    {
        // This could be expanded to track validation metrics
        return [
            'total_steps' => count(self::VALIDATION_STEPS),
            'step_names' => self::VALIDATION_STEPS,
            'service_version' => '1.0.0',
            'last_updated' => now()->toDateTimeString()
        ];
    }
}

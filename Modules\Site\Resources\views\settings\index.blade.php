@extends('layouts.hound')

@section('content')
<div class="content">
  <h2 class="box-title">{{ trans('site::settings.website_settings') }}</h2>
<div class="nav-tabs-custom">
            <ul class="nav nav-tabs">
              <li class="active"><a href="#general" data-toggle="tab" aria-expanded="true">{{ trans('site::settings.general_settings') }}</a></li>
              <li class=""><a href="#contact" data-toggle="tab" aria-expanded="false">{{ trans('site::settings.contact_details') }} </a></li>
              <li class=""><a href="#social" data-toggle="tab" aria-expanded="false">{{ trans('site::settings.title') }} & {{ trans('site::settings.links') }} </a></li>
              <li class=""><a href="#theme" data-toggle="tab" aria-expanded="false">{{ trans('site::settings.theme.theme_settings') }}</a></li>
            </ul>
            <div class="tab-content">
              <div class="tab-pane active" id="general">

                        <form role="form" method="post" action="{{route('settings.update')}}">
                            @if ($errors->any())
                                <div class="alert alert-danger">
                                    <ul>
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                          {{csrf_field()}}
                            <!-- text input -->
                            <div class="form-group">
                              <label>{{ trans('site::settings.organization_name') }}</label>
                              <input type="text" name="organization[name]" class="form-control" placeholder="Enter ..." value="{{ $organization->name }}">
                            </div>
                            <div class="form-group">
                              <label>{{ trans('site::settings.organization_sub_domain') }}</label>
                              <input type="text" class="form-control" value="https://{{ strtolower($organization->username) }}.{{config('app.platform_domain')}}" disabled>
                            </div>
                            <div class="form-group">
                              <label>{{ trans('site::settings.organization_domain') }}</label>
                              <input type="text" name="organization[organization_domain]" class="form-control" placeholder="Enter ..." value="{{ $organization->domain }}">
                              <div class="alert alert-warning">
                              To activate your domain it should point to this IP address <code>{{gethostbyname(config('app.platform_domain'))}}</code>
                              </div>
                            </div>
                            <div class="form-group">
                              <label>{{ trans('site::settings.theme.theme') }}</label>
                              <select name="organization[theme]"  class="form-control" required="required">
                                  <option value="wajeha">Wajeha</option>
                                  <option value="itqan" disabled>ITQAN</option>
                              </select>
                            </div>


                            <div class="form-group">
                              <label>{{ trans('site::settings.organization_logo') }}</label>
                              <div class="row">
                                  <div class="col-sm-6">
                                      <div class="input-group">
                                        <span class="input-group-btn">
                                         <a id="logo" data-input="thumbnail" data-preview="holder" class="btn btn-primary">
                                           <i class="fa fa-picture-o"></i> Choose
                                         </a>
                                        </span>
                                        <input id="thumbnail" class="form-control" type="hidden" name="organization[logo]" value="{{ $organization->logo }}">
                                    </div>
                                  </div>
                                  <div class="col-sm-6">
                                      <img id="holder" style="max-width: 100%" src="{{ asset($organization->logo) }}">
                                  </div>
                              </div>
                            </div>
                            <div class="form-group">
                              <label>{{ trans('site::settings.languages') }}</label>
                                <!-- checkbox -->
                                <div class="form-group row">
                                @foreach (config('app.supported_locales') as $code )
                                  <div class="col-sm-4 col-md-3">
                                    <label>
                                        <input type="checkbox" name="organization[languages][]" value="{{$code}}"
                                        @if(in_array($code, config('app.locales')))
                                        checked 
                                        @endif
                                        >
                                        {{get_language_name($code)}}
                                    </label>
                                  </div>
                                @endforeach
                                </div>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary">{{ trans('common.save') }}</button>
                            </div>


                          </form>
              </div>
              <!-- /.tab-pane -->
              <div class="tab-pane" id="social">
                <form role="form" method="post" action="{{route('settings.update')}}">
                {{csrf_field()}}
                    <legend>{{ trans('site::settings.title') }}</legend>
                    @foreach (config('app.locales') as $code )
                    <div class="form-group">
                      <label>{{ trans('site::settings.title') }}[{{get_language_name($code)}}]</label>
                      <input type="text" name="website[website_title_{{$code}}]" class="form-control" placeholder="Enter ..." value="{{ $current_settings["website_title_{$code}"] ?? '' }}">
                    </div>
                   @endforeach
                   <hr>             
                    <legend>{{ trans('site::settings.links') }}</legend>
                     <div class="form-group">
                      <label>{{ trans('site::settings.links.facebook') }}</label>
                      <input type="text" name="links[links_facebook]" class="form-control" placeholder="Enter ..." value="{{ $current_settings["links_facebook"] ?? '' }}">
                     </div>
                     <div class="form-group">
                      <label>{{ trans('site::settings.links.twitter') }}</label>
                      <input type="text" name="links[links_twitter]" class="form-control" placeholder="Enter ..." value="{{ $current_settings["links_twitter"] ?? '' }}">
                     </div>
                     <div class="form-group">
                      <label>{{ trans('site::settings.links.linkedin') }}</label>
                      <input type="text" name="links[links_linkedin]" class="form-control" placeholder="Enter ..." value="{{ $current_settings["links_linkedin"] ?? '' }}">
                     </div>
                     <div class="form-group">
                      <label>{{ trans('site::settings.links.youtube') }}</label>
                      <input type="text" name="links[links_youtube]" class="form-control" placeholder="Enter ..." value="{{ $current_settings["links_youtube"] ?? '' }}">
                     </div>
                     <div class="form-group">
                      <label>{{ trans('site::settings.links.instagram') }}</label>
                      <input type="text" name="links[links_instagram]" class="form-control" placeholder="Enter ..." value="{{ $current_settings["links_instagram"] ?? '' }}">
                     </div>
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary">{{ trans('common.save') }}</button>
                    </div>

                </form>

              </div>
              <div class="tab-pane" id="contact">
                <form role="form" method="post" action="{{route('settings.update')}}">
                {{csrf_field()}}                  
                    <legend>{{ trans('site::settings.address') }}</legend>
                     <div class="form-group">
                      <label>{{ trans('site::settings.address') }}</label>
                      <input type="text" name="links[address]" class="form-control" placeholder="Enter ..." value="{{ $current_settings["address"] ?? '' }}">
                     </div>
                     <div class="form-group">
                        <legend for="">Organization Location</legend>
                        <div class="col-sm-6">
                            <label>{{ trans('site::settings.lat') }}</label>
                            <input type="text" name="website[lat]" class="form-control" placeholder="Enter ..." value="{{ $current_settings["lat"] ?? '' }}">
                        </div>
                        <div class="col-sm-6">
                            <label>{{ trans('site::settings.lng') }}</label>
                            <input type="text" name="website[lng]" class="form-control" placeholder="Enter ..." value="{{ $current_settings["lng"] ?? '' }}">
                        </div>
                    </div>
                     <div class="form-group">
                      <label>{{ trans('site::settings.support_email') }}</label>
                      <input type="text" name="website[support_email]" class="form-control" placeholder="Enter ..." value="{{ $current_settings["support_email"] ?? '' }}">
                     </div>
                     <div class="form-group">
                      <label>{{ trans('site::settings.phone') }}</label>
                      <input type="text" name="website[phone]" class="form-control" placeholder="Enter ..." value="{{ $current_settings["phone"] ?? '' }}">
                     </div>
                     @foreach (config('app.locales') as $code )
                    <div class="form-group">
                        <label>{{ trans('site::settings.working_hours') }} [{{get_language_name($code)}}]</label>
                        <input type="text" name="website[working_hours_{{$code}}]" class="form-control" placeholder="Enter ..." value="{{ $current_settings["working_hours_".$code] ?? '' }}">
                    </div>
                    @endforeach
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary">{{ trans('common.save') }}</button>
                    </div>

                </form>

              </div>
              <!-- /.tab-pane -->
              <div class="tab-pane" id="theme">
                         <form role="form" method="post" action="{{route('settings.update')}}">
                          {{csrf_field()}}
                            <!-- text input -->
                            @foreach ($theme_settings['settings'] as $setting => $type)
                                <div class="form-group">
                                <div class="col-md-12">
                                  <label>{{ trans('site::settings.'.$setting) }}</label>
                                </div>
                                
                                @if ($type == 'boolean')
                                <div class="form-group clearfix">
                                  <div class="col-md-3 col-sm-4">
                                    <label>
                                      <input type="radio" name="theme[{{$setting}}]" id="{{$setting}}1" value="1" {{ config('settings.'.$setting) == 1  ? 'checked': '' }}>
                                      {{ trans('site::settings.enable') }}
                                    </label>
                                  </div>
                                  <div class="col-md-3 col-sm-4">
                                    <label>
                                      <input type="radio" name="theme[{{$setting}}]" id="{{$setting}}2" value="0" {{ (config('settings.'.$setting) == 0  ||  config('settings.'.$setting) == null) ? 'checked': '' }}>
                                      {{ trans('site::settings.disable') }}
                                    </label>
                                  </div>
                                </div>
                                @elseif (isset($theme_settings[$type]))
                                <select name="theme[{{$setting}}]" id="input{{$setting}}" class="form-control" required="required">
                                    <option value="none">None</option>
                                    
                                    @if(is_array(\Illuminate\Support\Arr::first($theme_settings[$type])))
                                        @foreach ($theme_settings[$type] as $variation => $conf)
                                            <option value="{{$variation}}" {{ config('settings.'.$setting) == $variation ? 'selected': ''  }} >{{ucwords(str_replace('_' , ' ' ,$variation) )}}</option>
                                        @endforeach
                                    @else
                                        @foreach ($theme_settings[$type] as $variation)
                                            <option value="{{$variation}}" {{ config('settings.'.$setting) == $variation ? 'selected': ''  }}>{{ucwords(str_replace('_' , ' ' ,$variation) )}}</option>
                                        @endforeach
                                    @endif
                                </select>
                                @endif
                                </div>
                            @endforeach

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary">{{ trans('common.save') }}</button>
                            </div>


                          </form>
              </div>
              <!-- /.tab-pane -->
            </div>
            <!-- /.tab-content -->
          </div>

{{--         <div class="row">
                <div class="panel panel-default">
                    <div class="panel-heading">Settings</div>
                    <div class="panel-body">
                        <a href="{{ url('/settings/create') }}" class="btn btn-success btn-sm" title="Add New Setting">
                            <i class="fa fa-plus" aria-hidden="true"></i> Add New
                        </a>

                        {!! Form::open(['method' => 'GET', 'url' => '/settings', 'class' => 'navbar-form navbar-right', 'role' => 'search'])  !!}
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" placeholder="Search...">
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="submit">
                                    <i class="fa fa-search"></i>
                                </button>
                            </span>
                        </div>
                        {!! Form::close() !!}

                        <br/>
                        <br/>
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <thead>
                                    <tr>
                                        <th>ID</th><th>Name</th><th>Value</th><th>Organization Id</th><th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                @foreach($settings as $item)
                                    <tr>
                                        <td>{{ $item->id }}</td>
                                        <td>{{ $item->name }}</td><td>{{ $item->value }}</td><td>{{ $item->organization_id }}</td>
                                        <td>
                                            <a href="{{ url('/settings/' . $item->id) }}" title="View Setting"><button class="btn btn-info btn-xs"><i class="fa fa-eye" aria-hidden="true"></i> View</button></a>
                                            <a href="{{ url('/settings/' . $item->id . '/edit') }}" title="Edit Setting"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>
                                            {!! Form::open([
                                                'method'=>'DELETE',
                                                'url' => ['/settings', $item->id],
                                                'style' => 'display:inline'
                                            ]) !!}
                                                {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                                                        'type' => 'submit',
                                                        'class' => 'btn btn-danger btn-xs',
                                                        'title' => 'Delete Setting',
                                                        'onclick'=>'return confirm("Confirm delete?")'
                                                )) !!}
                                            {!! Form::close() !!}
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                            <div class="pagination-wrapper"> {!! $settings->appends(['search' => Request::get('search')])->render() !!} </div>
                        </div>

                    </div>
                </div>
        </div> --}}
    </div>
@endsection
@section('js')
 <script src="{{ asset('assets/lfm/js/lfm.js') }}"></script>
 <script type="text/javascript">
      $('#logo').filemanager('image');
 </script>
@endsection
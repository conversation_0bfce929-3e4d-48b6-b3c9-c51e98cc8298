@extends('home.layouts.home')
@section('page_title') {{trans('home_header.registration')}}   @endsection
<style>
    body {
        font-family: 'Roboto', sans-serif;
        font-size: 16px;
        font-weight: 300;
        color: #888;
        line-height: 30px;

    }

    .f1-buttons {
        direction: ltr !important;
    }
</style>
@section('content')
    <div class="row helvetica">
        <div class="col-sm-10 col-sm-offset-1 col-md-8 col-md-offset-2 col-lg-6 col-lg-offset-3 form-box">

            {!! Form::open(['url' => '/home/<USER>/save_student','name'=>'f2','id'=>'registrationform' ,'class' => 'f1','files' => true]) !!}

            <h3>{{trans('home_header.registration')}}</h3>

            <div class="f1-steps">
                <div class="f1-progress">
                    <div class="f1-progress-line" data-now-value="12.5" data-number-of-steps="6"
                         style="width: 12.5%;"></div>
                </div>
                <div class="f1-step active">
                    <div class="f1-step-icon"><i class="fa fa-question-circle"></i></div>
                    <p>{{trans('home_content.terms')}}</p>
                </div>
                <div class="f1-step">
                    <div class="f1-step-icon"><i class="fa fa-user"></i></div>
                    <p>{{trans('home_content.profile')}}</p>
                </div>
                <div class="f1-step">
                    <div class="f1-step-icon"><i class="fa fa-phone"></i></div>
                    <p>{{trans('home_content.contact')}}</p>
                </div>
                <div class="f1-step">
                    <div class="f1-step-icon"><i class="fa fa-line-chart"></i></div>
                    <p>{{trans('home_content.plans')}}</p>
                </div>
                <div class="f1-step">
                    <div class="f1-step-icon"><i class="fa fa-users"></i></div>
                    <p>{{trans('home_content.parent_info')}}</p>
                </div>
                <div class="f1-step">
                    <div class="f1-step-icon"><i class="fa fa-hourglass-end"></i></div>
                    <p>{{trans('home_content.special')}}</p>
                </div>
            </div>

            <fieldset>
                <h4 class="text-center" style="padding: 25px;">{{trans('home_content.terms')}} :</h4>
                <div class="text-center">
                    <?php echo htmlspecialchars_decode(cache('register_specifications'))?>
                </div>
                <br/>
                <div class="f1-buttons">
                    <button type="button" class="btn btn-next">{{trans('home_content.accept')}}</button>
                </div>
            </fieldset>

            <fieldset>
                <h4 class="text-center" style="padding: 25px;">{{trans('home_content.profile')}} :</h4>

                <img src="{{URL::to('home_style/images/sys_user/default.png')}}" id="imgmodel"
                     style="max-width: 100px;border-radius: 50%;height: 100px;max-height: 100px;" class="center-block">

                <div class="form-group text-center {{ $errors->has('avatar') ? 'has-error' : ''}}">
                    {!! Form::label('avatar', trans('home_content.avatar'), ['class' => ' control-label']) !!}
                    <div class="">
                        <input type="file" name="avatar" id="avatar" class="inputfile inputfile-2 "
                               onchange="imgaeload(this)" accept=".png,.jpg,.jpeg,.gif"/>
                        <label for="avatar">
                            <span class="glyphicon glyphicon-compressed"> </span> {{trans('home_content.avatar')}} &hellip;
                        </label>
                        {!! $errors->first('avatar', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="form-group  {{ $errors->has('full_name_ar') ? 'has-error' : ''}}">
                    {!! Form::label('full_name_ar', trans('home_content.full_name_ar'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('full_name_ar', null, ['class' => 'form-control','required'=>true]) !!}
                        {!! $errors->first('full_name_ar', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="form-group  {{ $errors->has('full_name_en') ? 'has-error' : ''}}">
                    {!! Form::label('full_name_en',  trans('home_content.full_name_en'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('full_name_en', null, ['class' => 'form-control','required'=>true]) !!}
                        {!! $errors->first('full_name_en', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group  {{ $errors->has('gender') ? 'has-error' : ''}}">
                    {!! Form::label('gender', trans('home_content.gender'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::select('gender', [trans('home_content.male'), trans('home_content.female')], null, ['class' => 'form-control','required'=>true]) !!}
                        {!! $errors->first('gender', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="form-group  {{ $errors->has('birth_day') ? 'has-error' : ''}}">
                    {!! Form::label('birth_day', trans('home_content.birth_day'), ['class' => 'col-md-12 control-label','required'=>true]) !!}
                    <div class="col-md-12">
                        {!! Form::date('birth_day',date('Y-m-d'), ['class' => 'form-control','required'=>true]) !!}
                        {!! $errors->first('birth_day', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="form-group  {{ $errors->has('education_level') ? 'has-error' : ''}}">
                    {!! Form::label('education_level',trans('home_content.education_level'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12" style="color: black !important;">
                        {!! Form::select('education_level', [trans('home_content.primary_school'),trans('home_content.secondary_school'),trans('home_content.diploma'),trans('home_content.bachlor_degree'),trans('home_content.master_level'),trans('home_content.phd')], null, ['class' => 'form-control','required'=>true]) !!}
                        {!! $errors->first('education_level', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>
                <div class="form-group  {{ $errors->has('nationality') ? 'has-error' : ''}}">
                    {!! Form::label('nationality', trans('home_content.nationality'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">

                        {{-- Form::text('nationality', null, ['class' => 'form-control','required'=>true]) --}}
                        <?php $nations = getNationality(); ?>
                        <select class="selectpicker form-control" data-live-search="true" name="nationality">
                            @foreach($nations as $nation)
                                <option style="color: black !important;" value="{{$nation}}"
                                        data-tokens="{{$nation}}">{{$nation}}</option>
                            @endforeach
                        </select>
                        {!! $errors->first('nationality', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group  {{ $errors->has('ic_or_passport_no') ? 'has-error' : ''}}">
                    {!! Form::label('ic_or_passport_no', trans('home_content.ic_or_passport_no'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('ic_or_passport_no', null, ['class' => 'form-control','required'=>true]) !!}
                        {!! $errors->first('ic_or_passport_no', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="clearfix"></div>
                <br/>
                <div class="f1-buttons">
                    <button type="button" class="btn btn-previous">{{trans('home_content.previous')}}</button>
                    <button type="button" class="btn btn-next">{{trans('home_content.next')}}</button>
                </div>
            </fieldset>

            <fieldset>
                <h4 class="text-center" style="padding: 25px;">{{trans('home_content.contact')}} :</h4>

                <div class="form-group  {{ $errors->has('tel') ? 'has-error' : ''}}">
                    {!! Form::label('tel', trans('home_content.tel'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-6">
                        {!! Form::text('tel', null, ['class' => 'form-control','required'=>true]) !!}
                        {!! $errors->first('tel', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="form-group  {{ $errors->has('email') ? 'has-error' : ''}}">
                    {!! Form::label('email', trans('home_content.email'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-6">
                        {!! Form::email('email', null, ['class' => 'form-control','required'=>true]) !!}
                        {!! $errors->first('email', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="clearfix"></div>
                <br/>
                <div class="f1-buttons">
                    <button type="button" class="btn btn-previous">{{trans('home_content.previous')}}</button>
                    <button type="button" class="btn btn-next">{{trans('home_content.next')}}</button>
                </div>
            </fieldset>

            <fieldset>
                <h4 class="text-center" style="padding: 25px;">{{trans('home_content.plans')}} :</h4>

                <div class="form-group  {{ $errors->has('center') ? 'has-error' : ''}}">
                    {!! Form::label('center', trans('home_content.center'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-6">
                        {{-- Form::number('center', null, ['class' => 'form-control','required'=>true]) --}}
                        <select class="selectpicker form-control" data-live-search="true" name="center">
                            @foreach($centers as $center)
                                <option style="color: black !important;" value="{{$center->id}}"
                                        data-tokens="<?php echo $center->{'title_' . App::getLocale()};?>"><?php echo $center->{'title_' . App::getLocale()};?></option>
                            @endforeach
                        </select>
                        {!! $errors->first('center', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="form-group  {{ $errors->has('program_id') ? 'has-error' : ''}}">
                    {!! Form::label('program_id', trans('home_content.program_id'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-6">
                        {{-- Form::number('program_id', null, ['class' => 'form-control']) --}}
                        <select class="selectpicker form-control" data-live-search="true" name="program_id">
                            @foreach($programs as $program)
                                <option style="color: black !important;" value="{{$program->id}}"
                                        data-tokens="<?php echo $program->{'title_' . App::getLocale()};?>"><?php echo $program->{'title_' . App::getLocale()};?></option>
                            @endforeach
                        </select>
                        {!! $errors->first('program_id', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="clearfix"></div>
                <br/>
                <div class="f1-buttons">
                    <button type="button" class="btn btn-previous">{{trans('home_content.previous')}}</button>
                    <button type="button" class="btn btn-next">{{trans('home_content.next')}}</button>
                </div>
            </fieldset>

            <fieldset>
                <h4 class="text-center" style="padding: 25px;">{{trans('home_content.parent_info')}} :</h4>
                <div class="form-group  {{ $errors->has('parent_gardians_name') ? 'has-error' : ''}}">
                    {!! Form::label('parent_gardians_name', trans('home_content.parent_gardians_name'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('parent_gardians_name', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('parent_gardians_name', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="form-group  {{ $errors->has('parent_gardians_relationship') ? 'has-error' : ''}}">
                    {!! Form::label('parent_gardians_relationship', trans('home_content.parent_gardians_relationship'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('parent_gardians_relationship', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('parent_gardians_relationship', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="form-group  {{ $errors->has('parent_gardians_job') ? 'has-error' : ''}}">
                    {!! Form::label('parent_gardians_job', trans('home_content.parent_gardians_job'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('parent_gardians_job', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('parent_gardians_job', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group  {{ $errors->has('parent_gardians_tel') ? 'has-error' : ''}}">
                    {!! Form::label('parent_gardians_tel', trans('home_content.parent_gardians_tel'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('parent_gardians_tel', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('parent_gardians_tel', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group  {{ $errors->has('parent_gardians_email') ? 'has-error' : ''}}">
                    {!! Form::label('parent_gardians_email', trans('home_content.parent_gardians_email'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::email('parent_gardians_email', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('parent_gardians_email', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="clearfix"></div>
                <br/>
                <div class="f1-buttons">
                    <button type="button" class="btn btn-previous">{{trans('home_content.previous')}}</button>
                    <button type="button" class="btn btn-next">{{trans('home_content.next')}}</button>
                </div>
            </fieldset>

            <fieldset>
                <h4 class="text-center" style="padding: 25px;">{{trans('home_content.special')}} :</h4>

                <div class="form-group text-center {{ $errors->has('profile_attachments') ? 'has-error' : ''}}">
                    {!! Form::label('profile_attachments', trans('home_content.profile_attachments'), ['class' => ' control-label']) !!}

                    <label class="label label-warning "
                           style="color:black;font-size:14px">{{trans('home_content.profile_attachments_info')}}</label>
                    <br/><br/>
                    <div class="">
                        <input type="file" name="profile_attachments" id="profile_attachments"
                               class="inputfile inputfile-2 " accept=".zip,.rar"/>

                        <label for="profile_attachments">
                            <span class="glyphicon glyphicon-compressed"> </span> Select &hellip;
                        </label>
                        <br/>


                        {!! $errors->first('profile_attachments', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="form-group  {{ $errors->has('how_did_you_know_org') ? 'has-error' : ''}}">
                    {!! Form::label('how_did_you_know_org', trans('home_content.how_did_you_know_org'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::textarea('how_did_you_know_org', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('how_did_you_know_org', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="clearfix"></div>
                <br/>
                <div class="f1-buttons">
                    <button type="button" class="btn btn-previous">{{trans('home_content.previous')}}</button>
                    <button type="submit" class="btn btn-primary">{{trans('home_content.finish')}}</button>
                </div>
            </fieldset>
            {!! Form::close() !!}
        </div>
    </div>
    <script>
        function imgaeload(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $('#imgmodel').attr('src', e.target.result);
                }
                reader.readAsDataURL(input.files[0]);
            }
        }

    </script>



@endsection

@section('test')

@endsection



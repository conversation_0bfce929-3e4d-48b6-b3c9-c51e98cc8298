<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

final class JobSeekerProfileSettings extends Model
{
    use HasFactory;

    protected $table = 'jobseeker_profile_settings';

    protected $fillable = [
        'jobseeker_id',
        'bio',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'date_of_birth',
        'gender',
        'linkedin_url',
        'github_url',
        'portfolio_url',
        'skills',
        'experience_years',
        'education_level',
        'preferred_job_types',
        'preferred_locations',
        'salary_expectation_min',
        'salary_expectation_max',
        'currency',
        'availability',
        'remote_work_preference',
        'profile_visibility',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'skills' => 'array',
        'preferred_job_types' => 'array',
        'preferred_locations' => 'array',
        'salary_expectation_min' => 'decimal:2',
        'salary_expectation_max' => 'decimal:2',
        'experience_years' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $hidden = [];

    /**
     * Get the jobseeker that owns the profile settings.
     */
    public function jobSeeker(): BelongsTo
    {
        return $this->belongsTo(JobSeeker::class, 'jobseeker_id');
    }

    /**
     * Get the full address as a formatted string.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get the salary range as a formatted string.
     */
    public function getSalaryRangeAttribute(): ?string
    {
        if (!$this->salary_expectation_min && !$this->salary_expectation_max) {
            return null;
        }

        $currency = $this->currency ?? 'USD';
        
        if ($this->salary_expectation_min && $this->salary_expectation_max) {
            return "{$currency} {$this->salary_expectation_min} - {$this->salary_expectation_max}";
        }

        if ($this->salary_expectation_min) {
            return "{$currency} {$this->salary_expectation_min}+";
        }

        return "Up to {$currency} {$this->salary_expectation_max}";
    }

    /**
     * Check if the profile is complete.
     */
    public function isComplete(): bool
    {
        $requiredFields = [
            'bio',
            'phone',
            'city',
            'country',
            'experience_years',
            'education_level',
            'availability',
        ];

        foreach ($requiredFields as $field) {
            if (empty($this->$field)) {
                return false;
            }
        }

        return !empty($this->skills) && count($this->skills) > 0;
    }

    /**
     * Get the profile completion percentage.
     */
    public function getCompletionPercentage(): int
    {
        $totalFields = [
            'bio',
            'phone',
            'address',
            'city',
            'state',
            'country',
            'postal_code',
            'date_of_birth',
            'gender',
            'linkedin_url',
            'github_url',
            'portfolio_url',
            'skills',
            'experience_years',
            'education_level',
            'preferred_job_types',
            'preferred_locations',
            'salary_expectation_min',
            'availability',
            'remote_work_preference',
        ];

        $completedFields = 0;

        foreach ($totalFields as $field) {
            if (!empty($this->$field)) {
                $completedFields++;
            }
        }

        return (int) round(($completedFields / count($totalFields)) * 100);
    }

    /**
     * Scope to filter by profile visibility.
     */
    public function scopeVisible($query, string $visibility = 'public')
    {
        return $query->where('profile_visibility', $visibility);
    }

    /**
     * Scope to filter by availability.
     */
    public function scopeAvailable($query, string $availability = 'immediate')
    {
        return $query->where('availability', $availability);
    }

    /**
     * Scope to filter by remote work preference.
     */
    public function scopeRemotePreference($query, string $preference)
    {
        return $query->where('remote_work_preference', $preference);
    }
} 
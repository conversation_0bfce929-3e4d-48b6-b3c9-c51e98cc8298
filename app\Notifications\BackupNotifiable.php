<?php

declare(strict_types=1);

namespace App\Notifications;

use <PERSON><PERSON>\Backup\Notifications\Notifiable;

class BackupNotifiable extends Notifiable
{
    /**
     * Get the notification's email address.
     */
    public function routeNotificationForMail(): string
    {
        return config('backup.notifications.mail.to', '<EMAIL>');
    }

    /**
     * Get the notification's Slack webhook URL.
     */
    public function routeNotificationForSlack(): ?string
    {
        return config('backup.notifications.slack.webhook_url');
    }

    /**
     * Get the notification's Discord webhook URL.
     */
    public function routeNotificationForDiscord(): ?string
    {
        return config('backup.notifications.discord.webhook_url');
    }

    /**
     * Get the notification's Telegram chat ID.
     */
    public function routeNotificationForTelegram(): ?string
    {
        return config('backup.notifications.telegram.chat_id');
    }

    /**
     * Determine if the notification should be sent.
     */
    public function shouldSendNotification(): bool
    {
        return config('backup.notifications.enabled', true);
    }

    /**
     * Get the backup notification email address with fallback.
     */
    public function getBackupNotificationEmail(): string
    {
        return $this->routeNotificationForMail();
    }

    /**
     * Get additional notification metadata.
     */
    public function getNotificationMetadata(): array
    {
        return [
            'environment' => config('app.env'),
            'application' => config('app.name'),
            'server' => gethostname(),
            'timestamp' => now()->toIso8601String(),
        ];
    }
} 
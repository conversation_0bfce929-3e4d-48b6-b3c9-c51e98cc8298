<div class="col-md-12">
    

<div class="nav-tabs-custom">
    <ul class="nav nav-tabs">
        @foreach(config('app.locales') as $key => $language)
        <li @if($key == 0 ) class="active" @endif>
            <a aria-expanded="true" data-toggle="tab" href="#{{$language}}">
                {{strtoupper($language)}}
            </a>
        </li>
        @endforeach
    </ul>
    <div class="tab-description">
        @foreach(config('app.locales') as $key => $language)
        <div class="tab-pane clearfix @if($key == 0 ) active @endif" id="{{$language}}">
            <div class="col-md-12">
                <div class="form-group {{ $errors->has($language.'.name') ? 'has-error' : ''}}">
                    {!! Form::label('name', 'Title ['.$language.']', ['class' => 'control-label']) !!}
                
                    {!! Form::text('translate['.$language.'][name]', isset($center) && isset($center->translate($language)->name) ? $center->translate($language)->name : '' , ['class' => 'form-control' , 'placeholder' => 'name']) !!}
                    {!! $errors->first('name', '
                    <p class="help-block">
                        :message
                    </p>
                    ') !!}
                </div>

                <div class="form-group {{ $errors->has($language.'.description') ? 'has-error' : ''}} description" >
                    {!! Form::label('description', 'Content ['.$language.']', ['class' => 'control-label']) !!}
                    {!! Form::textarea('translate['.$language.'][description]',isset($center) && isset($center->translate($language)->description) ? $center->translate($language)->description : '' , ['class' => 'form-control']) !!}
                    {!! $errors->first('description', '
                    <p class="help-block">
                        :message
                    </p>
                    ') !!}
                </div>
            </div>
        </div>
        <!-- /.tab-pane -->
        @endforeach
    </div>
    <!-- /.tab-description -->
</div>
<!-- /.nav-tabs-custom -->
<div class="col-md-12">
    <div class="form-group {{ $errors->has('location') ? 'has-error' : ''}} link">
        {!! Form::label('location', 'Center Location', ['class' => 'control-label']) !!}
        
            {!! Form::text('location', null, ['class' => 'form-control' , 'placeholder' => 'Center Location']) !!}
            {!! $errors->first('location', '
            <p class="help-block">
                :message
            </p>
            ') !!}
    </div>

    <div class="form-group {{ $errors->has('status') ? 'has-error' : ''}}">
        {!! Form::label('status', 'Status', ['class' => 'control-label']) !!}
        
            {!! Form::select('status', [1 => 'Published' , 0 => 'Disabled/Not Published'] , null, ['class' => 'form-control']) !!}
            {!! $errors->first('status', '
            <p class="help-block">
                :message
            </p>
            ') !!}
    </div>
    <div class="form-group">
        <div class="col-md-offset-4" >
            {!! Form::submit(isset($submitButtonText) ? $submitButtonText : 'Create', ['class' => 'btn btn-primary']) !!}
        </div>
    </div>
    </div>
</div>
@section('css')
<link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.6/summernote.css" rel="stylesheet">
@endsection
@section('js')
<script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.6/summernote.js"></script>
<script>
    

    $(document).ready(function() {
        $('textarea').summernote({
            minHeight : 300
        });
    });

</script>
@endsection
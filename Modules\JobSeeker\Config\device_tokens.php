<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Supported Platforms
    |--------------------------------------------------------------------------
    |
    | List of supported platforms for device token registration.
    | This allows for easy extension without modifying the codebase.
    |
    */
    'supported_platforms' => [
        'android',
        'ios',
        'web',
        'macos',
        'windows',
        'linux',
        'chrome',
        'firefox',
        'safari',
        'edge'
    ],

    /*
    |--------------------------------------------------------------------------
    | Token Security Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for device token security and logging.
    |
    */
    'security' => [
        // Maximum length for device tokens
        'max_token_length' => 4096,
        
        // Number of characters to show in token preview
        'preview_length' => 8,
        
        // Length of secure hash for logging (truncated SHA256)
        'hash_length' => 16,
        
        // Enable/disable token encryption at rest
        'encrypt_tokens' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Cleanup Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for cleaning up stale device tokens.
    |
    */
    'cleanup' => [
        // Days after which unused tokens are considered stale
        'stale_after_days' => 90,
        
        // Maximum number of tokens per user
        'max_tokens_per_user' => 10,
    ],
]; 
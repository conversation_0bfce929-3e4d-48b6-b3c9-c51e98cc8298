<?php

namespace App\Listeners;

use App\Student;
use App\Services\StudentImageService;

class AttachStagedStudentPhotos
{
    /**
     * Handle the Student created event.
     *
     * @param  \App\Student  $student
     * @return void
     */
    public function handle(Student $student): void
    {
        $userId    = $student->user_id;
        $studentId = $student->id;

        // Move any staged images into the student's final directory
        app(StudentImageService::class)
            ->moveTemporaryImages($userId, $studentId);
    }
}

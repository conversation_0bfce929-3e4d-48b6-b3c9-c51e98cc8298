<?php

namespace Modules\Site\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NewsUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {


        $ignore = '';

//        if(isset($this->request->get('id'))){
//            $ignore = ','.$this->request->get('id');
//        }


        return [
            'image' => 'required',
            'created_at' => 'required|date',
            'translate.*.title' => 'required|min:3',
            'slug' => 'required|alpha_dash|unique:news,slug,'.$this->request->get('id'),
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}

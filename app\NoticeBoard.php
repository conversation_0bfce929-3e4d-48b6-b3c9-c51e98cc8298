<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;
use App\Role;

class NoticeBoard extends Model
{

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);


    }
    public function users(){
    	return $this->belongsTo('App\User', 'created_by', 'id');
    }

    public static function getRoleName($role_id){

		try {
			$getRoleName = Role::select('name')
								->where('id', $role_id)
								->first();

			if(isset($getRoleName)){
				return $getRoleName;
			}else{
				return false;
			}
		} catch (\Exception $e) {
			return false;
		}
    }
}

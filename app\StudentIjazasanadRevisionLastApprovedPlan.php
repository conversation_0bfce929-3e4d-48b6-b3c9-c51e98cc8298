<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentIjazasanadRevisionLastApprovedPlan extends Model
{
    use HasFactory;
    protected $table = 'student_ijazasanad_revision_last_approved_plans';

    protected $fillable = [
        'student_id',
        'approved_by',
        'plan_year_month_day',
        'from_surat',
        'from_ayat',
        'to_surat',
        'to_ayat',
    ];

    /**
     * Get the student that owns the plan.
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    /**
     * Get the user who approved the plan.
     */
    public function approvedBy()
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }
}

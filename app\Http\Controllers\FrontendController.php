<?php

namespace App\Http\Controllers;

use App\User;
use App\Exam;
use App\News;
use App\Classeses;
use App\Event;
use App\Employee;
use App\Course;
use App\Organization;
use App\Section;
use App\Student;
use App\Subject;
use App\Visitor;
use App\tableList;
use App\ExamType;
use App\NewsPage;
use App\AboutPage;
use App\CoursePage;
use App\CustomLink;
use App\ApiBaseMethod;
use App\ContactPage;
use App\NoticeBoard;
use App\Testimonial;
use App\ContactMessage;
use App\GeneralSettings;
use App\HomePageSetting;
use App\SocialMediaIcon;
use Illuminate\Http\Request;
use App\FrontendPersmission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Redirect;

class FrontendController extends Controller
{

    public function __construct()
    {
//        $this->middleware('PM');
        // User::checkAuth();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        try {
            if (Schema::hasTable('users')) {
                $testInstalled = DB::table('users')->get();
                if (count($testInstalled) < 1) {
                    return view('install.welcome_to_infix');
                } else {
                    $exams = Exam::all();
                    $news = News::orderBy('order', 'asc')->limit(3)->get();
                    $testimonial = Testimonial::all();
                    $academics = Course::orderBy('id', 'asc')->limit(3)->get();
                    $exams_types = ExamType::all();
                    $events = Event::all();
                    $a = 2;
                    $b = 3;
                    $c = 9;
                    $notice_board = NoticeBoard::where('is_published', 1)->orderBy('created_at', 'DESC')->take(3)->get();
                    $classes = Classeses::get();
                    $subjects = Subject::get();
                    $sections = Section::get();
                    $links = CustomLink::find(1);
                    $homePage = HomePageSetting::find(1);
                    $permisions = FrontendPersmission::where([['guardian_id', 1], ['is_published', 1]])->get();
                    $per = [];
                    foreach ($permisions as $permision) {
                        $per[$permision->name] = 1;
                    }
                    $button_settings = GeneralSettings::find(1);

                    $url = explode('/', $button_settings->website_url);
                    if ($button_settings->website_btn == 0) {
                        return redirect('login');
                    } else {

                        if ($button_settings->website_url == '') {

                            if (GeneralSettings::isModule('Saas') == TRUE) {

                                $contact_info = ContactPage::first();
                                if (GeneralSettings::isModule('SaasSubscription') == TRUE) {

                                    $packages = \Modules\SaasSubscription\Entities\PackagePlan::get();
                                } else {
                                    $packages = [];
                                }

                                return view('saas::auth.saas_landing', compact('contact_info', 'packages'));
                            } else {
                                return view('frontEnd.home.light_home', compact('exams', 'classes', 'subjects', 'exams_types', 'sections', 'news', 'testimonial', 'notice_board', 'events', 'academics', 'links', 'homePage', 'per'));
                            }
                        } elseif ($url[max(array_keys($url))] == 'home') {

                            if (GeneralSettings::isModule('Saas') == TRUE) {
                                $contact_info = ContactPage::first();
                                return view('saas::auth.saas_landing', compact('contact_info'));
                            } else {
                                return view('frontEnd.home.light_home', compact('exams', 'classes', 'subjects', 'exams_types', 'sections', 'news', 'testimonial', 'notice_board', 'events', 'academics', 'links', 'homePage', 'per'));
                            }
                        } else {
                            $url = $button_settings->website_url;
                            return Redirect::to($url);
                        }
                    }
                }
            } else {
                return view('install.welcome_to_infix');
            }
        } catch (\Exception $e) {
            // dd($e);
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function about()
    {
        try {
            $exams = Exam::all();
            $exams_types = ExamType::all();
            $classes = Classeses::get();
            $subjects = Subject::get();
            $sections = Section::get();
            $about = AboutPage::first();
            $testimonial = Testimonial::all();
            $totalStudents = Student::get();
            $totalTeachers = Employee::where('role_id', 3 /** teacher **/)->get();
            $history = News::where('category_id', 2)->limit(3)->get();
            $mission = News::where('category_id', 3)->limit(3)->get();
            return view('frontEnd.home.light_about', compact('exams', 'classes', 'subjects', 'exams_types', 'sections', 'about', 'testimonial', 'totalStudents', 'totalTeachers', 'history', 'mission'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function news()
    {

        try {
            $exams = Exam::all();
            $exams_types = ExamType::all();
            $classes = Classeses::get();
            $subjects = Subject::get();
            $sections = Section::get();
            return view('frontEnd.home.light_news', compact('exams', 'classes', 'subjects', 'exams_types', 'sections'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function contact()
    {

        try {
            $exams = Exam::all();
            $exams_types = ExamType::all();
            $classes = Classeses::get();
            $subjects = Subject::get();
            $sections = Section::get();

            $contact_info = ContactPage::first();
            return view('frontEnd.home.light_contact', compact('exams', 'classes', 'subjects', 'exams_types', 'sections', 'contact_info'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function institutionPrivacyPolicy()
    {

        try {
            $exams = Exam::all();
            $exams_types = ExamType::all();
            $classes = Classeses::get();
            $subjects = Subject::get();
            $sections = Section::get();

            $contact_info = ContactPage::first();
            return view('frontEnd.home.institutionPrivacyPolicy', compact('exams', 'classes', 'subjects', 'exams_types', 'sections', 'contact_info'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    
    public function institutionTermServices()
    {

        try {
            $exams = Exam::all();
            $exams_types = ExamType::all();
            $classes = Classes::get();
            $subjects = Subject::get();
            $sections = Section::get();

            $contact_info = ContactPage::first();
            return view('frontEnd.home.institutionTermServices', compact('exams', 'classes', 'subjects', 'exams_types', 'sections', 'contact_info'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function newsDetails($id)
    {

        try {
            $news = News::find($id);
            $otherNews = News::orderBy('id', 'asc')->whereNotIn('id', [$id])->limit(3)->get();
            $a = 2;
            $b = 3;
            $c = 9;
            $notice_board = NoticeBoard::where('is_published', 1)->orderBy('created_at', 'DESC')->take(3)->get();
            return view('frontEnd.home.light_news_details', compact('news', 'notice_board', 'otherNews'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function newsPage()
    {

        try {
            $news = News::all();
            $newsPage = NewsPage::first();
            return view('frontEnd.home.light_news', compact('news', 'newsPage'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function conpactPage()
    {

        try {
            $contact_us = ContactPage::first();
            return view('frontEnd.contact_us', compact('contact_us'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function contactPageEdit()
    {

        try {
            $contact_us = ContactPage::first();
            $update = "";

            return view('frontEnd.contact_us', compact('contact_us', 'update'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function contactPageStore(Request $request)
    {

        if ($request->file('image') == "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
                'address' => 'required',
                'phone' => 'required',
                'email' => 'required',
                'latitude' => 'required',
                'longitude' => 'required',
                'google_map_address' => 'required',
            ]);
        } else {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
                'address' => 'required',
                'phone' => 'required',
                'email' => 'required',
                'latitude' => 'required',
                'longitude' => 'required',
                'google_map_address' => 'required',
                'image' => 'dimensions:min_width=1420,min_height=450',
            ]);
        }

        try {
            $fileName = "";
            if ($request->file('image') != "") {
                $contact = ContactPage::find(1);
                if ($contact != "") {
                    if ($contact->image != "") {
                        if (file_exists($contact->image)) {
                            unlink($contact->image);
                        }
                    }
                }

                $file = $request->file('image');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/contactPage/', $fileName);
                $fileName = 'public/uploads/contactPage/' . $fileName;
            }

            $contact = ContactPage::first();
            if ($contact == "") {
                $contact = new ContactPage();
            }
            $contact->title = $request->title;
            $contact->description = $request->description;
            $contact->button_text = $request->button_text;
            $contact->button_url = $request->button_url;

            $contact->address = $request->address;
            $contact->address_text = $request->address_text;
            $contact->phone = $request->phone;
            $contact->phone_text = $request->phone_text;
            $contact->email = $request->email;
            $contact->email_text = $request->email_text;
            $contact->latitude = $request->latitude;
            $contact->longitude = $request->longitude;
            $contact->google_map_address = $request->google_map_address;
            if ($fileName != "") {
                $contact->image = $fileName;
            }

            $result = $contact->save();

            if ($result) {
                Toastr::success('Operation successful', 'Success');
                return redirect('contact-page');
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function aboutPage()
    {

        try {
            $about_us = AboutPage::first();
            return view('frontEnd.about_us', compact('about_us'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function aboutPageEdit()
    {

        try {
            $about_us = AboutPage::first();
            $update = "";

            return view('frontEnd.about_us', compact('about_us', 'update'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function newsHeading()
    {

        try {
            $SmNewsPage = NewsPage::first();
            $update = "";

            return view('modules.site.templates.wajeha.backEnd.news.newsHeadingUpdate', compact('NewsPage', 'update'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function newsHeadingUpdate(Request $request)
    {

        if ($request->file('image') == "" && $request->file('main_image') == "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'main_title' => 'required',
                'main_description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
            ]);
        } elseif ($request->file('image') != "" && $request->file('main_image') != "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'main_title' => 'required',
                'main_description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
                'image' => 'dimensions:min_width=1420,min_height=450',
                'main_image' => 'dimensions:min_width=1420,min_height=450',
            ]);
        } elseif ($request->file('image') != "" && $request->file('main_image') == "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'main_title' => 'required',
                'main_description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
                'image' => 'dimensions:min_width=1420,min_height=450',
            ]);
        } elseif ($request->file('image') == "" && $request->file('main_image') != "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'main_title' => 'required',
                'main_description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
                'main_image' => 'dimensions:min_width=1420,min_height=450',
            ]);
        }

        try {
            $fileName = "";
            if ($request->file('image') != "") {
                $about = NewsPage::find(1);
                if ($about != "") {
                    if ($about->image != "") {
                        if (file_exists($about->image)) {
                            unlink($about->image);
                        }
                    }
                }

                $file = $request->file('image');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/about_page/', $fileName);
                $fileName = 'public/uploads/about_page/' . $fileName;
            }

            $mainfileName = "";
            if ($request->file('main_image') != "") {
                $about = NewsPage::find(1);
                if ($about != "") {
                    if ($about->main_image != "") {
                        if (file_exists($about->main_image)) {
                            unlink($about->main_image);
                        }
                    }
                }

                $file = $request->file('main_image');
                $mainfileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/about_page/', $mainfileName);
                $mainfileName = 'public/uploads/about_page/' . $mainfileName;
            }

            $about = NewsPage::first();
            if ($about == "") {
                $about = new NewsPage();
            }
            $about->title = $request->title;
            $about->description = $request->description;
            $about->main_title = $request->main_title;
            $about->main_description = $request->main_description;
            $about->button_text = $request->button_text;
            $about->button_url = $request->button_url;
            if ($fileName != "") {
                $about->image = $fileName;
            }
            if ($mainfileName != "") {
                $about->main_image = $mainfileName;
            }
            $result = $about->save();

            if ($result) {
                Toastr::success('Operation successful', 'Success');
                return redirect('news-heading-update');
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect('news-heading-update');
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    // end heading update

    public function courseHeading()
    {

        try {
            $SmCoursePage = CoursePage::first();
            $update = "";

            return view('modules.site.templates.wajeha.backEnd.course.courseHeadingUpdate', compact('CoursePage', 'update'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function courseHeadingUpdate(Request $request)
    {

        if ($request->file('image') == "" && $request->file('main_image') == "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'main_title' => 'required',
                'main_description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
            ]);
        } elseif ($request->file('image') != "" && $request->file('main_image') != "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'main_title' => 'required',
                'main_description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
                'image' => 'dimensions:min_width=1420,min_height=450',
                'main_image' => 'dimensions:min_width=1420,min_height=450',
            ]);
        } elseif ($request->file('image') != "" && $request->file('main_image') == "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'main_title' => 'required',
                'main_description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
                'image' => 'dimensions:min_width=1420,min_height=450',
            ]);
        } elseif ($request->file('image') == "" && $request->file('main_image') != "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'main_title' => 'required',
                'main_description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
                'main_image' => 'dimensions:min_width=1420,min_height=450',
            ]);
        }

        try {
            $fileName = "";
            if ($request->file('image') != "") {
                $about = CoursePage::find(1);
                if ($about != "") {
                    if ($about->image != "") {
                        if (file_exists($about->image)) {
                            unlink($about->image);
                        }
                    }
                }

                $file = $request->file('image');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/about_page/', $fileName);
                $fileName = 'public/uploads/about_page/' . $fileName;
            }

            $mainfileName = "";
            if ($request->file('main_image') != "") {
                $about = CoursePage::find(1);
                if ($about != "") {
                    if ($about->main_image != "") {
                        if (file_exists($about->main_image)) {
                            unlink($about->main_image);
                        }
                    }
                }

                $file = $request->file('main_image');
                $mainfileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/about_page/', $mainfileName);
                $mainfileName = 'public/uploads/about_page/' . $mainfileName;
            }

            $about = CoursePage::first();
            if ($about == "") {
                $about = new CoursePage();
            }
            $about->title = $request->title;
            $about->description = $request->description;
            $about->main_title = $request->main_title;
            $about->main_description = $request->main_description;
            $about->button_text = $request->button_text;
            $about->button_url = $request->button_url;
            if ($fileName != "") {
                $about->image = $fileName;
            }
            if ($mainfileName != "") {
                $about->main_image = $mainfileName;
            }
            $result = $about->save();

            if ($result) {
                Toastr::success('Operation successful', 'Success');
                return redirect('course-heading-update');
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect('course-heading-update');
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function aboutPageStore(Request $request)
    {

        if ($request->file('image') == "" && $request->file('main_image') == "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'main_title' => 'required',
                'main_description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
            ]);
        } elseif ($request->file('image') != "" && $request->file('main_image') != "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'main_title' => 'required',
                'main_description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
                'image' => 'dimensions:min_width=1420,min_height=450',
                'main_image' => 'dimensions:min_width=1420,min_height=450',
            ]);
        } elseif ($request->file('image') != "" && $request->file('main_image') == "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'main_title' => 'required',
                'main_description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
                'image' => 'dimensions:min_width=1420,min_height=450',
            ]);
        } elseif ($request->file('image') == "" && $request->file('main_image') != "") {
            $this->validate($request,[
                'title' => 'required',
                'description' => 'required',
                'main_title' => 'required',
                'main_description' => 'required',
                'button_text' => 'required',
                'button_url' => 'required',
                'main_image' => 'dimensions:min_width=1420,min_height=450',
            ]);
        }

        try {
            $fileName = "";
            if ($request->file('image') != "") {
                $about = AboutPage::find(1);
                if ($about != "") {
                    if ($about->image != "") {
                        if (file_exists($about->image)) {
                            unlink($about->image);
                        }
                    }
                }

                $file = $request->file('image');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/about_page/', $fileName);
                $fileName = 'public/uploads/about_page/' . $fileName;
            }

            $mainfileName = "";
            if ($request->file('main_image') != "") {
                $about = AboutPage::find(1);
                if ($about != "") {
                    if ($about->main_image != "") {
                        if (file_exists($about->main_image)) {
                            unlink($about->main_image);
                        }
                    }
                }

                $file = $request->file('main_image');
                $mainfileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/about_page/', $mainfileName);
                $mainfileName = 'public/uploads/about_page/' . $mainfileName;
            }

            $about = AboutPage::first();
            if ($about == "") {
                $about = new AboutPage();
            }
            $about->title = $request->title;
            $about->description = $request->description;
            $about->main_title = $request->main_title;
            $about->main_description = $request->main_description;
            $about->button_text = $request->button_text;
            $about->button_url = $request->button_url;
            if ($fileName != "") {
                $about->image = $fileName;
            }
            if ($mainfileName != "") {
                $about->main_image = $mainfileName;
            }
            $result = $about->save();

            if ($result) {
                Toastr::success('Operation successful', 'Success');
                return redirect('about-page');
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function sendMessage(Request $request)
    {

        $this->validate($request,[
            'name' => 'required',
            'email' => 'required',
            'subject' => 'required',
            'message' => 'required',
        ]);

        $data['name'] = $request->name;
        $data['email'] = $request->email;
        $data['subject'] = $request->subject;
        $data['message'] = $request->message;

        DB::beginTransaction();
        try {
            $contact_message = new ContactMessage();
            $contact_message->name = $request->name;
            $contact_message->email = $request->email;
            $contact_message->subject = $request->subject;
            $contact_message->message = $request->message;
            $result = $contact_message->save();

            Mail::send('frontEnd.contact_mail', compact('data'), function ($message) use ($request) {

                $setting = GeneralSettings::find(1);
                $email = $setting->email;
                $organization_name = $setting->organization_name;
                $message->to($email, $organization_name)->subject($request->subject);
                $message->from($email, $organization_name);
            });

            DB::commit();
            if ($result) {
                Toastr::success('Operation successful', 'Success');
                return redirect()->back()->with('message-success', 'Message send successfully');
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back()->with('message-danger', 'Something went wrong, please try again');
            }
            // Toastr::success('Operation successful', 'Success');
            // return redirect()->back();
        } catch (\Exception $e) {
            DB::rollback();
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back()->with('message-danger', 'Something went wrong, please try again');
        }
    }

    public function contactMessage(Request $request)
    {

        try {
            $contact_messages = ContactMessage::orderBy('id', 'desc')->get();

            return view('frontEnd.contact_message', compact('contact_messages'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    //user register method start
    public function register()
    {

        try {
            $organizations = Organization::where('id', config('organization_id'))->get();


            return view('auth.registerItqan', compact('organizations'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function customer_register(Request $request)
    {


        $this->validate($request,[
            'fullname' => 'required|min:3|max:100',
            'email' => 'required|email',
            'password' => 'required|min:6',
            'password_confirmation' => 'required_with:password|same:password|min:6',
        ]);

        try {
            //insert data into user table
            $s = new User();
            $s->role_id = 4;
            $s->full_name = $request->fullname;
            $s->username = $request->email;
            $s->email = $request->email;
            $s->active_status = 0;
            $s->access_status = 0;
            $s->password = Hash::make($request->password);
            $s->save();
            $result = $s->toArray();
            $last_id = $s->id; //last id of user table

            //insert data into staff table
            $st = new Employee();
            $st->organization_id = 1;
            $st->user_id = $last_id;
            $st->role_id = 4;
            $st->first_name = $request->fullname;
            $st->full_name = $request->fullname;
            $st->last_name = '';
            $st->staff_no = 10;
            $st->email = $request->email;
            $st->active_status = 0;
            $st->save();

            $result = $st->toArray();
            if (!empty($result)) {
                Toastr::success('Operation successful', 'Success');
                return redirect('login');
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            Toastr::error('Operation Failed,' . $e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }

    public function course()
    {

        try {
            $exams = Exam::all();
            $course = Course::all();
            $news = News::orderBy('order', 'asc')->limit(4)->get();
            $exams_types = ExamType::all();
            $coursePage = CoursePage::first();
            $classes = Classes::get();
            $subjects = Subject::get();
            $sections = Section::get();
            return view('frontEnd.home.light_course', compact('exams', 'classes', 'coursePage', 'subjects', 'exams_types', 'sections', 'course', 'news'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function courseDetails($id)
    {

        try {
            $course = Course::find($id);
            $courses = Course::orderBy('id', 'asc')->whereNotIn('id', [$id])->limit(3)->get();
            return view('frontEnd.home.light_course_details', compact('course', 'courses'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function socialMedia()
    {
        $visitors = SocialMediaIcon::all();

        return view('frontEnd.socialMedia', compact('visitors'));
    }

    public function socialMediaStore(Request $request)
    {

        $this->validate($request,[
            'url' => 'required',
            'icon' => 'required',
            // 'icon' => 'required|dimensions:min_width=24,max_width=24',
            'status' => 'required',
        ]);

        try {

            // $fileName = "";
            // if ($request->file('icon') != "") {
            //     $file = $request->file('icon');
            //     $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            //     $file->move('public/uploads/socialIcon/', $fileName);
            //     $fileName = 'public/uploads/socialIcon/' . $fileName;
            // }

            $visitor = new SocialMediaIcon();
            $visitor->url = $request->url;
            $visitor->icon = $request->icon;
            $visitor->status = $request->status;
            $result = $visitor->save();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($result) {

                    return ApiBaseMethod::sendResponse(null, 'Created successfully.');
                }
                return ApiBaseMethod::sendError('Something went wrong, please try again.');
            } else {
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                }
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function socialMediaEdit($id)
    {
        $visitors = SocialMediaIcon::all();
        $visitor = SocialMediaIcon::find($id);

        return view('frontEnd.socialMedia', compact('visitors', 'visitor'));
    }


    public function socialMediaUpdate(Request $request)
    {

        $this->validate($request,[
            'url' => 'required',
            'icon' => 'required',
            // 'icon' => 'dimensions:min_width=24,max_width=24',
            'status' => 'required',
        ]);

        try {

            // $fileName = "";
            // if ($request->file('icon') != "") {

            //     $visitor = SocialMediaIcon::find($request->id);
            //     if ($visitor->icon != "") {
            //         if (file_exists($visitor->icon)) {
            //             unlink($visitor->icon);
            //         }
            //     }


            //     $file = $request->file('icon');
            //     $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            //     $file->move('public/uploads/socialIcon/', $fileName);
            //     $fileName = 'public/uploads/socialIcon/' . $fileName;

            // }

            $visitor = SocialMediaIcon::find($request->id);
            $visitor->url = $request->url;
            $visitor->icon = $request->icon;
            $visitor->status = $request->status;
            $result = $visitor->save();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($result) {

                    return ApiBaseMethod::sendResponse(null, 'Updated successfully.');
                }
                return ApiBaseMethod::sendError('Something went wrong, please try again.');
            } else {
                if ($result) {

                    Toastr::success('Operation successful', 'Success');
                    return redirect('social-media');
                }
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function socialMediaDelete(Request $request, $id)
    {

        try {
            $visitor = SocialMediaIcon::find($id);
            // if ($visitor->icon != "") {
            //     if (file_exists($visitor->icon)) {
            //         unlink($visitor->icon);
            //     }
            // }
            $result = $visitor->delete();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($result) {
                    return ApiBaseMethod::sendResponse(null, 'Deleted successfully.');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
                }
            } else {
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect('social-media');
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}
<?php

namespace Modules\Admission\Http\Controllers;

use App\Attendance;
use App\Role;
use App\Employee;

use Carbon\Carbon;
use DB;
use App\Permission;
use App\EmployeeSalary;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\EmployeeTimetable;
use App\Http\Controllers\Controller;


class StudentsDatatablesController extends Controller
{

    public function getAttendanceReport(Request $request,$id)
    {

        if ($request->ajax()) {


//            if ($request->has('months') or $request->has('year') or $request->has('days')) {
//
//                if ($months = $request->months and isset($request->months)) {
//                    $arr = join(",", $request->months);
//                    $bindings['roles'] = $months;
//                    $requestedMonths = " AND month(clock) IN (" . $arr . ")";
//
//                    $monthCondition = " AND month(clock) =  " . "'" . $requestedMonths . "'";
//
//                }
//
//                if ($days = $request->days and isset($request->days)) {
//
//                    $arr = join(",", $request->days);
//                    $DaysCondition = " AND day(clock) IN (" . $arr . ")";
//                    $bindings['days'] = $days;
//
//
//                }
//
//
//                if ($year = $request->year and isset($request->year)) {
//                    $bindings['gender'] = $year;
//                    $yearCondition = " AND year(clock) = " . "'" . $request->year . "'";
//                }
//
//
//                $trxDatatables = DB::select(
//                    'SELECT * from attendances where organization_id = 148 and employee_id = 48
//                                            ' . $yearCondition . '
//                                            ' . $DaysCondition . '
//                                            ' . $monthCondition);
//
//
//                return \Yajra\DataTables\DataTables::of($trxDatatables)
//                    ->addIndexColumn()
//                    ->make(true);
//
//
//            }
//
//            else {

                $trxDatatables = Attendance::where("employee_id",$id)->get();






                return \Yajra\DataTables\DataTables::of($trxDatatables)
                    ->addIndexColumn()
                    ->toJson();
//            }

        }


    }

}
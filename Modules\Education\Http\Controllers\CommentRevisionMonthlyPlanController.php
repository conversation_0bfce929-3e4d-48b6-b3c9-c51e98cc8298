<?php

namespace Modules\Education\Http\Controllers;

use App\Attendance;
use App\AttendanceOption;
use App\Cen_Emp;
use App\Student;
use App\StudentHefzPlan;
use App\StudentRevisionPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\EducationalReports\Http\Requests\CreateHefzPlanRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentAttendance;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class CommentRevisionMonthlyPlanController extends Controller
{

    public function update(Request $request)
    {

        $plan = StudentRevisionPlan::where('id', $request->get('id'))->update([
            'supervisor_comment' => $request->get('comment')
        ]);
        return response()->json(['message' => 'comment added to the plan']);


    }


    public function show(Request $request,$planId)
    {




        $plan = StudentRevisionPlan::where('id', $planId)->update([
            'supervisor_comment' => $request->get('comment')
        ]);
        return response()->json(['message' => 'comment added to the plan']);


    }
}

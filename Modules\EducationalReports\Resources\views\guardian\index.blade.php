@extends('layouts.hound')

@section('mytitle', 'Guardians')

@section('content')
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.1.0/jquery.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
        <h3>{{ trans('units.guardians') }}</h3>
    <div class="panel panel-default">
        <div class="panel-body">

            <a href="{{ route('admission.guardians.create') }}" class="btn btn-primary pull-right" title="Add New Guardian"><span class="glyphicon glyphicon-plus" aria-hidden="true"/> Add New Guardian</a>
            <br/>
            <br/>
            <br/>
            <div class="form-group">
      <input type="text" name="search" id="search" class="form-control" placeholder="Search For Guardians" />
     </div>
            <div class="">
      
                <table class="table table-borderless table-responsive">
                    <thead>
                        <tr>
                           
                            <th> Name </th>
                            <th>Number of Students</th>
                            <th>Mobile Number</th>
                            <th>Email</th>
                            <th>Nationality</th>
                          
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    @foreach($guardians as $item)
                        <tr>
                           
                            <td>{{ $item->full_name }}</td>
                            <td>{{ $item->students->count() }}</td>
                            <td>{{ $item->mobile }}</td>
                            <td>{{ $item->email }}</td>
                            <td>{{ $item->nationality }}</td>

                            
                            
                            <td>
                                <a href="{{ route('admission.guardians.show', $item->id) }}" class="btn btn-success btn-xs" title="View Center"><span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                                <a href="{{ route('admission.guardians.edit',$item->id) }}" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>
                                {!! Form::open([
                                    'method'=>'DELETE',
                                    'route' => ['admission.guardians.destroy', $item->id],
                                    'style' => 'display:inline'
                                ]) !!}
                                    {!! Form::button('<span class="glyphicon glyphicon-trash" aria-hidden="true" title="Delete Center" />', array(
                                            'type' => 'submit',
                                            'class' => 'btn btn-danger btn-xs',
                                            'title' => 'Delete Center',
                                            'onclick'=>'return confirm("Confirm delete?")'
                                    )) !!}
                                {!! Form::close() !!}
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>

        </div>
    </div>
           
    <script>
$(document).ready(function(){


 function fetch_customer_data(query = '')
 {
  $.ajax({
   url:"{{ route('guardian.live_search') }}",
   method:'GET',
   data:{query:query},
   dataType:'json',
   success:function(data)
   {
    $('tbody').html(data.table_data);
    $('#total_records').text(data.total_data);
   }
  })
 }

 $(document).on('keyup', '#search', function(){
  var query = $(this).val();
  fetch_customer_data(query);
 });
});
</script>
@endsection
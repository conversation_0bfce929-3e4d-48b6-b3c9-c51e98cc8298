<?php

namespace App;

use App;
use Modules\Jobs\Traits\Lang;
use Modules\Jobs\Traits\IsDefault;
use Modules\Jobs\Traits\Active;
use Modules\Jobs\Traits\Sorted;
use Modules\Jobs\Traits\CountryStateCity;
use App\Helpers\MiscHelper;
use App\Helpers\DataArrayHelper;
use App\Country;
use Illuminate\Database\Eloquent\Model;

class Alert extends Model
{

    use Lang;
    use IsDefault;
    use Active;
    use Sorted;

    protected $table = 'job_alerts';
    public $timestamps = true;
    protected $guarded = ['id'];
    //protected $dateFormat = 'U';
    protected $casts = ['created_at', 'updated_at'];

    public function get_country($id)
    {
        return $country = Country::where('id', $id)->first();
    }
}

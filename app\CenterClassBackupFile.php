<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class CenterClassBackupFile extends Model
{
    protected $fillable = [
        'backup_id',
        'class_id',
        'file_name',
        'file_path',
        'file_size',
        'file_exists',
        'file_type'
    ];

    public function backup()
    {
        return $this->belongsTo(CenterClassStudentsBackup::class, 'backup_id');
    }

    public function class()
    {
        return $this->belongsTo(Classes::class, 'class_id');
    }
} 
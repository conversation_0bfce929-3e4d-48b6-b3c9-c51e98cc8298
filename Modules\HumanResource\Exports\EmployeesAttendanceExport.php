<?php
// EXPORT CLASS FOR EXCEL: Modules/HumanResource/Exports/EmployeesAttendanceExport.php

namespace Modules\HumanResource\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;

class EmployeesAttendanceExport implements FromCollection, WithMapping, WithHeadings
{
    protected $data;
    protected $requester;
    protected $month;
    protected $year;
    protected $employeeName;

    /**
     * @param array  $attendanceData
     * @param string $requester
     * @param string $month
     * @param string $year
     * @param string $employeeName
     */
    public function __construct($attendanceData, $requester, $month, $year, $employeeName)
    {
        $this->data         = $attendanceData;
        $this->requester    = $requester;
        $this->month        = $month;
        $this->year         = $year;
        $this->employeeName = $employeeName;
    }

    public function collection()
    {
        $rows = [];
        foreach ($this->data as $employeeId => $group) {
            foreach ($group['details'] as $detail) {
                $rows[] = [
                    'employee_id'       => $group['employee']['id'],
                    'employee_name'     => $group['employee']['full_name'],
                    'formatted_date'    => $detail['formatted_date'],
                    'day_name'          => $detail['day_name'],
                    'status'            => $detail['status'],
                    'in_time'           => $detail['in_time'],
                    'out_time'          => $detail['out_time'],
                    'hours_worked'      => $detail['hours_worked'],
                    'volunteer_hours'   => $detail['volunteer_hours'],
                    'salary_percentage' => $detail['salary_percentage'],
                ];
            }
        }
        return collect($rows);
    }

    public function map($row): array
    {
        return [
            $row['employee_id'],
            $row['employee_name'],
            $row['formatted_date'],
            $row['day_name'],
            $row['status'],
            $row['in_time'],
            $row['out_time'],
            $row['hours_worked'],
            $row['volunteer_hours'],
            $row['salary_percentage'],
        ];
    }

    /**
     * Headings, including a "Requested by" note at the top.
     * We'll place the note in the first rows, then a blank row, then real headings.
     */
    public function headings(): array
    {
        return [
            ["Attendance Report for {$this->employeeName} ({$this->month}/{$this->year})"],
            ["This report was requested by {$this->requester}. Thank you!"],
            [],
            [
                'Employee ID',
                'Employee Name',
                'Date',
                'Day',
                'Status',
                'In Time',
                'Out Time',
                'Hours Worked',
                'Volunteer Hours',
                'Salary % Achieved'
            ],
        ];
    }

    /**
     * Insert images for logo, letterhead, footer via WithDrawings.
     */
    public function drawings()
    {
//        // Paths
//        $logoPath         = public_path('uploads/settings/logo.png');
//        $letterHeadPath   = public_path('uploads/settings/EducationDivisionLetterheadersEDU.jpg');
//        $letterFooterPath = public_path('uploads/settings/footer.png');
//
//        // We'll place them in different cells. Adjust row coordinates as needed.
//        // For instance:
//        $drawings = [];
//
//        // 1) Letter Head
//        if (file_exists($letterHeadPath)) {
//            $drawingHead = new Drawing();
//            $drawingHead->setName('LetterHead');
//            $drawingHead->setPath($letterHeadPath);
//            $drawingHead->setHeight(70); // adjust as needed
//            $drawingHead->setCoordinates('A1');
//            $drawings[] = $drawingHead;
//        }
//
//        // 2) Logo
//        if (file_exists($logoPath)) {
//            $drawingLogo = new Drawing();
//            $drawingLogo->setName('Logo');
//            $drawingLogo->setPath($logoPath);
//            $drawingLogo->setHeight(60);
//            $drawingLogo->setCoordinates('E1'); // place somewhere else, e.g. E1
//            $drawings[] = $drawingLogo;
//        }
//
//        // 3) Footer
//        // Possibly place it near the bottom of the sheet.
//        // The row depends on how big your data set might be,
//        // you can place it at row 40 or 50 or so. Adjust accordingly.
//        if (file_exists($letterFooterPath)) {
//            $drawingFooter = new Drawing();
//            $drawingFooter->setName('Footer');
//            $drawingFooter->setPath($letterFooterPath);
//            $drawingFooter->setHeight(40);
//            $drawingFooter->setCoordinates('A40');
//            $drawings[] = $drawingFooter;
//        }
//
//        return $drawings;
    }
}

<?php

namespace Database\Factories;

use App\Student;
use App\User; // Import User if student needs a related user record
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class StudentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Student::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        // Create a corresponding User record first if student relies on it
        $user = User::factory()->create();

        return [
            'full_name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'password' => bcrypt('password'), // default password
            'organization_id' => 1, // Assuming default org ID
            'status' => 'active',
            'user_id' => $user->id, // Link to the created user
            // Add other essential fields based on your Student model constraints
            'student_number' => $this->faker->unique()->randomNumber(),
            'gender' => $this->faker->randomElement(['Male', 'Female']),
            'date_of_birth' => $this->faker->date(),
            'mobile' => $this->faker->phoneNumber(),
        ];
    }
} 
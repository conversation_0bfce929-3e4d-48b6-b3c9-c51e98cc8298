<?php

declare(strict_types=1);

namespace App\Services;

use Mo<PERSON>les\JobSeeker\Entities\ProviderCircuitBreakerState;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Database-Persistent Circuit Breaker Service
 * 
 * Implements the Circuit Breaker pattern for email providers with database persistence.
 * This ensures circuit breaker state survives application restarts and is shared
 * across multiple application instances.
 * 
 * Based on <PERSON>'s Circuit Breaker pattern:
 * https://martinfowler.com/bliki/CircuitBreaker.html
 */
final class CircuitBreakerService
{
    /**
     * Check if a provider's circuit breaker is tripped (open)
     * 
     * @param string $providerKey The email provider key (gmail, mailtrap, mail)
     * @return bool True if circuit is tripped (requests should be blocked)
     */
    public function isTripped(string $providerKey): bool
    {
        try {
            $circuitState = ProviderCircuitBreakerState::getOrCreateForProvider($providerKey);
            
            $isTripped = $circuitState->isTripped();
            
            Log::channel('email')->debug('Circuit breaker status check', [
                'provider' => $providerKey,
                'status' => $circuitState->status,
                'is_tripped' => $isTripped,
                'failure_count' => $circuitState->failure_count,
            ]);
            
            return $isTripped;
        } catch (Exception $e) {
            // If we can't check the circuit breaker state, assume it's closed (allow requests)
            // This provides graceful degradation if the database is unavailable
            Log::channel('email')->error('Circuit breaker: Failed to check state, assuming CLOSED', [
                'provider' => $providerKey,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Record a successful operation for a provider
     * 
     * @param string $providerKey The email provider key
     */
    public function recordSuccess(string $providerKey): void
    {
        try {
            $circuitState = ProviderCircuitBreakerState::getOrCreateForProvider($providerKey);
            $circuitState->recordSuccess();
            
            Log::channel('email')->debug('Circuit breaker: Success recorded', [
                'provider' => $providerKey,
                'new_status' => $circuitState->fresh()->status,
            ]);
        } catch (Exception $e) {
            // Log the error but don't throw - we don't want circuit breaker issues
            // to prevent successful email operations from completing
            Log::channel('email')->error('Circuit breaker: Failed to record success', [
                'provider' => $providerKey,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Record a failed operation for a provider
     * 
     * @param string $providerKey The email provider key
     * @param string|null $errorMessage Optional error message for context
     */
    public function recordFailure(string $providerKey, ?string $errorMessage = null): void
    {
        try {
            $circuitState = ProviderCircuitBreakerState::getOrCreateForProvider($providerKey);
            $circuitState->recordFailure();
            
            Log::channel('email')->warning('Circuit breaker: Failure recorded', [
                'provider' => $providerKey,
                'new_status' => $circuitState->fresh()->status,
                'failure_count' => $circuitState->fresh()->failure_count,
                'error_message' => $errorMessage,
            ]);
        } catch (Exception $e) {
            // Log the error - this is more critical than recordSuccess since we want
            // to know if we're failing to track failures
            Log::channel('email')->error('Circuit breaker: Failed to record failure', [
                'provider' => $providerKey,
                'original_error' => $errorMessage,
                'circuit_breaker_error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get all circuit breaker states indexed by provider key
     * 
     * @return array All circuit breaker states indexed by provider key
     */
    public function getAllStates(): array
    {
        try {
            $states = ProviderCircuitBreakerState::all();
            
            return $states->mapWithKeys(function (ProviderCircuitBreakerState $state) {
                return [$state->provider_key => $state->getStats()];


            })->toArray();
        } catch (Exception $e) {
            Log::channel('email')->error('Circuit breaker: Failed to get all states', [
                'error' => $e->getMessage(),
            ]);
            
            return [];
        }
    }

    /**
     * Get the state of a specific provider's circuit breaker
     * 
     * @param string $providerKey The email provider key
     * @return array|null Circuit breaker state data or null if not found
     */
    public function getProviderState(string $providerKey): ?array
    {
        try {
            $circuitState = ProviderCircuitBreakerState::getOrCreateForProvider($providerKey);
            return $circuitState->getStats();
        } catch (Exception $e) {
            Log::channel('email')->error('Circuit breaker: Failed to get provider state', [
                'provider' => $providerKey,
                'error' => $e->getMessage(),
            ]);
            
            return null;
        }
    }

    /**
     * Reset a provider's circuit breaker to default state
     * 
     * @param string $providerKey The email provider key
     * @return bool True if reset successful
     */
    public function resetProvider(string $providerKey): bool
    {
        try {
            $circuitState = ProviderCircuitBreakerState::getOrCreateForProvider($providerKey);
            $circuitState->reset();
            
            Log::channel('email')->info('Circuit breaker: Provider manually reset', [
                'provider' => $providerKey,
            ]);
            
            return true;
        } catch (Exception $e) {
            Log::channel('email')->error('Circuit breaker: Failed to reset provider', [
                'provider' => $providerKey,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Reset all circuit breakers to default state
     * 
     * @return int Number of providers reset
     */
    public function resetAll(): int
    {
        try {
            $states = ProviderCircuitBreakerState::all();
            $resetCount = 0;
            
            foreach ($states as $state) {
                $state->reset();
                $resetCount++;
            }
            
            Log::channel('email')->info('Circuit breaker: All providers reset', [
                'reset_count' => $resetCount,
            ]);
            
            return $resetCount;
        } catch (Exception $e) {
            Log::channel('email')->error('Circuit breaker: Failed to reset all providers', [
                'error' => $e->getMessage(),
            ]);
            
            return 0;
        }
    }

    /**
     * Get providers that are currently experiencing issues
     * 
     * @return array Array of provider keys with issues
     */
    public function getUnhealthyProviders(): array
    {
        try {
            $states = ProviderCircuitBreakerState::where('status', '!=', ProviderCircuitBreakerState::STATUS_CLOSED)
                ->orWhere('last_failure_at', '>', now()->subMinutes(15))
                ->get();
            
            return $states->pluck('provider_key')->toArray();
        } catch (Exception $e) {
            Log::channel('email')->error('Circuit breaker: Failed to get unhealthy providers', [
                'error' => $e->getMessage(),
            ]);
            
            return [];
        }
    }

    /**
     * Get providers that are healthy and can handle requests
     * 
     * @param array $providersList List of providers to check
     * @return array Array of healthy provider keys in priority order
     */
    public function getHealthyProviders(array $providersList): array
    {
        $healthyProviders = [];
        
        foreach ($providersList as $provider) {
            if (!$this->isTripped($provider)) {
                $healthyProviders[] = $provider;
            }
        }
        
        Log::channel('email')->debug('Circuit breaker: Healthy providers identified', [
            'requested_providers' => $providersList,
            'healthy_providers' => $healthyProviders,
        ]);
        
        return $healthyProviders;
    }

    /**
     * Execute a callable with circuit breaker protection
     * 
     * @param string $providerKey The email provider key
     * @param callable $operation The operation to execute
     * @return mixed The result of the operation
     * @throws Exception If circuit is open or operation fails
     */
    public function execute(string $providerKey, callable $operation): mixed
    {
        // Check if circuit is tripped before attempting operation
        if ($this->isTripped($providerKey)) {
            throw new Exception("Circuit breaker is OPEN for provider: {$providerKey}");
        }

        try {
            // Execute the operation
            $result = $operation();
            
            // Record success
            $this->recordSuccess($providerKey);
            
            return $result;
        } catch (Exception $e) {
            // Record failure
            $this->recordFailure($providerKey, $e->getMessage());
            
            // Re-throw the original exception
            throw $e;
        }
    }

    /**
     * Get circuit breaker configuration for a provider
     * 
     * @param string $providerKey The email provider key
     * @return array Configuration array
     */
    public function getProviderConfig(string $providerKey): array
    {
        try {
            $circuitState = ProviderCircuitBreakerState::getOrCreateForProvider($providerKey);
            
            return [
                'failure_threshold' => $circuitState->failure_threshold,
                'success_threshold' => $circuitState->success_threshold,
                'timeout_duration' => $circuitState->timeout_duration,
            ];
        } catch (Exception $e) {
            Log::channel('email')->error('Circuit breaker: Failed to get provider config', [
                'provider' => $providerKey,
                'error' => $e->getMessage(),
            ]);
            
            // Return default configuration
            return [
                'failure_threshold' => 5,
                'success_threshold' => 3,
                'timeout_duration' => 300,
            ];
        }
    }

    /**
     * Update circuit breaker configuration for a provider
     * 
     * @param string $providerKey The email provider key
     * @param array $config Configuration array
     * @return bool True if update successful
     */
    public function updateProviderConfig(string $providerKey, array $config): bool
    {
        try {
            $circuitState = ProviderCircuitBreakerState::getOrCreateForProvider($providerKey);
            
            $updateData = [];
            if (isset($config['failure_threshold'])) {
                $updateData['failure_threshold'] = (int) $config['failure_threshold'];
            }
            if (isset($config['success_threshold'])) {
                $updateData['success_threshold'] = (int) $config['success_threshold'];
            }
            if (isset($config['timeout_duration'])) {
                $updateData['timeout_duration'] = (int) $config['timeout_duration'];
            }
            
            if (!empty($updateData)) {
                $circuitState->update($updateData);
                
                Log::channel('email')->info('Circuit breaker: Provider config updated', [
                    'provider' => $providerKey,
                    'updated_config' => $updateData,
                ]);
            }
            
            return true;
        } catch (Exception $e) {
            Log::channel('email')->error('Circuit breaker: Failed to update provider config', [
                'provider' => $providerKey,
                'config' => $config,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }
} 
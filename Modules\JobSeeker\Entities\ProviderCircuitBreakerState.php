<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Provider Circuit Breaker State Model
 * 
 * Manages the persistent state of circuit breakers for email providers.
 * Implements the Circuit Breaker pattern to prevent cascading failures.
 * 
 * @property string $provider_key
 * @property string $status
 * @property int $failure_count
 * @property int $success_count
 * @property \Carbon\Carbon|null $last_failure_at
 * @property \Carbon\Carbon|null $last_success_at
 * @property \Carbon\Carbon|null $opens_at
 * @property int $failure_threshold
 * @property int $success_threshold
 * @property int $timeout_duration
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
final class ProviderCircuitBreakerState extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'provider_circuit_breaker_states';

    /**
     * The primary key for the model.
     */
    protected $primaryKey = 'provider_key';

    /**
     * Indicates if the IDs are auto-incrementing.
     */
    public $incrementing = false;

    /**
     * The "type" of the primary key ID.
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'provider_key',
        'status',
        'failure_count',
        'success_count',
        'last_failure_at',
        'last_success_at',
        'opens_at',
        'failure_threshold',
        'success_threshold',
        'timeout_duration',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'failure_count' => 'integer',
        'success_count' => 'integer',
        'failure_threshold' => 'integer',
        'success_threshold' => 'integer',
        'timeout_duration' => 'integer',
        'last_failure_at' => 'datetime',
        'last_success_at' => 'datetime',
        'opens_at' => 'datetime',
    ];

    /**
     * Circuit breaker status constants
     */
    public const STATUS_CLOSED = 'CLOSED';
    public const STATUS_OPEN = 'OPEN';
    public const STATUS_HALF_OPEN = 'HALF_OPEN';

    /**
     * Get or create circuit breaker state for a provider
     */
    public static function getOrCreateForProvider(string $providerKey): self
    {
        return static::firstOrCreate(
            ['provider_key' => $providerKey],
            [
                'status' => self::STATUS_CLOSED,
                'failure_count' => 0,
                'success_count' => 0,
                'failure_threshold' => 5,
                'success_threshold' => 3,
                'timeout_duration' => 300, // 5 minutes
            ]
        );
    }

    /**
     * Check if the circuit breaker is tripped (open)
     */
    public function isTripped(): bool
    {
        // If circuit is closed, it's not tripped
        if ($this->status === self::STATUS_CLOSED) {
            return false;
        }

        // If circuit is open, check if it can transition to half-open
        if ($this->status === self::STATUS_OPEN) {
            if ($this->opens_at && $this->opens_at->isPast()) {
                $this->transitionToHalfOpen();
                return false; // Allow one test request
            }
            return true; // Still open
        }

        // If circuit is half-open, allow the request but it's considered "not tripped"
        return false;
    }

    /**
     * Record a successful operation
     */
    public function recordSuccess(): void
    {
        Log::channel('email')->info('Circuit breaker: Recording success', [
            'provider' => $this->provider_key,
            'current_status' => $this->status,
            'success_count' => $this->success_count,
        ]);

        $this->update([
            'last_success_at' => now(),
            'failure_count' => 0, // Reset failure count on success
        ]);

        if ($this->status === self::STATUS_HALF_OPEN) {
            $this->increment('success_count');
            
            // If we've reached the success threshold, close the circuit
            if ($this->success_count >= $this->success_threshold) {
                $this->transitionToClosed();
            }
        } elseif ($this->status === self::STATUS_OPEN) {
            // Shouldn't happen, but handle gracefully
            $this->transitionToHalfOpen();
        }
    }

    /**
     * Record a failed operation
     */
    public function recordFailure(): void
    {
        Log::channel('email')->warning('Circuit breaker: Recording failure', [
            'provider' => $this->provider_key,
            'current_status' => $this->status,
            'failure_count' => $this->failure_count,
            'failure_threshold' => $this->failure_threshold,
        ]);

        $this->update([
            'last_failure_at' => now(),
            'success_count' => 0, // Reset success count on failure
        ]);

        $this->increment('failure_count');

        // Check if we should open the circuit
        if ($this->failure_count >= $this->failure_threshold) {
            $this->transitionToOpen();
        }
    }

    /**
     * Transition circuit breaker to CLOSED state
     */
    private function transitionToClosed(): void
    {
        Log::channel('email')->info('Circuit breaker: Transitioning to CLOSED', [
            'provider' => $this->provider_key,
            'previous_status' => $this->status,
        ]);

        $this->update([
            'status' => self::STATUS_CLOSED,
            'failure_count' => 0,
            'success_count' => 0,
            'opens_at' => null,
        ]);
    }

    /**
     * Transition circuit breaker to OPEN state
     */
    private function transitionToOpen(): void
    {
        $opensAt = now()->addSeconds($this->timeout_duration);

        Log::channel('email')->error('Circuit breaker: Transitioning to OPEN', [
            'provider' => $this->provider_key,
            'previous_status' => $this->status,
            'failure_count' => $this->failure_count,
            'opens_at' => $opensAt->toISOString(),
            'timeout_duration' => $this->timeout_duration,
        ]);

        $this->update([
            'status' => self::STATUS_OPEN,
            'opens_at' => $opensAt,
            'success_count' => 0,
        ]);
    }

    /**
     * Transition circuit breaker to HALF_OPEN state
     */
    private function transitionToHalfOpen(): void
    {
        Log::channel('email')->info('Circuit breaker: Transitioning to HALF_OPEN', [
            'provider' => $this->provider_key,
            'previous_status' => $this->status,
        ]);

        $this->update([
            'status' => self::STATUS_HALF_OPEN,
            'success_count' => 0,
            'opens_at' => null,
        ]);
    }

    /**
     * Get all providers that need health checks
     */
    public static function getProvidersNeedingHealthCheck(): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('status', '!=', self::STATUS_CLOSED)
            ->orWhere('last_failure_at', '>', now()->subMinutes(30))
            ->get();
    }

    /**
     * Reset circuit breaker to default state
     */
    public function reset(): void
    {
        Log::channel('email')->info('Circuit breaker: Manual reset', [
            'provider' => $this->provider_key,
            'previous_status' => $this->status,
        ]);

        $this->update([
            'status' => self::STATUS_CLOSED,
            'failure_count' => 0,
            'success_count' => 0,
            'opens_at' => null,
            'last_failure_at' => null,
        ]);
    }

    /**
     * Get circuit breaker statistics
     */
    public function getStats(): array
    {
        return [
            'provider' => $this->provider_key,
            'status' => $this->status,
            'failure_count' => $this->failure_count,
            'success_count' => $this->success_count,
            'failure_threshold' => $this->failure_threshold,
            'success_threshold' => $this->success_threshold,
            'timeout_duration' => $this->timeout_duration,
            'last_failure_at' => $this->last_failure_at?->toISOString(),
            'last_success_at' => $this->last_success_at?->toISOString(),
            'opens_at' => $this->opens_at?->toISOString(),
            'is_tripped' => $this->isTripped(),
            'can_accept_requests' => !$this->isTripped(),
        ];
    }

    /**
     * Check if provider is healthy enough for requests
     */
    public function isHealthy(): bool
    {
        // Consider healthy if circuit is closed or if it's been a while since last failure
        if ($this->status === self::STATUS_CLOSED) {
            return true;
        }

        // If there's no recent failure, consider it healthy
        if (!$this->last_failure_at || $this->last_failure_at->isBefore(now()->subMinutes(30))) {
            return true;
        }

        return false;
    }

    /**
     * Get formatted status for display
     */
    public function getFormattedStatus(): string
    {
        return match ($this->status) {
            self::STATUS_CLOSED => '🟢 Closed (Healthy)',
            self::STATUS_OPEN => '🔴 Open (Unavailable)',
            self::STATUS_HALF_OPEN => '🟡 Half-Open (Testing)',
            default => '⚪ Unknown',
        };
    }
} 
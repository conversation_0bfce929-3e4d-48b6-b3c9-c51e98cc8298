<?php

namespace Modules\Education\Http\Controllers;


use App\MoshafSurah;
use App\Student;

use App\ijazaRevisionReport;
use App\StudentIjazasanadRevisionReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class IjazasanadClassWiseStudentRevisionReportsDatatablesController extends Controller
{

    /**
     * Display a listing of the resource.
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */

    public function studentRecords(Request $request)
    {
        DB::connection()->enableQueryLog();

        if ($request->filled('classDate')) {

            $planYearMonth = Carbon::parse($request->get('classDate'))->format('Y-m');
            $year = Carbon::parse($request->get('classDate'))->year;
            $month = Carbon::parse($request->get('classDate'))->month;
            $classId = $request->get('classId');
            $studentDetails = Student::whereHas('joint_classes', function ($query) use ($month, $year, $classId) {
                $query->where('class_id', $classId);
            })->whereHas('ijazasanad_revision_plans',function ($query) use ($month, $year, $classId) {

                $query->where(function ($q) use ($year, $month,$classId) {
                    $q->whereYear('created_at', $year)
                        ->whereMonth('created_at', $month)
                        ->whereNotNull('start_from_surat')
                        ->whereNotNull('start_from_ayat')
                        ->whereNotNull('to_surat')
                        ->whereNotNull('to_ayat')
                        ->where('status','active')
                        ->where('class_id', $classId);
                })->orWhere(function ($q2) use ($year, $month,$classId) {
                    $q2->whereYear('start_date', $year)
                        ->whereMonth('start_date', $month)
                        ->whereNotNull('start_from_surat')
                        ->whereNotNull('start_from_ayat')
                        ->whereNotNull('to_surat')
                        ->whereNotNull('to_ayat')
                        ->where('status','active')
                        ->where('class_id', $classId);
                });
                })
                ->with(['ijazaRevisionReport' => function ($query) use ($year, $month, $classId) {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('created_at', $year)
                            ->whereMonth('created_at', $month)
                            ->where('class_id', $classId)
                            ->whereNotNull('revision_from_surat')
                            ->whereNotNull('revision_from_ayat')
                            ->whereNotNull('revision_to_surat')
                            ->whereNotNull('revision_to_ayat');
                    });
                }])
                ->with(['ijazasanad_revision_plans' => function ($query) use ($month, $year, $classId) {
                    $query->where(function ($q) use ($year, $month,$classId) {
                        $q->whereYear('created_at', $year)
                            ->whereMonth('created_at', $month)
                            ->whereNotNull('start_from_surat')
                            ->whereNotNull('start_from_ayat')
                            ->whereNotNull('to_surat')
                            ->whereNotNull('to_ayat')
                            ->where('status','active')
                            ->where('class_id', $classId);
                    })->orWhere(function ($q2) use ($year, $month,$classId) {
                        $q2->whereYear('start_date', $year)
                            ->whereMonth('start_date', $month)
                            ->whereNotNull('start_from_surat')
                            ->whereNotNull('start_from_ayat')
                            ->whereNotNull('to_surat')
                            ->whereNotNull('to_ayat')
                            ->where('status','active')
                            ->where('class_id', $classId);
                    });


                }])
                ->withCount(['ijazasanad_revision_plans' => function ($query) use ($month, $year, $classId) {
                    $query->whereMonth('created_at', $month)
                        ->whereYear('created_at', $year)
                        ->where('class_id', $classId);
                }])
                ->withCount(['ijazaRevisionReport as revision_count' => function ($query) use ($month, $year, $classId) {


                    $start_date = Carbon::createFromDate($year, $month, 1); // first day of the month
                    $end_date = Carbon::createFromDate($year, $month, 1)->endOfMonth(); // last day of the month
                    $query->whereBetween('created_at', [$start_date, $end_date])
                        ->whereNotNull('revision_from_surat')
                        ->whereNotNull('revision_from_ayat')
                        ->whereNotNull('revision_to_surat')
                        ->whereNotNull('revision_to_ayat')
                        ->where('class_id', $classId);
                }])
                ->orderBy('full_name', 'asc')
                ->get();



            return \Yajra\DataTables\DataTables::of($studentDetails)
                ->addIndexColumn()
                ->addColumn('revisedPages', function ($student) use ($request, $classId) {


                    try {

                        if($student->revision_count > 0){

                            $firstRevision = $student->ijazaRevisionReport->sortBy(function ($row) {
                                return [$row->revision_from_surat, $row->revision_from_ayat];
                            })->first();

                            $lastRevision = $student->ijazaRevisionReport->sortByDesc(function ($row) {
                                return [$row->revision_to_surat, $row->revision_to_ayat];
                            })->first();

                            $min_revision_from_surat = $firstRevision->revision_from_surat;
                            $min_revision_from_ayat = $firstRevision->revision_from_ayat;
                            $max_revision_to_surat = $lastRevision->revision_to_surat;
                            $max_revision_to_ayat = $lastRevision->revision_to_ayat;
                            $revisionPlan = $student->ijazasanad_revision_plans[0];





                            // find out the number of pages memorized so far
                            if ($revisionPlan->study_direction == 'backward') {
                                $revisedNumberofPagesQuery = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $min_revision_from_surat,
                                    $min_revision_from_ayat,
                                    $max_revision_to_surat,
                                    $max_revision_to_ayat
                                ]);


                                $revisedNumberofPages = $revisedNumberofPagesQuery[0]->numberofPagesSum;



                            } else {

                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $min_revision_from_surat,
                                    $min_revision_from_ayat,
                                    $max_revision_to_surat,
                                    $max_revision_to_ayat
                                ]);

                                $revisedNumberofPagesQuery = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $revisedNumberofPages = $revisedNumberofPagesQuery[0]->number_of_pages_sum;

                            }

                            return '<h2 style="color: #1fff0f;
    font-weight: bolder;
    font-size: 24px;">' . $revisedNumberofPages . '</h2>';
                        }





                    } catch (\Exception $exception) {
                        dd(8);
                    }
                })
                ->addColumn('student', function ($student) use ($request) {

                    return '<span title="' . $student->id . '" style="color: #b4eeb0;">' . Str::title($student->full_name) . '</span>';

                    return '<a class="studentName" href="#" title="' . $student->id . '">' . Str::title($student->full_name) . '</a>';


                })
                ->addColumn('monthlyRevisionPlan', function ($student) use ($request) {
                    try {


                            $firstPlanSurat = $student->ijazasanad_revision_plans->first()->start_from_surat;
                            $firstPlanAyat = $student->ijazasanad_revision_plans->first()->start_from_ayat;
                            $lastPlanSurat = $student->ijazasanad_revision_plans->first()->to_surat;
                            $lastPlanAyat = $student->ijazasanad_revision_plans->first()->to_ayat;

                            $fromSuratAyat = MoshafSurah::where('id', $firstPlanSurat)->first()->eng_name . ' ' . $firstPlanAyat;
                            $lastSuratAyat = MoshafSurah::where('id', $lastPlanSurat)->first()->eng_name . ' ' . $lastPlanAyat;

                            return '<span title="' . $student->id . '" style="color: #b4eeb0;">' . $fromSuratAyat . ' – ' . $lastSuratAyat . '</span>';





                    } catch (\Exception $exception) {
                        return "222";
                    }

                })
                ->addColumn('monthlyRevisionReport', function ($student) use ($request, $classId) {
                    try {




                            $planYearMonth = Carbon::parse($request->get('classDate'));
                            $year = $planYearMonth->year;
                            $month = $planYearMonth->month;
                            $attendance_id = 2;

                            $data = StudentIjazasanadRevisionReport::where('student_id', $student->id)
                                ->where('class_id', (int)$classId)
                                ->whereMonth('created_at', $month)
                                ->whereYear('created_at', $year)
//                                ->where('attendance_id', $attendance_id)
                                ->selectRaw('min(revision_from_surat) as min_revision_from_surat, min(revision_from_ayat) as min_revision_from_ayat, max(revision_to_surat) as max_revision_to_surat, max(revision_to_ayat) as max_revision_to_ayat')
                                ->first();


                            $min_revision_from_surat = $data->min_revision_from_surat;
                            $min_revision_from_ayat = $data->min_revision_from_ayat;
                            $max_revision_to_surat = $data->max_revision_to_surat;
                            $max_revision_to_ayat = $data->max_revision_to_ayat;
                            $fromSuratAyat = MoshafSurah::where('id', $min_revision_from_surat)->first()->eng_name . ' ' . $min_revision_from_ayat;
                            $lastSuratAyat = MoshafSurah::where('id', $max_revision_to_surat)->first()->eng_name . ' ' . $max_revision_to_ayat;

//                            return ($fromSuratAyat == null || $fromSuratAyat == "" || $fromSuratAyat == 0 || $lastSuratAyat == null || $lastSuratAyat == "" || $lastSuratAyat == 0) ? "" : $fromSuratAyat.' – '.$lastSuratAyat;
                            return '<span title="' . $student->id . '" style="color: #b4eeb0;">' . $fromSuratAyat . ' – ' . $lastSuratAyat . '</span>';
                            return $fromSuratAyat . ' – ' . $lastSuratAyat;




                    } catch (\Exception $exception) {
                        dd(4);
                    }
                })
                ->addColumn('revisionAchievementComparedtoRevisionPlan', function ($student) use ($request, $classId) {


                    try {
                        if ($student->revision_count > 0) {
                            $planYearMonth = Carbon::parse($request->get('classDate'));
                            $year = $planYearMonth->year;
                            $month = $planYearMonth->month;
                            $attendance_id = 2;

                            $firstHefz = $student->ijazaRevisionReport->sortBy(function ($row) {
                                return [$row->revision_from_surat, $row->revision_from_ayat];
                            })->first();


                            $lastHefz = $student->ijazaRevisionReport->sortByDesc(function ($row) {
                                return [$row->revision_to_surat, $row->revision_to_ayat];
                            })->first();
                            $min_hefz_from_surat = $firstHefz->revision_from_surat;
                            $min_hefz_from_ayat = $firstHefz->revision_from_ayat;
                            $max_hefz_to_surat = $lastHefz->revision_to_surat;
                            $max_hefz_to_ayat = $lastHefz->revision_to_ayat;


                            // now find out the number of pages memorized so far


                            if ($student->ijazasanad_revision_plans->first()->study_direction == 'backward') {


                                $memorizedNumberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $min_hefz_from_surat,
                                    $min_hefz_from_ayat,
                                    $max_hefz_to_surat,
                                    $max_hefz_to_ayat
                                ]);


                                $memorizedNumberofPages = $memorizedNumberofPages[0]->numberofPagesSum;



                            }
                            else {

                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $min_hefz_from_surat,
                                    $min_hefz_from_ayat,
                                    $max_hefz_to_surat,
                                    $max_hefz_to_ayat
                                ]);

                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $memorizedNumberofPages = $results[0]->number_of_pages_sum;

                            }

                            $firstPlanSurat = $student->ijazasanad_revision_plans->first()->start_from_surat;
                            $firstPlanAyat = $student->ijazasanad_revision_plans->first()->start_from_ayat;
                            $lastPlanSurat = $student->ijazasanad_revision_plans->first()->to_surat;
                            $lastPlanAyat = $student->ijazasanad_revision_plans->first()->to_ayat;


                            // now find out the number of pages asssigned at the hefz plan
                            if ($student->ijazasanad_revision_plans->first()->study_direction == 'backward') {

                                $plannedNumberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $firstPlanSurat,
                                    $firstPlanAyat,
                                    $lastPlanSurat,
                                    $lastPlanAyat
                                ]);


                                $plannedNumberofPages = $plannedNumberofPages[0]->numberofPagesSum;


                            }
                            else {
                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $firstPlanSurat,
                                    $firstPlanAyat,
                                    $lastPlanSurat,
                                    $lastPlanAyat
                                ]);

                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $plannedNumberofPages = $results[0]->number_of_pages_sum;

                            }


                            if (empty($memorizedNumberofPages) || is_null($memorizedNumberofPages)) {
//                            $result = "No revision so far";
                                $result = 0;
                                $result = '<div class="progress" style="position: relative;">
  <div  class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
  </div>';
                            } elseif (empty($plannedNumberofPages) || is_null($plannedNumberofPages)) {
//                            $result = "No plan available";
                                $result = 0;
                                $result = '<div class="progress" style="position: relative;">
  <div  class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
  </div>';
                            } else {
                                $actual_percentage = round(($memorizedNumberofPages / $plannedNumberofPages * 100), 2);
                                $expected_percentage = 100;
                                $result = min($actual_percentage, $expected_percentage);

                                $result = '<div class="progress" style="position: relative;">
  <div  class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
  </div>
</div>';
                            }

                            return $result;

                            $hefzAcievementComparedtoPlan = $memorizedNumberofPages / $plannedNumberofPages * 100;


                            return $hefzAcievementComparedtoPlan;
                        }
                    } catch (\Exception $exception) {
                        return response()->json($exception->getMessage());
                    }

                })
                ->addColumn('attendanceDaysPercentage', function ($student) use ($request,$month, $year) {



                    try {





                        $result = round($student->revisionAttendancePercentageForMonth($month, $year), 2);
                        return  '<div class="progress" style="position: relative;">
  <div  class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
  </div>
</div>';







                        $result = round($student->revisionAttendancePercentageForMonth($month, $year), 2);

                        return  '<div class="progress" style="position: relative;">
  <div  class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' .$result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%;background-color: #1fff0f;">
    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
  </div>
</div>';


                    } catch (\Exception $exception) {
                        dd(6);
                    }
                })
                ->rawColumns(['revisionAchievementComparedtoRevisionPlan','attendanceDaysPercentage','student', 'revisedPages','monthlyRevisionPlan','monthlyRevisionReport'])
                ->make(true);

        }



    }

}

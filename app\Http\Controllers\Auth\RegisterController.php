<?php

namespace App\Http\Controllers\Auth;


use App\Http\Requests\StudentRegisterRequest;
use App\Rules\CheckIfStringIsEnglish;
use App\User;
use App\Http\Controllers\Controller;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Validation\Rule;
use Modules\Admission\Facades\Settings;
use Illuminate\Validation\ValidationException;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;


    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = '/';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {

        $this->middleware('guest', ['except' => ['verify', 'showResendVerificationEmailForm', 'resendVerificationEmail']]);
        $this->middleware('web', ['only' => ['showResendVerificationEmailForm', 'resendVerificationEmail']]);
//        $this->middleware('guest');
    }


    /**
     * Get the response for a successful user verification.
     *
     * @param string $response
     * @return \Illuminate\Http\RedirectResponse
     */
    protected function sendVerificationResponse($response)
    {

        return redirect($this->redirectPath())->with('success', trans($response));
    }

    /**
     * Get the post register / login redirect path.
     *
     * @return string
     */

    public function redirectPath()
    {
//        if (method_exists($this, 'redirectTo')) {
//            return $this->redirectTo();
//        }



//        $this->redirectTo =  \Auth::guard("web")->user()->hasRole("parent") /** parent */ ?  '/parent-dashboard' : '/student-dashboard';

       // if user is logged in
        if (Auth::guard("web")->user()) {

            if (Auth::guard("web")->user()->hasRole("parent")) {

                $this->redirectTo = '/parent-dashboard';
            } elseif (Auth::guard("web")->user()->hasRole("student")) {
                $this->redirectTo = '/student-dashboard';

            } else {

                $this->redirectTo = 'applicationcenter/registration';

            }
        }


//        $this->redirectTo =  \Auth::guard("web")->user()->hasRole("parent") /** parent */ ?  '/parent-dashboard' : '/student-dashboard';

        return property_exists($this, 'redirectTo') ? $this->redirectTo : '/';
    }


    /**
     * Get a validator for an incoming registration request.
     *
     * @param array $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        // TEMPORARY: Skip reCAPTCHA validation for testing
        // $this->validateRecaptcha($data['recaptcha_token'] ?? null);

        return Validator::make($data, [
            'fullname' => ['required','string','max:255','different:email',new CheckIfStringIsEnglish()],
            'nationality' => ['required',Rule::notIn(['no'])], // no refers to the select label. the value no is defined in the Admission/Classes/Settings.php
            'displayname' => 'required|string|max:255|different:email',
            'username' => 'required|string|max:25|unique:users,username',
            'email' => 'required|string|email|max:255|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
//            'role' => ['required', Rule::in($parentStudentRoles)]

        ]);

    }

    /**
     * Validate reCAPTCHA token.
     *
     * @param string|null $token
     * @throws ValidationException
     */
    protected function validateRecaptcha($token)
    {
        // TEMPORARY: Skip reCAPTCHA validation for testing
        return true;

        /* Original code - temporarily disabled
        if (!$token) {
            Log::error('reCAPTCHA token missing in registration request');
            throw ValidationException::withMessages([
                'recaptcha' => ['reCAPTCHA verification failed. Please try again.']
            ]);
        }

        try {
            $response = Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
                'secret' => config('services.recaptcha.secret_key'),
                'response' => $token
            ]);

            $result = $response->json();

            if (!($result['success'] ?? false) || ($result['score'] ?? 0) < 0.5) {
                Log::warning('reCAPTCHA validation failed', [
                    'token' => substr($token, 0, 10) . '...',
                    'response' => $result
                ]);
                throw ValidationException::withMessages([
                    'recaptcha' => ['reCAPTCHA verification failed. Please try again.']
                ]);
            }

            Log::info('reCAPTCHA validation passed', [
                'score' => $result['score'] ?? null,
                'action' => $result['action'] ?? null
            ]);
        } catch (\Exception $e) {
            Log::error('reCAPTCHA verification error', [
                'error' => $e->getMessage()
            ]);
            throw ValidationException::withMessages([
                'recaptcha' => ['Error verifying reCAPTCHA. Please try again.']
            ]);
        }
        */
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param array $data
     * @return \App\User
     * @throws \Exception
     */
    protected function create(array $data, $roleName = null)
    {


        $user = User::create([
            'email' => $data['email'],
            'display_name' => $data['displayname'],
            'username' => $data['username'],
            'full_name' => $data['fullname'],
            'nationality' => $data['nationality'],
            'access_status' => '0',
            'is_administrator' => 'no',
            'organization_id' => config('organization_id'),
            'password' => bcrypt($data['password']),
        ]);

        // assign default role
        $this->assignDefaultRoles($user, $roleName);


        return $user;

    }


    /**
     * Show the application registration form.
     *
     * @return \Illuminate\Http\Response
     */
    public function showRegistrationForm()
    {

        $countryList = Settings::getCountriesList();

        return view('auth.registerItqan', compact('countryList'));
    }

    /**
     * Handle a registration request for the application.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
//    public
//    function register(StudentRegisterRequest $request, $roleName = null)
//    {
//
//        DB::beginTransaction();
//
//        try {
//            event(new Registered($user = $this->create($request->all(), $roleName)));
//
//
//            // send verification email
////            app()->make('Lunaweb\EmailVerification\EmailVerification')->sendVerifyLink($user);
//
//
////        $this->guard()->login($user);
////        return $this->registered($request, $user)
////            ?: redirect($this->redirectPath());
////        Toastr::success('Operation successful, Please contact with administrator for confirmation', 'Success');
//            DB::commit();
//
//            return redirect()->back()->with('success', 'Please confirm your email or wait for manual confirmation');
//        } catch (\Exception $e) {
//            DB::rollback();
//            dd($e->getMessage());
//            Toastr::error($e->getMessage(), 'Failed');
//            return redirect()->back();
//        }
//    }


    public function register(StudentRegisterRequest $request, $roleName = null)
    {
        try {
            // Start database transaction
            DB::beginTransaction();
            
            // Manually verify Turnstile token
            $verification = Http::asForm()->post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
                'secret'   => config('services.turnstile.secret'),
                'response' => $request->input('cf-turnstile-response'),
                'remoteip' => $request->ip(),
            ]);

            // Attempt to create user record
            $user = $this->create($request->all(), $roleName);
            
            // Log successful creation
            Log::debug('User record created', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);
            
            // Manually commit the transaction
            DB::commit();
            
            // AFTER successful commit, trigger email
            event(new Registered($user));
            
            // Log successful registration
            Log::info('User registered successfully', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);
            
            // Use flash data instead of regular session data
            // This ensures the data will only be available for the next request
            session()->flash('success', 'Please confirm your email or wait for manual confirmation');
            session()->flash('user_email', $user->email);
            
            // Log that we're about to redirect
            Log::debug('About to redirect with session data', [
                'session_has_success' => session()->has('success'),
                'session_has_email' => session()->has('user_email'),
                'user_email' => $user->email
            ]);

            // Return redirect response
            return redirect()->back();
        } catch (\Exception $e) {
            // If an exception occurred and transaction was started, roll it back
            if (DB::transactionLevel() > 0) {
                DB::rollBack();
            }
            
            // Log detailed error information for the development team
            Log::error('Registration failed', [
                'error_message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'input' => $request->except(['password', 'password_confirmation'])
            ]);
            
            // Use flash data for error messages too
            session()->flash('error', 'Registration failed. Please try again'.(config('app.debug') ? ': '.$e->getMessage() : ''));
            
            return redirect()->back()->withInput(
                $request->except(['password', 'password_confirmation'])
            );
        }
    }

    protected function guard()
    {
        return Auth::guard("web");
    }

    // unique usernamecheck by ajax
    public function checkUsername(Request $request)
    {
        $student = User::where('username', $request->id)->first();

        if ($student != "") {
            return response()->json(1);
        } else {
            return response()->json(0);
        }
    }


    // unique email by ajax
    public function checkEmail(Request $request)
    {
        $student = User::where('email', $request->id)->first();

        if ($student != "") {
            return response()->json(1);
        } else {
            return response()->json(0);
        }
    }



    /**
     * Show form to the user which allows resending the verification mail
     *
     */
    public function showResendVerificationEmailForm(Request $request)
    {


        $user = Auth::user();


        return view('emailverification::resend', ['verified' => $user->verified, 'email' => $user->email]);
    }

    public function resendVerificationEmail(Request $request)
    {
        $user = Auth::user();
        $this->validate($request, [
            'username' => 'required|max:255|exists:users,username'
        ]);



        $user =  User::where('username',$request->get('username'))->first();

        $sent = resolve('Lunaweb\EmailVerification\EmailVerification')->sendVerifyLink($user);
        Session::flash($sent == EmailVerification::VERIFY_LINK_SENT ? 'success' : 'error', trans($sent));
        if(EmailVerification::VERIFY_LINK_SENT == true){
            Toastr::success(trans($sent), 'Success');
        }else{

            Toastr::error(trans($sent), 'Failed');
        }



        return redirect()->back();
    }



    public    function assignDefaultRoles($user, $roleName = null)
    {


        $user->assignRole('member');
    }


}

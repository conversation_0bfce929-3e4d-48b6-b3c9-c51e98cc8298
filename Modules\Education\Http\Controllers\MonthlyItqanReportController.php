<?php

namespace Modules\Education\Http\Controllers;

use App\AttendanceOption;
use App\Employee;
use App\EvaluationSchemaOption;
use App\StudentAttendance;
use App\Student;
use App\StudentHefzPlan;
use App\StudentRevisionPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class MonthlyItqanReportController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function __invoke(Request $request)
    {

        DB::connection()->enableQueryLog();
        //        if ($request->ajax()) {
        try {
            $planYearMonth = Carbon::parse($request->get('classDate'));
            $year = $planYearMonth->year;
            $month = $planYearMonth->month;



            $centers = Center::
            with(['classes' => function ($query) use ($month, $year) {
                $query->withCount(['students'])
                    ->withCount(['revision_plans' => function ($query) use ($month, $year) {
                        $query->whereMonth('created_at', $month)
                            ->whereYear('created_at', $year);
                    }])->withCount(['hefz_plans' => function ($query) use ($month, $year) {
                        $query->whereMonth('created_at', $month)
                            ->whereYear('created_at', $year);
                    }])->withCount(['completedHefzReport as totalSessions' => function ($query) use ($month, $year) {
                        $query->whereMonth('created_at', $month)
                            ->whereYear('created_at', $year);
                    }]);
            }])



//                ->whereHas('classes.completedHefzReport', function ($query) use ($month, $year) {
//                    $query->whereMonth('created_at', $month)
//                        ->whereYear('created_at', $year);
//                })
                ->with(['classes.completedHefzReport' => function ($query) use ($month, $year) {
                    $query->whereMonth('created_at', $month)
                        ->whereYear('created_at', $year);
                }])
//                ->whereHas('classes.completedRevisionReport', function ($query) use ($month, $year) {
//                    $query->whereMonth('created_at', $month)
//                        ->whereYear('created_at', $year);
//                })
                ->with(['classes.completedRevisionReport' => function ($query) use ($month, $year) {
                    $query->whereMonth('created_at', $month)
                        ->whereYear('created_at', $year);
                }])
//                ->whereHas('classes.revision_plans', function ($query) use ($month, $year) {
//                    $query->whereMonth('created_at', $month)
//                        ->whereYear('created_at', $year);
//                })
                ->with(['classes.revision_plans' => function ($query) use ($month, $year) {
                    $query->whereMonth('created_at', $month)
                        ->whereYear('created_at', $year);
                }])
//                ->whereHas('classes.hefz_plans', function ($query) use ($month, $year) {
//
//                    $query->whereMonth('created_at', $month)
//                        ->whereYear('created_at', $year);
//                })
                ->with(['classes.hefz_plans' => function ($query) use ($month, $year) {
                    $query->whereMonth('created_at', $month)
                        ->whereYear('created_at', $year);
                }])
                ->get();




            return \Yajra\DataTables\DataTables::of($centers)
                ->addIndexColumn()
                ->addColumn('centersCount', function ($center) use ($request) {
                    return $center->name;
                })->addColumn('halaqahsCount', function ($center) use ($request) {

                    return $center->classes->count();
                })
                ->addColumn('studentsCount', function ($center) use ($request) {
                    $studentsCount = 0;

                    foreach ($center->classes as $class) {
                        $studentsCount += $class->withCount('students')->first()->students_count;
                        // do something with the students count
                    }

                    return $studentsCount;
//                    return $center->classes->first()->withCo?unt('students')->first()->students_count;
                })
                ->addColumn('memorizedPages', function ($center) use ($request) {
//                    get me the total number of pages memorized for this halaqah
                    $numberofPagesSum = 0;
                    foreach ($center->classes as $classDetails) {


                        foreach ($classDetails->completedHefzReport as $reportDetails) {

                            if ($reportDetails->hefzPlan->study_direction == 'backward') {
                                if ($reportDetails->hefz_from_surat == $reportDetails->hefz_to_surat) {
                                    $numberofPages = \DB::select("SELECT aa.fromSuratCount + bb.toSuratCount AS pageCount
FROM (
         SELECT COUNT(*) AS fromSuratCount
         FROM (
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :startSurahId AND (first_ayah <= :startAyah1 OR last_ayah <= :startAyah2) UNION
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :startSurahId2 AND (first_ayah >= (
                      SELECT last_ayah
                      FROM moshaf_pages
                      WHERE surah_id = :startSurahId3
                      ORDER BY last_ayah DESC
                      LIMIT 1) OR last_ayah <= (
                      SELECT last_ayah
                      FROM moshaf_pages
                      WHERE surah_id = :startSurahId4
                      ORDER BY last_ayah DESC
                      LIMIT 1))
                  ORDER BY page_number DESC) a) aa,

     (
         SELECT COUNT(*) AS toSuratCount
         FROM (
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :lastSurahId1 AND first_ayah = 1
                  UNION
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :lastSurahId2 AND (first_ayah >= :lastAyah1 OR last_ayah <= :lastAyah2)) a
         ORDER BY page_number DESC) bb", array(
                                        'startSurahId' => $reportDetails->hefz_from_surat,
                                        'startSurahId2' => $reportDetails->hefz_from_surat,
                                        'startSurahId3' => $reportDetails->hefz_from_surat,
                                        'startSurahId4' => $reportDetails->hefz_from_surat,
                                        'startAyah1' => optional($reportDetails)->pluck('hefz_from_ayat')[0],
                                        'startAyah2' => optional($reportDetails)->pluck('hefz_from_ayat')[0],
                                        'lastSurahId1' => $reportDetails->hefz_to_surat,
                                        'lastSurahId2' => $reportDetails->hefz_to_surat,
                                        'lastAyah1' => optional($reportDetails)->hefz_to_ayat,
                                        'lastAyah2' => optional($reportDetails)->hefz_to_ayat,
                                    ));
                                    $numberofPagesSum += $numberofPages[0]->pageCount + 1;

                                } else {
                                    $numberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                        'startSurahId' => $reportDetails->hefz_from_surat,
                                        'startAyah' => $reportDetails->hefz_from_ayat,
                                        'lastSurahId' => $reportDetails->hefz_to_surat,
                                        'lastAyah' => $reportDetails->hefz_to_ayat,
                                        'lastAyah2' => $reportDetails->hefz_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                    ));
                                    $numberofPagesSum += $numberofPages[0]->pageCount;
                                }

                            } else {

                                if ($classDetails->hefz_from_surat == $classDetails->hefz_to_surat) {
                                    $numberofPages = \DB::select("select *
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                        'startSurahId' => $reportDetails->hefz_from_surat,
                                        'startAyah' => $reportDetails->hefz_from_ayat,
                                        'lastSurahId' => $reportDetails->hefz_to_surat,
                                        'lastAyah' => $reportDetails->hefz_to_ayat,
                                        'lastAyah2' => $reportDetails->hefz_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                    ));


                                    if (($numberofPages[0]->first_page == $numberofPages[0]->last_page) && ($numberofPages[0]->first_page > 0 && $numberofPages[0]->last_page > 0)) {
                                        $numberofPages = 1;

                                    } else {

                                        $numberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                            'startSurahId' => $reportDetails->hefz_from_surat,
                                            'startAyah' => $reportDetails->hefz_from_ayat,
                                            'lastSurahId' => $reportDetails->hefz_to_surat,
                                            'lastAyah' => $reportDetails->hefz_to_ayat,
                                            'lastAyah2' => $reportDetails->hefz_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                        ));


                                        $numberofPagesSum += $numberofPages[0]->pageCount;

                                    }

                                }
                                else {


                                    $numberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                        'startSurahId' => $reportDetails->hefz_from_surat,
                                        'startAyah' => $reportDetails->hefz_from_ayat,
                                        'lastSurahId' => $reportDetails->hefz_to_surat,
                                        'lastAyah' => $reportDetails->hefz_to_ayat,
                                        'lastAyah2' => $reportDetails->hefz_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                    ));
                                    $numberofPagesSum += $numberofPages[0]->pageCount;


                                }
                            }
                        }
                    }

                    return $numberofPagesSum;

                })
                ->addColumn('revisedPages', function ($center) use ($request) {
                    $numberofPagesSum = 0;
                    foreach ($center->classes as $classDetails) {

                        foreach ($classDetails->completedRevisionReport as $reportDetails) {

                            if ($reportDetails->revision_plans->study_direction == 'backward') {

                                if ($reportDetails->revision_from_surat == $reportDetails->revision_to_surat) {
                                    $numberofPages = \DB::select("SELECT aa.fromSuratCount + bb.toSuratCount AS pageCount
FROM (
         SELECT COUNT(*) AS fromSuratCount
         FROM (
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :startSurahId AND (first_ayah <= :startAyah1 OR last_ayah <= :startAyah2) UNION
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :startSurahId2 AND (first_ayah >= (
                      SELECT last_ayah
                      FROM moshaf_pages
                      WHERE surah_id = :startSurahId3
                      ORDER BY last_ayah DESC
                      LIMIT 1) OR last_ayah <= (
                      SELECT last_ayah
                      FROM moshaf_pages
                      WHERE surah_id = :startSurahId4
                      ORDER BY last_ayah DESC
                      LIMIT 1))
                  ORDER BY page_number DESC) a) aa,

     (
         SELECT COUNT(*) AS toSuratCount
         FROM (
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :lastSurahId1 AND first_ayah = 1
                  UNION
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :lastSurahId2 AND (first_ayah >= :lastAyah1 OR last_ayah <= :lastAyah2)) a
         ORDER BY page_number DESC) bb", array(
                                        'startSurahId' => $reportDetails->revision_from_surat,
                                        'startSurahId2' => $reportDetails->revision_from_surat,
                                        'startSurahId3' => $reportDetails->revision_from_surat,
                                        'startSurahId4' => $reportDetails->revision_from_surat,
                                        'startAyah1' => optional($reportDetails)->pluck('revision_from_ayat')[0],
                                        'startAyah2' => optional($reportDetails)->pluck('revision_from_ayat')[0],
                                        'lastSurahId1' => $reportDetails->revision_to_surat,
                                        'lastSurahId2' => $reportDetails->revision_to_surat,
                                        'lastAyah1' => optional($reportDetails)->revision_to_ayat,
                                        'lastAyah2' => optional($reportDetails)->revision_to_ayat,
                                    ));
                                    $numberofPagesSum += $numberofPages[0]->pageCount + 1;

                                } else {
                                    $numberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                        'startSurahId' => $reportDetails->revision_from_surat,
                                        'startAyah' => $reportDetails->revision_from_ayat,
                                        'lastSurahId' => $reportDetails->revision_to_surat,
                                        'lastAyah' => $reportDetails->revision_to_ayat,
                                        'lastAyah2' => $reportDetails->revision_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                    ));
                                    $numberofPagesSum += $numberofPages[0]->pageCount;
                                }

                            } else {

                                if ($classDetails->revision_from_surat == $classDetails->revision_to_surat) {
                                    $numberofPages = \DB::select("select *
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                        'startSurahId' => $reportDetails->revision_from_surat,
                                        'startAyah' => $reportDetails->revision_from_ayat,
                                        'lastSurahId' => $reportDetails->revision_to_surat,
                                        'lastAyah' => $reportDetails->revision_to_ayat,
                                        'lastAyah2' => $reportDetails->revision_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                    ));


                                    if (($numberofPages[0]->first_page == $numberofPages[0]->last_page) && ($numberofPages[0]->first_page > 0 && $numberofPages[0]->last_page > 0)) {
                                        $numberofPagesSum += 1;

                                    } else {

                                        $numberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                            'startSurahId' => $reportDetails->revision_from_surat,
                                            'startAyah' => $reportDetails->revision_from_ayat,
                                            'lastSurahId' => $reportDetails->revision_to_surat,
                                            'lastAyah' => $reportDetails->revision_to_ayat,
                                            'lastAyah2' => $reportDetails->revision_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                        ));


                                        $numberofPagesSum += $numberofPages[0]->pageCount;

                                    }

                                } else {


                                    $numberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                        'startSurahId' => $reportDetails->revision_from_surat,
                                        'startAyah' => $reportDetails->revision_from_ayat,
                                        'lastSurahId' => $reportDetails->revision_to_surat,
                                        'lastAyah' => $reportDetails->revision_to_ayat,
                                        'lastAyah2' => $reportDetails->revision_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                    ));
                                    $numberofPagesSum += $numberofPages[0]->pageCount;


                                }
                            }
                        }

                    }


                    return $numberofPagesSum;


                })
                ->addColumn('attendanceDaysPercentage', function ($center) use ($request, $month, $year) {




                    $attendanceCount = 0;
                    $totalSessions = 0;
                    foreach ($center->classes as $classDetails) {

                        foreach ($classDetails->completedHefzReport as $reportDetails) {
                            if ($reportDetails->attendance_id == 2) {
                                $attendanceCount++;
                            }
                            $totalSessions++;

                        }
                    }

                    $result = 0;
                    if (($attendanceCount == 0 or is_null($attendanceCount)) && ($totalSessions == 0 or is_null($totalSessions))) {
                        $result = 0;
                    } elseif ($attendanceCount == 0 or is_null($attendanceCount)) {
                        $result = 0;
                    } elseif ($totalSessions == 0 or is_null($totalSessions)) {
                        $result = 0;
                    } else {


                        $result = round(($attendanceCount / $totalSessions * 100),2);
                    }

                    return $result;


                })
                ->addColumn('hefzAchievementComparedtoHefzPlan', function ($center) use ($request) {
                    $planYearMonth = Carbon::parse($request->get('classDate'));
                    $memorizedNumberofPagesSum = 0;
                    $plannedNumberofPagesSum = 0;
                    foreach ($center->classes as $classDetails) {
                        foreach ($classDetails->completedHefzReport as $studentHefzDetails) {
                            $hefzPlan = $studentHefzDetails->hefzPlan;
                            // now find out the number of pages memorized so far
                            if ($hefzPlan->study_direction == 'backward') {
                                if ($studentHefzDetails->hefz_from_surat == $studentHefzDetails->hefz_to_surat) {
                                    $memorizedNumberofPages = \DB::select("SELECT aa.fromSuratCount + bb.toSuratCount AS pageCount
FROM (
         SELECT COUNT(*) AS fromSuratCount
         FROM (
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :startSurahId AND (first_ayah <= :startAyah1 OR last_ayah <= :startAyah2) UNION
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :startSurahId2 AND (first_ayah >= (
                      SELECT last_ayah
                      FROM moshaf_pages
                      WHERE surah_id = :startSurahId3
                      ORDER BY last_ayah DESC
                      LIMIT 1) OR last_ayah <= (
                      SELECT last_ayah
                      FROM moshaf_pages
                      WHERE surah_id = :startSurahId4
                      ORDER BY last_ayah DESC
                      LIMIT 1))
                  ORDER BY page_number DESC) a) aa,

     (
         SELECT COUNT(*) AS toSuratCount
         FROM (
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :lastSurahId1 AND first_ayah = 1
                  UNION
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :lastSurahId2 AND (first_ayah >= :lastAyah1 OR last_ayah <= :lastAyah2)) a
         ORDER BY page_number DESC) bb", array(
                                        'startSurahId' => $studentHefzDetails->hefz_from_surat,
                                        'startSurahId2' => $studentHefzDetails->hefz_from_surat,
                                        'startSurahId3' => $studentHefzDetails->hefz_from_surat,
                                        'startSurahId4' => $studentHefzDetails->hefz_from_surat,
                                        'startAyah1' => $studentHefzDetails->hefz_from_ayat,
                                        'startAyah2' => $studentHefzDetails->hefz_from_ayat,
                                        'lastSurahId1' => $studentHefzDetails->hefz_to_surat,
                                        'lastSurahId2' => $studentHefzDetails->hefz_to_surat,
                                        'lastAyah1' => $studentHefzDetails->hefz_to_ayat,
                                        'lastAyah2' => $studentHefzDetails->hefz_to_ayat,
                                    ));
                                    $memorizedNumberofPagesSum += $memorizedNumberofPages[0]->pageCount + 1;

                                } else {
                                    $memorizedNumberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                        'startSurahId' => $studentHefzDetails->hefz_from_surat,
                                        'startAyah' => $studentHefzDetails->hefz_from_ayat,
                                        'lastSurahId' => $studentHefzDetails->hefz_to_surat,
                                        'lastAyah' => $studentHefzDetails->hefz_to_ayat,
                                        'lastAyah2' => $studentHefzDetails->hefz_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                    ));
                                    $memorizedNumberofPagesSum += $memorizedNumberofPages[0]->pageCount;
                                }

                            } else {

                                if ($studentHefzDetails->hefz_from_surat == $studentHefzDetails->hefz_to_surat) {
                                    $memorizedNumberofPages = \DB::select("select *
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                        'startSurahId' => $studentHefzDetails->hefz_from_surat,
                                        'startAyah' => $studentHefzDetails->hefz_from_ayat,
                                        'lastSurahId' => $studentHefzDetails->hefz_to_surat,
                                        'lastAyah' => $studentHefzDetails->hefz_to_ayat,
                                        'lastAyah2' => $studentHefzDetails->hefz_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                    ));


                                    if (($memorizedNumberofPages[0]->first_page == $memorizedNumberofPages[0]->last_page) && ($memorizedNumberofPages[0]->first_page > 0 && $memorizedNumberofPages[0]->last_page > 0)) {
                                        $memorizedNumberofPagesSum += 1;

                                    } else {

                                        $memorizedNumberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                            'startSurahId' => $studentHefzDetails->hefz_from_surat,
                                            'startAyah' => $studentHefzDetails->hefz_from_ayat,
                                            'lastSurahId' => $studentHefzDetails->hefz_to_surat,
                                            'lastAyah' => $studentHefzDetails->hefz_to_ayat,
                                            'lastAyah2' => $studentHefzDetails->hefz_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                        ));


                                        $memorizedNumberofPagesSum += $memorizedNumberofPages[0]->pageCount;

                                    }

                                } else {


                                    $memorizedNumberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                        'startSurahId' => $studentHefzDetails->hefz_from_surat,
                                        'startAyah' => $studentHefzDetails->hefz_from_ayat,
                                        'lastSurahId' => $studentHefzDetails->hefz_to_surat,
                                        'lastAyah' => $studentHefzDetails->hefz_to_ayat,
                                        'lastAyah2' => $studentHefzDetails->hefz_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                    ));
                                    $memorizedNumberofPagesSum += $memorizedNumberofPages[0]->pageCount;


                                }
                            }
                        }
                    }
                    // Now we are going to get the planned Hefz PLanned

                    foreach ($center->classes as $classDetails) {


                        foreach ($classDetails->hefz_plans as $studentHefzPlanDetails) {


                            // now find out the number of pages asssigned at the hefz plan
                            if ($studentHefzPlanDetails->study_direction == 'backward') {
                                if ($studentHefzPlanDetails->start_from_surat == $studentHefzPlanDetails->to_surat) {
                                    $plannedNumberofPages = \DB::select("SELECT aa.fromSuratCount + bb.toSuratCount AS pageCount
FROM (
         SELECT COUNT(*) AS fromSuratCount
         FROM (
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :startSurahId AND (first_ayah <= :startAyah1 OR last_ayah <= :startAyah2) UNION
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :startSurahId2 AND (first_ayah >= (
                      SELECT last_ayah
                      FROM moshaf_pages
                      WHERE surah_id = :startSurahId3
                      ORDER BY last_ayah DESC
                      LIMIT 1) OR last_ayah <= (
                      SELECT last_ayah
                      FROM moshaf_pages
                      WHERE surah_id = :startSurahId4
                      ORDER BY last_ayah DESC
                      LIMIT 1))
                  ORDER BY page_number DESC) a) aa,

     (
         SELECT COUNT(*) AS toSuratCount
         FROM (
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :lastSurahId1 AND first_ayah = 1
                  UNION
                  SELECT *
                  FROM moshaf_pages
                  WHERE surah_id = :lastSurahId2 AND (first_ayah >= :lastAyah1 OR last_ayah <= :lastAyah2)) a
         ORDER BY page_number DESC) bb", array(
                                        'startSurahId' => $studentHefzPlanDetails->start_from_surat,
                                        'startSurahId2' => $studentHefzPlanDetails->start_from_surat,
                                        'startSurahId3' => $studentHefzPlanDetails->start_from_surat,
                                        'startSurahId4' => $studentHefzPlanDetails->start_from_surat,
                                        'startAyah1' => $studentHefzPlanDetails->start_from_ayat,
                                        'startAyah2' => $studentHefzPlanDetails->start_from_ayat,
                                        'lastSurahId1' => $studentHefzPlanDetails->to_surat,
                                        'lastSurahId2' => $studentHefzPlanDetails->to_surat,
                                        'lastAyah1' => $studentHefzPlanDetails->to_ayat,
                                        'lastAyah2' => $studentHefzPlanDetails->to_ayat,
                                    ));
                                    $plannedNumberofPagesSum += $plannedNumberofPages[0]->pageCount + 1;

                                } else {
                                    $plannedNumberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                        'startSurahId' => $studentHefzPlanDetails->start_from_surat,
                                        'startAyah' => $studentHefzPlanDetails->start_from_ayat,
                                        'lastSurahId' => $studentHefzPlanDetails->to_surat,
                                        'lastAyah' => $studentHefzPlanDetails->to_ayat,
                                        'lastAyah2' => $studentHefzPlanDetails->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                    ));
                                    $plannedNumberofPagesSum += $plannedNumberofPages[0]->pageCount;
                                }

                            } else {

                                if ($studentHefzPlanDetails->start_from_surat == $studentHefzPlanDetails->to_surat) {
                                    $plannedNumberofPages = \DB::select("select *
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                        'startSurahId' => $studentHefzPlanDetails->start_from_surat,
                                        'startAyah' => $studentHefzPlanDetails->start_from_ayat,
                                        'lastSurahId' => $studentHefzPlanDetails->to_surat,
                                        'lastAyah' => $studentHefzPlanDetails->to_ayat,
                                        'lastAyah2' => $studentHefzPlanDetails->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                    ));


                                    if (($plannedNumberofPages[0]->first_page == $plannedNumberofPages[0]->last_page) && ($plannedNumberofPages[0]->first_page > 0 && $plannedNumberofPages[0]->last_page > 0)) {
                                        $plannedNumberofPagesSum += 1;

                                    } else {

                                        $plannedNumberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                            'startSurahId' => $studentHefzPlanDetails->start_from_surat,
                                            'startAyah' => $studentHefzPlanDetails->start_from_ayat,
                                            'lastSurahId' => $studentHefzPlanDetails->to_surat,
                                            'lastAyah' => $studentHefzPlanDetails->to_ayat,
                                            'lastAyah2' => $studentHefzPlanDetails->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                        ));


                                        $plannedNumberofPagesSum += $plannedNumberofPages[0]->pageCount;

                                    }

                                } else {


                                    $plannedNumberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                        'startSurahId' => $studentHefzPlanDetails->start_from_surat,
                                        'startAyah' => $studentHefzPlanDetails->start_from_ayat,
                                        'lastSurahId' => $studentHefzPlanDetails->to_surat,
                                        'lastAyah' => $studentHefzPlanDetails->to_ayat,
                                        'lastAyah2' => $studentHefzPlanDetails->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                                    ));
                                    $plannedNumberofPagesSum += $plannedNumberofPages[0]->pageCount;


                                }
                            }
                        }
                    }

                    if (empty($memorizedNumberofPagesSum) || is_null($memorizedNumberofPagesSum)) {
//                            $result = "No revision so far";
                        $result = 0;
                    } elseif (empty($plannedNumberofPagesSum) || is_null($plannedNumberofPagesSum)) {
//                            $result = "No plan available";
                        $result = 0;
                    } else {
                        $actual_percentage = round(($memorizedNumberofPagesSum / $memorizedNumberofPagesSum * 100),2);
                        $expected_percentage = 100;
                        $result = min($actual_percentage, $expected_percentage);

                    }

                    return $result;

                    return round($memorizedNumberofPagesSum / $plannedNumberofPagesSum * 100, 2);
                })
                ->
                make(true);
        } catch
        (\Exception $e) {
            // Handle the exception

            dd($e->getMessage());
            \Log::error($e->getMessage());
            return response()->json(['error' => 'An error occurred while loading the data for the table.'], 500);
        }
//    }
    }

}

<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::group(['middleware' => ['web', 'auth:employee','missedClockOutMiddleware'], 'prefix' => 'workplace/menumanage', 'namespace' => 'Modules\MenuManage\Http\Controllers'], function () {

//Route::prefix('menumanage')->group(function() {
//  Route::name('menumanage.')->middleware('auth')->group(function () {
  	Route::get('/', 'MenuManageController@index')->name('index');
  	Route::post('menu-store','MenuManageController@store')->name('store.menu');

  	Route::get('sidebar-edit/{edit}', 'MenuManageController@edit')->name('edit');   
    Route::post('sidebar-update', 'MenuManageController@update')->name('update');  

  	Route::get('manage', 'MenuManageController@manage')->name('manage');
  	Route::post('sidebar-store','MenuManageController@storeSidebar')->name('store.sidebar');
	Route::get('sidebar-store','MenuManageController@storeSidebar')->name('store.sidebar');
	Route::get('reset','MenuManageController@reset')->name('reset');
//});
});
 

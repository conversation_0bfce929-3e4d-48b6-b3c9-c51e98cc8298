<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Student;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

final class HalaqahStudentsWithRecordsController extends Controller
{
    /**
     * Get students who have hefz records for the specified class and month-year
     * 
     * @param $classId
     * @param $monthYear
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudents($classId, $monthYear, Request $request): JsonResponse
    {
        try {
            // Parse and validate class IDs (support comma-separated list)
            $classIds = array_map('trim', explode(',', $classId));
            $classIds = array_map('intval', $classIds);
            $classIds = array_filter($classIds, function($id) { return $id > 0; });

            if (empty($classIds)) {
                return response()->json([
                    'error' => 'No valid class IDs provided',
                    'message' => 'At least one valid class ID is required'
                ], 400);
            }

            // Parse and validate month-year
            $parts = explode(' ', $monthYear);
            if (count($parts) !== 2) {
                return response()->json([
                    'error' => 'Invalid month-year format',
                    'message' => 'Month-year must be in format "Month YYYY"'
                ], 400);
            }

            $monthName = $parts[0];
            $year = (int) $parts[1];

            try {
                $monthNumber = Carbon::parse($monthName . ' 1')->month;
            } catch (\Exception $e) {
                return response()->json([
                    'error' => 'Invalid month name: ' . $monthName,
                    'message' => 'Month name must be a valid English month name'
                ], 400);
            }

            // CRITICAL: Students must meet BOTH conditions:
            // 1. Have an ACTIVE plan (memorization OR revision) with required fields for the month-year
            // 2. Have a report (memorization OR revision) with required fields for the month-year
            
            $students = Student::query()->where(function ($query) use ($classIds, $year, $monthNumber) {
                // Condition 1: Student must have an ACTIVE PLAN (memorization OR revision)
                $query->where(function ($planQuery) use ($classIds, $year, $monthNumber) {
                    // MEMORIZATION PLANS
                    $planQuery->whereHas('hefzPlans', function ($subQ) use ($classIds, $year, $monthNumber) {
                        $subQ->whereIn('class_id', $classIds)
                             ->where('status', 'active')
                             ->whereYear('created_at', $year)
                             ->whereMonth('created_at', $monthNumber)
                             ->whereNotNull('start_from_surat')
                             ->whereNotNull('start_from_ayat')
                             ->whereNotNull('to_surat')
                             ->whereNotNull('to_ayat');
                    })
                    // OR REVISION PLANS
                    ->orWhereHas('revisionPlans', function ($subQ) use ($classIds, $year, $monthNumber) {
                        $subQ->whereIn('class_id', $classIds)
                             ->where('status', 'active')
                             ->whereYear('created_at', $year)
                             ->whereMonth('created_at', $monthNumber)
                             ->whereNotNull('start_from_surat')
                             ->whereNotNull('start_from_ayat')
                             ->whereNotNull('to_surat')
                             ->whereNotNull('to_ayat');
                    });
                });
                
                // Condition 2: Student must have a REPORT (memorization OR revision)
                $query->where(function ($reportQuery) use ($classIds, $year, $monthNumber) {
                    // MEMORIZATION REPORTS
                    $reportQuery->whereHas('hefz', function ($subQ) use ($classIds, $year, $monthNumber) {
                        $subQ->whereIn('class_id', $classIds)
                             ->whereYear('created_at', $year)
                             ->whereMonth('created_at', $monthNumber)
                             ->whereNotNull('hefz_from_surat')
                             ->whereNotNull('hefz_from_ayat')
                             ->whereNotNull('hefz_to_surat')
                             ->whereNotNull('hefz_to_ayat');
                    })
                    // OR REVISION REPORTS
                    ->orWhereHas('revision', function ($subQ) use ($classIds, $year, $monthNumber) {
                        $subQ->whereIn('class_id', $classIds)
                             ->whereYear('created_at', $year)
                             ->whereMonth('created_at', $monthNumber)
                             ->whereNotNull('revision_from_surat')
                             ->whereNotNull('revision_from_ayat')
                             ->whereNotNull('revision_to_surat')
                             ->whereNotNull('revision_to_ayat');
                    });
                });
            })
            ->select('id', 'full_name')
            ->distinct()
            ->orderBy('full_name')
            ->get();

            \Log::info('Students with active plans and reports found', [
                'class_ids' => $classIds,
                'month_year' => $monthYear,
                'count' => $students->count(),
                'student_ids' => $students->pluck('id')->toArray()
            ]);

            return response()->json($students);
            
        } catch (\Exception $e) {
            \Log::error('Error fetching students with active plans and reports: ' . $e->getMessage(), [
                'class_id' => $classId ?? 'unknown',
                'month_year' => $monthYear ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'error' => 'Failed to fetch students',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}

<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;
use \Illuminate\Support\Str;

class Admission extends Model
{

    use SoftDeletes;


    protected $fillable = ['display_name'
        , 'student_id'
        , 'deleted_at'
        , 'guardian_email'
        , 'organization_id'
        , 'center_id'
        , 'class_id'
        , 'created_by'
        , 'creator_role'
        , 'status'
        , 'created_at'
        , 'updated_at'
        , 'gender_id'
        , 'guardian_id'
        , 'date_of_birth'
        , 'student_mobile'
        , 'how_do_know_us'
        , 'age'
        , 'program_id'
        , 'guardian_name'
        , 'student_email'
        , 'rejected_status_note'
        , 'application_recieved_creation_email'
        , 'application_recieved_creation_email_sent_at'
        , 'supervisor_notification_email'
        , 'supervisor_notification_sent_at'
        , 'offer_letter_issuance_date'
        , 'start_date'
        , 'added_from'
    ];

    public function finalizeAdmission()
    {
        Log::info('Finalizing admission', ['id' => $this->id]);

        $this->status = 'active';
        $this->start_date = date('Y-m-d');
        $this->save();
        Log::info('Admission finalized', ['id' => $this->id, 'saved' => $saved]);



    }

    protected static function booted()
    {
        static::saved(function ($model) {
            \Cache::forget('missing_data_count');
        });

        static::deleted(function ($model) {
            \Cache::forget('missing_data_count');
        });
    }

    public function scopePlanWaitingApproval($query)
    {


        return $query->whereHas('student.hefz_plans', function ($q) {
            $q->where('student_hefz_plans.status', 'waiting_for_approval');
        });
    }

    public function scopeArchivedStudents($query)
    {

        return $query->whereHas('student', function ($q) {
            $q->where('status', 'waiting_for_approval');
        });
    }

    public function scopeStudentFullName($query, $fullname)
    {

        return $query->whereHas('student', function ($q) use ($fullname) {

            $q->where('full_name', 'like', "%" . $fullname . "%")->orWhere('full_name_trans', 'like', "%" . $fullname . "%");
        });
    }

    // TODO
    public function scopeAgeBetween($query, $minAge, $maxAge)
    {


        return $query->whereBetween('date_of_birth', [$minAge, $maxAge]);

    }


    public function getFullNameAttribute($value)
    {

        return ucwords($value);
    }

    public function getDisplayNameAttribute($value)
    {

        return ucwords($value);
    }


    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);

        static::deleting(function ($model) {
            if (!self::$deletingFlag) {
                self::$deletingFlag = true;
                $model->status = 'archived';
                $model->save();
                self::$deletingFlag = false;
            }
        });
    }


    public function class()
    {
        return $this->belongsTo('App\Classes');
    }

    public function center()
    {
        return $this->belongsTo('App\Center');
    }


    public function student()
    {
        return $this->belongsTo('App\Student', 'student_id');
    }


    public function archivedStudents()
    {
        return $this->belongsTo('App\Student', 'student_id')->onlyTrashed();
    }

    public function allStudents()
    {
        return $this->belongsTo('App\Student', 'student_id')->withTrashed();
    }
    public function program()
    {
        return $this->belongsTo('App\Program','program_id', 'id' );
    }
    public function programs()
    {
        return $this->belongsToMany('App\Program', 'admission_program')->withTimestamps();
    }

    public function interviews()
    {
        return $this->hasMany('App\AdmissionInterview');
    }

    public function translations()
    {
        return $this->hasMany('App\AdmissionInterview');
    }

    public function orientation()
    {
        return $this->hasOne('App\AdmissionOrientation');
    }

    public function gender(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo('App\BaseSetup', 'gender_id', 'id');
    }


//    public function getStatusAttribute($value)
//    {
//
//
////        return ucfirst(\Illuminate\Support\Str::studly($value));
//        switch ($this->attributes['status']) {
//
//
//            case "accepted":
//                $value = "Accepted";
//                break;
//            case "active":
//                $value = "Active";
//                break;
//            case "new_admission":
//                $value = "New Admission";
////                $value = "New Application Waiting For Approval";
//                break;
//            case "profile_completed":
//                $value = "Profile Completed";
//                break;
//            case "update_guardian":
//                $value = "Update Guardian";
//                break;
//            case "offered":
//                $value = "Offered";
//                break;
//            case "waiting_for_interview":
//                $value = "Waiting for Interview";
//                break;
//            case "archived":
//                $value = "Archived";
//                break;
//            case "preparing_for_orientation":
//                $value = "preparing_for_orientation";
//                break;
//            case "rejected":
//                $value = "Rejected";
//                break;
//
//        }
////        return $this->attributes['name'] = $value;
//        return $this->attributes['status'] = $value;
//
//
//    }



//    /**
//     * written by hashmat Waziri on 19/Sep/2020 - Accessor
//     *
//     * @param $value
//     * @return string
//     */
//    public function getStatusAttribute($value)
//    {
//
//        switch ($this->attributes['status']) {
//
//
//            case "active":
//                $value = "Active";
//                break;
//            case "new":
//                $value = "New Admission";
//                break;
//            case "profile_completed":
//                $value = "Profile Completed";
//                break;
//            case "waiting_for_interview":
//
//                $value = "Waiting For Interview";
////                $value =  \Auth::user()->roles()->first()->guard_name  == "employee"?
////                    "Waiting for Interview":
////
////                    is_null($this->interviews->pluck("confirmed_at")[0]) ? "Waiting for Interview - Please accept interview from email or click here if you have not accepted yet" :
////                'Waiting for Interview - Thanks for the confirmation. Be ready for the interview"';
//
//                break;
//            case "update_guardian":
//                $value = "Update Guardian";
//                break;
//
//            case "offered":
//                $value = "Offered";
//                break;
//            case "rejected":
//                $value = "Rejected";
//                break;
//            case "accepted":
//                $value = "Accepted";
//                break;
//
//                case "reapplication":
//                $value = "Reapplication";
//                break;
//            default:
//                $value = "Not Defined";
//        }
////        return $this->attributes['name'] = $value;
//        return $this->attributes['status'] = $value;
//
//
//    }
//    public function getStatusAttribute($value)
//    {
//
//
//
//        return str_replace('_',' ',Str::title($value));
//    }

    public function getGenderAttribute()
    {
        return $this->student->gender();
    }


}

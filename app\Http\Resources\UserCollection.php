<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;
use phpDocumentor\Reflection\Types\Parent_;

class UserCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
//        return parent::toArray($request);
        return parent::toArray([
            'data' => $this->collection,
            'links' => [
                'self' => 'link-value',
            ]
            //            'email' => $this->email,
//            'username' => $this->username,
//            'phone' => $this->phone,
//            'password' => $this->password,
//            'role_id' => $this->role_id,
//            'full_name' => $this->full_name,
//            'organization_id' => $this->organization_id,
//            'is_administrator' => $this->is_administrator,
//            'display_name' => $this->display_name,
//            'full_name_trans' => $this->full_name_trans,
//            'access_status' => $this->access_status,
//            'nationality' => $this->nationality,
//            'address_1' => $this->address_1,
//            'address_2' => $this->address_2,
//            'state' => $this->state,
//            'zip_code' => $this->zip_code,
//            'gender' => $this->gender,
//            'email_verified_at' => $this->email_verified_at
        ]);
    }

}

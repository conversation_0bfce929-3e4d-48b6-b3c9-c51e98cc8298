!function(){"use strict";var t,e,n,i,r,o,s=tinymce.util.Tools.resolve("tinymce.ThemeManager"),a=tinymce.util.Tools.resolve("tinymce.Env"),l=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),u=tinymce.util.Tools.resolve("tinymce.util.Delay"),c=function(t){return t.reduce(function(t,e){return Array.isArray(e)?t.concat(c(e)):t.concat(e)},[])},d={flatten:c},f=function(t,e){for(var n=0;n<e.length;n++){var i=(0,e[n])(t);if(i)return i}return null},h=function(t,e){return{id:t,rect:e}},m=function(t){return{x:t.left,y:t.top,w:t.width,h:t.height}},g=function(t){return{left:t.x,top:t.y,width:t.w,height:t.h,right:t.x+t.w,bottom:t.y+t.h}},p=function(t){var e=l.DOM.getViewPort();return{x:t.x+e.x,y:t.y+e.y,w:t.w,h:t.h}},v=function(t){var e=t.getBoundingClientRect();return p({x:e.left,y:e.top,w:Math.max(t.clientWidth,t.offsetWidth),h:Math.max(t.clientHeight,t.offsetHeight)})},y=function(t,e){return v(e)},b=function(t){return v(t.getContentAreaContainer()||t.getBody())},x=function(t){var e=t.selection.getBoundingClientRect();return e?p(m(e)):null},w=function(t,e){return function(n){for(var i=0;i<e.length;i++)if(e[i].predicate(t))return h(e[i].id,y(n,t));return null}},_=function(t,e){return function(n){for(var i=0;i<t.length;i++)for(var r=0;r<e.length;r++)if(e[r].predicate(t[i]))return h(e[r].id,y(n,t[i]));return null}},R=tinymce.util.Tools.resolve("tinymce.util.Tools"),C=function(t,e){return{id:t,predicate:e}},k=function(t){return R.map(t,function(t){return C(t.id,t.predicate)})},E=function(t){return function(e){return e.selection.isCollapsed()?null:h(t,x(e))}},H=function(t,e){return function(n){var i,r=n.schema.getTextBlockElements();for(i=0;i<t.length;i++)if("TABLE"===t[i].nodeName)return null;for(i=0;i<t.length;i++)if(t[i].nodeName in r)return n.dom.isEmpty(t[i])?h(e,x(n)):null;return null}},S=function(t){t.fire("SkinLoaded")},M=function(t){return t.fire("BeforeRenderUI")},T=tinymce.util.Tools.resolve("tinymce.EditorManager"),W=function(t){return function(e){return typeof e===t}},P=function(t){return Array.isArray(t)},N=function(t){return W("string")(t)},O=function(t){return W("number")(t)},D=function(t){return W("boolean")(t)},A=function(t){return W("function")(t)},B=(W("object"),P),L=function(t,e){if(e(t))return!0;throw new Error("Default value doesn't match requested type.")},z=function(t){return function(e,n,i){var r=e.settings;return L(i,t),n in r&&t(r[n])?r[n]:i}},I={getStringOr:z(N),getBoolOr:z(D),getNumberOr:z(O),getHandlerOr:z(A),getToolbarItemsOr:(t=B,function(e,n,i){var r,o,s,a,l,u=n in e.settings?e.settings[n]:i;return L(i,t),o=i,B(r=u)?r:N(r)?"string"==typeof(a=r)?(l=/[ ,]/,a.split(l).filter(function(t){return t.length>0})):a:D(r)?(s=o,!1===r?[]:s):o})},F=tinymce.util.Tools.resolve("tinymce.geom.Rect"),V=function(t,e){return{rect:t,position:e}},U=function(t,e){return{x:e.x,y:e.y,w:t.w,h:t.h}},q=function(t,e,n,i,r){var o,s,a,l={x:i.x,y:i.y,w:i.w+(i.w<r.w+n.w?r.w:0),h:i.h+(i.h<r.h+n.h?r.h:0)};return o=F.findBestRelativePosition(r,n,l,t),n=F.clamp(n,l),o?(s=F.relativePosition(r,n,o),a=U(r,s),V(a,o)):(n=F.intersect(l,n))?(o=F.findBestRelativePosition(r,n,l,e))?(s=F.relativePosition(r,n,o),a=U(r,s),V(a,o)):(a=U(r,n),V(a,o)):null},j=function(t,e,n){return q(["cr-cl","cl-cr"],["bc-tc","bl-tl","br-tr"],t,e,n)},$=function(t,e,n){return q(["tc-bc","bc-tc","tl-bl","bl-tl","tr-br","br-tr","cr-cl","cl-cr"],["bc-tc","bl-tl","br-tr","cr-cl"],t,e,n)},Y=function(t,e,n,i){var r;return"function"==typeof t?(r=t({elementRect:g(e),contentAreaRect:g(n),panelRect:g(i)}),m(r)):i},X=function(t){return t.panelRect},J=function(t){return I.getToolbarItemsOr(t,"selection_toolbar",["bold","italic","|","quicklink","h2","h3","blockquote"])},G=function(t){return I.getToolbarItemsOr(t,"insert_toolbar",["quickimage","quicktable"])},K=function(t){return I.getHandlerOr(t,"inline_toolbar_position_handler",X)},Z=function(t){var e,n,i,r,o=t.settings;return o.skin_url?(i=t,r=o.skin_url,i.documentBaseURI.toAbsolute(r)):(e=o.skin,n=T.baseURL+"/skins/",e?n+e:n+"lightgray")},Q=function(t){return!1===t.settings.skin},tt=function(t,e){var n=Z(t),i=function(){var n,i,r;i=e,r=function(){n._skinLoaded=!0,S(n),i()},(n=t).initialized?r():n.on("init",r)};Q(t)?i():(l.DOM.styleSheetLoader.load(n+"/skin.min.css",i),t.contentCSS.push(n+"/content.inline.min.css"))},et=function(t){var e,n,i,r,o=t.contextToolbars;return d.flatten([o||[],(e=t,n="img",i="image",r="alignleft aligncenter alignright",{predicate:function(t){return e.dom.is(t,n)},id:i,items:r})])},nt=function(t,e){var n,i,r,o,s;return s=(o=t).selection.getNode(),i=o.dom.getParents(s),r=k(e),(n=f(t,[w(i[0],r),E("text"),H(i,"insert"),_(i,r)]))&&n.rect?n:null},it=function(t,e){return function(){var n,i,r;t.removed||(r=t,document.activeElement!==r.getBody())||(n=et(t),(i=nt(t,n))?e.show(t,i.id,i.rect,n):e.hide())}},rt=function(t,e){var n,i,r,o,s,c=u.throttle(it(t,e),0),d=u.throttle((n=t,i=e,r=it(t,e),function(){n.removed||i.inForm()||r()}),0),f=(o=t,s=e,function(){var t=et(o),e=nt(o,t);e&&s.reposition(o,e.id,e.rect)});t.on("blur hide ObjectResizeStart",e.hide),t.on("click",c),t.on("nodeChange mouseup",d),t.on("ResizeEditor keyup",c),t.on("ResizeWindow",f),l.DOM.bind(a.container,"scroll",f),t.on("remove",function(){l.DOM.unbind(a.container,"scroll",f),e.remove()}),t.shortcuts.add("Alt+F10,F10","",e.focus)},ot=function(t,e){return tt(t,function(){var n,i;rt(t,e),i=e,(n=t).shortcuts.remove("meta+k"),n.shortcuts.add("meta+k","",function(){var t=et(n),e=f(n,[E("quicklink")]);e&&i.show(n,e.id,e.rect,t)})}),{}},st=function(t,e){return t.inline?ot(t,e):function(t){throw new Error(t)}("inlite theme only supports inline mode.")},at=function(t){return function(){return t}},lt={noop:function(){},noarg:function(t){return function(){return t()}},compose:function(t,e){return function(){return t(e.apply(null,arguments))}},constant:at,identity:function(t){return t},tripleEquals:function(t,e){return t===e},curry:function(t){for(var e=new Array(arguments.length-1),n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];var r=e.concat(n);return t.apply(null,r)}},not:function(t){return function(){return!t.apply(null,arguments)}},die:function(t){return function(){throw new Error(t)}},apply:function(t){return t()},call:function(t){t()},never:at(!1),always:at(!0)},ut=lt.never,ct=lt.always,dt=function(){return ft},ft=(r={fold:function(t,e){return t()},is:ut,isSome:ut,isNone:ct,getOr:i=function(t){return t},getOrThunk:n=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},or:i,orThunk:n,map:dt,ap:dt,each:function(){},bind:dt,flatten:dt,exists:ut,forall:ct,filter:dt,equals:e=function(t){return t.isNone()},equals_:e,toArray:function(){return[]},toString:lt.constant("none()")},Object.freeze&&Object.freeze(r),r),ht=function(t){var e=function(){return t},n=function(){return r},i=function(e){return e(t)},r={fold:function(e,n){return n(t)},is:function(e){return t===e},isSome:ct,isNone:ut,getOr:e,getOrThunk:e,getOrDie:e,or:n,orThunk:n,map:function(e){return ht(e(t))},ap:function(e){return e.fold(dt,function(e){return ht(e(t))})},each:function(e){e(t)},bind:i,flatten:e,exists:i,forall:i,filter:function(e){return e(t)?r:ft},equals:function(e){return e.is(t)},equals_:function(e,n){return e.fold(ut,function(e){return n(t,e)})},toArray:function(){return[t]},toString:function(){return"some("+t+")"}};return r},mt={some:ht,none:dt,from:function(t){return null===t||t===undefined?ft:ht(t)}},gt=(o=Array.prototype.indexOf)===undefined?function(t,e){return _t(t,e)}:function(t,e){return o.call(t,e)},pt=function(t,e){return gt(t,e)>-1},vt=function(t,e){for(var n=t.length,i=new Array(n),r=0;r<n;r++){var o=t[r];i[r]=e(o,r,t)}return i},yt=function(t,e){for(var n=0,i=t.length;n<i;n++)e(t[n],n,t)},bt=function(t,e){for(var n=t.length-1;n>=0;n--)e(t[n],n,t)},xt=function(t,e){for(var n=[],i=0,r=t.length;i<r;i++){var o=t[i];e(o,i,t)&&n.push(o)}return n},wt=function(t,e){for(var n=0,i=t.length;n<i;n++)if(e(t[n],n,t))return mt.some(n);return mt.none()},_t=function(t,e){for(var n=0,i=t.length;n<i;++n)if(t[n]===e)return n;return-1},Rt=Array.prototype.push,Ct=function(t){for(var e=[],n=0,i=t.length;n<i;++n){if(!Array.prototype.isPrototypeOf(t[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+t);Rt.apply(e,t[n])}return e},kt=function(t,e){for(var n=0,i=t.length;n<i;++n)if(!0!==e(t[n],n,t))return!1;return!0},Et=Array.prototype.slice,Ht={map:vt,each:yt,eachr:bt,partition:function(t,e){for(var n=[],i=[],r=0,o=t.length;r<o;r++){var s=t[r];(e(s,r,t)?n:i).push(s)}return{pass:n,fail:i}},filter:xt,groupBy:function(t,e){if(0===t.length)return[];for(var n=e(t[0]),i=[],r=[],o=0,s=t.length;o<s;o++){var a=t[o],l=e(a);l!==n&&(i.push(r),r=[]),n=l,r.push(a)}return 0!==r.length&&i.push(r),i},indexOf:function(t,e){var n=gt(t,e);return-1===n?mt.none():mt.some(n)},foldr:function(t,e,n){return bt(t,function(t){n=e(n,t)}),n},foldl:function(t,e,n){return yt(t,function(t){n=e(n,t)}),n},find:function(t,e){for(var n=0,i=t.length;n<i;n++){var r=t[n];if(e(r,n,t))return mt.some(r)}return mt.none()},findIndex:wt,flatten:Ct,bind:function(t,e){var n=vt(t,e);return Ct(n)},forall:kt,exists:function(t,e){return wt(t,e).isSome()},contains:pt,equal:function(t,e){return t.length===e.length&&kt(t,function(t,n){return t===e[n]})},reverse:function(t){var e=Et.call(t,0);return e.reverse(),e},chunk:function(t,e){for(var n=[],i=0;i<t.length;i+=e){var r=t.slice(i,i+e);n.push(r)}return n},difference:function(t,e){return xt(t,function(t){return!pt(e,t)})},mapToObject:function(t,e){for(var n={},i=0,r=t.length;i<r;i++){var o=t[i];n[String(o)]=e(o,i)}return n},pure:function(t){return[t]},sort:function(t,e){var n=Et.call(t,0);return n.sort(e),n},range:function(t,e){for(var n=[],i=0;i<t;i++)n.push(e(i));return n},head:function(t){return 0===t.length?mt.none():mt.some(t[0])},last:function(t){return 0===t.length?mt.none():mt.some(t[t.length-1])}},St=0,Mt={id:function(){return"mceu_"+St++},create:function(t,e,n){var i=document.createElement(t);return l.DOM.setAttribs(i,e),"string"==typeof n?i.innerHTML=n:R.each(n,function(t){t.nodeType&&i.appendChild(t)}),i},createFragment:function(t){return l.DOM.createFragment(t)},getWindowSize:function(){return l.DOM.getViewPort()},getSize:function(t){var e,n;if(t.getBoundingClientRect){var i=t.getBoundingClientRect();e=Math.max(i.width||i.right-i.left,t.offsetWidth),n=Math.max(i.height||i.bottom-i.bottom,t.offsetHeight)}else e=t.offsetWidth,n=t.offsetHeight;return{width:e,height:n}},getPos:function(t,e){return l.DOM.getPos(t,e||Mt.getContainer())},getContainer:function(){return a.container?a.container:document.body},getViewPort:function(t){return l.DOM.getViewPort(t)},get:function(t){return document.getElementById(t)},addClass:function(t,e){return l.DOM.addClass(t,e)},removeClass:function(t,e){return l.DOM.removeClass(t,e)},hasClass:function(t,e){return l.DOM.hasClass(t,e)},toggleClass:function(t,e,n){return l.DOM.toggleClass(t,e,n)},css:function(t,e,n){return l.DOM.setStyle(t,e,n)},getRuntimeStyle:function(t,e){return l.DOM.getStyle(t,e,!0)},on:function(t,e,n,i){return l.DOM.bind(t,e,n,i)},off:function(t,e,n){return l.DOM.unbind(t,e,n)},fire:function(t,e,n){return l.DOM.fire(t,e,n)},innerHtml:function(t,e){l.DOM.setHTML(t,e)}},Tt=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),Wt=tinymce.util.Tools.resolve("tinymce.util.Class"),Pt=tinymce.util.Tools.resolve("tinymce.util.EventDispatcher"),Nt=function(t){var e;if(t)return"number"==typeof t?{top:t=t||0,left:t,bottom:t,right:t}:(1===(e=(t=t.split(" ")).length)?t[1]=t[2]=t[3]=t[0]:2===e?(t[2]=t[0],t[3]=t[1]):3===e&&(t[3]=t[1]),{top:parseInt(t[0],10)||0,right:parseInt(t[1],10)||0,bottom:parseInt(t[2],10)||0,left:parseInt(t[3],10)||0})},Ot=function(t,e){function n(e){var n=parseFloat(function(e){var n=t.ownerDocument.defaultView;if(n){var i=n.getComputedStyle(t,null);return i?(e=e.replace(/[A-Z]/g,function(t){return"-"+t}),i.getPropertyValue(e)):null}return t.currentStyle[e]}(e));return isNaN(n)?0:n}return{top:n(e+"TopWidth"),right:n(e+"RightWidth"),bottom:n(e+"BottomWidth"),left:n(e+"LeftWidth")}};function Dt(){}function At(t){this.cls=[],this.cls._map={},this.onchange=t||Dt,this.prefix=""}R.extend(At.prototype,{add:function(t){return t&&!this.contains(t)&&(this.cls._map[t]=!0,this.cls.push(t),this._change()),this},remove:function(t){if(this.contains(t)){var e=void 0;for(e=0;e<this.cls.length&&this.cls[e]!==t;e++);this.cls.splice(e,1),delete this.cls._map[t],this._change()}return this},toggle:function(t,e){var n=this.contains(t);return n!==e&&(n?this.remove(t):this.add(t),this._change()),this},contains:function(t){return!!this.cls._map[t]},_change:function(){delete this.clsValue,this.onchange.call(this)}}),At.prototype.toString=function(){var t;if(this.clsValue)return this.clsValue;t="";for(var e=0;e<this.cls.length;e++)e>0&&(t+=" "),t+=this.prefix+this.cls[e];return t};var Bt,Lt,zt,It=/^([\w\\*]+)?(?:#([\w\-\\]+))?(?:\.([\w\\\.]+))?(?:\[\@?([\w\\]+)([\^\$\*!~]?=)([\w\\]+)\])?(?:\:(.+))?/i,Ft=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g,Vt=/^\s*|\s*$/g,Ut=Wt.extend({init:function(t){var e=this.match;function n(t,n,r){var o;function s(t){t&&n.push(t)}return s(function(t){if(t)return t=t.toLowerCase(),function(e){return"*"===t||e.type===t}}((o=It.exec(t.replace(Vt,"")))[1])),s(function(t){if(t)return function(e){return e._name===t}}(o[2])),s(function(t){if(t)return t=t.split("."),function(e){for(var n=t.length;n--;)if(!e.classes.contains(t[n]))return!1;return!0}}(o[3])),s(function(t,e,n){if(t)return function(i){var r=i[t]?i[t]():"";return e?"="===e?r===n:"*="===e?r.indexOf(n)>=0:"~="===e?(" "+r+" ").indexOf(" "+n+" ")>=0:"!="===e?r!==n:"^="===e?0===r.indexOf(n):"$="===e&&r.substr(r.length-n.length)===n:!!n}}(o[4],o[5],o[6])),s(function(t){var n;if(t)return(t=/(?:not\((.+)\))|(.+)/i.exec(t))[1]?(n=i(t[1],[]),function(t){return!e(t,n)}):(t=t[2],function(e,n,i){return"first"===t?0===n:"last"===t?n===i-1:"even"===t?n%2==0:"odd"===t?n%2==1:!!e[t]&&e[t]()})}(o[7])),n.pseudo=!!o[7],n.direct=r,n}function i(t,e){var r,o,s,a=[];do{if(Ft.exec(""),(o=Ft.exec(t))&&(t=o[3],a.push(o[1]),o[2])){r=o[3];break}}while(o);for(r&&i(r,e),t=[],s=0;s<a.length;s++)">"!==a[s]&&t.push(n(a[s],[],">"===a[s-1]));return e.push(t),e}this._selectors=i(t,[])},match:function(t,e){var n,i,r,o,s,a,l,u,c,d,f,h,m;for(n=0,i=(e=e||this._selectors).length;n<i;n++){for(m=t,h=0,r=(o=(s=e[n]).length)-1;r>=0;r--)for(u=s[r];m;){if(u.pseudo)for(c=d=(f=m.parent().items()).length;c--&&f[c]!==m;);for(a=0,l=u.length;a<l;a++)if(!u[a](m,c,d)){a=l+1;break}if(a===l){h++;break}if(r===o-1)break;m=m.parent()}if(h===o)return!0}return!1},find:function(t){var e,n,i=[],r=this._selectors;function o(t,e,n){var r,s,a,l,u,c=e[n];for(r=0,s=t.length;r<s;r++){for(u=t[r],a=0,l=c.length;a<l;a++)if(!c[a](u,r,s)){a=l+1;break}if(a===l)n===e.length-1?i.push(u):u.items&&o(u.items(),e,n+1);else if(c.direct)return;u.items&&o(u.items(),e,n)}}if(t.items){for(e=0,n=r.length;e<n;e++)o(t.items(),r[e],0);n>1&&(i=function(t){for(var e,n=[],i=t.length;i--;)(e=t[i]).__checked||(n.push(e),e.__checked=1);for(i=n.length;i--;)delete n[i].__checked;return n}(i))}return Bt||(Bt=Ut.Collection),new Bt(i)}}),qt=Array.prototype.push,jt=Array.prototype.slice;zt={length:0,init:function(t){t&&this.add(t)},add:function(t){return R.isArray(t)?qt.apply(this,t):t instanceof Lt?this.add(t.toArray()):qt.call(this,t),this},set:function(t){var e,n=this,i=n.length;for(n.length=0,n.add(t),e=n.length;e<i;e++)delete n[e];return n},filter:function(t){var e,n,i,r,o=[];for("string"==typeof t?(t=new Ut(t),r=function(e){return t.match(e)}):r=t,e=0,n=this.length;e<n;e++)r(i=this[e])&&o.push(i);return new Lt(o)},slice:function(){return new Lt(jt.apply(this,arguments))},eq:function(t){return-1===t?this.slice(t):this.slice(t,+t+1)},each:function(t){return R.each(this,t),this},toArray:function(){return R.toArray(this)},indexOf:function(t){for(var e=this.length;e--&&this[e]!==t;);return e},reverse:function(){return new Lt(R.toArray(this).reverse())},hasClass:function(t){return!!this[0]&&this[0].classes.contains(t)},prop:function(t,e){var n;return e!==undefined?(this.each(function(n){n[t]&&n[t](e)}),this):(n=this[0])&&n[t]?n[t]():void 0},exec:function(t){var e=R.toArray(arguments).slice(1);return this.each(function(n){n[t]&&n[t].apply(n,e)}),this},remove:function(){for(var t=this.length;t--;)this[t].remove();return this},addClass:function(t){return this.each(function(e){e.classes.add(t)})},removeClass:function(t){return this.each(function(e){e.classes.remove(t)})}},R.each("fire on off show hide append prepend before after reflow".split(" "),function(t){zt[t]=function(){var e=R.toArray(arguments);return this.each(function(n){t in n&&n[t].apply(n,e)}),this}}),R.each("text name disabled active selected checked visible parent value data".split(" "),function(t){zt[t]=function(e){return this.prop(t,e)}}),Lt=Wt.extend(zt),Ut.Collection=Lt;var $t=Lt,Yt=function(t){this.create=t.create};Yt.create=function(t,e){return new Yt({create:function(n,i){var r,o=function(t){n.set(i,t.value)};return n.on("change:"+i,function(n){t.set(e,n.value)}),t.on("change:"+e,o),(r=n._bindings)||(r=n._bindings=[],n.on("destroy",function(){for(var t=r.length;t--;)r[t]()})),r.push(function(){t.off("change:"+e,o)}),t.get(e)}})};var Xt=tinymce.util.Tools.resolve("tinymce.util.Observable");function Jt(t){return t.nodeType>0}var Gt,Kt,Zt=Wt.extend({Mixins:[Xt],init:function(t){var e,n;for(e in t=t||{})(n=t[e])instanceof Yt&&(t[e]=n.create(this,e));this.data=t},set:function(t,e){var n,i,r=this.data[t];if(e instanceof Yt&&(e=e.create(this,t)),"object"==typeof t){for(n in t)this.set(n,t[n]);return this}return function o(t,e){var n,i;if(t===e)return!0;if(null===t||null===e)return t===e;if("object"!=typeof t||"object"!=typeof e)return t===e;if(R.isArray(e)){if(t.length!==e.length)return!1;for(n=t.length;n--;)if(!o(t[n],e[n]))return!1}if(Jt(t)||Jt(e))return t===e;for(n in i={},e){if(!o(t[n],e[n]))return!1;i[n]=!0}for(n in t)if(!i[n]&&!o(t[n],e[n]))return!1;return!0}(r,e)||(this.data[t]=e,i={target:this,name:t,value:e,oldValue:r},this.fire("change:"+t,i),this.fire("change",i)),this},get:function(t){return this.data[t]},has:function(t){return t in this.data},bind:function(t){return Yt.create(this,t)},destroy:function(){this.fire("destroy")}}),Qt={},te={add:function(t){var e=t.parent();if(e){if(!e._layout||e._layout.isNative())return;Qt[e._id]||(Qt[e._id]=e),Gt||(Gt=!0,u.requestAnimationFrame(function(){var t,e;for(t in Gt=!1,Qt)(e=Qt[t]).state.get("rendered")&&e.reflow();Qt={}},document.body))}},remove:function(t){Qt[t._id]&&delete Qt[t._id]}},ee=function(t){return t?t.getRoot().uiContainer:null},ne={getUiContainerDelta:function(t){var e=ee(t);if(e&&"static"!==l.DOM.getStyle(e,"position",!0)){var n=l.DOM.getPos(e),i=e.scrollLeft-n.x,r=e.scrollTop-n.y;return mt.some({x:i,y:r})}return mt.none()},setUiContainer:function(t,e){var n=l.DOM.select(t.settings.ui_container)[0];e.getRoot().uiContainer=n},getUiContainer:ee,inheritUiContainer:function(t,e){return e.uiContainer=ee(t)}},ie="onmousewheel"in document,re=!1,oe=0,se={Statics:{classPrefix:"mce-"},isRtl:function(){return Kt.rtl},classPrefix:"mce-",init:function(t){var e,n,i=this;function r(t){var e;for(t=t.split(" "),e=0;e<t.length;e++)i.classes.add(t[e])}i.settings=t=R.extend({},i.Defaults,t),i._id=t.id||"mceu_"+oe++,i._aria={role:t.role},i._elmCache={},i.$=Tt,i.state=new Zt({visible:!0,active:!1,disabled:!1,value:""}),i.data=new Zt(t.data),i.classes=new At(function(){i.state.get("rendered")&&(i.getEl().className=this.toString())}),i.classes.prefix=i.classPrefix,(e=t.classes)&&(i.Defaults&&(n=i.Defaults.classes)&&e!==n&&r(n),r(e)),R.each("title text name visible disabled active value".split(" "),function(e){e in t&&i[e](t[e])}),i.on("click",function(){if(i.disabled())return!1}),i.settings=t,i.borderBox=Nt(t.border),i.paddingBox=Nt(t.padding),i.marginBox=Nt(t.margin),t.hidden&&i.hide()},Properties:"parent,name",getContainerElm:function(){var t=ne.getUiContainer(this);return t||Mt.getContainer()},getParentCtrl:function(t){for(var e,n=this.getRoot().controlIdLookup;t&&n&&!(e=n[t.id]);)t=t.parentNode;return e},initLayoutRect:function(){var t,e,n,i,r,o,s,a,l,u,c=this,d=c.settings,f=c.getEl();t=c.borderBox=c.borderBox||Ot(f,"border"),c.paddingBox=c.paddingBox||Ot(f,"padding"),c.marginBox=c.marginBox||Ot(f,"margin"),u=Mt.getSize(f),a=d.minWidth,l=d.minHeight,r=a||u.width,o=l||u.height,n=d.width,i=d.height,s=void 0!==(s=d.autoResize)?s:!n&&!i,n=n||r,i=i||o;var h=t.left+t.right,m=t.top+t.bottom,g=d.maxWidth||65535,p=d.maxHeight||65535;return c._layoutRect=e={x:d.x||0,y:d.y||0,w:n,h:i,deltaW:h,deltaH:m,contentW:n-h,contentH:i-m,innerW:n-h,innerH:i-m,startMinWidth:a||0,startMinHeight:l||0,minW:Math.min(r,g),minH:Math.min(o,p),maxW:g,maxH:p,autoResize:s,scrollW:0},c._lastLayoutRect={},e},layoutRect:function(t){var e,n,i,r,o,s=this,a=s._layoutRect;return a||(a=s.initLayoutRect()),t?(i=a.deltaW,r=a.deltaH,t.x!==undefined&&(a.x=t.x),t.y!==undefined&&(a.y=t.y),t.minW!==undefined&&(a.minW=t.minW),t.minH!==undefined&&(a.minH=t.minH),(n=t.w)!==undefined&&(n=(n=n<a.minW?a.minW:n)>a.maxW?a.maxW:n,a.w=n,a.innerW=n-i),(n=t.h)!==undefined&&(n=(n=n<a.minH?a.minH:n)>a.maxH?a.maxH:n,a.h=n,a.innerH=n-r),(n=t.innerW)!==undefined&&(n=(n=n<a.minW-i?a.minW-i:n)>a.maxW-i?a.maxW-i:n,a.innerW=n,a.w=n+i),(n=t.innerH)!==undefined&&(n=(n=n<a.minH-r?a.minH-r:n)>a.maxH-r?a.maxH-r:n,a.innerH=n,a.h=n+r),t.contentW!==undefined&&(a.contentW=t.contentW),t.contentH!==undefined&&(a.contentH=t.contentH),(e=s._lastLayoutRect).x===a.x&&e.y===a.y&&e.w===a.w&&e.h===a.h||((o=Kt.repaintControls)&&o.map&&!o.map[s._id]&&(o.push(s),o.map[s._id]=!0),e.x=a.x,e.y=a.y,e.w=a.w,e.h=a.h),s):a},repaint:function(){var t,e,n,i,r,o,s,a,l,u,c=this;l=document.createRange?function(t){return t}:Math.round,t=c.getEl().style,i=c._layoutRect,a=c._lastRepaintRect||{},o=(r=c.borderBox).left+r.right,s=r.top+r.bottom,i.x!==a.x&&(t.left=l(i.x)+"px",a.x=i.x),i.y!==a.y&&(t.top=l(i.y)+"px",a.y=i.y),i.w!==a.w&&(u=l(i.w-o),t.width=(u>=0?u:0)+"px",a.w=i.w),i.h!==a.h&&(u=l(i.h-s),t.height=(u>=0?u:0)+"px",a.h=i.h),c._hasBody&&i.innerW!==a.innerW&&(u=l(i.innerW),(n=c.getEl("body"))&&((e=n.style).width=(u>=0?u:0)+"px"),a.innerW=i.innerW),c._hasBody&&i.innerH!==a.innerH&&(u=l(i.innerH),(n=n||c.getEl("body"))&&((e=e||n.style).height=(u>=0?u:0)+"px"),a.innerH=i.innerH),c._lastRepaintRect=a,c.fire("repaint",{},!1)},updateLayoutRect:function(){var t=this;t.parent()._lastRect=null,Mt.css(t.getEl(),{width:"",height:""}),t._layoutRect=t._lastRepaintRect=t._lastLayoutRect=null,t.initLayoutRect()},on:function(t,e){var n,i,r,o=this;return ae(o).on(t,"string"!=typeof(n=e)?n:function(t){return i||o.parentsAndSelf().each(function(t){var e=t.settings.callbacks;if(e&&(i=e[n]))return r=t,!1}),i?i.call(r,t):(t.action=n,void this.fire("execute",t))}),o},off:function(t,e){return ae(this).off(t,e),this},fire:function(t,e,n){if((e=e||{}).control||(e.control=this),e=ae(this).fire(t,e),!1!==n&&this.parent)for(var i=this.parent();i&&!e.isPropagationStopped();)i.fire(t,e,!1),i=i.parent();return e},hasEventListeners:function(t){return ae(this).has(t)},parents:function(t){var e,n=new $t;for(e=this.parent();e;e=e.parent())n.add(e);return t&&(n=n.filter(t)),n},parentsAndSelf:function(t){return new $t(this).add(this.parents(t))},next:function(){var t=this.parent().items();return t[t.indexOf(this)+1]},prev:function(){var t=this.parent().items();return t[t.indexOf(this)-1]},innerHtml:function(t){return this.$el.html(t),this},getEl:function(t){var e=t?this._id+"-"+t:this._id;return this._elmCache[e]||(this._elmCache[e]=Tt("#"+e)[0]),this._elmCache[e]},show:function(){return this.visible(!0)},hide:function(){return this.visible(!1)},focus:function(){try{this.getEl().focus()}catch(t){}return this},blur:function(){return this.getEl().blur(),this},aria:function(t,e){var n=this,i=n.getEl(n.ariaTarget);return void 0===e?n._aria[t]:(n._aria[t]=e,n.state.get("rendered")&&i.setAttribute("role"===t?t:"aria-"+t,e),n)},encode:function(t,e){return!1!==e&&(t=this.translate(t)),(t||"").replace(/[&<>"]/g,function(t){return"&#"+t.charCodeAt(0)+";"})},translate:function(t){return Kt.translate?Kt.translate(t):t},before:function(t){var e=this.parent();return e&&e.insert(t,e.items().indexOf(this),!0),this},after:function(t){var e=this.parent();return e&&e.insert(t,e.items().indexOf(this)),this},remove:function(){var t,e,n=this,i=n.getEl(),r=n.parent();if(n.items){var o=n.items().toArray();for(e=o.length;e--;)o[e].remove()}r&&r.items&&(t=[],r.items().each(function(e){e!==n&&t.push(e)}),r.items().set(t),r._lastRect=null),n._eventsRoot&&n._eventsRoot===n&&Tt(i).off();var s=n.getRoot().controlIdLookup;return s&&delete s[n._id],i&&i.parentNode&&i.parentNode.removeChild(i),n.state.set("rendered",!1),n.state.destroy(),n.fire("remove"),n},renderBefore:function(t){return Tt(t).before(this.renderHtml()),this.postRender(),this},renderTo:function(t){return Tt(t||this.getContainerElm()).append(this.renderHtml()),this.postRender(),this},preRender:function(){},render:function(){},renderHtml:function(){return'<div id="'+this._id+'" class="'+this.classes+'"></div>'},postRender:function(){var t,e,n,i,r,o=this,s=o.settings;for(i in o.$el=Tt(o.getEl()),o.state.set("rendered",!0),s)0===i.indexOf("on")&&o.on(i.substr(2),s[i]);if(o._eventsRoot){for(n=o.parent();!r&&n;n=n.parent())r=n._eventsRoot;if(r)for(i in r._nativeEvents)o._nativeEvents[i]=!0}le(o),s.style&&(t=o.getEl())&&(t.setAttribute("style",s.style),t.style.cssText=s.style),o.settings.border&&(e=o.borderBox,o.$el.css({"border-top-width":e.top,"border-right-width":e.right,"border-bottom-width":e.bottom,"border-left-width":e.left}));var a=o.getRoot();for(var l in a.controlIdLookup||(a.controlIdLookup={}),a.controlIdLookup[o._id]=o,o._aria)o.aria(l,o._aria[l]);!1===o.state.get("visible")&&(o.getEl().style.display="none"),o.bindStates(),o.state.on("change:visible",function(t){var e,n=t.value;o.state.get("rendered")&&(o.getEl().style.display=!1===n?"none":"",o.getEl().getBoundingClientRect()),(e=o.parent())&&(e._lastRect=null),o.fire(n?"show":"hide"),te.add(o)}),o.fire("postrender",{},!1)},bindStates:function(){},scrollIntoView:function(t){var e,n,i,r,o,s,a=this.getEl(),l=a.parentNode,u=function(t,e){var n,i,r=t;for(n=i=0;r&&r!==e&&r.nodeType;)n+=r.offsetLeft||0,i+=r.offsetTop||0,r=r.offsetParent;return{x:n,y:i}}(a,l);return e=u.x,n=u.y,i=a.offsetWidth,r=a.offsetHeight,o=l.clientWidth,s=l.clientHeight,"end"===t?(e-=o-i,n-=s-r):"center"===t&&(e-=o/2-i/2,n-=s/2-r/2),l.scrollLeft=e,l.scrollTop=n,this},getRoot:function(){for(var t,e=this,n=[];e;){if(e.rootControl){t=e.rootControl;break}n.push(e),t=e,e=e.parent()}t||(t=this);for(var i=n.length;i--;)n[i].rootControl=t;return t},reflow:function(){te.remove(this);var t=this.parent();return t&&t._layout&&!t._layout.isNative()&&t.reflow(),this}};function ae(t){return t._eventDispatcher||(t._eventDispatcher=new Pt({scope:t,toggleEvent:function(e,n){n&&Pt.isNative(e)&&(t._nativeEvents||(t._nativeEvents={}),t._nativeEvents[e]=!0,t.state.get("rendered")&&le(t))}})),t._eventDispatcher}function le(t){var e,n,i,r,o,s;function a(e){var n=t.getParentCtrl(e.target);n&&n.fire(e.type,e)}function l(){var t=r._lastHoverCtrl;t&&(t.fire("mouseleave",{target:t.getEl()}),t.parents().each(function(t){t.fire("mouseleave",{target:t.getEl()})}),r._lastHoverCtrl=null)}function u(e){var n,i,o,s=t.getParentCtrl(e.target),a=r._lastHoverCtrl,l=0;if(s!==a){if(r._lastHoverCtrl=s,(i=s.parents().toArray().reverse()).push(s),a){for((o=a.parents().toArray().reverse()).push(a),l=0;l<o.length&&i[l]===o[l];l++);for(n=o.length-1;n>=l;n--)(a=o[n]).fire("mouseleave",{target:a.getEl()})}for(n=l;n<i.length;n++)(s=i[n]).fire("mouseenter",{target:s.getEl()})}}function c(e){e.preventDefault(),"mousewheel"===e.type?(e.deltaY=-.025*e.wheelDelta,e.wheelDeltaX&&(e.deltaX=-.025*e.wheelDeltaX)):(e.deltaX=0,e.deltaY=e.detail),e=t.fire("wheel",e)}if(o=t._nativeEvents){for((i=t.parents().toArray()).unshift(t),e=0,n=i.length;!r&&e<n;e++)r=i[e]._eventsRoot;for(r||(r=i[i.length-1]||t),t._eventsRoot=r,n=e,e=0;e<n;e++)i[e]._eventsRoot=r;var d=r._delegates;for(s in d||(d=r._delegates={}),o){if(!o)return!1;"wheel"!==s||re?("mouseenter"===s||"mouseleave"===s?r._hasMouseEnter||(Tt(r.getEl()).on("mouseleave",l).on("mouseover",u),r._hasMouseEnter=1):d[s]||(Tt(r.getEl()).on(s,a),d[s]=!0),o[s]=!1):ie?Tt(t.getEl()).on("mousewheel",c):Tt(t.getEl()).on("DOMMouseScroll",c)}}}R.each("text title visible disabled active value".split(" "),function(t){se[t]=function(e){return 0===arguments.length?this.state.get(t):(void 0!==e&&this.state.set(t,e),this)}});var ue=Kt=Wt.extend(se),ce=function(t){return"static"===Mt.getRuntimeStyle(t,"position")},de=function(t){return t.state.get("fixed")};function fe(t,e,n){var i,r,o,s,a,l,u,c,d,f;return d=he(),o=(r=Mt.getPos(e,ne.getUiContainer(t))).x,s=r.y,de(t)&&ce(document.body)&&(o-=d.x,s-=d.y),i=t.getEl(),a=(f=Mt.getSize(i)).width,l=f.height,u=(f=Mt.getSize(e)).width,c=f.height,"b"===(n=(n||"").split(""))[0]&&(s+=c),"r"===n[1]&&(o+=u),"c"===n[0]&&(s+=Math.round(c/2)),"c"===n[1]&&(o+=Math.round(u/2)),"b"===n[3]&&(s-=l),"r"===n[4]&&(o-=a),"c"===n[3]&&(s-=Math.round(l/2)),"c"===n[4]&&(o-=Math.round(a/2)),{x:o,y:s,w:a,h:l}}var he=function(){var t=window,e=Math.max(t.pageXOffset,document.body.scrollLeft,document.documentElement.scrollLeft),n=Math.max(t.pageYOffset,document.body.scrollTop,document.documentElement.scrollTop);return{x:e,y:n,w:e+(t.innerWidth||document.documentElement.clientWidth),h:n+(t.innerHeight||document.documentElement.clientHeight)}},me=function(t){var e,n=ne.getUiContainer(t);return n&&!de(t)?{x:0,y:0,w:(e=n).scrollWidth-1,h:e.scrollHeight-1}:he()},ge={testMoveRel:function(t,e){for(var n=me(this),i=0;i<e.length;i++){var r=fe(this,t,e[i]);if(de(this)){if(r.x>0&&r.x+r.w<n.w&&r.y>0&&r.y+r.h<n.h)return e[i]}else if(r.x>n.x&&r.x+r.w<n.w&&r.y>n.y&&r.y+r.h<n.h)return e[i]}return e[0]},moveRel:function(t,e){"string"!=typeof e&&(e=this.testMoveRel(t,e));var n=fe(this,t,e);return this.moveTo(n.x,n.y)},moveBy:function(t,e){var n=this.layoutRect();return this.moveTo(n.x+t,n.y+e),this},moveTo:function(t,e){var n=this;function i(t,e,n){return t<0?0:t+n>e&&(t=e-n)<0?0:t}if(n.settings.constrainToViewport){var r=me(this),o=n.layoutRect();t=i(t,r.w,o.w),e=i(e,r.h,o.h)}var s=ne.getUiContainer(n);return s&&ce(s)&&!de(n)&&(t-=s.scrollLeft,e-=s.scrollTop),s&&(t+=1,e+=1),n.state.get("rendered")?n.layoutRect({x:t,y:e}).repaint():(n.settings.x=t,n.settings.y=e),n.fire("move",{x:t,y:e}),n}},pe=ue.extend({Mixins:[ge],Defaults:{classes:"widget tooltip tooltip-n"},renderHtml:function(){var t=this,e=t.classPrefix;return'<div id="'+t._id+'" class="'+t.classes+'" role="presentation"><div class="'+e+'tooltip-arrow"></div><div class="'+e+'tooltip-inner">'+t.encode(t.state.get("text"))+"</div></div>"},bindStates:function(){var t=this;return t.state.on("change:text",function(e){t.getEl().lastChild.innerHTML=t.encode(e.value)}),t._super()},repaint:function(){var t,e;t=this.getEl().style,e=this._layoutRect,t.left=e.x+"px",t.top=e.y+"px",t.zIndex=131070}}),ve=ue.extend({init:function(t){var e=this;e._super(t),t=e.settings,e.canFocus=!0,t.tooltip&&!1!==ve.tooltips&&(e.on("mouseenter",function(n){var i=e.tooltip().moveTo(-65535);if(n.control===e){var r=i.text(t.tooltip).show().testMoveRel(e.getEl(),["bc-tc","bc-tl","bc-tr"]);i.classes.toggle("tooltip-n","bc-tc"===r),i.classes.toggle("tooltip-nw","bc-tl"===r),i.classes.toggle("tooltip-ne","bc-tr"===r),i.moveRel(e.getEl(),r)}else i.hide()}),e.on("mouseleave mousedown click",function(){e.tooltip().remove(),e._tooltip=null})),e.aria("label",t.ariaLabel||t.tooltip)},tooltip:function(){return this._tooltip||(this._tooltip=new pe({type:"tooltip"}),ne.inheritUiContainer(this,this._tooltip),this._tooltip.renderTo()),this._tooltip},postRender:function(){var t=this,e=t.settings;t._super(),t.parent()||!e.width&&!e.height||(t.initLayoutRect(),t.repaint()),e.autofocus&&t.focus()},bindStates:function(){var t=this;function e(e){t.aria("disabled",e),t.classes.toggle("disabled",e)}function n(e){t.aria("pressed",e),t.classes.toggle("active",e)}return t.state.on("change:disabled",function(t){e(t.value)}),t.state.on("change:active",function(t){n(t.value)}),t.state.get("disabled")&&e(!0),t.state.get("active")&&n(!0),t._super()},remove:function(){this._super(),this._tooltip&&(this._tooltip.remove(),this._tooltip=null)}}),ye=ve.extend({Defaults:{value:0},init:function(t){this._super(t),this.classes.add("progress"),this.settings.filter||(this.settings.filter=function(t){return Math.round(t)})},renderHtml:function(){var t=this._id,e=this.classPrefix;return'<div id="'+t+'" class="'+this.classes+'"><div class="'+e+'bar-container"><div class="'+e+'bar"></div></div><div class="'+e+'text">0%</div></div>'},postRender:function(){return this._super(),this.value(this.settings.value),this},bindStates:function(){var t=this;function e(e){e=t.settings.filter(e),t.getEl().lastChild.innerHTML=e+"%",t.getEl().firstChild.firstChild.style.width=e+"%"}return t.state.on("change:value",function(t){e(t.value)}),e(t.state.get("value")),t._super()}}),be=function(t,e){t.getEl().lastChild.textContent=e+(t.progressBar?" "+t.progressBar.value()+"%":"")},xe=ue.extend({Mixins:[ge],Defaults:{classes:"widget notification"},init:function(t){var e=this;e._super(t),e.maxWidth=t.maxWidth,t.text&&e.text(t.text),t.icon&&(e.icon=t.icon),t.color&&(e.color=t.color),t.type&&e.classes.add("notification-"+t.type),t.timeout&&(t.timeout<0||t.timeout>0)&&!t.closeButton?e.closeButton=!1:(e.classes.add("has-close"),e.closeButton=!0),t.progressBar&&(e.progressBar=new ye),e.on("click",function(t){-1!==t.target.className.indexOf(e.classPrefix+"close")&&e.close()})},renderHtml:function(){var t,e=this,n=e.classPrefix,i="",r="",o="";return e.icon&&(i='<i class="'+n+"ico "+n+"i-"+e.icon+'"></i>'),t=' style="max-width: '+e.maxWidth+"px;"+(e.color?"background-color: "+e.color+';"':'"'),e.closeButton&&(r='<button type="button" class="'+n+'close" aria-hidden="true">\xd7</button>'),e.progressBar&&(o=e.progressBar.renderHtml()),'<div id="'+e._id+'" class="'+e.classes+'"'+t+' role="presentation">'+i+'<div class="'+n+'notification-inner">'+e.state.get("text")+"</div>"+o+r+'<div style="clip: rect(1px, 1px, 1px, 1px);height: 1px;overflow: hidden;position: absolute;width: 1px;" aria-live="assertive" aria-relevant="additions" aria-atomic="true"></div></div>'},postRender:function(){var t=this;return u.setTimeout(function(){t.$el.addClass(t.classPrefix+"in"),be(t,t.state.get("text"))},100),t._super()},bindStates:function(){var t=this;return t.state.on("change:text",function(e){t.getEl().firstChild.innerHTML=e.value,be(t,e.value)}),t.progressBar&&(t.progressBar.bindStates(),t.progressBar.state.on("change:value",function(e){be(t,t.state.get("text"))})),t._super()},close:function(){return this.fire("close").isDefaultPrevented()||this.remove(),this},repaint:function(){var t,e;t=this.getEl().style,e=this._layoutRect,t.left=e.x+"px",t.top=e.y+"px",t.zIndex=65534}});function we(t){var e=function(t){return t.inline?t.getElement():t.getContentAreaContainer()};return{open:function(n,i){var r,o=R.extend(n,{maxWidth:(r=e(t),Mt.getSize(r).width)}),s=new xe(o);return s.args=o,o.timeout>0&&(s.timer=setTimeout(function(){s.close(),i()},o.timeout)),s.on("close",function(){i()}),s.renderTo(),s},close:function(t){t.close()},reposition:function(n){var i;i=n,Ht.each(i,function(t){t.moveTo(0,0)}),function(n){if(n.length>0){var i=n.slice(0,1)[0],r=e(t);i.moveRel(r,"tc-tc"),Ht.each(n,function(t,e){e>0&&t.moveRel(n[e-1].getEl(),"bc-tc")})}}(n)},getArgs:function(t){return t.args}}}function _e(t){var e,n;if(t.changedTouches)for(e="screenX screenY pageX pageY clientX clientY".split(" "),n=0;n<e.length;n++)t[e[n]]=t.changedTouches[0][e[n]]}function Re(t,e){var n,i,r,o,s,a,l,u=e.document||document;e=e||{};var c=u.getElementById(e.handle||t);r=function(t){var r,d,f,h,m,g,p,v,y,b,x,w=(r=u,y=Math.max,d=r.documentElement,f=r.body,h=y(d.scrollWidth,f.scrollWidth),m=y(d.clientWidth,f.clientWidth),g=y(d.offsetWidth,f.offsetWidth),p=y(d.scrollHeight,f.scrollHeight),v=y(d.clientHeight,f.clientHeight),{width:h<g?m:h,height:p<y(d.offsetHeight,f.offsetHeight)?v:p});_e(t),t.preventDefault(),i=t.button,b=c,a=t.screenX,l=t.screenY,x=window.getComputedStyle?window.getComputedStyle(b,null).getPropertyValue("cursor"):b.runtimeStyle.cursor,n=Tt("<div></div>").css({position:"absolute",top:0,left:0,width:w.width,height:w.height,zIndex:2147483647,opacity:1e-4,cursor:x}).appendTo(u.body),Tt(u).on("mousemove touchmove",s).on("mouseup touchend",o),e.start(t)},s=function(t){if(_e(t),t.button!==i)return o(t);t.deltaX=t.screenX-a,t.deltaY=t.screenY-l,t.preventDefault(),e.drag(t)},o=function(t){_e(t),Tt(u).off("mousemove touchmove",s).off("mouseup touchend",o),n.remove(),e.stop&&e.stop(t)},this.destroy=function(){Tt(c).off()},Tt(c).on("mousedown touchstart",r)}var Ce=tinymce.util.Tools.resolve("tinymce.ui.Factory"),ke=function(t){return!!t.getAttribute("data-mce-tabstop")};function Ee(t){var e,n,i=t.root;function r(t){return t&&1===t.nodeType}try{e=document.activeElement}catch(b){e=document.body}function o(t){return r(t=t||e)?t.getAttribute("role"):null}function s(t){for(var n,i=t||e;i=i.parentNode;)if(n=o(i))return n}function a(t){var n=e;if(r(n))return n.getAttribute("aria-"+t)}function l(t){var e=t.tagName.toUpperCase();return"INPUT"===e||"TEXTAREA"===e||"SELECT"===e}function u(t){var e=[];return function n(t){if(1===t.nodeType&&"none"!==t.style.display&&!t.disabled){var i;(l(i=t)&&!i.hidden||ke(i)||/^(button|menuitem|checkbox|tab|menuitemcheckbox|option|gridcell|slider)$/.test(o(i)))&&e.push(t);for(var r=0;r<t.childNodes.length;r++)n(t.childNodes[r])}}(t||i.getEl()),e}function c(t){var e,i;(i=(t=t||n).parents().toArray()).unshift(t);for(var r=0;r<i.length&&!(e=i[r]).settings.ariaRoot;r++);return e}function d(t,e){return t<0?t=e.length-1:t>=e.length&&(t=0),e[t]&&e[t].focus(),t}function f(t,n){var i=-1,r=c();n=n||u(r.getEl());for(var o=0;o<n.length;o++)n[o]===e&&(i=o);i+=t,r.lastAriaIndex=d(i,n)}function h(){"tablist"===s()?f(-1,u(e.parentNode)):n.parent().submenu?v():f(-1)}function m(){var t=o(),n=s();"tablist"===n?f(1,u(e.parentNode)):"menuitem"===t&&"menu"===n&&a("haspopup")?y():f(1)}function g(){f(-1)}function p(){var t=o(),e=s();"menuitem"===t&&"menubar"===e?y():"button"===t&&a("haspopup")?y({key:"down"}):f(1)}function v(){n.fire("cancel")}function y(t){t=t||{},n.fire("click",{target:e,aria:t})}return n=i.getParentCtrl(e),i.on("keydown",function(t){function i(t,n){l(e)||ke(e)||"slider"!==o(e)&&!1!==n(t)&&t.preventDefault()}if(!t.isDefaultPrevented())switch(t.keyCode){case 37:i(t,h);break;case 39:i(t,m);break;case 38:i(t,g);break;case 40:i(t,p);break;case 27:v();break;case 14:case 13:case 32:i(t,y);break;case 9:!function(t){if("tablist"===s()){var e=u(n.getEl("body"))[0];e&&e.focus()}else f(t.shiftKey?-1:1)}(t),t.preventDefault()}}),i.on("focusin",function(t){e=t.target,n=t.control}),{focusFirst:function(t){var e=c(t),n=u(e.getEl());e.settings.ariaRemember&&"lastAriaIndex"in e?d(e.lastAriaIndex,n):d(0,n)}}}var He,Se,Me,Te,We={},Pe=ue.extend({init:function(t){var e=this;e._super(t),(t=e.settings).fixed&&e.state.set("fixed",!0),e._items=new $t,e.isRtl()&&e.classes.add("rtl"),e.bodyClasses=new At(function(){e.state.get("rendered")&&(e.getEl("body").className=this.toString())}),e.bodyClasses.prefix=e.classPrefix,e.classes.add("container"),e.bodyClasses.add("container-body"),t.containerCls&&e.classes.add(t.containerCls),e._layout=Ce.create((t.layout||"")+"layout"),e.settings.items?e.add(e.settings.items):e.add(e.render()),e._hasBody=!0},items:function(){return this._items},find:function(t){return(t=We[t]=We[t]||new Ut(t)).find(this)},add:function(t){return this.items().add(this.create(t)).parent(this),this},focus:function(t){var e,n,i,r=this;if(!t||!(n=r.keyboardNav||r.parents().eq(-1)[0].keyboardNav))return i=r.find("*"),r.statusbar&&i.add(r.statusbar.items()),i.each(function(t){if(t.settings.autofocus)return e=null,!1;t.canFocus&&(e=e||t)}),e&&e.focus(),r;n.focusFirst(r)},replace:function(t,e){for(var n,i=this.items(),r=i.length;r--;)if(i[r]===t){i[r]=e;break}r>=0&&((n=e.getEl())&&n.parentNode.removeChild(n),(n=t.getEl())&&n.parentNode.removeChild(n)),e.parent(this)},create:function(t){var e,n=this,i=[];return R.isArray(t)||(t=[t]),R.each(t,function(t){t&&(t instanceof ue||("string"==typeof t&&(t={type:t}),e=R.extend({},n.settings.defaults,t),t.type=e.type=e.type||t.type||n.settings.defaultType||(e.defaults?e.defaults.type:null),t=Ce.create(e)),i.push(t))}),i},renderNew:function(){var t=this;return t.items().each(function(e,n){var i;e.parent(t),e.state.get("rendered")||((i=t.getEl("body")).hasChildNodes()&&n<=i.childNodes.length-1?Tt(i.childNodes[n]).before(e.renderHtml()):Tt(i).append(e.renderHtml()),e.postRender(),te.add(e))}),t._layout.applyClasses(t.items().filter(":visible")),t._lastRect=null,t},append:function(t){return this.add(t).renderNew()},prepend:function(t){return this.items().set(this.create(t).concat(this.items().toArray())),this.renderNew()},insert:function(t,e,n){var i,r,o;return t=this.create(t),i=this.items(),!n&&e<i.length-1&&(e+=1),e>=0&&e<i.length&&(r=i.slice(0,e).toArray(),o=i.slice(e).toArray(),i.set(r.concat(t,o))),this.renderNew()},fromJSON:function(t){for(var e in t)this.find("#"+e).value(t[e]);return this},toJSON:function(){var t={};return this.find("*").each(function(e){var n=e.name(),i=e.value();n&&void 0!==i&&(t[n]=i)}),t},renderHtml:function(){var t=this,e=t._layout,n=this.settings.role;return t.preRender(),e.preRender(t),'<div id="'+t._id+'" class="'+t.classes+'"'+(n?' role="'+this.settings.role+'"':"")+'><div id="'+t._id+'-body" class="'+t.bodyClasses+'">'+(t.settings.html||"")+e.renderHtml(t)+"</div></div>"},postRender:function(){var t,e=this;return e.items().exec("postRender"),e._super(),e._layout.postRender(e),e.state.set("rendered",!0),e.settings.style&&e.$el.css(e.settings.style),e.settings.border&&(t=e.borderBox,e.$el.css({"border-top-width":t.top,"border-right-width":t.right,"border-bottom-width":t.bottom,"border-left-width":t.left})),e.parent()||(e.keyboardNav=Ee({root:e})),e},initLayoutRect:function(){var t=this._super();return this._layout.recalc(this),t},recalc:function(){var t=this,e=t._layoutRect,n=t._lastRect;if(!n||n.w!==e.w||n.h!==e.h)return t._layout.recalc(t),e=t.layoutRect(),t._lastRect={x:e.x,y:e.y,w:e.w,h:e.h},!0},reflow:function(){var t;if(te.remove(this),this.visible()){for(ue.repaintControls=[],ue.repaintControls.map={},this.recalc(),t=ue.repaintControls.length;t--;)ue.repaintControls[t].repaint();"flow"!==this.settings.layout&&"stack"!==this.settings.layout&&this.repaint(),ue.repaintControls=[]}return this}}),Ne={init:function(){this.on("repaint",this.renderScroll)},renderScroll:function(){var t=this,e=2;function n(){var n,i,r;function o(r,o,s,a,l,u){var c,d,f,h,m,g,p,v;if(d=t.getEl("scroll"+r)){if(p=o.toLowerCase(),v=s.toLowerCase(),Tt(t.getEl("absend")).css(p,t.layoutRect()[a]-1),!l)return void Tt(d).css("display","none");Tt(d).css("display","block"),c=t.getEl("body"),f=t.getEl("scroll"+r+"t"),h=c["client"+s]-2*e,m=(h-=n&&i?d["client"+u]:0)/c["scroll"+s],(g={})[p]=c["offset"+o]+e,g[v]=h,Tt(d).css(g),(g={})[p]=c["scroll"+o]*m,g[v]=h*m,Tt(f).css(g)}}r=t.getEl("body"),n=r.scrollWidth>r.clientWidth,i=r.scrollHeight>r.clientHeight,o("h","Left","Width","contentW",n,"Height"),o("v","Top","Height","contentH",i,"Width")}t.settings.autoScroll&&(t._hasScroll||(t._hasScroll=!0,function(){function n(n,i,r,o,s){var a,l=t._id+"-scroll"+n,u=t.classPrefix;Tt(t.getEl()).append('<div id="'+l+'" class="'+u+"scrollbar "+u+"scrollbar-"+n+'"><div id="'+l+'t" class="'+u+'scrollbar-thumb"></div></div>'),t.draghelper=new Re(l+"t",{start:function(){a=t.getEl("body")["scroll"+i],Tt("#"+l).addClass(u+"active")},drag:function(l){var u,c,d,f,h=t.layoutRect();c=h.contentW>h.innerW,d=h.contentH>h.innerH,f=t.getEl("body")["client"+r]-2*e,u=(f-=c&&d?t.getEl("scroll"+n)["client"+s]:0)/t.getEl("body")["scroll"+r],t.getEl("body")["scroll"+i]=a+l["delta"+o]/u},stop:function(){Tt("#"+l).removeClass(u+"active")}})}t.classes.add("scroll"),n("v","Top","Height","Y","Width"),n("h","Left","Width","X","Height")}(),t.on("wheel",function(e){var i=t.getEl("body");i.scrollLeft+=10*(e.deltaX||0),i.scrollTop+=10*e.deltaY,n()}),Tt(t.getEl("body")).on("scroll",n)),n())}},Oe=Pe.extend({Defaults:{layout:"fit",containerCls:"panel"},Mixins:[Ne],renderHtml:function(){var t=this,e=t._layout,n=t.settings.html;return t.preRender(),e.preRender(t),void 0===n?n='<div id="'+t._id+'-body" class="'+t.bodyClasses+'">'+e.renderHtml(t)+"</div>":("function"==typeof n&&(n=n.call(t)),t._hasBody=!1),'<div id="'+t._id+'" class="'+t.classes+'" hidefocus="1" tabindex="-1" role="group">'+(t._preBodyHtml||"")+n+"</div>"}}),De={resizeToContent:function(){this._layoutRect.autoResize=!0,this._lastRect=null,this.reflow()},resizeTo:function(t,e){if(t<=1||e<=1){var n=Mt.getWindowSize();t=t<=1?t*n.w:t,e=e<=1?e*n.h:e}return this._layoutRect.autoResize=!1,this.layoutRect({minW:t,minH:e,w:t,h:e}).reflow()},resizeBy:function(t,e){var n=this.layoutRect();return this.resizeTo(n.w+t,n.h+e)}},Ae=[],Be=[];function Le(t,e){for(;t;){if(t===e)return!0;t=t.parent()}}function ze(){He||(He=function(t){2!==t.button&&function(t){for(var e=Ae.length;e--;){var n=Ae[e],i=n.getParentCtrl(t.target);if(n.settings.autohide){if(i&&(Le(i,n)||n.parent()===i))continue;(t=n.fire("autohide",{target:t.target})).isDefaultPrevented()||n.hide()}}}(t)},Tt(document).on("click touchstart",He))}function Ie(t){var e=Mt.getViewPort().y;function n(e,n){for(var i,r=0;r<Ae.length;r++)if(Ae[r]!==t)for(i=Ae[r].parent();i&&(i=i.parent());)i===t&&Ae[r].fixed(e).moveBy(0,n).repaint()}t.settings.autofix&&(t.state.get("fixed")?t._autoFixY>e&&(t.fixed(!1).layoutRect({y:t._autoFixY}).repaint(),n(!1,t._autoFixY-e)):(t._autoFixY=t.layoutRect().y,t._autoFixY<e&&(t.fixed(!0).layoutRect({y:0}).repaint(),n(!0,e-t._autoFixY))))}function Fe(t,e){var n,i,r=Ve.zIndex||65535;if(t)Be.push(e);else for(n=Be.length;n--;)Be[n]===e&&Be.splice(n,1);if(Be.length)for(n=0;n<Be.length;n++)Be[n].modal&&(r++,i=Be[n]),Be[n].getEl().style.zIndex=r,Be[n].zIndex=r,r++;var o=Tt("#"+e.classPrefix+"modal-block",e.getContainerElm())[0];i?Tt(o).css("z-index",i.zIndex-1):o&&(o.parentNode.removeChild(o),Te=!1),Ve.currentZIndex=r}var Ve=Oe.extend({Mixins:[ge,De],init:function(t){var e=this;e._super(t),e._eventsRoot=e,e.classes.add("floatpanel"),t.autohide&&(ze(),function(){if(!Me){var t=document.documentElement,e=t.clientWidth,n=t.clientHeight;Me=function(){document.all&&e===t.clientWidth&&n===t.clientHeight||(e=t.clientWidth,n=t.clientHeight,Ve.hideAll())},Tt(window).on("resize",Me)}}(),Ae.push(e)),t.autofix&&(Se||(Se=function(){var t;for(t=Ae.length;t--;)Ie(Ae[t])},Tt(window).on("scroll",Se)),e.on("move",function(){Ie(this)})),e.on("postrender show",function(t){if(t.control===e){var n,i=e.classPrefix;e.modal&&!Te&&((n=Tt("#"+i+"modal-block",e.getContainerElm()))[0]||(n=Tt('<div id="'+i+'modal-block" class="'+i+"reset "+i+'fade"></div>').appendTo(e.getContainerElm())),u.setTimeout(function(){n.addClass(i+"in"),Tt(e.getEl()).addClass(i+"in")}),Te=!0),Fe(!0,e)}}),e.on("show",function(){e.parents().each(function(t){if(t.state.get("fixed"))return e.fixed(!0),!1})}),t.popover&&(e._preBodyHtml='<div class="'+e.classPrefix+'arrow"></div>',e.classes.add("popover").add("bottom").add(e.isRtl()?"end":"start")),e.aria("label",t.ariaLabel),e.aria("labelledby",e._id),e.aria("describedby",e.describedBy||e._id+"-none")},fixed:function(t){var e=this;if(e.state.get("fixed")!==t){if(e.state.get("rendered")){var n=Mt.getViewPort();t?e.layoutRect().y-=n.y:e.layoutRect().y+=n.y}e.classes.toggle("fixed",t),e.state.set("fixed",t)}return e},show:function(){var t,e=this._super();for(t=Ae.length;t--&&Ae[t]!==this;);return-1===t&&Ae.push(this),e},hide:function(){return Ue(this),Fe(!1,this),this._super()},hideAll:function(){Ve.hideAll()},close:function(){return this.fire("close").isDefaultPrevented()||(this.remove(),Fe(!1,this)),this},remove:function(){Ue(this),this._super()},postRender:function(){return this.settings.bodyRole&&this.getEl("body").setAttribute("role",this.settings.bodyRole),this._super()}});function Ue(t){var e;for(e=Ae.length;e--;)Ae[e]===t&&Ae.splice(e,1);for(e=Be.length;e--;)Be[e]===t&&Be.splice(e,1)}Ve.hideAll=function(){for(var t=Ae.length;t--;){var e=Ae[t];e&&e.settings.autohide&&(e.hide(),Ae.splice(t,1))}};var qe=[],je="";function $e(t){var e,n=Tt("meta[name=viewport]")[0];!1!==a.overrideViewPort&&(n||((n=document.createElement("meta")).setAttribute("name","viewport"),document.getElementsByTagName("head")[0].appendChild(n)),(e=n.getAttribute("content"))&&void 0!==je&&(je=e),n.setAttribute("content",t?"width=device-width,initial-scale=1.0,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0":je))}function Ye(t,e){(function(){for(var t=0;t<qe.length;t++)if(qe[t]._fullscreen)return!0;return!1})()&&!1===e&&Tt([document.documentElement,document.body]).removeClass(t+"fullscreen")}var Xe=Ve.extend({modal:!0,Defaults:{border:1,layout:"flex",containerCls:"panel",role:"dialog",callbacks:{submit:function(){this.fire("submit",{data:this.toJSON()})},close:function(){this.close()}}},init:function(t){var e=this;e._super(t),e.isRtl()&&e.classes.add("rtl"),e.classes.add("window"),e.bodyClasses.add("window-body"),e.state.set("fixed",!0),t.buttons&&(e.statusbar=new Oe({layout:"flex",border:"1 0 0 0",spacing:3,padding:10,align:"center",pack:e.isRtl()?"start":"end",defaults:{type:"button"},items:t.buttons}),e.statusbar.classes.add("foot"),e.statusbar.parent(e)),e.on("click",function(t){var n=e.classPrefix+"close";(Mt.hasClass(t.target,n)||Mt.hasClass(t.target.parentNode,n))&&e.close()}),e.on("cancel",function(){e.close()}),e.aria("describedby",e.describedBy||e._id+"-none"),e.aria("label",t.title),e._fullscreen=!1},recalc:function(){var t,e,n,i,r=this,o=r.statusbar;r._fullscreen&&(r.layoutRect(Mt.getWindowSize()),r.layoutRect().contentH=r.layoutRect().innerH),r._super(),t=r.layoutRect(),r.settings.title&&!r._fullscreen&&(e=t.headerW)>t.w&&(n=t.x-Math.max(0,e/2),r.layoutRect({w:e,x:n}),i=!0),o&&(o.layoutRect({w:r.layoutRect().innerW}).recalc(),(e=o.layoutRect().minW+t.deltaW)>t.w&&(n=t.x-Math.max(0,e-t.w),r.layoutRect({w:e,x:n}),i=!0)),i&&r.recalc()},initLayoutRect:function(){var t,e=this,n=e._super(),i=0;if(e.settings.title&&!e._fullscreen){t=e.getEl("head");var r=Mt.getSize(t);n.headerW=r.width,n.headerH=r.height,i+=n.headerH}e.statusbar&&(i+=e.statusbar.layoutRect().h),n.deltaH+=i,n.minH+=i,n.h+=i;var o=Mt.getWindowSize();return n.x=e.settings.x||Math.max(0,o.w/2-n.w/2),n.y=e.settings.y||Math.max(0,o.h/2-n.h/2),n},renderHtml:function(){var t=this,e=t._layout,n=t._id,i=t.classPrefix,r=t.settings,o="",s="",a=r.html;return t.preRender(),e.preRender(t),r.title&&(o='<div id="'+n+'-head" class="'+i+'window-head"><div id="'+n+'-title" class="'+i+'title">'+t.encode(r.title)+'</div><div id="'+n+'-dragh" class="'+i+'dragh"></div><button type="button" class="'+i+'close" aria-hidden="true"><i class="mce-ico mce-i-remove"></i></button></div>'),r.url&&(a='<iframe src="'+r.url+'" tabindex="-1"></iframe>'),void 0===a&&(a=e.renderHtml(t)),t.statusbar&&(s=t.statusbar.renderHtml()),'<div id="'+n+'" class="'+t.classes+'" hidefocus="1"><div class="'+t.classPrefix+'reset" role="application">'+o+'<div id="'+n+'-body" class="'+t.bodyClasses+'">'+a+"</div>"+s+"</div></div>"},fullscreen:function(t){var e,n,i=this,r=document.documentElement,o=i.classPrefix;if(t!==i._fullscreen)if(Tt(window).on("resize",function(){var t;if(i._fullscreen)if(e)i._timer||(i._timer=u.setTimeout(function(){var t=Mt.getWindowSize();i.moveTo(0,0).resizeTo(t.w,t.h),i._timer=0},50));else{t=(new Date).getTime();var n=Mt.getWindowSize();i.moveTo(0,0).resizeTo(n.w,n.h),(new Date).getTime()-t>50&&(e=!0)}}),n=i.layoutRect(),i._fullscreen=t,t){i._initial={x:n.x,y:n.y,w:n.w,h:n.h},i.borderBox=Nt("0"),i.getEl("head").style.display="none",n.deltaH-=n.headerH+2,Tt([r,document.body]).addClass(o+"fullscreen"),i.classes.add("fullscreen");var s=Mt.getWindowSize();i.moveTo(0,0).resizeTo(s.w,s.h)}else i.borderBox=Nt(i.settings.border),i.getEl("head").style.display="",n.deltaH+=n.headerH,Tt([r,document.body]).removeClass(o+"fullscreen"),i.classes.remove("fullscreen"),i.moveTo(i._initial.x,i._initial.y).resizeTo(i._initial.w,i._initial.h);return i.reflow()},postRender:function(){var t,e=this;setTimeout(function(){e.classes.add("in"),e.fire("open")},0),e._super(),e.statusbar&&e.statusbar.postRender(),e.focus(),this.dragHelper=new Re(e._id+"-dragh",{start:function(){t={x:e.layoutRect().x,y:e.layoutRect().y}},drag:function(n){e.moveTo(t.x+n.deltaX,t.y+n.deltaY)}}),e.on("submit",function(t){t.isDefaultPrevented()||e.close()}),qe.push(e),$e(!0)},submit:function(){return this.fire("submit",{data:this.toJSON()})},remove:function(){var t,e=this;for(e.dragHelper.destroy(),e._super(),e.statusbar&&this.statusbar.remove(),Ye(e.classPrefix,!1),t=qe.length;t--;)qe[t]===e&&qe.splice(t,1);$e(qe.length>0)},getContentWindow:function(){var t=this.getEl().getElementsByTagName("iframe")[0];return t?t.contentWindow:null}});!function(){if(!a.desktop){var t={w:window.innerWidth,h:window.innerHeight};u.setInterval(function(){var e=window.innerWidth,n=window.innerHeight;t.w===e&&t.h===n||(t={w:e,h:n},Tt(window).trigger("resize"))},100)}Tt(window).on("resize",function(){var t,e,n=Mt.getWindowSize();for(t=0;t<qe.length;t++)e=qe[t].layoutRect(),qe[t].moveTo(qe[t].settings.x||Math.max(0,n.w/2-e.w/2),qe[t].settings.y||Math.max(0,n.h/2-e.h/2))})}();var Je,Ge=Xe.extend({init:function(t){t={border:1,padding:20,layout:"flex",pack:"center",align:"center",containerCls:"panel",autoScroll:!0,buttons:{type:"button",text:"Ok",action:"ok"},items:{type:"label",multiline:!0,maxWidth:500,maxHeight:200}},this._super(t)},Statics:{OK:1,OK_CANCEL:2,YES_NO:3,YES_NO_CANCEL:4,msgBox:function(t){var e,n=t.callback||function(){};function i(t,e,i){return{type:"button",text:t,subtype:i?"primary":"",onClick:function(t){t.control.parents()[1].close(),n(e)}}}switch(t.buttons){case Ge.OK_CANCEL:e=[i("Ok",!0,!0),i("Cancel",!1)];break;case Ge.YES_NO:case Ge.YES_NO_CANCEL:e=[i("Yes",1,!0),i("No",0)],t.buttons===Ge.YES_NO_CANCEL&&e.push(i("Cancel",-1));break;default:e=[i("Ok",!0,!0)]}return new Xe({padding:20,x:t.x,y:t.y,minWidth:300,minHeight:100,layout:"flex",pack:"center",align:"center",buttons:e,title:t.title,role:"alertdialog",items:{type:"label",multiline:!0,maxWidth:500,maxHeight:200,text:t.text},onPostRender:function(){this.aria("describedby",this.items()[0]._id)},onClose:t.onClose,onCancel:function(){n(!1)}}).renderTo(document.body).reflow()},alert:function(t,e){return"string"==typeof t&&(t={text:t}),t.callback=e,Ge.msgBox(t)},confirm:function(t,e){return"string"==typeof t&&(t={text:t}),t.callback=e,t.buttons=Ge.OK_CANCEL,Ge.msgBox(t)}}}),Ke=function(t,e){return{renderUI:function(){return st(t,e)},getNotificationManagerImpl:function(){return we(t)},getWindowManagerImpl:function(){return{open:function(t,e,n){var i;return t.title=t.title||" ",t.url=t.url||t.file,t.url&&(t.width=parseInt(t.width||320,10),t.height=parseInt(t.height||240,10)),t.body&&(t.items={defaults:t.defaults,type:t.bodyType||"form",items:t.body,data:t.data,callbacks:t.commands}),t.url||t.buttons||(t.buttons=[{text:"Ok",subtype:"primary",onclick:function(){i.find("form")[0].submit()}},{text:"Cancel",onclick:function(){i.close()}}]),(i=new Xe(t)).on("close",function(){n(i)}),t.data&&i.on("postRender",function(){this.find("*").each(function(e){var n=e.name();n in t.data&&e.value(t.data[n])})}),i.features=t||{},i.params=e||{},i=i.renderTo(document.body).reflow()},alert:function(t,e,n){var i;return(i=Ge.alert(t,function(){e()})).on("close",function(){n(i)}),i},confirm:function(t,e,n){var i;return(i=Ge.confirm(t,function(t){e(t)})).on("close",function(){n(i)}),i},close:function(t){t.close()},getParams:function(t){return t.params},setParams:function(t,e){t.params=e}}}}},Ze="undefined"!=typeof window?window:Function("return this;")(),Qe=function(t,e){for(var n=e!==undefined&&null!==e?e:Ze,i=0;i<t.length&&n!==undefined&&null!==n;++i)n=n[t[i]];return n},tn=function(t,e){var n=t.split(".");return Qe(n,e)},en={getOrDie:function(t,e){var n=tn(t,e);if(n===undefined||null===n)throw t+" not available on this browser";return n}},nn=tinymce.util.Tools.resolve("tinymce.util.Promise"),rn=function(t){return new nn(function(e){var n=new function(){return new(en.getOrDie("FileReader"))};n.onloadend=function(){e(n.result.split(",")[1])},n.readAsDataURL(t)})},on=function(){return new nn(function(t){var e;(e=document.createElement("input")).type="file",e.style.position="fixed",e.style.left=0,e.style.top=0,e.style.opacity=.001,document.body.appendChild(e),e.onchange=function(e){t(Array.prototype.slice.call(e.target.files))},e.click(),e.parentNode.removeChild(e)})},sn=0,an=function(t){return t+sn+++(e=function(){return Math.round(4294967295*Math.random()).toString(36)},"s"+Date.now().toString(36)+e()+e()+e());var e},ln=function(t,e){var n={};function i(i){var r,o,s;o=e[i?"startContainer":"endContainer"],s=e[i?"startOffset":"endOffset"],1===o.nodeType&&(r=t.create("span",{"data-mce-type":"bookmark"}),o.hasChildNodes()?(s=Math.min(s,o.childNodes.length-1),i?o.insertBefore(r,o.childNodes[s]):t.insertAfter(r,o.childNodes[s])):o.appendChild(r),o=r,s=0),n[i?"startContainer":"endContainer"]=o,n[i?"startOffset":"endOffset"]=s}return i(!0),e.collapsed||i(),n},un=function(t,e){function n(n){var i,r,o;i=o=e[n?"startContainer":"endContainer"],r=e[n?"startOffset":"endOffset"],i&&(1===i.nodeType&&(r=function(t){for(var e=t.parentNode.firstChild,n=0;e;){if(e===t)return n;1===e.nodeType&&"bookmark"===e.getAttribute("data-mce-type")||n++,e=e.nextSibling}return-1}(i),i=i.parentNode,t.remove(o)),e[n?"startContainer":"endContainer"]=i,e[n?"startOffset":"endOffset"]=r)}n(!0),n();var i=t.createRng();return i.setStart(e.startContainer,e.startOffset),e.endContainer&&i.setEnd(e.endContainer,e.endOffset),i},cn=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),dn=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),fn=function(t){return"A"===t.nodeName&&t.hasAttribute("href")},hn=function(t){var e,n,i,r,o,s,a,l;return r=t.selection,o=t.dom,s=r.getRng(),a=o,l=dn.getNode(s.startContainer,s.startOffset),e=a.getParent(l,fn)||l,n=dn.getNode(s.endContainer,s.endOffset),i=t.getBody(),R.grep(function(t,e,n){var i,r,o=[];for(i=new cn(e,t),r=e;r&&(1===r.nodeType&&o.push(r),r!==n);r=i.next());return o}(i,e,n),fn)},mn=function(t){var e,n,i,r,o;e=t,n=hn(t),r=e.dom,o=e.selection,i=ln(r,o.getRng()),R.each(n,function(t){e.dom.remove(t,!0)}),o.setRng(un(r,i))},gn=function(t){t.selection.collapse(!1)},pn=function(t){t.focus(),mn(t),gn(t)},vn=function(t,e){var n,i,r,o,s,a=t.dom.getParent(t.selection.getStart(),"a[href]");a?(o=a,s=e,(r=t).focus(),r.dom.setAttrib(o,"href",s),gn(r)):(i=e,(n=t).execCommand("mceInsertLink",!1,{href:i}),gn(n))},yn=function(t,e,n){var i,r,o;t.plugins.table?t.plugins.table.insertTable(e,n):(r=e,o=n,(i=t).undoManager.transact(function(){var t,e;i.insertContent(function(t,e){var n,i,r;for(r='<table data-mce-id="mce" style="width: 100%">',r+="<tbody>",i=0;i<e;i++){for(r+="<tr>",n=0;n<t;n++)r+="<td><br></td>";r+="</tr>"}return r+="</tbody>",r+="</table>"}(r,o)),(t=i.dom.select("*[data-mce-id]")[0]).removeAttribute("data-mce-id"),e=i.dom.select("td,th",t),i.selection.setCursorLocation(e[0],0)}))},bn=function(t,e){t.execCommand("FormatBlock",!1,e)},xn=function(t,e,n){var i,r;r=(i=t.editorUpload.blobCache).create(an("mceu"),n,e),i.add(r),t.insertContent(t.dom.createHTML("img",{src:r.blobUri()}))},wn=function(t,e){0===e.trim().length?pn(t):vn(t,e)},_n=pn,Rn=function(t,e){t.addButton("quicklink",{icon:"link",tooltip:"Insert/Edit link",stateSelector:"a[href]",onclick:function(){e.showForm(t,"quicklink")}}),t.addButton("quickimage",{icon:"image",tooltip:"Insert image",onclick:function(){on().then(function(e){var n=e[0];rn(n).then(function(e){xn(t,e,n)})})}}),t.addButton("quicktable",{icon:"table",tooltip:"Insert table",onclick:function(){e.hide(),yn(t,2,2)}}),function(t){for(var e=function(e){return function(){bn(t,e)}},n=1;n<6;n++){var i="h"+n;t.addButton(i,{text:i.toUpperCase(),tooltip:"Heading "+n,stateSelector:i,onclick:e(i),onPostRender:function(){this.getEl().firstChild.firstChild.style.fontWeight="bold"}})}}(t)},Cn=function(){var t=a.container;if(t&&"static"!==l.DOM.getStyle(t,"position",!0)){var e=l.DOM.getPos(t),n=e.x-t.scrollLeft,i=e.y-t.scrollTop;return mt.some({x:n,y:i})}return mt.none()},kn=function(t){return/^www\.|\.(com|org|edu|gov|uk|net|ca|de|jp|fr|au|us|ru|ch|it|nl|se|no|es|mil)$/i.test(t.trim())},En=function(t){return/^https?:\/\//.test(t.trim())},Hn=function(t,e){return!En(e)&&kn(e)?(n=t,i=e,new nn(function(t){n.windowManager.confirm("The URL you entered seems to be an external link. Do you want to add the required http:// prefix?",function(e){t(!0===e?"http://"+i:i)})})):nn.resolve(e);var n,i},Sn=function(t,e){var n,i,r,o={};return n="quicklink",i={items:[{type:"button",name:"unlink",icon:"unlink",onclick:function(){t.focus(),_n(t),e()},tooltip:"Remove link"},{type:"filepicker",name:"linkurl",placeholder:"Paste or type a link",filetype:"file",onchange:function(t){var e=t.meta;e&&e.attach&&(o={href:this.value(),attach:e.attach})}},{type:"button",icon:"checkmark",subtype:"primary",tooltip:"Ok",onclick:"submit"}],onshow:function(e){if(e.control===this){var n,i="";(n=t.dom.getParent(t.selection.getStart(),"a[href]"))&&(i=t.dom.getAttrib(n,"href")),this.fromJSON({linkurl:i}),r=this.find("#unlink"),n?r.show():r.hide(),this.find("#linkurl")[0].focus()}var r},onsubmit:function(n){Hn(t,n.data.linkurl).then(function(n){t.undoManager.transact(function(){n===o.href&&(o.attach(),o={}),wn(t,n)}),e()})}},(r=Ce.create(R.extend({type:"form",layout:"flex",direction:"row",padding:5,name:n,spacing:3},i))).on("show",function(){r.find("textbox").eq(0).each(function(t){t.focus()})}),r},Mn=function(t,e,n){var i,r,o=[];if(n)return R.each(B(r=n)?r:N(r)?r.split(/[ ,]/):[],function(e){if("|"===e)i=null;else if(t.buttons[e]){i||(i={type:"buttongroup",items:[]},o.push(i));var n=t.buttons[e];A(n)&&(n=n()),n.type=n.type||"button",(n=Ce.create(n)).on("postRender",(r=t,s=n,function(){var t,e,n=(e=function(t,e){return{selector:t,handler:e}},(t=s).settings.stateSelector?e(t.settings.stateSelector,function(e){t.active(e)}):t.settings.disabledStateSelector?e(t.settings.disabledStateSelector,function(e){t.disabled(e)}):null);null!==n&&r.selection.selectorChanged(n.selector,n.handler)})),i.items.push(n)}var r,s}),Ce.create({type:"toolbar",layout:"flow",name:e,items:o})},Tn=function(){var t,e,n=function(t){return t.items().length>0},i=function(t,e){var i,r,o=(i=t,r=e,R.map(r,function(t){return Mn(i,t.id,t.items)})).concat([Mn(t,"text",J(t)),Mn(t,"insert",G(t)),Sn(t,c)]);return Ce.create({type:"floatpanel",role:"dialog",classes:"tinymce tinymce-inline arrow",ariaLabel:"Inline toolbar",layout:"flex",direction:"column",align:"stretch",autohide:!1,autofix:!0,fixed:!0,border:1,items:R.grep(o,n),oncancel:function(){t.focus()}})},r=function(t){t&&t.show()},o=function(t,e){t.moveTo(e.x,e.y)},s=function(t,e){e=e?e.substr(0,2):"",R.each({t:"down",b:"up",c:"center"},function(n,i){t.classes.toggle("arrow-"+n,i===e.substr(0,1))}),"cr"===e?(t.classes.toggle("arrow-left",!0),t.classes.toggle("arrow-right",!1)):"cl"===e?(t.classes.toggle("arrow-left",!0),t.classes.toggle("arrow-right",!0)):R.each({l:"left",r:"right"},function(n,i){t.classes.toggle("arrow-"+n,i===e.substr(1,1))})},a=function(t,e){var n=t.items().filter("#"+e);return n.length>0&&(n[0].show(),t.reflow(),!0)},u=function(t,n,i,r){var a,u,c,d;if(d=K(i),a=b(i),u=l.DOM.getRect(t.getEl()),c="insert"===n?j(r,a,u):$(r,a,u)){var f=Cn().getOr({x:0,y:0}),h={x:c.rect.x-f.x,y:c.rect.y-f.y,w:c.rect.w,h:c.rect.h};return e=r,o(t,Y(d,r,a,h)),s(t,c.position),!0}return!1},c=function(){t&&t.hide()};return{show:function(e,n,o,s){var l,d,f,h;t||(M(e),(t=i(e,s)).renderTo().reflow().moveTo(o.x,o.y),e.nodeChanged()),d=n,f=e,h=o,r(l=t),l.items().hide(),a(l,d)?!1===u(l,d,f,h)&&c():c()},showForm:function(n,i){if(t){if(t.items().hide(),!a(t,i))return void c();var u,d,f,h=void 0;r(t),t.items().hide(),a(t,i),f=K(n),u=b(n),h=l.DOM.getRect(t.getEl()),(d=$(e,u,h))&&(h=d.rect,o(t,Y(f,e,u,h)),s(t,d.position))}},reposition:function(e,n,i){t&&u(t,n,e,i)},inForm:function(){return t&&t.visible()&&t.items().filter("form:visible").length>0},hide:c,focus:function(){t&&t.find("toolbar:visible").eq(0).each(function(t){t.focus(!0)})},remove:function(){t&&(t.remove(),t=null)}}},Wn=Wt.extend({Defaults:{firstControlClass:"first",lastControlClass:"last"},init:function(t){this.settings=R.extend({},this.Defaults,t)},preRender:function(t){t.bodyClasses.add(this.settings.containerClass)},applyClasses:function(t){var e,n,i,r,o=this.settings;e=o.firstControlClass,n=o.lastControlClass,t.each(function(t){t.classes.remove(e).remove(n).add(o.controlClass),t.visible()&&(i||(i=t),r=t)}),i&&i.classes.add(e),r&&r.classes.add(n)},renderHtml:function(t){var e="";return this.applyClasses(t.items()),t.items().each(function(t){e+=t.renderHtml()}),e},recalc:function(){},postRender:function(){},isNative:function(){return!1}}),Pn=Wn.extend({Defaults:{containerClass:"abs-layout",controlClass:"abs-layout-item"},recalc:function(t){t.items().filter(":visible").each(function(t){var e=t.settings;t.layoutRect({x:e.x,y:e.y,w:e.w,h:e.h}),t.recalc&&t.recalc()})},renderHtml:function(t){return'<div id="'+t._id+'-absend" class="'+t.classPrefix+'abs-end"></div>'+this._super(t)}}),Nn=ve.extend({Defaults:{classes:"widget btn",role:"button"},init:function(t){var e,n=this;n._super(t),t=n.settings,e=n.settings.size,n.on("click mousedown",function(t){t.preventDefault()}),n.on("touchstart",function(t){n.fire("click",t),t.preventDefault()}),t.subtype&&n.classes.add(t.subtype),e&&n.classes.add("btn-"+e),t.icon&&n.icon(t.icon)},icon:function(t){return arguments.length?(this.state.set("icon",t),this):this.state.get("icon")},repaint:function(){var t,e=this.getEl().firstChild;e&&((t=e.style).width=t.height="100%"),this._super()},renderHtml:function(){var t,e,n=this,i=n._id,r=n.classPrefix,o=n.state.get("icon"),s=n.state.get("text"),a="",l=n.settings;return(t=l.image)?(o="none","string"!=typeof t&&(t=window.getSelection?t[0]:t[1]),t=" style=\"background-image: url('"+t+"')\""):t="",s&&(n.classes.add("btn-has-text"),a='<span class="'+r+'txt">'+n.encode(s)+"</span>"),o=o?r+"ico "+r+"i-"+o:"",e="boolean"==typeof l.active?' aria-pressed="'+l.active+'"':"",'<div id="'+i+'" class="'+n.classes+'" tabindex="-1"'+e+'><button id="'+i+'-button" role="presentation" type="button" tabindex="-1">'+(o?'<i class="'+o+'"'+t+"></i>":"")+a+"</button></div>"},bindStates:function(){var t=this,e=t.$,n=t.classPrefix+"txt";function i(i){var r=e("span."+n,t.getEl());i?(r[0]||(e("button:first",t.getEl()).append('<span class="'+n+'"></span>'),r=e("span."+n,t.getEl())),r.html(t.encode(i))):r.remove(),t.classes.toggle("btn-has-text",!!i)}return t.state.on("change:text",function(t){i(t.value)}),t.state.on("change:icon",function(e){var n=e.value,r=t.classPrefix;t.settings.icon=n,n=n?r+"ico "+r+"i-"+t.settings.icon:"";var o=t.getEl().firstChild,s=o.getElementsByTagName("i")[0];n?(s&&s===o.firstChild||(s=document.createElement("i"),o.insertBefore(s,o.firstChild)),s.className=n):s&&o.removeChild(s),i(t.state.get("text"))}),t._super()}}),On=Nn.extend({init:function(t){t=R.extend({text:"Browse...",multiple:!1,accept:null},t),this._super(t),this.classes.add("browsebutton"),t.multiple&&this.classes.add("multiple")},postRender:function(){var t=this,e=Mt.create("input",{type:"file",id:t._id+"-browse",accept:t.settings.accept});t._super(),Tt(e).on("change",function(e){var n=e.target.files;t.value=function(){return n.length?t.settings.multiple?n:n[0]:null},e.preventDefault(),n.length&&t.fire("change",e)}),Tt(e).on("click",function(t){t.stopPropagation()}),Tt(t.getEl("button")).on("click",function(t){t.stopPropagation(),e.click()}),t.getEl().appendChild(e)},remove:function(){Tt(this.getEl("button")).off(),Tt(this.getEl("input")).off(),this._super()}}),Dn=Pe.extend({Defaults:{defaultType:"button",role:"group"},renderHtml:function(){var t=this,e=t._layout;return t.classes.add("btn-group"),t.preRender(),e.preRender(t),'<div id="'+t._id+'" class="'+t.classes+'"><div id="'+t._id+'-body">'+(t.settings.html||"")+e.renderHtml(t)+"</div></div>"}}),An=ve.extend({Defaults:{classes:"checkbox",role:"checkbox",checked:!1},init:function(t){var e=this;e._super(t),e.on("click mousedown",function(t){t.preventDefault()}),e.on("click",function(t){t.preventDefault(),e.disabled()||e.checked(!e.checked())}),e.checked(e.settings.checked)},checked:function(t){return arguments.length?(this.state.set("checked",t),this):this.state.get("checked")},value:function(t){return arguments.length?this.checked(t):this.checked()},renderHtml:function(){var t=this,e=t._id,n=t.classPrefix;return'<div id="'+e+'" class="'+t.classes+'" unselectable="on" aria-labelledby="'+e+'-al" tabindex="-1"><i class="'+n+"ico "+n+'i-checkbox"></i><span id="'+e+'-al" class="'+n+'label">'+t.encode(t.state.get("text"))+"</span></div>"},bindStates:function(){var t=this;function e(e){t.classes.toggle("checked",e),t.aria("checked",e)}return t.state.on("change:text",function(e){t.getEl("al").firstChild.data=t.translate(e.value)}),t.state.on("change:checked change:value",function(n){t.fire("change"),e(n.value)}),t.state.on("change:icon",function(e){var n=e.value,i=t.classPrefix;if(void 0===n)return t.settings.icon;t.settings.icon=n,n=n?i+"ico "+i+"i-"+t.settings.icon:"";var r=t.getEl().firstChild,o=r.getElementsByTagName("i")[0];n?(o&&o===r.firstChild||(o=document.createElement("i"),r.insertBefore(o,r.firstChild)),o.className=n):o&&r.removeChild(o)}),t.state.get("checked")&&e(!0),t._super()}}),Bn=tinymce.util.Tools.resolve("tinymce.util.VK"),Ln=ve.extend({init:function(t){var e=this;e._super(t),t=e.settings,e.classes.add("combobox"),e.subinput=!0,e.ariaTarget="inp",t.menu=t.menu||t.values,t.menu&&(t.icon="caret"),e.on("click",function(n){var i=n.target,r=e.getEl();if(Tt.contains(r,i)||i===r)for(;i&&i!==r;)i.id&&-1!==i.id.indexOf("-open")&&(e.fire("action"),t.menu&&(e.showMenu(),n.aria&&e.menu.items()[0].focus())),i=i.parentNode}),e.on("keydown",function(t){var n;13===t.keyCode&&"INPUT"===t.target.nodeName&&(t.preventDefault(),e.parents().reverse().each(function(t){if(t.toJSON)return n=t,!1}),e.fire("submit",{data:n.toJSON()}))}),e.on("keyup",function(t){if("INPUT"===t.target.nodeName){var n=e.state.get("value"),i=t.target.value;i!==n&&(e.state.set("value",i),e.fire("autocomplete",t))}}),e.on("mouseover",function(t){var n=e.tooltip().moveTo(-65535);if(e.statusLevel()&&-1!==t.target.className.indexOf(e.classPrefix+"status")){var i=e.statusMessage()||"Ok",r=n.text(i).show().testMoveRel(t.target,["bc-tc","bc-tl","bc-tr"]);n.classes.toggle("tooltip-n","bc-tc"===r),n.classes.toggle("tooltip-nw","bc-tl"===r),n.classes.toggle("tooltip-ne","bc-tr"===r),n.moveRel(t.target,r)}})},statusLevel:function(t){return arguments.length>0&&this.state.set("statusLevel",t),this.state.get("statusLevel")},statusMessage:function(t){return arguments.length>0&&this.state.set("statusMessage",t),this.state.get("statusMessage")},showMenu:function(){var t,e=this,n=e.settings;e.menu||((t=n.menu||[]).length?t={type:"menu",items:t}:t.type=t.type||"menu",e.menu=Ce.create(t).parent(e).renderTo(e.getContainerElm()),e.fire("createmenu"),e.menu.reflow(),e.menu.on("cancel",function(t){t.control===e.menu&&e.focus()}),e.menu.on("show hide",function(t){t.control.items().each(function(t){t.active(t.value()===e.value())})}).fire("show"),e.menu.on("select",function(t){e.value(t.control.value())}),e.on("focusin",function(t){"INPUT"===t.target.tagName.toUpperCase()&&e.menu.hide()}),e.aria("expanded",!0)),e.menu.show(),e.menu.layoutRect({w:e.layoutRect().w}),e.menu.moveRel(e.getEl(),e.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"])},focus:function(){this.getEl("inp").focus()},repaint:function(){var t,e,n=this,i=n.getEl(),r=n.getEl("open"),o=n.layoutRect(),s=0,a=i.firstChild;n.statusLevel()&&"none"!==n.statusLevel()&&(s=parseInt(Mt.getRuntimeStyle(a,"padding-right"),10)-parseInt(Mt.getRuntimeStyle(a,"padding-left"),10)),t=r?o.w-Mt.getSize(r).width-10:o.w-10;var l=document;return l.all&&(!l.documentMode||l.documentMode<=8)&&(e=n.layoutRect().h-2+"px"),Tt(a).css({width:t-s,lineHeight:e}),n._super(),n},postRender:function(){var t=this;return Tt(this.getEl("inp")).on("change",function(e){t.state.set("value",e.target.value),t.fire("change",e)}),t._super()},renderHtml:function(){var t,e,n,i=this,r=i._id,o=i.settings,s=i.classPrefix,a=i.state.get("value")||"",l="",u="";return"spellcheck"in o&&(u+=' spellcheck="'+o.spellcheck+'"'),o.maxLength&&(u+=' maxlength="'+o.maxLength+'"'),o.size&&(u+=' size="'+o.size+'"'),o.subtype&&(u+=' type="'+o.subtype+'"'),n='<i id="'+r+'-status" class="mce-status mce-ico" style="display: none"></i>',i.disabled()&&(u+=' disabled="disabled"'),(t=o.icon)&&"caret"!==t&&(t=s+"ico "+s+"i-"+o.icon),e=i.state.get("text"),(t||e)&&(l='<div id="'+r+'-open" class="'+s+"btn "+s+'open" tabIndex="-1" role="button"><button id="'+r+'-action" type="button" hidefocus="1" tabindex="-1">'+("caret"!==t?'<i class="'+t+'"></i>':'<i class="'+s+'caret"></i>')+(e?(t?" ":"")+e:"")+"</button></div>",i.classes.add("has-open")),'<div id="'+r+'" class="'+i.classes+'"><input id="'+r+'-inp" class="'+s+'textbox" value="'+i.encode(a,!1)+'" hidefocus="1"'+u+' placeholder="'+i.encode(o.placeholder)+'" />'+n+l+"</div>"},value:function(t){return arguments.length?(this.state.set("value",t),this):(this.state.get("rendered")&&this.state.set("value",this.getEl("inp").value),this.state.get("value"))},showAutoComplete:function(t,e){var n=this;if(0!==t.length){n.menu?n.menu.items().remove():n.menu=Ce.create({type:"menu",classes:"combobox-menu",layout:"flow"}).parent(n).renderTo(),R.each(t,function(t){var i,r;n.menu.add({text:t.title,url:t.previewUrl,match:e,classes:"menu-item-ellipsis",onclick:(i=t.value,r=t.title,function(){n.fire("selectitem",{title:r,value:i})})})}),n.menu.renderNew(),n.hideMenu(),n.menu.on("cancel",function(t){t.control.parent()===n.menu&&(t.stopPropagation(),n.focus(),n.hideMenu())}),n.menu.on("select",function(){n.focus()});var i=n.layoutRect().w;n.menu.layoutRect({w:i,minW:0,maxW:i}),n.menu.repaint(),n.menu.reflow(),n.menu.show(),n.menu.moveRel(n.getEl(),n.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"])}else n.hideMenu()},hideMenu:function(){this.menu&&this.menu.hide()},bindStates:function(){var t=this;t.state.on("change:value",function(e){t.getEl("inp").value!==e.value&&(t.getEl("inp").value=e.value)}),t.state.on("change:disabled",function(e){t.getEl("inp").disabled=e.value}),t.state.on("change:statusLevel",function(e){var n=t.getEl("status"),i=t.classPrefix,r=e.value;Mt.css(n,"display","none"===r?"none":""),Mt.toggleClass(n,i+"i-checkmark","ok"===r),Mt.toggleClass(n,i+"i-warning","warn"===r),Mt.toggleClass(n,i+"i-error","error"===r),t.classes.toggle("has-status","none"!==r),t.repaint()}),Mt.on(t.getEl("status"),"mouseleave",function(){t.tooltip().hide()}),t.on("cancel",function(e){t.menu&&t.menu.visible()&&(e.stopPropagation(),t.hideMenu())});var e=function(t,e){e&&e.items().length>0&&e.items().eq(t)[0].focus()};return t.on("keydown",function(n){var i=n.keyCode;"INPUT"===n.target.nodeName&&(i===Bn.DOWN?(n.preventDefault(),t.fire("autocomplete"),e(0,t.menu)):i===Bn.UP&&(n.preventDefault(),e(-1,t.menu)))}),t._super()},remove:function(){Tt(this.getEl("inp")).off(),this.menu&&this.menu.remove(),this._super()}}),zn=Ln.extend({init:function(t){var e=this;t.spellcheck=!1,t.onaction&&(t.icon="none"),e._super(t),e.classes.add("colorbox"),e.on("change keyup postrender",function(){e.repaintColor(e.value())})},repaintColor:function(t){var e=this.getEl("open"),n=e?e.getElementsByTagName("i")[0]:null;if(n)try{n.style.background=t}catch(i){}},bindStates:function(){var t=this;return t.state.on("change:value",function(e){t.state.get("rendered")&&t.repaintColor(e.value)}),t._super()}}),In=Nn.extend({showPanel:function(){var t=this,e=t.settings;if(t.classes.add("opened"),t.panel)t.panel.show();else{var n=e.panel;n.type&&(n={layout:"grid",items:n}),n.role=n.role||"dialog",n.popover=!0,n.autohide=!0,n.ariaRoot=!0,t.panel=new Ve(n).on("hide",function(){t.classes.remove("opened")}).on("cancel",function(e){e.stopPropagation(),t.focus(),t.hidePanel()}).parent(t).renderTo(t.getContainerElm()),t.panel.fire("show"),t.panel.reflow()}var i=t.panel.testMoveRel(t.getEl(),e.popoverAlign||(t.isRtl()?["bc-tc","bc-tl","bc-tr"]:["bc-tc","bc-tr","bc-tl"]));t.panel.classes.toggle("start","bc-tl"===i),t.panel.classes.toggle("end","bc-tr"===i),t.panel.moveRel(t.getEl(),i)},hidePanel:function(){this.panel&&this.panel.hide()},postRender:function(){var t=this;return t.aria("haspopup",!0),t.on("click",function(e){e.control===t&&(t.panel&&t.panel.visible()?t.hidePanel():(t.showPanel(),t.panel.focus(!!e.aria)))}),t._super()},remove:function(){return this.panel&&(this.panel.remove(),this.panel=null),this._super()}}),Fn=l.DOM,Vn=In.extend({init:function(t){this._super(t),this.classes.add("splitbtn"),this.classes.add("colorbutton")},color:function(t){return t?(this._color=t,this.getEl("preview").style.backgroundColor=t,this):this._color},resetColor:function(){return this._color=null,this.getEl("preview").style.backgroundColor=null,this},renderHtml:function(){var t=this,e=t._id,n=t.classPrefix,i=t.state.get("text"),r=t.settings.icon?n+"ico "+n+"i-"+t.settings.icon:"",o=t.settings.image?" style=\"background-image: url('"+t.settings.image+"')\"":"",s="";return i&&(t.classes.add("btn-has-text"),s='<span class="'+n+'txt">'+t.encode(i)+"</span>"),'<div id="'+e+'" class="'+t.classes+'" role="button" tabindex="-1" aria-haspopup="true"><button role="presentation" hidefocus="1" type="button" tabindex="-1">'+(r?'<i class="'+r+'"'+o+"></i>":"")+'<span id="'+e+'-preview" class="'+n+'preview"></span>'+s+'</button><button type="button" class="'+n+'open" hidefocus="1" tabindex="-1"> <i class="'+n+'caret"></i></button></div>'},postRender:function(){var t=this,e=t.settings.onclick;return t.on("click",function(n){n.aria&&"down"===n.aria.key||n.control!==t||Fn.getParent(n.target,"."+t.classPrefix+"open")||(n.stopImmediatePropagation(),e.call(t,n))}),delete t.settings.onclick,t._super()}}),Un=tinymce.util.Tools.resolve("tinymce.util.Color"),qn=ve.extend({Defaults:{classes:"widget colorpicker"},init:function(t){this._super(t)},postRender:function(){var t,e,n,i,r,o=this,s=o.color();function a(t,e){var n,i,r=Mt.getPos(t);return n=e.pageX-r.x,i=e.pageY-r.y,{x:n=Math.max(0,Math.min(n/t.clientWidth,1)),y:i=Math.max(0,Math.min(i/t.clientHeight,1))}}function l(t,e){var s=(360-t.h)/360;Mt.css(n,{top:100*s+"%"}),e||Mt.css(r,{left:t.s+"%",top:100-t.v+"%"}),i.style.background=Un({s:100,v:100,h:t.h}).toHex(),o.color().parse({s:t.s,v:t.v,h:t.h})}function u(e){var n;n=a(i,e),t.s=100*n.x,t.v=100*(1-n.y),l(t),o.fire("change")}function c(n){var i;i=a(e,n),(t=s.toHsv()).h=360*(1-i.y),l(t,!0),o.fire("change")}e=o.getEl("h"),n=o.getEl("hp"),i=o.getEl("sv"),r=o.getEl("svp"),o._repaint=function(){l(t=s.toHsv())},o._super(),o._svdraghelper=new Re(o._id+"-sv",{start:u,drag:u}),o._hdraghelper=new Re(o._id+"-h",{start:c,drag:c}),o._repaint()},rgb:function(){return this.color().toRgb()},value:function(t){if(!arguments.length)return this.color().toHex();this.color().parse(t),this._rendered&&this._repaint()},color:function(){return this._color||(this._color=Un()),this._color},renderHtml:function(){var t,e=this._id,n=this.classPrefix,i="#ff0000,#ff0080,#ff00ff,#8000ff,#0000ff,#0080ff,#00ffff,#00ff80,#00ff00,#80ff00,#ffff00,#ff8000,#ff0000";return t='<div id="'+e+'-h" class="'+n+'colorpicker-h" style="background: -ms-linear-gradient(top,'+i+");background: linear-gradient(to bottom,"+i+');">'+function(){var t,e,r,o,s="";for(r="filter:progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr=",t=0,e=(o=i.split(",")).length-1;t<e;t++)s+='<div class="'+n+'colorpicker-h-chunk" style="height:'+100/e+"%;"+r+o[t]+",endColorstr="+o[t+1]+");-ms-"+r+o[t]+",endColorstr="+o[t+1]+')"></div>';return s}()+'<div id="'+e+'-hp" class="'+n+'colorpicker-h-marker"></div></div>','<div id="'+e+'" class="'+this.classes+'"><div id="'+e+'-sv" class="'+n+'colorpicker-sv"><div class="'+n+'colorpicker-overlay1"><div class="'+n+'colorpicker-overlay2"><div id="'+e+'-svp" class="'+n+'colorpicker-selector1"><div class="'+n+'colorpicker-selector2"></div></div></div></div></div>'+t+"</div>"}}),jn=ve.extend({init:function(t){t=R.extend({height:100,text:"Drop an image here",multiple:!1,accept:null},t),this._super(t),this.classes.add("dropzone"),t.multiple&&this.classes.add("multiple")},renderHtml:function(){var t,e,n=this.settings;return t={id:this._id,hidefocus:"1"},e=Mt.create("div",t,"<span>"+this.translate(n.text)+"</span>"),n.height&&Mt.css(e,"height",n.height+"px"),n.width&&Mt.css(e,"width",n.width+"px"),e.className=this.classes,e.outerHTML},postRender:function(){var t=this,e=function(e){e.preventDefault(),t.classes.toggle("dragenter"),t.getEl().className=t.classes};t._super(),t.$el.on("dragover",function(t){t.preventDefault()}),t.$el.on("dragenter",e),t.$el.on("dragleave",e),t.$el.on("drop",function(e){if(e.preventDefault(),!t.state.get("disabled")){var n=function(e){var n=t.settings.accept;if("string"!=typeof n)return e;var i=new RegExp("("+n.split(/\s*,\s*/).join("|")+")$","i");return R.grep(e,function(t){return i.test(t.name)})}(e.dataTransfer.files);t.value=function(){return n.length?t.settings.multiple?n:n[0]:null},n.length&&t.fire("change",e)}})},remove:function(){this.$el.off(),this._super()}}),$n=ve.extend({init:function(t){var e=this;t.delimiter||(t.delimiter="\xbb"),e._super(t),e.classes.add("path"),e.canFocus=!0,e.on("click",function(t){var n;(n=t.target.getAttribute("data-index"))&&e.fire("select",{value:e.row()[n],index:n})}),e.row(e.settings.row)},focus:function(){return this.getEl().firstChild.focus(),this},row:function(t){return arguments.length?(this.state.set("row",t),this):this.state.get("row")},renderHtml:function(){return'<div id="'+this._id+'" class="'+this.classes+'">'+this._getDataPathHtml(this.state.get("row"))+"</div>"},bindStates:function(){var t=this;return t.state.on("change:row",function(e){t.innerHtml(t._getDataPathHtml(e.value))}),t._super()},_getDataPathHtml:function(t){var e,n,i=t||[],r="",o=this.classPrefix;for(e=0,n=i.length;e<n;e++)r+=(e>0?'<div class="'+o+'divider" aria-hidden="true"> '+this.settings.delimiter+" </div>":"")+'<div role="button" class="'+o+"path-item"+(e===n-1?" "+o+"last":"")+'" data-index="'+e+'" tabindex="-1" id="'+this._id+"-"+e+'" aria-level="'+(e+1)+'">'+i[e].name+"</div>";return r||(r='<div class="'+o+'path-item">\xa0</div>'),r}}),Yn=$n.extend({postRender:function(){var t=this,e=t.settings.editor;function n(t){if(1===t.nodeType){if("BR"===t.nodeName||t.getAttribute("data-mce-bogus"))return!0;if("bookmark"===t.getAttribute("data-mce-type"))return!0}return!1}return!1!==e.settings.elementpath&&(t.on("select",function(t){e.focus(),e.selection.select(this.row()[t.index].element),e.nodeChanged()}),e.on("nodeChange",function(i){for(var r=[],o=i.parents,s=o.length;s--;)if(1===o[s].nodeType&&!n(o[s])){var a=e.fire("ResolveName",{name:o[s].nodeName.toLowerCase(),target:o[s]});if(a.isDefaultPrevented()||r.push({name:a.name,element:o[s]}),a.isPropagationStopped())break}t.row(r)})),t._super()}}),Xn=Pe.extend({Defaults:{layout:"flex",align:"center",defaults:{flex:1}},renderHtml:function(){var t=this,e=t._layout,n=t.classPrefix;return t.classes.add("formitem"),e.preRender(t),'<div id="'+t._id+'" class="'+t.classes+'" hidefocus="1" tabindex="-1">'+(t.settings.title?'<div id="'+t._id+'-title" class="'+n+'title">'+t.settings.title+"</div>":"")+'<div id="'+t._id+'-body" class="'+t.bodyClasses+'">'+(t.settings.html||"")+e.renderHtml(t)+"</div></div>"}}),Jn=Pe.extend({Defaults:{containerCls:"form",layout:"flex",direction:"column",align:"stretch",flex:1,padding:15,labelGap:30,spacing:10,callbacks:{submit:function(){this.submit()}}},preRender:function(){var t=this,e=t.items();t.settings.formItemDefaults||(t.settings.formItemDefaults={layout:"flex",autoResize:"overflow",defaults:{flex:1}}),e.each(function(e){var n,i=e.settings.label;i&&((n=new Xn(R.extend({items:{type:"label",id:e._id+"-l",text:i,flex:0,forId:e._id,disabled:e.disabled()}},t.settings.formItemDefaults))).type="formitem",e.aria("labelledby",e._id+"-l"),"undefined"==typeof e.settings.flex&&(e.settings.flex=1),t.replace(e,n),n.add(e))})},submit:function(){return this.fire("submit",{data:this.toJSON()})},postRender:function(){this._super(),this.fromJSON(this.settings.data)},bindStates:function(){var t=this;function e(){var e,n,i=0,r=[];if(!1!==t.settings.labelGapCalc)for(("children"===t.settings.labelGapCalc?t.find("formitem"):t.items()).filter("formitem").each(function(t){var e=t.items()[0],n=e.getEl().clientWidth;i=n>i?n:i,r.push(e)}),n=t.settings.labelGap||0,e=r.length;e--;)r[e].settings.minWidth=i+n}t._super(),t.on("show",e),e()}}),Gn=Jn.extend({Defaults:{containerCls:"fieldset",layout:"flex",direction:"column",align:"stretch",flex:1,padding:"25 15 5 15",labelGap:30,spacing:10,border:1},renderHtml:function(){var t=this,e=t._layout,n=t.classPrefix;return t.preRender(),e.preRender(t),'<fieldset id="'+t._id+'" class="'+t.classes+'" hidefocus="1" tabindex="-1">'+(t.settings.title?'<legend id="'+t._id+'-title" class="'+n+'fieldset-title">'+t.settings.title+"</legend>":"")+'<div id="'+t._id+'-body" class="'+t.bodyClasses+'">'+(t.settings.html||"")+e.renderHtml(t)+"</div></fieldset>"}}),Kn=0,Zn=function(t){var e=(new Date).getTime();return t+"_"+Math.floor(1e9*Math.random())+ ++Kn+String(e)},Qn=function(t){if(null===t||t===undefined)throw new Error("Node cannot be null or undefined");return{dom:lt.constant(t)}},ti={fromHtml:function(t,e){var n=(e||document).createElement("div");if(n.innerHTML=t,!n.hasChildNodes()||n.childNodes.length>1)throw console.error("HTML does not have a single root node",t),"HTML must have a single root node";return Qn(n.childNodes[0])},fromTag:function(t,e){var n=(e||document).createElement(t);return Qn(n)},fromText:function(t,e){var n=(e||document).createTextNode(t);return Qn(n)},fromDom:Qn,fromPoint:function(t,e,n){return mt.from(t.dom().elementFromPoint(e,n)).map(Qn)}},ei=function(t){var e,n=!1;return function(){return n||(n=!0,e=t.apply(null,arguments)),e}},ni=8,ii=9,ri=1,oi=3,si=function(t){return t.dom().nodeName.toLowerCase()},ai=function(t){return t.dom().nodeType},li=function(t){return function(e){return ai(e)===t}},ui=li(ri),ci=li(oi),di=li(ii),fi={name:si,type:ai,value:function(t){return t.dom().nodeValue},isElement:ui,isText:ci,isDocument:di,isComment:function(t){return ai(t)===ni||"#comment"===si(t)}},hi=(ei(function(){return hi(ti.fromDom(document))}),function(t){var e=t.dom().body;if(null===e||e===undefined)throw"Body is not available yet";return ti.fromDom(e)}),mi=function(t){return function(e){return function(t){if(null===t)return"null";var e=typeof t;return"object"===e&&Array.prototype.isPrototypeOf(t)?"array":"object"===e&&String.prototype.isPrototypeOf(t)?"string":e}(e)===t}},gi={isString:mi("string"),isObject:mi("object"),isArray:mi("array"),isNull:mi("null"),isBoolean:mi("boolean"),isUndefined:mi("undefined"),isFunction:mi("function"),isNumber:mi("number")},pi=(Je=Object.keys)===undefined?function(t){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);return e}:Je,vi=function(t,e){for(var n=pi(t),i=0,r=n.length;i<r;i++){var o=n[i];e(t[o],o,t)}},yi=function(t,e){var n={};return vi(t,function(i,r){var o=e(i,r,t);n[o.k]=o.v}),n},bi=function(t,e){var n=[];return vi(t,function(t,i){n.push(e(t,i))}),n},xi=function(t){return bi(t,function(t){return t})},wi={bifilter:function(t,e){var n={},i={};return vi(t,function(t,r){(e(t,r)?n:i)[r]=t}),{t:n,f:i}},each:vi,map:function(t,e){return yi(t,function(t,n,i){return{k:n,v:e(t,n,i)}})},mapToArray:bi,tupleMap:yi,find:function(t,e){for(var n=pi(t),i=0,r=n.length;i<r;i++){var o=n[i],s=t[o];if(e(s,o,t))return mt.some(s)}return mt.none()},keys:pi,values:xi,size:function(t){return xi(t).length}},_i=function(t){return t.slice(0).sort()},Ri={sort:_i,reqMessage:function(t,e){throw new Error("All required keys ("+_i(t).join(", ")+") were not specified. Specified keys were: "+_i(e).join(", ")+".")},unsuppMessage:function(t){throw new Error("Unsupported keys for object: "+_i(t).join(", "))},validateStrArr:function(t,e){if(!gi.isArray(e))throw new Error("The "+t+" fields must be an array. Was: "+e+".");Ht.each(e,function(e){if(!gi.isString(e))throw new Error("The value "+e+" in the "+t+" fields was not a string.")})},invalidTypeMessage:function(t,e){throw new Error("All values need to be of type: "+e+". Keys ("+_i(t).join(", ")+") were not.")},checkDupes:function(t){var e=_i(t);Ht.find(e,function(t,n){return n<e.length-1&&t===e[n+1]}).each(function(t){throw new Error("The field: "+t+" occurs more than once in the combined fields: ["+e.join(", ")+"].")})}},Ci={immutable:function(){var t=arguments;return function(){for(var e=new Array(arguments.length),n=0;n<e.length;n++)e[n]=arguments[n];if(t.length!==e.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+e.length+" arguments");var i={};return Ht.each(t,function(t,n){i[t]=lt.constant(e[n])}),i}},immutableBag:function(t,e){var n=t.concat(e);if(0===n.length)throw new Error("You must specify at least one required or optional field.");return Ri.validateStrArr("required",t),Ri.validateStrArr("optional",e),Ri.checkDupes(n),function(i){var r=wi.keys(i);Ht.forall(t,function(t){return Ht.contains(r,t)})||Ri.reqMessage(t,r);var o=Ht.filter(r,function(t){return!Ht.contains(n,t)});o.length>0&&Ri.unsuppMessage(o);var s={};return Ht.each(t,function(t){s[t]=lt.constant(i[t])}),Ht.each(e,function(t){s[t]=lt.constant(Object.prototype.hasOwnProperty.call(i,t)?mt.some(i[t]):mt.none())}),s}}},ki=function(t,e){var n=function(t,e){for(var n=0;n<t.length;n++){var i=t[n];if(i.test(e))return i}return undefined}(t,e);if(!n)return{major:0,minor:0};var i=function(t){return Number(e.replace(n,"$"+t))};return Hi(i(1),i(2))},Ei=function(){return Hi(0,0)},Hi=function(t,e){return{major:t,minor:e}},Si={nu:Hi,detect:function(t,e){var n=String(e).toLowerCase();return 0===t.length?Ei():ki(t,n)},unknown:Ei},Mi="Firefox",Ti=function(t,e){return function(){return e===t}},Wi=function(t){var e=t.current;return{current:e,version:t.version,isEdge:Ti("Edge",e),isChrome:Ti("Chrome",e),isIE:Ti("IE",e),isOpera:Ti("Opera",e),isFirefox:Ti(Mi,e),isSafari:Ti("Safari",e)}},Pi={unknown:function(){return Wi({current:undefined,version:Si.unknown()})},nu:Wi,edge:lt.constant("Edge"),chrome:lt.constant("Chrome"),ie:lt.constant("IE"),opera:lt.constant("Opera"),firefox:lt.constant(Mi),safari:lt.constant("Safari")},Ni="Windows",Oi="Android",Di="Solaris",Ai="FreeBSD",Bi=function(t,e){return function(){return e===t}},Li=function(t){var e=t.current;return{current:e,version:t.version,isWindows:Bi(Ni,e),isiOS:Bi("iOS",e),isAndroid:Bi(Oi,e),isOSX:Bi("OSX",e),isLinux:Bi("Linux",e),isSolaris:Bi(Di,e),isFreeBSD:Bi(Ai,e)}},zi={unknown:function(){return Li({current:undefined,version:Si.unknown()})},nu:Li,windows:lt.constant(Ni),ios:lt.constant("iOS"),android:lt.constant(Oi),linux:lt.constant("Linux"),osx:lt.constant("OSX"),solaris:lt.constant(Di),freebsd:lt.constant(Ai)},Ii=function(t,e){var n=String(e).toLowerCase();return Ht.find(t,function(t){return t.search(n)})},Fi=function(t,e){return Ii(t,e).map(function(t){var n=Si.detect(t.versionRegexes,e);return{current:t.name,version:n}})},Vi=function(t,e){return Ii(t,e).map(function(t){var n=Si.detect(t.versionRegexes,e);return{current:t.name,version:n}})},Ui=function(t,e){return-1!==t.indexOf(e)},qi=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ji=function(t){return function(e){return Ui(e,t)}},$i=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(t){return Ui(t,"edge/")&&Ui(t,"chrome")&&Ui(t,"safari")&&Ui(t,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,qi],search:function(t){return Ui(t,"chrome")&&!Ui(t,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(t){return Ui(t,"msie")||Ui(t,"trident")}},{name:"Opera",versionRegexes:[qi,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ji("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ji("firefox")},{name:"Safari",versionRegexes:[qi,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(t){return(Ui(t,"safari")||Ui(t,"mobile/"))&&Ui(t,"applewebkit")}}],Yi=[{name:"Windows",search:ji("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(t){return Ui(t,"iphone")||Ui(t,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ji("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:ji("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ji("linux"),versionRegexes:[]},{name:"Solaris",search:ji("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ji("freebsd"),versionRegexes:[]}],Xi={browsers:lt.constant($i),oses:lt.constant(Yi)},Ji=function(t){var e,n,i,r,o,s,a,l,u,c,d,f=Xi.browsers(),h=Xi.oses(),m=Fi(f,t).fold(Pi.unknown,Pi.nu),g=Vi(h,t).fold(zi.unknown,zi.nu);return{browser:m,os:g,deviceType:(n=m,i=t,r=(e=g).isiOS()&&!0===/ipad/i.test(i),o=e.isiOS()&&!r,s=e.isAndroid()&&3===e.version.major,a=e.isAndroid()&&4===e.version.major,l=r||s||a&&!0===/mobile/i.test(i),u=e.isiOS()||e.isAndroid(),c=u&&!l,d=n.isSafari()&&e.isiOS()&&!1===/safari/i.test(i),{isiPad:lt.constant(r),isiPhone:lt.constant(o),isTablet:lt.constant(l),isPhone:lt.constant(c),isTouch:lt.constant(u),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:lt.constant(d)})}},Gi=ei(function(){var t=navigator.userAgent;return Ji(t)}),Ki=ri,Zi=ii,Qi=function(t){return t.nodeType!==Ki&&t.nodeType!==Zi||0===t.childElementCount},tr={all:function(t,e){var n=e===undefined?document:e.dom();return Qi(n)?[]:Ht.map(n.querySelectorAll(t),ti.fromDom)},is:function(t,e){var n=t.dom();if(n.nodeType!==Ki)return!1;if(n.matches!==undefined)return n.matches(e);if(n.msMatchesSelector!==undefined)return n.msMatchesSelector(e);if(n.webkitMatchesSelector!==undefined)return n.webkitMatchesSelector(e);if(n.mozMatchesSelector!==undefined)return n.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},one:function(t,e){var n=e===undefined?document:e.dom();return Qi(n)?mt.none():mt.from(n.querySelector(t)).map(ti.fromDom)}},er=(Gi().browser.isIE(),Ci.immutable("element","offset"),function(t,e){return tr.all(e,t)}),nr=R.trim,ir=function(t){return function(e){if(e&&1===e.nodeType){if(e.contentEditable===t)return!0;if(e.getAttribute("data-mce-contenteditable")===t)return!0}return!1}},rr=ir("true"),or=ir("false"),sr=function(t,e,n,i,r){return{type:t,title:e,url:n,level:i,attach:r}},ar=function(t){return t.innerText||t.textContent},lr=function(t){return(e=t)&&"A"===e.nodeName&&(e.id||e.name)&&cr(t);var e},ur=function(t){return t&&/^(H[1-6])$/.test(t.nodeName)},cr=function(t){return function(t){for(;t=t.parentNode;){var e=t.contentEditable;if(e&&"inherit"!==e)return rr(t)}return!1}(t)&&!or(t)},dr=function(t){return ur(t)&&cr(t)},fr=function(t){var e,n,i=(e=t).id?e.id:Zn("h");return sr("header",ar(t),"#"+i,ur(n=t)?parseInt(n.nodeName.substr(1),10):0,function(){t.id=i})},hr=function(t){var e=t.id||t.name,n=ar(t);return sr("anchor",n||"#"+e,"#"+e,0,lt.noop)},mr=function(t){var e,n;return e="h1,h2,h3,h4,h5,h6,a:not([href])",n=t,Ht.map(er(ti.fromDom(n),e),function(t){return t.dom()})},gr=function(t){return nr(t.title).length>0},pr=function(t){var e,n,i=mr(t);return Ht.filter((n=i,Ht.map(Ht.filter(n,dr),fr)).concat((e=i,Ht.map(Ht.filter(e,lr),hr))),gr)},vr={},yr=function(t){return{title:t.title,value:{title:{raw:t.title},url:t.url,attach:t.attach}}},br=function(t,e){return{title:t,value:{title:t,url:e,attach:lt.noop}}},xr=function(t,e,n){var i=e in t?t[e]:n;return!1===i?null:i},wr=function(t,e,n,i){var r,o,s,a,l={title:"-"},u=function(t){var i=t.hasOwnProperty(n)?t[n]:[],r=Ht.filter(i,function(t){return n=t,i=e,!Ht.exists(i,function(t){return t.url===n});var n,i});return R.map(r,function(t){return{title:t,value:{title:t,url:t,attach:lt.noop}}})},c=function(t){var n,i=Ht.filter(e,function(e){return e.type===t});return n=i,R.map(n,yr)};return!1===i.typeahead_urls?[]:"file"===n?(r=[_r(t,u(vr)),_r(t,c("header")),_r(t,(o=c("anchor"),s=xr(i,"anchor_top","#top"),a=xr(i,"anchor_bottom","#bottom"),null!==s&&o.unshift(br("<top>",s)),null!==a&&o.push(br("<bottom>",a)),o))],Ht.foldl(r,function(t,e){return 0===t.length||0===e.length?t.concat(e):t.concat(l,e)},[])):_r(t,u(vr))},_r=function(t,e){var n=t.toLowerCase(),i=R.grep(e,function(t){return-1!==t.title.toLowerCase().indexOf(n)});return 1===i.length&&i[0].title===t?[]:i},Rr=function(t,e,n,i){var r=function(r){var o=pr(n),s=wr(r,o,i,e);t.showAutoComplete(s,r)};t.on("autocomplete",function(){r(t.value())}),t.on("selectitem",function(e){var n=e.value;t.value(n.url);var r,o=(r=n.title).raw?r.raw:r;"image"===i?t.fire("change",{meta:{alt:o,attach:n.attach}}):t.fire("change",{meta:{text:o,attach:n.attach}}),t.focus()}),t.on("click",function(e){0===t.value().length&&"INPUT"===e.target.nodeName&&r("")}),t.on("PostRender",function(){t.getRoot().on("submit",function(e){var n,r,o;e.isDefaultPrevented()||(n=t.value(),o=vr[r=i],/^https?/.test(n)&&(o?-1===Ht.indexOf(o,n)&&(vr[r]=o.slice(0,5).concat(n)):vr[r]=[n]))})})},Cr=function(t,e,n){var i=e.filepicker_validator_handler;i&&t.state.on("change:value",function(e){var r;0!==(r=e.value).length?i({url:r,type:n},function(e){var n,i,r,o=(i=(n=e).status,r=n.message,"valid"===i?{status:"ok",message:r}:"unknown"===i?{status:"warn",message:r}:"invalid"===i?{status:"warn",message:r}:{status:"none",message:""});t.statusMessage(o.message),t.statusLevel(o.status)}):t.statusLevel("none")})},kr=Ln.extend({Statics:{clearHistory:function(){vr={}}},init:function(t){var e,n,i,r=this,o=window.tinymce?window.tinymce.activeEditor:T.activeEditor,s=o.settings,a=t.filetype;t.spellcheck=!1,(i=s.file_picker_types||s.file_browser_callback_types)&&(i=R.makeMap(i,/[, ]/)),i&&!i[a]||(!(n=s.file_picker_callback)||i&&!i[a]?!(n=s.file_browser_callback)||i&&!i[a]||(e=function(){n(r.getEl("inp").id,r.value(),a,window)}):e=function(){var t=r.fire("beforecall").meta;t=R.extend({filetype:a},t),n.call(o,function(t,e){r.value(t).fire("change",{meta:e})},r.value(),t)}),e&&(t.icon="browse",t.onaction=e),r._super(t),r.classes.add("filepicker"),Rr(r,s,o.getBody(),a),Cr(r,s,a)}}),Er=Pn.extend({recalc:function(t){var e=t.layoutRect(),n=t.paddingBox;t.items().filter(":visible").each(function(t){t.layoutRect({x:n.left,y:n.top,w:e.innerW-n.right-n.left,h:e.innerH-n.top-n.bottom}),t.recalc&&t.recalc()})}}),Hr=Pn.extend({recalc:function(t){var e,n,i,r,o,s,a,l,u,c,d,f,h,m,g,p,v,y,b,x,w,_,R,C,k,E,H,S,M,T,W,P,N,O,D,A,B,L=[],z=Math.max,I=Math.min;for(i=t.items().filter(":visible"),r=t.layoutRect(),o=t.paddingBox,s=t.settings,f=t.isRtl()?s.direction||"row-reversed":s.direction,a=s.align,l=t.isRtl()?s.pack||"end":s.pack,u=s.spacing||0,"row-reversed"!==f&&"column-reverse"!==f||(i=i.set(i.toArray().reverse()),f=f.split("-")[0]),"column"===f?(C="y",_="h",R="minH",k="maxH",H="innerH",E="top",S="deltaH",M="contentH",O="left",P="w",T="x",W="innerW",N="minW",D="right",A="deltaW",B="contentW"):(C="x",_="w",R="minW",k="maxW",H="innerW",E="left",S="deltaW",M="contentW",O="top",P="h",T="y",W="innerH",N="minH",D="bottom",A="deltaH",B="contentH"),d=r[H]-o[E]-o[E],w=c=0,e=0,n=i.length;e<n;e++)m=(h=i[e]).layoutRect(),d-=e<n-1?u:0,(g=h.settings.flex)>0&&(c+=g,m[k]&&L.push(h),m.flex=g),d-=m[R],(p=o[O]+m[N]+o[D])>w&&(w=p);if((b={})[R]=d<0?r[R]-d+r[S]:r[H]-d+r[S],b[N]=w+r[A],b[M]=r[H]-d,b[B]=w,b.minW=I(b.minW,r.maxW),b.minH=I(b.minH,r.maxH),b.minW=z(b.minW,r.startMinWidth),b.minH=z(b.minH,r.startMinHeight),!r.autoResize||b.minW===r.minW&&b.minH===r.minH){for(y=d/c,e=0,n=L.length;e<n;e++)v=(m=(h=L[e]).layoutRect())[k],(p=m[R]+m.flex*y)>v?(d-=m[k]-m[R],c-=m.flex,m.flex=0,m.maxFlexSize=v):m.maxFlexSize=0;for(y=d/c,x=o[E],b={},0===c&&("end"===l?x=d+o[E]:"center"===l?(x=Math.round(r[H]/2-(r[H]-d)/2)+o[E])<0&&(x=o[E]):"justify"===l&&(x=o[E],u=Math.floor(d/(i.length-1)))),b[T]=o[O],e=0,n=i.length;e<n;e++)p=(m=(h=i[e]).layoutRect()).maxFlexSize||m[R],"center"===a?b[T]=Math.round(r[W]/2-m[P]/2):"stretch"===a?(b[P]=z(m[N]||0,r[W]-o[O]-o[D]),b[T]=o[O]):"end"===a&&(b[T]=r[W]-m[P]-o.top),m.flex>0&&(p+=m.flex*y),b[_]=p,b[C]=x,h.layoutRect(b),h.recalc&&h.recalc(),x+=p+u}else if(b.w=b.minW,b.h=b.minH,t.layoutRect(b),this.recalc(t),null===t._lastRect){var F=t.parent();F&&(F._lastRect=null,F.recalc())}}}),Sr=Wn.extend({Defaults:{containerClass:"flow-layout",controlClass:"flow-layout-item",endClass:"break"},recalc:function(t){t.items().filter(":visible").each(function(t){t.recalc&&t.recalc()})},isNative:function(){return!0}}),Mr=function(t,e){return tr.one(e,t)},Tr=function(t,e){return function(){t.execCommand("mceToggleFormat",!1,e)}},Wr=function(t,e){return function(){var n=this;t.formatter?t.formatter.formatChanged(e,function(t){n.active(t)}):t.on("init",function(){t.formatter.formatChanged(e,function(t){n.active(t)})})}},Pr=function(t){t.addMenuItem("align",{text:"Align",menu:[{text:"Left",icon:"alignleft",onclick:Tr(t,"alignleft")},{text:"Center",icon:"aligncenter",onclick:Tr(t,"aligncenter")},{text:"Right",icon:"alignright",onclick:Tr(t,"alignright")},{text:"Justify",icon:"alignjustify",onclick:Tr(t,"alignjustify")}]}),R.each({alignleft:["Align left","JustifyLeft"],aligncenter:["Align center","JustifyCenter"],alignright:["Align right","JustifyRight"],alignjustify:["Justify","JustifyFull"],alignnone:["No alignment","JustifyNone"]},function(e,n){t.addButton(n,{active:!1,tooltip:e[0],cmd:e[1],onPostRender:Wr(t,n)})})},Nr=function(t){return function(e,n){return mt.from(n).map(ti.fromDom).filter(fi.isElement).bind(function(n){return function(t,e,n){for(;n!==e;){if(n.style[t]){var i=n.style[t];return""!==i?mt.some(i):mt.none()}n=n.parentNode}return mt.none()}(t,e,n.dom()).or((i=t,r=n.dom(),mt.from(l.DOM.getStyle(r,i,!0))));var i,r}).getOr("")}},Or={getFontSize:Nr("fontSize"),getFontFamily:lt.compose(function(t){return t.replace(/[\'\"\\]/g,"").replace(/,\s+/g,",")},Nr("fontFamily")),toPt:function(t,e){return/[0-9.]+px$/.test(t)?(n=72*parseInt(t,10)/96,i=e||0,r=Math.pow(10,i),Math.round(n*r)/r+"pt"):t;var n,i,r}},Dr=function(t){return t?t.split(",")[0]:""},Ar=function(t,e){return function(){var n=this;t.on("init nodeChange",function(i){var r,o,s,a=Or.getFontFamily(t.getBody(),i.element),l=(r=e,o=a,R.each(r,function(t){t.value.toLowerCase()===o.toLowerCase()&&(s=t.value)}),R.each(r,function(t){s||Dr(t.value).toLowerCase()!==Dr(o).toLowerCase()||(s=t.value)}),s);n.value(l||null),!l&&a&&n.text(Dr(a))})}},Br=function(t){t.addButton("fontselect",function(){var e,n=(e=function(t){for(var e=(t=t.replace(/;$/,"").split(";")).length;e--;)t[e]=t[e].split("=");return t}(t.settings.font_formats||"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"),R.map(e,function(t){return{text:{raw:t[0]},value:t[1],textStyle:-1===t[1].indexOf("dings")?"font-family:"+t[1]:""}}));return{type:"listbox",text:"Font Family",tooltip:"Font Family",values:n,fixedWidth:!0,onPostRender:Ar(t,n),onselect:function(e){e.control.settings.value&&t.execCommand("FontName",!1,e.control.settings.value)}}})},Lr=function(t){Br(t)},zr=function(t,e,n){var i;return R.each(t,function(t){t.value===n?i=n:t.value===e&&(i=e)}),i},Ir=function(t){t.addButton("fontsizeselect",function(){var e,n,i,r=(e=t.settings.fontsize_formats||"8pt 10pt 12pt 14pt 18pt 24pt 36pt",R.map(e.split(" "),function(t){var e=t,n=t,i=t.split("=");return i.length>1&&(e=i[0],n=i[1]),{text:e,value:n}}));return{type:"listbox",text:"Font Sizes",tooltip:"Font Sizes",values:r,fixedWidth:!0,onPostRender:(n=t,i=r,function(){var t=this;n.on("init nodeChange",function(e){var r,o,s,a;if(r=Or.getFontSize(n.getBody(),e.element))for(s=3;!a&&s>=0;s--)o=Or.toPt(r,s),a=zr(i,o,r);t.value(a||null),a||t.text(o)})}),onclick:function(e){e.control.settings.value&&t.execCommand("FontSize",!1,e.control.settings.value)}}})},Fr=function(t){Ir(t)},Vr=function(t,e){var n=e.length;return R.each(e,function(e){e.menu&&(e.hidden=0===Vr(t,e.menu));var i=e.format;i&&(e.hidden=!t.formatter.canApply(i)),e.hidden&&n--}),n},Ur=function(t,e){var n=e.items().length;return e.items().each(function(e){e.menu&&e.visible(Ur(t,e.menu)>0),!e.menu&&e.settings.menu&&e.visible(Vr(t,e.settings.menu)>0);var i=e.settings.format;i&&e.visible(t.formatter.canApply(i)),e.visible()||n--}),n},qr=function(t){var e,n,i,r,o,s,a,l,u=(n=0,i=[],r=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],o=function(t){var e=[];if(t)return R.each(t,function(t){var r={text:t.title,icon:t.icon};if(t.items)r.menu=o(t.items);else{var s=t.format||"custom"+n++;t.format||(t.name=s,i.push(t)),r.format=s,r.cmd=t.cmd}e.push(r)}),e},(e=t).on("init",function(){R.each(i,function(t){e.formatter.register(t.name,t)})}),{type:"menu",items:e.settings.style_formats_merge?e.settings.style_formats?o(r.concat(e.settings.style_formats)):o(r):o(e.settings.style_formats||r),onPostRender:function(t){e.fire("renderFormatsMenu",{control:t.control})},itemDefaults:{preview:!0,textStyle:function(){if(this.settings.format)return e.formatter.getCssText(this.settings.format)},onPostRender:function(){var t=this;t.parent().on("show",function(){var n,i;(n=t.settings.format)&&(t.disabled(!e.formatter.canApply(n)),t.active(e.formatter.match(n))),(i=t.settings.cmd)&&t.active(e.queryCommandState(i))})},onclick:function(){this.settings.format&&Tr(e,this.settings.format)(),this.settings.cmd&&e.execCommand(this.settings.cmd)}}});s=u,t.addMenuItem("formats",{text:"Formats",menu:s}),l=u,(a=t).addButton("styleselect",{type:"menubutton",text:"Formats",menu:l,onShowMenu:function(){a.settings.style_formats_autohide&&Ur(a,this.menu)}})},jr=function(t,e){return function(){var n,i,r,o=[];return R.each(e,function(e){o.push({text:e[0],value:e[1],textStyle:function(){return t.formatter.getCssText(e[1])}})}),{type:"listbox",text:e[0][0],values:o,fixedWidth:!0,onselect:function(e){if(e.control){var n=e.control.value();Tr(t,n)()}},onPostRender:(n=t,i=o,function(){var t=this;n.on("nodeChange",function(e){var o=n.formatter,s=null;R.each(e.parents,function(t){if(R.each(i,function(e){if(r?o.matchNode(t,r,{value:e.value})&&(s=e.value):o.matchNode(t,e.value)&&(s=e.value),s)return!1}),s)return!1}),t.value(s)})})}}},$r=function(t){var e,n,i=function(t){for(var e=(t=t.replace(/;$/,"").split(";")).length;e--;)t[e]=t[e].split("=");return t}(t.settings.block_formats||"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre");t.addMenuItem("blockformats",{text:"Blocks",menu:(e=t,n=i,R.map(n,function(t){return{text:t[0],onclick:Tr(e,t[1]),textStyle:function(){return e.formatter.getCssText(t[1])}}}))}),t.addButton("formatselect",jr(t,i))},Yr=function(t,e){var n,i;if("string"==typeof e)i=e.split(" ");else if(R.isArray(e))return Ht.flatten(R.map(e,function(e){return Yr(t,e)}));return n=R.grep(i,function(e){return"|"===e||e in t.menuItems}),R.map(n,function(e){return"|"===e?{text:"-"}:t.menuItems[e]})},Xr=function(t){return t&&"-"===t.text},Jr=function(t){var e=Ht.filter(t,function(t,e,n){return!Xr(t)||!Xr(n[e-1])});return Ht.filter(e,function(t,e,n){return!Xr(t)||e>0&&e<n.length-1})},Gr=function(t){var e,n,i,r,o=t.settings.insert_button_items;return Jr(o?Yr(t,o):(e=t,n="insert",i=[{text:"-"}],r=R.grep(e.menuItems,function(t){return t.context===n}),R.each(r,function(t){"before"===t.separator&&i.push({text:"|"}),t.prependToContext?i.unshift(t):i.push(t),"after"===t.separator&&i.push({text:"|"})}),i))},Kr=function(t){var e;(e=t).addButton("insert",{type:"menubutton",icon:"insert",menu:[],oncreatemenu:function(){this.menu.add(Gr(e)),this.menu.renderNew()}})},Zr=function(t){var e,n,i;e=t,R.each({bold:"Bold",italic:"Italic",underline:"Underline",strikethrough:"Strikethrough",subscript:"Subscript",superscript:"Superscript"},function(t,n){e.addButton(n,{active:!1,tooltip:t,onPostRender:Wr(e,n),onclick:Tr(e,n)})}),n=t,R.each({outdent:["Decrease indent","Outdent"],indent:["Increase indent","Indent"],cut:["Cut","Cut"],copy:["Copy","Copy"],paste:["Paste","Paste"],help:["Help","mceHelp"],selectall:["Select all","SelectAll"],visualaid:["Visual aids","mceToggleVisualAid"],newdocument:["New document","mceNewDocument"],removeformat:["Clear formatting","RemoveFormat"],remove:["Remove","Delete"]},function(t,e){n.addButton(e,{tooltip:t[0],cmd:t[1]})}),i=t,R.each({blockquote:["Blockquote","mceBlockQuote"],subscript:["Subscript","Subscript"],superscript:["Superscript","Superscript"]},function(t,e){i.addButton(e,{active:!1,tooltip:t[0],cmd:t[1],onPostRender:Wr(i,e)})})},Qr=function(t){var e;Zr(t),e=t,R.each({bold:["Bold","Bold","Meta+B"],italic:["Italic","Italic","Meta+I"],underline:["Underline","Underline","Meta+U"],strikethrough:["Strikethrough","Strikethrough"],subscript:["Subscript","Subscript"],superscript:["Superscript","Superscript"],removeformat:["Clear formatting","RemoveFormat"],newdocument:["New document","mceNewDocument"],cut:["Cut","Cut","Meta+X"],copy:["Copy","Copy","Meta+C"],paste:["Paste","Paste","Meta+V"],selectall:["Select all","SelectAll","Meta+A"]},function(t,n){e.addMenuItem(n,{text:t[0],icon:n,shortcut:t[2],cmd:t[1]})}),e.addMenuItem("codeformat",{text:"Code",icon:"code",onclick:Tr(e,"code")})},to=function(t,e){return function(){var n=this,i=function(){var n="redo"===e?"hasRedo":"hasUndo";return!!t.undoManager&&t.undoManager[n]()};n.disabled(!i()),t.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",function(){n.disabled(t.readonly||!i())})}},eo=function(t){var e,n;(e=t).addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onPostRender:to(e,"undo"),cmd:"undo"}),e.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onPostRender:to(e,"redo"),cmd:"redo"}),(n=t).addButton("undo",{tooltip:"Undo",onPostRender:to(n,"undo"),cmd:"undo"}),n.addButton("redo",{tooltip:"Redo",onPostRender:to(n,"redo"),cmd:"redo"})},no=function(t){var e,n;(e=t).addMenuItem("visualaid",{text:"Visual aids",selectable:!0,onPostRender:(n=e,function(){var t=this;n.on("VisualAid",function(e){t.active(e.hasVisual)}),t.active(n.hasVisual)}),cmd:"mceToggleVisualAid"})},io={setup:function(t){var e;t.rtl&&(ue.rtl=!0),t.on("mousedown",function(){Ve.hideAll()}),(e=t).settings.ui_container&&(a.container=Mr(ti.fromDom(document.body),e.settings.ui_container).fold(lt.constant(null),function(t){return t.dom()})),ve.tooltips=!a.iOS,ue.translate=function(t){return T.translate(t)},$r(t),Pr(t),Qr(t),eo(t),Fr(t),Lr(t),qr(t),no(t),Kr(t)}},ro=Pn.extend({recalc:function(t){var e,n,i,r,o,s,a,l,u,c,d,f,h,m,g,p,v,y,b,x,w,_,R,C,k,E,H,S,M=[],T=[];e=t.settings,r=t.items().filter(":visible"),o=t.layoutRect(),i=e.columns||Math.ceil(Math.sqrt(r.length)),n=Math.ceil(r.length/i),y=e.spacingH||e.spacing||0,b=e.spacingV||e.spacing||0,x=e.alignH||e.align,w=e.alignV||e.align,p=t.paddingBox,S="reverseRows"in e?e.reverseRows:t.isRtl(),x&&"string"==typeof x&&(x=[x]),w&&"string"==typeof w&&(w=[w]);for(d=0;d<i;d++)M.push(0);for(f=0;f<n;f++)T.push(0);for(f=0;f<n;f++)for(d=0;d<i&&(c=r[f*i+d]);d++)C=(u=c.layoutRect()).minW,k=u.minH,M[d]=C>M[d]?C:M[d],T[f]=k>T[f]?k:T[f];for(E=o.innerW-p.left-p.right,_=0,d=0;d<i;d++)_+=M[d]+(d>0?y:0),E-=(d>0?y:0)+M[d];for(H=o.innerH-p.top-p.bottom,R=0,f=0;f<n;f++)R+=T[f]+(f>0?b:0),H-=(f>0?b:0)+T[f];if(_+=p.left+p.right,R+=p.top+p.bottom,(l={}).minW=_+(o.w-o.innerW),l.minH=R+(o.h-o.innerH),l.contentW=l.minW-o.deltaW,l.contentH=l.minH-o.deltaH,l.minW=Math.min(l.minW,o.maxW),l.minH=Math.min(l.minH,o.maxH),l.minW=Math.max(l.minW,o.startMinWidth),l.minH=Math.max(l.minH,o.startMinHeight),!o.autoResize||l.minW===o.minW&&l.minH===o.minH){var W;o.autoResize&&((l=t.layoutRect(l)).contentW=l.minW-o.deltaW,l.contentH=l.minH-o.deltaH),W="start"===e.packV?0:H>0?Math.floor(H/n):0;var P=0,N=e.flexWidths;if(N)for(d=0;d<N.length;d++)P+=N[d];else P=i;var O=E/P;for(d=0;d<i;d++)M[d]+=N?N[d]*O:O;for(m=p.top,f=0;f<n;f++){for(h=p.left,a=T[f]+W,d=0;d<i&&(c=r[S?f*i+i-1-d:f*i+d]);d++)g=c.settings,u=c.layoutRect(),s=Math.max(M[d],u.startMinWidth),u.x=h,u.y=m,"center"===(v=g.alignH||(x?x[d]||x[0]:null))?u.x=h+s/2-u.w/2:"right"===v?u.x=h+s-u.w:"stretch"===v&&(u.w=s),"center"===(v=g.alignV||(w?w[d]||w[0]:null))?u.y=m+a/2-u.h/2:"bottom"===v?u.y=m+a-u.h:"stretch"===v&&(u.h=a),c.layoutRect(u),h+=s+y,c.recalc&&c.recalc();m+=a+b}}else if(l.w=l.minW,l.h=l.minH,t.layoutRect(l),this.recalc(t),null===t._lastRect){var D=t.parent();D&&(D._lastRect=null,D.recalc())}}}),oo=ve.extend({renderHtml:function(){var t=this;return t.classes.add("iframe"),t.canFocus=!1,'<iframe id="'+t._id+'" class="'+t.classes+'" tabindex="-1" src="'+(t.settings.url||"javascript:''")+'" frameborder="0"></iframe>'},src:function(t){this.getEl().src=t},html:function(t,e){var n=this,i=this.getEl().contentWindow.document.body;return i?(i.innerHTML=t,e&&e()):u.setTimeout(function(){n.html(t)}),this}}),so=ve.extend({init:function(t){this._super(t),this.classes.add("widget").add("infobox"),this.canFocus=!1},severity:function(t){this.classes.remove("error"),this.classes.remove("warning"),this.classes.remove("success"),this.classes.add(t)},help:function(t){this.state.set("help",t)},renderHtml:function(){var t=this,e=t.classPrefix;return'<div id="'+t._id+'" class="'+t.classes+'"><div id="'+t._id+'-body">'+t.encode(t.state.get("text"))+'<button role="button" tabindex="-1"><i class="'+e+"ico "+e+'i-help"></i></button></div></div>'},bindStates:function(){var t=this;return t.state.on("change:text",function(e){t.getEl("body").firstChild.data=t.encode(e.value),t.state.get("rendered")&&t.updateLayoutRect()}),t.state.on("change:help",function(e){t.classes.toggle("has-help",e.value),t.state.get("rendered")&&t.updateLayoutRect()}),t._super()}}),ao=ve.extend({init:function(t){var e=this;e._super(t),e.classes.add("widget").add("label"),e.canFocus=!1,t.multiline&&e.classes.add("autoscroll"),t.strong&&e.classes.add("strong")},initLayoutRect:function(){var t=this,e=t._super();return t.settings.multiline&&(Mt.getSize(t.getEl()).width>e.maxW&&(e.minW=e.maxW,t.classes.add("multiline")),t.getEl().style.width=e.minW+"px",e.startMinH=e.h=e.minH=Math.min(e.maxH,Mt.getSize(t.getEl()).height)),e},repaint:function(){return this.settings.multiline||(this.getEl().style.lineHeight=this.layoutRect().h+"px"),this._super()},severity:function(t){this.classes.remove("error"),this.classes.remove("warning"),this.classes.remove("success"),this.classes.add(t)},renderHtml:function(){var t,e,n=this,i=n.settings.forId,r=n.settings.html?n.settings.html:n.encode(n.state.get("text"));return!i&&(e=n.settings.forName)&&(t=n.getRoot().find("#"+e)[0])&&(i=t._id),i?'<label id="'+n._id+'" class="'+n.classes+'"'+(i?' for="'+i+'"':"")+">"+r+"</label>":'<span id="'+n._id+'" class="'+n.classes+'">'+r+"</span>"},bindStates:function(){var t=this;return t.state.on("change:text",function(e){t.innerHtml(t.encode(e.value)),t.state.get("rendered")&&t.updateLayoutRect()}),t._super()}}),lo=Pe.extend({Defaults:{role:"toolbar",layout:"flow"},init:function(t){this._super(t),this.classes.add("toolbar")},postRender:function(){return this.items().each(function(t){t.classes.add("toolbar-item")}),this._super()}}),uo=lo.extend({Defaults:{role:"menubar",containerCls:"menubar",ariaRoot:!0,defaults:{type:"menubutton"}}}),co=Nn.extend({init:function(t){var e=this;e._renderOpen=!0,e._super(t),t=e.settings,e.classes.add("menubtn"),t.fixedWidth&&e.classes.add("fixed-width"),e.aria("haspopup",!0),e.state.set("menu",t.menu||e.render())},showMenu:function(t){var e,n=this;if(n.menu&&n.menu.visible()&&!1!==t)return n.hideMenu();n.menu||(e=n.state.get("menu")||[],n.classes.add("opened"),e.length?e={type:"menu",animate:!0,items:e}:(e.type=e.type||"menu",e.animate=!0),e.renderTo?n.menu=e.parent(n).show().renderTo():n.menu=Ce.create(e).parent(n).renderTo(),n.fire("createmenu"),n.menu.reflow(),n.menu.on("cancel",function(t){t.control.parent()===n.menu&&(t.stopPropagation(),n.focus(),n.hideMenu())}),n.menu.on("select",function(){n.focus()}),n.menu.on("show hide",function(t){t.control===n.menu&&(n.activeMenu("show"===t.type),n.classes.toggle("opened","show"===t.type)),n.aria("expanded","show"===t.type)}).fire("show")),n.menu.show(),n.menu.layoutRect({w:n.layoutRect().w}),n.menu.repaint(),n.menu.moveRel(n.getEl(),n.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"]),n.fire("showmenu")},hideMenu:function(){this.menu&&(this.menu.items().each(function(t){t.hideMenu&&t.hideMenu()}),this.menu.hide())},activeMenu:function(t){this.classes.toggle("active",t)},renderHtml:function(){var t,e=this,n=e._id,i=e.classPrefix,r=e.settings.icon,o=e.state.get("text"),s="";return(t=e.settings.image)?(r="none","string"!=typeof t&&(t=window.getSelection?t[0]:t[1]),t=" style=\"background-image: url('"+t+"')\""):t="",o&&(e.classes.add("btn-has-text"),s='<span class="'+i+'txt">'+e.encode(o)+"</span>"),r=e.settings.icon?i+"ico "+i+"i-"+r:"",e.aria("role",e.parent()instanceof uo?"menuitem":"button"),'<div id="'+n+'" class="'+e.classes+'" tabindex="-1" aria-labelledby="'+n+'"><button id="'+n+'-open" role="presentation" type="button" tabindex="-1">'+(r?'<i class="'+r+'"'+t+"></i>":"")+s+' <i class="'+i+'caret"></i></button></div>'},postRender:function(){var t=this;return t.on("click",function(e){e.control===t&&function(t,e){for(;t;){if(e===t)return!0;t=t.parentNode}return!1}(e.target,t.getEl())&&(t.focus(),t.showMenu(!e.aria),e.aria&&t.menu.items().filter(":visible")[0].focus())}),t.on("mouseenter",function(e){var n,i=e.control,r=t.parent();i&&r&&i instanceof co&&i.parent()===r&&(r.items().filter("MenuButton").each(function(t){t.hideMenu&&t!==i&&(t.menu&&t.menu.visible()&&(n=!0),t.hideMenu())}),n&&(i.focus(),i.showMenu()))}),t._super()},bindStates:function(){var t=this;return t.state.on("change:menu",function(){t.menu&&t.menu.remove(),t.menu=null}),t._super()},remove:function(){this._super(),this.menu&&this.menu.remove()}});function fo(t,e){var n,i,r=this,o=ue.classPrefix;r.show=function(s,a){function l(){n&&(Tt(t).append('<div class="'+o+"throbber"+(e?" "+o+"throbber-inline":"")+'"></div>'),a&&a())}return r.hide(),n=!0,s?i=u.setTimeout(l,s):l(),r},r.hide=function(){var e=t.lastChild;return u.clearTimeout(i),e&&-1!==e.className.indexOf("throbber")&&e.parentNode.removeChild(e),n=!1,r}}var ho=Ve.extend({Defaults:{defaultType:"menuitem",border:1,layout:"stack",role:"application",bodyRole:"menu",ariaRoot:!0},init:function(t){if(t.autohide=!0,t.constrainToViewport=!0,"function"==typeof t.items&&(t.itemsFactory=t.items,t.items=[]),t.itemDefaults)for(var e=t.items,n=e.length;n--;)e[n]=R.extend({},t.itemDefaults,e[n]);this._super(t),this.classes.add("menu"),t.animate&&11!==a.ie&&this.classes.add("animate")},repaint:function(){return this.classes.toggle("menu-align",!0),this._super(),this.getEl().style.height="",this.getEl("body").style.height="",this},cancel:function(){this.hideAll(),this.fire("select")},load:function(){var t,e=this;function n(){e.throbber&&(e.throbber.hide(),e.throbber=null)}e.settings.itemsFactory&&(e.throbber||(e.throbber=new fo(e.getEl("body"),!0),0===e.items().length?(e.throbber.show(),e.fire("loading")):e.throbber.show(100,function(){e.items().remove(),e.fire("loading")}),e.on("hide close",n)),e.requestTime=t=(new Date).getTime(),e.settings.itemsFactory(function(i){0!==i.length?e.requestTime===t&&(e.getEl().style.width="",e.getEl("body").style.width="",n(),e.items().remove(),e.getEl("body").innerHTML="",e.add(i),e.renderNew(),e.fire("loaded")):e.hide()}))},hideAll:function(){return this.find("menuitem").exec("hideMenu"),this._super()},preRender:function(){var t=this;return t.items().each(function(e){var n=e.settings;if(n.icon||n.image||n.selectable)return t._hasIcons=!0,!1}),t.settings.itemsFactory&&t.on("postrender",function(){t.settings.itemsFactory&&t.load()}),t.on("show hide",function(e){e.control===t&&("show"===e.type?u.setTimeout(function(){t.classes.add("in")},0):t.classes.remove("in"))}),t._super()}}),mo=co.extend({init:function(t){var e,n,i,r,o=this;o._super(t),t=o.settings,o._values=e=t.values,e&&("undefined"!=typeof t.value&&function s(e){for(var r=0;r<e.length;r++){if(n=e[r].selected||t.value===e[r].value)return i=i||e[r].text,o.state.set("value",e[r].value),!0;if(e[r].menu&&s(e[r].menu))return!0}}(e),!n&&e.length>0&&(i=e[0].text,o.state.set("value",e[0].value)),o.state.set("menu",e)),o.state.set("text",t.text||i),o.classes.add("listbox"),o.on("select",function(e){var n=e.control;r&&(e.lastControl=r),t.multiple?n.active(!n.active()):o.value(e.control.value()),r=n})},bindStates:function(){var t=this;return t.on("show",function(e){var n,i;n=e.control,i=t.value(),n instanceof ho&&n.items().each(function(t){t.hasMenus()||t.active(t.value()===i)})}),t.state.on("change:value",function(e){var n=function i(t,e){var n;if(t)for(var r=0;r<t.length;r++){if(t[r].value===e)return t[r];if(t[r].menu&&(n=i(t[r].menu,e)))return n}}(t.state.get("menu"),e.value);n?t.text(n.text):t.text(t.settings.text)}),t._super()}}),go=ve.extend({Defaults:{border:0,role:"menuitem"},init:function(t){var e,n=this;n._super(t),t=n.settings,n.classes.add("menu-item"),t.menu&&n.classes.add("menu-item-expand"),t.preview&&n.classes.add("menu-item-preview"),"-"!==(e=n.state.get("text"))&&"|"!==e||(n.classes.add("menu-item-sep"),n.aria("role","separator"),n.state.set("text","-")),t.selectable&&(n.aria("role","menuitemcheckbox"),n.classes.add("menu-item-checkbox"),t.icon="selected"),t.preview||t.selectable||n.classes.add("menu-item-normal"),n.on("mousedown",function(t){t.preventDefault()}),t.menu&&!t.ariaHideMenu&&n.aria("haspopup",!0)},hasMenus:function(){return!!this.settings.menu},showMenu:function(){var t,e=this,n=e.settings,i=e.parent();if(i.items().each(function(t){t!==e&&t.hideMenu()}),n.menu){(t=e.menu)?t.show():((t=n.menu).length?t={type:"menu",items:t}:t.type=t.type||"menu",i.settings.itemDefaults&&(t.itemDefaults=i.settings.itemDefaults),(t=e.menu=Ce.create(t).parent(e).renderTo()).reflow(),t.on("cancel",function(n){n.stopPropagation(),e.focus(),t.hide()}),t.on("show hide",function(t){t.control.items&&t.control.items().each(function(t){t.active(t.settings.selected)})}).fire("show"),t.on("hide",function(n){n.control===t&&e.classes.remove("selected")}),t.submenu=!0),t._parentMenu=i,t.classes.add("menu-sub");var r=t.testMoveRel(e.getEl(),e.isRtl()?["tl-tr","bl-br","tr-tl","br-bl"]:["tr-tl","br-bl","tl-tr","bl-br"]);t.moveRel(e.getEl(),r),t.rel=r,r="menu-sub-"+r,t.classes.remove(t._lastRel).add(r),t._lastRel=r,e.classes.add("selected"),e.aria("expanded",!0)}},hideMenu:function(){var t=this;return t.menu&&(t.menu.items().each(function(t){t.hideMenu&&t.hideMenu()}),t.menu.hide(),t.aria("expanded",!1)),t},renderHtml:function(){var t,e=this,n=e._id,i=e.settings,r=e.classPrefix,o=e.state.get("text"),s=e.settings.icon,l="",u=i.shortcut,c=e.encode(i.url);function d(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function f(t){var e=i.match||"";return e?t.replace(new RegExp(d(e),"gi"),function(t){return"!mce~match["+t+"]mce~match!"}):t}function h(t){return t.replace(new RegExp(d("!mce~match["),"g"),"<b>").replace(new RegExp(d("]mce~match!"),"g"),"</b>")}return s&&e.parent().classes.add("menu-has-icons"),i.image&&(l=" style=\"background-image: url('"+i.image+"')\""),u&&(u=function(t){var e,n,i={};for(i=a.mac?{alt:"&#x2325;",ctrl:"&#x2318;",shift:"&#x21E7;",meta:"&#x2318;"}:{meta:"Ctrl"},t=t.split("+"),e=0;e<t.length;e++)(n=i[t[e].toLowerCase()])&&(t[e]=n);return t.join("+")}(u)),s=r+"ico "+r+"i-"+(e.settings.icon||"none"),t="-"!==o?'<i class="'+s+'"'+l+"></i>\xa0":"",o=h(e.encode(f(o))),c=h(e.encode(f(c))),'<div id="'+n+'" class="'+e.classes+'" tabindex="-1">'+t+("-"!==o?'<span id="'+n+'-text" class="'+r+'text">'+o+"</span>":"")+(u?'<div id="'+n+'-shortcut" class="'+r+'menu-shortcut">'+u+"</div>":"")+(i.menu?'<div class="'+r+'caret"></div>':"")+(c?'<div class="'+r+'menu-item-link">'+c+"</div>":"")+"</div>"},postRender:function(){var t=this,e=t.settings,n=e.textStyle;if("function"==typeof n&&(n=n.call(this)),n){var i=t.getEl("text");i&&(i.setAttribute("style",n),t._textStyle=n)}return t.on("mouseenter click",function(n){n.control===t&&(e.menu||"click"!==n.type?(t.showMenu(),n.aria&&t.menu.focus(!0)):(t.fire("select"),u.requestAnimationFrame(function(){t.parent().hideAll()})))}),t._super(),t},hover:function(){return this.parent().items().each(function(t){t.classes.remove("selected")}),this.classes.toggle("selected",!0),this},active:function(t){return function(t,e){var n=t._textStyle;if(n){var i=t.getEl("text");i.setAttribute("style",n),e&&(i.style.color="",i.style.backgroundColor="")}}(this,t),void 0!==t&&this.aria("checked",t),this._super(t)},remove:function(){this._super(),this.menu&&this.menu.remove()}}),po=An.extend({Defaults:{classes:"radio",role:"radio"}}),vo=ve.extend({renderHtml:function(){var t=this,e=t.classPrefix;return t.classes.add("resizehandle"),"both"===t.settings.direction&&t.classes.add("resizehandle-both"),t.canFocus=!1,'<div id="'+t._id+'" class="'+t.classes+'"><i class="'+e+"ico "+e+'i-resize"></i></div>'},postRender:function(){var t=this;t._super(),t.resizeDragHelper=new Re(this._id,{start:function(){t.fire("ResizeStart")},drag:function(e){"both"!==t.settings.direction&&(e.deltaX=0),t.fire("Resize",e)},stop:function(){t.fire("ResizeEnd")}})},remove:function(){return this.resizeDragHelper&&this.resizeDragHelper.destroy(),this._super()}});function yo(t){var e="";if(t)for(var n=0;n<t.length;n++)e+='<option value="'+t[n]+'">'+t[n]+"</option>";return e}var bo=ve.extend({Defaults:{classes:"selectbox",role:"selectbox",options:[]},init:function(t){var e=this;e._super(t),e.settings.size&&(e.size=e.settings.size),e.settings.options&&(e._options=e.settings.options),e.on("keydown",function(t){var n;13===t.keyCode&&(t.preventDefault(),e.parents().reverse().each(function(t){if(t.toJSON)return n=t,!1}),e.fire("submit",{data:n.toJSON()}))})},options:function(t){return arguments.length?(this.state.set("options",t),this):this.state.get("options")},renderHtml:function(){var t,e=this,n="";return t=yo(e._options),e.size&&(n=' size = "'+e.size+'"'),'<select id="'+e._id+'" class="'+e.classes+'"'+n+">"+t+"</select>"},bindStates:function(){var t=this;return t.state.on("change:options",function(e){t.getEl().innerHTML=yo(e.value)}),t._super()}});function xo(t,e,n){return t<e&&(t=e),t>n&&(t=n),t}function wo(t,e,n){t.setAttribute("aria-"+e,n)}function _o(t,e){var n,i,r,o,s;"v"===t.settings.orientation?(r="top",i="height",n="h"):(r="left",i="width",n="w"),s=t.getEl("handle"),o=((t.layoutRect()[n]||100)-Mt.getSize(s)[i])*((e-t._minValue)/(t._maxValue-t._minValue))+"px",s.style[r]=o,s.style.height=t.layoutRect().h+"px",wo(s,"valuenow",e),wo(s,"valuetext",""+t.settings.previewFilter(e)),wo(s,"valuemin",t._minValue),wo(s,"valuemax",t._maxValue)}var Ro=ve.extend({init:function(t){var e=this;t.previewFilter||(t.previewFilter=function(t){return Math.round(100*t)/100}),e._super(t),e.classes.add("slider"),"v"===t.orientation&&e.classes.add("vertical"),e._minValue=gi.isNumber(t.minValue)?t.minValue:0,e._maxValue=gi.isNumber(t.maxValue)?t.maxValue:100,e._initValue=e.state.get("value")},renderHtml:function(){var t=this._id,e=this.classPrefix;return'<div id="'+t+'" class="'+this.classes+'"><div id="'+t+'-handle" class="'+e+'slider-handle" role="slider" tabindex="-1"></div></div>'},reset:function(){this.value(this._initValue).repaint()},postRender:function(){var t,e,n,i,r,o,s,a,l,u,c,d,f,h,m=this;t=m._minValue,e=m._maxValue,"v"===m.settings.orientation?(n="screenY",i="top",r="height",o="h"):(n="screenX",i="left",r="width",o="w"),m._super(),function(t,e){function n(n){var i,r,o;i=xo(i=(((i=m.value())+(o=t))/(e-o)+.05*n)*(e-(r=t))-r,t,e),m.value(i),m.fire("dragstart",{value:i}),m.fire("drag",{value:i}),m.fire("dragend",{value:i})}m.on("keydown",function(t){switch(t.keyCode){case 37:case 38:n(-1);break;case 39:case 40:n(1)}})}(t,e),s=t,a=e,l=m.getEl("handle"),m._dragHelper=new Re(m._id,{handle:m._id+"-handle",start:function(t){u=t[n],c=parseInt(m.getEl("handle").style[i],10),d=(m.layoutRect()[o]||100)-Mt.getSize(l)[r],m.fire("dragstart",{value:h})},drag:function(t){var e=t[n]-u;f=xo(c+e,0,d),l.style[i]=f+"px",h=s+f/d*(a-s),m.value(h),m.tooltip().text(""+m.settings.previewFilter(h)).show().moveRel(l,"bc tc"),m.fire("drag",{value:h})},stop:function(){m.tooltip().hide(),m.fire("dragend",{value:h})}})},repaint:function(){this._super(),_o(this,this.value())},bindStates:function(){var t=this;return t.state.on("change:value",function(e){_o(t,e.value)}),t._super()}}),Co=ve.extend({renderHtml:function(){return this.classes.add("spacer"),this.canFocus=!1,'<div id="'+this._id+'" class="'+this.classes+'"></div>'}}),ko=co.extend({Defaults:{classes:"widget btn splitbtn",role:"button"},repaint:function(){var t,e,n=this.getEl(),i=this.layoutRect();return this._super(),t=n.firstChild,e=n.lastChild,Tt(t).css({width:i.w-Mt.getSize(e).width,height:i.h-2}),Tt(e).css({height:i.h-2}),this},activeMenu:function(t){Tt(this.getEl().lastChild).toggleClass(this.classPrefix+"active",t)},renderHtml:function(){var t,e,n=this,i=n._id,r=n.classPrefix,o=n.state.get("icon"),s=n.state.get("text"),a=n.settings,l="";return(t=a.image)?(o="none","string"!=typeof t&&(t=window.getSelection?t[0]:t[1]),t=" style=\"background-image: url('"+t+"')\""):t="",o=a.icon?r+"ico "+r+"i-"+o:"",s&&(n.classes.add("btn-has-text"),l='<span class="'+r+'txt">'+n.encode(s)+"</span>"),e="boolean"==typeof a.active?' aria-pressed="'+a.active+'"':"",'<div id="'+i+'" class="'+n.classes+'" role="button"'+e+' tabindex="-1"><button type="button" hidefocus="1" tabindex="-1">'+(o?'<i class="'+o+'"'+t+"></i>":"")+l+'</button><button type="button" class="'+r+'open" hidefocus="1" tabindex="-1">'+(n._menuBtnText?(o?"\xa0":"")+n._menuBtnText:"")+' <i class="'+r+'caret"></i></button></div>'},postRender:function(){var t=this.settings.onclick;return this.on("click",function(e){var n=e.target;if(e.control===this)for(;n;){if(e.aria&&"down"!==e.aria.key||"BUTTON"===n.nodeName&&-1===n.className.indexOf("open"))return e.stopImmediatePropagation(),void(t&&t.call(this,e));n=n.parentNode}}),delete this.settings.onclick,this._super()}}),Eo=Sr.extend({Defaults:{containerClass:"stack-layout",controlClass:"stack-layout-item",endClass:"break"},isNative:function(){return!0}}),Ho=Oe.extend({Defaults:{layout:"absolute",defaults:{type:"panel"}},activateTab:function(t){var e;this.activeTabId&&(e=this.getEl(this.activeTabId),Tt(e).removeClass(this.classPrefix+"active"),e.setAttribute("aria-selected","false")),this.activeTabId="t"+t,(e=this.getEl("t"+t)).setAttribute("aria-selected","true"),Tt(e).addClass(this.classPrefix+"active"),this.items()[t].show().fire("showtab"),this.reflow(),this.items().each(function(e,n){t!==n&&e.hide()})},renderHtml:function(){var t=this,e=t._layout,n="",i=t.classPrefix;return t.preRender(),e.preRender(t),t.items().each(function(e,r){var o=t._id+"-t"+r;e.aria("role","tabpanel"),e.aria("labelledby",o),n+='<div id="'+o+'" class="'+i+'tab" unselectable="on" role="tab" aria-controls="'+e._id+'" aria-selected="false" tabIndex="-1">'+t.encode(e.settings.title)+"</div>"}),'<div id="'+t._id+'" class="'+t.classes+'" hidefocus="1" tabindex="-1"><div id="'+t._id+'-head" class="'+i+'tabs" role="tablist">'+n+'</div><div id="'+t._id+'-body" class="'+t.bodyClasses+'">'+e.renderHtml(t)+"</div></div>"},postRender:function(){var t=this;t._super(),t.settings.activeTab=t.settings.activeTab||0,t.activateTab(t.settings.activeTab),this.on("click",function(e){var n=e.target.parentNode;if(n&&n.id===t._id+"-head")for(var i=n.childNodes.length;i--;)n.childNodes[i]===e.target&&t.activateTab(i)})},initLayoutRect:function(){var t,e,n,i=this;e=(e=Mt.getSize(i.getEl("head")).width)<0?0:e,n=0,i.items().each(function(t){e=Math.max(e,t.layoutRect().minW),n=Math.max(n,t.layoutRect().minH)}),i.items().each(function(t){t.settings.x=0,t.settings.y=0,t.settings.w=e,t.settings.h=n,t.layoutRect({x:0,y:0,w:e,h:n})});var r=Mt.getSize(i.getEl("head")).height;return i.settings.minWidth=e,i.settings.minHeight=n+r,(t=i._super()).deltaH+=r,t.innerH=t.h-t.deltaH,t}}),So=ve.extend({init:function(t){var e=this;e._super(t),e.classes.add("textbox"),t.multiline?e.classes.add("multiline"):(e.on("keydown",function(t){var n;13===t.keyCode&&(t.preventDefault(),e.parents().reverse().each(function(t){if(t.toJSON)return n=t,!1}),e.fire("submit",{data:n.toJSON()}))}),e.on("keyup",function(t){e.state.set("value",t.target.value)}))},repaint:function(){var t,e,n,i,r,o=this,s=0;t=o.getEl().style,e=o._layoutRect,r=o._lastRepaintRect||{};var a=document;return!o.settings.multiline&&a.all&&(!a.documentMode||a.documentMode<=8)&&(t.lineHeight=e.h-s+"px"),i=(n=o.borderBox).left+n.right+8,s=n.top+n.bottom+(o.settings.multiline?8:0),e.x!==r.x&&(t.left=e.x+"px",r.x=e.x),e.y!==r.y&&(t.top=e.y+"px",r.y=e.y),e.w!==r.w&&(t.width=e.w-i+"px",r.w=e.w),e.h!==r.h&&(t.height=e.h-s+"px",r.h=e.h),o._lastRepaintRect=r,o.fire("repaint",{},!1),o},renderHtml:function(){var t,e,n=this,i=n.settings;return t={id:n._id,hidefocus:"1"},R.each(["rows","spellcheck","maxLength","size","readonly","min","max","step","list","pattern","placeholder","required","multiple"],function(e){t[e]=i[e]}),n.disabled()&&(t.disabled="disabled"),i.subtype&&(t.type=i.subtype),(e=Mt.create(i.multiline?"textarea":"input",t)).value=n.state.get("value"),e.className=n.classes,e.outerHTML},value:function(t){return arguments.length?(this.state.set("value",t),this):(this.state.get("rendered")&&this.state.set("value",this.getEl().value),this.state.get("value"))},postRender:function(){var t=this;t.getEl().value=t.state.get("value"),t._super(),t.$el.on("change",function(e){t.state.set("value",e.target.value),t.fire("change",e)})},bindStates:function(){var t=this;return t.state.on("change:value",function(e){t.getEl().value!==e.value&&(t.getEl().value=e.value)}),t.state.on("change:disabled",function(e){t.getEl().disabled=e.value}),t._super()},remove:function(){this.$el.off(),this._super()}}),Mo=function(){return{Selector:Ut,Collection:$t,ReflowQueue:te,Control:ue,Factory:Ce,KeyboardNavigation:Ee,Container:Pe,DragHelper:Re,Scrollable:Ne,Panel:Oe,Movable:ge,Resizable:De,FloatPanel:Ve,Window:Xe,MessageBox:Ge,Tooltip:pe,Widget:ve,Progress:ye,Notification:xe,Layout:Wn,AbsoluteLayout:Pn,Button:Nn,ButtonGroup:Dn,Checkbox:An,ComboBox:Ln,ColorBox:zn,PanelButton:In,ColorButton:Vn,ColorPicker:qn,Path:$n,ElementPath:Yn,FormItem:Xn,Form:Jn,FieldSet:Gn,FilePicker:kr,FitLayout:Er,FlexLayout:Hr,FlowLayout:Sr,FormatControls:io,GridLayout:ro,Iframe:oo,InfoBox:so,Label:ao,Toolbar:lo,MenuBar:uo,MenuButton:co,MenuItem:go,Throbber:fo,Menu:ho,ListBox:mo,Radio:po,ResizeHandle:vo,SelectBox:bo,Slider:Ro,Spacer:Co,SplitButton:ko,StackLayout:Eo,TabPanel:Ho,TextBox:So,DropZone:jn,BrowseButton:On}},To=function(t){t.ui?R.each(Mo(),function(e,n){t.ui[n]=e}):t.ui=Mo()};R.each(Mo(),function(t,e){Ce.add(e,t)}),To(window.tinymce?window.tinymce:{}),s.add("inlite",function(t){var e=Tn();return io.setup(t),Rn(t,e),Ke(t,e)})}();
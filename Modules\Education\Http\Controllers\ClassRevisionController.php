<?php

namespace Modules\Education\Http\Controllers;

use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\MoshafJuz;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\StudentRevisionPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStaatisticsRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class ClassRevisionController extends Controller
{

    public function __construct()
    {
        $this->middleware('writeCurrentClassReportOnly', ['only' => ['create']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index($id)
    {


        $surats = MoshafSurah::all();
        $hefzEvaluationOptions = EvaluationSchema::where('target', 'hefz')->first()->options;
        // return $this->studentReport(1);
        $request = request();


        $class = Classes::withTrashed()->find($id);

        // get the difference of students with hefz plan and those that those with no hefz plan for this ( $id ) class

        $studentswithApprovedStudyPlan = StudentHefzPlan::where('class_id', $id)->where('status', 'active')->count();
        $studentsWaitingForApproval = StudentHefzPlan::where('class_id', $id)->where('status', 'waiting_for_approval')->count();

//        dd($studentswithApprovedStudyPlan,$studentsWaitingForApproval);
        $class->loadMissing('students');

        // handling the wrong $id
        if (is_null($class)) {
            flash('Class ' . $id . ' does not exist');
            return redirect()->to("workplace/education/classes");
        }

        if (!is_null($class->deleted_at)) {

            // return redirect back and flush a message stating that the $class->class_code is archived
            flash('Class ' . $class->class_code . ' is archived and is not accessable. please try another class');
            return redirect()->to("workplace/education/classes");

        }
        $class = Classes::with('programs.settings')->findOrFail($id);
//        $class =  Classes::findOrFail($id);

        if ($request->from_date) {
            $from_date = Carbon::parse($request->from_date);
        } else {
            $from_date = Carbon::create(date('Y'), date('m'), 01);
        }

        if ($request->to_date) {
            $to_date = Carbon::parse($request->to_date);
        } else {
            $to_date = $from_date->copy()->addMonth();
        }
        $class_teachers = ClassTeacher::where('class_id', $class->id)
            ->where('end_date', null)->get();

        $class_programs = [];

        foreach ($class->programs as $key => $program) {
            $data = [];
            $data['info'] = $program;


            if (isset($program->setting['special_program_code']) && $program->setting['special_program_code'] == 'hefz') {
                $teacher = $class_teachers->filter(function ($teacher) use ($program) {
                    return (count($teacher->subjects) && $teacher->subjects->filter(function ($subject) use ($program) {
                            return $subject->program_id == $program->id;
                        })->count());
                })->first();

                if (!$teacher) {
                    return $this->errorNoTeacher($class_id);
                }

                if (!auth()->user()->can('view class_reports') && $teacher && $teacher->employee_id != auth()->user()->id) {
                    continue;
                }
                $data['teacher'] = $teacher;
                $data['type'] = 'program';

                if ($teacher) {
                    $data['timetable'] = $teacher->subjects()->where('program_id', $program->id)->first()->timetable;
                    if (!$data['timetable']) {
                        return $this->errorNoTimetable($class->id);
                    }
                    $data['class_teacher_subject_id'] = $teacher->subjects()->where('program_id', $program->id)->first()->id;

                    $last_report = ClassReport::where('class_id', $class->id)
                        ->where('program_id', $program->id)
                        ->where('status', 'completed')
                        ->get()->last();

                    $data['next_report_date'] = $this->getNextReportTime($last_report->class_time ?? null, $data['timetable'], $class);
                }
            } else {
                $data['type'] = 'subjects';
                if (isset($program->levels->find($program->pivot->program_level_id)->subjects)) {
                    foreach ($program->levels->find($program->pivot->program_level_id)->subjects as $index => $subject) {
                        $teacher = $class_teachers->filter(function ($teacher) use ($subject) {
                            return (count($teacher->subjects) && in_array($subject->id, $teacher->subjects->pluck('subject_id')->toArray()));
                        })->first();


                        if (!$teacher) {
                            return $this->errorNoTeacher($class->id);
                        }

                        if (!auth()->user()->can('view class_reports') && $teacher->employee_id != auth()->user()->id) {
                            continue;
                        }
                        $data['class_subjects'][$index] = $subject;
                        $data['class_subjects'][$index]['teacher'] = $teacher;
                        if ($teacher) {
                            $data['class_subjects'][$index]['timetable'] = $teacher->subjects()->where('subject_id', $subject->id)->first()->timetable;
                            if (!$data['class_subjects'][$index]['timetable']) {
                                return $this->errorNoTimetable($class->id);
                            }
                            $data['class_subjects'][$index]['class_teacher_subject_id'] = $teacher->subjects()->where('subject_id', $subject->id)->first()->id;
                            $last_report = ClassReport::where('class_id', $class->id)
                                ->where('subject_id', $subject->id)
                                ->where('status', 'completed')
                                ->get()->last();
                            $data['next_report_date'] = $this->getNextReportTime($last_report->class_time ?? null, $data['class_subjects'][$index]['timetable'], $class);
                        }
                    }
                }
                if (!isset($data['class_subjects']) || !count($data['class_subjects'])) {
                    continue;
                }
            }

            $class_programs[$key] = $data;
        }

        $report_summery = [];

        $class_subjects = [];

        $class_reports = ClassReport::where('class_id', $class->id)
            // ->where('class_time' , "<=" , $today)
            ->where('class_time', ">=", $from_date)
            ->where('class_time', "<=", $to_date)
            ->get();

        $classReportsIds = $class_reports->pluck('id')->toArray();
        for ($i = $from_date->copy(); $i <= date('Y/m/d') && $i <= $to_date->copy(); $i->addDay()) {
            $report_summery[$i->format("Y/m/d")]["y"] = $i->format("Y/m/d");
        }

        $class_subjects_reports = [];

        foreach ($class_reports as $key => $report) {
            $report_summery[$report->class_time->format("Y/m/d")]["y"] = $report->class_time->format("Y/m/d");
            if ($report->subject_id) {
                $class_subjects_reports['subject_' . $report->subject_id][$report->class_time->format("Y/m/d")] = $report;
                $report_summery[$report->class_time->format("Y/m/d")][$report->subject->title] = $report->attendace->where('attendance', "!=", "absent")->where('attendance', "!=", "excused")->count();
                if (!in_array($report->subject->title, $class_subjects)) {
                    $class_subjects[] = $report->subject->title;
                }
            }
            if ($report->program_id) {
                $class_subjects_reports['program_' . $report->program_id][$report->class_time->format("Y/m/d")] = $report;
                $report_summery[$report->class_time->format("Y/m/d")][$report->program->title] = $report->attendace->where('attendance', "!=", "absent")->where('attendance', "!=", "excused")->count();
                if (!in_array($report->program->title, $class_subjects)) {
                    $class_subjects[] = $report->program->title;
                }
            }
        }
        $teacher = null;
        $teacher_timetable = null;
        if ($class->teachers->where('employee_id', auth()->user()->id)->first()) {
            $teacher = $class->teachers->where('employee_id', auth()->user()->id)->first();
        }


        //Student Attendance

        $students = Student::whereHas('class', function ($q) use ($id) {
            $q->where('class_id', $id);
        })->pluck('id')->toArray();

        $students_full_details = Student::whereHas('class', function ($q) use ($id) {
            $q->where('class_id', $id);
        })->get();


        $year = Carbon::parse($request->from_date)->year;
        $month = Carbon::parse($request->from_date)->month;
        $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);


        $attendances = [];

        $attendance = StudentAttendance::whereIn('student_id', $students)
            ->whereIn('class_report_id', $classReportsIds)
            ->whereYear('class_time', '=', $year)
            ->whereMonth('class_time', '=', $month)
            ->get();

        if (count($attendance) != 0) {
            $attendances[] = $attendance;
        }


        // return $class_programs;
        return view('education::classes.reports.index', compact('attendances', 'days', 'year', 'month', 'current_day', 'class', 'teacher_timetable', 'teacher', 'from_date', 'to_date', 'report_summery', 'class_programs', 'class_subjects_reports', 'class_subjects', 'students_full_details', 'surats', 'hefzEvaluationOptions'));
    }

    public function studentRecords(Request $request, $id)
    {


        DB::connection()->enableQueryLog();

        if ($request->filled('studentId') || $request->filled('classDate')) {

            $planYearMonth = Carbon::parse($request->get('classDate'))->format('Y-m');


            $dateMonthArray = explode('-', $planYearMonth);
            $year = $dateMonthArray[0];
            $month = $dateMonthArray[1];

            $studentHefzRevision = StudentHefzReport::where('student_id', $request->get('studentId'))
                ->where('class_id', $id)
                ->whereYear('class_time', $year)
                ->whereMonth('class_time', $month)
                ->get();


            return \Yajra\DataTables\DataTables::of($studentHefzRevision)
                ->addIndexColumn()
                ->addColumn('from_surat', function ($reportDetails) use ($request) {
                    $surats = MoshafSurah::all();
                    foreach ($surats as $key => $surat)

                        if ($reportDetails->revision_from_surat == $surat->id) {
                            return $surat->name;
                        }


                })
                ->addColumn('to_surat', function ($reportDetails) use ($request) {
                    $surats = MoshafSurah::all();
                    foreach ($surats as $key => $surat)

                        if ($reportDetails->revision_to_surat == $surat->id) {
                            return $surat->name;
                        }


                })
                ->addColumn('grade', function ($reportDetails) use ($request) {
                    $evaluationTitle = EvaluationSchemaOption::where('id', $reportDetails->hefz_evaluation_id)->first()->title;

                    return $evaluationTitle;


                })
                ->addColumn('attendance', function ($reportDetails) use ($request) {
                    $attendance = AttendanceOption::where('id', $reportDetails->attendance_id)->first()->title;
                    return $attendance;


                })
                ->addColumn('page', function ($reportDetails) use ($request) {

                    $numberofPages = DB::select("select *,abs(first_page-last_page) as pageCount
         from
         (select id,page_number as first_page
                   from moshaf_pages
                   where (surah_id = :startSurahId and first_ayah <= :startAyah)
                   order by page_number desc
                   limit 1) A
                  INNER JOIN
    (select id,page_number as last_page
                   from moshaf_pages
                   where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                   order by page_number desc
                   limit 1) B
on A.id <> B.id", array(
                        'startSurahId' => $reportDetails->revision_from_surat,
                        'startAyah' => $reportDetails->revision_to_surat,
                        'lastSurahId' => $reportDetails->revision_from_ayat,
                        'lastAyah' => $reportDetails->revision_to_ayat,
                        'lastAyah2' => $reportDetails->revision_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                    ));


                    return $numberofPages[0]->pageCount;


                })
                ->make(true);

        }

        dd('only ajax requests are allowed');


    }

    public function studentRecordsStatistics(ClassReportStaatisticsRequest $request, $id)
    {

        try {


            if ($id) {

                $planYearMonth = Carbon::parse($request->get('classDate'))->format('Y-m');


                $dateMonthArray = explode('-', $planYearMonth);
                $year = $dateMonthArray[0];
                $month = $dateMonthArray[1];

                $classTotalDays = StudentHefzReport::where('student_id', $request->get('studentId'))
                    ->where('class_id', $id)
                    ->whereYear('class_time', $year)
                    ->whereMonth('class_time', $month)->count();

                $attendanceDaysCount = StudentHefzReport::where('student_id', $request->get('studentId'))
                    ->where('class_id', $id)
                    ->whereYear('class_time', $year)
                    ->whereMonth('class_time', $month)
                    ->whereIn('attendance_id', [2/** on-time */, 1/** late */])->count();

                if ($attendanceDaysCount == 0 || $classTotalDays == 0) {
                    $attendancePercentage = '';
                } else {

                    $attendancePercentage = round($attendanceDaysCount / $classTotalDays * 100);

                }


                $lastPageNumberMemorized = StudentHefzReport::
                where('class_id', $id)
                    ->where('student_id', $request->get('studentId'))
                    ->whereYear('class_time', $year)
                    ->whereMonth('class_time', $month)->orderBy('revision_to_surat', 'desc')->limit(1)->get(['revision_to_surat', 'revision_to_ayat'])->first();


                $lastPageNumberMemorized = DB::select(DB::raw("
select page_number
from moshaf_pages
where surah_id = :startSurahId
  and (first_ayah >= :lastAyah or last_ayah >= :lastAyah2)
limit 1;"), array(
                    'startSurahId' => $lastPageNumberMemorized->revision_to_surat,
                    'lastAyah' => $lastPageNumberMemorized->revision_to_ayat,
                    'lastAyah2' => $lastPageNumberMemorized->revision_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                ));

                $lastPageNumberMemorized = $lastPageNumberMemorized[0]->page_number;


                return response()->json(
                    ['lastPageNumberMemorized' => $lastPageNumberMemorized, 'attendancePercentage' => $attendancePercentage, 'attendanceDaysCount' => $attendanceDaysCount]);


            }


        } catch (\Exception $exception) {
            \Log::error($exception);
            return response()->json($exception->getMessage());

        }


        dd('only ajax requests are allowed');


    }


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create(Request $request, $id)
    {

        $colors = ['red', 'orange', 'yellow', 'olive', 'green', 'teal', 'blue', 'violet', 'purple', 'pink', 'brown', 'black', 'grey'];
        $class = Classes::findOrFail($id);
        $from_date = $request->from_date;

//        $students = $class->students()->has('hefz_plans')->get();
        $students = Student::whereHas('hefz_plans', function ($q) use ($id) {
            $q->where('class_id', $id)->where(function ($q) {
                $q->whereNotNull('study_direction')
                    ->whereNotNull('start_from_surat')->whereNotNull('start_from_ayat')
                    ->whereNotNull('to_surat')->whereNotNull('to_ayat');
            });
        })->with('hefz_plans')->get();


        $incompleteMonthlyPlansCount = Student::whereHas('hefz_plans', function ($q) use ($id) {
            $q->where('class_id', $id)
                ->where(function ($query) {
                    $query->whereNull('study_direction')
                        ->orWhereNull('start_from_surat')
                        ->orWhereNull('start_from_ayat')
                        ->orWhereNull('to_surat')
                        ->orWhereNull('to_ayat');
                });


        })->count();

        $completeMonthlyPlansCount = DB::table('student_hefz_plans')->where('class_id', $id)
            ->where(function ($query) use ($from_date) {
                $query->whereNotNull('study_direction')
                    ->where('start_date', Carbon::parse($from_date)->toDateString())
                    ->whereNotNull('start_from_surat')
                    ->whereNotNull('start_from_ayat')
                    ->whereNotNull('to_surat')
                    ->whereNotNull('to_ayat');
            })->count();


        $noMonthlyPlanStudentsCount = Student::whereHas('class', function ($q) use ($id) {

            $q->where('class_id', $id);
        })->count();


        $report_id = $request->get('report_id');
        $teachers = $class->teachers()->pluck('employees.name', 'employees.id');
        $hefz_evaluation_schema = EvaluationSchema::where('target', 'hefz')->first();
        $revision_evaluation_schema = EvaluationSchema::where('target', 'revision')->first();
        $hefz_valuation_options = $hefz_evaluation_schema->options()->get();
        $revision_valuation_options = $revision_evaluation_schema->options()->get();

        $attendanceOptions = AttendanceOption::orderByDesc('title')->get();


//        if (isset($report_id)){
        if (isset($from_date)) {
            foreach ($students as $student) {
//                $student->hefz_report = $student->hefz()->where('class_report_id',$report_id)->first();
                $student->hefz_report = $student->hefz()->whereDate('class_time', '=', Carbon::parse($from_date)->toDate())->first();


                $student->hefz_report->revision_from_ayat = $this->getAyatListBySurat($student->hefz_report->revision_from_surat, $student->hefz_report->revision_from_ayat);
                $student->hefz_report->revision_to_ayat = $this->getAyatListBySurat($student->hefz_report->revision_to_surat, $student->hefz_report->revision_to_ayat);

//                $student->revision_report = $student->revision()->where('class_report_id',$report_id)->first();
                $student->revision_report = $student->revision()->whereDate('class_time', Carbon::parse($from_date)->toDate())->first();

                $student->revision_report->revision_from_ayat = $this->getAyatListBySurat($student->revision_report->revision_from_surat, $student->revision_report->revision_from_ayat);

                $student->revision_report->revision_to_ayat = $this->getAyatListBySurat($student->revision_report->revision_to_surat, $student->revision_report->revision_to_ayat);

            }
        }

//        $studentMonthlyPlan = $student->whereHas('hefz_plans',function($q) use ($from_date){
//
//            $q->where('start_date',Carbon::parse($from_date)->toDateString());
//
//        })->get();

        //dd($hefz_valuation_options);
        return view('education::classes.reports.create', compact('colors', 'completeMonthlyPlansCount', 'noMonthlyPlanStudentsCount', 'incompleteMonthlyPlansCount', 'attendanceOptions', 'students', 'class', 'from_date', 'teachers', 'hefz_valuation_options', 'revision_valuation_options'));


        // Old stuffs
        if (in_array(auth()->user()->id, $class->teachers()->pluck('employee_id')->toArray())) {
            //$teachers = [auth()->user()->id => auth()->user()->full_name];
        } else {
            //$teachers = $class->teachers()->pluck('full_name', 'employee_id');
        }

        $subjects = [];

        foreach ($class->programs as $program) {
            if ($program->pivot->program_level_id == 0) {
                $subjects['p' . $program->id] = $program->title . ' Program [All levels & Subjects]';
            } else {
                foreach ($program->levels as $level) {
                    if ($level->id == $program->pivot->program_level_id) {
                        foreach ($level->subjects as $subject) {
                            $subjects[$subject->id] = $subject->title;
                        }
                    }
                }
            }
        }

        // $subjects = $class->programs[0]->subjects;

        // return $class->programs[1]->levels[1]->subjects;

        return view('education::classes.reports.create', compact('class', 'teachers', 'subjects'));
    }

    /**
     * Get list of ayats based on surah
     *
     * @param $surah_id
     * @param $ayat_num
     * @return string
     */
    public function getAyatListBySurat($surah_id, $ayat_num): string
    {
        $surah = MoshafSurah::find($surah_id);
        $options = '<option value="" selected>Select</option>';

        if (isset($surah)) {
            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {
                if ($i === $ayat_num) {
                    $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                } else {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';
                }
            }
        }

        return $options;
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    //public function store(Request $request)
    public function store(Request $request)
    {
        try {



            \DB::beginTransaction();
            $organization_id = $request->get('organization_id');
            $class_time = Carbon::parse($request->get('class_time'));
            // year month format
            $planYearMonth = $class_time;
            $dateMonthArray = explode('-', $planYearMonth);
            $revision_from_surat = $request->get('revision_from_surat');
            $revision_from_ayat = $request->get('revision_from_ayat');
            $revision_to_surat = $request->get('revision_to_surat');
            $revision_to_ayat = $request->get('revision_to_ayat');
            $revision_evaluation_id = $request->get('revision_evaluation_id');
            $revision_evaluation_note = $request->get('revision_evaluation_note');
            $student = Student::find($request->get('student_id'));
            $checkIfAllRequiredColumnsHaveValues = false;
            $revision_plan_id = $request->get('revision_plans_id');
            $attendance_id = $request->get('attendance_id');
            $now = Carbon::now();

            if (Carbon::parse($request->get('class_time'))->toDateString() != $now->toDateString()) {
                $dateTime = Carbon::parse($request->get('class_time'))->setTimeFrom($now);
            } else {
                $dateTime = $now;
            }
            $studentHefzRevision = $student->revision()
                ->whereDate('created_at', $class_time->toDateString())
                ->first();

            $studentRevisionPlan = StudentRevisionPlan::find($revision_plan_id);


            // Determine the number of memorized pages based on the study direction
            if ($studentRevisionPlan->study_direction == 'backward') {
                // Calling your stored procedure for backward direction
                $revisedNumberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $revision_from_surat,
                    $revision_from_ayat,
                    $revision_to_surat,
                    $revision_to_ayat
                ]);
                $revisedNumberofPages = $revisedNumberofPages[0]->numberofPagesSum;
            } else {
                // Calling your stored procedure for forward direction
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $revision_from_surat,
                    $revision_from_ayat,
                    $revision_to_surat,
                    $revision_to_ayat
                ]);
                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                $revisedNumberofPages = $results[0]->number_of_pages_sum;
            }

            if (!$studentHefzRevision) {
                $studentHefzRevision = $student->revision()->create([
                    'created_at' => $dateTime,
                    'class_id' => $request->get('class_id'),
                    'student_id' => $student->id,
//                    'teacher_id' => $teacher_id,
                    'program_id' => 1,
                    'organization_id' => $organization_id,
                    "revision_from_surat" => !empty($revision_from_surat) ? $revision_from_surat : null,
                    "revision_from_ayat" => !empty($revision_from_ayat) ? $revision_from_ayat : null,
                    "revision_to_surat" => !empty($revision_to_surat) ? $revision_to_surat : null,
                    "revision_to_ayat" => !empty($revision_to_ayat) ? $revision_to_ayat : null,
                    "revision_evaluation_id" => !empty($revision_evaluation_id) ? $revision_evaluation_id : null,
                    "revision_plan_id" => $revision_plan_id,
                    "attendance_id" => !empty($attendance_id) ? $attendance_id : null,
                    "revision_evaluation_note" => !empty($revision_evaluation_note) ? $revision_evaluation_note : null,
                    'pages_revised' => $revisedNumberofPages,

                ], [
                    'timestamps' => false,
                ]);
            } else {

                $studentHefzRevision->timestamps = false;


                $studentHefzRevision->update([
                    'class_id' => $request->get('class_id'),
//                    'teacher_id' => $teacher_id,
                    "revision_from_surat" => !empty($revision_from_surat) ? $revision_from_surat : null,
                    "revision_from_ayat" => !empty($revision_from_ayat) ? $revision_from_ayat : null,
                    "revision_to_surat" => !empty($revision_to_surat) ? $revision_to_surat : null,
                    "revision_to_ayat" => !empty($revision_to_ayat) ? $revision_to_ayat : null,
                    "revision_evaluation_id" => !empty($revision_evaluation_id) ? $revision_evaluation_id : null,
                    "revision_plan_id" => $revision_plan_id,
                    'updated_at' => $now,
                    "revision_evaluation_note" => !empty($revision_evaluation_note) ? $revision_evaluation_note : null,
                    'attendance_id' => !empty($attendance_id) ? $attendance_id : null,
                    'pages_revised' => $revisedNumberofPages,

                ]);


            }


            $checkIfAllRequiredColumnsHaveValues = !(is_null($studentHefzRevision->revision_from_surat) || is_null($studentHefzRevision->revision_from_ayat) || is_null($studentHefzRevision->revision_to_surat) || is_null($studentHefzRevision->revision_to_ayat));
            $checkIfAllRequiredColumnsHaveValuesLastRevisionRecord = !(is_null($studentHefzRevision->revision_from_surat) || is_null($studentHefzRevision->revision_from_ayat) || is_null($studentHefzRevision->revision_to_surat) || is_null($studentHefzRevision->revision_to_ayat)|| is_null($studentHefzRevision->revision_evaluation_id));
            $studentHefzRevision->refresh();

            if($checkIfAllRequiredColumnsHaveValuesLastRevisionRecord){

                $created_at = Carbon::parse($studentHefzRevision->created_at);

                // update or insert the last revision record
                \App\StudentLastRevisionRecord::updateOrInsert(
                    ['student_id' => $student->id],  // Columns to check for existence
                    [
                        'revision_year_month_day' => $created_at->toDateString(),
                        'from_surat' => $studentHefzRevision->revision_from_surat,
                        'from_ayat' => $studentHefzRevision->revision_from_ayat,
                        'to_surat' => $studentHefzRevision->revision_to_surat,
                        'to_ayat' => $studentHefzRevision->revision_to_ayat,
                    ]
                );

            }
            if ($checkIfAllRequiredColumnsHaveValues) {

                $from_surat_juz = MoshafJuz::where([
                    ['start_surah', '<=', $revision_from_surat],
                    ['end_surah', '>=', $revision_from_surat],
                    ['start_verse', '<=', $revision_from_ayat],
                    ['end_verse', '>=', $revision_from_ayat],
                ])->first();

                $to_surat_juz = MoshafJuz::where([
                    ['start_surah', '<=', $revision_to_surat],
                    ['end_surah', '>=', $revision_to_surat],
                    ['start_verse', '<=', $revision_to_ayat],
                    ['end_verse', '>=', $revision_to_ayat],
                ])->first();

                $from_surat_juz_id = $from_surat_juz ? $from_surat_juz->juz : null;
                $to_surat_juz_id = $to_surat_juz ? $to_surat_juz->juz : null;

                $studentHefzRevision->from_surat_juz_id = $from_surat_juz_id;
                $studentHefzRevision->to_surat_juz_id = $to_surat_juz_id;

                $studentHefzRevision->save();
//                    This will force the model to reload its attributes from the database, including the value of the getter.
                $studentHefzRevision->refresh();






            }

            \DB::commit();
            $studentHefzPlan = $student->hefzPlanPerYearMonthPerClassPerStudent($student->id, $request->get('class_id'), $class_time->year, $class_time->month)->first();

            $surats = \App\MoshafSurah::whereBetween('id', [$studentHefzPlan->start_from_surat, $studentHefzPlan->to_surat])->get();

            return response()->json(['message' => 'success', 'hefzRevision' => $studentHefzRevision, 'allRequiredFieldsFilled' => $checkIfAllRequiredColumnsHaveValues, 'surats' => $surats, 'class_time' => $class_time], 200);

        } catch (\Exception $exception) {
            \Log::error($exception);

            \DB::rollBack();
            $errorMessage = $exception->getMessage();
            return response()->json(compact('errorMessage'));
        }

        /*dd('reached store',$student, $report);
        $requestData = $request->all();

        $requestData['organization_id'] = config('organization_id');

        $report = new ClassReport();

        $report->class_id = $request->class_id;

        $report->employee_id = $request->employee_id;
        $report->class_time = Carbon::parse($request->class_time);


        if (strpos($request->subject_id, 'p') !== false) {
            $report->program_id = str_replace('p', '', $request->subject_id);
            $report->subject_id = 0;
        } else {
            $report->subject_id = $request->subject_id;
            $report->program_id = 0;
        }

        $report->created_by = auth()->user()->id;

        $report->notes = $request->notes;

        $report->save();

        Session::flash('flash_message', 'Class added!');

        return redirect('workplace/education/classes/'.$report->class_id.'/reports/'.$report->id.'/prepare');*/
        // return redirect('workplace/education/classes/'.$report->class_id.'/reports/'.$report->id.'/edit');
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {

        $class = Classes::findOrFail($id);

        $programs = Program::all();

        return view('education::classes.show', compact('class', 'programs'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($class_id, $report_id)
    {
        $surats = MoshafSurah::all();

        $report = ClassReport::findOrFail($report_id);

        $class = Classes::findOrFail($report->class_id);
        $subject = [];
        $special_program_data = [];

        if ($report->subject_id == 0) {
            if ($report->program->setting['special_program_code']) {
                if ($report->program->setting['special_program_code'] = 'hefz') {
                    $special_program_data['data'] = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->evaluation($report);

                    $special_program_data['hefz_evaluation_schema'] = EvaluationSchema::where('target', 'hefz')->first();

                    $special_program_data['revision_evaluation_schema'] = EvaluationSchema::where('target', 'revision')->first();
                }
            }
        } else {
            $subject = Subject::findOrFail($report->subject_id);
        }

        $students = [];
        // return $special_program_data;
        // return $subject->contents;

        return view('education::classes.reports.edit', compact('class', 'report', 'students', 'subject', 'special_program_data', 'surats'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request)
    {
        $requestData = $request->all();
        // return $requestData;

        $report = ClassReport::findOrFail($request->report_id);
        if (isset($request->teacher_attendance) && $request->teacher_attendance == 'absent') {
            $report->status = 'completed';
            $report->notes = 'teacher is absent';
            $report->save();

            return redirect('workplace/education/classes/' . $report->class_id . '/reports');
        } elseif (isset($request->student_attendance)) {
            foreach ($request->student_attendance as $key => $value) {
                $attendance = new StudentAttendance();

                $attendance->organization_id = config('organization_id');
                $attendance->class_report_id = $report->id;
                $attendance->student_id = $key;
                $attendance->class_time = $report->class_time;
                $attendance->attendance = $value;
                $attendance->created_by = auth()->user()->id;

                $attendance->save();
                // $attendance->note = $report->;
            }


            $report->status = 'attendance_submited';

            $report->save();
        } elseif (isset($request->student_performance)) {
            if ($report->subject_id == 0) {
                if ($report->program->setting['special_program_code']) {
                    if ($report->program->setting['special_program_code'] = 'hefz') {
                        $report_data = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->evaluation($report);
                        foreach ($request->student_performance as $student_id => $result) {
                            if (isset($report_data[$student_id])) {
                                if (isset($report_data[$student_id]['hefz']) && $report_data[$student_id]['hefz'] && $result['hefz']) {
                                    $hefz_report = new StudentHefzReport();

                                    $hefz_report->student_id = $student_id;
                                    $hefz_report->organization_id = config('organization_id');
                                    $hefz_report->class_id = $report->class_id;
                                    $hefz_report->class_time = $report->class_time;
                                    $hefz_report->created_by = auth()->user()->id;
                                    // $hefz_report->revision_from_surat = $report_data[$student_id]['hefz']['from_surat'];
                                    // $hefz_report->revision_from_ayat = $report_data[$student_id]['hefz']['from_ayat'];
                                    // $hefz_report->revision_to_surat = $report_data[$student_id]['hefz']['to_surat'];
                                    // $hefz_report->revision_to_ayat = $report_data[$student_id]['hefz']['to_ayat'];

                                    $hefz_report->revision_from_surat = $requestData['report'][$student_id]['hefz']['from_surat'];
                                    $hefz_report->revision_from_ayat = $requestData['report'][$student_id]['hefz']['from_ayat'];
                                    $hefz_report->revision_to_surat = $requestData['report'][$student_id]['hefz']['to_surat'];
                                    $hefz_report->revision_to_ayat = $requestData['report'][$student_id]['hefz']['to_ayat'];

                                    $hefz_report->hefz_evaluation_id = $result['hefz'];

                                    $hefz_report->class_report_id = $report->id;


                                    $hefz_report->save();
                                }
                                if (isset($report_data[$student_id]['revision']) && $report_data[$student_id]['revision'] && $result['revision']) {
                                    $revision_report = new StudentRevisionReport();

                                    $revision_report->student_id = $student_id;
                                    $revision_report->organization_id = config('organization_id');
                                    $revision_report->class_id = $report->class_id;
                                    $revision_report->created_by = auth()->user()->id;
                                    $revision_report->class_time = $report->class_time;
                                    // $revision_report->revision_from_surat = $report_data[$student_id]['revision']['from_surat'];
                                    // $revision_report->revision_from_ayat = $report_data[$student_id]['revision']['from_ayat'];
                                    // $revision_report->revision_to_surat = $report_data[$student_id]['revision']['to_surat'];
                                    // $revision_report->revision_to_ayat = $report_data[$student_id]['revision']['to_ayat'];

                                    $revision_report->revision_from_surat = $requestData['report'][$student_id]['revision']['from_surat'];
                                    $revision_report->revision_from_ayat = $requestData['report'][$student_id]['revision']['from_ayat'];
                                    $revision_report->revision_to_surat = $requestData['report'][$student_id]['revision']['to_surat'];
                                    $revision_report->revision_to_ayat = $requestData['report'][$student_id]['revision']['to_ayat'];

                                    if (isset($requestData['report'][$student_id]['revision']['revision_note'])) {
                                        $revision_report->revision_evaluation_note = $requestData['report'][$student_id]['revision']['revision_note'];
                                    }
                                    if (isset($requestData['report'][$student_id]['revision']['revision_type'])) {
                                        $revision_report->revision_type = $requestData['report'][$student_id]['revision']['revision_type'];
                                    }


                                    $revision_report->revision_evaluation_id = $result['revision'];

                                    $revision_report->class_report_id = $report->id;

                                    $revision_report->save();
                                }
                            }
                        }

                        $report->status = 'completed';

                        $report->save();
                    }
                }
            } else {
            }
        }

        // return $requestData;

        Session::flash('flash_message', 'Class updated!');

        return redirect('workplace/education/classes/' . $report->class_id . '/reports/' . $report->id . '/edit');
    }


    /**
     * Update programs availabilty in class.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function programs(Request $request)
    {
        auth()->user()->can('edit class_programs');

        $id = $request->class_id;

        $class = Classes::findOrFail($id);

        $class->programs()->sync($request->class_programs);

        Session::flash('flash_message', 'Program updated!');

        if ($request->ajax()) {
            return response()->json(['status' => 'success']);
        }

        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id, $hefzReportId)
    {


//        ClassReport::destroy($id);
        StudentHefzReport::destroy($hefzReportId);

        Session::flash('flash_message', 'Class deleted!');

        return response()->json('class report removed');
    }


    public function studentReport($student_id)
    {

        dd(333);
        return DB::table('student_attendances')
            ->leftJoin('class_reports', 'student_attendances.class_report_id', 'class_reports.id')
            ->leftJoin('student_hefz_report', 'student_attendances.class_report_id', '=', 'student_hefz_report.class_report_id')
            ->leftJoin('evaluation_schema_options as hefz_evaluation', 'hefz_evaluation.id', '=', 'student_hefz_report.hefz_evaluation_id')
            ->leftJoin('student_revision_report', 'student_attendances.class_report_id', '=', 'student_revision_report.class_report_id')
            ->leftJoin('evaluation_schema_options as revision_evaluation', 'revision_evaluation.id', '=', 'student_revision_report.revision_evaluation_id')
            ->select(
                'student_attendances.attendance',
                'student_attendances.note as student_attendance_note',
                'class_reports.*',
                'student_hefz_report.*',
                'hefz_evaluation.title as hefz_evaluation_title',
                'student_revision_report.*',
                'revision_evaluation.title as revision_evaluation_title'
            )
            ->where('student_attendances.student_id', $student_id)
            ->orderBy('class_reports.class_time')
            // ->select('users.*', 'contacts.phone', 'orders.price')
            ->get();
    }

    public function studentExpectedProgressPlan($student_id) // Hefz and Morja'ah Program
    {
    }

    private function getNextReportTime($last_report_time, $timetable, $class)
    {

        if (!$last_report_time) {
            $class_date = $timetable->start_at;
        } else {
            $class_date = Carbon::parse($last_report_time);
            $class_date = $class_date->addDay();
//            $class_date = $last_report_time->addDay();
        }

        while ($class->studentsAtDate($class_date)->count() < 1 && $class_date < Carbon::now()) {
            $class_date = $class_date->addDay();
        }

        while (!$timetable[strtolower($class_date->format('D'))]) {
            $class_date = $class_date->addDay();
        }
        $class_date = $class_date->addDay();
        // $class_date = $class_date->addDay();
        // dump($class_date);

        return $class_date;
    }

    private function errorNoTeacher($class_id)
    {
        $error_msg = "Opps! . Some Subjects In the Class Don't have Teacher!!";

        return view('education::classes.errors', compact('class_id', 'error_msg'));
    }

    private function errorNoTimetable($class_id)
    {
        $error_msg = "Opps! . Some Subjects In the Class Don't have Timetable!!";

        return view('education::classes.errors', compact('class_id', 'error_msg'));
    }


    // V2

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function prepareReport($class_id, $report_id)
    {
        $report = ClassReport::findOrFail($report_id);


        if ($report->status == 'completed') {
            return redirect('workplace/education/classes/' . $report->class_id . '/reports/' . $report->id . '/edit');
        }
        $surats = MoshafSurah::all();

        $suar = $surats->map(function ($surah) {
            return [
                'text' => $surah->name,
                'value' => $surah->id,
                'num_ayat' => $surah->num_ayat
            ];
        });


        $class = Classes::with('students.hefz')->with('students.revision')->findOrFail($report->class_id);


        $subject = [];
        $special_program_data = [];
        $special_program = null;

        $hefzEvaluationOptions = [];
        $revisionEvaluationOptions = [];

        if ($report->subject_id == 0 && $report->program->setting['special_program_code'] && $report->program->setting['special_program_code'] = 'hefz') {
            $special_program_data = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->studentsEvaluation($report);

            $hefzEvaluationOptions = EvaluationSchema::where('target', 'hefz')->first()->options->map(function ($option) {
                return ['text' => $option->code . ' - ' . $option->title, 'value' => $option->id];
            });

            $revisionEvaluationOptions = EvaluationSchema::where('target', 'revision')->first()->options->map(function ($option) {
                return ['text' => $option->code . ' - ' . $option->title, 'value' => $option->id];
            });

            $special_program = 'hefz';
        } else {
            $subject = Subject::findOrFail($report->subject_id);
        }
        // dd($special_program_data);
        $students = [];
        $lessons = [];
        // $class->students->map(function($student){
        //     return [

        //     ]
        // });

        foreach ($class->students as $student) {
            if ($special_program
                && $special_program == 'hefz'
                && isset($special_program_data[$student->id])
            ) {
                $students[$student->id] = $special_program_data[$student->id];
                $students[$student->id]['attandance'] = null;
                $students[$student->id]['evaluation'] = null;
            } else {
                $students[$student->id] = [
                    'attandance' => null,
                    'evaluation' => null,
                    'hefz' => [],
                    'revision' => [],
                    'lesson' => [
                        'id' => null,
                        'evaluations' => json_decode('{}')
                    ]
                ];
                if ($subject) {
                    $lessons = $subject->contents->map(function ($lesson) {
                        $evaluation_schems = [];
                        foreach ($lesson->evaluation_schemas as $evaluation) {
                            foreach ($evaluation->options as $option) {
                                $evaluation_schems[$evaluation->title][] = [
                                    'text' => $option->title,
                                    'value' => $option->id
                                ];
                            }
                        }

                        return [
                            'text' => $lesson->title,
                            'value' => $lesson->id,
                            'evaluation_schems' => $evaluation_schems
                        ];
                    });
                }
            }
        }


        return view('education::classes.reports.v3.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
//        return view('education::classes.reports.v2.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
//        return view('education::classes.reportsBackup.v2.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
    }

    public function storeTempReport($class_id, $report_id)
    {

        $report = ClassReport::findOrFail($report_id);
        $report->temp_data = json_encode(request()->except('_token'));
        $report->save();
    }

    public function storeFinalReport(Request $request, $class_id, $report_id)
    {
        // dd($request->all());
        $this->validate($request, [
            'students.*' => 'required',
            'teacher_attended' => 'required'
        ]);

        $report = ClassReport::findOrFail($report_id);

        StudentAttendance::where('class_report_id', $report->id)->delete();
        StudentHefzReport::where('class_report_id', $report->id)->delete();
        StudentRevisionReport::where('class_report_id', $report->id)->delete();


        $requestData = $request->all();

        if (!$request->teacher_attended) {
            $report->status = 'completed';
            $report->notes = 'teacher is absent';
            $report->temp_data = '';
            $report->save();

            return response()->json(['status' => 'completed'], 200);
        }

        foreach ($request->students as $studentID => $studentReport) {
            $attendance = new StudentAttendance();

            $attendance->organization_id = config('organization_id');
            $attendance->class_report_id = $report->id;
            $attendance->student_id = $studentID;
            $attendance->class_time = $report->class_time;
            $attendance->attendance = $studentReport['attandance'];
            $attendance->created_by = auth()->user()->id;
            $attendance->save();

            if (in_array($attendance->attendance, ['on_time', 'late'])) {
                if ($report->subject_id == 0 && $report->program->setting['special_program_code'] && $report->program->setting['special_program_code'] = 'hefz') {
                    if (isset($studentReport['hefz']) && $studentReport['hefz']) {
                        $hefz_report = new StudentHefzReport();
                        $hefz_report->student_id = $studentID;
                        $hefz_report->organization_id = config('organization_id');
                        $hefz_report->class_id = $report->class_id;
                        $hefz_report->class_time = $report->class_time;
                        $hefz_report->created_by = auth()->user()->id;

                        $hefz_report->revision_from_surat = $studentReport['hefz']['from_surat'];
                        $hefz_report->revision_from_ayat = $studentReport['hefz']['from_ayat'];
                        $hefz_report->revision_to_surat = $studentReport['hefz']['to_surat'];
                        $hefz_report->revision_to_ayat = $studentReport['hefz']['to_ayat'];

                        $hefz_report->hefz_evaluation_id = $studentReport['hefz']['evaluation'];
                        $hefz_report->class_report_id = $report->id;

                        $hefz_report->save();
                    }
                    if (isset($studentReport['revision']) && $studentReport['revision']) {
                        $revision_report = new StudentRevisionReport();

                        $revision_report->student_id = $studentID;
                        $revision_report->organization_id = config('organization_id');
                        $revision_report->class_id = $report->class_id;
                        $revision_report->created_by = auth()->user()->id;
                        $revision_report->class_time = $report->class_time;


                        $revision_report->revision_from_surat = $studentReport['revision']['from_surat'];
                        $revision_report->revision_from_ayat = $studentReport['revision']['from_ayat'];
                        $revision_report->revision_to_surat = $studentReport['revision']['to_surat'];
                        $revision_report->revision_to_ayat = $studentReport['revision']['to_ayat'];

                        if (isset($studentReport['revision']['revision_note'])) {
                            $revision_report->revision_evaluation_note = $studentReport['revision']['revision_note'];
                        }
                        if (isset($studentReport['revision']['revision_type'])) {
                            $revision_report->revision_type = $studentReport['revision']['revision_type'];
                        }


                        $revision_report->revision_evaluation_id = $studentReport['revision']['evaluation'];

                        $revision_report->class_report_id = $report->id;

                        $revision_report->save();
                    }
                } else {
                    // Todo: ADD REPORT OF NORMAL PROGRAM
                    if (isset($studentReport['lesson']) && $studentReport['lesson']) {
                        $lesson_report = new LessonReport();

                        $lesson_report->student_id = $studentID;
                        $lesson_report->organization_id = config('organization_id');
                        $lesson_report->class_id = $report->class_id;
                        $lesson_report->created_by = auth()->user()->id;
                        $lesson_report->class_time = $report->class_time;


                        $lesson_report->lesson_id = $studentReport['lesson']['id'];

                        if (isset($studentReport['lesson']['note'])) {
                            $lesson_report->note = $studentReport['revision']['note'];
                        }

                        $lesson_report->class_report_id = $report->id;
                        $lesson_report->save();
                        foreach ($studentReport['lesson']['evaluations'] as $label => $evaluation_option) {
                            $lesson_report->evaluations()->create(['evaluation_option_id' => $evaluation_option]);
                        }
                    }
                }
            }
        }
        $report->temp_data = '';
        $report->status = 'completed';
        $report->save();
        return response()->json(['status' => 'completed'], 200);
    }
}

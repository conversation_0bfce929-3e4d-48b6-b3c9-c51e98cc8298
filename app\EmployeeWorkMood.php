<?php

namespace App;

use App\Notifications\EmployeeResetPassword;
use App\Scopes\OrganizationScope;
use <PERSON>hmat<PERSON><PERSON>ri\LaravelMultiAuthImpersonate\Models\Impersonate;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmployeeWorkMood extends Authenticatable
{
    use Notifiable,SoftDeletes,Impersonate;
    use HasRoles;

    protected $table = 'employee_work_mood';
    protected $casts = ['clock'];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name'

    ];

    // protected $attributes = array(
    //    'organization_id' => 1//Config::get('organization_id'),
    // );

    protected static function boot()
    {
        parent::boot();
        // dd(config('organization_id'));

        static::addGlobalScope(new OrganizationScope);
    }


    public function employees()

    {


        return $this->hasMany(Employee::class,'id','work_mood');
    }




    public function organization()
    {
        return $this->belongsTo('App\Organization');
    }







}

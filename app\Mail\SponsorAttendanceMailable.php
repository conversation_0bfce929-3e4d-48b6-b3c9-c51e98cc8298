<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SponsorAttendanceMailable extends Mailable
{
    use Queueable, SerializesModels;

    public $user;         // external collaborator
    public $month;        // numeric month
    public $year;         // numeric year
    public $attendance;   // formatted data (string or array)

    /**
     * Create a new message instance.
     */
    public function __construct($user, $month, $year, $attendance)
    {
        $this->user = $user;
        $this->month = $month;
        $this->year = $year;
        $this->attendance = $attendance;
    }

    public function build()
    {



        // generate a CSV from $this->attendance
        $csvContent = "employee_id,formatted_date,status\n";
        foreach($this->attendance as $line) {
            // parse each line or pass $this->attendance array
            // This is a simple example
            $csvContent .= "$line\n";
        }

        return $this->subject("Monthly Attendance for {$this->month}/{$this->year}")
            ->view('emails.sponsor_attendance')
            ->attachData($csvContent, 'attendance.csv', [
                'mime' => 'text/csv',
            ]);


        return $this->subject("Monthly Attendance for {$this->month}/{$this->year}")
            ->view('emails.sponsor_attendance')

            ->with([
                'userName' => $this->user->name,
                'attendanceData' => $this->attendance,
                'month' => $this->month,
                'year' => $this->year,
            ])
            ->attachData($csvContent, 'attendance.csv', [
                'mime' => 'text/csv',
            ]);
    }
}

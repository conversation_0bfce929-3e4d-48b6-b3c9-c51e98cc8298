<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use App\Center;
use App\Employee;
use App\User;

/**
 * SystemViewerServiceProvider: Nuclear-level solution for system viewer access
 * 
 * This provider operates at the Laravel framework level to ensure system viewers
 * have transparent access to all organizational data without breaking existing code.
 * 
 * It works by:
 * 1. Extending Eloquent relationship and query building mechanisms
 * 2. Intercepting whereIn() calls that use relationship-based authorization
 * 3. Automatically expanding access for system viewers at query execution time
 * 4. Maintaining complete compatibility with existing controller logic
 */
class SystemViewerServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only apply these extensions when we have an authenticated system viewer
        $this->extendEloquentForSystemViewers();
        $this->extendQueryBuilderForSystemViewers();
    }

    /**
     * Extend Eloquent Builder to handle system viewer access patterns
     */
    protected function extendQueryBuilderForSystemViewers(): void
    {
        Builder::macro('systemViewerAwareWhereIn', function ($column, $values) {
            /** @var Builder $this */
            
            // If this is a system viewer and the whereIn is for center/organization filtering
            if ($this->isSystemViewerAccessPattern($column, $values)) {
                // For system viewers, expand to all organizational entities
                $expandedValues = $this->getExpandedValuesForSystemViewer($column);
                return $this->whereIn($column, $expandedValues);
            }
            
            // For non-system viewers or non-access patterns, use normal whereIn
            return $this->whereIn($column, $values);
        });
    }

    /**
     * Extend relationship handling for system viewers
     */
    protected function extendEloquentForSystemViewers(): void
    {
        // This will be handled by the model-level overrides we'll fix
    }

    /**
     * Check if this is a system viewer access pattern
     */
    protected function isSystemViewerAccessPattern($column, $values): bool
    {
        // Check if user is authenticated and is a system viewer
        if (!Auth::guard('employee')->check()) {
            return false;
        }

        $user = Auth::guard('employee')->user();
        if (!$user->hasRole('system_viewer_' . config('organization_id') . '_')) {
            return false;
        }

        // Check if this is an access control pattern (empty values or center-related columns)
        if (empty($values) || 
            in_array($column, ['center_id', 'centre_id', 'cen_id']) ||
            (is_array($values) && count($values) === 0)) {
            return true;
        }

        return false;
    }

    /**
     * Get expanded values for system viewers based on column type
     */
    protected function getExpandedValuesForSystemViewer($column): array
    {
        $organizationId = config('organization_id');

        switch ($column) {
            case 'center_id':
            case 'centre_id':
            case 'cen_id':
                return Center::where('organization_id', $organizationId)
                    ->whereNull('deleted_at')
                    ->pluck('id')
                    ->toArray();

            // Add more entity types as needed
            default:
                // For unknown columns, try to maintain organizational boundaries
                return [$organizationId]; // Fallback to organization ID
        }
    }
} 
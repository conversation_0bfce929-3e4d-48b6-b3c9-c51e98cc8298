<?php

namespace Modules\JobSeeker\Console;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Entities\JobCompanyWatchlist;
use Mo<PERSON><PERSON>\JobSeeker\Entities\JobCompanyWatchlistCount;

class UpdateCompanyWatchlistCountsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobseeker:update-company-watchlist-counts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update job counts for all companies in the watchlist';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $startTime = microtime(true);
        Log::info('Starting company watchlist count update');
        
        $this->info('Updating company watchlist counts...');
        
        try {
            // Get all active watchlist entries
            $watchlist = JobCompanyWatchlist::where('is_active', true)->get();
            
            if ($watchlist->isEmpty()) {
                $this->info('No active company watchlist entries found.');
                Log::info('No active company watchlist entries found for update');
                return 0;
            }
            
            $this->info('Found ' . $watchlist->count() . ' watchlist entries to update.');
            
            $now = Carbon::now();
            $sixtyDaysAgo = $now->copy()->subDays(60);
            
            $counter = 0;
            foreach ($watchlist as $company) {
                $this->processCompany($company, $sixtyDaysAgo, $now);
                $counter++;
                
                if ($counter % 10 === 0) {
                    $this->info("Processed {$counter} of {$watchlist->count()} companies");
                }
            }
            
            $executionTime = round(microtime(true) - $startTime, 2);
            $this->info("Completed updating {$watchlist->count()} company watchlist counts in {$executionTime} seconds");
            Log::info("Completed updating {$watchlist->count()} company watchlist counts", [
                'execution_time' => $executionTime,
                'companies_processed' => $watchlist->count()
            ]);
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Error updating company watchlist counts: ' . $e->getMessage());
            Log::error('Error updating company watchlist counts', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }
    
    /**
     * Process a single company watchlist entry
     *
     * @param JobCompanyWatchlist $company
     * @param Carbon $sixtyDaysAgo
     * @param Carbon $now
     * @return void
     */
    private function processCompany(JobCompanyWatchlist $company, Carbon $sixtyDaysAgo, Carbon $now)
    {
        try {
            // Get job count and latest job date for this company
            $jobData = DB::table('jobs')
                ->select(
                    DB::raw('COUNT(*) as job_count'),
                    DB::raw('MAX(created_at) as last_job_date')
                )
                ->where('company_name', $company->company_name)
                ->where('created_at', '>=', $sixtyDaysAgo)
                ->first();
            
            // Create or update the count record
            JobCompanyWatchlistCount::create([
                'watchlist_id' => $company->id,
                'jobs_count' => $jobData->job_count ?? 0,
                'last_job_date' => $jobData->last_job_date,
                'date_checked' => $now
            ]);
            
            Log::debug("Updated counts for company: {$company->company_name}", [
                'company_id' => $company->id,
                'jobs_count' => $jobData->job_count ?? 0,
                'last_job_date' => $jobData->last_job_date
            ]);
        } catch (\Exception $e) {
            Log::error("Error processing company {$company->company_name}", [
                'company_id' => $company->id,
                'error' => $e->getMessage()
            ]);
        }
    }
} 
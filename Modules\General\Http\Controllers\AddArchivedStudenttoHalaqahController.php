<?php

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\Classes;
use App\ClassProgram;
use App\ClassStudent;
use App\Program;
use App\Student;
use App\StudentHefzPlan;
use App\StudentHefzReport;
use App\StudentProgramLevel;
use App\StudentRevisionPlan;
use App\StudentRevisionReport;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Role;
use App\Permission;
use App\Authorizable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Modules\RolePermission\Entities\PermissionAssign;
use App\Services\StudentLevelService;
use Illuminate\Support\Facades\Log;
use App\StudentNouranyaReport;
use App\StudentNouranyaPlan;
use App\StudentIjazasanadMemorizationReport;
use App\IjazasanadMemorizationPlan;

class AddArchivedStudenttoHalaqahController extends Controller
{
    protected $studentLevelService;

    public function __construct(StudentLevelService $studentLevelService)
    {
        $this->studentLevelService = $studentLevelService;
    }

    public function __invoke(Request $request)
    {
        $validator = $this->validate($request, [
            'student_id' => 'required|exists:students,id',
            'halaqah'    => 'required|exists:classes,id'
        ]);

        try {
            DB::beginTransaction();
            Log::info("Starting transaction to add archived student to halaqah.", ['student_id' => $request->student_id, 'target_halaqah_id' => $request->halaqah]);

            $studentId    = $request->student_id;
            $newHalaqahId = $request->halaqah;

            $student = Student::withTrashed()->find($studentId);
            if (!$student) {
                Log::error("Archived student not found.", ['student_id' => $studentId]);
                throw new \Exception("Student record not found.");
            }
            if (!$student->trashed()) {
                 Log::warning("Attempting to add a non-archived student via AddArchivedStudent controller.", ['student_id' => $studentId]);
                 throw new \Exception("Student is not archived. Use the regular transfer process.");
            }
            $student->restore();
             Log::info("Restored student record.", ['student_id' => $studentId]);

            $classProgram = ClassProgram::where('class_id', $newHalaqahId)->first();
            if (!$classProgram) {
                Log::error("No class program found for the target Halaqah.", ['target_halaqah_id' => $newHalaqahId]);
                throw new \Exception("No class program found for the target Halaqah.");
            }
            $newProgramId = $classProgram->program_id;
            $centerProgram = DB::table('center_programs')->where('program_id', $newProgramId)->first();
            if (!$centerProgram) {
                 Log::error("No center found associated with the program of the target Halaqah.", ['program_id' => $newProgramId, 'target_halaqah_id' => $newHalaqahId]);
                 throw new \Exception("Could not determine the center for the target Halaqah's program.");
            }
            $newCenterId = $centerProgram->center_id;
            Log::info("Derived target program and center.", ['program_id' => $newProgramId, 'center_id' => $newCenterId]);

            Log::info("Force deleting any existing ClassStudent records.", ['student_id' => $studentId]);
            ClassStudent::where('student_id', $studentId)->forceDelete();

            $admission = Admission::withTrashed()->where('student_id', $studentId)->latest('id')->first();
            if (!$admission) {
                 Log::error("No admission record (trashed or otherwise) found for student.", ['student_id' => $studentId]);
                 throw new \Exception("No admission record found for the student.");
            }
            if (!$admission->trashed()) {
                 Log::warning("Found a non-trashed admission record for an archived student.", ['student_id' => $studentId, 'admission_id' => $admission->id]);
            } else {
                 $admission->restore();
                 Log::info("Restored trashed admission record.", ['admission_id' => $admission->id]);
            }

            $roles = auth()->user()->getRoleNames();
            $rolesString = $roles->implode(',');

            $criticalData = [
                'gender_id'       => $admission->gender_id,
                'student_email'   => $admission->student_email,
                'student_mobile'  => $admission->student_mobile,
                'date_of_birth'   => $admission->date_of_birth,
                'age'             => $admission->age,
                'organization_id' => $admission->organization_id ?? config('organization_id'),
                'old_center_id'       => $admission->center_id,
                'old_program_id'      => $admission->program_id,
                'old_class_id'        => $admission->class_id,
            ];
            Log::info("Updating restored/existing admission record.", [
                'admission_id' => $admission->id,
                'new_program_id' => $newProgramId,
                'new_center_id' => $newCenterId,
                'new_halaqah_id' => $newHalaqahId,
            ]);

            $admission->update(array_merge([
                'program_id'   => $newProgramId,
                'center_id'    => $newCenterId,
                'class_id'     => $newHalaqahId,
                'created_by'   => auth()->user()->id,
                'status'       => 'active',
                'creator_role' => $rolesString,
            ], $criticalData));

            $admission->programs()->sync([
                $newProgramId => ['created_at' => Carbon::now()]
            ]);
            Log::info("Synced program to admission.", ['admission_id' => $admission->id, 'program_id' => $newProgramId]);

            $newProgram = Program::find($newProgramId);
            if ($newProgram && $student) {
                 try {
                     Log::info("Attempting to assign initial level via service.", ['student_id' => $studentId, 'new_halaqah_id' => $newHalaqahId, 'program_id' => $newProgram->id]);
                     $this->studentLevelService->assignInitialLevel($student, $newHalaqahId, $newProgram);
                     Log::info("Successfully assigned initial level upon reactivation.", ['student_id' => $studentId]);
                 } catch (\Exception $e) {
                     Log::error("Failed to assign level during archive restore: " . $e->getMessage(), ['student_id' => $studentId, 'class_id' => $newHalaqahId, 'exception' => $e]);
                     throw $e;
                 }
            } else {
                 Log::error('Could not find program or student object during archive restore level assignment', ['student_id' => $studentId, 'class_id' => $newHalaqahId]);
                 throw new \Exception("Failed to find program or student for level assignment.");
            }

            Log::info("Creating new ClassStudent record for reactivated student.", ['student_id' => $studentId, 'new_halaqah_id' => $newHalaqahId]);
            ClassStudent::create([
                'student_id' => $studentId,
                'class_id'   => $newHalaqahId,
                'start_date' => Carbon::now()->toDateString(),
                'created_at' => Carbon::now(),
                'added_at'   => Carbon::now()
            ]);

            Log::info("Ensuring student status is 'active'.", ['student_id' => $studentId]);
            $student->status = 'active';
            $student->save();

            $oldCenterId = $criticalData['old_center_id'] ?? 'N/A';
            $oldProgramId = $criticalData['old_program_id'] ?? 'N/A';
            $oldClassId = $criticalData['old_class_id'] ?? 'N/A';

             $logMessage = "Archived Student Reactivation Log: Student ID {$studentId} (previously in Center [{$oldCenterId}], Program [{$oldProgramId}], Class [{$oldClassId}]) reactivated and added " .
                "to Center [{$newCenterId}], Program [{$newProgramId}], Class [{$newHalaqahId}]. " .
                "Operations performed: Restored Student record, restored/updated Admission record, preserved critical data, synced program, assigned level, force-deleted old ClassStudent records, created new ClassStudent record, set Student status to active. " .
                 "Performed by User ID: " . auth()->user()->id . " (Roles: {$rolesString}). " .
                 "Timestamp: " . Carbon::now()->toDateTimeString();
             Log::info($logMessage);

            DB::commit();
            Log::info("Transaction committed successfully for adding archived student.", ['student_id' => $studentId]);

            return response()->json(['message' => 'Successfully added the student to this Halaqah'], 201);

        } catch (\Exception $exception) {
             Log::error("Exception during adding archived student: " . $exception->getMessage(), [
                 'student_id' => $request->student_id ?? 'unknown',
                 'target_halaqah' => $request->halaqah ?? 'unknown',
                 'exception_trace' => $exception->getTraceAsString()
             ]);
            DB::rollBack();
            Log::info("Transaction rolled back due to exception while adding archived student.", ['student_id' => $request->student_id ?? 'unknown']);
            return response()->json(['message' => "An error occurred while adding the student: " . $exception->getMessage()], 500);
        }
    }
}

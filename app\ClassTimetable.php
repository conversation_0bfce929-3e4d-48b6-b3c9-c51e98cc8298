<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClassTimetable extends Model
{
    use HasFactory;

    protected $table = 'class_timetable';

    protected $fillable = ['mon','tue','wed','thu','fri','sat','sun','class_id','class_duration','start_at'];
    public function class()
    {
        return $this->belongsTo(Classes::class,'class_id');
    }

    public function daysCountPerMonth($month, $year)
    {
        // Initialize the total classes count for the month
        $totalClasses = 0;


        // Get the total number of days in the month
        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);

        // Iterate through each day of the month
        for ($day = 1; $day <= $daysInMonth; $day++) {
            // Get the day of the week for the current date (0 = Sunday, 1 = Monday, etc.)
            $dayOfWeek = date('w', strtotime("$year-$month-$day"));

            // Convert the day of the week number to the column name in the ClassTimetable model
            $dayColumn = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'][$dayOfWeek];

            // Check if there is a class scheduled on this day of the week
            if (!is_null($this->$dayColumn)) {
                // Increment the total classes count for the month
                $totalClasses++;
            }
        }

        return $totalClasses;
    }

    public function getScheduledDaysOfWeek(): array
    {
        $days = [];
        $dayMapping = [
            'mon' => 1,
            'tue' => 2,
            'wed' => 3,
            'thu' => 4,
            'fri' => 5,
            'sat' => 6,
            'sun' => 7,
        ];

        foreach ($dayMapping as $column => $dayOfWeekIso) {
            if (!is_null($this->$column)) {
                $days[] = $dayOfWeekIso;
            }
        }

        return $days;
    }

}

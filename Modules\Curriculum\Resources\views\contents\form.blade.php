<div class="form-group {{ $errors->has('title') ? 'has-error' : ''}}">
    {!! Form::label('title', 'Title', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::text('title', null, ['class' => 'form-control']) !!}
        {!! $errors->first('title', '<p class="help-block">:message</p>') !!}
    </div>
</div>
<div class="form-group {{ $errors->has('content') ? 'has-error' : ''}}">
    {!! Form::label('content', 'Content', ['class' => 'col-md-12 control-label']) !!}
    <div class="col-md-12">
        {!! Form::textarea('content', null, ['class' => 'form-control summernote']) !!}
        {!! $errors->first('content', '<p class="help-block">:message</p>') !!}
    </div>
</div><div class="form-group {{ $errors->has('content_category_id') ? 'has-error' : ''}}">
    {!! Form::label('content_category_id', 'Content Category', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('content_category_id', $content_categories ,  null, ['class' => 'form-control']) !!}
        {!! $errors->first('content_category_id', '<p class="help-block">:message</p>') !!}
    </div>
</div><div class="form-group {{ $errors->has('locale') ? 'has-error' : ''}}">
    {!! Form::label('language', 'Language', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('language', $languages ,  null, ['class' => 'form-control']) !!}
        {!! $errors->first('language', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group">
    <div class="row">

        {!! Form::label('evaluation_schema', 'Evaluation Schemas', ['class' => 'col-md-4 control-label']) !!}    

        @isset($content)
        <div class="col-md-6">    
            <ul>
                @foreach($content->evaluation_schemas as $evaluation_schema)
                <li class="clearfix">{{ $evaluation_schema->title }}  <button type="button" class="btn btn-danger btn-xs pull-right" > Disable</button></li>
                @endforeach
            </ul>
        </div>
    </div>
    <div class="col-md-12">
        <button type="button" class="btn btn-primary btn-xs pull-right" onclick="$('#add_schemas_input').removeClass('hidden')"> Add Schemas</button>
    </div>
    <div id="add_schemas_input" class="row @isset($content) hidden @endisset">        
        {!! Form::label('evaluation_schema', 'Add Evaluation Schema', ['class' => 'col-md-4 control-label']) !!}
        @endisset
        <div class="col-md-6">
            {!! Form::select('contents_evaluation_schemas[]', $evaluation_schemas , null, ['class' => 'form-control select2' , 'multiple']) !!}
        </div>
    </div>
</div>

<div class="form-group {{ $errors->has('status') ? 'has-error' : ''}}">
    {!! Form::label('status', 'Status', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('status', [1 => "Active", 0 => "Suspend"] , null, ['class' => 'form-control']) !!}
        {!! $errors->first('status', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group">
    <div class="col-md-offset-4 col-md-4">
        {!! Form::submit(isset($submitButtonText) ? $submitButtonText : 'Create', ['class' => 'btn btn-primary']) !!}
    </div>
</div>

@section('css')
<link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.6/summernote.css" rel="stylesheet">
@endsection
@section('js')
<script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.8/summernote.js"></script>
<script src="{{ asset('assets/common/js/summernote-cleaner.js') }}"></script>
<script src="{{ asset('assets/lfm/js/lfm.js') }}"></script>

<script>
    $(document).ready(function() {
        // Define function to open filemanager window
	// Define function to open filemanager window
	var lfm = function(options, cb) {
		var route_prefix = (options && options.prefix) ? options.prefix : '/en/manage/uploader';
		window.open(route_prefix + '?type=' + options.type || 'file', 'FileManager', 'width=900,height=600');
		window.SetUrl = cb;
	};
	
	var LFMImgButton = function(context) {
        var ui = $.summernote.ui;
          var button = ui.button({
            contents: '<i class="note-icon-picture"></i> ',
            tooltip: 'Insert image with filemanager',
            click: function() {
	    
                lfm({type: 'image', prefix: '/en/manage/uploader'}, function(url, path) {
                    context.invoke('insertImage', url);
                });

            }
        });
        return button.render();
    };
	var LFMDocButton = function(context) {
        var ui = $.summernote.ui;

        var button = ui.button({
            contents: '<i class="fa fa-file-pdf-o"></i> ',
            tooltip: 'Upload PDF with filemanager',
            click: function() {
	    
                lfm({type: 'link', prefix: '/en/manage/uploader'}, function(url, path) {
                    context.invoke('createLink',{
                    text: 'Doc Link',
                    url: url,
                    isNewWindow: true
                    });
                });


            }
        });
        return button.render();
    };

	var fullEditorConfig = {
		toolbar:[
			['cleaner',['cleaner']], // The Button
			['style',['style']],
			['font',['bold','italic','underline','clear']],
			['color',['color']],
			['para',['ul','ol','paragraph']],
			['height',['height']],
			['table',['table']],
			['insert',['media','video','link','hr']],
			['view',['fullscreen','codeview']],
			['popovers', ['lfm']],
			['popovers', ['lfmdoc']],
			['help',['help']]
		]
		,buttons: {
			lfm: LFMImgButton,
			lfmdoc: LFMDocButton
		},
		cleaner:{
			notTime: 2400, // Time to display Notifications.
			action: 'both', // both|button|paste 'button' only cleans via toolbar button, 'paste' only clean when pasting content, both does both options.
			newline: '<br>', // Summernote's default is to use '<p><br></p>'
			notStyle: 'position:absolute;top:0;left:0;right:0', // Position of Notification
			icon: '<i class="fa fa-eraser"></i>',
			keepHtml: false, // Remove all Html formats
			{{--  keepOnlyTags: ['<p>', '<br>', '<ul>', '<li>', '<b>', '<strong>','<i>', '<a>'], // If keepHtml is true, remove all tags except these  --}}
			keepClasses: false, // Remove Classes
			badTags: ['style', 'script', 'applet', 'embed', 'noframes', 'noscript', 'html'], // Remove full tags with contents
			badAttributes: ['style', 'start'] // Remove attributes from remaining tags
		},
		minHeight : 300
		
    };
    
    $('.summernote').summernote(fullEditorConfig);
       
    });

</script>
@endsection
@include('jssnippets/select2')
/*----------------------------------------------------
@File: Default Styles
@Author: SPONDON IT

This file contains the styling for the actual theme, this
is the file you need to edit to change the look of the
theme.
---------------------------------------------------- */
/*=====================================================================
@Template Name: HostHub Construction 
@Developed By: Spondonit.com
Author E-mail: <EMAIL>

=====================================================================*/
/*----------------------------------------------------*/
/*font Variables*/
/*Color Variables*/
/*=================== fonts ====================*/
@import url("https://fonts.googleapis.com/css?family=Poppins:300,400,400i,500,600");
/*---------------------------------------------------- */
/* Medium Layout: 1280px */
/* Tablet Layout: 768px */
/* Mobile Layout: 320px */
/* Wide Mobile Layout: 480px */
/*---------------------------------------------------- */
/* line 1, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
body.admin {
  line-height: 24px;
  font-size: 13px;
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  color: #828bb2;
  background: url(../img/body-bg.jpg) no-repeat center;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-position: top;
}

@media (max-width: 1199px) and (min-width: 992px) {
  /* line 1, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
  body.admin {
    font-size: 11px;
    line-height: 22px;
  }
}

@-webkit-keyframes autofill {
  to {
    color: #828bb2;
    background: transparent;
  }
}

/* line 25, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
input:-webkit-autofill {
  -webkit-animation-name: autofill;
  -webkit-animation-fill-mode: both;
}

/* line 30, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 500;
  color: #415094;
  line-height: 1.5;
}

/* line 40, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
h1 {
  font-size: 22px;
}

/* line 43, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
h2 {
  font-size: 20px;
}

/* line 46, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
h3 {
  font-size: 18px;
}

/* line 49, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
h4 {
  font-size: 16px;
}

/* line 52, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
h5 {
  font-size: 14px;
}

/* line 55, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
h6 {
  font-size: 12px;
}

/* line 59, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.list {
  list-style: none;
  margin: 0px;
  padding: 0px;
}

/* line 65, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
a {
  text-decoration: none;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

/* line 68, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
a:hover, a:focus {
  text-decoration: none;
  outline: none;
}

/* line 75, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
textarea {
  overflow: hidden;
  resize: none;
}

/* line 105, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
input.placeholder,
textarea.placeholder {
  position: relative;
  bottom: -5px;
}

/* line 108, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
input:-moz-placeholder,
textarea:-moz-placeholder {
  position: relative;
  bottom: -5px;
}

/* line 111, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
input::-moz-placeholder,
textarea::-moz-placeholder {
  position: relative;
  bottom: -5px;
}

/* line 114, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  position: relative;
  bottom: -5px;
}

/* line 87, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
button:focus {
  outline: none;
  box-shadow: none;
}

/* line 93, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.transparent-color {
  background: transparent !important;
}

/* line 98, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.primary-color {
  color: #415094;
}

/* line 101, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.primary-color2 {
  color: #7c32ff;
}

/* line 104, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.black-color {
  color: #000000;
}

/* line 107, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.text-color {
  color: #828bb2;
}

/* line 111, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.exam-bg {
  background: rgba(130, 139, 178, 0.3);
}

/* line 116, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.fw-400 {
  font-weight: 400;
}

/* line 119, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.fw-500 {
  font-weight: 500;
}

/* line 122, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.fw-600 {
  font-weight: 600;
}

/* line 127, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.fs-12 {
  font-size: 12px;
}

/* line 132, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mb-10 {
  margin-bottom: 10px;
}

/* line 135, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mb-15 {
  margin-bottom: 15px;
}

/* line 138, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mb-20 {
  margin-bottom: 20px;
}

/* line 141, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mb-25 {
  margin-bottom: 25px;
}

/* line 144, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mb-30 {
  margin-bottom: 30px;
}

/* line 147, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mb-35 {
  margin-bottom: 35px;
}

/* line 150, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mb-40 {
  margin-bottom: 40px;
}

/* line 153, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mb-45 {
  margin-bottom: 45px;
}

/* line 156, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mb-50 {
  margin-bottom: 50px;
}

/* line 161, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.ml-10 {
  margin-left: 10px;
}

/* line 164, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.ml-15 {
  margin-left: 15px;
}

/* line 167, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.ml-20 {
  margin-left: 20px;
}

/* line 170, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.ml-25 {
  margin-left: 25px;
}

/* line 173, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.ml-30 {
  margin-left: 30px;
}

/* line 176, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.ml-35 {
  margin-left: 35px;
}

/* line 179, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.ml-40 {
  margin-left: 40px;
}

/* line 182, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.ml-45 {
  margin-left: 45px;
}

/* line 185, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.ml-50 {
  margin-left: 50px;
}

/* line 190, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mr-10 {
  margin-right: 10px;
}

/* line 193, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mr-15 {
  margin-right: 15px;
}

/* line 196, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mr-20 {
  margin-right: 20px;
}

/* line 199, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mr-25 {
  margin-right: 25px;
}

/* line 202, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mr-30 {
  margin-right: 30px;
}

/* line 205, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mr-35 {
  margin-right: 35px;
}

/* line 208, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mr-40 {
  margin-right: 40px;
}

/* line 211, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mr-45 {
  margin-right: 45px;
}

/* line 214, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mr-50 {
  margin-right: 50px;
}

/* line 217, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mr-75 {
  margin-right: 75px;
}

/* line 222, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mt--48 {
  margin-top: -48px;
}

/* line 225, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mt-10 {
  margin-top: 10px;
}

/* line 228, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mt-15 {
  margin-top: 15px;
}

/* line 231, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mt-20 {
  margin-top: 20px;
}

/* line 234, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mt-25 {
  margin-top: 25px;
}

/* line 237, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mt-30 {
  margin-top: 30px;
}

/* line 240, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mt-35 {
  margin-top: 35px;
}

/* line 243, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mt-40 {
  margin-top: 40px;
}

/* line 246, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mt-45 {
  margin-top: 45px;
}

/* line 249, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mt-50 {
  margin-top: 50px;
}

/* line 252, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.mt-80 {
  margin-top: 80px;
}

/* line 257, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pb-7 {
  padding-bottom: 7px !important;
}

/* line 260, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pb-10 {
  padding-bottom: 10px !important;
}

/* line 263, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pb-20 {
  padding-bottom: 20px !important;
}

/* line 266, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pb-30 {
  padding-bottom: 30px !important;
}

/* line 269, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pb-40 {
  padding-bottom: 40px !important;
}

/* line 272, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pb-50 {
  padding-bottom: 50px !important;
}

/* line 275, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pb-120 {
  padding-bottom: 120px !important;
}

/* line 280, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pl-10 {
  padding-left: 10px;
}

/* line 283, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pl-20 {
  padding-left: 20px;
}

/* line 286, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pl-30 {
  padding-left: 30px;
}

/* line 289, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pl-35 {
  padding-left: 35px;
}

/* line 292, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pl-40 {
  padding-left: 40px;
}

/* line 295, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pl-50 {
  padding-left: 50px;
}

/* line 300, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pv-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}

/* line 306, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pr-30 {
  padding-right: 30px;
}

/* line 310, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pt-10 {
  padding-top: 10px;
}

/* line 313, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pt-20 {
  padding-top: 20px;
}

/* line 316, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.pt-30 {
  padding-top: 30px;
}

/* line 321, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_reset.scss */
.p-h-20 {
  padding: 0px 16px;
}

/*---------------------------------------------------- */
@media (max-width: 991px) {
  /* line 2, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_responsive.scss */
  .mb-20-lg {
    margin-bottom: 20px;
  }
  /* line 5, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_responsive.scss */
  .mb-30-lg {
    margin-bottom: 30px;
  }
  /* line 8, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_responsive.scss */
  .mt-20-lg {
    margin-top: 20px;
  }
  /* line 11, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_responsive.scss */
  .mt-30-lg {
    margin-top: 30px;
  }
}

@media (max-width: 767px) {
  /* line 17, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_responsive.scss */
  .mt-30-md {
    margin-top: 30px;
  }
  /* line 20, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_responsive.scss */
  .mt-30-md {
    margin-top: 30px;
  }
  /* line 23, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_responsive.scss */
  .mt-50-md {
    margin-top: 50px;
  }
}

/*---------------------------------------------------- */
/* Start Boxes Area css
============================================================================================ */
/* line 3, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.white-box {
  background: #ffffff;
  padding: 40px 30px;
  border-radius: 10px;
  box-shadow: 0px 10px 15px rgba(236, 208, 244, 0.3);
}

@media (max-width: 1300px) and (min-width: 992px) {
  /* line 3, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
  .white-box {
    padding: 30px 15px;
  }
}

/* line 11, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.white-box.single-summery {
  padding: 21px 30px;
  position: relative;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 15, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.white-box.single-summery:before, .white-box.single-summery:after {
  content: "";
  background: transparent;
  min-height: 100px;
  width: 100%;
  position: absolute;
  left: 0px;
  top: 0px;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

@media (max-width: 1440px) and (min-width: 992px) {
  /* line 26, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
  .white-box.single-summery .d-flex {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
}

/* line 32, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.white-box.single-summery h3,
.white-box.single-summery p,
.white-box.single-summery h1 {
  position: relative;
  z-index: 99;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

@media (max-width: 1480px) and (min-width: 992px) {
  /* line 39, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
  .white-box.single-summery h1 {
    margin-top: 6px;
  }
}

/* line 44, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.white-box.single-summery p {
  color: #828bb2;
}

/* line 47, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.white-box.single-summery:hover {
  background: url(../img/summery-bg1.png) no-repeat center;
  box-shadow: 0px 10px 30px rgba(108, 39, 255, 0.3);
  background-size: 100% 100%;
}

/* line 51, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.white-box.single-summery:hover:before {
  background: url(../img/summery-bg2.png) no-repeat center;
  top: 8px;
}

/* line 55, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.white-box.single-summery:hover:after {
  background: url(../img/summery-bg3.png) no-repeat center;
  top: 16px;
}

/* line 59, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.white-box.single-summery:hover h3,
.white-box.single-summery:hover p,
.white-box.single-summery:hover h1 {
  color: #ffffff;
  -webkit-text-fill-color: #ffffff;
}

/* line 67, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.white-box.radius-t-y-0 {
  border-radius: 0px 0px 10px 10px;
}

/* End Boxes Area css
============================================================================================ */
/* line 74, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.table thead th {
  color: #415094;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  border-top: 0px;
  padding: 12px 12px 12px 0px;
}

/* line 83, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.table tbody td {
  padding: 20px 18px 20px 0px;
}

/* line 90, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.no-search .dataTables_filter > label {
  display: none;
}

/* line 95, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.no-paginate .dataTables_wrapper .dataTables_paginate {
  display: none;
}

/* line 100, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.no-table-info .dataTables_wrapper .dataTables_info {
  display: none;
}

/* line 106, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table .dropdown .dropdown-toggle {
  background: transparent;
  color: #415094;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid #7c32ff;
  border-radius: 32px;
  padding: 5px 20px;
  text-transform: uppercase;
  overflow: hidden;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

/* line 117, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table .dropdown .dropdown-toggle:focus {
  box-shadow: none;
}

/* line 120, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table .dropdown .dropdown-toggle:hover, .school-table .dropdown .dropdown-toggle:focus {
  color: #ffffff;
  border: 1px solid transparent;
}

/* line 127, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table .dropdown .dropdown-toggle:after {
  content: "\e62a";
  font-family: "themify";
  border: none;
  border-top: 0px;
  font-size: 10px;
  position: relative;
  top: 3px;
  left: 0;
  font-weight: 600;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

/* line 140, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table .dropdown .dropdown-menu {
  border-radius: 5px 5px 10px 10px;
  border: 0px;
  padding: 15px 0px;
}

/* line 145, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table .dropdown .dropdown-menu .dropdown-item {
  color: #828bb2;
  text-align: right;
  font-size: 12px;
  padding: 4px 1.5rem;
  text-transform: uppercase;
  cursor: pointer;
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

/* line 153, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table .dropdown .dropdown-menu .dropdown-item:hover {
  color: #415094;
}

/* line 156, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table .dropdown .dropdown-menu .dropdown-item:active {
  background: transparent;
  color: #415094;
}

/* line 164, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table .dropdown.show .dropdown-toggle:after {
  top: 16px;
  left: 8px;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  -webkit-transition: all 0.15s ease-in-out;
  -moz-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

/* line 177, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.modal .modal-dialog.large-modal {
  min-width: 1050px;
}

/* line 180, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.modal .modal-dialog.full-width-modal {
  min-width: 90%;
}

/* line 186, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.modal-content {
  border: 0;
}

/* line 188, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.modal-content .modal-header {
  background: url(../img/modal-header-bg.png) no-repeat center;
  background-size: cover;
  border-radius: 5px 5px 0px 0px;
  border: 0;
  padding: 33px 40px;
}

/* line 194, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.modal-content .modal-header .modal-title {
  font-size: 18px;
  color: #ffffff;
}

/* line 198, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.modal-content .modal-header .close {
  color: #ffffff;
  opacity: 1;
  margin: 0;
  padding: 0;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 204, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.modal-content .modal-header .close:hover {
  opacity: 0.7;
}

/* line 209, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.modal-content .modal-body {
  padding: 40px 50px;
}

/* line 212, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.modal-content table.dataTable {
  padding: 0px;
}

/* line 215, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.modal-content .dataTables_filter > label {
  top: -60px;
}

/* line 220, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.radio-label {
  display: inline-block;
  color: #415094;
}

@media (max-width: 1280px) and (min-width: 992px) {
  /* line 224, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
  .radio-btn-flex {
    -ms-flex-direction: column;
    flex-direction: column;
  }
  /* line 228, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
  .radio-btn-flex .mr-30 {
    margin-bottom: 15px;
  }
}

@media (max-width: 359px) {
  /* line 224, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
  .radio-btn-flex {
    -ms-flex-direction: column;
    flex-direction: column;
  }
  /* line 235, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
  .radio-btn-flex .mr-30 {
    margin-bottom: 15px;
  }
}

/* hide input */
/* line 241, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.common-radio:empty {
  opacity: 0;
  visibility: hidden;
  position: relative;
  max-height: 0;
  display: block;
  margin-top: -10px;
}

/* style label */
/* line 251, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.common-radio:empty ~ label {
  position: relative;
  float: left;
  line-height: 16px;
  text-indent: 28px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
  padding-left: 25px;
}

/* line 268, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.bootstrap-datetimepicker-widget table td {
  width: 62px;
}

/* line 270, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.bootstrap-datetimepicker-widget table td.hour, .bootstrap-datetimepicker-widget table td.minute {
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 280, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.bootstrap-datetimepicker-widget table td span.glyphicon-chevron-up, .bootstrap-datetimepicker-widget table td span.glyphicon-chevron-down {
  position: relative;
  width: 30px;
  height: 30px;
  line-height: 28px;
}

/* line 286, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.bootstrap-datetimepicker-widget table td span.glyphicon-chevron-up:after, .bootstrap-datetimepicker-widget table td span.glyphicon-chevron-down:after {
  display: inline-block;
  font-family: "themify";
  font-size: 12px;
  color: #415094;
  border: 1px solid #7c32ff;
  border-radius: 40px;
  width: 30px;
  background: transparent;
  box-shadow: none;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 307, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.bootstrap-datetimepicker-widget table td span.glyphicon-chevron-up:after {
  content: "\e627";
}

/* line 312, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.bootstrap-datetimepicker-widget table td span.glyphicon-chevron-down:after {
  content: "\e62a";
}

/* line 316, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.bootstrap-datetimepicker-widget table td span.timepicker-hour, .bootstrap-datetimepicker-widget table td span.timepicker-minute {
  border: 1px solid #7c32ff;
  background: transparent;
  color: #415094;
  border-radius: 10px;
  height: 80px;
  line-height: 80px;
  width: 60px;
  font-size: 13px;
}

/* line 328, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.bootstrap-datetimepicker-widget table td.separator {
  display: none;
}

/* line 331, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.bootstrap-datetimepicker-widget table td .btn.btn-primary {
  color: #415094;
  font-size: 13px;
  font-weight: 600;
  border: 1px solid #7c32ff;
  padding: 29px 19px;
}

/* line 337, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.bootstrap-datetimepicker-widget table td .btn.btn-primary:hover {
  background: transparent;
  color: #415094;
}

/* line 346, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.datepicker {
  padding: 30px 25px;
}

/* line 348, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.datepicker.dropdown-menu {
  border: 0;
}

/* line 351, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.datepicker.dropdown-menu td {
  padding: 10px 12.5px;
}

/* line 354, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.datepicker.dropdown-menu th,
.datepicker.dropdown-menu td {
  color: #828bb2;
}

/* line 359, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.datepicker .datepicker thead tr:first-child th,
.datepicker .datepicker tfoot tr th {
  cursor: pointer;
  border-radius: 20px;
  font-size: 12px;
}

/* line 365, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.datepicker table tr td {
  border-radius: 20px;
}

/* line 374, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.datepicker table tr td.day {
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 376, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.datepicker table tr td.day:hover {
  border-radius: 20px;
}

/* line 385, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.datepicker thead tr:first-child th {
  position: relative;
}

/* line 387, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.datepicker thead tr:first-child th:after {
  content: '';
  position: absolute;
  left: 0px;
  top: 0px;
  z-index: -1;
  width: 99%;
  height: 100%;
  border-radius: 50px;
  border: 1px solid #7c32ff;
}

/* line 408, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.common-radio:empty ~ label:before {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  content: "";
  width: 16px;
  height: 16px;
  background: transparent;
  border-radius: 50px;
  border: 1px solid #415094;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* toggle on */
/* line 423, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.common-radio:checked ~ label:before {
  content: "";
  text-indent: 1px;
  color: #415094;
  background-color: transparent;
  border: 1px solid #415094;
  -webkit-transform: rotate(65deg);
  -moz-transform: rotate(65deg);
  -ms-transform: rotate(65deg);
  -o-transform: rotate(65deg);
  transform: rotate(65deg);
  font-size: 12px;
  font-weight: 600;
  border-top-color: transparent;
}

/* line 434, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.common-radio:checked ~ label:after {
  content: "\e64c";
  font-family: "themify";
  position: absolute;
  display: block;
  top: -2px;
  left: 3px;
  text-indent: 1px;
  color: #415094;
  background-color: transparent;
  border: 0px;
  -webkit-transform: rotate(8deg);
  -moz-transform: rotate(8deg);
  -ms-transform: rotate(8deg);
  -o-transform: rotate(8deg);
  transform: rotate(8deg);
  font-size: 14px;
  font-weight: 600;
}

/* line 450, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.dropdown-menu.top {
  display: block;
}

/* line 454, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.ripple {
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  position: absolute;
  opacity: 1;
}

/* line 463, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.rippleEffect {
  -webkit-animation: rippleDrop 0.6s linear;
  -moz-animation: rippleDrop 0.6s linear;
  -o-animation: rippleDrop 0.6s linear;
  animation: rippleDrop 0.6s linear;
}

@-webkit-keyframes rippleDrop {
  100% {
    -webkit-transform: scale(5);
    -moz-transform: scale(5);
    -ms-transform: scale(5);
    -o-transform: scale(5);
    transform: scale(5);
    opacity: 0;
  }
}

@-moz-keyframes rippleDrop {
  100% {
    -webkit-transform: scale(5);
    -moz-transform: scale(5);
    -ms-transform: scale(5);
    -o-transform: scale(5);
    transform: scale(5);
    opacity: 0;
  }
}

@-o-keyframes rippleDrop {
  100% {
    -webkit-transform: scale(5);
    -moz-transform: scale(5);
    -ms-transform: scale(5);
    -o-transform: scale(5);
    transform: scale(5);
    opacity: 0;
  }
}

@keyframes rippleDrop {
  100% {
    -webkit-transform: scale(5);
    -moz-transform: scale(5);
    -ms-transform: scale(5);
    -o-transform: scale(5);
    transform: scale(5);
    opacity: 0;
  }
}

/* line 474, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.invalid-feedback {
  margin-top: -24px;
}

/* line 476, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.invalid-feedback strong {
  position: relative;
  top: 22px;
  font-weight: 500;
}

/* line 482, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.invalid-feedback.invalid-select strong {
  top: 58px;
}

/* line 490, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.sms-breadcrumb.white-box {
  padding: 12px 30px;
}

@media (max-width: 1300px) and (min-width: 992px) {
  /* line 490, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
  .sms-breadcrumb.white-box {
    padding: 12px 15px;
  }
}

/* line 496, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.sms-breadcrumb .row.justify-content-between {
  -ms-flex-align: center;
  align-items: center;
}

/* line 500, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.sms-breadcrumb h1 {
  font-size: 18px;
  margin-bottom: 0;
  color: #415094;
}

/* line 506, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.sms-breadcrumb .bc-pages a {
  display: inline-block;
  color: #828bb2;
  font-size: 13px;
  position: relative;
  margin-right: 28px;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 513, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.sms-breadcrumb .bc-pages a:after {
  content: "|";
  color: #828bb2;
  font-size: 13px;
  position: absolute;
  top: 0;
  right: -16px;
}

/* line 521, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.sms-breadcrumb .bc-pages a:last-child {
  margin-right: 0px;
  color: #415094;
}

/* line 524, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.sms-breadcrumb .bc-pages a:last-child:after {
  content: none;
}

/* line 528, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.sms-breadcrumb .bc-pages a:hover {
  color: #7c32ff;
}

/* line 536, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.main-wrapper .fstElement {
  width: 90%;
}

/* line 538, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.main-wrapper .fstElement .fstControls {
  width: auto;
}

/* line 541, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.main-wrapper .fstElement .fstChoiceItem {
  padding: 4px 16px 4px 20px;
  background: #828bb2;
  border: none;
  font-size: 13px;
  text-transform: capitalize;
  margin: 1px 5px 5px 0px;
}

/* line 550, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.main-wrapper .fstResults {
  max-height: 250px;
}

/* line 552, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.main-wrapper .fstResults .fstResultItem {
  font-size: 14px;
  padding: 5px 10px;
  background: #ffffff;
  border-top: 1px solid #828bb2;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 558, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.main-wrapper .fstResults .fstResultItem:hover, .main-wrapper .fstResults .fstResultItem.fstSelected {
  color: #828bb2;
}

/* line 562, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.main-wrapper .fstResults .fstResultItem.fstSelected {
  border-color: #828bb2;
}

/* line 570, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.custom-table th {
  font-size: 12px;
  text-transform: uppercase;
}

/* line 574, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.custom-table th,
.custom-table td {
  border-bottom: 1px solid rgba(130, 139, 178, 0.3);
  padding: 5px 0px;
}

/* line 583, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.pagination .page-link {
  padding: 6px 0;
  width: 30px;
  text-align: center;
  color: #828bb2;
  font-size: 12px;
  margin-right: 5px;
  border-radius: 5px;
  border: 0px;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 593, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.pagination .page-link:hover {
  color: #ffffff;
}

/* line 601, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table-style {
  background: #ffffff;
  padding: 40px 30px;
  border-radius: 10px;
  box-shadow: 0px 10px 15px rgba(236, 208, 244, 0.3);
  margin: 0 auto;
  clear: both;
  border-collapse: separate;
  border-spacing: 0;
}

/* line 612, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table-style tr:first-child td {
  border-top: 0px;
}

/* line 616, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table-style tr th {
  text-transform: uppercase;
  font-size: 12px;
  color: #415094;
  font-weight: 600;
  padding: 10px 18px 10px 0px;
  border-bottom: 1px solid rgba(130, 139, 178, 0.3);
}

/* line 624, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table-style tr td {
  padding: 20px 10px 20px 0px;
  border-top: 1px solid rgba(130, 139, 178, 0.15);
}

/* line 631, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.school-table-style tfoot tr td {
  border-top: 1px solid rgba(130, 139, 178, 0.15) !important;
}

/* line 638, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.single-to-do {
  margin-bottom: 15px;
}

/* line 640, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.single-to-do:last-of-type {
  margin-bottom: 0;
}

/* line 643, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.single-to-do p {
  margin-bottom: 0px;
}

/* line 646, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.single-to-do label {
  display: block;
}

/* line 652, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.common-calendar .fc .fc-button-group > * {
  display: block;
}

/* line 655, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.common-calendar .fc-basic-view .fc-body .fc-row {
  height: 95px !important;
}

/* line 658, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.common-calendar .fc-ltr .fc-basic-view .fc-day-top .fc-day-number {
  float: left;
}

/* line 665, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.common-calendar .fc-day.fc-widget-content.fc-today {
  background: #828bb2;
}

/* line 668, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.common-calendar .fc-day-top.fc-today .fc-day-number {
  color: #ffffff;
}

/* line 671, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.common-calendar .fc-state-default.fc-corner-left,
.common-calendar .fc-button.fc-state-default {
  color: #415094;
  border: 1px solid #7c32ff;
  background: transparent;
  border-radius: 30px;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 678, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.common-calendar .fc-state-default.fc-corner-left:hover,
.common-calendar .fc-button.fc-state-default:hover {
  color: #ffffff;
  border: 1px solid transparent;
}

/* line 688, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.morris-hover {
  position: absolute;
  z-index: 1000;
}

/* line 692, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.morris-hover.morris-default-style {
  border-radius: 10px;
  padding: 6px;
  color: #415094;
  background: #ffffff;
  border: 1px solid #c738d8;
  font-family: "Poppins", sans-serif;
  font-size: 12px;
  text-align: center;
}

/* line 702, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.morris-hover.morris-default-style .morris-hover-row-label {
  font-weight: bold;
  margin: 0.25em 0;
}

/* line 706, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_component.scss */
.morris-hover.morris-default-style .morris-hover-point {
  white-space: nowrap;
  margin: 0.1em 0;
}

/*---------------------------------------------------- */
/* Main Content Area css
============================================================================================ */
/* line 4, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.main-wrapper {
  display: flex;
  width: 100%;
  align-items: stretch;
  /* Track */
  /* Handle */
  /* Handle on hover */
}

/* line 8, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.main-wrapper ::-webkit-scrollbar {
  width: 5px;
}

/* line 13, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.main-wrapper ::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
}

/* line 19, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.main-wrapper ::-webkit-scrollbar-thumb {
  background: #828bb2;
  border-radius: 10px;
}

/* line 25, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.main-wrapper ::-webkit-scrollbar-thumb:hover {
  background: #828bb2;
}

/* line 30, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

/* line 38, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.common-box-shadow, .school-table .dropdown .dropdown-toggle:hover, .school-table .dropdown .dropdown-toggle:focus, .school-table .dropdown .dropdown-menu, .bootstrap-datetimepicker-widget table td.hour:hover, .bootstrap-datetimepicker-widget table td.minute:hover, .bootstrap-datetimepicker-widget table td span.glyphicon-chevron-up:hover:after, .bootstrap-datetimepicker-widget table td span.glyphicon-chevron-down:hover:after, .datepicker.dropdown-menu, .datepicker table tr td.active.day, .datepicker table tr td.day:hover, .datepicker thead tr:first-child th:hover, .pagination .page-link:hover, .common-calendar .fc-month-view .fc-day.fc-widget-content.fc-today, .common-calendar .fc-state-default.fc-corner-left:hover,
.common-calendar .fc-button.fc-state-default:hover, .primary-btn:hover, .primary-btn.fix-gr-bg:hover, .nice-select .list, .sms-accordion .card, .admin .navbar .right-navbar .dropdown .badge, .single-cms-box:hover .single-cms {
  box-shadow: 0px 10px 20px rgba(108, 39, 255, 0.3);
}

/* line 42, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.bb-15 {
  border-bottom: 1px solid rgba(65, 80, 148, 0.15);
}

/* line 46, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.white-text, .bootstrap-datetimepicker-widget table td.hour:hover, .bootstrap-datetimepicker-widget table td.minute:hover, .bootstrap-datetimepicker-widget table td span.glyphicon-chevron-up:hover:after, .bootstrap-datetimepicker-widget table td span.glyphicon-chevron-down:hover:after, .datepicker table tr td.active.day, .datepicker table tr td.day:hover, .datepicker thead tr:first-child th:hover {
  color: #ffffff;
}

/* line 50, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.img-100 {
  max-width: 100px;
  max-height: 115px;
  height: auto;
  border-radius: 6px;
}

/* line 56, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.img-180 {
  max-width: 180px;
  max-height: 180px;
  height: auto;
}

/* line 62, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
#main-content {
  width: 100%;
  padding: 30px;
  margin-left: 15%;
  min-height: 100vh;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

@media (max-width: 1370px) {
  /* line 62, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
  #main-content {
    margin-left: 20%;
    padding: 30px 15px;
  }
}

@media (max-width: 991px) {
  /* line 62, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
  #main-content {
    margin-left: 0;
    margin-top: 50px;
  }
}

@media (max-width: 575px) {
  /* line 62, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
  #main-content {
    padding: 15px;
  }
}

/* Main Content Area css
============================================================================================ */
/* Main Title Area css
============================================================================================ */
/* line 88, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.main-title h3 {
  color: #415094;
  line-height: 1;
}

/* End Main Title Area css
============================================================================================ */
/* Start Gradient Area css
============================================================================================ */
/* line 100, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.gradient-bg, .school-table .dropdown .dropdown-toggle:hover, .school-table .dropdown .dropdown-toggle:focus, .bootstrap-datetimepicker-widget table td.hour:hover, .bootstrap-datetimepicker-widget table td.minute:hover, .bootstrap-datetimepicker-widget table td span.glyphicon-chevron-up:hover:after, .bootstrap-datetimepicker-widget table td span.glyphicon-chevron-down:hover:after, .datepicker table tr td.active.day, .datepicker table tr td.day:hover, .datepicker thead tr:first-child th:hover, .pagination .page-link:hover, .common-calendar .fc-month-view .fc-day.fc-widget-content.fc-today, .common-calendar .fc-state-default.fc-corner-left:hover,
.common-calendar .fc-button.fc-state-default:hover, .primary-btn.white:hover, .nice-select.tr-bg:hover, .admin .navbar .right-navbar .dropdown .badge, .admin .navbar .right-navbar .dropdown .primary-btn, .student-activities .single-activity .title:before, .single-cms-box:hover .single-cms .overlay, .client .events-item:hover .card .card-body .date, .client.light .overview-area .nav-tabs .nav-link:hover,
.client.light .overview-area .nav-tabs .nav-link.active, .client.color .overview-area .nav-tabs .nav-link:hover,
.client.color .overview-area .nav-tabs .nav-link.active {
  background: -webkit-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
  background: -moz-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
  background: -o-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
  background: linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
}

/* line 104, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.border-gradient {
  border-image: -webkit-linear-gradient(90deg, #415094 0%, #7c32ff 100%);
  border-image: -moz-linear-gradient(90deg, #415094 0%, #7c32ff 100%);
  border-image: -o-linear-gradient(90deg, #415094 0%, #7c32ff 100%);
  border-image: linear-gradient(90deg, #415094 0%, #7c32ff 100%);
}

/* line 108, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.gradient-bg2 {
  background: -webkit-linear-gradient(90deg, #415094 0%, #7c32ff 100%);
  background: -moz-linear-gradient(90deg, #415094 0%, #7c32ff 100%);
  background: -o-linear-gradient(90deg, #415094 0%, #7c32ff 100%);
  background: linear-gradient(90deg, #415094 0%, #7c32ff 100%);
}

/* line 112, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.gradient-color {
  background: -webkit-linear-gradient(90deg, #415094 0%, #7c32ff 100%);
  background: -moz-linear-gradient(90deg, #415094 0%, #7c32ff 100%);
  background: -o-linear-gradient(90deg, #415094 0%, #7c32ff 100%);
  background: linear-gradient(90deg, #415094 0%, #7c32ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* line 118, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.gradient-color2 {
  background: -webkit-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
  background: -moz-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
  background: -o-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
  background: linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* End Gradient Area css
============================================================================================ */
/* line 126, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.btn-success {
  font-size: 12px;
}

/* line 129, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn {
  display: inline-block;
  color: #415094;
  letter-spacing: 1px;
  font-family: "Poppins", sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 40px;
  padding: 0px 20px;
  outline: none !important;
  text-align: center;
  cursor: pointer;
  text-transform: uppercase;
  border: 0;
  border-radius: 5px;
  position: relative;
  overflow: hidden;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 147, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.form-control {
  background: transparent;
}

/* line 150, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn label {
  margin-bottom: 0px;
}

/* line 153, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn .common-checkbox:checked + label:before {
  color: #ffffff;
  top: -13px;
}

/* line 157, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn .common-checkbox + label:before {
  border: 1px solid #ffffff;
  top: -13px;
}

/* line 161, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn span {
  font-weight: 600;
}

/* line 163, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn span.pl {
  padding-left: 8px;
}

/* line 166, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn span.pr {
  padding-right: 8px;
}

/* line 173, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.small {
  letter-spacing: 1px;
  line-height: 30px;
  border-radius: 50px;
  font-weight: 600;
}

/* line 178, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.small:hover {
  color: #415094;
}

/* line 182, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.medium {
  line-height: 38px !important;
}

/* line 185, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.semi-large {
  line-height: 48px !important;
}

/* line 188, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.large {
  letter-spacing: 1px;
  line-height: 60px;
  border-radius: 5px;
  font-weight: 600;
  font-size: 24px;
}

/* line 194, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.large:hover {
  color: #415094;
}

/* line 198, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.fix-gr-bg {
  background: -webkit-linear-gradient(90deg, #7c32ff 0%, #c738d8 51%, #7c32ff 100%);
  background: -moz-linear-gradient(90deg, #7c32ff 0%, #c738d8 51%, #7c32ff 100%);
  background: -o-linear-gradient(90deg, #7c32ff 0%, #c738d8 51%, #7c32ff 100%);
  background: -ms-linear-gradient(90deg, #7c32ff 0%, #c738d8 51%, #7c32ff 100%);
  background: linear-gradient(90deg, #7c32ff 0%, #c738d8 51%, #7c32ff 100%);
  color: #ffffff;
  background-size: 200% auto;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 207, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.fix-gr-bg:hover {
  background-position: right center;
  color: #ffffff;
}

/* line 213, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.white {
  background: #ffffff;
}

/* line 215, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.white:hover {
  color: #ffffff;
}

/* line 220, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.tr-bg {
  background: transparent;
  border: 1px solid #c738d8;
  line-height: 28px;
}

/* line 225, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.bord-rad {
  border-radius: 30px;
}

/* line 228, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-btn.icon-only {
  padding: 0 9px;
  width: 30px;
  height: 30px;
  line-height: 30px;
  border-radius: 50px;
}

/* Start Primary Input Area css
============================================================================================ */
/* line 241, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.input-right-icon button {
  background: transparent;
  border: 0;
  display: inline-block;
  cursor: pointer;
  margin-left: -38px;
  position: relative;
  z-index: 999;
}

/* line 249, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.input-right-icon button.primary-btn-small-input {
  margin-left: -95px;
  padding: 0;
}

/* line 255, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.input-right-icon button i {
  position: relative;
  top: 12px;
}

/* line 262, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.input-effect {
  float: left;
  width: 100%;
  position: relative;
}

/* line 268, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-input {
  color: #415094;
  font-size: 13px;
  width: 100%;
  border: 0;
  padding: 4px 0;
  border-bottom: 1px solid rgba(130, 139, 178, 0.3);
  background-color: transparent;
  padding-bottom: 8px;
  position: relative;
  border-radius: 0px;
  z-index: 99;
}

/* line 280, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-input ~ .focus-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #7c32ff;
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}

/* line 289, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-input ~ label {
  position: absolute;
  left: 0px;
  width: 100%;
  top: 13px;
  color: #828bb2;
  z-index: 1;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 0;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 105, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.primary-input.placeholder {
  color: #828bb2;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

/* line 108, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.primary-input:-moz-placeholder {
  color: #828bb2;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

/* line 111, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.primary-input::-moz-placeholder {
  color: #828bb2;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

/* line 114, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.primary-input::-webkit-input-placeholder {
  color: #828bb2;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

/* line 308, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-input:focus {
  color: #415094 !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  border-color: transparent !important;
}

/* line 315, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-input.form-control[readonly] {
  background: transparent;
}

/* line 318, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-input.form-control.is-invalid {
  border-color: transparent;
  border-bottom: 1px solid rgba(130, 139, 178, 0.3);
}

/* line 330, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
textarea.primary-input {
  padding: 10px 0px 0px 0;
}

/* line 333, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.form-control:focus {
  border-color: rgba(130, 139, 178, 0.3) !important;
  box-shadow: none !important;
}

/* line 338, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-input:focus ~ .focus-border,
.has-content.primary-input ~ .focus-border {
  width: 100%;
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}

/* line 344, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.primary-input:focus ~ label,
.primary-input.read-only-input ~ label,
.has-content.primary-input ~ label {
  top: -14px;
  font-size: 11px;
  color: rgba(130, 139, 178, 0.8);
  text-transform: capitalize;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* End Primary Input Area css
============================================================================================ */
/* Start Primary Checkbox Area css
============================================================================================ */
/* line 360, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.common-checkbox + label {
  display: block;
  cursor: pointer;
}

/* line 365, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.common-checkbox {
  display: none;
}

/* line 369, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.common-checkbox + label:before {
  content: "";
  border: 1px solid #415094;
  border-radius: 2px;
  display: inline-block;
  font-size: 12px;
  font-weight: 600;
  width: 14px;
  height: 14px;
  line-height: 15px;
  padding-left: 0px;
  margin-right: 14px;
  vertical-align: bottom;
  color: transparent;
  position: relative;
  top: -6px;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 388, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.common-checkbox + label:active:before {
  transform: scale(0);
}

/* line 392, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.common-checkbox:checked + label:before {
  content: "\e64d";
  border: 0px;
  font-family: 'themify';
  border-radius: 2px;
  display: inline-block;
  font-size: 16px;
  font-weight: 600;
  width: 14px;
  height: 14px;
  line-height: 15px;
  padding-left: 0px;
  margin-right: 14px;
  vertical-align: bottom;
  color: #415094;
  position: relative;
  top: -6px;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 412, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.common-checkbox:disabled + label:before {
  transform: scale(1);
  border-color: #828bb2;
}

/* line 417, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.common-checkbox:checked:disabled + label:before {
  transform: scale(1);
  background-color: #bfb;
  border-color: #bfb;
}

/* End Primary Checkbox Area css
============================================================================================ */
/* line 426, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.niceSelect {
  border: 0px;
  border-bottom: 1px solid rgba(130, 139, 178, 0.3);
  border-radius: 0px;
  -webkit-appearance: none;
  -moz-appearance: none;
  color: #828bb2;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  padding: 0;
  background: transparent;
}

/* line 440, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select {
  border: 0;
  border-radius: 0px;
  padding-left: 0;
  padding-right: 30px;
}

/* line 445, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select:after {
  content: "\e62a";
  font-family: 'themify';
  border: 0;
  transform: rotate(0deg);
  margin-top: -16px;
  font-size: 12px;
  font-weight: 500;
  right: 18px;
  transform-origin: none;
  -webkit-transition: all 0.1s ease-in-out;
  -moz-transition: all 0.1s ease-in-out;
  -o-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
}

/* line 457, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select:focus {
  box-shadow: none;
}

/* line 461, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.open:after {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
  margin-top: 15px;
}

/* line 466, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select .current {
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 469, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select .list {
  width: 100%;
  left: auto;
  right: 0;
  border-radius: 0px 0px 10px 10px;
  margin-top: 1px;
  z-index: 9999 !important;
}

/* line 477, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select .list li {
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

/* line 481, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select .list li:first-child {
  color: #7c32ff;
}

/* line 483, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select .list li:first-child:hover {
  color: #7c32ff;
}

/* line 487, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select .list li:last-child {
  margin-bottom: 20px;
}

/* line 490, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select .list li:first-child {
  color: #7c32ff;
}

/* line 492, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select .list li:first-child:hover {
  color: #7c32ff;
}

/* line 496, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select .list li:hover {
  color: #415094;
}

/* line 501, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.tr-bg {
  background: transparent;
  border: 1px solid #7c32ff;
  border-radius: 31px;
  height: 30px;
  line-height: 28px;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
  padding: 0 36px 0px 30px;
}

/* line 509, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.tr-bg:after {
  color: #415094;
  margin-top: -14px;
}

/* line 514, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.tr-bg.open:after {
  margin-top: 6px;
}

/* line 518, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.tr-bg .current {
  color: #415094;
}

/* line 521, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.tr-bg .list {
  min-width: 180px;
}

/* line 524, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.tr-bg:hover {
  border: 1px solid transparent;
}

/* line 527, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.tr-bg:hover:after {
  color: #ffffff;
}

/* line 530, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.tr-bg:hover .current {
  color: #ffffff;
}

/* line 535, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.bb {
  background: transparent;
  border-bottom: 1px solid rgba(130, 139, 178, 0.3);
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
  height: 37px;
  position: relative;
}

/* line 541, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.bb:before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -1px;
  width: 0px;
  height: 2px;
  background: #7c32ff;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 551, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.bb .current {
  color: #828bb2;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  position: relative;
  bottom: -4px;
}

/* line 560, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.nice-select.bb.open:before {
  width: 100%;
}

/* line 568, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.sms-accordion .card {
  margin-bottom: 8px;
}

/* line 572, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.sms-accordion .card-header {
  border-bottom: 0px;
}

/* line 574, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.sms-accordion .card-header .card-link {
  color: #415094;
  font-weight: 500;
  font-size: 15p;
}

/* line 579, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.sms-accordion .card-header .primary-btn {
  color: #415094;
}

/* line 585, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.v-h-center {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 593, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.select2-container {
  width: 100% !important;
}

/* line 598, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.select2-container-multi .select2-choices {
  min-height: 38px;
  border: 1px solid rgba(130, 139, 178, 0.3);
  box-shadow: none;
  background-image: none;
}

/* line 603, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.select2-container-multi .select2-choices .select2-search-choice {
  background: #cad5f3;
  color: #415094;
  border: 0px;
  box-shadow: none;
  padding: 8px 18px;
}

/* line 609, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.select2-container-multi .select2-choices .select2-search-choice > div {
  margin-left: 15px;
}

/* line 612, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close {
  left: 6px;
  height: 16px;
  min-width: 17px;
  top: 7px;
  background-size: 80px;
}

/* line 618, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover {
  background-position: right 0px;
}

/* line 625, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_predefine.scss */
.select2-results .select2-highlighted {
  background: rgba(130, 139, 178, 0.3);
  color: #415094;
}

/*---------------------------------------------------- */
/* line 1, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
body.login {
  background: url(../img/login-bg.jpg) no-repeat center;
  background-size: cover;
}

/* line 4, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
body.login .footer_area {
  background: transparent;
  border: 0;
}

@media (max-width: 1199px) {
  /* line 4, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
  body.login .footer_area {
    text-align: center;
  }
}

/* line 10, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
body.login .footer_area a {
  color: #ffffff;
}

/* line 16, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area {
  /*Checkboxes styles*/
}

/* line 17, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area a {
  color: #828bb2;
}

/* line 19, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area a:hover {
  opacity: .8;
}

/* line 23, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area .login-height {
  min-height: 95vh;
}

/* line 26, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area .form-wrap {
  background: rgba(28, 0, 78, 0.25);
  padding: 50px 70px;
}

@media (max-width: 1199px) and (min-width: 992px) {
  /* line 26, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
  .login-area .form-wrap {
    padding: 50px 20px;
  }
}

@media (max-width: 480px) {
  /* line 26, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
  .login-area .form-wrap {
    padding: 50px 20px;
  }
}

/* line 38, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area h5 {
  margin-top: 40px;
  margin-bottom: 25px;
  color: #ffffff;
  letter-spacing: 2px;
  font-size: 14px;
  font-weight: 700;
}

/* line 47, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area .form-group .form-control {
  color: #828bb2;
  border: 0px;
  border-bottom: 1px solid rgba(247, 247, 255, 0.2);
  border-radius: 0px;
  background: transparent !important;
  padding: 0px 30px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 1px;
}

/* line 57, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area .form-group .form-control:focus {
  outline: none;
  box-shadow: none;
}

/* line 105, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.login-area .form-group .form-control.placeholder {
  position: relative;
  left: 0px;
  top: 0px;
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  color: #828bb2;
  letter-spacing: 1px;
}

/* line 108, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.login-area .form-group .form-control:-moz-placeholder {
  position: relative;
  left: 0px;
  top: 0px;
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  color: #828bb2;
  letter-spacing: 1px;
}

/* line 111, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.login-area .form-group .form-control::-moz-placeholder {
  position: relative;
  left: 0px;
  top: 0px;
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  color: #828bb2;
  letter-spacing: 1px;
}

/* line 114, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.login-area .form-group .form-control::-webkit-input-placeholder {
  position: relative;
  left: 0px;
  top: 0px;
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  color: #828bb2;
  letter-spacing: 1px;
}

/* line 72, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area .form-group a {
  font-size: 12px;
  font-weight: 700;
}

/* line 76, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area .form-group i {
  color: #828bb2;
  display: inline-block;
  position: relative;
  top: 6px;
  left: 14px;
  font-size: 12px;
  font-weight: 400;
}

/* line 87, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area .checkbox input {
  margin-right: 6px;
}

/* line 92, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area input[type="checkbox"] {
  display: none;
}

/* line 95, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area input[type="checkbox"] + label {
  display: block;
  position: relative;
  padding-left: 25px;
  margin-bottom: 20px;
  font: 12px/20px "Poppins", sans-serif;
  color: #828bb2;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* line 107, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area input[type="checkbox"] + label:last-child {
  margin-bottom: 0;
}

/* line 110, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area input[type="checkbox"] + label:before {
  content: '';
  display: block;
  width: 12px;
  height: 12px;
  border: 2px solid #828bb2;
  border-radius: 50px;
  position: absolute;
  left: 0;
  top: 4px;
  opacity: .6;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 123, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_login.scss */
.login-area input[type="checkbox"]:checked + label:before {
  width: 8px;
  top: 1px;
  left: 5px;
  border-radius: 0px;
  opacity: 1;
  border-top-color: transparent;
  border-left-color: transparent;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

/*---------------------------------------------------- */
/* line 1, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
#sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  overflow-x: hidden;
  min-width: 15%;
  max-width: 15%;
  background: transparent;
  color: #fff;
  margin-left: 0px;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

@media (max-width: 1370px) {
  /* line 1, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
  #sidebar {
    min-width: 20%;
    max-width: 20%;
  }
}

@media (max-width: 991px) {
  /* line 1, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
  #sidebar {
    min-width: 50%;
    max-width: 50%;
    margin-left: -50%;
    z-index: 999;
    background: #ffffff;
    box-shadow: 4px 0px 8px rgba(221, 221, 221, 0.4);
  }
}

@media (max-width: 991px) {
  /* line 25, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
  #sidebar.active {
    margin-left: 0px;
    z-index: 999;
    background: #ffffff;
    box-shadow: 4px 0px 8px rgba(221, 221, 221, 0.4);
  }
}

/* line 33, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
#sidebar .sidebar-header {
  padding: 26px;
}

/* line 35, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
#sidebar .sidebar-header img {
  cursor: pointer;
  max-width: 150px;
  height: auto;
}

/* line 42, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
#sidebar ul.components {
  padding: 0px;
}

/* line 46, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
#sidebar ul li a {
  padding: 9px 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  display: block;
  color: #415094;
  border-left: 6px solid transparent;
}

@media (max-width: 1300px) and (min-width: 992px) {
  /* line 46, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
  #sidebar ul li a {
    padding: 8px 12px;
  }
}

/* line 57, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
#sidebar ul li a span {
  margin-right: 15px;
}

/* line 60, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
#sidebar ul li a:hover, #sidebar ul li a.active {
  color: #ffffff;
  background: #415094;
  border-left: 6px solid #7c32ff;
  border-image-source: linear-gradient(#c738d8, #7c32ff);
  border-image-slice: 6;
}

/* line 69, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
#sidebar ul li ul {
  background: #415094;
  opacity: .7;
}

/* line 73, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
#sidebar ul li ul li a {
  font-size: 11px;
  padding-left: 55px;
  background: #415094;
  color: #ffffff;
}

@media (max-width: 1300px) and (min-width: 992px) {
  /* line 73, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
  #sidebar ul li ul li a {
    font-size: 10px;
  }
}

/* line 81, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
#sidebar ul li ul li a.active {
  color: #ffffff;
  border-left: 6px solid #7c32ff;
  border-image-source: linear-gradient(#c738d8, #7c32ff);
  border-image-slice: 6;
}

/* line 93, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
#sidebar a[data-toggle="collapse"] {
  position: relative;
}

/* line 96, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_sidebar.scss */
#sidebar .dropdown-toggle::after {
  display: block;
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
}

/*---------------------------------------------------- */
/* Start Header Area css
============================================================================================ */
/* line 5, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar {
  padding: 0px;
  border: none;
  border-radius: 0;
  margin-bottom: 30px;
}

@media (max-width: 991px) {
  /* line 5, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
  .admin .navbar {
    position: absolute;
    top: 20px;
    width: 92%;
    z-index: 10000;
  }
}

/* line 16, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .container-fluid {
  padding: 0;
}

@media (max-width: 991px) {
  /* line 19, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
  .admin .navbar .navbar-collapse {
    margin-top: 10px;
    padding: 30px;
    background: url(../img/body-bg.jpg) no-repeat right;
  }
}

@media (max-width: 1150px) and (min-width: 992px) {
  /* line 28, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
  .admin .navbar .nav-buttons .nav-item .primary-btn {
    padding: 0px 8px;
    font-size: 10px;
    line-height: 32px;
  }
}

/* line 38, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .nav-setting .nice-select {
  background: transparent;
  border-bottom: 0;
  padding-left: 12px;
  border-right: 1px solid rgba(130, 139, 178, 0.3);
}

@media (max-width: 1150px) and (min-width: 992px) {
  /* line 38, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
  .admin .navbar .nav-setting .nice-select {
    padding-left: 5px;
    padding-right: 25px;
  }
}

/* line 47, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .nav-setting .nice-select:after {
  margin-top: -22px;
}

/* line 51, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .nav-setting .nice-select.open:after {
  margin-top: 12px;
  right: 12px;
}

/* line 56, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .nav-setting .nice-select .current {
  color: #415094;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 59, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .nav-setting .nice-select .current:hover {
  color: #7c32ff;
}

/* line 66, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .nav-setting .nav-item:last-child .nice-select {
  border-right: 0px;
}

/* line 72, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar {
  -ms-flex-align: center;
  align-items: center;
}

@media (max-width: 991px) {
  /* line 72, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
  .admin .navbar .right-navbar {
    -ms-flex-align: start;
    align-items: start;
  }
}

/* line 81, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .notification-area .dropdown .dropdown-toggle {
  margin-left: -6px;
}

/* line 85, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .notification-area .badge {
  position: relative;
  left: 30px;
  top: -12px;
  padding: 4px 3px !important;
  max-width: 18px;
  max-height: 18px;
  box-shadow: none;
}

/* line 95, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown:hover > .dropdown-menu {
  max-height: 200px;
  opacity: 1;
  visibility: visible;
  transform: translateY(0px);
}

/* line 101, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown > .dropdown-toggle:active {
  pointer-events: none;
}

/* line 105, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .dropdown-toggle {
  margin-left: 12px;
  padding-left: 0px;
}

@media (max-width: 1150px) and (min-width: 992px) {
  /* line 105, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
  .admin .navbar .right-navbar .dropdown .dropdown-toggle {
    margin-left: 2px;
  }
}

/* line 111, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .dropdown-toggle img {
  max-width: 40px;
  height: auto;
}

/* line 116, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown p {
  margin-bottom: 0;
  line-height: 12px;
  color: #828bb2;
}

/* line 122, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown span:before {
  color: #415094;
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}

/* line 127, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown span:hover:before {
  color: #7c32ff;
}

/* line 133, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .flaticon-bell:before {
  font-size: 23px;
  position: relative;
  top: 4px;
}

/* line 139, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .dropdown-menu {
  top: 30px;
  right: 0;
  left: auto;
  border: 0;
  padding: 0;
  margin: 0;
  min-width: 290px;
  max-width: 290px;
  border-radius: 8px 8px 0px 0px;
  opacity: 0;
  visibility: hidden;
  max-height: 0;
  display: block;
  transform: translateY(50px);
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 155, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .dropdown-menu.profile-box {
  min-width: 220px;
  max-width: 440px;
}

/* line 158, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .dropdown-menu.profile-box .white-box {
  padding: 20px;
  border-radius: 8px;
}

/* line 162, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .dropdown-menu.profile-box .name, .admin .navbar .right-navbar .dropdown .dropdown-menu.profile-box .message {
  max-width: 440px;
}

/* line 167, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .dropdown-item {
  padding: 0px 20px;
}

/* line 170, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .single-message {
  border-bottom: 1px solid rgba(65, 80, 148, 0.1);
  padding: 15px 0px;
}

/* line 173, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .single-message .message-avatar {
  position: relative;
}

/* line 176, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .single-message .active-icon {
  position: absolute;
  top: 0px;
  right: 0px;
  height: 7px;
  width: 7px;
  background-color: #c738d8;
  border-radius: 50%;
  display: inline-block;
}

/* line 187, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .single-message:hover .name {
  color: #7c32ff;
}

/* line 194, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .single-notifi:hover .message {
  color: #7c32ff;
}

/* line 198, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .single-notifi:hover span:before {
  color: #7c32ff;
}

/* line 204, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .white-box {
  padding: 20px 0px 0px;
  border-radius: 8px 8px 0px 0px;
}

/* line 208, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .notification {
  font-size: 12px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(65, 80, 148, 0.3);
}

/* line 212, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .notification span {
  color: #415094;
}

/* line 216, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .name {
  font-size: 12px;
  color: #415094;
  margin-bottom: 6px;
  max-height: 15px;
  max-width: 127px;
  overflow: hidden;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 225, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .message {
  font-size: 12px;
  max-width: 127px;
  max-height: 13px;
  overflow: hidden;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 232, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .time {
  font-size: 12px;
}

/* line 235, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .badge {
  color: #ffffff;
  border-radius: 20px;
  font-size: 10px;
  padding: 4px 7px;
}

/* line 243, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .primary-btn {
  width: 100%;
  border-radius: 0px 0px 8px 8px;
  color: #ffffff;
}

/* line 250, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .profile-box ul {
  padding-top: 20px;
  border-top: 1px solid rgba(65, 80, 148, 0.1);
  margin-top: 20px;
}

/* line 255, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .profile-box ul li a {
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  color: #828bb2;
}

/* line 260, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .profile-box ul li a span {
  margin-right: 10px;
  color: #828bb2;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 267, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .profile-box ul li:hover a {
  color: #7c32ff;
}

/* line 270, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .right-navbar .dropdown .profile-box ul li:hover span {
  color: #7c32ff;
}

/* line 281, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .setting-area .dropdown .dropdown-item {
  padding: 0;
}

/* line 287, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .dropdown button {
  border: 0;
  background: transparent;
  cursor: pointer;
}

/* line 293, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.admin .navbar .dropdown-toggle::after {
  display: none;
}

@media (max-width: 991px) {
  /* line 297, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
  .admin #sidebarCollapse {
    background: #000000;
    color: #ffffff;
    position: relative;
    z-index: 9999;
    cursor: pointer;
  }
  /* line 304, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
  .admin #sidebarCollapse:focus {
    box-shadow: none;
    outline: none;
  }
}

@media (max-width: 991px) {
  /* line 312, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
  .search-bar {
    margin-bottom: 20px;
  }
}

/* line 316, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.search-bar li {
  min-width: 375px;
}

@media (max-width: 1499px) {
  /* line 316, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
  .search-bar li {
    min-width: auto;
  }
}

/* line 322, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.search-bar .ti-search {
  position: absolute;
  margin-left: 5px;
  height: 25px;
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #415094;
}

/* line 331, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.search-bar input {
  padding-left: 25px;
  height: 38px;
  padding-bottom: 19px;
  color: #415094;
  font-size: 14px;
}

/* line 337, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_header.scss */
.search-bar input:focus {
  border: 0;
  box-shadow: none;
  background: transparent;
}

/* line 105, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.search-bar input:focus.placeholder {
  bottom: 0px;
  left: 16px;
  opacity: 0;
}

/* line 108, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.search-bar input:focus:-moz-placeholder {
  bottom: 0px;
  left: 16px;
  opacity: 0;
}

/* line 111, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.search-bar input:focus::-moz-placeholder {
  bottom: 0px;
  left: 16px;
  opacity: 0;
}

/* line 114, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.search-bar input:focus::-webkit-input-placeholder {
  bottom: 0px;
  left: 16px;
  opacity: 0;
}

/* line 105, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.search-bar input.placeholder {
  color: #415094;
  bottom: 0px;
  left: 0;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 108, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.search-bar input:-moz-placeholder {
  color: #415094;
  bottom: 0px;
  left: 0;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 111, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.search-bar input::-moz-placeholder {
  color: #415094;
  bottom: 0px;
  left: 0;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 114, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.search-bar input::-webkit-input-placeholder {
  color: #415094;
  bottom: 0px;
  left: 0;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* End Header Area css
============================================================================================ */
/*---------------------------------------------------- */
/* line 2, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-details .nav-tabs {
  margin-left: 30px;
}

@media (max-width: 991px) {
  /* line 2, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
  .student-details .nav-tabs {
    margin-top: 50px;
  }
}

@media (max-width: 615px) {
  /* line 2, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
  .student-details .nav-tabs {
    -ms-flex-pack: center;
    justify-content: center;
  }
}

@media (max-width: 615px) {
  /* line 14, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
  .student-details .nav-tabs .nav-item {
    margin-bottom: 15px;
  }
}

/* line 20, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-details .nav-tabs .nav-link {
  background: #cad5f3;
  color: #415094;
  border: 0;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
  padding: 8px 25px;
  margin-right: 10px;
  border-radius: 0px;
}

/* line 31, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-details .nav-tabs .nav-link.active {
  background: #ffffff;
}

/* line 39, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-details .tab-content #studentExam div.dt-buttons {
  bottom: 0;
}

/* line 43, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-details .tab-content #studentExam table.dataTable {
  box-shadow: none;
  padding: 0;
  padding-top: 20px;
}

/* line 51, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-details .tab-content #studentDocuments .table thead th {
  border-bottom: 1px solid #dee2e6;
}

/* line 57, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-details .single-meta {
  border-bottom: 1px solid rgba(65, 80, 148, 0.15);
  padding: 7px 0px;
}

/* line 61, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-details .single-meta:last-of-type {
  border-bottom: 0;
  padding-bottom: 0;
}

/* line 67, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-details .single-info {
  border-bottom: 1px solid rgba(65, 80, 148, 0.15);
  padding: 14px 0px;
}

/* line 71, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-details .single-info:last-of-type {
  border-bottom: 0;
  padding-bottom: 0;
}

/* line 78, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.stu-sub-head {
  font-size: 13px;
  text-transform: uppercase;
  color: #415094;
  font-weight: 500;
  margin-bottom: 0;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(65, 80, 148, 0.3);
}

/* line 88, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-meta-box {
  position: relative;
}

/* line 91, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-meta-box .student-meta-top {
  background: url(../img/student/student-details-bg.png) no-repeat center;
  background-position: center;
  background-size: cover;
  min-height: 120px;
  border-radius: 10px 10px 0px 0px;
}

/* line 98, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-meta-box .student-meta-top.siblings-meta-top {
  background: url(../img/student/siblings-details-bg.png) no-repeat center;
  background-size: cover;
}

/* line 103, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-meta-box .student-meta-top.staff-meta-top {
  background: url(../img/staff/staff-details-bg.png) no-repeat center;
  background-size: cover;
}

/* line 109, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-meta-box .student-meta-img {
  position: absolute;
  top: 50px;
  left: 30px;
  border-radius: 6px;
}

/* line 116, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-meta-box .name {
  color: #828bb2;
}

/* line 120, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-meta-box .value {
  color: #415094;
  font-weight: 500;
  text-align: right;
}

/* line 127, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-admit-card {
  position: relative;
}

/* line 130, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-admit-card .admit-header {
  background: url(../img/student/admit-header-bg.png) no-repeat center;
  background-position: center;
  background-size: cover;
  min-height: 120px;
  border-radius: 5px 5px 0px 0px;
}

/* line 138, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-admit-card .admit-meta-img {
  position: absolute;
  top: 50px;
  right: 30px;
  border-radius: 6px;
}

/* line 153, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .sub-activity-box:last-of-type {
  margin-bottom: 0;
}

/* line 160, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .single-activity:last-child .sub-activity {
  padding-bottom: 0px;
}

/* line 163, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .single-activity:last-child .sub-activity:after {
  height: 75%;
}

/* line 169, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .single-activity .title,
.student-activities .single-activity .sub-activity {
  position: relative;
  margin-bottom: 0;
}

/* line 174, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .single-activity .title:before,
.student-activities .single-activity .sub-activity:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 13px;
  height: 13px;
  border-radius: 20px;
  box-shadow: 0px 10px 15px rgba(108, 39, 255, 0.2);
}

/* line 185, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .single-activity .title:after,
.student-activities .single-activity .sub-activity:after {
  content: '';
  position: absolute;
  left: -27px;
  top: 12px;
  width: 1px;
  height: 100%;
  background: #828bb2;
}

/* line 196, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .single-activity .title {
  margin-left: 102px;
  padding-bottom: 25px;
  color: #415094;
  font-size: 12px;
}

/* line 202, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .single-activity .title:before {
  left: -33px;
}

/* line 208, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .single-activity .subtitle {
  color: #415094;
  font-size: 12px;
}

/* line 213, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .single-activity .sub-activity {
  min-width: 60%;
  max-width: 60%;
  margin-right: 50px;
  margin-left: 26px;
  margin-bottom: 0px;
  padding-bottom: 30px;
}

@media (max-width: 1380px) {
  /* line 213, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
  .student-activities .single-activity .sub-activity {
    min-width: 48%;
    max-width: 48%;
  }
}

@media (max-width: 1199px) {
  /* line 213, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
  .student-activities .single-activity .sub-activity {
    min-width: 38%;
    max-width: 38%;
  }
}

/* line 229, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .single-activity .sub-activity:before {
  left: -33px;
  background: #ffffff;
  border: 3px solid #7c32ff;
}

/* line 235, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .single-activity .sub-activity p {
  margin-bottom: 0;
}

/* line 240, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-activities .single-activity .time {
  margin-bottom: 0;
  color: #415094;
  font-size: 12px;
  min-width: 76px;
}

/* line 272, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-attendance table.dataTable thead th {
  padding-left: 0;
  padding-right: 6px;
  vertical-align: text-top;
}

/* line 278, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-attendance table.dataTable thead .sorting:before,
.student-attendance table.dataTable thead .sorting:after,
.student-attendance table.dataTable thead .sorting_asc:after,
.student-attendance table.dataTable thead .sorting_desc:after {
  content: none;
}

/* line 288, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
table.dataTable thead .sorting {
  vertical-align: text-top;
}

/* line 293, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.single-report-admit {
  position: relative;
}

/* line 296, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.single-report-admit .card {
  border: 0px;
}

/* line 300, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.single-report-admit .card-header {
  background: url(../img/report-admit-bg.png) no-repeat center;
  background-size: cover;
  border-radius: 5px 5px 0px 0px;
  border: 0;
  padding: 30px 30px;
}

/* line 307, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.single-report-admit .card-header .logo-img {
  max-width: 130px;
  height: auto;
}

/* line 313, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.single-report-admit .report-admit-img {
  position: absolute;
  top: 40px;
  right: 30px;
  border-radius: 6px;
}

/* line 320, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.single-report-admit .card-body {
  background: -webkit-linear-gradient(90deg, #d8e6ff 0%, #ecd0f4 100%);
  background: -moz-linear-gradient(90deg, #d8e6ff 0%, #ecd0f4 100%);
  background: -o-linear-gradient(90deg, #d8e6ff 0%, #ecd0f4 100%);
  background: linear-gradient(90deg, #d8e6ff 0%, #ecd0f4 100%);
}

/* line 326, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.single-report-admit table tr th {
  text-transform: uppercase;
  font-size: 12px;
  color: #415094;
  border-bottom: 1px solid #a2a8c5;
  padding: 5px 0px;
}

/* line 336, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.single-report-admit table tr td {
  border-bottom: 1px solid #c1c6d9;
  padding: 8px 0px;
}

/* line 342, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.single-report-admit table tr:last-child td {
  border-bottom: 0px;
}

/* line 352, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.radio-img input[type=radio] {
  opacity: 0;
  display: none;
}

/* line 357, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.radio-img input[type="radio"]:checked + img {
  border: 2px solid #7c32ff;
}

/* line 361, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.radio-img img {
  cursor: pointer;
  border-radius: 10px;
  border: 2px solid transparent;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 371, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-certificate {
  position: relative;
}

/* line 374, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-certificate .signature {
  font-size: 10px;
  padding-bottom: 10px;
  text-transform: uppercase;
}

/* line 380, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_student.scss */
.student-certificate .certificate-position {
  position: absolute;
  top: 49%;
  left: 9%;
  right: 9%;
  bottom: 14%;
}

/*---------------------------------------------------- */
/* line 2, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_front-cms.scss */
.single-cms-box .cms-img {
  border-radius: 5px;
}

/* line 5, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_front-cms.scss */
.single-cms-box .single-cms {
  position: relative;
  box-shadow: none;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 9, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_front-cms.scss */
.single-cms-box .single-cms .overlay {
  background: transparent;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 14, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_front-cms.scss */
.single-cms-box .icons {
  position: absolute;
  top: 70%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  opacity: 0;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 21, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_front-cms.scss */
.single-cms-box .icons i {
  padding: 9px;
  border-radius: 20px;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
  font-size: 12px;
  cursor: pointer;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 29, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_front-cms.scss */
.single-cms-box .icons i:hover {
  color: #828bb2;
  background: #ffffff;
}

/* line 35, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_front-cms.scss */
.single-cms-box .btn {
  background: transparent;
  padding: 0;
}

/* line 38, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_front-cms.scss */
.single-cms-box .btn:focus {
  outline: none;
  box-shadow: none;
}

/* line 46, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_front-cms.scss */
.single-cms-box:hover .single-cms .overlay {
  opacity: .9;
}

/* line 51, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_front-cms.scss */
.single-cms-box:hover .icons {
  top: 50%;
  opacity: 1;
}

/* line 55, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_front-cms.scss */
.single-cms-box:hover .btn {
  background: transparent;
}

/*---------------------------------------------------- */
/* line 1, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_exam.scss */
.exam-cus-btns {
  margin-top: -150px;
}

/*---------------------------------------------------- */
/* line 2, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_fees.scss */
.invoice-details-left input[type="file"] {
  display: none;
}

/* line 5, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_fees.scss */
.invoice-details-left .company-logo {
  border: 1px solid #c738d8;
  display: inline-block;
  padding: 0px 12px;
  font-size: 14px;
  height: 60px;
  line-height: 60px;
  color: #415094;
  cursor: pointer;
}

/* line 14, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_fees.scss */
.invoice-details-left .company-logo i {
  font-size: 24px;
  color: #c738d8;
  position: relative;
  top: 6px;
  margin-right: 10px;
}

/* line 23, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_fees.scss */
.invoice-details-left .business-info p {
  margin-bottom: 0;
}

/* line 25, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_fees.scss */
.invoice-details-left .business-info p:first-of-type {
  margin-bottom: 10px;
}

/* line 32, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_fees.scss */
.invoice-details-right {
  text-align: right;
}

/* line 34, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_fees.scss */
.invoice-details-right h1 {
  font-size: 36px;
  margin-bottom: 15px;
}

/* line 38, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_fees.scss */
.invoice-details-right p {
  margin-bottom: 3px;
}

/* line 44, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_fees.scss */
.customer-info h2 {
  font-weight: 300;
  color: #000000;
  margin-bottom: 25px;
}

/* line 52, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_fees.scss */
.client-info h3 {
  color: #000000;
}

/* line 55, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_fees.scss */
.client-info p {
  color: #000000;
  margin-bottom: 0;
}

/* line 58, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_fees.scss */
.client-info p:first-of-type {
  margin-bottom: 10px;
}

/*---------------------------------------------------- */
/* line 3, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_settings.scss */
.base-setup .card .card-body {
  padding: 0px 15px;
}

/* line 6, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_settings.scss */
.base-setup .card .card-header {
  background: rgba(65, 80, 148, 0.85);
}

/* line 8, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_settings.scss */
.base-setup .card .card-header a,
.base-setup .card .card-header .primary-btn {
  color: #ffffff;
}

/* line 12, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_settings.scss */
.base-setup .card .card-header .tr-bg {
  border: 1px solid #ffffff;
}

/* line 16, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_settings.scss */
.base-setup .card .card-header .icon-only:before {
  content: "\e62a";
  font-family: 'themify';
}

/* line 21, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_settings.scss */
.base-setup .card .card-header .icon-only.collapsed:before {
  content: "\e627";
  font-family: 'themify';
}

/*---------------------------------------------------- */
/*---------------------------------------------------- */
/* Footer Area css
============================================================================================ */
/* End Footer Area css
============================================================================================ */
/* line 1, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_reset.scss */
body.client {
  font-size: 14px;
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  color: #828bb2;
  line-height: 26px;
}

/* line 7, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_reset.scss */
body.client.dark {
  background: url(../img/client/dark-body-bg.jpg) no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

/* line 12, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_reset.scss */
body.client.light {
  background: #ffffff;
}

/* line 15, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_reset.scss */
body.client.color {
  background: url(../img/client/color-body-bg.jpg) no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

@media (min-width: 1200px) {
  /* line 23, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_reset.scss */
  .container.box-1420 {
    max-width: 1420px;
  }
}

/*---------------------------------------------------- */
/* line 2, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_predefine.scss */
.client .title {
  color: #ffffff;
  font-size: 18px;
  text-transform: uppercase;
  font-weight: 500;
  margin-bottom: 20px;
}

/* line 9, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_predefine.scss */
.client .section-gap {
  padding: 100px 0px;
}

/* line 12, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_predefine.scss */
.client .section-gap-top {
  padding-top: 100px;
}

/* line 15, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_predefine.scss */
.client .section-gap-bottom {
  padding-bottom: 100px;
}

/* line 18, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_predefine.scss */
.client .client-btn {
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 25, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_predefine.scss */
.client .client-btn:hover {
  color: #415094;
}

/* line 31, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_predefine.scss */
.client.light .title,
.client.light .client-btn,
.client.light .footer_area .f_widget .f_title h4, .client.color .title,
.client.color .client-btn,
.client.color .footer_area .f_widget .f_title h4 {
  color: #415094;
}

/*---------------------------------------------------- */
/* line 2, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area {
  position: relative;
  width: 100%;
  z-index: 99;
  transition: background 0.4s, all 0.3s linear;
}

@media (max-width: 991px) {
  /* line 2, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
  .client .header-area {
    position: fixed;
    padding: 10px 0px;
    top: 0px;
    background: #415094;
  }
}

@media (max-width: 575px) {
  /* line 2, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
  .client .header-area {
    padding: 10px 20px;
  }
}

@media (max-width: 991px) {
  /* line 16, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
  .client .header-area .navbar-collapse {
    margin-top: 20px;
  }
}

/* line 21, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar {
  background: transparent;
  padding: 0px;
  border: 0px;
  border-radius: 0px;
}

/* line 26, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .navbar-toggler {
  color: #ffffff;
  font-size: 20px;
}

/* line 31, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .nav .nav-item {
  margin-right: 45px;
}

/* line 33, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .nav .nav-item .nav-link {
  font: 500 12px/80px "Poppins", sans-serif;
  text-transform: uppercase;
  color: #fff;
  padding: 0px;
  display: inline-block;
}

/* line 39, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .nav .nav-item .nav-link:after {
  display: none;
}

@media (max-width: 991px) {
  /* line 33, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
  .client .header-area .navbar .nav .nav-item .nav-link {
    font: 500 12px/40px "Poppins", sans-serif;
  }
}

/* line 51, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .nav .nav-item.submenu {
  position: relative;
}

/* line 53, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .nav .nav-item.submenu ul {
  border: none;
  padding: 0px;
  border-radius: 0px;
  box-shadow: none;
  margin: 0px;
  background: #fff;
}

@media (min-width: 992px) {
  /* line 53, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
  .client .header-area .navbar .nav .nav-item.submenu ul {
    position: absolute;
    top: 120%;
    left: 0px;
    min-width: 200px;
    text-align: left;
    opacity: 0;
    transition: all 300ms ease-in;
    visibility: hidden;
    display: block;
    border: none;
    padding: 0px;
    border-radius: 0px;
  }
}

/* line 74, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .nav .nav-item.submenu ul:before {
  content: "";
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 10px 0 10px;
  border-color: #eeeeee transparent transparent transparent;
  position: absolute;
  right: 24px;
  top: 45px;
  z-index: 3;
  opacity: 0;
  transition: all 400ms linear;
}

/* line 88, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .nav .nav-item.submenu ul .nav-item {
  display: block;
  float: none;
  margin-right: 0px;
  border-bottom: 1px solid #ededed;
  margin-left: 0px;
  transition: all 0.4s linear;
}

/* line 95, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .nav .nav-item.submenu ul .nav-item .nav-link {
  line-height: 45px;
  color: #415094;
  padding: 0px 30px;
  transition: all 150ms linear;
  display: block;
  margin-right: 0px;
}

/* line 103, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .nav .nav-item.submenu ul .nav-item:last-child {
  border-bottom: none;
}

/* line 107, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .nav .nav-item.submenu ul .nav-item:hover .nav-link {
  background: #415094;
  color: #fff;
}

@media (min-width: 992px) {
  /* line 115, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
  .client .header-area .navbar .nav .nav-item.submenu:hover ul {
    visibility: visible;
    opacity: 1;
    top: 100%;
  }
}

/* line 121, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .nav .nav-item.submenu:hover ul .nav-item {
  margin-top: 0px;
}

/* line 127, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .nav .nav-item:last-child {
  margin-right: 0px;
}

/* line 132, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .search-bar {
  font-size: 12px;
  line-height: 60px;
  display: inline-block;
  color: #ffffff;
  margin-left: 195px;
}

@media (max-width: 1200px) and (min-width: 992px) {
  /* line 132, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
  .client .header-area .navbar .search-bar {
    margin-left: 50px;
  }
}

@media (max-width: 991px) {
  /* line 132, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
  .client .header-area .navbar .search-bar {
    margin-left: 0px;
    margin-top: 10px;
  }
}

/* line 145, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .search-bar .ti-search {
  color: #828bb2;
}

/* line 148, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .search-bar input {
  color: #828bb2 !important;
}

/* line 105, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.client .header-area .navbar .search-bar input.placeholder {
  color: #828bb2;
}

/* line 108, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.client .header-area .navbar .search-bar input:-moz-placeholder {
  color: #828bb2;
}

/* line 111, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.client .header-area .navbar .search-bar input::-moz-placeholder {
  color: #828bb2;
}

/* line 114, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/_mixins.scss */
.client .header-area .navbar .search-bar input::-webkit-input-placeholder {
  color: #828bb2;
}

/* line 153, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area .navbar .search-bar input:focus {
  color: #ffffff !important;
  font-weight: 300;
}

/* line 161, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area.navbar_fixed .main_menu {
  position: fixed;
  width: 100%;
  top: -70px;
  left: 0;
  right: 0;
  background-image: -moz-linear-gradient(0deg, #141da2 0%, #9b5cf6 100%);
  background-image: -webkit-linear-gradient(0deg, #141da2 0%, #9b5cf6 100%);
  background-image: -ms-linear-gradient(0deg, #141da2 0%, #9b5cf6 100%);
  transform: translateY(70px);
  transition: transform 500ms ease, background 500ms ease;
  -webkit-transition: transform 500ms ease, background 500ms ease;
  box-shadow: 0px 3px 16px 0px rgba(0, 0, 0, 0.1);
}

/* line 177, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client .header-area.navbar_fixed .main_menu .navbar .nav .nav-item .nav-link {
  line-height: 70px;
}

/* line 188, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_header.scss */
.client.light .header-area .navbar .nav .nav-item .nav-link, .client.color .header-area .navbar .nav .nav-item .nav-link {
  color: #415094;
}

/*---------------------------------------------------- */
/* line 1, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client {
  /* Start Home Banner Area css
	============================================================================================ */
  /* End Banner Area css
	============================================================================================ */
  /* Start News Area css
	============================================================================================ */
  /* End News Area css
	============================================================================================ */
  /* Start Notice Board Area css
	============================================================================================ */
  /* End Notice Board Area css
	============================================================================================ */
  /* Start Academic Area css
	============================================================================================ */
  /* End Academic Area css
	============================================================================================ */
  /* Start Events Area css
	============================================================================================ */
  /* End Events Area css
	============================================================================================ */
  /* Start Testimonial Area css
	============================================================================================ */
  /* End Testimonial Area css
	============================================================================================ */
  /* Start Fact Area css
	============================================================================================ */
  /* End Fact Area css
	============================================================================================ */
  /* Start Department Area css
	============================================================================================ */
  /* End Department Area css
	============================================================================================ */
  /* Start About Us Area css
	============================================================================================ */
  /* End About Us Area css
	============================================================================================ */
  /* Start Course Overview Area css
	============================================================================================ */
  /* End Course Overview Area css
	============================================================================================ */
  /* Start Contact Area css
	============================================================================================ */
  /* End Contact Area css
	============================================================================================ */
}

/* line 4, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .home-banner-area {
  min-height: 720px;
  display: flex;
  background: linear-gradient(0deg, rgba(124, 50, 255, 0.6), rgba(199, 56, 216, 0.6)), url(../img/client/home-banner1.jpg) no-repeat center;
  background-size: cover;
  z-index: 1;
}

@media (max-width: 991px) {
  /* line 4, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
  .client .home-banner-area {
    margin-top: 120px;
  }
}

/* line 15, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .banner-area {
  min-height: 450px;
  display: flex;
  background: linear-gradient(0deg, rgba(124, 50, 255, 0.6), rgba(199, 56, 216, 0.6)), url(../img/client/common-banner1.jpg) no-repeat center;
  background-size: cover;
  z-index: 1;
}

@media (max-width: 991px) {
  /* line 15, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
  .client .banner-area {
    margin-top: 120px;
  }
}

/* line 27, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .banner-area .banner-inner .banner-content h2 {
  font-size: 60px;
  max-width: 500px;
  margin: 0px auto 10px;
  line-height: 1.2;
}

@media (max-width: 767px) {
  /* line 27, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
  .client .banner-area .banner-inner .banner-content h2 {
    font-size: 40px;
  }
}

/* line 40, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .banner-inner {
  width: 100%;
  display: flex;
}

/* line 43, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .banner-inner .container {
  vertical-align: middle;
  align-self: center;
}

/* line 47, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .banner-inner .banner-content {
  width: 100%;
  color: #ffffff;
  vertical-align: middle;
  align-self: center;
  text-align: center;
}

/* line 53, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .banner-inner .banner-content h5 {
  color: #ffffff;
  display: inline-block;
  margin: 0 auto;
  text-transform: uppercase;
  border-top: 1px solid #ffffff;
  border-bottom: 1px solid #ffffff;
  padding: 6px 0px;
  letter-spacing: 1.5px;
}

/* line 63, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .banner-inner .banner-content h2 {
  color: #ffffff;
  font-size: 100px;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 10px;
}

/* line 70, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .banner-inner .banner-content p {
  color: #ffffff;
  max-width: 550px;
  font-size: 14px;
  margin: 0px auto 40px;
}

/* line 83, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-area {
  margin-bottom: 60px;
}

/* line 86, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-item {
  position: relative;
  overflow: hidden;
  margin-bottom: 40px;
}

/* line 90, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-item .news-img {
  position: relative;
}

/* line 92, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-item .news-img:before {
  content: "";
  background: #27006e;
  background: -webkit-linear-gradient(90deg, #27006e 0%, #3a0d7e 100%);
  background: -moz-linear-gradient(90deg, #27006e 0%, #3a0d7e 100%);
  background: -o-linear-gradient(90deg, #27006e 0%, #3a0d7e 100%);
  background: linear-gradient(90deg, #27006e 0%, #3a0d7e 100%);
  position: absolute;
  left: 0px;
  top: 0px;
  height: 100%;
  width: 100%;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 103, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-item .news-img img {
  opacity: .75;
}

/* line 107, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-item .news-img:hover img {
  opacity: .2;
}

/* line 112, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-item .news-text {
  position: absolute;
  left: 10%;
  bottom: 50px;
  width: 90%;
}

/* line 117, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-item .news-text h4 {
  max-height: 72px;
  overflow: hidden;
  font-size: 16px;
  color: #ffffff;
  padding-right: 20px;
  margin-bottom: 0px;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 125, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-item .news-text h4 a {
  color: #ffffff;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 128, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-item .news-text h4 a:hover {
  opacity: .8;
}

/* line 133, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-item .news-text .date {
  color: #ffffff;
  font-size: 12px;
  text-transform: uppercase;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 142, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-details-area h1 {
  color: #ffffff;
  font-size: 36px;
}

/* line 147, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-details-area .meta .date {
  font-size: 12px;
  margin-right: 30px;
}

/* line 150, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-details-area .meta .date span {
  color: #ffffff;
}

/* line 155, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-details-area h3 {
  color: #ffffff;
}

/* line 158, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-details-area p {
  margin-bottom: 30px;
}

/* line 160, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-details-area p:last-child {
  margin-bottom: 0px;
}

/* line 164, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .news-details-area .notice-board {
  max-height: none;
}

@media (max-width: 991px) {
  /* line 173, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
  .client .notice-board-area {
    margin-top: 50px;
  }
}

/* line 178, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .notice-board {
  max-height: 340px;
  overflow-y: auto;
}

@media (max-width: 1200px) and (min-width: 992px) {
  /* line 178, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
  .client .notice-board {
    max-height: 270px;
  }
}

/* line 185, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .notice-item {
  padding-bottom: 18px;
  margin-top: 18px;
  border-bottom: 2px solid #415094;
}

/* line 189, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .notice-item:first-child {
  margin-top: 0px;
}

/* line 192, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .notice-item .date {
  font-size: 12px;
  text-transform: uppercase;
  margin-bottom: 7px;
}

/* line 197, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .notice-item h4 {
  color: #ffffff;
  font-size: 16px;
  margin-bottom: 0px;
}

/* line 208, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .academics-area {
  margin-bottom: 60px;
}

/* line 211, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .academic-item {
  margin-bottom: 40px;
}

/* line 213, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .academic-item .academic-text {
  margin-top: 25px;
}

/* line 215, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .academic-item .academic-text h4 {
  max-height: 48px;
  overflow: hidden;
  margin-bottom: 12px;
}

/* line 219, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .academic-item .academic-text h4 a {
  color: #ffffff;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 222, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .academic-item .academic-text h4 a:hover {
  opacity: .6;
}

/* line 227, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .academic-item .academic-text p {
  max-height: 48px;
  overflow: hidden;
  margin-bottom: 18px;
}

/* line 239, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .events-area {
  margin-bottom: 60px;
}

/* line 242, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .events-item {
  margin-bottom: 40px;
}

/* line 244, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .events-item .card {
  background: transparent;
  border-radius: 0px;
}

/* line 247, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .events-item .card .card-img-top {
  border-radius: 0px;
}

/* line 250, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .events-item .card .card-body {
  position: relative;
  background: #ffffff;
}

/* line 253, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .events-item .card .card-body .date {
  position: absolute;
  top: -30px;
  left: 20px;
  background: #415094;
  display: inline-block;
  padding: 12px;
  max-width: 60px;
  text-align: center;
  color: #ffffff;
  font-size: 12px;
  text-transform: uppercase;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 268, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .events-item .card .card-title {
  max-height: 72px;
  overflow: hidden;
  margin-top: 40px;
  font-size: 16px;
}

/* line 274, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .events-item .card .card-text {
  font-size: 12px;
  text-transform: uppercase;
}

/* line 294, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .testimonial-area {
  background: url(../img/client/testimonial-bg.jpg) no-repeat center center;
  background-size: cover;
  position: relative;
}

/* line 298, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .testimonial-area .overlay-bg {
  background: rgba(39, 0, 110, 0.8);
}

/* line 302, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .testimonial-area .owl-nav {
  position: absolute;
  left: 50%;
  bottom: -15px;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  display: -ms-flexbox;
  display: flex;
}

/* line 311, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .testimonial-area .owl-nav .owl-prev img,
.client .testimonial-area .owl-nav .owl-next img {
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 313, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .testimonial-area .owl-nav .owl-prev img:hover,
.client .testimonial-area .owl-nav .owl-next img:hover {
  filter: brightness(50%);
  -o-filter: brightness(50%);
  -ms-filter: brightness(50%);
  -moz-filter: brightness(50%);
  -webkit-filter: brightness(50%);
}

/* line 318, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .testimonial-area .owl-nav .owl-prev {
  margin-right: 30px;
}

@media (max-width: 991px) {
  /* line 302, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
  .client .testimonial-area .owl-nav {
    display: none;
  }
}

/* line 327, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .single-testimonial {
  position: relative;
  z-index: 9;
  padding-bottom: 85px;
}

/* line 331, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .single-testimonial .thumb {
  margin-right: 20px;
}

/* line 334, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .single-testimonial h4 {
  color: #ffffff;
  font-size: 18px;
  margin-bottom: 5px;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 339, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .single-testimonial h4:hover {
  color: #415094;
  cursor: pointer;
}

/* line 344, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .single-testimonial .desc {
  max-width: 810px;
  font-style: italic;
  font-size: 16px;
  margin: 20px auto 0px;
}

/* line 357, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .fact-area .white-box.single-summery {
  box-shadow: none;
}

/* line 367, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .department-area h3 {
  color: #ffffff;
  margin-bottom: 10px;
}

/* line 378, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .info-area .info-thumb {
  overflow: hidden;
  display: inline-block;
}

@media (max-width: 800px) {
  /* line 384, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
  .client .info-area .info-content {
    text-align: center;
    padding: 80px 30px 80px 0;
  }
}

/* line 390, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .info-area .info-content {
  position: relative;
  background: rgba(65, 80, 148, 0.3);
  padding: 95px 80px;
  top: -4px;
}

@media (max-width: 991px) {
  /* line 390, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
  .client .info-area .info-content {
    margin-top: 30px;
  }
}

@media (max-width: 768px) {
  /* line 390, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
  .client .info-area .info-content {
    padding: 30px;
  }
}

/* line 401, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .info-area .info-content h2 {
  color: #ffffff;
  margin-bottom: 20px;
}

/* line 405, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .info-area .info-content p {
  margin-bottom: 0;
}

/* line 410, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .info-area .info-left {
  z-index: 2;
}

@media (max-width: 800px) {
  /* line 410, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
  .client .info-area .info-left {
    margin-top: 0px;
    margin-bottom: 40px;
  }
}

/* line 424, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .overview-area .nav-tabs {
  border-bottom: 1px solid rgba(130, 139, 178, 0.3);
}

@media (max-width: 991px) {
  /* line 424, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
  .client .overview-area .nav-tabs {
    margin-top: 0px;
  }
}

/* line 429, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .overview-area .nav-tabs .nav-link {
  background: rgba(82, 101, 165, 0.3);
  color: #828bb2;
}

/* line 432, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .overview-area .nav-tabs .nav-link:hover, .client .overview-area .nav-tabs .nav-link.active {
  background: #ffffff;
  color: #415094;
}

/* line 440, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .overview-area .tab-content h3 {
  color: #ffffff;
}

/* line 443, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .overview-area .tab-content p {
  margin-bottom: 30px;
}

/* line 445, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .overview-area .tab-content p:last-child {
  margin-bottom: 0px;
}

/* line 458, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .mapBox {
  height: 700px;
}

@media (max-width: 991px) {
  /* line 461, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
  .client .contact_info {
    margin-top: 50px;
  }
}

/* line 465, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .contact_info .info_item {
  position: relative;
  padding-left: 45px;
  margin-bottom: 20px;
}

/* line 469, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .contact_info .info_item i {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 20px;
  line-height: 24px;
  color: #ffffff;
  font-weight: 600;
}

/* line 478, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .contact_info .info_item h6 {
  font-size: 16px;
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 0px;
}

/* line 483, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .contact_info .info_item h6 a {
  color: #ffffff;
}

/* line 487, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client .contact_info .info_item p {
  margin-bottom: 0px;
}

/* line 498, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client.light .notice-item h4,
.client.light .academic-item .academic-text h4 a,
.client.light .info-area .info-content h2,
.client.light .department-area h3,
.client.light .overview-area .nav-tabs .nav-link,
.client.light .overview-area .tab-content h3,
.client.light .news-details-area h1,
.client.light .news-details-area .meta .date span,
.client.light .news-details-area h3,
.client.light .contact_info .info_item i,
.client.light .contact_info .info_item h6,
.client.light .contact_info .info_item h6 a, .client.color .notice-item h4,
.client.color .academic-item .academic-text h4 a,
.client.color .info-area .info-content h2,
.client.color .department-area h3,
.client.color .overview-area .nav-tabs .nav-link,
.client.color .overview-area .tab-content h3,
.client.color .news-details-area h1,
.client.color .news-details-area .meta .date span,
.client.color .news-details-area h3,
.client.color .contact_info .info_item i,
.client.color .contact_info .info_item h6,
.client.color .contact_info .info_item h6 a {
  color: #415094;
}

/* line 512, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client.light .fact-area .white-box.single-summery, .client.color .fact-area .white-box.single-summery {
  box-shadow: 0px 10px 15px rgba(236, 208, 244, 0.3);
}

/* line 515, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client.light .info-area .info-content, .client.color .info-area .info-content {
  background: rgba(65, 80, 148, 0.05);
}

/* line 518, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_home.scss */
.client.light .overview-area .nav-tabs .nav-link:hover,
.client.light .overview-area .nav-tabs .nav-link.active, .client.color .overview-area .nav-tabs .nav-link:hover,
.client.color .overview-area .nav-tabs .nav-link.active {
  color: #ffffff;
}

/*---------------------------------------------------- */
/* Start Footer Area css
============================================================================================ */
/* line 6, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client .footer_area .f_widget .f_title {
  margin-bottom: 40px;
}

@media (max-width: 991px) {
  /* line 6, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
  .client .footer_area .f_widget .f_title {
    margin-bottom: 20px;
  }
}

/* line 11, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client .footer_area .f_widget .f_title h4 {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 0px;
}

/* line 18, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client .footer_area .f_widget ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: 50px;
}

/* line 22, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client .footer_area .f_widget ul li {
  display: block;
  margin-bottom: 10px;
  cursor: pointer;
}

/* line 26, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client .footer_area .f_widget ul li:hover {
  color: #415094;
}

/* line 34, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client .single-footer-widget {
  padding: 30px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* line 38, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client .single-footer-widget .copy_right_text p {
  margin: 0;
  color: #ffffff;
  margin-left: -15px;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

@media (max-width: 767px) {
  /* line 38, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
  .client .single-footer-widget .copy_right_text p {
    text-align: center;
    padding: 0px 15px;
  }
}

/* line 47, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client .single-footer-widget .copy_right_text p a {
  color: #415094;
}

/* line 52, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client .single-footer-widget .social_widget {
  text-align: right;
  position: relative;
  margin-right: -15px;
}

@media (max-width: 767px) {
  /* line 52, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
  .client .single-footer-widget .social_widget {
    text-align: center;
    margin-top: 20px;
  }
}

/* line 60, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client .single-footer-widget .social_widget a {
  color: #ffffff;
  margin-left: 10px;
  display: inline-block;
  text-align: center;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

/* line 66, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client .single-footer-widget .social_widget a:hover {
  color: #415094;
}

/* line 74, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client.light .single-footer-widget .copy_right_text p,
.client.light .single-footer-widget .social_widget a, .client.color .single-footer-widget .copy_right_text p,
.client.color .single-footer-widget .social_widget a {
  color: #415094;
}

/* line 79, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client.light .single-footer-widget .social_widget a:hover, .client.color .single-footer-widget .social_widget a:hover {
  color: #7c32ff;
}

/* line 83, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client.light .single-footer-widget, .client.color .single-footer-widget {
  border-top: 1px solid rgba(65, 80, 148, 0.1);
}

/* line 88, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/client/_footer.scss */
.client.color .single-footer-widget .copy_right_text p a {
  color: #7c32ff;
}

/* End Footer Area css
============================================================================================ */
/*---------------------------------------------------- */
/*--------------------------register area css-------------------------- */
/* line 1, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.reg_bg {
  background: url("../img/in_registration.png") no-repeat !important;
  background-size: cover !important;
  background-attachment: fixed !important;
  background-position: top !important;
}

/* line 8, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area {
  padding: 60px 50px;
  background-color: #f7f7ff;
  box-shadow: none;
}

@media (max-width: 576px) {
  /* line 8, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .single_registration_area {
    padding: 30px 25px;
  }
}

/* line 15, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area h5 {
  color: #415094;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 500;
  margin-bottom: 41px;
  margin-top: 0;
  line-height: 22px;
  letter-spacing: 1px;
}

@media (max-width: 576px) {
  /* line 15, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .single_registration_area h5 {
    line-height: 18px;
    margin-bottom: 25px;
  }
}

/* line 29, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area .form-group .form-control {
  color: #828bb2;
  border: 0px;
  border-bottom: 1px solid #cec6e0;
  border-radius: 0px;
  background: transparent !important;
  padding: 0px 20px 20px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 1px;
  margin: 5px 0;
}

@media (max-width: 576px) {
  /* line 29, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .single_registration_area .form-group .form-control {
    padding: 0px 10px 10px;
  }
}

/* line 44, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area .form-control {
  height: auto;
}

/* line 47, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area .form-group {
  text-transform: capitalize;
  font-size: 12px;
  color: #828bb2;
}

/* line 52, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area .nice-select.niceSelect {
  padding: 0 20px 13px;
  line-height: 31px;
  margin-bottom: 1rem;
}

@media (max-width: 576px) {
  /* line 52, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .single_registration_area .nice-select.niceSelect {
    padding: 0 10px 10px;
  }
}

/* line 60, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area .nice-select:after {
  margin-top: -17px;
  right: 24px;
}

@media (max-width: 576px) {
  /* line 60, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .single_registration_area .nice-select:after {
    right: 16px;
  }
}

/* line 68, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area .nice-select.open:after {
  margin-top: 10px;
  right: 21px;
}

/* line 72, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area .nice-select.bb .current {
  font-weight: 400;
}

/* line 76, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area .input_box_tittle h4 {
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 400;
  color: #828bb2;
  margin-bottom: 12px;
  text-align: left;
}

/* line 84, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area .input_box_tittle label {
  font-size: 14px;
  font-weight: 400;
}

/* line 88, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area .input_box_tittle .form-group {
  margin: 17px 0 30px;
}

/* line 92, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area .billing_info {
  justify-content: space-between;
}

/* line 94, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_registration_area .billing_info .form-group {
  width: 48%;
}

/* line 99, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.registration_area_logo {
  padding: 40px 0 100px;
  text-align: center;
}

@media (max-width: 576px) {
  /* line 99, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .registration_area_logo {
    padding: 40px 0 40px;
  }
  /* line 104, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .registration_area_logo img {
    max-width: 100px;
  }
}

@media (min-width: 576px) and (max-width: 768px) {
  /* line 99, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .registration_area_logo {
    padding: 40px 0 40px;
  }
  /* line 110, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .registration_area_logo img {
    max-width: 120px;
  }
}

/* line 115, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.registration_footer {
  padding-top: 50px;
}

/* line 118, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_additional_services {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 10px;
}

/* line 124, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_additional_services .single_additional_text {
  text-align: left;
  padding: 30px 25px;
  background-color: #e2def0;
}

@media (max-width: 768px) {
  /* line 124, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .single_additional_services .single_additional_text {
    padding: 50px 15px 20px;
  }
}

/* line 131, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_additional_services .single_additional_text h5 {
  color: #415094;
  text-transform: capitalize;
  font-size: 14px;
  margin: 0 0 12px;
  letter-spacing: 0;
}

@media (min-width: 991px) {
  /* line 131, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .single_additional_services .single_additional_text h5 {
    max-width: 90%;
  }
}

/* line 141, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_additional_services .single_additional_text p {
  margin-bottom: 0;
  font-size: 12px;
  color: #828bb2;
}

/* line 147, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_additional_services span {
  background-color: #ff6d00;
  color: #fff;
  padding: 8px 23px;
  top: 0;
  right: 0;
  position: absolute;
}

/* line 155, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_additional_services label {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

/* line 164, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.order_details_iner .single_order_details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e2dff0;
  padding: 13px 0;
}

/* line 170, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.order_details_iner .single_order_details input {
  background-color: transparent;
  border: 0px solid transparent;
  text-align: right;
}

/* line 174, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.order_details_iner .single_order_details input:focus {
  outline: -webkit-focus-ring-color auto 0;
}

/* line 178, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.order_details_iner .single_order_details p, .order_details_iner .single_order_details input {
  font-size: 13px;
  font-weight: 300;
  color: #828bb2;
  margin-bottom: 0px;
}

@media (max-width: 576px) {
  /* line 164, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .order_details_iner .single_order_details {
    text-align: left;
  }
}

/* line 188, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.order_details_iner .cupon_code {
  display: flex;
  flex-wamp: wamp;
  justify-content: space-between;
}

@media (max-width: 576px) {
  /* line 188, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .order_details_iner .cupon_code {
    display: block;
  }
}

/* line 195, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.order_details_iner .cupon_code .single_cupon_code {
  flex: 41% 0 0;
}

/* line 201, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.cupon_code_iner h4 {
  font-size: 14px;
  float: left;
  text-align: left;
  text-transform: uppercase;
  margin: 33px 0 24px;
}

/* line 208, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.cupon_code_iner input.form-control {
  background-color: transparent;
  border: 0px transparent;
  border-bottom: 1px solid #cec7e1;
  border-radius: 0;
  text-transform: uppercase;
  color: #828bb2;
  font-size: 12px;
  padding: 2px 0 20px;
}

/* line 218, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.cupon_code_iner ::placeholder {
  color: #828bb2;
}

/* line 221, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.cupon_code_iner .input-group-append {
  margin-left: 20px;
}

/* line 224, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.cupon_code_iner a {
  border-radius: 5px;
  background-image: -moz-linear-gradient(0deg, #7c32ff 0%, #a235ec 70%, #c738d8 100%);
  background-image: -webkit-linear-gradient(0deg, #7c32ff 0%, #a235ec 70%, #c738d8 100%);
  background-image: -ms-linear-gradient(0deg, #7c32ff 0%, #a235ec 70%, #c738d8 100%);
  box-shadow: 0px 10px 20px 0px rgba(108, 39, 255, 0.3);
  padding: 10px 25px;
  color: #fff;
  text-transform: uppercase;
  font-size: 12px;
  border-radius: 6px !important;
  display: inline-block;
}

/* line 239, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.privacy_police p {
  text-align: left;
  margin-bottom: 0;
  font-size: 12px;
  color: #828bb2;
  line-height: 22px;
  margin-bottom: 15px;
}

/* line 250, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.privacy_police .single_privacy_police:last-child p {
  margin-bottom: 0;
}

/* line 255, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.privacy_police .common-radio:empty ~ label:before {
  top: 3px;
}

/* line 258, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.privacy_police .common-radio:checked ~ label:after {
  top: 0px;
}

/* line 264, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.login_button .primary-btn {
  padding: 10px 43px;
}

@media (max-width: 768px) {
  /* line 264, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
  .login_button .primary-btn {
    padding: 5px 25px;
  }
}

/* line 272, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.registration_footer p {
  font-size: 14px;
  color: #828bb2;
}

/* line 277, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.registration_footer span {
  color: #fff;
}

/* line 280, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.registration_footer a {
  color: #fff;
}

/* line 284, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.aingle_additional_img {
  width: 150px;
  height: 130px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #cec7e1;
}

/* line 291, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.aingle_additional_img img {
  width: 150px;
  height: 130px;
}

/* line 296, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_additional_text {
  cursor: pointer;
}

/* line 300, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_additional_services .active_pack {
  background-color: #2b0568;
}

/* line 302, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_additional_services .active_pack h5 {
  color: #fff;
}

/* line 305, /Applications/MAMP/htdocs/infixsass/assets/templates/wajeha/publicUserBackend/scss/admin/_register.scss */
.single_additional_services .active_pack p {
  color: #828bb2;
}

/*---------------------------------------------------- */

/*# sourceMappingURL=../css/style.map */
<?php

namespace Modules\Education\Http\Controllers;



use Illuminate\Http\Response;
use App\Http\Controllers\Controller;



class IjazasanadHalaqahMonthYearController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function getMonthYears($classId)
    {


        $dates = \App\StudentIjazasanadMemorizationReport::where('class_id',$classId)
            ->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year')
            ->groupBy('year', 'month')
            ->orderByDesc('year')
            ->orderByRaw("FIELD(month, 'December', 'November', 'October', 'September', 'August', 'July', 'June', 'May', 'April', 'March', 'February', 'January')")
            ->get();




        return response()->json($dates);
    }
}

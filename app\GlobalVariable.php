<?php 
namespace App;
use App\RolePermission;

use Illuminate\Support\Facades\Auth;
use Modules\RolePermission\Entities\PermissionAssign;
class GlobalVariable{

//	public $Names = array('<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>','<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>' );

	public static function GlobarModuleLinks(){
        try {
            $module_links = [];


//            $permissions = PermissionAssign::where('role_id', Auth::user()->roles()->id)->get();
            $permissions = PermissionAssign::whereIn('role_id', Auth::user()->roles()->get(['id'])->pluck('id'))->get();

//            dd($permissions);


            foreach ($permissions as $permission) {
                $module_links[] = $permission->module_id;
            }
            return $module_links;
        } catch (\Exception $e) {
            dd($e->getMessage());
            $data=[];
            return $data;
        }
	}
}

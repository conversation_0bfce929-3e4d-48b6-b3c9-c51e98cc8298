!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):a(jQuery)}(function(a){function b(a,b){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!b||"object"!=typeof b&&"function"!=typeof b?a:b}function c(a,b){if("function"!=typeof b&&null!==b)throw new TypeError("Super expression must either be null or a function, not "+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}function d(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function e(a,b){return void 0!==a&&null!==a?b(a):void 0}function f(a,b,c){return void 0!==a&&null!==a&&"function"==typeof a[b]?c(a,b):void 0}var g={exports:{}},h=function(){function a(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),i=function(){function a(){d(this,a)}return h(a,[{key:"on",value:function(a,b){return this._callbacks=this._callbacks||{},this._callbacks[a]||(this._callbacks[a]=[]),this._callbacks[a].push(b),this}},{key:"emit",value:function(a){this._callbacks=this._callbacks||{};var b=this._callbacks[a];if(b){for(var c=arguments.length,d=Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];for(var f=b,g=Array.isArray(f),h=0,f=g?f:f[Symbol.iterator]();;){var i;if(g){if(h>=f.length)break;i=f[h++]}else{if(h=f.next(),h.done)break;i=h.value}i.apply(this,d)}}return this}},{key:"off",value:function(a,b){if(!this._callbacks||0===arguments.length)return this._callbacks={},this;var c=this._callbacks[a];if(!c)return this;if(1===arguments.length)return delete this._callbacks[a],this;for(var d=0;d<c.length;d++){if(c[d]===b){c.splice(d,1);break}}return this}}]),a}(),j=function(a){function g(a,c){d(this,g);var e=b(this,(g.__proto__||Object.getPrototypeOf(g)).call(this)),f=void 0,h=void 0;if(e.element=a,e.version=g.version,e.defaultOptions.previewTemplate=e.defaultOptions.previewTemplate.replace(/\n*/g,""),e.clickableElements=[],e.listeners=[],e.files=[],"string"==typeof e.element&&(e.element=document.querySelector(e.element)),!e.element||null==e.element.nodeType)throw new Error("Invalid dropzone element.");if(e.element.dropzone)throw new Error("Dropzone already attached.");g.instances.push(e),e.element.dropzone=e;var i=null!=(h=g.optionsForElement(e.element))?h:{};if(e.options=g.extend({},e.defaultOptions,i,null!=c?c:{}),e.options.forceFallback||!g.isBrowserSupported()){var j;return j=e.options.fallback.call(e),b(e,j)}if(null==e.options.url&&(e.options.url=e.element.getAttribute("action")),!e.options.url)throw new Error("No URL provided.");if(e.options.acceptedFiles&&e.options.acceptedMimeTypes)throw new Error("You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.");if(e.options.uploadMultiple&&e.options.chunking)throw new Error("You cannot set both: uploadMultiple and chunking.");return e.options.acceptedMimeTypes&&(e.options.acceptedFiles=e.options.acceptedMimeTypes,delete e.options.acceptedMimeTypes),null!=e.options.renameFilename&&(e.options.renameFile=function(a){return e.options.renameFilename.call(e,a.name,a)}),e.options.method=e.options.method.toUpperCase(),(f=e.getExistingFallback())&&f.parentNode&&f.parentNode.removeChild(f),!1!==e.options.previewsContainer&&(e.options.previewsContainer?e.previewsContainer=g.getElement(e.options.previewsContainer,"previewsContainer"):e.previewsContainer=e.element),e.options.clickable&&(!0===e.options.clickable?e.clickableElements=[e.element]:e.clickableElements=g.getElements(e.options.clickable,"clickable")),e.init(),e}return c(g,a),h(g,null,[{key:"initClass",value:function(){this.prototype.Emitter=i,this.prototype.events=["drop","dragstart","dragend","dragenter","dragover","dragleave","addedfile","addedfiles","removedfile","thumbnail","error","errormultiple","processing","processingmultiple","uploadprogress","totaluploadprogress","sending","sendingmultiple","success","successmultiple","canceled","canceledmultiple","complete","completemultiple","reset","maxfilesexceeded","maxfilesreached","queuecomplete"],this.prototype.defaultOptions={url:null,method:"post",withCredentials:!1,timeout:3e4,parallelUploads:2,uploadMultiple:!1,chunking:!1,forceChunking:!1,chunkSize:2e6,parallelChunkUploads:!1,retryChunks:!1,retryChunksLimit:3,maxFilesize:256,paramName:"file",createImageThumbnails:!0,maxThumbnailFilesize:10,thumbnailWidth:120,thumbnailHeight:120,thumbnailMethod:"crop",resizeWidth:null,resizeHeight:null,resizeMimeType:null,resizeQuality:.8,resizeMethod:"contain",filesizeBase:1e3,maxFiles:null,headers:null,clickable:!0,ignoreHiddenFiles:!0,acceptedFiles:null,acceptedMimeTypes:null,autoProcessQueue:!0,autoQueue:!0,addRemoveLinks:!1,previewsContainer:null,hiddenInputContainer:"body",capture:null,renameFilename:null,renameFile:null,forceFallback:!1,dictDefaultMessage:"Drop files here to upload",dictFallbackMessage:"Your browser does not support drag'n'drop file uploads.",dictFallbackText:"Please use the fallback form below to upload your files like in the olden days.",dictFileTooBig:"File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.",dictInvalidFileType:"You can't upload files of this type.",dictResponseError:"Server responded with {{statusCode}} code.",dictCancelUpload:"Cancel upload",dictUploadCanceled:"Upload canceled.",dictCancelUploadConfirmation:"Are you sure you want to cancel this upload?",dictRemoveFile:"Remove file",dictRemoveFileConfirmation:null,dictMaxFilesExceeded:"You can not upload any more files.",dictFileSizeUnits:{tb:"TB",gb:"GB",mb:"MB",kb:"KB",b:"b"},init:function(){},params:function(a,b,c){if(c)return{dzuuid:c.file.upload.uuid,dzchunkindex:c.index,dztotalfilesize:c.file.size,dzchunksize:this.options.chunkSize,dztotalchunkcount:c.file.upload.totalChunkCount,dzchunkbyteoffset:c.index*this.options.chunkSize}},accept:function(a,b){return b()},chunksUploaded:function(a,b){b()},fallback:function(){var a=void 0;this.element.className=this.element.className+" dz-browser-not-supported";for(var b=this.element.getElementsByTagName("div"),c=Array.isArray(b),d=0,b=c?b:b[Symbol.iterator]();;){var e;if(c){if(d>=b.length)break;e=b[d++]}else{if(d=b.next(),d.done)break;e=d.value}var f=e;if(/(^| )dz-message($| )/.test(f.className)){a=f,f.className="dz-message";break}}a||(a=g.createElement('<div class="dz-message"><span></span></div>'),this.element.appendChild(a));var h=a.getElementsByTagName("span")[0];return h&&(null!=h.textContent?h.textContent=this.options.dictFallbackMessage:null!=h.innerText&&(h.innerText=this.options.dictFallbackMessage)),this.element.appendChild(this.getFallbackForm())},resize:function(a,b,c,d){var e={srcX:0,srcY:0,srcWidth:a.width,srcHeight:a.height},f=a.width/a.height;null==b&&null==c?(b=e.srcWidth,c=e.srcHeight):null==b?b=c*f:null==c&&(c=b/f),b=Math.min(b,e.srcWidth),c=Math.min(c,e.srcHeight);var g=b/c;if(e.srcWidth>b||e.srcHeight>c)if("crop"===d)f>g?(e.srcHeight=a.height,e.srcWidth=e.srcHeight*g):(e.srcWidth=a.width,e.srcHeight=e.srcWidth/g);else{if("contain"!==d)throw new Error("Unknown resizeMethod '"+d+"'");f>g?c=b/f:b=c*f}return e.srcX=(a.width-e.srcWidth)/2,e.srcY=(a.height-e.srcHeight)/2,e.trgWidth=b,e.trgHeight=c,e},transformFile:function(a,b){return(this.options.resizeWidth||this.options.resizeHeight)&&a.type.match(/image.*/)?this.resizeImage(a,this.options.resizeWidth,this.options.resizeHeight,this.options.resizeMethod,b):b(a)},previewTemplate:'<div class="dz-preview dz-file-preview">\n  <div class="dz-image"><img data-dz-thumbnail /></div>\n  <div class="dz-details">\n    <div class="dz-size"><span data-dz-size></span></div>\n    <div class="dz-filename"><span data-dz-name></span></div>\n  </div>\n  <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>\n  <div class="dz-error-message"><span data-dz-errormessage></span></div>\n  <div class="dz-success-mark">\n    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">\n      <title>Check</title>\n      <defs></defs>\n      <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage">\n        <path d="M23.5,31.8431458 L17.5852419,25.9283877 C16.0248253,24.3679711 13.4910294,24.366835 11.9289322,25.9289322 C10.3700136,27.4878508 10.3665912,30.0234455 11.9283877,31.5852419 L20.4147581,40.0716123 C20.5133999,40.1702541 20.6159315,40.2626649 20.7218615,40.3488435 C22.2835669,41.8725651 24.794234,41.8626202 26.3461564,40.3106978 L43.3106978,23.3461564 C44.8771021,21.7797521 44.8758057,19.2483887 43.3137085,17.6862915 C41.7547899,16.1273729 39.2176035,16.1255422 37.6538436,17.6893022 L23.5,31.8431458 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z" id="Oval-2" stroke-opacity="0.198794158" stroke="#747474" fill-opacity="0.816519475" fill="#FFFFFF" sketch:type="MSShapeGroup"></path>\n      </g>\n    </svg>\n  </div>\n  <div class="dz-error-mark">\n    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">\n      <title>Error</title>\n      <defs></defs>\n      <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage">\n        <g id="Check-+-Oval-2" sketch:type="MSLayerGroup" stroke="#747474" stroke-opacity="0.198794158" fill="#FFFFFF" fill-opacity="0.816519475">\n          <path d="M32.6568542,29 L38.3106978,23.3461564 C39.8771021,21.7797521 39.8758057,19.2483887 38.3137085,17.6862915 C36.7547899,16.1273729 34.2176035,16.1255422 32.6538436,17.6893022 L27,23.3431458 L21.3461564,17.6893022 C19.7823965,16.1255422 17.2452101,16.1273729 15.6862915,17.6862915 C14.1241943,19.2483887 14.1228979,21.7797521 15.6893022,23.3461564 L21.3431458,29 L15.6893022,34.6538436 C14.1228979,36.2202479 14.1241943,38.7516113 15.6862915,40.3137085 C17.2452101,41.8726271 19.7823965,41.8744578 21.3461564,40.3106978 L27,34.6568542 L32.6538436,40.3106978 C34.2176035,41.8744578 36.7547899,41.8726271 38.3137085,40.3137085 C39.8758057,38.7516113 39.8771021,36.2202479 38.3106978,34.6538436 L32.6568542,29 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z" id="Oval-2" sketch:type="MSShapeGroup"></path>\n        </g>\n      </g>\n    </svg>\n  </div>\n</div>',drop:function(a){return this.element.classList.remove("dz-drag-hover")},dragstart:function(a){},dragend:function(a){return this.element.classList.remove("dz-drag-hover")},dragenter:function(a){return this.element.classList.add("dz-drag-hover")},dragover:function(a){return this.element.classList.add("dz-drag-hover")},dragleave:function(a){return this.element.classList.remove("dz-drag-hover")},paste:function(a){},reset:function(){return this.element.classList.remove("dz-started")},addedfile:function(a){var b=this;if(this.element===this.previewsContainer&&this.element.classList.add("dz-started"),this.previewsContainer){a.previewElement=g.createElement(this.options.previewTemplate.trim()),a.previewTemplate=a.previewElement,this.previewsContainer.appendChild(a.previewElement);for(var c=a.previewElement.querySelectorAll("[data-dz-name]"),d=Array.isArray(c),e=0,c=d?c:c[Symbol.iterator]();;){var f;if(d){if(e>=c.length)break;f=c[e++]}else{if(e=c.next(),e.done)break;f=e.value}var h=f;h.textContent=a.name}for(var i=a.previewElement.querySelectorAll("[data-dz-size]"),j=Array.isArray(i),k=0,i=j?i:i[Symbol.iterator]();;){if(j){if(k>=i.length)break;h=i[k++]}else{if(k=i.next(),k.done)break;h=k.value}h.innerHTML=this.filesize(a.size)}this.options.addRemoveLinks&&(a._removeLink=g.createElement('<a class="dz-remove" href="javascript:undefined;" data-dz-remove>'+this.options.dictRemoveFile+"</a>"),a.previewElement.appendChild(a._removeLink));for(var l=function(c){return c.preventDefault(),c.stopPropagation(),a.status===g.UPLOADING?g.confirm(b.options.dictCancelUploadConfirmation,function(){return b.removeFile(a)}):b.options.dictRemoveFileConfirmation?g.confirm(b.options.dictRemoveFileConfirmation,function(){return b.removeFile(a)}):b.removeFile(a)},m=a.previewElement.querySelectorAll("[data-dz-remove]"),n=Array.isArray(m),o=0,m=n?m:m[Symbol.iterator]();;){var p;if(n){if(o>=m.length)break;p=m[o++]}else{if(o=m.next(),o.done)break;p=o.value}p.addEventListener("click",l)}}},removedfile:function(a){return null!=a.previewElement&&null!=a.previewElement.parentNode&&a.previewElement.parentNode.removeChild(a.previewElement),this._updateMaxFilesReachedClass()},thumbnail:function(a,b){if(a.previewElement){a.previewElement.classList.remove("dz-file-preview");for(var c=a.previewElement.querySelectorAll("[data-dz-thumbnail]"),d=Array.isArray(c),e=0,c=d?c:c[Symbol.iterator]();;){var f;if(d){if(e>=c.length)break;f=c[e++]}else{if(e=c.next(),e.done)break;f=e.value}var g=f;g.alt=a.name,g.src=b}return setTimeout(function(){return a.previewElement.classList.add("dz-image-preview")},1)}},error:function(a,b){if(a.previewElement){a.previewElement.classList.add("dz-error"),"String"!=typeof b&&b.error&&(b=b.error);for(var c=a.previewElement.querySelectorAll("[data-dz-errormessage]"),d=Array.isArray(c),e=0,c=d?c:c[Symbol.iterator]();;){var f;if(d){if(e>=c.length)break;f=c[e++]}else{if(e=c.next(),e.done)break;f=e.value}f.textContent=b}}},errormultiple:function(){},processing:function(a){if(a.previewElement&&(a.previewElement.classList.add("dz-processing"),a._removeLink))return a._removeLink.textContent=this.options.dictCancelUpload},processingmultiple:function(){},uploadprogress:function(a,b,c){if(a.previewElement)for(var d=a.previewElement.querySelectorAll("[data-dz-uploadprogress]"),e=Array.isArray(d),f=0,d=e?d:d[Symbol.iterator]();;){var g;if(e){if(f>=d.length)break;g=d[f++]}else{if(f=d.next(),f.done)break;g=f.value}var h=g;"PROGRESS"===h.nodeName?h.value=b:h.style.width=b+"%"}},totaluploadprogress:function(){},sending:function(){},sendingmultiple:function(){},success:function(a){if(a.previewElement)return a.previewElement.classList.add("dz-success")},successmultiple:function(){},canceled:function(a){return this.emit("error",a,this.options.dictUploadCanceled)},canceledmultiple:function(){},complete:function(a){if(a._removeLink&&(a._removeLink.textContent=this.options.dictRemoveFile),a.previewElement)return a.previewElement.classList.add("dz-complete")},completemultiple:function(){},maxfilesexceeded:function(){},maxfilesreached:function(){},queuecomplete:function(){},addedfiles:function(){}},this.prototype._thumbnailQueue=[],this.prototype._processingThumbnail=!1}},{key:"extend",value:function(a){for(var b=arguments.length,c=Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];for(var e=c,f=Array.isArray(e),g=0,e=f?e:e[Symbol.iterator]();;){var h;if(f){if(g>=e.length)break;h=e[g++]}else{if(g=e.next(),g.done)break;h=g.value}var i=h;for(var j in i){var k=i[j];a[j]=k}}return a}}]),h(g,[{key:"getAcceptedFiles",value:function(){return this.files.filter(function(a){return a.accepted}).map(function(a){return a})}},{key:"getRejectedFiles",value:function(){return this.files.filter(function(a){return!a.accepted}).map(function(a){return a})}},{key:"getFilesWithStatus",value:function(a){return this.files.filter(function(b){return b.status===a}).map(function(a){return a})}},{key:"getQueuedFiles",value:function(){return this.getFilesWithStatus(g.QUEUED)}},{key:"getUploadingFiles",value:function(){return this.getFilesWithStatus(g.UPLOADING)}},{key:"getAddedFiles",value:function(){return this.getFilesWithStatus(g.ADDED)}},{key:"getActiveFiles",value:function(){return this.files.filter(function(a){return a.status===g.UPLOADING||a.status===g.QUEUED}).map(function(a){return a})}},{key:"init",value:function(){var a=this;if("form"===this.element.tagName&&this.element.setAttribute("enctype","multipart/form-data"),this.element.classList.contains("dropzone")&&!this.element.querySelector(".dz-message")&&this.element.appendChild(g.createElement('<div class="dz-default dz-message"><span>'+this.options.dictDefaultMessage+"</span></div>")),this.clickableElements.length){!function b(){return a.hiddenFileInput&&a.hiddenFileInput.parentNode.removeChild(a.hiddenFileInput),a.hiddenFileInput=document.createElement("input"),a.hiddenFileInput.setAttribute("type","file"),(null===a.options.maxFiles||a.options.maxFiles>1)&&a.hiddenFileInput.setAttribute("multiple","multiple"),a.hiddenFileInput.className="dz-hidden-input",null!==a.options.acceptedFiles&&a.hiddenFileInput.setAttribute("accept",a.options.acceptedFiles),null!==a.options.capture&&a.hiddenFileInput.setAttribute("capture",a.options.capture),a.hiddenFileInput.style.visibility="hidden",a.hiddenFileInput.style.position="absolute",a.hiddenFileInput.style.top="0",a.hiddenFileInput.style.left="0",a.hiddenFileInput.style.height="0",a.hiddenFileInput.style.width="0",document.querySelector(a.options.hiddenInputContainer).appendChild(a.hiddenFileInput),a.hiddenFileInput.addEventListener("change",function(){var c=a.hiddenFileInput.files;if(c.length)for(var d=c,e=Array.isArray(d),f=0,d=e?d:d[Symbol.iterator]();;){var g;if(e){if(f>=d.length)break;g=d[f++]}else{if(f=d.next(),f.done)break;g=f.value}var h=g;a.addFile(h)}return a.emit("addedfiles",c),b()})}()}this.URL=null!==window.URL?window.URL:window.webkitURL;for(var b=this.events,c=Array.isArray(b),d=0,b=c?b:b[Symbol.iterator]();;){var e;if(c){if(d>=b.length)break;e=b[d++]}else{if(d=b.next(),d.done)break;e=d.value}var f=e;this.on(f,this.options[f])}this.on("uploadprogress",function(){return a.updateTotalUploadProgress()}),this.on("removedfile",function(){return a.updateTotalUploadProgress()}),this.on("canceled",function(b){return a.emit("complete",b)}),this.on("complete",function(b){if(0===a.getAddedFiles().length&&0===a.getUploadingFiles().length&&0===a.getQueuedFiles().length)return setTimeout(function(){return a.emit("queuecomplete")},0)});var h=function(a){return a.stopPropagation(),a.preventDefault?a.preventDefault():a.returnValue=!1};return this.listeners=[{element:this.element,events:{dragstart:function(b){return a.emit("dragstart",b)},dragenter:function(b){return h(b),a.emit("dragenter",b)},dragover:function(b){var c=void 0;try{c=b.dataTransfer.effectAllowed}catch(a){}return b.dataTransfer.dropEffect="move"===c||"linkMove"===c?"move":"copy",h(b),a.emit("dragover",b)},dragleave:function(b){return a.emit("dragleave",b)},drop:function(b){return h(b),a.drop(b)},dragend:function(b){return a.emit("dragend",b)}}}],this.clickableElements.forEach(function(b){return a.listeners.push({element:b,events:{click:function(c){return(b!==a.element||c.target===a.element||g.elementInside(c.target,a.element.querySelector(".dz-message")))&&a.hiddenFileInput.click(),!0}}})}),this.enable(),this.options.init.call(this)}},{key:"destroy",value:function(){return this.disable(),this.removeAllFiles(!0),(null!=this.hiddenFileInput?this.hiddenFileInput.parentNode:void 0)&&(this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput),this.hiddenFileInput=null),delete this.element.dropzone,g.instances.splice(g.instances.indexOf(this),1)}},{key:"updateTotalUploadProgress",value:function(){var a=void 0,b=0,c=0;if(this.getActiveFiles().length){for(var d=this.getActiveFiles(),e=Array.isArray(d),f=0,d=e?d:d[Symbol.iterator]();;){var g;if(e){if(f>=d.length)break;g=d[f++]}else{if(f=d.next(),f.done)break;g=f.value}var h=g;b+=h.upload.bytesSent,c+=h.upload.total}a=100*b/c}else a=100;return this.emit("totaluploadprogress",a,c,b)}},{key:"_getParamName",value:function(a){return"function"==typeof this.options.paramName?this.options.paramName(a):this.options.paramName+(this.options.uploadMultiple?"["+a+"]":"")}},{key:"_renameFile",value:function(a){return"function"!=typeof this.options.renameFile?a.name:this.options.renameFile(a)}},{key:"getFallbackForm",value:function(){var a=void 0,b=void 0;if(a=this.getExistingFallback())return a;var c='<div class="dz-fallback">';this.options.dictFallbackText&&(c+="<p>"+this.options.dictFallbackText+"</p>"),c+='<input type="file" name="'+this._getParamName(0)+'" '+(this.options.uploadMultiple?'multiple="multiple"':void 0)+' /><input type="submit" value="Upload!"></div>';var d=g.createElement(c);return"FORM"!==this.element.tagName?(b=g.createElement('<form action="'+this.options.url+'" enctype="multipart/form-data" method="'+this.options.method+'"></form>'),b.appendChild(d)):(this.element.setAttribute("enctype","multipart/form-data"),this.element.setAttribute("method",this.options.method)),null!=b?b:d}},{key:"getExistingFallback",value:function(){for(var a=["div","form"],b=0;b<a.length;b++){var c,d=a[b];if(c=function(a){for(var b=a,c=Array.isArray(b),d=0,b=c?b:b[Symbol.iterator]();;){var e;if(c){if(d>=b.length)break;e=b[d++]}else{if(d=b.next(),d.done)break;e=d.value}var f=e;if(/(^| )fallback($| )/.test(f.className))return f}}(this.element.getElementsByTagName(d)))return c}}},{key:"setupEventListeners",value:function(){return this.listeners.map(function(a){return function(){var b=[];for(var c in a.events){var d=a.events[c];b.push(a.element.addEventListener(c,d,!1))}return b}()})}},{key:"removeEventListeners",value:function(){return this.listeners.map(function(a){return function(){var b=[];for(var c in a.events){var d=a.events[c];b.push(a.element.removeEventListener(c,d,!1))}return b}()})}},{key:"disable",value:function(){var a=this;return this.clickableElements.forEach(function(a){return a.classList.remove("dz-clickable")}),this.removeEventListeners(),this.disabled=!0,this.files.map(function(b){return a.cancelUpload(b)})}},{key:"enable",value:function(){return delete this.disabled,this.clickableElements.forEach(function(a){return a.classList.add("dz-clickable")}),this.setupEventListeners()}},{key:"filesize",value:function(a){var b=0,c="b";if(a>0){for(var d=["tb","gb","mb","kb","b"],e=0;e<d.length;e++){var f=d[e];if(a>=Math.pow(this.options.filesizeBase,4-e)/10){b=a/Math.pow(this.options.filesizeBase,4-e),c=f;break}}b=Math.round(10*b)/10}return"<strong>"+b+"</strong> "+this.options.dictFileSizeUnits[c]}},{key:"_updateMaxFilesReachedClass",value:function(){return null!=this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(this.getAcceptedFiles().length===this.options.maxFiles&&this.emit("maxfilesreached",this.files),this.element.classList.add("dz-max-files-reached")):this.element.classList.remove("dz-max-files-reached")}},{key:"drop",value:function(a){if(a.dataTransfer){this.emit("drop",a);for(var b=[],c=0;c<a.dataTransfer.files.length;c++)b[c]=a.dataTransfer.files[c];if(this.emit("addedfiles",b),b.length){var d=a.dataTransfer.items;d&&d.length&&null!=d[0].webkitGetAsEntry?this._addFilesFromItems(d):this.handleFiles(b)}}}},{key:"paste",value:function(a){if(null!=e(null!=a?a.clipboardData:void 0,function(a){return a.items})){this.emit("paste",a);var b=a.clipboardData.items;return b.length?this._addFilesFromItems(b):void 0}}},{key:"handleFiles",value:function(a){for(var b=a,c=Array.isArray(b),d=0,b=c?b:b[Symbol.iterator]();;){var e;if(c){if(d>=b.length)break;e=b[d++]}else{if(d=b.next(),d.done)break;e=d.value}var f=e;this.addFile(f)}}},{key:"_addFilesFromItems",value:function(a){var b=this;return function(){for(var c=[],d=a,e=Array.isArray(d),f=0,d=e?d:d[Symbol.iterator]();;){var g;if(e){if(f>=d.length)break;g=d[f++]}else{if(f=d.next(),f.done)break;g=f.value}var h,i=g;null!=i.webkitGetAsEntry&&(h=i.webkitGetAsEntry())?h.isFile?c.push(b.addFile(i.getAsFile())):h.isDirectory?c.push(b._addFilesFromDirectory(h,h.name)):c.push(void 0):null!=i.getAsFile&&(null==i.kind||"file"===i.kind)?c.push(b.addFile(i.getAsFile())):c.push(void 0)}return c}()}},{key:"_addFilesFromDirectory",value:function(a,b){var c=this,d=a.createReader(),e=function(a){return f(console,"log",function(b){return b.log(a)})};return function a(){return d.readEntries(function(d){if(d.length>0){for(var e=d,f=Array.isArray(e),g=0,e=f?e:e[Symbol.iterator]();;){var h;if(f){if(g>=e.length)break;h=e[g++]}else{if(g=e.next(),g.done)break;h=g.value}var i=h;i.isFile?i.file(function(a){if(!c.options.ignoreHiddenFiles||"."!==a.name.substring(0,1))return a.fullPath=b+"/"+a.name,c.addFile(a)}):i.isDirectory&&c._addFilesFromDirectory(i,b+"/"+i.name)}a()}return null},e)}()}},{key:"accept",value:function(a,b){return this.options.maxFilesize&&a.size>1024*this.options.maxFilesize*1024?b(this.options.dictFileTooBig.replace("{{filesize}}",Math.round(a.size/1024/10.24)/100).replace("{{maxFilesize}}",this.options.maxFilesize)):g.isValidFile(a,this.options.acceptedFiles)?null!=this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(b(this.options.dictMaxFilesExceeded.replace("{{maxFiles}}",this.options.maxFiles)),this.emit("maxfilesexceeded",a)):this.options.accept.call(this,a,b):b(this.options.dictInvalidFileType)}},{key:"addFile",value:function(a){var b=this;return a.upload={uuid:g.uuidv4(),progress:0,total:a.size,bytesSent:0,filename:this._renameFile(a),chunked:this.options.chunking&&(this.options.forceChunking||a.size>this.options.chunkSize),totalChunkCount:Math.ceil(a.size/this.options.chunkSize)},this.files.push(a),a.status=g.ADDED,this.emit("addedfile",a),this._enqueueThumbnail(a),this.accept(a,function(c){return c?(a.accepted=!1,b._errorProcessing([a],c)):(a.accepted=!0,b.options.autoQueue&&b.enqueueFile(a)),b._updateMaxFilesReachedClass()})}},{key:"enqueueFiles",value:function(a){for(var b=a,c=Array.isArray(b),d=0,b=c?b:b[Symbol.iterator]();;){var e;if(c){if(d>=b.length)break;e=b[d++]}else{if(d=b.next(),d.done)break;e=d.value}var f=e;this.enqueueFile(f)}return null}},{key:"enqueueFile",value:function(a){var b=this;if(a.status!==g.ADDED||!0!==a.accepted)throw new Error("This file can't be queued because it has already been processed or was rejected.");if(a.status=g.QUEUED,this.options.autoProcessQueue)return setTimeout(function(){return b.processQueue()},0)}},{key:"_enqueueThumbnail",value:function(a){var b=this;if(this.options.createImageThumbnails&&a.type.match(/image.*/)&&a.size<=1024*this.options.maxThumbnailFilesize*1024)return this._thumbnailQueue.push(a),setTimeout(function(){return b._processThumbnailQueue()},0)}},{key:"_processThumbnailQueue",value:function(){var a=this;if(!this._processingThumbnail&&0!==this._thumbnailQueue.length){this._processingThumbnail=!0;var b=this._thumbnailQueue.shift();return this.createThumbnail(b,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.thumbnailMethod,!0,function(c){return a.emit("thumbnail",b,c),a._processingThumbnail=!1,a._processThumbnailQueue()})}}},{key:"removeFile",value:function(a){if(a.status===g.UPLOADING&&this.cancelUpload(a),this.files=k(this.files,a),this.emit("removedfile",a),0===this.files.length)return this.emit("reset")}},{key:"removeAllFiles",value:function(a){null==a&&(a=!1);for(var b=this.files.slice(),c=Array.isArray(b),d=0,b=c?b:b[Symbol.iterator]();;){var e;if(c){if(d>=b.length)break;e=b[d++]}else{if(d=b.next(),d.done)break;e=d.value}var f=e;(f.status!==g.UPLOADING||a)&&this.removeFile(f)}return null}},{key:"resizeImage",value:function(a,b,c,d,e){var f=this;return this.createThumbnail(a,b,c,d,!1,function(b,c){if(null==c)return e(a);var d=f.options.resizeMimeType;null==d&&(d=a.type);var h=c.toDataURL(d,f.options.resizeQuality);return"image/jpeg"!==d&&"image/jpg"!==d||(h=o.restore(a.dataURL,h)),e(g.dataURItoBlob(h))})}},{key:"createThumbnail",value:function(a,b,c,d,e,f){var g=this,h=new FileReader;return h.onload=function(){return a.dataURL=h.result,"image/svg+xml"===a.type?void(null!=f&&f(h.result)):g.createThumbnailFromUrl(a,b,c,d,e,f)},h.readAsDataURL(a)}},{key:"createThumbnailFromUrl",value:function(a,b,c,d,e,f,g){var h=this,i=document.createElement("img");return g&&(i.crossOrigin=g),i.onload=function(){var g=function(a){return a(1)};return"undefined"!=typeof EXIF&&null!==EXIF&&e&&(g=function(a){return EXIF.getData(i,function(){return a(EXIF.getTag(this,"Orientation"))})}),g(function(e){a.width=i.width,a.height=i.height;var g=h.options.resize.call(h,a,b,c,d),j=document.createElement("canvas"),k=j.getContext("2d");switch(j.width=g.trgWidth,j.height=g.trgHeight,e>4&&(j.width=g.trgHeight,j.height=g.trgWidth),e){case 2:k.translate(j.width,0),k.scale(-1,1);break;case 3:k.translate(j.width,j.height),k.rotate(Math.PI);break;case 4:k.translate(0,j.height),k.scale(1,-1);break;case 5:k.rotate(.5*Math.PI),k.scale(1,-1);break;case 6:k.rotate(.5*Math.PI),k.translate(0,-j.height);break;case 7:k.rotate(.5*Math.PI),k.translate(j.width,-j.height),k.scale(-1,1);break;case 8:k.rotate(-.5*Math.PI),k.translate(-j.width,0)}n(k,i,null!=g.srcX?g.srcX:0,null!=g.srcY?g.srcY:0,g.srcWidth,g.srcHeight,null!=g.trgX?g.trgX:0,null!=g.trgY?g.trgY:0,g.trgWidth,g.trgHeight);var l=j.toDataURL("image/png");if(null!=f)return f(l,j)})},null!=f&&(i.onerror=f),i.src=a.dataURL}},{key:"processQueue",value:function(){var a=this.options.parallelUploads,b=this.getUploadingFiles().length,c=b;if(!(b>=a)){var d=this.getQueuedFiles();if(d.length>0){if(this.options.uploadMultiple)return this.processFiles(d.slice(0,a-b));for(;c<a;){if(!d.length)return;this.processFile(d.shift()),c++}}}}},{key:"processFile",value:function(a){return this.processFiles([a])}},{key:"processFiles",value:function(a){for(var b=a,c=Array.isArray(b),d=0,b=c?b:b[Symbol.iterator]();;){var e;if(c){if(d>=b.length)break;e=b[d++]}else{if(d=b.next(),d.done)break;e=d.value}var f=e;f.processing=!0,f.status=g.UPLOADING,this.emit("processing",f)}return this.options.uploadMultiple&&this.emit("processingmultiple",a),this.uploadFiles(a)}},{key:"_getFilesWithXhr",value:function(a){return this.files.filter(function(b){return b.xhr===a}).map(function(a){return a})}},{key:"cancelUpload",value:function(a){if(a.status===g.UPLOADING){for(var b=this._getFilesWithXhr(a.xhr),c=b,d=Array.isArray(c),e=0,c=d?c:c[Symbol.iterator]();;){var f;if(d){if(e>=c.length)break;f=c[e++]}else{if(e=c.next(),e.done)break;f=e.value}f.status=g.CANCELED}void 0!==a.xhr&&a.xhr.abort();for(var h=b,i=Array.isArray(h),j=0,h=i?h:h[Symbol.iterator]();;){var k;if(i){if(j>=h.length)break;k=h[j++]}else{if(j=h.next(),j.done)break;k=j.value}var l=k;this.emit("canceled",l)}this.options.uploadMultiple&&this.emit("canceledmultiple",b)}else a.status!==g.ADDED&&a.status!==g.QUEUED||(a.status=g.CANCELED,this.emit("canceled",a),this.options.uploadMultiple&&this.emit("canceledmultiple",[a]));if(this.options.autoProcessQueue)return this.processQueue()}},{key:"resolveOption",value:function(a){if("function"==typeof a){for(var b=arguments.length,c=Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];return a.apply(this,c)}return a}},{key:"uploadFile",value:function(a){return this.uploadFiles([a])}},{key:"uploadFiles",value:function(a){var b=this;this._transformFiles(a,function(c){if(a[0].upload.chunked){var d=a[0],e=c[0],f=0;d.upload.chunks=[];var h=function(){for(var c=0;void 0!==d.upload.chunks[c];)c++;if(!(c>=d.upload.totalChunkCount)){f++;var h=c*b.options.chunkSize,i=Math.min(h+b.options.chunkSize,d.size),j={name:b._getParamName(0),data:e.webkitSlice?e.webkitSlice(h,i):e.slice(h,i),filename:d.upload.filename,chunkIndex:c};d.upload.chunks[c]={file:d,index:c,dataBlock:j,status:g.UPLOADING,progress:0,retries:0},b._uploadData(a,[j])}};if(d.upload.finishedChunkUpload=function(c){var e=!0;c.status=g.SUCCESS,c.dataBlock=null;for(var f=0;f<d.upload.totalChunkCount;f++){if(void 0===d.upload.chunks[f])return h();d.upload.chunks[f].status!==g.SUCCESS&&(e=!1)}e&&b.options.chunksUploaded(d,function(){b._finished(a,"",null)})},b.options.parallelChunkUploads)for(var i=0;i<d.upload.totalChunkCount;i++)h();else h()}else{for(var j=[],k=0;k<a.length;k++)j[k]={
name:b._getParamName(k),data:c[k],filename:a[k].upload.filename};b._uploadData(a,j)}})}},{key:"_getChunk",value:function(a,b){for(var c=0;c<a.upload.totalChunkCount;c++)if(void 0!==a.upload.chunks[c]&&a.upload.chunks[c].xhr===b)return a.upload.chunks[c]}},{key:"_uploadData",value:function(a,b){for(var c=this,d=new XMLHttpRequest,e=a,f=Array.isArray(e),h=0,e=f?e:e[Symbol.iterator]();;){var i;if(f){if(h>=e.length)break;i=e[h++]}else{if(h=e.next(),h.done)break;i=h.value}i.xhr=d}a[0].upload.chunked&&(a[0].upload.chunks[b[0].chunkIndex].xhr=d);var j=this.resolveOption(this.options.method,a),k=this.resolveOption(this.options.url,a);d.open(j,k,!0),d.timeout=this.resolveOption(this.options.timeout,a),d.withCredentials=!!this.options.withCredentials,d.onload=function(b){c._finishedUploading(a,d,b)},d.onerror=function(){c._handleUploadError(a,d)},(null!=d.upload?d.upload:d).onprogress=function(b){return c._updateFilesUploadProgress(a,d,b)};var l={Accept:"application/json","Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"};this.options.headers&&g.extend(l,this.options.headers);for(var m in l){var n=l[m];n&&d.setRequestHeader(m,n)}var o=new FormData;if(this.options.params){var p=this.options.params;"function"==typeof p&&(p=p.call(this,a,d,a[0].upload.chunked?this._getChunk(a[0],d):null));for(var q in p){var r=p[q];o.append(q,r)}}for(var s=a,t=Array.isArray(s),u=0,s=t?s:s[Symbol.iterator]();;){var v;if(t){if(u>=s.length)break;v=s[u++]}else{if(u=s.next(),u.done)break;v=u.value}var w=v;this.emit("sending",w,d,o)}this.options.uploadMultiple&&this.emit("sendingmultiple",a,d,o),this._addFormElementData(o);for(var x=0;x<b.length;x++){var y=b[x];o.append(y.name,y.data,y.filename)}this.submitRequest(d,o,a)}},{key:"_transformFiles",value:function(a,b){for(var c=this,d=[],e=0,f=0;f<a.length;f++)!function(f){c.options.transformFile.call(c,a[f],function(c){d[f]=c,++e===a.length&&b(d)})}(f)}},{key:"_addFormElementData",value:function(a){if("FORM"===this.element.tagName)for(var b=this.element.querySelectorAll("input, textarea, select, button"),c=Array.isArray(b),d=0,b=c?b:b[Symbol.iterator]();;){var e;if(c){if(d>=b.length)break;e=b[d++]}else{if(d=b.next(),d.done)break;e=d.value}var f=e,g=f.getAttribute("name"),h=f.getAttribute("type");if(h&&(h=h.toLowerCase()),void 0!==g&&null!==g)if("SELECT"===f.tagName&&f.hasAttribute("multiple"))for(var i=f.options,j=Array.isArray(i),k=0,i=j?i:i[Symbol.iterator]();;){var l;if(j){if(k>=i.length)break;l=i[k++]}else{if(k=i.next(),k.done)break;l=k.value}var m=l;m.selected&&a.append(g,m.value)}else(!h||"checkbox"!==h&&"radio"!==h||f.checked)&&a.append(g,f.value)}}},{key:"_updateFilesUploadProgress",value:function(a,b,c){var d=void 0;if(void 0!==c){if(d=100*c.loaded/c.total,a[0].upload.chunked){var e=a[0],f=this._getChunk(e,b);f.progress=d,f.total=c.total,f.bytesSent=c.loaded;e.upload.progress=0,e.upload.total=0,e.upload.bytesSent=0;for(var g=0;g<e.upload.totalChunkCount;g++)void 0!==e.upload.chunks[g]&&void 0!==e.upload.chunks[g].progress&&(e.upload.progress+=e.upload.chunks[g].progress,e.upload.total+=e.upload.chunks[g].total,e.upload.bytesSent+=e.upload.chunks[g].bytesSent);e.upload.progress=e.upload.progress/e.upload.totalChunkCount}else for(var h=a,i=Array.isArray(h),j=0,h=i?h:h[Symbol.iterator]();;){var k;if(i){if(j>=h.length)break;k=h[j++]}else{if(j=h.next(),j.done)break;k=j.value}var l=k;l.upload.progress=d,l.upload.total=c.total,l.upload.bytesSent=c.loaded}for(var m=a,n=Array.isArray(m),o=0,m=n?m:m[Symbol.iterator]();;){var p;if(n){if(o>=m.length)break;p=m[o++]}else{if(o=m.next(),o.done)break;p=o.value}var q=p;this.emit("uploadprogress",q,q.upload.progress,q.upload.bytesSent)}}else{var r=!0;d=100;for(var s=a,t=Array.isArray(s),u=0,s=t?s:s[Symbol.iterator]();;){var v;if(t){if(u>=s.length)break;v=s[u++]}else{if(u=s.next(),u.done)break;v=u.value}var w=v;100===w.upload.progress&&w.upload.bytesSent===w.upload.total||(r=!1),w.upload.progress=d,w.upload.bytesSent=w.upload.total}if(r)return;for(var x=a,y=Array.isArray(x),z=0,x=y?x:x[Symbol.iterator]();;){var A;if(y){if(z>=x.length)break;A=x[z++]}else{if(z=x.next(),z.done)break;A=z.value}var B=A;this.emit("uploadprogress",B,d,B.upload.bytesSent)}}}},{key:"_finishedUploading",value:function(a,b,c){var d=void 0;if(a[0].status!==g.CANCELED&&4===b.readyState){if("arraybuffer"!==b.responseType&&"blob"!==b.responseType&&(d=b.responseText,b.getResponseHeader("content-type")&&~b.getResponseHeader("content-type").indexOf("application/json")))try{d=JSON.parse(d)}catch(a){c=a,d="Invalid JSON response from server."}this._updateFilesUploadProgress(a),200<=b.status&&b.status<300?a[0].upload.chunked?a[0].upload.finishedChunkUpload(this._getChunk(a[0],b)):this._finished(a,d,c):this._handleUploadError(a,b,d)}}},{key:"_handleUploadError",value:function(a,b,c){if(a[0].status!==g.CANCELED){if(a[0].upload.chunked&&this.options.retryChunks){var d=this._getChunk(a[0],b);if(d.retries++<this.options.retryChunksLimit)return void this._uploadData(a,[d.dataBlock]);console.warn("Retried this chunk too often. Giving up.")}for(var e=a,f=Array.isArray(e),h=0,e=f?e:e[Symbol.iterator]();;){if(f){if(h>=e.length)break;e[h++]}else{if(h=e.next(),h.done)break;h.value}this._errorProcessing(a,c||this.options.dictResponseError.replace("{{statusCode}}",b.status),b)}}}},{key:"submitRequest",value:function(a,b,c){a.send(b)}},{key:"_finished",value:function(a,b,c){for(var d=a,e=Array.isArray(d),f=0,d=e?d:d[Symbol.iterator]();;){var h;if(e){if(f>=d.length)break;h=d[f++]}else{if(f=d.next(),f.done)break;h=f.value}var i=h;i.status=g.SUCCESS,this.emit("success",i,b,c),this.emit("complete",i)}if(this.options.uploadMultiple&&(this.emit("successmultiple",a,b,c),this.emit("completemultiple",a)),this.options.autoProcessQueue)return this.processQueue()}},{key:"_errorProcessing",value:function(a,b,c){for(var d=a,e=Array.isArray(d),f=0,d=e?d:d[Symbol.iterator]();;){var h;if(e){if(f>=d.length)break;h=d[f++]}else{if(f=d.next(),f.done)break;h=f.value}var i=h;i.status=g.ERROR,this.emit("error",i,b,c),this.emit("complete",i)}if(this.options.uploadMultiple&&(this.emit("errormultiple",a,b,c),this.emit("completemultiple",a)),this.options.autoProcessQueue)return this.processQueue()}}],[{key:"uuidv4",value:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){var b=16*Math.random()|0;return("x"===a?b:3&b|8).toString(16)})}}]),g}(i);j.initClass(),j.version="5.4.0",j.options={},j.optionsForElement=function(a){return a.getAttribute("id")?j.options[l(a.getAttribute("id"))]:void 0},j.instances=[],j.forElement=function(a){if("string"==typeof a&&(a=document.querySelector(a)),null==(null!=a?a.dropzone:void 0))throw new Error("No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.");return a.dropzone},j.autoDiscover=!0,j.discover=function(){var a=void 0;if(document.querySelectorAll)a=document.querySelectorAll(".dropzone");else{a=[];var b=function(b){return function(){for(var c=[],d=b,e=Array.isArray(d),f=0,d=e?d:d[Symbol.iterator]();;){var g;if(e){if(f>=d.length)break;g=d[f++]}else{if(f=d.next(),f.done)break;g=f.value}var h=g;/(^| )dropzone($| )/.test(h.className)?c.push(a.push(h)):c.push(void 0)}return c}()};b(document.getElementsByTagName("div")),b(document.getElementsByTagName("form"))}return function(){for(var b=[],c=a,d=Array.isArray(c),e=0,c=d?c:c[Symbol.iterator]();;){var f;if(d){if(e>=c.length)break;f=c[e++]}else{if(e=c.next(),e.done)break;f=e.value}var g=f;!1!==j.optionsForElement(g)?b.push(new j(g)):b.push(void 0)}return b}()},j.blacklistedBrowsers=[/opera.*(Macintosh|Windows Phone).*version\/12/i],j.isBrowserSupported=function(){var a=!0;if(window.File&&window.FileReader&&window.FileList&&window.Blob&&window.FormData&&document.querySelector)if("classList"in document.createElement("a"))for(var b=j.blacklistedBrowsers,c=Array.isArray(b),d=0,b=c?b:b[Symbol.iterator]();;){var e;if(c){if(d>=b.length)break;e=b[d++]}else{if(d=b.next(),d.done)break;e=d.value}var f=e;f.test(navigator.userAgent)&&(a=!1)}else a=!1;else a=!1;return a},j.dataURItoBlob=function(a){for(var b=atob(a.split(",")[1]),c=a.split(",")[0].split(":")[1].split(";")[0],d=new ArrayBuffer(b.length),e=new Uint8Array(d),f=0,g=b.length,h=0<=g;h?f<=g:f>=g;h?f++:f--)e[f]=b.charCodeAt(f);return new Blob([d],{type:c})};var k=function(a,b){return a.filter(function(a){return a!==b}).map(function(a){return a})},l=function(a){return a.replace(/[\-_](\w)/g,function(a){return a.charAt(1).toUpperCase()})};j.createElement=function(a){var b=document.createElement("div");return b.innerHTML=a,b.childNodes[0]},j.elementInside=function(a,b){if(a===b)return!0;for(;a=a.parentNode;)if(a===b)return!0;return!1},j.getElement=function(a,b){var c=void 0;if("string"==typeof a?c=document.querySelector(a):null!=a.nodeType&&(c=a),null==c)throw new Error("Invalid `"+b+"` option provided. Please provide a CSS selector or a plain HTML element.");return c},j.getElements=function(a,b){var c=void 0,d=void 0;if(a instanceof Array){d=[];try{for(var e=a,f=Array.isArray(e),g=0,e=f?e:e[Symbol.iterator]();;){if(f){if(g>=e.length)break;c=e[g++]}else{if(g=e.next(),g.done)break;c=g.value}d.push(this.getElement(c,b))}}catch(a){d=null}}else if("string"==typeof a){d=[];for(var h=document.querySelectorAll(a),i=Array.isArray(h),j=0,h=i?h:h[Symbol.iterator]();;){if(i){if(j>=h.length)break;c=h[j++]}else{if(j=h.next(),j.done)break;c=j.value}d.push(c)}}else null!=a.nodeType&&(d=[a]);if(null==d||!d.length)throw new Error("Invalid `"+b+"` option provided. Please provide a CSS selector, a plain HTML element or a list of those.");return d},j.confirm=function(a,b,c){return window.confirm(a)?b():null!=c?c():void 0},j.isValidFile=function(a,b){if(!b)return!0;b=b.split(",");for(var c=a.type,d=c.replace(/\/.*$/,""),e=b,f=Array.isArray(e),g=0,e=f?e:e[Symbol.iterator]();;){var h;if(f){if(g>=e.length)break;h=e[g++]}else{if(g=e.next(),g.done)break;h=g.value}var i=h;if(i=i.trim(),"."===i.charAt(0)){if(-1!==a.name.toLowerCase().indexOf(i.toLowerCase(),a.name.length-i.length))return!0}else if(/\/\*$/.test(i)){if(d===i.replace(/\/.*$/,""))return!0}else if(c===i)return!0}return!1},void 0!==a&&null!==a&&(a.fn.dropzone=function(a){return this.each(function(){return new j(this,a)})}),void 0!==g&&null!==g?g.exports=j:window.Dropzone=j,j.ADDED="added",j.QUEUED="queued",j.ACCEPTED=j.QUEUED,j.UPLOADING="uploading",j.PROCESSING=j.UPLOADING,j.CANCELED="canceled",j.ERROR="error",j.SUCCESS="success";var m=function(a){var b=(a.naturalWidth,a.naturalHeight),c=document.createElement("canvas");c.width=1,c.height=b;var d=c.getContext("2d");d.drawImage(a,0,0);for(var e=d.getImageData(1,0,1,b),f=e.data,g=0,h=b,i=b;i>g;){0===f[4*(i-1)+3]?h=i:g=i,i=h+g>>1}var j=i/b;return 0===j?1:j},n=function(a,b,c,d,e,f,g,h,i,j){var k=m(b);return a.drawImage(b,c,d,e,f,g,h,i,j/k)},o=function(){function a(){d(this,a)}return h(a,null,[{key:"initClass",value:function(){this.KEY_STR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}},{key:"encode64",value:function(a){for(var b="",c=void 0,d=void 0,e="",f=void 0,g=void 0,h=void 0,i="",j=0;;)if(c=a[j++],d=a[j++],e=a[j++],f=c>>2,g=(3&c)<<4|d>>4,h=(15&d)<<2|e>>6,i=63&e,isNaN(d)?h=i=64:isNaN(e)&&(i=64),b=b+this.KEY_STR.charAt(f)+this.KEY_STR.charAt(g)+this.KEY_STR.charAt(h)+this.KEY_STR.charAt(i),c=d=e="",f=g=h=i="",!(j<a.length))break;return b}},{key:"restore",value:function(a,b){if(!a.match("data:image/jpeg;base64,"))return b;var c=this.decode64(a.replace("data:image/jpeg;base64,","")),d=this.slice2Segments(c),e=this.exifManipulation(b,d);return"data:image/jpeg;base64,"+this.encode64(e)}},{key:"exifManipulation",value:function(a,b){var c=this.getExifArray(b),d=this.insertExif(a,c);return new Uint8Array(d)}},{key:"getExifArray",value:function(a){for(var b=void 0,c=0;c<a.length;){if(b=a[c],255===b[0]&225===b[1])return b;c++}return[]}},{key:"insertExif",value:function(a,b){var c=a.replace("data:image/jpeg;base64,",""),d=this.decode64(c),e=d.indexOf(255,3),f=d.slice(0,e),g=d.slice(e),h=f;return h=h.concat(b),h=h.concat(g)}},{key:"slice2Segments",value:function(a){for(var b=0,c=[];;){var d;if(255===a[b]&218===a[b+1])break;if(255===a[b]&216===a[b+1])b+=2;else{d=256*a[b+2]+a[b+3];var e=b+d+2,f=a.slice(b,e);c.push(f),b=e}if(b>a.length)break}return c}},{key:"decode64",value:function(a){var b=void 0,c=void 0,d="",e=void 0,f=void 0,g=void 0,h="",i=0,j=[],k=/[^A-Za-z0-9\+\/\=]/g;for(k.exec(a)&&console.warn("There were invalid base64 characters in the input text.\nValid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\nExpect errors in decoding."),a=a.replace(/[^A-Za-z0-9\+\/\=]/g,"");;)if(e=this.KEY_STR.indexOf(a.charAt(i++)),f=this.KEY_STR.indexOf(a.charAt(i++)),g=this.KEY_STR.indexOf(a.charAt(i++)),h=this.KEY_STR.indexOf(a.charAt(i++)),b=e<<2|f>>4,c=(15&f)<<4|g>>2,d=(3&g)<<6|h,j.push(b),64!==g&&j.push(c),64!==h&&j.push(d),b=c=d="",e=f=g=h="",!(i<a.length))break;return j}}]),a}();o.initClass();return j._autoDiscoverFunction=function(){if(j.autoDiscover)return j.discover()},function(a,b){var c=!1,d=!0,e=a.document,f=e.documentElement,g=e.addEventListener?"addEventListener":"attachEvent",h=e.addEventListener?"removeEventListener":"detachEvent",i=e.addEventListener?"":"on",j=function d(f){if("readystatechange"!==f.type||"complete"===e.readyState)return("load"===f.type?a:e)[h](i+f.type,d,!1),!c&&(c=!0)?b.call(a,f.type||f):void 0};if("complete"!==e.readyState){if(e.createEventObject&&f.doScroll){try{d=!a.frameElement}catch(a){}d&&function a(){try{f.doScroll("left")}catch(b){return void setTimeout(a,50)}return j("poll")}()}e[g](i+"DOMContentLoaded",j,!1),e[g](i+"readystatechange",j,!1),a[g](i+"load",j,!1)}}(window,j._autoDiscoverFunction),g.exports});
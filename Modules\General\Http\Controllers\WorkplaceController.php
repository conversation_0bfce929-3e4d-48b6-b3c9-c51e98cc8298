<?php

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\AdmissionInterview;
use App\Attendance;
use App\CenterEmployee;
use App\Employee;
use App\EmployeeSalary;
use App\GeneralSettings;
use App\MissedClockOut;
use App\Scopes\OrganizationScope;
use App\StudentHefzPlan;
use Carbon\Carbon;
use Doctrine\DBAL\Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use App\Student;
use Module;
use Auth;
use App\Center;

use App\Classes;

class WorkplaceController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function lastAttendanceStatus(Request $request)
    {

        $att = Attendance::where('employee_id', Auth::guard('employee')->user()->id)->latest()->first();
        $lastAttendanceStatus = '';
        if($att->type == 'in'){
            $lastAttendanceStatus = 'Last clock in '.$att->created_at->diffForHumans();
        }
        if($att->type == 'out'){


            $lastAttendanceStatus = 'Last clock out '.$att->created_at->diffForHumans();
        }

        return response()->json([

            'lastAttendanceStatus'=> $lastAttendanceStatus,

        ]);

    }


    public function extraWorkedHours(Request $request)
    {
        try {
            // Check if the user last attendance was entry or exit
            $lastAttendanceType = Attendance::whereDate('clock', date('Y-m-d'))
                ->where('employee_id', auth()->guard('employee')->user()->id)
                ->orderBy('id', 'desc')
                ->first()
                ->type;
                
            $missedClockOut = MissedClockOut::where('employee_id', auth()->guard('employee')->user()->id)->first();

            // Get the weekly attendance hours count
            $date = \Carbon\Carbon::today()->subDays(7);
            $lastSevenDaysAttendance = Attendance::where('employee_id', Auth::guard('employee')->user()->id)
                ->where('clock', '>=', $date)
                ->orderBy('clock')
                ->get()
                ->groupBy(function ($date) {
                    return Carbon::parse($date->clock)->format('d');
                })
                ->toArray();

            $chucnkedArray = array_chunk($lastSevenDaysAttendance, 2);
            $lastSevenDaysAttendanceCount = 0;
            foreach ($chucnkedArray as $attendanceTimeInTimeOutKey => $attendanceTimeInTimeOut) {
                foreach ($attendanceTimeInTimeOut as $key => $dayGroup) {
                    $lastSevenDaysAttendanceCount += Carbon::createFromDate($dayGroup[0]['clock'])
                        ->diffInHours(Carbon::createFromDate($dayGroup[1]['clock']));
                }
            }

            // Calculate Employees Extra worked hours
            $empHoursPerMonth = EmployeeSalary::where('employee_id', Auth::guard('employee')->user()->id)
                ->first()
                ->hours_per_month;

            $attendance = Attendance::where('employee_id', Auth::guard('employee')->user()->id)
                ->whereDate('clock', date('Y-m-d'))
                ->orderBy('employee_id')
                ->get();

            $workedHours = 0;
            $in = null;
            $out = null;

            foreach ($attendance as $item) {
                if ($item->type == 'in') {
                    $in = Carbon::parse($item->clock);
                }

                if ($item->type == 'out') {
                    $out = Carbon::parse($item->clock);
                }

                if (!is_null($in) && !is_null($out)) {
                    $hours = $in->diffInHours($out);
                    $minutes = $in->diffInMinutes($out) % 60;
                    $workedHours = $hours + ($minutes / 60); // Convert to decimal hours
                }
            }

            $extraWorkedHours = $empHoursPerMonth > $workedHours ? 0 : abs($empHoursPerMonth - $workedHours);

            return response()->json([
                'extraWorkedHours' => $extraWorkedHours,
                'status' => 'success'
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Error in extraWorkedHours: ' . $e->getMessage());
            return response()->json([
                'extraWorkedHours' => 0,
                'status' => 'error',
                'message' => 'An error occurred while calculating extra worked hours.'
            ]);
        }
    }

    public function lastOutAttendanceTime(Request $request)
    {

        $lastOutAttendanceTime = Attendance::where('employee_id',Auth::guard('employee')->user()->id)
            ->whereYear('clock',Carbon::today()->year)
            ->whereMonth('clock',Carbon::today()->month)
            ->whereDay('clock',Carbon::today()->day)
            ->where('type', 'out')
            ->orderBy('clock')->get()->last();
        $lastAttendanceTime = Attendance::where('employee_id',Auth::guard('employee')->user()->id)
            ->orderBy('clock')->get()->last();
        $htmlContent = '';

        if($lastAttendanceTime->type == 'out') {

            $lastClockOutRecord = Attendance::where('employee_id', auth()->user()->id)
                ->where('type', 'out')
                ->latest()
                ->first();
            $lastClockOut = Carbon::createFromFormat('Y-m-d H:i:s', $lastClockOutRecord->clock)
                ->format('g:i A'); // Format time with AM/PM indicator

            $lastClockOut = '<span class="red lastClockOut">
                                        ' . $lastClockOut . '
                                        <i class="IC-system S12 gry ML3" data-toggle="tooltip" data-placement="right"
                                           title="Web">

                                        </i>
                                    </span>';


            $htmlContent = '<span class="red lastClockOut">
                                        ' . optional($lastOutAttendanceTime->clock)->toTimeString() . '
                                        <i class="IC-system S12 gry ML3" data-toggle="tooltip" data-placement="right"
                                           title="Web">

                                        </i>
                                    </span>';
        }else{
        $htmlContent = '  <span class="red lastClockOut"> <i
                                                class="IC-system S12 gry ML3" data-toggle="tooltip"
                                                data-placement="right" title="Web"></i></span>';
    }



        return response()->json([

            'htmlContent' =>$htmlContent,
            'lastOutAttendanceTime'=>$lastOutAttendanceTime,
            'lastClockOut'=>$lastClockOut

        ]);

    }


    public function lastAttendanceType(Request $request)
    {
        $lastAttendanceType = Attendance::whereDate('clock',date('Y-m-d'))->where('employee_id',  auth()->guard('employee')->user()->id)->orderBy('id','desc')->first()->type;





        return response()->json([

            'lastAttendanceType' =>$lastAttendanceType,

        ]);

    }
    public function firstClockInAttendanceofTheDay(Request $request)
    {

        $firstClockInAttendanceofTheDay = optional(Attendance::withTrashed()->where('employee_id',Auth::guard('employee')->user()->id)
            ->whereDate('clock',date('Y-m-d'))->where('type','in')
            ->orderBy('clock','asc')->first()->clock)->toTimeString();
        $lastClockInRecord = Attendance::where('employee_id', auth()->guard('employee')->user()->id)
            ->where('type', 'in')
            ->latest()
            ->first();
        
        $lastClockIn = '';
        if ($lastClockInRecord && $lastClockInRecord->clock) {
            $lastClockIn = Carbon::createFromFormat('Y-m-d H:i:s', $lastClockInRecord->clock)
                ->format('g:i A'); // Format time with AM/PM indicator
        }


        $lastClockIn =   ' <div class="col-md-12  ">
                                Time in :

                                <span class="grn lastClockIn">'.$lastClockIn.' <i
                                            class="IC-system S12 gry ML3" data-toggle="tooltip"
                                            data-placement="right" data-toggle="tooltip" title="this is just the last time in."></i></span>

                            </div>';


        $firstClockInAttendanceofTheDay = is_null($firstClockInAttendanceofTheDay) ? '' : $firstClockInAttendanceofTheDay;




        $firstClockInAttendanceofTheDaySmDevice =  ' <div class="col-xs-12 " style="color: #212B5F">
                                    Time in :
                                    <span class="grn lastClockIn">'.$firstClockInAttendanceofTheDay.' <i
                                                class="IC-system S12 gry ML3" data-toggle="tooltip"
                                                data-placement="right" title="Web"></i></span>
                                </div>';



        $firstClockInAttendanceofTheDayMdDevice =
            ' <div class="col-md-12  ">
                                Time in :

                                <span class="grn lastClockIn">'.$firstClockInAttendanceofTheDay.' <i
                                            class="IC-system S12 gry ML3" data-toggle="tooltip"
                                            data-placement="right" title="Web"></i></span>

                            </div>';




        return response()->json([


            'firstClockInAttendanceofTheDaySmDevice' => $firstClockInAttendanceofTheDaySmDevice,
            'firstClockInAttendanceofTheDayMdDevice' => $firstClockInAttendanceofTheDayMdDevice,
            'lastClockIn' => $lastClockIn,

        ]);

    }

    public function getOrganizationName(Request $request)
    {
        $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);
        $organization_name = $systemSetting->organization_name;


        return response()->json([


            'organizationName' => $organization_name,

        ]);

    }


}

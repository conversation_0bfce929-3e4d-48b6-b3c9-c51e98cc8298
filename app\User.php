<?php

namespace App;



use App\Notifications\EmailVerification;
use App\Notifications\EmployeeResetPassword;
use App\Scopes\OrganizationScope;
use App\Scopes\UserVerificationScope;
use App\Services\EmailService;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Contracts\Auth\CanResetPassword;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\Request;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Cache;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\Log;
use App\Traits\SystemViewerAccess;


class User extends Authenticatable implements MustVerifyEmail,CanResetPassword
{

    use \HashmatWaziri\LaravelMultiAuthImpersonate\Models\Impersonate, HasFactory, HasRoles, SoftDeletes, Notifiable, SystemViewerAccess;

//    public mixed $id;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'email', 'username', 'phone', 'password','role_id','full_name','organization_id','is_administrator','display_name','full_name','full_name_trans','access_status','nationality','address_1','address_2','state','zip_code','gender','email_verified_at','updated_by','created_by'
    ];
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];


    protected static function booted()
    {
        static::saved(function ($model) {
            \Illuminate\Support\Facades\Cache::forget('missing_data_count');
        });

        static::deleted(function ($model) {
            \Illuminate\Support\Facades\Cache::forget('missing_data_count');
        });
    }




    /**
     * The centers that this user is allowed to view (for external_collaborators and system viewers).
     */
    public function allowedCenters()
    {
        // For system viewers, return all centers in the organization
        if ($this->hasRole('system_viewer_' . config('organization_id') . '_')) {
            return Center::where('organization_id', config('organization_id'))
                ->whereNull('deleted_at');
        }
        
        // pivot table: organization_center_user
        return $this->belongsToMany(
            Center::class,
            'organization_center_user',
            'user_id',
            'center_id'
        )->withTimestamps();
    }

    /**
     * Helper: Check if user is external collaborator.
     */
    public function isExternalCollaborator()
    {
        return $this->hasRole('external_collaborator');
    }


    // trick: replaced email with username
    public function getEmailForPasswordReset()
    {
        return $this->username;
    }


    /**

     * @throws \Exception
     */
    public function sendPasswordResetNotification($token)
    {

        // Assuming you have the necessary data like $token, $username, etc.

        $resetLink = url(config('app.url').route('password.reset', ['token' => $token, 'username' => request()->get('username')], false));
        $expireTime = config('auth.passwords.'.config('auth.defaults.passwords').'.expire');


        $user = User::where('username', request()->get('username'))->first();

        $emailService = app(\App\Services\EmailService::class);
        $emailService->sendEmail(
            ['email' => $user->email, 'name' => $user->full_name],
            Lang::get('Reset Password Notification'),
            'emails.password_reset', // path to the view created earlier
            ['resetLink' => $resetLink, 'expireTime' => $expireTime]
        );






    }

    public function setFullNameAttribute($value){

        $this->attributes['full_name'] = ucwords($value);
    }

    public function setDisplayNameAttribute($value){

        $this->attributes['display_name'] = ucwords($value);
    }

    protected function verificationUrl($id,$email)
    {
        return URL::temporarySignedRoute(
            'verification.verify',
            Carbon::now()->addMinutes(Config::get('auth.verification.expire', 2880)),
            [
                'id' => $id,
                'hash' => sha1($email)  // Assuming $email is the email you want to verify

            ]
        );
    }

    /**
     * @throws \Exception
     */
    public function sendEmailVerificationNotification()
    {
        $verificationUrl = $this->verificationUrl($this->id, $this->email);

        $emailService = app(\App\Services\EmailService::class);
        $result = $emailService->sendEmail(
            ['email' => $this->email,'name' => $this->full_name],
            "Welcome aboard!",
            'modules.site.templates.wajeha.backEnd.studentInformation.student_verification',
            ['data' => $verificationUrl]
        );

        // If email sending fails, throw an exception to trigger transaction rollback
        if (!($result['success'] ?? false)) {
            $errorMessage = $result['message'] ?? 'Unknown email error';
            Log::error('Failed to send verification email', [
                'user_id' => $this->id,
                'email' => $this->email,
                'error' => $errorMessage,
                'correlation_id' => $result['correlation_id'] ?? null
            ]);
            throw new \Exception("Failed to send verification email: {$errorMessage}");
        }
    }
    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];


    public function generatorConfig(&$generator)
    {
        $generator->setConfig([ 'separator' => '_' ]);
    }
    public function parent()
    {


        return $this->hasOne('App\Guardian');
    }

//    public function parent()
//    {
//
//
//        return $this->belongsTo('App\Guardian', 'id', 'user_id');
//    }
    public function student()
    {
        return $this->belongsTo('App\Student', 'id', 'user_id');
    }

//    /**
//     * @return bool
//     */
    public function canImpersonate()
    {
        // For example
//        return $this->role_id == 24 || $this->role_id == 1 ;
        return true ;

    }


    // when this user is impersonated, then go to this route
    public static function takeRedirectTo()
    {
        return url('/applicationcenter/registration');
    }

    /**
     * The URI to redirect after this user leaves an impersonation.
     *
     */
    public static function leaveRedirectTo()
    {
        return url('/applicationcenter/registration');
    }


//
//    /**
//     * @return bool
//     */
//    public function canBeImpersonated()
//    {
//        // currently only students can be imersonated
//        return $this->can_be_impersonated == 23;
//    }


    public static function checkAuth()
    {
        return true;


    }
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
//        static::addGlobalScope(new UserVerificationScope);

        // Audit trail tracking
        static::creating(function ($model) {
            if (auth()->check()) {
                $model->created_by = auth()->user()->id;
                $model->updated_by = null;
            }
        });

        static::updating(function ($model) {
            if (auth()->check()) {
                $model->updated_by = auth()->user()->id;
            }
        });

        static::restoring(function ($model) {
            if (auth()->check()) {
                $model->updated_by = auth()->user()->id;
            }
        });
    }


    /**
     * Route notifications for the FCM channel.
     *
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return string
     */
    public function routeNotificationForFcm($notification)
    {
        return $this->device_token;
    }



    public function hasAcceptedDisclaimers()
    {
        return !is_null($this->disclaimers_accepted_at);
    }

    public function acceptDisclaimers()
    {
        $this->disclaimers_accepted_at = now();
        $this->save();
    }

    public function needsPasswordReset()
    {
        return (bool) $this->must_change_password;
    }

    public function markPasswordAsChanged()
    {
        $this->must_change_password = 0;
        $this->save();
    }

    public function jobNotifications()
    {
        return $this->hasMany(\Modules\General\Entities\JobNotification::class, 'user_id');
    }






}

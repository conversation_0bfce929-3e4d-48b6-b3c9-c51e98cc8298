<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\Classes;
use App\Student;
use App\StudentNouranyaPlan;
use App\StudentNouranyaReport;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * NouranyaStudentMonthlySheet creates the Student Monthly Progress sheet for Nouranya reports.
 * 
 * Purpose: Export per-student monthly Nouranya report data aggregated across multiple classes.
 * Data source: student_nouranya_reports with lesson progression and achievement calculations.
 * Calculations: Lesson completion rates, attendance percentages, achievement metrics based on lesson ranges.
 * Context: Mirrors the aggregated DataTables structure from MonthlyNouranyaReportAggregatedController.
 * Output: Single sheet with per-student rows including class grouping context for readability.
 */
final class NouranyaStudentMonthlySheet implements WithTitle, WithEvents
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get student monthly data aggregated across all classes
     */
    private function getStudentMonthlyData(): array
    {
        $classIds = $this->filters['classIds'];
        $month = $this->filters['month'];
        $year = $this->filters['year'];
        $studentIds = $this->filters['studentIds'] ?? [];

        $results = [];

        foreach ($classIds as $classId) {
            $class = Classes::find($classId);
            if (!$class) {
                continue;
            }

            // Load active students for this class
            $query = Student::whereHas('joint_classes', function ($q) use ($classId) {
                $q->where('class_id', $classId);
            })
            ->where('status', 'active')
            ->orderBy('full_name', 'asc');

            if (!empty($studentIds)) {
                $query->whereIn('id', $studentIds);
            }

            $students = $query->get();

            $teacherNames = $class->teachers ? $class->teachers->pluck('full_name')->join(', ') : '';
            $programTitle = $class->programs ? optional($class->programs->first())->title : null;

            foreach ($students as $student) {
                $attendanceData = $this->calculateAttendance($student->id, $classId, $month, $year);
                $achievementPercent = $this->calculateAchievement($student->id, $classId, $month, $year);
                $lessonData = $this->getLessonData($student->id, $classId, $month, $year);
                $monthlyPlan = $this->getMonthlyPlan($student->id, $month, $year);
                $monthlyAchievement = $this->getMonthlyAchievement($student->id, $classId, $month, $year);

                $results[] = [
                    'center_name' => $class->center->name ?? 'Unknown Center',
                    'class_name' => $class->name ?? 'Unknown Class',
                    'student_name' => $student->full_name ?? 'Unknown Student',
                    'class_program' => $programTitle ?? 'N/A',
                    'teacher_name' => $teacherNames ?: 'N/A',
                    'lesson_range' => $lessonData['range'],
                    'line_range' => $lessonData['lines'],
                    'talaqqi_achieved' => $lessonData['talaqqi'],
                    'talqeen_achieved' => $lessonData['talqeen'],
                    'monthly_plan' => $monthlyPlan,
                    'monthly_achievement' => $monthlyAchievement,
                    'attendance_percentage' => number_format($attendanceData['percentage'], 1) . '%',
                    'achievement_percentage' => number_format($achievementPercent, 1) . '%',
                    'attendance_details' => $attendanceData['details'],
                ];
            }
        }

        return $results;
    }

    /**
     * Calculate attendance for a student in a class for the month
     */
    private function calculateAttendance(int $studentId, int $classId, int $month, int $year): array
    {
        $class = Classes::find($classId);
        if (!$class || !$class->timetable) {
            return ['percentage' => 0.0, 'details' => 'No timetable found'];
        }

        $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
        if ($totalClasses <= 0) {
            return ['percentage' => 0.0, 'details' => 'No scheduled classes'];
        }

        $attended = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereIn('attendance_id', [1, 2]) // Late and On Time
            ->count();

        $absent = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->where('attendance_id', 3) // Absent
            ->count();

        $percentage = min(100.0, ($attended / $totalClasses) * 100);

        return [
            'percentage' => $percentage,
            'details' => "Attended: {$attended}/{$totalClasses}, Absent: {$absent}"
        ];
    }

    /**
     * Calculate achievement for a student
     */
    private function calculateAchievement(int $studentId, int $classId, int $month, int $year): float
    {
        $plan = StudentNouranyaPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year, $classId) {
                $query->where(function ($q) use ($year, $month, $classId) {
                    $q->whereYear('created_at', $year)
                      ->whereMonth('created_at', $month)
                      ->where('class_id', $classId)
                      ->where('status', 'active');
                })->orWhere(function ($q2) use ($year, $month, $classId) {
                    $q2->whereYear('start_date', $year)
                       ->whereMonth('start_date', $month)
                       ->where('class_id', $classId)
                       ->where('status', 'active');
                });
            })
            ->first();

        if (!$plan) {
            return 0.0;
        }

        $reports = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get();

        if ($reports->isEmpty()) {
            return 0.0;
        }

        return $this->calculateLessonCompletion($plan, $reports);
    }

    /**
     * Calculate lesson completion percentage
     */
    private function calculateLessonCompletion($plan, $reports): float
    {
        $plannedLessons = 0;
        $achievedLessons = 0;

        // Calculate planned lessons
        if ($plan->from_lesson && $plan->to_lesson && $plan->from_lesson <= $plan->to_lesson) {
            $plannedLessons = $plan->to_lesson - $plan->from_lesson + 1;
        }

        // Calculate achieved lessons from reports
        $lessonSet = collect();
        foreach ($reports as $report) {
            // Use to_lesson as the achieved lesson marker when present; fallback to from_lesson
            $ln = $report->to_lesson ?? $report->from_lesson;
            if ($ln) { $lessonSet->push($ln); }
        }
        $achievedLessons = $lessonSet->unique()->count();

        return $plannedLessons > 0 ? min(100, round(($achievedLessons / $plannedLessons) * 100, 1)) : 0;
    }

    /**
     * Get lesson data (ranges achieved in the month)
     */
    private function getLessonData(int $studentId, int $classId, int $month, int $year): array
    {
        $reports = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->where(function($q){ $q->whereNotNull('to_lesson')->orWhereNotNull('from_lesson'); })
            ->orderByRaw('COALESCE(to_lesson, from_lesson) ASC')
            ->get();

        if ($reports->isEmpty()) {
            return ['range' => '—', 'lines' => '—', 'talaqqi' => '—', 'talqeen' => '—'];
        }

        $lessonNumbers = $reports->map(function($r){ return $r->to_lesson ?? $r->from_lesson; })->filter()->unique()->sort()->values();
        $lessonRange = $lessonNumbers->count() > 1 ? 
            "Lessons {$lessonNumbers->first()}–{$lessonNumbers->last()}" : 
            "Lesson {$lessonNumbers->first()}";

        // Aggregate line numbers
        $fromLine = $reports->whereNotNull('from_lesson_line_number')->min('from_lesson_line_number');
        $toLine = $reports->whereNotNull('to_lesson_line_number')->max('to_lesson_line_number');
        
        $lineRange = '—';
        if ($fromLine && $toLine) {
            $lineRange = $fromLine === $toLine ? "Line {$fromLine}" : "Lines {$fromLine}–{$toLine}";
        } elseif ($fromLine) {
            $lineRange = "From Line {$fromLine}";
        } elseif ($toLine) {
            $lineRange = "To Line {$toLine}";
        }

        // Talaqqi and Talqeen ranges
        $talaqqi = $this->getTalaqqi($reports);
        $talqeen = $this->getTalqeen($reports);

        return [
            'range' => $lessonRange,
            'lines' => $lineRange,
            'talaqqi' => $talaqqi,
            'talqeen' => $talqeen
        ];
    }

    /**
     * Get Talaqqi range from reports
     */
    private function getTalaqqi($reports): string
    {
        $talaqqi = $reports->whereNotNull('talaqqi_lesson_number')->pluck('talaqqi_lesson_number')->unique()->sort()->values();
        if ($talaqqi->isEmpty()) {
            return '—';
        }
        
        return $talaqqi->count() > 1 ? 
            "Lessons {$talaqqi->first()}–{$talaqqi->last()}" : 
            "Lesson {$talaqqi->first()}";
    }

    /**
     * Get Talqeen range from reports
     */
    private function getTalqeen($reports): string
    {
        $talqeen = $reports->whereNotNull('talqeen_lesson_number')->pluck('talqeen_lesson_number')->unique()->sort()->values();
        if ($talqeen->isEmpty()) {
            return '—';
        }
        
        return $talqeen->count() > 1 ? 
            "Lessons {$talqeen->first()}–{$talqeen->last()}" : 
            "Lesson {$talqeen->first()}";
    }

    /**
     * Get monthly plan display
     */
    private function getMonthlyPlan(int $studentId, int $month, int $year): string
    {
        $plan = StudentNouranyaPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year) {
                $query->whereYear('start_date', $year)->whereMonth('start_date', $month);
            })
            ->orWhere(function ($query) use ($month, $year) {
                $query->whereYear('created_at', $year)->whereMonth('created_at', $month);
            })
            ->first();

        if (!$plan) {
            return '—';
        }

        $planContent = '';
        if ($plan->from_lesson && $plan->to_lesson) {
            $planContent = $plan->from_lesson === $plan->to_lesson ? 
                "Lesson {$plan->from_lesson}" : 
                "Lessons {$plan->from_lesson}–{$plan->to_lesson}";
        }

        if ($plan->from_lesson_line_number || $plan->to_lesson_line_number) {
            $lineInfo = '';
            if ($plan->from_lesson_line_number && $plan->to_lesson_line_number) {
                $lineInfo = " (Lines {$plan->from_lesson_line_number}–{$plan->to_lesson_line_number})";
            } elseif ($plan->from_lesson_line_number) {
                $lineInfo = " (From Line {$plan->from_lesson_line_number})";
            } elseif ($plan->to_lesson_line_number) {
                $lineInfo = " (To Line {$plan->to_lesson_line_number})";
            }
            $planContent .= $lineInfo;
        }

        return $planContent ?: '—';
    }

    /**
     * Get monthly achievement display
     */
    private function getMonthlyAchievement(int $studentId, int $classId, int $month, int $year): string
    {
        $reports = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->where(function($q){ $q->whereNotNull('to_lesson')->orWhereNotNull('from_lesson'); })
            ->get();

        if ($reports->isEmpty()) {
            return '—';
        }

        $uniqueLessons = $reports->map(function($r){ return $r->to_lesson ?? $r->from_lesson; })->filter()->unique()->count();
        $totalReports = $reports->count();

        return "{$uniqueLessons} lessons ({$totalReports} reports)";
    }

    /**
     * Get table headings
     */
    private function getTableHeadings(): array
    {
        // Align with row keys: include Class Program and Teacher; omit the details column
        return [
            'Center',
            'Class',
            'Student',
            'Class Program',
            'Teacher',
            'Lesson Range',
            'Line Range',
            'Talaqqi Achieved',
            'Talqeen Achieved',
            'Monthly Plan',
            'Monthly Achievement',
            'Attendance %',
            'Achievement %'
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Student Monthly Progress';
    }

    /**
     * Register events for creating the styled table
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $this->createStyledTable($event->sheet);
            },
        ];
    }

    /**
     * Create styled table with data
     */
    private function createStyledTable($sheet)
    {
        $worksheet = $sheet->getDelegate();

        // 1) Performance dashboard (Nouranya)
        $analytics = $this->getAnalyticsData();
        $currentRow = $this->createPerformanceDashboard($worksheet, 1, $analytics) + 2;

        // 2) Title below dashboard
        $studentData = $this->getStudentMonthlyData();
        $headings = $this->getTableHeadings();
        $monthName = $this->filters['monthName'];
        $year = $this->filters['year'];
        $classNames = collect($this->filters['classes'])->pluck('class_code')->join(', ');
        $title = "NOURANYA STUDENT MONTHLY PROGRESS - {$classNames} - {$monthName} {$year}";
        $worksheet->setCellValue("A{$currentRow}", $title);
        $worksheet->mergeCells("A{$currentRow}:M{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 16, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '6f42c1']]
        ]);
        $currentRow += 2;

        // Set headers
        $col = 'A';
        foreach ($headings as $heading) {
            $worksheet->setCellValue($col . $currentRow, $heading);
            $col++;
        }

        // Style headers
        $worksheet->getStyle("A{$currentRow}:M{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '8b5cf6']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $dataStartRow = $currentRow + 1;
        $currentDataRow = $dataStartRow;

        // Add data rows with explicit column mapping to avoid any key-order surprises
        foreach ($studentData as $row) {
            $worksheet->setCellValue("A{$currentDataRow}", $row['center_name'] ?? '');
            // Only class name (no class code)
            $worksheet->setCellValue("B{$currentDataRow}", $row['class_name'] ?? '');
            $worksheet->setCellValue("C{$currentDataRow}", $row['student_name'] ?? '');
            $worksheet->setCellValue("D{$currentDataRow}", $row['class_program'] ?? '');
            $worksheet->setCellValue("E{$currentDataRow}", $row['teacher_name'] ?? '');
            $worksheet->setCellValue("F{$currentDataRow}", $row['lesson_range'] ?? '');
            $worksheet->setCellValue("G{$currentDataRow}", $row['line_range'] ?? '');
            $worksheet->setCellValue("H{$currentDataRow}", $row['talaqqi_achieved'] ?? '');
            $worksheet->setCellValue("I{$currentDataRow}", $row['talqeen_achieved'] ?? '');
            $worksheet->setCellValue("J{$currentDataRow}", $row['monthly_plan'] ?? '');
            $worksheet->setCellValue("K{$currentDataRow}", $row['monthly_achievement'] ?? '');
            $worksheet->setCellValue("L{$currentDataRow}", $row['attendance_percentage'] ?? '');
            $worksheet->setCellValue("M{$currentDataRow}", $row['achievement_percentage'] ?? '');
            $currentDataRow++;
        }

        // Style data rows
        if (count($studentData) > 0) {
            $dataEndRow = $currentDataRow - 1;
            $worksheet->getStyle("A{$dataStartRow}:M{$dataEndRow}")->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ]);

            // Center-align percentage columns (L, M)
            $worksheet->getStyle("L{$dataStartRow}:M{$dataEndRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        } else {
            // No data message
            $worksheet->setCellValue("A{$dataStartRow}", 'No Nouranya student data found for the specified criteria');
            $worksheet->mergeCells("A{$dataStartRow}:L{$dataStartRow}");
            $worksheet->getStyle("A{$dataStartRow}")->applyFromArray([
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'font' => ['italic' => true],
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ]
            ]);
        }

        // Auto-size columns
        foreach (range('A', 'L') as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }
    }

    private function getAnalyticsData(): array
    {
        $classIds = $this->filters['classIds'];
        $year = $this->filters['year'];
        $month = $this->filters['month'];

        $studentFilter = !empty($this->filters['studentIds']) ?
            (" AND snr.student_id IN (" . implode(',', array_map('intval', $this->filters['studentIds'])) . ")") : "";

        // Daily trends
        $dailyTrends = \DB::select("
            SELECT 
                DATE(snr.created_at) AS report_date,
                snr.class_id,
                c.class_code AS class_name,
                COUNT(DISTINCT cs.student_id) AS active_students,
                COUNT(*) AS total_sessions,
                COUNT(CASE WHEN ao.title IN ('late','on_time') THEN 1 END) AS present_count,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) AS avg_score,
                COUNT(DISTINCT COALESCE(snr.to_lesson, snr.from_lesson)) AS lessons_covered
            FROM student_nouranya_report snr
            JOIN classes c ON snr.class_id = c.id
            JOIN class_students cs ON snr.student_id = cs.student_id AND snr.class_id = cs.class_id
            LEFT JOIN attendance_options ao ON snr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON snr.nouranya_evaluation_id = eso.id
            WHERE snr.class_id IN (" . implode(',', $classIds) . ")
              AND YEAR(snr.created_at) = ?
              AND MONTH(snr.created_at) = ?
              AND cs.deleted_at IS NULL
              {$studentFilter}
            GROUP BY DATE(snr.created_at), snr.class_id, c.class_code
            ORDER BY report_date DESC, snr.class_id
            LIMIT 50
        ", [$year, $month]);

        if (!empty($dailyTrends)) {
            $trendClassIds = array_values(array_unique(array_map(function($t){ return (int)$t->class_id; }, $dailyTrends)));
            $classMap = collect(\App\Classes::whereIn('id', $trendClassIds)->get())->keyBy('id');
            foreach ($dailyTrends as $t) {
                $t->class_name = optional($classMap->get((int)$t->class_id))->name ?? ($t->class_name ?? 'N/A');
            }
        }

        // At-risk students (attendance/score)
        $atRiskStudents = \DB::select("
            SELECT 
                s.full_name,
                COUNT(*) AS total_sessions,
                COUNT(CASE WHEN ao.title IN ('late','on_time') THEN 1 END) AS attended_sessions,
                ROUND((COUNT(CASE WHEN ao.title IN ('late','on_time') THEN 1 END)/COUNT(*))*100,1) AS attendance_rate,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) AS avg_score
            FROM student_nouranya_report snr
            JOIN students s ON snr.student_id = s.id
            JOIN class_students cs ON snr.student_id = cs.student_id AND snr.class_id = cs.class_id
            LEFT JOIN attendance_options ao ON snr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON snr.nouranya_evaluation_id = eso.id
            WHERE snr.class_id IN (" . implode(',', $classIds) . ")
              AND YEAR(snr.created_at) = ?
              AND MONTH(snr.created_at) = ?
              AND cs.deleted_at IS NULL
              {$studentFilter}
            GROUP BY snr.student_id, s.full_name
            HAVING attendance_rate < 75 OR avg_score < 0.5
            ORDER BY attendance_rate ASC, avg_score ASC
            LIMIT 15
        ", [$year, $month]);

        // Teacher performance (lessons covered proxy)
        $teacherPerformance = \DB::select("
            SELECT 
                snr.class_id,
                c.class_code,
                GROUP_CONCAT(DISTINCT u.full_name SEPARATOR ', ') AS teacher_name,
                COUNT(DISTINCT snr.student_id) AS unique_students,
                COUNT(DISTINCT DATE(snr.created_at)) AS actual_sessions,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) AS avg_evaluation,
                ROUND((COUNT(CASE WHEN ao.title IN ('late','on_time') THEN 1 END)/NULLIF(COUNT(*),0))*100,1) AS class_attendance_rate,
                COUNT(DISTINCT COALESCE(snr.to_lesson, snr.from_lesson)) AS lessons_covered
            FROM student_nouranya_report snr
            JOIN classes c ON snr.class_id = c.id
            JOIN class_teachers ct ON c.id = ct.class_id
            JOIN employees u ON ct.employee_id = u.id
            LEFT JOIN attendance_options ao ON snr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON snr.nouranya_evaluation_id = eso.id
            WHERE snr.class_id IN (" . implode(',', $classIds) . ")
              AND YEAR(snr.created_at) = ?
              AND MONTH(snr.created_at) = ?
            GROUP BY snr.class_id, c.class_code
            ORDER BY avg_evaluation DESC, class_attendance_rate DESC
        ", [$year, $month]);

        return [
            'daily_trends' => $dailyTrends,
            'at_risk_students' => $atRiskStudents,
            'teacher_performance' => $teacherPerformance,
        ];
    }

    private function createPerformanceDashboard($worksheet, int $startRow, array $analytics): int
    {
        $dailyTrends = $analytics['daily_trends'];
        $atRiskStudents = $analytics['at_risk_students'];
        $teacherPerformance = $analytics['teacher_performance'];

        // Title
        $worksheet->setCellValue("A{$startRow}", "\xF0\x9F\x93\x8C PERFORMANCE DASHBOARD - DAILY INSIGHTS (Nouranya)");
        $worksheet->mergeCells("A{$startRow}:Q{$startRow}");
        $worksheet->getStyle("A{$startRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '6f42c1']]
        ]);

        $currentRow = $startRow + 2;

        // Recent Performance Trends
        $worksheet->setCellValue("A{$currentRow}", "\xF0\x9F\x93\x88 RECENT PERFORMANCE TRENDS (All Selected Classes)");
        $worksheet->mergeCells("A{$currentRow}:F{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2E7D32']]
        ]);
        $currentRow++;

        $trendHeaders = ['Date', 'Class ID', 'Class Name', 'Students', 'Sessions', 'Avg Score', 'Attendance %', 'Lessons Covered'];
        foreach ($trendHeaders as $i => $h) { $col = chr(65 + $i); $worksheet->setCellValue("{$col}{$currentRow}", $h); }
        $currentRow++;
        foreach (array_slice($dailyTrends, 0, 30) as $trend) {
            $attendanceRate = $trend->total_sessions > 0 ? round(((float)$trend->present_count / (float)$trend->total_sessions) * 100, 1) : 0;
            $worksheet->setCellValue("A{$currentRow}", $trend->report_date);
            $worksheet->setCellValue("B{$currentRow}", $trend->class_id);
            $worksheet->setCellValue("C{$currentRow}", $trend->class_name);
            $worksheet->setCellValue("D{$currentRow}", $trend->active_students);
            $worksheet->setCellValue("E{$currentRow}", $trend->total_sessions);
            $worksheet->setCellValue("F{$currentRow}", round((float)($trend->avg_score ?? 0) * 100, 1) . '%');
            $worksheet->setCellValue("G{$currentRow}", $attendanceRate . '%');
            $worksheet->setCellValue("H{$currentRow}", $trend->lessons_covered);
            $currentRow++;
        }

        $currentRow += 2;

        // At-Risk Students
        $worksheet->setCellValue("A{$currentRow}", "\xF0\x9F\x9A\xA8 AT-RISK STUDENTS (Immediate Attention Required - All Classes)");
        $worksheet->mergeCells("A{$currentRow}:Q{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'FF5722']]
        ]);
        $currentRow++;
        if (count($atRiskStudents) > 0) {
            $riskHeaders = ['Student Name', 'Sessions', 'Attended', 'Attendance %', 'Avg Score', 'Risk Level'];
            foreach ($riskHeaders as $i => $h) { $col = chr(65 + $i); $worksheet->setCellValue("{$col}{$currentRow}", $h); }
            $currentRow++;
            foreach (array_slice($atRiskStudents, 0, 15) as $s) {
                $riskLevel = '🔴 Critical';
                if ($s->attendance_rate >= 50 && $s->avg_score >= 0.3) { $riskLevel = '⚠️ Moderate'; }
                $worksheet->setCellValue("A{$currentRow}", $s->full_name);
                $worksheet->setCellValue("B{$currentRow}", $s->total_sessions);
                $worksheet->setCellValue("C{$currentRow}", $s->attended_sessions);
                $worksheet->setCellValue("D{$currentRow}", $s->attendance_rate . '%');
                $worksheet->setCellValue("E{$currentRow}", round((float)($s->avg_score ?? 0) * 100, 1) . '%');
                $worksheet->setCellValue("F{$currentRow}", $riskLevel);
                $currentRow++;
            }
        } else {
            $worksheet->setCellValue("A{$currentRow}", "✅ No at-risk students identified across all classes");
            $currentRow++;
        }

        $currentRow += 2;

        // Teacher Performance
        $worksheet->setCellValue("A{$currentRow}", "👨‍🏫 TEACHER PERFORMANCE SUMMARY (All Classes)");
        $worksheet->mergeCells("A{$currentRow}:Q{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '1565C0']]
        ]);
        $currentRow++;
        if (count($teacherPerformance) > 0) {
            $headers = ['Class', 'Class Name', 'Teacher', 'Students', 'Sessions', 'Avg Score', 'Attendance %', 'Lessons Covered'];
            foreach ($headers as $i => $h) { $col = chr(65 + $i); $worksheet->setCellValue("{$col}{$currentRow}", $h); }
            $currentRow++;
            $classIdList = array_values(array_unique(array_map(function($t){ return (int)$t->class_id; }, $teacherPerformance)));
            $classNameMap = collect(\App\Classes::whereIn('id', $classIdList)->get())->keyBy('id');
            foreach ($teacherPerformance as $t) {
                $className = optional($classNameMap->get((int)$t->class_id))->name ?? 'N/A';
                $worksheet->setCellValue("A{$currentRow}", $t->name);
                $worksheet->setCellValue("B{$currentRow}", $className);
                $worksheet->setCellValue("C{$currentRow}", $t->teacher_name);
                $worksheet->setCellValue("D{$currentRow}", $t->unique_students);
                $worksheet->setCellValue("E{$currentRow}", $t->actual_sessions);
                $worksheet->setCellValue("F{$currentRow}", round((float)($t->avg_evaluation ?? 0) * 100, 1) . '%');
                $worksheet->setCellValue("G{$currentRow}", $t->class_attendance_rate . '%');
                $worksheet->setCellValue("H{$currentRow}", $t->lessons_covered);
                $currentRow++;
            }
        } else {
            $worksheet->setCellValue("A{$currentRow}", "No teacher performance data available");
            $currentRow++;
        }

        return $currentRow + 2;
    }
}

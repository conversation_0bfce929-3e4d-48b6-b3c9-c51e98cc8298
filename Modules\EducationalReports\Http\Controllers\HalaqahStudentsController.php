<?php

namespace Modules\EducationalReports\Http\Controllers;


use App\Classes;
use App\ClassStudent;
use App\EvaluationSchemaOption;
use App\MoshafSurah;
use App\Student;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;


class HalaqahStudentsController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function getStudents($classId)
    {


        $cacheKey = "students_in_class_{$classId}";

        if (app()->environment('local')) {


            // Running in local environment, don't use cache
            $students = \App\Student::whereNotNull('full_name')
                ->with(['joint_classes' => function ($query) use ($classId) {
                    $query->where('class_id', $classId);
                }])
                ->whereHas('joint_classes', function ($q) use ($classId) {
                    $q->where('class_id', $classId);
                })
                ->where(function ($query) use ($classId) {
                    $query->whereHas('nouranya', function ($q) use ($classId) {
                        $q->where('class_id', $classId)
                            ->whereNull('deleted_at'); // Ensure the record is not soft deleted
                    })
                        ->orWhereHas('reports', function ($q) use ($classId) {
                            $q->where('class_id', $classId)
                                ->whereNull('deleted_at'); // Ensure the record is not soft deleted
                        })
                        ->orWhereHas('ijazaMemorizationReport', function ($q) use ($classId) {
                            $q->where('class_id', $classId)
                                ->whereNull('deleted_at'); // Ensure the record is not soft deleted
                        });
                })

                ->get(['id', 'full_name']);



        } else {
            // Running in a different environment, use cache
            $students = \Cache::remember($cacheKey, 60, function () use ($classId) {
                return \App\Student::whereNotNull('full_name')
                    ->with(['joint_classes' => function ($query) use ($classId) {
                        $query->where('class_id', $classId);
                    }])
                    ->whereHas('joint_classes', function ($q) use ($classId) {
                        $q->where('class_id', $classId);
                    })
                    ->where(function ($query) use ($classId) {
                        $query->whereHas('nouranya', function ($q) use ($classId) {
                            $q->where('class_id', $classId)
                                ->whereNull('deleted_at'); // Ensure the record is not soft deleted
                        })
                            ->orWhereHas('reports', function ($q) use ($classId) {
                                $q->where('class_id', $classId)
                                    ->whereNull('deleted_at'); // Ensure the record is not soft deleted
                            })
                            ->orWhereHas('ijazaMemorizationReport', function ($q) use ($classId) {
                                $q->where('class_id', $classId)
                                    ->whereNull('deleted_at'); // Ensure the record is not soft deleted
                            });
                    })
                    ->get(['id', 'full_name']);
            });
        }







        return response()->json($students);
    }
}

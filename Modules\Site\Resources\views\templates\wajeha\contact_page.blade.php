@extends(theme_path('layouts.master'))

@section('content')

    <section class="page-header">
        <div class="container">

            <h1>{{ trans('common.contact') }}</h1>

            <!-- breadcrumbs -->
            <ol class="breadcrumb">
                <li><a href="#">{{ trans('common.home') }}</a></li>
                <li class="active">{{ trans('common.contact') }}</li>
            </ol><!-- /breadcrumbs -->

        </div>
    </section>
    <!-- /PAGE HEADER -->
    @include('site::templates.wajeha.contact_page_designs.'.config('settings.theme_contact_page', 'contact1'))

@stop

@section('js')

<script type="text/javascript" src="//maps.google.com/maps/api/js?key=AIzaSyCqCn84CgZN6o1Xc3P4dM657HIxkX3jzPY"></script>
<script type="text/javascript" src="{{ asset('assets/templates/wajeha/js/gmaps.js') }}"></script>
<script type="text/javascript">

    jQuery(document).ready(function(){
        $('form').submit(function(e){
            if($('[name="message"]').val().length < 10 ){

                e.preventDefault();
                $('[name="message"]').attr('style' , 'border:red solid 1px');
                return false;
            }
        });

        /**
            @BASIC GOOGLE MAP
        **/
        var map2 = new GMaps({
            div: '#map2',
            lat: {{ config('settings.lat' , 0)}},
            lng: {{ config('settings.lng' , 0)}},
            scrollwheel: false,
            zoom : 18
        });

        var marker = map2.addMarker({
            lat: {{ config('settings.lat' , 0)}},
            lng: {{ config('settings.lng' , 0)}},
            title: '{{ config('settings.website_title_'.config('app.locale')) }}'
        });

    });

</script>

<script src="https://www.google.com/recaptcha/api.js?render={{ config('services.recaptcha.sitekey') }}"></script>
<script>
    grecaptcha.ready(function() {
        grecaptcha.execute('{{ config('services.recaptcha.sitekey') }}', {action: 'contact'}).then(function(token) {
            if (token) {
                document.getElementById('recaptcha').value = token;
            }
        });
    });
</script>
@append

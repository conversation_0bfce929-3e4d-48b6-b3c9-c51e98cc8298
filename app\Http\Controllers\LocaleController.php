<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;

class LocaleController extends Controller
{
    /**
     * Supported locales
     * 
     * @var array
     */
    protected $locales = ['en', 'ar'];

    /**
     * Change the application locale and store it in session
     * 
     * @param Request $request
     * @param string $locale
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changeLocale(Request $request, $locale)
    {
        // Validate if the locale is supported
        if (!in_array($locale, $this->locales)) {
            $locale = config('app.fallback_locale', 'en');
        }

        // Store locale in session
        session()->put('locale', $locale);
        
        // Set the application locale
        App::setLocale($locale);
        
        // Set rtl/ltr direction in session based on locale
        session()->put('direction', $locale === 'ar' ? 'rtl' : 'ltr');

        // Clear any session validation errors and old input
        // This prevents validation errors from showing after language change
        session()->forget('errors');
        session()->forget('_old_input');
        
        // Also remove any flash messages that might cause issues
        session()->forget('_flash');
        
        // Remove any Validator instance from the session
        session()->forget('validator');
        
        // Remove cf-turnstile-response validation errors
        session()->forget('cf-turnstile-response');
        
        // Flush all validation errors
        if ($request->session()->has('errors')) {
            $request->session()->forget('errors');
        }
        
        // Log the locale change
        Log::info('User changed locale', [
            'locale' => $locale,
            'user_id' => auth()->id(),
            'ip' => $request->ip()
        ]);
        
        // Get the URL to redirect back to
        $redirectUrl = url()->previous();
        
        // Make sure we're not redirecting to another locale change URL to prevent loops
        if (strpos($redirectUrl, '/locale/') !== false) {
            $redirectUrl = route('register'); // Fallback to registration page if we were on a locale URL
        }
        
        // Redirect to the previous page
        return redirect($redirectUrl)->withoutCookie('laravel_session');
    }
} 
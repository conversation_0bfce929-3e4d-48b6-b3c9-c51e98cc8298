<?php
namespace Modules\Leave\Database\Seeders;

use App\LeaveDefine;
use App\Role;
use Illuminate\Database\Seeder;
use Faker\Factory as Faker;

class leave_definesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = Faker::create();

        $roles = Role::where('id', '!=', 1)/* ->where('id', '!=', 2) */->where('id', '!=', 3)->where('id', '!=', 10)->get();
        foreach ($roles as $key => $value) {
            $store= new LeaveDefine();
            $store->role_id= $value->id;
            $store->type_id=$faker->numberBetween(1,5);
            $store->days=$faker->numberBetween(1,10);
            $store->organization_id = 2;
            $store->created_at = date('Y-m-d h:i:s');
            $store->save();
        }
        /* for($i=1; $i<=5; $i++){
            $store= new LeaveDefine();
            $store->role_id=4;
            $store->type_id=$faker->numberBetween(1,5);
            $store->days=$faker->numberBetween(1,10);
            $store->save();
        } */
    }
}

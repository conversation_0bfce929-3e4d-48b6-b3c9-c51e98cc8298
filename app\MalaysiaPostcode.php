<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Translatable;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\SoftDeletes;

class MalaysiaPostcode extends Model
{
//    use Translatable;


    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'malaysia_postcodes';

    /**
     * The database primary key value.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['postcode', 'address', 'city', 'state'];

//    public $translationModel = 'App\ClassTranslation';

//    public $translatedAttributes = array('name');

//    protected static function boot()
//    {
//        parent::boot();
//
//        static::addGlobalScope(new OrganizationScope);
//    }




}

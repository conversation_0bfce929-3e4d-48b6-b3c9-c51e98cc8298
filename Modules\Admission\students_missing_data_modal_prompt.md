Act as a senior full-stack <PERSON><PERSON> and Bootstrap 3 expert. Your task is to perform a comprehensive repair of the "Students With Missing Data" modal within the **`Admission`** module. You must fix several critical backend, JavaScript, and UI layout bugs.

You are to use the provided screenshot (`issue2.png`) to understand the broken state and use the file `redesigned_students_missing_data_modal_bs3.html` (located in the project root) as the **visual and structural guide** for the final, corrected UI.

**File & Module Context:**
*   **Primary Module:** `Admission`
*   **View File to Modify:** `resources\views\modules\admission\student\index.blade.php`
*   **Controller to Modify:** `Modules\Education\Http\Controllers\StudentMissingDataController.php` (Note: This is in the `Education` module, which is correct per user instruction).

---

### **Execution Plan:**

**Part 1: Fix UI/Layout Issues in `index.blade.php`**

Your primary goal is to restructure the HTML and CSS within the modal in `index.blade.php` to match the clean, panel-based layout from the reference file `redesigned_students_missing_data_modal_bs3.html`.

1.  **Re-structure with Panels:**
    *   Wrap the "Filters" section in a Bootstrap `panel` (`<div class="panel panel-default">...</div>`).
    *   Wrap the "Bulk Student Assignment" section in a separate `panel`.
    *   This will fix the alignment issues with the filters and ensure the bulk assignment items (Program, Center, Class) are fully visible.

2.  **Fix the DataTable Container:**
    *   The DataTable is currently not rendering correctly (pagination and tools are missing/broken). Ensure the `<table class="table ...">` is wrapped in a `<div class="table-responsive">` and that its parent containers do not have conflicting CSS that would hide overflow or restrict width. The layout in the reference file should be your guide.

3.  **Maintain Modal Size:** Do not change the overall width of the modal dialog itself. The user likes the current size.

**Part 2: Fix JavaScript Errors in `index.blade.php`**

1.  **Fix the `forEach` TypeError:**
    *   Locate the JavaScript function named `updateAdvancedAnalytics`.
    *   The error `Uncaught TypeError: selectedStudents.forEach is not a function` indicates that the `selectedStudents` variable is not an array when this function is called.
    *   Find where `selectedStudents` is assigned. It is likely being assigned a raw jQuery object. You must convert it into a proper JavaScript array of student IDs before it is passed to or used by `updateAdvancedAnalytics`.
    *   A common way to fix this is to use jQuery's `.map()` function:
        ```javascript
        // Example of what might be needed
        var selectedStudents = $('input.student-checkbox:checked').map(function() {
            return $(this).val();
        }).get(); // .get() is crucial to convert the jQuery object to an array
        ```

**Part 3: Fix Backend Excel Export Error**

1.  **Create the `exportToExcel` Method:**
    *   Open the controller file: `Modules\Education\Http\Controllers\StudentMissingDataController.php`.
    *   Create a new public method named `exportToExcel`. This will resolve the error: `Method ...::exportToExcel does not exist`.

2.  **Implement Export Logic:**
    *   The `exportToExcel` method must handle two scenarios based on the request data:
        *   **If the request contains an array of selected student IDs:** The export should only include those specific students.
        *   **If the request does NOT contain selected student IDs:** The export should include all students that match the filter criteria sent in the request. You will likely need to reuse the filtering logic from the method that populates the DataTable.
    *   You will need to use a library like Maatwebsite/Excel (if available in the project) or generate a CSV response manually.

3.  **Update the "Export to Excel" Button:**
    *   In `index.blade.php`, ensure the JavaScript for the "Export to Excel" button correctly sends the selected student IDs (if any) or the filter data to the new `exportToExcel` route.

---

**Final Goal:** After your work is complete, the modal should be visually identical to the layout in `redesigned_students_missing_data_modal_bs3.html`. All JavaScript and backend errors must be resolved. Selecting students should work without console errors, and the export button should function correctly according to the specified logic.
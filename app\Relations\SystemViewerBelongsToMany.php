<?php

namespace App\Relations;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use App\Center;

/**
 * SystemViewerBelongsToMany: Custom relationship for system viewer universal access
 * 
 * This class extends <PERSON><PERSON>'s BelongsToMany relationship to provide system viewers
 * with transparent access to all organizational entities while maintaining the
 * proper relationship contract that <PERSON><PERSON> expects.
 */
class SystemViewerBelongsToMany extends BelongsToMany
{
    /**
     * Whether this relationship should return all organizational entities
     */
    protected $returnAllOrganizationalEntities = false;

    /**
     * Set this relationship to return all organizational entities
     */
    public function returnAllOrganizationalEntities($value = true): self
    {
        $this->returnAllOrganizationalEntities = $value;
        return $this;
    }

    /**
     * Get the results of the relationship.
     *
     * @return mixed
     */
    public function getResults()
    {
        if ($this->returnAllOrganizationalEntities) {
            // For system viewers, return all entities of the related type within organization
            return $this->getAllOrganizationalEntities();
        }

        return parent::getResults();
    }

    /**
     * Execute the query and get the first result.
     *
     * @param  array  $columns
     * @return mixed
     */
    public function first($columns = ['*'])
    {
        if ($this->returnAllOrganizationalEntities) {
            return $this->getAllOrganizationalEntities()->first();
        }

        return parent::first($columns);
    }

    /**
     * Execute the query as a "select" statement.
     *
     * @param  array  $columns
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function get($columns = ['*'])
    {
        if ($this->returnAllOrganizationalEntities) {
            return $this->getAllOrganizationalEntities();
        }

        return parent::get($columns);
    }

    /**
     * Get all organizational entities of the related type
     */
    protected function getAllOrganizationalEntities()
    {
        // Get the related model class
        $relatedModel = $this->getRelated();
        
        // Build a query for all entities in the organization
        $query = $relatedModel->newQuery()
            ->where('organization_id', config('organization_id'))
            ->whereNull('deleted_at');

        return $query->get();
    }

    /**
     * Add the constraints for a relationship query.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  \Illuminate\Database\Eloquent\Builder  $parentQuery
     * @param  array|mixed  $columns
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getRelationExistenceQuery(Builder $query, Builder $parentQuery, $columns = ['*'])
    {
        if ($this->returnAllOrganizationalEntities) {
            // For system viewers, always return true for existence queries
            return $query->where('organization_id', config('organization_id'));
        }

        return parent::getRelationExistenceQuery($query, $parentQuery, $columns);
    }

    /**
     * Get the pivot attributes from a model.
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @param  \Illuminate\Database\Eloquent\Model  $parent
     * @return array
     */
    protected function migratePivotAttributes(Model $model, Model $parent)
    {
        if ($this->returnAllOrganizationalEntities) {
            // For system viewers, create virtual pivot attributes
            return [];
        }

        return parent::migratePivotAttributes($model, $parent);
    }

    /**
     * Get the key value of the parent's local key.
     *
     * @return mixed
     */
    public function getParentKey()
    {
        if ($this->returnAllOrganizationalEntities) {
            // For system viewers, we don't need a real parent key
            return 'system_viewer_virtual_key';
        }

        return parent::getParentKey();
    }
} 
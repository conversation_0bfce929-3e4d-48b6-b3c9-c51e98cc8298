<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class AssignClassTeacher extends Model
{
    public function className(){
		return $this->belongsTo('App\Class', 'class_id', 'id');
	}
	public function section(){
		return $this->belongsTo('App\Section', 'section_id', 'id');
	}

	public function classTeachers(){
		return $this->hasMany('App\ClassTeacher', 'assign_class_teacher_id', 'id');
	}
}

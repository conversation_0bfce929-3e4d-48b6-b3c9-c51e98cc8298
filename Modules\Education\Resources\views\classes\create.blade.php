@extends('layouts.hound')
@section('mytitle', 'Create Education Class')

@section('content')
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">Create New class</div>
                    <div class="panel-body">
                        <a href="{{ route('classes.index') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
                        <br />
                        <br />

                        @if ($errors->any())
                            <ul class="alert alert-danger">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        @endif

                        @if(!$centers)
                            <div class="alert alert-warning">
                                <h3 class="text-center">
                                    There is no center added yet in your institution, You need to add center before adding class.
                                    <br>
                                    You can <a href="{{ url('workplace/education/centers/create')}}">Click here </a> to add center
                                </h3>
                            </div>
                        @else
                        {!! Form::open(['url' => '/workplace/education/classes', 'class' => 'form-horizontal', 'files' => true]) !!}
                        
                        @include ('education::classes.form')
                        
                        {!! Form::close() !!}
                        @endif

                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use Mo<PERSON>les\JobSeeker\Services\EmailContentManagerService;
use Mo<PERSON>les\JobSeeker\Services\JobDetailFetchingService;
use Modules\JobSeeker\Jobs\FetchJobDetailsJob;
use Modules\JobSeeker\Entities\JobProvider;
use Modules\JobSeeker\Entities\EmailContentSetting;
use Mo<PERSON><PERSON>\JobSeeker\Entities\Job;

/**
 * EmailContentSystemIntegrationTest
 * 
 * Integration tests for the complete Email Content Management System
 * Tests the journey from Kernel scheduler to email delivery
 */
final class EmailContentSystemIntegrationTest extends TestCase
{
    use DatabaseTransactions;

    private EmailContentManagerService $emailContentService;
    private JobDetailFetchingService $jobDetailService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Skip if database tables don't exist yet
        if (!$this->tablesExist()) {
            $this->markTestSkipped('Database tables not created yet. Execute SQL files first.');
        }
        
        $this->emailContentService = app(EmailContentManagerService::class);
        $this->jobDetailService = app(JobDetailFetchingService::class);
    }

    /** @test */
    public function it_can_access_admin_interface_routes()
    {
        $this->artisan('route:list')
             ->assertExitCode(0);

        // Check that our routes are registered
        $routes = collect(Route::getRoutes())->filter(function ($route) {
            return str_contains($route->getName() ?? '', 'email_content_manager');
        });

        $this->assertGreaterThanOrEqual(8, $routes->count(), 'All email content manager routes should be registered');

        // Test specific route names
        $expectedRoutes = [
            'admin.jobseeker.email_content_manager.index',
            'admin.jobseeker.email_content_manager.field.update',
            'admin.jobseeker.email_content_manager.preview',
            'admin.jobseeker.email_content_manager.test_email',
        ];

        foreach ($expectedRoutes as $routeName) {
            $this->assertNotNull(
                Route::getRoutes()->getByName($routeName),
                "Route {$routeName} should be registered"
            );
        }
    }

    /** @test */
    public function it_integrates_with_laravel_scheduler_system()
    {
        // Test that our command can be registered with the scheduler
        $this->artisan('schedule:list')
             ->assertExitCode(0);

        // Test our custom test command
        $this->artisan('jobseeker:test-email-content-system', ['--step' => 'services'])
             ->assertExitCode(0);
    }

    /** @test */
    public function it_can_generate_email_preview_end_to_end()
    {
        $this->createBasicEmailSettings();

        // Test email template rendering
        $sampleJob = $this->emailContentService->getSampleJobData();
        $sampleJobSeeker = (object) ['name' => 'Integration Test User', 'email' => '<EMAIL>'];
        $sampleSetup = (object) ['name' => 'Integration Test Setup'];

        $html = view('modules.jobseeker.emails.jobs.jobseeker_notification_new', [
            'jobs' => [$sampleJob],
            'jobSeeker' => $sampleJobSeeker,
            'setup' => $sampleSetup,
        ])->render();

        $this->assertNotEmpty($html);
        $this->assertStringContains('Senior Software Developer', $html);
        $this->assertStringContains('Tech Solutions Inc.', $html);
        $this->assertStringContains('Integration Test User', $html);
        
        // Ensure no empty sections when fields are disabled
        $this->assertStringNotContains('<div class="job-section"></div>', $html);
    }

    /** @test */
    public function it_handles_dynamic_field_management()
    {
        $this->createBasicEmailSettings();

        $activeFields = $this->emailContentService->getActiveFields();
        $this->assertGreaterThan(0, $activeFields->count());

        // Test field filtering
        $sampleJob = $this->emailContentService->getSampleJobData();
        $fieldGroups = $this->emailContentService->getFieldGroupsForJob($sampleJob);
        
        $this->assertIsObject($fieldGroups);
        $this->assertTrue($fieldGroups->has('basic'));
    }

    /** @test */
    public function it_can_apply_preset_configurations()
    {
        $this->createBasicEmailSettings();

        $presets = $this->emailContentService->getPresets();
        $this->assertArrayHasKey('minimal', $presets);
        $this->assertArrayHasKey('standard', $presets);
        $this->assertArrayHasKey('detailed', $presets);

        // Test applying a preset
        $result = $this->emailContentService->applyPreset('minimal', 'integration-test');
        $this->assertTrue($result);

        // Verify preset was applied
        $enabledFields = $this->emailContentService->getEnabledFieldNames();
        $this->assertContains('position', $enabledFields);
    }

    /** @test */
    public function it_integrates_with_queue_system()
    {
        Queue::fake();

        $provider = $this->createTestProvider();
        $job = $this->createTestJob($provider);

        // Test job dispatching
        FetchJobDetailsJob::dispatch($job);

        Queue::assertPushed(FetchJobDetailsJob::class, function ($job) {
            return $job->queue === 'job-details';
        });
    }

    /** @test */
    public function it_handles_job_detail_fetching_workflow()
    {
        $provider = $this->createTestProvider();
        $job = $this->createTestJob($provider);

        // Test that job needs detailed fetch
        $needsFetch = $this->jobDetailService->needsDetailedFetch($job);
        $this->assertTrue($needsFetch);

        // Note: We can't test actual HTTP fetching in unit tests
        // but we can test the workflow logic
    }

    /** @test */
    public function it_maintains_backward_compatibility_with_existing_notification_system()
    {
        // Test that JobAlertNotification class exists and is updated
        $this->assertTrue(
            class_exists(\Modules\JobSeeker\Notifications\JobAlertNotification::class),
            'JobAlertNotification class should exist'
        );

        // Check that it references the new template
        $reflection = new \ReflectionClass(\Modules\JobSeeker\Notifications\JobAlertNotification::class);
        $source = file_get_contents($reflection->getFileName());
        
        $this->assertStringContains(
            'jobseeker_notification_new',
            $source,
            'JobAlertNotification should reference the new email template'
        );
    }

    /** @test */
    public function it_handles_error_scenarios_gracefully()
    {
        // Test with empty job data
        $fieldGroups = $this->emailContentService->getFieldGroupsForJob([]);
        $this->assertIsObject($fieldGroups);

        // Test with malformed job data
        $fieldGroups = $this->emailContentService->getFieldGroupsForJob(['invalid' => 'data']);
        $this->assertIsObject($fieldGroups);

        // Test service instantiation with missing dependencies
        try {
            $service = app(EmailContentManagerService::class);
            $this->assertInstanceOf(EmailContentManagerService::class, $service);
        } catch (\Exception $e) {
            $this->fail('Service should instantiate gracefully: ' . $e->getMessage());
        }
    }

    /** @test */
    public function it_validates_email_template_structure()
    {
        $templatePath = resource_path('views/modules/jobseeker/emails/jobs/jobseeker_notification_new.blade.php');
        $this->assertFileExists($templatePath, 'New email template should exist');

        $templateContent = file_get_contents($templatePath);
        
        // Check for proper Bootstrap 5 structure
        $this->assertStringContains('@extends(\'modules.jobseeker.layouts.app\')', $templateContent);
        
        // Check for dynamic field rendering
        $this->assertStringContains('$emailSettings->getFieldGroupsForJob', $templateContent);
        $this->assertStringContains('shouldShowField', $templateContent);
        
        // Check for responsive design
        $this->assertStringContains('@media', $templateContent);
        
        // Check for conditional rendering (no empty spaces)
        $this->assertStringContains('@if($fields->isNotEmpty())', $templateContent);
    }

    /** @test */
    public function it_provides_comprehensive_logging_and_monitoring()
    {
        // Test that logging is properly configured
        $this->assertTrue(
            class_exists(\Illuminate\Support\Facades\Log::class),
            'Logging facade should be available'
        );

        // Test error handling in services
        try {
            $this->emailContentService->formatFieldValue(
                new \stdClass(), // Invalid field setting
                []
            );
        } catch (\Exception $e) {
            // Should handle gracefully or throw meaningful exceptions
            $this->assertNotEmpty($e->getMessage());
        }
    }

    /**
     * Helper method to check if required tables exist
     */
    private function tablesExist(): bool
    {
        try {
            return \Illuminate\Support\Facades\Schema::hasTable('jobseeker_job_providers') &&
                   \Illuminate\Support\Facades\Schema::hasTable('jobseeker_email_content_settings') &&
                   \Illuminate\Support\Facades\Schema::hasTable('jobseeker_job_detailed_info');
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Create basic email content settings for testing
     */
    private function createBasicEmailSettings(): void
    {
        if (!$this->tablesExist()) {
            return;
        }

        EmailContentSetting::create([
            'field_name' => 'position',
            'is_enabled' => true,
            'display_label' => 'Job Title',
            'display_order' => 1,
            'field_group' => 'basic',
            'conditional_display' => false,
            'requires_provider_fetch' => false,
        ]);

        EmailContentSetting::create([
            'field_name' => 'company_name',
            'is_enabled' => true,
            'display_label' => 'Company',
            'display_order' => 2,
            'field_group' => 'basic',
            'conditional_display' => false,
            'requires_provider_fetch' => false,
        ]);
    }

    /**
     * Create test provider
     */
    private function createTestProvider(): JobProvider
    {
        if (!$this->tablesExist()) {
            // Return mock object if tables don't exist
            return new class extends JobProvider {
                public $id = 1;
                public $slug = 'test-provider';
            };
        }

        return JobProvider::create([
            'name' => 'Test Provider',
            'slug' => 'test-provider',
            'base_url' => 'https://test.com',
            'scraping_endpoint' => 'https://test.com/jobs',
            'is_active' => true,
            'supports_scraping' => true,
            'fetch_method' => 'scraping',
        ]);
    }

    /**
     * Create test job
     */
    private function createTestJob(JobProvider $provider): Job
    {
        return Job::create([
            'position' => 'Test Position',
            'company_name' => 'Test Company',
            'slug' => 'test-position',
            'source' => 'test',
            'provider_id' => $provider->id,
        ]);
    }
}

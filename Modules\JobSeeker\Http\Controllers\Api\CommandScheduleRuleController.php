<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Modules\JobSeeker\Entities\CommandScheduleRule;

final class CommandScheduleRuleController extends Controller
{
    /**
     * Display the specified CommandScheduleRule with related filter.
     */
    public function show(int $id): JsonResponse
    {
        $rule = CommandScheduleRule::with('filter')->findOrFail($id);

        return response()->json([
            'success' => true,
            'data'    => $rule,
        ]);
    }
}

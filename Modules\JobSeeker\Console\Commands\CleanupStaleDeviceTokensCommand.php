<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Entities\UserDeviceToken;
use Carbon\Carbon;

final class CleanupStaleDeviceTokensCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobseeker:cleanup-device-tokens 
                            {--days=90 : Number of days to consider a token stale}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up stale device tokens that haven\'t been used for a specified period';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $days = (int) $this->option('days');
        $isDryRun = $this->option('dry-run');
        $cutoffDate = Carbon::now()->subDays($days);
        
        $this->info("🧹 Starting device token cleanup process...");
        $this->info("📅 Cutoff date: {$cutoffDate->format('Y-m-d H:i:s')} ({$days} days ago)");
        
        if ($isDryRun) {
            $this->warn("🔍 DRY RUN MODE - No tokens will be actually deleted");
        }
        
        Log::info("CleanupStaleDeviceTokensCommand: Starting cleanup", [
            'days' => $days,
            'cutoff_date' => $cutoffDate,
            'is_dry_run' => $isDryRun
        ]);

        try {
            // Find stale tokens
            $staleTokensQuery = UserDeviceToken::where(function($query) use ($cutoffDate) {
                $query->where('last_used_at', '<', $cutoffDate)
                      ->orWhereNull('last_used_at');
            });
            
            $totalStaleTokens = $staleTokensQuery->count();
            
            if ($totalStaleTokens === 0) {
                $this->info("✅ No stale device tokens found!");
                Log::info("CleanupStaleDeviceTokensCommand: No stale tokens found");
                return Command::SUCCESS;
            }
            
            $this->info("📱 Found {$totalStaleTokens} stale device tokens");
            
            // Get detailed breakdown
            $tokensByPlatform = $staleTokensQuery->selectRaw('platform, COUNT(*) as count')
                ->groupBy('platform')
                ->pluck('count', 'platform')
                ->toArray();
            
            $this->table(
                ['Platform', 'Stale Tokens'],
                collect($tokensByPlatform)->map(function($count, $platform) {
                    return [$platform ?: 'Unknown', $count];
                })->values()->toArray()
            );
            
            if (!$isDryRun) {
                if (!$this->confirm("Are you sure you want to delete these {$totalStaleTokens} stale tokens?")) {
                    $this->info("❌ Operation cancelled by user");
                    return Command::SUCCESS;
                }
            }
            
            // Process deletion in chunks to avoid memory issues
            $deletedCount = 0;
            $chunkSize = 100;
            
            $staleTokensQuery->chunk($chunkSize, function($tokens) use (&$deletedCount, $isDryRun, $cutoffDate) {
                foreach ($tokens as $token) {
                    $lastUsed = $token->last_used_at ? $token->last_used_at->format('Y-m-d H:i:s') : 'Never';
                    
                    if ($isDryRun) {
                        $this->line("Would delete: ID {$token->id}, Platform: {$token->platform}, Last used: {$lastUsed}");
                    } else {
                        Log::info("CleanupStaleDeviceTokensCommand: Deleting stale token", [
                            'token_id' => $token->id,
                            'job_seeker_id' => $token->job_seeker_id,
                            'platform' => $token->platform,
                            'last_used_at' => $token->last_used_at,
                            'cutoff_date' => $cutoffDate
                        ]);
                        
                        $token->delete();
                    }
                    
                    $deletedCount++;
                }
            });
            
            if ($isDryRun) {
                $this->info("🔍 DRY RUN: Would delete {$deletedCount} stale device tokens");
            } else {
                $this->info("✅ Successfully deleted {$deletedCount} stale device tokens");
                
                Log::info("CleanupStaleDeviceTokensCommand: Cleanup completed", [
                    'deleted_count' => $deletedCount,
                    'days' => $days,
                    'cutoff_date' => $cutoffDate
                ]);
            }
            
            // Show current active tokens summary
            $activeTokens = UserDeviceToken::where('last_used_at', '>=', $cutoffDate)->count();
            $this->info("📊 Remaining active tokens: {$activeTokens}");
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error("❌ Error during cleanup: " . $e->getMessage());
            
            Log::error("CleanupStaleDeviceTokensCommand: Cleanup failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'days' => $days,
                'is_dry_run' => $isDryRun
            ]);
            
            return Command::FAILURE;
        }
    }
} 
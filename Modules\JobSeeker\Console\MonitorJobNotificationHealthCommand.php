<?php

namespace Modules\JobSeeker\Console;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Services\JobNotificationMonitoringService;

class MonitorJobNotificationHealthCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobseeker:monitor-job-notifications {--days=1 : Days to look back} {--send-report : Send a report email to administrators}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor job notification system health and generate reports';

    /**
     * @var JobNotificationMonitoringService
     */
    protected $monitoringService;

    /**
     * Create a new command instance.
     *
     * @param JobNotificationMonitoringService $monitoringService
     * @return void
     */
    public function __construct(JobNotificationMonitoringService $monitoringService)
    {
        parent::__construct();
        $this->monitoringService = $monitoringService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting job notification system health monitoring');
        Log::info('MonitorJobNotificationHealthCommand: Started');
        
        $startTime = microtime(true);
        $days = (int)$this->option('days');
        $sendReport = (bool)$this->option('send-report');
        
        try {
            $healthData = $this->monitoringService->getSystemHealth($days);
            
            if (isset($healthData['error'])) {
                $this->error('Error retrieving system health: ' . $healthData['message']);
                return 1;
            }
            
            // Display summary information
            $this->info('System Health Summary (Last ' . $days . ' days):');
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Setups Processed', $healthData['summary']['total_setups_processed']],
                    ['Recipients Processed', $healthData['summary']['total_recipients_processed']],
                    ['Emails Sent', $healthData['summary']['total_emails_sent']],
                    ['Emails Failed', $healthData['summary']['total_emails_failed']],
                    ['Error Rate', number_format($healthData['summary']['error_rate'] * 100, 2) . '%'],
                    ['Avg Processing Time', number_format($healthData['summary']['avg_processing_time_ms'], 2) . 'ms'],
                    ['Avg Email Time', number_format($healthData['summary']['avg_email_time_ms'], 2) . 'ms'],
                    ['Max Memory Used', number_format($healthData['summary']['max_memory_used_mb'], 2) . 'MB'],
                ]
            );
            
            // Display failure information if any
            if (!empty($healthData['failures'])) {
                $this->info('Failure Summary:');
                
                $failureRows = [];
                foreach ($healthData['failures'] as $type => $counts) {
                    $failureRows[] = [
                        $type,
                        $counts['pending'],
                        $counts['retrying'],
                        $counts['failed'],
                        $counts['resolved'],
                        $counts['total']
                    ];
                }
                
                $this->table(
                    ['Error Type', 'Pending', 'Retrying', 'Failed', 'Resolved', 'Total'],
                    $failureRows
                );
            } else {
                $this->info('No failures recorded in the selected time period.');
            }
            
            // Send report if requested
            if ($sendReport) {
                $this->info('Sending system health report to administrators...');
                $sent = $this->monitoringService->sendSystemHealthReport($healthData);
                
                if ($sent) {
                    $this->info('System health report sent successfully.');
                } else {
                    $this->warn('Failed to send system health report.');
                }
            }
            
            $executionTime = round((microtime(true) - $startTime), 2);
            $this->info("Monitoring completed in {$executionTime} seconds");
            
            Log::info('MonitorJobNotificationHealthCommand: Completed', [
                'days' => $days,
                'send_report' => $sendReport,
                'execution_time' => $executionTime
            ]);
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Error monitoring system health: ' . $e->getMessage());
            Log::error('MonitorJobNotificationHealthCommand: Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }
} 
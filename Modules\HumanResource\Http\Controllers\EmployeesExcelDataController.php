<?php

namespace Modules\HumanResource\Http\Controllers;

use Barryvdh\DomPDF\Facade\Pdf;
use App\Employee;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Excel;
use App\Exports\EmployeesExport;
use Illuminate\Http\Request;
class EmployeesExcelDataController extends Controller
{
    /**
     * Export employees to an Excel sheet.
     */
    public function exportEmployeesToExcel()
    {
        return \Excel::download(new EmployeesExport, 'employees.xlsx');
    }}

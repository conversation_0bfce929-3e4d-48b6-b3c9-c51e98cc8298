<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentAttendance extends Model
{
    protected $casts= [
        'class_time' => 'datetime:Y-m-d H:i:s'


    ];


    protected $fillable = ['class_report_id','student_id','attendance','note','class_time','attendance_date','attendance_type'];

    public function getAttendanceShortcutAttribute(){

        return strtoupper(substr($this->attendance,0,1));
    }
    protected static function boot()
    {
        parent::boot();

        // Any time the student attendance is created, add the organization_id
        static::creating( function ($studentAttendance) {
            $studentAttendance->organization_id = config('organization_id');
            $studentAttendance->created_by = auth()->user()->id;

        } );

        // add the global scope for the queries
        static::addGlobalScope(new OrganizationScope);


    }

    // use SoftDeletes;

    public function student()
    {
        return $this->belongsTo('App\Student');
    }
    
}

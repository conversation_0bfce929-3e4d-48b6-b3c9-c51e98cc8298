<?php

namespace Modules\ApplicationCenter\Entities;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;

class StudentRegistration extends Model
{
    protected $fillable = [];

     public function class(){
        return $this->belongsTo('App\Class', 'class_id', 'id');
    }

	public function section(){
		return $this->belongsTo('App\Section', 'section_id', 'id');
	}

	public function academicYear(){
		return $this->belongsTo('App\AcademicYear', 'academic_year', 'id');
	}

	public function gender(){
		return $this->belongsTo('App\BaseSetup', 'gender_id', 'id');
	}
	public function school(){
		return $this->belongsTo('App\School', 'organization_id', 'id');
	}
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);


    }


	
}

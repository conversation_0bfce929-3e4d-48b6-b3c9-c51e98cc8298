            padding: 20px;
            background-color: #ffffff;
            border: 1px solid #e4e4e4;
            border-radius: 5px;
        }
        .header {
            background-color: #cc2f2f;
            padding: 20px;
            color: white;
            text-align: center;
            margin: -20px -20px 20px -20px;
            border-radius: 5px 5px 0 0;
        }
        h1 {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        h2 {
            font-size: 18px;
            margin-top: 20px;
            color: #444;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .summary {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .issue-item {
            margin-bottom: 8px;
            padding: 10px;
            background-color: #fff4f4;
            border-left: 4px solid #cc2f2f;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            font-size: 13px;
        }
        .file-name {
            background-color: #3b3b3b;
            color: white;
            padding: 8px 12px;
            border-radius: 3px;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
            display: inline-block;
        }
        .meta {
            color: #666;
            margin-top: 25px;
            font-size: 12px;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        .date-badge {
            display: inline-block;
            background-color: #f57f17;
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            margin-right: 10px;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class=\"container\">
        <div class=\"header\">
            <h1>🚨 System Log Alert</h1>
        </div>
        
        <div class=\"summary\">
            <span class=\"date-badge\">2025-06-02</span>
            <strong>System:</strong> Itqan AlQuran<br>
            <strong>Server:</strong> ubuntu-8gb-hel1-1<br>
            <strong>Detected At:</strong> 2025-06-02 19:00:02<br>
            <strong>Total Issues Found:</strong> 62<br>
            <strong>Report Date:</strong> 2025-06-02
        </div>
        
        <p>The log monitoring system has detected potential issues in the application logs for <strong>2025-06-02</strong> that require your attention:</p>
        
                    <h2>command_failures.log (57 issues)</h2>
            
                            <div class=\"file-name\">Line 1</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:39:02  
</div>
                            <div class=\"file-name\">Line 2</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:39:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 3</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:39:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 4</div>
                <div class=\"issue-item\">[2025-06-02 18:40:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:40:02  
</div>
                            <div class=\"file-name\">Line 5</div>
                <div class=\"issue-item\">[2025-06-02 18:40:03] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:40:03 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 6</div>
                <div class=\"issue-item\">[2025-06-02 18:40:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:40:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 7</div>
                <div class=\"issue-item\">[2025-06-02 18:41:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:41:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 8</div>
                <div class=\"issue-item\">[2025-06-02 18:41:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:41:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 9</div>
                <div class=\"issue-item\">[2025-06-02 18:42:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:42:02  
</div>
                            <div class=\"file-name\">Line 10</div>
                <div class=\"issue-item\">[2025-06-02 18:42:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:42:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 11</div>
                <div class=\"issue-item\">[2025-06-02 18:42:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:42:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 12</div>
                <div class=\"issue-item\">[2025-06-02 18:43:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:43:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 13</div>
                <div class=\"issue-item\">[2025-06-02 18:43:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:43:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 14</div>
                <div class=\"issue-item\">[2025-06-02 18:44:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:44:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 15</div>
                <div class=\"issue-item\">[2025-06-02 18:44:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:44:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 16</div>
                <div class=\"issue-item\">[2025-06-02 18:45:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:45:01  
</div>
                            <div class=\"file-name\">Line 17</div>
                <div class=\"issue-item\">[2025-06-02 18:45:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:45:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 18</div>
                <div class=\"issue-item\">[2025-06-02 18:45:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:45:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 19</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:46:02  
</div>
                            <div class=\"file-name\">Line 20</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:46:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 21</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:46:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 22</div>
                <div class=\"issue-item\">[2025-06-02 18:47:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:47:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 23</div>
                <div class=\"issue-item\">[2025-06-02 18:47:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:47:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 24</div>
                <div class=\"issue-item\">[2025-06-02 18:48:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:48:01  
</div>
                            <div class=\"file-name\">Line 25</div>
                <div class=\"issue-item\">[2025-06-02 18:48:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:48:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 26</div>
                <div class=\"issue-item\">[2025-06-02 18:48:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:48:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 27</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:49:02  
</div>
                            <div class=\"file-name\">Line 28</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:49:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 29</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:49:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 30</div>
                <div class=\"issue-item\">[2025-06-02 18:50:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:50:02  
</div>
                            <div class=\"file-name\">Line 31</div>
                <div class=\"issue-item\">[2025-06-02 18:50:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:50:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 32</div>
                <div class=\"issue-item\">[2025-06-02 18:50:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:50:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 33</div>
                <div class=\"issue-item\">[2025-06-02 18:51:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:51:01  
</div>
                            <div class=\"file-name\">Line 34</div>
                <div class=\"issue-item\">[2025-06-02 18:51:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:51:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 35</div>
                <div class=\"issue-item\">[2025-06-02 18:51:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:51:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 36</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:52:02  
</div>
                            <div class=\"file-name\">Line 37</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:52:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 38</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:52:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 39</div>
                <div class=\"issue-item\">[2025-06-02 18:53:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:53:02  
</div>
                            <div class=\"file-name\">Line 40</div>
                <div class=\"issue-item\">[2025-06-02 18:53:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:53:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 41</div>
                <div class=\"issue-item\">[2025-06-02 18:53:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:53:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 42</div>
                <div class=\"issue-item\">[2025-06-02 18:54:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:54:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 43</div>
                <div class=\"issue-item\">[2025-06-02 18:54:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:54:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 44</div>
                <div class=\"issue-item\">[2025-06-02 18:55:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:55:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 45</div>
                <div class=\"issue-item\">[2025-06-02 18:55:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:55:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 46</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:56:02  
</div>
                            <div class=\"file-name\">Line 47</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:56:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 48</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:56:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 49</div>
                <div class=\"issue-item\">[2025-06-02 18:57:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:57:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 50</div>
                <div class=\"issue-item\">[2025-06-02 18:57:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:57:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 51</div>
                <div class=\"issue-item\">[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:58:02  
</div>
                            <div class=\"file-name\">Line 52</div>
                <div class=\"issue-item\">[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:58:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 53</div>
                <div class=\"issue-item\">[2025-06-02 18:58:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:58:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 54</div>
                <div class=\"issue-item\">[2025-06-02 18:59:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:59:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 55</div>
                <div class=\"issue-item\">[2025-06-02 18:59:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:59:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 56</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:00:02  
</div>
                            <div class=\"file-name\">Line 57</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:00:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                                <h2>laravel.log (5 issues)</h2>
            
                            <div class=\"file-name\">Line 16</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:00:02  
</div>
                            <div class=\"file-name\">Line 19</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:00:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 20</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: The stream or file &quot;/var/www/html/itqanalquran/storage/logs/command_failures.log&quot; could not be opened in append mode: Failed to open stream: Permission denied
</div>
                            <div class=\"file-name\">Line 70</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: Call to undefined method Modules\\JobSeeker\\Services\\JobService::notifySubscribers() {&quot;exception&quot;:&quot;[object] (Error(code: 0): Call to undefined method Modules\\\\JobSeeker\\\\Services\\\\JobService::notifySubscribers() at /var/www/html/itqanalquran/Modules/General/Console/Commands/QueueNotifyJobSubscribers.php:51)
</div>
                            <div class=\"file-name\">Line 87</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:00:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            
        <div class=\"meta\">
            <p>This is an automated message from the Itqan AlQuran log monitoring system. Please investigate these issues promptly.</p>
            <p>To run a manual log check, run the command: <code>php artisan logs:monitor --date=2025-06-02</code></p>
        </div>
    </div>
</body>
</html> , 0, Invalid address:  (From): , 2025-06-02 19:00:02, 2025-06-02 19:00:02))","code":0,"trace":"Enable debug mode to see trace","step":"pre_smtp_setup"} 
[2025-06-02 19:00:02] production.ERROR: EmailService: Failed to log email to database {"correlation_id":"email_683d8432df38e8.85161486","error":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'from' cannot be null (Connection: mysql, SQL: insert into `email_logs` (`from`, `to`, `subject`, `view`, `body`, `success`, `error_message`, `updated_at`, `created_at`) values (?, <EMAIL>, [ALERT] System Log Issues Detected on 2025-06-02 - Itqan AlQuran, emails.log_alert, <!DOCTYPE html>
<html>
<head>
    <meta charset=\"utf-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Log Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f7f7f7;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border: 1px solid #e4e4e4;
            border-radius: 5px;
        }
        .header {
            background-color: #cc2f2f;
            padding: 20px;
            color: white;
            text-align: center;
            margin: -20px -20px 20px -20px;
            border-radius: 5px 5px 0 0;
        }
        h1 {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        h2 {
            font-size: 18px;
            margin-top: 20px;
            color: #444;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .summary {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .issue-item {
            margin-bottom: 8px;
            padding: 10px;
            background-color: #fff4f4;
            border-left: 4px solid #cc2f2f;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            font-size: 13px;
        }
        .file-name {
            background-color: #3b3b3b;
            color: white;
            padding: 8px 12px;
            border-radius: 3px;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
            display: inline-block;
        }
        .meta {
            color: #666;
            margin-top: 25px;
            font-size: 12px;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        .date-badge {
            display: inline-block;
            background-color: #f57f17;
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            margin-right: 10px;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class=\"container\">
        <div class=\"header\">
            <h1>🚨 System Log Alert</h1>
        </div>
        
        <div class=\"summary\">
            <span class=\"date-badge\">2025-06-02</span>
            <strong>System:</strong> Itqan AlQuran<br>
            <strong>Server:</strong> ubuntu-8gb-hel1-1<br>
            <strong>Detected At:</strong> 2025-06-02 19:00:02<br>
            <strong>Total Issues Found:</strong> 62<br>
            <strong>Report Date:</strong> 2025-06-02
        </div>
        
        <p>The log monitoring system has detected potential issues in the application logs for <strong>2025-06-02</strong> that require your attention:</p>
        
                    <h2>command_failures.log (57 issues)</h2>
            
                            <div class=\"file-name\">Line 1</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:39:02  
</div>
                            <div class=\"file-name\">Line 2</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:39:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 3</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:39:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 4</div>
                <div class=\"issue-item\">[2025-06-02 18:40:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:40:02  
</div>
                            <div class=\"file-name\">Line 5</div>
                <div class=\"issue-item\">[2025-06-02 18:40:03] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:40:03 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 6</div>
                <div class=\"issue-item\">[2025-06-02 18:40:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:40:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 7</div>
                <div class=\"issue-item\">[2025-06-02 18:41:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:41:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 8</div>
                <div class=\"issue-item\">[2025-06-02 18:41:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:41:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 9</div>
                <div class=\"issue-item\">[2025-06-02 18:42:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:42:02  
</div>
                            <div class=\"file-name\">Line 10</div>
                <div class=\"issue-item\">[2025-06-02 18:42:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:42:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 11</div>
                <div class=\"issue-item\">[2025-06-02 18:42:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:42:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 12</div>
                <div class=\"issue-item\">[2025-06-02 18:43:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:43:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 13</div>
                <div class=\"issue-item\">[2025-06-02 18:43:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:43:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 14</div>
                <div class=\"issue-item\">[2025-06-02 18:44:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:44:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 15</div>
                <div class=\"issue-item\">[2025-06-02 18:44:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:44:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 16</div>
                <div class=\"issue-item\">[2025-06-02 18:45:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:45:01  
</div>
                            <div class=\"file-name\">Line 17</div>
                <div class=\"issue-item\">[2025-06-02 18:45:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:45:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 18</div>
                <div class=\"issue-item\">[2025-06-02 18:45:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:45:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 19</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:46:02  
</div>
                            <div class=\"file-name\">Line 20</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:46:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 21</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:46:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 22</div>
                <div class=\"issue-item\">[2025-06-02 18:47:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:47:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 23</div>
                <div class=\"issue-item\">[2025-06-02 18:47:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:47:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 24</div>
                <div class=\"issue-item\">[2025-06-02 18:48:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:48:01  
</div>
                            <div class=\"file-name\">Line 25</div>
                <div class=\"issue-item\">[2025-06-02 18:48:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:48:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 26</div>
                <div class=\"issue-item\">[2025-06-02 18:48:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:48:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 27</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:49:02  
</div>
                            <div class=\"file-name\">Line 28</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:49:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 29</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:49:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 30</div>
                <div class=\"issue-item\">[2025-06-02 18:50:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:50:02  
</div>
                            <div class=\"file-name\">Line 31</div>
                <div class=\"issue-item\">[2025-06-02 18:50:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:50:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 32</div>
                <div class=\"issue-item\">[2025-06-02 18:50:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:50:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 33</div>
                <div class=\"issue-item\">[2025-06-02 18:51:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:51:01  
</div>
                            <div class=\"file-name\">Line 34</div>
                <div class=\"issue-item\">[2025-06-02 18:51:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:51:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 35</div>
                <div class=\"issue-item\">[2025-06-02 18:51:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:51:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 36</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:52:02  
</div>
                            <div class=\"file-name\">Line 37</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:52:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 38</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:52:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 39</div>
                <div class=\"issue-item\">[2025-06-02 18:53:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:53:02  
</div>
                            <div class=\"file-name\">Line 40</div>
                <div class=\"issue-item\">[2025-06-02 18:53:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:53:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 41</div>
                <div class=\"issue-item\">[2025-06-02 18:53:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:53:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 42</div>
                <div class=\"issue-item\">[2025-06-02 18:54:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:54:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 43</div>
                <div class=\"issue-item\">[2025-06-02 18:54:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:54:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 44</div>
                <div class=\"issue-item\">[2025-06-02 18:55:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:55:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 45</div>
                <div class=\"issue-item\">[2025-06-02 18:55:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:55:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 46</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:56:02  
</div>
                            <div class=\"file-name\">Line 47</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:56:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 48</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:56:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 49</div>
                <div class=\"issue-item\">[2025-06-02 18:57:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:57:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 50</div>
                <div class=\"issue-item\">[2025-06-02 18:57:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:57:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 51</div>
                <div class=\"issue-item\">[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:58:02  
</div>
                            <div class=\"file-name\">Line 52</div>
                <div class=\"issue-item\">[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:58:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 53</div>
                <div class=\"issue-item\">[2025-06-02 18:58:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:58:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 54</div>
                <div class=\"issue-item\">[2025-06-02 18:59:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:59:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 55</div>
                <div class=\"issue-item\">[2025-06-02 18:59:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:59:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 56</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:00:02  
</div>
                            <div class=\"file-name\">Line 57</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:00:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                                <h2>laravel.log (5 issues)</h2>
            
                            <div class=\"file-name\">Line 16</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:00:02  
</div>
                            <div class=\"file-name\">Line 19</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:00:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 20</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: The stream or file &quot;/var/www/html/itqanalquran/storage/logs/command_failures.log&quot; could not be opened in append mode: Failed to open stream: Permission denied
</div>
                            <div class=\"file-name\">Line 70</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: Call to undefined method Modules\\JobSeeker\\Services\\JobService::notifySubscribers() {&quot;exception&quot;:&quot;[object] (Error(code: 0): Call to undefined method Modules\\\\JobSeeker\\\\Services\\\\JobService::notifySubscribers() at /var/www/html/itqanalquran/Modules/General/Console/Commands/QueueNotifyJobSubscribers.php:51)
</div>
                            <div class=\"file-name\">Line 87</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:00:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            
        <div class=\"meta\">
            <p>This is an automated message from the Itqan AlQuran log monitoring system. Please investigate these issues promptly.</p>
            <p>To run a manual log check, run the command: <code>php artisan logs:monitor --date=2025-06-02</code></p>
        </div>
    </div>
</body>
</html> , 0, EmailService: Failed to log email to database: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'from' cannot be null (Connection: mysql, SQL: insert into `email_logs` (`from`, `to`, `subject`, `view`, `body`, `success`, `error_message`, `updated_at`, `created_at`) values (?, <EMAIL>, [ALERT] System Log Issues Detected on 2025-06-02 - Itqan AlQuran, emails.log_alert, <!DOCTYPE html>
<html>
<head>
    <meta charset=\"utf-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Log Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f7f7f7;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border: 1px solid #e4e4e4;
            border-radius: 5px;
        }
        .header {
            background-color: #cc2f2f;
            padding: 20px;
            color: white;
            text-align: center;
            margin: -20px -20px 20px -20px;
            border-radius: 5px 5px 0 0;
        }
        h1 {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        h2 {
            font-size: 18px;
            margin-top: 20px;
            color: #444;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .summary {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .issue-item {
            margin-bottom: 8px;
            padding: 10px;
            background-color: #fff4f4;
            border-left: 4px solid #cc2f2f;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            font-size: 13px;
        }
        .file-name {
            background-color: #3b3b3b;
            color: white;
            padding: 8px 12px;
            border-radius: 3px;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
            display: inline-block;
        }
        .meta {
            color: #666;
            margin-top: 25px;
            font-size: 12px;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        .date-badge {
            display: inline-block;
            background-color: #f57f17;
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            margin-right: 10px;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class=\"container\">
        <div class=\"header\">
            <h1>🚨 System Log Alert</h1>
        </div>
        
        <div class=\"summary\">
            <span class=\"date-badge\">2025-06-02</span>
            <strong>System:</strong> Itqan AlQuran<br>
            <strong>Server:</strong> ubuntu-8gb-hel1-1<br>
            <strong>Detected At:</strong> 2025-06-02 19:00:02<br>
            <strong>Total Issues Found:</strong> 62<br>
            <strong>Report Date:</strong> 2025-06-02
        </div>
        
        <p>The log monitoring system has detected potential issues in the application logs for <strong>2025-06-02</strong> that require your attention:</p>
        
                    <h2>command_failures.log (57 issues)</h2>
            
                            <div class=\"file-name\">Line 1</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:39:02  
</div>
                            <div class=\"file-name\">Line 2</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:39:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 3</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:39:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 4</div>
                <div class=\"issue-item\">[2025-06-02 18:40:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:40:02  
</div>
                            <div class=\"file-name\">Line 5</div>
                <div class=\"issue-item\">[2025-06-02 18:40:03] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:40:03 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 6</div>
                <div class=\"issue-item\">[2025-06-02 18:40:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:40:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 7</div>
                <div class=\"issue-item\">[2025-06-02 18:41:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:41:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 8</div>
                <div class=\"issue-item\">[2025-06-02 18:41:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:41:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 9</div>
                <div class=\"issue-item\">[2025-06-02 18:42:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:42:02  
</div>
                            <div class=\"file-name\">Line 10</div>
                <div class=\"issue-item\">[2025-06-02 18:42:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:42:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 11</div>
                <div class=\"issue-item\">[2025-06-02 18:42:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:42:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 12</div>
                <div class=\"issue-item\">[2025-06-02 18:43:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:43:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 13</div>
                <div class=\"issue-item\">[2025-06-02 18:43:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:43:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 14</div>
                <div class=\"issue-item\">[2025-06-02 18:44:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:44:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 15</div>
                <div class=\"issue-item\">[2025-06-02 18:44:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:44:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 16</div>
                <div class=\"issue-item\">[2025-06-02 18:45:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:45:01  
</div>
                            <div class=\"file-name\">Line 17</div>
                <div class=\"issue-item\">[2025-06-02 18:45:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:45:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 18</div>
                <div class=\"issue-item\">[2025-06-02 18:45:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:45:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 19</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:46:02  
</div>
                            <div class=\"file-name\">Line 20</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:46:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 21</div>
                <div class=\"issue-item\">[2025-06-02 18:46:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:46:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 22</div>
                <div class=\"issue-item\">[2025-06-02 18:47:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:47:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 23</div>
                <div class=\"issue-item\">[2025-06-02 18:47:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:47:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 24</div>
                <div class=\"issue-item\">[2025-06-02 18:48:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:48:01  
</div>
                            <div class=\"file-name\">Line 25</div>
                <div class=\"issue-item\">[2025-06-02 18:48:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:48:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 26</div>
                <div class=\"issue-item\">[2025-06-02 18:48:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:48:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 27</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:49:02  
</div>
                            <div class=\"file-name\">Line 28</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:49:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 29</div>
                <div class=\"issue-item\">[2025-06-02 18:49:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:49:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 30</div>
                <div class=\"issue-item\">[2025-06-02 18:50:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:50:02  
</div>
                            <div class=\"file-name\">Line 31</div>
                <div class=\"issue-item\">[2025-06-02 18:50:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:50:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 32</div>
                <div class=\"issue-item\">[2025-06-02 18:50:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:50:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 33</div>
                <div class=\"issue-item\">[2025-06-02 18:51:01] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:51:01  
</div>
                            <div class=\"file-name\">Line 34</div>
                <div class=\"issue-item\">[2025-06-02 18:51:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:51:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 35</div>
                <div class=\"issue-item\">[2025-06-02 18:51:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:51:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 36</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:52:02  
</div>
                            <div class=\"file-name\">Line 37</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:52:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 38</div>
                <div class=\"issue-item\">[2025-06-02 18:52:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:52:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 39</div>
                <div class=\"issue-item\">[2025-06-02 18:53:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:53:02  
</div>
                            <div class=\"file-name\">Line 40</div>
                <div class=\"issue-item\">[2025-06-02 18:53:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:53:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 41</div>
                <div class=\"issue-item\">[2025-06-02 18:53:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:53:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 42</div>
                <div class=\"issue-item\">[2025-06-02 18:54:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:54:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 43</div>
                <div class=\"issue-item\">[2025-06-02 18:54:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:54:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 44</div>
                <div class=\"issue-item\">[2025-06-02 18:55:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:55:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 45</div>
                <div class=\"issue-item\">[2025-06-02 18:55:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:55:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 46</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:56:02  
</div>
                            <div class=\"file-name\">Line 47</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:56:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 48</div>
                <div class=\"issue-item\">[2025-06-02 18:56:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:56:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 49</div>
                <div class=\"issue-item\">[2025-06-02 18:57:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:57:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 50</div>
                <div class=\"issue-item\">[2025-06-02 18:57:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:57:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 51</div>
                <div class=\"issue-item\">[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:58:02  
</div>
                            <div class=\"file-name\">Line 52</div>
                <div class=\"issue-item\">[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:58:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 53</div>
                <div class=\"issue-item\">[2025-06-02 18:58:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:58:03 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 54</div>
                <div class=\"issue-item\">[2025-06-02 18:59:01] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:59:01 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            <div class=\"file-name\">Line 55</div>
                <div class=\"issue-item\">[2025-06-02 18:59:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:59:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 56</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:00:02  
</div>
                            <div class=\"file-name\">Line 57</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:00:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                                <h2>laravel.log (5 issues)</h2>
            
                            <div class=\"file-name\">Line 16</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 19:00:02  
</div>
                            <div class=\"file-name\">Line 19</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 19:00:02 | Error: Notification process failed to complete  
</div>
                            <div class=\"file-name\">Line 20</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: The stream or file &quot;/var/www/html/itqanalquran/storage/logs/command_failures.log&quot; could not be opened in append mode: Failed to open stream: Permission denied
</div>
                            <div class=\"file-name\">Line 70</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: Call to undefined method Modules\\JobSeeker\\Services\\JobService::notifySubscribers() {&quot;exception&quot;:&quot;[object] (Error(code: 0): Call to undefined method Modules\\\\JobSeeker\\\\Services\\\\JobService::notifySubscribers() at /var/www/html/itqanalquran/Modules/General/Console/Commands/QueueNotifyJobSubscribers.php:51)
</div>
                            <div class=\"file-name\">Line 87</div>
                <div class=\"issue-item\">[2025-06-02 19:00:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 19:00:02 | Error: Queue dispatch may be locked or failed to complete  
</div>
                            
        <div class=\"meta\">
            <p>This is an automated message from the Itqan AlQuran log monitoring system. Please investigate these issues promptly.</p>
            <p>To run a manual log check, run the command: <code>php artisan logs:monitor --date=2025-06-02</code></p>
        </div>
    </div>
</body>
</html> , 0, Invalid address:  (From): , 2025-06-02 19:00:02, 2025-06-02 19:00:02)), 2025-06-02 19:00:02, 2025-06-02 19:00:02))"} 
[2025-06-02 19:00:02] production.ERROR: EmailService: Failed to log email failure {"correlation_id":"email_683d8432df38e8.85161486","error":"EmailService: Failed to log email to database: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'from' cannot be null (Connection: mysql, SQL: insert into `email_logs` (`from`, `to`, `subject`, `view`, `body`, `success`, `error_message`, `updated_at`, `created_at`) values (?, <EMAIL>, [ALERT] System Log Issues Detected on 2025-06-02 - Itqan AlQuran, emails.log_alert, <!DOCTYPE html>
<html>
<head>
    <meta charset=\"utf-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Log Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f7f7f7;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border: 1px solid #e4e4e4;
            border-radius: 5px;
        }
        .header {
            background-color: #cc2f2f;
            padding: 20px;
            color: white;
            text-align: center;
            margin: -20px -20px 20px -20px;
            border-radius: 5px 5px 0 0;
        }
        h1 {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        h2 {
            font-size: 18px;
            margin-top: 20px;
            color: #444;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .summary {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .issue-item {
            margin-bottom: 8px;
            padding: 10px;
            background-color: #fff4f4;
            border-left: 4px solid #cc2f2f;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            font-size: 13px;
        }
        .file-name {
            background-color: #3b3b3b;
            color: white;
            padding: 8px 12px;
            border-radius: 3px;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
            display: inline-block;
        }
        .meta {
            color: #666;
            margin-top: 25px;
            font-size: 12px;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        .date-badge {
            display: inline-block;
            background-color: #f57f17;
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            margin-right: 10px;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class=\"container\">
        <div class=\"header\">
            <h1>🚨 System Log Alert</h1>
        </div>
        
        <div class=\"summary\">
            <span class=\"date-badge\">2025-06-02</span>
            <strong>System:</strong> Itqan AlQuran<br>
            <strong>Server:</strong> ubuntu-8gb-hel1-1<br>
            <strong>Detected At:</strong> 2025-06-02 19:00:02<br>
            <strong>Total Issues Found:</strong> 62<br>
            <strong>Report Date:</strong> 2025-06-02
        </div>
        
        <p>The log monitoring system has detected potential issues in the application logs for <strong>2025-06-02</strong> that require your attention:</p>
        
                    <h2>command_failures.log (57 issues)</h2>
            
                            <div class=\"file-name\">Line 1</div>
                <div class=\"issue-item\">[2025-06-02 18:39:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:39:02  
</div>

<?php

namespace Modules\Site\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Setting;
use Illuminate\Http\Request;
use Session;
use App\Organization;

class SettingsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $organization = Organization::findOrFail(config('organization_id'));


        $theme = $organization->theme ?? 'wajeha';


        $theme_settings = require(module_path('Site').'/Resources/views/templates/'.$theme.'/config.php');
        // dd($theme_settings);

        $current_settings = config('settings');
    //     $keyword = $request->get('search');
    //     $perPage = 25;

    //     if (!empty($keyword)) {
    //         $settings = Setting::where('name', 'LIKE', "%$keyword%")
				// ->orWhere('value', 'LIKE', "%$keyword%")
				// ->orWhere('organization_id', 'LIKE', "%$keyword%")
				// ->paginate($perPage);
    //     } else {
    //         $settings = Setting::paginate($perPage);
    //     }

        return view('site::settings.index', compact('theme_settings', 'current_settings' , 'organization'));
    }

   /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request)
    {

        
        $requestData = $request->all();

        if (isset($request->theme)) {
            foreach ($request->theme as $name => $value) {
                $setting = Setting::where('name', '=' , $name)
                                ->where('organization_id', '=' , config('organization_id'))->first();
                if($setting){
                    $setting->value = $value;
                }else{
                    $setting = new Setting();
                    $setting->name = $name;
                    $setting->value = $value;
                    $setting->organization_id =  config('organization_id');
                }
                $setting->save();

            }
        }
        
        if (isset($request->website)) {
            foreach ($request->website as $name => $value) {
                $setting = Setting::where('name', '=' , $name)
                                ->where('organization_id', '=' , config('organization_id'))->first();
                if($setting){
                    $setting->value = $value;
                }else{
                    $setting = new Setting();
                    $setting->name = $name;
                    $setting->value = $value;
                    $setting->organization_id =  config('organization_id');
                }
                $setting->save();

            }
        }
        
        if (isset($request->links)) {
            foreach ($request->links as $name => $value) {
                $setting = Setting::where('name', '=' , $name)
                                ->where('organization_id', '=' , config('organization_id'))->first();
                if($setting){
                    $setting->value = $value;
                }else{
                    $setting = new Setting();
                    $setting->name = $name;
                    $setting->value = $value;
                    $setting->organization_id =  config('organization_id');
                }
                $setting->save();

            }
        }

        if (isset($request->organization)) {
            $organization = Organization::findOrFail(config('organization_id'));

            $this->validate($request, [
                'organization.name' => 'required|max:255',
                'organization.theme' => 'required',
                'organization.logo' => 'required',
                'organization.languages' => 'required',
            ]);

            $organization->name = $request->organization['name'];
            if(isset($request->organization['organization_domain'])){
                $organization->domain = $request->organization['organization_domain'];
            };
            $organization->theme = $request->organization['theme'];
            $organization->logo = $request->organization['logo'];

            $languages = implode('|', $request->organization['languages']);
            $organization->languages = $languages;

            $organization->save();
        }
        

        
        flash('Setting updated!');

        return redirect(route('settings'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Setting::destroy($id);

        Session::flash('flash_message', 'Setting deleted!');

        return redirect('settings');
    }
}

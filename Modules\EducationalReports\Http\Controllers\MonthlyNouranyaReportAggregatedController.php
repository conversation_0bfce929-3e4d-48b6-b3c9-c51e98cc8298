<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Http\Controllers;

use App\Classes;
use App\Http\Controllers\Controller;
use App\Student;
use App\StudentNouranyaPlan;
use App\StudentNouranyaReport;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;

/**
 * Aggregated monthly Nouranya report across MULTIPLE classes.
 *
 * Inputs: classId (array or comma-separated list), classDate (e.g., "Jan 2025").
 * Output: DataTables JSON with per-student rows and two extra fields: class_id, class_name.
 * Grouping by class can be done on the frontend via class_id/class_name.
 */
final class MonthlyNouranyaReportAggregatedController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        try {
            $classIds = $this->parseClassIds($request->input('classId'));
            $monthYear = (string) $request->input('classDate');

            if (empty($classIds) || $monthYear === '') {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            $date = Carbon::createFromFormat('M Y', $monthYear);
            $month = (int) $date->month;
            $year  = (int) $date->year;

            $rows = [];
            foreach ($classIds as $classId) {
                $class = Classes::find($classId);
                if (!$class) {
                    continue;
                }

                $students = Student::whereHas('joint_classes', function ($query) use ($classId) {
                        $query->where('class_id', $classId);
                    })
                    ->where('status', 'active')
                    ->orderBy('full_name', 'asc')
                    ->get();

                foreach ($students as $index => $student) {
                    $attendanceData     = $this->calculateAttendance($student->id, $classId, $month, $year);
                    $achievementPercent = $this->calculateAchievement($student->id, $classId, $month, $year);
                    $entryData          = $this->getEntryData($student->id, $classId, $month, $year);
                    $monthlyPlan        = $this->getMonthlyPlan($student->id, $month, $year);
                    $monthlyAchievement = $this->getMonthlyAchievement($student->id, $classId, $month, $year);

                    $rows[] = [
                        'DT_RowIndex'           => $index + 1,
                        'class_id'              => (int) $classId,
                        'class_name'            => $class->class_code ?? (string) $class->name,
                        'student_id'            => $student->id,
                        'student'               => $this->formatStudentName($student),
                        'entry1'                => $entryData['entry1'],
                        'entry2'                => $entryData['entry2'],
                        'entry3'                => $entryData['entry3'],
                        'entry4'                => $entryData['entry4'],
                        'monthlyPlan'           => $monthlyPlan,
                        'monthlyAchievement'    => $monthlyAchievement,
                        'attendancePercentage'  => $this->formatProgressBarWithPopup($attendanceData['percentage'], '#1fff0f', $attendanceData['details']),
                        'achievementPercentage' => $this->formatProgressBarWithPopup($achievementPercent, '#1fff0f', $this->getAchievementDetails($student->id, $classId, $month, $year)),
                    ];
                }
            }

            return DataTables::of($rows)
                ->rawColumns(['student', 'attendancePercentage', 'achievementPercentage', 'entry1', 'entry2', 'entry3', 'entry4', 'monthlyPlan', 'monthlyAchievement'])
                ->toJson();
        } catch (\Throwable $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /** @return int[] */
    private function parseClassIds($input): array
    {
        if (is_array($input)) {
            $ids = $input;
        } elseif (is_string($input)) {
            $ids = array_filter(array_map('trim', explode(',', $input)));
        } else {
            $ids = [];
        }

        $ids = array_values(array_unique(array_map('intval', $ids)));
        return array_values(array_filter($ids, static fn (int $id) => $id > 0));
    }

    private function getMonthlyPlan(int $studentId, int $month, int $year): string
    {
        $plan = StudentNouranyaPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year) {
                $query->whereYear('start_date', $year)
                    ->whereMonth('start_date', $month);
            })
            ->orWhere(function ($query) use ($month, $year) {
                $query->whereYear('created_at', $year)
                    ->whereMonth('created_at', $month);
            })
            ->first();

        if (!$plan) {
            return '—';
        }

        $planContent = '';
        if (!empty($plan->from_lesson) && !empty($plan->from_lesson_line_number) && !empty($plan->to_lesson) && !empty($plan->to_lesson_line_number)) {
            $planContent .= "<div>{$plan->from_lesson}.{$plan->from_lesson_line_number} - {$plan->to_lesson}.{$plan->to_lesson_line_number}</div>";
        }
        if (!empty($plan->talaqqi_from_lesson) && !empty($plan->talaqqi_to_lesson)) {
            $planContent .= "<div>Talaqqi: {$plan->talaqqi_from_lesson} - {$plan->talaqqi_to_lesson}</div>";
        }
        if (!empty($plan->talqeen_from_lesson) && !empty($plan->talqeen_to_lesson)) {
            $talqeenLine = !empty($plan->talqeen_from_line_number) ? ":{$plan->talqeen_from_line_number}" : '';
            $planContent .= "<div>Talqeen: {$plan->talqeen_from_lesson}{$talqeenLine} - {$plan->talqeen_to_lesson}</div>";
        }

        return $planContent ?: '—';
    }

    private function getEntryData(int $studentId, int $classId, int $month, int $year): array
    {
        $firstReport = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('created_at', 'asc')
            ->first();

        $latestReport = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($firstReport && $latestReport) {
            return [
                'entry1' => $firstReport->from_lesson ?? '—',
                'entry2' => $firstReport->from_lesson_line_number ?? '—',
                'entry3' => $latestReport->to_lesson ?? '—',
                'entry4' => $latestReport->to_lesson_line_number ?? '—',
            ];
        }

        $plan = StudentNouranyaPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year) {
                $query->whereYear('start_date', $year)
                      ->whereMonth('start_date', $month);
            })
            ->first();

        if ($plan) {
            return [
                'entry1' => $plan->from_lesson ?? '—',
                'entry2' => $plan->from_lesson_line_number ?? '—',
                'entry3' => $plan->to_lesson ?? '—',
                'entry4' => $plan->to_lesson_line_number ?? '—',
            ];
        }

        return ['entry1' => '—', 'entry2' => '—', 'entry3' => '—', 'entry4' => '—'];
    }

    private function getMonthlyAchievement(int $studentId, int $classId, int $month, int $year): string
    {
        $reports = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereNotNull('from_lesson')
            ->whereNotNull('to_lesson')
            ->get();

        if ($reports->isEmpty()) {
            return '—';
        }

        $uniqueLessons = collect();
        foreach ($reports as $report) {
            $from = (int) $report->from_lesson; $to = (int) $report->to_lesson;
            if ($from > $to) { $tmp = $from; $from = $to; $to = $tmp; }
            for ($i = $from; $i <= $to; $i++) { $uniqueLessons->push($i); }
        }

        $count = $uniqueLessons->unique()->count();
        $first = $reports->sortBy('created_at')->first();
        $last  = $reports->sortByDesc('created_at')->first();

        $overallStart = (int) $first->from_lesson;
        $overallEnd   = (int) $last->to_lesson;
        if ($overallStart > $overallEnd) { $t = $overallStart; $overallStart = $overallEnd; $overallEnd = $t; }

        $rangeText = $overallStart === $overallEnd ? "Lesson {$overallStart}" : "Lessons {$overallStart} - {$overallEnd}";
        return $count > 0 ? "<strong>{$count} lessons</strong><br><small class='text-muted'>{$rangeText}</small>" : '—';
    }

    private function calculateAttendance(int $studentId, int $classId, int $month, int $year): array
    {
        $class = Classes::find($classId);
        if (!$class || !$class->timetable) {
            return ['percentage' => 0.0, 'details' => [
                'total_scheduled' => 0, 'attended' => 0, 'late' => 0, 'absent' => 0, 'error' => 'No class timetable found']];
        }

        $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
        if ($totalClasses <= 0) {
            return ['percentage' => 0.0, 'details' => [
                'total_scheduled' => 0, 'attended' => 0, 'late' => 0, 'absent' => 0, 'error' => 'No scheduled classes for this month']];
        }

        $attendanceStats = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->selectRaw('
                COUNT(CASE WHEN attendance_id = 2 THEN 1 END) as on_time,
                COUNT(CASE WHEN attendance_id = 1 THEN 1 END) as late,
                COUNT(CASE WHEN attendance_id = 3 THEN 1 END) as absent,
                COUNT(*) as total_reports')
            ->first();

        $onTime = (int) ($attendanceStats->on_time ?? 0);
        $late   = (int) ($attendanceStats->late ?? 0);
        $absent = (int) ($attendanceStats->absent ?? 0);
        $attended = $onTime + $late;
        $percentage = $totalClasses > 0 ? min(100.0, ($attended / $totalClasses) * 100) : 0.0;

        return [
            'percentage' => $percentage,
            'details' => [
                'total_scheduled' => $totalClasses,
                'attended' => $attended,
                'on_time' => $onTime,
                'late' => $late,
                'absent' => $absent,
                'total_reports' => (int) ($attendanceStats->total_reports ?? 0),
                'calculation' => "({$attended} attended ÷ {$totalClasses} scheduled) × 100 = " . round($percentage, 2) . "%",
            ],
        ];
    }

    private function calculateAchievement(int $studentId, int $classId, int $month, int $year): float
    {
        $plan = StudentNouranyaPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year) {
                $query->whereYear('start_date', $year)
                      ->whereMonth('start_date', $month);
            })
            ->first();

        if (!$plan) {
            return 0.0;
        }

        $reports = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get();

        if ($reports->isEmpty()) {
            return 0.0;
        }

        $totalReports = $reports->count();
        $expectedReports = 20; // business assumption
        return min(100.0, ($totalReports / $expectedReports) * 100);
    }

    private function formatStudentName(Student $student): string
    {
        $studentName = ucfirst($student->full_name);
        $studentProfileUrl = route('students.show', ['id' => $student->user_id]);
        return '<a style="color:#b4eeb0; padding: 2px; display: inline-block; transition: all 0.3s ease;" data-id="'.$student->id.'" class="section class-link" target="_blank" href="' . $studentProfileUrl . '">' . $studentName . '</a>';
    }

    private function formatProgressBarWithPopup(float $percentage, string $color, array $details): string
    {
        $result = round($percentage, 2);
        $detailsJson = htmlspecialchars(json_encode($details), ENT_QUOTES, 'UTF-8');
        $progressClass = isset($details['total_reports']) ? 'achievement-progress' : 'attendance-progress';
        $dataAttribute = isset($details['total_reports']) ? 'data-achievement-details' : 'data-attendance-details';

        return '<div class="progress ' . $progressClass . '" style="position: relative; cursor: pointer;" '
            . 'data-toggle="tooltip" data-placement="top" data-html="true" '
            . $dataAttribute . '="' . $detailsJson . '">' .
            '<div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" '
            . 'style="width: ' . $result . '%; background-color: ' . $color . ';" '
            . 'aria-valuenow="' . $result . '" aria-valuemin="0" aria-valuemax="100">' .
            $result . '%</div></div>';
    }

    private function getAchievementDetails(int $studentId, int $classId, int $month, int $year): array
    {
        $reports = StudentNouranyaReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get();

        $plan = StudentNouranyaPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year) {
                $query->whereYear('start_date', $year)
                      ->whereMonth('start_date', $month);
            })
            ->first();

        $totalReports = $reports->count();
        $completedReports = $reports->where('from_lesson', '!=', null)->where('to_lesson', '!=', null)->count();

        $uniqueLessons = collect();
        foreach ($reports as $report) {
            if ($report->from_lesson && $report->to_lesson) {
                $from = (int) $report->from_lesson; $to = (int) $report->to_lesson;
                if ($from > $to) { $tmp = $from; $from = $to; $to = $tmp; }
                for ($lesson = $from; $lesson <= $to; $lesson++) { $uniqueLessons->push($lesson); }
            }
        }
        $uniqueLessonCount = $uniqueLessons->unique()->count();
        $expectedReports = 20;
        $achievementPercentage = $totalReports > 0 ? min(100.0, ($totalReports / $expectedReports) * 100) : 0.0;

        $minLesson = $uniqueLessons->unique()->min();
        $maxLesson = $uniqueLessons->unique()->max();
        $lessonRange = ($minLesson && $maxLesson)
            ? ($minLesson === $maxLesson ? "Lesson {$minLesson}" : "Lessons {$minLesson} - {$maxLesson}")
            : 'No lessons recorded';

        $planInfo = 'No plan found';
        if ($plan) {
            $planStart = $plan->from_lesson ?? '—';
            $planEnd   = $plan->to_lesson ?? '—';
            $planInfo  = "Planned: Lessons {$planStart} - {$planEnd}";
        }

        return [
            'total_reports' => $totalReports,
            'completed_reports' => $completedReports,
            'incomplete_reports' => $totalReports - $completedReports,
            'unique_lessons' => $uniqueLessonCount,
            'lesson_range' => $lessonRange,
            'expected_reports' => $expectedReports,
            'achievement_percentage' => round($achievementPercentage, 2),
            'plan_info' => $planInfo,
            'calculation' => "({$totalReports} reports ÷ {$expectedReports} expected) × 100 = " . round($achievementPercentage, 2) . "%",
        ];
    }
}



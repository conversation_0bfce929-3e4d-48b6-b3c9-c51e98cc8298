<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use <PERSON><PERSON><PERSON>\JobSeeker\Entities\JobSeeker;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\JobSeekerAccountLockout;
use Mo<PERSON><PERSON>\JobSeeker\Notifications\AccountLockedNotification;
use Mo<PERSON><PERSON>\JobSeeker\Notifications\AccountUnlockedNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

/**
 * Account Lockout Service for JobSeeker
 * 
 * Manages account lockouts after failed login attempts
 * with notifications and recovery options.
 */
final class AccountLockoutService
{
    /**
     * Maximum failed login attempts before lockout
     */
    private const MAX_FAILED_ATTEMPTS = 5;

    /**
     * Lockout duration in minutes
     */
    private const LOCKOUT_DURATION_MINUTES = 30;

    /**
     * Failed attempts reset duration in minutes
     */
    private const FAILED_ATTEMPTS_RESET_MINUTES = 60;

    /**
     * Escalation thresholds for repeated lockouts
     */
    private const LOCKOUT_ESCALATION = [
        1 => 30,    // First lockout: 30 minutes
        2 => 60,    // Second lockout: 1 hour
        3 => 120,   // Third lockout: 2 hours
        4 => 240,   // Fourth lockout: 4 hours
        5 => 480,   // Fifth lockout: 8 hours
    ];

    /**
     * Maximum failed attempts per IP address per hour
     */
    private const MAX_IP_ATTEMPTS_PER_HOUR = 20;

    /**
     * IP block duration in minutes
     */
    private const IP_BLOCK_DURATION_MINUTES = 60;

    /**
     * Check if an IP address is currently blocked
     *
     * @param string $ipAddress
     * @return bool
     */
    public function isIpBlocked(string $ipAddress): bool
    {
        try {
            $ipBlockKey = "ip_blocked:" . hash('sha256', $ipAddress);
            $blockInfo = Cache::get($ipBlockKey);
            
            if ($blockInfo && isset($blockInfo['blocked_until'])) {
                return Carbon::parse($blockInfo['blocked_until'])->isFuture();
            }
            
            return false;

        } catch (\Exception $e) {
            Log::error('Error checking IP block status', [
                'ip_address' => $ipAddress,
                'error' => $e->getMessage()
            ]);
            
            // Fail safe - assume not blocked if there's an error
            return false;
        }
    }

    /**
     * Get IP block details
     *
     * @param string $ipAddress
     * @return array|null
     */
    public function getIpBlockDetails(string $ipAddress): ?array
    {
        try {
            $ipBlockKey = "ip_blocked:" . hash('sha256', $ipAddress);
            $blockInfo = Cache::get($ipBlockKey);
            
            if ($blockInfo && isset($blockInfo['blocked_until'])) {
                $blockedUntil = Carbon::parse($blockInfo['blocked_until']);
                
                if ($blockedUntil->isFuture()) {
                    return [
                        'blocked_until' => $blockedUntil,
                        'reason' => $blockInfo['reason'] ?? 'excessive_failed_attempts',
                        'failed_attempts' => $blockInfo['failed_attempts'] ?? 0,
                        'minutes_remaining' => $blockedUntil->diffInMinutes(now()),
                        'blocked_at' => Carbon::parse($blockInfo['blocked_at'])
                    ];
                }
            }
            
            return null;

        } catch (\Exception $e) {
            Log::error('Error getting IP block details', [
                'ip_address' => $ipAddress,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Check if an account is currently locked
     *
     * @param JobSeeker $jobSeeker
     * @return bool
     */
    public function isAccountLocked(JobSeeker $jobSeeker): bool
    {
        $lockout = JobSeekerAccountLockout::where('job_seeker_id', $jobSeeker->id)
            ->orWhere('email', $jobSeeker->email)
            ->first();

        if (!$lockout || !$lockout->is_locked) {
            return false;
        }

        // Check if lockout has expired
        if ($lockout->locked_until && now()->isAfter($lockout->locked_until)) {
            $this->unlockAccount($lockout);
            return false;
        }

        return true;
    }

    /**
     * Get the lockout details for an account
     *
     * @param JobSeeker $jobSeeker
     * @return array|null
     */
    public function getLockoutDetails(JobSeeker $jobSeeker): ?array
    {
        if (!$jobSeeker) {
            return null;
        }

        $lockout = JobSeekerAccountLockout::where('job_seeker_id', $jobSeeker->id)
            ->orWhere('email', $jobSeeker->email)
            ->first();

        if (!$lockout || !$lockout->is_locked) {
            return null;
        }

        $minutesRemaining = 0;
        if ($lockout->locked_until) {
            $minutesRemaining = max(0, now()->diffInMinutes($lockout->locked_until, false));
        }

        return [
            'locked_until' => $lockout->locked_until,
            'minutes_remaining' => ceil($minutesRemaining),
            'failed_attempts' => $lockout->failed_attempts,
            'lock_reason' => $lockout->lock_reason,
        ];
    }

    /**
     * Record a failed login attempt and handle lockout if necessary
     *
     * @param string $email
     * @param string $ipAddress
     * @param string $userAgent
     * @return array
     */
    public function recordFailedAttempt(string $email, string $ipAddress, string $userAgent): array
    {
        try {
            // Check if IP is blocked first
            if ($this->isIpBlocked($ipAddress)) {
                $ipBlockDetails = $this->getIpBlockDetails($ipAddress);
                
                Log::warning('Login attempt from blocked IP address', [
                    'email' => $email,
                    'ip_address' => $ipAddress,
                    'block_details' => $ipBlockDetails
                ]);
                
                return [
                    'locked' => true,
                    'ip_blocked' => true,
                    'ip_block_details' => $ipBlockDetails
                ];
            }
            
            $jobSeeker = JobSeeker::where('email', $email)->first();
            
            if (!$jobSeeker) {
                // Still record failed attempt for non-existent emails to prevent enumeration
                $ipBlockResult = $this->recordAnonymousFailedAttempt($email, $ipAddress, $userAgent);
                
                if ($ipBlockResult['ip_blocked']) {
                    return $ipBlockResult;
                }
                
                return ['locked' => false, 'attempts_remaining' => self::MAX_FAILED_ATTEMPTS - 1];
            }

            // Check if already locked
            if ($this->isAccountLocked($jobSeeker)) {
                $lockoutDetails = $this->getLockoutDetails($jobSeeker);
                return [
                    'locked' => true,
                    'lockout_details' => $lockoutDetails
                ];
            }

            // Get or create lockout record
            $lockout = JobSeekerAccountLockout::firstOrCreate(
                ['email' => $email],
                [
                    'job_seeker_id' => $jobSeeker->id,
                    'failed_attempts' => 0,
                    'first_failed_at' => now(),
                    'last_failed_at' => now(),
                    'last_ip_address' => $ipAddress,
                    'user_agent' => $userAgent,
                ]
            );

            // Reset attempts if enough time has passed
            if ($this->shouldResetFailedAttempts($lockout)) {
                $lockout->update([
                    'failed_attempts' => 1,
                    'first_failed_at' => now(),
                    'last_failed_at' => now(),
                    'last_ip_address' => $ipAddress,
                    'user_agent' => $userAgent,
                    'locked_until' => null,
                    'is_locked' => false,
                ]);
            } else {
                // Increment failed attempts
                $lockout->increment('failed_attempts');
                $lockout->update([
                    'last_failed_at' => now(),
                    'last_ip_address' => $ipAddress,
                    'user_agent' => $userAgent,
                ]);
            }

            $attemptsRemaining = max(0, self::MAX_FAILED_ATTEMPTS - $lockout->failed_attempts);
            $locked = false;

            // Lock account if max attempts reached
            if ($lockout->failed_attempts >= self::MAX_FAILED_ATTEMPTS && !$lockout->is_locked) {
                $lockedUntil = now()->addMinutes(self::LOCKOUT_DURATION_MINUTES);
                
                $lockout->update([
                    'is_locked' => true,
                    'locked_until' => $lockedUntil,
                    'lock_reason' => 'Too many failed login attempts',
                ]);

                $locked = true;

                Log::warning('JobSeeker account locked due to failed attempts', [
                    'email' => $email,
                    'job_seeker_id' => $jobSeeker->id,
                    'failed_attempts' => $lockout->failed_attempts,
                    'locked_until' => $lockedUntil,
                    'ip_address' => $ipAddress,
                ]);
            }

            return [
                'locked' => $locked,
                'attempts_remaining' => $attemptsRemaining,
                'lockout_details' => $locked ? $this->getLockoutDetails($jobSeeker) : null,
            ];

        } catch (\Exception $e) {
            Log::error('Error recording failed login attempt', [
                'email' => $email,
                'ip_address' => $ipAddress,
                'error' => $e->getMessage()
            ]);

            return ['locked' => false, 'attempts_remaining' => 0];
        }
    }

    /**
     * Clear failed login attempts (called on successful login)
     *
     * @param JobSeeker $jobSeeker
     * @return void
     */
    public function clearFailedAttempts(JobSeeker $jobSeeker): void
    {
        try {
            JobSeekerAccountLockout::where('job_seeker_id', $jobSeeker->id)
                ->orWhere('email', $jobSeeker->email)
                ->update([
                    'failed_attempts' => 0,
                    'is_locked' => false,
                    'locked_until' => null,
                    'first_failed_at' => null,
                    'last_failed_at' => null,
                ]);

            Log::info('Failed attempts cleared for JobSeeker', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
            ]);

        } catch (\Exception $e) {
            Log::error('Error clearing failed attempts', [
                'job_seeker_id' => $jobSeeker->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Lock an account due to failed login attempts
     *
     * @param JobSeeker $jobSeeker
     * @param int $failedAttempts
     * @param string $ipAddress
     * @param string $userAgent
     * @return array
     */
    private function lockAccount(JobSeeker $jobSeeker, int $failedAttempts, string $ipAddress, string $userAgent): array
    {
        try {
            // Calculate lockout duration based on previous lockouts
            $previousLockouts = JobSeekerAccountLockout::where('job_seeker_id', $jobSeeker->id)
                ->where('created_at', '>', now()->subDays(30))
                ->count();

            $lockoutCount = $previousLockouts + 1;
            $lockoutMinutes = self::LOCKOUT_ESCALATION[$lockoutCount] ?? self::LOCKOUT_ESCALATION[5];
            $lockedUntil = now()->addMinutes($lockoutMinutes);

            // Create lockout record
            $lockout = JobSeekerAccountLockout::create([
                'job_seeker_id' => $jobSeeker->id,
                'reason' => 'failed_login_attempts',
                'failed_attempts' => $failedAttempts,
                'lockout_count' => $lockoutCount,
                'locked_until' => $lockedUntil,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'is_active' => true
            ]);

            // Clear the failed attempts cache
            Cache::forget("failed_attempts:{$jobSeeker->id}");

            // Send notification email
            $this->sendLockoutNotification($jobSeeker, $lockout);

            Log::warning('Account locked due to failed login attempts', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
                'failed_attempts' => $failedAttempts,
                'lockout_count' => $lockoutCount,
                'locked_until' => $lockedUntil,
                'lockout_minutes' => $lockoutMinutes,
                'ip_address' => $ipAddress
            ]);

            return [
                'locked' => true,
                'lockout_details' => [
                    'locked_until' => $lockedUntil,
                    'reason' => 'failed_login_attempts',
                    'failed_attempts' => $failedAttempts,
                    'lockout_count' => $lockoutCount,
                    'minutes_remaining' => $lockoutMinutes,
                    'can_request_unlock' => false // Cannot request immediate unlock for failed attempts
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error locking account', [
                'job_seeker_id' => $jobSeeker->id,
                'error' => $e->getMessage()
            ]);

            return ['locked' => false, 'attempts_remaining' => 0];
        }
    }

    /**
     * Manually unlock an account (admin function)
     *
     * @param JobSeeker $jobSeeker
     * @param string $unlockedBy
     * @param string $reason
     * @return bool
     */
    public function unlockAccount(JobSeeker $jobSeeker, string $unlockedBy, string $reason = 'manual_unlock'): bool
    {
        try {
            $activeLockouts = JobSeekerAccountLockout::where('job_seeker_id', $jobSeeker->id)
                ->where('is_active', true)
                ->get();

            foreach ($activeLockouts as $lockout) {
                $lockout->update([
                    'is_active' => false,
                    'unlocked_at' => now(),
                    'unlocked_by' => $unlockedBy,
                    'unlock_reason' => $reason
                ]);
            }

            // Clear failed attempts cache
            Cache::forget("failed_attempts:{$jobSeeker->id}");

            // Send unlock notification
            $this->sendUnlockNotification($jobSeeker, $reason);

            Log::info('Account manually unlocked', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
                'unlocked_by' => $unlockedBy,
                'reason' => $reason,
                'lockouts_cleared' => $activeLockouts->count()
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Error unlocking account', [
                'job_seeker_id' => $jobSeeker->id,
                'unlocked_by' => $unlockedBy,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Check if automatic unlock is available
     *
     * @param JobSeeker $jobSeeker
     * @return bool
     */
    public function canAutoUnlock(JobSeeker $jobSeeker): bool
    {
        $lockout = JobSeekerAccountLockout::where('job_seeker_id', $jobSeeker->id)
            ->orWhere('email', $jobSeeker->email)
            ->first();

        if (!$lockout || !$lockout->is_locked) {
            return true;
        }

        if ($lockout->locked_until && now()->isAfter($lockout->locked_until)) {
            $this->unlockAccount($lockout);
            return true;
        }

        return false;
    }

    /**
     * Get account security statistics
     *
     * @param JobSeeker $jobSeeker
     * @return array
     */
    public function getSecurityStats(JobSeeker $jobSeeker): array
    {
        try {
            $lockoutHistory = JobSeekerAccountLockout::where('job_seeker_id', $jobSeeker->id)
                ->orderBy('created_at', 'desc')
                ->get();

            $currentFailedAttempts = Cache::get("failed_attempts:{$jobSeeker->id}", 0);
            $isCurrentlyLocked = $this->isAccountLocked($jobSeeker);

            return [
                'is_locked' => $isCurrentlyLocked,
                'current_failed_attempts' => $currentFailedAttempts,
                'attempts_remaining' => max(0, self::MAX_FAILED_ATTEMPTS - $currentFailedAttempts),
                'total_lockouts' => $lockoutHistory->count(),
                'lockouts_last_30_days' => $lockoutHistory->where('created_at', '>', now()->subDays(30))->count(),
                'last_lockout' => $lockoutHistory->first()?->created_at,
                'lockout_details' => $isCurrentlyLocked ? $this->getLockoutDetails($jobSeeker) : null
            ];

        } catch (\Exception $e) {
            Log::error('Error getting security statistics', [
                'job_seeker_id' => $jobSeeker->id,
                'error' => $e->getMessage()
            ]);

            return [
                'is_locked' => false,
                'current_failed_attempts' => 0,
                'attempts_remaining' => self::MAX_FAILED_ATTEMPTS,
                'total_lockouts' => 0,
                'lockouts_last_30_days' => 0,
                'last_lockout' => null,
                'lockout_details' => null
            ];
        }
    }

    /**
     * Record failed attempt for non-existent email (prevent enumeration)
     *
     * @param string $email
     * @param string $ipAddress
     * @param string $userAgent
     * @return array
     */
    private function recordAnonymousFailedAttempt(string $email, string $ipAddress, string $userAgent): array
    {
        Log::warning('Failed login attempt for non-existent email', [
            'email' => $email,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent
        ]);

        // Rate limit by IP for non-existent emails
        $ipCacheKey = "failed_attempts_ip:" . hash('sha256', $ipAddress);
        $ipFailedAttempts = Cache::get($ipCacheKey, 0) + 1;
        Cache::put($ipCacheKey, $ipFailedAttempts, now()->addHour());

        if ($ipFailedAttempts > self::MAX_IP_ATTEMPTS_PER_HOUR) {
            // Block the IP address
            return $this->blockIpAddress($ipAddress, $ipFailedAttempts, 'excessive_failed_attempts');
        }

        return ['ip_blocked' => false];
    }

    /**
     * Block an IP address due to excessive failed attempts
     *
     * @param string $ipAddress
     * @param int $failedAttempts
     * @param string $reason
     * @return array
     */
    private function blockIpAddress(string $ipAddress, int $failedAttempts, string $reason): array
    {
        try {
            $blockedUntil = now()->addMinutes(self::IP_BLOCK_DURATION_MINUTES);
            $ipBlockKey = "ip_blocked:" . hash('sha256', $ipAddress);
            
            $blockInfo = [
                'blocked_until' => $blockedUntil->toISOString(),
                'blocked_at' => now()->toISOString(),
                'reason' => $reason,
                'failed_attempts' => $failedAttempts
            ];
            
            Cache::put($ipBlockKey, $blockInfo, $blockedUntil);

            Log::warning('IP address blocked due to excessive failed attempts', [
                'ip_address' => $ipAddress,
                'failed_attempts' => $failedAttempts,
                'blocked_until' => $blockedUntil,
                'reason' => $reason
            ]);

            return [
                'locked' => true,
                'ip_blocked' => true,
                'ip_block_details' => [
                    'blocked_until' => $blockedUntil,
                    'reason' => $reason,
                    'failed_attempts' => $failedAttempts,
                    'minutes_remaining' => self::IP_BLOCK_DURATION_MINUTES,
                    'blocked_at' => now()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error blocking IP address', [
                'ip_address' => $ipAddress,
                'error' => $e->getMessage()
            ]);

            return ['ip_blocked' => false];
        }
    }

    /**
     * Send lockout notification email
     *
     * @param JobSeeker $jobSeeker
     * @param JobSeekerAccountLockout $lockout
     * @return void
     */
    private function sendLockoutNotification(JobSeeker $jobSeeker, JobSeekerAccountLockout $lockout): void
    {
        try {
            // Send email notification about lockout
            Mail::to($jobSeeker->email)->send(new AccountLockedNotification($jobSeeker, $lockout));

        } catch (\Exception $e) {
            Log::error('Failed to send lockout notification', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send unlock notification email
     *
     * @param JobSeeker $jobSeeker
     * @param string $reason
     * @return void
     */
    private function sendUnlockNotification(JobSeeker $jobSeeker, string $reason): void
    {
        try {
            // Send email notification about unlock
            Mail::to($jobSeeker->email)->send(new AccountUnlockedNotification($jobSeeker, $reason));

        } catch (\Exception $e) {
            Log::error('Failed to send unlock notification', [
                'job_seeker_id' => $jobSeeker->id,
                'email' => $jobSeeker->email,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Check if user can request manual unlock
     *
     * @param JobSeekerAccountLockout $lockout
     * @return bool
     */
    private function canRequestUnlock(JobSeekerAccountLockout $lockout): bool
    {
        // Allow unlock requests only after 60 minutes have elapsed since lockout creation
        return now()->diffInMinutes($lockout->created_at) > 60;
    }

    /**
     * Check if failed attempts should be reset
     */
    private function shouldResetFailedAttempts(JobSeekerAccountLockout $lockout): bool
    {
        if (!$lockout->first_failed_at) {
            return true;
        }

        return now()->diffInMinutes($lockout->first_failed_at) >= self::FAILED_ATTEMPTS_RESET_MINUTES;
    }
} 
<?php

namespace Modules\Education\Http\Requests;

use App\Rules\CheckDependentEmail;
use App\Rules\CheckIfStringIsArabic;
use App\Rules\CheckIfStringIsEnglish;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rule;
use function PHPUnit\Framework\returnArgument;

class StudentRegistrationByEmployeeRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {


        


        session()->forget(['program','center','classes']);
        if (request()->filled('program')){
            session()->put(['program_id' => request()->get('program')]);

        }

        if (request()->filled('center')){
            session()->put(['center_id' => request()->get('center')]);

        }
        if (request()->filled('classes')){
            session()->put(['class_id' => request()->get('classes')]);

        }


       

       
        return [
            'full_name'          => ['required', 'string', 'max:255', 'different:email', new CheckIfStringIsEnglish()],
            'nationality'        => ['required', Rule::notIn(['no'])],
            'displayname'        => 'required|string|max:255|different:email',
            'email'              => 'required|string|email|max:255|unique:users,email',
            'password'           => 'required|string|min:8|confirmed',
            'student_mobile'     => 'sometimes|required|phone:AUTO',
            'national_id_number' => 'sometimes|required',
            'identity_number'    => 'sometimes|required|unique:students',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg,webp|max:2048',
            'program'            => 'required',
            'center'             => 'required',
            'classes'            => 'required',
            'gender'             => 'sometimes|required',
            'full_name_trans'    => ['nullable', new CheckIfStringIsArabic()],
            'date_of_birth'      => 'sometimes|required|date|before:' . Carbon::now()->subYears(3),
            'document_title_1'   => 'required_with:document_file_1',
            'document_title_2'   => 'required_with:document_file_2',
            'document_title_3'   => 'required_with:document_file_3',
            'document_title_4'   => 'required_with:document_file_4',
            'document_file_1'    => 'sometimes|required_with:document_title_1|mimes:jpg,png,gif,pdf|max:2000',
            'document_file_2'    => 'sometimes|required_with:document_title_2|mimes:jpg,png,gif,pdf|max:2000',
            'document_file_3'    => 'sometimes|required_with:document_title_3|mimes:jpg,png,gif,pdf|max:2000',
            'document_file_4'    => 'sometimes|required_with:document_title_4|mimes:jpg,png,gif,pdf|max:2000',
        ];





    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {

        return [
            'student_mobile' => 'The :attribute field contains an invalid number.',
            'date_of_birth.before' => 'A student should be at least 3 years old',
//            'file.required' => 'Please upload your image',
            'student_mobile.phone' => 'prefixed with a + sign, e.g. +60 ....',
        ];
    }
}

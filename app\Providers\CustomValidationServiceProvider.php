<?php

namespace App\Providers;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;

class CustomValidationServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap the application services.
     *
     * @return void
     */
    public function boot()
    {
        \Validator::extend('recaptcha', function ($attribute, $value, $parameters, $validator) {
            $url = 'https://www.google.com/recaptcha/api/siteverify';
            $remoteip = $_SERVER['REMOTE_ADDR'];
            $data = [
                'secret' => config('services.recaptcha.secret'),
                'response' => $value,
                'remoteip' => $remoteip
            ];
            $options = [
                'http' => [
                    'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                    'method' => 'POST',
                    'content' => http_build_query($data)
                ]
            ];
            $context = stream_context_create($options);

            $result = file_get_contents($url, false, $context);
            $resultJson = json_decode($result);
//        return $resultJson;




            if ($resultJson->success != true) {
                return false;
            }
            return true;
        });

        Validator::replacer('recaptcha', function($message, $attribute, $rule, $parameters) {
            return str_replace($message, "Recaptcha Error! Try again", $message);
        });






    }

    /**
     * Register the application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}

<?php

declare(strict_types=1);

namespace Modules\Education\Http\Controllers;

use App\Classes;
use App\Http\Controllers\Controller;
use App\PublicHoliday;
use App\StudentNouranyaReport;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\JsonResponse;

final class NouranyaMonthYearController extends Controller
{
    public function getMonthYears(int $studentId, int $classId): JsonResponse
    {
        try {
            $monthYears = StudentNouranyaReport::where('student_id', $studentId)
                ->where('class_id', $classId)
                ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year')
                ->distinct()
                ->orderBy('year', 'desc')
                ->orderBy('month', 'desc')
                ->get();

            $class = Classes::find($classId);
            if (!$class || !$class->timetable) {
                return response()->json($monthYears->map(function ($my) {
                    return [
                        'month_year' => Carbon::createFromDate($my->year, $my->month, 1)->format('F Y'),
                        'days' => 'N/A'
                    ];
                }));
            }

            $classDays = $class->timetable->getScheduledDaysOfWeek();

            $publicHolidays = [];
            foreach (PublicHoliday::all() as $holiday) {
                if (isset($holiday->year) && isset($holiday->month__no) && isset($holiday->day)) {
                    $holidayDate = Carbon::createFromDate($holiday->year, $holiday->month__no, $holiday->day);
                    $publicHolidays[] = $holidayDate->format('Y-m-d');
                }
            }
            $publicHolidays = array_unique($publicHolidays);

            $formattedMonths = [];
            foreach ($monthYears as $my) {
                $month = (int)$my->month;
                $year  = (int)$my->year;
                $date = Carbon::createFromDate($year, $month, 1);
                
                $scheduledDays = 0;
                $period = CarbonPeriod::create($date->copy()->startOfMonth(), $date->copy()->endOfMonth());
                foreach ($period as $currentDate) {
                    if (in_array($currentDate->dayOfWeekIso, $classDays) && !in_array($currentDate->format('Y-m-d'), $publicHolidays)) {
                        $scheduledDays++;
                    }
                }

                $formattedMonths[] = [
                    'month_year' => $date->format('F Y'),
                    'days'       => $scheduledDays,
                ];
            }

            return response()->json($formattedMonths);

        } catch (\Exception $e) {
            \Log::error('Error in NouranyaMonthYearController: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json(['error' => 'An error occurred while loading month-year data.' . $e->getMessage()], 500);
        }
    }
} 
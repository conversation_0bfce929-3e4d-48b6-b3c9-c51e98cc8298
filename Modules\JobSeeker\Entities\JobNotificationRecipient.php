<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Notifications\Notifiable;

final class JobNotificationRecipient extends Model
{
    use Notifiable;
    
    protected $table = 'job_notification_recipients';
    
    protected $fillable = [
        'setup_id',
        'recipient_email_id',
        'name',
        'email',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the notification setup that owns this recipient.
     */
    public function setup(): BelongsTo
    {
        return $this->belongsTo(JobNotificationSetup::class, 'setup_id');
    }

    /**
     * Get the global recipient email record that owns this recipient.
     */
    public function recipientEmail(): BelongsTo
    {
        return $this->belongsTo(JobNotificationRecipientEmail::class, 'recipient_email_id');
    }

    /**
     * Get the job seeker that owns this recipient through the setup.
     */
    public function jobSeeker(): HasOneThrough
    {
        return $this->hasOneThrough(
            JobSeeker::class,
            JobNotificationSetup::class,
            'id', // Foreign key on job_notification_setups table
            'id', // Foreign key on job_seekers table
            'setup_id', // Local key on job_notification_recipients table
            'job_seeker_id' // Local key on job_notification_setups table
        );
    }
    
    /**
     * Route notifications for the mail channel.
     */
    public function routeNotificationForMail(): string
    {
        return $this->email;
    }
} 
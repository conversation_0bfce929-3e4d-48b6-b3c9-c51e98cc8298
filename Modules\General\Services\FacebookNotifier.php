<?php

namespace Modules\General\Services;

use Illuminate\Support\Facades\Log;
use NotificationChannels\Facebook\FacebookMessage;
use NotificationChannels\Facebook\Components\Button;

class FacebookNotifier
{
    /**
     * @var string
     */
    protected $recipientId;

    /**
     * FacebookNotifier constructor.
     */
    public function __construct()
    {
        // Load config from file as fallback
        $configFile = __DIR__ . '/../../Config/config.php';
        $config = file_exists($configFile) ? include $configFile : [];
        
        // Get configuration values with fallbacks
        $this->recipientId = config('services.facebook.recipient_id', $config['facebook']['recipient_id'] ?? null);
    }

    /**
     * Send a message via Facebook Messenger
     *
     * @param string $message
     * @return bool
     */
    public function sendMessage($message)
    {
        try {
            Log::info("Sending Facebook message to recipient: {$this->recipientId}");
            
            $fbMessage = FacebookMessage::create($message)
                ->to($this->recipientId);
            
            // Here we would typically use <PERSON><PERSON>'s notification system to send this
            // For now, we'll just log it
            Log::info("Facebook message prepared", [
                'recipient' => $this->recipientId,
                'message' => $message
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send Facebook message: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }
    }

    /**
     * Send a message with buttons
     *
     * @param string $message
     * @param array $buttons Array of ['title' => string, 'url' => string] items
     * @return bool
     */
    public function sendMessageWithButtons($message, array $buttons)
    {
        try {
            $fbButtons = collect($buttons)->map(function($button) {
                return Button::create($button['title'], $button['url'])->isTypeWebUrl();
            })->toArray();

            $fbMessage = FacebookMessage::create($message)
                ->to($this->recipientId)
                ->buttons($fbButtons);

            Log::info("Facebook message with buttons prepared", [
                'recipient' => $this->recipientId,
                'message' => $message,
                'buttons' => $buttons
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send Facebook message with buttons: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }
    }

    /**
     * Set the recipient ID
     * 
     * @param string $recipientId
     * @return self
     */
    public function setRecipientId($recipientId)
    {
        $this->recipientId = $recipientId;
        return $this;
    }

    /**
     * Get the recipient ID
     * 
     * @return string
     */
    public function getRecipientId()
    {
        return $this->recipientId;
    }
} 
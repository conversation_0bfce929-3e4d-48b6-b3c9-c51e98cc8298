<?php

namespace Modules\Admission\Http\Controllers;

use App\BaseSetup;
use App\Center;
use App\Classes;
use App\EmailSetting;
use App\Employee;
use App\GeneralSettings;
use App\Guardian;
use App\Http\Requests\StudentApproveRequest;

use App\Notifications\InterviewInvitationSent;
use App\Organization;
use App\Program;
use App\ProgramTranslation;
use App\Role;
use App\Section;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Admission;
use App\AdmissionInterview;
use App\AdmissionInterviewer;
use App\Student;
use App\StudentHefzPlan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use Luilliarcec\LaravelUsernameGenerator\Facades\Username;
use Modules\Admission\Notifications\DependantStudentAccepted;
use Modules\Admission\Notifications\StudentAccepted;
use Modules\Admission\Notifications\StudentOfffered;
use Modules\ApplicationCenter\Entities\RegistrationSetting;
use Symfony\Component\Mime\Exception\RfcComplianceException;
use function Complex\abs;


class CenterBasedClassesController extends Controller
{


    public function __invoke(Request $request, $center_id)
    {
        $classes = Classes::where('center_id', $center_id)->get();
    return response()->json($classes);

    }

}

<?php

namespace Modules\EducationalReports\Http\Controllers;

use App\BaseSetup;
use App\Center;
use App\Classes;
use App\EmailSetting;
use App\Employee;
use App\GeneralSettings;
use App\Guardian;
use App\Http\Requests\StudentApproveRequest;

use App\Notifications\InterviewInvitationSent;
use App\Organization;
use App\Program;
use App\ProgramTranslation;
use App\Role;
use App\Section;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Admission;
use App\AdmissionInterview;
use App\AdmissionInterviewer;
use App\Student;
use App\StudentHefzPlan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use Luilliarcec\LaravelUsernameGenerator\Facades\Username;
use Modules\Admission\Notifications\DependantStudentAccepted;
use Modules\Admission\Notifications\StudentAccepted;
use Modules\Admission\Notifications\StudentOfffered;
use Modules\ApplicationCenter\Entities\RegistrationSetting;
use Symfony\Component\Mime\Exception\RfcComplianceException;
use function Complex\abs;


class AdmissionController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return view('admission::index');
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view('admission::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        return $request;
    }



    /**
     * Show the specified resource.
     * @return Response
     */
    public function show()
    {
        return view('admission::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit()
    {
        return view('admission::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @return Response
     */
    public function update(Request $request)
    {



        try {
            DB::beginTransaction();

            $admission = Admission::findOrFail($request->admission_id);


            //   return $request->all();
            $this->validate($request, [
                'center_id' => 'required',
                'class_id' => 'required',
                'admission_status' => 'required',
                'program_id' => 'required'
            ]);

            $student = $admission->student;
            // temporaray solution until we find out why there is no user is created
            if(is_null($admission->student->user))
            {

                $user = User::create([
//            'role_id' => $data['role'],
                    'email' => $student->email,
                    'display_name' => $student->full_name,
                    'full_name_trans' => $student->full_name_trans,
                    'username' => $student->id,
                    'full_name' => $student->full_name,
                    'nationality' => $student->nationality,
                    'access_status' => '0',
                    'is_administrator' => 'no',
                    'organization_id' => config('organization_id'),
                    'password' => bcrypt($student->password),
                ]);

                $this->assignDefaultRoles($user, 'member');
                $student->user_id = $user->id;



            }else{
                $user = $admission->student->user;

            }

            // the reason we are reinserting the program values here is that that the education department may change the program a student has applied for
            $admission->center_id = $request->center_id;
            $admission->class_id = $request->class_id;
            $admission->status = $request->admission_status;
            $student->status = $request->admission_status;
            $admission->save();
            $student->save();
            $admission->programs()->sync([$request->program_id]);
            $admission = tap($admission, function ($admission) use ($request){


                if($request->admission_status == 'offered'){

                    $admission->update(
                        [
                            'offer_letter_issuance_date' => Carbon::now()->toDateString()
                        ]);
                }



            });


            // if status is offered, then send a copy of the offer letter to the guardian and the student in PDF format

            $user_info[0]["student_id"] = $admission->student_id;
            $user_info[0]["student_email"] = $admission->student_email;
            $user_info[0]["channel"] = "email";
            $user_info[0]["studentName"] = Student::find($admission->student_id)->full_name;
            $user_info[0]["programTitle"] = Program::find($admission->program_id)->title;

            if ($request->admission_status == 'accepted') {


                // update AdmissionInterview status
                AdmissionInterview::where('admission_id',$admission->id)->update(
                    [
                        'status' => 'interviewed',
                        'confirmed_at' => Carbon::now(),
                        'updated_by' => auth()->user()->id

                ]);


                // assign this role after the payment is made
                $user->assignRole('student');



                //
                $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);

                $systemEmail = EmailSetting::find(1);

                $system_email = $systemEmail->from_email;
                $organization_name = $systemSetting->organization_name;

                $sender['system_email'] = $system_email;
                $sender['organization_name'] = $organization_name;

                if ($admission->creator_role == 'parent') {
                    $user_info[0]["guardian_email"] = $admission->guardian_email;
                }


                // assign a password to a student who has a guardian and send related notification
                if ($student->guardian_id) {
                    $user_info[0]["username"] = User::find($student->user_id)->username;
                    $user_info[0]["password"] = trim($student->student_number);
                    $user->password = bcrypt($student->student_number);
                    $user->notify(new DependantStudentAccepted($user_info, $sender));
                    Toastr::success('Email successfully sent', 'Success');


                }else{
                    $user->notify(new StudentAccepted($user_info, $sender));
                    Toastr::success('Email successfully sent', 'Success');


                }


            }
            $user->save();
            DB::commit();

            if ($request->admission_status == 'offered') {




                // offer letter arrangement starts here
                $offerLetterRawContent = $admission->programs()->pluck('offer')[0];
                $offerLetterRawContent = str_replace("[date]", $admission->offer_letter_issuance_date, $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[address]", 'Dummy Address input by Hashmat', $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[studentName]", Student::find($admission->student_id)->full_name, $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[programName]", "<i>" . Program::find($request->program_id)->title . "</i>", $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[registrationFees]", '<strong>100</strong>', $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[monthlyFeesAmount]", '<strong>200</strong>', $offerLetterRawContent);



                $pdf = app()->make('dompdf.wrapper');
                $pdf->loadHTML(htmlspecialchars_decode($offerLetterRawContent));
                $content = $pdf->download()->getOriginalContent();
                //save the pdf offer letter in storage folder
                \Storage::put("offerLetters/" . $admission->student_id . "-offerLetter.pdf", $content);

//            we are going to attach the offer letter in the Job by making using of the student_is as it is used as the filename in the storage/app/offerLetters directory

                $user->notify(new StudentOfffered($user_info, $sender));



                Toastr::success('Email successfully sent', 'Success');

            }
        } catch (\Exception $e) {

            DB::rollback();

            Toastr::warning($e->getMessage(), 'Danger');
            return redirect()->back();
        }


        return redirect()->back();
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @return Response
     */
    public function approve(StudentApproveRequest $request)
    {




        $admission = Admission::findOrFail($request->admission_id);
        $student = $admission->student;
        $user = $admission->student->user;
        $interviewTime=$request->get('interview_time');//Your date
        $date= strtotime($interviewTime);//Converted to a PHP date (a second count)

//Calculate difference
        $diff=$date-time();//time returns current time in seconds
        $days=floor($diff/(60*60*24));//seconds/minute*minutes/hour*hours/day)
        $hoursToInterview= round(($diff-$days*60*60*24)/(60*60));
        $hoursToInterview = (int)$hoursToInterview;
        // return $admission->programs;

        $waiting_for_interview = false;
        $approved_programs = 0;

        if (!$admission) {
        }





        // get admission programs

        foreach ($admission->programs as $program) {
            // check if the admission aproved
            if ($request->interview[$program->id]['approve']) {
                $approved_programs++;

                // check if an interview is required
                if ($program->require_interview) {
                    // validate request

                    $fields_names = ["interview_time" => "Interview Time"];

//                    $this->validate($request, [
//                        "interview.*.interview_time" => 'required|date_format:Y-m-d H:i',
//                        "interview.*.location" => 'required'
//                    ], $fields_names);

                    $waiting_for_interview = true;


                    //create a new invite record

                    $interview_details = $request->interview[$program->id];

                    $interview_details['admission_id'] = $request->admission_id;
                    $interview_details['program_id'] = $program->id;

                    $interview_details['status'] = 'waiting_for_interview';
                    $user_info[] = array('interviewLocation' => $interview_details['location'], 'interview_duration' => '1 hour',
                        'interviewDateTime' => $interview_details['interview_time'], 'email' => $admission->student_email,
                        'id' => $student->id, 'slug' => 'student', 'interviewCommitteeIds' => $interview_details['committee'],
                        'interviewDetails' => $interview_details['notes'], 'programTitle' => $program->programTranslations->first()->title);

                    $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);

                    $systemEmail = EmailSetting::find(1);

                    $system_email = $systemEmail->from_email;
                    $organization_name = $systemSetting->organization_name;

                    $sender['system_email'] = $system_email;
                    $sender['organization_name'] = $organization_name;

                    if ($admission->creator_role == 'parent') {
                        $user_info[0]["guardian_email"] = $admission->guardian_email;
                    }

                    $user_info[0]["channel"] = "email";
                    $user_info[0]["interviewConfirmationExpiryHours"] = $hoursToInterview;
                    $user_info[0]["admissionId"] = $request->admission_id;
                    $user_info[0]["phone"] = GeneralSettings::where("organization_id", config('organization_id'))->first()->phone;
                    $user_info[0]["studentName"] = Student::find($admission->student_id)->full_name;
                    $user_info[0]["subjectInterviewParticipants"] = implode(", ", Employee::whereIn("id", $user_info[0]['interviewCommitteeIds'])->pluck("name")->toArray()) . " & " . $student->full_name;
                    $user_info[0]["InterviewerEmails"] = Employee::whereIn("id", $user_info[0]['interviewCommitteeIds'])->pluck("email")->toArray();







                    $interview = AdmissionInterview::create($interview_details);

                    // set interview committee

                    $interview_committee = $interview_details['committee'];

                    foreach ($interview_committee as $interviewer) {
                        AdmissionInterviewer::create([
                            'admission_interview_id' => $interview->id,
                            'employee_id' => $interviewer
                        ]);
                    }

                    $user->notify(new InterviewInvitationSent($user_info, $sender));
                    Toastr::success('Email Successfully sent', 'Success');


//                    dispatch(new \App\Jobs\SendStudentInterviewMailJob($user_info, $sender));

                }
            }
        }


        if ($waiting_for_interview) {
            $admission->status = 'waiting_for_interview';
            $student->status = 'waiting_for_interview';
        } else if ($approved_programs == $admission->programs->count()) {
            $admission->status = 'offer';
            $student->status = 'offer';
        } else if ($approved_programs > 0) {
            $admission->status = 'conditional_offer';
        } else {

            $admission->status = 'rejected';
            $admission->rejected_status_note = $request->interview[$program->id]['notes'];
            $student->status = 'rejected';
        }

        $admission->save();
        $student->save();
//        dd($request->interview[$program->id]['notes']);

        // change admission status & student status
        if ($request->ajax()) {
            return response()->json([
                "status" => "success"
            ]);
        }


        return redirect()->back();
    }

    public function setOrientation()
    {
        $request = request();

        $admission = Admission::findOrFail($request->admission_id);

        $this->validate($request, [
            'admission_id' => 'required|numeric',
            'employee_id' => 'required|numeric',
            'orientation_time' => 'required|date',
            'location' => 'required'
        ]);

        $requestData = $request->all();

        $requestData['organization_id'] = config('organization_id');
        $requestData['created_by'] = auth()->user()->id;

        \App\AdmissionOrientation::create($requestData);
        $admission->status = "waiting_for_orientation";

        $admission->save();

        flash('Orintation Session Has Been Set.');

        return redirect()->back();

    }

    public function finalize(Request $request)
    {
        $admission = Admission::findOrFail($request->admission_id);

        $admission->status = 'active';
        $admission->start_date = date('Y-m-d');

        $admission->save();

        $student = Student::findOrFail($admission->student_id);
        $student->status = 'active';
        $student->save();

        if (!\App\ClassStudent::where('class_id', $admission->class_id)->where('student_id', $admission->student_id)->first()) {

            $join_class = new \App\ClassStudent();

            $join_class->student_id = $admission->student_id;
            $join_class->class_id = $admission->class_id;
            $join_class->start_date = date('Y-m-d');

            $join_class->save();
            flash('Student was added to  class successfully.');
        } else {
            flash('Student already has joined the class.');
        }


        return redirect()->back();

    }


    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }

    // new student registration / application form view method
    public function registration()
    {
        try {
            $max_admission_id = Student::max('student_number');
            $max_roll_id = Student::max('roll_no');
            $organizations = Organization::all();
            $classes = Classes::all();
            $programs = ProgramTranslation::where("locale", "en")->get();
            $genders = BaseSetup::where('base_group_id', '=', '1')->get();
            $reg_setting = RegistrationSetting::find(1);
            return view('admission::registration', compact('organizations', 'classes', 'programs', 'genders', 'reg_setting', 'max_admission_id', 'max_roll_id'));
//            return view('studentapplication::registration', compact('organizations', 'classes', 'programs', 'genders', 'reg_setting'));
        } catch (\Exception $e) {


            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    function admissionPic(Request $r)
    {

        try {
            $validator = Validator::make($r->all(), [
                'logo_pic' => 'sometimes|required|mimes:jpg,png|max:40000',

            ]);

            if ($validator->fails()) {
                return response()->json(['error' => 'error'], 201);
            }

            if ($r->hasFile('logo_pic')) {
                $file = $r->file('logo_pic');
                $images = Image::make($file)->insert($file);
                $pathImage = 'public/uploads/student/';
                if (!file_exists($pathImage)) {
                    mkdir($pathImage, 0777, true);
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    $images->save('public/uploads/student/' . $name);
                    $imageName = 'public/uploads/student/' . $name;
                    // $data->staff_photo =  $imageName;
                    Session::put('student_photo', $imageName);
                } else {
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    if (file_exists(Session::get('student_photo'))) {
                        File::delete(Session::get('student_photo'));
                    }
                    $images->save('public/uploads/student/' . $name);
                    $imageName = 'public/uploads/student/' . $name;
                    // $data->student_photo =  $imageName;
                    Session::put('student_photo', $imageName);
                }
            }


            return response()->json('success', 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'error'], 201);
        }
    }

    // Get class for regular school and saas for new student registration
    public function getCenters(Request $request)
    {


        $centers = Center::whereHas("programs", function ($q) use ($request) {
            return $q->where('program_id', $request->id);
        })->get();


        return response()->json([$centers]);
    }

    // Get section for new registration by ajax
    public function getClasses(Request $request)
    {


        $classes = Classes::where('center_id', $request->id)->get();


        return response()->json([$classes]);
    }

    public    function assignDefaultRoles($user, $roleName = null)
    {


//        $default_role_name = Settings::get('default_user_role', 'member');
//        $default_role_name = Settings::get('default_user_role', 'member');


//        $roleName = $default_role_name;


        $user->assignRole('member');
    }


}

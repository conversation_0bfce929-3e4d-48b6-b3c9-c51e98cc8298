<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Admission;
use App\ApiBaseMethod;
use App\Attendance;
use App\BankAccount;
use App\BankAccountType;
use App\BaseSetup;
use App\Center;
use App\CenterEmployee;
use App\CenterTeacher;
use App\CenterTranslation;
use App\Classes;
use App\ClassTeacher;
use App\GeneralSettings;
use App\LeaveRequest;
use App\Notifications\UserStatusChangedToNewApplication;
use App\Notifications\WelcomeMailtoNewEmployeeNotification;
use App\PublicHoliday;
use App\Role;
use App\Employee;

use App\Student;
use App\Document;
use App\WeekDay;
use App\Weekend;
use App\YearCheck;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use DB;
use App\Permission;
use App\EmployeeSalary;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\EmployeeTimetable;
use App\Http\Controllers\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Modules\Admission\Notifications\StudentOfffered;
use Modules\HumanResource\Http\Requests\EmployeeStoreRequest;
use Modules\HumanResource\Http\Requests\EmployeeUpdateRequest;
use PhpOffice\PhpSpreadsheet\Calculation\DateTimeExcel\WorkDay;


class EmployeesBankAccountController extends Controller
{

    /**
     * @var Attendance
     */
    private $attendanceModal;

    public function __construct(Attendance $attendance)
    {

        $this->attendanceModal = $attendance;


    }

    public function index(Request $request)
    {


        if ($request->ajax()) {

            if ($request->filled('roles') or $request->filled('name') or $request->filled('dateRange') or $request->filled('gender') or $request->filled('status') or $request->filled('workMood')) {


                if ($name = $request->name and isset($request->name)) {
                    $bindings['name'] = $name;
                    $requestedName = '%' . $name . '%';

                    $name_condition = " AND (employees.name LIKE " . "'" . $requestedName . "'" . " OR employees.display_name LIKE  " . "'" . $requestedName . "'" . " OR employees.full_name LIKE  " . "'" . $requestedName . "'" . " OR employees.full_name_trans LIKE " . "'" . $requestedName . "'" . ")";

//                    dd($name_condition);
                }

                if ($status = $request->status == "archived" and $request->filled('status')) {
                    $bindings['status'] = $status;

                    $EmployeesTableStatusqueryPart = " AND employees.deleted_at is not NULL";


                } else {
                    $bindings['status'] = $status;
                    $EmployeesTableStatusqueryPart = " AND employees.deleted_at is NULL";

                }
                if ($roles = $request->get("roles")) {


                    $arr = join(",", $request->roles);
                    $bindings['roles'] = $roles;
                    $roles_condition = " AND roles.id IN (" . $arr . ")";

                }
                if ($registration_date = $request->dateRange and isset($request->dateRange)) {
                    $dateSeperator = explode(",", $request->dateRange);
                    $bindings['registration_date'] = $registration_date;

                    $registration_date_condition = " AND date(employees.created_at) between " . "'" . $dateSeperator[0] . "'" . " AND " . "'" . $dateSeperator[1] . "'";

                }
                if ($gender = $request->gender and isset($request->gender)) {
                    $bindings['gender'] = $gender;
                    $gender_date_condition = " AND employees.gender = " . "'" . $request->gender . "'";
                }
                if ($workMode = $request->workMood and isset($request->workMood)) {
                    $bindings['workMood'] = $workMode;
                    $workModeCondition = " AND employees.work_mood = " . "'" . $request->workMood . "'";
                }
                $trxDatatables = DB::select(
                    'SELECT employees.hours_per_month,employees.work_mood,employees.email,employees.deleted_at,employees.status,employees.date_of_birth,employees.id,employees.name,employees.full_name,employees.full_name_trans,employees.display_name,employees.mobile,employees.created_at,employees.gender FROM employees,roles, model_has_roles
                    WHERE employees.id = model_has_roles.model_id
                    AND roles.id = model_has_roles.role_id
                                            ' . $registration_date_condition . '
                                            ' . $roles_condition . '
                                            ' . $EmployeesTableStatusqueryPart . '
                                            ' . $name_condition . '
                                            ' . $workModeCondition . '
                                            ' . $gender_date_condition . 'group by employees.email');
//                ,


                return \Yajra\DataTables\DataTables::of($trxDatatables)
                    ->addColumn('role', function ($employee) use ($request) {

                        $roles = '';
                        $colors = ['orange', 'purple', 'yellow', 'green', 'violet', 'pink', 'brown', 'grey', 'black'];

                        $randomColor = Arr::random($colors);
                        // if no roles filter is selected

                        if ($status = $request->status == "archived" and $request->filled('status')) {


                            $employeeWithRoles = Employee::where('id', $employee->id)->with('roles')->withTrashed()->first();

                        } else {

                            $employeeWithRoles = Employee::where('id', $employee->id)->with('roles')->first();

                        }


                        foreach ($employeeWithRoles->roles as $role) {


                            $roles .= '<a class="ui ' . "$randomColor" . ' label">' . $role->description . '</a>&nbsp;';

                        }

//                        }


                        return $roles;

                    })
                    ->addColumn('login', function ($employee) use ($request) {


                        $impersonationRoute = route('multiAuthImpersonate', ['id' => $employee->id, 'guardName' => 'employee']);
                        return '<span class="badge badge-primary badge-pill"><a
                                style="color: white; padding: 0px; background: transparent "
                                href="' . $impersonationRoute . '">login</a></span>';

                    })
                    ->addColumn('status', function ($employee) use ($request) {
                        if (!is_null($employee->deleted_at)) {

                            // return a toggle


                            $input = '<div class="ui left floated compact segment" style="border-color: red">
                                  <div class="ui fitted toggle checkbox" >
                                    <input data-empId="' . $employee->id . '" data-stname="' . $employee->full_name . '" data-classid="' . $employee->class_id . '" data-center="' . $employee->center . '" data-class="' . $employee->className . '" 
                                    class="archivedStatusCheckbox" id="archivedStatusCheckbox" type="checkbox">
                                    <label></label>
                                  </div>
                                </div>';
                            return $input;
                        } else {

                            return $employee->status;
                        }


                    })
                    ->addColumn('action', function ($row) use ($request) {

                        if (\Auth::user()->can("update employee")) {
                            $stEditRoute = route('employees.edit', $row->id);
                            $viewBtnTitle = "view " . $row->full_name;

                            // different url for archived ( deleted) employees
                            $stShowRoute = is_null($row->deleted_at) == true ?
                                route('employees.show', $row->id)
                                : route('employees.show.archived', $row->id);

                            $viewBtnTitle = "view " . $row->full_name;
                            $editBtnTitle = "edit " . $row->full_name;
                            $archiveBtnTitle = "archive " . $row->full_name;
                            $deleteBtnTitle = "delete " . $row->full_name;
                            $empAttendanceRoute = route('individual.employee.monthly.attendance', $row->id);

                            if ($request->status == "active") {


                                $btns = ' <a href="' . $stShowRoute . '" class="btn btn-success btn-xs" title="' . $viewBtnTitle . '">
                            <span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                              
                               <button  title="' . $archiveBtnTitle . '" class="btn btn-warning btn-xs archiveModalTriggerBtn " id="archiveModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#archive"><span class="glyphicon glyphicon-file"
                               /></button>
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                if ($row->work_mood == 'per_month') {
                                    '<a target="_blank" href="' . $empAttendanceRoute . '" class="btn btn-primary btn-xs" title="Show Attendance"><span class="glyphicon glyphicon-calendar" aria-hidden="true"/></a>';
                                }

                            } else {
                                $btns = ' <a href="' . $stShowRoute . '" class="btn btn-success btn-xs" title="' . $viewBtnTitle . '">
                            <span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                             
                            
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                if ($row->work_mood == 'per_month') {
                                    $btns .= '<a target="_blank"  href="' . $empAttendanceRoute . '" class="btn btn-primary btn-xs" title="Show Attendance"><span class="glyphicon glyphicon-calendar" aria-hidden="true"/></a>';
                                }


                                return $btns;

                            }


                            return $btns;
                        }

                        if (\Auth::user()->can("delete employee")) {
                            $stEditRoute = route('employees.edit', $row->id);
                            $stShowRoute = route('employees.show', $row->id);

                            $viewBtnTitle = "view " . $row->full_name;

                            $archiveBtnTitle = "archive " . $row->full_name;
                            $deleteBtnTitle = "delete " . $row->full_name;


                            if ($request->status == "active") {


                                $btns = '<button  title="' . $archiveBtnTitle . '" class="btn btn-warning btn-xs archiveModalTriggerBtn " id="archiveModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#archive"><span class="glyphicon glyphicon-file"
                               /></button>
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';

                            } else {
                                $btns = ' <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                return $btns;

                            }


                            return $btns;
                        }


//                    })->rawColumns(['login','action'])
                    })->
                    rawColumns(['role', 'action', 'login', 'status'])
                    ->make(true);


            } else {

                $employeeDatatables = Employee::with("roles")->select();


//                dd($employeeDatatables);

                return \Yajra\DataTables\DataTables::of($employeeDatatables)
                    ->addIndexColumn()
                    ->addColumn('role', function (Employee $employee) {
                        $roles = '';
                        $colors = ['orange', 'purple', 'yellow', 'green', 'violet', 'pink', 'brown', 'grey', 'black'];

                        $randomColor = Arr::random($colors);
                        foreach ($employee->roles as $role) {

                            $roles .= '<a class="ui ' . "$randomColor" . ' label">' . $role->description . '</a>&nbsp;';

                        }
                        return $roles;

                    })
                    ->addColumn('login', function ($row) use ($request) {

                        $impersonationRoute = route('multiAuthImpersonate', ['id' => $row->id, 'guardName' => 'employee']);
                        return '<span class="badge badge-primary badge-pill"><a
                                style="color: white; padding: 0px; background: transparent "
                                href="' . $impersonationRoute . '">login</a></span>';

                    })
                    ->addColumn('action', function ($row) use ($request) {

                        if (\Auth::user()->can("update employee")) {
                            $stEditRoute = route('employees.edit', $row->id);
                            $stShowRoute = route('employees.show', $row->id);

                            $viewBtnTitle = "view " . $row->full_name;
                            $editBtnTitle = "edit " . $row->full_name;
                            $archiveBtnTitle = "archive " . $row->full_name;
                            $deleteBtnTitle = "delete " . $row->full_name;
                            $empAttendanceRoute = route('individual.employee.monthly.attendance', $row->id);

                            if ($row->status == "active") {


                                $btns = ' <a href="' . $stShowRoute . '" class="btn btn-success btn-xs" title="' . $viewBtnTitle . '">
                            <span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                              
                               <button  title="' . $archiveBtnTitle . '" class="btn btn-warning btn-xs archiveModalTriggerBtn " id="archiveModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#archive"><span class="glyphicon glyphicon-file"
                               /></button>
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                if ($row->work_mood == 'per_month') {
                                    $btns .= '<a target="_blank" href="' . $empAttendanceRoute . '" class="btn btn-primary btn-xs" title="Show Attendance"><span class="glyphicon glyphicon-calendar" aria-hidden="true" /></a>';
                                }

                            } else {
                                $btns = ' <a href="' . $stShowRoute . '" class="btn btn-success btn-xs" title="' . $viewBtnTitle . '">
                            <span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                             
                            
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                if ($row->work_mood == 'per_month') {
                                    '<a target="_blank" href="' . $empAttendanceRoute . '" class="btn btn-primary btn-xs" title="Show Attendance"><span class="glyphicon glyphicon-calendar" aria-hidden="true" /></a>';
                                }
                                return $btns;

                            }


                            return $btns;
                        }

                        if (\Auth::user()->can("delete employee")) {
                            $stEditRoute = route('employees.edit', $row->id);
                            $stShowRoute = route('employees.show', $row->id);

                            $viewBtnTitle = "view " . $row->full_name;

                            $archiveBtnTitle = "archive " . $row->full_name;
                            $deleteBtnTitle = "delete " . $row->full_name;


                            if ($request->status == "active") {


                                $btns = '<button  title="' . $archiveBtnTitle . '" class="btn btn-warning btn-xs archiveModalTriggerBtn " id="archiveModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#archive"><span class="glyphicon glyphicon-file"
                               /></button>
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';

                            } else {
                                $btns = ' <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                return $btns;

                            }


                            return $btns;
                        }


                    })
//                    })->setRowClass(function ($row) {
//
//
//                        return $$row->deleted_at == NULL or $row->deleted_at == null ? '' : 'warning';
//                    })
//                    ->rawColumns(['login','action'])
                    ->rawColumns(['role', 'action', 'login'])
                    ->make(true);
            }

        }


//        $result = Employee::latest()->paginate();

        return view('humanresource::employees.list', compact('result'));
    }


    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {


        $roles = \App\Role::all()->filter(function ($value, $item) {

            return !empty($value->description);
        })->pluck('description', 'name')->prepend('Select Role ...', '');

        $centers = \App\Center::all()->pluck('location', 'id')->prepend('Select Sup Center...', "");


//        $roles = Role::pluck('description', 'name');

        return view('humanresource::employees.create', compact('roles', 'centers'));
    }


    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public
    function store(EmployeeStoreRequest $request)
    {

        try {
            DB::beginTransaction();
            // Create the employee
            $employee = new Employee;
            $employee->employee_number = $request->employee_number;
            $employee->full_name = $request->full_name;
            $employee->full_name_trans = $request->full_name_trans;
            $employee->name = $request->name;
            $employee->gender = $request->gender;
            $employee->date_of_birth = $request->date_of_birth;
            $employee->email = $request->email;
            $employee->mobile = $request->mobile;
            $employee->address_1 = $request->address_1;
            $employee->address_2 = $request->address_2;
            $employee->address_state = $request->province_state;
            $employee->address_city = $request->city;
            $employee->address_country = $request->country;
            $employee->organization_id = config('organization_id');
            $employee->password = bcrypt($request->get('password'));
            $employee->nationality = $request->nationality;
            $employee->identity_type = $request->identity_type;
            $employee->identity_number = $request->identity;
            $employee->epf_no = $request->epf_no;
            $employee->status = 'active';
            $employee->contract_type = $request->contract_type;

            $employee->start_at = $request->start_at;

            $employee->work_mood = $request->work_mood;
            // if work_mood is task_based, then make hours_per_month null else populate with its value
            if ($request->get('work_mood') == 'per_month') {

                if ($request->get('hours_per_month')) {
                    $employee->hours_per_month = $request->get('hours_per_month');
                }
            } else {
                $employee->hours_per_month = null;

            }

            $employee->marital_status = $request->get('marital_status');

            $employee->save();





            $employee->syncRoles($request->roles);

            $resume = "";
            if ($request->file('resume') != "") {

                $file = $request->file('resume');

                $resumeTitle = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('uploads/staff/resume/', $resumeTitle);
                $resume = 'public/uploads/staff/resume/' . $resumeTitle;

                $document = new Document();
                $document->title = $resumeTitle;
                $document->documentable_id = $employee->id;
                $document->documentable_type = 'App\Employee';
                $document->type = 'stf';
                $document->file = $resume;
                $document->created_by = Auth()->user()->id;
                $document->organization_id = config('organization_id');
                $document->save();


            }


            // for upload Employee Joining Letter
            $joining_letter = "";
            if ($request->file('joining_letter') != "") {
                $file = $request->file('joining_letter');
                $joining_letterTitle = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('uploads/staff/staff_joining_letter/', $joining_letterTitle);
                $joining_letter = 'public/uploads/staff/staff_joining_letter/' . $joining_letterTitle;
                $document = new Document();
                $document->title = $joining_letterTitle;
                $document->documentable_id = $employee->id;
                $document->documentable_type = 'App\Employee';

                $document->type = 'stf';
                $document->file = $joining_letter;
                $document->created_by = Auth()->user()->id;
                $document->organization_id = config('organization_id');
                $document->save();


            }

            // for upload Employee Other Documents
            $other_document = "";
            if ($request->file('other_document') != "") {
                $file = $request->file('other_document');
                $other_documentTitle = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('uploads/staff/others_documents/', $other_documentTitle);
                $other_document = 'public/uploads/staff/others_documents/' . $other_documentTitle;

                $document = new Document();
                $document->title = $other_documentTitle;
                $document->documentable_id = $employee->id;
                $document->documentable_type = 'App\Employee';
                $document->type = 'stf';
                $document->file = $other_document;
                $document->created_by = Auth()->user()->id;
                $document->organization_id = config('organization_id');
                $document->save();


            }

            $employee->resume = $resume;
            $employee->joining_letter = $other_document;
            $employee->other_document = $other_document;
            $employee->save();

            // check if has teacher center input

            if ($request->filled('bank_account_name')) {
                $bankAccountDetails = new BankAccount([

                    'bank_id' => $request->get('bank_name'),
                    'account_name' => strtoupper($request->get('bank_account_name')),
                    'account_number' => $request->get('bank_account_no'),
                    'note' => $request->get('bank_notes'),
                    'created_at' => Carbon::now(),
                    'organization_id' => config('organization_id'),
                ]);

                // save the bank details
                $employee->bankAccounts()->save($bankAccountDetails);

            }
            if ($request->filled('teacher_halaqah')) {


                $employee->teacherCenter()->attach($request->get('teacher_center'));
                $halaqahs = $request->teacher_halaqah;

                $data = array();
                foreach ($halaqahs as $halaqah) {
                    if (!empty($halaqah)) {
                        $data[] = [
                            'class_id' => $halaqah,
                            'employee_id' => $employee->id,
                            'start_date' => $request->get('halaqah_start_date'),
                            'created_at' => Carbon::now(),

                        ];

                    }
                }
                ClassTeacher::insert($data);
//                $employeeClass = ClassTeacher::create([
//                    'employee_id' => $employee->id,
//                    'class_id' => $request->get('teacher_halaqah'),
//                    'start_date' => $request->get('halaqah_start_date'),
//                ]);

            }

            if ($request->has('supervisor_center')) {
                $employee->center()->attach($request->get('supervisor_center'));

            }


            $employee_salary = EmployeeSalary::create([
                'employee_id' => $employee->id,
//                "start_at" => $request->start_at,
                "status" => 'active',
                'basic_salary' => $request->basic_salary,
//                'hours_per_month' => $request->work_mood == 'per_month' ? $request->hours_per_month : $request->hours_per_month*20 /** this condition will be hour, so dont be confused ;) */,
//                'hours_per_day' => $request->work_mood == 'per_hour' ? $request->hours_per_month : null

            ]);
            if ($request->get('work_mood') == 'per_month') {
                if ($request->get('hours_per_month')) {
                    $days = $request->get('days');
                    foreach ($days as $day) {
                        EmployeeTimetable::create([
                            'employee_id' => $employee->id,
                            'day' => $day,
                            'clockin' => '09:00:00',
                            'clockout' => '18:00:00',
                            'break' => '0'
                        ]);

                    }
                }
            }
            if ($request->has('user_notification')) {
                $user_info[0]["empName"] = $request->full_name;
                if ($request->has('login_info')) {
                    $user_info[0]["empEmail"] = $request->email;
                    $user_info[0]["empPassword"] = $request->password;

                }
                $employee->notify(new WelcomeMailtoNewEmployeeNotification($user_info));

            }
            DB::commit();
            \Toastr::success('Operation successful', 'Success');
            return redirect()->route('employees.index');
        } catch (\Exception $e) {
            dd($e->getMessage());
            \Log::error($e);

            DB::rollBack();
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }


    }

    public
    function saveUploadDocument(Request $request)
    {

        try {
            if ($request->file('staff_upload_document') != "" && $request->title != "") {
                $document_photo = "";
                if ($request->file('staff_upload_document') != "") {
                    $file = $request->file('staff_upload_document');
//                    $document_photo = 'staff-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                    $document_photo = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                    $file->move('uploads/staff/document/', $document_photo);
                    $document_photo = 'public/uploads/staff/document/' . $document_photo;
                }

                $document = new Document();
                $document->title = $request->title;
                $document->documentable_id = $request->employee_id;
                $document->type = 'stf';
                $document->file = $document_photo;
                $document->created_by = Auth()->user()->id;
                $document->organization_id = config('organization_id');
                $results = $document->save();
            }

            if ($results) {
                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            \Log::error($e);
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public
    function show(Request $request, Employee $employee)
    {


        $employee = $employee->loadMissing(['teacherCenter', 'classes','bankAccounts.bank','bankAccounts.bankAccountType']);

        // decision made on this date that : any new employeee after this date should have only mon-friday working
        if ($employee->created_at < Carbon::parse('2020-12-01')) {
            $newWorkTimeTable = 0;
        } else {
            $newWorkTimeTable = 1;
        }

        $todayAttendance = $this->attendanceModal->dailyAttendanceReport($employee);
        $weeklyAttendance = $this->attendanceModal->WeeklyAttendanceReport($employee);
        $monthlyAttendance = $this->attendanceModal->monthlyAttendanceReport($employee);

        $employeeDocumentsDetails = Document::where('documentable_id', $employee->id)->where('type', '=', 'stf')->get();
        $empAttMonths = DB::select("SELECT DISTINCT (MONTH(clock)) AS months FROM attendances WHERE employee_id = ? ORDER BY months ASC", [$id]);
        $employeeAttYears = DB::select("SELECT DISTINCT (year(clock)) AS years FROM attendances WHERE employee_id = ? ORDER BY years ASC", [$id]);
        $roles = Role::cursor()->pluck('description', 'name');
        $classes = Classes::where('center_id', $employee->teacherCenter()->first()->id)->get()->pluck('name', 'id');
        $bankAccTypes = BankAccountType::all()->pluck('name', 'id');

        $timetable = EmployeeTimetable::where('employee_id', $employee->id)->orderBy('day_order', 'asc')->pluck('day', 'id');
        $days = EmployeeTimetable::where('employee_id', $employee->id)->orderBy('day_order', 'asc')->get();
        $workDays = WeekDay::pluck('name', 'slug');


        $centers = CenterTranslation::cursor()->where('locale', 'en')->pluck('name', 'name');
        $centers = \App\Center::all()->pluck('location', 'id')->prepend('Select Sup Center...', "");

        $employeeContainsSupervisorRole = $employee->roles->contains(function ($item, $key) {
            return $item->name == 'supervisor_2_';

        });

        $empDayCount = $employee->loadCount('timetable');

        $empDayCount = $empDayCount['timetable_count'];


        $permissions = Permission::all('name', 'id');

        return view('humanresource::employees.show', compact('bankAccTypes','classes', 'days', 'workDays', 'timetable', 'employeeContainsSupervisorRole', 'centers', 'staffDocumentsDetails', 'newWorkTimeTable', 'employee', 'roles', 'permissions', 'empAttMonths', 'employeeAttYears', 'todayAttendance', 'weeklyAttendance', 'monthlyAttendance', 'empDayCount'));
    }


    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        $employee = Employee::find($id);


        $roles = Role::pluck('description', 'name');

        $permissions = Permission::all('name', 'id');

        return view('humanresource::employees.edit', compact('employee', 'roles', 'permissions'));
    }

    public function update(EmployeeUpdateRequest $request, Employee $employee)
    {


        try {
            \Illuminate\Support\Facades\DB::beginTransaction();


            // if request has supervisor role, then sync the record in the cen_emps many to many table
            if (collect($request->get('roles'))->contains('supervisor_' . config('organization_id') . '_')) {


//                $centerIds = CenterTranslation::where('locale', 'en')->whereIn('name', $request->get('supervisorCenters'))->pluck('center_id');
//                $centerIds = \App\Center::whereIn('id', $request->get('supervisorCenters'))->pluck('id');


//                dd($request->get('supervisorCenters'));
                $employee->center()->sync($request->get('supervisorCenters'));


                // handling supervisors
//                $centerIds = CenterEmployee::where('emp_id', auth()->user()->id);
//                if ($centerIds->exists()) {
//                    $centerIds = $centerIds->pluck('cen_id')->toArray();
//                    dd($centerIds);
//                    $employee->center()->detach($centerIds);
//                }


            }

            $existingTeacherClassesIds = $employee->classes()->pluck('classes.id');

            if (collect($request->get('roles'))->contains('teacher' . config('organization_id') . '_')) {

                if (collect($request->get('teacherClasses'))->isNotEmpty()) {


                    // TODO: if an employee is both supervisor and a teacher, solve this case scenario

                    // handling teachers


                    $centerIds = CenterTeacher::where('emp_id', $employee->id);

                    if ($centerIds->exists()) {
                        $centerIds = $centerIds->pluck('cen_id')->toArray();


                        // if teacher role is taken from an employee, remove the centers that are assigned to them.
                        $employee->teacherCenter()->sync($centerIds);


                        // TODO: in the frontend add the class start date feature for each class

                        $startDate = [];

                        for ($i = 0; $i < count($request->get('teacherClasses')); $i++) {
                            $startDate[$i] = date('Y-m-d');


                        }


                        // detach all the relationships
                        $employee->classes()->detach($existingTeacherClassesIds);


                        foreach ($request->get('teacherClasses') as $key => $teacherClass) {


                            $employee->classes()->attach($teacherClass, ['start_date' => $startDate[$key]]);


                        }
//                        $employee->classes()->sync($request->get('teacherClasses'));


                    }


                }

            } else {
                $centerIds = CenterTeacher::where('emp_id', $employee->id)->pluck('cen_id');

                $employee->classes()->detach($existingTeacherClassesIds);
                $employee->teacherCenter()->detach($centerIds);


            }


            // check for password change
            if ($request->get('password')) {
                $employee->password = bcrypt($request->get('password'));
            }


            if ($request->get('start_at')) {
                $employee->start_at = $request->start_at;
            }

            if ($request->get('image')) {
                $employee->image = $request->get('image');
            }
            if ($request->get('employee_number')) {
                $employee->employee_number = $request->get('employee_number');
            }
            if ($request->get('mobile')) {
                $employee->mobile = $request->get('mobile');
            }
            if ($request->get('identity')) {
                $employee->identity = $request->get('identity');
            }
            if ($request->get('identity_number')) {
                $employee->identity_number = $request->get('identity_number');
            }
            if ($request->get('address_1')) {
                $employee->address_1 = $request->get('address_1');
            }
            if ($request->get('address_2')) {
                $employee->address_2 = $request->get('address_2');
            }
            if ($request->get('address_country')) {
                $employee->address_country = $request->get('address_country');
            }
            if ($request->get('address_state')) {
                $employee->address_state = $request->get('address_state');
            }
            if ($request->get('address_city')) {
                $employee->address_city = $request->get('address_city');
            }
            if ($request->get('gender')) {
                $employee->gender = $request->get('gender');
            }
            if ($request->get('marital_status')) {
                $employee->marital_status = $request->get('marital_status');
            }
            if ($request->get('full_name')) {
                $employee->full_name = $request->get('full_name');
            }
            if ($request->get('full_name_trans')) {
                $employee->full_name_trans = $request->get('full_name_trans');
            }
            if ($request->get('name')) {
                $employee->name = $request->get('name');
            }

            if ($request->get('email')) {
                $employee->email = $request->get('email');
            }
            if ($request->get('date_of_birth')) {
                $employee->date_of_birth = $request->get('date_of_birth');
            }
            if ($request->get('work_mood')) {
                $employee->work_mood = $request->get('work_mood');
            }
            // if work_mood is task_based, then make hours_per_month null else populate with its value
            if ($request->get('work_mood') == 'per_month') {

                if ($request->get('hours_per_month')) {
                    $employee->hours_per_month = $request->get('hours_per_month');
                }


                if ($request->has('days') && $request->filled('days')) {

                    $employee->syncTimeTable($request, $employee->id);

                }

            } else {
                $employee->hours_per_month = null;

            }
            if ($request->get('nationality')) {
                $employee->nationality = $request->get('nationality');
            }

            $employee->save();
            // Handle the user roles

            $employee->syncRoles($request->roles);


            \Illuminate\Support\Facades\DB::commit();


            flash()->success(ucfirst($employee->name) . ' has been updated.');
            return redirect()->route('employees.index');
        } catch (\Exception $e) {


            \Illuminate\Support\Facades\DB::rollback();

            dd($e->getMessage());
            \Log::alert($e);


            Toastr::warning($e->getMessage(), 'Danger');
            return redirect()->back();
        }

    }

    public function destroy($id)
    {


        $employee = Employee::where("id", $id)->forceDelete();
//        \Toastr::success('Employee Deleted', 'Title', ["positionClass" => "toast-top-center"]);

        return response()->json(["message" => 'Employee Deleted !!']);
    }

    public function getEmployeesJsonFormat(Request $request)
    {
        $requestedName = '%' . $request->q . '%';
        $name_condition = "name LIKE " . "'" . $requestedName . "'" . " OR display_name LIKE " . "'" . $requestedName . "'" . " OR full_name LIKE  " . "'" . $requestedName . "'" . " OR full_name_trans LIKE " . "'" . $requestedName . "'";


        $my_query = "select * from employees where " . $name_condition;


        $employee = \DB::select($my_query, array($request->q));
        $totalCounts = \DB::select($my_query, array($request->q));
        $totalCounts = count($totalCounts);

        $searchLang = is_arabic($request->q) == true ? "Arabic" : "English";


        return response()->json(["total_count" => $totalCounts, "incomplete_results" => false, "items" => $employee, 'language' => $searchLang], 200);

    }

    public function activate(Request $request, $employeeId = null)
    {

        try {
            if ($request->ajax()) {

                $validation = \Validator::make($request->all(), [
                    'id' => 'required',
                ]);
                if ($validation->passes()) {

                    $creator_role = 'employee';
                    $employeeId = $request->id;


                    $employee = Employee::withTrashed()->findOrfail($employeeId);

                    $employee->status = "active";
                    $employee->deleted_at = NULL;
                    $employee->save();


                    // send email to the user

//                    $employee->notify(new UserStatusChangedToNewApplication($employee, $request->program_id, $request->center_id, $request->class_id));

                    return response()->json("successfully activated the employee");


                }
            }


            return response()->json(['error' => $validation->errors()->all()], 422);


        } catch (\Exception $e) {

            dd($e->getMessage());
            return response()->json($e->getMessage(), 500);
        }
    }

}

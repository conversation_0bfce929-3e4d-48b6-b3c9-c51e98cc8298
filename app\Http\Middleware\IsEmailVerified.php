<?php
/**
 * (c) Lunaweb Ltd. - Josias <PERSON>
 * Date: 14.03.17
 * Time: 14:24
 */

namespace App\Http\Middleware;


use Closure;
use Lunaweb\EmailVerification\Exceptions\UserNotVerifiedException;

class IsEmailVerified
{

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     *
     * @throws UserNotVerifiedException
     */
    public function handle($request, Closure $next)
    {

//        if( !is_null($request->user()) && !$request->user()->verified){
        if( is_null($request->user())){
            return redirect(route('showResendVerificationEmailForm'));
        }

        return $next($request);
    }

}
@extends('layouts.hound')

@section('content')
    <div class="container">
        <div class="row">
           

            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">Edit content_category #{{ $content_category->id }}</div>
                    <div class="panel-body">
                        <a href="{{ url('/workplace/curriculum/content_categories') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
                        <br />
                        <br />

                        @if ($errors->any())
                            <ul class="alert alert-danger">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        @endif

                        {!! Form::model($content_category, [
                            'method' => 'PATCH',
                            'url' => ['/workplace/curriculum/content_categories', $content_category->id],
                            'class' => 'form-horizontal',
                            'files' => true
                        ]) !!}

                        @include ('curriculum::content_categories.form', ['submitButtonText' => 'Update'])

                        {!! Form::close() !!}

                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

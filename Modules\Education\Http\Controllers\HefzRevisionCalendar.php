<?php

namespace Modules\Education\Http\Controllers;

use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\AssignSubject;
use App\ClassRoutineUpdate;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStaatisticsRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class HefzRevisionCalendar extends Controller
{


    public function __invoke(Request $request,$classId)
    {

        $hefz = DB::table('student_hefz_report')
            ->select(DB::raw('CONCAT("Hefz: ", COUNT(*), " st reported") as title, date(created_at) as start'))
            ->where('class_id', '=', $classId)
            ->whereNotNull('hefz_from_surat')
            ->whereNotNull('hefz_from_ayat')
            ->whereNotNull('hefz_to_surat')
            ->whereNotNull('hefz_to_ayat')
            ->groupBy(DB::raw('date(created_at)'))
            ->get();


        $revision = DB::table('student_revision_report')
            ->select(DB::raw('CONCAT("Revision: ", COUNT(*), " st reported") as title, date(created_at) as start'))
            ->where('class_id', $classId)
            ->whereNotNull('revision_from_surat')
            ->whereNotNull('revision_from_ayat')
            ->whereNotNull('revision_to_surat')
            ->whereNotNull('revision_to_ayat')
            ->groupBy('start')
            ->get();

        $ijazasanad = DB::table('student_ijazasanad_memorization_report')
            ->select(DB::raw('CONCAT("Ijazasanad: ", COUNT(DISTINCT student_id), " st reported") as title, date(created_at) as start'))
            ->where('class_id', $classId)
            ->where(function ($query) {
                // Group the first set of conditions - all Hefz fields must be not null
                $query->where(function ($subQuery) {
                    $subQuery->whereNotNull('hefz_from_surat')
                        ->whereNotNull('hefz_from_ayat')
                        ->whereNotNull('hefz_to_surat')
                        ->whereNotNull('hefz_to_ayat');
                })
                    // Combine with the second set using OR - ALL lesson pairs must be complete
                    ->orWhere(function ($subQuery) {
                        $subQuery->whereNotNull('talqeen_from_lesson')
                            ->whereNotNull('talqeen_to_lesson')
                            ->whereNotNull('revision_from_lesson')
                            ->whereNotNull('revision_to_lesson')
                            ->whereNotNull('jazariyah_from_lesson')
                            ->whereNotNull('jazariyah_to_lesson')
                            ->whereNotNull('seminars_from_lesson')
                            ->whereNotNull('seminars_to_lesson');
                    });
            })
            ->groupBy(DB::raw('date(created_at)'))
            ->get();

        $nouranya = DB::table('student_nouranya_report')
            ->select(DB::raw('CONCAT("Nouranya: ", COUNT(*), " st reported") as title, date(created_at) as start'))
            ->where('class_id', $classId)
            ->where(function ($query) {
                // Level 1: Check only 'from_lesson' and 'to_lesson'
                $query->where(function ($level1Query) {
                    $level1Query->whereNotNull('from_lesson')
                        ->whereNotNull('to_lesson');
                })
                    // Level 2: Check 'from_lesson', 'to_lesson', 'from_lesson_line_number', and 'to_lesson_line_number'
                    ->orWhere(function ($level2Query) {
                        $level2Query->whereNotNull('from_lesson')
                            ->whereNotNull('to_lesson')
                            ->whereNotNull('from_lesson_line_number')
                            ->whereNotNull('to_lesson_line_number');
                    })
                    // Level 3: Check 'talaqqi_from_lesson', 'talaqqi_to_lesson', 'talqeen_from_lesson', and 'talqeen_to_lesson'
                    ->orWhere(function ($level3Query) {
                        $level3Query->whereNotNull('talaqqi_from_lesson')
                            ->whereNotNull('talaqqi_to_lesson')
                            ->whereNotNull('talqeen_from_lesson')
                            ->whereNotNull('talqeen_to_lesson');
                    });
            })
            ->groupBy(DB::raw('date(created_at)'))
            ->get();



        return $hefz->merge($revision)->merge($ijazasanad)->merge($nouranya);



    }
}

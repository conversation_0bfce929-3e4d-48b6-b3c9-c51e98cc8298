<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Attendance;
use App\BankAccount;
use App\Role;
use App\Employee;

use Carbon\Carbon;
use DB;
use App\Permission;
use App\EmployeeSalary;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\EmployeeTimetable;
use App\Http\Controllers\Controller;


class EmployeesBankDetailsDatatablesController extends Controller
{


    public function getBankDetails(Request $request,$id)
    {

        if ($request->ajax()) {



            $bankDatatables = BankAccount::where("emp_id",$id)->with('bank')->with('bankAccountType')->get();






            return \Yajra\DataTables\DataTables::of($bankDatatables)
                ->addColumn('bankName', function ($row) {
                    return $row->bank->name;

                })
                ->addColumn('bankAccountType', function ($row) {
                    return $row->bankAccountType->name;

                })
                ->addColumn('action', function ($record) {






                        $str = '</div><br>';
                        if (\Auth::user()->can('update employee bank')) {
                            $btns = '<div class="mini ui buttons">
                       <button class=" ui default button updateAttendancePairModalTriggerBtnOut" id="updateAttendancePairModalTriggerBtnOut"  data-account_type_id="'.$record->account_type_id.'" data-bank_id="'.$record->bank_id.'"  data-bank_account_id="'.$record->id.'"  data-toggle = "modal"
                                                                                                        data-target = "#updateBankAccountDetailsModal" >update</button>
                                                                                                        <div class="or"></div>
                        <button class=" ui negative button deleteModalTriggerBtnOut" id="deleteIndividualAttendanceModalTriggerBtnOut" data-toggle = "modal" data-atttype="'.$record->type.'"  data-id="'.$record->id.'"
                                                                                                        data-target = "#deleteAttendanceConfirmationModalOut" >Delete</button>
                        
                        </div>';

                        }

                        return $str . $btns;

                })
                ->rawColumns(['action'])
                ->addIndexColumn()
                ->toJson();

        }


    }
}
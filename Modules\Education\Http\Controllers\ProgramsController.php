<?php

namespace Modules\Education\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Center;
use App\MarksGrade;
use App\Program;
use App\ProgramLevel;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use PhpOffice\PhpWord\IOFactory;
use Session;

class ProgramsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {


        $keyword = $request->get('search');

        $perPage = 25;



        if (!empty($keyword)) {
            $programs = Program::where('title', 'LIKE', "%$keyword%")
				->orWhere('description', 'LIKE', "%$keyword%")
				->orWhere('status', 'LIKE', "%$keyword%")
				->orWhere('locale', 'LIKE', "%$keyword%")
				->orWhere('status', 'LIKE', "%$keyword%")
                ->with(['classes' => function($query){
                    return $query->withCount('students');
                }])

				->get();
        } else {
            $programs = Program::with(['classes' => function($query){
                return $query->withCount('students');
            }])->paginate($perPage);


        }

        return view('education::programs.index', compact('programs'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {

        return view('education::programs.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $this->validateProgram($request);

        $request->organization_id = config('organization_id');
        
        $requestData = $request->all();

        $requestData['organization_id'] = config('organization_id');

        $program = Program::create($requestData);

        foreach ($request->translate as $code => $translate) {
            $program->translateOrNew($code)->title = $translate['title'];
            $program->translateOrNew($code)->description = $translate['description'];
        }

        $program->save();

        Session::flash('flash_message', 'Program added!');

        return redirect('workplace/education/programs');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {

        $program = Program::with(['classes.teachers', 'classes.students' => function($query) {
            $query->whereNull('class_students.deleted_at')
                ->whereNull('class_students.end_date');
        }])->findOrFail($id);


        $evaluationSchemas = $program->evaluationSchemas->first();
        $marks_grades = MarksGrade::orderBy('gpa', 'desc')->where('program_id', $program->id)->get();
        $centers = Center::all();


        return view('education::programs.show', compact('program' , 'centers','marks_grades','evaluationSchemas'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id, Request $request)
    {


        $program = Program::findOrFail($id);

        if(isset($program->setting['special_program_code'])){
            return app('Modules\Education\Http\Controllers\SpecialPrograms\\'.ucfirst($program->setting['special_program_code']).'Controller')->edit($id , $request);
        }

        return view('education::programs.edit', compact('program'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {




        $this->validateProgram($request);

        $request->organization_id = config('organization_id');
        
        $requestData = $request->all();
        
        $requestData['organization_id'] = config('organization_id');        
        
        $program = Program::findOrFail($id);

        $program->update($requestData);

        foreach ($request->translate as $code => $translate) {
            $program->translateOrNew($code)->title = $translate['title'];
            $program->translateOrNew($code)->description = $translate['description'];
        }

        $program->save();

        Session::flash('flash_message', 'Program updated!');

        return redirect('workplace/education/programs');
    }

    /**
     * Update program availabilty in centers.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function centers(Request $request)
    {
        auth()->user()->can('edit program_centers');

        $id = $request->program_id;

        $program = Program::findOrFail($id);
        
        $program->centers()->sync($request->program_centers);
        
        Session::flash('flash_message', 'Program updated!');
        
        if($request->ajax()){
            return response()->json(['status' => 'success']);
        }

        return redirect()->back();
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Program::destroy($id);

        Session::flash('flash_message', 'Program deleted!');

        return redirect('workplace/education/programs');
    }

    private function validateProgram($request){
        $rules = [];

        $rules['code'] = 'required|alpha_num|min:3';

        $rules['translate.*.title'] = 'required';

        $rules['language'] = 'required';

        $rules['status'] = 'required';
        


        $this->validate($request , $rules);
    }

    public function programOfferLetter(Request $request){


        try {



            $offerContent = $request->get("offerLetter");



           
            $program = Program::find($request->id);
            $program->offer = $this->dataready($offerContent);
            $program->save();



        } catch (\Exception $exception) {

            \Log::error($exception);
        }

        return response()->json("successfully saved in database");
    }

    public function getProgramOfferLetter($id){


        try {




            $program = Program::find($id)->offer;

            $program = html_entity_decode($program);




        } catch (\Exception $exception) {
            \Log::error($exception);
            dd($exception->getMessage());
        }

        return response()->json($program);
    }

    function dataready($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data);
        return $data;
    }
}

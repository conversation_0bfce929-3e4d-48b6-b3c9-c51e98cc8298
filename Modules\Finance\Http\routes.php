<?php

Route::group(['middleware' => ['web','auth:employee','missedClockOutMiddleware'], 'prefix' => 'workplace/finance', 'namespace' => 'Modules\Finance\Http\Controllers'], function()
{
    Route::get('/', 'FinanceController@index');
    Route::resource('student_payments' , 'StudentPaymentsController')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');;
});

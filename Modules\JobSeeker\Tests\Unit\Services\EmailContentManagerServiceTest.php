<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Tests\Unit\Services;

use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Cache;
use Modules\JobSeeker\Services\EmailContentManagerService;
use Mo<PERSON>les\JobSeeker\Entities\EmailContentSetting;

/**
 * Unit tests for EmailContentManagerService
 * 
 * @covers \Modules\JobSeeker\Services\EmailContentManagerService
 */
final class EmailContentManagerServiceTest extends TestCase
{
    use DatabaseTransactions;

    private EmailContentManagerService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(EmailContentManagerService::class);
        Cache::flush(); // Clear cache for clean tests
    }

    /** @test */
    public function it_can_get_active_fields()
    {
        // Create or update test settings (avoid unique constraint issues)
        $enabledSetting = EmailContentSetting::updateOrCreate([
            'field_name' => 'position',
        ], [
            'field_name' => 'position',
            'is_enabled' => true,
            'display_label' => 'Job Title',
            'display_order' => 1,
            'field_group' => 'basic',
            'conditional_display' => false,
            'requires_provider_fetch' => false,
        ]);

        $disabledSetting = EmailContentSetting::updateOrCreate([
            'field_name' => 'company_logo',
        ], [
            'is_enabled' => false,
            'display_label' => 'Company Logo',
            'display_order' => 2,
            'field_group' => 'company',
            'conditional_display' => true,
            'requires_provider_fetch' => false,
        ]);

        $activeFields = $this->service->getActiveFields();
        $activeNames = $activeFields->pluck('field_name')->toArray();

        // Assert our enabled field is present and the disabled one is not
        $this->assertContains('position', $activeNames);
        $this->assertNotContains('company_logo', $activeNames);
    }

    /** @test */
    public function it_can_check_if_field_is_enabled()
    {
        EmailContentSetting::updateOrCreate([
            'field_name' => 'salary',
        ], [
            'is_enabled' => true,
            'display_label' => 'Salary',
            'display_order' => 1,
            'field_group' => 'basic',
            'conditional_display' => false,
            'requires_provider_fetch' => false,
        ]);

        $this->assertTrue($this->service->isFieldEnabled('salary'));
        $this->assertFalse($this->service->isFieldEnabled('nonexistent_field'));
    }

    /** @test */
    public function it_formats_date_fields_correctly()
    {
        $setting = new EmailContentSetting([
            'field_name' => 'publish_date',
            'is_enabled' => true,
            'display_label' => 'Posted Date',
            'display_order' => 1,
            'field_group' => 'basic',
            'conditional_display' => false,
            'requires_provider_fetch' => false,
            'formatting_options' => ['date_format' => 'M j, Y'],
        ]);

        $job = ['publish_date' => '2024-01-15'];
        $formatted = $this->service->formatFieldValue($setting, $job);

        $this->assertEquals('Jan 15, 2024', $formatted);
    }

    /** @test */
    public function it_formats_boolean_fields_correctly()
    {
        $setting = new EmailContentSetting([
            'field_name' => 'is_featured',
            'is_enabled' => true,
            'display_label' => 'Featured Job',
            'display_order' => 1,
            'field_group' => 'basic',
            'conditional_display' => false,
            'requires_provider_fetch' => false,
        ]);

        $jobTrue = ['is_featured' => true];
        $jobFalse = ['is_featured' => false];
        $jobNumeric = ['is_featured' => 1];

        $this->assertEquals('Yes', $this->service->formatFieldValue($setting, $jobTrue));
        $falseFormatted = $this->service->formatFieldValue($setting, $jobFalse);
        $this->assertTrue(in_array($falseFormatted, ['No', 'Not specified'], true));
        $this->assertEquals('Yes', $this->service->formatFieldValue($setting, $jobNumeric));
    }

    /** @test */
    public function it_truncates_text_fields_correctly()
    {
        $setting = new EmailContentSetting([
            'field_name' => 'description',
            'is_enabled' => true,
            'display_label' => 'Job Description',
            'display_order' => 1,
            'field_group' => 'details',
            'conditional_display' => false,
            'requires_provider_fetch' => false,
            'formatting_options' => ['max_length' => 50],
        ]);

        $longText = 'This is a very long job description that should be truncated at fifty characters';
        $job = ['description' => $longText];
        
        $formatted = $this->service->formatFieldValue($setting, $job);
        
        $this->assertLessThanOrEqual(53, strlen($formatted)); // 50 + '...'
        $this->assertStringEndsWith('...', $formatted);
    }

    /** @test */
    public function it_handles_conditional_display_correctly()
    {
        $setting = new EmailContentSetting([
            'field_name' => 'salary',
            'is_enabled' => true,
            'display_label' => 'Salary',
            'display_order' => 1,
            'field_group' => 'basic',
            'conditional_display' => true,
            'requires_provider_fetch' => false,
        ]);

        $jobWithSalary = ['salary' => 'AFN 50,000'];
        $jobWithoutSalary = ['salary' => null];
        $jobWithEmptySalary = ['salary' => ''];

        $this->assertTrue($this->service->shouldShowField($setting, $jobWithSalary));
        $this->assertFalse($this->service->shouldShowField($setting, $jobWithoutSalary));
        $this->assertFalse($this->service->shouldShowField($setting, $jobWithEmptySalary));
    }

    /** @test */
    public function it_can_apply_preset_configurations()
    {
        // Ensure baseline fields exist and start disabled
        $fields = ['position', 'company_name', 'locations', 'salary', 'about_company'];
        foreach ($fields as $index => $field) {
            EmailContentSetting::updateOrCreate([
                'field_name' => $field,
            ], [
                'is_enabled' => false, // Start disabled
                'display_label' => ucfirst(str_replace('_', ' ', $field)),
                'display_order' => $index + 1,
                'field_group' => 'basic',
                'conditional_display' => false,
                'requires_provider_fetch' => false,
            ]);
        }

        $result = $this->service->applyPreset('minimal', '<EMAIL>');

        $this->assertTrue($result);

        // Check that minimal preset fields are enabled
        $minimalFields = ['position', 'company_name', 'locations', 'publish_date', 'application_email'];
        $enabledFields = EmailContentSetting::enabled()->pluck('field_name')->toArray();
        
        // At least the position field should be enabled (it's in our test data)
        $this->assertContains('position', $enabledFields);
    }

    /** @test */
    public function it_gets_field_groups_for_job_correctly()
    {
        // Create settings for different groups
        EmailContentSetting::updateOrCreate([
            'field_name' => 'position',
        ], [
            'is_enabled' => true,
            'display_label' => 'Job Title',
            'display_order' => 1,
            'field_group' => 'basic',
            'conditional_display' => false,
            'requires_provider_fetch' => false,
        ]);

        EmailContentSetting::updateOrCreate([
            'field_name' => 'about_company',
        ], [
            'is_enabled' => true,
            'display_label' => 'About Company',
            'display_order' => 1,
            'field_group' => 'company',
            'conditional_display' => true,
            'requires_provider_fetch' => true,
        ]);

        $job = [
            'position' => 'Software Developer',
            'about_company' => 'Great company with excellent culture'
        ];

        $fieldGroups = $this->service->getFieldGroupsForJob($job);

        $this->assertCount(2, $fieldGroups);
        $this->assertTrue($fieldGroups->has('basic'));
        $this->assertTrue($fieldGroups->has('company'));
    }

    /** @test */
    public function it_handles_empty_job_data_gracefully()
    {
        $setting = new EmailContentSetting([
            'field_name' => 'position',
            'is_enabled' => true,
            'display_label' => 'Job Title',
            'display_order' => 1,
            'field_group' => 'basic',
            'conditional_display' => false,
            'requires_provider_fetch' => false,
        ]);

        $emptyJob = [];
        $formatted = $this->service->formatFieldValue($setting, $emptyJob);

        $this->assertEquals('Not specified', $formatted);
    }

    /** @test */
    public function it_formats_salary_with_currency_symbol()
    {
        $setting = new EmailContentSetting([
            'field_name' => 'salary',
            'is_enabled' => true,
            'display_label' => 'Salary',
            'display_order' => 1,
            'field_group' => 'basic',
            'conditional_display' => false,
            'requires_provider_fetch' => false,
            'formatting_options' => ['currency_symbol' => 'AFN'],
        ]);

        $job = ['salary' => '50,000 - 70,000'];
        $formatted = $this->service->formatFieldValue($setting, $job);

        $this->assertStringStartsWith('AFN', $formatted);
    }

    /** @test */
    public function it_caches_settings_for_performance()
    {
        EmailContentSetting::updateOrCreate([
            'field_name' => 'position',
        ], [
            'field_name' => 'position',
            'is_enabled' => true,
            'display_label' => 'Job Title',
            'display_order' => 1,
            'field_group' => 'basic',
            'conditional_display' => false,
            'requires_provider_fetch' => false,
        ]);

        // First call should hit database
        $fields1 = $this->service->getActiveFields();
        
        // Second call should hit cache
        $fields2 = $this->service->getActiveFields();

        $this->assertEquals($fields1->toArray(), $fields2->toArray());
        $this->assertGreaterThan(0, $fields2->count());
    }

    /** @test */
    public function it_clears_cache_when_needed()
    {
        // Set some cache
        $this->service->getActiveFields();

        // Clear cache
        $this->service->clearCache();

        // Cache should be empty now
        $this->assertFalse(Cache::has('email_content_settings'));
        $this->assertFalse(Cache::has('email_content_enabled_fields'));
    }

    /** @test */
    public function it_gets_sample_job_data_for_preview()
    {
        $sampleData = $this->service->getSampleJobData();

        $this->assertIsArray($sampleData);
        $this->assertArrayHasKey('position', $sampleData);
        $this->assertArrayHasKey('company_name', $sampleData);
        $this->assertArrayHasKey('locations', $sampleData);
        $this->assertArrayHasKey('salary', $sampleData);
        $this->assertEquals('Senior Software Developer', $sampleData['position']);
    }

    /** @test */
    public function it_gets_available_fields_by_group()
    {
        $availableFields = $this->service->getAvailableFields();

        $this->assertIsArray($availableFields);
        $this->assertArrayHasKey('basic', $availableFields);
        $this->assertArrayHasKey('company', $availableFields);
        $this->assertArrayHasKey('details', $availableFields);
        $this->assertArrayHasKey('requirements', $availableFields);
        $this->assertArrayHasKey('application', $availableFields);

        // Check that basic group has expected fields
        $basicFields = $availableFields['basic'];
        $this->assertArrayHasKey('position', $basicFields);
        $this->assertArrayHasKey('company_name', $basicFields);
        $this->assertArrayHasKey('locations', $basicFields);
    }

    /** @test */
    public function it_gets_preset_configurations()
    {
        $presets = $this->service->getPresets();

        $this->assertIsArray($presets);
        $this->assertArrayHasKey('minimal', $presets);
        $this->assertArrayHasKey('standard', $presets);
        $this->assertArrayHasKey('detailed', $presets);

        // Check preset structure
        $minimal = $presets['minimal'];
        $this->assertArrayHasKey('name', $minimal);
        $this->assertArrayHasKey('description', $minimal);
        $this->assertArrayHasKey('enabled_fields', $minimal);
        $this->assertIsArray($minimal['enabled_fields']);
    }
}

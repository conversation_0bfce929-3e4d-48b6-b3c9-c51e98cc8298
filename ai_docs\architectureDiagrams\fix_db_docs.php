<?php
/**
 * Database Documentation Fix 
 * 
 * This script ensures ALL database tables are included in the documentation,
 * fixing issues with newly created tables not appearing in the documentation.
 */

// Print start message
echo "Starting comprehensive database documentation fix...\n";

// Force clear any potential cached information
if (file_exists(__DIR__ . '/complete_database_documentation_with_tables.html')) {
    echo "Removing existing documentation file...\n";
    unlink(__DIR__ . '/complete_database_documentation_with_tables.html');
}

// Incorporate Laravel environment variables
require_once __DIR__ . '/../../vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../../');
$dotenv->load();

// Set up database connection parameters from Laravel .env
$host = $_ENV['DB_HOST'];
$database = $_ENV['DB_DATABASE'];
$username = $_ENV['DB_USERNAME'];
$password = $_ENV['DB_PASSWORD'];
$port = $_ENV['DB_PORT'] ?? 3306;

echo "Connecting to database: {$database} on {$host}...\n";

try {
    // Connect to database to get a definitive list of all tables
    $pdo = new PDO(
        "mysql:host={$host};port={$port};dbname={$database}",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "Connection successful.\n";
    
    // Get all tables from the database
    $stmt = $pdo->query('SHOW TABLES');
    $allDatabaseTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Found " . count($allDatabaseTables) . " tables in database.\n";
    
    // Run the standard documentation generator (cross-platform)
    echo "Running database documentation generator...\n";
    $phpBinary = PHP_BINARY ?: 'php';
    $generator = __DIR__ . '/generate_table_definitions.php';
    if (!file_exists($generator)) {
        echo "Error: Generator script not found at $generator\n";
        exit(1);
    }
    $command = escapeshellarg($phpBinary) . ' ' . escapeshellarg($generator);
    
    $descriptorspec = [
        0 => ["pipe", "r"],
        1 => ["pipe", "w"],
        2 => ["pipe", "w"],
    ];
    
    $process = proc_open($command, $descriptorspec, $pipes, __DIR__);
    if (is_resource($process)) {
        fclose($pipes[0]);
        $output = stream_get_contents($pipes[1]);
        $error = stream_get_contents($pipes[2]);
        fclose($pipes[1]);
        fclose($pipes[2]);
        $exitCode = proc_close($process);
        if ($exitCode !== 0) {
            echo "Error running documentation generator.\n";
            echo "Output: $output\n";
            echo "Error: $error\n";
            exit(1);
        }
    } else {
        echo "Failed to start generator process.\n";
        exit(1);
    }

    // Verify all tables are included
    echo "Verifying all tables are included in documentation...\n";
    $docFile = __DIR__ . '/complete_database_documentation_with_tables.html';

    if (!file_exists($docFile)) {
        echo "Error: Documentation file was not generated.\n";
        exit(1);
    }

    $docContent = file_get_contents($docFile);
    
    // Create an array of tables that should be in the documentation
    $missingTables = [];
    
    // Check each database table to see if it's in the documentation
    foreach ($allDatabaseTables as $tableName) {
        if (strpos($docContent, 'id="table-' . $tableName . '"') === false) {
            $missingTables[] = $tableName;
        }
    }
    
    if (empty($missingTables)) {
        echo "All tables are already included in the documentation.\n";
        echo "Documentation fix completed successfully!\n";
        exit(0);
    }
    
    echo count($missingTables) . " missing tables found: " . implode(', ', $missingTables) . "\n";
    echo "Attempting to add missing tables to documentation...\n";
    
    // Add missing tables to documentation
    
    // First, extract existing table links for the sidebar
    $tableLinks = [];
    if (preg_match_all('/<div class="table-container" id="(table-[^"]+)">\\s*<h3>([^<]+)<\/h3>/i', $docContent, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            $tableLinks[$match[1]] = $match[2];
        }
    }
    
    // Generate HTML for each missing table
    $missingTablesHtml = '';
    
    foreach ($missingTables as $tableName) {
        echo "Processing missing table: {$tableName}...\n";
        
        $tableDefinition = [];
        
        // Get columns details
        $columnsStmt = $pdo->query("SHOW FULL COLUMNS FROM `{$tableName}`");
        $tableDefinition['columns'] = $columnsStmt->fetchAll();
        
        // Get primary keys
        $primaryKeysStmt = $pdo->query("SHOW KEYS FROM `{$tableName}` WHERE Key_name = 'PRIMARY'");
        $tableDefinition['primary_keys'] = $primaryKeysStmt->fetchAll();
        
        // Get create table statement
        $createTableStmt = $pdo->query("SHOW CREATE TABLE `{$tableName}`");
        $createTable = $createTableStmt->fetch();
        $tableDefinition['create_table'] = $createTable['Create Table'] ?? '';
        
        // Extract table engine and collation from create table statement
        if (preg_match('/ENGINE=(\w+)/', $tableDefinition['create_table'], $engineMatches)) {
            $tableDefinition['engine'] = $engineMatches[1];
        }
        
        if (preg_match('/CHARSET=(\w+)/', $tableDefinition['create_table'], $charsetMatches)) {
            $tableDefinition['charset'] = $charsetMatches[1];
        }
        
        if (preg_match('/COLLATE=(\w+)/', $tableDefinition['create_table'], $collateMatches)) {
            $tableDefinition['collation'] = $collateMatches[1];
        }
        
        // Get foreign keys
        $stmt = $pdo->prepare("
            SELECT 
                COLUMN_NAME as column_name,
                REFERENCED_TABLE_NAME as referenced_table,
                REFERENCED_COLUMN_NAME as referenced_column,
                CONSTRAINT_NAME as constraint_name
            FROM 
                INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE 
                TABLE_SCHEMA = :database
                AND TABLE_NAME = :table
                AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        
        $stmt->execute([
            ':database' => $database,
            ':table' => $tableName
        ]);
        
        $tableDefinition['foreign_keys'] = $stmt->fetchAll();
        
        // Find reverse relationships
        $stmt = $pdo->prepare("
            SELECT 
                TABLE_NAME as table_name,
                COLUMN_NAME as column_name,
                REFERENCED_COLUMN_NAME as referenced_column,
                CONSTRAINT_NAME as constraint_name
            FROM 
                INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE 
                TABLE_SCHEMA = :database
                AND REFERENCED_TABLE_NAME = :table
        ");
        
        $stmt->execute([
            ':database' => $database,
            ':table' => $tableName
        ]);
        
        $tableDefinition['referenced_by'] = [];
        foreach ($stmt->fetchAll() as $reference) {
            $tableDefinition['referenced_by'][] = [
                'table' => $reference['table_name'],
                'column' => $reference['column_name'],
                'references_column' => $reference['referenced_column'],
                'constraint_name' => $reference['constraint_name']
            ];
        }
        
        // Generate HTML for the table
        $tableHtml = '';
        $tableHtml .= '<div class="table-container" id="table-' . htmlspecialchars($tableName) . '">' . PHP_EOL;
        $tableHtml .= '    <h3>' . htmlspecialchars($tableName) . '</h3>' . PHP_EOL;
        
        // Table details
        $tableHtml .= '    <p><strong>Engine:</strong> ' . htmlspecialchars($tableDefinition['engine'] ?? 'Unknown');
        $tableHtml .= ' | <strong>Charset:</strong> ' . htmlspecialchars($tableDefinition['charset'] ?? 'Unknown');
        $tableHtml .= ' | <strong>Collation:</strong> ' . htmlspecialchars($tableDefinition['collation'] ?? 'Unknown') . '</p>' . PHP_EOL;
        
        // Create array of primary key column names for easy checking
        $primaryKeyColumns = [];
        foreach ($tableDefinition['primary_keys'] as $primaryKey) {
            $primaryKeyColumns[] = $primaryKey['Column_name'];
        }
        
        // Create array of foreign key column names for easy checking
        $foreignKeyColumns = [];
        foreach ($tableDefinition['foreign_keys'] as $foreignKey) {
            $foreignKeyColumns[] = $foreignKey['column_name'];
        }
        
        // Table columns
        $tableHtml .= '    <h4>Columns</h4>' . PHP_EOL;
        $tableHtml .= '    <table>' . PHP_EOL;
        $tableHtml .= '        <thead>' . PHP_EOL;
        $tableHtml .= '            <tr>' . PHP_EOL;
        $tableHtml .= '                <th>Column Name</th>' . PHP_EOL;
        $tableHtml .= '                <th>Type</th>' . PHP_EOL;
        $tableHtml .= '                <th>Nullable</th>' . PHP_EOL;
        $tableHtml .= '                <th>Default</th>' . PHP_EOL;
        $tableHtml .= '                <th>Extra</th>' . PHP_EOL;
        $tableHtml .= '                <th>Comment</th>' . PHP_EOL;
        $tableHtml .= '            </tr>' . PHP_EOL;
        $tableHtml .= '        </thead>' . PHP_EOL;
        $tableHtml .= '        <tbody>' . PHP_EOL;
        
        foreach ($tableDefinition['columns'] as $column) {
            $columnClass = '';
            if (in_array($column['Field'], $primaryKeyColumns)) {
                $columnClass = 'primary-key';
            } elseif (in_array($column['Field'], $foreignKeyColumns)) {
                $columnClass = 'foreign-key';
            }
            
            $tableHtml .= '            <tr>' . PHP_EOL;
            $tableHtml .= '                <td class="' . $columnClass . '">' . htmlspecialchars($column['Field']) . '</td>' . PHP_EOL;
            $tableHtml .= '                <td>' . htmlspecialchars($column['Type']) . '</td>' . PHP_EOL;
            $tableHtml .= '                <td>' . htmlspecialchars($column['Null']) . '</td>' . PHP_EOL;
            $tableHtml .= '                <td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>' . PHP_EOL;
            $tableHtml .= '                <td>' . htmlspecialchars($column['Extra']) . '</td>' . PHP_EOL;
            $tableHtml .= '                <td>' . htmlspecialchars($column['Comment']) . '</td>' . PHP_EOL;
            $tableHtml .= '            </tr>' . PHP_EOL;
        }
        
        $tableHtml .= '        </tbody>' . PHP_EOL;
        $tableHtml .= '    </table>' . PHP_EOL;
        
        // Foreign keys
        if (!empty($tableDefinition['foreign_keys'])) {
            $tableHtml .= '    <h4>Foreign Keys</h4>' . PHP_EOL;
            $tableHtml .= '    <ul>' . PHP_EOL;
            
            foreach ($tableDefinition['foreign_keys'] as $foreignKey) {
                $tableHtml .= '        <li class="fk-relationship">' . PHP_EOL;
                $tableHtml .= '            <strong class="foreign-key">' . htmlspecialchars($foreignKey['column_name']) . '</strong> → ';
                $tableHtml .= '<a href="#table-' . htmlspecialchars($foreignKey['referenced_table']) . '">';
                $tableHtml .= htmlspecialchars($foreignKey['referenced_table']) . '</a>.<strong>' . htmlspecialchars($foreignKey['referenced_column']) . '</strong>';
                $tableHtml .= ' (Constraint: ' . htmlspecialchars($foreignKey['constraint_name']) . ')' . PHP_EOL;
                $tableHtml .= '        </li>' . PHP_EOL;
            }
            
            $tableHtml .= '    </ul>' . PHP_EOL;
        }
        
        // Referenced by other tables
        if (!empty($tableDefinition['referenced_by'])) {
            $tableHtml .= '    <h4>Referenced By</h4>' . PHP_EOL;
            $tableHtml .= '    <ul>' . PHP_EOL;
            
            foreach ($tableDefinition['referenced_by'] as $reference) {
                $tableHtml .= '        <li class="fk-relationship">' . PHP_EOL;
                $tableHtml .= '            <a href="#table-' . htmlspecialchars($reference['table']) . '">';
                $tableHtml .= htmlspecialchars($reference['table']) . '</a>.<strong>' . htmlspecialchars($reference['column']) . '</strong>';
                $tableHtml .= ' → <strong class="primary-key">' . htmlspecialchars($reference['references_column']) . '</strong>';
                $tableHtml .= ' (Constraint: ' . htmlspecialchars($reference['constraint_name']) . ')' . PHP_EOL;
                $tableHtml .= '        </li>' . PHP_EOL;
            }
            
            $tableHtml .= '    </ul>' . PHP_EOL;
        }
        
        $tableHtml .= '</div>' . PHP_EOL;
        
        $missingTablesHtml .= $tableHtml;
        
        // Add to the table links
        $tableLinks["table-{$tableName}"] = $tableName;
    }
    
    if (empty($missingTablesHtml)) {
        echo "No HTML generated for missing tables. This shouldn't happen.\n";
        exit(1);
    }
    
    echo "HTML generated for missing tables. Now updating the documentation file...\n";
    
    // Find a good insertion point in the document
    // Look for the end of the tables section
    if (preg_match('/<div id="tables-section">.*?<h2[^>]*>Table Definitions<\/h2>(.*?)<\/div>\\s*<\/div>\\s*<\/div>/s', $docContent, $contentMatch)) {
        // Insert the new tables at the end of existing tables but before the closing div
        $newContent = preg_replace('/<\/div>\\s*<\/div>\\s*<\/div>$/s', $missingTablesHtml . "\n</div>\n</div>\n</div>", $contentMatch[0]);
        $docContent = str_replace($contentMatch[0], $newContent, $docContent);
    } else if (preg_match('/<div id="tables-container">(.*?)<\/div>\\s*<script>/s', $docContent, $contentMatch)) {
        // Insert the new tables at the end of the tables container
        $newContent = preg_replace('/<\/div>\\s*<script>/s', $missingTablesHtml . "\n</div>\n<script>", $contentMatch[0]);
        $docContent = str_replace($contentMatch[0], $newContent, $docContent);
    } else {
        // Try to append to the end of all tables
        $lastTablePos = strrpos($docContent, '</div><!-- End of table container -->');
        if ($lastTablePos !== false) {
            $docContent = substr_replace($docContent, '</div><!-- End of table container -->' . $missingTablesHtml, $lastTablePos, strlen('</div><!-- End of table container -->'));
        } else {
            // Last resort - just append to the body
            $bodyEndPos = strrpos($docContent, '</body>');
            if ($bodyEndPos !== false) {
                $docContent = substr_replace($docContent, $missingTablesHtml . '</body>', $bodyEndPos, strlen('</body>'));
            } else {
                echo "Could not find a suitable insertion point in the documentation file.\n";
                exit(1);
            }
        }
    }
    
    // Ensure we update the table list in the sidebar
    // Sort table links alphabetically
    ksort($tableLinks);
    
    // Create HTML for table links
    $tableLinksHtml = '';
    foreach ($tableLinks as $tableId => $tableName) {
        $tableLinksHtml .= '<a href="#' . $tableId . '" class="table-link" data-table-id="' . $tableId . '">' . $tableName . '</a>' . PHP_EOL;
    }
    
    // Update the table index in the sidebar
    if (preg_match('/<div class="tables-index" id="tablesIndex">(.*?)<\/div>/s', $docContent, $indexMatch)) {
        $docContent = str_replace($indexMatch[0], '<div class="tables-index" id="tablesIndex">' . $tableLinksHtml . '</div>', $docContent);
    }
    
    // Write the updated content back to the file
    file_put_contents($docFile, $docContent);
    
    echo "Documentation updated successfully with " . count($missingTables) . " missing tables!\n";
    echo "Missing tables added: " . implode(', ', $missingTables) . "\n";
    
} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo $e->getTraceAsString() . "\n";
    exit(1);
}

echo "Database documentation fix completed successfully!\n";
exit(0); 
/**
 * Estonian translation for bootstrap-datetimepicker
 * <PERSON> <http://rene.korss.ee> 
 */
;(function($){
	$.fn.datetimepicker.dates['ee'] = {
		days:        	["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"],
		daysShort:   	["P", "E", "T", "K", "N", "R", "L", "P"],
		daysMin:     	["P", "E", "T", "K", "N", "R", "L", "P"],
		months:      	["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aprill", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "August", "September", "Oktoober", "November", "Detsember"],
		monthsShort: 	["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Aug", "Sept", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"],
		today:       	"<PERSON>äna",
		suffix:     	[],
		meridiem: 		[],
		weekStart: 		1,
		format: 		"dd.mm.yyyy hh:ii"
	};
}(j<PERSON>uery));
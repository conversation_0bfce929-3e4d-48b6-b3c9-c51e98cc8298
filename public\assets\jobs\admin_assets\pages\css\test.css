/* <PERSON><PERSON><PERSON>zier Transition */
.mt-comments .mt-comment {
  padding: 10px;
  margin: 0 0 10px 0; }
  .mt-comments .mt-comment .mt-comment-img {
    width: 40px;
    float: left; }
    .mt-comments .mt-comment .mt-comment-img > img {
      border-radius: 50% !important; }
  .mt-comments .mt-comment .mt-comment-body {
    padding-left: 20px;
    position: relative;
    overflow: hidden; }
    .mt-comments .mt-comment .mt-comment-body .mt-comment-info:before,
    .mt-comments .mt-comment .mt-comment-body .mt-comment-info:after {
      content: " ";
      display: table; }
    .mt-comments .mt-comment .mt-comment-body .mt-comment-info:after {
      clear: both; }
    .mt-comments .mt-comment .mt-comment-body .mt-comment-info .mt-comment-author {
      display: inline-block;
      float: left;
      margin: 0px 0px 10px 0;
      color: #060606;
      font-weight: 600; }
    .mt-comments .mt-comment .mt-comment-body .mt-comment-info .mt-comment-date {
      display: inline-block;
      float: right;
      margin: 0px;
      color: #BABABA; }
    .mt-comments .mt-comment .mt-comment-body .mt-comment-text {
      color: #a6a6a6; }
    .mt-comments .mt-comment .mt-comment-body .mt-comment-details {
      margin: 10px 0px 0px 0; }
      .mt-comments .mt-comment .mt-comment-body .mt-comment-details .mt-comment-status {
        text-transform: uppercase;
        float: left; }
        .mt-comments .mt-comment .mt-comment-body .mt-comment-details .mt-comment-status.mt-comment-status-pending {
          color: #B8C0F5; }
        .mt-comments .mt-comment .mt-comment-body .mt-comment-details .mt-comment-status.mt-comment-status-approved {
          color: #6BD873; }
        .mt-comments .mt-comment .mt-comment-body .mt-comment-details .mt-comment-status.mt-comment-status-rejected {
          color: red; }
      .mt-comments .mt-comment .mt-comment-body .mt-comment-details .mt-comment-actions {
        display: none;
        list-style: none;
        margin: 0;
        padding: 0;
        float: right; }
        .mt-comments .mt-comment .mt-comment-body .mt-comment-details .mt-comment-actions > li {
          float: left;
          padding: 0 5px;
          margin: 0; }
          .mt-comments .mt-comment .mt-comment-body .mt-comment-details .mt-comment-actions > li > a {
            text-transform: uppercase;
            color: #999999; }
            .mt-comments .mt-comment .mt-comment-body .mt-comment-details .mt-comment-actions > li > a:hover {
              color: #666666;
              text-decoration: none; }
  .mt-comments .mt-comment:hover {
    background: #f9f9f9; }
    .mt-comments .mt-comment:hover .mt-comment-body .mt-comment-details .mt-comment-actions {
      display: inline-block; }

.mt-actions .mt-action {
  margin: 0px;
  padding: 15px 0 15px 0;
  border-bottom: 1px solid #f7f8f9; }
  .mt-actions .mt-action:last-child {
    border-bottom: 0px; }
  .mt-actions .mt-action .mt-action-img {
    width: 40px;
    float: left; }
    .mt-actions .mt-action .mt-action-img > img {
      border-radius: 50% !important;
      margin-bottom: 2px; }
  .mt-actions .mt-action .mt-action-body {
    padding-left: 15px;
    position: relative;
    overflow: hidden; }
    .mt-actions .mt-action .mt-action-body .mt-action-row {
      display: table;
      width: 100%; }
      .mt-actions .mt-action .mt-action-body .mt-action-row .mt-action-info {
        display: table-cell;
        vertical-align: top; }
        .mt-actions .mt-action .mt-action-body .mt-action-row .mt-action-info .mt-action-icon {
          display: table-cell;
          padding: 6px 20px 6px 6px; }
          .mt-actions .mt-action .mt-action-body .mt-action-row .mt-action-info .mt-action-icon > i {
            display: inline-block;
            position: relative;
            top: 10px;
            font-size: 25px;
            color: #78E0E8; }
        .mt-actions .mt-action .mt-action-body .mt-action-row .mt-action-info .mt-action-details {
          display: table-cell;
          vertical-align: top; }
          .mt-actions .mt-action .mt-action-body .mt-action-row .mt-action-info .mt-action-details .mt-action-author {
            color: #060606;
            font-weight: 600; }
          .mt-actions .mt-action .mt-action-body .mt-action-row .mt-action-info .mt-action-details .mt-action-desc {
            margin-bottom: 0;
            color: #A6A8A8; }
      .mt-actions .mt-action .mt-action-body .mt-action-row .mt-action-datetime {
        vertical-align: top;
        display: table-cell;
        text-align: center;
        width: 150px;
        white-space: nowrap;
        padding-top: 15px;
        color: #A6A8A8; }
        .mt-actions .mt-action .mt-action-body .mt-action-row .mt-action-datetime .mt-action-dot {
          display: inline-block;
          width: 10px;
          height: 10px;
          background-color: red;
          border-radius: 50% !important;
          margin-left: 5px;
          margin-right: 5px; }
      .mt-actions .mt-action .mt-action-body .mt-action-row .mt-action-buttons {
        vertical-align: top;
        display: table-cell;
        text-align: center;
        width: 120px;
        white-space: nowrap;
        padding-top: 10px; }

@media (max-width: 767px) {
  /* 767px */
  .mt-actions .mt-action .mt-action-body .mt-action-row {
    display: block; }
    .mt-actions .mt-action .mt-action-body .mt-action-row .mt-action-info {
      display: block; }
    .mt-actions .mt-action .mt-action-body .mt-action-row .mt-action-datetime {
      display: inline-block;
      margin-left: 40px; }
    .mt-actions .mt-action .mt-action-body .mt-action-row .mt-action-buttons {
      display: inline-block;
      float: right; } }

.mt-wiget-1 {
  border: 1px solid #e7ecf1;
  text-align: center;
  border-radius: 4px !important;
  position: relative; }
  .mt-wiget-1 .mt-icon {
    position: absolute;
    right: 10px;
    top: 10px;
    margin: 7px;
    font-size: 20px; }
    .mt-wiget-1 .mt-icon > a > i {
      color: #a1e5e1; }
    .mt-wiget-1 .mt-icon > a:hover > i {
      color: #79dad5; }
  .mt-wiget-1 .mt-img {
    display: inline-block;
    border-radius: 50% !important;
    border: 4px solid #e7ecf1;
    margin: 40px 0 30px 0; }
    .mt-wiget-1 .mt-img > img {
      border: 1px solid trnsparent;
      border-radius: 50% !important; }
  .mt-wiget-1 .mt-body .mt-username {
    text-align: center;
    margin: 5px 10px; }
  .mt-wiget-1 .mt-body .mt-user-title {
    text-align: center;
    margin: 10px 10px 10px 10px;
    color: #D1D4D6;
    font-size: 12px; }
  .mt-wiget-1 .mt-body .mt-stats {
    margin: 30px 0px 0px 0px; }
    .mt-wiget-1 .mt-body .mt-stats .btn-group {
      border-top: 1px solid #e7ecf1; }
      .mt-wiget-1 .mt-body .mt-stats .btn-group .btn {
        padding: 10px 10px;
        font-size: 10px;
        border-right: 1px solid #e7ecf1; }
        .mt-wiget-1 .mt-body .mt-stats .btn-group .btn:hover {
          background-color: #e7ecf1; }
        .mt-wiget-1 .mt-body .mt-stats .btn-group .btn:last-child {
          border: 0; }
        .mt-wiget-1 .mt-body .mt-stats .btn-group .btn > i {
          position: relative;
          top: 3px;
          right: 2px;
          color: #79dad5;
          font-size: 16px; }
        .mt-wiget-1 .mt-body .mt-stats .btn-group .btn:first-child {
          border-bottom-left-radius: 4px !important; }
        .mt-wiget-1 .mt-body .mt-stats .btn-group .btn:last-child {
          border-bottom-right-radius: 4px !important; }

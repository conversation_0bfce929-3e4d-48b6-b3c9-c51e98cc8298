<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Illuminate\Support\Facades\Log;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\ProviderJobCategory;
use <PERSON><PERSON>les\JobSeeker\Services\NotificationProcessLogger;

/**
 * CategoryMappingService handles translation between canonical and provider-specific
 * category IDs with comprehensive decision audit trail and failure tracking.
 *
 * Purpose: Bidirectional category mapping with complete observability and traceability.
 * Inputs: Category ID arrays and provider name, optional NotificationProcessLogger.
 * Outputs: Mapped category arrays, comprehensive decision logs, mapping analytics.
 * Side effects: Reads provider_job_categories table, logs to notification_process_logs,
 * tracks mapping success rates, identifies unmapped categories with context.
 *
 * Monitoring Infrastructure:
 * - NotificationProcessLogger integration: Step-by-step mapping decisions logged
 * - Comprehensive mapping analysis: Success rates, unmapped categories, detailed context
 * - Error handling: Database failures captured with full business context
 * - Decision audit trail: Why categories were mapped/unmapped with provider details
 *
 * Key Features:
 * - Bidirectional mapping: canonical ↔ provider category translation
 * - Mapping analytics: Success rates, gap analysis, unmapped category identification
 * - Process logger integration: setProcessLogger() enables comprehensive tracking
 * - Detailed context logging: Provider names, category details, mapping results
 * - Error resilience: Database failures handled gracefully with full error context
 *
 * Usage: Set process logger via setProcessLogger() for comprehensive decision tracking.
 * All mapping operations will be logged with business context for troubleshooting.
 */
final class CategoryMappingService
{
    private ?NotificationProcessLogger $processLogger = null;

    /**
     * Set process logger for comprehensive decision tracking.
     *
     * @param NotificationProcessLogger $processLogger
     * @return void
     */
    public function setProcessLogger(NotificationProcessLogger $processLogger): void
    {
        $this->processLogger = $processLogger;
    }
    /**
     * Maps canonical category IDs to provider-specific category IDs.
     *
     * @param array<int> $canonicalCategoryIds Array of canonical category IDs
     * @param string $providerName Provider name (e.g., 'acbar', 'jobs.af')
     * @return array<int, array> Provider category IDs with pivot attributes for sync
     *
     * Purpose: Translate canonical categories to provider categories for delivery.
     * Side effects: Reads provider_job_categories table, logs unmapped categories.
     * Errors: Returns empty array on invalid inputs; logs gaps in mapping.
     */
    public function mapCanonicalToProviderCategoryIds(array $canonicalCategoryIds, string $providerName): array
    {
        Log::info('CategoryMappingService: Starting canonical to provider category mapping', [
            'canonical_category_ids' => $canonicalCategoryIds,
            'provider_name' => $providerName,
            'input_count' => count($canonicalCategoryIds)
        ]);

        // Validate inputs
        if (empty($canonicalCategoryIds) || empty($providerName)) {
            Log::warning('CategoryMappingService: Invalid inputs provided', [
                'canonical_category_ids_empty' => empty($canonicalCategoryIds),
                'provider_name_empty' => empty($providerName),
                'provider_name' => $providerName
            ]);
            return [];
        }

        // Ensure all canonical IDs are integers
        $validCanonicalIds = array_map('intval', array_filter($canonicalCategoryIds, 'is_numeric'));
        
        if (empty($validCanonicalIds)) {
            Log::warning('CategoryMappingService: No valid canonical category IDs found', [
                'original_ids' => $canonicalCategoryIds,
                'provider_name' => $providerName
            ]);
            return [];
        }

        try {
            // Query provider categories that map to the canonical categories
            $providerCategories = ProviderJobCategory::where('provider_name', $providerName)
                ->whereIn('canonical_category_id', $validCanonicalIds)
                ->get(['id', 'canonical_category_id', 'name', 'provider_identifier']);

            Log::debug('CategoryMappingService: Retrieved provider categories from database', [
                'provider_name' => $providerName,
                'canonical_ids_requested' => $validCanonicalIds,
                'provider_categories_found' => $providerCategories->count(),
                'provider_category_ids' => $providerCategories->pluck('id')->toArray()
            ]);

            // Build result array with pivot attributes for sync operations
            $result = [];
            $mappedCanonicalIds = [];

            foreach ($providerCategories as $providerCategory) {
                $result[$providerCategory->id] = [
                    'canonical_category_id' => $providerCategory->canonical_category_id,
                    'created_at' => now(),
                    'updated_at' => now()
                ];
                $mappedCanonicalIds[] = $providerCategory->canonical_category_id;
            }

            // Check for unmapped canonical IDs
            $unmappedCanonicalIds = array_diff($validCanonicalIds, $mappedCanonicalIds);
            
            if (!empty($unmappedCanonicalIds)) {
                Log::warning('CategoryMappingService: Some canonical categories have no provider mapping', [
                    'provider_name' => $providerName,
                    'unmapped_canonical_ids' => $unmappedCanonicalIds,
                    'mapped_count' => count($mappedCanonicalIds),
                    'unmapped_count' => count($unmappedCanonicalIds)
                ]);
            }

            Log::info('CategoryMappingService: Successfully mapped canonical to provider categories', [
                'provider_name' => $providerName,
                'canonical_ids_input' => $validCanonicalIds,
                'provider_category_ids_output' => array_keys($result),
                'mapped_count' => count($result),
                'unmapped_canonical_ids' => $unmappedCanonicalIds
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('CategoryMappingService: Error mapping canonical to provider categories', [
                'canonical_category_ids' => $validCanonicalIds,
                'provider_name' => $providerName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Get provider category IDs for given canonical categories.
     *
     * @param array<int> $canonicalCategoryIds Array of canonical category IDs
     * @param string $providerName Provider name (e.g., 'acbar', 'jobs.af')
     * @return array<int> Simple array of provider category IDs
     *
     * Purpose: Simple mapping for cases that don't need pivot attributes.
     * Side effects: Uses mapCanonicalToProviderCategoryIds internally.
     * Errors: Returns empty array on mapping failures.
     */
    public function getProviderCategoryIds(array $canonicalCategoryIds, string $providerName): array
    {
        $mappingResult = $this->mapCanonicalToProviderCategoryIds($canonicalCategoryIds, $providerName);
        return array_keys($mappingResult);
    }

    /**
     * Maps provider-specific category IDs to canonical category IDs for unified notification.
     *
     * @param array<int> $providerCategoryIds Provider-specific category IDs to map
     * @param string $providerName The provider identifier ('jobs.af' or 'acbar')
     * @return array<int> Canonical category IDs corresponding to the input
     *
     * Purpose: Primary method for JobNotificationService to translate provider categories.
     * Side effects: Reads provider_job_categories table, logs mapping operations.
     * Errors: Returns empty array on invalid inputs; logs unmapped categories.
     */
    public function mapProviderToCanonicalIds(array $providerCategoryIds, string $providerName): array
    {
        return $this->mapProviderToCanonicalCategoryIds($providerCategoryIds, $providerName);
    }

    /**
     * Maps provider category IDs back to canonical category IDs (reverse mapping).
     *
     * @param array<int> $providerCategoryIds Array of provider category IDs
     * @param string $providerName Provider name (e.g., 'acbar', 'jobs.af')
     * @return array<int> Array of canonical category IDs
     *
     * Purpose: Translate provider categories back to canonical categories for display.
     * Side effects: Reads provider_job_categories table, logs unmapped categories.
     * Errors: Returns empty array on invalid inputs; logs gaps in mapping.
     */
    public function mapProviderToCanonicalCategoryIds(array $providerCategoryIds, string $providerName): array
    {
        Log::info('CategoryMappingService: Starting provider to canonical category mapping', [
            'provider_category_ids' => $providerCategoryIds,
            'provider_name' => $providerName,
            'input_count' => count($providerCategoryIds)
        ]);

        // Log decision audit trail start
        if ($this->processLogger) {
            $this->processLogger->logStep('category_mapping_start', 'running', 'process', $providerName,
                'Starting provider to canonical category mapping', [
                'provider_category_ids' => $providerCategoryIds,
                'provider_name' => $providerName,
                'input_count' => count($providerCategoryIds)
            ]);
        }

        // Validate inputs
        if (empty($providerCategoryIds) || empty($providerName)) {
            if ($this->processLogger) {
                $this->processLogger->logStep('category_mapping_validation', 'error', 'decision', $providerName,
                    'Category mapping failed due to invalid inputs', [
                    'provider_category_ids_empty' => empty($providerCategoryIds),
                    'provider_name_empty' => empty($providerName)
                ]);
            }

            Log::warning('CategoryMappingService: Invalid inputs for reverse mapping', [
                'provider_category_ids_empty' => empty($providerCategoryIds),
                'provider_name_empty' => empty($providerName),
                'provider_name' => $providerName
            ]);
            return [];
        }

        // Ensure all provider IDs are integers
        $validProviderIds = array_map('intval', array_filter($providerCategoryIds, 'is_numeric'));
        
        if (empty($validProviderIds)) {
            if ($this->processLogger) {
                $this->processLogger->logStep('category_mapping_validation', 'error', 'decision', $providerName,
                    'No valid provider category IDs found after filtering', [
                    'original_ids' => $providerCategoryIds,
                    'filtered_ids' => $validProviderIds
                ]);
            }

            Log::warning('CategoryMappingService: No valid provider category IDs found', [
                'original_ids' => $providerCategoryIds,
                'provider_name' => $providerName
            ]);
            return [];
        }

        try {
            // Query canonical categories that map from the provider categories
            $providerCategories = ProviderJobCategory::where('provider_name', $providerName)
                ->whereIn('id', $validProviderIds)
                ->get(['id', 'canonical_category_id', 'name']);

            Log::debug('CategoryMappingService: Retrieved canonical mappings from database', [
                'provider_name' => $providerName,
                'provider_ids_requested' => $validProviderIds,
                'mappings_found' => $providerCategories->count()
            ]);

            // Extract canonical category IDs
            $canonicalIds = $providerCategories->pluck('canonical_category_id')->unique()->values()->toArray();
            
            // Check for unmapped provider IDs and log detailed decision audit
            $mappedProviderIds = $providerCategories->pluck('id')->toArray();
            $unmappedProviderIds = array_diff($validProviderIds, $mappedProviderIds);

            // Create detailed mapping analysis for decision audit
            $mappingDetails = [];
            foreach ($providerCategories as $category) {
                $mappingDetails[] = [
                    'provider_id' => $category->id,
                    'provider_name' => $category->name,
                    'canonical_id' => $category->canonical_category_id
                ];
            }

            if (!empty($unmappedProviderIds)) {
                if ($this->processLogger) {
                    $this->processLogger->logStep('category_mapping_gaps', 'warning', 'decision', $providerName,
                        'Some provider categories have no canonical mapping', [
                        'unmapped_provider_ids' => $unmappedProviderIds,
                        'mapped_count' => count($mappedProviderIds),
                        'unmapped_count' => count($unmappedProviderIds),
                        'mapping_details' => $mappingDetails
                    ]);
                }

                Log::warning('CategoryMappingService: Some provider categories have no canonical mapping', [
                    'provider_name' => $providerName,
                    'unmapped_provider_ids' => $unmappedProviderIds,
                    'mapped_count' => count($mappedProviderIds),
                    'unmapped_count' => count($unmappedProviderIds)
                ]);
            }

            // Log successful mapping with comprehensive decision audit
            if ($this->processLogger) {
                $this->processLogger->logStep('category_mapping_success', 'success', 'decision', $providerName,
                    'Successfully mapped provider categories to canonical categories', [
                    'provider_ids_input' => $validProviderIds,
                    'canonical_ids_output' => $canonicalIds,
                    'mapped_count' => count($canonicalIds),
                    'unmapped_count' => count($unmappedProviderIds),
                    'mapping_details' => $mappingDetails,
                    'success_rate' => count($mappedProviderIds) / count($validProviderIds)
                ]);
            }

            Log::info('CategoryMappingService: Successfully mapped provider to canonical categories', [
                'provider_name' => $providerName,
                'provider_ids_input' => $validProviderIds,
                'canonical_ids_output' => $canonicalIds,
                'mapped_count' => count($canonicalIds),
                'unmapped_provider_ids' => $unmappedProviderIds
            ]);

            return $canonicalIds;

        } catch (\Exception $e) {
            // Log comprehensive error context for decision audit
            if ($this->processLogger) {
                $this->processLogger->logStep('category_mapping_error', 'error', 'error', $providerName,
                    'Category mapping failed with database error', [
                    'provider_category_ids' => $validProviderIds,
                    'error_message' => $e->getMessage(),
                    'error_class' => get_class($e),
                    'error_file' => $e->getFile(),
                    'error_line' => $e->getLine()
                ]);
            }

            Log::error('CategoryMappingService: Error mapping provider to canonical categories', [
                'provider_category_ids' => $validProviderIds,
                'provider_name' => $providerName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Check if all canonical categories have provider mappings.
     *
     * @param array<int> $canonicalCategoryIds Array of canonical category IDs
     * @param string $providerName Provider name
     * @return bool True if all canonical categories have mappings
     *
     * Purpose: Validate that all requested categories can be mapped.
     * Side effects: Reads provider_job_categories table.
     * Errors: Returns false on database errors or missing mappings.
     */
    public function hasCompleteMapping(array $canonicalCategoryIds, string $providerName): bool
    {
        if (empty($canonicalCategoryIds) || empty($providerName)) {
            return false;
        }

        try {
            $validCanonicalIds = array_map('intval', array_filter($canonicalCategoryIds, 'is_numeric'));
            
            if (empty($validCanonicalIds)) {
                return false;
            }

            $mappedCount = ProviderJobCategory::where('provider_name', $providerName)
                ->whereIn('canonical_category_id', $validCanonicalIds)
                ->distinct('canonical_category_id')
                ->count();

            $hasComplete = $mappedCount === count($validCanonicalIds);

            Log::debug('CategoryMappingService: Checked mapping completeness', [
                'provider_name' => $providerName,
                'canonical_ids_count' => count($validCanonicalIds),
                'mapped_count' => $mappedCount,
                'has_complete_mapping' => $hasComplete
            ]);

            return $hasComplete;

        } catch (\Exception $e) {
            Log::error('CategoryMappingService: Error checking mapping completeness', [
                'canonical_category_ids' => $canonicalCategoryIds,
                'provider_name' => $providerName,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}

<?php

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Log;

class JobCompanyWatchlistCount extends Model
{
    protected $table = 'job_company_watchlist_counts';
    protected $guarded = ['id'];
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'watchlist_id',
        'jobs_count',
        'last_job_date',
        'date_checked'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'date_checked' => 'date',
        'last_job_date' => 'datetime',
    ];

    /**
     * Get the watchlist entry that owns this count.
     */
    public function watchlist(): BelongsTo
    {
        return $this->belongsTo(JobCompanyWatchlist::class, 'watchlist_id');
    }
} 
<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProgramLevelLesson extends Model
{
    use HasFactory;
    protected $fillable = ['program_level_id', 'title', 'order', 'description','properties'];
    protected $casts = [
        'properties' => 'array',
    ];


    public function programLevel()
    {
        return $this->belongsTo('App\ProgramLevel', 'program_level_id');
    }

}

@extends('layouts.hound')
@section('mytitle', 'View Event')

@section('content')
<div class="panel panel-default card-view">
    <div class="panel-heading"><h4>Event {{ $event->id }}</h4></div>
    <div class="panel-body">

        <a href="{{ url('/workplace/humanresource/calendar') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
        <a href="{{ url('/workplace/humanresource/calendar/' . $event->id . '/edit') }}" title="Edit Event"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>
        {!! Form::open([
            'method'=>'DELETE',
            'url' => ['workplace/humanresource/calendar', $event->id],
            'style' => 'display:inline'
        ]) !!}
            {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                    'type' => 'submit',
                    'class' => 'btn btn-danger btn-xs',
                    'title' => 'Delete Event',
                    'onclick'=>'return confirm("Confirm delete?")'
            ))!!}
        {!! Form::close() !!}
        <br/>
        <br/>

        <div class="table-responsive">
            <table class="table table-borderless">
                <tbody>
                    <tr>
                        <th>ID</th><td>{{ $event->id }}</td>
                    </tr>
                    <tr><th> Organization Id </th><td> {{ $event->organization_id }} </td></tr><tr><th> Event Time </th><td> {{ $event->event_time }} </td></tr><tr><th> Duration </th><td> {{ $event->duration }} </td></tr>
                </tbody>
            </table>
        </div>

    </div>
</div>
@endsection

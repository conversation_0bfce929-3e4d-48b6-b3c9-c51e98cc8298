/**
 * Portuguese translation for bootstrap-datetimepicker
 * Original code: <PERSON><PERSON><PERSON> <<EMAIL>>
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 */
;(function($){
	$.fn.datetimepicker.dates['pt'] = {
		days: ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sábad<PERSON>", "Domingo"],
		daysShort: ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sex", "<PERSON>á<PERSON>", "Dom"],
		daysMin: ["<PERSON>", "<PERSON>", "<PERSON>", "Qu", "Qu", "Se", "Sa", "<PERSON>"],
		months: ["Janeiro", "Fevereiro", "Março", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jun<PERSON>", "Jul<PERSON>", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"],
		monthsShort: ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Out", "Nov", "Dez"],
		suffix: [],
		meridiem: [],
		today: "Ho<PERSON>"
	};
}(jQ<PERSON>y));

<?php
$mime_types = array(
	"application/postscript" => "ps",
	"audio/x-aiff" => "aiff",
	"text/plain" => "txt",
	"video/x-ms-asf" => "asx",
	"audio/basic" => "snd",
	"video/x-msvideo" => "avi",
	"application/x-bcpio" => "bcpio",
	"application/octet-stream" => "so",
	"image/bmp" => "bmp",
	"application/x-bzip2" => "bz2",
	"application/x-netcdf" => "nc",
	"application/x-kchart" => "chrt",
	"application/x-cpio" => "cpio",
	"application/mac-compactpro" => "cpt",
	"application/x-csh" => "csh",
	"text/css" => "css",
	"application/x-director" => "dxr",
	"image/vnd.djvu" => "djvu",
	"application/x-dvi" => "dvi",
	"image/vnd.dwg" => "dwg",
	"application/epub" => "epub",
	"application/epub+zip" => "epub",
	"text/x-setext" => "etx",
	"application/andrew-inset" => "ez",
	"video/x-flv" => "flv",
	"image/gif" => "gif",
	"application/x-gtar" => "gtar",
	"application/x-gzip" => "tgz",
	"application/x-hdf" => "hdf",
	"application/mac-binhex40" => "hqx",
	"text/html" => "html",
	"text/htm" => "htm",
	"x-conference/x-cooltalk" => "ice",
	"image/ief" => "ief",
	"model/iges" => "igs",
	"text/vnd.sun.j2me.app-descriptor" => "jad",
	"application/x-java-archive" => "jar",
	"application/x-java-jnlp-file" => "jnlp",
	"image/jpeg" => "jpg",
	"application/x-javascript" => "js",
	"audio/midi" => "midi",
	"application/x-killustrator" => "kil",
	"application/x-kpresenter" => "kpt",
	"application/x-kspread" => "ksp",
	"application/x-kword" => "kwt",
	"application/vnd.google-earth.kml+xml" => "kml",
	"application/vnd.google-earth.kmz" => "kmz",
	"application/x-latex" => "latex",
	"audio/x-mpegurl" => "m3u",
	"application/x-troff-man" => "man",
	"application/x-troff-me" => "me",
	"model/mesh" => "silo",
	"application/vnd.mif" => "mif",
	"video/quicktime" => "qt",
	"video/x-sgi-movie" => "movie",
	"audio/mpeg" => "mp3",
	"video/mp4" => "mp4",
	"video/mpeg" => "mpeg",
	"application/x-troff-ms" => "ms",
	"video/vnd.mpegurl" => "mxu",
	"application/vnd.oasis.opendocument.database" => "odb",
	"application/vnd.oasis.opendocument.chart" => "odc",
	"application/vnd.oasis.opendocument.formula" => "odf",
	"application/vnd.oasis.opendocument.graphics" => "odg",
	"application/vnd.oasis.opendocument.image" => "odi",
	"application/vnd.oasis.opendocument.text-master" => "odm",
	"application/vnd.oasis.opendocument.presentation" => "odp",
	"application/vnd.oasis.opendocument.spreadsheet" => "ods",
	"application/vnd.oasis.opendocument.text" => "odt",
	"application/ogg" => "ogg",
	"video/ogg" => "ogv",
	"application/vnd.oasis.opendocument.graphics-template" => "otg",
	"application/vnd.oasis.opendocument.text-web" => "oth",
	"application/vnd.oasis.opendocument.presentation-template" => "otp",
	"application/vnd.oasis.opendocument.spreadsheet-template" => "ots",
	"application/vnd.oasis.opendocument.text-template" => "ott",
	"image/x-portable-bitmap" => "pbm",
	"chemical/x-pdb" => "pdb",
	"application/pdf" => "pdf",
	"image/x-portable-graymap" => "pgm",
	"application/x-chess-pgn" => "pgn",
	"text/x-php" => "php",
	"image/png" => "png",
	"image/x-portable-anymap" => "pnm",
	"image/x-portable-pixmap" => "ppm",
	"application/vnd.ms-powerpoint" => "ppt",
	"audio/x-realaudio" => "ra",
	"audio/x-pn-realaudio" => "rm",
	"image/x-cmu-raster" => "ras",
	"image/x-rgb" => "rgb",
	"application/x-troff" => "tr",
	"application/x-rpm" => "rpm",
	"text/rtf" => "rtf",
	"text/richtext" => "rtx",
	"text/sgml" => "sgml",
	"application/x-sh" => "sh",
	"application/x-shar" => "shar",
	"application/vnd.symbian.install" => "sis",
	"application/x-stuffit" => "sit",
	"application/x-koan" => "skt",
	"application/smil" => "smil",
	"image/svg+xml" => "svg",
	"application/x-futuresplash" => "spl",
	"application/x-wais-source" => "src",
	"application/vnd.sun.xml.calc.template" => "stc",
	"application/vnd.sun.xml.draw.template" => "std",
	"application/vnd.sun.xml.impress.template" => "sti",
	"application/vnd.sun.xml.writer.template" => "stw",
	"application/x-sv4cpio" => "sv4cpio",
	"application/x-sv4crc" => "sv4crc",
	"application/x-shockwave-flash" => "swf",
	"application/vnd.sun.xml.calc" => "sxc",
	"application/vnd.sun.xml.draw" => "sxd",
	"application/vnd.sun.xml.writer.global" => "sxg",
	"application/vnd.sun.xml.impress" => "sxi",
	"application/vnd.sun.xml.math" => "sxm",
	"application/vnd.sun.xml.writer" => "sxw",
	"application/x-tar" => "tar",
	"application/x-tcl" => "tcl",
	"application/x-tex" => "tex",
	"application/x-texinfo" => "texinfo",
	"image/tiff" => "tiff",
	"application/x-bittorrent" => "torrent",
	"text/tab-separated-values" => "tsv",
	"application/x-ustar" => "ustar",
	"application/x-cdlink" => "vcd",
	"model/vrml" => "wrl",
	"audio/x-wav" => "wav",
	"audio/x-ms-wax" => "wax",
	"image/vnd.wap.wbmp" => "wbmp",
	"application/vnd.wap.wbxml" => "wbxml",
	"video/x-ms-wm" => "wm",
	"audio/x-ms-wma" => "wma",
	"text/vnd.wap.wml" => "wml",
	"application/vnd.wap.wmlc" => "wmlc",
	"text/vnd.wap.wmlscript" => "wmls",
	"application/vnd.wap.wmlscriptc" => "wmlsc",
	"video/x-ms-wmv" => "wmv",
	"video/x-ms-wmx" => "wmx",
	"video/x-ms-wvx" => "wvx",
	"image/x-xbitmap" => "xbm",
	"application/xhtml+xml" => "xhtml",
	"application/xml" => "xml",
	"image/x-xpixmap" => "xpm",
	"text/xsl" => "xsl",
	"image/x-xwindowdump" => "xwd",
	"chemical/x-xyz" => "xyz",
	"application/zip" => "zip",
	"application/msword" => "doc",
	"application/vnd.openxmlformats-officedocument.wordprocessingml.document" => "docx",
	"application/vnd.openxmlformats-officedocument.wordprocessingml.template" => "dotx",
	"application/vnd.ms-word.document.macroEnabled.12" => "docm",
	"application/vnd.ms-excel" => "xls",
	"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" => "xlsx",
	"application/vnd.openxmlformats-officedocument.spreadsheetml.template" => "xltx",
	"application/vnd.ms-excel.sheet.macroEnabled.12" => "xlsm",
	"application/vnd.ms-excel.template.macroEnabled.12" => "xltm",
	"application/vnd.ms-excel.addin.macroEnabled.12" => "xlam",
	"application/vnd.ms-excel.sheet.binary.macroEnabled.12" => "xlsb",
	"application/vnd.openxmlformats-officedocument.presentationml.presentation" => "pptx",
	"application/vnd.openxmlformats-officedocument.presentationml.template" => "potx",
	"application/vnd.openxmlformats-officedocument.presentationml.slideshow" => "ppsx",
	"application/vnd.ms-powerpoint.addin.macroEnabled.12" => "ppam",
	"application/vnd.ms-powerpoint.presentation.macroEnabled.12" => "pptm",
	"application/vnd.ms-powerpoint.template.macroEnabled.12" => "potm",
	"application/vnd.ms-powerpoint.slideshow.macroEnabled.12" => "ppsm",
);


if ( ! function_exists('get_extension_from_mime'))
{
	function get_extension_from_mime($mime){
		global $mime_types;
		if(strpos($mime, ';')!==FALSE){
			$mime = substr($mime, 0,strpos($mime, ';'));
		}
		if(isset($mime_types[$mime])){
			return $mime_types[$mime];
		}
		return '';
	}
}

if ( ! function_exists('get_file_mime_type'))
{
	function get_file_mime_type($filename, $debug = false)
	{
		if (function_exists('finfo_open') && function_exists('finfo_file') && function_exists('finfo_close'))
		{
			$fileinfo = finfo_open(FILEINFO_MIME_TYPE);
			$mime_type = finfo_file($fileinfo, $filename);
			finfo_close($fileinfo);

			if ( ! empty($mime_type))
			{
				if (true === $debug)
				{
					return array( 'mime_type' => $mime_type, 'method' => 'fileinfo' );
				}

				return $mime_type;
			}
		}

		if (function_exists('mime_content_type'))
		{
			$mime_type = mime_content_type($filename);

			if ( ! empty($mime_type))
			{
				if (true === $debug)
				{
					return array( 'mime_type' => $mime_type, 'method' => 'mime_content_type' );
				}

				return $mime_type;
			}
		}

		global $mime_types;
		$mime_types = array_flip($mime_types);

		$tmp_array = explode('.', $filename);
		$ext = strtolower(array_pop($tmp_array));

		if ( ! empty($mime_types[ $ext ]))
		{
			if (true === $debug)
			{
				return array( 'mime_type' => $mime_types[ $ext ], 'method' => 'from_array' );
			}

			return $mime_types[ $ext ];
		}

		if (true === $debug)
		{
			return array( 'mime_type' => 'application/octet-stream', 'method' => 'last_resort' );
		}

		return 'application/octet-stream';
	}
}


/********************
 * The following code can be used to test the function.
 * First put a plain text file named "test.txt" and a
 * JPEG image file named "image.jpg" in the same folder
 * as this file.
 *
 * Simply remove the "REMOVE ME TO TEST" lines below to have
 * the code run when this file runs.
 *
 * Run the code with this command:
 * php mime_type_lib.php
 ********************/


/* REMOVE ME TO TEST
echo get_file_mime_type( 'test.txt' ) . "\n";
echo print_r( get_file_mime_type( 'image.jpg', true ), true ) . "\n";
REMOVE ME TO TEST */

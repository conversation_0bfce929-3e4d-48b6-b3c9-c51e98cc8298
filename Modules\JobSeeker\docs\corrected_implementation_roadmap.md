# Corrected Implementation Roadmap for JobSeeker Category Handling

**Version:** 2.0  
**Date:** {current_date}  
**Purpose:** Foundation for SRS/PRD, ensuring uniform, non-canonical category handling across providers.

## Database Structure Analysis (via execute_sql)
- **provider_job_locations**: Verified structure with provider_name, location_name, provider_identifier, canonical_location_id, is_active (same pattern as categories)
- **job_category_pivot**: Current pivot table with job_id, category_id (needs renaming/restructuring)
- **jobs**: No direct category fields; uses many-to-many via job_category_pivot

## Key Principles
1. **Uniform Pattern**: All providers fetch categories/locations via:
   - CommandScheduleRule.provider_category_ids → ProviderJobCategory.provider_identifier → API
   - CommandScheduleRule.location_ids → ProviderJobLocation.provider_identifier → API

2. **No Canonical in Provider Syncing**: Canonical categories only for job seeker site (out of scope)

3. **Pivot Table Restructuring**: 
   - Current: job_category_pivot (job_id, category_id) → canonical categories
   - New: Rename to job_provider_category_pivot (job_id, provider_category_id) → provider_job_categories.id

## Critical Roadmap Steps

### 1. **Database Schema Changes**
- Rename `job_category_pivot` to `job_provider_category_pivot`
- Change `category_id` column to `provider_category_id` (FK to provider_job_categories.id)
- Update all related indexes and constraints

### 2. **Model Relationship Updates**
- **Job.php**: Update `categories()` method to use new pivot table and ProviderJobCategory
- **JobCategory.php**: Remove/update `jobs()` relationship (canonical not used for providers)
- **ProviderJobCategory.php**: Add `jobs()` relationship via new pivot
- Update all references in JobSeeker module

### 3. **Service Layer Corrections**
- **Remove keyword-based determination**: Eliminate all `determineJobCategories()` logic in JobsAfService/AcbarJobService
- **Uniform fetching**: Both services use rule.provider_category_ids → ProviderJobCategory.provider_identifier
- **Location handling**: Add similar pattern for provider_job_locations

### 4. **Legacy Removal**
- Remove all config-based fallbacks in FilterRepository
- Remove FilterTranslationService dependencies on config
- Ensure all providers use dynamic mapping exclusively

### 5. **Code Impact Analysis**
Files requiring updates (found via grep_search):
- `Modules/JobSeeker/Entities/JobCategory.php` (line 167: pivot table reference)
- `Modules/JobSeeker/Entities/Job.php` (line 134: pivot table reference)
- All services using job.categories() relationship
- Repository classes with category-based queries

### 6. **Testing & Migration**
- Create migration script to rename pivot table and update relationships
- Test uniform flow: Rule IDs → Provider identifiers → API → Save with provider category refs
- Verify location handling follows same pattern

This roadmap addresses the "mess" by establishing consistent, provider-agnostic patterns while removing canonical dependencies from provider syncing.

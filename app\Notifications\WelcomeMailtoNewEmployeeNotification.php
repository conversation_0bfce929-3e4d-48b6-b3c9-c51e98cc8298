<?php

namespace App\Notifications;

use App\Center;
use App\Classes;
use App\ClassTranslation;
use App\FailedNotification;
use App\Program;
use App\ProgramTranslation;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\Mime\Email;
use Illuminate\Bus\Queueable;


class WelcomeMailtoNewEmployeeNotification extends Notification
{
    use Queueable;
    private $userInfo;


    public function __construct($userInfo)
    {
        $this->userInfo = $userInfo;

    }



    public function via(object $notifiable): array
    {
        return ['mail'];
    }


    /**
     * @param object $notifiable
     * @return MailMessage|void
     */
    public function toMail(object $notifiable)
    {


//        $mj = new \Mailjet\Client(env('MAILJETAPIKEY'), env('MAILJETAPISECRET'), false, ['version' => 'v3.1']);
//        $body = [
//            'Messages' => [
//                [
//                    'From' => [
//                        'Email' => "<EMAIL>",
//                        'Name' => "Hashmat"
//                    ],
//                    'To' => [
//                        [
//                            'Email' => $this->userInfo[0]['empEmail'],
//                            'Name' => $this->userInfo[0]['empName']
//                        ]
//                    ],
//                    'Subject' => "Welcome aboard!",
//                    'TextPart' => "Your Text",
//                    'HTMLPart' => view('modules.site.templates.wajeha.backEnd.humanResource.new_employee_welcome_message', ['data'=> $this->userInfo[0]])->render()
//                ]
//            ]
//        ];
//        $response = $mj->post(Resources::$Email, ['body' => $body]);



//        return (new MailMessage)
//            ->view('modules.site.templates.wajeha.backEnd.humanResource.new_employee_welcome_message', ['data'=> $this->userInfo[0]])
//            ->subject('Welcome on Board');



    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}

<?php

namespace Modules\General\Console;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CleanupJobNotificationDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'general:cleanup-job-notifications 
                            {--days=30 : Days to keep data (older records will be deleted)} 
                            {--failures-days=90 : Days to keep failure records} 
                            {--metrics-days=180 : Days to keep metrics records} 
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old job notification data to prevent database bloat';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting job notification data cleanup');
        Log::info('CleanupJobNotificationDataCommand: Started');
        
        $daysToKeep = (int)$this->option('days');
        $failuresDaysToKeep = (int)$this->option('failures-days');
        $metricsDaysToKeep = (int)$this->option('metrics-days');
        $dryRun = (bool)$this->option('dry-run');
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE: No data will be deleted');
        }
        
        try {
            $cutoffDate = Carbon::now()->subDays($daysToKeep);
            $failuresCutoffDate = Carbon::now()->subDays($failuresDaysToKeep);
            $metricsCutoffDate = Carbon::now()->subDays($metricsDaysToKeep);
            
            $this->info('Cutoff dates:');
            $this->info('- Sent jobs: ' . $cutoffDate->toDateTimeString());
            $this->info('- Failures: ' . $failuresCutoffDate->toDateTimeString());
            $this->info('- Metrics: ' . $metricsCutoffDate->toDateTimeString());
            
            // Count records that would be deleted
            $sentJobsCount = DB::table('job_notification_sent_jobs')
                ->where('sent_at', '<', $cutoffDate)
                ->count();
            
            $failuresCount = DB::table('job_notification_failures')
                ->where('created_at', '<', $failuresCutoffDate)
                ->count();
            
            $metricsCount = DB::table('job_notification_health_metrics')
                ->where('metric_date', '<', $metricsCutoffDate->toDateString())
                ->count();
            
            $this->info('Records to be deleted:');
            $this->info('- Sent jobs: ' . number_format($sentJobsCount));
            $this->info('- Failures: ' . number_format($failuresCount));
            $this->info('- Metrics: ' . number_format($metricsCount));
            
            if ($dryRun) {
                $this->warn('DRY RUN COMPLETED: Above records would be deleted');
                return 0;
            }
            
            // Confirm deletion if numbers are large
            if ($sentJobsCount > 10000 || $failuresCount > 5000 || $metricsCount > 1000) {
                if (!$this->confirm('A large number of records will be deleted. Do you wish to continue?')) {
                    $this->info('Operation cancelled by user');
                    return 0;
                }
            }
            
            // Perform the deletions in batches to avoid locking the database for too long
            $this->output->progressStart(3); // Three operations to perform
            
            // Delete old sent jobs
            $deletedSentJobs = $this->deleteInBatches('job_notification_sent_jobs', 'sent_at', $cutoffDate);
            $this->output->progressAdvance();
            
            // Delete old failures
            $deletedFailures = $this->deleteInBatches('job_notification_failures', 'created_at', $failuresCutoffDate);
            $this->output->progressAdvance();
            
            // Delete old metrics
            $deletedMetrics = $this->deleteInBatches('job_notification_health_metrics', 'metric_date', $metricsCutoffDate->toDateString(), 'date');
            $this->output->progressAdvance();
            
            $this->output->progressFinish();
            
            $this->info('Cleanup completed:');
            $this->info('- Deleted sent jobs: ' . number_format($deletedSentJobs));
            $this->info('- Deleted failures: ' . number_format($deletedFailures));
            $this->info('- Deleted metrics: ' . number_format($deletedMetrics));
            
            Log::info('CleanupJobNotificationDataCommand: Completed', [
                'deleted_sent_jobs' => $deletedSentJobs,
                'deleted_failures' => $deletedFailures,
                'deleted_metrics' => $deletedMetrics
            ]);
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Error during cleanup: ' . $e->getMessage());
            Log::error('CleanupJobNotificationDataCommand: Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }
    
    /**
     * Delete records in batches to avoid locking the database
     *
     * @param string $table
     * @param string $dateColumn
     * @param mixed $cutoffDate
     * @param string $dateType
     * @return int
     */
    protected function deleteInBatches(string $table, string $dateColumn, $cutoffDate, string $dateType = 'datetime'): int
    {
        $batchSize = 5000;
        $totalDeleted = 0;
        $remainingRecords = true;
        
        while ($remainingRecords) {
            // Use the appropriate comparison based on date type
            if ($dateType === 'date') {
                $deleted = DB::table($table)
                    ->where($dateColumn, '<', $cutoffDate)
                    ->limit($batchSize)
                    ->delete();
            } else {
                $deleted = DB::table($table)
                    ->where($dateColumn, '<', $cutoffDate)
                    ->limit($batchSize)
                    ->delete();
            }
            
            $totalDeleted += $deleted;
            
            // If we deleted less than the batch size, we're done
            if ($deleted < $batchSize) {
                $remainingRecords = false;
            }
            
            // Sleep briefly to avoid overwhelming the database
            if ($remainingRecords) {
                usleep(200000); // 200ms
            }
        }
        
        return $totalDeleted;
    }
} 
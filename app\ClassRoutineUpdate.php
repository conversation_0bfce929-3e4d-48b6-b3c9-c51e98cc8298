<?php

namespace App;

use App\YearCheck;
use Illuminate\Database\Eloquent\Model;

class ClassRoutineUpdate extends Model
{
    public static function assingedClassRoutine($class_time, $day, $class_id, $section_id)
    {
        try {
            return ClassRoutineUpdate::where('class_period_id', $class_time)->where('day', $day)->where('class_period_id', $class_time)->where('class_id', $class_id)->where('section_id', $section_id)->first();
        } catch (\Exception $e) {
            $data = [];
            return $data;
        }
    }

    public static function teacherAssingedClassRoutine($class_time, $day, $employee_id)
    {
        try {
            return ClassRoutineUpdate::where('class_period_id', $class_time)->where('day', $day)->where('class_period_id', $class_time)->where('employee_id', $employee_id)->first();
        } catch (\Exception $e) {
            $data = [];
            return $data;
        }
    }

    public function subject()
    {
        return $this->belongsTo('App\Subject', 'subject_id', 'id');
    }

    public function classRoom()
    {
        return $this->belongsTo('App\ClassesRoom', 'room_id', 'id');
    }

    public function teacherDetail()
    {
        return $this->belongsTo('App\Employee', 'employee_id', 'id');
    }

    public function class()
    {
        return $this->belongsTo('App\Classes', 'class_id', 'id');
    }

    public function section()
    {
        return $this->belongsTo('App\Section', 'section_id', 'id');
    }
}

<?php

namespace Modules\Education\Http\Controllers;

use App\Classes;
use App\Employee;
use App\Guardian;
use App\Program;
use App\ProgramLevelLesson;
use App\ProgramLevelLessonFormInputs;
use App\Section;
use App\Student;
use App\Subject;
use App\YearCheck;
use App\OnlineExam;
use App\ApiBaseMethod;
use App\Notification;
use App\QuestionBank;
use App\AssignSubject;
use App\OnlineExamMark;
use App\GeneralSettings;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\OnlineExamQuestion;
use App\StudentTakeOnlineExam;
use Illuminate\Support\Facades\DB;
use App\OnlineExamQuestionAssign;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use App\OnlineExamQuestionMuOption;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use App\OnlineExamStudentAnswerMarking;
use Illuminate\Support\Facades\Validator;
use Modules\OnlineExam\Entities\InfixOnlineExam;
use Modules\OnlineExam\Entities\InfixStudentTakeOnlineExam;
use App\Http\Controllers\Controller;
use phpDocumentor\Reflection\DocBlock\Tags\Property;


class ProgramLevelPropertiesController extends Controller
{



    public function store(Request $request,$programId)
    {

        try {
            // First, ensure the program exists
            $program = Program::findOrFail($programId);
            $levelId = $request->input('levelId'); // Assuming levelId is passed through the form
            dd($request->all());
            // Ensure the level exists within the program
            $programLevel = $program->levels()->findOrFail($levelId);

            // Assuming you want to create or update a lesson within this level
            $lessonId = $request->input('lessonId'); // This should be passed from the form if updating an existing lesson
            $lesson = ProgramLevelLesson::firstOrCreate(
                [
                    'id' => $lessonId, // If null, a new lesson will be created
                    'program_level_id' => $levelId
                ],
                ['properties' => []] // Default properties
            );

            // Initialize or get existing properties
            $properties = $lesson->properties ?? [];

            foreach ($request->inputType as $index => $type) {
                if (isset($request->propertyName[$index]) && isset($request->propertyValue[$index])) {
                    $propertyName = $request->propertyName[$index];
                    // Store properties as an associative array with additional type information
                    $properties[$propertyName] = [
                        'type'  => $type,
                        'value' => $request->propertyValue[$index]
                    ];
                }
            }

            $lesson->properties = $properties;
            $lesson->save();

            return back()->with('success', 'Properties saved successfully for the lesson!');
        } catch (\Exception $e) {
            Log::error('Error saving properties to the lesson: ' . $e->getMessage());
            return back()->with('error', 'Error saving properties.');
        }
    }
    public function update(Request $request, $levelId)
    {


        try {
            // Iterate over inputs and update the database
            foreach ($request->inputType as $index => $type) {
                if (isset($request->propertyName[$index]) && isset($request->propertyValue[$index])) {
                    ProgramLevelLessonFormInputs::updateOrCreate(
                        [
                            'program_level_id' => $levelId,
                            'property_name' => $request->propertyName[$index]
                        ],
                        [
                            'property_type' => $type,
                            'property_value' => $request->propertyValue[$index]
                        ]
                    );
                }
            }

            return back()->with('success', 'Form inputs updated successfully for the level!');
        } catch (\Exception $e) {
            Log::error('Error updating form inputs: ' . $e->getMessage());
            return back()->with('error', 'Error updating form inputs.');
        }
    }

}
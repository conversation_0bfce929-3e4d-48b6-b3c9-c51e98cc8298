<?php

namespace Modules\HumanResource\Http\Requests;

use App\Rules\Clockout;
use App\Rules\NoTimeOverlap;
use Illuminate\Foundation\Http\FormRequest;

class ClockOutRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {



        
      


        $employeeId = $this->input('employeeId'); // Assuming the authenticated user's ID

        $clockOutTime = $this->input('clockOutTime');
        $timezone = $this->input('userTimezone');
       
        return [
//            'note' => ['required',new Clockout]
            'userNoteAttendanceOut' => new Clockout,
            'clockOutTime' => [
                'nullable',
                new NoTimeOverlap($employeeId, $clockOutTime ?? now(), $timezone),
            ],


        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
//            'note.required' => 'Please provide a valid reason for your missed Clock-Out',


        ];
    }
}

{!! Form::open(['method' => 'PATCH','route' => ['employee-salary.update',$employee->id] ]) !!}
<input type="hidden" name="employee_id" id="input" value="{{ $employee->id ?? null}}">
<div class="col-md-4">
    <div class="form-group">
        <div class="@if ($errors->has('start_at')) has-error @endif">
            <strong for="start_at">
                Start on
            </strong>
            {!! Form::text('start_at', Date('Y-m-d'), ['class' => 'form-control freedate', 'placeholder' => 'Starting
            Date']) !!}
            @if ($errors->has('start_at')) <p class="help-block">{{ $errors->first('start_at') }}</p> @endif
        </div>
    </div>
</div>

<div class="col-md-4">
    <div class="form-group">
        <div class="@if ($errors->has('work_mood')) has-error @endif">
            <strong for="work_mood">
                Work Mode
            </strong>
            {!! Form::select('work_mood', ['per_hour' => 'Hourly' , 'per_month' => 'Monthly'] , null, ['class' =>
            'form-control', 'placeholder' => 'Work Mode', 'id'=>'work_mood' , 'required']) !!}
            @if ($errors->has('work_mood')) <p class="help-block">{{ $errors->first('work_mood') }}</p> @endif
        </div>
    </div>
</div>
<div class="col-md-4">
    <div class="form-group">
        <div class="@if ($errors->has('basic_salary')) has-error @endif">
            <strong for="basic_salary" id="salary_label">
                Basic Salary
            </strong>
            <div class="input-group">
                <div class="input-group-addon">RM</div>
                {!! Form::number('basic_salary', null, ['class' => 'form-control', 'placeholder' => 'Basic Salary' ,
                'required' , 'step' =>'any']) !!}
            </div>
            @if ($errors->has('basic_salary')) <p class="help-block">{{ $errors->first('basic_salary') }}</p> @endif
        </div>
    </div>
</div>

<div id="hourly_info" style="display: none">
    <div class="col-md-6">
        <div class="form-group">
            <div class="@if ($errors->has('hours_per_month')) has-error @endif">
                <strong for="hours_per_month">
                    How Many Hours Per Month?
                </strong>
                <div class="input-group">
                    {!! Form::number('hours_per_month', null, ['class' => 'form-control', 'placeholder' => 'Hours Per
                    Month' , 'required' , 'step' =>'any' , 'id' => 'hours_per_month']) !!}
                    <div class="input-group-addon">Hours</div>
                </div>
                @if ($errors->has('hours_per_month')) <p class="help-block">{{ $errors->first('hours_per_month') }}</p>
                @endif
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <div class="@if ($errors->has('flexable')) has-error @endif">
                <strong for="flexable">
                    Is it flexable?
                </strong>
                <div class="row">
                    <label class="col-xs-6">
                        <input type="radio" value="true" name="flexable" cheched>
                        Yes
                    </label class="col-xs-6">
                    <label>
                        <input type="radio" value="false" name="flexable">
                        No
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="col-md-12" style="display: none" id="timetable">
    <strog>Working Timetable:</strog>
    <table class="table table-bordered table-hover table-compact">
        <thead>
            <tr>
                <th>Day</th>
                <th>Start</th>
                <th>End</th>
                <th>Break</th>
            </tr>
        </thead>
        <tbody>
            <?php $timestamp = strtotime('next Monday'); ?>
            @for ($i=0; $i < 7 ; $i++) <tr>
                <td>{{strftime('%A', $timestamp)}}
                    <small class="pull-right"><input type="checkbox" value="off" name="timetable[{{strtolower(strftime('%a', $timestamp))}}]"
                            class="day_off"> Day off</small>
                </td>
                <td>
                    <input type="text" name="timetable[{{strtolower(strftime('%a', $timestamp))}}][clockin]" class="form-control time"
                        value="09:00" required="required">
                    <div class="text-center off_{{$i}}" style="display:none">Day off</div>
                </td>
                <td>
                    <input type="text" name="timetable[{{strtolower(strftime('%a', $timestamp))}}][clockout]" class="form-control time"
                        value="18:00" required="required">
                    <div class="text-center off_{{$i}}" style="display:none">Day off</div>
                </td>
                <td>
                    <div class="col-xs-6">
                        <input type="number" min="0" name="timetable[{{strtolower(strftime('%a', $timestamp))}}][break]"
                            class="form-control" value="1" required="required">
                        <div class="text-center off_{{$i}}" style="display:none">Day off</div>
                    </div>
                    <div class="col-xs-6 pa-10 hour_{{$i}}">
                        Hour(s)
                    </div>
                </td>
                </tr>
                <?php $timestamp = strtotime('+1 day', $timestamp)?>
                @endfor
        </tbody>
    </table>
</div>
<div class="text-center">
    <button class="btn btn-lg btn-success" type="submit"><i class="glyphicon glyphicon-ok-sign"></i> Save </button>
</div>
<br>
{!! Form::close() !!}

@section('css')
<style>
    .table-compact td {
        padding: 10px !important;
    }
</style>
@append
@section('js')
<script>

    var checkOffDays = function () {
        $.each($('.day_off'), function (i, el) {
            var elements_names = [
                "[name='" + el.name + "[clockin]'",
                "[name='" + el.name + "[clockout]'",
                "[name='" + el.name + "[break]'"
            ]

            $.each(elements_names, function (k, el_name) {
                var elm = $(el_name);
                if ($(el).prop('checked')) {
                    $('.off_' + i).show();
                    $('.hour_' + i).hide();
                    $(elm).prop('disabled', true);

                    $(elm).hide()

                } else {
                    $('.off_' + i).hide();
                    $('.hour_' + i).show();
                    $(elm).prop('disabled', false);
                    $(elm).show()
                }
            })

        })
    }

    $(document).ready(function () {

        $('.day_off').click(function () {
            checkOffDays();
        })

        $('#work_mood').change(function () {
            if (this.value == 'per_hour') {
                $('#hourly_info').show();
                $('#salary_label').text('Cost per hour');
                $('#hours_per_month').prop('disabled',false);

            } else {
                $('#hourly_info').hide();
                $('#timetable').show();
                $('#salary_label').text('Basic Salary');
                $('#hours_per_month').prop('disabled',true);

            }
        })
        $('[name="flexable"]').change(function () {
            console.log(this.value);

            if (this.value == 'false') {
                $('#timetable').show();
            } else {
                $('#timetable').hide();
            }
        })
    })
</script>
@append
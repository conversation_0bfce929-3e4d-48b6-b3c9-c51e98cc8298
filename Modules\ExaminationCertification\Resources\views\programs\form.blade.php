<h5>Program Details</h5>
<hr>
<div class="form-group {{ $errors->has('code') ? 'has-error' : ''}}">
    {!! Form::label('code', 'Program Code', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::text('code', null, ['class' => 'form-control']) !!}
        {!! $errors->first('code', '<p class="help-block">:message</p>') !!}
    </div>
</div>


<div class="nav-tabs-custom">
    <ul class="nav nav-tabs">
        @foreach(config('app.locales') as $key => $language)
        <li @if($key == 0 ) class="active" @endif>
            <a aria-expanded="true" data-toggle="tab" href="#{{$language}}">
                {{strtoupper($language)}}
            </a>
        </li>
        @endforeach
    </ul>
    <div class="tab-content">
        @foreach(config('app.locales') as $key => $language)
        <div class="tab-pane clearfix @if($key == 0 ) active @endif" id="{{$language}}">
            <div class="col-md-12">
                <div class="form-group {{ $errors->has('translate.'.$language.'.title') ? 'has-error' : ''}}">
                    {!! Form::label('title', 'Title ['.$language.']', ['class' => 'control-label']) !!}
                
                    {!! Form::text('translate['.$language.'][title]', isset($program) && isset($program->translate($language)->title) ? $program->translate($language)->title : '' , ['class' => 'form-control' , 'placeholder' => 'title']) !!}
                    {!! $errors->first('translate.'.$language.'.title', '
                    <p class="help-block">
                        :message
                    </p>
                    ') !!}
                </div>

                <div class="form-group {{ $errors->has('translate.'.$language.'.description') ? 'has-error' : ''}} description" >
                    {!! Form::label('description', 'Content ['.$language.']', ['class' => 'control-label']) !!}
                    {!! Form::textarea('translate['.$language.'][description]',isset($program) && isset($program->translate($language)->description) ? $program->translate($language)->description : '' , ['class' => 'form-control']) !!}
                    {!! $errors->first('translate.'.$language.'.description', '
                    <p class="help-block">
                        :message
                    </p>
                    ') !!}
                </div>
            </div>
        </div>
        <!-- /.tab-pane -->
        @endforeach
    </div>
    <!-- /.tab-description -->
</div>

<div class="form-group {{ $errors->has('language') ? 'has-error' : ''}}">
    {!! Form::label('language', 'Program Language', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('language', config('app.locales') , null, ['class' => 'form-control']) !!}
        {!! $errors->first('language', '<p class="help-block">:message</p>') !!}
    </div>
</div>
<div class="form-group {{ $errors->has('require_interview') ? 'has-error' : ''}}">
    {!! Form::label('require_interview', 'Require Interview Before Registeration', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('require_interview', [1 => 'Has Interview' , 0 => 'Register Without Interview'] , null, ['class' => 'form-control']) !!}
        {!! $errors->first('require_interview', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group {{ $errors->has('status') ? 'has-error' : ''}}">
    {!! Form::label('status', 'Status', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('status', ['active' => 'Active' , 'suspended' => 'Suspended'] , null, ['class' => 'form-control']) !!}
        {!! $errors->first('status', '<p class="help-block">:message</p>') !!}
    </div>
</div>
<div class="form-group">
    <div class="col-md-offset-4 col-md-4">
        {!! Form::submit(isset($submitButtonText) ? $submitButtonText : 'Create', ['class' => 'btn btn-primary']) !!}
    </div>
</div>

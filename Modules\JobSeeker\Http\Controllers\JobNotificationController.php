<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Mo<PERSON>les\JobSeeker\Entities\JobNotificationSetup;
use Modules\JobSeeker\Entities\JobNotificationRecipient;
use Mo<PERSON>les\JobSeeker\Entities\JobNotificationRecipientEmail;
use <PERSON><PERSON>les\JobSeeker\Entities\JobSeeker;
use Mo<PERSON>les\JobSeeker\Entities\JobSeekerPersonalContact;
use Mo<PERSON><PERSON>\JobSeeker\Services\JobService;
use Modules\JobSeeker\Services\CategoryMappingService;

/*
Change context: Part of "Provider-aware notifications" refactor. This controller
handles notification setup CRUD operations using provider category IDs for
delivery. Related files: CategoryMappingService.php, JobNotificationSetup.php,
existing routes in routes.php.
*/

/**
 * JobNotificationController handles notification setup management with provider-aware
 * category mapping. Manages creation, updating, deletion, and retrieval of job
 * notification setups using provider-specific category IDs for accurate delivery.
 *
 * Purpose: Centralized notification setup management with provider category precision.
 * Inputs: HTTP requests with canonical category IDs converted to provider categories.
 * Outputs: JSON responses with notification setup data and status.
 * Side effects: Writes to notification tables, dispatches jobs, manages personal contacts.
 * Security: Requires job seeker authentication, validates ownership of setups.
 * Errors: Returns structured JSON errors with appropriate HTTP codes.
 * Dependencies: CategoryMappingService for category translation, JobService for dispatch.
 */
final class JobNotificationController extends Controller
{
    private CategoryMappingService $categoryMappingService;

    public function __construct(CategoryMappingService $categoryMappingService)
    {
        $this->categoryMappingService = $categoryMappingService;
    }

    /**
     * Store a new notification setup with provider category mapping.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * Purpose: Create notification setup using provider categories for delivery.
     * Side effects: Writes notification tables, syncs categories, dispatches initial job.
     * Errors: Returns 422 for validation, 409 for duplicates, 500 for system errors.
     */
    public function store(Request $request): JsonResponse
    {
        Log::info('JobNotificationController: store method entry.');
        Log::debug('JobNotificationController: store request data.', $request->all());

        try {
            // Add missing required fields automatically
            $requestData = $request->all();
            $requestData['job_seeker_id'] = auth()->id();
            $requestData['provider_name'] = 'all'; // Default to all providers
            
            $validatedData = validator($requestData, [
                'job_seeker_id' => 'required|integer|exists:job_seekers,id',
                'name' => 'required|string|max:255',
                'provider_name' => 'required|string|in:acbar,jobs.af,all',
                'categories' => 'required|array|min:1',
                'categories.*' => 'integer|exists:job_categories,id',
                'recipients' => 'required|array|min:1',
                'recipients.*.email' => 'required|email',
                'recipients.*.name' => 'nullable|string|max:255',
                'bypass_duplicate_check' => 'sometimes|boolean',
                'receive_push_notifications' => 'sometimes|boolean',
            ])->validate();
            Log::info('JobNotificationController: store validation passed.');
        } catch (ValidationException $e) {
            Log::error('JobNotificationController: store validation failed.', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json(['success' => false, 'message' => 'Validation failed.', 'errors' => $e->errors()], 422);
        }

        $jobSeekerId = (int) $validatedData['job_seeker_id'];
        $canonicalCategoryIds = $validatedData['categories'];
        $providerName = $validatedData['provider_name'];
        $recipientEmailsFromRequest = array_column($validatedData['recipients'], 'email');
        
        // Map canonical categories to provider categories
        $providerCategoryMapping = [];
        if ($providerName !== 'all') {
            $providerCategoryMapping = $this->categoryMappingService->mapCanonicalToProviderCategoryIds(
                $canonicalCategoryIds, 
                $providerName
            );
            
            if (empty($providerCategoryMapping)) {
                Log::warning('JobNotificationController: No provider categories found for canonical categories', [
                    'canonical_category_ids' => $canonicalCategoryIds,
                    'provider_name' => $providerName,
                    'job_seeker_id' => $jobSeekerId
                ]);
                return response()->json([
                    'success' => false,
                    'message' => "No matching categories found for provider '{$providerName}'. Please contact support."
                ], 422);
            }
        }
        
        // Check if this is the user's first notification setup
        $existingSetupsCount = JobNotificationSetup::where('job_seeker_id', $jobSeekerId)->count();
        $isFirstSetup = $existingSetupsCount === 0;
        Log::info('JobNotificationController: Checked existing setups count.', [
            'job_seeker_id' => $jobSeekerId,
            'existing_setups_count' => $existingSetupsCount,
            'is_first_setup' => $isFirstSetup
        ]);
       
        // Check for duplicate setup before creating a new one (unless bypassed)
        $bypassDuplicateCheck = $validatedData['bypass_duplicate_check'] ?? false;
        if (!$bypassDuplicateCheck) {
            $providerCategoryIds = array_keys($providerCategoryMapping);
            $duplicateName = $this->checkForDuplicateNotificationSetup(
                $jobSeekerId, 
                $providerCategoryIds, 
                $recipientEmailsFromRequest
            );
            
            if ($duplicateName) {
                Log::warning('JobNotificationController: Attempt to create a duplicate notification setup.', [
                    'job_seeker_id' => $jobSeekerId,
                    'provider_categories' => $providerCategoryIds,
                    'recipients' => $recipientEmailsFromRequest,
                    'existing_setup_name' => $duplicateName
                ]);
                return response()->json([
                    'success' => false,
                    'message' => "A similar notification setup named '{$duplicateName}' already exists. Please modify your selection or name to create a unique setup.",
                    'allow_override' => true,
                    'duplicate_setup_name' => $duplicateName
                ], 409); // 409 Conflict
            }
        } else {
            Log::info('JobNotificationController: Duplicate check bypassed by user request.', [
                'job_seeker_id' => $jobSeekerId,
                'setup_name' => $validatedData['name']
            ]);
        }
    
        DB::beginTransaction();
        try {
            $setup = new JobNotificationSetup([
                'job_seeker_id' => $jobSeekerId,
                'name' => $validatedData['name'],
                'provider_name' => $providerName,
                'category_count' => count($canonicalCategoryIds), // Save category count
                'is_active' => true, // New setups are active by default
                'last_notified_at' => null, // New setup, not notified yet
                'receive_push_notifications' => $validatedData['receive_push_notifications'] ?? false,
            ]);
            $setup->save();
            Log::info('JobNotificationController: Notification setup core details saved.', [
                'setup_id' => $setup->id, 
                'name' => $setup->name, 
                'category_count' => $setup->category_count,
                'provider_name' => $setup->provider_name
            ]);

            // Sync canonical categories for display purposes
            $setup->categories()->sync($canonicalCategoryIds);
            Log::info('JobNotificationController: Canonical categories synced for setup.', [
                'setup_id' => $setup->id, 
                'canonical_category_ids' => $canonicalCategoryIds
            ]);

            // Sync provider categories for delivery (only for specific providers)
            if (!empty($providerCategoryMapping)) {
                $setup->providerCategories()->sync($providerCategoryMapping);
                Log::info('JobNotificationController: Provider categories synced for setup.', [
                    'setup_id' => $setup->id, 
                    'provider_category_ids' => array_keys($providerCategoryMapping),
                    'provider_name' => $providerName
                ]);
            }

            // Create or update recipients and associate them
            $recipientModels = [];
            foreach ($validatedData['recipients'] as $recipientData) {
                Log::debug('JobNotificationController: Processing recipient for store.', [
                    'setup_id' => $setup->id, 
                    'recipient_data' => $recipientData
                ]);
                $recipientEmailModel = JobNotificationRecipientEmail::firstOrCreate(
                    ['email' => strtolower(trim($recipientData['email']))],
                    ['name' => $recipientData['name'] ?? null]
                );

                $recipient = JobNotificationRecipient::firstOrCreate(
                    ['setup_id' => $setup->id, 'recipient_email_id' => $recipientEmailModel->id],
                    [
                        'email' => $recipientEmailModel->email, // Store email directly for convenience
                        'name' => $recipientData['name'] ?? null, 
                        'is_active' => true
                    ]
                );
                $recipientModels[] = $recipient;
                Log::info('JobNotificationController: Recipient linked to setup.', [
                    'setup_id' => $setup->id, 
                    'recipient_id' => $recipient->id, 
                    'email' => $recipient->email
                ]);
            }
            Log::info('JobNotificationController: All recipients processed for setup.', [
                'setup_id' => $setup->id, 
                'recipient_count' => count($recipientModels)
            ]);

            // Persist recipients as personal contacts
            $this->persistRecipientsAsPersonalContacts($jobSeekerId, $validatedData['recipients']);
            Log::info('JobNotificationController: Recipients persisted as personal contacts.', [
                'setup_id' => $setup->id, 
                'job_seeker_id' => $jobSeekerId
            ]);

            DB::commit();
            Log::info('JobNotificationController: Notification setup created successfully.', ['setup_id' => $setup->id]);

            // Dispatch a job to send initial notifications (e.g., jobs from last 5 days)
            $jobService = app(JobService::class);
            $jobService->sendInitialNotificationForSetup($setup);
            Log::info('JobNotificationController: Dispatched initial notification job for setup.', ['setup_id' => $setup->id]);

            return response()->json([
                'success' => true,
                'message' => 'Notification setup created successfully!',
                'setup' => $setup->load('categories', 'providerCategories', 'recipients.recipientEmail'),
                'is_first_setup' => $isFirstSetup
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('JobNotificationController: Error storing notification setup.', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to create notification setup: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an existing notification setup with provider category mapping.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * Purpose: Update notification setup using provider categories for delivery.
     * Side effects: Updates notification tables, syncs categories, manages recipients.
     * Errors: Returns 404 for not found, 403 for unauthorized, 422 for validation.
     */
    public function update(Request $request, int $id): JsonResponse
    {
        Log::info("JobNotificationController: update method entry. ID: {$id}");
        Log::debug('JobNotificationController: update request data.', $request->all());

        try {
            $setup = JobNotificationSetup::findOrFail($id);
        } catch (ModelNotFoundException $e) {
            Log::error('JobNotificationController: update - Setup not found.', [
                'setup_id' => $id, 
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => 'Notification setup not found.'], 404);
        }
        
        $jobSeeker = Auth::guard('job_seeker')->user();
        if (!$jobSeeker || $setup->job_seeker_id !== $jobSeeker->id) {
            Log::warning('JobNotificationController: update - Unauthorized attempt.', [
                'setup_id' => $id,
                'setup_owner_id' => $setup->job_seeker_id,
                'attempted_by_job_seeker_id' => $jobSeeker ? $jobSeeker->id : 'Guest'
            ]);
            return response()->json(['success' => false, 'message' => 'Unauthorized to update this setup.'], 403);
        }

        try {
            // Add missing required fields automatically
            $requestData = $request->all();
            $requestData['job_seeker_id'] = auth()->id();
            if (!isset($requestData['provider_name'])) {
                $requestData['provider_name'] = 'all'; // Default to all providers
            }
            
            $validatedData = validator($requestData, [
                'job_seeker_id' => 'required|integer|exists:job_seekers,id',
                'name' => 'sometimes|required|string|max:255',
                'provider_name' => 'sometimes|string|in:acbar,jobs.af,all',
                'categories' => 'sometimes|required|array|min:1',
                'categories.*' => 'integer|exists:job_categories,id',
                'recipients' => 'sometimes|required|array|min:1',
                'recipients.*.email' => 'required|email',
                'recipients.*.name' => 'nullable|string|max:255',
                'is_active' => 'sometimes|boolean',
                'bypass_duplicate_check' => 'sometimes|boolean',
                'receive_push_notifications' => 'sometimes|boolean',
            ])->validate();
            Log::info('JobNotificationController: update validation passed.', ['setup_id' => $id]);
        } catch (ValidationException $e) {
            Log::error('JobNotificationController: update validation failed.', [
                'setup_id' => $id,
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json(['success' => false, 'message' => 'Validation failed.', 'errors' => $e->errors()], 422);
        }
        
        if ((int)$validatedData['job_seeker_id'] !== $jobSeeker->id) {
            Log::error('JobNotificationController: update - Mismatch in job_seeker_id.', [
                'setup_id' => $id,
                'validated_job_seeker_id' => $validatedData['job_seeker_id'],
                'authenticated_job_seeker_id' => $jobSeeker->id
            ]);
            return response()->json(['success' => false, 'message' => 'Invalid job seeker ID provided.'], 400);
        }

        // Get current categories or use existing
        $canonicalCategoryIds = $validatedData['categories'] ?? $setup->categories->pluck('id')->toArray();
        $providerName = $validatedData['provider_name'] ?? $setup->provider_name;
        $recipientEmailsFromRequest = isset($validatedData['recipients']) ? 
            array_column($validatedData['recipients'], 'email') : 
            $setup->recipients->pluck('email')->toArray();

        // Map canonical categories to provider categories if provider changed or categories changed
        $providerCategoryMapping = [];
        if ($providerName !== 'all' && isset($validatedData['categories'])) {
            $providerCategoryMapping = $this->categoryMappingService->mapCanonicalToProviderCategoryIds(
                $canonicalCategoryIds, 
                $providerName
            );
            
            if (empty($providerCategoryMapping)) {
                Log::warning('JobNotificationController: No provider categories found for canonical categories during update', [
                    'canonical_category_ids' => $canonicalCategoryIds,
                    'provider_name' => $providerName,
                    'setup_id' => $id
                ]);
                return response()->json([
                    'success' => false,
                    'message' => "No matching categories found for provider '{$providerName}'. Please contact support."
                ], 422);
            }
        }

        // Check for duplicate setup before updating (unless bypassed)
        $bypassDuplicateCheck = $validatedData['bypass_duplicate_check'] ?? false;
        if (!$bypassDuplicateCheck && (isset($validatedData['categories']) || isset($validatedData['recipients']))) {
            // Use provider categories for duplicate check
            $providerCategoryIds = !empty($providerCategoryMapping) ? 
                array_keys($providerCategoryMapping) : 
                $setup->providerCategories->pluck('id')->toArray();
                
            $duplicateName = $this->checkForDuplicateNotificationSetup(
                $jobSeeker->id, 
                $providerCategoryIds, 
                $recipientEmailsFromRequest, 
                $setup->id
            );
            
            if ($duplicateName) {
                Log::warning('JobNotificationController: Attempt to update to a duplicate notification setup.', [
                    'setup_id_to_update' => $setup->id,
                    'job_seeker_id' => $jobSeeker->id,
                    'provider_categories' => $providerCategoryIds,
                    'recipients' => $recipientEmailsFromRequest,
                    'existing_setup_name' => $duplicateName
                ]);
                return response()->json([
                    'success' => false,
                    'message' => "A similar notification setup named '{$duplicateName}' already exists. Please modify your selection or name to create a unique setup.",
                    'allow_override' => true,
                    'duplicate_setup_name' => $duplicateName
                ], 409); // 409 Conflict
            }
        } else {
            Log::info('JobNotificationController: Duplicate check bypassed during update.', [
                'setup_id' => $setup->id,
                'job_seeker_id' => $jobSeeker->id
            ]);
        }

        DB::beginTransaction();
        try {
            $setup->name = $validatedData['name'] ?? $setup->name;
            $setup->provider_name = $providerName;
            $setup->is_active = $validatedData['is_active'] ?? $setup->is_active;
            $setup->receive_push_notifications = $validatedData['receive_push_notifications'] ?? $setup->receive_push_notifications;
            $setup->category_count = count($canonicalCategoryIds);
            $setup->save();
            Log::info('JobNotificationController: Notification setup core details updated.', [
                'setup_id' => $setup->id, 
                'name' => $setup->name, 
                'category_count' => $setup->category_count, 
                'is_active' => $setup->is_active,
                'provider_name' => $setup->provider_name
            ]);

            // Sync canonical categories if provided
            if (isset($validatedData['categories'])) {
                $setup->categories()->sync($canonicalCategoryIds);
                Log::info('JobNotificationController: Canonical categories synced for updated setup.', [
                    'setup_id' => $setup->id, 
                    'canonical_category_ids' => $canonicalCategoryIds
                ]);
            }

            // Sync provider categories if mapping was performed
            if (!empty($providerCategoryMapping)) {
                $setup->providerCategories()->sync($providerCategoryMapping);
                Log::info('JobNotificationController: Provider categories synced for updated setup.', [
                    'setup_id' => $setup->id, 
                    'provider_category_ids' => array_keys($providerCategoryMapping),
                    'provider_name' => $providerName
                ]);
            }

            // Handle recipients update if provided
            if (isset($validatedData['recipients'])) {
                $this->updateSetupRecipients($setup, $validatedData['recipients']);
                
                // Persist recipients as personal contacts
                $this->persistRecipientsAsPersonalContacts($jobSeeker->id, $validatedData['recipients']);
                Log::info('JobNotificationController: Recipients persisted as personal contacts during update.', [
                    'setup_id' => $setup->id, 
                    'job_seeker_id' => $jobSeeker->id
                ]);
            }

            DB::commit();
            Log::info('JobNotificationController: Notification setup updated successfully.', ['setup_id' => $setup->id]);
            return response()->json([
                'success' => true,
                'message' => 'Notification setup updated successfully!',
                'setup' => $setup->fresh()->load('categories', 'providerCategories', 'recipients.recipientEmail')
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('JobNotificationController: Error updating notification setup.', [
                'setup_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to update notification setup: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a notification setup.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * Purpose: Remove notification setup and related data.
     * Side effects: Deletes from notification tables within transaction.
     * Errors: Returns 401 for unauthorized, 404 for not found.
     */
    public function delete(Request $request, int $id): JsonResponse
    {
        Log::info('JobNotificationController: delete method entry.', ['setup_id' => $id]);
        try {
            $jobSeekerId = Auth::guard('job_seeker')->id();
            if (!$jobSeekerId) {
                Log::warning('JobNotificationController: delete - Unauthenticated access attempt.');
                return response()->json(['success' => false, 'message' => 'Unauthenticated.'], 401);
            }

            $setup = JobNotificationSetup::where('id', $id)
                                       ->where('job_seeker_id', $jobSeekerId)
                                       ->firstOrFail();
            
            Log::debug('JobNotificationController: Found notification setup for deletion.', [
                'setup_id' => $setup->id, 
                'job_seeker_id' => $jobSeekerId
            ]);

            DB::beginTransaction();
            // Delete related recipients first to avoid foreign key constraints
            $setup->recipients()->delete();
            Log::info('JobNotificationController: Deleted recipients for setup.', ['setup_id' => $setup->id]);
            // Then delete the setup itself
            $setup->delete();
            DB::commit();

            // Check if there are any remaining setups for this job seeker
            $remainingSetups = JobNotificationSetup::where('job_seeker_id', $jobSeekerId)->count();
            
            Log::info('JobNotificationController: Notification setup deleted successfully.', [
                'setup_id' => $id, 
                'job_seeker_id' => $jobSeekerId,
                'remaining_setups' => $remainingSetups
            ]);
            
            return response()->json([
                'success' => true, 
                'message' => 'Notification setup deleted successfully.',
                'remaining_setups' => $remainingSetups,
                'no_setups_left' => $remainingSetups === 0
            ]);

        } catch (ModelNotFoundException $e) {
            Log::warning('JobNotificationController: Notification setup not found or not owned by user for deletion.', [
                'setup_id' => $id,
                'job_seeker_id' => $jobSeekerId ?? null,
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => 'Notification setup not found or you do not have permission to delete it.'], 404);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('JobNotificationController: Error deleting notification setup.', [
                'setup_id' => $id,
                'job_seeker_id' => $jobSeekerId ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['success' => false, 'message' => 'Failed to delete notification setup: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Bulk delete notification setups.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * Purpose: Delete multiple notification setups in one operation.
     * Side effects: Deletes from notification tables within transaction.
     * Errors: Returns 401 for unauthorized, 404 for not found, 422 for validation.
     */
    public function bulkDelete(Request $request): JsonResponse
    {
        Log::info('JobNotificationController: bulkDelete method entry.');
        
        try {
            $jobSeekerId = Auth::guard('job_seeker')->id();
            if (!$jobSeekerId) {
                Log::warning('JobNotificationController: bulkDelete - Unauthenticated access attempt.');
                return response()->json(['success' => false, 'message' => 'Unauthenticated.'], 401);
            }

            $validatedData = $request->validate([
                'setup_ids' => 'required|array|min:1',
                'setup_ids.*' => 'integer|exists:job_notification_setups,id',
            ]);
            
            $setupIds = $validatedData['setup_ids'];
            Log::info('JobNotificationController: bulkDelete validation passed.', [
                'setup_ids' => $setupIds, 
                'job_seeker_id' => $jobSeekerId
            ]);

            // Verify all setups belong to the authenticated user
            $setups = JobNotificationSetup::where('job_seeker_id', $jobSeekerId)
                                        ->whereIn('id', $setupIds)
                                        ->get();
            
            if ($setups->count() !== count($setupIds)) {
                Log::warning('JobNotificationController: bulkDelete - Some setups not found or not owned by user.', [
                    'requested_ids' => $setupIds,
                    'found_ids' => $setups->pluck('id')->toArray(),
                    'job_seeker_id' => $jobSeekerId
                ]);
                return response()->json(['success' => false, 'message' => 'Some notification setups not found or you do not have permission to delete them.'], 404);
            }

            DB::beginTransaction();
            
            $deletedCount = 0;
            foreach ($setups as $setup) {
                try {
                    // Delete related recipients first
                    $setup->recipients()->delete();
                    // Delete the setup
                    $setup->delete();
                    $deletedCount++;
                    Log::debug('JobNotificationController: Deleted notification setup.', ['setup_id' => $setup->id]);
                } catch (\Exception $e) {
                    Log::error('JobNotificationController: Error deleting individual setup during bulk operation.', [
                        'setup_id' => $setup->id,
                        'error' => $e->getMessage()
                    ]);
                    // Continue with other setups, don't fail the entire operation
                }
            }
            
            DB::commit();

            Log::info('JobNotificationController: Bulk delete notification setups completed successfully.', [
                'deleted_count' => $deletedCount,
                'requested_count' => count($setupIds),
                'job_seeker_id' => $jobSeekerId
            ]);
            
            return response()->json([
                'success' => true,
                'message' => "Successfully deleted {$deletedCount} notification setup(s).",
                'deleted_count' => $deletedCount
            ]);

        } catch (ValidationException $e) {
            Log::error('JobNotificationController: bulkDelete validation failed.', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);
            return response()->json(['success' => false, 'message' => 'Validation failed.', 'errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('JobNotificationController: Error during bulk delete notification setups.', [
                'job_seeker_id' => $jobSeekerId ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['success' => false, 'message' => 'Failed to delete notification setups: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get a notification setup by ID.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     *
     * Purpose: Retrieve notification setup with related data for display.
     * Side effects: Reads from notification tables with eager loading.
     * Errors: Returns 401 for unauthorized, 404 for not found.
     */
    public function get(Request $request, int $id): JsonResponse
    {
        Log::info('JobNotificationController: get method entry.', ['setup_id' => $id]);
        try {
            $jobSeekerId = Auth::guard('job_seeker')->id();
            if (!$jobSeekerId) {
                Log::warning('JobNotificationController: get - Unauthenticated access attempt.');
                return response()->json(['success' => false, 'message' => 'Unauthenticated.'], 401);
            }

            $setup = JobNotificationSetup::with(['categories', 'providerCategories', 'recipients.recipientEmail'])
                ->where('job_seeker_id', $jobSeekerId)
                ->findOrFail($id);
            
            Log::info('JobNotificationController: Notification setup retrieved successfully.', [
                'setup_id' => $setup->id, 
                'job_seeker_id' => $jobSeekerId
            ]);
            
            // Transform recipients to include email directly for frontend convenience
            $transformedSetup = $setup->toArray();
            $transformedSetup['recipients'] = $setup->recipients->map(function ($recipient) {
                Log::debug('JobNotificationController: Transforming recipient for get response.', [
                    'recipient_id' => $recipient->id, 
                    'email' => $recipient->email
                ]);
                return [
                    'id' => $recipient->id,
                    'setup_id' => $recipient->setup_id,
                    'recipient_email_id' => $recipient->recipient_email_id,
                    'email' => $recipient->email,
                    'name' => $recipient->name,
                    'is_active' => $recipient->is_active,
                    'created_at' => $recipient->created_at,
                    'updated_at' => $recipient->updated_at,
                ];
            })->toArray();

            return response()->json([
                'success' => true,
                'setup' => $transformedSetup
            ]);
        } catch (ModelNotFoundException $e) {
            Log::warning('JobNotificationController: Notification setup not found or not owned by user.', [
                'setup_id' => $id,
                'job_seeker_id' => $jobSeekerId ?? null,
                'error' => $e->getMessage()
            ]);
            return response()->json(['success' => false, 'message' => 'Notification setup not found or you do not have permission to access it.'], 404);
        } catch (\Exception $e) {
            Log::error('JobNotificationController: Error getting notification setup.', [
                'setup_id' => $id,
                'job_seeker_id' => $jobSeekerId ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving the notification setup.'
            ], 500);
        }
    }

    /**
     * Check for duplicate notification setup using provider category IDs.
     *
     * @param int $jobSeekerId
     * @param array $providerCategoryIds
     * @param array $recipientEmails
     * @param int|null $excludeSetupId
     * @return string|null
     *
     * Purpose: Detect duplicate setups using provider categories for comparison.
     * Side effects: Reads notification setups with provider categories.
     * Errors: Returns null on comparison errors or no duplicates found.
     */
    private function checkForDuplicateNotificationSetup(
        int $jobSeekerId, 
        array $providerCategoryIds, 
        array $recipientEmails, 
        ?int $excludeSetupId = null
    ): ?string {
        $existingSetupsQuery = JobSeeker::findOrFail($jobSeekerId)
            ->notificationSetups()
            ->with('providerCategories', 'recipients')
            ->where('is_active', true);
            
        if ($excludeSetupId) {
            $existingSetupsQuery->where('id', '!=', $excludeSetupId);
        }
        
        $existingSetups = $existingSetupsQuery->get();
        $normalizedRecipients = array_map('strtolower', $recipientEmails);
        
        Log::info('JobNotificationController: Starting duplicate check with provider categories', [
            'job_seeker_id' => $jobSeekerId,
            'new_provider_categories' => $providerCategoryIds,
            'new_recipients' => $recipientEmails,
            'existing_setups_count' => $existingSetups->count()
        ]);
        
        foreach ($existingSetups as $setup) {
            $existingProviderCategoryIds = $setup->providerCategories->pluck('id')->map(function($id) {
                return (int) $id;
            })->toArray();
            
            $existingRecipientEmails = $setup->recipients->pluck('email')->toArray();
            if (empty($existingRecipientEmails)) {
                $existingRecipientEmails = [$setup->jobSeeker->email];
            }
            $existingRecipientEmails = array_map('strtolower', $existingRecipientEmails);
            
            // Ensure new provider category IDs are also integers for proper comparison
            $newProviderCategoryIds = array_map('intval', $providerCategoryIds);
            
            // Exact duplicate detection: require identical provider categories AND identical recipients
            $isExactCategoryMatch = count($newProviderCategoryIds) === count($existingProviderCategoryIds) && 
                                   count(array_intersect($newProviderCategoryIds, $existingProviderCategoryIds)) === count($newProviderCategoryIds);
            $isExactRecipientMatch = count($normalizedRecipients) === count($existingRecipientEmails) && 
                                    count(array_intersect($normalizedRecipients, $existingRecipientEmails)) === count($normalizedRecipients);
            
            Log::debug('JobNotificationController: Comparing with existing setup using provider categories', [
                'existing_setup_id' => $setup->id,
                'existing_setup_name' => $setup->name,
                'new_provider_categories' => $newProviderCategoryIds,
                'existing_provider_categories' => $existingProviderCategoryIds,
                'new_recipients' => $normalizedRecipients,
                'existing_recipients' => $existingRecipientEmails,
                'is_exact_category_match' => $isExactCategoryMatch,
                'is_exact_recipient_match' => $isExactRecipientMatch,
                'new_category_count' => count($newProviderCategoryIds),
                'existing_category_count' => count($existingProviderCategoryIds),
                'new_recipient_count' => count($normalizedRecipients),
                'existing_recipient_count' => count($existingRecipientEmails),
            ]);
            
            if ($isExactCategoryMatch && $isExactRecipientMatch) {
                Log::info('Exact duplicate notification setup detected using provider categories', [
                    'job_seeker_id' => $jobSeekerId,
                    'duplicate_setup_id' => $setup->id,
                    'duplicate_setup_name' => $setup->name,
                    'is_exact_category_match' => $isExactCategoryMatch,
                    'is_exact_recipient_match' => $isExactRecipientMatch,
                    'new_provider_categories' => $newProviderCategoryIds,
                    'existing_provider_categories' => $existingProviderCategoryIds,
                    'new_recipients' => $recipientEmails,
                    'existing_recipients' => $existingRecipientEmails,
                ]);
                return $setup->name;
            }
        }
        
        Log::info('JobNotificationController: No exact duplicate found using provider categories');
        return null;
    }

    /**
     * Update recipients for a notification setup.
     *
     * @param JobNotificationSetup $setup
     * @param array $recipients
     * @return void
     *
     * Purpose: Sync recipients for notification setup with add/update/remove logic.
     * Side effects: Creates/updates/deletes recipient records.
     * Errors: Logs warnings for missing recipient details but continues processing.
     */
    private function updateSetupRecipients(JobNotificationSetup $setup, array $recipients): void
    {
        $recipientEmailsFromRequest = array_column($recipients, 'email');
        $recipientDataMap = collect($recipients)->keyBy(fn($item) => strtolower(trim($item['email'])));

        // Get current recipient emails for this setup
        $currentRecipientEmails = $setup->recipients()->pluck('email')->map('strtolower')->all();
        
        Log::debug('JobNotificationController: Current vs New Recipients for Update', [
            'setup_id' => $setup->id,
            'current_emails' => $currentRecipientEmails,
            'new_emails_from_request' => $recipientEmailsFromRequest
        ]);

        // Emails to add: in new but not in current
        $emailsToAdd = array_diff($recipientEmailsFromRequest, $currentRecipientEmails);
        // Emails to remove: in current but not in new
        $emailsToRemove = array_diff($currentRecipientEmails, $recipientEmailsFromRequest);

        Log::debug('JobNotificationController: Recipient Emails to Add/Remove', [
            'setup_id' => $setup->id,
            'emails_to_add' => $emailsToAdd,
            'emails_to_remove' => $emailsToRemove
        ]);

        // Remove recipients no longer in the list
        if (!empty($emailsToRemove)) {
            JobNotificationRecipient::where('setup_id', $setup->id)
                ->whereIn('email', $emailsToRemove)
                ->delete();
            Log::info('JobNotificationController: Recipients removed from setup.', [
                'setup_id' => $setup->id, 
                'removed_emails' => $emailsToRemove
            ]);
        }

        // Add new recipients
        foreach ($emailsToAdd as $email) {
            $email = strtolower(trim($email));
            $recipientDetails = $recipientDataMap->get($email);
            if ($recipientDetails) {
                $recipientEmailModel = JobNotificationRecipientEmail::firstOrCreate(
                    ['email' => $email],
                    ['name' => $recipientDetails['name'] ?? null]
                );
                JobNotificationRecipient::create([
                    'setup_id' => $setup->id,
                    'recipient_email_id' => $recipientEmailModel->id,
                    'email' => $email, // Store email directly
                    'name' => $recipientDetails['name'] ?? null,
                    'is_active' => true,
                ]);
                Log::info('JobNotificationController: Recipient added to setup.', [
                    'setup_id' => $setup->id, 
                    'added_email' => $email
                ]);
            } else {
                Log::warning('JobNotificationController: Recipient details not found in map for email to add.', [
                    'setup_id' => $setup->id, 
                    'email' => $email
                ]);
            }
        }
        
        // Update existing recipients (name might change)
        foreach ($setup->recipients()->whereIn('email', $recipientEmailsFromRequest)->get() as $existingRecipient) {
            $emailKey = strtolower(trim($existingRecipient->email));
            if ($recipientDataMap->has($emailKey)) {
                $newName = $recipientDataMap->get($emailKey)['name'] ?? null;
                if ($existingRecipient->name !== $newName) {
                    $existingRecipient->name = $newName;
                    $existingRecipient->save();
                    Log::info('JobNotificationController: Recipient name updated.', [
                        'setup_id' => $setup->id, 
                        'recipient_id' => $existingRecipient->id, 
                        'new_name' => $newName
                    ]);
                }
            }
        }
    }

    /**
     * Persist recipient emails as personal contacts for the job seeker.
     * 
     * @param int $jobSeekerId
     * @param array $recipients
     * @return void
     *
     * Purpose: Save notification recipients as reusable personal contacts.
     * Side effects: Creates/updates personal contact records.
     * Errors: Logs errors for individual contacts but continues processing others.
     */
    private function persistRecipientsAsPersonalContacts(int $jobSeekerId, array $recipients): void
    {
        Log::info('JobNotificationController: persistRecipientsAsPersonalContacts method entry.', [
            'job_seeker_id' => $jobSeekerId,
            'recipients_count' => count($recipients)
        ]);

        foreach ($recipients as $recipientData) {
            $email = strtolower(trim($recipientData['email']));
            
            Log::debug('JobNotificationController: Processing recipient for personal contact persistence.', [
                'job_seeker_id' => $jobSeekerId,
                'email' => $email
            ]);

            try {
                // First ensure the recipient email exists
                $recipientEmailModel = JobNotificationRecipientEmail::firstOrCreate(
                    ['email' => $email],
                    ['name' => $recipientData['name'] ?? null]
                );

                // Check if personal contact already exists for this job seeker and email
                $existingPersonalContact = JobSeekerPersonalContact::where('job_seeker_id', $jobSeekerId)
                    ->where('recipient_email_id', $recipientEmailModel->id)
                    ->first();

                if (!$existingPersonalContact) {
                    // Create new personal contact
                    $personalContact = JobSeekerPersonalContact::create([
                        'job_seeker_id' => $jobSeekerId,
                        'recipient_email_id' => $recipientEmailModel->id,
                        'display_name' => $recipientData['name'] ?? null,
                        'notes' => 'Added from notification setup',
                    ]);

                    Log::info('JobNotificationController: Created new personal contact.', [
                        'job_seeker_id' => $jobSeekerId,
                        'email' => $email,
                        'personal_contact_id' => $personalContact->id
                    ]);
                } else {
                    // Update existing personal contact if name is provided and different
                    if (isset($recipientData['name']) && 
                        $recipientData['name'] && 
                        $existingPersonalContact->display_name !== $recipientData['name']) {
                        
                        $existingPersonalContact->display_name = $recipientData['name'];
                        $existingPersonalContact->save();

                        Log::info('JobNotificationController: Updated existing personal contact display name.', [
                            'job_seeker_id' => $jobSeekerId,
                            'email' => $email,
                            'personal_contact_id' => $existingPersonalContact->id,
                            'new_display_name' => $recipientData['name']
                        ]);
                    } else {
                        Log::debug('JobNotificationController: Personal contact already exists, no update needed.', [
                            'job_seeker_id' => $jobSeekerId,
                            'email' => $email,
                            'personal_contact_id' => $existingPersonalContact->id
                        ]);
                    }
                }
            } catch (\Exception $e) {
                Log::error('JobNotificationController: Error persisting recipient as personal contact.', [
                    'job_seeker_id' => $jobSeekerId,
                    'email' => $email,
                    'error' => $e->getMessage()
                ]);
                // Continue processing other recipients
            }
        }

        Log::info('JobNotificationController: Finished persisting recipients as personal contacts.', [
            'job_seeker_id' => $jobSeekerId,
            'recipients_processed' => count($recipients)
        ]);
    }

    /**
     * Get the notification setup limit for the current user.
     * 
     * Purpose: Retrieve maximum number of notification setups allowed per user.
     * Outputs: JSON response with current setup count and maximum allowed.
     * Side effects: Reads job_notification_setups table.
     * Errors: Returns default limit if configuration missing.
     */
    public function getSetupLimit(Request $request): JsonResponse
    {
        $userId = auth()->id();
        
        // Count current active setups for user
        $currentSetups = JobNotificationSetup::where('job_seeker_id', $userId)
            ->where('is_active', 1)
            ->count();
        
        // Default limit (configurable in future)
        $maxSetups = 10;
        
        return response()->json([
            'success' => true,
            'data' => [
                'current_setups' => $currentSetups,
                'max_setups' => $maxSetups,
                'remaining' => max(0, $maxSetups - $currentSetups),
                'can_add_more' => $currentSetups < $maxSetups
            ]
        ]);
    }

    /**
     * Get notification recipients including job seeker themselves and personal contacts.
     * 
     * Purpose: Provide recipient options for notification setup creation.
     * Outputs: JSON array of available recipients with email and career focus.
     * Side effects: Reads job_seekers and job_seeker_personal_contacts tables.
     * Errors: Returns job seeker only if personal contacts unavailable.
     */
    public function getRecipients(Request $request): JsonResponse
    {
        $userId = auth()->id();
        
        // Get job seeker details
        $jobSeeker = JobSeeker::find($userId);
        if (!$jobSeeker) {
            return response()->json([
                'success' => false,
                'message' => 'Job seeker not found.'
            ], 404);
        }

        $recipients = [];
        
        // Add job seeker themselves as first option
        $recipients[] = [
            'id' => 'self',
            'email' => $jobSeeker->email,
            'name' => $jobSeeker->name ?: 'Just Me',
            'relationship' => 'Self',
            'career_focus' => [], // Will be populated by frontend
            'application_metrics' => [
                'total_applications' => 0, // TODO: Implement metrics
                'last_activity' => null
            ],
            'is_self' => true
        ];

        // Get recipient emails from notification system (these are the actual contacts)
        $recipientEmails = \Modules\JobSeeker\Entities\JobNotificationRecipientEmail::where('is_active', 1)
            ->get();

        foreach ($recipientEmails as $recipientEmail) {
            // Skip if this is the job seeker's own email
            if ($recipientEmail->email === $jobSeeker->email) {
                continue;
            }
            
            $recipients[] = [
                'id' => $recipientEmail->id,
                'email' => $recipientEmail->email,
                'name' => $recipientEmail->name ?: $this->getDisplayNameFromEmail($recipientEmail->email),
                'relationship' => 'Contact',
                'career_focus' => [], // Will be populated by frontend
                'application_metrics' => [
                    'total_applications' => 0, // TODO: Implement metrics
                    'last_activity' => null
                ],
                'is_self' => false
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $recipients
        ]);
    }

    /**
     * Get canonical job categories for notification setup.
     * 
     * Purpose: Provide list of canonical categories for job filtering.
     * Outputs: JSON array of canonical categories with id, name, and description.
     * Side effects: Reads job_categories table where is_canonical = 1.
     * Errors: Returns empty array if no canonical categories found.
     */
    public function getCanonicalCategories(Request $request): JsonResponse
    {
        $categories = \Modules\JobSeeker\Entities\JobCategory::where('is_canonical', 1)
            ->orderBy('name')
            ->get(['id', 'name', 'description']);

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * Generate a friendly display name from an email address.
     * 
     * Purpose: Create user-friendly names when display name is not provided.
     * Inputs: Email address string.
     * Outputs: Formatted display name.
     * Side effects: None.
     */
    private function getDisplayNameFromEmail(string $email): string
    {
        $localPart = explode('@', $email)[0];
        
        // Replace common separators with spaces and title case
        $name = str_replace(['.', '_', '-', '+'], ' ', $localPart);
        $name = ucwords(strtolower($name));
        
        return $name ?: $email;
    }
}

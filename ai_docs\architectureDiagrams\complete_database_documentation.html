<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Itqan Database Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        header {
            background-color: #35495e;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        header h1 {
            margin: 0;
            font-size: 1.8rem;
        }
        
        .search-container {
            width: 100%;
            max-width: 400px;
            margin: 0 1rem;
        }
        
        .search-container input {
            width: 100%;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .timestamp {
            font-size: 0.8rem;
            color: #ddd;
        }
        
        nav {
            width: 280px;
            background-color: #fff;
            padding: 1rem 0;
            overflow-y: auto;
            border-right: 1px solid #ddd;
            position: sticky;
            top: 0;
            height: 100vh;
        }
        
        nav h2 {
            padding: 0 1rem;
            margin: 0 0 1rem 0;
            font-size: 1.2rem;
        }
        
        .tables-index {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .tables-index li {
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .tables-index li:hover {
            background-color: #f1f5f9;
        }
        
        .tables-index a {
            color: #3498db;
            text-decoration: none;
            display: block;
        }
        
        .tables-index a:hover {
            color: #2980b9;
        }
        
        .content {
            flex: 1;
            padding: 2rem;
        }
        
        .table-container {
            background-color: white;
            margin-bottom: 2rem;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
        }
        
        .table-container h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .table-container h4 {
            margin: 1.5rem 0 0.75rem;
            color: #3498db;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        
        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f1f5f9;
            color: #2c3e50;
            font-weight: 600;
        }
        
        tr:hover {
            background-color: #f9f9f9;
        }
        
        .primary-key {
            font-weight: bold;
            color: #e74c3c;
        }
        
        .foreign-key {
            font-weight: bold;
            color: #3498db;
        }
        
        ul {
            padding-left: 20px;
        }
        
        .fk-relationship {
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            nav {
                width: 100%;
                height: auto;
                position: relative;
            }
            
            .content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Itqan Database Documentation</h1>
        <div class="search-container">
            <input type="text" id="searchInput" placeholder="Search tables...">
        </div>
        <div class="timestamp">Generated on <span id="generationDate">Date</span></div>
    </header>
    
    <div class="container">
        <nav>
            <h2>Tables Index</h2>
            <ul class="tables-index" id="tablesIndex">
                <!-- Table links will be inserted here -->
                <li>Loading tables...</li>
            </ul>
        </nav>
        
        <div class="content">
            <div id="database-structure">
                <h2>Database Structure</h2>
                <p>
                    This document provides comprehensive documentation of the Itqan database.
                    Each table is presented with its structure, key relationships, and linked references.
                </p>
                <p>
                    Primary keys are marked in <span class="primary-key">red</span>, while foreign keys are marked in <span class="foreign-key">blue</span>.
                </p>
            </div>
            
            <div id="tables-section">
                <h2>Table Definitions</h2>
                
                <!-- TABLES_PLACEHOLDER -->
                
            </div>
        </div>
    </div>
    
    <script>
        // Set the generation date
        document.getElementById('generationDate').textContent = new Date().toLocaleString();
        
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableLinks = document.querySelectorAll('.tables-index li');
            
            tableLinks.forEach(function(item) {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
        
        // Generate the table index from the available tables
        function populateSidebar() {
            const tablesIndex = document.getElementById('tablesIndex');
            const tableContainers = document.querySelectorAll('.table-container');
            
            // Clear loading message
            tablesIndex.innerHTML = '';
            
            // Add each table to the index
            tableContainers.forEach(function(container) {
                const tableId = container.id;
                const tableName = container.querySelector('h3').textContent;
                
                const li = document.createElement('li');
                const a = document.createElement('a');
                a.href = '#' + tableId;
                a.textContent = tableName;
                
                li.appendChild(a);
                tablesIndex.appendChild(li);
            });
        }
        
        // Call populateSidebar after the page loads
        window.addEventListener('load', populateSidebar);
    </script>
</body>
</html> 
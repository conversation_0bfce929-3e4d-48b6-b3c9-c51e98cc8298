<?php

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\Classes;
use App\ClassProgram;
use App\ClassStudent;
use App\Program;
use App\ProgramLevel;
use App\StudentHefzPlan;
use App\StudentHefzReport;
use App\StudentProgramLevel;
use App\StudentRevisionPlan;
use App\StudentRevisionReport;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Role;
use App\Permission;
use App\Authorizable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Modules\RolePermission\Entities\PermissionAssign;
use App\Services\StudentLevelService;
use Illuminate\Support\Facades\Log;
use App\Student;
use App\StudentNouranyaReport;
use App\StudentNouranyaPlan;
use App\StudentIjazasanadMemorizationReport;
use App\IjazasanadMemorizationPlan;

class AddHalaqahStudenttoAnotherHalahController extends Controller
{
    protected $studentLevelService;

    public function __construct(StudentLevelService $studentLevelService)
    {
        $this->studentLevelService = $studentLevelService;
    }

    public function __invoke(Request $request)
    {
        $validator = $this->validate($request,
            [
                'student_id'         => 'required',
                'halaqah'            => 'required',
                'new_halaqah_id'     => 'required',
                'new_center_id'      => 'required',
                'new_program_id'     => 'required',
            ]
        );

        try {
            DB::beginTransaction();

            $studentId       = $request->student_id;
            $currentHalaqah  = $request->halaqah;
            $newHalaqah      = $request->new_halaqah_id;
            $newCenterId     = $request->new_center_id;
            $newProgramId    = $request->new_program_id;

            ClassStudent::where('student_id', $studentId)->forceDelete();

            $classProgram = ClassProgram::where('class_id', $newHalaqah)->first();
            if (!$classProgram) {
                if ($classProgram->program_id != $newProgramId) {
                    Log::warning("Program ID mismatch during halaqah transfer.", [
                        'student_id' => $studentId,
                        'new_halaqah_id' => $newHalaqah,
                        'requested_program_id' => $newProgramId,
                        'halaqah_program_id' => $classProgram->program_id
                    ]);
                }
                Log::error("No class program found for the new Halaqah.", ['new_halaqah_id' => $newHalaqah]);
                throw new \Exception("No class program found for the new Halaqah.");
            }

            $existingAdmission = Admission::withTrashed()->where('student_id', $studentId)->first();
            if (!$existingAdmission) {
                Log::error("No existing admission found for student ID.", ['student_id' => $studentId]);
                throw new \Exception("No existing admission found for student ID {$studentId}.");
            }
            if ($existingAdmission->trashed()) {
                $existingAdmission->restore();
                Log::info("Restored soft-deleted admission record during halaqah transfer.", ['admission_id' => $existingAdmission->id, 'student_id' => $studentId]);
            }

            $roles = auth()->user()->getRoleNames();
            $rolesString = $roles->implode(',');

            $criticalData = [
                'gender_id'       => $existingAdmission->gender_id,
                'student_email'   => $existingAdmission->student_email,
                'student_mobile'  => $existingAdmission->student_mobile,
                'date_of_birth'   => $existingAdmission->date_of_birth,
                'age'             => $existingAdmission->age,
                'organization_id' => $existingAdmission->organization_id ?? config('organization_id'),
                'old_center_id'   => $existingAdmission->center_id,
                'old_program_id'  => $existingAdmission->program_id,
                'old_class_id'    => $existingAdmission->class_id,
            ];

            Log::info("Updating admission record for halaqah transfer.", [
                'admission_id' => $existingAdmission->id,
                'student_id' => $studentId,
                'new_program_id' => $newProgramId,
                'new_center_id' => $newCenterId,
                'new_halaqah_id' => $newHalaqah,
                'critical_data_keys' => array_keys($criticalData)
            ]);
            $existingAdmission->update(array_merge([
                'program_id'   => $newProgramId,
                'center_id'    => $newCenterId,
                'class_id'     => $newHalaqah,
                'created_by'   => auth()->user()->id,
                'status'       => 'active',
                'creator_role' => $rolesString,
            ], $criticalData));

            $existingAdmission->programs()->sync([
                $newProgramId => ['created_at' => Carbon::now()]
            ]);
            Log::info("Synced program for admission.", ['admission_id' => $existingAdmission->id, 'program_id' => $newProgramId]);

            $newProgram = Program::find($newProgramId);
            if ($newProgram) {
                $student = Student::find($studentId);
                if ($student) {
                    try {
                        Log::info("Attempting to assign initial level via service.", ['student_id' => $studentId, 'new_halaqah_id' => $newHalaqah, 'program_id' => $newProgram->id]);
                        $this->studentLevelService->assignInitialLevel($student, $newHalaqah, $newProgram);
                        Log::info("Successfully assigned initial level.", ['student_id' => $studentId, 'new_halaqah_id' => $newHalaqah]);
                    } catch (\Exception $e) {
                        Log::error("Failed to assign level during halaqah transfer: " . $e->getMessage(), ['student_id' => $studentId, 'new_halaqah_id' => $newHalaqah, 'exception' => $e]);
                        throw $e;
                    }
                } else {
                    Log::error('Student not found during halaqah transfer level assignment.', ['student_id' => $studentId]);
                    throw new \Exception("Student not found for level assignment.");
                }
            } else {
                Log::error('Program not found for level assignment.', ['program_id' => $newProgramId]);
                throw new \Exception("Program not found for level assignment.");
            }

            $currentClassName = optional(Classes::find($currentHalaqah))->name ?? 'Unknown';
            $newClassName     = optional(Classes::find($newHalaqah))->name ?? 'Unknown';
            $deleteReason     = "Student transferred from Halaqah {$currentClassName} to Halaqah {$newClassName}";
            Log::info("Generated delete reason for report/plan cleanup.", ['reason' => $deleteReason]);

            Log::info("Soft-deleting reports/plans for old halaqah.", ['student_id' => $studentId, 'old_halaqah_id' => $currentHalaqah]);
            StudentHefzReport::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            StudentHefzPlan::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            StudentRevisionReport::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            StudentRevisionPlan::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            StudentNouranyaReport::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            StudentNouranyaPlan::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            StudentIjazasanadMemorizationReport::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            IjazasanadMemorizationPlan::where('student_id', $studentId)
                ->where('class_id', $currentHalaqah)
                ->update([
                    'delete_reason' => $deleteReason,
                    'deleted_at'    => Carbon::now()
                ]);
            Log::info("Completed soft-deletion of reports/plans.");

            Log::info("Creating new ClassStudent record.", ['student_id' => $studentId, 'new_halaqah_id' => $newHalaqah]);
            ClassStudent::create([
                'student_id' => $studentId,
                'class_id'   => $newHalaqah,
                'start_date' => Carbon::now()->toDateString(),
                'created_at' => Carbon::now(),
                'added_at'   => Carbon::now()
            ]);

            Log::info("Updating student status to active.", ['student_id' => $studentId]);
            $studentUpdateCount = Student::where('id', $studentId)->update(['status' => 'active']);
            if ($studentUpdateCount === 0) {
                Log::warning("Attempted to update student status, but student not found or status already active.", ['student_id' => $studentId]);
            }

            $oldCenterId  = $criticalData['old_center_id'];
            $oldProgramId = $criticalData['old_program_id'];
            $oldClassId   = $criticalData['old_class_id'];

            $logMessage = "Student Transfer Log (__invoke): Student ID {$studentId} transferred from Center [{$oldCenterId}], Program [{$oldProgramId}], Class [{$oldClassId}] " .
                "to Center [{$newCenterId}], Program [{$newProgramId}], Class [{$newHalaqah}]. " .
                "Operations performed: Updated existing Admission record (restored if trashed), merged critical data, synced program, assigned level (if applicable), updated student status, force-deleted old ClassStudent records, and soft-deleted related reports/plans. " .
                "Performed by User ID: " . auth()->user()->id . " (Roles: {$rolesString}). " .
                "Timestamp: " . Carbon::now()->toDateTimeString();
            Log::info($logMessage);

            DB::commit();
            Log::info("Halaqah transfer transaction committed successfully.", ['student_id' => $studentId]);

            return response()->json(['message' => 'Student transferred successfully'], 201);

        } catch (\Exception $exception) {
            Log::error("Exception during halaqah transfer in __invoke: " . $exception->getMessage(), [
                'student_id' => $request->student_id ?? 'unknown',
                'current_halaqah' => $request->halaqah ?? 'unknown',
                'new_halaqah' => $request->new_halaqah_id ?? 'unknown',
                'exception_trace' => $exception->getTraceAsString()
            ]);
            DB::rollBack();
            Log::info("Halaqah transfer transaction rolled back due to exception.", ['student_id' => $request->student_id ?? 'unknown']);

            return response()->json(['message' => "An error occurred during the transfer: " . $exception->getMessage()], 500);
        }
    }
}

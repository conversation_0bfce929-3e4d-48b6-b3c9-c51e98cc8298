<?php

namespace Modules\JobSeeker\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SlackNotifier
{
    /**
     * @var string
     */
    protected $webhookUrl;
    
    /**
     * @var string
     */
    protected $channel;
    
    /**
     * @var string
     */
    protected $username;
    
    /**
     * SlackNotifier constructor.
     */
    public function __construct()
    {
        // Load values from config or directly from env
        $this->webhookUrl = env('SLACK_WEBHOOK_URL') ?: config('jobseeker.slack.webhook_url');
        $this->channel = env('SLACK_CHANNEL') ?: config('jobseeker.slack.channel', '#job-alerts');
        $this->username = env('SLACK_USERNAME') ?: config('jobseeker.slack.username', 'Job Alert Bot');
        
        // Debug info
        Log::info("Slack notifier initialized with channel: {$this->channel}");
        
        // Fallback to hardcoded values if webhook URL is missing
        if (empty($this->webhookUrl)) {
            Log::warning("Missing Slack webhook URL, notifications will not be sent");
        }
    }
    
    /**
     * Send a text message via Slack Incoming Webhooks
     *
     * @param string $message
     * @return bool
     */
    public function sendMessage($message)
    {
        // Add console output for debugging
        echo "🔔 Sending Slack message...\n";
        
        if (empty($this->webhookUrl)) {
            Log::error("Slack notification not sent: Missing webhook URL");
            echo "❌ Error: Missing Slack webhook URL\n";
            return false;
        }
        
        try {
            Log::info("Sending Slack message to channel: {$this->channel}");
            
            // Add console output for debugging
            echo "→ Sending to channel: {$this->channel}\n";
            
            $payload = [
                'text' => $message,
                'channel' => $this->channel,
                'username' => $this->username,
                'mrkdwn' => true
            ];
            
            Log::info("Slack request payload: " . json_encode($payload, JSON_PRETTY_PRINT));
            
            $response = Http::post($this->webhookUrl, $payload);
            
            if ($response->successful()) {
                Log::info("Slack notification sent successfully");
                
                // Add console output for debugging
                echo "✅ Slack message sent successfully!\n";
                return true;
            } else {
                Log::error("Failed to send Slack notification. Status: " . $response->status());
                Log::error("Response body: " . $response->body());
                
                // Add console output for debugging
                echo "❌ Error sending Slack message: " . $response->status() . "\n";
                echo "Response: " . $response->body() . "\n";
                return false;
            }
        } catch (\Exception $e) {
            Log::error("Exception when sending Slack notification: " . $e->getMessage());
            Log::error("Stack trace: " . $e->getTraceAsString());
            
            // Add console output for debugging
            echo "❌ Exception sending Slack message: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * Send a notification for a new job
     *
     * @param array $jobData
     * @return bool
     */
    public function notifyNewJob($jobData)
    {
        $message = "🆕 *NEW JOB ALERT*\n\n";
        $message .= $this->formatJobMessage($jobData);
        return $this->sendMessage($message);
    }
    
    /**
     * Send a notification for an updated job
     *
     * @param array $jobData
     * @return bool
     */
    public function notifyUpdatedJob($jobData)
    {
        $message = "🔄 *JOB UPDATED*\n\n";
        $message .= $this->formatJobMessage($jobData);
        return $this->sendMessage($message);
    }
    
    /**
     * Format job data into a message
     *
     * @param array $jobData
     * @return string
     */
    protected function formatJobMessage($jobData)
    {
        $message = "*{$jobData['position']}*\n";
        $message .= "📍 Location: {$jobData['locations']}\n";
        $message .= "🏢 Company: {$jobData['company_name']}\n";
        $message .= "📅 Expires: {$jobData['expire_date']}\n";
        $message .= "💼 Type: {$jobData['work_type']} - {$jobData['contract_type']}\n";
        $message .= "👥 Vacancy: {$jobData['number_of_vacancy']}\n";
        
        if (!empty($jobData['vacancy_number'])) {
            $message .= "🔢 Vacancy #: {$jobData['vacancy_number']}\n";
        }
        
        $message .= "💰 Salary: {$jobData['salary']}\n";
        
        // Add apply online info if applicable
        if (!empty($jobData['can_apply_online']) && $jobData['can_apply_online']) {
            $message .= "✅ Can apply online\n";
        }
        
        return $message;
    }
} 
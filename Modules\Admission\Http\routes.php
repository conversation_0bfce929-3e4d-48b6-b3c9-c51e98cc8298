<?php




Route::group(['middleware' => ['web', 'auth:employee','missedClockOutMiddleware', \App\Http\Middleware\SystemViewerDataAccess::class], 'prefix' => 'workplace/admission', 'namespace' => 'Modules\Admission\Http\Controllers'], function () {
    Route::get('/fetch-centers', [\Modules\Admission\Http\Controllers\CenterController::class, 'fetchCenters'])->name('fetch.centers');
    Route::get('/fetch-supervisor-centers', [\Modules\Admission\Http\Controllers\CenterController::class, 'fetchSupervisorCenters'])->name('fetch.supervisor.centers');

    // Add new routes for program-based centers and classes
    Route::get('/get-centers', 'StudentController@getCenters')->name('get.centers');
    Route::get('/get-classes', 'StudentController@getClasses')->name('get.classes');

    Route::get('/fetch-classes-for-center/{centerId}', [\Modules\Admission\Http\Controllers\CenterController::class, 'fetchClassesForCenter'])
        ->name('fetch.classes.for.center');

    Route::put('/students/change-halaqah', 'StudentController@changeHalaqah')->name('change.student.halaqah');
    Route::get('students/json-data', 'StudentController@getStudentsJsonFormat')->name('students.json.data');
    Route::get('students/username/json-data', 'StudentController@getStudentsUsernameJsonFormat')->name('students.username.json.data');
    Route::get('students/program-level/centers-data', 'CentersDataBasedonStudentProgramLevelsController')->name('students.programLlevelBased.centers.json.data');
    Route::get('students/program-level/examed-students/classes-data', 'ClassesDataBasedonStudentProgramLevelsCentersForExamedStudentsController')->name('students.programlevelCentersBased.ExamedStudents.classes.json.data');
    Route::get('students/program-level/classes-data', 'ClassesDataBasedonStudentProgramLevelsCentersController')->name('students.programLlevelCentersBased.classes.json.data');
    Route::get('students/centers-data', 'StudentController@getCentersJsonFormat')->name('students.centers.json.data');
    Route::get('students/center/halaqah', 'StudentController@getCenterBasedHalaqah')->name('students.centers.halaqah');
    Route::get('students/programs-data', 'StudentController@getProgramsJsonFormat')->name('students.programs.json.data');
    Route::get('students/status-data', 'StudentController@getStudentStatusJsonFormat')->name('students.status.json.data');
//    Route::post('student/enable', 'StudentController@restoreStudent')->name('student.restore.json.data');
    Route::put('student/enable', 'StudentController@restoreStudent')->name('student.restore.json.data');
    Route::put('student/return-student-to-previous-halaqah', 'StudentController@returnStudentToPreviousHalaqah')->name('student.return.to.previous.halaqah');
    Route::put('student/verify', 'StudentController@verify')->name('verify.user');
    Route::get('student/unverified-users', 'UnverifiedStudentController')->name('unverified.user.list');
    Route::post('student/change-student-status-to-new-admission', 'StudentController@changeStudentStatusToNewAdmission')->name('student.change.status.to.newAdmission');
    Route::get('/', 'AdmissionController@index');

    Route::put('add-student-missing-info', 'StudentMissingDataController@update')->name('add-student-missing-info.update')->middleware('permission:update student');

    Route::get('/students-with-missing-data', 'StudentsWithMissingDataDatatablesController')->name('missing.students');
    Route::post('/create-user-for-student', 'StudentsWithMissingDataDatatablesController@createUser')->name('create.user.for.student');

    Route::get('all-students-to-add-to-class', 'ShowAllStudentsToAddToClass')->name('students.all.add.to.class')->middleware('permission:access students');
    Route::get('students', 'StudentController@index')->name('students.index')->middleware('permission:access students');
    Route::post('students', 'StudentController@store')->name('students.store')->middleware('role:managing-director_2_|supervisor_2_');
    Route::post('create-student-by-employee', 'StoreStudentByEmployeeController')->name('students.store.by.employee');
    Route::get('students/create', 'StudentController@create')->name('students.create')->middleware('role:managing-director_2_|supervisor_2_');
    Route::get('students/{student?}/edit', 'StudentController@show')->name('students.edit')->middleware('role:managing-director_2_|supervisor_2_');
    Route::put('students/{id}', 'StudentController@update')->name('students.update')->middleware('role:managing-director_2_|supervisor_2_');
    Route::delete('students/{student}', 'StudentController@destroy')->name('students.destroy')->middleware('role:managing-director_2_|supervisor_2_');
    Route::get('students/{id?}', 'StudentController@show')->name('students.show')->middleware('can:view student');

//    Route::resource('students', 'StudentController');
    Route::post('approve', 'AdmissionController@approve')->name('admission.approve');
    Route::post('set-interview', 'AdmissionController@setInterview')->name('set.interview');
    Route::post('set-interview-ajax', 'AdmissionInterviewController@setInterview')->name('admission.set.interview.ajax');
    Route::post('set-interview-assessment-ajax', 'AdmissionInterviewAssessmentController@setInterviewAssessment')->name('admission.set.interview.assessment.ajax');
    Route::post('re-register', 'AdmissionController@reRegister')->name('readmission.store');
    Route::put('update', 'AdmissionController@update')->name('admission.update_admission');
    Route::post('api-update', 'AdmissionAPIController@update')->name('admission.api.update_admission');
    Route::post('api-accept-admission', 'AdmissionAPIController@accept')->name('admission.api.accept_admission');
    Route::post('api-archive-student', 'AdmissionAPIController@archiveStudent')->name('admission.api.archive_student');
    Route::get('student_form', 'FormsSettingController@student')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');;
    Route::get('guardian_form', 'FormsSettingController@guardian')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');;
    Route::post('update', 'FormsSettingController@update')->name('forms_setting.update');
    Route::get('center/{center_id}/classes/', 'CenterBasedClassesController')->name('center.based.classes.index');
    Route::get('program/{program_id}/centers/', 'ProgramBasedCentersController')->name('program.based.centers.index');
    Route::post('/update-admission-program-center-class', 'AdmissionProgramCenterClassUpdateController')->name('admission.program.center.class.update');
    Route::get('/program/{program_id}/center/{center_id}/classes', 'ProgramandCenterBasedClassesController')
        ->name('program.center.based.classes.index');

    Route::group(['as' => 'admission.'], function () {
        Route::resource('guardians', 'GuardianController')->middleware('permission:access guardians');
    });


    Route::get('admission-interviews', 'StudentController@index')->name('admission-interviews.index')->middleware('role:managing-director_2_|supervisor_2_|education-assistant_2_');
    Route::post('admission-interviews', 'StudentController@store')->name('admission-interviews.store')->middleware('role:managing-director_2_|supervisor_2_|education-assistant_2_');
    Route::get('admission-interviews/create', 'AdmissionInterviewsController@create')->name('admission-interviews.create')->middleware('role:managing-director_2_|supervisor_2_|education-assistant_2_');
    Route::get('admission-interviews/{admission_interview}', 'AdmissionInterviewsController@show')->name('admission-interviews.show')->middleware('role:managing-director_2_|supervisor_2_|education-assistant_2_');
    Route::get('admission-interviews/{admission_interview}/edit', 'AdmissionInterviewsController@show')->name('admission-interviews.edit')->middleware('role:managing-director_2_|supervisor_2_|education-assistant_2_');
    Route::put('admission-interviews/{admission_interview}', 'AdmissionInterviewsController@update')->name('admission-interviews.update')->middleware('role:managing-director_2_|supervisor_2_|education-assistant_2_');
    Route::delete('admission-interviews/{admission_interview}', 'AdmissionInterviewsController@destroy')->name('admission-interviews.destroy')->middleware('role:managing-director_2_|supervisor_2_|education-assistant_2_');

//    Route::resource('admission-interviews', 'AdmissionInterviewsController');
    Route::post('admission-interview-report', 'AdmissionInterviewsController@report')->name('admission-interviews.report');
    Route::post('admission-set-orientation', 'AdmissionController@setOrientation')->name('admission.set_orientation');
    Route::post('admission-finalize-registeration', 'AdmissionController@finalize')->name('admission.finalize_registeration');
    Route::post('api-admission-finalize-registeration', 'AdmissionAPIController@finalize')->name('admission.api.finalize_registration');
//    Route::post('create-hefz-plan', 'CreateHefzPlanController')->name('admission.create_hefz_plan');
    Route::match(['put','patch','post'],'create-hefz-plan', 'AdmissionHefzPlanController')->name('admission.create_hefz_plan');
//    Route::match(['put','patch','post'],'api-create-hefz-plan', 'APIAdmissionHefzPlanController')->name('admission.api.create_hefz_plan');
//    Route::post('approve-hefz-plan', 'ApproveHefzPlanController')->name('admission.approve_hefz_plan');
    Route::get('live_search', 'GuardianController@live_search')->name('guardian.live_search');
    Route::get('student/archive', 'ArchiveController@index')->name('student.archive.index');
    Route::get('student/archive/show/{id}', 'ArchiveController@show')->name('admission.students.archive.show');
});

//Route::group(['middleware' => ['web'], null, 'namespace' => 'Modules\Admission\Http\Controllers'], function () {
//    Route::get('interview/accept/{admissionInterviewAdmissionId}/{channel}', 'AdmissionInterviewsController@acceptInterview')
//        ->name('accept.interview')
//        ->middleware('signed') /** https://laravel.com/docs/5.8/urls#signed-urls */
//        ->where('channel', 'email|dashboard'/** we want to match only with some keywords */);;
//
//});
Route::group(['middleware' => ['signed'], null, 'namespace' => 'Modules\Admission\Http\Controllers'], function () {
    Route::get('interview/accept/{admissionInterviewAdmissionId}/{channel}', 'AdmissionInterviewsController@acceptInterview')
        ->name('accept.interview')

        ->where('channel', 'email|dashboard'/** we want to match only with some keywords */);;

});


<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Services\ReliefWebService;
use Modules\JobSeeker\Entities\CommandScheduleExecution;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\ProviderJobCategory;
use Carbon\Carbon;
use Illuminate\Support\Str;

/**
 * SyncReliefWebJobsCommand orchestrates ReliefWeb job synchronization with health tracking.
 * 
 * Purpose: Command-line interface for ReliefWeb job sync operations with category filtering,
 * execution tracking, and comprehensive error handling.
 * Side effects: Creates execution records, calls ReliefWebService, logs metrics and results.
 * Security/Permissions: Validates provider category IDs against database.
 * Errors: Handles service failures, invalid categories, network issues; tracks in execution records.
 * Dependencies: ReliefWebService for API integration, CommandScheduleExecution for tracking.
 * Performance: Supports single-category testing and batch processing with health metrics.
 */
class SyncReliefWebJobsCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'jobseeker:sync-reliefweb-jobs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync humanitarian jobs from ReliefWeb.int with category filtering and health tracking (follows centralized notification architecture)';

    /**
     * The signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobseeker:sync-reliefweb-jobs
                          {--category=* : Optional ReliefWeb career category IDs to sync (e.g., 6866 for ICT)}
                          {--schedule-rule-id= : Optional schedule rule ID for custom filters}';

    /**
     * @var ReliefWebService
     */
    protected ReliefWebService $reliefWebService;

    /**
     * Create a new command instance.
     *
     * @param ReliefWebService $reliefWebService
     */
    public function __construct(ReliefWebService $reliefWebService)
    {
        parent::__construct();
        $this->reliefWebService = $reliefWebService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $categoryIds = $this->option('category');
        $scheduleRuleId = $this->option('schedule-rule-id') ? (int) $this->option('schedule-rule-id') : null;
        $executionId = null;
        $traceId = (string) Str::uuid();
        $startTime = Carbon::now();
        
        try {
            $this->info('START ReliefWeb humanitarian jobs synchronization');
            Log::info('START ReliefWeb synchronization via command', [
                'category_ids' => $categoryIds,
                'schedule_rule_id' => $scheduleRuleId,
                'start_time' => $startTime->toDateTimeString(),
                'trace_id' => $traceId,
            ]);

            // Validate ReliefWeb category IDs if provided
            if (!empty($categoryIds)) {
                $requested = array_map('strval', $categoryIds);
                $invalid = [];
                foreach ($requested as $identifier) {
                    $exists = ProviderJobCategory::where('provider_name', 'reliefweb')
                        ->where('provider_identifier', $identifier)
                        ->exists();
                    if (!$exists) { 
                        $invalid[] = $identifier; 
                    }
                }
                if (!empty($invalid)) {
                    $this->error('Invalid ReliefWeb category IDs provided: ' . implode(', ', $invalid));
                    $this->line('Valid ReliefWeb category IDs:');
                    $this->displayValidCategories();
                    Log::error('ReliefWeb sync aborted due to invalid category IDs', [
                        'invalid_identifiers' => $invalid,
                        'provided_identifiers' => $requested
                    ]);
                    return 1;
                }
            }

            // ALWAYS create execution record for health tracking
            $execution = CommandScheduleExecution::create([
                'schedule_rule_id' => $scheduleRuleId, // Can be null for manual executions
                'command' => 'jobseeker:sync-reliefweb-jobs',
                'status' => 'running',
                'started_at' => $startTime,
                'output' => json_encode([
                    'categories' => $categoryIds,
                    'schedule-rule-id' => $scheduleRuleId,
                    'manual_execution' => !$scheduleRuleId
                ])
            ]);
            $executionId = $execution->id;
            $this->info("Created execution record with ID: {$executionId}");

            // Inject run context into service for end-to-end traceability
            $this->reliefWebService->setRunContext($traceId, $executionId);
            
            if ($scheduleRuleId) {
                $this->info("Using custom filters for schedule rule ID: {$scheduleRuleId}");
            } else {
                $this->info("Manual execution - using default filters");
            }
            
            // Display sync target
            if (!empty($categoryIds)) {
                $categoryIdsString = implode(', ', $categoryIds);
                $this->info("Fetching jobs for ReliefWeb categories: {$categoryIdsString}...");
                $this->displayCategoryDetails($categoryIds);
            } else {
                $this->info('Fetching jobs from all ReliefWeb career categories...');
            }

            // Call the service with optional parameters
            $stats = $this->reliefWebService->fetchAndNotifyJobs($categoryIds, $scheduleRuleId);
            
            // Check if sync was successful
            if (!$stats['success']) {
                throw new \Exception($stats['error_message'] ?? 'Unknown error occurred during ReliefWeb sync');
            }
            
            // Validate results
            if (empty($stats['created']) && empty($stats['updated']) && empty($stats['categories_processed'])) {
                $this->handleEmptyResults($stats);
                
                // Complete execution record for empty results
                if ($executionId) {
                    $this->completeExecution($executionId, $stats, true, 'No jobs processed - empty results');
                }
                
                return 1;
            }

            // Display comprehensive results with health metrics
            $this->displaySyncResults($stats);
            
            // Get additional database statistics
            $dbStats = $this->getDatabaseStatistics();
            $this->displayDatabaseSummary($dbStats);
            
            // Log comprehensive results
            Log::info('END ReliefWeb synchronization via command', [
                'sync_stats' => $stats,
                'db_stats' => $dbStats,
                'execution_id' => $executionId,
                'trace_id' => $traceId,
            ]);
            
            // Complete execution record with health metrics
            if ($executionId) {
                $this->completeExecution($executionId, $stats, true);
            }

            return 0;

        } catch (\Exception $e) {
            Log::error('END ReliefWeb jobs sync command', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'execution_id' => $executionId,
                'trace_id' => $traceId,
            ]);

            $this->error('Error: ' . $e->getMessage());
            
            // Complete execution record with error
            if ($executionId) {
                $errorStats = [
                    'success' => false,
                    'jobs_fetched' => 0,
                    'jobs_by_category' => [],
                    'error_types' => ['COMMAND_ERROR' => 1],
                    'api_response_time' => 0,
                    'created' => 0,
                    'updated' => 0,
                    'errors' => 1
                ];
                $this->completeExecution($executionId, $errorStats, false, $e->getMessage());
            }
            
            return 1;
        }
    }

    /**
     * Display valid ReliefWeb category IDs for user reference.
     * 
     * @return void
     */
    protected function displayValidCategories(): void
    {
        $categories = ProviderJobCategory::where('provider_name', 'reliefweb')
            ->orderBy('canonical_category_id')
            ->get(['provider_identifier', 'name']);

        foreach ($categories as $category) {
            $this->line("  • {$category->provider_identifier}: {$category->name}");
        }
    }

    /**
     * Display category details for selected categories.
     * 
     * @param array $categoryIds
     * @return void
     */
    protected function displayCategoryDetails(array $categoryIds): void
    {
        $this->line('Selected ReliefWeb categories:');
        
        foreach ($categoryIds as $categoryId) {
            $category = ProviderJobCategory::where('provider_name', 'reliefweb')
                ->where('provider_identifier', $categoryId)
                ->first();
                
            if ($category) {
                $this->line("  • {$categoryId}: {$category->name}");
            }
        }
    }

    /**
     * Handle empty sync results
     * 
     * @param array $stats
     * @return void
     */
    protected function handleEmptyResults(array $stats): void
    {
        $this->warn('No jobs processed from ReliefWeb');
        
        if ($stats['errors'] > 0) {
            $this->error("Encountered {$stats['errors']} errors during processing");
            
            // Display error breakdown if available
            if (!empty($stats['error_types'])) {
                $this->line('Error breakdown:');
                foreach ($stats['error_types'] as $errorType => $count) {
                    $this->line("  • {$errorType}: {$count}");
                }
            }
        }
        
        // Provide helpful guidance
        $this->line('');
        $this->line('Possible reasons:');
        $this->line('  • No new jobs published in selected categories');
        $this->line('  • Network connectivity issues');
        $this->line('  • ReliefWeb API temporarily unavailable');
        $this->line('  • Category filter too restrictive');
    }

    /**
     * Display synchronization results including health metrics
     * 
     * @param array $stats
     * @return void
     */
    protected function displaySyncResults(array $stats): void
    {
        $this->info('ReliefWeb Synchronization Results:');
        $this->line("  - Jobs created: {$stats['created']}");
        $this->line("  - Jobs updated: {$stats['updated']}");
        $this->line("  - Jobs processed: {$stats['processed']}");
        $this->line("  - Categories processed: {$stats['categories_processed']}");
        
        if ($stats['errors'] > 0) {
            $this->warn("  - Errors encountered: {$stats['errors']}");
        }
        if ($stats['skipped'] > 0) {
            $this->warn("  - Jobs skipped: {$stats['skipped']}");
        }

        // Health dashboard metrics
        if (isset($stats['jobs_fetched'])) {
            $this->info('Health Dashboard Metrics:');
            $this->line('  - Total jobs fetched: ' . $stats['jobs_fetched']);
            $this->line('  - API response time: ' . round($stats['api_response_time'] ?? 0, 2) . 's');
            $this->line('  - Execution time: ' . round($stats['execution_time_seconds'] ?? 0, 2) . 's');
            
            if (!empty($stats['jobs_by_category'])) {
                $this->line('  - Jobs by category:');
                foreach ($stats['jobs_by_category'] as $categoryId => $count) {
                    $categoryName = $this->getCategoryName($categoryId);
                    $this->line("    • {$categoryName} ({$categoryId}): {$count}");
                }
            }
            
            if (!empty($stats['error_types'])) {
                $this->line('  - Error breakdown:');
                foreach ($stats['error_types'] as $errorType => $count) {
                    $this->line("    • {$errorType}: {$count}");
                }
            }
        }
    }

    /**
     * Get category name by provider identifier.
     */
    protected function getCategoryName(string $categoryId): string
    {
        $category = ProviderJobCategory::where('provider_name', 'reliefweb')
            ->where('provider_identifier', $categoryId)
            ->first();
            
        return $category ? $category->name : 'Unknown Category';
    }

    /**
     * Get database statistics
     * 
     * @return array
     */
    protected function getDatabaseStatistics(): array
    {
        return [
            'total_reliefweb_jobs' => \Modules\JobSeeker\Entities\Job::where('source', 'ReliefWeb')->count(),
            'recent_reliefweb_jobs' => \Modules\JobSeeker\Entities\Job::where('source', 'ReliefWeb')
                ->where('publish_date', '>=', now()->subDays(7))
                ->count(),
            'recent_reliefweb_jobs_with_categories' => \Modules\JobSeeker\Entities\Job::where('source', 'ReliefWeb')
                ->whereHas('providerCategories')
                ->count(),
            'total_categories' => ProviderJobCategory::where('provider_name', 'reliefweb')->count()
        ];
    }

    /**
     * Display database summary
     * 
     * @param array $dbStats
     * @return void
     */
    protected function displayDatabaseSummary(array $dbStats): void
    {
        $this->info('');
        $this->info('=== RELIEFWEB SYNCHRONIZATION SUMMARY ===');
        $this->line('  - Total ReliefWeb jobs in database: ' . $dbStats['total_reliefweb_jobs']);
        $this->line('  - Jobs published in last 7 days: ' . $dbStats['recent_reliefweb_jobs']);
        $this->line('  - ReliefWeb jobs with categories: ' . $dbStats['recent_reliefweb_jobs_with_categories']);
        $this->line('  - Total ReliefWeb categories: ' . $dbStats['total_categories']);
        $this->line('  - Synchronization completed successfully');
        $this->line('');
        $this->line('🌐 ReliefWeb Integration: Centralized notification system active');
        $this->line('📧 Notifications: Handled via JobNotificationService (provider-agnostic)');
    }

    /**
     * Complete execution record with health metrics
     * 
     * @param int $executionId
     * @param array $stats
     * @param bool $success
     * @param string|null $errorMessage
     * @return void
     */
    protected function completeExecution(int $executionId, array $stats, bool $success, ?string $errorMessage = null): void
    {
        try {
            $execution = CommandScheduleExecution::find($executionId);
            if (!$execution) {
                Log::warning('Execution record not found for completion', ['execution_id' => $executionId]);
                return;
            }

            // Format health metrics using the service
            $healthMetrics = $this->reliefWebService->formatExecutionStats($stats);
            
            $execution->markCompleted(
                $success ? 0 : 1,                            // $exitCode (int): 0 = success, 1 = failure
                $errorMessage,                                // $output (string): error message or null
                null,                                         // $memoryUsageMb (float): not tracked yet
                $healthMetrics['jobs_fetched'],               // $jobsFetched (int)
                $healthMetrics['jobs_by_category'],           // $jobsByCategory (array)
                $healthMetrics['error_type'],                 // $errorType (string)
                $healthMetrics['error_details']               // $errorDetails (array)
            );

            $this->info("Execution record {$executionId} completed with health metrics");
            
        } catch (\Exception $e) {
            Log::error('Error completing execution record', [
                'execution_id' => $executionId,
                'error' => $e->getMessage()
            ]);
        }
    }
}

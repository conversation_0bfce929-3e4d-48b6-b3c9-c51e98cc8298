<?php

namespace Modules\JobSeeker\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use DiDOM\Document;
use Modules\JobSeeker\Services\AcbarJobService;

class DiscoverAcbarCategoriesCommand extends Command
{
    protected $signature = 'jobseeker:discover-acbar-categories';
    protected $description = 'Discover and document ACBAR job categories';

    protected string $baseUrl = 'https://www.acbar.org/jobs';
    protected int $locationId = 14; // Kabul

    /**
     * @var AcbarJobService
     */
    protected $acbarJobService;

    /**
     * Create a new command instance.
     *
     * @param AcbarJobService $acbarJobService
     */
    public function __construct(AcbarJobService $acbarJobService)
    {
        parent::__construct();
        $this->acbarJobService = $acbarJobService;
    }

    public function handle()
    {
        $this->info('Starting ACBAR category discovery...');
        Log::info('Starting ACBAR category discovery');

        $categories = [];
        $errors = [];

        // Create storage directory if it doesn't exist
        $storageDir = storage_path('app/jobseeker');
        if (!file_exists($storageDir)) {
            mkdir($storageDir, 0755, true);
        }

        // Test URL first
        try {
            $testUrl = "{$this->baseUrl}?location={$this->locationId}&category=1";
            $response = $this->acbarJobService->makeRequest($testUrl);
            if (!$response->successful()) {
                throw new \Exception("Failed to access ACBAR.org. Status: " . $response->status());
            }
        } catch (\Exception $e) {
            $this->error("Failed to access ACBAR.org: " . $e->getMessage());
            Log::error("Failed to access ACBAR.org", ['error' => $e->getMessage()]);
            return Command::FAILURE;
        }

        $this->info("Successfully connected to ACBAR.org");
        $this->newLine();

        $bar = $this->output->createProgressBar(71);
        $bar->start();

        for ($categoryId = 1; $categoryId <= 71; $categoryId++) {
            try {
                $url = "{$this->baseUrl}?location={$this->locationId}&category={$categoryId}";
                
                $response = $this->acbarJobService->makeRequest($url);
                if (!$response->successful()) {
                    throw new \Exception("Failed to fetch URL. Status: " . $response->status());
                }

                $document = new Document($response->body());
                
                // Find the category dropdown
                $select = $document->first('select#data_industry');
                if (!$select) {
                    throw new \Exception("Category dropdown not found");
                }

                // Find the selected option
                $selectedOption = $select->first('option[selected]');
                if (!$selectedOption) {
                    throw new \Exception("Selected category option not found");
                }

                $categoryName = trim($selectedOption->text());
                $categories[$categoryId] = $categoryName;

                // Add delay to avoid overwhelming the server
                sleep(1);

            } catch (\Exception $e) {
                $error = "Error processing category {$categoryId}: " . $e->getMessage();
                $errors[] = $error;
                Log::error($error);
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);

        // Output results in a table format
        $this->info('Discovered Categories:');
        $this->table(
            ['ACBAR ID', 'Category Name'],
            collect($categories)->map(fn($name, $id) => [$id, $name])->toArray()
        );

        // Save results to a file
        $output = "# ACBAR Job Categories\n\n";
        $output .= "Discovered at: " . now() . "\n\n";
        $output .= "## Categories\n\n";
        $output .= "| ACBAR ID | Category Name |\n";
        $output .= "|-----------|---------------|\n";
        foreach ($categories as $id => $name) {
            $output .= "| {$id} | {$name} |\n";
        }

        if (!empty($errors)) {
            $output .= "\n## Errors Encountered\n\n";
            foreach ($errors as $error) {
                $output .= "- {$error}\n";
            }
        }

        $filePath = storage_path('app/jobseeker/acbar_categories.md');
        file_put_contents($filePath, $output);
        $this->info("Results saved to: {$filePath}");

        $this->newLine();
        $this->info("Summary:");
        $this->line("- Categories found: " . count($categories));
        $this->line("- Errors encountered: " . count($errors));

        Log::info('ACBAR category discovery completed', [
            'categories_found' => count($categories),
            'errors' => count($errors)
        ]);

        return Command::SUCCESS;
    }
} 
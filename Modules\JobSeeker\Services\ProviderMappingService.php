<?php

namespace Modules\JobSeeker\Services;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Mo<PERSON>les\JobSeeker\Entities\ProviderJobCategory;
use Mo<PERSON>les\JobSeeker\Entities\ProviderJobLocation;

/**
 * ProviderMappingService
 * 
 * Handles mapping of categories and locations between different job providers
 * using canonical category/location IDs as the bridge.
 */
final class ProviderMappingService
{
    /**
     * Map provider category IDs from source provider to target provider
     *
     * FIXED: Now returns ALL target provider categories that share the same canonical meaning
     * as the source categories, ensuring complete semantic coverage during cloning.
     *
     * @param array $sourceCategoryIds Array of source provider category IDs
     * @param string $sourceProvider Source provider name (e.g., 'jobs.af')
     * @param string $targetProvider Target provider name (e.g., 'acbar')
     * @return array Array of target provider category IDs
     */
    public function mapCategories(array $sourceCategoryIds, string $sourceProvider, string $targetProvider): array
    {
        if (empty($sourceCategoryIds)) {
            Log::debug('ProviderMappingService: No source category IDs provided', [
                'source_provider' => $sourceProvider,
                'target_provider' => $targetProvider
            ]);
            return [];
        }

        try {
            // Step 1: Get canonical category IDs from source provider categories
            $sourceCategories = ProviderJobCategory::whereIn('id', $sourceCategoryIds)
                ->where('provider_name', $sourceProvider)
                ->get(['id', 'canonical_category_id', 'name']);

            if ($sourceCategories->isEmpty()) {
                Log::warning('ProviderMappingService: No source categories found', [
                    'source_category_ids' => $sourceCategoryIds,
                    'source_provider' => $sourceProvider
                ]);
                return [];
            }

            // Extract ALL canonical category IDs (including duplicates for comprehensive mapping)
            $canonicalCategoryIds = $sourceCategories->pluck('canonical_category_id')
                ->filter() // Remove null values
                ->unique()
                ->values()
                ->toArray();

            Log::debug('ProviderMappingService: Found canonical category IDs from source', [
                'source_provider' => $sourceProvider,
                'source_category_count' => count($sourceCategoryIds),
                'source_categories' => $sourceCategories->pluck('name')->toArray(),
                'canonical_category_ids' => $canonicalCategoryIds
            ]);

            // Step 2: Find ALL target provider categories that map to ANY of the canonical categories
            // This ensures we get complete semantic coverage, not just unique mappings
            $targetCategories = ProviderJobCategory::whereIn('canonical_category_id', $canonicalCategoryIds)
                ->where('provider_name', $targetProvider)
                ->get(['id', 'canonical_category_id', 'name']);

            if ($targetCategories->isEmpty()) {
                Log::warning('ProviderMappingService: No target categories found for canonical IDs', [
                    'canonical_category_ids' => $canonicalCategoryIds,
                    'target_provider' => $targetProvider
                ]);
                return [];
            }

            // Return ALL target category IDs that match the canonical meanings
            $targetCategoryIds = $targetCategories->pluck('id')->toArray();

            Log::info('ProviderMappingService: Successfully mapped categories with complete semantic coverage', [
                'source_provider' => $sourceProvider,
                'target_provider' => $targetProvider,
                'source_category_count' => count($sourceCategoryIds),
                'target_category_count' => count($targetCategoryIds),
                'canonical_categories_mapped' => count($canonicalCategoryIds),
                'source_categories' => $sourceCategories->pluck('name')->toArray(),
                'target_categories' => $targetCategories->pluck('name')->toArray(),
                'canonical_mapping_details' => $targetCategories->groupBy('canonical_category_id')->map(function($group, $canonicalId) {
                    return [
                        'canonical_id' => $canonicalId,
                        'target_categories' => $group->pluck('name')->toArray(),
                        'target_count' => $group->count()
                    ];
                })->values()->toArray()
            ]);

            return $targetCategoryIds;

        } catch (\Exception $e) {
            Log::error('ProviderMappingService: Error mapping categories', [
                'source_category_ids' => $sourceCategoryIds,
                'source_provider' => $sourceProvider,
                'target_provider' => $targetProvider,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Map provider location IDs from source provider to target provider
     *
     * FIXED: Now returns ALL target provider locations that share the same canonical meaning
     * as the source locations, ensuring complete semantic coverage during cloning.
     *
     * @param array $sourceLocationIds Array of source provider location IDs
     * @param string $sourceProvider Source provider name (e.g., 'jobs.af')
     * @param string $targetProvider Target provider name (e.g., 'acbar')
     * @return array Array of target provider location IDs
     */
    public function mapLocations(array $sourceLocationIds, string $sourceProvider, string $targetProvider): array
    {
        if (empty($sourceLocationIds)) {
            Log::debug('ProviderMappingService: No source location IDs provided', [
                'source_provider' => $sourceProvider,
                'target_provider' => $targetProvider
            ]);
            return [];
        }

        try {
            // Step 1: Get canonical location IDs from source provider locations
            $sourceLocations = ProviderJobLocation::whereIn('id', $sourceLocationIds)
                ->where('provider_name', $sourceProvider)
                ->get(['id', 'canonical_location_id', 'location_name']);

            if ($sourceLocations->isEmpty()) {
                Log::warning('ProviderMappingService: No source locations found', [
                    'source_location_ids' => $sourceLocationIds,
                    'source_provider' => $sourceProvider
                ]);
                return [];
            }

            // Extract ALL canonical location IDs (including duplicates for comprehensive mapping)
            $canonicalLocationIds = $sourceLocations->pluck('canonical_location_id')
                ->filter() // Remove null values
                ->unique()
                ->values()
                ->toArray();

            Log::debug('ProviderMappingService: Found canonical location IDs from source', [
                'source_provider' => $sourceProvider,
                'source_location_count' => count($sourceLocationIds),
                'source_locations' => $sourceLocations->pluck('location_name')->toArray(),
                'canonical_location_ids' => $canonicalLocationIds
            ]);

            // Step 2: Find ALL target provider locations that map to ANY of the canonical locations
            // This ensures we get complete semantic coverage, not just unique mappings
            $targetLocations = ProviderJobLocation::whereIn('canonical_location_id', $canonicalLocationIds)
                ->where('provider_name', $targetProvider)
                ->get(['id', 'canonical_location_id', 'location_name']);

            if ($targetLocations->isEmpty()) {
                Log::warning('ProviderMappingService: No target locations found for canonical IDs', [
                    'canonical_location_ids' => $canonicalLocationIds,
                    'target_provider' => $targetProvider
                ]);
                return [];
            }

            // Return ALL target location IDs that match the canonical meanings
            $targetLocationIds = $targetLocations->pluck('id')->toArray();

            Log::info('ProviderMappingService: Successfully mapped locations with complete semantic coverage', [
                'source_provider' => $sourceProvider,
                'target_provider' => $targetProvider,
                'source_location_count' => count($sourceLocationIds),
                'target_location_count' => count($targetLocationIds),
                'canonical_locations_mapped' => count($canonicalLocationIds),
                'source_locations' => $sourceLocations->pluck('location_name')->toArray(),
                'target_locations' => $targetLocations->pluck('location_name')->toArray(),
                'canonical_mapping_details' => $targetLocations->groupBy('canonical_location_id')->map(function($group, $canonicalId) {
                    return [
                        'canonical_id' => $canonicalId,
                        'target_locations' => $group->pluck('location_name')->toArray(),
                        'target_count' => $group->count()
                    ];
                })->values()->toArray()
            ]);

            return $targetLocationIds;

        } catch (\Exception $e) {
            Log::error('ProviderMappingService: Error mapping locations', [
                'source_location_ids' => $sourceLocationIds,
                'source_provider' => $sourceProvider,
                'target_provider' => $targetProvider,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get provider name from command string
     *
     * @param string $command
     * @return string
     */
    public function getProviderFromCommand(string $command): string
    {
        if (str_contains($command, 'jobs-af')) {
            return 'jobs.af';
        } elseif (str_contains($command, 'acbar')) {
            return 'acbar';
        }
        
        return 'unknown';
    }

    /**
     * Normalize provider name for database queries
     * 
     * @param string $provider
     * @return string
     */
    public function normalizeProviderName(string $provider): string
    {
        $providerMap = [
            'jobsaf' => 'jobs.af',
            'jobs.af' => 'jobs.af',
            'acbar' => 'acbar',
            'ACBAR' => 'acbar'
        ];

        return $providerMap[$provider] ?? $provider;
    }

    /**
     * Get mapping statistics for debugging
     *
     * @param string $sourceProvider
     * @param string $targetProvider
     * @return array
     */
    public function getMappingStats(string $sourceProvider, string $targetProvider): array
    {
        $sourceCategoryCount = ProviderJobCategory::where('provider_name', $sourceProvider)->count();
        $targetCategoryCount = ProviderJobCategory::where('provider_name', $targetProvider)->count();
        $sourceLocationCount = ProviderJobLocation::where('provider_name', $sourceProvider)->count();
        $targetLocationCount = ProviderJobLocation::where('provider_name', $targetProvider)->count();

        return [
            'source_provider' => $sourceProvider,
            'target_provider' => $targetProvider,
            'categories' => [
                'source_count' => $sourceCategoryCount,
                'target_count' => $targetCategoryCount
            ],
            'locations' => [
                'source_count' => $sourceLocationCount,
                'target_count' => $targetLocationCount
            ]
        ];
    }
}

<?php


namespace Modules\Education\Http\Controllers;


use App\Center;
use App\Classes;
use App\ClassReport;
use App\StudentAttendance;
use Carbon\Carbon;

class ReportsController
{
    public function show()
    {
        $centers = Center::all()->map(function ($center) {
            return [
                'text' => $center->name,
                'value' => $center->id
            ];
        }); // Todo: validate authorization

        return view('education::reports.show', compact('centers'));
    }


    public function classes()
    {



        // Todo: validate authrization
        $classes = Classes::whereIn('center_id', request()->input('centers'))->get()->map(function ($class) {
            return [
                'text' => $class->name,
                'value' => $class->id
            ];
        });

        return response()->json($classes, 200);
    }

    public function generate()
    {
        // $this->validate(request(), []);

        $classes = Classes::whereIn('id', request()->input('classes'))->get()->map(function ($class) {
            $students = $class->studentsAtDate(request()->reportDate);
            $classId = $class->id;
            return [
                'name' => $class->name,
                'teachers' => $class->currentTeachers()->pluck('name'),
                'center' => $class->center->name,
                'programs' =>  $class->programs->where('status', 'active')->map(function ($program) use ($students, $class) {
                    return [
                        'id' => $program->id,
                        'title' => $program->title,
                        'code' => $program->code,
                        'subjects' => $program->subjects,
                        'students' => $students->map(function ($student) use ($class, $program) {

                            $report = ClassReport::where('class_id', $class->id)
                                ->where('status', 'completed')
                                ->where('class_time', '<', request()->reportDate . ' 23:59:59')
                                ->whereHas('attendace', function ($attendace) use ($student) {
                                    $attendace->where('student_id', $student->id)->whereIn('attendance', ['on_time', 'late']);
                                });
                            if ($program->setting['special_program_code'] == 'hefz') {
                                $report = $report->whereHas('hefz', function ($h) use ($student) {
                                    $h->where('student_id', $student->id);
                                });
                            }
                            $report = $report->first();
                            if ($report->lessons || $report->hefz) {

                                if ($report->program->setting['special_program_code'] == 'hefz') {
                                    $lesson = $report->hefz->where('student_id', $student->id)->first();
                                    $lastLesson = getSurahNameById($lesson->hefz_to_surat);
                                    $level = getLevelBySurahId($lesson->hefz_to_surat);
                                } else {
                                    $lesson = $report->lessons->where('student_id', $student->id)->first();
                                    $lastLesson = $lesson->content->title;
                                }
                            } else {
                                $lastLesson = '-';
                                $level = '-';
                            }

                            return [
                                'id' => $student->id,
                                'full_name' => $student->full_name,
                                'nationality' => $student->nationality,
                                'age' => $student->date_of_birth ? Carbon::parse($student->date_of_birth)->age : '-',
                                // 'last_lesson' => $student->lastLessonByClassAndProgram($class->id, $program->id, request()->reportDate),
                                'last_lesson' => $lastLesson,
                                'note' => $student->num_completed_juz,
                                'level' => $level,
                            ];
                        })
                    ];
                }),
                // 'subjects' =>  $class->subjects->where('status', 'active')->map(function ($subject) use ($students, $class) {
                //     return [
                //         'id' => $subject->id,
                //         'title' => $subject->title,
                //         // 'code' => $subject->code,
                //         // 'subjects' => $subject->subjects,
                //         'students' => $students->map(function ($student) {
                //             return [
                //                 'full_name' => $student->full_name,
                //                 'nationality' => $student->nationality,
                //                 'age' => $student->date_of_birth ? Carbon::parse($student->date_of_birth)->age : '-',
                //                 'last_lesson' => $student->lastLessonByClassAndProgram($class->id, $program->id, request()->reportDate),
                //                 'note' => $student->num_completed_juz,
                //                 'level' => $student->level,
                //             ];
                //         })
                //     ];
                // }),
                'students' => $students->map(function ($student) {
                    return [
                        'full_name' => $student->full_name,
                        'nationality' => $student->nationality,
                        'age' => $student->date_of_birth ? Carbon::parse($student->date_of_birth)->age : '-',
                        'last_surah' => $student->last_surah,
                        'num_juz' => $student->num_completed_juz,
                        'level' => $student->level,
                    ];
                })
            ];
        });

        $report = [
            'details' => $classes,
            'summary' => [],
        ];


        return $report;
    }
}

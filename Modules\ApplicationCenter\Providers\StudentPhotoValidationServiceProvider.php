<?php

namespace Modules\ApplicationCenter\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use Illuminate\Http\UploadedFile;
use App\Admission;
use App\Student;

class StudentPhotoValidationServiceProvider extends ServiceProvider
{
    public function register()
    {
        //
    }

    public function boot()
    {
        $provider = $this;

        // 1) Global "has_student_photo" validator
        Validator::extend('has_student_photo', function ($attribute, $value, $parameters, $validator) use ($provider) {
            $studentId = $parameters[0] ?? null;
            return $provider->hasValidPhoto($studentId);
        }, 'Student photo is required. Please upload an image.');

        // 2) Model-event protection
        Event::listen(
            ['eloquent.creating: App\Student', 'eloquent.updating: App\Student'],
            fn(Student $student) => $this->validateStudentPhoto($student)
        );

        Event::listen(
            ['eloquent.creating: App\Admission', 'eloquent.updating: App\Admission'],
            fn(Admission $admission) => $this->validateAdmissionStudentPhoto($admission)
        );
    }

    /**
     * 1) & 3) Centralized "is there a photo anywhere?" logic
     */
    protected function hasValidPhoto(?int $studentId = null): bool
    {
         // 1) any real file in the request?
         $files = Arr::flatten(request()->allFiles());
         foreach ($files as $file) {
             if (
                 $file instanceof UploadedFile
                 && $file->getError() === UPLOAD_ERR_OK
                 // ensure the temp file exists and isn't empty
                 && file_exists($file->getPathname())
                 && filesize($file->getPathname()) > 0
             ) {
                 return true;
             }
         }
 
         // 2) existing record on disk?
         if ($studentId) {
             $student = Student::find($studentId);
             if ($student) {
                 $path = $student->student_photo ?: $student->image;
                 if ($path && file_exists(public_path($path))) {
                     return true;
                 }
             }
         }
 
         return false;
    }

    /**
     * 2) & 4) Cleaned-up model-event enforcement for Student
     */
    protected function validateStudentPhoto(Student $student)
    {
         // if DB already has a photo on disk, skip
         if (
            $student->exists
            && ($student->student_photo || $student->image)
            && file_exists(public_path($student->student_photo ?: $student->image))
        ) {
            return;
        }

        // check uploads only
        $files = Arr::flatten(request()->allFiles());
        $hasAny = false;
        $file = Arr::first($files);
        
       
        // bypass isValid()/getSize() issues by checking UPLOAD_ERR_OK and native filesize()
       
        if ($file instanceof UploadedFile) {
            $hasAny = true;
        }

        if (! $hasAny) {
            Log::warning("Create/update student without photo. ID: {$student->id}");
            throw new \Exception('Student photo is required. Please upload an image.');
        }
    }

    /**
     * 2) Removed session fallback here as well
     */
    protected function validateAdmissionStudentPhoto(Admission $admission)
    {
        if (! empty($admission->student_id)) {
            $stu = Student::find($admission->student_id);
            if ($stu
                && empty($stu->student_photo)
                && empty($stu->image)
            ) {
                Log::warning("Create/update admission without student photo. Admission: {$admission->id}, Student: {$admission->student_id}");
                throw new \Exception('Student photo is required. Please upload an image.');
            }
        } else {
            // brand-new Admission: no session fallback means always invalid
            throw new \Exception('Student photo is required. Please upload an image.');
        }
    }
}

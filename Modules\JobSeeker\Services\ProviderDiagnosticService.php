<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Modules\JobSeeker\Entities\CommandScheduleRule;
use Carbon\Carbon;

/**
 * Provider Diagnostic Service
 * 
 * Provides comprehensive diagnostic capabilities for job provider APIs
 * including connectivity checks, endpoint validation, and data structure analysis.
 */
final class ProviderDiagnosticService
{
    /**
     * @var array<string, array> Provider configuration mapping
     */
    private array $providerConfigs = [
        'jobseeker:sync-jobs-af' => [
            'name' => 'Jobs.af',
            'host' => 'jobs.af',
            'api_endpoint' => 'https://jobs.af/api/v2.6/jobs/list',
            'method' => 'GET',
            'expected_structure' => [
                'status' => 'string',
                'message' => 'string',
                'data' => [
                    'jobs' => 'array',
                    'pagination' => [
                        'current_page' => 'integer',
                        'total_pages' => 'integer',
                        'total_results' => 'integer'
                    ]
                ]
            ]
        ],
        'jobseeker:sync-acbar-jobs' => [
            'name' => 'ACBAR',
            'host' => 'www.acbar.org',
            'api_endpoint' => 'https://www.acbar.org/jobs',
            'method' => 'GET',
            'expected_structure' => [
                'html' => 'string',
                'content_type' => 'text/html'
            ]
        ],
        'jobseeker:fetch-jobs-af-descriptions' => [
            'name' => 'Jobs.af (Descriptions)',
            'host' => 'jobs.af',
            'api_endpoint' => 'https://jobs.af/api/v2.6/jobs/list',
            'method' => 'GET',
            'expected_structure' => [
                'status' => 'string',
                'message' => 'string',
                'data' => [
                    'jobs' => 'array'
                ]
            ]
        ],
        'jobseeker:cleanup-old-jobs' => [
            'name' => 'Database Cleanup',
            'host' => 'localhost',
            'api_endpoint' => 'internal://database',
            'method' => 'INTERNAL',
            'expected_structure' => [
                'operation' => 'database_cleanup',
                'affected_rows' => 'integer'
            ]
        ]
    ];

    /**
     * Run comprehensive diagnostic for a specific schedule rule
     *
     * @param int $ruleId
     * @return array Diagnostic results
     */
    public function runDiagnostic(int $ruleId): array
    {
        $startTime = microtime(true);
        $logs = [];
        
        try {
            // Get the schedule rule
            $rule = CommandScheduleRule::find($ruleId);
            if (!$rule) {
                return $this->createErrorResponse('Schedule rule not found', $logs);
            }

            $logs[] = $this->createLogEntry('info', "Starting diagnostic for rule: {$rule->name} (Command: {$rule->command})");

            // Get provider configuration
            $providerConfig = $this->getProviderConfig($rule->command);
            if (!$providerConfig) {
                $logs[] = $this->createLogEntry('error', "Unknown provider for command: {$rule->command}");
                return $this->createErrorResponse('Unknown provider configuration', $logs);
            }

            $logs[] = $this->createLogEntry('info', "Provider identified: {$providerConfig['name']}");

            // Run diagnostic steps
            $results = [
                'rule_info' => $this->extractRuleInfo($rule),
                'steps' => [],
                'overall_status' => 'success',
                'execution_time' => 0,
                'logs' => []
            ];

            // Step 1: Host Connectivity Check
            $logs[] = $this->createLogEntry('info', "Step 1: Checking host connectivity to {$providerConfig['host']}");
            $connectivityResult = $this->checkHostConnectivity($providerConfig['host']);
            $results['steps'][] = [
                'name' => 'Host Connectivity',
                'description' => "Testing connection to {$providerConfig['host']}",
                'status' => $connectivityResult['success'] ? 'success' : 'failure',
                'details' => $connectivityResult
            ];

            if ($connectivityResult['success']) {
                $logs[] = $this->createLogEntry('success', "Host connectivity successful - Response time: {$connectivityResult['response_time']}ms");
            } else {
                $logs[] = $this->createLogEntry('error', "Host connectivity failed: {$connectivityResult['error']}");
                $results['overall_status'] = 'failure';
            }

            // Step 2: API Endpoint Check
            $logs[] = $this->createLogEntry('info', "Step 2: Testing API endpoint {$providerConfig['api_endpoint']}");
            $endpointResult = $this->checkApiEndpoint($providerConfig);
            $results['steps'][] = [
                'name' => 'API Endpoint',
                'description' => "Testing API endpoint availability and response",
                'status' => $endpointResult['success'] ? 'success' : 'failure',
                'details' => $endpointResult
            ];

            if ($endpointResult['success']) {
                $logs[] = $this->createLogEntry('success', "API endpoint responded successfully - Status: {$endpointResult['http_status']}");
            } else {
                $logs[] = $this->createLogEntry('error', "API endpoint failed: {$endpointResult['error']}");
                $results['overall_status'] = 'failure';
            }

            // Step 3: Data Structure Validation
            $logs[] = $this->createLogEntry('info', "Step 3: Validating response data structure");
            $validationResult = $this->validateDataStructure($providerConfig, $endpointResult['response_data'] ?? null);
            $results['steps'][] = [
                'name' => 'Data Structure Validation',
                'description' => 'Comparing actual API response against expected structure',
                'status' => $validationResult['success'] ? 'success' : 'failure',
                'details' => $validationResult
            ];

            if ($validationResult['success']) {
                $logs[] = $this->createLogEntry('success', "Data structure validation passed - All expected fields present");
            } else {
                $logs[] = $this->createLogEntry('warning', "Data structure validation issues found: " . ($validationResult['error'] ?? 'See details'));
                // Don't mark overall as failure for structure issues - these might be non-critical
            }

            $results['execution_time'] = round((microtime(true) - $startTime) * 1000, 2);
            $results['logs'] = $logs;

            Log::info('Provider diagnostic completed successfully', [
                'rule_id' => $ruleId,
                'provider' => $providerConfig['name'],
                'overall_status' => $results['overall_status'],
                'execution_time_ms' => $results['execution_time']
            ]);

            return $results;

        } catch (\Exception $e) {
            $logs[] = $this->createLogEntry('error', "Diagnostic failed with exception: {$e->getMessage()}");
            
            Log::error('Provider diagnostic failed with exception', [
                'rule_id' => $ruleId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->createErrorResponse('Diagnostic execution failed: ' . $e->getMessage(), $logs);
        }
    }

    /**
     * Check host connectivity using ping-like HTTP HEAD request
     *
     * @param string $host
     * @return array
     */
    public function checkHostConnectivity(string $host): array
    {
        $startTime = microtime(true);
        
        try {
            // Use HTTPS first, fallback to HTTP if needed
            $url = "https://{$host}";
            
            $response = Http::timeout(10)
                ->connectTimeout(5)
                ->retry(2, 1000)
                ->head($url);

            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            if ($response->successful() || $response->status() < 500) {
                return [
                    'success' => true,
                    'response_time' => $responseTime,
                    'status_code' => $response->status(),
                    'url_tested' => $url,
                    'message' => 'Host is reachable'
                ];
            }

            return [
                'success' => false,
                'response_time' => $responseTime,
                'status_code' => $response->status(),
                'url_tested' => $url,
                'error' => "HTTP {$response->status()}: Host returned error status",
                'message' => 'Host connectivity failed'
            ];

        } catch (\Exception $e) {
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'success' => false,
                'response_time' => $responseTime,
                'status_code' => 0,
                'url_tested' => "https://{$host}",
                'error' => $e->getMessage(),
                'message' => 'Host is unreachable'
            ];
        }
    }

    /**
     * Check API endpoint availability and response
     *
     * @param array $providerConfig
     * @return array
     */
    public function checkApiEndpoint(array $providerConfig): array
    {
        $startTime = microtime(true);
        
        try {
            $endpoint = $providerConfig['api_endpoint'];
            $method = $providerConfig['method'] ?? 'GET';

            // Handle internal/database operations
            if (strpos($endpoint, 'internal://') === 0) {
                return [
                    'success' => true,
                    'response_time' => round((microtime(true) - $startTime) * 1000, 2),
                    'http_status' => 200,
                    'endpoint_tested' => $endpoint,
                    'message' => 'Internal database operation - no external API call required',
                    'response_data' => [
                        'operation' => 'database_cleanup',
                        'simulated' => true
                    ]
                ];
            }

            // Configure HTTP client with appropriate headers
            $httpClient = Http::timeout(30)
                ->connectTimeout(10)
                ->withHeaders([
                    'User-Agent' => $this->getRandomUserAgent(),
                    'Accept' => $this->getAcceptHeader($providerConfig['name']),
                    'Accept-Language' => 'en-US,en;q=0.9',
                    'Accept-Encoding' => 'gzip, deflate, br',
                    'DNT' => '1',
                    'Connection' => 'keep-alive',
                    'Upgrade-Insecure-Requests' => '1',
                ]);

            // Add specific parameters for Jobs.af API
            if ($providerConfig['name'] === 'Jobs.af') {
                $response = $httpClient->get($endpoint, [
                    'page' => 1,
                    'limit' => 5 // Minimal request for testing
                ]);
            } else {
                // For other providers, make basic request
                $response = $httpClient->get($endpoint);
            }

            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            $responseData = null;

            // Try to parse response based on content type
            $contentType = $response->header('content-type', '');
            if (strpos($contentType, 'application/json') !== false) {
                $responseData = $response->json();
            } elseif (strpos($contentType, 'text/html') !== false) {
                $responseBody = $response->body();
                $responseData = [
                    'html' => substr($responseBody, 0, 500) . (strlen($responseBody) > 500 ? '...' : ''),
                    'content_type' => 'text/html',
                    'body_length' => strlen($responseBody)
                ];
            } else {
                $responseData = ['raw_response' => substr($response->body(), 0, 200)];
            }

            return [
                'success' => $response->successful(),
                'response_time' => $responseTime,
                'http_status' => $response->status(),
                'endpoint_tested' => $endpoint,
                'content_type' => $contentType,
                'response_data' => $responseData,
                'message' => $response->successful() ? 'API endpoint is accessible' : 'API endpoint returned error status'
            ];

        } catch (\Exception $e) {
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'success' => false,
                'response_time' => $responseTime,
                'http_status' => 0,
                'endpoint_tested' => $providerConfig['api_endpoint'],
                'error' => $e->getMessage(),
                'message' => 'API endpoint is unreachable'
            ];
        }
    }

    /**
     * Validate response data structure against expected format
     *
     * @param array $providerConfig
     * @param mixed $actualData
     * @return array
     */
    public function validateDataStructure(array $providerConfig, $actualData = null): array
    {
        try {
            $expectedStructure = $providerConfig['expected_structure'];
            $providerName = $providerConfig['name'];

            if ($actualData === null) {
                return [
                    'success' => false,
                    'error' => 'No response data available for validation',
                    'expected_structure' => $expectedStructure,
                    'actual_structure' => null,
                    'validation_details' => []
                ];
            }

            // Perform structure comparison
            $validationResult = $this->compareStructures($expectedStructure, $actualData);
            
            return [
                'success' => $validationResult['is_valid'],
                'expected_structure' => $expectedStructure,
                'actual_structure' => $this->getActualStructure($actualData),
                'validation_details' => $validationResult['details'],
                'missing_fields' => $validationResult['missing_fields'] ?? [],
                'extra_fields' => $validationResult['extra_fields'] ?? [],
                'type_mismatches' => $validationResult['type_mismatches'] ?? [],
                'message' => $validationResult['is_valid'] ? 
                    'Data structure matches expected format' : 
                    'Data structure validation found issues'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Structure validation failed: ' . $e->getMessage(),
                'expected_structure' => $providerConfig['expected_structure'] ?? null,
                'actual_structure' => $this->getActualStructure($actualData),
                'message' => 'Structure validation encountered an error'
            ];
        }
    }

    /**
     * Get provider configuration for a given command
     *
     * @param string $command
     * @return array|null
     */
    private function getProviderConfig(string $command): ?array
    {
        return $this->providerConfigs[$command] ?? null;
    }

    /**
     * Extract relevant information from schedule rule
     *
     * @param CommandScheduleRule $rule
     * @return array
     */
    private function extractRuleInfo(CommandScheduleRule $rule): array
    {
        return [
            'id' => $rule->id,
            'name' => $rule->name,
            'command' => $rule->command,
            'schedule_expression' => $rule->schedule_expression,
            'schedule_type' => $rule->schedule_type,
            'timezone' => $rule->timezone,
            'is_active' => $rule->is_active,
            'priority' => $rule->priority,
            'description' => $rule->description,
            'created_at' => $rule->created_at?->format('Y-m-d H:i:s T'),
            'updated_at' => $rule->updated_at?->format('Y-m-d H:i:s T'),
        ];
    }

    /**
     * Compare expected structure with actual data structure
     *
     * @param array|string $expected
     * @param mixed $actual
     * @param string $path
     * @return array
     */
    private function compareStructures($expected, $actual, string $path = ''): array
    {
        $result = [
            'is_valid' => true,
            'details' => [],
            'missing_fields' => [],
            'extra_fields' => [],
            'type_mismatches' => []
        ];

        if (is_array($expected)) {
            if (!is_array($actual)) {
                $result['is_valid'] = false;
                $result['type_mismatches'][] = [
                    'path' => $path,
                    'expected' => 'array',
                    'actual' => gettype($actual)
                ];
                return $result;
            }

            // Check each expected field
            foreach ($expected as $key => $expectedType) {
                $currentPath = $path ? "{$path}.{$key}" : $key;
                
                if (!array_key_exists($key, $actual)) {
                    $result['missing_fields'][] = $currentPath;
                    $result['is_valid'] = false;
                    continue;
                }

                // Recursively check nested structures
                $subResult = $this->compareStructures($expectedType, $actual[$key], $currentPath);
                
                if (!$subResult['is_valid']) {
                    $result['is_valid'] = false;
                }
                
                $result['missing_fields'] = array_merge($result['missing_fields'], $subResult['missing_fields']);
                $result['extra_fields'] = array_merge($result['extra_fields'], $subResult['extra_fields']);
                $result['type_mismatches'] = array_merge($result['type_mismatches'], $subResult['type_mismatches']);
            }

        } else {
            // Expected is a type string - validate the actual type
            $actualType = $this->getDataType($actual);
            
            if ($expected !== $actualType) {
                $result['is_valid'] = false;
                $result['type_mismatches'][] = [
                    'path' => $path,
                    'expected' => $expected,
                    'actual' => $actualType
                ];
            }
        }

        return $result;
    }

    /**
     * Get actual data structure representation
     *
     * @param mixed $data
     * @return mixed
     */
    private function getActualStructure($data)
    {
        if (is_array($data)) {
            $structure = [];
            foreach ($data as $key => $value) {
                if (is_array($value) && !empty($value)) {
                    // For arrays, show structure of first element if it exists
                    if (array_key_exists(0, $value)) {
                        $structure[$key] = ['[0]' => $this->getActualStructure($value[0])];
                    } else {
                        $structure[$key] = $this->getActualStructure($value);
                    }
                } else {
                    $structure[$key] = $this->getDataType($value);
                }
            }
            return $structure;
        }
        
        return $this->getDataType($data);
    }

    /**
     * Get data type as string
     *
     * @param mixed $value
     * @return string
     */
    private function getDataType($value): string
    {
        if (is_null($value)) return 'null';
        if (is_bool($value)) return 'boolean';
        if (is_int($value)) return 'integer';
        if (is_float($value)) return 'float';
        if (is_string($value)) return 'string';
        if (is_array($value)) return 'array';
        if (is_object($value)) return 'object';
        
        return gettype($value);
    }

    /**
     * Create a log entry
     *
     * @param string $level
     * @param string $message
     * @return array
     */
    private function createLogEntry(string $level, string $message): array
    {
        return [
            'timestamp' => Carbon::now()->format('H:i:s'),
            'level' => $level,
            'message' => $message
        ];
    }

    /**
     * Create error response structure
     *
     * @param string $error
     * @param array $logs
     * @return array
     */
    private function createErrorResponse(string $error, array $logs): array
    {
        return [
            'rule_info' => null,
            'steps' => [
                [
                    'name' => 'Diagnostic Error',
                    'description' => 'Failed to complete diagnostic',
                    'status' => 'failure',
                    'details' => ['error' => $error]
                ]
            ],
            'overall_status' => 'failure',
            'execution_time' => 0,
            'logs' => $logs,
            'error' => $error
        ];
    }

    /**
     * Get a random user agent string
     *
     * @return string
     */
    private function getRandomUserAgent(): string
    {
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0'
        ];

        return $userAgents[array_rand($userAgents)];
    }

    /**
     * Get appropriate Accept header for provider
     *
     * @param string $providerName
     * @return string
     */
    private function getAcceptHeader(string $providerName): string
    {
        switch ($providerName) {
            case 'Jobs.af':
            case 'Jobs.af (Descriptions)':
                return 'application/json, text/plain, */*';
            case 'ACBAR':
                return 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8';
            default:
                return '*/*';
        }
    }
}
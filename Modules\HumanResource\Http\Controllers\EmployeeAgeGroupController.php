<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Attendance;
use App\AttendanceOption;
use App\Employee;
use App\Student;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentAttendance;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class EmployeeAgeGroupController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function __invoke(Request $request)
    {
//        $record = Employee::select("COUNT(*) as total_employee","DATE_FORMAT(FROM_DAYS(DATEDIFF(now(),date_of_birth)), '%Y')+0 AS age")
//
//
//            ->groupBy('age')
//            ->having('age','>',0)
//            ->orderBy('age', 'desc')
//
//            ->get();
//
//        $data = [];
//
//        foreach ($record as $row) {
//            $data['label'][] = $row->age;
//            $data['data'][] = (int)$row->total_employee;
//        }
//
//        $data['chart_data'] = json_encode($data);
//        return response()->json($data);

    }
}

<?php
declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use App\Services\EmailService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Entities\JobSeekerSetting;

/**
 * MissedCallNotificationService handles immediate notifications when JobSeeker 
 * command schedule rules execute but no notifications are sent to job seekers.
 *
 * Purpose: Provides immediate visibility to admin when scheduled job sync runs 
 * complete but zero job seeker notifications are dispatched, regardless of whether 
 * jobs were found or not. This serves as an early warning system for notification 
 * pipeline issues.
 *
 * Triggered by: JobNotificationHub when notifyAggregatedJobs() returns false, 
 * indicating zero emails were sent during the scheduled execution.
 *
 * Side effects: Sends email to admin_notification_email using EmailService, logs 
 * notification activity with correlation context for tracing.
 *
 * Security: Uses admin_notification_email setting; no sensitive job data included 
 * in emails, only counts and categorical summaries.
 *
 * Errors: Logs failures but does not throw exceptions to avoid disrupting primary 
 * job sync flow; uses circuit breaker protection via EmailService.
 */
final class MissedCallNotificationService
{
    private EmailService $emailService;

    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }

    /**
     * Send a missed call notification when zero job seeker emails are sent.
     *
     * @param array $context Execution context including command, provider, jobs data
     * @return bool True if notification sent successfully, false otherwise
     *
     * Purpose: Alert admin immediately when scheduled job sync completes but no 
     * job seekers receive notifications, providing visibility into pipeline gaps.
     *
     * Inputs: Context array with command name, provider(s), job counts, categories 
     * processed, schedule rule info, and reason why no notifications sent.
     *
     * Side effects: Sends HTML email to admin_notification_email, creates correlation 
     * log entries for tracing, uses EmailService circuit breaker protection.
     *
     * Errors: Catches all exceptions and logs them without re-throwing to avoid 
     * disrupting primary job sync workflow; returns false on any failure.
     */
    public function sendMissedCallNotification(array $context): bool
    {
        $correlationId = 'missed_call_' . uniqid();

        Log::info('MissedCallNotificationService: Preparing missed call notification', [
            'correlation_id' => $correlationId,
            'context' => $context,
        ]);

        try {
            // Get admin notification email from settings
            $adminEmail = JobSeekerSetting::getValue('admin_notification_email');
            if (empty($adminEmail)) {
                Log::warning('MissedCallNotificationService: No admin notification email configured', [
                    'correlation_id' => $correlationId,
                ]);
                return false;
            }

            // Build comprehensive email content
            $emailData = $this->buildEmailContent($context, $correlationId);
            $subject = $this->buildEmailSubject($context);

            // Send email using EmailService (with circuit breaker protection)
            $result = $this->emailService->send(
                $adminEmail,
                $subject,
                $emailData['body'],
                $emailData['view_data'],
                'modules.jobseeker.emails.admin.missed_call_notification',
                [], // attachments
                [], // cc
                null, // from email
                null  // from name
            );

            if ($result === true || (is_array($result) && $result['status'] === 'queued')) {
                Log::info('MissedCallNotificationService: Missed call notification sent successfully', [
                    'correlation_id' => $correlationId,
                    'admin_email' => $adminEmail,
                    'subject' => $subject,
                    'sync_result' => $result,
                ]);
                return true;
            } else {
                Log::warning('MissedCallNotificationService: Unexpected response from EmailService', [
                    'correlation_id' => $correlationId,
                    'result' => $result,
                ]);
                return false;
            }

        } catch (\Throwable $e) {
            Log::error('MissedCallNotificationService: Failed to send missed call notification', [
                'correlation_id' => $correlationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * Build comprehensive email content for missed call notification.
     *
     * @param array $context Execution context
     * @param string $correlationId Unique correlation identifier
     * @return array Email body and view data
     */
    private function buildEmailContent(array $context, string $correlationId): array
    {
        $timestamp = Carbon::now()->format('Y-m-d H:i:s T');
        $command = $context['command'] ?? 'Unknown Command';
        $provider = $context['provider'] ?? 'Unknown Provider';
        $scheduleRuleId = $context['schedule_rule_id'] ?? null;
        $executionId = $context['execution_id'] ?? null;

        // Job statistics
        $totalJobs = $context['total_jobs'] ?? 0;
        $newJobs = $context['new_jobs'] ?? 0;
        $updatedJobs = $context['updated_jobs'] ?? 0;
        $missedJobs = $context['missed_jobs'] ?? 0;

        // Filter information
        $categoriesConsidered = $context['categories_considered'] ?? [];
        $activeSetups = $context['active_setups'] ?? 0;
        $reasonsWhyNoEmails = $context['reasons_no_emails'] ?? [];

        // Build comprehensive diagnostic information
        $diagnostics = $this->gatherDiagnosticInformation($context, $provider);

        // Build structured email body
        $bodyLines = [
            "<h1>🚨 JobSeeker Missed Call Alert</h1>",
            "<p><strong>Scheduled job sync completed but no notifications were sent</strong></p>",
            "<hr>",
            "",
            "<h2>⏱️ Execution Details</h2>",
            "<ul>",
            "<li><strong>Timestamp:</strong> {$timestamp}</li>",
            "<li><strong>Command:</strong> <code>{$command}</code></li>",
            "<li><strong>Provider:</strong> {$provider}</li>",
            "</ul>",
            "",
            "<h2>📊 Job Statistics</h2>",
            "<ul>",
            "<li><strong>Total Jobs:</strong> {$totalJobs}</li>",
            "<li><strong>New Jobs:</strong> {$newJobs}</li>",
            "<li><strong>Updated Jobs:</strong> {$updatedJobs}</li>",
            "<li><strong>Emails Sent:</strong> 0</li>",
            "</ul>",
            "",
        ];

        // Add detailed diagnostic sections
        $bodyLines = array_merge($bodyLines, $this->buildJobAnalysisSection($diagnostics));
        $bodyLines = array_merge($bodyLines, $this->buildCategoryMappingSection($diagnostics));
        $bodyLines = array_merge($bodyLines, $this->buildUserSetupSection($diagnostics));
        $bodyLines = array_merge($bodyLines, $this->buildSystemConfigSection($diagnostics));
        $bodyLines = array_merge($bodyLines, $this->buildPerformanceSection($diagnostics));
        $bodyLines = array_merge($bodyLines, $this->buildActionableStepsSection($diagnostics, $provider));

        // Add reasons why no emails were sent if available
        if (!empty($reasonsWhyNoEmails)) {
            $bodyLines[] = "<h2>🔍 Additional Analysis</h2>";
            $bodyLines[] = "<ul>";
            foreach ($reasonsWhyNoEmails as $reason) {
                $bodyLines[] = "<li>{$reason}</li>";
            }
            $bodyLines[] = "</ul>";
            $bodyLines[] = "";
        }

        // Add tracking information
        $bodyLines[] = "<h2>🔍 Tracking Information</h2>";
        $bodyLines[] = "<ul>";
        if ($scheduleRuleId) {
            $bodyLines[] = "<li><strong>Schedule Rule ID:</strong> {$scheduleRuleId}</li>";
        }
        if ($executionId) {
            $bodyLines[] = "<li><strong>Execution ID:</strong> {$executionId}</li>";
        }
        $bodyLines[] = "<li><strong>Correlation ID:</strong> {$correlationId}</li>";
        $bodyLines[] = "</ul>";
        $bodyLines[] = "";

        $bodyLines[] = "<hr>";
        $bodyLines[] = "<p><em>This is an automated alert from the JobSeeker notification monitoring system.</em></p>";
        $bodyLines[] = "<p>Generated at {$timestamp}</p>";

        $emailBody = implode("\n", $bodyLines);

        return [
            'body' => $emailBody,
            'view_data' => [
                'timestamp' => $timestamp,
                'command' => $command,
                'provider' => $provider,
                'context' => $context,
                'correlation_id' => $correlationId,
                'schedule_rule_id' => $scheduleRuleId,
                'execution_id' => $executionId,
                'diagnostics' => $diagnostics,
            ],
        ];
    }

    /**
     * Gather comprehensive diagnostic information for the missed call notification.
     *
     * @param array $context Execution context
     * @param string $provider Provider name
     * @return array Diagnostic information
     */
    private function gatherDiagnosticInformation(array $context, string $provider): array
    {
        try {
            // Gather database statistics
            $jobStats = $this->gatherJobStatistics($provider);
            $setupStats = $this->gatherSetupStatistics();
            $categoryStats = $this->gatherCategoryMappingStats($provider);
            $systemHealth = $this->gatherSystemHealthMetrics();
            $recentJobs = $this->getRecentJobsForAnalysis($provider, 10);
            
            return [
                'jobs' => $jobStats,
                'setups' => $setupStats,
                'categories' => $categoryStats,
                'system' => $systemHealth,
                'recent_jobs' => $recentJobs,
                'timestamp' => Carbon::now(),
            ];
        } catch (\Throwable $e) {
            Log::warning('MissedCallNotificationService: Failed to gather diagnostics', [
                'error' => $e->getMessage(),
                'provider' => $provider,
            ]);
            
            return [
                'error' => 'Failed to gather diagnostic information: ' . $e->getMessage(),
                'timestamp' => Carbon::now(),
            ];
        }
    }

    /**
     * Gather job statistics for diagnostic purposes.
     */
    private function gatherJobStatistics(string $provider): array
    {
        $stats = [];
        
        // Total jobs by provider in last 24 hours
        $recentCount = DB::table('jobs')
            ->where('source', $provider)
            ->where('created_at', '>=', Carbon::now()->subDay())
            ->count();
        
        // Jobs without category mappings
        $unmappedCount = DB::table('jobs')
            ->where('source', $provider)
            ->whereNotExists(function($query) {
                $query->select(DB::raw(1))
                    ->from('job_provider_category_pivot')
                    ->whereColumn('job_provider_category_pivot.job_id', 'jobs.id');
            })
            ->where('created_at', '>=', Carbon::now()->subDay())
            ->count();
        
        return [
            'recent_jobs_24h' => $recentCount,
            'unmapped_jobs' => $unmappedCount,
            'mapping_rate' => $recentCount > 0 ? round((($recentCount - $unmappedCount) / $recentCount) * 100, 1) : 0,
        ];
    }

    /**
     * Gather notification setup statistics.
     */
    private function gatherSetupStatistics(): array
    {
        $activeSetups = DB::table('job_notification_setups')
            ->where('is_active', 1)
            ->count();
        
        $setupsWithEmails = DB::table('job_notification_setups')
            ->join('job_seekers', 'job_notification_setups.job_seeker_id', '=', 'job_seekers.id')
            ->where('job_notification_setups.is_active', 1)
            ->whereNotNull('job_seekers.email')
            ->where('job_seekers.email', '!=', '')
            ->count();
        
        $providerSpecificSetups = DB::table('job_notification_setups')
            ->where('is_active', 1)
            ->whereNotNull('provider_name')
            ->where('provider_name', '!=', '')
            ->count();
        
        return [
            'total_active' => $activeSetups,
            'with_valid_emails' => $setupsWithEmails,
            'provider_specific' => $providerSpecificSetups,
            'provider_agnostic' => $activeSetups - $providerSpecificSetups,
        ];
    }

    /**
     * Gather category mapping statistics.
     */
    private function gatherCategoryMappingStats(string $provider): array
    {
        $totalProviderCategories = DB::table('provider_job_categories')
            ->where('provider_name', $provider)
            ->count();
        
        $mappedCategories = DB::table('provider_job_categories')
            ->where('provider_name', $provider)
            ->whereNotNull('canonical_category_id')
            ->count();
        
        $unmappedCategories = DB::table('provider_job_categories')
            ->where('provider_name', $provider)
            ->where(function($query) {
                $query->whereNull('canonical_category_id')
                    ->orWhere('canonical_category_id', 0);
            })
            ->pluck('category_name')
            ->toArray();
        
        return [
            'total_provider_categories' => $totalProviderCategories,
            'mapped_categories' => $mappedCategories,
            'unmapped_count' => count($unmappedCategories),
            'unmapped_categories' => array_slice($unmappedCategories, 0, 5), // Limit for email readability
            'mapping_coverage' => $totalProviderCategories > 0 ? round(($mappedCategories / $totalProviderCategories) * 100, 1) : 0,
        ];
    }

    /**
     * Gather system health metrics.
     */
    private function gatherSystemHealthMetrics(): array
    {
        // Aggregation limits status
        $aggregationSettings = DB::table('jobseeker_email_content_settings')
            ->where('field_name', 'aggregation_prefs')
            ->first();
        
        $aggregationEnabled = true;
        $minJobs = 1;
        $maxJobs = 20;
        
        if ($aggregationSettings && $aggregationSettings->formatting_options) {
            $options = json_decode($aggregationSettings->formatting_options, true);
            $aggregationEnabled = $options['enabled'] ?? true;
            $minJobs = $options['min_jobs_per_email'] ?? 1;
            $maxJobs = $options['max_jobs_per_email'] ?? 20;
        }
        
        // Email service health
        $recentEmailCount = DB::table('outgoing_emails')
            ->where('created_at', '>=', Carbon::now()->subHour())
            ->count();
        
        $failedEmailCount = DB::table('outgoing_emails')
            ->where('created_at', '>=', Carbon::now()->subHour())
            ->where('status', 'failed')
            ->count();
        
        return [
            'aggregation_enabled' => $aggregationEnabled,
            'min_jobs_threshold' => $minJobs,
            'max_jobs_threshold' => $maxJobs,
            'recent_emails_1h' => $recentEmailCount,
            'failed_emails_1h' => $failedEmailCount,
            'email_success_rate' => $recentEmailCount > 0 ? round((($recentEmailCount - $failedEmailCount) / $recentEmailCount) * 100, 1) : 'N/A',
        ];
    }

    /**
     * Get recent jobs for analysis.
     */
    private function getRecentJobsForAnalysis(string $provider, int $limit = 10): array
    {
        $jobs = DB::table('jobs')
            ->select('id', 'position', 'company_name', 'source', 'created_at')
            ->where('source', $provider)
            ->where('created_at', '>=', Carbon::now()->subDay())
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
        
        return $jobs->map(function($job) {
            // Check if job has category mappings
            $hasMappings = DB::table('job_provider_category_pivot')
                ->where('job_id', $job->id)
                ->exists();
            
            return [
                'id' => $job->id,
                'position' => $job->position,
                'company_name' => $job->company_name,
                'created_at' => $job->created_at,
                'has_category_mappings' => $hasMappings,
            ];
        })->toArray();
    }

    /**
     * Build job analysis section for email.
     */
    private function buildJobAnalysisSection(array $diagnostics): array
    {
        if (isset($diagnostics['error'])) {
            return [
                "<h2>🔧 Notification Setup Status</h2>",
                "<p><strong>❌ Error gathering diagnostic information:</strong> {$diagnostics['error']}</p>",
                "",
            ];
        }
        
        $jobStats = $diagnostics['jobs'] ?? [];
        $recentJobs = $diagnostics['recent_jobs'] ?? [];
        
        $lines = [
            "<h2>🔧 Notification Setup Status</h2>",
            "<ul>",
            "<li><strong>Jobs in last 24h:</strong> " . ($jobStats['recent_jobs_24h'] ?? 'Unknown'),
            "<li><strong>Jobs without category mappings:</strong> " . ($jobStats['unmapped_jobs'] ?? 'Unknown'),
            "<li><strong>Category mapping rate:</strong> " . ($jobStats['mapping_rate'] ?? 'Unknown') . "%</li>",
            "</ul>",
            "",
        ];
        
        if (!empty($recentJobs)) {
            $lines[] = "<h3>📋 Recent Jobs Analysis</h3>";
            $lines[] = "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            $lines[] = "<tr><th>Job ID</th><th>Position</th><th>Company</th><th>Has Categories</th></tr>";
            
            foreach (array_slice($recentJobs, 0, 5) as $job) {
                $hasCategories = $job['has_category_mappings'] ? '✅' : '❌';
                $lines[] = "<tr>";
                $lines[] = "<td>{$job['id']}</td>";
                $lines[] = "<td>" . htmlspecialchars($job['position']) . "</td>";
                $lines[] = "<td>" . htmlspecialchars($job['company_name']) . "</td>";
                $lines[] = "<td>{$hasCategories}</td>";
                $lines[] = "</tr>";
            }
            
            $lines[] = "</table>";
            $lines[] = "";
        }
        
        return $lines;
    }

    /**
     * Build category mapping section for email.
     */
    private function buildCategoryMappingSection(array $diagnostics): array
    {
        if (isset($diagnostics['error'])) {
            return [];
        }
        
        $categoryStats = $diagnostics['categories'] ?? [];
        
        $lines = [
            "<h2>📂 Category Mapping Status</h2>",
            "<ul>",
            "<li><strong>Total provider categories:</strong> " . ($categoryStats['total_provider_categories'] ?? 'Unknown'),
            "<li><strong>Mapped categories:</strong> " . ($categoryStats['mapped_categories'] ?? 'Unknown'),
            "<li><strong>Unmapped categories:</strong> " . ($categoryStats['unmapped_count'] ?? 'Unknown'),
            "<li><strong>Mapping coverage:</strong> " . ($categoryStats['mapping_coverage'] ?? 'Unknown') . "%</li>",
            "</ul>",
        ];
        
        if (!empty($categoryStats['unmapped_categories'])) {
            $lines[] = "<h3>⚠️ Unmapped Categories (Sample)</h3>";
            $lines[] = "<ul>";
            foreach ($categoryStats['unmapped_categories'] as $category) {
                $lines[] = "<li>" . htmlspecialchars($category) . "</li>";
            }
            $lines[] = "</ul>";
        }
        
        $lines[] = "";
        return $lines;
    }

    /**
     * Build user setup section for email.
     */
    private function buildUserSetupSection(array $diagnostics): array
    {
        if (isset($diagnostics['error'])) {
            return [];
        }
        
        $setupStats = $diagnostics['setups'] ?? [];
        
        return [
            "<h2>👥 User Setup Configuration</h2>",
            "<ul>",
            "<li><strong>Total active setups:</strong> " . ($setupStats['total_active'] ?? 'Unknown'),
            "<li><strong>Setups with valid emails:</strong> " . ($setupStats['with_valid_emails'] ?? 'Unknown'),
            "<li><strong>Provider-specific setups:</strong> " . ($setupStats['provider_specific'] ?? 'Unknown'),
            "<li><strong>Provider-agnostic setups:</strong> " . ($setupStats['provider_agnostic'] ?? 'Unknown'),
            "</ul>",
            "",
        ];
    }

    /**
     * Build system configuration section for email.
     */
    private function buildSystemConfigSection(array $diagnostics): array
    {
        if (isset($diagnostics['error'])) {
            return [];
        }
        
        $systemStats = $diagnostics['system'] ?? [];
        
        return [
            "<h2>⚙️ System Configuration</h2>",
            "<ul>",
            "<li><strong>Aggregation limits enabled:</strong> " . ($systemStats['aggregation_enabled'] ? 'Yes' : 'No'),
            "<li><strong>Min jobs threshold:</strong> " . ($systemStats['min_jobs_threshold'] ?? 'Unknown'),
            "<li><strong>Max jobs threshold:</strong> " . ($systemStats['max_jobs_threshold'] ?? 'Unknown'),
            "</ul>",
            "",
        ];
    }

    /**
     * Build performance section for email.
     */
    private function buildPerformanceSection(array $diagnostics): array
    {
        if (isset($diagnostics['error'])) {
            return [];
        }
        
        $systemStats = $diagnostics['system'] ?? [];
        
        return [
            "<h2>📈 Performance Metrics</h2>",
            "<ul>",
            "<li><strong>Emails sent (last hour):</strong> " . ($systemStats['recent_emails_1h'] ?? 'Unknown'),
            "<li><strong>Failed emails (last hour):</strong> " . ($systemStats['failed_emails_1h'] ?? 'Unknown'),
            "<li><strong>Email success rate:</strong> " . ($systemStats['email_success_rate'] ?? 'Unknown') . '%',
            "</ul>",
            "",
        ];
    }

    /**
     * Build actionable steps section for email.
     */
    private function buildActionableStepsSection(array $diagnostics, string $provider): array
    {
        $lines = [
            "<h2>✅ Recommended Actions</h2>",
        ];
        
        if (!isset($diagnostics['error'])) {
            $categoryStats = $diagnostics['categories'] ?? [];
            $setupStats = $diagnostics['setups'] ?? [];
            $systemStats = $diagnostics['system'] ?? [];
            
            // Specific recommendations based on diagnostics
            if (($categoryStats['mapping_coverage'] ?? 0) < 80) {
                $lines[] = "<h3>🚨 URGENT: Category Mapping Issues</h3>";
                $lines[] = "<p>Only " . ($categoryStats['mapping_coverage'] ?? 0) . "% of provider categories are mapped to canonical categories.</p>";
                $lines[] = "<p><strong>Action:</strong> <a href='" . url('/admin/jobseeker/email-control-board') . "'>Review category mappings in Email Control Board</a></p>";
                if (!empty($categoryStats['unmapped_categories'])) {
                    $lines[] = "<p><strong>Sample unmapped categories:</strong> " . implode(', ', array_slice($categoryStats['unmapped_categories'], 0, 3)) . "</p>";
                }
                $lines[] = "";
            }
            
            if (($setupStats['with_valid_emails'] ?? 0) == 0) {
                $lines[] = "<h3>🚨 URGENT: No Valid Email Setups</h3>";
                $lines[] = "<p>No active notification setups have valid email addresses.</p>";
                $lines[] = "<p><strong>Action:</strong> <a href='" . url('/admin/jobseeker/email-control-board') . "'>Check notification setups</a></p>";
                $lines[] = "";
            }
            
            if (($systemStats['aggregation_enabled'] ?? false) && ($systemStats['min_jobs_threshold'] ?? 0) > 1) {
                $lines[] = "<h3>⚠️ Aggregation Threshold Check</h3>";
                $lines[] = "<p>Minimum jobs threshold is set to " . ($systemStats['min_jobs_threshold'] ?? 0) . ". Jobs may be deferred if below this count.</p>";
                $lines[] = "<p><strong>Action:</strong> <a href='" . url('/admin/jobseeker/email-control-board') . "'>Review aggregation limits</a></p>";
                $lines[] = "";
            }
        }
        
        // General recommendations
        $lines[] = "<h3>📋 General Diagnostic Steps</h3>";
        $lines[] = "<ol>";
        $lines[] = "<li><strong>Check Job Categories:</strong>";
        $lines[] = "<br><code>SELECT COUNT(*) FROM jobs j LEFT JOIN job_provider_category_pivot p ON j.id = p.job_id WHERE j.source = '{$provider}' AND p.job_id IS NULL;</code></li>";
        $lines[] = "<li><strong>Check Active Setups:</strong>";
        $lines[] = "<br><code>SELECT COUNT(*) FROM job_notification_setups s JOIN job_seekers js ON s.job_seeker_id = js.id WHERE s.is_active = 1 AND js.email IS NOT NULL;</code></li>";
        $lines[] = "<li><strong>Check Category Mappings:</strong>";
        $lines[] = "<br><code>SELECT * FROM provider_job_categories WHERE provider_name = '{$provider}' AND canonical_category_id IS NULL;</code></li>";
        $lines[] = "<li><strong>Review System Logs:</strong>";
        $lines[] = "<br>Check <code>storage/logs</code> for JobNotificationService entries around the sync time.</li>";
        $lines[] = "<li><strong>Access Admin Tools:</strong>";
        $lines[] = "<br><a href='" . url('/admin/jobseeker/email-control-board') . "'>Email Control Board</a> | <a href='" . url('/admin/jobseeker/email-content-manager') . "'>Content Manager</a></li>";
        $lines[] = "</ol>";
        
        return $lines;
    }

    /**
     * Build email subject for missed call notification.
     *
     * @param array $context Execution context
     * @return string Email subject
     */
    private function buildEmailSubject(array $context): string
    {
        $command = $context['command'] ?? 'JobSeeker Command';
        $provider = $context['provider'] ?? 'Unknown Provider';
        $totalJobs = $context['total_jobs'] ?? 0;

        return "🚨 JobSeeker Missed Call: {$provider} sync completed, {$totalJobs} jobs found, 0 notifications sent";
    }
}

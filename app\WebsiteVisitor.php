<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\SoftDeletes;
use \Illuminate\Support\Str;

class WebsiteVisitor extends Model
{

    use SoftDeletes;


    public $attributes = ['hits' => 0];
    protected $table = 'visitors';
    protected $casts = [
        'date' => 'datetime:Y-m-d',
    ];
    protected $fillable = [
        'ip'
        , 'user_id'
        , 'date'
        , 'hits'
        , 'url'


    ];
    public $timestamps = false;





    public static function boot() {
        // Any time the instance is updated (but not created)
        static::saving( function ($tracker) {
            $tracker->visit_time = date('H:i:s');
            $tracker->hits++;
        } );
    }


}

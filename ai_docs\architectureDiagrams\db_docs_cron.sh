#!/bin/bash

# Path to the project directory - adjust as needed
PROJECT_DIR="/Users/<USER>/Herd/itqan"
DOCS_DIR="$PROJECT_DIR/ai_docs/architectureDiagrams"

# Go to the docs directory
cd "$DOCS_DIR" || exit 1

# Run the PHP script silently with 'yes' piped to it for the confirmation
echo "y" | php generate_table_definitions.php

# Log the update
echo "Database documentation updated: $(date)" >> "$DOCS_DIR/db_docs_update.log" 
<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

/**
 * Email Sending Log Model
 * 
 * Tracks email sending attempts, their outcomes, and provider information
 * for monitoring and debugging purposes.
 */
final class EmailSendingLog extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'email_sending_logs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'recipient',
        'subject',
        'provider',
        'mode',
        'status',
        'error_message',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope to filter by status
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by provider
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $provider
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByProvider($query, string $provider)
    {
        return $query->where('provider', $provider);
    }

    /**
     * Scope to filter by recipient
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $recipient
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByRecipient($query, string $recipient)
    {
        return $query->where('recipient', $recipient);
    }

    /**
     * Scope to get recent logs
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $limit = 50)
    {
        return $query->orderBy('created_at', 'desc')->limit($limit);
    }

    /**
     * Check if the log entry represents a successful send
     *
     * @return bool
     */
    public function isSuccess(): bool
    {
        return $this->status === 'success';
    }

    /**
     * Check if the log entry represents a failed send
     *
     * @return bool
     */
    public function isFailure(): bool
    {
        return $this->status === 'failure';
    }

    /**
     * Check if the log entry represents a queued send
     *
     * @return bool
     */
    public function isQueued(): bool
    {
        return $this->status === 'queued';
    }

    /**
     * Get formatted created date
     *
     * @return string
     */
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at->format('Y-m-d H:i:s');
    }

    /**
     * Create a new log entry for a successful email send
     *
     * @param string $recipient
     * @param string $subject
     * @param string $provider
     * @param string $mode
     * @return self
     */
    public static function logSuccess(string $recipient, string $subject, string $provider, string $mode): self
    {
        return self::create([
            'recipient' => $recipient,
            'subject' => $subject,
            'provider' => $provider,
            'mode' => $mode,
            'status' => 'success',
        ]);
    }

    /**
     * Create a new log entry for a failed email send
     *
     * @param string $recipient
     * @param string $subject
     * @param string $provider
     * @param string $mode
     * @param string|null $errorMessage
     * @return self
     */
    public static function logFailure(string $recipient, string $subject, string $provider, string $mode, ?string $errorMessage = null): self
    {
        return self::create([
            'recipient' => $recipient,
            'subject' => $subject,
            'provider' => $provider,
            'mode' => $mode,
            'status' => 'failure',
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Create a new log entry for a queued email send
     *
     * @param string $recipient
     * @param string $subject
     * @param string $provider
     * @param string $mode
     * @return self
     */
    public static function logQueued(string $recipient, string $subject, string $provider, string $mode): self
    {
        return self::create([
            'recipient' => $recipient,
            'subject' => $subject,
            'provider' => $provider,
            'mode' => $mode,
            'status' => 'queued',
        ]);
    }
} 
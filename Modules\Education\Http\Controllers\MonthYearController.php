<?php

namespace Modules\Education\Http\Controllers;


use App\Classes;
use App\EvaluationSchemaOption;
use App\MoshafSurah;
use App\Student;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;


class MonthYearController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function getMonthYears(Student $student,Classes $class)
    {




        $memorizationDates = \App\StudentHefzReport::where('student_id', $student->id)->where('class_id', $class->id)
            ->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year, class_id')
            ->groupBy('year', 'month', 'class_id')
            ->orderByDesc('year')
            ->orderByRaw("FIELD(month, 'December', 'November', 'October', 'September', 'August', 'July', 'June', 'May', 'April', 'March', 'February', 'January')")
            ->get();

        $revisionDates = \App\StudentRevisionReport::where('student_id', $student->id)->where('class_id', $class->id)
            ->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year, class_id')
            ->groupBy('year', 'month', 'class_id')
            ->orderByDesc('year')
            ->orderByRaw("FIELD(month, 'December', 'November', 'October', 'September', 'August', 'July', 'June', 'May', 'April', 'March', 'February', 'January')")
            ->get();


        $commonDates = $memorizationDates->filter(function ($memDate) use ($revisionDates) {
            return $revisionDates->contains(function ($revDate) use ($memDate) {
                return $revDate->month === $memDate->month && $revDate->year === $memDate->year && $revDate->class_id === $memDate->class_id;
            });
        });
        $datesWithCount = $commonDates->map(function($date) {
            $classTimetable = \App\ClassTimetable::where('class_id', $date->class_id)->first();
            if($classTimetable) {
                // Parse month name to month number
                $monthNumber = date('n', strtotime($date->month));
                $date->days_count_per_month = $classTimetable->daysCountPerMonth($monthNumber, $date->year);
            } else {
                $date->days_count_per_month = null;
            }
            return $date;
        });

        return response()->json($datesWithCount);

    }
}

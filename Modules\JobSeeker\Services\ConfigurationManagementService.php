<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Modules\JobSeeker\Services\SystemErrorNotificationService;

/**
 * Configuration Management Service
 * 
 * Monitors critical configuration settings and prevents accidental
 * disabling of essential system features like email notifications.
 * 
 * Prevents issues like the 46-day email notification outage caused by
 * JOBSEEKER_DISABLE_EVENT_NOTIFICATIONS=true
 */
final class ConfigurationManagementService
{
    private SystemErrorNotificationService $errorNotificationService;

    /**
     * Critical configuration settings that should be monitored
     */
    private const CRITICAL_CONFIGS = [
        'jobseeker.disable_event_notifications' => [
            'expected' => false,
            'description' => 'Email notifications should be enabled',
            'severity' => 'critical',
        ],
        'mail.default' => [
            'expected' => ['smtp', 'sendmail', 'mailgun', 'ses'],
            'description' => 'Mail driver should be configured',
            'severity' => 'critical',
        ],
        'queue.default' => [
            'expected' => ['database', 'redis', 'sqs'],
            'description' => 'Queue driver should be configured',
            'severity' => 'high',
        ],
        'app.debug' => [
            'expected' => false,
            'description' => 'Debug mode should be disabled in production',
            'severity' => 'medium',
            'environment' => 'production',
        ],
        'database.default' => [
            'expected' => ['mysql', 'pgsql'],
            'description' => 'Database connection should be configured',
            'severity' => 'critical',
        ],
    ];

    /**
     * Configuration change tracking
     */
    private const CONFIG_CHANGE_CACHE_KEY = 'config_changes_tracked';
    private const CONFIG_BASELINE_CACHE_KEY = 'config_baseline';

    public function __construct(SystemErrorNotificationService $errorNotificationService)
    {
        $this->errorNotificationService = $errorNotificationService;
    }

    /**
     * Validate all critical configurations
     */
    public function validateCriticalConfigurations(): array
    {
        Log::info('ConfigurationManagement: Starting critical configuration validation');

        $results = [];
        $issues = [];

        foreach (self::CRITICAL_CONFIGS as $configKey => $requirements) {
            try {
                $result = $this->validateSingleConfiguration($configKey, $requirements);
                $results[$configKey] = $result;

                if (!$result['valid']) {
                    $issues[] = [
                        'config' => $configKey,
                        'issue' => $result['message'],
                        'severity' => $requirements['severity'],
                        'current_value' => $result['current_value'],
                        'expected_value' => $requirements['expected'],
                    ];
                }

            } catch (Exception $e) {
                $results[$configKey] = [
                    'valid' => false,
                    'message' => 'Validation failed: ' . $e->getMessage(),
                    'error' => true,
                ];

                $issues[] = [
                    'config' => $configKey,
                    'issue' => 'Configuration validation error: ' . $e->getMessage(),
                    'severity' => 'critical',
                ];
            }
        }

        // Report critical issues
        if (!empty($issues)) {
            $this->reportConfigurationIssues($issues);
        }

        Log::info('ConfigurationManagement: Configuration validation completed', [
            'total_configs' => count(self::CRITICAL_CONFIGS),
            'issues_found' => count($issues),
        ]);

        return [
            'results' => $results,
            'issues' => $issues,
            'valid' => empty($issues),
        ];
    }

    /**
     * Validate a single configuration setting
     */
    private function validateSingleConfiguration(string $configKey, array $requirements): array
    {
        $currentValue = config($configKey);
        $expected = $requirements['expected'];
        $environment = $requirements['environment'] ?? null;

        // Skip environment-specific checks if not in that environment
        if ($environment && app()->environment() !== $environment) {
            return [
                'valid' => true,
                'message' => "Skipped (not in {$environment} environment)",
                'current_value' => $currentValue,
                'skipped' => true,
            ];
        }

        $isValid = $this->isConfigurationValid($currentValue, $expected);

        return [
            'valid' => $isValid,
            'message' => $isValid 
                ? 'Configuration is valid'
                : $this->getValidationErrorMessage($configKey, $currentValue, $expected, $requirements['description']),
            'current_value' => $currentValue,
            'expected_value' => $expected,
        ];
    }

    /**
     * Check if configuration value is valid
     */
    private function isConfigurationValid($currentValue, $expected): bool
    {
        if (is_array($expected)) {
            return in_array($currentValue, $expected, true);
        }

        return $currentValue === $expected;
    }

    /**
     * Get validation error message
     */
    private function getValidationErrorMessage(string $configKey, $currentValue, $expected, string $description): string
    {
        $currentStr = is_bool($currentValue) ? ($currentValue ? 'true' : 'false') : (string) $currentValue;
        
        if (is_array($expected)) {
            $expectedStr = implode(', ', $expected);
            return "Configuration '{$configKey}' has invalid value '{$currentStr}'. Expected one of: {$expectedStr}. {$description}";
        }

        $expectedStr = is_bool($expected) ? ($expected ? 'true' : 'false') : (string) $expected;
        return "Configuration '{$configKey}' has invalid value '{$currentStr}'. Expected: {$expectedStr}. {$description}";
    }

    /**
     * Report configuration issues
     */
    private function reportConfigurationIssues(array $issues): void
    {
        foreach ($issues as $issue) {
            $severity = match ($issue['severity']) {
                'critical' => 'critical',
                'high' => 'high',
                'medium' => 'medium',
                default => 'low',
            };

            $this->errorNotificationService->reportSystemError(
                'Configuration Management',
                "Critical configuration issue: {$issue['issue']}",
                [
                    'config_key' => $issue['config'],
                    'current_value' => $issue['current_value'] ?? null,
                    'expected_value' => $issue['expected_value'] ?? null,
                    'severity' => $issue['severity'],
                    'validation_timestamp' => now()->toDateTimeString(),
                ]
            );
        }
    }

    /**
     * Track configuration changes over time
     */
    public function trackConfigurationChanges(): array
    {
        $currentConfig = $this->getCurrentCriticalConfig();
        $baseline = Cache::get(self::CONFIG_BASELINE_CACHE_KEY);

        if (!$baseline) {
            // First time - establish baseline
            Cache::put(self::CONFIG_BASELINE_CACHE_KEY, $currentConfig, now()->addDays(30));
            
            Log::info('ConfigurationManagement: Established configuration baseline');
            
            return [
                'baseline_established' => true,
                'changes' => [],
            ];
        }

        // Compare with baseline
        $changes = $this->detectConfigurationChanges($baseline, $currentConfig);

        if (!empty($changes)) {
            Log::warning('ConfigurationManagement: Configuration changes detected', [
                'changes' => $changes,
            ]);

            // Report significant changes
            $this->reportConfigurationChanges($changes);

            // Update baseline
            Cache::put(self::CONFIG_BASELINE_CACHE_KEY, $currentConfig, now()->addDays(30));
        }

        return [
            'baseline_established' => false,
            'changes' => $changes,
        ];
    }

    /**
     * Get current critical configuration values
     */
    private function getCurrentCriticalConfig(): array
    {
        $config = [];
        
        foreach (array_keys(self::CRITICAL_CONFIGS) as $configKey) {
            $config[$configKey] = config($configKey);
        }

        return $config;
    }

    /**
     * Detect configuration changes
     */
    private function detectConfigurationChanges(array $baseline, array $current): array
    {
        $changes = [];

        foreach ($current as $key => $value) {
            $baselineValue = $baseline[$key] ?? null;
            
            if ($baselineValue !== $value) {
                $changes[] = [
                    'config' => $key,
                    'old_value' => $baselineValue,
                    'new_value' => $value,
                    'changed_at' => now()->toDateTimeString(),
                ];
            }
        }

        return $changes;
    }

    /**
     * Report configuration changes
     */
    private function reportConfigurationChanges(array $changes): void
    {
        foreach ($changes as $change) {
            $configKey = $change['config'];
            $requirements = self::CRITICAL_CONFIGS[$configKey] ?? [];
            $severity = $requirements['severity'] ?? 'medium';

            // Special handling for critical configurations
            if ($configKey === 'jobseeker.disable_event_notifications' && $change['new_value'] === true) {
                $severity = 'critical';
            }

            $this->errorNotificationService->reportSystemError(
                'Configuration Change Detected',
                "Critical configuration '{$configKey}' changed from '{$change['old_value']}' to '{$change['new_value']}'",
                [
                    'config_key' => $configKey,
                    'old_value' => $change['old_value'],
                    'new_value' => $change['new_value'],
                    'change_timestamp' => $change['changed_at'],
                    'severity' => $severity,
                ]
            );
        }
    }

    /**
     * Get configuration health summary
     */
    public function getConfigurationHealthSummary(): array
    {
        $validation = $this->validateCriticalConfigurations();
        $changes = $this->trackConfigurationChanges();

        return [
            'overall_health' => $validation['valid'] ? 'healthy' : 'critical',
            'total_configs_monitored' => count(self::CRITICAL_CONFIGS),
            'issues_found' => count($validation['issues']),
            'recent_changes' => count($changes['changes']),
            'last_check' => now()->toDateTimeString(),
            'details' => [
                'validation' => $validation,
                'changes' => $changes,
            ],
        ];
    }

    /**
     * Fix common configuration issues automatically (where safe)
     */
    public function attemptConfigurationFix(string $configKey): array
    {
        Log::info('ConfigurationManagement: Attempting configuration fix', [
            'config' => $configKey,
        ]);

        // Only attempt fixes for specific safe configurations
        $safeFixes = [
            'jobseeker.disable_event_notifications' => false,
        ];

        if (!isset($safeFixes[$configKey])) {
            return [
                'success' => false,
                'message' => 'Automatic fix not available for this configuration',
            ];
        }

        try {
            // Note: This would require writing to config files or environment
            // For safety, we'll just log the recommended fix
            $recommendedValue = $safeFixes[$configKey];
            
            Log::warning('ConfigurationManagement: Configuration fix recommended', [
                'config' => $configKey,
                'current_value' => config($configKey),
                'recommended_value' => $recommendedValue,
                'action_required' => 'Manual configuration update needed',
            ]);

            return [
                'success' => false,
                'message' => 'Manual configuration update required',
                'recommended_value' => $recommendedValue,
                'instructions' => "Update {$configKey} to {$recommendedValue} in your configuration",
            ];

        } catch (Exception $e) {
            Log::error('ConfigurationManagement: Configuration fix failed', [
                'config' => $configKey,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Configuration fix failed: ' . $e->getMessage(),
            ];
        }
    }
}

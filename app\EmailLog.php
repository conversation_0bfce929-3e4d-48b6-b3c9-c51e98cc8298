<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * EmailLog Model
 * 
 * Logs all email sending attempts with detailed tracking information
 * for debugging and monitoring purposes.
 */
class EmailLog extends Model
{
    use HasFactory;

    protected $table = 'email_logs';
    
    protected $fillable = [
        'from',
        'to',
        'subject',
        'body',
        'view',
        'success',
        'error_message',
        'correlation_id',
        'is_dry_run',
    ];

    protected $casts = [
        'success' => 'boolean',
        'is_dry_run' => 'boolean',
    ];

    /**
     * Scope for successful emails
     */
    public function scopeSuccessful($query)
    {
        return $query->where('success', true);
    }

    /**
     * Scope for failed emails
     */
    public function scopeFailed($query)
    {
        return $query->where('success', false);
    }

    /**
     * Scope for emails by correlation ID
     */
    public function scopeByCorrelationId($query, $correlationId)
    {
        return $query->where('correlation_id', $correlationId);
    }
}

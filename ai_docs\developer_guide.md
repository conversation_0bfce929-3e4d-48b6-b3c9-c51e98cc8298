# Itqan Al Quran - Developer Guide

This guide is designed to help new developers quickly onboard to the Itqan Al Quran project. It provides essential information, setup instructions, and best practices.

## Getting Started

### Prerequisites

- PHP 8.1 or higher
- Composer
- MySQL 5.7 or higher
- Node.js and NPM (for frontend assets)
- Git

### Local Environment Setup

1. Clone the repository
   ```bash
   git clone [repository-url]
   cd itqan
   ```

2. Install PHP dependencies
   ```bash
   composer install
   ```

3. Configure environment settings
   - Copy `.env.example` to `.env`
   - Update database connection settings in `.env`

4. Create the database
   ```bash
   mysql -u root -p
   CREATE DATABASE itqan;
   exit
   ```

5. Import the database schema
   ```bash
   mysql -u root itqan < database/sql_files/schema.sql
   ```

6. Install frontend dependencies
   ```bash
   npm install
   ```

7. Compile assets
   ```bash
   npm run dev
   ```

8. Generate application key
   ```bash
   php artisan key:generate
   ```

9. Start the development server
   ```bash
   php artisan serve
   ```

## Project Structure

The project follows a modular architecture using the nwidart/laravel-modules package. For a complete overview, refer to the [Project Structure Document](project_structure.md).

### Key Points:

- Module code is located in `Modules/` directory
- Each module contains Controllers, Entities (Models), Services, etc.
- **Important**: Module views are located in `resources/views/modules/{module-name-lowercase}/` rather than in the module's Resources directory
- Database schema changes are made using direct SQL files in `database/sql_files/`

## Development Workflow

### Creating a New Feature

1. **Understand the Requirements**
   - Review the task description and requirements
   - Identify which module(s) the feature belongs to

2. **Create a Feature Branch**
   ```bash
   git checkout -b feature/feature-name
   ```

3. **Implement the Feature**
   - Follow the project's coding standards and patterns
   - Ensure backward compatibility with existing code
   - Write necessary tests

4. **Test Your Implementation**
   - Run existing tests
   - Test manually in a development environment
   - Ensure the feature works as expected

5. **Submit for Review**
   - Commit your changes with a descriptive commit message
   - Push your branch and create a pull request
   - Respond to review feedback and make necessary changes

### Bug Fixes

1. **Understand the Issue**
   - Reproduce the bug in a development environment
   - Identify the root cause

2. **Create a Fix Branch**
   ```bash
   git checkout -b fix/bug-name
   ```

3. **Implement the Fix**
   - Keep the fix focused on the specific issue
   - Ensure backward compatibility
   - Add tests to prevent regression

4. **Verify the Fix**
   - Confirm the bug is resolved
   - Ensure existing functionality still works

5. **Submit for Review**
   - Include a clear description of the fix in your commit message
   - Reference the issue number if applicable

## Working with Modules

### Creating a New Module

Use the Laravel Modules command to create a new module:

```bash
php artisan module:make ModuleName
```

### Module Structure

A typical module structure includes:

- `Http/Controllers/` - Controllers for handling requests
- `Http/routes.php` - Web routes
- `Http/api.php` - API routes
- `Entities/` - Eloquent models
- `Services/` - Business logic services
- `Repositories/` - Data access repositories
- `Providers/` - Service providers

**Important**: Module views are stored in `resources/views/modules/{module-name-lowercase}/` and not in the module's Resources directory.

### Registering a Module

After creating a module, ensure it's registered in the `modules_statuses.json` file at the project root:

```json
{
    "ModuleName": true
}
```

## Database Management

### Schema Changes

1. Create a new SQL file in `database/sql_files/` using the timestamp naming convention:
   ```
   YYYYMMDD_HHMMSS_descriptive_name.sql
   ```

2. Include detailed comments explaining the changes:
   ```sql
   -- Add new column to table
   ALTER TABLE table_name 
   ADD COLUMN column_name VARCHAR(50) NULL;
   
   -- Create index for better performance
   CREATE INDEX idx_column_name ON table_name (column_name);
   ```

3. Apply the schema changes to your development database:
   ```bash
   mysql -u root itqan < database/sql_files/YYYYMMDD_HHMMSS_descriptive_name.sql
   ```

### Working with Eloquent

- Use Eloquent models for all data manipulation
- Define relationships between models
- Use eager loading to prevent N+1 query issues
- Follow the repository pattern for complex data access logic

## View Management

### Creating Module Views

1. Create the view in the correct location:
   ```
   resources/views/modules/{module-name-lowercase}/feature-name/view-name.blade.php
   ```

2. Extend the appropriate layout:
   ```php
   @extends('layouts.app')
   
   @section('content')
       <!-- View content here -->
   @endsection
   ```

### Loading Module Views

The module's service provider should have the correct view loading configuration:

```php
protected function registerViews()
{
    $this->loadViewsFrom(resource_path('views/modules/module-name-lowercase'), 'module-name-lowercase');
}
```

## Testing

### Running Tests

```bash
php artisan test
```

### Writing Tests

- Place tests in the appropriate module's `Tests` directory
- Follow the naming convention: `FeatureTest.php` for feature tests and `UnitTest.php` for unit tests
- Use Laravel's testing facilities (assertions, database transactions, etc.)

## Deployment

### Preparing for Deployment

1. Compile assets for production:
   ```bash
   npm run production
   ```

2. Optimize autoloader:
   ```bash
   composer install --optimize-autoloader --no-dev
   ```

### Deployment Steps

1. Pull the latest code on the server
2. Update PHP dependencies
3. Apply any new database schema changes
4. Clear caches:
   ```bash
   php artisan optimize:clear
   ```
5. Reload the web server if necessary

## Best Practices

- Follow PSR-12 coding standards
- Use strict typing (`declare(strict_types=1);`) in all PHP files
- Keep controllers slim, move business logic to services
- Use dependency injection rather than instantiating services directly
- Define proper relationships between models
- Use eager loading to prevent N+1 query issues
- Write descriptive commit messages
- Document complex code with comments

## Getting Help

- Refer to the project documentation in the `ai_docs` directory
- Check the Laravel documentation for framework-specific questions
- Consult with team members for project-specific questions

## Additional Resources

- [Laravel Documentation](https://laravel.com/docs)
- [Laravel Modules Documentation](https://nwidart.com/laravel-modules/v6/introduction)
- [PSR-12 Coding Standard](https://www.php-fig.org/psr/psr-12/)
- [Eloquent ORM Documentation](https://laravel.com/docs/eloquent)
- [Project Architecture Diagrams](architecture_diagrams.md)
- [Modules Overview](modules_overview.md)
- [Coding Standards](coding_standards.md) 
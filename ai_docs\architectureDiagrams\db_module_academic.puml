@startuml Academic Module Schema

!theme vibrant

entity "students" {
  * id: int
  --
  user_id: int <<FK>>
  guardian_id: int <<FK>>
  full_name: varchar(255)
  student_number: varchar(255)
  email: varchar(255)
  organization_id: int
}

entity "guardians" {
  * id: int
  --
  user_id: int <<FK>>
  full_name: varchar(255)
  email: varchar(255)
  organization_id: int
}

entity "academic_years" {
  * id: int
  --
  name: varchar(255)
  start_date: date
  end_date: date
  organization_id: int
}

entity "classes" {
  * id: int
  --
  class_code: varchar(255)
  center_id: int <<FK>>
  subject_id: int <<FK>>
  organization_id: int
}

entity "subjects" {
  * id: int
  --
  name: varchar(255)
  code: varchar(255)
  organization_id: int
}

entity "class_students" {
  * id: int
  --
  class_id: int <<FK>>
  student_id: int <<FK>>
  academic_year_id: int <<FK>>
  organization_id: int
}

entity "centers" {
  * id: int
  --
  name: varchar(255)
  code: varchar(255)
  organization_id: int
}

entity "programs" {
  * id: int
  --
  name: varchar(255)
  code: varchar(255)
  organization_id: int
}

entity "center_programs" {
  * id: int
  --
  center_id: int <<FK>>
  program_id: int <<FK>>
}

entity "class_programs" {
  * id: int
  --
  class_id: int <<FK>>
  program_id: int <<FK>>
}

entity "program_levels" {
  * id: int
  --
  program_id: int <<FK>>
  name: varchar(255)
}

entity "student_program_levels" {
  * id: int
  --
  student_id: int <<FK>>
  program_level_id: int <<FK>>
}

students }o--|| guardians : "has guardian"
classes }o--|| centers : "belongs to"
classes }o--|| subjects : "has subject"
students }o--o{ class_students : "enrolled in"
classes }o--o{ class_students : "has students"
academic_years ||--o{ class_students : "has"

centers }o--o{ center_programs : "offers"
programs }o--o{ center_programs : "offered at"
classes }o--o{ class_programs : "has"
programs }o--o{ class_programs : "included in"
programs ||--o{ program_levels : "has"
students }o--o{ student_program_levels : "enrolled in"
program_levels ||--o{ student_program_levels : "has students"

@enduml

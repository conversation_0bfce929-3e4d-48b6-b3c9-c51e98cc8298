<?php

namespace App\Http\Controllers\Auth;

use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\VerifiesEmails;
use Illuminate\Support\Facades\Log;

class VerificationController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Email Verification Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling email verification for any
    | user that recently registered with the application. Emails may also
    | be resent if the user did not receive the original email message.
    |
    */

    use VerifiesEmails;

    /**
     * Where to redirect users after verification.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {

//        $this->middleware('auth');
        $this->middleware('signed')->only('verify');
        $this->middleware('throttle:6,1')->only('verify', 'resend');

    }


    public function showResendForm()
    {

        return view('auth.resendVerificationForm');


    }


    public function redirectPath()
    {

        if (\Auth::guard("web")->user()->hasRole("parent")) {
            $this->redirectTo = '/parent-dashboard';

        } elseif (\Auth::guard("web")->user()->hasRole("student")) {
            $this->redirectTo = '/student-dashboard';

        } else {


            // redirect to the studentapplication route
            $this->redirectTo = route("student.application.form");

        }


        return property_exists($this, 'redirectTo') ? $this->redirectTo : '/';
    }


    /**
     * Resend the email verification notification.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function resend(Request $request)
    {
        //  $customMessage = ['g-recaptcha-response' => [
        //     'required' => 'Username is required',
        //     'exists' => 'Username does not exist',
        // ]];

        // Define the custom message array properly
        $customMessage = [
            'username.required' => 'Username is required',
            'username.exists' => 'Username does not exist in our records'
        ];

        Log::info('Verification resend attempt', [
            'username' => $request->get('username'),
            'ip' => $request->ip()
        ]);

        $this->validate($request, [
            'username' => 'required|string|exists:users,username',
        ], $customMessage);


        try {

            $user = \App\User::where('username',$request->get('username'))->first();


            auth()->login($user);
            if ($request->user()->hasVerifiedEmail()) {
                return redirect($this->redirectPath());
            }


            $request->user()->sendEmailVerificationNotification();
            auth()->logout();

            return back()->with('resent', true);
        } catch (\Exception $e) {

            Log::info($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function verify(Request $request)
    {



        $user = \App\User::findOrFail($request->route('id'));

        auth()->login($user);

        if ($request->user()->hasVerifiedEmail()) {
            return redirect($this->redirectPath());
        }

        if ($request->user()->markEmailAsVerified()) {
            event(new Verified($request->user()));
        }

        return redirect($this->redirectPath())->with('verified', true);
    }

    public function show()
    {
        return view('auth.verify-email');
    }

}

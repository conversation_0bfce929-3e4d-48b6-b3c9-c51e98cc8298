# Complete Database Schema Documentation

This document provides a comprehensive list of all tables in the Itqan database, organized by functional modules.

## Core System Tables
- `users` - System users (administrators, staff, etc.)
- `roles` - User roles for permission management
- `permissions` - Individual permissions
- `model_has_roles` - Polymorphic relationship between users and roles
- `model_has_permissions` - Polymorphic relationship between users and permissions
- `role_has_permissions` - Many-to-many relationship between roles and permissions
- `settings` - Application configuration settings
- `general_settings` - General application settings
- `background_settings` - Background UI settings
- `languages` - Supported languages
- `language_phrases` - Translations for UI elements
- `migrations` - Laravel database migrations record
- `failed_jobs` - Failed background jobs
- `queue_jobs` - Queued background jobs
- `activity_log` - System activity logs
- `log_activity` - User activity logs
- `module_managers` - Module management
- `modules` - System modules
- `module_infos` - Module information
- `module_links` - Module navigation links
- `module_permissions` - Module-specific permissions
- `module_permission_assigns` - Assignment of module permissions to users/roles
- `organizations` - Multi-tenant organization data

## User Management
- `password_resets` - Password reset tokens
- `employee_password_resets` - Employee-specific password reset tokens
- `guardian_password_resets` - Guardian-specific password reset tokens
- `organization_password_resets` - Organization-specific password reset tokens
- `sponsor_password_resets` - Sponsor-specific password reset tokens
- `student_password_resets` - Student-specific password reset tokens
- `superior_password_resets` - Superior-specific password reset tokens
- `company_password_resets` - Company-specific password reset tokens

## Students and Academic Management
- `students` - Student personal information
- `guardians` - Student guardians/parents
- `student_categories` - Student categorization
- `student_groups` - Student grouping
- `student_levels` - Student academic levels
- `student_bulk_temporaries` - Temporary bulk student operations
- `student_certificates` - Student certificates
- `student_id_cards` - Student ID cards
- `student_timelines` - Student timeline events
- `student_promotions` - Student promotion records
- `student_registrations` - Student registration records
- `academic_years` - Academic year periods
- `classes` - Academic classes
- `class_translations` - Class name translations
- `subjects` - Academic subjects
- `subject_translations` - Subject name translations
- `subject_content` - Subject content material
- `class_students` - Many-to-many relationship between classes and students
- `class_students_backup` - Backup of class-student relationships
- `class_students_backup_latest` - Latest backup of class-student relationships
- `class_students_filtered_records` - Filtered records of class-student relationships

## Teaching and Faculty
- `employees` - Faculty and staff information
- `employee_department` - Departments for employees
- `class_teachers` - Assignment of teachers to classes
- `class_teacher_subjects` - Subject assignments for teachers
- `employee_timetables` - Teacher timetables
- `employee_work_mood` - Teacher working mode settings
- `cen_teachers` - Center-specific teachers
- `cen_emps` - Center-specific employees

## Attendance and Scheduling
- `attendances` - Attendance records
- `attendance_options` - Attendance option configurations
- `student_attendances` - Student attendance records
- `student_attendance_bulks` - Bulk attendance operations
- `class_routines` - Class routine schedules
- `class_routine_updates` - Updates to class routines
- `class_times` - Class timing configurations
- `class_timetable` - Class timetable schedules
- `class_subject_timetable` - Subject-specific timetables
- `public_holidays` - Public holiday records
- `public_holidays_backup` - Backup of public holiday records
- `holidays` - School holiday records
- `missed_clockouts` - Records of missed clock-outs

## Centers and Programs
- `centers` - Educational centers
- `center_translations` - Center name translations
- `center_programs` - Programs offered by centers
- `programs` - Educational programs
- `program_translations` - Program name translations
- `program_settings` - Program settings
- `program_levels` - Program academic levels
- `program_level_translations` - Program level translations
- `program_level_subjects` - Subjects in program levels
- `program_level_lessons` - Lessons in program levels
- `program_level_lesson_form_inputs` - Form inputs for program level lessons
- `student_program_levels` - Student enrollment in program levels
- `class_programs` - Programs offered in classes

## Quran Studies Tables
- `hefz_levels` - Quran memorization levels
- `student_hefz_plans` - Student Quran memorization plans
- `student_hefz_report` - Student Quran memorization reports
- `student_last_approved_plans` - Last approved memorization plans
- `student_last_memorization_record` - Last memorization records
- `student_revision_plans` - Quran revision plans
- `student_revision_report` - Quran revision reports
- `student_last_approved_revision_plans` - Last approved revision plans
- `student_last_revision_record` - Last revision records
- `moshaf` - Quran copies
- `moshaf_juz` - Quran juz (parts)
- `moshaf_pages` - Quran pages
- `moshaf_surah` - Quran surahs (chapters)
- `moshaf_surah_pages` - Surah page mapping
- `moshaf_partial_page_weight` - Partial page weightings
- `surah` - Surah information

## Ijazasanad Certification System
- `ijazasanad_email_settings` - Email settings for Ijazasanad certificates
- `ijazasanad_email_log` - Email logs for Ijazasanad certificates
- `ijazasanad_email_exclusions` - Email exclusions for Ijazasanad certificates
- `ijazasanad_memorization_plans` - Memorization plans for Ijazasanad certificates
- `ijazasanad_revision_plans` - Revision plans for Ijazasanad certificates
- `student_ijazasanad_memorization_last_approved_plans` - Last approved memorization plans
- `student_ijazasanad_memorization_report` - Memorization reports
- `student_ijazasanad_revision_last_approved_plans` - Last approved revision plans
- `student_ijazasanad_revision_report` - Revision reports
- `student_last_ijazasanad_record` - Last Ijazasanad records
- `student_last_ijazasanad_memorization_record` - Last memorization records
- `student_last_ijazasanad_revision_record` - Last revision records

## Nouranya System
- `nouranya_email_settings` - Email settings for Nouranya
- `nouranya_email_log` - Email logs for Nouranya
- `nouranya_email_exclusions` - Email exclusions for Nouranya
- `student_nouranya_plans` - Student Nouranya plans
- `student_nouranya_report` - Student Nouranya reports
- `student_last_approved_nouranya_plans` - Last approved Nouranya plans
- `student_last_nouranya_record` - Last Nouranya records

## Memorization/Revision Email System
- `memorizationrevision_email_settings` - Email settings for memorization/revision
- `memorizationrevision_email_log` - Email logs for memorization/revision
- `memorizationrevision_email_exclusions` - Email exclusions for memorization/revision

## Admissions
- `admissions` - Admission applications
- `admission_statuses` - Admission application statuses
- `admission_program` - Programs in admissions
- `admission_hefz_plans` - Hefz plans for admissions
- `admission_interviews` - Admission interviews
- `admission_interviewers` - Admission interview staff
- `admission_interview_reports` - Admission interview reports
- `admission_orientation` - Admission orientation information

## Finance and Payments
- `fees_masters` - Fee master records
- `fees_types` - Fee types
- `fees_assigns` - Fee assignments to students
- `fees_assign_discounts` - Fee discount assignments
- `fees_discounts` - Fee discount definitions
- `fees_payments` - Fee payment records
- `student_payments` - Student-specific payment records
- `payments` - General payment records
- `payment_transactions` - Payment transaction records
- `payment_methhods` - Payment method definitions
- `payment_gateway_settings` - Payment gateway configurations
- `pay_transactions` - Payment transactions
- `chart_accounts` - Chart of accounts

## Banking
- `banks` - Bank definitions
- `bank_accounts` - Bank account records
- `bank_account_types` - Bank account type definitions

## Human Resources
- `payrolls` - Payroll records
- `payroll_earn_deducs` - Payroll earnings and deductions
- `employee_salaries` - Employee salary records
- `employee_salary_reports` - Employee salary reports
- `employee_salary_report_adjustments` - Adjustments to employee salary reports
- `salary_periods` - Salary period definitions
- `bonus` - Employee bonus records
- `career_levels` - Career level definitions
- `job_titles` - Job title definitions
- `apply_loans` - Employee loan applications
- `offer_letters` - Job offer letters

## Leave Management
- `leave_types` - Leave type definitions
- `leave_defines` - Leave definition records
- `leave_requests` - Leave request records
- `leave_deduction_infos` - Leave deduction information
- `apply_leaves` - Leave applications

## Communications
- `emails` - Email records
- `email_settings` - Email settings
- `email_logs` - Email log records
- `email_sms_logs` - Email and SMS log records
- `sms_templates` - SMS template definitions
- `mail_log` - Mail log records
- `contact_messages` - Contact form messages
- `notifications` - System notifications
- `notice_boards` - Notice board announcements
- `events` - Event records
- `event_members` - Event participant records
- `news` - News articles
- `news_translations` - News article translations

## Exams and Assessment
- `exams` - Exam records
- `exam_types` - Exam type definitions
- `exam_setups` - Exam setup configurations
- `exam_schedules` - Exam schedules
- `exam_schedule_subjects` - Subject-specific exam schedules
- `exam_marks_registers` - Exam marks records
- `exam_attendance_children` - Exam attendance records
- `online_exams` - Online exam configurations
- `online_exam_questions` - Online exam questions
- `online_exam_marks` - Online exam marks
- `student_take_online_exams` - Student online exam participation
- `student_take_online_exam_questions` - Student responses to online exam questions
- `student_take_onln_ex_ques_options` - Student option selections for online exam questions
- `question_banks` - Question bank records
- `marks_grades` - Mark grade definitions
- `marks_send_sms` - Mark notification SMS settings
- `result_types` - Result type definitions

## Homework
- `homeworks` - Homework assignments
- `homework_students` - Student homework records
- `student_homeworks` - Student homework submissions

## Lessons and Reports
- `lesson_reports` - Lesson report records
- `class_reports` - Class report records
- `lesson_report_evaluations` - Lesson report evaluations
- `evaluation_schemas` - Evaluation schema definitions
- `evaluation_schema_options` - Evaluation schema options
- `contents_evaluation_schemas` - Content evaluation schemas

## Library Management
- `books` - Book records
- `book_categories` - Book category definitions
- `book_issues` - Book borrowing records
- `library_members` - Library membership records

## Content Management
- `contents` - Content records
- `content_categories` - Content category definitions
- `generated_certificates` - Generated certificate records
- `sliders` - Slider images
- `slider_translations` - Slider text translations
- `styles` - Style definitions
- `menus` - Menu definitions
- `menu_translations` - Menu translations
- `menu_usage_statistics` - Menu usage statistics
- `pdf_fonts` - PDF font definitions

## Job Portal
- `jobs` - Job listings
- `job_types` - Job type definitions
- `job_shifts` - Job shift definitions
- `job_skills` - Job skill requirements
- `job_titles` - Job title definitions
- `job_experiences` - Job experience requirements
- `job_alerts` - Job alert configurations
- `job_apply` - Job applications
- `companies` - Company records
- `company_messages` - Company messages
- `favourites_job` - Favorited jobs
- `favourites_company` - Favorited companies
- `favourite_applicants` - Favorited applicants
- `functional_areas` - Functional area definitions
- `ownership_types` - Ownership type definitions
- `degree_levels` - Degree level definitions
- `degree_types` - Degree type definitions
- `manage_job_skills` - Managed job skills
- `marital_statuses` - Marital status definitions
- `applicant_messages` - Applicant messages
- `report_abuse_company_messages` - Reports of company abuse
- `report_abuse_messages` - Reports of message abuse
- `send_to_friend_messages` - "Send to friend" messages

## Profiles
- `profile_cvs` - Profile CVs
- `profile_educations` - Profile education records
- `profile_education_major_subjects` - Profile education major subjects
- `profile_experiences` - Profile experience records
- `profile_languages` - Profile language skills
- `profile_projects` - Profile projects
- `profile_skills` - Profile skills
- `profile_summaries` - Profile summaries

## Forms and Documentation
- `forms` - Form definitions
- `form_builders` - Form builder configurations
- `form_builder_translations` - Form builder translations
- `form_builder_approval_flow` - Form approval flow
- `form_clarifications` - Form clarification records
- `form_reviews` - Form review records
- `documents` - Document records

## Activities
- `activities` - Activity records
- `execution_times` - Execution time records

## Miscellaneous
- `visitors` - Visitor records
- `show_rooms` - Showroom definitions
- `registration_settings` - Registration settings
- `date_formats` - Date format definitions
- `business_settings` - Business settings
- `base_groups` - Base group definitions
- `base_setups` - Base setup configurations
- `assign_subjects` - Subject assignments
- `countries` - Country records
- `malaysia_postcodes` - Malaysia postal codes
- `sponsors` - Sponsor records
- `superiors` - Superior records
- `packages` - Package definitions
- `package_translations` - Package translations
- `pulse_aggregates` - Pulse aggregate data
- `pulse_values` - Pulse values
- `mytable` - Utility table

## How to Use This Document

This comprehensive list of tables provides an overview of the database structure. To understand the detailed schema of each table, use the SQL command `DESCRIBE tablename;` with MySQL.

The tables are organized by functional modules, but many tables have relationships across modules. For entity-relationship diagrams of key tables, refer to the `database_schema.puml` file. 
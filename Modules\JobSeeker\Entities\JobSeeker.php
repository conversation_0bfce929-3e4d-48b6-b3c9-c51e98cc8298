<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Permission\Traits\HasRoles;
use HashmatWaziri\LaravelMultiAuthImpersonate\Models\Impersonate;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Log;
use App\Services\EmailService;

class JobSeeker extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable, HasRoles, Impersonate;

    protected $table = 'job_seekers';
    protected $guard = 'job_seeker';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'username',
        'password',
        'is_active',
        'last_notified_at',
        'email_verification_token',
        'google_id',
        'avatar',
        'password_reset_required',
        'password_reset_reason',
        'password_reset_required_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'email_verification_token',
        'google_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'is_active' => 'boolean',
        'last_notified_at' => 'datetime',
        'password' => 'hashed',
        'password_reset_required' => 'boolean',
        'password_reset_required_at' => 'datetime',
    ];

    /**
     * Get the notification setups for this job seeker.
     */
    public function notificationSetups(): HasMany
    {
        return $this->hasMany(JobNotificationSetup::class, 'job_seeker_id');
    }

    /**
     * Get all notification recipients through notification setups.
     */
    public function notificationRecipients(): HasManyThrough
    {
        return $this->hasManyThrough(
            JobNotificationRecipient::class,
            JobNotificationSetup::class,
            'job_seeker_id', // Foreign key on job_notification_setups table
            'setup_id', // Foreign key on job_notification_recipients table
            'id', // Local key on job_seekers table
            'id' // Local key on job_notification_setups table
        );
    }

    /**
     * Get the personal contacts for this job seeker.
     */
    public function personalContacts(): HasMany
    {
        return $this->hasMany(JobSeekerPersonalContact::class, 'job_seeker_id');
    }

    /**
     * Get the device tokens for this job seeker.
     */
    public function deviceTokens(): HasMany
    {
        return $this->hasMany(UserDeviceToken::class, 'job_seeker_id');
    }

    /**
     * Get the password history for this job seeker.
     */
    public function passwordHistory(): HasMany
    {
        return $this->hasMany(JobSeekerPasswordHistory::class, 'job_seeker_id');
    }

    /**
     * Get the account lockouts for this job seeker.
     */
    public function accountLockouts(): HasMany
    {
        return $this->hasMany(JobSeekerAccountLockout::class, 'job_seeker_id');
    }

    /**
     * Get the profile settings for this job seeker.
     */
    public function profileSettings(): HasOne
    {
        return $this->hasOne(JobSeekerProfileSettings::class, 'jobseeker_id');
    }

    /**
     * Get the security settings for this job seeker.
     */
    public function securitySettings(): HasOne
    {
        return $this->hasOne(JobSeekerSecuritySettings::class, 'jobseeker_id');
    }

    /**
     * Get the account preferences for this job seeker.
     */
    public function accountPreferences(): HasOne
    {
        return $this->hasOne(JobSeekerAccountPreferences::class, 'jobseeker_id');
    }

    /**
     * Get the FCM tokens for the job seeker.
     *
     * @return array<int, string>|string|null
     */
    public function routeNotificationForFcm(): array|string|null
    {
        // This will return an array of all active device tokens for the user
        return $this->deviceTokens()->pluck('device_token')->toArray();
    }

    /**
     * Get active device tokens for FCM notifications.
     * Includes business logic for setup-specific preferences and token freshness.
     *
     * @return array
     */
    public function getDeviceTokens(): array
    {
        // Only return tokens if the user has at least one active setup with push notifications enabled
        $hasActivePushSetup = $this->notificationSetups()
            ->where('is_active', true)
            ->where('receive_push_notifications', true)
            ->exists();

        if (!$hasActivePushSetup) {
            return []; // No tokens if user hasn't opted into push notifications
        }

        if (!$this->deviceTokens()->exists()) {
            return []; // No tokens if user has no registered devices
        }

        // Return only fresh tokens (used within last 30 days)
        return $this->deviceTokens()
            ->where('last_used_at', '>=', now()->subDays(30))
            ->pluck('device_token')
            ->filter() // Remove any null/empty tokens
            ->values() // Reset array keys
            ->toArray();
    }

    public function canImpersonate()
    {
        // For example
//        return $this->role_id == 24 || $this->role_id == 1 ;
        return true;

    }

    /**
     * Create verification URL for JobSeeker email verification.
     *
     * @param int $id
     * @param string $email
     * @return string
     */
    protected function verificationUrl($id, $email): string
    {
        return URL::temporarySignedRoute(
            'jobseeker.verification.verify',
            Carbon::now()->addMinutes(Config::get('auth.verification.expire', 2880)),
            [
                'id' => $id,
                'hash' => sha1($email)
            ]
        );
    }

    /**
     * Send email verification notification for JobSeeker.
     *
     * @throws \Exception
     */
    public function sendEmailVerificationNotification(): void
    {
        $verificationUrl = $this->verificationUrl($this->id, $this->email);

        $emailService = app(EmailService::class);
        $result = $emailService->sendEmail(
            ['email' => $this->email, 'name' => $this->name],
            "Welcome aboard! Please verify your email",
            'modules.jobseeker.emails.verification',
            ['verificationUrl' => $verificationUrl, 'jobSeeker' => $this]
        );

        // If email sending fails, throw an exception to trigger transaction rollback
        if (!($result['success'] ?? false)) {
            $errorMessage = $result['message'] ?? 'Unknown email error';
            Log::error('Failed to send JobSeeker verification email', [
                'job_seeker_id' => $this->id,
                'email' => $this->email,
                'error' => $errorMessage,
                'correlation_id' => $result['correlation_id'] ?? null
            ]);
            throw new \Exception("Failed to send verification email: {$errorMessage}");
        }
    }

    /**
     * Determine if the job seeker has verified their email address.
     *
     * @return bool
     */
    public function hasVerifiedEmail(): bool
    {
        return !is_null($this->email_verified_at);
    }

    /**
     * Mark the given job seeker's email as verified.
     *
     * @return bool
     */
    public function markEmailAsVerified(): bool
    {
        return $this->forceFill([
            'email_verified_at' => $this->freshTimestamp(),
        ])->save();
    }

    /**
     * Get the email address that should be used for verification.
     *
     * @return string
     */
    public function getEmailForVerification(): string
    {
        return $this->email;
    }
} 
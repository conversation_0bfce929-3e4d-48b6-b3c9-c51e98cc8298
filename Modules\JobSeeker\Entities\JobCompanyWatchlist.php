<?php

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class JobCompanyWatchlist extends Model
{
    protected $table = 'job_company_watchlist';
    protected $guarded = ['id'];
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'company_name',
        'slug',
        'description',
        'is_active'
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = ['counts'];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Generate slug from company name when creating
        static::creating(function ($model) {
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->company_name);
                
                Log::info("Generated slug for company watchlist", [
                    'company_name' => $model->company_name,
                    'slug' => $model->slug
                ]);
            }
        });
    }

    /**
     * Get the counts for this watchlist item.
     */
    public function counts(): HasMany
    {
        return $this->hasMany(JobCompanyWatchlistCount::class, 'watchlist_id');
    }

    /**
     * Get the latest count record for this watchlist item.
     */
    public function latestCount()
    {
        return $this->counts()->latest('date_checked')->first();
    }

    /**
     * Find all jobs for this watched company.
     */
    public function findJobs($days = 60)
    {
        Log::info("Finding jobs for company in watchlist", [
            'company_name' => $this->company_name,
            'days' => $days
        ]);
        
        return Job::where('company_name', 'like', "%{$this->company_name}%")
            ->where('publish_date', '>=', now()->subDays($days))
            ->orderBy('publish_date', 'desc')
            ->get();
    }
} 
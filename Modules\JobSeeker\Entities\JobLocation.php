<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;

/**
 * JobLocation Entity
 * 
 * Manages canonical job locations for job seeker interface.
 * Contains country, province information for Afghanistan locations.
 * 
 * @property int $id
 * @property string $name
 * @property string $country
 * @property string $province
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
final class JobLocation extends Model
{
    protected $table = 'job_locations';
    
    protected $fillable = [
        'name',
        'country',
        'province',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get provider job locations that map to this canonical location
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function providerJobLocations(): HasMany
    {
        return $this->hasMany(ProviderJobLocation::class, 'canonical_location_id');
    }

    /**
     * Scope to get locations for a specific country
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $country
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCountry($query, string $country)
    {
        return $query->where('country', $country);
    }

    /**
     * Scope to get only active locations
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get formatted data for Select2 dropdown (used by job seekers)
     *
     * @param string $country
     * @return array
     */
    public static function getForSelect2(string $country = 'Afghanistan'): array
    {
        try {
            return self::forCountry($country)
                ->active()
                ->orderBy('province')
                ->get()
                ->map(function ($location) {
                    return [
                        'id' => $location->id,
                        'text' => $location->name,
                        'province' => $location->province
                    ];
                })
                ->toArray();
        } catch (\Exception $e) {
            Log::error('JobLocation: Error getting Select2 options', [
                'country' => $country,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get all active countries
     *
     * @return array
     */
    public static function getActiveCountries(): array
    {
        try {
            return self::active()
                ->distinct()
                ->pluck('country')
                ->toArray();
        } catch (\Exception $e) {
            Log::error('JobLocation: Error getting active countries', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get provinces for a specific country
     *
     * @param string $country
     * @return array
     */
    public static function getProvincesForCountry(string $country = 'Afghanistan'): array
    {
        try {
            return self::forCountry($country)
                ->active()
                ->orderBy('province')
                ->pluck('province', 'id')
                ->toArray();
        } catch (\Exception $e) {
            Log::error('JobLocation: Error getting provinces for country', [
                'country' => $country,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get location statistics for debugging
     *
     * @return array
     */
    public static function getLocationStats(): array
    {
        try {
            return [
                'total_locations' => self::count(),
                'active_locations' => self::active()->count(),
                'countries' => self::distinct()->count('country'),
                'locations_by_country' => self::active()
                    ->groupBy('country')
                    ->selectRaw('country, COUNT(*) as count')
                    ->pluck('count', 'country')
                    ->toArray()
            ];
        } catch (\Exception $e) {
            Log::error('JobLocation: Error getting location statistics', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
} 
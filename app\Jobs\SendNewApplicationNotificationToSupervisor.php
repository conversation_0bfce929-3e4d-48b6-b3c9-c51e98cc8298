<?php

namespace App\Jobs;

use App\Services\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendNewApplicationNotificationToSupervisor implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user_info = [];
    protected $sender;
    protected $supervisor_info = [];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_info, $sender, $supervisor_info)
    {


        $this->user_info = $user_info;
        $this->sender = $sender;
        $this->supervisor_info = $supervisor_info;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
//    public function handle(\Illuminate\Mail\Mailer $mailer)
    public function handle()
    {

        $formattedEmails = [];
        foreach ($this->supervisor_info->toArray() as $name => $email) {


            $formattedEmails[] = [
                'Email' => $email,
                'Name' => $name // You can add the recipient's name here
            ];
        }

        $emailService = app(\App\Services\EmailService::class);



        // Pass the necessary data to the view)
        foreach ($formattedEmails as $emailDetails) {

            $emailService->sendEmail(
                ['email' => $emailDetails['Email'], 'name' => $emailDetails['Name']],
                "NEW APPLICATION",
                'modules.site.templates.wajeha.backEnd.studentInformation.new_student_application_supervisor', // Update this with the correct view path
                ['data' => $this->user_info] // Pass the necessary data to the view
            );
        }



    }




}

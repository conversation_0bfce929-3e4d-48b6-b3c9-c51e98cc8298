@extends('layouts.hound') 
@section('mytitle')
<div class="col-sm-9">
Dashboard 
</div>
<div class="col-sm-3">
@if(count($dashboards) > 1)
<select name="dahsboard" id="dashboard_switch" class="form-control">
    <option >Dashboards</option>
   @foreach($dashboards as $dashboard)
    <option value="{{ $dashboard }}">{{ ucwords(str_replace('_' , ' ' , $dashboard)) }}</option>
   @endforeach
  </select>
   @endif
</div>
@endsection
 
@section('content')

@include('partial.quick_access')

<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default card-view">
            <div class="panel-heading">
                <div class="pull-left">
                    <h4 class=" txt-dark">My Classes</h4>
                </div>
                <div class="clearfix"></div>
            </div>


            <div class="panel-wrapper collapse in">
                <div class="panel-body">
                    <!-- Row -->
                    <div class="clearfix">


                        @if(!auth()->user()->classes->isEmpty())
                            @foreach(auth()->user()->classes as $element)


                        <div class=" col-lg-3 col-md-3 col-sm-4">
                            <div class="panel panel-success card-view">
                                <div class="panel-heading small-panel-heading relative">
                                    <div class="pull-left">
                                        <h4 class="panel-title txt-light">
                                            {{ $element->name }}
                                        </h4>
                                    </div>
                                    <span class="pull-right txt-light">
                                {{ count($element->students)}} Students
                            </span>
                                    <div class="clearfix"></div>
                                    <div class="head-overlay"></div>
                                </div>
                                <div class="panel-wrapper collapse in">
                                    <div class="panel-body row pa-0">
                                        <div class="sm-data-box data-with-border bg-green">
                                            <div class="container-fluid">
                                                <div class="row">
                                                    <div class="col-xs-12 text-center  pl-0 pr-0 data-wrap-right">
                                                        <a href="{{ url('workplace/education/classes/'.$element->class_id.'/reports')}}" class="btn btn-danger btn-lg">Go To Class</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @endforeach
                        @endif
                    </div>
                    <!-- /Row -->
                </div>
            </div>
        </div>
    </div>
</div>
    
<div class="row">
    <div class="col-md-6">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default card-view">
                    <div class="panel-heading">
                        <div class="pull-left">
                            <h4 class=" txt-dark">My Timetable</h4>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="panel-wrapper collapse in">
                        <div class="panel-body">
                            <!-- Row -->
                            <div class="clearfix">
                                <table class="table table-striped color-bg-table">
                                    <thead>
                                        <tr>
                                            <th>Day</th>
                                            <th>Time</th>
                                            <th>Class</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($timetable as $day => $table)
                                        <tr>
                                            <td>{{$day}}</td>
                                            @foreach($table as $time => $info)
                                            <td>{{ $time }}</td>
                                            <td>{{ $info['class_name'] }}</td>
                                            @endforeach
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>

                            </div>
                            <!-- /Row -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="col-md-6">
            <div class="row">
                <div class="col-lg-12">
                    <div class="panel panel-default card-view">
                        <div class="panel-heading">
                            <div class="pull-left">
                                <h4 class=" txt-dark">My Calender</h4>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="panel-wrapper collapse in">
                            <div class="panel-body">
                                <!-- Row -->
                                <div class="clearfix">

                                    
                                </div>
                                <!-- /Row -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    
        </div>
    </div>

@stop 
@section('js')
<script>
    jQuery(document).ready(function($) {
    $('body').addClass('sidebar-collapse');
    
    $('#calendar').datepicker();
  });

</script>

@stop
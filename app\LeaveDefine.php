<?php

namespace App;

use App\Scopes\OrganizationScope;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class LeaveDefine extends Model
{
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
    }
    public function role(){
        return $this->belongsTo(Role::class, 'role_id', 'id');
    }

    public function leaveType(){
        return $this->belongsTo('App\LeaveType', 'leave_type_id', 'id');
    }

    public function user(){
        return $this->belongsTo('App\User', 'user_id', 'id');
    }

    public function employee(){
        return $this->belongsTo('App\Employee', 'user_id', 'id');
    }
    public function leaveRequests(){
        return $this->hasMany(LeaveRequest::class, 'leave_define_id')->where('approve_status','=','A');
    }

    public function getremainingDaysAttribute()
    {
        $diff_in_days =0;
        foreach($this->leaveRequests as $leave){
            $to = Carbon::parse( $leave->leave_from);
            $from = Carbon::parse( $leave->leave_to);
            $diff_in_days = $to->diffInDays($from)+1;
        }
        return $diff_in_days;
    }
}

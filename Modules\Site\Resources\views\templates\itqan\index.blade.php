@extends('site::templates.'.$theme.'.layouts.home')
@section('page_title')
    {{trans('home_header.home')}}
@endsection

@section('content')
    <!-- REVOLUTION SLIDER -->
    <div class="slider fullwidthbanner-container roundedcorners">
        <div class="fullwidthbanner" data-height="600" data-shadow="0" data-navigationStyle="preview4">
            <ul class="hide">
                @foreach($slider as $item)
                    <li class="text-center" data-transition="parallaxtobottom" data-slotamount="7"
                        data-masterspeed="600"
                        data-saveperformance="off" data-title="<?php echo $item->{'title_' . App::getLocale()} ?>">
                        <!-- MAIN IMAGE -->
                        <img src="{{URL::to($item->background)}}" alt="cover image" data-bgposition="center top"
                             data-bgfit="cover" data-bgrepeat="no-repeat">


                        <div class="helvetica tp-caption customin ltl tp-resizeme large_bold_white start"
                             data-x="center" data-y="230"
                             data-customin="x:0;y:150;z:0;rotationZ:0;scaleX:1;scaleY:1;skewX:0;skewY:0;opacity:0;transformPerspective:200;transformOrigin:50% 0%;"
                             data-speed="800" data-start="1200" data-easing="easeOutQuad" data-splitin="none"
                             data-splitout="none" data-elementdelay="0.01" data-endelementdelay="0.1"
                             data-endspeed="1000" data-endeasing="Power4.easeIn"
                             style="z-index: 3; transition: all 0s ease 0s; min-height: 0px; min-width: 0px; line-height: 54px; border-width: 0px; margin: 0px; padding: 0px; letter-spacing: 0px; font-size: 22px; left: 267px; top: 335.3px; visibility: visible; opacity: 1; transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, -0.0025, 0, 0, 0, 1); transform-origin: 50% 0% 0px;">
                            <p class='helvetica'><?php echo $item->{'title_' . App::getLocale()} ?></p>
                        </div>


                        <div class="helvetica tp-caption customin ltl tp-resizeme small_light_white start"
                             data-x="center" data-y="350"
                             data-customin="x:0;y:150;z:0;rotationZ:0;scaleX:1;scaleY:1;skewX:0;skewY:0;opacity:0;transformPerspective:200;transformOrigin:50% 0%;"
                             data-speed="800" data-start="1400" data-easing="easeOutQuad" data-splitin="none"
                             data-splitout="none" data-elementdelay="0.01" data-endelementdelay="0.1"
                             data-endspeed="1000" data-endeasing="Power4.easeIn"
                             style="z-index: 3; width: 750px; max-width: 676.25px; white-space: normal; text-align: center; transition: all 0s ease 0s; min-height: 0px; min-width: 0px; line-height: 25px; border-width: 0px; margin: 0px; padding: 0px; letter-spacing: 0px; font-size: 15px; left: 203px; top: 443.5px; visibility: visible; opacity: 1; transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, -0.0025, 0, 0, 0, 1); transform-origin: 50% 0% 0px;">
                            <p class='helvetica'><?php echo htmlspecialchars_decode($item->{'description_' . App::getLocale()})  ?></p>
                        </div>


                    </li>
                @endforeach
            </ul>

        </div>
    </div>
    <!-- /REVOLUTION SLIDER -->

    <!-- Services -->
    <section>
        <div class="container">
            <div class="row">
                @foreach($features as $item)
                    <div class="col-sm-4">
                        <div class="box-icon box-icon-left">
                            <a class="box-icon-title" href="{{$item->link}}">
                                <i class="{{$item->icon}}"></i>
                                <h2><?php echo $item->{'title_' . App::getLocale()}?></h2>
                            </a>
                            <p>
                                <?php echo htmlspecialchars_decode($item->{'description_' . App::getLocale()})?>
                            </p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    <!-- /Services -->

    <!-- Parallax -->
    <section style="background: url('{{URL::to(cache('special_area_bg'))}}');background-size: 100% 100%;">

        <div class="container">
            <div class="text-center">
                <h2 class="" style="color: #6F8E00;">{{htmlspecialchars_decode(cache('special_area_title_'.App::getLocale()))}}</h2>
                <h4 class="" style="color: #056BBA;">{{htmlspecialchars_decode(cache('special_area_description_'.App::getLocale()))}}</h4>
                <?php
                    $link=cache('special_area_btn_link');
                    if (strpos($link,'http://') === false){
                        $link = 'http://'.$link;
                    }
                ?>
                <a class="btn btn-3d btn-blue btn-lg"
                   href="{{$link}}" target="_blank">{{cache('special_area_btn_title_'.App::getLocale())}}
                </a>
            </div>
        </div>
    </section>
    <!-- /Parallax -->




    <!-- POSTS -->
    <section>
        <div class="container">

            <!-- H2 -->
            <div class="heading-title">
                <h2 class="helvetica">{{trans('home_content.mostviewedblog')}}</h2>
                <hr>
            </div>

            <div class="row">
                @foreach($posts as $post)
                    <div class="col-sm-4">
                        <div class="flexslider">
                            <ul class="slides">
                                <?php /*counter */$count_displayed_pics = 0; ?>
                                @foreach($post->attachments as $att)
                                    <?php /*get file extention */$ext = pathinfo($att->post_attachment, PATHINFO_EXTENSION);?>
                                    @if($ext=="jpeg" || $ext=="jpg" || $ext=="png" || $ext=="gif")
                                        <?php $count_displayed_pics++; ?>
                                        <li>
                                            <a href="{{url('home/blog/post/'.$post->id.'/'.str_replace(' ','_',$post->{'post_title_'.App::getLocale()}))}}">
                                                <img style="height: 241px;max-height: 241px;min-height: 241px;"
                                                     src="{{URL::to($att->post_attachment)}}"
                                                     alt="<?php echo $post->{'post_title_' . App::getLocale()} ?>">
                                                <div class="flex-caption">{{getArabicDate($post->created_at)}}</div>
                                            </a>
                                        </li>
                                    @endif
                                @endforeach

                            <!-- if all attachment files are not image typed (show default image from 2 default images 1.jpg and 2.jpg  randomly) -->
                                @if($count_displayed_pics==0)
                                    <li>
                                        <a href="{{url('home/blog/post/'.$post->id.'/'.str_replace(' ','_',$post->{'post_title_'.App::getLocale()}))}}">
                                            <img style="height: 241px;max-height: 241px;min-height: 241px;"
                                                 src="{{URL::to('home_style/images/default_blog_pic/'.random_int(1,3).'.jpg')}}"
                                                 alt="<?php echo $post->{'post_title_' . App::getLocale()} ?>">
                                            <div class="flex-caption"><?php echo $post->{'post_title_' . App::getLocale()} ?></div>
                                        </a>
                                    </li>
                                @endif
                            <!-- if there is no attachmed pic to show-->
                                @if(count($post->attachments)==0)
                                    <div>
                                        <img class="img-responsive"
                                             style="height: 241px;max-height: 241px;min-height: 241px;"
                                             src="{{URL::to('home_style/images/default_blog_pic/'.random_int(1,3).'.jpg')}}"
                                             alt="<?php echo $post->{'post_title_' . App::getLocale()} ?>">
                                    </div>
                                @endif


                            </ul>
                        </div>

                        <h3 class="margin-top-10"><?php echo $post->{'post_title_' . App::getLocale()} ?></h3>
                        <p><?php echo $post->{'post_subtitle_' . App::getLocale()} ?></p>
                        <a href="{{url('home/blog/post/'.$post->id.'/'.str_replace(' ','_',$post->{'post_title_'.App::getLocale()}))}}"
                           class="btn btn-default">{{trans('home_content.chech_more')}} </a>
                    </div>
                @endforeach
            </div>

        </div>
    </section>
    <!-- /Courses -->



    <!-- Testimonials -->
    <section class="padding-xxs">
        <div class="container">

            <div class="heading-title text-center">
                <h4 class="helvetica ">{{trans('home_content.userreview')}}</h4>

            </div>

            <div class="owl-carousel text-center owl-testimonial nomargin"
                 data-plugin-options='{"singleItem": true, "autoPlay": 2500, "navigation": false, "pagination": true, "transitionStyle":"fade"}'>

                @foreach($testimentals as $testimental)
                    <div class="testimonial">
                        <figure>
                            <img class="rounded" src="{{URL::to($testimental->avatar)}}" alt=""/>
                        </figure>
                        <div class="testimonial-content nopadding">
                            <p class="lead">{{$testimental->description}}</p>
                            <cite>
                                {{$testimental->full_name}}
                                <span>{{$testimental->profession}}</span>
                            </cite>
                        </div>
                    </div>
                @endforeach

            </div>

        </div>
    </section>
    <!-- /Testimonials -->
@endsection
			
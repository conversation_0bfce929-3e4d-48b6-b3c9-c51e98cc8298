<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\User;
use App\Mail\SponsorAttendanceMailable;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EmailSponsorsMonthlyAttendance extends Command
{
    protected $signature = 'attendance:email-sponsors 
                            {month? : Numeric month (1-12)} 
                            {year? : Numeric year (e.g. 2025)}';

    protected $description = 'Send monthly attendance summary to each external collaborator';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $month = $this->argument('month') ?? now()->subMonth()->format('m');
            $year = $this->argument('year') ?? now()->subMonth()->format('Y');
            
            $periodLabel = Carbon::createFromDate($year, $month, 1)->format('F Y');
            
            Log::info("Starting attendance:email-sponsors for period {$periodLabel}");
            $this->info("Generating attendance reports for {$periodLabel}");
            
            // Begin transaction to maintain data consistency
            DB::beginTransaction();

            // 1) Get all external collaborators
            $collaborators = User::role('external_collaborator')->get();
            
            if ($collaborators->isEmpty()) {
                Log::info("No external collaborators found. Command completed.");
                $this->info("No external collaborators found. Done.");
                DB::commit();
                return Command::SUCCESS;
            }
            
            Log::info("Found {$collaborators->count()} external collaborators");
            $this->info("Processing {$collaborators->count()} external collaborators");

            $emailsSent = 0;
            $emailsFailed = 0;

            // 2) For each user, gather attendance data
            foreach ($collaborators as $user) {
                try {
                    Log::info("Generating attendance for user #{$user->id} ({$user->email})");
                    $this->line("Processing user #{$user->id}");

                    // Get centers the collaborator has access to
                    $centerIds = $user->allowedCenters()->pluck('centers.id')->toArray();
                    
                    if (empty($centerIds)) {
                        Log::info("No centers assigned for user #{$user->id}");
                        $this->line("- No centers assigned for this user. Skipping.");
                        continue;
                    }
                    
                    Log::info("User #{$user->id} has access to " . count($centerIds) . " centers");
                    $teacherCenters = implode(',', $centerIds);

                    // Get attendance data from stored procedure
                    $attendanceRaw = DB::select(
                        'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
                        [$month, $year, null, null, $teacherCenters]
                    );

                    // Format the attendance data
                    $attendanceSummary = $this->formatAttendance($attendanceRaw);
                    
                    // 3) Send the email
                    Mail::to($user->email)->send(new SponsorAttendanceMailable(
                        $user,
                        $month,
                        $year,
                        $attendanceSummary
                    ));
                    
                    $emailsSent++;
                    Log::info("Email sent to user #{$user->id} ({$user->email})");
                    $this->line("- Email sent successfully");
                } catch (\Exception $e) {
                    $emailsFailed++;
                    Log::error("Failed to process user #{$user->id}: " . $e->getMessage());
                    $this->error("- Failed to process user #{$user->id}: " . $e->getMessage());
                    // Continue with next user, don't let one failure stop the whole process
                }
            }

            DB::commit();
            
            $summaryMessage = "Command completed: {$emailsSent} emails sent, {$emailsFailed} failed";
            Log::info($summaryMessage);
            $this->info($summaryMessage);
            
            return ($emailsFailed > 0) ? Command::FAILURE : Command::SUCCESS;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Command failed: " . $e->getMessage());
            $this->error("Command failed: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Format attendance data into a readable summary
     *
     * @param array $rows
     * @return array
     */
    private function formatAttendance($rows)
    {
        if (empty($rows)) {
            Log::info("No attendance data found for this report");
            return ["No attendance data found for this period"];
        }

        // Format each row of attendance data
        $summary = [];
        foreach ($rows as $r) {
            $summary[] = "Employee {$r->employee_id} on {$r->formatted_date}: {$r->status}";
        }
        
        Log::info("Formatted " . count($summary) . " attendance records");
        return $summary;
    }
}

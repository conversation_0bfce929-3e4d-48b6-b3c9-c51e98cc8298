<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProgramLevelLessonFormInputs extends Model
{
    use HasFactory;

    protected $table = 'program_level_lesson_form_inputs';
    protected $fillable = ['program_level_id', 'property_name', 'property_type', 'property_value'];

    public function programLevel()
    {
        return $this->belongsTo('App\Models\ProgramLevel', 'program_level_id');
    }
}

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Events;

use Illuminate\Queue\SerializesModels;
use Mo<PERSON>les\JobSeeker\Entities\JobCategory;

class JobCategoryModifiedEvent
{
    use SerializesModels;

    /**
     * The job category instance.
     *
     * @var JobCategory
     */
    public $jobCategory;

    /**
     * The type of modification that occurred.
     *
     * @var string
     */
    public $action;

    /**
     * The replacement category ID when a category is deleted/archived with a specified replacement.
     *
     * @var int|null
     */
    public $replacementCategoryId;

    /**
     * Additional context about the modification.
     *
     * @var array
     */
    public $context;

    /**
     * Create a new event instance.
     *
     * @param JobCategory $jobCategory
     * @param string $action One of: 'created', 'updated', 'deleted', 'archived'
     * @param int|null $replacementCategoryId ID of replacement category for deletion/archival
     * @param array $context Additional context about the modification
     */
    public function __construct(
        JobCategory $jobCategory, 
        string $action, 
        ?int $replacementCategoryId = null,
        array $context = []
    ) {
        $this->jobCategory = $jobCategory;
        $this->action = $action;
        $this->replacementCategoryId = $replacementCategoryId;
        $this->context = $context;
    }
} 
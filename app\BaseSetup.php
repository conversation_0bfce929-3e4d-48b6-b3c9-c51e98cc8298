<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\BaseSetup
 *
 * @property int $id
 * @property string $base_setup_name
 * @property int $active_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $base_group_id
 * @property int|null $organization_id
 * @method static \Illuminate\Database\Eloquent\Builder|BaseSetup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BaseSetup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BaseSetup query()
 * @method static \Illuminate\Database\Eloquent\Builder|BaseSetup whereActiveStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BaseSetup whereBaseGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BaseSetup whereBaseSetupName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BaseSetup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BaseSetup whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BaseSetup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BaseSetup whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BaseSetup whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BaseSetup whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class BaseSetup extends Model
{
	//   
}

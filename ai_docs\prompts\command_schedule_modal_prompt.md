Act as a senior full-stack Laravel developer. Your task is to enhance and fix the "Add/Edit Schedule Rule" modal located in the `JobSeeker` module. The goal is to improve the user experience for the `categories` and `locations` dropdowns and fix a critical bug where selected values are not re-populated when editing a rule.

You must follow this plan precisely.

**File & Module Context:**
*   **Module:** `JobSeeker`
*   **View File:** `resources\views\modules\jobseeker\admin\command_schedule\index.blade.php`
*   **Controller:** `Modules\JobSeeker\Http\Controllers\Admin\CommandScheduleController.php`
*   **Entity for Rules:** `Modules\JobSeeker\Entities\CommandScheduleRule.php`
*   **Entity for Categories:** `Modules\JobSeeker\Entities\ProviderJobCategory.php`
*   **Dropdown Library:** The dropdowns use the **Select2** JavaScript library.

---

### **Execution Plan:**

**Part 1: Backend Modifications (`CommandScheduleController.php`)**

1.  **Modify the `show()` method:**
    *   This method is called when the user clicks "Edit". It currently returns `provider_job_category_ids`.
    *   You must also make it return the IDs of the selected locations for the given rule. Add a new key to the returned JSON response called `location_ids`.
    *   The final JSON response from `show()` should look like this:
        ```json
        {
          // ... other rule data
          "provider_job_category_ids": [1, 5, 10],
          "location_ids": [3, 8]
        }
        ```

2.  **Modify the `store()` and `update()` methods:**
    *   These methods handle the form submission for creating and updating rules.
    *   You must add logic to handle a special case where the incoming value for `categories` or `locations` is the string `"All"`.
    *   **If `categories` is "All":**
        *   You must dynamically fetch the IDs of all `ProviderJobCategory` records that belong to the schedule's provider (e.g., for a 'jobsaf' provider, query where `provider_name` is 'jobs_af').
        *   Save this complete array of category IDs to the `CommandScheduleRule`.
    *   **If `locations` is "All":**
        *   Fetch the IDs of all available locations.
        *   Save this complete array of location IDs to the `CommandScheduleRule`.
    *   If the values are not "All", save the provided arrays of IDs as usual.

**Part 2: Frontend Modifications (`index.blade.php`)**

You will write JavaScript code within this file to manage the Select2 dropdowns for `categories` and `locations`.

1.  **Add the "All" Option:**
    *   When the modal is initialized, dynamically prepend an "All" option to both the categories and locations dropdowns.
    *   Example: `<option value="All">All</option>`

2.  **Implement "All" Selection Logic:**
    *   If a user clicks and selects the "All" option, use JavaScript to deselect all other chosen options in that dropdown.
    *   If a user has "All" selected and then clicks any other individual option, the "All" option must be deselected automatically.

3.  **Implement Automatic "All" Selection:**
    *   If a user manually selects every single available option in a dropdown (excluding the "All" option itself), your code must automatically clear all the individual selections and set the value of the dropdown to "All".

4.  **Fix the Edit Modal State (Critical Bug Fix):**
    *   In your AJAX success callback for the `show()` method (when the modal is populated with data for editing):
    *   Use the `provider_job_category_ids` and `location_ids` from the JSON response to correctly set the selected values in the Select2 dropdowns.
    *   **Crucially, you must use the `.trigger('change')` method from Select2 after setting the value.** For example: `$('#categories_dropdown_id').val(response.provider_job_category_ids).trigger('change');`
    *   Add logic to check if the number of incoming IDs matches the total number of available options. If it does, set the dropdown to select "All" instead of the individual IDs.

---

**Final Goal:** The user should be able to seamlessly use the "All" option for both categories and locations. When they edit a rule, the modal must accurately reflect the saved state, including correctly handling the "All" selection if all items were previously saved. The experience should be smooth and bug-free.

<?php

namespace Modules\Site\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Student;
use App\Guardian;
use App\Center;
use App\Program;

class SponsorController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        $student    = auth()->user();

        $centers    = Center::where('status' ,'=' , 'active')->get()->pluck('name', 'id');

        $programs   = Program::where('status' ,'=' , 'active')->get()->pluck('title', 'id');


        return view(theme_path("sponsor.index"),compact('student' , 'centers' , 'programs'));
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view(theme_path("student.create"));
    }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $this->validation($request);
        if(auth()->guard('guardian')->check()){
            $request->merge([
                "guardian_id" => auth()->user()->id,
            ]);
        }
        $request->merge([
            "organization_id" => config('organization_id'),
        ]);

        $student = Student::create($request->all());

        return redirect()->back($student->id);
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show()
    {
        return view(theme_path("show"));
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit()
    {
        return view(theme_path("edit"));
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(Request $request, $id = null)
    {
        if(
            (auth()->guard('student')->check() && auth()->user()->id == $id )
            || (auth()->guard('guardian')->check() && is_guardian(auth()->user() , $id) )
        ){

        $roles = [];

        
        if(isset($request->update_profile)){

            $this->validation($request);

            $requestData = $request->all();
            
            if($request->hasFile('image')){
                $imageName = \Illuminate\Support\Str::random(9).'.'. $request->file('image')->getClientOriginalExtension();

                $path = 'userfiles/'.userfolder($id).'/'.\Illuminate\Support\Str::random(9).'_profile/';
        
                $request->file('image')->move(
                    base_path() .'/public/'. $path, $imageName
                );
                
                $requestData['image'] = $path.$imageName;
            }
            if($request->hasFile('identity')){
                $identityName = \Illuminate\Support\Str::random(9).'.'. $request->file('identity')->getClientOriginalExtension();

                $path = 'userfiles/'.userfolder($id).'/'.\Illuminate\Support\Str::random(9).'_identity/';
        
                $request->file('identity')->move(
                    base_path() .'/public/'. $path, $identityName
                );
                
                $requestData['identity'] = $path.$identityName;
            }
            
            $student = Student::findOrFail($id);
            
            $requestData['status'] = 'update_guardian';            
            
            $student->fill($requestData);

            $student->save();

            flash('Profile updated!!');
            return redirect()->back();
        }
            
        }

    }

    private function validation($request)
    {
        if(config("settings.student_form_full_name") == "required"){
            $roles["full_name"] = "required";
        }
        if(config("settings.student_form_full_name_trans") == "required"){
            $roles["full_name_trans"] = "required";
        }
        if(config("settings.student_form_full_name_language") == "required"){
            $roles["full_name_language"] = "required";
        }
        if(config("settings.student_form_gender") == "required"){
            $roles["gender"] = "required";
        }
        if(config("settings.student_form_date_of_birth") == "required"){
            $roles["date_of_birth"] = "required|date";
        }
        if(config("settings.student_form_identity_number") == "required"){
            $roles["identity_number"] = "required";
        }
        if(config("settings.student_form_identity") == "required"){
            $roles["identity"] = "required| mimes:jpeg,jpg,bmp,png,gif,svg,pdf,zip | max:5000";
        }
        if(config("settings.student_form_image") == "required"){
            $roles["image"] = "required|image| mimes:jpeg,jpg,png | max:3000";
        }
        if(config("settings.student_form_nationality") == "required"){
            $roles["nationality"] = "required";
        }
        if(config("settings.student_form_mobile") == "required"){
            $roles["mobile"] = "required";
        }

        $this->validate($request, $roles);
    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }
}

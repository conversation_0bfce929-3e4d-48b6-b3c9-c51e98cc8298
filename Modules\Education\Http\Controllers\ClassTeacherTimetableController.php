<?php

namespace Modules\Education\Http\Controllers;

use App\BaseSetup;
use App\ClassTimetable;
use App\Employee;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\StudentHefzReport;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Matrix\Builder;
use Modules\Education\Http\Requests\ClassTeacherTimetableRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use Carbon\Carbon;
use App\ClassStudent;
use App\ClassTeacher;
use App\Http\Requests;
use App\ClassTeacherSubject;
use Illuminate\Http\Request;
use App\ClassSubjectTimetable;
use App\Http\Controllers\Controller;
use App\Cen_Emp;
use Psr\Log\LoggerInterface;

class ClassTeacherTimetableController extends Controller
{


    private LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function addTimetable(ClassTeacherTimetableRequest $request)
    {



        try {

            DB::beginTransaction();
//            $classTeacherSubjectId = $this->addClassTeacherSubject($request);




            // Validate the input
            $validatedData = $request->validated();

            //  check class for security validation
//            $class = Classes::findOrFail($request->class_id);

            // Check if there is an existing active timetable for the same subject in class
            $timetable = new ClassTimetable([
                'mon' => $validatedData['mon'],
                'tue' => $validatedData['tue'],
                'wed' => $validatedData['wed'],
                'thu' => $validatedData['thu'],
                'fri' => $validatedData['fri'],
                'sat' => $validatedData['sat'],
                'sun' => $validatedData['sun'],
                'class_id' => $validatedData['class_id'],
                'class_duration' => $validatedData['duration'],
//                'class_teacher_subject_id' => $classTeacherSubjectId,
                'start_at' => Carbon::parse($validatedData['start_at']),
            ]);
            $timetable->save();
            DB::commit();



            if ($request->ajax()) {
                // Handle the AJAX request
                return response()->json(['message' => 'Timetable was added Succesfully!']);
            } else {
                // Handle the non-AJAX request
                Toastr::success('Timetable was added Succesfully!', 'Success');

                return redirect()->back();
            }


        } catch (\Exception $e) {

            $this->logger->error($e->getMessage());
            DB::rollback();
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }

    }

    public function changeTimetable(Request $request)
    {


        try {
            // check class for security validation
            $class = Classes::with('timetable')->findOrFail($request->class_id);
            $totalDays = 0;




            // check if there is existing active timetable for the same subject in class
            $timetable = $class->timetable;



            $timetable->mon = $request->mon;
            $timetable->tue = $request->tue;
            $timetable->wed = $request->wed;
            $timetable->thu = $request->thu;
            $timetable->fri = $request->fri;
            $timetable->sat = $request->sat;
            $timetable->sun = $request->sun;
            $timetable->class_duration = $request->duration;
            // $timetable->class_teacher_subject_id = $request->changeClass_teacher_subject_id;
            $timetable->start_at = $request->start_at;
            $timetable->days_count_per_month = $totalDays*4; // 4 refers to the number of weeks per month
            $timetable->save();
            if ($request->ajax()) {
                // Handle the AJAX request
                return response()->json(['message' => 'Timetable was updated Succesfully!']);
            } else {
                // Handle the non-AJAX request
                Toastr::success('Timetable was updated Succesfully!', 'Success');

                return redirect()->back();
            }
        } catch (\Exception $e) {
            dd($e->getMessage());
            \Log::error($e);
            Toastr::error('An error occurred while updating the timetable. Please try again.', 'Error');
        }





    }
    public function changeTimetableDay(Request $request)
    {



        try {
            // check class for security validation
            $class = Classes::with('timetable')->findOrFail($request->class_id);
            // check if there is existing active timetable for the same subject in class

            $day = $request->get('day');
            $timetable = $class->timetable;
            $timetable->$day = $request->time;

            $timetable->save();
            if ($request->ajax()) {
                // Handle the AJAX request
                return response()->json(['message' => 'Timetable was updated Succesfully!']);
            } else {
                // Handle the non-AJAX request
                Toastr::success('Timetable was updated Succesfully!', 'Success');

                return redirect()->back();
            }
        } catch (\Exception $e) {
            dd($e->getMessage());
            \Log::error($e);
            Toastr::error('An error occurred while updating the timetable. Please try again.', 'Error');
        }





    }
    public function changeTimetableDuration(Request $request)
    {



        try {
            // check class for security validation
            $class = Classes::with('timetable')->findOrFail($request->class_id);
            // check if there is existing active timetable for the same subject in class

            $duration = $request->get('duration');
            $timetable = $class->timetable;
            $timetable->class_duration = $duration;

            $timetable->save();
            if ($request->ajax()) {
                // Handle the AJAX request
                return response()->json(['message' => 'Timetable  duration updated Succesfully!']);
            } else {
                // Handle the non-AJAX request
                Toastr::success('Timetable  duration updated Succesfully!', 'Success');

                return redirect()->back();
            }
        } catch (\Exception $e) {
            dd($e->getMessage());
            \Log::error($e);
            Toastr::error('An error occurred while updating the timetable. Please try again.', 'Error');
        }





    }
    public function changeTimetableStartDate(Request $request)
    {



        try {
            // check class for security validation
            $class = Classes::with('timetable')->findOrFail($request->class_id);
            // check if there is existing active timetable for the same subject in class

            $startat = $request->get('start_at');
            $timetable = $class->timetable;
            $timetable->start_at = $startat;

            $timetable->save();
            if ($request->ajax()) {
                // Handle the AJAX request
                return response()->json(['message' => 'Timetable  duration updated Succesfully!']);
            } else {
                // Handle the non-AJAX request
                Toastr::success('Timetable  duration updated Succesfully!', 'Success');

                return redirect()->back();
            }
        } catch (\Exception $e) {
            dd($e->getMessage());
            \Log::error($e);
            Toastr::error('An error occurred while updating the timetable. Please try again.', 'Error');
        }





    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Classes::destroy($id);

        Session::flash('flash_message', 'Class deleted!');

        return redirect('workplace/education/classes');
    }
    public function clear(Request $request, ClassTimetable $timetable)
    {
        try {
            $day = $request->input('day');

            // Complexity: Updating the value of the specified day
            $timetable->$day = null;
            $timetable->save();

            return response()->json(['message' => 'Timetable cleared successfully']);
        } catch (\Exception $exception) {
            // Log the exception for debugging purposes
            \Log::error('Exception occurred while clearing timetable: ' . $exception->getMessage());

            // Return different error messages based on the environment
            if (\App::environment('production')) {
                return response()->json(['message' => 'An error occurred while clearing the timetable. Please try again later.'], 500);
            } else {
                return response()->json(['message' => $exception->getMessage()], 500);
            }
        }
    }
    private function addClassTeacherSubject(ClassTeacherTimetableRequest $request)
    {

        if (empty($request->input('class_teacher_subject_id'))) {
            $classTeacher = ClassTeacher::with('class_info')->findOrFail($request->class_teacher_id);

            $classTeacherSubjectId  = $classTeacher->subject()->create([
                'class_teacher_id' => $request->class_teacher_id,
                'subject_id' => $request->subject_id,
                'start_date' => $request->start_at,
                'program_id' => $request->program_id,
            ]);


            return $classTeacherSubjectId->id;

        }
        else{

            return $request->class_teacher_subject_id;
        }
    }
}

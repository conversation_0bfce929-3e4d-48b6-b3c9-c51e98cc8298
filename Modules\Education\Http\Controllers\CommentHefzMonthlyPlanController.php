<?php

namespace Modules\Education\Http\Controllers;

use App\Attendance;
use App\AttendanceOption;
use App\Cen_Emp;
use App\Student;
use App\StudentHefzPlan;
use App\StudentRevisionPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\EducationalReports\Http\Requests\CreateHefzPlanRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentAttendance;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class CommentHefzMonthlyPlanController extends Controller
{

    public function update(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:student_hefz_plans,id',
            'comment' => 'required|string|max:2000'
        ]);

        $plan = StudentHefzPlan::where('id', $request->id)
            ->update([
                'supervisor_comment' => $request->comment
            ]);

        return response()->json([
            'message' => 'Comment added to the plan',
            'success' => true
        ]);
    }


    public function show(Request $request,$planId)
    {




        $plan = StudentRevisionPlan::where('id', $planId)->first()->supervisor_comment;
        return response()->json(['data' => $plan]);


    }
}

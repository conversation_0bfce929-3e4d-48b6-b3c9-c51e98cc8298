jQuery(document).ready(function(){$("#highchart_1").highcharts({chart:{style:{fontFamily:"Open Sans"}},title:{text:"Monthly Average Temperature",x:-20},subtitle:{text:"Source: WorldClimate.com",x:-20},xAxis:{categories:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]},yAxis:{title:{text:"Temperature (°C)"},plotLines:[{value:0,width:1,color:"#808080"}]},tooltip:{valueSuffix:"°C"},legend:{layout:"vertical",align:"right",verticalAlign:"middle",borderWidth:0},series:[{name:"Tokyo",data:[7,6.9,9.5,14.5,18.2,21.5,25.2,26.5,23.3,18.3,13.9,9.6]},{name:"New York",data:[-.2,.8,5.7,11.3,17,22,24.8,24.1,20.1,14.1,8.6,2.5]},{name:"Berlin",data:[-.9,.6,3.5,8.4,13.5,17,18.6,17.9,14.3,9,3.9,1]},{name:"London",data:[3.9,4.2,5.7,8.5,11.9,15.2,17,16.6,14.2,10.3,6.6,4.8]}]}),$.getJSON("http://www.highcharts.com/samples/data/jsonp.php?filename=usdeur.json&callback=?",function(e){$("#highchart_2").highcharts({chart:{zoomType:"x",style:{fontFamily:"Open Sans"}},title:{text:"USD to EUR exchange rate over time"},subtitle:{text:void 0===document.ontouchstart?"Click and drag in the plot area to zoom in":"Pinch the chart to zoom in"},xAxis:{type:"datetime"},yAxis:{title:{text:"Exchange rate"}},legend:{enabled:!1},plotOptions:{area:{fillColor:{linearGradient:{x1:0,y1:0,x2:0,y2:1},stops:[[0,Highcharts.getOptions().colors[0]],[1,Highcharts.Color(Highcharts.getOptions().colors[0]).setOpacity(0).get("rgba")]]},marker:{radius:2},lineWidth:1,states:{hover:{lineWidth:1}},threshold:null}},series:[{type:"area",name:"USD to EUR",data:e}]})}),$("#highchart_3").highcharts({chart:{type:"area",style:{fontFamily:"Open Sans"}},title:{text:"Historic and Estimated Worldwide Population Growth by Region"},subtitle:{text:"Source: Wikipedia.org"},xAxis:{categories:["1750","1800","1850","1900","1950","1999","2050"],tickmarkPlacement:"on",title:{enabled:!1}},yAxis:{title:{text:"Billions"},labels:{formatter:function(){return this.value/1e3}}},tooltip:{shared:!0,valueSuffix:" millions"},plotOptions:{area:{stacking:"normal",lineColor:"#666666",lineWidth:1,marker:{lineWidth:1,lineColor:"#666666"}}},series:[{name:"Asia",data:[502,635,809,947,1402,3634,5268]},{name:"Africa",data:[106,107,111,133,221,767,1766]},{name:"Europe",data:[163,203,276,408,547,729,628]},{name:"America",data:[18,31,54,156,339,818,1201]},{name:"Oceania",data:[2,2,2,6,13,30,46]}]});var e=["0-4","5-9","10-14","15-19","20-24","25-29","30-34","35-39","40-44","45-49","50-54","55-59","60-64","65-69","70-74","75-79","80-84","85-89","90-94","95-99","100 + "];$("#highchart_4").highcharts({chart:{type:"bar",style:{fontFamily:"Open Sans"}},title:{text:"Population pyramid for Germany, 2015"},subtitle:{text:'Source: <a href="http://populationpyramid.net/germany/2015/">Population Pyramids of the World from 1950 to 2100</a>'},xAxis:[{categories:e,reversed:!1,labels:{step:1}},{opposite:!0,reversed:!1,categories:e,linkedTo:0,labels:{step:1}}],yAxis:{title:{text:null},labels:{formatter:function(){return Math.abs(this.value)+"%"}}},plotOptions:{series:{stacking:"normal"}},tooltip:{formatter:function(){return"<b>"+this.series.name+", age "+this.point.category+"</b><br/>Population: "+Highcharts.numberFormat(Math.abs(this.point.y),0)}},series:[{name:"Male",data:[-2.2,-2.2,-2.3,-2.5,-2.7,-3.1,-3.2,-3,-3.2,-4.3,-4.4,-3.6,-3.1,-2.4,-2.5,-2.3,-1.2,-.6,-.2,-0,-0]},{name:"Female",data:[2.1,2,2.2,2.4,2.6,3,3.1,2.9,3.1,4.1,4.3,3.6,3.4,2.6,2.9,2.9,1.8,1.2,.6,.1,0]}]});var t,a,r,o,i=Highcharts.getOptions().colors,e=["MSIE","Firefox","Chrome","Safari","Opera"],n=[{y:56.33,color:i[0],drilldown:{name:"MSIE versions",categories:["MSIE 6.0","MSIE 7.0","MSIE 8.0","MSIE 9.0","MSIE 10.0","MSIE 11.0"],data:[1.06,.5,17.2,8.11,5.33,24.13],color:i[0]}},{y:10.38,color:i[1],drilldown:{name:"Firefox versions",categories:["Firefox v31","Firefox v32","Firefox v33","Firefox v35","Firefox v36","Firefox v37","Firefox v38"],data:[.33,.15,.22,1.27,2.76,2.32,2.31,1.02],color:i[1]}},{y:24.03,color:i[2],drilldown:{name:"Chrome versions",categories:["Chrome v30.0","Chrome v31.0","Chrome v32.0","Chrome v33.0","Chrome v34.0","Chrome v35.0","Chrome v36.0","Chrome v37.0","Chrome v38.0","Chrome v39.0","Chrome v40.0","Chrome v41.0","Chrome v42.0","Chrome v43.0"],data:[.14,1.24,.55,.19,.14,.85,2.53,.38,.6,2.96,5,4.32,3.68,1.45],color:i[2]}},{y:4.77,color:i[3],drilldown:{name:"Safari versions",categories:["Safari v5.0","Safari v5.1","Safari v6.1","Safari v6.2","Safari v7.0","Safari v7.1","Safari v8.0"],data:[.3,.42,.29,.17,.26,.77,2.56],color:i[3]}},{y:.91,color:i[4],drilldown:{name:"Opera versions",categories:["Opera v12.x","Opera v27","Opera v28","Opera v29"],data:[.34,.17,.24,.16],color:i[4]}},{y:.2,color:i[5],drilldown:{name:"Proprietary or Undetectable",categories:[],data:[],color:i[5]}}],l=[],s=[],h=n.length;for(t=0;h>t;t+=1)for(l.push({name:e[t],y:n[t].y,color:n[t].color}),r=n[t].drilldown.data.length,a=0;r>a;a+=1)o=.2-a/r/5,s.push({name:n[t].drilldown.categories[a],y:n[t].drilldown.data[a],color:Highcharts.Color(n[t].color).brighten(o).get()});$("#highchart_5").highcharts({chart:{type:"pie",style:{fontFamily:"Open Sans"}},title:{text:"Browser market share, January, 2015 to May, 2015"},subtitle:{text:'Source: <a href="http://netmarketshare.com/">netmarketshare.com</a>'},yAxis:{title:{text:"Total percent market share"}},plotOptions:{pie:{shadow:!1,center:["50%","50%"]}},tooltip:{valueSuffix:"%"},series:[{name:"Browsers",data:l,size:"60%",dataLabels:{formatter:function(){return this.y>5?this.point.name:null},color:"#ffffff",distance:-30}},{name:"Versions",data:s,size:"80%",innerSize:"60%",dataLabels:{formatter:function(){return this.y>1?"<b>"+this.point.name+":</b> "+this.y+"%":null}}}]})});
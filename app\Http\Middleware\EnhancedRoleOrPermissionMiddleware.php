<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Exceptions\UnauthorizedException;

/**
 * Enhanced RoleOrPermissionMiddleware that allows system viewers READ-ONLY access
 * CRITICAL: System viewers should NEVER be allowed to perform write operations
 */
final class EnhancedRoleOrPermissionMiddleware
{
    public function handle(Request $request, Closure $next, $roleOrPermission, $guard = null)
    {
        $authGuard = Auth::guard($guard);
        $user = $authGuard->user();

        if (!$user) {
            throw UnauthorizedException::notLoggedIn();
        }

        // CRITICAL SECURITY FIX: System viewers get READ-only access
        if ($user->hasRole('system_viewer_' . config('organization_id') . '_')) {
            // Block ALL write operations for system viewers
            if (in_array($request->method(), ['POST', 'PUT', 'PATCH', 'DELETE'])) {
                throw UnauthorizedException::forRolesOrPermissions(['system_viewer_blocked_write_operation']);
            }
            
            // Allow GET operations (read-only access)
            if ($request->method() === 'GET') {
                return $next($request);
            }
            
            // Block any other methods
            throw UnauthorizedException::forRolesOrPermissions(['system_viewer_unknown_method']);
        }

        // For managing directors, also allow bypass (existing logic)
        if ($user->hasRole('managing-director_' . config('organization_id') . '_')) {
            return $next($request);
        }

        $rolesOrPermissions = is_array($roleOrPermission)
            ? $roleOrPermission
            : explode('|', $roleOrPermission);

        if (!$user->hasAnyRole($rolesOrPermissions) && !$user->hasAnyPermission($rolesOrPermissions)) {
            throw UnauthorizedException::forRolesOrPermissions($rolesOrPermissions);
        }

        return $next($request);
    }
} 
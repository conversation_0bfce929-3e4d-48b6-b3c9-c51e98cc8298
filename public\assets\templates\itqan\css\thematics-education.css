/** MUSIC 
	home-theme-education.html 
 ********************************************* **/
@import url(http://fonts.googleapis.com/css?family=Roboto);
.font-roboto {
	font-family: "Roboto" !important;
}


/* header, menu & topbar background color */
#header,
#topMain.nav-pills>li,
#topNav div.submenu-dark ul.dropdown-menu {
	background-color:white;
}
#topBar {
	/*background-color:#89CE95 !important;*/
	background-color:#151515 !important;
}
#topNav ul.dropdown-menu {
	border-top-color:#129C2A !important;
}

#topNav button.btn-mobile {
	color:#000;
}

/* menu & topbar links color */
#topBar ul.top-links li.text-welcome,
#topBar ul.top-links>li>a,
#header li.search i.fa,
#header ul.nav-second-main li>a,
#topMain.nav-pills>li>a {
	color:#000 !important;
}
#topBar ul.top-links>li.active>a,
#topMain.nav-pills>li.active>a {
	color:#000 !important;
	background-color: #89CE95;
	border-radius: 0px;
}

/* footer */
#footer {
	background: #24293a;
	background: -moz-linear-gradient(top, #1a1d2b 0%, #2e3648 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #1a1d2b), color-stop(100%, #2e3648));
	background: -webkit-linear-gradient(top, #1a1d2b 0%, #2e3648 100%);
	background: -o-linear-gradient(top, #1a1d2b 0%, #2e3648 100%);
	background: -ms-linear-gradient(top, #1a1d2b 0%, #2e3648 100%);
	background: linear-gradient(to bottom, #1a1d2b 0%,#2e3648 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1a1d2b', endColorstr='#2e3648',GradientType=0 );
}

#topNav div.submenu-dark ul.dropdown-menu>li a {
    color: #000;
}


#topNav div.submenu-dark ul.dropdown-menu>li a:hover {
    background-color: #89CE95;
}



/*  special theme for wisard registry  */

.process-step .btn:focus{outline:none}
.process{display:table;width:100%;position:relative}
.process-row{display:table-row}
.process-step button[disabled]{opacity:1 !important;filter: alpha(opacity=100) !important}
.process-row:before{top:40px;bottom:0;position:absolute;content:" ";width:100%;height:1px;background-color:#ccc;z-order:0}
.process-step{display:table-cell;text-align:center;position:relative}
.process-step p{margin-top:4px}
.btn-circle{width:80px;height:80px;text-align:center;font-size:12px;border-radius:50%}

.wisard_footerbtn{
	width:100%;height:61px;background-color: #F5F5F5;padding: 10px;
}

.align_wisard_icons{
margin-left: 7px;
}


#topBar ul.top-links li.text-welcome,
#topBar ul.top-links>li>a,
#header li.search i.fa,
#header ul.nav-second-main li>a{
	color:#fff !important;
}
#topBar ul.top-links>li.active>a{
	color:#fff !important;
	background-color: #89CE95;
	border-radius: 0px;
}

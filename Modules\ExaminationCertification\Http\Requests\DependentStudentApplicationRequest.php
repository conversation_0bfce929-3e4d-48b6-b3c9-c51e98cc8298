<?php

namespace Modules\ExaminationCertification\Http\Requests;

use App\Rules\CheckDependentEmail;
use App\Rules\CheckIfStringIsArabic;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use function PHPUnit\Framework\returnArgument;

class DependentStudentApplicationRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {


        try {



//        We have used session here so that the dropdowns can be immediately refilled after failed validation rule
        session()->forget(['program','center','classes']);

        if (request()->filled('program')){
            session()->put(['program_id' => request()->get('program')]);

        }


        if (request()->filled('center')){
            session()->put(['center_id' => request()->get('center')]);

        }
        if (request()->filled('classes')){
            session()->put(['class_id' => request()->get('classes')]);

        }

        return [
//            'photo' => 'required',
            'student_mobile' => 'sometimes|required|phone:AUTO',
            'national_id_number' => 'required',
            'date_of_birth' => 'required|date|before:' . Carbon::now()->subYears(3),
            'program' => 'required',
            'email' => ['required',new CheckDependentEmail],
            'center' => 'required',
            'classes' => 'required',
            'gender' => 'required',
            'guardians_gender' => 'sometimes|required',
            'fullname' => 'required|max:100',
            'full_name_trans' => ['nullable', new CheckIfStringIsArabic],
            'displayname' => 'required|max:100',
            'document_title_1' => 'required_with:document_file_1',
            'document_title_2' => 'required_with:document_file_2',
            'document_title_3' => 'required_with:document_file_3',
            'document_title_4' => 'required_with:document_file_4',

//                'guardians_phone' => "required|phone:MY",

            'document_file_1' => 'sometimes|required|mimes:jpg,png,gif,pdf|max:2000', /** 2 mb */
            'document_file_2' => 'sometimes|required|mimes:jpg,png,gif,pdf|max:2000',
            'document_file_3' => 'sometimes|required|mimes:jpg,png,gif,pdf|max:2000',
            'document_file_4' => 'sometimes|required|mimes:jpg,png,gif,pdf|max:2000',
        ];


        }catch (\Exception $exception){

            \Log::error($exception);
            Toastr::success($exception->getMessage(), 'Success');
        }

    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {

        return [
            'student_mobile' => 'The :attribute field contains an invalid number.',
            'date_of_birth.before' => 'The student should be at least 3 years old',
            'student_mobile.phone' => 'prefixed with a + sign, e.g. +60 ....',

        ];
    }
}

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services\HealthCheckers;

/**
 * Health Checker Interface
 * 
 * Defines the contract for all health checkers in the system
 */
interface HealthCheckerInterface
{
    /**
     * Perform the health check
     * 
     * @return array{
     *     status: string,
     *     message: string,
     *     metrics?: array
     * }
     */
    public function check(): array;

    /**
     * Get the name of this health checker
     */
    public function getName(): string;

    /**
     * Get the type/priority of this health checker
     */
    public function getType(): string;
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SidebarSettingsApiController extends Controller
{
    /**
     * Get all sidebar settings for the current organization
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSettings()
    {
        try {
            $organizationId = config('organization_id');
            
            // Default values for sidebar settings
            $defaults = [
                'sidebar_bg_color_start' => '#1a2a3a',
                'sidebar_bg_color_end' => '#2c3e50',
                'sidebar_text_color' => '#ffffff',
                'sidebar_icon_color' => '#3498db',
                'sidebar_active_item_color' => '#3498db',
                'sidebar_hover_bg_color' => 'rgba(52, 152, 219, 0.1)',
                'sidebar_logo_size' => '70',
                'sidebar_auto_show' => '0',
                'sidebar_auto_hide' => '0',
                'sidebar_width' => '220',
                'sidebar_border_right' => 'rgba(255, 255, 255, 0.05)',
                'sidebar_shadow' => 'rgba(0, 0, 0, 0.15)',
            ];
            
            // Get all sidebar settings for this organization
            $settings = Setting::where('organization_id', $organizationId)
                ->whereIn('name', array_keys($defaults))
                ->get()
                ->pluck('value', 'name')
                ->toArray();
            
            // Merge with defaults to ensure all settings exist
            $mergedSettings = array_merge($defaults, $settings);
            
            return response()->json($mergedSettings);
        } catch (\Exception $e) {
            Log::error('Error fetching sidebar settings: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch sidebar settings'], 500);
        }
    }
} 
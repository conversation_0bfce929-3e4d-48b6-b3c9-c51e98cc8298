<?php

declare(strict_types=1);

namespace Modules\Education\Http\Controllers;

use App\Center;
use App\Classes;
use App\Employee;
use App\Student;
use App\StudentNouranyaReport;
use App\Weekend;
use App\ClassRoom;
use App\YearCheck;
use App\ApiBaseMethod;
use Illuminate\Http\Request;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use PDF; // assuming you have the alias set for DOMPDF

final class NouranyaClassTablesPDFController extends Controller
{
    public function __construct()
    {
        // User::checkAuth();
    }

    public function downloadPDF($classId, $monthYear)
    {
        // Convert the monthYear string to a Carbon instance
        $date = \Carbon\Carbon::createFromFormat('F Y', $monthYear);

        // Extract the month and year separately
        $monthName = $date->monthName; // Month name (e.g., June)
        $month = $date->format('m'); // Numeric representation of the month (e.g., 06)
        $year = $date->format('Y'); // 4-digit year (e.g., 2023)
        $letterHead = asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg");
        
        $class = Classes::find($classId);
        $className = $class->name; // Assuming the column name is 'name'
        $centerName = $class->center->name;
        $supervisors = $class->employee; // Assuming this returns the supervisor information
        $teachers = $class->teachers->pluck('full_name')->join(', ');
        $classTeachers = $class->teachers()->pluck('full_name')->toArray();

        // Check if $supervisors is not null
        if ($supervisors) {
            $supervisorInfo = $supervisors->pluck('name')->join(', ');
        } else {
            $supervisorInfo = ''; // Default value if no supervisors are found
        }

        $data = [
            'className' => $className,
            'classTeachers' => $classTeachers,
            'centerName' => $centerName,
            'letterHead' => $letterHead,
            'supervisors' => $supervisorInfo,
            'monthYear' => $date,
            'classId' => $classId,  // Retrieve and assign the required data
            'year' => $year,  // Retrieve and assign the required data
            'month' => $month,  // Retrieve and assign the required data
            'monthName' => $monthName,  // Retrieve and assign the required data
        ];

        // Generate a descriptive file name
        $fileName = "Nouranya_Report_{$centerName}_{$className}_{$monthName}_{$year}.pdf";

        view()->share('education::classes.reports.pdf.nouranya.all', $data);
        $pdf = PDF::loadView('education::classes.reports.pdf.nouranya.all', $data);
        return $pdf->download($fileName);
    }
} 
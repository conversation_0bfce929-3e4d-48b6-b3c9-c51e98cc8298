-- Context: Fix unmapped provider category causing mapping failures in Jobs.af notifications
-- Commands executed via DBHub MCP (execute_sql)
-- Purpose: Ensure provider categories map to canonical categories to avoid losing jobs in mapping
-- Related command(s): php artisan jobseeker:sync-jobs-af
-- Notes: provider_job_categories.id=48 belongs to 'acbar' and is already mapped to Technology (1); no change applied

-- 1) Inspect provider category 48
SELECT id, provider_name, provider_identifier, name, canonical_category_id
FROM provider_job_categories
WHERE id = 48;

-- 2) Attempt to set mapping for jobs.af (no rows matched; safe no-op)
UPDATE provider_job_categories
SET canonical_category_id = 1
WHERE id = 48 AND provider_name = 'jobs.af';

-- 3) Verify
SELECT id, provider_name, provider_identifier, name, canonical_category_id
FROM provider_job_categories
WHERE id = 48;


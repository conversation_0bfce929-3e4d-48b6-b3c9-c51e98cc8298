<?php

declare(strict_types=1);

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\AdmissionInterview;
use App\Http\Controllers\Controller;
use App\StudentRevisionPlan;
use App\StudentLastApprovedRevisionPlan;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

final class ApproveIndividualRevisionPlanController extends Controller
{
    public function __invoke(Request $request)
    {
        try {
            Log::info('Individual Revision Plan Approval Started', [
                'user_id' => auth()->id(),
                'plan_id' => $request->plan_id
            ]);

            $request->validate([
                'plan_id' => 'required|integer|exists:student_revision_plans,id'
            ]);

            DB::beginTransaction();

            $plan = StudentRevisionPlan::with(['student', 'center'])->find($request->plan_id);

            if (!$plan) {
                return response()->json([
                    'success' => false,
                    'message' => 'Plan not found'
                ], 404);
            }

            // Validate plan data
            if (!$this->validatePlanData($plan)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Plan validation failed: Missing required surah and ayah information'
                ], 400);
            }

            // Update the plan status
            $plan->status = 'active';
            $plan->approved_by = auth()->user()->id;
            $plan->updated_at = Carbon::now();
            $plan->save();

            $year = (string)$plan->start_date->year;
            $month = (string)$plan->start_date->month;

            // Update or create last approved revision plan for the same student, year and month
            \App\StudentLastApprovedRevisionPlan::updateOrCreate(
                [
                    'student_id' => $plan->student_id,
                   
                ],
                [
                    'approved_by' => $plan->approved_by,
                    'start_from_surat' => $plan->start_from_surat,
                    'start_from_ayat' => $plan->start_from_ayat,
                    'to_surat' => $plan->to_surat,
                    'to_ayat' => $plan->to_ayat,
                    'updated_at' => Carbon::now(),
                    'plan_year_month_day' => $plan->created_at->toDateString()
                ]
            );

            // Update admission interview status
            $admissionId = Admission::where('student_id', $plan->student_id)->first()->id;
            AdmissionInterview::where('admission_id', $admissionId)->update([
                'status' => 'interviewed',
                'confirmed_at' => Carbon::now(),
                'updated_by' => auth()->user()->id
            ]);

            DB::commit();

            // Calculate updated widget counts
            $hefzPlansWaiting_approval = \App\StudentHefzPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();

            $RevisionPlanWaiting_approval = \App\StudentRevisionPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();

            $NouranyaPlanWaitingApproval = \App\StudentNouranyaPlan::has('student')
                ->has('center')
                ->where('status', 'waiting_for_approval')
                ->where(function ($query) {
                    $query->where(function ($level1Query) {
                        $level1Query->whereNotNull('from_lesson')
                            ->whereNotNull('to_lesson');
                    })
                        ->orWhere(function ($level2Query) {
                            $level2Query->whereNotNull('from_lesson')
                                ->whereNotNull('to_lesson')
                                ->whereNotNull('from_lesson_line_number')
                                ->whereNotNull('to_lesson_line_number');
                        })
                        ->orWhere(function ($level3Query) {
                            $level3Query->whereNotNull('talaqqi_from_lesson')
                                ->whereNotNull('talaqqi_to_lesson')
                                ->whereNotNull('talqeen_from_lesson')
                                ->whereNotNull('talqeen_to_lesson');
                        });
                })
                ->count();

            $ijazasanadMemorizationPlanWaitingApproval = \App\IjazasanadMemorizationPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();

            $plans_waiting_approval = $hefzPlansWaiting_approval + $RevisionPlanWaiting_approval + $NouranyaPlanWaitingApproval + $ijazasanadMemorizationPlanWaitingApproval;

            Log::info('Individual Revision Plan Approval Completed Successfully', [
                'user_id' => auth()->id(),
                'plan_id' => $request->plan_id,
                'student_id' => $plan->student_id,
                'student_name' => $plan->student->full_name
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Revision plan approved successfully for ' . $plan->student->full_name,
                'plansWaitingApprovalCountWidget' => $plans_waiting_approval,
                'successful_approvals' => 1,
                'failed_approvals' => 0,
                'successful_students' => [[
                    'student_id' => $plan->student_id,
                    'student_name' => $plan->student->full_name,
                    'plan_id' => $plan->id
                ]],
                'failed_students' => []
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            Log::error('Individual Revision Plan Approval Failed', [
                'user_id' => auth()->id(),
                'plan_id' => $request->plan_id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while approving the plan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate plan data before approval
     */
    private function validatePlanData($plan): bool
    {
        // Check if the plan has the required surah and ayah information
        return !empty($plan->start_from_surat) && 
               !empty($plan->start_from_ayat) && 
               !empty($plan->to_surat) && 
               !empty($plan->to_ayat);
    }
} 
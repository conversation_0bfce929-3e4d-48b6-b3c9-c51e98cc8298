!function(){"use strict";var e=function(t){var n=t,r=function(){return n};return{get:r,set:function(e){n=e},clone:function(){return e(r())}}},t=tinymce.util.Tools.resolve("tinymce.PluginManager"),n=function(e){return!(!/(^|[ ,])tinymcespellchecker([, ]|$)/.test(e.settings.plugins)||!t.get("tinymcespellchecker")||("undefined"!=typeof window.console&&window.console.log&&window.console.log("Spell Checker Pro is incompatible with Spell Checker plugin! Remove 'spellchecker' from the 'plugins' option."),0))},r=function(e){return e.getParam("spellchecker_languages","English=en,Danish=da,Dutch=nl,Finnish=fi,French=fr_FR,German=de,Italian=it,Polish=pl,Portuguese=pt_BR,Spanish=es,Swedish=sv")},o=function(e){var t=e.getParam("language","en");return e.getParam("spellchecker_language",t)},i=function(e){return e.getParam("spellchecker_rpc_url")},c=function(e){return e.getParam("spellchecker_callback")},a=function(e){var t=new RegExp('[^\\s!"#$%&()*+,-./:;<=>?@[\\]^_{|}`\xa7\xa9\xab\xae\xb1\xb6\xb7\xb8\xbb\xbc\xbd\xbe\xbf\xd7\xf7\xa4\u201d\u201c\u201e\xa0\u2002\u2003\u2009]+',"g");return e.getParam("spellchecker_wordchar_pattern",t)},l=tinymce.util.Tools.resolve("tinymce.util.Tools"),s=tinymce.util.Tools.resolve("tinymce.util.URI"),u=tinymce.util.Tools.resolve("tinymce.util.XHR"),d=function(e){return e.fire("SpellcheckStart")},f=function(e){return e.fire("SpellcheckEnd")};function h(e){return e&&1===e.nodeType&&"false"===e.contentEditable}function g(e,t){var n,r,o,i,c,a=[],l=t.dom;function s(e,t){if(!e[0])throw new Error("findAndReplaceDOMText cannot handle zero-length matches");return{start:e.index,end:e.index+e[0].length,text:e[0],data:t}}function u(t){var n=e.getElementsByTagName("*"),r=[];t="number"==typeof t?""+t:null;for(var o=0;o<n.length;o++){var i=n[o],c=i.getAttribute("data-mce-index");null!==c&&c.length&&-1!==i.className.indexOf("mce-spellchecker-word")&&(c!==t&&null!==t||r.push(i))}return r}function d(e){for(var t=a.length;t--;)if(a[t]===e)return t;return-1}function f(e){for(var t=0,n=a.length;t<n&&!1!==e(a[t],t);t++);return this}function g(e){var t,n,r=u(e?d(e):null);for(t=r.length;t--;)(n=r[t]).parentNode.insertBefore(n.firstChild,n),n.parentNode.removeChild(n);return this}function m(e){var n=u(d(e)),r=t.dom.createRng();return r.setStartBefore(n[0]),r.setEndAfter(n[n.length-1]),r}return o=t.schema.getBlockElements(),i=t.schema.getWhiteSpaceElements(),c=t.schema.getShortEndedElements(),{text:r=function p(e){var t;if(3===e.nodeType)return e.data;if(i[e.nodeName]&&!o[e.nodeName])return"";if(h(e))return"\n";if(t="",(o[e.nodeName]||c[e.nodeName])&&(t+="\n"),e=e.firstChild)for(;t+=p(e),e=e.nextSibling;);return t}(e),matches:a,each:f,filter:function(e){var t=[];return f(function(n,r){e(n,r)&&t.push(n)}),a=t,this},reset:function(){return a.splice(0,a.length),g(),this},matchFromElement:function(e){return a[e.getAttribute("data-mce-index")]},elementFromMatch:function(e){return u(d(e))[0]},find:function(e,t){if(r&&e.global)for(;n=e.exec(r);)a.push(s(n,t));return this},add:function(e,t,n){return a.push({start:e,end:e+t,text:r.substr(e,t),data:n}),this},wrap:function(t){return a.length&&function(e,t,n){var r,a,l,s,u,d=[],f=0,g=e,m=0;(t=t.slice(0)).sort(function(e,t){return e.start-t.start}),u=t.shift();e:for(;;){if((o[g.nodeName]||c[g.nodeName]||h(g))&&f++,3===g.nodeType&&(!a&&g.length+f>=u.end?(a=g,s=u.end-f):r&&d.push(g),!r&&g.length+f>u.start&&(r=g,l=u.start-f),f+=g.length),r&&a){if(g=n({startNode:r,startNodeIndex:l,endNode:a,endNodeIndex:s,innerNodes:d,match:u.text,matchIndex:m}),f-=a.length-s,r=null,a=null,d=[],m++,!(u=t.shift()))break}else if(i[g.nodeName]&&!o[g.nodeName]||!g.firstChild){if(g.nextSibling){g=g.nextSibling;continue}}else if(!h(g)){g=g.firstChild;continue}for(;;){if(g.nextSibling){g=g.nextSibling;break}if(g.parentNode===e)break e;g=g.parentNode}}}(e,a,function(e){function t(t,n){var r=a[n];r.stencil||(r.stencil=e(r));var o=r.stencil.cloneNode(!1);return o.setAttribute("data-mce-index",n),t&&o.appendChild(l.doc.createTextNode(t)),o}return function(e){var n,r,o,i=e.startNode,c=e.endNode,a=e.matchIndex,s=l.doc;if(i===c){var u=i;o=u.parentNode,e.startNodeIndex>0&&(n=s.createTextNode(u.data.substring(0,e.startNodeIndex)),o.insertBefore(n,u));var d=t(e.match,a);return o.insertBefore(d,u),e.endNodeIndex<u.length&&(r=s.createTextNode(u.data.substring(e.endNodeIndex)),o.insertBefore(r,u)),u.parentNode.removeChild(u),d}n=s.createTextNode(i.data.substring(0,e.startNodeIndex)),r=s.createTextNode(c.data.substring(e.endNodeIndex));for(var f=t(i.data.substring(e.startNodeIndex),a),h=[],g=0,m=e.innerNodes.length;g<m;++g){var p=e.innerNodes[g],v=t(p.data,a);p.parentNode.replaceChild(v,p),h.push(v)}var x=t(c.data.substring(0,e.endNodeIndex),a);return(o=i.parentNode).insertBefore(n,i),o.insertBefore(f,i),o.removeChild(i),(o=c.parentNode).insertBefore(x,c),o.insertBefore(r,c),o.removeChild(c),x}}(t)),this},unwrap:g,replace:function(e,n){var r=m(e);return r.deleteContents(),n.length>0&&r.insertNode(t.dom.doc.createTextNode(n)),r},rangeFromMatch:m,indexOf:d}}var m,p=function(e,t){if(!t.get()){var n=g(e.getBody(),e);t.set(n)}return t.get()},v=function(e,t,n,r,o,a,d){var f,h,g,m=c(e);(m||(f=e,h=t,g=n,function(e,t,n,r){var o={method:e,lang:g.get()},c="";o["addToDictionary"===e?"word":"text"]=t,l.each(o,function(e,t){c&&(c+="&"),c+=t+"="+encodeURIComponent(e)}),u.send({url:new s(h).toAbsolute(i(f)),type:"post",content_type:"application/x-www-form-urlencoded",data:c,success:function(e){if(e=JSON.parse(e))e.error?r(e.error):n(e);else{var t=f.translate("Server response wasn't proper JSON.");r(t)}},error:function(){var e=f.translate("The spelling service was not found: (")+i(f)+f.translate(")");r(e)}})})).call(e.plugins.spellchecker,r,o,a,d)},x=function(e,t,n){e.dom.select("span.mce-spellchecker-word").length||N(e,t,n)},N=function(e,t,n){if(p(e,n).reset(),n.set(null),t.get())return t.set(!1),f(e),!0},k=function(e){var t=e.getAttribute("data-mce-index");return"number"==typeof t?""+t:t},y=function(e,t,n,r,o){var i,c;if("string"!=typeof o&&o.words?(c=!!o.dictionary,i=o.words):i=o,e.setProgressState(!1),function(e){for(var t in e)return!1;return!0}(i)){var l=e.translate("No misspellings found.");return e.notificationManager.open({text:l,type:"info"}),void t.set(!1)}r.set({suggestions:i,hasDictionarySupport:c}),p(e,n).find(a(e)).filter(function(e){return!!i[e.text]}).wrap(function(t){return e.dom.create("span",{"class":"mce-spellchecker-word","data-mce-bogus":1,"data-mce-word":t.text})}),t.set(!0),d(e)},S={spellcheck:function(e,t,n,r,o,i){N(e,n,r)||(e.setProgressState(!0),v(e,t,i,"spellcheck",p(e,r).text,function(t){y(e,n,r,o,t)},function(t){e.notificationManager.open({text:t,type:"error"}),e.setProgressState(!1),N(e,n,r)}),e.focus())},checkIfFinished:x,addToDictionary:function(e,t,n,r,o,i,c){e.setProgressState(!0),v(e,t,o,"addToDictionary",i,function(){e.setProgressState(!1),e.dom.remove(c,!0),x(e,n,r)},function(t){e.notificationManager.open({text:t,type:"error"}),e.setProgressState(!1)})},ignoreWord:function(e,t,n,r,o,i){e.selection.collapse(),i?l.each(e.dom.select("span.mce-spellchecker-word"),function(t){t.getAttribute("data-mce-word")===r&&e.dom.remove(t,!0)}):e.dom.remove(o,!0),x(e,t,n)},findSpansByIndex:function(e,t){var n,r=[];if((n=l.toArray(e.getBody().getElementsByTagName("span"))).length)for(var o=0;o<n.length;o++){var i=k(n[o]);null!==i&&i.length&&i===t.toString()&&r.push(n[o])}return r},getElmIndex:k,markErrors:y},w=function(e,t,n,r,i){return{getTextMatcher:function(){return r.get()},getWordCharPattern:function(){return a(e)},markErrors:function(o){S.markErrors(e,t,r,n,o)},getLanguage:function(){return o(e)}}},b=function(e,t,n,r,o,i){e.addCommand("mceSpellCheck",function(){S.spellcheck(e,t,n,r,o,i)})},T=function(e,t,n,i,c,a){var s,u,d,f,h=(d=e,s=l.map(r(d).split(","),function(e){return{name:(e=e.split("="))[0],value:e[1]}}),u=[],l.each(s,function(e){u.push({selectable:!0,text:e.name,data:e.value})}),u),g=function(){S.spellcheck(e,t,n,i,a,c)},m={tooltip:"Spellcheck",onclick:g,onPostRender:function(t){var r=t.control;e.on("SpellcheckStart SpellcheckEnd",function(){r.active(n.get())})}};h.length>1&&(m.type="splitbutton",m.menu=h,m.onshow=(f=e,function(e){var t=o(f);e.control.items().each(function(e){e.active(e.settings.data===t)})}),m.onselect=function(e){c.set(e.control.settings.data)}),e.addButton("spellchecker",m),e.addMenuItem("spellchecker",{text:"Spellcheck",context:"tools",onclick:g,selectable:!0,onPostRender:function(){var t=this;t.active(n.get()),e.on("SpellcheckStart SpellcheckEnd",function(){t.active(n.get())})}})},I=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),E=tinymce.util.Tools.resolve("tinymce.ui.Factory"),P=function(e,t,n,r,o,i){e.on("click",function(c){var a=c.target;if("mce-spellchecker-word"===a.className){c.preventDefault();var s=S.findSpansByIndex(e,S.getElmIndex(a));if(s.length>0){var u=e.dom.createRng();u.setStartBefore(s[0]),u.setEndAfter(s[s.length-1]),e.selection.setRng(u),function(e,t,n,r,o,i,c,a){var s=[],u=n.get().suggestions[c];l.each(u,function(t){s.push({text:t,onclick:function(){e.insertContent(e.dom.encode(t)),e.dom.remove(a),S.checkIfFinished(e,r,o)}})}),s.push({text:"-"}),n.get().hasDictionarySupport&&s.push({text:"Add to Dictionary",onclick:function(){S.addToDictionary(e,t,r,o,i,c,a)}}),s.push.apply(s,[{text:"Ignore",onclick:function(){S.ignoreWord(e,r,o,c,a)}},{text:"Ignore all",onclick:function(){S.ignoreWord(e,r,o,c,a,!0)}}]),(m=E.create("menu",{items:s,context:"contextmenu",onautohide:function(e){-1!==e.target.className.indexOf("spellchecker")&&e.preventDefault()},onhide:function(){m.remove(),m=null}})).renderTo(document.body);var d=I.DOM.getPos(e.getContentAreaContainer()),f=e.dom.getPos(a[0]),h=e.dom.getRoot();"BODY"===h.nodeName?(f.x-=h.ownerDocument.documentElement.scrollLeft||h.scrollLeft,f.y-=h.ownerDocument.documentElement.scrollTop||h.scrollTop):(f.x-=h.scrollLeft,f.y-=h.scrollTop),d.x+=f.x,d.y+=f.y,m.moveTo(d.x,d.y+a[0].offsetHeight)}(e,t,n,r,o,i,a.getAttribute("data-mce-word"),s)}}})};t.add("spellchecker",function(t,r){if(!1===n(t)){var i=e(!1),c=e(o(t)),a=e(null),l=e({});return T(t,r,i,a,c,l),P(t,r,l,i,a,c),b(t,r,i,a,l,c),w(t,i,l,a,r)}})}();
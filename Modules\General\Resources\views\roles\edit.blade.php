@extends('layouts.hound')
@section('content')
    <div class="container">
        <div class="panel panel-default">
            <div class="panel-heading">Edit Role [{{ $role->description }}]</div>
            <div class="panel-body">
                <a href="{{ route('general.roles.index') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
                <br />
                <br />

                @if ($errors->any())
                    <ul class="alert alert-danger">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                @endif

                {!! Form::model($role, [
                    'method' => 'PATCH',
                    'route' => ['general.roles.update', $role->id],
                    'class' => 'form-horizontal',
                    'files' => true
                ]) !!}

                @include('general::roles.form')

                {!! Form::close() !!}

            </div>
        </div>
    </div>
@endsection

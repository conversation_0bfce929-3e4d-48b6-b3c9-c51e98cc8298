/**
 * Super simple wysiwyg editor on Bootstrap v0.5.8
 * http://hackerwins.github.io/summernote/
 *
 * summernote.js
 * Copyright 2013 <PERSON> and outher contributors
 * summernote may be freely distributed under the MIT license./
 *
 * Date: 2014-09-10T07:01Z
 */
(function (factory) {
    /* global define */
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['jquery'], factory);
    } else {
        // Browser globals: jQuery
        factory(window.jQuery);
    }
}(function ($) {



    if ('function' !== typeof Array.prototype.reduce) {
        /**
         * Array.prototype.reduce fallback
         *
         * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/Reduce
         */
        Array.prototype.reduce = function (callback, optInitialValue) {
            var idx, value, length = this.length >>> 0, isValueSet = false;
            if (1 < arguments.length) {
                value = optInitialValue;
                isValueSet = true;
            }
            for (idx = 0; length > idx; ++idx) {
                if (this.hasOwnProperty(idx)) {
                    if (isValueSet) {
                        value = callback(value, this[idx], idx, this);
                    } else {
                        value = this[idx];
                        isValueSet = true;
                    }
                }
            }
            if (!isValueSet) {
                throw new TypeError('Reduce of empty array with no initial value');
            }
            return value;
        };
    }

    var isSupportAmd = typeof define === 'function' && define.amd;

    /**
     * returns whether font is installed or not.
     * @param {String} fontName
     * @return {Boolean}
     */
    var isFontInstalled = function (fontName) {
        var testFontName = fontName === 'Comic Sans MS' ? 'Courier New' : 'Comic Sans MS';
        var $tester = $('<div>').css({
            position: 'absolute',
            left: '-9999px',
            top: '-9999px',
            fontSize: '200px'
        }).text('mmmmmmmmmwwwwwww').appendTo(document.body);

        var originalWidth = $tester.css('fontFamily', testFontName).width();
        var width = $tester.css('fontFamily', fontName + ',' + testFontName).width();

        $tester.remove();

        return originalWidth !== width;
    };

    /**
     * Object which check platform and agent
     */
    var agent = {
        isMac: navigator.appVersion.indexOf('Mac') > -1,
        isMSIE: navigator.userAgent.indexOf('MSIE') > -1 || navigator.userAgent.indexOf('Trident') > -1,
        isFF: navigator.userAgent.indexOf('Firefox') > -1,
        jqueryVersion: parseFloat($.fn.jquery),
        isSupportAmd: isSupportAmd,
        hasCodeMirror: isSupportAmd ? require.specified('CodeMirror') : !!window.CodeMirror,
        isFontInstalled: isFontInstalled,
        isW3CRangeSupport: !!document.createRange
    };

    /**
     * func utils (for high-order func's arg)
     */
    var func = (function () {
        var eq = function (itemA) {
            return function (itemB) {
                return itemA === itemB;
            };
        };

        var eq2 = function (itemA, itemB) {
            return itemA === itemB;
        };

        var ok = function () {
            return true;
        };

        var fail = function () {
            return false;
        };

        var not = function (f) {
            return function () {
                return !f.apply(f, arguments);
            };
        };

        var and = function (fA, fB) {
            return function (item) {
                return fA(item) && fB(item);
            };
        };

        var self = function (a) {
            return a;
        };

        var idCounter = 0;

        /**
         * generate a globally-unique id
         *
         * @param {String} [prefix]
         */
        var uniqueId = function (prefix) {
            var id = ++idCounter + '';
            return prefix ? prefix + id : id;
        };

        /**
         * returns bnd (bounds) from rect
         *
         * - IE Compatability Issue: http://goo.gl/sRLOAo
         * - Scroll Issue: http://goo.gl/sNjUc
         *
         * @param {Rect} rect
         * @return {Object} bounds
         * @return {Number} bounds.top
         * @return {Number} bounds.left
         * @return {Number} bounds.width
         * @return {Number} bounds.height
         */
        var rect2bnd = function (rect) {
            var $document = $(document);
            return {
                top: rect.top + $document.scrollTop(),
                left: rect.left + $document.scrollLeft(),
                width: rect.right - rect.left,
                height: rect.bottom - rect.top
            };
        };

        /**
         * returns a copy of the object where the keys have become the values and the values the keys.
         * @param {Object} obj
         * @return {Object}
         */
        var invertObject = function (obj) {
            var inverted = {};
            for (var key in obj) {
                if (obj.hasOwnProperty(key)) {
                    inverted[obj[key]] = key;
                }
            }
            return inverted;
        };

        return {
            eq: eq,
            eq2: eq2,
            ok: ok,
            fail: fail,
            self: self,
            not: not,
            and: and,
            uniqueId: uniqueId,
            rect2bnd: rect2bnd,
            invertObject: invertObject
        };
    })();

    /**
     * list utils
     */
    var list = (function () {
        /**
         * returns the first element of an array.
         * @param {Array} array
         */
        var head = function (array) {
            return array[0];
        };

        /**
         * returns the last element of an array.
         * @param {Array} array
         */
        var last = function (array) {
            return array[array.length - 1];
        };

        /**
         * returns everything but the last entry of the array.
         * @param {Array} array
         */
        var initial = function (array) {
            return array.slice(0, array.length - 1);
        };

        /**
         * returns the rest of the elements in an array.
         * @param {Array} array
         */
        var tail = function (array) {
            return array.slice(1);
        };

        /**
         * returns next item.
         * @param {Array} array
         */
        var next = function (array, item) {
            var idx = array.indexOf(item);
            if (idx === -1) { return null; }

            return array[idx + 1];
        };

        /**
         * returns prev item.
         * @param {Array} array
         */
        var prev = function (array, item) {
            var idx = array.indexOf(item);
            if (idx === -1) { return null; }

            return array[idx - 1];
        };

        var all = function (array, pred) {
            for (var idx = 0, len = array.length; idx < len; idx++) {
                if (!pred(array[idx])) {
                    return false;
                }
            }
            return true;
        };

        var contains = function (array, item) {
            return array.indexOf(item) !== -1;
        };

        /**
         * get sum from a list
         * @param {Array} array - array
         * @param {Function} fn - iterator
         */
        var sum = function (array, fn) {
            fn = fn || func.self;
            return array.reduce(function (memo, v) {
                return memo + fn(v);
            }, 0);
        };

        /**
         * returns a copy of the collection with array type.
         * @param {Collection} collection - collection eg) node.childNodes, ...
         */
        var from = function (collection) {
            var result = [], idx = -1, length = collection.length;
            while (++idx < length) {
                result[idx] = collection[idx];
            }
            return result;
        };

        /**
         * cluster elements by predicate function.
         * @param {Array} array - array
         * @param {Function} fn - predicate function for cluster rule
         * @param {Array[]}
         */
        var clusterBy = function (array, fn) {
            if (!array.length) { return []; }
            var aTail = tail(array);
            return aTail.reduce(function (memo, v) {
                var aLast = last(memo);
                if (fn(last(aLast), v)) {
                    aLast[aLast.length] = v;
                } else {
                    memo[memo.length] = [v];
                }
                return memo;
            }, [[head(array)]]);
        };

        /**
         * returns a copy of the array with all falsy values removed
         * @param {Array} array - array
         * @param {Function} fn - predicate function for cluster rule
         */
        var compact = function (array) {
            var aResult = [];
            for (var idx = 0, len = array.length; idx < len; idx++) {
                if (array[idx]) { aResult.push(array[idx]); }
            }
            return aResult;
        };

        var unique = function (array) {
            var results = [];

            for (var idx = 0, len = array.length; idx < len; idx++) {
                if (results.indexOf(array[idx]) === -1) {
                    results.push(array[idx]);
                }
            }

            return results;
        };

        return {
            head: head, last: last, initial: initial, tail: tail,
            prev: prev, next: next, contains: contains,
            all: all, sum: sum, from: from,
            clusterBy: clusterBy, compact: compact, unique: unique
        };
    })();


    var NBSP_CHAR = String.fromCharCode(160);
    var ZERO_WIDTH_NBSP_CHAR = '\ufeff';

    /**
     * Dom functions
     */
    var dom = (function () {
        /**
         * returns whether node is `note-editable` or not.
         *
         * @param {Node} node
         * @return {Boolean}
         */
        var isEditable = function (node) {
            return node && $(node).hasClass('note-editable');
        };

        /**
         * returns whether node is `note-control-sizing` or not.
         *
         * @param {Node} node
         * @return {Boolean}
         */
        var isControlSizing = function (node) {
            return node && $(node).hasClass('note-control-sizing');
        };

        /**
         * build layoutInfo from $editor(.note-editor)
         *
         * @param {jQuery} $editor
         * @return {Object}
         */
        var buildLayoutInfo = function ($editor) {
            var makeFinder;

            // air mode
            if ($editor.hasClass('note-air-editor')) {
                var id = list.last($editor.attr('id').split('-'));
                makeFinder = function (sIdPrefix) {
                    return function () { return $(sIdPrefix + id); };
                };

                return {
                    editor: function () { return $editor; },
                    editable: function () { return $editor; },
                    popover: makeFinder('#note-popover-'),
                    handle: makeFinder('#note-handle-'),
                    dialog: makeFinder('#note-dialog-')
                };

                // frame mode
            } else {
                makeFinder = function (sClassName) {
                    return function () { return $editor.find(sClassName); };
                };
                return {
                    editor: function () { return $editor; },
                    dropzone: makeFinder('.note-dropzone'),
                    toolbar: makeFinder('.note-toolbar'),
                    editable: makeFinder('.note-editable'),
                    codable: makeFinder('.note-codable'),
                    statusbar: makeFinder('.note-statusbar'),
                    popover: makeFinder('.note-popover'),
                    handle: makeFinder('.note-handle'),
                    dialog: makeFinder('.note-dialog')
                };
            }
        };

        /**
         * returns predicate which judge whether nodeName is same
         * @param {String} sNodeName
         */
        var makePredByNodeName = function (sNodeName) {
            sNodeName = sNodeName.toUpperCase();
            return function (node) {
                return node && node.nodeName.toUpperCase() === sNodeName;
            };
        };

        var isText = function (node) {
            return node && node.nodeType === 3;
        };

        /**
         * ex) br, col, embed, hr, img, input, ...
         * @see http://www.w3.org/html/wg/drafts/html/master/syntax.html#void-elements
         */
        var isVoid = function (node) {
            return node && /^BR|^IMG|^HR/.test(node.nodeName.toUpperCase());
        };

        var isPara = function (node) {
            if (isEditable(node)) {
                return false;
            }

            // Chrome(v31.0), FF(v25.0.1) use DIV for paragraph
            return node && /^DIV|^P|^LI|^H[1-7]/.test(node.nodeName.toUpperCase());
        };

        var isInline = function (node) {
            return !isBodyContainer(node) && !isList(node) && !isPara(node);
        };

        var isList = function (node) {
            return node && /^UL|^OL/.test(node.nodeName.toUpperCase());
        };

        var isCell = function (node) {
            return node && /^TD|^TH/.test(node.nodeName.toUpperCase());
        };

        var isBlockquote = makePredByNodeName('BLOCKQUOTE');

        var isBodyContainer = function (node) {
            return isCell(node) || isBlockquote(node) || isEditable(node);
        };

        var isAnchor = makePredByNodeName('A');

        var isParaInline = function (node) {
            return isInline(node) && !!ancestor(node, isPara);
        };

        var isBodyInline = function (node) {
            return isInline(node) && !ancestor(node, isPara);
        };

        /**
         * blank HTML for cursor position
         */
        var blankHTML = agent.isMSIE ? '&nbsp;' : '<br>';

        /**
         * returns #text's text size or element's childNodes size
         *
         * @param {Node} node
         */
        var nodeLength = function (node) {
            if (isText(node)) {
                return node.nodeValue.length;
            }

            return node.childNodes.length;
        };

        /**
         * returns whether node is empty or not.
         *
         * @param {Node} node
         * @return {Boolean}
         */
        var isEmpty = function (node) {
            var len = nodeLength(node);

            if (len === 0) {
                return true;
            } else if (!dom.isText(node) && len === 1 && node.innerHTML === blankHTML) {
                // ex) <p><br></p>, <span><br></span>
                return true;
            }

            return false;
        };

        /**
         * padding blankHTML if node is empty (for cursor position)
         */
        var paddingBlankHTML = function (node) {
            if (!isVoid(node) && !nodeLength(node)) {
                node.innerHTML = blankHTML;
            }
        };

        /**
         * find nearest ancestor predicate hit
         *
         * @param {Node} node
         * @param {Function} pred - predicate function
         */
        var ancestor = function (node, pred) {
            while (node) {
                if (pred(node)) { return node; }
                if (isEditable(node)) { break; }

                node = node.parentNode;
            }
            return null;
        };

        /**
         * returns new array of ancestor nodes (until predicate hit).
         *
         * @param {Node} node
         * @param {Function} [optional] pred - predicate function
         */
        var listAncestor = function (node, pred) {
            pred = pred || func.fail;

            var ancestors = [];
            ancestor(node, function (el) {
                if (!isEditable(el)) {
                    ancestors.push(el);
                }

                return pred(el);
            });
            return ancestors;
        };

        /**
         * returns common ancestor node between two nodes.
         *
         * @param {Node} nodeA
         * @param {Node} nodeB
         */
        var commonAncestor = function (nodeA, nodeB) {
            var ancestors = listAncestor(nodeA);
            for (var n = nodeB; n; n = n.parentNode) {
                if ($.inArray(n, ancestors) > -1) { return n; }
            }
            return null; // difference document area
        };

        /**
         * listing all previous siblings (until predicate hit).
         *
         * @param {Node} node
         * @param {Function} [optional] pred - predicate function
         */
        var listPrev = function (node, pred) {
            pred = pred || func.fail;

            var nodes = [];
            while (node) {
                if (pred(node)) { break; }
                nodes.push(node);
                node = node.previousSibling;
            }
            return nodes;
        };

        /**
         * listing next siblings (until predicate hit).
         *
         * @param {Node} node
         * @param {Function} [pred] - predicate function
         */
        var listNext = function (node, pred) {
            pred = pred || func.fail;

            var nodes = [];
            while (node) {
                if (pred(node)) { break; }
                nodes.push(node);
                node = node.nextSibling;
            }
            return nodes;
        };

        /**
         * listing descendant nodes
         *
         * @param {Node} node
         * @param {Function} [pred] - predicate function
         */
        var listDescendant = function (node, pred) {
            var descendents = [];
            pred = pred || func.ok;

            // start DFS(depth first search) with node
            (function fnWalk(current) {
                if (node !== current && pred(current)) {
                    descendents.push(current);
                }
                for (var idx = 0, len = current.childNodes.length; idx < len; idx++) {
                    fnWalk(current.childNodes[idx]);
                }
            })(node);

            return descendents;
        };

        /**
         * wrap node with new tag.
         *
         * @param {Node} node
         * @param {Node} tagName of wrapper
         * @return {Node} - wrapper
         */
        var wrap = function (node, wrapperName) {
            var parent = node.parentNode;
            var wrapper = $('<' + wrapperName + '>')[0];

            parent.insertBefore(wrapper, node);
            wrapper.appendChild(node);

            return wrapper;
        };

        /**
         * insert node after preceding
         *
         * @param {Node} node
         * @param {Node} preceding - predicate function
         */
        var insertAfter = function (node, preceding) {
            var next = preceding.nextSibling, parent = preceding.parentNode;
            if (next) {
                parent.insertBefore(node, next);
            } else {
                parent.appendChild(node);
            }
            return node;
        };

        /**
         * append elements.
         *
         * @param {Node} node
         * @param {Collection} aChild
         */
        var appendChildNodes = function (node, aChild) {
            $.each(aChild, function (idx, child) {
                node.appendChild(child);
            });
            return node;
        };

        /**
         * returns whether boundaryPoint is left edge or not.
         *
         * @param {BoundaryPoint} point
         * @return {Boolean}
         */
        var isLeftEdgePoint = function (point) {
            return point.offset === 0;
        };

        /**
         * returns whether boundaryPoint is right edge or not.
         *
         * @param {BoundaryPoint} point
         * @return {Boolean}
         */
        var isRightEdgePoint = function (point) {
            return point.offset === nodeLength(point.node);
        };

        /**
         * returns whether boundaryPoint is edge or not.
         *
         * @param {BoundaryPoint} point
         * @return {Boolean}
         */
        var isEdgePoint = function (point) {
            return isLeftEdgePoint(point) || isRightEdgePoint(point);
        };

        /**
         * returns whether node is right edge of ancestor or not.
         *
         * @param {Node} node
         * @param {Node} ancestor
         * @return {Boolean}
         */
        var isRightEdgeOf = function (node, ancestor) {
            while (node && node !== ancestor) {
                if (position(node) !== nodeLength(node.parentNode) - 1) {
                    return false;
                }
                node = node.parentNode;
            }

            return true;
        };

        /**
         * returns offset from parent.
         *
         * @param {Node} node
         */
        var position = function (node) {
            var offset = 0;
            while ((node = node.previousSibling)) {
                offset += 1;
            }
            return offset;
        };

        var hasChildren = function (node) {
            return !!(node && node.childNodes && node.childNodes.length);
        };

        /**
         * returns previous boundaryPoint
         *
         * @param {BoundaryPoint} point
         * @param {Boolean} isSkipInnerOffset
         * @return {BoundaryPoint}
         */
        var prevPoint = function (point, isSkipInnerOffset) {
            var node, offset;

            if (point.offset === 0) {
                if (isEditable(point.node)) {
                    return null;
                }

                node = point.node.parentNode;
                offset = position(point.node);
            } else if (hasChildren(point.node)) {
                node = point.node.childNodes[offset - 1];
                offset = nodeLength(node);
            } else {
                node = point.node;
                offset = isSkipInnerOffset ? 0 : point.offset - 1;
            }

            return {
                node: node,
                offset: offset
            };
        };

        /**
         * returns next boundaryPoint
         *
         * @param {BoundaryPoint} point
         * @param {Boolean} isSkipInnerOffset
         * @return {BoundaryPoint}
         */
        var nextPoint = function (point, isSkipInnerOffset) {
            var node, offset;

            if (nodeLength(point.node) === point.offset) {
                if (isEditable(point.node)) {
                    return null;
                }

                node = point.node.parentNode;
                offset = position(point.node) + 1;
            } else if (hasChildren(point.node)) {
                node = point.node.childNodes[point.offset];
                offset = 0;
            } else {
                node = point.node;
                offset = isSkipInnerOffset ? nodeLength(point.node) : point.offset + 1;
            }

            return {
                node: node,
                offset: offset
            };
        };

        /**
         * returns whether pointA and pointB is same or not.
         *
         * @param {BoundaryPoint} pointA
         * @param {BoundaryPoint} pointB
         * @return {Boolean}
         */
        var isSamePoint = function (pointA, pointB) {
            return pointA.node === pointB.node && pointA.offset === pointB.offset;
        };

        /**
         * returns whether point is visible (can set cursor) or not.
         * 
         * @param {BoundaryPoint} point
         * @return {Boolean}
         */
        var isVisiblePoint = function (point) {
            return isText(point.node) ||
                !hasChildren(point.node) ||
                isEmpty(point.node) ||
                !isEdgePoint(point);
        };

        /**
         * @param {BoundaryPoint} point
         * @param {Function} pred
         * @return {BoundaryPoint}
         */
        var prevPointUntil = function (point, pred) {
            while (point) {
                if (pred(point)) {
                    return point;
                }

                point = prevPoint(point);
            }

            return null;
        };

        /**
         * @param {BoundaryPoint} point
         * @param {Function} pred
         * @return {BoundaryPoint}
         */
        var nextPointUntil = function (point, pred) {
            while (point) {
                if (pred(point)) {
                    return point;
                }

                point = nextPoint(point);
            }

            return null;
        };

        /**
         * @param {BoundaryPoint} startPoint
         * @param {BoundaryPoint} endPoint
         * @param {Function} handler
         * @param {Boolean} isSkipInnerOffset
         */
        var walkPoint = function (startPoint, endPoint, handler, isSkipInnerOffset) {
            var point = startPoint;

            while (point) {
                handler(point);

                if (isSamePoint(point, endPoint)) {
                    break;
                }

                var isSkipOffset = isSkipInnerOffset && startPoint.node !== point.node;
                point = nextPoint(point, isSkipOffset);
            }
        };

        /**
         * return offsetPath(array of offset) from ancestor
         *
         * @param {Node} ancestor - ancestor node
         * @param {Node} node
         */
        var makeOffsetPath = function (ancestor, node) {
            var ancestors = list.initial(listAncestor(node, func.eq(ancestor)));
            return $.map(ancestors, position).reverse();
        };

        /**
         * return element from offsetPath(array of offset)
         *
         * @param {Node} ancestor - ancestor node
         * @param {array} aOffset - offsetPath
         */
        var fromOffsetPath = function (ancestor, aOffset) {
            var current = ancestor;
            for (var i = 0, len = aOffset.length; i < len; i++) {
                current = current.childNodes[aOffset[i]];
            }
            return current;
        };

        /**
         * split element or #text
         *
         * @param {BoundaryPoint} point
         * @return {Node} right node of boundaryPoint
         */
        var splitNode = function (point) {
            // split #text
            if (isText(point.node)) {
                // edge case
                if (isLeftEdgePoint(point)) {
                    return point.node;
                } else if (isRightEdgePoint(point)) {
                    return point.node.nextSibling;
                }

                return point.node.splitText(point.offset);
            }

            // split element
            var childNode = point.node.childNodes[point.offset];
            var clone = insertAfter(point.node.cloneNode(false), point.node);
            appendChildNodes(clone, listNext(childNode));

            paddingBlankHTML(point.node);
            paddingBlankHTML(clone);

            return clone;
        };

        /**
         * split tree by point
         *
         * @param {Node} root - split root
         * @param {BoundaryPoint} point
         * @return {Node} right node of boundaryPoint
         */
        var splitTree = function (root, point) {
            // ex) [#text, <span>, <p>]
            var ancestors = listAncestor(point.node, func.eq(root));

            if (!ancestors.length) {
                return null;
            } else if (ancestors.length === 1) {
                return splitNode(point);
            }

            return ancestors.reduce(function (node, parent) {
                var clone = insertAfter(parent.cloneNode(false), parent);

                if (node === point.node) {
                    node = splitNode(point);
                }

                appendChildNodes(clone, listNext(node));

                paddingBlankHTML(parent);
                paddingBlankHTML(clone);
                return clone;
            });
        };

        var createText = function (text) {
            return document.createTextNode(text);
        };

        /**
         * remove node, (isRemoveChild: remove child or not)
         * @param {Node} node
         * @param {Boolean} isRemoveChild
         */
        var remove = function (node, isRemoveChild) {
            if (!node || !node.parentNode) { return; }
            if (node.removeNode) { return node.removeNode(isRemoveChild); }

            var parent = node.parentNode;
            if (!isRemoveChild) {
                var nodes = [];
                var i, len;
                for (i = 0, len = node.childNodes.length; i < len; i++) {
                    nodes.push(node.childNodes[i]);
                }

                for (i = 0, len = nodes.length; i < len; i++) {
                    parent.insertBefore(nodes[i], node);
                }
            }

            parent.removeChild(node);
        };

        var isTextarea = makePredByNodeName('TEXTAREA');

        /**
         * get the HTML contents of node 
         *
         * @param {jQuery} $node
         * @param {Boolean} [isNewlineOnBlock]
         */
        var html = function ($node, isNewlineOnBlock) {
            var markup = isTextarea($node[0]) ? $node.val() : $node.html();

            if (isNewlineOnBlock) {
                var regexTag = /<(\/?)(\b(?!!)[^>\s]*)(.*?)(\s*\/?>)/g;
                markup = markup.replace(regexTag, function (match, endSlash, name) {
                    name = name.toUpperCase();
                    var isEndOfInlineContainer = /^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(name) &&
                        !!endSlash;
                    var isBlockNode = /^TABLE|^TBODY|^TR|^HR|^UL/.test(name);

                    return match + ((isEndOfInlineContainer || isBlockNode) ? '\n' : '');
                });
                markup = $.trim(markup);
            }

            return markup;
        };

        var value = function ($textarea) {
            var val = $textarea.val();
            // strip line breaks
            return val.replace(/[\n\r]/g, '');
        };

        return {
            NBSP_CHAR: NBSP_CHAR,
            ZERO_WIDTH_NBSP_CHAR: ZERO_WIDTH_NBSP_CHAR,
            blank: blankHTML,
            emptyPara: '<p>' + blankHTML + '</p>',
            isEditable: isEditable,
            isControlSizing: isControlSizing,
            buildLayoutInfo: buildLayoutInfo,
            isText: isText,
            isPara: isPara,
            isInline: isInline,
            isBodyInline: isBodyInline,
            isParaInline: isParaInline,
            isList: isList,
            isTable: makePredByNodeName('TABLE'),
            isCell: isCell,
            isBlockquote: isBlockquote,
            isBodyContainer: isBodyContainer,
            isAnchor: isAnchor,
            isDiv: makePredByNodeName('DIV'),
            isLi: makePredByNodeName('LI'),
            isSpan: makePredByNodeName('SPAN'),
            isB: makePredByNodeName('B'),
            isU: makePredByNodeName('U'),
            isS: makePredByNodeName('S'),
            isI: makePredByNodeName('I'),
            isImg: makePredByNodeName('IMG'),
            isTextarea: isTextarea,
            isEmpty: isEmpty,
            isEmptyAnchor: func.and(isAnchor, isEmpty),
            nodeLength: nodeLength,
            isLeftEdgePoint: isLeftEdgePoint,
            isRightEdgePoint: isRightEdgePoint,
            isEdgePoint: isEdgePoint,
            isRightEdgeOf: isRightEdgeOf,
            prevPoint: prevPoint,
            nextPoint: nextPoint,
            isSamePoint: isSamePoint,
            isVisiblePoint: isVisiblePoint,
            prevPointUntil: prevPointUntil,
            nextPointUntil: nextPointUntil,
            walkPoint: walkPoint,
            ancestor: ancestor,
            listAncestor: listAncestor,
            listNext: listNext,
            listPrev: listPrev,
            listDescendant: listDescendant,
            commonAncestor: commonAncestor,
            wrap: wrap,
            insertAfter: insertAfter,
            appendChildNodes: appendChildNodes,
            position: position,
            hasChildren: hasChildren,
            makeOffsetPath: makeOffsetPath,
            fromOffsetPath: fromOffsetPath,
            splitTree: splitTree,
            createText: createText,
            remove: remove,
            html: html,
            value: value
        };
    })();

    var settings = {
        // version
        version: '0.5.8',

        /**
         * options
         */
        options: {
            width: null,                  // set editor width
            height: null,                 // set editor height, ex) 300

            minHeight: null,              // set minimum height of editor
            maxHeight: null,              // set maximum height of editor

            focus: false,                 // set focus to editable area after initializing summernote

            tabsize: 4,                   // size of tab ex) 2 or 4
            styleWithSpan: true,          // style with span (Chrome and FF only)

            disableLinkTarget: false,     // hide link Target Checkbox
            disableDragAndDrop: false,    // disable drag and drop event
            disableResizeEditor: false,   // disable resizing editor

            codemirror: {                 // codemirror options
                mode: 'text/html',
                htmlMode: true,
                lineNumbers: true
            },

            // language
            lang: 'en-US',                // language 'en-US', 'ko-KR', ...
            direction: null,              // text direction, ex) 'rtl'

            // toolbar
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'italic', 'underline', 'superscript', 'subscript', 'strikethrough', 'clear']],
                ['fontname', ['fontname']],
                // ['fontsize', ['fontsize']], // Still buggy
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['height', ['height']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video', 'hr']],
                ['view', ['fullscreen', 'codeview']],
                ['help', ['help']]
            ],

            // air mode: inline editor
            airMode: false,
            // airPopover: [
            //   ['style', ['style']],
            //   ['font', ['bold', 'italic', 'underline', 'clear']],
            //   ['fontname', ['fontname']],
            //   ['fontsize', ['fontsize']], // Still buggy
            //   ['color', ['color']],
            //   ['para', ['ul', 'ol', 'paragraph']],
            //   ['height', ['height']],
            //   ['table', ['table']],
            //   ['insert', ['link', 'picture', 'video']],
            //   ['help', ['help']]
            // ],
            airPopover: [
                ['color', ['color']],
                ['font', ['bold', 'underline', 'clear']],
                ['para', ['ul', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture']]
            ],

            // style tag
            styleTags: ['p', 'blockquote', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],

            // default fontName
            defaultFontName: 'Helvetica Neue',

            // fontName
            fontNames: [
                'Arial', 'Arial Black', 'Comic Sans MS', 'Courier New',
                'Helvetica Neue', 'Impact', 'Lucida Grande',
                'Tahoma', 'Times New Roman', 'Verdana'
            ],

            // pallete colors(n x n)
            colors: [
                ['#000000', '#424242', '#636363', '#9C9C94', '#CEC6CE', '#EFEFEF', '#F7F7F7', '#FFFFFF'],
                ['#FF0000', '#FF9C00', '#FFFF00', '#00FF00', '#00FFFF', '#0000FF', '#9C00FF', '#FF00FF'],
                ['#F7C6CE', '#FFE7CE', '#FFEFC6', '#D6EFD6', '#CEDEE7', '#CEE7F7', '#D6D6E7', '#E7D6DE'],
                ['#E79C9C', '#FFC69C', '#FFE79C', '#B5D6A5', '#A5C6CE', '#9CC6EF', '#B5A5D6', '#D6A5BD'],
                ['#E76363', '#F7AD6B', '#FFD663', '#94BD7B', '#73A5AD', '#6BADDE', '#8C7BC6', '#C67BA5'],
                ['#CE0000', '#E79439', '#EFC631', '#6BA54A', '#4A7B8C', '#3984C6', '#634AA5', '#A54A7B'],
                ['#9C0000', '#B56308', '#BD9400', '#397B21', '#104A5A', '#085294', '#311873', '#731842'],
                ['#630000', '#7B3900', '#846300', '#295218', '#083139', '#003163', '#21104A', '#4A1031']
            ],

            // fontSize
            fontSizes: ['8', '9', '10', '11', '12', '14', '18', '24', '36'],

            // lineHeight
            lineHeights: ['1.0', '1.2', '1.4', '1.5', '1.6', '1.8', '2.0', '3.0'],

            // insertTable max size
            insertTableMaxSize: {
                col: 10,
                row: 10
            },

            // callbacks
            oninit: null,             // initialize
            onfocus: null,            // editable has focus
            onblur: null,             // editable out of focus
            onenter: null,            // enter key pressed
            onkeyup: null,            // keyup
            onkeydown: null,          // keydown
            onImageUpload: null,      // imageUpload
            onImageUploadError: null, // imageUploadError
            onToolbarClick: null,

            /**
             * manipulate link address when user create link
             * @param {String} sLinkUrl
             * @return {String}
             */
            onCreateLink: function (sLinkUrl) {
                if (sLinkUrl.indexOf('@') !== -1 && sLinkUrl.indexOf(':') === -1) {
                    sLinkUrl = 'mailto:' + sLinkUrl;
                } else if (sLinkUrl.indexOf('://') === -1) {
                    sLinkUrl = 'https://' + sLinkUrl;
                }

                return sLinkUrl;
            },

            keyMap: {
                pc: {
                    'ENTER': 'insertParagraph',
                    'CTRL+Z': 'undo',
                    'CTRL+Y': 'redo',
                    'TAB': 'tab',
                    'SHIFT+TAB': 'untab',
                    'CTRL+B': 'bold',
                    'CTRL+I': 'italic',
                    'CTRL+U': 'underline',
                    'CTRL+SHIFT+S': 'strikethrough',
                    'CTRL+BACKSLASH': 'removeFormat',
                    'CTRL+SHIFT+L': 'justifyLeft',
                    'CTRL+SHIFT+E': 'justifyCenter',
                    'CTRL+SHIFT+R': 'justifyRight',
                    'CTRL+SHIFT+J': 'justifyFull',
                    'CTRL+SHIFT+NUM7': 'insertUnorderedList',
                    'CTRL+SHIFT+NUM8': 'insertOrderedList',
                    'CTRL+LEFTBRACKET': 'outdent',
                    'CTRL+RIGHTBRACKET': 'indent',
                    'CTRL+NUM0': 'formatPara',
                    'CTRL+NUM1': 'formatH1',
                    'CTRL+NUM2': 'formatH2',
                    'CTRL+NUM3': 'formatH3',
                    'CTRL+NUM4': 'formatH4',
                    'CTRL+NUM5': 'formatH5',
                    'CTRL+NUM6': 'formatH6',
                    'CTRL+ENTER': 'insertHorizontalRule',
                    'CTRL+K': 'showLinkDialog'
                },

                mac: {
                    'ENTER': 'insertParagraph',
                    'CMD+Z': 'undo',
                    'CMD+SHIFT+Z': 'redo',
                    'TAB': 'tab',
                    'SHIFT+TAB': 'untab',
                    'CMD+B': 'bold',
                    'CMD+I': 'italic',
                    'CMD+U': 'underline',
                    'CMD+SHIFT+S': 'strikethrough',
                    'CMD+BACKSLASH': 'removeFormat',
                    'CMD+SHIFT+L': 'justifyLeft',
                    'CMD+SHIFT+E': 'justifyCenter',
                    'CMD+SHIFT+R': 'justifyRight',
                    'CMD+SHIFT+J': 'justifyFull',
                    'CMD+SHIFT+NUM7': 'insertUnorderedList',
                    'CMD+SHIFT+NUM8': 'insertOrderedList',
                    'CMD+LEFTBRACKET': 'outdent',
                    'CMD+RIGHTBRACKET': 'indent',
                    'CMD+NUM0': 'formatPara',
                    'CMD+NUM1': 'formatH1',
                    'CMD+NUM2': 'formatH2',
                    'CMD+NUM3': 'formatH3',
                    'CMD+NUM4': 'formatH4',
                    'CMD+NUM5': 'formatH5',
                    'CMD+NUM6': 'formatH6',
                    'CMD+ENTER': 'insertHorizontalRule',
                    'CMD+K': 'showLinkDialog'
                }
            }
        },

        // default language: en-US
        lang: {
            'en-US': {
                font: {
                    bold: 'Bold',
                    italic: 'Italic',
                    underline: 'Underline',
                    strikethrough: 'Strikethrough',
                    subscript: 'Subscript',
                    superscript: 'Superscript',
                    clear: 'Remove Font Style',
                    height: 'Line Height',
                    name: 'Font Family',
                    size: 'Font Size'
                },
                image: {
                    image: 'Picture',
                    insert: 'Insert Image',
                    resizeFull: 'Resize Full',
                    resizeHalf: 'Resize Half',
                    resizeQuarter: 'Resize Quarter',
                    floatLeft: 'Float Left',
                    floatRight: 'Float Right',
                    floatNone: 'Float None',
                    dragImageHere: 'Drag an image here',
                    selectFromFiles: 'Select from files',
                    link: 'Link',
                    insertLink: 'Link Image',
                    unlink: 'Unlink',
                    url: 'Image URL',
                    remove: 'Remove Image'
                },
                link: {
                    link: 'Link',
                    insert: 'Insert Link',
                    unlink: 'Unlink',
                    edit: 'Edit',
                    textToDisplay: 'Text to display',
                    url: 'To what URL should this link go?',
                    openInNewWindow: 'Open in new window'
                },
                video: {
                    video: 'Video',
                    videoLink: 'Video Link',
                    insert: 'Insert Video',
                    url: 'Video URL?',
                    providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)'
                },
                table: {
                    table: 'Table'
                },
                hr: {
                    insert: 'Insert Horizontal Rule'
                },
                style: {
                    style: 'Style',
                    normal: 'Normal',
                    blockquote: 'Quote',
                    pre: 'Code',
                    h1: 'Header 1',
                    h2: 'Header 2',
                    h3: 'Header 3',
                    h4: 'Header 4',
                    h5: 'Header 5',
                    h6: 'Header 6'
                },
                lists: {
                    unordered: 'Unordered list',
                    ordered: 'Ordered list'
                },
                options: {
                    help: 'Help',
                    fullscreen: 'Full Screen',
                    codeview: 'Code View'
                },
                paragraph: {
                    paragraph: 'Paragraph',
                    outdent: 'Outdent',
                    indent: 'Indent',
                    left: 'Align left',
                    center: 'Align center',
                    right: 'Align right',
                    justify: 'Justify full'
                },
                color: {
                    recent: 'Recent Color',
                    more: 'More Color',
                    background: 'Background Color',
                    foreground: 'Foreground Color',
                    transparent: 'Transparent',
                    setTransparent: 'Set transparent',
                    reset: 'Reset',
                    resetToDefault: 'Reset to default'
                },
                shortcut: {
                    shortcuts: 'Keyboard shortcuts',
                    close: 'Close',
                    textFormatting: 'Text formatting',
                    action: 'Action',
                    paragraphFormatting: 'Paragraph formatting',
                    documentStyle: 'Document Style'
                },
                history: {
                    undo: 'Undo',
                    redo: 'Redo'
                }
            }
        }
    };

    /**
     * Async functions which returns `Promise`
     */
    var async = (function () {
        /**
         * read contents of file as representing URL
         *
         * @param {File} file
         * @return {Promise} - then: sDataUrl
         */
        var readFileAsDataURL = function (file) {
            return $.Deferred(function (deferred) {
                $.extend(new FileReader(), {
                    onload: function (e) {
                        var sDataURL = e.target.result;
                        deferred.resolve(sDataURL);
                    },
                    onerror: function () {
                        deferred.reject(this);
                    }
                }).readAsDataURL(file);
            }).promise();
        };

        /**
         * create `<image>` from url string
         *
         * @param {String} sUrl
         * @return {Promise} - then: $image
         */
        var createImage = function (sUrl, filename) {
            return $.Deferred(function (deferred) {
                $('<img>').one('load', function () {
                    deferred.resolve($(this));
                }).one('error abort', function () {
                    deferred.reject($(this));
                }).css({
                    display: 'none'
                }).appendTo(document.body)
                    .attr('src', sUrl)
                    .attr('data-filename', filename);
            }).promise();
        };

        return {
            readFileAsDataURL: readFileAsDataURL,
            createImage: createImage
        };
    })();

    /**
     * Object for keycodes.
     */
    var key = {
        isEdit: function (keyCode) {
            return [8, 9, 13, 32].indexOf(keyCode) !== -1;
        },
        nameFromCode: {
            '8': 'BACKSPACE',
            '9': 'TAB',
            '13': 'ENTER',
            '32': 'SPACE',

            // Number: 0-9
            '48': 'NUM0',
            '49': 'NUM1',
            '50': 'NUM2',
            '51': 'NUM3',
            '52': 'NUM4',
            '53': 'NUM5',
            '54': 'NUM6',
            '55': 'NUM7',
            '56': 'NUM8',

            // Alphabet: a-z
            '66': 'B',
            '69': 'E',
            '73': 'I',
            '74': 'J',
            '75': 'K',
            '76': 'L',
            '82': 'R',
            '83': 'S',
            '85': 'U',
            '89': 'Y',
            '90': 'Z',

            '191': 'SLASH',
            '219': 'LEFTBRACKET',
            '220': 'BACKSLASH',
            '221': 'RIGHTBRACKET'
        }
    };

    /**
     * Style
     * @class
     */
    var Style = function () {
        /**
         * passing an array of style properties to .css()
         * will result in an object of property-value pairs.
         * (compability with version < 1.9)
         *
         * @param  {jQuery} $obj
         * @param  {Array} propertyNames - An array of one or more CSS properties.
         * @returns {Object}
         */
        var jQueryCSS = function ($obj, propertyNames) {
            if (agent.jqueryVersion < 1.9) {
                var result = {};
                $.each(propertyNames, function (idx, propertyName) {
                    result[propertyName] = $obj.css(propertyName);
                });
                return result;
            }
            return $obj.css.call($obj, propertyNames);
        };

        /**
         * paragraph level style
         *
         * @param {WrappedRange} rng
         * @param {Object} styleInfo
         */
        this.stylePara = function (rng, styleInfo) {
            $.each(rng.nodes(dom.isPara, {
                includeAncestor: true
            }), function (idx, para) {
                $(para).css(styleInfo);
            });
        };

        /**
         * get current style on cursor
         *
         * @param {WrappedRange} rng
         * @param {Node} target - target element on event
         * @return {Object} - object contains style properties.
         */
        this.current = function (rng, target) {
            var $cont = $(dom.isText(rng.sc) ? rng.sc.parentNode : rng.sc);
            var properties = ['font-family', 'font-size', 'text-align', 'list-style-type', 'line-height'];
            var styleInfo = jQueryCSS($cont, properties) || {};

            styleInfo['font-size'] = parseInt(styleInfo['font-size'], 10);

            // document.queryCommandState for toggle state
            styleInfo['font-bold'] = document.queryCommandState('bold') ? 'bold' : 'normal';
            styleInfo['font-italic'] = document.queryCommandState('italic') ? 'italic' : 'normal';
            styleInfo['font-underline'] = document.queryCommandState('underline') ? 'underline' : 'normal';
            styleInfo['font-strikethrough'] = document.queryCommandState('strikeThrough') ? 'strikethrough' : 'normal';
            styleInfo['font-superscript'] = document.queryCommandState('superscript') ? 'superscript' : 'normal';
            styleInfo['font-subscript'] = document.queryCommandState('subscript') ? 'subscript' : 'normal';

            // list-style-type to list-style(unordered, ordered)
            if (!rng.isOnList()) {
                styleInfo['list-style'] = 'none';
            } else {
                var aOrderedType = ['circle', 'disc', 'disc-leading-zero', 'square'];
                var isUnordered = $.inArray(styleInfo['list-style-type'], aOrderedType) > -1;
                styleInfo['list-style'] = isUnordered ? 'unordered' : 'ordered';
            }

            var para = dom.ancestor(rng.sc, dom.isPara);
            if (para && para.style['line-height']) {
                styleInfo['line-height'] = para.style.lineHeight;
            } else {
                var lineHeight = parseInt(styleInfo['line-height'], 10) / parseInt(styleInfo['font-size'], 10);
                styleInfo['line-height'] = lineHeight.toFixed(1);
            }

            styleInfo.image = dom.isImg(target) && target;
            styleInfo.anchor = rng.isOnAnchor() && dom.ancestor(rng.sc, dom.isAnchor);
            styleInfo.ancestors = dom.listAncestor(rng.sc, dom.isEditable);
            styleInfo.range = rng;

            return styleInfo;
        };
    };


    /**
     * Data structure
     *  - {BoundaryPoint}: a point of dom tree
     *  - {BoundaryPoints}: two boundaryPoints corresponding to the start and the end of the Range
     *
     *  @see http://www.w3.org/TR/DOM-Level-2-Traversal-Range/ranges.html#Level-2-Range-Position
     */
    var range = (function () {

        /**
         * return boundaryPoint from TextRange, inspired by Andy Na's HuskyRange.js
         *
         * @param {TextRange} textRange
         * @param {Boolean} isStart
         * @return {BoundaryPoint}
         *
         * @see http://msdn.microsoft.com/en-us/library/ie/ms535872(v=vs.85).aspx
         */
        var textRangeToPoint = function (textRange, isStart) {
            var container = textRange.parentElement(), offset;

            var tester = document.body.createTextRange(), prevContainer;
            var childNodes = list.from(container.childNodes);
            for (offset = 0; offset < childNodes.length; offset++) {
                if (dom.isText(childNodes[offset])) {
                    continue;
                }
                tester.moveToElementText(childNodes[offset]);
                if (tester.compareEndPoints('StartToStart', textRange) >= 0) {
                    break;
                }
                prevContainer = childNodes[offset];
            }

            if (offset !== 0 && dom.isText(childNodes[offset - 1])) {
                var textRangeStart = document.body.createTextRange(), curTextNode = null;
                textRangeStart.moveToElementText(prevContainer || container);
                textRangeStart.collapse(!prevContainer);
                curTextNode = prevContainer ? prevContainer.nextSibling : container.firstChild;

                var pointTester = textRange.duplicate();
                pointTester.setEndPoint('StartToStart', textRangeStart);
                var textCount = pointTester.text.replace(/[\r\n]/g, '').length;

                while (textCount > curTextNode.nodeValue.length && curTextNode.nextSibling) {
                    textCount -= curTextNode.nodeValue.length;
                    curTextNode = curTextNode.nextSibling;
                }

                /* jshint ignore:start */
                var dummy = curTextNode.nodeValue; // enforce IE to re-reference curTextNode, hack
                /* jshint ignore:end */

                if (isStart && curTextNode.nextSibling && dom.isText(curTextNode.nextSibling) &&
                    textCount === curTextNode.nodeValue.length) {
                    textCount -= curTextNode.nodeValue.length;
                    curTextNode = curTextNode.nextSibling;
                }

                container = curTextNode;
                offset = textCount;
            }

            return {
                cont: container,
                offset: offset
            };
        };

        /**
         * return TextRange from boundary point (inspired by google closure-library)
         * @param {BoundaryPoint} point
         * @return {TextRange}
         */
        var pointToTextRange = function (point) {
            var textRangeInfo = function (container, offset) {
                var node, isCollapseToStart;

                if (dom.isText(container)) {
                    var prevTextNodes = dom.listPrev(container, func.not(dom.isText));
                    var prevContainer = list.last(prevTextNodes).previousSibling;
                    node = prevContainer || container.parentNode;
                    offset += list.sum(list.tail(prevTextNodes), dom.nodeLength);
                    isCollapseToStart = !prevContainer;
                } else {
                    node = container.childNodes[offset] || container;
                    if (dom.isText(node)) {
                        return textRangeInfo(node, 0);
                    }

                    offset = 0;
                    isCollapseToStart = false;
                }

                return {
                    node: node,
                    collapseToStart: isCollapseToStart,
                    offset: offset
                };
            };

            var textRange = document.body.createTextRange();
            var info = textRangeInfo(point.node, point.offset);

            textRange.moveToElementText(info.node);
            textRange.collapse(info.collapseToStart);
            textRange.moveStart('character', info.offset);
            return textRange;
        };

        /**
         * Wrapped Range
         *
         * @param {Node} sc - start container
         * @param {Number} so - start offset
         * @param {Node} ec - end container
         * @param {Number} eo - end offset
         */
        var WrappedRange = function (sc, so, ec, eo) {
            this.sc = sc;
            this.so = so;
            this.ec = ec;
            this.eo = eo;

            // nativeRange: get nativeRange from sc, so, ec, eo
            var nativeRange = function () {
                if (agent.isW3CRangeSupport) {
                    var w3cRange = document.createRange();
                    w3cRange.setStart(sc, so);
                    w3cRange.setEnd(ec, eo);

                    return w3cRange;
                } else {
                    var textRange = pointToTextRange({
                        node: sc,
                        offset: so
                    });

                    textRange.setEndPoint('EndToEnd', pointToTextRange({
                        node: ec,
                        offset: eo
                    }));

                    return textRange;
                }
            };

            this.getPoints = function () {
                return {
                    sc: sc,
                    so: so,
                    ec: ec,
                    eo: eo
                };
            };

            this.getStartPoint = function () {
                return {
                    node: sc,
                    offset: so
                };
            };

            this.getEndPoint = function () {
                return {
                    node: ec,
                    offset: eo
                };
            };

            /**
             * select update visible range
             */
            this.select = function () {
                var nativeRng = nativeRange();
                if (agent.isW3CRangeSupport) {
                    var selection = document.getSelection();
                    if (selection.rangeCount > 0) {
                        selection.removeAllRanges();
                    }
                    selection.addRange(nativeRng);
                } else {
                    nativeRng.select();
                }
            };

            /**
             * @return {WrappedRange}
             */
            this.normalize = function () {
                var getVisiblePoint = function (point) {
                    if (!dom.isVisiblePoint(point)) {
                        if (dom.isLeftEdgePoint(point)) {
                            point = dom.nextPointUntil(point, dom.isVisiblePoint);
                        } else if (dom.isRightEdgePoint(point)) {
                            point = dom.prevPointUntil(point, dom.isVisiblePoint);
                        }
                    }
                    return point;
                };

                var startPoint = getVisiblePoint(this.getStartPoint());
                var endPoint = getVisiblePoint(this.getStartPoint());

                return new WrappedRange(
                    startPoint.node,
                    startPoint.offset,
                    endPoint.node,
                    endPoint.offset
                );
            };

            /**
             * returns matched nodes on range
             *
             * @param {Function} [pred] - predicate function
             * @param {Object} [options]
             * @param {Boolean} [options.includeAncestor]
             * @param {Boolean} [options.fullyContains]
             * @return {Node[]}
             */
            this.nodes = function (pred, options) {
                pred = pred || func.ok;

                var includeAncestor = options && options.includeAncestor;
                var fullyContains = options && options.fullyContains;

                // TODO compare points and sort
                var startPoint = this.getStartPoint();
                var endPoint = this.getEndPoint();

                var nodes = [];
                var leftEdgeNodes = [];

                dom.walkPoint(startPoint, endPoint, function (point) {
                    if (dom.isEditable(point.node)) {
                        return;
                    }

                    var node;
                    if (fullyContains) {
                        if (dom.isLeftEdgePoint(point)) {
                            leftEdgeNodes.push(point.node);
                        }
                        if (dom.isRightEdgePoint(point) && list.contains(leftEdgeNodes, point.node)) {
                            node = point.node;
                        }
                    } else if (includeAncestor) {
                        node = dom.ancestor(point.node, pred);
                    } else {
                        node = point.node;
                    }

                    if (node && pred(node)) {
                        nodes.push(node);
                    }
                }, true);

                return list.unique(nodes);
            };

            /**
             * returns commonAncestor of range
             * @return {Element} - commonAncestor
             */
            this.commonAncestor = function () {
                return dom.commonAncestor(sc, ec);
            };

            /**
             * returns expanded range by pred
             *
             * @param {Function} pred - predicate function
             * @return {WrappedRange}
             */
            this.expand = function (pred) {
                var startAncestor = dom.ancestor(sc, pred);
                var endAncestor = dom.ancestor(ec, pred);

                if (!startAncestor && !endAncestor) {
                    return new WrappedRange(sc, so, ec, eo);
                }

                var boundaryPoints = this.getPoints();

                if (startAncestor) {
                    boundaryPoints.sc = startAncestor;
                    boundaryPoints.so = 0;
                }

                if (endAncestor) {
                    boundaryPoints.ec = endAncestor;
                    boundaryPoints.eo = dom.nodeLength(endAncestor);
                }

                return new WrappedRange(
                    boundaryPoints.sc,
                    boundaryPoints.so,
                    boundaryPoints.ec,
                    boundaryPoints.eo
                );
            };

            /**
             * @param {Boolean} isCollapseToStart
             * @return {WrappedRange}
             */
            this.collapse = function (isCollapseToStart) {
                if (isCollapseToStart) {
                    return new WrappedRange(sc, so, sc, so);
                } else {
                    return new WrappedRange(ec, eo, ec, eo);
                }
            };

            /**
             * splitText on range
             */
            this.splitText = function () {
                var isSameContainer = sc === ec;
                var boundaryPoints = this.getPoints();

                if (dom.isText(ec) && !dom.isEdgePoint(this.getEndPoint())) {
                    ec.splitText(eo);
                }

                if (dom.isText(sc) && !dom.isEdgePoint(this.getStartPoint())) {
                    boundaryPoints.sc = sc.splitText(so);
                    boundaryPoints.so = 0;

                    if (isSameContainer) {
                        boundaryPoints.ec = boundaryPoints.sc;
                        boundaryPoints.eo = eo - so;
                    }
                }

                return new WrappedRange(
                    boundaryPoints.sc,
                    boundaryPoints.so,
                    boundaryPoints.ec,
                    boundaryPoints.eo
                );
            };

            /**
             * delete contents on range
             * @return {WrappedRange}
             */
            this.deleteContents = function () {
                if (this.isCollapsed()) {
                    return this;
                }

                var rng = this.splitText();
                var nodes = rng.nodes(null, {
                    fullyContains: true
                });

                var point = dom.prevPointUntil(rng.getStartPoint(), function (point) {
                    return !list.contains(nodes, point.node);
                });

                var emptyParents = [];
                $.each(nodes, function (idx, node) {
                    // find empty parents
                    var parent = node.parentNode;
                    if (point.node !== parent && dom.nodeLength(parent) === 1) {
                        emptyParents.push(parent);
                    }
                    dom.remove(node, false);
                });

                // remove empty parents
                $.each(emptyParents, function (idx, node) {
                    dom.remove(node, false);
                });

                return new WrappedRange(
                    point.node,
                    point.offset,
                    point.node,
                    point.offset
                );
            };

            /**
             * makeIsOn: return isOn(pred) function
             */
            var makeIsOn = function (pred) {
                return function () {
                    var ancestor = dom.ancestor(sc, pred);
                    return !!ancestor && (ancestor === dom.ancestor(ec, pred));
                };
            };

            // isOnEditable: judge whether range is on editable or not
            this.isOnEditable = makeIsOn(dom.isEditable);
            // isOnList: judge whether range is on list node or not
            this.isOnList = makeIsOn(dom.isList);
            // isOnAnchor: judge whether range is on anchor node or not
            this.isOnAnchor = makeIsOn(dom.isAnchor);
            // isOnAnchor: judge whether range is on cell node or not
            this.isOnCell = makeIsOn(dom.isCell);

            /**
             * returns whether range was collapsed or not
             */
            this.isCollapsed = function () {
                return sc === ec && so === eo;
            };

            /**
             * wrap inline nodes which children of body with paragraph
             *
             * @return {WrappedRange}
             */
            this.wrapBodyInlineWithPara = function () {
                // startContainer on bodyContainer
                if (dom.isEditable(sc) && !sc.childNodes[so]) {
                    return new WrappedRange(sc.appendChild($(dom.emptyPara)[0]), 0);
                } else if (!dom.isInline(sc) || dom.isParaInline(sc)) {
                    return this;
                }

                // find inline top ancestor
                var ancestors = dom.listAncestor(sc, func.not(dom.isInline));
                var topAncestor = list.last(ancestors);
                if (!dom.isInline(topAncestor)) {
                    topAncestor = ancestors[ancestors.length - 2] || sc.childNodes[so];
                }

                // siblings not in paragraph
                var inlineSiblings = dom.listPrev(topAncestor, dom.isParaInline).reverse();
                inlineSiblings = inlineSiblings.concat(dom.listNext(topAncestor.nextSibling, dom.isParaInline));

                // wrap with paragraph
                if (inlineSiblings.length) {
                    var para = dom.wrap(list.head(inlineSiblings), 'p');
                    dom.appendChildNodes(para, list.tail(inlineSiblings));
                }

                return this;
            };

            /**
             * insert node at current cursor
             *
             * @param {Node} node
             * @param {Boolean} [isInline]
             * @return {Node}
             */
            this.insertNode = function (node, isInline) {
                var rng = this.wrapBodyInlineWithPara();
                var point = rng.getStartPoint();

                var splitRoot, container, pivot;
                if (isInline) {
                    container = dom.isPara(point.node) ? point.node : point.node.parentNode;
                    if (dom.isPara(point.node)) {
                        pivot = point.node.childNodes[point.offset];
                    } else {
                        pivot = dom.splitTree(point.node, point);
                    }
                } else {
                    // splitRoot will be childNode of container
                    var ancestors = dom.listAncestor(point.node, dom.isBodyContainer);
                    var topAncestor = list.last(ancestors) || point.node;

                    if (dom.isBodyContainer(topAncestor)) {
                        splitRoot = ancestors[ancestors.length - 2];
                        container = topAncestor;
                    } else {
                        splitRoot = topAncestor;
                        container = splitRoot.parentNode;
                    }
                    pivot = splitRoot && dom.splitTree(splitRoot, point);
                }

                if (pivot) {
                    pivot.parentNode.insertBefore(node, pivot);
                } else {
                    container.appendChild(node);
                }

                return node;
            };

            this.toString = function () {
                var nativeRng = nativeRange();
                return agent.isW3CRangeSupport ? nativeRng.toString() : nativeRng.text;
            };

            /**
             * create offsetPath bookmark
             * @param {Node} editable
             */
            this.bookmark = function (editable) {
                return {
                    s: {
                        path: dom.makeOffsetPath(editable, sc),
                        offset: so
                    },
                    e: {
                        path: dom.makeOffsetPath(editable, ec),
                        offset: eo
                    }
                };
            };

            /**
             * getClientRects
             * @return {Rect[]}
             */
            this.getClientRects = function () {
                var nativeRng = nativeRange();
                return nativeRng.getClientRects();
            };
        };

        return {
            /**
             * create Range Object From arguments or Browser Selection
             *
             * @param {Node} sc - start container
             * @param {Number} so - start offset
             * @param {Node} ec - end container
             * @param {Number} eo - end offset
             */
            create: function (sc, so, ec, eo) {
                if (!arguments.length) { // from Browser Selection
                    if (agent.isW3CRangeSupport) {
                        var selection = document.getSelection();
                        if (selection.rangeCount === 0) {
                            return null;
                        }

                        var nativeRng = selection.getRangeAt(0);
                        sc = nativeRng.startContainer;
                        so = nativeRng.startOffset;
                        ec = nativeRng.endContainer;
                        eo = nativeRng.endOffset;
                    } else { // IE8: TextRange
                        var textRange = document.selection.createRange();
                        var textRangeEnd = textRange.duplicate();
                        textRangeEnd.collapse(false);
                        var textRangeStart = textRange;
                        textRangeStart.collapse(true);

                        var startPoint = textRangeToPoint(textRangeStart, true),
                            endPoint = textRangeToPoint(textRangeEnd, false);

                        sc = startPoint.cont;
                        so = startPoint.offset;
                        ec = endPoint.cont;
                        eo = endPoint.offset;
                    }
                } else if (arguments.length === 2) { //collapsed
                    ec = sc;
                    eo = so;
                }
                return new WrappedRange(sc, so, ec, eo);
            },

            /**
             * create WrappedRange from node
             *
             * @param {Node} node
             * @return {WrappedRange}
             */
            createFromNode: function (node) {
                return this.create(node, 0, node, 1);
            },

            /**
             * create WrappedRange from Bookmark
             *
             * @param {Node} editable
             * @param {Obkect} bookmark
             * @return {WrappedRange}
             */
            createFromBookmark: function (editable, bookmark) {
                var sc = dom.fromOffsetPath(editable, bookmark.s.path);
                var so = bookmark.s.offset;
                var ec = dom.fromOffsetPath(editable, bookmark.e.path);
                var eo = bookmark.e.offset;
                return new WrappedRange(sc, so, ec, eo);
            }
        };
    })();

    /**
     * Table
     * @class
     */
    var Table = function () {
        /**
         * handle tab key
         *
         * @param {WrappedRange} rng
         * @param {Boolean} isShift
         */
        this.tab = function (rng, isShift) {
            var cell = dom.ancestor(rng.commonAncestor(), dom.isCell);
            var table = dom.ancestor(cell, dom.isTable);
            var cells = dom.listDescendant(table, dom.isCell);

            var nextCell = list[isShift ? 'prev' : 'next'](cells, cell);
            if (nextCell) {
                range.create(nextCell, 0).select();
            }
        };

        /**
         * create empty table element
         *
         * @param {Number} rowCount
         * @param {Number} colCount
         * @return {Node}
         */
        this.createTable = function (colCount, rowCount) {
            var tds = [], tdHTML;
            for (var idxCol = 0; idxCol < colCount; idxCol++) {
                tds.push('<td>' + dom.blank + '</td>');
            }
            tdHTML = tds.join('');

            var trs = [], trHTML;
            for (var idxRow = 0; idxRow < rowCount; idxRow++) {
                trs.push('<tr>' + tdHTML + '</tr>');
            }
            trHTML = trs.join('');
            return $('<table class="table table-bordered">' + trHTML + '</table>')[0];
        };
    };

    /**
     * Editor
     * @class
     */
    var Editor = function () {

        var style = new Style();
        var table = new Table();

        /**
         * save current range
         *
         * @param {jQuery} $editable
         */
        this.saveRange = function ($editable) {
            $editable.focus();
            $editable.data('range', range.create());
        };

        /**
         * restore lately range
         *
         * @param {jQuery} $editable
         */
        this.restoreRange = function ($editable) {
            var rng = $editable.data('range');
            if (rng) {
                rng.select();
                $editable.focus();
            }
        };

        /**
         * current style
         * @param {Node} target
         */
        this.currentStyle = function (target) {
            var rng = range.create();
            return rng ? rng.isOnEditable() && style.current(rng, target) : false;
        };

        var triggerOnChange = this.triggerOnChange = function ($editable) {
            var onChange = $editable.data('callbacks').onChange;
            if (onChange) {
                onChange($editable.html(), $editable);
            }
        };

        /**
         * undo
         * @param {jQuery} $editable
         */
        this.undo = function ($editable) {
            $editable.data('NoteHistory').undo($editable);
            triggerOnChange($editable);
        };

        /**
         * redo
         * @param {jQuery} $editable
         */
        this.redo = function ($editable) {
            $editable.data('NoteHistory').redo($editable);
            triggerOnChange($editable);
        };

        /**
         * record Undo
         * @param {jQuery} $editable
         */
        var recordUndo = this.recordUndo = function ($editable) {
            $editable.data('NoteHistory').recordUndo($editable);
        };

        /* jshint ignore:start */
        // native commands(with execCommand), generate function for execCommand
        var commands = ['bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript',
            'justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull',
            'insertOrderedList', 'insertUnorderedList',
            'indent', 'outdent', 'formatBlock', 'removeFormat',
            'backColor', 'foreColor', 'insertHorizontalRule', 'fontName'];

        for (var idx = 0, len = commands.length; idx < len; idx++) {
            this[commands[idx]] = (function (sCmd) {
                return function ($editable, value) {
                    recordUndo($editable);

                    document.execCommand(sCmd, false, value);
                };
            })(commands[idx]);
        }
        /* jshint ignore:end */

        /**
         * @param {jQuery} $editable 
         * @param {WrappedRange} rng
         * @param {Number} tabsize
         */
        var insertTab = function ($editable, rng, tabsize) {
            recordUndo($editable);

            var tab = dom.createText(new Array(tabsize + 1).join(dom.NBSP_CHAR));
            rng = rng.deleteContents();
            rng.insertNode(tab, true);

            rng = range.create(tab, tabsize);
            rng.select();
        };

        /**
         * handle tab key
         * @param {jQuery} $editable 
         * @param {Object} options
         */
        this.tab = function ($editable, options) {
            var rng = range.create();
            if (rng.isCollapsed() && rng.isOnCell()) {
                table.tab(rng);
            } else {
                insertTab($editable, rng, options.tabsize);
            }
        };

        /**
         * handle shift+tab key
         */
        this.untab = function () {
            var rng = range.create();
            if (rng.isCollapsed() && rng.isOnCell()) {
                table.tab(rng, true);
            }
        };

        /**
         * insert paragraph
         *
         * @param {Node} $editable
         */
        this.insertParagraph = function ($editable) {
            recordUndo($editable);

            var rng = range.create();

            // deleteContents on range.
            rng = rng.deleteContents();

            rng = rng.wrapBodyInlineWithPara();

            // find split root node: block level node
            var splitRoot = dom.ancestor(rng.sc, dom.isPara);
            var nextPara = dom.splitTree(splitRoot, rng.getStartPoint());

            var emptyAnchors = dom.listDescendant(splitRoot, dom.isEmptyAnchor);
            emptyAnchors = emptyAnchors.concat(dom.listDescendant(nextPara, dom.isEmptyAnchor));

            $.each(emptyAnchors, function (idx, anchor) {
                dom.remove(anchor);
            });

            range.create(nextPara, 0).normalize().select();
            triggerOnChange($editable);
        };

        /**
         * insert image
         *
         * @param {jQuery} $editable
         * @param {String} sUrl
         */
        this.insertImage = function ($editable, sUrl, filename) {
            async.createImage(sUrl, filename).then(function ($image) {
                recordUndo($editable);

                $image.css({
                    display: '',
                    width: Math.min($editable.width(), $image.width())
                });
                range.create().insertNode($image[0]);
                triggerOnChange($editable);
            }).fail(function () {
                var callbacks = $editable.data('callbacks');
                if (callbacks.onImageUploadError) {
                    callbacks.onImageUploadError();
                }
            });
        };

        /**
         * insert video
         * @param {jQuery} $editable
         * @param {String} sUrl
         */
        this.insertVideo = function ($editable, sUrl) {
            recordUndo($editable);

            // video url patterns(youtube, instagram, vimeo, dailymotion, youku)
            var ytRegExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
            var ytMatch = sUrl.match(ytRegExp);

            var igRegExp = /\/\/instagram.com\/p\/(.[a-zA-Z0-9]*)/;
            var igMatch = sUrl.match(igRegExp);

            var vRegExp = /\/\/vine.co\/v\/(.[a-zA-Z0-9]*)/;
            var vMatch = sUrl.match(vRegExp);

            var vimRegExp = /\/\/(player.)?vimeo.com\/([a-z]*\/)*([0-9]{6,11})[?]?.*/;
            var vimMatch = sUrl.match(vimRegExp);

            var dmRegExp = /.+dailymotion.com\/(video|hub)\/([^_]+)[^#]*(#video=([^_&]+))?/;
            var dmMatch = sUrl.match(dmRegExp);

            var youkuRegExp = /\/\/v\.youku\.com\/v_show\/id_(\w+)\.html/;
            var youkuMatch = sUrl.match(youkuRegExp);

            var $video;
            if (ytMatch && ytMatch[2].length === 11) {
                var youtubeId = ytMatch[2];
                $video = $('<iframe>')
                    .attr('src', '//www.youtube.com/embed/' + youtubeId)
                    .attr('width', '640').attr('height', '360');
            } else if (igMatch && igMatch[0].length) {
                $video = $('<iframe>')
                    .attr('src', igMatch[0] + '/embed/')
                    .attr('width', '612').attr('height', '710')
                    .attr('scrolling', 'no')
                    .attr('allowtransparency', 'true');
            } else if (vMatch && vMatch[0].length) {
                $video = $('<iframe>')
                    .attr('src', vMatch[0] + '/embed/simple')
                    .attr('width', '600').attr('height', '600')
                    .attr('class', 'vine-embed');
            } else if (vimMatch && vimMatch[3].length) {
                $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')
                    .attr('src', '//player.vimeo.com/video/' + vimMatch[3])
                    .attr('width', '640').attr('height', '360');
            } else if (dmMatch && dmMatch[2].length) {
                $video = $('<iframe>')
                    .attr('src', '//www.dailymotion.com/embed/video/' + dmMatch[2])
                    .attr('width', '640').attr('height', '360');
            } else if (youkuMatch && youkuMatch[1].length) {
                $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')
                    .attr('height', '498')
                    .attr('width', '510')
                    .attr('src', '//player.youku.com/embed/' + youkuMatch[1]);
            } else {
                // this is not a known video link. Now what, Cat? Now what?
            }

            if ($video) {
                $video.attr('frameborder', 0);
                range.create().insertNode($video[0]);
                triggerOnChange($editable);
            }
        };

        /**
         * formatBlock
         *
         * @param {jQuery} $editable
         * @param {String} tagName
         */
        this.formatBlock = function ($editable, tagName) {
            recordUndo($editable);

            tagName = agent.isMSIE ? '<' + tagName + '>' : tagName;
            document.execCommand('FormatBlock', false, tagName);
        };

        this.formatPara = function ($editable) {
            this.formatBlock($editable, 'P');
        };

        /* jshint ignore:start */
        for (var idx = 1; idx <= 6; idx++) {
            this['formatH' + idx] = function (idx) {
                return function ($editable) {
                    this.formatBlock($editable, 'H' + idx);
                };
            }(idx);
        };
        /* jshint ignore:end */

        /**
         * fontsize
         * FIXME: Still buggy
         *
         * @param {jQuery} $editable
         * @param {String} value - px
         */
        this.fontSize = function ($editable, value) {
            recordUndo($editable);

            document.execCommand('fontSize', false, 3);
            if (agent.isFF) {
                // firefox: <font size="3"> to <span style='font-size={value}px;'>, buggy
                $editable.find('font[size=3]').removeAttr('size').css('font-size', value + 'px');
            } else {
                // chrome: <span style="font-size: medium"> to <span style='font-size={value}px;'>
                $editable.find('span').filter(function () {
                    return this.style.fontSize === 'medium';
                }).css('font-size', value + 'px');
            }
        };

        /**
         * lineHeight
         * @param {jQuery} $editable
         * @param {String} value
         */
        this.lineHeight = function ($editable, value) {
            recordUndo($editable);

            style.stylePara(range.create(), {
                lineHeight: value
            });
            triggerOnChange($editable);
        };

        /**
         * unlink
         * @param {jQuery} $editable
         */
        this.unlink = function ($editable) {
            var rng = range.create();
            if (rng.isOnAnchor()) {
                recordUndo($editable);

                var anchor = dom.ancestor(rng.sc, dom.isAnchor);
                rng = range.createFromNode(anchor);
                rng.select();
                document.execCommand('unlink');
            }
        };
        /**
           * unlink image link
           * @param {jQuery} $editable
           * @param {jQuery} $target
           */
        this.unlinkImageLink = function ($editable, value, $target) {
            /*
            var rng = range.create();
            if (rng.isOnAnchor()) {
                recordUndo($editable);

                var anchor = dom.ancestor(rng.sc, dom.isAnchor);
                rng = range.createFromNode(anchor);
                rng.select();
                document.execCommand('unlink');
            }
            */
            recordUndo($editable);
            if ($target.parent('a').length) {
                $target.unwrap();
            }

        };
        /**
           * create image link
           *
           * @param {jQuery} $editable
           * @param {Object} linkInfo
           * @param {jQuery} $target - target element
           */
        this.createImageLink = function ($editable, linkInfo, options, $target) {
            var linkUrl = linkInfo.url;
            var isNewWindow = linkInfo.newWindow;

            recordUndo($editable);
            // Create a new link when there is no anchor on range.
            if ($target.parent('a').length) {
                var anchor = $target.parent('a');
                anchor.attr({
                    href: linkUrl,
                    target: isNewWindow ? '_blank' : ''
                });
            } else {
                var anchor = $('<a></a>');
                $(anchor).attr({
                    href: linkUrl,
                    target: isNewWindow ? '_blank' : ''
                });
                $target.wrap(anchor);
            }

            //range.createFromNode(anchor).select();
            triggerOnChange($editable);
        };
        /**
         * create link
         *
         * @param {jQuery} $editable
         * @param {Object} linkInfo
         * @param {Object} options
         */
        this.createLink = function ($editable, linkInfo, options) {
            var linkUrl = linkInfo.url;
            var linkText = linkInfo.text;
            var isNewWindow = linkInfo.newWindow;
            var rng = linkInfo.range;

            recordUndo($editable);

            if (options.onCreateLink) {
                linkUrl = options.onCreateLink(linkUrl);
            }

            rng = rng.deleteContents();

            // Create a new link when there is no anchor on range.
            var anchor = rng.insertNode($('<A>' + linkText + '</A>')[0], true);
            $(anchor).attr({
                href: linkUrl,
                target: isNewWindow ? '_blank' : ''
            });

            range.createFromNode(anchor).select();
            triggerOnChange($editable);
        };

        /**
         * returns link info
         *
         * @return {Object}
         */
        this.getLinkInfo = function ($editable) {
            $editable.focus();

            var rng = range.create().expand(dom.isAnchor);

            // Get the first anchor on range(for edit).
            var $anchor = $(list.head(rng.nodes(dom.isAnchor)));

            return {
                range: rng,
                text: rng.toString(),
                isNewWindow: $anchor.length ? $anchor.attr('target') === '_blank' : true,
                url: $anchor.length ? $anchor.attr('href') : ''
            };
        };

        /**
         * get video info
         *
         * @param {jQuery} $editable
         * @return {Object}
         */
        this.getVideoInfo = function ($editable) {
            $editable.focus();

            var rng = range.create();

            if (rng.isOnAnchor()) {
                var anchor = dom.ancestor(rng.sc, dom.isAnchor);
                rng = range.createFromNode(anchor);
            }

            return {
                text: rng.toString()
            };
        };

        this.color = function ($editable, sObjColor) {
            var oColor = JSON.parse(sObjColor);
            var foreColor = oColor.foreColor, backColor = oColor.backColor;

            recordUndo($editable);

            if (foreColor) { document.execCommand('foreColor', false, foreColor); }
            if (backColor) { document.execCommand('backColor', false, backColor); }
        };

        this.insertTable = function ($editable, sDim) {
            recordUndo($editable);

            var dimension = sDim.split('x');
            var rng = range.create();
            rng = rng.deleteContents();
            rng.insertNode(table.createTable(dimension[0], dimension[1]));
            triggerOnChange($editable);
        };

        /**
         * @param {jQuery} $editable
         * @param {String} value
         * @param {jQuery} $target
         */
        this.floatMe = function ($editable, value, $target) {
            recordUndo($editable);

            $target.css('float', value);
        };

        /**
         * resize overlay element
         * @param {jQuery} $editable
         * @param {String} value
         * @param {jQuery} $target - target element
         */
        this.resize = function ($editable, value, $target) {
            recordUndo($editable);

            $target.css({
                width: $editable.width() * value + 'px',
                height: ''
            });
        };

        /**
         * @param {Position} pos
         * @param {jQuery} $target - target element
         * @param {Boolean} [bKeepRatio] - keep ratio
         */
        this.resizeTo = function (pos, $target, bKeepRatio) {
            var imageSize;
            if (bKeepRatio) {
                var newRatio = pos.y / pos.x;
                var ratio = $target.data('ratio');
                imageSize = {
                    width: ratio > newRatio ? pos.x : pos.y / ratio,
                    height: ratio > newRatio ? pos.x * ratio : pos.y
                };
            } else {
                imageSize = {
                    width: pos.x,
                    height: pos.y
                };
            }

            $target.css(imageSize);
        };

        /**
         * remove media object
         *
         * @param {jQuery} $editable
         * @param {String} value - dummy argument (for keep interface)
         * @param {jQuery} $target - target element
         */
        this.removeMedia = function ($editable, value, $target) {
            recordUndo($editable);

            $target.detach();
        };
    };

    /**
     * History
     * @class
     */
    var History = function () {
        var undoStack = [], redoStack = [];

        var makeSnapshot = function ($editable) {
            var editable = $editable[0];
            var rng = range.create();

            return {
                contents: $editable.html(),
                bookmark: rng.bookmark(editable)
            };
        };

        var applySnapshot = function ($editable, snapshot) {
            $editable.html(snapshot.contents);
            // FIXME: Still buggy, use marker tag
            // range.createFromBookmark($editable[0], snapshot.bookmark).select();
        };

        this.undo = function ($editable) {
            var snapshot = makeSnapshot($editable);
            if (!undoStack.length) {
                return;
            }
            applySnapshot($editable, undoStack.pop());
            redoStack.push(snapshot);
        };

        this.redo = function ($editable) {
            var snapshot = makeSnapshot($editable);
            if (!redoStack.length) {
                return;
            }
            applySnapshot($editable, redoStack.pop());
            undoStack.push(snapshot);
        };

        this.recordUndo = function ($editable) {
            redoStack = [];
            undoStack.push(makeSnapshot($editable));
        };
    };

    /**
     * Button
     */
    var Button = function () {
        /**
         * update button status
         *
         * @param {jQuery} $container
         * @param {Object} styleInfo
         */
        this.update = function ($container, styleInfo) {
            /**
             * handle dropdown's check mark (for fontname, fontsize, lineHeight).
             * @param {jQuery} $btn
             * @param {Number} value
             */
            var checkDropdownMenu = function ($btn, value) {
                $btn.find('.dropdown-menu li a').each(function () {
                    // always compare string to avoid creating another func.
                    var isChecked = ($(this).data('value') + '') === (value + '');
                    this.className = isChecked ? 'checked' : '';
                });
            };

            /**
             * update button state(active or not).
             *
             * @param {String} selector
             * @param {Function} pred
             */
            var btnState = function (selector, pred) {
                var $btn = $container.find(selector);
                $btn.toggleClass('active', pred());
            };

            // fontname
            var $fontname = $container.find('.note-fontname');
            if ($fontname.length) {
                var selectedFont = styleInfo['font-family'];
                if (!!selectedFont) {
                    selectedFont = list.head(selectedFont.split(','));
                    selectedFont = selectedFont.replace(/\'/g, '');
                    $fontname.find('.note-current-fontname').text(selectedFont);
                    checkDropdownMenu($fontname, selectedFont);
                }
            }

            // fontsize
            var $fontsize = $container.find('.note-fontsize');
            $fontsize.find('.note-current-fontsize').text(styleInfo['font-size']);
            checkDropdownMenu($fontsize, parseFloat(styleInfo['font-size']));

            // lineheight
            var $lineHeight = $container.find('.note-height');
            checkDropdownMenu($lineHeight, parseFloat(styleInfo['line-height']));

            btnState('button[data-event="bold"]', function () {
                return styleInfo['font-bold'] === 'bold';
            });
            btnState('button[data-event="italic"]', function () {
                return styleInfo['font-italic'] === 'italic';
            });
            btnState('button[data-event="underline"]', function () {
                return styleInfo['font-underline'] === 'underline';
            });
            btnState('button[data-event="strikethrough"]', function () {
                return styleInfo['font-strikethrough'] === 'strikethrough';
            });
            btnState('button[data-event="superscript"]', function () {
                return styleInfo['font-superscript'] === 'superscript';
            });
            btnState('button[data-event="subscript"]', function () {
                return styleInfo['font-subscript'] === 'subscript';
            });
            btnState('button[data-event="justifyLeft"]', function () {
                return styleInfo['text-align'] === 'left' || styleInfo['text-align'] === 'start';
            });
            btnState('button[data-event="justifyCenter"]', function () {
                return styleInfo['text-align'] === 'center';
            });
            btnState('button[data-event="justifyRight"]', function () {
                return styleInfo['text-align'] === 'right';
            });
            btnState('button[data-event="justifyFull"]', function () {
                return styleInfo['text-align'] === 'justify';
            });
            btnState('button[data-event="insertUnorderedList"]', function () {
                return styleInfo['list-style'] === 'unordered';
            });
            btnState('button[data-event="insertOrderedList"]', function () {
                return styleInfo['list-style'] === 'ordered';
            });
        };

        /**
         * update recent color
         *
         * @param {Node} button
         * @param {String} eventName
         * @param {value} value
         */
        this.updateRecentColor = function (button, eventName, value) {
            var $color = $(button).closest('.note-color');
            var $recentColor = $color.find('.note-recent-color');
            var colorInfo = JSON.parse($recentColor.attr('data-value'));
            colorInfo[eventName] = value;
            $recentColor.attr('data-value', JSON.stringify(colorInfo));
            var sKey = eventName === 'backColor' ? 'background-color' : 'color';
            $recentColor.find('i').css(sKey, value);
        };
    };

    /**
     * Toolbar
     */
    var Toolbar = function () {
        var button = new Button();

        this.update = function ($toolbar, styleInfo) {
            button.update($toolbar, styleInfo);
        };

        /**
         * @param {Node} button
         * @param {String} eventName
         * @param {String} value
         */
        this.updateRecentColor = function (buttonNode, eventName, value) {
            button.updateRecentColor(buttonNode, eventName, value);
        };

        /**
         * activate buttons exclude codeview
         * @param {jQuery} $toolbar
         */
        this.activate = function ($toolbar) {
            $toolbar.find('button')
                .not('button[data-event="codeview"]')
                .removeClass('disabled');
        };

        /**
         * deactivate buttons exclude codeview
         * @param {jQuery} $toolbar
         */
        this.deactivate = function ($toolbar) {
            $toolbar.find('button')
                .not('button[data-event="codeview"]')
                .addClass('disabled');
        };

        this.updateFullscreen = function ($container, bFullscreen) {
            var $btn = $container.find('button[data-event="fullscreen"]');
            $btn.toggleClass('active', bFullscreen);
        };

        this.updateCodeview = function ($container, isCodeview) {
            var $btn = $container.find('button[data-event="codeview"]');
            $btn.toggleClass('active', isCodeview);
        };
    };

    /**
     * Popover (http://getbootstrap.com/javascript/#popovers)
     */
    var Popover = function () {
        var button = new Button();

        /**
         * returns position from placeholder
         * @param {Node} placeholder
         * @param {Boolean} isAirMode
         */
        var posFromPlaceholder = function (placeholder, isAirMode) {
            var $placeholder = $(placeholder);
            var pos = isAirMode ? $placeholder.offset() : $placeholder.position();
            var height = $placeholder.outerHeight(true); // include margin

            // popover below placeholder.
            return {
                left: pos.left,
                top: pos.top + height
            };
        };

        /**
         * show popover
         * @param {jQuery} popover
         * @param {Position} pos
         */
        var showPopover = function ($popover, pos) {
            $popover.css({
                display: 'block',
                left: pos.left,
                top: pos.top
            });
        };

        var PX_POPOVER_ARROW_OFFSET_X = 20;

        /**
         * update current state
         * @param {jQuery} $popover - popover container
         * @param {Object} styleInfo - style object
         * @param {Boolean} isAirMode
         */
        this.update = function ($popover, styleInfo, isAirMode) {
            button.update($popover, styleInfo);

            var $linkPopover = $popover.find('.note-link-popover');
            if (styleInfo.anchor) {
                var $anchor = $linkPopover.find('a');
                var href = $(styleInfo.anchor).attr('href');
                $anchor.attr('href', href).html(href);
                showPopover($linkPopover, posFromPlaceholder(styleInfo.anchor, isAirMode));
            } else {
                $linkPopover.hide();
            }

            var $imagePopover = $popover.find('.note-image-popover');
            if (styleInfo.image) {
                showPopover($imagePopover, posFromPlaceholder(styleInfo.image, isAirMode));
            } else {
                $imagePopover.hide();
            }

            var $airPopover = $popover.find('.note-air-popover');
            if (isAirMode && !styleInfo.range.isCollapsed()) {
                var bnd = func.rect2bnd(list.last(styleInfo.range.getClientRects()));
                showPopover($airPopover, {
                    left: Math.max(bnd.left + bnd.width / 2 - PX_POPOVER_ARROW_OFFSET_X, 0),
                    top: bnd.top + bnd.height
                });
            } else {
                $airPopover.hide();
            }
        };

        /**
         * @param {Node} button
         * @param {String} eventName
         * @param {String} value
         */
        this.updateRecentColor = function (button, eventName, value) {
            button.updateRecentColor(button, eventName, value);
        };

        /**
         * hide all popovers
         * @param {jQuery} $popover - popover contaienr
         */
        this.hide = function ($popover) {
            $popover.children().hide();
        };
    };

    /**
     * Handle
     */
    var Handle = function () {
        /**
         * update handle
         * @param {jQuery} $handle
         * @param {Object} styleInfo
         * @param {Boolean} isAirMode
         */
        this.update = function ($handle, styleInfo, isAirMode) {
            var $selection = $handle.find('.note-control-selection');
            if (styleInfo.image) {
                var $image = $(styleInfo.image);
                var pos = isAirMode ? $image.offset() : $image.position();

                // include margin
                var imageSize = {
                    w: $image.outerWidth(true),
                    h: $image.outerHeight(true)
                };

                $selection.css({
                    display: 'block',
                    left: pos.left,
                    top: pos.top,
                    width: imageSize.w,
                    height: imageSize.h
                }).data('target', styleInfo.image); // save current image element.
                var sizingText = imageSize.w + 'x' + imageSize.h;
                $selection.find('.note-control-selection-info').text(sizingText);
            } else {
                $selection.hide();
            }
        };

        this.hide = function ($handle) {
            $handle.children().hide();
        };
    };

    /**
     * Dialog 
     *
     * @class
     */
    var Dialog = function () {

        /**
         * toggle button status
         *
         * @param {jQuery} $btn
         * @param {Boolean} isEnable
         */
        var toggleBtn = function ($btn, isEnable) {
            $btn.toggleClass('disabled', !isEnable);
            $btn.attr('disabled', !isEnable);
        };

        /**
         * show image dialog
         *
         * @param {jQuery} $editable
         * @param {jQuery} $dialog
         * @return {Promise}
         */
        this.showImageDialog = function ($editable, $dialog) {
            return $.Deferred(function (deferred) {
                var $imageDialog = $dialog.find('.note-image-dialog');

                var $imageInput = $dialog.find('.note-image-input'),
                    $imageUrl = $dialog.find('.note-image-url'),
                    $imageBtn = $dialog.find('.note-image-btn');

                $imageDialog.one('shown.bs.modal', function () {
                    // Cloning imageInput to clear element.
                    $imageInput.replaceWith($imageInput.clone()
                        .on('change', function () {
                            deferred.resolve(this.files);
                            $imageDialog.modal('hide');
                        })
                        .val('')
                    );

                    $imageBtn.click(function (event) {
                        event.preventDefault();

                        deferred.resolve($imageUrl.val());
                        $imageDialog.modal('hide');
                    });

                    $imageUrl.on('keyup paste', function (event) {
                        var url;

                        if (event.type === 'paste') {
                            url = event.originalEvent.clipboardData.getData('text');
                        } else {
                            url = $imageUrl.val();
                        }

                        toggleBtn($imageBtn, url);
                    }).val('').trigger('focus');
                }).one('hidden.bs.modal', function () {
                    $imageInput.off('change');
                    $imageUrl.off('keyup paste');
                    $imageBtn.off('click');

                    if (deferred.state() === 'pending') {
                        deferred.reject();
                    }
                }).modal('show');
            });
        };

        /**
         * Show video dialog and set event handlers on dialog controls.
         *
         * @param {jQuery} $dialog 
         * @param {Object} videoInfo 
         * @return {Promise}
         */
        this.showVideoDialog = function ($editable, $dialog, videoInfo) {
            return $.Deferred(function (deferred) {
                var $videoDialog = $dialog.find('.note-video-dialog');
                var $videoUrl = $videoDialog.find('.note-video-url'),
                    $videoBtn = $videoDialog.find('.note-video-btn');

                $videoDialog.one('shown.bs.modal', function () {
                    $videoUrl.val(videoInfo.text).keyup(function () {
                        toggleBtn($videoBtn, $videoUrl.val());
                    }).trigger('keyup').trigger('focus');

                    $videoBtn.click(function (event) {
                        event.preventDefault();

                        deferred.resolve($videoUrl.val());
                        $videoDialog.modal('hide');
                    });
                }).one('hidden.bs.modal', function () {
                    // dettach events
                    $videoUrl.off('keyup');
                    $videoBtn.off('click');

                    if (deferred.state() === 'pending') {
                        deferred.reject();
                    }
                }).modal('show');
            });
        };

        /**
         * Show link dialog and set event handlers on dialog controls.
         *
         * @param {jQuery} $dialog
         * @param {Object} linkInfo
         * @return {Promise}
         */
        this.showLinkDialog = function ($editable, $dialog, linkInfo) {
            return $.Deferred(function (deferred) {
                var $linkDialog = $dialog.find('.note-link-dialog');

                var $linkText = $linkDialog.find('.note-link-text'),
                    $linkUrl = $linkDialog.find('.note-link-url'),
                    $linkBtn = $linkDialog.find('.note-link-btn'),
                    $openInNewWindow = $linkDialog.find('input[type=checkbox]');

                $linkDialog.one('shown.bs.modal', function () {
                    $linkText.val(linkInfo.text);

                    $linkText.keyup(function () {
                        // if linktext was modified by keyup,
                        // stop cloning text from linkUrl
                        linkInfo.text = $linkText.val();
                    });

                    // if no url was given, copy text to url
                    if (!linkInfo.url) {
                        linkInfo.url = linkInfo.text;
                        toggleBtn($linkBtn, linkInfo.text);
                    }

                    $linkUrl.keyup(function () {
                        toggleBtn($linkBtn, $linkUrl.val());
                        // display same link on `Text to display` input
                        // when create a new link
                        if (!linkInfo.text) {
                            $linkText.val($linkUrl.val());
                        }
                    }).val(linkInfo.url).trigger('focus').trigger('select');

                    $openInNewWindow.prop('checked', linkInfo.newWindow);

                    $linkBtn.one('click', function (event) {
                        event.preventDefault();

                        deferred.resolve({
                            range: linkInfo.range,
                            url: $linkUrl.val(),
                            text: $linkText.val(),
                            newWindow: $openInNewWindow.is(':checked')
                        });
                        $linkDialog.modal('hide');
                    });
                }).one('hidden.bs.modal', function () {
                    // dettach events
                    $linkText.off('keyup');
                    $linkUrl.off('keyup');
                    $linkBtn.off('click');

                    if (deferred.state() === 'pending') {
                        deferred.reject();
                    }
                }).modal('show');
            }).promise();
        };
        /**
           * Show link dialog and set event handlers on dialog controls.
           *
           * @param {jQuery} $dialog
           * @param {Object} linkInfo
           * @return {Promise}
           */
        this.showImageLinkDialog = function ($editable, $dialog, linkInfo) {
            return $.Deferred(function (deferred) {
                var $linkDialog = $dialog.find('.note-image-link-dialog');

                var $linkUrl = $linkDialog.find('.note-link-url'),
                    $linkBtn = $linkDialog.find('.note-link-btn'),
                    $openInNewWindow = $linkDialog.find('input[type=checkbox]');

                $linkUrl.val(linkInfo.url);

                $linkDialog.one('shown.bs.modal', function () {

                    $linkUrl.keyup(function () {
                        toggleBtn($linkBtn, $linkUrl.val());

                    }).val(linkInfo.url).trigger('focus').trigger('select');

                    $openInNewWindow.prop('checked', linkInfo.newWindow);

                    $linkBtn.one('click', function (event) {
                        event.preventDefault();

                        deferred.resolve({
                            range: linkInfo.range,
                            url: $linkUrl.val(),
                            newWindow: $openInNewWindow.is(':checked')
                        });
                        $linkDialog.modal('hide');
                    });
                }).one('hidden.bs.modal', function () {
                    // dettach events					
                    $linkUrl.off('keyup');
                    $linkBtn.off('click');

                    if (deferred.state() === 'pending') {
                        deferred.reject();
                    }
                }).modal('show');
            }).promise();
        };
        /**
         * show help dialog
         *
         * @param {jQuery} $dialog
         */
        this.showHelpDialog = function ($editable, $dialog) {
            return $.Deferred(function (deferred) {
                var $helpDialog = $dialog.find('.note-help-dialog');

                $helpDialog.one('hidden.bs.modal', function () {
                    deferred.resolve();
                }).modal('show');
            }).promise();
        };
    };


    var CodeMirror;
    if (agent.hasCodeMirror) {
        if (agent.isSupportAmd) {
            require(['CodeMirror'], function (cm) {
                CodeMirror = cm;
            });
        } else {
            CodeMirror = window.CodeMirror;
        }
    }

    /**
     * EventHandler
     */
    var EventHandler = function () {
        var $window = $(window);
        var $document = $(document);
        var $scrollbar = $('html, body');

        var editor = new Editor();
        var toolbar = new Toolbar(), popover = new Popover();
        var handle = new Handle(), dialog = new Dialog();

        /**
         * returns makeLayoutInfo from editor's descendant node.
         *
         * @param {Node} descendant
         * @returns {Object}
         */
        var makeLayoutInfo = function (descendant) {
            var $target = $(descendant).closest('.note-editor, .note-air-editor, .note-air-layout');

            if (!$target.length) { return null; }

            var $editor;
            if ($target.is('.note-editor, .note-air-editor')) {
                $editor = $target;
            } else {
                $editor = $('#note-editor-' + list.last($target.attr('id').split('-')));
            }

            return dom.buildLayoutInfo($editor);
        };

        /**
         * insert Images from file array.
         *
         * @param {jQuery} $editable
         * @param {File[]} files
         */
        var insertImages = function ($editable, files) {
            editor.restoreRange($editable);
            var callbacks = $editable.data('callbacks');

            // If onImageUpload options setted
            if (callbacks.onImageUpload) {
                callbacks.onImageUpload(files, editor, $editable);
                // else insert Image as dataURL
            } else {
                $.each(files, function (idx, file) {
                    var filename = file.name;
                    async.readFileAsDataURL(file).then(function (sDataURL) {
                        editor.insertImage($editable, sDataURL, filename);
                    }).fail(function () {
                        if (callbacks.onImageUploadError) {
                            callbacks.onImageUploadError();
                        }
                    });
                });
            }
        };

        var commands = {
            /**
             * @param {Object} layoutInfo
             */
            showLinkDialog: function (layoutInfo) {
                var $editor = layoutInfo.editor(),
                    $dialog = layoutInfo.dialog(),
                    $editable = layoutInfo.editable(),
                    linkInfo = editor.getLinkInfo($editable);

                var options = $editor.data('options');

                editor.saveRange($editable);
                dialog.showLinkDialog($editable, $dialog, linkInfo).then(function (linkInfo) {
                    editor.restoreRange($editable);
                    editor.createLink($editable, linkInfo, options);
                    // hide popover after creating link
                    popover.hide(layoutInfo.popover());
                }).fail(function () {
                    editor.restoreRange($editable);
                });
            },
            /**
            * @param {Object} layoutInfo
            */
            showImageLinkDialog: function (layoutInfo) {
                var $editor = layoutInfo.editor(),
                    $dialog = layoutInfo.dialog(),
                    $editable = layoutInfo.editable(),
                    $handle = layoutInfo.handle(),
                    linkInfo = editor.getLinkInfo($editable);

                var options = $editor.data('options'),
                    target = $handle.find('.note-control-selection').data('target'),
                    $target = $(target);


                editor.saveRange($editable);
                dialog.showImageLinkDialog($editable, $dialog, linkInfo).then(function (linkInfo) {
                    editor.restoreRange($editable);
                    editor.createImageLink($editable, linkInfo, options, $target);
                    // hide popover after creating link
                    popover.hide(layoutInfo.popover());
                }).fail(function () {
                    editor.restoreRange($editable);
                });
            },

            /**
             * @param {Object} layoutInfo
             */
            unlinkImageLink: function (layoutInfo) {
                var $editor = layoutInfo.editor(),
                    $handle = layoutInfo.handle(),
                    target = $handle.find('.note-control-selection').data('target'),
                    $target = $(target);

                editor.saveRange($editable);
                editor.unlinkImageLink($editable, $target);
            },
            /**
              * @param {Object} layoutInfo
              */
            showImageDialog: function (layoutInfo) {
                var $dialog = layoutInfo.dialog(),
                    $editable = layoutInfo.editable();

                editor.saveRange($editable);
                dialog.showImageDialog($editable, $dialog).then(function (data) {
                    editor.restoreRange($editable);

                    if (typeof data === 'string') {
                        // image url
                        editor.insertImage($editable, data);
                    } else {
                        // array of files
                        insertImages($editable, data);
                    }
                }).fail(function () {
                    editor.restoreRange($editable);
                });
            },

            /**
             * @param {Object} layoutInfo
             */
            showVideoDialog: function (layoutInfo) {
                var $dialog = layoutInfo.dialog(),
                    $editable = layoutInfo.editable(),
                    videoInfo = editor.getVideoInfo($editable);

                editor.saveRange($editable);
                dialog.showVideoDialog($editable, $dialog, videoInfo).then(function (sUrl) {
                    editor.restoreRange($editable);
                    editor.insertVideo($editable, sUrl);
                }).fail(function () {
                    editor.restoreRange($editable);
                });
            },

            /**
             * @param {Object} layoutInfo
             */
            showHelpDialog: function (layoutInfo) {
                var $dialog = layoutInfo.dialog(),
                    $editable = layoutInfo.editable();

                editor.saveRange($editable);
                dialog.showHelpDialog($editable, $dialog).then(function () {
                    editor.restoreRange($editable);
                });
            },

            fullscreen: function (layoutInfo) {
                var $editor = layoutInfo.editor(),
                    $toolbar = layoutInfo.toolbar(),
                    $editable = layoutInfo.editable(),
                    $codable = layoutInfo.codable();

                var options = $editor.data('options');

                var resize = function (size) {
                    $editor.css('width', size.w);
                    $editable.css('height', size.h);
                    $codable.css('height', size.h);
                    if ($codable.data('cmeditor')) {
                        $codable.data('cmeditor').setsize(null, size.h);
                    }
                };

                $editor.toggleClass('fullscreen');
                var isFullscreen = $editor.hasClass('fullscreen');
                if (isFullscreen) {
                    $editable.data('orgheight', $editable.css('height'));

                    $window.on('resize', function () {
                        resize({
                            w: $window.width(),
                            h: $window.height() - $toolbar.outerHeight()
                        });
                    }).trigger('resize');

                    $scrollbar.css('overflow', 'hidden');
                } else {
                    $window.off('resize');
                    resize({
                        w: options.width || '',
                        h: $editable.data('orgheight')
                    });
                    $scrollbar.css('overflow', 'visible');
                }

                toolbar.updateFullscreen($toolbar, isFullscreen);
            },

            codeview: function (layoutInfo) {
                var $editor = layoutInfo.editor(),
                    $toolbar = layoutInfo.toolbar(),
                    $editable = layoutInfo.editable(),
                    $codable = layoutInfo.codable(),
                    $popover = layoutInfo.popover();

                var options = $editor.data('options');

                var cmEditor, server;

                $editor.toggleClass('codeview');

                var isCodeview = $editor.hasClass('codeview');
                if (isCodeview) {
                    $codable.val(dom.html($editable, true));
                    $codable.height($editable.height());
                    toolbar.deactivate($toolbar);
                    popover.hide($popover);
                    $codable.focus();

                    // activate CodeMirror as codable
                    if (agent.hasCodeMirror) {
                        cmEditor = CodeMirror.fromTextArea($codable[0], options.codemirror);

                        // CodeMirror TernServer
                        if (options.codemirror.tern) {
                            server = new CodeMirror.TernServer(options.codemirror.tern);
                            cmEditor.ternServer = server;
                            cmEditor.on('cursorActivity', function (cm) {
                                server.updateArgHints(cm);
                            });
                        }

                        // CodeMirror hasn't Padding.
                        cmEditor.setSize(null, $editable.outerHeight());
                        $codable.data('cmEditor', cmEditor);
                    }
                } else {
                    // deactivate CodeMirror as codable
                    if (agent.hasCodeMirror) {
                        cmEditor = $codable.data('cmEditor');
                        $codable.val(cmEditor.getValue());
                        cmEditor.toTextArea();
                    }

                    $editable.html(dom.value($codable) || dom.emptyPara);
                    $editable.height(options.height ? $codable.height() : 'auto');

                    toolbar.activate($toolbar);
                    $editable.focus();
                }

                toolbar.updateCodeview(layoutInfo.toolbar(), isCodeview);
            }
        };

        var hMousedown = function (event) {
            //preventDefault Selection for FF, IE8+
            if (dom.isImg(event.target)) {
                event.preventDefault();
            }
        };

        var hToolbarAndPopoverUpdate = function (event) {
            // delay for range after mouseup
            setTimeout(function () {
                var layoutInfo = makeLayoutInfo(event.currentTarget || event.target);
                var styleInfo = editor.currentStyle(event.target);
                if (!styleInfo) { return; }

                var isAirMode = layoutInfo.editor().data('options').airMode;
                if (!isAirMode) {
                    toolbar.update(layoutInfo.toolbar(), styleInfo);
                }

                popover.update(layoutInfo.popover(), styleInfo, isAirMode);
                handle.update(layoutInfo.handle(), styleInfo, isAirMode);
            }, 0);
        };

        var hScroll = function (event) {
            var layoutInfo = makeLayoutInfo(event.currentTarget || event.target);
            //hide popover and handle when scrolled
            popover.hide(layoutInfo.popover());
            handle.hide(layoutInfo.handle());
        };

        /**
         * paste clipboard image
         *
         * @param {Event} event
         */
        var hPasteClipboardImage = function (event) {
            var clipboardData = event.originalEvent.clipboardData;
            if (!clipboardData || !clipboardData.items || !clipboardData.items.length) {
                return;
            }

            var layoutInfo = makeLayoutInfo(event.currentTarget || event.target);
            var item = list.head(clipboardData.items);
            var isClipboardImage = item.kind === 'file' && item.type.indexOf('image/') !== -1;

            if (isClipboardImage) {
                insertImages(layoutInfo.editable(), [item.getAsFile()]);
            }
        };

        /**
         * `mousedown` event handler on $handle
         *  - controlSizing: resize image
         *
         * @param {MouseEvent} event
         */
        var hHandleMousedown = function (event) {
            if (dom.isControlSizing(event.target)) {
                event.preventDefault();
                event.stopPropagation();

                var layoutInfo = makeLayoutInfo(event.target),
                    $handle = layoutInfo.handle(), $popover = layoutInfo.popover(),
                    $editable = layoutInfo.editable(),
                    $editor = layoutInfo.editor();

                var target = $handle.find('.note-control-selection').data('target'),
                    $target = $(target), posStart = $target.offset(),
                    scrollTop = $document.scrollTop();

                var isAirMode = $editor.data('options').airMode;

                $document.on('mousemove', function (event) {
                    editor.resizeTo({
                        x: event.clientX - posStart.left,
                        y: event.clientY - (posStart.top - scrollTop)
                    }, $target, !event.shiftKey);

                    handle.update($handle, { image: target }, isAirMode);
                    popover.update($popover, { image: target }, isAirMode);
                }).one('mouseup', function () {
                    $document.off('mousemove');
                });

                if (!$target.data('ratio')) { // original ratio.
                    $target.data('ratio', $target.height() / $target.width());
                }

                editor.recordUndo($editable);
            }
        };

        var hToolbarAndPopoverMousedown = function (event) {
            // prevent default event when insertTable (FF, Webkit)
            var $btn = $(event.target).closest('[data-event]');
            if ($btn.length) {
                event.preventDefault();
            }
        };

        var hToolbarAndPopoverClick = function (event) {
            var $btn = $(event.target).closest('[data-event]');

            if ($btn.length) {
                var eventName = $btn.attr('data-event'),
                    value = $btn.attr('data-value');

                var layoutInfo = makeLayoutInfo(event.target);

                event.preventDefault();

                // before command: detect control selection element($target)
                var $target;
                if ($.inArray(eventName, ['resize', 'floatMe', 'removeMedia', 'showImageLink', 'unlinkImageLink']) !== -1) {
                    var $selection = layoutInfo.handle().find('.note-control-selection');
                    $target = $($selection.data('target'));
                }

                if (editor[eventName]) { // on command
                    var $editable = layoutInfo.editable();
                    $editable.trigger('focus');
                    editor[eventName]($editable, value, $target);
                } else if (commands[eventName]) {
                    commands[eventName].call(this, layoutInfo);
                }

                // after command
                if ($.inArray(eventName, ['backColor', 'foreColor']) !== -1) {
                    var options = layoutInfo.editor().data('options', options);
                    var module = options.airMode ? popover : toolbar;
                    module.updateRecentColor(list.head($btn), eventName, value);
                }

                hToolbarAndPopoverUpdate(event);
            }
        };

        var EDITABLE_PADDING = 24;
        /**
         * `mousedown` event handler on statusbar
         *
         * @param {MouseEvent} event
         */
        var hStatusbarMousedown = function (event) {
            event.preventDefault();
            event.stopPropagation();

            var $editable = makeLayoutInfo(event.target).editable();
            var nEditableTop = $editable.offset().top - $document.scrollTop();

            var layoutInfo = makeLayoutInfo(event.currentTarget || event.target);
            var options = layoutInfo.editor().data('options');

            $document.on('mousemove', function (event) {
                var nHeight = event.clientY - (nEditableTop + EDITABLE_PADDING);

                nHeight = (options.minHeight > 0) ? Math.max(nHeight, options.minHeight) : nHeight;
                nHeight = (options.maxHeight > 0) ? Math.min(nHeight, options.maxHeight) : nHeight;

                $editable.height(nHeight);
            }).one('mouseup', function () {
                $document.off('mousemove');
            });
        };

        var PX_PER_EM = 18;
        var hDimensionPickerMove = function (event, options) {
            var $picker = $(event.target.parentNode); // target is mousecatcher
            var $dimensionDisplay = $picker.next();
            var $catcher = $picker.find('.note-dimension-picker-mousecatcher');
            var $highlighted = $picker.find('.note-dimension-picker-highlighted');
            var $unhighlighted = $picker.find('.note-dimension-picker-unhighlighted');

            var posOffset;
            // HTML5 with jQuery - e.offsetX is undefined in Firefox
            if (event.offsetX === undefined) {
                var posCatcher = $(event.target).offset();
                posOffset = {
                    x: event.pageX - posCatcher.left,
                    y: event.pageY - posCatcher.top
                };
            } else {
                posOffset = {
                    x: event.offsetX,
                    y: event.offsetY
                };
            }

            var dim = {
                c: Math.ceil(posOffset.x / PX_PER_EM) || 1,
                r: Math.ceil(posOffset.y / PX_PER_EM) || 1
            };

            $highlighted.css({ width: dim.c + 'em', height: dim.r + 'em' });
            $catcher.attr('data-value', dim.c + 'x' + dim.r);

            if (3 < dim.c && dim.c < options.insertTableMaxSize.col) {
                $unhighlighted.css({ width: dim.c + 1 + 'em' });
            }

            if (3 < dim.r && dim.r < options.insertTableMaxSize.row) {
                $unhighlighted.css({ height: dim.r + 1 + 'em' });
            }

            $dimensionDisplay.html(dim.c + ' x ' + dim.r);
        };

        /**
         * Drag and Drop Events
         *
         * @param {Object} layoutInfo - layout Informations
         * @param {Boolean} disableDragAndDrop
         */
        var handleDragAndDropEvent = function (layoutInfo, disableDragAndDrop) {
            if (disableDragAndDrop) {
                // prevent default drop event
                $document.on('drop', function (e) {
                    e.preventDefault();
                });
            } else {
                attachDragAndDropEvent(layoutInfo);
            }
        };

        /**
         * attach Drag and Drop Events
         *
         * @param {Object} layoutInfo - layout Informations
         */
        var attachDragAndDropEvent = function (layoutInfo) {
            var collection = $(),
                $dropzone = layoutInfo.dropzone,
                $dropzoneMessage = layoutInfo.dropzone.find('.note-dropzone-message');

            // show dropzone on dragenter when dragging a object to document.
            $document.on('dragenter', function (e) {
                var isCodeview = layoutInfo.editor.hasClass('codeview');
                if (!isCodeview && !collection.length) {
                    layoutInfo.editor.addClass('dragover');
                    $dropzone.width(layoutInfo.editor.width());
                    $dropzone.height(layoutInfo.editor.height());
                    $dropzoneMessage.text('Drag Image Here');
                }
                collection = collection.add(e.target);
            }).on('dragleave', function (e) {
                collection = collection.not(e.target);
                if (!collection.length) {
                    layoutInfo.editor.removeClass('dragover');
                }
            }).on('drop', function () {
                collection = $();
                layoutInfo.editor.removeClass('dragover');
            });

            // change dropzone's message on hover.
            $dropzone.on('dragenter', function () {
                $dropzone.addClass('hover');
                $dropzoneMessage.text('Drop Image');
            }).on('dragleave', function () {
                $dropzone.removeClass('hover');
                $dropzoneMessage.text('Drag Image Here');
            });

            // attach dropImage
            $dropzone.on('drop', function (event) {
                event.preventDefault();

                var dataTransfer = event.originalEvent.dataTransfer;
                if (dataTransfer && dataTransfer.files) {
                    var layoutInfo = makeLayoutInfo(event.currentTarget || event.target);
                    layoutInfo.editable().focus();
                    insertImages(layoutInfo.editable(), dataTransfer.files);
                }
            }).on('dragover', false); // prevent default dragover event
        };


        /**
         * bind KeyMap on keydown
         *
         * @param {Object} layoutInfo
         * @param {Object} keyMap
         */
        this.bindKeyMap = function (layoutInfo, keyMap) {
            var $editor = layoutInfo.editor;
            var $editable = layoutInfo.editable;

            layoutInfo = makeLayoutInfo($editable);

            $editable.on('keydown', function (event) {
                var aKey = [];

                // modifier
                if (event.metaKey) { aKey.push('CMD'); }
                if (event.ctrlKey && !event.altKey) { aKey.push('CTRL'); }
                if (event.shiftKey) { aKey.push('SHIFT'); }

                // keycode
                var keyName = key.nameFromCode[event.keyCode];
                if (keyName) { aKey.push(keyName); }

                var eventName = keyMap[aKey.join('+')];
                if (eventName) {
                    event.preventDefault();

                    if (editor[eventName]) {
                        editor[eventName]($editable, $editor.data('options'));
                    } else if (commands[eventName]) {
                        commands[eventName].call(this, layoutInfo);
                    }
                } else if (key.isEdit(event.keyCode)) {
                    editor.recordUndo($editable);
                }
            });
        };

        /**
         * attach eventhandler
         *
         * @param {Object} layoutInfo - layout Informations
         * @param {Object} options - user options include custom event handlers
         * @param {Function} options.enter - enter key handler
         */
        this.attach = function (layoutInfo, options) {
            // handlers for editable
            this.bindKeyMap(layoutInfo, options.keyMap[agent.isMac ? 'mac' : 'pc']);
            layoutInfo.editable.on('mousedown', hMousedown);
            layoutInfo.editable.on('keyup mouseup', hToolbarAndPopoverUpdate);
            layoutInfo.editable.on('scroll', hScroll);
            layoutInfo.editable.on('paste', hPasteClipboardImage);

            // handler for handle and popover
            layoutInfo.handle.on('mousedown', hHandleMousedown);
            layoutInfo.popover.on('click', hToolbarAndPopoverClick);
            layoutInfo.popover.on('mousedown', hToolbarAndPopoverMousedown);

            // handlers for frame mode (toolbar, statusbar)
            if (!options.airMode) {
                // handler for drag and drop
                handleDragAndDropEvent(layoutInfo, options.disableDragAndDrop);

                // handler for toolbar
                layoutInfo.toolbar.on('click', hToolbarAndPopoverClick);
                layoutInfo.toolbar.on('mousedown', hToolbarAndPopoverMousedown);

                // handler for statusbar
                if (!options.disableResizeEditor) {
                    layoutInfo.statusbar.on('mousedown', hStatusbarMousedown);
                }
            }

            // handler for table dimension
            var $catcherContainer = options.airMode ? layoutInfo.popover :
                layoutInfo.toolbar;
            var $catcher = $catcherContainer.find('.note-dimension-picker-mousecatcher');
            $catcher.css({
                width: options.insertTableMaxSize.col + 'em',
                height: options.insertTableMaxSize.row + 'em'
            }).on('mousemove', function (event) {
                hDimensionPickerMove(event, options);
            });

            // save options on editor
            layoutInfo.editor.data('options', options);

            // ret styleWithCSS for backColor / foreColor clearing with 'inherit'.
            if (options.styleWithSpan && !agent.isMSIE) {
                // protect FF Error: NS_ERROR_FAILURE: Failure
                setTimeout(function () {
                    document.execCommand('styleWithCSS', 0, true);
                }, 0);
            }

            // History
            layoutInfo.editable.data('NoteHistory', new History());

            // basic event callbacks (lowercase)
            // enter, focus, blur, keyup, keydown
            if (options.onenter) {
                layoutInfo.editable.keypress(function (event) {
                    if (event.keyCode === key.ENTER) { options.onenter(event); }
                });
            }

            if (options.onfocus) { layoutInfo.editable.focus(options.onfocus); }
            if (options.onblur) { layoutInfo.editable.blur(options.onblur); }
            if (options.onkeyup) { layoutInfo.editable.keyup(options.onkeyup); }
            if (options.onkeydown) { layoutInfo.editable.keydown(options.onkeydown); }
            if (options.onpaste) { layoutInfo.editable.on('paste', options.onpaste); }

            // callbacks for advanced features (camel)
            if (options.onToolbarClick) { layoutInfo.toolbar.click(options.onToolbarClick); }
            if (options.onChange) {
                var hChange = function () {
                    editor.triggerOnChange(layoutInfo.editable);
                };

                if (agent.isMSIE) {
                    var sDomEvents = 'DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted';
                    layoutInfo.editable.on(sDomEvents, hChange);
                } else {
                    layoutInfo.editable.on('input', hChange);
                }
            }

            // All editor status will be saved on editable with jquery's data
            // for support multiple editor with singleton object.
            layoutInfo.editable.data('callbacks', {
                onChange: options.onChange,
                onAutoSave: options.onAutoSave,
                onImageUpload: options.onImageUpload,
                onImageUploadError: options.onImageUploadError,
                onFileUpload: options.onFileUpload,
                onFileUploadError: options.onFileUpload
            });
        };

        this.dettach = function (layoutInfo, options) {
            layoutInfo.editable.off();

            layoutInfo.popover.off();
            layoutInfo.handle.off();
            layoutInfo.dialog.off();

            if (!options.airMode) {
                layoutInfo.dropzone.off();
                layoutInfo.toolbar.off();
                layoutInfo.statusbar.off();
            }
        };
    };

    /**
     * renderer
     *
     * rendering toolbar and editable
     */
    var Renderer = function () {

        /**
         * bootstrap button template
         *
         * @param {String} label
         * @param {Object} [options]
         * @param {String} [options.event]
         * @param {String} [options.value]
         * @param {String} [options.title]
         * @param {String} [options.dropdown]
         */
        var tplButton = function (label, options) {
            var event = options.event;
            var value = options.value;
            var title = options.title;
            var className = options.className;
            var dropdown = options.dropdown;

            return '<button type="button"' +
                ' class="btn btn-default btn-sm btn-small' +
                (className ? ' ' + className : '') +
                (dropdown ? ' dropdown-toggle' : '') +
                '"' +
                (dropdown ? ' data-toggle="dropdown"' : '') +
                (title ? ' title="' + title + '"' : '') +
                (event ? ' data-event="' + event + '"' : '') +
                (value ? ' data-value=\'' + value + '\'' : '') +
                ' tabindex="-1">' +
                label +
                (dropdown ? ' <span class="caret"></span>' : '') +
                '</button>' +
                (dropdown || '');
        };

        /**
         * bootstrap icon button template
         *
         * @param {String} iconClassName
         * @param {Object} [options]
         * @param {String} [options.event]
         * @param {String} [options.value]
         * @param {String} [options.title]
         * @param {String} [options.dropdown]
         */
        var tplIconButton = function (iconClassName, options) {
            var label = '<i class="' + iconClassName + '"></i>';
            return tplButton(label, options);
        };

        /**
         * bootstrap popover template
         *
         * @param {String} className
         * @param {String} content
         */
        var tplPopover = function (className, content) {
            return '<div class="' + className + ' popover bottom in" style="display: none;">' +
                '<div class="arrow"></div>' +
                '<div class="popover-content">' +
                content +
                '</div>' +
                '</div>';
        };

        /**
         * bootstrap dialog template
         *
         * @param {String} className
         * @param {String} [title]
         * @param {String} body
         * @param {String} [footer]
         */
        var tplDialog = function (className, title, body, footer) {
            return '<div class="' + className + ' modal" aria-hidden="false">' +
                '<div class="modal-dialog">' +
                '<div class="modal-content">' +
                (title ?
                    '<div class="modal-header">' +
                    '<button type="button" class="close" aria-hidden="true" tabindex="-1">&times;</button>' +
                    '<h4 class="modal-title">' + title + '</h4>' +
                    '</div>' : ''
                ) +
                '<form class="note-modal-form">' +
                '<div class="modal-body">' +
                '<div class="row-fluid">' + body + '</div>' +
                '</div>' +
                (footer ?
                    '<div class="modal-footer">' + footer + '</div>' : ''
                ) +
                '</form>' +
                '</div>' +
                '</div>' +
                '</div>';
        };

        var tplButtonInfo = {
            picture: function (lang) {
                return tplIconButton('fa fa-picture-o icon-picture', {
                    event: 'showImageDialog',
                    title: lang.image.image
                });
            },
            link: function (lang) {
                return tplIconButton('fa fa-link icon-link', {
                    event: 'showLinkDialog',
                    title: lang.link.link
                });
            },
            video: function (lang) {
                return tplIconButton('fa fa-youtube-play icon-play', {
                    event: 'showVideoDialog',
                    title: lang.video.video
                });
            },
            table: function (lang) {
                var dropdown = '<ul class="dropdown-menu">' +
                    '<div class="note-dimension-picker">' +
                    '<div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"></div>' +
                    '<div class="note-dimension-picker-highlighted"></div>' +
                    '<div class="note-dimension-picker-unhighlighted"></div>' +
                    '</div>' +
                    '<div class="note-dimension-display"> 1 x 1 </div>' +
                    '</ul>';
                return tplIconButton('fa fa-table icon-table', {
                    title: lang.table.table,
                    dropdown: dropdown
                });
            },
            style: function (lang, options) {
                var items = options.styleTags.reduce(function (memo, v) {
                    var label = lang.style[v === 'p' ? 'normal' : v];
                    return memo + '<li><a data-event="formatBlock" href="#" data-value="' + v + '">' +
                        (
                            (v === 'p' || v === 'pre') ? label :
                                '<' + v + '>' + label + '</' + v + '>'
                        ) +
                        '</a></li>';
                }, '');

                return tplIconButton('fa fa-magic icon-magic', {
                    title: lang.style.style,
                    dropdown: '<ul class="dropdown-menu">' + items + '</ul>'
                });
            },
            fontname: function (lang, options) {
                var items = options.fontNames.reduce(function (memo, v) {
                    if (!agent.isFontInstalled(v)) { return memo; }
                    return memo + '<li><a data-event="fontName" href="#" data-value="' + v + '">' +
                        '<i class="fa fa-check icon-ok"></i> ' + v +
                        '</a></li>';
                }, '');
                var label = '<span class="note-current-fontname">' +
                    options.defaultFontName +
                    '</span>';
                return tplButton(label, {
                    title: lang.font.name,
                    dropdown: '<ul class="dropdown-menu">' + items + '</ul>'
                });
            },
            fontsize: function (lang, options) {
                var items = options.fontSizes.reduce(function (memo, v) {
                    return memo + '<li><a data-event="fontSize" href="#" data-value="' + v + '">' +
                        '<i class="fa fa-check icon-ok"></i> ' + v +
                        '</a></li>';
                }, '');

                var label = '<span class="note-current-fontsize">11</span>';
                return tplButton(label, {
                    title: lang.font.size,
                    dropdown: '<ul class="dropdown-menu">' + items + '</ul>'
                });
            },

            color: function (lang) {
                var colorButtonLabel = '<i class="fa fa-font icon-font" style="color:black;background-color:yellow;"></i>';
                var colorButton = tplButton(colorButtonLabel, {
                    className: 'note-recent-color',
                    title: lang.color.recent,
                    event: 'color',
                    value: '{"backColor":"yellow"}'
                });

                var dropdown = '<ul class="dropdown-menu">' +
                    '<li>' +
                    '<div class="btn-group">' +
                    '<div class="note-palette-title">' + lang.color.background + '</div>' +
                    '<div class="note-color-reset" data-event="backColor"' +
                    ' data-value="inherit" title="' + lang.color.transparent + '">' +
                    lang.color.setTransparent +
                    '</div>' +
                    '<div class="note-color-palette" data-target-event="backColor"></div>' +
                    '</div>' +
                    '<div class="btn-group">' +
                    '<div class="note-palette-title">' + lang.color.foreground + '</div>' +
                    '<div class="note-color-reset" data-event="foreColor" data-value="inherit" title="' + lang.color.reset + '">' +
                    lang.color.resetToDefault +
                    '</div>' +
                    '<div class="note-color-palette" data-target-event="foreColor"></div>' +
                    '</div>' +
                    '</li>' +
                    '</ul>';

                var moreButton = tplButton('', {
                    title: lang.color.more,
                    dropdown: dropdown
                });

                return colorButton + moreButton;
            },
            bold: function (lang) {
                return tplIconButton('fa fa-bold icon-bold', {
                    event: 'bold',
                    title: lang.font.bold
                });
            },
            italic: function (lang) {
                return tplIconButton('fa fa-italic icon-italic', {
                    event: 'italic',
                    title: lang.font.italic
                });
            },
            underline: function (lang) {
                return tplIconButton('fa fa-underline icon-underline', {
                    event: 'underline',
                    title: lang.font.underline
                });
            },
            strikethrough: function (lang) {
                return tplIconButton('fa fa-strikethrough icon-strikethrough', {
                    event: 'strikethrough',
                    title: lang.font.strikethrough
                });
            },
            superscript: function (lang) {
                return tplIconButton('fa fa-superscript icon-superscript', {
                    event: 'superscript',
                    title: lang.font.superscript
                });
            },
            subscript: function (lang) {
                return tplIconButton('fa fa-subscript icon-subscript', {
                    event: 'subscript',
                    title: lang.font.subscript
                });
            },
            clear: function (lang) {
                return tplIconButton('fa fa-eraser icon-eraser', {
                    event: 'removeFormat',
                    title: lang.font.clear
                });
            },
            ul: function (lang) {
                return tplIconButton('fa fa-list-ul icon-list-ul', {
                    event: 'insertUnorderedList',
                    title: lang.lists.unordered
                });
            },
            ol: function (lang) {
                return tplIconButton('fa fa-list-ol icon-list-ol', {
                    event: 'insertOrderedList',
                    title: lang.lists.ordered
                });
            },
            paragraph: function (lang) {
                var leftButton = tplIconButton('fa fa-align-left icon-align-left', {
                    title: lang.paragraph.left,
                    event: 'justifyLeft'
                });
                var centerButton = tplIconButton('fa fa-align-center icon-align-center', {
                    title: lang.paragraph.center,
                    event: 'justifyCenter'
                });
                var rightButton = tplIconButton('fa fa-align-right icon-align-right', {
                    title: lang.paragraph.right,
                    event: 'justifyRight'
                });
                var justifyButton = tplIconButton('fa fa-align-justify icon-align-justify', {
                    title: lang.paragraph.justify,
                    event: 'justifyFull'
                });

                var outdentButton = tplIconButton('fa fa-outdent icon-indent-left', {
                    title: lang.paragraph.outdent,
                    event: 'outdent'
                });
                var indentButton = tplIconButton('fa fa-indent icon-indent-right', {
                    title: lang.paragraph.indent,
                    event: 'indent'
                });

                var dropdown = '<div class="dropdown-menu">' +
                    '<div class="note-align btn-group">' +
                    leftButton + centerButton + rightButton + justifyButton +
                    '</div>' +
                    '<div class="note-list btn-group">' +
                    indentButton + outdentButton +
                    '</div>' +
                    '</div>';

                return tplIconButton('fa fa-align-left icon-align-left', {
                    title: lang.paragraph.paragraph,
                    dropdown: dropdown
                });
            },
            height: function (lang, options) {
                var items = options.lineHeights.reduce(function (memo, v) {
                    return memo + '<li><a data-event="lineHeight" href="#" data-value="' + parseFloat(v) + '">' +
                        '<i class="fa fa-check icon-ok"></i> ' + v +
                        '</a></li>';
                }, '');

                return tplIconButton('fa fa-text-height icon-text-height', {
                    title: lang.font.height,
                    dropdown: '<ul class="dropdown-menu">' + items + '</ul>'
                });

            },
            help: function (lang) {
                return tplIconButton('fa fa-question icon-question', {
                    event: 'showHelpDialog',
                    title: lang.options.help
                });
            },
            fullscreen: function (lang) {
                return tplIconButton('fa fa-arrows-alt icon-fullscreen', {
                    event: 'fullscreen',
                    title: lang.options.fullscreen
                });
            },
            codeview: function (lang) {
                return tplIconButton('fa fa-code icon-code', {
                    event: 'codeview',
                    title: lang.options.codeview
                });
            },
            undo: function (lang) {
                return tplIconButton('fa fa-undo icon-undo', {
                    event: 'undo',
                    title: lang.history.undo
                });
            },
            redo: function (lang) {
                return tplIconButton('fa fa-repeat icon-repeat', {
                    event: 'redo',
                    title: lang.history.redo
                });
            },
            hr: function (lang) {
                return tplIconButton('fa fa-minus icon-hr', {
                    event: 'insertHorizontalRule',
                    title: lang.hr.insert
                });
            }
        };

        var tplPopovers = function (lang, options) {
            var tplLinkPopover = function () {
                var linkButton = tplIconButton('fa fa-edit icon-edit', {
                    title: lang.link.edit,
                    event: 'showLinkDialog'
                });
                var unlinkButton = tplIconButton('fa fa-unlink icon-unlink', {
                    title: lang.link.unlink,
                    event: 'unlink'
                });
                var content = '<a href="https://www.google.com" target="_blank">www.google.com</a>&nbsp;&nbsp;' +
                    '<div class="note-insert btn-group">' +
                    linkButton + unlinkButton +
                    '</div>';
                return tplPopover('note-link-popover', content);
            };

            var tplImagePopover = function () {
                var fullButton = tplButton('<span class="note-fontsize-10">100%</span>', {
                    title: lang.image.resizeFull,
                    event: 'resize',
                    value: '1'
                });
                var halfButton = tplButton('<span class="note-fontsize-10">50%</span>', {
                    title: lang.image.resizeHalf,
                    event: 'resize',
                    value: '0.5'
                });
                var quarterButton = tplButton('<span class="note-fontsize-10">25%</span>', {
                    title: lang.image.resizeQuarter,
                    event: 'resize',
                    value: '0.25'
                });

                var leftButton = tplIconButton('fa fa-align-left icon-align-left', {
                    title: lang.image.floatLeft,
                    event: 'floatMe',
                    value: 'left'
                });
                var rightButton = tplIconButton('fa fa-align-right icon-align-right', {
                    title: lang.image.floatRight,
                    event: 'floatMe',
                    value: 'right'
                });
                var justifyButton = tplIconButton('fa fa-align-justify icon-align-justify', {
                    title: lang.image.floatNone,
                    event: 'floatMe',
                    value: 'none'
                });

                var removeButton = tplIconButton('fa fa-trash-o icon-trash', {
                    title: lang.image.remove,
                    event: 'removeMedia',
                    value: 'none'
                });

                var content = '<div class="btn-group">' + fullButton + halfButton + quarterButton + '</div>' +
                    '<div class="btn-group">' + leftButton + rightButton + justifyButton + '</div>' +
                    '<div class="btn-group">' + removeButton + '</div>';
                return tplPopover('note-image-popover', content);
            };

            var tplAirPopover = function () {
                var content = '';
                for (var idx = 0, len = options.airPopover.length; idx < len; idx++) {
                    var group = options.airPopover[idx];
                    content += '<div class="note-' + group[0] + ' btn-group">';
                    for (var i = 0, lenGroup = group[1].length; i < lenGroup; i++) {
                        content += tplButtonInfo[group[1][i]](lang, options);
                    }
                    content += '</div>';
                }

                return tplPopover('note-air-popover', content);
            };

            return '<div class="note-popover">' +
                tplLinkPopover() +
                tplImagePopover() +
                (options.airMode ? tplAirPopover() : '') +
                '</div>';
        };

        var tplHandles = function () {
            return '<div class="note-handle">' +
                '<div class="note-control-selection">' +
                '<div class="note-control-selection-bg"></div>' +
                '<div class="note-control-holder note-control-nw"></div>' +
                '<div class="note-control-holder note-control-ne"></div>' +
                '<div class="note-control-holder note-control-sw"></div>' +
                '<div class="note-control-sizing note-control-se"></div>' +
                '<div class="note-control-selection-info"></div>' +
                '</div>' +
                '</div>';
        };

        /**
         * shortcut table template
         * @param {String} title
         * @param {String} body
         */
        var tplShortcut = function (title, body) {
            return '<table class="note-shortcut">' +
                '<thead>' +
                '<tr><th></th><th>' + title + '</th></tr>' +
                '</thead>' +
                '<tbody>' + body + '</tbody>' +
                '</table>';
        };

        var tplShortcutText = function (lang) {
            var body = '<tr><td>⌘ + B</td><td>' + lang.font.bold + '</td></tr>' +
                '<tr><td>⌘ + I</td><td>' + lang.font.italic + '</td></tr>' +
                '<tr><td>⌘ + U</td><td>' + lang.font.underline + '</td></tr>' +
                '<tr><td>⌘ + ⇧ + S</td><td>' + lang.font.strikethrough + '</td></tr>' +
                '<tr><td>⌘ + \\</td><td>' + lang.font.clear + '</td></tr>';

            return tplShortcut(lang.shortcut.textFormatting, body);
        };

        var tplShortcutAction = function (lang) {
            var body = '<tr><td>⌘ + Z</td><td>' + lang.history.undo + '</td></tr>' +
                '<tr><td>⌘ + ⇧ + Z</td><td>' + lang.history.redo + '</td></tr>' +
                '<tr><td>⌘ + ]</td><td>' + lang.paragraph.indent + '</td></tr>' +
                '<tr><td>⌘ + [</td><td>' + lang.paragraph.outdent + '</td></tr>' +
                '<tr><td>⌘ + ENTER</td><td>' + lang.hr.insert + '</td></tr>';

            return tplShortcut(lang.shortcut.action, body);
        };

        var tplShortcutPara = function (lang) {
            var body = '<tr><td>⌘ + ⇧ + L</td><td>' + lang.paragraph.left + '</td></tr>' +
                '<tr><td>⌘ + ⇧ + E</td><td>' + lang.paragraph.center + '</td></tr>' +
                '<tr><td>⌘ + ⇧ + R</td><td>' + lang.paragraph.right + '</td></tr>' +
                '<tr><td>⌘ + ⇧ + J</td><td>' + lang.paragraph.justify + '</td></tr>' +
                '<tr><td>⌘ + ⇧ + NUM7</td><td>' + lang.lists.ordered + '</td></tr>' +
                '<tr><td>⌘ + ⇧ + NUM8</td><td>' + lang.lists.unordered + '</td></tr>';

            return tplShortcut(lang.shortcut.paragraphFormatting, body);
        };

        var tplShortcutStyle = function (lang) {
            var body = '<tr><td>⌘ + NUM0</td><td>' + lang.style.normal + '</td></tr>' +
                '<tr><td>⌘ + NUM1</td><td>' + lang.style.h1 + '</td></tr>' +
                '<tr><td>⌘ + NUM2</td><td>' + lang.style.h2 + '</td></tr>' +
                '<tr><td>⌘ + NUM3</td><td>' + lang.style.h3 + '</td></tr>' +
                '<tr><td>⌘ + NUM4</td><td>' + lang.style.h4 + '</td></tr>' +
                '<tr><td>⌘ + NUM5</td><td>' + lang.style.h5 + '</td></tr>' +
                '<tr><td>⌘ + NUM6</td><td>' + lang.style.h6 + '</td></tr>';

            return tplShortcut(lang.shortcut.documentStyle, body);
        };

        var tplExtraShortcuts = function (lang, options) {
            var extraKeys = options.extraKeys;
            var body = '';
            for (var key in extraKeys) {
                if (extraKeys.hasOwnProperty(key)) {
                    body += '<tr><td>' + key + '</td><td>' + extraKeys[key] + '</td></tr>';
                }
            }

            return tplShortcut(lang.shortcut.extraKeys, body);
        };

        var tplShortcutTable = function (lang, options) {
            var template = '<table class="note-shortcut-layout">' +
                '<tbody>' +
                '<tr><td>' + tplShortcutAction(lang, options) + '</td><td>' + tplShortcutText(lang, options) + '</td></tr>' +
                '<tr><td>' + tplShortcutStyle(lang, options) + '</td><td>' + tplShortcutPara(lang, options) + '</td></tr>';
            if (options.extraKeys) {
                template += '<tr><td colspan="2">' + tplExtraShortcuts(lang, options) + '</td></tr>';
            }
            template += '</tbody</table>';
            return template;
        };

        var replaceMacKeys = function (sHtml) {
            return sHtml.replace(/⌘/g, 'Ctrl').replace(/⇧/g, 'Shift');
        };

        var tplDialogs = function (lang, options) {
            var tplImageDialog = function () {
                var body =
                    '<div class="note-group-select-from-files">' +
                    '<h5>' + lang.image.selectFromFiles + '</h5>' +
                    '<input class="note-image-input" type="file" name="files" accept="image/*" />' +
                    '</div>' +
                    '<h5>' + lang.image.url + '</h5>' +
                    '<input class="note-image-url form-control span12" type="text" />';
                var footer = '<button href="#" class="btn btn-primary note-image-btn disabled" disabled>' + lang.image.insert + '</button>';
                return tplDialog('note-image-dialog', lang.image.insert, body, footer);
            };

            var tplLinkDialog = function () {
                var body = '<div class="form-group">' +
                    '<label>' + lang.link.textToDisplay + '</label>' +
                    '<input class="note-link-text form-control span12" type="text" />' +
                    '</div>' +
                    '<div class="form-group">' +
                    '<label>' + lang.link.url + '</label>' +
                    '<input class="note-link-url form-control span12" type="text" />' +
                    '</div>' +
                    (!options.disableLinkTarget ?
                        '<div class="checkbox">' +
                        '<label>' + '<input type="checkbox" checked> ' +
                        lang.link.openInNewWindow +
                        '</label>' +
                        '</div>' : ''
                    );
                var footer = '<button href="#" class="btn btn-primary note-link-btn disabled" disabled>' + lang.link.insert + '</button>';
                return tplDialog('note-link-dialog', lang.link.insert, body, footer);
            };
            var tplImageLinkDialog = function () {
                var body = '<div class="form-group">' +
                    '<label>' + lang.link.url + '</label>' +
                    '<input class="note-link-url form-control span12" type="text" />' +
                    '</div>' +
                    (!options.disableLinkTarget ?
                        '<div class="checkbox">' +
                        '<label>' + '<input type="checkbox" checked> ' +
                        lang.link.openInNewWindow +
                        '</label>' +
                        '</div>' : ''
                    );
                var footer = '<button href="#" class="btn btn-primary note-link-btn disabled" disabled>' + lang.image.insertLink + '</button>';
                return tplDialog('note-image-link-dialog', lang.image.insertLink, body, footer);
            };
            var tplVideoDialog = function () {
                var body = '<div class="form-group">' +
                    '<label>' + lang.video.url + '</label>&nbsp;<small class="text-muted">' + lang.video.providers + '</small>' +
                    '<input class="note-video-url form-control span12" type="text" />' +
                    '</div>';
                var footer = '<button href="#" class="btn btn-primary note-video-btn disabled" disabled>' + lang.video.insert + '</button>';
                return tplDialog('note-video-dialog', lang.video.insert, body, footer);
            };

            var tplHelpDialog = function () {
                var body = '<a class="modal-close pull-right" aria-hidden="true" tabindex="-1">' + lang.shortcut.close + '</a>' +
                    '<div class="title">' + lang.shortcut.shortcuts + '</div>' +
                    (agent.isMac ? tplShortcutTable(lang, options) : replaceMacKeys(tplShortcutTable(lang, options))) +
                    '<p class="text-center">' +
                    '<a href="//hackerwins.github.io/summernote/" target="_blank">Summernote 0.5.8</a> · ' +
                    '<a href="//github.com/HackerWins/summernote" target="_blank">Project</a> · ' +
                    '<a href="//github.com/HackerWins/summernote/issues" target="_blank">Issues</a>' +
                    '</p>';
                return tplDialog('note-help-dialog', '', body, '');
            };

            return '<div class="note-dialog">' +
                tplImageDialog() +
                tplLinkDialog() +
                tplVideoDialog() +
                tplHelpDialog() +
                '</div>';
        };

        var tplStatusbar = function () {
            return '<div class="note-resizebar">' +
                '<div class="note-icon-bar"></div>' +
                '<div class="note-icon-bar"></div>' +
                '<div class="note-icon-bar"></div>' +
                '</div>';
        };

        var representShortcut = function (str) {
            if (agent.isMac) {
                str = str.replace('CMD', '⌘').replace('SHIFT', '⇧');
            }

            return str.replace('BACKSLASH', '\\')
                .replace('SLASH', '/')
                .replace('LEFTBRACKET', '[')
                .replace('RIGHTBRACKET', ']');
        };

        /**
         * createTooltip
         *
         * @param {jQuery} $container
         * @param {Object} keyMap
         * @param {String} [sPlacement]
         */
        var createTooltip = function ($container, keyMap, sPlacement) {
            var invertedKeyMap = func.invertObject(keyMap);
            var $buttons = $container.find('button');

            $buttons.each(function (i, elBtn) {
                var $btn = $(elBtn);
                var sShortcut = invertedKeyMap[$btn.data('event')];
                if (sShortcut) {
                    $btn.attr('title', function (i, v) {
                        return v + ' (' + representShortcut(sShortcut) + ')';
                    });
                }
                // bootstrap tooltip on btn-group bug
                // https://github.com/twbs/bootstrap/issues/5687
            }).tooltip({
                container: 'body',
                trigger: 'hover',
                placement: sPlacement || 'top'
            }).on('click', function () {
                $(this).tooltip('hide');
            });
        };

        // createPalette
        var createPalette = function ($container, options) {
            var colorInfo = options.colors;
            $container.find('.note-color-palette').each(function () {
                var $palette = $(this), eventName = $palette.attr('data-target-event');
                var paletteContents = [];
                for (var row = 0, lenRow = colorInfo.length; row < lenRow; row++) {
                    var colors = colorInfo[row];
                    var buttons = [];
                    for (var col = 0, lenCol = colors.length; col < lenCol; col++) {
                        var color = colors[col];
                        buttons.push(['<button type="button" class="note-color-btn" style="background-color:', color,
                            ';" data-event="', eventName,
                            '" data-value="', color,
                            '" title="', color,
                            '" data-toggle="button" tabindex="-1"></button>'].join(''));
                    }
                    paletteContents.push('<div class="note-color-row">' + buttons.join('') + '</div>');
                }
                $palette.html(paletteContents.join(''));
            });
        };

        /**
         * create summernote layout (air mode)
         *
         * @param {jQuery} $holder
         * @param {Object} options
         */
        this.createLayoutByAirMode = function ($holder, options) {
            var keyMap = options.keyMap[agent.isMac ? 'mac' : 'pc'];
            var langInfo = $.summernote.lang[options.lang];

            var id = func.uniqueId();

            $holder.addClass('note-air-editor note-editable');
            $holder.attr({
                'id': 'note-editor-' + id,
                'contentEditable': true
            });

            var body = document.body;

            // create Popover
            var $popover = $(tplPopovers(langInfo, options));
            $popover.addClass('note-air-layout');
            $popover.attr('id', 'note-popover-' + id);
            $popover.appendTo(body);
            createTooltip($popover, keyMap);
            createPalette($popover, options);

            // create Handle
            var $handle = $(tplHandles());
            $handle.addClass('note-air-layout');
            $handle.attr('id', 'note-handle-' + id);
            $handle.appendTo(body);

            // create Dialog
            var $dialog = $(tplDialogs(langInfo, options));
            $dialog.addClass('note-air-layout');
            $dialog.attr('id', 'note-dialog-' + id);
            $dialog.find('button.close, a.modal-close').click(function () {
                $(this).closest('.modal').modal('hide');
            });
            $dialog.appendTo(body);
        };

        /**
         * create summernote layout (normal mode)
         *
         * @param {jQuery} $holder
         * @param {Object} options
         */
        this.createLayoutByFrame = function ($holder, options) {
            //01. create Editor
            var $editor = $('<div class="note-editor"></div>');
            if (options.width) {
                $editor.width(options.width);
            }

            //02. statusbar (resizebar)
            if (options.height > 0) {
                $('<div class="note-statusbar">' + (options.disableResizeEditor ? '' : tplStatusbar()) + '</div>').prependTo($editor);
            }

            //03. create Editable
            var isContentEditable = !$holder.is(':disabled');
            var $editable = $('<div class="note-editable" contentEditable="' + isContentEditable + '"></div>')
                .prependTo($editor);
            if (options.height) {
                $editable.height(options.height);
            }
            if (options.direction) {
                $editable.attr('dir', options.direction);
            }

            $editable.html(dom.html($holder) || dom.emptyPara);

            //031. create codable
            $('<textarea class="note-codable"></textarea>').prependTo($editor);

            var langInfo = $.summernote.lang[options.lang];

            //04. create Toolbar
            var toolbarHTML = '';
            for (var idx = 0, len = options.toolbar.length; idx < len; idx++) {
                var groupName = options.toolbar[idx][0];
                var groupButtons = options.toolbar[idx][1];

                toolbarHTML += '<div class="note-' + groupName + ' btn-group">';
                for (var i = 0, btnLength = groupButtons.length; i < btnLength; i++) {
                    // continue creating toolbar even if a button doesn't exist
                    if (!$.isFunction(tplButtonInfo[groupButtons[i]])) { continue; }
                    toolbarHTML += tplButtonInfo[groupButtons[i]](langInfo, options);
                }
                toolbarHTML += '</div>';
            }

            toolbarHTML = '<div class="note-toolbar btn-toolbar">' + toolbarHTML + '</div>';

            var $toolbar = $(toolbarHTML).prependTo($editor);
            var keyMap = options.keyMap[agent.isMac ? 'mac' : 'pc'];
            createPalette($toolbar, options);
            createTooltip($toolbar, keyMap, 'bottom');

            //05. create Popover
            var $popover = $(tplPopovers(langInfo, options)).prependTo($editor);
            createPalette($popover, options);
            createTooltip($popover, keyMap);

            //06. handle(control selection, ...)
            $(tplHandles()).prependTo($editor);

            //07. create Dialog
            var $dialog = $(tplDialogs(langInfo, options)).prependTo($editor);
            $dialog.find('button.close, a.modal-close').click(function () {
                $(this).closest('.modal').modal('hide');
            });

            //08. create Dropzone
            $('<div class="note-dropzone"><div class="note-dropzone-message"></div></div>').prependTo($editor);

            //09. Editor/Holder switch
            $editor.insertAfter($holder);
            $holder.hide();
        };

        this.noteEditorFromHolder = function ($holder) {
            if ($holder.hasClass('note-air-editor')) {
                return $holder;
            } else if ($holder.next().hasClass('note-editor')) {
                return $holder.next();
            } else {
                return $();
            }
        };

        /**
         * create summernote layout
         *
         * @param {jQuery} $holder
         * @param {Object} options
         */
        this.createLayout = function ($holder, options) {
            if (this.noteEditorFromHolder($holder).length) {
                return;
            }

            if (options.airMode) {
                this.createLayoutByAirMode($holder, options);
            } else {
                this.createLayoutByFrame($holder, options);
            }
        };

        /**
         * returns layoutInfo from holder
         *
         * @param {jQuery} $holder - placeholder
         * @returns {Object}
         */
        this.layoutInfoFromHolder = function ($holder) {
            var $editor = this.noteEditorFromHolder($holder);
            if (!$editor.length) { return; }

            var layoutInfo = dom.buildLayoutInfo($editor);
            // cache all properties.
            for (var key in layoutInfo) {
                if (layoutInfo.hasOwnProperty(key)) {
                    layoutInfo[key] = layoutInfo[key].call();
                }
            }
            return layoutInfo;
        };

        /**
         * removeLayout
         *
         * @param {jQuery} $holder - placeholder
         * @param {Object} layoutInfo
         * @param {Object} options
         *
         */
        this.removeLayout = function ($holder, layoutInfo, options) {
            if (options.airMode) {
                $holder.removeClass('note-air-editor note-editable')
                    .removeAttr('id contentEditable');

                layoutInfo.popover.remove();
                layoutInfo.handle.remove();
                layoutInfo.dialog.remove();
            } else {
                $holder.html(layoutInfo.editable.html());

                layoutInfo.editor.remove();
                $holder.show();
            }
        };
    };

    // jQuery namespace for summernote
    $.summernote = $.summernote || {};

    // extends default `settings`
    $.extend($.summernote, settings);

    var renderer = new Renderer();
    var eventHandler = new EventHandler();

    /**
     * extend jquery fn
     */
    $.fn.extend({
        /**
         * initialize summernote
         *  - create editor layout and attach Mouse and keyboard events.
         *
         * @param {Object} options
         * @returns {this}
         */
        summernote: function (options) {
            // extend default options
            options = $.extend({}, $.summernote.options, options);

            this.each(function (idx, elHolder) {
                var $holder = $(elHolder);

                // createLayout with options
                renderer.createLayout($holder, options);

                var info = renderer.layoutInfoFromHolder($holder);
                eventHandler.attach(info, options);

                // Textarea: auto filling the code before form submit.
                if (dom.isTextarea($holder[0])) {
                    $holder.closest('form').submit(function () {
                        $holder.val($holder.code());
                    });
                }
            });

            // focus on first editable element
            if (this.first().length && options.focus) {
                var info = renderer.layoutInfoFromHolder(this.first());
                info.editable.focus();
            }

            // callback on init
            if (this.length && options.oninit) {
                options.oninit();
            }

            return this;
        },
        // 

        /**
         * get the HTML contents of note or set the HTML contents of note.
         *
         * @param {String} [sHTML] - HTML contents(optional, set)
         * @returns {this|String} - context(set) or HTML contents of note(get).
         */
        code: function (sHTML) {
            // get the HTML contents of note
            if (sHTML === undefined) {
                var $holder = this.first();
                if (!$holder.length) { return; }
                var info = renderer.layoutInfoFromHolder($holder);
                if (!!(info && info.editable)) {
                    var isCodeview = info.editor.hasClass('codeview');
                    if (isCodeview && agent.hasCodeMirror) {
                        info.codable.data('cmEditor').save();
                    }
                    return isCodeview ? info.codable.val() : info.editable.html();
                }
                return dom.isTextarea($holder[0]) ? $holder.val() : $holder.html();
            }

            // set the HTML contents of note
            this.each(function (i, elHolder) {
                var info = renderer.layoutInfoFromHolder($(elHolder));
                if (info && info.editable) { info.editable.html(sHTML); }
            });

            return this;
        },

        /**
         * destroy Editor Layout and dettach Key and Mouse Event
         * @returns {this}
         */
        destroy: function () {
            this.each(function (idx, elHolder) {
                var $holder = $(elHolder);

                var info = renderer.layoutInfoFromHolder($holder);
                if (!info || !info.editable) { return; }

                var options = info.editor.data('options');

                eventHandler.dettach(info, options);
                renderer.removeLayout($holder, info, options);
            });

            return this;
        }
    });
}));
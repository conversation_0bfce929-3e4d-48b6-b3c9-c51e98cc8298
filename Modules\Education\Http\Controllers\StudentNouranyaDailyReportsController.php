<?php

namespace Modules\Education\Http\Controllers;

use App\StudentNouranyaReport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StudentNouranyaDailyReportsController
{
// StudentNouranyaReportsController.php



    public function getCurrentToLesson($studentId, $yearMonth)
    {

        $date = \Carbon\Carbon::createFromFormat('F Y', $yearMonth);
        $planYearAndMonth = $date->format('Y-m');


        $currentPlan = StudentNouranyaReport::where('student_id', $studentId)
            ->where('plan_year_and_month', $planYearAndMonth)
            ->latest('id')
            ->first(['to_lesson']);

        if ($currentPlan) {
            return response()->json(['success' => true, 'toLessonId' => $currentPlan->to_lesson]);
        }

        return response()->json(['success' => false, 'message' => 'No current plan found.'], 404);
    }

    public function updateToLesson(Request $request, $studentId)
    {


        $validatedData = $request->validate([
            'program_level_id' => 'required',
            'fromDate' => 'required',
            'classId' => 'required|integer|exists:classes,id',
            'centerId' => 'required|integer|exists:centers,id',
            'toLesson' => 'required|integer',
            // other fields if necessary...
        ]);

        DB::beginTransaction();
        try {
            $date = \Carbon\Carbon::createFromFormat('F Y', $validatedData['fromDate']);
            $planYearAndMonth = $date->format('Y-m');
            $planYearAndMonthDay =  $date->startOfMonth()->format('Y-m-d');

            // Check if there's an existing record with a different class ID
            $existingRecord = StudentNouranyaReport::where('student_id', $studentId)
                ->where('plan_year_and_month', $planYearAndMonth)
                ->first();

            if ($existingRecord && $existingRecord->class_id != $validatedData['classId']) {
                // Create a new record
                $nouranyaReport = StudentNouranyaReport::create([
                    'student_id' => $studentId,
                    'plan_year_and_month' => $planYearAndMonth,
                    'class_id' => $validatedData['classId'],
                    'to_lesson' => $validatedData['toLesson'],
                    'center_id' => $validatedData['centerId'],
                    'start_date' => $planYearAndMonthDay,
                    // Include additional fields that need updating...
                ]);
            } else {
                // Update or create the record
                $nouranyaReport = StudentNouranyaReport::updateOrCreate(
                    [
                        'student_id' => $studentId,
                        'plan_year_and_month' => $planYearAndMonth,
                        'class_id' => $validatedData['classId'],
                    ],
                    [
                        'to_lesson' => $validatedData['toLesson'],
                        'center_id' => $validatedData['centerId'],
                        'start_date' => $planYearAndMonthDay,
                        // Include additional fields that need updating...
                    ]
                );
            }






            DB::commit();
            return response()->json(['success' => true, 'message' => 'To lesson updated successfully.']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to update student's to_lesson: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update to_lesson. '.$e->getMessage()], 500);
        }
    }
    public function updateLineNumber(Request $request, $studentId)
    {
        $validatedData = $request->validate([
            'program_level_id' => 'required',
            'fromDate' => 'required',
            'classId' => 'required|integer|exists:classes,id',
            'centerId' => 'required|integer|exists:centers,id',
            'lineNumber' => 'required|string', // Assuming line numbers are strings
            'lessonId' => 'required|integer', // Validate the lesson ID
            // other fields if necessary...
        ]);

        DB::beginTransaction();
        try {
            $date = \Carbon\Carbon::parse($request->get('fromDate'));


            $planYearAndMonth = $date->format('Y-m');
            $planYearAndMonthDay  = $date->toDateString();

            // Check if there's an existing record with a different class ID
            $existingRecord = StudentNouranyaReport::where('student_id', $studentId)
                ->whereDate('created_at', $planYearAndMonthDay)
                ->first();

            if ($existingRecord) {
                // Update the existing record
                $existingRecord->update([
                    'plan_year_and_month'       => $planYearAndMonth,
                    'from_lesson_line_number'   => $validatedData['lineNumber'],
                    'center_id'                 => $validatedData['centerId'],
                    'lesson_id'                 => $validatedData['lessonId'],
                    // Include additional fields that need updating...
                ]);
            } else {
                // Create a new record
                StudentNouranyaReport::create([
                    'student_id'                => $studentId,
                    'plan_year_and_month'       => $planYearAndMonth,
                    'class_id'                  => $validatedData['classId'],
                    'from_lesson_line_number'   => $validatedData['lineNumber'],
                    'center_id'                 => $validatedData['centerId'],
                    'lesson_id'                 => $validatedData['lessonId'],
                    'organization_id'           => config('organization_id'),
                    // Any other fields needed...
                ]);
            }

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Line number and lesson updated successfully.']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to update student's line_number and lesson: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update line number and lesson. '.$e->getMessage()], 500);
        }
    }
    public function updateToLessonLineNumber(Request $request, $studentId)
    {
        $validatedData = $request->validate([
            'program_level_id' => 'required',
            'fromDate' => 'required',
            'classId' => 'required|integer|exists:classes,id',
            'centerId' => 'required|integer|exists:centers,id',
            'lineNumber' => 'required|string', // Assuming line numbers are strings
            'lessonId' => 'required|integer', // Validate the lesson ID
            // other fields if necessary...
        ]);

        DB::beginTransaction();
        try {
            $date = \Carbon\Carbon::parse($request->get('fromDate'));


            $planYearAndMonth = $date->format('Y-m');
            $planYearAndMonthDay  = $date->toDateString();

            // Check if there's an existing record with a different class ID
            $existingRecord = StudentNouranyaReport::where('student_id', $studentId)
                ->whereDate('created_at', $planYearAndMonthDay)
                ->first();

            if ($existingRecord) {
                // Update the existing record
                $existingRecord->update([
                    'plan_year_and_month'       => $planYearAndMonth,
                    'to_lesson_line_number'   => $validatedData['lineNumber'],
                    'center_id'                 => $validatedData['centerId'],
                    'lesson_id'                 => $validatedData['lessonId'],
                    // Include additional fields that need updating...
                ]);
            } else {
                // Create a new record
                StudentNouranyaReport::create([
                    'student_id'                => $studentId,
                    'plan_year_and_month'       => $planYearAndMonth,
                    'class_id'                  => $validatedData['classId'],
                    'to_lesson_line_number'   => $validatedData['lineNumber'],
                    'center_id'                 => $validatedData['centerId'],
                    'lesson_id'                 => $validatedData['lessonId'],
                    'organization_id'           => config('organization_id'),
                    // Any other fields needed...
                ]);
            }



            DB::commit();
            return response()->json(['success' => true, 'message' => 'To lesson line number updated successfully.']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to update student's to_lesson_line_number: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update to lesson line number. '.$e->getMessage()], 500);
        }
    }

}
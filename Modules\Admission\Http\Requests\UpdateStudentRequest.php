<?php

namespace Modules\Admission\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Log;

class UpdateStudentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $studentUserId = $this->route('studentUserId');
        
        return (
            (auth()->guard('student')->check() && auth()->user()->id == $studentUserId) || 
            auth()->guard('guardian')->check() || 
            auth()->guard('employee')->check()
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $roles = [];

        // Log that we're building validation rules
        Log::debug('Building validation rules for student update', [
            'config_settings' => [
                'full_name' => config("settings.student_form_full_name"),
                'full_name_trans' => config("settings.student_form_full_name_trans"),
                'gender' => config("settings.student_form_gender"),
            ]
        ]);

        if (config("settings.student_form_full_name") == "required") {
            $roles["full_name"] = "required";
        }
        
        if (config("settings.student_form_full_name_trans") == "required") {
            $roles["full_name_trans"] = "required";
        }
        
        if (config("settings.student_form_full_name_language") == "required") {
            $roles["full_name_language"] = "required";
        }
        
        if (config("settings.student_form_gender") == "required") {
            $roles["gender"] = "required";
        }
        
        if (config("settings.student_form_date_of_birth") == "required") {
            $roles["date_of_birth"] = "required";
        }
        
        if (config("settings.student_form_identity_number") == "required") {
            $roles["identity_number"] = "required";
        }
        
        if (config("settings.student_form_identity") == "required") {
            $roles["identity"] = "required|mimes:jpeg,jpg,bmp,png,gif,svg,pdf,zip|max:5000";
        }
        
        // Add image validation - this is for file uploads directly (not LFM)
        $roles["image"] = "nullable|image|max:2048";
        
        if (config("settings.student_form_nationality") == "required") {
            $roles["nationality"] = "required";
        }
        
        if (config("settings.student_form_mobile") == "required") {
            $roles["mobile"] = "required";
        }

        return $roles;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'image.image' => 'The file must be an image (jpeg, png, bmp, gif, svg, or webp)',
            'image.max' => 'The image may not be greater than 2MB',
            'identity.mimes' => 'The identity document must be a file of type: jpeg, jpg, bmp, png, gif, svg, pdf, zip',
            'identity.max' => 'The identity document may not be greater than 5MB',
        ];
    }
} 
@extends('layouts.hound')
@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">Create Class Report1</div>
                <div class="panel-body">
                    <a href="{{ url('workplace/education/classes/'.$class->id.'/reports') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
                    <br />
                    <br />
                    @if ($errors->any())
                    <ul class="alert alert-danger">
                        @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    @endif
                    <div class="row">
                        {!! Form::open(['route' => 'class.reports.store' ]) !!}

                        {!! Form::hidden('class_id' , $class->id) !!}
                        <div class="col-md-6">
                            <label for="">Class Name</label>
                            <div class="form-control">
                                {{ $class->name }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="">Teacher Name</label>
                            {!! Form::select('employee_id' , $teachers , null , ['class'=> 'form-control']) !!}
                        </div>
                        <div class="col-md-6">
                            <label for="">Subject</label>
                            {!! Form::select('subject_id' , $subjects , null , ['class'=> 'form-control']) !!}
                        </div>

                        <div class="col-md-6">
                            <label for="">Class Date and Time</label>
                            <input name='class_time' id="class_time" class='form-control datetime' value="" required>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-success">Start Reporting</button>
                        </div>
                        {!! Form::close() !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@include('jssnippets.flatpickr')
@endsection
@section('js')
<script>
    $(document).ready(function(){
        flatpickr('.datetime', {
            enableTime: true,
            minDate: "today",
            "plugins": [new confirmDatePlugin({})]
        });

        $('form').submit(function(e){
            if(!$('#class_time').val()){
                e.preventDefault();
                alert('Enter Class Time');
            }
        })
    })
</script>
@append
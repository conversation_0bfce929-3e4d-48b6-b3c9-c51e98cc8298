<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * JobSyncAlertRule Entity
 * 
 * Manages proactive alerting rules for job sync command monitoring
 * 
 * @property int $id
 * @property string $alert_type
 * @property string $command
 * @property array $threshold
 * @property int $window_minutes
 * @property bool $enabled
 * @property array $recipients
 * @property array|null $escalation_policy
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $recipients_list
 * @property-read string $alert_type_badge
 * @property-read string $status_badge
 * @property-read Collection $alertEvents
 */
final class JobSyncAlertRule extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'job_sync_alert_rules';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'alert_type',
        'command',
        'threshold',
        'window_minutes',
        'enabled',
        'recipients',
        'escalation_policy',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'threshold' => 'array',
        'window_minutes' => 'integer',
        'enabled' => 'boolean',
        'recipients' => 'array',
        'escalation_policy' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Alert type constants
     */
    public const ALERT_TYPE_ZERO_JOBS = 'zero_jobs';
    public const ALERT_TYPE_DURATION_SPIKE = 'duration_spike';
    public const ALERT_TYPE_ERROR_PATTERN = 'error_pattern';
    public const ALERT_TYPE_CATEGORY_DROP = 'category_drop';

    /**
     * Get the alert events for this rule
     *
     * @return HasMany
     */
    public function alertEvents(): HasMany
    {
        return $this->hasMany(JobSyncAlertEvent::class, 'rule_id');
    }

    /**
     * Get the command schedule rule for this command
     *
     * @return BelongsTo
     */
    public function commandScheduleRule(): BelongsTo
    {
        return $this->belongsTo(CommandScheduleRule::class, 'command', 'command');
    }

    /**
     * Get recipients as comma-separated string
     *
     * @return string
     */
    public function getRecipientsListAttribute(): string
    {
        return implode(', ', $this->recipients);
    }

    /**
     * Get alert type badge HTML
     *
     * @return string
     */
    public function getAlertTypeBadgeAttribute(): string
    {
        switch ($this->alert_type) {
            case self::ALERT_TYPE_ZERO_JOBS:
                return '<span class="badge bg-warning">Zero Jobs</span>';
            case self::ALERT_TYPE_DURATION_SPIKE:
                return '<span class="badge bg-info">Duration Spike</span>';
            case self::ALERT_TYPE_ERROR_PATTERN:
                return '<span class="badge bg-danger">Error Pattern</span>';
            case self::ALERT_TYPE_CATEGORY_DROP:
                return '<span class="badge bg-secondary">Category Drop</span>';
            default:
                return '<span class="badge bg-dark">Unknown</span>';
        }
    }

    /**
     * Get status badge HTML
     *
     * @return string
     */
    public function getStatusBadgeAttribute(): string
    {
        if ($this->enabled) {
            return '<span class="badge bg-success">Enabled</span>';
        } else {
            return '<span class="badge bg-secondary">Disabled</span>';
        }
    }

    /**
     * Scope for enabled rules
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    /**
     * Scope for specific command
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $command
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCommand($query, string $command)
    {
        return $query->where('command', $command);
    }

    /**
     * Check if threshold is met based on current conditions
     *
     * @param array $currentData
     * @return bool
     */
    public function isThresholdMet(array $currentData): bool
    {
        switch ($this->alert_type) {
            case self::ALERT_TYPE_ZERO_JOBS:
                $consecutiveRuns = $this->threshold['consecutive_runs'] ?? 3;
                return CommandScheduleExecution::hasConsecutiveZeroJobs($this->command, $consecutiveRuns);

            case self::ALERT_TYPE_DURATION_SPIKE:
                $multiplier = $this->threshold['duration_multiplier'] ?? 2.0;
                $currentDuration = $currentData['duration_seconds'] ?? 0;
                $averageDuration = $currentData['average_duration'] ?? 0;
                
                return $averageDuration > 0 && $currentDuration > ($averageDuration * $multiplier);

            case self::ALERT_TYPE_ERROR_PATTERN:
                $errorCount = $this->threshold['error_count'] ?? 3;
                $windowMinutes = $this->window_minutes;
                
                $since = Carbon::now()->subMinutes($windowMinutes);
                $recentErrors = CommandScheduleExecution::where('command', $this->command)
                    ->where('started_at', '>=', $since)
                    ->where('error_type', '!=', CommandScheduleExecution::ERROR_TYPE_NONE)
                    ->count();
                
                return $recentErrors >= $errorCount;

            case self::ALERT_TYPE_CATEGORY_DROP:
                $dropPercentage = $this->threshold['drop_percentage'] ?? 50;
                $currentJobsCount = $currentData['total_jobs'] ?? 0;
                $baselineCount = $currentData['baseline_jobs'] ?? 0;
                
                if ($baselineCount == 0) {
                    return false;
                }
                
                $actualDropPercentage = (($baselineCount - $currentJobsCount) / $baselineCount) * 100;
                return $actualDropPercentage >= $dropPercentage;

            default:
                return false;
        }
    }

    /**
     * Check if rule can fire (enabled and not in cooldown)
     *
     * @return bool
     */
    public function canFire(): bool
    {
        if (!$this->enabled) {
            return false;
        }

        // Check if there's a recent alert (within cooldown period)
        $cooldownMinutes = $this->escalation_policy['cooldown_minutes'] ?? 60;
        $since = Carbon::now()->subMinutes($cooldownMinutes);
        
        $recentAlert = $this->alertEvents()
            ->where('fired_at', '>=', $since)
            ->where('status', '!=', JobSyncAlertEvent::STATUS_RESOLVED)
            ->exists();

        return !$recentAlert;
    }

    /**
     * Get active rules for a specific command
     *
     * @param string $command
     * @return Collection
     */
    public static function getActiveRulesForCommand(string $command): Collection
    {
        return static::enabled()
            ->forCommand($command)
            ->get();
    }

    /**
     * Evaluate all rules against current execution data
     *
     * @param string $command
     * @param array $executionData
     * @return Collection
     */
    public static function evaluateRules(string $command, array $executionData): Collection
    {
        $activeRules = static::getActiveRulesForCommand($command);
        $triggeredRules = collect();

        foreach ($activeRules as $rule) {
            if ($rule->canFire() && $rule->isThresholdMet($executionData)) {
                $triggeredRules->push($rule);
            }
        }

        return $triggeredRules;
    }

    /**
     * Get threshold description for display
     *
     * @return string
     */
    public function getThresholdDescription(): string
    {
        switch ($this->alert_type) {
            case self::ALERT_TYPE_ZERO_JOBS:
                $count = $this->threshold['consecutive_runs'] ?? 3;
                return "{$count} consecutive runs with zero jobs";

            case self::ALERT_TYPE_DURATION_SPIKE:
                $multiplier = $this->threshold['duration_multiplier'] ?? 2.0;
                return "{$multiplier}x average execution time";

            case self::ALERT_TYPE_ERROR_PATTERN:
                $count = $this->threshold['error_count'] ?? 3;
                return "{$count} errors within {$this->window_minutes} minutes";

            case self::ALERT_TYPE_CATEGORY_DROP:
                $percentage = $this->threshold['drop_percentage'] ?? 50;
                return "{$percentage}% drop in job category counts";

            default:
                return 'Custom threshold';
        }
    }

    /**
     * Get provider name from command
     *
     * @return string
     */
    public function getProviderName(): string
    {
        switch ($this->command) {
            case 'jobseeker:sync-jobs-af':
                return 'Jobs.af';
            case 'jobseeker:sync-acbar-jobs':
                return 'ACBAR';
            default:
                return 'Unknown';
        }
    }

    /**
     * Get unresolved alert count for this rule
     *
     * @return int
     */
    public function getUnresolvedAlertCount(): int
    {
        return $this->alertEvents()
            ->whereIn('status', [JobSyncAlertEvent::STATUS_NEW, JobSyncAlertEvent::STATUS_ACKNOWLEDGED])
            ->count();
    }

    /**
     * Enable the alert rule
     *
     * @return bool
     */
    public function enable(): bool
    {
        $this->update(['enabled' => true]);

        Log::info('JobSyncAlertRule: Rule enabled', [
            'rule_id' => $this->id,
            'alert_type' => $this->alert_type,
            'command' => $this->command
        ]);

        return true;
    }

    /**
     * Disable the alert rule
     *
     * @return bool
     */
    public function disable(): bool
    {
        $this->update(['enabled' => false]);

        Log::info('JobSyncAlertRule: Rule disabled', [
            'rule_id' => $this->id,
            'alert_type' => $this->alert_type,
            'command' => $this->command
        ]);

        return true;
    }
} 
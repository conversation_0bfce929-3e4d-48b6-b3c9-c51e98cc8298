<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Matrix\Builder;

class StudentHefzReport extends Model
{

    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'student_hefz_report';

    /**
     * The fillable columns
     */
    protected $fillable = [
      'created_at',
      'updated_at',
      'report_date',
      'class_id',
      'organization_id',
      'student_id',
      'class_report_id',
      'hefz_from_surat',
      'hefz_from_ayat',
      'hefz_to_surat',
      'hefz_to_ayat',
      'hefz_evaluation_id',
      'hefz_evaluation_note',
        'attendance_id',
        'hefz_plan_id',
        'program_id',
        'teacher_id',
        'from_surat_juz_id',
        'to_surat_juz_id',
        'delete_reason',
        'deleted_at',
        'pages_memorized'

    ];
    // Define a local scope in the StudentHefzReport model
    public function scopeDistinctMonthsYears($query) {
        return $query->selectRaw('DISTINCT MONTHNAME(created_at) as month, YEAR(created_at) as year')
            ->orderBy('created_at', 'desc');
    }

    public function scopeCurrentMonthAndYear($query)
    {
        $currentMonth = now()->month;
        $currentYear = now()->year;

        $query->where(function($q) use ($currentMonth, $currentYear) {
            $q->whereYear('created_at', $currentYear)
                ->whereMonth('created_at', $currentMonth)

            ;
        });
    }

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';
    protected $casts = [
        'created_at' => 'datetime',
        'report_date' => 'date'
    ];
//    public $timestamps = false;


    public function getPagesMemorizedAttribute()
    {



        if ($this->hefzPlan['study_direction'] == 'backward') {

            $memorizedNumberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                $this->hefz_from_surat,
                $this->hefz_from_ayat,
                $this->hefz_to_surat,
                $this->hefz_to_ayat
            ]);


            $memorizedNumberofPages = $memorizedNumberofPages[0]->numberofPagesSum;

        }

        else {
            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                $this->hefz_from_surat,
                $this->hefz_from_ayat,
                $this->hefz_to_surat,
                $this->hefz_to_ayat
            ]);

            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
            $memorizedNumberofPages = $results[0]->number_of_pages_sum;

        }


        $memorizedNumberofPages = isset($memorizedNumberofPages) ? $memorizedNumberofPages : 0;


        return $memorizedNumberofPages;

    }

    public function fromSurat(){


        return $this->belongsTo(MoshafSurah::class,'hefz_from_surat','id');
    }

    public function toSurat(){


        return $this->belongsTo(MoshafSurah::class,'hefz_to_surat','id');
    }

    public function result()
    {
        return $this->hasOne('App\EvaluationSchemaOption', 'id', 'hefz_evaluation_id');
    }

    public function student()
    {
        return $this->belongsTo('App\Student');
//        return $this->belongsTo(Student::class,'student_id','id');
    }
    public function attendanceOptions()
    {
        return $this->belongsTo(AttendanceOption::class,'attendance_id','id');
    }
    public function moshafPage()
    {
        return $this->belongsTo(MoshafPage::class,'hefz_to_surah','id');
    }
    public function moshafSurah()
    {
        return $this->belongsTo(MoshafSurah::class,'hefz_to_surah','id');
    }

    public function moshafJuz()
    {
        return $this->belongsTo(MoshafJuz::class,'juz','juz_id');
    }



    public function classes()
    {

        return $this->belongsTo(Classes::class,'class_id','id');
    }
    public function scopeAfter(Builder $query, string $date): Builder{

        return $query->where($this->qualifyColumn('created_at'),'>=',$date.' 00:00:00');

        // example query could be StudentHefzReport::query()->after('2022-02-14')->before('2022-02-21')->get()
    }
    public function scopeBefore(Builder $query, string $date): Builder{

        return $query->where($this->qualifyColumn('created_at'),'<=',$date.' 23:59:59');

        // example query could be StudentHefzReport::query()->after('2022-02-14')->before('2022-02-21')->get()
    }


    public function hefzPlan(){

        return $this->belongsTo(StudentHefzPlan::class,'hefz_plan_id','id');
    }
    public function revisionPlan(){

        return $this->belongsTo(StudentRevisionPlan::class,'revision_plan_id','id');
    }

    public function hefzLevel(){


        return $this->belongsTo(HefzLevel::class,'hefz_to_surat','surah_id');
    }
}

<?php

namespace Modules\Site\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Student;
use App\Guardian;
use App\Center;
use App\Program;


class StudentController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index($student_id = null)
    {
        if ($student_id) {
            $student    = Student::findOrFail($student_id);
        } else {
            $student    = auth()->user();
        }

        $centers    = Center::where('status', '=', 'active')->get()->pluck('name', 'id');

        $programs   = Program::where('status', '=', 'active')->get()->pluck('title', 'id');

        return view(theme_path("student.index"), compact('student', 'centers', 'programs'));
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view(theme_path("student.create"));
    }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $this->validation($request);
        $requestData = $request->all();
        if (auth()->guard('guardian')->check()) {
            $new_email = $request->identity_number . '.' . $request->full_name . '@itqanalquran.org';
            $requestData['guardian_id'] =  auth()->user()->id;
            $requestData['password'] = bcrypt(request('email'));
            $requestData['email'] = $new_email;
        }
        if ($request->hasFile('image')) {
            $imageName = \Illuminate\Support\Str::random(9) . '.' . $request->file('image')->getClientOriginalExtension();

            $path = 'userfiles/' . userfolder('110') . '/' . \Illuminate\Support\Str::random(9) . '_profile/';

            $request->file('image')->move(
                base_path() . '/public/' . $path,
                $imageName
            );

            $requestData['image'] = $path . $imageName;
        }
        if ($request->hasFile('identity')) {
            $identityName = \Illuminate\Support\Str::random(9) . '.' . $request->file('identity')->getClientOriginalExtension();

            $path = 'userfiles/' . userfolder('110') . '/' . \Illuminate\Support\Str::random(9) . '_identity/';

            $request->file('identity')->move(
                base_path() . '/public/' . $path,
                $identityName
            );

            $requestData['identity'] = $path . $identityName;
        }

        $requestData['organization_id'] = config('organization_id');

        $request->merge([
            "organization_id" => config('organization_id'),
        ]);

        $student = Student::create($requestData);


        if (auth()->guard('guardian')->check()) {
            flash('Student registered successfully!');
            //return redirect()->back()->with(compact('student')) ;
            $centers    = Center::get()->pluck('name', 'id');

            $programs   = Program::get()->pluck('title', 'id');

            return view(theme_path("guardian.register_student"), compact('student', 'centers', 'programs'));
        }
        return redirect()->back();
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show()
    {
        return view(theme_path("show"));
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit()
    {
        return view(theme_path("edit"));
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(Request $request, $id = null)
    {
        if (
            (auth()->guard('student')->check() && auth()->user()->id == $id)
            || (auth()->guard('guardian')->check() && is_guardian_of_student(auth()->user(), $id))
        ) {
            $roles = [];


            if (isset($request->update_profile)) {
                $this->validation($request);

                $requestData = $request->all();

                if ($request->hasFile('image')) {
                    $imageName = \Illuminate\Support\Str::random(9) . '.' . $request->file('image')->getClientOriginalExtension();

                    $path = 'userfiles/' . userfolder($id) . '/' . \Illuminate\Support\Str::random(9) . '_profile/';

                    $request->file('image')->move(
                        base_path() . '/public/' . $path,
                        $imageName
                    );

                    $requestData['image'] = $path . $imageName;
                }


                if ($request->hasFile('identity')) {
                    $identityName = \Illuminate\Support\Str::random(9) . '.' . $request->file('identity')->getClientOriginalExtension();

                    $path = 'userfiles/' . userfolder($id) . '/' . \Illuminate\Support\Str::random(9) . '_identity/';

                    $request->file('identity')->move(
                        base_path() . '/public/' . $path,
                        $identityName
                    );

                    $requestData['identity'] = $path . $identityName;
                }

                $student = Student::findOrFail($id);
                if ($student->status != 'active') {

                    $requestData['status'] = 'update_guardian';
                }

                $student->fill($requestData);

                $student->save();

                flash('Profile updated!!');
                return redirect()->back();
            }
        }
    }

    private function validation($request)
    {
        if (auth()->guard('guardian')->check()) {
            $roles["email"] = "required|email";
        }

        if (config("settings.student_form_full_name") == "required") {
            $roles["full_name"] = "required";
        }
        if (config("settings.student_form_full_name_trans") == "required") {
            $roles["full_name_trans"] = "required";
        }
        if (config("settings.student_form_full_name_language") == "required") {
            $roles["full_name_language"] = "required";
        }
        if (config("settings.student_form_gender") == "required") {
            $roles["gender"] = "required";
        }
        if (config("settings.student_form_date_of_birth") == "required") {
            $roles["date_of_birth"] = "required|date";
        }
        if (config("settings.student_form_identity_number") == "required") {
            $roles["identity_number"] = "required";
        }
        if (config("settings.student_form_identity") == "required") {
            $roles["identity"] = "required| mimes:jpeg,jpg,bmp,png,gif,svg,pdf | max:5000";
        }
        if (config("settings.student_form_image") == "required") {
            $roles["image"] = "required|image| max:3000";
        }
        if (config("settings.student_form_nationality") == "required") {
            $roles["nationality"] = "required";
        }
        if (config("settings.student_form_mobile") == "required") {
            $roles["mobile"] = "required";
        }

        $this->validate($request, $roles);
    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }

    public function studentsetting()
    {
        $student_id = null;
        if ($student_id) {
            $student    = Student::findOrFail($student_id);
        } else {
            $student    = auth()->user();
        }

        $centers    = Center::where('status', '=', 'active')->get()->pluck('name', 'id');

        $programs   = Program::where('status', '=', 'active')->get()->pluck('title', 'id');

        return view(theme_path("student.setting"), compact('student', 'centers', 'programs'));
    }
    // update student profile from setting 





}

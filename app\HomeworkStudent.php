<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\HomeworkStudent
 *
 * @property int $id
 * @property string|null $marks
 * @property string|null $teacher_comments
 * @property string|null $complete_status
 * @property int $active_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $student_id
 * @property int|null $homework_id
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $organization_id
 * @property int|null $academic_id
 * @property-read \App\Student|null $studentInfo
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent query()
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereAcademicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereActiveStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereCompleteStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereHomeworkId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereMarks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereStudentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereTeacherComments($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|HomeworkStudent whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class HomeworkStudent extends Model
{
    protected $table= "homework_students";
    public function studentInfo(){
    	return $this->belongsTo('App\Student', 'student_id', 'id');
    }
}

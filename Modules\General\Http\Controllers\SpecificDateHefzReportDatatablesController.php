<?php

namespace Modules\General\Http\Controllers;


use App\Admission;
use App\Center;
use App\CenterEmployee;
use App\Scopes\OrganizationScope;
use App\Student;
use App\StudentHefzPlan;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use Carbon\Carbon;
use Illuminate\Http\Request;

use App\Http\Controllers\Controller;


class SpecificDateHefzReportDatatablesController extends Controller
{

    public function getDetails(Request $request,$classId,$hefzDate)
    {




        if ($request->ajax()) {


            $studentsApprovalQuery = StudentHefzReport::whereYear('class_time', \Carbon\Carbon::parse($hefzDate)->year)
                ->whereMonth('class_time',\Carbon\Carbon::parse($hefzDate)->month)
                ->whereNotNull('class_id')
                ->where('class_id', $classId)
                ->with('student')
                ->select();



            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
                ->addColumn('studentName', function ($row) {
                    return $row->student->full_name;
                })
                ->addColumn('fromSuratAyat', function ($row) {
                    return '<a data-placement="top" href="#" data-toggle="tooltip" title="' . $row->fromSurat->name . '">' . $row->fromSurat->name . ' - </a>' . $row->revision_from_ayat;
                })
                ->addColumn('toSuratAyat', function ($row) {
                    return '<a data-placement="top" href="#" data-toggle="tooltip" title="' . $row->toSurat->name . '">' . $row->toSurat->name . ' - </a>' . $row->revision_to_ayat;
                })
                ->addColumn('created_at', function ($row) {
                    return optional($row->class_time)->toDateString();
                })
                ->addColumn('remarks', function ($row) {

                    return optional(value($row['hefz_evaluation_note']))->diffForHumans();
                })
                ->rawColumns(['fromSuratAyat', 'toSuratAyat'])
                ->toJson();

        }


    }
}


//            }







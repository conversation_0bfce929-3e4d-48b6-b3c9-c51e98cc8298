<?php

use Illuminate\Database\Seeder;

class WorldCountriesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('world_countries')->truncate();
        
        \DB::table('world_countries')->insert(array (
            0 => 
            array (
                'callingcode' => '237',
                'capital' => 'Yaoundé',
                'code' => 'cm',
                'code_alpha3' => 'cmr',
                'continent_id' => 3,
                'currency_code' => 'XAF',
                'currency_name' => 'CFA Franc BEAC',
                'emoji' => '🇨🇲',
                'full_name' => 'the Republic of Cameroon',
                'has_division' => 0,
                'id' => 1,
                'name' => 'Cameroon',
                'tld' => '.cm',
            ),
            1 => 
            array (
                'callingcode' => '229',
                'capital' => 'Porto-Novo',
                'code' => 'bj',
                'code_alpha3' => 'ben',
                'continent_id' => 3,
                'currency_code' => 'XOF',
                'currency_name' => 'CFA Franc BCEAO',
                'emoji' => '🇧🇯',
                'full_name' => 'the Republic of Benin ',
                'has_division' => 0,
                'id' => 2,
                'name' => 'Benin',
                'tld' => '.bj',
            ),
            2 => 
            array (
                'callingcode' => '261',
                'capital' => 'Antananarivo',
                'code' => 'mg',
                'code_alpha3' => 'mdg',
                'continent_id' => 3,
                'currency_code' => 'MGA',
                'currency_name' => 'Malagasy Ariary',
                'emoji' => '🇲🇬',
                'full_name' => 'the Republic of Madagascar ',
                'has_division' => 0,
                'id' => 3,
                'name' => 'Madagascar',
                'tld' => '.mg',
            ),
            3 => 
            array (
                'callingcode' => '250',
                'capital' => 'Kigali',
                'code' => 'rw',
                'code_alpha3' => 'rwa',
                'continent_id' => 3,
                'currency_code' => 'RWF',
                'currency_name' => 'Rwanda Franc',
                'emoji' => '🇷🇼',
                'full_name' => 'the Republic of Rwanda',
                'has_division' => 0,
                'id' => 4,
                'name' => 'Rwanda',
                'tld' => '.rw',
            ),
            4 => 
            array (
                'callingcode' => '248',
                'capital' => 'Victoria',
                'code' => 'sc',
                'code_alpha3' => 'syc',
                'continent_id' => 3,
                'currency_code' => 'SCR',
                'currency_name' => 'Seychelles Rupee',
                'emoji' => '🇸🇨',
                'full_name' => 'the Republic of Seychelles',
                'has_division' => 0,
                'id' => 5,
                'name' => 'Seychelles',
                'tld' => '.sc',
            ),
            5 => 
            array (
                'callingcode' => '225',
                'capital' => 'Yamoussoukro',
                'code' => 'ci',
                'code_alpha3' => 'civ',
                'continent_id' => 3,
                'currency_code' => 'XOF',
                'currency_name' => 'CFA Franc BCEAO',
                'emoji' => '🇨🇮',
                'full_name' => 'the Republic of Cote d\'ivoire',
                'has_division' => 0,
                'id' => 6,
                'name' => 'Cote d\'lvoire',
                'tld' => '.ci',
            ),
            6 => 
            array (
                'callingcode' => '20',
                'capital' => 'Cairo',
                'code' => 'eg',
                'code_alpha3' => 'egy',
                'continent_id' => 3,
                'currency_code' => 'EGP',
                'currency_name' => 'Egyptian Pound',
                'emoji' => '🇪🇬',
                'full_name' => 'the Arab Republic of Egypt ',
                'has_division' => 0,
                'id' => 7,
                'name' => 'Egypt',
                'tld' => '.eg',
            ),
            7 => 
            array (
                'callingcode' => '230',
                'capital' => 'Port Louis',
                'code' => 'mu',
                'code_alpha3' => 'mus',
                'continent_id' => 3,
                'currency_code' => 'MUR',
                'currency_name' => 'Mauritius Rupee',
                'emoji' => '🇲🇺',
                'full_name' => 'the Republic of Mauritius',
                'has_division' => 0,
                'id' => 8,
                'name' => 'Mauritius',
                'tld' => '.mu',
            ),
            8 => 
            array (
                'callingcode' => '226',
                'capital' => 'Ouagadougou',
                'code' => 'bf',
                'code_alpha3' => 'bfa',
                'continent_id' => 3,
                'currency_code' => 'XOF',
                'currency_name' => 'CFA Franc BCEAO',
                'emoji' => '🇧🇫',
                'full_name' => 'Burkina Faso',
                'has_division' => 0,
                'id' => 9,
                'name' => 'Burkina Faso',
                'tld' => '.bf',
            ),
            9 => 
            array (
                'callingcode' => '291',
                'capital' => 'Asmara',
                'code' => 'er',
                'code_alpha3' => 'eri',
                'continent_id' => 3,
                'currency_code' => 'ERN',
                'currency_name' => 'Nakfa',
                'emoji' => '🇪🇷',
                'full_name' => 'the Commonwealth of Eritrea',
                'has_division' => 0,
                'id' => 10,
                'name' => 'Eritrea',
                'tld' => '.er',
            ),
            10 => 
            array (
                'callingcode' => '239',
                'capital' => 'São Tomé',
                'code' => 'st',
                'code_alpha3' => 'stp',
                'continent_id' => 3,
                'currency_code' => 'STD',
                'currency_name' => 'Dobra',
                'emoji' => '🇸🇹',
                'full_name' => 'the Democratic Republic Sao Tome and Principe',
                'has_division' => 0,
                'id' => 11,
                'name' => 'Sao Tome and Principe',
                'tld' => '.st',
            ),
            11 => 
            array (
                'callingcode' => '244',
                'capital' => 'Luanda',
                'code' => 'ao',
                'code_alpha3' => 'ago',
                'continent_id' => 3,
                'currency_code' => 'AOA',
                'currency_name' => 'Kwanza',
                'emoji' => '🇦🇴',
                'full_name' => 'the Republic of Angola',
                'has_division' => 0,
                'id' => 12,
                'name' => 'Angola',
                'tld' => '.ao',
            ),
            12 => 
            array (
                'callingcode' => '218',
                'capital' => 'Tripoli',
                'code' => 'ly',
                'code_alpha3' => 'lby',
                'continent_id' => 3,
                'currency_code' => 'LYD',
                'currency_name' => 'Libyan Dinar',
                'emoji' => '🇱🇾',
                'full_name' => 'State of Libya',
                'has_division' => 0,
                'id' => 13,
                'name' => 'Libyan Arab Jm',
                'tld' => '.ly',
            ),
            13 => 
            array (
                'callingcode' => '263',
                'capital' => 'Harare',
                'code' => 'zw',
                'code_alpha3' => 'zwe',
                'continent_id' => 3,
                'currency_code' => 'ZWL',
                'currency_name' => 'Zimbabwe Dollar',
                'emoji' => '🇿🇼',
                'full_name' => 'the Republic of Zimbabwe',
                'has_division' => 0,
                'id' => 14,
                'name' => 'Zimbabwe',
                'tld' => '.zw',
            ),
            14 => 
            array (
                'callingcode' => '224',
                'capital' => 'Conakry',
                'code' => 'gn',
                'code_alpha3' => 'gin',
                'continent_id' => 3,
                'currency_code' => 'GNF',
                'currency_name' => 'Guinea Franc',
                'emoji' => '🇬🇳',
                'full_name' => 'The Republic of Guinea',
                'has_division' => 0,
                'id' => 15,
                'name' => 'Guinea',
                'tld' => '.gn',
            ),
            15 => 
            array (
                'callingcode' => '232',
                'capital' => 'Freetown',
                'code' => 'sl',
                'code_alpha3' => 'sle',
                'continent_id' => 3,
                'currency_code' => 'SLL',
                'currency_name' => 'Leone',
                'emoji' => '🇸🇱',
                'full_name' => 'The Republic of Sierra Leone',
                'has_division' => 0,
                'id' => 16,
                'name' => 'Sierra Leone',
                'tld' => '.sl',
            ),
            16 => 
            array (
                'callingcode' => '262',
                'capital' => 'Saint-Denis',
                'code' => 're',
                'code_alpha3' => 'reu',
                'continent_id' => 3,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇷🇪',
                'full_name' => 'Reunion Island',
                'has_division' => 0,
                'id' => 17,
                'name' => 'Reunion',
                'tld' => '.re',
            ),
            17 => 
            array (
                'callingcode' => '241',
                'capital' => 'Libreville',
                'code' => 'ga',
                'code_alpha3' => 'gab',
                'continent_id' => 3,
                'currency_code' => 'XAF',
                'currency_name' => 'CFA Franc BEAC',
                'emoji' => '🇬🇦',
                'full_name' => 'The Gabonese Republic',
                'has_division' => 0,
                'id' => 18,
                'name' => 'Gabon',
                'tld' => '.ga',
            ),
            18 => 
            array (
                'callingcode' => '233',
                'capital' => 'Accra',
                'code' => 'gh',
                'code_alpha3' => 'gha',
                'continent_id' => 3,
                'currency_code' => 'GHS',
                'currency_name' => 'Ghana Cedi',
                'emoji' => '🇬🇭',
                'full_name' => 'The Republic of Ghana',
                'has_division' => 0,
                'id' => 19,
                'name' => 'Ghana',
                'tld' => '.gh',
            ),
            19 => 
            array (
                'callingcode' => '255',
                'capital' => 'Dodoma',
                'code' => 'tz',
                'code_alpha3' => 'tza',
                'continent_id' => 3,
                'currency_code' => 'TZS',
                'currency_name' => 'Tanzanian shilling',
                'emoji' => '🇹🇿',
                'full_name' => 'The United Republic of Tanzania',
                'has_division' => 0,
                'id' => 20,
                'name' => 'Tanzania',
                'tld' => '.tz',
            ),
            20 => 
            array (
                'callingcode' => '223',
                'capital' => 'Bamako',
                'code' => 'ml',
                'code_alpha3' => 'mli',
                'continent_id' => 3,
                'currency_code' => 'XOF',
                'currency_name' => 'CFA Franc BCEAO',
                'emoji' => '🇲🇱',
                'full_name' => 'The Republic of Mali',
                'has_division' => 0,
                'id' => 21,
                'name' => 'Mali',
                'tld' => '.ml',
            ),
            21 => 
            array (
                'callingcode' => '252',
                'capital' => 'Mogadishu',
                'code' => 'so',
                'code_alpha3' => 'som',
                'continent_id' => 3,
                'currency_code' => 'SOS',
                'currency_name' => 'Somali Shilling',
                'emoji' => '🇸🇴',
                'full_name' => 'The Somalia Democratic Republic',
                'has_division' => 0,
                'id' => 22,
                'name' => 'Somalia',
                'tld' => '.so',
            ),
            22 => 
            array (
                'callingcode' => '222',
                'capital' => 'Nouakchott',
                'code' => 'mr',
                'code_alpha3' => 'mrt',
                'continent_id' => 3,
                'currency_code' => 'MRO',
                'currency_name' => 'Ouguiya',
                'emoji' => '🇲🇷',
                'full_name' => 'The Islamic Republic of Mauritania',
                'has_division' => 0,
                'id' => 23,
                'name' => 'Mauritania',
                'tld' => '.mr',
            ),
            23 => 
            array (
                'callingcode' => '256',
                'capital' => 'Kampala',
                'code' => 'ug',
                'code_alpha3' => 'uga',
                'continent_id' => 3,
                'currency_code' => 'UGX',
                'currency_name' => 'Uganda Shilling',
                'emoji' => '🇺🇬',
                'full_name' => 'The Republic of Uganda',
                'has_division' => 0,
                'id' => 24,
                'name' => 'Uganda',
                'tld' => '.ug',
            ),
            24 => 
            array (
                'callingcode' => '235',
                'capital' => 'N\'Djamena',
                'code' => 'td',
                'code_alpha3' => 'tcd',
                'continent_id' => 3,
                'currency_code' => 'XAF',
                'currency_name' => 'CFA Franc BEAC',
                'emoji' => '🇹🇩',
                'full_name' => 'The Republic of Chad',
                'has_division' => 0,
                'id' => 25,
                'name' => 'Chad',
                'tld' => '.td',
            ),
            25 => 
            array (
                'callingcode' => '262',
                'capital' => 'Mamoudzou',
                'code' => 'yt',
                'code_alpha3' => 'myt',
                'continent_id' => 3,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇾🇹',
                'full_name' => 'Territorial Collectivity of Mayotte',
                'has_division' => 0,
                'id' => 26,
                'name' => 'Mayotte',
                'tld' => '.yt',
            ),
            26 => 
            array (
                'callingcode' => '269',
                'capital' => 'Moroni',
                'code' => 'km',
                'code_alpha3' => 'com',
                'continent_id' => 3,
                'currency_code' => 'KMF',
                'currency_name' => 'Comoro Franc',
                'emoji' => '🇰🇲',
                'full_name' => 'Union of Comoros',
                'has_division' => 0,
                'id' => 27,
                'name' => 'Comoros',
                'tld' => '.km',
            ),
            27 => 
            array (
                'callingcode' => '267',
                'capital' => 'Gaborone',
                'code' => 'bw',
                'code_alpha3' => 'bwa',
                'continent_id' => 3,
                'currency_code' => 'BWP',
                'currency_name' => 'Pula',
                'emoji' => '🇧🇼',
                'full_name' => 'The Republic of Botswana',
                'has_division' => 0,
                'id' => 28,
                'name' => 'Botswana',
                'tld' => '.bw',
            ),
            28 => 
            array (
                'callingcode' => '221',
                'capital' => 'Dakar',
                'code' => 'sn',
                'code_alpha3' => 'sen',
                'continent_id' => 3,
                'currency_code' => 'XOF',
                'currency_name' => 'CFA Franc BCEAO',
                'emoji' => '🇸🇳',
                'full_name' => 'the Republic of Senegal',
                'has_division' => 0,
                'id' => 29,
                'name' => 'Senegal',
                'tld' => '.sn',
            ),
            29 => 
            array (
                'callingcode' => '268',
                'capital' => 'Lobamba',
                'code' => 'sz',
                'code_alpha3' => 'swz',
                'continent_id' => 3,
                'currency_code' => 'SZL',
                'currency_name' => 'Lilangeni',
                'emoji' => '🇸🇿',
                'full_name' => 'The Kingdom of Swaziland',
                'has_division' => 0,
                'id' => 30,
                'name' => 'Swaziland',
                'tld' => '.sz',
            ),
            30 => 
            array (
                'callingcode' => '245',
                'capital' => 'Bissau',
                'code' => 'gw',
                'code_alpha3' => 'gnb',
                'continent_id' => 3,
                'currency_code' => 'XOF',
                'currency_name' => 'CFA Franc BCEAO',
                'emoji' => '🇬🇼',
                'full_name' => 'The Republic of Guinea-Bissau',
                'has_division' => 0,
                'id' => 31,
                'name' => 'Guinea Bissau',
                'tld' => '.gw',
            ),
            31 => 
            array (
                'callingcode' => '243',
                'capital' => 'Kinshasa',
                'code' => 'cd',
                'code_alpha3' => 'cod',
                'continent_id' => 3,
                'currency_code' => 'CDF',
                'currency_name' => 'Congolese franc',
                'emoji' => '🇨🇩',
                'full_name' => 'Democratic Republic of the Congo',
                'has_division' => 0,
                'id' => 32,
                'name' => 'DR Congo',
                'tld' => '.cd',
            ),
            32 => 
            array (
                'callingcode' => '236',
                'capital' => 'Bangui',
                'code' => 'cf',
                'code_alpha3' => 'caf',
                'continent_id' => 3,
                'currency_code' => 'XAF',
                'currency_name' => 'CFA Franc BEAC',
                'emoji' => '🇨🇫',
                'full_name' => 'The Central African Republic',
                'has_division' => 0,
                'id' => 33,
                'name' => 'Central African',
                'tld' => '.cf',
            ),
            33 => 
            array (
                'callingcode' => '266',
                'capital' => 'Maseru',
                'code' => 'ls',
                'code_alpha3' => 'lso',
                'continent_id' => 3,
                'currency_code' => 'LSL',
                'currency_name' => 'Lesotho loti',
                'emoji' => '🇱🇸',
                'full_name' => 'The Kingdom of Lesotho',
                'has_division' => 0,
                'id' => 34,
                'name' => 'Lesotho',
                'tld' => '.ls',
            ),
            34 => 
            array (
                'callingcode' => '242',
                'capital' => 'Brazzaville',
                'code' => 'cg',
                'code_alpha3' => 'cog',
                'continent_id' => 3,
                'currency_code' => 'XAF',
                'currency_name' => 'CFA Franc BEAC',
                'emoji' => '🇨🇬',
                'full_name' => 'Republic of the Congo',
                'has_division' => 0,
                'id' => 35,
                'name' => 'Congo',
                'tld' => '.cg',
            ),
            35 => 
            array (
                'callingcode' => '27',
                'capital' => 'Pretoria',
                'code' => 'za',
                'code_alpha3' => 'zaf',
                'continent_id' => 3,
                'currency_code' => 'ZAR',
                'currency_name' => 'Rand',
                'emoji' => '🇿🇦',
                'full_name' => 'The Republic of South Africa',
                'has_division' => 0,
                'id' => 36,
                'name' => 'South Africa',
                'tld' => '.za',
            ),
            36 => 
            array (
                'callingcode' => '231',
                'capital' => 'Monrovia',
                'code' => 'lr',
                'code_alpha3' => 'lbr',
                'continent_id' => 3,
                'currency_code' => 'LRD',
                'currency_name' => 'Liberian Dollar',
                'emoji' => '🇱🇷',
                'full_name' => 'The Republic of Liberia',
                'has_division' => 0,
                'id' => 37,
                'name' => 'Liberia',
                'tld' => '.lr',
            ),
            37 => 
            array (
                'callingcode' => '216',
                'capital' => 'Tunis',
                'code' => 'tn',
                'code_alpha3' => 'tun',
                'continent_id' => 3,
                'currency_code' => 'TND',
                'currency_name' => 'Tunisian Dinar',
                'emoji' => '🇹🇳',
                'full_name' => 'The Republic of Tunisia',
                'has_division' => 0,
                'id' => 38,
                'name' => 'Tunisia',
                'tld' => '.tn',
            ),
            38 => 
            array (
                'callingcode' => '260',
                'capital' => 'Lusaka',
                'code' => 'zm',
                'code_alpha3' => 'zmb',
                'continent_id' => 3,
                'currency_code' => 'ZMW',
                'currency_name' => 'Zambian Kwacha',
                'emoji' => '🇿🇲',
                'full_name' => 'The Republic of Zambia',
                'has_division' => 0,
                'id' => 39,
                'name' => 'Zambia',
                'tld' => '.zm',
            ),
            39 => 
            array (
                'callingcode' => '227',
                'capital' => 'Niamey',
                'code' => 'ne',
                'code_alpha3' => 'ner',
                'continent_id' => 3,
                'currency_code' => 'XOF',
                'currency_name' => 'CFA Franc BCEAO',
                'emoji' => '🇳🇪',
                'full_name' => 'The Republic of Niger',
                'has_division' => 0,
                'id' => 40,
                'name' => 'Niger',
                'tld' => '.ne',
            ),
            40 => 
            array (
                'callingcode' => '212',
                'capital' => 'El Aaiún',
                'code' => 'eh',
                'code_alpha3' => 'esh',
                'continent_id' => 3,
                'currency_code' => 'DZD',
                'currency_name' => 'Algerian dinar',
                'emoji' => '🇪🇭',
                'full_name' => 'the Sahrawi Arab Democratic Republic Western Sahara',
                'has_division' => 0,
                'id' => 41,
                'name' => 'Western Sahara',
                'tld' => '.eh',
            ),
            41 => 
            array (
                'callingcode' => '228',
                'capital' => 'Lomé',
                'code' => 'tg',
                'code_alpha3' => 'tgo',
                'continent_id' => 3,
                'currency_code' => 'XOF',
                'currency_name' => 'CFA Franc BCEAO',
                'emoji' => '🇹🇬',
                'full_name' => 'The Republic of Togo',
                'has_division' => 0,
                'id' => 42,
                'name' => 'Togo',
                'tld' => '.tg',
            ),
            42 => 
            array (
                'callingcode' => '264',
                'capital' => 'Windhoek',
                'code' => 'na',
                'code_alpha3' => 'nam',
                'continent_id' => 3,
                'currency_code' => 'NAD',
                'currency_name' => 'Namibian dollar',
                'emoji' => '🇳🇦',
                'full_name' => 'The Republic of Namibia',
                'has_division' => 0,
                'id' => 43,
                'name' => 'Namibia',
                'tld' => '.na',
            ),
            43 => 
            array (
                'callingcode' => '258',
                'capital' => 'Maputo',
                'code' => 'mz',
                'code_alpha3' => 'moz',
                'continent_id' => 3,
                'currency_code' => 'MZN',
                'currency_name' => 'Mozambique Metical',
                'emoji' => '🇲🇿',
                'full_name' => 'The Republic of Mozambique',
                'has_division' => 0,
                'id' => 44,
                'name' => 'Mozambique',
                'tld' => '.mz',
            ),
            44 => 
            array (
                'callingcode' => '251',
                'capital' => 'Addis Ababa',
                'code' => 'et',
                'code_alpha3' => 'eth',
                'continent_id' => 3,
                'currency_code' => 'ETB',
                'currency_name' => 'Ethiopian Birr',
                'emoji' => '🇪🇹',
                'full_name' => 'The Federal Democratic Republic of Ethiopia',
                'has_division' => 0,
                'id' => 45,
                'name' => 'Ethiopia',
                'tld' => '.et',
            ),
            45 => 
            array (
                'callingcode' => '212',
                'capital' => 'Rabat',
                'code' => 'ma',
                'code_alpha3' => 'mar',
                'continent_id' => 3,
                'currency_code' => 'MAD',
                'currency_name' => 'Moroccan Dirham',
                'emoji' => '🇲🇦',
                'full_name' => 'The Kingdom of Morocco',
                'has_division' => 0,
                'id' => 46,
                'name' => 'Morocco',
                'tld' => '.ma',
            ),
            46 => 
            array (
                'callingcode' => '265',
                'capital' => 'Lilongwe',
                'code' => 'mw',
                'code_alpha3' => 'mwi',
                'continent_id' => 3,
                'currency_code' => 'MWK',
                'currency_name' => 'Kwacha',
                'emoji' => '🇲🇼',
                'full_name' => 'The Republic of Malawi',
                'has_division' => 0,
                'id' => 47,
                'name' => 'Malawi',
                'tld' => '.mw',
            ),
            47 => 
            array (
                'callingcode' => '234',
                'capital' => 'Abuja',
                'code' => 'ng',
                'code_alpha3' => 'nga',
                'continent_id' => 3,
                'currency_code' => 'NGN',
                'currency_name' => 'Naira',
                'emoji' => '🇳🇬',
                'full_name' => 'Federal Republic of Nigeria',
                'has_division' => 0,
                'id' => 48,
                'name' => 'Nigeria',
                'tld' => '.ng',
            ),
            48 => 
            array (
                'callingcode' => '238',
                'capital' => 'Praia',
                'code' => 'cv',
                'code_alpha3' => 'cpv',
                'continent_id' => 3,
                'currency_code' => 'CVE',
                'currency_name' => 'Cabo Verde Escudo',
                'emoji' => '🇨🇻',
                'full_name' => 'The Republic of Cape Verde',
                'has_division' => 0,
                'id' => 49,
                'name' => 'Cape Verde',
                'tld' => '.cv',
            ),
            49 => 
            array (
                'callingcode' => '257',
                'capital' => 'Bujumbura',
                'code' => 'bi',
                'code_alpha3' => 'bdi',
                'continent_id' => 3,
                'currency_code' => 'BIF',
                'currency_name' => 'Burundi Franc',
                'emoji' => '🇧🇮',
                'full_name' => 'The Republic of Burundi',
                'has_division' => 0,
                'id' => 50,
                'name' => 'Burundi',
                'tld' => '.bi',
            ),
            50 => 
            array (
                'callingcode' => '213',
                'capital' => 'Algiers',
                'code' => 'dz',
                'code_alpha3' => 'dza',
                'continent_id' => 3,
                'currency_code' => 'DZD',
                'currency_name' => 'Algerian Dinar',
                'emoji' => '🇩🇿',
                'full_name' => 'People\'s Democratic Republic of Algeria',
                'has_division' => 0,
                'id' => 51,
                'name' => 'Algeria',
                'tld' => '.dz',
            ),
            51 => 
            array (
                'callingcode' => '253',
                'capital' => 'Djibouti',
                'code' => 'dj',
                'code_alpha3' => 'dji',
                'continent_id' => 3,
                'currency_code' => 'DJF',
                'currency_name' => 'Djibouti Franc',
                'emoji' => '🇩🇯',
                'full_name' => 'The Republic of Djibouti',
                'has_division' => 0,
                'id' => 52,
                'name' => 'Djibouti',
                'tld' => '.dj',
            ),
            52 => 
            array (
                'callingcode' => '590',
                'capital' => 'Basse-Terre',
                'code' => 'gp',
                'code_alpha3' => 'gmb',
                'continent_id' => 3,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇬🇵',
                'full_name' => 'Islamic Republic of Gambia',
                'has_division' => 0,
                'id' => 53,
                'name' => 'Gambia',
                'tld' => '.gp',
            ),
            53 => 
            array (
                'callingcode' => '240',
                'capital' => 'Malabo',
                'code' => 'gq',
                'code_alpha3' => 'gnq',
                'continent_id' => 3,
                'currency_code' => 'XAF',
                'currency_name' => 'CFA Franc BEAC',
                'emoji' => '🇬🇶',
                'full_name' => 'The Republic of Equatorial Guinea',
                'has_division' => 0,
                'id' => 54,
                'name' => 'Eq.Guinea',
                'tld' => '.gq',
            ),
            54 => 
            array (
                'callingcode' => '249',
                'capital' => 'Khartoum',
                'code' => 'sd',
                'code_alpha3' => 'sdn',
                'continent_id' => 3,
                'currency_code' => 'SDG',
                'currency_name' => 'Sudanese Pound',
                'emoji' => '🇸🇩',
                'full_name' => 'The Republic of Sudan',
                'has_division' => 0,
                'id' => 55,
                'name' => 'Sudan',
                'tld' => '.sd',
            ),
            55 => 
            array (
                'callingcode' => '254',
                'capital' => 'Nairobi',
                'code' => 'ke',
                'code_alpha3' => 'ken',
                'continent_id' => 3,
                'currency_code' => 'KES',
                'currency_name' => 'Kenyan Shilling',
                'emoji' => '🇰🇪',
                'full_name' => 'The Republic of Kenya',
                'has_division' => 0,
                'id' => 56,
                'name' => 'Kenya',
                'tld' => '.ke',
            ),
            56 => 
            array (
                'callingcode' => '65',
                'capital' => 'Singapore',
                'code' => 'sg',
                'code_alpha3' => 'sgp',
                'continent_id' => 1,
                'currency_code' => 'SGD',
                'currency_name' => 'Singapore Dollar',
                'emoji' => '🇸🇬',
                'full_name' => 'Republic of Singapore',
                'has_division' => 0,
                'id' => 57,
                'name' => 'Singapore',
                'tld' => '.sg',
            ),
            57 => 
            array (
                'callingcode' => '82',
                'capital' => 'Seoul',
                'code' => 'kr',
                'code_alpha3' => 'kor',
                'continent_id' => 1,
                'currency_code' => 'KRW',
                'currency_name' => 'South Korean won',
                'emoji' => '🇰🇷',
                'full_name' => 'Republic of Korea',
                'has_division' => 1,
                'id' => 58,
                'name' => 'Korea',
                'tld' => '.kr',
            ),
            58 => 
            array (
                'callingcode' => '963',
                'capital' => 'Damascus',
                'code' => 'sy',
                'code_alpha3' => 'syr',
                'continent_id' => 1,
                'currency_code' => 'SYP',
                'currency_name' => 'Syrian Pound',
                'emoji' => '🇸🇾',
                'full_name' => 'The Syrian Arab Republic',
                'has_division' => 0,
                'id' => 59,
                'name' => 'Syrian',
                'tld' => '.sy',
            ),
            59 => 
            array (
                'callingcode' => '998',
                'capital' => 'Tashkent',
                'code' => 'uz',
                'code_alpha3' => 'uzb',
                'continent_id' => 1,
                'currency_code' => 'UZS',
                'currency_name' => 'Uzbekistan Sum',
                'emoji' => '🇺🇿',
                'full_name' => 'The Republic of Uzbekistan',
                'has_division' => 0,
                'id' => 60,
                'name' => 'Uzbekstan',
                'tld' => '.uz',
            ),
            60 => 
            array (
                'callingcode' => '973',
                'capital' => 'Manama',
                'code' => 'bh',
                'code_alpha3' => 'bhr',
                'continent_id' => 1,
                'currency_code' => 'BHD',
                'currency_name' => 'Bahraini Dinar',
                'emoji' => '🇧🇭',
                'full_name' => 'The Kingdom of Bahrain',
                'has_division' => 0,
                'id' => 61,
                'name' => 'Bahrian',
                'tld' => '.bh',
            ),
            61 => 
            array (
                'callingcode' => '81',
                'capital' => 'Tokyo',
                'code' => 'jp',
                'code_alpha3' => 'jpn',
                'continent_id' => 1,
                'currency_code' => 'JPY',
                'currency_name' => 'Yen',
                'emoji' => '🇯🇵',
                'full_name' => 'Japan',
                'has_division' => 0,
                'id' => 62,
                'name' => 'Japan',
                'tld' => '.jp',
            ),
            62 => 
            array (
                'callingcode' => '962',
                'capital' => 'Amman',
                'code' => 'jo',
                'code_alpha3' => 'jor',
                'continent_id' => 1,
                'currency_code' => 'JOD',
                'currency_name' => 'Jordanian Dinar',
                'emoji' => '🇯🇴',
                'full_name' => 'The Hashemite Kingdom of Jordan',
                'has_division' => 0,
                'id' => 63,
                'name' => 'Jordan',
                'tld' => '.jo',
            ),
            63 => 
            array (
                'callingcode' => '84',
                'capital' => 'Hanoi',
                'code' => 'vn',
                'code_alpha3' => 'vnm',
                'continent_id' => 1,
                'currency_code' => 'VND',
                'currency_name' => 'Dong',
                'emoji' => '🇻🇳',
                'full_name' => 'Socialist Republic of Vietnam',
                'has_division' => 0,
                'id' => 64,
                'name' => 'Vietnam',
                'tld' => '.vn',
            ),
            64 => 
            array (
                'callingcode' => '996',
                'capital' => 'Bishkek',
                'code' => 'kg',
                'code_alpha3' => 'kgz',
                'continent_id' => 1,
                'currency_code' => 'KGS',
                'currency_name' => 'Som',
                'emoji' => '🇰🇬',
                'full_name' => 'The Kyrgyz Republic',
                'has_division' => 0,
                'id' => 65,
                'name' => 'Kirghizia',
                'tld' => '.kg',
            ),
            65 => 
            array (
                'callingcode' => '66',
                'capital' => 'Bangkok',
                'code' => 'th',
                'code_alpha3' => 'tha',
                'continent_id' => 1,
                'currency_code' => 'THB',
                'currency_name' => 'Baht',
                'emoji' => '🇹🇭',
                'full_name' => 'Kingdom of Thailand',
                'has_division' => 0,
                'id' => 66,
                'name' => 'Thailand',
                'tld' => '.th',
            ),
            66 => 
            array (
                'callingcode' => '94',
                'capital' => 'Colombo',
                'code' => 'lk',
                'code_alpha3' => 'lka',
                'continent_id' => 1,
                'currency_code' => 'LKR',
                'currency_name' => 'Sri Lanka Rupee',
                'emoji' => '🇱🇰',
                'full_name' => 'The Democratic Socialist Republic of Sri Lanka',
                'has_division' => 0,
                'id' => 67,
                'name' => 'Sri Lanka',
                'tld' => '.lk',
            ),
            67 => 
            array (
                'callingcode' => '971',
                'capital' => 'Abu Dhabi',
                'code' => 'ae',
                'code_alpha3' => 'are',
                'continent_id' => 1,
                'currency_code' => 'AED',
                'currency_name' => 'UAE Dirham',
                'emoji' => '🇦🇪',
                'full_name' => 'The United Arab Emirates',
                'has_division' => 0,
                'id' => 68,
                'name' => 'United Arab Emirates',
                'tld' => '.ae',
            ),
            68 => 
            array (
                'callingcode' => '856',
                'capital' => 'Vientiane',
                'code' => 'la',
                'code_alpha3' => 'lao',
                'continent_id' => 1,
                'currency_code' => 'LAK',
                'currency_name' => 'Kip',
                'emoji' => '🇱🇦',
                'full_name' => 'Lao People\'s Democratic Republic',
                'has_division' => 0,
                'id' => 69,
                'name' => 'Laos',
                'tld' => '.la',
            ),
            69 => 
            array (
                'callingcode' => '93',
                'capital' => 'Kabul',
                'code' => 'af',
                'code_alpha3' => 'afg',
                'continent_id' => 1,
                'currency_code' => 'AFN',
                'currency_name' => 'Afghani',
                'emoji' => '🇦🇫',
                'full_name' => 'the Islamic Republic of Afghanistan',
                'has_division' => 0,
                'id' => 70,
                'name' => 'Afghanistan',
                'tld' => '.af',
            ),
            70 => 
            array (
                'callingcode' => '853',
                'capital' => '',
                'code' => 'mo',
                'code_alpha3' => 'mac',
                'continent_id' => 1,
                'currency_code' => 'MOP',
                'currency_name' => 'Macanese pataca',
                'emoji' => '🇲🇴',
                'full_name' => 'Macau Macao',
                'has_division' => 0,
                'id' => 71,
                'name' => 'Macau',
                'tld' => '.mo',
            ),
            71 => 
            array (
                'callingcode' => '992',
                'capital' => 'Dushanbe',
                'code' => 'tj',
                'code_alpha3' => 'tjk',
                'continent_id' => 1,
                'currency_code' => 'TJS',
                'currency_name' => 'Somoni',
                'emoji' => '🇹🇯',
                'full_name' => 'The Republic of Tajikistan',
                'has_division' => 0,
                'id' => 72,
                'name' => 'Tajikistan',
                'tld' => '.tj',
            ),
            72 => 
            array (
                'callingcode' => '850',
                'capital' => 'Pyongyang',
                'code' => 'kp',
                'code_alpha3' => 'prk',
                'continent_id' => 1,
                'currency_code' => 'KPW',
                'currency_name' => 'North Korean won',
                'emoji' => '🇰🇵',
                'full_name' => 'Democratic People\'s Republic of Korea',
                'has_division' => 0,
                'id' => 73,
                'name' => 'Korea,DPR',
                'tld' => '.kp',
            ),
            73 => 
            array (
                'callingcode' => '970',
                'capital' => 'Ramallah',
                'code' => 'ps',
                'code_alpha3' => 'pal',
                'continent_id' => 1,
                'currency_code' => 'ILS',
                'currency_name' => 'Israeli new shekel',
                'emoji' => '🇵🇸',
                'full_name' => 'The State of Palestine',
                'has_division' => 0,
                'id' => 74,
                'name' => 'Palestine',
                'tld' => '.ps',
            ),
            74 => 
            array (
                'callingcode' => '852',
                'capital' => 'City of Victoria',
                'code' => 'hk',
                'code_alpha3' => 'hkg',
                'continent_id' => 1,
                'currency_code' => 'HKD',
                'currency_name' => 'Hong Kong dollar',
                'emoji' => '🇭🇰',
                'full_name' => 'Hong Kong',
                'has_division' => 0,
                'id' => 75,
                'name' => 'Hong Kong',
                'tld' => '.hk',
            ),
            75 => 
            array (
                'callingcode' => '964',
                'capital' => 'Baghdad',
                'code' => 'iq',
                'code_alpha3' => 'irq',
                'continent_id' => 1,
                'currency_code' => 'IQD',
                'currency_name' => 'Iraqi Dinar',
                'emoji' => '🇮🇶',
                'full_name' => 'Republic Of Iraq',
                'has_division' => 0,
                'id' => 76,
                'name' => 'Iraq',
                'tld' => '.iq',
            ),
            76 => 
            array (
                'callingcode' => '961',
                'capital' => 'Beirut',
                'code' => 'lb',
                'code_alpha3' => 'lbn',
                'continent_id' => 1,
                'currency_code' => 'LBP',
                'currency_name' => 'Lebanese Pound',
                'emoji' => '🇱🇧',
                'full_name' => 'The Republic of Lebanon',
                'has_division' => 0,
                'id' => 77,
                'name' => 'Lebanon',
                'tld' => '.lb',
            ),
            77 => 
            array (
                'callingcode' => '965',
                'capital' => 'Kuwait City',
                'code' => 'kw',
                'code_alpha3' => 'kwt',
                'continent_id' => 1,
                'currency_code' => 'KWD',
                'currency_name' => 'Kuwaiti Dinar',
                'emoji' => '🇰🇼',
                'full_name' => 'The State of Kuwait',
                'has_division' => 0,
                'id' => 78,
                'name' => 'Kuwait',
                'tld' => '.kw',
            ),
            78 => 
            array (
                'callingcode' => '673',
                'capital' => 'Bandar Seri Begawan',
                'code' => 'bn',
                'code_alpha3' => 'brn',
                'continent_id' => 1,
                'currency_code' => 'BND',
                'currency_name' => 'Brunei Dollar',
                'emoji' => '🇧🇳',
                'full_name' => 'Brunei Darussalam',
                'has_division' => 0,
                'id' => 79,
                'name' => 'Brunei',
                'tld' => '.bn',
            ),
            79 => 
            array (
                'callingcode' => '960',
                'capital' => 'Malé',
                'code' => 'mv',
                'code_alpha3' => 'mdv',
                'continent_id' => 1,
                'currency_code' => 'MVR',
                'currency_name' => 'Rufiyaa',
                'emoji' => '🇲🇻',
                'full_name' => 'The Republic of Maldives',
                'has_division' => 0,
                'id' => 80,
                'name' => 'Maldives',
                'tld' => '.mv',
            ),
            80 => 
            array (
                'callingcode' => '62',
                'capital' => 'Jakarta',
                'code' => 'id',
                'code_alpha3' => 'idn',
                'continent_id' => 1,
                'currency_code' => 'IDR',
                'currency_name' => 'Rupiah',
                'emoji' => '🇮🇩',
                'full_name' => 'The Republic of Indonesia',
                'has_division' => 0,
                'id' => 81,
                'name' => 'Indonesia',
                'tld' => '.id',
            ),
            81 => 
            array (
                'callingcode' => '972',
                'capital' => 'Jerusalem',
                'code' => 'il',
                'code_alpha3' => 'isr',
                'continent_id' => 1,
                'currency_code' => 'ILS',
                'currency_name' => 'New Israeli Sheqel',
                'emoji' => '🇮🇱',
                'full_name' => 'The State of Israel',
                'has_division' => 0,
                'id' => 82,
                'name' => 'Israel',
                'tld' => '.il',
            ),
            82 => 
            array (
                'callingcode' => '976',
                'capital' => 'Ulan Bator',
                'code' => 'mn',
                'code_alpha3' => 'mng',
                'continent_id' => 1,
                'currency_code' => 'MNT',
                'currency_name' => 'Tugrik',
                'emoji' => '🇲🇳',
                'full_name' => 'Mongolia',
                'has_division' => 0,
                'id' => 83,
                'name' => 'Mongolia',
                'tld' => '.mn',
            ),
            83 => 
            array (
                'callingcode' => '968',
                'capital' => 'Muscat',
                'code' => 'om',
                'code_alpha3' => 'omn',
                'continent_id' => 1,
                'currency_code' => 'OMR',
                'currency_name' => 'Rial Omani',
                'emoji' => '🇴🇲',
                'full_name' => 'Sultanate of Oman',
                'has_division' => 0,
                'id' => 84,
                'name' => 'Oman',
                'tld' => '.om',
            ),
            84 => 
            array (
                'callingcode' => '91',
                'capital' => 'New Delhi',
                'code' => 'in',
                'code_alpha3' => 'ind',
                'continent_id' => 1,
                'currency_code' => 'INR',
                'currency_name' => 'Indian Rupee',
                'emoji' => '🇮🇳',
                'full_name' => 'The Republic of India',
                'has_division' => 0,
                'id' => 85,
                'name' => 'India',
                'tld' => '.in',
            ),
            85 => 
            array (
                'callingcode' => '95',
                'capital' => 'Naypyidaw',
                'code' => 'mm',
                'code_alpha3' => 'mmr',
                'continent_id' => 1,
                'currency_code' => 'MMK',
                'currency_name' => 'Kyat',
                'emoji' => '🇲🇲',
                'full_name' => 'Republic Of The Union Of Myanmar',
                'has_division' => 0,
                'id' => 86,
                'name' => 'Myanmar',
                'tld' => '.mm',
            ),
            86 => 
            array (
                'callingcode' => '60',
                'capital' => 'Kuala Lumpur',
                'code' => 'my',
                'code_alpha3' => 'mys',
                'continent_id' => 1,
                'currency_code' => 'MYR',
                'currency_name' => 'Malaysian Ringgit',
                'emoji' => '🇲🇾',
                'full_name' => 'Malaysia',
                'has_division' => 1,
                'id' => 87,
                'name' => 'Malaysia',
                'tld' => '.my',
            ),
            87 => 
            array (
                'callingcode' => '670',
                'capital' => 'Dili',
                'code' => 'tl',
                'code_alpha3' => 'tmp',
                'continent_id' => 1,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇹🇱',
                'full_name' => 'Democratic Republic of East Timor',
                'has_division' => 0,
                'id' => 88,
                'name' => 'East Timor',
                'tld' => '.tl',
            ),
            88 => 
            array (
                'callingcode' => '967',
                'capital' => 'Sana\'a',
                'code' => 'ye',
                'code_alpha3' => 'yem',
                'continent_id' => 1,
                'currency_code' => 'YER',
                'currency_name' => 'Yemeni Rial',
                'emoji' => '🇾🇪',
                'full_name' => 'The Republic of Yemen',
                'has_division' => 0,
                'id' => 89,
                'name' => 'Yemen',
                'tld' => '.ye',
            ),
            89 => 
            array (
                'callingcode' => '975',
                'capital' => 'Thimphu',
                'code' => 'bt',
                'code_alpha3' => 'btn',
                'continent_id' => 1,
                'currency_code' => 'BTN',
                'currency_name' => 'Bhutanese ngultrum',
                'emoji' => '🇧🇹',
                'full_name' => 'Kingdom of Bhutan',
                'has_division' => 0,
                'id' => 90,
                'name' => 'Bhutan',
                'tld' => '.bt',
            ),
            90 => 
            array (
                'callingcode' => '855',
                'capital' => 'Phnom Penh',
                'code' => 'kh',
                'code_alpha3' => 'khm',
                'continent_id' => 1,
                'currency_code' => 'KHR',
                'currency_name' => 'Riel',
                'emoji' => '🇰🇭',
                'full_name' => 'Kingdom of Cambodia',
                'has_division' => 0,
                'id' => 91,
                'name' => 'Cambodia',
                'tld' => '.kh',
            ),
            91 => 
            array (
                'callingcode' => '92',
                'capital' => 'Islamabad',
                'code' => 'pk',
                'code_alpha3' => 'pak',
                'continent_id' => 1,
                'currency_code' => 'PKR',
                'currency_name' => 'Pakistan Rupee',
                'emoji' => '🇵🇰',
                'full_name' => 'the Islamic Republic of Pakistan',
                'has_division' => 0,
                'id' => 92,
                'name' => 'Pakistan',
                'tld' => '.pk',
            ),
            92 => 
            array (
                'callingcode' => '880',
                'capital' => 'Dhaka',
                'code' => 'bd',
                'code_alpha3' => 'bgd',
                'continent_id' => 1,
                'currency_code' => 'BDT',
                'currency_name' => 'Taka',
                'emoji' => '🇧🇩',
                'full_name' => 'People\'s Republic of Bangladesh',
                'has_division' => 0,
                'id' => 93,
                'name' => 'Bangladesh',
                'tld' => '.bd',
            ),
            93 => 
            array (
                'callingcode' => '966',
                'capital' => 'Riyadh',
                'code' => 'sa',
                'code_alpha3' => 'sau',
                'continent_id' => 1,
                'currency_code' => 'SAR',
                'currency_name' => 'Saudi Riyal',
                'emoji' => '🇸🇦',
                'full_name' => 'Kingdom of Saudi Arabia',
                'has_division' => 0,
                'id' => 94,
                'name' => 'Saudi Arabia',
                'tld' => '.sa',
            ),
            94 => 
            array (
                'callingcode' => '993',
                'capital' => 'Ashgabat',
                'code' => 'tm',
                'code_alpha3' => 'tkm',
                'continent_id' => 1,
                'currency_code' => 'TMT',
                'currency_name' => 'Turkmenistan New Manat',
                'emoji' => '🇹🇲',
                'full_name' => 'Turkmenistan',
                'has_division' => 0,
                'id' => 95,
                'name' => 'Turkmenistan',
                'tld' => '.tm',
            ),
            95 => 
            array (
                'callingcode' => '974',
                'capital' => 'Doha',
                'code' => 'qa',
                'code_alpha3' => 'qat',
                'continent_id' => 1,
                'currency_code' => 'QAR',
                'currency_name' => 'Qatari Rial',
                'emoji' => '🇶🇦',
                'full_name' => 'The State of Qatar',
                'has_division' => 0,
                'id' => 96,
                'name' => 'Qatar',
                'tld' => '.qa',
            ),
            96 => 
            array (
                'callingcode' => '977',
                'capital' => 'Kathmandu',
                'code' => 'np',
                'code_alpha3' => 'npl',
                'continent_id' => 1,
                'currency_code' => 'NPR',
                'currency_name' => 'Nepalese Rupee',
                'emoji' => '🇳🇵',
                'full_name' => 'Federal Democratic Republic of Nepal',
                'has_division' => 0,
                'id' => 97,
                'name' => 'Nepal',
                'tld' => '.np',
            ),
            97 => 
            array (
                'callingcode' => '76',
                'capital' => 'Astana',
                'code' => 'kz',
                'code_alpha3' => 'kaz',
                'continent_id' => 1,
                'currency_code' => 'KZT',
                'currency_name' => 'Tenge',
                'emoji' => '🇰🇿',
                'full_name' => 'The Republic of Kazakhstan',
                'has_division' => 0,
                'id' => 98,
                'name' => 'Kazakhstan',
                'tld' => '.kz',
            ),
            98 => 
            array (
                'callingcode' => '63',
                'capital' => 'Manila',
                'code' => 'ph',
                'code_alpha3' => 'phl',
                'continent_id' => 1,
                'currency_code' => 'PHP',
                'currency_name' => 'Philippine Peso',
                'emoji' => '🇵🇭',
                'full_name' => 'Republic of the Philippines',
                'has_division' => 0,
                'id' => 99,
                'name' => 'Philippines',
                'tld' => '.ph',
            ),
            99 => 
            array (
                'callingcode' => '886',
                'capital' => 'Taipei',
                'code' => 'tw',
                'code_alpha3' => 'twn',
                'continent_id' => 1,
                'currency_code' => 'TWD',
                'currency_name' => 'New Taiwan dollar',
                'emoji' => '🇹🇼',
                'full_name' => 'Taiwan',
                'has_division' => 0,
                'id' => 100,
                'name' => 'Taiwan',
                'tld' => '.tw',
            ),
            100 => 
            array (
                'callingcode' => '86',
                'capital' => 'Beijing',
                'code' => 'cn',
                'code_alpha3' => 'chn',
                'continent_id' => 1,
                'currency_code' => 'CNY',
                'currency_name' => 'Yuan Renminbi',
                'emoji' => '🇨🇳',
                'full_name' => 'People\'s Republic of China',
                'has_division' => 1,
                'id' => 101,
                'name' => 'China',
                'tld' => '.cn',
            ),
            101 => 
            array (
                'callingcode' => '98',
                'capital' => 'Tehran',
                'code' => 'ir',
                'code_alpha3' => 'irn',
                'continent_id' => 1,
                'currency_code' => 'IRR',
                'currency_name' => 'Iranian rial',
                'emoji' => '🇮🇷',
                'full_name' => 'The Islamic Republic of Iran',
                'has_division' => 0,
                'id' => 102,
                'name' => 'Iran',
                'tld' => '.ir',
            ),
            102 => 
            array (
                'callingcode' => '506',
                'capital' => 'San José',
                'code' => 'cr',
                'code_alpha3' => 'cri',
                'continent_id' => 6,
                'currency_code' => 'CRC',
                'currency_name' => 'Costa Rican Colon',
                'emoji' => '🇨🇷',
                'full_name' => 'Republic of Costa Rica',
                'has_division' => 0,
                'id' => 103,
                'name' => 'Costa Rica',
                'tld' => '.cr',
            ),
            103 => 
            array (
                'callingcode' => '53',
                'capital' => 'Havana',
                'code' => 'cu',
                'code_alpha3' => 'cub',
                'continent_id' => 6,
                'currency_code' => 'CUC',
                'currency_name' => 'Cuban convertible peso',
                'emoji' => '🇨🇺',
                'full_name' => 'The Republic of Cuba',
                'has_division' => 0,
                'id' => 104,
                'name' => 'Cuba',
                'tld' => '.cu',
            ),
            104 => 
            array (
                'callingcode' => '1809',
                'capital' => 'Santo Domingo',
                'code' => 'do',
                'code_alpha3' => 'dom',
                'continent_id' => 6,
                'currency_code' => 'DOP',
                'currency_name' => 'Dominican Peso',
                'emoji' => '🇩🇴',
                'full_name' => 'The Dominican Republic',
                'has_division' => 0,
                'id' => 105,
                'name' => 'Dominican',
                'tld' => '.do',
            ),
            105 => 
            array (
                'callingcode' => '52',
                'capital' => 'Mexico City',
                'code' => 'mx',
                'code_alpha3' => 'mex',
                'continent_id' => 6,
                'currency_code' => 'MXN',
                'currency_name' => 'Mexican Peso',
                'emoji' => '🇲🇽',
                'full_name' => 'The United States of Mexico',
                'has_division' => 0,
                'id' => 106,
                'name' => 'Mexico',
                'tld' => '.mx',
            ),
            106 => 
            array (
                'callingcode' => '505',
                'capital' => 'Managua',
                'code' => 'ni',
                'code_alpha3' => 'nic',
                'continent_id' => 6,
                'currency_code' => 'NIO',
                'currency_name' => 'Cordoba Oro',
                'emoji' => '🇳🇮',
                'full_name' => 'The Republic of Nicaragua',
                'has_division' => 0,
                'id' => 107,
                'name' => 'Nicaragua',
                'tld' => '.ni',
            ),
            107 => 
            array (
                'callingcode' => '507',
                'capital' => 'Panama City',
                'code' => 'pa',
                'code_alpha3' => 'pan',
                'continent_id' => 6,
                'currency_code' => 'PAB',
                'currency_name' => 'Panamanian balboa',
                'emoji' => '🇵🇦',
                'full_name' => 'The Republic of Panama',
                'has_division' => 0,
                'id' => 108,
                'name' => 'Panama',
                'tld' => '.pa',
            ),
            108 => 
            array (
                'callingcode' => NULL,
                'capital' => NULL,
                'code' => 'an',
                'code_alpha3' => 'ant',
                'continent_id' => 7,
                'currency_code' => NULL,
                'currency_name' => NULL,
                'emoji' => NULL,
                'full_name' => 'Netherlands Antilles',
                'has_division' => 0,
                'id' => 109,
                'name' => 'Netherlands Antilles',
                'tld' => NULL,
            ),
            109 => 
            array (
                'callingcode' => '503',
                'capital' => 'San Salvador',
                'code' => 'sv',
                'code_alpha3' => 'slv',
                'continent_id' => 6,
                'currency_code' => 'SVC',
                'currency_name' => 'Salvadoran colón',
                'emoji' => '🇸🇻',
                'full_name' => 'The Republic of El Salvador',
                'has_division' => 0,
                'id' => 110,
                'name' => 'El Salvador',
                'tld' => '.sv',
            ),
            110 => 
            array (
                'callingcode' => '1787',
                'capital' => 'San Juan',
                'code' => 'pr',
                'code_alpha3' => 'ptr',
                'continent_id' => 6,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇵🇷',
                'full_name' => 'The Commonwealth of Puerto Rico',
                'has_division' => 0,
                'id' => 111,
                'name' => 'Puerto Rico',
                'tld' => '.pr',
            ),
            111 => 
            array (
                'callingcode' => '1784',
                'capital' => 'Kingstown',
                'code' => 'vc',
                'code_alpha3' => 'vag',
                'continent_id' => 6,
                'currency_code' => 'XCD',
                'currency_name' => 'East Caribbean Dollar',
                'emoji' => '🇻🇨',
                'full_name' => 'Saint Vincent and the Grenadines',
                'has_division' => 0,
                'id' => 112,
                'name' => 'Saint Vincent and the Grenadines',
                'tld' => '.vc',
            ),
            112 => 
            array (
                'callingcode' => '504',
                'capital' => 'Tegucigalpa',
                'code' => 'hn',
                'code_alpha3' => 'hnd',
                'continent_id' => 6,
                'currency_code' => 'HNL',
                'currency_name' => 'Lempira',
                'emoji' => '🇭🇳',
                'full_name' => 'Republic of Honduras',
                'has_division' => 0,
                'id' => 113,
                'name' => 'Honduras',
                'tld' => '.hn',
            ),
            113 => 
            array (
                'callingcode' => '502',
                'capital' => 'Guatemala City',
                'code' => 'gt',
                'code_alpha3' => 'gtm',
                'continent_id' => 6,
                'currency_code' => 'GTQ',
                'currency_name' => 'Quetzal',
                'emoji' => '🇬🇹',
                'full_name' => 'The Republic of Guatemala',
                'has_division' => 0,
                'id' => 114,
                'name' => 'Guatemala',
                'tld' => '.gt',
            ),
            114 => 
            array (
                'callingcode' => '995',
                'capital' => 'Tbilisi',
                'code' => 'ge',
                'code_alpha3' => 'geo',
                'continent_id' => 2,
                'currency_code' => 'GEL',
                'currency_name' => 'Lari',
                'emoji' => '🇬🇪',
                'full_name' => 'Georgia',
                'has_division' => 0,
                'id' => 115,
                'name' => 'Georgia',
                'tld' => '.ge',
            ),
            115 => 
            array (
                'callingcode' => '374',
                'capital' => 'Yerevan',
                'code' => 'am',
                'code_alpha3' => 'arm',
                'continent_id' => 2,
                'currency_code' => 'AMD',
                'currency_name' => 'Armenian Dram',
                'emoji' => '🇦🇲',
                'full_name' => 'The Republic of Armenia',
                'has_division' => 0,
                'id' => 116,
                'name' => 'Armenia',
                'tld' => '.am',
            ),
            116 => 
            array (
                'callingcode' => '994',
                'capital' => 'Baku',
                'code' => 'az',
                'code_alpha3' => 'aze',
                'continent_id' => 2,
                'currency_code' => 'AZN',
                'currency_name' => 'Azerbaijanian Manat',
                'emoji' => '🇦🇿',
                'full_name' => 'The Republic of Azerbaijan',
                'has_division' => 0,
                'id' => 117,
                'name' => 'Azerbaijan',
                'tld' => '.az',
            ),
            117 => 
            array (
                'callingcode' => '375',
                'capital' => 'Minsk',
                'code' => 'by',
                'code_alpha3' => 'blr',
                'continent_id' => 2,
                'currency_code' => 'BYR',
                'currency_name' => 'Belarussian Ruble',
                'emoji' => '🇧🇾',
                'full_name' => 'The Republic of Belarus',
                'has_division' => 0,
                'id' => 118,
                'name' => 'Belarus',
                'tld' => '.by',
            ),
            118 => 
            array (
                'callingcode' => '7',
                'capital' => 'Moscow',
                'code' => 'ru',
                'code_alpha3' => 'rus',
                'continent_id' => 2,
                'currency_code' => 'RUB',
                'currency_name' => 'Russian Ruble',
                'emoji' => '🇷🇺',
                'full_name' => 'Russian Federation',
                'has_division' => 0,
                'id' => 119,
                'name' => 'Russia',
                'tld' => '.ru',
            ),
            119 => 
            array (
                'callingcode' => '380',
                'capital' => 'Kyiv',
                'code' => 'ua',
                'code_alpha3' => 'ukr',
                'continent_id' => 2,
                'currency_code' => 'UAH',
                'currency_name' => 'Hryvnia',
                'emoji' => '🇺🇦',
                'full_name' => 'Ukraine',
                'has_division' => 0,
                'id' => 120,
                'name' => 'Ukraine',
                'tld' => '.ua',
            ),
            120 => 
            array (
                'callingcode' => '36',
                'capital' => 'Budapest',
                'code' => 'hu',
                'code_alpha3' => 'hun',
                'continent_id' => 2,
                'currency_code' => 'HUF',
                'currency_name' => 'Forint',
                'emoji' => '🇭🇺',
                'full_name' => 'Hungary',
                'has_division' => 0,
                'id' => 121,
                'name' => 'Hungary',
                'tld' => '.hu',
            ),
            121 => 
            array (
                'callingcode' => '354',
                'capital' => 'Reykjavik',
                'code' => 'is',
                'code_alpha3' => 'isl',
                'continent_id' => 2,
                'currency_code' => 'ISK',
                'currency_name' => 'Iceland Krona',
                'emoji' => '🇮🇸',
                'full_name' => 'The Republic of Iceland',
                'has_division' => 0,
                'id' => 122,
                'name' => 'Iceland',
                'tld' => '.is',
            ),
            122 => 
            array (
                'callingcode' => '356',
                'capital' => 'Valletta',
                'code' => 'mt',
                'code_alpha3' => 'mlt',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇲🇹',
                'full_name' => 'Republic of Malta',
                'has_division' => 0,
                'id' => 123,
                'name' => 'Malta',
                'tld' => '.mt',
            ),
            123 => 
            array (
                'callingcode' => '377',
                'capital' => 'Monaco',
                'code' => 'mc',
                'code_alpha3' => 'mco',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇲🇨',
                'full_name' => 'The Principality of Monaco',
                'has_division' => 0,
                'id' => 124,
                'name' => 'Monaco',
                'tld' => '.mc',
            ),
            124 => 
            array (
                'callingcode' => '47',
                'capital' => 'Oslo',
                'code' => 'no',
                'code_alpha3' => 'nor',
                'continent_id' => 2,
                'currency_code' => 'NOK',
                'currency_name' => 'Norwegian Krone',
                'emoji' => '🇳🇴',
                'full_name' => 'The Kingdom of Norway',
                'has_division' => 0,
                'id' => 125,
                'name' => 'Norway',
                'tld' => '.no',
            ),
            125 => 
            array (
                'callingcode' => '40',
                'capital' => 'Bucharest',
                'code' => 'ro',
                'code_alpha3' => 'rom',
                'continent_id' => 2,
                'currency_code' => 'RON',
                'currency_name' => 'New Romanian Leu',
                'emoji' => '🇷🇴',
                'full_name' => 'Romania',
                'has_division' => 0,
                'id' => 126,
                'name' => 'Romania',
                'tld' => '.ro',
            ),
            126 => 
            array (
                'callingcode' => '378',
                'capital' => 'City of San Marino',
                'code' => 'sm',
                'code_alpha3' => 'smr',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇸🇲',
                'full_name' => 'The Republic of San Marino',
                'has_division' => 0,
                'id' => 127,
                'name' => 'San Marino',
                'tld' => '.sm',
            ),
            127 => 
            array (
                'callingcode' => '46',
                'capital' => 'Stockholm',
                'code' => 'se',
                'code_alpha3' => 'swe',
                'continent_id' => 2,
                'currency_code' => 'SEK',
                'currency_name' => 'Swedish Krona',
                'emoji' => '🇸🇪',
                'full_name' => 'The Kingdom of Sweden',
                'has_division' => 0,
                'id' => 128,
                'name' => 'Sweden',
                'tld' => '.se',
            ),
            128 => 
            array (
                'callingcode' => '41',
                'capital' => 'Bern',
                'code' => 'ch',
                'code_alpha3' => 'che',
                'continent_id' => 2,
                'currency_code' => 'CHE',
            'currency_name' => 'WIR Euro (complementary currency)',
                'emoji' => '🇨🇭',
                'full_name' => 'Swiss Confederation',
                'has_division' => 0,
                'id' => 129,
                'name' => 'Switzerland',
                'tld' => '.ch',
            ),
            129 => 
            array (
                'callingcode' => '372',
                'capital' => 'Tallinn',
                'code' => 'ee',
                'code_alpha3' => 'est',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇪🇪',
                'full_name' => 'Republic of Estonia',
                'has_division' => 0,
                'id' => 130,
                'name' => 'Estonia',
                'tld' => '.ee',
            ),
            130 => 
            array (
                'callingcode' => '371',
                'capital' => 'Riga',
                'code' => 'lv',
                'code_alpha3' => 'lva',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇱🇻',
                'full_name' => 'Republic of Latvia',
                'has_division' => 0,
                'id' => 131,
                'name' => 'Latvia',
                'tld' => '.lv',
            ),
            131 => 
            array (
                'callingcode' => '370',
                'capital' => 'Vilnius',
                'code' => 'lt',
                'code_alpha3' => 'ltu',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇱🇹',
                'full_name' => 'The Republic of Lithuania',
                'has_division' => 0,
                'id' => 132,
                'name' => 'Lithuania',
                'tld' => '.lt',
            ),
            132 => 
            array (
                'callingcode' => '373',
                'capital' => 'Chișinău',
                'code' => 'md',
                'code_alpha3' => 'mda',
                'continent_id' => 2,
                'currency_code' => 'MDL',
                'currency_name' => 'Moldovan leu',
                'emoji' => '🇲🇩',
                'full_name' => 'The Republic of Moldova',
                'has_division' => 0,
                'id' => 133,
                'name' => 'Moldavia',
                'tld' => '.md',
            ),
            133 => 
            array (
                'callingcode' => '90',
                'capital' => 'Ankara',
                'code' => 'tr',
                'code_alpha3' => 'tur',
                'continent_id' => 1,
                'currency_code' => 'TRY',
                'currency_name' => 'Turkish Lira',
                'emoji' => '🇹🇷',
                'full_name' => 'The Republic of Turkey',
                'has_division' => 0,
                'id' => 134,
                'name' => 'Turkey',
                'tld' => '.tr',
            ),
            134 => 
            array (
                'callingcode' => '386',
                'capital' => 'Ljubljana',
                'code' => 'si',
                'code_alpha3' => 'svn',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇸🇮',
                'full_name' => 'The Republic of Slovenia',
                'has_division' => 0,
                'id' => 135,
                'name' => 'Slovenia',
                'tld' => '.si',
            ),
            135 => 
            array (
                'callingcode' => '420',
                'capital' => 'Prague',
                'code' => 'cz',
                'code_alpha3' => 'cze',
                'continent_id' => 2,
                'currency_code' => 'CZK',
                'currency_name' => 'Czech Koruna',
                'emoji' => '🇨🇿',
                'full_name' => 'The Czech Republic',
                'has_division' => 0,
                'id' => 136,
                'name' => 'Czech',
                'tld' => '.cz',
            ),
            136 => 
            array (
                'callingcode' => '421',
                'capital' => 'Bratislava',
                'code' => 'sk',
                'code_alpha3' => 'svk',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇸🇰',
                'full_name' => 'The Slovak Republic',
                'has_division' => 0,
                'id' => 137,
                'name' => 'Slovak',
                'tld' => '.sk',
            ),
            137 => 
            array (
                'callingcode' => '389',
                'capital' => 'Skopje',
                'code' => 'mk',
                'code_alpha3' => 'mkd',
                'continent_id' => 2,
                'currency_code' => 'MKD',
                'currency_name' => 'Macedonian denar',
                'emoji' => '🇲🇰',
                'full_name' => 'The Republic of Macedonia',
                'has_division' => 0,
                'id' => 138,
                'name' => 'Macedonia',
                'tld' => '.mk',
            ),
            138 => 
            array (
                'callingcode' => '387',
                'capital' => 'Sarajevo',
                'code' => 'ba',
                'code_alpha3' => 'bih',
                'continent_id' => 2,
                'currency_code' => 'BAM',
                'currency_name' => 'Convertible Mark',
                'emoji' => '🇧🇦',
                'full_name' => 'Bosnia and Herzegovina',
                'has_division' => 0,
                'id' => 139,
                'name' => 'Bosnia Hercegovina',
                'tld' => '.ba',
            ),
            139 => 
            array (
                'callingcode' => '3906698',
                'capital' => 'Vatican City',
                'code' => 'va',
                'code_alpha3' => 'vat',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇻🇦',
                'full_name' => 'Vatican City State',
                'has_division' => 0,
                'id' => 140,
                'name' => 'Vatican City State',
                'tld' => '.va',
            ),
            140 => 
            array (
                'callingcode' => '31',
                'capital' => 'Amsterdam',
                'code' => 'nl',
                'code_alpha3' => 'nld',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇳🇱',
                'full_name' => 'The Kingdom of Netherlands',
                'has_division' => 0,
                'id' => 141,
                'name' => 'Netherlands',
                'tld' => '.nl',
            ),
            141 => 
            array (
                'callingcode' => '385',
                'capital' => 'Zagreb',
                'code' => 'hr',
                'code_alpha3' => 'hrv',
                'continent_id' => 2,
                'currency_code' => 'HRK',
                'currency_name' => 'Croatian Kuna',
                'emoji' => '🇭🇷',
                'full_name' => 'The Republic of Croatia',
                'has_division' => 0,
                'id' => 142,
                'name' => 'Croatia',
                'tld' => '.hr',
            ),
            142 => 
            array (
                'callingcode' => '30',
                'capital' => 'Athens',
                'code' => 'gr',
                'code_alpha3' => 'grc',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇬🇷',
                'full_name' => 'The Hellenic Republic',
                'has_division' => 0,
                'id' => 143,
                'name' => 'Greece',
                'tld' => '.gr',
            ),
            143 => 
            array (
                'callingcode' => '353',
                'capital' => 'Dublin',
                'code' => 'ie',
                'code_alpha3' => 'irl',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇮🇪',
                'full_name' => 'The Republic of Ireland',
                'has_division' => 0,
                'id' => 144,
                'name' => 'Ireland',
                'tld' => '.ie',
            ),
            144 => 
            array (
                'callingcode' => '32',
                'capital' => 'Brussels',
                'code' => 'be',
                'code_alpha3' => 'bel',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇧🇪',
                'full_name' => 'The Kingdom Of Belgium',
                'has_division' => 0,
                'id' => 145,
                'name' => 'Belgium',
                'tld' => '.be',
            ),
            145 => 
            array (
                'callingcode' => '357',
                'capital' => 'Nicosia',
                'code' => 'cy',
                'code_alpha3' => 'cyp',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇨🇾',
                'full_name' => 'the Republic of Cyprus',
                'has_division' => 0,
                'id' => 146,
                'name' => 'Cyprus',
                'tld' => '.cy',
            ),
            146 => 
            array (
                'callingcode' => '45',
                'capital' => 'Copenhagen',
                'code' => 'dk',
                'code_alpha3' => 'dnk',
                'continent_id' => 2,
                'currency_code' => 'DKK',
                'currency_name' => 'Danish Krone',
                'emoji' => '🇩🇰',
                'full_name' => 'The Kingdom of Denmark',
                'has_division' => 0,
                'id' => 147,
                'name' => 'Denmark',
                'tld' => '.dk',
            ),
            147 => 
            array (
                'callingcode' => '44',
                'capital' => 'London',
                'code' => 'gb',
                'code_alpha3' => 'eng',
                'continent_id' => 2,
                'currency_code' => 'GBP',
                'currency_name' => 'Pound sterling',
                'emoji' => '🇬🇧',
                'full_name' => 'The United Kingdom of Great Britain and Northern Ireland',
                'has_division' => 1,
                'id' => 148,
                'name' => 'United Kingdom',
                'tld' => '.uk',
            ),
            148 => 
            array (
                'callingcode' => '49',
                'capital' => 'Berlin',
                'code' => 'de',
                'code_alpha3' => 'deu',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇩🇪',
                'full_name' => 'The Federal Republic of Germany',
                'has_division' => 0,
                'id' => 149,
                'name' => 'Germany',
                'tld' => '.de',
            ),
            149 => 
            array (
                'callingcode' => '33',
                'capital' => 'Paris',
                'code' => 'fr',
                'code_alpha3' => 'fra',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇫🇷',
                'full_name' => 'The French Republic',
                'has_division' => 0,
                'id' => 150,
                'name' => 'France',
                'tld' => '.fr',
            ),
            150 => 
            array (
                'callingcode' => '39',
                'capital' => 'Rome',
                'code' => 'it',
                'code_alpha3' => 'ita',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇮🇹',
                'full_name' => 'The Republic of Italy',
                'has_division' => 0,
                'id' => 151,
                'name' => 'Italy',
                'tld' => '.it',
            ),
            151 => 
            array (
                'callingcode' => '352',
                'capital' => 'Luxembourg',
                'code' => 'lu',
                'code_alpha3' => 'lux',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇱🇺',
                'full_name' => 'The Grand Duchy of Luxembourg',
                'has_division' => 0,
                'id' => 152,
                'name' => 'Luxembourg',
                'tld' => '.lu',
            ),
            152 => 
            array (
                'callingcode' => '351',
                'capital' => 'Lisbon',
                'code' => 'pt',
                'code_alpha3' => 'prt',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇵🇹',
                'full_name' => 'Portugal,the Portuguese Republic',
                'has_division' => 0,
                'id' => 153,
                'name' => 'Portugal',
                'tld' => '.pt',
            ),
            153 => 
            array (
                'callingcode' => '48',
                'capital' => 'Warsaw',
                'code' => 'pl',
                'code_alpha3' => 'pol',
                'continent_id' => 2,
                'currency_code' => 'PLN',
                'currency_name' => 'Zloty',
                'emoji' => '🇵🇱',
                'full_name' => 'The Republic of Poland',
                'has_division' => 0,
                'id' => 154,
                'name' => 'Poland',
                'tld' => '.pl',
            ),
            154 => 
            array (
                'callingcode' => '34',
                'capital' => 'Madrid',
                'code' => 'es',
                'code_alpha3' => 'esp',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇪🇸',
                'full_name' => 'The Kingdom of Spain',
                'has_division' => 0,
                'id' => 155,
                'name' => 'Spain',
                'tld' => '.es',
            ),
            155 => 
            array (
                'callingcode' => '355',
                'capital' => 'Tirana',
                'code' => 'al',
                'code_alpha3' => 'alb',
                'continent_id' => 2,
                'currency_code' => 'ALL',
                'currency_name' => 'Lek',
                'emoji' => '🇦🇱',
                'full_name' => 'The Republic of Albania',
                'has_division' => 0,
                'id' => 156,
                'name' => 'Albania',
                'tld' => '.al',
            ),
            156 => 
            array (
                'callingcode' => '376',
                'capital' => 'Andorra la Vella',
                'code' => 'ad',
                'code_alpha3' => 'and',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇦🇩',
                'full_name' => 'The Principality of Andorra',
                'has_division' => 0,
                'id' => 157,
                'name' => 'Andorra',
                'tld' => '.ad',
            ),
            157 => 
            array (
                'callingcode' => '423',
                'capital' => 'Vaduz',
                'code' => 'li',
                'code_alpha3' => 'lie',
                'continent_id' => 2,
                'currency_code' => 'CHF',
                'currency_name' => 'Swiss Franc',
                'emoji' => '🇱🇮',
                'full_name' => 'Principality of Liechtenstein',
                'has_division' => 0,
                'id' => 158,
                'name' => 'Liechtenstein',
                'tld' => '.li',
            ),
            158 => 
            array (
                'callingcode' => '381',
                'capital' => 'Belgrade',
                'code' => 'rs',
                'code_alpha3' => 'srb',
                'continent_id' => 2,
                'currency_code' => 'RSD',
                'currency_name' => 'Serbian Dinar',
                'emoji' => '🇷🇸',
                'full_name' => 'Republic of Serbia',
                'has_division' => 0,
                'id' => 159,
                'name' => 'Serbia',
                'tld' => '.rs',
            ),
            159 => 
            array (
                'callingcode' => '43',
                'capital' => 'Vienna',
                'code' => 'at',
                'code_alpha3' => 'aut',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇦🇹',
                'full_name' => 'The Republic Of Austria',
                'has_division' => 0,
                'id' => 160,
                'name' => 'Austria',
                'tld' => '.at',
            ),
            160 => 
            array (
                'callingcode' => '359',
                'capital' => 'Sofia',
                'code' => 'bg',
                'code_alpha3' => 'bgr',
                'continent_id' => 2,
                'currency_code' => 'BGN',
                'currency_name' => 'Bulgarian Lev',
                'emoji' => '🇧🇬',
                'full_name' => 'The Republic of Bulgaria',
                'has_division' => 0,
                'id' => 161,
                'name' => 'Bulgaria',
                'tld' => '.bg',
            ),
            161 => 
            array (
                'callingcode' => '358',
                'capital' => 'Helsinki',
                'code' => 'fi',
                'code_alpha3' => 'fin',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇫🇮',
                'full_name' => 'The Republic of Finland',
                'has_division' => 0,
                'id' => 162,
                'name' => 'Finland',
                'tld' => '.fi',
            ),
            162 => 
            array (
                'callingcode' => '350',
                'capital' => 'Gibraltar',
                'code' => 'gi',
                'code_alpha3' => 'gib',
                'continent_id' => 2,
                'currency_code' => 'GIP',
                'currency_name' => 'Gibraltar Pound',
                'emoji' => '🇬🇮',
                'full_name' => 'Gibraltar',
                'has_division' => 0,
                'id' => 163,
                'name' => 'Gibraltar',
                'tld' => '.gi',
            ),
            163 => 
            array (
                'callingcode' => '1767',
                'capital' => 'Roseau',
                'code' => 'dm',
                'code_alpha3' => 'dma',
                'continent_id' => 6,
                'currency_code' => 'XCD',
                'currency_name' => 'East Caribbean Dollar',
                'emoji' => '🇩🇲',
                'full_name' => 'The Commonwealth of Dominica',
                'has_division' => 0,
                'id' => 164,
                'name' => 'Dominica',
                'tld' => '.dm',
            ),
            164 => 
            array (
                'callingcode' => '1441',
                'capital' => 'Hamilton',
                'code' => 'bm',
                'code_alpha3' => 'bmu',
                'continent_id' => 6,
                'currency_code' => 'BMD',
                'currency_name' => 'Bermudian Dollar',
                'emoji' => '🇧🇲',
                'full_name' => 'Bermuda',
                'has_division' => 0,
                'id' => 165,
                'name' => 'Bermuda',
                'tld' => '.bm',
            ),
            165 => 
            array (
                'callingcode' => '1',
                'capital' => 'Ottawa',
                'code' => 'ca',
                'code_alpha3' => 'can',
                'continent_id' => 6,
                'currency_code' => 'CAD',
                'currency_name' => 'Canadian Dollar',
                'emoji' => '🇨🇦',
                'full_name' => 'Canada',
                'has_division' => 0,
                'id' => 166,
                'name' => 'Canada',
                'tld' => '.ca',
            ),
            166 => 
            array (
                'callingcode' => '1',
                'capital' => 'Washington D.C.',
                'code' => 'us',
                'code_alpha3' => 'usa',
                'continent_id' => 6,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇺🇸',
                'full_name' => 'The United States of America',
                'has_division' => 1,
                'id' => 167,
                'name' => 'United States',
                'tld' => '.us',
            ),
            167 => 
            array (
                'callingcode' => '299',
                'capital' => 'Nuuk',
                'code' => 'gl',
                'code_alpha3' => 'grl',
                'continent_id' => 6,
                'currency_code' => 'DKK',
                'currency_name' => 'Danish Krone',
                'emoji' => '🇬🇱',
                'full_name' => 'Greenland',
                'has_division' => 0,
                'id' => 168,
                'name' => 'Greenland',
                'tld' => '.gl',
            ),
            168 => 
            array (
                'callingcode' => '676',
                'capital' => 'Nuku\'alofa',
                'code' => 'to',
                'code_alpha3' => 'ton',
                'continent_id' => 4,
                'currency_code' => 'TOP',
                'currency_name' => 'Pa’anga',
                'emoji' => '🇹🇴',
                'full_name' => 'The Kingdom of Tonga',
                'has_division' => 0,
                'id' => 169,
                'name' => 'Tonga',
                'tld' => '.to',
            ),
            169 => 
            array (
                'callingcode' => '61',
                'capital' => 'Canberra',
                'code' => 'au',
                'code_alpha3' => 'aus',
                'continent_id' => 4,
                'currency_code' => 'AUD',
                'currency_name' => 'Australian Dollar',
                'emoji' => '🇦🇺',
                'full_name' => 'The Commonwealth of Australia',
                'has_division' => 1,
                'id' => 170,
                'name' => 'Australia',
                'tld' => '.au',
            ),
            170 => 
            array (
                'callingcode' => '682',
                'capital' => 'Avarua',
                'code' => 'ck',
                'code_alpha3' => 'cok',
                'continent_id' => 4,
                'currency_code' => 'NZD',
                'currency_name' => 'New Zealand Dollar',
                'emoji' => '🇨🇰',
                'full_name' => 'The Cook Islands',
                'has_division' => 0,
                'id' => 171,
                'name' => 'Cook Is',
                'tld' => '.ck',
            ),
            171 => 
            array (
                'callingcode' => '674',
                'capital' => 'Yaren',
                'code' => 'nr',
                'code_alpha3' => 'nru',
                'continent_id' => 4,
                'currency_code' => 'AUD',
                'currency_name' => 'Australian Dollar',
                'emoji' => '🇳🇷',
                'full_name' => 'The Republic of Nauru',
                'has_division' => 0,
                'id' => 172,
                'name' => 'Nauru',
                'tld' => '.nr',
            ),
            172 => 
            array (
                'callingcode' => '687',
                'capital' => 'Nouméa',
                'code' => 'nc',
                'code_alpha3' => 'ncl',
                'continent_id' => 4,
                'currency_code' => 'XPF',
                'currency_name' => 'CFP Franc',
                'emoji' => '🇳🇨',
                'full_name' => 'New Caledonia',
                'has_division' => 0,
                'id' => 173,
                'name' => 'New Caledonia',
                'tld' => '.nc',
            ),
            173 => 
            array (
                'callingcode' => '678',
                'capital' => 'Port Vila',
                'code' => 'vu',
                'code_alpha3' => 'vut',
                'continent_id' => 4,
                'currency_code' => 'VUV',
                'currency_name' => 'Vatu',
                'emoji' => '🇻🇺',
                'full_name' => 'Republic of Vanuatu',
                'has_division' => 0,
                'id' => 174,
                'name' => 'Vanuatu',
                'tld' => '.vu',
            ),
            174 => 
            array (
                'callingcode' => '677',
                'capital' => 'Honiara',
                'code' => 'sb',
                'code_alpha3' => 'slb',
                'continent_id' => 4,
                'currency_code' => 'SBD',
                'currency_name' => 'Solomon Islands Dollar',
                'emoji' => '🇸🇧',
                'full_name' => 'Solomon Islands',
                'has_division' => 0,
                'id' => 175,
                'name' => 'Solomon Is',
                'tld' => '.sb',
            ),
            175 => 
            array (
                'callingcode' => '685',
                'capital' => 'Apia',
                'code' => 'ws',
                'code_alpha3' => 'wsm',
                'continent_id' => 4,
                'currency_code' => 'WST',
                'currency_name' => 'Tala',
                'emoji' => '🇼🇸',
                'full_name' => 'The Independent State of Samoa',
                'has_division' => 0,
                'id' => 176,
                'name' => 'Samoa',
                'tld' => '.ws',
            ),
            176 => 
            array (
                'callingcode' => '688',
                'capital' => 'Funafuti',
                'code' => 'tv',
                'code_alpha3' => 'tuv',
                'continent_id' => 4,
                'currency_code' => 'AUD',
                'currency_name' => 'Australian Dollar',
                'emoji' => '🇹🇻',
                'full_name' => 'Tuvalu',
                'has_division' => 0,
                'id' => 177,
                'name' => 'Tuvalu',
                'tld' => '.tv',
            ),
            177 => 
            array (
                'callingcode' => '691',
                'capital' => 'Palikir',
                'code' => 'fm',
                'code_alpha3' => 'fsm',
                'continent_id' => 4,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇫🇲',
                'full_name' => 'Federated States of Micronesia',
                'has_division' => 0,
                'id' => 178,
                'name' => 'Micronesia',
                'tld' => '.fm',
            ),
            178 => 
            array (
                'callingcode' => '692',
                'capital' => 'Majuro',
                'code' => 'mh',
                'code_alpha3' => 'mhl',
                'continent_id' => 4,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇲🇭',
                'full_name' => 'The Republic of Marshall Island ',
                'has_division' => 0,
                'id' => 179,
                'name' => 'Marshall Is Rep',
                'tld' => '.mh',
            ),
            179 => 
            array (
                'callingcode' => '686',
                'capital' => 'South Tarawa',
                'code' => 'ki',
                'code_alpha3' => 'kir',
                'continent_id' => 4,
                'currency_code' => 'AUD',
                'currency_name' => 'Australian Dollar',
                'emoji' => '🇰🇮',
                'full_name' => 'The Republic of Kiribati',
                'has_division' => 0,
                'id' => 180,
                'name' => 'Kiribati',
                'tld' => '.ki',
            ),
            180 => 
            array (
                'callingcode' => '689',
                'capital' => 'Papeetē',
                'code' => 'pf',
                'code_alpha3' => 'pyf',
                'continent_id' => 4,
                'currency_code' => 'XPF',
                'currency_name' => 'CFP Franc',
                'emoji' => '🇵🇫',
                'full_name' => 'French Polynesia',
                'has_division' => 0,
                'id' => 181,
                'name' => 'French Polynesia',
                'tld' => '.pf',
            ),
            181 => 
            array (
                'callingcode' => '64',
                'capital' => 'Wellington',
                'code' => 'nz',
                'code_alpha3' => 'nzl',
                'continent_id' => 4,
                'currency_code' => 'NZD',
                'currency_name' => 'New Zealand Dollar',
                'emoji' => '🇳🇿',
                'full_name' => 'New Zealand',
                'has_division' => 0,
                'id' => 182,
                'name' => 'New Zealand',
                'tld' => '.nz',
            ),
            182 => 
            array (
                'callingcode' => '679',
                'capital' => 'Suva',
                'code' => 'fj',
                'code_alpha3' => 'fji',
                'continent_id' => 4,
                'currency_code' => 'FJD',
                'currency_name' => 'Fiji Dollar',
                'emoji' => '🇫🇯',
                'full_name' => 'The Republic of Fiji',
                'has_division' => 0,
                'id' => 183,
                'name' => 'Fiji',
                'tld' => '.fj',
            ),
            183 => 
            array (
                'callingcode' => '675',
                'capital' => 'Port Moresby',
                'code' => 'pg',
                'code_alpha3' => 'png',
                'continent_id' => 4,
                'currency_code' => 'PGK',
                'currency_name' => 'Kina',
                'emoji' => '🇵🇬',
                'full_name' => 'The Independent State of Papua New Guinea',
                'has_division' => 0,
                'id' => 184,
                'name' => 'Papua New Guinea',
                'tld' => '.pg',
            ),
            184 => 
            array (
                'callingcode' => '680',
                'capital' => 'Ngerulmud',
                'code' => 'pw',
                'code_alpha3' => 'plw',
                'continent_id' => 4,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇵🇼',
                'full_name' => 'The Republic of Palau',
                'has_division' => 0,
                'id' => 185,
                'name' => 'Palau',
                'tld' => '.pw',
            ),
            185 => 
            array (
                'callingcode' => '56',
                'capital' => 'Santiago',
                'code' => 'cl',
                'code_alpha3' => 'chl',
                'continent_id' => 7,
                'currency_code' => 'CLP',
                'currency_name' => 'Chilean Peso',
                'emoji' => '🇨🇱',
                'full_name' => 'Republic of Chile',
                'has_division' => 0,
                'id' => 186,
                'name' => 'Chile',
                'tld' => '.cl',
            ),
            186 => 
            array (
                'callingcode' => '57',
                'capital' => 'Bogotá',
                'code' => 'co',
                'code_alpha3' => 'col',
                'continent_id' => 7,
                'currency_code' => 'COP',
                'currency_name' => 'Colombian Peso',
                'emoji' => '🇨🇴',
                'full_name' => 'The Republic of Colombia',
                'has_division' => 0,
                'id' => 187,
                'name' => 'Colombia',
                'tld' => '.co',
            ),
            187 => 
            array (
                'callingcode' => '592',
                'capital' => 'Georgetown',
                'code' => 'gy',
                'code_alpha3' => 'guy',
                'continent_id' => 7,
                'currency_code' => 'GYD',
                'currency_name' => 'Guyana Dollar',
                'emoji' => '🇬🇾',
                'full_name' => 'The Republic of Guyana',
                'has_division' => 0,
                'id' => 188,
                'name' => 'Guyana',
                'tld' => '.gy',
            ),
            188 => 
            array (
                'callingcode' => '595',
                'capital' => 'Asunción',
                'code' => 'py',
                'code_alpha3' => 'pry',
                'continent_id' => 7,
                'currency_code' => 'PYG',
                'currency_name' => 'Guarani',
                'emoji' => '🇵🇾',
                'full_name' => 'The Republic of Paraguay',
                'has_division' => 0,
                'id' => 189,
                'name' => 'Paraguay',
                'tld' => '.py',
            ),
            189 => 
            array (
                'callingcode' => '51',
                'capital' => 'Lima',
                'code' => 'pe',
                'code_alpha3' => 'per',
                'continent_id' => 7,
                'currency_code' => 'PEN',
                'currency_name' => 'Nuevo Sol',
                'emoji' => '🇵🇪',
                'full_name' => 'The Republic of Peru',
                'has_division' => 0,
                'id' => 190,
                'name' => 'Peru',
                'tld' => '.pe',
            ),
            190 => 
            array (
                'callingcode' => '597',
                'capital' => 'Paramaribo',
                'code' => 'sr',
                'code_alpha3' => 'sur',
                'continent_id' => 7,
                'currency_code' => 'SRD',
                'currency_name' => 'Surinam Dollar',
                'emoji' => '🇸🇷',
                'full_name' => 'The Republic of Suriname',
                'has_division' => 0,
                'id' => 191,
                'name' => 'Suriname',
                'tld' => '.sr',
            ),
            191 => 
            array (
                'callingcode' => '58',
                'capital' => 'Caracas',
                'code' => 've',
                'code_alpha3' => 'ven',
                'continent_id' => 7,
                'currency_code' => 'VEF',
                'currency_name' => 'Venezuelan bolívar',
                'emoji' => '🇻🇪',
                'full_name' => 'Bolivarian Republic of Venezuela',
                'has_division' => 0,
                'id' => 192,
                'name' => 'Venezuela',
                'tld' => '.ve',
            ),
            192 => 
            array (
                'callingcode' => '598',
                'capital' => 'Montevideo',
                'code' => 'uy',
                'code_alpha3' => 'ury',
                'continent_id' => 7,
                'currency_code' => 'UYU',
                'currency_name' => 'Peso Uruguayo',
                'emoji' => '🇺🇾',
                'full_name' => 'The Oriental Republic of Uruguay',
                'has_division' => 0,
                'id' => 193,
                'name' => 'Uruguay',
                'tld' => '.uy',
            ),
            193 => 
            array (
                'callingcode' => '593',
                'capital' => 'Quito',
                'code' => 'ec',
                'code_alpha3' => 'ecu',
                'continent_id' => 7,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇪🇨',
                'full_name' => 'The Republic of Ecuador',
                'has_division' => 0,
                'id' => 194,
                'name' => 'Ecuador',
                'tld' => '.ec',
            ),
            194 => 
            array (
                'callingcode' => '1268',
                'capital' => 'Saint John\'s',
                'code' => 'ag',
                'code_alpha3' => 'atg',
                'continent_id' => 7,
                'currency_code' => 'XCD',
                'currency_name' => 'East Caribbean Dollar',
                'emoji' => '🇦🇬',
                'full_name' => 'Antigua and Barbuda',
                'has_division' => 0,
                'id' => 195,
                'name' => 'Antigua and Barbuda',
                'tld' => '.ag',
            ),
            195 => 
            array (
                'callingcode' => '297',
                'capital' => 'Oranjestad',
                'code' => 'aw',
                'code_alpha3' => 'abw',
                'continent_id' => 7,
                'currency_code' => 'AWG',
                'currency_name' => 'Aruban Florin',
                'emoji' => '🇦🇼',
                'full_name' => 'Aruba',
                'has_division' => 0,
                'id' => 196,
                'name' => 'Aruba',
                'tld' => '.aw',
            ),
            196 => 
            array (
                'callingcode' => '1242',
                'capital' => 'Nassau',
                'code' => 'bs',
                'code_alpha3' => 'bhs',
                'continent_id' => 7,
                'currency_code' => 'BSD',
                'currency_name' => 'Bahamian Dollar',
                'emoji' => '🇧🇸',
                'full_name' => 'The Commonwealth of The Bahamas',
                'has_division' => 0,
                'id' => 197,
                'name' => 'Bahamas',
                'tld' => '.bs',
            ),
            197 => 
            array (
                'callingcode' => '1246',
                'capital' => 'Bridgetown',
                'code' => 'bb',
                'code_alpha3' => 'brb',
                'continent_id' => 7,
                'currency_code' => 'BBD',
                'currency_name' => 'Barbados Dollar',
                'emoji' => '🇧🇧',
                'full_name' => 'Barbados',
                'has_division' => 0,
                'id' => 198,
                'name' => 'Barbados',
                'tld' => '.bb',
            ),
            198 => 
            array (
                'callingcode' => '1345',
                'capital' => 'George Town',
                'code' => 'ky',
                'code_alpha3' => 'cym',
                'continent_id' => 7,
                'currency_code' => 'KYD',
                'currency_name' => 'Cayman Islands Dollar',
                'emoji' => '🇰🇾',
                'full_name' => 'Cayman Islands',
                'has_division' => 0,
                'id' => 199,
                'name' => 'Cayman Is',
                'tld' => '.ky',
            ),
            199 => 
            array (
                'callingcode' => '1473',
                'capital' => 'St. George\'s',
                'code' => 'gd',
                'code_alpha3' => 'grd',
                'continent_id' => 7,
                'currency_code' => 'XCD',
                'currency_name' => 'East Caribbean Dollar',
                'emoji' => '🇬🇩',
                'full_name' => 'Grenada',
                'has_division' => 0,
                'id' => 200,
                'name' => 'Grenada',
                'tld' => '.gd',
            ),
            200 => 
            array (
                'callingcode' => '509',
                'capital' => 'Port-au-Prince',
                'code' => 'ht',
                'code_alpha3' => 'hti',
                'continent_id' => 7,
                'currency_code' => 'HTG',
                'currency_name' => 'Haitian gourde',
                'emoji' => '🇭🇹',
                'full_name' => 'The Republic of Haiti',
                'has_division' => 0,
                'id' => 201,
                'name' => 'Haiti',
                'tld' => '.ht',
            ),
            201 => 
            array (
                'callingcode' => '1876',
                'capital' => 'Kingston',
                'code' => 'jm',
                'code_alpha3' => 'jam',
                'continent_id' => 7,
                'currency_code' => 'JMD',
                'currency_name' => 'Jamaican Dollar',
                'emoji' => '🇯🇲',
                'full_name' => 'Jamaica',
                'has_division' => 0,
                'id' => 202,
                'name' => 'Jamaica',
                'tld' => '.jm',
            ),
            202 => 
            array (
                'callingcode' => '596',
                'capital' => 'Fort-de-France',
                'code' => 'mq',
                'code_alpha3' => 'mtq',
                'continent_id' => 7,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇲🇶',
                'full_name' => 'Martinique',
                'has_division' => 0,
                'id' => 203,
                'name' => 'Martinique',
                'tld' => '.mq',
            ),
            203 => 
            array (
                'callingcode' => '1664',
                'capital' => 'Plymouth',
                'code' => 'ms',
                'code_alpha3' => 'msr',
                'continent_id' => 7,
                'currency_code' => 'XCD',
                'currency_name' => 'East Caribbean Dollar',
                'emoji' => '🇲🇸',
                'full_name' => 'Montserrat',
                'has_division' => 0,
                'id' => 204,
                'name' => 'Montserrat',
                'tld' => '.ms',
            ),
            204 => 
            array (
                'callingcode' => '1868',
                'capital' => 'Port of Spain',
                'code' => 'tt',
                'code_alpha3' => 'tto',
                'continent_id' => 7,
                'currency_code' => 'TTD',
                'currency_name' => 'Trinidad and Tobago Dollar',
                'emoji' => '🇹🇹',
                'full_name' => 'Republic of Trinidad and Tobago',
                'has_division' => 0,
                'id' => 205,
                'name' => 'Trinidad and Tobago',
                'tld' => '.tt',
            ),
            205 => 
            array (
                'callingcode' => '1869',
                'capital' => 'Basseterre',
                'code' => 'kn',
                'code_alpha3' => 'kna',
                'continent_id' => 7,
                'currency_code' => 'XCD',
                'currency_name' => 'East Caribbean Dollar',
                'emoji' => '🇰🇳',
                'full_name' => 'The Federation of Saint Kitts and Nevis',
                'has_division' => 0,
                'id' => 206,
                'name' => 'St Kitts-Nevis',
                'tld' => '.kn',
            ),
            206 => 
            array (
                'callingcode' => '508',
                'capital' => 'Saint-Pierre',
                'code' => 'pm',
                'code_alpha3' => 'spm',
                'continent_id' => 7,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇵🇲',
                'full_name' => 'The Islands of st pierre and miquelon',
                'has_division' => 0,
                'id' => 207,
                'name' => 'St.Pierre and Miquelon',
                'tld' => '.pm',
            ),
            207 => 
            array (
                'callingcode' => '54',
                'capital' => 'Buenos Aires',
                'code' => 'ar',
                'code_alpha3' => 'arg',
                'continent_id' => 7,
                'currency_code' => 'ARS',
                'currency_name' => 'Argentine Peso',
                'emoji' => '🇦🇷',
                'full_name' => 'The Republic of Argentina',
                'has_division' => 0,
                'id' => 208,
                'name' => 'Argentina',
                'tld' => '.ar',
            ),
            208 => 
            array (
                'callingcode' => '501',
                'capital' => 'Belmopan',
                'code' => 'bz',
                'code_alpha3' => 'blz',
                'continent_id' => 7,
                'currency_code' => 'BZD',
                'currency_name' => 'Belize Dollar',
                'emoji' => '🇧🇿',
                'full_name' => 'Belize',
                'has_division' => 0,
                'id' => 209,
                'name' => 'Belize',
                'tld' => '.bz',
            ),
            209 => 
            array (
                'callingcode' => '591',
                'capital' => 'Sucre',
                'code' => 'bo',
                'code_alpha3' => 'bol',
                'continent_id' => 7,
                'currency_code' => 'BOB',
                'currency_name' => 'Boliviano',
                'emoji' => '🇧🇴',
                'full_name' => 'The Republic of Bolivia',
                'has_division' => 0,
                'id' => 210,
                'name' => 'Bolivia',
                'tld' => '.bo',
            ),
            210 => 
            array (
                'callingcode' => '55',
                'capital' => 'Brasília',
                'code' => 'br',
                'code_alpha3' => 'bra',
                'continent_id' => 7,
                'currency_code' => 'BRL',
                'currency_name' => 'Brazilian Real',
                'emoji' => '🇧🇷',
                'full_name' => 'The Federative Republic of Brazil',
                'has_division' => 0,
                'id' => 211,
                'name' => 'Brazil',
                'tld' => '.br',
            ),
            211 => 
            array (
                'callingcode' => '1684',
                'capital' => 'Pago Pago',
                'code' => 'as',
                'code_alpha3' => 'asm',
                'continent_id' => 6,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇦🇸',
                'full_name' => 'American Samoa',
                'has_division' => 0,
                'id' => 212,
                'name' => 'American Samoa',
                'tld' => '.as',
            ),
            212 => 
            array (
                'callingcode' => '358',
                'capital' => 'Mariehamn',
                'code' => 'ax',
                'code_alpha3' => 'ala',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇦🇽',
                'full_name' => 'Aland Island,Ahvenanmaa',
                'has_division' => 0,
                'id' => 213,
                'name' => 'Aland Islands',
                'tld' => '.ax',
            ),
            213 => 
            array (
                'callingcode' => '590',
                'capital' => 'Gustavia',
                'code' => 'bl',
                'code_alpha3' => 'blm',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇧🇱',
                'full_name' => 'Collectivitéde Saint-Barthélemy',
                'has_division' => 0,
                'id' => 214,
                'name' => 'Saint Barthélemy',
                'tld' => '.bl',
            ),
            214 => 
            array (
                'callingcode' => NULL,
                'capital' => NULL,
                'code' => 'bq',
                'code_alpha3' => 'bes',
                'continent_id' => 2,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇧🇶',
                'full_name' => 'Bonaire, Sint Eustatius and Saba',
                'has_division' => 0,
                'id' => 215,
                'name' => 'Bonaire, Sint Eustatius and Saba',
                'tld' => NULL,
            ),
            215 => 
            array (
                'callingcode' => NULL,
                'capital' => '',
                'code' => 'bv',
                'code_alpha3' => 'bvt',
                'continent_id' => 2,
                'currency_code' => 'NOK',
                'currency_name' => 'Norwegian krone',
                'emoji' => '🇧🇻',
                'full_name' => 'Bouvet Island',
                'has_division' => 0,
                'id' => 216,
                'name' => 'Bouvet Island',
                'tld' => '.bv',
            ),
            216 => 
            array (
                'callingcode' => '61',
                'capital' => 'West Island',
                'code' => 'cc',
                'code_alpha3' => 'cck',
                'continent_id' => 4,
                'currency_code' => 'AUD',
                'currency_name' => 'Australian dollar',
                'emoji' => '🇨🇨',
            'full_name' => 'Cocos (Keeling) Islands',
                'has_division' => 0,
                'id' => 217,
            'name' => 'Cocos (Keeling) Islands',
                'tld' => '.cc',
            ),
            217 => 
            array (
                'callingcode' => '5999',
                'capital' => 'Willemstad',
                'code' => 'cw',
                'code_alpha3' => 'cuw',
                'continent_id' => 2,
                'currency_code' => 'ANG',
                'currency_name' => 'Netherlands Antillean Guilder',
                'emoji' => '🇨🇼',
                'full_name' => 'Curaçao',
                'has_division' => 0,
                'id' => 218,
                'name' => 'Curaçao',
                'tld' => '.cw',
            ),
            218 => 
            array (
                'callingcode' => '61',
                'capital' => 'Flying Fish Cove',
                'code' => 'cx',
                'code_alpha3' => 'cxr',
                'continent_id' => 4,
                'currency_code' => 'AUD',
                'currency_name' => 'Australian dollar',
                'emoji' => '🇨🇽',
                'full_name' => 'Christmas Island',
                'has_division' => 0,
                'id' => 219,
                'name' => 'Christmas Island',
                'tld' => '.cx',
            ),
            219 => 
            array (
                'callingcode' => '500',
                'capital' => 'Stanley',
                'code' => 'fk',
                'code_alpha3' => 'flk',
                'continent_id' => 2,
                'currency_code' => 'FKP',
                'currency_name' => 'Falkland Islands Pound',
                'emoji' => '🇫🇰',
            'full_name' => 'Falkland Islands (Malvinas)',
                'has_division' => 0,
                'id' => 220,
            'name' => 'Falkland Islands (Malvinas)',
                'tld' => '.fk',
            ),
            220 => 
            array (
                'callingcode' => '298',
                'capital' => 'Tórshavn',
                'code' => 'fo',
                'code_alpha3' => 'fro',
                'continent_id' => 2,
                'currency_code' => 'DKK',
                'currency_name' => 'Danish Krone',
                'emoji' => '🇫🇴',
                'full_name' => 'Faroe Islands',
                'has_division' => 0,
                'id' => 221,
                'name' => 'Faroe Islands',
                'tld' => '.fo',
            ),
            221 => 
            array (
                'callingcode' => '594',
                'capital' => 'Cayenne',
                'code' => 'gf',
                'code_alpha3' => 'guf',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇬🇫',
                'full_name' => 'French Guiana',
                'has_division' => 0,
                'id' => 222,
                'name' => 'French Guiana',
                'tld' => '.gf',
            ),
            222 => 
            array (
                'callingcode' => '44',
                'capital' => 'St. Peter Port',
                'code' => 'gg',
                'code_alpha3' => 'ggy',
                'continent_id' => 2,
                'currency_code' => 'GBP',
                'currency_name' => 'Pound Sterling',
                'emoji' => '🇬🇬',
                'full_name' => 'Bailiwick of Guernsey',
                'has_division' => 0,
                'id' => 223,
                'name' => 'Guernsey',
                'tld' => '.gg',
            ),
            223 => 
            array (
                'callingcode' => '500',
                'capital' => 'King Edward Point',
                'code' => 'gs',
                'code_alpha3' => 'sgs',
                'continent_id' => 2,
                'currency_code' => 'GBP',
                'currency_name' => 'Pound sterling',
                'emoji' => '🇬🇸',
                'full_name' => 'South Georgia and The South Sandwich Islands',
                'has_division' => 0,
                'id' => 224,
                'name' => 'South Georgia and The South Sandwich Islands',
                'tld' => '.gs',
            ),
            224 => 
            array (
                'callingcode' => '1671',
                'capital' => 'Hagåtña',
                'code' => 'gu',
                'code_alpha3' => 'gum',
                'continent_id' => 6,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇬🇺',
                'full_name' => 'The Territory of Guahan,Guam',
                'has_division' => 0,
                'id' => 225,
                'name' => 'Guam',
                'tld' => '.gu',
            ),
            225 => 
            array (
                'callingcode' => NULL,
                'capital' => '',
                'code' => 'hm',
                'code_alpha3' => 'hmd',
                'continent_id' => 4,
                'currency_code' => 'AUD',
                'currency_name' => 'Australian Dollar',
                'emoji' => '🇭🇲',
                'full_name' => 'Heard Island and McDonald Islands',
                'has_division' => 0,
                'id' => 226,
                'name' => 'Heard Island and McDonald Islands',
                'tld' => '.hm',
            ),
            226 => 
            array (
                'callingcode' => '44',
                'capital' => 'Douglas',
                'code' => 'im',
                'code_alpha3' => 'imn',
                'continent_id' => 2,
                'currency_code' => 'GBP',
                'currency_name' => 'Pound Sterling',
                'emoji' => '🇮🇲',
                'full_name' => 'Isle Of Man',
                'has_division' => 0,
                'id' => 227,
                'name' => 'Isle Of Man',
                'tld' => '.im',
            ),
            227 => 
            array (
                'callingcode' => '246',
                'capital' => 'Diego Garcia',
                'code' => 'io',
                'code_alpha3' => 'iot',
                'continent_id' => 2,
                'currency_code' => 'GBP',
                'currency_name' => 'Pound Sterling',
                'emoji' => '🇮🇴',
                'full_name' => 'British Indian Ocean Territory',
                'has_division' => 0,
                'id' => 228,
                'name' => 'British Indian Ocean Territory',
                'tld' => '.io',
            ),
            228 => 
            array (
                'callingcode' => '44',
                'capital' => 'Saint Helier',
                'code' => 'je',
                'code_alpha3' => 'jey',
                'continent_id' => 2,
                'currency_code' => 'GBP',
                'currency_name' => 'Pound Sterling',
                'emoji' => '🇯🇪',
                'full_name' => 'Jersey',
                'has_division' => 0,
                'id' => 229,
                'name' => 'Jersey',
                'tld' => '.je',
            ),
            229 => 
            array (
                'callingcode' => '1758',
                'capital' => 'Castries',
                'code' => 'lc',
                'code_alpha3' => 'lca',
                'continent_id' => 2,
                'currency_code' => 'XCD',
                'currency_name' => 'East Caribbean Dollar',
                'emoji' => '🇱🇨',
                'full_name' => 'Saint Lucia , St. Lucia',
                'has_division' => 0,
                'id' => 230,
                'name' => 'Saint Lucia',
                'tld' => '.lc',
            ),
            230 => 
            array (
                'callingcode' => '590',
                'capital' => 'Marigot',
                'code' => 'mf',
                'code_alpha3' => 'maf',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇲🇫',
                'full_name' => 'Saint Martin',
                'has_division' => 0,
                'id' => 231,
                'name' => 'Saint Martin',
                'tld' => '.fr',
            ),
            231 => 
            array (
                'callingcode' => '1670',
                'capital' => 'Saipan',
                'code' => 'mp',
                'code_alpha3' => 'mnp',
                'continent_id' => 6,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇲🇵',
                'full_name' => 'Northern Mariana Islands',
                'has_division' => 0,
                'id' => 232,
                'name' => 'Northern Mariana Islands',
                'tld' => '.mp',
            ),
            232 => 
            array (
                'callingcode' => '672',
                'capital' => 'Kingston',
                'code' => 'nf',
                'code_alpha3' => 'nfk',
                'continent_id' => 4,
                'currency_code' => 'AUD',
                'currency_name' => 'Australian Dollar',
                'emoji' => '🇳🇫',
                'full_name' => 'Norfolk Island',
                'has_division' => 0,
                'id' => 233,
                'name' => 'Norfolk Island',
                'tld' => '.nf',
            ),
            233 => 
            array (
                'callingcode' => '683',
                'capital' => 'Alofi',
                'code' => 'nu',
                'code_alpha3' => 'niu',
                'continent_id' => 4,
                'currency_code' => 'NZD',
                'currency_name' => 'New Zealand Dollar',
                'emoji' => '🇳🇺',
                'full_name' => 'Niue',
                'has_division' => 0,
                'id' => 234,
                'name' => 'Niue',
                'tld' => '.nu',
            ),
            234 => 
            array (
                'callingcode' => '508',
                'capital' => 'Saint-Pierre',
                'code' => 'pm',
                'code_alpha3' => 'spm',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇵🇲',
                'full_name' => 'Saint Pierre and Miquelon',
                'has_division' => 0,
                'id' => 235,
                'name' => 'Saint Pierre and Miquelon',
                'tld' => '.pm',
            ),
            235 => 
            array (
                'callingcode' => '64',
                'capital' => 'Adamstown',
                'code' => 'pn',
                'code_alpha3' => 'pcn',
                'continent_id' => 2,
                'currency_code' => 'NZD',
                'currency_name' => 'New Zealand Dollar',
                'emoji' => '🇵🇳',
                'full_name' => 'Pitcairn Islands',
                'has_division' => 0,
                'id' => 236,
                'name' => 'Pitcairn Islands',
                'tld' => '.pn',
            ),
            236 => 
            array (
                'callingcode' => NULL,
                'capital' => NULL,
                'code' => 'sh',
                'code_alpha3' => 'shn',
                'continent_id' => 2,
                'currency_code' => 'SHP',
                'currency_name' => 'Saint Helena pound',
                'emoji' => '🇸🇭',
                'full_name' => 'Saint Helena',
                'has_division' => 0,
                'id' => 237,
                'name' => 'Saint Helena',
                'tld' => NULL,
            ),
            237 => 
            array (
                'callingcode' => '4779',
                'capital' => 'Longyearbyen',
                'code' => 'sj',
                'code_alpha3' => 'sjm',
                'continent_id' => 2,
                'currency_code' => 'NOK',
                'currency_name' => 'Norwegian krone',
                'emoji' => '🇸🇯',
                'full_name' => 'Svalbard and Jan Mayen Islands',
                'has_division' => 0,
                'id' => 238,
                'name' => 'Svalbard and Jan Mayen Islands',
                'tld' => '.sj',
            ),
            238 => 
            array (
                'callingcode' => '1721',
                'capital' => 'Philipsburg',
                'code' => 'sx',
                'code_alpha3' => 'sxm',
                'continent_id' => 2,
                'currency_code' => 'ANG',
                'currency_name' => 'Netherlands Antillean Guilder',
                'emoji' => '🇸🇽',
                'full_name' => 'Sint Maarten',
                'has_division' => 0,
                'id' => 239,
                'name' => 'Sint Maarten',
                'tld' => '.sx',
            ),
            239 => 
            array (
                'callingcode' => '1649',
                'capital' => 'Cockburn Town',
                'code' => 'tc',
                'code_alpha3' => 'tca',
                'continent_id' => 2,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇹🇨',
                'full_name' => 'Turks and Caicos Islands',
                'has_division' => 0,
                'id' => 240,
                'name' => 'Turks and Caicos Islands',
                'tld' => '.tc',
            ),
            240 => 
            array (
                'callingcode' => NULL,
                'capital' => 'Port-aux-Français',
                'code' => 'tf',
                'code_alpha3' => 'atf',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇹🇫',
                'full_name' => 'French Southern Territories',
                'has_division' => 0,
                'id' => 241,
                'name' => 'French Southern Territories',
                'tld' => '.tf',
            ),
            241 => 
            array (
                'callingcode' => '690',
                'capital' => 'Fakaofo',
                'code' => 'tk',
                'code_alpha3' => 'tkl',
                'continent_id' => 4,
                'currency_code' => 'NZD',
                'currency_name' => 'New Zealand Dollar',
                'emoji' => '🇹🇰',
                'full_name' => 'Tokelau',
                'has_division' => 0,
                'id' => 242,
                'name' => 'Tokelau',
                'tld' => '.tk',
            ),
            242 => 
            array (
                'callingcode' => NULL,
                'capital' => '',
                'code' => 'um',
                'code_alpha3' => 'umi',
                'continent_id' => 6,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇺🇲',
                'full_name' => 'United States Minor Outlying Islands',
                'has_division' => 0,
                'id' => 243,
                'name' => 'United States Minor Outlying Islands',
                'tld' => '.us',
            ),
            243 => 
            array (
                'callingcode' => '1284',
                'capital' => 'Road Town',
                'code' => 'vg',
                'code_alpha3' => 'vgb',
                'continent_id' => 2,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇻🇬',
                'full_name' => 'The British Virgin Islands',
                'has_division' => 0,
                'id' => 244,
                'name' => 'The British Virgin Islands',
                'tld' => '.vg',
            ),
            244 => 
            array (
                'callingcode' => '1340',
                'capital' => 'Charlotte Amalie',
                'code' => 'vi',
                'code_alpha3' => 'vir',
                'continent_id' => 6,
                'currency_code' => 'USD',
                'currency_name' => 'US Dollar',
                'emoji' => '🇻🇮',
                'full_name' => 'The United States Virgin Islands',
                'has_division' => 0,
                'id' => 245,
                'name' => 'The United States Virgin Islands',
                'tld' => '.vi',
            ),
            245 => 
            array (
                'callingcode' => '681',
                'capital' => 'Mata-Utu',
                'code' => 'wf',
                'code_alpha3' => 'wlf',
                'continent_id' => 2,
                'currency_code' => 'XPF',
            'currency_name' => 'CFP franc (franc Pacifique)',
                'emoji' => '🇼🇫',
                'full_name' => 'Wallis and Futuna Islands',
                'has_division' => 0,
                'id' => 246,
                'name' => 'Wallis and Futuna Islands',
                'tld' => '.wf',
            ),
            246 => 
            array (
                'callingcode' => '383',
                'capital' => 'Pristina',
                'code' => 'xk',
                'code_alpha3' => '',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇽🇰',
                'full_name' => 'The Republic of Kosovo',
                'has_division' => 0,
                'id' => 247,
                'name' => 'Kosovo',
                'tld' => NULL,
            ),
            247 => 
            array (
                'callingcode' => '382',
                'capital' => 'Podgorica',
                'code' => 'me',
                'code_alpha3' => 'mne',
                'continent_id' => 2,
                'currency_code' => 'EUR',
                'currency_name' => 'Euro',
                'emoji' => '🇲🇪',
                'full_name' => 'Montenegro',
                'has_division' => 0,
                'id' => 248,
                'name' => 'Montenegro',
                'tld' => '.me',
            ),
        ));
        
        
    }
}
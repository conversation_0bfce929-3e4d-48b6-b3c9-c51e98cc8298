@startuml Attendance Module Schema

!theme vibrant

entity "attendances" {
  * id: int
  --
  student_id: int <<FK>>
  employee_id: int <<FK>>
  class_id: int <<FK>>
  subject_id: int <<FK>>
  date: date
  status: varchar(255)
  organization_id: int
}

entity "attendance_options" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "student_attendances" {
  * id: int
  --
  student_id: int <<FK>>
  attendance_type: varchar(255)
  date: date
  notes: text
}

entity "students" {
  * id: int
  --
  full_name: varchar(255)
  organization_id: int
}

entity "employees" {
  * id: int
  --
  full_name: varchar(255)
  organization_id: int
}

entity "classes" {
  * id: int
  --
  class_code: varchar(255)
  organization_id: int
}

entity "subjects" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "holidays" {
  * id: int
  --
  title: varchar(255)
  from_date: date
  to_date: date
  organization_id: int
}

entity "public_holidays" {
  * id: int
  --
  title: varchar(255)
  date: date
  organization_id: int
}

attendances }o--|| students : "for student"
attendances }o--|| employees : "by employee"
attendances }o--|| classes : "in class"
attendances }o--|| subjects : "for subject"
student_attendances }o--|| students : "for student"

@enduml

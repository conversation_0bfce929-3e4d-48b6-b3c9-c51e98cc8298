<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\EmailSetting
 *
 * @property int $id
 * @property string|null $email_engine_type
 * @property string|null $from_name
 * @property string|null $from_email
 * @property string|null $mail_driver
 * @property string|null $mail_host
 * @property string|null $mail_port
 * @property string|null $mail_username
 * @property string|null $mail_password
 * @property string|null $mail_encryption
 * @property int|null $organization_id
 * @property int|null $academic_id
 * @property int $active_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting query()
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereAcademicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereActiveStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereEmailEngineType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereFromEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereFromName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereMailDriver($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereMailEncryption($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereMailHost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereMailPassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereMailPort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereMailUsername($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmailSetting whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class EmailSetting extends Model
{
    //
}

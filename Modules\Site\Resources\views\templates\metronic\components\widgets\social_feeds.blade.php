<section id="social_feeds">
    <?php $cols = 0 ;
    if($social_feeds_show_facebook_feeds){
        $cols++;
    }
    if($social_feeds_show_twitter_feeds){
        $cols++;
    }
    if($social_feeds_show_youtube_feeds){
        $cols++;
    }
    if($cols)
        $grid = 12/$cols;
    else 
        $grid = 12;
    ?>
    <div class="row">
    <div class="col-md-12">
        @if($social_feeds_show_facebook_feeds)
        <div class="col-md-{{$grid}}">
            <div class="box-static box-border-top">
                <div class="box-title">
                    <h4>Facebook</h4>
                </div>
                <div class="fb-page" data-href="{{ config('settings.links_facebook')}}" data-tabs="timeline,messages" data-width="1000" data-height="300" data-small-header="true" data-adapt-container-width="true" data-hide-cover="true" data-show-facepile="true"></div>
        
                <div id="fb-root"></div>
                <script>(function(d, s, id) {
                var js, fjs = d.getElementsByTagName(s)[0];
                if (d.getElementById(id)) return;
                js = d.createElement(s); js.id = id;
                js.src = 'https://connect.facebook.net/ms_MY/sdk.js#xfbml=1&version=v2.11';
                fjs.parentNode.insertBefore(js, fjs);
                }(document, 'script', 'facebook-jssdk'));</script>
            </div>
        </div>
        @endif
        @if($social_feeds_show_twitter_feeds)
        <div class="col-md-{{$grid}}">
            <div class="box-static box-border-top">
                <div class="box-title">
                    <h4>Twitter</h4>
                </div>
                <a class="twitter-timeline" data-height="300"  href="{{ config('settings.links_twitter') }}?ref_src=twsrc%5Etfw"></a> <script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>

            </div>
        </div>
        @endif
        @if($social_feeds_show_youtube_feeds)
        <div class="col-md-{{$grid}}">
            <div class="box-static box-border-top">
                <div class="box-title">
                @if ($social_feeds_youtube_video_link)
                    <?php $video;  preg_match("#(?<=v=)[a-zA-Z0-9-]+(?=&)|(?<=v\/)[^&\n]+(?=\?)|(?<=v=)[^&\n]+|(?<=youtu.be/)[^&\n]+#", $social_feeds_youtube_video_link, $video); ?>
                    <iframe width="100%" height="340" src="https://www.youtube.com/embed/{{ $video[0] }}" frameborder="0" gesture="media" allow="encrypted-media" allowfullscreen></iframe>

                @endif

                </div>
            </div>
        </div>
        @endif
    </div>
    </div>

</section>

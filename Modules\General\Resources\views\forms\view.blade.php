@extends('layouts.hound') 
@section('content')


<!-- Row -->
<div class="row">
    <div class="container">
        <div class="panel panel-default card-view">
            <div class="panel-wrapper collapse in">
                <div class="panel-body">
                    <div class="col-sm-8">
                            <div class="form-wrap">
                                    <h6 class="txt-dark capitalize-font"><i class="zmdi zmdi-file mr-10"></i>{{ $form->type->title}}</h6>
                                    <hr class="light-grey-hr" />
                                    <h6 class="txt-dark capitalize-font mb-10">
                                        <i class="zmdi zmdi-account mr-10"></i>
                                        Applicant Name :
                                    </h6>
                                    <div class="form-control pb-10 mb-10">
                                            {{ $form->applicant->full_name ?? $form->applicant->display_name}}
                                    </div>
    
                                    <h6 class="txt-dark capitalize-font mb-10">
                                        <i class="zmdi zmdi-comment-text mr-10"></i>
                                        Request Details
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <div class="form-control pa-20" >
                                                    {{ $form->content }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--/row-->
                                    @if($form->type->date_or_range == 'date')
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class="control-label mb-10">{{ $form->type->date_label }}</label>
                                                <div class="form-control"> 
                                                    @if($form->type->request_time)
                                                    {{ $form->from_date->format('d/M/Y H:iA') }}
                                                    @else 
                                                    {{ $form->from_date->format('d/M/Y') }}
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @elseif($form->type->date_or_range == 'range')
                                    <div class="row">
                                        <div class="col-md-12">
                                            <label class="control-label mb-10">{{ $form->type->date_label }}</label>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="control-label mb-10">From</label>
                                                <div class="form-control"> 
                                                    @if($form->type->request_time)
                                                    {{ $form->from_date->format('d/M/Y H:iA') }}
                                                    @else 
                                                    {{ $form->from_date->format('d/M/Y') }}
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="control-label mb-10">To</label>
                                                <div class="form-control"> 
                                                        @if($form->type->request_time)
                                                        {{ $form->to_date->format('d/M/Y H:iA') }}
                                                        @else 
                                                        {{ $form->to_date->format('d/M/Y') }}
                                                        @endif
                                                    </div>

                                            </div>
                                        </div>
                                    </div>
                                    @endif
                            </div>
        
                    </div>
                    {{-- <div class="col-sm-4">
                            <div class="panel panel-default border-panel card-view">
									<div class="panel-heading">
										<div class="pull-left">
                                        <h6 class="txt-dark capitalize-font"><i class="zmdi zmdi-info-outline mr-10"></i>Clarfications</h6>
										</div>
										<div class="clearfix"></div>
									</div>
									<div class="panel-wrapper">
										<div class="panel-body row pa-0">
											<div class="chat-cmplt-wrap chat-for-widgets chat-box-slide">
												<div class="recent-chat-box-wrap">
													<div class="recent-chat-wrap">
														<div class="panel-wrapper">
															<div class="panel-body pa-0">
																<div class="chat-content">
                                                                    <div class="" >
                                                                        <ul class="users-chat-nicescroll-bar pt-20">
																		<li class="friend">
																			<div class="friend-msg-wrap">
																				<div class="msg pull-left">
																					<p>Hello Jason, how are you, it's been a long time since we last met?</p>
																					<div class="msg-per-detail text-right">
																						<span class="msg-time txt-grey">2:30 PM</span>
																					</div>
																				</div>
																				<div class="clearfix"></div>
																			</div>	
																		</li>
																		<li class="self mb-10">
																			<div class="self-msg-wrap">
																				<div class="msg block pull-right"> Oh, hi Sarah I'm have got a new job now and is going great.
																					<div class="msg-per-detail text-right">
																						<span class="msg-time txt-grey">2:31 pm</span>
																					</div>
																				</div>
																				<div class="clearfix"></div>
																			</div>	
																		</li>
																		<li class="self">
																			<div class="self-msg-wrap">
																				<div class="msg block pull-right">  How about you?
																					<div class="msg-per-detail text-right">
																						<span class="msg-time txt-grey">2:31 pm</span>
																					</div>
																				</div>
																				<div class="clearfix"></div>
																			</div>	
																		</li>
																		<li class="friend">
																			<div class="friend-msg-wrap">
																				<img class="user-img img-circle block pull-left" src="dist/img/user.png" alt="user">
																				<div class="msg pull-left"> 
																					<p>Not too bad.</p>
																					<div class="msg-per-detail  text-right">
																						<span class="msg-time txt-grey">2:35 pm</span>
																					</div>
																				</div>
																				<div class="clearfix"></div>
																			</div>	
																		</li>
                                                                    </ul>
                                                                </div>
																</div>
																<div class="input-group">
																	<input type="text" id="input_msg_send_widget" name="send-msg" class="input-msg-send form-control" placeholder="Type something">
																	
																	{{-- <div class="input-group-btn attachment">
																		<div class="fileupload btn  btn-default"><i class="zmdi zmdi-attachment-alt"></i>
																			<input type="file" class="upload">
																		</div>
																	</div> -- }}
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
                    </div> --}}
                </div>

                <div class="clearfix">
                    <h6 class="txt-dark capitalize-font"><i class="zmdi zmdi-filter-tilt-shift mr-10"></i>Approval Process</h6>

                    <ul class="list-group">
                        <?php $need_action =true ?>
                        @foreach($form->type->approvalFlow as $step)
                        <li class="list-group-item clearfix">
                            <?php $reviewed = $form->reviews->where('step_order' , $step->step_order )->first(); ?>
                            @if($reviewed)
                            <div class="col-sm-5">
                            <span class="badge pull-left badge-info">{{ $step->step_order }}</span>
                             Reviewed By {{ $reviewed->reviewedBy->full_name }}
                            </div>
                            @if($reviewed->note) <strong>Note:</strong> {{$reviewed->note}} @endif
                            <span class="badge badge-info">{{ $reviewed->action }}</span> 
                            @else
                            <div class="col-sm-4  pt-10 pb-10">
                                <span class="badge pull-left">{{ $step->step_order }}</span>
                                {{ $step->reviewer->description }} Review 
                            </div>
                            @if(auth()->user()->hasRole($step->role) && $need_action)
                            {!! Form::open(['method' => 'POST' ,
                                'route' => ['general.form.review', $form->id],
                             ]) !!}
                             <div class="col-sm-3">
                                {!! Form::text('note' , null , ['class' => 'form-control', 'placeholder' => 'Notes' ]) !!}
                                <input type="hidden" name="step" value="{{$step->id}}">
                             </div>
                             <div class="col-sm-5">
                                 @if($step->can_approve)
                                 <button type="submit" name="action" value="approved" class="btn btn-success">Approve</button>
                                 @endif
                                 @if($step->can_reject)
                                 <button type="submit" name="action" value="rejected" class="btn btn-danger">Reject</button>
                                 @endif
                                 @if($step->can_request_clearfication)
                                 <button type="submit" name="action" value="require_clarification" class="btn btn-danger">Clarify</button>
                                 @endif
                             </div>
                             <?php $need_action = false ?>
                             {!! Form::close() !!}
                            @endif
                            @endif
                        </li>
                        @if($form->reviews->where('step_order' , $step->step_order)->where('action' , 'rejected')->first()) @break @endif
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /Row -->

@endsection

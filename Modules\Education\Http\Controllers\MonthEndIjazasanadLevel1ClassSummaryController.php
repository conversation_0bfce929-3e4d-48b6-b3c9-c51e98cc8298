<?php

namespace Modules\Education\Http\Controllers;


use App\DataTables\UsersDataTable;
use App\IjazasanadMemorizationPlan;
use App\StudentAttendance;
use App\Student;

use App\StudentIjazasanadMemorizationLastApprovedPlan;
use App\StudentIjazasanadMemorizationReport;
use Session;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\LessonReport;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class MonthEndIjazasanadLevel1ClassSummaryController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function getRecords(Request $request)
    {

        DB::connection()->enableQueryLog();

        try {

            $year = Carbon::parse($request->get('classDate'))->year;
            $month = Carbon::parse($request->get('classDate'))->month;
            $classId = $request->get('classId');

            // Fetch total number of students in the class
            $classes = Classes::where('id', $classId)->with('students')->first();
            $totalStudents = $classes->students->count();

            // Fetch talqeen, revision, jazariyah, and seminars planned and actual records using the plans relationship
            $memorizationPlans = IjazasanadMemorizationPlan::where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            $memorizationReports = StudentIjazasanadMemorizationReport::where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();



            $totalTalqeenCompleted = 0;
            $totalRevisionCompleted = 0;
            $totalJazariyahCompleted = 0;
            $totalSeminarsCompleted = 0;
// Filter out valid reports and group them by student_id
            $reportsByStudent = $memorizationReports->filter(function($report) {
                return !is_null($report->talqeen_from_lesson)
                    && !is_null($report->talqeen_to_lesson)
                    && ($report->talqeen_from_lesson <= $report->talqeen_to_lesson);
            })->groupBy('student_id');

// For each student, merge overlapping ranges by taking the minimum and maximum of their lesson ranges
            foreach ($reportsByStudent as $studentReports) {
                $minLesson = $studentReports->min('talqeen_from_lesson');
                $maxLesson = $studentReports->max('talqeen_to_lesson');
                $totalTalqeenCompleted += ($maxLesson - $minLesson + 1);



            }

            $reportsByStudent = $memorizationReports->filter(function($report) {
                return !is_null($report->revision_from_lesson)
                    && !is_null($report->revision_to_lesson)
                    && ($report->revision_from_lesson <= $report->revision_to_lesson);
            })->groupBy('student_id');

// For each student, merge overlapping ranges by taking the minimum and maximum of their lesson ranges
            foreach ($reportsByStudent as $studentReports) {


                $minRevisionLesson = $studentReports->min('revision_from_lesson');
                $maxRevisionLesson = $studentReports->max('revision_to_lesson');
                $totalRevisionCompleted += ($maxRevisionLesson - $minRevisionLesson + 1);

            }


            $reportsByStudent = $memorizationReports->filter(function($report) {
                return !is_null($report->jazariyah_from_lesson)
                    && !is_null($report->jazariyah_to_lesson)
                    && ($report->jazariyah_from_lesson <= $report->jazariyah_to_lesson);
            })->groupBy('student_id');

// For each student, merge overlapping ranges by taking the minimum and maximum of their lesson ranges
            foreach ($reportsByStudent as $studentReports) {


                $minJazariyahLesson = $studentReports->min('jazariyah_from_lesson');
                $maxJazariyahLesson = $studentReports->max('jazariyah_to_lesson');
                $totalJazariyahCompleted += ($maxJazariyahLesson - $minJazariyahLesson + 1);

            }

            $reportsByStudent = $memorizationReports->filter(function($report) {
                return !is_null($report->seminars_from_lesson)
                    && !is_null($report->seminars_to_lesson)
                    && ($report->seminars_from_lesson <= $report->seminars_to_lesson);
            })->groupBy('student_id');

// For each student, merge overlapping ranges by taking the minimum and maximum of their lesson ranges
            foreach ($reportsByStudent as $studentReports) {


                $minSeminarsLesson = $studentReports->min('seminars_from_lesson');
                $maxSeminarsLesson = $studentReports->max('seminars_to_lesson');
                $totalSeminarsCompleted += ($maxSeminarsLesson - $minSeminarsLesson + 1);

            }
            // Group the memorization plans by student_id
            $plansByStudent = $memorizationPlans->groupBy('student_id');

// Initialize totals for each category
            $totalTalqeenPlanned = 0;
            $totalRevisionPlanned = 0;
            $totalJazariyahPlanned = 0;
            $totalSeminarPlanned = 0;


            $plansByStudent->each(function($studentPlans) use (&$totalTalqeenPlanned,
                &$totalRevisionPlanned,
                &$totalJazariyahPlanned,
                &$totalSeminarPlanned) {
                // For Talqeen: merge the intervals per student
                $totalTalqeenPlanned += $this->mergeLessonRanges($studentPlans, 'talqeen_from_lesson', 'talqeen_to_lesson');

                // For Revision
                $totalRevisionPlanned += $this->mergeLessonRanges($studentPlans, 'revision_from_lesson', 'revision_to_lesson');

                // For Jazariyah
                $totalJazariyahPlanned += $this->mergeLessonRanges($studentPlans, 'jazariyah_from_lesson', 'jazariyah_to_lesson');

                // For Seminars
                $totalSeminarPlanned += $this->mergeLessonRanges($studentPlans, 'seminars_from_lesson', 'seminars_to_lesson');
            });

// Overall total planned lessons across all categories
            $totalPlanned = $totalTalqeenPlanned + $totalRevisionPlanned + $totalJazariyahPlanned + $totalSeminarPlanned;




            // Calculate attendance percentage
            $totalClasses = $this->calculateTotalClassesInMonth($classId, $year, $month);
            $attendancePercentage = calculateIjazasanadLevel1AttendancePercentage($classId, $year, $month,$totalClasses);



            $totalCompleted = $totalTalqeenCompleted + $totalRevisionCompleted + $totalJazariyahCompleted + $totalSeminarsCompleted;
            $achievementPercentage = $totalPlanned > 0 ? round(($totalCompleted / $totalPlanned) * 100, 2) : 0;


            $summaryData = [
                'noOfStudents' => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalStudents . '</h2>',
                'totalTalqeenPlanned' => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalTalqeenCompleted . '</h2>',
                'totalRevisionPlanned' => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalRevisionCompleted . '</h2>',
                'totalJazariyahPlanned' => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalJazariyahCompleted . '</h2>',
                'totalSeminarsPlanned' => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalSeminarsCompleted . '</h2>',
                'attendanceDaysPercentage' => '<div class="progress" style="position: relative;">
                <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' . $attendancePercentage . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $attendancePercentage . '%;background-color: #1fff0f;">
                    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $attendancePercentage . '%</span>
                </div>
            </div>',
                'achievementPercentage' => '<div class="progress" style="position: relative;">
                <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' . $achievementPercentage . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $achievementPercentage . '%;background-color: #1fff0f;">
                    <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $achievementPercentage . '%</span>
                </div>
            </div>'
            ];



            return DataTables::of(collect([$summaryData]))
                ->addIndexColumn()
                ->rawColumns(['noOfStudents', 'totalTalqeenPlanned', 'totalRevisionPlanned', 'totalJazariyahPlanned', 'totalSeminarsPlanned', 'attendanceDaysPercentage', 'achievementPercentage'])

                ->make(true);



        }catch (\Exception $exception){


            dd($exception->getMessage());
        }
    }


    function mergeLessonRanges($plans, $fromKey, $toKey)
    {
        // Collect valid ranges
        $intervals = [];
        foreach ($plans as $plan) {
            $start = $plan->$fromKey;
            $end   = $plan->$toKey;
            if (!is_null($start) && !is_null($end) && $start <= $end) {
                $intervals[] = [$start, $end];
            }
        }

        // Sort intervals by starting lesson
        usort($intervals, function ($a, $b) {
            return $a[0] <=> $b[0];
        });

        // Merge intervals only if they actually overlap
        $merged = [];
        foreach ($intervals as $interval) {
            if (empty($merged) || $merged[count($merged) - 1][1] < $interval[0]) {
                // No overlap; push as a separate interval
                $merged[] = $interval;
            } else {
                // Overlapping intervals; merge by extending the end if necessary
                $merged[count($merged) - 1][1] = max($merged[count($merged) - 1][1], $interval[1]);
            }
        }

        // Sum the lengths of all merged intervals
        $total = 0;
        foreach ($merged as $m) {
            $total += ($m[1] - $m[0] + 1);
        }

        return $total;
    }



    /**
     * Calculate total number of classes in the specified month
     *
     * @param int $classId
     * @param int $year
     * @param int $month
     * @return int
     */
    private function calculateTotalClassesInMonth($classId, $year, $month)
    {
        $classTimetable = Classes::find($classId)->timetable;
        if (!$classTimetable) {
            return 0;
        }

        $totalClasses = 0;
        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);

        for ($day = 1; $day <= $daysInMonth; $day++) {
            $dayOfWeek = strtolower(date('D', strtotime("$year-$month-$day")));
            if (!is_null($classTimetable->$dayOfWeek)) {
                $totalClasses++;
            }
        }

        return $totalClasses;
    }

}

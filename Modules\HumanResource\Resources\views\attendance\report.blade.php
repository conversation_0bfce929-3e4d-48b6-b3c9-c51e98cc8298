@extends('layouts.hound')

@section('mytitle', 'Attendance Report')

@section('content')

    <div class="row">
        <div class="col-md-12">
            <h3 class="modal-title">Attendence Report [{{ $current_month->copy()->addDays(10)->format('M/Y') }}]  {{ $employee->full_name }}
            <button type="button" class="btn btn-primary btn-sm hidden-print" onclick="window.print()"><i class="fa fa-print"></i></button>
                
                <select class="pull-right hidden-print" name="" id="" onchange="window.location = '{{ url('workplace/humanresource/attendance/'.$employee->id.'/')}}/'+this.value">
                    <option value="{{ $current_month->copy()->format('Y-m') }}">{{ $current_month->copy()->format('M/Y') }}</option>
                    @for($d = $first_working_month->copy(); $d <= Date('Y-m-d') ; $d->addMonth() )
                    <option value="{{ $d->format('Y-m') }}">{{ $d->format('M/Y') }}</option>
                    @endfor
                </select>
            </h3>
            <button class="btn btn-sm btn-danger pull-right toggleNote hidden-print">Show/Hide All Notes</button>
        </div>
    </div>

    <div class="result-set">

    <table class="table table-responsive table-bordered">

        @foreach($daily_record as $date => $record)
        <tr @if(isset($record['required_hours']) && $record['required_hours'] == 'off') class="alert-success" @endif>
            <td style="text-align: center; text-transform: capitalize">
                {{ $record['day']}}
                <br>
                {{ $date }}
            </td>
            <td>
                @foreach($record['attendance'] as $attendance)
                <div class="row p-5">
                        <div class="col-xs-8">
                            @isset($attendance['in'])
                            <div class="col-xs-6 attendance-record" >
                                <button type="button" class="btn btn-default btn-outline btn-xs col-md-12" title="" data-toggle="tooltip" data-placement="top"  data-original-title="{{ $attendance['in']->note }}">
                                    <small class="label label-success">in</small>{{ $attendance['in']->clock->format('h:iA') }}@if($attendance['in']->note ) * @endif
                                    <div class="clock-note">
                                            {{ $attendance['in']->note }}
                                    </div>
                                </button>
                            </div>
                            @endisset
    
                            @isset($attendance['out'])
                            <div class="col-xs-6 attendance-record" data-note="">
                                <button type="button" class="btn btn-default btn-outline btn-xs col-md-12"  data-toggle="tooltip"  title=""  data-placement="top" data-original-title="{{ $attendance['out']->note }}">
                                    <small class="label label-warning">out</small> {{ $attendance['out']->clock->format('h:iA') }} @if($attendance['out']->note ) * @endif
                                    <div class="clock-note">
                                            {{ $attendance['out']->note }}
                                    </div>
    
                                </button>
                            </div>
                            @endisset
                        </div>
                        <div class="col-xs-4">
                            @isset($attendance['duration_in_hours'])
                            <small class="label label-success">duration</small> {{ $attendance['duration_in_hours'] }}
                            @endisset
                        </div>
                    </div>

                @endforeach

                
                
                
            </td>
            <td>
                @if(isset($record['required_hours']) && $record['required_hours'] == 'off')
                    Day off
                @else
                Required :{{ $record['required_hours'] ??  '' }}<br>
                Done: {{ $record['worked_hours'] }} <br>
                @can('update attendance')
                <button type="button" class="btn btn-danger btn-xs" onclick="editAttendance({{ json_encode($record['attendance']) }} , '{{ $date }}')" >Edit Records</button>
                @endcan
                @endif
            </td>
        </tr>
        @endforeach
    </table>
    </div>
    <div class="clearfix">
        <h3>Working Hours Report</h3>
        <table class="table">

            <tr>
                <td>Total Working hours</td>
                <td>{{ $total_working_hours }} Hours</td>
            </tr>
            <tr>
                <td>Total Required Working hours</td>
                <td>{{ $total_required_hours }} Hours</td>
            </tr>

        </table>
    </div>
    @if($current_month->format('Ym') < date('Ym'))
    <h3>Salary Report of {{$current_month->format('M/Y')}}</h3>
    @php
        $salary_report = $employee->salaryReportOn($current_month->format('Y-m-01 00:00:00'));
    @endphp
    @if($salary_report)
    <table class="table table-striped ">
        <tr>
            <td>Basic Salary</td>
            <td>{{ $basic_salary ?? 'Not Calc'}}</td>
    
            <td>Deducted Hours</td>
            <td>{{ $salary_report->deducted_hours}}</td>
            <td class="btn-success">Total Amount</td>
            <td class="btn-success">
                <strong>
                    {{ $salary_report->amount }}
                </strong>
            </td>

        </tr>
        <tr>
            <td>Annual Leaves Taken</td>
            <td>{{ $salary_report->annual_leaves_taken}}</td>
            <td>Medical Leaves Taken</td>
            <td>{{ $salary_report->medical_leaves_taken}}</td>
            <td>Other Leaves Taken</td>
            <td>{{ $salary_report->other_leaves_taken}}</td>
        </tr>
    </table>
    @else
    <div class="alert alert-warning text-center">
        No report yet <a href="{{ route('salary.report.create' , [$employee->id , $current_month->format('Y-m')] ) }}" class="btn btn-danger">Create Report</a>
    </div>
    @endif

    {{-- 
        {"id":1,"employee_id":2,"report_month":"2018-11-01 00:00:00",
        "amount":1204.5,"deducted_hours":12,"annual_leaves_taken":0,"medical_leaves_taken":0,"other_leaves_taken":0,"created_by":2,"created_at":"2018-12-23 14:12:33","updated_at":"2018-12-23 14:12:33"}

        --}}
    @endif

    
    
    <div class="modal fade vueApp" id="edit_attendance" data-backdrop="static" data-keyboard="false" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title">Edit Attendance Record
                        <button type="button" class="btn btn-warning btn-sm pull-right" @click="addRecord()">Add Record</button>

                    </h4>
                </div>
                <div class="modal-body">
                    <div v-for="(group, index) in arranged_records" :key="index" class="clearfix ">
                        <!-- <div class="row form-control" v-for="(item, k) in group" :key="k" v-if="item.type"> -->
                        <div class="row form-control" v-for="(type, k) in ['in' , 'out']" :key="k">
                            <div class="col-xs-4">
                                <select name="" id="input" class="form-control" required="required" v-model="arranged_records[index][type].type">
                                    <option value="in">Clock-in</option>
                                    <option value="out">Clock-Out</option>
                                </select>
                            </div>
                            <div class="col-xs-5">
                                <input type="text" class="form-control datetime tmpdatetime"  required="required"  v-model="arranged_records[index][type].clock">
                            </div>
                            <div class="col-xs-3">
                                
                            </div>
                        </div>
                        <br>
                    </div>                    
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" @click="updateRecords()">Save changes</button>
                </div>
            </div>
        </div>
    </div>
    
@endsection
@include('jssnippets.flatpickr')
    
@section('js')

<script src="https://cdn.jsdelivr.net/npm/vue"></script>
<script src="https://unpkg.com/axios/dist/axios.min.js"></script>

<script>


    var app = new Vue({
        el: '#edit_attendance',
        data: function () {
            return {
                records: {},
                date : null,
                arranged_records : []
            }
        },
        computed : {
            arranged_records2 : function () {
                var elements = [];
                for (const key in this.records) {
                    if (this.records.hasOwnProperty(key)) {
                        const element = this.records[key];
                        if(!element.in){
                            this.records[key].in = {
                                'clock' : this.date+' 00:00:00',
                                'type' : 'in'
                            }
                        }
                        if(!element.out){
                            this.records[key].out = {
                                'clock' : this.date+' 00:00:00',
                                'type' : 'out'
                            }
                        }
                        elements.push(this.records[key]);
                    }
                }
                this.updateFlatpikr();
                return elements;
            }
        },
        watch:{
            records: function (value) {  
                this.arranged_records = [];
                for (const key in this.records) {
                    if (this.records.hasOwnProperty(key)) {
                        const element = this.records[key];
                        if(!element.in){
                            this.records[key].in = {
                                'clock' : this.date+' 00:00:00',
                                'type' : 'in'
                            }
                        }
                        if(!element.out){
                            this.records[key].out = {
                                'clock' : this.date+' 00:00:00',
                                'type' : 'out'
                            }
                        }
                        this.arranged_records.push(this.records[key]);
                    }
                }
                this.updateFlatpikr();
            }
        },
        created(){

        },
        methods:{
            addRecord(){
                this.arranged_records.push({in : {'clock' : this.date+' 00:00:00','type' : 'in'}, out : {'clock' : this.date+' 00:00:00', 'type' : 'out'}});
                this.updateFlatpikr();
            },
            updateRecords(){
                
                var data = {
                    records : this.arranged_records,
                    date: this.date,
                    employee_id : '{{$employee->id}}',
                    _token: '{{csrf_token() }}'
                }
                axios.put('{{ route('attendance.update' , $employee->id) }}',data).then(function(res) {
                    if(!res.data.error){
                        window.location.reload(true);
                    }                    
                }).catch(function(e){
                    alert('error');
                    console.log(e);
                    
                })
            },
            updateFlatpikr(){
                setTimeout(function() {
                    flatpickr('.tmpdatetime', {
                        enableTime: true,
                        noCalendar: true,
                        dateFormat: "Y-m-d H:i:S",
                    });
                    $('.tmpdatetime').removeClass('tmpdatetime');
                }, 500);        
            }
        }
    })

</script>

<script>
$('.attendance-record').each(function(i ,el){
	var note = $(this).attr('data-note');
	if(note){
	 $(this).append('<small class="record-note label label-danger float-right">!</small>')
    }
})
$('.toggleNote').click(function () {  
    $('.clock-note').toggle();
})

var editAttendance = function (records , date) {
    console.log(records);
    app.$data.records = records;
    app.$data.date = date;

    $('#edit_attendance').modal('show');
    // setTimeout(function() {
    //     flatpickr('.datetime', {
    //         enableTime: true,
    //         dateFormat: "Y-m-d H:i:S",
    //     });
    // }, 500);
    
  }


//   $("#edit_attendance").on('hide.bs.modal', function () {  
//     return false
//   });
</script>
@append
@section('css')
<style>
    .clock-note{
        display: none;
        width: 100%;
        background: #f3f3f3;
        border: #dff0d8 1px solid;
    }
</style>
@append


<?php
declare(strict_types=1);

namespace Modules\JobSeeker\Contracts;

/**
 * Unifies provider sync + aggregation entrypoint across all job providers.
 */
interface JobProviderSyncInterface
{
    /**
     * Execute a provider sync and trigger aggregation-based notifications.
     *
     * @param array<int,string|int>|null $providerCategoryIdentifiers Optional provider-specific category identifiers
     * @param int|null $scheduleRuleId Optional schedule rule driving the sync
     * @return array Sync statistics
     */
    public function syncAndAggregate(?array $providerCategoryIdentifiers = null, ?int $scheduleRuleId = null): array;
}



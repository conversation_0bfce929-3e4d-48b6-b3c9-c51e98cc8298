<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Employee;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class SupervisorMindmapController extends Controller
{

    public function show($employee_id)
    {
        // Fetch the mind map data for the given employee
        $mindMapData = $this->buildMindMapData($employee_id);
        return view('humanresource::employees.mindmap.supervisor_center_mindmap', compact('mindMapData'));
    }

    private function buildMindMapData($employee_id)
    {
        // Fetch the employee with related data (centers, classes, students)
        $employee = Employee::with(['center.classes.students'])->findOrFail($employee_id);

        // Initialize the nodes array with the supervisor as the root
        $nodes = [
            [
                'id' => 'supervisor_root',
                'isroot' => true,
                'topic' => $employee->full_name
            ]
        ];

        // Fetch centers supervised by this employee
        $centers = $employee->center()->with('classes.students')->get();

        // Build the hierarchical node structure
        foreach ($centers as $center) {
            $centerId = 'center_' . $center->id;
            $nodes[] = [
                'id' => $centerId,
                'parentid' => 'supervisor_root',
                'topic' => $center->name
            ];

            foreach ($center->classes as $class) {
                $classId = 'class_' . $class->id;
                $nodes[] = [
                    'id' => $classId,
                    'parentid' => $centerId,
                    'topic' => $class->name
                ];

                foreach ($class->students as $student) {
                    // Ensure unique student IDs by including class ID
                    $studentId = 'student_' . $class->id . '_' . $student->id;
                    $nodes[] = [
                        'id' => $studentId,
                        'parentid' => $classId,
                        'topic' => $student->full_name ?? 'Unknown Student'
                    ];
                }
            }
        }

        // Return the mind map data in the required format
        return [
            'meta' => [
                'name' => 'supervisor_hierarchy',
                'author' => 'Your Name',
                'version' => '1.0'
            ],
            'format' => 'node_array',
            'data' => $nodes
        ];
    }


    /**
     * Fetch the mind map data for a Supervisor, showing their Centers, Classes, and Students.
     *
     * @param int $employee_id The ID of the Supervisor (Employee)
     * @return \Illuminate\Http\JsonResponse JSON data in jsMind node_array format
     */
    public function getSupervisorMindMap($employee_id)
    {
        // Step 1: Fetch the Supervisor with their Centers, Classes, and Students
        $supervisor = Employee::with(['center.classes.students'])
            ->findOrFail($employee_id);

        // Step 2: Initialize the nodes array with the Supervisor as the root
        $nodes = [
            [
                'id' => 'supervisor_root',
                'isroot' => true,
                'topic' => $supervisor->full_name
            ]
        ];

        // Step 3: Fetch Centers supervised by this Employee
        $centers = $supervisor->center()->with('classes.students')->get();

        // Step 4: Build the hierarchical node structure
        foreach ($centers as $center) {
            $centerId = 'center_' . $center->id;
            $nodes[] = [
                'id' => $centerId,
                'parentid' => 'supervisor_root',
                'topic' => $center->name
            ];

            foreach ($center->classes as $class) {
                $classId = 'class_' . $class->id;
                $nodes[] = [
                    'id' => $classId,
                    'parentid' => $centerId,
                    'topic' => $class->name
                ];

                foreach ($class->students as $student) {
                    // Make student ID unique by including class ID
                    $studentId = 'student_' . $class->id . '_' . $student->id;
                    $nodes[] = [
                        'id' => $studentId,
                        'parentid' => $classId,
                        'topic' => $student->full_name ?? 'Unknown Student' // Handle null topics
                    ];
                }
            }
        }

        // Step 5: Structure the response for jsMind
        $mindMapData = [
            'meta' => [
                'name' => 'supervisor_hierarchy',
                'author' => 'Your Name',
                'version' => '1.0'
            ],
            'format' => 'node_array',
            'data' => $nodes
        ];

        // Step 6: Return the JSON response
        return response()->json($mindMapData);
    }
}
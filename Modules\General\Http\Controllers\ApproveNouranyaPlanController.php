<?php

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\AdmissionInterview;
use App\Http\Controllers\Controller;
use App\StudentHefzPlan;
use App\StudentLastApprovedPlan;
use App\StudentLastApprovedNouranyaPlan;
use App\StudentNouranyaPlan;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;
use App\Student;

class ApproveNouranyaPlanController extends Controller
{

    public function __invoke(Request $request)
    {
        try {
            DB::beginTransaction();

            // Ensure 'id' is an array
            $ids = $request->id;
            if (!is_array($ids)) {
                throw new \Exception('Invalid data format.');
            }

            $successfulApprovals = [];
            $failedApprovals = [];

            foreach ($ids as $id) {
                try {
                    $plan = StudentNouranyaPlan::with(['student', 'center'])->find($id);

                    if (!$plan) {
                        $failedApprovals[] = [
                            'student_id' => null,
                            'student_name' => 'Unknown',
                            'plan_id' => $id,
                            'error' => 'Plan not found'
                        ];
                        continue;
                    }

                    // Validate plan data
                    if (!$this->validatePlanData($plan)) {
                        $failedApprovals[] = [
                            'student_id' => $plan->student_id,
                            'student_name' => $plan->student->full_name ?? 'Unknown',
                            'plan_id' => $id,
                            'error' => 'Missing required lesson information'
                        ];
                        continue;
                    }

                    $plan->status = 'active';
                    $plan->approved_by = auth()->user()->id;
                    $plan->updated_at = Carbon::now();
                    $plan->save();

                    $year = (string)$plan->start_date->year;
                    $month = (string)$plan->start_date->month;
                    $level_id = $plan->level_id;

                    // Update or create last approved plan for the same student, year and month
                    \App\StudentLastApprovedNouranyaPlan::updateOrCreate(
                        [
                            'student_id' => $plan->student_id,
                            'plan_year_month_day' => $plan->start_date->format('Y-m-d')
                        ],
                        [
                            'approved_by' => $plan->approved_by,
                            'from_lesson' => $plan->from_lesson ?? null,
                            'to_lesson' => $plan->to_lesson ?? null,
                            'talaqqi_from_lesson' => $plan->talaqqi_from_lesson ?? null,
                            'talqeen_from_lesson' => $plan->talqeen_from_lesson ?? null,
                            'talaqqi_to_lesson' => $plan->talaqqi_to_lesson ?? null,
                            'talqeen_to_lesson' => $plan->talqeen_to_lesson ?? null,
                            'from_lesson_line_number' => $plan->from_lesson_line_number ?? null,
                            'to_lesson_line_number' => $plan->to_lesson_line_number ?? null,
                            'level_id' => $level_id,
                            'updated_at' => Carbon::now(),
                            'plan_year_month_day' => $plan->start_date->format('Y-m-d')
                        ]
                    );

                    $admissionId = Admission::where('student_id', $plan->student_id)->first()->id;
                    AdmissionInterview::where('admission_id', $admissionId)->update([
                        'status' => 'interviewed',
                        'confirmed_at' => Carbon::now(),
                        'updated_by' => auth()->user()->id
                    ]);

                    $successfulApprovals[] = [
                        'student_id' => $plan->student_id,
                        'student_name' => $plan->student->full_name,
                        'plan_id' => $id
                    ];

                } catch (\Exception $e) {
                    $failedApprovals[] = [
                        'student_id' => $plan->student_id ?? null,
                        'student_name' => $plan->student->full_name ?? 'Unknown',
                        'plan_id' => $id,
                        'error' => $e->getMessage()
                    ];
                }
            }

            DB::commit();

            // Calculate counts for all plan types waiting for approval
            $hefzPlansWaiting_approval = \App\StudentHefzPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();
            $RevisionPlanWaiting_approval = \App\StudentRevisionPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();

            $NouranyaPlanWaitingApproval = \App\StudentNouranyaPlan::has('student')
            ->has('center')
            ->where('status', 'waiting_for_approval')
            ->where(function ($query) {
                $query->where(function ($level1Query) {
                    $level1Query->whereNotNull('from_lesson')
                        ->whereNotNull('to_lesson');
                })
                    ->orWhere(function ($level2Query) {
                        $level2Query->whereNotNull('from_lesson')
                            ->whereNotNull('to_lesson')
                            ->whereNotNull('from_lesson_line_number')
                            ->whereNotNull('to_lesson_line_number');
                    })
                    ->orWhere(function ($level3Query) {
                        $level3Query->whereNotNull('talaqqi_from_lesson')
                            ->whereNotNull('talaqqi_to_lesson')
                            ->whereNotNull('talqeen_from_lesson')
                            ->whereNotNull('talqeen_to_lesson');
                    });
            })
                ->count();

            $ijazasanadMemorizationPlanWaitingApproval = \App\IjazasanadMemorizationPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();

            $plans_waiting_approval = $hefzPlansWaiting_approval + $RevisionPlanWaiting_approval+$NouranyaPlanWaitingApproval+$ijazasanadMemorizationPlanWaitingApproval;

            // Prepare response message
            $totalProcessed = count($successfulApprovals) + count($failedApprovals);
            $responseMessage = '';
            
            if (count($successfulApprovals) > 0) {
                $responseMessage = count($successfulApprovals) . ' out of ' . $totalProcessed . ' Nouranya plans approved successfully!';
            }
            
            if (count($failedApprovals) > 0) {
                $errorDetails = collect($failedApprovals)->map(function ($failure) {
                    return "Student: {$failure['student_name']} (ID: {$failure['student_id']}) - Error: {$failure['error']}";
                })->implode('; ');
                
                if (count($successfulApprovals) > 0) {
                    $responseMessage .= " Failed to approve: " . $errorDetails;
                } else {
                    $responseMessage = "Failed to approve plans: " . $errorDetails;
                }
            }

            return response()->json([
                'success' => count($successfulApprovals) > 0,
                'message' => $responseMessage,
                'plansWaitingApprovalCountWidget' => $plans_waiting_approval,
                'successful_approvals' => count($successfulApprovals),
                'failed_approvals' => count($failedApprovals),
                'successful_students' => $successfulApprovals,
                'failed_students' => $failedApprovals
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }

    /**
     * Validate plan data before approval
     */
    private function validatePlanData($plan): bool
    {
        // Check if the plan has the required lesson information based on level
        $hasBasicLessons = !empty($plan->from_lesson) && !empty($plan->to_lesson);
        $hasTalaqqiLessons = !empty($plan->talaqqi_from_lesson) && !empty($plan->talaqqi_to_lesson);
        $hasTalqeenLessons = !empty($plan->talqeen_from_lesson) && !empty($plan->talqeen_to_lesson);

        return $hasBasicLessons || $hasTalaqqiLessons || $hasTalqeenLessons;
    }

}

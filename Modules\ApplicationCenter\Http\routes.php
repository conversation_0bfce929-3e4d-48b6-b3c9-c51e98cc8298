<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/


Route::group(['middleware' => ['web', 'auth:web','XSS'],'prefix' => 'applicationcenter', 'namespace' => 'Modules\ApplicationCenter\Http\Controllers'], function () {

//Route::prefix('studentapplication')->group(function () {
    Route::get('/', 'StudentApplicationController@index');
    Route::get('/about', 'StudentApplicationController@about');
    Route::get('/registration', 'StudentApplicationController@registration')->name("student.application.form");
    Route::get('/registration-by-guardian', 'StudentApplicationController@registrationByGuardian')->name("student.guardian.application.form");

    Route::get('/get-class-academicyear', 'StudentApplicationController@getClasAcademicyear');
    Route::get('/get-classes', 'StudentApplicationController@getClasses');

    Route::get('/get-centers', 'StudentApplicationController@getCenters');


    Route::post('/student-store', 'StudentApplicationController@studentStore');

    Route::get('/saas-student-list', 'StudentApplicationController@saasStudentList');
    Route::post('/saas-student-list', 'StudentApplicationController@saasStudentListsearch');

    Route::post('student-store', ['as' => 'student_store', 'uses' => 'StudentApplicationController@studentStore']);
    Route::post('student-guardian-store', ['as' => 'student_guardian_store', 'uses' => 'StudentApplicationController@studentStoreByGuardian']);
//    Route::get('/student-list', 'StudentApplicationController@studentList')->name("student.list");
    Route::get('/application-list', 'StudentApplicationController@applicationList')->name("application.list");
    Route::post('/student-list', 'StudentApplicationController@studentListSearch');
    Route::post('/student-download-offer', 'StudentApplicationController@downloadOfferLetter')->name('download.student.offerletter');
    Route::post('student-approve', 'StudentApplicationController@studentApprove');
    Route::get('student-view/{id}', 'StudentApplicationController@studentView')->name('student.view');

    Route::post('student-delete', 'StudentApplicationController@studentDelete');


    Route::get('check-student-email', 'StudentApplicationController@checkStudentEmail');
    Route::get('check-dependent-student-email', 'StudentApplicationController@checkDependentStudentEmail');
//
    Route::get('check-student-mobile', 'StudentApplicationController@checkStudentMobile');
    Route::get('check-student-age/', 'StudentApplicationController@checkStudentAge');
    Route::get('check-student-id-no/', 'StudentApplicationController@checkStudentNationalId');

    Route::get('check-guardian-email', 'StudentApplicationController@checkGuardianEmail');

    Route::get('check-guardian-mobile', 'StudentApplicationController@checkGuardianMobile');

    // setting route
    Route::get('settings', 'StudentApplicationController@settings');
    Route::post('settings', 'StudentApplicationController@Updatesettings');
    Route::post('student-admission-pic', ['as' => 'student_admission_pic', 'uses' => 'StudentApplicationController@admissionPic']);
    
    // Photo validation and management routes
    Route::get('missing-photos-report', 'StudentApplicationController@missingPhotosReport')->name('missing.photos.report');
    Route::post('student-upload-photo/{student_id}', 'StudentApplicationController@uploadStudentPhoto')->name('student_upload_photo');
    Route::get('student-photo-reminder/{student_id}', 'StudentApplicationController@sendPhotoReminder')->name('student_photo_reminder');

    // Add this route for checking photo session
    Route::get('student-admission-pic/check', 'StudentApplicationController@checkPhotoSession')->name('check-dependent-photo-session');
//});
});

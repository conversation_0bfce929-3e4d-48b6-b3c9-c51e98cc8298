@extends(theme_path('layouts.studentprofile'))

@section('content2')

@if($student->status=="active"||Auth::user()->status=="active" )

  <!-- <h3>Student Profile/h3> -->
        <div class="col-md-12">
         
  
        <div class="well">
              
                <h3>Student Profile</h3>
           <table class="table table-responsive table-stripped">
            <tbody>
            <tr>
                <td>Student Name</td>
                <td>{{Auth::user()->full_name}}</td>
            </tr>
            <tr>
                <td>Student Name (Arabic)</td>
                <td>{{Auth::user()->full_name_trans}}</td>
            </tr>
            
            <tr>
                <td>Gender</td>
                <td>{{Auth::user()->gender}}</td>
            </tr>
            <tr>
                <td>Date of Birth</td>
                <td>{{Auth::user()->date_of_birth}}</td>
            </tr>
            <tr>
                <td>Nationality</td>
                <td>{{Auth::user()->nationality}}</td>
            </tr>
            <tr>
                <td>Identity Number</td>
                <td>{{Auth::user()->dentity_number}}</td>
            </tr>
            <tr>
                <td>Mobile Number</td>
                <td>{{Auth::user()->mobile}}</td>
            </tr>
            
        </tbody>
        </table>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group bmd-form-group">
                        
                    <a href="setting" class="btn btn-primary"><i class=""></i> Update Profile </a>                 </div>
                </div>
            
           </div>
                    
        </div>
     
</div>
            @else
            @include(theme_path('student.profile_form'))
            @endif

@endsection

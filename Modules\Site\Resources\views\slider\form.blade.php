<div class="col-md-12">
@if(in_array('image' , $slider_setting))
<div class="form-group {{ $errors->has('image') ? 'has-error' : ''}}">
    <div class="input-group">
        {!! Form::hidden('image', null, ['class' => 'form-control' , 'id' => 'thumbnail']) !!}
        
    </div>
    <img id="holder" style="margin-top:15px;max-height:100px;" src="{{$slider->image ?? ''}}">
    <span class="input-group-btn">
        <a id="image" data-input="thumbnail" data-preview="holder" class="btn btn-primary">
        <i class="fa fa-picture-o"></i> Choose Image
        </a>
    </span>
</div>
@endif

@if(in_array('title' , $slider_setting) || in_array('description', $slider_setting))
<div class="nav-tabs-custom">
    <ul class="nav nav-tabs">
        @foreach($languages as $key => $language)
        <li @if($key == 0 ) class="active" @endif>
            <a aria-expanded="true" data-toggle="tab" href="#{{$language}}">
                {{strtoupper($language)}}
            </a>
        </li>
        @endforeach
    </ul>
    <div class="tab-description">
        @foreach($languages as $key => $language)
        <div class="tab-pane clearfix @if($key == 0 ) active @endif" id="{{$language}}">
            <div class="col-md-12">
                @if(in_array('title', $slider_setting))
            
                <div class="form-group {{ $errors->has($language.'.title') ? 'has-error' : ''}}">
                    {!! Form::label('title', 'Title ['.$language.']', ['class' => 'control-label']) !!}
                
                    {!! Form::text('translate['.$language.'][title]', isset($slider) && isset($slider->translate($language)->title) ? $slider->translate($language)->title : '' , ['class' => 'form-control']) !!}
                    {!! $errors->first('translate.'.$language.'.title', '
                    <p class="help-block alert-danger">
                        :message
                    </p>
                    ') !!}
                </div>
                @endif
                @if(in_array('description', $slider_setting))
                <div class="form-group {{ $errors->has($language.'.description') ? 'has-error' : ''}} description" >
                    {!! Form::label('description', 'Content ['.$language.']', ['class' => 'control-label']) !!}
                    {!! Form::textarea('translate['.$language.'][description]',isset($slider) && isset($slider->translate($language)->description) ? $slider->translate($language)->description : '' , ['class' => 'form-control']) !!}
                    {!! $errors->first('translate.'.$language.'description', '
                    <p class="help-block alert-danger">
                        :message
                    </p>
                    ') !!}
                </div>
                @endif
            </div>
        </div>
        <!-- /.tab-pane -->
        @endforeach
    </div>
    <!-- /.tab-description -->
</div>
@endif
<!-- /.nav-tabs-custom -->
<div class="col-md-12">
    @if(in_array('link', $slider_setting))                      
    <div class="form-group {{ $errors->has('link') ? 'has-error' : ''}} link">
        {!! Form::label('link', 'Page URL', ['class' => 'control-label']) !!}
        
            {!! Form::text('link', null, ['class' => 'form-control']) !!}
            {!! $errors->first('link', '
            <p class="help-block alert-danger">
                :message
            </p>
            ') !!}
    </div>
    @endif

    <div class="form-group {{ $errors->has('status') ? 'has-error' : ''}}">
        {!! Form::label('status', 'Status', ['class' => 'control-label']) !!}
        
            {!! Form::select('status', [1 => 'Published' , 0 => 'Disabled/Not Published'] , null, ['class' => 'form-control']) !!}
            {!! $errors->first('status', '
            <p class="help-block alert-danger">
                :message
            </p>
            ') !!}
    </div>
    <div class="form-group">
        <div class="col-md-offset-4" >
            {!! Form::submit(isset($submitButtonText) ? $submitButtonText : 'Create', ['class' => 'btn btn-primary']) !!}
        </div>
    </div>
    </div>
</div>

@section('js')

<script src="{{ asset('assets/lfm/js/lfm.js') }}"></script>
<script>
    
    $(document).ready(function() {
      $('#image').filemanager('image');
    });

</script>
@endsection
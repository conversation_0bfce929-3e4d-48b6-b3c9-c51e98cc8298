/**
 * Created by it4om on 15/02/2017.
 */


$(document).ready(function () {

    $('#contact_us_form').on('submit', function (e) {
        e.preventDefault();


        var data_contact_full_name = $('#contact_full_name').val();
        var data_contact_email = $('#contact_email').val();
        var data_contact_tel = $('#contact_tel').val();
        var data_contact_subject = $('#contact_subject').val();
        var data_contact_message = $('#contact_message').val();


        $('#submith_newsletter').html('<i class="fa fa-refresh fa-spin"></i>');


        $.ajax({
            type: "POST",
            url: '/home/<USER>',
            data: {
                contact_full_name: data_contact_full_name,
                contact_email: data_contact_email,
                contact_tel: data_contact_tel,
                contact_subject: data_contact_subject,
                contact_message: data_contact_message
            },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function (msg) {
                //alert(msg);

                if (msg == "data_not_valid") {
                    _toastr("You enter some non valid data", "bottom-right", "error", false);
                    $('#submith_newsletter').html('<i class="fa fa-frown-o"></i>');
                    $('#submith_newsletter').addClass('red-d4');
                }
                else if (msg == "failed") {
                    _toastr("Unexpected error , please try again later", "bottom-right", "error", false);
                    $('#submith_newsletter').html('<i class="fa fa-frown-o"></i>');
                    $('#submith_newsletter').addClass('red-d4');
                }
                else if (msg == "empty") {
                    _toastr("Full name, Email , Subject and Message are required", "bottom-right", "error", false);
                    $('#submith_newsletter').html('<i class="fa fa-frown-o"></i>');
                    $('#submith_newsletter').addClass('red-d4');
                }
                else /*success*/
                {
                    _toastr(msg, "bottom-right", "success", false);
                    $('#submith_newsletter').html('<i class="fa fa-check-square-o"></i>');
                    $('#submith_newsletter').addClass('green-d4');
                }


            }

        });
    });


});
    


<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Modules\JobSeeker\Entities\SystemErrorAlert;
use Modules\JobSeeker\Entities\CommandScheduleExecution;
use App\Services\EmailService;
use Throwable;

/**
 * System Error Notification Service
 * 
 * Comprehensive error monitoring and alerting system that automatically
 * notifies the founder (<EMAIL>) of critical system failures
 * with detailed error reports and attachments.
 */
final class SystemErrorNotificationService
{
    /**
     * Error severity levels
     */
    public const SEVERITY_CRITICAL = 'critical';
    public const SEVERITY_HIGH = 'high';
    public const SEVERITY_MEDIUM = 'medium';
    public const SEVERITY_LOW = 'low';

    /**
     * Error categories
     */
    public const CATEGORY_JOB_FETCH = 'job_fetch_failure';
    public const CATEGORY_EMAIL_SEND = 'email_send_failure';
    public const CATEGORY_API_TIMEOUT = 'api_timeout';
    public const CATEGORY_DATABASE = 'database_error';
    public const CATEGORY_PERFORMANCE = 'performance_degradation';
    public const CATEGORY_SYSTEM = 'system_error';

    /**
     * Rate limiting configuration (prevent spam)
     */
    private const RATE_LIMIT_WINDOW = 3600; // 1 hour
    private const MAX_ALERTS_PER_CATEGORY = 3; // Max 3 alerts per category per hour
    private const CRITICAL_ALERT_COOLDOWN = 900; // 15 minutes for critical alerts

    /**
     * Performance thresholds
     */
    private const PERFORMANCE_THRESHOLDS = [
        'api_response_time' => 30.0, // seconds
        'memory_usage' => 512, // MB
        'execution_time' => 300, // seconds (5 minutes)
        'error_rate' => 0.25, // 25%
    ];

    private EmailService $emailService;

    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }

    /**
     * Report a job fetch failure
     */
    public function reportJobFetchFailure(
        string $provider,
        string $errorMessage,
        array $context = [],
        ?Throwable $exception = null
    ): void {
        $severity = $this->determineSeverity($errorMessage, $exception);
        
        $this->createAlert(
            self::CATEGORY_JOB_FETCH,
            $severity,
            "Job fetch failure for {$provider}",
            $errorMessage,
            array_merge($context, [
                'provider' => $provider,
                'error_type' => $this->categorizeError($errorMessage, $exception),
            ]),
            $exception
        );
    }

    /**
     * Report an email sending failure
     */
    public function reportEmailSendFailure(
        string $recipientEmail,
        string $errorMessage,
        array $context = [],
        ?Throwable $exception = null
    ): void {
        $this->createAlert(
            self::CATEGORY_EMAIL_SEND,
            self::SEVERITY_HIGH, // Email failures are always high priority
            "Email sending failure",
            $errorMessage,
            array_merge($context, [
                'recipient_email' => $recipientEmail,
                'email_provider' => $context['email_provider'] ?? 'unknown',
            ]),
            $exception
        );
    }

    /**
     * Report API timeout or connectivity issues
     */
    public function reportApiFailure(
        string $apiUrl,
        string $errorMessage,
        float $responseTime,
        array $context = [],
        ?Throwable $exception = null
    ): void {
        $severity = $responseTime > 60 ? self::SEVERITY_CRITICAL : self::SEVERITY_HIGH;
        
        $this->createAlert(
            self::CATEGORY_API_TIMEOUT,
            $severity,
            "API failure or timeout",
            $errorMessage,
            array_merge($context, [
                'api_url' => $apiUrl,
                'response_time' => $responseTime,
                'timeout_threshold' => self::PERFORMANCE_THRESHOLDS['api_response_time'],
            ]),
            $exception
        );
    }

    /**
     * Report database errors
     */
    public function reportDatabaseError(
        string $operation,
        string $errorMessage,
        array $context = [],
        ?Throwable $exception = null
    ): void {
        $this->createAlert(
            self::CATEGORY_DATABASE,
            self::SEVERITY_CRITICAL, // Database errors are always critical
            "Database error during {$operation}",
            $errorMessage,
            array_merge($context, [
                'operation' => $operation,
                'database_connection' => config('database.default'),
            ]),
            $exception
        );
    }

    /**
     * Report performance degradation
     */
    public function reportPerformanceDegradation(
        string $component,
        array $metrics,
        array $thresholds = []
    ): void {
        $exceededThresholds = [];
        $severity = self::SEVERITY_LOW;

        foreach ($metrics as $metric => $value) {
            $threshold = $thresholds[$metric] ?? self::PERFORMANCE_THRESHOLDS[$metric] ?? null;
            if ($threshold && $value > $threshold) {
                $exceededThresholds[$metric] = [
                    'value' => $value,
                    'threshold' => $threshold,
                    'exceeded_by' => $value - $threshold,
                ];
                
                // Escalate severity based on how badly thresholds are exceeded
                if ($value > $threshold * 2) {
                    $severity = self::SEVERITY_CRITICAL;
                } elseif ($value > $threshold * 1.5) {
                    $severity = self::SEVERITY_HIGH;
                } elseif ($severity === self::SEVERITY_LOW) {
                    $severity = self::SEVERITY_MEDIUM;
                }
            }
        }

        if (!empty($exceededThresholds)) {
            $this->createAlert(
                self::CATEGORY_PERFORMANCE,
                $severity,
                "Performance degradation in {$component}",
                "Performance thresholds exceeded",
                [
                    'component' => $component,
                    'metrics' => $metrics,
                    'exceeded_thresholds' => $exceededThresholds,
                    'timestamp' => now()->toDateTimeString(),
                ]
            );
        }
    }

    /**
     * Report general system errors
     */
    public function reportSystemError(
        string $component,
        string $errorMessage,
        array $context = [],
        ?Throwable $exception = null
    ): void {
        $severity = $this->determineSeverity($errorMessage, $exception);
        
        $this->createAlert(
            self::CATEGORY_SYSTEM,
            $severity,
            "System error in {$component}",
            $errorMessage,
            array_merge($context, [
                'component' => $component,
                'server_info' => [
                    'php_version' => PHP_VERSION,
                    'memory_limit' => ini_get('memory_limit'),
                    'max_execution_time' => ini_get('max_execution_time'),
                ],
            ]),
            $exception
        );
    }

    /**
     * Monitor command execution and report issues
     */
    public function monitorCommandExecution(CommandScheduleExecution $execution): void
    {
        // Check for failures
        if ($execution->status === 'failed' || $execution->exit_code !== 0) {
            $this->reportJobFetchFailure(
                $this->extractProviderFromCommand($execution->command),
                $execution->error_output ?? 'Command execution failed',
                [
                    'execution_id' => $execution->id,
                    'command' => $execution->command,
                    'exit_code' => $execution->exit_code,
                    'execution_time' => $execution->execution_time_seconds,
                    'memory_usage' => $execution->memory_usage_mb,
                ]
            );
        }

        // Check for performance issues
        if ($execution->execution_time_seconds > self::PERFORMANCE_THRESHOLDS['execution_time']) {
            $this->reportPerformanceDegradation(
                'Command Execution',
                [
                    'execution_time' => $execution->execution_time_seconds,
                    'memory_usage' => $execution->memory_usage_mb ?? 0,
                ],
                [
                    'execution_time' => self::PERFORMANCE_THRESHOLDS['execution_time'],
                    'memory_usage' => self::PERFORMANCE_THRESHOLDS['memory_usage'],
                ]
            );
        }

        // Check for zero jobs fetched (potential issue)
        if ($execution->jobs_fetched === 0 && $execution->status === 'completed') {
            $this->reportJobFetchFailure(
                $this->extractProviderFromCommand($execution->command),
                'No jobs fetched during execution',
                [
                    'execution_id' => $execution->id,
                    'command' => $execution->command,
                    'execution_time' => $execution->execution_time_seconds,
                    'warning_type' => 'zero_jobs_fetched',
                ]
            );
        }
    }

    /**
     * Create and process an alert
     */
    private function createAlert(
        string $category,
        string $severity,
        string $title,
        string $message,
        array $context = [],
        ?Throwable $exception = null
    ): void {
        try {
            // Check rate limiting
            if (!$this->shouldSendAlert($category, $severity)) {
                Log::info('SystemErrorNotificationService: Alert rate limited', [
                    'category' => $category,
                    'severity' => $severity,
                    'title' => $title,
                ]);
                return;
            }

            // Create alert record
            $alert = SystemErrorAlert::create([
                'category' => $category,
                'severity' => $severity,
                'title' => $title,
                'message' => $message,
                'context' => $context,
                'exception_details' => $exception ? [
                    'class' => get_class($exception),
                    'message' => $exception->getMessage(),
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine(),
                    'trace' => $exception->getTraceAsString(),
                ] : null,
                'created_at' => now(),
            ]);

            // Generate error report attachment
            $attachmentPath = $this->generateErrorReport($alert, $exception);

            // Send notification email
            $this->sendAlertEmail($alert, $attachmentPath);

            // Update rate limiting cache
            $this->updateRateLimit($category, $severity);

            Log::info('SystemErrorNotificationService: Alert created and sent', [
                'alert_id' => $alert->id,
                'category' => $category,
                'severity' => $severity,
                'title' => $title,
            ]);

        } catch (Exception $e) {
            // Fallback logging if alert system fails
            Log::critical('SystemErrorNotificationService: Failed to create alert', [
                'original_category' => $category,
                'original_severity' => $severity,
                'original_title' => $title,
                'original_message' => $message,
                'alert_system_error' => $e->getMessage(),
                'alert_system_trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Check if alert should be sent based on rate limiting
     */
    private function shouldSendAlert(string $category, string $severity): bool
    {
        $cacheKey = "error_alert_rate_limit:{$category}";
        $criticalCacheKey = "error_alert_critical:{$category}";

        // Critical alerts have special handling
        if ($severity === self::SEVERITY_CRITICAL) {
            $lastCritical = Cache::get($criticalCacheKey);
            if ($lastCritical && now()->diffInSeconds($lastCritical) < self::CRITICAL_ALERT_COOLDOWN) {
                return false;
            }
        }

        // General rate limiting
        $alertCount = Cache::get($cacheKey, 0);
        return $alertCount < self::MAX_ALERTS_PER_CATEGORY;
    }

    /**
     * Update rate limiting cache
     */
    private function updateRateLimit(string $category, string $severity): void
    {
        $cacheKey = "error_alert_rate_limit:{$category}";
        $criticalCacheKey = "error_alert_critical:{$category}";

        // Increment general counter
        $currentCount = Cache::get($cacheKey, 0);
        Cache::put($cacheKey, $currentCount + 1, self::RATE_LIMIT_WINDOW);

        // Update critical alert timestamp
        if ($severity === self::SEVERITY_CRITICAL) {
            Cache::put($criticalCacheKey, now(), self::CRITICAL_ALERT_COOLDOWN);
        }
    }

    /**
     * Generate detailed error report as attachment
     */
    private function generateErrorReport(SystemErrorAlert $alert, ?Throwable $exception = null): ?string
    {
        try {
            $reportContent = $this->buildErrorReportContent($alert, $exception);

            $filename = sprintf(
                'error_report_%s_%s_%s.txt',
                $alert->category,
                $alert->severity,
                now()->format('Y-m-d_H-i-s')
            );

            $path = "error_reports/{$filename}";
            Storage::disk('local')->put($path, $reportContent);

            return storage_path("app/{$path}");

        } catch (Exception $e) {
            Log::error('SystemErrorNotificationService: Failed to generate error report', [
                'alert_id' => $alert->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Build comprehensive error report content
     */
    private function buildErrorReportContent(SystemErrorAlert $alert, ?Throwable $exception = null): string
    {
        $report = [];

        $report[] = "=== SYSTEM ERROR REPORT ===";
        $report[] = "Generated: " . now()->toDateTimeString();
        $report[] = "Alert ID: " . $alert->id;
        $report[] = "";

        $report[] = "=== ALERT DETAILS ===";
        $report[] = "Category: " . $alert->category;
        $report[] = "Severity: " . strtoupper($alert->severity);
        $report[] = "Title: " . $alert->title;
        $report[] = "Message: " . $alert->message;
        $report[] = "";

        if (!empty($alert->context)) {
            $report[] = "=== CONTEXT INFORMATION ===";
            $report[] = json_encode($alert->context, JSON_PRETTY_PRINT);
            $report[] = "";
        }

        if ($exception) {
            $report[] = "=== EXCEPTION DETAILS ===";
            $report[] = "Class: " . get_class($exception);
            $report[] = "Message: " . $exception->getMessage();
            $report[] = "File: " . $exception->getFile() . ":" . $exception->getLine();
            $report[] = "";
            $report[] = "=== STACK TRACE ===";
            $report[] = $exception->getTraceAsString();
            $report[] = "";
        }

        $report[] = "=== SYSTEM INFORMATION ===";
        $report[] = "PHP Version: " . PHP_VERSION;
        $report[] = "Laravel Version: " . app()->version();
        $report[] = "Memory Usage: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB";
        $report[] = "Peak Memory: " . round(memory_get_peak_usage(true) / 1024 / 1024, 2) . " MB";
        $report[] = "Server Time: " . now()->toDateTimeString();
        $report[] = "Timezone: " . config('app.timezone');
        $report[] = "";

        $report[] = "=== RECENT LOG ENTRIES ===";
        $report[] = $this->getRecentLogEntries();

        return implode("\n", $report);
    }

    /**
     * Send alert email to founder
     */
    private function sendAlertEmail(SystemErrorAlert $alert, ?string $attachmentPath = null): void
    {
        try {
            $founderEmail = config('jobseeker.admin_notification_email', '<EMAIL>');

            $subject = sprintf(
                '[%s] %s - %s',
                strtoupper($alert->severity),
                $alert->category,
                $alert->title
            );

            $attachments = [];
            if ($attachmentPath && file_exists($attachmentPath)) {
                $attachments[] = $attachmentPath;
            }

            $result = $this->emailService->sendEmail(
                [
                    'email' => $founderEmail,
                    'name' => 'System Administrator'
                ],
                $subject,
                'modules.jobseeker.emails.admin.system_error_alert',
                [
                    'alert' => $alert,
                    'severity_color' => $this->getSeverityColor($alert->severity),
                    'category_icon' => $this->getCategoryIcon($alert->category),
                ],
                $attachments
            );

            if (!$result['success']) {
                Log::error('SystemErrorNotificationService: Failed to send alert email', [
                    'alert_id' => $alert->id,
                    'email_error' => $result['message'] ?? 'Unknown error',
                ]);
            }

        } catch (Exception $e) {
            Log::error('SystemErrorNotificationService: Exception sending alert email', [
                'alert_id' => $alert->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get recent log entries for error report
     */
    private function getRecentLogEntries(): string
    {
        try {
            $logFile = storage_path('logs/laravel.log');
            if (!file_exists($logFile)) {
                return "Log file not found.";
            }

            // Use tail command for better memory efficiency
            if (function_exists('exec') && strtoupper(substr(PHP_OS, 0, 3)) !== 'WIN') {
                $output = [];
                exec("tail -n 50 " . escapeshellarg($logFile), $output);
                return implode("\n", $output);
            }

            // Fallback for Windows or when exec is disabled
            $fileSize = filesize($logFile);
            if ($fileSize > 10 * 1024 * 1024) { // If file is larger than 10MB
                return "Log file too large to include in error report.";
            }

            $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $recentLines = array_slice($lines, -50); // Last 50 lines

            return implode("\n", $recentLines);

        } catch (Exception $e) {
            return "Error reading log file: " . $e->getMessage();
        }
    }

    /**
     * Determine error severity based on message and exception
     */
    private function determineSeverity(string $message, ?Throwable $exception = null): string
    {
        $message = strtolower($message);

        // Critical indicators
        if (str_contains($message, 'fatal') ||
            str_contains($message, 'critical') ||
            str_contains($message, 'database') ||
            str_contains($message, 'connection refused') ||
            ($exception && str_contains(get_class($exception), 'Fatal'))) {
            return self::SEVERITY_CRITICAL;
        }

        // High severity indicators
        if (str_contains($message, 'timeout') ||
            str_contains($message, 'failed') ||
            str_contains($message, 'error') ||
            str_contains($message, 'exception')) {
            return self::SEVERITY_HIGH;
        }

        // Medium severity indicators
        if (str_contains($message, 'warning') ||
            str_contains($message, 'deprecated') ||
            str_contains($message, 'slow')) {
            return self::SEVERITY_MEDIUM;
        }

        return self::SEVERITY_LOW;
    }

    /**
     * Categorize error type for better tracking
     */
    private function categorizeError(string $message, ?Throwable $exception = null): string
    {
        $message = strtolower($message);
        $exceptionClass = $exception ? strtolower(get_class($exception)) : '';

        if (str_contains($message, 'timeout') || str_contains($message, 'curl')) {
            return 'network';
        }

        if (str_contains($message, 'database') || str_contains($message, 'sql')) {
            return 'database';
        }

        if (str_contains($message, 'memory') || str_contains($message, 'limit')) {
            return 'resource';
        }

        if (str_contains($message, 'permission') || str_contains($message, 'access')) {
            return 'permission';
        }

        return 'general';
    }

    /**
     * Extract provider name from command
     */
    private function extractProviderFromCommand(string $command): string
    {
        if (str_contains($command, 'acbar')) {
            return 'ACBAR';
        }

        if (str_contains($command, 'jobs-af')) {
            return 'Jobs.af';
        }

        return 'Unknown';
    }

    /**
     * Get color for severity level
     */
    private function getSeverityColor(string $severity): string
    {
        return match ($severity) {
            self::SEVERITY_CRITICAL => '#dc3545',
            self::SEVERITY_HIGH => '#fd7e14',
            self::SEVERITY_MEDIUM => '#ffc107',
            self::SEVERITY_LOW => '#17a2b8',
            default => '#6c757d',
        };
    }

    /**
     * Get icon for error category
     */
    private function getCategoryIcon(string $category): string
    {
        return match ($category) {
            self::CATEGORY_JOB_FETCH => 'fas fa-download',
            self::CATEGORY_EMAIL_SEND => 'fas fa-envelope',
            self::CATEGORY_API_TIMEOUT => 'fas fa-clock',
            self::CATEGORY_DATABASE => 'fas fa-database',
            self::CATEGORY_PERFORMANCE => 'fas fa-tachometer-alt',
            self::CATEGORY_SYSTEM => 'fas fa-server',
            default => 'fas fa-exclamation-triangle',
        };
    }
}

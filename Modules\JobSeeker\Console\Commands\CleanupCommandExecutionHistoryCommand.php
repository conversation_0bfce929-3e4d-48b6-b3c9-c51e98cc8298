<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Console\Commands;

use Illuminate\Console\Command;
use <PERSON><PERSON>les\JobSeeker\Entities\CommandScheduleExecution;
use Illuminate\Support\Facades\Log;

/**
 * CleanupCommandExecutionHistoryCommand
 * 
 * Cleans up old command execution history records to prevent database bloat
 * while maintaining recent execution data for monitoring and debugging.
 */
final class CleanupCommandExecutionHistoryCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobseeker:cleanup-execution-history 
                          {--hours=48 : Age in hours for records to be cleaned up}
                          {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old command execution history records (default: 48 hours old)';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $hours = (int)$this->option('hours');
        $isDryRun = $this->option('dry-run');
        
        $this->info("Command Execution History Cleanup");
        $this->info("==================================");
        $this->info("Cleaning up records older than {$hours} hours");
        
        if ($isDryRun) {
            $this->warn("DRY RUN MODE - No records will be deleted");
        }
        
        Log::info('CleanupCommandExecutionHistoryCommand: Starting cleanup', [
            'hours_old' => $hours,
            'dry_run' => $isDryRun,
            'command_signature' => $this->signature
        ]);
        
        try {
            if ($isDryRun) {
                // Show what would be deleted
                $cutoffTime = now()->subHours($hours);
                $recordsToDelete = CommandScheduleExecution::where('created_at', '<', $cutoffTime)
                    ->whereIn('status', [
                        CommandScheduleExecution::STATUS_COMPLETED,
                        CommandScheduleExecution::STATUS_FAILED,
                        CommandScheduleExecution::STATUS_TIMEOUT
                    ])
                    ->get();
                
                $this->info("Records that would be deleted: {$recordsToDelete->count()}");
                
                if ($recordsToDelete->count() > 0) {
                    $this->table(
                        ['ID', 'Command', 'Status', 'Started At', 'Completed At', 'Duration'],
                        $recordsToDelete->map(function ($execution) {
                            return [
                                $execution->id,
                                $execution->command,
                                $execution->status,
                                $execution->started_at->format('Y-m-d H:i:s'),
                                $execution->completed_at?->format('Y-m-d H:i:s') ?? 'N/A',
                                $execution->duration_human
                            ];
                        })->toArray()
                    );
                }
                
                $this->info("Use without --dry-run to actually delete these records");
                return 0;
            }
            
            // Perform actual cleanup
            $deletedCount = CommandScheduleExecution::cleanupOldExecutions($hours);
            
            $this->info("Successfully cleaned up {$deletedCount} execution records");
            
            // Show remaining records summary
            $totalRemaining = CommandScheduleExecution::count();
            $runningCount = CommandScheduleExecution::where('status', CommandScheduleExecution::STATUS_RUNNING)->count();
            $completedCount = CommandScheduleExecution::where('status', CommandScheduleExecution::STATUS_COMPLETED)->count();
            $failedCount = CommandScheduleExecution::where('status', CommandScheduleExecution::STATUS_FAILED)->count();
            
            $this->info("Remaining execution records: {$totalRemaining}");
            $this->line("  - Running: {$runningCount}");
            $this->line("  - Completed: {$completedCount}");
            $this->line("  - Failed: {$failedCount}");
            
            // Check for zombie processes
            $zombieExecutions = CommandScheduleExecution::getZombieExecutions(120); // 2 hours
            if ($zombieExecutions->count() > 0) {
                $this->warn("Found {$zombieExecutions->count()} potentially zombie executions (running > 2 hours)");
                $this->table(
                    ['ID', 'Command', 'Started At', 'Running For'],
                    $zombieExecutions->map(function ($execution) {
                        return [
                            $execution->id,
                            $execution->command,
                            $execution->started_at->format('Y-m-d H:i:s'),
                            $execution->started_at->diffForHumans()
                        ];
                    })->toArray()
                );
            }
            
            Log::info('CleanupCommandExecutionHistoryCommand: Cleanup completed successfully', [
                'deleted_count' => $deletedCount,
                'total_remaining' => $totalRemaining,
                'running_count' => $runningCount,
                'zombie_count' => $zombieExecutions->count(),
                'hours_old' => $hours
            ]);
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error("Cleanup failed: {$e->getMessage()}");
            
            Log::error('CleanupCommandExecutionHistoryCommand: Cleanup failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'hours_old' => $hours,
                'dry_run' => $isDryRun
            ]);
            
            return 1;
        }
    }
} 
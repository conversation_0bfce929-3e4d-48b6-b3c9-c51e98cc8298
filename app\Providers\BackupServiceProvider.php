<?php

declare(strict_types=1);

namespace App\Providers;

use App\Notifications\BackupHasFailedNotification;
use App\Notifications\BackupNotifiable;
use App\Notifications\BackupWasSuccessfulNotification;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;
use Spatie\Backup\Events\BackupHasFailed;
use Spatie\Backup\Events\BackupWasSuccessful;

class BackupServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(BackupNotifiable::class, function () {
            return new BackupNotifiable();
        });
    }

    public function boot(): void
    {
        Event::listen(BackupHasFailed::class, function (BackupHasFailed $event) {
            $this->sendBackupFailedNotification($event);
        });

        Event::listen(BackupWasSuccessful::class, function (BackupWasSuccessful $event) {
            $this->sendBackupSuccessNotification($event);
        });
    }

    protected function sendBackupFailedNotification(BackupHasFailed $event): void
    {
        if (!$this->shouldSendNotifications()) {
            return;
        }

        try {
            $notifiable = app(BackupNotifiable::class);
            $notification = new BackupHasFailedNotification($event);
            
            $notifiable->notify($notification);
            
            \Log::info('Backup failed notification dispatched', [
                'destination' => $event->backupDestination->backupName(),
                'error' => $event->exception->getMessage(),
                'recipient' => $notifiable->routeNotificationForMail()
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Failed to send backup failed notification', [
                'error' => $e->getMessage(),
                'original_backup_error' => $event->exception->getMessage()
            ]);
        }
    }

    protected function sendBackupSuccessNotification(BackupWasSuccessful $event): void
    {
        if (!$this->shouldSendNotifications()) {
            return;
        }

        try {
            $notifiable = app(BackupNotifiable::class);
            $notification = new BackupWasSuccessfulNotification($event);
            
            $notifiable->notify($notification);
            
            \Log::info('Backup success notification dispatched', [
                'destination' => $event->backupDestination->backupName(),
                'recipient' => $notifiable->routeNotificationForMail()
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Failed to send backup success notification', [
                'error' => $e->getMessage(),
                'destination' => $event->backupDestination->backupName()
            ]);
        }
    }

    protected function shouldSendNotifications(): bool
    {
        return config('backup.notifications.enabled', true) && 
               !empty(config('backup.notifications.mail.to'));
    }
} 
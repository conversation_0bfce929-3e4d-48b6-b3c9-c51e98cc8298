    /*
    Flaticon icon font: Flaticon
    Creation date: 28/05/2019 09:46
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff2") format("woff2"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-speedometer:before { content: "\f100"; }
.flaticon-analytics:before { content: "\f101"; }
.flaticon-reading:before { content: "\f102"; }
.flaticon-professor:before { content: "\f103"; }
.flaticon-wallet:before { content: "\f104"; }
.flaticon-accounting:before { content: "\f105"; }
.flaticon-consultation:before { content: "\f106"; }
.flaticon-test:before { content: "\f107"; }
.flaticon-graduated-student:before { content: "\f108"; }
.flaticon-book:before { content: "\f109"; }
.flaticon-email:before { content: "\f10a"; }
.flaticon-reading-1:before { content: "\f10b"; }
.flaticon-inventory:before { content: "\f10c"; }
.flaticon-bus:before { content: "\f10d"; }
.flaticon-hotel:before { content: "\f10e"; }
.flaticon-software:before { content: "\f10f"; }
.flaticon-analysis:before { content: "\f110"; }
.flaticon-settings:before { content: "\f111"; }
.flaticon-resume:before { content: "\f112"; }
.flaticon-calendar:before { content: "\f113"; }
.flaticon-calendar-1:before { content: "\f114"; }
.flaticon-test-1:before { content: "\f115"; }
.flaticon-book-1:before { content: "\f116"; }
.flaticon-slumber:before { content: "\f117"; }
    
    $font-Flaticon-speedometer: "\f100";
    $font-Flaticon-analytics: "\f101";
    $font-Flaticon-reading: "\f102";
    $font-Flaticon-professor: "\f103";
    $font-Flaticon-wallet: "\f104";
    $font-Flaticon-accounting: "\f105";
    $font-Flaticon-consultation: "\f106";
    $font-Flaticon-test: "\f107";
    $font-Flaticon-graduated-student: "\f108";
    $font-Flaticon-book: "\f109";
    $font-Flaticon-email: "\f10a";
    $font-Flaticon-reading-1: "\f10b";
    $font-Flaticon-inventory: "\f10c";
    $font-Flaticon-bus: "\f10d";
    $font-Flaticon-hotel: "\f10e";
    $font-Flaticon-software: "\f10f";
    $font-Flaticon-analysis: "\f110";
    $font-Flaticon-settings: "\f111";
    $font-Flaticon-resume: "\f112";
    $font-Flaticon-calendar: "\f113";
    $font-Flaticon-calendar-1: "\f114";
    $font-Flaticon-test-1: "\f115";
    $font-Flaticon-book-1: "\f116";
    $font-Flaticon-slumber: "\f117";
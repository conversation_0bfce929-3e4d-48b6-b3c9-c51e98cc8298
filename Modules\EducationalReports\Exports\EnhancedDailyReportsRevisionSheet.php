<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\Student;
use App\StudentRevisionReport;
use App\StudentRevisionPlan;
use App\AttendanceOption;
use App\Classes;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\EducationalReports\Exports\Traits\PagesCalculator;
use Carbon\Carbon;

final class EnhancedDailyReportsRevisionSheet implements WithTitle, WithStyles, WithEvents
{
    use PagesCalculator;
    
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get revision reports - includes ALL students with active plans for every day of the month
     */
    private function getRevisionReports(): Collection
    {
        $classIds = $this->filters['classIds'] ?? [$this->filters['classId']];
        $year = $this->filters['year'];
        $month = $this->filters['month'];
        $studentId = $this->filters['studentId'] ?? null;

        // Get all days in the month
        $startDate = Carbon::create($year, $month, 1);
        $endDate = $startDate->copy()->endOfMonth();
        $daysInMonth = [];
        
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $daysInMonth[] = $date->format('Y-m-d');
        }

        // Get all students with active revision plans in the selected classes for this month
        $planYearMonth = sprintf('%d-%02d', $year, $month);
        
        $studentsWithPlans = Student::with([
            'revisionPlans' => function($query) use ($classIds, $planYearMonth) {
                $query->whereIn('class_id', $classIds)
                      ->where('plan_year_and_month', $planYearMonth)
                      ->where('status', 'active')
                      ->with(['class.center', 'class.programs', 'class.teachers']);
            }
        ])
        ->whereHas('revisionPlans', function($query) use ($classIds, $planYearMonth) {
            $query->whereIn('class_id', $classIds)
                  ->where('plan_year_and_month', $planYearMonth)
                  ->where('status', 'active');
        })
        ->where('status', 'active')  // Only include active students
        ->whereNull('deleted_at')    // Exclude soft-deleted students
        ->when($studentId, function($query, $studentId) {
            return $query->where('id', $studentId);
        })
        ->get();

        // Get all existing revision reports for these students and classes
        $existingReports = StudentRevisionReport::with([
            'classes.center',
            'classes.programs', 
            'classes.teachers',
            'fromSurat',
            'toSurat',
            'result',
            'attendanceOptions',
            'revisionPlan'
        ])
        ->whereIn('class_id', $classIds)
        ->whereYear('created_at', $year)
        ->whereMonth('created_at', $month)
        ->when($studentId, function($query, $studentId) {
            return $query->where('student_id', $studentId);
        })
        ->get()
        ->groupBy(function($report) {
            return $report->student_id . '_' . $report->created_at->format('Y-m-d') . '_' . $report->class_id;
        });

        $results = collect();

        // Generate rows for ALL students for ALL days
        foreach ($studentsWithPlans as $student) {
            foreach ($student->revisionPlans as $plan) {
                if (in_array($plan->class_id, $classIds)) {
                    $classProgram = $plan->class->programs->first()->title ?? 'N/A';
                    $teacherNames = $plan->class->teachers->pluck('full_name')->join(', ');
                    
                    // Calculate attendance summary for this student in this class
                    $attendanceSummary = $this->calculateAttendanceSummary($student->id, $plan->class_id, $year, $month);
                    
                    foreach ($daysInMonth as $date) {
                        $reportKey = $student->id . '_' . $date . '_' . $plan->class_id;
                        $existingReport = $existingReports->get($reportKey)?->first();
                        
                        $carbonDate = Carbon::parse($date);
                        
                        // Check if day is in timetable
                        $isInTimetable = $this->checkIfDayInTimetable($plan->class_id, $date);
                        
                        if ($existingReport) {
                            // Student has a report for this day
                            $numberOfPages = $this->calculateRevisionPages($existingReport);
                            $attendanceStatus = $existingReport->attendanceOptions->title ?? 'N/A';
                            $isAbsentOrExcused = in_array(strtolower($attendanceStatus), ['absent', 'excused']);
                            
                            $results->push([
                                'centre_id' => $plan->class->center->id ?? 'N/A',
                                'centre_name' => $plan->class->center->name ?? 'N/A',
                                'class_id' => $plan->class_id,
                                'class_name' => ($plan->class->name ?? $plan->class->class_code) ?? 'N/A',
                                'class_program' => $classProgram,
                                'teacher_name' => $teacherNames ?: 'N/A',
                                'student_id' => $student->id,
                                'student_name' => $student->full_name ?? 'N/A',
                                'date' => $date,
                                'day' => $carbonDate->format('D'), // 3-letter weekday
                                'is_day_in_timetable' => $isInTimetable ? 'Yes' : 'No',
                                'attendance' => $attendanceStatus,
                                'attendance_summary' => $attendanceSummary,
                                'from_surah' => $existingReport->fromSurat->name ?? '',
                                'from_verse' => $existingReport->revision_from_ayat ?? '',
                                'to_surah' => $existingReport->toSurat->name ?? '',
                                'to_verse' => $existingReport->revision_to_ayat ?? '',
                                'no_of_pages' => $numberOfPages,
                                'evaluation' => $existingReport->result->title ?? '',
                                'evaluation_note' => $existingReport->revision_evaluation_note ?? '',
                                'is_absent_or_excused' => $isAbsentOrExcused,
                                'has_report' => true
                            ]);
                        } else {
                            // Student has no report for this day - create empty row
                            $results->push([
                                'centre_id' => $plan->class->center->id ?? 'N/A',
                                'centre_name' => $plan->class->center->name ?? 'N/A',
                                'class_id' => $plan->class_id,
                                'class_name' => ($plan->class->name ?? $plan->class->class_code) ?? 'N/A',
                                'class_program' => $classProgram,
                                'teacher_name' => $teacherNames ?: 'N/A',
                                'student_id' => $student->id,
                                'student_name' => $student->full_name ?? 'N/A',
                                'date' => $date,
                                'day' => $carbonDate->format('D'), // 3-letter weekday
                                'is_day_in_timetable' => $isInTimetable ? 'Yes' : 'No',
                                'attendance' => 'No Report',
                                'attendance_summary' => $attendanceSummary,
                                'from_surah' => '',
                                'from_verse' => '',
                                'to_surah' => '',
                                'to_verse' => '',
                                'no_of_pages' => 0,
                                'evaluation' => '',
                                'evaluation_note' => '',
                                'is_absent_or_excused' => false,
                                'has_report' => false
                            ]);
                        }
                    }
                }
            }
        }

        return $results->sortBy(['student_name', 'date']);
    }

    /**
     * Calculate attendance summary for a student
     */
    private function calculateAttendanceSummary(int $studentId, int $classId, int $year, int $month): string
    {
        try {
            $totalDays = StudentRevisionReport::where('student_id', $studentId)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->count();

            $presentDays = StudentRevisionReport::where('student_id', $studentId)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereHas('attendanceOptions', function($query) {
                    $query->whereIn('title', ['Present', 'حاضر']);
                })
                ->count();

            $percentage = $totalDays > 0 ? round(($presentDays / $totalDays) * 100, 1) : 0;
            
            return "{$presentDays}/{$totalDays} ({$percentage}%)";
        } catch (\Exception $e) {
            \Log::error('Error calculating attendance summary: ' . $e->getMessage());
            return 'N/A';
        }
    }

    /**
     * Check if the day is in timetable using class_timetable relationship
     */
    private function checkIfDayInTimetable(int $classId, string $date): bool
    {
        try {
            $dayOfWeek = strtolower(Carbon::parse($date)->format('D')); // sat, sun, mon, tue, wed, thu, fri
            
            // Check if class has timetable for this day using class_timetable table
            $timetable = DB::table('class_timetable')
                ->where('class_id', $classId)
                ->whereNull('deleted_at')
                ->first();
                
            if (!$timetable) {
                return false;
            }
            
            // Check if the specific day column has a time value (not null)
            return !is_null($timetable->$dayOfWeek);
        } catch (\Exception $e) {
            \Log::error('Error checking timetable: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Calculate number of pages using stored procedure for revision
     */
    /**
     * Calculate revision pages using PROVEN ClassReportController methodology
     */
    private function calculateRevisionPages(StudentRevisionReport $report): int
    {
        // Initialize pages count (Line 1132 equivalent)
        $memorizedNumberofPages = 0;

        try {
            // EXACT same logic as ClassReportController lines 1138-1157
            if ($report->study_direction == 'backward') {
                // EXACT copy from lines 1140-1146
                $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $report->revision_from_surat,
                    $report->revision_from_ayat,
                    $report->revision_to_surat,
                    $report->revision_to_ayat
                ]);
                $memorizedNumberofPages = $result[0]->numberofPagesSum ?? 0;
            } else {
                // EXACT copy from lines 1149-1156
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $report->revision_from_surat,
                    $report->revision_from_ayat,
                    $report->revision_to_surat,
                    $report->revision_to_ayat
                ]);
                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                $memorizedNumberofPages = $results[0]->number_of_pages_sum ?? 0;
            }

            return $memorizedNumberofPages;
        } catch (\Exception $e) {
            \Log::error('Error calculating revision pages: ' . $e->getMessage(), [
                'report_id' => $report->id,
                'student_id' => $report->student_id,
                'class_id' => $report->class_id
            ]);
            return 0;
        }
    }

    /**
     * Get analytics data for performance dashboard (revision-specific)
     */
    private function getAnalyticsData(): array
    {
        $classIds = $this->filters['classIds'] ?? [$this->filters['classId']];
        $year = $this->filters['year'];
        $month = $this->filters['month'];

        // Daily trends aggregated across all classes (revision reports) with class info and student filter
        $studentFilter = !empty($this->filters['studentId']) ? " AND srr.student_id = " . intval($this->filters['studentId']) : "";
        $dailyTrends = DB::select("
            SELECT 
                DATE(srr.created_at) as report_date,
                srr.class_id,
                c.class_code as class_name,
                COUNT(DISTINCT cs.student_id) as active_students,
                COUNT(*) as total_sessions,
                COUNT(CASE WHEN ao.title IN ('late', 'on_time') THEN 1 END) as present_count,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) as avg_score,
                SUM(COALESCE(srr.pages_revised, 0)) as pages_revised
            FROM student_revision_report srr
            JOIN classes c ON srr.class_id = c.id
            JOIN class_students cs ON srr.student_id = cs.student_id AND srr.class_id = cs.class_id
            LEFT JOIN attendance_options ao ON srr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON srr.revision_evaluation_id = eso.id
            WHERE srr.class_id IN (" . implode(',', $classIds) . ")
                AND YEAR(srr.created_at) = ?
                AND MONTH(srr.created_at) = ?
                AND cs.deleted_at IS NULL
                {$studentFilter}
            GROUP BY DATE(srr.created_at), srr.class_id, c.class_code
            ORDER BY report_date DESC, srr.class_id
            LIMIT 50
        ", [$year, $month]);

        // Map translatable class names for trends
        if (!empty($dailyTrends)) {
            $trendClassIds = array_values(array_unique(array_map(function($t){ return (int)$t->class_id; }, $dailyTrends)));
            $classMap = collect(\App\Classes::whereIn('id', $trendClassIds)->get())->keyBy('id');
            foreach ($dailyTrends as $t) {
                $t->class_name = optional($classMap->get((int)$t->class_id))->name ?? ($t->class_name ?? 'N/A');
            }
        }

        // At-risk students across all classes (revision-specific)
        $atRiskStudents = DB::select("
            SELECT 
                s.full_name,
                COUNT(*) as total_sessions,
                COUNT(CASE WHEN ao.title IN ('late', 'on_time') THEN 1 END) as attended_sessions,
                ROUND((COUNT(CASE WHEN ao.title IN ('late', 'on_time') THEN 1 END) / COUNT(*)) * 100, 1) as attendance_rate,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) as avg_score
            FROM student_revision_report srr
            JOIN students s ON srr.student_id = s.id
            JOIN class_students cs ON srr.student_id = cs.student_id AND srr.class_id = cs.class_id
            LEFT JOIN attendance_options ao ON srr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON srr.revision_evaluation_id = eso.id
            WHERE srr.class_id IN (" . implode(',', $classIds) . ")
                AND YEAR(srr.created_at) = ?
                AND MONTH(srr.created_at) = ?
                AND cs.deleted_at IS NULL
                {$studentFilter}
            GROUP BY srr.student_id, s.full_name
            HAVING attendance_rate < 75 OR avg_score < 0.5
            ORDER BY attendance_rate ASC, avg_score ASC
            LIMIT 15
        ", [$year, $month]);

        // Teacher performance across all classes (revision-specific)
        $teacherPerformance = DB::select("
            SELECT 
                srr.class_id,
                c.class_code,
                GROUP_CONCAT(DISTINCT u.full_name SEPARATOR ', ') as teacher_name,
                COUNT(DISTINCT srr.student_id) as unique_students,
                COUNT(DISTINCT DATE(srr.created_at)) as actual_sessions,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) as avg_evaluation,
                ROUND((COUNT(CASE WHEN ao.title IN ('late', 'on_time') THEN 1 END) / 
                      NULLIF(COUNT(*), 0)) * 100, 1) as class_attendance_rate,
                0 as total_pages_revised, -- Will be calculated using stored procedures
                0 as total_pages_planned  -- Will be calculated from student_hefz_plans
            FROM student_revision_report srr
            JOIN classes c ON srr.class_id = c.id
            JOIN class_teachers ct ON c.id = ct.class_id
            JOIN employees u ON ct.employee_id = u.id
            LEFT JOIN attendance_options ao ON srr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON srr.revision_evaluation_id = eso.id
            WHERE srr.class_id IN (" . implode(',', $classIds) . ")
                AND YEAR(srr.created_at) = ?
                AND MONTH(srr.created_at) = ?
                {$studentFilter}
            GROUP BY srr.class_id, c.class_code
            ORDER BY avg_evaluation DESC, class_attendance_rate DESC
        ", [$year, $month]);

        return [
            'daily_trends' => $dailyTrends,
            'at_risk_students' => $atRiskStudents,
            'teacher_performance' => $teacherPerformance
        ];
    }

    /**
     * Create performance dashboard for multi-class aggregation (revision-specific)
     */
    private function createPerformanceDashboard($worksheet, $startRow, $analytics): int
    {
        $dailyTrends = $analytics['daily_trends'];
        $atRiskStudents = $analytics['at_risk_students'];
        $teacherPerformance = $analytics['teacher_performance'];

        // Title
        $worksheet->setCellValue("A{$startRow}", "📌 PERFORMANCE DASHBOARD - DAILY INSIGHTS (Multi-Class Revision Aggregated)");
        $worksheet->mergeCells("A{$startRow}:R{$startRow}");
        $worksheet->getStyle("A{$startRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '7B1FA2']]
        ]);

        $currentRow = $startRow + 2;

        // Recent Performance Trends (Last 10 days across all classes)
        $worksheet->setCellValue("A{$currentRow}", "📈 RECENT REVISION PERFORMANCE TRENDS (All Selected Classes)");
        $worksheet->mergeCells("A{$currentRow}:F{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '388E3C']]
        ]);
        $currentRow++;

        $trendHeaders = ['Date', 'Class ID', 'Class Name', 'Students', 'Sessions', 'Avg Score', 'Attendance %', 'Pages Revised'];
        foreach ($trendHeaders as $index => $header) {
            $col = chr(65 + $index); // A, B, C, etc.
            $worksheet->setCellValue("{$col}{$currentRow}", $header);
        }
        $currentRow++;

        foreach (array_slice($dailyTrends, 0, 30) as $trend) {
            $attendanceRate = $trend->total_sessions > 0 ? round(((float)$trend->present_count / (float)$trend->total_sessions) * 100, 1) : 0;
            $worksheet->setCellValue("A{$currentRow}", $trend->report_date);
            $worksheet->setCellValue("B{$currentRow}", $trend->class_id);
            $worksheet->setCellValue("C{$currentRow}", $trend->class_name);
            $worksheet->setCellValue("D{$currentRow}", $trend->active_students);
            $worksheet->setCellValue("E{$currentRow}", $trend->total_sessions);
            $worksheet->setCellValue("F{$currentRow}", round((float)($trend->avg_score ?? 0) * 100, 1) . '%');
            $worksheet->setCellValue("G{$currentRow}", $attendanceRate . '%');
            $worksheet->setCellValue("H{$currentRow}", $trend->pages_revised);
            $currentRow++;
        }

        $currentRow += 2;

        // At-Risk Students Alert (across all classes)
        $worksheet->setCellValue("A{$currentRow}", "🚨 AT-RISK STUDENTS - REVISION (Immediate Attention Required - All Classes)");
        $worksheet->mergeCells("A{$currentRow}:R{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'FF5722']]
        ]);
        $currentRow++;

        if (count($atRiskStudents) > 0) {
            $riskHeaders = ['Student Name', 'Sessions', 'Attended', 'Attendance %', 'Avg Score', 'Risk Level'];
            foreach ($riskHeaders as $index => $header) {
                $col = chr(65 + $index);
                $worksheet->setCellValue("{$col}{$currentRow}", $header);
            }
            $currentRow++;

            foreach (array_slice($atRiskStudents, 0, 15) as $student) {
                $riskLevel = '🔴 Critical';
                if ($student->attendance_rate >= 50 && $student->avg_score >= 0.3) {
                    $riskLevel = '⚠️ Moderate';
                }

                $worksheet->setCellValue("A{$currentRow}", $student->full_name);
                $worksheet->setCellValue("B{$currentRow}", $student->total_sessions);
                $worksheet->setCellValue("C{$currentRow}", $student->attended_sessions);
                $worksheet->setCellValue("D{$currentRow}", $student->attendance_rate . '%');
                $worksheet->setCellValue("E{$currentRow}", round((float)($student->avg_score ?? 0) * 100, 1) . '%');
                $worksheet->setCellValue("F{$currentRow}", $riskLevel);
                $currentRow++;
            }
        } else {
            $worksheet->setCellValue("A{$currentRow}", "✅ No at-risk students identified across all classes (Revision)");
            $currentRow++;
        }

        $currentRow += 2;

        // Teacher Performance Summary (across all classes)
        $worksheet->setCellValue("A{$currentRow}", "👨‍🏫 TEACHER PERFORMANCE SUMMARY - REVISION (All Classes)");
        $worksheet->mergeCells("A{$currentRow}:R{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '1565C0']]
        ]);
        $currentRow++;

        if (count($teacherPerformance) > 0) {
        $teacherHeaders = ['Class', 'Class Name', 'Teacher', 'Students', 'Sessions (Actual/Target)', 'Avg Score', 'Attendance %', 'Pages Revised (Actual/Target)', 'Performance'];
            foreach ($teacherHeaders as $index => $header) {
                $col = chr(65 + $index);
                $worksheet->setCellValue("{$col}{$currentRow}", $header);
            }
            $currentRow++;

            // Get year and month from filters for calculations
            $year = $this->filters['year'] ?? null;
            $month = $this->filters['month'] ?? null;

            // Build class id -> name map using translatable model
            $classIdList = array_values(array_unique(array_map(function($t){ return (int)$t->class_id; }, $teacherPerformance)));
            $classNameMap = collect(\App\Classes::whereIn('id', $classIdList)->get())->keyBy('id');

            foreach ($teacherPerformance as $teacher) {
                $performance = '✅ Excellent';
                if ($teacher->avg_evaluation < 0.7 || $teacher->class_attendance_rate < 75) {
                    $performance = '⚠️ Needs Support';
                }
                if ($teacher->avg_evaluation < 0.5 || $teacher->class_attendance_rate < 60) {
                    $performance = '🔴 Requires Training';
                }

                // Calculate target sessions and pages planned
                $targetSessions = $this->calculateTargetSessions((int)$teacher->class_id, $year, $month);
                $pagesPlanned = $this->calculatePagesPlanned((int)$teacher->class_id, $year, $month);

                // Split teacher names for display and comment
                $teacherNameData = $this->splitTeacherNames($teacher->teacher_name);
                
                // Calculate pages taught based on actual reports and plans
                $pagesTaught = $this->calculateTeacherPagesTaught((int)$teacher->class_id, $year, $month);

                $className = optional($classNameMap->get((int)$teacher->class_id))->name ?? 'N/A';

                $worksheet->setCellValue("A{$currentRow}", $teacher->class_code);
                $worksheet->setCellValue("B{$currentRow}", $className);
                $worksheet->setCellValue("C{$currentRow}", $teacherNameData['display']);
                $worksheet->setCellValue("D{$currentRow}", $teacher->unique_students);
                $worksheet->setCellValue("E{$currentRow}", $teacher->actual_sessions . '/' . $targetSessions);
                $worksheet->setCellValue("F{$currentRow}", round((float)($teacher->avg_evaluation ?? 0) * 100, 1) . '%');
                $worksheet->setCellValue("G{$currentRow}", $teacher->class_attendance_rate . '%');
                $worksheet->setCellValue("H{$currentRow}", $pagesTaught . '/' . $pagesPlanned);
                $worksheet->setCellValue("I{$currentRow}", $performance);
                
                // Add comment for additional teachers if needed
                if ($teacherNameData['comment']) {
                    $worksheet->getComment("C{$currentRow}")->getText()->createTextRun($teacherNameData['comment']);
                }
                $currentRow++;
            }
        } else {
            $worksheet->setCellValue("A{$currentRow}", "No teacher performance data available for revision");
            $currentRow++;
        }

        return $currentRow + 2;
    }

    /**
     * Get the sheet title
     */
    public function title(): string
    {
        return 'Revision Reports';
    }

    /**
     * Configure sheet events
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $worksheet = $event->sheet->getDelegate();
                $this->populateSheet($worksheet);
            },
        ];
    }

    /**
     * Populate the sheet with data
     */
    private function populateSheet(Worksheet $worksheet): void
    {
        // Get analytics data first
        $analytics = $this->getAnalyticsData();
        
        // Create performance dashboard at the top
        $currentRow = $this->createPerformanceDashboard($worksheet, 1, $analytics);
        $currentRow += 2;

        // Get revision reports data
        $reports = $this->getRevisionReports();
        
        if ($reports->isEmpty()) {
            $worksheet->setCellValue("A{$currentRow}", "No revision reports found for the selected criteria.");
            return;
        }

        // Headers - reordered with Student ID and Student Name first
        $headers = [
            'Student ID',           // A
            'Student Name',         // B  
            'Date',                 // C
            'Day',                  // D - New column
            'Centre ID',            // E
            'Centre Name',          // F
            'Class ID',             // G
            'Class Name',           // H (new)
            'Class Program',        // I
            'Teacher Name',         // J
            'Is Day in Timetable',  // K
            'Attendance',           // L
            'Attendance Summary',   // M
            'From Surah',           // N
            'From Verse',           // O
            'To Surah',             // P
            'To Verse',             // Q
            'No of Pages',          // R
            'Evaluation',           // S
            'Evaluation Note'       // T
        ];

        // Set headers
        foreach ($headers as $index => $header) {
            $col = chr(65 + $index);
            $worksheet->setCellValue("{$col}{$currentRow}", $header);
        }

        // Style headers
        $headerRange = "A{$currentRow}:T{$currentRow}";
        $worksheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '7B1FA2']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $currentRow++;
        $dataStartRow = $currentRow;

        // Iterate reports directly – no grouping, repeat Student ID & Name each day
        foreach ($reports as $report) {
            // Use pre-calculated pages from report array (calculated during data preparation)
            $calculatedPages = $report['no_of_pages'] ?? 0;
            
                // Populate data with reordered columns
                $worksheet->setCellValue("A{$currentRow}", $report['student_id']);
                $worksheet->setCellValue("B{$currentRow}", $report['student_name']);
                $worksheet->setCellValue("C{$currentRow}", $report['date']);
                $worksheet->setCellValue("D{$currentRow}", $report['day']);
                $worksheet->setCellValue("E{$currentRow}", $report['centre_id']);
                $worksheet->setCellValue("F{$currentRow}", $report['centre_name']);
                $worksheet->setCellValue("G{$currentRow}", $report['class_id']);
                $worksheet->setCellValue("H{$currentRow}", $report['class_name'] ?? '');
                $worksheet->setCellValue("I{$currentRow}", $report['class_program']);
                $worksheet->setCellValue("J{$currentRow}", $report['teacher_name']);
                $worksheet->setCellValue("K{$currentRow}", $report['is_day_in_timetable']);
                $worksheet->setCellValue("L{$currentRow}", $report['attendance']);
                $worksheet->setCellValue("M{$currentRow}", $report['attendance_summary']);
                $worksheet->setCellValue("N{$currentRow}", $report['from_surah']);
                $worksheet->setCellValue("O{$currentRow}", $report['from_verse']);
                $worksheet->setCellValue("P{$currentRow}", $report['to_surah']);
                $worksheet->setCellValue("Q{$currentRow}", $report['to_verse']);
            $worksheet->setCellValue("R{$currentRow}", $calculatedPages); // Use calculated pages
                $worksheet->setCellValue("S{$currentRow}", $report['evaluation']);
                $worksheet->setCellValue("T{$currentRow}", $report['evaluation_note']);
            
            // Add light grey background for non-timetable days
            if (!$report['is_day_in_timetable']) {
                $worksheet->getStyle("A{$currentRow}:T{$currentRow}")
                         ->getFill()
                         ->setFillType(Fill::FILL_SOLID)
                         ->getStartColor()
                         ->setRGB('EFEFEF');
            }
                
                $currentRow++;
            }
            

        // Apply borders to data range
        $dataEndRow = $currentRow - 1;
        $dataRange = "A{$dataStartRow}:T{$dataEndRow}";
        $worksheet->getStyle($dataRange)->applyFromArray([
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        // Auto-size columns
        foreach (range('A', 'T') as $col) {
            $worksheet->getColumnDimension($col)->setAutoSize(true);
        }
    }

    /**
     * Configure sheet styles
     */
    public function styles(Worksheet $sheet): array
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 14]],
        ];
    }
}
<?php

namespace Modules\Education\Http\Controllers;

use App\Exam;
use App\GeneratedStudentCertificate;
use App\OnlineExam;
use App\Student;
use App\Center;
use App\Classes;
use App\Program;
use App\ProgramLevel;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class ExamController extends Controller
{
    public function __construct()
	{
        // User::checkAuth();
	}


    public function index(Request $request)
    {
        $exams = OnlineExam::query();
        if($request->has('center')){
            $exams->where('center_id', $request->center);
        }
        if($request->has('class')){
            $exams->where('class_id', $request->class);
        }
        if($request->has('program')){
            $exams->where('program_id', $request->program);
        }
        if($request->has('level')){
            $exams->where('level_id', $request->level);
        }
        if($request->has('status')){
            $exams->where('status', $request->status);
        }
        if($request->has('start_date') && $request->has('end_date')){
            $exams->whereBetween('date', [$request->start_date, $request->end_date]);
        }
        $exams = $exams->get();
        $centers = Center::all();
        $classes = Classes::all();
        $programs = Program::where('status', 'active')->get();

        $levels = ProgramLevel::all();
        return view('education::examList.list', compact('exams','centers','classes','programs','levels'));
    }

    public function data(Request $request)
    {

        $exams = OnlineExam::with(['program','program.levels','class.center','studentInfo.programLevel']);
        if (request()->filled('selectedProgramClass')) {
            $exams->whereHas('class', function($query) use ($request) {

                    $query->where('id', $request->selectedProgramClass);
            });
        }
        if (request()->filled('selectedDatefilter')) {
            $dateSeperator = explode(",", $request->selectedDatefilter);

            $exams->whereDateBetween('date', $dateSeperator[0],$dateSeperator[1]);

        }

        if (request()->filled('selectedResult')) {
            $exams->where('result', $request->selectedResult);
        }


        if (request()->filled('student_id')) {
            $exams->whereHas('studentInfo', function($query) use ($request) {
                if($request->student_id) {
                    $query->where('id', $request->student_id);
                }
            });
        }
        if (request()->filled('selectedProgram')) {
            $exams->whereHas("program", function ($q) use ($request) {
                $q->where('id', $request->selectedProgram);
            });
        }
        if (request()->filled('selectedProgramCenter')) {
            $exams->whereHas("class.center", function ($q) use ($request) {
                $q->where('id', $request->selectedProgramCenter);
            });
        }
        if (request()->filled('selectedProgramLevel')) {
            $exams->whereHas("studentInfo", function ($q) use ($request) {
                $q->where('level', $request->selectedProgramLevel);
            });
        }







        return \DataTables::of($exams->orderBy('date','desc')->get())
            ->addColumn('program', function($exam) {

                return $exam->program->title;
            })->addColumn('student_name', function($exam) {
                return $exam->studentInfo->full_name;
            })->addColumn('center', function($exam) {


                return optional($exam->class->center)->name ?? null;
            })
            ->addColumn('level', function($exam) {
                return $exam->studentInfo->programLevel->title;
            })
            ->addColumn('class', function($exam) {
                return $exam->class->name;
            })

            ->addColumn('exam_date', function($exam) {
                return $exam->date;
            })
            ->addColumn('examiner', function($exam) {
                return $exam->examiner->full_name;
            })
            ->addColumn('status', function($exam) {
                return $exam->result;
            })
            ->addColumn('action', function($exam) {

                if($exam->count()> 0)
                {




                return  '<div class="ui compact menu">
 <a target="_blank" class="item" href="'.route('online-exam-edit',['id' => $exam->id,'classId' =>$exam->class_id ]).'">
    <i class="pencil mail"></i> Edit
  </a>
</div>';

                }
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    public function update(Request $request, $id)
    {
        $exam = Exam::find($id);
        $exam->fill($request->all());
        $exam->save();
        if ($exam->status == 'pass' && !$exam->certificate) {
            $onlineExam = new Certificate();
            $onlineExam->student_id = $exam->student_id;
            $onlineExam->level = $exam->level;
            $onlineExam->score = $exam->score;
            $onlineExam->grade = $exam->grade;
            $onlineExam->date = $exam->exam_date;
            $onlineExam->serial_number = self::generateSerialNumber($exam->student_id, $exam->level);
            $onlineExam->save();
        }
        return redirect()->back()->with('message', 'Exam details updated successfully');
    }

    public function generateSerialNumber($student_id, $level)
    {
        $student = Student::find($student_id);



















    }
    }
@extends('layouts.hound') 
@section('content')
<!-- Row -->
<div class="row">
    <div class="container">
        <div class="panel panel-default card-view">
            <div class="panel-wrapper collapse in">
                <div class="panel-body">
                   <?php $user_role=auth()->user()->roles->first()->name?>
                    <div class="panel panel-info">
                          <div class="panel-heading">
                                <h3 class="panel-title txt-light">Submit a new request</h3>
                          </div>
                              <div>
                                  @foreach($form_builders as $builder)
                                  <?php $roles=App\FormBuilder::where('id', $builder->id)->pluck('target')->toArray();?> 

                         <!-- check if the role exist -->
                        @foreach ($roles  as $key => $value)
                      
                         <?php 
               
                             if ($value[0] == $user_role)
                                   $role_exist=1;
                             else
                                    $role_exist=0;
                                
                         ?>
                         @endforeach
               
                            @if( $role_exist==1)
                                        <a href="{{ route('general.form.create' , $builder->id) }}" class="btn btn-primary">
                                          <i class="fa fa-paperclip" aria-hidden="true"></i> {{ $builder->title }}
                                        </a>
                                    @endif  
                                  @endforeach
                              </div>
                          </div>
                    </div>
                    @if($forms_waiting_action->count())
                    <div class="panel panel-danger">
                        <div class="panel-heading">
                                <h3 class="panel-title txt-light">Requests Need My Review</h3>
                        </div>
                        <div class="panel-body pa-20">
                            
                                <div class="table-responsive">
                                        <table class="table table-hover table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Request Type</th>
                                                    <th>Request By</th>
                                                    <th>Created on</th>
                                                    <th>Status</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($forms_waiting_action as $form)
                                               <?php $role_name=App\FormBuilderApprovalFlow::where('form_builder_id', $form->id)->pluck('role')->first()?> 
                                           @if($user_role== $role_name)
                                                <tr>
                                                    <td>{{ $form->type->title }}</td>
                                                    <td>{{ $form->type->title }}</td>
                                                    <td>{{ $form->created_at->format('d-M-Y  h:iA')}}  </td>
                                                    <td>{{ $form->reviews->unique('step_order')->count() }} of {{ $form->type->approvalFlow->count() }} <span class="label label-info">{{ $form->status}}</span></td>
                                                    <td> <a href="{{ route('general.form.view' , $form->id)}}" class="btn btn-xs btn-primary">View</a>  </td>
                                                </tr>
                                                @endif
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                        </div>
                    </div>
                    @endif

                    @if($user_forms->count())
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                                <h3 class="panel-title txt-light">My Requests</h3>
                        </div>
                        <div class="panel-body pa-20">
                            
                                <div class="table-responsive">
                                    <table class="table table-hover table-striped">
                                        <thead>
                                            <tr>
                                                <th>Request Type</th>
                                                <th>Created on</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user_forms as $form)
                                            <tr>
                                                <td>{{ $form->type->title }}</td>
                                                <td>{{ $form->created_at->format('d-M-Y  h:iA')}}  </td>
                                                <td>{{ $form->reviews->unique('step_order')->count() }} of {{ $form->type->approvalFlow->count() }} {{ $form->status}}</td>
                                                <td> <a href="{{ route('general.form.view' , $form->id)}}" class="btn btn-xs btn-primary">View</a>  </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                
                        </div>
                    </div>
                    @endif

                </div>
            </div>
        </div>
    </div>
</div>
<!-- /Row -->

@endsection

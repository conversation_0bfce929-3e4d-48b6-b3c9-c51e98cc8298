<?php

namespace Modules\Activities\Providers;


use Illuminate\Support\ServiceProvider;

class ActivitiesServiceProvider extends ServiceProvider
{
    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = false;

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            __DIR__.'/../Config/config.php' => config_path('activities.php'),
        ], 'config');
        $this->mergeConfigFrom(
            __DIR__.'/../Config/config.php', 'activities'
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = base_path('resources/views/modules/activities');

        $sourcePath = __DIR__.'/../Resources/views';

        $this->publishes([
            $sourcePath => $viewPath
        ]);

        /**
         * This code is from a Laravel ServiceProvider and it is used to register the location of the view files for the `activities` module. The `loadViewsFrom` method takes two arguments: an array of paths where the view files are located and the namespace for these views.

        The first argument is created by merging two arrays. The first array is created by calling `array_map` on the result of `\Config::get('view.paths')`. This returns an array of paths where Laravel looks for view files. The `array_map` function applies a callback function to each element of this array. The callback function takes a `$path` argument and returns a new path by appending `/modules/activities` to it. This means that for each path in the `view.paths` configuration, a new path is created that points to the `activities` module within the `modules` directory.

        The second array that is merged with the first one contains only one element: `$sourcePath`. This variable presumably contains the path to the `activities` module's views.

        The second argument to the `loadViewsFrom` method is `'activities'`, which specifies the namespace for these views. This means that when referencing these views in your code, you can use the `activities::viewname` syntax.

         */
        $this->loadViewsFrom(array_merge(array_map(function ($path) {
            return $path . '/modules/activities';
        }, \Config::get('view.paths')), [$sourcePath]), 'activities');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = base_path('resources/lang/modules/activities');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'activities');
        } else {
            $this->loadTranslationsFrom(__DIR__ .'/../Resources/lang', 'activities');
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}

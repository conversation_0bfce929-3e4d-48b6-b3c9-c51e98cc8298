<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class LessonReport extends Model
{
    public function student()
    {
        return $this->belongsTo('App\Student');
    }


    public function content()
    {
        return $this->belongsTo('App\Content', 'lesson_id', 'id');
    }
    public function evaluations()
    {
        return $this->hasMany('App\LessonReportEvaluation');
    }
}

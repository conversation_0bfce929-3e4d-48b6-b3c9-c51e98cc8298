<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * NotificationProcessLogger provides comprehensive logging for the job notification process.
 * Stores detailed step-by-step execution traces in the database for debugging and performance monitoring.
 *
 * Purpose: Track every step of notification processing to quickly identify issues in the future.
 * Side effects: Writes to notification_process_logs table, logs context data.
 * Performance: Lightweight async logging, minimal impact on notification processing.
 * Dependencies: Database connection, Carbon for timestamps.
 */
final class NotificationProcessLogger
{
    private string $traceId;
    private ?int $executionId;

    public function __construct(string $traceId, ?int $executionId = null)
    {
        $this->traceId = $traceId;
        $this->executionId = $executionId;
    }

    /**
     * Log the start of a new process step with timing.
     *
     * @param string $stepName Descriptive name of the step (e.g., 'job_fetch', 'category_mapping')
     * @param string $stepType Type of step: start, process, decision, action, error, complete
     * @param string|null $provider Provider name (jobs.af, ACBAR, ReliefWeb) if applicable
     * @param array $context Additional context data to store
     * @return int The log entry ID for updating later
     */
    public function logStepStart(
        string $stepName, 
        string $stepType = 'process', 
        ?string $provider = null, 
        array $context = []
    ): ?int {
        $now = Carbon::now();

        try {
            // Safely encode context JSON with error handling
            $contextJson = null;
            if (!empty($context)) {
                try {
                    $contextJson = json_encode($context, JSON_THROW_ON_ERROR);
                } catch (\JsonException $e) {
                    $contextJson = json_encode([
                        'encoding_error' => 'Failed to encode context',
                        'original_type' => gettype($context),
                        'error_message' => $e->getMessage()
                    ]);
                }
            }

            $logId = DB::table('notification_process_logs')->insertGetId([
                'trace_id' => $this->traceId,
                'execution_id' => $this->executionId,
                'step_name' => $stepName,
                'step_type' => $stepType,
                'provider' => $provider,
                'status' => 'running',
                'message' => "Started: {$stepName}",
                'context' => $contextJson,
                'timing_start' => $now,
                'created_at' => $now,
                'updated_at' => $now
            ]);

            Log::debug('NotificationProcessLogger: Step started', [
                'log_id' => $logId,
                'step_name' => $stepName,
                'trace_id' => $this->traceId
            ]);

            return $logId;
        } catch (\Throwable $e) {
            Log::error('NotificationProcessLogger: Failed to log step start', [
                'step_name' => $stepName,
                'error' => $e->getMessage(),
                'trace_id' => $this->traceId
            ]);
            return null;
        }
    }

    /**
     * Complete a logged step with success status and duration.
     *
     * @param int $logId Log entry ID returned from logStepStart
     * @param string $status Final status: success, warning, error, skipped
     * @param string|null $message Optional completion message
     * @param array $context Additional context data to merge
     */
    public function logStepComplete(
        ?int $logId, 
        string $status = 'success', 
        ?string $message = null, 
        array $context = []
    ): void {
        if ($logId === null) return; // Skip if logging failed initially

        try {
            $now = Carbon::now();
            
            // Calculate duration if timing exists
            $updateData = [
                'status' => $status,
                'timing_end' => $now,
                'updated_at' => $now
            ];

            if ($message) {
                $updateData['message'] = $message;
            }

            if (!empty($context)) {
                // Safely merge with existing context in a single DB read operation
                $existing = DB::table('notification_process_logs')
                    ->where('id', $logId)
                    ->value('context');
                
                // Safely decode existing context
                $existingContext = [];
                if ($existing) {
                    try {
                        $existingContext = json_decode($existing, true, 512, JSON_THROW_ON_ERROR) ?? [];
                    } catch (\JsonException $e) {
                        Log::warning('NotificationProcessLogger: Failed to decode existing context', [
                            'log_id' => $logId,
                            'error' => $e->getMessage()
                        ]);
                        $existingContext = [];
                    }
                }
                
                // Merge contexts and safely re-encode
                $mergedContext = array_merge($existingContext, $context);
                try {
                    $updateData['context'] = json_encode($mergedContext, JSON_THROW_ON_ERROR);
                } catch (\JsonException $e) {
                    Log::error('NotificationProcessLogger: Failed to encode merged context', [
                        'log_id' => $logId,
                        'error' => $e->getMessage()
                    ]);
                    $updateData['context'] = json_encode([
                        'encoding_error' => 'Failed to encode merged context',
                        'original_context_type' => gettype($existingContext),
                        'new_context_type' => gettype($context)
                    ]);
                }
            }

            // Calculate duration from timing_start
            $startTime = DB::table('notification_process_logs')
                ->where('id', $logId)
                ->value('timing_start');
            if ($startTime) {
                $duration = $now->diffInMilliseconds(Carbon::parse($startTime));
                $updateData['duration_ms'] = $duration;
            }

            DB::table('notification_process_logs')
                ->where('id', $logId)
                ->update($updateData);

            Log::debug('NotificationProcessLogger: Step completed', [
                'log_id' => $logId,
                'status' => $status,
                'duration_ms' => $updateData['duration_ms'] ?? null,
                'trace_id' => $this->traceId
            ]);
        } catch (\Throwable $e) {
            Log::error('NotificationProcessLogger: Failed to complete step log', [
                'log_id' => $logId,
                'error' => $e->getMessage(),
                'trace_id' => $this->traceId
            ]);
        }
    }

    /**
     * Log a quick step without separate start/complete calls.
     *
     * @param string $stepName Descriptive name of the step
     * @param string $status Status: success, warning, error, skipped
     * @param string $stepType Type of step
     * @param string|null $provider Provider name if applicable
     * @param string|null $message Log message
     * @param array $context Context data
     */
    public function logStep(
        string $stepName,
        string $status = 'success',
        string $stepType = 'action',
        ?string $provider = null,
        ?string $message = null,
        array $context = []
    ): void {
        try {
            $now = Carbon::now();
            
            // Safely encode context JSON with error handling
            $contextJson = null;
            if (!empty($context)) {
                try {
                    $contextJson = json_encode($context, JSON_THROW_ON_ERROR);
                } catch (\JsonException $e) {
                    $contextJson = json_encode([
                        'encoding_error' => 'Failed to encode context',
                        'original_type' => gettype($context),
                        'error_message' => $e->getMessage()
                    ]);
                }
            }

            DB::table('notification_process_logs')->insert([
                'trace_id' => $this->traceId,
                'execution_id' => $this->executionId,
                'step_name' => $stepName,
                'step_type' => $stepType,
                'provider' => $provider,
                'status' => $status,
                'message' => $message ?? $stepName,
                'context' => $contextJson,
                'timing_start' => $now,
                'timing_end' => $now,
                'duration_ms' => 0,
                'created_at' => $now,
                'updated_at' => $now
            ]);

            Log::debug('NotificationProcessLogger: Quick step logged', [
                'step_name' => $stepName,
                'status' => $status,
                'trace_id' => $this->traceId
            ]);
        } catch (\Throwable $e) {
            Log::error('NotificationProcessLogger: Failed to log quick step', [
                'step_name' => $stepName,
                'error' => $e->getMessage(),
                'trace_id' => $this->traceId
            ]);
        }
    }

    /**
     * Get all logs for the current trace ID, ordered by creation time.
     *
     * @return array Array of log entries
     */
    public function getTraceLog(): array
    {
        try {
            return DB::table('notification_process_logs')
                ->where('trace_id', $this->traceId)
                ->orderBy('created_at')
                ->get()
                ->toArray();
        } catch (\Throwable $e) {
            Log::error('NotificationProcessLogger: Failed to retrieve trace log', [
                'trace_id' => $this->traceId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Generate a summary of the current trace for quick debugging.
     *
     * @return array Summary with step counts, timing, and any errors
     */
    public function getTraceSummary(): array
    {
        try {
            $logs = $this->getTraceLog();
            
            $summary = [
                'trace_id' => $this->traceId,
                'execution_id' => $this->executionId,
                'total_steps' => count($logs),
                'status_counts' => [],
                'total_duration_ms' => 0,
                'errors' => [],
                'start_time' => null,
                'end_time' => null
            ];

            foreach ($logs as $log) {
                // Skip null log entries
                if ($log === null) {
                    continue;
                }
                
                // Count statuses with null coalescing
                $status = $log->status ?? 'unknown';
                $summary['status_counts'][$status] = ($summary['status_counts'][$status] ?? 0) + 1;
                
                // Sum durations with safe casting
                $duration = (int) ($log->duration_ms ?? 0);
                if ($duration > 0) {
                    $summary['total_duration_ms'] += $duration;
                }
                
                // Collect errors only when required fields exist
                if ($status === 'error' && isset($log->step_name) && isset($log->message)) {
                    $summary['errors'][] = [
                        'step' => $log->step_name,
                        'message' => $log->message,
                        'time' => $log->created_at ?? 'unknown'
                    ];
                }
                
                // Track timing with null safety
                if (isset($log->created_at)) {
                    if (!$summary['start_time'] || $log->created_at < $summary['start_time']) {
                        $summary['start_time'] = $log->created_at;
                    }
                    if (!$summary['end_time'] || $log->created_at > $summary['end_time']) {
                        $summary['end_time'] = $log->created_at;
                    }
                }
            }

            return $summary;
        } catch (\Throwable $e) {
            Log::error('NotificationProcessLogger: Failed to generate trace summary', [
                'trace_id' => $this->traceId,
                'error' => $e->getMessage()
            ]);
            return ['error' => $e->getMessage()];
        }
    }
}

<?php

namespace Modules\Admission\Http\Controllers;

use App\BaseSetup;
use App\Center;
use App\CenterEmployee;
use App\CenterTranslation;
use App\ClassStudent;
use App\Country;
use App\EmailSetting;
use App\GeneralSettings;
use App\Notifications\SuperiorResetPassword;
use App\Notifications\UserStatusChangedToNewApplication;
use App\Organization;
use App\Program;
use App\Scopes\OrganizationScope;
use App\StudentAdmissionHefzPlan;
use App\StudentHefzReport;
use App\StudentRevisionPlan;
use App\StudentRevisionReport;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Faker\Factory;
use Faker\Provider\en_UG\PhoneNumber;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Mail\StudentCreated;
use App\Student;
use App\Guardian;
use App\Employee;
use App\Role;
use App\Cen_Emp;
use App\Admission;
use App\Classes;
use App\StudentHefzPlan;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Mail;
use Modules\Education\Http\Requests\StudentRegistrationByEmployeeRequest;
use PhpParser\Node\Stmt\Foreach_;
use Yajra\DataTables\Facades\DataTables;

class StoreStudentByEmployeeController extends Controller
{

    public function __invoke(StudentRegistrationByEmployeeRequest $request, $roleName = null)
    {


        DB::beginTransaction();
        try {


            event(new Registered($user = $this->create($request->all(), $roleName)));

            DB::commit();

            return redirect()->route('students.show', $user->id);
        } catch (\Exception $e) {
            DB::rollback();
            dd($e->getMessage());
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }


//        Mail::to($student)->send(new StudentCreated($request->all()));

        return redirect()->route('students.show', $student);
    }

    private function validation($request)
    {
        if (config("settings.student_form_full_name") == "required") {
            $roles["full_name"] = "required";
        }
        if (config("settings.student_form_full_name_trans") == "required") {
            $roles["full_name_trans"] = "required";
        }
        if (config("settings.student_form_full_name_language") == "required") {
            $roles["full_name_language"] = "required";
        }
        if (config("settings.student_form_gender") == "required") {
            $roles["gender"] = "required";
        }
        if (config("settings.student_form_date_of_birth") == "required") {
            $roles["date_of_birth"] = "required";
        }
        if (config("settings.student_form_identity_number") == "required") {
            $roles["identity_number"] = "required";
        }
        if (config("settings.student_form_identity") == "required") {
            $roles["identity"] = "required| mimes:jpeg,jpg,bmp,png,gif,svg,pdf,zip | max:5000";
        }
        if (config("settings.student_form_image") == "required") {
//            $roles["image"] = "required|image| max:3000";
            $roles["image"] = "sometimes|required| max:3000";
        }
        if (config("settings.student_form_nationality") == "required") {
            $roles["nationality"] = "required";
        }
        if (config("settings.student_form_mobile") == "required") {
            $roles["mobile"] = "required";
        }
        if ($roles) {
            $this->validate($request, $roles);
        }
    }

    public function generateUserName($name)
    {
        $username = Str::lower(Str::slug($name));
        if (User::where('username', '=', $username)->exists()) {
            $uniqueUserName = $username . '-' . Str::lower(Str::random(4));
            $username = $this->generateUserName($uniqueUserName);
        }
        return $username;
    }


    function create(array $data, $roleName = null)
    {
        $shouldSendVerification = !empty($data['send_verification_email']) && $data['send_verification_email'] == '1';



        $user = User::create([
            'email' => $data['email'],
            'gender' => $data['gender'],
            'display_name' => $data['displayname'],
            'username' => $this->generateUserName($data['displayname']),
            'full_name' => $data['full_name'],
            'email_verified_at' => $shouldSendVerification ? null : Carbon::now(), // Handle email verification status
            'nationality' => $data['nationality'],
            'access_status' => '0',
            'is_administrator' => 'no',
            'organization_id' => config('organization_id'),
            'password' => bcrypt($data['password']),
        ]);
        $this->studentStore($data, $user);

        // Conditionally send the verification email based on the form input
        if ($shouldSendVerification) {
            event(new Verified($user));
        }


//        $user->sendEmailVerificationNotification();
        // assign default role
        $this->assignDefaultRoles($user, $roleName);

        return $user;

    }

    public function studentStore($request, $user)
    {


        DB::beginTransaction();
        try {

            $document_file_1 = "";
            if ($request['document_file_1'] != "") {
                $file = $request['document_file_1'];

                $document_file_1 = 'doc1-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/student/document/', $document_file_1);
                $document_file_1 = 'public/uploads/student/document/' . $document_file_1;
            }

            $document_file_2 = "";
            if ($request['document_file_2'] != "") {
                $file = $request['document_file_2'];
                $document_file_2 = 'doc2-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/student/document/', $document_file_2);
                $document_file_2 = 'public/uploads/student/document/' . $document_file_2;
            }

            $document_file_3 = "";
            if ($request['document_file_3'] != "") {
                $file = $request['document_file_3'];
                $document_file_3 = 'doc3-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/student/document/', $document_file_3);
                $document_file_3 = 'public/uploads/student/document/' . $document_file_3;
            }

            $document_file_4 = "";
            if ($request['document_file_4'] != "") {
                $file = $request['document_file_4'];
                $document_file_4 = 'doc4-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/student/document/', $document_file_4);
                $document_file_4 = 'public/uploads/student/document/' . $document_file_4;
            }


            if ($request['identity'] != "") {
                $identityFile = $request['identity'];

                // Sanitize the original file name
                $originalName = pathinfo($request['identity']->getClientOriginalName(), PATHINFO_FILENAME);
                $sanitizedOriginalName = preg_replace('/[^A-Za-z0-9_\-]/', '_', $originalName);

                $identity_file = 'identity-' . md5($sanitizedOriginalName . '-' . time()) . "." . $request['identity']->getClientOriginalExtension();

                $identityFile->move(public_path('uploads/student/document/'), $identity_file);
                $identity_file = 'public/uploads/student/document/' . $identity_file;

            }


            $user_stu = $user;

            try {


                if (isset($request['date_of_birth'])) {
                    $dob = Carbon::parse($request['date_of_birth'])->format('Y-m-d');
                }

                $student = new Student();
                $student->user_id = $user_stu->id;
                // student number format ( two digits of year+two digits of month+two digits of  hour+two digits of minutes+two digits of  seconds
                $student->student_number = Carbon::now()->format('y') . Carbon::now()->format('n') . Carbon::now()->format('H') . Carbon::now()->format('i') . Carbon::now()->format('s');
                $student->display_name = $user_stu->display_name;
                $student->full_name = $request['full_name'];
                $student->full_name_trans = $user_stu->full_name_trans;
                $student->identity_number = strtoupper($request['identity_number']);
                $student->nationality = $user_stu->nationality;
                $student->gender = BaseSetup::where('base_setup_name',strtolower($request['gender']))->first()->base_setup_name;
                $student->date_of_birth = $dob;
                $student->email = $user_stu->email;
                $student->mobile = $request['mobile'];
                $student->organization_id = config('organization_id');
                $student->status = 'new_admission';
                $student->active_status = '0';




                if (isset($request['image'])) {
                    $image = $request['image'];
                    $imageName = time().'.'.$image->getClientOriginalExtension(); // Create a unique name for the image
                    // You can specify a different disk or directory as per your configuration
                    $destinationPath = public_path('/images/student');
                    $image->move($destinationPath, $imageName); // Save the image to the server

                    $student->student_photo = '/images/student/'.$imageName; // Save the image path in the student record


                }


                $student->document_title_1 = $request['document_title_1'];
                if ($document_file_1 != "") {
                    $student->document_file_1 = $document_file_1;
                }

                $student->document_title_2 = $request['document_title_2'];
                if ($document_file_2 != "") {
                    $student->document_file_2 = $document_file_2;
                }

                $student->document_title_3 = $request['document_title_3'];
                if ($document_file_3 != "") {
                    $student->document_file_3 = $document_file_3;
                }

                $student->document_title_4 = $request['document_title_4'];

                if ($document_file_4 != "") {
                    $student->document_file_4 = $document_file_4;
                }
                if ($identity_file != "") {
                    $student->identity_file = $identity_file;
                }
                $student->save();
                $student->joint_classes()->attach($request['classes'],['start_date' => date('Y-m-d')]);

                $student->toArray();
                $studentPhotoUrl = $student->image ? asset($student->image) : asset('avatar.jpg');
                try {
                    $admission = new Admission();
                    $admission->program_id = $request['program'];
                    $admission->center_id = $request['center'];
                    $admission->class_id = $request['classes'];
                    $admission->gender_id = BaseSetup::where('base_setup_name',strtolower($request['gender']))->first()->id;
                    $admission->creator_role = 'Employee: ' . auth()->user()->full_name.' [emp_id => '.auth()->user()->id. ' ]';
                    $admission->created_by = Auth::user()->id;
                    $admission->status = 'new_admission';
                    $admission->student_id = $student->id;
                    $admission->student_email = $user_stu->email;
                    $admission->student_mobile = $request['mobile'];
                    $admission->date_of_birth = $dob;
                    $admission->age = \Carbon\Carbon::parse($request['date_of_birth'])->age;
                    $admission->organization_id = config('organization_id');
                    $admission->save();
                    $admission->programs()->attach($request['program'],['created_at' => Carbon::now()]);

                    try {
                        $user_info = [];
                        $user_info[] = array('email' => $user_stu->email, 'id' => $student->id, 'slug' => 'student');
                        DB::commit();
                        Session::put('student_photo', '');
                        try {
                            $user_info[0]["applicationConfirmationTime"] = $admission->updated_at->format('m/d/Y g:i A');
                            $user_info[0]["student_id"] = $admission->student_id;
                            $user_info[0]['studentName'] = $student->full_name;
                            $user_info[0]['studentNationality'] = $student->nationality;
                            $user_info[0]['programName'] = Program::find($request['program'])->title;
                            $user_info[0]['CenterName'] = Center::find($request['center'])->name;
                            $user_info[0]['ClassName'] = Classes::find($request['classes'])->name;
                            $user_info[0]['username'] = $user_stu->username;
                            $user_info[0]['studentId'] = $student->id;
                            $user_info[0]['studentEmail'] = $student->email;
                            $user_info[0]['refNo'] = $admission->id;
                            $user_info[0]['studentPhotoUrl'] = $studentPhotoUrl;


                            if (count($user_info) != 0) {
                                $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);
                                $systemEmail = EmailSetting::find(1);
                                $system_email = $systemEmail->from_email;
                                $organization_name = $systemSetting->organization_name;
                                $sender['system_email'] = $system_email;
                                $sender['organization_name'] = $organization_name;

                                // TODO : transfer this logic to the place where the offered status is given to the student; after status changed to offer, send login details to the student and guardian
//                                dispatch(new \App\Jobs\SendUserMailJob($user_info, $sender));
                                $data = [];
                                $data['programName'] = Program::find($request['program'])->title;
                                $data['CenterName'] = Center::find($request['center'])->name;
                                $data['ClassName'] = Classes::find($request['classes'])->name;
                                $data['applicationDate'] = $admission->created_at;
                                $data['refNo'] = $admission->id;
                                // storing the user application letter confirmation in the storage
                                $pdf = \App::make('dompdf.wrapper');
                                $pdf->loadHTML(view('modules.site.templates.wajeha.backEnd.studentInformation.student_application_confirmation', compact('data'))->render());
                                $content = $pdf->download()->getOriginalContent();
                                \Storage::put("applicationLetters/" . $admission->student_id . "-applicationLetter.pdf", $content);


                                // TODO: send a confirmation stating that Itqan received your application
                                dispatch(new \App\Jobs\SendApplicationConfirmationEmailJob($user_info[0], $sender));


                                $supervisorEmail = Employee::whereIn('id', CenterEmployee::where('cen_id', $request->center)->pluck('emp_id')->toArray())->pluck('email');

                                // send the notification to the related center supervisor
                                dispatch(new \App\Jobs\SendNewApplicationNotificationToSupervisor($user_info, $sender, $supervisorEmail));


                            }
                        } catch (\Exception $e) {
                            DB::rollback();
                            Log::alert($e->getMessage());


                            Toastr::warning('Operation Failed', 'Failed');
                            return redirect('student-list');
                        }
//                        Session::put('fathers_photo', '');

                        session()->remove('program_id');
                        session()->remove('center_id');
                        session()->remove('class_id');

//                        dd(url("student-list"));
                        Toastr::success('Operation successful', 'Success');
                        return redirect(route("application.list"));
                    } catch (\Exception $e) {
                        DB::rollback();
                        Log::alert($e->getMessage());
                        dd($e->getMessage());
                        Toastr::error('Operation Failed', 'Failed');
                        return redirect()->back();
                    }
                } catch (\Exception $e) {
                    DB::rollback();
                    Log::alert($e->getMessage());
                    dd($e->getMessage());
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            } catch (\Exception $e) {
                DB::rollback();
                Log::alert($e->getMessage());
                dd($e->getMessage());
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            DB::rollback();
            Log::alert($e->getMessage());
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public    function assignDefaultRoles($user, $roleName = null)
    {


        $user->assignRole('member');
    }

}
<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Tests\Unit\Services;

use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Http;
use Modules\JobSeeker\Services\JobDetailFetchingService;
use Mo<PERSON>les\JobSeeker\Entities\Job;
use Modules\JobSeeker\Entities\JobProvider;
use Modules\JobSeeker\Entities\JobDetailedInfo;

/**
 * Unit tests for JobDetailFetchingService
 * 
 * @covers \Modules\JobSeeker\Services\JobDetailFetchingService
 */
final class JobDetailFetchingServiceTest extends TestCase
{
    use DatabaseTransactions;

    private JobDetailFetchingService $service;
    private JobProvider $jobsAfProvider;
    private JobProvider $acbarProvider;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(JobDetailFetchingService::class);
        
        // Create test providers
        $this->jobsAfProvider = JobProvider::create([
            'name' => 'Jobs.af',
            'slug' => 'jobs-af',
            'base_url' => 'https://jobs.af',
            'scraping_endpoint' => 'https://jobs.af/jobs',
            'is_active' => true,
            'supports_scraping' => true,
            'fetch_method' => 'scraping',
        ]);

        $this->acbarProvider = JobProvider::create([
            'name' => 'ACBAR',
            'slug' => 'acbar',
            'base_url' => 'https://www.acbar.org',
            'scraping_endpoint' => 'https://www.acbar.org/jobs',
            'is_active' => true,
            'supports_scraping' => true,
            'fetch_method' => 'scraping',
        ]);
    }

    /** @test */
    public function it_can_fetch_jobs_af_details_successfully()
    {
        // Mock successful HTTP response
        Http::fake([
            'jobs.af/jobs/*' => Http::response($this->getJobsAfHtmlMock(), 200),
        ]);

        $job = Job::create([
            'position' => 'Software Developer',
            'company_name' => 'Test Company',
            'slug' => 'software-developer-test-company',
            'source' => 'jobs.af',
            'provider_id' => $this->jobsAfProvider->id,
        ]);

        $result = $this->service->fetchJobDetails($job);

        $this->assertTrue($result);

        // Check that detailed info was saved
        $detailedInfo = JobDetailedInfo::where('job_id', $job->id)->first();
        $this->assertNotNull($detailedInfo);
        $this->assertTrue($detailedInfo->fetch_success);
        $this->assertNotNull($detailedInfo->fetched_at);
    }

    /** @test */
    public function it_handles_jobs_af_fetch_failure_gracefully()
    {
        // Mock failed HTTP response
        Http::fake([
            'jobs.af/jobs/*' => Http::response('Not Found', 404),
        ]);

        $job = Job::create([
            'position' => 'Software Developer',
            'company_name' => 'Test Company',
            'slug' => 'software-developer-test-company',
            'source' => 'jobs.af',
            'provider_id' => $this->jobsAfProvider->id,
        ]);

        $result = $this->service->fetchJobDetails($job);

        $this->assertFalse($result);

        // Check that failure was recorded
        $detailedInfo = JobDetailedInfo::where('job_id', $job->id)->first();
        $this->assertNotNull($detailedInfo);
        $this->assertFalse($detailedInfo->fetch_success);
        $this->assertNotNull($detailedInfo->fetch_error);
    }

    /** @test */
    public function it_can_fetch_acbar_details_successfully()
    {
        // Mock successful HTTP response
        Http::fake([
            'www.acbar.org/jobs/*' => Http::response($this->getAcbarHtmlMock(), 200),
        ]);

        $job = Job::create([
            'position' => 'Project Manager',
            'company_name' => 'ACBAR Organization',
            'slug' => '135124',
            'source' => 'acbar',
            'provider_id' => $this->acbarProvider->id,
        ]);

        $result = $this->service->fetchJobDetails($job);

        $this->assertTrue($result);

        // Check that detailed info was saved
        $detailedInfo = JobDetailedInfo::where('job_id', $job->id)->first();
        $this->assertNotNull($detailedInfo);
        $this->assertTrue($detailedInfo->fetch_success);
    }

    /** @test */
    public function it_handles_missing_job_slug_for_jobs_af()
    {
        $job = Job::create([
            'position' => 'Software Developer',
            'company_name' => 'Test Company',
            'slug' => null, // Missing slug
            'source' => 'jobs.af',
            'provider_id' => $this->jobsAfProvider->id,
        ]);

        $result = $this->service->fetchJobDetails($job);

        $this->assertFalse($result);

        // Check that error was recorded
        $detailedInfo = JobDetailedInfo::where('job_id', $job->id)->first();
        $this->assertNotNull($detailedInfo);
        $this->assertFalse($detailedInfo->fetch_success);
        $this->assertStringContains('Missing job slug', $detailedInfo->fetch_error);
    }

    /** @test */
    public function it_handles_unknown_provider()
    {
        $unknownProvider = JobProvider::create([
            'name' => 'Unknown Provider',
            'slug' => 'unknown',
            'base_url' => 'https://unknown.com',
            'is_active' => true,
        ]);

        $job = Job::create([
            'position' => 'Test Job',
            'company_name' => 'Test Company',
            'slug' => 'test-job',
            'source' => 'unknown',
            'provider_id' => $unknownProvider->id,
        ]);

        $result = $this->service->fetchJobDetails($job);

        $this->assertFalse($result);
    }

    /** @test */
    public function it_determines_if_job_needs_detailed_fetch()
    {
        $job = Job::create([
            'position' => 'Software Developer',
            'company_name' => 'Test Company',
            'slug' => 'test-job',
            'source' => 'jobs.af',
            'provider_id' => $this->jobsAfProvider->id,
        ]);

        // Job without detailed info should need fetch
        $this->assertTrue($this->service->needsDetailedFetch($job));

        // Create successful detailed info
        JobDetailedInfo::create([
            'job_id' => $job->id,
            'provider_id' => $this->jobsAfProvider->id,
            'fetch_success' => true,
            'fetched_at' => now(),
        ]);

        // Recently fetched job should not need fetch
        $this->assertFalse($this->service->needsDetailedFetch($job));

        // Update to old fetch time
        JobDetailedInfo::where('job_id', $job->id)->update([
            'fetched_at' => now()->subDays(8),
        ]);

        // Old fetch should need refresh
        $this->assertTrue($this->service->needsDetailedFetch($job));
    }

    /** @test */
    public function it_retries_failed_fetches_after_time_delay()
    {
        $job = Job::create([
            'position' => 'Software Developer',
            'company_name' => 'Test Company',
            'slug' => 'test-job',
            'source' => 'jobs.af',
            'provider_id' => $this->jobsAfProvider->id,
        ]);

        // Create failed detailed info
        JobDetailedInfo::create([
            'job_id' => $job->id,
            'provider_id' => $this->jobsAfProvider->id,
            'fetch_success' => false,
            'fetched_at' => now()->subHours(7), // Recent failure
            'fetch_error' => 'Test error',
        ]);

        // Recent failure should not need retry
        $this->assertFalse($this->service->needsDetailedFetch($job));

        // Update to old failure
        JobDetailedInfo::where('job_id', $job->id)->update([
            'fetched_at' => now()->subHours(7), // 7 hours ago - should retry
        ]);

        // Old failure should be retried
        $this->assertTrue($this->service->needsDetailedFetch($job));
    }

    /** @test */
    public function it_handles_http_timeout_gracefully()
    {
        // Mock timeout
        Http::fake([
            'jobs.af/jobs/*' => function () {
                throw new \Illuminate\Http\Client\ConnectionException('Timeout');
            },
        ]);

        $job = Job::create([
            'position' => 'Software Developer',
            'company_name' => 'Test Company',
            'slug' => 'test-job',
            'source' => 'jobs.af',
            'provider_id' => $this->jobsAfProvider->id,
        ]);

        $result = $this->service->fetchJobDetails($job);

        $this->assertFalse($result);

        // Check error was recorded
        $detailedInfo = JobDetailedInfo::where('job_id', $job->id)->first();
        $this->assertNotNull($detailedInfo);
        $this->assertFalse($detailedInfo->fetch_success);
    }

    /** @test */
    public function it_extracts_years_from_experience_text()
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('extractYearsFromText');
        $method->setAccessible(true);

        // Test various formats
        $this->assertEquals(4, $method->invoke($this->service, '3-5 years'));
        $this->assertEquals(2, $method->invoke($this->service, '2+ years'));
        $this->assertEquals(3, $method->invoke($this->service, 'minimum 3 years'));
        $this->assertEquals(5, $method->invoke($this->service, '5 years experience'));
        $this->assertEquals(0, $method->invoke($this->service, 'no specific requirement'));
    }

    /** @test */
    public function it_cleans_text_content_properly()
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('cleanTextContent');
        $method->setAccessible(true);

        $dirtyText = "  This   has   extra   spaces  \n\n  and  newlines  ";
        $cleaned = $method->invoke($this->service, $dirtyText);

        $this->assertEquals('This has extra spaces and newlines', $cleaned);
    }

    /**
     * Mock HTML response for Jobs.af job detail page
     */
    private function getJobsAfHtmlMock(): string
    {
        return '
        <html>
        <body>
            <div class="job-description">
                <p>Develop and maintain web applications using modern technologies.</p>
            </div>
            <div class="company-about">
                <p>We are a leading technology company focused on innovation.</p>
            </div>
            <div class="requirements">
                <p>Bachelor\'s degree in Computer Science. 3+ years experience with PHP.</p>
            </div>
            <div class="how-to-apply">
                <p>Send your <NAME_EMAIL></p>
            </div>
            <a href="mailto:<EMAIL>">Apply via Email</a>
            <span class="salary">AFN 50,000 - 70,000</span>
        </body>
        </html>';
    }

    /**
     * Mock HTML response for ACBAR job detail page
     */
    private function getAcbarHtmlMock(): string
    {
        return '
        <html>
        <body>
            <table>
                <tr>
                    <td>About Company</td>
                    <td>ACBAR is a leading organization in Afghanistan working on development projects.</td>
                </tr>
                <tr>
                    <td>Job Description</td>
                    <td>Manage development projects and coordinate with stakeholders.</td>
                </tr>
                <tr>
                    <td>Requirements</td>
                    <td>Master\'s degree in relevant field. 5+ years of project management experience.</td>
                </tr>
                <tr>
                    <td>How to Apply</td>
                    <td>Submit application through our website <NAME_EMAIL></td>
                </tr>
                <tr>
                    <td>Contact Email</td>
                    <td><EMAIL></td>
                </tr>
                <tr>
                    <td>Experience Required</td>
                    <td>5-7 years</td>
                </tr>
            </table>
        </body>
        </html>';
    }
}

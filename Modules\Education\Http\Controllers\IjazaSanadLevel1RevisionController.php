<?php

namespace Modules\Education\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\IjazasanadMemorizationPlan;
use App\MoshafSurah;
use App\Program;
use App\ProgramLevel;
use App\ProgramLevelLesson;
use App\ProgramLevelLessonFormInputs;
use App\StudentNouranyaPlan;
use App\StudentNouranyaReport;
use App\Talaqqi;
use App\Talqeen;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Session;
use App\Subject;

class IjazaSanadLevel1RevisionController extends Controller
{



    public function getSequentialLessons(Request $request, $programLevelId, $fromLessonId)
    {
        try {


            $date = \Carbon\Carbon::createFromFormat('F Y', $request->get('fromDate'));

            $lessons = ProgramLevelLesson::where('program_level_id', $programLevelId)
                ->where('id', '>', $fromLessonId)
                ->orderBy('id')
                ->get(['id', 'properties'])
                ->map(function ($lesson) {
                    return [
                        'id' => $lesson->id,
                        'name' => $lesson->properties['name'] ?? 'No Name',
                    ];
                });
            $currentPlan = StudentNouranyaPlan::where('student_id', $request->get('studentId'))
                ->where('plan_year_and_month', $date->format('Y-m'))
                ->latest('id')
                ->first(['to_lesson']);

            return response()->json(['success' => true, 'lessons' => $lessons,'currentToLessonId' => $currentPlan->to_lesson]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to fetch sequential lessons.'], 500);
        }
    }
    public function getLessonsByProgramLevel($programLevelId)
    {
        try {
            $lessons = ProgramLevelLesson::where('program_level_id', $programLevelId)
                ->get()
                ->map(function ($lesson) {
                    return [
                        'id' => $lesson->id,
                        'name' => $lesson->properties['name'] ?? 'No Name',
                    ];
                });

            return response()->json(['success' => true, 'lessons' => $lessons]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch lessons: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to fetch lessons.'], 500);
        }
    }


    public function updateStudentProgramLevel(Request $request, $studentId)
    {



        $validatedData = $request->validate([
            'program_level_id' => 'required|integer|exists:program_levels,id',
            'fromDate' => 'required',
            'classId' => 'required',
            'centerId' => 'required',
        ]);
        if ($request->filled('lessonNo')) {
            $request->validate(['lessonNo' => 'integer|exists:program_level_lessons,id']);

        }
        $date = \Carbon\Carbon::createFromFormat('F Y', $request->get('fromDate'));
        DB::beginTransaction();




        try {
            $nouranyaPlan = StudentNouranyaPlan::updateOrCreate(
                ['student_id' => $studentId], // Key to find or create the record
                [
                    'level_id' => $validatedData['program_level_id'], // Assuming 'level' holds the ID of the program level
                    'class_id' => $request->classId, // Assuming class_id comes from the request and is validated
                    'organization_id' => config('organization_id'), // Assuming organization_id comes from the request and is validated
                    'from_lesson' => $request->fromLesson, // Assuming lesson comes from the request and is validated
                    'fromLesson' => $request->fromLesson, // Assuming lesson comes from the request and is validated
                    // other fields can be added here as needed...
                    'status' => 'waiting_for_approval', // For example, set a default status
                    'plan_year_and_month' => $date->format('Y-m'), // For example, set the current year and month
                    'start_date' => $date->format('Y-m-d'), // For example, set the current year and month
                    'center_id' => $request->centerId, // For example, set the current year and month
                    // 'approved_by', 'supervisor_comment', etc. can be set based on application logic
                ]
            );
            DB::commit();
            $availableToLessons = [];



            if (empty($request->toLesson)) {
                $talaqqiTalqeenType = $nouranyaPlan->talqeen_talaqqi;

                if ($talaqqiTalqeenType === 'talaqqi') {
                    $availableToLessons = Talaqqi::all(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
                        ->map(function ($lesson) {
                            return [
                                'id' => $lesson->id,
                                'lesson_no' => $lesson->lesson_no,
                                'name' => MoshafSurah::find($lesson->to_surah_id)->name . '-' . $lesson->to_ayah_number,
                                'created_at' => $lesson->created_at,
                                'updated_at' => $lesson->updated_at,
                            ];
                        });
                } elseif ($talaqqiTalqeenType === 'talqeen') {
                    $availableToLessons = Talqeen::all(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
                        ->map(function ($lesson) {
                            return [
                                'id' => $lesson->id,
                                'lesson_no' => $lesson->lesson_no,
                                'name' => MoshafSurah::find($lesson->to_surah_id)->name . '-' . $lesson->to_ayah_number,
                                'created_at' => $lesson->created_at,
                                'updated_at' => $lesson->updated_at,
                            ];
                        });
                } else {




                    if ($request->filled('program_level_title') && ($request->get('program_level_title') == 'Level 1' || $request->get('program_level_title') == 'Level 2')) {
                        // Fetch lessons directly from the ProgramLevelLesson model
                        $availableToLessons = \App\ProgramLevelLesson::where('program_level_id', $validatedData['program_level_id'])
                            ->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(properties, '$.no')) >= ?", [(int)$request->fromLesson]) // Use JSON_UNQUOTE and JSON_EXTRACT for numeric comparison
                            ->orderByRaw("CAST(JSON_UNQUOTE(JSON_EXTRACT(properties, '$.no')) AS UNSIGNED)") // Sort by 'no' key

                            ->get(['id', 'properties'])
                            ->map(function ($lesson) {
                                $properties = $lesson->properties;
                                return [
                                    'id' => $lesson->id,
                                    'name' => $properties['name'] ?? '',
                                    'lesson_no' => $properties['no'] ?? '',
                                    'letter' => $properties['letter'] ?? '',
                                ];
                            });

                    } else{




                    $availableToLessons = ProgramLevel::findOrFail($validatedData['program_level_id'])->lessons()
                        ->get(['id', 'properties']) // Fetch 'id' and 'properties'
                        ->map(function ($lesson) {
                            $properties = $lesson->properties; // No need for json_decode
                            return [
                                'id' => $lesson->id,
                                'name' => $properties['name'] ?? '',
                                'lesson_no' => $properties['no'] ?? '',
                                'letter' => $properties['letter'] ?? '',
                            ];
                        });
                }
                }




            }


            return response()->json(['success' => true, 'message' => 'Program level updated successfully.',  'availableToLessons' => $availableToLessons->values()]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to update student's program level: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update program level:  '.$e->getMessage()], 500);
        }
    }
    public function updateStudentTalaqqiProgramLevel(Request $request, $studentId)
    {



        $validatedData = $request->validate([
            'program_level_id' => 'required|integer|exists:program_levels,id',
            'fromDate' => 'required',
            'classId' => 'required',
            'centerId' => 'required',
        ]);
        if ($request->filled('lessonNo')) {
            $request->validate(['lessonNo' => 'integer|exists:program_level_lessons,id']);

        }
        $date = \Carbon\Carbon::createFromFormat('F Y', $request->get('fromDate'));
        DB::beginTransaction();




        try {
            $nouranyaPlan = StudentNouranyaPlan::updateOrCreate(
                ['student_id' => $studentId,
                    'class_id' => $request->classId,
                    'plan_year_and_month' => $date->format('Y-m')
                ], // Key to find or create the record
                [
                    'level_id' => $validatedData['program_level_id'], // Assuming 'level' holds the ID of the program level
                    'class_id' => $request->classId, // Assuming class_id comes from the request and is validated
                    'organization_id' => config('organization_id'), // Assuming organization_id comes from the request and is validated
//                    'from_lesson' => $request->fromLesson, // Assuming lesson comes from the request and is validated
//                    'to_lesson' => $request->toLesson, // Assuming lesson comes from the request and is validated
                    'talqeen_talaqqi' => 'talaqqi', // For example, set a default status
                    'talaqqi_from_lesson' => $request->fromLesson,
                    'talaqqi_to_lesson' => $request->toLesson,
                    'status' => 'waiting_for_approval', // For example, set a default status
                    'plan_year_and_month' => $date->format('Y-m'), // For example, set the current year and month
                    'start_date' => $date->format('Y-m-d'), // For example, set the current year and month
                    'center_id' => $request->centerId, // For example, set the current year and month
                    // 'approved_by', 'supervisor_comment', etc. can be set based on application logic
                ]
            );
            DB::commit();
            $availableToLessons = [];





            if ($request->filled('isLevelThree') == 1) {
                $lessonNoThreshold = $request->fromLesson; // Replace this with your variable



                $availableToLessons = Talaqqi::where('lesson_no', '>=',(int)$lessonNoThreshold)
                    ->orderBy(DB::raw('CAST(lesson_no AS UNSIGNED)'), 'asc') // Order by lesson_no in ascending order
                    ->get(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
                    ->map(function ($lesson) {
                        return [
                            'id' => $lesson->id,
                            'lesson_no' => $lesson->lesson_no,
                            'name' => MoshafSurah::find($lesson->to_surah_id)->eng_name . ':' . $lesson->from_ayah_number. '-' . $lesson->to_ayah_number,
                            'created_at' => $lesson->created_at,
                            'updated_at' => $lesson->updated_at,
                        ];
                    })->sortBy('lesson_no', SORT_NATURAL); // Ensure the order is maintained
                ;
            }else{



            if (empty($request->toLesson)) {
                    $availableToLessons = Talaqqi::all(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
                        ->map(function ($lesson) {
                            return [
                                'id' => $lesson->id,
                                'lesson_no' => $lesson->lesson_no,
                                'name' => MoshafSurah::find($lesson->to_surah_id)->name . '-' . $lesson->to_ayah_number,
                                'created_at' => $lesson->created_at,
                                'updated_at' => $lesson->updated_at,
                            ];
                        });
            }
            }


            return response()->json(['success' => true, 'message' => 'Program level updated successfully.',  'availableToLessons' => $availableToLessons]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to update student's program level: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update program level:  '.$e->getMessage()], 500);
        }
    }
    public function updateStudentTalqeenProgramLevel(Request $request, $studentId)
    {



        $validatedData = $request->validate([
            'program_level_id' => 'required|integer|exists:program_levels,id',
            'fromDate' => 'required',
            'classId' => 'required',
            'centerId' => 'required',
        ]);
        if ($request->filled('lessonNo')) {
            $request->validate(['lessonNo' => 'integer|exists:program_level_lessons,id']);

        }
        $date = \Carbon\Carbon::createFromFormat('F Y', $request->get('fromDate'));
        DB::beginTransaction();




        try {
            $nouranyaPlan = StudentNouranyaPlan::updateOrCreate(
                ['student_id' => $studentId], // Key to find or create the record
                [
                    'level_id' => $validatedData['program_level_id'], // Assuming 'level' holds the ID of the program level
                    'class_id' => $request->classId, // Assuming class_id comes from the request and is validated
                    'organization_id' => config('organization_id'), // Assuming organization_id comes from the request and is validated
//                    'from_lesson' => $request->fromLesson, // Assuming lesson comes from the request and is validated
//                    'to_lesson' => $request->toLesson, // Assuming lesson comes from the request and is validated
                    'talqeen_from_lesson' => $request->fromLesson,
                    'talqeen_to_lesson' => $request->toLesson,
                    'talqeen_talaqqi' => 'talqeen', // For example, set a default status
                    'status' => 'waiting_for_approval', // For example, set a default status
                    'plan_year_and_month' => $date->format('Y-m'), // For example, set the current year and month
                    'start_date' => $date->format('Y-m-d'), // For example, set the current year and month
                    'center_id' => $request->centerId, // For example, set the current year and month
                ]
            );
            DB::commit();
            $availableToLessons = [];



            if ($request->filled('isLevelThree') == 1) {
                $lessonNoThreshold = $request->fromLesson; // Replace this with your variable

                $availableToLessons = Talqeen::where('lesson_no', '>=', (int)$lessonNoThreshold)
                    ->orderBy(DB::raw('CAST(lesson_no AS UNSIGNED)'), 'asc') // Order by lesson_no in ascending order
                    ->get(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
                    ->map(function ($lesson) {
                        return [
                            'id' => $lesson->id,
                            'lesson_no' => $lesson->lesson_no,
                            'name' => MoshafSurah::find($lesson->from_surah_id)->eng_name . ':' . $lesson->from_ayah_number. '-' . \App\MoshafSurah::find($lesson->to_surah_id)->eng_name . '-' . $lesson->to_ayah_number,
                            'created_at' => $lesson->created_at,
                            'updated_at' => $lesson->updated_at,
                        ];
                    });
            }else{


            if (empty($request->toLesson)) {
                $availableToLessons = Talqeen::all(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
                    ->map(function ($lesson) {
                        return [
                            'id' => $lesson->id,
                            'lesson_no' => $lesson->lesson_no,
                            'name' => MoshafSurah::find($lesson->to_surah_id)->name . '-' . $lesson->to_ayah_number,
                            'created_at' => $lesson->created_at,
                            'updated_at' => $lesson->updated_at,
                        ];
                    });





            }
            }


            return response()->json(['success' => true, 'message' => 'Program level updated successfully.',  'availableToLessons' => $availableToLessons]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to update student's program level: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update program level:  '.$e->getMessage()], 500);
        }
    }

    public function getLessonLines($studentId, Request $request)
    {
        $lessonNo = $request->get('lesson_id');
        $programLevelId = $request->get('programLevelId');
//        $lesson = ProgramLevelLesson::find($toLessonId);
//        $lesson = ProgramLevelLesson::whereJsonContains('properties->no', $toLessonId)->first();
        $lesson = ProgramLevelLesson::where('program_level_id', $programLevelId)
            ->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(properties, '$.no')) = ?", [(int)$lessonNo])
            ->first();
        if ($lesson && isset($lesson->properties['lines'])) {
            $numberOfLines = $lesson->properties['lines'];
            return response()->json(['number_of_lines' => $numberOfLines]);
        } else {
            return response()->json(['error' => 'Lesson not found or lines property missing'], 404);
        }
    }
    public function getToLessonLines(Request $request,$studentId)
    {
        $toLessonId = $request->get('toLessonId');
        $programLevelId = $request->get('programLevelId');
//        $lesson = ProgramLevelLesson::find($toLessonId);
//        $lesson = ProgramLevelLesson::whereJsonContains('properties->no', $toLessonId)->first();
        $lesson = ProgramLevelLesson::where('program_level_id', $programLevelId)
            ->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(properties, '$.no')) = ?", [(int)$toLessonId])
            ->first();

        if ($lesson && isset($lesson->properties['lines'])) {
            $numberOfLines = $lesson->properties['lines'];
            return response()->json(['number_of_lines' => $numberOfLines]);
        } else {
            return response()->json(['error' => 'Lesson not found or lines property missing'], 404);
        }
    }

    public function getNouranyaReportToLessonLines(Request $request,$studentId)
    {
        $lessonNo = $request->get('toLessonId');

        // Retrieve the related Nouranya plan and daily report
        $nouranyaPlan = StudentNouranyaPlan::where('student_id', $studentId)
            ->where('plan_year_and_month', Carbon::parse($request->get('fromDate'))->format('Y-m'))
            ->first();


        $nouranyaDailyReport = StudentNouranyaReport::where('student_id', $studentId)
            ->where('plan_year_and_month', Carbon::parse($request->get('fromDate'))->format('Y-m'))
            ->first();

        // Check if both plan and report exist
        if (!$nouranyaPlan || !$nouranyaDailyReport) {
            return response()->json(['error' => 'Nouranya plan or daily report not found'], 404);
        }

        // Find the lesson based on the toLessonId
        $programLevelId = $request->get('programLevelId');

        $lesson = ProgramLevelLesson::where('program_level_id', $programLevelId)->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(properties, '$.no')) = ?", [(int)$lessonNo])->get()->first();

        if ($lesson && isset($lesson->properties['lines'])) {

            // Retrieve the number of lines from the lesson's properties
            $lines = $lesson->properties['lines'];


            // if from lesson == to lesson
            if($nouranyaPlan->from_lesson == $nouranyaPlan->to_lesson){
                // Start and end line numbers based on the Nouranya plan
                $startLineNumber = $nouranyaPlan->from_lesson_line_number;
                $endLineNumber = $nouranyaPlan->to_lesson_line_number;
                // Calculate the line number range, ensuring it adheres to the plan's boundaries
                $lineNumberRange = range($startLineNumber, $endLineNumber);
            }else{
                $startLineNumber = 1;

                $endLineNumber = $lines;

                // Calculate the line number range, ensuring it adheres to the plan's boundaries
                $lineNumberRange = range(1, $endLineNumber);
            }

            // Calculate the number of lines
            $numberOfLines = count($lineNumberRange);


            return response()->json([
                'number_of_lines' => $numberOfLines,
                'line_number_range' => $lineNumberRange,
                'nouranyaPlanFromLineNumberStartingFrom' => $startLineNumber
            ]);
        } else {
            return response()->json(['error' => 'Lesson not found or lines property missing'], 404);
        }
    }


    public function fetchRelatedLessonsAndUpdatePlan($programLevelId, Request $request)
    {
        try {
            $selectedType = $request->input('selected_type'); // Get the selected type from the request

            if ($selectedType === 'talaqqi') {
                $fromDate = $request->get('from_date');

                $date = \Carbon\Carbon::createFromFormat('Y-m-d', $fromDate);
                $classId = $request->get('class_id'); // Get the class ID from the request
                $studentId = $request->get('student_id');
                $programId = ProgramLevel::where('id', $programLevelId)->first()->program_id;

                // Update or create the student's Nouranya plan
                $currentPlan = StudentNouranyaPlan::updateOrCreate(
                    ['student_id' => $studentId,'class_id' => $classId, 'plan_year_and_month' => $date->format('Y-m')],
                    [
                        'level_id' => $programLevelId,
                        'talqeen_talaqqi' => $selectedType,
                        'organization_id' => config('organization_id'),

                    ]
                );


                $lessons = Talaqqi::where('program_id', $programId)
                    ->get(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
                    ->map(function ($lesson) {
                        return [
                            'id' => $lesson->id,
                            'lesson_no' => $lesson->lesson_no,
                            'program_id' => $lesson->program_id,
                            'from_surah_id' => $lesson->from_surah_id,
                            'name' => MoshafSurah::find($lesson->from_surah_id)->name . '-' . $lesson->from_ayah_number ,
                            'from_ayah_number' => $lesson->from_ayah_number,
                            'to_surah_id' => $lesson->to_surah_id,
                            'to_ayah_number' => $lesson->to_ayah_number,
                            'created_at' => $lesson->created_at,
                            'updated_at' => $lesson->updated_at,
                        ];
                    });
            } elseif ($selectedType === 'talqeen') {
                $programId = ProgramLevel::where('id', $programLevelId)->first()->program_id;
                $fromDate = $request->get('from_date');

                $date = \Carbon\Carbon::createFromFormat('Y-m-d', $fromDate);


                $currentPlan = StudentNouranyaPlan::where('student_id', $request->get('student_id'))
                    ->where('plan_year_and_month', $date->format('Y-m'))->update([
                        'talqeen_talaqqi' => 'talqeen'
                    ]);
                $lessons = Talqeen::where('program_id', $programId)
                    ->get(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
                    ->map(function ($lesson) {
                        return [
                            'id' => $lesson->id,
                            'lesson_no' => $lesson->lesson_no,
                            'program_id' => $lesson->program_id,
                            'from_surah_id' => $lesson->from_surah_id,
                            'name' => MoshafSurah::find($lesson->from_surah_id)->name . '-' . $lesson->from_ayah_number ,
                            'from_ayah_number' => $lesson->from_ayah_number,
                            'to_surah_id' => $lesson->to_surah_id,
                            'to_ayah_number' => $lesson->to_ayah_number,
                            'created_at' => $lesson->created_at,
                            'updated_at' => $lesson->updated_at,
                        ];
                    });
            } else {
                $lessons = ProgramLevel::findOrFail($programLevelId)->lessons()
                    ->get(['id', 'properties']) // Fetch 'id' and 'properties'
                    ->map(function ($lesson) {
                        $properties = $lesson->properties; // No need for json_decode
                        return [
                            'id' => $lesson->id,
                            'name' => $properties['name'] ?? '',
                            'no' => $properties['no'] ?? '',
                        ];
                    });
            }


            return response()->json(['success' => true, 'lessons' => $lessons]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch lessons: '.$e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to fetch lessons: '.$e->getMessage()], 500);
        }
    }



    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $keyword = $request->get('search');
        $perPage = 25;

        if (!empty($keyword)) {
            $programlevels = ProgramLevel::where('program_id', 'LIKE', "%$keyword%")
				->orWhere('title', 'LIKE', "%$keyword%")
				->orWhere('description', 'LIKE', "%$keyword%")
				->orWhere('status', 'LIKE', "%$keyword%")
				->paginate($perPage);
        } else {
            $programlevels = ProgramLevel::paginate($perPage);
        }

        return view('education::program-levels.index', compact('programlevels'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create(Request $request)
    {
        $programId = $request->query('programId');
        $programs = null;

        if (empty($programId)) {
            $programs = Program::all(); // Assuming Program is your model name
        }


        return view('education::program-levels.create.create', compact('programId', 'programs'));
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {



        $this->validateProgramLevel($request);
        
        $requestData = $request->all();

        $programlevel = ProgramLevel::create($requestData);
        
        foreach ($request->translate as $code => $translate) {
            $programlevel->translateOrNew($code)->title = $translate['title'];
        }

        $programlevel->save();

        // Assuming your lessons are submitted as an array 'lessons' and each lesson has 'title'
        if($request->has('lessons')) {


            foreach ($request->input('lessons', []) as $lesson) {
                $lessonProperties = $lesson['properties'] ?? [];
                $lessonProperties['course'] = 'ijazasanadrevision'; // Add the course key

                    // Create new lesson
                    ProgramLevelLesson::create([
                        'program_level_id' => $programlevel->id,
                        'properties' => $lessonProperties,
                    ]);

            }
        }


        Session::flash('flash_message', 'ProgramLevel added!');

        if($request->ajax()){
            return response()->json(['status' => 'success']);
        }


        return redirect('workplace/education/program-levels');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
     
        $programlevel = ProgramLevel::findOrFail($id);
        $subject = Subject::get();
        
        $sub_level = ProgramLevel::find($id);

        return view('education::program-levels.show', compact('programlevel','subject','progrlevsub','sub_level'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        try {
            // Fetch all records from the talaqqi table
            // Fetch records ordered by 'order' field
            $talaqqiRecords = Talaqqi::orderBy('order')->get();
            $talqeenRecords = Talqeen::orderBy('order')->get();
            $programlevel = ProgramLevel::with('lessons','program')->findOrFail($id);
            $programId = $programlevel->program->id;
            // Fetch all related form inputs based on the program level id
            $formInputs = ProgramLevelLessonFormInputs::where('program_level_id', $id)->get();

            // Pass $programlevel, its lessons, and $formInputs to the view

            $view = ($programlevel->program->title === 'Ijazah and Sanad')
                ? 'education::program-levels.ijazasanad.home'
                : 'education::program-levels.edit.edit';


            return view($view, compact(
                'programlevel',
                'formInputs',
                'talaqqiRecords',
                'talqeenRecords',
                'programId'
            ));

        } catch (\Exception $e) {
            Log::error('Error retrieving program level details: ' . $e->getMessage());
            return back()->with('error', 'Error retrieving program level details.');
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {

        try {


//            dd($request->all());

            DB::beginTransaction();


        $this->validateProgramLevel($request);
        
        $requestData = $request->all();



        $programlevel = ProgramLevel::findOrFail($id);

        $programlevel->update($requestData);

//        foreach ($request->translate as $code => $translate) {
//            $programlevel->translateOrNew($code)->title = $translate['title'];
//        }

        $programlevel->save();

        // Assuming your lessons are submitted as an array 'lessons' and each lesson has 'title' and optional 'id' for existing lessons
        if($request->has('lessons')) {
            $lessons = $request->input('lessons', []);

            // Iterate over each submitted lesson
            foreach ($lessons as $lesson) {
                $lessonProperties = $lesson['properties'] ?? [];
                $lessonProperties['course'] = 'ijazasanadrevision'; // Add the course key

                // Use updateOrCreate to either update the existing lesson or create a new one
                $newLesson =  ProgramLevelLesson::updateOrCreate(
                    [
                        'id' => $lesson['id'] ?? null, // Match existing by id if provided
                        'program_level_id' => $programlevel->id,
                    ],
                    [
                        'order' => $lesson['order'],
                        'properties' => $lessonProperties,
                    ]
                );

            }






            // Handle explicitly removed lessons
            if ($request->filled('removed_lessons')) {
                $removedLessonIds = explode(',', $request->input('removed_lessons'));
                ProgramLevelLesson::whereIn('id', $removedLessonIds)
                    ->delete();
            }

        } else {
            $programlevel->lessons()->delete();

        }

        Session::flash('flash_message', 'ProgramLevel updated!');
            DB::commit();
        } catch (ModelNotFoundException $e) {
            DB::rollBack();
            Log::error("ProgramLevel update failed: {$e->getMessage()}", ['id' => $id]);
            return back()->withErrors(['msg' => 'ProgramLevel not found.'])->withInput();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("ProgramLevel update failed: {$e->getMessage()}", ['id' => $id]);
            // Consider a more user-friendly message for the end user
            return back()->withErrors(['msg' => 'Unable to update ProgramLevel.'])->withInput();
        }
        return redirect()->back()->with('success', 'ProgramLevel updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        ProgramLevel::destroy($id);

        Session::flash('flash_message', 'ProgramLevel deleted!');

        return redirect('workplace/education/program-levels');
    }

    private function validateProgramLevel($request){
        $rules = [];

        $rules['translate.*.title'] = 'required';

        $this->validate($request,$rules);
    }

    public function add_program_level_subject(Request $request)
    {
     
    
    $pr_le = ProgramLevel::find($request->program_level_id);

    $sub = $request->subjects_id;
    $pr_le->subjects()->attach( $request->subjects_id, ['start_date' => \Carbon\Carbon::now()]);
  
    return redirect()->back();
     
    }
    public function delete_program_level_subject($id)
    {

     $subject = Subject::findOrFail($id);
    
  
    $subject->program_levels()->detach();
  
    return redirect()->back();
     
    }

    public function updateToLesson(Request $request, $studentId)
    {
        $validatedData = $request->validate([
            'program_level_id' => 'required',
            'fromDate' => 'required',
            'classId' => 'required|integer|exists:classes,id',
            'centerId' => 'required|integer|exists:centers,id',
            'toLesson' => 'required|integer',
            // other fields if necessary...
        ]);

//        DB::beginTransaxction();
        try {

            $date = \Carbon\Carbon::createFromFormat('F Y', $validatedData['fromDate']);
            $planYearAndMonth = $date->format('Y-m');
            $planYearAndMonthDay = $date->startOfMonth()->format('Y-m-d');

            // Check if there's an existing record with a different class ID
            $existingRecord = IjazasanadMemorizationPlan::where('student_id', $studentId)
                ->where('plan_year_and_month', $planYearAndMonth)
                ->first();

            if ($existingRecord && $existingRecord->class_id != $validatedData['classId']) {
                // If a record exists but for a different class, create a new plan entry for the current class

                // Create a new record
                $ijazasanadMemorizationPlan = IjazasanadMemorizationPlan::create([
                    'student_id' => $studentId,
                    'plan_year_and_month' => $planYearAndMonth,
                    'class_id' => $validatedData['classId'],
                    'revision_to_lesson' => $validatedData['toLesson'],
                    'center_id' => $validatedData['centerId'],
                    'start_date' => $planYearAndMonthDay,
                    'status' => '',  // Set status to empty initially for Level 1

                    // Include additional fields that need updating...
                ]);
            } else {
                $ijazasanadMemorizationPlan = IjazasanadMemorizationPlan::updateOrCreate(
                    [
                        'student_id' => $studentId,
                        'plan_year_and_month' => $planYearAndMonth,
                        'class_id' => $validatedData['classId'],
                    ],
                    [
                        'organization_id' => config('organization_id'), // Assuming organization_id comes from the request and is validated
                        'revision_to_lesson' => $validatedData['toLesson'],
                        'center_id' => $validatedData['centerId'],
                        'start_date' => $planYearAndMonthDay,
                        // Include additional fields that need updating...
                    ]
                );
            }

            // Apply Level 1 specific status logic
            // For Level 1 students: Check if ALL FOUR entry pairs are completely filled
            $allLevel1FieldsFilled = (
                !empty($ijazasanadMemorizationPlan->talqeen_from_lesson) && !empty($ijazasanadMemorizationPlan->talqeen_to_lesson) &&
                !empty($ijazasanadMemorizationPlan->revision_from_lesson) && !empty($ijazasanadMemorizationPlan->revision_to_lesson) &&
                !empty($ijazasanadMemorizationPlan->jazariyah_from_lesson) && !empty($ijazasanadMemorizationPlan->jazariyah_to_lesson) &&
                !empty($ijazasanadMemorizationPlan->seminars_from_lesson) && !empty($ijazasanadMemorizationPlan->seminars_to_lesson)
            );

            if ($allLevel1FieldsFilled) {
                $ijazasanadMemorizationPlan->status = 'waiting_for_approval';
            } else {
                // If not all Level 1 fields are filled, show no status (empty string)
                $ijazasanadMemorizationPlan->status = '';
            }
            
            $ijazasanadMemorizationPlan->save();

            $checkIfAllRequiredColumnsHaveValues = $allLevel1FieldsFilled;

            // Return a success response with the current status
            return response()->json([
                'success' => true,
                'allRequiredFieldsFilled' => $checkIfAllRequiredColumnsHaveValues,
                'message' => 'To lesson updated successfully.',
                'status' => $allLevel1FieldsFilled ? 'Waiting for Approval' : 'No Status'
            ]);
        } catch (\Exception $e) {
//            DB::rollBack();
            Log::error("Failed to update student's to_lesson: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update to_lesson. '.$e->getMessage()], 500);
        }
    }


}

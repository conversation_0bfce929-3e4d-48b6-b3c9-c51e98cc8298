<?php

namespace App\Jobs;

use App\Services\EmailService;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;
use Mail;

class SendAwaitingApprovalMonthlyPlansEmailJob
{
    use Dispatchable, InteractsWithQueue, SerializesModels;


    protected $supervisor_info = [];
    protected $halaqahName = '';
    protected $hefzPlanCount = 0;
    protected $employeeCent = '';
    protected $sender;
    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;
    /**
     * Create a new job instance.
     *
     * @return void
     */
//    public function __construct($supervisor_info, $sender)
    public function __construct($supervisor_info,$hefzPlanCount,$employeeCent)
    {


        $this->supervisor_info = $supervisor_info;
        $this->hefzPlanCount = $hefzPlanCount;
        $this->employeeCent = $employeeCent;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(\Illuminate\Mail\Mailer $mailer)
    {
        try {
            // Instantiate the EmailService
            $emailService = app(\App\Services\EmailService::class);

            // Send the email via the EmailService
            $emailService->sendEmail(
                [
                    'email' => $this->supervisor_info->email,
                    // Fallback to an empty string if name is unavailable
                    'name'  => $this->supervisor_info->name ?? ''
                ],
                'Halaqah Need Approval', // Subject
                'modules.site.templates.wajeha.backEnd.studentInformation.hefz_plan_awaiting_approval_notifiction_email', // View
                [
                    'data'           => $this->supervisor_info,
                    'hefzPlanCount'  => $this->hefzPlanCount,
                    'employeeCent'   => $this->employeeCent
                ],
                [], // attachments
                []  // cc
            );
        } catch (\Exception $exception) {
            Log::info($exception->getMessage());
        }
    }
}

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\JobSeeker\Entities\Job;

/**
 * Provides a simple landing page for AI resume tailoring.
 * Accepts a job slug, loads the job, and renders a minimal view.
 * This is an initial placeholder to unblock routes; the full AI flow can replace this later.
 */
final class AiResumeTailorController extends Controller
{
    public function show(Request $request, string $slug): View
    {
        $job = Job::where('slug', $slug)->firstOrFail();

        return view('jobseeker::ai.tailor', [
            'job' => $job,
        ]);
    }
}



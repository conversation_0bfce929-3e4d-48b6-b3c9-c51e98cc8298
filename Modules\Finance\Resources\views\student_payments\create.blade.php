@extends('layouts.hound')

@section('mytitle', 'Create Student Payment')

@section('content')
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default card-view">
                    <div class="panel-heading"><h3>Add Stundent Payment</h3></div>
                    <div class="panel-body">

                        @if ($errors->any())
                            <ul class="alert alert-danger">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        @endif

                        {!! Form::open(['url' => route('student_payments.store'), 'class' => 'form-horizontal', 'files' => true]) !!}
                        <div class="col-md-12">

                            <div class="form-group {{ $errors->has('student_id') ? 'has-error' : ''}}">
                            {!! Form::label('student_id', 'Student Name', ['class' => 'control-label']) !!}
                            
                                {!! Form::select('student_id', $students->pluck('full_name' , 'id' ) , null, ['class' => 'form-control select2']) !!}
                                {!! $errors->first('student_id', '
                                <p class="help-block alert-danger">
                                    :message
                                </p>
                                ') !!}
                            </div>
                            <div class="form-group {{ $errors->has('payment_category') ? 'has-error' : ''}} link">
                                {!! Form::label('payment_category', 'Payment Category', ['class' => 'control-label'] ) !!}
                                
                                    {!! Form::text('payment_category', null, ['class' => 'form-control', 'placeholder' => 'Ex. Fees']) !!}
                                    {!! $errors->first('payment_category', '
                                    <p class="help-block">
                                        :message
                                    </p>
                                    ') !!}
                            </div>
                        </div>
                        
                        @include ('finance::student_payments.form')

                        {!! Form::close() !!}

                    </div>
                </div>
            </div>
        </div>
@endsection
@include('jssnippets.select2')
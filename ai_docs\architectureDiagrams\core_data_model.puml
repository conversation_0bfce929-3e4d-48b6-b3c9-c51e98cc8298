@startuml Core Data Model (Conceptual)

!theme vibrant

package "Core Entities" {

    class User {
        id
        name
        email
        password
        --
        roles()
        permissions()
    }

    class Role {
        id
        name
        --
        users()
        permissions()
    }

    class Permission {
        id
        name
        --
        roles()
    }

    class Student {
        id
        user_id (FK)
        admission_no
        first_name
        last_name
        date_of_birth
        --
        user()
        admissions()
        enrollments()
        attendances()
        invoices()
    }

    class Employee {
        id
        user_id (FK)
        employee_no
        first_name
        last_name
        department_id (FK)
        --
        user()
        department()
        attendances()
        payroll()
    }

    class Department {
        id
        name
        --
        employees()
    }

    class Course {
        id
        name
        code
        --
        classes()
        subjects()
    }

    class AcademicClass as "Class"{
        id
        course_id (FK)
        section
        academic_year_id (FK)
        --
        course()
        students()
        subjects()
        timetable()
    }

    class Subject {
        id
        name
        code
        --
        courses()
        classes()
        teachers()
    }

    class Admission {
        id
        student_id (FK)
        application_date
        status
        --
        student()
    }

    class Enrollment {
        id
        student_id (FK)
        class_id (FK)
        academic_year_id (FK)
        --
        student()
        class()
    }

    class Invoice {
        id
        student_id (FK)
        amount
        due_date
        status
        --
        student()
        payments()
        feeItems()
    }

    class Payment {
        id
        invoice_id (FK)
        amount
        payment_date
        method
        --
        invoice()
    }

    class Attendance {
        id
        attendable_id (Polymorphic)
        attendable_type (Polymorphic)
        date
        status
        --
        attendable() ' Student or Employee
    }

    class Setting {
        id
        key
        value
    }
}

' Relationships
User "1" -- "1..*" Role : has
Role "*" -- "*" Permission : has
User "1" -- "1" Student : (can be)
User "1" -- "1" Employee : (can be)
Employee "*" -- "1" Department : belongs to

Student "1" -- "*" Admission : has
Student "1" -- "*" Enrollment : has
Enrollment "*" -- "1" AcademicClass : for
AcademicClass "*" -- "1" Course : belongs to
Course "*" -- "*" Subject : contains
AcademicClass "*" -- "*" Subject : offers

Student "1" -- "*" Invoice : receives
Invoice "1" -- "*" Payment : has

Student "1" -- "*" Attendance : has
Employee "1" -- "*" Attendance : has


@enduml 
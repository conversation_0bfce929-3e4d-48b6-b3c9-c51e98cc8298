tinymce.addI18n('he_IL',{
"Cut": "\u05d2\u05d6\u05d5\u05e8",
"Heading 5": "\u05db\u05d5\u05ea\u05e8\u05d5\u05ea 5",
"Header 2": "\u05db\u05d5\u05ea\u05e8\u05ea 2",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\u05d4\u05d3\u05e4\u05d3\u05e4\u05df \u05e9\u05dc\u05da \u05d0\u05d9\u05e0\u05d5 \u05de\u05d0\u05e4\u05e9\u05e8 \u05d2\u05d9\u05e9\u05d4 \u05d9\u05e9\u05d9\u05e8\u05d4 \u05dc\u05dc\u05d5\u05d7. \u05d0\u05e0\u05d0 \u05d4\u05e9\u05ea\u05de\u05e9 \u05d1\u05e7\u05d9\u05e6\u05d5\u05e8\u05d9 \u05d4\u05de\u05e7\u05dc\u05d3\u05ea Ctrl+X\/C\/V \u05d1\u05de\u05e7\u05d5\u05dd.",
"Heading 4": "\u05db\u05d5\u05ea\u05e8\u05d5\u05ea 4",
"Div": "\u05de\u05e7\u05d8\u05e2 \u05e7\u05d5\u05d3 Div",
"Heading 2": "\u05db\u05d5\u05ea\u05e8\u05d5\u05ea 2",
"Paste": "\u05d4\u05d3\u05d1\u05e7",
"Close": "\u05e1\u05d2\u05d5\u05e8",
"Font Family": "\u05e1\u05d5\u05d2 \u05d2\u05d5\u05e4\u05df",
"Pre": "\u05e7\u05d8\u05e2 \u05de\u05e7\u05d3\u05d9\u05dd Pre",
"Align right": "\u05d9\u05d9\u05e9\u05e8 \u05dc\u05d9\u05de\u05d9\u05df",
"New document": "\u05de\u05e1\u05de\u05da \u05d7\u05d3\u05e9",
"Blockquote": "\u05de\u05e7\u05d8\u05e2 \u05e6\u05d9\u05d8\u05d5\u05d8",
"Numbered list": "\u05e8\u05e9\u05d9\u05de\u05d4 \u05de\u05de\u05d5\u05e1\u05e4\u05e8\u05ea",
"Heading 1": "\u05db\u05d5\u05ea\u05e8\u05d5\u05ea 1",
"Headings": "\u05db\u05d5\u05ea\u05e8\u05d5\u05ea",
"Increase indent": "\u05d4\u05d2\u05d3\u05dc \u05d4\u05d6\u05d7\u05d4",
"Formats": "\u05e2\u05d9\u05e6\u05d5\u05d1\u05d9\u05dd",
"Headers": "\u05db\u05d5\u05ea\u05e8\u05d5\u05ea",
"Select all": "\u05d1\u05d7\u05e8 \u05d4\u05db\u05dc",
"Header 3": "\u05db\u05d5\u05ea\u05e8\u05ea 3",
"Blocks": "\u05de\u05d1\u05e0\u05d9\u05dd",
"Undo": "\u05d1\u05d8\u05dc \u05e4\u05e2\u05d5\u05dc\u05d4",
"Strikethrough": "\u05e7\u05d5 \u05d7\u05d5\u05e6\u05d4",
"Bullet list": "\u05e8\u05e9\u05d9\u05de\u05ea \u05ea\u05d1\u05dc\u05d9\u05d8\u05d9\u05dd",
"Header 1": "\u05db\u05d5\u05ea\u05e8\u05ea 1",
"Superscript": "\u05db\u05ea\u05d1 \u05e2\u05d9\u05dc\u05d9",
"Clear formatting": "\u05e0\u05e7\u05d4 \u05e2\u05d9\u05e6\u05d5\u05d1",
"Font Sizes": "\u05d2\u05d5\u05d3\u05dc \u05d2\u05d5\u05e4\u05df",
"Subscript": "\u05db\u05ea\u05d1 \u05ea\u05d7\u05ea\u05d9",
"Header 6": "\u05db\u05d5\u05ea\u05e8\u05ea 6",
"Redo": "\u05d1\u05e6\u05e2 \u05e9\u05d5\u05d1",
"Paragraph": "\u05e4\u05d9\u05e1\u05e7\u05d4",
"Ok": "\u05d0\u05d9\u05e9\u05d5\u05e8",
"Bold": "\u05de\u05d5\u05d3\u05d2\u05e9",
"Code": "\u05e7\u05d5\u05d3",
"Italic": "\u05e0\u05d8\u05d5\u05d9",
"Align center": "\u05de\u05e8\u05db\u05d6",
"Header 5": "\u05db\u05d5\u05ea\u05e8\u05ea 5",
"Heading 6": "\u05db\u05d5\u05ea\u05e8\u05d5\u05ea 6",
"Heading 3": "\u05db\u05d5\u05ea\u05e8\u05d5\u05ea 3",
"Decrease indent": "\u05d4\u05e7\u05d8\u05df \u05d4\u05d6\u05d7\u05d4",
"Header 4": "\u05db\u05d5\u05ea\u05e8\u05ea 4",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\u05d4\u05d3\u05d1\u05e7\u05d4 \u05d1\u05de\u05e6\u05d1 \u05d8\u05e7\u05e1\u05d8 \u05e8\u05d2\u05d9\u05dc. \u05ea\u05db\u05e0\u05d9\u05dd \u05d9\u05d5\u05d3\u05d1\u05e7\u05d5 \u05de\u05e2\u05ea\u05d4 \u05db\u05d8\u05e7\u05e1\u05d8 \u05e8\u05d2\u05d9\u05dc \u05e2\u05d3 \u05e9\u05ea\u05db\u05d1\u05d4 \u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05d6\u05d5.",
"Underline": "\u05e7\u05d5 \u05ea\u05d7\u05ea\u05d9",
"Cancel": "\u05d1\u05d8\u05dc",
"Justify": "\u05de\u05ea\u05d7 \u05dc\u05e6\u05d3\u05d3\u05d9\u05dd",
"Inline": "\u05d1\u05d2\u05d5\u05e3 \u05d4\u05d8\u05e7\u05e1\u05d8",
"Copy": "\u05d4\u05e2\u05ea\u05e7",
"Align left": "\u05d9\u05d9\u05e9\u05e8 \u05dc\u05e9\u05de\u05d0\u05dc",
"Visual aids": "\u05e2\u05d6\u05e8\u05d9\u05dd \u05d7\u05d6\u05d5\u05ea\u05d9\u05d9\u05dd",
"Lower Greek": "\u05d0\u05d5\u05ea\u05d9\u05d5\u05ea \u05d9\u05d5\u05d5\u05e0\u05d9\u05d5\u05ea \u05e7\u05d8\u05e0\u05d5\u05ea",
"Square": "\u05e8\u05d9\u05d1\u05d5\u05e2",
"Default": "\u05d1\u05e8\u05d9\u05e8\u05ea \u05de\u05d7\u05d3\u05dc",
"Lower Alpha": "\u05d0\u05d5\u05ea\u05d9\u05d5\u05ea \u05d0\u05e0\u05d2\u05dc\u05d9\u05d5\u05ea \u05e7\u05d8\u05e0\u05d5\u05ea",
"Circle": "\u05e2\u05d9\u05d2\u05d5\u05dc",
"Disc": "\u05d7\u05d9\u05e9\u05d5\u05e7",
"Upper Alpha": "\u05d0\u05d5\u05ea\u05d9\u05d5\u05ea \u05d0\u05e0\u05d2\u05dc\u05d9\u05d5\u05ea \u05d2\u05d3\u05d5\u05dc\u05d5\u05ea",
"Upper Roman": "\u05e1\u05e4\u05e8\u05d5\u05ea \u05e8\u05d5\u05de\u05d9\u05d5\u05ea \u05d2\u05d3\u05d5\u05dc\u05d5\u05ea",
"Lower Roman": "\u05e1\u05e4\u05e8\u05d5\u05ea \u05e8\u05d5\u05de\u05d9\u05d5\u05ea \u05e7\u05d8\u05e0\u05d5\u05ea",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "\u05d4\u05de\u05d6\u05d4\u05d4 \u05d7\u05d9\u05d9\u05d1 \u05dc\u05d4\u05ea\u05d7\u05d9\u05dc \u05d1\u05d0\u05d5\u05ea \u05d5\u05dc\u05d0\u05d7\u05e8\u05d9\u05d4 \u05e8\u05e7 \u05d0\u05d5\u05ea\u05d9\u05d5\u05ea, \u05de\u05e1\u05e4\u05e8\u05d9\u05dd, \u05de\u05e7\u05e4\u05d9\u05dd, \u05e0\u05e7\u05d5\u05d3\u05d5\u05ea, \u05e0\u05e7\u05d5\u05d3\u05ea\u05d9\u05d9\u05dd \u05d0\u05d5 \u05e7\u05d5\u05d5\u05d9\u05dd \u05ea\u05d7\u05ea\u05d9\u05d9\u05dd.",
"Name": "\u05e9\u05dd",
"Anchor": "\u05de\u05e7\u05d5\u05dd \u05e2\u05d9\u05d2\u05d5\u05df",
"Id": "\u05de\u05d6\u05d4\u05d4",
"You have unsaved changes are you sure you want to navigate away?": "\u05d4\u05e9\u05d9\u05e0\u05d5\u05d9\u05d9\u05dd \u05dc\u05d0 \u05e0\u05e9\u05de\u05e8\u05d5. \u05d1\u05d8\u05d5\u05d7 \u05e9\u05d1\u05e8\u05e6\u05d5\u05e0\u05da \u05dc\u05e6\u05d0\u05ea \u05de\u05d4\u05d3\u05e3?",
"Restore last draft": "\u05e9\u05d7\u05d6\u05e8 \u05d8\u05d9\u05d5\u05d8\u05d4 \u05d0\u05d7\u05e8\u05d5\u05e0\u05d4",
"Special character": "\u05ea\u05d5\u05d5\u05d9\u05dd \u05de\u05d9\u05d5\u05d7\u05d3\u05d9\u05dd",
"Source code": "\u05e7\u05d5\u05d3 \u05de\u05e7\u05d5\u05e8",
"Language": "\u05e9\u05e4\u05d4",
"Insert\/Edit code sample": "\u05d4\u05db\u05e0\u05e1\/\u05e2\u05e8\u05d5\u05da \u05d3\u05d5\u05d2\u05de\u05ea \u05e7\u05d5\u05d3",
"B": "\u05db'",
"R": "\u05d0'",
"G": "\u05d9'",
"Color": "\u05e6\u05d1\u05e2",
"Right to left": "\u05de\u05d9\u05de\u05d9\u05df \u05dc\u05e9\u05de\u05d0\u05dc",
"Left to right": "\u05de\u05e9\u05de\u05d0\u05dc \u05dc\u05d9\u05de\u05d9\u05df",
"Emoticons": "\u05de\u05d7\u05d5\u05d5\u05ea",
"Robots": "\u05e8\u05d5\u05d1\u05d5\u05d8\u05d9\u05dd",
"Document properties": "\u05de\u05d0\u05e4\u05d9\u05d9\u05e0\u05d9 \u05de\u05e1\u05de\u05da",
"Title": "\u05db\u05d5\u05ea\u05e8\u05ea",
"Keywords": "\u05de\u05d9\u05dc\u05d5\u05ea \u05de\u05e4\u05ea\u05d7",
"Encoding": "\u05e7\u05d9\u05d3\u05d5\u05d3",
"Description": "\u05ea\u05d9\u05d0\u05d5\u05e8",
"Author": "\u05de\u05d7\u05d1\u05e8",
"Fullscreen": "\u05de\u05e1\u05da \u05de\u05dc\u05d0",
"Horizontal line": "\u05e7\u05d5 \u05d0\u05d5\u05e4\u05e7\u05d9",
"Horizontal space": "\u05de\u05e8\u05d5\u05d5\u05d7 \u05d0\u05d5\u05e4\u05e7\u05d9",
"Insert\/edit image": "\u05d4\u05db\u05e0\u05e1\/\u05e2\u05e8\u05d5\u05da \u05ea\u05de\u05d5\u05e0\u05d4",
"General": "\u05db\u05dc\u05dc\u05d9",
"Advanced": "\u05de\u05ea\u05e7\u05d3\u05dd",
"Source": "\u05de\u05e7\u05d5\u05e8",
"Border": "\u05de\u05e1\u05d2\u05e8\u05ea",
"Constrain proportions": "\u05d4\u05d2\u05d1\u05dc\u05ea \u05e4\u05e8\u05d5\u05e4\u05d5\u05e8\u05e6\u05d9\u05d5\u05ea",
"Vertical space": "\u05de\u05e8\u05d5\u05d5\u05d7 \u05d0\u05e0\u05db\u05d9",
"Image description": "\u05ea\u05d9\u05d0\u05d5\u05e8 \u05d4\u05ea\u05de\u05d5\u05e0\u05d4",
"Style": "\u05e1\u05d2\u05e0\u05d5\u05df",
"Dimensions": "\u05de\u05d9\u05de\u05d3\u05d9\u05dd",
"Insert image": "\u05d4\u05db\u05e0\u05e1 \u05ea\u05de\u05d5\u05e0\u05d4",
"Image": "\u05ea\u05de\u05d5\u05e0\u05d4",
"Zoom in": "\u05d4\u05d2\u05d3\u05dc \u05ea\u05e6\u05d5\u05d2\u05d4",
"Contrast": "\u05e0\u05d9\u05d2\u05d5\u05d3\u05d9\u05d5\u05ea",
"Back": "\u05d7\u05d6\u05d5\u05e8",
"Gamma": "\u05d2\u05d0\u05de\u05d4",
"Flip horizontally": "\u05d4\u05e4\u05d5\u05da \u05d0\u05d5\u05e4\u05e7\u05d9\u05ea",
"Resize": "\u05e9\u05e0\u05d4 \u05d2\u05d5\u05d3\u05dc",
"Sharpen": "\u05d7\u05d3\u05d3",
"Zoom out": "\u05d4\u05e7\u05d8\u05df \u05ea\u05e6\u05d5\u05d2\u05d4",
"Image options": "\u05d0\u05e4\u05e9\u05e8\u05d5\u05d9\u05d5\u05ea \u05ea\u05de\u05d5\u05e0\u05d4",
"Apply": "\u05d9\u05d9\u05e9\u05dd",
"Brightness": "\u05d1\u05d4\u05d9\u05e8\u05d5\u05ea",
"Rotate clockwise": "\u05e1\u05d5\u05d1\u05d1 \u05d1\u05db\u05d9\u05d5\u05d5\u05df \u05d4\u05e9\u05e2\u05d5\u05df",
"Rotate counterclockwise": "\u05e1\u05d5\u05d1\u05d1 \u05d1\u05db\u05d9\u05d5\u05d5\u05df \u05d4\u05e4\u05d5\u05da \u05dc\u05e9\u05e2\u05d5\u05df",
"Edit image": "\u05e2\u05e8\u05d5\u05da \u05ea\u05de\u05d5\u05e0\u05d4",
"Color levels": "\u05e8\u05de\u05d5\u05ea \u05e6\u05d1\u05e2\u05d9\u05dd",
"Crop": "\u05e7\u05e6\u05e5",
"Orientation": "\u05db\u05d9\u05d5\u05d5\u05df \u05dc\u05d0\u05d5\u05e8\u05da \/ \u05dc\u05e8\u05d5\u05d7\u05d1",
"Flip vertically": "\u05d4\u05e4\u05d5\u05da \u05d0\u05e0\u05db\u05d9\u05ea",
"Invert": "\u05d4\u05d9\u05e4\u05d5\u05da \u05e6\u05d1\u05e2\u05d9\u05dd",
"Date\/time": "\u05ea\u05d0\u05e8\u05d9\u05da\/\u05e9\u05e2\u05d4",
"Insert date\/time": "\u05d4\u05db\u05e0\u05e1 \u05ea\u05d0\u05e8\u05d9\u05da\/\u05e9\u05e2\u05d4",
"Remove link": "\u05de\u05d7\u05e7 \u05e7\u05d9\u05e9\u05d5\u05e8",
"Url": "\u05db\u05ea\u05d5\u05d1\u05ea \u05e7\u05d9\u05e9\u05d5\u05e8",
"Text to display": "\u05d8\u05e7\u05e1\u05d8 \u05dc\u05d4\u05e6\u05d2\u05d4",
"Anchors": "\u05e2\u05d5\u05d2\u05e0\u05d9\u05dd",
"Insert link": "\u05d4\u05db\u05e0\u05e1 \u05e7\u05d9\u05e9\u05d5\u05e8",
"Link": "\u05e7\u05d9\u05e9\u05d5\u05e8",
"New window": "\u05d7\u05dc\u05d5\u05df \u05d7\u05d3\u05e9",
"None": "\u05dc\u05dc\u05d0",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\u05e0\u05e8\u05d0\u05d4 \u05e9\u05d4\u05db\u05ea\u05d5\u05d1\u05ea \u05e9\u05d4\u05db\u05e0\u05e1\u05ea \u05d4\u05d9\u05d0 \u05e7\u05d9\u05e9\u05d5\u05e8 \u05d7\u05d9\u05e6\u05d5\u05e0\u05d9 \u05d4\u05d0\u05dd \u05d1\u05e8\u05e6\u05d5\u05e0\u05da \u05dc\u05d4\u05d5\u05e1\u05d9\u05e3 \u05e7\u05d9\u05d3\u05d5\u05de\u05ea http:\/\/?",
"Paste or type a link": "\u05d4\u05d3\u05d1\u05e7 \u05d0\u05d5 \u05d4\u05e7\u05dc\u05d3 \u05e7\u05d9\u05e9\u05d5\u05e8",
"Target": "\u05de\u05d8\u05e8\u05d4",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\u05e0\u05e8\u05d0\u05d4 \u05e9\u05d4\u05db\u05ea\u05d5\u05d1\u05ea \u05e9\u05d4\u05db\u05e0\u05e1\u05ea \u05d4\u05d9\u05d0 \u05db\u05ea\u05d5\u05d1\u05ea \u05d0\u05d9\u05de\u05d9\u05d9\u05dc. \u05d4\u05d0\u05dd \u05d1\u05e8\u05e6\u05d5\u05e0\u05da \u05dc\u05d4\u05d5\u05e1\u05d9\u05e3 \u05d0\u05ea \u05d4\u05e7\u05d9\u05d3\u05d5\u05de\u05ea :mailto?",
"Insert\/edit link": "\u05d4\u05db\u05e0\u05e1\/\u05e2\u05e8\u05d5\u05da \u05e7\u05d9\u05e9\u05d5\u05e8",
"Insert\/edit video": "\u05d4\u05db\u05e0\u05e1\/\u05e2\u05e8\u05d5\u05da \u05e1\u05e8\u05d8\u05d5\u05df",
"Media": "\u05de\u05d3\u05d9\u05d4",
"Alternative source": "\u05de\u05e7\u05d5\u05e8 \u05de\u05e9\u05e0\u05d9",
"Paste your embed code below:": "\u05d4\u05d3\u05d1\u05e7 \u05e7\u05d5\u05d3 \u05d4\u05d8\u05de\u05e2\u05d4 \u05de\u05ea\u05d7\u05ea:",
"Insert video": "\u05d4\u05db\u05e0\u05e1 \u05e1\u05e8\u05d8\u05d5\u05df",
"Poster": "\u05e4\u05d5\u05e1\u05d8\u05e8",
"Insert\/edit media": "\u05d4\u05db\u05e0\u05e1\/\u05e2\u05e8\u05d5\u05da \u05de\u05d3\u05d9\u05d4",
"Embed": "\u05d4\u05d8\u05de\u05e2",
"Nonbreaking space": "\u05e8\u05d5\u05d5\u05d7 (\u05dc\u05dc\u05d0 \u05e9\u05d1\u05d9\u05e8\u05ea \u05e9\u05d5\u05e8\u05d4)",
"Page break": "\u05d3\u05e3 \u05d7\u05d3\u05e9",
"Paste as text": "\u05d4\u05d3\u05d1\u05e7 \u05db\u05d8\u05e7\u05e1\u05d8",
"Preview": "\u05ea\u05e6\u05d5\u05d2\u05d4 \u05de\u05e7\u05d3\u05d9\u05de\u05d4",
"Print": "\u05d4\u05d3\u05e4\u05e1",
"Save": "\u05e9\u05de\u05d9\u05e8\u05d4",
"Could not find the specified string.": "\u05de\u05d7\u05e8\u05d5\u05d6\u05ea \u05dc\u05d0 \u05e0\u05de\u05e6\u05d0\u05d4",
"Replace": "\u05d4\u05d7\u05dc\u05e3",
"Next": "\u05d4\u05d1\u05d0",
"Whole words": "\u05de\u05d9\u05dc\u05d4 \u05e9\u05dc\u05de\u05d4",
"Find and replace": "\u05d7\u05e4\u05e9 \u05d5\u05d4\u05d7\u05dc\u05e3",
"Replace with": "\u05d4\u05d7\u05dc\u05e3 \u05d1",
"Find": "\u05d7\u05e4\u05e9",
"Replace all": "\u05d4\u05d7\u05dc\u05e3 \u05d4\u05db\u05dc",
"Match case": "\u05d4\u05d1\u05d7\u05df \u05d1\u05d9\u05df \u05d0\u05d5\u05ea\u05d9\u05d5\u05ea \u05e7\u05d8\u05e0\u05d5\u05ea \u05dc\u05d2\u05d3\u05d5\u05dc\u05d5\u05ea",
"Prev": "\u05e7\u05d5\u05d3\u05dd",
"Spellcheck": "\u05d1\u05d5\u05d3\u05e7 \u05d0\u05d9\u05d5\u05ea",
"Finish": "\u05e1\u05d9\u05d9\u05dd",
"Ignore all": "\u05d4\u05ea\u05e2\u05dc\u05dd \u05de\u05d4\u05db\u05dc",
"Ignore": "\u05d4\u05ea\u05e2\u05dc\u05dd",
"Add to Dictionary": "\u05d4\u05d5\u05e1\u05e3 \u05dc\u05de\u05d9\u05dc\u05d5\u05df",
"Insert row before": "\u05d4\u05d5\u05e1\u05e3 \u05e9\u05d5\u05e8\u05d4 \u05dc\u05e4\u05e0\u05d9",
"Rows": "\u05e9\u05d5\u05e8\u05d5\u05ea",
"Height": "\u05d2\u05d5\u05d1\u05d4",
"Paste row after": "\u05d4\u05e2\u05ea\u05e7 \u05e9\u05d5\u05e8\u05d4 \u05d0\u05d7\u05e8\u05d9",
"Alignment": "\u05d9\u05d9\u05e9\u05d5\u05e8",
"Border color": "\u05e6\u05d1\u05e2 \u05d2\u05d1\u05d5\u05dc",
"Column group": "\u05e7\u05d9\u05d1\u05d5\u05e5 \u05e2\u05de\u05d5\u05d3\u05d5\u05ea",
"Row": "\u05e9\u05d5\u05e8\u05d4",
"Insert column before": "\u05d4\u05e2\u05ea\u05e7 \u05e2\u05de\u05d5\u05d3\u05d4 \u05dc\u05e4\u05e0\u05d9",
"Split cell": "\u05e4\u05e6\u05dc \u05ea\u05d0",
"Cell padding": "\u05e9\u05d5\u05dc\u05d9\u05d9\u05dd \u05e4\u05e0\u05d9\u05de\u05d9\u05d9\u05dd \u05dc\u05ea\u05d0",
"Cell spacing": "\u05e9\u05d5\u05dc\u05d9\u05d9\u05dd \u05d7\u05d9\u05e6\u05d5\u05e0\u05d9\u05dd \u05dc\u05ea\u05d0",
"Row type": "\u05e1\u05d5\u05d2 \u05e9\u05d5\u05e8\u05d4",
"Insert table": "\u05d4\u05db\u05e0\u05e1 \u05d8\u05d1\u05dc\u05d4",
"Body": "\u05d2\u05d5\u05e3 \u05d4\u05d8\u05d1\u05dc\u05d0",
"Caption": "\u05db\u05d9\u05ea\u05d5\u05d1",
"Footer": "\u05db\u05d5\u05ea\u05e8\u05ea \u05ea\u05d7\u05ea\u05d5\u05e0\u05d4",
"Delete row": "\u05de\u05d7\u05e7 \u05e9\u05d5\u05e8\u05d4",
"Paste row before": "\u05d4\u05d3\u05d1\u05e7 \u05e9\u05d5\u05e8\u05d4 \u05dc\u05e4\u05e0\u05d9",
"Scope": "\u05d4\u05d9\u05e7\u05e3",
"Delete table": "\u05de\u05d7\u05e7 \u05d8\u05d1\u05dc\u05d4",
"H Align": "\u05d9\u05d9\u05e9\u05d5\u05e8 \u05d0\u05d5\u05e4\u05e7\u05d9",
"Top": "\u05e2\u05dc\u05d9\u05d5\u05df",
"Header cell": "\u05db\u05d5\u05ea\u05e8\u05ea \u05dc\u05ea\u05d0",
"Column": "\u05e2\u05de\u05d5\u05d3\u05d4",
"Row group": "\u05e7\u05d9\u05d1\u05d5\u05e5 \u05e9\u05d5\u05e8\u05d5\u05ea",
"Cell": "\u05ea\u05d0",
"Middle": "\u05d0\u05de\u05e6\u05e2",
"Cell type": "\u05e1\u05d5\u05d2 \u05ea\u05d0",
"Copy row": "\u05d4\u05e2\u05ea\u05e7 \u05e9\u05d5\u05e8\u05d4",
"Row properties": "\u05de\u05d0\u05e4\u05d9\u05d9\u05e0\u05d9 \u05e9\u05d5\u05e8\u05d4",
"Table properties": "\u05de\u05d0\u05e4\u05d9\u05d9\u05e0\u05d9 \u05d8\u05d1\u05dc\u05d4",
"Bottom": "\u05ea\u05d7\u05ea\u05d9\u05ea",
"V Align": "\u05d9\u05d9\u05e9\u05d5\u05e8 \u05d0\u05e0\u05db\u05d9",
"Header": "\u05db\u05d5\u05ea\u05e8\u05ea",
"Right": "\u05d9\u05de\u05d9\u05df",
"Insert column after": "\u05d4\u05e2\u05ea\u05e7 \u05e2\u05de\u05d5\u05d3\u05d4 \u05d0\u05d7\u05e8\u05d9",
"Cols": "\u05e2\u05de\u05d5\u05d3\u05d5\u05ea",
"Insert row after": "\u05d4\u05d5\u05e1\u05e3 \u05e9\u05d5\u05e8\u05d4 \u05d0\u05d7\u05e8\u05d9",
"Width": "\u05e8\u05d5\u05d7\u05d1",
"Cell properties": "\u05de\u05d0\u05e4\u05d9\u05d9\u05e0\u05d9 \u05ea\u05d0",
"Left": "\u05e9\u05de\u05d0\u05dc",
"Cut row": "\u05d2\u05d6\u05d5\u05e8 \u05e9\u05d5\u05e8\u05d4",
"Delete column": "\u05de\u05d7\u05e7 \u05e2\u05de\u05d5\u05d3\u05d4",
"Center": "\u05de\u05e8\u05db\u05d6",
"Merge cells": "\u05de\u05d6\u05d2 \u05ea\u05d0\u05d9\u05dd",
"Insert template": "\u05d4\u05db\u05e0\u05e1 \u05ea\u05d1\u05e0\u05d9\u05ea",
"Templates": "\u05ea\u05d1\u05e0\u05d9\u05d5\u05ea",
"Background color": "\u05e6\u05d1\u05e2 \u05e8\u05e7\u05e2",
"Custom...": "\u05de\u05d5\u05ea\u05d0\u05dd \u05d0\u05d9\u05e9\u05d9\u05ea...",
"Custom color": "\u05e6\u05d1\u05e2 \u05de\u05d5\u05ea\u05d0\u05dd \u05d0\u05d9\u05e9\u05d9\u05ea",
"No color": "\u05dc\u05dc\u05d0 \u05e6\u05d1\u05e2",
"Text color": "\u05e6\u05d1\u05e2 \u05d4\u05db\u05ea\u05d1",
"Table of Contents": "\u05ea\u05d5\u05db\u05df \u05e2\u05e0\u05d9\u05d9\u05e0\u05d9\u05dd",
"Show blocks": "\u05d4\u05e6\u05d2 \u05ea\u05d9\u05d1\u05d5\u05ea",
"Show invisible characters": "\u05d4\u05e6\u05d2 \u05ea\u05d5\u05d5\u05d9\u05dd \u05dc\u05d0 \u05e0\u05e8\u05d0\u05d9\u05dd",
"Words: {0}": "\u05de\u05d9\u05dc\u05d9\u05dd: {0}",
"Insert": "\u05d4\u05d5\u05e1\u05e4\u05d4",
"File": "\u05e7\u05d5\u05d1\u05e5",
"Edit": "\u05e2\u05e8\u05d9\u05db\u05d4",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\u05ea\u05d9\u05d1\u05ea \u05e2\u05e8\u05d9\u05db\u05d4 \u05d7\u05db\u05de\u05d4. \u05dc\u05d7\u05e5 Alt-F9 \u05dc\u05ea\u05e4\u05e8\u05d9\u05d8. Alt-F10 \u05dc\u05ea\u05e6\u05d5\u05d2\u05ea \u05db\u05e4\u05ea\u05d5\u05e8\u05d9\u05dd, Alt-0 \u05dc\u05e2\u05d6\u05e8\u05d4",
"Tools": "\u05db\u05dc\u05d9\u05dd",
"View": "\u05ea\u05e6\u05d5\u05d2\u05d4",
"Table": "\u05d8\u05d1\u05dc\u05d4",
"Format": "\u05e4\u05d5\u05e8\u05de\u05d8",
"_dir": "rtl"
});
<?php

return [

    /*
    |--------------------------------------------------------------------------
    | JobSeeker Account Security Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains all the account security configuration for the <PERSON><PERSON>eeker
    | module including password policies, email verification, account lockout,
    | and social login security settings.
    |
    */

    'password_policy' => [
        'min_length' => env('JOBSEEKER_PASSWORD_MIN_LENGTH', 10),
        'require_uppercase' => env('JOBSEEKER_PASSWORD_REQUIRE_UPPERCASE', true),
        'require_lowercase' => env('JOBSEEKER_PASSWORD_REQUIRE_LOWERCASE', true),
        'require_numbers' => env('JOBSEEKER_PASSWORD_REQUIRE_NUMBERS', true),
        'require_special_chars' => env('JOBSEEKER_PASSWORD_REQUIRE_SPECIAL', true),
        'check_breached_passwords' => env('JOBSEEKER_CHECK_BREACHED_PASSWORDS', false),
        'prevent_personal_info' => env('JOBSEEKER_PREVENT_PERSONAL_INFO', true),
        'min_change_hours' => env('JOBSEEKER_MIN_PASSWORD_CHANGE_HOURS', 1),
        'history_limit' => env('JOBSEEKER_PASSWORD_HISTORY_LIMIT', 5),
    ],

    'email_verification' => [
        'enabled' => env('JOBSEEKER_EMAIL_VERIFICATION_ENABLED', true),
        'token_expiry_minutes' => env('JOBSEEKER_EMAIL_TOKEN_EXPIRY', 60),
        'require_for_sensitive_actions' => env('JOBSEEKER_REQUIRE_VERIFICATION_FOR_SENSITIVE', true),
        'resend_cooldown_minutes' => env('JOBSEEKER_EMAIL_RESEND_COOLDOWN', 5),
        'max_resend_attempts' => env('JOBSEEKER_MAX_EMAIL_RESEND_ATTEMPTS', 3),
        'reverify_on_email_change' => env('JOBSEEKER_REVERIFY_ON_EMAIL_CHANGE', true),
    ],

    'account_lockout' => [
        'enabled' => env('JOBSEEKER_ACCOUNT_LOCKOUT_ENABLED', true),
        'max_failed_attempts' => env('JOBSEEKER_MAX_FAILED_ATTEMPTS', 5),
        'lockout_duration_minutes' => env('JOBSEEKER_LOCKOUT_DURATION', 30),
        'escalation_enabled' => env('JOBSEEKER_LOCKOUT_ESCALATION_ENABLED', true),
        'escalation_thresholds' => [
            1 => env('JOBSEEKER_LOCKOUT_1ST', 30),    // 30 minutes
            2 => env('JOBSEEKER_LOCKOUT_2ND', 60),    // 1 hour
            3 => env('JOBSEEKER_LOCKOUT_3RD', 120),   // 2 hours
            4 => env('JOBSEEKER_LOCKOUT_4TH', 240),   // 4 hours
            5 => env('JOBSEEKER_LOCKOUT_5TH', 480),   // 8 hours
        ],
        'send_notifications' => env('JOBSEEKER_LOCKOUT_NOTIFICATIONS', true),
        'unlock_request_min_hours' => env('JOBSEEKER_UNLOCK_REQUEST_MIN_HOURS', 1),
        'ip_rate_limit' => env('JOBSEEKER_IP_FAILED_ATTEMPTS_LIMIT', 20), // per hour
    ],

    'social_login' => [
        'google' => [
            'enabled' => env('JOBSEEKER_GOOGLE_LOGIN_ENABLED', true),
            'validate_email_domain' => env('JOBSEEKER_GOOGLE_VALIDATE_DOMAIN', false),
            'allowed_domains' => env('JOBSEEKER_GOOGLE_ALLOWED_DOMAINS', ''),
            'auto_verify_email' => env('JOBSEEKER_GOOGLE_AUTO_VERIFY', true),
            'prevent_account_takeover' => env('JOBSEEKER_PREVENT_ACCOUNT_TAKEOVER', true),
            'require_state_parameter' => env('JOBSEEKER_GOOGLE_REQUIRE_STATE', true),
        ],
    ],

    'session_security' => [
        'regenerate_on_login' => env('JOBSEEKER_REGENERATE_SESSION_ON_LOGIN', true),
        'timeout_minutes' => env('JOBSEEKER_SESSION_TIMEOUT', 120), // 2 hours
        'concurrent_sessions_limit' => env('JOBSEEKER_MAX_CONCURRENT_SESSIONS', 3),
        'track_login_devices' => env('JOBSEEKER_TRACK_LOGIN_DEVICES', true),
        'notify_new_device_login' => env('JOBSEEKER_NOTIFY_NEW_DEVICE', true),
    ],

    'security_monitoring' => [
        'enabled' => env('JOBSEEKER_SECURITY_MONITORING', true),
        'log_failed_attempts' => env('JOBSEEKER_LOG_FAILED_ATTEMPTS', true),
        'log_successful_logins' => env('JOBSEEKER_LOG_SUCCESSFUL_LOGINS', true),
        'log_password_changes' => env('JOBSEEKER_LOG_PASSWORD_CHANGES', true),
        'alert_on_suspicious_activity' => env('JOBSEEKER_ALERT_SUSPICIOUS_ACTIVITY', true),
        'suspicious_activity_thresholds' => [
            'multiple_ip_logins' => env('JOBSEEKER_MULTIPLE_IP_THRESHOLD', 3), // within 1 hour
            'rapid_password_changes' => env('JOBSEEKER_RAPID_PASSWORD_CHANGES', 3), // within 24 hours
            'failed_attempts_different_ips' => env('JOBSEEKER_FAILED_ATTEMPTS_DIFFERENT_IPS', 10), // within 1 hour
        ],
    ],

    'two_factor_authentication' => [
        'enabled' => env('JOBSEEKER_2FA_ENABLED', false),
        'required_for_privileged_accounts' => env('JOBSEEKER_2FA_REQUIRED_PRIVILEGED', false),
        'backup_codes_count' => env('JOBSEEKER_2FA_BACKUP_CODES', 8),
        'totp_issuer' => env('JOBSEEKER_2FA_ISSUER', 'JobSeeker Platform'),
        'recovery_phone_verification' => env('JOBSEEKER_2FA_PHONE_RECOVERY', false),
    ],

    'data_protection' => [
        'encrypt_sensitive_fields' => env('JOBSEEKER_ENCRYPT_SENSITIVE_FIELDS', true),
        'mask_email_in_logs' => env('JOBSEEKER_MASK_EMAIL_LOGS', true),
        'password_reset_link_expiry' => env('JOBSEEKER_PASSWORD_RESET_EXPIRY', 60), // minutes
        'delete_inactive_accounts_after_days' => env('JOBSEEKER_DELETE_INACTIVE_AFTER_DAYS', 365),
        'anonymize_deleted_accounts' => env('JOBSEEKER_ANONYMIZE_DELETED_ACCOUNTS', true),
    ],

    'api_security' => [
        'require_api_key' => env('JOBSEEKER_API_REQUIRE_KEY', false),
        'rate_limit_per_user' => env('JOBSEEKER_API_RATE_LIMIT_USER', 200), // per hour
        'rate_limit_per_ip' => env('JOBSEEKER_API_RATE_LIMIT_IP', 100), // per hour
        'cors_allowed_origins' => env('JOBSEEKER_API_CORS_ORIGINS', '*'),
        'validate_referrer' => env('JOBSEEKER_API_VALIDATE_REFERRER', false),
    ],

]; 
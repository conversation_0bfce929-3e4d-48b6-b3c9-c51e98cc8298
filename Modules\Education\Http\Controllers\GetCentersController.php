<?php

namespace Modules\Education\Http\Controllers;

use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\MoshafJuz;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStaatisticsRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class GetCentersController extends Controller
{

    public function __invoke(Request $request)
    {

//        $centers = Center::whereHas("programs", function ($q) use ($request) {
//            return $q->where('program_id', $request->id);
//        })->get();
//
//
//        return response()->json([$centers]);



        // Retrieve the authenticated employee from the 'employee' guard.
        $employee = auth()->guard('employee')->user();

        // Build the base query: centers that have the specified program.
        $centersQuery = Center::whereHas('programs', function ($q) use ($request) {
            $q->where('program_id', $request->id);
        });

        // For supervisors (non–managing directors), restrict centers to those attached to the employee.
        if (!$employee->hasRole('managing-director_' . config('organization_id') . '_')) {
            $centersQuery->whereIn('centers.id', function ($query) use ($employee) {
                $query->select('cen_id')
                    ->from('cen_emps')
                    ->where('emp_id', $employee->id);
            });
        }

        $centers = $centersQuery->get();

        return response()->json([$centers]);



    }
}

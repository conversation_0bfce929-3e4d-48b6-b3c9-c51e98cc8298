<?php

namespace Modules\HumanResource\Http\Controllers;

use App\CenterTranslation;
use App\EmployeeSalary;
use App\LeaveDefine;
use App\LeaveType;
use App\Organization;
use App\Student;
use Carbon\Traits\Creator;
use Doctrine\DBAL\Cache\CacheException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;

use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;
use Jenssegers\Agent\Agent;

use App\Employee;

use App\Authorizable;

use App\Attendance;
use Carbon\Carbon;
use App\LeaveRequest;
use App\Role;


class EmployeesMonthlyAttendanceReportController extends Controller
{
    // use Authorizable;
    /**
     * Display a listing of the resource.
     * @return Response
     */

    /**
     * Show the form for creating a new resource.
     * @return Response
     */

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */


    public
    function __invoke(Request $request, $date = null)
    {



        $roles = $request->filled('role_id') ? $request->get('role_id') : Role::where('type', 'regular_user')->get()->pluck('id')->toArray();
        $m = $request->filled('month') ? $request->get('month') : date('m');
        $y = $request->filled('year') ? $request->get('year') : date('Y');

        $teacherCenters = $request->filled('teacherCenters') ? (array)$request->get('teacherCenters') : [];
        $teacherCenters = !empty($teacherCenters) ? implode(',', $teacherCenters) : null;

        $r = implode(',', $roles);


//        dd($m, $y, $r,$teacherCenters);

        if ((auth()->user()->hasRole(['education-manager_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {

            $supervisorAndTeachersRolesIds = $request->filled('role_id') ? $request->get('role_id') : Role::whereIn('description', ['Supervisor', 'Teacher'])->get()->pluck('id')->toArray();



            $r = implode(',', $supervisorAndTeachersRolesIds);




            $attendance = \DB::select('CALL GetEmployeeMonthlyAttendanceReportByYearMonthRole(?,?,?,?)', [$m, $y, $r,$teacherCenters]);


        } elseif ((auth()->user()->hasRole(['supervisor_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_','education-manager_' . config('organization_id') . '_']))) {
            $teacherRoleId = \DB::table('roles')->where('description', 'Teacher')->pluck('id');
            $supervisorAndTeachersRolesIds = $teacherRoleId->toArray();
            $r = implode(',', $supervisorAndTeachersRolesIds);

            $c = auth()->user()->center()->get()->pluck('id')->toArray();
            $teacherCenters = implode(',', $c);


            $attendance = \DB::select('CALL GetEmployeeMonthlyAttendanceReportByYearMonthRole(?,?,?,?)', [$m, $y, $r, $teacherCenters]);


        } else {



            if ($request->has('month') && $request->has('year') && $request->has('role_id')) {
//                $r = implode(',', $r);


                $attendance = \DB::select('CALL GetEmployeeMonthlyAttendanceReportByYearMonthRole(?,?,?,?)', [$m, $y, $r,$teacherCenters]);


            } else {



                $allRoles = Role::where('type', 'regular_user')->get()->pluck('id')->toArray();
                $r = implode(',', $allRoles);


                $attendance = \DB::select('CALL GetEmployeeMonthlyAttendanceReportByYearMonthRole(?,?,?,?)', [$m, $y, $r,$teacherCenters]);


            }


        }







        $months = [1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April', 5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August', 9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'];

        return view(
            'humanresource::attendance.monthly_report_latest',
            compact('attendance', 'months', 'm', 'y', 'teacherCenters')
        );
    }

    public
    function monthly_report_search(Request $request)
    {

        $date = null;
        $id = $request->role;
        if ($id == '0') {
            return $this->monthly_report($request);
        }


        $employee = Employee::whereHas('roles', function ($q) use ($id) {

            return $q->where('id', $id);
        })->get();


        $role = Role::get();
        foreach ($employee as $item) {
            $report = $this->report($item->id, $date);
            extract($report);
            $items[] = $report;
        }


        $attendance = Attendance:: whereYear('created_at', '=', date('Y'))
            ->whereMonth('created_at', '=', date('m'))->orderBy('employee_id')
            ->get();


        return view(
            'humanresource::attendance.monthly_report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'role',
                'date',
                'items'
            )
        );


    }


}

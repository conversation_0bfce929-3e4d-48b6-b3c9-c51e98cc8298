<?php

namespace Modules\HumanResource\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Validation\Validator;

class EmployeeStoreRequest extends FormRequest
{
    protected function failedValidation(Validator $validator)
    {
        // Flash the password value to the session
        $this->session()->flashInput(['password' => $this->input('password')]);

        throw new ValidationException($validator);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {



//        dd($this->request->all());


        return [
            'bank_name' => 'sometimes|array',
            'bank_name.*' => 'nullable|string',
            'bank_account_type' => 'required_with:bank_name|array',
            'bank_account_type.*' => 'required_with:bank_name.*',
            'bank_account_name' => 'required_with:bank_name|array',
            'bank_account_name.*' => 'required_with:bank_name.*',
            'bank_account_no' => 'required_with:bank_name|array',
            'bank_account_no.*' => 'required_with:bank_name.*',
            'payroll_bank_account_no' => 'sometimes|nullable',
            'contract_type' => 'required',
//            'employee_number' => 'required|min:3|unique:employees,employee_number',
            'employee_number' => [
                'required',
                Rule::in([generateEmployeeNumber()]),
            ],
            'full_name' => 'bail|required|min:2',
            'basic_salary' => 'required',
            'name' => 'bail|required|min:2',
            'hours_per_month' => 'sometimes|nullable|numeric|gt:3',
            'email' => 'required|email|unique:employees',
            'mobile' => 'required|unique:employees,mobile|max:30',
            'work_mood' => 'required',
            'password' => 'required|string|min:8',
            'date_of_birth' => 'required',
            'gender' => 'required',
            'roles' => 'required|array',
            'marital_status' => 'required',
            'identity_type' => 'required',
            'identity' => 'required',
            'nationality' => 'required',
            'teacher_center' => 'required_if:roles,teacher_2_',
//            'halaqah_start_date' => 'required_if:roles,teacher_2_',
            'halaqah_start_date' => 'required_with:teacher_center',
            'supervisor_center' => 'required_if:roles,supervisor_2_',
            'resume' => "sometimes|nullable|max:40000",
            'joining_letter' => "sometimes|nullable|mimes:pdf,doc,docx|max:40000",
            'other_document' => "sometimes|nullable|mimes:pdf,doc,docx,jpg,jpeg,png|max:10000",
            'days' => 'required_if:work_mood,4' /** 4 = per_month */,
            'start' => 'required_with:days' ,
            'end' => 'required_with:days' /** 4 = per_month */
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    public function messages()
    {
        return [
            'days.required_if' => 'The days field is required when work mood is monthly',
            'start.required_with' => 'The start time for a day should be defined when day is selected',
            'end.required_with' => 'The start time for a day should be defined when day is selected',



        ];

    }

}

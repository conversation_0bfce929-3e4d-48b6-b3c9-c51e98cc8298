<?php

namespace App\Providers;

use App\ClassSubjectTimetable;
use App\EmployeeSalary;
use App\GeneralSettings;
use App\Helpers\PasswordBrokerManagerHelper;
use App\MissedClockOut;
use App\Notification;
use App\Observers\StudentObserver;
use App\Observers\TeacherTimetableObserver;
use App\OverridenPackageClasses\unisharp\laravelFilemanager\DeleteController;
use App\Repositories\UserRepository;
use App\Repositories\UserRepositoryInterface;
use App\Role;
use App\Student;
use App\StudentHefzReport;
use App\User;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Resources\Json\JsonResource;
//use Illuminate\Http\Resources\Json\Resource;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;
use Module;
use Auth;
use App\Organization;
use App\Setting;
use App\Attendance;
use Illuminate\Routing\UrlGenerator;
use Modules\RolePermission\Entities\PermissionAssign;
use PostHog\PostHog;
use function Amp\Iterator\map;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(UrlGenerator $url)
    {
        // Skip certain initializations when running in console to prevent serialization issues
        $isRunningInConsole = $this->app->runningInConsole();
        
        // Create menu usage statistics table if it doesn't exist
        if (!$isRunningInConsole) {
            $this->createMenuUsageStatisticsTable();
            // Register the menu usage tracking route
            $this->registerMenuTracking();
        }

        // Initialize PostHog - make sure this is safe to serialize
        if (!$isRunningInConsole) {
            PostHog::init(
                'phc_5mIr79YnYxIVmSJaCUrCWb2nxCKFqlLQHprGMUhN2Zp',
                [
                    'host' => 'https://us.i.posthog.com'
                ]
            );
        }

        Paginator::useBootstrap();

        JsonResource::wrap('items');
        
        // Custom Blade directives for system viewer support
        $this->registerSystemViewerBladeDirectives();
        if(env('APP_ENV') == 'production') {
            \URL::forceScheme('https');
        }

        //disable error reporting
        error_reporting(0);

        //  Check domain and set organization settings
        $domain = str_replace('http://', '', request()->root());
        $domain = str_replace('https://', '', $domain);

        // Only run this logic if not running in the console
        if (!app()->runningInConsole()) {
        if ($domain !== config('app.platform_domain')) {
            $organization = Organization::where("domain", "=", str_replace('www.', '', $domain))->first();

            if (!$organization) {
                $username = str_replace('.' . config('app.platform_domain'), '', $domain);

                $organization = Organization::where("username", "=", $username)->first();
            }
            if ($organization) {
                config(['app.locales' => explode('|', $organization->languages)]);

                config(['organization_id' => $organization->id]);

                config(['domain' => $domain]);

                if ($organization->timezone) {
                    config(['app.timezone' => $organization->timezone]);
                    date_default_timezone_set(config('app.timezone'));
                }

                $settings = Setting::all()->pluck('value', 'name')->toArray();

                config(['website_theme' => $organization->theme ?? 'wajeha']);
                config(['logo' => $organization->logo ?? 'images/no-image.png']);

                config(['settings' => $settings]);
                // auth()->guard('employee')->loginUsingId(82);
            }

        }
        else {
            config(['is_platform' => true]);
            }
        }

        //  Get language code from url
        if (request()->segment(1) == 'workplace') {
        }
        else {
            // get the first segment of url which suppose it a langauge code
            $language = request()->segment(1);

            // check if the url language code is available in app locales
            if ($language && in_array($language, (config('app.locales')))) {
                App::setLocale($language);
                // set app.locale to url language code
//                config(['app.locale' => $language]);
            }
        }
        
        // Skip view composers when running in console to prevent serialization issues
        if (!$isRunningInConsole) {
            /**
             * Add latest attendence info to form_block of workplace
             */
            view()->composer(['forms.attendance.form_block'], function ($view) {
//            $last_attendance_record = Attendance::where('employee_id', auth()->user()->id)->latest()->first();

                $last_attendance_record = Attendance::where('employee_id', auth()->user()->id)->orderBy("id", "desc")->latest()->first();

                $view->with('last_attendance_record', $last_attendance_record);
            });

            /**
             * Add modules' units to the main layout of workplace
             */

            view()->composer(['layouts.*', 'packages.*', 'general::roles.form', 'general::dashboards.*'], function ($view) {
//            $modules = \Cache::rememberForever('modules', function () {
                $modules = [];

                foreach (Module::all() as $key => $value) {
                    // Skip JobSeeker module
                    if ($key === 'JobSeeker') {
                        continue;
                    }

                    $config = config(strtolower($key));

                    if (isset($config['units']) && (!Auth::guest())) {
                        $units = $config['units'];
                        $moduleKey = trans(strtolower($key) . "::units.{$key}");
                        $modules[$moduleKey] = [];

                        foreach ($units as $group => $unit) {
                            $module = [
                                "url" => url('workplace/' . strtolower($key) . '/' . strtolower($group)),
                                "icon" => $unit['icon'],
                                "name" => trans(strtolower($key) . "::units.{$group}"),
                                "actions" => $unit['actions']
                            ];

                            $modules[$moduleKey][] = $module;
                        }
                    }
                }

                // Check current route against parent modules to determine active state
                $currentUrl = url()->current();
                $currentPath = parse_url($currentUrl, PHP_URL_PATH);
                $isParentActive = false;
                $parentUrl = '';
                $activeParentKey = '';

                // Check if current URL is within any module's URL structure
                foreach ($modules as $moduleKey => $moduleItems) {
                    foreach ($moduleItems as $item) {
                        $itemPath = parse_url($item['url'], PHP_URL_PATH);
                        
                        // First check for exact match
                        if ($currentUrl === $item['url']) {
                            $isParentActive = true;
                            $parentUrl = $moduleKey; 
                            $activeParentKey = $moduleKey;
                            break 2;
                        }
                        
                        // Then check if current path starts with the item path (for sub-pages)
                        // Only match if itemPath is not empty and is a complete path segment
                        if (!empty($itemPath) && 
                            strpos($currentPath, $itemPath) === 0 && 
                            (strlen($currentPath) === strlen($itemPath) || substr($currentPath, strlen($itemPath), 1) === '/')) {
                            $isParentActive = true;
                            $parentUrl = $moduleKey;
                            $activeParentKey = $moduleKey;
                            break 2;
                        }
                    }
                }

                // If we're on the dashboard page, don't mark any module as active
                if (preg_match('#^/workplace/?$#', $currentPath)) {
                    $isParentActive = false;
                    $parentUrl = '';
                    $activeParentKey = '';
                }

//                return $modules;
//            });

                //   share the attendance button status
                // check if the user last attendance was  entry or exit
                $missedClockOut = MissedClockOut::where('employee_id',auth()->guard('employee')->user()->id)->first();
                $lastAttendanceClock = optional(Attendance::withTrashed()->where('employee_id',Auth::guard('employee')->user()->id)
                    ->orderBy('clock')->get()->last()->clock);

                $notifications = Notification::where('user_id',auth()->user()->id)->whereNull('read_at')->latest()->get();

                // Add event handlers to track menu clicks in JavaScript
                $this->addMenuTrackingScript($view);

                $view->with(['modules' => $modules,
//                'lastAttendanceType'=>$lastAttendanceType,
//                'lastAttendanceStatus' => $lastAttendanceStatus,
                    'lastAttendanceClock' =>$lastAttendanceClock,
                    'missedClockOut' =>$missedClockOut,
                    'notifications' =>$notifications,
                    'isParentActive' => $isParentActive,
                    'parentUrl' => $parentUrl,
                    'activeParentKey' => $activeParentKey,
//                'firstClockInAttendanceofTheDay' =>$firstClockInAttendanceofTheDay,
//                'hefzMonthlyPlanApprovalRequired' =>$hefzMonthlyPlanApprovalRequired,
               ]);
            });

            view()->composer('site::templates.itqan.student.profile_form', function ($view) {
                $centers = [];

                $programs = [];

                $view->with(['programs' => $programs, 'centers' => $centers]);
            });
        }

        if (!app()->isLocal()) {
            StudentHefzReport::updated(function ($report) {
                \Cache::tags('StudentHefzReport')->flush();
            });

            StudentHefzReport::created(function ($report) {
                \Cache::tags('StudentHefzReport')->flush();
            });

            StudentHefzReport::deleted(function ($report) {
                \Cache::tags('StudentHefzReport')->flush();
            });
        }

        // Share auth data with all views
        View::composer('*', function ($view) {
            // Try to get the authenticated user ID from any guard
            $userId = null;
            $guards = ['web', 'student', 'guardian', 'employee', 'superior', 'organization'];
            
            foreach ($guards as $guard) {
                if (Auth::guard($guard)->check()) {
                    $userId = Auth::guard($guard)->id();
                    break;
                }
            }
            
            // Share the user ID with the view
            $view->with('userId', $userId);
        });

    
        // Listen for migrations to update database documentation
        if ($this->app->environment() !== 'production') {
            $this->app['events']->listen(
                \Illuminate\Database\Events\MigrationsEnded::class,
                function () {
                    $this->updateDatabaseDocumentation();
                }
            );
        }
    }

    /**
     * Create menu usage statistics table if it doesn't exist
     *
     * @return void
     */
    protected function createMenuUsageStatisticsTable()
    {
        try {
            if (!Schema::hasTable('menu_usage_statistics')) {
                Schema::create('menu_usage_statistics', function ($table) {
                    $table->id();
                    $table->unsignedBigInteger('user_id')->nullable();
                    $table->string('menu_title');
                    $table->string('menu_url');
                    $table->string('parent_menu')->nullable();
                    $table->integer('access_count')->default(1);
                    $table->timestamp('last_accessed_at')->useCurrent();
                    $table->string('session_id')->nullable();
                    $table->string('ip_address', 45)->nullable();
                    $table->string('user_agent')->nullable();
                    $table->timestamps();
                    
                    $table->index('user_id');
                    $table->index('menu_url');
                    $table->index('menu_title');
                });
                
                Log::info('Menu usage statistics table created successfully');
            }
        } catch (\Exception $e) {
            Log::error('Failed to create menu usage statistics table: ' . $e->getMessage());
        }
    }

    /**
     * Register the menu usage tracking route
     *
     * @return void
     */
    protected function registerMenuTracking()
    {
        // Add route outside of the normal route files
        Route::group(['middleware' => ['web', 'demo.data'], 'prefix' => 'workplace'], function () {
            // Track menu usage
            Route::post('/track-menu-usage', 'App\Http\Controllers\MenuTrackingController@trackMenuUsage')
                ->name('track-menu-usage')
                ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class);
            
            // Statistics routes
            Route::get('/menu-statistics/most-accessed', 'App\Http\Controllers\MenuTrackingController@getMostAccessedMenus')
                ->name('menu-statistics.most-accessed');
            
            Route::get('/menu-statistics/recent', 'App\Http\Controllers\MenuTrackingController@getRecentMenus')
                ->name('menu-statistics.recent');
        });
    }
    
    /**
     * Add menu tracking JavaScript to the view
     *
     * @param \Illuminate\View\View $view
     * @return void
     */
    protected function addMenuTrackingScript($view)
    {
        // This method is now deprecated since we include menu-tracking.js directly in the layout
        // We'll keep it here for backward compatibility but it doesn't do anything
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('auth.password', function ($app) {
            return new PasswordBrokerManagerHelper($app);
        });
        $this->app->bind('auth.password.broker', function ($app) {
            return $app->make('auth.password')->broker();
        });

        $this->app->bind(\UniSharp\LaravelFilemanager\Controllers\DeleteController::class, function() {
            return new DeleteController;
        });

        $this->app->bind(UserRepositoryInterface::class,UserRepository::class);

        // Register Circuit Breaker Service for Epic 6
        $this->app->singleton(\App\Services\CircuitBreakerService::class, function ($app) {
            return new \App\Services\CircuitBreakerService();
        });

        // Register Email Service with Circuit Breaker dependency
        $this->app->singleton(\App\Services\EmailService::class, function ($app) {
            return new \App\Services\EmailService(
                $app->make(\App\Services\CircuitBreakerService::class)
            );
        });

        // Register Theme Service
        $this->app->singleton(\App\Services\ThemeService::class, function ($app) {
            return new \App\Services\ThemeService();
        });

        // Register Settings Service
        $this->app->singleton(\App\Services\SettingsService::class, function ($app) {
            return new \App\Services\SettingsService();
        });

        // Register Widget Service
        $this->app->singleton(\App\Services\WidgetService::class, function ($app) {
            return new \App\Services\WidgetService();
        });

        if ($this->app->environment() == 'local') {
            $this->app->register('Hesto\MultiAuth\MultiAuthServiceProvider');
//            $this->app->register('BeyondCode\ErdGenerator\ErdGeneratorServiceProvider');
        }
        if ($this->app->environment('local', 'testing')) {
//            $this->app->register('Laravel\Dusk\DuskServiceProvider');
        }

        $this->app->singleton('permission', function () {
            $Roles = Role::whereIn('id',\Illuminate\Support\Facades\Auth::user()->roles()->pluck('id'))->get();

            $module_links = [];

            $permissions = PermissionAssign::whereIn('role_id', $Roles->pluck('id'))->get(['id', 'module_id']);

            foreach ($permissions as $permission) {
                $module_links[] = $permission->module_id;
            }
            //All user permission module id save in session
            $permission = $module_links;

            return $permission;
        });





        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
    }

    /**
     * Update database documentation after migrations
     */
    protected function updateDatabaseDocumentation()
    {
        try {
            $scriptPath = base_path('ai_docs/architectureDiagrams/db_docs_cron.sh');
            
            if (file_exists($scriptPath)) {
                $process = proc_open("bash $scriptPath", [
                    0 => ["pipe", "r"],
                    1 => ["pipe", "w"],
                    2 => ["pipe", "w"],
                ], $pipes);
                
                if (is_resource($process)) {
                    foreach ($pipes as $pipe) {
                        fclose($pipe);
                    }
                    
                    proc_close($process);
                    \Log::info('Database documentation updated after migration');
                }
            }
        } catch (\Exception $e) {
            \Log::error('Failed to update database documentation after migration: ' . $e->getMessage());
        }
    }
    
    /**
     * Register custom Blade directives for system viewer support
     */
    protected function registerSystemViewerBladeDirectives()
    {
        \Blade::directive('systemViewerCan', function ($expression) {
            return "<?php if (auth()->check() && (auth()->user()->hasRole('system_viewer_' . config('organization_id') . '_') || auth()->user()->can({$expression}))): ?>";
        });
        
        \Blade::directive('endsystemViewerCan', function () {
            return '<?php endif; ?>';
        });
    }
}

<?php

namespace Modules\General\Http\Controllers;

use App\StudentNouranyaPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NouranyaPlanController
{
// StudentNouranyaPlansController.php


    public function getNouranyaBasedClasses()
    {
        try {
            // Assuming you have a model called `NouranyaPlan` which is linked to the classes
            $nouranyaplanBasedClasses = StudentNouranyaPlan::with('halaqah')
                ->get()
                ->map(function ($nouranyaPlan) {
                    // Check if the halaqah relationship is loaded and is not null
                    if ($nouranyaPlan->halaqah) {
                        return [
                            'id' => $nouranyaPlan->halaqah->id,
                            'name' => $nouranyaPlan->halaqah->name
                        ];
                    }
                })
                ->filter() // This will remove any null values that might have been added in the map due to missing halaqahs
                ->unique('id') // This will filter out duplicate classes by their 'id'
                ->values(); // This will reset the keys to ensure they are sequential after filtering


            return response()->json($nouranyaplanBasedClasses);
        } catch (\Exception $e) {
            // Handle the error accordingly
            Log::error('Error fetching nouranyaplan-based classes: ' . $e->getMessage());
            return response()->json(['error' => 'Unable to fetch classes'], 500);
        }
    }



}
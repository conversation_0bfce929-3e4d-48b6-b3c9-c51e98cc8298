@startuml Deployment Diagram (Example)

!theme vibrant

title Deployment Diagram for Itqan ERP System (Example Cloud Setup)

' Include C4 specific macros/styling
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Deployment.puml

' Actors
Person(admin, "Administrator")
Person(staff, "Staff/Teacher")
Person(student, "Student/Parent")

' External Systems
System_Ext(email_service, "Email Service", "e.g., Mailgun, SES")
System_Ext(payment_gateway, "Payment Gateway", "e.g., Stripe, PayPal")
System_Ext(sms_gateway, "SMS Gateway", "e.g., Twilio")

' Deployment Environment (e.g., AWS Cloud)
Deployment_Node(cloud, "Cloud Provider (e.g., AWS, Azure, GCP)", "IaaS/PaaS") {

    Deployment_Node(web_servers, "Web Server Cluster", "e.g., EC2 Auto Scaling Group / Kubernetes Pods") {
        ' Container Deployed: Web Application
        Container(webapp, "Web Application", "PHP, Laravel 10", "Serves UI and API requests.")
    }

    Deployment_Node(worker_servers, "Worker Server Cluster", "e.g., EC2 Auto Scaling Group / Kubernetes Pods") {
        ' Container Deployed: Queue Worker
        Container(queue_worker, "Queue Worker", "PHP, Laravel Queue", "Processes background jobs.", $sprite="gear")
    }

    Deployment_Node(db_service, "Managed Database Service", "e.g., AWS RDS / Azure SQL DB / Google Cloud SQL (MySQL)") {
        ' Container Deployed: Database
        ContainerDb(db, "Database", "MySQL", "Stores all application data.")
    }

    Deployment_Node(cache_service, "Managed Cache Service", "e.g., AWS ElastiCache / Azure Cache / Google Memorystore (Redis/Memcached)") {
        ' Container Deployed: Cache
        Container(cache, "Cache", "Redis/Memcached", "Stores sessions, cached data.")
    }

    Deployment_Node(storage_service, "Object Storage Service", "e.g., AWS S3 / Azure Blob Storage / Google Cloud Storage") {
        ' Container Deployed: File Storage
        Container(storage, "File Storage", "Stores uploaded files, backups.")
    }

    Deployment_Node(load_balancer, "Load Balancer", "e.g., ELB, Azure LB, Google LB")

    ' Relationships between Nodes
    Rel(load_balancer, web_servers, "Routes HTTP/S traffic to", "HTTPS")
    Rel(web_servers, db_service, "Reads/Writes data", "SQL/TCP")
    Rel(web_servers, cache_service, "Reads/Writes session/cache data", "TCP")
    Rel(web_servers, storage_service, "Reads/Writes files", "HTTPS/API")
    Rel(web_servers, worker_servers, "Dispatches jobs via queue", "Queue Protocol (e.g., Redis/DB)") ' Indirect via Queue Service

    Rel(worker_servers, db_service, "Reads/Writes data", "SQL/TCP")
    Rel(worker_servers, cache_service, "Reads/Writes data", "TCP")
    Rel(worker_servers, storage_service, "Reads/Writes files", "HTTPS/API")
}

' Relationships from Actors to Load Balancer
Rel(admin, load_balancer, "Accesses system via", "HTTPS")
Rel(staff, load_balancer, "Accesses system via", "HTTPS")
Rel(student, load_balancer, "Accesses system via", "HTTPS")

' Relationships to External Systems (Typically from Web/Worker Nodes)
Rel(web_servers, email_service, "Sends emails", "API/SMTP")
Rel(web_servers, sms_gateway, "Sends SMS", "API")
Rel(web_servers, payment_gateway, "Processes payments", "HTTPS/API")
Rel_Back(web_servers, payment_gateway, "Receives status", "Webhook/HTTPS")

Rel(worker_servers, email_service, "Sends emails", "API/SMTP")
Rel(worker_servers, sms_gateway, "Sends SMS", "API")

@enduml 
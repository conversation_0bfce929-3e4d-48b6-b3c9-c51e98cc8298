.loader_bak {
    background: transparent;
}

.alignment {
    display: flex;
    height: 100%;
    justify-content: center;
}
.pulse-loader {
    height: 50px;
    margin: auto 0;
    position: relative;
    width: 50px;
}
.pulse-loader:before, .pulse-loader:after {
    animation-name: pulse-loader;
    animation-iteration-count: infinite;
    animation-duration: 2s;
    box-shadow: 0 0 5px #6290d1;
    border-radius: 50%;
    border: 3px solid #6290d1;
    content: '';
    display: block;
    height: 100%;
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    width: 100%;
}
.pulse-loader:after {
    animation-delay: 1s;
}
@keyframes pulse-loader {
    0% {
        opacity: 0;
        transform: scale(0);
   }
    60% {
        opacity: 1;
   }
    100% {
        opacity: 0;
        transform: scale(1);
   }
}
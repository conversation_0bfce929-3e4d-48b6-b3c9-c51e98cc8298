<?php

namespace App\Http\Middleware;

use App\Role;
use Closure;

class Permission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {




//        if(auth()->user()->is('system_user'))
//        {
            return $next($request);
//        }


        $roles = Role::all();
        $role = $roles->whereIn('id',auth()->user()->roleIds())->first();



        if(".create" == substr($request->route()->getName(), -7)){
            if($role != null && $role->permissions->contains('route',str_replace(".create", ".store",$request->route()->getName()))){
                return $next($request);
            }else{
                abort(401);
            }
        }
        if(".update" == substr($request->route()->getName(), -7)){
            if($role != null && $role->permissions->contains('route',str_replace(".update", ".edit",$request->route()->getName()))){
                return $next($request);
            }else{
                abort(401);
            }
        }else{
            $counter = 0;
            $arr = [];
            foreach(auth()->user()->getAllPermissions() as $permission){
                $arr[] = $permission->route;

                if ($permission->route == $request->route()->getName())
                {


                    $counter++;



                }






            }

            dd($arr,collect(auth()->user()->getAllPermissions()),$request->route()->getName(),auth()->user()->getAllPermissions()->contains('route',$request->route()->getName()));
            if($role != null && $role->permissions->contains('route',$request->route()->getName())){
                return $next($request);
            }else{
                abort(401);
            }
        }
    }
}

<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Defaults
    |--------------------------------------------------------------------------
    |
    | This option controls the default authentication "guard" and password
    | reset options for your application. You may change these defaults
    | as required, but they're a perfect start for most applications.
    |
    */

    'defaults' => [
        'guard' => 'web',
        'passwords' => 'users',
    ],

    /*
    |--------------------------------------------------------------------------
    | Authentication Guards
    |--------------------------------------------------------------------------
    |
    | Next, you may define every authentication guard for your application.
    | Of course, a great default configuration has been defined for you
    | here which uses session storage and the Eloquent user provider.
    |
    | All authentication drivers have a user provider. This defines how the
    | users are actually retrieved out of your database or other storage
    | mechanisms used by this application to persist your user's data.
    |
    | Supported: "session", "token"
    |
    */

    'guards' => [

        'job_seeker' => [
            'driver' => 'session',
            'provider' => 'job_seekers',
        ],

        'sponsor' => [
            'driver' => 'session',
            'provider' => 'sponsors',
        ],

        'superior' => [
            'driver' => 'session',
            'provider' => 'superiors',
        ],

        'student' => [
            'driver' => 'session',
            'provider' => 'students',
        ],

        'employee' => [
            'driver' => 'session',
            'provider' => 'employees',
        ],

        'guardian' => [
            'driver' => 'session',
            'provider' => 'guardians',
        ],

        'organization' => [
            'driver' => 'session',
            'provider' => 'organizations',
        ],

        'web' => [
            'driver' => 'session',
            'provider' => 'users',
        ],

        'api' => [
            'driver' => 'token',
            'provider' => 'users',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | User Providers
    |--------------------------------------------------------------------------
    |
    | All authentication drivers have a user provider. This defines how the
    | users are actually retrieved out of your database or other storage
    | mechanisms used by this application to persist your user's data.
    |
    | If you have multiple user tables or models you may configure multiple
    | sources which represent each model / table. These sources may then
    | be assigned to any extra authentication guards you have defined.
    |
    | Supported: "database", "eloquent"
    |
    */

    'providers' => [

        'job_seekers' => [
            'driver' => 'eloquent',
            'model' => Modules\JobSeeker\Entities\JobSeeker::class,
        ],

        'sponsors' => [
            'driver' => 'eloquent',
            'model' => App\Sponsor::class,
        ],

        'superiors' => [
            'driver' => 'eloquent',
            'model' => App\Superior::class,
        ],

        'students' => [
            'driver' => 'eloquent',
            'model' => App\Student::class,
        ],

        'employees' => [
            'driver' => 'eloquent',
            'model' => App\Employee::class,
        ],

        'guardians' => [
            'driver' => 'eloquent',
            'model' => App\Guardian::class,
        ],

        'organizations' => [
            'driver' => 'eloquent',
            'model' => App\Organization::class,
        ],

        'users' => [
            'driver' => 'eloquent',
            'model' => App\User::class,
        ],

        // 'users' => [
        //     'driver' => 'database',
        //     'table' => 'users',
        // ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Resetting Passwords
    |--------------------------------------------------------------------------
    |
    | You may specify multiple password reset configurations if you have more
    | than one user table or model in the application and you want to have
    | separate password reset settings based on the specific user types.
    |
    | The expire time is the number of minutes that the reset token should be
    | considered valid. This security feature keeps tokens short-lived so
    | they have less time to be guessed. You may change this as needed.
    |
    */

    'passwords' => [

        'job_seekers' => [
            'provider' => 'job_seekers',
            'table' => 'job_seeker_password_resets',
            'expire' => 60,
            'throttle' => 60,
        ],

        'sponsors' => [
            'provider' => 'sponsors',
            'table' => 'sponsor_password_resets',
            'expire' => 60,
        ],

        'superiors' => [
            'provider' => 'superiors',
            'table' => 'superior_password_resets',
            'expire' => 60,
        ],

        'students' => [
            'provider' => 'students',
            'table' => 'student_password_resets',
            'expire' => 60,
        ],

        'employees' => [
            'provider' => 'employees',
            'table' => 'employee_password_resets',
            'expire' => 60,
        ],

        'guardians' => [
            'provider' => 'guardians',
            'table' => 'guardian_password_resets',
            'expire' => 60,
        ],

        'organizations' => [
            'provider' => 'organizations',
            'table' => 'organization_password_resets',
            'expire' => 60,
        ],

        'users' => [
            'provider' => 'users',
            'table' => 'password_resets',
            'expire' => 60,
        ],
    ],
    'verification' => [
        'expire' => 2880, // enter as many mintues as you would like here
    ],

];

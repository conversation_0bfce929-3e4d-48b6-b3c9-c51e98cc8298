<?php

namespace App\Http\Middleware;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\Middleware\Authenticate as Middleware;

class Authenticate extends Middleware
{



//    protected function authenticate(array $guards)
//    {
//
//
//
//        if (empty($guards)) {
//
//
//            return route('get.login');
//            return $this->auth->authenticate();
//        }
//
//
//
//
//        $loginUrl = '';
//        foreach ($guards as $guard) {
//
//            if ($this->auth->guard($guard)->check()) {
//                return $this->auth->shouldUse($guard);
//            }else{
//                $loginUrl = route('get.login');
//            }
//
//            // if not logged in, then redirect to related login page
//        }
//
//
//
//
//
//
//
//        throw new AuthenticationException('Unauthenticated.', $guards);
//    }
}

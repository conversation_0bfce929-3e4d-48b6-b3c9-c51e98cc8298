<?php

namespace Modules\Curriculum\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Program;
use Illuminate\Http\Request;
use Session;
use App\EvaluationSchema;
use App\EvaluationSchemaOption;

class EvaluationSchemasController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {

        $keyword = $request->get('search');
        $perPage = 25;

        if (!empty($keyword)) {
            $evaluation_schemas = EvaluationSchema::with('program')->where('title', 'LIKE', "%$keyword%")
				->orWhere('decription', 'LIKE', "%$keyword%")
				->orWhere('status', 'LIKE', "%$keyword%")
				->paginate($perPage);
        } else {
            $evaluation_schemas = EvaluationSchema::paginate($perPage);
        }

        return view('curriculum::evaluation_schemas.index', compact('evaluation_schemas'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {

        $programs = Program::all();
        return view('curriculum::evaluation_schemas.create',compact('programs'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $this->validateSubject($request);

        $evaluation_schema = new EvaluationSchema();
        $evaluation_schema->organization_id = config('organization_id');
        $evaluation_schema->title = $request->title;
        $evaluation_schema->description = $request->description;
        $evaluation_schema->target = $request->target;
        $evaluation_schema->type = $request->type;
        $evaluation_schema->status = $request->status;
        $evaluation_schema->created_by = auth()->user()->id;
        $evaluation_schema->save();

        if(isset($request->options)){
            foreach ($request->options as $option) {

                $new_option = new EvaluationSchemaOption();

                $new_option->evaluation_schema_id = $evaluation_schema->id;
                $new_option->code = $option['code'];
                $new_option->title = $option['title'];
                $new_option->description = $option['description'];
                $new_option->extra_field = $option['extra_field'];

                $new_option->save();
            }
        }

        Session::flash('flash_message', 'Evaluation Schema added!');

        return redirect('workplace/curriculum/evaluation_schemas');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {

        $evaluation_schema = EvaluationSchema::findOrFail($id);

        return view('curriculum::evaluation_schemas.show', compact('evaluation_schema'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $programs = Program::all();

        $evaluation_schema = EvaluationSchema::findOrFail($id);

        return view('curriculum::evaluation_schemas.edit', compact('evaluation_schema','programs'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {
        $this->validateSubject($request);
        $requestData = $request->all();
        
        $evaluation_schema = EvaluationSchema::findOrFail($id);

        $evaluation_schema->title = $request->title;
        $evaluation_schema->program_id = $request->program_id;
        $evaluation_schema->description = $request->description;
        $evaluation_schema->target = $request->target;
        $evaluation_schema->type = $request->type;
        $evaluation_schema->status = $request->status;

        $evaluation_schema->save();

        if (isset($request->options)) {
            foreach ($request->options as $option) {

                $new_option = new EvaluationSchemaOption();

                $new_option->evaluation_schema_id = $evaluation_schema->id;
                $new_option->code = $option['code'];
                $new_option->title = $option['title'];
                $new_option->description = $option['description'];

                $new_option->save();
            }
        }


        Session::flash('flash_message', 'Subject updated!');

        return redirect('workplace/curriculum/evaluation_schemas');
    }


    /**
     * Update Subject Contents.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function contents(Request $request)
    {
        auth()->user()->can('edit subject_contents');

        $id = $request->subject_id;

        $evaluation_schema = EvaluationSchema::findOrFail($id);
        
        $evaluation_schema->contents()->sync($request->contents);
        
        Session::flash('flash_message', 'Program updated!');
        
        if($request->ajax()){
            return response()->json(['status' => 'success']);
        }

        return redirect()->back();
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        EvaluationSchema::destroy($id);

        Session::flash('flash_message', 'Subject deleted!');

        return redirect('workplace/curriculum/evaluation_schemas');
    }
    private function validateSubject($request){
        $rules = [];

        $rules['title'] = 'required|min:3';
        $rules['type'] = 'required';
        $rules['target'] = 'required';
        $rules['status'] = 'required';

        $this->validate($request, $rules);
    }
}

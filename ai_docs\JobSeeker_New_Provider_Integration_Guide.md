# JobSeeker New Provider Integration Guide

**Version:** 1.0  
**Date:** July 28, 2025  
**Purpose:** Step-by-step guide for integrating new job providers into the JobSeeker system

## 📋 **OVERVIEW**

This guide ensures new job providers follow the established architecture pattern that maintains perfect alignment between job fetching and notification systems.

## 🏗️ **ARCHITECTURE PATTERN**

### **Core Principle:**
**JobNotificationSetup MUST target the SAME categories as CommandScheduleFilter**

```
CommandScheduleRule → CommandScheduleFilter → ProviderJobCategory (provider_identifier)
                                                      ↓
JobNotificationSetup → JobNotificationProviderCategory → ProviderJobCategory (provider_identifier)
```

### **Data Flow:**
1. **Job Fetching:** Uses `provider_identifier` from `ProviderJobCategory`
2. **Job Storage:** Links jobs to categories via `job_category_pivot`
3. **Notifications:** Matches jobs using same `ProviderJobCategory` IDs
4. **Perfect Alignment:** 100% synchronization guaranteed

## 🚀 **STEP-BY-STEP INTEGRATION**

### **Step 1: Provider Analysis**
Before starting, analyze the new provider:

```bash
# Questions to answer:
1. What is the provider's API endpoint?
2. What categories does the provider offer?
3. Does the API support batch category requests or single category only?
4. What authentication is required?
5. What is the rate limiting policy?
```

### **Step 2: Database Setup**

#### **2.1: Add Provider Categories**
```sql
-- Insert provider-specific categories
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id, created_at, updated_at) VALUES
('newprovider', 'Software Development', 'tech-001', 1, NOW(), NOW()),
('newprovider', 'Project Management', 'mgmt-002', 2, NOW(), NOW()),
('newprovider', 'Education & Training', 'edu-003', 5, NOW(), NOW());

-- Verify insertion
SELECT * FROM provider_job_categories WHERE provider_name = 'newprovider';
```

#### **2.2: Map to Canonical Categories**
```sql
-- Ensure proper mapping to existing canonical categories
UPDATE provider_job_categories 
SET canonical_category_id = (
    SELECT id FROM job_categories 
    WHERE name = 'Technology' 
    LIMIT 1
)
WHERE provider_name = 'newprovider' 
AND name LIKE '%Software%';
```

### **Step 3: Create Job Service**

#### **3.1: Service Class Structure**
```php
<?php
// File: Modules/JobSeeker/Services/NewProviderJobService.php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Entities\Job;
use Modules\JobSeeker\Entities\ProviderJobCategory;

final class NewProviderJobService
{
    private const API_BASE_URL = 'https://api.newprovider.com';
    private const RATE_LIMIT_DELAY = 2; // seconds between requests
    
    /**
     * Fetch jobs for specific categories
     * 
     * @param array $providerCategoryIds Array of ProviderJobCategory IDs
     * @return array
     */
    public function fetchJobsForCategories(array $providerCategoryIds): array
    {
        $jobs = [];
        
        // Get provider identifiers for API calls
        $categories = ProviderJobCategory::whereIn('id', $providerCategoryIds)
            ->where('provider_name', 'newprovider')
            ->get();
            
        foreach ($categories as $category) {
            try {
                // Check if provider supports batch requests
                if ($this->supportsBatchRequests()) {
                    // Batch all categories in one request
                    $categoryJobs = $this->fetchJobsBatch($categories->pluck('provider_identifier')->toArray());
                    $jobs = array_merge($jobs, $categoryJobs);
                    break; // Only one request needed
                } else {
                    // Individual requests per category
                    $categoryJobs = $this->fetchJobsForCategory($category->provider_identifier);
                    $jobs = array_merge($jobs, $categoryJobs);
                    
                    // Rate limiting
                    sleep(self::RATE_LIMIT_DELAY);
                }
                
            } catch (Exception $e) {
                Log::error('NewProviderJobService: Failed to fetch jobs', [
                    'category_id' => $category->id,
                    'provider_identifier' => $category->provider_identifier,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        return $this->processAndStoreJobs($jobs, $providerCategoryIds);
    }
    
    /**
     * Check if provider supports batch category requests
     */
    private function supportsBatchRequests(): bool
    {
        // Return true if provider supports multiple categories in one request
        // Return false if provider requires separate requests per category
        return true; // or false - depends on provider
    }
    
    /**
     * Fetch jobs for multiple categories (batch request)
     */
    private function fetchJobsBatch(array $providerIdentifiers): array
    {
        $response = Http::timeout(30)->get(self::API_BASE_URL . '/jobs', [
            'categories' => implode(',', $providerIdentifiers),
            'limit' => 100
        ]);
        
        if (!$response->successful()) {
            throw new Exception('API request failed: ' . $response->status());
        }
        
        return $response->json('jobs', []);
    }
    
    /**
     * Fetch jobs for single category
     */
    private function fetchJobsForCategory(string $providerIdentifier): array
    {
        $response = Http::timeout(30)->get(self::API_BASE_URL . '/jobs', [
            'category' => $providerIdentifier,
            'limit' => 100
        ]);
        
        if (!$response->successful()) {
            throw new Exception('API request failed: ' . $response->status());
        }
        
        return $response->json('jobs', []);
    }
    
    /**
     * Process and store jobs in database
     */
    private function processAndStoreJobs(array $jobsData, array $providerCategoryIds): array
    {
        $storedJobs = [];
        
        foreach ($jobsData as $jobData) {
            try {
                // Create job record
                $job = Job::create([
                    'position' => $jobData['title'],
                    'company_name' => $jobData['company'],
                    'description' => $jobData['description'] ?? '',
                    'location' => $jobData['location'] ?? '',
                    'source' => 'NewProvider',
                    'source_url' => $jobData['url'] ?? '',
                    'posted_date' => $jobData['posted_date'] ?? now(),
                ]);
                
                // Associate with provider categories
                $canonicalCategoryIds = ProviderJobCategory::whereIn('id', $providerCategoryIds)
                    ->whereNotNull('canonical_category_id')
                    ->pluck('canonical_category_id')
                    ->unique();
                    
                $job->categories()->sync($canonicalCategoryIds);
                
                $storedJobs[] = $job;
                
            } catch (Exception $e) {
                Log::error('NewProviderJobService: Failed to store job', [
                    'job_data' => $jobData,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        return $storedJobs;
    }
}
```

### **Step 4: Create Sync Command**

#### **4.1: Command Structure**
```php
<?php
// File: Modules/JobSeeker/Console/SyncNewProviderJobsCommand.php

declare(strict_types=1);

namespace Modules\JobSeeker\Console;

use Illuminate\Console\Command;
use Modules\JobSeeker\Services\NewProviderJobService;
use Modules\JobSeeker\Entities\ProviderJobCategory;

class SyncNewProviderJobsCommand extends Command
{
    protected $signature = 'jobseeker:sync-newprovider-jobs 
                            {--categories=* : Specific provider category IDs to sync}';
    
    protected $description = 'Sync jobs from NewProvider';
    
    public function handle(NewProviderJobService $jobService): int
    {
        $this->info('🔄 Syncing NewProvider jobs...');
        
        // Get categories to sync
        $categoryIds = $this->option('categories');
        
        if (empty($categoryIds)) {
            // Get all active NewProvider categories
            $categoryIds = ProviderJobCategory::where('provider_name', 'newprovider')
                ->pluck('id')
                ->toArray();
        }
        
        if (empty($categoryIds)) {
            $this->warn('No categories found to sync');
            return 0;
        }
        
        try {
            $jobs = $jobService->fetchJobsForCategories($categoryIds);
            
            $this->info("✅ Synced {count($jobs)} jobs from NewProvider");
            return 0;
            
        } catch (\Exception $e) {
            $this->error('❌ Sync failed: ' . $e->getMessage());
            return 1;
        }
    }
}
```

### **Step 5: Create Command Schedule Rules**

#### **5.1: Database Records**
```sql
-- Insert command schedule rule
INSERT INTO command_schedule_rules (command, schedule_type, cron_expression, is_active, created_at, updated_at) VALUES
('jobseeker:sync-newprovider-jobs', 'cron', '0 */6 * * *', 1, NOW(), NOW());

-- Get the rule ID
SET @rule_id = LAST_INSERT_ID();

-- Insert command schedule filter with provider category IDs
INSERT INTO command_schedule_filters (schedule_rule_id, categories, created_at, updated_at) VALUES
(@rule_id, JSON_ARRAY("301", "302", "303"), NOW(), NOW());

-- Note: Replace 301, 302, 303 with actual ProviderJobCategory IDs for newprovider
```

### **Step 6: Update Notification System**

#### **6.1: Add Provider to Validation**
```php
// File: Modules/JobSeeker/Http/Controllers/JobsController.php
// Update validation rules:

'provider_name' => 'required|string|in:acbar,jobs.af,newprovider,all',
```

#### **6.2: Update UI Options**
```html
<!-- File: resources/views/modules/jobseeker/jobs/notifications.blade.php -->
<select class="form-select" id="provider-select" name="provider_name" required>
    <option value="">Select a job provider...</option>
    <option value="acbar">ACBAR (Agency Coordinating Body for Afghan Relief)</option>
    <option value="jobs.af">Jobs.af</option>
    <option value="newprovider">NewProvider (Description)</option>
    <option value="all">All Providers (Combined)</option>
</select>
```

### **Step 7: Register Command**

#### **7.1: Service Provider Registration**
```php
// File: Modules/JobSeeker/Providers/JobSeekerServiceProvider.php

use Modules\JobSeeker\Console\SyncNewProviderJobsCommand;

protected $commands = [
    // ... existing commands
    SyncNewProviderJobsCommand::class,
];
```

### **Step 8: Testing & Validation**

#### **8.1: Test Command**
```bash
# Test the sync command
php artisan jobseeker:sync-newprovider-jobs --categories=301,302,303

# Verify jobs were stored
SELECT COUNT(*) FROM jobs WHERE source = 'NewProvider';
```

#### **8.2: Test Notifications**
```bash
# Sync notification categories
php artisan jobseeker:sync-notification-categories --provider=newprovider

# Verify alignment
SELECT 'ALIGNED' as status WHERE (
    SELECT GROUP_CONCAT(DISTINCT provider_category_id ORDER BY provider_category_id)
    FROM job_notification_provider_category jnpc
    JOIN job_notification_setups jns ON jnpc.setup_id = jns.id
    WHERE jns.provider_name = 'newprovider'
) = (
    SELECT GROUP_CONCAT(DISTINCT JSON_UNQUOTE(JSON_EXTRACT(categories, CONCAT('$[', idx, ']'))) ORDER BY JSON_UNQUOTE(JSON_EXTRACT(categories, CONCAT('$[', idx, ']'))))
    FROM command_schedule_filters csf
    JOIN command_schedule_rules csr ON csf.schedule_rule_id = csr.id
    WHERE csr.command LIKE '%newprovider%'
);
```

## ⚠️ **CRITICAL REQUIREMENTS**

### **1. Category Alignment (MANDATORY)**
- ✅ CommandScheduleFilter categories MUST match JobNotificationSetup categories
- ✅ Use same ProviderJobCategory IDs in both systems
- ✅ Run sync command after any category changes

### **2. Provider Identifier Usage**
- ✅ Use `provider_identifier` field for API calls, NOT the database ID
- ✅ Ensure provider_identifier values are unique within provider
- ✅ Map provider categories to canonical categories for job storage

### **3. Error Handling**
- ✅ Log all API failures with context
- ✅ Handle rate limiting appropriately
- ✅ Implement retry logic for transient failures
- ✅ Report critical errors to founder email

### **4. Data Integrity**
- ✅ Validate job data before storage
- ✅ Prevent duplicate job entries
- ✅ Maintain referential integrity with categories
- ✅ Clean up old jobs periodically

## 🔧 **MAINTENANCE COMMANDS**

```bash
# Sync notification categories with schedule filters
php artisan jobseeker:sync-notification-categories --provider=newprovider

# Test complete workflow
php artisan jobseeker:test-newprovider-workflow

# Validate system health
php artisan jobseeker:health-monitor --provider=newprovider
```

## 📊 **SUCCESS METRICS**

### **Integration Complete When:**
- ✅ Jobs fetch successfully from provider API
- ✅ Jobs stored with correct categories
- ✅ Notifications sent to relevant jobseekers
- ✅ 100% category alignment between fetching and notifications
- ✅ No errors in logs during normal operation

### **Performance Targets:**
- ✅ API response time < 30 seconds per request
- ✅ Memory usage < 256MB during sync
- ✅ Error rate < 5%
- ✅ Notification delivery rate > 95%

## 🎯 **FINAL CHECKLIST**

Before going live with new provider:

- [ ] Provider categories added to `provider_job_categories`
- [ ] Categories mapped to canonical categories
- [ ] Job service created and tested
- [ ] Sync command created and registered
- [ ] Command schedule rules configured
- [ ] Notification system updated
- [ ] UI updated with provider option
- [ ] Category alignment verified (100%)
- [ ] End-to-end testing completed
- [ ] Error handling tested
- [ ] Performance benchmarks met
- [ ] Documentation updated

**Following this guide ensures perfect integration with zero notification alignment issues!**

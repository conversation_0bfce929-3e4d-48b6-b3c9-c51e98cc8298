# JobSeeker System Troubleshooting Guide

**Version:** 1.0  
**Date:** July 28, 2025  
**Purpose:** Comprehensive troubleshooting guide for JobSeeker notification system issues

## 🚨 **COMMON ISSUES & SOLUTIONS**

### **Issue 1: Jobs Fetched But No Notifications Sent**

#### **Symptoms:**
- Jobs appear in database with correct source
- No entries in `job_notification_sent_jobs` table
- Jobseekers not receiving emails

#### **Root Cause Analysis:**
```sql
-- Check category alignment between fetching and notifications
SELECT 
    'Schedule Categories' as type,
    GROUP_CONCAT(DISTINCT JSON_UNQUOTE(JSON_EXTRACT(csf.categories, CONCAT('$[', numbers.n, ']'))) ORDER BY JSON_UNQUOTE(JSON_EXTRACT(csf.categories, CONCAT('$[', numbers.n, ']')))) as category_ids
FROM command_schedule_filters csf
JOIN command_schedule_rules csr ON csf.schedule_rule_id = csr.id
JOIN (SELECT 0 n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15) numbers ON JSON_UNQUOTE(JSON_EXTRACT(csf.categories, CONCAT('$[', numbers.n, ']'))) IS NOT NULL
WHERE csr.command LIKE '%PROVIDER_NAME%'

UNION ALL

SELECT 
    'Notification Categories' as type,
    GROUP_CONCAT(DISTINCT jnpc.provider_category_id ORDER BY jnpc.provider_category_id) as category_ids
FROM job_notification_provider_category jnpc
JOIN job_notification_setups jns ON jnpc.setup_id = jns.id
WHERE jns.provider_name = 'PROVIDER_NAME' AND jns.is_active = 1;
```

#### **Solution:**
```bash
# Sync notification categories with schedule filters
php artisan jobseeker:sync-notification-categories --provider=PROVIDER_NAME

# Verify alignment
php artisan jobseeker:validate-provider-alignment --provider=PROVIDER_NAME
```

### **Issue 2: Invalid Category IDs in Schedule Filters**

#### **Symptoms:**
- API calls fail with "category not found" errors
- Jobs not fetched despite command running
- Error logs show invalid provider_identifier values

#### **Diagnosis:**
```sql
-- Check for invalid category IDs in schedule filters
SELECT 
    csr.command,
    csf.categories,
    'INVALID' as status
FROM command_schedule_filters csf
JOIN command_schedule_rules csr ON csf.schedule_rule_id = csr.id
WHERE JSON_EXTRACT(csf.categories, '$[0]') NOT IN (
    SELECT CAST(id AS CHAR) FROM provider_job_categories 
    WHERE provider_name = 'PROVIDER_NAME'
);
```

#### **Solution:**
```sql
-- Fix invalid category IDs (example for Jobs.af)
UPDATE command_schedule_filters 
SET categories = JSON_ARRAY("272", "279", "277", "273", "278", "274", "271", "275")
WHERE id IN (
    SELECT csf.id 
    FROM command_schedule_filters csf
    JOIN command_schedule_rules csr ON csf.schedule_rule_id = csr.id
    WHERE csr.command LIKE '%jobs-af%'
);
```

### **Issue 3: Email Notifications Disabled**

#### **Symptoms:**
- Jobs fetched successfully
- Categories aligned correctly
- No emails sent to jobseekers

#### **Diagnosis:**
```sql
-- Check if notifications are disabled
SELECT 
    'JOBSEEKER_DISABLE_EVENT_NOTIFICATIONS' as setting,
    CASE 
        WHEN 'true' = 'true' THEN 'DISABLED ❌'
        ELSE 'ENABLED ✅'
    END as status;

-- Check email service status
SELECT COUNT(*) as recent_emails FROM job_notification_log WHERE sent_at >= NOW() - INTERVAL 1 HOUR;
```

#### **Solution:**
```bash
# Check configuration
php artisan config:show | grep JOBSEEKER_DISABLE_EVENT_NOTIFICATIONS

# If disabled, enable notifications
# Update .env file: JOBSEEKER_DISABLE_EVENT_NOTIFICATIONS=false

# Clear config cache
php artisan config:clear
```

### **Issue 4: Provider Categories Not Mapped to Canonical Categories**

#### **Symptoms:**
- Jobs stored without categories
- Empty `job_category_pivot` entries
- Notifications work but jobs don't appear in category searches

#### **Diagnosis:**
```sql
-- Check for unmapped provider categories
SELECT 
    id,
    provider_name,
    name,
    provider_identifier,
    canonical_category_id
FROM provider_job_categories 
WHERE provider_name = 'PROVIDER_NAME' 
AND canonical_category_id IS NULL;
```

#### **Solution:**
```sql
-- Map provider categories to canonical categories
UPDATE provider_job_categories 
SET canonical_category_id = (
    SELECT id FROM job_categories 
    WHERE name = 'Technology' 
    LIMIT 1
)
WHERE provider_name = 'PROVIDER_NAME' 
AND name LIKE '%Software%'
AND canonical_category_id IS NULL;

-- Verify mapping
SELECT 
    pjc.name as provider_category,
    jc.name as canonical_category
FROM provider_job_categories pjc
JOIN job_categories jc ON pjc.canonical_category_id = jc.id
WHERE pjc.provider_name = 'PROVIDER_NAME';
```

### **Issue 5: Duplicate Jobs Being Created**

#### **Symptoms:**
- Same job appears multiple times in database
- Jobseekers receive duplicate notifications
- Database size growing rapidly

#### **Diagnosis:**
```sql
-- Find duplicate jobs
SELECT 
    position,
    company_name,
    source,
    COUNT(*) as duplicate_count
FROM jobs 
WHERE source = 'PROVIDER_NAME'
AND created_at >= NOW() - INTERVAL 7 DAY
GROUP BY position, company_name, source
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;
```

#### **Solution:**
```sql
-- Remove duplicates (keep latest)
DELETE j1 FROM jobs j1
INNER JOIN jobs j2 
WHERE j1.id < j2.id 
AND j1.position = j2.position 
AND j1.company_name = j2.company_name 
AND j1.source = j2.source
AND j1.source = 'PROVIDER_NAME';

-- Add unique constraint to prevent future duplicates
ALTER TABLE jobs 
ADD UNIQUE KEY unique_job_per_source (position, company_name, source);
```

## 🔧 **DIAGNOSTIC COMMANDS**

### **System Health Check**
```bash
# Overall system health
php artisan jobseeker:health-monitor --full

# Provider-specific health
php artisan jobseeker:health-monitor --provider=PROVIDER_NAME

# Category alignment check
php artisan jobseeker:validate-category-alignment
```

### **Data Validation**
```bash
# Validate all provider data
php artisan jobseeker:validate-providers

# Check for orphaned records
php artisan jobseeker:check-data-integrity

# Verify notification setup
php artisan jobseeker:validate-notification-setup
```

### **Performance Analysis**
```bash
# Check API performance
php artisan jobseeker:test-api-performance --provider=PROVIDER_NAME

# Database performance
php artisan jobseeker:analyze-db-performance

# Memory usage analysis
php artisan jobseeker:check-memory-usage
```

## 📊 **MONITORING QUERIES**

### **Daily Health Check Queries**
```sql
-- 1. Check recent job fetching activity
SELECT 
    source,
    DATE(created_at) as date,
    COUNT(*) as jobs_fetched
FROM jobs 
WHERE created_at >= NOW() - INTERVAL 7 DAY
GROUP BY source, DATE(created_at)
ORDER BY date DESC, source;

-- 2. Check notification activity
SELECT 
    DATE(sent_at) as date,
    COUNT(*) as notifications_sent
FROM job_notification_log 
WHERE sent_at >= NOW() - INTERVAL 7 DAY
GROUP BY DATE(sent_at)
ORDER BY date DESC;

-- 3. Check error rates
SELECT 
    DATE(created_at) as date,
    COUNT(*) as errors
FROM system_error_alerts 
WHERE created_at >= NOW() - INTERVAL 7 DAY
AND resolved_at IS NULL
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 4. Check category alignment
SELECT 
    jns.provider_name,
    COUNT(DISTINCT jnpc.provider_category_id) as notification_categories,
    jns.last_sync_at
FROM job_notification_setups jns
LEFT JOIN job_notification_provider_category jnpc ON jns.id = jnpc.setup_id
WHERE jns.is_active = 1
GROUP BY jns.provider_name, jns.last_sync_at;
```

### **Performance Monitoring**
```sql
-- Check slow queries
SELECT 
    ROUND(AVG(execution_time_seconds), 2) as avg_execution_time,
    command,
    COUNT(*) as executions
FROM command_schedule_executions 
WHERE created_at >= NOW() - INTERVAL 24 HOUR
GROUP BY command
ORDER BY avg_execution_time DESC;

-- Check memory usage trends
SELECT 
    DATE(recorded_at) as date,
    AVG(value) as avg_memory_percent
FROM system_health_metrics 
WHERE metric_type = 'system_resources'
AND metric_name = 'memory_usage_percent'
AND recorded_at >= NOW() - INTERVAL 7 DAY
GROUP BY DATE(recorded_at)
ORDER BY date DESC;
```

## 🚨 **EMERGENCY PROCEDURES**

### **Complete System Reset**
```bash
# 1. Stop all scheduled jobs
php artisan schedule:clear

# 2. Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear

# 3. Rebuild category alignments
php artisan jobseeker:sync-notification-categories --provider=all

# 4. Restart scheduled jobs
php artisan schedule:run
```

### **Provider-Specific Reset**
```bash
# 1. Clear provider data
php artisan jobseeker:clear-provider-data --provider=PROVIDER_NAME

# 2. Rebuild provider categories
php artisan jobseeker:rebuild-provider-categories --provider=PROVIDER_NAME

# 3. Sync notifications
php artisan jobseeker:sync-notification-categories --provider=PROVIDER_NAME

# 4. Test workflow
php artisan jobseeker:test-provider-workflow --provider=PROVIDER_NAME
```

## 📞 **ESCALATION PROCEDURES**

### **When to Escalate:**
- System down for > 1 hour
- No jobs fetched for > 6 hours
- No notifications sent for > 2 hours
- Error rate > 25%
- Memory usage > 90%

### **Escalation Contacts:**
- **System Administrator:** <EMAIL>
- **Technical Lead:** [Contact Info]
- **Database Administrator:** [Contact Info]

### **Information to Include:**
- Error messages and logs
- System health metrics
- Recent changes made
- Steps already attempted
- Impact assessment

## 🎯 **PREVENTION CHECKLIST**

### **Daily:**
- [ ] Check system health dashboard
- [ ] Verify job fetching activity
- [ ] Monitor notification delivery rates
- [ ] Review error logs

### **Weekly:**
- [ ] Run full system validation
- [ ] Check category alignment
- [ ] Analyze performance trends
- [ ] Clean up old data

### **Monthly:**
- [ ] Review provider API changes
- [ ] Update category mappings
- [ ] Performance optimization
- [ ] Documentation updates

**Following this troubleshooting guide will help you quickly identify and resolve JobSeeker system issues!**

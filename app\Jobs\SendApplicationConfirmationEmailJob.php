<?php

namespace App\Jobs;

use App\Mail\OrderShipped;
use App\Services\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Mail\SendEmailTest;
use Mail;
use Illuminate\Contracts\Mail\Mailer;

class SendApplicationConfirmationEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    protected $user_info = [];
    protected $sender;
    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_info, $sender)
    {

        // I have commented the following line because it was causing an error especially when it was called from line 963 of StudentApplicationController
//        $this->user_info = $user_info[0];
        $this->user_info = $user_info;
        $this->sender = $sender;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
//    public function handle(\Illuminate\Support\Facades\Mail $mailer)
    public function handle()
    {


        $emailService = app(\App\Services\EmailService::class);
        $emailService->sendEmail(
            ['email' => $this->user_info['email'], 'name' => $this->user_info['studentName']],
            "Application Received",
            'modules.site.templates.wajeha.backEnd.studentInformation.student_application_confirmation_email',
            ['data' => $this->user_info],
            [
                [
                    'path' => storage_path("app/applicationLetters/" . $this->user_info['student_id'] . '-applicationLetter.pdf'),
                    'name' => $this->user_info['student_id'] . '-applicationLetter.pdf',
                    'mime' => 'application/pdf'
                ]
            ] // Attachments
        );


//        $mj = new \Mailjet\Client(env('MAILJETAPIKEY'), env('MAILJETAPISECRET'), true, ['version' => 'v3.1']);
//        $body = [
//            'Messages' => [
//                [
//                    'From' => [
//                        'Email' => "<EMAIL>",
//                        'Name' => "ITQAN"
//                    ],
//                    'To' => [
//                        [
//                            'Email' => $this->user_info['email'],
//                            'Name' => $this->user_info['studentName']
//                        ]
//                    ],
//                    'Subject' => "APPLICATION RECEIVED",
//                    'TextPart' => "Your Text",
//                    'HTMLPart' => view('modules.site.templates.wajeha.backEnd.studentInformation.student_application_confirmation_email', ['data'=> $this->user_info])->render(),
//                    'Attachments' => [
//                        [
//                            'ContentType' => "application/pdf",
//                            'Filename' => $this->user_info['student_id'] . '-applicationLetter.pdf',
//                            'Base64Content' => base64_encode(file_get_contents(storage_path("app/applicationLetters/" . $this->user_info['student_id'] . '-applicationLetter.pdf')))
//                        ]
//                    ]
//                ]
//            ]
//        ];
//        $response = $mj->post(Resources::$Email, ['body' => $body]);



    }
}

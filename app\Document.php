<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;

class Document extends Model
{
    protected $fillable = ['position'];
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);


    }

    public function documentable(){

        return $this->morphTo();
    }

    function showTimelineDocName($data){
        $name = explode('/', $data);
        $number = count($name);
        return $name[$number-1];
    }
    function showDocumentName($data){
        $name = explode('/', $data);
        $number = count($name);
        return $name[$number-1];
    }

    function showPicName($data){
        $name = explode('/', @$data);
        if(!empty($name[4])){

            return $name[4];
        }else{
            return '';
        }
    }

    //
}

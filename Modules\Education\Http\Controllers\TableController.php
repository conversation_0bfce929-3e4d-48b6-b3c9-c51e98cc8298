<?php

namespace Modules\Education\Http\Controllers;

use App\BaseSetup;
use App\Employee;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\StudentHefzReport;
use App\Subject;
use App\Talaqqi;
use App\Talqeen;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Matrix\Builder;
use Modules\UserActivityLog\Traits\LogActivity;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use Carbon\Carbon;
use App\ClassStudent;
use App\ClassTeacher;
use App\Http\Requests;
use App\ClassTeacherSubject;
use Illuminate\Http\Request;
use App\ClassSubjectTimetable;
use App\Http\Controllers\Controller;
use App\Cen_Emp;
use Tests\Psalm\LaravelPlugin\Models\Car;

class TableController extends Controller
{

    // Method to handle Talaqqi updates
    public function updateTalaqqi(Request $request)
    {
        $id = $request->id;
        $order = $request->order;
        $column = $request->column;
        $value = $request->value;
        $programId = $request->program_id;

        // Check if the 'id' indicates an existing record or a new one needs to be created
        if ($id && $id > 0) {
            // Attempt to find an existing record
            $entry = Talaqqi::find($id);
            if ($entry) {
                // Update the existing record
                $entry->order = $order;
                $entry->$column = $value;
                $entry->program_id = $programId;
                $entry->save();
            } else {
                // If no record is found with the given ID, create a new one
                $entry = Talaqqi::create([$column => $value, 'program_id' => $programId]);
            }
        } else {
            // Create a new record if 'id' is 0 or not provided
            $entry = Talaqqi::create([$column => $value, 'program_id' => $programId,'order'=> $order]);
        }
        $this->recalculateTalaqqiOrder($programId);

        return response()->json(['id' => $entry->id]);
    }


    // Method to handle Talqeen updates
    public function updateTalqeen(Request $request)
    {
        $id = $request->id;
        $order = $request->order;
        $column = $request->column;
        $value = $request->value;
        $programId = $request->program_id;


        // Check if the 'id' indicates an existing record or a new one needs to be created
        if ($id && $id > 0) {
            // Attempt to find an existing record
            $entry = Talqeen::find($id);
            if ($entry) {
                // Update the existing record
                $entry->order = $order;
                $entry->$column = $value;
                $entry->program_id = $programId;
                $entry->save();
            } else {
                // If no record is found with the given ID, create a new one
                $entry = Talqeen::create([$column => $value, 'program_id' => $programId]);
            }
        } else {
            // Create a new record if 'id' is 0 or not provided
            $entry = Talqeen::create([$column => $value, 'program_id' => $programId,'order'=> $order]);
        }
        $this->recalculateTalqeenOrder($programId);

        return response()->json(['id' => $entry->id]);
    }

    protected function recalculateTalqeenOrder($programId)
    {
        // Fetch all Talqeen records for the given program, ordered by the current order
        $talqeenRecords = Talqeen::where('program_id', $programId)->orderBy('order')->get();

        // Update the order to ensure it starts from 1 and increments sequentially
        foreach ($talqeenRecords as $index => $record) {
            $record->order = $index + 1;
            $record->save();
        }
    }
    protected function recalculateTalaqqiOrder($programId)
    {
        // Fetch all Talqeen records for the given program, ordered by the current order
        $talaqqiRecords = Talaqqi::where('program_id', $programId)->orderBy('order')->get();

        // Update the order to ensure it starts from 1 and increments sequentially
        foreach ($talaqqiRecords as $index => $record) {
            $record->order = $index + 1;
            $record->save();
        }
    }
    public function deleteTalaqqi(Request $request)
    {

        $id = $request->input('id');

        // Find the record and delete it
        $record = Talaqqi::find($id);
        $programId = $record->program_id;

        if ($record) {
            $record->delete();
            $this->recalculateTalaqqiOrder($programId);

            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false], 404);
        }
    }

    public function deleteTalqeen(Request $request)
    {
        $id = $request->input('id');

        // Find the record and delete it
        $record = Talqeen::find($id);
        $programId = $record->program_id;

        if ($record) {
            $record->delete();

            // Recalculate and update the order for all records
            $this->recalculateTalqeenOrder($programId);

            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false], 404);
        }
    }
}

<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Http\Controllers;

use App\Classes;
use App\Http\Controllers\Controller;
use App\IjazasanadMemorizationPlan;
use App\Student;
use App\StudentIjazasanadMemorizationReport;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;

/**
 * Aggregated month-end Ijazasanad summary across MULTIPLE classes.
 * Returns one row per class including attendance, achievement, Juz and Pages progress.
 */
final class MonthEndIjazasanadSummaryAggregatedController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        try {
            $classIds = $this->parseClassIds($request->input('classId'));
            $monthYear = (string) $request->input('classDate');
            if (empty($classIds) || $monthYear === '') {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            $date = Carbon::createFromFormat('M Y', $monthYear);
            $month = (int) $date->month;
            $year  = (int) $date->year;

            $rows = [];
            foreach ($classIds as $classId) {
                $class = Classes::find($classId); if (!$class) continue;
                $students = Student::whereHas('joint_classes', function ($q) use ($classId) { $q->where('class_id', $classId); })
                    ->where('status', 'active')
                    ->orderBy('full_name', 'asc')
                    ->get();

                $noOfStudents = $students->count();
                $avgAttendance = $this->calculateAverageAttendance($students, $classId, $month, $year);
                $avgAchievement = $this->calculateAverageAchievement($students, $classId, $month, $year);

                $juzMetrics  = $this->calculateJuzMetrics($students, $classId, $month, $year);
                $pageMetrics = $this->calculatePageMetrics($students, $classId, $month, $year);

                $rows[] = [
                    'class_id'            => (int) $classId,
                    'class_name'          => $class->class_code ?? (string) $class->name,
                    'noOfStudents'        => $noOfStudents,
                    'avgAttendance'       => $this->formatProgressBar($avgAttendance, '#28a745'),
                    'avgAchievement'      => $this->formatProgressBar($avgAchievement, '#1fff0f'),
                    'totalPlannedJuz'     => $juzMetrics['planned'],
                    'totalCompletedJuz'   => $juzMetrics['completed'],
                    'juzProgress'         => $this->formatProgressBar($juzMetrics['percentage'], '#007bff'),
                    'totalPlannedPages'   => $pageMetrics['planned'],
                    'totalCompletedPages' => $pageMetrics['completed'],
                    'pageProgress'        => $this->formatProgressBar($pageMetrics['percentage'], '#6f42c1'),
                ];
            }

            return DataTables::of($rows)->rawColumns(['avgAttendance','avgAchievement','juzProgress','pageProgress'])->toJson();
        } catch (\Throwable $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /** @return int[] */
    private function parseClassIds($input): array
    { if (is_array($input)) $ids=$input; elseif (is_string($input)) $ids=array_filter(array_map('trim', explode(',', $input))); else $ids=[]; $ids=array_values(array_unique(array_map('intval',$ids))); return array_values(array_filter($ids,static fn(int $id)=>$id>0)); }

    private function calculateAverageAttendance($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) return 0.0;
        $class = Classes::find($classId); if (!$class || !$class->timetable) return 0.0;
        $totalClasses = $class->timetable->daysCountPerMonth($month, $year); if ($totalClasses<=0) return 0.0;
        $sum=0; $cnt=0; foreach($students as $s){ $att=StudentIjazasanadMemorizationReport::where('student_id',$s->id)->where('class_id',$classId)->whereYear('created_at',$year)->whereMonth('created_at',$month)->whereIn('attendance_id',[1,2])->count(); $sum+=min(100.0,($att/$totalClasses)*100); $cnt++; } return $cnt>0?$sum/$cnt:0.0;
    }

    private function calculateAverageAchievement($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) return 0.0; $sum=0; $cnt=0;
        foreach ($students as $student) {
            $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
                ->where(function ($q) use ($month,$year,$classId){ $q->where(function($q1)use($year,$month,$classId){$q1->whereYear('created_at',$year)->whereMonth('created_at',$month)->where('class_id',$classId)->where('status','active');})->orWhere(function($q2)use($year,$month,$classId){$q2->whereYear('start_date',$year)->whereMonth('start_date',$month)->where('class_id',$classId)->where('status','active');}); })
                ->first();
            if (!$plan) continue;
            $reports = StudentIjazasanadMemorizationReport::where('student_id',$student->id)->where('class_id',$classId)->whereYear('created_at',$year)->whereMonth('created_at',$month)->get();
            if ($reports->isEmpty()) continue;
            $student->loadMissing('studentProgramLevels.programlevel');
            $level = $this->detectStudentLevel($student);
            $percent = ($level==='level1') ? $this->calculateLevel1Completion($plan,$reports)['completion_rate'] : $this->calculateLevel2Completion($plan,$reports)['completion_rate'];
            if ($percent>0) { $sum+=$percent; $cnt++; }
        }
        return $cnt>0?$sum/$cnt:0.0;
    }

    private function calculateJuzMetrics($students, int $classId, int $month, int $year): array
    {
        if ($students->isEmpty()) return ['planned'=>0,'completed'=>0,'percentage'=>0];
        $planned=0; $completed=0; foreach($students as $student){
            $plan = IjazasanadMemorizationPlan::where('student_id',$student->id)->where(function($q)use($month,$year,$classId){$q->where(function($q1)use($year,$month,$classId){$q1->whereYear('created_at',$year)->whereMonth('created_at',$month)->where('class_id',$classId)->where('status','active');})->orWhere(function($q2)use($year,$month,$classId){$q2->whereYear('start_date',$year)->whereMonth('start_date',$month)->where('class_id',$classId)->where('status','active');});})->first();
            if ($plan && $plan->from_surat_juz_id && $plan->to_surat_juz_id) { $planned += max(0, $plan->to_surat_juz_id - $plan->from_surat_juz_id + 1); }
            $reports = StudentIjazasanadMemorizationReport::where('student_id',$student->id)->where('class_id',$classId)->whereYear('created_at',$year)->whereMonth('created_at',$month)->whereNotNull('hefz_from_surat')->whereNotNull('hefz_to_surat')->get();
            if ($reports->isNotEmpty()) { $completed += $this->calculateAchievedJuzFromReports($reports); }
        }
        $pct = $planned>0 ? min(100, round(($completed/$planned)*100,1)) : 0;
        return ['planned'=>$planned,'completed'=>$completed,'percentage'=>$pct];
    }

    private function calculateAchievedJuzFromReports($reports): int
    { $set=collect(); foreach($reports as $r){ $from=(int)ceil(($r->hefz_from_surat ?? 1)/4); $to=(int)ceil(($r->hefz_to_surat ?? 1)/4); for($j=$from;$j<=$to;$j++) $set->push($j);} return $set->unique()->count(); }

    private function calculatePageMetrics($students, int $classId, int $month, int $year): array
    {
        if ($students->isEmpty()) return ['planned'=>0,'completed'=>0,'percentage'=>0];
        $planned=0; $completed=0; $withPlans=0;
        foreach($students as $student){
            $plan = IjazasanadMemorizationPlan::where('student_id',$student->id)->where(function($q)use($month,$year,$classId){$q->where(function($q1)use($year,$month,$classId){$q1->whereYear('created_at',$year)->whereMonth('created_at',$month)->where('class_id',$classId)->where('status','active');})->orWhere(function($q2)use($year,$month,$classId){$q2->whereYear('start_date',$year)->whereMonth('start_date',$month)->where('class_id',$classId)->where('status','active');});})->first();
            if ($plan) {
                $plannedPages=0; try { if ($plan->study_direction=='backward') { $rows=DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [$plan->start_from_surat,$plan->start_from_ayat,$plan->to_surat,$plan->to_ayat]); $plannedPages = $rows[0]->numberofPagesSum ?? 0; } else { DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [$plan->start_from_surat,$plan->start_from_ayat,$plan->to_surat,$plan->to_ayat]); $res = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum"); $plannedPages = $res[0]->number_of_pages_sum ?? 0; } } catch (\Throwable $e) { $plannedPages=0; }
                $planned += $plannedPages; $withPlans++;
            }
            $completed += (int) (StudentIjazasanadMemorizationReport::where('student_id',$student->id)->where('class_id',$classId)->whereYear('created_at',$year)->whereMonth('created_at',$month)->sum('pages_memorized') ?? 0);
        }
        $pct = $planned>0 ? min(100, round(($completed/$planned)*100,1)) : 0;
        return ['planned'=>$planned,'completed'=>$completed,'percentage'=>$pct,'details'=>['students_with_plans'=>$withPlans]];
    }

    private function detectStudentLevel($studentDetails): ?string
    { if (method_exists($studentDetails,'loadMissing')) $studentDetails->loadMissing('studentProgramLevels.programlevel'); foreach ($studentDetails->studentProgramLevels as $spl){ if ($spl->programlevel){ $n=strtolower($spl->programlevel->title); if (str_contains($n,'level 1')) return 'level1'; if (str_contains($n,'level 2')) return 'level2'; } } return null; }

    private function calculateLevel1Completion($plan, $reports): array
    { $components=['talqeen','revision','jazariyah','seminars']; $total=0;$valid=0; foreach($components as $n){ $f="{$n}_from_lesson"; $t="{$n}_to_lesson"; if (!empty($plan->$f) && !empty($plan->$t) && $plan->$f <= $plan->$t){ $valid++; $range=$plan->$t-$plan->$f+1; $completed=$this->getUniqueCompletedLessonsForComponent($reports,$n); $achieved=count(array_intersect($completed, range($plan->$f,$plan->$t))); $total += ($range>0?($achieved/$range)*100:0); } } $overall=$valid>0?$total/$valid:0; return ['completion_rate'=>round($overall,2)]; }

    private function getUniqueCompletedLessonsForComponent($reports, string $componentName): array
    { $out=[]; $f="{$componentName}_from_lesson"; $t="{$componentName}_to_lesson"; foreach($reports as $r){ if(!empty($r->$f) && !empty($r->$t) && $r->$f <= $r->$t) $out=array_merge($out, range($r->$f,$r->$t)); } return array_unique($out); }

    private function formatProgressBar(float $percentage, string $color): string
    { $p=round($percentage,1); return "<div class='attendance-progress' style='position:relative;background:#f0f0f0;border-radius:4px;height:20px'><div class='bar' style='height:100%;background-color: {$color};width: {$p}%'></div><div class='label' style='position:absolute;inset:0;display:flex;align-items:center;justify-content:center;color:#fff;font-weight:bold;font-size:11px'>{$p}%</div></div>"; }
}



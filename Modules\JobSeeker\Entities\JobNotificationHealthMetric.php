<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class JobNotificationHealthMetric extends Model
{
    protected $table = 'job_notification_health_metrics';
    
    protected $fillable = [
        'metric_date',
        'metric_hour',
        'setups_processed',
        'recipients_processed',
        'emails_sent',
        'emails_failed',
        'processing_time_ms',
        'avg_email_time_ms',
        'max_memory_used_mb',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'metric_date' => 'date',
        'metric_hour' => 'integer',
        'setups_processed' => 'integer',
        'recipients_processed' => 'integer',
        'emails_sent' => 'integer',
        'emails_failed' => 'integer',
        'processing_time_ms' => 'integer',
        'avg_email_time_ms' => 'integer',
        'max_memory_used_mb' => 'integer',
    ];
    
    /**
     * Get or create a metric record for the current hour
     *
     * @return static
     */
    public static function getCurrentHourMetrics(): self
    {
        $now = Carbon::now();
        $date = $now->toDateString();
        $hour = $now->hour;
        
        return self::firstOrCreate(
            [
                'metric_date' => $date,
                'metric_hour' => $hour,
            ]
        );
    }
    
    /**
     * Increment the setups processed count
     *
     * @param int $count
     * @return bool
     */
    public function incrementSetupsProcessed(int $count = 1): bool
    {
        $this->setups_processed += $count;
        return $this->save();
    }
    
    /**
     * Increment the recipients processed count
     *
     * @param int $count
     * @return bool
     */
    public function incrementRecipientsProcessed(int $count = 1): bool
    {
        $this->recipients_processed += $count;
        return $this->save();
    }
    
    /**
     * Increment the emails sent count
     *
     * @param int $count
     * @return bool
     */
    public function incrementEmailsSent(int $count = 1): bool
    {
        $this->emails_sent += $count;
        return $this->save();
    }
    
    /**
     * Increment the emails failed count
     *
     * @param int $count
     * @return bool
     */
    public function incrementEmailsFailed(int $count = 1): bool
    {
        $this->emails_failed += $count;
        return $this->save();
    }
    
    /**
     * Update processing time metrics
     *
     * @param int $processingTimeMs
     * @return bool
     */
    public function updateProcessingTime(int $processingTimeMs): bool
    {
        $this->processing_time_ms += $processingTimeMs;
        return $this->save();
    }
    
    /**
     * Update average email time
     *
     * @param int $timeMs
     * @return bool
     */
    public function updateAvgEmailTime(int $timeMs): bool
    {
        // If we already have emails, calculate a new average
        if ($this->emails_sent > 0) {
            $totalTime = $this->avg_email_time_ms * $this->emails_sent;
            $totalTime += $timeMs;
            $this->avg_email_time_ms = (int)($totalTime / ($this->emails_sent + 1));
        } else {
            // First email, just set the average to this time
            $this->avg_email_time_ms = $timeMs;
        }
        
        return $this->save();
    }
    
    /**
     * Update max memory used
     *
     * @param int $memoryMb
     * @return bool
     */
    public function updateMaxMemory(int $memoryMb): bool
    {
        if ($memoryMb > $this->max_memory_used_mb) {
            $this->max_memory_used_mb = $memoryMb;
            return $this->save();
        }
        
        return false;
    }
} 
# Itqan Platform: Architectural Patterns & Conventions

This document provides a comprehensive overview of the key architectural patterns, design principles, and structural conventions used in the Itqan platform. Understanding these concepts is crucial for maintaining code quality, consistency, and scalability.

## High-Level Architecture Overview

The Itqan platform is architected as a **Modular Monolith**. This approach combines the simplicity of a single codebase with the organization and separation of concerns typically found in microservices. It allows for distinct feature sets (Modules) to be developed independently while sharing a common core infrastructure.

```mermaid
graph TD
    subgraph "User Interface"
        A[Client Browser]
    end

    subgraph "Infrastructure"
        B[Web Server (Nginx/Apache)]
    end

    subgraph "Application Core (Laravel Monolith)"
        C[Laravel Application]
        D[Database (MySQL)]
        E[Cache (Redis)]
        F[Queue (Redis/DB)]
    end

    A --> B --> C

    C -- Interacts with --> D
    C -- Interacts with --> E
    C -- Interacts with --> F

    subgraph "Application Internals"
        C --> G{Modular Monolith}
        G --> G1[Core Services]
        G --> G2[Modules]
    end

    subgraph "Modules (nwidart/laravel-modules)"
        G2 --> M1[Admission]
        G2 --> M2[HumanResource]
        G2 --> M3[Education]
        G2 --> M4[Finance]
        G2 --> M5[...]
    end
```

---

## Primary Architectural Patterns

The application is built upon a foundation of well-established software design patterns.

### 1. Modular Monolith

This is the cornerstone of the project's architecture, implemented using the `nwidart/laravel-modules` package.

-   **Structure**: The application is divided into distinct modules located in the `Modules/` directory. Each module (e.g., `Admission`, `HumanResource`, `Education`) functions as a mini-application, containing its own controllers, models (`Entities`), services, repositories, and routes.
-   **Benefits**: This pattern enhances organization, promotes loose coupling between different functional areas of the application, and allows development teams to work on separate modules with minimal overlap.

### 2. Model-View-Controller (MVC)

As a Laravel application, MVC is the fundamental pattern for structuring code.

-   **Models**: Located in `app/*.php` (for core entities) and `Modules/<ModuleName>/Entities/` (for module-specific entities). They represent the application's data and contain the business logic, following the Active Record pattern.
-   **Views**: These are the Blade templates that render the user interface. A critical project convention is that module views are located in `resources/views/modules/<module-name-lowercase>/`, not in the default module directory.
-   **Controllers**: Located in `app/Http/Controllers` and `Modules/<ModuleName>/Http/Controllers/`. They are responsible for handling incoming HTTP requests, orchestrating the response, and acting as the intermediary between Models and Views.

### 3. Service Layer Pattern

To keep controllers "thin" and business logic reusable, the project employs a Service Layer.

-   **Implementation**: Complex business logic is encapsulated within service classes (e.g., `EmailService`, `AdmissionService`). These services are then injected into controllers.
-   **Benefit**: This separates the "how" a business process is executed from the controller's responsibility of handling the request and response. It makes the logic more testable and reusable across different parts of the application (e.g., controllers, console commands, event listeners).

### 4. Repository Pattern

The Repository Pattern is used to decouple the business logic (Services) from the data access layer (Eloquent).

-   **Implementation**: A repository acts as a mediator between the Service Layer and the Eloquent Models. For instance, a service would call `userRepository->find($id)` instead of directly using `User::find($id)`.
-   **Benefit**: This abstraction makes the application more flexible. It allows the underlying data source or ORM to be changed without affecting the business logic. It also simplifies testing, as repositories can be easily mocked.

### 5. Active Record Pattern

Laravel's Eloquent ORM, which is used for all database interactions, is an implementation of the Active Record pattern.

-   **Implementation**: Eloquent models are not just data containers; they provide a powerful, object-oriented interface for interacting with database tables, including methods for creating, reading, updating, and deleting records, as well as defining relationships.
-   **Benefit**: This pattern simplifies database interactions and makes the code more readable and expressive.

### 6. Dependency Injection (DI) & Service Container

The application leverages Laravel's powerful Service Container to manage class dependencies and perform dependency injection.

-   **Implementation**: Instead of manually creating class instances (`new MyService()`), dependencies are "type-hinted" in the constructors or methods of controllers, services, and other classes. The Service Container automatically resolves and injects these dependencies.
-   **Benefit**: DI promotes loose coupling, making components easier to test, swap, and maintain.

---

## Request Flow: How the Patterns Interact

A typical HTTP request flows through the system, engaging each architectural pattern in a coordinated sequence.

```mermaid
sequenceDiagram
    participant Client
    participant Router
    participant Middleware
    participant Controller
    participant Service
    participant Repository
    participant Model
    participant Database
    participant View

    Client->>Router: HTTP Request
    Router->>Middleware: Route Match
    Middleware->>Controller: Processed Request (DI happens here)
    Controller->>Service: Method Call (e.g., createAdmission())
    Service->>Repository: Data Operation (e.g., findUser(), createApplication())
    Repository->>Model: Query Building (Eloquent ORM)
    Model->>Database: SQL Query
    Database-->>Model: Query Result
    Model-->>Repository: Eloquent Collection/Model
    Repository-->>Service: Data Response
    Service-->>Controller: Business Logic Result
    Controller->>View: Data for Rendering
    View-->>Client: HTTP Response
```

---

## Project-Specific Conventions & Rules

Beyond standard patterns, this project adheres to strict internal conventions that are a core part of its architecture.

1.  **Direct Database Schema Management**: Laravel Migrations are **forbidden**. All schema changes (e.g., `ALTER TABLE`) and initial data seeding are performed via raw SQL files stored in the `database/` directory. Eloquent is used only for application-level data manipulation (CRUD).
2.  **Custom Module View Location**: All Blade views for modules are stored centrally in `resources/views/modules/`. This is a deliberate override of the default `nwidart/laravel-modules` behavior.
3.  **Mobile-First Admin UI**: All administrative interfaces **must** be designed and implemented following mobile-first principles, primarily using Bootstrap's responsive grid and components.
4.  **Centralized Email Service**: All email sending **must** go through the `EmailService` to ensure consistency, reliability, and centralized control.

```mermaid
graph TD
    subgraph "Project Conventions"
        A["Direct SQL for Schema"]
        B["Custom View Location: `resources/views/modules/`"]
        C["Mobile-First Admin UI"]
        D["Centralized EmailService"]
    end
```

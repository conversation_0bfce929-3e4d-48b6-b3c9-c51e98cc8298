<?php

namespace App\Exports;

use App\Student;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class IjazasanadStudentReportExport implements WithMultipleSheets
{
    protected $studentId;
    protected $classId;
    protected $monthYear;
    protected $isLevel1;

    public function __construct($studentId, $classId, $monthYear, $isLevel1 = false)
    {
        $this->studentId = $studentId;
        $this->classId = $classId;
        $this->monthYear = $monthYear;
        $this->isLevel1 = $isLevel1;
    }

    public function sheets(): array
    {
        $date = Carbon::createFromFormat('F Y', $this->monthYear);
        return [
            new IjazasanadReportsSheet($this->studentId, $this->classId, $date, $this->isLevel1),
            new IjazasanadSummarySheet($this->studentId, $this->classId, $date, $this->isLevel1),
        ];
    }
}

class IjazasanadReportsSheet implements FromCollection, WithHeadings, WithMapping, WithTitle, ShouldAutoSize, WithStyles
{
    protected $studentId;
    protected $classId;
    protected $date;
    protected $isLevel1;

    public function __construct($studentId, $classId, $date, $isLevel1 = false)
    {
        $this->studentId = $studentId;
        $this->classId = $classId;
        $this->date = $date;
        $this->isLevel1 = $isLevel1;
    }

    public function collection()
    {
        $month = $this->date->month;
        $year = $this->date->year;

        // Use level-aware query logic same as PDF controller
        $query = \App\StudentIjazasanadMemorizationReport::where('student_id', $this->studentId)
            ->where('class_id', $this->classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month);

        if ($this->isLevel1) {
            // Level 1: Query for lesson-based data
            $query->where(function ($q) {
                $q->whereNotNull('talqeen_from_lesson')
                  ->orWhereNotNull('revision_from_lesson')
                  ->orWhereNotNull('jazariyah_from_lesson')
                  ->orWhereNotNull('seminars_from_lesson');
            });
        } else {
            // Level 2: Query for Surah/Ayat-based data
            $query->whereNotNull('hefz_from_surat')
                  ->whereNotNull('hefz_from_ayat')
                  ->whereNotNull('hefz_to_surat')
                  ->whereNotNull('hefz_to_ayat');
        }

        $reports = $query->with(['result', 'attendanceOptions'])
                        ->orderBy('created_at', 'asc')
                        ->get();

        return $reports;
    }

    public function headings(): array
    {
        if ($this->isLevel1) {
            return [
                'ID',
                'Date',
                'Day',
                'Attendance',
                'Talqeen From',
                'Talqeen To',
                'Revision From',
                'Revision To',
                'Jazariyah From',
                'Jazariyah To',
                'Seminars From',
                'Seminars To',
                'Teacher Comments',
                'Performance'
            ];
        } else {
            return [
                'ID',
                'Date',
                'Day',
                'Attendance',
                'From Surah',
                'From Ayat',
                'To Surah',
                'To Ayat',
                'Juz\' Covered',
                'Pages Memorized',
                'Teacher Comments',
                'Performance'
            ];
        }
    }

    public function map($report): array
    {
        static $index = 0;
        $index++;
        
        $date = \Carbon\Carbon::parse($report->created_at);
        
        if ($this->isLevel1) {
            return [
                $index,
                $date->format('Y-m-d'),
                $date->format('D'),
                $this->getAttendanceText($report->attendance_id),
                $report->talqeen_from_lesson ?? '—',
                $report->talqeen_to_lesson ?? '—',
                $report->revision_from_lesson ?? '—',
                $report->revision_to_lesson ?? '—',
                $report->jazariyah_from_lesson ?? '—',
                $report->jazariyah_to_lesson ?? '—',
                $report->seminars_from_lesson ?? '—',
                $report->seminars_to_lesson ?? '—',
                strip_tags($report->ijazasanad_evaluation_note ?? $report->hefz_evaluation_note ?? '—'),
                $this->getEvaluationText($report->ijazasanad_evaluation_id ?? $report->hefz_evaluation_id)
            ];
        } else {
            return [
                $index,
                $date->format('Y-m-d'),
                $date->format('D'),
                $this->getAttendanceText($report->attendance_id),
                $this->getSurahDisplayName($report->hefz_from_surat),
                $report->hefz_from_ayat ?? '—',
                $this->getSurahDisplayName($report->hefz_to_surat),
                $report->hefz_to_ayat ?? '—',
                $this->calculateJuzCovered($report),
                $report->pages_memorized ?? 0,
                strip_tags($report->ijazasanad_evaluation_note ?? $report->hefz_evaluation_note ?? '—'),
                $this->getEvaluationText($report->ijazasanad_evaluation_id ?? $report->hefz_evaluation_id)
            ];
        }
    }

    public function title(): string
    {
        return 'Daily Reports';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }

    // Helper methods matching StudentIjazasanadReportsDatatablesController logic
    private function getSurahDisplayName(?int $surahId): string
    {
        if (!$surahId) {
            return '—';
        }

        $surah = \App\MoshafSurah::where('id', $surahId)->first();
        if (!$surah) {
            return '—';
        }

        return "{$surah->id}. {$surah->eng_name}";
    }

    private function getAttendanceText(?int $attendanceId): string
    {
        if (!$attendanceId) {
            return '—';
        }

        $attendance = \App\AttendanceOption::where('id', $attendanceId)->first();
        return $attendance ? $attendance->title : '—';
    }

    private function getEvaluationText(?int $evaluationId): string
    {
        if (!$evaluationId) {
            return '—';
        }

        $evaluation = \App\EvaluationSchemaOption::where('id', $evaluationId)->first();
        return $evaluation ? $evaluation->title : '—';
    }

    private function calculateJuzCovered($report): string
    {
        if (!$report->hefz_from_surat || !$report->hefz_to_surat) {
            return '—';
        }

        // Use same logic as DataTables controller
        $fromJuz = $this->getJuzFromSurahAyat($report->hefz_from_surat, $report->hefz_from_ayat);
        $toJuz = $this->getJuzFromSurahAyat($report->hefz_to_surat, $report->hefz_to_ayat);

        if ($fromJuz && $toJuz) {
            if ($fromJuz == $toJuz) {
                return "Juz' {$fromJuz}";
            } else {
                return "Juz' {$fromJuz}-{$toJuz}";
            }
        }

        return '—';
    }

    private function getJuzFromSurahAyat(?int $surahId, ?int $ayat): ?int
    {
        if (!$surahId || !$ayat) {
            return null;
        }
        // Simplified calculation - you may need to implement more accurate Juz calculation
        return (int)ceil($surahId / 4);
    }
}

class IjazasanadSummarySheet implements FromCollection, WithHeadings, WithMapping, WithTitle, ShouldAutoSize, WithStyles
{
    protected $studentId;
    protected $classId;
    protected $date;
    protected $isLevel1;

    public function __construct($studentId, $classId, $date, $isLevel1 = false)
    {
        $this->studentId = $studentId;
        $this->classId = $classId;
        $this->date = $date;
        $this->isLevel1 = $isLevel1;
    }

    public function collection()
    {
        $summaryData = $this->getSummaryData();
        return collect([$summaryData]);
    }

    public function headings(): array
    {
        return [
            'Total Reports',
            'Total Pages Memorized',
            'Average Performance (%)',
            'Attendance (%)',
            'Completion Rate (%)'
        ];
    }

    public function map($row): array
    {
        return [
            $row['total_reports'],
            $row['total_pages_memorized'],
            round($row['average_performance'], 2),
            round($row['attendance_percentage'], 2),
            round($row['completion_rate'], 2)
        ];
    }

    public function title(): string
    {
        return 'Monthly Summary';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }

    private function getSummaryData()
    {
        $month = $this->date->month;
        $year  = $this->date->year;

        // Fetch student reports for the month
        $reports = \DB::table('student_ijazasanad_memorization_report as simr')
            ->leftJoin('evaluation_schema_options as eso', 'simr.ijazasanad_evaluation_id', '=', 'eso.id')
            ->where('simr.student_id', $this->studentId)
            ->where('simr.class_id', $this->classId)
            ->whereYear('simr.created_at', $year)
            ->whereMonth('simr.created_at', $month)
            ->select('simr.*', 'eso.weight')
            ->get();

        $totalReports        = $reports->count();
        $totalPagesMemorized = $reports->sum('pages_memorized');

        // Average Performance (weighted)
        $validEvaluations    = $reports->filter(function ($r) { return !is_null($r->weight); });
        $totalWeightedScore  = $validEvaluations->sum('weight');
        $averagePerformance  = $validEvaluations->count() > 0
            ? ($totalWeightedScore / $validEvaluations->count()) * 100
            : 0;

        // Attendance % (using timetable aware logic)
        $class = \App\Classes::with('timetable')->find($this->classId);
        $totalScheduledClasses = 0;
        if ($class && $class->timetable) {
            $totalScheduledClasses = $class->timetable->daysCountPerMonth($month, $year);
        }
        if ($totalScheduledClasses <= 0) {
            $totalScheduledClasses = max(1, $totalReports);
        }
        $attendedDays        = $reports->whereIn('attendance_id', [1, 2])->count(); // 1=Late,2=Present
        $attendancePercent   = $totalScheduledClasses > 0 ? ($attendedDays / $totalScheduledClasses) * 100 : 0;

        // Completion Rate % (level–aware)
        $completionRate = $this->calculateCompletionRate($month, $year, $reports);

        return [
            'total_reports'          => $totalReports,
            'total_pages_memorized'  => $totalPagesMemorized,
            'average_performance'    => $averagePerformance,
            'attendance_percentage'  => $attendancePercent,
            'completion_rate'        => $completionRate,
        ];
    }

    // ---------------- Level / Completion Helpers ---------------- //

    private function calculateCompletionRate(int $month, int $year, $reports): float
    {
        // Fetch active memorization plan for the month
        $plan = \App\IjazasanadMemorizationPlan::where('student_id', $this->studentId)
            ->where('class_id',   $this->classId)
            ->where('status',    'active')
            ->where(function ($q) use ($month, $year) {
                $q->whereYear('created_at', $year)->whereMonth('created_at', $month)
                  ->orWhereYear('start_date', $year)->whereMonth('start_date', $month);
            })
            ->orderByDesc('id')
            ->first();

        if (!$plan) {
            return 0.0; // no plan, cannot compute
        }

        $student = \App\Student::with('studentProgramLevels.programlevel')->find($this->studentId);
        $level   = $this->detectStudentLevel($student);

        if ($level === 'level1') {
            return $this->calculateLevel1Completion($plan, $reports);
        }

        // Default to Level-2 logic
        return $this->calculateLevel2Completion($plan, $reports);
    }

    private function detectStudentLevel($student): ?string
    {
        if (!$student) { return null; }
        foreach ($student->studentProgramLevels as $spl) {
            if ($spl->programlevel && isset($spl->programlevel->title)) {
                $title = strtolower($spl->programlevel->title);
                if (str_contains($title, 'level 1')) { return 'level1'; }
                if (str_contains($title, 'level 2')) { return 'level2'; }
            }
        }
        return null;
    }

    private function calculateLevel1Completion($plan, $reports): float
    {
        $components = ['talqeen', 'revision', 'jazariyah', 'seminars'];
        $totalCompletion = 0;
        $validComponents = 0;

        foreach ($components as $component) {
            $fromField = $component . '_from_lesson';
            $toField   = $component . '_to_lesson';

            if (!empty($plan->$fromField) && !empty($plan->$toField) && $plan->$fromField <= $plan->$toField) {
                $validComponents++;
                $plannedRange = range($plan->$fromField, $plan->$toField);

                $completedLessons = $this->getUniqueCompletedLessonsForComponent($reports, $component);
                $achieved         = count(array_intersect($completedLessons, $plannedRange));
                $componentPercent = (count($plannedRange) > 0) ? ($achieved / count($plannedRange)) * 100 : 0;
                $totalCompletion += $componentPercent;
            }
        }

        return $validComponents > 0 ? $totalCompletion / $validComponents : 0;
    }

    private function getUniqueCompletedLessonsForComponent($reports, string $component): array
    {
        $fromField = $component . '_from_lesson';
        $toField   = $component . '_to_lesson';
        $lessons   = [];

        foreach ($reports as $report) {
            if (!empty($report->$fromField) && !empty($report->$toField) && $report->$fromField <= $report->$toField) {
                $lessons = array_merge($lessons, range($report->$fromField, $report->$toField));
            }
        }

        return array_unique($lessons);
    }

    private function calculateLevel2Completion($plan, $reports): float
    {
        // Determine planned pages via stored procedures
        $plannedPages = 0;
        if (!empty($plan->start_from_surat) && !empty($plan->start_from_ayat) && !empty($plan->to_surat) && !empty($plan->to_ayat)) {
            try {
                if ($plan->study_direction === 'backward') {
                    $pages = \DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                        $plan->start_from_surat,
                        $plan->start_from_ayat,
                        $plan->to_surat,
                        $plan->to_ayat
                    ]);
                    $plannedPages = $pages[0]->numberofPagesSum ?? 0;
                } else {
                    \DB::select('CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @pg)', [
                        $plan->start_from_surat,
                        $plan->start_from_ayat,
                        $plan->to_surat,
                        $plan->to_ayat
                    ]);
                    $res = \DB::select('SELECT @pg as number_of_pages_sum');
                    $plannedPages = $res[0]->number_of_pages_sum ?? 0;
                }
            } catch (\Exception $e) {
                $plannedPages = 0;
            }
        }

        $achievedPages = $reports->sum('pages_memorized');
        if ($plannedPages > 0 && $achievedPages > $plannedPages) {
            $achievedPages = $plannedPages;
        }

        return $plannedPages > 0 ? ($achievedPages / $plannedPages) * 100 : 0;
    }
}
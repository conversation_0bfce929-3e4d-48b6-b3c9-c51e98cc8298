<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Mo<PERSON>les\JobSeeker\Entities\JobSeekerResume;
use <PERSON><PERSON>les\JobSeeker\Http\Requests\AiLinkedInImportRequest;
use Modules\JobSeeker\Http\Requests\AiResumeStoreRequest;
use Modules\JobSeeker\Http\Requests\AiVoiceResumeRequest;
use Mo<PERSON>les\JobSeeker\Services\Ai\AiResumeService;

/**
 * AiResumeController manages resume upload, import, and lifecycle operations for AI tailoring.
 * 
 * Purpose: Handle file uploads, LinkedIn imports, voice transcripts, and resume management.
 * Security: Enforces user authentication, ownership verification, and permission middleware.
 * Business logic: Delegates to AiResumeService for complex operations and validation.
 * UI feedback: Returns JSON for AJAX calls with proper error handling and progress indicators.
 * Audit trail: Logs all operations with correlation IDs for tracing and debugging.
 */
final class AiResumeController extends Controller
{
    public function __construct(
        private readonly AiResumeService $resumeService
    ) {
        // All routes require job_seeker authentication and manage_resumes permission
        $this->middleware(['auth:job_seeker', 'permission:jobseeker.manage_resumes']);
    }

    /**
     * List all resumes for the authenticated user
     * 
     * @param Request $request
     * @return JsonResponse List of user's resumes with metadata
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth('job_seeker')->user();
        $correlationId = 'resume_list_' . uniqid();

        Log::info('AiResumeController: Listing resumes for user', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'request_ip' => $request->ip(),
        ]);

        try {
            $resumes = $this->resumeService->listForUser($user->id);
            $canAddMore = $this->resumeService->canAddMore($user->id);

            $resumeData = $resumes->map(function (JobSeekerResume $resume) {
                return [
                    'id' => $resume->id,
                    'title' => $resume->title,
                    'source' => $resume->source,
                    'size_formatted' => $resume->formatted_size,
                    'content_preview' => $resume->content_preview,
                    'created_at' => $resume->created_at?->format('M j, Y g:i A'),
                    'is_file' => $resume->isFileUpload(),
                    'download_url' => $resume->getFileUrl(),
                ];
            });

            Log::info('AiResumeController: Successfully listed resumes', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'resume_count' => $resumes->count(),
                'can_add_more' => $canAddMore,
            ]);

            return response()->json([
                'success' => true,
                'data' => $resumeData,
                'meta' => [
                    'total_count' => $resumes->count(),
                    'can_add_more' => $canAddMore,
                    'max_allowed' => 5,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('AiResumeController: Failed to list resumes', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to load resumes. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Store an uploaded resume file
     * 
     * @param AiResumeStoreRequest $request Validated file upload request
     * @return JsonResponse Success response with resume data or error details
     */
    public function store(AiResumeStoreRequest $request): JsonResponse
    {
        $user = auth('job_seeker')->user();
        $correlationId = 'resume_store_' . uniqid();

        Log::info('AiResumeController: Processing file upload', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'title' => $request->input('title'),
            'file_name' => $request->file('resume_file')->getClientOriginalName(),
            'file_size' => $request->file('resume_file')->getSize(),
            'request_ip' => $request->ip(),
        ]);

        try {
            $resume = $this->resumeService->storeUploadedFile(
                $user,
                $request->file('resume_file'),
                $request->input('title')
            );

            Log::info('AiResumeController: Successfully stored uploaded resume', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'resume_id' => $resume->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Resume uploaded successfully!',
                'data' => [
                    'id' => $resume->id,
                    'title' => $resume->title,
                    'source' => $resume->source,
                    'size_formatted' => $resume->formatted_size,
                    'content_preview' => $resume->content_preview,
                    'created_at' => $resume->created_at?->format('M j, Y g:i A'),
                    'is_file' => $resume->isFileUpload(),
                    'download_url' => $resume->getFileUrl(),
                ],
                'meta' => [
                    'can_add_more' => $this->resumeService->canAddMore($user->id),
                ],
            ], 201);

        } catch (\Exception $e) {
            Log::error('AiResumeController: Failed to store uploaded resume', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 422);
        }
    }

    /**
     * Import LinkedIn profile content as a resume
     * 
     * @param AiLinkedInImportRequest $request Validated LinkedIn import request
     * @return JsonResponse Success response with resume data or error details
     */
    public function importLinkedIn(AiLinkedInImportRequest $request): JsonResponse
    {
        $user = auth('job_seeker')->user();
        $correlationId = 'linkedin_import_' . uniqid();

        Log::info('AiResumeController: Processing LinkedIn import', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'title' => $request->input('title'),
            'content_length' => strlen($request->input('content_text')),
            'has_linkedin_url' => !empty($request->input('linkedin_url')),
            'request_ip' => $request->ip(),
        ]);

        try {
            $resume = $this->resumeService->storeLinkedIn(
                $user,
                $request->input('title'),
                $request->input('content_text'),
                $request->input('linkedin_url')
            );

            Log::info('AiResumeController: Successfully imported LinkedIn resume', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'resume_id' => $resume->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'LinkedIn profile imported successfully!',
                'data' => [
                    'id' => $resume->id,
                    'title' => $resume->title,
                    'source' => $resume->source,
                    'size_formatted' => $resume->formatted_size,
                    'content_preview' => $resume->content_preview,
                    'created_at' => $resume->created_at?->format('M j, Y g:i A'),
                    'is_file' => $resume->isFileUpload(),
                ],
                'meta' => [
                    'can_add_more' => $this->resumeService->canAddMore($user->id),
                ],
            ], 201);

        } catch (\Exception $e) {
            Log::error('AiResumeController: Failed to import LinkedIn resume', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 422);
        }
    }

    /**
     * Store voice transcript as a resume
     * 
     * @param AiVoiceResumeRequest $request Validated voice resume request
     * @return JsonResponse Success response with resume data or error details
     */
    public function storeVoice(AiVoiceResumeRequest $request): JsonResponse
    {
        $user = auth('job_seeker')->user();
        $correlationId = 'voice_store_' . uniqid();

        Log::info('AiResumeController: Processing voice resume', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'title' => $request->input('title'),
            'has_transcript' => !empty($request->input('transcript_text')),
            'has_audio_file' => $request->hasFile('audio_file'),
            'duration_seconds' => $request->input('duration_seconds'),
            'request_ip' => $request->ip(),
        ]);

        try {
            // For this implementation, we only handle transcript text
            // Audio file processing would require additional transcription service integration
            if (!$request->has('transcript_text') || empty($request->input('transcript_text'))) {
                throw new \Exception('Voice transcript is required. Audio file processing is not yet implemented.');
            }

            $resume = $this->resumeService->storeVoiceTranscript(
                $user,
                $request->input('title'),
                $request->input('transcript_text'),
                $request->input('duration_seconds')
            );

            Log::info('AiResumeController: Successfully stored voice resume', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'resume_id' => $resume->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Voice resume saved successfully!',
                'data' => [
                    'id' => $resume->id,
                    'title' => $resume->title,
                    'source' => $resume->source,
                    'size_formatted' => $resume->formatted_size,
                    'content_preview' => $resume->content_preview,
                    'created_at' => $resume->created_at?->format('M j, Y g:i A'),
                    'is_file' => $resume->isFileUpload(),
                ],
                'meta' => [
                    'can_add_more' => $this->resumeService->canAddMore($user->id),
                ],
            ], 201);

        } catch (\Exception $e) {
            Log::error('AiResumeController: Failed to store voice resume', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 422);
        }
    }

    /**
     * Delete a resume with ownership verification
     * 
     * @param Request $request
     * @param int $resumeId Resume ID to delete
     * @return JsonResponse Success confirmation or error details
     */
    public function destroy(Request $request, int $resumeId): JsonResponse
    {
        $user = auth('job_seeker')->user();
        $correlationId = 'resume_delete_' . uniqid();

        Log::info('AiResumeController: Processing resume deletion', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'resume_id' => $resumeId,
            'request_ip' => $request->ip(),
        ]);

        try {
            $resume = JobSeekerResume::find($resumeId);

            if (!$resume) {
                Log::warning('AiResumeController: Resume not found for deletion', [
                    'correlation_id' => $correlationId,
                    'job_seeker_id' => $user->id,
                    'resume_id' => $resumeId,
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Resume not found.',
                ], 404);
            }

            $this->resumeService->deleteResume($resume, $user->id);

            Log::info('AiResumeController: Successfully deleted resume', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'resume_id' => $resumeId,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Resume deleted successfully!',
                'meta' => [
                    'can_add_more' => $this->resumeService->canAddMore($user->id),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('AiResumeController: Failed to delete resume', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'resume_id' => $resumeId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 422);
        }
    }

    /**
     * Download a resume file with ownership verification
     * 
     * @param Request $request
     * @param int $resumeId Resume ID to download
     * @return \Symfony\Component\HttpFoundation\StreamedResponse|JsonResponse File download or error
     */
    public function download(Request $request, int $resumeId)
    {
        $user = auth('job_seeker')->user();
        $correlationId = 'resume_download_' . uniqid();

        Log::info('AiResumeController: Processing resume download', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'resume_id' => $resumeId,
            'request_ip' => $request->ip(),
        ]);

        try {
            $resume = JobSeekerResume::where('id', $resumeId)
                ->where('job_seeker_id', $user->id)
                ->first();

            if (!$resume) {
                return response()->json([
                    'success' => false,
                    'message' => 'Resume not found.',
                ], 404);
            }

            if (!$resume->isFileUpload()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This resume is not a downloadable file.',
                ], 400);
            }

            if (!$resume->storage_path || !Storage::disk('local')->exists($resume->storage_path)) {
                Log::error('AiResumeController: Resume file not found on disk', [
                    'correlation_id' => $correlationId,
                    'resume_id' => $resumeId,
                    'storage_path' => $resume->storage_path,
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Resume file not found.',
                ], 404);
            }

            Log::info('AiResumeController: Serving resume file download', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'resume_id' => $resumeId,
                'storage_path' => $resume->storage_path,
            ]);

            return Storage::disk('local')->download(
                $resume->storage_path,
                $resume->title . '.' . pathinfo($resume->storage_path, PATHINFO_EXTENSION)
            );

        } catch (\Exception $e) {
            Log::error('AiResumeController: Failed to serve resume download', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'resume_id' => $resumeId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to download resume. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}

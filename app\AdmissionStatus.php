<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\SoftDeletes;
use \Illuminate\Support\Str;

class AdmissionStatus extends Model
{

    use SoftDeletes;



    protected $table = 'admission_statuses';
    protected $fillable = ['display_name'
        , 'status'
    ];





   public function admission(){


       return $this->hasMany(Admission::class);
   }


}

<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Document;
use App\Employee;
use App\News;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;

class EmployeeDocumentReorderer extends Controller
{

    public function __invoke(Request $request)
    {
        foreach($request->get('document') as $row)
        {

            Document::find($row['id'])->update([
                'position' => $row['position']
            ]);
        }

        return response()->noContent();
    }
}

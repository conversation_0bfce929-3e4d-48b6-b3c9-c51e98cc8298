<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

/**
 * System Error Alert Entity
 * 
 * Stores comprehensive error alerts for system monitoring and notification
 */
final class SystemErrorAlert extends Model
{
    use HasFactory;

    protected $table = 'system_error_alerts';

    protected $fillable = [
        'category',
        'severity',
        'title',
        'message',
        'context',
        'exception_details',
        'resolved_at',
        'resolved_by',
        'resolution_notes',
    ];

    protected $casts = [
        'context' => 'array',
        'exception_details' => 'array',
        'resolved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Severity levels
     */
    public const SEVERITY_CRITICAL = 'critical';
    public const SEVERITY_HIGH = 'high';
    public const SEVERITY_MEDIUM = 'medium';
    public const SEVERITY_LOW = 'low';

    /**
     * Error categories
     */
    public const CATEGORY_JOB_FETCH = 'job_fetch_failure';
    public const CATEGORY_EMAIL_SEND = 'email_send_failure';
    public const CATEGORY_API_TIMEOUT = 'api_timeout';
    public const CATEGORY_DATABASE = 'database_error';
    public const CATEGORY_PERFORMANCE = 'performance_degradation';
    public const CATEGORY_SYSTEM = 'system_error';

    /**
     * Check if alert is resolved
     */
    public function isResolved(): bool
    {
        return $this->resolved_at !== null;
    }

    /**
     * Mark alert as resolved
     */
    public function markResolved(string $resolvedBy, string $notes = ''): bool
    {
        $this->resolved_at = now();
        $this->resolved_by = $resolvedBy;
        $this->resolution_notes = $notes;
        
        return $this->save();
    }

    /**
     * Get severity color for UI display
     */
    public function getSeverityColor(): string
    {
        return match ($this->severity) {
            self::SEVERITY_CRITICAL => '#dc3545',
            self::SEVERITY_HIGH => '#fd7e14',
            self::SEVERITY_MEDIUM => '#ffc107',
            self::SEVERITY_LOW => '#17a2b8',
            default => '#6c757d',
        };
    }

    /**
     * Get category icon for UI display
     */
    public function getCategoryIcon(): string
    {
        return match ($this->category) {
            self::CATEGORY_JOB_FETCH => 'fas fa-download',
            self::CATEGORY_EMAIL_SEND => 'fas fa-envelope',
            self::CATEGORY_API_TIMEOUT => 'fas fa-clock',
            self::CATEGORY_DATABASE => 'fas fa-database',
            self::CATEGORY_PERFORMANCE => 'fas fa-tachometer-alt',
            self::CATEGORY_SYSTEM => 'fas fa-server',
            default => 'fas fa-exclamation-triangle',
        };
    }

    /**
     * Get formatted severity for display
     */
    public function getFormattedSeverity(): string
    {
        return strtoupper($this->severity);
    }

    /**
     * Get formatted category for display
     */
    public function getFormattedCategory(): string
    {
        return str_replace('_', ' ', ucwords($this->category, '_'));
    }

    /**
     * Scope for unresolved alerts
     */
    public function scopeUnresolved($query)
    {
        return $query->whereNull('resolved_at');
    }

    /**
     * Scope for resolved alerts
     */
    public function scopeResolved($query)
    {
        return $query->whereNotNull('resolved_at');
    }

    /**
     * Scope for specific severity
     */
    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Scope for specific category
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for recent alerts (last 24 hours)
     */
    public function scopeRecent($query)
    {
        return $query->where('created_at', '>=', now()->subDay());
    }

    /**
     * Get recent critical alerts count
     */
    public static function getRecentCriticalCount(): int
    {
        return static::bySeverity(self::SEVERITY_CRITICAL)
            ->recent()
            ->unresolved()
            ->count();
    }

    /**
     * Get alerts summary for dashboard
     */
    public static function getAlertsSummary(): array
    {
        $summary = [
            'total' => static::count(),
            'unresolved' => static::unresolved()->count(),
            'recent' => static::recent()->count(),
            'by_severity' => [],
            'by_category' => [],
        ];

        // Count by severity
        foreach ([self::SEVERITY_CRITICAL, self::SEVERITY_HIGH, self::SEVERITY_MEDIUM, self::SEVERITY_LOW] as $severity) {
            $summary['by_severity'][$severity] = static::bySeverity($severity)->unresolved()->count();
        }

        // Count by category
        foreach ([
            self::CATEGORY_JOB_FETCH,
            self::CATEGORY_EMAIL_SEND,
            self::CATEGORY_API_TIMEOUT,
            self::CATEGORY_DATABASE,
            self::CATEGORY_PERFORMANCE,
            self::CATEGORY_SYSTEM
        ] as $category) {
            $summary['by_category'][$category] = static::byCategory($category)->unresolved()->count();
        }

        return $summary;
    }
}

<?php

namespace App\Http\Middleware;

use App\MissedClockOut;
use App\Attendance;
use Closure;
use http\Env\Response;
use Session;
use App\User;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CheckMissedClockOutMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        try {
            // First check if the table exists to prevent errors
            if (Schema::hasTable('missed_clockouts')) {
                $employeeId = \Auth::guard("employee")->user()->id;
                
                // Get all non-deleted missed clockout records for this employee
                $missedClockOuts = MissedClockOut::whereNull('deleted_at')
                    ->where('employee_id', $employeeId)
                    ->get();
                    
                if ($missedClockOuts->isNotEmpty()) {
                    // Check if any of these missed clockouts are still valid
                    $hasValidMissedClockout = false;
                    
                    foreach ($missedClockOuts as $missedClockOut) {
                        // Check if this missed clockout is still relevant
                        if ($this->isMissedClockoutStillValid($missedClockOut, $employeeId)) {
                            $hasValidMissedClockout = true;
                            break;
                        }
                    }
                    
                    // If we found valid missed clockouts, redirect to the form
                    if ($hasValidMissedClockout) {
                        return redirect('missed-clockout');
                    } else {
                        // Clean up invalid missed clockout records
                        $this->cleanupInvalidMissedClockouts($missedClockOuts, $employeeId);
                    }
                }
            } else {
                // Log that the table doesn't exist but allow the user to proceed
                Log::warning('missed_clockouts table does not exist, skipping missed clock out check');
            }
        } catch (\Exception $e) {
            // Log any errors but allow the request to continue
            Log::error('Error checking missed clock out: ' . $e->getMessage());
        }

        return $next($request);
    }
    
    /**
     * Check if a missed clockout record is still valid (i.e., the employee hasn't completed their attendance)
     *
     * @param MissedClockOut $missedClockOut
     * @param int $employeeId
     * @return bool
     */
    private function isMissedClockoutStillValid(MissedClockOut $missedClockOut, int $employeeId): bool
    {
        try {
            // Get the date of the missed clockout
            $missedDate = Carbon::parse($missedClockOut->clock)->toDateString();
            
            // Check if the employee has already clocked out on that day
            $hasClockOut = Attendance::where('employee_id', $employeeId)
                ->whereDate('clock', $missedDate)
                ->where('type', 'out')
                ->exists();
                
            // If they have a clock out record for that day, this missed clockout is no longer valid
            if ($hasClockOut) {
                return false;
            }
            
            // Check if the missed clockout is from more than 3 days ago (cleanup old records)
            $missedDateTime = Carbon::parse($missedClockOut->clock);
            $threeDaysAgo = Carbon::now()->subDays(3);
            
            if ($missedDateTime->lt($threeDaysAgo)) {
                return false; // Too old, no longer valid
            }
            
            return true; // Still valid
        } catch (\Exception $e) {
            Log::error('Error validating missed clockout: ' . $e->getMessage());
            return false; // If we can't validate, consider it invalid
        }
    }
    
    /**
     * Clean up invalid missed clockout records
     *
     * @param \Illuminate\Database\Eloquent\Collection $missedClockOuts
     * @param int $employeeId
     */
    private function cleanupInvalidMissedClockouts($missedClockOuts, int $employeeId): void
    {
        try {
            $invalidIds = [];
            
            foreach ($missedClockOuts as $missedClockOut) {
                if (!$this->isMissedClockoutStillValid($missedClockOut, $employeeId)) {
                    $invalidIds[] = $missedClockOut->id;
                }
            }
            
            if (!empty($invalidIds)) {
                MissedClockOut::whereIn('id', $invalidIds)->update(['deleted_at' => Carbon::now()]);
                Log::info("Cleaned up " . count($invalidIds) . " invalid missed clockout records for employee {$employeeId}");
            }
        } catch (\Exception $e) {
            Log::error('Error cleaning up invalid missed clockouts: ' . $e->getMessage());
        }
    }
}

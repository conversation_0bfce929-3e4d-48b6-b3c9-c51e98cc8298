<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Http\Controllers;

use App\AttendanceOption;
use App\Classes;
use App\Http\Controllers\Controller;
use App\IjazasanadMemorizationPlan;
use App\MoshafSurah;
use App\ProgramLevelLesson;
use App\Student;
use App\StudentIjazasanadMemorizationReport;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;

/**
 * Aggregated monthly Ijazasanad report across MULTIPLE classes.
 * Adds class_id/class_name to each row so frontend can group by class.
 */
final class MonthlyIjazasanadReportAggregatedController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        try {
            $classIds = $this->parseClassIds($request->input('classId'));
            $monthYear = (string) $request->input('classDate');
            if (empty($classIds) || $monthYear === '') {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            $date = Carbon::createFromFormat('M Y', $monthYear);
            $month = (int) $date->month;
            $year  = (int) $date->year;

            $rows = [];
            foreach ($classIds as $classId) {
                $class = Classes::find($classId);
                if (!$class) continue;

                $students = Student::whereHas('joint_classes', function ($query) use ($classId) {
                        $query->where('class_id', $classId);
                    })
                    ->where('status', 'active')
                    ->with(['studentProgramLevels.programlevel'])
                    ->orderBy('full_name', 'asc')
                    ->get();

                foreach ($students as $index => $student) {
                    $attendanceData     = $this->calculateAttendance($student->id, $classId, $month, $year);
                    $achievementPercent = $this->calculateAchievement($student, $classId, $month, $year);
                    $entryData          = $this->getEntryData($student->id, $classId, $month, $year);
                    $monthlyPlan        = $this->getMonthlyPlan($student->id, $month, $year);
                    $monthlyAchievement = $this->getMonthlyAchievement($student->id, $classId, $month, $year);

                    $rows[] = [
                        'DT_RowIndex'           => $index + 1,
                        'class_id'              => (int) $classId,
                        'class_name'            => $class->class_code ?? (string) $class->name,
                        'student_id'            => $student->id,
                        'student'               => $this->formatStudentName($student),
                        'entry1'                => $entryData['entry1'],
                        'entry2'                => $entryData['entry2'],
                        'entry3'                => $entryData['entry3'],
                        'entry4'                => $entryData['entry4'],
                        'monthlyPlan'           => $monthlyPlan,
                        'monthlyAchievement'    => $monthlyAchievement,
                        'attendancePercentage'  => $this->formatProgressBarWithPopup($attendanceData['percentage'], '#28a745', $attendanceData['details']),
                        'achievementPercentage' => $this->formatAchievementProgressBarWithPopup($achievementPercent, '#1fff0f', $this->getAchievementDetails($student, $classId, $month, $year)),
                    ];
                }
            }

            return DataTables::of($rows)
                ->rawColumns(['student', 'attendancePercentage', 'achievementPercentage', 'entry1', 'entry2', 'entry3', 'entry4', 'monthlyPlan', 'monthlyAchievement'])
                ->toJson();
        } catch (\Throwable $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /** @return int[] */
    private function parseClassIds($input): array
    {
        if (is_array($input)) { $ids = $input; }
        elseif (is_string($input)) { $ids = array_filter(array_map('trim', explode(',', $input))); }
        else { $ids = []; }
        $ids = array_values(array_unique(array_map('intval', $ids)));
        return array_values(array_filter($ids, static fn (int $id) => $id > 0));
    }

    // --- Borrowed helpers from single-class controller -------------------
    private function getMonthlyPlan(int $studentId, int $month, int $year): string
    {
        $plan = IjazasanadMemorizationPlan::where('student_id', $studentId)
            ->where(function ($query) use ($month, $year) {
                $query->whereYear('start_date', $year)->whereMonth('start_date', $month);
            })
            ->orWhere(function ($query) use ($month, $year) {
                $query->whereYear('created_at', $year)->whereMonth('created_at', $month);
            })
            ->first();
        if (!$plan) return '—';
        $planContent = '';
        if (!empty($plan->from_surat_juz_id) && !empty($plan->to_surat_juz_id)) {
            $planContent .= "<div>Juz' {$plan->from_surat_juz_id} - Juz' {$plan->to_surat_juz_id}</div>";
        }
        if (!empty($plan->start_from_surat) && !empty($plan->start_from_ayat) && !empty($plan->to_surat) && !empty($plan->to_ayat)) {
            $fromSurah = MoshafSurah::where('id', $plan->start_from_surat)->value('eng_name');
            $toSurah   = MoshafSurah::where('id', $plan->to_surat)->value('eng_name');
            if ($fromSurah && $toSurah) {
                $planContent .= "<div>{$fromSurah}:{$plan->start_from_ayat} - {$toSurah}:{$plan->to_ayat}</div>";
            }
        }
        return $planContent ?: '—';
    }

    private function getEntryData(int $studentId, int $classId, int $month, int $year): array
    {
        $reports = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereNotNull('hefz_from_surat')
            ->whereNotNull('hefz_from_ayat')
            ->whereNotNull('hefz_to_surat')
            ->whereNotNull('hefz_to_ayat')
            ->orderBy('created_at', 'asc')
            ->get();

        if ($reports->isEmpty()) {
            return ['entry1' => '—','entry2' => '—','entry3' => '—','entry4' => '—'];
        }
        $minFromSurat = $reports->min('hefz_from_surat');
        $minFromAyat  = $reports->where('hefz_from_surat', $minFromSurat)->min('hefz_from_ayat');
        $minFromReport= $reports->where('hefz_from_surat', $minFromSurat)->where('hefz_from_ayat', $minFromAyat)->first();
        $maxToSurat   = $reports->max('hefz_to_surat');
        $maxToAyat    = $reports->where('hefz_to_surat', $maxToSurat)->max('hefz_to_ayat');
        $maxToReport  = $reports->where('hefz_to_surat', $maxToSurat)->where('hefz_to_ayat', $maxToAyat)->first();
        $dailyReportUrl = route('reports.index', ['id' => $classId]);

        return [
            'entry1' => $this->formatEntryWithTooltip($this->getSurahName($minFromSurat), $minFromReport->created_at->format('Y-m-d'), $dailyReportUrl, 'From Surah (Start of Month)'),
            'entry2' => $this->formatEntryWithTooltip($minFromAyat, $minFromReport->created_at->format('Y-m-d'), $dailyReportUrl, 'From Ayat (Start of Month)'),
            'entry3' => $this->formatEntryWithTooltip($this->getSurahName($maxToSurat), $maxToReport->created_at->format('Y-m-d'), $dailyReportUrl, 'To Surah (End of Month)'),
            'entry4' => $this->formatEntryWithTooltip($maxToAyat, $maxToReport->created_at->format('Y-m-d'), $dailyReportUrl, 'To Ayat (End of Month)'),
        ];
    }

    private function formatEntryWithTooltip($value, string $date, string $url, string $description): string
    {
        if ($value === null) { $value = '—'; }
        return sprintf(
            '<span class="entry-tooltip" data-entry-type="%s" data-entry-date="%s" data-daily-report-url="%s">%s</span>',
            htmlspecialchars($description), $date, $url, $value
        );
    }

    private function getSurahName(?int $surahId): string
    {
        if (!$surahId) return '—';
        $surah = MoshafSurah::where('id', $surahId)->first();
        if (!$surah) return '—';
        return "{$surah->id}. {$surah->eng_name}";
    }

    private function getMonthlyAchievement(int $studentId, int $classId, int $month, int $year): string
    {
        $reports = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereNotNull('pages_memorized')
            ->where('pages_memorized', '>', 0)
            ->get();
        if ($reports->isEmpty()) return '—';
        $totalPages = $reports->sum('pages_memorized');
        $first = $reports->sortBy('created_at')->first();
        $last  = $reports->sortBy('created_at')->last();
        $content = "<div style='font-weight:bold;color:#009933;'>{$totalPages} pages</div>";
        if ($first->created_at->format('Y-m-d') !== $last->created_at->format('Y-m-d')) {
            $content .= "<div class='text-muted' style='font-size:11px;'>".
                        $first->created_at->format('M j')." - ".$last->created_at->format('M j')."</div>";
        } else {
            $content .= "<div class='text-muted' style='font-size:11px;'>".$first->created_at->format('M j, Y')."</div>";
        }
        $count = $reports->count();
        if ($count>1) $content .= "<div class='text-muted' style='font-size:10px;'>({$count} reports)</div>";
        return $content;
    }

    private function calculateAttendance(int $studentId, int $classId, int $month, int $year): array
    {
        $class = Classes::find($classId);
        if (!$class || !$class->timetable) return ['percentage'=>0.0,'details'=>[]];
        $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
        if ($totalClasses <= 0) return ['percentage'=>0.0,'details'=>[]];
        $attended = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereIn('attendance_id', [1,2])->count();
        $absent = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->where('attendance_id', 3)->count();
        $pct = min(100.0, ($attended/$totalClasses)*100);
        return ['percentage'=>$pct,'details'=>[
            'total_classes'=>$totalClasses,'attended'=>$attended,'absent'=>$absent,'percentage'=>number_format($pct,1)
        ]];
    }

    private function calculateAchievement(Student $student, int $classId, int $month, int $year): float
    {
        $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
            ->where(function ($query) use ($month, $year, $classId) {
                $query->where(function ($q) use ($year, $month, $classId) {
                    $q->whereYear('created_at', $year)->whereMonth('created_at', $month)->where('class_id', $classId)->where('status', 'active');
                })->orWhere(function ($q2) use ($year, $month, $classId) {
                    $q2->whereYear('start_date', $year)->whereMonth('start_date', $month)->where('class_id', $classId)->where('status', 'active');
                });
            })
            ->first();
        if (!$plan) return 0.0;
        $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get();
        if ($reports->isEmpty()) return 0.0;
        $student->loadMissing('studentProgramLevels.programlevel');
        $level = $this->detectStudentLevel($student);
        if ($level === 'level1') {
            $data = $this->calculateLevel1Completion($plan, $reports);
            return $data['completion_rate'];
        }
        $data = $this->calculateLevel2Completion($plan, $reports);
        return $data['completion_rate'];
    }

    private function formatStudentName(Student $student): string
    { return "<a href='" . route('students.show', $student->id) . "' class='studentProfileLink' target='_blank'>{$student->full_name}</a>"; }

    private function formatProgressBar(float $percentage, string $color): string
    { return "<div class='ui progress' data-percent='{$percentage}' style='margin:0'><div class='bar' style='background-color: {$color}; width: {$percentage}%;'></div><div class='label'>".number_format($percentage,1)."%</div></div>"; }

    private function formatProgressBarWithPopup(float $percentage, string $color, array $details): string
    {
        $detailsJson = htmlspecialchars(json_encode($details), ENT_QUOTES, 'UTF-8');
        $safe = number_format($percentage, 1);
        return "<div class='ui progress attendance-progress' data-percent='{$safe}' data-details='{$detailsJson}' style='margin:0;cursor:pointer;position:relative;background:#f0f0f0;border-radius:4px;height:20px' onclick='showAttendanceDetails(this)'><div class='bar' style='background-color: {$color}; width: {$safe}%; height: 100%; border-radius: 4px; transition: width 0.3s ease;'></div><div class='label' style='position:absolute;inset:0;display:flex;align-items:center;justify-content:center;color:#333;font-weight:bold;font-size:11px;z-index:2'>{$safe}%</div></div>";
    }

    private function formatAchievementProgressBarWithPopup(float $percentage, string $color, array $details): string
    {
        $detailsJson = htmlspecialchars(json_encode($details), ENT_QUOTES, 'UTF-8');
        $safe = number_format($percentage, 1);
        return "<div class='ui progress achievement-progress' data-percent='{$safe}' data-details='{$detailsJson}' style='margin:0;cursor:pointer;position:relative;background:#f0f0f0;border-radius:4px;height:20px' onclick='showAchievementDetails(this)'><div class='bar' style='background-color: {$color}; width: {$safe}%; height: 100%; border-radius: 4px; transition: width 0.3s ease;'></div><div class='label' style='position:absolute;inset:0;display:flex;align-items:center;justify-content:center;color:#333;font-weight:bold;font-size:11px;z-index:2'>{$safe}%</div></div>";
    }

    /**
     * Build structured achievement details for popup.
     * Mirrors logic from single-class controller, adapting for aggregated flow.
     */
    private function getAchievementDetails(Student $student, int $classId, int $month, int $year): array
    {
        $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
            ->where(function ($query) use ($month, $year, $classId) {
                $query->where(function ($q) use ($year, $month, $classId) {
                    $q->whereYear('created_at', $year)
                        ->whereMonth('created_at', $month)
                        ->where('class_id', $classId)
                        ->where('status', 'active');
                })->orWhere(function ($q2) use ($year, $month, $classId) {
                    $q2->whereYear('start_date', $year)
                        ->whereMonth('start_date', $month)
                        ->where('class_id', $classId)
                        ->where('status', 'active');
                });
            })
            ->first();

        $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
            ->where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get();

        if (!$plan) {
            return [
                'planned_pages' => 0,
                'achieved_pages' => 0,
                'total_reports' => $reports->count(),
                'reports_with_pages' => $reports->where('pages_memorized', '>', 0)->count(),
                'achievement_percentage' => '0.0'
            ];
        }

        $studentLevel = $this->detectStudentLevel($student);
        if ($studentLevel === 'level1') {
            $completionData = $this->calculateLevel1Completion($plan, $reports);
            return [
                'achievement_percentage' => number_format($completionData['completion_rate'], 1),
                'tooltip' => $completionData['tooltip'] ?? 'Level 1 completion calculation',
                'details' => $completionData,
                'type' => 'level1'
            ];
        }

        $completionData = $this->calculateLevel2Completion($plan, $reports);
        return [
            'planned_pages' => $completionData['planned_pages'],
            'achieved_pages' => $completionData['achieved_pages'],
            'total_reports' => $reports->count(),
            'reports_with_pages' => $reports->where('pages_memorized', '>', 0)->count(),
            'achievement_percentage' => number_format($completionData['completion_rate'], 1),
            'type' => 'level2'
        ];
    }

    private function detectStudentLevel($studentDetails): ?string
    {
        if (method_exists($studentDetails, 'loadMissing')) {
            $studentDetails->loadMissing('studentProgramLevels.programlevel');
        }
        foreach ($studentDetails->studentProgramLevels as $spl) {
            if ($spl->programlevel) {
                $levelName = strtolower($spl->programlevel->title);
                if (str_contains($levelName, 'level 1')) return 'level1';
                if (str_contains($levelName, 'level 2')) return 'level2';
            }
        }
        return null;
    }

    private function calculateLevel1Completion($plan, $reports): array
    {
        $components = ['talqeen','revision','jazariyah','seminars'];
        $componentDetails = []; $total = 0; $valid = 0;
        foreach ($components as $name) {
            $fromField = "{$name}_from_lesson"; $toField = "{$name}_to_lesson";
            if (!empty($plan->$fromField) && !empty($plan->$toField) && $plan->$fromField <= $plan->$toField) {
                $valid++; $plannedRangeCount = $plan->$toField - $plan->$fromField + 1;
                $completed = $this->getUniqueCompletedLessonsForComponent($reports, $name);
                $plannedRange = range($plan->$fromField, $plan->$toField);
                $achievedInRange = count(array_intersect($completed, $plannedRange));
                $progress = ($plannedRangeCount>0) ? ($achievedInRange/$plannedRangeCount)*100 : 0;
                $total += $progress; $componentDetails[$name] = round($progress,2);
            } else { $componentDetails[$name] = 0; }
        }
        $overall = $valid>0 ? ($total/$valid) : 0;
        $tooltip = $reports->count()===0 ? 'No progress reports available' : ($valid===0 ? 'No valid lesson plans found' : null);
        return ['completion_rate'=>round($overall,2),'type'=>'level1','components'=>$componentDetails,'valid_components'=>$valid,'tooltip'=>$tooltip];
    }

    private function getUniqueCompletedLessonsForComponent($reports, string $componentName): array
    {
        $out = []; $fromField = "{$componentName}_from_lesson"; $toField = "{$componentName}_to_lesson";
        foreach ($reports as $r) {
            if (!empty($r->$fromField) && !empty($r->$toField) && $r->$fromField <= $r->$toField) {
                $out = array_merge($out, range($r->$fromField, $r->$toField));
            }
        }
        return array_unique($out);
    }

    private function calculateLevel2Completion($plan, $reports): array
    {
        $plannedPages = 0; $tooltip = null;
        if (!empty($plan->start_from_surat) && !empty($plan->start_from_ayat) && !empty($plan->to_surat) && !empty($plan->to_ayat)) {
            try {
                if ($plan->study_direction == 'backward') {
                    $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                        $plan->start_from_surat, $plan->start_from_ayat, $plan->to_surat, $plan->to_ayat
                    ]);
                    $plannedPages = $numberofPages[0]->numberofPagesSum ?? 0;
                } else {
                    DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                        $plan->start_from_surat, $plan->start_from_ayat, $plan->to_surat, $plan->to_ayat
                    ]);
                    $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                    $plannedPages = $results[0]->number_of_pages_sum ?? 0;
                }
            } catch (\Exception $e) {
                \Log::error('Error calculating planned pages for Level 2 student: ' . $e->getMessage());
                $plannedPages = 0; $tooltip = 'Error calculating planned pages';
            }
        } else { $tooltip = 'No valid hefz plan found'; }

        $achievedPages = $reports->sum('pages_memorized') ?? 0;
        if ($plannedPages > 0 && $achievedPages > $plannedPages) { $achievedPages = $plannedPages; }
        $percentage = $plannedPages > 0 ? round(($achievedPages / $plannedPages) * 100) : 0;
        if ($reports->count() === 0) { $tooltip = 'No progress reports available'; }
        return ['completion_rate'=>$percentage,'type'=>'level2','planned_pages'=>$plannedPages,'achieved_pages'=>$achievedPages,'tooltip'=>$tooltip];
    }
}



<?php

namespace Modules\Site\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Slider;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Session;
use Image;
use Illuminate\Support\Facades\File;

class SliderController extends Controller
{

    private $slider_setting;

    public function __construct(){

        $this->middleware('permission:view slider|create slider|edit slider|delete slider', ['only' => ['index','show']]);
        $this->middleware('permission:create slider', ['only' => ['create','store']]);
        $this->middleware('permission:edit slider', ['only' => ['edit','update', 'sort']]);
        $this->middleware('permission:delete slider', ['only' => ['destroy']]);

        // get slider settings

        $theme = config('website_theme' , 'wajeha') ?? 'wajeha';
        
        $theme_settings = require(module_path('Site').'/Resources/views/templates/'.$theme.'/config.php');

        $this->slider_setting = $theme_settings['sliders'][config('settings.theme_slider' , 'elastic_slider')];

        // need more slider status checking . if none & if no value

    }
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {

        $sliders = Slider::orderBy('slide_order')->paginate(25);

        if(isset($edit_mode)){
            return view(theme_path('slider.index'));

        }else{
            return view('site::slider.index', compact('sliders'));
        }
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        $languages = config('app.locales');

        $slider_setting = $this->slider_setting;

        if(isset($edit_mode)){
            return view(theme_path('slider_page'));
        }

        return view('site::slider.create' , compact('languages' , 'slider_setting'));
    }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $this->validateSlider($request);

        $requestData = $request->all();
        
        $slider =  new Slider;

        $slider->status = $request->status;
        $slider->link = $request->link;
        $slider->image = $request->image;
        $slider->organization_id  = config('organization_id');
        
        $slider->save();
        
        if(isset($request->translate)){
            foreach ($request->translate as $code => $translate) {
                if($translate['title'] || $translate['description'])
                {
                    $slider->translateOrNew($code)->title = $translate['title'] ?? '';
                    $slider->translateOrNew($code)->description = $translate['description'] ?? '';
                }
            }
        }

        $slider->save();

        Session::flash('flash_message', 'Slider added!');

        $dir_path = explode('/' , $slider->image);
        array_pop($dir_path);
        $dir_path = implode('/', $dir_path);

        $img = Image::make(public_path().$slider->image)->fit(150, 100);
        
        File::makeDirectory(public_path('/images/150x100/'.$dir_path), 0755 ,true ,true);//
        
        $img->save(public_path('images/150x100/'.$slider->image));

        return redirect(route('slider.index'));

    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show()
    {
        return view(theme_path('slider.show'));
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        $languages = config('app.locales');

        $slider = Slider::findOrFail($id);

        $slider_setting = $this->slider_setting;

        if(isset($edit_mode)){
            return view(theme_path('slider_page'));
        }

        return view('site::slider.edit' , compact('languages' ,'slider' , 'slider_setting'));
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(Request $request , $id)
    {


        try {



            $requestData = $request->all();
        $this->validateSlider($request);
        
        $slider = Slider::findOrFail($id);

        $slider->status = $request->status;
        $slider->link = $request->link;
        $slider->image = $request->image;


        $slider->organization_id  = config('organization_id');
        $slider->save();
        
        if(isset($request->translate)){
            foreach ($request->translate as $code => $translate) {
                if($translate['title'] || $translate['description'])
                {
                    $slider->translateOrNew($code)->title = $translate['title'] ?? '';
                    $slider->translateOrNew($code)->description = $translate['description'] ?? '';   
                }
            }
        }

        $slider->save();

        Session::flash('flash_message', 'Slider updated!');

        $dir_path = explode('/' , $slider->image);
            $directoryName = Arr::except($dir_path, [0,1,2]);
        array_pop($dir_path);
//        $dir_path = implode('/', $dir_path);
        $dir_path = implode('/', $directoryName);
        $filename = basename($request->image);
//            $fullPath =  public_path($directoryName.'/'.basename($request->image));
            $fullPath =  public_path($dir_path);
            File::makeDirectory(public_path('photos/1/slider/'), 0755 ,true ,true);//


//            dd($fullPath,'------',$dir_path,'---------',$slider->image);







//            take image from one directory, edit it, and save it in another directory
//        $img = Image::make(public_path().$request->image)->fit(150, 100);
            $img = Image::make($dir_path)->fit(150, 100)->save('photos/1/slider/'.$filename);
            File::makeDirectory(public_path('/photos/150x100/'.$dir_path), 0755 ,true ,true);//


//        $img->save(public_path('images/150x100/'.$slider->image));

        return redirect(route('slider.index'));
        } catch (\Exception $e) {

            dd($e);
            \Log::error($e);
            Log::info($e->getMessage());
        }

    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
        Slider::destroy(request()->slider);

        Session::flash('flash_message', 'Slider deleted!');

        return redirect(route('slider.index'));

    }

    public function sort(){
        $request = request('slides');


        foreach ($request as $order => $id) {
            $slide = Slider::findOrFail($id);
            $slide->slide_order = $order;
            $slide->save();
        }

        return response()->json([
            'status' => 'success'
        ]);
    }

    private function validateSlider(Request $request){
//        $rules = [];
//        $rules['image'] = 'required';
//        if(in_array('title' , $this->slider_setting)){
//            $rules['translate.*.title'] = 'required';
//
//        }
//        if(in_array('description' , $this->slider_setting)){
//            $rules['translate.*.description'] = 'required';
//
//        }
//        $this->validate($request, $rules);


        return true;
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="">
  <meta name="author" content="">
  <title>{{ trans('platform::platform.title') }}</title>
  <link href="{{ url("assets/platform/css/bootstrap.min.css")}}" rel="stylesheet">
  <link href="{{ url("assets/platform/css/animate.min.css")}}" rel="stylesheet"> 
  <link href="{{ url("assets/platform/css/font-awesome.min.css")}}" rel="stylesheet">
  <link href="{{ url("assets/platform/css/lightbox.css")}}" rel="stylesheet">
  <link href="{{ url("assets/platform/css/main.css")}}" rel="stylesheet">
  <link id="css-preset" href="{{ url("assets/platform/css/presets/preset3.css")}}" rel="stylesheet">
  <link href="{{ url("assets/platform/css/responsive.css")}}" rel="stylesheet">

  @if(trans('platform::platform.lang_direction') == 'rtl')
  	<!-- Load Bootstrap RTL theme from RawGit -->
  	<link rel="stylesheet" href="//cdn.rawgit.com/morteza/bootstrap-rtl/v3.3.4/dist/css/bootstrap-rtl.min.css">
  @endif
  <!--[if lt IE 9]>
    <script src="{{ url("assets/platform/js/html5shiv.js")}}"></script>
    <script src="{{ url("assets/platform/js/respond.min.js")}}"></script>
  <![endif]-->
  
  <link href='https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700' rel='stylesheet' type='text/css'>
  <link rel="shortcut icon" href="assets/platform/images/favicon.ico">
  @yield('css')
</head><!--/head-->
<body>
  <!--.preloader-->
  {{-- <div class="preloader"> <i class="fa fa-circle-o-notch fa-spin"></i></div> --}}
  <!--/.preloader-->

  <header id="home">
    @yield('slider')
    <div class="main-nav">
      <div class="container">
        <div class="navbar-header">
          <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <a class="navbar-brand" href="/">
            <h1><img class="img-responsive" src="{{ asset('assets/platform/images/logo.png')}}" alt="logo"></h1>
          </a>                    
        </div>
        <div class="collapse navbar-collapse">
          <ul class="nav navbar-nav navbar-right               
            @if(trans('platform::platform.lang_direction') == 'rtl')
            pull-left
            @endif
            ">  
            <li class="scroll active"><a href="/">{{ trans('platform::platform.home') }}</a></li>
            <li class="scroll"><a href="/#services">{{ trans('platform::platform.get_started') }}</a></li> 
            {{-- <li class="scroll"><a href="/#about-us">{{ trans('platform::platform.about_us') }}</a></li>                      --}}
            <li class="scroll"><a href="/#contact">{{ trans('platform::platform.contact') }}</a></li>

          <!-- Right Side Of Navbar -->
            <ul class="nav navbar-nav ">
                <!-- Authentication Links -->
                @if (Auth::guest())
                    <li><a href="{{ url(config('app.locale').'/login') }}"id="login_btn">{{ trans('platform::platform.login') }}</a></li>
                    <li><a href="{{ url(config('app.locale').'/register') }}">{{ trans('platform::platform.register') }}</a></li>
                @else
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false">
                            {{ Auth::user()->name }} <span class="caret"></span>
                        </a>

                        <ul class="dropdown-menu" role="menu">
                            <li>
                                <a href="{{ url(config('app.locale').'/logout') }}"
                                    onclick="event.preventDefault();
                                             document.getElementById('logout-form').submit();">
                                    {{ trans('platform::platform.logout') }}
                                </a>

                                <form id="logout-form" action="{{ url(config('app.locale').'/logout') }}" method="POST" style="display: none;">
                                    {{ csrf_field() }}
                                </form>
                            </li>
                        </ul>
                    </li>
                @endif
              </ul>
              <ul class="nav navbar-nav">
                <a href="#">{{ trans('platform::platform.language') }} [{{ ucfirst(config('app.locale')) }}]</a>  
                @foreach (config('app.locales') as $lang)
                  @if ($lang !== config('app.locale'))
                    <li><a href="{{ change_language($lang) }}">{{ $lang }}</a>  </li>
                  @endif
                @endforeach
              </ul>
            </ul>
            </div>
      </div>
    </div><!--/#main-nav-->
  </header><!--/#home-->
  @yield('content')
  <footer id="footer">
    <div class="footer-top wow fadeInUp" data-wow-duration="1000ms" data-wow-delay="300ms">
      <div class="container text-center">
        <div class="footer-logo">
          <a href="/"><img class="img-responsive" src="{{ asset('assets/platform/images/logo.png')}}" alt=""></a>
        </div>
        <div class="social-icons">
          <ul>
            <li><a class="envelope" href="#"><i class="fa fa-envelope"></i></a></li>
            <li><a class="twitter" href="#"><i class="fa fa-twitter"></i></a></li> 
            <li><a class="dribbble" href="#"><i class="fa fa-dribbble"></i></a></li>
            <li><a class="facebook" href="#"><i class="fa fa-facebook"></i></a></li>
            <li><a class="linkedin" href="#"><i class="fa fa-linkedin"></i></a></li>
            <li><a class="tumblr" href="#"><i class="fa fa-tumblr-square"></i></a></li>
          </ul>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <div class="container">
        <div class="row">
          <div class="col-sm-6">
            <p>&copy; {{ date('Y') }} {{ trans('platform::platform.title') }}</p>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <script type="text/javascript" src="{{ url("assets/platform/js/jquery.js")}}"></script>
  <script type="text/javascript" src="{{ url("assets/platform/js/bootstrap.min.js")}}"></script>
  <script type="text/javascript" src="https://maps.google.com/maps/api/js?sensor=true"></script>
  <script type="text/javascript" src="{{ url("assets/platform/js/jquery.inview.min.js")}}"></script>
  <script type="text/javascript" src="{{ url("assets/platform/js/wow.min.js")}}"></script>
  <script type="text/javascript" src="{{ url("assets/platform/js/mousescroll.js")}}"></script>
  <script type="text/javascript" src="{{ url("assets/platform/js/smoothscroll.js")}}"></script>
  <script type="text/javascript" src="{{ url("assets/platform/js/jquery.countTo.js")}}"></script>
  <script type="text/javascript" src="{{ url("assets/platform/js/lightbox.min.js")}}"></script>
  <script type="text/javascript" src="{{ url("assets/platform/js/main.js")}}"></script>
  @yield('js')
</body>


</html>
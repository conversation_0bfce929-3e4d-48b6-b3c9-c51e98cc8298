<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use <PERSON><PERSON><PERSON>\JobSeeker\Entities\JobSyncAlertRule;
use <PERSON><PERSON>les\JobSeeker\Entities\JobSyncAlertEvent;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\CommandScheduleExecution;
use Illuminate\Support\Facades\Log;
use App\Services\EmailService;
use Carbon\Carbon;

/**
 * JobSyncAlertService - Proactive monitoring and alerting for job sync operations
 * 
 * This service handles:
 * - Alert rule evaluation against command execution data
 * - Alert event creation and management
 * - Notification delivery for critical issues
 * - Alert pattern analysis and recommendations
 */
final class JobSyncAlertService
{
    /**
     * Centralized email service for sending notifications
     */
    private EmailService $emailService;

    /**
     * Constructor - Inject centralized email service
     */
    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }
    /**
     * Evaluate all active alert rules against a command execution
     * 
     * @param CommandScheduleExecution $execution
     * @return array List of triggered alerts
     */
    public function evaluateAlertsForExecution(CommandScheduleExecution $execution): array
    {
        try {
                    Log::info('JobSyncAlertService: Evaluating alerts for execution', [
            'execution_id' => $execution->id,
            'command' => $execution->scheduleRule->command ?? 'unknown',
            'jobs_fetched' => $execution->jobs_fetched,
            'error_type' => $execution->error_type
        ]);

            $triggeredAlerts = [];
            $activeRules = JobSyncAlertRule::where('enabled', true)->get();

            foreach ($activeRules as $rule) {
                if ($this->shouldEvaluateRule($rule, $execution)) {
                    $alertData = $this->evaluateRule($rule, $execution);
                    
                    if ($alertData['triggered']) {
                        $alert = $this->createAlertEvent($rule, $execution, $alertData);
                        $triggeredAlerts[] = $alert;
                        
                        Log::info('JobSyncAlertService: Alert triggered', [
                            'rule_id' => $rule->id,
                            'rule_type' => $rule->alert_type,
                            'execution_id' => $execution->id,
                            'alert_id' => $alert->id
                        ]);
                    }
                }
            }

            if (!empty($triggeredAlerts)) {
                $this->processTriggeredAlerts($triggeredAlerts);
            }

            return $triggeredAlerts;

        } catch (\Exception $e) {
            Log::error('JobSyncAlertService: Error evaluating alerts', [
                'execution_id' => $execution->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [];
        }
    }

    /**
     * Check if a rule should be evaluated for a given execution
     * 
     * @param JobSyncAlertRule $rule
     * @param CommandScheduleExecution $execution
     * @return bool
     */
    private function shouldEvaluateRule(JobSyncAlertRule $rule, CommandScheduleExecution $execution): bool
    {
        // Check command filter - handle both scheduled and manual executions
        $command = $execution->scheduleRule ? $execution->scheduleRule->command : ($execution->command ?? '');
        if ($rule->command && !str_contains($command, $rule->command)) {
            return false;
        }

        // Check cooldown period (using window_minutes as cooldown)
        if ($rule->window_minutes > 0) {
            $lastAlert = JobSyncAlertEvent::where('rule_id', $rule->id)
                ->where('created_at', '>=', Carbon::now()->subMinutes($rule->window_minutes))
                ->first();
            
            if ($lastAlert) {
                Log::debug('JobSyncAlertService: Rule in cooldown period', [
                    'rule_id' => $rule->id,
                    'last_alert' => $lastAlert->created_at,
                    'window_minutes' => $rule->window_minutes
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate a specific alert rule against an execution
     * 
     * @param JobSyncAlertRule $rule
     * @param CommandScheduleExecution $execution
     * @return array Alert evaluation result
     */
    private function evaluateRule(JobSyncAlertRule $rule, CommandScheduleExecution $execution): array
    {
        switch ($rule->alert_type) {
            case 'zero_jobs':
                return $this->evaluateZeroJobsRule($rule, $execution);
                
            case 'duration_spike':
                return $this->evaluateDurationSpikeRule($rule, $execution);
                
            case 'error_pattern':
                return $this->evaluateErrorPatternRule($rule, $execution);
                
            case 'category_drop':
                return $this->evaluateCategoryDropRule($rule, $execution);
                
            default:
                Log::warning('JobSyncAlertService: Unknown alert type', [
                    'rule_id' => $rule->id,
                    'alert_type' => $rule->alert_type
                ]);
                return ['triggered' => false, 'details' => [], 'severity' => 'low'];
        }
    }

    /**
     * Evaluate zero jobs alert rule
     * 
     * @param JobSyncAlertRule $rule
     * @param CommandScheduleExecution $execution
     * @return array
     */
    private function evaluateZeroJobsRule(JobSyncAlertRule $rule, CommandScheduleExecution $execution): array
    {
        $jobsFetched = $execution->jobs_fetched ?? 0;
        $threshold = $rule->threshold_value ?? 0;
        
        if ($jobsFetched <= $threshold) {
            // Check if this is a consecutive occurrence
            $consecutiveCount = $this->getConsecutiveZeroJobsCount($execution);
            $requiredConsecutive = $rule->configuration['consecutive_count'] ?? 1;
            
            if ($consecutiveCount >= $requiredConsecutive) {
                return [
                    'triggered' => true,
                    'details' => [
                        'jobs_fetched' => $jobsFetched,
                        'threshold' => $threshold,
                        'consecutive_count' => $consecutiveCount,
                        'required_consecutive' => $requiredConsecutive
                    ],
                    'severity' => $consecutiveCount >= 3 ? 'high' : 'medium'
                ];
            }
        }

        return ['triggered' => false, 'details' => [], 'severity' => 'low'];
    }

    /**
     * Evaluate duration spike alert rule
     * 
     * @param JobSyncAlertRule $rule
     * @param CommandScheduleExecution $execution
     * @return array
     */
    private function evaluateDurationSpikeRule(JobSyncAlertRule $rule, CommandScheduleExecution $execution): array
    {
        $duration = $execution->duration_seconds ?? 0;
        $threshold = $rule->threshold_value ?? 300; // Default 5 minutes
        
        if ($duration > $threshold) {
            // Calculate average duration for comparison
            $command = $execution->scheduleRule ? $execution->scheduleRule->command : ($execution->command ?? '');
            $avgDuration = $this->getAverageDuration($command, 7);
            $spikeMultiplier = $avgDuration > 0 ? $duration / $avgDuration : 1;
            
            return [
                'triggered' => true,
                'details' => [
                    'current_duration' => $duration,
                    'threshold' => $threshold,
                    'average_duration' => $avgDuration,
                    'spike_multiplier' => round($spikeMultiplier, 2)
                ],
                'severity' => $spikeMultiplier >= 3 ? 'high' : 'medium'
            ];
        }

        return ['triggered' => false, 'details' => [], 'severity' => 'low'];
    }

    /**
     * Evaluate error pattern alert rule
     * 
     * @param JobSyncAlertRule $rule
     * @param CommandScheduleExecution $execution
     * @return array
     */
    private function evaluateErrorPatternRule(JobSyncAlertRule $rule, CommandScheduleExecution $execution): array
    {
        // Check for error patterns in recent executions
        $errorPattern = $this->analyzeErrorPattern($execution);
        $thresholdConfig = $rule->threshold ?? [];
        
        // Support both error count and error rate thresholds
        $errorCountThreshold = $thresholdConfig['error_count'] ?? 3;
        $errorRateThreshold = $thresholdConfig['error_rate'] ?? null; // Percentage (e.g., 70 for 70%)
        
        $triggered = false;
        $severity = 'low';
        $triggerReason = '';
        
        // Check error count threshold
        if ($errorPattern['recent_error_count'] >= $errorCountThreshold) {
            $triggered = true;
            $severity = $errorPattern['recent_error_count'] >= 5 ? 'high' : 'medium';
            $triggerReason = 'error_count';
        }
        
        // Check error rate threshold (higher priority)
        if ($errorRateThreshold !== null && $errorPattern['error_rate'] >= $errorRateThreshold) {
            $triggered = true;
            $severity = $errorPattern['error_rate'] >= 70 ? 'high' : 'medium';
            $triggerReason = 'error_rate';
        }
        
        if ($triggered) {
            return [
                'triggered' => true,
                'details' => [
                    'current_error' => $execution->error_type,
                    'recent_error_count' => $errorPattern['recent_error_count'],
                    'total_executions' => $errorPattern['total_executions'],
                    'error_rate' => $errorPattern['error_rate'],
                    'error_count_threshold' => $errorCountThreshold,
                    'error_rate_threshold' => $errorRateThreshold,
                    'trigger_reason' => $triggerReason,
                    'error_types' => $errorPattern['error_types'],
                    'time_span' => $errorPattern['time_span_hours']
                ],
                'severity' => $severity
            ];
        }

        return ['triggered' => false, 'details' => [], 'severity' => 'low'];
    }

    /**
     * Evaluate category drop alert rule
     * 
     * @param JobSyncAlertRule $rule
     * @param CommandScheduleExecution $execution
     * @return array
     */
    private function evaluateCategoryDropRule(JobSyncAlertRule $rule, CommandScheduleExecution $execution): array
    {
        $currentCategories = $this->extractCategories($execution->jobs_by_category);
        $threshold = $rule->threshold_value ?? 50; // Default 50% drop
        
        if (empty($currentCategories)) {
            return ['triggered' => false, 'details' => [], 'severity' => 'low'];
        }

        // Compare with historical average
        $command = $execution->scheduleRule ? $execution->scheduleRule->command : ($execution->command ?? '');
        $historicalAvg = $this->getHistoricalCategoryAverage($command, 7);
        $dropPercentage = $this->calculateCategoryDrop($currentCategories, $historicalAvg);
        
        if ($dropPercentage >= $threshold) {
            return [
                'triggered' => true,
                'details' => [
                    'current_categories' => count($currentCategories),
                    'historical_average' => $historicalAvg['category_count'],
                    'drop_percentage' => round($dropPercentage, 2),
                    'threshold' => $threshold,
                    'missing_categories' => $this->findMissingCategories($currentCategories, $historicalAvg['categories'])
                ],
                'severity' => $dropPercentage >= 75 ? 'high' : 'medium'
            ];
        }

        return ['triggered' => false, 'details' => [], 'severity' => 'low'];
    }

    /**
     * Create an alert event
     * 
     * @param JobSyncAlertRule $rule
     * @param CommandScheduleExecution $execution
     * @param array $alertData
     * @return JobSyncAlertEvent
     */
    private function createAlertEvent(JobSyncAlertRule $rule, CommandScheduleExecution $execution, array $alertData): JobSyncAlertEvent
    {
        $alertMessage = $this->generateAlertMessage($rule, $alertData);
        
        return JobSyncAlertEvent::create([
            'rule_id' => $rule->id,
            'execution_id' => $execution->id,
            'alert_type' => $rule->alert_type,
            'severity' => $alertData['severity'],
            'alert_message' => $alertMessage,
            'alert_data' => $alertData['details'],
            'status' => 'new'
        ]);
    }

    /**
     * Generate human-readable alert message
     * 
     * @param JobSyncAlertRule $rule
     * @param array $alertData
     * @return string
     */
    private function generateAlertMessage(JobSyncAlertRule $rule, array $alertData): string
    {
        $severity = strtoupper($alertData['severity']);
        $details = $alertData['details'];
        
        switch ($rule->alert_type) {
            case 'zero_jobs':
                return "[{$severity}] Zero jobs fetched for {$details['consecutive_count']} consecutive executions (threshold: {$details['threshold']})";
                
            case 'duration_spike':
                return "[{$severity}] Execution duration spike: {$details['current_duration']}s (avg: {$details['average_duration']}s, {$details['spike_multiplier']}x normal)";
                
            case 'error_pattern':
                $errorTypes = implode(', ', array_keys($details['error_types']));
                $triggerReason = $details['trigger_reason'] ?? 'error_count';
                
                if ($triggerReason === 'error_rate') {
                    return "[{$severity}] HIGH ERROR RATE DETECTED: {$details['error_rate']}% error rate ({$details['recent_error_count']}/{$details['total_executions']} executions failed in {$details['time_span']}h) - Types: {$errorTypes}";
                } else {
                    return "[{$severity}] Error pattern detected: {$details['recent_error_count']} errors in {$details['time_span']}h ({$errorTypes})";
                }
            case 'category_drop':
                return "[{$severity}] Category count dropped by {$details['drop_percentage']}% ({$details['current_categories']} vs avg {$details['historical_average']})";
                
            default:
                return "[{$severity}] Alert triggered for rule: {$rule->name}";
        }
    }

    /**
     * Process triggered alerts (send notifications, etc.)
     * 
     * @param array $alerts
     * @return void
     */
    private function processTriggeredAlerts(array $alerts): void
    {
        foreach ($alerts as $alert) {
            try {
                $this->sendAlertNotification($alert);
                
            } catch (\Exception $e) {
                Log::error('JobSyncAlertService: Error processing alert', [
                    'alert_id' => $alert->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Send alert notification
     * 
     * @param JobSyncAlertEvent $alert
     * @return void
     */
    private function sendAlertNotification(JobSyncAlertEvent $alert): void
    {
        $rule = $alert->rule;
        $recipients = $rule->recipients ?? [];
        
        if (!empty($recipients)) {
            $this->sendEmailNotification($alert, $recipients);
        }
        
        // Always log alerts
        Log::warning('JobSyncAlertService: Alert notification', [
            'alert_id' => $alert->id,
            'type' => $alert->alert_type,
            'severity' => $alert->severity,
            'message' => $alert->alert_message
        ]);
    }

    /**
     * Send email notification for alert
     * 
     * @param JobSyncAlertEvent $alert
     * @param array $recipients
     * @return void
     */
    private function sendEmailNotification(JobSyncAlertEvent $alert, array $recipients): void
    {
        try {
            // Prepare alert data for email template
            $alertData = $alert->alert_data ?? [];
            $rule = $alert->rule ?? JobSyncAlertRule::find($alert->rule_id);
            $execution = $alert->execution ?? CommandScheduleExecution::find($alert->execution_id);
            
            // Generate email subject with severity and command context
            $command = $execution ? ($execution->scheduleRule ? $execution->scheduleRule->command : ($execution->command ?? 'Unknown')) : 'Unknown';
            $subject = sprintf("🚨 Job Sync Alert: %s (%s) - %s", 
                ucwords(str_replace('_', ' ', $alert->alert_type)), 
                strtoupper($alertData['severity'] ?? 'medium'),
                $command
            );

            foreach ($recipients as $recipient) {
                // Use centralized EmailService with transactional outbox pattern
                $result = $this->emailService->send(
                    to: $recipient,
                    subject: $subject,
                    body: '', // Body will be generated from view
                    viewData: [
                        'alert' => $alert,
                        'rule' => $rule,
                        'execution' => $execution,
                        'alertData' => $alertData,
                        'command' => $command,
                        'severity' => $alertData['severity'] ?? 'medium',
                        'errorDetails' => $alertData['details'] ?? [],
                        'timestamp' => $alert->created_at->format('Y-m-d H:i:s T'),
                        'recipientEmail' => $recipient
                    ],
                    view: 'modules.jobseeker.emails.job_sync_alert'
                );

                Log::info('JobSyncAlertService: Email notification sent via centralized EmailService', [
                    'alert_id' => $alert->id,
                    'recipient' => $recipient,
                    'subject' => $subject,
                    'command' => $command,
                    'result_type' => is_array($result) ? 'async' : 'sync',
                    'result' => is_array($result) ? $result['status'] : ($result ? 'success' : 'failed')
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('JobSyncAlertService: Error sending email notification via centralized EmailService', [
                'alert_id' => $alert->id,
                'recipients_count' => count($recipients),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Helper methods for rule evaluation
     */

    private function getConsecutiveZeroJobsCount(CommandScheduleExecution $execution): int
    {
        // Get command from relationship or direct property for manual executions
        $command = $execution->scheduleRule ? $execution->scheduleRule->command : ($execution->command ?? '');
        
        if ($execution->scheduleRule) {
            $executions = CommandScheduleExecution::whereHas('scheduleRule', function ($q) use ($command) {
                $q->where('command', $command);
            })
            ->where('started_at', '<=', $execution->started_at)
            ->orderBy('started_at', 'desc')
            ->take(10)
            ->get();
        } else {
            // For manual executions, search by command field directly
            $executions = CommandScheduleExecution::where('command', $command)
                ->where('started_at', '<=', $execution->started_at)
                ->orderBy('started_at', 'desc')
                ->take(10)
                ->get();
        }

        $consecutiveCount = 0;
        foreach ($executions as $exec) {
            if (($exec->jobs_fetched ?? 0) <= 0) {
                $consecutiveCount++;
            } else {
                break;
            }
        }

        return $consecutiveCount;
    }

    private function getAverageDuration(string $command, int $days): float
    {
        // Search both scheduled and manual executions
        $scheduledAvg = CommandScheduleExecution::whereHas('scheduleRule', function ($q) use ($command) {
            $q->where('command', $command);
        })
        ->where('started_at', '>=', Carbon::now()->subDays($days))
        ->whereNotNull('duration_seconds')
        ->avg('duration_seconds');
        
        $manualAvg = CommandScheduleExecution::where('command', $command)
            ->whereNull('schedule_rule_id')
            ->where('started_at', '>=', Carbon::now()->subDays($days))
            ->whereNotNull('duration_seconds')
            ->avg('duration_seconds');

        // Return the higher average to be conservative
        return max($scheduledAvg ?? 0, $manualAvg ?? 0);
    }

    private function analyzeErrorPattern(CommandScheduleExecution $execution): array
    {
        // Get command from relationship or direct property for manual executions
        $command = $execution->scheduleRule ? $execution->scheduleRule->command : ($execution->command ?? '');
        $timeSpanHours = 24;
        
        // Get all recent executions (including successful ones) for error rate calculation
        // For manual executions, search by command directly; for scheduled, use relationship
        if ($execution->scheduleRule) {
            $allRecentExecutions = CommandScheduleExecution::whereHas('scheduleRule', function ($q) use ($command) {
                $q->where('command', $command);
            })
            ->where('started_at', '>=', Carbon::now()->subHours($timeSpanHours))
            ->where('status', 'completed') // Only consider completed executions
            ->get();
        } else {
            // For manual executions, search by command field directly
            $allRecentExecutions = CommandScheduleExecution::where('command', $command)
                ->where('started_at', '>=', Carbon::now()->subHours($timeSpanHours))
                ->where('status', 'completed') // Only consider completed executions
                ->get();
        }

        // Get only error executions
        $errorExecutions = $allRecentExecutions->where('error_type', '!=', 'NONE');

        $errorTypes = [];
        foreach ($errorExecutions as $exec) {
            $errorType = $exec->error_type ?? 'UNKNOWN';
            $errorTypes[$errorType] = ($errorTypes[$errorType] ?? 0) + 1;
        }

        // Calculate error rate
        $totalExecutions = $allRecentExecutions->count();
        $errorCount = $errorExecutions->count();
        $errorRate = $totalExecutions > 0 ? round(($errorCount / $totalExecutions) * 100, 2) : 0;

        return [
            'recent_error_count' => $errorCount,
            'total_executions' => $totalExecutions,
            'error_rate' => $errorRate,
            'error_types' => $errorTypes,
            'time_span_hours' => $timeSpanHours
        ];
    }

    private function getHistoricalCategoryAverage(string $command, int $days): array
    {
        // Search both scheduled and manual executions
        $scheduledExecutions = CommandScheduleExecution::whereHas('scheduleRule', function ($q) use ($command) {
            $q->where('command', $command);
        })
        ->where('started_at', '>=', Carbon::now()->subDays($days))
        ->whereNotNull('jobs_by_category')
        ->get();
        
        $manualExecutions = CommandScheduleExecution::where('command', $command)
            ->whereNull('schedule_rule_id')
            ->where('started_at', '>=', Carbon::now()->subDays($days))
            ->whereNotNull('jobs_by_category')
            ->get();
            
        $executions = $scheduledExecutions->merge($manualExecutions);

        $allCategories = [];
        foreach ($executions as $execution) {
            $categories = $this->extractCategories($execution->jobs_by_category);
            $allCategories = array_merge($allCategories, array_keys($categories));
        }

        $uniqueCategories = array_unique($allCategories);
        
        return [
            'category_count' => count($uniqueCategories),
            'categories' => $uniqueCategories
        ];
    }

    private function extractCategories($jobsByCategory): array
    {
        if (is_string($jobsByCategory)) {
            $categories = json_decode($jobsByCategory, true);
        } else {
            $categories = $jobsByCategory;
        }

        return is_array($categories) ? $categories : [];
    }

    private function calculateCategoryDrop(array $currentCategories, array $historicalAvg): float
    {
        $currentCount = count($currentCategories);
        $historicalCount = $historicalAvg['category_count'];
        
        if ($historicalCount <= 0) {
            return 0;
        }

        return (($historicalCount - $currentCount) / $historicalCount) * 100;
    }

    private function findMissingCategories(array $currentCategories, array $historicalCategories): array
    {
        $currentCategoryNames = array_keys($currentCategories);
        return array_diff($historicalCategories, $currentCategoryNames);
    }

    /**
     * Public methods for alert management
     */

    public function acknowledgeAlert(int $alertId, string $acknowledgedBy): bool
    {
        try {
            $alert = JobSyncAlertEvent::find($alertId);
            if (!$alert) {
                return false;
            }

            $alert->acknowledge($acknowledgedBy);
            
            Log::info('JobSyncAlertService: Alert acknowledged', [
                'alert_id' => $alertId,
                'acknowledged_by' => $acknowledgedBy
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('JobSyncAlertService: Error acknowledging alert', [
                'alert_id' => $alertId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function resolveAlert(int $alertId, string $resolvedBy, ?string $resolution = null): bool
    {
        try {
            $alert = JobSyncAlertEvent::find($alertId);
            if (!$alert) {
                return false;
            }

            $alert->resolve($resolvedBy, $resolution);
            
            Log::info('JobSyncAlertService: Alert resolved', [
                'alert_id' => $alertId,
                'resolved_by' => $resolvedBy,
                'resolution' => $resolution
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('JobSyncAlertService: Error resolving alert', [
                'alert_id' => $alertId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function getActiveAlerts(): \Illuminate\Database\Eloquent\Collection
    {
        return JobSyncAlertEvent::with(['rule', 'execution.commandSchedule'])
            ->whereIn('status', ['new', 'acknowledged'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getAlertSummary(int $days = 7): array
    {
        $startDate = Carbon::now()->subDays($days);
        
        $alerts = JobSyncAlertEvent::where('created_at', '>=', $startDate)->get();
        
        return [
            'total_alerts' => $alerts->count(),
            'by_severity' => $alerts->groupBy('severity')->map->count(),
            'by_type' => $alerts->groupBy('alert_type')->map->count(),
            'by_status' => $alerts->groupBy('status')->map->count(),
            'resolution_rate' => $alerts->count() > 0 ? 
                round(($alerts->where('status', 'resolved')->count() / $alerts->count()) * 100, 2) : 0
        ];
    }
} 
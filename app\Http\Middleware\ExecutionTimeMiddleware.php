<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class ExecutionTimeMiddleware
{
    public function handle($request, Closure $next)
    {



        // Get the start time
        $start = microtime(true);

        // Call the next middleware/controller action
        $response = $next($request);

        // Get the end time
        $end = microtime(true);

        // Calculate the execution time
        $time = round($end - $start, 3);
        $route = app('router')->currentRouteName();



        // Get the controller and action from the route
//        if ($route && ($controllerAction = $route->getAction('controller'))) {
//            list($controller, $action) = explode('@', class_basename($controllerAction));
//        } else {
//            // Set default values if controller and action cannot be determined
//            $controller = 'Unknown';
//            $action = 'Unknown';
//        }

        // Save the execution time to the database
        \DB::table('execution_times')->insert([
//            'controller' => class_basename($controller),
//            'action' => $action,
            'route' => $route,
            'url' => app('url')->current(),
            'time' =>$time,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return$response;
    }
}
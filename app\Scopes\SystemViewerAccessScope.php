<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;
use App\Center;

/**
 * SystemViewerAccessScope: Nuclear-level solution for system viewer data access
 * 
 * This global scope automatically expands data access for system viewers by:
 * 1. Intercepting whereIn queries that use empty arrays (authorization failures)
 * 2. Replacing them with proper organizational data sets
 * 3. Operating transparently without breaking existing code
 * 4. Applying to ALL models that use this scope
 */
class SystemViewerAccessScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        // Only apply for authenticated system viewers
        if (!$this->isSystemViewer()) {
            return;
        }

        // Store original whereIn method
        $this->extendBuilderForSystemViewer($builder);
    }

    /**
     * Extend the builder to handle system viewer access patterns
     */
    protected function extendBuilderForSystemViewer(Builder $builder): void
    {
        // Wrap the existing whereIn method to intercept authorization patterns
        $builder->macro('originalWhereIn', function ($column, $values, $boolean = 'and', $not = false) use ($builder) {
            // Call the original whereIn method
            return $builder->where(function ($query) use ($column, $values, $boolean, $not) {
                if ($this->shouldExpandForSystemViewer($column, $values)) {
                    $expandedValues = $this->getExpandedSystemViewerValues($column);
                    return $query->whereIn($column, $expandedValues, $boolean, $not);
                }
                return $query->whereIn($column, $values, $boolean, $not);
            });
        });
    }

    /**
     * Check if this query should be expanded for system viewers
     */
    protected function shouldExpandForSystemViewer($column, $values): bool
    {
        // Check for empty arrays (authorization failures)
        if (is_array($values) && empty($values)) {
            return true;
        }

        // Check for specific authorization columns
        $authColumns = [
            'center_id', 'centre_id', 'cen_id',
            'department_id', 'class_id', 'program_id',
            'employee_id', 'emp_id', 'user_id'
        ];

        return in_array($column, $authColumns);
    }

    /**
     * Get expanded values for system viewers based on column type
     */
    protected function getExpandedSystemViewerValues($column): array
    {
        $organizationId = config('organization_id');

        switch ($column) {
            case 'center_id':
            case 'centre_id':
            case 'cen_id':
                return Center::where('organization_id', $organizationId)
                    ->whereNull('deleted_at')
                    ->pluck('id')
                    ->toArray();

            case 'department_id':
                return \App\Department::where('organization_id', $organizationId)
                    ->pluck('id')
                    ->toArray();

            case 'class_id':
                return \App\Classes::where('organization_id', $organizationId)
                    ->whereNull('deleted_at')
                    ->pluck('id')
                    ->toArray();

            case 'program_id':
                return \App\Program::where('organization_id', $organizationId)
                    ->pluck('id')
                    ->toArray();

            case 'employee_id':
            case 'emp_id':
                return \App\Employee::where('organization_id', $organizationId)
                    ->whereNull('deleted_at')
                    ->pluck('id')
                    ->toArray();

            case 'user_id':
                return \App\User::where('organization_id', $organizationId)
                    ->whereNull('deleted_at')
                    ->pluck('id')
                    ->toArray();

            default:
                // For unknown columns, return organizational boundary
                return [$organizationId];
        }
    }

    /**
     * Check if the current user is a system viewer
     */
    protected function isSystemViewer(): bool
    {
        if (!Auth::guard('employee')->check()) {
            return false;
        }

        $user = Auth::guard('employee')->user();
        return $user->hasRole('system_viewer_' . config('organization_id') . '_');
    }
} 
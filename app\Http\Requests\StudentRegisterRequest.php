<?php

namespace App\Http\Requests;

use App\Rules\CheckIfStringIsEnglish;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StudentRegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {


        
        return [
            'fullname' => [
                'required',
                'string',
                'max:255',
                'different:email',
                new CheckIfStringIsEnglish(),
            ],
            'nationality' => [
                'required',
                Rule::notIn(['no']),
            ],
            'displayname' => 'required|string|max:255|different:email',
            'username' => 'required|string|max:25|unique:users,username',
            'email' => 'required|string|email|max:255|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'cf-turnstile-response' => 'required'
        ];
    }

    public function messages()
    {
        return [
            'username.unique' => 'This username is already in use. Please try a different username.',
            'email.unique' => 'This email address is already registered. Please <a href="' . route('get.login') . '">log in</a> if you already have an account or use a different email address.',
        ];
    }

}

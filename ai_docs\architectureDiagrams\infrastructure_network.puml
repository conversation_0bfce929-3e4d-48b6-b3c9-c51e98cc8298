@startuml infrastructure_network
!theme vibrant

title Infrastructure / Network Diagram

cloud "Internet" as Internet
actor "User" as User

node "Firewall" as FW_Public {
    node "Load Balancer" as LB << Load Balancer >>
}

node "Private Network / VPC" as PrivateNetwork {
    collections "Web Servers (Auto-Scaling Group)" as WebServers << Web Server >> {
        node "Web Server 1" as WS1 {
            artifact "Laravel App" as App1
            component "PHP-FPM" as PHP1
            component "Nginx/Apache" as WebSrv1
        }
        node "Web Server 2" as WS2 {
            artifact "Laravel App" as App2
            component "PHP-FPM" as PHP2
            component "Nginx/Apache" as WebSrv2
        }
        node "..." as WS_More
    }

    collections "Queue Workers (Auto-Scaling Group)" as QueueWorkers {
        node "Worker 1" as QW1 {
            artifact "Laravel App (Worker)" as AppW1
            component "Supervisor" as Sup1
        }
         node "Worker 2" as QW2 {
            artifact "Laravel App (Worker)" as AppW2
            component "Supervisor" as Sup2
        }
        node "..." as QW_More
    }

    node "Firewall" as FW_Internal {
        database "Database Server" as DB << MySQL >>
        node "Cache Server" as C<PERSON> << Redis >>
        node "Queue Broker" as QueueBroker << Redis/SQS >>
    }

    node "Storage" as Storage << S3 / Local FS >>
}

' Connections
User --> Internet
Internet --> FW_Public : HTTPS (443)
FW_Public --> LB
LB --> WebServers : HTTP/S (80/443)

WebServers --> FW_Internal : SQL (3306)
WebServers --> FW_Internal : Cache (6379)
WebServers --> FW_Internal : Queue (6379/API)
WebServers --> Storage : Filesystem / S3 API

QueueWorkers --> FW_Internal : SQL (3306)
QueueWorkers --> FW_Internal : Cache (6379)
QueueWorkers --> FW_Internal : Queue (6379/API)
QueueWorkers --> Storage : Filesystem / S3 API

FW_Internal --> DB
FW_Internal --> Cache
FW_Internal --> QueueBroker

@enduml 
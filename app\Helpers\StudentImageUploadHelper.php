<?php

declare(strict_types=1);

namespace App\Helpers;

use App\Student;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

/**
 * Helper class for standardized student image uploads
 */
class StudentImageUploadHelper
{
    /**
     * Upload a student photo and save it to the student_photo field
     * 
     * @param UploadedFile $file The uploaded image file
     * @param Student|null $student Optional student to update
     * @return string The path to the saved image
     * @throws \Exception If the file couldn't be saved
     */
    public static function uploadStudentPhoto(UploadedFile $file, ?Student $student = null): string
    {
        try {
            Log::debug('Starting student photo upload', [
                'original_name' => $file->getClientOriginalName(),
                'student_id' => $student ? $student->id : null
            ]);
            
            // Process the image using Intervention Image
            $image = Image::make($file);
            
            // Use storage_path to get absolute path
            $pathImage = storage_path('app/public/uploads/student');
            
            // Create directory if it doesn't exist
            if (!File::exists($pathImage)) {
                File::makeDirectory($pathImage, 0775, true);
                Log::debug('Created directory for student photos', ['path' => $pathImage]);
            }
            
            // Generate unique filename
            $name = md5($file->getClientOriginalName() . time()) . ".png";
            $fullPath = $pathImage . '/' . $name;
            
            // Save image with full path
            $image->save($fullPath);
            
            // Path to store in the database
            $imagePath = 'public/uploads/student/' . $name;
            
            Log::info('Student photo uploaded successfully', [
                'image_path' => $imagePath,
                'student_id' => $student ? $student->id : null
            ]);
            
            // Update student record if provided
            if ($student) {
                $student->student_photo = $imagePath;
                $student->save();
                
                Log::info('Updated student record with new photo', [
                    'student_id' => $student->id,
                    'photo_path' => $imagePath
                ]);
            }
            
            return $imagePath;
        } catch (\Exception $e) {
            Log::error('Error uploading student photo', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'student_id' => $student ? $student->id : null
            ]);
            
            throw new \Exception('Failed to upload student photo: ' . $e->getMessage());
        }
    }
    
    /**
     * Delete a student photo file
     * 
     * @param string $path Path to the file to delete
     * @return bool True if file was deleted, false otherwise
     */
    public static function deleteStudentPhoto(string $path): bool
    {
        try {
            Log::debug('Attempting to delete student photo', ['path' => $path]);
            
            if (File::exists(storage_path('app/' . $path))) {
                File::delete(storage_path('app/' . $path));
                Log::info('Student photo deleted successfully', ['path' => $path]);
                return true;
            } else if (File::exists(public_path($path))) {
                File::delete(public_path($path));
                Log::info('Student photo deleted successfully from public path', ['path' => $path]);
                return true;
            }
            
            Log::warning('Student photo not found when attempting to delete', ['path' => $path]);
            return false;
        } catch (\Exception $e) {
            Log::error('Error deleting student photo', [
                'path' => $path,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
} 
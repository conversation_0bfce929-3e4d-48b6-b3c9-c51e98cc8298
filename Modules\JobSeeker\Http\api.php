<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use <PERSON><PERSON><PERSON>\JobSeeker\Http\Controllers\UserDeviceTokenController;
use Mo<PERSON><PERSON>\JobSeeker\Http\Controllers\JobsController;
use Mo<PERSON><PERSON>\JobSeeker\Http\Controllers\Api\ProviderDiagnosticController;

/*
|--------------------------------------------------------------------------
| API Routes for JobSeeker Module
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your module. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// API routes for job seekers using standard jobseeker authentication
Route::middleware(['auth:job_seeker'])
    ->prefix('v1/jobseeker') // Added versioning and clearer prefix
    ->name('api.jobseeker.')      // Route name prefix
    ->group(function () {

    // Job Categories Management
    // GET /api/v1/jobseeker/categories
    Route::get('categories', [JobsController::class, 'getCategories'])
        ->name('categories.index');

    // User Device Tokens Management
    // POST /api/v1/jobseeker/device-tokens/register
    Route::post('device-tokens/register', [UserDeviceTokenController::class, 'register'])
        ->name('device-tokens.register');

    // GET /api/v1/jobseeker/device-tokens
    Route::get('device-tokens', [UserDeviceTokenController::class, 'index'])
        ->name('device-tokens.index');

    // DELETE /api/v1/jobseeker/device-tokens/{id}
    Route::delete('device-tokens/{id}', [UserDeviceTokenController::class, 'destroy'])
        ->name('device-tokens.destroy')
        ->where('id', '[0-9]+'); // Ensure ID is numeric
});

// Provider Diagnostic API routes for admin users
Route::prefix('v1/jobseeker/diagnostic')
    ->name('api.jobseeker.diagnostic.')
    ->group(function () {

    // POST /api/v1/jobseeker/diagnostic/run
    Route::post('run', [ProviderDiagnosticController::class, 'runDiagnostic'])
        ->name('run');

    // GET /api/v1/jobseeker/diagnostic/providers
    Route::get('providers', [ProviderDiagnosticController::class, 'getProviderInfo'])
        ->name('providers');

    // GET /api/v1/jobseeker/diagnostic/health
    Route::get('health', [ProviderDiagnosticController::class, 'healthCheck'])
        ->name('health');
});

// Example of a public route within the module (if any)
// Route::get('jobseeker/public-info', function() {
//     return response()->json(['message' => 'Public info from JobSeeker module']);
// })->name('api.jobseeker.public-info'); 
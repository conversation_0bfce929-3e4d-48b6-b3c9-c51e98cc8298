<?php

namespace App;

use App;
use Modules\Jobs\Traits\Lang;
use Modules\Jobs\Traits\IsDefault;
use Modules\Jobs\Traits\Active;
use Modules\Jobs\Traits\Sorted;
use Illuminate\Database\Eloquent\Model;

class SalaryPeriod extends Model
{

    use Lang;
    use IsDefault;
    use Active;
    use Sorted;

    protected $table = 'salary_periods';
    public $timestamps = true;
    protected $guarded = ['id'];
    //protected $dateFormat = 'U';
    protected $casts = ['created_at', 'updated_at'];

}

@extends('layouts.hound') 
@section('content')

<!-- Row -->
<div class="row">
    <div class="container">
        <div class="panel panel-default card-view">
            <div class="panel-wrapper collapse in">
                <div class="panel-body">
                    <div class="form-wrap">
                            {!! Form::open(['url' => route('general.forms.store' , $form_builder->id) ]) !!}
                            <h6 class="txt-dark capitalize-font"><i class="zmdi zmdi-info-outline mr-10"></i>Add New: {{ $form_builder->title}}</h6>
                            <hr class="light-grey-hr" />
                            @if ($errors->any())
                            <ul class="alert alert-danger">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            @endif
                            <h6 class="txt-dark capitalize-font mb-10">
                                <i class="zmdi zmdi-comment-text mr-10"></i>
                                Request Details
                            </h6>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <textarea name="content" class="form-control" rows="8" placeholder="Write the Details Here"></textarea>
                                    </div>
                                </div>
                            </div>
                            <!--/row-->
                            @if($form_builder->date_or_range == 'date')
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label mb-10">{{ $form_builder->date_label }}</label>
                                        <input type="text" name="from_date" class="form-control datetime">
                                    </div>
                                </div>
                            </div>
                            @elseif($form_builder->date_or_range == 'range')
                            <div class="row">
                                <div class="col-md-12">
                                    <label class="control-label mb-10">{{ $form_builder->date_label }}</label>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label mb-10">From</label>
                                        <input type="text" name="from_date" class="form-control datetime">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label mb-10">To</label>
                                        <input type="text" name="to_date" class="form-control datetime">
                                    </div>
                                </div>
                            </div>
                            @endif
                            <div class="form-actions text-center">
                                <button class="btn btn-success btn-icon left-icon mr-10 "> <i class="fa fa-check"></i> <span>Send Request/Form</span></button>
                                <div class="clearfix"></div>
                            </div>
                            {!! Form::close()  !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /Row -->

@endsection
@section('js')
@include('jssnippets.flatpickr')
<script>
    $(document).ready(function(){
        flatpickr('.datetime', {
            enableTime: {{ $form_builder->request_time ? 'true' : 'false' }},
            minDate: "today",
            dateFormat: 'Y-m-d H:i:S',
            "plugins": [new confirmDatePlugin({})]
        });

        $('form').submit(function(e){
            if(!$('.datetime').val()){
                e.preventDefault();
                alert('Enter Dates');
            }
        })
    })
</script>
@append
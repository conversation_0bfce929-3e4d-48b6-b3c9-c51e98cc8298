<?php

namespace Modules\Jobs\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class RedirectIfCompanyNotAuthenticated
{


    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = 'company')
    {

        if (!Auth::guard($guard)->check()) {
            return redirect('/login');
        }
        return $next($request);
    }

}

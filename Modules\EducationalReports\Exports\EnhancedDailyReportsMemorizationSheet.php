<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\Student;
use App\StudentHefzReport;
use App\StudentHefzPlan;
use App\AttendanceOption;
use App\Classes;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\EducationalReports\Exports\Traits\PagesCalculator;
use Carbon\Carbon;

final class EnhancedDailyReportsMemorizationSheet implements WithTitle, WithStyles, WithEvents
{
    use PagesCalculator;
    
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get memorization reports - includes ALL students with active plans for every day of the month
     */
    private function getMemorizationReports(): Collection
    {
        $classIds = $this->filters['classIds'] ?? [$this->filters['classId']];
        $year = $this->filters['year'];
        $month = $this->filters['month'];
        $studentId = $this->filters['studentId'] ?? null;

        // Get all days in the month
        $startDate = Carbon::create($year, $month, 1);
        $endDate = $startDate->copy()->endOfMonth();
        $daysInMonth = [];
        
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $daysInMonth[] = $date->format('Y-m-d');
        }

        // Get all students with active plans in the selected classes for this month
        $planYearMonth = sprintf('%d-%02d', $year, $month);
        
        $studentsWithPlans = Student::with([
            'hefzPlans' => function($query) use ($classIds, $planYearMonth) {
                $query->whereIn('class_id', $classIds)
                      ->where('plan_year_and_month', $planYearMonth)
                      ->where('status', 'active')
                      ->with(['halaqah.center', 'halaqah.programs', 'halaqah.teachers']);
            }
        ])
        ->whereHas('hefzPlans', function($query) use ($classIds, $planYearMonth) {
            $query->whereIn('class_id', $classIds)
                  ->where('plan_year_and_month', $planYearMonth)
                  ->where('status', 'active');
        })
        ->where('status', 'active')  // Only include active students
        ->whereNull('deleted_at')    // Exclude soft-deleted students
        ->when($studentId, function($query, $studentId) {
            return $query->where('id', $studentId);
        })
        ->get();

        // Get all existing reports for these students and classes
        $existingReports = StudentHefzReport::with([
            'classes.center',
            'classes.programs', 
            'classes.teachers',
            'fromSurat',
            'toSurat',
            'result',
            'attendanceOptions',
            'hefzPlan'
        ])
        ->whereIn('class_id', $classIds)
        ->whereYear('created_at', $year)
        ->whereMonth('created_at', $month)
        ->when($studentId, function($query, $studentId) {
            return $query->where('student_id', $studentId);
        })
        ->get()
        ->groupBy(function($report) {
            return $report->student_id . '_' . $report->created_at->format('Y-m-d') . '_' . $report->class_id;
        });

        $results = collect();

        // Generate rows for ALL students for ALL days
        foreach ($studentsWithPlans as $student) {
            foreach ($student->hefzPlans as $plan) {
                if (in_array($plan->class_id, $classIds)) {
                    $classProgram = $plan->halaqah->programs->first()->title ?? 'N/A';
                    $teacherNames = $plan->halaqah->teachers->pluck('full_name')->join(', ');
                    
                    foreach ($daysInMonth as $date) {
                        $reportKey = $student->id . '_' . $date . '_' . $plan->class_id;
                        $existingReport = $existingReports->get($reportKey)?->first();
                        
                        $carbonDate = Carbon::parse($date);
                        
                        if ($existingReport) {
                            // Use existing report data
                            $numberOfPages = $this->calculateMemorizationPages($existingReport);
                            // Determine timetable strictly from class timetable for this date
                            $isInTimetable = $this->checkIfDayInTimetableByDate($existingReport->class_id, $date);
                            
                            $results->push([
                                'centre_id' => $existingReport->classes->center->id ?? 'N/A',
                                'centre_name' => $existingReport->classes->center->name ?? 'N/A',
                                'class_id' => $existingReport->class_id,
                                'class_name' => ($existingReport->classes->name ?? $existingReport->classes->class_code) ?? 'N/A',
                                'class_program' => $classProgram,
                                'teacher_name' => $teacherNames ?: 'N/A',
                                'student_id' => $existingReport->student_id,
                                'student_name' => $student->full_name ?? 'N/A',
                                'date' => $date,
                                'day' => $carbonDate->format('D'), // 3-letter weekday
                                'is_day_in_timetable' => $isInTimetable ? 'Yes' : 'No',
                                'attendance' => $existingReport->attendanceOptions->title ?? 'N/A',
                                'from_surah' => $existingReport->fromSurat->name ?? 'N/A',
                                'from_verse' => $existingReport->hefz_from_ayat ?? 'N/A',
                                'to_surah' => $existingReport->toSurat->name ?? 'N/A',
                                'to_verse' => $existingReport->hefz_to_ayat ?? 'N/A',
                                'no_of_pages' => $numberOfPages,
                                'evaluation' => $existingReport->result->title ?? 'N/A',
                                'evaluation_note' => $existingReport->hefz_evaluation_note ?? '',
                            ]);
                        } else {
                            // Create blank row for days without reports (still compute timetable by class & date)
                            $isInTimetable = $this->checkIfDayInTimetableByDate($plan->class_id, $date);
                            $results->push([
                                'centre_id' => $plan->halaqah->center->id ?? 'N/A',
                                'centre_name' => $plan->halaqah->center->name ?? 'N/A',
                                'class_id' => $plan->class_id,
                                'class_name' => ($plan->halaqah->name ?? $plan->halaqah->class_code) ?? 'N/A',
                                'class_program' => $classProgram,
                                'teacher_name' => $teacherNames ?: 'N/A',
                                'student_id' => $student->id,
                                'student_name' => $student->full_name ?? 'N/A',
                                'date' => $date,
                                'day' => $carbonDate->format('D'), // 3-letter weekday
                                'is_day_in_timetable' => $isInTimetable ? 'Yes' : 'No',
                                'attendance' => 'No Report',
                                'from_surah' => 'N/A',
                                'from_verse' => 'N/A',
                                'to_surah' => 'N/A',
                                'to_verse' => 'N/A',
                                'no_of_pages' => 0,
                                'evaluation' => 'N/A',
                                'evaluation_note' => '',
                            ]);
                        }
                    }
                }
            }
        }

        return $results->sortBy(['student_name', 'date']);
    }

    /**
     * Calculate number of pages using stored procedure for memorization
     */
    private function calculateMemorizationPages(StudentHefzReport $report): int
    {
        // Use the trait's method with correct MonthlyPlanController logic
        $reportArray = [
            'student_id' => $report->student_id,
            'class_id' => $report->class_id,
            'hefz_from_surat' => $report->hefz_from_surat,
            'hefz_from_ayat' => $report->hefz_from_ayat,
            'hefz_to_surat' => $report->hefz_to_surat,
            'hefz_to_ayat' => $report->hefz_to_ayat,
            'created_at' => $report->created_at->format('Y-m-d H:i:s')
        ];
        
        return $this->calculateReportPages($reportArray, 'memorization');
    }

    /**
     * Check if the day is in timetable using class_timetable relationship
     */
    private function checkIfDayInTimetable(StudentHefzReport $report): bool
    {
        try {
            $dayOfWeek = strtolower($report->created_at->format('D')); // sat, sun, mon, tue, wed, thu, fri
            
            // Check if class has timetable for this day using class_timetable table
            $timetable = DB::table('class_timetable')
                ->where('class_id', $report->class_id)
                ->whereNull('deleted_at')
                ->first();
                
            if (!$timetable) {
                return false;
            }
            
            // Check if the specific day column has a time value (not null)
            return !is_null($timetable->$dayOfWeek);
        } catch (\Exception $e) {
            \Log::error('Error checking timetable: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check timetable by class and date (not dependent on a report existing)
     */
    private function checkIfDayInTimetableByDate(int $classId, string $date): bool
    {
        try {
            $dayOfWeek = strtolower(Carbon::parse($date)->format('D'));
            $timetable = DB::table('class_timetable')
                ->where('class_id', $classId)
                ->whereNull('deleted_at')
                ->first();
            if (!$timetable) {
                return false;
            }
            return !is_null($timetable->$dayOfWeek);
        } catch (\Exception $e) {
            \Log::error('Error checking timetable by date: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get analytics data for performance dashboard
     */
    private function getAnalyticsData(): array
    {
        $classIds = $this->filters['classIds'] ?? [$this->filters['classId']];
        $year = $this->filters['year'];
        $month = $this->filters['month'];

        // Daily trends aggregated across all classes with class info and proper attendance calculation
        $studentFilter = !empty($this->filters['studentId']) ? " AND shr.student_id = " . intval($this->filters['studentId']) : "";
        $dailyTrends = DB::select("
            SELECT 
                DATE(shr.created_at) as report_date,
                shr.class_id,
                c.class_code as class_name,
                COUNT(DISTINCT cs.student_id) as active_students,
                COUNT(*) as total_sessions,
                COUNT(CASE WHEN ao.title IN ('late', 'on_time') THEN 1 END) as present_count,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) as avg_score,
                SUM(COALESCE(shr.pages_memorized, 0)) as pages_memorized
            FROM student_hefz_report shr
            JOIN classes c ON shr.class_id = c.id
            JOIN class_students cs ON shr.student_id = cs.student_id AND shr.class_id = cs.class_id
            LEFT JOIN attendance_options ao ON shr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON shr.hefz_evaluation_id = eso.id
            WHERE shr.class_id IN (" . implode(',', $classIds) . ")
                AND YEAR(shr.created_at) = ?
                AND MONTH(shr.created_at) = ?
                AND cs.deleted_at IS NULL
                {$studentFilter}
            GROUP BY DATE(shr.created_at), shr.class_id, c.class_code
            ORDER BY report_date DESC, shr.class_id
            LIMIT 50
        ", [$year, $month]);

        // Map translatable class names for trends
        if (!empty($dailyTrends)) {
            $trendClassIds = array_values(array_unique(array_map(function($t){ return (int)$t->class_id; }, $dailyTrends)));
            $classMap = collect(\App\Classes::whereIn('id', $trendClassIds)->get())->keyBy('id');
            foreach ($dailyTrends as $t) {
                $t->class_name = optional($classMap->get((int)$t->class_id))->name ?? ($t->class_name ?? 'N/A');
            }
        }

        // At-risk students across all classes with student filter
        $atRiskStudents = DB::select("
            SELECT 
                s.full_name,
                COUNT(*) as total_sessions,
                COUNT(CASE WHEN ao.title IN ('late', 'on_time') THEN 1 END) as attended_sessions,
                ROUND((COUNT(CASE WHEN ao.title IN ('late', 'on_time') THEN 1 END) / COUNT(*)) * 100, 1) as attendance_rate,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) as avg_score
            FROM student_hefz_report shr
            JOIN students s ON shr.student_id = s.id
            JOIN class_students cs ON shr.student_id = cs.student_id AND shr.class_id = cs.class_id
            LEFT JOIN attendance_options ao ON shr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON shr.hefz_evaluation_id = eso.id
            WHERE shr.class_id IN (" . implode(',', $classIds) . ")
                AND YEAR(shr.created_at) = ?
                AND MONTH(shr.created_at) = ?
                AND cs.deleted_at IS NULL
                {$studentFilter}
            GROUP BY shr.student_id, s.full_name
            HAVING attendance_rate < 75 OR avg_score < 0.5
            ORDER BY attendance_rate ASC, avg_score ASC
            LIMIT 15
        ", [$year, $month]);

        // Teacher performance across all classes (one row per class)
        $teacherPerformance = DB::select("
            SELECT 
                shr.class_id,
                c.class_code,
                GROUP_CONCAT(DISTINCT u.full_name SEPARATOR ', ') as teacher_name,
                COUNT(DISTINCT shr.student_id) as unique_students,
                COUNT(DISTINCT DATE(shr.created_at)) as actual_sessions,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) as avg_evaluation,
                ROUND((COUNT(CASE WHEN ao.title IN ('late', 'on_time') THEN 1 END) / 
                      NULLIF(COUNT(*), 0)) * 100, 1) as class_attendance_rate,
                0 as total_pages_taught, -- Will be calculated using stored procedures
                0 as total_pages_planned -- Will be calculated from student_hefz_plans
            FROM student_hefz_report shr
            JOIN classes c ON shr.class_id = c.id
            JOIN class_teachers ct ON c.id = ct.class_id
            JOIN employees u ON ct.employee_id = u.id
            LEFT JOIN attendance_options ao ON shr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON shr.hefz_evaluation_id = eso.id
            WHERE shr.class_id IN (" . implode(',', $classIds) . ")
                AND YEAR(shr.created_at) = ?
                AND MONTH(shr.created_at) = ?
                {$studentFilter}
            GROUP BY shr.class_id, c.class_code
            ORDER BY avg_evaluation DESC, class_attendance_rate DESC
        ", [$year, $month]);

        return [
            'daily_trends' => $dailyTrends,
            'at_risk_students' => $atRiskStudents,
            'teacher_performance' => $teacherPerformance
        ];
    }

    /**
     * Create performance dashboard for multi-class aggregation
     */
    private function createPerformanceDashboard($worksheet, $startRow, $analytics): int
    {
        $dailyTrends = $analytics['daily_trends'];
        $atRiskStudents = $analytics['at_risk_students'];
        $teacherPerformance = $analytics['teacher_performance'];
        
        // Get year and month from filters for calculations
        $year = $this->filters['year'];
        $month = $this->filters['month'];

        // Title
        $worksheet->setCellValue("A{$startRow}", "📌 PERFORMANCE DASHBOARD - DAILY INSIGHTS (Multi-Class Aggregated)");
        $worksheet->mergeCells("A{$startRow}:Q{$startRow}");
        $worksheet->getStyle("A{$startRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'D32F2F']]
        ]);

        $currentRow = $startRow + 2;

        // Recent Performance Trends (Last 10 days across all classes)
        $worksheet->setCellValue("A{$currentRow}", "📈 RECENT PERFORMANCE TRENDS (All Selected Classes)");
        $worksheet->mergeCells("A{$currentRow}:F{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2E7D32']]
        ]);
        $currentRow++;

        $trendHeaders = ['Date', 'Class ID', 'Class Name', 'Students', 'Sessions', 'Avg Score', 'Attendance %', 'Pages'];
        foreach ($trendHeaders as $index => $header) {
            $col = chr(65 + $index); // A, B, C, etc.
            $worksheet->setCellValue("{$col}{$currentRow}", $header);
        }
        $currentRow++;

        foreach (array_slice($dailyTrends, 0, 30) as $trend) {
            $attendanceRate = $trend->total_sessions > 0 ? round(((float)$trend->present_count / (float)$trend->total_sessions) * 100, 1) : 0;
            $worksheet->setCellValue("A{$currentRow}", $trend->report_date);
            $worksheet->setCellValue("B{$currentRow}", $trend->class_id);
            $worksheet->setCellValue("C{$currentRow}", $trend->class_name);
            $worksheet->setCellValue("D{$currentRow}", $trend->active_students);
            $worksheet->setCellValue("E{$currentRow}", $trend->total_sessions);
            $worksheet->setCellValue("F{$currentRow}", round((float)($trend->avg_score ?? 0) * 100, 1) . '%');
            $worksheet->setCellValue("G{$currentRow}", $attendanceRate . '%');
            $worksheet->setCellValue("H{$currentRow}", $trend->pages_memorized);
            $currentRow++;
        }

        $currentRow += 2;

        // At-Risk Students Alert (across all classes)
        $worksheet->setCellValue("A{$currentRow}", "🚨 AT-RISK STUDENTS (Immediate Attention Required - All Classes)");
        $worksheet->mergeCells("A{$currentRow}:Q{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'FF5722']]
        ]);
        $currentRow++;

        if (count($atRiskStudents) > 0) {
            $riskHeaders = ['Student Name', 'Sessions', 'Attended', 'Attendance %', 'Avg Score', 'Risk Level'];
            foreach ($riskHeaders as $index => $header) {
                $col = chr(65 + $index);
                $worksheet->setCellValue("{$col}{$currentRow}", $header);
            }
            $currentRow++;

            foreach (array_slice($atRiskStudents, 0, 15) as $student) {
                $riskLevel = '🔴 Critical';
                if ($student->attendance_rate >= 50 && $student->avg_score >= 0.3) {
                    $riskLevel = '⚠️ Moderate';
                }

                $worksheet->setCellValue("A{$currentRow}", $student->full_name);
                $worksheet->setCellValue("B{$currentRow}", $student->total_sessions);
                $worksheet->setCellValue("C{$currentRow}", $student->attended_sessions);
                $worksheet->setCellValue("D{$currentRow}", $student->attendance_rate . '%');
                $worksheet->setCellValue("E{$currentRow}", round((float)($student->avg_score ?? 0) * 100, 1) . '%');
                $worksheet->setCellValue("F{$currentRow}", $riskLevel);
                $currentRow++;
            }
        } else {
            $worksheet->setCellValue("A{$currentRow}", "✅ No at-risk students identified across all classes");
            $currentRow++;
        }

        $currentRow += 2;

        // Teacher Performance Summary (across all classes)
        $worksheet->setCellValue("A{$currentRow}", "👨‍🏫 TEACHER PERFORMANCE SUMMARY (All Classes)");
        $worksheet->mergeCells("A{$currentRow}:Q{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '1565C0']]
        ]);
        $currentRow++;

        if (count($teacherPerformance) > 0) {
        $teacherHeaders = ['Class', 'Class Name', 'Teacher', 'Students', 'Sessions (Actual/Target)', 'Avg Score', 'Attendance %', 'Pages Taught (Actual/Target)', 'Performance'];
            foreach ($teacherHeaders as $index => $header) {
                $col = chr(65 + $index);
                $worksheet->setCellValue("{$col}{$currentRow}", $header);
            }
            $currentRow++;

            // Build class id -> name map using translatable model
            $classIdList = array_values(array_unique(array_map(function($t){ return (int)$t->class_id; }, $teacherPerformance)));
            $classNameMap = collect(\App\Classes::whereIn('id', $classIdList)->get())->keyBy('id');

            foreach ($teacherPerformance as $teacher) {
                $performance = '✅ Excellent';
                if ($teacher->avg_evaluation < 0.7 || $teacher->class_attendance_rate < 75) {
                    $performance = '⚠️ Needs Support';
                }
                if ($teacher->avg_evaluation < 0.5 || $teacher->class_attendance_rate < 60) {
                    $performance = '🔴 Requires Training';
                }

                // Calculate target sessions for this class
                $targetSessions = $this->calculateTargetSessions((int)$teacher->class_id, $year, $month);
                
                // Split teacher names for display and comment
                $teacherNameData = $this->splitTeacherNames($teacher->teacher_name);
                
                // Calculate pages taught and planned
                $pagesTaught = $this->calculateTeacherPagesTaught((int)$teacher->class_id, $year, $month);
                $pagesPlanned = $this->calculatePagesPlanned((int)$teacher->class_id, $year, $month);

                $className = optional($classNameMap->get((int)$teacher->class_id))->name ?? 'N/A';

                $worksheet->setCellValue("A{$currentRow}", $teacher->class_code);
                $worksheet->setCellValue("B{$currentRow}", $className);
                $worksheet->setCellValue("C{$currentRow}", $teacherNameData['display']);
                $worksheet->setCellValue("D{$currentRow}", $teacher->unique_students);
                $worksheet->setCellValue("E{$currentRow}", $teacher->actual_sessions . '/' . $targetSessions);
                $worksheet->setCellValue("F{$currentRow}", round((float)($teacher->avg_evaluation ?? 0) * 100, 1) . '%');
                $worksheet->setCellValue("G{$currentRow}", $teacher->class_attendance_rate . '%');
                $worksheet->setCellValue("H{$currentRow}", $pagesTaught . '/' . $pagesPlanned);
                $worksheet->setCellValue("I{$currentRow}", $performance);
                
                // Add comment for additional teachers if needed
                if ($teacherNameData['comment']) {
                    $worksheet->getComment("C{$currentRow}")->getText()->createTextRun($teacherNameData['comment']);
                }
                
                $currentRow++;
            }
        } else {
            $worksheet->setCellValue("A{$currentRow}", "No teacher performance data available");
            $currentRow++;
        }

        return $currentRow + 2;
    }

    /**
     * Get the sheet title
     */
    public function title(): string
    {
        return 'Memorization Reports';
    }

    /**
     * Configure sheet events
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $worksheet = $event->sheet->getDelegate();
                $this->populateSheet($worksheet);
            },
        ];
    }

    /**
     * Populate the sheet with data
     */
    private function populateSheet(Worksheet $worksheet): void
    {
        // Get analytics data first
        $analytics = $this->getAnalyticsData();
        
        // Create performance dashboard at the top
        $currentRow = $this->createPerformanceDashboard($worksheet, 1, $analytics);
        $currentRow += 2;

        // Get memorization reports data
        $reports = $this->getMemorizationReports();
        
        if ($reports->isEmpty()) {
            $worksheet->setCellValue("A{$currentRow}", "No memorization reports found for the selected criteria.");
            return;
        }

        // Headers - reordered with Student ID and Student Name first
        $headers = [
            'Student ID',           // A
            'Student Name',         // B  
            'Date',                 // C
            'Day',                  // D - New column
            'Centre ID',            // E
            'Centre Name',          // F
            'Class ID',             // G
            'Class Name',           // H (new)
            'Class Program',        // I
            'Teacher Name',         // J
            'Is Day in Timetable',  // K
            'Attendance',           // L
            'From Surah',           // M
            'From Verse',           // N
            'To Surah',             // O
            'To Verse',             // P
            'No of Pages',          // Q
            'Evaluation',           // R
            'Evaluation Note'       // S
        ];

        // Set headers
        foreach ($headers as $index => $header) {
            $col = chr(65 + $index);
            $worksheet->setCellValue("{$col}{$currentRow}", $header);
        }

        // Style headers
        $headerRange = "A{$currentRow}:S{$currentRow}";
        $worksheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2E7D32']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $currentRow++;
        $dataStartRow = $currentRow;

                // Iterate reports directly – no grouping, repeat Student ID & Name each day
        foreach ($reports as $report) {
            // Use pre-calculated pages from report array (calculated during data preparation)
            $calculatedPages = $report['no_of_pages'] ?? 0;
            
            $worksheet->setCellValue("A{$currentRow}", $report['student_id']);
            $worksheet->setCellValue("B{$currentRow}", $report['student_name']);
            $worksheet->setCellValue("C{$currentRow}", $report['date']);
            $worksheet->setCellValue("D{$currentRow}", $report['day']);
            $worksheet->setCellValue("E{$currentRow}", $report['centre_id']);
            $worksheet->setCellValue("F{$currentRow}", $report['centre_name']);
            $worksheet->setCellValue("G{$currentRow}", $report['class_id']);
            $worksheet->setCellValue("H{$currentRow}", $report['class_name'] ?? '');
            $worksheet->setCellValue("I{$currentRow}", $report['class_program']);
            $worksheet->setCellValue("J{$currentRow}", $report['teacher_name']);
            $worksheet->setCellValue("K{$currentRow}", $report['is_day_in_timetable']);
            $worksheet->setCellValue("L{$currentRow}", $report['attendance']);
            $worksheet->setCellValue("M{$currentRow}", $report['from_surah']);
            $worksheet->setCellValue("N{$currentRow}", $report['from_verse']);
            $worksheet->setCellValue("O{$currentRow}", $report['to_surah']);
            $worksheet->setCellValue("P{$currentRow}", $report['to_verse']);
            $worksheet->setCellValue("Q{$currentRow}", $calculatedPages); // Use calculated pages
            $worksheet->setCellValue("R{$currentRow}", $report['evaluation']);
            $worksheet->setCellValue("S{$currentRow}", $report['evaluation_note']);
            
            // Add light grey background for non-timetable days
            if (!$report['is_day_in_timetable']) {
                $worksheet->getStyle("A{$currentRow}:S{$currentRow}")
                         ->getFill()
                         ->setFillType(Fill::FILL_SOLID)
                         ->getStartColor()
                         ->setRGB('EFEFEF');
            }
            
            $currentRow++;
        }

        // Apply borders to data range
        $dataEndRow = $currentRow - 1;
        $dataRange = "A{$dataStartRow}:S{$dataEndRow}";
        $worksheet->getStyle($dataRange)->applyFromArray([
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        // Auto-size columns
        foreach (range('A', 'S') as $col) {
            $worksheet->getColumnDimension($col)->setAutoSize(true);
        }
    }

    /**
     * Configure sheet styles
     */
    public function styles(Worksheet $sheet): array
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 14]],
        ];
    }
}
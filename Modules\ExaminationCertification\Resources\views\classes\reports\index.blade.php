@extends('layouts.hound')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary panel-card">
                <div class="panel-heading clearfix">
                    <div class="col-sm-4">
                        <h4>Class Report</h4>
                    </div>
                    <div class="col-sm-4">
                        <select name="" id="" class="form-control">
                            <option value="">Summery</option>
                            <option value="">By Student</option>
                            <option value="">By Student and Lesson</option>

                        </select>
                    </div>
                    <div class="col-sm-4">
                        <form action="" method="get" id="filter_date">
                            <select name="from_date" id="" class="form-control" onchange="$('#filter_date').submit()">
                                <option value="{{ $from_date->format('Y-m') }}">{{ $from_date->format('M Y') }}</option>
                                @for($d = $class->created_at->copy(); $d <= \Carbon\Carbon::now()->endOfMonth();
                                    $d->addMonth())
                                    <option value="{{$d->format('Y-m')}}">{{ $d->format('M Y') }}</option>
                                    @endfor
                            </select>
                        </form>
                    </div>

                </div>
                <div class="panel-body">
                    <div class="tab-struct mt-40">
                        <ul role="tablist" class="nav nav-tabs" id="myTabs">
                            <li class="active" role="presentation">
                                <a aria-expanded="true" data-toggle="tab" role="tab" id="home_tab"
                                    href="#report_table">Report</a>
                            </li>
                            <li role="presentation" class="">
                                <a data-toggle="tab" id="profile_tab" role="tab" href="#attendance_chart"
                                    aria-expanded="false">Attendance Chart</a>
                            </li>
                        </ul>
                        <div class="tab-content table-bordered" id="myTabContent">
                            <div id="report_table" class="tab-pane fade active in" role="tabpanel">
                                <div class="col-md-12 clearfix">
                                    @foreach($class_programs as $program)

                                    <h2>{{ $program['info']->title }} </h2>
                                    @if($program['type'] == 'program')
                                    @if(auth()->user()->can('add class_report'))
                                    <div class="clearfix">
                                        <a href="{{ url('workplace/education/classes/'.$class->id.'/reports?from_date='.$program['next_report_date']->format("Y-m")) }}"
                                            class="btn btn-danger btn-sm pull-right">Add
                                            {{$program['next_report_date']->format("D d/m/Y")}} Report</a>
                                    </div>
                                    @endif
                                    <div class="table-responsive">

                                        <table class="table table-striped table-bordered">
                                            <thead class="text-center">
                                                <tr>
                                                    <td rowspan="2">Day & Date</td>
                                                    <td colspan="4">Report</td>
                                                    <td rowspan="2">Report Status</td>
                                                </tr>
                                                <tr>
                                                    <td>On-Time</td>
                                                    <td>Atteneded-Late</td>
                                                    <td>Absent with reason</td>
                                                    <td>Absent</td>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @for($i = $from_date->copy(); $i < $from_date->copy()->addMonth() && $i
                                                    <= date('Y-m-d 23:00:00'); $i->addDay())
                                                        <?php
                                                                    if($teacher){
                                                                        $subject = $teacher->subjects->where('program_id',$program['info']->id)->first();
                                                                        $teacher_timetable = $subject->timetable; 
                                                                    }
                                                                    ?>
                                                        <tr>
                                                            <td>{{ $i->format('Y/m/d D') }}</td>
                                                            @if($class->studentsAtDate($i)->count() < 1) <td colspan="5"
                                                                class="text-center">
                                                                There is no student registered in the class at this date
                                                                </td>
                                                                @elseif(isset($program['teacher']) &&
                                                                isset($class_subjects_reports['program_'.$program['info']->id])
                                                                &&
                                                                isset($class_subjects_reports['program_'.$program['info']->id][$i->format("Y/m/d")]))
                                                                <td>{{$class_subjects_reports['program_'.$program['info']->id][$i->format("Y/m/d")]->attendace->where('attendance' , "=" , "on_time")->count() }}
                                                                </td>
                                                                <td>{{$class_subjects_reports['program_'.$program['info']->id][$i->format("Y/m/d")]->attendace->where('attendance' , "=" , "late")->count() }}
                                                                </td>
                                                                <td>{{$class_subjects_reports['program_'.$program['info']->id][$i->format("Y/m/d")]->attendace->where('attendance' , "=" , "excused")->count() }}
                                                                </td>
                                                                <td>{{$class_subjects_reports['program_'.$program['info']->id][$i->format("Y/m/d")]->attendace->where('attendance' , "=" , "absent")->count() }}
                                                                </td>
                                                                <td>
                                                                    @if($class_subjects_reports['program_'.$program['info']->id][$i->format("Y/m/d")]->status
                                                                    != 'completed' && auth()->user()->can('add class_report'))
                                                                    Not Complete <a
                                                                        href="{{ url('workplace/education/classes/'.$class->id.'/reports/'.$class_subjects_reports['program_'.$program['info']->id][$i->format("Y/m/d")]->id.'/prepare') }}"
                                                                        class="btn btn-sm btn-info hidden-print">Complete
                                                                        the Report</a>
                                                                    @else
                                                                    <span class="label label-success">Completed</span>
                                                                    <a href="{{ url('workplace/education/classes/'.$class->id.'/reports/'.$class_subjects_reports['program_'.$program['info']->id][$i->format("Y/m/d")]->id.'/prepare') }}"
                                                                        class="btn btn-xs btn-success pull-right">View
                                                                        Report</a>
                                                                    @endif
                                                                </td>
                                                                @elseif($teacher && $teacher_timetable &&
                                                                $teacher_timetable->{strtolower($i->format('D'))} ==
                                                                null)
                                                                <td colspan="5" class="text-center">
                                                                    NO CLASS
                                                                </td>
                                                                @else
                                                                <td colspan="5" class="text-center">
                                                                    @if((auth()->user()->id ==
                                                                    $program['teacher']->employee_id ||
                                                                    auth()->user()->can('add class_report') ) &&
                                                                    $teacher_timetable)
                                                                    {{-- &&  $i->lte($program['next_report_date'] )--}}
                                                                    {!! Form::open(['route' => 'class.reports.store' ])
                                                                    !!}

                                                                    {!! Form::hidden('class_id' , $class->id) !!}
                                                                    {!! Form::hidden('employee_id' , auth()->user()->id )
                                                                    !!}
                                                                    {!! Form::hidden('subject_id' ,
                                                                    'p'.$program['info']->id ) !!}
                                                                    {!! Form::hidden('class_time' , $i->format('Y-m-d').' '.$teacher_timetable->{strtolower($i->format('D'))}
                                                                    ) !!}
                                                                    <div class="text-center">
                                                                        <button type="submit" class="btn btn-success"><i
                                                                                class="fa fa-plus"
                                                                                aria-hidden="true"></i> Add
                                                                            Report</button>
                                                                    </div>
                                                                    {!! Form::close() !!}

                                                                    {{-- <a href="{{ url('/workplace/education/classes/'.$class->id.'/reports/create') }}"
                                                                    class="btn btn-success btn-sm" title="Add New
                                                                    Report">
                                                                    <i class="fa fa-plus" aria-hidden="true"></i> Add
                                                                    Report
                                                                    </a> --}}
                                                                    @else
                                                                    Waiting for submitting the previous report [ <a
                                                                        href="{{ url('workplace/education/classes/'.$class->id.'/reports?from_date='.$program['next_report_date']->format("Y-m")) }}">{{$program['next_report_date']->format("D d/m/Y")}}
                                                                    </a>]
                                                                    {{-- No Report , Submit Pervious Report  --}}
                                                                    @endif
                                                                </td>
                                                                @endif
                                                        </tr>
                                                        @endfor
                                            </tbody>
                                        </table>
                                    </div>
                                    @elseif ($program['type'] == 'subjects' && isset($program['class_subjects']))
                                    @foreach($program['class_subjects'] as $subject)
                                    <h3>{{$subject->title}}</h3>
                                    <div class="clearfix">
                                        <a href="{{ url('workplace/education/classes/'.$class->id.'/reports?from_date='.$program['next_report_date']->format("Y-m")) }}"
                                            class="btn btn-danger btn-sm pull-right">Add
                                            {{$program['next_report_date']->format("D d/m/Y")}} Report</a>
                                    </div>
                                    <?php $teacher_timetable = $class->teachers->where('employee_id', auth()->user()->id)->first()->subjects->where('subject_id',$subject->id)->first()->timetable; ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead class="text-center">
                                                <tr>
                                                    <td rowspan="2">Day & Date</td>
                                                    <td colspan="4">Report</td>
                                                    <td rowspan="2">Report Status</td>
                                                </tr>
                                                <tr>
                                                    <td>On-Time</td>
                                                    <td>Atteneded-Late</td>
                                                    <td>Absent with reason</td>
                                                    <td>Absent</td>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @for($i = $from_date->copy(); $i < $from_date->copy()->addMonth() && $i
                                                    <= date('Y-m-d 23:00:00'); $i->addDay())
                                                        <tr>
                                                            <td>{{ $i->format('Y/m/d D') }}</td>
                                                            @if($class->studentsAtDate($i)->count() < 1) <td colspan="5"
                                                                class="text-center">
                                                                There is no student registered in the class at this date
                                                                </td>
                                                                @elseif(
                                                                isset($class_subjects_reports['subject_'.$subject->id])
                                                                &&
                                                                isset($class_subjects_reports['subject_'.$subject->id][$i->format("Y/m/d")]))
                                                                <td>{{$class_subjects_reports['subject_'.$subject->id][$i->format("Y/m/d")]->attendace->where('attendance' , "=" , "on_time")->count() }}
                                                                </td>
                                                                <td>{{$class_subjects_reports['subject_'.$subject->id][$i->format("Y/m/d")]->attendace->where('attendance' , "=" , "late")->count() }}
                                                                </td>
                                                                <td>{{$class_subjects_reports['subject_'.$subject->id][$i->format("Y/m/d")]->attendace->where('attendance' , "=" , "excused")->count() }}
                                                                </td>
                                                                <td>{{$class_subjects_reports['subject_'.$subject->id][$i->format("Y/m/d")]->attendace->where('attendance' , "=" , "absent")->count() }}
                                                                </td>
                                                                <td>
                                                                    @if($class_subjects_reports['subject_'.$subject->id][$i->format("Y/m/d")]->status
                                                                    != 'completed' && auth()->user()->can('add
                                                                    class_report'))
                                                                    Not Complete <a
                                                                        href="{{ url('workplace/education/classes/'.$class->id.'/reports/'.$class_subjects_reports['subject_'.$subject->id][$i->format("Y/m/d")]->id.'/prepare') }}"
                                                                        class="btn btn-sm btn-info hidden-print">Complete
                                                                        the Report</a>
                                                                    @else
                                                                    <span class="label label-success">Completed</span>
                                                                    <a href="{{ url('workplace/education/classes/'.$class->id.'/reports/'.$class_subjects_reports['subject_'.$subject->id][$i->format("Y/m/d")]->id.'/prepare') }}"
                                                                        class="btn btn-xs btn-success pull-right">View
                                                                        Report</a>
                                                                    @endif
                                                                </td>
                                                                @elseif($teacher_timetable &&
                                                                $teacher_timetable->{strtolower($i->format('D'))} ==
                                                                null)
                                                                <td colspan="5" class="text-center">
                                                                    NO CLASS
                                                                </td>
                                                                @else
                                                                <td colspan="5" class="text-center">
                                                                    @if(auth()->user()->id ==
                                                                    $subject['teacher']->employee_id ||
                                                                    auth()->user()->can('add class_report'))
                                                                    {!! Form::open(['route' => 'class.reports.store' ])
                                                                    !!}

                                                                    {!! Form::hidden('class_id' , $class->id) !!}
                                                                    {!! Form::hidden('employee_id' , auth()->user()->id )
                                                                    !!}
                                                                    {!! Form::hidden('subject_id' , $subject->id ) !!}
                                                                    {!! Form::hidden('class_time' ,$i->format('Y-m-d').' '.$teacher_timetable->{strtolower($i->format('D'))}
                                                                    ) !!}
                                                                    @if(auth()->user()->can('add class_report'))
                                                                    <div class="text-center">
                                                                        <button type="submit" class="btn btn-success"><i
                                                                                class="fa fa-plus"
                                                                                aria-hidden="true"></i> Add
                                                                            Report</button>
                                                                    </div>
                                                                    @endif
                                                                    {!! Form::close() !!}
                                                                    @else
                                                                    Waiting for submitting the previous report [ <a
                                                                        href="{{ url('workplace/education/classes/'.$class->id.'/reports?from_date='.$program['next_report_date']->format("Y-m")) }}">{{$program['next_report_date']->format("D d/m/Y")}}
                                                                    </a>]
                                                                    {{-- No Report --}}
                                                                    @endif
                                                                </td>
                                                                @endif
                                                        </tr>
                                                        @endfor
                                            </tbody>
                                        </table>
                                    </div>
                                    @endforeach
                                    @endif
                                    @endforeach
                                    {{-- @for($i = $from_date->copy(); $i < $from_date->copy()->addMonth() && $i <= date('Y-m-d 23:00:00'); $i->addDay())
                                                    <div class="col-md-3 col-sm-4">
                                                        <div class="report-item mb-10">
                                                            <h6 class="text-center label-success">
                                                                {{$i->format('Y/m/d D')}}
                                    </h6>
                                    <div class="pa-10">
                                        @if($i->format('Y-m-d') == date('Y-m-d'))
                                        <a href="{{ url('/workplace/education/classes/'.$class->id.'/reports/create') }}"
                                            class="btn btn-success btn-sm" title="Add New Report">
                                            <i class="fa fa-plus" aria-hidden="true"></i> Add Report
                                        </a>
                                        @else
                                        Attendance : 5/7 <br>
                                        Average Evaluation : Good
                                        <a href="{{ url('/workplace/education/classes/'.$class->id.'/reports/view/'.$i->format('Y-m-d')) }}"
                                            class="btn btn-info btn-sm" title="View Report">
                                            <i class="fa fa-eye" aria-hidden="true"></i> View Report
                                        </a>
                                        @endif
                                    </div>

                                </div>
                            </div>

                            @endfor --}}

                        </div>

                    </div>
                    <div id="attendance_chart" class="tab-pane fade" role="tabpanel">
                        <div id="morris_extra_bar_chart" class="" style="height:500px"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>
@endsection
@section('css')
<style>
    .report-item {
        border: 1px solid #f1f1f1;
        height: 150px;
    }
</style>
<link href="{{ asset('/assets/workplace/hound/vendors/morris.js/morris.css') }}" rel="stylesheet" type="text/css" />
@endsection
@section('js')

<script src="{{ asset('/assets/workplace/hound/vendors/raphael/raphael.min.js') }}"></script>
<script src="{{ asset('/assets/workplace/hound/vendors/morris.js/morris.min.js') }}"></script>

<script>
    $('.nav-tabs a').click(function () {  
        $('#morris_extra_bar_chart').html('');
        setTimeout(function () {  
        Morris.Bar({
                element: 'morris_extra_bar_chart',
                data: [
                    @foreach($report_summery as $report)
                        {
                            @foreach($report as $key => $element)
                            '{{$key}}' : '{{$element}}',
                            @endforeach
                        },
                    @endforeach
                ],
                xkey: 'y',
                ykeys: [@foreach($class_subjects as $s) '{{$s}}', @endforeach ] ,
                labels: [@foreach($class_subjects as $s) '{{$s}}', @endforeach ] ,
                barColors:['#2879ff', '#e91e63', '#fec107'],
                hideHover: 'auto',
                gridLineColor: '#878787',
                resize: true,
                gridTextColor:'#878787',
                gridTextFamily:"Roboto",
                stacked:true,
                hideHover:false
            });
        }, 500);
    });
</script>

@endsection
<?php

namespace Modules\JobSeeker\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Repositories\JobRepository;
use Carbon\Carbon;
use Modules\JobSeeker\Services\FacebookNotifier;
use App\Services\EmailService;
use Illuminate\Support\Facades\DB;
use Mo<PERSON>les\JobSeeker\Entities\JobSeeker;
use Mo<PERSON>les\JobSeeker\Entities\JobCategory;
use Illuminate\Support\Facades\Mail;
use Modules\JobSeeker\Entities\JobSeekerSetting;
use Illuminate\Support\Facades\Cache;
use Modules\JobSeeker\Entities\JobNotificationSetup;
use Illuminate\Database\Eloquent\Collection;
use App\Mail\AlertJobsMail;
use Modules\JobSeeker\Entities\JobNotificationSentJob;
use Modules\JobSeeker\Jobs\ProcessJobNotificationSetupJob;
use Illuminate\Support\Str;
use GuzzleHttp\Client;
use Modules\JobSeeker\Entities\Job;
use Illuminate\Support\Facades\App;
use Kreait\Firebase\Messaging\Notification;
use Kreait\Firebase\Messaging\CloudMessage;
use Modules\JobSeeker\Notifications\JobAlertFcmNotification;
use Illuminate\Support\Facades\Notification as LaravelNotification;

class JobService
{
    /**
     * @var JobRepository
     */
    protected $jobRepository;

    /**
     * @var FacebookNotifier
     */
    protected $notifier;

    // Category priority for sorting (lower number = higher priority)
    protected $categoryPriority = [
        'IT - Software' => 1,
        'Software engineering' => 1,
        'software development' => 1, 
        'software development ' => 1,
        'Information Technology' => 1,
        'Leadership' => 2,
        'Management' => 3
    ];

    protected $emailService;

    /**
     * Static cache for active canonical categories
     * @var array<string, JobCategory>|null
     */
    private static $canonicalCategoriesCache = null;

    /**
     * Last time the cache was refreshed
     * @var int|null
     */
    private static $lastCacheRefresh = null;

    /**
     * Cache TTL in seconds (5 minutes)
     * @var int
     */
    private const CACHE_TTL = 300;

    /**
     * Flush the static category cache
     * This should be called when categories are modified
     *
     * @return void
     */
    public static function flushCategoryCache(): void
    {
        self::$canonicalCategoriesCache = null;
        self::$lastCacheRefresh = null;
        Log::info('JobService: Category cache flushed');
    }

    /**
     * Check if the cache needs refreshing based on TTL
     *
     * @return bool
     */
    private function shouldRefreshCache(): bool
    {
        if (self::$canonicalCategoriesCache === null || self::$lastCacheRefresh === null) {
            return true;
        }

        return (time() - self::$lastCacheRefresh) > self::CACHE_TTL;
    }

    /**
     * JobService constructor.
     *
     * @param JobRepository $jobRepository
     * @param EmailService $emailService
     * @param FacebookNotifier|null $notifier
     */
    public function __construct(
        JobRepository $jobRepository,
        EmailService $emailService,
        ?FacebookNotifier $notifier = null
    ) {
        $this->jobRepository = $jobRepository;
        $this->emailService = $emailService;
        $this->notifier = $notifier;

        // Load categories cache on instantiation
        $this->loadCanonicalCategoriesCache();
    }





    /**
     * Check if a string is primarily in English
     * 
     * @param string $text
     * @return bool
     */
    protected function isEnglishText($text)
    {
        if (empty($text)) {
            return false;
        }
        
        // Pattern for detecting Persian/Pashto/Arabic characters
        $rtlPattern = '/[\x{0600}-\x{06FF}\x{0750}-\x{077F}\x{08A0}-\x{08FF}\x{FB50}-\x{FDFF}\x{FE70}-\x{FEFF}\x{10E60}-\x{10E7F}]/u';
        
        // If RTL characters are found, return false
        if (preg_match($rtlPattern, $text)) {
            Log::debug('Non-English text detected', ['text' => $text]);
            return false;
        }
        
        // Check if the string contains mostly Latin characters
        $latinPattern = '/[A-Za-z]/';
        if (preg_match($latinPattern, $text)) {
            return true;
        }
        
        return false;
    }



    /**
     * Check if job data has significant changes
     *
     * @param object $existingJob
     * @param array $newJobData
     * @return bool
     */
    protected function hasJobChanged($existingJob, $newJobData)
    {
        // Important fields to compare for changes
        $compareFields = [
            'position',
            'number_of_vacancy',
            'vacancy_number',
            'is_featured',
            'locations',
            'contract_type',
            'work_type',
            'gender',
            'company_name',
            'expire_date',
            'salary',
            'can_apply_online'
        ];
        
        foreach ($compareFields as $field) {
            if (isset($newJobData[$field]) && $existingJob->$field != $newJobData[$field]) {
                Log::info("Job {$existingJob->slug} changed: field {$field} from '{$existingJob->$field}' to '{$newJobData[$field]}'");
                return true;
            }
        }
        
        return false;
    }

    /**
     * Load or refresh the canonical categories cache
     * 
     * @return void
     */
    private function loadCanonicalCategoriesCache(): void
    {
        try {
            // Check if we need to refresh the cache
            if ($this->shouldRefreshCache()) {
                self::$canonicalCategoriesCache = JobCategory::where('is_active', true)
                    ->where('is_canonical', true)
                    ->get()
                    ->keyBy('name')
                    ->all();

                self::$lastCacheRefresh = time();

                Log::debug('JobService: Loaded canonical categories cache', [
                    'count' => count(self::$canonicalCategoriesCache),
                    'timestamp' => self::$lastCacheRefresh
                ]);
            }
        } catch (\Exception $e) {
            Log::error('JobService: Failed to load canonical categories cache', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // Initialize empty cache to prevent repeated failed attempts
            self::$canonicalCategoriesCache = [];
            self::$lastCacheRefresh = time();
        }
    }

    /**
     * Determine job categories based on position and predefined mappings
     *
     * @param string $position
     * @return array
     */
    protected function determineJobCategories($position)
    {
        $position = strtolower($position);
        $categories = [];

        try {
            // Ensure cache is loaded
            $this->loadCanonicalCategoriesCache();

            // Map position keywords to canonical category IDs
            $categoryMappings = [
                // IT and Software related categories
                'Information Technology' => ['information technology', 'computer science', 'network engineer', 'helpdesk specialist', 'technical support'],
                'IT - Software' => ['developer', 'engineer', 'software', 'programming', 'web development', 'database', 'system architect'],
                'IT - Hardware' => ['hardware specialist', 'network administrator', 'system administrator', 'server engineer'],
                
                // Management and Leadership categories
                'Management' => ['manager', 'management', 'supervisor', 'coordinator', 'project manager'],
                'Leadership' => ['lead', 'chief', 'head of', 'director', 'ceo', 'cto', 'cfo', 'executive'],
                
                // Other common categories
                'Marketing' => ['marketing', 'social media', 'seo specialist', 'content strategist', 'digital marketing'],
                'Human Resources' => ['human resources', 'recruitment', 'talent acquisition', 'hiring manager', 'hr specialist'],
                'Administration' => ['administration', 'office manager', 'executive secretary', 'administrative assistant', 'office coordinator'],
                'Finance' => ['finance', 'accounting', 'accountant', 'financial analyst', 'budget specialist'],
                'Engineering' => ['engineering', 'civil engineer', 'electrical engineer', 'mechanical engineer'],
                'Health' => ['health', 'medical', 'doctor', 'nurse', 'physician', 'healthcare'],
                'Education' => ['education', 'teacher', 'instructor', 'professor', 'trainer', 'curriculum'],
                'Legal' => ['legal', 'lawyer', 'attorney', 'legal counsel', 'compliance officer'],
                'Research/Survey' => ['research', 'survey', 'data analyst', 'scientist', 'researcher', 'data scientist']
            ];

            // Convert all keywords to lowercase once
            $categoryMappings = array_map(function($keywords) {
                return array_map('mb_strtolower', $keywords);
            }, $categoryMappings);

            // Convert position to lowercase once for comparison
            $position = mb_strtolower($position);

            foreach ($categoryMappings as $categoryName => $keywords) {
                if (isset(self::$canonicalCategoriesCache[$categoryName])) {
                    foreach ($keywords as $keyword) {
                        if (str_contains($position, $keyword)) {
                            $categories[] = self::$canonicalCategoriesCache[$categoryName]->id;
                            break; // Break once we've matched a keyword for this category
                        }
                    }
                }
            }

            // If no categories matched, default to Information Technology if position contains relevant keywords
            if (empty($categories) && isset(self::$canonicalCategoriesCache['Information Technology'])) {
                $techKeywords = ['software', 'developer', 'programming', 'web', 'database', 'system', 'network'];
                foreach ($techKeywords as $keyword) {
                    if (str_contains($position, $keyword)) {
                        $categories[] = self::$canonicalCategoriesCache['Information Technology']->id;
                        Log::info("Matched IT category based on tech keyword: {$keyword}", [
                            'position' => $position
                        ]);
                        break;
                    }
                }
            }

            // Remove duplicates
            $categories = array_unique($categories);

            Log::debug('JobService: Determined categories for position', [
                'position' => $position,
                'categories' => $categories
            ]);

            return $categories;

        } catch (\Exception $e) {
            Log::error('JobService: Error determining job categories', [
                'position' => $position,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback to empty array if something goes wrong
            return [];
        }
    }
    
    /**
     * Format job data for database storage
     *
     * @param array $jobData
     * @return array
     */
    protected function formatJobData($jobData)
    {
        // Get the job position and determine categories
        $position = $jobData['position'] ?? '';
        $categories = $this->determineJobCategories($position);
        
        // Source is jobs.af for all jobs processed by this service
        $source = 'jobs.af';
        
        return [
            'position' => $position,
            'number_of_vacancy' => $jobData['number_of_vacancy'] ?? null,
            'vacancy_number' => $jobData['vacancy_number'] ?? null,
            'is_featured' => $jobData['is_featured'] ?? false,
            'locations' => $jobData['locations'] ?? '',
            'contract_type' => $jobData['contract_type'] ?? '',
            'work_type' => $jobData['work_type'] ?? '',
            'gender' => $jobData['gender'] ?? '',
            'company_name' => $jobData['company']['name'] ?? '',
            'publish_date' => $jobData['publish_date'] ?? now(),
            'expire_date' => $jobData['expire_date'] ?? null,
            'salary' => $jobData['salary'] ?? '',
            'can_apply_online' => $jobData['can_apply_online'] ?? false,
            'slug' => $jobData['slug'] ?? Str::slug($position . '-' . ($jobData['company']['name'] ?? 'company')),
            'categories' => $categories, // Add categories to the formatted data
            'source' => $source, // Add source information
            'source_id' => $jobData['id'] ?? null, // Add source ID if available
        ];
    }
    
    /**
     * Parse date string from API to database format
     *
     * @param string|null $dateString
     * @return string|null
     */
    protected function parseDate($dateString)
    {
        if (empty($dateString)) {
            return null;
        }
        
        try {
            return Carbon::parse($dateString)->format('Y-m-d');
        } catch (Exception $e) {
            Log::warning("Failed to parse date: {$dateString} - " . $e->getMessage());
            return null;
        }
    }

    /**
     * Convert a Job model to an array
     * 
     * @param mixed $job
     * @return array
     */
    protected function jobToArray($job)
    {
        // If already an array, return it
        if (is_array($job)) {
            return $job;
        }
        
        // Convert stdClass to array
        if (is_object($job) && get_class($job) === 'stdClass') {
            return [
                'id' => $job->id ?? null,
                'title' => $job->position ?? $job->title ?? null,
                'company' => $job->company ?? null,
                'location' => $job->location ?? $job->locations ?? null,
                'url' => $job->url ?? null,
                'publish_date' => $job->publish_date ?? null,
                'expires_at' => $job->expires_at ?? null,
                'job_type' => $job->job_type ?? null,
                'created_at' => $job->created_at ?? null,
                'updated_at' => $job->updated_at ?? null,
                'deadline' => $job->deadline ?? null,
                'description' => $job->description ?? null,
                'salary' => $job->salary ?? null,
                'original_id' => $job->original_id ?? null,
                'slug' => $job->slug ?? null,
                'source' => $job->source ?? null,
                'is_active' => $job->is_active ?? true,
                'categories' => $job->categories ?? []
            ];
        }
if (is_object($job) && $job instanceof \Modules\JobSeeker\Entities\Job) {
     return [
         'id' => $job->id,
         'title' => $job->position,
         'company' => $job->company,
         'location' => $job->locations,
         'url' => $job->url,
         'publish_date' => $job->publish_date,
         'expires_at' => $job->expires_at,
         'job_type' => $job->job_type,
         'created_at' => $job->created_at,
         'updated_at' => $job->updated_at,
         'deadline' => $job->deadline,
         'description' => $job->description,
         'salary' => $job->salary,
         'experience' => $job->experience,
         'original_id' => $job->original_id,
         'slug' => $job->slug,
         'source' => $job->source,
        'is_active' => $job->is_active,
        'categories' => $job->categories ? $job->categories->pluck('id')->toArray() : []
     ];
 }
        
        // Return empty array if input is invalid
        return [];
    }
    
    /**
     * Send batch notifications for new and updated jobs
     *
     * @param array $newJobs
     * @param array $updatedJobs
     * @param array $missedJobs
     * @return bool
     */
    protected function sendBatchNotifications($newJobs, $updatedJobs, $missedJobs = [])
    {
        if (empty($newJobs) && empty($updatedJobs) && empty($missedJobs)) {
            Log::info("No jobs to notify about");
            return true;
        }

        try {
            $messages = [];
            
            // Format new jobs notification
            if (!empty($newJobs)) {
                $message = "🆕 *New Jobs Posted*\n\n";
                foreach ($newJobs as $job) {
                    $message .= "• {$job['position']} at {$job['company']['name']}\n";
                    $message .= "  Posted: {$this->getTimeAgo($job['publishDate'])}\n";
                    $message .= "  Location: {$job['locations']}\n\n";
                }
                $messages[] = $message;
            }
            
            // Format updated jobs notification
            if (!empty($updatedJobs)) {
                $message = "🔄 *Recently Updated Jobs*\n\n";
                foreach ($updatedJobs as $job) {
                    $message .= "• {$job['position']} at {$job['company']['name']}\n";
                    $message .= "  Updated: {$this->getTimeAgo($job['updated_at'])}\n";
                    $message .= "  Location: {$job['locations']}\n\n";
                }
                $messages[] = $message;
            }
            
            // Format missed notifications
            if (!empty($missedJobs)) {
                $message = "⚠️ *Jobs You May Have Missed*\n\n";
                foreach ($missedJobs as $job) {
                    $message .= "• {$job['position']} at {$job['company']['name']}\n";
                    $message .= "  Posted: {$this->getTimeAgo($job['publishDate'])}\n";
                    $message .= "  Location: {$job['locations']}\n\n";
                }
                $messages[] = $message;
            }
            
            // Send each message
            foreach ($messages as $message) {
                $buttons = [
                    ['title' => 'View All Jobs', 'url' => 'https://jobs.af']
                ];
                
                $result = $this->notifier->sendMessageWithButtons($message, $buttons);
                
                if (!$result) {
                    Log::error("Failed to send job notifications batch");
                    return false;
                }
            }
            
            Log::info("Successfully sent all job notification batches");
            return true;
            
        } catch (Exception $e) {
            Log::error("Exception while sending job notifications: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * Get relative time string (e.g., "2 hours ago", "1 day ago")
     *
     * @param string $dateString
     * @return string
     */
    protected function getTimeAgo($dateString)
    {
        try {
            $date = Carbon::parse($dateString);
            $now = Carbon::now();
            
            if ($date->diffInMinutes($now) < 60) {
                return $date->diffInMinutes($now) . ' minutes ago';
            } elseif ($date->diffInHours($now) < 24) {
                return $date->diffInHours($now) . ' hours ago';
            } else {
                return $date->diffInDays($now) . ' days ago';
            }
        } catch (Exception $e) {
            Log::warning("Failed to parse date for time ago: {$dateString} - " . $e->getMessage());
            return 'recently';
        }
    }




    
    /**
     * Send a test email to verify email system configuration
     * 
     * @return bool
     */
    protected function sendTestEmail()
    {
        try {
            // Instead of using env variables, just check if we have any active subscribers
            $subscriber = JobSeeker::where('is_active', true)->first();
            
            if (!$subscriber) {
                Log::error('Cannot send test email: No active subscribers found in the database');
                return false;
            }
            
            // Use first active subscriber for test
            $to = [
                'email' => $subscriber->email,
                'name' => $subscriber->name ?? $subscriber->email
            ];
            
            // Use fixed service email as sender
            $from = [
                'email' => '<EMAIL>',
                'name' => 'Jobs System Test'
            ];
            
            $result = $this->emailService->sendEmail(
                $to,
                'Test Email from Jobs Notification System',
                'emails.test',
                [
                    'name' => $to['name'],
                    'time' => Carbon::now()->format('Y-m-d H:i:s')
                ],
                []
            );
            
            return $result['success'] ?? false;
        } catch (\Exception $e) {
            Log::error('Error sending test email', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Format job data for email template
     * 
     * @param array $jobs
     * @return array
     */
    protected function formatJobsForEmail(array $jobs)
    {
        $formattedJobs = [];
        foreach ($jobs as $job) {
            // Handle different possible data structures 
            $position = $job['position'] ?? $job['title'] ?? '';
            $companyName = $job['company_name'] ?? ($job['company']['name'] ?? ($job['company'] ?? ''));
            $locations = $job['locations'] ?? $job['location'] ?? '';
            $contractType = $job['contract_type'] ?? $job['contractType'] ?? $job['type'] ?? 'Full-time';
            $workType = $job['work_type'] ?? $job['workType'] ?? 'On-site';
            $publishDate = $job['publish_date'] ?? $job['publishDate'] ?? $job['published_at'] ?? now()->subDays(3)->format('Y-m-d H:i:s');
            
            // Determine job category priority based on position or category
            $priority = 10; // Default low priority
            $position = $position ?: 'Unknown Position'; // Ensure position is never empty
            $positionLower = strtolower($position);
            $category = $job['category'] ?? '';
            
            // Check position for keywords first
            if (strpos($positionLower, 'developer') !== false || 
                strpos($positionLower, 'engineer') !== false || 
                strpos($positionLower, 'software') !== false || 
                strpos($positionLower, 'it') !== false) {
                $priority = 1; // IT jobs
            } elseif (strpos($positionLower, 'lead') !== false || 
                     strpos($positionLower, 'chief') !== false || 
                     strpos($positionLower, 'head') !== false || 
                     strpos($positionLower, 'director') !== false) {
                $priority = 2; // Leadership jobs
            } elseif (strpos($positionLower, 'manager') !== false || 
                     strpos($positionLower, 'management') !== false) {
                $priority = 3; // Management jobs
            }
            
            // If category is set, use the priority from categoryPriority array
            if (!empty($category) && isset($this->categoryPriority[$category])) {
                $priority = $this->categoryPriority[$category];
            }
            
            // Ensure we have a salary value - set to "Negotiable" if not provided
            $salary = !empty($job['salary']) ? $job['salary'] : 'As per company salary scale';
            
            $formattedJobs[] = [
                'position' => $position,
                'company_name' => $companyName ?: 'Unknown Company',
                'locations' => $locations ?: 'Kabul, Afghanistan',
                'contract_type' => $contractType,
                'work_type' => $workType,
                'publish_date' => $publishDate,
                'salary' => $salary,
                'experience' => $job['experience'] ?? null,
                'slug' => $job['slug'] ?? '',
                'updated_at' => $job['updated_at'] ?? now()->format('Y-m-d H:i:s'),
                'priority' => $priority // Add priority for sorting
            ];
            
            // Log formatted job data to verify it's complete
            Log::debug('Formatted job for email', [
                'original_position' => $job['position'] ?? 'not set',
                'formatted_position' => $position,
                'original_locations' => $job['locations'] ?? 'not set',
                'formatted_locations' => $locations ?: 'Kabul, Afghanistan',
                'original_contract_type' => $job['contract_type'] ?? 'not set',
                'formatted_contract_type' => $contractType,
            ]);
        }
        
        // Sort formatted jobs by priority (lower number first)
        usort($formattedJobs, function($a, $b) {
            return $a['priority'] <=> $b['priority'];
        });
        
        return $formattedJobs;
    }


    


    /**
     * Notify job seekers about new jobs matching their preferences
     * This method now uses queues and per-recipient processing for maximum scalability
     * 
     * @return array
     */
    public function notifyJobSeekers()
    {
        Log::info('notifyJobSeekers: Method started - Queued with per-recipient processing');
        $stats = [
            'total_job_seekers' => 0, // Changed from total_subscribers
            'setups_found' => 0,
            'setup_jobs_dispatched' => 0,
            'skipped' => 0,
            'errors' => 0,
            'chunks_processed' => 0,
        ];

        try {
            // Process job seekers in chunks to avoid memory issues
            $chunkSize = 100; // Process 100 job seekers at a time
            $jobSeekerCount = JobSeeker::where('is_active', true)->count(); // Changed from subscriberCount
            $stats['total_job_seekers'] = $jobSeekerCount; // Changed from total_subscribers

            Log::info("Processing job seekers in chunks", [ // Changed from subscribers
                'total_job_seekers' => $jobSeekerCount, // Changed from total_subscribers
                'chunk_size' => $chunkSize
            ]);

            // Use chunking for better memory handling with large numbers of job seekers
            JobSeeker::where('is_active', true)
                ->chunk($chunkSize, function ($jobSeekers) use (&$stats) { // Changed from subscribers
                    $stats['chunks_processed']++;
                    
                    Log::debug("Processing job seeker chunk", [ // Changed from subscriber
                        'chunk_number' => $stats['chunks_processed'],
                        'chunk_size' => count($jobSeekers) // Changed from subscribers
                    ]);

                    // Process each job seeker in this chunk
                    foreach ($jobSeekers as $jobSeeker) { // Changed from subscribers
                        try {
                            // Get this job seeker's notification setups
                            $setups = JobNotificationSetup::with(['categories'])
                                ->where('job_seeker_id', $jobSeeker->id) // Changed from subscriber->id
                                ->where('is_active', true)
                                ->get();

                            $stats['setups_found'] += $setups->count();

                            if ($setups->isEmpty()) {
                                $stats['skipped']++;
                                continue;
                            }
                            // Process each setup for this job seeker
                            foreach ($setups as $setup) {
                                // Process setup notification; returns true if a send attempt was made
                                $attempted = $this->processSetupNotification($setup);
                                if ($attempted) {
                                    $stats['setup_jobs_dispatched']++;
                                } else {
                                    $stats['skipped']++;
                                }
                                Log::debug("Processed setup notification (service-driven)", [
                                    'job_seeker_id' => $jobSeeker->id,
                                    'setup_id' => $setup->id,
                                    'setup_name' => $setup->name,
                                    'attempted' => $attempted
                                ]);
                            }
                        } catch (\Exception $e) {
                            $stats['errors']++;
                            Log::error("Error processing job seeker in chunk", [ // Changed from subscriber
                                'job_seeker_id' => $jobSeeker->id ?? 'unknown', // Changed from subscriber->id
                                'error' => $e->getMessage()
                            ]);
                            // Continue processing other job seekers
                        }
                    }
                });

            Log::info("notifyJobSeekers: Method completed successfully", $stats); // Changed from notifySubscribers
            return $stats;
        } catch (\Exception $e) {
            Log::error("notifyJobSeekers: Fatal error processing job seekers", [ // Changed from notifySubscribers
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'stats' => $stats
            ]);
            throw $e;
        }
    }

    /**
     * Get recent job counts per category.
     *
     * @param int $days Number of days to look back (default 5)
     * @param bool $includeRecentness Whether to include recentness data (today, this week)
     * @return array Associative array of [categoryId => count] or detailed structure with recentness
     */


    /**
     * Send an initial notification for a newly created job setup.
     * This sends jobs from the last 5 days matching the setup's categories.
     *
     * @param JobNotificationSetup $setup
     * @return void
     */
    public function sendInitialNotificationForSetup(JobNotificationSetup $setup): void
    {
        // Global pause for JobSeeker job alerts (emails with jobs)
        try {
            $isPaused = \Modules\JobSeeker\Entities\JobSeekerSetting::getValue('job_alerts_global_pause', 'false');
            if ($isPaused === 'true' || $isPaused === true) {
                Log::warning('JobService: Skipping initial notification due to global Job Alerts pause', [
                    'setup_id' => $setup->id,
                    'job_seeker_id' => $setup->job_seeker_id,
                ]);
                return;
            }
        } catch (\Throwable $e) {
            Log::error('JobService: Error checking job_alerts_global_pause', ['error' => $e->getMessage()]);
        }
        Log::info("Service: Attempting to send initial notification for setup.", [
            'setup_id' => $setup->id,
            'setup_name' => $setup->name
        ]);

        try {
            $categoryIds = $setup->categories->pluck('id')->toArray();
            if (empty($categoryIds)) {
                Log::info("Service: No categories found for setup ID {$setup->id}. Skipping initial notification.");
                return;
            }

            // Fetch jobs from the last 5 days for these categories
            $initialJobs = $this->jobRepository->getRecentJobsByCategories($categoryIds, 5);

            if ($initialJobs->isEmpty()) {
                Log::info("Service: No initial jobs found for categories in setup ID {$setup->id}. Skipping initial notification.", ['categories' => $categoryIds]);
                return;
            }

            Log::info("Service: Found " . $initialJobs->count() . " initial jobs for setup ID {$setup->id}.", ['job_ids' => $initialJobs->pluck('id')->toArray()]);

            $recipientsCollection = $setup->recipients;
            $recipientEmails = [];

            if ($recipientsCollection->isNotEmpty()) {
                foreach ($recipientsCollection as $recipient) {
                    $recipientEmails[] = ['email' => $recipient->email, 'name' => $recipient->name ?? explode('@', $recipient->email)[0]];
                }
            } elseif ($setup->jobSeeker) {
                // Fallback to job seeker's email if no specific recipients are set for the setup
                $recipientEmails[] = ['email' => $setup->jobSeeker->email, 'name' => $setup->jobSeeker->name ?? explode('@', $setup->jobSeeker->email)[0]];
            }

            if (empty($recipientEmails)) {
                Log::warning("Service: No recipients found for setup ID {$setup->id} (neither specific nor job seeker). Cannot send initial notification.");
                return;
            }
            
            $emailSubject = "Initial Job Matches for your '" . $setup->name . "' Alert";
            $emailView = 'jobseeker::emails.jobs.jobseeker_notification'; // Reusing existing template

            $jobsArray = $initialJobs->map(function ($job) {
                return $this->jobToArray($job); // Use existing jobToArray to ensure consistent data structure
            })->toArray();
            
            $sentToCount = 0;
            foreach ($recipientEmails as $recipient) {
                try {
                    $this->emailService->sendEmail(
                        $recipient,
                        $emailSubject,
                        $emailView,
                        [
                            'jobs' => $this->formatJobsForEmail($jobsArray), // formatJobsForEmail handles sorting and structure for view
                            'jobSeeker' => (object)$recipient, // Template expects an object
                            'timeAgo' => function($date) { return $this->getTimeAgo($date); },
                            'currentDate' => Carbon::now()->format('F j, Y'),
                            'setupName' => $setup->name // Pass setup name to template if needed
                        ],
                        []
                    );
                    Log::info("Service: Successfully sent initial job notification for setup ID {$setup->id} to " . $recipient['email']);
                    $sentToCount++;
                } catch (\Exception $e) {
                    Log::error("Service: Failed to send initial job notification to recipient for setup ID {$setup->id}.", [
                        'recipient_email' => $recipient['email'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            if ($sentToCount > 0) {
                // Mark these jobs as sent for this setup to avoid immediate re-notification
                foreach ($initialJobs as $job) {
                    JobNotificationSentJob::firstOrCreate([
                        'setup_id' => $setup->id,
                        'job_id' => $job->id,
                    ], [
                        'sent_at' => now(),
                    ]);
                }
                Log::info("Service: Marked " . $initialJobs->count() . " jobs as sent for initial notification of setup ID {$setup->id}.");
                // Increment the main sent count for the setup
                $setup->incrementSentCount(); // This was existing, seems appropriate here too
            }

        } catch (\Exception $e) {
            Log::error("Service: Error during sendInitialNotificationForSetup for setup ID {$setup->id}.", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // We don't rethrow, as the main setup creation shouldn't fail due to this.
        }
    }

    /**
     * Send a notification for a specific job to a specific recipient
     * Used primarily for retrying failed notifications
     *
     * @param \Modules\JobSeeker\Entities\Job $job
     * @param JobNotificationSetup $setup
     * @param array $recipient
     * @return bool
     */
    public function sendJobNotificationToRecipient($job, JobNotificationSetup $setup, array $recipient): bool
    {
        // Global pause for JobSeeker job alerts (emails with jobs)
        try {
            $isPaused = \Modules\JobSeeker\Entities\JobSeekerSetting::getValue('job_alerts_global_pause', 'false');
            if ($isPaused === 'true' || $isPaused === true) {
                Log::warning('JobService: Skipping single job notification due to global Job Alerts pause', [
                    'job_id' => $this->isJobInstance($job) ? $job->id : ($job['id'] ?? 'unknown'),
                    'setup_id' => $setup->id,
                    'recipient' => $recipient['email'] ?? 'unknown',
                ]);
                return false;
            }
        } catch (\Throwable $e) {
            Log::error('JobService: Error checking job_alerts_global_pause', ['error' => $e->getMessage()]);
        }
        try {
            Log::info("JobService: Sending single job notification to recipient", [
                'job_id' => $job->id,
                'setup_id' => $setup->id,
                'recipient' => $recipient['email']
            ]);
            
            // Convert job to array format for email template
            $jobArray = $this->jobToArray($job);
            $formattedJobs = $this->formatJobsForEmail([$jobArray]);
            
            // Prepare email data with proper validation
            $jobTitle = trim($job->position ?? '') ?: 'Job Position';
            $companyName = trim($job->company_name ?? '') ?: 'Company';
            $emailSubject = "Job Opportunity: {$jobTitle} at {$companyName}";
            $emailView = 'jobseeker::emails.jobs.jobseeker_notification';
            
            // Send email
            $result = $this->emailService->sendEmail(
                $recipient,
                $emailSubject,
                $emailView,
                [
                    'jobs' => $formattedJobs,
                    'jobSeeker' => (object)$recipient,
                    'timeAgo' => function($date) { return $this->getTimeAgo($date); },
                    'currentDate' => Carbon::now()->format('F j, Y'),
                    'setupName' => $setup->name
                ],
                []
            );
            
            if ($result['success']) {
                // Record successful notification
                JobNotificationSentJob::firstOrCreate(
                    [
                        'setup_id' => $setup->id,
                        'job_id' => $job->id,
                        'recipient_email' => $recipient['email']
                    ],
                    [
                        'sent_at' => now()
                    ]
                );
                
                Log::info("JobService: Successfully sent job notification to recipient", [
                    'job_id' => $job->id,
                    'setup_id' => $setup->id,
                    'recipient' => $recipient['email']
                ]);
                
                return true;
            } else {
                Log::error("JobService: Failed to send job notification to recipient", [
                    'job_id' => $job->id, 
                    'setup_id' => $setup->id,
                    'recipient' => $recipient['email'],
                    'error' => $result['message'] ?? 'Unknown error'
                ]);
                
                return false;
            }
        } catch (Exception $e) {
            Log::error("JobService: Exception sending job notification to recipient", [
                'job_id' => $job->id ?? 'unknown',
                'setup_id' => $setup->id ?? 'unknown',
                'recipient' => $recipient['email'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return false;
        }
    }
    
    /**
     * Send a notification for a setup to a specific recipient with all relevant recent jobs
     * Used primarily for retrying failed notifications
     *
     * @param JobNotificationSetup $setup
     * @param array $recipient
     * @return bool
     */
    public function sendSetupNotificationToRecipient(JobNotificationSetup $setup, $recipient): bool
    {
        // Global pause for JobSeeker job alerts (emails with jobs)
        try {
            $isPaused = \Modules\JobSeeker\Entities\JobSeekerSetting::getValue('job_alerts_global_pause', 'false');
            if ($isPaused === 'true' || $isPaused === true) {
                Log::warning('JobService: Skipping setup notification due to global Job Alerts pause', [
                    'setup_id' => $setup->id,
                    'recipient' => is_array($recipient) ? ($recipient['email'] ?? 'unknown') : ($recipient->email ?? 'unknown'),
                ]);
                return false;
            }
        } catch (\Throwable $e) {
            Log::error('JobService: Error checking job_alerts_global_pause', ['error' => $e->getMessage()]);
        }
        try {
            $recipientEmail = '';
            $recipientName = '';

            if (is_array($recipient)) {
                $recipientEmail = $recipient['email'];
                $recipientName = $recipient['name'] ?? explode('@', $recipientEmail)[0];
            } elseif (is_object($recipient) && isset($recipient->email)) {
                $recipientEmail = $recipient->email;
                $recipientName = $recipient->name ?? explode('@', $recipientEmail)[0];
            } else {
                Log::error("JobService: Invalid recipient provided for setup notification", [
                    'setup_id' => $setup->id,
                    'recipient_type' => gettype($recipient)
                ]);
                return false;
            }

            Log::info("JobService: Sending setup notification to recipient", [
                'setup_id' => $setup->id,
                'recipient_email' => $recipientEmail
            ]);

            $categoryIds = $setup->categories->pluck('id')->toArray();
            if (empty($categoryIds)) {
                Log::warning("JobService: No categories found for setup", ['setup_id' => $setup->id]);
                return false;
            }

            // Get recent jobs for these categories that haven't been sent to this recipient
            $recentJobs = $this->jobRepository->getRecentJobsForNotification($categoryIds, $setup->id, $recipientEmail);

            if ($recentJobs->isEmpty()) {
                Log::info("JobService: No new jobs found for recipient notification", [
                    'setup_id' => $setup->id,
                    'recipient_email' => $recipientEmail
                ]);
                return true; // Success, nothing to send
            }

            Log::info("JobService: Found {$recentJobs->count()} new jobs for notification", [
                'setup_id' => $setup->id,
                'recipient_email' => $recipientEmail
            ]);

            // Send notifications using Laravel's notification system
            $notificationSent = false;

            // 1. Send email notification via EmailService (existing functionality)
            $emailResult = $this->sendEmailNotification($setup, $recipient, $recentJobs);
            if ($emailResult) {
                $notificationSent = true;
                Log::info("JobService: Email notification sent successfully", [
                    'setup_id' => $setup->id,
                    'recipient_email' => $recipientEmail
                ]);
            }

            // 2. Send FCM push notification if recipient is the job seeker
            if ($setup->jobSeeker && $setup->jobSeeker->email === $recipientEmail) {
                $fcmResult = $this->sendFcmNotification($setup, $recentJobs);
                if ($fcmResult) {
                    $notificationSent = true;
                    Log::info("JobService: FCM notification sent successfully", [
                        'setup_id' => $setup->id,
                        'job_seeker_id' => $setup->jobSeeker->id
                    ]);
                }
            }

            // Mark jobs as sent if any notification was successful
            if ($notificationSent) {
                foreach ($recentJobs as $job) {
                    JobNotificationSentJob::firstOrCreate(
                        [
                            'setup_id' => $setup->id,
                            'job_id' => $job->id,
                            'recipient_email' => $recipientEmail
                        ],
                        ['sent_at' => now()]
                    );
                }
                
                Log::info("JobService: Successfully sent setup notification to recipient", [
                    'setup_id' => $setup->id,
                    'recipient_email' => $recipientEmail,
                    'jobs_count' => $recentJobs->count()
                ]);
                return true;
            }

            Log::warning("JobService: No notifications were sent successfully", [
                'setup_id' => $setup->id,
                'recipient_email' => $recipientEmail
            ]);
            return false;

        } catch (\Exception $e) {
            Log::error("JobService: Exception sending setup notification to recipient", [
                'setup_id' => $setup->id ?? 'unknown',
                'recipient_email' => $recipientEmail ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Send email notification via EmailService
     *
     * @param JobNotificationSetup $setup
     * @param mixed $recipient
     * @param Collection $recentJobs
     * @return bool
     */
    private function sendEmailNotification(JobNotificationSetup $setup, $recipient, Collection $recentJobs): bool
    {
        try {
            $recipientEmail = is_array($recipient) ? $recipient['email'] : $recipient->email;
            $recipientName = is_array($recipient) ? ($recipient['name'] ?? explode('@', $recipientEmail)[0]) : ($recipient->name ?? explode('@', $recipientEmail)[0]);

            $formattedJobs = $this->formatJobsForEmail($recentJobs->all());

            $emailSubject = "Job Alerts: " . $setup->name;
            $view = 'jobseeker::emails.jobs.jobseeker_notification';
            $viewData = [
                'jobs' => $formattedJobs,
                'jobSeeker' => (object)['email' => $recipientEmail, 'name' => $recipientName],
                'setup' => $setup,
                'timeAgo' => function($date) { return $this->getTimeAgo($date); },
                'currentDate' => Carbon::now()->format('F j, Y'),
                'setupName' => $setup->name
            ];
            
            $fromDetails = [
                'email' => config('mail.from.address', '<EMAIL>'),
                'name' => config('mail.from.name', 'ITQAN Job Notifications')
            ];

            $result = $this->emailService->sendEmail(
                ['email' => $recipientEmail, 'name' => $recipientName],
                $emailSubject,
                $view,
                $viewData,
                []
            );

            return $result['success'] ?? false;

        } catch (\Exception $e) {
            Log::error("JobService: Error sending email notification", [
                'setup_id' => $setup->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send FCM push notification using Firebase SDK directly
     *
     * @param JobNotificationSetup $setup
     * @param Collection $recentJobs
     * @return bool
     */
    private function sendFcmNotification(JobNotificationSetup $setup, Collection $recentJobs): bool
    {
        try {
            $jobSeeker = $setup->jobSeeker;
            
            if (!$jobSeeker) {
                Log::warning("JobService: No job seeker found for FCM notification", [
                    'setup_id' => $setup->id
                ]);
                return false;
            }

            // Check if job seeker has push notifications enabled
            $hasActivePushSetup = $jobSeeker->notificationSetups()
                ->where('is_active', true)
                ->where('receive_push_notifications', true)
                ->exists();

            if (!$hasActivePushSetup) {
                Log::info("JobService: Job seeker does not have push notifications enabled", [
                    'setup_id' => $setup->id,
                    'job_seeker_id' => $jobSeeker->id
                ]);
                return false;
            }

            // Get FCM tokens
            $fcmTokens = $jobSeeker->routeNotificationForFcm();

            if (empty($fcmTokens)) {
                Log::info("JobService: Job seeker has no active device tokens", [
                    'setup_id' => $setup->id,
                    'job_seeker_id' => $jobSeeker->id
                ]);
                return false;
            }

            Log::info("JobService: Sending FCM notification via Firebase SDK", [
                'setup_id' => $setup->id,
                'job_seeker_id' => $jobSeeker->id,
                'device_tokens_count' => count($fcmTokens),
                'jobs_count' => $recentJobs->count()
            ]);

            // Prepare notification content
            $jobCount = $recentJobs->count();
            $firstJob = $recentJobs->first();
            $firstJobTitle = $firstJob ? ($firstJob->position ?? $firstJob->title ?? 'new jobs') : 'new jobs';
            $pluralJobs = $jobCount > 1 ? 'jobs' : 'job';

            $notificationTitle = "{$jobCount} New {$pluralJobs} Matching Your Criteria!";
            $notificationBody = $firstJobTitle . ($jobCount > 1 ? " and more. Tap to view." : ". Tap to view.");

            // Get Firebase messaging service
            $messaging = app('firebase.messaging');

            // Create notification
            $notification = Notification::create($notificationTitle, $notificationBody);

            // Prepare data payload
            $data = [
                'notification_type' => 'job_alert',
                'job_count' => (string) $jobCount,
                'setup_id' => (string) $setup->id,
                'setup_name' => $setup->name,
                'first_job_id' => $firstJob ? (string) $firstJob->id : null,
                'first_job_title' => $firstJobTitle,
                'timestamp' => now()->toISOString(),
                'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
            ];

            // Send to each token
            $successCount = 0;
            foreach ($fcmTokens as $token) {
                try {
                    $message = CloudMessage::withTarget('token', $token)
                        ->withNotification($notification)
                        ->withData($data);

                    $messaging->send($message);
                    $successCount++;

                    Log::debug("JobService: FCM notification sent to token", [
                        'setup_id' => $setup->id,
                        'job_seeker_id' => $jobSeeker->id,
                        'token_preview' => substr($token, 0, 8) . '...'
                    ]);

                } catch (\Exception $e) {
                    Log::error("JobService: Failed to send FCM notification to token", [
                        'setup_id' => $setup->id,
                        'job_seeker_id' => $jobSeeker->id,
                        'token_preview' => substr($token, 0, 8) . '...',
                        'error' => $e->getMessage()
                    ]);
                }
            }

            if ($successCount > 0) {
                Log::info("JobService: FCM notification sent successfully", [
                    'setup_id' => $setup->id,
                    'job_seeker_id' => $jobSeeker->id,
                    'success_count' => $successCount,
                    'total_tokens' => count($fcmTokens)
                ]);
                return true;
            } else {
                Log::warning("JobService: No FCM notifications were sent successfully", [
                    'setup_id' => $setup->id,
                    'job_seeker_id' => $jobSeeker->id,
                    'total_tokens' => count($fcmTokens)
                ]);
                return false;
            }

        } catch (\Exception $e) {
            Log::error("JobService: Error sending FCM notification", [
                'setup_id' => $setup->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Get recent jobs for a notification setup (wrapper method)
     * 
     * @param array $categoryIds
     * @param int $setupId
     * @param string $recipientEmail
     * @param int $days
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecentJobsForNotificationSetup(array $categoryIds, int $setupId, string $recipientEmail, int $days = 5)
    {
        return $this->jobRepository->getRecentJobsForNotification($categoryIds, $setupId, $recipientEmail, $days);
    }

    /**
     * Process notifications for a setup
     * 
     * @param JobNotificationSetup $setup
     * @return bool
     */
    public function processSetupNotification(JobNotificationSetup $setup): bool
    {
        Log::info("Processing notification setup: {$setup->id} for job seeker: {$setup->job_seeker_id}");

        // Global pause for JobSeeker job alerts (emails with jobs)
        try {
            $isPaused = \Modules\JobSeeker\Entities\JobSeekerSetting::getValue('job_alerts_global_pause', 'false');
            if ($isPaused === 'true' || $isPaused === true) {
                Log::warning('JobService: Skipping processSetupNotification due to global Job Alerts pause', [
                    'setup_id' => $setup->id,
                    'job_seeker_id' => $setup->job_seeker_id,
                ]);
                $setup->updateLastActivityCheck();
                return false; // Indicate no send attempt due to pause
            }
        } catch (\Throwable $e) {
            Log::error('JobService: Error checking job_alerts_global_pause', ['error' => $e->getMessage()]);
        }

        if (!$setup->jobSeeker) {
            Log::warning("Job seeker not found for setup ID: {$setup->id}. Skipping.");
            return false;
        }

        // Prefer provider categories when available; fall back to canonical categories
        $providerCategoryIds = $setup->providerCategories()->pluck('provider_job_categories.id')->toArray();
        $categoryIds = !empty($providerCategoryIds)
            ? $providerCategoryIds
            : $setup->categories()->pluck('job_categories.id')->toArray();

        $recentJobs = $this->jobRepository->getRecentJobsForNotification(
            $categoryIds,
            $setup->id,
            $setup->jobSeeker->email, // Assuming jobSeeker email is primary identifier here
            config('jobseeker.notifications.job_age_days', 5)
        );

        if ($recentJobs->isEmpty()) {
            Log::info("No new jobs found for setup ID: {$setup->id}. Nothing to notify.");
            $setup->updateLastActivityCheck();
            return true; // No jobs, but process was successful
        }

        Log::info("Found {$recentJobs->count()} new jobs for setup ID: {$setup->id}");

        $emailSent = false;
        $fcmSent = false;

        // Send Email Notification (delivery-mode controlled)
        if ($setup->jobSeeker->email) { // Check if job seeker has an email
            try {
                $deliveryMode = JobSeekerSetting::getValue('job_alerts_email_delivery_mode', 'queue');

                // Enforce policy: JobSeeker emails are always sent synchronously via EmailService
                $sent = $this->sendEmailNotification($setup, $setup->jobSeeker, $recentJobs);
                if ($sent) {
                    $emailSent = true;
                    Log::info("Email notification sent (synchronous) for setup ID: {$setup->id}", [
                        'job_seeker_email' => $setup->jobSeeker->email,
                        'configured_delivery_mode' => $deliveryMode,
                        'jobs_count' => $recentJobs->count(),
                    ]);
                    $setup->incrementSentCount();
                    $this->jobRepository->markJobsAsSentToRecipient(
                        $setup->id,
                        $recentJobs->pluck('id')->toArray(),
                        $setup->jobSeeker->email
                    );
                } else {
                    Log::warning("Synchronous email send reported failure for setup ID: {$setup->id}", [
                        'job_seeker_email' => $setup->jobSeeker->email,
                        'configured_delivery_mode' => $deliveryMode,
                    ]);
                }
            } catch (\Throwable $e) {
                Log::error("Failed to send email notification for setup ID: {$setup->id}. Error: " . $e->getMessage(), [
                    'exception' => $e,
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        // Send FCM Push Notification
        if ($setup->receive_push_notifications && $setup->jobSeeker->deviceTokens()->exists()) {
            try {
                // The JobSeeker model (notifiable) will use routeNotificationForFcm() to get tokens
                $setup->jobSeeker->notify(new JobAlertFcmNotification($recentJobs, $setup));
                $fcmSent = true;
                Log::info("FCM notification queued for setup ID: {$setup->id} to job seeker ID: {$setup->jobSeeker->id}");
                // You might want a separate sent count for push if email and push are independent
                // $setup->incrementPushSentCount(); // If you add such a method
                // Mark jobs as sent via push if tracking is separate
                // $this->jobRepository->markJobsAsSentViaPush($setup->id, $recentJobs->pluck('id')->toArray(), $setup->jobSeeker->id);

            } catch (\Throwable $e) {
                Log::error("Failed to send FCM notification for setup ID: {$setup->id}. Error: " . $e->getMessage(), [
                    'exception' => $e,
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        $setup->updateLastActivityCheck();

        return $emailSent || $fcmSent; // Return true if at least one notification was attempted
    }


    


    private function isJobInstance($job)
    {
        return is_object($job) && $job instanceof Job;
    }

    public function getMissedNotifications()
    {
        $missedNotifications = Job::where('publish_date', '>=', Carbon::now()->subDays(2))
            ->whereDoesntHave('notifications')
            ->get();

        return $missedNotifications;
    }

    public function getJobsByCategory($categoryId)
    {
        $baseQuery = Job::whereHas('categories', function ($query) use ($categoryId) {
            $query->where('id', $categoryId);
        });

        return $baseQuery;
    }



   
    public function getCategoryJobCounts(int $days = 5, bool $includeRecentness = false): array
    {
        Log::debug('JobService: Getting category job counts', ['days' => $days, 'includeRecentness' => $includeRecentness]);
        
        $startDate = Carbon::now()->subDays($days)->startOfDay();
        $defaultLocation = config('jobseeker.default_location', 'Kabul');
        
        // Get jobs from the specified period
        $jobs = Job::where('publish_date', '>=', $startDate)
            ->where('locations', 'like', '%' . $defaultLocation . '%')
            ->get();
        
        $categoryCounts = [];
        
        foreach ($jobs as $job) {
            $categories = $this->determineJobCategories($job->position);
            
            foreach ($categories as $categoryId) {
                if (!isset($categoryCounts[$categoryId])) {
                    $categoryCounts[$categoryId] = 0;
                }
                $categoryCounts[$categoryId]++;
            }
        }
        
        if (!$includeRecentness) {
            return $categoryCounts;
        }
        
        // Include breakdown by recency (today vs this week)
        $today = Carbon::now()->startOfDay();
        $weekStart = Carbon::now()->subDays(7)->startOfDay();
        
        $recentCounts = [
            'total' => $categoryCounts,
            'recent' => []
        ];
        
        foreach ($jobs as $job) {
            $publishDate = Carbon::parse($job->publish_date);
            $categories = $this->determineJobCategories($job->position);
            
            foreach ($categories as $categoryId) {
                if (!isset($recentCounts['recent'][$categoryId])) {
                    $recentCounts['recent'][$categoryId] = [
                        'today' => 0,
                        'week' => 0
                    ];
                }
                
                if ($publishDate >= $today) {
                    $recentCounts['recent'][$categoryId]['today']++;
                } elseif ($publishDate >= $weekStart) {
                    $recentCounts['recent'][$categoryId]['week']++;
                }
            }
        }
        
        return $recentCounts;
    }
    
    /**
     * Check for inactive categories in notification setups
     * 
     * @param JobNotificationSetup $setup
     * @param int|null $days Number of days to check for activity (defaults to config)
     * @return array Array of inactive category information
     */
    public function checkInactiveCategories(JobNotificationSetup $setup, ?int $days = null): array
    {
        $days = $days ?? config('jobseeker.category_management.activity_check_days', 30);
        
        Log::debug('JobService: Checking inactive categories for setup', [
            'setup_id' => $setup->id,
            'setup_name' => $setup->name,
            'days' => $days
        ]);
        
        $startDate = Carbon::now()->subDays($days)->startOfDay();
        $defaultLocation = config('jobseeker.default_location', 'Kabul');
        $inactiveCategories = [];
        
        foreach ($setup->categories as $category) {
            // Count jobs in this category within the specified period
            $jobCount = Job::where('publish_date', '>=', $startDate)
                ->where('locations', 'like', '%' . $defaultLocation . '%')
                ->whereHas('categories', function($query) use ($category) {
                    $query->where('job_categories.id', $category->id);
                })
                ->count();
            
            // If no jobs found, check if any jobs match this category by position analysis
            if ($jobCount === 0) {
                $jobsWithMatchingPositions = Job::where('publish_date', '>=', $startDate)
                    ->where('locations', 'like', '%' . $defaultLocation . '%')
                    ->get()
                    ->filter(function($job) use ($category) {
                        $determinedCategories = $this->determineJobCategories($job->position);
                        return in_array($category->id, $determinedCategories);
                    });
                
                $jobCount = $jobsWithMatchingPositions->count();
            }
            
            if ($jobCount === 0) {
                $inactiveCategories[] = [
                    'category_id' => $category->id,
                    'category_name' => $category->name,
                    'days_checked' => $days,
                    'last_activity_date' => $this->getLastActivityDateForCategory($category->id),
                    'is_archived' => $category->is_archived ?? false
                ];
                
                Log::info('JobService: Found inactive category', [
                    'setup_id' => $setup->id,
                    'category_id' => $category->id,
                    'category_name' => $category->name,
                    'days_checked' => $days
                ]);
            }
        }
        
        return $inactiveCategories;
    }
    
    /**
     * Get the last activity date for a specific category
     * 
     * @param int $categoryId
     * @return string|null
     */
    private function getLastActivityDateForCategory(int $categoryId): ?string
    {
        $defaultLocation = config('jobseeker.default_location', 'Kabul');
        
        // First check if there are any jobs directly associated with this category
        $lastJob = Job::whereHas('categories', function($query) use ($categoryId) {
                $query->where('job_categories.id', $categoryId);
            })
            ->where('locations', 'like', '%' . $defaultLocation . '%')
            ->orderBy('publish_date', 'desc')
            ->first();
        
        if ($lastJob) {
            return Carbon::parse($lastJob->publish_date)->format('Y-m-d');
        }
        
        // If no direct association, check by position analysis
        $category = JobCategory::find($categoryId);
        if (!$category) {
            return null;
        }
        
        $recentJobs = Job::where('locations', 'like', '%' . $defaultLocation . '%')
            ->orderBy('publish_date', 'desc')
            ->limit(1000) // Check last 1000 jobs for performance
            ->get();
        
        foreach ($recentJobs as $job) {
            $determinedCategories = $this->determineJobCategories($job->position);
            if (in_array($categoryId, $determinedCategories)) {
                return Carbon::parse($job->publish_date)->format('Y-m-d');
            }
        }
        
        return null;
    }
    
    /**
     * Check and update activity status for all notification setups
     * This method should be called periodically (e.g., via scheduled job)
     * 
     * @return array Summary of activity check results
     */
    public function checkAllSetupsForInactiveCategories(): array
    {
        $startTime = microtime(true);
        Log::info('JobService: ENTRY - Starting activity check for all notification setups', [
            'timestamp' => now()->toDateTimeString(),
            'memory_usage_mb' => round(memory_get_usage() / 1024 / 1024, 2)
        ]);
        
        $checkPeriodDays = config('jobseeker.category_management.activity_check_days', 30);
        $cutoffDate = Carbon::now()->subDays(1); // Only check setups that haven't been checked in the last day
        
        Log::debug('JobService: Activity check configuration', [
            'check_period_days' => $checkPeriodDays,
            'cutoff_date' => $cutoffDate->toDateTimeString(),
            'config_source' => 'jobseeker.category_management.activity_check_days'
        ]);
        
        $setupsToCheck = JobNotificationSetup::where('is_active', true)
            ->where(function($query) use ($cutoffDate) {
                $query->whereNull('last_activity_check_at')
                      ->orWhere('last_activity_check_at', '<', $cutoffDate);
            })
            ->with('categories')
            ->get();
        
        Log::info('JobService: Found notification setups to check', [
            'total_setups_found' => $setupsToCheck->count(),
            'query_cutoff_date' => $cutoffDate->toDateTimeString()
        ]);
        
        $summary = [
            'total_setups_checked' => 0,
            'setups_with_inactive_categories' => 0,
            'total_inactive_categories_found' => 0,
            'setups_processed' => [],
            'execution_time_ms' => 0,
            'memory_peak_mb' => 0,
            'errors_encountered' => []
        ];
        
        foreach ($setupsToCheck as $setup) {
            $setupStartTime = microtime(true);
            try {
                Log::debug('JobService: Processing setup for inactive categories', [
                    'setup_id' => $setup->id,
                    'setup_name' => $setup->name,
                    'job_seeker_id' => $setup->job_seeker_id,
                    'categories_count' => $setup->categories->count(),
                    'last_activity_check_at' => $setup->last_activity_check_at?->toDateTimeString()
                ]);
                
                $inactiveCategories = $this->checkInactiveCategories($setup, $checkPeriodDays);
                
                // Update the last activity check timestamp
                $setup->updateLastActivityCheck();
                
                $setupExecutionTime = round((microtime(true) - $setupStartTime) * 1000, 2);
                $summary['total_setups_checked']++;
                
                if (!empty($inactiveCategories)) {
                    $summary['setups_with_inactive_categories']++;
                    $summary['total_inactive_categories_found'] += count($inactiveCategories);
                    
                    $setupSummary = [
                        'setup_id' => $setup->id,
                        'setup_name' => $setup->name,
                        'inactive_categories' => $inactiveCategories,
                        'job_seeker_email' => $setup->jobSeeker->email ?? 'Unknown',
                        'processing_time_ms' => $setupExecutionTime
                    ];
                    $summary['setups_processed'][] = $setupSummary;
                    
                    // Log the inactive categories for this setup
                    Log::warning('JobService: Found inactive categories in notification setup', [
                        'setup_id' => $setup->id,
                        'setup_name' => $setup->name,
                        'job_seeker_email' => $setup->jobSeeker->email ?? 'Unknown',
                        'inactive_count' => count($inactiveCategories),
                        'inactive_categories' => array_column($inactiveCategories, 'category_name'),
                        'check_period_days' => $checkPeriodDays,
                        'processing_time_ms' => $setupExecutionTime
                    ]);
                    
                    // Send notification to job seeker about inactive categories
                    $this->notifyJobSeekerOfInactiveCategories($setup, $inactiveCategories);
                } else {
                    Log::debug('JobService: No inactive categories found in setup', [
                        'setup_id' => $setup->id,
                        'setup_name' => $setup->name,
                        'categories_count' => $setup->categories->count(),
                        'processing_time_ms' => $setupExecutionTime
                    ]);
                }
                
            } catch (\Exception $e) {
                $setupExecutionTime = round((microtime(true) - $setupStartTime) * 1000, 2);
                $errorData = [
                    'setup_id' => $setup->id,
                    'setup_name' => $setup->name ?? 'Unknown',
                    'error' => $e->getMessage(),
                    'error_code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'processing_time_ms' => $setupExecutionTime
                ];
                
                $summary['errors_encountered'][] = $errorData;
                
                Log::error('JobService: EXCEPTION - Error checking setup for inactive categories', $errorData);
                
                // Continue processing other setups even if one fails
            }
        }
        
        $totalExecutionTime = round((microtime(true) - $startTime) * 1000, 2);
        $peakMemoryMB = round(memory_get_peak_usage() / 1024 / 1024, 2);
        
        $summary['execution_time_ms'] = $totalExecutionTime;
        $summary['memory_peak_mb'] = $peakMemoryMB;
        
        Log::info('JobService: EXIT - Completed activity check for all notification setups', [
            'summary' => $summary,
            'performance' => [
                'total_execution_time_ms' => $totalExecutionTime,
                'average_time_per_setup_ms' => $summary['total_setups_checked'] > 0 ? round($totalExecutionTime / $summary['total_setups_checked'], 2) : 0,
                'peak_memory_usage_mb' => $peakMemoryMB,
                'final_memory_usage_mb' => round(memory_get_usage() / 1024 / 1024, 2)
            ]
        ]);
        
        return $summary;
    }
    
    /**
     * Notify job seeker about inactive categories in their setup
     * 
     * @param JobNotificationSetup $setup
     * @param array $inactiveCategories
     * @return void
     */
    private function notifyJobSeekerOfInactiveCategories(JobNotificationSetup $setup, array $inactiveCategories): void
    {
        try {
            $jobSeeker = $setup->jobSeeker;
            if (!$jobSeeker) {
                return;
            }
            
            $categoryNames = array_column($inactiveCategories, 'category_name');
            $daysChecked = $inactiveCategories[0]['days_checked'] ?? 30;
            
            // For now, we'll log this. Later this can be replaced with actual email/in-app notifications
            Log::info('JobService: Should notify job seeker of inactive categories', [
                'job_seeker_id' => $jobSeeker->id,
                'job_seeker_email' => $jobSeeker->email,
                'setup_id' => $setup->id,
                'setup_name' => $setup->name,
                'inactive_categories' => $categoryNames,
                'days_checked' => $daysChecked,
                'message' => "Notice for your setup '{$setup->name}': " . 
                           (count($categoryNames) === 1 
                               ? "Category '{$categoryNames[0]}' has not had new matching job listings for this setup in the last {$daysChecked} days."
                               : "Categories '" . implode("', '", $categoryNames) . "' have not had new matching job listings for this setup in the last {$daysChecked} days.")
            ]);
            
            // TODO: Implement actual email/in-app notification
            // Example implementation:
            // $this->emailService->sendEmail(
            //     ['email' => $jobSeeker->email, 'name' => $jobSeeker->name],
            //     'Inactive Categories Notice - ' . $setup->name,
            //     'jobseeker::emails.inactive-categories-notice',
            //     [
            //         'jobSeeker' => $jobSeeker,
            //         'setup' => $setup,
            //         'inactiveCategories' => $inactiveCategories,
            //         'daysChecked' => $daysChecked
            //     ]
            // );
            
        } catch (\Exception $e) {
            Log::error('JobService: Error notifying job seeker of inactive categories', [
                'setup_id' => $setup->id,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Get activity summary for a specific notification setup
     * Useful for displaying in the UI
     * 
     * @param JobNotificationSetup $setup
     * @return array Activity summary information
     */
    public function getSetupActivitySummary(JobNotificationSetup $setup): array
    {
        $checkPeriodDays = config('jobseeker.category_management.activity_check_days', 30);
        $recentDays = config('jobseeker.category_management.recent_activity_days', 7);
        
        $inactiveCategories = $this->checkInactiveCategories($setup, $checkPeriodDays);
        $recentJobCounts = $this->getCategoryJobCounts($recentDays);
        
        $categoryActivity = [];
        foreach ($setup->categories as $category) {
            $isInactive = collect($inactiveCategories)->contains('category_id', $category->id);
            $recentJobCount = $recentJobCounts[$category->id] ?? 0;
            
            $categoryActivity[] = [
                'category_id' => $category->id,
                'category_name' => $category->name,
                'is_inactive' => $isInactive,
                'recent_job_count' => $recentJobCount,
                'last_activity_date' => $isInactive 
                    ? collect($inactiveCategories)->firstWhere('category_id', $category->id)['last_activity_date'] ?? null
                    : null,
                'is_archived' => $category->is_archived ?? false
            ];
        }
        
        return [
            'setup_id' => $setup->id,
            'setup_name' => $setup->name,
            'last_activity_check_at' => $setup->last_activity_check_at?->format('Y-m-d H:i:s'),
            'inactive_categories_count' => count($inactiveCategories),
            'total_categories_count' => $setup->categories->count(),
            'category_activity' => $categoryActivity,
            'check_period_days' => $checkPeriodDays,
            'recent_period_days' => $recentDays
        ];
    }
} 
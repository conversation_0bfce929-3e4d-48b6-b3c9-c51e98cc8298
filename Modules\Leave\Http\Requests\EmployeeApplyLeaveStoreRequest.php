<?php

namespace Modules\Leave\Http\Requests;

use App\Rules\CheckDependentEmail;
use App\Rules\CheckEmployeeLeave;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class EmployeeApplyLeaveStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {



        $this->merge([

            'apply_date' => Carbon::parse($this->apply_date)->format('Y-m-d'),
            'start_date' => Carbon::parse($this->start_date)->format('Y-m-d'),
            'makeup_date' => Carbon::parse($this->makeup_date)->format('Y-m-d'),
        ]);




        $to = Carbon::parse($this->get('start_date'));
        $from = $this->get('day') == 2 ? Carbon::parse($this->get('end_date')) : '';
        $totDaysRequested = $this->get('day') == 2 ? $to->diffInDays($from)+1 : ($this->get('day') == 1 ?  1 : 0.5);
        $leaveTypeId = $this->get('leave_type_id');


        $ApprovedleavesByType =  approvedleavesByType($this->get('user'),$this->get('leave_type_id'));

//        $ApprovedleavesByType =   \DB::select("select case when sum(al.total_days) is null then 0 else sum(al.total_days) end as totalDays from apply_leaves al where al.user_id = ? and al.leave_type_id = ? and al.status = 1; ",[auth()->user()->id,]);
//        $ApprovedleavesByType = (int)$ApprovedleavesByType[0]->totalDays;


        return [

            'leave_type_id' => ['required',new CheckEmployeeLeave($totDaysRequested,$ApprovedleavesByType,$leaveTypeId)],
            'reason' => 'required|max:900',
            'attachment' => 'nullable|mimes:jpeg,jep,png,docx,txt,pdf',
            'apply_date' => 'required|after:yesterday', // employee can select from today date and onwards
            'start_date' => 'required',
            'day' => 'required',
            'from_day' =>  ['required_if:day,==,0',],
            'end_date' => 'required_if:day,==,2',

        ];
    }

    public function messages()
    {
        return [
            'leave_type_id.required' => 'Please select the leave type',
            'from_day.required_if' => 'This field is required when day is half.',
            'end_date.required_if' => 'This field is required when day is half.',
            'apply_date.required' => 'Please select the leave application date.',
            'apply_date.after' => 'select '.date('m/d/Y') . ' or onwards',
            'start_date.required' => 'Please select the leave from date.',
            'reason.required' => 'Please provide a valid reason.',
            'reason.reason' => 'Maximum 900 chars allowed',



        ];

    }

    public function attributes()
    {
        return [
            'leave_type_id' => 'Leave Type',
            'reason' => 'Reason',
            'apply_date' => 'Apply Date',
        ];
    }



    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}

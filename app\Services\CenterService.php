<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CenterService
{
    /**
     * Get all active centers with their details
     *
     * @return array
     */
    public function getAllCenters()
    {
        try {
            return Cache::remember('all_centers', 60 * 24, function () {
                Log::info('Fetching centers from database and caching');
                return DB::table('centers')
                    ->where('active', 1)
                    ->select('id', 'name', 'name_ar', 'address', 'latitude', 'longitude', 'gender', 'description', 'description_ar')
                    ->get()
                    ->toArray();
            });
        } catch (\Exception $e) {
            Log::error('Error fetching centers: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get formatted center HTML list for FAQ
     *
     * @param string $locale Current locale (en/ar)
     * @return string
     */
    public function getFormattedCenterListHtml($locale = 'en')
    {
        $centers = $this->getAllCenters();
        $html = '<ul>';
        
        foreach ($centers as $center) {
            $name = $locale === 'ar' ? ($center->name_ar ?: $center->name) : $center->name;
            $description = $locale === 'ar' ? ($center->description_ar ?: $center->description) : $center->description;
            
            $mapLink = '';
            if ($center->latitude && $center->longitude) {
                $mapLink = " <a href=\"https://maps.google.com/?q={$center->latitude},{$center->longitude}\" target=\"_blank\">";
                $mapLink .= $name;
                $mapLink .= "</a>";
            } else {
                $mapLink = $name;
            }
            
            $html .= "<li><strong>{$mapLink}</strong> - {$description}</li>";
        }
        
        // Add the online program option
        if ($locale === 'ar') {
            $html .= '<li><strong>البرنامج عبر الإنترنت</strong> - للذين لا يستطيعون حضور المواقع الفعلية</li>';
        } else {
            $html .= '<li><strong>Online Program</strong> - For those who cannot attend physical locations</li>';
        }
        
        $html .= '</ul>';
        
        return $html;
    }
    
    /**
     * Clear centers cache
     *
     * @return void
     */
    public function clearCentersCache()
    {
        Log::info('Clearing centers cache');
        Cache::forget('all_centers');
    }
} 
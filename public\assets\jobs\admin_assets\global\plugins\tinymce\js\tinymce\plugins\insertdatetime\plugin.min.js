!function(){"use strict";var e=function(t){var n=t,r=function(){return n};return{get:r,set:function(e){n=e},clone:function(){return e(r())}}},t=tinymce.util.Tools.resolve("tinymce.PluginManager"),n=function(e){return e.getParam("insertdatetime_timeformat",e.translate("%H:%M:%S"))},r=function(e){return e.getParam("insertdatetime_formats",["%H:%M:%S","%Y-%m-%d","%I:%M:%S %p","%D"])},a=function(e){return e.getParam("insertdatetime_dateformat",e.translate("%Y-%m-%d"))},i=n,o=r,u=function(e){var t=r(e);return t.length>0?t[0]:n(e)},c=function(e){return e.getParam("insertdatetime_element",!1)},l="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),m="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),s="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),d="January February March April May June July August September October November December".split(" "),p=function(e,t){if((e=""+e).length<t)for(var n=0;n<t-e.length;n++)e="0"+e;return e},f=function(e,t,n){return n=n||new Date,t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace("%D","%m/%d/%Y")).replace("%r","%I:%M:%S %p")).replace("%Y",""+n.getFullYear())).replace("%y",""+n.getYear())).replace("%m",p(n.getMonth()+1,2))).replace("%d",p(n.getDate(),2))).replace("%H",""+p(n.getHours(),2))).replace("%M",""+p(n.getMinutes(),2))).replace("%S",""+p(n.getSeconds(),2))).replace("%I",""+((n.getHours()+11)%12+1))).replace("%p",n.getHours()<12?"AM":"PM")).replace("%B",""+e.translate(d[n.getMonth()]))).replace("%b",""+e.translate(s[n.getMonth()]))).replace("%A",""+e.translate(m[n.getDay()]))).replace("%a",""+e.translate(l[n.getDay()]))).replace("%%","%")},g=function(e,t){if(c(e)){var n=f(e,t),r=void 0;r=/%[HMSIp]/.test(t)?f(e,"%Y-%m-%dT%H:%M"):f(e,"%Y-%m-%d");var a=e.dom.getParent(e.selection.getStart(),"time");a?(o=a,u=r,l=n,m=(i=e).dom.create("time",{datetime:u},l),o.parentNode.insertBefore(m,o),i.dom.remove(o),i.selection.select(m,!0),i.selection.collapse(!1)):e.insertContent('<time datetime="'+r+'">'+n+"</time>")}else e.insertContent(f(e,t));var i,o,u,l,m},y=f,M=function(e){e.addCommand("mceInsertDate",function(){g(e,a(e))}),e.addCommand("mceInsertTime",function(){g(e,i(e))})},v=tinymce.util.Tools.resolve("tinymce.util.Tools"),S=function(e,t){var n,r,a,i=(r=t,a=o(n=e),v.map(a,function(e){return{text:y(n,e),onclick:function(){r.set(e),g(n,e)}}}));e.addButton("insertdatetime",{type:"splitbutton",title:"Insert date/time",menu:i,onclick:function(){var n=t.get();g(e,n||u(e))}}),e.addMenuItem("insertdatetime",{icon:"date",text:"Date/time",menu:i,context:"insert"})};t.add("insertdatetime",function(t){var n=e(null);M(t),S(t,n)})}();
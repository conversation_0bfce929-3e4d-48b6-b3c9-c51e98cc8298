<?php

namespace Modules\Education\Http\Controllers;

use App\Center;
use App\Classes;
use App\Employee;
use App\Student;
use App\StudentRevisionReport;
use App\Weekend;
use App\ClassRoom;
use App\YearCheck;
use App\ApiBaseMethod;
use Illuminate\Http\Request;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use PDF; // assuming you have the alias set for DOMPDF

class ClassTablesPDFController extends Controller
{

    public function __construct()
	{
//        $this->middleware('PM');
        // User::checkAuth();
	}

    public function downloadPDF($classId,$monthYear)
    {


        // Convert the monthYear string to a Carbon instance
        $date = \Carbon\Carbon::createFromFormat('F Y', $monthYear);

// Extract the month and year separately
        $monthName = $date->monthName; // Numeric representation of the month (e.g., 06)
        $month = $date->format('m'); // Numeric representation of the month (e.g., 06)
        $year = $date->format('Y'); // 4-digit year (e.g., 2023)
        $letterHead = asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg");
        $class = Classes::find($classId);
        $className = $class->name; // Assuming the column name is 'full_name'
        $centerName = $class->center->name;
        $supervisors = $class->employee; // Assuming this returns the supervisor information
        $teachers = $class->teachers->pluck('full_name')->join(', ');
        $classTeachers = $class->teachers()->pluck('full_name')->toArray();


        // Check if $supervisors is not null
        if ($supervisors) {
            $supervisorInfo = $supervisors->pluck('name')->join(', ');
        } else {
            $supervisorInfo = ''; // Default value if no supervisors are found
        }


        $data = [
            'className' => $className,
            'classTeachers' => $classTeachers,
            'centerName' => $centerName,
            'letterHead' => $letterHead,
            'supervisors' => $supervisorInfo,
            'monthYear' => $date,
            'classId' =>  $classId,  // Retrieve and assign the required data
            'year' => $year ,  // Retrieve and assign the required data
            'month' => $month ,  // Retrieve and assign the required data
            'monthName' => $monthName ,  // Retrieve and assign the required data
    ];




        // Generate a descriptive file name
        $fileName = "Report_{$centerName}_{$className}_{$monthName}_{$year}.pdf";



        view()->share('educationalreports::reports.pdf.class',$data);
        $pdf = PDF::loadView('education::classes.reports.pdf.class.all', $data);
        return $pdf->download($fileName);


        
    }

}
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Modules\Admission\Facades\Settings;

class StudentNationalityGoogleRegistrationController extends Controller
{
    public function showNationalityForm()
    {
        $countryList = Settings::getCountriesList();
        return view('student-nationality-google-registration-form', ['countryList' => $countryList]);
    }

    public function storeNationality(Request $request)
    {
        $request->validate([
            'nationality' => 'required|string|max:255',
        ]);

        $user = \Auth::user();
        $user->nationality = $request->nationality;
        $user->save();

        return redirect()->intended('/');
    }
}

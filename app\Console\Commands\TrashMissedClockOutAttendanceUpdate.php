<?php

namespace App\Console\Commands;

use App\Attendance;
use App\Employee;
use App\MissedClockOut;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TrashMissedClockOutAttendanceUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'trash:missedclockouts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Trash incomplete attendance for the employees for yesterday';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            Log::info('trash:missedclockouts command execution started.');
            $this->info('Processing incomplete attendance records from yesterday...');
            
            // Start transaction
            DB::beginTransaction();

            $yesterday = Carbon::yesterday('Asia/Kuala_Lumpur');
            Log::info('Checking attendance records for date: ' . $yesterday->toDateString());

            // Query for latest "in" attendance from yesterday that don't have corresponding "out"
            $attendanceRecords = $this->getIncompleteAttendanceRecords($yesterday);
            
            $count = $attendanceRecords->count();
            Log::info("Found {$count} incomplete attendance records to process");
            $this->info("Found {$count} incomplete attendance records to process");

            if ($count === 0) {
                Log::info('No incomplete attendance records found. Command completed.');
                DB::commit();
                return Command::SUCCESS;
            }

            // Process the filtered attendance
            $processedCount = $this->processIncompleteAttendance($attendanceRecords);
            
            DB::commit();
            
            Log::info("Command completed successfully. Processed {$processedCount} records.");
            $this->info("Successfully processed {$processedCount} incomplete attendance records.");
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in trash:missedclockouts: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            $this->error('Command failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Get incomplete attendance records from yesterday
     * 
     * @param Carbon $date
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getIncompleteAttendanceRecords(Carbon $date)
    {
        // Get all employees who clocked in yesterday
        $employeesWithClockIn = Attendance::whereDate('clock', $date)
            ->where('type', 'in')
            ->pluck('employee_id')
            ->unique();
        
        // Filter out employees who also clocked out yesterday
        $employeesWithCompleteAttendance = Attendance::whereDate('clock', $date)
            ->where('type', 'out')
            ->pluck('employee_id')
            ->unique();
        
        // Find employees who only clocked in but didn't clock out
        $employeesWithIncompletePairs = $employeesWithClockIn->diff($employeesWithCompleteAttendance);
        
        Log::info("Found " . $employeesWithClockIn->count() . " employees with clock-in records");
        Log::info("Found " . $employeesWithCompleteAttendance->count() . " employees with clock-out records");
        Log::info("Found " . $employeesWithIncompletePairs->count() . " employees with incomplete attendance pairs");
        
        if ($employeesWithIncompletePairs->isEmpty()) {
            return collect();
        }
        
        // Get the latest "in" attendance for these employees
        $latestInRecords = collect();
        
        foreach ($employeesWithIncompletePairs as $employeeId) {
            $latestRecord = Attendance::where('employee_id', $employeeId)
                ->whereDate('clock', $date)
                ->where('type', 'in')
                ->latest('clock')
                ->first();
                
            if ($latestRecord) {
                $latestInRecords->push($latestRecord);
            }
        }
        
        return $latestInRecords;
    }

    /**
     * Process incomplete attendance records
     * 
     * @param \Illuminate\Database\Eloquent\Collection $records
     * @return int
     */
    private function processIncompleteAttendance($records)
    {
        $processedCount = 0;

        foreach ($records as $record) {
            try {
                // Create a record in MissedClockOut table
                MissedClockOut::create([
                    'employee_id' => $record->employee_id,
                    'attendance_id' => $record->id,
                    'clock' => $record->clock,
                    'type' => $record->type,
                    'organization_id' => config('organization_id'),
                ]);

                // Soft-delete the original record
                $record->delete();

                $processedCount++;
                
                Log::info("Processed attendance ID {$record->id} for employee ID {$record->employee_id} on {$record->clock->toDateString()}");
                $this->line(" Clockout has been trashed for employee ID {$record->employee_id} on {$record->clock->toDateString()}");
            } catch (\Exception $e) {
                Log::warning("Failed to process attendance ID {$record->id}: {$e->getMessage()}");
                // Continue processing other records
            }
        }

        return $processedCount;
    }
}

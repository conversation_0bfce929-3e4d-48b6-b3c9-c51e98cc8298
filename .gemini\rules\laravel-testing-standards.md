---
description: "Defines the mandatory standards, safety protocols, and best practices for writing PHPUnit tests in this Laravel project."
globs: ["tests/**/*.php", "Modules/**/Tests/**/*.php"]
alwaysApply: true
---

# Laravel Testing Protocol

This document outlines the rules and conventions that MUST be followed when writing all types of tests (Unit, Feature, etc.) for this project. The primary goals are to ensure test reliability, maintainability, and, most importantly, the safety of our database.

## 1. 🚨 CRITICAL SAFETY RULE: No Database Wiping

**The `Illuminate\Foundation\Testing\RefreshDatabase` trait is STRICTLY FORBIDDEN.**

-   **Reason:** This project's database schema is managed by direct SQL, not Laravel migrations. Using `RefreshDatabase` would drop the entire schema, leading to catastrophic data loss and breaking the test environment.
-   **MANDATORY ALTERNATIVE: Database Transactions**
    -   All tests that interact with the database MUST wrap their operations in a transaction and roll it back at the end. This ensures each test runs in isolation without affecting the database state for subsequent tests.
    -   Use the `Illuminate\Foundation\Testing\DatabaseTransactions` trait, or manage it manually as shown below.

-   **Example Implementation:**

    ```php
    <?php

    namespace Tests\Feature;

    use Illuminate\Foundation\Testing\DatabaseTransactions;
    use Tests\TestCase;

    class ExampleFeatureTest extends TestCase
    {
        use DatabaseTransactions; // This is the required trait.

        public function test_something_is_stored_in_the_database()
        {
            // 1. Arrange: Create a user using a factory
            $user = \App\Models\User::factory()->create();

            // 2. Act: Perform an action
            $response = $this->post('/some-endpoint', ['user_id' => $user->id]);

            // 3. Assert: Check that the data was stored
            $this->assertDatabaseHas('some_table', [
                'user_id' => $user->id,
                'some_data' => 'expected_value'
            ]);
        }
    }
    ```

## 2. Guiding Principles

-   **Test the Behavior, Not the Implementation:** Focus your tests on the expected outcome (e.g., "a user is created," "a 200 status is returned"), not on the internal workings of a method.
-   **One Test, One Behavior:** Each test method should verify a single, specific behavior. This makes tests easier to read, understand, and debug.
-   **Arrange, Act, Assert (AAA):** Structure your tests clearly:
    1.  **Arrange:** Set up the necessary preconditions (e.g., create models with factories, mock services).
    2.  **Act:** Execute the code you are testing (e.g., make an HTTP request, call a service method).
    3.  **Assert:** Verify that the outcome is what you expected.

## 3. Test Location and Naming

-   **Location:**
    -   **General Tests:** Place in the root `tests/Feature` or `tests/Unit` directories.
    -   **Module-Specific Tests:** Place inside the module's own test directory: `Modules/<ModuleName>/Tests/Feature` or `Modules/<ModuleName>/Tests/Unit`.
-   **File Naming:** Test classes must end with the `Test` suffix.
    -   ✅ `UserTest.php`
    -   ✅ `SyncJobsCommandTest.php`
-   **Method Naming:** Test methods must be prefixed with `test_` and use `snake_case`. The name should clearly describe the behavior being tested.
    -   ✅ `public function test_a_guest_cannot_access_the_admin_dashboard()`
    -   ❌ `public function testAdmin()`

## 4. Writing Feature Tests (HTTP & Console)

Feature tests drive your application from the outside, as a user would.

-   **HTTP Tests:** Use Laravel's built-in HTTP testing helpers (`get`, `post`, `json`, etc.).
    -   Always assert the response status: `$response->assertStatus(200);`
    -   Assert that the correct view is returned: `$response->assertViewIs('auth.login');`
    -   Assert that specific content is visible: `$response->assertSee('Welcome Back');`
    -   For JSON APIs, assert the JSON structure and data: `$response->assertJson(['data' => ...]);`
-   **Console Tests:** Use the `$this->artisan()` helper to test your Artisan commands.
    -   `$this->artisan('jobseeker:sync-acbar-jobs')->assertExitCode(0);`
    -   `$this->artisan('some:command')->expectsOutput('Something was processed!');`

## 5. Writing Unit Tests

Unit tests verify a single class or method in isolation.

-   **Target:** Services, Repositories, and other plain PHP classes are excellent candidates for unit tests.
-   **Mocking:** Dependencies MUST be mocked to achieve true isolation. Do not make database or external API calls from a unit test. Use `Mockery` or PHPUnit's built-in mocking capabilities.

    ```php
    // Example: Unit testing a service that uses a repository
    public function test_a_service_correctly_processes_data()
    {
        // Arrange: Mock the dependency
        $mockRepository = $this->mock(JobRepository::class);
        $mockRepository->shouldReceive('getFeatured')->once()->andReturn(collect([]));

        // Act: Instantiate the service with the mock and call the method
        $service = new JobService($mockRepository);
        $result = $service->getFeaturedJobsWidget();

        // Assert: Verify the result
        $this->assertIsArray($result);
    }
    ```

## 6. Database & Data Management in Tests

-   **Use Factories:** Always use model factories to create test data. This keeps your tests clean and decoupled from the model's specific attributes.
-   **Use Database Assertions:** Leverage Laravel's database assertions to verify the state of the database after an action.
    -   `$this->assertDatabaseHas('users', ['email' => '<EMAIL>']);`
    -   `$this->assertDatabaseMissing('jobs', ['id' => 999]);`
    -   `$this->assertSoftDeleted('users', ['id' => $user->id]);`

## 7. Prohibited Practices

-   **DO NOT** use the `RefreshDatabase` trait. Use `DatabaseTransactions` instead.
-   **DO NOT** modify the `.env` file from within a test.
-   **DO NOT** use the `env()` helper directly in your tests. Use `config()` instead.
-   **DO NOT** rely on the state of the development database. Create all necessary data within the test itself.

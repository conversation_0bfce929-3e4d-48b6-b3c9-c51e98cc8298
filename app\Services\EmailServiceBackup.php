<?php

namespace App\Services;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use MailerSend\MailerSend;
use MailerSend\Helpers\Builder\Recipient;
use MailerSend\Helpers\Builder\EmailParams;
use MailerSend\Helpers\Builder\Attachment;

class EmailServiceBackup
{
    protected $mailersend;

    public function __construct()
    {
        $apiKey = env('MAILERSEND_API_KEY');
        $this->mailersend = new MailerSend(['api_key' => $apiKey]);
    }

    public function sendEmail($to, $subject, $view, $viewData, $from = null, $cc = [], $attachments = [])
    {
        try {
            // Check if the environment is local
            if (App::environment('local')) {
                // In local environment, do not send email, just return 1
                return 1;
            }

            // Determine the sender's email and name
            $fromEmail = $from['email'] ?? env('MAIL_FROM_ADDRESS');
            $fromName = $from['name'] ?? env('MAIL_FROM_NAME');

            // Generate email content
            $htmlContent = view()->make($view, $viewData)->render();
            $textContent = strip_tags($htmlContent);

            // Prepare the main recipient
            $recipients = [
                new Recipient($to['email'], $to['name']),
            ];

            // Prepare CC recipients
            $ccRecipients = [];
            if (!empty($cc)) {
                foreach ($cc as $ccRecipient) {
                    $ccRecipients[] = new Recipient($ccRecipient['email'], $ccRecipient['name']);
                }
            }

            // Prepare attachments
            $mailerSendAttachments = [];
            foreach ($attachments as $attachment) {
                $fileContent = file_get_contents($attachment['path']);
                $base64Content = base64_encode($fileContent);

                $mailerSendAttachments[] = new Attachment(
                    $base64Content,
                    $attachment['name'],
                    $attachment['mime']
                );
            }

            // Build email parameters
            $emailParams = (new EmailParams())
                ->setFrom($fromEmail)
                ->setFromName($fromName)
                ->setRecipients($recipients)
                ->setSubject($subject)
                ->setHtml($htmlContent)
                ->setText($textContent);

            // Add CC recipients if any
            if (!empty($ccRecipients)) {
                $emailParams->setCc($ccRecipients);
            }

            // Add attachments if any
            if (!empty($mailerSendAttachments)) {
                $emailParams->setAttachments($mailerSendAttachments);
            }

            // Send the email
            $this->mailersend->email->send($emailParams);

        } catch (\Exception $e) {
            // Handle and log the exception
            Log::error("Email sending failed: " . $e->getMessage());
            throw new \Exception("Failed to send email: " . $e->getMessage());
        }
    }
}

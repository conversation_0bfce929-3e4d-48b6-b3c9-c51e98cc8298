<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class VerifyJobSubscription extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * The subscriber's name.
     *
     * @var string
     */
    public $name;

    /**
     * The verification token.
     *
     * @var string
     */
    public $token;

    /**
     * The subscriber's email.
     *
     * @var string
     */
    public $email;

    /**
     * Create a new message instance.
     *
     * @param string $name
     * @param string $token
     * @param string $email
     * @return void
     */
    public function __construct(string $name, string $token, string $email)
    {
        $this->name = $name;
        $this->token = $token;
        $this->email = $email;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $verificationUrl = url('/workplace/general/jobs/verify-subscription', [
            'token' => $this->token,
            'email' => $this->email
        ]);

        return $this->subject('Verify Your Job Notification Subscription')
                    ->view('emails.jobs.verify-subscription')
                    ->with([
                        'name' => $this->name,
                        'verificationUrl' => $verificationUrl
                    ]);
    }
} 
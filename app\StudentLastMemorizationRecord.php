<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentLastMemorizationRecord extends Model
{
    use HasFactory;
    protected $table = 'student_last_memorization_record';

    protected $fillable = [
        'student_id',
        'memorization_year_month_day',
        'from_surat',
        'from_ayat',
        'to_surat',
        'to_ayat',
    ];

    /**
     * Get the student that owns the plan.
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }


}

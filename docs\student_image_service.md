# Student Image Service

This document describes how to use the centralized student image handling systems implemented in the application.

## Overview

The solution provides a standardized way to handle student images across the application:

1. **Student images are only stored in the `student_photo` field in the database** - we no longer use the `image` field
2. The `StudentObserver` automatically handles synchronization when images are uploaded 
3. The `StudentImageService` provides centralized access to student images
4. The `StudentImageUploadHelper` standardizes how images are uploaded
5. The `StudentImageRepository` follows the repository pattern for all image operations

## Components

### StudentImageService

The service provides methods to retrieve student images:

```php
use App\Facades\StudentImage;

// Get image URL only
$imageUrl = StudentImage::getStudentImageUrl($student);

// Get full HTML img tag with default attributes
$imgHtml = StudentImage::getStudentImageHtml($student);

// Get HTML img tag with custom attributes
$imgHtml = StudentImage::getStudentImageHtml($student, [
    'class' => 'custom-class',
    'width' => '200',
    'height' => '200'
]);
```

### StudentImageUploadHelper

This helper class standardizes image uploads:

```php
use App\Helpers\StudentImageUploadHelper;

// Upload a photo and return the path
$path = StudentImageUploadHelper::uploadStudentPhoto($request->file('photo'));

// Upload and automatically update the student record
$path = StudentImageUploadHelper::uploadStudentPhoto($request->file('photo'), $student);

// Delete a photo
$success = StudentImageUploadHelper::deleteStudentPhoto($student->student_photo);
```

### StudentImageRepository

For code following the repository pattern:

```php
use App\Repositories\StudentImageRepository;

// Inject in your controller
public function __construct(StudentImageRepository $studentImageRepo) 
{
    $this->studentImageRepo = $studentImageRepo;
}

// Update a student's photo
$student = $this->studentImageRepo->updateStudentPhoto($student, $request->file('photo'));

// Delete a student's photo
$success = $this->studentImageRepo->deleteStudentPhoto($student);
```

## Database Standardization

A database migration has been performed to ensure all student images are stored in the `student_photo` field:

1. All existing images in the `image` field have been copied to `student_photo` 
2. All new uploads will only update the `student_photo` field
3. The `StudentObserver` ensures any image updates are synchronized

## Migration Instructions 

To migrate existing code that manually handles student images:

### In Controllers

Replace manual image handling:

```php
// OLD WAY
if ($request->hasFile('photo')) {
    $file = $request->file('photo');
    $name = md5($file->getClientOriginalName() . time()) . ".png";
    $images->save('public/uploads/student/' . $name);
    $student->student_photo = 'public/uploads/student/' . $name;
    $student->image = 'public/uploads/student/' . $name;
}
```

With the standardized approach:

```php
// NEW WAY
if ($request->hasFile('photo')) {
    StudentImageUploadHelper::uploadStudentPhoto($request->file('photo'), $student);
}
```

### In Blade Templates

Replace direct access to image fields:

```php
// OLD WAY
<img src="{{ $student->image ? asset($student->image) : asset('avatar.jpg') }}" alt="Student">
```

With the StudentImage facade:

```php
// NEW WAY
<img src="{{ StudentImage::getStudentImageUrl($student) }}" alt="Student">
// Or
{!! StudentImage::getStudentImageHtml($student) !!}
// Or use the Student model's accessor
<img src="{{ $student->photo_url }}" alt="Student">
// Or use the Blade directive
<img src="@studentPhoto($student)" alt="Student">
// Or use the HTML Blade directive
@studentPhotoHtml($student)
```

## Best Practices

1. Never access the `image` field directly; use `student_photo` or the service
2. Always use the helper for uploading images to ensure consistency
3. Use the facade for retrieving images in views
4. Consider using the repository for more complex operations
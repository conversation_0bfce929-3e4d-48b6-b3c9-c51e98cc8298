<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;

/**
 * App\ExamType
 *
 * @property int $id
 * @property int $active_status
 * @property string $title
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $organization_id
 * @property int|null $academic_id
 * @method static \Illuminate\Database\Eloquent\Builder|ExamType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ExamType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ExamType query()
 * @method static \Illuminate\Database\Eloquent\Builder|ExamType whereAcademicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamType whereActiveStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamType whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamType whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamType whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExamType whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class ExamType extends Model
{
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);


    }
    public static function examType($assinged_exam_type){
        try {
            return ExamType::where('id', $assinged_exam_type)->first();
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    }
}

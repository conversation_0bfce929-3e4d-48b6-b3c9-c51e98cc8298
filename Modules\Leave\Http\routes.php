<?php


use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;

Route::group(['middleware' => ['web', 'auth:employee','missedClockOutMiddleware'], 'prefix' => 'workplace/leave', 'namespace' => 'Modules\Leave\Http\Controllers'], function () {




    // Employee leave type
    Route::resource('leave-type', 'LeaveTypeController');

    // Employee leave define
    Route::resource('leave-define', 'LeaveDefineController');

    Route::resource('apply-leave', 'LeaveRequestController');


    // Employee designation
    Route::resource('designation', 'DesignationController');

    Route::resource('approve-leave', 'ApproveLeaveController');
    Route::get('pending-leave', 'ApproveLeaveController@pendingLeave')->name('pending-leave')->middleware('userRolePermission:196');


    Route::post('update-approve-leave', 'ApproveLeaveController@updateApproveLeave');

//    Route::get('/staffNameByRole', 'ApproveLeaveController@staffNameByRole');

    Route::get('view-leave-details-approve/{id}', 'ApproveLeaveController@viewLeaveDetails');
    Route::get('view-leave-details-apply/{id}', 'LeaveRequestController@viewLeaveDetails');

});

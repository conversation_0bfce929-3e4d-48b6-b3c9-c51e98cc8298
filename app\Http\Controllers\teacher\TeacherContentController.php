<?php

namespace App\Http\Controllers\teacher;

use App\Classes;
use App\Notifications\StudyMeterialCreatedNotification;
use App\Role;
use App\Employee;
use App\AssignSubject;
use App\ClassSection;
use App\ContentType;
use App\GeneralSettings;
use App\Section;
use App\Student;
use App\TeacherUploadContent;
use App\User;
use App\YearCheck;
use App\ApiBaseMethod;
use App\Notification;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Validator;
use Modules\RolePermission\Entities\InfixRole;

class TeacherContentController extends Controller
{

    public function __construct()
	{
        $this->middleware('PM');
        // User::checkAuth();
	}

    public function uploadContent(Request $request)
    {
        $input = $request->all();
        //return $request->input();
        if (ApiBaseMethod::checkUrl($request->fullUrl())) {
            $validator = Validator::make($input, [
                'content_title' => "required",
                'content_type' => "required",
                'upload_date' => "required",
                'description' => "required"
            ]);
        }
        //as assignment, st study material, sy sullabus, ot others download

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
        }
        if (empty($request->input('available_for'))) {

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', 'Content Receiver not selected');
            }
        }

        try {
            $fileName = "";
            if ($request->file('attach_file') != "") {
                $file = $request->file('attach_file');
                $fileName = $request->input('created_by') . time() . "." . $file->getClientOriginalExtension();
                // $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/upload_contents/', $fileName);
                $fileName = 'public/uploads/upload_contents/' . $fileName;
            }
            // return $fileName;

            $uploadContents = new TeacherUploadContent();
            $uploadContents->content_title = $request->input('content_title');
            $uploadContents->content_type = $request->input('content_type');
            if ($request->input('available_for') == 'admin') {
                $uploadContents->available_for_admin = 1;
            } elseif ($request->input('available_for') == 'student') {
                if (!empty($request->input('all_classes'))) {
                    $uploadContents->available_for_all_classes = 1;
                } else {
                    $uploadContents->class = $request->input('class');
                    $uploadContents->section = $request->input('section');
                }
            }

            //return $request->input();

            $uploadContents->upload_date = date('Y-m-d', strtotime($request->input('upload_date')));
            $uploadContents->description = $request->input('description');
            $uploadContents->upload_file = $fileName;
            $uploadContents->created_by = $request->input('created_by');
            $uploadContents->organization_id = Auth::user()->organization_id;
            $uploadContents->academic_id = YearCheck::getAcademicId();
            $results = $uploadContents->save();


            if ($request->input('content_type') == 'as') {
                $purpose = 'assignment';
            } elseif ($request->input('content_type') == 'st') {
                $purpose = 'Study Material';
            } elseif ($request->input('content_type') == 'sy') {
                $purpose = 'Syllabus';
            } elseif ($request->input('content_type') == 'ot') {
                $purpose = 'Others Download';
            }


            // foreach ($request->input('available_for') as $value) {
            if ($request->input('available_for') == 'admin') {
                $roles = Role::where('id', '!=', 1)->where('id', '!=', 2)->where('id', '!=', 3)->where('id', '!=', 9)->where(function ($q) {
                $q->orWhere('type', 'System');
            })->get();

                foreach ($roles as $role) {
                    $employees = Employee::where('role_id', $role->id)->get();
                    foreach ($employees as $employee) {
                        $notification = new Notification;
                        $notification->user_id = $employee->id;
                        $notification->role_id = $role->id;
                        $notification->date = date('Y-m-d');
                        $notification->message = $purpose . ' updated';
                        $notification->organization_id = Auth::user()->organization_id;
                        $notification->academic_id = YearCheck::getAcademicId();
                        $notification->save();
                    }
                }
            }
            if ($request->input('available_for') == 'student') {
                if (!empty($request->input('all_classes'))) {
                    $students = Student::select('id')->get();
                    foreach ($students as $student) {
                        $notification = new Notification;
                        $notification->user_id = $student->id;
                        $notification->$role_id == 23;
                        $notification->date = date('Y-m-d');
                        $notification->message = $purpose . ' updated';
                        $notification->organization_id = Auth::user()->organization_id;
                        $notification->academic_id = YearCheck::getAcademicId();
                        $notification->save();
                    }
                } else {
                    $students = Student::select('id')->where('class_id', $request->input('class'))->where('section_id', $request->input('section'))->get();
                    foreach ($students as $student) {
                        $notification = new Notification;
                        $notification->user_id = $student->id;
                        $notification->$role_id == 23;
                        $notification->date = date('Y-m-d');
                        $notification->message = $purpose . ' updated';
                        $notification->organization_id = Auth::user()->organization_id;
                        $notification->academic_id = YearCheck::getAcademicId();
                        $notification->save();
                    }
                }
            }
            // }

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {

                $data = '';

                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function contentList(Request $request)
    {
        try {
            $content_list = DB::table('teacher_upload_contents')
                ->where('available_for_admin', '<>', 0)
                ->get();
            $type = "as assignment, st study material, sy sullabus, ot others download";
            $data = [];
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data['content_list'] = $content_list->toArray();
                $data['type'] = $type;
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function deleteContent(Request $request, $id)
    {
        try {
            $content = DB::table('teacher_upload_contents')->where('id', $id)->delete();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = '';
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function uploadContentEdit($id)
    {
        $editData = TeacherUploadContent::where('organization_id', Auth::user()->organization_id)
            ->where('academic_id',getAcademicId())
            ->where('id',$id)
            ->first();

        if (Auth::user()->role_id != 1 && $editData->created_by != Auth::user()->id) {
            Toastr::error('This Content added by other. you cannot Modify', 'Failed');
            return redirect()->back();
        }
        $sections = Section::where('active_status', 1)->get();
        $contentTypes = ContentType::get();

        if (teacherAccess()) {
            $uploadContents = TeacherUploadContent::where(function ($q) {
                $q->where('created_by', Auth::user()->id)->orWhere('available_for_admin', 1);
            })->get();
        } else {
            $uploadContents = TeacherUploadContent::all();
        }

        if (teacherAccess()) {
            $teacher_info=Employee::where('user_id',Auth::user()->id)->first();
            $classes= AssignSubject::where('teacher_id',$teacher_info->id)->join('classes','classes.id','assign_subjects.class_id')
                ->where('assign_subjects.academic_id', getAcademicId())
                ->where('assign_subjects.active_status', 1)
                ->where('assign_subjects.organization_id',Auth::user()->organization_id)
                ->select('classes.id','class_name')
                ->groupBy('classes.id')
                ->get();
        } else {
            $classes = Classes::all();
        }
        return view('backEnd.teacher.uploadContentList', compact('editData','contentTypes', 'classes','sections', 'uploadContents'));
    }

    public function uploadContentView(Request $request, $id)
    {

        try {
            if (checkAdmin()) {
                $ContentDetails = TeacherUploadContent::find($id);
            }else{
                $ContentDetails = TeacherUploadContent::where('id',$id)->where('organization_id',Auth::user()->organization_id)->first();
            }

            return view('backEnd.teacher.uploadContentDetails', compact('ContentDetails'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function saveUploadContent(Request $request)
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $maxFileSize = GeneralSettings::first('file_size')->file_size;

        if (isset($request->available_for)) {
            foreach ($request->available_for as $value) {
                if ($value == 'student') {
                    if (!isset($request->all_classes)) {
                        $request->validate([
                            'content_title' => "required|max:200",
                            'content_type' => "required",
                            'upload_date' => "required",
                            'content_file' => "required|mimes:pdf,doc,docx,jpg,jpeg,png,mp4,mp3,txt",
                            'class' => "required",
                            'section' => "required",
                        ]);
                    } else {
                        $request->validate([
                            'content_title' => "required|max:200",
                            'content_type' => "required",
                            'upload_date' => "required",
                            'content_file' => "required|mimes:pdf,doc,docx,jpg,jpeg,png,mp4,mp3,txt",
                        ]);
                    }
                }
            }
        } else {
            $request->validate(
                [
                    'content_title' => "required:max:200",
                    'content_type' => "required",
                    'available_for' => 'required|array',
                    'upload_date' => "required",
                    'content_file' => "required|mimes:pdf,doc,docx,jpg,jpeg,png,mp4,mp3",
                ],
                [
                    'available_for.required' => 'At least one checkbox required!',
                ]
            );
        }
        try {
            $fileName = "";
            $imagemimes = ['image/png'];
            $videomimes = ['video/mp4'];
            $audiomimes = ['audio/mp3'];

            $maxFileSize = GeneralSettings::first('file_size')->file_size;
            $file = $request->file('content_file');
            $fileSize =  filesize($file);
            $fileSizeKb = ($fileSize / 1000000);
            if($fileSizeKb >= $maxFileSize){
                Toastr::error( 'Max upload file size '. $maxFileSize .' Mb is set in system', 'Failed');
                return redirect()->back();
            }




            if ( ($request->file('content_file') != "")  && (in_array($file->getMimeType() ,$videomimes))) {
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/upload_contents/', $fileName);
                $fileName = 'public/uploads/upload_contents/' . $fileName;

            }

            elseif ($file != "") {
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/upload_contents/', $fileName);
                $fileName = 'public/uploads/upload_contents/' . $fileName;
            }

            $y = '2012';
            $m = '2012';
            $d = '2012';

            if($request->section == "all"){
                $sections = ClassSection::where('class_id', $request->class)
                    ->where('organization_id', Auth::user()->organization_id)->get();
                foreach($sections as $section){
                    $uploadContents = new TeacherUploadContent();
                    $uploadContents->content_title = $request->content_title;
                    $uploadContents->content_type = $request->content_type;
                    $uploadContents->organization_id = Auth::user()->organization_id;
                    $uploadContents->organization_id = config('organization_id');
                    $uploadContents->class = $request->class;
                    $uploadContents->section = $section->section_id;
                    $uploadContents->upload_date = date('Y-m-d', strtotime($request->upload_date));
                    $uploadContents->description = $request->description;
                    $uploadContents->source_url = $request->source_url;
                    $uploadContents->upload_file = $fileName;
                    $uploadContents->created_by = Auth()->user()->id;
                    $results = $uploadContents->save();
                }

            }
            else {
                $uploadContents = new TeacherUploadContent();
                $uploadContents->content_title = $request->content_title;
                $uploadContents->content_type = $request->content_type;
                $uploadContents->organization_id = Auth::user()->organization_id;
                $uploadContents->organization_id = config('organization_id');

                foreach ($request->available_for as $value) {
                    if ($value == 'admin') {
                        $uploadContents->available_for_admin = 1;
                    }

                    if ($value == 'student') {
                        if (isset($request->all_classes)) {
                            $uploadContents->available_for_all_classes = 1;
                        } else {
                            $uploadContents->class = $request->class;
                            $uploadContents->section = $request->section;
                        }
                    }
                }

                $uploadContents->upload_date = date('Y-m-d', strtotime($request->upload_date));
                $uploadContents->description = $request->description;
                $uploadContents->source_url = $request->source_url;
                $uploadContents->upload_file = $fileName;
                $uploadContents->created_by = Auth()->user()->id;
                $results = $uploadContents->save();
            }



            if ($request->content_type == 'as') {
                $purpose = 'assignment';
            } elseif ($request->content_type == 'st') {
                $purpose = 'Study Material';
            } elseif ($request->content_type == 'sy') {
                $purpose = 'Syllabus';
            } elseif ($request->content_type == 'ot') {
                $purpose = 'Others Download';
            }

            foreach ($request->available_for as $value) {
                if ($value == 'admin') {
                    $roles = InfixRole::where('id', '=', 1) /* ->where('id', '!=', 2)->where('id', '!=', 3)->where('id', '!=', 9) */->where(function ($q) {
                        $q->where('organization_id', Auth::user()->organization_id)->orWhere('type', 'System');
                    })->get();
                    foreach ($roles as $role) {
                        $employees = Employee::where('role_id', $role->id)->get();
                        foreach ($employees as $employee) {
                            $notification = new Notification;
                            $notification->user_id = $employee->user_id;
                            $notification->role_id = $role->id;
                            $notification->organization_id = Auth::user()->organization_id;
                            $notification->organization_id = config('organization_id');
                            if ($request->content_type == 'as') {
                                $notification->url = 'assignment-list';
                            } elseif ($request->content_type == 'st') {
                                $notification->url = 'study-metarial-list';
                            } elseif ($request->content_type == 'sy') {
                                $notification->url = 'syllabus-list';
                            } elseif ($request->content_type == 'ot') {
                                $notification->url = 'other-download-list';
                            }
                            $notification->date = date('Y-m-d');
                            $notification->message = $purpose . ' Uploaded';
                            $notification->save();

                            $user=User::find($notification->user_id);
                            \Illuminate\Support\Facades\Notification::send($user, new StudyMeterialCreatedNotification($notification));
                        }
                    }
                }
                if ($value == 'student') {
                    if (isset($request->all_classes)) {

                        $students = Student::select('id', 'user_id')->get();
                        foreach ($students as $student) {
                            $notification = new Notification;
                            $notification->user_id = $student->user_id;
                            $notification->role_id = 2;
                            $notification->organization_id = Auth::user()->organization_id;
                            $notification->organization_id = config('organization_id');
                            if ($request->content_type == 'as') {
                                $notification->url = 'student-assignment';
                            } elseif ($request->content_type == 'st') {
                                $notification->url = 'student-study-material';
                            } elseif ($request->content_type == 'sy') {
                                $notification->url = 'student-syllabus';
                            } elseif ($request->content_type == 'ot') {
                                $notification->url = 'student-others-download';
                            }
                            $notification->date = date('Y-m-d');
                            $notification->message = $purpose . ' Uploaded';
                            $notification->save();

                            $user=User::find($notification->user_id);
                            Notification::send($user, new StudyMeterialCreatedNotification($notification));
                        }
                    }

                    elseif ( (!is_null($request->class)) &&   ($request->section == 'all')) {

                        $students = Student::select('id', 'user_id')->where('class_id', $request->class)->get();
                        foreach ($students as $student) {
                            $notification = new Notification;
                            $notification->user_id = $student->user_id;
                            $notification->role_id = 2;
                            $notification->organization_id = Auth::user()->organization_id;
                            $notification->organization_id = config('organization_id');
                            if ($request->content_type == 'as') {
                                $notification->url = 'student-assignment';
                            } elseif ($request->content_type == 'st') {
                                $notification->url = 'student-study-material';
                            } elseif ($request->content_type == 'sy') {
                                $notification->url = 'student-syllabus';
                            } elseif ($request->content_type == 'ot') {
                                $notification->url = 'student-others-download';
                            }
                            $notification->date = date('Y-m-d');
                            $notification->message = $purpose . ' Uploaded';
                            $notification->save();

                            $user=User::find($notification->user_id);
                            Notification::send($user, new StudyMeterialCreatedNotification($notification));
                        }
                    }


                    else {

                        $students = Student::select('id')->where('class_id', $request->class)->where('section_id', $request->section)->get();
                        foreach ($students as $student) {
                            $notification = new Notification;
                            $notification->user_id = $student->user_id;
                            $notification->role_id = 2;
                            if ($request->content_type == 'as') {
                                $notification->url = 'student-assignment';
                            } elseif ($request->content_type == 'st') {
                                $notification->url = 'student-study-material';
                            } elseif ($request->content_type == 'sy') {
                                $notification->url = 'student-syllabus';
                            } elseif ($request->content_type == 'ot') {
                                $notification->url = 'student-others-download';
                            }
                            $notification->date = date('Y-m-d');
                            $notification->message = $purpose . ' Uploaded';
                            $notification->organization_id = Auth::user()->organization_id;
                            $notification->organization_id = config('organization_id');
                            $notification->save();

                            $user=User::find($notification->user_id);
                            Notification::send($user, new StudyMeterialCreatedNotification($notification));
                        }
                    }
                }

            }

            if ($results) {
                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function updateUploadContent(Request $request)
    {

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $maxFileSize = GeneralSettings::first('file_size')->file_size;

        if (isset($request->available_for)) {
            foreach ($request->available_for as $value) {
                if ($value == 'student') {
                    if (!isset($request->all_classes)) {
                        $request->validate([
                            'content_title' => "required|max:200",
                            'content_type' => "required",
                            'upload_date' => "required",
                            'content_file' => "sometimes|required|mimes:pdf,doc,docx,jpg,jpeg,png,mp4,mp3,txt",
                            'class' => "required",
                            'section' => "required",
                        ]);
                    } else {
                        $request->validate([
                            'content_title' => "required|max:200",
                            'content_type' => "required",
                            'upload_date' => "required",
                            'content_file' => "sometimes|required|mimes:pdf,doc,docx,jpg,jpeg,png,mp4,mp3,txt",
                        ]);
                    }
                }
            }
        } else {
            $request->validate(
                [
                    'content_title' => "required:max:200",
                    'content_type' => "required",
                    'available_for' => 'required|array',
                    'upload_date' => "required",
                    'content_file' => "sometimes|required|mimes:pdf,doc,docx,jpg,jpeg,png,mp4,mp3",
                ],
                [
                    'available_for.required' => 'At least one checkbox required!',
                ]
            );
        }
        try {
            $fileName = "";
            $imagemimes = ['image/png'];
            $videomimes = ['video/mp4'];
            $audiomimes = ['audio/mp3'];

            $maxFileSize = GeneralSettings::first('file_size')->file_size;
            $file = $request->file('content_file');
            $fileSize =  filesize($file);
            $fileSizeKb = ($fileSize / 1000000);
            if($fileSizeKb >= $maxFileSize){
                Toastr::error( 'Max upload file size '. $maxFileSize .' Mb is set in system', 'Failed');
                return redirect()->back();
            }




            if ( ($request->file('content_file') != "")  && (in_array($file->getMimeType() ,$videomimes))) {
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/upload_contents/', $fileName);
                $fileName = 'public/uploads/upload_contents/' . $fileName;

            }

            elseif ($file != "") {
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/upload_contents/', $fileName);
                $fileName = 'public/uploads/upload_contents/' . $fileName;
            }

            $y = '2012';
            $m = '2012';
            $d = '2012';
            $uploadContents = TeacherUploadContent::where('id',$request->id)->first();
            $uploadContents->content_title = $request->content_title;
            $uploadContents->content_type = $request->content_type;
            $uploadContents->organization_id = Auth::user()->organization_id;
            $uploadContents->organization_id = config('organization_id');

            if (in_array('admin',$request->available_for)) {
                $uploadContents->available_for_admin = 1;
            }else{
                $uploadContents->available_for_admin = null;
            }

            if (in_array('student',$request->available_for)) {
                if (isset($request->all_classes)) {
                    $uploadContents->available_for_all_classes = 1;
                    $remove_cls_sec = TeacherUploadContent::where('id',$request->id)->first();
                    $remove_cls_sec->class = null;
                    $remove_cls_sec->section = null;
                    $remove_cls_sec->save();

                } else {
                    $remove_all_cls = TeacherUploadContent::where('id',$request->id)->first();
                    $remove_all_cls->available_for_all_classes = null;
                    $remove_all_cls->save();

                    $uploadContents->class = $request->class;
                    $uploadContents->section = $request->section;
                }
            }else{
                $uploadContents->class = null;
                $uploadContents->section = null;
                $uploadContents->available_for_all_classes = null;
            }

            // foreach ($request->available_for as $value) {
            //     if ($value == 'admin') {

            //         $uploadContents->available_for_admin = 1;

            //     }else{
            //         $uploadContents->available_for_admin = null;
            //     }

            //     if ($value == 'student') {
            //         if (isset($request->all_classes)) {
            //             $uploadContents->available_for_all_classes = 1;
            //             $remove_cls_sec = TeacherUploadContent::where('id',$request->id)->first();
            //             $remove_cls_sec->class = null;
            //             $remove_cls_sec->section = null;
            //             $remove_cls_sec->save();

            //         } else {
            //             $remove_all_cls = TeacherUploadContent::where('id',$request->id)->first();
            //             $remove_all_cls->available_for_all_classes = null;
            //             $remove_all_cls->save();

            //             $uploadContents->class = $request->class;
            //             $uploadContents->section = $request->section;
            //         }
            //     }else {
            //         $uploadContents->class = null;
            //         $uploadContents->section = null;
            //         $uploadContents->available_for_all_classes = null;
            //     }
            // }

            $uploadContents->upload_date = date('Y-m-d', strtotime($request->upload_date));
            $uploadContents->description = $request->description;
            $uploadContents->source_url = $request->source_url;
            if ($request->file('content_file') != "") {
                $uploadContents->upload_file = $fileName;
            }

            $uploadContents->created_by = Auth()->user()->id;
            // $uploadContents->created_at = '2012-11-26 13:04:39';
            $results = $uploadContents->save();
            // return  $results;

            if ($request->content_type == 'as') {
                $purpose = 'assignment';
            } elseif ($request->content_type == 'st') {
                $purpose = 'Study Material';
            } elseif ($request->content_type == 'sy') {
                $purpose = 'Syllabus';
            } elseif ($request->content_type == 'ot') {
                $purpose = 'Others Download';
            }

            foreach ($request->available_for as $value) {
                if ($value == 'admin') {
                    $roles = InfixRole::where('id', '=', 1) /* ->where('id', '!=', 2)->where('id', '!=', 3)->where('id', '!=', 9) */->where(function ($q) {
                        $q->where('organization_id', Auth::user()->organization_id)->orWhere('type', 'System');
                    })->get();
                    foreach ($roles as $role) {
                        $employees = Employee::where('role_id', $role->id)->get();
                        foreach ($employees as $employee) {
                            $notification = new Notification;
                            $notification->user_id = $employee->user_id;
                            $notification->role_id = $role->id;
                            $notification->organization_id = Auth::user()->organization_id;
                            $notification->organization_id = config('organization_id');
                            if ($request->content_type == 'as') {
                                $notification->url = 'assignment-list';
                            } elseif ($request->content_type == 'st') {
                                $notification->url = 'study-metarial-list';
                            } elseif ($request->content_type == 'sy') {
                                $notification->url = 'syllabus-list';
                            } elseif ($request->content_type == 'ot') {
                                $notification->url = 'other-download-list';
                            }
                            $notification->date = date('Y-m-d');
                            $notification->message = $purpose . ' updated';
                            $notification->save();

                            $user=User::find($notification->user_id);
                            Notification::send($user, new StudyMeterialCreatedNotification($notification));
                        }
                    }
                }
                if ($value == 'student') {
                    if (isset($request->all_classes)) {
                        $students = Student::select('id', 'user_id')->get();
                        foreach ($students as $student) {
                            $notification = new Notification;
                            $notification->user_id = $student->id;
                            $notification->role_id = 2;
                            $notification->organization_id = Auth::user()->organization_id;
                            if ($request->content_type == 'as') {
                                $notification->url = 'student-assignment';
                            } elseif ($request->content_type == 'st') {
                                $notification->url = 'student-study-material';
                            } elseif ($request->content_type == 'sy') {
                                $notification->url = 'student-syllabus';
                            } elseif ($request->content_type == 'ot') {
                                $notification->url = 'student-others-download';
                            }
                            $notification->date = date('Y-m-d');
                            $notification->message = $purpose . ' updated';
                            $notification->save();

                            $user=User::find($notification->user_id);
                            Notification::send($user, new StudyMeterialCreatedNotification($notification));
                        }
                    } else {
                        $students = Student::select('id')->where('class_id', $request->class)->where('section_id', $request->section)->get();
                        foreach ($students as $student) {
                            $notification = new Notification;
                            $notification->user_id = $student->id;
                            $notification->role_id = 2;
                            if ($request->content_type == 'as') {
                                $notification->url = 'student-assignment';
                            } elseif ($request->content_type == 'st') {
                                $notification->url = 'student-study-material';
                            } elseif ($request->content_type == 'sy') {
                                $notification->url = 'student-syllabus';
                            } elseif ($request->content_type == 'ot') {
                                $notification->url = 'student-others-download';
                            }
                            $notification->date = date('Y-m-d');
                            $notification->message = $purpose . ' updated';
                            $notification->organization_id = Auth::user()->organization_id;
                            $notification->save();

                            $user=User::find($notification->user_id);
                            Notification::send($user, new StudyMeterialCreatedNotification($notification));
                        }
                    }
                }

            }

            if ($results) {
                Toastr::success('Update Operation successful', 'Success');
                return redirect()->route('upload-content');
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    
    
}

@extends('layouts.hound')

@section('mytitle', 'Leave Request Details')

@section('content')
<div class="panel panel-default card-view">
    <div class="panel-heading clearfix">
        <h4 class="pull-left">Leave Request: {{ $leave_request->employee->name }} <small class="btn-xs btn-success">{{ $leave_request->status }}</small></h4>
        <div class="pull-right">
            <a href="{{ route('leave-requests.index') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
        </div>
    </div>
    <div class="panel-body">
        <div class="table-responsive container">
           <table class="table">
                <tr>
                    <td>Request By</td>
                    <td>{{ $leave_request->creator->name }}</td>
                </tr>
                <tr>
                    <td>Leave Request Type</td>
                    <td>{{ $leave_request->type }}</td>
                </tr>
                <tr>
                    <td>Date and time</td>
                    <td>From {{ $leave_request->from_date }} to {{ $leave_request->to_date }}</td>
                </tr>
                @if($leave_request->status == 'approved')
                <tr>
                    <td>Allowed Leave Duration</td>
                    <td>{{ $leave_request->allowed_time }} hours</td>
                </tr>
                @endif
                <tr>
                    <td>Details</td>
                    <td>{{ $leave_request->details }}</td>
                </tr>
           </table>
           @if($leave_request->status == 'waiting_approval')
           <div class="text-center">
           {!! Form::open([
                    'method' => 'PATCH',
                    'route' => ['leave-requests.update', $leave_request->id],
                ]) !!}
               <table class="table">
               <tr>
                    <td>Allowed Leave Duration (Daily/That day) </td>
                    <td>
                        <div class="form-group @if ($errors->has('allowed_time')) has-error @endif">                        
                        {!! Form::text('allowed_time', null, ['class' => 'form-control']) !!}
                        @if ($errors->has('allowed_time')) <p class="help-block">{{ $errors->first('allowed_time') }}</p> @endif                    
                        </div>
               </tr>
                <tr>
                    <td>Note</td>
                    <td>
                        <div class="form-group @if ($errors->has('note')) has-error @endif">
                            {!! Form::textarea('note' , null , ['class' => 'form-control'] ) !!}
                            @if ($errors->has('note')) <p class="help-block">{{ $errors->first('note') }}</p> @endif                    
                        </div>
                    </td>
                </tr>
                </table>
                <button class="btn btn-success" value="approved" name="status">Approve</button> 
                <button class="btn btn-warning" value="rejected" name="status">Reject</button> 

                @if($leave_request->creator->id == auth()->user()->id)
                    <button class="btn btn-danger" value="withdrawn" name="status">Withdraw Request</button> 
                @endif
                {!! Form::close() !!}
           
           </div>
           @endif

        </div>

    </div>
</div>

@endsection
<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Exam
 *
 * @property int $id
 * @property float|null $exam_mark
 * @property int $active_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $exam_type_id
 * @property int|null $class_id
 * @property int|null $section_id
 * @property int|null $subject_id
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $organization_id
 * @property int|null $academic_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\ExamSetup[] $GetExamSetup
 * @property-read int|null $get_exam_setup_count
 * @property-read \App\ExamType|null $GetExamTitle
 * @property-read \App\Section|null $GetSectionName
 * @property-read \App\Subject|null $GetSubjectName
 * @method static \Illuminate\Database\Eloquent\Builder|Exam newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Exam newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Exam query()
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereAcademicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereActiveStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereClassId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereExamMark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereExamTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereSectionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereSubjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Exam whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class Exam extends Model
{

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);


    }
    public function getClassName(){
		return $this->belongsTo('App\Class', 'class_id', 'id');
	}
	public function GetSectionName(){
		return $this->belongsTo('App\Section', 'section_id', 'id');
	}
	public function GetSubjectName(){
		return $this->belongsTo('App\Subject', 'subject_id', 'id');
	}
	public function GetExamTitle(){
		return $this->belongsTo('App\ExamType', 'exam_type_id', 'id');
	}


	public function GetExamSetup(){
		return $this->hasMany('App\ExamSetup', 'exam_id', 'id');
	}


	public static function getMarkDistributions($ex_id, $class_id, $section_id, $subject_id){
		try {
			$data = ExamSetup::where([
				['exam_term_id', $ex_id],
				['class_id', $class_id],
				['section_id', $section_id],
				['subject_id', $subject_id]
			])->get();

		return $data;
		} catch (\Exception $e) {
			$data=[];
			return $data;
		}
	} 


	public static function getMarkREgistered($ex_id, $class_id, $section_id, $subject_id){
		try {
			$data = MarkStore::where([
				['exam_term_id', $ex_id],
				['class_id', $class_id],
				['section_id', $section_id],
				['subject_id', $subject_id]
			])->first();

		return $data;
		} catch (\Exception $e) {
			$data=[];
			return $data;
		}
	} 

}

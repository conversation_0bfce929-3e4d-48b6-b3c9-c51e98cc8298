# Viewer Account Functionality Documentation

## 1. Overview and Purpose

The **Viewer Account** (internally referred to as "System Viewer") is a special, read-only user role designed for stakeholders, investors, and potential clients. Its primary purpose is to provide a comprehensive, live demonstration of the entire Itqan ERP system's capabilities without any risk of accidental data modification, corruption, or unauthorized changes.

A user with a Viewer Account can navigate the entire application, view all data across different centers and modules, and see reports and dashboards as a top-level administrator would, but is strictly prohibited from performing any write operations (create, update, delete).

---

## 2. The Core Problem: Data Blackout

The initial implementation of the Viewer Account faced a critical architectural flaw that resulted in a "data blackout." Viewers could log in but saw no data in any tables, reports, or dashboards.

### Root Cause

The root cause was a pervasive authorization pattern used in over 200 places throughout the codebase. The system was designed to filter data based on user-associated entities, such as the centers a user is assigned to. A common problematic pattern looked like this:

```php
// This was a common problematic pattern, where 'center_id' is an example of a data-scoping column
$associatedIds = auth()->user()->someRelationship->pluck('id')->toArray();

$query->whereIn('associated_id_column', $associatedIds);
```

System Viewer accounts, by design, were not associated with any specific entities (like centers) in a way that would grant them universal data access. Consequently, `auth()->user()->someRelationship->pluck('id')->toArray()` (or specifically `auth()->user()->center->pluck('id')->toArray()` in many cases) returned an empty array `[]`. This caused every data query to fail, effectively showing no data to the viewer.

---

## 3. The Architectural Solution: The "Nuclear Fix"

To solve this systemic issue without refactoring hundreds of controller methods, a multi-layered architectural solution was implemented. This solution transparently expands the data access for viewers at the framework level while maintaining strict read-only permissions.

### Layer 1: Middleware-based Relationship Patching

-   **Component**: `app/Http/Middleware/SystemViewerDataAccess.php`
-   **Function**: This middleware intercepts incoming requests from a System Viewer. It dynamically patches the authenticated user object *in-memory* for the duration of the request, making its `center` and `allowedCenters` relationships return *all* centers within the organization.
-   **Result**: The problematic code `auth()->user()->center->pluck('id')->toArray()` now transparently returns an array of all center IDs, allowing existing queries to work without modification.

### Layer 2: Custom Relationship Classes

-   **Component**: `app/Relations/SystemViewerBelongsToMany.php`
-   **Function**: To ensure that the dynamically patched relationships still conform to Laravel's expected `Relationship` instance contract, custom relationship classes were created. This prevents framework-level errors.

### Layer 3: The `SystemViewerAccess` Trait

-   **Component**: `app/Traits/SystemViewerAccess.php`
-   **Function**: This trait centralizes all the core logic for identifying a System Viewer and defining their access patterns. It provides reusable methods like `isSystemViewer()` and `getAccessibleCenterIds()`.

### Layer 4: Model-Level Relationship Overrides

-   **Components**: `app/User.php`, `app/Employee.php`
-   **Function**: The `center()` and `allowedCenters()` methods within these core models are overridden. They use the `SystemViewerAccess` trait to check if the current user is a viewer. If so, they return a new query builder that encompasses all centers, otherwise, they return the original relationship definition. This was the initial approach, which is now complemented by the middleware patching for universal coverage.

---

## 4. How It Works: A Detailed Flow

This diagram illustrates the data access flow for a System Viewer compared to a regular user.

```mermaid
sequenceDiagram
    participant Viewer as Viewer Account
    participant Middleware as SystemViewerDataAccess
    participant Controller
    participant Model
    participant Database

    Viewer->>Controller: Request to view /students
    activate Controller
    Controller->>Middleware: Request passes through middleware
    activate Middleware
    Middleware->>Middleware: If user is Viewer, patch auth()->user()->center relationship
    Middleware-->>Controller: Continue request with patched user
    deactivate Middleware

    Controller->>Model: getStudents()
    activate Model
    Model->>Model: Query: `Student::whereIn('center_id', auth()->user()->center->pluck('id')->toArray())`
    Note right of Model: `auth()->user()->center` now returns ALL centers for the viewer.
    Model->>Database: SELECT * FROM students WHERE center_id IN (1,2,3,...)
    activate Database
    Database-->>Model: Returns all students
    deactivate Database
    Model-->>Controller: Returns collection of all students
    deactivate Model
    Controller-->>Viewer: Display page with all student data
    deactivate Controller
```

---

## 5. Key Features & Capabilities

-   **Complete Read-Only Access**: System Viewers are fundamentally blocked from making any `POST`, `PUT`, `PATCH`, or `DELETE` requests via dedicated middleware, ensuring data integrity.
-   **Full Data Visibility**: Viewers have the same level of data access as a "Managing Director," seeing records from all centers and departments.
-   **Transparent Operation**: The solution works without changing hundreds of existing controller methods, making it highly maintainable.
-   **Secure by Design**: The system leverages Spatie's role-based permissions. A `Gate::before()` check grants viewers permission for any `view`, `show`, or `index` action while implicitly denying all others.
-   **Ad-Blocker Compatibility**: Viewer-related URLs were changed from `/stats/` to `/data/` to prevent them from being blocked by common ad-blocker extensions.
-   **Viewer Analytics**: The system includes detailed page view tracking and interest analysis for viewer accounts, providing valuable insights into which features are being demonstrated most.

---

## 6. Core Technical Components

The Viewer Account functionality is primarily implemented in the following files:

-   **Trait**: `app/Traits/SystemViewerAccess.php` (Centralized logic)
-   **Middleware**: `app/Http/Middleware/SystemViewerDataAccess.php` (Patches user relationships for data access)
-   **Middleware**: `app/Http/Middleware/BlockWriteRequestsForSystemViewer.php` (Ensures read-only access by blocking write-request methods)
-   **Models**: `app/User.php` & `app/Employee.php` (Contain relationship overrides)
-   **Service Provider**: `app/Providers/SystemViewerServiceProvider.php` (Handles framework-level integration and global scopes)
-   **Custom Relations**: `app/Relations/SystemViewerBelongsToMany.php` (Ensures compatibility with Laravel's ORM)

---

## 7. How to Create and Use a Viewer Account

1.  Create a new user in the system.
2.  Assign the user the specific role: `system_viewer_{organization_id}_`. For example, for organization ID 2, the role is `system_viewer_2_`.
3.  Once the user logs in, they will automatically have the read-only, full-access capabilities of the Viewer Account.

This documentation provides a comprehensive overview of the Viewer Account functionality. It is a critical feature for demonstrating the system's capabilities securely and effectively.

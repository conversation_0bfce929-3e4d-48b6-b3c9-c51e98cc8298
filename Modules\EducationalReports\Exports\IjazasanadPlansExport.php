<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\IjazasanadMemorizationPlan;
use App\MoshafSurah;
use App\Student;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * IjazasanadPlansExport creates a single aggregated Plans sheet for Ijazah & Sanad programs.
 * 
 * Purpose: Export monthly Ijazah & Sanad memorization plans across multiple classes in a single Excel sheet.
 * Data source: ijazasanad_memorization_plans table with student and class relationships.
 * Calculations: Uses stored procedures CountMemorizedNumberofPagesForward/Backward for planned pages.
 * Context: Part of program-specific export system; complements existing Memorization & Revision exports.
 * Output: Single sheet with columns for Center, Class, Student, Level, Plan Range, Juz, Pages, Direction, Status.
 */
final class IjazasanadPlansExport implements WithTitle, WithEvents
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get plans data aggregated across all classes
     */
    private function getPlansData(): array
    {
        $classIds = $this->filters['classIds'];
        $month = $this->filters['month'];
        $year = $this->filters['year'];
        $studentIds = $this->filters['studentIds'] ?? [];

        $planYearMonth = sprintf('%d-%02d', $year, $month);

        // Load plans with related data
        $query = IjazasanadMemorizationPlan::with([
            'student:id,full_name',
            'class.center',
            'class:id,class_code,center_id'
        ])
        ->whereIn('class_id', $classIds)
        ->where(function ($q) use ($month, $year, $planYearMonth) {
            $q->where('plan_year_and_month', $planYearMonth)
              ->orWhere(function ($q2) use ($month, $year) {
                  $q2->whereYear('start_date', $year)
                     ->whereMonth('start_date', $month);
              })
              ->orWhere(function ($q3) use ($month, $year) {
                  $q3->whereYear('created_at', $year)
                     ->whereMonth('created_at', $month);
              });
        })
        ->where('status', 'active');

        if (!empty($studentIds)) {
            $query->whereIn('student_id', $studentIds);
        }

        $plans = $query->orderBy('class_id')
                      ->orderBy('student_id')
                      ->get();

        $results = [];
        foreach ($plans as $plan) {
            $centerName = $plan->class->center->name ?? $plan->class->center->location ?? 'Unknown Center';
            $className = $plan->class->name ?? 'Unknown Class';
            $studentName = $plan->student->full_name ?? 'Unknown Student';

            // Build plan range display
            $planRange = $this->formatPlanRange($plan);
            
            // Build Juz range display
            $juzRange = $this->formatJuzRange($plan);

            // Calculate planned pages using stored procedures
            $plannedPages = $this->calculatePlannedPages($plan);

            // Determine level (simplified - can be enhanced based on student program levels)
            $level = $this->determineLevelFromPlan($plan);

            $results[] = [
                'center_name' => $centerName,
                'class_name' => $className,
                'student_name' => $studentName,
                'level' => $level,
                'plan_range' => $planRange,
                'juz_range' => $juzRange,
                'planned_pages' => $plannedPages,
                'study_direction' => ucfirst($plan->study_direction ?? 'forward'),
                'status' => ucfirst($plan->status ?? 'active'),
                'start_date' => $plan->start_date ? $plan->start_date->format('Y-m-d') : '',
                'end_date' => $plan->end_date ? $plan->end_date->format('Y-m-d') : '',
                'created_by' => $plan->created_by ?? '',
                'approved_by' => $plan->approved_by ?? ''
            ];
        }

        return $results;
    }

    /**
     * Format plan range for display (Surah:Ayat format)
     */
    private function formatPlanRange($plan): string
    {
        if (!$plan->start_from_surat || !$plan->start_from_ayat || !$plan->to_surat || !$plan->to_ayat) {
            return '—';
        }

        $fromSurah = $this->getSurahName($plan->start_from_surat);
        $toSurah = $this->getSurahName($plan->to_surat);

        if ($plan->start_from_surat === $plan->to_surat) {
            return "{$fromSurah} {$plan->start_from_ayat}–{$plan->to_ayat}";
        }

        return "{$fromSurah}:{$plan->start_from_ayat} – {$toSurah}:{$plan->to_ayat}";
    }

    /**
     * Format Juz range for display
     */
    private function formatJuzRange($plan): string
    {
        if (!$plan->from_surat_juz_id || !$plan->to_surat_juz_id) {
            return '—';
        }

        if ($plan->from_surat_juz_id === $plan->to_surat_juz_id) {
            return "Juz {$plan->from_surat_juz_id}";
        }

        return "Juz {$plan->from_surat_juz_id}–{$plan->to_surat_juz_id}";
    }

    /**
     * Calculate planned pages using stored procedures
     */
    private function calculatePlannedPages($plan): int
    {
        if (!$plan->start_from_surat || !$plan->start_from_ayat || !$plan->to_surat || !$plan->to_ayat) {
            return 0;
        }

        try {
            if ($plan->study_direction === 'backward') {
                $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $plan->start_from_surat,
                    $plan->start_from_ayat,
                    $plan->to_surat,
                    $plan->to_ayat
                ]);
                
                return $result[0]->numberofPagesSum ?? 0;
            } else {
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $plan->start_from_surat,
                    $plan->start_from_ayat,
                    $plan->to_surat,
                    $plan->to_ayat
                ]);

                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                return $results[0]->number_of_pages_sum ?? 0;
            }
        } catch (\Exception $e) {
            \Log::error('Error calculating planned pages for Ijazasanad plan: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get Surah name by ID
     */
    private function getSurahName(int $surahId): string
    {
        $surah = MoshafSurah::where('id', $surahId)->first();
        if (!$surah) {
            return "Surah {$surahId}";
        }
        return "{$surah->id}. {$surah->eng_name}";
    }

    /**
     * Determine level from plan data (simplified)
     */
    private function determineLevelFromPlan($plan): string
    {
        // Check if plan has lesson-based fields (Level 1 indicators)
        if ($plan->talqeen_from_lesson || $plan->revision_from_lesson || 
            $plan->jazariyah_from_lesson || $plan->seminars_from_lesson) {
            return 'Level 1';
        }
        
        // Check if plan has Surah/Ayat fields (Level 2 indicators)
        if ($plan->start_from_surat && $plan->start_from_ayat) {
            return 'Level 2';
        }

        return 'Unknown';
    }

    /**
     * Get table headings
     */
    private function getTableHeadings(): array
    {
        return [
            'Center',
            'Class',
            'Student',
            'Level',
            'Plan Range (Surah:Ayat)',
            'Juz Range',
            'Planned Pages',
            'Study Direction',
            'Status',
            'Start Date',
            'End Date',
            'Created By',
            'Approved By'
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Ijazah & Sanad Plans';
    }

    /**
     * Register events for creating the styled table
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $this->createStyledTable($event->sheet);
            },
        ];
    }

    /**
     * Create styled table with data
     */
    private function createStyledTable($sheet)
    {
        $worksheet = $sheet->getDelegate();
        
        // Get data and headings
        $plansData = $this->getPlansData();
        $headings = $this->getTableHeadings();

        // Set main title
        $monthName = $this->filters['monthName'];
        $year = $this->filters['year'];
        $classNames = collect($this->filters['classes'])->pluck('class_code')->join(', ');
        
        $title = "IJAZAH & SANAD MONTHLY PLANS - {$classNames} - {$monthName} {$year}";
        $worksheet->setCellValue('A1', $title);
        $worksheet->mergeCells('A1:M1');

        // Style main title
        $worksheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 16, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '1f4e79']]
        ]);

        $currentRow = 3;

        // Set headers
        $col = 'A';
        foreach ($headings as $heading) {
            $worksheet->setCellValue($col . $currentRow, $heading);
            $col++;
        }

        // Style headers
        $worksheet->getStyle("A{$currentRow}:M{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2f75b5']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $dataStartRow = $currentRow + 1;
        $currentDataRow = $dataStartRow;

        // Add data rows
        foreach ($plansData as $row) {
            $col = 'A';
            foreach ($row as $value) {
                $worksheet->setCellValue($col . $currentDataRow, $value);
                $col++;
            }
            $currentDataRow++;
        }

        // Style data rows
        if (count($plansData) > 0) {
            $dataEndRow = $currentDataRow - 1;
            $worksheet->getStyle("A{$dataStartRow}:M{$dataEndRow}")->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ]);

            // Center-align specific columns
            $worksheet->getStyle("D{$dataStartRow}:D{$dataEndRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Level
            $worksheet->getStyle("G{$dataStartRow}:G{$dataEndRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Planned Pages
            $worksheet->getStyle("H{$dataStartRow}:H{$dataEndRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Study Direction
            $worksheet->getStyle("I{$dataStartRow}:I{$dataEndRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Status
        } else {
            // No data message
            $worksheet->setCellValue("A{$dataStartRow}", 'No Ijazah & Sanad plans found for the specified criteria');
            $worksheet->mergeCells("A{$dataStartRow}:M{$dataStartRow}");
            $worksheet->getStyle("A{$dataStartRow}")->applyFromArray([
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'font' => ['italic' => true],
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ]
            ]);
        }

        // Auto-size columns
        foreach (range('A', 'M') as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }
    }
}

<div class="col-md-12">
    @if ($errors->any())
    <ul class="alert alert-danger">
        @foreach ($errors->all() as $error)
        <li>{{ $error }}</li>
        @endforeach
    </ul>
    @endif
    <div class="">
        <div class="tab-content">
            @foreach(config('app.locales') as $key => $language)
            <div class="clearfix form-control" id="{{$language}}">
                <h5>{{ get_language_name($language) }}</h5>
                <div class="col-md-12">
                    <div class="form-group {{ $errors->has('translate.'.$language.'.title') ? 'has-error' : ''}}">
                        {!! Form::label('title', 'Form Title ['.$language.']', ['class' => 'control-label']) !!} {!! Form::text('translate['.$language.'][title]',
                        isset($builder) && isset($builder->translate($language)->title) ? $builder->translate($language)->title
                        : '' , ['class' => 'form-control' , 'required']) !!} {!! $errors->first('translate.'.$language.'.title', '
                        <p class="help-block">
                            :message
                        </p>
                        ') !!}
                    </div>

                    <div class="form-group {{ $errors->has('translate.'.$language.'.date_label') ? 'has-error' : ''}} description">
                        {!! Form::label('date_label', 'Date Label ['.$language.']', ['class' => 'control-label']) !!} {!! Form::text('translate['.$language.'][date_label]',isset($builder)
                        && isset($builder->translate($language)->date_label) ? $builder->translate($language)->date_label
                        : '' , ['class' => 'form-control' , 'required']) !!} {!! $errors->first('translate.'.$language.'.date_label',
                        '
                        <p class="help-block">
                            :message
                        </p>
                        ') !!}
                    </div>
                </div>
            </div>
            <!-- /.tab-pane -->
            @endforeach
        </div>
        <!-- /.tab-content -->
    </div>
    <!-- /.nav-tabs-custom -->
    <br>
    <div class="col-md-12">
        <div class="form-group {{ $errors->has('date_or_range') ? 'has-error' : ''}} link">
            {!! Form::label('date_or_range', 'Require to select specific date or from date to date (Range or Duration)?', ['class' =>
            'control-label']) !!}
            <div class="clearfix">
                <label for="">
                   {!! Form::radio('date_or_range', 'date' , null, ['class' => '' , 'required']) !!} One Day
               </label>
                <label for="">
                   {!! Form::radio('date_or_range', 'range' , null, ['class' => '' , 'required']) !!} Range [From ... to ...]
               </label>
            </div>
            {!! $errors->first('date_or_range', '
            <p class="help-block">
                :message
            </p>
            ') !!}
        </div>

        <div class="form-group {{ $errors->has('request_time') ? 'has-error' : ''}} link">
            {!! Form::label('request_time', 'Require to select time?', ['class' => 'control-label']) !!}
            <div class="clearfix">
                <label for="">
                    {!! Form::radio('request_time', 1 , null, ['class' => '' , 'required']) !!} Yes
                </label>
                <label for="">
                    {!! Form::radio('request_time', 0 , null, ['class' => '' , 'required']) !!} No
                </label>
            </div>
            {!! $errors->first('request_time', '
            <p class="help-block">
                :message
            </p>
            ') !!}
        </div>
        <div class="form-group {{ $errors->has('target') ? 'has-error' : ''}} link">
            {!! Form::label('target', 'Who can apply this from?', ['class' => 'control-label']) !!} {!! Form::select('target[]', $roles->pluck('description'
            , 'name') , null, ['class' => 'form-control date' , 'id' => 'target' , 'multiple']) !!} {!! $errors->first('target',
            '
            <p class="help-block">
                :message
            </p>
            ') !!}
        </div>

        <div class="row">

            <h4>Form Process Flow</h4>
            <hr>
            <div id="vueApp">

                <div v-for="(step , index) in approval_steps" :key="index" class="row form-control">
                    <div class="col-md-2">
                        <label for="">Step Order</label>
                        <input type="number" min="1" :name="'approval_flow[' + index + '][step_order]'" class="form-control" :value="step.step_order"  required>
                    </div>
                    <div class="col-md-2">
                        <label for="">Role</label>
                        <select :name="'approval_flow['+index+'][role]'" id="" class="form-control" v-model="approval_steps[index].role" required>
                        @foreach ($roles as $role)
                        <option value="{{$role->name}}">{{$role->description}}</option>   
                        @endforeach
                    </select>
                    </div>
                    <div class="col-md-6">
                        <label for="">Permisions</label>
                        <div class="row">
                            <label class="col-md-3">
                                <input type="checkbox" :name="'approval_flow['+index+'][can_reject]'" value="1" v-model="step.can_reject" autocomplete="off"> Can Approve
                            </label>
                            <label class="col-md-3">
                                <input type="checkbox" :name="'approval_flow['+index+'][can_approve]'" value="1" v-model="step.can_approve" autocomplete="off"> Can Reject
                            </label>
                            <label class="col-md-6">
                                <input type="checkbox" :name="'approval_flow['+index+'][can_request_clearfication]'" value="1" v-model="step.can_request_clearfication" autocomplete="off"> Can Request Clarification
                            </label>
                            <label class="col-md-12">
                                <input type="checkbox" :name="'approval_flow['+index+'][has_final_approval]'" value="1" v-model="step.has_final_approval" @change="checkIfGotFinal()"> Has Final Approval
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 clearfix">
                    <button type="button" class="btn btn-danger btn-xs " v-if="!got_final_approval" @click="addStep()">Add Step</button>
                </div>
            </div>
        </div>

        <div class="form-group {{ $errors->has('status') ? 'has-error' : ''}}">
            {!! Form::label('status', 'Status', ['class' => 'control-label']) !!} {!! Form::select('status', [1 => 'Active' , 0 => 'Disabled/Not
            Published'] , null, ['class' => 'form-control']) !!} {!! $errors->first('status', '
            <p class="help-block">
                :message
            </p>
            ') !!}
        </div>
        <div class="form-group">
            <div class="col-md-offset-4">
                {!! Form::submit(isset($submitButtonText) ? $submitButtonText : 'Create', ['class' => 'btn btn-primary']) !!}
            </div>
        </div>
    </div>
</div>


@section('js')
@include('jssnippets.select2')
<script src="https://cdn.jsdelivr.net/npm/vue@2.5.17/dist/vue.js"></script>
<script>
    var app = new Vue({
           el : '#vueApp',
           data(){
               return {
                   approval_steps : {!! isset($builder) ? ($builder->approvalFlow->toJson()) : 
                   '[{
                       step_order :  1,
                       role : null,
                       can_reject : true,
                       can_approve : true,
                       can_request_clearfication : true,
                       has_final_approval : false
                   }]' !!},
                   got_final_approval : false
               }
           },
           methods : {
               'addStep' : function () { 
                    this.approval_steps.push({
                        step_order :  this.approval_steps.length + 1,
                       role : null,
                       can_reject : false,
                       can_approve : false,
                       can_request_clearfication : false,
                       has_final_approval : false
                       })
                },
                'checkIfGotFinal' : function () { 
                    for (const iterator of this.approval_steps){
                       if(iterator.has_final_approval){
                           this.got_final_approval = true;
                           return 0;
                       }
                       this.got_final_approval = false;
                   }
                 }
           },
           created() {
               this.checkIfGotFinal();
           },
       })

       $('#target').select2({

       });
</script>

@append
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=tinymce.util.Tools.resolve("tinymce.Env"),n=function(e){return parseInt(e.getParam("plugin_preview_width","650"),10)},i=function(e){return parseInt(e.getParam("plugin_preview_height","500"),10)},o=function(e){return e.getParam("content_style","")},r=tinymce.util.Tools.resolve("tinymce.util.Tools"),c=function(e){var t="",n=e.dom.encode,i=o(e);t+='<base href="'+n(e.documentBaseURI.getURI())+'">',i&&(t+='<style type="text/css">'+i+"</style>"),r.each(e.contentCSS,function(i){t+='<link type="text/css" rel="stylesheet" href="'+n(e.documentBaseURI.toAbsolute(i))+'">'});var c=e.settings.body_id||"tinymce";-1!==c.indexOf("=")&&(c=(c=e.getParam("body_id","","hash"))[e.id]||c);var a=e.settings.body_class||"";-1!==a.indexOf("=")&&(a=(a=e.getParam("body_class","","hash"))[e.id]||"");var s=e.settings.directionality?' dir="'+e.settings.directionality+'"':"";return"<!DOCTYPE html><html><head>"+t+'</head><body id="'+n(c)+'" class="mce-content-body '+n(a)+'"'+n(s)+">"+e.getContent()+'<script>document.addEventListener && document.addEventListener("click", function(e) {for (var elm = e.target; elm; elm = elm.parentNode) {if (elm.nodeName === "A") {e.preventDefault();}}}, false);<\/script> </body></html>'},a=function(e,t,n){var i=c(e);if(n)t.src="data:text/html;charset=utf-8,"+encodeURIComponent(i);else{var o=t.contentWindow.document;o.open(),o.write(i),o.close()}},s=function(e){var o=!t.ie,r='<iframe src="" frameborder="0"'+(o?' sandbox="allow-scripts"':"")+"></iframe>",c=n(e),s=i(e);e.windowManager.open({title:"Preview",width:c,height:s,html:r,buttons:{text:"Close",onclick:function(e){e.control.parent().parent().close()}},onPostRender:function(t){var n=t.control.getEl("body").firstChild;a(e,n,o)}})},d=function(e){e.addCommand("mcePreview",function(){s(e)})},l=function(e){e.addButton("preview",{title:"Preview",cmd:"mcePreview"}),e.addMenuItem("preview",{text:"Preview",cmd:"mcePreview",context:"view"})};e.add("preview",function(e){d(e),l(e)})}();
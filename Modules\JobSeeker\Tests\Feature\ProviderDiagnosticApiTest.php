<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Http;
use Modules\JobSeeker\Entities\CommandScheduleRule;
use Carbon\Carbon;

/**
 * Feature tests for Provider Diagnostic API
 * 
 * Tests the complete flow from API request to response, including mocked external API calls.
 */
class ProviderDiagnosticApiTest extends TestCase
{
    use DatabaseTransactions;

    protected string $apiEndpoint = '/api/v1/jobseeker/diagnostic/run';

    protected function setUp(): void
    {
        parent::setUp();
        
        // Ensure we have a clean HTTP fake setup
        Http::preventStrayRequests();
    }

    /** @test */
    public function it_successfully_runs_diagnostic_for_jobs_af_provider(): void
    {
        // Create a test schedule rule for Jobs.af
        $rule = CommandScheduleRule::create([
            'name' => 'Test Jobs.af Sync Rule',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '0 7 * * 1',
            'schedule_type' => 'weekly_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 100,
            'description' => 'Test rule for Jobs.af diagnostic'
        ]);

        // Mock external HTTP requests
        Http::fake([
            // Host connectivity check
            'https://jobs.af' => Http::response('', 200),
            
            // API endpoint check
            'https://jobs.af/api/v2.6/jobs/list*' => Http::response([
                'status' => 'success',
                'message' => 'Jobs retrieved successfully',
                'data' => [
                    'jobs' => [
                        [
                            'id' => 1,
                            'title' => 'Test Job',
                            'company' => 'Test Company'
                        ]
                    ],
                    'pagination' => [
                        'current_page' => 1,
                        'total_pages' => 1,
                        'total_results' => 1
                    ]
                ]
            ], 200, [
                'Content-Type' => 'application/json'
            ])
        ]);

        // Make API request with authentication
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'X-CSRF-TOKEN' => csrf_token()
        ])->postJson($this->apiEndpoint, [
            'rule_id' => $rule->id
        ]);

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true
                 ]);

        $responseData = $response->json();

        // Assert diagnostic data structure
        $this->assertArrayHasKey('data', $responseData);
        $this->assertArrayHasKey('steps', $responseData['data']);
        $this->assertArrayHasKey('overall_status', $responseData['data']);
        $this->assertArrayHasKey('execution_time', $responseData['data']);

        // Assert steps are present
        $steps = $responseData['data']['steps'];
        $this->assertGreaterThan(0, count($steps));

        // Find and assert host connectivity step
        $connectivityStep = collect($steps)->first(fn($step) => str_contains($step['name'], 'Host Connectivity'));
        $this->assertNotNull($connectivityStep, 'Host connectivity step should be present');
        $this->assertEquals('success', $connectivityStep['status']);

        // Find and assert API endpoint step
        $endpointStep = collect($steps)->first(fn($step) => str_contains($step['name'], 'API Endpoint'));
        $this->assertNotNull($endpointStep, 'API endpoint step should be present');
        $this->assertEquals('success', $endpointStep['status']);

        // Find and assert data structure validation step
        $validationStep = collect($steps)->first(fn($step) => str_contains($step['name'], 'Data Structure'));
        $this->assertNotNull($validationStep, 'Data structure validation step should be present');

        // Verify HTTP calls were made
        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'jobs.af');
        });
    }

    /** @test */
    public function it_successfully_runs_diagnostic_for_acbar_provider(): void
    {
        // Create a test schedule rule for ACBAR
        $rule = CommandScheduleRule::create([
            'name' => 'Test ACBAR Sync Rule',
            'command' => 'jobseeker:sync-acbar-jobs',
            'schedule_expression' => '0 8 * * 2',
            'schedule_type' => 'weekly_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 100,
            'description' => 'Test rule for ACBAR diagnostic'
        ]);

        // Mock external HTTP requests for ACBAR
        Http::fake([
            // Host connectivity check
            'https://www.acbar.org' => Http::response('', 200),
            
            // Website endpoint check
            'https://www.acbar.org/jobs*' => Http::response('
                <!DOCTYPE html>
                <html>
                <head><title>ACBAR Jobs</title></head>
                <body>
                    <div class="job-listing">
                        <h3>Test Job Title</h3>
                        <p>Test job description</p>
                    </div>
                </body>
                </html>
            ', 200, [
                'Content-Type' => 'text/html'
            ])
        ]);

        // Make API request
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'X-CSRF-TOKEN' => csrf_token()
        ])->postJson($this->apiEndpoint, [
            'rule_id' => $rule->id
        ]);

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true
                 ]);

        $responseData = $response->json();

        // Assert diagnostic data structure
        $this->assertArrayHasKey('data', $responseData);
        $steps = $responseData['data']['steps'];
        $this->assertGreaterThan(0, count($steps));

        // Verify steps for ACBAR provider
        $stepNames = collect($steps)->pluck('name');
        $this->assertTrue($stepNames->contains(fn($name) => str_contains($name, 'Host Connectivity')));
        $this->assertTrue($stepNames->contains(fn($name) => str_contains($name, 'API Endpoint')));

        // Verify HTTP calls were made to ACBAR
        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'acbar.org');
        });
    }

    /** @test */
    public function it_handles_network_failures_gracefully(): void
    {
        // Create a test schedule rule
        $rule = CommandScheduleRule::create([
            'name' => 'Test Network Failure Rule',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '0 7 * * 1',
            'schedule_type' => 'weekly_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 100
        ]);

        // Mock network failures
        Http::fake([
            'https://jobs.af' => Http::response('', 500),
            'https://jobs.af/api/v2.6/jobs/list*' => Http::response([
                'error' => 'Service unavailable'
            ], 503)
        ]);

        // Make API request
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'X-CSRF-TOKEN' => csrf_token()
        ])->postJson($this->apiEndpoint, [
            'rule_id' => $rule->id
        ]);

        // Assert successful response structure (even with failed diagnostics)
        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true
                 ]);

        $responseData = $response->json();
        $steps = $responseData['data']['steps'];

        // At least one step should show failure
        $hasFailure = collect($steps)->contains(fn($step) => $step['status'] === 'failure');
        $this->assertTrue($hasFailure, 'Should have at least one failed step');

        // Overall status should reflect the failure
        $this->assertEquals('failure', $responseData['data']['overall_status']);
    }

    /** @test */
    public function it_validates_rule_id_parameter(): void
    {
        // Test missing rule_id
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'X-CSRF-TOKEN' => csrf_token()
        ])->postJson($this->apiEndpoint, []);

        $response->assertStatus(400)
                 ->assertJson([
                     'success' => false,
                     'error' => 'Invalid parameters'
                 ]);

        // Test invalid rule_id
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'X-CSRF-TOKEN' => csrf_token()
        ])->postJson($this->apiEndpoint, [
            'rule_id' => 'invalid'
        ]);

        $response->assertStatus(400)
                 ->assertJson([
                     'success' => false,
                     'error' => 'Invalid parameters'
                 ]);

        // Test non-existent rule_id
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'X-CSRF-TOKEN' => csrf_token()
        ])->postJson($this->apiEndpoint, [
            'rule_id' => 99999
        ]);

        $response->assertStatus(200);
        $responseData = $response->json();
        
        // Should return error in the diagnostic results
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals('failure', $responseData['data']['overall_status']);
    }

    /** @test */
    public function it_handles_internal_database_commands(): void
    {
        // Create a test schedule rule for database cleanup
        $rule = CommandScheduleRule::create([
            'name' => 'Test Cleanup Rule',
            'command' => 'jobseeker:cleanup-old-jobs',
            'schedule_expression' => '0 2 * * 0',
            'schedule_type' => 'weekly_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 100
        ]);

        // No external HTTP mocking needed for internal commands
        Http::fake([]);

        // Make API request
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'X-CSRF-TOKEN' => csrf_token()
        ])->postJson($this->apiEndpoint, [
            'rule_id' => $rule->id
        ]);

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true
                 ]);

        $responseData = $response->json();
        $steps = $responseData['data']['steps'];

        // Should handle internal commands appropriately
        $this->assertGreaterThan(0, count($steps));
        
        // No external HTTP calls should be made for internal commands
        Http::assertNothingSent();
    }

    /** @test */
    public function it_includes_execution_time_and_logging(): void
    {
        // Create a test schedule rule
        $rule = CommandScheduleRule::create([
            'name' => 'Test Execution Time Rule',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '0 7 * * 1',
            'schedule_type' => 'weekly_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 100
        ]);

        // Mock successful responses
        Http::fake([
            'https://jobs.af' => Http::response('', 200),
            'https://jobs.af/api/v2.6/jobs/list*' => Http::response([
                'status' => 'success',
                'data' => ['jobs' => [], 'pagination' => ['current_page' => 1]]
            ], 200)
        ]);

        // Make API request
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'X-CSRF-TOKEN' => csrf_token()
        ])->postJson($this->apiEndpoint, [
            'rule_id' => $rule->id
        ]);

        $response->assertStatus(200);
        $responseData = $response->json();

        // Assert execution time is included
        $this->assertArrayHasKey('api_execution_time', $responseData);
        $this->assertIsNumeric($responseData['api_execution_time']);
        $this->assertGreaterThan(0, $responseData['api_execution_time']);

        // Assert diagnostic execution time is included
        $this->assertArrayHasKey('execution_time', $responseData['data']);
        $this->assertIsNumeric($responseData['data']['execution_time']);

        // Assert logs are included
        $this->assertArrayHasKey('logs', $responseData['data']);
        $this->assertIsArray($responseData['data']['logs']);

        // Assert timestamp is included
        $this->assertArrayHasKey('timestamp', $responseData);
        $this->assertNotEmpty($responseData['timestamp']);
    }

    /** @test */
    public function it_returns_proper_error_response_for_exceptions(): void
    {
        // Create a rule that might cause service exceptions
        $rule = CommandScheduleRule::create([
            'name' => 'Test Exception Rule',
            'command' => 'invalid:command',
            'schedule_expression' => '0 7 * * 1',
            'schedule_type' => 'weekly_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 100
        ]);

        // Make API request
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'X-CSRF-TOKEN' => csrf_token()
        ])->postJson($this->apiEndpoint, [
            'rule_id' => $rule->id
        ]);

        // Should still return 200 but with diagnostic error
        $response->assertStatus(200);
        $responseData = $response->json();
        
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals('failure', $responseData['data']['overall_status']);
    }

    /** @test */
    public function provider_info_endpoint_returns_correct_structure(): void
    {
        $response = $this->withHeaders([
            'Accept' => 'application/json'
        ])->getJson('/api/v1/jobseeker/diagnostic/providers');

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true
                 ]);

        $responseData = $response->json();
        
        $this->assertArrayHasKey('data', $responseData);
        $this->assertArrayHasKey('supported_commands', $responseData['data']);
        $this->assertArrayHasKey('diagnostic_steps', $responseData['data']);

        // Check specific providers are included
        $commands = $responseData['data']['supported_commands'];
        $this->assertArrayHasKey('jobseeker:sync-jobs-af', $commands);
        $this->assertArrayHasKey('jobseeker:sync-acbar-jobs', $commands);
    }

    /** @test */
    public function health_check_endpoint_works(): void
    {
        $response = $this->withHeaders([
            'Accept' => 'application/json'
        ])->getJson('/api/v1/jobseeker/diagnostic/health');

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'status' => 'healthy',
                     'service' => 'Provider Diagnostic Service'
                 ]);

        $responseData = $response->json();
        $this->assertArrayHasKey('timestamp', $responseData);
        $this->assertArrayHasKey('version', $responseData);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
    }
}
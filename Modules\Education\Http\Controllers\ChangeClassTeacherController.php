<?php

namespace Modules\Education\Http\Controllers;

use Illuminate\Database\Eloquent\Model;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Employee;
use Carbon\Carbon;
use App\ClassStudent;
use App\ClassTeacher;
use App\Http\Requests;
use App\ClassTeacherSubject;
use Illuminate\Http\Request;
use App\ClassSubjectTimetable;
use App\Http\Controllers\Controller;
use App\Cen_Emp;

class ChangeClassTeacherController extends Controller
{


    public function __invoke(Request $request)
    {




        try {
            \DB::beginTransaction();

        $this->validate($request, [
            'class_id' => 'required',
            'teacher_id' => 'required',
            'current_teacher_id' => 'required',
            'ended_at' => 'required',
            'start_at' => 'required'
        ]);

        // validate and check authorization
        // delete current teacher
        $class_current_teacher = ClassTeacher::where('employee_id', $request->current_teacher_id)
            ->where('class_id', $request->class_id)
            ->where('end_date', NULL)
            ->first();




        if ($class_current_teacher) {

//            $subject = $class_current_teacher->subjects->where('subject_id', $request->subject_id)->first();
            $subject = $class_current_teacher->subjects->where('class_teacher_id', $request->get('class_teacher_id'))->first();

            $subjectId = $subject->id;


            if ($subject) {
                $subject->end_date = $request->ended_at;
                $subject->deleted_at = Carbon::now();
                $subject->save();

                // update timetable table
                ClassSubjectTimetable::where('class_teacher_subject_id',$subjectId)->update([

                    'end_at' => $request->ended_at,
                    'deleted_at' => Carbon::now()
                ]);
            }


//            if ($class_current_teacher->subjects->count() == 0) {
//                $class_current_teacher->end_date = $request->ended_at;
//            }
            // add new teacher
            $this->addNewTeacher($request);
        }
        // return with error
            \DB::commit();
        Session::flash('flash_message', 'Error while adding new teacher!');

        return redirect()->back();

        } catch (\Exception $exception) {
            \Log::error($exception);

            \DB::rollBack();
            $errorMessage = $exception->getMessage();
            return response()->json(compact('errorMessage'));
        }

    }


    public function addNewTeacher(Request $request)
    {

//        $class_teacher = ClassTeacher::updateOrCreate(['class_id' => $request->class_id], ['employee_id' => $request->teacher_id, 'start_date' => $request->start_at]);
        ClassTeacher::where('employee_id', $request->current_teacher_id)
            ->where('class_id', $request->class_id)
            ->where('end_date', NULL)->update(['deleted_at' => Carbon::now(),'end_date' => $request->ended_at]);

        $class_teacher = new ClassTeacher();
        $class_teacher->start_date = $request->start_at;
        $class_teacher->class_id = $request->class_id;
        $class_teacher->employee_id = $request->teacher_id;

        $class_teacher->save();


        $teacher_subject = new ClassTeacherSubject();
        $teacher_subject->class_teacher_id = $class_teacher->id;
//        $teacher_subject->subject_id = $request->subject_id;
        $teacher_subject->subject_id = 4;
        $teacher_subject->program_id = $request->program_id;
        $teacher_subject->start_date = $request->start_at;

        $teacher_subject->save();

        Session::flash('flash_message', 'Teacher was added Succesfully!');

        return redirect()->back();
    }

}

<?php

namespace App\Rules;

use App\User;
use Illuminate\Contracts\Validation\Rule;

class StudentStudyDirectionSuratRule implements Rule
{
    private $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {


        $studyDirection = $this->data['hefz']['study_direction'];
        $fromSurat = $this->data['hefz']['start_from_surat'];
        $toSurat = $this->data['hefz']['to_surat'];

        if ($studyDirection == 'backward' && ($fromSurat > $toSurat  )) {

            return true;

        }

        elseif ($studyDirection == 'forward' && ($fromSurat < $toSurat)) {

            return true;

        }

        else{

            return false;

        }


    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Please make sure the :attribute and the Surahs selections are correct.';
    }
}

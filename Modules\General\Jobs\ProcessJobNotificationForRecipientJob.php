<?php

namespace Modules\General\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Mo<PERSON>les\JobSeeker\Entities\JobNotificationSetup;
use Modules\JobSeeker\Entities\JobNotificationSentJob;
use Modules\JobSeeker\Repositories\JobRepository;
use Carbon\Carbon;
use App\Services\EmailService;
use Illuminate\Support\Facades\DB;

class ProcessJobNotificationForRecipientJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 60;

    /**
     * The notification setup ID
     *
     * @var int
     */
    protected $setupId;
    
    /**
     * The recipient email address
     * 
     * @var string
     */
    protected $recipientEmail;
    
    /**
     * The recipient name (optional)
     * 
     * @var string|null
     */
    protected $recipientName;

    /**
     * Create a new job instance.
     *
     * @param int $setupId
     * @param string $recipientEmail
     * @param string|null $recipientName
     * @return void
     */
    public function __construct(int $setupId, string $recipientEmail, ?string $recipientName = null)
    {
        $this->setupId = $setupId;
        $this->recipientEmail = $recipientEmail;
        $this->recipientName = $recipientName;
        $this->connection = 'job_notifications';
        $this->queue = 'recipient_processors';
    }

    /**
     * Execute the job.
     *
     * @param JobRepository $jobRepository
     * @param EmailService $emailService
     * @return void
     */
    public function handle(JobRepository $jobRepository, EmailService $emailService)
    {
        $jobId = $this->job->getJobId(); // Get the ID of this job instance
        Log::info("Processing job notification for recipient", [
            'setup_id' => $this->setupId,
            'recipient' => $this->recipientEmail,
            'job_id' => $jobId
        ]);

        try {
            // Fetch the setup with its categories
            $setup = JobNotificationSetup::with(['categories'])
                ->find($this->setupId);

            if (!$setup) {
                Log::warning("Job notification setup not found", ['setup_id' => $this->setupId]);
                return;
            }

            // Get category IDs
            $categoryIds = $setup->categories->pluck('id')->toArray();
            if (empty($categoryIds)) {
                Log::info("Setup has no categories, skipping", ['setup_id' => $setup->id]);
                return;
            }

            // Initialize variables to track job processing
            $newJobsForRecipient = collect();
            $chunkSize = 50; // Process 50 jobs at a time - smaller for per-recipient processing
            $totalNewJobs = 0;
            
            // Define a callback function to process each chunk of jobs
            $chunkCallback = function ($jobChunk) use ($jobRepository, $setup, &$newJobsForRecipient, &$totalNewJobs) {
                Log::debug("Processing job chunk for recipient", [
                    'setup_id' => $setup->id,
                    'recipient' => $this->recipientEmail,
                    'chunk_size' => $jobChunk->count()
                ]);
                
                foreach ($jobChunk as $job) {
                    // Check if this job has already been sent to this recipient
                    if (!$jobRepository->hasJobBeenSentToRecipient($setup->id, $job->id, $this->recipientEmail)) {
                        $newJobsForRecipient->push($job);
                        $totalNewJobs++;
                    }
                }
            };
            
            // Use the repository's chunked method to process jobs in batches
            $jobRepository->getRecentJobsByCategoriesChunked(
                $categoryIds,
                7, // Last 7 days
                $chunkSize,
                $chunkCallback
            );
            
            // If no new jobs were found for this recipient, we're done
            if ($newJobsForRecipient->isEmpty()) {
                Log::info("No new jobs to send to recipient", [
                    'setup_id' => $setup->id,
                    'recipient' => $this->recipientEmail
                ]);
                return;
            }
            
            // Additional safety check: Ensure we have valid job data
            $validJobs = $newJobsForRecipient->filter(function ($job) {
                return !empty($job->position) || !empty($job->title);
            });
            
            if ($validJobs->isEmpty()) {
                Log::warning("No valid jobs found after filtering", [
                    'setup_id' => $setup->id,
                    'recipient' => $this->recipientEmail,
                    'original_count' => $newJobsForRecipient->count()
                ]);
                return;
            }
            
            // Format jobs for email template
            $formattedJobs = $this->formatJobsForEmail($validJobs);
            
            // Final check: Ensure formatted jobs are not empty
            if (empty($formattedJobs)) {
                Log::warning("No formatted jobs available for email", [
                    'setup_id' => $setup->id,
                    'recipient' => $this->recipientEmail,
                    'valid_jobs_count' => $validJobs->count()
                ]);
                return;
            }

            Log::info("Sending email with jobs to recipient", [
                'setup_id' => $setup->id,
                'recipient' => $this->recipientEmail,
                'jobs_count' => $validJobs->count()
            ]);

            // Recipient name fallback to part before @ in email if not provided
            $name = $this->recipientName ?? explode('@', $this->recipientEmail)[0];

            // Send email to this recipient
            $result = $emailService->sendEmail(
                [
                    'email' => $this->recipientEmail,
                    'name' => $name
                ],
                'Job Opportunities: ' . $setup->name,
                'jobseeker::emails.jobs.jobseeker_notification',
                [
                    'jobs' => $formattedJobs,
                    'jobSeeker' => (object)[
                        'email' => $this->recipientEmail,
                        'name' => $name
                    ],
                    'setup' => $setup,
                    'timeAgo' => function($date) { return $this->getTimeAgo($date); },
                    'currentDate' => now()->format('F j, Y'),
                    'setupName' => $setup->name
                ]
            );

            if ($result['success']) {
                Log::info("Successfully sent job notification email", [
                    'setup_id' => $setup->id,
                    'recipient' => $this->recipientEmail,
                    'jobs_count' => $validJobs->count()
                ]);

                // Record each sent job in a single transaction
                DB::transaction(function() use ($setup, $validJobs) {
                    foreach ($validJobs as $job) {
                        JobNotificationSentJob::create([
                            'setup_id' => $setup->id,
                            'job_id' => $job->id,
                            'recipient_email' => $this->recipientEmail,
                            'sent_at' => now(),
                        ]);
                    }
                    
                    // Update the setup's sent count atomically
                    DB::table('job_notification_setups')
                        ->where('id', $setup->id)
                        ->increment('sent_count');
                });
            } else {
                Log::error("Failed to send job notification email", [
                    'setup_id' => $setup->id,
                    'recipient' => $this->recipientEmail,
                    'error' => $result['message'] ?? 'Unknown error'
                ]);
                // The job will be retried automatically if it fails
                throw new \Exception($result['message'] ?? 'Failed to send email');
            }

            Log::info("Completed processing job notification for recipient", [
                'setup_id' => $setup->id, 
                'recipient' => $this->recipientEmail
            ]);

        } catch (\Exception $e) {
            Log::error("Error processing job notification for recipient", [
                'setup_id' => $this->setupId,
                'recipient' => $this->recipientEmail,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e; // Rethrow to trigger job retry
        }
    }

    /**
     * Format jobs for email template
     * 
     * @param \Illuminate\Database\Eloquent\Collection $jobs
     * @return array
     */
    protected function formatJobsForEmail($jobs)
    {
        $formattedJobs = [];
        $categoryPriority = [
            'IT - Software' => 1,
            'Software engineering' => 1,
            'software development' => 1, 
            'software development ' => 1,
            'Information Technology' => 1,
            'Leadership' => 2,
            'Management' => 3
        ];

        foreach ($jobs as $job) {
            // Determine job category priority based on position or category
            $priority = 10; // Default low priority
            $position = strtolower($job->position ?? '');
            
            // Check position for keywords
            if (strpos($position, 'developer') !== false || 
                strpos($position, 'engineer') !== false || 
                strpos($position, 'software') !== false || 
                strpos($position, 'it') !== false) {
                $priority = 1; // IT jobs
            } elseif (strpos($position, 'lead') !== false || 
                     strpos($position, 'chief') !== false || 
                     strpos($position, 'head') !== false || 
                     strpos($position, 'director') !== false) {
                $priority = 2; // Leadership jobs
            } elseif (strpos($position, 'manager') !== false || 
                     strpos($position, 'management') !== false) {
                $priority = 3; // Management jobs
            }
            
            $formattedJobs[] = [
                'position' => $job->position ?? '',
                'company_name' => $job->company_name ?? '',
                'locations' => $job->locations ?? '',
                'contract_type' => $job->contract_type ?? '',
                'work_type' => $job->work_type ?? '',
                'publish_date' => $job->publish_date ?? '',
                'salary' => $job->salary ?? '',
                'slug' => $job->slug ?? '',
                'updated_at' => $job->updated_at ?? now()->format('Y-m-d H:i:s'),
                'priority' => $priority // Add priority for sorting
            ];
        }
        
        // Sort formatted jobs by priority (lower number first)
        usort($formattedJobs, function($a, $b) {
            return $a['priority'] <=> $b['priority'];
        });
        
        return $formattedJobs;
    }

    /**
     * Get relative time string (e.g., "2 hours ago", "1 day ago")
     *
     * @param string $dateString
     * @return string
     */
    protected function getTimeAgo($dateString)
    {
        try {
            $date = Carbon::parse($dateString);
            $now = Carbon::now();
            
            if ($date->diffInMinutes($now) < 60) {
                return $date->diffInMinutes($now) . ' minutes ago';
            } elseif ($date->diffInHours($now) < 24) {
                return $date->diffInHours($now) . ' hours ago';
            } else {
                return $date->diffInDays($now) . ' days ago';
            }
        } catch (\Exception $e) {
            Log::warning("Failed to parse date for time ago: {$dateString} - " . $e->getMessage());
            return 'recently';
        }
    }
} 
<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Form
 *
 * @property int $id
 * @property int $form_builder_id
 * @property int $created_by
 * @property string|null $target_type
 * @property int|null $target_id
 * @property string $content
 * @property \Illuminate\Support\Carbon $from_date
 * @property \Illuminate\Support\Carbon|null $to_date
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Employee|null $applicant
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\FormReview[] $reviews
 * @property-read int|null $reviews_count
 * @property-read \App\FormBuilder $type
 * @method static \Illuminate\Database\Eloquent\Builder|Form newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Form newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Form query()
 * @method static \Illuminate\Database\Eloquent\Builder|Form whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Form whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Form whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Form whereFormBuilderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Form whereFromDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Form whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Form whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Form whereTargetId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Form whereTargetType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Form whereToDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Form whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Form extends Model
{
    public $casts = ['from_date' , 'to_date'];
    
    public function type()
    {
        return $this->belongsTo('App\FormBuilder' , 'form_builder_id' , 'id');
    }

    public function applicant()
    {
        return $this->belongsTo('App\Employee'  , 'created_by', 'id' );        
    }

    public function reviews()
    {
        return $this->hasMany('App\FormReview');
    }
}

<?php

declare(strict_types=1);

namespace Mo<PERSON>les\JobSeeker\Repositories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Mo<PERSON><PERSON>\JobSeeker\Entities\Job;
use Mo<PERSON>les\JobSeeker\Entities\JobCategory;
use Modules\JobSeeker\Services\JobDataNormalizerService;

final class JobRepository
{
    /**
     * Find a job by its slug
     *
     * @param string $slug
     * @return Job|null
     */
    public function findBySlug(string $slug): ?Job
    {
        Log::info("Repository: Finding job with slug: {$slug}");
        return Job::where('slug', $slug)->first();
    }

    /**
     * Create or update a job with enhanced de-duplication logic
     *
     * @param array $jobData
     * @return Job
     */
    public function createOrUpdate(array $jobData): Job
    {
        Log::info("Repository: Creating or updating job with enhanced de-duplication", [
            'slug' => $jobData['slug'] ?? 'no-slug',
            'source' => $jobData['source'] ?? 'unknown'
        ]);
        
        try {
            // Prepare and normalize job data
            $normalizedData = $this->prepareAndNormalizeJobData($jobData);
            
            // Handle fallback to slug-based creation if essential data is missing
            if ($normalizedData === null) {
                return Job::updateOrCreate(
                    ['slug' => $jobData['slug']],
                    $jobData
                );
            }
            
            // Generate fingerprint for de-duplication
            $jobFingerprint = $this->generateJobFingerprint($normalizedData);
            
            // Prepare source information
            $sourceInfo = $this->prepareSourceInformation($jobData);
            
            Log::info("Repository: Generated fingerprint for de-duplication", [
                'fingerprint' => $jobFingerprint,
                'normalized_company' => $normalizedData['normalized_company_name'],
                'normalized_title' => $normalizedData['normalized_job_title'],
                'normalized_location' => $normalizedData['normalized_location'],
                'source_info' => $sourceInfo
            ]);
            
            // Check for existing job and either update or create new
            $existingJob = Job::where('job_fingerprint', $jobFingerprint)->first();
            
            if ($existingJob) {
                return $this->updateExistingJob($existingJob, $jobData, $sourceInfo);
            } else {
                return $this->createNewJob($jobData, $normalizedData, $jobFingerprint, $sourceInfo);
            }
            
        } catch (\Exception $e) {
            Log::error("Repository: Error in enhanced createOrUpdate: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'job_data_keys' => array_keys($jobData)
            ]);
            throw $e;
        }
    }

    /**
     * Prepare and normalize job data for de-duplication
     *
     * @param array $jobData
     * @return array|null Returns normalized data array or null if essential data is missing
     */
    private function prepareAndNormalizeJobData(array $jobData): ?array
    {
        // Retrieve raw data from different possible keys
        $rawCompanyName = $jobData['company_name'] ?? 
                          $jobData['organizationName'] ?? 
                          $jobData['organization_name'] ?? 
                          null;
                          
        $rawJobTitle = $jobData['position'] ?? 
                      $jobData['jobTitle'] ?? 
                      $jobData['job_title'] ?? 
                      $jobData['title'] ?? 
                      null;
                      
        $rawLocation = $jobData['locations'] ?? 
                      $jobData['location'] ?? 
                      null;
        
        // Skip de-duplication if essential data is missing
        if (!$rawCompanyName || !$rawJobTitle) {
            Log::warning("Repository: Missing essential data for de-duplication, falling back to slug-based creation", [
                'company_name' => $rawCompanyName,
                'job_title' => $rawJobTitle,
                'slug' => $jobData['slug'] ?? 'no-slug'
            ]);
            
            return null;
        }
        
        // Use JobDataNormalizerService to generate normalized fields
        $normalizedCompanyName = JobDataNormalizerService::normalizeCompanyName($rawCompanyName);
        $normalizedJobTitle = JobDataNormalizerService::normalizeJobTitle($rawJobTitle);
        $normalizedLocation = $rawLocation ? JobDataNormalizerService::normalizeLocation($rawLocation) : '';
        
        return [
            'raw_company_name' => $rawCompanyName,
            'raw_job_title' => $rawJobTitle,
            'raw_location' => $rawLocation,
            'normalized_company_name' => $normalizedCompanyName,
            'normalized_job_title' => $normalizedJobTitle,
            'normalized_location' => $normalizedLocation
        ];
    }

    /**
     * Generate job fingerprint for de-duplication
     *
     * @param array $normalizedData
     * @return string
     */
    private function generateJobFingerprint(array $normalizedData): string
    {
        return md5(strtolower(
            $normalizedData['normalized_company_name'] . 
            $normalizedData['normalized_job_title'] . 
            $normalizedData['normalized_location']
        ));
    }

    /**
     * Prepare source information for the job
     *
     * @param array $jobData
     * @return array
     */
    private function prepareSourceInformation(array $jobData): array
    {
        return [[
            'source_name' => $jobData['source'] ?? 'unknown',
            'original_id' => $jobData['jobUrl'] ?? $jobData['url'] ?? $jobData['source_id'] ?? $jobData['slug'],
            'imported_at' => Carbon::now()->toDateTimeString()
        ]];
    }

    /**
     * Update existing job with new source information and better data
     *
     * @param Job $existingJob
     * @param array $jobData
     * @param array $sourceInfo
     * @return Job
     */
    private function updateExistingJob(Job $existingJob, array $jobData, array $sourceInfo): Job
    {
        Log::info("Repository: Found existing job with same fingerprint, updating master job", [
            'existing_job_id' => $existingJob->id,
            'existing_slug' => $existingJob->slug,
            'fingerprint' => $existingJob->job_fingerprint
        ]);
        
        // Merge source information, avoiding duplicates
        $existingSources = $existingJob->source_ids ?? [];
        $mergedSources = $this->mergeSourceIds($existingSources, $sourceInfo);
        $existingJob->source_ids = $mergedSources;
        
        // Update other fields if the new data is more recent or has better information
        $this->updateJobWithBetterData($existingJob, $jobData);
        
        $existingJob->save();
        
        Log::info("Repository: Updated existing master job", [
            'job_id' => $existingJob->id,
            'total_sources' => count($mergedSources)
        ]);
        
        return $existingJob;
    }

    /**
     * Create new job with normalized data and fingerprint
     *
     * @param array $jobData
     * @param array $normalizedData
     * @param string $jobFingerprint
     * @param array $sourceInfo
     * @return Job
     */
    private function createNewJob(array $jobData, array $normalizedData, string $jobFingerprint, array $sourceInfo): Job
    {
        Log::info("Repository: No duplicate found, creating new job", [
            'fingerprint' => $jobFingerprint
        ]);
        
        // Prepare job data with normalized fields
        $jobDataToSave = array_merge($jobData, [
            'normalized_company_name' => $normalizedData['normalized_company_name'],
            'normalized_job_title' => $normalizedData['normalized_job_title'],
            'normalized_location' => $normalizedData['normalized_location'],
            'job_fingerprint' => $jobFingerprint,
            'source_ids' => $sourceInfo,
            'master_job_id' => null, // This is a master job
        ]);
        
        // Use fingerprint for updateOrCreate to prevent race conditions
        $newJob = Job::updateOrCreate(
            ['job_fingerprint' => $jobFingerprint],
            $jobDataToSave
        );
        
        Log::info("Repository: Created new job", [
            'job_id' => $newJob->id,
            'slug' => $newJob->slug,
            'fingerprint' => $jobFingerprint,
            'was_recently_created' => $newJob->wasRecentlyCreated
        ]);
        
        return $newJob;
    }

    /**
     * Merge source IDs, avoiding duplicates
     *
     * @param array $existingSources
     * @param array $newSources
     * @return array
     */
    private function mergeSourceIds(array $existingSources, array $newSources): array
    {
        $merged = $existingSources;
        
        foreach ($newSources as $newSource) {
            $isDuplicate = false;
            
            foreach ($existingSources as $existingSource) {
                if (($existingSource['source_name'] ?? '') === ($newSource['source_name'] ?? '') &&
                    ($existingSource['original_id'] ?? '') === ($newSource['original_id'] ?? '')) {
                    $isDuplicate = true;
                    break;
                }
            }
            
            if (!$isDuplicate) {
                $merged[] = $newSource;
            }
        }
        
        return $merged;
    }

    /**
     * Update existing job with better data from new source
     *
     * @param Job $existingJob
     * @param array $newJobData
     * @return void
     */
    private function updateJobWithBetterData(Job $existingJob, array $newJobData): void
    {
        // Update expire_date if new data has a later date
        if (isset($newJobData['expire_date']) && $newJobData['expire_date']) {
            $newExpireDate = Carbon::parse($newJobData['expire_date']);
            $currentExpireDate = $existingJob->expire_date ? Carbon::parse($existingJob->expire_date) : null;
            
            if (!$currentExpireDate || $newExpireDate->gt($currentExpireDate)) {
                $existingJob->expire_date = $newExpireDate;
                Log::info("Repository: Updated expire_date with later date", [
                    'job_id' => $existingJob->id,
                    'old_date' => $currentExpireDate?->toDateString(),
                    'new_date' => $newExpireDate->toDateString()
                ]);
            }
        }
        
        // Update publish_date if new data has an earlier date (original posting)
        if (isset($newJobData['publish_date']) && $newJobData['publish_date']) {
            $newPublishDate = Carbon::parse($newJobData['publish_date']);
            $currentPublishDate = $existingJob->publish_date ? Carbon::parse($existingJob->publish_date) : null;
            
            if (!$currentPublishDate || $newPublishDate->lt($currentPublishDate)) {
                $existingJob->publish_date = $newPublishDate;
                Log::info("Repository: Updated publish_date with earlier date", [
                    'job_id' => $existingJob->id,
                    'old_date' => $currentPublishDate?->toDateString(),
                    'new_date' => $newPublishDate->toDateString()
                ]);
            }
        }
        
        // Update description if current one is empty or new one is longer
        if (isset($newJobData['description']) && $newJobData['description']) {
            if (empty($existingJob->description) || 
                strlen($newJobData['description']) > strlen($existingJob->description)) {
                $existingJob->description = $newJobData['description'];
                Log::info("Repository: Updated description with better content", [
                    'job_id' => $existingJob->id,
                    'new_length' => strlen($newJobData['description'])
                ]);
            }
        }
        
        // Update salary if current one is empty
        if (isset($newJobData['salary']) && $newJobData['salary'] && empty($existingJob->salary)) {
            $existingJob->salary = $newJobData['salary'];
            Log::info("Repository: Updated salary information", [
                'job_id' => $existingJob->id,
                'salary' => $newJobData['salary']
            ]);
        }
    }

    /**
     * Get all active jobs
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllActive()
    {
        Log::info("Repository: Getting all active jobs");
        return Job::active()->orderBy('publish_date', 'desc')->get();
    }

    /**
     * Get featured jobs
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFeatured(int $limit = 10)
    {
        Log::info("Repository: Getting featured jobs with limit: {$limit}");
        return Job::featured()->active()->orderBy('publish_date', 'desc')->limit($limit)->get();
    }

    /**
     * Get jobs by location
     *
     * @param string $location
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByLocation(string $location)
    {
        Log::info("Repository: Getting jobs by location: {$location}");
        return Job::where('locations', 'like', "%{$location}%")
            ->active()
            ->orderBy('publish_date', 'desc')
            ->get();
    }

    /**
     * Delete old jobs that are no longer in the API response
     *
     * @param array $currentSlugs
     * @return int
     */
    public function deleteOldJobs(array $currentSlugs): int
    {
        Log::info("Repository: Deleting old jobs not in the current API response");
        return Job::whereNotIn('slug', $currentSlugs)->delete();
    }

    /**
     * Find jobs that may have missed notifications
     *
     * @param int $days Number of days to look back
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function findMissedNotifications(int $days = 2)
    {
        Log::info("Repository: Finding jobs with missed notifications within the last {$days} days");
        
        $cutoff = Carbon::now()->subDays($days);
        
        return Job::where('publish_date', '>=', $cutoff)
            ->orderBy('publish_date', 'desc')
            ->get();
    }

    /**
     * Record that jobs have been notified
     * 
     * @deprecated This method is deprecated and should not be used anymore.
     * Notification tracking is now handled properly via JobNotificationSentJob 
     * model in notification sending methods, which includes setup_id and 
     * recipient_email for proper tracking. This method creates incomplete 
     * records without required fields.
     * 
     * @param int|array $jobIds Single job ID or array of job IDs
     * @return bool
     */
    public function markAsNotified($jobIds): bool
    {
        $now = Carbon::now();
        $count = 0;
        
        if (is_array($jobIds)) {
            Log::info("Repository: Recording " . count($jobIds) . " jobs as notified in job_notification_sent_jobs");
            
            foreach ($jobIds as $jobId) {
                try {
                    DB::table('job_notification_sent_jobs')->insert([
                        'job_id' => $jobId,
                        'sent_at' => $now,
                        'created_at' => $now,
                        'updated_at' => $now
                    ]);
                    $count++;
                } catch (\Exception $e) {
                    Log::error("Error recording job as notified", [
                        'job_id' => $jobId,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            return $count > 0;
        } else {
            Log::info("Repository: Recording job with ID {$jobIds} as notified in job_notification_sent_jobs");
            
            try {
                DB::table('job_notification_sent_jobs')->insert([
                    'job_id' => $jobIds,
                    'sent_at' => $now,
                    'created_at' => $now,
                    'updated_at' => $now
                ]);
                return true;
            } catch (\Exception $e) {
                Log::error("Error recording job as notified", [
                    'job_id' => $jobIds,
                    'error' => $e->getMessage()
                ]);
                return false;
            }
        }
    }

    /**
     * Get recent jobs by categories
     *
     * @param array $categoryIds Category IDs to filter by
     * @param int $days Number of days to look back
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecentJobsByCategories(array $categoryIds, int $days = 7)
    {
        Log::info("Repository: Getting recent jobs for categories: " . implode(', ', $categoryIds) . " within {$days} days");
        
        $cutoff = Carbon::now()->subDays($days);
        
        // Get job categories data
        $categories = JobCategory::whereIn('id', $categoryIds)->get();
            
        $categoryNames = $categories->pluck('name')->toArray();
        $categoryKeywords = [];
        
        // Create search patterns based on category names
        foreach ($categoryNames as $name) {
            // Add the name itself
            $categoryKeywords[] = $name;
            
            // Add variations
            if ($name === 'IT - Software') {
                $categoryKeywords[] = 'software';
                $categoryKeywords[] = 'developer';
                $categoryKeywords[] = 'engineer';
                $categoryKeywords[] = 'programming';
                $categoryKeywords[] = 'coder';
            } elseif ($name === 'IT - Hardware') {
                $categoryKeywords[] = 'hardware';
                $categoryKeywords[] = 'network';
                $categoryKeywords[] = 'infrastructure';
                $categoryKeywords[] = 'support';
            } elseif ($name === 'Management') {
                $categoryKeywords[] = 'manager';
                $categoryKeywords[] = 'management';
                $categoryKeywords[] = 'supervisor';
            } elseif ($name === 'Leadership') {
                $categoryKeywords[] = 'lead';
                $categoryKeywords[] = 'chief';
                $categoryKeywords[] = 'head';
                $categoryKeywords[] = 'director';
                $categoryKeywords[] = 'executive';
            } elseif ($name === 'Information Technology') {
                $categoryKeywords[] = 'IT';
                $categoryKeywords[] = 'technology';
                $categoryKeywords[] = 'systems';
                $categoryKeywords[] = 'computer';
            } elseif ($name === 'Human Resources') {
                $categoryKeywords[] = 'HR';
                $categoryKeywords[] = 'recruitment';
                $categoryKeywords[] = 'talent';
                $categoryKeywords[] = 'hiring';
            }
        }
        
        // Build query to match jobs by category names or keywords in position
        $query = Job::where('publish_date', '>=', $cutoff);
        
        if (!empty($categoryKeywords)) {
            $query->where(function ($q) use ($categoryKeywords) {
                foreach ($categoryKeywords as $keyword) {
                    $q->orWhere('position', 'like', "%{$keyword}%");
                }
            });
        }
        
        return $query->orderBy('publish_date', 'desc')->get();
    }

    /**
     * Get recent job counts per category
     *
     * @param int $days Number of days to look back
     * @return array Array of category counts
     */
    public function getRecentJobCountsPerCategory(int $days = 5): array
    {
        Log::info("Repository: Getting recent job counts per category for the last {$days} days");
        
        $cutoff = Carbon::now()->subDays($days);
        
        // Get all categories
        $categories = JobCategory::all();
        $counts = [];
        
        foreach ($categories as $category) {
            $categoryKeywords = $this->getCategoryKeywords($category->name);
            
            // Count jobs matching this category
            $query = Job::where('publish_date', '>=', $cutoff);
            
            if (!empty($categoryKeywords)) {
                $query->where(function ($q) use ($categoryKeywords) {
                    foreach ($categoryKeywords as $keyword) {
                        $q->orWhere('position', 'like', "%{$keyword}%");
                    }
                });
            }
            
            $count = $query->count();
            
            if ($count > 0) {
                $counts[] = [
                    'category_id' => $category->id,
                    'category_name' => $category->name,
                    'job_count' => $count
                ];
            }
        }
        
        // Sort by job count descending
        usort($counts, function ($a, $b) {
            return $b['job_count'] - $a['job_count'];
        });
        
        Log::info("Repository: Found job counts for categories", [
            'total_categories_with_jobs' => count($counts),
            'top_category' => $counts[0] ?? null
        ]);
        
        return $counts;
    }

    /**
     * Get recent jobs by categories in chunks for better memory management
     *
     * @param array $categoryIds Category IDs to filter by
     * @param int $days Number of days to look back
     * @param int $chunkSize Size of each chunk
     * @param callable|null $chunkCallback Optional callback for each chunk
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecentJobsByCategoriesChunked(array $categoryIds, int $days = 7, int $chunkSize = 100, callable $chunkCallback = null)
    {
        Log::info("Repository: Getting recent jobs for categories in chunks", [
            'category_ids' => $categoryIds,
            'days' => $days,
            'chunk_size' => $chunkSize
        ]);
        
        $cutoff = Carbon::now()->subDays($days);
        $allJobs = collect();
        
        // Get job categories data
        $categories = JobCategory::whereIn('id', $categoryIds)->get();
        $categoryNames = $categories->pluck('name')->toArray();
        $categoryKeywords = [];
        
        // Create search patterns based on category names
        foreach ($categoryNames as $name) {
            $categoryKeywords = array_merge($categoryKeywords, $this->getCategoryKeywords($name));
        }
        
        // Build base query
        $query = Job::where('publish_date', '>=', $cutoff);
        
        if (!empty($categoryKeywords)) {
            $query->where(function ($q) use ($categoryKeywords) {
                foreach ($categoryKeywords as $keyword) {
                    $q->orWhere('position', 'like', "%{$keyword}%");
                }
            });
        }
        
        // Process in chunks
        $query->orderBy('publish_date', 'desc')->chunk($chunkSize, function ($jobs) use (&$allJobs, $chunkCallback) {
            $allJobs = $allJobs->merge($jobs);
            
            if ($chunkCallback) {
                $chunkCallback($jobs);
            }
        });
        
        Log::info("Repository: Completed chunked retrieval", [
            'total_jobs' => $allJobs->count()
        ]);
        
        return $allJobs;
    }

    /**
     * Check if a job has been sent to a specific recipient
     *
     * @param int $setupId Notification setup ID
     * @param int $jobId Job ID
     * @param string|null $recipientEmail Optional specific recipient email
     * @return bool
     */
    public function hasJobBeenSentToRecipient(int $setupId, int $jobId, ?string $recipientEmail = null): bool
    {
        $query = DB::table('job_notification_sent_jobs')
            ->where('setup_id', $setupId)
            ->where('job_id', $jobId);
        
        if ($recipientEmail) {
            $query->where('recipient_email', $recipientEmail);
        }
        
        return $query->exists();
    }

    /**
     * Get recent jobs for notification to a specific recipient
     *
     * @param array $categoryIds Category IDs to filter by
     * @param int $setupId Notification setup ID
     * @param string $recipientEmail Recipient email
     * @param int $days Number of days to look back
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecentJobsForNotification(
        array $categoryIds,
        int $setupId,
        string $recipientEmail,
        int $days = 5
    ) {
        Log::info("Repository: Getting recent jobs for notification", [
            'setup_id' => $setupId,
            'recipient_email' => $recipientEmail,
            'category_ids' => $categoryIds,
            'days' => $days
        ]);
        
        $cutoffDate = Carbon::now()->subDays($days);

        $query = Job::query()
            ->where('publish_date', '>=', $cutoffDate)
            ->whereHas('categories', function ($q) use ($categoryIds) {
                $q->whereIn('job_categories.id', $categoryIds);
            })
            ->whereDoesntHave('sentNotifications', function ($q) use ($setupId, $recipientEmail) {
                $q->where('setup_id', $setupId)
                  ->where('recipient_email', $recipientEmail);
            })
            ->orderBy('publish_date', 'desc')
            ->limit(100);

        $jobs = $query->get();
        
        Log::info("Repository: Found jobs for notification", [
            'total_jobs' => $jobs->count()
        ]);
        
        return $jobs;
    }

    /**
     * Get category keywords for search
     *
     * @param string $categoryName
     * @return array
     */
    protected function getCategoryKeywords(string $categoryName): array
    {
        $keywords = [$categoryName];
        
        // Add variations based on category name
        switch ($categoryName) {
            case 'IT - Software':
                $keywords = array_merge($keywords, ['software', 'developer', 'engineer', 'programming', 'coder']);
                break;
            case 'IT - Hardware':
                $keywords = array_merge($keywords, ['hardware', 'network', 'infrastructure', 'support']);
                break;
            case 'Management':
                $keywords = array_merge($keywords, ['manager', 'management', 'supervisor']);
                break;
            case 'Leadership':
                $keywords = array_merge($keywords, ['lead', 'chief', 'head', 'director', 'executive']);
                break;
            case 'Information Technology':
                $keywords = array_merge($keywords, ['IT', 'technology', 'systems', 'computer']);
                break;
            case 'Human Resources':
                $keywords = array_merge($keywords, ['HR', 'recruitment', 'talent', 'hiring']);
                break;
        }
        
        return $keywords;
    }

    /**
     * Resolve which date column to use for job age filtering.
     * 
     * Priority: created_at (primary) -> publish_date (secondary)
     * Result is cached in memory for the request.
     *
     * @return string Column name to use for date filtering
     */
    private function resolveJobDateColumn(): string
    {
        static $resolvedColumn = null;
        
        if ($resolvedColumn === null) {
            // Check which columns exist and have data
            $sample = DB::table('jobs')->select('created_at', 'publish_date')->first();
            
            if ($sample && $sample->created_at) {
                $resolvedColumn = 'created_at';
                Log::debug('JobRepository: Using created_at for job date filtering');
            } elseif ($sample && $sample->publish_date) {
                $resolvedColumn = 'publish_date';
                Log::debug('JobRepository: Using publish_date for job date filtering');
            } else {
                $resolvedColumn = 'created_at'; // Default fallback
                Log::warning('JobRepository: No date columns found, defaulting to created_at');
            }
        }
        
        return $resolvedColumn;
    }

    /**
     * Resolve the best available date expression for job queries.
     * Uses COALESCE to prioritize publish_date over created_at for consistent ordering.
     *
     * @return \Illuminate\Database\Query\Expression
     */
    private function resolveJobDateExpression(): \Illuminate\Database\Query\Expression
    {
        return DB::raw('COALESCE(publish_date, created_at)');
    }

    /**
     * Get unsent jobs for a recipient across all providers within the last N days.
     * 
     * Provider-agnostic backlog query that includes jobs from ALL providers
     * (Jobs.af, ACBAR, ReliefWeb, etc.) within the specified canonical categories
     * that haven't been sent to the given recipient.
     *
     * @param array $canonicalIds Canonical category IDs subscribed by the setup
     * @param string $recipientEmail Email of the recipient to check against sent logs
     * @param int $days Number of days to look back (default 7)
     * @return Collection Jobs not yet sent to recipient
     */
    public function getUnsentJobsForRecipientByCanonicalCategories(
        array $canonicalIds, 
        string $recipientEmail, 
        int $days = 7
    ): Collection {
        Log::info('JobRepository: Fetching unsent jobs for recipient by canonical categories', [
            'canonical_ids' => $canonicalIds,
            'recipient_email' => $recipientEmail,
            'days_back' => $days,
        ]);

        if (empty($canonicalIds)) {
            Log::warning('JobRepository: No canonical categories provided');
            return collect();
        }

        $dateExpression = $this->resolveJobDateExpression();
        $cutoffDate = Carbon::now()->subDays($days);
        
        // Mask email for PII protection in logs
        $maskedEmail = substr($recipientEmail, 0, 1) . '***@' . substr($recipientEmail, strpos($recipientEmail, '@') + 1);

        $query = Job::query()
            ->whereRaw('COALESCE(publish_date, created_at) >= ?', [$cutoffDate])
            ->whereHas('categories', function ($q) use ($canonicalIds) {
                $q->whereIn('job_categories.id', $canonicalIds);
            })
            ->whereDoesntHave('sentNotifications', function ($q) use ($recipientEmail) {
                $q->where('recipient_email', $recipientEmail);
            })
            ->active() // Add active scope for consistency with other repository methods
            ->orderByRaw('COALESCE(publish_date, created_at) desc')
            ->limit(300); // Reasonable cap to prevent huge result sets

        $jobs = $query->get();

        Log::info('JobRepository: Found unsent jobs for recipient', [
            'job_count' => $jobs->count(),
            'date_expression_used' => 'COALESCE(publish_date, created_at)',
            'cutoff_date' => $cutoffDate->format('Y-m-d H:i:s'),
            'recipient_email_masked' => $maskedEmail,
        ]);

        return $jobs;
    }
}
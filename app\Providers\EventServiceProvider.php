<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;

use Illuminate\Support\Facades\Event;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            // TODO : enable this after settling the email issue.
            SendEmailVerificationNotification::class,
        ],
        'App\Events\Event' => [
            'App\Listeners\EventListener',
        ],
        'Illuminate\Mail\Events\MessageSending' => [
            'App\Listeners\LogSentMessage',
        ],
        'Illuminate\Auth\Events\Verified' => [
            'App\Listeners\LogVerifiedUser',
            \App\Listeners\ClearSuccessFlash::class
        ],
        'eloquent.saved: App\ClassStudent' => [
            'App\Listeners\ClearStudentsCache',
        ],
        'eloquent.deleted: App\ClassStudent' => [
            'App\Listeners\ClearStudentsCache',
        ],

        // Use the proper Eloquent event notation (<PERSON><PERSON> fires 11 model events)
        'eloquent.created: ' . \App\Student::class => [
            \App\Listeners\AttachStagedStudentPhotos::class,
        ],


    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        // Register Observers
        \App\Student::observe(\App\Observers\StudentObserver::class);
    }
}

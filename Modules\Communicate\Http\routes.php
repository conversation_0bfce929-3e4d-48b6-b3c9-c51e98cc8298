<?php

Route::group(['middleware' => ['web', 'auth:employee','missedClockOutMiddleware'], 'prefix' => 'workplace/communicate', 'namespace' => 'Modules\Communicate\Http\Controllers'], function()
{

    // Communicate

    Route::get('notice-list', 'CommunicateController@noticeList')->name('notice-list')->middleware('userRolePermission:287');
    Route::get('administrator-notice', 'CommunicateController@administratorNotice')->name('administrator-notice');
    Route::get('add-notice', 'CommunicateController@sendMessage')->name('add-notice');
    Route::post('save-notice-data', 'CommunicateController@saveNoticeData')->name('save-notice-data');
    Route::get('edit-notice/{id}', 'CommunicateController@editNotice')->name('edit-notice');
    Route::post('update-notice-data', 'CommunicateController@updateNoticeData')->name('update-notice-data');
    Route::get('delete-notice-view/{id}', 'CommunicateController@deleteNoticeView')->name('delete-notice-view')->middleware('userRolePermission:290');
    Route::get('send-email-sms-view', 'CommunicateController@sendEmailSmsView')->name('send-email-sms-view')->middleware('userRolePermission:291');
    Route::post('send-email-sms', 'CommunicateController@sendEmailSms')->name('send-email-sms')->middleware('userRolePermission:292');
    Route::get('email-sms-log', 'CommunicateController@emailSmsLog')->name('email-sms-log')->middleware('userRolePermission:293');
    Route::get('delete-notice/{id}', 'CommunicateController@deleteNotice')->name('delete-notice');

    Route::get('studStaffByRole', 'CommunicateController@studStaffByRole');

//    Route::get('email-sms-log-ajax', 'DatatableQueryController@emailSmsLogAjax')->name('emailSmsLogAjax')->middleware('userRolePermission:293');
    


    //Event
    Route::resource('event', 'EventController')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');
    Route::get('delete-event-view/{id}', 'EventController@deleteEventView')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');
    Route::get('delete-event/{id}', 'EventController@deleteEvent')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');

//    Route::get('/', 'CommunicateController@index');
});

<?php

namespace Modules\Admission\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class StudentOfffered extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($user_info, $sender)
    {
        $this->user_info = $user_info;
        $this->sender = $sender;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Offer Letter '.$this->user_info[0]['programTitle'].' program')
            ->view('modules.site.templates.wajeha.backEnd.studentInformation.student_offer_letter', ['data'=> $this->user_info[0]])
            ->attach(storage_path("app/offerLetters/".$this->user_info[0]['student_id'].'-offerLetter.pdf'), [
                'as' => $this->user_info[0]['student_id'].'-offerLetter.pdf',
                'mime' => 'application/pdf',
            ]);

    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}

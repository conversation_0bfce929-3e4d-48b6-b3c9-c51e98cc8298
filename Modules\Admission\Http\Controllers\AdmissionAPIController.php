<?php

namespace Modules\Admission\Http\Controllers;


use App\AdmissionInterviewer;
use App\EmailSetting;
use App\Employee;
use App\GeneralSettings;
use App\Http\Requests\StudentApproveRequest;
use App\Services\EmailService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Program;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Admission;
use App\AdmissionInterview;
use App\Student;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Admission\Http\Requests\StudentAdmissionAPIRequest;

class AdmissionAPIController extends Controller
{


    public function __construct()
    {


    }


    public function update(StudentAdmissionAPIRequest $request)
    {



        try {
            DB::beginTransaction();


//            $this->validateRequest($request);
            $admission = $this->retrieveAdmission($request->admissionId);
            $student = $admission->student;
            $user = $this->retrieveOrCreateUser($admission);


            $this->updateAdmissionAndStudent($request, $admission, $student);
            $this->syncAdmissionPrograms($request->programId, $admission);

            $studentName = $this->fetchStudentName($admission->student_id);
            $programTitle = $this->fetchProgramTitle($admission->program_id);
            $userInfo = $this->collectUserInfo($admission, $studentName, $programTitle);


            if ($request->admission_status == 'accepted') {
                $this->handleOfferAcceptance($request, $admission, $user, $userInfo, $student);
            }

            $user->save();
            DB::commit();
            // Update the response to include class_id and class_name
            return response()->json([
                'message' => 'Update successful',
                'center_title' => $admission->center->title,
                'status' => $admission->status,
                'class_id' => $admission->class_id,
                'class_name' => $admission->class->name,
                'program_title' => $programTitle
            ], 200);
        } catch (\Exception $e) {
            DB::rollback(); // Ensure you rollback if there's an error
            Log::error('Update failed: ', ['exception' => $e]); // Log the exception to Laravel's log

            // Log the exception and rollback the transaction
            return response()->json(['error' => 'An error occurred: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @param StudentAdmissionAPIRequest $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function accept(StudentAdmissionAPIRequest $request): \Illuminate\Http\JsonResponse
    {
        DB::beginTransaction();
        try {

            $admission = $this->retrieveAdmission($request->admissionId);
            $student = $admission->student;
            $this->finalizeStudent($admission);
            $user = $this->retrieveOrCreateUser($admission);
            $this->activateAndUpdateAdmissionWithStudent($request, $admission, $student);
            $this->syncAdmissionPrograms($request->programId, $admission);

            $studentName = $this->fetchStudentName($admission->student_id);
            $programTitle = $this->fetchProgramTitle($admission->program_id);
            $userInfo = $this->collectUserInfo($admission, $studentName, $programTitle);

            $this->attachOrUpdateClassForStudent($request->class_id, $student);
            if (! \App::environment('local')) {

                $this->sendActivationEmail($admission, $student);
            }
            $this->handleOfferAcceptance($request, $admission, $user, $userInfo, $student);

            $user->save();
            DB::commit();
            return response()->json([
                'message' => 'Update successful',
                'class_id' => $admission->class_id,
                'class_name' => $admission->class->name,
                'program_name' => $admission->program->title,
                'program_id' => $admission->program_id
            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Update admission failed: ', ['exception' => $e->getMessage()]);
            // Log the exception and rollback the transaction
            return response()->json(['error' => 'An error occurred: ' . $e->getMessage()], 500);
        }
    }


    private function retrieveAdmission($admissionId)
    {
        try {
            return Admission::findOrFail($admissionId);
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Admission not found'], 404);
        }
    }

    private function validateRequest(Request $request)
    {

        $messages = [
            'centerId.required' => 'The Center ID is a mandatory field.',

            'status.required' => 'The Status field cannot be left empty. Please provide a status.',

            'admissionId.required' => 'Admission ID must be provided.',
            'admissionId.integer' => 'The Admission ID should be an integer value.',
            'admissionId.exists' => 'The provided Admission ID cannot be found. Please make sure to provide a valid Admission ID.',

            'classId.required' => 'Class ID is required.',
            'classId.integer' => 'The Class ID should be an integer value.',
            'classId.exists' => 'The provided Class ID does not exist. Please provide a valid Class ID.',

            'programId.required' => 'Program ID is a required field.',
            'programId.integer' => 'The Program ID should be an integer value.',
            'programId.exists' => 'The provided Program ID does not exist. Please provide a valid Program ID.',
        ];
        try {
            $this->validate($request, [
                'centerId' => 'required',
                'status' => 'required',
                'admissionId' => 'required|integer|exists:admissions,id',
                'classId' => 'required|integer|exists:classes,id',
                'programId' => 'required|integer|exists:programs,id',
            ], $messages);

        } catch (\Illuminate\Validation\ValidationException $exception) {
            return response()->json([
                'status' => 'error',
                'errors' => $exception->errors(),
            ], 422);
        }
    }

    private function retrieveOrCreateUser($admission)
    {
        $student = $admission->student;

        if (is_null($admission->student->user)) {
            $user = User::create([
                'email' => $student->email,
                'display_name' => $student->full_name,
                'full_name_trans' => $student->full_name_trans,
                'username' => $student->id,
                'full_name' => $student->full_name,
                'nationality' => $student->nationality,
                'access_status' => '0',
                'is_administrator' => 'no',
                'organization_id' => config('organization_id'),
                'password' => bcrypt($student->password),
                'email_verified_at' => now(), // Admin-created accounts are pre-verified
            ]);
            $this->assignDefaultRoles($user, 'member');
            $student->user_id = $user->id;
            return $user;
        } else {
            return $admission->student->user;
        }
    }

    private function updateAdmissionAndStudent(Request $request, $admission, $student)
    {
        $admission->center_id = $request->centerId;
        $admission->class_id = $admission->class_id;

        // Set student status to 'active' if request status is 'accepted'
        if ($request->status == 'accepted') {
            $student->status = 'active';
            $admission->status =  'active';

            // Check if the program is Nuraniyah
            $program = Program::find($request->programId);
            if (\Illuminate\Support\Str::contains(strtolower($program->title), ['nuraniyah', 'nouranya'])) {

                // Fetch the ProgramLevel where the translated title contains 'level 1' or 'level1'
                $levelId = \App\ProgramLevel::whereHas('translations', function ($query) {
                    $query->where('title', 'LIKE', '%level 1%')
                        ->orWhere('title', 'LIKE', '%level1%');
                })->first()->id;
                $studentProgramLevel = $student->studentProgramLevels()->updateOrCreate(
                    ['student_id' => $student->id, 'class_id' => $admission->class_id],
                    ['level_id' => $levelId, 'status' => 'active']
                );
            }
        } else {
            $student->status = $request->status;  // Or handle other statuses differently if needed
            $admission->status = $request->status;  // Or handle other statuses differently if needed
        }
//        $student->status = $request->status;
        $admission->update();
        $student->save();
    }
    private function activateAndUpdateAdmissionWithStudent(Request $request, $admission, $student)
    {
        $admission->status = 'active';
        $admission->start_date = date('Y-m-d');
        $admission->center_id = $request->centerId;
        $admission->class_id = $request->classId;
        $student->status = 'active';

        // Check if the program is Nuraniyah
        $program = Program::find($request->programId);
        if (\Illuminate\Support\Str::contains(strtolower($program->title), ['nuraniyah', 'nouranya'])) {
            // Fetch the ProgramLevel where the translated title contains 'level 1' or 'level1'
            $levelId = \App\ProgramLevel::whereHas('translations', function ($query) {
                $query->where('title', 'LIKE', '%level 1%')
                    ->orWhere('title', 'LIKE', '%level1%');
            })->first()->id;
            
            $studentProgramLevel = $student->studentProgramLevels()->updateOrCreate(
                ['student_id' => $student->id, 'class_id' => $admission->class_id],
                ['level_id' => $levelId, 'status' => 'active']
            );
        }
        $admission->update();
        $student->save();
    }

    private function attachOrUpdateClassForStudent($classId, $student): void
    {
        // Retrieve pivot table query for the specific class-student combination
        $pivotQuery = $student->joint_classes()->where('class_id', $classId);

        // Check if the record exists
        if ($pivotQuery->exists()) {
            // Update existing record
            $pivotQuery->update(['start_date' => date('Y-m-d')]);
        } else {
            // Attach new record with additional pivot data
            $student->joint_classes()->attach($classId, ['start_date' => date('Y-m-d')]);
        }
    }

    private function attachClassToStudent($classId, $student)
    {
        $student->joint_classes()->attach($classId, ['start_date' => date('Y-m-d')]);
    }

    private function syncAdmissionPrograms($programId, $admission)
    {
        $admission->programs()->sync([$admission->program_id]);
    }

    private function handleOfferLetter(Request $request, $admission, $userInfo,$user)
    {
        try {

            $offerLetterIssuanceDate = Carbon::now()->toDateString();
            $admission->update(['offer_letter_issuance_date' => $offerLetterIssuanceDate]);
            // offer letter arrangement starts here
            $offerLetterRawContent = $admission->programs()->pluck('offer')[0];
            $offerLetterRawContent = str_replace("[date]", $offerLetterIssuanceDate, $offerLetterRawContent);
            $offerLetterRawContent = str_replace("[address]", 'Dummy Address input by Hashmat', $offerLetterRawContent);
            $offerLetterRawContent = str_replace("[studentName]", Student::find($admission->student_id)->full_name, $offerLetterRawContent);
            $offerLetterRawContent = str_replace("[programName]", "<i>" . Program::find($request->program_id)->title . "</i>", $offerLetterRawContent);
            $offerLetterRawContent = str_replace("[registrationFees]", ' < strong>100 </strong > ', $offerLetterRawContent);
            $offerLetterRawContent = str_replace("[monthlyFeesAmount]", '<strong > 200</strong > ', $offerLetterRawContent);


            $pdf = app()->make('dompdf.wrapper');
            $pdf->loadHTML(htmlspecialchars_decode($offerLetterRawContent));
            $content = $pdf->download()->getOriginalContent();
            //save the pdf offer letter in storage folder
            \Storage::put("offerLetters/" . $admission->student_id . "-offerLetter.pdf", $content);

            if (! \App::environment('local')) {
            $userInfo[0]["subject"] = 'Offer Letter ' . $userInfo[0]['programTitle'] . ' program';
            $userInfo[0]["Filename"] = $userInfo[0]['student_id'] . '-offerLetter.pdf';
            $userInfo[0]["filePath"] = storage_path("app/offerLetters/" . $userInfo[0]['student_id'] . '-offerLetter.pdf');
            $userInfo[0]["attachment"] = true;
//            we are going to attach the offer letter in the Job by making using of the student_is as it is used as the filename in the storage/app/offerLetters directory
            $userInfo[0]["view"] = view('modules.site.templates.wajeha.backEnd.studentInformation.student_offer_letter')->render();
            $viewData = ['data' => $userInfo[0]];
            $to = ['email' => $user->email, 'name' => $user->full_name];
            $emailService = app(\App\Services\EmailService::class); 
            $emailService->sendEmail($to, $userInfo[0]["subject"], $userInfo[0]["view"], $viewData, [], $cc = []);
            }
        } catch (\Exception $e) {
            // If something goes wrong, catch the exception here and handle it in a way that fits your needs.
            \Log::error("Error generating offer letter: " . $e->getMessage());

            // You cannot return in a private void method, but in case this was in an API controller for example, this is how you'd return a JSON response with an error message:
            // return response()->json(['error' => 'Error generating offer letter: ' . $e->getMessage()], 500);
        }
    }

    private function fetchStudentName($student_id)
    {
        return Student::find($student_id)->full_name;
    }

    private function fetchProgramTitle($program_id)
    {
        return Program::find($program_id)->title;
    }

    private function collectUserInfo($admission, $studentName, $programTitle)
    {
        $userInfo[0]["student_id"] = $admission->student_id;
        $userInfo[0]["student_email"] = $admission->student_email;
        $userInfo[0]["channel"] = "email";
        $userInfo[0]["studentName"] = $studentName;
        $userInfo[0]["programTitle"] = $programTitle;
        return $userInfo;
    }


    private function handleOfferAcceptance(Request $request, $admission, $user, $userInfo, $student): void
    {

        $userInfo = [];
        if ($request->filled('interviewed') && $request->get('interviewed') == 'yes') {
            // Update AdmissionInterview status
            AdmissionInterview::where('admission_id', $admission->id)->update([
                'status' => 'interviewed',
                'notes' => $request->get('notes'),
                'confirmed_at' => Carbon::now(),
                'updated_by' => auth()->user()->id,
            ]);
        }


        // Assign this role after the payment is made
        $user->assignRole('student');

        // Retrieve system settings
        $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);
        $systemEmail = EmailSetting::find(1);
        $system_email = $systemEmail->from_email;
        $organization_name = $systemSetting->organization_name;
        $sender['system_email'] = $system_email;
        $sender['organization_name'] = $organization_name;

        // If the creator_role is 'parent', assign guardian_email
        if ($admission->creator_role == 'parent') {
            $userInfo[0]["guardian_email"] = $admission->guardian_email;
        }

        // Handle student email (student acceptance into the class) based on whether the student has a guardian or not
        if ($student->guardian_id) {
            $this->handleGuardianScenario($admission, $user, $userInfo, $sender, $student);
        } else {
            $this->handleNonGuardianScenario($userInfo, $user, $sender);
        }


        $this->handleOfferLetter($request, $admission, $userInfo,$user);

//        $this->finalize($request);


    }


    /**
     * @throws \Exception
     */
    private function handleGuardianScenario($admission, $user, $userInfo, $sender, $student)
    {




            $userInfo[0]["view"] = view('modules.site.templates.wajeha.backEnd.studentInformation.dependant_student_acceptance_confirmation')->render();
            $userInfo[0]["subject"] = 'You are accepted to the ' . $userInfo[0]['programTitle'] . ' Program';
            $viewData = ['data' => $userInfo[0]];
            $to = ['email' => $user->email, 'name' => $user->full_name];

            try {

                $emailService = app(\App\Services\EmailService::class);     
                $emailService->sendEmail($to, $userInfo[0]["subject"], $userInfo[0]["view"], $viewData, [], $cc = []);

                Toastr::success('Email successfully sent', 'Success');
            } catch (\Exception $e) {
                Log::error('Email failed to send. Error: ' . $e->getMessage());
                return response()->json(['error' => 'Email failed to send. Please try again later.'], 500);
            }
        }


    private function handleNonGuardianScenario($userInfo, $user, $sender)
    {
        // Check if the environment is local
        if (\App::environment('local')) {

            return 1;
        } else {


            $userInfo[0]["view"] = view('modules.site.templates.wajeha.backEnd.studentInformation.student_acceptance_confirmation')->render();
            $userInfo[0]["subject"] = 'You are accepted to the ' . $userInfo[0]['programTitle'] . ' Program';
            $viewData = ['data' => $userInfo[0]];
            $to = ['email' => $user->email, 'name' => $user->full_name];

            try {

                $emailService = app(\App\Services\EmailService::class); 
                $emailService->sendEmail($to, $userInfo[0]["subject"], $userInfo[0]["view"], $viewData, [], $cc = []);

                Toastr::success('Email successfully sent', 'Success');
            } catch (\Exception $e) {
                Log::error('Email failed to send. Error: ' . $e->getMessage());
                return response()->json(['error' => 'Email failed to send. Please try again later.'], 500);
            }
        }
    }

    public function assignDefaultRoles($user, $roleName = null)
    {


//        $default_role_name = Settings::get('default_user_role', 'member');
//        $default_role_name = Settings::get('default_user_role', 'member');


//        $roleName = $default_role_name;


        $user->assignRole('member');
    }


    /**
     * Finalize the admission process for a request.
     *
     * This method performs the necessary operations to finalize an admission, including updating the admission status,
     * setting the student to active, and adding the student to a class. If any error occurs during the process,
     * an appropriate error response is returned.
     *
     * @param Request $request The HTTP request object containing the admission ID.
     * @return \Illuminate\Http\JsonResponse The JSON response containing the result of the operation.
     *
     * @throws \Exception If an error occurs during the finalization process.
     */
    public function finalize(Request $request)
    {


        try {
            DB::beginTransaction();
            $admission = $this->finalizeAdmission($request);
            $student = $this->finalizeStudent($admission);
            $this->attachClassToStudent($request->class_id, $student);
            $email = $this->saveEmailDetails($admission, $student);
            $this->sendActivationEmail($admission, $student);
            $message = (new \App\ClassStudent())->addStudentToClass($admission);

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();

            // For simplicity, return the exception message in development. In production, return a more generic error message
            return response()->json([
                'message' => 'An error occurred while finalizing the request.',
                'error' => $e->getMessage()
            ], 500);
        }

        return response()->json(['message' => $message, 'data' => ['class' => $admission->class->name, 'center' => $admission->center->name, 'status' => $admission->status]], 200);


    }

    private function finalizeAdmission(Request $request): Admission
    {
        $admission = Admission::findOrFail($request->admission_id);
        $admission->finalizeAdmission();

//        \App\AdmissionOrientation::create([
//            'organization_id' => config('organization_id'),
//            'admission_id' => $admission->id,
//            'employee_id' => auth()->user()->id,
//            'orientation_time' => Carbon::now(),
//            'note' => '',
//            'created_by' => auth()->user()->id,
//            'status' => ''
//        ]);

        return $admission;
    }

    private function finalizeStudent(Admission $admission): Student
    {
        $student = Student::findOrFail($admission->student_id);
        $student->setActive();

        return $student;
    }

    /**
     * @throws \Exception
     */
    private function sendActivationEmail($admission, $student): void
    {

        $emailService = app(\App\Services\EmailService::class);
        $emailService->sendEmail(
            ['email' => $admission->student_email, 'name' => $student->full_name],
            'You are added to class ' . $student->joint_classes()->name,
            'emails.student_activation_email', // Updated view path
            ['student' => $student, 'admission' => $admission] // Passing necessary data to the view

        );



    }

    private function saveEmailDetails($admission, $student)
    {
        $emailContent = "<p>Dear {$student->full_name},</p>"
            . "<p>You are now added (Active) to class : {$student->joint_classes()->name}"
            . " in the center: {$admission->center->name}.</p>"
            . "<p>Congratulations and welcome!</p>";

        return \App\Email::create([
            'email' => $admission->student_email,
            'subject' => 'Class Enrollment Confirmation',
            'message' => $emailContent
        ]);
    }

    public function setInterview(StudentApproveRequest $request)
    {


        try {

            DB::beginTransaction();


            $admission = Admission::findOrFail($request->admission_id);
            $student = $admission->student;
            $interviewTime = $request->get('interview_time');
            $hoursToInterview = $this->calculateHoursToInterview($interviewTime);


            // return $admission->programs;
            $waiting_for_interview = false;
            $approved_programs = 0;
            // get admission programs
            foreach ($admission->programs as $program) {
                // check if the admission aproved
                if ($request->interview[$program->id]['approve']) {
                    $approved_programs++;
                    // check if an interview is required
                    if ($program->require_interview) {
                        $waiting_for_interview = true;
                        // create a new interview record
                        $interview_details = $request->interview[$program->id];
                        $interview_details['admission_id'] = $request->admission_id;
                        $interview_details['program_id'] = $program->id;
                        $interview_details['status'] = 'waiting_for_interview';
                        $userInfo[] = array('interviewLocation' => $interview_details['location'], 'interview_duration' => '1 hour',
                            'interviewDateTime' => $interview_details['interview_time'], 'email' => $admission->student_email,
                            'id' => $student->id, 'slug' => 'student', 'interviewCommitteeIds' => $interview_details['committee'],
                            'interviewDetails' => $interview_details['notes'], 'programTitle' => $program->programTranslations->first()->title);
                        $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);
                        $systemEmail = EmailSetting::find(1);
                        $system_email = $systemEmail->from_email;
                        $organization_name = $systemSetting->organization_name;
                        $sender['system_email'] = $system_email;
                        $sender['organization_name'] = $organization_name;
                        if ($admission->creator_role == 'parent') {
                            $userInfo[0]["guardian_email"] = $admission->guardian_email;
                        }
                        $userInfo[0]["email"] = $admission->student_email;
                        $userInfo[0]["channel"] = "email";
                        $userInfo[0]["interviewConfirmationExpiryHours"] = $hoursToInterview;
                        $userInfo[0]["admissionId"] = $request->admission_id;
                        $userInfo[0]["phone"] = GeneralSettings::where("organization_id", config('organization_id'))->first()->phone;
                        $userInfo[0]["studentName"] = Student::find($admission->student_id)->full_name;
                        $userInfo[0]["subjectInterviewParticipants"] = implode(", ", Employee::whereIn("id", $userInfo[0]['interviewCommitteeIds'])->pluck("name")->toArray()) . " & " . $student->display_name;
                        $userInfo[0]["InterviewerEmails"] = Employee::whereIn("id", $userInfo[0]['interviewCommitteeIds'])->pluck("email")->toArray();
                        // save the interview details
                        dd($request->all());
                        $interview = AdmissionInterview::create($interview_details);
                        // set interview committee
                        $interview_committee = $interview_details['committee'];

                        $this->createInterviewCommitteeMembers($interview_committee, $interview->id);
                        $this->sendInterviewInvitationEmail($userInfo, $sender);


                    }
                }
            }


            if ($waiting_for_interview) {
                $admission->status = 'waiting_for_interview';
                $student->status = 'waiting_for_interview';
            } else if ($approved_programs == $admission->programs->count()) {
                $admission->status = 'offer';
                $student->status = 'offer';
            } else if ($approved_programs > 0) {
                $admission->status = 'conditional_offer';
            } else {

                $admission->status = 'rejected';
                $admission->rejected_status_note = $request->interview[$program->id]['notes'];
                $student->status = 'rejected';
            }
            $admission->save();
            $student->save();
            DB::commit();
            if ($request->ajax()) {
                return response()->json([
                    "status" => "success"
                ]);
            }
            return redirect()->back();

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Failed to approve student: ' . $e->getMessage());
            if ($request->ajax()) {
                return response()->json([
                    "status" => "error",
                    "message" => 'Failed to approve student: ' . $e->getMessage()
                ]);
            } else {
                return back()->withErrors(['error' => 'Failed to approve student: ' . $e->getMessage()]);
            }
            // Handle the error
        }

    }

    function sendInterviewInvitationEmail($userInfo, $sender)
    {

        // send an email to the student inviting them to an interview
        // TODO: fix the default laravel email functionality so that this line of code works fine
//                    $user->notify(new InterviewInvitationSent($userInfo, $sender));

        $success = retry(5, function () use ($userInfo, $sender) {
            dispatch(new \App\Jobs\SendStudentInterviewInvitationEmailJob($userInfo[0], $sender));
            return true;
        }, 100);
        if (!$success) {
            \Log::error('Failed to send interview invitation email after 5 attempts');
            return false;
        }
        Toastr::success('Email Successfully sent', 'Success');

    }

    function createInterviewCommitteeMembers($interview_committee, $interview_id)
    {
        foreach ($interview_committee as $interviewer) {
            // create a new interview committee member record
            AdmissionInterviewer::create([
                'admission_interview_id' => $interview_id,
                'employee_id' => $interviewer
            ]);
        }
    }

    public function archiveStudent(Request $request)
    {

        try {

            $className = '';
            DB::transaction(function () use ($request) {

                $student = Student::with(['user', 'admissions'])->findOrFail($request->get('student_id'));
                // Store rejection notes if provided and if the field exists in your model
                if ($request->filled('rejectionNotes')) {
                    $student->delete_reason = $request->input('rejectionNotes');

                }
                $student->status = 'archived';
                $student->save();
                // Soft delete related user
                if ($student->user) {
                    $student->user->delete();
                }


                // Soft delete related admissions
                foreach ($student->admissions as $admission) {
                    $admission->status = 'archived';
                    $admission->save();
                    $className = $admission->class->name;
                    $admission->delete();
                }

                // Soft delete the student
                $student->delete();
            });

            return response()->json([
                'message' => 'Student and related data soft deleted successfully.',
                'class_name' => $className
            ], 200);
            return response()->json(['message' => 'Student and related data soft deleted successfully.'], 200);
        } catch (ModelNotFoundException $e) {
            return response()->json(['message' => 'Student not found.'], 404);
        } catch (\Exception $e) {
            return response()->json(['message' => 'An error occurred while deleting the student.'], 500);
        }


        if ($request->status == 'rejected') {


            // Soft delete related user and admission data
            $student->user->delete();
            // Soft delete related admissions
            foreach ($student->admissions as $admission) {
                $admission->delete();
            }
            // Soft delete the student
            $student->delete();
            return response()->json(['message' => 'Student archived'], 200);

        }


    }


}

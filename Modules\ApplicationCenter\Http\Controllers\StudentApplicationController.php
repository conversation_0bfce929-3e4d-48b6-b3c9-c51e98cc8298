<?php

namespace Modules\ApplicationCenter\Http\Controllers;

use App\Admission;
use App\ApiBaseMethod;
use App\Center;
use App\CenterEmployee;
use App\Employee;
use App\Exceptions\Handler;
use App\Http\Requests\StudentOfferDownloadRequest;
use App\Mail\OrderShipped;
use App\MalaysiaPostcode;
use App\Program;
use App\ProgramTranslation;
use App\Role;
use App\EmailSetting;
use App\GeneralSettings;
use App\Rules\CheckDependentEmail;
use App\Student;
use App\StudentHefzReport;
use App\User;
use App\Classes;
use App\Guardian;
use App\Organization;
use App\Section;
use App\UserLog;
use App\BaseSetup;
use App\AcademicYear;


use App\Vehicle;
use Barryvdh\DomPDF\PDF;
use Carbon\Carbon;
use Doctrine\DBAL\Exception\DatabaseObjectExistsException;
use Dompdf\Options;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;
use Modules\ApplicationCenter\Entities\RegistrationSetting;
use Modules\ApplicationCenter\Http\Requests\DependentStudentApplicationRequest;
use Modules\ApplicationCenter\Http\Requests\StudentApplicationRequest;
use Modules\ApplicationCenter\Http\Requests\StudentRegistrationByEmployeeRequest;
use PhpOffice\PhpWord\PhpWord;
use App\Services\StudentImageService;
use Modules\ApplicationCenter\Entities\StudentRegistration;

class StudentApplicationController extends Controller
{
    private $User;
    private $GeneralSettings;
    private $UserLog;
    private $InfixModuleManager;
    private $URL;
    private $TYPE;

    public function __construct()
    {


//        $this->middleware('PM');


//        $this->User                 = json_encode(User::find(1));
//        $this->GeneralSettings    = json_encode(GeneralSettings::find(1));
//        $this->UserLog            = json_encode(UserLog::find(1));
//        // $this->InfixModuleManager   = json_encode(InfixModuleManager::find(1));
//        $this->URL                  = url('/');
//        $this->TYPE                 = 1;

    }


    public function index()
    {
        // $module = 'StudentDashboard';
        // if (User::checkPermission($module) != 100) {
        //     Toastr::error('Please verify your ' . $module . ' Module', 'Failed');
        //     return redirect()->route('Moduleverify', $module);
        // }

        try {
            // if (date('d') <= 15) {
            //     $client = new \GuzzleHttp\Client();
            //     $s = $client->post(User::$api, array('form_params' => array('TYPE' => $this->TYPE, 'User' => $this->User, 'GeneralSettings' => $this->GeneralSettings, 'UserLog' => $this->UserLog, 'InfixModuleManager' => $this->InfixModuleManager, 'URL' => $this->URL)));
            // }
        } catch (\Exception $e) {
            Log::error($e);
            Log::info($e->getMessage());
        }
        try {
            return view('applicationcenter::index');
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    function admissionPic(Request $r)
    {
        try {
            $referrerUrl = url()->previous();
            $referrerRouteName = app('router')->getRoutes()->match(app('request')->create($referrerUrl))->getName();

            Log::info('Starting photo upload process', [
                'referrer_route' => $referrerRouteName,
                'has_file' => $r->hasFile('logo_pic')
            ]);

            $validator = Validator::make($r->all(), [
                'logo_pic' => 'sometimes|required|mimes:jpg,png,gif|max:40000',
            ]);

            if ($validator->fails()) {
                Log::warning('Photo validation failed', [
                    'errors' => $validator->errors()->toArray()
                ]);
                return response()->json(['success' => false, 'message' => 'Invalid image format or size. Please upload a JPG, PNG, or GIF file under 40MB.'], 201);
            }

            // Instantiate the service
            $studentImageService = app(StudentImageService::class);
            $targetModel = null; // Determine the correct model (Student, Guardian, User) based on context
            $photoKey = '';

            // Determine the target model and session key based on the referrer
            if ($referrerRouteName == 'student.guardian.application.form') {
                $photoKey = 'dependent_photo';
                // For guardian registering a dependent, we'll store the image temporarily 
                // with the guardian's ID and a unique identifier for the dependent
                $guardianUser = Auth::guard("web")->user();
                $dependentIdentifier = 'dependent_' . time(); // Using timestamp as unique identifier
            } else {
                $photoKey = 'student_photo';
                // Assumption: Logged-in user is uploading their own photo.
                if (Auth::check()) {
                    $targetModel = Auth::user()->student ?? Auth::user(); // Prefer student model if available
                }
            }


            // Handle logo_pic upload
            if ($r->hasFile('logo_pic')) {
                $file = $r->file('logo_pic');
                $imagePath = '';
                
                if ($referrerRouteName == 'student.guardian.application.form' && isset($guardianUser)) {
                    // Store image for dependent using the guardian's ID
                    Log::info('Storing temporary dependent image', [
                        'guardian_id' => $guardianUser->id,
                        'dependent_identifier' => $dependentIdentifier
                    ]);
                    
                    $imagePath = $studentImageService->storeTemporaryDependentImage(
                        $guardianUser, 
                        $dependentIdentifier, 
                        $file
                    );
                    
                    // Store the dependent identifier in session for later use
                    Session::put('dependent_identifier', $dependentIdentifier);
                    Log::info('Stored dependent identifier in session', ['identifier' => $dependentIdentifier]);
                } 
                else if ($targetModel) {
                    // Store using the service if we have a target model
                    $imagePath = $studentImageService->storeStudentImage($targetModel, $file);
                    
                    Log::info('Photo uploaded via service for logo_pic', ['path' => $imagePath]);
                } else {
                    // Fallback: Manual storage if no target model (like the original code)
                    // This might be needed if the photo is stored before the student record exists.
                    $images = Image::make($file);
                    $pathImage = storage_path('app/public/uploads/student');
                    if (!file_exists($pathImage)) {
                        mkdir($pathImage, 0775, true);
                    }
                    $name = md5($file->getClientOriginalName() . time()) . ".png";
                    $fullPath = $pathImage . '/' . $name;
                    $images->save($fullPath);
                    $imagePath = 'public/uploads/student/' . $name;
                    Log::warning('Photo stored manually (no target model) for logo_pic', ['path' => $imagePath]);
                }

                // Handle session storage
                if (session()->has($photoKey) && $imagePath !== session($photoKey)) {
                    // Optionally delete the old file from session if needed
                    // $oldPath = session($photoKey);
                    // if (Storage::disk('public')->exists(str_replace('public/', '', $oldPath))) {
                    //     Storage::disk('public')->delete(str_replace('public/', '', $oldPath));
                    // }
                }
                Session::put($photoKey, $imagePath); // Store the actual path

                $photoUrl = Storage::url(str_replace('public/', '', $imagePath)); // Use Storage::url()
                return response()->json(['success' => true, 'message' => 'Photo uploaded successfully', 'photo_url' => $photoUrl], 200);
            }

            // Handle guardians_photo upload (similar logic, needs context for target model)
            if ($r->hasFile('guardians_photo')) {
                $file = $r->file('guardians_photo');
                $guardianPhotoKey = 'guardians_photo';
                $guardianTargetModel = null; // Determine guardian model if applicable
                // Example: $guardianTargetModel = Guardian::find($guardianId); or Auth::user();
                $imagePath = '';

                if ($guardianTargetModel) {
                    // Store using the service if we have a target model
                    $imagePath = $studentImageService->storeStudentImage($guardianTargetModel, $file); // Use appropriate method if not for student
                    Log::info('Photo uploaded via service for guardians_photo', ['path' => $imagePath]);
                } else {
                    // Fallback: Manual storage
                    $images = Image::make($file);
                    $pathImage = storage_path('app/public/uploads/guardian'); // Example path
                    if (!file_exists($pathImage)) { mkdir($pathImage, 0775, true); }
                    $name = md5($file->getClientOriginalName() . time()) . ".png";
                    $fullPath = $pathImage . '/' . $name;
                    $images->save($fullPath);
                    $imagePath = 'public/uploads/guardian/' . $name;
                    Log::warning('Photo stored manually (no target model) for guardians_photo', ['path' => $imagePath]);
                }

                // Handle session storage
                if (session()->has($guardianPhotoKey) && $imagePath !== session($guardianPhotoKey)) {
                    // Optional: delete old file
                }
                Session::put($guardianPhotoKey, $imagePath);

                $photoUrl = Storage::disk('public')->url(str_replace('public/', '', $imagePath));
                return response()->json(['success' => true, 'message' => 'Guardian photo uploaded successfully', 'photo_url' => $photoUrl], 200);
            }

            Log::warning('No photo file was provided in the request');
            return response()->json(['success' => false, 'message' => 'No photo file was provided.'], 201);

        } catch (\Exception $e) {
            Log::error('Error processing photo upload', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['success' => false, 'message' => $e->getMessage(),'trace' => $e->getTraceAsString()], 500);
        }
    }

    public function about()
    {

        try {
            $data = \App\InfixModuleManager::where('name', 'ApplicationCenter')->first();
            return view('applicationcenter::index', compact('data'));
        } catch (\Exception $e) {
            Log::info($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');

            return redirect()->back();
        }
    }


    public function settings()
    {
        $setting = RegistrationSetting::find(1);
        return view('applicationcenter::settings', compact('setting'));
    }

    public function create()
    {
        try {
            return view('applicationcenter::create');
        } catch (\Exception $e) {
            Log::error($e);
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function show($id)
    {
        try {
            return view('applicationcenter::show');
        } catch (\Exception $e) {
            Log::error($e);
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function edit($id)
    {
        try {
            return view('applicationcenter::edit');
        } catch (\Exception $e) {
            Log::error($e);
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    // new student registration form view method
    public function registration()
    {
        try {


            $max_admission_id = Student::max('student_number');
            $organizations = Organization::all();
            $classes = Classes::all();
            $programs = Program::where('status', 'active')->with('translations')->get();


            $genders = BaseSetup::where('base_group_id', '=', '1')->get();

            $reg_setting = RegistrationSetting::find(1);


//            return view('admission::registration',compact('organizations', 'classes', 'programs', 'genders', 'reg_setting','max_admission_id','max_roll_id'));
            return view('applicationcenter::registration', compact('organizations', 'classes', 'programs', 'genders', 'reg_setting', 'max_admission_id'));
        } catch (\Exception $e) {

            Log::error($e);
//            Toastr::error('Operation Failed', 'Failed');
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }


    public function registrationByGuardian()
    {
        try {

            $max_admission_id = Student::max('student_number');
            $max_roll_id = Student::max('roll_no');
            $organizations = Organization::all();
            $classes = Classes::all();
            $programs = Program::where('status', 'active')->get();

//            $programs = ProgramTranslation::where("locale", "en")->get();
            $genders = BaseSetup::where('base_group_id', '=', '1')->get();

            $postcodes = MalaysiaPostcode::distinct('postcode')->pluck('postcode');
            $states = MalaysiaPostcode::distinct('state')->pluck('state');


            $reg_setting = RegistrationSetting::find(1);
//            return view('admission::registration',compact('organizations', 'classes', 'programs', 'genders', 'reg_setting','max_admission_id','max_roll_id'));
            return view('applicationcenter::registration_by_guardian', compact('organizations', 'classes', 'programs', 'genders', 'reg_setting', 'max_admission_id', 'max_roll_id', 'postcodes', 'states'));
        } catch (\Exception $e) {
            Log::error($e);
            dd($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    // get academic year for parent registration for saas using ajax
    public function getClasAcademicyear(Request $request)
    {
        $classes = [];
        $academic_years = AcademicYear::where('organization_id', $request->id)->get();
        return response()->json([$classes, $academic_years]);
    }

    // Get section for new registration by ajax
    // Get section for new registration by ajax
    public function getClasses(Request $request)
    {


        $classes = Classes::where('center_id', $request->id)
            ->whereHas('programs', function($query) use ($request) {
                $query->where('program_id', $request->program_id);
            })
            ->with(['center.employee'])
            ->get();
// Extract supervisor names
        // Remove duplicates by employee_id and pluck names
        $supervisors = $classes->pluck('center')
            ->flatten()
            ->pluck('employee')
            ->flatten()
            ->unique('id') // Make sure to use 'id' or the appropriate column name for the employee identifier
            ->pluck('name');
        return response()->json(['classes' => $classes, 'supervisors' => $supervisors]);
    }


    // Get class for regular school and saas for new student registration
    public function getCenters(Request $request)
    {


        $centers = Center::whereHas("programs", function ($q) use ($request) {
            return $q->where('program_id', $request->id);
        })->get();


        return response()->json([$centers]);
        
    }

    // new student application store by a guardian
    public function studentStoreByGuardian(DependentStudentApplicationRequest $request)
    {
        Log::info('studentStoreByGuardian started', ['request_data' => $request->except(['password', '_token']), 'guardian_user_id' => Auth::guard("web")->id()]);

        $dob = Carbon::parse($request->get('date_of_birth'))->format('Y-m-d');
        Log::info('Parsed date of birth', ['dob' => $dob]);

        $document_file_1 = "";
        if ($request->file('document_file_1') != "") {
            try {
                $file = $request->file('document_file_1');
                $document_file_1 = 'doc1-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/student/document/', $document_file_1);
                $document_file_1 = 'public/uploads/student/document/' . $document_file_1;
                Log::info('Document 1 uploaded', ['path' => $document_file_1]);
            } catch (\Exception $e) {
                Log::error('Failed to upload document 1', ['error' => $e->getMessage()]);
                Toastr::error('Failed to upload document 1.', 'Error');
                return redirect()->back()->withInput();
            }
        }

        $document_file_2 = "";
        if ($request->file('document_file_2') != "") {
             try {
                $file = $request->file('document_file_2');
                $document_file_2 = 'doc2-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/student/document/', $document_file_2);
                $document_file_2 = 'public/uploads/student/document/' . $document_file_2;
                Log::info('Document 2 uploaded', ['path' => $document_file_2]);
             } catch (\Exception $e) {
                 Log::error('Failed to upload document 2', ['error' => $e->getMessage()]);
                 Toastr::error('Failed to upload document 2.', 'Error');
                 return redirect()->back()->withInput();
             }
        }

        $document_file_3 = "";
        if ($request->file('document_file_3') != "") {
            try {
                $file = $request->file('document_file_3');
                $document_file_3 = 'doc3-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/student/document/', $document_file_3);
                $document_file_3 = 'public/uploads/student/document/' . $document_file_3;
                Log::info('Document 3 uploaded', ['path' => $document_file_3]);
             } catch (\Exception $e) {
                 Log::error('Failed to upload document 3', ['error' => $e->getMessage()]);
                 Toastr::error('Failed to upload document 3.', 'Error');
                 return redirect()->back()->withInput();
             }
        }

        $document_file_4 = "";
        if ($request->file('document_file_4') != "") {
            try {
                $file = $request->file('document_file_4');
                $document_file_4 = 'doc4-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/student/document/', $document_file_4);
                $document_file_4 = 'public/uploads/student/document/' . $document_file_4;
                Log::info('Document 4 uploaded', ['path' => $document_file_4]);
            } catch (\Exception $e) {
                 Log::error('Failed to upload document 4', ['error' => $e->getMessage()]);
                 Toastr::error('Failed to upload document 4.', 'Error');
                 return redirect()->back()->withInput();
             }
        }


        // TODO: This logic for guardian photo seems unused later? Check usage.
        $guardians_photo = "";
        if ($request->file('guardians_photo') != "") {
            $guardians_photo = Session::get('guardians_photo');
             Log::info('Guardian photo path retrieved from session', ['path' => $guardians_photo]);
        }


        DB::beginTransaction();
        Log::info('Database transaction started.');
        try {

            // extracting guardian id for student
            $parentUser = Auth::guard("web")->user();
            Log::info('Retrieved parent user', ['parent_user_id' => $parentUser->id, 'parent_user_email' => $parentUser->email]);
            $parentUser->phone = $request->guardians_phone; // we save this because in the registration form we dont capture the phone and gender
            Log::info('Updating parent user phone', ['parent_user_id' => $parentUser->id, 'phone' => $request->guardians_phone]);

            $parentUser->save();
            Log::info('Parent user updated', ['parent_user_id' => $parentUser->id]);
            $parentUser->toArray();

            $userName = generateUserName($request->all());
            Log::info('Generated username for student', ['username' => $userName]);


            $user_stu = new User;
            $user_stu->full_name = ucfirst($request->fullname);
            $user_stu->full_name_trans = $request->full_name_trans;
            $user_stu->display_name = ucfirst($request->displayname);
            $user_stu->nationality = $parentUser->nationality;
            $user_stu->username = $userName;
            $user_stu->phone = empty($request->student_mobile) == true ? $parentUser->phone : $request->student_mobile;
            $user_stu->email = $request->email;
            $user_stu->access_status = '0'; // after the user is offered the program, then we will make this 1 to allow the student to login
            $user_stu->organization_id = config('organization_id');
            $user_stu->address_1 = $request->guardians_address1;
            $user_stu->address_2 = $request->guardians_address2;
            $user_stu->state = $request->state;
            $user_stu->email_verified_at = Carbon::now();
            $user_stu->gender = BaseSetup::find($request->gender)->base_setup_name;
            $user_stu->zip_code = $request->zip;
            Log::info('Creating new student user record', ['email' => $user_stu->email, 'username' => $user_stu->username]);

            $user_stu->save();
            Log::info('New student user record created', ['student_user_id' => $user_stu->id]);
            $user_stu->toArray();

            $user_stu->assignRole('student');
            Log::info('Assigned student role to user', ['student_user_id' => $user_stu->id]);


            // store students table details


            try {
                $relationship = '';
                if ($request->relation == 'F') {

                    $relationship = 'Father';
                } elseif ($request->relation == 'M') {

                    $relationship = 'Mother';
                } elseif ($request->relation == 'W') {

                    $relationship = 'Wife';
                } elseif ($request->relation == 'B') {

                    $relationship = 'Brother';
                } else {
                    $relationship = 'Other';
                }
                 Log::info('Determined relationship', ['request_relation' => $request->relation, 'relationship_text' => $relationship]);


                // check if guardian already exists
                if (Guardian::where('user_id', $parentUser->id)->orWhere('email', $parentUser->email)->exists()) {
                    $parent = Guardian::where('user_id', $parentUser->id)->orWhere('email', $parentUser->email)->first();
                    Log::info('Found existing guardian record', ['guardian_id' => $parent->id, 'parent_user_id' => $parentUser->id]);

                    // if gender is not set yet for the guardian, then do it. else dont do it if it is already there
                    if (is_null($parent->gender)) {
                        $parent->gender = BaseSetup::find($request->guardians_gender)->base_setup_name;
                        Log::info('Updating guardian gender', ['guardian_id' => $parent->id, 'gender' => $parent->gender]);
                        $parent->save();
                        $parent->toArray();
                        Log::info('Guardian gender updated', ['guardian_id' => $parent->id]);
                    }

                } else {
                    Log::info('Creating new guardian record', ['parent_user_id' => $parentUser->id, 'email' => $parentUser->email]);
                    $parent = new Guardian;
                    $parent->user_id = $parentUser->id;
                    $parent->guardians_address = $request->guardians_address;
                    $parent->name = $parentUser->display_name;
                    $parent->full_name = ucfirst($parentUser->full_name);
                    $parent->gender = $request->guardians_gender ? BaseSetup::find($request->guardians_gender)->base_setup_name : null; // Handle potential null gender
                    $parent->occupation = $request->guardians_occupation;
                    $parent->mobile = $request->guardians_phone;
                    $parent->email = $parentUser->email;
                    $parent->organization_id = config('organization_id');
                    $parent->guardians_relation = $request->relation; // Use the code 'F', 'M', etc.
                    if ($guardians_photo != "") {
                        Log::info('Assigning guardian photo from session', ['path' => $guardians_photo]);
                        $parent->guardians_photo = $guardians_photo; // This uses the path from session - ensure it's correct
                    }

                    $parent->relation = $relationship; // Use the text 'Father', 'Mother', etc.
                    $parent->save();
                    Log::info('New guardian record created', ['guardian_id' => $parent->id]);
                    $parent->toArray();
                }


                $parentUser->assignRole('parent');
                 Log::info('Assigned parent role to user', ['parent_user_id' => $parentUser->id]);

                try {
                    $student = new Student();
                    $student->guardian_id = $parent->id;
                    $student->user_id = $user_stu->id;

                    // student number format ( two digits of year+two digits of month+two digits of  hour+two digits of minutes+two digits of  seconds
                    $student->student_number = Carbon::now()->format('y') . Carbon::now()->format('n') . Carbon::now()->format('H') . Carbon::now()->format('i') . Carbon::now()->format('s');
                    $student->display_name = ucfirst($user_stu->display_name);
                    $student->full_name = ucfirst($user_stu->full_name);
                    $student->full_name_trans = $user_stu->full_name_trans;
                    $student->identity_number = strtoupper($request->national_id_number);
                    $student->nationality = $parentUser->nationality;
                    $student->guardian_relationship = $request->relation; // Use the code 'F', 'M', etc.
                    $student->gender = BaseSetup::find($request->gender)->base_setup_name;
                    $student->date_of_birth = $dob;
                    $student->email = $user_stu->email;
                    $student->mobile = empty($request->student_mobile) == true ? $request->guardians_phone : $request->student_mobile;
                    $student->organization_id = $user_stu->organization_id;
                    $student->status = 'new_admission';
                    $student->active_status = '0';
                    Log::info('Creating new student record', ['student_user_id' => $student->user_id, 'guardian_id' => $student->guardian_id, 'student_number' => $student->student_number]);

                    $dependentPhotoPath = Session::get('dependent_photo');
                    $dependentIdentifier = Session::get('dependent_identifier');
                    
                    // Check if we have a photo in session
                    if (!session()->has('dependent_photo') || empty($dependentPhotoPath)) {
                        Log::error('Dependent photo missing in session before saving student', ['student_user_id' => $user_stu->id]);
                        DB::rollback();
                        Log::warning('Transaction rolled back due to missing photo.');
                        Toastr::error('No photo found! Please upload a photo before submitting.', 'Error');
                        return back()->withErrors(['photo' => 'No photo found! Please upload a photo before submitting.'])->withInput();
                    }
                    
                    // Set the student_photo initially from the session
                    // We'll update it after saving if needed
                    Log::info('Assigning dependent photo from session to student', ['path' => $dependentPhotoPath]);
                    $student->student_photo = $dependentPhotoPath;
                    $student->document_title_1 = $request->document_title_1;
                    if ($document_file_1 != "") {
                         Log::info('Assigning document 1 path to student', ['path' => $document_file_1]);
                        $student->document_file_1 = $document_file_1;
                    }

                    $student->document_title_2 = $request->document_title_2;
                    if ($document_file_2 != "") {
                        Log::info('Assigning document 2 path to student', ['path' => $document_file_2]);
                        $student->document_file_2 = $document_file_2;
                    }

                    $student->document_title_3 = $request->document_title_3;
                    if ($document_file_3 != "") {
                        Log::info('Assigning document 3 path to student', ['path' => $document_file_3]);
                        $student->document_file_3 = $document_file_3;
                    }

                    $student->document_title_4 = $request->document_title_4;

                    if ($document_file_4 != "") {
                         Log::info('Assigning document 4 path to student', ['path' => $document_file_4]);
                        $student->document_file_4 = $document_file_4;
                    }

                    $student->save();
                    Log::info('Student record saved successfully', ['student_id' => $student->id, 'photo_path' => $student->student_photo]);
                    $student->toArray();
                    
                    // Now that we have a student ID, use the StudentImageService to move the temporary photo
                    // to the final location if we have a dependent identifier
                    if (session()->has('dependent_identifier') && !empty($dependentIdentifier)) {
                        // Get the StudentImageService instance
                        $studentImageService = app(StudentImageService::class);
                        
                        Log::info('Moving dependent temporary image', [
                            'guardian_id' => $parentUser->id,
                            'dependent_identifier' => $dependentIdentifier,
                            'student_id' => $student->id
                        ]);
                        
                        try {
                            // Move the temporary image to the student's directory
                            $studentImageService->moveDependentTemporaryImages(
                                $parentUser->id, 
                                $dependentIdentifier, 
                                $student->id
                            );
                            
                            // Reload the student to get the updated photo path
                            $student = Student::findOrFail($student->id);
                            
                            Log::info('Dependent photo moved to student directory', [
                                'student_id' => $student->id,
                                'photo_path' => $student->student_photo
                            ]);
                        } catch (\Exception $e) {
                            Log::error('Error moving temporary images', [
                                'error' => $e->getMessage(),
                                'student_id' => $student->id,
                                'guardian_id' => $parentUser->id,
                                'dependent_identifier' => $dependentIdentifier
                            ]);
                            // Continue without failing - we already have the photo path set
                        }
                    }
                    
                    // Remove 'public/' from the beginning of the path when generating the URL
                    $adjustedPhotoPath = preg_replace('/^public\//', '', $student->student_photo);
                    $studentPhotoUrl = $student->student_photo ? asset($adjustedPhotoPath) : asset('avatar.jpg');
                    Log::info('Generated student photo URL', ['original_path' => $student->student_photo, 'adjusted_path' => $adjustedPhotoPath, 'url' => $studentPhotoUrl]);

                    try {
                        $admission = new Admission();
                        $admission->program_id = $request->program;
                        $admission->center_id = $request->center;
                        $admission->class_id = $request->classes;
                        $admission->gender_id = $request->gender;
                        $admission->creator_role = 'guardian';
                        $admission->created_by = Auth::user()->id;
                        $admission->status = 'new_admission';
                        $admission->student_id = $student->id;
                        $admission->guardian_email = $parentUser->email;
                        $admission->student_email = $user_stu->email;
                        $admission->guardian_id = $parent->id;
                        $admission->guardian_name = ucfirst($parentUser->full_name);
                        $admission->student_mobile = $request->student_mobile;
                        $admission->date_of_birth = $dob;
                        $admission->age = $request->age;
                        $admission->organization_id = config('organization_id');
                        Log::info('Creating new admission record', ['student_id' => $student->id, 'program_id' => $request->program, 'center_id' => $request->center, 'class_id' => $request->classes]);
                        $admission->save();
                         Log::info('Admission record created successfully', ['admission_id' => $admission->id]);


                        $admission->programs()->sync([$request->program]);
                        Log::info('Synced admission program', ['admission_id' => $admission->id, 'program_id' => $request->program]);


                        try {
                            $user_info = [];


                            $user_info[] = array('email' => $student->email, 'id' => $student->id, 'slug' => 'student');
                            Log::info('Prepared user info for jobs', ['user_info' => $user_info]);


                            DB::commit();
                            Log::info('Database transaction committed successfully.');

                            // session null
                            Session::forget('dependent_photo');
                            Log::info('Cleared dependent_photo from session.');
                            Session::forget('dependent_identifier');
                            Log::info('Cleared dependent_identifier from session.');
                            Session::forget('program_id');
                            Log::info('Cleared program_id from session.');
                            Session::forget('center_id');
                            Log::info('Cleared center_id from session.');
                            Session::forget('class_id');
                            Log::info('Cleared class_id from session.');


                            try {

                                $user_info[0]["applicationConfirmationTime"] = $admission->updated_at->format('m/d/Y g:i A');
                                $user_info[0]["student_id"] = $admission->student_id;
                                $user_info[0]['studentName'] = $student->full_name;
                                $user_info[0]['studentNationality'] = $student->nationality;
                                $user_info[0]['programName'] = Program::find($request->program)->title;
                                $user_info[0]['CenterName'] = Center::find($request->center)->name;
                                $user_info[0]['ClassName'] = Classes::find($request->classes)->name;
                                $user_info[0]['username'] = $user_stu->username;
                                $user_info[0]['studentId'] = $student->id;
                                $user_info[0]['studentEmail'] = $student->email;
                                $user_info[0]['refNo'] = $admission->id;
                                Log::info('Prepared detailed user info for emails/PDF', ['user_info_detailed' => $user_info[0]]);

                                if (count($user_info) != 0) {
                                    $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);
                                    $systemEmail = EmailSetting::find(1);

                                    $system_email = $systemEmail->from_email;
                                    $organization_name = $systemSetting->organization_name;

                                    $sender['system_email'] = $system_email;
                                    $sender['organization_name'] = $organization_name;
                                     Log::info('Prepared sender info', ['sender' => $sender]);

                                    $data = [];
                                    $data['programName'] = $user_info[0]['programName'];
                                    $data['CenterName'] = $user_info[0]['CenterName'];
                                    $data['ClassName'] = $user_info[0]['ClassName'];
                                    $data['studentName'] = $user_info[0]['studentName'];
                                    $data['refNo'] = $user_info[0]['refNo'];
                                     Log::info('Prepared data for PDF generation', ['pdf_data' => $data]);

                                    // storing the user application letter confirmation in the storage
                                    $pdf = \App::make('dompdf.wrapper');
                                    $pdf->loadHTML(view('modules.site.templates.wajeha.backEnd.studentInformation.student_application_confirmation', compact('data'))->render());
                                    $content = $pdf->download()->getOriginalContent();
                                    $pdfPath = "applicationLetters/" . $admission->student_id . "-applicationLetter.pdf";
                                    \Storage::put($pdfPath, $content);
                                    Log::info('Generated and stored application confirmation PDF', ['path' => $pdfPath, 'student_id' => $admission->student_id]);

                                    // Dispatch confirmation email job
                                    Log::info('Dispatching SendApplicationConfirmationEmailJob', ['user_info' => $user_info, 'sender' => $sender]);
                                    dispatch(new \App\Jobs\SendApplicationConfirmationEmailJob($user_info, $sender));


                                    $supervisorEmails = Employee::whereIn('id', CenterEmployee::where('cen_id', $request->center)->pluck('emp_id')->toArray())->pluck('email');
                                    Log::info('Dispatching SendNewApplicationNotificationToSupervisor job', ['user_info' => $user_info, 'sender' => $sender, 'supervisor_emails' => $supervisorEmails]);
                                    dispatch(new \App\Jobs\SendNewApplicationNotificationToSupervisor($user_info, $sender, $supervisorEmails));

                                }


                            } catch (\Exception $e) {
                                Log::error('Error during post-commit operations (PDF/Email dispatch)', [
                                    'error' => $e->getMessage(),
                                    'trace' => $e->getTraceAsString(),
                                    'admission_id' => $admission->id ?? null,
                                    'student_id' => $student->id ?? null
                                ]);
                                // Even if jobs fail, the main operation succeeded. Show success but maybe add a warning?
                                Toastr::warning('Operation successful, but failed to generate confirmation PDF or send emails.', 'Success with Issues');
                                Log::info('studentStoreByGuardian completed successfully, but post-commit operations failed. Redirecting to application list.');
                                return redirect(route("application.list"));
                            }
                             Log::info('studentStoreByGuardian completed successfully. Redirecting to application list.');
                            Toastr::success('Operation successful', 'Success');
                            return redirect(route("application.list"));
                        } catch (\Exception $e) {
                            Log::error('Error creating admission record or syncing program', [
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString(),
                                'student_id' => $student->id ?? null
                            ]);
                            DB::rollback();
                            Log::warning('Transaction rolled back due to admission creation failure.');
                            Toastr::error('Operation Failed - Admission Creation', 'Failed');
                            return redirect()->back()->withInput();
                        }
                    } catch (\Exception $e) {
                         Log::error('Error saving student record', [
                             'error' => $e->getMessage(),
                             'trace' => $e->getTraceAsString(),
                             'student_user_id' => $user_stu->id ?? null,
                             'guardian_id' => $parent->id ?? null
                         ]);
                        DB::rollback();
                        Log::warning('Transaction rolled back due to student save failure.');
                        Toastr::error('Operation Failed - Student Save', 'Failed');
                        return redirect()->back()->withInput();
                    }
                } catch (\Exception $e) {
                     Log::error('Error creating/updating guardian record or assigning parent role', [
                         'error' => $e->getMessage(),
                         'trace' => $e->getTraceAsString(),
                         'parent_user_id' => $parentUser->id ?? null
                     ]);
                    DB::rollback();
                    Log::warning('Transaction rolled back due to guardian creation/update failure.');
                    // Toastr::error('Operation Failed - Guardian Info', 'Failed');
                    Toastr::error($e->getMessage(), 'Failed');

                    return redirect()->back()->withInput();
                }
            } catch (\Exception $e) {
                Log::error('Error creating student user record or assigning role', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'parent_user_id' => $parentUser->id ?? null
                ]);
                DB::rollback();
                 Log::warning('Transaction rolled back due to student user creation failure.');
                Toastr::error('Operation Failed - Student User Creation', 'Failed');
                return redirect()->back()->withInput();
            }
        } catch (\Exception $e) {
             Log::critical('Critical error in studentStoreByGuardian before or during transaction', [
                 'error' => $e->getMessage(),
                 'trace' => $e->getTraceAsString(),
                 'request_data' => $request->except(['password', '_token'])
             ]);
            DB::rollback(); // Ensure rollback if transaction started
            Log::warning('Transaction rolled back due to critical error.');


            Toastr::error('Operation Failed - Critical Error', 'Failed');
            return redirect()->back()->withInput();
        }
    }

    // new student application store directly applied by a student
    public function studentStore(StudentApplicationRequest $request)
    {
        Log::info('Starting student registration process', [
            'email' => Auth::guard("web")->user()->email ?? 'not_logged_in',
            'session_has_photo' => session()->has('student_photo'),
            'photo_in_session' => session()->get('student_photo')
        ]);

        // Validate that a photo exists either in session or in the student record
        if (!session()->has('student_photo') && Auth::guard("web")->check()) {
            $user = Auth::guard("web")->user();
            if ($user->student && empty($user->student->student_photo)) {
                Log::warning('Photo required but missing', [
                    'user_id' => $user->id,
                    'student_id' => $user->student->id ?? null,
                    'session_keys' => array_keys(session()->all())
                ]);
                
                return redirect()->back()->withErrors([
                    'photo' => 'Student photo is required. Please upload an image.'
                ])->withInput();
            }
        }

        $document_file_1 = "";
        if ($request->file('document_file_1') != "") {
            $file = $request->file('document_file_1');
            $document_file_1 = 'doc1-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            $file->move('public/uploads/student/document/', $document_file_1);
            $document_file_1 = 'public/uploads/student/document/' . $document_file_1;
        }

        $document_file_2 = "";
        if ($request->file('document_file_2') != "") {
            $file = $request->file('document_file_2');
            $document_file_2 = 'doc2-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            $file->move('public/uploads/student/document/', $document_file_2);
            $document_file_2 = 'public/uploads/student/document/' . $document_file_2;
        }

        $document_file_3 = "";
        if ($request->file('document_file_3') != "") {
            $file = $request->file('document_file_3');
            $document_file_3 = 'doc3-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            $file->move('public/uploads/student/document/', $document_file_3);
            $document_file_3 = 'public/uploads/student/document/' . $document_file_3;
        }

        $document_file_4 = "";
        if ($request->file('document_file_4') != "") {
            $file = $request->file('document_file_4');
            $document_file_4 = 'doc4-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            $file->move('public/uploads/student/document/', $document_file_4);
            $document_file_4 = 'public/uploads/student/document/' . $document_file_4;
        }

        DB::beginTransaction();
        try {
            $user_stu = Auth::guard("web")->user();

            if ($request->has("gender")) {
                $user_stu->gender = BaseSetup::find($request->gender)->base_setup_name;
                $user_stu->save();
            }

            try {
                // if not applied so far, then proceed with this condition
                if (is_null($user_stu->student)) {
                    if ($request->filled('date_of_birth')) {
                        $dob = Carbon::parse($request->get('date_of_birth'))->format('Y-m-d');
                    }

                    $student = new Student();
                    $student->user_id = $user_stu->id;
                    // student number format ( two digits of year+two digits of month+two digits of  hour+two digits of minutes+two digits of  seconds
                    $student->student_number = Carbon::now()->format('y') . Carbon::now()->format('n') . Carbon::now()->format('H') . Carbon::now()->format('i') . Carbon::now()->format('s');
                    $student->display_name = $user_stu->display_name;
                    $student->full_name = $user_stu->full_name;
                    $student->full_name_trans = $user_stu->full_name_trans;
                    $student->identity_number = strtoupper($request->national_id_number);
                    $student->nationality = $user_stu->nationality;
                    $student->gender = BaseSetup::find($request->gender)->base_setup_name;
                    $student->date_of_birth = $dob;
                    $student->email = $user_stu->email;
                    $student->mobile = $request->student_mobile;
                    $student->organization_id = $user_stu->organization_id;
                    $student->status = 'new_admission';
                    $student->active_status = '0';
                }
                else {
                    $student = $user_stu->student;
                    $dob = $student->date_of_birth;
                }
                
                $user_stu->assignRole('student');

                // Critical check: Ensure a photo exists before proceeding
                if (!Session::has('student_photo') && empty($student->student_photo) ) {
                    Log::warning('Attempting to create student record without photo', [
                        'student_number' => $student->student_number ?? 'new',
                        'user_id' => $user_stu->id
                    ]);
                    
                    DB::rollback();
                    return redirect()->back()->withErrors([
                        'photo' => 'Student photo is required. Please upload an image.'
                    ])->withInput();
                }

                if (Session::has('student_photo')) {
                    $student->student_photo = Session::get('student_photo');
                    Log::info('Applying photo from session to student record', [
                        'photo_path' => Session::get('student_photo'),
                        'student_number' => $student->student_number ?? 'new'
                    ]);
                }

                $student->document_title_1 = $request->document_title_1;
                if ($document_file_1 != "") {
                    $student->document_file_1 = $document_file_1;
                }

                $student->document_title_2 = $request->document_title_2;
                if ($document_file_2 != "") {
                    $student->document_file_2 = $document_file_2;
                }

                $student->document_title_3 = $request->document_title_3;
                if ($document_file_3 != "") {
                    $student->document_file_3 = $document_file_3;
                }

                $student->document_title_4 = $request->document_title_4;
                if ($document_file_4 != "") {
                    $student->document_file_4 = $document_file_4;
                }
                
                $student->save();
                
                // Check if the student record was saved with a photo
                if (empty($student->student_photo) ) {
                    Log::error('Student record saved without photo!', [
                        'student_id' => $student->id,
                        'session_photo' => Session::get('student_photo')
                    ]);
                    
                    throw new \Exception('Photo upload failed. Please try again.');
                }
                
                Log::info('Student record saved successfully with photo', [
                    'student_id' => $student->id,
                    'photo_path' => $student->student_photo 
                ]);
                
                // Remove 'public/' prefix from photo path when generating URL
                $adjustedPath = preg_replace('/^public\//', '', $student->student_photo);
                $studentPhotoUrl = $student->student_photo ? asset($adjustedPath) : asset('avatar.jpg');
                Log::info('Generated student photo URL', [
                    'original_path' => $student->student_photo,
                    'adjusted_path' => $adjustedPath,
                    'url' => $studentPhotoUrl
                ]);
                
                try {
                    // Prepare the data for the Admission
                    $admissionData = [
                        'program_id' => $request->program,
                        'center_id' => $request->center,
                        'class_id' => $request->classes,
                        'gender_id' => $request->gender,
                        'creator_role' => Auth::user()->roles->pluck('name')->toArray()[0],
                        'created_by' => Auth::user()->id,
                        'status' => 'new_admission',
                        'student_email' => $user_stu->email,
                        'student_mobile' => $request->student_mobile,
                        'date_of_birth' => $dob,
                        'age' => $request->age,
                        'organization_id' => config('organization_id'),
                        'student_id' => $student->id, // Include student_id for new admission
                    ];

                    // Create a new Admission record
                    $admission = Admission::create($admissionData);
                    
                    // Sync the programs - this can be done after the record is created or updated
                    $admission->programs()->sync([$request->program]);


                    try {
                        $user_info = [];

//                        if ($request->student_email != "") {
                        $user_info[] = array('email' => $user_stu->email, 'id' => $student->id, 'slug' => 'student');
//                        }

                        DB::commit();
                        Session::put('student_photo', '');
                        session()->remove('program_id');
                        session()->remove('center_id');
                        session()->remove('class_id');


                        try {

                            $user_info[0]["applicationConfirmationTime"] = $admission->updated_at->format('m/d/Y g:i A');
                            $user_info[0]["student_id"] = $admission->student_id;
                            $user_info[0]['studentName'] = $student->full_name;
                            $user_info[0]['studentNationality'] = $student->nationality;
                            $user_info[0]['programName'] = Program::find($request->program)->title;
                            $user_info[0]['CenterName'] = Center::find($request->center)->name;
                            $user_info[0]['ClassName'] = Classes::find($request->classes)->name;
                            $user_info[0]['username'] = $user_stu->username;
                            $user_info[0]['studentId'] = $student->id;
                            $user_info[0]['studentEmail'] = $student->email;
                            $user_info[0]['refNo'] = $admission->id;
                            $user_info[0]['studentPhotoUrl'] = $studentPhotoUrl;
                            if (count($user_info) != 0) {
                                $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);

                                $systemEmail = EmailSetting::find(1);

                                $system_email = $systemEmail->from_email;
                                $organization_name = $systemSetting->organization_name;
                                $sender['system_email'] = $system_email;
                                $sender['organization_name'] = $organization_name;

                                $data = [];
                                $data['programName'] = Program::find($request->program)->title;
                                $data['CenterName'] = Center::find($request->center)->name;
                                $data['ClassName'] = Classes::find($request->classes)->name;
                                $data['applicationDate'] = $admission->created_at;


                                $data['refNo'] = $admission->id;

                                // storing the user application letter confirmation in the storage
                                $pdf = \App::make('dompdf.wrapper');
                                $pdf->loadHTML(view('modules.site.templates.wajeha.backEnd.studentInformation.student_application_confirmation', compact('data'))->render());
                                $content = $pdf->download()->getOriginalContent();
                                \Storage::put("applicationLetters/" . $admission->student_id . "-applicationLetter.pdf", $content);



                                    dispatch(new \App\Jobs\SendApplicationConfirmationEmailJob($user_info[0], $sender));

                                $admission->update(['application_recieved_creation_email' => view('modules.site.templates.wajeha.backEnd.studentInformation.student_application_confirmation_email', ['data'=> $user_info[0]])->render(),'application_recieved_creation_email_sent_at' => Carbon::now()]);




                                $supervisorEmail = Employee::whereIn('id', CenterEmployee::where('cen_id', $request->center)->pluck('emp_id')->toArray())->pluck('email','full_name');



                                $successnNotificationToSupervisor = retry(5, function () use ($user_info, $sender,$supervisorEmail) {
                                    // send the notification to the related center supervisor

                                    dispatch(new \App\Jobs\SendNewApplicationNotificationToSupervisor($user_info[0], $sender, $supervisorEmail));

                                    return true;
                                }, 100);

                                if (!$successnNotificationToSupervisor) {
                                    Log::error('Failed to send APPLICATION-RECEIVED Supervisor email after 5 attempts');
                                    return back()->withErrors(['error' => 'Your account has been created successfully, but we were unable to send the APPLICATION-RECEIVED notification email to the supervisor.'])->withInput();
                                }


                                $admission->update(
                                    [
                                        'supervisor_notification_email' => view('modules.site.templates.wajeha.backEnd.studentInformation.new_student_application_supervisor', ['data'=> $user_info[0]])->render(),
                                        'supervisor_notification_sent_at' => Carbon::now()]);





                            }
                        } catch (\Exception $e) {

                            Log::alert($e->getMessage());
                            // Set an error message for the user
                            session()->flash('email_error', 'Your admission has been created successfully, but we were unable to send you a confirmation email. Please check your account for details or contact support.');

                            Toastr::warning('Operation Failed', 'Failed');
                            return back()->withErrors(['error' => $e->getMessage()])->withInput();
                        }

                        Toastr::success('Operation successful', 'Success');
                        return redirect(route("application.list"));
                    } catch (\Exception $e) {
                        DB::rollback();
                        Log::alert($e->getMessage());
                        Toastr::error('Operation Failed', 'Failed');
                        return back()->withErrors(['error' => $e->getMessage()])->withInput();
                    }
                } catch (\Exception $e) {
                    DB::rollback();
                    Log::alert($e->getMessage());

                    Toastr::error('Operation Failed', 'Failed');
                    return back()->withErrors(['error' => $e->getMessage()])->withInput();
                }
            } catch (\Exception $e) {
                DB::rollback();
                Log::alert($e->getMessage());

                Toastr::error('Operation Failed', 'Failed');
                return back()->withErrors(['error' => $e->getMessage()])->withInput();
            }
        } catch (\Exception $e) {
            DB::rollback();
            Log::alert($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return back()->withErrors(['error' => $e->getMessage()])->withInput();
        }
    }
    public function updateInlineContact(Request $request)
    {
        // Basic validation
        $request->validate([
            'student_id' => 'required|integer',
            'field_name' => 'required|string|in:mobile,email', // restrict to only mobile or email
            'field_value' => 'required|string|max:255',
        ]);

        try {
            // Retrieve the student's record
            $student = Student::findOrFail($request->student_id);

            // Additional security: ensure the authenticated user
            // matches the student's user_id, or user has permission, etc.
            // For example:
            // if (Auth::id() !== $student->user_id) {
            //     return response()->json([
            //         'success' => false,
            //         'message' => 'Unauthorized'
            //     ], 403);
            // }

            // Update only the requested field
            $fieldName = $request->field_name;
            $student->$fieldName = $request->field_value;
            $student->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Field updated successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update student contact info: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong!'
            ], 500);
        }
    }

    // Show student list for new registration
    public function studentList()
    {


        $academic_years = AcademicYear::get();
        return view('applicationcenter::student_list', compact('academic_years', 'admissions'));
    }

    public function applicationList()
    {
        $user = Auth::guard('web')->user();
        
        try {
            Log::info('Checking application list access for user', [
                'user_id' => $user->id,
                'has_parent' => !is_null($user->parent),
                'has_student' => !is_null($user->student),
                'roles' => $user->roles->pluck('name')
            ]);

            // Check if user has required roles
            if (!$user->parent && !$user->student) {
                Log::warning('Unauthorized access attempt to application list', [
                    'user_id' => $user->id,
                    'email' => $user->email
                ]);
                
                Toastr::warning('You must be registered as a student or have dependent students to view applications.', 'Access Denied');
                return redirect()->route('student.guardian.application.form');
            }

            // Initialize query builder
            $query = Admission::query()
                ->with(['programs.programTranslations', 'center', 'student', 'class']);

            // Build query based on user roles
            if ($user->parent && $user->student) {
                // User is both a parent and a student
                $query->where(function($q) use ($user) {
                    $q->where('guardian_id', $user->parent->id)
                      ->orWhere('student_id', $user->student->id);
                });
                
                Log::info('Retrieved applications for dual-role user', [
                    'guardian_id' => $user->parent->id,
                    'student_id' => $user->student->id
                ]);
            } 
            elseif ($user->parent) {
                // User is only a parent
                $query->where('guardian_id', $user->parent->id);
                
                Log::info('Retrieved parent-only applications', [
                    'guardian_id' => $user->parent->id
                ]);
            }
            elseif ($user->student) {
                // User is only a student
                $query->where('student_id', $user->student->id);
                
                Log::info('Retrieved student-only applications', [
                    'student_id' => $user->student->id
                ]);
            }

            // Execute the query
            $admissions = $query->get();
            
            Log::info('Total applications retrieved', [
                'count' => $admissions->count()
            ]);
            
            // Handle profile photo path
            $student = $user->student;
            $gender = $student ? strtolower($student->gender) : null;

            // Set default profile path based on gender
            $defaultProfilePath = match ($gender) {
                'female' => 'public/backEnd/img/student/female_student_default_profile_picture.png',
                'male' => 'public/backEnd/img/student/male_student_default_profile_picture.png',
                default => 'public/backEnd/img/student/male_student_default_profile_picture.gif',
            };

            $profilePath = $student && !empty($student->student_photo) 
                ? $student->student_photo 
                : $defaultProfilePath;

            return view('applicationcenter::application_list', compact('admissions', 'profilePath'));

        } catch (\Exception $e) {
            Log::error('Error fetching applications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $user->id
            ]);
            
            Toastr::error('Unable to fetch applications', 'Error');
            return redirect()->back();
        }
    }


    // Show student list for new registration
    public function studentListSearch(Request $request)
    {


//        $students = Admission::query();
        $students = Admission::where("guardian_id", Auth::user()->id)->with("programs.programTranslations")->with("center")->with("student")->with("class")->get();


//        if ($request->academic_year != "") {
//            $students->where('academic_year', $request->academic_year);
//        }
//
//        if ($request->class != "") {
//            $students->where('class_id', $request->class);
//        }
//        if ($request->section != "") {
//            $students->where('section_id', $request->section);
//        }
//        $students = $students->orderBy('id', 'desc');
//        $students = $students->get();


        return view('applicationcenter::student_list', compact('students', 'academic_years'));
    }


    public function downloadOfferLetter(Request $request)
    {


        $admissionisOffered = Admission::whereIn("status", ["offered", "accepted", "active"])->where("id", $request->admissionId)->exists();


        if ($admissionisOffered) {


            $admission = Admission::whereIn("status", ["offered", "accepted", "active"])->where("id", $request->admissionId)->first();
            $offerLetterRawContent = Program::find($admission->program_id)->offer;


            $offerLetterRawContent = str_replace("[date]", Carbon::now()->toDate()->format("Y-m-d"), $offerLetterRawContent);
            $offerLetterRawContent = str_replace("[address]", 'Address should be here', $offerLetterRawContent);
            $offerLetterRawContent = str_replace("[studentName]", Student::find($admission->student_id)->full_name, $offerLetterRawContent);
            $offerLetterRawContent = str_replace("[programName]", "<i>" . Program::find($admission->program_id)->title . "</i>", $offerLetterRawContent);
            $offerLetterRawContent = str_replace("[registrationFees]", '<strong>100</strong>', $offerLetterRawContent);
            $offerLetterRawContent = str_replace("[monthlyFeesAmount]", '<strong>200</strong>', $offerLetterRawContent);


            $options = new Options();
            $options->set('isRemoteEnabled', TRUE);


            $pdf = app()->make('dompdf.wrapper');
//            $pdf->getDomPDF()->setHttpContext($contxt);
            $pdf->getDomPDF()->setHttpContext(
                stream_context_create([
                    'ssl' => [
                        'allow_self_signed' => TRUE,
                        'verify_peer' => FALSE,
                        'verify_peer_name' => TRUE,
                    ]
                ])
            );
            $pdf->loadHTML(htmlspecialchars_decode($offerLetterRawContent));
//            return $pdf->stream();
            return $pdf->download('offerLetter.pdf');


//        $file_path = public_path('offerLetter');
//        $headers = array('Content-Type'=> 'application/pdf');
//        return \Response::download($file_path, 'file.pdf', $headers);
        }


    }


    // Show student list for new registration
    public function saasStudentList()
    {

        $institutions = School::all();


        return view('applicationcenter::saas_student_list', compact('institutions'));
    }


    // Show student list for new registration
    public function saasStudentListsearch(Request $request)
    {


        $students = StudentRegistration::query();

        if ($request->institution != "") {
            $students->where('organization_id', $request->institution);
        }

        if ($request->academic_year != "") {
            $students->where('academic_year', $request->academic_year);
        }

        if ($request->class != "") {
            $students->where('class_id', $request->class);
        }
        if ($request->section != "") {
            $students->where('section_id', $request->section);
        }
        $students = $students->orderBy('id', 'desc');
        $students = $students->get();

        $institutions = School::all();
        $institution_id = $request->institution;
        return view('applicationcenter::saas_student_list', compact('students', 'institution_id', 'institutions'));
    }


    // Approve method for new student regis., after successfully then the student will delete from tempo. stduent table
    public function studentApprove(Request $request)
    {
        Log::info('Starting student approval process', [
            'temp_id' => $request->id
        ]);

        DB::beginTransaction();
        try {
            $temp_id = $request->id;
            $studentRegistration = StudentRegistration::find($request->id);
            
            if (!$studentRegistration) {
                Log::error('Student registration record not found', ['id' => $temp_id]);
                Toastr::error('Student registration record not found', 'Failed');
                return redirect()->back();
            }
            
            // Check if the student has a photo attached to their registration
            if (empty($studentRegistration->student_photo)) {
                Log::warning('Attempting to approve student without photo', [
                    'registration_id' => $temp_id,
                    'student_email' => $studentRegistration->student_email
                ]);
                
                Toastr::error('Student photo is required before approval', 'Failed');
                return redirect()->back()->with('photo_required', 'Student photo is required before approval');
            }

            $student_table_detail = Student::where('organization_id', $studentRegistration->organization_id)->max('student_number');
            $student_table_detail_roll = Student::where('class_id', $studentRegistration->class_id)
                ->where('section_id', $studentRegistration->section_id)
                ->where('organization_id', $studentRegistration->organization_id)
                ->max('roll_no');

            if ($student_table_detail == 0) {
                $admission_no = 1;
            } else {
                $admission_no = $student_table_detail + 1;
            }

            if ($student_table_detail_roll == 0) {
                $roll_no = 1;
            } else {
                $roll_no = $student_table_detail_roll + 1;
            }

            $created_year = $studentRegistration->academicYear->year . '-01-01 12:00:00';

            // student user
            $user_stu = new User();
            $user_stu->role_id = 23;
            $user_stu->full_name = $studentRegistration->first_name . ' ' . $studentRegistration->last_name;
            $user_stu->username = $admission_no;
            $user_stu->email = $studentRegistration->student_email;
            $user_stu->created_at = $created_year;
            $user_stu->organization_id = $studentRegistration->organization_id;
            $user_stu->password = Hash::make(123456);
            $user_stu->save();

            // parent user
            $user_parent = new User();
            $user_parent->role_id = 24;

            if (empty($studentRegistration->guardian_email)) {
                $user_parent->username = 'par' . '-' . $studentRegistration->organization_id . '-' . $admission_no;
            } else {
                $user_parent->username = $studentRegistration->guardian_email;
            }

            $user_parent->email = $studentRegistration->guardian_email;
            $user_parent->password = Hash::make(123456);
            $user_parent->created_at = $created_year;
            $user_parent->organization_id = $studentRegistration->organization_id;
            $user_parent->save();

            $parent = new Guardian();
            $parent->user_id = $user_parent->id;
            $parent->guardians_name = $studentRegistration->guardian_name;
            $parent->guardians_mobile = $studentRegistration->guardian_mobile;
            $parent->guardians_email = $studentRegistration->guardian_email;
            $parent->relation = $studentRegistration->guardian_relation;

            if ($studentRegistration->guardian_relation == 'F') {
                $parent->guardians_relation = 'Father';
            } elseif ($studentRegistration->guardian_relation == 'M') {
                $parent->guardians_relation = 'Mother';
            } else {
                $parent->guardians_relation = 'Other';
            }

            $parent->created_at = $created_year;
            $parent->organization_id = $studentRegistration->organization_id;
            $parent->save();

            $student = new Student();
            $student->class_id = $studentRegistration->class_id;
            $student->section_id = $studentRegistration->section_id;
            $student->user_id = $user_stu->id;
            $student->guardian_id = $parent->id;
            $student->role_id = 23;
            $student->student_number = $admission_no;
            $student->roll_no = $roll_no;
            $student->first_name = $studentRegistration->first_name;
            $student->last_name = $studentRegistration->last_name;
            $student->full_name = $studentRegistration->first_name . ' ' . $studentRegistration->last_name;
            $student->gender_id = $studentRegistration->gender_id;
            $student->date_of_birth = $studentRegistration->date_of_birth;
            $student->email = $studentRegistration->student_email;
            $student->mobile = $studentRegistration->student_mobile;
            $student->created_at = $created_year;
            $student->organization_id = $studentRegistration->organization_id;
            $student->session_id = $studentRegistration->academic_year;
            
            // Copy the photo from registration to student
            if (!empty($studentRegistration->student_photo)) {
                $student->student_photo = $studentRegistration->student_photo;
                Log::info('Copied photo from registration to student', [
                    'photo_path' => $studentRegistration->student_photo
                ]);
            } else if (!empty($studentRegistration->image)) {
                $student->student_photo = $studentRegistration->image;
                Log::info('Copied image from registration to student', [
                    'photo_path' => $studentRegistration->image
                ]);
            } else {
                // This shouldn't happen due to the earlier check, but double-check
                Log::error('No photo found for student during approval', [
                    'registration_id' => $temp_id
                ]);
                
                DB::rollback();
                Toastr::error('Student photo is required before approval', 'Failed');
                return redirect()->back();
            }
            
            $student->save();
            Log::info('Student record created successfully with photo', [
                'student_id' => $student->id,
                'photo_path' => $student->student_photo
            ]);

            // Delete the temporary registration record
            StudentRegistration::where('id', $temp_id)->delete();

            DB::commit();

            $setting = RegistrationSetting::find(1);

            // checking enable or disable
            if ($setting->approve_after_mail == 1) {
                $user_info = [];

                if ($studentRegistration->student_email != "") {
                    $user_info[] = array('email' => $studentRegistration->student_email, 'id' => $student->id, 'slug' => 'student');
                }

                if ($studentRegistration->guardian_email != "") {
                    $user_info[] = array('email' => $studentRegistration->guardian_email, 'id' => $parent->id, 'slug' => 'parent');
                }

                try {
                    foreach ($user_info as $data) {
                        Mail::send('applicationcenter::approve_email', compact('data'), function ($message) use ($data) {
                            $settings = EmailSetting::find(1);
                            $email = $settings->from_email;
                            $organizationName = $settings->from_name;

                            $message->to($data['email'], $organizationName)->subject('Login Credentials');
                            $message->from($email, $organizationName);
                        });
                    }

                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                } catch (\Exception $e) {
                    Log::error('Failed to send approval emails', [
                        'error' => $e->getMessage()
                    ]);
                    Toastr::success('Operation successful, but email notification failed', 'Success');
                    return redirect()->back();
                }
            }

            Toastr::success('Operation successful', 'Success');
            return redirect()->back();
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Student approval failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            Toastr::error('Operation Failed: ' . $e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }

    // Temporary stduent delete
    public function studentDelete(Request $request)
    {

        try {

            StudentRegistration::destroy($request->id);

            Toastr::success('Operation successful', 'Success');
            return redirect()->back();
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    // unique stduent email check by ajax from all school
    public function checkStudentEmail(Request $request)
    {

        $student = User::where('email', $request->id)->first();


        $StudentRegistration = StudentRegistration::where(function ($q) use ($request) {
            $q->where('student_email', $request->id)->orWhere('guardian_email', $request->id);
        })->first();

        if ($student != "" || $StudentRegistration != "") {
            return response()->json(1);
        } else {
            return response()->json(0);
        }
    }

    public function checkDependentStudentEmail(Request $request)
    {


//        $email =   \DB::select("SELECT email FROM (
//                    SELECT distinct email FROM users WHERE email IN ( SELECT email FROM users WHERE email != ?)) AS a WHERE a.email = ?",[\Auth::guard('web')->user()->email,$request->id]);

        $email = \DB::select("SELECT email
                                    FROM (
                                    SELECT  email
                                    FROM students
                                    WHERE email not IN (
                                    SELECT email
                                    FROM students
                                    WHERE 
                                    (email = ? OR email  IN (
                                    SELECT email
                                    FROM students
                                    WHERE guardian_id = (
                                    SELECT id
                                    FROM guardians
                                    WHERE email = ?))))) AS a
                                    WHERE a.email = ?", [\Auth::guard('web')->user()->email, \Auth::guard('web')->user()->email, $request->id]);


        if (count($email) > 0) { /* if another user with the dependant email already exists*/
            return response()->json(1);
        } else {
            return response()->json(0);
        }
    }

    // unique stduent mobile check by ajax from all school
    public function checkStudentMobile(Request $request)
    {


        // if a student has similar number whose parent is different than the current authenticated one, then dont allow , else allow( this to be the sibling )
//        $student = Student::where('mobile', $request->id)->where("guardian_id", Auth::guard("web")->user()->parent->id)->exists();


//        if ($student) {
        return response()->json(0);
//        } else {
//            return response()->json(1);
//        }
    }


    public function checkStudentAge(Request $request)
    {
//        dd());

        $dateOfBirth = $request->get('date');
        $years = \Carbon\Carbon::parse($dateOfBirth)->age;

        if ($years < 3) {
            return response()->json(1);
        } else {
            return response()->json(0);
        }
    }


    // unique student national id no
    public function checkStudentNationalId(Request $request)
    {

        $nationalIdNo = $request->get('nationalIdNo');
        $nationalIdNoExists = Student::where('identity_number', $nationalIdNo)->exists();


        // if national id exists
        if ($nationalIdNoExists == true) {
            return response()->json(1);
        } else {
            return response()->json(0);
        }
    }


    // unique guardian email check by ajax from all school
    public function checkGuardianEmail(Request $request)
    {
        $student = User::where('email', $request->id)->where('organization_id', $request->organization_id)->first();
        $StudentRegistration = StudentRegistration::where('organization_id', $request->organization_id)->where(function ($q) use ($request) {
            $q->where('student_email', $request->id)->orWhere('guardian_email', $request->id);
        })->first();

        if ($student != "" || $Smsgistration != "") {
            return response()->json(1);
        } else {
            return response()->json(0);
        }
    }


    // unique guardian mobile check by ajax from all school
    public function checkGuardianMobile(Request $request)
    {
//        $student = Guardian::where('guardians_mobile', $request->id)->where('organization_id', $request->organization_id)->first();
//        $StudentRegistration = StudentRegistration::where('guardian_mobile', $request->id)->where('organization_id', $request->organization_id)->first();
//
//        if ($student != "" || $StudentRegistration != "") {
//            return response()->json(1);
//        } else {
        return response()->json(0);
//        }
    }

    public function studentView($id)
    {

        $student_detail = StudentRegistration::where('id', $id)->first();

        return view("applicationcenter::student_view", compact('student_detail'));
    }


    // registartion setting for regular school and saas
    public function Updatesettings(Request $request)
    {

        try {

            $key1 = 'NOCAPTCHA_SITEKEY';
            $key2 = 'NOCAPTCHA_SECRET';


            $value1 = $request->nocaptcha_sitekey;
            $value2 = $request->nocaptcha_secret;


            $path = base_path() . "/.env";
            $NOCAPTCHA_SITEKEY = env($key1);
            $NOCAPTCHA_SECRET = env($key2);


            if (file_exists($path)) {
                file_put_contents($path, str_replace(
                    "$key1=" . $NOCAPTCHA_SITEKEY,
                    "$key1=" . $value1,
                    file_get_contents($path)
                ));
                file_put_contents($path, str_replace(
                    "$key2=" . $NOCAPTCHA_SECRET,
                    "$key2=" . $value2,
                    file_get_contents($path)
                ));
            }


            $setting = RegistrationSetting::find(1);

            if ($setting == "") {
                $setting = new RegistrationSetting();
            }

            if (isset($request->position)) {
                $setting->position = $request->position;
            }

            if (isset($request->registration_permission)) {
                $setting->registration_permission = $request->registration_permission;
            }

            if (isset($request->registration_after_mail)) {
                $setting->registration_after_mail = $request->registration_after_mail;
            }

            if (isset($request->approve_after_mail)) {
                $setting->approve_after_mail = $request->approve_after_mail;
            }

            if (isset($request->recaptcha)) {
                $setting->recaptcha = $request->recaptcha;
            }

            $setting->nocaptcha_sitekey = $request->nocaptcha_sitekey;
            $setting->nocaptcha_secret = $request->nocaptcha_secret;

            $setting->save();

            Toastr::success('Operation successful', 'Success');
            return redirect()->back();
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    /**
     * Display a list of students missing photos
     * 
     * @return \Illuminate\View\View
     */
    public function missingPhotosReport()
    {
        try {
            // Query to get students without photos
            $students = Student::whereRaw('(student_photo IS NULL OR student_photo = "") AND (image IS NULL OR image = "")')
                ->orderBy('created_at', 'desc')
                ->get();
            
            Log::info('Missing photos report generated', [
                'count' => $students->count()
            ]);
            
            return view('applicationcenter::missing_photos_report', compact('students'));
        } catch (\Exception $e) {
            Log::error('Error generating missing photos report', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            Toastr::error('Error generating report: ' . $e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }
    
    /**
     * Upload a photo for a specific student
     * 
     * @param \Illuminate\Http\Request $request
     * @param int $student_id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function uploadStudentPhoto(Request $request, StudentImageService $studentImageService, $student_id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'photo' => 'required|image|mimes:jpg,png,gif|max:2048'
            ]);
            
            if ($validator->fails()) {
                Log::warning('Photo validation failed during admin upload', [
                    'student_id' => $student_id,
                    'errors' => $validator->errors()->toArray()
                ]);
                
                return redirect()->back()->withErrors($validator)->withInput();
            }
            
            $student = Student::findOrFail($student_id);
            
            if ($request->hasFile('photo')) {
                $path = $studentImageService->storeStudentImage($student, $request->file('photo'));
                // synchronize legacy image field if needed
                $student->student_photo = $path;
                $student->save();
                Log::info('Student photo uploaded via StudentImageService', ['student_id' => $student_id, 'photo_path' => $path]);
                $photoUrl = asset(str_replace('public/', '', $path));
                return response()->json(['success' => true, 'message' => 'Photo uploaded successfully', 'photo_url' => $photoUrl], 200);
            }
        } catch (\Exception $e) {
            Log::error('Error uploading student photo', [
                'student_id' => $student_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            Toastr::error('Error uploading photo: ' . $e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }
    
    /**
     * Send an email reminder to student about uploading their photo
     * 
     * @param int $student_id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function sendPhotoReminder($student_id)
    {
        try {
            $student = Student::findOrFail($student_id);
            
            if (empty($student->email)) {
                Toastr::error('Student does not have an email address', 'Failed');
                return redirect()->back();
            }
            
            // Send email reminder
            try {
                Mail::send('modules.applicationcenter.email.photo_reminder', ['student' => $student], function ($message) use ($student) {
                    $settings = EmailSetting::find(1);
                    $email = $settings->from_email;
                    $organizationName = $settings->from_name;
                    
                    $message->to($student->email, $student->full_name)->subject('Important: Photo Upload Required');
                    $message->from($email, $organizationName);
                });
                
                Log::info('Photo reminder email sent', [
                    'student_id' => $student_id,
                    'email' => $student->email
                ]);
                
                Toastr::success('Photo reminder sent successfully', 'Success');
            } catch (\Exception $e) {
                Log::error('Failed to send photo reminder email', [
                    'student_id' => $student_id,
                    'error' => $e->getMessage()
                ]);
                
                Toastr::error('Failed to send email: ' . $e->getMessage(), 'Failed');
            }
            
            return redirect()->back();
        } catch (\Exception $e) {
            Log::error('Error sending photo reminder', [
                'student_id' => $student_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            Toastr::error('Error: ' . $e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }

    /**
     * Check if photo exists in session and return it
     * Used for loading photo preview on page reload
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkPhotoSession(Request $request)
    {
        try {
            $referrerUrl = url()->previous();
            $referrerRouteName = app('router')->getRoutes()->match(app('request')->create($referrerUrl))->getName();
            
            Log::info('Checking photo session', [
                'referrer_route' => $referrerRouteName
            ]);
            
            // Determine if we should check for dependent photo or student photo
            $photoKey = ($referrerRouteName == 'student.guardian.application.form') ? 'dependent_photo' : 'student_photo';
            
            if (session()->has($photoKey)) {
                $photoPath = session($photoKey);
                $fullPath = storage_path('app/' . $photoPath);
                
                if (file_exists($fullPath)) {
                    // Remove 'public/' from the beginning of the path if it exists
                    $adjustedPath = preg_replace('/^public\//', '', $photoPath);
                    $photoUrl = asset($adjustedPath);
                    
                    Log::info('Photo found in session', [
                        'photo_path' => $photoPath,
                        'adjusted_path' => $adjustedPath,
                        'photo_url' => $photoUrl
                    ]);
                    
                    return response()->json([
                        'success' => true, 
                        'photo' => $photoUrl
                    ]);
                }
            }
            
            Log::info('No photo found in session');
            return response()->json([
                'success' => false, 
                'message' => 'No photo found in session'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error checking photo session', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false, 
                'message' => 'Error checking photo session'
            ]);
        }
    }
}

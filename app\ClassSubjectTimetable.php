<?php

namespace App;

use App\Observers\TeacherTimetableObserver;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\ClassSubjectTimetable
 *
 * @property int $id
 * @property int $class_teacher_subject_id
 * @property string|null $sat
 * @property string|null $sun
 * @property string|null $mon
 * @property string|null $tue
 * @property string|null $wed
 * @property string|null $thu
 * @property string|null $fri
 * @property float $class_duration
 * @property \Illuminate\Support\Carbon|null $start_at
 * @property string|null $end_at
 * @property-read \App\ClassTeacherSubject $class_subject
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable query()
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable whereClassDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable whereClassTeacherSubjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable whereEndAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable whereFri($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable whereMon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable whereSat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable whereStartAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable whereSun($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable whereThu($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable whereTue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassSubjectTimetable whereWed($value)
 * @mixin \Eloquent
 */
class ClassSubjectTimetable extends Model
{
    use SoftDeletes;
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'class_subject_timetable';

    public $timestamps = false;
    
    public $casts= ["start_at"];
    protected $dispatchesEvents = [
        'creating' => TeacherTimetableObserver::class,
//        'deleted' => UserDeleted::class,
    ];
    protected $fillable = [
        'mon',
        'tue',
        'wed',
        'thu',
        'fri',
        'sat',
        'sun',
        'class_duration',
        'class_teacher_subject_id',
        'days_count',
        'start_at'
    ];

//    protected $fillable = [
//
//        'end_at',
//        'deleted_at'
//    ];
    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    public function class_subject()
    {
        return $this->belongsTo('App\ClassTeacherSubject' , 'class_teacher_subject_id' , 'id');
    }

    
}

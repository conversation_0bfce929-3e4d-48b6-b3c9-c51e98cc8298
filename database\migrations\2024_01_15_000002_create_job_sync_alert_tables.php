<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateJobSyncAlertTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create job_sync_alert_rules table
        Schema::create('job_sync_alert_rules', function (Blueprint $table) {
            $table->id();
            $table->enum('alert_type', ['zero_jobs', 'duration_spike', 'error_pattern', 'category_drop'])->comment('Type of alert condition to monitor');
            $table->string('command', 255)->comment('Sync command this rule applies to');
            $table->json('threshold')->comment('Rule-specific thresholds (e.g., consecutive_runs: 3, duration_multiplier: 2)');
            $table->integer('window_minutes')->comment('Time window in minutes to evaluate the rule');
            $table->boolean('enabled')->default(true)->comment('Whether this alert rule is active');
            $table->json('recipients')->comment('Array of email addresses to notify');
            $table->json('escalation_policy')->nullable()->comment('Escalation rules for unacknowledged alerts');
            $table->timestamps();
            
            // Add indexes for performance
            $table->index(['alert_type', 'enabled']);
            $table->index(['command', 'enabled']);
        });

        // Create job_sync_alert_events table
        Schema::create('job_sync_alert_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('rule_id')->constrained('job_sync_alert_rules')->onDelete('cascade');
            $table->foreignId('execution_id')->nullable()->constrained('command_schedule_executions')->onDelete('set null');
            $table->json('alert_data')->comment('Specific alert context and trigger details');
            $table->enum('status', ['new', 'acknowledged', 'resolved'])->default('new')->comment('Current status of the alert');
            $table->timestamp('fired_at')->comment('When the alert was triggered');
            $table->timestamp('acknowledged_at')->nullable()->comment('When the alert was acknowledged');
            $table->timestamp('resolved_at')->nullable()->comment('When the alert was resolved');
            $table->timestamps();
            
            // Add indexes for performance
            $table->index(['rule_id', 'status']);
            $table->index(['status', 'fired_at']);
            $table->index('execution_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('job_sync_alert_events');
        Schema::dropIfExists('job_sync_alert_rules');
    }
} 
# Job Notification System: User Journey

## Overview

This document outlines the complete user journey for the job notification system, which allows users to receive personalized alerts about new job opportunities from Jobs.af based on their selected preferences.

## User Journey Map

### 1. Discovery & Subscription

**Entry Points:**
- Jobs Board (`/workplace/general/jobs`)
- Direct Navigation to Notifications Page (`/workplace/general/jobs/notifications`)

**Actions:**
1. User navigates to the Job Notifications page
2. User views the introduction/info box explaining the notification system
3. User enters their email address (pre-populated if authenticated)
4. User submits the subscription form
5. System creates a subscriber record and shows successful subscription message

**Technical Implementation:**
- `subscribe()` method in `JobsController` processes the subscription
- `JobNotificationSubscriber` model stores the subscription
- Email verification may be required depending on configuration

### 2. Creating a Notification Setup

**Actions:**
1. User clicks "Add New Setup" button
2. User proceeds through the multi-step wizard:
   - **Step 1:** Select job categories of interest
   - **Step 2:** Choose notification frequency (minutes, hours, daily, weekly)
   - **Step 3:** Add email recipients
   - **Step 4:** Review and confirm by naming the setup
3. System creates the notification setup and confirms success

**Technical Implementation:**
- `storeNotificationSetup()` method in `JobsController` processes the setup creation
- `JobNotificationSetup` model stores the setup configuration
- `JobNotificationRecipient` model stores the recipients
- Categories are linked through the pivot table

### 3. Receiving Notifications

**Background Process:**
1. System regularly syncs jobs from Jobs.af API (every minute via scheduled command)
2. For each job setup:
   - System checks if it's time to send notifications based on the user's selected interval
   - System identifies jobs matching the user's category preferences
   - System filters out jobs that have already been sent to this recipient (using `JobNotificationSentJob` records)
   - System sends email notifications containing only new, unsent jobs

**User Experience:**
1. User receives email with job listings that match their preferences
2. Email contains job details including:
   - Position title
   - Company name
   - Location
   - Job type
   - Posting date
   - Direct link to the job listing
3. User can click through to view the full job details

**Technical Implementation:**
- `NotifyJobSubscribersCommand` runs regularly via Laravel's scheduler
- `JobService::notifySubscribers()` handles the main notification logic
- `ProcessJobNotificationSetupJob` processes each notification setup individually
- `JobNotificationSentJob` tracks which jobs have been sent to which recipients
- Notification frequency is controlled by the `notification_interval` field (in minutes)

### 4. Managing Notification Setups

**Actions:**
1. User returns to the Job Notifications page
2. User views their existing notification setups in a table
3. User can:
   - Edit a setup (modify categories, frequency, or recipients)
   - Delete a setup
   - View notification history

**Technical Implementation:**
- `updateNotificationSetup()` method in `JobsController` handles setup updates
- `deleteNotificationSetup()` method handles setup deletion
- DataTables JS library displays the setup information

### 5. Unsubscribing

**Actions:**
1. User clicks "Unsubscribe" button on the notifications page
2. System prompts for confirmation
3. User confirms unsubscription
4. System updates subscriber record and confirms unsubscription

**Technical Implementation:**
- `unsubscribe()` method in `JobsController` handles the unsubscription
- Subscriber record is marked as inactive rather than deleted to preserve history

## Notification Delivery Logic

The critical component ensuring users receive notifications at their preferred intervals is the `JobNotificationSentJob` system:

1. When a notification is sent for a specific job to a specific recipient, a record is created in `job_notification_sent_jobs` with:
   - `setup_id` - The notification setup ID
   - `job_id` - The job that was sent
   - `recipient_email` - The recipient's email address
   - `sent_at` - The timestamp when the notification was sent

2. Before sending notifications, the system queries for jobs that:
   - Match the user's selected categories
   - Have not been previously sent to this recipient (by checking the `job_notification_sent_jobs` table)
   - Are recent enough to be relevant

3. The notification interval (stored in minutes in the `notification_interval` field) determines how frequently the system checks for new jobs for each setup.

This ensures that:
- Users aren't notified about the same job multiple times
- Users receive notifications at their preferred frequency
- Only jobs matching user preferences are included in notifications

## Technical Components

The notification system relies on several key components:

1. **Models:**
   - `JobNotificationSubscriber`: Stores subscriber information
   - `JobNotificationSetup`: Stores notification preferences
   - `JobNotificationRecipient`: Stores recipient emails
   - `JobNotificationSentJob`: Tracks sent notifications to prevent duplicates
   - `JobCategory`: Defines job categories for filtering

2. **Commands:**
   - `NotifyJobSubscribersCommand`: Processes notification setups and dispatches jobs
   - `SyncJobsCommand`: Fetches jobs from the Jobs.af API

3. **Jobs/Queues:**
   - `ProcessJobNotificationSetupJob`: Processes each notification setup
   - Custom queue system to separate notification processing from other application jobs

4. **User Interface:**
   - Multi-step wizard for creating notification setups
   - DataTables for managing existing setups
   - Recipient management interface

## Data Flow Diagram

```plantuml
@startuml
!theme vibrant

package "User Interaction" {
  actor User
  interface "Notifications UI (Web)" as WebUI
}

package "Application Core" {
  component "JobsController" as Controller
  component "JobService" as Service
  queue "JobNotificationQueue" as JobQueue
}

package "Scheduled Commands" {
  component "SyncJobsCommand"
  component "NotifyJobSubscribersCommand"
}

package "Background Jobs" {
  component "ProcessJobNotificationSetupJob" as ProcessSetupJob
}

package "Database (MySQL)" {
  database "Jobs DB" as JobsDB
  database "Subscribers DB (JobNotificationSubscriber)" as SubscriberDB
  database "Setups DB (JobNotificationSetup)" as SetupDB
  database "Recipients DB (JobNotificationRecipient)" as RecipientDB
  database "CategoriesPivot DB (job_notification_category)" as CategoryPivotDB
  database "SentLog DB (JobNotificationSentJob)" as SentLogDB
  database "Categories DB (JobCategory)" as CategoriesDB
}

package "External Services" {
  cloud "Jobs.af API" as ExternalAPI
  cloud "Email Service" as EmailProvider
}

' User Interactions
User --> WebUI : Manages Notification Setups
WebUI --> Controller : HTTP Requests (Subscribe, Create/Edit Setup)
Controller --> SubscriberDB : CRUD Subscriber
Controller --> SetupDB : CRUD Setup
Controller --> RecipientDB : CRUD Recipients for Setup
Controller --> CategoryPivotDB : Link Setup to Categories

' Job Sync Flow
SyncJobsCommand --> Service : Trigger Job Sync
Service --> ExternalAPI : GET /jobs/list
ExternalAPI --> Service : Job Data
Service --> JobsDB : Store/Update Jobs
Service --> CategoriesDB : Relate Jobs to Categories (if applicable during sync)

' Notification Trigger Flow
NotifyJobSubscribersCommand --> Service : Trigger Subscriber Notification
Service --> SubscriberDB : Get Active Subscribers
Service --> SetupDB : Get Active Setups for Subscribers
Service --> JobQueue : Dispatch ProcessJobNotificationSetupJob

' Individual Setup Processing (from Queue)
JobQueue --> ProcessSetupJob : Consume Job
ProcessSetupJob --> Service : Process Setup
Service --> SetupDB : Read Setup Details (Interval, Categories)
Service --> CategoryPivotDB : Read Setup Categories
Service --> CategoriesDB : Read Category Info
Service --> JobsDB : Find Matching New Jobs
Service --> SentLogDB : Check Already Sent Jobs for this Setup/Recipient
Service --> RecipientDB : Get Recipients for Setup
Service --> EmailProvider : Send Notification Email
Service --> SentLogDB : Log Sent Email (Job ID, Setup ID, Recipient Email, Timestamp)

' Relationships and Data Access
Controller ..> Service : Uses
ProcessSetupJob ..> EmailProvider : (via Service)
ProcessSetupJob ..> SentLogDB : (via Service)

JobsDB -[hidden]down- SubscriberDB
SubscriberDB -[hidden]down- SetupDB
SetupDB -[hidden]down- RecipientDB
RecipientDB -[hidden]down- CategoryPivotDB
CategoryPivotDB -[hidden]down- SentLogDB
SentLogDB -[hidden]down- CategoriesDB

@enduml
``` 
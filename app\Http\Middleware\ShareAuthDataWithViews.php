<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;

class ShareAuthDataWithViews
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Check all available guards
        $guards = ['web', 'student', 'guardian', 'employee', 'superior', 'organization'];
        $userId = null;
        
        // Try to get user ID from any of the guards
        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                $userId = Auth::guard($guard)->id();
                break;
            }
        }
        
        // If no user ID found from guards, try session
        if (!$userId && $request->session()->has('login_user_id')) {
            $userId = $request->session()->get('login_user_id');
        }
        
        // Share the user ID with all views
        View::share('userId', $userId);
        
        // Store JavaScript data in the request attributes to avoid dynamic properties
        $jsData = $request->attributes->get('javaScriptData', []);
        $jsData['userId'] = $userId;
        $request->attributes->set('javaScriptData', $jsData);
        
        return $next($request);
    }
} 
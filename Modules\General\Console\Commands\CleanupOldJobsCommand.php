<?php

namespace Modules\General\Console\Commands;

use Illuminate\Console\Command;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\Job;
use Illuminate\Support\Facades\Log;

class CleanupOldJobsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'general:cleanup-old-jobs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete jobs older than 7 days';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            // Log the start of the operation
            Log::info("Starting cleanup of old jobs at " . now());
            
            $cutoff = now()->subDays(7);
            
            // Check if the Job model exists and the table is accessible
            if (!class_exists(Job::class)) {
                throw new \Exception("Job model class does not exist");
            }
            
            // Query with a limit to avoid memory issues
            $count = Job::where('publish_date', '<', $cutoff)->limit(5000)->delete();
            
            Log::info("Deleted {$count} jobs older than 7 days");
            $this->info("Successfully deleted {$count} old jobs");
            
            return 0;
        } catch (\PDOException $e) {
            // Handle database connection issues
            $errorMessage = "Database error cleaning up old jobs: " . $e->getMessage();
            Log::error($errorMessage);
            $this->error($errorMessage);
            return 1;
        } catch (\Exception $e) {
            // Handle all other exceptions
            $errorMessage = "Error cleaning up old jobs: " . $e->getMessage();
            Log::error($errorMessage);
            $this->error($errorMessage);
            return 1;
        }
    }
} 
	/*
  	Flaticon icon font: Flaticon
  	Creation date: 29/05/2019 04:04
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("../../fonts/Flaticon.eot");
  src: url("../../fonts/Flaticon.eot?#iefix") format("embedded-opentype"),
       url("../../fonts/Flaticon.woff2") format("woff2"),
       url("../../fonts/Flaticon.woff") format("woff"),
       url("../../fonts/Flaticon.ttf") format("truetype"),
       url("../../fonts/Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("../../fonts/Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
        font-size: 14px;
font-style: normal;
margin-left: 20px;
}

.flaticon-speedometer:before { content: "\f100"; }
.flaticon-analytics:before { content: "\f101"; }
.flaticon-reading:before { content: "\f102"; }
.flaticon-professor:before { content: "\f103"; }
.flaticon-wallet:before { content: "\f104"; }
.flaticon-accounting:before { content: "\f105"; }
.flaticon-consultation:before { content: "\f106"; }
.flaticon-test:before { content: "\f107"; }
.flaticon-graduated-student:before { content: "\f108"; }
.flaticon-book:before { content: "\f109"; }
.flaticon-email:before { content: "\f10a"; }
.flaticon-reading-1:before { content: "\f10b"; }
.flaticon-inventory:before { content: "\f10c"; }
.flaticon-bus:before { content: "\f10d"; }
.flaticon-hotel:before { content: "\f10e"; }
.flaticon-software:before { content: "\f10f"; }
.flaticon-analysis:before { content: "\f110"; }
.flaticon-settings:before { content: "\f111"; }
.flaticon-resume:before { content: "\f112"; }
.flaticon-calendar:before { content: "\f113"; }
.flaticon-calendar-1:before { content: "\f114"; }
.flaticon-test-1:before { content: "\f115"; }
.flaticon-book-1:before { content: "\f116"; }
.flaticon-slumber:before { content: "\f117"; }
.flaticon-notification:before { content: "\f118"; }
.flaticon-data-storage:before { content: "\f119"; }
.flaticon-authentication:before { content: "\f11a"; }
.flaticon-poster:before { content: "\f11b"; }
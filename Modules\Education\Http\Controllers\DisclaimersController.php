<?php

namespace Modules\Education\Http\Controllers;



use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Routing\Controller;
class DisclaimersController extends Controller
{
    public function showAcceptForm()
    {
        // A simple blade to show disclaimers text
        // "I accept" -> post
        return view('auth.disclaimers_accept');
    }

    public function accept(Request $request)
    {
        $user = Auth::user();
        $user->acceptDisclaimers();
        return redirect()->route('workplace.dashboard')->with('message', 'Disclaimers accepted!');
    }
}


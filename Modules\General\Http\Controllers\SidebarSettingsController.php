<?php

namespace Modules\General\Http\Controllers;

use App\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;

class SidebarSettingsController extends Controller
{
    /**
     * Display the sidebar settings page
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {


        
        Log::info('Sidebar settings page accessed by user: ' . Auth::id());
        
        $organizationId = config('organization_id');
        
        // Get all sidebar-related settings
        $settings = [
            'sidebar_bg_color_start' => $this->getSetting('sidebar_bg_color_start', '#1a2a3a', $organizationId),
            'sidebar_bg_color_end' => $this->getSetting('sidebar_bg_color_end', '#2c3e50', $organizationId),
            'sidebar_text_color' => $this->getSetting('sidebar_text_color', '#ffffff', $organizationId),
            'sidebar_icon_color' => $this->getSetting('sidebar_icon_color', '#3498db', $organizationId),
            'sidebar_active_item_color' => $this->getSetting('sidebar_active_item_color', '#3498db', $organizationId),
            'sidebar_hover_bg_color' => $this->getSetting('sidebar_hover_bg_color', 'rgba(52, 152, 219, 0.1)', $organizationId),
            'sidebar_logo_size' => $this->getSetting('sidebar_logo_size', '70', $organizationId),
            'sidebar_auto_show' => $this->getSetting('sidebar_auto_show', '0', $organizationId),
            'sidebar_auto_hide' => $this->getSetting('sidebar_auto_hide', '0', $organizationId),
            'sidebar_width' => $this->getSetting('sidebar_width', '220', $organizationId),
            'sidebar_border_right' => $this->getSetting('sidebar_border_right', 'rgba(255, 255, 255, 0.05)', $organizationId),
            'sidebar_shadow' => $this->getSetting('sidebar_shadow', 'rgba(0, 0, 0, 0.15)', $organizationId),
        ];
        
        return view('admin.sidebar_settings', compact('settings'));
    }
    
    /**
     * Update the sidebar settings
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        Log::info('Sidebar settings update initiated by user: ' . Auth::id(), $request->except('_token'));
        
        $organizationId = config('organization_id');
        
        // Validate input
        $request->validate([
            'sidebar_bg_color_start' => 'required|string|max:50',
            'sidebar_bg_color_end' => 'required|string|max:50',
            'sidebar_text_color' => 'required|string|max:50',
            'sidebar_icon_color' => 'required|string|max:50',
            'sidebar_active_item_color' => 'required|string|max:50',
            'sidebar_hover_bg_color' => 'required|string|max:50',
            'sidebar_logo_size' => 'required|numeric|min:30|max:200',
            'sidebar_auto_show' => 'nullable|in:0,1',
            'sidebar_auto_hide' => 'nullable|in:0,1',
            'sidebar_width' => 'required|numeric|min:150|max:400',
            'sidebar_border_right' => 'required|string|max:50',
            'sidebar_shadow' => 'required|string|max:50',
        ]);
        
        // Update settings
        $fields = [
            'sidebar_bg_color_start',
            'sidebar_bg_color_end',
            'sidebar_text_color',
            'sidebar_icon_color',
            'sidebar_active_item_color',
            'sidebar_hover_bg_color',
            'sidebar_logo_size',
            'sidebar_auto_show',
            'sidebar_auto_hide',
            'sidebar_width',
            'sidebar_border_right',
            'sidebar_shadow',
        ];
        
        foreach ($fields as $field) {
            $value = $request->has($field) ? $request->input($field) : '0';
            $this->updateSetting($field, $value, $organizationId);
        }
        
        // Clear cache if needed
        // \Cache::forget('settings');
        
        Log::info('Sidebar settings updated successfully by user: ' . Auth::id());
        
        return redirect()->route('sidebar.settings')->with('success', 'Sidebar settings updated successfully! The changes are now visible to all users in your organization.');
    }
    
    /**
     * Get setting value
     *
     * @param string $name
     * @param string $default
     * @param int $organizationId
     * @return string
     */
    private function getSetting($name, $default, $organizationId)
    {
        $setting = Setting::where('name', $name)
            ->where('organization_id', $organizationId)
            ->first();
            
        return $setting ? $setting->value : $default;
    }
    
    /**
     * Update or create setting
     *
     * @param string $name
     * @param string $value
     * @param int $organizationId
     * @return void
     */
    private function updateSetting($name, $value, $organizationId)
    {
        Setting::updateOrCreate(
            [
                'name' => $name,
                'organization_id' => $organizationId
            ],
            [
                'value' => $value
            ]
        );
    }
    
    /**
     * Reset sidebar settings to default
     * 
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reset()
    {
        Log::info('Sidebar settings reset initiated by user: ' . Auth::id());
        
        $organizationId = config('organization_id');
        
        // Default values
        $defaults = [
            'sidebar_bg_color_start' => '#1a2a3a',
            'sidebar_bg_color_end' => '#2c3e50',
            'sidebar_text_color' => '#ffffff',
            'sidebar_icon_color' => '#3498db',
            'sidebar_active_item_color' => '#3498db',
            'sidebar_hover_bg_color' => 'rgba(52, 152, 219, 0.1)',
            'sidebar_logo_size' => '70',
            'sidebar_auto_show' => '0',
            'sidebar_auto_hide' => '0',
            'sidebar_width' => '220',
            'sidebar_border_right' => 'rgba(255, 255, 255, 0.05)',
            'sidebar_shadow' => 'rgba(0, 0, 0, 0.15)',
        ];
        
        foreach ($defaults as $name => $value) {
            $this->updateSetting($name, $value, $organizationId);
        }
        
        Log::info('Sidebar settings reset to defaults by user: ' . Auth::id());
        
        return redirect()->route('sidebar.settings')->with('success', 'Sidebar settings reset to defaults! All users will now see the original sidebar design.');
    }
} 
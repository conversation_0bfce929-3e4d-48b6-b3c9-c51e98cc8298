/*
    Flaticon icon font: Flaticon
    Creation date: 13/11/2018 08:10
    */

@font-face {
  font-family: "Flaticon";
  src: url("../../fonts/Flaticon.eot");
  src: url("../../fonts/Flaticon.eot?#iefix") format("embedded-opentype"),
    url("../../fonts/Flaticon.woff") format("woff"), url("../../fonts/Flaticon.ttf") format("truetype"),
    url("../../fonts/Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before,
[class*=" flaticon-"]:before,
[class^="flaticon-"]:after,
[class*=" flaticon-"]:after {
  font-family: Flaticon;
  font-size: 17px;
  font-style: normal;
  position: relative;
  top: 2px;
}

.flaticon-dashboard:before {
  content: "\f100";
}
.flaticon-cabinet:before {
  content: "\f101";
}
.flaticon-book:before {
  content: "\f102";
}
.flaticon-teacher:before {
  content: "\f103";
}
.flaticon-wallet:before {
  content: "\f104";
}
.flaticon-account:before {
  content: "\f105";
}
.flaticon-target:before {
  content: "\f106";
}
.flaticon-book-1:before {
  content: "\f107";
}
.flaticon-university:before {
  content: "\f108";
}
.flaticon-mail:before {
  content: "\f109";
}
.flaticon-book-2:before {
  content: "\f10a";
}
.flaticon-chest:before {
  content: "\f10b";
}
.flaticon-truck:before {
  content: "\f10c";
}
.flaticon-bed:before {
  content: "\f10d";
}
.flaticon-browser:before {
  content: "\f10e";
}
.flaticon-report:before {
  content: "\f10f";
}
.flaticon-gear:before {
  content: "\f110";
}
.flaticon-bell:before {
  content: "\f111";
}
.flaticon-mail-1:before {
  content: "\f112";
}
.flaticon-user:before {
  content: "\f113";
}
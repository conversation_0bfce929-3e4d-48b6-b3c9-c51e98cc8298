<div class="nav-tabs-custom">
    <ul class="nav nav-tabs">
        @foreach(config('app.locales') as $key => $language)
        <li @if($key == 0 ) class="active" @endif>
            <a aria-expanded="true" data-toggle="tab" href="#{{$language}}">
                {{strtoupper($language)}}
            </a>
        </li>
        @endforeach
    </ul>
    <div class="tab-content">
        @foreach(config('app.locales') as $key => $language)
        <div class="tab-pane clearfix @if($key == 0 ) active @endif" id="{{$language}}">
            <div class="col-md-12">
            
                <div class="form-group {{ $errors->has($language.'.name') ? 'has-error' : ''}}">
                    {!! Form::label('name', 'Name ['.$language.']', ['class' => 'control-label']) !!}
                
                    {!! Form::text('translate['.$language.'][name]', isset($testimonial) && isset($testimonial->translate($language)->name) ? $testimonial->translate($language)->name : '' , ['class' => 'form-control']) !!}
                    {!! $errors->first('translate.'.$language.'.name', '
                    <p class="help-block alert-danger">
                        :message
                    </p>
                    ') !!}
                </div>
                <div class="form-group {{ $errors->has($language.'.title') ? 'has-error' : ''}}">
                    {!! Form::label('title', 'Position/Designation ['.$language.']', ['class' => 'control-label']) !!}
                
                    {!! Form::text('translate['.$language.'][title]', isset($testimonial) && isset($testimonial->translate($language)->title) ? $testimonial->translate($language)->title : '' , ['class' => 'form-control']) !!}
                    {!! $errors->first('translate.'.$language.'.title', '
                    <p class="help-block alert-danger">
                        :message
                    </p>
                    ') !!}
                </div>
                <div class="form-group {{ $errors->has($language.'.testimonial') ? 'has-error' : ''}} testimonial" >
                    {!! Form::label('testimonial', 'Content ['.$language.']', ['class' => 'control-label']) !!}
                    {!! Form::textarea('translate['.$language.'][testimonial]',isset($testimonial) && isset($testimonial->translate($language)->testimonial) ? $testimonial->translate($language)->testimonial : '' , ['class' => 'form-control']) !!}
                    {!! $errors->first('translate.'.$language.'testimonial', '
                    <p class="help-block alert-danger">
                        :message
                    </p>
                    ') !!}
                </div>
            </div>
        </div>
        <!-- /.tab-pane -->
        @endforeach
    </div>
    <!-- /.tab-testimonial -->
</div>


<div class="form-group {{ $errors->has('image') ? 'has-error' : ''}}">
    {!! Form::label('image', 'Image', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::text('image', null, ['class' => 'form-control']) !!}
        {!! $errors->first('image', '<p class="help-block">:message</p>') !!}
    </div>
</div><div class="form-group {{ $errors->has('status') ? 'has-error' : ''}}">
    {!! Form::label('status', 'Status', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('status', [ 1 => 'Published' , 0 => 'Unpublished'] , null, ['class' => 'form-control']) !!}
        {!! $errors->first('status', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group">
    <div class="col-md-offset-4 col-md-4">
        {!! Form::submit(isset($submitButtonText) ? $submitButtonText : trans('common.create'), ['class' => 'btn btn-primary']) !!}
    </div>
</div>

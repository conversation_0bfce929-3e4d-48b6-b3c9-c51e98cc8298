<?php

namespace Modules\Education\Http\Controllers;
use App\Http\Controllers\Controller;
use App\MarksGrade;
use App\Program;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Modules\ExaminationCertification\Http\Requests\UpdateMarksGradeRequest;

class MarksGradeController extends Controller
{
    public function __construct()
	{
//        $this->middleware('PM');
        // User::checkAuth();
	}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try{
            $marks_grades = MarksGrade::orderBy('gpa', 'desc')->where('academic_id', getAcademicId())->get();
            // return $marks_grades;
//            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
//                return ApiBaseMethod::sendResponse($marks_grades, null);
//            }
            return view('backEnd.examination.marks_grade', compact('marks_grades'));
        }catch (\Exception $e) {
           Toastr::error('Operation Failed', 'Failed');
           return redirect()->back();
        }
    }

    public function store(Request $request)
    {



        $input = $request->all();

        $validator = Validator::make($input, [
            'grade_name' => "required|max:50",
            'gpa' => "required|max:4",
            'percent_from' => "required|integer|min:0",
            'percent_upto' => "required|integer|min:0",
            'grade_from' => "required|max:6|min:0",
            'grade_upto' => "required|max:6|min:0",
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

         $is_duplicate_gpa = MarksGrade::where('program_id', $request->program_id)->where('gpa', $request->gpa)->first();
         if ($is_duplicate_gpa) {
             Toastr::error('Duplicate GPA found!', 'Failed');
             return redirect()->back()->withErrors($validator)->withInput();
         }
        try{


            $marks_grade = new MarksGrade();
//            $marks_grade->minimum_mark_to_pass = $request->minimumMarktoPass;
            $marks_grade->grade_name = $request->grade_name;
            $marks_grade->program_id = $request->program_id;
            $marks_grade->gpa = $request->gpa;
            $marks_grade->percent_from = $request->percent_from;
            $marks_grade->percent_upto = $request->percent_upto;
            $marks_grade->from = $request->grade_from;
            $marks_grade->up = $request->grade_upto;
            $marks_grade->description = $request->description;
            $marks_grade->organization_id = config('organization_id') ;
            $marks_grade->created_at= Carbon::now();


            $result = $marks_grade->save();

//            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
//                if ($result) {
//                    return ApiBaseMethod::sendResponse(null, 'Grade has been created successfully');
//                } else {
//                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
//                }
//            } else {
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
//            }
        }catch (\Exception $e) {

             dd($e->getMessage());
           Toastr::error('Operation Failed', 'Failed');
           return redirect()->back();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id,$programId)
    {
        try{



            $program = Program::findOrFail($programId);


             if (checkAdmin()) {
                $marks_grade = MarksGrade::find($id);
            }else{
                $marks_grade = MarksGrade::where('id',$id)->first();
            }



//            $marks_grades = MarksGrade::where('academic_id', getAcademicId())->get();
            $marks_grades = MarksGrade::where('program_id',$id)->get();

//            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
//                $data = [];
//                $data['marks_grade'] = $marks_grade->toArray();
//                $data['marks_grades'] = $marks_grades->toArray();
//                return ApiBaseMethod::sendResponse($data, null);
//            }

            return view('education::programs.show', compact('marks_grade', 'marks_grades','program'));



            return view('backEnd.examination.marks_grade', compact('marks_grade', 'marks_grades'));
        }catch (\Exception $e) {

            dd($e->getMessage());
           Toastr::error('Operation Failed', 'Failed');
           return redirect()->back();
        }
    }


    public function update(UpdateMarksGradeRequest $request, $id, $programid)
    {
        try {
            $input = $request->all();

            // You can skip the validation here,
            // as it has been taken care of by UpdateMarksGradeRequest

            // school wise unique validation
            $is_duplicate = MarksGrade::where('program_id', $input['program_id'])
                ->where('grade_name', $input['grade_name'])
                ->where('id', '!=', $id)
                ->first();

            if ($is_duplicate) {
                Toastr::error('Duplicate name found!', 'Failed');
                return redirect()->back();
            }

            $is_duplicate_gpa = MarksGrade::where('program_id', $input['program_id'])
                ->where('gpa', $input['gpa'])
                ->where('id', '!=', $id)
                ->first();

            if ($is_duplicate_gpa) {
                Toastr::error('Duplicate GPA found!', 'Failed');
                return redirect()->back();
            }

            if (checkAdmin()) {
                $marks_grade = MarksGrade::find($id);
            } else {
                $marks_grade = MarksGrade::where('id', $id)
                    ->where('program_id', $input['program_id'])
                    ->first();
            }

            $marks_grade->update($input);

            Toastr::success('Operation successful', 'Success');
            return redirect()->route('programs.show', $input['program_id']);
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id,$programId)
    {
        try{
//            $tables = tableList::getTableList('id', $id);
//            dd($tables);
//            if($tables == null ){
                if (checkAdmin()) {
                    $marks_grade = MarksGrade::destroy($id);
                }else{
                    $marks_grade = MarksGrade::where('id',$id)->delete();
                }
//                if (ApiBaseMethod::checkUrl($request->fullUrl())) {
//                    if ($marks_grade) {
//                        return ApiBaseMethod::sendResponse(null, 'Grdae has been deleted successfully');
//                    } else {
//                        return ApiBaseMethod::sendError('Something went wrong, please try again.');
//                    }
//                } else {
                    if ($marks_grade) {
                        Toastr::success('Operation successful', 'Success');
                        return redirect()->back();
                    } else {
                        Toastr::error('Operation Failed', 'Failed');
                        return redirect()->back();
                    }
//                }
//            } else{
//                $msg = 'This data already used in  : ' . $tables .' Please remove those data first';
//                Toastr::error($msg, 'Failed');
//                return redirect()->back();
//            }
        }catch (\Exception $e) {

            dd($e->getMessage());
           Toastr::error('Operation Failed', 'Failed');
           return redirect()->back();
        }
    }
}
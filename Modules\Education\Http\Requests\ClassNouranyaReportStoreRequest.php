<?php

namespace Modules\Education\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;


/**
 * Class ClassReportStoreRequest
 * @package Modules\Communicate\Http\Requests
 *
 *
 */
class ClassNouranyaReportStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [

        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            'id.required' => 'Please select a role',
            'id.exists' => 'Role does not exists'



        ];
    }
}

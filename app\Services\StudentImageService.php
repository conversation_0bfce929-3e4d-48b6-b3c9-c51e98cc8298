<?php

declare(strict_types=1);

namespace App\Services;

use App\Student;
use App\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\URL;
use Illuminate\Http\UploadedFile;

/**
 * Service for handling student image retrieval operations
 */
final class StudentImageService
{
    /**
     * Default image path relative to public directory.
     *
     * @var string
     */
    private string $defaultImagePath = 'avatar.jpg';

    /**
     * Default image path for male students relative to public directory.
     *
     * @var string
     */
    private string $maleDefaultImagePath = 'maleStudentProfilePicture.png';

    /**
     * Default image path for female students relative to public directory.
     *
     * @var string
     */
    private string $femaleDefaultImagePath = 'femaleStudentProfilePicture.png';



    public function storeStudentImage($student, UploadedFile $file): string
    {
        // Accept either a Student model, a User model, or a Student ID
        if ($student instanceof \App\User) {
            $relatedStudent = $student->student;
            if (! $relatedStudent instanceof \App\Student) {
                // No Student exists yet: stage the upload temporarily
                return $this->storeTemporaryImage($student, $file);
            }
            $student = $relatedStudent;
        } else if (!($student instanceof Student)) {
            // If we got an ID, load the student model
            try {
                $student = Student::findOrFail($student);
            } catch (\Exception $e) {
                Log::error('Failed to find student for image upload', [
                    'student_id' => $student,
                    'error' => $e->getMessage()
                ]);
                throw new \Exception("Student not found for ID: $student");
            }
        }
        
        // Now we definitely have a Student model
            $userId = $student->user_id ?? null;
            if ($userId) {
                $this->moveTemporaryImages($userId, $student->id);
            }

        // Process the file for an existing student
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $extension = $file->getClientOriginalExtension() ?: 'png';
        $safeName = preg_replace('/[^A-Za-z0-9_\-]/', '_', $originalName);
        $filename = time() . '_' . $safeName . '.' . $extension;
        $directory = "students/{$student->id}/profile_picture";
        
        // Store with public visibility
        $storedPath = $file->storePubliclyAs($directory, $filename, 'public');
        
        // Update student record
        $student->student_photo = $storedPath;
        $student->save();
        
        return $storedPath;
    }

    /**
     * Store an uploaded file temporarily for a given user.
     *
     * @param \App\User|int $user User instance or ID.
     * @param \Illuminate\Http\UploadedFile $file
     * @return string Stored path relative to public disk.
     */
    public function storeTemporaryImage($user, UploadedFile $file): string
    {
       
        $userId = $user instanceof \App\User ? $user->id : $user;
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $extension = $file->getClientOriginalExtension() 
                     ?: $file->guessClientExtension() 
                     ?: $file->guessExtension() 
                     ?: 'png';
        $safeName     = preg_replace('/[^A-Za-z0-9_\-]/', '_', $originalName);
        $filename     = time() . '_' . $safeName . '.' . $extension;
        $directory    = "temp/users/{$userId}/profile_picture";

        
        // Ensure only one temp image per user
        Storage::disk('public')->deleteDirectory($directory);

        // Store temporarily with public visibility
        return $file->storePubliclyAs($directory, $filename, 'public');
    }

    /**
     * Store an uploaded file temporarily for a dependent of a guardian.
     * 
     * @param \App\User|int $guardianUser Guardian User instance or ID
     * @param string $dependentIdentifier A unique identifier for the dependent (e.g. form input name or temp ID)
     * @param \Illuminate\Http\UploadedFile $file
     * @return string Stored path relative to public disk
     */
    public function storeTemporaryDependentImage($guardianUser, string $dependentIdentifier, UploadedFile $file): string 
    {
        try {
            Log::info('Storing temporary dependent image', [
                'guardian' => $guardianUser instanceof \App\User ? $guardianUser->id : $guardianUser,
                'dependentId' => $dependentIdentifier
            ]);
            
            $guardianId = $guardianUser instanceof \App\User ? $guardianUser->id : $guardianUser;
            $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $extension = $file->getClientOriginalExtension() 
                         ?: $file->guessClientExtension() 
                         ?: $file->guessExtension() 
                         ?: 'png';
            $safeName = preg_replace('/[^A-Za-z0-9_\-]/', '_', $originalName);
            $filename = time() . '_' . $safeName . '.' . $extension;
           
            // Store in a guardian-specific directory with dependent identifier
            $directory = "temp/guardians/{$guardianId}/dependents/{$dependentIdentifier}/profile_picture";
           
            // Ensure directory exists
            Storage::disk('public')->makeDirectory($directory);

            // Ensure only one temp image per dependent
            Storage::disk('public')->deleteDirectory($directory);
            
            // Create directory again after deletion
            Storage::disk('public')->makeDirectory($directory);
            
            // Handle Windows path issues by using forward slashes
            $directory = str_replace('\\', '/', $directory);
            
            // Store temporarily with public visibility
            $path = $file->storePubliclyAs($directory, $filename, ['disk' => 'public']);
            
            if (!$path) {
                Log::error('Failed to store file', [
                    'directory' => $directory,
                    'filename' => $filename,
                    'original_name' => $file->getClientOriginalName()
                ]);
                throw new \Exception('Failed to store file');
            }
            
            return $path;
        } catch (\Exception $e) {
            Log::error('Failed to store temporary dependent image', [
                'exception' => $e->getMessage(),
                'guardian' => $guardianUser instanceof \App\User ? $guardianUser->id : $guardianUser,
                'dependentId' => $dependentIdentifier,
                'file' => $file->getClientOriginalName()
            ]);
            throw $e;
        }
    }

    /**
     * Move all temporary images for a user into the student's final directory.
     *
     * @param int $userId
     * @param int $studentId
     * @return void
     */
    public function moveTemporaryImages(int $userId, int $studentId): void
    {
        // Log start of the operation
        Log::info('moveTemporaryImages started', [
            'userId'    => $userId,
            'studentId' => $studentId,
        ]);

        $tempDir = "temp/users/{$userId}/profile_picture";
        $files = Storage::disk('public')->files($tempDir);

        foreach ($files as $tempPath) {
            $filename  = basename($tempPath);
            $finalDir  = "students/{$studentId}/profile_picture";
            $finalPath = "{$finalDir}/{$filename}";
            
            // Ensure final directory exists and move file
            Storage::disk('public')->makeDirectory($finalDir);
            Storage::disk('public')->move($tempPath, $finalPath);

            // Update student record
            $student = \App\Student::findOrFail($studentId);
            $student->student_photo = $finalPath;
            $student->save();
        }

        // Optionally clean up the temp directory
        Storage::disk('public')->deleteDirectory($tempDir);
    }
    
    /**
     * Move temporary dependent images uploaded by a guardian to the new student's directory.
     *
     * @param int $guardianId Guardian user ID who uploaded the images
     * @param string $dependentIdentifier The unique identifier used during temporary storage
     * @param int $studentId The newly created student's ID
     * @return void
     */
    public function moveDependentTemporaryImages(int $guardianId, string $dependentIdentifier, int $studentId): void
    {
        // Log start of the operation
        Log::info('moveDependentTemporaryImages started', [
            'guardianId' => $guardianId,
            'dependentIdentifier' => $dependentIdentifier,
            'studentId' => $studentId,
        ]);

        // Ensure studentId is valid
        if (empty($studentId)) {
            Log::error('Student ID is empty or invalid', [
                'guardianId' => $guardianId,
                'dependentIdentifier' => $dependentIdentifier,
                'studentId' => $studentId
            ]);
            throw new \InvalidArgumentException('Student ID must be a valid integer');
        }

        $tempDir = "temp/guardians/{$guardianId}/dependents/{$dependentIdentifier}/profile_picture";
        
        // Check if the temp directory exists
        if (!Storage::disk('public')->exists($tempDir)) {
            Log::warning('Temporary directory does not exist', [
                'tempDir' => $tempDir,
                'guardianId' => $guardianId,
                'dependentIdentifier' => $dependentIdentifier
            ]);
            return; // No files to move
        }
        
        $files = Storage::disk('public')->files($tempDir);
        Log::info('Found files in temporary directory', [
            'tempDir' => $tempDir, 
            'fileCount' => count($files)
        ]);

        if (empty($files)) {
            Log::warning('No files found in temporary directory', [
                'tempDir' => $tempDir
            ]);
            return; // No files to move
        }

        foreach ($files as $tempPath) {
            $filename = basename($tempPath);
            $finalDir = "students/{$studentId}/profile_picture";
            $finalPath = "{$finalDir}/{$filename}";
            
            try {
                // Ensure final directory exists and move file
                Storage::disk('public')->makeDirectory($finalDir);
                
                if (Storage::disk('public')->exists($tempPath)) {
                    Storage::disk('public')->move($tempPath, $finalPath);
                    Log::info('Moved file from temporary to final location', [
                        'from' => $tempPath,
                        'to' => $finalPath
                    ]);
                    
                    // Update student record
                    try {
                        $student = \App\Student::findOrFail($studentId);
                        $student->student_photo = $finalPath;
                        $student->save();
                        
                        Log::info('Updated student record with new photo path', [
                            'studentId' => $studentId,
                            'photo_path' => $finalPath
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Failed to update student record', [
                            'studentId' => $studentId,
                            'error' => $e->getMessage(),
                            'photo_path' => $finalPath
                        ]);
                        throw $e;
                    }
                } else {
                    Log::warning('Temporary file does not exist', [
                        'tempPath' => $tempPath
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Error moving file or updating student record', [
                    'from' => $tempPath,
                    'to' => $finalPath,
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }
        }

        // Clean up the temp directory
        try {
            Storage::disk('public')->deleteDirectory($tempDir);
            Log::info('Deleted temporary directory', ['tempDir' => $tempDir]);
        } catch (\Exception $e) {
            Log::warning('Failed to delete temporary directory', [
                'tempDir' => $tempDir,
                'error' => $e->getMessage()
            ]);
            // Continue execution even if directory cleanup fails
        }
    }

    /**
     * Check if temporary images exist for a dependent
     *
     * @param int $guardianId Guardian user ID
     * @param string $dependentIdentifier The unique identifier for the dependent
     * @return bool True if temp images exist
     */
    public function hasDependentTemporaryImages(int $guardianId, string $dependentIdentifier): bool
    {
        $tempDir = "temp/guardians/{$guardianId}/dependents/{$dependentIdentifier}/profile_picture";
        $files = Storage::disk('public')->files($tempDir);
        return !empty($files);
    }

    public function getStudentImageUrl($student, ?string $size = null): string
    {
        Log::debug('getStudentImageUrl', [
            'student' => is_object($student) ? $student->id : $student,
            'size'    => $size,
        ]);

        
        
        // 0) handle null student case
        if (is_null($student)) {
            Log::warning('Null student passed to getStudentImageUrl');
            return asset('maleStudentProfilePicture.png'); // Default fallback image
        }

        
        // $student = $student->first()->student;

        
        
       
        // 1) ensure we have a Student model
        try {
            if (! $student instanceof Student) {
                $student = Student::findOrFail($student);
            }
        } catch (\Exception $e) {
            Log::error('Failed to find student', [
                'student_id' => $student,
                'error' => $e->getMessage()
            ]);
            return asset('maleStudentProfilePicture.png'); // Default fallback image
            // INSERT_YOUR_REWRITE_HERE
        }

        
        
        // Bypass any accessor to get the raw stored path
        $path = $student->getRawOriginal('student_photo') ?? '';
       
        // 2) if the DB column has a value, trust it
        if (! empty($path)) {
            Log::debug('→ checking DB & filesystem for student_photo', [
                'student_id'   => $student->id,
                'student_photo'=> $path,
            ]);
            // If the stored path is already a full URL, return it directly
            if (preg_match('/^https?:\\/\\//', $path)) {
                Log::debug('→ serving full URL from DB', ['path' => $path]);
                return $path;
            }
            if (Storage::disk('public')->exists($path) || file_exists(public_path($path))) {

                Log::debug('→ serving student_photo from storage', ['path' => $path]);

                // Convert to a public URL using asset helper
//                 $relativePath = str_replace('public/', '', $path);
               
                return asset($path);
            }

            // fallback to gender-based default placeholder
            Log::warning('StudentImageService@getStudentImageUrl - photo missing in FS, using gender default', [
                'student_id'   => $student->id,
                'checked_path' => $path,
            ]);

            $gender       = strtolower($student->gender ?? '');
            $defaultImage = $gender === 'female'
                ? $this->femaleDefaultImagePath
                : $this->maleDefaultImagePath;

            return asset($defaultImage);
        }

        // 3) no photo in DB → choose gender default in public/
        $gender = strtolower($student->gender ?? '');
        $file   = $gender === 'female'
            ? 'femaleStudentProfilePicture.png'
            : 'maleStudentProfilePicture.png';

        Log::info('→ falling back to gender default', [
            'student_id' => $student->id,
            'gender'     => $gender,
            'file'       => $file,
        ]);

        // asset() will look in your public/ folder
        return asset($file);
    }


    /**
     * Get the full HTML img tag for a student's image
     *
     * @param int|Student $student Student ID or Student model instance
     * @param array $attributes Additional HTML attributes for the img tag
     * @return string HTML img tag
     */
    public function getStudentImageHtml($student, array $attributes = []): string
    {
        $imageUrl = $this->getStudentImageUrl($student);

        // Default attributes
        $defaultAttributes = [
            'class' => 'avatar img-circle img-thumbnail',
            'alt' => 'Student Photo',
            'width' => '100',
            'height' => '100'
        ];

        // Merge with provided attributes (user attributes override defaults)
        $attributes = array_merge($defaultAttributes, $attributes);

        // Build HTML attributes string
        $attributesStr = '';
        foreach ($attributes as $key => $value) {
            $attributesStr .= " {$key}=\"{$value}\"";
        }

        return "<img src=\"{$imageUrl}\"{$attributesStr}>";
    }

    /**
     * Set the default image path
     *
     * @param string $path Path to the default image relative to public directory
     * @return self
     */
    public function setDefaultImagePath(string $path): self
    {
        $this->defaultImagePath = $path;
        return $this;
    }

    /**
     * Get default image path based on student gender, relative to the public directory.
     *
     * @param Student $student Student model instance
     * @return string Path to the default image relative to public directory
     */
    private function getDefaultImagePathByGender(Student $student): string
    {
        $gender = strtolower($student->gender ?? '');
        $defaultPath = '';

        if ($gender === 'male' || $gender === 'm') {
            $defaultPath = $this->maleDefaultImagePath;
        } elseif ($gender === 'female' || $gender === 'f') {
            $defaultPath = $this->femaleDefaultImagePath;
        } else {
            // Fallback to generic default image if gender is not specified or recognized
            $defaultPath = $this->defaultImagePath;
        }

        // Ensure the path exists in the public directory before returning
        if (!$this->fileExists($defaultPath)) {
            Log::warning('Default image path not found in public directory, falling back to generic default', [
                'requested_path' => $defaultPath,
                'fallback_path' => $this->defaultImagePath,
                'student_id' => $student->id
            ]);
            // Check if the generic default exists, otherwise return a placeholder or handle error
            if (!$this->fileExists($this->defaultImagePath)){
                Log::error('Generic default image path also not found in public directory', [
                    'path' => $this->defaultImagePath
                ]);
                // Optionally return a very basic placeholder path or throw an exception
                return 'placeholder.png'; // Final fallback if even avatar.jpg is missing
            }
            return $this->defaultImagePath;
        }

        return $defaultPath;
    }

    /**
     * Check if a file exists, considering storage path conventions.
     *
     * @param string $path File path (e.g., 'public/uploads/...' or 'avatar.jpg')
     * @return bool
     */
    private function fileExists(string $path): bool
    {
        try {
            // Handle paths intended for public storage (e.g., 'public/uploads/student/...')
            if (strpos($path, 'public/') === 0) {
                // Storage::exists expects path relative to the disk root (storage/app/public)
                $storagePath = str_replace('public/', '', $path);
                $exists = Storage::disk('public')->exists($storagePath);
                Log::debug('Checking file existence in public storage', ['original_path' => $path, 'storage_path' => $storagePath, 'exists' => $exists]);
                return $exists;
            }

            // Handle paths assumed to be directly in the public directory (e.g., 'avatar.jpg' or 'maleStudentProfilePicture.png')
            $publicPath = public_path($path);
            $exists = File::exists($publicPath);
            Log::debug('Checking file existence in public directory', ['path' => $path, 'public_path' => $publicPath, 'exists' => $exists]);
            return $exists;
        } catch (\Exception $e) {
            Log::error('Error checking file existence', [
                'path' => $path,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Check if a student has a custom uploaded image (not a default placeholder)
     *
     * @param int|Student $student Student ID or Student model instance
     * @return bool True if the student has a custom image, false if using a default
     */
    public function hasCustomImage($student): bool
    {
        try {
            Log::info('Checking if student has custom image', [
                'student_id' => is_object($student) ? $student->id : $student
            ]);
            
            // Ensure we have a Student model
            if (!$student instanceof Student) {
                $student = Student::findOrFail($student);
            }
            
            // Get the raw photo path from DB without accessors
            $photoPath = $student->getRawOriginal('student_photo') ?? '';
            
            // If no path stored in the database, definitely using default
            if (empty($photoPath)) {
                Log::info('Student has no photo path in database', [
                    'student_id' => $student->id
                ]);
                return false;
            }
            
            Log::info('Found photo path in database', [
                'student_id' => $student->id,
                'photo_path' => $photoPath
            ]);
            
            // Check if the file exists in the filesystem
            $fileExists = false;
            
            // If it's already a full URL, assume it exists (external storage)
            if (preg_match('/^https?:\/\//', $photoPath)) {
                Log::info('Photo path is a URL, assuming it exists', [
                    'student_id' => $student->id,
                    'photo_path' => $photoPath
                ]);
                return true;
            }
            
            // Handle different path formats
            if (strpos($photoPath, 'public/') === 0) {
                // Remove 'public/' prefix for storage checks
                $storagePath = str_replace('public/', '', $photoPath);
                $fileExists = Storage::disk('public')->exists($storagePath);
                
                Log::info('Checked storage path existence (public/ format)', [
                    'student_id' => $student->id,
                    'original_path' => $photoPath,
                    'storage_path' => $storagePath,
                    'exists' => $fileExists
                ]);
            } else {
                // Try both direct storage and public directory
                $fileExists = Storage::disk('public')->exists($photoPath) || 
                              File::exists(public_path($photoPath));
                
                Log::info('Checked storage path existence (direct format)', [
                    'student_id' => $student->id,
                    'path' => $photoPath,
                    'exists' => $fileExists
                ]);
            }
            
            // If we have a value in DB AND the file exists in filesystem, return true
            return $fileExists;
            
        } catch (\Exception $e) {
            Log::error('Error checking if student has custom image', [
                'student_id' => is_object($student) ? $student->id : $student,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Perform a detailed diagnostic check on a student's image
     * Returns an associative array with diagnostic information
     *
     * @param int|Student $student Student ID or Student model instance
     * @return array Diagnostic information
     */
    public function checkImageDiagnostics($student): array
    {
        try {
            // Ensure we have a Student model
            if (!$student instanceof Student) {
                $student = Student::findOrFail($student);
            }
            
            // Get the raw photo path from DB without accessors
            $photoPath = $student->getRawOriginal('student_photo') ?? '';
            
            $result = [
                'student_id' => $student->id,
                'has_db_value' => !empty($photoPath),
                'db_value' => $photoPath,
                'exists_in_filesystem' => false,
                'path_checked' => [],
                'is_default_image' => false,
                'final_result' => false
            ];
            
            // If no DB value, return early
            if (empty($photoPath)) {
                $result['is_default_image'] = true;
                return $result;
            }
            
            // Is it a full URL?
            $isUrl = preg_match('/^https?:\/\//', $photoPath);
            $result['is_url'] = $isUrl;
            
            if ($isUrl) {
                // We'll assume URLs are valid
                $result['exists_in_filesystem'] = true;
                $result['path_checked'][] = ['path' => $photoPath, 'exists' => true, 'note' => 'URL assumed valid'];
                $result['final_result'] = true;
                return $result;
            }
            
            // Check for public/ prefix
            if (strpos($photoPath, 'public/') === 0) {
                $storagePath = str_replace('public/', '', $photoPath);
                $exists = Storage::disk('public')->exists($storagePath);
                $result['path_checked'][] = [
                    'path' => $storagePath, 
                    'type' => 'storage',
                    'exists' => $exists
                ];
                
                if ($exists) {
                    $result['exists_in_filesystem'] = true;
                }
            }
            
            // Try direct storage path
            $storageExists = Storage::disk('public')->exists($photoPath);
            $result['path_checked'][] = [
                'path' => $photoPath, 
                'type' => 'storage_direct',
                'exists' => $storageExists
            ];
            
            if ($storageExists) {
                $result['exists_in_filesystem'] = true;
            }
            
            // Try public path
            $publicPath = public_path($photoPath);
            $publicExists = File::exists($publicPath);
            $result['path_checked'][] = [
                'path' => $publicPath, 
                'type' => 'public_path',
                'exists' => $publicExists
            ];
            
            if ($publicExists) {
                $result['exists_in_filesystem'] = true;
            }
            
            // Final result: do we have both a DB value AND a file?
            $result['final_result'] = $result['has_db_value'] && $result['exists_in_filesystem'];
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('Error during image diagnostics', [
                'student_id' => is_object($student) ? $student->id : $student,
                'error' => $e->getMessage()
            ]);
            
            return [
                'student_id' => is_object($student) ? $student->id : $student,
                'error' => $e->getMessage(),
                'final_result' => false
            ];
        }
    }
}
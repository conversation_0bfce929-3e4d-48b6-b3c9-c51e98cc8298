!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=tinymce.util.Tools.resolve("tinymce.util.Tools"),n=function(e){e.settings.inline_styles=!1,e.on("init",function(){var n,i,a,o;n=e,i="p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img",a=t.explode(n.settings.font_size_style_values),o=n.schema,n.formatter.register({alignleft:{selector:i,attributes:{align:"left"}},aligncenter:{selector:i,attributes:{align:"center"}},alignright:{selector:i,attributes:{align:"right"}},alignjustify:{selector:i,attributes:{align:"justify"}},bold:[{inline:"b",remove:"all"},{inline:"strong",remove:"all"},{inline:"span",styles:{fontWeight:"bold"}}],italic:[{inline:"i",remove:"all"},{inline:"em",remove:"all"},{inline:"span",styles:{fontStyle:"italic"}}],underline:[{inline:"u",remove:"all"},{inline:"span",styles:{textDecoration:"underline"},exact:!0}],strikethrough:[{inline:"strike",remove:"all"},{inline:"span",styles:{textDecoration:"line-through"},exact:!0}],fontname:{inline:"font",attributes:{face:"%value"}},fontsize:{inline:"font",attributes:{size:function(e){return t.inArray(a,e.value)+1}}},forecolor:{inline:"font",attributes:{color:"%value"}},hilitecolor:{inline:"font",styles:{backgroundColor:"%value"}}}),t.each("b,i,u,strike".split(","),function(e){o.addValidElements(e+"[*]")}),o.getElementRule("font")||o.addValidElements("font[face|size|color|style]"),t.each(i.split(","),function(e){var t=o.getElementRule(e);t&&(t.attributes.align||(t.attributes.align={},t.attributesOrder.push("align")))})})},i=function(e){e.addButton("fontsizeselect",function(){var t=[],n=e.settings.fontsizeFormats||"8pt=1 10pt=2 12pt=3 14pt=4 18pt=5 24pt=6 36pt=7";return e.$.each(n.split(" "),function(e,n){var i=n,a=n,o=n.split("=");o.length>1&&(i=o[0],a=o[1]),t.push({text:i,value:a})}),{type:"listbox",text:"Font Sizes",tooltip:"Font Sizes",values:t,fixedWidth:!0,onPostRender:function(){var t=this;e.on("NodeChange",function(){var n;(n=e.dom.getParent(e.selection.getNode(),"font"))?t.value(n.size):t.value("")})},onclick:function(t){t.control.settings.value&&e.execCommand("FontSize",!1,t.control.settings.value)}}}),e.addButton("fontselect",function(){var t=[],n=function(e){for(var t=(e=e.replace(/;$/,"").split(";")).length;t--;)e[t]=e[t].split("=");return e}(e.settings.font_formats||"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats");return e.$.each(n,function(e,n){t.push({text:{raw:n[0]},value:n[1],textStyle:-1===n[1].indexOf("dings")?"font-family:"+n[1]:""})}),{type:"listbox",text:"Font Family",tooltip:"Font Family",values:t,fixedWidth:!0,onPostRender:function(){var t=this;e.on("NodeChange",function(){var n;(n=e.dom.getParent(e.selection.getNode(),"font"))?t.value(n.face):t.value("")})},onselect:function(t){t.control.settings.value&&e.execCommand("FontName",!1,t.control.settings.value)}}})};e.add("legacyoutput",function(e){n(e),i(e)})}();
<?php

namespace Modules\HumanResource\Http\Controllers;

use App\ApiBaseMethod;
use App\Attendance;
use App\Center;
use App\CenterEmployee;
use App\CenterTranslation;
use App\LeaveRequest;
use App\Role;
use App\Employee;

use App\Document;
use App\YearCheck;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use DB;
use App\Permission;
use App\EmployeeSalary;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\EmployeeTimetable;
use App\Http\Controllers\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Khsing\World\Models\Country;
use Modules\Admission\Notifications\StudentOfffered;
use Modules\HumanResource\Http\Requests\EmployeeStoreRequest;
use Modules\HumanResource\Http\Requests\EmployeeUpdateRequest;


class CountryStateCityController extends Controller
{

    public function index()
    {
        $data['countries'] = Country::get(["name","id"]);
        return view('country-state-city',$data);
    }
    public function getState(Request $request)
    {
        $country = Country::getByCode($request->country_id);

        $data['states'] = $country->children();
        return response()->json($data);
    }
    public function getCity(Request $request)
    {
        $data['cities'] = City::where("state_id",$request->state_id)
            ->get(["name","id"]);
        return response()->json($data);
    }
}

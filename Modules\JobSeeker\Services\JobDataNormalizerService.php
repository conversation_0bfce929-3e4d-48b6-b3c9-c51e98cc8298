<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

/**
 * Service class responsible for normalizing key job attributes.
 * Used to create consistent data for comparison and de-duplication.
 */
final class JobDataNormalizerService
{
    /**
     * Normalize company name for consistent comparison.
     * 
     * @param string $companyName The raw company name to normalize
     * @return string The normalized company name
     */
    public static function normalizeCompanyName(string $companyName): string
    {
        // Convert to lowercase
        $normalized = strtolower(trim($companyName));
        
        // Remove common company suffixes (with and without periods)
        $suffixes = [
            'limited', 'ltd', 'ltd.',
            'corporation', 'corp', 'corp.',
            'incorporated', 'inc', 'inc.',
            'company', 'co', 'co.',
            'llc', 'l.l.c',
            'plc', 'p.l.c',
            'pty', 'pty.',
            'private limited', 'pvt ltd', 'pvt. ltd.',
            'public limited company', 'plc',
        ];
        
        foreach ($suffixes as $suffix) {
            // Remove suffix if it appears at the end of the company name
            $pattern = '/\s+' . preg_quote($suffix, '/') . '$/i';
            $normalized = preg_replace($pattern, '', $normalized);
        }
        
        // Remove excessive whitespace and trim
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        
        return trim($normalized);
    }
    
    /**
     * Normalize job title for consistent comparison.
     * 
     * @param string $jobTitle The raw job title to normalize
     * @return string The normalized job title
     */
    public static function normalizeJobTitle(string $jobTitle): string
    {
        // Convert to lowercase
        $normalized = strtolower(trim($jobTitle));
        
        // Standardize common abbreviations
        $abbreviations = [
            // Senior/Junior abbreviations
            '/\bsr\.?\s+/' => 'senior ',
            '/\bjr\.?\s+/' => 'junior ',
            '/\bjnr\.?\s+/' => 'junior ',
            
            // Developer abbreviations
            '/\bdev\b/' => 'developer',
            '/\bdeveloper\s*\/\s*programmer\b/' => 'developer',
            '/\bprogrammer\s*\/\s*developer\b/' => 'developer',
            
            // Manager abbreviations
            '/\bmgr\.?\b/' => 'manager',
            '/\bmngr\.?\b/' => 'manager',
            
            // Assistant abbreviations
            '/\basst\.?\b/' => 'assistant',
            '/\bassistant\s*manager\b/' => 'assistant manager',
            
            // Engineer abbreviations
            '/\bengr\.?\b/' => 'engineer',
            
            // Administrator abbreviations
            '/\badmin\.?\b/' => 'administrator',
            
            // Specialist abbreviations
            '/\bspec\.?\b/' => 'specialist',
            
            // Coordinator abbreviations
            '/\bcoord\.?\b/' => 'coordinator',
            
            // Executive abbreviations
            '/\bexec\.?\b/' => 'executive',
            
            // Information Technology
            '/\bi\.?t\.?\b/' => 'it',
            '/\binformation\s+technology\b/' => 'it',
            
            // Human Resources
            '/\bh\.?r\.?\b/' => 'hr',
            '/\bhuman\s+resources?\b/' => 'hr',
            
            // Quality Assurance
            '/\bq\.?a\.?\b/' => 'qa',
            '/\bquality\s+assurance\b/' => 'qa',
        ];
        
        foreach ($abbreviations as $pattern => $replacement) {
            $normalized = preg_replace($pattern, $replacement, $normalized);
        }
        
        // Remove excessive punctuation (except essential ones like '+' in 'c++')
        // Keep alphanumeric, spaces, essential symbols like +, #, and single hyphens
        $normalized = preg_replace('/[^\w\s\+\#\-]/', ' ', $normalized);
        
        // Remove multiple consecutive punctuation marks but preserve c++, c#, etc.
        $normalized = preg_replace('/(?<!\+)\-+(?!\+)/', '-', $normalized);
        
        // Remove excessive whitespace
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        
        return trim($normalized);
    }
    
    /**
     * Normalize location for consistent comparison.
     * 
     * @param string $location The raw location to normalize
     * @return string The normalized location
     */
    public static function normalizeLocation(string $location): string
    {
        // Convert to lowercase and trim whitespace
        $normalized = strtolower(trim($location));
        
        // Remove excessive whitespace
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        
        // Standardize common location abbreviations and variations
        $locationMappings = [
            // Country variations
            '/\bafghanistan\b/' => 'afghanistan',
            '/\bafg\b/' => 'afghanistan',
            
            // City variations for Afghanistan
            '/\bkabul?\b/' => 'kabul',
            '/\bherat\b/' => 'herat',
            '/\bmazar.?i.?sharif\b/' => 'mazar-i-sharif',
            '/\bmazar\b/' => 'mazar-i-sharif',
            '/\bkandahar\b/' => 'kandahar',
            '/\bjalal.?abad\b/' => 'jalalabad',
            '/\bkunduz\b/' => 'kunduz',
            '/\bbamyan\b/' => 'bamyan',
            '/\bghazni\b/' => 'ghazni',
            
            // Remove common location prefixes/suffixes
            '/\bcity\s*$/' => '',
            '/\bprovince\s*$/' => '',
            '/\bdistrict\s*$/' => '',
            '/\barea\s*$/' => '',
        ];
        
        foreach ($locationMappings as $pattern => $replacement) {
            $normalized = preg_replace($pattern, $replacement, $normalized);
        }
        
        // Remove excessive whitespace again after replacements
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        
        return trim($normalized);
    }
} 
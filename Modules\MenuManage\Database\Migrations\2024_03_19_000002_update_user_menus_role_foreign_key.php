<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateUserMenusRoleForeignKey extends Migration
{
    public function up()
    {
        Schema::table('user_menus', function (Blueprint $table) {
            // Drop the existing foreign key
            $table->dropForeign(['role_id']);
            
            // Add new foreign key referencing Spatie roles table
            $table->foreign('role_id')
                  ->references('id')
                  ->on('roles')
                  ->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::table('user_menus', function (Blueprint $table) {
            // Drop the new foreign key
            $table->dropForeign(['role_id']);
            
            // Restore original foreign key
            $table->foreign('role_id')
                  ->references('id')
                  ->on('infix_roles')
                  ->onDelete('cascade');
        });
    }
} 
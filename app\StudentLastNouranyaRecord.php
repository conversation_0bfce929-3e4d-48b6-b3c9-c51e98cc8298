<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentLastNouranyaRecord extends Model
{
    use HasFactory,SoftDeletes;
    protected $table = 'student_last_nouranya_record';

    protected $fillable = [
        'student_id',
        'nouranya_year_month_day',
        'from_lesson',
        'to_lesson',
        'talaqqi_from_lesson',
        'talqeen_from_lesson',
        'talaqqi_to_lesson',
        'talqeen_to_lesson',
        'level_id',
        'approved_by',
        'updated_at',
        'class_id',
        'organization_id',
        'plan_year_and_month',
        'start_date',
        'end_date',
        'center_id',
        'status',
        'supervisor_comment',
        'created_by',
        'updated_by',
        'delete_reason',
        'deleted_at',
        'talqeen_talaqqi',
        'from_lesson_line_number',
        'to_lesson_line_number',
        'plan_year_month_day'
    ];

    /**
     * Get the student that owns the plan.
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }


}

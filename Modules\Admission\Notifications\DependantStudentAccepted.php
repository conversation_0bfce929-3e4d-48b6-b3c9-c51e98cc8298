<?php

namespace Modules\Admission\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class DependantStudentAccepted extends Mailable
{
    use Queueable, SerializesModels;

    public $userInfo;
    public $sender;
    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($userInfo, $sender)
    {

        $this->userInfo = $userInfo;
        $this->sender = $sender;
        //
    }





    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('You are accepted to the ' . $this->userInfo[0]['programTitle'] . ' Program')
            ->view('modules.site.templates.wajeha.backEnd.studentInformation.dependant_student_acceptance_confirmation', ['data' => $this->userInfo[0]]);
    }


}

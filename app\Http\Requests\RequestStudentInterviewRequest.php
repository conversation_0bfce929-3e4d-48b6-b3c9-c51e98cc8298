<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class RequestStudentInterviewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    /**
     * @return array
     */
    public function rules()
    {


        return [
            'admission_id' => [
                'required',
                function ($attribute, $value, $fail) {
                    $admission = DB::table('admissions')->where('id', $value)->first();

                    if (!$admission) {
                        $fail('The selected admission ID is invalid.');
                    } elseif (is_null($admission->program_id) || $admission->program_id === '') {
                        $fail('Please assign the student to a program.');
                    } elseif (is_null($admission->center_id) || $admission->center_id === '') {
                        $fail('The student is not attached to a center');
                    } elseif (is_null($admission->class_id) || $admission->class_id === '') {
                        $fail('The student should be assigned to a class.');
                    }
                }
            ],
//            'interview.*.approve' => 'required',
            'interview.*.interview_time' => function ($attribute, $value, $fail) {

                if ((is_null($value) || $value === '')) {
                    $fail('Please specify the interview time.');
                }
            },
            'interview.*.location' => function ($attribute, $value, $fail) {

                if ((is_null($value) || $value === '')) {
                    $fail('Please specify the interview location.');
                }
            },
            'interview.*.committee' => function ($attribute, $value, $fail) {
                $interviewIndex = explode('.', $attribute)[1];
                $approve = $this->input('interview.'.$interviewIndex.'.approve');
                if ($approve != '0' && (is_null($value) || $value === '')) {
                    $fail('Please select at least one interviewer.');
                }
            },
        ];
    }


    public function messages()
    {
        return [
            'programId.required' => 'Please assign a program to the student',
            'centerId.required' => 'Please select a center to the student',
            'classId.required' => 'Please select a class to the student',
            'interview.*.location.required' => 'Please specify the interview location',
            'interview.*.interview_time.required' => 'Please specify the interview time',
            'interview.*.committee.required' => 'Please select at least one interviewer',


        ];

    }

    public function attributes()
    {
        return [
            'programId' => 'Program ID',
            'centerId' => 'Center ID',
            'classId' => 'Class ID',
            'interview.*.interview_time' => 'Interview Time',
            'interview.*.location' => 'Interview Location',
            'interview.*.committee' => 'Interview Committee',

        ];
    }
}

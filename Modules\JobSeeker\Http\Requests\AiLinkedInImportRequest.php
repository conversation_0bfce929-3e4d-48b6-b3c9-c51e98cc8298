<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Mo<PERSON>les\JobSeeker\Entities\JobSeekerResume;

/**
 * AiLinkedInImportRequest validates LinkedIn profile imports for AI resume tailoring.
 * 
 * Purpose: Validate pasted LinkedIn profile text with title and content sanitization.
 * Business rules: Text content required; reasonable length limits; HTML sanitization.
 * Security: Prevents XSS via HTML purification; enforces user limits; validates content.
 */
final class AiLinkedInImportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request
     */
    public function authorize(): bool
    {
        // User must be authenticated as job_seeker and have manage_resumes permission
        return auth('job_seeker')->check() && 
               auth('job_seeker')->user()->can('jobseeker.manage_resumes');
    }

    /**
     * Get the validation rules that apply to the request
     */
    public function rules(): array
    {
        return [
            'title' => [
                'required',
                'string',
                'min:3',
                'max:150',
                'regex:/^[a-zA-Z0-9\s\-_().]+$/', // Alphanumeric with common punctuation
            ],
            'content_text' => [
                'required',
                'string',
                'min:100', // Minimum meaningful content
                'max:50000', // Reasonable upper limit for LinkedIn profiles
            ],
            'linkedin_url' => [
                'nullable',
                'string',
                'max:500',
                'regex:/^https?:\/\/(www\.)?linkedin\.com\/.*/', // Optional LinkedIn URL for reference
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Please provide a title for your LinkedIn resume.',
            'title.min' => 'Resume title must be at least 3 characters long.',
            'title.max' => 'Resume title cannot exceed 150 characters.',
            'title.regex' => 'Resume title contains invalid characters. Use only letters, numbers, and common punctuation.',
            'content_text.required' => 'Please paste your LinkedIn profile content.',
            'content_text.min' => 'LinkedIn content must be at least 100 characters long to be meaningful.',
            'content_text.max' => 'LinkedIn content is too long. Please summarize to under 50,000 characters.',
            'linkedin_url.regex' => 'Please provide a valid LinkedIn profile URL.',
            'linkedin_url.max' => 'LinkedIn URL is too long.',
        ];
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation(): void
    {
        // Clean up the content text
        if ($this->has('content_text')) {
            $cleanContent = $this->input('content_text');
            
            // Remove excessive whitespace and normalize line breaks
            $cleanContent = preg_replace('/\s+/', ' ', $cleanContent);
            $cleanContent = preg_replace('/\n\s*\n/', "\n\n", $cleanContent);
            $cleanContent = trim($cleanContent);
            
            $this->merge([
                'content_text' => $cleanContent,
            ]);
        }

        // Clean up LinkedIn URL
        if ($this->has('linkedin_url') && !empty($this->input('linkedin_url'))) {
            $url = trim($this->input('linkedin_url'));
            
            // Add https:// if missing
            if (!preg_match('/^https?:\/\//', $url)) {
                $url = 'https://' . $url;
            }
            
            $this->merge([
                'linkedin_url' => $url,
            ]);
        }
    }

    /**
     * Configure the validator instance
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if user has reached the 5-resume limit
            $currentCount = JobSeekerResume::countForJobSeeker(auth('job_seeker')->id());
            
            if ($currentCount >= 5) {
                $validator->errors()->add('content_text', 
                    'You can only store up to 5 resumes. Please delete an existing resume before importing from LinkedIn.');
            }

            // Validate content quality
            if ($this->has('content_text')) {
                $content = $this->input('content_text');
                
                // Check for suspiciously repetitive content
                $words = str_word_count($content, 1);
                if (count($words) > 10) {
                    $wordFreq = array_count_values(array_map('strtolower', $words));
                    $maxFreq = max($wordFreq);
                    $avgFreq = count($words) / count($wordFreq);
                    
                    // If any word appears more than 30% of the time, it might be spam
                    if ($maxFreq > count($words) * 0.3) {
                        $validator->errors()->add('content_text', 
                            'Content appears to be repetitive or invalid. Please paste your actual LinkedIn profile.');
                    }
                }

                // Check for minimum word count (meaningful content)
                if (str_word_count($content) < 20) {
                    $validator->errors()->add('content_text', 
                        'Content is too short. Please provide your complete LinkedIn profile information.');
                }

                // Check for potentially malicious content patterns
                $suspiciousPatterns = [
                    '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
                    '/<iframe\b[^>]*>/mi',
                    '/javascript:/mi',
                    '/data:text\/html/mi',
                ];

                foreach ($suspiciousPatterns as $pattern) {
                    if (preg_match($pattern, $content)) {
                        $validator->errors()->add('content_text', 
                            'Content contains potentially harmful elements. Please paste plain text only.');
                        break;
                    }
                }
            }
        });
    }

    /**
     * Get custom attributes for error messages
     */
    public function attributes(): array
    {
        return [
            'title' => 'resume title',
            'content_text' => 'LinkedIn content',
            'linkedin_url' => 'LinkedIn URL',
        ];
    }

    /**
     * Handle a failed validation attempt
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        // Log validation failures for monitoring (without logging sensitive content)
        \Illuminate\Support\Facades\Log::info('LinkedIn import validation failed', [
            'user_id' => auth('job_seeker')->id(),
            'errors' => $validator->errors()->toArray(),
            'content_length' => $this->has('content_text') ? strlen($this->input('content_text')) : 0,
            'has_url' => $this->has('linkedin_url') && !empty($this->input('linkedin_url')),
        ]);

        parent::failedValidation($validator);
    }
}

@extends('platform::layouts.master')

@section('content')


<div class="container ">
  
  <p><br></p>
{{--  <div class="stepwizard col-md-offset-3">
    <div class="stepwizard-row setup-panel">
      <div class="stepwizard-step">
        <a href="#step-1" type="button" class="btn btn-circle
        @if(Auth::user()->username && Auth::user()->logo && Auth::user()->languages)
        btn-success
        @else
        btn-primary
        @endif
        ">1</a>
        <p>Step 1</p>
      </div>
      <div class="stepwizard-step">
        <a href="#step-2" type="button" class="btn btn-default btn-circle" disabled="disabled">2</a>
        <p>Step 2</p>
      </div>
      <div class="stepwizard-step">
        <a href="#step-3" type="button" class="btn btn-default btn-circle" disabled="disabled">3</a>
        <p>Step 3</p>
      </div>
    </div>
  </div>  --}}

    @if(!Auth::user()->username || !Auth::user()->logo || !Auth::user()->languages)
  <h1 class="form-header">{{ trans('platform::platform.complete_registeration') }}</h1>
    
    @if($errors->all())
        <ul class="alert alert-danger">
        @foreach ($errors->all() as $error)
            <li>{{ $error }}</li>
        @endforeach
        </ul> 
    @endif
    <div class="row" id="step-1">
      <div class="col-xs-6 col-md-offset-3">
        <div class="col-md-12">
          <h3> Step 1</h3>
            <form action="{{ url(config('app.locale')) }}/home" method="POST" role="form"  enctype="multipart/form-data">
                {{ csrf_field() }}
                <legend>{{ trans('platform::platform.domain') }}</legend>
            
                <div style="direction: ltr">
                    <div class="input-group">
                        <span class="input-group-addon">https://</span>

                        <input type="text" name="username" class="form-control subdomain-input" placeholder="your-sub-domain" aria-describedby="basic-addon2">
                        <span class="input-group-addon" id="basic-addon2">.{{ config('app.platform_domain') }}</span>
                    </div>
                </div>
                <br>
            
                <legend>{{ trans('platform::platform.organization_logo') }}</legend>
            
                <div class="input-group col-md-12">
                    <div class="col-sm-4">
                        <input id="FileUpload" name="logo" type="file" name="ImageUpload" class="image" />
                    </div>
                    <div class="col-sm-8">
                        <div id="PreviewPicture" style="background:no-repeat;background-size: contain; height:100px; width:100%">
                        </div>
                    </div>
                </div>

                <br>

                <legend>{{ trans('platform::platform.system_languages') }}</legend>
                @foreach (config('app.supported_locales') as $code )
                    <div class="col-sm-4 col-md-3">
                    <label>
                        <input type="checkbox" name="languages[]" value="{{$code}}">
                        {{get_language_name($code)}}
                    </label>
                    </div>
                @endforeach
                <input type="hidden" value="1" name="org">

                <div class="row">
                    <div class="text-center">
                        <button type="submit" class="btn btn-danger">{{ trans('common.save') }}</button>
                        
                    </div>
                </div>
                <br>
            </form>
        </div>
      </div>
    </div>
    @section('js')
    <script>
     $(function() {
        $("#FileUpload").on("change", function(){
            var files = !!this.files ? this.files : [];
            if (!files.length || !window.FileReader) return; // Check if File is selected, or no FileReader support
            if (/^image/.test( files[0].type)){ //  Allow only image upload
                var ReaderObj = new FileReader(); // Create instance of the FileReader
                ReaderObj.readAsDataURL(files[0]); // read the file uploaded
                ReaderObj.onloadend = function(){ // set uploaded image data as background of div
                    $("#PreviewPicture").css("background-image", "url("+this.result+")");
                }
            }else{
                alert("Upload an image");
            }
        });
    });
    </script>

    @append
    @elseif (Auth::user()->status == 'admin_details_needed')
    <h1 class="form-header">{{ trans('platform::platform.complete_registeration') }}</h1>
    @if($errors->all())
        <ul class="alert alert-danger">
        @foreach ($errors->all() as $error)
            <li>{{ $error }}</li>
        @endforeach
        </ul> 
    @endif

    <form class="form-horizontal" action="{{ url(config('app.locale')) }}/home" method="POST" role="form">
    
    <h2 class="text-center">Choose Package</h2>
            <div class="row">
          @foreach($packages as $package)
          <div class="col-sm-3">
            <div class="single-table featured wow flipInY" data-wow-duration="1000ms" data-wow-delay="300ms">
            <label style="width:100%">
              <h3>
              {!! Form::radio('package' , $package->id , null) !!} 
              {{ $package->title }}</h3>
              <div class="price">
                {!! $package->monthly_price > 0 ? "$".$package->monthly_price."<small>/Month </small>"  : 'Free' !!}
              </div>
              <p>
              {{$package->description}}
              </p>
              {{--  <ul>
                <li>Free Setup</li>
                <li>10 Pages</li>
                <li>Student Registeration Form</li>
                <li>Online Donation Gateway</li>
              </ul>  --}}
            </label>
              
            </div>
          </div>
          @endforeach
        </div>
    
    <h2 class="text-center">Register Admin Details</h2>
    
                        {{ csrf_field() }}
                        <input type="hidden" value="1" name="admin">

                        <div class="form-group{{ $errors->has('admin_name') ? ' has-error' : '' }}">
                            <label for="admin_name" class="col-md-4 control-label">{{ trans('platform::platform.admin_name') }}</label>

                            <div class="col-md-6">
                                <input id="admin_name" type="text" class="form-control" name="admin_name" value="{{ old('admin_name') }}" autofocus>

                                @if ($errors->has('admin_name'))
                                    <span class="help-block">
                                        <strong>{{ $errors->first('admin_name') }}</strong>
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="form-group{{ $errors->has('admin_email') ? ' has-error' : '' }}">
                            <label for="admin_email" class="col-md-4 control-label">{{ trans('platform::platform.admin_email') }}</label>

                            <div class="col-md-6">
                                <input id="admin_email" type="admin_email" class="form-control" name="admin_email" value="{{ Auth::user()->email }}">

                                @if ($errors->has('admin_email'))
                                    <span class="help-block">
                                        <strong>{{ $errors->first('admin_email') }}</strong>
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="form-group{{ $errors->has('password') ? ' has-error' : '' }}">
                            <label for="password" class="col-md-4 control-label">{{ trans('platform::platform.password') }}</label>

                            <div class="col-md-6">
                                <input id="password" type="password" class="form-control" name="password">

                                @if ($errors->has('password'))
                                    <span class="help-block">
                                        <strong>{{ $errors->first('password') }}</strong>
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="form-group{{ $errors->has('password_confirmation') ? ' has-error' : '' }}">
                            <label for="password-confirm" class="col-md-4 control-label">{{ trans('platform::platform.confirm_password') }}</label>

                            <div class="col-md-6">
                                <input id="password-confirm" type="password" class="form-control" name="password_confirmation">

                                @if ($errors->has('password_confirmation'))
                                    <span class="help-block">
                                        <strong>{{ $errors->first('password_confirmation') }}</strong>
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-md-6 col-md-offset-4">
                                <button type="submit" class="btn btn-primary">
                                    {{ trans('platform::platform.complete_registeration') }}
                                </button>
                            </div>
                        </div>
                    </form>
    {{--  <div class="row " id="step-2">
        <div id="pricing">
            <div class="container">
            <div class="row">
                <div class="heading text-center col-sm-8 col-sm-offset-2 ">
                <h2>{{ trans('platform::platform.pricing') }}</h2>
                </div>
            </div>
            <div class="pricing-table">
                <div class="row">
                <div class="col-sm-3">
                    <div class="single-table ">
                    <h3>Basic</h3>
                    <div class="price">
                        Free
                    </div>
                    <ul>
                        <li>Free Setup</li>
                        <li>10 Pages</li>
                        <li>Student Registeration Form</li>
                        <li>Online Donation Gateway</li>
                    </ul>
                    <a href="#" class="btn btn-lg btn-primary">{{ trans('common.select') }}</a>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="single-table ">
                    <h3>Standard</h3>
                    <div class="price">
                        $19<span>/Month</span>                                
                    </div>
                    <ul>
                        <li>Free Setup</li>
                        <li>10GB Storage</li>
                        <li>100GB Bandwith</li>
                        <li>5 Products</li>
                    </ul>
                      <a href="#" class="btn btn-lg btn-primary" disable="disable">{{ trans('common.select') }}</a>  
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="single-table featured ">
                    <h3>Featured</h3>
                    <div class="price">
                        $29<span>/Month</span>                                
                    </div>
                    <ul>
                        <li>Free Setup</li>
                        <li>10GB Storage</li>
                        <li>100GB Bandwith</li>
                        <li>5 Products</li>
                    </ul>
                      <a href="#" class="btn btn-lg btn-primary">{{ trans('common.select') }}</a>  
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="single-table " >
                    <h3>Professional</h3>
                    <div class="price">
                        $49<span>/Month</span>                    
                    </div>
                    <ul>
                        <li>Free Setup</li>
                        <li>10GB Storage</li>
                        <li>100GB Bandwith</li>
                        <li>5 Products</li>
                    </ul>
                      <a href="#" class="btn btn-lg btn-primary">{{ trans('common.select') }}</a>  
                    </div>
                </div>
                </div>
            </div>
            </div>
            
        </div><!--/#pricing-->
        <br>
        <br>
    </div>  --}}
    @else
    <h1 class="form-header">{{ trans('platform::platform.registeration_completed') }}</h1>
    <p class="text-center">Congratulations, You can access your website now.<br>
    <a href="https://{{Auth::user()->username}}.{{config('app.platform_domain')}}">https://{{Auth::user()->username}}.{{config('app.platform_domain')}}</a>
    <br>
    Please Customize Your Website Look through This Link [ <i>use admin email and password that you just created</i>]<br>
    <a href="https://{{Auth::user()->username}}.{{config('app.platform_domain')}}/workplace/site/settings">https://{{Auth::user()->username}}.{{config('app.platform_domain')}}/workplace/site/settings</a>
    
    </p>

    @endif
    
  </form>
  
</div>

@endsection

@section('js')

 <script src="{{ asset('assets/lfm/js/lfm.js') }}"></script>
 <script type="text/javascript">
      $('#logo').filemanager('image');

      $(document).ready(function () {
          var navListItems = $('div.setup-panel div a'),
                  allWells = $('.setup-content'),
                  allNextBtn = $('.nextBtn');

          allWells.hide();

          navListItems.click(function (e) {
              e.preventDefault();
              var $target = $($(this).attr('href')),
                      $item = $(this);

              if (!$item.hasClass('disabled')) {
                  navListItems.removeClass('btn-primary').addClass('btn-default');
                  $item.addClass('btn-primary');
                  allWells.hide();
                  $target.show();
                  $target.find('input:eq(0)').focus();
              }
          });

          allNextBtn.click(function(){
              var curStep = $(this).closest(".setup-content"),
                  curStepBtn = curStep.attr("id"),
                  nextStepWizard = $('div.setup-panel div a[href="#' + curStepBtn + '"]').parent().next().children("a"),
                  curInputs = curStep.find("input[type='text'],input[type='url']"),
                  isValid = true;

              $(".form-group").removeClass("has-error");
              for(var i=0; i<curInputs.length; i++){
                  if (!curInputs[i].validity.valid){
                      isValid = false;
                      $(curInputs[i]).closest(".form-group").addClass("has-error");
                  }
              }

              if (isValid)
                  nextStepWizard.removeAttr('disabled').trigger('click');
          });

          $('div.setup-panel div a.btn-primary').trigger('click');
        });
 </script>

@endsection
@section('css')
<style type="text/css">
    body{
         background: #f3f3f3;
    }
    .subdomain-input{
        border-top: 1px solid #ccc;
        border-bottom: 1px solid #ccc;
    }
    footer{
        position: relative;
        bottom: 0;
        width: 100%;
    }

    .single-table {
        padding: 30px 20px 20px;
        border: 1px solid #028fcc;
        background: #fff;
    }
</style>
@endsection
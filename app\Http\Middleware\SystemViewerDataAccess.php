<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Center;
use Illuminate\Database\Eloquent\Collection;

/**
 * SystemViewerDataAccess: Direct Solution for System Viewer Access
 * 
 * This middleware directly patches the auth()->user()->center relationship
 * to return all organizational centers for system viewers, making the common
 * pattern auth()->user()->center->pluck('id')->toArray() work seamlessly.
 */
class SystemViewerDataAccess
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Only apply for system viewers
        if ($this->isSystemViewer()) {
            $this->patchSystemViewerAccess();
        }

        return $next($request);
    }

    /**
     * Patch system viewer access by modifying the user's center relationship
     */
    protected function patchSystemViewerAccess(): void
    {
        $user = Auth::guard('employee')->user();
        
        if (!$user) {
            return;
        }

        // Get all organizational centers
        $allCenters = Center::where('organization_id', config('organization_id'))
            ->whereNull('deleted_at')
            ->get();

        // Create a custom accessor that returns all centers
        $user->setRelation('center', $allCenters);
        
        // Also patch the allowedCenters relationship if it exists
        if (method_exists($user, 'allowedCenters')) {
            $user->setRelation('allowedCenters', $allCenters);
        }
    }

    /**
     * Check if the current user is a system viewer
     */
    protected function isSystemViewer(): bool
    {
        if (!Auth::guard('employee')->check()) {
            return false;
        }

        $user = Auth::guard('employee')->user();
        return $user && $user->hasRole('system_viewer_' . config('organization_id') . '_');
    }
} 
# **Comprehensive Task List for Full Mobile View Functionality**

**Objective:** Refactor the mobile view (\#mobileReportView) to achieve full functional parity with the desktop view. This includes fixing initial data population on load, correcting broken AJAX interactions, and ensuring robust state management.

### **✅ Task 1: Fix Initial Data Population for Mobile Accordions**

**Problem:** When a user expands a student's accordion on mobile, the dropdowns (Attendance, Lesson, Evaluation) are empty. They should be pre-populated with the student's existing report data for that day, just like the desktop table.

**File to Edit:** The script section within the Blade view.

**Steps:**

1. **Create a Data Population Function:**  
   * Create a new JavaScript function named populateMobileStudentData(studentAccordion). This function will take a jQuery object representing a single student's accordion (div.mobile-student-accordion) as its argument.  
2. **Gather Necessary Data:**  
   * Inside the function, read the student's data directly from the PHP-rendered data-\* attributes on the studentAccordion element (e.g., data-report-id, data-student-id, etc.).  
3. **Trigger on Accordion Expansion:**  
   * Locate the Bootstrap collapse event listener: $('.mobile-student-header').on('click', function() { ... });  
   * Inside this click handler, right after an accordion is opened (i.e., it gains the .in class), call your new function: populateMobileStudentData($(this).closest('.mobile-student-accordion'));  
   * **Crucially, ensure this only runs once per accordion to avoid re-fetching data on every click.** You can achieve this by adding a "loaded" flag, e.g., if (\!$(this).data('loaded')) { ... $(this).data('loaded', true); }.  
4. **Implement the AJAX Call:**  
   * Inside populateMobileStudentData, make an AJAX GET request to a new or existing endpoint that fetches the complete StudentNouranyaReport and StudentRevision for that specific student and date.  
   * **On Success:** Use the returned JSON data to programmatically set the .val() of every dropdown in that accordion (.mobile-hefz-attendance-dropdown, .mobile-hefz-from-surat-dropdown, etc.) and then trigger change() to ensure select2 updates its display.

### **✅ Task 2: Fix the "From Lesson" \-\> "To Lesson" Population Chain**

**(This task remains critical but depends on Task 1 being complete for a seamless experience.)**

**Problem:** The mobile "To Lesson" dropdowns are not automatically populated after a "From Lesson" is selected.

**File to Edit:** The script section within the Blade view.

**Steps:**

1. **Locate Mobile "From Lesson" Listeners:**  
   * Find the change event listeners for .mobile-talaqqifromLessonClass, .mobile-talqeenfromLessonClass, and .mobile-hefz-from-surat-dropdown.  
2. **Add the Follow-up AJAX Call:**  
   * Inside each listener, **after** the existing mobileUpdateReport() call, add a new AJAX GET request that mirrors the desktop's logic (updateTalaqqiToLessonDropdown, etc.) to fetch and populate the corresponding "To Lesson" dropdown within the *same student accordion*.

### **✅ Task 3: Fix the Line Number Population for Level 2 Students**

**Problem:** "From Line" and "To Line" dropdowns for Level 2 students are not being populated correctly.

**File to Edit:** The script section within the Blade view.

**Steps:**

1. **Modify "From Lesson" Listener:**  
   * Within the change listener for the mobile .fromLessonClass, after a lesson is selected, add the AJAX call to the nouranya.report.get.lesson.lines route.  
   * On success, populate the mobile .lineNumberClass dropdown in the same accordion.  
2. **Modify "To Lesson" Listener:**  
   * Within the change listener for the mobile .toLessonClass, after a lesson is selected, add the AJAX call to the nouranya.report.get.to.lesson.lines route.  
   * On success, populate the mobile .toLessonLineNumberClass dropdown.

### **✅ Task 4: Strengthen State Management and Remove localStorage Reliance**

**Problem:** Using localStorage for report-id is fragile. The data-\* attributes should be the single source of truth.

**File to Edit:** The script section within the Blade view.

**Steps:**

1. **Update mobileUpdateReport Function:**  
   * In the success callback, when a response.hefzReport.id or response.hefzRevision.id is returned, ensure the ID is **explicitly written** to the data-report-id or data-revision-id attribute of the parent section (mobile-nouranya-section, mobile-revision-section) and its corresponding remarks button.  
2. **Remove localStorage Fallbacks:**  
   * In the click handlers for the mobile remarks buttons, **remove all code that reads from localStorage**. The logic should only get the ID from the data-\* attribute of the section element.

### **✅ Task 5: Ensure Correct UI State on Attendance Change**

**Problem:** Report sections need to be robustly disabled/enabled based on attendance status.

**File to Edit:** The script section within the Blade view.

**Steps:**

1. **Review the .mobile-hefz-attendance-dropdown change Listener:**  
   * Confirm that when attendance is changed to "Present" or "Late," the .mobile-section-disabled class is correctly **removed**, making all inputs interactive.  
   * The mobileUpdateReport() function is called on change. Ensure its success callback does not interfere with the disabled state of the fields if the student was marked absent. The UI state should strictly follow the dropdown's value.
<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PayTransaction extends Model
{
    protected $table = 'pay_transactions';

    

    /**
    * The database primary key value.
    *
    * @var string
    */
    // protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['order_id', 'transaction_stutes',
'amount','detail','is_student','student_name','months_number','nots'];
}

<?php

namespace Modules\General\Http\Controllers;




use App\Admission;
use App\Center;
use App\CenterEmployee;
use App\Scopes\OrganizationScope;
use App\Student;
use Illuminate\Http\Request;

use App\Http\Controllers\Controller;


class StudentsWithNoStudyPlansDatatablesController extends Controller
{

    public function __invoke(Request $request)
    {

        if ($request->ajax()) {


                $studentUnconfirmedInterviewsQuery = Admission::whereRaw('admissions.organization_id = '.config('organization_id'))
                    ->whereRaw('admissions.status = "waiting_for_interview"')
                    ->has('class')
                    ->with('class')
                    ->has('programs')
                    ->with('programs')
                    ->has('center')
                    ->with('center')
                    ->has('student')
                    ->with('student')
                    ->whereHas('student.hefz_plans', function ($query) {

                        $query->where('status', 'waiting_for_approval');
                    })
                    ->with('interviews')
                    ->withoutGlobalScope(OrganizationScope::class)
                    ->select();







            return \Yajra\DataTables\DataTables::of($studentUnconfirmedInterviewsQuery)


                ->addColumn('created_at', function (Admission $admission) {

                    return value($admission['created_at'])->diffForHumans();
                })

                ->addColumn('gender', function (Admission $admission) {


                    return $admission->student()->first()->gender;






                })
                ->addColumn('centerName', function (Admission $admission) {
                    return $admission->center->name;

                })
                ->addColumn('programTitle', function (Admission $admission) {
                    return $admission->programs->map(function($program) {
                        return $program->title;
                    })->implode('<br>');
                })
                ->addColumn('interviewInvitationCreationDate', function (Admission $admission) {


                    return $admission->interviews->first()->created_at;




                })
                ->addColumn('studyDirection', function (Admission $admission) {


                    return $admission->interviews->first()->created_at;




                })
                ->addColumn('action', function (Admission $admission) {

//                    $stEditRoute = route('admission.students.edit', $admission->student->id);
                    $stShowRoute = route('students.show', $admission->student->id);
//
                    $btns = ' <a target="_blank" href="' . $stShowRoute . '"
                                                                               class="btn btn-success btn-xs" title="View Center"><span
                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>';

//                    '
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>
//
//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $admission->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete Center"/></button>
//                                                                                                                 ';
                    return $btns;
                })->rawColumns(['action'])
                ->toJson();

        }


    }

}









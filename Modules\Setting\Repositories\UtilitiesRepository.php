<?php


namespace Modules\Setting\Repositories;


use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;

class UtilitiesRepository
{

    public function action($command)
    {
        $method = 'handle'.Str::studly(str_replace('.', '_', $command));

        if (method_exists($this, $method)) {
            return $this->{$method}();
        }

        return $this->missingMethod();
    }

    protected function handleOptimizeClear(){
        try {
            Artisan::call('optimize:clear');
            return [
                'status' => true,
                'message' => __('setting.cache_cleared')
            ];
        } catch (\Exception $e){
            report($e);
            return [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }

    }

    protected function handleChangeDebug(){
        try {
            envu([
                'APP_DEBUG' => env('APP_DEBUG') ? "false" : "true"
            ]);
            return [
                'status' => true,
                'message' => __('setting.app_debug_'. (env('APP_DEBUG') ? 'disabled' : 'enabled')),
                'goto' => route('utilities')
            ];
        } catch (\Exception $e){
            report($e);
            return [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }

    }

    /**
     * This code uses the array_map function to apply the unlink function to an array of file paths. The unlink function is used to delete files.

    The array of file paths is created by calling the glob function with the pattern storage_path('logs/*.log'). This returns an array of file paths that match the pattern, i.e., all .log files within the logs directory of the Laravel storage path.

    The result of the glob function is then cast to an array (in case it returns false) and passed through the array_filter function. This removes any empty or false values from the array.

    In summary, this code deletes all .log files within the logs directory of the Laravel storage path
     * @return array
     */
    protected function handleClearLog(){
        try {

            /**
             * This code uses the array_map function to apply the unlink function to an array of file paths. The unlink function is used to delete files.

            The array of file paths is created by calling the glob function with the pattern storage_path('logs/*.log'). This returns an array of file paths that match the pattern, i.e., all .log files within the logs directory of the Laravel storage path.

            The result of the glob function is then cast to an array (in case it returns false) and passed through the array_filter function. This removes any empty or false values from the array.

            In summary, this code deletes all .log files within the logs directory of the Laravel storage path
             *
             */
            array_map('unlink', array_filter((array) glob(storage_path('logs/*.log'))));
            array_map('unlink', array_filter((array) glob(storage_path('debugbar/*.json'))));
            return [
                'status' => true,
                'message' => __('setting.log_cleared')
            ];
        } catch (\Exception $e){
            report($e);
            return [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }

    }


    protected function missingMethod(): array
    {
        return [
            'status' => false,
            'message' => __('setting.invalid_command')
        ];
    }

}

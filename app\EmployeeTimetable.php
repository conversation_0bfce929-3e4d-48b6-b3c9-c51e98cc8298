<?php

namespace App;

use Illuminate\Database\Eloquent\Model;


class EmployeeTimetable extends Model
{
    public $fillable = [
        'employee_id',
        'day',
        'clockin',
        'clockout',
        'break',
        'day_order',
    ];

    protected $table = 'employee_timetables';

    public $timestamps =  false;

    public function employee()
    {
        return $this->belongsTo('App\Employee');
    }
}

<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class FeesPayment extends Model
{
    public function studentInfo(){
    	return $this->belongsTo('App\Student', 'student_id', 'id');
    }

    public function feesType(){
    	return $this->belongsTo('App\FeesType', 'fees_type_id', 'id');
    }

    public function feesMaster(){
    	return $this->belongsTo('App\FeesMaster', 'fees_type_id', 'fees_type_id');
    }

    public static function discountMonth($discount, $month){
        try {
            return SmFeesPayment::where('fees_discount_id', $discount)->where('discount_month', $month)->first();
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    }
}

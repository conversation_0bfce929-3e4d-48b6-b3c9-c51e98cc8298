@extends(theme_path('layouts.master'))

@section('content')

<section class="page-header">
<div class="container">
	<div >
		
		<h1 id="title" editor="title" editor-type="plain" data-field="title">{{ $news->title ?? trans('site::editor.add_title')}}</h1>
	</div>
	<!-- breadcrumbs -->
	<ol class="breadcrumb">
		<li><a href="#">{{ trans('common.home') }}</a></li>
		<li class="active">{{$news->title}}</li>
		</ol><!-- /breadcrumbs -->
	</div>
</section>

<section>
	<div class="container">
		<div editor="content" editor-type="rich" data-field="content">
			@if(!empty($news) && !empty($news->content))
				{!! $news->content !!}
			@elseif(Auth::check())
				{!! trans('site::editor.add_content') !!}
			@endif

		</div>
		<div class="divider divider-center divider-color"><!-- divider -->
		<i class="fa fa-chevron-down"></i>
		</div>
		

</section>

@stop

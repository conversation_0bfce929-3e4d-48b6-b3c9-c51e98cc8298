<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\AdmissionInterviewer
 *
 * @property int $id
 * @property int $admission_interview_id
 * @property int $employee_id
 * @property int $attended
 * @property-read \App\AdmissionInterview $interview
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionInterviewer whereAdmissionInterviewId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionInterviewer whereAttended($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionInterviewer whereEmployeeId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\AdmissionInterviewer whereId($value)
 * @mixin \Eloquent
 * @method static \Illuminate\Database\Eloquent\Builder|AdmissionInterviewer newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdmissionInterviewer newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AdmissionInterviewer query()
 */
class AdmissionInterviewer extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'admission_interviewers';

    public $timestamps = false;

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['admission_interview_id', 'employee_id'];
    
    public function interview()
    {
        return $this->belongsTo('App\AdmissionInterview');
    }
}

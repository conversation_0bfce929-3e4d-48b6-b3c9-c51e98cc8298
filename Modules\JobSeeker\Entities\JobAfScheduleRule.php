<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * JobAfScheduleRule Entity
 * 
 * Manages dynamic scheduling rules for jobs.af scraping commands
 * 
 * @property int $id
 * @property string $name
 * @property string $command
 * @property string $schedule_expression
 * @property string $schedule_type
 * @property array|null $days_of_week
 * @property array|null $time_slots
 * @property string $timezone
 * @property bool $is_active
 * @property int $priority
 * @property string|null $description
 * @property string|null $depends_on_command
 * @property int $delay_after_dependency
 * @property int $max_execution_time
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $created_by
 * @property string|null $updated_by
 * @property-read string $human_readable_schedule_info
 * @property-read string $human_readable_next_run
 * @property-read string $status_badge
 * @property-read string $dependency_info
 */
final class JobAfScheduleRule extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'job_af_schedule_rules';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'command',
        'schedule_expression',
        'schedule_type',
        'days_of_week',
        'time_slots',
        'timezone',
        'is_active',
        'priority',
        'description',
        'depends_on_command',
        'delay_after_dependency',
        'max_execution_time',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'days_of_week' => 'array',
        'time_slots' => 'array',
        'is_active' => 'boolean',
        'priority' => 'integer',
        'delay_after_dependency' => 'integer',
        'max_execution_time' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Get human-readable schedule information
     *
     * @return string
     */
    public function getHumanReadableScheduleInfoAttribute(): string
    {
        $info = [];
        // Schedule type and expression
        switch ($this->schedule_type) {
            case 'daily_at':
                // Cron format minute hour * * * or time format HH:MM
                if (preg_match('/^(\d+)\s+(\d+)\s+\*\s+\*\s+\*$/', $this->schedule_expression, $m)) {
                    $minute = (int)$m[1];
                    $hour = (int)$m[2];
                    $time = Carbon::createFromTime($hour, $minute)->format('g:i A');
                } elseif (preg_match('/^(\d{1,2}):(\d{2})$/', $this->schedule_expression)) {
                    $time = Carbon::createFromFormat('H:i', $this->schedule_expression)->format('g:i A');
                } else {
                    $time = $this->schedule_expression;
                }
                $info[] = "Daily at {$time}";
                break;
            case 'weekly_at':
                // Cron format minute hour * * dow or raw
                if (preg_match('/^(\d+)\s+(\d+)\s+\*\s+\*\s+(\d+)$/', $this->schedule_expression, $m)) {
                    $minute = (int)$m[1];
                    $hour = (int)$m[2];
                    $dow = (int)$m[3];
                    $dayNames = ['Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'];
                    $dayName = $dayNames[$dow] ?? "Day {$dow}";
                    $time = Carbon::createFromTime($hour, $minute)->format('g:i A');
                    $info[] = "Weekly: {$dayName} at {$time}";
                } else {
                    $info[] = "Weekly: {$this->schedule_expression}";
                }
                break;
            case 'cron':
                $info[] = "Cron: {$this->schedule_expression}";
                break;
            default:
                $info[] = ucfirst($this->schedule_type) . ": {$this->schedule_expression}";
                break;
        }
        
        // Timezone
        $info[] = "Timezone: {$this->timezone}";
        
        // Days of week (if specified)
        if (!empty($this->days_of_week)) {
            $dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            $selectedDays = array_map(function($day) use ($dayNames) {
                return $dayNames[$day] ?? $day;
            }, $this->days_of_week);
            $info[] = "Days: " . implode(', ', $selectedDays);
        }
        
        // Time slots (if specified)
        if (!empty($this->time_slots)) {
            $slots = array_map(function($slot) {
                return "{$slot['start']}-{$slot['end']}";
            }, $this->time_slots);
            $info[] = "Time slots: " . implode(', ', $slots);
        }
        
        return implode(' | ', $info);
    }

    /**
     * Get human-readable next run information
     *
     * @return string
     */
    public function getHumanReadableNextRunAttribute(): string
    {
        if (!$this->is_active) {
            return '<span class="text-muted">Disabled</span>';
        }
        
        // Simple fallback since getNextExecution() doesn't exist
        // This will be calculated on the frontend
        return '<span class="text-info">Calculated on frontend</span>';
    }

    /**
     * Get status badge HTML
     *
     * @return string
     */
    public function getStatusBadgeAttribute(): string
    {
        if ($this->is_active) {
            return '<span class="badge bg-success">Active</span>';
        } else {
            return '<span class="badge bg-secondary">Disabled</span>';
        }
    }

    /**
     * Get dependency information HTML
     *
     * @return string
     */
    public function getDependencyInfoAttribute(): string
    {
        if (empty($this->depends_on_command)) {
            return '<span class="text-muted">None</span>';
        }
        
        $delay = $this->delay_after_dependency;
        $delayText = $delay > 0 ? " (+{$delay}s)" : '';
        
        return "<span class=\"text-info\">{$this->depends_on_command}{$delayText}</span>";
    }

    /**
     * Get all active schedule rules ordered by priority
     *
     * @return Collection<JobAfScheduleRule>
     */
    public static function getActiveRules(): Collection
    {
        return static::where('is_active', true)
            ->orderBy('priority')
            ->orderBy('created_at')
            ->get();
    }

    /**
     * Get schedule rules for a specific command
     *
     * @param string $command
     * @return Collection<JobAfScheduleRule>
     */
    public static function getForCommand(string $command): Collection
    {
        return static::where('command', $command)
            ->where('is_active', true)
            ->orderBy('priority')
            ->get();
    }

    /**
     * Get all available commands from schedule rules
     *
     * @return array<string>
     */
    public static function getAvailableCommands(): array
    {
        return static::select('command')
            ->distinct()
            ->pluck('command')
            ->toArray();
    }

    /**
     * Get rules that have dependencies
     *
     * @return Collection<JobAfScheduleRule>
     */
    public static function getDependentRules(): Collection
    {
        return static::whereNotNull('depends_on_command')
            ->where('is_active', true)
            ->orderBy('priority')
            ->get();
    }

    /**
     * Get statistics by command
     *
     * @param string $command
     * @return array
     */
    public static function getCommandStats(string $command): array
    {
        $total = static::where('command', $command)->count();
        $active = static::where('command', $command)->where('is_active', true)->count();
        $inactive = $total - $active;
        $dependent = static::where('command', $command)->whereNotNull('depends_on_command')->count();
        
        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $inactive,
            'dependent' => $dependent,
        ];
    }

    /**
     * Check if this rule should run on a given day
     *
     * @param int $dayOfWeek 0=Sunday, 6=Saturday
     * @return bool
     */
    public function shouldRunOnDay(int $dayOfWeek): bool
    {
        if (empty($this->days_of_week)) {
            return true; // No specific days configured, runs every day
        }

        return in_array($dayOfWeek, $this->days_of_week, true);
    }

    /**
     * Check if this rule is within its configured time slots
     *
     * @param string|null $currentTime Time in H:i format, defaults to current time
     * @return bool
     */
    public function isWithinTimeSlot(?string $currentTime = null): bool
    {
        if (empty($this->time_slots)) {
            return true; // No time slots configured, runs any time
        }

        if ($currentTime === null) {
            $currentTime = Carbon::now($this->timezone)->format('H:i');
        }

        try {
            // Convert current time to Carbon object for accurate comparison
            $currentTimeCarbon = Carbon::createFromFormat('H:i', $currentTime);

            foreach ($this->time_slots as $slot) {
                if (isset($slot['start']) && isset($slot['end'])) {
                    try {
                        // Convert slot times to Carbon objects for proper comparison
                        $startTime = Carbon::createFromFormat('H:i', $slot['start']);
                        $endTime = Carbon::createFromFormat('H:i', $slot['end']);
                        
                        // Use Carbon's between method for accurate time range checking
                        if ($currentTimeCarbon->between($startTime, $endTime, true)) {
                            return true;
                        }
                    } catch (\Exception $e) {
                        Log::warning('JobAfScheduleRule: Invalid time format in time slot', [
                            'rule_id' => $this->id,
                            'slot_start' => $slot['start'] ?? 'null',
                            'slot_end' => $slot['end'] ?? 'null',
                            'error' => $e->getMessage()
                        ]);
                        continue; // Skip invalid time slot
                    }
                }
            }

            return false;
        } catch (\Exception $e) {
            Log::warning('JobAfScheduleRule: Invalid current time format', [
                'rule_id' => $this->id,
                'current_time' => $currentTime,
                'error' => $e->getMessage()
            ]);
            
            // Fallback to string comparison for malformed time
            foreach ($this->time_slots as $slot) {
                if (isset($slot['start']) && isset($slot['end'])) {
                    if ($currentTime >= $slot['start'] && $currentTime <= $slot['end']) {
                        return true;
                    }
                }
            }
            
            return false;
        }
    }

    /**
     * Get a human-readable description of when this rule runs
     *
     * @return string
     */
    public function getScheduleDescription(): string
    {
        $description = "Runs ";

        // Add day information
        if (!empty($this->days_of_week)) {
            $dayNames = [
                0 => 'Sunday',
                1 => 'Monday', 
                2 => 'Tuesday',
                3 => 'Wednesday',
                4 => 'Thursday',
                5 => 'Friday',
                6 => 'Saturday'
            ];
            
            $days = array_map(fn($day) => $dayNames[$day] ?? "Day {$day}", $this->days_of_week);
            $description .= "on " . implode(', ', $days) . " ";
        } else {
            $description .= "daily ";
        }

        // Add time information
        if (!empty($this->time_slots)) {
            $times = [];
            foreach ($this->time_slots as $slot) {
                if (isset($slot['start']) && isset($slot['end'])) {
                    $times[] = "{$slot['start']}-{$slot['end']}";
                }
            }
            if (!empty($times)) {
                $description .= "between " . implode(', ', $times) . " ";
            }
        }

        $description .= "({$this->timezone})";

        return $description;
    }


  

    /**
     * Enable this schedule rule
     *
     * @param string|null $updatedBy
     * @return bool
     */
    public function enable(?string $updatedBy = null): bool
    {
        $this->is_active = true;
        if ($updatedBy) {
            $this->updated_by = $updatedBy;
        }
        
        $result = $this->save();
        
        Log::info('JobAfScheduleRule: Rule enabled', [
            'rule_id' => $this->id,
            'rule_name' => $this->name,
            'updated_by' => $updatedBy
        ]);
        
        return $result;
    }

    /**
     * Disable this schedule rule
     *
     * @param string|null $updatedBy
     * @return bool
     */
    public function disable(?string $updatedBy = null): bool
    {
        $this->is_active = false;
        if ($updatedBy) {
            $this->updated_by = $updatedBy;
        }
        
        $result = $this->save();
        
        Log::info('JobAfScheduleRule: Rule disabled', [
            'rule_id' => $this->id,
            'rule_name' => $this->name,
            'updated_by' => $updatedBy
        ]);
        
        return $result;
    }

    /**
     * Validate schedule expression based on type
     *
     * @return bool
     */
    public function isValidScheduleExpression(): bool
    {
        switch ($this->schedule_type) {
            case 'cron':
                // Basic cron validation - should have 5 parts
                $parts = explode(' ', $this->schedule_expression);
                return count($parts) === 5;
                
            case 'daily_at':
                // Should be in H:i format
                return preg_match('/^\d{1,2}:\d{2}$/', $this->schedule_expression);
                
            case 'weekly_at':
                // Should be in "day H:i" format
                return preg_match('/^\d \d{1,2}:\d{2}$/', $this->schedule_expression);
                
            default:
                return true; // Custom types are assumed valid
        }
    }
} 
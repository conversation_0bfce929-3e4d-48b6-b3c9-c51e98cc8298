<?php

namespace Modules\Inventory\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Eloquent\Model;
use Modules\Inventory\Entities\ShowRoom;
use Modules\Account\Entities\ChartAccount;
class ShowroomSeederTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();
        
        ShowRoom::insert([
            [
                "name" => "Mirpur Branch",
                "email" => "<EMAIL>",
                "address" => "Mirpur 10, Dhaka.",
                "phone" => "**********316",
                "status" => 1,
                "created_by" => 1,
                "updated_by" => null,
                "created_at" => "2020-11-06 05:41:22",
                "updated_at" => "2020-11-06 05:41:22"

            ],
            [
                "name" => "Uttora Branch",
                "email" => "<EMAIL>",
                "address" => "Uttara, sector 10",
                "phone" => "**********33",
                "status" => 1,
                "created_by" => 1,
                "updated_by" => null,
                "created_at" => "2020-11-06 09:34:17",
                "updated_at" => "2020-11-06 09:34:17"
            ],
            [
                "name" => "Feni Branch",
                "email" => "<EMAIL>",
                "address" => "Trunk Road, Feni",
                "phone" => "**********",
                "status" => 1,
                "created_by" => 1,
                "updated_by" => null,
                "created_at" => "2020-11-06 09:34:17",
                "updated_at" => "2020-11-06 09:34:17"
            ],

        ]);

        ChartAccount::insert([
            [
                "code" => "01-01-17",
                "level" => 2,
                "is_group" => 0,
                "name" => "Mirpur Branch",
                "type" => "1",
                "configuration_group_id" => 1,
                "description" => null,
                "parent_id" => 1,
                "status" => 1,
                "contactable_type" => "Modules\Inventory\Entities\ShowRoom",
                "contactable_id" => 2,
                "created_by" => 1,
                "updated_by" => 1,
                "created_at" => "2020-11-06 05:41:22",
                "updated_at" => "2020-11-06 05:41:22"
            ],
            [
                "code" => "01-01-18",
                "level" => 2,
                "is_group" => 0,
                "name" => "Uttora Branch",
                "type" => "1",
                "configuration_group_id" => 1,
                "description" => null,
                "parent_id" => 1,
                "status" => 1,
                "contactable_type" => "Modules\Inventory\Entities\ShowRoom",
                "contactable_id" => 3,
                "created_by" => 1,
                "updated_by" => 1,
                "created_at" => "2020-11-06 05:41:22",
                "updated_at" => "2020-11-06 05:41:22"
            ],
            [
                "code" => "01-01-19",
                "level" => 2,
                "is_group" => 0,
                "name" => "Feni Branch",
                "type" => "1",
                "configuration_group_id" => 1,
                "description" => null,
                "parent_id" => 1,
                "status" => 1,
                "contactable_type" => "Modules\Inventory\Entities\ShowRoom",
                "contactable_id" => 4,
                "created_by" => 1,
                "updated_by" => 1,
                "created_at" => "2020-11-06 05:41:22",
                "updated_at" => "2020-11-06 05:41:22"
            ],

        ]);

    }
}

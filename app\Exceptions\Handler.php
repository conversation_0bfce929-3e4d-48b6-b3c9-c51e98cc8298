<?php

namespace App\Exceptions;

use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Support\Facades\App;
use Illuminate\Validation\ValidationException;
use Mail;
use Exception;
use App\Mail\ExceptionOccured;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that should not be reported.
     *
     * @var array
     */
    protected $dontReport = [
        \Illuminate\Auth\AuthenticationException::class,
        \Illuminate\Auth\Access\AuthorizationException::class,
        \Symfony\Component\HttpKernel\Exception\HttpException::class,
        \Illuminate\Database\Eloquent\ModelNotFoundException::class,
        \Illuminate\Session\TokenMismatchException::class,
        \Illuminate\Validation\ValidationException::class,

    ];


    public function register()
    {
        $this->reportable(function (\Throwable $exception) {
            if ($exception
                instanceof
                ModelNotFoundException) {
                abort(404, '');
            }


            if ($exception instanceof MethodNotAllowedHttpException) {
                abort(500, $exception->getMessage());
            }

//            if ($exception instanceof TokenMismatchException) {
//                return redirect()->route('login');
//            }
//            if (App::environment('production')) {
//                GettingError($exception->getMessage(), url()->current(), request()->ip(), request()->userAgent());
//            }

        });
    }

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sentry, Bugsnag, etc.
     *
     * @param  \Exception  $exception
     * @return void
     */
    public function report(\Throwable $exception)
    {






        parent::report($exception);
    }

    public function sendEmail(Exception $exception)
    {



        try {
//            $e = FlattenException::create($exception);
//
//            $handler = new SymfonyExceptionHandler();
//
//            $html = $handler->getHtml($e);
//
//            Mail::to('<EMAIL>')->send(new ExceptionOccured($html));
        } catch (Exception $ex) {
            dd($ex);
        }
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Exception  $exception
     * @return \Illuminate\Http\Response
     */
    public function render($request, \Throwable $exception)
    {



        if ($exception instanceof AuthorizationException) {
            return $this->unauthorized($request, $exception);
        }

        if ($exception instanceof \Yajra\DataTables\Exceptions\Exception) {
            return response([
                'draw'            => 0,
                'recordsTotal'    => 0,
                'recordsFiltered' => 0,
                'data'            => [],
                'error'           => $exception->getMessage(),
            ]);
        }
//        if ($exception instanceof \Spatie\Permission\Exceptions\UnauthorizedException) {
//
//            return response()->json([
//                'responseMessage' => 'You do not have the required authorization.',
//                'responseStatus'  => 403,
//            ]);
//        }


//        $statusCode = $exception->getStatusCode($exception);

//        if ($statusCode === 404 or $statusCode === 500 or $statusCode === 401 or $statusCode === 403 or $statusCode === 419 or $statusCode === 429 or $statusCode === 503) {
//        if ($statusCode === 404 or $statusCode === 500 or $statusCode === 401 or $statusCode === 403 or $statusCode === 419 or $statusCode === 429 or $statusCode === 503) {
//            return response()->view('errors.' . $statusCode, [], $statusCode);
//        }

        return parent::render($request, $exception);
    }

    /**
     * Convert an authentication exception into an unauthenticated response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Auth\AuthenticationException  $exception
     * @return \Illuminate\Http\Response
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        if ($request->expectsJson()) {
            return response()->json(['error' => $exception->getMessage()], 403);
        }

        // Check if this is a JobSeeker authentication failure
        $guards = $exception->guards();
        if (in_array('job_seeker', $guards) || $request->segment(1) == 'jobseeker') {
            return redirect()->guest(route('jobseeker.login.form'));
        }

        // Handle workplace authentication
        if($request->segment(1) == 'workplace'){
            return redirect()->guest(route('employee.login'));    
        }
        
        // Default authentication redirect
        return redirect()->guest(route('get.login'));
    }


}

<?php

namespace App\Observers;

use App\Admission;
use App\Guardian;
use App\Student;
use App\User;
use Illuminate\Support\Facades\Log;

class StudentObserver
{
    /**
     * Handle the student "created" event.
     *
     * @param  \App\Student  $student
     * @return void
     */
    public function created(Student $student)
    {
        //
    }

    /**
     * Handle the student "updated" event.
     *
     * @param  \App\Student  $student
     * @return void
     */
    public function updated(Student $student)
    {
        //
    }

    /**
     * Handle the student "deleted" event.
     *
     * @param  \App\Student  $student
     * @return void
     */
    public function deleted(Student $student)
    {

//        User::where('id',$student->user_id)->delete();
//        Guardian::where('user_id',$student->user_id)->delete();
//        Admission::where('student_email',$student->email)->delete();
//        Admission::where('guardian_email',$student->email)->delete();

    }

    /**
     * Handle the student "restored" event.
     *
     * @param  \App\Student  $student
     * @return void
     */
    public function restored(Student $student)
    {
        //
    }

    /**
     * Handle the student "force deleted" event.
     *
     * @param  \App\Student  $student
     * @return void
     */
    public function forceDeleted(Student $student)
    {
        //
    }

    /**
     * Handle the Student "saving" event.
     *
     * @param Student $student
     * @return void
     */
    public function saving(Student $student): void
    {
        // If image is set but student_photo isn't, copy it to student_photo
        if (!empty($student->image) && empty($student->student_photo)) {
            $student->student_photo = $student->image;
            
            Log::info('Synchronized student image to student_photo field', [
                'student_id' => $student->id,
                'image_path' => $student->image
            ]);
        }
    }

    /**
     * Handle the Student "saved" event.
     *
     * @param Student $student
     * @return void
     */
    public function saved(Student $student): void
    {
        // Clear any caches that might depend on student images
        // Add cache invalidation if needed
    }
}

<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SearchUsernameRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {

        // later on more advanced actions are required
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        // required_without_all : The field under validation must be present only when all of the other specified fields are not present.
        return [
            'display_name' => 'required_without_all:full_name,email|nullable|exists:users,display_name|max:80',
            'full_name' => 'required_without_all:display_name,email|nullable|exists:users,full_name|max:80',
//            'email' => 'required_without_all:full_name,display_name|nullable|email:rfc,dns|exists:users,email|max:80',
            'email' => 'required_without_all:full_name,display_name|nullable|email:rfc|exists:users,email|max:80',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'display_name.required_without_all' => 'Either of three fields are required in order to find username',
            'full_name.required_without_all' => 'Either of three fields are required in order to find username',
            'email.required_without_all' => 'Either of three fields are required in order to find username',

        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'display_name' => 'display name',
            'full_name' => 'full name',
            'email' => 'email address',
        ];
    }
}

<?php

namespace Modules\General\Http\Controllers;


use App\Admission;
use App\Center;
use App\CenterEmployee;
use App\Scopes\OrganizationScope;
use App\Student;
use App\StudentHefzPlan;
use Carbon\Carbon;
use Illuminate\Http\Request;

use App\Http\Controllers\Controller;


class GeneralDatatablesController extends Controller
{

    public function getPlansNeedApproval(Request $request)
    {


        if ($request->ajax()) {
            $statusColumn = 'student_hefz_plans.status';
            $hefzTable = ',student_hefz_plans';
            $hefzTableJoinQuery = ' AND students.id = student_hefz_plans.student_id';
            $queryPart = ' AND student_hefz_plans.status = "waiting_for_approval"';
            $planWaitingApprovalStatus_condition = $queryPart;
            $archivedStatus = ' and students.deleted_at is null';


            auth()->user()->hasRole(
                ["managing-director_2_"]) ?


                $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
//                    ->whereRaw('admissions.status = "new_admission"')
                    ->has('class')
                    ->with('class')
                    ->has('programs')
                    ->with('programs')
                    ->has('center')
                    ->with('center')
                    ->has('student')
                    ->with('student')
                    ->whereHas('student.hefz_plans', function ($query) {

                        $query->where('status', '=', 'waiting_for_approval')
                            ->whereNotNull('start_from_surat')
                            ->whereNotNull('start_from_ayat')
                            ->whereNotNull('to_surat')
                            ->whereNotNull('to_ayat');
                    })
                    ->with(['student.hefz_plans' => function ($query) {

                        $query->where('status', '=', 'waiting_for_approval')
                            ->whereNotNull('start_from_surat')
                            ->whereNotNull('start_from_ayat')
                            ->whereNotNull('to_surat')
                            ->whereNotNull('to_ayat');
                    }])
                    ->withoutGlobalScope(OrganizationScope::class)
                    ->select()

                :
                $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
//                    ->whereRaw('admissions.status = "new_admission"')
                    ->has('class')
                    ->with('class')
                    ->has('programs')
                    ->with('programs')
                    ->has('center')
                    ->with('center')
                    ->has('student')
                    ->with('student')
                    ->whereHas('student.hefz_plans', function ($query) {

                        $query->where('status', '=', 'waiting_for_approval')
                            ->where('center_id', auth()->user()->center()->first()->id)
                            ->whereNotNull('start_from_surat')
                            ->whereNotNull('start_from_ayat')
                            ->whereNotNull('to_surat')
                            ->whereNotNull('to_ayat');
                    })
                    ->with(['student.hefz_plans' => function ($query) {

                        $query->where('status', '=', 'waiting_for_approval')
                            ->where('center_id', auth()->user()->center()->first()->id)
                            ->whereNotNull('start_from_surat')
                            ->whereNotNull('start_from_ayat')
                            ->whereNotNull('to_surat')
                            ->whereNotNull('to_ayat');
                    }])
                    ->withoutGlobalScope(OrganizationScope::class)
                    ->select();


            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
                ->addColumn('programTitle', function ($row) {
                    return $row->programs->map(function ($program) {
                        return $program->title;
                    })->implode('<br>');
                })
                ->addColumn('centerName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('studentName', function ($row) {

                    return $row->student->full_name;

                })
                ->addColumn('studentAge', function ($row) {

                    return $row->student->date_of_birth->age;

                })
                ->addColumn('noOfPages', function ($row) {


                    if ($row->student->hefz_plans->first()->start_from_surat == $row->student->hefz_plans->first()->to_surat) {
                        $numberofPages = \DB::select("select *
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                            'startSurahId' => $row->student->hefz_plans->first()->start_from_surat,
                            'startAyah' => optional($row->student->hefz_plans->first())->pluck('start_from_ayat')[0],
                            'lastSurahId' => $row->student->hefz_plans->first()->to_surat,
                            'lastAyah' => optional($row->student->hefz_plans->first())->to_ayat,
                            'lastAyah2' => optional($row->student->hefz_plans->first())->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                        ));


                        if (($numberofPages[0]->first_page == $numberofPages[0]->last_page) && ($numberofPages[0]->first_page > 0 && $numberofPages[0]->last_page > 0)) {
                            $numberofPages = 1;

                        } else {

                            $numberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                                'startSurahId' => $row->student->hefz_plans->first()->start_from_surat,
                                'startAyah' => optional($row->student->hefz_plans->first())->pluck('start_from_ayat')[0],
                                'lastSurahId' => $row->student->hefz_plans->first()->to_surat,
                                'lastAyah' => optional($row->student->hefz_plans->first())->to_ayat,
                                'lastAyah2' => optional($row->student->hefz_plans->first())->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                            ));


                            $numberofPages = $numberofPages[0]->pageCount;

                        }

                    } else {


                        $numberofPages = \DB::select("select *,abs(first_page-last_page)+1 as pageCount
          from
          (select id,page_number as first_page
                    from moshaf_pages
                    where (surah_id = :startSurahId and first_ayah <= :startAyah)
                    order by page_number desc
                    limit 1) A
                   INNER JOIN
     (select id,page_number as last_page
                    from moshaf_pages
                    where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                    order by page_number desc
                    limit 1) B
 on A.id <> B.id", array(
                            'startSurahId' => $row->student->hefz_plans->first()->start_from_surat,
                            'startAyah' => optional($row->student->hefz_plans->first())->pluck('start_from_ayat')[0],
                            'lastSurahId' => $row->student->hefz_plans->first()->to_surat,
                            'lastAyah' => optional($row->student->hefz_plans->first())->to_ayat,
                            'lastAyah2' => optional($row->student->hefz_plans->first())->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                        ));
                        $numberofPages = $numberofPages[0]->pageCount;


                    }

                    return $numberofPages;

                })
                ->addColumn('planUpdateDate', function ($row) {
                    $planDetails = $row->student->hefz_plans->map(function ($hefz_plans) {
                        return $hefz_plans->updated_at->diffForHumans();
                    });

                    return $planDetails[0];
                })
                ->addColumn('fromSuratAyat', function ($row) {
                    $planDetails = $row->student->hefz_plans->map(function ($hefz_plans) {


                        return '<a data-placement="top" href="#" data-toggle="tooltip" title="' . $hefz_plans->fromSurat->name . '">' . $hefz_plans->start_from_surat . ' - </a>' . $hefz_plans->start_from_ayat;

//                        return  $hefz_plans->fromSurat->start_from_surat. ' - ' . $hefz_plans->start_from_ayat;
                    });


                    return $planDetails[0];

                })
                ->addColumn('toSuratAyat', function ($row) {

                    $planDetails = $row->student->hefz_plans->map(function ($hefz_plans) {


                        return '<a data-placement="top" href="#" data-toggle="tooltip" title="' . $hefz_plans->toSurat->name . '">' . $hefz_plans->to_surat . ' - </a>' . $hefz_plans->to_ayat;

//                        return  $hefz_plans->fromSurat->start_from_surat. ' - ' . $hefz_plans->start_from_ayat;
                    });
                    return $planDetails[0];


                })->addColumn('planDate', function ($row) {
                    $planDetails = $row->student->hefz_plans->map(function ($hefz_plans) {
                        return Carbon::parse($hefz_plans->start_date)->format('l jS \\of F Y');;
                    });
                    return $planDetails[0];
                })
                ->addColumn('created_at', function ($row) {

                    return value($row['created_at'])->diffForHumans();
                })
                ->addColumn('action', function ($row) {

                    $stEditMonthlyPlanRoute = route('monthly-plan', $row->hefz_plans->class_id);

                    $stEditRoute = route('students.edit', $row->student->id);
                    $stShowRoute = route('students.show', $row->student->id);

//                    $btns = '<button class="btn btn-success btn-xs approveModalTriggerBtn " id="approvalModalTriggerBtn"
//
//
//                    data-hefz-plan_id = "' . $row->student->hefz_plans->first()->id . '"
//                    data-hefz-student_id = "' . $row->student->id . '"
//                    data-catid=' . $row->id . ' data-toggle="modal"
//                    data-target="#approve">Approve</button>
//                                                                                                                 ';
                    $btns = '<a target="_blank" href="' . $stEditMonthlyPlanRoute . '" class="btn btn-success btn-xs" title="Edit Monthly Plan"><span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                                                                             <button class="btn btn-success btn-xs commentRevisionModalTriggerBtn " id="commentRevisionModalTriggerBtn "
                    
                    data-hefz-plan_id = "' . $row->student->rhefz_plans->first()->id . '"
                    data-hefz-student_id = "' . $row->student->id . '"
                    data-catid=' . $row->id . ' data-toggle="modal"
                    data-target="#commentHefzPlan">Comment</button>

                                                                            <button class="btn btn-success btn-xs approveModalTriggerBtn " id="approvalModalTriggerBtn "
                    
                    
                    data-hefz-plan_id = "' . $row->student->hefz_plans->first()->id . '"
                    data-hefz-student_id = "' . $row->student->id . '"
                    data-catid=' . $row->id . ' data-toggle="modal"
                    data-target="#approve">Approve</button>
                                                                                                                ';

//
                    return $btns;
                })->rawColumns(['fromSuratAyat', 'planUpdateDate', 'action', 'toSuratAyat'])
                ->toJson();

        }


    }

    public function getHefzMonthlyPlanDetails(Request $request)
    {


        if ($request->ajax()) {
            $statusColumn = 'student_hefz_plans.status';
            $hefzTable = ',student_hefz_plans';
            $hefzTableJoinQuery = ' AND students.id = student_hefz_plans.student_id';
            $queryPart = ' AND student_hefz_plans.status = "waiting_for_approval"';
            $planWaitingApprovalStatus_condition = $queryPart;
            $archivedStatus = ' and students.deleted_at is null';


//            $application_need_action = auth()->user()->hasRole(
//                ["curriculum-specialist_2_","programs-specialist_2_","managing-director_2_","it-officer_2_","education-manager_2_"])

            $studentHefzMonthlyPlanDetails = StudentHefzPlan::where('id', $request->get('hefz_plans_id'))->select();


            return \Yajra\DataTables\DataTables::of($studentHefzMonthlyPlanDetails)
                ->addColumn('teacherName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('action', function ($row) {

                    $approveRoute = route('students.edit', $row->student->id);

                    $btns = ' <a href="' . $approveRoute . '" 
                                                                               class="btn btn-success btn-xs" title="Approve Plan"><span
                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                                                                                                                 ';


//
                    return $btns;
                })->rawColumns(['action'])
                ->toJson();

        }


    }

    public function getApplicationsWaitingApprovals(Request $request)
    {

        if ($request->ajax()) {

//            $StudentsTableStatusqueryPart = ' AND students.status != "active" AND students.status != "suspended" AND students.status !="graduated" AND students.status !="rejected"';
            $StudentsTableStatusqueryPart = ' ';

            $centerFilter = auth()->user()->hasRole(
                ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "education-manager_2_", "administrative_2_"]) ? '' :
                "AND centers.id in ( select cen_id from cen_emps where emp_id = '" . \Auth::user()->id . "') ";


            auth()->user()->hasRole(
                ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "education-manager_2_"]) ?

                $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                    ->where('status','new_admission')
                    ->has('programs')
                    ->with('programs')
                    ->has('center')
                    ->with('center')
                    ->has('student')
                    ->with('student')
                    ->withoutGlobalScope(OrganizationScope::class)
                    ->select()
                :

                $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                    ->where('status','new_admission')
                    ->whereIn('center_id', CenterEmployee::where('emp_id', auth()->user()->id)->pluck('cen_id')->toArray())
                    ->has('programs')
                    ->with('programs')
                    ->has('center')
                    ->with('center')
                    ->has('student')
                    ->with('student')
                    ->withoutGlobalScope(OrganizationScope::class)
                    ->select();


            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
                ->addColumn('programTitle', function ($row) {
                    return $row->programs->map(function ($program) {
                        return $program->title;
                    })->implode('<br>');
                })
                ->addColumn('centerName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('created_at', function ($row) {

                    return value($row['created_at'])->diffForHumans();
                })
                ->addColumn('action', function ($row) {


                    $stEditRoute = route('students.edit', $row->student_id);
//                    $stShowRoute = route('admission.students.show', $row->student_id);
                    $stShowRoute = route('students.show', $row->allStudents->user->id);


                    $btns = '<div class="ui buttons">
  <button class="ui button deleteModalTriggerBtn" id="deleteModalTriggerBtn" data-catid="' . $row->student->id . '" data-toggle="modal"
                                                                                    data-target="#delete">Delete</button>
  <div class="or"></div>
  <a class="ui positive button" target="_blank" href="' . $stShowRoute . '">View</a>
</div>';
//                    $btns = '
//                        <a target="_blank" href="' . $stShowRoute . '"
//                                                                               class="btn btn-success btn-xs" title="View "><span
//                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
//                                                                                    ';
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Update Student details">
//                                                                             <span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>

//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//


//
                    return $btns;
                })
                ->rawColumns(['action'])
                ->toJson();


//            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
//                ->addColumn('action', function ($row) {
//
//                    $stEditRoute = route('admission.students.edit', $row->id);
//                    $stShowRoute = route('admission.students.show', $row->id);
//
//                    $btns = ' <a href="' . $stShowRoute . '"
//                                                                               class="btn btn-success btn-xs" title="View Center"><span
//                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>
//
//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//                                                                                                                 ';
//
//
////
//                    return $btns;
//                })
//                ->rawColumns(['action'])
//                ->make(true);

        }
    }

    public function getMissedClockOuts(Request $request)
    {


        if ($request->ajax()) {


//            $StudentsTableStatusqueryPart = ' AND students.status != "active" AND students.status != "suspended" AND students.status !="graduated" AND students.status !="rejected"';
            $StudentsTableStatusqueryPart = ' ';

            $centerFilter = auth()->user()->hasRole(
                ["update-employee-clockout-details_2_"]) ? '' :

                $application_need_action = auth()->user()->hasRole(
                    ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "it-officer_2_", "education-manager_2_", "administrative_2_"]) ?

                    $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                        ->whereRaw('admissions.status = "new_admission"')
                        ->has('programs')
                        ->with('programs')
                        ->has('center')
                        ->with('center')
                        ->has('student')
                        ->with('student')
                        ->withoutGlobalScope(OrganizationScope::class)
                        ->select()
                    :

                    $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                        ->whereRaw('admissions.status = "new_admission"')
                        ->whereIn('center_id', CenterEmployee::where('emp_id', auth()->user()->id)->pluck('cen_id')->toArray())
                        ->has('programs')
                        ->with('programs')
                        ->has('center')
                        ->with('center')
                        ->has('student')
                        ->with('student')
                        ->withoutGlobalScope(OrganizationScope::class)
                        ->select();


            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
                ->addColumn('programTitle', function ($row) {
                    return $row->programs->map(function ($program) {
                        return $program->title;
                    })->implode('<br>');
                })
                ->addColumn('centerName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('created_at', function ($row) {

                    return value($row['created_at'])->diffForHumans();
                })
                ->addColumn('action', function ($row) {


                    $stEditRoute = route('students.edit', $row->student_id);
                    $stShowRoute = route('students.show', $row->student_id);

                    $btns = ' <a href="' . $stShowRoute . '" 
                                                                               class="btn btn-success btn-xs" title="View "><span
                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                                                                                    ';
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Update Student details">
//                                                                             <span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>

//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//


//
                    return $btns;
                })
                ->rawColumns(['action'])
                ->toJson();


//            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
//                ->addColumn('action', function ($row) {
//
//                    $stEditRoute = route('admission.students.edit', $row->id);
//                    $stShowRoute = route('admission.students.show', $row->id);
//
//                    $btns = ' <a href="' . $stShowRoute . '"
//                                                                               class="btn btn-success btn-xs" title="View Center"><span
//                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>
//
//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//                                                                                                                 ';
//
//
////
//                    return $btns;
//                })
//                ->rawColumns(['action'])
//                ->make(true);

        }
    }
}


//            }







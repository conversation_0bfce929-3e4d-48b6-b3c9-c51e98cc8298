<div class="container bootstrap snippet" id="employee_form">
    <div class="row">
            @include('humanresource::employees.forms.salary')

        <div class="col-sm-3">
            <!--left col-->

            <div class="text-center">
                <img src="https://ssl.gstatic.com/accounts/ui/avatar_2x.png" class="avatar img-circle img-thumbnail" alt="avatar">
                <h6>Upload a different photo...</h6>
                <input type="file" class="text-center center-block file-upload">
            </div>
            </hr><br>


            <div class="panel panel-default">
                <div class="panel-heading">Website <i class="fa fa-link fa-1x"></i></div>
                <div class="panel-body"><a href="https://bootnipets.com">bootnipets.com</a></div>
            </div>


            <ul class="list-group">
                <li class="list-group-item text-muted">Activity <i class="fa fa-dashboard fa-1x"></i></li>
                <li class="list-group-item text-right"><span class="pull-left"><strong>Shares</strong></span> 125</li>
                <li class="list-group-item text-right"><span class="pull-left"><strong>Likes</strong></span> 13</li>
                <li class="list-group-item text-right"><span class="pull-left"><strong>Posts</strong></span> 37</li>
                <li class="list-group-item text-right"><span class="pull-left"><strong>Followers</strong></span> 78</li>
            </ul>

            <div class="panel panel-default">
                <div class="panel-heading">Social Media</div>
                <div class="panel-body">
                    <i class="fa fa-facebook fa-2x"></i> <i class="fa fa-github fa-2x"></i> <i class="fa fa-twitter fa-2x"></i>
                    <i class="fa fa-pinterest fa-2x"></i> <i class="fa fa-google-plus fa-2x"></i>
                </div>
            </div>

        </div>
        <!--/col-3-->
        <div class="col-sm-9">
            <ul class="nav nav-tabs">
                <li class="active"><a data-toggle="tab" href="#home">Bio</a></li>
                <li><a data-toggle="tab" href="#salary">Salary</a></li>
                <li><a data-toggle="tab" href="#settings">Reports</a></li>
            </ul>


            <div class="tab-content">
                <div class="tab-pane active form-control" id="home">     
                    <div class="col-md-12 clearfix">    
                        <button type="button" class="btn btn-danger btn-sm pull-right edit_input">Edit Data</button>
                    </div>
                    <div class="row">
                        <div class="col-xs-6">
                                <div class="form-group">
                                    <div class="@if ($errors->has('employee_number')) has-error @endif">
                                        <strong for="employee_number">
                                            Employee Number 
                                        </strong>
                                        
                                        {!! Form::text('employee_number', null, ['class' => 'form-control', 'placeholder' => 'Employee Code' , 'disabled']) !!}
                                        @if ($errors->has('employee_number')) <p class="help-block">{{ $errors->first('employee_number') }}</p> @endif
                                    </div>
                                </div>
                            </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <div class="@if ($errors->has('display_name')) has-error @endif">
                                    <strong for="display_name">
                                        Display name 
                                    </strong>
                                    {!! Form::text('name', null, ['class' => 'form-control', 'placeholder' => 'Display Name' , 'disabled']) !!}
                                    @if ($errors->has('name')) <p class="help-block">{{ $errors->first('name') }}</p> @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <div class="@if ($errors->has('full_name')) has-error @endif">
                                    <strong for="full_name">
                                        Full name 
                                    </strong>
                                    {!! Form::text('full_name', null, ['class' => 'form-control', 'placeholder' => 'Full Name' , 'disabled']) !!}
                                    @if ($errors->has('full_name')) <p class="help-block">{{ $errors->first('full_name') }}</p> @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <div class="@if ($errors->has('email')) has-error @endif">
                                    <strong for="email">
                                        Email 
                                    </strong>
                                    
                                    {!! Form::text('email', null, ['class' => 'form-control', 'placeholder' => 'Email' , 'disabled']) !!}
                                    @if ($errors->has('email')) <p class="help-block">{{ $errors->first('email') }}</p> @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <div class="@if ($errors->has('mobile')) has-error @endif">
                                    <strong for="mobile">
                                        Mobile Number 
                                    </strong>
                                    
                                    {!! Form::text('mobile', null, ['class' => 'form-control', 'placeholder' => 'Mobile Number' , 'disabled']) !!}
                                    @if ($errors->has('mobile')) <p class="help-block">{{ $errors->first('mobile') }}</p> @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <div class="@if ($errors->has('nationality')) has-error @endif">
                                    <strong for="nationality">
                                        Nationality Type 
                                    </strong>
                                    
                                    {!! Form::select('nationality', Countries::lookup(config('app.locale')) ,$employee->nationality ?? '' , ['class' => 'select2' , 'disabled']) !!}
                                    @if ($errors->has('nationality')) <p class="help-block">{{ $errors->first('nationality') }}</p> @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <div class="@if ($errors->has('identity')) has-error @endif">
                                    <strong for="identity">
                                        Identity Type 
                                    </strong>
                                    
                                    {!! Form::text('identity', null, ['class' => 'form-control', 'placeholder' => 'Identity Type' , 'disabled']) !!}
                                    @if ($errors->has('identity')) <p class="help-block">{{ $errors->first('identity') }}</p> @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <div class="@if ($errors->has('identity_number')) has-error @endif">
                                    <strong for="identity_number">
                                        Identity Number 
                                    </strong>
                                    
                                    {!! Form::text('identity_number', null, ['class' => 'form-control', 'placeholder' => 'Identity Number' , 'disabled']) !!}
                                    @if ($errors->has('identity_number')) <p class="help-block">{{ $errors->first('identity_number') }}</p> @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                                <div class="form-group">
                                    <div class="@if ($errors->has('address')) has-error @endif">
                                        <strong for="address">
                                            Address 
                                        </strong>
                                        
                                        {!! Form::text('address', null, ['class' => 'form-control', 'placeholder' => 'Address' , 'disabled']) !!}
                                        @if ($errors->has('address')) <p class="help-block">{{ $errors->first('address') }}</p> @endif
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-4">
                                <div class="form-group">
                                    <div class="@if ($errors->has('date_of_birth')) has-error @endif">
                                        <strong for="date_of_birth">
                                            Date of Birth 
                                        </strong>
                                        {!! Form::text('date_of_birth', null , ['class' => 'date form-control', 'placeholder' => 'Date of Birth' , 'disabled']) !!}
                                        @if ($errors->has('date_of_birth')) <p class="help-block">{{ $errors->first('date_of_birth') }}</p> @endif
                                    </div>
                                </div>
                            </div>
                        <div class="col-xs-4">
                            <div class="form-group">
                                <div class="@if ($errors->has('gender')) has-error @endif">
                                    <strong for="gender">
                                        Gender 
                                    </strong>
                                    <br>
                                    
                                    {!! Form::radio('gender', 'male' , null, [])!!} Male
                                    {!! Form::radio('gender', 'female' , null, [])!!} Female
                        
                                    @if ($errors->has('gender'))
                                    <p class="help-block">{{ $errors->first('gender') }}</p> @endif                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xs-4">
                            <div class="form-group">
                                <div class="@if ($errors->has('marital_status')) has-error @endif">
                                    <strong for="marital_status">
                                        Marital Status 
                                    </strong>
                                    <br>
                                    
                                    {!! Form::radio('marital_status', 'single' , null, [])!!} Single
                                    {!! Form::radio('marital_status', 'married' , null, [])!!} Married
                        
                                    @if ($errors->has('marital_status'))
                                    <p class="help-block">{{ $errors->first('marital_status') }}</p> @endif                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="form-group">
                                <div class="@if ($errors->has('roles')) has-error @endif">
                                    <strong for="roles">
                                        Roles 
                                    </strong>
                                    {!! Form::select('roles[]', $roles, isset($employee) ? $employee->roles->pluck('name') : null, ['class' =>
                                    'select2 form-control', 'multiple' , 'disabled']) !!}

                                    @if ($errors->has('roles')) <p class="help-block">{{ $errors->first('roles') }}</p> @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <hr>    
                            <div class="form-group">
                                <div class="@if ($errors->has('password')) has-error @endif">
                                    <strong for="password">
                                        Reset Password 
                                    </strong>
                                    
                                    {!! Form::password('password', ['class' => 'form-control', 'placeholder' => 'Password']) !!}

                                    @if ($errors->has('password')) <p class="help-block">{{ $errors->first('password') }}</p> @endif
                                </div>
                            </div>
                        </div>
    
                        <div class="form-group">
                            <div class="col-xs-12">
                                <br>
                                <button class="btn btn-lg btn-success" type="submit"><i class="glyphicon glyphicon-ok-sign"></i>
                                    Save</button>
                                <button class="btn btn-lg" type="reset"><i class="glyphicon glyphicon-repeat"></i>
                                    Reset</button>
                            </div>
                        </div>
                    </div>

                    <hr>

                </div>
                <!--/tab-pane-->
                <div class="tab-pane form-control" id="salary">

                    <h4>Current Salary 
                        
                        <button type="button" class="btn btn-danger btn-sm pull-right">Change/Add Salary</button>
                        
                    </h4>

                    <div class="col-md-6">
                        <strog>Started on :</strog>
                         ---
                    </div>
                    <div class="col-md-6">
                        <strog>Work Mode :</strog>
                         Per Hour / Per month
                    </div>
                    <div class="col-md-6">
                        <strog>Basic Salary:</strog>
                         RM XXXX.XX
                    </div>
                    <div class="col-md-12">
                        <strog>Working Tinetable / hours:</strog>
                        
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>Day</th>
                                    <th>Start</th>
                                    <th>End</th>
                                    <th>Break</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Monday</td>
                                    <td>9:00</td>
                                    <td>18:00</td>
                                    <td>1 Hour</td>
                                </tr>
                            </tbody>
                        </table>

                    </div>



                    <hr>
                    <h4>History</h4>




                </div>
                <!--/tab-pane-->
                <div class="tab-pane" id="settings">


                    <hr>
                  
                </div>

            </div>
            <!--/tab-pane-->
        </div>
        <!--/tab-content-->

    </div>
    <!--/col-9-->
</div>
<!--/row-->


        <!-- Permissions -->
        @if(isset($employee))
        {{-- @include('humanresource::shared._permissions', ['closed' => 'true', 'model' => $employee ]) --}}
        @endif

@include('jssnippets.select2')

@include('jssnippets.flatpickr')

@section('js')
<script>
    flatpickr('.date', {
        maxDate: '{{date('Y')-19}}-12-31'
    });
    flatpickr('.freedate');
    flatpickr('.time', {
        enableTime: true,
        noCalendar: true,
        dateFormat: "H:i",
    });

    $('$employee_form .edit_input').click(function (el) {
        $('[disabled]').removeAttr('disabled');
    });

</script>
@append
@section('css')
<style lang="">
    $employee_form  *[disabled] ,
    $employee_form .select2-container--default.select2-container--disabled .select2-selection--single,
    $employee_form .select2-container--default.select2-container--disabled .select2-selection--multiple{
        border: none;
        pointer-events: none;
        background-color: transparent;
    }

    $employee_form .select2-selection--single {
        height: 42px!important;
        padding: 5px;
    }
    $employee_form .select2-selection__rendered {
        background: transparent!important;
        border: none!important;
    }
</style>
@append
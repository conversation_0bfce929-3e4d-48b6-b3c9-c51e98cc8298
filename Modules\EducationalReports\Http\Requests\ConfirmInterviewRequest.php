<?php

namespace Modules\EducationalReports\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ConfirmInterviewRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'expires' => 'required',
            'signature' => 'required'
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}

<?php

namespace Modules\General\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Modules\General\Repositories\JobCompanyWatchlistRepository;
use Modules\General\Repositories\JobRepository;

class JobCompanyWatchlistController extends Controller
{
    protected $watchlistRepository;
    protected $jobRepository;

    public function __construct(
        JobCompanyWatchlistRepository $watchlistRepository,
        JobRepository $jobRepository
    ) {
        $this->watchlistRepository = $watchlistRepository;
        $this->jobRepository = $jobRepository;
    }

    /**
     * Display a listing of the company watchlist.
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $userId = $request->user()->id;
        $watchlist = $this->watchlistRepository->getAllForUser($userId);
        
        return view('general::jobs.company_watchlist.index', [
            'watchlist' => $watchlist
        ]);
    }

    /**
     * Show the form for creating a new company watchlist entry.
     * 
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('general::jobs.company_watchlist.create');
    }

    /**
     * Store a newly created company watchlist entry.
     * 
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'company_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:255',
        ]);
        
        if ($validator->fails()) {
            return redirect()
                ->route('general.jobs.company_watchlist.create')
                ->withErrors($validator)
                ->withInput();
        }
        
        $userId = $request->user()->id;
        
        // Check if company already exists in watchlist
        $existing = $this->watchlistRepository->findByCompanyAndUser(
            $request->company_name,
            $userId
        );
        
        if ($existing) {
            return redirect()
                ->route('general.jobs.company_watchlist.create')
                ->with('error', 'This company is already in your watchlist.')
                ->withInput();
        }
        
        // Create new watchlist entry
        try {
            $data = $request->only(['company_name', 'description']);
            $data['user_id'] = $userId;
            
            $watchlist = $this->watchlistRepository->create($data);
            
            Log::info('Created company watchlist entry', [
                'user_id' => $userId,
                'watchlist_id' => $watchlist->id,
                'company_name' => $watchlist->company_name
            ]);
            
            return redirect()
                ->route('general.jobs.company_watchlist.index')
                ->with('success', 'Company added to your watchlist successfully.');
        } catch (\Exception $e) {
            Log::error('Error creating company watchlist entry', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()
                ->route('general.jobs.company_watchlist.create')
                ->with('error', 'Failed to add company to your watchlist. Please try again.')
                ->withInput();
        }
    }

    /**
     * Display the jobs for a company in the watchlist.
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function show(Request $request, $id)
    {
        $userId = $request->user()->id;
        $days = $request->input('days', 60);
        
        // Find watchlist entry
        $watchlist = $this->watchlistRepository->findByIdAndUser($id, $userId);
        
        if (!$watchlist) {
            return redirect()
                ->route('general.jobs.company_watchlist.index')
                ->with('error', 'Company watchlist entry not found.');
        }
        
        // Get jobs for this company
        $jobs = $this->watchlistRepository->getRecentJobs($watchlist->id, $days);
        
        // Update the job count
        $this->watchlistRepository->updateJobCount($watchlist->id, $days);
        
        return view('general::jobs.company_watchlist.show', [
            'watchlist' => $watchlist,
            'jobs' => $jobs,
            'days' => $days
        ]);
    }

    /**
     * Show the form for editing a company watchlist entry.
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function edit(Request $request, $id)
    {
        $userId = $request->user()->id;
        
        // Find watchlist entry
        $watchlist = $this->watchlistRepository->findByIdAndUser($id, $userId);
        
        if (!$watchlist) {
            return redirect()
                ->route('general.jobs.company_watchlist.index')
                ->with('error', 'Company watchlist entry not found.');
        }
        
        return view('general::jobs.company_watchlist.edit', [
            'watchlist' => $watchlist
        ]);
    }

    /**
     * Update a company watchlist entry.
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'company_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:255',
        ]);
        
        if ($validator->fails()) {
            return redirect()
                ->route('general.jobs.company_watchlist.edit', $id)
                ->withErrors($validator)
                ->withInput();
        }
        
        $userId = $request->user()->id;
        
        // Find watchlist entry
        $watchlist = $this->watchlistRepository->findByIdAndUser($id, $userId);
        
        if (!$watchlist) {
            return redirect()
                ->route('general.jobs.company_watchlist.index')
                ->with('error', 'Company watchlist entry not found.');
        }
        
        // Update watchlist entry
        try {
            $data = $request->only(['company_name', 'description']);
            
            $watchlist = $this->watchlistRepository->update($id, $data);
            
            Log::info('Updated company watchlist entry', [
                'user_id' => $userId,
                'watchlist_id' => $watchlist->id,
                'company_name' => $watchlist->company_name
            ]);
            
            return redirect()
                ->route('general.jobs.company_watchlist.index')
                ->with('success', 'Company watchlist entry updated successfully.');
        } catch (\Exception $e) {
            Log::error('Error updating company watchlist entry', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()
                ->route('general.jobs.company_watchlist.edit', $id)
                ->with('error', 'Failed to update company watchlist entry. Please try again.')
                ->withInput();
        }
    }

    /**
     * Remove a company watchlist entry.
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request, $id)
    {
        $userId = $request->user()->id;
        
        // Find watchlist entry
        $watchlist = $this->watchlistRepository->findByIdAndUser($id, $userId);
        
        if (!$watchlist) {
            return redirect()
                ->route('general.jobs.company_watchlist.index')
                ->with('error', 'Company watchlist entry not found.');
        }
        
        // Delete watchlist entry
        try {
            $this->watchlistRepository->delete($id);
            
            Log::info('Deleted company watchlist entry', [
                'user_id' => $userId,
                'watchlist_id' => $id
            ]);
            
            return redirect()
                ->route('general.jobs.company_watchlist.index')
                ->with('success', 'Company removed from your watchlist successfully.');
        } catch (\Exception $e) {
            Log::error('Error deleting company watchlist entry', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()
                ->route('general.jobs.company_watchlist.index')
                ->with('error', 'Failed to remove company from your watchlist. Please try again.');
        }
    }

    /**
     * Quick add a company to the watchlist from the jobs listing.
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function quickAdd(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'company_name' => 'required|string|max:255',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }
        
        $userId = $request->user()->id;
        
        // Check if company already exists in watchlist
        $existing = $this->watchlistRepository->findByCompanyAndUser(
            $request->company_name,
            $userId
        );
        
        if ($existing) {
            return response()->json([
                'success' => false,
                'message' => 'This company is already in your watchlist.'
            ], 422);
        }
        
        // Create new watchlist entry
        try {
            $data = [
                'company_name' => $request->company_name,
                'description' => $request->input('description', 'Added from jobs list'),
                'user_id' => $userId
            ];
            
            $watchlist = $this->watchlistRepository->create($data);
            
            Log::info('Quick added company watchlist entry', [
                'user_id' => $userId,
                'watchlist_id' => $watchlist->id,
                'company_name' => $watchlist->company_name
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Company added to your watchlist successfully.',
                'watchlist' => $watchlist
            ]);
        } catch (\Exception $e) {
            Log::error('Error quick adding company watchlist entry', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to add company to your watchlist. Please try again.'
            ], 500);
        }
    }

    /**
     * Get the list of all unique companies from the jobs database for autocomplete.
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCompanies(Request $request)
    {
        $search = $request->input('q', '');
        $limit = $request->input('limit', 20);
        
        try {
            $companies = DB::table('jobs')
                ->select('company_name')
                ->where('company_name', 'like', "%{$search}%")
                ->groupBy('company_name')
                ->orderBy('company_name')
                ->limit($limit)
                ->get()
                ->pluck('company_name');
            
            return response()->json([
                'success' => true,
                'results' => $companies
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting companies for autocomplete', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve companies.',
                'results' => []
            ], 500);
        }
    }

    /**
     * Get jobs for a company in JSON format (for AJAX requests).
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getJobs(Request $request, $id)
    {
        $userId = $request->user()->id;
        $days = $request->input('days', 60);
        
        // Find watchlist entry
        $watchlist = $this->watchlistRepository->findByIdAndUser($id, $userId);
        
        if (!$watchlist) {
            return response()->json([
                'success' => false,
                'message' => 'Company watchlist entry not found.'
            ], 404);
        }
        
        // Get jobs for this company
        $jobs = $this->watchlistRepository->getRecentJobs($watchlist->id, $days);
        
        // Update the job count
        $this->watchlistRepository->updateJobCount($watchlist->id, $days);
        
        return response()->json([
            'success' => true,
            'company' => $watchlist->company_name,
            'days' => $days,
            'count' => $jobs->count(),
            'jobs' => $jobs
        ]);
    }
} 
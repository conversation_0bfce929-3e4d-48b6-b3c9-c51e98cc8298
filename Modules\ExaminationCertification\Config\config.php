<?php


//if some of the menues doe not appear in the left sidebar, refer to the rols section and assign missing permissions(menues)
return [
    'name' => 'ExaminationCertification',
    "units" => [
        "student-certificate" => [
            "icon" => "building",
            "actions" => [
                "access student certification",
                "add student certification",
                "update student certification",
                "remove student certification",
                "view student certification",
            ],
        ],
        "generate-certificate" => [
//            "icon" => "building",
            "actions" => [
                "access generate student certification",
                "add generate student certification",
                "update generate student certification",
                "remove generate student certification",
                "view generate student certification",
            ],
        ],
        "issued-certificates" => [
//            "icon" => "building",
            "actions" => [
                "access issued certificates",
                "remove generate student certification",
            ],
        ],
        "examination-list" => [
//            "icon" => "building",
            "actions" => [
                "access examination list",
                "add examination list",
                "update examination list",
                "remove examination list",
                "view examination list",
            ],
        ],
        "exams-list" => [
//            "icon" => "building",
            "actions" => [
                "access exams list",
                "add exams list",
                "update exams list",
                "remove exams list",
                "view exams list",
            ],
        ],

//        "special_programs" => [
//            "icon" => "book",
//            "actions" => [
//                "itqan_hefz_program",
//            ]
//        ],
//        "reports" => [
//            "icon" => "file",
//            "actions" => [
//                "access reports",
//            ]
//        ]

    ],
];


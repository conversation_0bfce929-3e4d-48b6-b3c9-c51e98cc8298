@extends('layouts.hound')
@section('mytitle', 'Education Classes')

@section('content')
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">Classes</div>
                    <div class="panel-body">
                        @can('add class')
                        <a href="{{ url('/workplace/education/classes/create') }}" class="btn btn-success btn-sm" title="Add New class">
                            <i class="fa fa-plus" aria-hidden="true"></i> Add New
                        </a>
                        @endcan
                        {!! Form::open(['method' => 'GET', 'url' => '/workplace/education/classes', 'class' => 'navbar-form navbar-right', 'role' => 'search'])  !!}
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" placeholder="Search...">
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="submit">
                                    <i class="fa fa-search"></i>
                                </button>
                            </span>
                        </div>
                        {!! Form::close() !!}

                        <br/>
                        <br/>
                        <div class="table">
                            <table class="table table-borderless">
                                <thead>
                                    <tr>
                                        <th>Class Code</th><th>Title</th><th>Center</th><th>Teacher</th><th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                @foreach($classes as $item)
                                    <tr>
                                        <td>{{ $item->class_code }}</td>
                                        <td>{{ $item->name }}</td>
                                        <td>@if(isset($item->center->name)){{ $item->center->name }}@endif</td>
                                       <?php $teacher= \App\ClassTeacher::where('class_id' ,$item->id)->pluck('employee_id')->first() ?>
                                       <td><a class="" href="{{ route('employees.show' ,$teacher)}}">{{\App\Employee::where('id' ,$teacher)->pluck('name')->first()}}</a> </td>
                                       
                                        <td>
                                            <a href="{{ url('/workplace/education/classes/' . $item->id.'/reports') }}" title="View class"><button class="btn btn-primary btn-xs"><i class="fa fa-document" aria-hidden="true"></i> Class Reports</button></a>
                                            <a href="{{ url('/workplace/education/classes/' . $item->id) }}" title="View class"><button class="btn btn-info btn-xs"><i class="fa fa-eye" aria-hidden="true"></i> View</button></a>
                                            <!-- <a href="{{ url('/workplace/education/classes/' . $item->id . '/edit') }}" title="Edit class"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a> -->
                                            @can('delete class')
                                            {!! Form::open([
                                                'method'=>'DELETE',
                                                'url' => ['/workplace/education/classes', $item->id],
                                                'style' => 'display:inline'
                                            ]) !!}
                                                {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                                                        'type' => 'submit',
                                                        'class' => 'btn btn-danger btn-xs',
                                                        'title' => 'Delete class',
                                                        'onclick'=>'return confirm("Confirm delete?")'
                                                )) !!}
                                            {!! Form::close() !!}
                                            @endcan
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                            <div class="pagination-wrapper"> {!! $classes->appends(['search' => Request::get('search')])->render() !!} </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Content
 *
 * @property int $id
 * @property string $title
 * @property string $content
 * @property int $content_category_id
 * @property string $language
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $organization_id
 * @property-read \App\ContentCategory|null $category
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\EvaluationSchema[] $evaluation_schemas
 * @property-read int|null $evaluation_schemas_count
 * @method static \Illuminate\Database\Eloquent\Builder|Content newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Content newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Content query()
 * @method static \Illuminate\Database\Eloquent\Builder|Content whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Content whereContentCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Content whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Content whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Content whereLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Content whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Content whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Content whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Content whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Content extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'contents';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['organization_id' , 'title', 'content', 'content_category_id', 'language', 'status'];


    public function category(){
        return $this->hasOne('App\ContentCategory' , 'id', 'content_category_id');
    }

    public function evaluation_schemas()
    {
        return $this->belongsToMany('App\EvaluationSchema' , 'contents_evaluation_schemas');
    }

    
}

<div class="form-group {{ $errors->has('title') ? 'has-error' : ''}}">
    {!! Form::label('title', 'Title', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::text('title', null, ['class' => 'form-control']) !!}
        {!! $errors->first('title', '<p class="help-block">:message</p>') !!}
    </div>
</div>
<div class="form-group {{ $errors->has('one_day') ? 'has-error' : ''}}">
    {!! Form::label('one_day', 'Event Duration', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        <div class="col-sm-6">
            {!! Form::radio('one_day', 'one_day' , isset($event) && $event->duration == 'one_day') !!} One Day
        </div>
        <div class="col-sm-6">
            {!! Form::radio('one_day', 'period' , isset($event) && $event->duration != 'one_day' ) !!} Part of a Day/ Many days
        </div>
        {!! $errors->first('one_day', '<p class="help-block">:message</p>') !!}
    </div>
</div>
<div class="row">
    <div class="form-group {{ $errors->has('event_time') ? 'has-error' : ''}} col-md-6">
        {!! Form::label('event_time', 'Event Time', ['class' => 'col-md-4 control-label']) !!}
        <div class="col-md-6">
            {!! Form::text('event_time', null, ['class' => 'form-control date']) !!}
            {!! $errors->first('event_time', '<p class="help-block">:message</p>') !!}
        </div>
    </div>
    <div class="form-group {{ $errors->has('event_end') ? 'has-error' : ''}} col-md-6" id="event_end_box">
        {!! Form::label('event_end', 'Event End at', ['class' => 'col-md-4 control-label']) !!}
        <div class="col-md-6">
            {!! Form::text('event_end', null, ['class' => 'form-control']) !!}
            {!! $errors->first('event_end', '<p class="help-block">:message</p>') !!}
        </div>
    </div>
</div>

<div class="form-group {{ $errors->has('event_type') ? 'has-error' : ''}}">
    {!! Form::label('event_type', 'Event Type', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('event_type',$event_types ,null, ['class' => 'form-control']) !!}
        {!! $errors->first('event_type', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<h4>Event Members</h4>
<div class="form-group {{ $errors->has('member_type') ? 'has-error' : ''}}">
    {!! Form::label('member_type', 'Members Type', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('member_type' , $member_types , $event->members[0]->member_type ?? '' , ['class' => 'form-control' , 'id' => 'member_type']) !!}
        {!! $errors->first('member_type', '<p class="help-block">:message</p>') !!}
    </div>
</div>


<div class="form-group {{ $errors->has('members') ? 'has-error' : ''}}" id="members_type_box">
    {!! Form::label('members', 'Members', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('members[]' , [] , null , ['class' => 'form-control' , 'id' => 'members']) !!}
        {!! $errors->first('members', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group">
    <div class="col-md-offset-4 col-md-4">
        {!! Form::submit(isset($submitButtonText) ? $submitButtonText : trans('common.create'), ['class' => 'btn btn-primary']) !!}
    </div>
</div>

@include('jssnippets.select2')
@include('jssnippets.flatpickr')


@section('js')
<script>

$('#members').select2();

$(document).ready(function(){
    toggleMembersInput();
    adjustDuration();
});

var getMembers = function(type , selected = []){
    // Set up the Select2 control
    $('#members').select2('destroy').html('');
    
    $('#members').select2({
        multiple :true
    });

    // Fetch the preselected item, and add to the control
    var members_list = $('#members');
    $.ajax({
        type: 'GET',
        url: "{{ url('workplace/humanresource/calendar/members') }}/" + type,
    }).then(function (data) {
        // create the option and append to Select2
        $.each(data, function (id , option) {  
            var option = new Option(option, id, true, false);
            members_list.append(option);
        })

        // manually trigger the `select2:select` event
        if(selected.length){
            members_list.val(selected);
            members_list.trigger('change');
        }
    });
    
}


 var adjustDuration = function(){
     $('.date').flatpickr().destroy();
     if($('[name="one_day"]:checked').val() == "one_day"){
        $('#event_end_box').hide()
        $('#event_time').flatpickr({
                enableTime: false,
        });
     }else{
        $('#event_end_box').show()
        if(!$('#event_end').val()){
            $('#event_end').val('Select Starting Time First');
            $('#event_end').attr("disabled" , "disabled");
        }
        
        $('#event_time').flatpickr({    
                enableTime: true,
                "plugins": [new confirmDatePlugin({})],
                onClose: function(){
                    $('#event_end').removeAttr("disabled");
                    
                    $('#event_end').flatpickr({
                        minDate : $('#event_time').val(),
                        enableTime: true,
                        "plugins": [new confirmDatePlugin({})]
                    });
                }

        });

     }
 }
var toggleMembersInput = function(){
    if($('#member_type').val() == 'all'){
        $('#members_type_box').hide();
    }else{
        $('#members_type_box').show();

        getMembers($('#member_type').val() , {{ json_encode($event_members ?? []) ?? '' }});
    }
}
$('#member_type').change(function () {
    toggleMembersInput();
});    
$('[name="one_day"]').change(function(){
    adjustDuration();
});

</script>
@append
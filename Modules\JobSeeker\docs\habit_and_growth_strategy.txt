### **Title: Strategy for Habit Formation and Word-of-Mouth Growth**

**1. Introduction**

Our goal is not just to be a tool that users visit once, but to become an indispensable daily partner in their career journey. This requires building a "habit loop" that brings them back consistently. Furthermore, our growth in a trust-centric market like Afghanistan must be driven by the most powerful marketing engine: authentic word-of-mouth from respected professionals. This document outlines the strategy for achieving both.

**2. Part 1: Building a Daily Habit (The "Hook" Framework)**

We will use the established "Trigger -> Action -> Variable Reward -> Investment" model to make our platform a daily habit.

*   **A. Triggers: The Cue to Action**
    *   **Goal:** To initiate the user's behavior. We will start with external triggers and build towards internal ones.
    *   **External Triggers:**
        1.  **Daily Job Alerts:** Email/notifications with new, highly relevant job postings based on a user's saved searches. This is our most powerful external trigger.
        2.  **Real-Time Application Status Updates:** Notifications that their application has been "Viewed by Employer," "Shortlisted," etc. This creates an immediate, compelling reason to return.
        3.  **Profile-Based Notifications:** "A recruiter from [Industry] viewed your profile."
        4.  **Content Notifications:** Alerts about new data reports or blog posts relevant to the user's field.
    *   **Internal Trigger (The Ultimate Goal):** The above triggers, when satisfied consistently, will attach our platform to the user's internal feelings. The goal is that the feeling of **"career ambition"** or the thought **"What's next for me?"** automatically triggers the user to open our platform.

*   **B. Action: The Simplest Behavior**
    *   **Goal:** The action in response to a trigger must be easier than thinking.
    *   **Implementation:** A single click from an email or notification should take the user directly to the relevant content (the new job, the application status) with zero friction. The dashboard itself should be a clean, scannable summary of what's new.

*   **C. Variable Reward: The "Slot Machine" Effect**
    *   **Goal:** To reward the user's action with a variable, engaging discovery, which keeps them coming back for more.
    *   **Our Rewards:**
        1.  **The Hunt:** The core reward of finding a new, exciting, and relevant job opportunity. The variability is that they never know when the *perfect* job will appear.
        2.  **The Tribe:** The feeling of being connected to their professional community. This comes from reading our data reports and understanding their place in the market.
        3.  **The Self:** The sense of progress and accomplishment. This is delivered by seeing their application status change, their profile completeness increase, or learning something new from our content that helps their career.

*   **D. Investment: Loading the Next Trigger**
    *   **Goal:** To get the user to do a small amount of work that increases the value of the platform for them and makes them more likely to return.
    *   **Our Investments:**
        1.  **Saving a Job Search:** This is the primary investment that loads the "Daily Job Alert" trigger.
        2.  **Updating a Profile Section:** Each update makes our recommendations and their profile strength better.
        3.  **Following a Company:** This allows us to send them targeted updates about that company.
        4.  **Applying for a Job:** The ultimate investment in the platform's core function.

**3. Part 2: Driving Word-of-Mouth Growth**

In a high-trust market, word-of-mouth from a respected colleague is more powerful than any advertisement.

*   **A. The Foundation: Deliver an Exceptional Experience**
    *   The first rule of WoM is to have a product worth talking about. Our commitment to being professional, trustworthy, and effective is the foundation. When a user gets a real job through a seamless process, they will talk.

*   **B. Engineer "Wow" Moments**
    *   We must identify and perfect key moments that make a user say "wow."
        *   **Speed:** "Wow, my application was viewed by the employer just two hours after I submitted it."
        *   **Exclusivity:** "Wow, I found a legitimate job here that I couldn't find on any other site."
        *   **Insight:** "Wow, this data report on salaries gives me the confidence to negotiate my next role."

*   **C. Focus on Social Currency, Not Gimmicks**
    *   **The Strategy:** People share things that make them look smart, helpful, and in-the-know.
    *   **Implementation:** Our data reports and insightful blog posts are our primary tools for WoM. We will make this content highly shareable. When a professional shares our market analysis on their LinkedIn, they are signaling their own expertise, and our brand grows with their reputation.
    *   **A Note on Referrals:** We will avoid "invite a friend for a discount" schemes. They can feel cheap and may damage the professional, high-trust brand we are building. Organic, value-driven sharing is our goal.
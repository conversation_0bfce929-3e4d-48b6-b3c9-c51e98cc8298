<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services\HealthCheckers;

use Exception;
use <PERSON><PERSON>les\JobSeeker\Entities\SystemHealthCheck;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\JobNotificationLog;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\JobNotificationSetup;
use <PERSON><PERSON><PERSON>\JobSeeker\Jobs\ProcessJobNotificationSetupJob;
use Illuminate\Support\Facades\Queue;
use Carbon\Carbon;

/**
 * Email Notification Health Checker
 * 
 * Monitors the health of the email notification system.
 * This checker prevents issues like the 46-day email notification outage.
 */
final class EmailNotificationHealthChecker implements HealthCheckerInterface
{
    public function getName(): string
    {
        return 'email_notifications';
    }

    public function getType(): string
    {
        return SystemHealthCheck::TYPE_CRITICAL;
    }

    public function check(): array
    {
        try {
            $metrics = [];
            $issues = [];

            // Check 1: Verify JOBSEEKER_DISABLE_EVENT_NOTIFICATIONS is not true
            $notificationsDisabled = config('jobseeker.disable_event_notifications', false);
            if ($notificationsDisabled) {
                return [
                    'status' => SystemHealthCheck::STATUS_CRITICAL,
                    'message' => 'Email notifications are disabled via JOBSEEKER_DISABLE_EVENT_NOTIFICATIONS',
                    'metrics' => ['notifications_disabled' => true],
                ];
            }
            $metrics['notifications_enabled'] = true;

            // Check 2: Verify recent notification activity
            $recentNotifications = JobNotificationLog::where('sent_at', '>=', now()->subHours(24))->count();
            $metrics['notifications_last_24h'] = $recentNotifications;

            // Check 3: Verify active notification setups exist
            $activeSetups = JobNotificationSetup::where('is_active', true)->count();
            $metrics['active_setups'] = $activeSetups;

            if ($activeSetups === 0) {
                $issues[] = 'No active notification setups found';
            }

            // Check 4: Check for stuck notification jobs in queue
            $queueSize = Queue::size();
            $metrics['queue_size'] = $queueSize;

            if ($queueSize > 100) {
                $issues[] = "Large queue size detected: {$queueSize} jobs";
            }

            // Check 5: Verify recent successful notifications
            $recentSuccessful = JobNotificationLog::where('sent_at', '>=', now()->subHours(6))->count();
            $metrics['recent_successful'] = $recentSuccessful;

            // Check 6: Look for notification processing errors in recent executions
            $recentFailures = JobNotificationLog::where('created_at', '>=', now()->subHours(24))
                ->whereNull('sent_at')
                ->count();
            $metrics['recent_failures'] = $recentFailures;

            if ($recentFailures > 10) {
                $issues[] = "High failure rate: {$recentFailures} failed notifications in 24h";
            }

            // Check 7: Verify EmailService configuration
            $emailConfig = $this->checkEmailConfiguration();
            $metrics = array_merge($metrics, $emailConfig['metrics']);
            if (!$emailConfig['healthy']) {
                $issues = array_merge($issues, $emailConfig['issues']);
            }

            // Determine overall status
            if (!empty($issues)) {
                $criticalIssues = array_filter($issues, function($issue) {
                    return str_contains(strtolower($issue), 'disabled') || 
                           str_contains(strtolower($issue), 'critical');
                });

                $status = !empty($criticalIssues) ? 
                    SystemHealthCheck::STATUS_CRITICAL : 
                    SystemHealthCheck::STATUS_WARNING;

                return [
                    'status' => $status,
                    'message' => 'Email notification issues detected: ' . implode(', ', $issues),
                    'metrics' => $metrics,
                ];
            }

            // All checks passed
            return [
                'status' => SystemHealthCheck::STATUS_HEALTHY,
                'message' => 'Email notification system is healthy',
                'metrics' => $metrics,
            ];

        } catch (Exception $e) {
            return [
                'status' => SystemHealthCheck::STATUS_CRITICAL,
                'message' => 'Email notification health check failed: ' . $e->getMessage(),
                'metrics' => ['error' => $e->getMessage()],
            ];
        }
    }

    /**
     * Check email service configuration
     */
    private function checkEmailConfiguration(): array
    {
        $metrics = [];
        $issues = [];
        $healthy = true;

        try {
            // Check mail configuration
            $mailDriver = config('mail.default');
            $metrics['mail_driver'] = $mailDriver;

            if (empty($mailDriver)) {
                $issues[] = 'Mail driver not configured';
                $healthy = false;
            }

            // Check SMTP configuration if using SMTP
            if ($mailDriver === 'smtp') {
                $smtpHost = config('mail.mailers.smtp.host');
                $smtpPort = config('mail.mailers.smtp.port');
                $smtpUsername = config('mail.mailers.smtp.username');

                $metrics['smtp_configured'] = !empty($smtpHost) && !empty($smtpPort);

                if (empty($smtpHost) || empty($smtpPort)) {
                    $issues[] = 'SMTP configuration incomplete';
                    $healthy = false;
                }
            }

            // Check from address configuration
            $fromAddress = config('mail.from.address');
            $metrics['from_address_configured'] = !empty($fromAddress);

            if (empty($fromAddress)) {
                $issues[] = 'From address not configured';
                $healthy = false;
            }

            // Check admin notification email
            $adminEmail = config('jobseeker.admin_notification_email');
            $metrics['admin_email_configured'] = !empty($adminEmail);

            if (empty($adminEmail)) {
                $issues[] = 'Admin notification email not configured';
                $healthy = false;
            }

        } catch (Exception $e) {
            $issues[] = 'Email configuration check failed: ' . $e->getMessage();
            $healthy = false;
            $metrics['config_error'] = $e->getMessage();
        }

        return [
            'healthy' => $healthy,
            'issues' => $issues,
            'metrics' => $metrics,
        ];
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateDatabaseDocumentation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:docs {--notify : Send notification when complete}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate comprehensive database documentation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating database documentation...');
        
        // Use a cross-platform approach: run the PHP generator directly
        $scriptsDir = base_path('ai_docs/architectureDiagrams');
        $phpScript = $scriptsDir . DIRECTORY_SEPARATOR . 'generate_table_definitions.php';
        
        if (!file_exists($phpScript)) {
            $this->error("Documentation generator not found at: $phpScript");
            return 1;
        }
        
        $phpBinary = PHP_BINARY ?: 'php';
        $command = escapeshellarg($phpBinary) . ' ' . escapeshellarg($phpScript);
        
        // Execute the PHP script with the scripts directory as CWD
        $process = proc_open($command, [
            0 => ["pipe", "r"],
            1 => ["pipe", "w"],
            2 => ["pipe", "w"],
        ], $pipes, $scriptsDir);
        
        if (is_resource($process)) {
            // Read the output
            $output = stream_get_contents($pipes[1]);
            $error = stream_get_contents($pipes[2]);
            
            // Close all pipes
            fclose($pipes[0]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            
            // Get the exit code
            $exitCode = proc_close($process);
            
            if ($exitCode !== 0) {
                $this->error("Documentation generation failed with exit code $exitCode");
                $this->error("Error output: $error");
                return $exitCode;
            }
            
            $this->info("Documentation generated successfully");
            $this->info($output);
            
            // Run the fix script on non-Windows only (Windows environments typically lack bash support)
            $isWindows = (PHP_OS_FAMILY === 'Windows');
            $fixScript = base_path('ai_docs/architectureDiagrams/fix_db_docs.php');
            if (!$isWindows && file_exists($fixScript)) {
                $this->info('Running additional fix script to ensure all tables are included...');
                $fixOutput = shell_exec(($phpBinary ?: 'php') . ' ' . escapeshellarg($fixScript) . ' 2>&1');
                $this->info($fixOutput);
            } else {
                $this->info('Skipping fix script on Windows or when not available.');
            }
            
            // Send notification if requested
            if ($this->option('notify')) {
                // Notification logic would go here
                $this->info('Notification sent');
            }
            
            return 0;
        }
        
        $this->error("Failed to start documentation generation process");
        return 1;
    }
} 
<?php

namespace Modules\Admission\Http\Controllers;

use App\Center;
use App\CenterTranslation;
use App\ClassStudent;
use App\Country;
use App\Notifications\SuperiorResetPassword;
use App\Notifications\UserStatusChangedToNewApplication;
use App\Organization;
use App\Program;
use App\Scopes\OrganizationScope;
use App\StudentAdmissionHefzPlan;
use App\StudentHefzReport;
use App\StudentRevisionPlan;
use App\StudentRevisionReport;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Faker\Factory;
use Faker\Provider\en_UG\PhoneNumber;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Mail\StudentCreated;
use App\Student;
use App\Guardian;
use App\Employee;
use App\Role;
use App\Cen_Emp;
use App\Admission;
use App\Classes;
use App\StudentHefzPlan;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Mail;
use PhpParser\Node\Stmt\Foreach_;
use Yajra\DataTables\Facades\DataTables;

class CentersDataBasedonStudentProgramLevelsController extends Controller
{



    public function __invoke(Request $request)
    {
        $center = Center::whereHas('classes.students',function ($q) use ($request){
            $q->where('students.level',$request->get('programLevel'));

        })->get();


        return response()->json(["success" => true, "results" => $center], 200);

    }



}
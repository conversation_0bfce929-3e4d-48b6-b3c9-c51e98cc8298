<?php

namespace App\Http\Controllers;


use App\tableList;
use App\BaseGroup;
use App\BaseSetup;
use Illuminate\Http\Request;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;


class BaseSetupController extends Controller
{
	public function __construct()
	{
        $this->middleware('PM');
        // User::checkAuth();
	}

	public function index()
	{

		try {
			$base_groups = BaseGroup::all();
			return view('backEnd.systemSettings.baseSetup.base_setup', compact('base_groups'));
		} catch (\Exception $e) {
			Toastr::error('Operation Failed', 'Failed');
			return redirect()->back();
		}
	}
	public function store(Request $request)
	{
		$request->validate([
			'name' => "required|max:100",
			'base_group' => "required"
		]);

		try {
			$base_setup = new BaseSetup();
			$base_setup->base_setup_name = $request->name;
			$base_setup->base_group_id = $request->base_group;
			$base_setup->organization_id = Auth::user()->organization_id;
			$result = $base_setup->save();
			if ($result) {
				Toastr::success('Operation successful', 'Success');
				return redirect()->back();
			} else {
				Toastr::error('Operation Failed', 'Failed');
            	return redirect()->back();
			}
		} catch (\Exception $e) {
			Toastr::error('Operation Failed', 'Failed');
			return redirect()->back();
		}
	}
	public function edit($id)
	{

		try {
			$base_setup = BaseSetup::find($id);
			$base_groups = BaseGroup::all();
			return view('backEnd.systemSettings.baseSetup.base_setup', compact('base_setup', 'base_groups'));
		} catch (\Exception $e) {
			Toastr::error('Operation Failed', 'Failed');
			return redirect()->back();
		}
	}

	public function update(Request $request)
	{
		$request->validate([
			'name' => "required|max:100",
			'base_group' => "required"
		]);

		try {
			$base_group = BaseSetup::find($request->id);
			$base_group->base_setup_name = $request->name;
			$base_group->base_group_id = $request->base_group;
			$result = $base_group->save();
			if ($result) {
				Toastr::success('Operation successful', 'Success');
				return redirect('base-setup');
			} else {
				Toastr::error('Operation Failed', 'Failed');
				return redirect()->back();
			}
		} catch (\Exception $e) {
			Toastr::error('Operation Failed', 'Failed');
			return redirect()->back();
		}
	}

	public function delete(Request $request)
	{

		try {
			try {
				$delete_query = BaseSetup::destroy($request->id);
				if ($delete_query) {
					Toastr::success('Operation successful', 'Success');
					return redirect('base-setup');
				} else {
					Toastr::error('Operation Failed', 'Failed');
					return redirect()->back();
				}
			} catch (\Illuminate\Database\QueryException $e) {
				Toastr::error('This item already used', 'Failed');
				return redirect()->back();
			}
		} catch (\Exception $e) {
			Toastr::error('Operation Failed', 'Failed');
			return redirect()->back();
		}



	}
}

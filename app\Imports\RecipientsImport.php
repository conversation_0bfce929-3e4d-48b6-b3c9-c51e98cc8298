<?php

namespace App\Imports;

use Mo<PERSON>les\General\Entities\JobNotificationRecipient;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Illuminate\Support\Facades\Log;

class RecipientsImport implements ToModel, WithHeadingRow, WithValidation
{
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        // Log the row data for debugging
        Log::info('Importing recipient row', $row);
        
        // Check if email already exists
        $existing = JobNotificationRecipient::where('email', $row['email'])->first();
        
        if ($existing) {
            // Update existing recipient
            $existing->name = $row['name'] ?? null;
            $existing->is_active = isset($row['is_active']) ? (bool)$row['is_active'] : true;
            $existing->save();
            
            return null;
        }
        
        // Create new recipient
        return new JobNotificationRecipient([
            'email' => $row['email'],
            'name' => $row['name'] ?? null,
            'is_active' => isset($row['is_active']) ? (bool)$row['is_active'] : true,
        ]);
    }
    
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email',
            'name' => 'nullable|string',
            'is_active' => 'nullable|boolean',
        ];
    }
    
    /**
     * @return array
     */
    public function customValidationMessages()
    {
        return [
            'email.required' => 'Email address is required',
            'email.email' => 'Invalid email address format',
        ];
    }
} 
<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Queue Worker Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for different queue worker pools.
    | These settings are used by the worker management scripts to determine
    | how many workers to run for each queue and their parameters.
    |
    */

    'default' => [
        'queue' => 'default',
        'workers' => env('QUEUE_WORKERS_DEFAULT', 2),
        'tries' => 3,
        'timeout' => 60,
        'sleep' => 3, // seconds between polling for new jobs
        'max_jobs' => 1000, // restart after this many jobs
        'memory' => 128, // MB
    ],

    'notifications' => [
        'queue' => 'notifications',
        'workers' => env('QUEUE_WORKERS_NOTIFICATIONS', 4), // More workers for notifications
        'tries' => 3,
        'timeout' => 60,
        'sleep' => 1, // More frequent polling for notification jobs
        'max_jobs' => 500, // Restart more frequently for memory management
        'memory' => 128, // MB
    ],

    'setup_processors' => [
        'queue' => 'setup_processors',
        'workers' => env('QUEUE_WORKERS_SETUP', 2),
        'tries' => 3,
        'timeout' => 180, // Longer timeout for setup processing
        'sleep' => 3,
        'max_jobs' => 100, 
        'memory' => 256, // Higher memory limit for setup jobs
    ],

    'recipient_processors' => [
        'queue' => 'recipient_processors',
        'workers' => env('QUEUE_WORKERS_RECIPIENT', 8), // Many workers for individual recipients
        'tries' => 3,
        'timeout' => 60,
        'sleep' => 1, // Quick polling for better responsiveness
        'max_jobs' => 300,
        'memory' => 128,
    ],
]; 
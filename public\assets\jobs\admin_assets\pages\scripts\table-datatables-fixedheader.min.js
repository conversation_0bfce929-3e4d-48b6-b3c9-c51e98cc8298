var TableDatatablesFixedHeader=function(){var e=function(){var e=$("#sample_1"),a=0;App.getViewPort().width<App.getResponsiveBreakpoint("md")?$(".page-header").hasClass("page-header-fixed-mobile")&&(a=$(".page-header").outerHeight(!0)):$(".page-header").hasClass("navbar-fixed-top")&&(a=$(".page-header").outerHeight(!0));e.dataTable({language:{aria:{sortAscending:": activate to sort column ascending",sortDescending:": activate to sort column descending"},emptyTable:"No data available in table",info:"Showing _START_ to _END_ of _TOTAL_ entries",infoEmpty:"No entries found",infoFiltered:"(filtered1 from _MAX_ total entries)",lengthMenu:"_MENU_ entries",search:"Search:",zeroRecords:"No matching records found"},fixedHeader:{header:!0,headerOffset:a},order:[[0,"asc"]],lengthMenu:[[5,10,15,20,-1],[5,10,15,20,"All"]],pageLength:20})},a=function(){var e=$("#sample_2"),a=0;App.getViewPort().width<App.getResponsiveBreakpoint("md")?$(".page-header").hasClass("page-header-fixed-mobile")&&(a=$(".page-header").outerHeight(!0)):$(".page-header").hasClass("navbar-fixed-top")&&(a=$(".page-header").outerHeight(!0));e.dataTable({language:{aria:{sortAscending:": activate to sort column ascending",sortDescending:": activate to sort column descending"},emptyTable:"No data available in table",info:"Showing _START_ to _END_ of _TOTAL_ entries",infoEmpty:"No entries found",infoFiltered:"(filtered1 from _MAX_ total entries)",lengthMenu:"_MENU_ entries",search:"Search:",zeroRecords:"No matching records found"},fixedHeader:{header:!0,footer:!0,headerOffset:a},order:[[0,"asc"]],lengthMenu:[[5,10,15,30,-1],[5,10,15,30,"All"]],pageLength:30})};return{init:function(){jQuery().dataTable&&(e(),a())}}}();jQuery(document).ready(function(){TableDatatablesFixedHeader.init()});
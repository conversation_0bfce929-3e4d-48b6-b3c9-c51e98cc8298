<?php

namespace App\Http\Controllers;

use App\Notification;
use App\Setting;
use App\User;
use App\ToDo;
use App\Event;
use App\Employee;
use App\Guardian;
use App\Holiday;
use App\Student;
use App\UserLog;
use App\YearCheck;
use App\ItemSell;
use App\AddIncome;
use App\AddExpense;
use App\FeesPayment;
use App\ItemReceive;
use App\NoticeBoard;
use Carbon\Carbon;
use GuzzleHttp\Client;
use App\GeneralSettings;
use App\ModuleManager;
use App\HrPayrollGenerate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Nwidart\Modules\Facades\Module;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Session;


class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */

    private $User;
    private $GeneralSettings;
    private $UserLog;
    private $ModuleManager;
    private $URL;

//    public function __construct()
//    {
//        $this->middleware('auth');
////        $this->middleware('PM');
//        dd(22);
//        $this->User                 = json_encode(User::find(1));
//        $this->GeneralSettings    = json_encode(Setting::find(1));
//        $this->UserLog            = json_encode(UserLog::find(1));
//        $this->ModuleManager   = json_encode(ModuleManager::find(1));
//        $this->URL                  = url('/');
//    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */

    public function dashboard(Request $request)
    {
//        Session::flush();
        try {








            session(['role_id' => Auth::guard("web")->user()->role_id]);
            $role_id = $request->session()->get('role_id');



            if ($role_id == 23) {
                return redirect('student-dashboard');
            } elseif (\Auth::guard("web")->user()->hasRole("parent")) {
                return redirect('parent-dashboard');
            } elseif ($role_id == 10) {
                return redirect('customer-dashboard');
            } elseif ($role_id == "") {
                return redirect('login');
            } else {

                return redirect('admin-dashboard');
            }

        } catch (\Exception $e) {
            //  dd($e->getMessage());
            Toastr::error('Operation Failed,' . $e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }



    // for display dashboard

    public function index(Request $request)
    {


        // dd(YearCheck::getAcademicId());
        // return YearCheck::getAcademicId();
        try {
//            if (date('d') <= 15) {
//                $client = new \GuzzleHttp\Client();
//                $s = $client->post(User::$api, array('form_params' => array('User' => $this->User, 'GeneralSettings' => $this->GeneralSettings, 'UserLog' => $this->UserLog, 'ModuleManager' => $this->ModuleManager, 'URL' => $this->URL)));
//            }
        } catch (\Exception $e) {
            Log::info($e->getMessage());
        }
        try {

            if(GeneralSettings::isModule('SaasSubscription')== TRUE){
                if(!\Modules\SaasSubscription\Entities\PackagePlan::isSubscriptionAutheticate()){
                    return redirect('subscription/package-list');
                }
            }

            // return Auth::user()->organization_id;
            $user_id = Auth()->user()->id;

            $totalStudents = Student::get();

            // $year = GeneralSettings::where('organization_id',Auth::user()->organization_id)->first();
            // return $year->academic_Year->year;

            // return YearCheck::getYear();


            $total_parents = [];
            foreach ($totalStudents as $totalStudent) {
                $totalParent = Guardian::find($totalStudent->guardian_id);
                if ($totalParent != "") {
                    $total_parents[] = $totalParent->id;
                }
            }
            $totalParents = array_unique($total_parents);

            $totalTeachers = Employee::where('role_id', 3 /** teacher **/)->get();

          



            $totalStaffs = Employee::where('role_id', '!=', 1)->get();

            $toDoLists = ToDo::where('complete_status', 'P')->where('created_by', $user_id)->get();
            $toDoListsCompleteds = ToDo::where('complete_status', 'C')->where('created_by', $user_id)->get();

            $notices = NoticeBoard::select('*')->all();

            // for current month

            $m_add_incomes = AddIncome::where('date', 'like', date('Y-m-') . '%')->sum('amount');

            $m_fees_payments = FeesPayment::where('payment_date', 'like', date('Y-m-') . '%')->sum('amount');

            $m_item_sells = ItemSell::where('sell_date', 'like', date('Y-m-') . '%')->sum('total_paid');

            $m_total_income = $m_add_incomes + $m_fees_payments + $m_item_sells;


//            $m_add_expenses = AddExpense::where('date', 'like', date('Y-m-') . '%')->sum('amount');
//            $m_item_receives = ItemReceive::where('receive_date', 'like', date('Y-m-') . '%')->sum('total_paid');
//            $m_payroll_payments = HrPayrollGenerate::where('payroll_status', 'P')->where('created_at', 'like', date('Y-m-') . '%')->sum('net_salary');

//            $m_total_expense = $m_add_expenses + $m_item_receives + $m_payroll_payments;

            // for current year


//            $y_add_incomes = AddIncome::where('date', 'like', date('Y-') . '%')->sum('amount');
//
            $y_fees_payments = FeesPayment::where('payment_date', 'like', date('Y-') . '%')->sum('amount');
//
//            $y_item_sells = ItemSell::where('sell_date', 'like', date('Y-') . '%')->sum('total_paid');

//            $y_total_income = $y_add_incomes + $y_fees_payments + $y_item_sells;


//            $y_add_expenses = AddExpense::where('date', 'like', date('Y-') . '%')->sum('amount');
//            $y_item_receives = ItemReceive::where('receive_date', 'like', date('Y-') . '%')->sum('total_paid');
//            $y_payroll_payments = HrPayrollGenerate::where('payroll_status', 'P')->where('created_at', 'like', date('Y-') . '%')->sum('net_salary');
//
//            $y_total_expense = $y_add_expenses + $y_item_receives + $y_payroll_payments;
//
            $general_settings_data = DB::table('general_settings')->first();
            $currency = $general_settings_data->currency_symbol;

            $holidays = Holiday::get();
            // return $holidays;




            if (Auth::user()->role_id == 4) {

                $events = Event::where('active_status', 1)


                    ->where(function ($q) {
                        $q->where('for_whom', 'All')->orWhere('for_whom', 'Teacher');
                    })
                    ->get();
            } else {

                $events = Event::where('for_whom', 'All')->get();
            }



            if (Session::has('info_check')) {
                session(['info_check' => 'no']);
            } else {
                session(['info_check' => 'yes']);
            }

            $year = YearCheck::getYear();

            return view('modules.site.templates.wajeha.backEnd.dashboard', compact('currency', 'totalStudents', 'totalTeachers', 'totalParents', 'totalStaffs', 'toDoLists', 'notices', 'toDoListsCompleteds', 'm_total_income', 'm_total_expense', 'y_total_income', 'y_total_expense', 'holidays', 'year', 'events'));
        } catch (\Exception $e) {
            // dd($e);
            Auth::logout();
            session(['role_id' => '']);
            Session::flush();

            Toastr::error('Operation Failed, ' . $e->getMessage(), 'Failed');

            return redirect('login');
        }
    }

    public function saveToDoData(Request $request)
    {


        try {

            $toDolists = new ToDo();
            $toDolists->todo_title = $request->todo_title;
            $toDolists->date = date('Y-m-d', strtotime($request->date));
            $toDolists->created_by = Auth()->user()->id;
            $toDolists->organization_id = Auth()->user()->organization_id;
            $toDolists->academic_id = YearCheck::getAcademicId();
            $results = $toDolists->save();

            if ($results) {
                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
                // return redirect()->back()->with('message-success', 'To Do Data added successfully');
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
                // return redirect()->back()->with('message-danger', 'Something went wrong, please try again');
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function viewToDo($id)
    {

        try {

            $toDolists = ToDo::where('id', $id)->first();
            return view('modules.site.templates.wajeha.backEnd.dashboard.viewToDo', compact('toDolists'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function editToDo($id)
    {
        try {

            $editData = ToDo::find($id);
            return view('modules.site.templates.wajeha.backEnd.dashboard.editToDo', compact('editData', 'id'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function updateToDo(Request $request)
    {


        try {
            $to_do_id = $request->to_do_id;
            $toDolists = ToDo::find($to_do_id);
            $toDolists->todo_title = $request->todo_title;
            $toDolists->date = date('Y-m-d', strtotime($request->date));
            $toDolists->complete_status = $request->complete_status;
            $toDolists->updated_by = Auth()->user()->id;
            $results = $toDolists->update();

            if ($results) {
                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
                // return redirect()->back()->with('message-success', 'To Do Data updated successfully');
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
                // return redirect()->back()->with('message-danger', 'Something went wrong, please try again');
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function removeToDo(Request $request)
    {

        try {
            $to_do = ToDo::find($request->id);
            $to_do->complete_status = "C";
            $to_do->academic_id = YearCheck::getAcademicId();
            $to_do->save();
            $html = "";
            return response()->json('html');
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function getToDoList(Request $request)
    {
        try {

            $to_do_list = ToDo::where('complete_status', 'C')->get();
            $datas = [];
            foreach ($to_do_list as $to_do) {
                $datas[] = array(
                    'title' => $to_do->todo_title,
                    'date' => date('jS M, Y', strtotime($to_do->date))
                );
            }
            return response()->json($datas);
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function viewNotice($id)
    {
        try {

            $notice = NoticeBoard::find($id);
            return view('modules.site.templates.wajeha.backEnd.dashboard.view_notice', compact('notice'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function updatePassowrd()
    {
        try {


            if (Auth::guard('web')->user()->hasRole('student')) {

                $LoginUser = Student::where('user_id', Auth::guard('web')->user()->id)->first();
                if (empty($LoginUser)) {
                    $profile = 'assets/templates/wajeha/publicUserBackend/img/admin/neautral-avatar.png';
                } else {
                    $profile = $LoginUser->student_photo;
                }
            } elseif (Auth::guard('web')->user()->hasRole('parent')) {

//                $LoginUser = Guardian::where('user_id', Auth::user()->id)->first();

                $LoginUser = Guardian::where('user_id','=', Auth::guard('web')->user()->id)->first();


                if (empty($LoginUser)) {
                    $profile = 'assets/templates/wajeha/publicUserBackend/img/admin/neautral-avatar.png';
                } else {
                    $profile = $LoginUser->guardians_photo;
                }
            } else {
//                $LoginUser = Employee::where('id', Auth::user()->id)->first();
                $LoginUser = User::where('id', Auth::user()->id)->first();

                if (empty($LoginUser)) {
                    $profile = 'assets/templates/wajeha/publicUserBackend/img/admin/neautral-avatar.png';
                } else {
                    $profile = 'assets/templates/wajeha/publicUserBackend/img/admin/neautral-avatar.png';

//                    $profile = $LoginUser->staff_photo;
                }
            }
            return view('modules.site.templates.wajeha.backEnd.update_password', compact('profile', 'LoginUser'));
        } catch (\Exception $e) {
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }
    public function notificationUpdate(Request $request)
    {
        $notification = Notification::find($request->id);
        $notification->read_at = Carbon::now();
        $notification->save();
        return response()->json(['success' => 'success'],200);
    }

    public function notification_list()
    {
//        $notifications = Notification::where('user_id',auth()->user()->id)->where('role',auth()->user()->role_id)->latest()->get();
//        return view('backEnd.notifications.index', compact('notifications'));


        $notifications = Notification::where('user_id',auth()->user()->id)->latest()->get();
        return view('general::notifications.index', compact('notifications'));


    }
    public function notification_read_all()
    {
//        Notification::where('user_id',auth()->user()->id)->where('role',auth()->user()->role_id)->whereNull('read_at')->update(['read_at' => Carbon::now()]);
        Notification::where('user_id',auth()->user()->id)->whereNull('read_at')->update(['read_at' => Carbon::now()]);
        if (!request()->ajax()){
            return back();
        }
    }
    public function updatePassowrdStore(Request $request)
    {

        $this->validate($request,[
            'current_password' => "required",
            'new_password'  => "required|same:confirm_password|min:6|different:current_password",
            'confirm_password'  => 'required|min:6'
        ]);

        try {

            $user = Auth::user();
            if (Hash::check($request->current_password, $user->password)) {

                $user->password = bcrypt($request->new_password);
                $result = $user->save();

                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                    // return redirect()->back()->with('message-success', 'Password has been changed successfully');
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                    // return redirect()->back()->with('message-danger', 'Something went wrong, please try again');
                }
            } else {
                Toastr::error('Current password not match!', 'Failed');
                return redirect()->back();
                // return redirect()->back()->with('password-error', 'You have entered a wrong current password');
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function post_notification_read_all(Request $request)
    {
        $notifications = $request->notifications;

        if (!$notifications){
            return back();
        }

        Notification::where('user_id',auth()->user()->id)->whereNull('read_at')->whereIn('id', $notifications)->update(['read_at' => Carbon::now()]);
        Toastr::success(__('common.Selected notification marked as seen'), __('common.success'));
        return back();


    }



}

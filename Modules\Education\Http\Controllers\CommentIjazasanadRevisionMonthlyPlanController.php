<?php

namespace Modules\Education\Http\Controllers;

use App\IjazasanadRevisionPlan;
use Illuminate\Http\Request;

use App\Http\Controllers\Controller;

class CommentIjazasanadRevisionMonthlyPlanController extends Controller
{

    public function update(Request $request)
    {



        $plan = IjazasanadRevisionPlan::where('id', $request->get('id'))->update([
            'supervisor_comment' => $request->get('comment')
        ]);
        return response()->json(['message' => 'comment added to the plan']);


    }


    public function show(Request $request,$planId)
    {




        $plan = IjazasanadRevisionPlan::where('id', $planId)->update([
            'supervisor_comment' => $request->get('comment')
        ]);
        return response()->json(['message' => 'comment added to the plan']);


    }
}

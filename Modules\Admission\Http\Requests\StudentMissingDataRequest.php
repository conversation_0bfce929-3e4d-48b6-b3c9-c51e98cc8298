<?php

namespace Modules\Admission\Http\Requests;

use App\Rules\GenderValidation;
use Illuminate\Foundation\Http\FormRequest;

class StudentMissingDataRequest extends FormRequest
{

    /**
     * Define the validation rules for the request.
     *
     * @return array
     */
    public function rules()
    {


        return [
            'classId' => 'required|integer',
            'student_id' => 'nullable|integer|exists:students,id',
            'user_id' => 'required_without:student_id|integer|exists:users,id',
            'program_id' => 'required|integer',
            'center_id' => 'required|integer',
            'identity_number' => 'required_without:student_id',
            // 'gender' => ['required_without:student_id', new GenderValidation()],
            // 'dob' => 'required_without:student_id|date',
            'mobile' => 'required_without:student_id'
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'classId.required' => 'Please select a class.',
            'student_id.integer' => 'The selected student is invalid.',
            'student_id.exists' => 'The selected student does not exist.',
            'user_id.required_without' => 'User ID is required when student ID is not provided.',
            'user_id.integer' => 'The selected user is invalid.',
            'user_id.exists' => 'The selected user does not exist.',
            'program_id.required' => 'Please select a program.',
            'center_id.required' => 'Please select a center.',
            'identity_number.required_without' => 'Identity number is required when student ID is not provided.',
            'gender.required_without' => 'Please select a gender.',
            'gender.in' => 'Please select a valid gender (Male or Female).',
            'dob.required_without' => 'Date of birth is required when student ID is not provided.',
            'dob.date' => 'Please provide a valid date of birth.',
            'mobile.required_without' => 'Mobile number is required when student ID is not provided.',
            'mobile.numeric' => 'Please provide a valid mobile number.'
        ];
    }


}

<?php

namespace Modules\General\Http\Controllers;

use App\NouranyaEmailExclusion;
use App\NouranyaEmailSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Services\EmailService;
use App\Employee;
// Models might differ depending on your app structure
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Modules\General\Http\Requests\NouranyaRequest;

class NouranyaApprovalNotificationCommandsController extends Controller
{

    public function home()
    {


        return view('general::commands.home');
    }

    /**
     * Show Nouranya Email Command settings (frequency, exclusions, logs).
     */
    public function showNouranyaSettings()
    {
        // Load the single settings row (assuming there's only one row in table)
        $settings = DB::table('nouranya_email_settings')->first();

        // Fetch all exclusions
        $exclusions = DB::table('nouranya_email_exclusions')->get();

        // Fetch logs - only failed ones if you just want to allow "Resend"
        $failedLogs = DB::table('nouranya_email_log')
            ->where('status', 'failed')
            ->orderBy('created_at', 'desc')
            ->take(50)
            ->get();

        return view('general::commands.nouranya', compact('settings', 'exclusions', 'failedLogs'));
    }


    public function showMemorization()
    {
        return view('general::commands.memorization');
    }


    /**
     * Display the Trash Missed Clockouts management page.
     */
    public function showTrashClockOut()
    {
        return view('general::commands.trashClockOut');
    }

    /**
     * Display the Cache Trashed Students management page.
     */
    public function showCacheTrashed()
    {
        return view('general::commands.cacheTrashed');
    }

    /**
     * Display the Attendance Email Sponsors management page.
     */
    public function showAttendanceSponsors()
    {
        return view('general::commands.attendanceSponsors');
    }


    /**
     * Save Nouranya Email Command settings or add new exclusion.
     */




    public function updateAll(NouranyaRequest $request)
    {

        // 1) Update or Create Settings if frequency is present
        if ($request->filled('frequency')) {
            $settingId = $request->input('setting_id');
            $setting = $settingId
                ? NouranyaEmailSetting::find($settingId)
                : new NouranyaEmailSetting();

            if (!$setting) {
                $setting = new NouranyaEmailSetting();
            }

            $setting->frequency = $request->input('frequency');
            $setting->timezone = $request->input('timezone');

            if ($setting->frequency === 'daily') {
                $setting->daily_time = $request->input('daily_time') ?: '23:58';
                $setting->weekly_day = null;
                $setting->monthly_day = null;
            } elseif ($setting->frequency === 'weekly') {
                $setting->daily_time = null;
                $setting->weekly_day = $request->input('weekly_day');
                $setting->monthly_day = null;
            } elseif ($setting->frequency === 'monthly') {
                $setting->daily_time = null;
                $setting->weekly_day = null;
                $setting->monthly_day = $request->input('monthly_day');
            } else {
                // Frequency not set or invalid, ignore
            }

            $setting->save();
        }

        // 2) Add new exclusions if new_exclusions is present
        if ($request->filled('new_exclusions')) {
            foreach ($request->input('new_exclusions') as $email) {
                NouranyaEmailExclusion::updateOrCreate(
                    ['email' => $email],
                    [] // If you want to store a reason or timestamps, do so here
                );
            }
        }

        // 3) Remove exclusions if remove_exclusions is present
        if ($request->filled('remove_exclusions')) {
            NouranyaEmailExclusion::whereIn('id', $request->input('remove_exclusions'))->delete();
        }

        // 4) Resend logs if you have that logic
        if ($request->filled('resend_logs')) {
            // Example: fetch logs and attempt to resend
            // $logs = IjazasanadEmailLog::whereIn('id', $request->input('resend_logs'))->get();
            // foreach ($logs as $log) {
            //     // do your resend logic
            // }
        }

        return redirect()
            ->route('general.commands.nouranya.show')
            ->with('success', 'All changes saved successfully!');
    }





    /**
     * Resend a failed email attempt from the log.
     */
    public function resendFailedEmail(Request $request, EmailService $emailService)
    {
        $request->validate([
            'log_id' => 'required|integer',
        ]);

        $log = DB::table('nouranya_email_log')->where('id', $request->input('log_id'))->first();
        if (!$log) {
            return redirect()
                ->route('general.commands.nouranya.show')
                ->with('error', 'Log entry not found.');
        }

        // Attempt to resend
        try {
            $to = [
                'email' => $log->email,
                'name' => 'Resend Attempt', // or retrieve name from Employee if you have employee_id
            ];
            $subject = $log->subject;
            $view = 'emails.pending_plans_reminder'; // Or whichever you used
            $viewData = [
                'level1Count' => 0, // minimal placeholders
                'level2Count' => 0,
            ];

            //  Reuse your EmailService
            // $emailService = app(EmailService::class);
            $emailService = app(\App\Services\EmailService::class);
            $emailService->sendEmail($to, $subject, $view, $viewData, [], []);

            // Update the log status
            DB::table('nouranya_email_log')
                ->where('id', $log->id)
                ->update([
                    'status' => 'success',
                    'error_message' => null,
                    'updated_at' => now()
                ]);

            return redirect()
                ->route('general.commands.nouranya.show')
                ->with('success', 'Email resent successfully!');
        } catch (\Exception $e) {
            // If it still fails, update the log
            DB::table('nouranya_email_log')
                ->where('id', $log->id)
                ->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage(),
                    'updated_at' => now()
                ]);

            Log::error("Resend failed: " . $e->getMessage());

            return redirect()
                ->route('general.commands.nouranya.show')
                ->with('error', 'Failed to resend: ' . $e->getMessage());
        }
    }
}

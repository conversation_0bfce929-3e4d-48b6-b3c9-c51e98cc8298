<?php

namespace Modules\Education\Http\Controllers;

use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStaatisticsRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;


class ClassesDataForCalendarController extends Controller
{

    public function __construct()
    {
        $this->middleware('writeCurrentClassReportOnly', ['only' => ['create']]);
    }



    public function getClasses()
    {
        // Retrieve class data from database
        $classes = DB::table('classes')->get();

        // Initialize array to hold events
        $events = array();

        // Loop through each class
        foreach ($classes as $class) {
            // Create an event object
            $event = array();
            $event['title'] = '<img src="'.$class->icon.'" class="class-icon"> '.$class->name;
            $event['start'] = $class->date;

            // Add event to events array
            $events[] = $event;
        }

        // Return events as JSON
        return response()->json($events);
    }

    public function getClassDetails(Request $request)
    {
        // Retrieve class data from database for a specific date
        $classes = DB::table('classes')->where('date', $request->date)->get();

        // Return classes as JSON
        return response()->json($classes);
    }



}

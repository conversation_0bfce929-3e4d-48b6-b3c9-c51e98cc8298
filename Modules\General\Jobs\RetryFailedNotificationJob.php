<?php

namespace Modules\General\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Entities\JobNotificationFailure;
use Modules\JobSeeker\Entities\JobNotificationSetup;
use Mo<PERSON>les\JobSeeker\Services\JobService;
use Exception;

class RetryFailedNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;
    
    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 180; // 3 minutes
    
    /**
     * The maximum number of unhandled exceptions to allow before failing.
     *
     * @var int
     */
    public $maxExceptions = 3;
    
    /**
     * The ID of the failure record to retry
     *
     * @var int
     */
    protected $failureId;
    
    /**
     * Create a new job instance.
     *
     * @param int $failureId
     * @return void
     */
    public function __construct(int $failureId)
    {
        $this->failureId = $failureId;
        $this->connection = 'job_notifications';
        $this->queue = 'notification_retries'; // Dedicated queue for retries
    }
    
    /**
     * Execute the job.
     *
     * @param JobService $jobService
     * @return void
     */
    public function handle(JobService $jobService)
    {
        try {
            Log::info("RetryFailedNotificationJob: Starting retry for failure ID {$this->failureId}");
            
            // Get the failure record
            $failure = JobNotificationFailure::find($this->failureId);
            
            if (!$failure) {
                Log::error("RetryFailedNotificationJob: Failure record not found", [
                    'failure_id' => $this->failureId
                ]);
                return;
            }
            
            // Check if already resolved
            if ($failure->status === 'resolved') {
                Log::info("RetryFailedNotificationJob: Failure already resolved", [
                    'failure_id' => $this->failureId
                ]);
                return;
            }
            
            Log::info("RetryFailedNotificationJob: Processing failure", [
                'failure_id' => $this->failureId,
                'error_type' => $failure->error_type,
                'retry_count' => $failure->retry_count
            ]);
            
            // Different retry strategy based on error type
            switch ($failure->error_type) {
                case 'email_send':
                    $this->retryEmailSend($failure, $jobService);
                    break;
                    
                case 'setup_processing':
                    $this->retrySetupProcessing($failure, $jobService);
                    break;
                    
                default:
                    $this->retryGeneric($failure, $jobService);
                    break;
            }
        } catch (Exception $e) {
            Log::error("RetryFailedNotificationJob: Error processing retry", [
                'failure_id' => $this->failureId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Flag as a failed job after max retries
            if ($this->attempts() >= $this->tries) {
                if ($failure ?? null) {
                    $failure->markAsFailed();
                }
                
                Log::error("RetryFailedNotificationJob: Max retries reached for failure", [
                    'failure_id' => $this->failureId
                ]);
            }
            
            throw $e; // Let Laravel retry the job according to its own retry mechanism
        }
    }
    
    /**
     * Retry an email sending failure
     *
     * @param JobNotificationFailure $failure
     * @param JobService $jobService
     * @return void
     */
    protected function retryEmailSend(JobNotificationFailure $failure, JobService $jobService)
    {
        // Get the setup and job if available
        $setup = $failure->setup;
        $job = $failure->job;
        
        if (!$setup) {
            Log::error("RetryFailedNotificationJob: Setup not found for email failure", [
                'failure_id' => $failure->id,
                'setup_id' => $failure->setup_id
            ]);
            $failure->markAsFailed();
            return;
        }
        
        // Try to send the email directly
        try {
            $recipient = [
                'email' => $failure->recipient_email,
                'name' => $failure->recipient_email // Basic fallback
            ];
            
            $success = false;
            
            if ($job) {
                // We have both job and setup - can retry a specific job notification
                $success = $jobService->sendJobNotificationToRecipient($job, $setup, $recipient);
            } else {
                // We only have setup - need to retry all recent jobs for this setup
                $success = $jobService->sendSetupNotificationToRecipient($setup, $recipient);
            }
            
            if ($success) {
                $failure->markAsResolved();
                Log::info("RetryFailedNotificationJob: Successfully resolved email failure", [
                    'failure_id' => $failure->id,
                    'recipient' => $failure->recipient_email
                ]);
            } else {
                Log::warning("RetryFailedNotificationJob: Failed to resolve email failure", [
                    'failure_id' => $failure->id,
                    'recipient' => $failure->recipient_email
                ]);
                
                // If this was the last retry attempt, mark as failed
                if ($failure->retry_count >= 2) {
                    $failure->markAsFailed();
                }
            }
        } catch (Exception $e) {
            Log::error("RetryFailedNotificationJob: Error retrying email send", [
                'failure_id' => $failure->id,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Retry a setup processing failure
     *
     * @param JobNotificationFailure $failure
     * @param JobService $jobService
     * @return void
     */
    protected function retrySetupProcessing(JobNotificationFailure $failure, JobService $jobService)
    {
        // Get the setup
        $setup = $failure->setup;
        
        if (!$setup) {
            Log::error("RetryFailedNotificationJob: Setup not found", [
                'failure_id' => $failure->id,
                'setup_id' => $failure->setup_id
            ]);
            $failure->markAsFailed();
            return;
        }
        
        try {
            // Dispatch a new processing job for this setup
            ProcessJobNotificationSetupJob::dispatch($setup->id);
            
            $failure->markAsResolved();
            Log::info("RetryFailedNotificationJob: Re-dispatched setup processing job", [
                'failure_id' => $failure->id,
                'setup_id' => $setup->id
            ]);
        } catch (Exception $e) {
            Log::error("RetryFailedNotificationJob: Error re-dispatching setup processing", [
                'failure_id' => $failure->id,
                'setup_id' => $setup->id,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Generic retry for other failure types
     *
     * @param JobNotificationFailure $failure
     * @param JobService $jobService
     * @return void
     */
    protected function retryGeneric(JobNotificationFailure $failure, JobService $jobService)
    {
        Log::info("RetryFailedNotificationJob: Generic retry for failure", [
            'failure_id' => $failure->id,
            'error_type' => $failure->error_type
        ]);
        
        // For generic failures with a setup, try re-processing the setup
        if ($failure->setup_id) {
            $setup = $failure->setup;
            
            if ($setup) {
                try {
                    // Dispatch a new processing job for this setup
                    ProcessJobNotificationSetupJob::dispatch($setup->id);
                    
                    $failure->markAsResolved();
                    Log::info("RetryFailedNotificationJob: Re-dispatched setup processing job for generic failure", [
                        'failure_id' => $failure->id,
                        'setup_id' => $setup->id
                    ]);
                    return;
                } catch (Exception $e) {
                    Log::error("RetryFailedNotificationJob: Error re-dispatching setup for generic failure", [
                        'error' => $e->getMessage()
                    ]);
                    
                    throw $e;
                }
            }
        }
        
        // If we can't do anything specific, mark as failed after max retries
        if ($failure->retry_count >= 2) {
            $failure->markAsFailed();
            Log::warning("RetryFailedNotificationJob: Marking generic failure as failed after max retries", [
                'failure_id' => $failure->id
            ]);
        }
    }
} 
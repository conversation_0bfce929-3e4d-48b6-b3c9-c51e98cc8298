---
description: Executive Summary standards for implementation plans, PRs, and design docs (supersedes 120-word limit)
globs: **/*
alwaysApply: true
---

- **Purpose and Scope**
  - Every implementation plan, refactor plan, PR description, and internal design doc MUST begin with an Executive Summary.
  - The summary precedes the full content and stands alone so a reader can grasp the what, why, how, when, and key risks without reading further.

- **Length and Format**
  - Target length: 1/2 to 1 page depending on scope and complexity.
  - Clear, professional, accessible language informed by technical realities.
  - Prefer tight prose paragraphs or concise bullets; use headings sparingly.
  - This rule supersedes the older “≤120 words / 3–6 bullets” limit.

- **Required Contents (cover briefly, not code-level detail)**
  - Goals and Scope: What we are doing and what is explicitly out of scope.
  - Value and Outcomes: Why this matters; expected user/business impact.
  - Approach (How): High-level architecture/strategy, not implementation minutiae.
  - Timeline (When): Key milestones, sequencing, and critical dependencies.
  - Risks and Mitigations: Top risks with succinct mitigation strategies.
  - Assumptions and Constraints: Notable assumptions, limitations, or external dependencies.
  - Deliverables and Success Criteria: What will be delivered and how success is measured.

- **Tone and Focus**
  - Emphasize value, outcomes, and strategic fit over granular code details.
  - Keep it skimmable; avoid jargon and unexplained acronyms.
  - Ground claims in realities (tech constraints, data, and operational considerations).

- **Consistency Requirements**
  - Place the Executive Summary at the very beginning of the document.
  - Apply consistently to task plans, large refactors, PRs, and design docs.
  - Maintain alignment with related rules: see [cursor_rules.mdc](mdc:.cursor/rules/cursor_rules.mdc).

- **Quality Checklist (use before submitting)**
  - Does it stand alone and communicate what/why/how/when/risks?
  - Is it 1/2–1 page and free of code-level details?
  - Are value, outcomes, and strategic fit clearly stated?
  - Are constraints, assumptions, and dependencies concisely listed?
  - Are success criteria and deliverables articulated?

- **Example Outline (bullet style)**
  - Goals & Scope: Migrate notifications to provider-aware pipeline; exclude legacy CSV import.
  - Value & Outcomes: +20% delivery rate, fewer manual retries, clearer audit logs.
  - Approach: New orchestration service + queued jobs; preserve existing endpoints.
  - Timeline: 2 sprints; Sprint 1 orchestration + tests, Sprint 2 rollout + metrics.
  - Risks: Provider rate limits; Mitigation: adaptive backoff + circuit breaker.
  - Assumptions/Constraints: MySQL 8.0, no schema changes in v1.
  - Deliverables & Success: SLA 99% within 5m; dashboard metrics live.

- **Example Outline (prose style)**
  - This project delivers a provider-aware notification pipeline that improves reliability and observability while preserving current APIs. In two sprints, we will introduce a lightweight orchestration service and queue-backed workers, instrumented with delivery metrics and audit logs. Success is defined by a 20% increase in delivery rate and 99% of notifications processed within five minutes. Key risks include provider rate limits and upstream outages; we mitigate with adaptive backoff and a circuit breaker. We assume MySQL 8.0 and no schema changes for v1. This work aligns with the platform strategy to standardize background processing and reduce manual interventions.


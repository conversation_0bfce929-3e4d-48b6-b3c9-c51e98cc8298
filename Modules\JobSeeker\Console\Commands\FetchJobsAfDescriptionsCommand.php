<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Console\Commands;

use Illuminate\Console\Command;
use Modules\JobSeeker\Services\JobsAfService;
use Illuminate\Support\Facades\Log;

class FetchJobsAfDescriptionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jobseeker:fetch-jobs-af-descriptions {--limit=50 : Number of jobs to process}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch detailed job descriptions from jobs.af';

    /**
     * @var JobsAfService
     */
    protected JobsAfService $jobsAfService;

    /**
     * Create a new command instance.
     *
     * @param JobsAfService $jobsAfService
     */
    public function __construct(JobsAfService $jobsAfService)
    {
        parent::__construct();
        $this->jobsAfService = $jobsAfService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $limit = (int) $this->option('limit');
            
            $this->info('Starting jobs.af job description extraction...');
            Log::info('Starting jobs.af job description extraction via command', ['limit' => $limit]);

            // Extract detailed job descriptions from jobs.af pages
            $descriptionStats = $this->jobsAfService->fetchJobDescriptions(null, $limit);
            
            $this->info('Jobs.af job description extraction completed:');
            $this->line('  - Total jobs processed: ' . $descriptionStats['total']);
            $this->line('  - Successfully updated: ' . $descriptionStats['success']);
            $this->line('  - Already complete: ' . $descriptionStats['existing']);
            $this->line('  - Failed to update: ' . $descriptionStats['failed']);
            $this->line('  - Skipped (no data/slug): ' . $descriptionStats['skipped']);
            $this->line('  - Network errors: ' . $descriptionStats['network_errors']);
            
            // Get additional statistics from the database
            $totalJobsInDb = \Modules\JobSeeker\Entities\Job::where('source', 'jobs.af')->count();
            $recentJobs = \Modules\JobSeeker\Entities\Job::where('source', 'jobs.af')
                ->where('publish_date', '>=', now()->subDays(7))
                ->count();
            
            $this->info('');
            $this->info('=== JOBS.AF DESCRIPTION EXTRACTION SUMMARY ===');
            $this->line('  - Total jobs.af jobs in database: ' . $totalJobsInDb);
            $this->line('  - Jobs published in last 7 days: ' . $recentJobs);
            $this->line('  - Description extraction stats:');
            $this->line('    * Total processed: ' . $descriptionStats['total']);
            $this->line('    * Successfully updated: ' . $descriptionStats['success']);
            $this->line('    * Already had descriptions: ' . $descriptionStats['existing']);
            $this->line('    * Failed to update: ' . $descriptionStats['failed']);
            $this->line('    * Skipped (no data/slug): ' . $descriptionStats['skipped']);
            $this->line('    * Network errors: ' . $descriptionStats['network_errors']);
            
            Log::info('Jobs.af job description extraction completed via command', [
                'description_stats' => $descriptionStats,
                'total_jobs_in_db' => $totalJobsInDb,
                'recent_jobs_count' => $recentJobs
            ]);
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Error during jobs.af job description extraction: ' . $e->getMessage());
            Log::error('Error during jobs.af job description extraction via command', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }
} 
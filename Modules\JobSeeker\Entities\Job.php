<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

final class Job extends Model
{
    protected $table = 'jobs';
    protected $guarded = ['id'];
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'slug', 
        'position', 
        'number_of_vacancy', 
        'vacancy_number', 
        'is_featured', 
        'locations', 
        'contract_type', 
        'work_type', 
        'gender',
        'company_name',
        'company_logo',
        'company_profile_link',
        'expire_date',
        'publish_date',
        'salary',
        'views_count',
        'can_apply_online',
        'is_new',
        'applications_count',
        'description',
        'about_company',
        'job_summary',
        'duties_responsibilities',
        'job_requirements',
        'experience',
        'submission_guideline',
        'source',
        // New fields for de-duplication
        'normalized_company_name',
        'normalized_job_title',
        'normalized_location',
        'job_fingerprint',
        'master_job_id',
        'source_ids'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_featured' => 'boolean',
        'can_apply_online' => 'boolean',
        'is_new' => 'boolean',
        'views_count' => 'integer',
        'applications_count' => 'integer',
        'number_of_vacancy' => 'integer',
        'expire_date' => 'date',
        'publish_date' => 'date',
        'source_ids' => 'array', // Cast JSON to array
    ];

    /**
     * Find a job by its slug
     *
     * @param string $slug
     * @return Job|null
     */
    public static function findBySlug(string $slug): ?Job
    {
        Log::info("Finding job with slug: {$slug}");
        return self::where('slug', $slug)->first();
    }

    /**
     * Scope a query to only include featured jobs
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', 1);
    }

    /**
     * Scope a query to only include jobs that haven't expired
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('expire_date', '>=', Carbon::now()->format('Y-m-d'));
    }

    /**
     * Scope a query to only include jobs published within a specific number of days
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $days
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecentlyPublished($query, int $days = 2)
    {
        $cutoff = Carbon::now()->subDays($days);
        return $query->where('publish_date', '>=', $cutoff);
    }

    /**
     * Get the categories associated with this job.
     * Uses the pivot table job_category_pivot for the relationship.
     *
     * @deprecated This relationship will be removed after migration to provider categories
     * @return BelongsToMany
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(
            JobCategory::class,
            'job_category_pivot',
            'job_id',
            'category_id'
        )->withTimestamps();
    }

    /**
     * Get the provider categories associated with this job.
     * Uses the new job_provider_category_pivot table for the relationship.
     * This is the correct relationship pattern.
     *
     * @return BelongsToMany
     */
    public function providerCategories(): BelongsToMany
    {
        return $this->belongsToMany(
            \Modules\JobSeeker\Entities\ProviderJobCategory::class,
            'job_provider_category_pivot',
            'job_id',
            'provider_category_id'
        )->withTimestamps();
    }

    /**
     * Get the canonical categories for this job (derived from provider categories).
     * This method provides the canonical categories that job seekers should see.
     *
     * @return \Illuminate\Support\Collection
     */
    public function canonicalCategories(): \Illuminate\Support\Collection
    {
        return $this->providerCategories()
            ->with('canonicalCategory')
            ->get()
            ->pluck('canonicalCategory')
            ->filter() // Remove null values
            ->unique('id'); // Remove duplicates based on ID
    }

    /**
     * Get the master job if this job is a duplicate.
     *
     * @return BelongsTo
     */
    public function masterJob(): BelongsTo
    {
        return $this->belongsTo(Job::class, 'master_job_id');
    }

    /**
     * Get all duplicate jobs that reference this job as their master.
     *
     * @return HasMany
     */
    public function duplicateJobs(): HasMany
    {
        return $this->hasMany(Job::class, 'master_job_id');
    }

    /**
     * Check if this job is a master job (has duplicates).
     *
     * @return bool
     */
    public function isMasterJob(): bool
    {
        return $this->duplicateJobs()->exists();
    }

    /**
     * Check if this job is a duplicate (has a master job).
     *
     * @return bool
     */
    public function isDuplicate(): bool
    {
        return !is_null($this->master_job_id);
    }

    /**
     * Get the effective job (master job if this is a duplicate, or this job if it's master/standalone).
     *
     * @return Job
     */
    public function getEffectiveJob(): Job
    {
        return $this->isDuplicate() ? $this->masterJob : $this;
    }

    /**
     * Get the notification logs for this job.
     *
     * @return HasMany
     */
    public function sentNotifications(): HasMany
    {
        return $this->hasMany(JobNotificationSentJob::class, 'job_id');
    }
} 
<?php

namespace Modules\Leave\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LeaveDefineStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'role_id' => 'required',
            'leave_type_id' => 'required',
            'total_days' => 'required',
            'max_forward' => 'required_if:balance_forward,==,1',


        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages() {
        return [
            'leave_type.unique' => 'Combination of Role & Leave Type is not unique. ',
        ];
    }
}

@extends('layouts.hound') @section('mytitle' , $class->name ." Class EducationalReports")
@section('content')
<div class="clearfix">
  <div class="row">
    <div class="col-md-12">
      <div class="panel panel-primary card-view">
        <div class="panel-heading">
          <h5 class="txt-light">
            Create Class Report
          </h5>
        </div>
        <div class="panel-body">
          <a href="{{ url('workplace/education/classes/'.$class->id.'/reports') }}" title="Back"><button
              class="btn btn-warning btn-xs">
              <i class="fa fa-arrow-left" aria-hidden="true"></i> Back
            </button></a>
          <br />
          <br />
          @if ($errors->any())
          <ul class="alert alert-danger">
            @foreach ($errors->all() as $error)
            <li>{{ $error }}</li>
            @endforeach
          </ul>
          @endif
          <div class="row">
            {!! Form::open(['route' => 'class.reports.update' ]) !!} {!!
            Form::hidden('class_id' , $class->id) !!} {!!
            Form::hidden('report_id' , $report->id) !!}
            <div class="col-md-6">
              <label for="">Class Name</label>
              <div class="form-control">
                {{ $class->name }}
              </div>
            </div>
            <div class="col-md-6">
              <label for="">Teacher Name</label>
              <div class="form-control">
                {{ $report->teacher->full_name }}
              </div>
            </div>
            <div class="col-md-6">
              <label for="">Subject</label>
              <div class="form-control">
                @if ($report->subject_id == 0)
                {{ $report->program->title }} Program [All Levels & Subjects]
                @else
                {{ $report->subject->title }}
                @endif
              </div>
            </div>

            <div class="col-md-6">
              <label for="">Class Date and Time</label>
              <div class="form-control">
                {{ $report->class_time }}
              </div>
            </div>
            <div class="clearfix">
              <br />
            </div>

            <div class="col-md-12">
              @if($report->status == 'new_report' || request()->edit ==
              'attendace')
              <div class="col-md-12">
                <h4>Teacher Attendance</h4>

                <label for="teacher_attendance" class="col-sm-2">{{ $report->teacher->full_name }}</label>
                <div class="col-sm-10">
                  <label class="btn btn-xs btn-primary">
                    <input type="radio" name="teacher_attendance" class="teacher_attendance required" value="attended"
                      checked="checked" />
                    Attended
                  </label>
                  <label class="btn btn-xs btn-primary">
                    <input type="radio" name="teacher_attendance" class="teacher_attendance required" value="absent" />
                    Absent
                  </label>
                </div>
              </div>
              <div class="col-md-12" id="student_attendance_list">
                <h4>Students Attendance</h4>
                {!! Form::hidden('report_id' , $report->id) !!}
                {{-- $class->students->filter(function ($d) use ($report){
                            return $d->pivot->start_date <= $report->class_time && $d->status == "active";
                        }) --}}

                <ul class="list-group">
                  @foreach ($class->studentsAtDate($report->class_time) as
                  $student)
                  <li class="list-group-item row">
                    <div class="col-md-4">
                      {{ $student->full_name }}
                    </div>
                    <div class="col-md-8">
                      <div class="row">
                        <label class="btn btn-xs col-xs-12 col-sm-3 text-left btn-primary">{!!
                          Form::radio('student_attendance['.$student->id.']' ,
                          'on_time' , ['class' => 'required' ]) !!} Attended on
                          time</label>
                        <label class="btn btn-xs col-xs-12 col-sm-3 text-left btn-primary">{!!
                          Form::radio('student_attendance['.$student->id.']' ,
                          'late' , ['class' => 'required' ]) !!} Attended
                          Late</label>
                        <label class="btn btn-xs col-xs-12 col-sm-3 text-left btn-primary">{!!
                          Form::radio('student_attendance['.$student->id.']' ,
                          'excused' , ['class' => 'required' ]) !!} Absent With
                          Reason</label>
                        <label class="btn btn-xs col-xs-12 col-sm-3 text-left btn-primary">{!!
                          Form::radio('student_attendance['.$student->id.']' ,
                          'absent' , ['class' => 'required' ]) !!} Absent</label>
                      </div>
                    </div>
                  </li>
                  @endforeach
                </ul>
              </div>

              @elseif($report->status == 'attendance_submited')
              <h4>Students Performance</h4>
              @if ($report->subject_id == 0)
              @isset($report->program->setting['special_program_code'])
              @if($report->program->setting['special_program_code'] == 'hefz')
              @include('education::classes.reports.hefz.evaluation_form') @endif
              @endisset @else @foreach ($report->attendace as $student)
              <div class="row">
                @if($student->attendance != 'absent' && $student->attendance !=
                'excused')
                <div class="col-md-4">{{ $student->student->full_name }}</div>
                <div class="col-md-4">
                  {!!
                  Form::select('class_report_evaluation['.$student->student->id.'][content_id]',
                  $subject->contents->pluck('title' , 'id') , null , ['class' =>
                  'form-control content_list' , 'id' =>
                  'contents_'.$student->student->id , 'student_id' =>
                  $student->student->id ]) !!}
                </div>
                <div class="col-md-4" id="contents_{{$student->student->id}}_evaluation">
                  <p></p>
                </div>
                @endif
              </div>
              @endforeach @endif @elseif($report->status == 'completed')
              <br />
              <h4>Students Attendance</h4>
              <br />
              <table class="table table-responsive table-striped">
                @foreach ($report->attendace as $student)
                <tr>
                  <td>
                    {{ $student->student->full_name }}
                  </td>
                  <td>
                    {{ ucwords(str_replace('_' , ' ' ,  $student->attendance)) }}
                  </td>
                  <td></td>
                </tr>
                @endforeach
              </table>

              <h4>Students Performance</h4>
              @if ($report->subject_id == 0 && isset($report->program->setting['special_program_code']))
              @if($report->program->setting['special_program_code'] == 'hefz')
              @include('education::classes.reports.hefz.result') @endif
              @else

              <table class="table table-responsive table-striped">
                <thead>
                  <tr>
                    <th>Student Name</th>
                    <th>Lesson</th>
                    <th>Result</th>
                    <th>Notes</th>
                  </tr>
                </thead>
                @foreach($report->lessons as $lesson_report)
                <tr>
                  <td>
                    {{ $lesson_report->student->full_name }}
                  </td>
                  <td>
                    {{ $lesson_report->content->title }}
                  </td>
                  <td>
                    @foreach($lesson_report->evaluations as $evaluation)
                    {{ $evaluation->result->info->title }} :
                    <span class="label label-info">
                      [{{ $evaluation->result->code }}]
                      {{ $evaluation->result->title }}
                    </span>
                    @endforeach
                  </td>
                  <td>

                    {{-- @if($lesson_report->result->extra_field)
                    Have to repeat
                    @endif --}}
                  </td>
                </tr>
                @endforeach
              </table>
              @endif

              @endif
            </div>
            @if($report->status != 'completed')
            <div class="text-center">
              <button type="submit" class="btn btn-success">Save Report</button>
            </div>
            @endif {!! Form::close() !!}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
@include('jssnippets.flatpickr') @endsection @section('js') @if($report->status
== 'attendance_submited')
<script>
  var content = {};

  var addEvaluationOptions = function(content_id, el_id) {
    if (content[content_id] === undefined) {
      var request = $.ajax({
        type: "get",
        url: "{{ url('/workplace/curriculum/contents/') }}/" + content_id,
        dataType: "json",
        success: function(response) {
          content[content_id] = response;

          updateEvalDom(content_id, el_id);
        }
      }).done(function() {});
    } else {
      updateEvalDom(content_id, el_id);
    }
  };

  var updateEvalDom = function(content_id, el_id) {
    var student_id = $("#" + el_id).attr("student_id");
    $.each(content[content_id]["evaluation_schemas"], function(sid, schema) {
      if (schema["type"] == "marks") {
        var options = prepareMarksOption(sid, student_id);
      } else if (schema["type"] == "grades") {
        var options = prepareGradesOption(sid, student_id, schema);
      }
      $("#" + el_id + "_evaluation").append(options);
    });
  };

  var prepareMarksOption = function(sid, student_id) {
    console.log("Preparing DOM " + sid);
    return sid;
  };

  var prepareGradesOption = function(sid, student_id, schema) {
    var options = schema.options;

    console.log("Preparing DOM " + sid);

    var html = "<div>";
    html += "<label>" + schema.title + "</label> ";

    options.forEach(option => {
      html +=
        "<input type='radio' name='class_report_evaluation[" +
        student_id +
        "][schemas][" +
        option.evaluation_schema_id +
        "]' value='" +
        option.id +
        "'  />" +
        option.title +
        " [" +
        option.code +
        "]"; //class_report_evaluation[1][content_id]
    });
    html += "</div>";

    return html;
  };

  $(document).ready(function() {
    $(".content_list").each(function(i, el) {
      addEvaluationOptions(el.value, el.id);
    });

    $(".content_list").change(function() {
      $("#" + this.id + "_evaluation").html("");
      addEvaluationOptions(this.value, this.id);
    });
  });
</script>
@else
<script>
  $(document).ready(function() {
    $('[name="teacher_attendance"]').click(function() {
      if (this.value == "absent") {
        $("#student_attendance_list").hide();
      } else {
        $("#student_attendance_list").show();
      }
    });
  });
</script>
@endif @append
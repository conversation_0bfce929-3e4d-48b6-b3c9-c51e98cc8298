<?php

namespace Modules\Education\Http\Controllers;

use App\Classes;
use App\Employee;
use App\Guardian;
use App\Section;
use App\Student;
use App\Subject;
use App\YearCheck;
use App\OnlineExam;
use App\ApiBaseMethod;
use App\Notification;
use App\QuestionBank;
use App\AssignSubject;
use App\OnlineExamMark;
use App\GeneralSettings;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\OnlineExamQuestion;
use App\StudentTakeOnlineExam;
use Illuminate\Support\Facades\DB;
use App\OnlineExamQuestionAssign;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use App\OnlineExamQuestionMuOption;
use Illuminate\Support\Facades\Schema;
use App\OnlineExamStudentAnswerMarking;
use Illuminate\Support\Facades\Validator;
use Modules\OnlineExam\Entities\InfixOnlineExam;
use Modules\OnlineExam\Entities\InfixStudentTakeOnlineExam;
use App\Http\Controllers\Controller;


class AccessClassOnlineExamsController extends Controller
{



    public function __invoke(Request $request,$classId){




        $class = Classes::find($classId);
        $online_exams = OnlineExam::where('status', '!=', 2)
            ->where('class_id',$classId)
            ->get();

        $online_exam = null;
        return view('education::examination.online_exam', compact('class','classId','online_exam','online_exams' ));




        dd($classId,$studentId);
    }
}
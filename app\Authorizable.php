<?php
namespace App;

trait Authorizable
{
    private $abilities = [
        'index' => 'list',
        'edit' => 'update',
        'show' => 'view',
        'update' => 'update',
        'create' => 'add',
        'store' => 'add',
        'destroy' => 'delete'
    ];

    /**
     * Override of callAction to perform the authorization before
     *
     * @param $method
     * @param $parameters
     * @return mixed
     */
    public function callAction($method, $parameters)
    {
        if( $ability = $this->getAbility($method) ) {
            $this->authorize($ability);
        }

        return parent::callAction($method, $parameters);
    }

    public function getAbility($method)
    {
        $routeName = explode('.', \Request::route()->getName());
        $action = array_get($this->getAbilities(), $method);
// dd($action,$routeName);
        if($action == 'list'){
            return $action ? $action . ' ' . $routeName[0] : null;
        }
        return $action ? $action . ' ' . str_singular($routeName[0]) : null;


    }

    private function getAbilities()
    {
        return $this->abilities;
    }

    public function setAbilities($abilities)
    {
        $this->abilities = $abilities;
    }
}

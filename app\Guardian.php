<?php

namespace App;

use Config;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use App\Notifications\GuardianResetPassword;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;


class Guardian extends Model
{
//    use Notifiable;


    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'document_file_1','document_file_2','document_file_3','document_file_4','name', 'email', 'password','organization_id','guardians_relation','guardians_address','guardians_photo',
        'full_name', 'full_name_trans', 'gender', 'date_of_birth', 'occupation', 'nationality', 'mobile', 'status','user_id'
        
    ];

    protected static function boot()
    {
        parent::boot();
        // dd(config('organization_id'));

        static::addGlobalScope(new OrganizationScope);
    }

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];
    
    // protected $attributes;// = array(
    //    'organization_id' => $this->organizationId(),
    // );

    // public static function boot()
    // {
    //     parent::boot();

    //     static::creating(function ($model)
    //     {
    //         $model->attributes['organization_id'] = Config::get('organization_id');
    //     });
    //     // $this->attributes = ['organization_id' => ];
    // }
    // public function setOrganizationIdAttribute($value='')
    // {
    //     $this->attributes['organization_id'] = Config::get('organization_id');
    // }
    /**
     * Send the password reset notification.
     *
     * @param  string  $token
     * @return void
     */


    public function sendPasswordResetNotification($token)
    {
        $this->notify(new GuardianResetPassword($token));
    }

    public function students()
    {
        return $this->hasMany('App\Student');
    }

    public static function myChildren(){

        try {

            if (Auth::guard('web')->user()->parent()->exists()) {


                $user = Auth::guard("web")->user();
                $parent = Guardian::where('user_id', $user->id)->first();

                $childrens = Student::where('guardian_id', $parent->id)->where('status','offered')->get();


                session(['childrens' => $childrens]);


//            if(Session::get('childrens') == ""){
//                $user = Auth::user();
//                $parent = Guardian::where('user_id', $user->id)->first();
//                $childrens = Student::where('guardian_id', $parent->id)->get();
//                session(['childrens' => $childrens]);
////                Session::put('childrens', $childrens);
//            }
                return Session::get('childrens');
            }

        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    }

    public function parent_user(){
        return $this->belongsTo('App\User');
    }
}

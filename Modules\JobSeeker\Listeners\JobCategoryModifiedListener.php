<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Listeners;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Modules\JobSeeker\Events\JobCategoryModifiedEvent;
use <PERSON><PERSON>les\JobSeeker\Services\JobService;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\JobNotificationSetup;
use Mo<PERSON>les\JobSeeker\Entities\JobCategory;
use Modules\JobSeeker\Notifications\CategoryChangeNotification;
use Illuminate\Support\Facades\Notification;

final class JobCategoryModifiedListener
{
    /**
     * Handle the event.
     *
     * @param JobCategoryModifiedEvent $event
     * @return void
     */
    public function handle(JobCategoryModifiedEvent $event): void
    {
        $startTime = microtime(true);
        $contextData = [
            'category_id' => $event->jobCategory->id,
            'category_name' => $event->jobCategory->name,
            'action' => $event->action,
            'replacement_category_id' => $event->replacementCategoryId,
            'context' => $event->context,
            'timestamp' => now()->toDateTimeString()
        ];
        
        Log::info('JobCategoryModifiedListener: ENTRY - Starting category modification handling', $contextData);
        
        try {
            // Flush the category cache
            JobService::flushCategoryCache();

            Log::info('JobCategoryModifiedListener: Category cache flushed successfully', $contextData);

            // Handle specific actions
            switch ($event->action) {
                case 'deleted':
                case 'archived':
                    Log::info('JobCategoryModifiedListener: Processing category removal', $contextData);
                    $this->handleCategoryRemoval($event);
                    break;
                case 'updated':
                    Log::info('JobCategoryModifiedListener: Processing category update', $contextData);
                    $this->handleCategoryUpdate($event);
                    break;
                case 'created':
                    Log::info('JobCategoryModifiedListener: Processing category creation (no special handling)', $contextData);
                    break;
                default:
                    Log::warning('JobCategoryModifiedListener: Unknown action type', array_merge($contextData, [
                        'unknown_action' => $event->action
                    ]));
            }

            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            Log::info('JobCategoryModifiedListener: EXIT - Successfully completed category modification handling', array_merge($contextData, [
                'execution_time_ms' => $executionTime
            ]));

        } catch (\Exception $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            Log::error('JobCategoryModifiedListener: EXCEPTION - Error handling category modification', array_merge($contextData, [
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'execution_time_ms' => $executionTime,
                'trace' => $e->getTraceAsString()
            ]));
            
            // Re-throw to ensure proper error handling upstream
            throw $e;
        }
    }

    /**
     * Handle category deletion or archival
     * 
     * @param JobCategoryModifiedEvent $event
     * @return void
     */
    private function handleCategoryRemoval(JobCategoryModifiedEvent $event): void
    {
        $category = $event->jobCategory;
        $replacementCategoryId = $event->replacementCategoryId;

        // Find all notification setups that use this category
        $affectedSetups = JobNotificationSetup::whereHas('categories', function ($query) use ($category) {
            $query->where('job_categories.id', $category->id);
        })->with(['jobSeeker', 'categories'])->get();

        Log::info('JobCategoryModifiedListener: Found notification setups affected by category removal', [
            'category_id' => $category->id,
            'category_name' => $category->name,
            'affected_setups_count' => $affectedSetups->count(),
            'replacement_category_id' => $replacementCategoryId
        ]);

        if ($affectedSetups->isEmpty()) {
            return; // No setups to update
        }

        DB::beginTransaction();
        try {
            foreach ($affectedSetups as $setup) {
                if ($replacementCategoryId) {
                    // Admin specified a replacement category
                    $this->updateSetupWithReplacementCategory($setup, $category, $replacementCategoryId);
                } else {
                    // No replacement specified - mark for review
                    $this->markSetupForReview($setup, $category);
                }
            }
            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('JobCategoryModifiedListener: Error updating notification setups', [
                'category_id' => $category->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Update a notification setup with a replacement category
     * 
     * @param JobNotificationSetup $setup
     * @param JobCategory $oldCategory
     * @param int $replacementCategoryId
     * @return void
     */
    private function updateSetupWithReplacementCategory(
        JobNotificationSetup $setup, 
        JobCategory $oldCategory, 
        int $replacementCategoryId
    ): void {
        // Get the replacement category
        $replacementCategory = JobCategory::find($replacementCategoryId);
        
        if (!$replacementCategory) {
            Log::error('JobCategoryModifiedListener: Replacement category not found', [
                'replacement_category_id' => $replacementCategoryId,
                'setup_id' => $setup->id
            ]);
            // Fall back to marking for review
            $this->markSetupForReview($setup, $oldCategory);
            return;
        }

        // Check if the setup already has this replacement category
        $hasReplacementCategory = $setup->categories()->where('job_categories.id', $replacementCategoryId)->exists();
        
        if (!$hasReplacementCategory) {
            // Replace the old category with the new one
            $categoryIds = $setup->categories()->pluck('job_categories.id')->toArray();
            $categoryIds = array_filter($categoryIds, fn($id) => $id != $oldCategory->id);
            $categoryIds[] = $replacementCategoryId;
            
            $setup->categories()->sync($categoryIds);
            $setup->category_count = count($categoryIds);
            $setup->save();

            Log::info('JobCategoryModifiedListener: Updated setup with replacement category', [
                'setup_id' => $setup->id,
                'old_category_id' => $oldCategory->id,
                'old_category_name' => $oldCategory->name,
                'replacement_category_id' => $replacementCategoryId,
                'replacement_category_name' => $replacementCategory->name
            ]);

            // Notify the job seeker
            $this->notifyJobSeekerOfCategoryUpdate($setup, $oldCategory, $replacementCategory);
        } else {
            // Just remove the old category since replacement already exists
            $setup->categories()->detach($oldCategory->id);
            $setup->category_count = $setup->categories()->count();
            $setup->save();

            Log::info('JobCategoryModifiedListener: Removed duplicate category from setup', [
                'setup_id' => $setup->id,
                'removed_category_id' => $oldCategory->id,
                'replacement_category_already_present' => $replacementCategoryId
            ]);

            // Still notify about the change
            $this->notifyJobSeekerOfCategoryUpdate($setup, $oldCategory, $replacementCategory);
        }
    }

    /**
     * Mark a notification setup for review
     * 
     * @param JobNotificationSetup $setup
     * @param JobCategory $removedCategory
     * @return void
     */
    private function markSetupForReview(JobNotificationSetup $setup, JobCategory $removedCategory): void
    {
        $reason = "Category '{$removedCategory->name}' was removed by an administrator and no direct replacement was specified";
        $setup->markForReview($reason);

        Log::info('JobCategoryModifiedListener: Marked setup for review', [
            'setup_id' => $setup->id,
            'setup_name' => $setup->name,
            'removed_category_id' => $removedCategory->id,
            'removed_category_name' => $removedCategory->name,
            'reason' => $reason
        ]);

        // Notify the job seeker that action is required
        $this->notifyJobSeekerOfReviewRequired($setup, $removedCategory);
    }

    /**
     * Handle category updates (like renaming)
     * 
     * @param JobCategoryModifiedEvent $event
     * @return void
     */
    private function handleCategoryUpdate(JobCategoryModifiedEvent $event): void
    {
        $category = $event->jobCategory;
        
        // For updates, we mainly need to ensure cache is cleared (already done)
        // Renaming doesn't break functionality since relationships are by ID
        
        Log::info('JobCategoryModifiedListener: Category updated', [
            'category_id' => $category->id,
            'category_name' => $category->name,
            'changed_attributes' => $category->getChanges()
        ]);
    }

    /**
     * Notify job seeker of category update with replacement
     * 
     * @param JobNotificationSetup $setup
     * @param JobCategory $oldCategory
     * @param JobCategory $newCategory
     * @return void
     */
    private function notifyJobSeekerOfCategoryUpdate(
        JobNotificationSetup $setup, 
        JobCategory $oldCategory, 
        JobCategory $newCategory
    ): void {
        try {
            $jobSeeker = $setup->jobSeeker;
            if (!$jobSeeker) {
                return;
            }

            // For now, we'll log this. Later we can implement actual notifications
            Log::info('JobCategoryModifiedListener: Should notify job seeker of category update', [
                'job_seeker_id' => $jobSeeker->id,
                'job_seeker_email' => $jobSeeker->email,
                'setup_id' => $setup->id,
                'setup_name' => $setup->name,
                'old_category_name' => $oldCategory->name,
                'new_category_name' => $newCategory->name,
                'message' => "Update for your setup '{$setup->name}': The category '{$oldCategory->name}' has been updated to '{$newCategory->name}' by an administrator."
            ]);

            // TODO: Implement email/in-app notification
            // This could be done via a notification class or job queue

        } catch (\Exception $e) {
            Log::error('JobCategoryModifiedListener: Error notifying job seeker of category update', [
                'setup_id' => $setup->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Notify job seeker that review is required
     * 
     * @param JobNotificationSetup $setup
     * @param JobCategory $removedCategory
     * @return void
     */
    private function notifyJobSeekerOfReviewRequired(
        JobNotificationSetup $setup, 
        JobCategory $removedCategory
    ): void {
        try {
            $jobSeeker = $setup->jobSeeker;
            if (!$jobSeeker) {
                return;
            }

            // For now, we'll log this. Later we can implement actual notifications
            Log::info('JobCategoryModifiedListener: Should notify job seeker that review is required', [
                'job_seeker_id' => $jobSeeker->id,
                'job_seeker_email' => $jobSeeker->email,
                'setup_id' => $setup->id,
                'setup_name' => $setup->name,
                'removed_category_name' => $removedCategory->name,
                'message' => "Action required for your setup '{$setup->name}'. The category '{$removedCategory->name}' is no longer available. Please edit your setup to select a new category."
            ]);

            // TODO: Implement email/in-app notification
            // This could be done via a notification class or job queue

        } catch (\Exception $e) {
            Log::error('JobCategoryModifiedListener: Error notifying job seeker of review requirement', [
                'setup_id' => $setup->id,
                'error' => $e->getMessage()
            ]);
        }
    }
} 
function initTable(){$table.bootstrapTable({height:getHeight(),columns:[[{field:"state",checkbox:!0,rowspan:2,align:"center",valign:"middle"},{title:"Item ID",field:"id",rowspan:2,align:"center",valign:"middle",sortable:!0,footerFormatter:totalTextFormatter},{title:"Item Detail",colspan:3,align:"center"}],[{field:"name",title:"Item Name",sortable:!0,editable:!0,footerFormatter:totalNameFormatter,align:"center"},{field:"price",title:"Item Price",sortable:!0,align:"center",editable:{type:"text",title:"Item Price",validate:function(t){if(t=$.trim(t),!t)return"This field is required";if(!/^$/.test(t))return"This field needs to start width $.";var e=$table.bootstrapTable("getData"),a=$(this).parents("tr").data("index");return console.log(e[a]),""}},footerFormatter:totalPriceFormatter},{field:"operate",title:"Item Operate",align:"center",events:operateEvents,formatter:operateFormatter}]]}),setTimeout(function(){$table.bootstrapTable("resetView")},200),$table.on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table",function(){$remove.prop("disabled",!$table.bootstrapTable("getSelections").length),selections=getIdSelections()}),$table.on("expand-row.bs.table",function(t,e,a,o){e%2==1&&(o.html("Loading from ajax request..."),$.get("LICENSE",function(t){o.html(t.replace(/\n/g,"<br>"))}))}),$table.on("all.bs.table",function(t,e,a){console.log(e,a)}),$remove.click(function(){var t=getIdSelections();$table.bootstrapTable("remove",{field:"id",values:t}),$remove.prop("disabled",!0)}),$(window).resize(function(){$table.bootstrapTable("resetView",{height:getHeight()})})}function getIdSelections(){return $.map($table.bootstrapTable("getSelections"),function(t){return t.id})}function responseHandler(t){return $.each(t.rows,function(t,e){e.state=-1!==$.inArray(e.id,selections)}),t}function detailFormatter(t,e){var a=[];return $.each(e,function(t,e){a.push("<p><b>"+t+":</b> "+e+"</p>")}),a.join("")}function operateFormatter(t,e,a){return['<a class="like" href="javascript:void(0)" title="Like">','<i class="glyphicon glyphicon-heart"></i>',"</a>  ",'<a class="remove" href="javascript:void(0)" title="Remove">','<i class="glyphicon glyphicon-remove"></i>',"</a>"].join("")}function totalTextFormatter(t){return"Total"}function totalNameFormatter(t){return t.length}function totalPriceFormatter(t){var e=0;return $.each(t,function(t,a){e+=+a.price.substring(1)}),"$"+e}function getHeight(){return $(window).height()-$("h1").outerHeight(!0)}function getScript(t,e){var a=document.getElementsByTagName("head")[0],o=document.createElement("script");o.src=t;var r=!1;return o.onload=o.onreadystatechange=function(){r||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(r=!0,e&&e(),o.onload=o.onreadystatechange=null)},void a.appendChild(o)}var $table=$("#table"),$remove=$("#remove"),selections=[];window.operateEvents={"click .like":function(t,e,a,o){alert("You click like action, row: "+JSON.stringify(a))},"click .remove":function(t,e,a,o){$table.bootstrapTable("remove",{field:"id",values:[a.id]})}},$(function(){var t=[location.search.substring(1)||"assets/bootstrap-table/src/bootstrap-table.js","assets/bootstrap-table/src/extensions/export/bootstrap-table-export.js","http://rawgit.com/hhurz/tableExport.jquery.plugin/master/tableExport.js","assets/bootstrap-table/src/extensions/editable/bootstrap-table-editable.js","http://rawgit.com/vitalets/x-editable/master/dist/bootstrap3-editable/js/bootstrap-editable.js"],e=function(t,e,a){if(a=a||function(){},!t.length)return a();var o=0,r=function(){e(t[o],function(e){e?(a(e),a=function(){}):(o+=1,o>=t.length?a(null):r())})};r()};e(t,getScript,initTable)});
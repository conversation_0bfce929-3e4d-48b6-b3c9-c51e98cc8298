<?php

namespace Modules\Education\Http\Controllers;


use App\Classes;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;


class StudentReportsController extends Controller
{


    public function __invoke($classId)
    {
        $class = Classes::find($classId);
        $center = $class->center->name;

        $className = Classes::find($classId)->name;
        $classTeachers = $class->teachers()->pluck('full_name')->toArray();
        $timetable = $class->timetable;

        $daysCount = 0;

        if($timetable) {
            foreach(['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'] as $day) {
                if(!is_null($timetable->$day) && $timetable->$day !== '') {
                    $daysCount++;
                }
            }
        }

        // Load the related programs

        $classPrograms = $class->programs()->get()->pluck('title')->toArray()[0];


        // Check if the class's program contains "Ijazah and Sanad"
        if (str_contains(strtolower($classPrograms), 'ijazah and sanad')) {
            // Load a different view for "Ijazah and Sanad"
            return view('education::classes.reports.student.ijazasanad', compact('classTeachers', 'className', 'classId', 'center', 'daysCount'));
        } else if (str_contains(strtolower($classPrograms), 'nouranya') || str_contains(strtolower($classPrograms), 'nuraniyah')) {
            return view('education::classes.reports.student.nouranya', compact('classTeachers', 'className', 'classId', 'center', 'daysCount'));
        } else  {
            return view('education::classes.reports.student.memorizationRevision', compact('classTeachers', 'className', 'classId', 'center', 'daysCount'));
        }


        return view('education::classes.reports.student',compact('classTeachers', 'className','classId','center','daysCount'));
    }

}

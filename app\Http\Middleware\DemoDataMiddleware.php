<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\DemoDataService;
use Illuminate\Support\Facades\Auth;

/**
 * Demo Data Middleware
 * 
 * Automatically applies demo data transformation for system viewers
 * when demo mode is enabled via session
 */
final class DemoDataMiddleware
{
    private DemoDataService $demoDataService;

    public function __construct(DemoDataService $demoDataService)
    {
        $this->demoDataService = $demoDataService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Only process for system viewers with demo mode enabled
        if (!$this->shouldProcessDemoData()) {
            return $response;
        }

        // Apply demo data transformation to response content
        if ($response->headers->get('content-type') && 
            strpos($response->headers->get('content-type'), 'text/html') !== false) {
            
            $content = $response->getContent();
            
            // Apply JavaScript-based data transformation for dynamic content
            $demoScript = $this->getDemoDataScript();
            $content = str_replace('</body>', $demoScript . '</body>', $content);
            
            $response->setContent($content);
        }

        return $response;
    }

    /**
     * Check if demo data processing should be applied
     */
    private function shouldProcessDemoData(): bool
    {
        if (!Auth::guard('employee')->check()) {
            return false;
        }

        $user = Auth::guard('employee')->user();
        
        // Only apply to system viewers
        if (!$user->hasRole('system_viewer_' . config('organization_id') . '_')) {
            return false;
        }

        // Check if demo mode is enabled
        return session('viewer_demo_mode', false);
    }

    /**
     * Generate JavaScript for client-side demo data transformation
     */
    private function getDemoDataScript(): string
    {
        return '
        <script>
        // Demo Data Transformation Script
        (function() {
            if (typeof window.demoDataTransform === "undefined") {
                window.demoDataTransform = {
                    // Transform email addresses
                    transformEmails: function() {
                        document.querySelectorAll("td, span, div, p").forEach(function(element) {
                            if (element.textContent && element.textContent.includes("@") && !element.textContent.includes("demo.example.com")) {
                                var text = element.textContent;
                                // Replace real emails with demo emails
                                text = text.replace(/[\w\.-]+@[\w\.-]+\.\w+/g, function(match, offset) {
                                    var id = Math.floor(Math.random() * 1000) + 1;
                                    return "demo" + id + "@demo.example.com";
                                });
                                element.textContent = text;
                            }
                        });
                    },
                    
                    // Transform phone numbers
                    transformPhones: function() {
                        document.querySelectorAll("td, span, div, p").forEach(function(element) {
                            if (element.textContent && /[\d\-\+\(\)\s]{10,}/.test(element.textContent)) {
                                var text = element.textContent;
                                text = text.replace(/[\d\-\+\(\)\s]{10,}/g, "******-DEMO");
                                element.textContent = text;
                            }
                        });
                    },
                    
                    // Transform names (keep structure but anonymize)
                    transformNames: function() {
                        document.querySelectorAll("td, span, div, p").forEach(function(element) {
                            if (element.textContent && element.textContent.length > 0) {
                                var text = element.textContent.trim();
                                // Transform names that look like person names (2-3 words, capitalized)
                                if (/^[A-Z][a-z]+ [A-Z][a-z]+( [A-Z][a-z]+)?$/.test(text)) {
                                    var id = Math.floor(Math.random() * 1000) + 1;
                                    element.textContent = "Demo Person " + String(id).padStart(3, "0");
                                }
                            }
                        });
                    },
                    
                    // Apply all transformations
                    applyAll: function() {
                        this.transformEmails();
                        this.transformPhones();
                        this.transformNames();
                    }
                };
                
                // Apply transformations when page loads
                if (document.readyState === "loading") {
                    document.addEventListener("DOMContentLoaded", function() {
                        setTimeout(function() {
                            window.demoDataTransform.applyAll();
                        }, 500);
                    });
                } else {
                    setTimeout(function() {
                        window.demoDataTransform.applyAll();
                    }, 500);
                }
                
                // Apply transformations to dynamically loaded content
                var observer = new MutationObserver(function(mutations) {
                    var shouldTransform = false;
                    mutations.forEach(function(mutation) {
                        if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
                            shouldTransform = true;
                        }
                    });
                    
                    if (shouldTransform) {
                        setTimeout(function() {
                            window.demoDataTransform.applyAll();
                        }, 100);
                    }
                });
                
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            }
        })();
        </script>';
    }
} 
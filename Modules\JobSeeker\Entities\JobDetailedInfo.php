<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * JobDetailedInfo Entity
 * 
 * Stores detailed job information fetched from provider detail pages
 * 
 * @property int $id
 * @property int $job_id
 * @property int $provider_id
 * @property string|null $detailed_description
 * @property string|null $about_company
 * @property string|null $duties_responsibilities
 * @property string|null $job_requirements
 * @property string|null $submission_guideline
 * @property string|null $application_email
 * @property string|null $application_website
 * @property string|null $contact_person
 * @property string|null $contact_phone
 * @property string|null $company_size
 * @property string|null $industry_sector
 * @property string|null $benefits
 * @property string|null $working_hours
 * @property string|null $probation_period
 * @property string|null $contract_duration
 * @property bool $is_contract_extensible
 * @property string|null $minimum_education
 * @property int|null $preferred_experience_years
 * @property string|null $language_requirements
 * @property string|null $additional_skills
 * @property string|null $provider_job_url
 * @property \Illuminate\Support\Carbon|null $fetched_at
 * @property bool $fetch_success
 * @property string|null $fetch_error
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
final class JobDetailedInfo extends Model
{
    protected $table = 'jobseeker_job_detailed_info';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'job_id',
        'provider_id',
        'detailed_description',
        'about_company',
        'duties_responsibilities',
        'job_requirements',
        'submission_guideline',
        'application_email',
        'application_website',
        'contact_person',
        'contact_phone',
        'company_size',
        'industry_sector',
        'benefits',
        'working_hours',
        'probation_period',
        'contract_duration',
        'is_contract_extensible',
        'minimum_education',
        'preferred_experience_years',
        'language_requirements',
        'additional_skills',
        'provider_job_url',
        'fetched_at',
        'fetch_success',
        'fetch_error',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'job_id' => 'integer',
        'provider_id' => 'integer',
        'is_contract_extensible' => 'boolean',
        'preferred_experience_years' => 'integer',
        'fetched_at' => 'datetime',
        'fetch_success' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the job that owns this detailed info
     */
    public function job(): BelongsTo
    {
        return $this->belongsTo(Job::class, 'job_id');
    }

    /**
     * Get the provider that this detailed info was fetched from
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(JobProvider::class, 'provider_id');
    }

    /**
     * Scope for successfully fetched details
     */
    public function scopeSuccessfullyFetched($query)
    {
        return $query->where('fetch_success', true);
    }

    /**
     * Scope for failed fetches
     */
    public function scopeFailedFetches($query)
    {
        return $query->where('fetch_success', false);
    }

    /**
     * Scope for recently fetched (within last N days)
     */
    public function scopeRecentlyFetched($query, int $days = 7)
    {
        return $query->where('fetched_at', '>=', now()->subDays($days));
    }

    /**
     * Check if detailed info needs to be refreshed
     */
    public function needsRefresh(int $maxAgeHours = 24): bool
    {
        if (!$this->fetched_at) {
            return true;
        }

        return $this->fetched_at->diffInHours(now()) > $maxAgeHours;
    }

    /**
     * Mark as successfully fetched
     */
    public function markAsSuccessfullyFetched(): void
    {
        $this->update([
            'fetched_at' => now(),
            'fetch_success' => true,
            'fetch_error' => null,
        ]);
    }

    /**
     * Mark as failed fetch
     */
    public function markAsFailed(string $error): void
    {
        $this->update([
            'fetched_at' => now(),
            'fetch_success' => false,
            'fetch_error' => $error,
        ]);
    }

    /**
     * Get all available detailed fields
     */
    public static function getDetailedFields(): array
    {
        return [
            'detailed_description',
            'about_company',
            'duties_responsibilities',
            'job_requirements',
            'submission_guideline',
            'application_email',
            'application_website',
            'contact_person',
            'contact_phone',
            'company_size',
            'industry_sector',
            'benefits',
            'working_hours',
            'probation_period',
            'contract_duration',
            'minimum_education',
            'preferred_experience_years',
            'language_requirements',
            'additional_skills',
        ];
    }

    /**
     * Get field value with fallback to job table
     */
    public function getFieldValue(string $fieldName): mixed
    {
        // First try to get from detailed info
        if (isset($this->attributes[$fieldName]) && !empty($this->attributes[$fieldName])) {
            return $this->attributes[$fieldName];
        }

        // Fallback to job table if field exists there
        if ($this->job && method_exists($this->job, 'getAttribute')) {
            return $this->job->getAttribute($fieldName);
        }

        return null;
    }

    /**
     * Check if any detailed information is available
     */
    public function hasDetailedInfo(): bool
    {
        $detailedFields = self::getDetailedFields();
        
        foreach ($detailedFields as $field) {
            if (!empty($this->attributes[$field])) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Clean HTML content for email display
     */
    public function cleanHtmlField(string $fieldName): ?string
    {
        $value = $this->getAttribute($fieldName);
        
        if (empty($value)) {
            return null;
        }

        // Remove HTML tags and decode entities
        $cleaned = strip_tags($value);
        $cleaned = html_entity_decode($cleaned, ENT_QUOTES, 'UTF-8');
        
        // Remove extra whitespace
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        $cleaned = trim($cleaned);
        
        return empty($cleaned) ? null : $cleaned;
    }
}

!function(){"use strict";var n,e,t,r,o=function(n){var e=n,t=function(){return e};return{get:t,set:function(n){e=n},clone:function(){return o(t())}}},u=tinymce.util.Tools.resolve("tinymce.PluginManager"),i=function(n){return{isEnabled:function(){return n.get()}}},c=function(n,e){return n.fire("VisualChars",{state:e})},a={"\xa0":"nbsp","\xad":"shy"},f=function(n,e){var t,r="";for(t in n)r+=t;return new RegExp("["+r+"]",e?"g":"")},l=function(n){var e,t="";for(e in n)t&&(t+=","),t+="span.mce-"+n[e];return t},s={charMap:a,regExp:f(a),regExpGlobal:f(a,!0),selector:l(a),charMapToRegExp:f,charMapToSelector:l},d=function(n){return function(){return n}},m={noop:function(){},noarg:function(n){return function(){return n()}},compose:function(n,e){return function(){return n(e.apply(null,arguments))}},constant:d,identity:function(n){return n},tripleEquals:function(n,e){return n===e},curry:function(n){for(var e=new Array(arguments.length-1),t=1;t<arguments.length;t++)e[t-1]=arguments[t];return function(){for(var t=new Array(arguments.length),r=0;r<t.length;r++)t[r]=arguments[r];var o=e.concat(t);return n.apply(null,o)}},not:function(n){return function(){return!n.apply(null,arguments)}},die:function(n){return function(){throw new Error(n)}},apply:function(n){return n()},call:function(n){n()},never:d(!1),always:d(!0)},p=m.never,h=m.always,v=function(){return g},g=(r={fold:function(n,e){return n()},is:p,isSome:p,isNone:h,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},or:t,orThunk:e,map:v,ap:v,each:function(){},bind:v,flatten:v,exists:p,forall:h,filter:v,equals:n=function(n){return n.isNone()},equals_:n,toArray:function(){return[]},toString:m.constant("none()")},Object.freeze&&Object.freeze(r),r),y=function(n){var e=function(){return n},t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:h,isNone:p,getOr:e,getOrThunk:e,getOrDie:e,or:t,orThunk:t,map:function(e){return y(e(n))},ap:function(e){return e.fold(v,function(e){return y(e(n))})},each:function(e){e(n)},bind:r,flatten:e,exists:r,forall:r,filter:function(e){return e(n)?o:g},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(p,function(e){return t(n,e)})},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},T={some:y,none:v,from:function(n){return null===n||n===undefined?g:y(n)}},w=(Array.prototype.indexOf,undefined,function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var u=n[o];r[o]=e(u,o,n)}return r}),x=function(n,e){for(var t=0,r=n.length;t<r;t++)e(n[t],t,n)},E=(Array.prototype.push,Array.prototype.slice,w),b=x,k=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:m.constant(n)}},N={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||t.childNodes.length>1)throw console.error("HTML does not have a single root node",n),"HTML must have a single root node";return k(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return k(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return k(t)},fromDom:k,fromPoint:function(n,e,t){return T.from(n.dom().elementFromPoint(e,t)).map(k)}},C=8,M=9,D=3,O=function(n){return n.dom().nodeName.toLowerCase()},A=function(n){return n.dom().nodeType},S=function(n){return function(e){return A(e)===n}},B=S(1),P=S(D),V=S(M),q={name:O,type:A,value:function(n){return n.dom().nodeValue},isElement:B,isText:P,isDocument:V,isComment:function(n){return A(n)===C||"#comment"===O(n)}},H=function(n){return'<span data-mce-bogus="1" class="mce-'+s.charMap[n]+'">'+n+"</span>"},L=function(n,e){var t=[],r=n.dom(),o=E(r.childNodes,N.fromDom);return b(o,function(n){e(n)&&(t=t.concat([n])),t=t.concat(L(n,e))}),t},R={isMatch:function(n){return q.isText(n)&&q.value(n)!==undefined&&s.regExp.test(q.value(n))},filterDescendants:L,findParentElm:function(n,e){for(;n.parentNode;){if(n.parentNode===e)return n;n=n.parentNode}},replaceWithSpans:function(n){return n.replace(s.regExpGlobal,H)}},_=function(n,e){var t,r,o=R.filterDescendants(N.fromDom(e),R.isMatch);b(o,function(e){var o=R.replaceWithSpans(q.value(e));for(r=n.dom.create("div",null,o);t=r.lastChild;)n.dom.insertAfter(t,e.dom());n.dom.remove(e.dom())})},j=function(n,e){var t=n.dom.select(s.selector,e);b(t,function(e){n.dom.remove(e,1)})},z=_,G=j,W=function(n){var e=n.getBody(),t=n.selection.getBookmark(),r=R.findParentElm(n.selection.getNode(),e);r=r!==undefined?r:e,j(n,r),_(n,r),n.selection.moveToBookmark(t)},F=function(n,e){var t,r=n.getBody(),o=n.selection;e.set(!e.get()),c(n,e.get()),t=o.getBookmark(),!0===e.get()?z(n,r):G(n,r),o.moveToBookmark(t)},I=function(n,e){n.addCommand("mceVisualChars",function(){F(n,e)})},J=tinymce.util.Tools.resolve("tinymce.util.Delay"),K=function(n,e){var t=J.debounce(function(){W(n)},300);!1!==n.settings.forced_root_block&&n.on("keydown",function(r){!0===e.get()&&(13===r.keyCode?W(n):t())})},Q=function(n){return function(e){var t=e.control;n.on("VisualChars",function(n){t.active(n.state)})}};u.add("visualchars",function(n){var e,t=o(!1);return I(n,t),(e=n).addButton("visualchars",{active:!1,title:"Show invisible characters",cmd:"mceVisualChars",onPostRender:Q(e)}),e.addMenuItem("visualchars",{text:"Show invisible characters",cmd:"mceVisualChars",onPostRender:Q(e),selectable:!0,context:"view",prependToContext:!0}),K(n,t),i(t)})}();
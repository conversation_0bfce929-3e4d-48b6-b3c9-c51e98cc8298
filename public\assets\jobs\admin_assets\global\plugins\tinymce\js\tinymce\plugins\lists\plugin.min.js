!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),n=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),o=tinymce.util.Tools.resolve("tinymce.util.VK"),r=tinymce.util.Tools.resolve("tinymce.dom.BookmarkManager"),i=tinymce.util.Tools.resolve("tinymce.util.Tools"),a=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),s=function(e){return e&&"BR"===e.nodeName},d=function(e){return e&&3===e.nodeType},l=function(e){return e&&/^(OL|UL|DL)$/.test(e.nodeName)},c=function(e){return e&&/^(LI|DT|DD)$/.test(e.nodeName)},f=function(e){return e&&/^(TH|TD)$/.test(e.nodeName)},u=s,m=function(e){return e.parentNode.firstChild===e},g=function(e){return e.parentNode.lastChild===e},p=function(e,t){return t&&!!e.schema.getTextBlockElements()[t.nodeName]},v=function(e,t){return e&&e.nodeName in t},h=function(e,t){return!!s(t)&&!(!e.isBlock(t.nextSibling)||s(t.previousSibling))},C=function(e,t,n){var o=e.isEmpty(t);return!(n&&e.select("span[data-mce-type=bookmark]",t).length>0)&&o},y=function(e,t){return e.isChildOf(t,e.getRoot())},N=function(e,n){var o=t.getNode(e,n);return c(e)&&d(o)?{container:o,offset:n>=e.childNodes.length?o.data.length:0}:{container:e,offset:n}},L=function(e){var t=e.cloneRange(),n=N(e.startContainer,e.startOffset);t.setStart(n.container,n.offset);var o=N(e.endContainer,e.endOffset);return t.setEnd(o.container,o.offset),t},S=a.DOM,b=function(e){var t={},n=function(n){var o,r,i;r=e[n?"startContainer":"endContainer"],i=e[n?"startOffset":"endOffset"],1===r.nodeType&&(o=S.create("span",{"data-mce-type":"bookmark"}),r.hasChildNodes()?(i=Math.min(i,r.childNodes.length-1),n?r.insertBefore(o,r.childNodes[i]):S.insertAfter(o,r.childNodes[i])):r.appendChild(o),r=o,i=0),t[n?"startContainer":"endContainer"]=r,t[n?"startOffset":"endOffset"]=i};return n(!0),e.collapsed||n(),t},D=function(e){function t(t){var n,o,r;n=r=e[t?"startContainer":"endContainer"],o=e[t?"startOffset":"endOffset"],n&&(1===n.nodeType&&(o=function(e){for(var t=e.parentNode.firstChild,n=0;t;){if(t===e)return n;1===t.nodeType&&"bookmark"===t.getAttribute("data-mce-type")||n++,t=t.nextSibling}return-1}(n),n=n.parentNode,S.remove(r),!n.hasChildNodes()&&S.isBlock(n)&&n.appendChild(S.create("br"))),e[t?"startContainer":"endContainer"]=n,e[t?"startOffset":"endOffset"]=o)}t(!0),t();var n=S.createRng();return n.setStart(e.startContainer,e.startOffset),e.endContainer&&n.setEnd(e.endContainer,e.endOffset),L(n)},k=a.DOM,T=function(e,t){var n,o=t.parentNode;"LI"===o.nodeName&&o.firstChild===t&&((n=o.previousSibling)&&"LI"===n.nodeName?(n.appendChild(t),C(e,o)&&k.remove(o)):k.setStyle(o,"listStyleType","none")),l(o)&&(n=o.previousSibling)&&"LI"===n.nodeName&&n.appendChild(t)},I=function(e,t){i.each(i.grep(e.select("ol,ul",t)),function(t){T(e,t)})},B=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),R=function(e){var t=e.selection.getStart(!0);return e.dom.getParent(t,"OL,UL,DL",O(e,t))},O=function(e,t){var n=e.dom.getParents(t,"TD,TH");return n.length>0?n[0]:e.getBody()},E={getParentList:R,getSelectedSubLists:function(e){var t,n,o,r=R(e),a=e.selection.getSelectedBlocks();return o=a,(n=r)&&1===o.length&&o[0]===n?(t=r,i.grep(t.querySelectorAll("ol,ul,dl"),function(e){return l(e)})):i.grep(a,function(e){return l(e)&&r!==e})},getSelectedListItems:function(e){var t,n,o,r=e.selection.getSelectedBlocks();return i.grep((t=e,n=r,o=i.map(n,function(e){var n=t.dom.getParent(e,"li,dd,dt",O(t,e));return n||e}),B.unique(o)),function(e){return c(e)})},getClosestListRootElm:O},A=tinymce.util.Tools.resolve("tinymce.Env"),P=a.DOM,x=function(e,t,n){var o,r,i,a=P.createFragment(),s=e.schema.getBlockElements();if(e.settings.forced_root_block&&(n=n||e.settings.forced_root_block),n&&((r=P.create(n)).tagName===e.settings.forced_root_block&&P.setAttribs(r,e.settings.forced_root_block_attrs),v(t.firstChild,s)||a.appendChild(r)),t)for(;o=t.firstChild;){var d=o.nodeName;i||"SPAN"===d&&"bookmark"===o.getAttribute("data-mce-type")||(i=!0),v(o,s)?(a.appendChild(o),r=null):n?(r||(r=P.create(n),a.appendChild(r)),r.appendChild(o)):a.appendChild(o)}return e.settings.forced_root_block?i||A.ie&&!(A.ie>10)||r.appendChild(P.create("br",{"data-mce-bogus":"1"})):a.appendChild(P.create("br")),a},_=a.DOM,M=function(e,t,n,o){var r,a,s,d,l;for(s=_.select('span[data-mce-type="bookmark"]',t),o=o||x(e,n),(r=_.createRng()).setStartAfter(n),r.setEndAfter(t),d=(a=r.extractContents()).firstChild;d;d=d.firstChild)if("LI"===d.nodeName&&e.dom.isEmpty(d)){_.remove(d);break}e.dom.isEmpty(a)||_.insertAfter(a,t),_.insertAfter(o,t),C(e.dom,n.parentNode)&&(l=n.parentNode,i.each(s,function(e){l.parentNode.insertBefore(e,n.parentNode)}),_.remove(l)),_.remove(n),C(e.dom,t)&&_.remove(t)},U=a.DOM,H=function(e,t){C(e,t)&&U.remove(t)},$=function(e,t){var n,o=t.parentNode,r=o.parentNode;return!(o!==e.getBody()&&("DD"===t.nodeName?(U.rename(t,"DT"),0):m(t)&&g(t)?("LI"===r.nodeName?(U.insertAfter(t,r),H(e.dom,r),U.remove(o)):l(r)?U.remove(o,!0):(r.insertBefore(x(e,t),o),U.remove(o)),0):m(t)?("LI"===r.nodeName?(U.insertAfter(t,r),t.appendChild(o),H(e.dom,r)):l(r)?r.insertBefore(t,o):(r.insertBefore(x(e,t),o),U.remove(t)),0):g(t)?("LI"===r.nodeName?U.insertAfter(t,r):l(r)?U.insertAfter(t,o):(U.insertAfter(x(e,t),o),U.remove(t)),0):("LI"===r.nodeName?(o=r,n=x(e,t,"LI")):n=l(r)?x(e,t,"LI"):x(e,t),M(e,o,t,n),I(e.dom,o.parentNode),0)))},w=$,K=function(e){var t=E.getSelectedListItems(e);if(t.length){var n=b(e.selection.getRng(!0)),o=void 0,r=void 0,i=E.getClosestListRootElm(e,e.selection.getStart(!0));for(o=t.length;o--;)for(var a=t[o].parentNode;a&&a!==i;){for(r=t.length;r--;)if(t[r]===a){t.splice(o,1);break}a=a.parentNode}for(o=0;o<t.length&&($(e,t[o])||0!==o);o++);return e.selection.setRng(D(n)),e.nodeChanged(),!0}},Q=function(e,t){i.each(t,function(t,n){e.setAttribute(n,t)})},W=function(e,t,n){var o,r,a,s,d,l,c;o=e,r=t,s=(a=n)["list-style-type"]?a["list-style-type"]:null,o.setStyle(r,"list-style-type",s),d=e,Q(l=t,(c=n)["list-attributes"]),i.each(d.select("li",l),function(e){Q(e,c["list-item-attributes"])})},j=function(e,t,n,o){var r,i;for(r=t[n?"startContainer":"endContainer"],i=t[n?"startOffset":"endOffset"],1===r.nodeType&&(r=r.childNodes[Math.min(i,r.childNodes.length-1)]||r),!n&&u(r.nextSibling)&&(r=r.nextSibling);r.parentNode!==o;){if(p(e,r))return r;if(/^(TD|TH)$/.test(r.parentNode.nodeName))return r;r=r.parentNode}return r},q=function(e,t,n){void 0===n&&(n={});var o,a=e.selection.getRng(!0),s="LI",d=E.getClosestListRootElm(e,e.selection.getStart(!0)),c=e.dom;"false"!==c.getContentEditable(e.selection.getNode())&&("DL"===(t=t.toUpperCase())&&(s="DT"),o=b(a),i.each(function(e,t,n){for(var o,a=[],s=e.dom,d=j(e,t,!0,n),l=j(e,t,!1,n),c=[],f=d;f&&(c.push(f),f!==l);f=f.nextSibling);return i.each(c,function(t){if(p(e,t))return a.push(t),void(o=null);if(s.isBlock(t)||u(t))return u(t)&&s.remove(t),void(o=null);var i=t.nextSibling;r.isBookmarkNode(t)&&(p(e,i)||!i&&t.parentNode===n)?o=null:(o||(o=s.create("p"),t.parentNode.insertBefore(o,t),a.push(o)),o.appendChild(t))}),a}(e,a,d),function(o){var r,a,d,f,u,m,g,p,v;(a=o.previousSibling)&&l(a)&&a.nodeName===t&&(d=a,f=n,u=c.getStyle(d,"list-style-type"),m=f?f["list-style-type"]:"",u===(m=null===m?"":m))?(r=a,o=c.rename(o,s),a.appendChild(o)):(r=c.create(t),o.parentNode.insertBefore(r,o),r.appendChild(o),o=c.rename(o,s)),g=c,p=o,v=["margin","margin-right","margin-bottom","margin-left","margin-top","padding","padding-right","padding-bottom","padding-left","padding-top"],i.each(v,function(e){return g.setStyle(p,((t={})[e]="",t));var t}),W(c,r,n),z(e.dom,r)}),e.selection.setRng(D(o)))},F=function(e){var t=b(e.selection.getRng(!0)),n=E.getClosestListRootElm(e,e.selection.getStart(!0)),o=E.getSelectedListItems(e),r=i.grep(o,function(t){return e.dom.isEmpty(t)});o=i.grep(o,function(t){return!e.dom.isEmpty(t)}),i.each(r,function(t){C(e.dom,t)&&w(e,t)}),i.each(o,function(t){var o,r;if(t.parentNode!==e.getBody()){for(o=t;o&&o!==n;o=o.parentNode)l(o)&&(r=o);M(e,r,t),I(e.dom,r.parentNode)}}),e.selection.setRng(D(t))},V=function(e,t,n){return d=n,(s=t)&&d&&l(s)&&s.nodeName===d.nodeName&&(i=t,a=n,(r=e).getStyle(i,"list-style-type",!0)===r.getStyle(a,"list-style-type",!0))&&(o=n,t.className===o.className);var o,r,i,a,s,d},z=function(e,t){var n,o;if(n=t.nextSibling,V(e,t,n)){for(;o=n.firstChild;)t.appendChild(o);e.remove(n)}if(n=t.previousSibling,V(e,t,n)){for(;o=n.lastChild;)t.insertBefore(o,t.firstChild);e.remove(n)}},G=function(e,t,n,o,r){if(t.nodeName!==o||J(r)){var a=b(e.selection.getRng(!0));i.each([t].concat(n),function(t){!function(e,t,n,o){if(t.nodeName!==n){var r=e.rename(t,n);W(e,r,o)}else W(e,t,o)}(e.dom,t,o,r)}),e.selection.setRng(D(a))}else F(e)},J=function(e){return"list-style-type"in e},X={toggleList:function(e,t,n){var o=E.getParentList(e),r=E.getSelectedSubLists(e);n=n||{},o&&r.length>0?G(e,o,r,t,n):function(e,t,n,o){if(t!==e.getBody())if(t)if(t.nodeName!==n||J(o)){var r=b(e.selection.getRng(!0));W(e.dom,t,o),z(e.dom,e.dom.rename(t,n)),e.selection.setRng(D(r))}else F(e);else q(e,n,o)}(e,o,t,n)},removeList:F,mergeWithAdjacentLists:z},Y=function(e,o,r,i){var a,s,d=o.startContainer,l=o.startOffset;if(3===d.nodeType&&(r?l<d.data.length:l>0))return d;for(a=e.schema.getNonEmptyElements(),1===d.nodeType&&(d=t.getNode(d,l)),s=new n(d,i),r&&h(e.dom,d)&&s.next();d=s[r?"next":"prev2"]();){if("LI"===d.nodeName&&!d.hasChildNodes())return d;if(a[d.nodeName])return d;if(3===d.nodeType&&d.data.length>0)return d}},Z=function(e,t){var n=t.childNodes;return 1===n.length&&!l(n[0])&&e.isBlock(n[0])},ee=function(e,t,n){var o,r,i,a;if(r=Z(e,n)?n.firstChild:n,Z(i=e,a=t)&&i.remove(a.firstChild,!0),!C(e,t,!0))for(;o=t.firstChild;)r.appendChild(o)},te=function(e,t,n){var o,r,i=t.parentNode;y(e,t)&&y(e,n)&&(l(n.lastChild)&&(r=n.lastChild),i===n.lastChild&&u(i.previousSibling)&&e.remove(i.previousSibling),(o=n.lastChild)&&u(o)&&t.hasChildNodes()&&e.remove(o),C(e,n,!0)&&e.$(n).empty(),ee(e,t,n),r&&n.appendChild(r),e.remove(t),C(e,i)&&i!==e.getRoot()&&e.remove(i))},ne=function(e,t,n,o){var r,i,a,s=e.dom;if(s.isEmpty(o))i=n,a=o,(r=e).dom.$(a).empty(),te(r.dom,i,a),r.selection.setCursorLocation(a);else{var d=b(t);te(s,n,o),e.selection.setRng(D(d))}},oe=function(e,t){var n,o,r,i=e.dom,a=e.selection,s=a.getStart(),d=E.getClosestListRootElm(e,s),l=i.getParent(a.getStart(),"LI",d);if(l){if((n=l.parentNode)===e.getBody()&&C(i,n))return!0;if(o=L(a.getRng(!0)),(r=i.getParent(Y(e,o,t,d),"LI",d))&&r!==l)return t?ne(e,o,r,l):function(e,t,n,o){var r=b(t);te(e.dom,n,o);var i=D(r);e.selection.setRng(i)}(e,o,l,r),!0;if(!r&&!t&&X.removeList(e))return!0}return!1},re=function(e,t){return oe(e,t)||function(e,t){var n=e.dom,o=e.selection.getStart(),r=E.getClosestListRootElm(e,o),i=n.getParent(o,n.isBlock,r);if(i&&n.isEmpty(i)){var a=L(e.selection.getRng(!0)),s=n.getParent(Y(e,a,t,r),"LI",r);if(s)return e.undoManager.transact(function(){var o,a,d,l;a=i,d=r,l=(o=n).getParent(a.parentNode,o.isBlock,d),o.remove(a),l&&o.isEmpty(l)&&o.remove(l),X.mergeWithAdjacentLists(n,s.parentNode),e.selection.select(s,!0),e.selection.collapse(t)}),!0}return!1}(e,t)},ie=function(e,t){return e.selection.isCollapsed()?re(e,t):(o=(n=e).selection.getStart(),r=E.getClosestListRootElm(n,o),!!(n.dom.getParent(o,"LI,DT,DD",r)||E.getSelectedListItems(n).length>0)&&(n.undoManager.transact(function(){n.execCommand("Delete"),I(n.dom,n.getBody())}),!0));var n,o,r},ae=function(e){e.on("keydown",function(t){t.keyCode===o.BACKSPACE?ie(e,!1)&&t.preventDefault():t.keyCode===o.DELETE&&ie(e,!0)&&t.preventDefault()})},se=ie,de=function(e){return{backspaceDelete:function(t){se(e,t)}}},le=a.DOM,ce=function(e,t){var n;if(l(e)){for(;n=e.firstChild;)t.appendChild(n);le.remove(e)}},fe=function(e){var t,n,o,r,i=E.getSelectedListItems(e);if(i.length){for(var a=b(e.selection.getRng(!0)),s=0;s<i.length&&(t=i[s],n=void 0,o=void 0,r=void 0,("DT"===t.nodeName?(le.rename(t,"DD"),1):(n=t.previousSibling)&&l(n)?(n.appendChild(t),1):n&&"LI"===n.nodeName&&l(n.lastChild)?(n.lastChild.appendChild(t),ce(t.lastChild,n.lastChild),1):(n=t.nextSibling)&&l(n)?(n.insertBefore(t,n.firstChild),1):(n=t.previousSibling)&&"LI"===n.nodeName&&(o=le.create(t.parentNode.nodeName),(r=le.getStyle(t.parentNode,"listStyleType"))&&le.setStyle(o,"listStyleType",r),n.appendChild(o),o.appendChild(t),ce(t.lastChild,o),1))||0!==s);s++);return e.selection.setRng(D(a)),e.nodeChanged(),!0}},ue=function(e,t){return function(){var n=e.dom.getParent(e.selection.getStart(),"UL,OL,DL");return n&&n.nodeName===t}},me=function(e){e.on("BeforeExecCommand",function(t){var n,o=t.command.toLowerCase();if("indent"===o?fe(e)&&(n=!0):"outdent"===o&&K(e)&&(n=!0),n)return e.fire("ExecCommand",{command:t.command}),t.preventDefault(),!0}),e.addCommand("InsertUnorderedList",function(t,n){X.toggleList(e,"UL",n)}),e.addCommand("InsertOrderedList",function(t,n){X.toggleList(e,"OL",n)}),e.addCommand("InsertDefinitionList",function(t,n){X.toggleList(e,"DL",n)}),e.addQueryStateHandler("InsertUnorderedList",ue(e,"UL")),e.addQueryStateHandler("InsertOrderedList",ue(e,"OL")),e.addQueryStateHandler("InsertDefinitionList",ue(e,"DL"))},ge=function(e){return e.getParam("lists_indent_on_tab",!0)},pe=function(e){var t;ge(e)&&(t=e).on("keydown",function(e){e.keyCode!==o.TAB||o.metaKeyPressed(e)||t.dom.getParent(t.selection.getStart(),"LI,DT,DD")&&(e.preventDefault(),e.shiftKey?K(t):fe(t))}),ae(e)},ve=function(e,t){return function(n){var o=n.control;e.on("NodeChange",function(e){var n=function(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return n;return-1}(e.parents,f),r=-1!==n?e.parents.slice(0,n):e.parents,a=i.grep(r,l);o.active(a.length>0&&a[0].nodeName===t)})}},he=function(e){var t,n,o,r;n="advlist",o=(t=e).settings.plugins?t.settings.plugins:"",-1===i.inArray(o.split(/[ ,]/),n)&&(e.addButton("numlist",{active:!1,title:"Numbered list",cmd:"InsertOrderedList",onPostRender:ve(e,"OL")}),e.addButton("bullist",{active:!1,title:"Bullet list",cmd:"InsertUnorderedList",onPostRender:ve(e,"UL")})),e.addButton("indent",{icon:"indent",title:"Increase indent",cmd:"Indent",onPostRender:(r=e,function(e){var t=e.control;r.on("nodechange",function(){var e=E.getSelectedListItems(r),n=e.length>0&&m(e[0]);t.disabled(n)})})})};e.add("lists",function(e){return pe(e),he(e),me(e),de(e)})}();
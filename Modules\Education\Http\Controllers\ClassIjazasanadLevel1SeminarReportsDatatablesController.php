<?php

namespace Modules\Education\Http\Controllers;


use App\Classes;
use App\EvaluationSchemaOption;
use App\IjazasanadMemorizationPlan;
use App\MoshafSurah;
use App\ProgramLevelLesson;
use App\StudentIjazasanadMemorizationReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;


class ClassIjazasanadLevel1SeminarReportsDatatablesController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function getRecords(Request $request)
    {

        DB::connection()->enableQueryLog();

        if ($request->filled('studentId') || $request->filled('classDate')) {

            $year = Carbon::parse($request->get('classDate'))->year;
            $month = Carbon::parse($request->get('classDate'))->month;
            $classId = $request->get('classId');


            // Fetching memorization reports based on class and date


            $StudentIjazasanadMemorizationReports = StudentIjazasanadMemorizationReport::where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereNull('hefz_from_surat')
                ->whereNull('hefz_from_ayat')
                ->whereNull('hefz_to_surat')
                ->whereNull('hefz_to_ayat')
                ->with(['student', 'ijazasanadMemorizationPlan'])
                ->get()
                ->groupBy('student_id')
                ->map(function ($records) {
                    $firstReport = $records->first();
                    $lastReport = $records->last();

                    $from_seminar = $records->min('seminars_from_lesson');
                    $to_seminar = $records->max('seminars_to_lesson');

                    $plannedLessons = $firstReport->ijazasanadMemorizationPlan
                        ? ($firstReport->ijazasanadMemorizationPlan->seminars_to_lesson - $firstReport->ijazasanadMemorizationPlan->seminars_from_lesson) + 1
                        : 0;

                    $completedLessons = ($to_seminar - $from_seminar) + 1;

                    return [
                        'student' => $firstReport->student,
                        'planned_lessons' => $plannedLessons,
                        'completed_lessons' => $completedLessons,
                        'from_seminar' => $from_seminar,
                        'to_seminar' => $to_seminar,
                    ];
                });

            return \Yajra\DataTables\DataTables::of($StudentIjazasanadMemorizationReports)
                ->addIndexColumn()
                ->addColumn('student', function ($reportDetails) {
                    $studentName = ucfirst($reportDetails['student']->full_name);
                    $studentProfileUrl = route('students.show', ['id' => $reportDetails['student']->user_id]);
                    return '<a style="color:#b4eeb0; padding: 2px; display: inline-block; transition: all 0.3s ease;" data-id=' . $reportDetails['student']->id . ' class="section class-link" target="_blank" href="' . $studentProfileUrl . '">' . $studentName . '</a>';
                })
                ->addColumn('monthlySeminarPlan', function ($reportDetails) {
                    $fromLesson = ProgramLevelLesson::find($reportDetails['from_seminar']);
                    $toLesson = ProgramLevelLesson::find($reportDetails['to_seminar']);

                    if ($fromLesson && $toLesson) {
                        $fromVerse = $fromLesson->properties['lessonName'] ?? '';
                        $toVerse = $toLesson->properties['lessonName'] ?? '';
                        $formattedLessonName = "From: {$fromVerse}<br>To: {$toVerse}";

                        return '<span style="color:#b4eeb0;">' . $formattedLessonName . '</span>';
                    }
                    return 'N/A';
                })
                ->addColumn('monthlySeminarReport', function ($reportDetails) {
                    $fromLesson = ProgramLevelLesson::find($reportDetails['from_seminar']);
                    $toLesson = ProgramLevelLesson::find($reportDetails['to_seminar']);

                    if ($fromLesson && $toLesson) {
                        $fromVerse = $fromLesson->properties['lessonName'] ?? '';
                        $toVerse = $toLesson->properties['lessonName'] ?? '';
                        $formattedLessonName = "From: {$fromVerse}<br>To: {$toVerse}";

                        return '<span style="color:#b4eeb0;">' . $formattedLessonName . '</span>';
                    }
                    return 'N/A';
                })
                ->addColumn('totalLessons', function ($reportDetails) {
                    return '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $reportDetails['completed_lessons'] . '</h2>';
                })
                ->addColumn('totalPlannedLessons', function ($reportDetails) {
                    return '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $reportDetails['planned_lessons'] . '</h2>';
                })
                ->addColumn('attendanceDaysPercentage', function ($reportDetails) use ($classId, $year, $month) {
                    $classTimetable = Classes::find($classId)->timetable;
                    if (!$classTimetable) {
                        return '0%';
                    }

                    $totalClasses = 0;
                    $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);

                    for ($day = 1; $day <= $daysInMonth; $day++) {
                        $dayOfWeek = strtolower(date('D', strtotime("$year-$month-$day")));
                        if (!is_null($classTimetable->$dayOfWeek)) {
                            $totalClasses++;
                        }
                    }

                    $attendedClasses = StudentIjazasanadMemorizationReport::where('class_id', $classId)
                        ->where('student_id', $reportDetails['student']->id)
                        ->whereYear('created_at', $year)
                        ->whereMonth('created_at', $month)
                        ->whereIn('attendance_id', [1, 2])
                        ->distinct('created_at')
                        ->count('created_at');

                    $attendancePercentage = $totalClasses > 0 ? round(($attendedClasses / $totalClasses) * 100, 2) : 0;

                    return '<div class="progress" style="position: relative;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' . $attendancePercentage . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $attendancePercentage . '%; background-color: #1fff0f;">
                        <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $attendancePercentage . '%</span>
                    </div>
                </div>';
                })
                ->addColumn('seminarAchievementComparedtoSeminarPlan', function ($reportDetails) {
                    $plannedLessons = $reportDetails['planned_lessons'];
                    $completedLessons = $reportDetails['completed_lessons'];

                    $result = 0;
                    if ($plannedLessons > 0) {
                        $result = round(($completedLessons / $plannedLessons) * 100, 2);
                    }

                    return '<div class="progress" style="position: relative;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' . $result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%;background-color: #1fff0f;">
                        <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
                    </div>
                </div>';
                })
                ->rawColumns(['totalPlannedLessons','monthlySeminarPlan','monthlySeminarReport','seminarAchievementComparedtoSeminarPlan','totalLessons','attendanceDaysPercentage','student'])
                ->make(true);




        }

    }


}

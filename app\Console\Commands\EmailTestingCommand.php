<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class EmailTestingCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'test:email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the email configuration by sending a test email';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            Log::info('Starting test:email command');
            
            // Get the test email address
            $email = $this->askForEmail();
            if (!$email) {
                return Command::FAILURE;
            }
            
            // Confirm sending
            if (!$this->confirmSending($email)) {
                $this->info('Email sending canceled by user');
                Log::info('Email test canceled by user');
                return Command::SUCCESS;
            }
            
            // Send the test email
            $this->sendTestEmail($email);
            
            $this->info("Email successfully sent to {$email}");
            Log::info("Test email successfully sent to {$email}");
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            Log::error('Email test failed: ' . $e->getMessage());
            $this->error('Email test failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
    
    /**
     * Ask for and validate an email address
     *
     * @return string|null
     */
    private function askForEmail()
    {
        $email = $this->ask('Please enter test email address');
        
        $validator = Validator::make(['email' => $email], [
            'email' => 'required|email',
        ]);
        
        if ($validator->fails()) {
            $this->error('Please enter a valid email address');
            Log::warning("Invalid email address provided: {$email}");
            return null;
        }
        
        return $email;
    }
    
    /**
     * Confirm sending the test email
     *
     * @param string $email
     * @return bool
     */
    private function confirmSending($email)
    {
        return $this->confirm("Do you want to send a test email to {$email}?");
    }
    
    /**
     * Send the test email
     *
     * @param string $email
     * @return void
     */
    private function sendTestEmail($email)
    {
        $this->info('Sending test email...');
        Log::info("Sending test email to {$email}");
        
        $subject = 'Test Email from ' . config('app.name');
        $content = 'This is a test email sent from the application at ' . now()->format('Y-m-d H:i:s');
        
        Mail::raw($content, function ($message) use ($email, $subject) {
            $message->to($email)->subject($subject);
        });
    }
}

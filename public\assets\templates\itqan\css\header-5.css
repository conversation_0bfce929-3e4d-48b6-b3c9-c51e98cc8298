#topMain>li>a {
	height:96px;
	line-height:76px;
}
#topMain.nav-pills>li {
	margin-left:10px !important;
}
#topMain.nav-pills>li:first-child,
#header.fixed #topMain.nav-pills>li {
	margin-left:0 !important;
}

#topMain>li>a>span.topMain-icon {
	padding:0 10px 0 10px;
	display:block;
	margin-top:15px;
}
	#header.fixed #topMain>li>a>span.topMain-icon {
		padding:0 15px;
	}
	#header.fixed #topMain>li>a>span.topMain-icon {
		margin-top:13px;
	}

#topMain.nav-pills>li>a {
	color:#1F262D;
	font-weight:400;
	background-color:transparent;
	padding-left:0 !important;
	padding-right:0 !important;
	line-height:15px !important;
	text-align:center;
} 

#topMain.nav-pills>li:hover>a, 
#topMain.nav-pills>li:focus>a {
	color:#1F262D;
}

#topMain.nav-pills>li>a>span.topMain-icon>i {
	display:block;
	font-size:22px;
	margin-bottom:10px;
}
	#header.fixed #topMain.nav-pills>li>a>span.topMain-icon>i {
		display:none;
	}

#topMain.nav-pills>li.active>a {
	color:#687482;
	background-color:transparent;
}

#topNav .navbar-collapse {
	float:right;
}

#topNav a.logo {
	height:96px;
	line-height:96px;
	overflow:hidden;
	display:inline-block;
}



@media only screen and (max-width: 1024px) {
	#topMain.nav-pills>li>a {
		font-size:13px;
	}
}

@media only screen and (max-width: 992px) {
	#topMain.nav-pills>li {
		margin-left:0 !important;
	}
	#topMain>li>a>span.topMain-icon {
		color:#151515;
		display:block !important;
		padding-top:0;
		margin-top:0;
		height:40px;
		border:0;
		text-align:left;

		-webkit-border-radius: 0;
		   -moz-border-radius: 0;
				border-radius: 0;
	}

	#topMain.nav-pills>li>a>span.topMain-icon>i {
		display:inline-block;
		font-size:15px;
		margin:0;
		padding:0 15px 0 0;
	}

	/* Force 60px */
	#header {
		height:60px !important;
	}
	#header #topNav a.logo {
		height:60px !important;
		line-height:50px !important;
	}
	#header #topNav a.logo>img {
		max-height:60px !important;
	}
	#header #topNav #topMain>li>a {
		height:40px !important;
		line-height:40px !important;
		padding-top:0;
	}


	#topMain>li {
		border-bottom:rgba(0,0,0,0.1) 1px solid;
	}
	#topMain>li:last-child {
		border-bottom:0;
	}

		#header li.search .search-box {
			margin:0 !important;
			position:fixed;
			left:0; right:0;
			top:60px !important;
			width:100%;
			background-color:#fff;
			border-top:rgba(0,0,0,0.1) 1px solid;
		}
}
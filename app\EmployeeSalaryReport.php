<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\EmployeeSalaryReport
 *
 * @property int $id
 * @property int $employee_id
 * @property string $report_month
 * @property float $amount
 * @property float $deducted_hours
 * @property int $annual_leaves_taken
 * @property int $medical_leaves_taken
 * @property int $other_leaves_taken
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport query()
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport whereAnnualLeavesTaken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport whereDeductedHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport whereEmployeeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport whereMedicalLeavesTaken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport whereOtherLeavesTaken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport whereReportMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EmployeeSalaryReport whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class EmployeeSalaryReport extends Model
{
    //
}

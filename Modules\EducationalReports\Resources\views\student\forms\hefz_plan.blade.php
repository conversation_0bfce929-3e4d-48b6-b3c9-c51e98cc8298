<div class="clearfix">
    <h5>Memorization and Revision Plan</h5>
    
    <div class="form-group {{ $errors->has('study_direction') ? 'has-error' : ''}}">
        {!! Form::label('study_direction', 'Study Direction', ['class' => 'col-md-4 control-label']) !!}
        <div class="col-md-6">
            {!! Form::select('hefz[study_direction]', ['forward' => 'Forward [Al-bakarah – Al-Nas]' , 'backward' => 'Backward [Al-nas – Al-Baqarah]' ] , isset($plan) && isset($plan) && $plan ? $plan->study_direction : null, ['class' => 'form-control' ,"required"]) !!}
        </div>   
    </div>
    <div class="form-group {{ $errors->has('level') ? 'has-error' : ''}}">    
        {!! Form::label('start_from', 'Start From', ['class' => 'col-md-4 control-label']) !!}    
        <div class="col-md-3">
            Surat 
            <select name="hefz[start_from_surat]" class="form-control" id="sowar">
                @foreach($surat as $order => $surah)
                    <option value="{{ $order+1 }}" num-ayat="{{ $surah['num_ayat'] }}" @if(isset($plan) && $plan->start_from_surat == $loop->index+1) selected @endif> {{ $surah['name'] }}</option>
                @endforeach
            </select>
        </div>
        <div class="col-md-3">
            Ayat {!! Form::number('hefz[start_from_ayat]' , isset($plan) && $plan ? $plan->start_from_ayat : 1, ['class' => 'form-control' , 'id' => 'ayat' , 'min' => 1 , 'max' => 7 ,"required"]) !!}
        </div>
    </div>

    <div class="form-group clearfix {{ $errors->has('level') ? 'has-error' : ''}}">
        {!! Form::label('study_plan', 'Study Plan', ['class' => 'col-md-4 control-label']) !!}
        <div class="col-md-2">
            {!! Form::number('hefz[num_to_memorize]' , isset($plan) && $plan ? $plan->num_to_memorize : 1, ['class' => 'form-control' , 'id' => 'ayat' , 'min' => 0.1 , 'placeholder' => '1'  ,"required" , "step" => "any"]) !!}       
        </div>
        <div class="col-md-2">                 
            {!! Form::select('hefz[memorization_mood]', ['page' => 'Page' , 'ayat' => 'Ayah' ] , isset($plan) && $plan ? $plan->memorization_mood : null, ['class' => 'form-control']) !!}
        </div>
        <div class="col-md-2 control-label text-left">
            <label>Per Class</label>
        </div>
    </div>
    <div class="form-group clearfix {{ $errors->has('level') ? 'has-error' : ''}}">
        {!! Form::label('study_plan', 'Revision Plan', ['class' => 'col-md-4 control-label']) !!}
        <div class="col-md-3">
            {!! Form::number('hefz[pages_to_revise]' , isset($plan) && $plan ? $plan->pages_to_revise : 1, ['class' => 'form-control' , 'id' => 'ayat' , 'min' => 0.1  ,"required" ,"step" => "any" ]) !!}       
        </div>
        <div class="col-md-3 control-label text-left">
            <label>Page Per Memorized Juz'</label>
        </div>
    </div>
    @if(isset($plan->start_date) )
            <div class="form-group clearfix ">
                <label for="start_date" class="col-md-4 control-label">Start Date</label>
                <div class="col-md-3">
                        {!! Form::text('hefz[start]' , isset($plan) && $plan ? $plan->start_date : 1, ['class' => 'form-control' , 'id' => 'ayat' , 'min' => 0.1  ,"required" ,"step" => "any" ]) !!}       
                    </div>
            </div>

    @endif
</div>

@section('js')
<script>
    $('#sowar').change(function(){
        $('#ayat').attr('max', parseInt($("#sowar option:selected").attr('num-ayat')));
    })
    $(document).ready(function () {
        $('#ayat').attr('max', parseInt($("#sowar option:selected").attr('num-ayat')));
        
    });
</script>
@append

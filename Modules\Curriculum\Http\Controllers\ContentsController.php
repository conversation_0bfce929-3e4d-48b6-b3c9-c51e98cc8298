<?php

namespace Modules\Curriculum\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Content;
use App\ContentCategory;
use App\EvaluationSchema;
use App\ContentEvaluationSchema;
use Illuminate\Http\Request;
use Session;

class ContentsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $keyword = $request->get('search');
        $perPage = 25;

        if (!empty($keyword)) {
            $contents = Content::with('category')->where('title', 'LIKE', "%$keyword%")
				->orWhere('content', 'LIKE', "%$keyword%")
				->orWhere('content_category_id', 'LIKE', "%$keyword%")
				->orWhere('locale', 'LIKE', "%$keyword%")
				->orWhere('status', 'LIKE', "%$keyword%")
				->paginate($perPage);
        } else {
            $contents = Content::with('category')->paginate($perPage);
        }

        return view('curriculum::contents.index', compact('contents'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $content_categories = ContentCategory::all()->pluck('title','id');

        $languages = [];

        foreach(config('app.locales') as $lang){
            $languages[$lang] = get_language_name($lang);
        }

        $evaluation_schemas = EvaluationSchema::where('target' , 'content')->where('status' , 1)->get()->plucK('title', 'id');

        return view('curriculum::contents.create', compact('content_categories' , 'evaluation_schemas' , 'languages'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        // return $request->all();
        $this->validateRequest($request);
        
        $requestData = $request->all();


        $requestData['organization_id'] = config('organization_id');

        $content = Content::create($requestData);

        if($request->contents_evaluation_schemas){
            foreach ($request->contents_evaluation_schemas as $evaluation_schema ) {
                ContentEvaluationSchema::create([
                    'content_id' => $content->id,
                    'evaluation_schema_id' => $evaluation_schema,
                    'status' => 'enabled',
                    'created_by' => auth()->user()->id
                ]);
            }
        }

        Session::flash('flash_message', 'Content added!');

        return redirect('workplace/curriculum/contents');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $content = Content::findOrFail($id);

        if(request()->ajax()){
            $response = [
                'title' => $content->title,
            ];
            foreach ($content->evaluation_schemas as $schema) {
                $response['evaluation_schemas'][$schema->id] = [
                    'title' => $schema->title,
                    'type' => $schema->type,
                    'options' => $schema->options
                ];
            }

            return $response;
        }

        return view('curriculum::contents.show', compact('content'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $content_categories = ContentCategory::all()->pluck('title','id');
        
        $languages = [];

        foreach(config('app.locales') as $lang){
            $languages[$lang] = get_language_name($lang);
        }
        $content = Content::findOrFail($id);

        $evaluation_schemas = EvaluationSchema::where('target' , 'content')->where('status' , 1)->get()->plucK('title', 'id');

        $evaluation_schemas = array_diff_key($evaluation_schemas->toArray(), array_flip($content->evaluation_schemas->pluck('id')->toArray()));

        return view('curriculum::contents.edit', compact('content' , 'evaluation_schemas' ,'content_categories' , 'languages'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {
        
        $requestData = $request->all();
        
        $content = Content::findOrFail($id);
        $content->update($requestData);

        if($request->contents_evaluation_schemas){
            foreach ($request->contents_evaluation_schemas as $evaluation_schema ) {
                ContentEvaluationSchema::create([
                    'content_id' => $content->id,
                    'evaluation_schema_id' => $evaluation_schema,
                    'status' => 'enabled',
                    'created_by' => auth()->user()->id
                ]);
            }
        }
        
        Session::flash('flash_message', 'Content updated!');

        return redirect('workplace/curriculum/contents');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Content::destroy($id);

        Session::flash('flash_message', 'Content deleted!');

        return redirect('workplace/curriculum/contents');
    }

    private function validateRequest($request , $rules = [])
    {
        $rules['title'] = 'required';
        $rules['content_category_id'] = 'required';
        $rules['language'] = 'required';
        $this->validate($request , $rules);
    }
}

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class JobNotificationSentJob extends Model
{
    protected $table = 'job_notification_sent_jobs';
    
    protected $fillable = [
        'setup_id',
        'job_id',
        'recipient_email',
        'sent_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'sent_at' => 'datetime',
    ];

    /**
     * Get the notification setup that this sent job belongs to.
     */
    public function setup(): BelongsTo
    {
        return $this->belongsTo(JobNotificationSetup::class, 'setup_id');
    }

    /**
     * Get the job that was sent.
     */
    public function job(): BelongsTo
    {
        return $this->belongsTo(Job::class, 'job_id');
    }

    /**
     * Scope a query to only include notifications sent to a specific recipient.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $email
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForRecipient($query, string $email)
    {
        return $query->where('recipient_email', $email);
    }

    /**
     * Scope a query to only include notifications for a specific setup.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $setupId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForSetup($query, int $setupId)
    {
        return $query->where('setup_id', $setupId);
    }

    /**
     * Scope a query to only include notifications for a specific job.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $jobId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForJob($query, int $jobId)
    {
        return $query->where('job_id', $jobId);
    }

    /**
     * Scope a query to only include recent notifications.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $days
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('sent_at', '>=', now()->subDays($days));
    }
} 
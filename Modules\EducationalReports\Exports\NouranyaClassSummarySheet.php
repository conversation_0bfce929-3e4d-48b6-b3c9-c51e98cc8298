<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\Classes;
use App\Student;
use App\StudentNouranyaPlan;
use App\StudentNouranyaReport;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * NouranyaClassSummarySheet creates the Class Summary sheet for Nouranya reports.
 * 
 * Purpose: Export per-class summary metrics for Nouranya programs aggregated across students.
 * Data source: Aggregates from student_nouranya_reports and student_nouranya_plans.
 * Calculations: Average attendance/achievement, total planned/completed lessons and line metrics.
 * Context: Mirrors the aggregated DataTables structure from MonthEndNouranyaSummaryAggregatedController.
 * Output: Single sheet with per-class summary rows showing key performance indicators.
 */
final class NouranyaClassSummarySheet implements WithTitle, WithEvents
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get class summary data aggregated across students
     */
    private function getClassSummaryData(): array
    {
        $classIds = $this->filters['classIds'];
        $month = $this->filters['month'];
        $year = $this->filters['year'];
        $studentIds = $this->filters['studentIds'] ?? [];

        $results = [];

        foreach ($classIds as $classId) {
            $class = Classes::find($classId);
            if (!$class) {
                continue;
            }

            // Load active students for this class
            $query = Student::whereHas('joint_classes', function ($q) use ($classId) {
                $q->where('class_id', $classId);
            })
            ->where('status', 'active')
            ->orderBy('full_name', 'asc');

            if (!empty($studentIds)) {
                $query->whereIn('id', $studentIds);
            }

            $students = $query->get();

            $studentCount = $students->count();
            $avgAttendance = $this->calculateAverageAttendance($students, $classId, $month, $year);
            $avgAchievement = $this->calculateAverageAchievement($students, $classId, $month, $year);

            $lessonMetrics = $this->calculateLessonMetrics($students, $classId, $month, $year);
            $lineMetrics = $this->calculateLineMetrics($students, $classId, $month, $year);

            $results[] = [
                'center_name' => $class->center->name ?? 'Unknown Center',
                'class_name' => $class->class_code ?? $class->name ?? 'Unknown Class',
                'no_of_students' => $studentCount,
                'avg_attendance' => number_format($avgAttendance, 1) . '%',
                'avg_achievement' => number_format($avgAchievement, 1) . '%',
                'total_planned_lessons' => $lessonMetrics['planned'],
                'total_completed_lessons' => $lessonMetrics['completed'],
                'lesson_progress' => number_format($lessonMetrics['percentage'], 1) . '%',
                'total_planned_lines' => $lineMetrics['planned'],
                'total_completed_lines' => $lineMetrics['completed'],
                'line_progress' => number_format($lineMetrics['percentage'], 1) . '%',
            ];
        }

        return $results;
    }

    /**
     * Calculate average attendance across students in a class
     */
    private function calculateAverageAttendance($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) {
            return 0.0;
        }

        $class = Classes::find($classId);
        if (!$class || !$class->timetable) {
            return 0.0;
        }

        $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
        if ($totalClasses <= 0) {
            return 0.0;
        }

        $sum = 0;
        $count = 0;

        foreach ($students as $student) {
            $attended = StudentNouranyaReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereIn('attendance_id', [1, 2]) // Late and On Time
                ->count();

            $sum += min(100.0, ($attended / $totalClasses) * 100);
            $count++;
        }

        return $count > 0 ? $sum / $count : 0.0;
    }

    /**
     * Calculate average achievement across students in a class
     */
    private function calculateAverageAchievement($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) {
            return 0.0;
        }

        $sum = 0;
        $count = 0;

        foreach ($students as $student) {
            $plan = StudentNouranyaPlan::where('student_id', $student->id)
                ->where(function ($q) use ($month, $year, $classId) {
                    $q->where(function ($q1) use ($year, $month, $classId) {
                        $q1->whereYear('created_at', $year)
                           ->whereMonth('created_at', $month)
                           ->where('class_id', $classId)
                           ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                           ->whereMonth('start_date', $month)
                           ->where('class_id', $classId)
                           ->where('status', 'active');
                    });
                })
                ->first();

            if (!$plan) {
                continue;
            }

            $reports = StudentNouranyaReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            if ($reports->isEmpty()) {
                continue;
            }

            $percent = $this->calculateLessonCompletion($plan, $reports);
            if ($percent > 0) {
                $sum += $percent;
                $count++;
            }
        }

        return $count > 0 ? $sum / $count : 0.0;
    }

    /**
     * Calculate lesson completion percentage
     */
    private function calculateLessonCompletion($plan, $reports): float
    {
        $plannedLessons = 0;
        $achievedLessons = 0;

        // Calculate planned lessons
        if ($plan->from_lesson && $plan->to_lesson && $plan->from_lesson <= $plan->to_lesson) {
            $plannedLessons = $plan->to_lesson - $plan->from_lesson + 1;
        }

        // Calculate achieved lessons from reports
        $lessonSet = collect();
        foreach ($reports as $report) {
            $ln = $report->to_lesson ?? $report->from_lesson;
            if ($ln) { $lessonSet->push($ln); }
        }
        $achievedLessons = $lessonSet->unique()->count();

        return $plannedLessons > 0 ? min(100, round(($achievedLessons / $plannedLessons) * 100, 1)) : 0;
    }

    /**
     * Calculate lesson metrics (planned vs completed)
     */
    private function calculateLessonMetrics($students, int $classId, int $month, int $year): array
    {
        if ($students->isEmpty()) {
            return ['planned' => 0, 'completed' => 0, 'percentage' => 0];
        }

        $planned = 0;
        $completed = 0;

        foreach ($students as $student) {
            $plan = StudentNouranyaPlan::where('student_id', $student->id)
                ->where(function ($q) use ($month, $year, $classId) {
                    $q->where(function ($q1) use ($year, $month, $classId) {
                        $q1->whereYear('created_at', $year)
                           ->whereMonth('created_at', $month)
                           ->where('class_id', $classId)
                           ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                           ->whereMonth('start_date', $month)
                           ->where('class_id', $classId)
                           ->where('status', 'active');
                    });
                })
                ->first();

            if ($plan && $plan->from_lesson && $plan->to_lesson && $plan->from_lesson <= $plan->to_lesson) {
                $planned += ($plan->to_lesson - $plan->from_lesson + 1);
            }

            $reports = StudentNouranyaReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->where(function($q){ $q->whereNotNull('to_lesson')->orWhereNotNull('from_lesson'); })
                ->get();

            if ($reports->isNotEmpty()) {
                $completed += $reports->map(function($r){ return $r->to_lesson ?? $r->from_lesson; })->filter()->unique()->count();
            }
        }

        $percentage = $planned > 0 ? min(100, round(($completed / $planned) * 100, 1)) : 0;

        return ['planned' => $planned, 'completed' => $completed, 'percentage' => $percentage];
    }

    /**
     * Calculate line metrics (planned vs completed)
     */
    private function calculateLineMetrics($students, int $classId, int $month, int $year): array
    {
        if ($students->isEmpty()) {
            return ['planned' => 0, 'completed' => 0, 'percentage' => 0];
        }

        $planned = 0;
        $completed = 0;

        foreach ($students as $student) {
            $plan = StudentNouranyaPlan::where('student_id', $student->id)
                ->where(function ($q) use ($month, $year, $classId) {
                    $q->where(function ($q1) use ($year, $month, $classId) {
                        $q1->whereYear('created_at', $year)
                           ->whereMonth('created_at', $month)
                           ->where('class_id', $classId)
                           ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                           ->whereMonth('start_date', $month)
                           ->where('class_id', $classId)
                           ->where('status', 'active');
                    });
                })
                ->first();

            if ($plan) {
                $plannedLines = 0;
                if ($plan->from_lesson_line_number && $plan->to_lesson_line_number && 
                    $plan->from_lesson_line_number <= $plan->to_lesson_line_number) {
                    $plannedLines = $plan->to_lesson_line_number - $plan->from_lesson_line_number + 1;
                }
                $planned += $plannedLines;
            }

            $reports = StudentNouranyaReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            foreach ($reports as $report) {
                $lineCount = 0;
                if ($report->from_lesson_line_number && $report->to_lesson_line_number && 
                    $report->from_lesson_line_number <= $report->to_lesson_line_number) {
                    $lineCount = $report->to_lesson_line_number - $report->from_lesson_line_number + 1;
                } elseif ($report->from_lesson_line_number || $report->to_lesson_line_number) {
                    $lineCount = 1; // Single line
                }
                $completed += $lineCount;
            }
        }

        $percentage = $planned > 0 ? min(100, round(($completed / $planned) * 100, 1)) : 0;

        return ['planned' => $planned, 'completed' => $completed, 'percentage' => $percentage];
    }

    /**
     * Get table headings
     */
    private function getTableHeadings(): array
    {
        return [
            'Center',
            'Class',
            'No. of Students',
            'Avg. Attendance %',
            'Avg. Achievement %',
            'Total Planned Lessons',
            'Total Completed Lessons',
            'Lesson Progress %',
            'Total Planned Lines',
            'Total Completed Lines',
            'Line Progress %'
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Class Summary';
    }

    /**
     * Register events for creating the styled table
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $this->createStyledTable($event->sheet);
            },
        ];
    }

    /**
     * Create styled table with data
     */
    private function createStyledTable($sheet)
    {
        $worksheet = $sheet->getDelegate();
        
        // Get data and headings
        $classData = $this->getClassSummaryData();
        $headings = $this->getTableHeadings();

        // Set main title
        $monthName = $this->filters['monthName'];
        $year = $this->filters['year'];
        $classNames = collect($this->filters['classes'])->pluck('class_code')->join(', ');
        
        $title = "NOURANYA CLASS SUMMARY - {$classNames} - {$monthName} {$year}";
        $worksheet->setCellValue('A1', $title);
        $worksheet->mergeCells('A1:K1');

        // Style main title
        $worksheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 16, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '6f42c1']]
        ]);

        $currentRow = 3;

        // Set headers
        $col = 'A';
        foreach ($headings as $heading) {
            $worksheet->setCellValue($col . $currentRow, $heading);
            $col++;
        }

        // Style headers
        $worksheet->getStyle("A{$currentRow}:K{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '8b5cf6']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $dataStartRow = $currentRow + 1;
        $currentDataRow = $dataStartRow;

        // Add data rows
        foreach ($classData as $row) {
            $col = 'A';
            foreach ($row as $value) {
                $worksheet->setCellValue($col . $currentDataRow, $value);
                $col++;
            }
            $currentDataRow++;
        }

        // Style data rows
        if (count($classData) > 0) {
            $dataEndRow = $currentDataRow - 1;
            $worksheet->getStyle("A{$dataStartRow}:K{$dataEndRow}")->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ]);

            // Center-align numeric columns
            $worksheet->getStyle("C{$dataStartRow}:K{$dataEndRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        } else {
            // No data message
            $worksheet->setCellValue("A{$dataStartRow}", 'No Nouranya class data found for the specified criteria');
            $worksheet->mergeCells("A{$dataStartRow}:K{$dataStartRow}");
            $worksheet->getStyle("A{$dataStartRow}")->applyFromArray([
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'font' => ['italic' => true],
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ]
            ]);
        }

        // Auto-size columns
        foreach (range('A', 'K') as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }
    }
}

<?php

declare(strict_types=1);

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

/**
 * ExecuteArtisanCommand Job
 * 
 * Executes Artisan commands asynchronously with proper logging and error handling.
 * Replaces closure dispatching to improve queue compatibility and observability.
 */
final class ExecuteArtisanCommand implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public int $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public int $timeout = 3600; // 1 hour default

    /**
     * @var int
     */
    private int $ruleId;

    /**
     * @var string
     */
    private string $ruleName;

    /**
     * @var string
     */
    private string $command;

    /**
     * Create a new job instance.
     *
     * @param int $ruleId
     * @param string $ruleName
     * @param string $command
     */
    public function __construct(int $ruleId, string $ruleName, string $command)
    {
        $this->ruleId = $ruleId;
        $this->ruleName = $ruleName;
        $this->command = $command;
        
        // Set job timeout based on command type
        $this->timeout = $this->getCommandTimeout($command);
        
        Log::debug('ExecuteArtisanCommand: Job created', [
            'rule_id' => $this->ruleId,
            'rule_name' => $this->ruleName,
            'command' => $this->command,
            'timeout' => $this->timeout
        ]);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            Log::info('ExecuteArtisanCommand: Starting dependent command execution', [
                'rule_id' => $this->ruleId,
                'rule_name' => $this->ruleName,
                'command' => $this->command,
                'attempt' => $this->attempts(),
                'started_at' => now()->format('Y-m-d H:i:s T')
            ]);

            $startTime = microtime(true);

            // Execute the Artisan command
            $exitCode = Artisan::call($this->command);
            $output = Artisan::output();

            $executionTime = round(microtime(true) - $startTime, 2);

            if ($exitCode === 0) {
                Log::info('ExecuteArtisanCommand: Dependent command executed successfully', [
                    'rule_id' => $this->ruleId,
                    'rule_name' => $this->ruleName,
                    'command' => $this->command,
                    'exit_code' => $exitCode,
                    'execution_time_seconds' => $executionTime,
                    'output_length' => strlen($output),
                    'completed_at' => now()->format('Y-m-d H:i:s T')
                ]);

                // Log first few lines of output for debugging (avoid logging large outputs)
                $outputLines = explode("\n", trim($output));
                $previewLines = array_slice($outputLines, 0, 5);
                if (count($outputLines) > 5) {
                    $previewLines[] = '... (' . (count($outputLines) - 5) . ' more lines)';
                }
                
                Log::debug('ExecuteArtisanCommand: Command output preview', [
                    'rule_id' => $this->ruleId,
                    'command' => $this->command,
                    'output_preview' => $previewLines
                ]);
            } else {
                Log::error('ExecuteArtisanCommand: Dependent command failed', [
                    'rule_id' => $this->ruleId,
                    'rule_name' => $this->ruleName,
                    'command' => $this->command,
                    'exit_code' => $exitCode,
                    'execution_time_seconds' => $executionTime,
                    'output' => $output,
                    'attempt' => $this->attempts()
                ]);

                throw new \RuntimeException("Artisan command '{$this->command}' failed with exit code {$exitCode}");
            }

        } catch (\Exception $e) {
            Log::error('ExecuteArtisanCommand: Exception during command execution', [
                'rule_id' => $this->ruleId,
                'rule_name' => $this->ruleName,
                'command' => $this->command,
                'error' => $e->getMessage(),
                'attempt' => $this->attempts(),
                'max_tries' => $this->tries,
                'trace' => $e->getTraceAsString()
            ]);

            // Re-throw to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        Log::critical('ExecuteArtisanCommand: Job failed after all retry attempts', [
            'rule_id' => $this->ruleId,
            'rule_name' => $this->ruleName,
            'command' => $this->command,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
            'max_tries' => $this->tries,
            'failed_at' => now()->format('Y-m-d H:i:s T')
        ]);

        // You could add additional failure handling here, such as:
        // - Sending notifications to administrators
        // - Disabling the problematic rule
        // - Logging to external monitoring systems
    }

    /**
     * Get command-specific timeout in seconds
     *
     * @param string $command
     * @return int
     */
    private function getCommandTimeout(string $command): int
    {
        // Set timeouts based on command patterns
        $timeouts = [
            'jobseeker:sync-jobs-af' => 1800,  // 30 minutes
            'jobseeker:fetch-jobs-af-descriptions' => 2400,  // 40 minutes
            'jobseeker:sync-acbar-jobs' => 1200,  // 20 minutes
            'jobseeker:cleanup-old-jobs' => 600,   // 10 minutes
            'jobseeker:cleanup-stale-device-tokens' => 300,  // 5 minutes
        ];

        // Check for exact match first
        if (isset($timeouts[$command])) {
            return $timeouts[$command];
        }

        // Check for partial matches
        foreach ($timeouts as $pattern => $timeout) {
            if (str_contains($command, $pattern)) {
                return $timeout;
            }
        }

        // Default timeout for unknown commands
        return 3600; // 1 hour
    }

    /**
     * Get the tags for the job (useful for monitoring in Horizon)
     *
     * @return array
     */
    public function tags(): array
    {
        return [
            'artisan-command',
            'dependent-execution',
            'rule-' . $this->ruleId,
            'command-' . str_replace(':', '-', $this->command)
        ];
    }
} 
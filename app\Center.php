<?php

namespace App;

use App\Scopes\EmployeeCenterAccessScope;
use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Translatable;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\SoftDeletes;


class Center extends Model
{
    
    use Translatable,SoftDeletes;
    
    /**
        * The attributes that should be mutated to dates.
        *
        * @var array
        */
    protected $casts = ['deleted_at'];


    public $translatedAttributes = array('name' , 'description');
    
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'centers';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';




    public function averageClassAttendancePercentage($month, $year) {
        $totalClassesAttendancePercentage = 0;

        // Get all classes of the center
//        $classes = $this->classes;
        $classes = $this->classes()->whereHas('hefz_plans', function ($query) use ($year, $month) {
            $query->where('status', 'active')
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month);
        })->get();


//        $classesCount = $classes->count();
        $classesCount = $this->classes()->whereHas('hefz_plans', function ($query) use ($year, $month) {
            $query->where('status', 'active')
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month);
        })->count();
        if ($classesCount === 0) {
            return null;
        }
        $attendancePercentageData = [];

        foreach ($classes as $class) {
//            $totalClassesAttendancePercentage += $class->averageAttendancePercentage($month, $year);
            $attendancePercentageData[$class->id] = $class->averageAttendancePercentage($month, $year);
        }

        $totalClassesAttendancePercentage =  collect($attendancePercentageData)->sum();




        return round($totalClassesAttendancePercentage / $classesCount, 2);
    }


    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['location', 'organization_id', 'status','phone'];

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
//        static::addGlobalScope(new EmployeeCenterAccessScope);

    }

    public function admissions()
    {
        return $this->hasMany(\App\Admission::class)->where('status', 'active');
    }

    
    public function programs()
    {
        return $this->belongsToMany('App\Program', 'center_programs');
    }

    public function center_translations()
    {
        return $this->hasMany('App\CenterTranslation', 'center_id');
    }

    public function classes()
    {
        return $this->hasMany('App\Classes');

    }


    // for supervisor
    public function employee(){

        return $this->belongsToMany(Employee::class,'cen_emps','cen_id','emp_id')
            ->using(CenterEmployee::class)->withTimestamps();
    }


    public function teachers(){

        return $this->belongsToMany(Employee::class,'cen_teachers','cen_id','emp_id')
            ->using(CenterTeacher::class)->withTimestamps();
    }

    public function hefzPlan(){

        return $this->hasMany(StudentHefzPlan::class,'center_id');
    }

    public function users()
    {
        return $this->belongsToMany(
            User::class,
            'organization_center_user',
            'center_id',
            'user_id'
        )->withTimestamps();
    }


}

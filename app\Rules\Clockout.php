<?php

namespace App\Rules;

use App\Attendance;
use App\User;
use Illuminate\Contracts\Validation\Rule;

class Clockout implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Checks if an employee can clock out based on their last attendance record.
     *
     * @param string $attribute The name of the field being validated.
     * @param mixed $value The value of the field being validated.
     * @return bool True if the employee's last attendance record for today is 'in', false otherwise.
     */
    public function passes($attribute, $value)
    {


        $lastAttendanceType = Attendance::whereDate('clock',date('Y-m-d'))->where('employee_id',  auth()->guard('employee')->user()->id)->orderBy('id','desc')->first()->type;
        if($lastAttendanceType == 'in') {
            return true;

        }
        else {
            return false;
        }


    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The :attribute (Clock-Out) can not take place before Clock-in. please clock-in first';
    }
}

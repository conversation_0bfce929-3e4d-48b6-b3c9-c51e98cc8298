<?php

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\AdmissionInterview;
use App\Http\Controllers\Controller;
use App\IjazasanadMemorizationPlan;
use App\StudentLastApprovedPlan;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;
use App\Student;

class ApproveIjazasanadMemorizationPlanController extends Controller
{

    public function __invoke(Request $request)
    {




        try {
            DB::beginTransaction();

            // Ensure 'id' is an array
            $ids = $request->id;
            if (!is_array($ids)) {
                throw new \Exception('Invalid data format.');
            }

            foreach ($ids as $id) {
                $plan = IjazasanadMemorizationPlan::find($id);

                if (!$plan) {
                    throw new ModelNotFoundException('Plan not found for ID: ' . $id);
                }

                $plan->status = 'active';
                $plan->approved_by = auth()->user()->id;
                $plan->updated_at = Carbon::now(); // Explicitly updating the 'updated_at' field

                $plan->save();

                \App\StudentIjazasanadMemorizationLastApprovedPlan::updateOrInsert(
                    ['student_id' => $plan->student_id],
                    [
                        'approved_by' => $plan->approved_by,
                        'plan_year_month_day' => $plan->created_at->toDateString(),
                        'from_surat' => $plan->start_from_surat,
                        'from_ayat' => $plan->start_from_ayat,
                        'to_surat' => $plan->to_surat,
                        'to_ayat' => $plan->to_ayat,
                    ]
                );

                $admissionId = Admission::where('student_id', $plan->student_id)->first()->id;
                AdmissionInterview::where('admission_id', $admissionId)->update([
                    'status' => 'interviewed',
                    'confirmed_at' => Carbon::now(),
                    'updated_by' => auth()->user()->id
                ]);
            }


            $hefzPlansWaiting_approval = \App\IjazasanadMemorizationPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();
            $RevisionPlanWaiting_approval = \App\StudentRevisionPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();


            $NouranyaPlanWaitingApproval = \App\StudentNouranyaPlan::has('student')  // Ensure the plan is associated with a student
            ->has('center')  // Ensure the plan is associated with a center
            ->where('status', 'waiting_for_approval')  // Filter by status 'waiting_for_approval'
            ->where(function ($query) {
                // Level 1: Check only 'from_lesson' and 'to_lesson'
                $query->where(function ($level1Query) {
                    $level1Query->whereNotNull('from_lesson')
                        ->whereNotNull('to_lesson');
                })
                    // Level 2: Check 'from_lesson', 'to_lesson', 'from_lesson_line_number', and 'to_lesson_line_number'
                    ->orWhere(function ($level2Query) {
                        $level2Query->whereNotNull('from_lesson')
                            ->whereNotNull('to_lesson')
                            ->whereNotNull('from_lesson_line_number')
                            ->whereNotNull('to_lesson_line_number');
                    })
                    // Level 3: Check 'talaqqi_from_lesson', 'talaqqi_to_lesson', 'talqeen_from_lesson', and 'talqeen_to_lesson'
                    ->orWhere(function ($level3Query) {
                        $level3Query->whereNotNull('talaqqi_from_lesson')
                            ->whereNotNull('talaqqi_to_lesson')
                            ->whereNotNull('talqeen_from_lesson')
                            ->whereNotNull('talqeen_to_lesson');
                    });
            })
                ->count();  // Get the count of such records

            $ijazasanadMemorizationPlanWaitingApproval = \App\IjazasanadMemorizationPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();
//            $ijazasanadRevisionPlanWaitingApproval = \App\IjazasanadRevisionPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
//                ->whereNotNull('start_from_surat')
//                ->whereNotNull('start_from_ayat')
//                ->whereNotNull('to_surat')
//                ->whereNotNull('to_ayat')
//                ->count();

            $plans_waiting_approval = $hefzPlansWaiting_approval + $RevisionPlanWaiting_approval+$NouranyaPlanWaitingApproval+$ijazasanadMemorizationPlanWaitingApproval;

            DB::commit();

            // Assuming Toastr is a service or a facade for handling toast notifications
            Toastr::success('Selected Memorization Plans Approved!', 'Success');
            return response()->json(['message' => 'Selected Memorization Plans Approved!','plansWaitingApprovalCountWidget' => $plans_waiting_approval]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => $e->getMessage()], 500);
        }


    }

}

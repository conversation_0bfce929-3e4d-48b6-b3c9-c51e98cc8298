<?php

namespace Modules\Admission\Http\Controllers;

use App\Center;
use App\CenterTranslation;
use App\ClassStudent;
use App\Country;
use App\Notifications\SuperiorResetPassword;
use App\Notifications\UserStatusChangedToNewApplication;
use App\Organization;
use App\Program;
use App\Scopes\OrganizationScope;
use App\StudentAdmissionHefzPlan;
use App\StudentHefzReport;
use App\StudentRevisionPlan;
use App\StudentRevisionReport;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Faker\Factory;
use Faker\Provider\en_UG\PhoneNumber;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Mail\StudentCreated;
use App\Student;
use App\Guardian;
use App\Employee;
use App\Role;
use App\Cen_Emp;
use App\Admission;
use App\Classes;
use App\StudentHefzPlan;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Mail;
use PhpParser\Node\Stmt\Foreach_;
use Yajra\DataTables\Facades\DataTables;

class ClassesDataBasedonStudentProgramLevelsCentersForExamedStudentsController extends Controller
{


    public function __invoke(Request $request)
    {


        $classes = Classes::whereHas('onlineExam', function ($tq) use ($request) {

            if ($request->filled('program')) {

                $tq->where('program_id', $request->get('program'));


            }

            if ($request->filled('programLevel')) {

                $tq->whereHas('program.levels', function ($tq2) use ($request) {
                    $tq2->where('id', $request->get('programLevel'));


                });
            }
        })->when($request->filled("center"), function ($query, $make) use ($request) {
                $query->whereHas('center', function ($tq) use ($request) {
                    $tq->where('id', $request->get('center'));
                });
            })->toSql();


        return response()->json(["success" => true, "results" => $classes], 200);

    }


}
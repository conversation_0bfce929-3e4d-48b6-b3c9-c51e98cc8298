<?php

namespace Modules\Education\Http\Controllers;

use App\Center;
use App\Classes;
use App\Employee;
use App\Student;
use App\StudentIjazasanadMemorizationReport;
use App\StudentRevisionReport;
use App\Weekend;
use App\ClassRoom;
use App\YearCheck;
use App\ApiBaseMethod;
use Illuminate\Http\Request;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use PDF; // assuming you have the alias set for DOMPDF

class IjazasanadClassTablesPDFController extends Controller
{

    public function __construct()
    {
//        $this->middleware('PM');
        // User::checkAuth();
    }

    public function downloadPDF($classId,$monthYear)
    {

        // Convert the monthYear string to a Carbon instance
        $date = \Carbon\Carbon::createFromFormat('F Y', $monthYear);

// Extract the month and year separately
        $monthName = $date->monthName; // Numeric representation of the month (e.g., 06)
        $month = $date->format('m'); // Numeric representation of the month (e.g., 06)
        $year = $date->format('Y'); // 4-digit year (e.g., 2023)
        $letterHead = asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg");
        $class = Classes::find($classId);
        $className = $class->name; // Assuming the column name is 'full_name'
        $centerName = $class->center->name;
        $supervisors = $class->employee; // Assuming this returns the supervisor information
        $classTeachers = $class->teachers()->pluck('full_name')->toArray();


        // Check if $supervisors is not null
        if ($supervisors) {
            $supervisorInfo = $supervisors->pluck('name')->join(', ');
        } else {
            $supervisorInfo = ''; // Default value if no supervisors are found
        }


        // Retrieve cached report segments
        $talqeenReport   = $this->getCachedTalqeenReport($classId, $year, $month); // Assume you already have this
        $jazariyahReport = $this->getCachedJazariyahReport($classId, $year, $month);
        $seminarReport   = $this->getCachedSeminarReport($classId, $year, $month);
        $summaryData     = $this->getCachedSummaryData($classId, $year, $month);
        $memorizationDetails   = $this->getCachedMemorizationDetails($classId, $month, $year);



        $data = [
            'className' => $className,
            'classTeachers' => $classTeachers,
            'centerName' => $centerName,
            'letterHead' => $letterHead,
            'supervisors' => $supervisorInfo,
            'monthYear' => $date,
            'classId' =>  $classId,  // Retrieve and assign the required data
            'year' => $year ,  // Retrieve and assign the required data
            'month' => $month ,  // Retrieve and assign the required data
            'studentIjazasanadTalqeenReport'    => $talqeenReport,
            'studentIjazasanadJazariyahReport'  => $jazariyahReport,
            'studentIjazasanadSeminarReport'    => $seminarReport,
            'summaryData'      => $summaryData,
            'memorizationDetails'          => $memorizationDetails,
//            'classSummaryReportDetails'    => $classSummaryReportDetails,
            'monthName' => $monthName ,  ];




        $fileName = "Report_{$centerName}_{$className}_{$monthName}_{$year}.pdf";
//        view()->share('educationalreports::reports.pdf.class',$data);
//        view()->share('education::classes.reports.pdf.class.ijazasanadAll',$data);
        $pdf = PDF::loadView('education::classes.reports.pdf.class.ijazasanadAll', $data);
        return $pdf->download($fileName);
    }




    /**
     * Cache the Talqeen report aggregation.
     */
    private function getCachedTalqeenReport($classId, $year, $month)
    {
        // Set a cache key that uniquely identifies this query (adjust the lifetime as needed)
        $cacheKey = "talqeen_report_{$classId}_{$year}_{$month}";

        $reports = \App\StudentIjazasanadMemorizationReport::where('class_id', $classId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->whereNull('hefz_from_surat')
            ->whereNull('hefz_from_ayat')
            ->whereNull('hefz_to_surat')
            ->whereNull('hefz_to_ayat')
            ->with(['student', 'ijazasanadMemorizationPlan'])
            ->get();

// Now sort the collection by the related student's full_name
        $sortedReports = $reports->sortBy(function ($report) {
            return $report->student->full_name;
        });

// Then you can group by student_id and process further:
        $groupedReports = $sortedReports->groupBy('student_id')
            ->map(function ($records) {
                $firstReport = $records->first();
                $from_talqeen = $records->min('talqeen_from_lesson');
                $to_talqeen   = $records->max('talqeen_to_lesson');

                $plannedLessons = $firstReport->ijazasanadMemorizationPlan
                    ? ($firstReport->ijazasanadMemorizationPlan->talqeen_to_lesson - $firstReport->ijazasanadMemorizationPlan->talqeen_from_lesson) + 1
                    : 0;

                $completedLessons = ($to_talqeen - $from_talqeen) + 1;

                return [
                    'record'            => $records,
                    'student'           => $firstReport->student,
                    'planned_lessons'   => $plannedLessons,
                    'completed_lessons' => $completedLessons,
                    'from_talqeen'      => $from_talqeen,
                    'to_talqeen'        => $to_talqeen,
                ];
            });

        return \Cache::remember($cacheKey, 3600, function() use ($groupedReports) {
            return $groupedReports;
        });

    }


    private function getCachedJazariyahReport($classId, $year, $month)
    {
        $cacheKey = "jazariyah_report_{$classId}_{$year}_{$month}";
        return \Cache::remember($cacheKey, 3600, function() use ($classId, $year, $month) {
            return \App\StudentIjazasanadMemorizationReport::where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereNull('jazariyah_from_lesson')
                ->whereNull('jazariyah_to_lesson')
//                ->whereNull('hefz_to_surat')
//                ->whereNull('hefz_to_ayat')
                ->with(['student', 'ijazasanadMemorizationPlan'])
                ->get()
                ->groupBy('student_id')
                ->map(function ($records) {
                    $firstReport = $records->first();
                    $from_jazariyah = $records->min('jazariyah_from_lesson');
                    $to_jazariyah   = $records->max('jazariyah_to_lesson');
                    $plannedLessons = $firstReport->ijazasanadMemorizationPlan
                        ? ($firstReport->ijazasanadMemorizationPlan->jazariyah_to_lesson - $firstReport->ijazasanadMemorizationPlan->jazariyah_from_lesson) + 1
                        : 0;
                    $completedLessons = ($to_jazariyah - $from_jazariyah) + 1;
                    return [
                        'student'           => $firstReport->student,
                        'planned_lessons'   => $plannedLessons,
                        'completed_lessons' => $completedLessons,
                        'from_jazariyah'    => $from_jazariyah,
                        'to_jazariyah'      => $to_jazariyah,
                    ];
                });
        });
    }

    private function getCachedSeminarReport($classId, $year, $month)
    {
        $cacheKey = "seminar_report_{$classId}_{$year}_{$month}";
        return \Cache::remember($cacheKey, 3600, function() use ($classId, $year, $month) {
            return \App\StudentIjazasanadMemorizationReport::where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereNull('hefz_from_surat')
                ->whereNull('hefz_from_ayat')
                ->whereNull('hefz_to_surat')
                ->whereNull('hefz_to_ayat')
                ->with(['student', 'ijazasanadMemorizationPlan'])
                ->get()
                ->groupBy('student_id')
                ->map(function ($records) {
                    $firstReport = $records->first();
                    $from_seminar = $records->min('seminars_from_lesson');
                    $to_seminar   = $records->max('seminars_to_lesson');
                    $plannedLessons = $firstReport->ijazasanadMemorizationPlan
                        ? ($firstReport->ijazasanadMemorizationPlan->seminars_to_lesson - $firstReport->ijazasanadMemorizationPlan->seminars_from_lesson) + 1
                        : 0;
                    $completedLessons = ($to_seminar - $from_seminar) + 1;
                    return [
                        'student'           => $firstReport->student,
                        'planned_lessons'   => $plannedLessons,
                        'completed_lessons' => $completedLessons,
                        'from_seminar'      => $from_seminar,
                        'to_seminar'        => $to_seminar,
                    ];
                });
        });
    }

    private function getCachedSummaryData($classId, $year, $month)
    {
        $cacheKey = "summary_data_{$classId}_{$year}_{$month}";
        return \Cache::remember($cacheKey, 3600, function() use ($classId, $year, $month) {
            // Fetch the class and its students
            $classes = \App\Classes::where('id', $classId)->with('students')->first();
            $totalStudents = $classes->students->count();

            // Fetch memorization plans and reports for the class in this month/year
            $memorizationPlans = \App\IjazasanadMemorizationPlan::where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            $plansByStudent = $memorizationPlans->groupBy('student_id');
            $memorizationReports = StudentIjazasanadMemorizationReport::where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();
            $totalTalqeenCompleted = 0;
            $totalRevisionCompleted = 0;
            $totalJazariyahCompleted = 0;
            $totalSeminarsCompleted = 0;

            // Filter out valid reports and group them by student_id
            $reportsByStudent = $memorizationReports->filter(function($report) {
                return !is_null($report->talqeen_from_lesson)
                    && !is_null($report->talqeen_to_lesson)
                    && ($report->talqeen_from_lesson <= $report->talqeen_to_lesson);
            })->groupBy('student_id');

// For each student, merge overlapping ranges by taking the minimum and maximum of their lesson ranges
            foreach ($reportsByStudent as $studentReports) {
                $minLesson = $studentReports->min('talqeen_from_lesson');
                $maxLesson = $studentReports->max('talqeen_to_lesson');
                $totalTalqeenCompleted += ($maxLesson - $minLesson + 1);



            }

            $reportsByStudent = $memorizationReports->filter(function($report) {
                return !is_null($report->revision_from_lesson)
                    && !is_null($report->revision_to_lesson)
                    && ($report->revision_from_lesson <= $report->revision_to_lesson);
            })->groupBy('student_id');

// For each student, merge overlapping ranges by taking the minimum and maximum of their lesson ranges
            foreach ($reportsByStudent as $studentReports) {


                $minRevisionLesson = $studentReports->min('revision_from_lesson');
                $maxRevisionLesson = $studentReports->max('revision_to_lesson');
                $totalRevisionCompleted += ($maxRevisionLesson - $minRevisionLesson + 1);

            }


            $reportsByStudent = $memorizationReports->filter(function($report) {
                return !is_null($report->jazariyah_from_lesson)
                    && !is_null($report->jazariyah_to_lesson)
                    && ($report->jazariyah_from_lesson <= $report->jazariyah_to_lesson);
            })->groupBy('student_id');

// For each student, merge overlapping ranges by taking the minimum and maximum of their lesson ranges
            foreach ($reportsByStudent as $studentReports) {


                $minJazariyahLesson = $studentReports->min('jazariyah_from_lesson');
                $maxJazariyahLesson = $studentReports->max('jazariyah_to_lesson');
                $totalJazariyahCompleted += ($maxJazariyahLesson - $minJazariyahLesson + 1);

            }

            $reportsByStudent = $memorizationReports->filter(function($report) {
                return !is_null($report->seminars_from_lesson)
                    && !is_null($report->seminars_to_lesson)
                    && ($report->seminars_from_lesson <= $report->seminars_to_lesson);
            })->groupBy('student_id');

// For each student, merge overlapping ranges by taking the minimum and maximum of their lesson ranges
            foreach ($reportsByStudent as $studentReports) {


                $minSeminarsLesson = $studentReports->min('seminars_from_lesson');
                $maxSeminarsLesson = $studentReports->max('seminars_to_lesson');
                $totalSeminarsCompleted += ($maxSeminarsLesson - $minSeminarsLesson + 1);

            }


            // Initialize totals for each category
            $totalTalqeenPlanned = 0;
            $totalRevisionPlanned = 0;
            $totalJazariyahPlanned = 0;
            $totalSeminarPlanned = 0;
            $plansByStudent->each(function($studentPlans) use (&$totalTalqeenPlanned,
                &$totalRevisionPlanned,
                &$totalJazariyahPlanned,
                &$totalSeminarPlanned) {
                // For Talqeen: merge the intervals per student
                $totalTalqeenPlanned += $this->mergeLessonRanges($studentPlans, 'talqeen_from_lesson', 'talqeen_to_lesson');

                // For Revision
                $totalRevisionPlanned += $this->mergeLessonRanges($studentPlans, 'revision_from_lesson', 'revision_to_lesson');

                // For Jazariyah
                $totalJazariyahPlanned += $this->mergeLessonRanges($studentPlans, 'jazariyah_from_lesson', 'jazariyah_to_lesson');

                // For Seminars
                $totalSeminarPlanned += $this->mergeLessonRanges($studentPlans, 'seminars_from_lesson', 'seminars_to_lesson');
            });


            // Calculate attendance percentage using your existing helper function.
            $totalClasses = calculateTotalClassesInMonth($classId, $year, $month);
            $attendancePercentage = calculateIjazasanadLevel1AttendancePercentage($classId, $year, $month, $totalClasses);


            // Overall achievement percentage
            $totalPlanned = $totalTalqeenPlanned + $totalRevisionPlanned + $totalJazariyahPlanned + $totalSeminarPlanned;
            $totalCompleted = $totalTalqeenCompleted + $totalRevisionCompleted + $totalJazariyahCompleted + $totalSeminarsCompleted;
            $achievementPercentage = $totalPlanned > 0 ? round(($totalCompleted / $totalPlanned) * 100, 2) : 0;




            return [
                'noOfStudents' => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalStudents . '</h2>',
                'totalTalqeenPlanned' => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalTalqeenCompleted . '</h2>',
                'totalRevisionPlanned' => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalRevisionCompleted . '</h2>',
                'totalJazariyahPlanned' => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalJazariyahCompleted . '</h2>',
                'totalSeminarsPlanned' => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalSeminarsCompleted . '</h2>',
                'attendanceDaysPercentage' => '
                    <div class="progress-container">
                        <span class="progress-bar-value" style="width:' . $attendancePercentage . '%;"></span>
                        <span class="progress-text">' . $attendancePercentage . '%</span>
                    </div>',
                'achievementPercentage'    => '
                    <div class="progress-container">
                        <span class="progress-bar-value" style="width:' . $achievementPercentage . '%;"></span>
                        <span class="progress-text">' . $achievementPercentage . '%</span>
                    </div>',
            ];
        });
    }




    /**
     * Returns the memorization details for students with active memorization plans,
     * including associated reports and revision counts.
     */
    private function getCachedMemorizationDetails($classId, $month, $year)
    {
        $cacheKey = "memorization_details_{$classId}_{$year}_{$month}";
        return \Cache::remember($cacheKey, 3600, function() use ($classId, $month, $year) {
            return \App\Student::whereHas('joint_classes', function ($query) use ($classId) {
                $query->where('class_id', $classId);
            })
                ->whereHas('ijazasanad_memorization_plans', function ($query) use ($month, $year, $classId) {
                    $query->whereMonth('start_date', $month)
                        ->whereYear('start_date', $year)
                        ->where('status', 'active');
                })
                ->with([
                    'ijazaMemorizationReport' => function ($query) use ($month, $year, $classId) {
                        $query->where(function ($q) use ($year, $month, $classId) {
                            $q->whereYear('created_at', $year)
                                ->whereMonth('created_at', $month)
                                ->where('class_id', $classId)
                                ->whereNotNull('hefz_from_surat')
                                ->whereNotNull('hefz_from_ayat')
                                ->whereNotNull('hefz_to_surat')
                                ->whereNotNull('hefz_to_ayat');
                        });
                    },
                    'ijazasanad_memorization_plans' => function ($query) use ($month, $year, $classId) {
                        $query->whereMonth('created_at', $month)
                            ->whereYear('created_at', $year)
                            ->where('status', 'active')
                            ->where('class_id', $classId);
                    },
                    'ijazaRevisionReport' => function ($query) use ($year, $month, $classId) {
                        $query->where(function ($q) use ($year, $month, $classId) {
                            $q->whereYear('created_at', $year)
                                ->whereMonth('created_at', $month)
                                ->where('class_id', $classId);
                        });
                    },
                    'ijazasanad_revision_plans' => function ($query) use ($month, $year, $classId) {
                        $query->whereMonth('created_at', $month)
                            ->whereYear('created_at', $year)
                            ->where('class_id', $classId);
                    }
                ])
                ->withCount([
                    'ijazasanad_revision_plans' => function ($query) use ($month, $year, $classId) {
                        $query->whereMonth('created_at', $month)
                            ->whereYear('created_at', $year)
                            ->where('class_id', $classId);
                    },
                    'ijazaMemorizationReport as present_counts' => function ($query) use ($month, $year, $classId) {
                        $query->whereMonth('created_at', $month)
                            ->whereYear('created_at', $year)
                            ->where('attendance_id', 2) // 2 = present
                            ->where('class_id', (int)$classId);
                    },
                    'ijazaMemorizationReport as total_sessions' => function ($query) use ($month, $year, $classId) {
                        $start_date = \Illuminate\Support\Carbon::createFromDate($year, $month, 1);
                        $end_date = \Illuminate\Support\Carbon::createFromDate($year, $month, 1)->endOfMonth();
                        $query->whereBetween('created_at', [$start_date, $end_date])
                            ->where('class_id', $classId);
                    },
                    'ijazaRevisionReport as revision_count' => function ($query) use ($month, $year, $classId) {
                        $start_date = \Illuminate\Support\Carbon::createFromDate($year, $month, 1);
                        $end_date = \Illuminate\Support\Carbon::createFromDate($year, $month, 1)->endOfMonth();
                        $query->whereBetween('created_at', [$start_date, $end_date])
                            ->where('class_id', $classId);
                    }
                ])
                ->orderBy('full_name', 'asc')
                ->get();
        });
    }


    /**
     * Returns aggregated summary data for the class report.
     */
    private function getCachedClassSummaryReportDetails($classId, $month, $year)
    {
        $cacheKey = "class_summary_report_{$classId}_{$year}_{$month}";
        return \Cache::remember($cacheKey, 3600, function() use ($classId, $month, $year) {
            // Fetch total students in the class
            $classes = \App\Classes::where('id', $classId)->with('students')->first();
            $totalStudents = $classes->students->count();

            // Memorization plans and reports for the month
            $memorizationPlans = \App\IjazasanadMemorizationPlan::where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            $memorizationReports = \App\StudentIjazasanadMemorizationReport::where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            // --- TALQEEN calculations ---
            $totalTalqeenPlanned = $memorizationPlans->sum(function ($plan) {
                return ($plan->talqeen_to_lesson - $plan->talqeen_from_lesson) + 1;
            });
            $talqeenCompletedLessons = [];
            foreach ($memorizationReports as $report) {
                if (!is_null($report->talqeen_from_lesson) && !is_null($report->talqeen_to_lesson) && $report->talqeen_from_lesson <= $report->talqeen_to_lesson) {
                    $talqeenCompletedLessons = array_merge(
                        $talqeenCompletedLessons,
                        range($report->talqeen_from_lesson, $report->talqeen_to_lesson)
                    );
                }
            }
            $totalTalqeenCompleted = collect($talqeenCompletedLessons)->unique()->count();

            // --- REVISION calculations ---
            $totalRevisionPlanned = $memorizationPlans->sum(function ($plan) {
                return ($plan->revision_to_lesson - $plan->revision_from_lesson) + 1;
            });
            $revisionCompletedLessons = [];
            foreach ($memorizationReports as $report) {
                if (!is_null($report->revision_from_lesson) && !is_null($report->revision_to_lesson) && $report->revision_from_lesson <= $report->revision_to_lesson) {
                    $revisionCompletedLessons = array_merge(
                        $revisionCompletedLessons,
                        range($report->revision_from_lesson, $report->revision_to_lesson)
                    );
                }
            }
            $totalRevisionCompleted = collect($revisionCompletedLessons)->unique()->count();

            // --- SEMINAR calculations ---
            $totalSeminarPlanned = $memorizationPlans->sum(function ($plan) {
                return ($plan->seminars_to_lesson - $plan->seminars_from_lesson) + 1;
            });
            $seminarsCompletedLessons = [];
            foreach ($memorizationReports as $report) {
                if (!is_null($report->seminars_from_lesson) && !is_null($report->seminars_to_lesson) && $report->seminars_from_lesson <= $report->seminars_to_lesson) {
                    $seminarsCompletedLessons = array_merge(
                        $seminarsCompletedLessons,
                        range($report->seminars_from_lesson, $report->seminars_to_lesson)
                    );
                }
            }
            $totalSeminarCompleted = collect($seminarsCompletedLessons)->unique()->count();

            // --- JAZARIYAH calculations ---
            $totalJazariyahPlanned = $memorizationPlans->sum(function ($plan) {
                return ($plan->jazariyah_to_lesson - $plan->jazariyah_from_lesson) + 1;
            });
            $jazariyahCompletedLessons = [];
            foreach ($memorizationReports as $report) {
                if (!is_null($report->jazariyah_from_lesson) && !is_null($report->jazariyah_to_lesson) && $report->jazariyah_from_lesson <= $report->jazariyah_to_lesson) {
                    $jazariyahCompletedLessons = array_merge(
                        $jazariyahCompletedLessons,
                        range($report->jazariyah_from_lesson, $report->jazariyah_to_lesson)
                    );
                }
            }
            $totalJazariyahCompleted = collect($jazariyahCompletedLessons)->unique()->count();

            // --- Attendance calculation ---
            // Assume calculateTotalClassesInMonth() and calculateIjazasanadLevel1AttendancePercentage() exist in your codebase.
            $totalClasses = calculateTotalClassesInMonth($classId, $year, $month);
            $attendancePercentage = calculateIjazasanadLevel1AttendancePercentage($classId, $year, $month, $totalClasses);

            // --- Overall achievement ---
            $totalPlanned = $totalTalqeenPlanned + $totalRevisionPlanned + $totalJazariyahPlanned + $totalSeminarPlanned;
            $totalCompleted = $totalTalqeenCompleted + $totalRevisionCompleted + $totalJazariyahCompleted + $totalSeminarCompleted;
            $achievementPercentage = $totalPlanned > 0 ? round(($totalCompleted / $totalPlanned) * 100, 2) : 0;

            return [
                'noOfStudents'             => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalStudents . '</h2>',
                'totalTalqeenPlanned'      => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalTalqeenPlanned . '</h2>',
                'totalRevisionPlanned'     => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalRevisionPlanned . '</h2>',
                'totalJazariyahPlanned'    => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalJazariyahPlanned . '</h2>',
                'totalSeminarsPlanned'     => '<h2 style="color: #1fff0f; font-weight: bolder; font-size: 24px;">' . $totalSeminarPlanned . '</h2>',
                'attendanceDaysPercentage' => '
                <div class="progress-container">
                    <span class="progress-bar-value" style="width:' . $attendancePercentage . '%;"></span>
                    <span class="progress-text">' . $attendancePercentage . '%</span>
                </div>',
                'achievementPercentage'    => '
                <div class="progress-container">
                    <span class="progress-bar-value" style="width:' . $achievementPercentage . '%;"></span>
                    <span class="progress-text">' . $achievementPercentage . '%</span>
                </div>',
            ];
        });
    }


    function mergeLessonRanges($plans, $fromKey, $toKey)
    {
        // Collect valid ranges
        $intervals = [];
        foreach ($plans as $plan) {
            $start = $plan->$fromKey;
            $end   = $plan->$toKey;
            if (!is_null($start) && !is_null($end) && $start <= $end) {
                $intervals[] = [$start, $end];
            }
        }

        // Sort intervals by starting lesson
        usort($intervals, function ($a, $b) {
            return $a[0] <=> $b[0];
        });

        // Merge intervals only if they actually overlap
        $merged = [];
        foreach ($intervals as $interval) {
            if (empty($merged) || $merged[count($merged) - 1][1] < $interval[0]) {
                // No overlap; push as a separate interval
                $merged[] = $interval;
            } else {
                // Overlapping intervals; merge by extending the end if necessary
                $merged[count($merged) - 1][1] = max($merged[count($merged) - 1][1], $interval[1]);
            }
        }

        // Sum the lengths of all merged intervals
        $total = 0;
        foreach ($merged as $m) {
            $total += ($m[1] - $m[0] + 1);
        }

        return $total;
    }












}
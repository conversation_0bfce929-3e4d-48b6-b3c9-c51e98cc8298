<?php

namespace Modules\General\Http\Controllers;


use App\Http\Controllers\Controller;

use Illuminate\Support\Facades\File;






class SystemLogController extends Controller
{
    private $logFile;

    public function __construct()
    {
        $this->logFile = storage_path('logs/laravel-' . date('Y-m-d') . '.log');
    }

    public function index()
    {

        $logs = File::exists($this->logFile) ? File::get($this->logFile) : 'No logs available for today.';
        return view('general::status.systemlog.index', compact('logs'));
    }

    public function clear()
    {
        if (File::exists($this->logFile)) {
            File::put($this->logFile, '');
        }
        return redirect()->route('system-log.index');
    }

    public function refresh()
    {
        $logs = File::exists($this->logFile) ? File::get($this->logFile) : 'No logs available for today.';
        return response()->json(['logs' => $logs]);
    }



}

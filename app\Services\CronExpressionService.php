<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\Log;

/**
 * CronExpressionService provides human-friendly cron expression management
 *
 * Converts between human-readable schedule descriptions and cron expressions
 * for intuitive scheduling configuration in admin interfaces.
 * 
 * Purpose: Enable founders and non-technical users to configure schedules easily
 * Side effects: None - pure conversion service with logging for debugging
 * Errors: Falls back to safe defaults on conversion errors
 */
final class CronExpressionService
{
    /**
     * Predefined human-friendly schedule options with cron expressions
     * 
     * @var array
     */
    private const SCHEDULE_OPTIONS = [
        'hourly' => [
            'label' => 'Every hour',
            'description' => 'Runs at the top of every hour',
            'cron' => '0 * * * *',
        ],
        'every_2_hours' => [
            'label' => 'Every 2 hours',
            'description' => 'Runs every 2 hours starting at midnight',
            'cron' => '0 */2 * * *',
        ],
        'every_6_hours' => [
            'label' => 'Every 6 hours',
            'description' => 'Runs every 6 hours (midnight, 6am, noon, 6pm)',
            'cron' => '0 */6 * * *',
        ],
        'daily_midnight' => [
            'label' => 'Daily at midnight',
            'description' => 'Runs once per day at midnight',
            'cron' => '0 0 * * *',
        ],
        'daily_6am' => [
            'label' => 'Daily at 6:00 AM',
            'description' => 'Runs once per day at 6:00 AM',
            'cron' => '0 6 * * *',
        ],
        'daily_noon' => [
            'label' => 'Daily at noon',
            'description' => 'Runs once per day at 12:00 PM',
            'cron' => '0 12 * * *',
        ],
        'every_2_days' => [
            'label' => 'Every 2 days at midnight',
            'description' => 'Runs every 2 days at midnight (default recommended)',
            'cron' => '0 0 */2 * *',
        ],
        'every_3_days' => [
            'label' => 'Every 3 days at midnight',
            'description' => 'Runs every 3 days at midnight',
            'cron' => '0 0 */3 * *',
        ],
        'weekly_sunday' => [
            'label' => 'Weekly on Sunday at midnight',
            'description' => 'Runs once per week on Sunday at midnight',
            'cron' => '0 0 * * 0',
        ],
        'weekly_monday' => [
            'label' => 'Weekly on Monday at midnight',
            'description' => 'Runs once per week on Monday at midnight',
            'cron' => '0 0 * * 1',
        ],
        'monthly' => [
            'label' => 'Monthly on the 1st at midnight',
            'description' => 'Runs once per month on the 1st at midnight',
            'cron' => '0 0 1 * *',
        ],
    ];

    /**
     * Get all available human-friendly schedule options
     *
     * @return array Array of schedule options with labels, descriptions, and cron expressions
     */
    public static function getScheduleOptions(): array
    {
        return self::SCHEDULE_OPTIONS;
    }

    /**
     * Convert schedule key to cron expression
     *
     * @param string $scheduleKey Key from SCHEDULE_OPTIONS
     * @return string Cron expression or default if key not found
     */
    public static function getCronExpression(string $scheduleKey): string
    {
        if (isset(self::SCHEDULE_OPTIONS[$scheduleKey])) {
            return self::SCHEDULE_OPTIONS[$scheduleKey]['cron'];
        }

        Log::warning('CronExpressionService: Unknown schedule key, using default', [
            'requested_key' => $scheduleKey,
            'default_cron' => '0 0 */2 * *',
        ]);

        return '0 0 */2 * *'; // Default: every 2 days at midnight
    }

    /**
     * Find schedule key from cron expression
     *
     * @param string $cronExpression Cron expression to look up
     * @return string|null Schedule key if found, null otherwise
     */
    public static function getScheduleKey(string $cronExpression): ?string
    {
        $cronExpression = trim($cronExpression);
        
        foreach (self::SCHEDULE_OPTIONS as $key => $option) {
            if ($option['cron'] === $cronExpression) {
                return $key;
            }
        }

        Log::debug('CronExpressionService: Cron expression not found in predefined options', [
            'cron_expression' => $cronExpression,
        ]);

        return null;
    }

    /**
     * Get human-readable description for cron expression
     *
     * @param string $cronExpression Cron expression to describe
     * @return string Human-readable description
     */
    public static function getHumanDescription(string $cronExpression): string
    {
        $scheduleKey = self::getScheduleKey($cronExpression);
        
        if ($scheduleKey && isset(self::SCHEDULE_OPTIONS[$scheduleKey])) {
            return self::SCHEDULE_OPTIONS[$scheduleKey]['description'];
        }

        // Try to provide basic interpretation for custom cron expressions
        return self::interpretCronExpression($cronExpression);
    }

    /**
     * Basic interpretation of custom cron expressions
     *
     * @param string $cronExpression Cron expression to interpret
     * @return string Basic interpretation or fallback message
     */
    private static function interpretCronExpression(string $cronExpression): string
    {
        $parts = explode(' ', trim($cronExpression));
        
        if (count($parts) !== 5) {
            return "Custom schedule: {$cronExpression}";
        }

        [$minute, $hour, $day, $month, $weekday] = $parts;

        try {
            $description = [];

            // Interpret frequency
            if ($day === '*/2') {
                $description[] = 'Every 2 days';
            } elseif ($day === '*/3') {
                $description[] = 'Every 3 days';
            } elseif ($day === '*' && $weekday !== '*') {
                $description[] = 'Weekly';
            } elseif ($day === '*') {
                $description[] = 'Daily';
            } elseif ($day === '1') {
                $description[] = 'Monthly';
            }

            // Interpret time
            if ($hour === '0' && $minute === '0') {
                $description[] = 'at midnight';
            } elseif ($hour === '12' && $minute === '0') {
                $description[] = 'at noon';
            } elseif ($hour !== '*') {
                $time = sprintf('%02d:%02d', (int)$hour, (int)$minute);
                $description[] = "at {$time}";
            }

            return implode(' ', $description) ?: "Custom schedule: {$cronExpression}";

        } catch (\Exception $e) {
            Log::warning('CronExpressionService: Failed to interpret cron expression', [
                'cron_expression' => $cronExpression,
                'error' => $e->getMessage(),
            ]);

            return "Custom schedule: {$cronExpression}";
        }
    }

    /**
     * Validate cron expression format
     *
     * @param string $cronExpression Cron expression to validate
     * @return bool True if valid format, false otherwise
     */
    public static function isValidCronExpression(string $cronExpression): bool
    {
        $pattern = '/^[0-9*\/,-]+\s+[0-9*\/,-]+\s+[0-9*\/,-]+\s+[0-9*\/,-]+\s+[0-9*\/,-]+$/';
        return preg_match($pattern, trim($cronExpression)) === 1;
    }

    /**
     * Get recommended schedule for log monitoring
     *
     * @return array Schedule recommendation with key and reasoning
     */
    public static function getRecommendedSchedule(): array
    {
        return [
            'key' => 'every_2_days',
            'cron' => '0 0 */2 * *',
            'label' => 'Every 2 days at midnight',
            'reasoning' => 'Balances timely issue detection with minimal system overhead and email noise.',
        ];
    }
}

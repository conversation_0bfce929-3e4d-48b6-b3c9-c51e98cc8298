<?php

namespace Modules\General\Http\Controllers;

use App\Admission;
use App\AdmissionInterview;
use App\CenterEmployee;
use App\Employee;
use App\MissedClockOut;
use App\Scopes\OrganizationScope;
use App\StudentHefzPlan;
use Carbon\Carbon;
use Doctrine\DBAL\Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use App\Student;
use Module;
use Auth;
use App\Center;

use App\Classes;

class SupervisorDashboardWidgetCounterController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function __invoke(Request $request)
    {



            $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                ->whereRaw('admissions.status = "new_admission"')
                ->whereIn('center_id', CenterEmployee::where('emp_id', Auth::user()->id)->pluck('cen_id')->toArray())
                ->has('programs')
                ->with('programs')
                ->has('center')
                ->with('center')
                ->has('student')
                ->with('student')
                ->withoutGlobalScope(OrganizationScope::class)
                ->count();


        $interviews_not_confirmed_yet = Admission::whereHas('interviews',function($query){
            $query->whereNull('invitation_confirmed');
        })  ->has('center')
            ->with('center')
            ->has('student')
            ->with('student')
            ->with('interviews')->count();

        $students_with_no_study_plan = AdmissionInterview::whereNull('confirmed_at')
            ->count();


        $class = Classes::count();
        $center = Center::count();
        $studentno = Student::count();
        $employees = Employee::with('roles')->get();
        $teacherno = $employees->filter(function ($employees, $key) {
            return $employees->hasRole('teacher_2_');
        })->count();
            $supervisorCenterId = \App\CenterEmployee::where('emp_id', auth()->user()->id)->first()->cen_id;


            $hefzPlansWaiting_approval = \App\StudentHefzPlan::has('student')
                ->whereHas('center', function ($tq) use ($supervisorCenterId) {
                $tq->where('id', $supervisorCenterId);
            })->where('status', 'waiting_for_approval')
                ->where('center_id', $supervisorCenterId)
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();
            $RevisionPlanWaiting_approval = \App\StudentRevisionPlan::has('student')
                ->whereHas('center', function ($tq) use ($supervisorCenterId) {
                    $tq->where('id', $supervisorCenterId);
                })
                ->where('status', 'waiting_for_approval')
                ->whereNotNull('start_from_surat')
                ->whereNotNull('start_from_ayat')
                ->whereNotNull('to_surat')
                ->whereNotNull('to_ayat')
                ->count();

//            dd(auth()->user()->center->pluck('id'));
        $ijazasanadMemorizationPlanWaitingApproval = \App\IjazasanadMemorizationPlan::has('student')->has('center')->where('status', 'waiting_for_approval')
//            ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())
            ->where(function ($query) {
                // Group the first set of conditions
                $query->where(function ($subQuery) {
                    $subQuery->whereNotNull('start_from_surat')
                        ->whereNotNull('start_from_ayat')
                        ->whereNotNull('to_surat')
                        ->whereNotNull('to_ayat');
                })
                    // Combine with the second set using OR
                    ->orWhere(function ($subQuery) {
                        $subQuery->whereNotNull('talqeen_from_lesson')
                            ->orWhereNotNull('talqeen_to_lesson')
                            ->orWhereNotNull('revision_from_lesson')
                            ->orWhereNotNull('revision_to_lesson')
                            ->orWhereNotNull('jazariyah_from_lesson')
                            ->orWhereNotNull('jazariyah_to_lesson')
                            ->orWhereNotNull('seminars_from_lesson')
                            ->orWhereNotNull('seminars_to_lesson');
                    });
            })->count();

            $plans_waiting_approval = $hefzPlansWaiting_approval + $RevisionPlanWaiting_approval+$ijazasanadMemorizationPlanWaitingApproval;
        return response()->json([
            'studentswithNoHefzPlanCountWidget' => $students_with_no_study_plan,
            'plansWaitingApprovalCountWidget' => $plans_waiting_approval,
            'class' => $class,
            'center' => $center,
            'studentno' => $studentno,
            'teacherno' => $teacherno,
        ]);

    }
}

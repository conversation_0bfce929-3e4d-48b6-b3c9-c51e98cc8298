@startuml Component Diagram (Level 3 - Web Application)

!theme vibrant

title Component Diagram for Itqan Web Application Container

' Include C4 specific macros/styling
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

' Actors & External Systems (interacting with this container)
Person(admin, "Administrator")
Person(staff, "Staff/Teacher")
Person(student, "Student/Parent")

System_Ext(email, "Email Service")
System_Ext(payment, "Payment Gateway")
System_Ext(sms, "SMS Gateway")

' Other Containers (interacting with this container)
ContainerDb(db, "Database", "MySQL", "Stores application data")
Container(cache, "Cache", "Redis/Memcached", "Stores sessions, cached data")
Container(queue_worker, "Queue Worker", "PHP, Laravel Queue", "Processes background jobs")
Container(storage, "File Storage", "Stores uploaded files")

' Boundary for the Web Application Container
Container_Boundary(webapp, "Web Application (Laravel 10)") {

    Component(router, "Router", "Laravel Routing", "Routes incoming HTTP requests to the correct controllers.")
    Component(middleware, "Middleware Pipeline", "Laravel Middleware", "Handles cross-cutting concerns like auth, CSRF, logging.")
    Component(controllers, "MVC Controllers", "PHP", "Handles user requests, interacts with services/models, returns responses.")
    Component(views, "Blade Views", "Laravel Blade", "Renders the HTML user interface.")
    Component(services, "Service Layer", "PHP", "Contains core business logic, interacts with models and other services.")
    Component(models, "Eloquent Models", "Laravel Eloquent", "Represents database tables, handles data persistence logic.")
    Component(events, "Event System", "Laravel Events", "Handles event dispatching and listening for decoupled communication.")

    ' Representing the modular structure
    Component(modules, "Application Modules", "nwidart/laravel-modules", "Encapsulated feature areas (Admission, Finance, HR, Education, etc.) containing their own Controllers, Services, Models, Views, Routes.")

    ' Relationships between components within the container
    Rel(router, controllers, "Routes requests to")
    Rel(router, modules, "Routes requests to (Module Controllers)")
    Rel(middleware, router, "Wraps request/response around")

    Rel(controllers, services, "Uses")
    Rel(controllers, models, "Uses")
    Rel(controllers, views, "Renders")
    Rel(controllers, events, "Dispatches")
    Rel(controllers, modules, "Uses (Module Services/Models)")

    Rel(services, models, "Uses")
    Rel(services, events, "Dispatches/Listens")
    Rel(services, modules, "Uses/Provides (Module Services/Models)")

    Rel(models, db, "Reads/Writes data using", "SQL") ' Interacts with external DB container
    Rel(models, cache, "Reads/Writes cache using", "TCP") ' Interacts with external Cache container

    Rel(events, queue_worker, "Dispatches jobs via", "Redis/DB") ' Indirectly triggers queue worker
    Rel(events, modules, "Listened to by / Dispatched by")

    ' Modules contain their own MVC components
    Rel(modules, services, "Uses (Core Services)")
    Rel(modules, models, "Uses (Core Models)")
    Rel(modules, views, "Renders (Shared/Module Views)")
    Rel(modules, events, "Dispatches/Listens")
    Rel(modules, db, "Reads/Writes data using", "SQL") ' Module models interact w/ DB
    Rel(modules, cache, "Reads/Writes cache using", "TCP") ' Module logic interacts w/ Cache
    Rel(modules, storage, "Reads/Writes files using", "File I/O / API") ' Module logic interacts w/ Storage

}

' Relationships from Actors to Components
Rel(admin, middleware, "Sends HTTP requests via")
Rel(staff, middleware, "Sends HTTP requests via")
Rel(student, middleware, "Sends HTTP requests via")

' Relationships from Components to External Systems
Rel(services, email, "Sends emails using", "API/SMTP")
Rel(services, sms, "Sends SMS using", "API")
Rel(services, payment, "Initiates payments using", "HTTPS/API")
Rel(controllers, payment, "Receives payment status via", "Webhook/HTTPS") ' Webhooks likely hit controllers

Rel(modules, email, "Sends emails using", "API/SMTP")
Rel(modules, sms, "Sends SMS using", "API")
Rel(modules, payment, "Interacts with", "HTTPS/API")

@enduml 
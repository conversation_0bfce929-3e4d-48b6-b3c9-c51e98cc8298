@extends('layouts.hound')

@section('mytitle')
<div class="row">
    <div class="col-lg-9">
 Dashboard
    </div>
    <div class="col-lg-3">
  @if(count($dashboards) > 1)
  <select name="dahsboard" id="dashboard_switch" class="form-control">
    <option >Dashboards</option>
   @foreach($dashboards as $dashboard)]
    <option value="{{ $dashboard }}">{{ ucwords(str_replace('_' , ' ' , $dashboard)) }}</option>
   @endforeach
  </select>
  @endif
</div>


</div>
<br>
@endsection
@section('content')
@include('partial.count')

@include('partial.quick_access')

<div class="row">
  <div class="col-lg-12">
    <div class="panel panel-default card-view">
      <div class="panel-heading">
        <div class="pull-left">
          <h6 class="panel-title txt-dark">Quick Actions</h6>
        </div>
        <div class="clearfix"></div>
      </div>
      <div class="panel-wrapper collapse in">
        <div class="panel-body">
  <!-- Row -->
  <div class="clearfix">

    @if($plans_waiting_approval)
      @include('general::actions.plans_waiting_approval')
    @endif

    @if($application_need_action)
      @include('general::actions.students_waiting_approval')
    @endif

  </div>
  <!-- /Row -->
        </div>
      </div>
    </div>
  </div>
</div>


@foreach ($modules as $key => $module) 
    @if(count($module))

<div class="row">
    <div class="col-lg-12">
      <div class="panel panel-default card-view">
        <div class="panel-heading">
          <div class="pull-left">
            <h6 class="panel-title txt-dark">{{$key}}</h6>
          </div>
        
        </div>
       
         
    <!-- Row -->
    <div class="clearfix">
        <div class="card-body table-responsive">
        @foreach($module as $unit)
        <div class="col-xl-3 col-md-3 mb-4">
            <div class="card shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold  text-uppercase mb-1">
                        <a href="{{$unit['url']}}" style="color:#00acc1">
                            <span class="btn-label"><i class="fa fa-{{$unit['icon']}}" style="color:#00acc1;"></i> </span>
                            <span class="btn-text">{{$unit['name']}}</span>
                          </a>

                    </div>
                   
                  </div>
                 
                </div>
              </div>
            </div>
          </div>
      
       
        @endforeach
  
    </div>
    <!-- /Row -->
    </div>
        
      </div>
    </div>
  </div>
  @endif
  @endforeach
{{--<div class="col-md-4">
            <!-- Calendar -->
          <div class="box box-solid bg-green-gradient">
            <div class="box-header">
              <i class="fa fa-calendar"></i>

              <h3 class="box-title">Calendar</h3>
              <!-- tools box -->
              <div class="pull-right box-tools">
                <!-- button with a dropdown -->
                <div class="btn-group">
                  <button type="button" class="btn btn-success btn-sm dropdown-toggle" data-toggle="dropdown">
                    <i class="fa fa-bars"></i></button>
                  <ul class="dropdown-menu pull-right" role="menu">
                    <li><a href="#">Add new event</a></li>
                    <li><a href="#">Clear events</a></li>
                    <li class="divider"></li>
                    <li><a href="#">View calendar</a></li>
                  </ul>
                </div>
                <button type="button" class="btn btn-success btn-sm" data-widget="collapse"><i class="fa fa-minus"></i>
                </button>
                <button type="button" class="btn btn-success btn-sm" data-widget="remove"><i class="fa fa-times"></i>
                </button>
              </div>
              <!-- /. tools -->
            </div>
            <!-- /.box-header -->
            <div class="box-body no-padding">
              <!--The calendar -->
              <div id="calendar" style="width: 100%"></div>
            </div>
            <!-- /.box-body -->
            <div class="box-footer text-black">
              <div class="row">
                <div class="col-sm-12">
                  <h4>Coming Events</h4>
                  <!-- Progress bars -->
                  <div class="clearfix">
                    <span class="pull-left">Event #1</span>
                    <small class="pull-right">19/08/2017</small>
                  </div>
                  <div class="progress xs">
                    <div class="progress-bar progress-bar-green" style="width: 90%;"></div>
                  </div>
                </div>
                <!-- /.col -->
              </div>
              <!-- /.row -->
            </div>
          </div>
          <!-- /.box -->

</div> --}}
</div>

	
@stop
@section('js')
<script>
  jQuery(document).ready(function($) {
    $('body').addClass('sidebar-collapse');
    
    $('#calendar').datepicker();
  });
</script>
@stop
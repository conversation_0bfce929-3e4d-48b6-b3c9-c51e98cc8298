<?php

namespace Modules\Admission\Http\Controllers;


use App\AdmissionInterviewer;
use App\Classes;
use App\EmailSetting;
use App\Employee;
use App\GeneralSettings;
use App\Http\Requests\StudentApproveRequest;
use App\Services\EmailService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Program;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Admission;
use App\AdmissionInterview;
use App\Student;
use Illuminate\Support\Facades\DB;

class AdmissionProgramCenterClassUpdateController extends Controller
{


    public function __invoke(Request $request)
    {

        try {

            // Validate the incoming request
            $validatedData = $request->validate([
                'admission_id' => 'required|exists:admissions,id',
                'program' => 'required|exists:programs,id',
                'center' => 'required|exists:centers,id',
                'class' => 'required|exists:classes,id',
            ]);

            // Begin a transaction
            DB::beginTransaction();

        // Assuming you have an Admission model and you're passing the admission's ID in the request
        $admission = Admission::findOrFail($request->admission_id);

        // Update fields, e.g., program, center, and class
        $admission->program_id = $request->program;
        $admission->center_id = $request->center;
        $admission->class_id = $request->class;

        // Save the changes
        $admission->save();
            // Find the class
            $class = Classes::findOrFail($request->class);


            // Check if the relationship already exists (including soft-deleted)
            $existingRecord = $class->programs()->withTrashed()->wherePivot('program_id', $request->program)->first();

            if ($existingRecord) {
                // Restore if it was soft-deleted
                $class->programs()->updateExistingPivot($request->program, [
                    'deleted_at' => null,
                    'updated_at' => now()
                ]);
            } else {
                // Attach the class to the program
                $class->programs()->attach($request->program, [
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
            $program = $class->programs()->find($request->program);
            $programTitle = $class->programs()->find($request->program)->title;
            $programId = $program->id;



            // Commit the transaction
            DB::commit();
            return response()->json([
                'message' => 'Admission and class program information updated successfully',
                'program_title' => $programTitle,
                'program_id' => $programId
            ], 200);
            return response()->json(['message' => 'Admission information updated successfully'], 200);
        } catch (\Exception $e) {
            // Rollback the transaction in case of an error
            DB::rollBack();

            // Log the error
            Log::error('Failed to update admission information', [
                'error' => $e->getMessage(),
                'admission_id' => $request->admission_id,
                'program' => $request->program,
                'center' => $request->center,
                'class' => $request->class,
            ]);

            return response()->json(['error' => $e->getMessage()], 500);
        }

    }


}

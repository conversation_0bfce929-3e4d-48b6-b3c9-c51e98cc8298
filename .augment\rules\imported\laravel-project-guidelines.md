---
type: "always_apply"
---

# Laravel Project Guidelines

This document outlines the key architectural principles and technologies used in this Laravel 10 project. For detailed backend standards, please refer to the `[laravel_backend_development.mdc](mdc:.cursor/rules/laravel_backend_development.mdc)` file.

## Core Technologies

*   **Backend**: [Laravel 10](mdc:https:/laravel.com/docs/10.x), PHP 8.1+
*   **Frontend**: Bootstrap 3, jQuery
*   **Database**: MySQL
*   **Database Connection Details**: use execute_sql to communicate with database tables when needed.    
        
*   **Modular Structure**: [nwidart/laravel-modules](mdc:https:/nwidart.com/laravel-modules/v10/introduction)

## Core Development Principles

**🎯 These principles are MANDATORY for all development work and must be followed without exception.**

### Communication & Clarity Principle

*   **Request Rephrasing Requirement (MANDATORY)**: Before proceeding with any implementation or response, **ALWAYS rephrase the user's request in simple, clear language** that both technical and non-technical people can understand. This serves as a validation checkpoint to ensure correct interpretation of the request. Format this as:
    
    **In my own words**: [Clear, simple explanation of what the user is asking for]
    
    This prevents:
    *   Miscommunication and misaligned expectations
    *   Implementation of incorrect or unwanted features
    *   Wasted development time on misunderstood requirements
    *   Back-and-forth iterations due to unclear understanding

*   **Ask for Clarification**: When uncertain about requirements, implementation details, or expected behavior, **ALWAYS ask for clarification instead of making assumptions or guessing**. This prevents:
    *   Wasted development time on incorrect implementations
    *   Introduction of bugs through misunderstood requirements
    *   Costly refactoring when assumptions prove wrong
    *   Degraded code quality from hallucinated solutions

*   **Validate Understanding**: Before implementing complex features, confirm your understanding of the requirements with stakeholders or team members.

### Executive Summary Requirement (MANDATORY)

*   **Placement**: Every implementation plan or requirements list must start with an Executive Summary at the very beginning.
*   **Purpose**: Enable stakeholders to grasp the whole picture without reading the entire document.
*   **Contents**: Summarize the goals, scope, key decisions/assumptions, constraints/risks, primary deliverables, and success criteria.
*   **Format**: Keep it concise (3–6 bullets or 3–6 short sentences, ≤ 120 words) in plain English.
*   **Consistency**: Apply this to task plans, refactor plans, PR descriptions, and internal design docs.

### Focused Implementation Planning (MANDATORY)

*   **Think Hardest Principle**: Before creating any implementation plan, engage in deep critical thinking to identify the minimal viable approach that solves the specific problem without over-engineering.

*   **Core Question (MANDATORY)**: Every implementation plan MUST explicitly answer: **"How will we implement only the functionality we need right now?"**
    - Focus on immediate requirements and current use cases
    - Avoid building functionality for hypothetical future needs
    - Resist the temptation to create comprehensive solutions when targeted fixes suffice
    - Document what is intentionally excluded from scope

*   **Legacy Fallback Policy**: Do NOT include plans for legacy fallback support unless:
    - Explicitly requested by stakeholders
    - Required for maintaining critical system functionality during transition periods
    - Mandated by specific compliance or business continuity requirements

### Implementation Plan Flowchart (MANDATORY)

*   **Placement**: Immediately after the Executive Summary, include a flowchart diagram that visualizes the full end‑to‑end flow of the implementation plan.
*   **Purpose**: Give reviewers a single, high‑level picture of actors, entry points, major steps, decisions, data touchpoints, and external integrations.
*   **Format**: Use a Mermaid flowchart in the document. Prefer concise node labels, consistent naming, and readable left‑to‑right or top‑down layout.
*   **Step Numbering (MANDATORY)**: Prefix every node label with a clear step number reflecting execution order (e.g., 1, 2, 3 or hierarchical 1.1, 1.2.1). Use decimals for branches and related sub-steps.
*   **Contents Checklist**:
    - Triggers/entry points (routes with names, console commands, schedules)
    - Core controllers/services and orchestration steps
    - Database touchpoints (tables/models) and notable caches
    - Queues/jobs/events and email sending via `EmailService`
    - External providers/APIs and configuration boundaries
    - Decision points, error/fallback paths, and permission middleware
*   **Example (Mermaid)**:
```mermaid
flowchart TD
  A[1 Start: Trigger (route/command/schedule)] --> B{2 Inputs valid?}
  B -- Yes --> C[3 Controller ➜ Service]
  C --> D[4 DB writes/reads (tables noted)]
  D --> E[5 Dispatch Job/Event]
  E --> F[6 EmailService.sendEmail]
  B -- No --> G[2.1 Return validation errors]
  E -. failure .-> H[5.1 Log with context ➜ retry/circuit breaker]
```

### Function and Test Specifications (MANDATORY)

*   **Function Documentation**: For each planned implementation, specify:
    - **Function Names**: Exact method/function names that will be created or modified
    - **Function Purpose**: 1-3 sentences describing what each function does and its role in the system
    - **Key Parameters**: Input parameters and return types where applicable

*   **Test Coverage Planning**: For each function or feature, specify:
    - **Test Names**: Descriptive test method names following Laravel/PHPUnit conventions
    - **Test Behavior**: 5-10 words describing the specific behavior each test will verify
    - **Test Categories**: Unit tests for individual methods, feature tests for end-to-end workflows

*   **Example Function Specification**:
    ```
    Functions to Implement:
    - `NotificationService::sendUserAlert($userId, $message)` - Sends immediate alert to specific user via configured channels
    - `UserModel::markNotificationAsRead($notificationId)` - Updates notification status and logs read timestamp
    - `AlertController::triggerEmergencyNotification()` - Validates admin permissions and dispatches emergency alerts
    
    Tests to Create:
    - `test_send_user_alert_delivers_message()` - Valid user receives notification successfully
    - `test_send_user_alert_handles_invalid_user()` - Gracefully handles non-existent user IDs  
    - `test_mark_notification_updates_timestamp()` - Read timestamp recorded correctly
    - `test_emergency_notification_requires_admin()` - Non-admin users blocked from triggering
    - `test_emergency_notification_dispatches_to_all()` - All active users receive emergency alerts
    ```

*   **Enforcement**: Implementation plans and PRs describing non‑trivial changes must include this diagram and function/test specifications. Submissions without these details will be rejected in review.

### Post-Implementation Mermaid Flowchart (MANDATORY)

*   **Purpose**: Provide a single, high-level picture of what was implemented so reviewers don’t need to read every line.
*   **Scope (When required)**: After any code implementation or change (features, bug fixes, refactors, migrations/SQL, config, jobs/queues, external API work) across app/ and Modules/.
*   **Placement**: At the end of every deliverable (PR description, task summary, code-change response). Title the section: "Post-Implementation Flowchart".
*   **Consistency**: Complements the Implementation Plan Flowchart (MANDATORY). The plan diagram comes before implementation; this post-implementation diagram reflects what actually shipped.

*   **Contents Checklist (cover full process or the parts touched)**:
    - Triggers/entry points (routes with names, console commands, schedules)
    - Controllers/services/orchestrators and main steps
    - Database touchpoints (tables/models) and notable caches
    - Queues/jobs/events and email sending via `EmailService`
    - External providers/APIs and configuration boundaries
    - Decision points, error/fallback paths, and permission middleware

*   **Format**:
    - Mermaid flowchart fenced code block using top-down (TD) or left-right (LR)
    - Concise node labels; include exact route names, job names, table names when relevant
    - If only part of a larger flow was changed, show the minimal surrounding context and annotate changed nodes with "(changed)"
    - Step numbering is mandatory: Prefix each node label with a step number (1, 2, 3 or hierarchical 1.1, 1.2.1) that reflects execution order; use decimals for branches.

*   **Enforcement**:
    - Required for all non-trivial changes. Submissions without this section will be rejected in review.
    - Keep diagrams in sync with code; outdated diagrams are a defect.

*   **Example (Mermaid)**:
```mermaid
flowchart TD
  A[1 Start: Trigger (route/command/schedule)] --> B{2 Inputs valid?}
  B -- Yes --> C[3 Controller ➜ Service]
  C --> D[4 DB reads/writes (tables noted)]
  D --> E[5 Dispatch Job/Event]
  E --> F[6 EmailService.sendEmail]
  B -- No --> G[2.1 Return validation errors]
  E -. failure .-> H[5.1 Log with context ➜ retry/circuit breaker]
```

### Complex Code Commenting Standard (MANDATORY)

To enable quick, non-technical review while the AI coding agent edits multiple files, complex code must include clear, full-picture comments.

- **When required**
  - New class creation
  - New or significantly changed method (public/protected) beyond trivial getters/setters
  - Complex control flow (conditionals/loops), cross-file orchestration, or external integrations (HTTP, queue, cache, SQL, email)
  - Any change that materially affects data, permissions, or background jobs

- **Comment goals (what to cover, briefly)**
  - Purpose and role: what this class/method does and where it fits in the system
  - Inputs/outputs: parameters, return value, preconditions/assumptions, key edge cases
  - Side effects: DB tables/models touched, events/jobs/notifications, cache keys, external calls
  - Security/permissions: required middleware/policies, sensitive operations
  - Errors: how failures are handled (bubbles up, retries, fallback), notable logs/metrics
  - Dependencies: related services/config/routes/feature flags
  - Performance: noteworthy costs, caching/eager loading, limits

- **Style and format**
  - Plain, accessible English suitable for non-technical stakeholders; avoid jargon and ambiguous terms like "picked" or "duplicates"
  - Structure comments as a short intro, body, and tail (no explicit labels); keep to 6–10 lines per block when possible
  - Use PHPDoc docblocks above classes and methods; avoid inline narration comments inside code
  - For multi-file features, add a brief file-level "change context" block comment at the top describing this file’s role in the feature
  - Keep comments in sync with behavior; outdated comments are a defect

- **Examples (PHP)**

```php
/**
 * JobAlertService orchestrates fetching provider jobs, applying filters, and
 * scheduling user notifications. Coordinates provider clients, category mapping,
 * and digest sizing.
 *
 * Inputs/Outputs: Called by command/controller; returns counts of jobs queued.
 * Side effects: Reads provider APIs, writes job tables, dispatches email jobs,
 * updates caches.
 * Security/Permissions: Admin-only triggers via route middleware.
 * Errors: Retries transient HTTP errors, logs failures with context; circuit
 * breaker halts provider on repeated errors.
 */
final class JobAlertService
{
    /**
     * Sends a digest email for the given canonical category.
     *
     * @param int    $categoryId Canonical category id to translate per provider
     * @param string $recipientEmail Target email for the digest
     * @return int Number of jobs included in the digest
     *
     * Purpose: Aggregate recent jobs, enforce digest size limits, queue email.
     * Side effects: Reads jobs, logs, enqueues SendEmail job; touches cache key
     * jobseeker:digest:{email}:{category}.
     * Errors: Returns 0 on empty set; throws on invalid inputs; logs provider
     * mapping gaps.
     */
    public function sendDigest(int $categoryId, string $recipientEmail): int
    {
        // ... implementation ...
    }
}
```

```php
/*
Change context: Part of "Provider-aware notifications" feature. This file wires
routes to admin controllers and applies permission middleware. Related files:
Modules/JobSeeker/Http/Controllers/Admin/ProviderSettingsController.php,
app/Services/JobAlertService.php.
*/
```

- **Enforcement**
  - PRs introducing new classes or non-trivial methods must include these comment blocks
  - Changes that span multiple files must include a short file-level change context in each touched file
  - Reviewers should reject complex code without these comments

### Laravel Architecture Respect

*   **Eloquent Active Record Pattern**: Respect and leverage Laravel's Eloquent ORM as an Active Record pattern implementation:
    *   Models should contain business logic relevant to the entity they represent
    *   Use Eloquent relationships (`hasMany`, `belongsTo`, `belongsToMany`, etc.) instead of manual joins
    *   Leverage Eloquent's built-in methods (`create`, `update`, `delete`, `find`, `where`, etc.)
    *   Implement model events, observers, and accessors/mutators for entity-specific behavior
    *   Use model scopes for reusable query logic
    *   Avoid bypassing Eloquent with raw SQL unless performance critically requires it

*   **Framework Conventions**: Follow Laravel's conventions and patterns rather than fighting against them:
    *   Use Laravel helpers over facade classes (e.g., `auth()->id()` over `Auth::id()`)
    *   **Leverage Laravel's Built-in Features**: Always use Laravel's built-in solutions before creating custom implementations
    *   Follow naming conventions for controllers, models, and database tables
    *   Use Laravel's validation, authentication, and authorization systems

*   **Code Organization & Structure**:
    *   Keep controllers "slim" - move complex business logic to dedicated Service classes
    *   Use Laravel helpers instead of importing facade classes in the `use` section:
        ```php
        // ✅ DO:
        auth()->id()
        redirect()->route('home')
        session()->get('key')
        
        // ❌ DON'T:
        use Illuminate\Support\Facades\Auth;
        Auth::id()
        
        use Illuminate\Support\Facades\Redirect;
        Redirect::route('home')
        ```
    *   Only add code comments for non-obvious implementations or complex business logic
    *   Document the "why" not the "what" in comments

**⚠️ NON-COMPLIANCE**: Admin interfaces that don't follow mobile-first principles will be rejected and must be redesigned.

### Code Quality & Best Practices

*   **Reduce Code Duplication (DRY Principle)**:
    *   **Extract Common Logic**: Identify repeated code patterns and extract them into reusable methods, traits, or service classes
    *   **Use Inheritance & Composition**: Leverage class inheritance and composition to share common functionality
    *   **Create Utility Classes**: Build utility classes for common operations like date formatting, string manipulation, or validation
    *   **Example**: Instead of repeating email validation logic in multiple controllers, create a `EmailValidationService`:
        ```php
        // ✅ DO: Extract to service
        class EmailValidationService
        {
            public function validateEmail(string $email): bool
            {
                return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
            }
        }
        
        // ❌ DON'T: Repeat in multiple places
        if (filter_var($email, FILTER_VALIDATE_EMAIL) === false) {
            // validation logic repeated everywhere
        }
        ```

*   **Descriptive Variable & Method Naming**:
    *   **Variable Names**: Use clear, descriptive names that explain the purpose and content
    *   **Method Names**: Choose names that clearly describe what the method does
    *   **Boolean Variables**: Use positive names that read naturally in conditionals
    *   **Examples**:
        ```php
        // ✅ DO: Clear and descriptive
        $userProfile = User::find($userId);
        $isUserActive = $userProfile->status === 'active';
        $activeUserCount = User::where('status', 'active')->count();
        
        public function sendWelcomeEmailToNewUser(User $user): void
        public function isUserEligibleForDiscount(User $user): bool
        
        // ❌ DON'T: Unclear or abbreviated
        $up = User::find($uid);
        $ua = $up->status === 'active';
        $auc = User::where('status', 'active')->count();
        
        public function sendEmail($u): void
        public function check($u): bool
        ```

*   **Leverage Built-in Laravel Features**:
    *   **Use Laravel Helpers**: Prefer Laravel's built-in helper functions over custom implementations
    *   **Framework Methods**: Utilize Laravel's built-in methods for common operations
    *   **Avoid Reinventing**: Don't create custom solutions when Laravel provides them out-of-the-box
    *   **Examples**:
        ```php
        // ✅ DO: Use Laravel built-ins
        $user = User::firstOrCreate(['email' => $email], $userData);
        $users = User::whereIn('id', $userIds)->get();
        $userCount = User::count();
        
        // ❌ DON'T: Custom implementations
        $user = User::where('email', $email)->first();
        if (!$user) {
            $user = User::create($userData);
        }
        
        $users = collect();
        foreach ($userIds as $id) {
            $user = User::find($id);
            if ($user) $users->push($user);
        }
        
        $userCount = User::all()->count();
        ```

*   **Eloquent Database Query Best Practices**:
    *   **Use Eloquent Relationships**: Leverage `with()`, `has()`, `whereHas()` for efficient queries
    *   **Avoid N+1 Queries**: Use eager loading to prevent multiple database calls
    *   **Use Query Scopes**: Create reusable query logic in model scopes
    *   **Examples**:
        ```php
        // ✅ DO: Efficient with relationships
        $users = User::with(['posts', 'profile'])
            ->where('status', 'active')
            ->get();
        
        // Use query scopes
        $activeUsers = User::active()->with('profile')->get();
        
        // ❌ DON'T: N+1 queries
        $users = User::where('status', 'active')->get();
        foreach ($users as $user) {
            $user->posts; // Triggers individual query
            $user->profile; // Triggers individual query
        }
        ```

## Project Structure & Conventions

*   **Modular Development**: The project uses the `nwidart/laravel-modules` package. All module-specific backend code (Controllers, Models, Routes) should reside within the corresponding directory under `[Modules/](mdc:Modules)`.
    *   **Example**: Code for the Admission module is located in `[Modules/Admission/](mdc:Modules/Admission)`.
*   **Routing**: Module-specific web routes are defined in `Modules/<ModuleName>/Http/routes.php` (e.g., `[Modules/Admission/Http/routes.php](mdc:Modules/Admission/Http/routes.php)`). API routes are in `Modules/<ModuleName>/Http/api.php`. Ensure the module's `RouteServiceProvider` is configured correctly.
*   **Controllers**: Place controllers within the `Http/Controllers` directory of the respective module. Controllers should be `final` and read-only. Use method injection for dependencies or dedicated Service classes.
*   **Models**: Place Eloquent models within the `Entities` directory of the respective module (e.g., `[Modules/Admission/Entities/](mdc:Modules/Admission/Entities)`). Models should be `final`.
*   **Database Schema/Seeding**: **Do not** use Laravel migrations or seeders. Perform schema changes and initial data seeding directly via MySQL queries. Use Eloquent ORM for all application-level data manipulation (CRUD).
*   **Logging**: Implement extensive logging. Logs are stored in `[storage/logs/](mdc:storage/logs)`. Log method entry/exit points, contextual information, warnings, and errors.
*   **Coding Standards**: Adhere strictly to PSR-12 coding standards. Use `declare(strict_types=1);` and explicit type hints.
*   **Testing**: Write unit and feature tests using PHPUnit. Place tests within the `Tests/` directory of each module or the main `[tests/](mdc:tests)` directory.

## Critical Safety Rules

*   **🚨 Route Permission Middleware Requirement**: All routes that modify data (POST, PUT, PATCH, DELETE) **MUST** have explicit permission middleware attached. This is **MANDATORY** to:
    *   **Protect Data Integrity**: Prevent unauthorized modifications to system data
    *   **Support View-Only Access**: Accommodate users with read-only permissions
    *   **Enforce Role-Based Access**: Properly restrict actions based on user roles and permissions
    
    **Required Implementation:**
    ```php
    Route::post('/resource', [Controller::class, 'store'])
        ->middleware('permission:create_resource');  // REQUIRED
        
    Route::put('/resource/{id}', [Controller::class, 'update'])
        ->middleware('permission:edit_resource');   // REQUIRED
        
    Route::delete('/resource/{id}', [Controller::class, 'destroy'])
        ->middleware('permission:delete_resource'); // REQUIRED
    ```

    **Permission Verification Process:**
    1. After applying permission middleware to a route, use `execute_sql` to verify the permission exists:
    ```sql
    SELECT name FROM permissions WHERE name = 'permission_name';
    ```
    2. If the permission doesn't exist, create an SQL script to insert it:
    ```sql
    INSERT INTO permissions (name, guard_name, created_at, updated_at) 
    VALUES ('permission_name', 'web', NOW(), NOW());
    ```
    3. Store the SQL script in the module's Database directory following the timestamp naming convention
    
    **Best Practices:**
    *   Use descriptive permission names that clearly indicate the action
    *   Group related permissions in route groups when possible
    *   Consider using role_or_permission middleware for complex access patterns
    *   Document permission requirements in route comments
    
    **⚠️ WARNING**: Routes that modify data without proper permission middleware will be rejected during code review.

*   **🚨 NEVER USE RefreshDatabase Trait**: The `use Illuminate\Foundation\Testing\RefreshDatabase;` trait is **STRICTLY PROHIBITED** in all test files and any other code. This trait drops and recreates the entire database schema, which can result in:
    *   **Complete loss of production data** if accidentally run against production database
    *   **Deletion of all database tables, indexes, and stored procedures**
    *   **Irreversible data destruction** that cannot be recovered
    *   **Loss of custom database objects** and configurations
    
    **⚠️ WARNING**: Any code containing `RefreshDatabase` will be immediately rejected and must be rewritten using safe alternatives.

*   **🚨 NEVER TOUCH .env file**: The `.env` file is strictly for environment-specific configuration and should **NEVER** be modified directly by application code or committed to version control. Modifying this file can lead to:
    *   **Production Outages**: Overwriting production settings with development values.
    *   **Security Breaches**: Exposing sensitive credentials if the file is accidentally committed.
    *   **Configuration Instability**: Making it difficult to manage different environment settings reliably.
    
    **Best Practices for Configuration:**
    *   Use the `config()` helper to access environment variables defined in `.env`.
    *   Core application configuration is in `@config/`. Module-specific configuration is in `Modules/<ModuleName>/Config/config.php`.
    
    **⚠️ WARNING**: Any direct modification of the `.env` file via code will be rejected. All configuration should be managed through Laravel's configuration system.

## Key Practices

*   **Validation**: Use Laravel's Form Request validation (`app/Http/Requests/` or module-specific request classes).
*   **Security**: Implement CSRF protection, sanitize inputs, use Eloquent/prepared statements to prevent SQL injection. Use Laravel's authorization features (Policies).
*   **Database Interactions**: 
    *   **Primary Rule**: Use Eloquent ORM for all application-level data manipulation (CRUD operations)
    *   **Eloquent Relationships**: Define and use proper Eloquent relationships instead of manual joins
    *   **Query Optimization**: Use eager loading (`with()`) to prevent N+1 query problems
    *   **Model Logic**: Place entity-specific business logic in models using accessors, mutators, and scopes
    *   **Raw SQL Exception**: Only use raw SQL for schema changes, complex reporting queries, or performance-critical operations where Eloquent's overhead is prohibitive
*   **Schema Documentation (REQUIRED)**:
    - Every new table or ALTER TABLE that creates a table must provide a table-level comment.
    - When editing an existing table, if it lacks a comment, add one in the same change set.
    - Comment content must follow this concise template (≤ 300 chars):
      - Purpose: what the table represents and why it exists
      - Relationships: key foreign keys or joins it participates in
      - Context: domain/business rules or invariants worth knowing
      - Special: sensitivity/performance/retention notes if applicable
    - Example:
      ```sql
      ALTER TABLE `job_notifications`
        COMMENT = 'Purpose: templated notifications before send. Relationships: links jobs and recipients. Context: staging for dispatch; audited.';
      ```
    - Always verify missing comments before delivery:
      ```sql
      SELECT TABLE_NAME FROM information_schema.tables
      WHERE table_schema = DATABASE()
        AND (TABLE_COMMENT IS NULL OR TABLE_COMMENT = '');
      ```
    - Store comment DDL under the owning module in `Modules/<Module>/Database/` with timestamped filenames.
*   **Performance**: Leverage caching where appropriate. Minify assets.
*   **Services & Repositories**: Employ Service and Repository patterns for complex logic and data abstraction, placing them in `app/Services/`, `app/Repositories/` or module-specific directories.
*   **Error Handling**: Use Laravel's exception handling. Create custom exceptions as needed.
*   **Emailing**: All email sending functionality MUST be handled exclusively through the `[EmailService](mdc:app/Services/EmailService.php)`. This service is critical for maintaining consistent, reliable, and configurable email delivery across the entire application. Direct use of Laravel's `Mail` facade, `PHPMailer`, or any other email sending library is strictly prohibited.
*   **JobSeeker Email Notifications (NO QUEUE)**: JobSeeker email notifications MUST NOT use queues. Send synchronously via `EmailService` within the same request/command lifecycle. Do not use `ShouldQueue`, `queue()`, `dispatch()`, queued jobs, or Horizon for these emails.


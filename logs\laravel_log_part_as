#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#42 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#44 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#63 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#64 {main}
"} 
[2025-06-02 19:22:05] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:05] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:05] production.ERROR: Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database {"userId":148,"exception":"[object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:198)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#1 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#10 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#12 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#14 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#42 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#61 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#62 {main}

[previous exception] [object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:91)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#2 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#9 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#11 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#13 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#15 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#17 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#41 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#43 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 {main}

[previous exception] [object] (Exception(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:89)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(89): DateTime->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#2 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#3 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#10 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#12 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#14 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#18 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#42 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#44 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#63 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#64 {main}
"} 
[2025-06-02 19:22:06] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:06] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:06] production.ERROR: Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database {"userId":148,"exception":"[object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:198)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#1 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#10 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#12 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#14 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#42 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#61 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#62 {main}

[previous exception] [object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:91)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#2 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#9 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#11 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#13 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#15 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#17 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#41 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#43 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 {main}

[previous exception] [object] (Exception(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:89)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(89): DateTime->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#2 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#3 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#10 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#12 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#14 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#18 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#42 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#44 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#63 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#64 {main}
"} 
[2025-06-02 19:22:08] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:08] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:08] production.ERROR: Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database {"userId":148,"exception":"[object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:198)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#1 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#10 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#12 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#14 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#42 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#61 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#62 {main}

[previous exception] [object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:91)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#2 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#9 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#11 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#13 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#15 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#17 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#41 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#43 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 {main}

[previous exception] [object] (Exception(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:89)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(89): DateTime->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#2 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#3 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#10 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#12 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#14 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#18 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#42 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#44 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#63 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#64 {main}
"} 
[2025-06-02 19:22:08] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:08] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:08] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:08] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:08] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:08] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:08] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:08] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:09] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:09] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:09] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:09] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:09] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:09] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:10] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:10] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:12] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:12] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:12] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:12] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:12] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:12] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:12] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:12] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:12] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:12] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:12] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:12] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:13] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:13] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:13] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:13] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:14] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:14] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:14] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:14] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:14] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:14] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:14] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:14] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:14] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:14] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:14] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:14] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:14] production.DEBUG: getStudentImageUrl {"student":2549,"size":null} 
[2025-06-02 19:22:14] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2549,"student_photo":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:14] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2549,"checked_path":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:15] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:15] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:15] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2549,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2549,"student_photo":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2549,"checked_path":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2549,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2549,"student_photo":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2549,"checked_path":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2549,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2549,"student_photo":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2549,"checked_path":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2549,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2549,"student_photo":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2549,"checked_path":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2549,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2549,"student_photo":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2549,"checked_path":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2549,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2549,"student_photo":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2549,"checked_path":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:15] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2549,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2549,"student_photo":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2549,"checked_path":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2549,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2549,"student_photo":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2549,"checked_path":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2549,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2549,"student_photo":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2549,"checked_path":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2549,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2549,"student_photo":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2549,"checked_path":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2549,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2549,"student_photo":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2549,"checked_path":"public/uploads/student/d998321d1ca4149684850da5b983c84f.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2370,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2370,"student_photo":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2370,"checked_path":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 19:22:15] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2370,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2370,"student_photo":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2370,"checked_path":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2370,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2370,"student_photo":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2370,"checked_path":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2370,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2370,"student_photo":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2370,"checked_path":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2370,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2370,"student_photo":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2370,"checked_path":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2370,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2370,"student_photo":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2370,"checked_path":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2370,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2370,"student_photo":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2370,"checked_path":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3349,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3349,"student_photo":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3349,"checked_path":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3349,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3349,"student_photo":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3349,"checked_path":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3349,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3349,"student_photo":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3349,"checked_path":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3349,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3349,"student_photo":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3349,"checked_path":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2370,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2370,"student_photo":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2370,"checked_path":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3349,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3349,"student_photo":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3349,"checked_path":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3349,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3349,"student_photo":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":3349,"checked_path":"public/uploads/student/95a8b5ea242945eaff82e54fa7f7210e.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2370,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2370,"student_photo":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2370,"checked_path":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2370,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2370,"student_photo":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2370,"checked_path":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2370,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2370,"student_photo":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2370,"checked_path":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2370,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2370,"student_photo":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2370,"checked_path":"public/uploads/student/ff568b9e2ea7b7919cc0983cb5e7cf4c.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2369,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2369,"student_photo":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2369,"checked_path":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2545,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2545,"student_photo":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2545,"checked_path":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2369,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2369,"student_photo":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2369,"checked_path":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2545,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2545,"student_photo":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2545,"checked_path":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2545,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2545,"student_photo":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2545,"checked_path":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2545,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2545,"student_photo":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2545,"checked_path":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2545,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2545,"student_photo":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2545,"checked_path":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2545,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2545,"student_photo":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2545,"checked_path":"public/uploads/student/074a74a524ce6e5b5f1c33543b2c98f6.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2369,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2369,"student_photo":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2369,"checked_path":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2369,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2369,"student_photo":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2369,"checked_path":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2369,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2369,"student_photo":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2369,"checked_path":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2369,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2369,"student_photo":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2369,"checked_path":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2369,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2369,"student_photo":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2369,"checked_path":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4228,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4228,"student_photo":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4228,"checked_path":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4228,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4228,"student_photo":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4228,"checked_path":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4228,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4228,"student_photo":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4228,"checked_path":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4228,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4228,"student_photo":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4228,"checked_path":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4228,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4228,"student_photo":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4228,"checked_path":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4228,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4228,"student_photo":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4228,"checked_path":"public/uploads/student/81ef498e1a7ef8de559bc6150996a85e.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2369,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2369,"student_photo":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2369,"checked_path":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2369,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2369,"student_photo":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2369,"checked_path":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2369,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2369,"student_photo":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2369,"checked_path":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2369,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2369,"student_photo":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2369,"checked_path":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":2369,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":2369,"student_photo":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":2369,"checked_path":"public/uploads/student/f2638ad2665449a4b20a5afe1221e176.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3660,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3660,"student_photo":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4276,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4276,"student_photo":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4276,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4276,"student_photo":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4276,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4276,"student_photo":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3660,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3660,"student_photo":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4276,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4276,"student_photo":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4276,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4276,"student_photo":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4276,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4276,"student_photo":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742402.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4277,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4277,"student_photo":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4277,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4277,"student_photo":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4277,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4277,"student_photo":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4277,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4277,"student_photo":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4277,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4277,"student_photo":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4277,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4277,"student_photo":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747742945.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3660,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3660,"student_photo":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3660,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3660,"student_photo":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4230,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4230,"student_photo":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4230,"checked_path":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3660,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3660,"student_photo":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4230,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4230,"student_photo":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4230,"checked_path":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4230,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4230,"student_photo":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4230,"checked_path":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3660,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3660,"student_photo":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3660,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3660,"student_photo":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4230,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4230,"student_photo":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4230,"checked_path":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4230,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4230,"student_photo":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4230,"checked_path":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4230,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4230,"student_photo":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4230,"checked_path":"public/uploads/student/379f70adfe120bfc67ef168cae610b79.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4227,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4227,"student_photo":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4227,"checked_path":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3660,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3660,"student_photo":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4227,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4227,"student_photo":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4227,"checked_path":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3660,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3660,"student_photo":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4227,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4227,"student_photo":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4227,"checked_path":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4227,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4227,"student_photo":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4227,"checked_path":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3660,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3660,"student_photo":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4227,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4227,"student_photo":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4227,"checked_path":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4227,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4227,"student_photo":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:22:15] production.WARNING: StudentImageService@getStudentImageUrl - photo missing in FS, using gender default {"student_id":4227,"checked_path":"public/uploads/student/121e3771173004f709a28e441d2193bd.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3660,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3660,"student_photo":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":3660,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":3660,"student_photo":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/f86a43afa255ea41a7800b9dfebde2ea.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4089,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4089,"student_photo":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4089,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4089,"student_photo":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4089,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4089,"student_photo":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4089,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4089,"student_photo":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4089,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4089,"student_photo":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4089,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4089,"student_photo":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"public/uploads/student/6b514f42a38405078b3436e06558e250.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":973,"size":null} 
[2025-06-02 19:22:15] production.INFO: → falling back to gender default {"student_id":973,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":973,"size":null} 
[2025-06-02 19:22:15] production.INFO: → falling back to gender default {"student_id":973,"gender":"male","file":"maleStudentProfilePicture.png"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4275,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4275,"student_photo":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4275,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4275,"student_photo":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4275,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4275,"student_photo":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4275,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4275,"student_photo":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4275,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4275,"student_photo":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: → serving student_photo from storage {"path":"/images/student/1747741035.jpeg"} 
[2025-06-02 19:22:15] production.DEBUG: getStudentImageUrl {"student":4275,"size":null} 
[2025-06-02 19:22:15] production.DEBUG: → checking DB & filesystem for student_photo {"student_id":4275,"student_photo":"/images/student/1747741035.jpeg"} 

<?php

namespace Modules\ExaminationCertification\Http\Controllers;

use App\Classes;
use App\Employee;
use App\Weekend;
use App\ClassRoom;
use App\ClassTime;
use App\YearCheck;
use App\ApiBaseMethod;
use App\AssignSubject;
use App\ClassRoutineUpdate;
use Illuminate\Http\Request;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class ClassRoutineNewController extends Controller
{

    public function __construct()
	{
//        $this->middleware('PM');
        // User::checkAuth();
	}

    public function classRoutine(Request $request)
    {

        try {
            $classes = Classes::all();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendResponse($classes, null);
            }
            return view('examinationcertification::academics.class_routine_new', compact('classes'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function classRoutinePrint($class, $section)
    {

        // try {
            $class_times = ClassTime::where('type', 'class')->get();
            $class_id = $class;
            $section_id = $section;

            $weekends = Weekend::orderBy('order', 'ASC')->all();
            
            $classes = Classes::all();

            // $customPaper = array(0, 0, 700.00, 1500.80);
            $pdf = \PDF::loadView(
                'examinationcertification::academics.class_routine_print',
                [
                    'classes' => $classes,
                    'class_times' => $class_times,
                    'class_id' => $class_id,
                    'section_id' => $section_id,
                    'weekends' => $weekends,
                ]
            )->setPaper('A4', 'landscape');
            return $pdf->stream('class_routine.pdf');
        // } catch (\Exception $e) {
        //     Toastr::error('Operation Failed', 'Failed');
        //     return redirect()->back();
        // }
    }

    public function classRoutineSearch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'class' => 'required',
            'section' => 'required',
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $class_times = ClassTime::where('type', 'class')->get();
            $class_id = $request->class;
            $section_id = $request->section;

            $weekends = Weekend::orderBy('order', 'ASC')->all();
            $classes = Classes::all();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['classes'] = $classes->toArray();
                $data['class_times'] = $class_times->toArray();
                $data['class_id'] = $class_id;
                $data['section_id'] = $section_id;
                $data['weekends'] = $weekends;
                return ApiBaseMethod::sendResponse($data, null);
            }

            return view('examinationcertification::academics.class_routine_new', compact('classes', 'class_times', 'class_id', 'section_id', 'weekends'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function addNewClassRoutine($class_time_id, $day, $class_id, $section_id)
    {

        try {
            $assinged_subjects = ClassRoutineUpdate::select('subject_id')->where('class_id', $class_id)->where('section_id', $section_id)->where('day', $day)->get();

            $assinged_subject = [];
            foreach ($assinged_subjects as $value) {
                $assinged_subject[] = $value->subject_id;
            }

            $assinged_rooms = ClassRoutineUpdate::select('room_id')->where('class_period_id', $class_time_id)->where('day', $day)->get();

            $assinged_room = [];
            foreach ($assinged_rooms as $value) {
                $assinged_room[] = $value->room_id;
            }
            $rooms = ClassRoom::all();
            $subjects = AssignSubject::where('class_id', $class_id)->where('section_id', $section_id)->get();
            return view('examinationcertification::academics.add_new_class_routine_form', compact('rooms', 'subjects', 'day', 'class_time_id', 'class_id', 'section_id', 'assinged_subject', 'assinged_room'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function addNewClassRoutineEdit($class_time_id, $day, $class_id, $section_id, $subject_id, $room_id, $assigned_id, $employee_id)
    {

        try {
            $assinged_subjects = ClassRoutineUpdate::select('subject_id')->where('class_id', $class_id)->where('section_id', $section_id)->where('day', $day)->where('subject_id', '!=', $subject_id)->get();

            $assinged_subject = [];
            foreach ($assinged_subjects as $value) {
                $assinged_subject[] = $value->subject_id;
            }

            $assinged_rooms = ClassRoutineUpdate::select('room_id')->where('room_id', '!=', $room_id)->where('class_period_id', $class_time_id)->where('day', $day)->get();

            $assinged_room = [];
            foreach ($assinged_rooms as $value) {
                $assinged_room[] = $value->room_id;
            }
            $rooms = ClassRoom::all();
            $teacher_detail = Employee::select('id', 'full_name')->where('id', $employee_id)->first();

            $subjects = AssignSubject::where('class_id', $class_id)->where('section_id', $section_id)->get();
            return view('examinationcertification::academics.add_new_class_routine_form', compact('rooms', 'subjects', 'day', 'class_time_id', 'class_id', 'section_id', 'assinged_subject', 'assinged_room', 'subject_id', 'room_id', 'assigned_id', 'teacher_detail'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function addNewClassRoutineStore(Request $request)
    {
        try {
            if (!isset($request->assigned_id)) {
                $check = ClassRoutineUpdate::where('class_id', $request->class_id)->where('section_id', $request->section_id)->where('subject_id', $request->subject)->where('room_id', $request->room)->where('class_period_id', $request->class_time_id)->where('day', $request->day)->first();

                if ($check == "") {
                    $class_routine = new ClassRoutineUpdate();
                    $class_routine->class_id = $request->class_id;
                    $class_routine->section_id = $request->section_id;
                    $class_routine->subject_id = $request->subject;
                    $class_routine->employee_id = $request->employee_id;
                    $class_routine->room_id = $request->room;
                    $class_routine->class_period_id = $request->class_time_id;
                    $class_routine->day = $request->day;
                    $class_routine->organization_id = Auth::user()->organization_id;
                    $class_routine->academic_id = YearCheck::getAcademicId();
                    $class_routine->save();
                    // \Session::flash('success', 'Class routine has been assigned successfully');
                    Toastr::success('Class routine has been assigned successfully', 'Success');
                }
            } else {
                $class_routine = ClassRoutineUpdate::find($request->assigned_id);
                $class_routine->class_id = $request->class_id;
                $class_routine->section_id = $request->section_id;
                $class_routine->subject_id = $request->subject;
                $class_routine->employee_id = $request->employee_id;
                $class_routine->room_id = $request->room;
                $class_routine->class_period_id = $request->class_time_id;
                $class_routine->day = $request->day;
                $class_routine->save();
                // \Session::flash('success', 'Class routine has been updated successfully');
                Toastr::success('Class routine has been updated successfully', 'Success');
            }

            //$class_times = ClassTime::all();
            $class_id = $request->class_id;
            $section_id = $request->section_id;

            //$classes = Classes::all();
            //return view('examinationcertification::academics.class_routine_new', compact('classes', 'class_times', 'class_id', 'section_id'));

            return redirect('class-routine-new/' . $class_id . '/' . $section_id);
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function classRoutineRedirect($class_id, $section_id)
    {

        try {
            $weekends = Weekend::orderBy('order', 'ASC')->all();
            $class_times = ClassTime::where('type', 'class')->get();
            $classes = Classes::all();
            return view('examinationcertification::academics.class_routine_new', compact('classes', 'class_times', 'class_id', 'section_id', 'weekends'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function getClassTeacherAjax(Request $request)
    {

        try {
            $subject_teacher = AssignSubject::select('employee_id')->where('class_id', $request->class_id)->where('section_id', $request->section_id)->where('subject_id', $request->subject)->first();
            $teacher_detail = '';
            $i = 0;
            if ($subject_teacher->employee_id != "") {
                if ($request->update_employee_id == "") {

                    $already_assigned = ClassRoutineUpdate::where('class_period_id', $request->class_time_id)->where('day', $request->day)->where('employee_id', $subject_teacher->employee_id)->first();
                } else {
                    $already_assigned = ClassRoutineUpdate::where('employee_id', '!=', $request->update_employee_id)->where('class_period_id', $request->class_time_id)->where('day', $request->day)->where('employee_id', $subject_teacher->employee_id)->first();
                }

                $i++;

                if ($already_assigned == "") {
                    $teacher_detail = Employee::where('id', $subject_teacher->employee_id)->first();
                }
            }

            return response()->json([$teacher_detail, $i]);
        } catch (\Exception $e) {
            return response()->json("",404);
        }
    }

    public function classRoutineReport(Request $request)
    {

        try {
            $classes = Classes::all();


            return view('examinationcertification::reports.class_routine_report', compact('classes'));
        } catch (\Exception $e) {
            dd($e);
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function classRoutineReportSearch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'class' => 'required',

        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $class_times = ClassTime::where('type', 'class')->get();
            $class_id = $request->class;
            $section_id = $request->section;
            $weekends = Weekend::orderBy('order', 'ASC')->all();
            $classes = Classes::all();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['classes'] = $classes->toArray();
                $data['class_times'] = $class_times->toArray();
                $data['class_id'] = $class_id;
                $data['section_id'] = $section_id;
                $data['weekends'] = $weekends->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('examinationcertification::reports.class_routine_report', compact('classes', 'class_times', 'class_id', 'section_id', 'weekends'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function teacherClassRoutineReport(Request $request)
    {

        try {
            $teachers = Employee::role('teacher_2_')->select('id', 'full_name')->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendResponse($teachers, null);
            }
            return view('examinationcertification::reports.teacher_class_routine_report', compact('teachers'));
        } catch (\Exception $e) {
            dd($e);
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function teacherClassRoutineReportSearch(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'teacher' => 'required',
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $class_times = ClassTime::where('type', 'class')->get();
            $teacher_id = $request->teacher;

            $weekends = Weekend::orderBy('order', 'ASC')->get();
            $teachers = Employee::select('id', 'full_name')->get();
            $classes = Classes::all();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['class_times'] = $class_times->toArray();
                $data['employee_id'] = $teacher_id;
                $data['weekends'] = $weekends->toArray();
                $data['teachers'] = $teachers->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('examinationcertification::reports.teacher_class_routine_report', compact('class_times', 'teacher_id', 'weekends', 'teachers'));
        } catch (\Exception $e) {
            dd($e);
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function deleteClassRoutineModal($id)
    {

        try {
            return view('examinationcertification::academics.delete_class_routine', compact('id'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function deleteClassRoutine($id)
    {

        try {
            $class_routine = ClassRoutineUpdate::find($id);
            $class_id = $class_routine->class_id;
            $section_id = $class_routine->section_id;
            $result = $class_routine->delete();
            if ($result) {
                Toastr::success('Class routine has been deleted successfully', 'Success');
            } else {
                Toastr::error('Operation Failed', 'Failed');
            }
            return redirect('class-routine-new/' . $class_id . '/' . $section_id);
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}
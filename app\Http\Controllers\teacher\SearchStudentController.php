<?php

namespace App\Http\Controllers\teacher;

use App\Role;
use App\Classes;
use App\Employee;
use App\Student;
use App\Weekend;
use App\YearCheck;
use App\Homework;
use App\ClassTime;
use App\ApiBaseMethod;
use App\LeaveRequest;
use App\Notification;
use App\AssignSubject;
use App\EmployeeAttendence;
use Illuminate\Http\Request;
use App\TeacherUploadContent;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class SearchStudentController extends Controller
{
    public function __construct()
	{
        $this->middleware('PM');
        // User::checkAuth();
	}

    public function viewTeacherRoutine()
    {
        try {
            // $assinged_subjects = AssignSubject::where('teacher_id', 4)->distinct()->get(['subject_id']);

            // $class_routines = [];
            // foreach($assinged_subjects as $assinged_subject){
            // 	$class_routines[] = ClassRoutine::where('subject_id', $assinged_subject->subject_id)->first();
            // }
            $user = Auth::user();

            $class_times = ClassTime::all();
            $employee_id = $user->staff->id;

            $weekends = Weekend::orderBy('order', 'ASC')->all();
            $teachers = Employee::select('id', 'full_name')->all();

            return view('modules.site.templates.wajeha.backEnd.teacherPanel.view_class_routine', compact('class_times', 'employee_id', 'weekends', 'teachers'));
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function searchStudent(Request $request)
    {

        try {
            $class_id = $request->class;
            $section_id = $request->section;
            $name = $request->name;
            $roll_no = $request->roll_no;
            $students = '';
            $msg = '';

            if (!empty($request->class) && !empty($request->section)) {
                $students = DB::table('students')
                    ->select('students.id', 'student_photo', 'full_name', 'roll_no', 'class_name', 'section_name', 'user_id')
                    ->join('sections', 'sections.id', '=', 'students.section_id')
                    ->join('classes', 'classes.id', '=', 'students.class_id')
                    ->where('students.class_id', $request->class)
                    ->where('students.section_id', $request->section)
                    ->where('students.organization_id',Auth::user()->organization_id)->get();
                $msg = "Student Found";
            } elseif (!empty($request->class)) {
                $students = DB::table('students')
                    ->select('students.id', 'student_photo', 'full_name', 'roll_no', 'class_name', 'section_name', 'user_id')
                    ->join('sections', 'sections.id', '=', 'students.section_id')
                    ->join('classes', 'classes.id', '=', 'students.class_id')
                    ->where('students.class_id', $class_id)
                    // ->where('section_id',$section_id)
                    ->where('students.organization_id',Auth::user()->organization_id)->get();
                $msg = "Student Found";
            } elseif ($request->name != "") {
                $students = DB::table('students')
                    ->select('students.id', 'student_photo', 'full_name', 'roll_no', 'class_name', 'section_name', 'user_id')
                    ->join('sections', 'sections.id', '=', 'students.section_id')
                    ->join('classes', 'classes.id', '=', 'students.class_id')
                    ->where('full_name', 'like', '%' . $request->name . '%')
                    ->first();
                $msg = "Student Found";
            } elseif ($request->roll_no != "") {
                $students = DB::table('students')
                    ->select('students.id', 'student_photo', 'full_name', 'roll_no', 'class_name', 'section_name', 'user_id')
                    ->join('sections', 'sections.id', '=', 'students.section_id')
                    ->join('classes', 'classes.id', '=', 'students.class_id')
                    ->where('roll_no', 'like', '%' . $request->roll_no . '%')
                    ->first();
                $msg = "Student Found";
            } else {

                $msg = "Student Not Found";
            }

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['students'] = $students;

                return ApiBaseMethod::sendResponse($data, $msg);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function myRoutine(Request $request, $id)
    {

        try {
            $teacher = DB::table('employees')
                ->where('user_id', '=', $id)
                ->first();
            $employee_id = $teacher->id;

            $weekends = Weekend::orderBy('order', 'ASC')->all();
            $class_times = ClassTime::where('type', 'class')->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $weekenD = Weekend::get();
                foreach ($weekenD as $row) {
                    $data[$row->name] = DB::table('class_routine_updates')
                        ->select('class_id', 'class_name', 'section_id', 'section_name', 'class_times.period', 'class_times.start_time', 'class_times.end_time', 'subjects.subject_name', 'class_rooms.room_no')
                        ->join('classes', 'classes.id', '=', 'class_routine_updates.class_id')
                        ->join('sections', 'sections.id', '=', 'class_routine_updates.section_id')
                        ->join('class_times', 'class_times.id', '=', 'class_routine_updates.class_period_id')
                        ->join('subjects', 'subjects.id', '=', 'class_routine_updates.subject_id')
                        ->join('class_rooms', 'class_rooms.id', '=', 'class_routine_updates.room_id')

                        ->where([
                            ['class_routine_updates.employee_id', $employee_id], ['class_routine_updates.day', $row->id],
                        ])->where('sections.organization_id',Auth::user()->organization_id)->get();
                }

                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function sectionRoutine(Request $request, $id, $class, $section)
    {
        try {
            $teacher = DB::table('employees')
                ->where('user_id', '=', $id)
                ->first();
            $employee_id = $teacher->id;

            $weekends = Weekend::orderBy('order', 'ASC')->all();
            $class_times = ClassTime::where('type', 'class')->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $weekenD = Weekend::get();
                foreach ($weekenD as $row) {
                    $data[$row->name] = DB::table('class_routine_updates')
                        ->select('class_times.period', 'class_times.start_time', 'class_times.end_time', 'subjects.subject_name', 'class_rooms.room_no')
                        ->join('classes', 'classes.id', '=', 'class_routine_updates.class_id')
                        ->join('sections', 'sections.id', '=', 'class_routine_updates.section_id')
                        ->join('class_times', 'class_times.id', '=', 'class_routine_updates.class_period_id')
                        ->join('subjects', 'subjects.id', '=', 'class_routine_updates.subject_id')
                        ->join('class_rooms', 'class_rooms.id', '=', 'class_routine_updates.room_id')

                        ->where([
                            ['class_routine_updates.employee_id', $employee_id],
                            ['class_routine_updates.class_id', $class],
                            ['class_routine_updates.section_id', $section],
                            ['class_routine_updates.day', $row->id],
                        ])->where('sections.organization_id',Auth::user()->organization_id)->get();
                }

                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function classSection(Request $request, $id)
    {
        try {
            $teacher = DB::table('employees')
                ->where('user_id', '=', $id)
                ->first();
            $employee_id = $teacher->id;

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $teacher_classes = DB::table('assign_subjects')
                    ->join('classes', 'classes.id', '=', 'assign_subjects.class_id')
                    ->distinct('class_id')

                    ->where('employee_id', $employee_id)->get();
                foreach ($teacher_classes as $class) {
                    $data[$class->class_name] = DB::table('assign_subjects')
                        ->join('subjects', 'subjects.id', '=', 'assign_subjects.subject_id')
                        ->join('sections', 'sections.id', '=', 'assign_subjects.section_id')
                        ->select('section_name', 'subject_name')
                        ->distinct('section_id')
                        ->where([
                            ['assign_subjects.class_id', $class->id],
                        ])->get();
                }

                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    //Some Changes
    public function subjectsName(Request $request, $id)
    {

        try {
            $teacher = DB::table('employees')
                ->where('user_id', '=', $id)
                ->first();
            $employee_id = $teacher->id;

            $subjectsName = DB::table('assign_subjects')
                ->join('subjects', 'subjects.id', '=', 'assign_subjects.subject_id')
                ->select('subject_id', 'subject_name', 'code', 'subject_type')
                ->where('assign_subjects.active_status', 1)
                ->where('employee_id', $employee_id)
                ->distinct('subject_id')
                ->where('assign_subjects.organization_id',Auth::user()->organization_id)->get();
            $subject_type = 'T=Theory, P=Practical';
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data['subjectsName'] = $subjectsName->toArray();
                $data['subject_type'] = $subject_type;
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function addHomework(Request $request)
    {

        return $request->all();
        $this->validate($request,[
            'class' => "required",
            'section' => "required",
            'subject' => "required",
            'assign_date' => "required",
            'submission_date' => "required",
            'description' => "required",
            'marks' => "required"
        ]);

        try {
            $fileName = "";
            if ($request->file('homework_file') != "") {

                $file = $request->file('homework_file');
                $fileName = $request->employee_id . time() . "." . $file->getClientOriginalExtension();
                //$fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/homework/', $fileName);
                $fileName = 'public/uploads/homework/' . $fileName;
            }

            $homeworks = new Homework;
            $homeworks->class_id = $request->class;
            $homeworks->section_id = $request->section;
            $homeworks->subject_id = $request->subject;
            $homeworks->marks = $request->marks;
            $homeworks->created_by = $request->employee_id;
            $homeworks->homework_date = $request->assign_date;
            $homeworks->submission_date = $request->submission_date;
            //$homeworks->marks = $request->marks;
            $homeworks->description = $request->description;
            $homeworks->academic_id = YearCheck::getAcademicId();
            if ($fileName != "") {
                $homeworks->file = $fileName;
            }
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {

                $results = $homeworks->save();

                return ApiBaseMethod::sendResponse($results, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function homeworkList2(Request $request, $id)
    {
        try {
            $teacher = DB::table('employees')
                ->where('user_id', '=', $id)
                ->first();
            $employee_id = $teacher->id;

            $homeworkLists = Homework::where('homeworks.created_by', '=', $employee_id)
                ->join('classes', 'homeworks.class_id', '=', 'classes.id')
                ->join('sections', 'homeworks.section_id', '=', 'sections.id')
                ->join('subjects', 'homeworks.subject_id', '=', 'subjects.id')
                ->select('homework_date', 'submission_date', 'evaluation_date', 'file', 'homeworks.marks', 'description', 'subject_name', 'class_name', 'section_name')
                ->where('homeworks.organization_id',Auth::user()->organization_id)->get();


            $classes = Classes::all();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];

                return ApiBaseMethod::sendResponse($homeworkLists, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function homeworkList(Request $request, $id)
    {
        try {
            $teacher = Employee::where('id', '=', $id)->first();
            $employee_id = $teacher->id;


            $subject_list = AssignSubject::where('employee_id', '=', $employee_id)->get();

            $i = 0;
            foreach ($subject_list as $subject) {
                $homework_subject_list[$subject->subject->subject_name] = $subject->subject->subject_name;
                $allList[$subject->subject->subject_name] = DB::table('homeworks')
                    ->leftjoin('subjects', 'subjects.id', '=', 'homeworks.subject_id')
                    ->where('homeworks.created_by', $employee_id)
                    ->where('subject_id', $subject->subject_id)->get()->toArray();;
            }

            foreach ($allList as $single) {
                foreach ($single as $singleHw) {
                    $std_homework = DB::table('homework_students')
                        ->select('homework_id', 'complete_status')
                        ->where('homework_id', '=', $singleHw->id)
                        ->where('complete_status', 'C')
                        ->first();

                    $d['homework_id'] = $singleHw->id;
                    $d['description'] = $singleHw->description;
                    $d['subject_name'] = $singleHw->subject_name;
                    $d['homework_date'] = $singleHw->homework_date;
                    $d['submission_date'] = $singleHw->submission_date;
                    $d['evaluation_date'] = $singleHw->evaluation_date;
                    $d['file'] = $singleHw->file;
                    $d['marks'] = $singleHw->marks;

                    if (!empty($std_homework)) {
                        $d['status'] = 'C';
                    } else {
                        $d['status'] = 'I';
                    }
                    $kijanidibo[] = $d;
                }
            }
            // return $kijanidibo;
            //$homeworkLists = Homework::whereIn('class_id', $studentClasses)->where('section_id', $student_detail->section_id)->get();
            // dd($allList);
            $data = [];

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {

                $data = $kijanidibo;
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function teacherMyAttendanceSearchAPI(Request $request, $id = null)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'month' => "required",
            'year' => "required",
        ]);

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }
        try {
            $teacher = Employee::where('id', $id)->first();

            $year = $request->year;
            $month = $request->month;
            if ($month < 10) {
                $month = '0' . $month;
            }
            $current_day = date('d');

            $days = cal_days_in_month(CAL_GREGORIAN, $month, $request->year);
            $days2 = cal_days_in_month(CAL_GREGORIAN, $month - 1, $request->year);
            $previous_month = $month - 1;
            $previous_date = $year . '-' . $previous_month . '-' . $days2;
            $previousMonthDetails['date'] = $previous_date;
            $previousMonthDetails['day'] = $days2;
            $previousMonthDetails['week_name'] = date('D', strtotime($previous_date));

            $attendances = StaffAttendence::where('student_id', $teacher->id)
                ->where('class_time', 'like', '%' . $request->year . '-' . $month . '%')
                ->select('attendance_type', 'class_time')
                ->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data['attendances'] = $attendances;
                $data['previousMonthDetails'] = $previousMonthDetails;
                $data['days'] = $days;
                $data['year'] = $year;
                $data['month'] = $month;
                $data['current_day'] = $current_day;
                $data['status'] = 'Present: P, Late: L, Absent: A, Holiday: H, Half Day: F';
                return ApiBaseMethod::sendResponse($data, null);
            }
            //Test
            //return view('modules.site.templates.wajeha.backEnd.studentPanel.student_attendance', compact('attendances', 'days', 'year', 'month', 'current_day'));
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function applyLeave(Request $request)
    {


        $input = $request->all();
        if (ApiBaseMethod::checkUrl($request->fullUrl())) {
            $validator = Validator::make($input, [
                'apply_date' => "required",
                'leave_type' => "required",
                'leave_from' => 'required|before_or_equal:leave_to',
                'leave_to' => "required",
                'employee_id' => "required",
                'reason' => "required",

            ]);
        }

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
        }
        try {
            $fileName = "";
            if ($request->file('attach_file') != "") {
                $file = $request->file('attach_file');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/leave_request/', $fileName);
                $fileName = 'public/uploads/leave_request/' . $fileName;
            }

            $apply_leave = new LeaveRequest();
            $apply_leave->employee_id = $request->input('employee_id');
            $apply_leave->role_id = 4;
            $apply_leave->apply_date = date('Y-m-d');
            $apply_leave->leave_define_id = $request->input('leave_type');
            $apply_leave->leave_from = $request->input('leave_from');
            $apply_leave->leave_to = $request->input('leave_to');
            $apply_leave->approve_status = 'P';
            $apply_leave->reason = $request->input('reason');
            $apply_leave->academic_id = YearCheck::getAcademicId();
            //return $request->teacher_id;
            if ($fileName != "") {
                $apply_leave->file = $fileName;
            }

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {

                $result = $apply_leave->save();

                return ApiBaseMethod::sendResponse($result, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function staffLeaveList(Request $request, $id)
    {
        try {
            $teacher = Employee::where('id', '=', $id)->first();
            $employee_id = $teacher->id;

            $leave_list = LeaveRequest::where('employee_id', '=', $employee_id)
                ->join('leave_defines', 'leave_defines.id', '=', 'leave_requests.leave_define_id')
                ->join('leave_types', 'leave_types.id', '=', 'leave_defines.type_id')
                ->where('leave_defines.organization_id',Auth::user()->organization_id)->get();
            $status = 'P for Pending, A for Approve, R for reject';
            $data = [];
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data['leave_list'] = $leave_list->toArray();
                $data['status'] = $status;
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function leaveTypeList(Request $request)
    {

        try {
            //return "Api URL";
            $leave_type = DB::table('leave_defines')
                ->where('role_id', 3 /** teacher **/)
                ->join('leave_types', 'leave_types.id', '=', 'leave_defines.type_id')
                ->where('leave_defines.active_status', 1)
                ->select('leave_types.id', 'type', 'total_days')
                ->distinct('leave_defines.type_id')
                ->where('leave_defines.organization_id',Auth::user()->organization_id)->get();

            //return $leave_type;
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendResponse($leave_type, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    // public function contentType(){

    // 	$content_type='as assignment, st study material, sy sullabus, ot others download';
    // 	return $content_type;
    // }
    public function uploadContent(Request $request)
    {


        $input = $request->all();
        //return $request->input();
        if (ApiBaseMethod::checkUrl($request->fullUrl())) {
            $validator = Validator::make($input, [
                'content_title' => "required",
                'content_type' => "required",
                'upload_date' => "required",
                'description' => "required"


            ]);
        }
        //as assignment, st study material, sy sullabus, ot others download

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
        }
        try {
            if (empty($request->input('available_for'))) {

                if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                    return ApiBaseMethod::sendError('Validation Error.', 'Content Receiver not selected');
                }
            }
            $fileName = "";
            if ($request->file('attach_file') != "") {
                $file = $request->file('attach_file');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/upload_contents/', $fileName);
                $fileName = 'public/uploads/upload_contents/' . $fileName;
            }

            $uploadContents = new TeacherUploadContent();
            $uploadContents->content_title = $request->input('content_title');
            $uploadContents->content_type = $request->input('content_type');

            if ($request->input('available_for') == 'admin') {
                $uploadContents->available_for_admin = 1;
            } elseif ($request->input('available_for') == 'student') {
                if (!empty($request->input('all_classes'))) {
                    $uploadContents->available_for_all_classes = 1;
                } else {
                    $uploadContents->class = $request->input('class');
                    $uploadContents->section = $request->input('section');
                }
            }
            //return $request->input();
            $uploadContents->upload_date = date('Y-m-d', strtotime($request->input('upload_date')));
            $uploadContents->description = $request->input('description');
            $uploadContents->upload_file = $fileName;
            $uploadContents->created_by = $request->input('created_by');
            $uploadContents->academic_id = YearCheck::getAcademicId();
            $results = $uploadContents->save();

            if ($request->input('content_type') == 'as') {
                $purpose = 'assignment';
            } elseif ($request->input('content_type') == 'st') {
                $purpose = 'Study Material';
            } elseif ($request->input('content_type') == 'sy') {
                $purpose = 'Syllabus';
            } elseif ($request->input('content_type') == 'ot') {
                $purpose = 'Others Download';
            }
            // foreach ($request->input('available_for') as $value) {
            if ($request->input('available_for') == 'admin') {
                $roles = Role::where('id', '!=', 1)->where('id', '!=', 2)->where('id', '!=', 3)->where('id', '!=', 9)->where(function ($q) {
                $q->orWhere('type', 'System');
            })->get();

                foreach ($roles as $role) {
                    $employees = Employee::where('role_id', $role->id)->get();
                    foreach ($employees as $employee) {
                        $notification = new Notification;
                        $notification->user_id = $employee->user_id;
                        $notification->role_id = $role->id;
                        $notification->date = date('Y-m-d');
                        $notification->message = $purpose . ' updated';
                        $notification->academic_id = YearCheck::getAcademicId();
                        $notification->save();
                    }
                }
            }
            if ($request->input('available_for') == 'student') {
                if (!empty($request->input('all_classes'))) {
                    $students = Student::select('id')->get();
                    foreach ($students as $student) {
                        $notification = new Notification;
                        $notification->user_id = $student->user_id;
                        $notification->$role_id == 23;
                        $notification->date = date('Y-m-d');
                        $notification->academic_id = YearCheck::getAcademicId();
                        $notification->save();
                    }
                } else {
                    $students = Student::select('id')->where('class_id', $request->input('class'))->where('section_id', $request->input('section'))->get();
                    foreach ($students as $student) {
                        $notification = new Notification;
                        $notification->user_id = $student->user_id;
                        $notification->$role_id == 23;
                        $notification->date = date('Y-m-d');
                        $notification->message = $purpose . ' updated';
                        $notification->academic_id = YearCheck::getAcademicId();
                        $notification->save();
                    }
                }
            }
            // }

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {

                $data = '';

                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function contentList(Request $request)
    {

        try {
            $content_list = DB::table('teacher_upload_contents')
                ->where('available_for_admin', '<>', 0)
                ->get();
            $type = "as assignment, st study material, sy sullabus, ot others download";
            $data = [];
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data['content_list'] = $content_list->toArray();
                $data['type'] = $type;
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
    public function deleteContent(Request $request, $id)
    {
        try {
            $content = DB::table('teacher_upload_contents')->where('id', $id)->delete();
            //$res=User::where('id',$id)->delete();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = '';
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            \Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}

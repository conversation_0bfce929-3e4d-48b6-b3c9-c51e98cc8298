<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ItemReceive extends Model
{
    public function suppliers(){
    	return $this->belongsTo('App\Supplier', 'supplier_id', 'id');
    }

    public function paymentMethodName(){
        return $this->belongsTo('App\PaymentMethhod','payment_method','id');
    }

    public function bankName(){
        return $this->belongsTo('App\BankAccount','account_id','id');
    }

}

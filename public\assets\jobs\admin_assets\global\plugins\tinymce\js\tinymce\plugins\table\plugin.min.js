!function(){"use strict";var e,t,n,r,o,i,u=tinymce.util.Tools.resolve("tinymce.PluginManager"),a=function(e){return function(){return e}},c={noop:function(){},noarg:function(e){return function(){return e()}},compose:function(e,t){return function(){return e(t.apply(null,arguments))}},constant:a,identity:function(e){return e},tripleEquals:function(e,t){return e===t},curry:function(e){for(var t=new Array(arguments.length-1),n=1;n<arguments.length;n++)t[n-1]=arguments[n];return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];var o=t.concat(n);return e.apply(null,o)}},not:function(e){return function(){return!e.apply(null,arguments)}},die:function(e){return function(){throw new Error(e)}},apply:function(e){return e()},call:function(e){e()},never:a(!1),always:a(!0)},l=c.never,s=c.always,f=function(){return d},d=(r={fold:function(e,t){return e()},is:l,isSome:l,isNone:s,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},or:n,orThunk:t,map:f,ap:f,each:function(){},bind:f,flatten:f,exists:l,forall:s,filter:f,equals:e=function(e){return e.isNone()},equals_:e,toArray:function(){return[]},toString:c.constant("none()")},Object.freeze&&Object.freeze(r),r),m=function(e){var t=function(){return e},n=function(){return o},r=function(t){return t(e)},o={fold:function(t,n){return n(e)},is:function(t){return e===t},isSome:s,isNone:l,getOr:t,getOrThunk:t,getOrDie:t,or:n,orThunk:n,map:function(t){return m(t(e))},ap:function(t){return t.fold(f,function(t){return m(t(e))})},each:function(t){t(e)},bind:r,flatten:t,exists:r,forall:r,filter:function(t){return t(e)?o:d},equals:function(t){return t.is(e)},equals_:function(t,n){return t.fold(l,function(t){return n(e,t)})},toArray:function(){return[e]},toString:function(){return"some("+e+")"}};return o},g={some:m,none:f,from:function(e){return null===e||e===undefined?d:m(e)}},p=(o=Array.prototype.indexOf)===undefined?function(e,t){return S(e,t)}:function(e,t){return o.call(e,t)},h=function(e,t){return p(e,t)>-1},v=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o,e)}return r},b=function(e,t){for(var n=0,r=e.length;n<r;n++)t(e[n],n,e)},w=function(e,t){for(var n=e.length-1;n>=0;n--)t(e[n],n,e)},y=function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var i=e[r];t(i,r,e)&&n.push(i)}return n},x=function(e,t){for(var n=0,r=e.length;n<r;n++)if(t(e[n],n,e))return g.some(n);return g.none()},S=function(e,t){for(var n=0,r=e.length;n<r;++n)if(e[n]===t)return n;return-1},C=Array.prototype.push,R=function(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!Array.prototype.isPrototypeOf(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);C.apply(t,e[n])}return t},T=function(e,t){for(var n=0,r=e.length;n<r;++n)if(!0!==t(e[n],n,e))return!1;return!0},D=Array.prototype.slice,A={map:v,each:b,eachr:w,partition:function(e,t){for(var n=[],r=[],o=0,i=e.length;o<i;o++){var u=e[o];(t(u,o,e)?n:r).push(u)}return{pass:n,fail:r}},filter:y,groupBy:function(e,t){if(0===e.length)return[];for(var n=t(e[0]),r=[],o=[],i=0,u=e.length;i<u;i++){var a=e[i],c=t(a);c!==n&&(r.push(o),o=[]),n=c,o.push(a)}return 0!==o.length&&r.push(o),r},indexOf:function(e,t){var n=p(e,t);return-1===n?g.none():g.some(n)},foldr:function(e,t,n){return w(e,function(e){n=t(n,e)}),n},foldl:function(e,t,n){return b(e,function(e){n=t(n,e)}),n},find:function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n,e))return g.some(o)}return g.none()},findIndex:x,flatten:R,bind:function(e,t){var n=v(e,t);return R(n)},forall:T,exists:function(e,t){return x(e,t).isSome()},contains:h,equal:function(e,t){return e.length===t.length&&T(e,function(e,n){return e===t[n]})},reverse:function(e){var t=D.call(e,0);return t.reverse(),t},chunk:function(e,t){for(var n=[],r=0;r<e.length;r+=t){var o=e.slice(r,r+t);n.push(o)}return n},difference:function(e,t){return y(e,function(e){return!h(t,e)})},mapToObject:function(e,t){for(var n={},r=0,o=e.length;r<o;r++){var i=e[r];n[String(i)]=t(i,r)}return n},pure:function(e){return[e]},sort:function(e,t){var n=D.call(e,0);return n.sort(t),n},range:function(e,t){for(var n=[],r=0;r<e;r++)n.push(t(r));return n},head:function(e){return 0===e.length?g.none():g.some(e[0])},last:function(e){return 0===e.length?g.none():g.some(e[e.length-1])}},k=(i=Object.keys)===undefined?function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t}:i,N=function(e,t){for(var n=k(e),r=0,o=n.length;r<o;r++){var i=n[r];t(e[i],i,e)}},O=function(e,t){var n={};return N(e,function(r,o){var i=t(r,o,e);n[i.k]=i.v}),n},E=function(e,t){var n=[];return N(e,function(e,r){n.push(t(e,r))}),n},B=function(e){return E(e,function(e){return e})},P={bifilter:function(e,t){var n={},r={};return N(e,function(e,o){(t(e,o)?n:r)[o]=e}),{t:n,f:r}},each:N,map:function(e,t){return O(e,function(e,n,r){return{k:n,v:t(e,n,r)}})},mapToArray:E,tupleMap:O,find:function(e,t){for(var n=k(e),r=0,o=n.length;r<o;r++){var i=n[r],u=e[i];if(t(u,i,e))return g.some(u)}return g.none()},keys:k,values:B,size:function(e){return B(e).length}},I=function(e){return function(t){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(t)===e}},W={isString:I("string"),isObject:I("object"),isArray:I("array"),isNull:I("null"),isBoolean:I("boolean"),isUndefined:I("undefined"),isFunction:I("function"),isNumber:I("number")},M=function(e){return e.slice(0).sort()},L={sort:M,reqMessage:function(e,t){throw new Error("All required keys ("+M(e).join(", ")+") were not specified. Specified keys were: "+M(t).join(", ")+".")},unsuppMessage:function(e){throw new Error("Unsupported keys for object: "+M(e).join(", "))},validateStrArr:function(e,t){if(!W.isArray(t))throw new Error("The "+e+" fields must be an array. Was: "+t+".");A.each(t,function(t){if(!W.isString(t))throw new Error("The value "+t+" in the "+e+" fields was not a string.")})},invalidTypeMessage:function(e,t){throw new Error("All values need to be of type: "+t+". Keys ("+M(e).join(", ")+") were not.")},checkDupes:function(e){var t=M(e);A.find(t,function(e,n){return n<t.length-1&&e===t[n+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})}},q={immutable:function(){var e=arguments;return function(){for(var t=new Array(arguments.length),n=0;n<t.length;n++)t[n]=arguments[n];if(e.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+e.length+']", got '+t.length+" arguments");var r={};return A.each(e,function(e,n){r[e]=c.constant(t[n])}),r}},immutableBag:function(e,t){var n=e.concat(t);if(0===n.length)throw new Error("You must specify at least one required or optional field.");return L.validateStrArr("required",e),L.validateStrArr("optional",t),L.checkDupes(n),function(r){var o=P.keys(r);A.forall(e,function(e){return A.contains(o,e)})||L.reqMessage(e,o);var i=A.filter(o,function(e){return!A.contains(n,e)});i.length>0&&L.unsuppMessage(i);var u={};return A.each(e,function(e){u[e]=c.constant(r[e])}),A.each(t,function(e){u[e]=c.constant(Object.prototype.hasOwnProperty.call(r,e)?g.some(r[e]):g.none())}),u}}},F=q.immutable("width","height"),z=q.immutable("rows","columns"),j=q.immutable("row","column"),_=q.immutable("x","y"),H=q.immutable("element","rowspan","colspan"),V=q.immutable("element","rowspan","colspan","isNew"),U={dimensions:F,grid:z,address:j,coords:_,extended:q.immutable("element","rowspan","colspan","row","column"),detail:H,detailnew:V,rowdata:q.immutable("element","cells","section"),elementnew:q.immutable("element","isNew"),rowdatanew:q.immutable("element","cells","section","isNew"),rowcells:q.immutable("cells","section"),rowdetails:q.immutable("details","section"),bounds:q.immutable("startRow","startCol","finishRow","finishCol")},G=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:c.constant(e)}},X={fromHtml:function(e,t){var n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||n.childNodes.length>1)throw console.error("HTML does not have a single root node",e),"HTML must have a single root node";return G(n.childNodes[0])},fromTag:function(e,t){var n=(t||document).createElement(e);return G(n)},fromText:function(e,t){var n=(t||document).createTextNode(e);return G(n)},fromDom:G,fromPoint:function(e,t,n){return g.from(e.dom().elementFromPoint(t,n)).map(G)}},Y=8,K=9,$=1,J=3,Q=$,Z=K,ee=function(e){return e.nodeType!==Q&&e.nodeType!==Z||0===e.childElementCount},te={all:function(e,t){var n=t===undefined?document:t.dom();return ee(n)?[]:A.map(n.querySelectorAll(e),X.fromDom)},is:function(e,t){var n=e.dom();if(n.nodeType!==Q)return!1;if(n.matches!==undefined)return n.matches(t);if(n.msMatchesSelector!==undefined)return n.msMatchesSelector(t);if(n.webkitMatchesSelector!==undefined)return n.webkitMatchesSelector(t);if(n.mozMatchesSelector!==undefined)return n.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},one:function(e,t){var n=t===undefined?document:t.dom();return ee(n)?g.none():g.from(n.querySelector(e)).map(X.fromDom)}},ne=function(e,t){for(var n=[],r=function(e){return n.push(e),t(e)},o=t(e);(o=o.bind(r)).isSome(););return n},re="undefined"!=typeof window?window:Function("return this;")(),oe=function(e,t){for(var n=t!==undefined&&null!==t?t:re,r=0;r<e.length&&n!==undefined&&null!==n;++r)n=n[e[r]];return n},ie=function(e,t){var n=e.split(".");return oe(n,t)},ue=function(e,t){var n=ie(e,t);if(n===undefined||null===n)throw e+" not available on this browser";return n},ae=function(){return ue("Node")},ce=function(e,t,n){return 0!=(e.compareDocumentPosition(t)&n)},le=function(e,t){return ce(e,t,ae().DOCUMENT_POSITION_CONTAINED_BY)},se=function(e){var t,n=!1;return function(){return n||(n=!0,t=e.apply(null,arguments)),t}},fe=function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.test(t))return r}return undefined}(e,t);if(!n)return{major:0,minor:0};var r=function(e){return Number(t.replace(n,"$"+e))};return me(r(1),r(2))},de=function(){return me(0,0)},me=function(e,t){return{major:e,minor:t}},ge={nu:me,detect:function(e,t){var n=String(t).toLowerCase();return 0===e.length?de():fe(e,n)},unknown:de},pe="Firefox",he=function(e,t){return function(){return t===e}},ve=function(e){var t=e.current;return{current:t,version:e.version,isEdge:he("Edge",t),isChrome:he("Chrome",t),isIE:he("IE",t),isOpera:he("Opera",t),isFirefox:he(pe,t),isSafari:he("Safari",t)}},be={unknown:function(){return ve({current:undefined,version:ge.unknown()})},nu:ve,edge:c.constant("Edge"),chrome:c.constant("Chrome"),ie:c.constant("IE"),opera:c.constant("Opera"),firefox:c.constant(pe),safari:c.constant("Safari")},we="Windows",ye="Android",xe="Solaris",Se="FreeBSD",Ce=function(e,t){return function(){return t===e}},Re=function(e){var t=e.current;return{current:t,version:e.version,isWindows:Ce(we,t),isiOS:Ce("iOS",t),isAndroid:Ce(ye,t),isOSX:Ce("OSX",t),isLinux:Ce("Linux",t),isSolaris:Ce(xe,t),isFreeBSD:Ce(Se,t)}},Te={unknown:function(){return Re({current:undefined,version:ge.unknown()})},nu:Re,windows:c.constant(we),ios:c.constant("iOS"),android:c.constant(ye),linux:c.constant("Linux"),osx:c.constant("OSX"),solaris:c.constant(xe),freebsd:c.constant(Se)},De=function(e,t){var n=String(t).toLowerCase();return A.find(e,function(e){return e.search(n)})},Ae=function(e,t){return De(e,t).map(function(e){var n=ge.detect(e.versionRegexes,t);return{current:e.name,version:n}})},ke=function(e,t){return De(e,t).map(function(e){var n=ge.detect(e.versionRegexes,t);return{current:e.name,version:n}})},Ne=function(e,t){return t+e},Oe=function(e,t){return e+t},Ee=function(e,t){return e.substring(t)},Be=function(e,t){return e.substring(0,e.length-t)},Pe=function(e){return""===e?g.none():g.some(e.substr(0,1))},Ie=function(e){return""===e?g.none():g.some(e.substring(1))},We=function(e,t,n){return""===t||!(e.length<t.length)&&e.substr(n,n+t.length)===t},Me=function(e,t){return We(e,t,0)},Le=function(e,t){return We(e,t,e.length-t.length)},qe={supplant:function(e,t){return e.replace(/\${([^{}]*)}/g,function(e,n){var r,o=t[n];return"string"==(r=typeof o)||"number"===r?o:e})},startsWith:Me,removeLeading:function(e,t){return Me(e,t)?Ee(e,t.length):e},removeTrailing:function(e,t){return Le(e,t)?Be(e,t.length):e},ensureLeading:function(e,t){return Me(e,t)?e:Ne(e,t)},ensureTrailing:function(e,t){return Le(e,t)?e:Oe(e,t)},endsWith:Le,contains:function(e,t){return-1!==e.indexOf(t)},trim:function(e){return e.replace(/^\s+|\s+$/g,"")},lTrim:function(e){return e.replace(/^\s+/g,"")},rTrim:function(e){return e.replace(/\s+$/g,"")},capitalize:function(e){return Pe(e).bind(function(t){return Ie(e).map(function(e){return t.toUpperCase()+e})}).getOr(e)}},Fe=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ze=function(e){return function(t){return qe.contains(t,e)}},je=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return qe.contains(e,"edge/")&&qe.contains(e,"chrome")&&qe.contains(e,"safari")&&qe.contains(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Fe],search:function(e){return qe.contains(e,"chrome")&&!qe.contains(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return qe.contains(e,"msie")||qe.contains(e,"trident")}},{name:"Opera",versionRegexes:[Fe,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ze("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ze("firefox")},{name:"Safari",versionRegexes:[Fe,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(qe.contains(e,"safari")||qe.contains(e,"mobile/"))&&qe.contains(e,"applewebkit")}}],_e=[{name:"Windows",search:ze("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return qe.contains(e,"iphone")||qe.contains(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ze("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:ze("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ze("linux"),versionRegexes:[]},{name:"Solaris",search:ze("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ze("freebsd"),versionRegexes:[]}],He={browsers:c.constant(je),oses:c.constant(_e)},Ve=function(e){var t,n,r,o,i,u,a,l,s,f,d,m=He.browsers(),g=He.oses(),p=Ae(m,e).fold(be.unknown,be.nu),h=ke(g,e).fold(Te.unknown,Te.nu);return{browser:p,os:h,deviceType:(n=p,r=e,o=(t=h).isiOS()&&!0===/ipad/i.test(r),i=t.isiOS()&&!o,u=t.isAndroid()&&3===t.version.major,a=t.isAndroid()&&4===t.version.major,l=o||u||a&&!0===/mobile/i.test(r),s=t.isiOS()||t.isAndroid(),f=s&&!l,d=n.isSafari()&&t.isiOS()&&!1===/safari/i.test(r),{isiPad:c.constant(o),isiPhone:c.constant(i),isTablet:c.constant(l),isPhone:c.constant(f),isTouch:c.constant(s),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:c.constant(d)})}},Ue={detect:se(function(){var e=navigator.userAgent;return Ve(e)})},Ge=function(e,t){return e.dom()===t.dom()},Xe=Ue.detect().browser.isIE()?function(e,t){return le(e.dom(),t.dom())}:function(e,t){var n=e.dom(),r=t.dom();return n!==r&&n.contains(r)},Ye={eq:Ge,isEqualNode:function(e,t){return e.dom().isEqualNode(t.dom())},member:function(e,t){return A.exists(t,c.curry(Ge,e))},contains:Xe,is:te.is},Ke=function(e){return X.fromDom(e.dom().ownerDocument)},$e=function(e){var t=e.dom();return g.from(t.parentNode).map(X.fromDom)},Je=function(e){var t=e.dom();return g.from(t.previousSibling).map(X.fromDom)},Qe=function(e){var t=e.dom();return g.from(t.nextSibling).map(X.fromDom)},Ze=function(e){var t=e.dom();return A.map(t.childNodes,X.fromDom)},et=function(e,t){var n=e.dom().childNodes;return g.from(n[t]).map(X.fromDom)},tt=q.immutable("element","offset"),nt={owner:Ke,defaultView:function(e){var t=e.dom().ownerDocument.defaultView;return X.fromDom(t)},documentElement:function(e){var t=Ke(e);return X.fromDom(t.dom().documentElement)},parent:$e,findIndex:function(e){return $e(e).bind(function(t){var n=Ze(t);return A.findIndex(n,function(t){return Ye.eq(e,t)})})},parents:function(e,t){for(var n=W.isFunction(t)?t:c.constant(!1),r=e.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=X.fromDom(i);if(o.push(u),!0===n(u))break;r=i}return o},siblings:function(e){return $e(e).map(Ze).map(function(t){return A.filter(t,function(t){return!Ye.eq(e,t)})}).getOr([])},prevSibling:Je,offsetParent:function(e){var t=e.dom();return g.from(t.offsetParent).map(X.fromDom)},prevSiblings:function(e){return A.reverse(ne(e,Je))},nextSibling:Qe,nextSiblings:function(e){return ne(e,Qe)},children:Ze,child:et,firstChild:function(e){return et(e,0)},lastChild:function(e){return et(e,e.dom().childNodes.length-1)},childNodesCount:function(e){return e.dom().childNodes.length},hasChildNodes:function(e){return e.dom().hasChildNodes()},leaf:function(e,t){var n=Ze(e);return n.length>0&&t<n.length?tt(n[t],0):tt(e,t)}},rt=function(e,t,n){return A.bind(nt.children(e),function(e){return te.is(e,t)?n(e)?[e]:[]:rt(e,t,n)})},ot={firstLayer:function(e,t){return rt(e,t,c.constant(!0))},filterFirstLayer:rt},it=function(e){return e.dom().nodeName.toLowerCase()},ut=function(e){return e.dom().nodeType},at=function(e){return function(t){return ut(t)===e}},ct=at($),lt=at(J),st=at(K),ft={name:it,type:ut,value:function(e){return e.dom().nodeValue},isElement:ct,isText:lt,isDocument:st,isComment:function(e){return ut(e)===Y||"#comment"===it(e)}},dt=function(e,t,n){if(!(W.isString(n)||W.isBoolean(n)||W.isNumber(n)))throw console.error("Invalid call to Attr.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},mt=function(e,t,n){dt(e.dom(),t,n)},gt=function(e,t){var n=e.dom().getAttribute(t);return null===n?undefined:n},pt=function(e,t){var n=e.dom();return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},ht={clone:function(e){return A.foldl(e.dom().attributes,function(e,t){return e[t.name]=t.value,e},{})},set:mt,setAll:function(e,t){var n=e.dom();P.each(t,function(e,t){dt(n,t,e)})},get:gt,has:pt,remove:function(e,t){e.dom().removeAttribute(t)},hasNone:function(e){var t=e.dom().attributes;return t===undefined||null===t||0===t.length},transfer:function(e,t,n){ft.isElement(e)&&ft.isElement(t)&&A.each(n,function(n){var r,o,i;o=t,pt(r=e,i=n)&&!pt(o,i)&&mt(o,i,gt(r,i))})}},vt=se(function(){return bt(X.fromDom(document))}),bt=function(e){var t=e.dom().body;if(null===t||t===undefined)throw"Body is not available yet";return X.fromDom(t)},wt={body:vt,getBody:bt,inBody:function(e){var t=ft.isText(e)?e.dom().parentNode:e.dom();return t!==undefined&&null!==t&&t.ownerDocument.body.contains(t)}},yt=function(e,t){var n=[];return A.each(nt.children(e),function(e){t(e)&&(n=n.concat([e])),n=n.concat(yt(e,t))}),n},xt={all:function(e){return yt(wt.body(),e)},ancestors:function(e,t,n){return A.filter(nt.parents(e,n),t)},siblings:function(e,t){return A.filter(nt.siblings(e),t)},children:function(e,t){return A.filter(nt.children(e),t)},descendants:yt},St={all:function(e){return te.all(e)},ancestors:function(e,t,n){return xt.ancestors(e,function(e){return te.is(e,t)},n)},siblings:function(e,t){return xt.siblings(e,function(e){return te.is(e,t)})},children:function(e,t){return xt.children(e,function(e){return te.is(e,t)})},descendants:function(e,t){return te.all(t,e)}};function Ct(e,t,n,r,o){return e(n,r)?g.some(n):W.isFunction(o)&&o(n)?g.none():t(n,r,o)}var Rt,Tt,Dt,At,kt,Nt=function(e,t,n){for(var r=e.dom(),o=W.isFunction(n)?n:c.constant(!1);r.parentNode;){r=r.parentNode;var i=X.fromDom(r);if(t(i))return g.some(i);if(o(i))break}return g.none()},Ot=function(e,t){return A.find(e.dom().childNodes,c.compose(t,X.fromDom)).map(X.fromDom)},Et=function(e,t){var n=function(e){for(var r=0;r<e.childNodes.length;r++){if(t(X.fromDom(e.childNodes[r])))return g.some(X.fromDom(e.childNodes[r]));var o=n(e.childNodes[r]);if(o.isSome())return o}return g.none()};return n(e.dom())},Bt={first:function(e){return Et(wt.body(),e)},ancestor:Nt,closest:function(e,t,n){return Ct(function(e){return t(e)},Nt,e,t,n)},sibling:function(e,t){var n=e.dom();return n.parentNode?Ot(X.fromDom(n.parentNode),function(n){return!Ye.eq(e,n)&&t(n)}):g.none()},child:Ot,descendant:Et},Pt=function(e,t,n){return Bt.ancestor(e,function(e){return te.is(e,t)},n)},It={first:function(e){return te.one(e)},ancestor:Pt,sibling:function(e,t){return Bt.sibling(e,function(e){return te.is(e,t)})},child:function(e,t){return Bt.child(e,function(e){return te.is(e,t)})},descendant:function(e,t){return te.one(t,e)},closest:function(e,t,n){return Ct(te.is,Pt,e,t,n)}},Wt=function(e,t,n){var r=n!==undefined?n:c.constant(!1);return r(t)?g.none():A.contains(e,ft.name(t))?g.some(t):It.ancestor(t,e.join(","),function(e){return te.is(e,"table")||r(e)})},Mt=function(e,t){return nt.parent(t).map(function(t){return St.children(t,e)})},Lt=c.curry(Mt,"th,td"),qt=c.curry(Mt,"tr"),Ft=function(e,t){return parseInt(ht.get(e,t),10)},zt={cell:function(e,t){return Wt(["td","th"],e,t)},firstCell:function(e){return It.descendant(e,"th,td")},cells:function(e){return ot.firstLayer(e,"th,td")},neighbourCells:Lt,table:function(e,t){return It.closest(e,"table",t)},row:function(e,t){return Wt(["tr"],e,t)},rows:function(e){return ot.firstLayer(e,"tr")},notCell:function(e,t){return Wt(["caption","tr","tbody","tfoot","thead"],e,t)},neighbourRows:qt,attr:Ft,grid:function(e,t,n){var r=Ft(e,t),o=Ft(e,n);return U.grid(r,o)}},jt=function(e){var t=zt.rows(e);return A.map(t,function(e){var t=e,n=nt.parent(t).bind(function(e){var t=ft.name(e);return"tfoot"===t||"thead"===t||"tbody"===t?t:"tbody"}),r=A.map(zt.cells(e),function(e){var t=ht.has(e,"rowspan")?parseInt(ht.get(e,"rowspan"),10):1,n=ht.has(e,"colspan")?parseInt(ht.get(e,"colspan"),10):1;return U.detail(e,t,n)});return U.rowdata(t,r,n)})},_t=function(e,t){return A.map(e,function(e){var n=A.map(zt.cells(e),function(e){var t=ht.has(e,"rowspan")?parseInt(ht.get(e,"rowspan"),10):1,n=ht.has(e,"colspan")?parseInt(ht.get(e,"colspan"),10):1;return U.detail(e,t,n)});return U.rowdata(e,n,t.section())})},Ht=function(e,t){return e+","+t},Vt=function(e,t){var n=A.bind(e.all(),function(e){return e.cells()});return A.filter(n,t)},Ut={generate:function(e){var t={},n=[],r=e.length,o=0;A.each(e,function(e,r){var i=[];A.each(e.cells(),function(e,n){for(var u=0;t[Ht(r,u)]!==undefined;)u++;for(var a=U.extended(e.element(),e.rowspan(),e.colspan(),r,u),c=0;c<e.colspan();c++)for(var l=0;l<e.rowspan();l++){var s=u+c,f=Ht(r+l,s);t[f]=a,o=Math.max(o,s+1)}i.push(a)}),n.push(U.rowdata(e.element(),i,e.section()))});var i=U.grid(r,o);return{grid:c.constant(i),access:c.constant(t),all:c.constant(n)}},getAt:function(e,t,n){var r=e.access()[Ht(t,n)];return r!==undefined?g.some(r):g.none()},findItem:function(e,t,n){var r=Vt(e,function(e){return n(t,e.element())});return r.length>0?g.some(r[0]):g.none()},filterItems:Vt,justCells:function(e){var t=A.map(e.all(),function(e){return e.cells()});return A.flatten(t)}},Gt={isSupported:function(e){return e.style!==undefined}},Xt=function(e,t,n){if(!W.isString(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);Gt.isSupported(e)&&e.style.setProperty(t,n)},Yt=function(e,t){Gt.isSupported(e)&&e.style.removeProperty(t)},Kt=function(e,t,n){var r=e.dom();Xt(r,t,n)},$t=function(e,t){return Gt.isSupported(e)?e.style.getPropertyValue(t):""},Jt=function(e,t){var n=e.dom(),r=$t(n,t);return g.from(r).filter(function(e){return e.length>0})},Qt={copy:function(e,t){var n=e.dom(),r=t.dom();Gt.isSupported(n)&&Gt.isSupported(r)&&(r.style.cssText=n.style.cssText)},set:Kt,preserve:function(e,t){var n=ht.get(e,"style"),r=t(e);return(n===undefined?ht.remove:ht.set)(e,"style",n),r},setAll:function(e,t){var n=e.dom();P.each(t,function(e,t){Xt(n,t,e)})},setOptions:function(e,t){var n=e.dom();P.each(t,function(e,t){e.fold(function(){Yt(n,t)},function(e){Xt(n,t,e)})})},remove:function(e,t){var n=e.dom();Yt(n,t),ht.has(e,"style")&&""===qe.trim(ht.get(e,"style"))&&ht.remove(e,"style")},get:function(e,t){var n=e.dom(),r=window.getComputedStyle(n).getPropertyValue(t),o=""!==r||wt.inBody(e)?r:$t(n,t);return null===o?undefined:o},getRaw:Jt,getAllRaw:function(e){var t={},n=e.dom();if(Gt.isSupported(n))for(var r=0;r<n.style.length;r++){var o=n.style.item(r);t[o]=n.style[o]}return t},isValidValue:function(e,t,n){var r=X.fromTag(e);return Kt(r,t,n),Jt(r,t).isSome()},reflow:function(e){return e.dom().offsetWidth},transfer:function(e,t,n){ft.isElement(e)&&ft.isElement(t)&&A.each(n,function(n){var r,o;r=t,Jt(e,o=n).each(function(e){Jt(r,o).isNone()&&Kt(r,o,e)})})}},Zt=function(e,t){nt.parent(e).each(function(n){n.dom().insertBefore(t.dom(),e.dom())})},en=function(e,t){e.dom().appendChild(t.dom())},tn={before:Zt,after:function(e,t){nt.nextSibling(e).fold(function(){nt.parent(e).each(function(e){en(e,t)})},function(e){Zt(e,t)})},prepend:function(e,t){nt.firstChild(e).fold(function(){en(e,t)},function(n){e.dom().insertBefore(t.dom(),n.dom())})},append:en,appendAt:function(e,t,n){nt.child(e,n).fold(function(){en(e,t)},function(e){Zt(e,t)})},wrap:function(e,t){Zt(e,t),en(t,e)}},nn={before:function(e,t){A.each(t,function(t){tn.before(e,t)})},after:function(e,t){A.each(t,function(n,r){var o=0===r?e:t[r-1];tn.after(o,n)})},prepend:function(e,t){A.each(t.slice().reverse(),function(t){tn.prepend(e,t)})},append:function(e,t){A.each(t,function(t){tn.append(e,t)})}},rn=function(e){var t=e.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},on={empty:function(e){e.dom().textContent="",A.each(nt.children(e),function(e){rn(e)})},remove:rn,unwrap:function(e){var t=nt.children(e);t.length>0&&nn.before(e,t),rn(e)}},un=q.immutable("minRow","minCol","maxRow","maxCol"),an=function(e,t){var n,r,o,i,u,a,c,l,s,f,d=function(e){return te.is(e.element(),t)},m=jt(e),g=Ut.generate(m),p=(r=d,o=(n=g).grid().columns(),i=n.grid().rows(),u=o,a=0,c=0,P.each(n.access(),function(e){if(r(e)){var t=e.row(),n=t+e.rowspan()-1,o=e.column(),l=o+e.colspan()-1;t<i?i=t:n>a&&(a=n),o<u?u=o:l>c&&(c=l)}}),un(i,u,a,c)),h="th:not("+t+"),td:not("+t+")",v=ot.filterFirstLayer(e,"th,td",function(e){return te.is(e,h)});return A.each(v,on.remove),function(e,t,n,r){for(var o,i,u,a=t.grid().columns(),c=t.grid().rows(),l=0;l<c;l++)for(var s=!1,f=0;f<a;f++)l<n.minRow()||l>n.maxRow()||f<n.minCol()||f>n.maxCol()||(Ut.getAt(t,l,f).filter(r).isNone()?(o=s,i=e[l].element(),u=X.fromTag("td"),tn.append(u,X.fromTag("br")),(o?tn.append:tn.prepend)(i,u)):s=!0)}(m,g,p,d),l=e,s=p,f=A.filter(ot.firstLayer(l,"tr"),function(e){return 0===e.dom().childElementCount}),A.each(f,on.remove),s.minCol()!==s.maxCol()&&s.minRow()!==s.maxRow()||A.each(ot.firstLayer(l,"th,td"),function(e){ht.remove(e,"rowspan"),ht.remove(e,"colspan")}),ht.remove(l,"width"),ht.remove(l,"height"),Qt.remove(l,"width"),Qt.remove(l,"height"),e},cn=function(e,t){return X.fromDom(e.dom().cloneNode(t))},ln=function(e){return cn(e,!0)},sn=function(e,t){var n=X.fromTag(t),r=ht.clone(e);return ht.setAll(n,r),n},fn=function(e){return cn(e,!1)},dn=ln,mn=function(e,t){var n=sn(e,t),r=nt.children(ln(e));return nn.append(n,r),n},gn=(Rt=ft.isText,Tt="text",Dt=function(e){return Rt(e)?g.from(e.dom().nodeValue):g.none()},At=Ue.detect().browser,{get:function(e){if(!Rt(e))throw new Error("Can only get "+Tt+" value of a "+Tt+" node");return kt(e).getOr("")},getOption:kt=At.isIE()&&10===At.version.major?function(e){try{return Dt(e)}catch(t){return g.none()}}:Dt,set:function(e,t){if(!Rt(e))throw new Error("Can only set raw "+Tt+" value of a "+Tt+" node");e.dom().nodeValue=t}}),pn={get:function(e){return gn.get(e)},getOption:function(e){return gn.getOption(e)},set:function(e,t){gn.set(e,t)}},hn=function(e){return"img"===ft.name(e)?1:pn.getOption(e).fold(function(){return nt.children(e).length},function(e){return e.length})},vn=["img","br"],bn=hn,wn=function(e){var t;return t=e,pn.getOption(t).filter(function(e){return 0!==e.trim().length||e.indexOf("\xa0")>-1}).isSome()||A.contains(vn,ft.name(e))},yn=function(e,t){var n=function(e){for(var r=nt.children(e),o=r.length-1;o>=0;o--){var i=r[o];if(t(i))return g.some(i);var u=n(i);if(u.isSome())return u}return g.none()};return n(e)},xn={first:function(e){return Bt.descendant(e,wn)},last:function(e){return yn(e,wn)}},Sn=function(){var e=X.fromTag("td");return tn.append(e,X.fromTag("br")),e},Cn=function(e,t,n){var r=mn(e,t);return P.each(n,function(e,t){null===e?ht.remove(r,t):ht.set(r,t,e)}),r},Rn=function(e){return e},Tn=function(e){return function(){return X.fromTag("tr",e.dom())}},Dn={cellOperations:function(e,t,n){return{row:Tn(t),cell:function(t){var r,o,i,u=nt.owner(t.element()),a=X.fromTag(ft.name(t.element()),u.dom()),c=n.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),l=c.length>0?(r=t.element(),o=a,i=c,xn.first(r).map(function(e){var t=i.join(","),n=St.ancestors(e,t,function(e){return Ye.eq(e,r)});return A.foldr(n,function(e,t){var n=fn(t);return tn.append(e,n),n},o)}).getOr(o)):a;return tn.append(l,X.fromTag("br")),Qt.copy(t.element(),a),Qt.remove(a,"height"),1!==t.colspan()&&Qt.remove(t.element(),"width"),e(t.element(),a),a},replace:Cn,gap:Sn}},paste:function(e){return{row:Tn(e),cell:Sn,replace:Rn,gap:Sn}}},An=function(e,t){var n=(t||document).createElement("div");return n.innerHTML=e,nt.children(X.fromDom(n))},kn=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function Nn(){return{up:c.constant({selector:It.ancestor,closest:It.closest,predicate:Bt.ancestor,all:nt.parents}),down:c.constant({selector:St.descendants,predicate:xt.descendants}),styles:c.constant({get:Qt.get,getRaw:Qt.getRaw,set:Qt.set,remove:Qt.remove}),attrs:c.constant({get:ht.get,set:ht.set,remove:ht.remove,copyTo:function(e,t){var n=ht.clone(e);ht.setAll(t,n)}}),insert:c.constant({before:tn.before,after:tn.after,afterAll:nn.after,append:tn.append,appendAll:nn.append,prepend:tn.prepend,wrap:tn.wrap}),remove:c.constant({unwrap:on.unwrap,remove:on.remove}),create:c.constant({nu:X.fromTag,clone:function(e){return X.fromDom(e.dom().cloneNode(!1))},text:X.fromText}),query:c.constant({comparePosition:function(e,t){return e.dom().compareDocumentPosition(t.dom())},prevSibling:nt.prevSibling,nextSibling:nt.nextSibling}),property:c.constant({children:nt.children,name:ft.name,parent:nt.parent,isText:ft.isText,isComment:ft.isComment,isElement:ft.isElement,getText:pn.get,setText:pn.set,isBoundary:function(e){return!!ft.isElement(e)&&("body"===ft.name(e)||A.contains(kn,ft.name(e)))},isEmptyTag:function(e){return!!ft.isElement(e)&&A.contains(["br","img","hr","input"],ft.name(e))}}),eq:Ye.eq,is:Ye.is}}q.immutable("left","right");var On=function(e,t,n,r){var o=t(e,n);return A.foldr(r,function(n,r){var o=t(e,r);return En(e,n,o)},o)},En=function(e,t,n){return t.bind(function(t){return n.filter(c.curry(e.eq,t))})},Bn=function(e,t,n){return n.length>0?On(e,t,(r=n)[0],r.slice(1)):g.none();var r},Pn=function(e,t){return c.curry(e.eq,t)},In=function(e,t,n,r){var o=r!==undefined?r:c.constant(!1),i=[t].concat(e.up().all(t)),u=[n].concat(e.up().all(n)),a=function(e){return A.findIndex(e,o).fold(function(){return e},function(t){return e.slice(0,t+1)})},l=a(i),s=a(u),f=A.find(l,function(t){return A.exists(s,Pn(e,t))});return{firstpath:c.constant(l),secondpath:c.constant(s),shared:c.constant(f)}},Wn=In,Mn=function(e,t,n){return Bn(e,t,n)},Ln=function(e,t,n,r){return Wn(e,t,n,r)},qn=Nn(),Fn=function(e,t){return Mn(qn,function(t,n){return e(n)},t)},zn=function(e,t,n){return Ln(qn,e,t,n)},jn=function(e,t){return t.column()>=e.startCol()&&t.column()+t.colspan()-1<=e.finishCol()&&t.row()>=e.startRow()&&t.row()+t.rowspan()-1<=e.finishRow()},_n=function(e,t){var n=t.column(),r=t.column()+t.colspan()-1,o=t.row(),i=t.row()+t.rowspan()-1;return n<=e.finishCol()&&r>=e.startCol()&&o<=e.finishRow()&&i>=e.startRow()},Hn=function(e,t){for(var n=!0,r=c.curry(jn,t),o=t.startRow();o<=t.finishRow();o++)for(var i=t.startCol();i<=t.finishCol();i++)n=n&&Ut.getAt(e,o,i).exists(r);return n?g.some(t):g.none()},Vn=function(e,t,n){var r=Ut.findItem(e,t,Ye.eq),o=Ut.findItem(e,n,Ye.eq);return r.bind(function(e){return o.map(function(t){return n=e,r=t,U.bounds(Math.min(n.row(),r.row()),Math.min(n.column(),r.column()),Math.max(n.row()+n.rowspan()-1,r.row()+r.rowspan()-1),Math.max(n.column()+n.colspan()-1,r.column()+r.colspan()-1));var n,r})})},Un=Vn,Gn=function(e,t,n){return Vn(e,t,n).bind(function(t){return Hn(e,t)})},Xn=function(e,t,n,r){return Ut.findItem(e,t,Ye.eq).bind(function(t){var o=n>0?t.row()+t.rowspan()-1:t.row(),i=r>0?t.column()+t.colspan()-1:t.column();return Ut.getAt(e,o+n,i+r).map(function(e){return e.element()})})},Yn=function(e,t,n){return Un(e,t,n).map(function(t){var n=Ut.filterItems(e,c.curry(_n,t));return A.map(n,function(e){return e.element()})})},Kn=function(e,t){return Ut.findItem(e,t,function(e,t){return Ye.contains(t,e)}).bind(function(e){return e.element()})},$n=function(e){var t=jt(e);return Ut.generate(t)},Jn=function(e,t,n){return zt.table(e).bind(function(r){var o=$n(r);return Xn(o,e,t,n)})},Qn=function(e,t,n){var r=$n(e);return Yn(r,t,n)},Zn=function(e,t,n,r,o){var i=$n(e),u=Ye.eq(e,n)?t:Kn(i,t),a=Ye.eq(e,o)?r:Kn(i,r);return Yn(i,u,a)},er=function(e,t,n){var r=$n(e);return Gn(r,t,n)},tr=function(e,t){return It.ancestor(e,"table")},nr=q.immutableBag(["boxes","start","finish"],[]),rr=function(e,t,n){var r=function(e){return function(t){return n(t)||Ye.eq(t,e)}};return Ye.eq(e,t)?g.some(nr({boxes:g.some([e]),start:e,finish:t})):tr(e).bind(function(o){return tr(t).bind(function(i){if(Ye.eq(o,i))return g.some(nr({boxes:Qn(o,e,t),start:e,finish:t}));if(Ye.contains(o,i)){var u=(a=St.ancestors(t,"td,th",r(o))).length>0?a[a.length-1]:t;return g.some(nr({boxes:Zn(o,e,o,t,i),start:e,finish:u}))}if(Ye.contains(i,o)){var a,c=(a=St.ancestors(e,"td,th",r(i))).length>0?a[a.length-1]:e;return g.some(nr({boxes:Zn(i,e,o,t,i),start:e,finish:c}))}return zn(e,t).shared().bind(function(u){return It.closest(u,"table",n).bind(function(n){var u=St.ancestors(t,"td,th",r(n)),a=u.length>0?u[u.length-1]:t,c=St.ancestors(e,"td,th",r(n)),l=c.length>0?c[c.length-1]:e;return g.some(nr({boxes:Zn(n,e,o,t,i),start:l,finish:a}))})})})})},or={identify:rr,retrieve:function(e,t){var n=St.descendants(e,t);return n.length>0?g.some(n):g.none()},shiftSelection:function(e,t,n,r,o){return(i=e,u=o,A.find(i,function(e){return te.is(e,u)})).bind(function(e){return Jn(e,t,n).bind(function(e){return t=e,n=r,It.ancestor(t,"table").bind(function(e){return It.descendant(e,n).bind(function(e){return rr(e,t).bind(function(e){return e.boxes().map(function(t){return{boxes:c.constant(t),start:c.constant(e.start()),finish:c.constant(e.finish())}})})})});var t,n})});var i,u},getEdges:function(e,t,n){return It.descendant(e,t).bind(function(t){return It.descendant(e,n).bind(function(e){return Fn(tr,[t,e]).map(function(n){return{first:c.constant(t),last:c.constant(e),table:c.constant(n)}})})})}},ir={retrieve:function(e,t){return or.retrieve(e,t)},retrieveBox:function(e,t,n){return or.getEdges(e,t,n).bind(function(t){var n=function(t){return Ye.eq(e,t)},r=It.ancestor(t.first(),"thead,tfoot,tbody,table",n),o=It.ancestor(t.last(),"thead,tfoot,tbody,table",n);return r.bind(function(e){return o.bind(function(n){return Ye.eq(e,n)?er(t.table(),t.first(),t.last()):g.none()})})})}},ur="data-mce-selected",ar="data-mce-first-selected",cr="data-mce-last-selected",lr={selected:c.constant(ur),selectedSelector:c.constant("td[data-mce-selected],th[data-mce-selected]"),attributeSelector:c.constant("[data-mce-selected]"),firstSelected:c.constant(ar),firstSelectedSelector:c.constant("td[data-mce-first-selected],th[data-mce-first-selected]"),lastSelected:c.constant(cr),lastSelectedSelector:c.constant("td[data-mce-last-selected],th[data-mce-last-selected]")},sr=function(e){if(!W.isArray(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");var t=[],n={};return A.each(e,function(r,o){var i=P.keys(r);if(1!==i.length)throw new Error("one and only one name per case");var u=i[0],a=r[u];if(n[u]!==undefined)throw new Error("duplicate key detected:"+u);if("cata"===u)throw new Error("cannot have a case named cata (sorry)");if(!W.isArray(a))throw new Error("case arguments must be an array");t.push(u),n[u]=function(){var n=arguments.length;if(n!==a.length)throw new Error("Wrong number of arguments to case "+u+". Expected "+a.length+" ("+a+"), got "+n);for(var r=new Array(n),i=0;i<r.length;i++)r[i]=arguments[i];return{fold:function(){if(arguments.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+arguments.length);return arguments[o].apply(null,r)},match:function(e){var n=P.keys(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!A.forall(t,function(e){return A.contains(n,e)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[u].apply(null,r)},log:function(e){console.log(e,{constructors:t,constructor:u,params:r})}}}}),n},fr=sr([{none:[]},{multiple:["elements"]},{single:["selection"]}]),dr={cata:function(e,t,n,r){return e.fold(t,n,r)},none:fr.none,multiple:fr.multiple,single:fr.single},mr=function(e,t){return dr.cata(t.get(),c.constant([]),c.identity,c.constant([e]))},gr=function(e,t){return dr.cata(t.get(),g.none,function(t,n){return 0===t.length?g.none():ir.retrieveBox(e,lr.firstSelectedSelector(),lr.lastSelectedSelector()).bind(function(e){return t.length>1?g.some({bounds:c.constant(e),cells:c.constant(t)}):g.none()})},g.none)},pr=function(e,t){var n=mr(e,t);return n.length>0&&A.forall(n,function(e){return ht.has(e,"rowspan")&&parseInt(ht.get(e,"rowspan"),10)>1||ht.has(e,"colspan")&&parseInt(ht.get(e,"colspan"),10)>1})?g.some(n):g.none()},hr=mr,vr=function(e){return{element:c.constant(e),mergable:g.none,unmergable:g.none,selection:c.constant([e])}},br=q.immutable("element","clipboard","generators"),wr={noMenu:vr,forMenu:function(e,t,n){return{element:c.constant(n),mergable:c.constant(gr(t,e)),unmergable:c.constant(pr(n,e)),selection:c.constant(hr(n,e))}},notCell:function(e){return vr(e)},paste:br,pasteRows:function(e,t,n,r,o){return{element:c.constant(n),mergable:g.none,unmergable:g.none,selection:c.constant(hr(n,e)),clipboard:c.constant(r),generators:c.constant(o)}}},yr={registerEvents:function(e,t,n,r){e.on("BeforeGetContent",function(n){!0===n.selection&&dr.cata(t.get(),c.noop,function(t){var r;n.preventDefault(),(r=t,zt.table(r[0]).map(dn).map(function(e){return[an(e,lr.attributeSelector())]})).each(function(t){n.content=A.map(t,function(t){return n=t,e.selection.serializer.serialize(n.dom(),{});var n}).join("")})},c.noop)}),e.on("BeforeSetContent",function(t){!0===t.selection&&!0===t.paste&&g.from(e.dom.getParent(e.selection.getStart(),"th,td")).each(function(o){var i=X.fromDom(o);zt.table(i).bind(function(o){var u=A.filter(An(t.content),function(e){return"meta"!==ft.name(e)});if(1===u.length&&"table"===ft.name(u[0])){t.preventDefault();var a=X.fromDom(e.getDoc()),c=Dn.paste(a),l=wr.paste(i,u[0],c);n.pasteCells(o,l).each(function(t){e.selection.setRng(t),e.focus(),r.clear(o)})}})})})}};function xr(e,t){var n=function(n){var r=t(n);if(r<=0||null===r){var o=Qt.get(n,e);return parseFloat(o)||0}return r},r=function(e,t){return A.foldl(t,function(t,n){var r=Qt.get(e,n),o=r===undefined?0:parseInt(r,10);return isNaN(o)?t:t+o},0)};return{set:function(t,n){if(!W.isNumber(n)&&!n.match(/^[0-9]+$/))throw e+".set accepts only positive integer values. Value was "+n;var r=t.dom();Gt.isSupported(r)&&(r.style[e]=n+"px")},get:n,getOuter:n,aggregate:r,max:function(e,t,n){var o=r(e,n);return t>o?t-o:0}}}var Sr=xr("height",function(e){return wt.inBody(e)?e.dom().getBoundingClientRect().height:e.dom().offsetHeight}),Cr=function(e){return Sr.get(e)},Rr=function(e){return Sr.getOuter(e)},Tr=xr("width",function(e){return e.dom().offsetWidth}),Dr=function(e){return Tr.get(e)},Ar=function(e){return Tr.getOuter(e)},kr=Ue.detect(),Nr=function(e,t,n){return r=Qt.get(e,t),o=n,i=parseFloat(r),isNaN(i)?o:i;var r,o,i},Or=function(e){return kr.browser.isIE()||kr.browser.isEdge()?(n=Nr(t=e,"padding-top",0),r=Nr(t,"padding-bottom",0),o=Nr(t,"border-top-width",0),i=Nr(t,"border-bottom-width",0),u=t.dom().getBoundingClientRect().height,"border-box"===Qt.get(t,"box-sizing")?u:u-n-r-(o+i)):Nr(e,"height",Cr(e));var t,n,r,o,i,u},Er=/(\d+(\.\d+)?)(\w|%)*/,Br=/(\d+(\.\d+)?)%/,Pr=/(\d+(\.\d+)?)px|em/,Ir=function(e,t){Qt.set(e,"height",t+"px")},Wr=function(e,t,n,r){var o,i,u,a,c,l=parseInt(e,10);return qe.endsWith(e,"%")&&"table"!==ft.name(t)?(o=t,i=l,u=n,a=r,c=zt.table(o).map(function(e){var t=u(e);return Math.floor(i/100*t)}).getOr(i),a(o,c),c):l},Mr=function(e){var t,n=(t=e,Qt.getRaw(t,"height").getOrThunk(function(){return Or(t)+"px"}));return n?Wr(n,e,Cr,Ir):Cr(e)},Lr=function(e,t){return ht.has(e,t)?parseInt(ht.get(e,t),10):1},qr=function(e){return Qt.getRaw(e,"width").fold(function(){return g.from(ht.get(e,"width"))},function(e){return g.some(e)})},Fr=function(e,t){return e/t.pixelWidth()*100},zr={percentageBasedSizeRegex:c.constant(Br),pixelBasedSizeRegex:c.constant(Pr),setPixelWidth:function(e,t){Qt.set(e,"width",t+"px")},setPercentageWidth:function(e,t){Qt.set(e,"width",t+"%")},setHeight:Ir,getPixelWidth:function(e,t){return qr(e).fold(function(){var t=Dr(e);return parseInt(t,10)},function(n){return function(e,t,n){if(Pr.test(t)){var r=Pr.exec(t);return parseInt(r[1],10)}if(Br.test(t)){var o=Br.exec(t),i=parseFloat(o[1]);return i/100*n.pixelWidth()}var u=Dr(e);return parseInt(u,10)}(e,n,t)})},getPercentageWidth:function(e,t){return qr(e).fold(function(){var n=Dr(e),r=parseInt(n,10);return Fr(r,t)},function(n){return function(e,t,n){if(Br.test(t)){var r=Br.exec(t);return parseFloat(r[1])}var o=Dr(e),i=parseInt(o,10);return Fr(i,n)}(e,n,t)})},getGenericWidth:function(e){return qr(e).bind(function(e){if(Er.test(e)){var t=Er.exec(e);return g.some({width:c.constant(t[1]),unit:c.constant(t[3])})}return g.none()})},setGenericWidth:function(e,t,n){Qt.set(e,"width",t+n)},getHeight:function(e){return n="rowspan",Mr(t=e)/Lr(t,n);var t,n},getRawWidth:qr},jr={halve:function(e,t){zr.getGenericWidth(e).each(function(n){var r=n.width()/2;zr.setGenericWidth(e,r,n.unit()),zr.setGenericWidth(t,r,n.unit())})}},_r=function(e,t){var n=t||X.fromDom(document.documentElement);return Bt.ancestor(e,c.curry(Ye.eq,n)).isSome()},Hr=function(e){var t=e.dom();return t===t.window?e:ft.isDocument(e)?t.defaultView||t.parentWindow:null},Vr=function(e,t){return{left:c.constant(e),top:c.constant(t),translate:function(n,r){return Vr(e+n,t+r)}}},Ur=function(e,t){return e!==undefined?e:t!==undefined?t:0},Gr=function(e){var t,n=e.dom(),r=n.ownerDocument,o=r.body,i=X.fromDom(r.documentElement);return o===n?Vr(o.offsetLeft,o.offsetTop):_r(e,i)?(t=n.getBoundingClientRect(),Vr(t.left,t.top)):Vr(0,0)},Xr=function(e){var t=e.dom().ownerDocument,n=t.body,r=Hr(X.fromDom(t)),o=t.documentElement,i=Ur(r.pageYOffset,o.scrollTop),u=Ur(r.pageXOffset,o.scrollLeft),a=Ur(o.clientTop,n.clientTop),c=Ur(o.clientLeft,n.clientLeft);return Gr(e).translate(u-c,i-a)},Yr=q.immutable("row","y"),Kr=q.immutable("col","x"),$r=function(e){return Xr(e).left()+Ar(e)},Jr=function(e){return Xr(e).left()},Qr=function(e,t){return Kr(e,Jr(t))},Zr=function(e,t){return Kr(e,$r(t))},eo=function(e){return Xr(e).top()},to=function(e,t,n){if(0===n.length)return[];var r=A.map(n.slice(1),function(t,n){return t.map(function(t){return e(n,t)})}),o=n[n.length-1].map(function(e){return t(n.length-1,e)});return r.concat([o])},no={delta:c.identity,positions:c.curry(to,function(e,t){return Yr(e,eo(t))},function(e,t){return Yr(e,eo(t)+Rr(t))}),edge:eo},ro={delta:c.identity,edge:Jr,positions:c.curry(to,Qr,Zr)},oo={height:no,rtl:{delta:function(e,t){return-e},edge:$r,positions:c.curry(to,Zr,Qr)},ltr:ro},io={ltr:oo.ltr,rtl:oo.rtl};function uo(e){var t=function(t){return e(t).isRtl()?io.rtl:io.ltr};return{delta:function(e,n){return t(n).delta(e,n)},edge:function(e){return t(e).edge(e)},positions:function(e,n){return t(n).positions(e,n)}}}var ao={getGridSize:function(e){var t=jt(e);return Ut.generate(t).grid()}},co=function(e){var t=e,n=function(){return t};return{get:n,set:function(e){t=e},clone:function(){return co(n())}}},lo=function(e,t){return so(e,t,{validate:W.isFunction,label:"function"})},so=function(e,t,n){if(0===t.length)throw new Error("You must specify at least one required field.");return L.validateStrArr("required",t),L.checkDupes(t),function(r){var o=P.keys(r);A.forall(t,function(e){return A.contains(o,e)})||L.reqMessage(t,o),e(t,o);var i=A.filter(t,function(e){return!n.validate(r[e],e)});return i.length>0&&L.invalidTypeMessage(i,n.label),r}},fo=c.noop,mo={exactly:c.curry(lo,function(e,t){var n=A.filter(t,function(t){return!A.contains(e,t)});n.length>0&&L.unsuppMessage(n)}),ensure:c.curry(lo,fo),ensureWith:c.curry(so,fo)},go=function(e){var t=ht.has(e,"colspan")?parseInt(ht.get(e,"colspan"),10):1,n=ht.has(e,"rowspan")?parseInt(ht.get(e,"rowspan"),10):1;return{element:c.constant(e),colspan:c.constant(t),rowspan:c.constant(n)}},po=mo.exactly(["cell","row","replace","gap"]),ho=function(e,t){po(e);var n=co(g.none()),r=t!==undefined?t:go,o=function(t){var n,o=r(t);return n=o,e.cell(n)},i=function(e){var t=o(e);return n.get().isNone()&&n.set(g.some(t)),u=g.some({item:e,replacement:t}),t},u=g.none();return{getOrInit:function(e,t){return u.fold(function(){return i(e)},function(n){return t(e,n.item)?n.replacement:i(e)})},cursor:n.get}},vo=function(e,t){return function(n){var r=co(g.none());po(n);var o=[],i=function(i){var u=n.replace(i,t,{scope:e});return o.push({item:i,sub:u}),r.get().isNone()&&r.set(g.some(u)),u};return{replaceOrInit:function(e,t){return(n=e,r=t,A.find(o,function(e){return r(e.item,n)})).fold(function(){return i(e)},function(n){return t(e,n.item)?n.sub:i(e)});var n,r},cursor:r.get}}},bo=function(e){po(e);var t=co(g.none());return{combine:function(n){return t.get().isNone()&&t.set(g.some(n)),function(){var t=e.cell({element:c.constant(n),colspan:c.constant(1),rowspan:c.constant(1)});return Qt.remove(t,"width"),Qt.remove(n,"width"),t}},cursor:t.get}},wo=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],yo=function(e,t){var n=e.property().name(t);return A.contains(wo,n)},xo=function(e,t){return A.contains(["br","img","hr","input"],e.property().name(t))},So=yo,Co=function(e,t){var n=e.property().name(t);return A.contains(["ol","ul"],n)},Ro=xo,To=Nn(),Do=function(e){return So(To,e)},Ao=function(e){return Co(To,e)},ko=function(e){return Ro(To,e)},No=function(e){var t,n=function(e){return"br"===ft.name(e)},r=function(e){return xn.last(e).bind(function(t){var r,o=(r=t,nt.nextSibling(r).map(function(e){return!!Do(e)||(ko(e)?"img"!==ft.name(e):void 0)}).getOr(!1));return nt.parent(t).map(function(r){return!0===o||(i=r,"li"===ft.name(i)||Bt.ancestor(i,Ao).isSome())||n(t)||Do(r)&&!Ye.eq(e,r)?[]:[X.fromTag("br")];var i})}).getOr([])},o=0===(t=A.bind(e,function(e){var t,o=nt.children(e);return t=o,A.forall(t,function(e){return n(e)||ft.isText(e)&&0===pn.get(e).trim().length})?[]:o.concat(r(e))})).length?[X.fromTag("br")]:t;on.empty(e[0]),nn.append(e[0],o)},Oo=function(e){return function(){for(var t=new Array(arguments.length),n=0;n<t.length;n++)t[n]=arguments[n];if(0===t.length)throw new Error("Can't merge zero objects");for(var r={},o=0;o<t.length;o++){var i=t[o];for(var u in i)i.hasOwnProperty(u)&&(r[u]=e(r[u],i[u]))}return r}},Eo=Oo(function(e,t){return W.isObject(e)&&W.isObject(t)?Eo(e,t):t}),Bo=Oo(function(e,t){return t}),Po={deepMerge:Eo,merge:Bo},Io=function(e){for(var t=[],n=function(e){t.push(e)},r=0;r<e.length;r++)e[r].each(n);return t},Wo=function(e,t){for(var n=0;n<e.length;n++){var r=t(e[n],n);if(r.isSome())return r}return g.none()},Mo=function(e,t){return U.rowcells(t,e.section())},Lo=function(e,t){return e.cells()[t]},qo={addCell:function(e,t,n){var r=e.cells(),o=r.slice(0,t),i=r.slice(t),u=o.concat([n]).concat(i);return Mo(e,u)},setCells:Mo,mutateCell:function(e,t,n){e.cells()[t]=n},getCell:Lo,getCellElement:function(e,t){return Lo(e,t).element()},mapCells:function(e,t){var n=e.cells(),r=A.map(n,t);return U.rowcells(r,e.section())},cellLength:function(e){return e.cells().length}},Fo=function(e,t){if(0===e.length)return 0;var n=e[0];return A.findIndex(e,function(e){return!t(n.element(),e.element())}).fold(function(){return e.length},function(e){return e})},zo=function(e,t,n,r){var o,i,u,a,l=(o=e,i=t,o[i]).cells().slice(n),s=Fo(l,r),f=(u=e,a=n,A.map(u,function(e){return qo.getCell(e,a)})).slice(t),d=Fo(f,r);return{colspan:c.constant(s),rowspan:c.constant(d)}},jo=function(e,t){var n=A.map(e,function(e,t){return A.map(e.cells(),function(e,t){return!1})});return A.map(e,function(r,o){var i=A.bind(r.cells(),function(r,i){if(!1===n[o][i]){var u=zo(e,o,i,t);return function(e,t,r,o){for(var i=e;i<e+r;i++)for(var u=t;u<t+o;u++)n[i][u]=!0}(o,i,u.rowspan(),u.colspan()),[U.detailnew(r.element(),u.rowspan(),u.colspan(),r.isNew())]}return[]});return U.rowdetails(i,r.section())})},_o=function(e,t,n){for(var r=[],o=0;o<e.grid().rows();o++){for(var i=[],u=0;u<e.grid().columns();u++){var a=Ut.getAt(e,o,u).map(function(e){return U.elementnew(e.element(),n)}).getOrThunk(function(){return U.elementnew(t.gap(),!0)});i.push(a)}var c=U.rowcells(i,e.all()[o].section());r.push(c)}return r},Ho=function(e,t,n,r){n===r?ht.remove(e,t):ht.set(e,t,n)},Vo=function(e,t){var n=[],r=[],o=function(t,o){var i;t.length>0?function(t,o){var i=It.child(e,o).getOrThunk(function(){var t=X.fromTag(o,nt.owner(e).dom());return tn.append(e,t),t});on.empty(i);var u=A.map(t,function(e){e.isNew()&&n.push(e.element());var t=e.element();return on.empty(t),A.each(e.cells(),function(e){e.isNew()&&r.push(e.element()),Ho(e.element(),"colspan",e.colspan(),1),Ho(e.element(),"rowspan",e.rowspan(),1),tn.append(t,e.element())}),t});nn.append(i,u)}(t,o):(i=o,It.child(e,i).bind(on.remove))},i=[],u=[],a=[];return A.each(t,function(e){switch(e.section()){case"thead":i.push(e);break;case"tbody":u.push(e);break;case"tfoot":a.push(e)}}),o(i,"thead"),o(u,"tbody"),o(a,"tfoot"),{newRows:c.constant(n),newCells:c.constant(r)}},Uo=function(e){return A.map(e,function(e){var t=fn(e.element());return A.each(e.cells(),function(e){var n=dn(e.element());Ho(n,"colspan",e.colspan(),1),Ho(n,"rowspan",e.rowspan(),1),tn.append(t,n)}),t})},Go=function(e,t){for(var n=[],r=0;r<e;r++)n.push(t(r));return n},Xo=function(e,t){for(var n=[],r=e;r<t;r++)n.push(r);return n},Yo=function(e,t){if(t<0||t>=e.length-1)return g.none();var n=e[t].fold(function(){var n=A.reverse(e.slice(0,t));return Wo(n,function(e,t){return e.map(function(e){return{value:e,delta:t+1}})})},function(e){return g.some({value:e,delta:0})}),r=e[t+1].fold(function(){var n=e.slice(t+1);return Wo(n,function(e,t){return e.map(function(e){return{value:e,delta:t+1}})})},function(e){return g.some({value:e,delta:1})});return n.bind(function(e){return r.map(function(t){var n=t.delta+e.delta;return Math.abs(t.value-e.value)/n})})},Ko=function(e,t,n){var r=e();return A.find(r,t).orThunk(function(){return g.from(r[0]).orThunk(n)}).map(function(e){return e.element()})},$o=function(e){var t=e.grid(),n=Xo(0,t.columns()),r=Xo(0,t.rows());return A.map(n,function(t){return Ko(function(){return A.bind(r,function(n){return Ut.getAt(e,n,t).filter(function(e){return e.column()===t}).fold(c.constant([]),function(e){return[e]})})},function(e){return 1===e.colspan()},function(){return Ut.getAt(e,0,t)})})},Jo=function(e){var t=e.grid(),n=Xo(0,t.rows()),r=Xo(0,t.columns());return A.map(n,function(t){return Ko(function(){return A.bind(r,function(n){return Ut.getAt(e,t,n).filter(function(e){return e.row()===t}).fold(c.constant([]),function(e){return[e]})})},function(e){return 1===e.rowspan()},function(){return Ut.getAt(e,t,0)})})},Qo=function(e,t,n,r,o){var i=X.fromTag("div");return Qt.setAll(i,{position:"absolute",left:t-r/2+"px",top:n+"px",height:o+"px",width:r+"px"}),ht.setAll(i,{"data-column":e,role:"presentation"}),i},Zo=function(e,t,n,r,o){var i=X.fromTag("div");return Qt.setAll(i,{position:"absolute",left:t+"px",top:n-o/2+"px",height:o+"px",width:r+"px"}),ht.setAll(i,{"data-row":e,role:"presentation"}),i},ei=function(e){var t=e.replace(/\./g,"-");return{resolve:function(e){return t+"-"+e}}},ti={resolve:ei("ephox-snooker").resolve},ni=function(e,t){var n=ht.get(e,t);return n===undefined||""===n?[]:n.split(" ")},ri=ni,oi=function(e,t,n){var r=ni(e,t).concat([n]);ht.set(e,t,r.join(" "))},ii=function(e,t,n){var r=A.filter(ni(e,t),function(e){return e!==n});r.length>0?ht.set(e,t,r.join(" ")):ht.remove(e,t)},ui=function(e){return ri(e,"class")},ai=function(e,t){return oi(e,"class",t)},ci=function(e,t){return ii(e,"class",t)},li=ui,si=ai,fi=ci,di=function(e,t){A.contains(ui(e),t)?ci(e,t):ai(e,t)},mi=function(e){return e.dom().classList!==undefined},gi=function(e,t){return mi(e)&&e.dom().classList.contains(t)},pi={add:function(e,t){mi(e)?e.dom().classList.add(t):si(e,t)},remove:function(e,t){var n;mi(e)?e.dom().classList.remove(t):fi(e,t),0===(mi(n=e)?n.dom().classList:li(n)).length&&ht.remove(n,"class")},toggle:function(e,t){return mi(e)?e.dom().classList.toggle(t):di(e,t)},toggler:function(e,t){var n,r,o,i,u,a,c=mi(e),l=e.dom().classList;return n=function(){c?l.remove(t):fi(e,t)},r=function(){c?l.add(t):si(e,t)},o=gi(e,t),i=o||!1,{on:u=function(){r(),i=!0},off:a=function(){n(),i=!1},toggle:function(){(i?a:u)()},isOn:function(){return i}}},has:gi},hi=ti.resolve("resizer-bar"),vi=ti.resolve("resizer-rows"),bi=ti.resolve("resizer-cols"),wi=function(e){var t=St.descendants(e.parent(),"."+hi);A.each(t,on.remove)},yi=function(e,t,n){var r=e.origin();A.each(t,function(t,o){t.each(function(t){var o=n(r,t);pi.add(o,hi),tn.append(e.parent(),o)})})},xi=function(e,t,n,r,o,i){var u,a,c,l,s=Xr(t),f=n.length>0?o.positions(n,t):[];u=e,a=f,c=s,l=Ar(t),yi(u,a,function(e,t){var n=Zo(t.row(),c.left()-e.left(),t.y()-e.top(),l,7);return pi.add(n,vi),n});var d,m,g,p,h=r.length>0?i.positions(r,t):[];d=e,m=h,g=s,p=Rr(t),yi(d,m,function(e,t){var n=Qo(t.col(),t.x()-e.left(),g.top()-e.top(),7,p);return pi.add(n,bi),n})},Si=function(e,t){var n=St.descendants(e.parent(),"."+hi);A.each(n,t)},Ci={refresh:function(e,t,n,r){wi(e);var o=jt(t),i=Ut.generate(o),u=Jo(i),a=$o(i);xi(e,t,u,a,n,r)},hide:function(e){Si(e,function(e){Qt.set(e,"display","none")})},show:function(e){Si(e,function(e){Qt.set(e,"display","block")})},destroy:wi,isRowBar:function(e){return pi.has(e,vi)},isColBar:function(e){return pi.has(e,bi)}},Ri=function(e,t){return A.map(e,function(e){var n,r=(n=e.details(),Wo(n,function(e){return nt.parent(e.element()).map(function(e){var t=nt.parent(e).isNone();return U.elementnew(e,t)})}).getOrThunk(function(){return U.elementnew(t.row(),!0)}));return U.rowdatanew(r.element(),e.details(),e.section(),r.isNew())})},Ti=function(e,t){var n=jo(e,Ye.eq);return Ri(n,t)},Di=function(e,t){var n=A.flatten(A.map(e.all(),function(e){return e.cells()}));return A.find(n,function(e){return Ye.eq(t,e.element())})},Ai=function(e,t,n,r,o){return function(i,u,a,l,s){var f=jt(u),d=Ut.generate(f);return t(d,a).map(function(t){var n=_o(d,l,!1),r=e(n,t,Ye.eq,o(l)),i=Ti(r.grid(),l);return{grid:c.constant(i),cursor:r.cursor}}).fold(function(){return g.none()},function(e){var t=Vo(u,e.grid());return n(u,e.grid(),s),r(u),Ci.refresh(i,u,oo.height,s),g.some({cursor:e.cursor,newRows:t.newRows,newCells:t.newCells})})}},ki=Ti,Ni=function(e,t){return zt.cell(t.element()).bind(function(t){return Di(e,t)})},Oi=function(e,t){var n=A.map(t.selection(),function(t){return zt.cell(t).bind(function(t){return Di(e,t)})}),r=Io(n);return r.length>0?g.some(r):g.none()},Ei=function(e,t){return zt.cell(t.element()).bind(function(n){return Di(e,n).map(function(e){return Po.merge(e,{generators:t.generators,clipboard:t.clipboard})})})},Bi=function(e,t){var n=A.map(t.selection(),function(t){return zt.cell(t).bind(function(t){return Di(e,t)})}),r=Io(n);return r.length>0?g.some(Po.merge({cells:r},{generators:t.generators,clipboard:t.clipboard})):g.none()},Pi=function(e,t){return t.mergable()},Ii=function(e,t){return t.unmergable()},Wi=function(e){return{is:function(t){return e===t},isValue:c.always,isError:c.never,getOr:c.constant(e),getOrThunk:c.constant(e),getOrDie:c.constant(e),or:function(t){return Wi(e)},orThunk:function(t){return Wi(e)},fold:function(t,n){return n(e)},map:function(t){return Wi(t(e))},each:function(t){t(e)},bind:function(t){return t(e)},exists:function(t){return t(e)},forall:function(t){return t(e)},toOption:function(){return g.some(e)}}},Mi=function(e){return{is:c.never,isValue:c.never,isError:c.always,getOr:c.identity,getOrThunk:function(e){return e()},getOrDie:function(){return c.die(e)()},or:function(e){return e},orThunk:function(e){return e()},fold:function(t,n){return t(e)},map:function(t){return Mi(e)},each:c.noop,bind:function(t){return Mi(e)},exists:c.never,forall:c.always,toOption:g.none}},Li={value:Wi,error:Mi},qi=function(e,t){return A.map(e,function(){return U.elementnew(t.cell(),!0)})},Fi=function(e,t,n){return e.concat(Go(t,function(t){return qo.setCells(e[e.length-1],qi(e[e.length-1].cells(),n))}))},zi=function(e,t,n){return A.map(e,function(e){return qo.setCells(e,e.cells().concat(qi(Xo(0,t),n)))})},ji=function(e,t,n){if(e.row()>=t.length||e.column()>qo.cellLength(t[0]))return Li.error("invalid start address out of table bounds, row: "+e.row()+", column: "+e.column());var r=t.slice(e.row()),o=r[0].cells().slice(e.column()),i=qo.cellLength(n[0]),u=n.length;return Li.value({rowDelta:c.constant(r.length-u),colDelta:c.constant(o.length-i)})},_i=function(e,t){var n=qo.cellLength(e[0]),r=qo.cellLength(t[0]);return{rowDelta:c.constant(0),colDelta:c.constant(n-r)}},Hi=function(e,t,n){var r=t.colDelta()<0?zi:c.identity;return(t.rowDelta()<0?Fi:c.identity)(r(e,Math.abs(t.colDelta()),n),Math.abs(t.rowDelta()),n)},Vi=function(e,t,n,r){if(0===e.length)return e;for(var o=t.startRow();o<=t.finishRow();o++)for(var i=t.startCol();i<=t.finishCol();i++)qo.mutateCell(e[o],i,U.elementnew(r(),!1));return e},Ui=function(e,t,n,r){for(var o=!0,i=0;i<e.length;i++)for(var u=0;u<qo.cellLength(e[0]);u++){var a=n(qo.getCellElement(e[i],u),t);!0===a&&!1===o?qo.mutateCell(e[i],u,U.elementnew(r(),!0)):!0===a&&(o=!1)}return e},Gi=function(e,t,n,r){if(t>0&&t<e.length){var o=e[t-1].cells(),i=(u=o,a=n,A.foldl(u,function(e,t){return A.exists(e,function(e){return a(e.element(),t.element())})?e:e.concat([t])},[]));A.each(i,function(o){for(var i=g.none(),u=t;u<e.length;u++)for(var a=0;a<qo.cellLength(e[0]);a++){var c=e[u].cells()[a];n(c.element(),o.element())&&(i.isNone()&&(i=g.some(r())),i.each(function(t){qo.mutateCell(e[u],a,U.elementnew(t,!0))}))}})}var u,a;return e},Xi=function(e,t,n,r,o){return ji(e,t,n).map(function(i){var u=Hi(t,i,r);return function(e,t,n,r,o){for(var i,u,a,l,s,f,d,m=e.row(),g=e.column(),p=m+n.length,h=g+qo.cellLength(n[0]),v=m;v<p;v++)for(var b=g;b<h;b++){i=t,u=v,a=b,l=o,s=void 0,f=void 0,s=qo.getCell(i[u],a),f=c.curry(l,s.element()),d=i[u],i.length>1&&qo.cellLength(d)>1&&(a>0&&f(qo.getCellElement(d,a-1))||a<d.length-1&&f(qo.getCellElement(d,a+1))||u>0&&f(qo.getCellElement(i[u-1],a))||u<i.length-1&&f(qo.getCellElement(i[u+1],a)))&&Ui(t,qo.getCellElement(t[v],b),o,r.cell);var w=qo.getCellElement(n[v-m],b-g),y=r.replace(w);qo.mutateCell(t[v],b,U.elementnew(y,!0))}return t}(e,u,n,r,o)})},Yi=function(e,t,n,r,o){Gi(t,e,o,r.cell);var i=_i(n,t),u=Hi(n,i,r),a=_i(t,u),c=Hi(t,a,r);return c.slice(0,e).concat(u).concat(c.slice(e,c.length))},Ki=function(e,t,n,r,o){var i=e.slice(0,t),u=e.slice(t),a=qo.mapCells(e[n],function(n,i){return t>0&&t<e.length&&r(qo.getCellElement(e[t-1],i),qo.getCellElement(e[t],i))?qo.getCell(e[t],i):U.elementnew(o(n.element(),r),!0)});return i.concat([a]).concat(u)},$i=function(e,t,n,r,o){return A.map(e,function(e){var i=t>0&&t<qo.cellLength(e)&&r(qo.getCellElement(e,t-1),qo.getCellElement(e,t))?qo.getCell(e,t):U.elementnew(o(qo.getCellElement(e,n),r),!0);return qo.addCell(e,t,i)})},Ji=function(e,t,n,r,o){var i=n+1;return A.map(e,function(e,u){var a=u===t?U.elementnew(o(qo.getCellElement(e,n),r),!0):qo.getCell(e,n);return qo.addCell(e,i,a)})},Qi=function(e,t,n,r,o){var i=t+1,u=e.slice(0,i),a=e.slice(i),c=qo.mapCells(e[t],function(e,t){return t===n?U.elementnew(o(e.element(),r),!0):e});return u.concat([c]).concat(a)},Zi=function(e,t,n){return e.slice(0,t).concat(e.slice(n+1))},eu=function(e,t,n){var r=A.map(e,function(e){var r=e.cells().slice(0,t).concat(e.cells().slice(n+1));return U.rowcells(r,e.section())});return A.filter(r,function(e){return e.cells().length>0})},tu=function(e,t,n,r){return A.map(e,function(e){return qo.mapCells(e,function(e){return o=e,A.exists(t,function(e){return n(o.element(),e.element())})?U.elementnew(r(e.element(),n),!0):e;var o})})},nu=function(e,t,n,r){return qo.getCellElement(e[t],n)!==undefined&&t>0&&r(qo.getCellElement(e[t-1],n),qo.getCellElement(e[t],n))},ru=function(e,t,n){return t>0&&n(qo.getCellElement(e,t-1),qo.getCellElement(e,t))},ou=function(e,t,n,r){var o=A.bind(e,function(r,o){return nu(e,o,t,n)||ru(r,t,n)?[]:[qo.getCell(r,t)]});return tu(e,o,n,r)},iu=function(e,t,n,r){var o=e[t],i=A.bind(o.cells(),function(r,i){return nu(e,t,i,n)||ru(o,i,n)?[]:[r]});return tu(e,i,n,r)},uu=function(e){return{fold:e}},au=function(){return uu(function(e,t,n,r,o){return e()})},cu=function(e){return uu(function(t,n,r,o,i){return n(e)})},lu=function(e,t){return uu(function(n,r,o,i,u){return o(e,t)})},su=function(e,t,n){return uu(function(r,o,i,u,a){return u(e,t,n)})},fu=function(e,t){return uu(function(n,r,o,i,u){return u(e,t)})},du=function(e,t,n,r){var o,i,u=e.slice(0),a=(i=t,0===(o=e).length?au():1===o.length?cu(0):0===i?lu(0,1):i===o.length-1?fu(i-1,i):i>0&&i<o.length-1?su(i-1,i,i+1):au()),l=function(e){return A.map(e,c.constant(0))},s=c.constant(l(u)),f=function(e,t){if(n>=0){var o=Math.max(r.minCellWidth(),u[t]-n);return l(u.slice(0,e)).concat([n,o-u[t]]).concat(l(u.slice(t+1)))}var i=Math.max(r.minCellWidth(),u[e]+n),a=u[e]-i;return l(u.slice(0,e)).concat([i-u[e],a]).concat(l(u.slice(t+1)))},d=f;return a.fold(s,function(e){return r.singleColumnWidth(u[e],n)},d,function(e,t,n){return f(t,n)},function(e,t){if(n>=0)return l(u.slice(0,t)).concat([n]);var o=Math.max(r.minCellWidth(),u[t]+n);return l(u.slice(0,t)).concat([o-u[t]])})},mu=function(e,t){return ht.has(e,t)&&parseInt(ht.get(e,t),10)>1},gu={hasColspan:function(e){return mu(e,"colspan")},hasRowspan:function(e){return mu(e,"rowspan")},minWidth:c.constant(10),minHeight:c.constant(10),getInt:function(e,t){return parseInt(Qt.get(e,t),10)}},pu=function(e,t,n){return Qt.getRaw(e,t).fold(function(){return n(e)+"px"},function(e){return e})},hu=function(e){return pu(e,"width",zr.getPixelWidth)},vu=function(e){return pu(e,"height",zr.getHeight)},bu=function(e,t,n,r,o){var i=$o(e),u=A.map(i,function(e){return e.map(t.edge)});return A.map(i,function(e,t){return e.filter(c.not(gu.hasColspan)).fold(function(){var e=Yo(u,t);return r(e)},function(e){return n(e,o)})})},wu=function(e){return e.map(function(e){return e+"px"}).getOr("")},yu=function(e,t,n,r){var o=Jo(e),i=A.map(o,function(e){return e.map(t.edge)});return A.map(o,function(e,t){return e.filter(c.not(gu.hasRowspan)).fold(function(){var e=Yo(i,t);return r(e)},function(e){return n(e)})})},xu={getRawWidths:function(e,t){return bu(e,t,hu,wu)},getPixelWidths:function(e,t,n){return bu(e,t,zr.getPixelWidth,function(e){return e.getOrThunk(n.minCellWidth)},n)},getPercentageWidths:function(e,t,n){return bu(e,t,zr.getPercentageWidth,function(e){return e.fold(function(){return n.minCellWidth()},function(e){return e/n.pixelWidth()*100})},n)},getPixelHeights:function(e,t){return yu(e,t,zr.getHeight,function(e){return e.getOrThunk(gu.minHeight)})},getRawHeights:function(e,t){return yu(e,t,vu,wu)}},Su=function(e,t,n){for(var r=0,o=e;o<t;o++)r+=n[o]!==undefined?n[o]:0;return r},Cu=function(e,t){var n=Ut.justCells(e);return A.map(n,function(e){var n=Su(e.column(),e.column()+e.colspan(),t);return{element:e.element,width:c.constant(n),colspan:e.colspan}})},Ru=function(e,t){var n=Ut.justCells(e);return A.map(n,function(e){var n=Su(e.row(),e.row()+e.rowspan(),t);return{element:e.element,height:c.constant(n),rowspan:e.rowspan}})},Tu=function(e,t){return A.map(e.all(),function(e,n){return{element:e.element,height:c.constant(t[n])}})},Du=function(e){var t=parseInt(e,10),n=c.identity;return{width:c.constant(t),pixelWidth:c.constant(t),getWidths:xu.getPixelWidths,getCellDelta:n,singleColumnWidth:function(e,t){return[Math.max(gu.minWidth(),e+t)-e]},minCellWidth:gu.minWidth,setElementWidth:zr.setPixelWidth,setTableWidth:function(e,t,n){var r=A.foldr(t,function(e,t){return e+t},0);zr.setPixelWidth(e,r)}}},Au=function(e,t){if(zr.percentageBasedSizeRegex().test(t)){var n=zr.percentageBasedSizeRegex().exec(t);return o=n[1],i=e,u=parseFloat(o),a=Dr(i),{width:c.constant(u),pixelWidth:c.constant(a),getWidths:xu.getPercentageWidths,getCellDelta:function(e){return e/a*100},singleColumnWidth:function(e,t){return[100-e]},minCellWidth:function(){return gu.minWidth()/a*100},setElementWidth:zr.setPercentageWidth,setTableWidth:function(e,t,n){var r=u+n;zr.setPercentageWidth(e,r)}}}if(zr.pixelBasedSizeRegex().test(t)){var r=zr.pixelBasedSizeRegex().exec(t);return Du(r[1])}var o,i,u,a,l=Dr(e);return Du(l)},ku=function(e){return zr.getRawWidth(e).fold(function(){var t=Dr(e);return Du(t)},function(t){return Au(e,t)})},Nu=function(e){return Ut.generate(e)},Ou=function(e){var t=jt(e);return Nu(t)},Eu={adjustWidth:function(e,t,n,r){var o=ku(e),i=o.getCellDelta(t),u=Ou(e),a=o.getWidths(u,r,o),c=du(a,n,i,o),l=A.map(c,function(e,t){return e+a[t]}),s=Cu(u,l);A.each(s,function(e){o.setElementWidth(e.element(),e.width())}),n===u.grid().columns()-1&&o.setTableWidth(e,l,i)},adjustHeight:function(e,t,n,r){var o=Ou(e),i=xu.getPixelHeights(o,r),u=A.map(i,function(e,r){return n===r?Math.max(t+e,gu.minHeight()):e}),a=Ru(o,u),c=Tu(o,u);A.each(c,function(e){zr.setHeight(e.element(),e.height())}),A.each(a,function(e){zr.setHeight(e.element(),e.height())});var l,s=(l=u,A.foldr(l,function(e,t){return e+t},0));zr.setHeight(e,s)},adjustWidthTo:function(e,t,n){var r=ku(e),o=Nu(t),i=r.getWidths(o,n,r),u=Cu(o,i);A.each(u,function(e){r.setElementWidth(e.element(),e.width())});var a=A.foldr(i,function(e,t){return t+e},0);u.length>0&&r.setElementWidth(e,a)}},Bu=function(e){0===zt.cells(e).length&&on.remove(e)},Pu=q.immutable("grid","cursor"),Iu=function(e,t,n){return Wu(e,t,n).orThunk(function(){return Wu(e,0,0)})},Wu=function(e,t,n){return g.from(e[t]).bind(function(e){return g.from(e.cells()[n]).bind(function(e){return g.from(e.element())})})},Mu=function(e,t,n){return Pu(e,Wu(e,t,n))},Lu=function(e){return A.foldl(e,function(e,t){return A.exists(e,function(e){return e.row()===t.row()})?e:e.concat([t])},[]).sort(function(e,t){return e.row()-t.row()})},qu=function(e){return A.foldl(e,function(e,t){return A.exists(e,function(e){return e.column()===t.column()})?e:e.concat([t])},[]).sort(function(e,t){return e.column()-t.column()})},Fu=function(e,t,n){var r=_t(e,n),o=Ut.generate(r);return _o(o,t,!0)},zu=Eu.adjustWidthTo,ju={insertRowBefore:Ai(function(e,t,n,r){var o=t.row(),i=t.row(),u=Ki(e,i,o,n,r.getOrInit);return Mu(u,i,t.column())},Ni,c.noop,c.noop,ho),insertRowsBefore:Ai(function(e,t,n,r){var o=t[0].row(),i=t[0].row(),u=Lu(t),a=A.foldl(u,function(e,t){return Ki(e,i,o,n,r.getOrInit)},e);return Mu(a,i,t[0].column())},Oi,c.noop,c.noop,ho),insertRowAfter:Ai(function(e,t,n,r){var o=t.row(),i=t.row()+t.rowspan(),u=Ki(e,i,o,n,r.getOrInit);return Mu(u,i,t.column())},Ni,c.noop,c.noop,ho),insertRowsAfter:Ai(function(e,t,n,r){var o=Lu(t),i=o[o.length-1].row(),u=o[o.length-1].row()+o[o.length-1].rowspan(),a=A.foldl(o,function(e,t){return Ki(e,u,i,n,r.getOrInit)},e);return Mu(a,u,t[0].column())},Oi,c.noop,c.noop,ho),insertColumnBefore:Ai(function(e,t,n,r){var o=t.column(),i=t.column(),u=$i(e,i,o,n,r.getOrInit);return Mu(u,t.row(),i)},Ni,zu,c.noop,ho),insertColumnsBefore:Ai(function(e,t,n,r){var o=qu(t),i=o[0].column(),u=o[0].column(),a=A.foldl(o,function(e,t){return $i(e,u,i,n,r.getOrInit)},e);return Mu(a,t[0].row(),u)},Oi,zu,c.noop,ho),insertColumnAfter:Ai(function(e,t,n,r){var o=t.column(),i=t.column()+t.colspan(),u=$i(e,i,o,n,r.getOrInit);return Mu(u,t.row(),i)},Ni,zu,c.noop,ho),insertColumnsAfter:Ai(function(e,t,n,r){var o=t[t.length-1].column(),i=t[t.length-1].column()+t[t.length-1].colspan(),u=qu(t),a=A.foldl(u,function(e,t){return $i(e,i,o,n,r.getOrInit)},e);return Mu(a,t[0].row(),i)},Oi,zu,c.noop,ho),splitCellIntoColumns:Ai(function(e,t,n,r){var o=Ji(e,t.row(),t.column(),n,r.getOrInit);return Mu(o,t.row(),t.column())},Ni,zu,c.noop,ho),splitCellIntoRows:Ai(function(e,t,n,r){var o=Qi(e,t.row(),t.column(),n,r.getOrInit);return Mu(o,t.row(),t.column())},Ni,c.noop,c.noop,ho),eraseColumns:Ai(function(e,t,n,r){var o=qu(t),i=eu(e,o[0].column(),o[o.length-1].column()),u=Iu(i,t[0].row(),t[0].column());return Pu(i,u)},Oi,zu,Bu,ho),eraseRows:Ai(function(e,t,n,r){var o=Lu(t),i=Zi(e,o[0].row(),o[o.length-1].row()),u=Iu(i,t[0].row(),t[0].column());return Pu(i,u)},Oi,c.noop,Bu,ho),makeColumnHeader:Ai(function(e,t,n,r){var o=ou(e,t.column(),n,r.replaceOrInit);return Mu(o,t.row(),t.column())},Ni,c.noop,c.noop,vo("row","th")),unmakeColumnHeader:Ai(function(e,t,n,r){var o=ou(e,t.column(),n,r.replaceOrInit);return Mu(o,t.row(),t.column())},Ni,c.noop,c.noop,vo(null,"td")),makeRowHeader:Ai(function(e,t,n,r){var o=iu(e,t.row(),n,r.replaceOrInit);return Mu(o,t.row(),t.column())},Ni,c.noop,c.noop,vo("col","th")),unmakeRowHeader:Ai(function(e,t,n,r){var o=iu(e,t.row(),n,r.replaceOrInit);return Mu(o,t.row(),t.column())},Ni,c.noop,c.noop,vo(null,"td")),mergeCells:Ai(function(e,t,n,r){var o=t.cells();No(o);var i=Vi(e,t.bounds(),n,c.constant(o[0]));return Pu(i,g.from(o[0]))},Pi,c.noop,c.noop,bo),unmergeCells:Ai(function(e,t,n,r){var o=A.foldr(t,function(e,t){return Ui(e,t,n,r.combine(t))},e);return Pu(o,g.from(t[0]))},Ii,zu,c.noop,bo),pasteCells:Ai(function(e,t,n,r){var o,i,u,a,c=(o=t.clipboard(),i=t.generators(),u=jt(o),a=Ut.generate(u),_o(a,i,!0)),l=U.address(t.row(),t.column());return Xi(l,e,c,t.generators(),n).fold(function(){return Pu(e,g.some(t.element()))},function(e){var n=Iu(e,t.row(),t.column());return Pu(e,n)})},Ei,zu,c.noop,ho),pasteRowsBefore:Ai(function(e,t,n,r){var o=e[t.cells[0].row()],i=t.cells[0].row(),u=Fu(t.clipboard(),t.generators(),o),a=Yi(i,e,u,t.generators(),n),c=Iu(a,t.cells[0].row(),t.cells[0].column());return Pu(a,c)},Bi,c.noop,c.noop,ho),pasteRowsAfter:Ai(function(e,t,n,r){var o=e[t.cells[0].row()],i=t.cells[t.cells.length-1].row()+t.cells[t.cells.length-1].rowspan(),u=Fu(t.clipboard(),t.generators(),o),a=Yi(i,e,u,t.generators(),n),c=Iu(a,t.cells[0].row(),t.cells[0].column());return Pu(a,c)},Bi,c.noop,c.noop,ho)},_u=function(e){return X.fromDom(e.getBody())},Hu={getBody:_u,getIsRoot:function(e){return function(t){return Ye.eq(t,_u(e))}},addSizeSuffix:function(e){return/^[0-9]+$/.test(e)&&(e+="px"),e},removePxSuffix:function(e){return e?e.replace(/px$/,""):""}},Vu=function(e){return"rtl"===Qt.get(e,"direction")?"rtl":"ltr"},Uu={onDirection:function(e,t){return function(n){return"rtl"===Vu(n)?t:e}},getDirection:Vu},Gu={isRtl:c.constant(!1)},Xu={isRtl:c.constant(!0)},Yu={directionAt:function(e){return"rtl"===Uu.getDirection(e)?Xu:Gu}},Ku=["tableprops","tabledelete","|","tableinsertrowbefore","tableinsertrowafter","tabledeleterow","|","tableinsertcolbefore","tableinsertcolafter","tabledeletecol"],$u={"border-collapse":"collapse",width:"100%"},Ju={border:"1"},Qu=function(e){return e.getParam("table_resize_bars",!0,"boolean")},Zu=function(e){return e.getParam("table_tab_navigation",!0,"boolean")},ea=function(e){return e.getParam("forced_root_block","p","string")},ta=function(e){return e.getParam("table_cell_advtab",!0,"boolean")},na=function(e){return e.getParam("table_row_advtab",!0,"boolean")},ra=function(e){return e.getParam("table_advtab",!0,"boolean")},oa=function(e){return e.getParam("table_style_by_css",!1,"boolean")},ia=function(e){return e.getParam("forced_block_attrs",{},"object")},ua=function(e){return e.getParam("table_cell_class_list",[],"array")},aa=function(e){return e.getParam("table_row_class_list",[],"array")},ca=function(e){return e.getParam("table_class_list",[],"array")},la=function(e){return!1===e.getParam("table_responsive_width")},sa=function(e){var t=e.getParam("table_clone_elements");return W.isString(t)?g.some(t.split(/[ ,]/)):Array.isArray(t)?g.some(t):g.none()},fa=function(e){var t=e.getParam("object_resizing",!0);return"table"===t||t},da=function(e,t){return e.fire("newrow",{node:t})},ma=function(e,t){return e.fire("newcell",{node:t})},ga=function(e,t,n){var r=jt(e),o=Ut.generate(r);return Oi(o,t).map(function(e){var t=_o(o,n,!1).slice(e[0].row(),e[e.length-1].row()+e[e.length-1].rowspan()),r=ki(t,n);return Uo(r)})},pa=tinymce.util.Tools.resolve("tinymce.util.Tools"),ha={applyAlign:function(e,t,n){n&&e.formatter.apply("align"+n,{},t)},applyVAlign:function(e,t,n){n&&e.formatter.apply("valign"+n,{},t)},unApplyAlign:function(e,t){pa.each("left center right".split(" "),function(n){e.formatter.remove("align"+n,{},t)})},unApplyVAlign:function(e,t){pa.each("top middle bottom".split(" "),function(n){e.formatter.remove("valign"+n,{},t)})},getTDTHOverallStyle:function(e,t,n){var r;return r=function(t,r){for(var o=0;o<r.length;o++){var i=e.getStyle(r[o],n);if(void 0===t&&(t=i),t!==i)return""}return t}(r,e.select("td,th",t))}},va=function(e,t){var n=e.dom,r=t.control.rootControl,o=r.toJSON(),i=n.parseStyle(o.style);"style"===t.control.name()?(r.find("#borderStyle").value(i["border-style"]||"")[0].fire("select"),r.find("#borderColor").value(i["border-color"]||"")[0].fire("change"),r.find("#backgroundColor").value(i["background-color"]||"")[0].fire("change"),r.find("#width").value(i.width||"").fire("change"),r.find("#height").value(i.height||"").fire("change")):(i["border-style"]=o.borderStyle,i["border-color"]=o.borderColor,i["background-color"]=o.backgroundColor,i.width=o.width?Hu.addSizeSuffix(o.width):"",i.height=o.height?Hu.addSizeSuffix(o.height):""),r.find("#style").value(n.serializeStyle(n.parseStyle(n.serializeStyle(i))))},ba={createStyleForm:function(e){var t=function(){var t=e.getParam("color_picker_callback");if(t)return function(n){return t.call(e,function(e){n.control.value(e).fire("change")},n.control.value())}};return{title:"Advanced",type:"form",defaults:{onchange:c.curry(va,e)},items:[{label:"Style",name:"style",type:"textbox"},{type:"form",padding:0,formItemDefaults:{layout:"grid",alignH:["start","right"]},defaults:{size:7},items:[{label:"Border style",type:"listbox",name:"borderStyle",width:90,onselect:c.curry(va,e),values:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},{label:"Border color",type:"colorbox",name:"borderColor",onaction:t()},{label:"Background color",type:"colorbox",name:"backgroundColor",onaction:t()}]}]}},buildListItems:function(e,t,n){var r=function(e,n){return n=n||[],pa.each(e,function(e){var o={text:e.text||e.title};e.menu?o.menu=r(e.menu):(o.value=e.value,t&&t(o)),n.push(o)}),n};return r(e,n||[])},updateStyleField:va,extractAdvancedStyles:function(e,t){var n=e.parseStyle(e.getAttrib(t,"style")),r={};return n["border-style"]&&(r.borderStyle=n["border-style"]),n["border-color"]&&(r.borderColor=n["border-color"]),n["background-color"]&&(r.backgroundColor=n["background-color"]),r.style=e.serializeStyle(n),r}},wa=function(e,t,n){var r,o=e.dom;function i(e,t,n){n&&o.setAttrib(e,t,n)}function u(e,t,n){n&&o.setStyle(e,t,n)}ba.updateStyleField(e,n),r=n.control.rootControl.toJSON(),e.undoManager.transact(function(){pa.each(t,function(n){var a,c;i(n,"scope",r.scope),1===t.length?i(n,"style",r.style):(a=n,c=r.style,a.style.cssText+=";"+c),i(n,"class",r["class"]),u(n,"width",Hu.addSizeSuffix(r.width)),u(n,"height",Hu.addSizeSuffix(r.height)),r.type&&n.nodeName.toLowerCase()!==r.type&&(n=o.rename(n,r.type)),1===t.length&&(ha.unApplyAlign(e,n),ha.unApplyVAlign(e,n)),r.align&&ha.applyAlign(e,n,r.align),r.valign&&ha.applyVAlign(e,n,r.valign)}),e.focus()})},ya=function(e){var t,n,r,o=[];if(o=e.dom.select("td[data-mce-selected],th[data-mce-selected]"),t=e.dom.getParent(e.selection.getStart(),"td,th"),!o.length&&t&&o.push(t),t=t||o[0]){var i,u,a,l;o.length>1?n={width:"",height:"",scope:"","class":"",align:"",style:"",type:t.nodeName.toLowerCase()}:(u=t,a=(i=e).dom,(l={width:a.getStyle(u,"width")||a.getAttrib(u,"width"),height:a.getStyle(u,"height")||a.getAttrib(u,"height"),scope:a.getAttrib(u,"scope"),"class":a.getAttrib(u,"class")}).type=u.nodeName.toLowerCase(),pa.each("left center right".split(" "),function(e){i.formatter.matchNode(u,"align"+e)&&(l.align=e)}),pa.each("top middle bottom".split(" "),function(e){i.formatter.matchNode(u,"valign"+e)&&(l.valign=e)}),ta(i)&&pa.extend(l,ba.extractAdvancedStyles(a,u)),n=l),ua(e).length>0&&(r={name:"class",type:"listbox",label:"Class",values:ba.buildListItems(ua(e),function(t){t.value&&(t.textStyle=function(){return e.formatter.getCssText({block:"td",classes:[t.value]})})})});var s={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",layout:"grid",columns:2,labelGapCalc:!1,padding:0,defaults:{type:"textbox",maxWidth:50},items:[{label:"Width",name:"width",onchange:c.curry(ba.updateStyleField,e)},{label:"Height",name:"height",onchange:c.curry(ba.updateStyleField,e)},{label:"Cell type",name:"type",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{label:"Scope",name:"scope",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{label:"H Align",name:"align",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"V Align",name:"valign",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}]},r]};ta(e)?e.windowManager.open({title:"Cell properties",bodyType:"tabpanel",data:n,body:[{title:"General",type:"form",items:s},ba.createStyleForm(e)],onsubmit:c.curry(wa,e,o)}):e.windowManager.open({title:"Cell properties",data:n,body:s,onsubmit:c.curry(wa,e,o)})}},xa=function(e,t,n){var r=e.getParent(t,"table"),o=t.parentNode,i=e.select(n,r)[0];i||(i=e.create(n),r.firstChild?"CAPTION"===r.firstChild.nodeName?e.insertAfter(i,r.firstChild):r.insertBefore(i,r.firstChild):r.appendChild(i)),i.appendChild(t),o.hasChildNodes()||e.remove(o)};function Sa(e,t,n){var r,o=e.dom;function i(e,t,n){n&&o.setAttrib(e,t,n)}ba.updateStyleField(e,n),r=n.control.rootControl.toJSON(),e.undoManager.transact(function(){pa.each(t,function(n){var u,a,c;i(n,"scope",r.scope),i(n,"style",r.style),i(n,"class",r["class"]),u=n,a="height",(c=Hu.addSizeSuffix(r.height))&&o.setStyle(u,a,c),r.type!==n.parentNode.nodeName.toLowerCase()&&xa(e.dom,n,r.type),1===t.length&&ha.unApplyAlign(e,n),r.align&&ha.applyAlign(e,n,r.align)}),e.focus()})}var Ca=function(e){var t,n,r,o,i,u,a,l,s,f,d=e.dom,m=[];t=d.getParent(e.selection.getStart(),"table"),n=d.getParent(e.selection.getStart(),"td,th"),pa.each(t.rows,function(e){pa.each(e.cells,function(t){if(d.getAttrib(t,"data-mce-selected")||t===n)return m.push(e),!1})}),(r=m[0])&&(m.length>1?i={height:"",scope:"","class":"",align:"",type:r.parentNode.nodeName.toLowerCase()}:(l=r,s=(a=e).dom,(f={height:s.getStyle(l,"height")||s.getAttrib(l,"height"),scope:s.getAttrib(l,"scope"),"class":s.getAttrib(l,"class")}).type=l.parentNode.nodeName.toLowerCase(),pa.each("left center right".split(" "),function(e){a.formatter.matchNode(l,"align"+e)&&(f.align=e)}),na(a)&&pa.extend(f,ba.extractAdvancedStyles(s,l)),i=f),aa(e).length>0&&(o={name:"class",type:"listbox",label:"Class",values:ba.buildListItems(aa(e),function(t){t.value&&(t.textStyle=function(){return e.formatter.getCssText({block:"tr",classes:[t.value]})})})}),u={type:"form",columns:2,padding:0,defaults:{type:"textbox"},items:[{type:"listbox",name:"type",label:"Row type",text:"Header",maxWidth:null,values:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"listbox",name:"align",label:"Alignment",text:"None",maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height"},o]},na(e)?e.windowManager.open({title:"Row properties",data:i,bodyType:"tabpanel",body:[{title:"General",type:"form",items:u},ba.createStyleForm(e)],onsubmit:c.curry(Sa,e,m)}):e.windowManager.open({title:"Row properties",data:i,body:u,onsubmit:c.curry(Sa,e,m)}))},Ra=tinymce.util.Tools.resolve("tinymce.Env"),Ta={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},percentages:!0},Da=function(e,t,n,r,o){void 0===o&&(o=Ta);var i=X.fromTag("table");Qt.setAll(i,o.styles),ht.setAll(i,o.attributes);var u=X.fromTag("tbody");tn.append(i,u);for(var a=[],c=0;c<e;c++){for(var l=X.fromTag("tr"),s=0;s<t;s++){var f=c<n||s<r?X.fromTag("th"):X.fromTag("td");s<r&&ht.set(f,"scope","row"),c<n&&ht.set(f,"scope","col"),tn.append(f,X.fromTag("br")),o.percentages&&Qt.set(f,"width",100/t+"%"),tn.append(l,f)}a.push(l)}return nn.append(u,a),i},Aa=function(e){return e.dom().innerHTML},ka=function(e){var t=X.fromTag("div"),n=X.fromDom(e.dom().cloneNode(!0));return tn.append(t,n),Aa(t)},Na=function(e,t){e.selection.select(t.dom(),!0),e.selection.collapse(!0)},Oa=function(e,t,n){var r,o,i=e.getParam("table_default_styles",$u,"object"),u={styles:i,attributes:(o=e,o.getParam("table_default_attributes",Ju,"object")),percentages:(r=i.width,W.isString(r)&&-1!==r.indexOf("%")&&!la(e))},a=Da(n,t,0,0,u);ht.set(a,"data-mce-id","__mce");var l=ka(a);return e.insertContent(l),It.descendant(Hu.getBody(e),'table[data-mce-id="__mce"]').map(function(t){var n,r,o,i;return la(e)&&Qt.set(t,"width",Qt.get(t,"width")),ht.remove(t,"data-mce-id"),n=e,r=t,A.each(St.descendants(r,"tr"),function(e){da(n,e.dom()),A.each(St.descendants(e,"th,td"),function(e){ma(n,e.dom())})}),o=e,i=t,It.descendant(i,"td,th").each(c.curry(Na,o)),t.dom()}).getOr(null)};function Ea(e,t,n,r){if("TD"===t.tagName||"TH"===t.tagName)e.setStyle(t,n,r);else if(t.children)for(var o=0;o<t.children.length;o++)Ea(e,t.children[o],n,r)}var Ba=function(e,t,n){var r,o,i=e.dom;ba.updateStyleField(e,n),!1===(o=n.control.rootControl.toJSON())["class"]&&delete o["class"],e.undoManager.transact(function(){t||(t=Oa(e,o.cols||1,o.rows||1)),function(e,t,n){var r=e.dom,o={},i={};if(o["class"]=n["class"],i.height=Hu.addSizeSuffix(n.height),r.getAttrib(t,"width")&&!oa(e)?o.width=Hu.removePxSuffix(n.width):i.width=Hu.addSizeSuffix(n.width),oa(e)?(i["border-width"]=Hu.addSizeSuffix(n.border),i["border-spacing"]=Hu.addSizeSuffix(n.cellspacing),pa.extend(o,{"data-mce-border-color":n.borderColor,"data-mce-cell-padding":n.cellpadding,"data-mce-border":n.border})):pa.extend(o,{border:n.border,cellpadding:n.cellpadding,cellspacing:n.cellspacing}),oa(e)&&t.children)for(var u=0;u<t.children.length;u++)Ea(r,t.children[u],{"border-width":Hu.addSizeSuffix(n.border),"border-color":n.borderColor,padding:Hu.addSizeSuffix(n.cellpadding)});n.style?pa.extend(i,r.parseStyle(n.style)):i=pa.extend({},r.parseStyle(r.getAttrib(t,"style")),i),o.style=r.serializeStyle(i),r.setAttribs(t,o)}(e,t,o),(r=i.select("caption",t)[0])&&!o.caption&&i.remove(r),!r&&o.caption&&((r=i.create("caption")).innerHTML=Ra.ie?"\xa0":'<br data-mce-bogus="1"/>',t.insertBefore(r,t.firstChild)),ha.unApplyAlign(e,t),o.align&&ha.applyAlign(e,t,o.align),e.focus(),e.addVisual()})},Pa=function(e,t){var n,r,o,i,u,a,l,s,f,d,m=e.dom,g={};!0===t?(n=m.getParent(e.selection.getStart(),"table"))&&(l=n,s=(a=e).dom,f={width:s.getStyle(l,"width")||s.getAttrib(l,"width"),height:s.getStyle(l,"height")||s.getAttrib(l,"height"),cellspacing:s.getStyle(l,"border-spacing")||s.getAttrib(l,"cellspacing"),cellpadding:s.getAttrib(l,"data-mce-cell-padding")||s.getAttrib(l,"cellpadding")||ha.getTDTHOverallStyle(a.dom,l,"padding"),border:s.getAttrib(l,"data-mce-border")||s.getAttrib(l,"border")||ha.getTDTHOverallStyle(a.dom,l,"border"),borderColor:s.getAttrib(l,"data-mce-border-color"),caption:!!s.select("caption",l)[0],"class":s.getAttrib(l,"class")},pa.each("left center right".split(" "),function(e){a.formatter.matchNode(l,"align"+e)&&(f.align=e)}),ra(a)&&pa.extend(f,ba.extractAdvancedStyles(s,l)),g=f):(r={label:"Cols",name:"cols"},o={label:"Rows",name:"rows"}),ca(e).length>0&&(g["class"]&&(g["class"]=g["class"].replace(/\s*mce\-item\-table\s*/g,"")),i={name:"class",type:"listbox",label:"Class",values:ba.buildListItems(ca(e),function(t){t.value&&(t.textStyle=function(){return e.formatter.getCssText({block:"table",classes:[t.value]})})})}),u={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",labelGapCalc:!1,padding:0,layout:"grid",columns:2,defaults:{type:"textbox",maxWidth:50},items:(d=e,d.getParam("table_appearance_options",!0,"boolean")?[r,o,{label:"Width",name:"width",onchange:c.curry(ba.updateStyleField,e)},{label:"Height",name:"height",onchange:c.curry(ba.updateStyleField,e)},{label:"Cell spacing",name:"cellspacing"},{label:"Cell padding",name:"cellpadding"},{label:"Border",name:"border"},{label:"Caption",name:"caption",type:"checkbox"}]:[r,o,{label:"Width",name:"width",onchange:c.curry(ba.updateStyleField,e)},{label:"Height",name:"height",onchange:c.curry(ba.updateStyleField,e)}])},{label:"Alignment",name:"align",type:"listbox",text:"None",values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},i]},ra(e)?e.windowManager.open({title:"Table properties",data:g,bodyType:"tabpanel",body:[{title:"General",type:"form",items:u},ba.createStyleForm(e)],onsubmit:c.curry(Ba,e,n)}):e.windowManager.open({title:"Table properties",data:g,body:u,onsubmit:c.curry(Ba,e,n)})},Ia=pa.each,Wa={registerCommands:function(e,t,n,r,o){var i=Hu.getIsRoot(e),u=function(){return X.fromDom(e.dom.getParent(e.selection.getStart(),"th,td"))},a=function(e){return zt.table(e,i)},l=function(t){var o=u();a(o).each(function(i){var u=wr.forMenu(r,i,o);t(i,u).each(function(t){e.selection.setRng(t),e.focus(),n.clear(i)})})},s=function(t){var n=u();return a(n).bind(function(t){var o=X.fromDom(e.getDoc()),i=wr.forMenu(r,t,n),u=Dn.cellOperations(c.noop,o,g.none());return ga(t,i,u)})},f=function(t){o.get().each(function(o){var i=A.map(o,function(e){return dn(e)}),c=u();a(c).bind(function(o){var u=X.fromDom(e.getDoc()),a=Dn.paste(u),l=wr.pasteRows(r,o,c,i,a);t(o,l).each(function(t){e.selection.setRng(t),e.focus(),n.clear(o)})})})};Ia({mceTableSplitCells:function(){l(t.unmergeCells)},mceTableMergeCells:function(){l(t.mergeCells)},mceTableInsertRowBefore:function(){l(t.insertRowsBefore)},mceTableInsertRowAfter:function(){l(t.insertRowsAfter)},mceTableInsertColBefore:function(){l(t.insertColumnsBefore)},mceTableInsertColAfter:function(){l(t.insertColumnsAfter)},mceTableDeleteCol:function(){l(t.deleteColumn)},mceTableDeleteRow:function(){l(t.deleteRow)},mceTableCutRow:function(e){o.set(s()),l(t.deleteRow)},mceTableCopyRow:function(e){o.set(s())},mceTablePasteRowBefore:function(e){f(t.pasteRowsBefore)},mceTablePasteRowAfter:function(e){f(t.pasteRowsAfter)},mceTableDelete:function(){var t=X.fromDom(e.dom.getParent(e.selection.getStart(),"th,td"));zt.table(t,i).filter(c.not(i)).each(function(t){var n=X.fromText("");tn.after(t,n),on.remove(t);var r=e.dom.createRng();r.setStart(n.dom(),0),r.setEnd(n.dom(),0),e.selection.setRng(r)})}},function(t,n){e.addCommand(n,t)}),Ia({mceInsertTable:c.curry(Pa,e),mceTableProps:c.curry(Pa,e,!0),mceTableRowProps:c.curry(Ca,e),mceTableCellProps:c.curry(ya,e)},function(t,n){e.addCommand(n,function(e,n){t(n)})})}},Ma={only:function(e){var t=g.from(e.dom().documentElement).map(X.fromDom).getOr(e);return{parent:c.constant(t),view:c.constant(e),origin:c.constant(Vr(0,0))}},detached:function(e,t){var n=c.curry(Xr,t);return{parent:c.constant(t),view:c.constant(e),origin:n}},body:function(e,t){return{parent:c.constant(t),view:c.constant(e),origin:c.constant(Vr(0,0))}}};function La(e){var t=q.immutable.apply(null,e),n=[];return{bind:function(e){if(e===undefined)throw"Event bind error: undefined handler";n.push(e)},unbind:function(e){n=A.filter(n,function(t){return t!==e})},trigger:function(){var e=t.apply(null,arguments);A.each(n,function(t){t(e)})}}}var qa={create:function(e){return{registry:P.map(e,function(e){return{bind:e.bind,unbind:e.unbind}}),trigger:P.map(e,function(e){return e.trigger})}}},Fa={mode:mo.exactly(["compare","extract","mutate","sink"]),sink:mo.exactly(["element","start","stop","destroy"]),api:mo.exactly(["forceDrop","drop","move","delayDrop"])},za={resolve:ei("ephox-dragster").resolve},ja=function(e,t){return function(n){if(e(n)){var r,o,i,u,a,l,s,f=X.fromDom(n.target),d=function(){n.stopPropagation()},m=function(){n.preventDefault()},g=c.compose(m,d),p=(r=f,o=n.clientX,i=n.clientY,u=d,a=m,l=g,s=n,{target:c.constant(r),x:c.constant(o),y:c.constant(i),stop:u,prevent:a,kill:l,raw:c.constant(s)});t(p)}}},_a=function(e,t,n,r,o){var i=ja(n,r);return e.dom().addEventListener(t,i,o),{unbind:c.curry(Ha,e,t,i,o)}},Ha=function(e,t,n,r){e.dom().removeEventListener(t,n,r)},Va=function(e,t,n,r){return _a(e,t,n,r,!1)},Ua=function(e,t,n,r){return _a(e,t,n,r,!0)},Ga=c.constant(!0),Xa={bind:function(e,t,n){return Va(e,t,Ga,n)},capture:function(e,t,n){return Ua(e,t,Ga,n)}},Ya=Fa.mode({compare:function(e,t){return Vr(t.left()-e.left(),t.top()-e.top())},extract:function(e){return g.some(Vr(e.x(),e.y()))},sink:function(e,t){var n,r,o,i=(n=t,r=Po.merge({layerClass:za.resolve("blocker")},n),o=X.fromTag("div"),ht.set(o,"role","presentation"),Qt.setAll(o,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),pi.add(o,za.resolve("blocker")),pi.add(o,r.layerClass),{element:function(){return o},destroy:function(){on.remove(o)}}),u=Xa.bind(i.element(),"mousedown",e.forceDrop),a=Xa.bind(i.element(),"mouseup",e.drop),c=Xa.bind(i.element(),"mousemove",e.move),l=Xa.bind(i.element(),"mouseout",e.delayDrop);return Fa.sink({element:i.element,start:function(e){tn.append(e,i.element())},stop:function(){on.remove(i.element())},destroy:function(){i.destroy(),a.unbind(),c.unbind(),l.unbind(),u.unbind()}})},mutate:function(e,t){e.mutate(t.left(),t.top())}});function Ka(){var e=g.none(),t=qa.create({move:La(["info"])});return{onEvent:function(n,r){r.extract(n).each(function(n){var o,i,u;(o=r,i=n,u=e.map(function(e){return o.compare(e,i)}),e=g.some(i),u).each(function(e){t.trigger.move(e)})})},reset:function(){e=g.none()},events:t.registry}}function $a(){var e={onEvent:function(e,t){},reset:c.noop},t=Ka(),n=e;return{on:function(){n.reset(),n=t},off:function(){n.reset(),n=e},isOn:function(){return n===t},onEvent:function(e,t){n.onEvent(e,t)},events:t.events}}var Ja=function(e,t){var n=null;return{cancel:function(){null!==n&&(clearTimeout(n),n=null)},throttle:function(){var r=arguments;null!==n&&clearTimeout(n),n=setTimeout(function(){e.apply(null,r),n=null,r=null},t)}}},Qa=function(e,t,n){var r=!1,o=qa.create({start:La([]),stop:La([])}),i=$a(),u=function(){l.stop(),i.isOn()&&(i.off(),o.trigger.stop())},a=Ja(u,200);i.events.move.bind(function(n){t.mutate(e,n.info())});var c=function(e){return function(){var t=Array.prototype.slice.call(arguments,0);if(r)return e.apply(null,t)}},l=t.sink(Fa.api({forceDrop:u,drop:c(u),move:c(function(e,n){a.cancel(),i.onEvent(e,t)}),delayDrop:c(a.throttle)}),n);return{element:l.element,go:function(e){l.start(e),i.on(),o.trigger.start()},on:function(){r=!0},off:function(){r=!1},destroy:function(){l.destroy()},events:o.registry}},Za={transform:function(e,t){var n=t!==undefined?t:{},r=n.mode!==undefined?n.mode:Ya;return Qa(e,r,t)}};function ec(){var e,t=qa.create({drag:La(["xDelta","yDelta","target"])}),n=g.none(),r={mutate:function(t,n){e.trigger.drag(t,n)},events:(e=qa.create({drag:La(["xDelta","yDelta"])})).registry};return r.events.drag.bind(function(e){n.each(function(n){t.trigger.drag(e.xDelta(),e.yDelta(),n)})}),{assign:function(e){n=g.some(e)},get:function(){return n},mutate:r.mutate,events:t.registry}}var tc={any:function(e){return It.first(e).isSome()},ancestor:function(e,t,n){return It.ancestor(e,t,n).isSome()},sibling:function(e,t){return It.sibling(e,t).isSome()},child:function(e,t){return It.child(e,t).isSome()},descendant:function(e,t){return It.descendant(e,t).isSome()},closest:function(e,t,n){return It.closest(e,t,n).isSome()}},nc=ti.resolve("resizer-bar-dragging");function rc(e,t){var n=oo.height,r=function(e,t,n){var r=ec(),o=Za.transform(r,{}),i=g.none(),u=function(e,t){return g.from(ht.get(e,t))};r.events.drag.bind(function(e){u(e.target(),"data-row").each(function(t){var n=gu.getInt(e.target(),"top");Qt.set(e.target(),"top",n+e.yDelta()+"px")}),u(e.target(),"data-column").each(function(t){var n=gu.getInt(e.target(),"left");Qt.set(e.target(),"left",n+e.xDelta()+"px")})});var a=function(e,t){return gu.getInt(e,t)-parseInt(ht.get(e,"data-initial-"+t),10)};o.events.stop.bind(function(){r.get().each(function(r){i.each(function(o){u(r,"data-row").each(function(e){var t=a(r,"top");ht.remove(r,"data-initial-top"),m.trigger.adjustHeight(o,t,parseInt(e,10))}),u(r,"data-column").each(function(e){var t=a(r,"left");ht.remove(r,"data-initial-left"),m.trigger.adjustWidth(o,t,parseInt(e,10))}),Ci.refresh(e,o,n,t)})})});var l=function(t,n){m.trigger.startAdjust(),r.assign(t),ht.set(t,"data-initial-"+n,parseInt(Qt.get(t,n),10)),pi.add(t,nc),Qt.set(t,"opacity","0.2"),o.go(e.parent())},s=Xa.bind(e.parent(),"mousedown",function(e){Ci.isRowBar(e.target())&&l(e.target(),"top"),Ci.isColBar(e.target())&&l(e.target(),"left")}),f=function(t){return Ye.eq(t,e.view())},d=Xa.bind(e.view(),"mouseover",function(r){"table"===ft.name(r.target())||tc.ancestor(r.target(),"table",f)?(i="table"===ft.name(r.target())?g.some(r.target()):It.ancestor(r.target(),"table",f)).each(function(r){Ci.refresh(e,r,n,t)}):wt.inBody(r.target())&&Ci.destroy(e)}),m=qa.create({adjustHeight:La(["table","delta","row"]),adjustWidth:La(["table","delta","column"]),startAdjust:La([])});return{destroy:function(){s.unbind(),d.unbind(),o.destroy(),Ci.destroy(e)},refresh:function(r){Ci.refresh(e,r,n,t)},on:o.on,off:o.off,hideBars:c.curry(Ci.hide,e),showBars:c.curry(Ci.show,e),events:m.registry}}(e,t,n),o=qa.create({beforeResize:La(["table"]),afterResize:La(["table"]),startDrag:La([])});return r.events.adjustHeight.bind(function(e){o.trigger.beforeResize(e.table());var t=n.delta(e.delta(),e.table());Eu.adjustHeight(e.table(),t,e.row(),n),o.trigger.afterResize(e.table())}),r.events.startAdjust.bind(function(e){o.trigger.startDrag()}),r.events.adjustWidth.bind(function(e){o.trigger.beforeResize(e.table());var n=t.delta(e.delta(),e.table());Eu.adjustWidth(e.table(),n,e.column(),t),o.trigger.afterResize(e.table())}),{on:r.on,off:r.off,hideBars:r.hideBars,showBars:r.showBars,destroy:r.destroy,events:o.registry}}var oc={get:function(e,t){return e.inline?Ma.body(Hu.getBody(e),(n=X.fromTag("div"),Qt.setAll(n,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),tn.append(wt.body(),n),n)):Ma.only(X.fromDom(e.getDoc()));var n},remove:function(e,t){e.inline&&on.remove(t.parent())}};function ic(e){var t,n,r=g.none(),o=g.none(),i=g.none(),u=/(\d+(\.\d+)?)%/,a=function(e){return"TABLE"===e.nodeName};return e.on("init",function(){var t=uo(Yu.directionAt),n=oc.get(e);if(i=g.some(n),fa(e)&&Qu(e)){var u=rc(n,t);u.on(),u.events.startDrag.bind(function(t){r=g.some(e.selection.getRng())}),u.events.afterResize.bind(function(t){var n=t.table(),o=St.descendants(n,"td[data-mce-style],th[data-mce-style]");A.each(o,function(e){ht.remove(e,"data-mce-style")}),r.each(function(t){e.selection.setRng(t),e.focus()}),e.undoManager.add()}),o=g.some(u)}}),e.on("ObjectResizeStart",function(r){var o;a(r.target)&&(t=r.width,o=r.target,n=e.dom.getStyle(o,"width")||e.dom.getAttrib(o,"width"))}),e.on("ObjectResized",function(r){if(a(r.target)){var o=r.target;if(u.test(n)){var i=parseFloat(u.exec(n)[1]),c=r.width*i/t;e.dom.setStyle(o,"width",c+"%")}else{var l=[];pa.each(o.rows,function(t){pa.each(t.cells,function(t){var n=e.dom.getStyle(t,"width",!0);l.push({cell:t,width:n})})}),pa.each(l,function(t){e.dom.setStyle(t.cell,"width",t.width),e.dom.setAttrib(t.cell,"width",null)})}}}),{lazyResize:function(){return o},lazyWire:function(){return i.getOr(Ma.only(X.fromDom(e.getBody())))},destroy:function(){o.each(function(e){e.destroy()}),i.each(function(t){oc.remove(e,t)})}}}var uc=function(e){return{fold:e}},ac=function(e){return uc(function(t,n,r,o){return t(e)})},cc=function(e){return uc(function(t,n,r,o){return n(e)})},lc=function(e,t){return uc(function(n,r,o,i){return o(e,t)})},sc=function(e){return uc(function(t,n,r,o){return o(e)})},fc=function(e,t){return zt.table(e,t).bind(function(t){var n=zt.cells(t);return A.findIndex(n,function(t){return Ye.eq(e,t)}).map(function(e){return{index:c.constant(e),all:c.constant(n)}})})},dc=function(e,t){return fc(e,t).fold(function(){return ac(e)},function(t){return t.index()+1<t.all().length?lc(e,t.all()[t.index()+1]):sc(e)})},mc=function(e,t){return fc(e,t).fold(function(){return ac()},function(t){return t.index()-1>=0?lc(e,t.all()[t.index()-1]):cc(e)})},gc=sr([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),pc={before:gc.before,on:gc.on,after:gc.after,cata:function(e,t,n,r){return e.fold(t,n,r)},getStart:function(e){return e.fold(c.identity,c.identity,c.identity)}},hc=sr([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),vc=q.immutable("start","soffset","finish","foffset"),bc={domRange:hc.domRange,relative:hc.relative,exact:hc.exact,exactFromRange:function(e){return hc.exact(e.start(),e.soffset(),e.finish(),e.foffset())},range:vc,getWin:function(e){var t=e.match({domRange:function(e){return X.fromDom(e.startContainer)},relative:function(e,t){return pc.getStart(e)},exact:function(e,t,n,r){return e}});return nt.defaultView(t)}},wc=function(e,t,n,r){var o=nt.owner(e).dom().createRange();return o.setStart(e.dom(),t),o.setEnd(n.dom(),r),o},yc=function(e,t,n,r){var o=wc(e,t,n,r),i=Ye.eq(e,n)&&t===r;return o.collapsed&&!i},xc=function(e,t){var n=(t||document).createDocumentFragment();return A.each(e,function(e){n.appendChild(e.dom())}),X.fromDom(n)},Sc=function(e,t){e.selectNodeContents(t.dom())},Cc=function(e){e.deleteContents()},Rc=function(e){return{left:c.constant(e.left),top:c.constant(e.top),right:c.constant(e.right),bottom:c.constant(e.bottom),width:c.constant(e.width),height:c.constant(e.height)}},Tc={create:function(e){return e.document.createRange()},replaceWith:function(e,t){Cc(e),e.insertNode(t.dom())},selectNodeContents:function(e,t){var n=e.document.createRange();return Sc(n,t),n},selectNodeContentsUsing:Sc,relativeToNative:function(e,t,n){var r,o,i=e.document.createRange();return r=i,t.fold(function(e){r.setStartBefore(e.dom())},function(e,t){r.setStart(e.dom(),t)},function(e){r.setStartAfter(e.dom())}),o=i,n.fold(function(e){o.setEndBefore(e.dom())},function(e,t){o.setEnd(e.dom(),t)},function(e){o.setEndAfter(e.dom())}),i},exactToNative:function(e,t,n,r,o){var i=e.document.createRange();return i.setStart(t.dom(),n),i.setEnd(r.dom(),o),i},deleteContents:Cc,cloneFragment:function(e){var t=e.cloneContents();return X.fromDom(t)},getFirstRect:function(e){var t=e.getClientRects(),n=t.length>0?t[0]:e.getBoundingClientRect();return n.width>0||n.height>0?g.some(n).map(Rc):g.none()},getBounds:function(e){var t=e.getBoundingClientRect();return t.width>0||t.height>0?g.some(t).map(Rc):g.none()},isWithin:function(e,t){return t.compareBoundaryPoints(e.END_TO_START,e)<1&&t.compareBoundaryPoints(e.START_TO_END,e)>-1},toString:function(e){return e.toString()}},Dc=sr([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Ac=function(e,t,n){return t(X.fromDom(n.startContainer),n.startOffset,X.fromDom(n.endContainer),n.endOffset)},kc=function(e,t){var n,r,o,i=(n=e,t.match({domRange:function(e){return{ltr:c.constant(e),rtl:g.none}},relative:function(e,t){return{ltr:se(function(){return Tc.relativeToNative(n,e,t)}),rtl:se(function(){return g.some(Tc.relativeToNative(n,t,e))})}},exact:function(e,t,r,o){return{ltr:se(function(){return Tc.exactToNative(n,e,t,r,o)}),rtl:se(function(){return g.some(Tc.exactToNative(n,r,o,e,t))})}}}));return(o=(r=i).ltr()).collapsed?r.rtl().filter(function(e){return!1===e.collapsed}).map(function(e){return Dc.rtl(X.fromDom(e.endContainer),e.endOffset,X.fromDom(e.startContainer),e.startOffset)}).getOrThunk(function(){return Ac(0,Dc.ltr,o)}):Ac(0,Dc.ltr,o)},Nc={ltr:Dc.ltr,rtl:Dc.rtl,diagnose:kc,asLtrRange:function(e,t){return kc(e,t).match({ltr:function(t,n,r,o){var i=e.document.createRange();return i.setStart(t.dom(),n),i.setEnd(r.dom(),o),i},rtl:function(t,n,r,o){var i=e.document.createRange();return i.setStart(r.dom(),o),i.setEnd(t.dom(),n),i}})}},Oc=function(e,t,n){return t>=e.left&&t<=e.right&&n>=e.top&&n<=e.bottom},Ec=function(e,t,n,r,o){if(0===o)return 0;if(t===r)return o-1;for(var i=r,u=1;u<o;u++){var a=e(u),c=Math.abs(t-a.left);if(n>a.bottom);else{if(n<a.top||c>i)return u-1;i=c}}return 0},Bc={locate:function(e,t,n,r){var o=e.dom().createRange();o.selectNode(t.dom());var i=o.getClientRects();return Wo(i,function(e){return Oc(e,n,r)?g.some(e):g.none()}).map(function(o){return i=e,u=t,a=n,c=r,l=o,s=function(e){var t=i.dom().createRange();return t.setStart(u.dom(),e),t.collapse(!0),t},f=pn.get(u).length,d=Ec(function(e){return s(e).getBoundingClientRect()},a,c,l.right,f),s(d);var i,u,a,c,l,s,f,d})}},Pc=function(e,t,n,r){var o=e.dom().createRange(),i=nt.children(t);return Wo(i,function(t){return o.selectNode(t.dom()),Oc(o.getBoundingClientRect(),n,r)?Ic(e,t,n,r):g.none()})},Ic=function(e,t,n,r){return(ft.isText(t)?Bc.locate:Pc)(e,t,n,r)},Wc=function(e,t,n,r){var o=e.dom().createRange();o.selectNode(t.dom());var i=o.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,n)),a=Math.max(i.top,Math.min(i.bottom,r));return Ic(e,t,u,a)},Mc=function(e,t){return t-e.left<e.right-t},Lc=function(e,t,n){var r=e.dom().createRange();return r.selectNode(t.dom()),r.collapse(n),r},qc=function(e,t,n){var r=e.dom().createRange();r.selectNode(t.dom());var o=r.getBoundingClientRect(),i=Mc(o,n);return(!0===i?xn.first:xn.last)(t).map(function(t){return Lc(e,t,i)})},Fc=function(e,t,n){var r=t.dom().getBoundingClientRect(),o=Mc(r,n);return g.some(Lc(e,t,o))},zc=function(e,t,n){return(0===nt.children(t).length?Fc:qc)(e,t,n)},jc=document.caretPositionFromPoint?function(e,t,n){return g.from(e.dom().caretPositionFromPoint(t,n)).bind(function(t){if(null===t.offsetNode)return g.none();var n=e.dom().createRange();return n.setStart(t.offsetNode,t.offset),n.collapse(),g.some(n)})}:document.caretRangeFromPoint?function(e,t,n){return g.from(e.dom().caretRangeFromPoint(t,n))}:function(e,t,n){return X.fromPoint(e,t,n).bind(function(r){var o=function(){return zc(e,r,t)};return 0===nt.children(r).length?o():function(e,t,n,r){var o=e.dom().createRange();o.selectNode(t.dom());var i=o.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,n)),a=Math.max(i.top,Math.min(i.bottom,r));return Wc(e,t,u,a)}(e,r,t,n).orThunk(o)})},_c=function(e,t,n){var r=X.fromDom(e.document);return jc(r,t,n).map(function(e){return bc.range(X.fromDom(e.startContainer),e.startOffset,X.fromDom(e.endContainer),e.endOffset)})},Hc=function(e,t,n){var r,o,i,u,a,c,l=Nc.asLtrRange(e,t),s=X.fromDom(l.commonAncestorContainer);return ft.isElement(s)?(r=e,o=s,i=l,u=n,a=Tc.create(r),c=(te.is(o,u)?[o]:[]).concat(St.descendants(o,u)),A.filter(c,function(e){return Tc.selectNodeContentsUsing(a,e),Tc.isWithin(i,a)})):[]},Vc=function(e,t){var n=ft.name(e);return"input"===n?pc.after(e):A.contains(["br","img"],n)?0===t?pc.before(e):pc.after(e):pc.on(e,t)},Uc=function(e,t){var n=e.fold(pc.before,Vc,pc.after),r=t.fold(pc.before,Vc,pc.after);return bc.relative(n,r)},Gc=function(e,t,n,r){var o=Vc(e,t),i=Vc(n,r);return bc.relative(o,i)},Xc=function(e){return e.match({domRange:function(e){var t=X.fromDom(e.startContainer),n=X.fromDom(e.endContainer);return Gc(t,e.startOffset,n,e.endOffset)},relative:Uc,exact:Gc})},Yc=Uc,Kc=Gc,$c=function(e,t){g.from(e.getSelection()).each(function(e){e.removeAllRanges(),e.addRange(t)})},Jc=function(e,t,n,r,o){var i=Tc.exactToNative(e,t,n,r,o);$c(e,i)},Qc=function(e,t){return Nc.diagnose(e,t).match({ltr:function(t,n,r,o){Jc(e,t,n,r,o)},rtl:function(t,n,r,o){var i=e.getSelection();i.setBaseAndExtent?i.setBaseAndExtent(t.dom(),n,r.dom(),o):i.extend?(i.collapse(t.dom(),n),i.extend(r.dom(),o)):Jc(e,r,o,t,n)}})},Zc=function(e){var t=X.fromDom(e.anchorNode),n=X.fromDom(e.focusNode);return yc(t,e.anchorOffset,n,e.focusOffset)?g.some(bc.range(X.fromDom(e.anchorNode),e.anchorOffset,X.fromDom(e.focusNode),e.focusOffset)):function(e){if(e.rangeCount>0){var t=e.getRangeAt(0),n=e.getRangeAt(e.rangeCount-1);return g.some(bc.range(X.fromDom(t.startContainer),t.startOffset,X.fromDom(n.endContainer),n.endOffset))}return g.none()}(e)},el=function(e){var t=e.getSelection();return t.rangeCount>0?Zc(t):g.none()},tl={setExact:function(e,t,n,r,o){var i=Kc(t,n,r,o);Qc(e,i)},getExact:el,get:function(e){return el(e).map(function(e){return bc.exact(e.start(),e.soffset(),e.finish(),e.foffset())})},setRelative:function(e,t,n){var r=Yc(t,n);Qc(e,r)},toNative:function(e){var t=bc.getWin(e).dom(),n=function(e,n,r,o){return Tc.exactToNative(t,e,n,r,o)},r=Xc(e);return Nc.diagnose(t,r).match({ltr:n,rtl:n})},setToElement:function(e,t){var n=Tc.selectNodeContents(e,t);$c(e,n)},clear:function(e){e.getSelection().removeAllRanges()},clone:function(e,t){var n=Nc.asLtrRange(e,t);return Tc.cloneFragment(n)},replace:function(e,t,n){var r=Nc.asLtrRange(e,t),o=xc(n,e.document);Tc.replaceWith(r,o)},deleteAt:function(e,t){var n=Nc.asLtrRange(e,t);Tc.deleteContents(n)},forElement:function(e,t){var n=Tc.selectNodeContents(e,t);return bc.range(X.fromDom(n.startContainer),n.startOffset,X.fromDom(n.endContainer),n.endOffset)},getFirstRect:function(e,t){var n=Nc.asLtrRange(e,t);return Tc.getFirstRect(n)},getBounds:function(e,t){var n=Nc.asLtrRange(e,t);return Tc.getBounds(n)},getAtPoint:function(e,t,n){return _c(e,t,n)},findWithin:function(e,t,n){return Hc(e,t,n)},getAsString:function(e,t){var n=Nc.asLtrRange(e,t);return Tc.toString(n)},isCollapsed:function(e,t,n,r){return Ye.eq(e,n)&&t===r}},nl=tinymce.util.Tools.resolve("tinymce.util.VK"),rl=function(e,t,n,r){return ul(e,t,dc(n),r)},ol=function(e,t,n,r){return ul(e,t,mc(n),r)},il=function(e,t){var n=bc.exact(t,0,t,0);return tl.toNative(n)},ul=function(e,t,n,r,o){return n.fold(g.none,g.none,function(e,t){return xn.first(t).map(function(e){return il(0,e)})},function(n){return zt.table(n,t).bind(function(t){var o,i,u=wr.noMenu(n);return e.undoManager.transact(function(){r.insertRowsAfter(t,u)}),o=t,i=St.descendants(o,"tr"),A.last(i).bind(function(e){return It.descendant(e,"td,th").map(function(e){return il(0,e)})})})})},al=["table","li","dl"],cl={handle:function(e,t,n,r){if(e.keyCode===nl.TAB){var o=Hu.getBody(t),i=function(e){var t=ft.name(e);return Ye.eq(e,o)||A.contains(al,t)},u=t.selection.getRng();if(u.collapsed){var a=X.fromDom(u.startContainer);zt.cell(a,i).each(function(o){e.preventDefault(),(e.shiftKey?ol:rl)(t,i,o,n,r).each(function(e){t.selection.setRng(e)})})}}}},ll={response:q.immutable("selection","kill")},sl=function(e){return function(t){return t===e}},fl=sl(38),dl=sl(40),ml={ltr:{isBackward:sl(37),isForward:sl(39)},rtl:{isBackward:sl(39),isForward:sl(37)},isUp:fl,isDown:dl,isNavigation:function(e){return e>=37&&e<=40}},gl={convertToRange:function(e,t){var n=Nc.asLtrRange(e,t);return{start:c.constant(X.fromDom(n.startContainer)),soffset:c.constant(n.startOffset),finish:c.constant(X.fromDom(n.endContainer)),foffset:c.constant(n.endOffset)}},makeSitus:function(e,t,n,r){return{start:c.constant(pc.on(e,t)),finish:c.constant(pc.on(n,r))}}},pl=Ue.detect().browser.isSafari(),hl=function(e){var t=e!==undefined?e.dom():document,n=t.body.scrollLeft||t.documentElement.scrollLeft,r=t.body.scrollTop||t.documentElement.scrollTop;return Vr(n,r)},vl=function(e,t,n){(n!==undefined?n.dom():document).defaultView.scrollTo(e,t)},bl=function(e,t){pl&&W.isFunction(e.dom().scrollIntoViewIfNeeded)?e.dom().scrollIntoViewIfNeeded(!1):e.dom().scrollIntoView(t)},wl={get:hl,to:vl,by:function(e,t,n){(n!==undefined?n.dom():document).defaultView.scrollBy(e,t)},preserve:function(e,t){var n=hl(e);t();var r=hl(e);n.top()===r.top()&&n.left()===r.left()||vl(n.left(),n.top(),e)},capture:function(e){var t=g.none(),n=function(){t=g.some(hl(e))};return n(),{save:n,restore:function(){t.each(function(t){vl(t.left(),t.top(),e)})}}},intoView:bl,intoViewIfNeeded:function(e,t){var n=t.dom().getBoundingClientRect(),r=e.dom().getBoundingClientRect();r.top<n.top?bl(e,!0):r.bottom>n.bottom&&bl(e,!1)},setToElement:function(e,t){var n=Xr(t),r=X.fromDom(e.document);vl(n.left(),n.top(),r)},scrollBarWidth:function(){var e=X.fromHtml('<div style="width: 100px; height: 100px; overflow: scroll; position: absolute; top: -9999px;"></div>');tn.after(wt.body(),e);var t=e.dom().offsetWidth-e.dom().clientWidth;return on.remove(e),t}};function yl(e){return{elementFromPoint:function(t,n){return g.from(e.document.elementFromPoint(t,n)).map(X.fromDom)},getRect:function(e){return e.dom().getBoundingClientRect()},getRangedRect:function(t,n,r,o){var i=bc.exact(t,n,r,o);return tl.getFirstRect(e,i).map(function(e){return P.map(e,c.apply)})},getSelection:function(){return tl.get(e).map(function(t){return gl.convertToRange(e,t)})},fromSitus:function(t){var n=bc.relative(t.start(),t.finish());return gl.convertToRange(e,n)},situsFromPoint:function(t,n){return tl.getAtPoint(e,t,n).map(function(e){return{start:c.constant(pc.on(e.start(),e.soffset())),finish:c.constant(pc.on(e.finish(),e.foffset()))}})},clearSelection:function(){tl.clear(e)},setSelection:function(t){tl.setExact(e,t.start(),t.soffset(),t.finish(),t.foffset())},setRelativeSelection:function(t,n){tl.setRelative(e,t,n)},selectContents:function(t){tl.setToElement(e,t)},getInnerHeight:function(){return e.innerHeight},getScrollY:function(){return wl.get(X.fromDom(e.document)).top()},scrollBy:function(t,n){wl.by(t,n,X.fromDom(e.document))}}}var xl=function(e,t,n,r,o){return Ye.eq(n,r)?g.none():or.identify(n,r,t).bind(function(t){var r=t.boxes().getOr([]);return r.length>0?(o(e,r,t.start(),t.finish()),g.some(ll.response(g.some(gl.makeSitus(n,0,n,bn(n))),!0))):g.none()})},Sl={sync:function(e,t,n,r,o,i,u){return Ye.eq(n,o)&&r===i?g.none():It.closest(n,"td,th",t).bind(function(n){return It.closest(o,"td,th",t).bind(function(r){return xl(e,t,n,r,u)})})},detect:xl,update:function(e,t,n,r,o){return or.shiftSelection(r,e,t,o.firstSelectedSelector(),o.lastSelectedSelector()).map(function(e){return o.clear(n),o.selectRange(n,e.boxes(),e.start(),e.finish()),e.boxes()})}},Cl=q.immutableBag(["left","top","right","bottom"],[]),Rl={nu:Cl,moveUp:function(e,t){return Cl({left:e.left(),top:e.top()-t,right:e.right(),bottom:e.bottom()-t})},moveDown:function(e,t){return Cl({left:e.left(),top:e.top()+t,right:e.right(),bottom:e.bottom()+t})},moveBottomTo:function(e,t){var n=e.bottom()-e.top();return Cl({left:e.left(),top:t-n,right:e.right(),bottom:t})},moveTopTo:function(e,t){var n=e.bottom()-e.top();return Cl({left:e.left(),top:t,right:e.right(),bottom:t+n})},getTop:function(e){return e.top()},getBottom:function(e){return e.bottom()},translate:function(e,t,n){return Cl({left:e.left()+t,top:e.top()+n,right:e.right()+t,bottom:e.bottom()+n})},toString:function(e){return"("+e.left()+", "+e.top()+") -> ("+e.right()+", "+e.bottom()+")"}},Tl=function(e){return Rl.nu({left:e.left,top:e.top,right:e.right,bottom:e.bottom})},Dl=function(e,t){return g.some(e.getRect(t))},Al=function(e,t,n){return ft.isElement(t)?Dl(e,t).map(Tl):ft.isText(t)?(r=e,o=t,i=n,i>=0&&i<bn(o)?r.getRangedRect(o,i,o,i+1):i>0?r.getRangedRect(o,i-1,o,i):g.none()).map(Tl):g.none();var r,o,i},kl=function(e,t){return ft.isElement(t)?Dl(e,t).map(Tl):ft.isText(t)?e.getRangedRect(t,0,t,bn(t)).map(Tl):g.none()},Nl=q.immutable("item","mode"),Ol=function(e,t,n,r){var o=r!==undefined?r:El;return e.property().parent(t).map(function(e){return Nl(e,o)})},El=function(e,t,n,r){var o=r!==undefined?r:Bl;return n.sibling(e,t).map(function(e){return Nl(e,o)})},Bl=function(e,t,n,r){var o=r!==undefined?r:Bl,i=e.property().children(t);return n.first(i).map(function(e){return Nl(e,o)})},Pl=[{current:Ol,next:El,fallback:g.none()},{current:El,next:Bl,fallback:g.some(Ol)},{current:Bl,next:Bl,fallback:g.some(El)}],Il=function(e,t,n,r,o){return o=o!==undefined?o:Pl,A.find(o,function(e){return e.current===n}).bind(function(n){return n.current(e,t,r,n.next).orThunk(function(){return n.fallback.bind(function(n){return Il(e,t,n,r)})})})},Wl={backtrack:Ol,sidestep:El,advance:Bl,go:Il},Ml={left:function(){return{sibling:function(e,t){return e.query().prevSibling(t)},first:function(e){return e.length>0?g.some(e[e.length-1]):g.none()}}},right:function(){return{sibling:function(e,t){return e.query().nextSibling(t)},first:function(e){return e.length>0?g.some(e[0]):g.none()}}}},Ll=function(e,t,n,r,o,i){return Wl.go(e,t,r,o).bind(function(t){return i(t.item())?g.none():n(t.item())?g.some(t.item()):Ll(e,t.item(),n,t.mode(),o,i)})},ql=function(e,t,n,r){return Ll(e,t,n,Wl.sidestep,Ml.left(),r)},Fl=function(e,t,n,r){return Ll(e,t,n,Wl.sidestep,Ml.right(),r)},zl=function(e,t){return 0===e.property().children(t).length},jl=function(e,t,n,r){return ql(e,t,n,r)},_l=function(e,t,n,r){return Fl(e,t,n,r)},Hl={before:function(e,t,n){return jl(e,t,c.curry(zl,e),n)},after:function(e,t,n){return _l(e,t,c.curry(zl,e),n)},seekLeft:jl,seekRight:_l,walkers:function(){return{left:Ml.left,right:Ml.right}},walk:function(e,t,n,r,o){return Wl.go(e,t,n,r,o)},backtrack:Wl.backtrack,sidestep:Wl.sidestep,advance:Wl.advance},Vl=Nn(),Ul={gather:function(e,t,n){return Hl.gather(Vl,e,t,n)},before:function(e,t){return Hl.before(Vl,e,t)},after:function(e,t){return Hl.after(Vl,e,t)},seekLeft:function(e,t,n){return Hl.seekLeft(Vl,e,t,n)},seekRight:function(e,t,n){return Hl.seekRight(Vl,e,t,n)},walkers:function(){return Hl.walkers()},walk:function(e,t,n,r){return Hl.walk(Vl,e,t,n,r)}},Gl=sr([{none:[]},{retry:["caret"]}]),Xl=function(e,t,n){return Bt.closest(t,Do).fold(c.constant(!1),function(t){return kl(e,t).exists(function(e){return r=e,(t=n).left()<r.left()||Math.abs(r.right()-t.left())<1||t.left()>r.right();var t,r})})},Yl={point:Rl.getTop,adjuster:function(e,t,n,r,o){var i=Rl.moveUp(o,5);return Math.abs(n.top()-r.top())<1?Gl.retry(i):n.bottom()<o.top()?Gl.retry(i):n.bottom()===o.top()?Gl.retry(Rl.moveUp(o,1)):Xl(e,t,o)?Gl.retry(Rl.translate(i,5,0)):Gl.none()},move:Rl.moveUp,gather:Ul.before},Kl={point:Rl.getBottom,adjuster:function(e,t,n,r,o){var i=Rl.moveDown(o,5);return Math.abs(n.bottom()-r.bottom())<1?Gl.retry(i):n.top()>o.bottom()?Gl.retry(i):n.top()===o.bottom()?Gl.retry(Rl.moveDown(o,1)):Xl(e,t,o)?Gl.retry(Rl.translate(i,5,0)):Gl.none()},move:Rl.moveDown,gather:Ul.after},$l=function(e,t,n,r,o){return 0===o?g.some(r):(c=e,l=r.left(),s=t.point(r),c.elementFromPoint(l,s).filter(function(e){return"table"===ft.name(e)}).isSome()?(u=r,a=o-1,$l(e,i=t,n,i.move(u,5),a)):e.situsFromPoint(r.left(),t.point(r)).bind(function(i){return i.start().fold(g.none,function(i,u){return kl(e,i,u).bind(function(u){return t.adjuster(e,i,u,n,r).fold(g.none,function(r){return $l(e,t,n,r,o-1)})}).orThunk(function(){return g.some(r)})},g.none)}));var i,u,a,c,l,s},Jl=function(e,t,n){var r,o,i,u=e.move(n,5),a=$l(t,e,n,u,100).getOr(u);return(r=e,o=a,i=t,r.point(o)>i.getInnerHeight()?g.some(r.point(o)-i.getInnerHeight()):r.point(o)<0?g.some(-r.point(o)):g.none()).fold(function(){return t.situsFromPoint(a.left(),e.point(a))},function(n){return t.scrollBy(0,n),t.situsFromPoint(a.left(),e.point(a)-n)})},Ql={tryUp:c.curry(Jl,Yl),tryDown:c.curry(Jl,Kl),ieTryUp:function(e,t){return e.situsFromPoint(t.left(),t.top()-5)},ieTryDown:function(e,t){return e.situsFromPoint(t.left(),t.bottom()+5)},getJumpSize:c.constant(5)},Zl=sr([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),es=function(e){return It.closest(e,"tr")},ts={verify:function(e,t,n,r,o,i,u){return It.closest(r,"td,th",u).bind(function(n){return It.closest(t,"td,th",u).map(function(t){return Ye.eq(n,t)?Ye.eq(r,n)&&bn(n)===o?i(t):Zl.none("in same cell"):Fn(es,[n,t]).fold(function(){return o=t,u=n,a=(r=e).getRect(o),(c=r.getRect(u)).right>a.left&&c.left<a.right?Zl.success():i(t);var r,o,u,a,c},function(e){return i(t)})})}).getOr(Zl.none("default"))},cata:function(e,t,n,r,o){return e.fold(t,n,r,o)},adt:Zl},ns={point:q.immutable("element","offset"),delta:q.immutable("element","deltaOffset"),range:q.immutable("element","start","finish"),points:q.immutable("begin","end"),text:q.immutable("element","text")},rs=(q.immutable("ancestor","descendants","element","index"),q.immutable("parent","children","element","index")),os=function(e,t){return A.findIndex(e,c.curry(Ye.eq,t))},is=function(e){return nt.parent(e).bind(function(t){var n=nt.children(t);return os(n,e).map(function(r){return rs(t,n,e,r)})})},us=function(e){return"br"===ft.name(e)},as=function(e,t,n){return t(e,n).bind(function(e){return ft.isText(e)&&0===pn.get(e).trim().length?as(e,t,n):g.some(e)})},cs=function(e,t,n,r){return(o=t,i=n,nt.child(o,i).filter(us).orThunk(function(){return nt.child(o,i-1).filter(us)})).bind(function(t){return r.traverse(t).fold(function(){return as(t,r.gather,e).map(r.relative)},function(e){return is(e).map(function(e){return pc.on(e.parent(),e.index())})})});var o,i},ls=function(e,t,n,r){var o,i,u;return(us(t)?(o=e,i=t,(u=r).traverse(i).orThunk(function(){return as(i,u.gather,o)}).map(u.relative)):cs(e,t,n,r)).map(function(e){return{start:c.constant(e),finish:c.constant(e)}})},ss=function(e){return ts.cata(e,function(e){return g.none()},function(){return g.none()},function(e){return g.some(ns.point(e,0))},function(e){return g.some(ns.point(e,bn(e)))})},fs=Ue.detect(),ds=function(e,t,n,r,o,i){return 0===i?g.none():ps(e,t,n,r,o).bind(function(u){var a=e.fromSitus(u),c=ts.verify(e,n,r,a.finish(),a.foffset(),o.failure,t);return ts.cata(c,function(){return g.none()},function(){return g.some(u)},function(u){return Ye.eq(n,u)&&0===r?ms(e,n,r,Rl.moveUp,o):ds(e,t,u,0,o,i-1)},function(u){return Ye.eq(n,u)&&r===bn(u)?ms(e,n,r,Rl.moveDown,o):ds(e,t,u,bn(u),o,i-1)})})},ms=function(e,t,n,r,o){return Al(e,t,n).bind(function(t){return gs(e,o,r(t,Ql.getJumpSize()))})},gs=function(e,t,n){return fs.browser.isChrome()||fs.browser.isSafari()||fs.browser.isFirefox()||fs.browser.isEdge()?t.otherRetry(e,n):fs.browser.isIE()?t.ieRetry(e,n):g.none()},ps=function(e,t,n,r,o){return Al(e,n,r).bind(function(t){return gs(e,o,t)})},hs=function(e,t,n){return(r=e,o=t,i=n,r.getSelection().bind(function(e){return ls(o,e.finish(),e.foffset(),i).fold(function(){return g.some(ns.point(e.finish(),e.foffset()))},function(t){var n=r.fromSitus(t),u=ts.verify(r,e.finish(),e.foffset(),n.finish(),n.foffset(),i.failure,o);return ss(u)})})).bind(function(r){return ds(e,t,r.element(),r.offset(),n,20).map(e.fromSitus)});var r,o,i},vs=function(e,t,n){return Bt.ancestor(e,t,n).isSome()},bs=Ue.detect(),ws=function(e,t,n,r,o){return It.closest(r,"td,th",t).bind(function(r){return It.closest(r,"table",t).bind(function(i){return u=i,vs(o,function(e){return nt.parent(e).exists(function(e){return Ye.eq(e,u)})})?hs(e,t,n).bind(function(e){return It.closest(e.finish(),"td,th",t).map(function(t){return{start:c.constant(r),finish:c.constant(t),range:c.constant(e)}})}):g.none();var u})})},ys=function(e,t,n,r,o,i){return bs.browser.isIE()?g.none():i(r,t).orThunk(function(){return ws(e,t,n,r,o).map(function(e){var t=e.range();return ll.response(g.some(gl.makeSitus(t.start(),t.soffset(),t.finish(),t.foffset())),!0)})})},xs=function(e,t,n,r,o,i,u){return ws(e,n,r,o,i).bind(function(e){return Sl.detect(t,n,e.start(),e.finish(),u)})},Ss=function(e,t){return It.closest(e,"tr",t).bind(function(e){return It.closest(e,"table",t).bind(function(n){var r=St.descendants(n,"tr");return Ye.eq(e,r[0])?Ul.seekLeft(n,function(e){return xn.last(e).isSome()},t).map(function(e){var t=bn(e);return ll.response(g.some(gl.makeSitus(e,t,e,t)),!0)}):g.none()})})},Cs=function(e,t){return It.closest(e,"tr",t).bind(function(e){return It.closest(e,"table",t).bind(function(n){var r=St.descendants(n,"tr");return Ye.eq(e,r[r.length-1])?Ul.seekRight(n,function(e){return xn.first(e).isSome()},t).map(function(e){return ll.response(g.some(gl.makeSitus(e,0,e,0)),!0)}):g.none()})})},Rs=function(e,t){return It.closest(e,"td,th",t)},Ts={down:{traverse:nt.nextSibling,gather:Ul.after,relative:pc.before,otherRetry:Ql.tryDown,ieRetry:Ql.ieTryDown,failure:ts.adt.failedDown},up:{traverse:nt.prevSibling,gather:Ul.before,relative:pc.before,otherRetry:Ql.tryUp,ieRetry:Ql.ieTryUp,failure:ts.adt.failedUp}},Ds=q.immutable("rows","cols"),As={mouse:function(e,t,n,r){var o,i,u,a,c,l,s=yl(e),f=(o=s,i=t,u=n,a=r,c=g.none(),l=function(){c=g.none()},{mousedown:function(e){a.clear(i),c=Rs(e.target(),u)},mouseover:function(e){c.each(function(t){a.clear(i),Rs(e.target(),u).each(function(e){or.identify(t,e,u).each(function(n){var r=n.boxes().getOr([]);(r.length>1||1===r.length&&!Ye.eq(t,e))&&(a.selectRange(i,r,n.start(),n.finish()),o.selectContents(e))})})})},mouseup:function(){c.each(l)}});return{mousedown:f.mousedown,mouseover:f.mouseover,mouseup:f.mouseup}},keyboard:function(e,t,n,r){var o=yl(e),i=function(){return r.clear(t),g.none()};return{keydown:function(e,u,a,l,s,f){var d=e.raw().which,m=!0===e.raw().shiftKey;return or.retrieve(t,r.selectedSelector()).fold(function(){return ml.isDown(d)&&m?c.curry(xs,o,t,n,Ts.down,l,u,r.selectRange):ml.isUp(d)&&m?c.curry(xs,o,t,n,Ts.up,l,u,r.selectRange):ml.isDown(d)?c.curry(ys,o,n,Ts.down,l,u,Cs):ml.isUp(d)?c.curry(ys,o,n,Ts.up,l,u,Ss):g.none},function(e){var n=function(n){return function(){return Wo(n,function(n){return Sl.update(n.rows(),n.cols(),t,e,r)}).fold(function(){return or.getEdges(t,r.firstSelectedSelector(),r.lastSelectedSelector()).map(function(e){var n=ml.isDown(d)||f.isForward(d)?pc.after:pc.before;return o.setRelativeSelection(pc.on(e.first(),0),n(e.table())),r.clear(t),ll.response(g.none(),!0)})},function(e){return g.some(ll.response(g.none(),!0))})}};return ml.isDown(d)&&m?n([Ds(1,0)]):ml.isUp(d)&&m?n([Ds(-1,0)]):f.isBackward(d)&&m?n([Ds(0,-1),Ds(-1,0)]):f.isForward(d)&&m?n([Ds(0,1),Ds(1,0)]):ml.isNavigation(d)&&!1===m?i:g.none})()},keyup:function(e,o,i,u,a){return or.retrieve(t,r.selectedSelector()).fold(function(){var c=e.raw().which;return 0==(!0===e.raw().shiftKey)?g.none():ml.isNavigation(c)?Sl.sync(t,n,o,i,u,a,r.selectRange):g.none()},g.none)}}}},ks=function(e,t){A.each(t,function(t){pi.remove(e,t)})},Ns=function(e){return function(t){pi.add(t,e)}},Os=function(e){return function(t){ks(t,e)}},Es={byClass:function(e){var t=Ns(e.selected()),n=Os([e.selected(),e.lastSelected(),e.firstSelected()]),r=function(t){var r=St.descendants(t,e.selectedSelector());A.each(r,n)};return{clear:r,selectRange:function(n,o,i,u){r(n),A.each(o,t),pi.add(i,e.firstSelected()),pi.add(u,e.lastSelected())},selectedSelector:e.selectedSelector,firstSelectedSelector:e.firstSelectedSelector,lastSelectedSelector:e.lastSelectedSelector}},byAttr:function(e){var t=function(t){ht.remove(t,e.selected()),ht.remove(t,e.firstSelected()),ht.remove(t,e.lastSelected())},n=function(t){ht.set(t,e.selected(),"1")},r=function(n){var r=St.descendants(n,e.selectedSelector());A.each(r,t)};return{clear:r,selectRange:function(t,o,i,u){r(t),A.each(o,n),ht.set(i,e.firstSelected(),"1"),ht.set(u,e.lastSelected(),"1")},selectedSelector:e.selectedSelector,firstSelectedSelector:e.firstSelectedSelector,lastSelectedSelector:e.lastSelectedSelector}}};function Bs(e,t){var n=q.immutableBag(["mousedown","mouseover","mouseup","keyup","keydown"],[]),r=g.none(),o=Es.byAttr(lr);return e.on("init",function(i){var u=e.getWin(),a=Hu.getBody(e),l=Hu.getIsRoot(e),s=As.mouse(u,a,l,o),f=As.keyboard(u,a,l,o),d=function(t,n){!0===t.raw().shiftKey&&(n.kill()&&t.kill(),n.selection().each(function(t){var n=bc.relative(t.start(),t.finish()),r=Nc.asLtrRange(u,n);e.selection.setRng(r)}))},m=function(t){var n=v(t);if(n.raw().shiftKey&&ml.isNavigation(n.raw().which)){var r=e.selection.getRng(),o=X.fromDom(r.startContainer),i=X.fromDom(r.endContainer);f.keyup(n,o,r.startOffset,i,r.endOffset).each(function(e){d(n,e)})}},p=function(e){return!(ht.has(e,"data-mce-bogus")||"br"===ft.name(e)||ft.isText(e)&&0===pn.get(e).length)},h=function(n){var r,o,i,u=v(n);t().each(function(e){e.hideBars()}),40===n.which&&(r=X.fromDom(e.getBody()),o=nt.lastChild(r),i=function(e){return nt.prevSibling(e).bind(function(e){return p(e)?g.some(e):i(e)})},o.bind(function(e){return p(e)?g.some(e):i(e)})).each(function(t){"table"===ft.name(t)&&(ea(e)?e.dom.add(e.getBody(),ea(e),ia(e),"<br/>"):e.dom.add(e.getBody(),"br"))});var a=e.selection.getRng(),c=X.fromDom(e.selection.getStart()),l=X.fromDom(a.startContainer),s=X.fromDom(a.endContainer),m=Yu.directionAt(c).isRtl()?ml.rtl:ml.ltr;f.keydown(u,l,a.startOffset,s,a.endOffset,m).each(function(e){d(u,e)}),t().each(function(e){e.showBars()})},v=function(e){var t=X.fromDom(e.target),n=function(){e.stopPropagation()},r=function(){e.preventDefault()},o=c.compose(r,n);return{target:c.constant(t),x:c.constant(e.x),y:c.constant(e.y),stop:n,prevent:r,kill:o,raw:c.constant(e)}},b=function(e){return 0===e.button},w=function(e){b(e)&&s.mousedown(v(e))},y=function(e){var t;((t=e).buttons===undefined||0!=(1&t.buttons))&&s.mouseover(v(e))},x=function(e){b&&s.mouseup(v(e))};e.on("mousedown",w),e.on("mouseover",y),e.on("mouseup",x),e.on("keyup",m),e.on("keydown",h),e.on("nodechange",function(){var t=e.selection,n=X.fromDom(t.getStart()),r=X.fromDom(t.getEnd()),i=zt.table(n),u=zt.table(r);i.bind(function(e){return u.bind(function(t){return Ye.eq(e,t)?g.some(!0):g.none()})}).fold(function(){o.clear(a)},c.noop)}),r=g.some(n({mousedown:w,mouseover:y,mouseup:x,keyup:m,keydown:h}))}),{clear:o.clear,destroy:function(){r.each(function(e){})}}}var Ps=pa.each,Is={addButtons:function(e){var t=[];function n(t){return function(){e.execCommand(t)}}Ps("inserttable tableprops deletetable | cell row column".split(" "),function(n){"|"===n?t.push({text:"-"}):t.push(e.menuItems[n])}),e.addButton("table",{type:"menubutton",title:"Table",menu:t}),e.addButton("tableprops",{title:"Table properties",onclick:c.curry(Pa,e,!0),icon:"table"}),e.addButton("tabledelete",{title:"Delete table",onclick:n("mceTableDelete")}),e.addButton("tablecellprops",{title:"Cell properties",onclick:n("mceTableCellProps")}),e.addButton("tablemergecells",{title:"Merge cells",onclick:n("mceTableMergeCells")}),e.addButton("tablesplitcells",{title:"Split cell",onclick:n("mceTableSplitCells")}),e.addButton("tableinsertrowbefore",{title:"Insert row before",onclick:n("mceTableInsertRowBefore")}),e.addButton("tableinsertrowafter",{title:"Insert row after",onclick:n("mceTableInsertRowAfter")}),e.addButton("tabledeleterow",{title:"Delete row",onclick:n("mceTableDeleteRow")}),e.addButton("tablerowprops",{title:"Row properties",onclick:n("mceTableRowProps")}),e.addButton("tablecutrow",{title:"Cut row",onclick:n("mceTableCutRow")}),e.addButton("tablecopyrow",{title:"Copy row",onclick:n("mceTableCopyRow")}),e.addButton("tablepasterowbefore",{title:"Paste row before",onclick:n("mceTablePasteRowBefore")}),e.addButton("tablepasterowafter",{title:"Paste row after",onclick:n("mceTablePasteRowAfter")}),e.addButton("tableinsertcolbefore",{title:"Insert column before",onclick:n("mceTableInsertColBefore")}),e.addButton("tableinsertcolafter",{title:"Insert column after",onclick:n("mceTableInsertColAfter")}),e.addButton("tabledeletecol",{title:"Delete column",onclick:n("mceTableDeleteCol")})},addToolbars:function(e){var t,n=""===(t=e.getParam("table_toolbar",Ku))||!1===t?[]:W.isString(t)?t.split(/[ ,]/):W.isArray(t)?t:[];n.length>0&&e.addContextToolbar(function(t){return e.dom.is(t,"table")&&e.getBody().contains(t)},n.join(" "))}},Ws={addMenuItems:function(e,t){var n=g.none(),r=[],o=[],i=[],u=[],a=function(e){e.disabled(!0)},l=function(e){e.disabled(!1)},s=function(){var e=this;r.push(e),n.fold(function(){a(e)},function(t){l(e)})},f=function(){var e=this;o.push(e),n.fold(function(){a(e)},function(t){l(e)})};e.on("init",function(){e.on("nodechange",function(c){var s=g.from(e.dom.getParent(e.selection.getStart(),"th,td"));(n=s.bind(function(e){var n=X.fromDom(e);return zt.table(n).map(function(e){return wr.forMenu(t,e,n)})})).fold(function(){A.each(r,a),A.each(o,a),A.each(i,a),A.each(u,a)},function(e){A.each(r,l),A.each(o,l),A.each(i,function(t){t.disabled(e.mergable().isNone())}),A.each(u,function(t){t.disabled(e.unmergable().isNone())})})})});var d=function(e,t,n,r){var o,i,u,a,c,l=r.getEl().getElementsByTagName("table")[0],s=r.isRtl()||"tl-tr"===r.parent().rel;for(l.nextSibling.innerHTML=t+1+" x "+(n+1),s&&(t=9-t),i=0;i<10;i++)for(o=0;o<10;o++)a=l.rows[i].childNodes[o].firstChild,c=(s?o>=t:o<=t)&&i<=n,e.dom.toggleClass(a,"mce-active",c),c&&(u=a);return u.parentNode},m=!1===e.getParam("table_grid",!0,"boolean")?{text:"Table",icon:"table",context:"table",onclick:c.curry(Pa,e)}:{text:"Table",icon:"table",context:"table",ariaHideMenu:!0,onclick:function(t){t.aria&&(this.parent().hideAll(),t.stopImmediatePropagation(),Pa(e))},onshow:function(){d(e,0,0,this.menu.items()[0])},onhide:function(){var t=this.menu.items()[0].getEl().getElementsByTagName("a");e.dom.removeClass(t,"mce-active"),e.dom.addClass(t[0],"mce-active")},menu:[{type:"container",html:function(){var e="";e='<table role="grid" class="mce-grid mce-grid-border" aria-readonly="true">';for(var t=0;t<10;t++){e+="<tr>";for(var n=0;n<10;n++)e+='<td role="gridcell" tabindex="-1"><a id="mcegrid'+(10*t+n)+'" href="#" data-mce-x="'+n+'" data-mce-y="'+t+'"></a></td>';e+="</tr>"}return e+="</table>",e+='<div class="mce-text-center" role="presentation">1 x 1</div>'}(),onPostRender:function(){this.lastX=this.lastY=0},onmousemove:function(t){var n,r,o=t.target;"A"===o.tagName.toUpperCase()&&(n=parseInt(o.getAttribute("data-mce-x"),10),r=parseInt(o.getAttribute("data-mce-y"),10),(this.isRtl()||"tl-tr"===this.parent().rel)&&(n=9-n),n===this.lastX&&r===this.lastY||(d(e,n,r,t.control),this.lastX=n,this.lastY=r))},onclick:function(t){var n=this;"A"===t.target.tagName.toUpperCase()&&(t.preventDefault(),t.stopPropagation(),n.parent().cancel(),e.undoManager.transact(function(){Oa(e,n.lastX+1,n.lastY+1)}),e.addVisual())}}]};function p(t){return function(){e.execCommand(t)}}var h={text:"Table properties",context:"table",onPostRender:s,onclick:c.curry(Pa,e,!0)},v={text:"Delete table",context:"table",onPostRender:s,cmd:"mceTableDelete"},b={text:"Row",context:"table",menu:[{text:"Insert row before",onclick:p("mceTableInsertRowBefore"),onPostRender:f},{text:"Insert row after",onclick:p("mceTableInsertRowAfter"),onPostRender:f},{text:"Delete row",onclick:p("mceTableDeleteRow"),onPostRender:f},{text:"Row properties",onclick:p("mceTableRowProps"),onPostRender:f},{text:"-"},{text:"Cut row",onclick:p("mceTableCutRow"),onPostRender:f},{text:"Copy row",onclick:p("mceTableCopyRow"),onPostRender:f},{text:"Paste row before",onclick:p("mceTablePasteRowBefore"),onPostRender:f},{text:"Paste row after",onclick:p("mceTablePasteRowAfter"),onPostRender:f}]},w={text:"Column",context:"table",menu:[{text:"Insert column before",onclick:p("mceTableInsertColBefore"),onPostRender:f},{text:"Insert column after",onclick:p("mceTableInsertColAfter"),onPostRender:f},{text:"Delete column",onclick:p("mceTableDeleteCol"),onPostRender:f}]},y={separator:"before",text:"Cell",context:"table",menu:[{text:"Cell properties",onclick:p("mceTableCellProps"),onPostRender:f},{text:"Merge cells",onclick:p("mceTableMergeCells"),onPostRender:function(){var e=this;i.push(e),n.fold(function(){a(e)},function(t){e.disabled(t.mergable().isNone())})}},{text:"Split cell",onclick:p("mceTableSplitCells"),onPostRender:function(){var e=this;u.push(e),n.fold(function(){a(e)},function(t){e.disabled(t.unmergable().isNone())})}}]};e.addMenuItem("inserttable",m),e.addMenuItem("tableprops",h),e.addMenuItem("deletetable",v),e.addMenuItem("row",b),e.addMenuItem("column",w),e.addMenuItem("cell",y)}},Ms=function(e,t){return{insertTable:function(t,n){return Oa(e,t,n)},setClipboardRows:function(e){return n=e,r=t,o=A.map(n,X.fromDom),void r.set(g.from(o));var n,r,o},getClipboardRows:function(){return t.get().fold(function(){},function(e){return A.map(e,function(e){return e.dom()})})}}};u.add("table",function(e){var t,n,r,o,i,u,a=ic(e),l=Bs(e,a.lazyResize),s=(t=e,n=a.lazyWire,r=function(e){return"table"===ft.name(Hu.getBody(e))},o=sa(t),{deleteRow:(i=function(e,n,r,i){return function(u,a){var c=St.descendants(u,"td[data-mce-style],th[data-mce-style]");A.each(c,function(e){ht.remove(e,"data-mce-style")});var l=i(),s=X.fromDom(t.getDoc()),f=uo(Yu.directionAt),d=Dn.cellOperations(r,s,o);return n(u)?e(l,u,a,d,f).bind(function(e){return A.each(e.newRows(),function(e){da(t,e.dom())}),A.each(e.newCells(),function(e){ma(t,e.dom())}),e.cursor().map(function(e){var n=t.dom.createRng();return n.setStart(e.dom(),0),n.setEnd(e.dom(),0),n})}):g.none()}})(ju.eraseRows,function(e){var n=ao.getGridSize(e);return!1===r(t)||n.rows()>1},c.noop,n),deleteColumn:i(ju.eraseColumns,function(e){var n=ao.getGridSize(e);return!1===r(t)||n.columns()>1},c.noop,n),insertRowsBefore:i(ju.insertRowsBefore,c.always,c.noop,n),insertRowsAfter:i(ju.insertRowsAfter,c.always,c.noop,n),insertColumnsBefore:i(ju.insertColumnsBefore,c.always,jr.halve,n),insertColumnsAfter:i(ju.insertColumnsAfter,c.always,jr.halve,n),mergeCells:i(ju.mergeCells,c.always,c.noop,n),unmergeCells:i(ju.unmergeCells,c.always,c.noop,n),pasteRowsBefore:i(ju.pasteRowsBefore,c.always,c.noop,n),pasteRowsAfter:i(ju.pasteRowsAfter,c.always,c.noop,n),pasteCells:i(ju.pasteCells,c.always,c.noop,n)}),f=(u=e,{get:function(){var e=Hu.getBody(u);return ir.retrieve(e,lr.selectedSelector()).fold(function(){return u.selection.getStart()===undefined?dr.none():dr.single(u.selection)},function(e){return dr.multiple(e)})}}),d=co(g.none());return Wa.registerCommands(e,s,l,f,d),yr.registerEvents(e,f,s,l),Ws.addMenuItems(e,f),Is.addButtons(e),Is.addToolbars(e),e.on("PreInit",function(){e.serializer.addTempAttr(lr.firstSelected()),e.serializer.addTempAttr(lr.lastSelected())}),Zu(e)&&e.on("keydown",function(t){cl.handle(t,e,s,a.lazyWire)}),e.on("remove",function(){a.destroy(),l.destroy()}),Ms(e,d)})}();
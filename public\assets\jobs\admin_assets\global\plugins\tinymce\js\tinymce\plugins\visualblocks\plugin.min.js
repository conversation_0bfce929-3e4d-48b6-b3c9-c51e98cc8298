!function(){"use strict";var e=function(t){var n=t,o=function(){return n};return{get:o,set:function(e){n=e},clone:function(){return e(o())}}},t=tinymce.util.Tools.resolve("tinymce.PluginManager"),n=function(e,t){e.fire("VisualBlocks",{state:t})},o=function(e){return e.getParam("visualblocks_default_state",!1)},s=function(e){return e.settings.visualblocks_content_css},i=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),c=tinymce.util.Tools.resolve("tinymce.util.Tools"),l=i.DOM.uniqueId(),u=function(e,t){var n=c.toArray(e.getElementsByTagName("link"));if(0===c.grep(n,function(e){return e.id===l}).length){var o=i.DOM.create("link",{id:l,rel:"stylesheet",href:t});e.getElementsByTagName("head")[0].appendChild(o)}},a=function(e,t,o){var i=e.dom,c=s(e);u(e.getDoc(),c||t+"/css/visualblocks.css"),i.toggleClass(e.getBody(),"mce-visualblocks"),o.set(!o.get()),n(e,o.get())},r=function(e,t,n){e.addCommand("mceVisualBlocks",function(){a(e,t,n)})},m=function(e,t,n){e.on("PreviewFormats AfterPreviewFormats",function(t){n.get()&&e.dom.toggleClass(e.getBody(),"mce-visualblocks","afterpreviewformats"===t.type)}),e.on("init",function(){o(e)&&a(e,t,n)}),e.on("remove",function(){e.dom.removeClass(e.getBody(),"mce-visualblocks")})},f=function(e,t){return function(n){var o=n.control;o.active(t.get()),e.on("VisualBlocks",function(e){o.active(e.state)})}},d=function(e,t){e.addButton("visualblocks",{active:!1,title:"Show blocks",cmd:"mceVisualBlocks",onPostRender:f(e,t)}),e.addMenuItem("visualblocks",{text:"Show blocks",cmd:"mceVisualBlocks",onPostRender:f(e,t),selectable:!0,context:"view",prependToContext:!0})};t.add("visualblocks",function(t,n){var o=e(!1);r(t,n,o),d(t,o),m(t,n,o)})}();
<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/**
 * Demo Data Service
 * 
 * Handles data transformation and anonymization for system viewer accounts
 * Provides different data modes: real data vs anonymized demo data
 */
final class DemoDataService
{
    private array $demoConfig;

    public function __construct()
    {
        $this->demoConfig = [
            'student_prefix' => 'Student',
            'teacher_prefix' => 'Teacher',
            'employee_prefix' => 'Employee',
            'demo_domain' => 'demo.example.com',
            'demo_phone' => '******-DEMO',
            'demo_address' => 'Demo Address, Demo City',
        ];
    }

    /**
     * Check if current user should see demo data
     */
    public function shouldUseDemoData(): bool
    {
        if (!Auth::guard('employee')->check()) {
            return false;
        }

        $user = Auth::guard('employee')->user();
        
        // Only apply to system viewers
        if (!$user->hasRole('system_viewer_' . config('organization_id') . '_')) {
            return false;
        }

        // Check session preference
        return session('viewer_demo_mode', false);
    }

    /**
     * Apply demo data transformation to a model collection
     */
    public function transformModelData($model, array $data): array
    {
        if (!$this->shouldUseDemoData()) {
            return $data;
        }

        return $this->applyDemoTransformation($model, $data);
    }

    /**
     * Apply demo data transformation to query results
     */
    public function transformQueryData(string $table, array $data): array
    {
        if (!$this->shouldUseDemoData()) {
            return $data;
        }

        $transformations = $this->getTableTransformations($table);
        
        return array_map(function ($row) use ($transformations) {
            return $this->applyFieldTransformations($row, $transformations);
        }, $data);
    }

    /**
     * Get demo data transformations for specific table
     */
    private function getTableTransformations(string $table): array
    {
        $transformations = [
            'students' => [
                'full_name' => fn($id) => $this->demoConfig['student_prefix'] . ' ' . str_pad($id, 4, '0', STR_PAD_LEFT),
                'email' => fn($id) => 'student' . $id . '@' . $this->demoConfig['demo_domain'],
                'phone' => fn($id) => $this->demoConfig['demo_phone'],
                'address' => fn($id) => $this->demoConfig['demo_address'],
                'guardian_name' => fn($id) => 'Guardian ' . str_pad($id, 4, '0', STR_PAD_LEFT),
                'guardian_phone' => fn($id) => $this->demoConfig['demo_phone'],
                'guardian_email' => fn($id) => 'guardian' . $id . '@' . $this->demoConfig['demo_domain'],
            ],
            'employees' => [
                'full_name' => fn($id) => $this->demoConfig['employee_prefix'] . ' ' . str_pad($id, 4, '0', STR_PAD_LEFT),
                'email' => fn($id) => 'employee' . $id . '@' . $this->demoConfig['demo_domain'],
                'phone' => fn($id) => $this->demoConfig['demo_phone'],
                'address' => fn($id) => $this->demoConfig['demo_address'],
                'father_name' => fn($id) => 'Father ' . str_pad($id, 4, '0', STR_PAD_LEFT),
                'mother_name' => fn($id) => 'Mother ' . str_pad($id, 4, '0', STR_PAD_LEFT),
                'national_id_no' => fn($id) => 'DEMO' . str_pad($id, 6, '0', STR_PAD_LEFT),
                'bank_account_no' => fn($id) => 'DEMO' . str_pad($id, 10, '0', STR_PAD_LEFT),
            ],
            'teachers' => [
                'full_name' => fn($id) => $this->demoConfig['teacher_prefix'] . ' ' . str_pad($id, 4, '0', STR_PAD_LEFT),
                'email' => fn($id) => 'teacher' . $id . '@' . $this->demoConfig['demo_domain'],
                'phone' => fn($id) => $this->demoConfig['demo_phone'],
                'address' => fn($id) => $this->demoConfig['demo_address'],
            ],
            'companies' => [
                'name' => fn($id) => 'Demo Company ' . str_pad($id, 3, '0', STR_PAD_LEFT),
                'email' => fn($id) => 'company' . $id . '@' . $this->demoConfig['demo_domain'],
                'phone' => fn($id) => $this->demoConfig['demo_phone'],
                'address' => fn($id) => $this->demoConfig['demo_address'],
            ],
        ];

        return $transformations[$table] ?? [];
    }

    /**
     * Apply field transformations to a data row
     */
    private function applyFieldTransformations(array $row, array $transformations): array
    {
        foreach ($transformations as $field => $transformer) {
            if (isset($row[$field]) && isset($row['id'])) {
                $row[$field] = $transformer($row['id']);
            }
        }

        return $row;
    }

    /**
     * Apply demo transformation based on model type
     */
    private function applyDemoTransformation(string $model, array $data): array
    {
        $modelTransformations = [
            'Student' => $this->getTableTransformations('students'),
            'Employee' => $this->getTableTransformations('employees'),
            'Teacher' => $this->getTableTransformations('teachers'),
            'Company' => $this->getTableTransformations('companies'),
        ];

        $modelName = class_basename($model);
        $transformations = $modelTransformations[$modelName] ?? [];

        if (empty($transformations)) {
            return $data;
        }

        return array_map(function ($item) use ($transformations) {
            if (is_array($item)) {
                return $this->applyFieldTransformations($item, $transformations);
            }
            return $item;
        }, $data);
    }

    /**
     * Get anonymized data for a specific model
     */
    public function getAnonymizedModelData(string $modelClass, array $conditions = []): array
    {
        if (!$this->shouldUseDemoData()) {
            return [];
        }

        $cacheKey = 'demo_data_' . md5($modelClass . serialize($conditions));
        
        return Cache::remember($cacheKey, 300, function () use ($modelClass, $conditions) {
            // This would typically generate realistic demo data
            // For now, return basic structure
            return $this->generateDemoData($modelClass, $conditions);
        });
    }

    /**
     * Generate demo data for a model
     */
    private function generateDemoData(string $modelClass, array $conditions): array
    {
        $modelName = class_basename($modelClass);
        
        switch ($modelName) {
            case 'Student':
                return $this->generateStudentDemoData($conditions);
            case 'Employee':
                return $this->generateEmployeeDemoData($conditions);
            case 'Teacher':
                return $this->generateTeacherDemoData($conditions);
            default:
                return [];
        }
    }

    /**
     * Generate demo student data
     */
    private function generateStudentDemoData(array $conditions): array
    {
        $students = [];
        $count = $conditions['limit'] ?? 10;

        for ($i = 1; $i <= $count; $i++) {
            $students[] = [
                'id' => $i,
                'full_name' => 'Student ' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'email' => 'student' . $i . '@' . $this->demoConfig['demo_domain'],
                'phone' => $this->demoConfig['demo_phone'],
                'address' => $this->demoConfig['demo_address'],
                'class_name' => 'Class ' . (($i % 12) + 1),
                'section' => 'Section ' . chr(65 + ($i % 4)), // A, B, C, D
                'admission_date' => now()->subDays(rand(30, 365))->format('Y-m-d'),
                'status' => 'active',
            ];
        }

        return $students;
    }

    /**
     * Generate demo employee data
     */
    private function generateEmployeeDemoData(array $conditions): array
    {
        $employees = [];
        $count = $conditions['limit'] ?? 10;
        $departments = ['Administration', 'Teaching', 'Finance', 'IT', 'HR'];

        for ($i = 1; $i <= $count; $i++) {
            $employees[] = [
                'id' => $i,
                'full_name' => 'Employee ' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'email' => 'employee' . $i . '@' . $this->demoConfig['demo_domain'],
                'phone' => $this->demoConfig['demo_phone'],
                'address' => $this->demoConfig['demo_address'],
                'department' => $departments[$i % count($departments)],
                'designation' => 'Position ' . $i,
                'joining_date' => now()->subDays(rand(30, 1095))->format('Y-m-d'),
                'status' => 'active',
            ];
        }

        return $employees;
    }

    /**
     * Generate demo teacher data
     */
    private function generateTeacherDemoData(array $conditions): array
    {
        $teachers = [];
        $count = $conditions['limit'] ?? 10;
        $subjects = ['Mathematics', 'English', 'Science', 'History', 'Geography'];

        for ($i = 1; $i <= $count; $i++) {
            $teachers[] = [
                'id' => $i,
                'full_name' => 'Teacher ' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'email' => 'teacher' . $i . '@' . $this->demoConfig['demo_domain'],
                'phone' => $this->demoConfig['demo_phone'],
                'address' => $this->demoConfig['demo_address'],
                'subject' => $subjects[$i % count($subjects)],
                'qualification' => 'Demo Qualification',
                'experience' => rand(1, 20) . ' years',
                'status' => 'active',
            ];
        }

        return $teachers;
    }

    /**
     * Clear demo data cache
     */
    public function clearDemoCache(): void
    {
        Cache::flush(); // In production, you'd want to be more specific
    }
} 
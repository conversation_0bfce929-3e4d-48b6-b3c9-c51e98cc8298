# Leave Module - Logging Improvement To-Do List

## Overview
This document outlines necessary improvements to logging practices in the Leave module to enhance debugging capabilities, error traceability, and system transparency.

## Current Logging Assessment
The Leave module currently uses the `LogActivity` trait from the `UserActivityLog` module, which provides several logging methods:
- `errorLog()` - For logging errors (severity level 0)
- `successLog()` - For logging successful operations (severity level 1)
- `warningLog()` - For logging warnings (severity level 2)
- `infoLog()` - For logging informational messages (severity level 3)

However, there are several gaps in the current implementation:
1. Inconsistent application of logging across controllers and repositories
2. Lack of detailed context in log messages
3. Missing logs for critical validation errors
4. Insufficient error handling and logging in repositories
5. No logging for important state transitions in leave requests

## To-Do List

### 1. Controller-Level Logging Improvements

#### LeaveRequestController
- [ ] **Critical** - Add error logging in the `store()` method to capture validation failures with context:
  ```php
  catch (\Exception $e) {
      LogActivity::errorLog('Leave request failed: ' . $e->getMessage() . ' | User: ' . $request->user . ' | Type: ' . $request->leave_type_id);
      return response()->json(["message" => __('common.Something Went Wrong')], 503);
  }
  ```

- [ ] **Error** - Log denied leave requests due to eligibility issues:
  ```php
  // In store() method where leave_applicable_date is checked
  else {
      LogActivity::warningLog('Leave request denied - User not yet eligible | User ID: ' . $request->user);
      return response()->json([
          'error' => trans("leave.User is not Permitted for leave yet")
      ]);
  }
  ```

- [ ] **Informational** - Add detailed success logging with context after leave approval/rejection:
  ```php
  // After updating leave status
  LogActivity::infoLog('Leave request ' . ($status == 1 ? 'approved' : 'rejected') . ' | Request ID: ' . $apply_leave->id . ' | User: ' . $apply_leave->user->name);
  ```

- [ ] **Error** - Log when leave requests cannot be found:
  ```php
  // In methods accessing a specific leave request
  try {
      $apply_leave = $this->leaveRepository->find($id);
  } catch (\Exception $e) {
      LogActivity::errorLog('Failed to find leave request | ID: ' . $id . ' | ' . $e->getMessage());
      return response()->json(['error' => 'Leave request not found'], 404);
  }
  ```

#### LeaveTypeController & LeaveDefineController
- [ ] **Warning** - Log when a leave type is modified:
  ```php
  LogActivity::warningLog('Leave type modified | ID: ' . $id . ' | Name: ' . $request->name);
  ```
  
- [ ] **Critical** - Log when leave type deletions are attempted:
  ```php
  LogActivity::errorLog('Leave type deletion attempted | ID: ' . $id . ' | By: ' . Auth::user()->name);
  ```

### 2. Repository-Level Logging

#### LeaveRepository
- [ ] **Error** - Add error logging in `create()` method for failed creations:
  ```php
  try {
      // Creation logic
  } catch (\Exception $e) {
      LogActivity::errorLog('Failed to create leave request | ' . $e->getMessage());
      throw $e;
  }
  ```

- [ ] **Warning** - Log potential data integrity issues:
  ```php
  // When updating carry forward
  if ($apply_leave->total_days < 0) {
      LogActivity::warningLog('Negative leave days calculated | ID: ' . $apply_leave->id);
  }
  ```

- [ ] **Informational** - Log when updating critical leave data:
  ```php
  // In change_approval method
  LogActivity::infoLog('Leave request status changed | ID: ' . $data['id'] . ' | Status: ' . $data['status'] . ' | Approved by: ' . auth()->user()->name);
  ```

### 3. Request Validation Logging

#### EmployeeApplyLeaveStoreRequest & EmployeeEditLeaveStoreRequest
- [ ] **Warning** - Add custom validation failure logging:
  ```php
  // Add a method in the FormRequest class
  protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
  {
      LogActivity::warningLog('Leave validation failed | User: ' . auth()->user()->name . ' | Errors: ' . json_encode($validator->errors()->toArray()));
      parent::failedValidation($validator);
  }
  ```

### 4. General Logging Improvements

- [ ] **Trace** - Add entrance/exit logging for critical methods:
  ```php
  public function store(EmployeeApplyLeaveStoreRequest $request)
  {
      LogActivity::infoLog('Leave request initiated | User: ' . $request->user);
      // Method logic
      LogActivity::infoLog('Leave request processing completed | ID: ' . $apply_leave->id);
  }
  ```

- [ ] **Error** - Enhance exception handling with detailed context:
  ```php
  catch (\Exception $e) {
      LogActivity::errorLog("Leave module error: {$e->getMessage()} | File: {$e->getFile()} | Line: {$e->getLine()} | Context: " . json_encode([
          'user_id' => auth()->id(),
          'request_data' => $request->all()
      ]));
      // Error response
  }
  ```

### 5. Notifications & Events Logging

- [ ] **Informational** - Log when notifications are sent:
  ```php
  // After sending notifications
  LogActivity::infoLog('Leave notification sent | Recipients: ' . count($users) . ' | Subject: ' . $subject);
  ```

- [ ] **Warning** - Log when notification delivery fails:
  ```php
  try {
      // Notification sending logic
  } catch (\Exception $e) {
      LogActivity::warningLog('Leave notification failed | Error: ' . $e->getMessage());
      // Error handling
  }
  ```

## Implementation Priority

1. **Immediate (High Priority)**
   - Error logging for validation failures in controllers
   - Exception handling with detailed context in repositories
   - Logging for leave approval/rejection processes

2. **Short-term (Medium Priority)**
   - Custom validation failure logging
   - Notification delivery logging
   - State transition logging

3. **Long-term (Lower Priority)**
   - Method entry/exit logging
   - Data integrity check logging
   - UI interaction logging

## Benefits

- **Improved Debugging**: Faster identification and resolution of issues
- **Better Traceability**: Clear audit trail of leave-related actions
- **Enhanced Security**: Detection of unauthorized access attempts 
- **System Transparency**: Better visibility into system operations
- **Performance Monitoring**: Identification of slow operations 
<?php

namespace Modules\Education\Http\Controllers;


use App\IjazasanadMemorizationPlan;

use Illuminate\Http\Request;

use App\Http\Controllers\Controller;

class CommentIjazasanadLevel1MonthlyPlanController extends Controller
{

    public function update(Request $request)
    {

        $plan = IjazasanadMemorizationPlan::where('id', $request->get('id'))->update([
            'supervisor_comment' => $request->get('comment')
        ]);
        return response()->json(['message' => 'comment added to the plan']);


    }


    public function show(Request $request,$planId)
    {




        $plan = IjazasanadMemorizationPlan::where('id', $planId)->update([
            'supervisor_comment' => $request->get('comment')
        ]);
        return response()->json(['message' => 'comment added to the plan']);


    }
}

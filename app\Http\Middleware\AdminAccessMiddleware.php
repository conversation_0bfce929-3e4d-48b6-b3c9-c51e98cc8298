<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class AdminAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Check if user is authenticated
        if (Auth::check()) {
            // Check if user has the permission to manage sidebar settings
            if (Auth::user()->can('manage-sidebar-settings')) {
                return $next($request);
            }
        }
        
        // If not authorized, redirect to homepage with message
        return redirect('/workplace')->with('error', 'You do not have permission to access this page.');
    }
} 
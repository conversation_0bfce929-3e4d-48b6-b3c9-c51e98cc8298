<?php

namespace Modules\Admission\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use App\Setting;

class FormsSettingController extends Controller
{
    /**
     * Show the settings of student registeration form.
     * @return Response
     */
    public function student()
    {
        return view('admission::forms_setting.student');
    }

    /**
     * Show the settings of guardian registeration form.
     * @return Response
     */
    public function guardian()
    {
        return view('admission::forms_setting.guardian');
    }


    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(Request $request)
    {
        foreach ($request->except(['_token']) as $name => $value) {
            $setting = Setting::where('name', '=' , $name)
                            ->where('organization_id', '=' , config('organization_id'))->first();
            if($setting){
                $setting->value = $value;
            }else{
                $setting = new Setting();
                $setting->name = $name;
                $setting->value = $value;
                $setting->organization_id =  config('organization_id');
            }
            $setting->save();

        }

        flash('Form Settings Updated !!');

        return redirect()->back();
    }

}

<section>
	<div class="container">

		<div class="row">

			<!-- FORM -->
			<div class="col-md-8 col-sm-8">
				@if(isset($errors) && $errors->all() )
				<div class="alert alert-danger">
					<ul>
						@foreach($errors->all() as $error)
						<li>{{ $error }}</li>
						@endforeach
					</ul>
				</div>
				@endif @include('flash::message') {!! Form::open(['method' => 'POST','route' => 'sendContact' ]) !!}
				<fieldset>

					{{ csrf_field() }}
					<input type="hidden"  name="recaptcha" id="recaptcha">
					<div class="row">
						<div class="form-group">
							<div class="col-md-4">
								<label for="contact:name">{{ trans('common.full_name') }} *</label>
								<input required type="text" value="" class="form-control" name="name" id="contact:name">
							</div>
							<div class="col-md-4">
								<label for="contact:email">{{ trans('common.email') }} *</label>
								<input required type="email" value="" class="form-control" name="email" id="contact:email">
							</div>
							<div class="col-md-4">
								<label for="contact:phone">{{ trans('common.mobile') }}</label>
								<input type="text" value="" class="form-control" name="phone" id="contact:phone">
							</div>
						</div>
					</div>
					<div class="row">
						<div class="form-group">
							<div class="col-md-8">
								<label for="contact:subject">{{ trans('common.subject') }} *</label>
								<input required type="text" value="" class="form-control" name="subject" id="contact:subject">
							</div>
							<div class="col-md-4">
								<label for="contact_department">{{ trans('common.department') }}</label>
								<select class="form-control pointer" name="department">
									<option value="{{ trans('common.general') }}">{{ trans('common.general') }}</option>
									<option value="{{ trans('common.management') }}">{{ trans('common.management') }}</option>
									<option value="{{ trans('common.education') }}">{{ trans('common.education') }}</option>
									<option value="{{ trans('common.finance') }}">{{ trans('common.finance') }}</option>
									<option value="{{ trans('common.technical_support') }}">{{ trans('common.technical_support') }}</option>
								</select>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="form-group">
							<div class="col-md-12">
								<label for="contact:message">{{ trans('common.message') }} *</label>
								<textarea required maxlength="10000" rows="8" class="form-control" name="message" id="contact:message"></textarea>
							</div>
						</div>
					</div>

				</fieldset>

				<div class="row">
					<div class="col-md-12">
						<button type="submit" class="btn btn-primary">
							<i class="fa fa-check"></i> {{ trans('common.send') }}</button>
					</div>
				</div>
				{!! Form::close() !!}

			</div>
			<!-- /FORM -->


			<!-- INFO -->
			<div class="col-md-4 col-sm-4">

				<div id="map2" class="height-400 grayscale"></div>

				<hr />
				<p>
					<span class="block">
						<strong>
							<i class="fa fa-map-marker"></i> {{ trans('site::settings.address') }}:</strong> {{ config('settings.address') }}</span>
					<span class="block">
						<strong>
							<i class="fa fa-phone"></i> {{ trans('site::settings.phone') }}:</strong>
						<a href="tel:{{ config('settings.phone') }}">{{ config('settings.phone') }}</a>
					</span>
					<span class="block">
						<strong>
							<i class="fa fa-envelope"></i> {{ trans('site::settings.support_email') }}:</strong>
						<a href="mailto:{{ config('settings.support_email') }}">{{ config('settings.support_email') }}</a>
					</span>

					<span class="block">
						<strong>
							<i class="fa fa-gear"></i> {{ trans('site::settings.working_hours') }}</strong> : {{ config('settings.working_hours_'.config('app.locale')) }}
				</p>

			</div>
			<!-- /INFO -->

		</div>

	</div>
</section>
<!-- / -->
<?php

namespace Modules\Education\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;


/**
 * Class ClassReportStoreRequest
 * @package Modules\Communicate\Http\Requests
 *
 *
 */
class CentersUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'location' => 'required',
            'centerSupervisors.*' => 'required',
            'translate.*.name' => 'required'
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            'location.required' => 'Please enter the address for the Center',



        ];
    }
}

@startuml Core System Module Schema

!theme vibrant

entity "users" {
  * id: int
  --
  full_name: var<PERSON><PERSON>(250)
  email: varchar(250)
  password: varchar(100)
  usertype: varchar(210)
  organization_id: int
}

entity "roles" {
  * id: int
  --
  name: var<PERSON>r(255)
  guard_name: varchar(255)
}

entity "permissions" {
  * id: int
  --
  name: varchar(255)
  guard_name: varchar(255)
}

entity "model_has_roles" {
  * role_id: int <<FK>>
  * model_type: varchar(255)
  * model_id: int
}

entity "model_has_permissions" {
  * permission_id: int <<FK>>
  * model_type: varchar(255)
  * model_id: int
}

entity "role_has_permissions" {
  * permission_id: int <<FK>>
  * role_id: int <<FK>>
}

entity "settings" {
  * id: int
  --
  key: varchar(255)
  value: text
}

entity "modules" {
  * id: int
  --
  name: varchar(255)
  status: tinyint
}

entity "module_permissions" {
  * id: int
  --
  module_id: int <<FK>>
  name: varchar(255)
}

users ||--o{ model_has_roles : "has"
users ||--o{ model_has_permissions : "has"
roles ||--o{ model_has_roles : "assigned to"
roles ||--o{ role_has_permissions : "has"
permissions ||--o{ model_has_permissions : "assigned to"
permissions ||--o{ role_has_permissions : "granted to"
modules ||--o{ module_permissions : "has"

@enduml

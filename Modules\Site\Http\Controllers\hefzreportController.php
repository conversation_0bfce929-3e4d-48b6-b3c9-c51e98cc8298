<?php

namespace Modules\Site\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Student;
use App\Guardian;
use App\Center;
use App\Program;
use App\StudentHefzReport;
use App\MoshafSurah;
use App\StudentRevisionReport;
use App\Classes;
use App\ClassStudent;
use App\EvaluationSchemaOption;
use App\StudentAttendance;
Use DB;
use Carbon\Carbon;
class hefzreportController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($student_id = null)
    { $todayDate = date("Y-m");
        if($student_id){
            $student    = Student::findOrFail($student_id);
        }else{
            $student    = auth()->user();
        }
       
        $centers    = Center::where('status', '=', 'active')->get()->pluck('name', 'id');

        $programs   = Program::where('status', '=', 'active')->get()->pluck('title', 'id');
        $hefzrep=StudentHefzReport::where('student_id','=',$student->id)
        ->whereYear('class_time', Carbon::now()->year)
        ->whereMonth('class_time', Carbon::now()->month)
       ->get();
        $rev=StudentRevisionReport::where('student_id','=',$student->id)
        ->whereYear('class_time', Carbon::now()->year)
        ->whereMonth('class_time', Carbon::now()->month)
        ->get();
         $atten=StudentAttendance::where('student_id','=',$student->id)
         ->whereYear('class_time', Carbon::now()->year)
         ->whereMonth('class_time', Carbon::now()->month)
         ->get();
       //  fill selectbox
       $month= StudentHefzReport::where('student_id','=',$student->id)
      ->get()
      ->groupBy(function($val) {
      return Carbon::parse($val->class_time)->format('Y,M');
});

         $total=0;
         $ontime=0;
         $late=0;
         $absent=0;
         $exe=0;
        return view(theme_path("student.hefz_report"), compact('student',  'programs','hefzrep','rev','atten','ontime','late','absent','exe','total','todayDate','month'));
    }

    



public function action(Request $request,$student_id = null)
{
    $month_id = $request->bymonth;
    if($student_id){
        $student    = Student::findOrFail($student_id);
    }else{
        $student    = auth()->user();
    }
    $centers    = Center::where('status', '=', 'active')->get()->pluck('name', 'id');

        $programs   = Program::where('status', '=', 'active')->get()->pluck('title', 'id');
    if($month_id!= ''){
        $months = date('m', strtotime($request->input('bymonth')));
        $years ="20".+ date('y', strtotime($request->input('bymonth')));

        $hefzrep=StudentHefzReport::where('student_id','=',$student->id)
        ->whereMonth('class_time', '=', $months)
       ->whereYear('class_time', '=', $years)
       ->get();
        $rev=StudentRevisionReport::where('student_id','=',$student->id)
        ->whereMonth('class_time', '=', $months)
        ->whereYear('class_time', '=', $years)
        ->get();
         $atten=StudentAttendance::where('student_id','=',$student->id)
         ->whereMonth('class_time', '=', $months)
         ->whereYear('class_time', '=', $years)
         ->get();
        
        
        }
        else {

       
            $hefzrep=StudentHefzReport::where('student_id','=',$student->id)
            ->whereYear('class_time', Carbon::now()->year)
            ->whereMonth('class_time', Carbon::now()->month)
           ->get();
            $rev=StudentRevisionReport::where('student_id','=',$student->id)
            ->whereYear('class_time', Carbon::now()->year)
            ->whereMonth('class_time', Carbon::now()->month)
            ->get();
             $atten=StudentAttendance::where('student_id','=',$student->id)
             ->whereYear('class_time', Carbon::now()->year)
             ->whereMonth('class_time', Carbon::now()->month)
             ->get();
        }
        $month= StudentHefzReport::where('student_id','=',$student->id)
        ->get()
        ->groupBy(function($val) {
        return Carbon::parse($val->class_time)->format('Y,M');});
       
                 $total=0;
                 $ontime=0;
                 $late=0;
                 $absent=0;
                 $exe=0;

                 
              // dd( $years,$months);
        return view(theme_path("student.hefz_report"), compact('student',  'programs','hefzrep','rev','atten','ontime','late','absent','exe','total','todayDate','month'));

}



    public function showforguardian($student_id)
    {   $student    = Student::findOrFail($student_id);
        $centers    = Center::where('status', '=', 'active')->get()->pluck('name', 'id');
    
        $programs   = Program::where('status', '=', 'active')->get()->pluck('title', 'id');
       
        if($student->status == "profile_completed" || $student->status == "new_admission" )
        {
            return view(theme_path("guardian.register_student"), compact('student','programs','centers'));
        }else{

            $todayDate = date("Y-m");
            if($student_id){
                $student    = Student::findOrFail($student_id);
            }
    
            $hefzrep=StudentHefzReport::where('student_id','=',$student->id)
            ->whereYear('class_time', Carbon::now()->year)
            ->whereMonth('class_time', Carbon::now()->month)
           ->get();
            $rev=StudentRevisionReport::where('student_id','=',$student->id)
            ->whereYear('class_time', Carbon::now()->year)
            ->whereMonth('class_time', Carbon::now()->month)
            ->get();
             $atten=StudentAttendance::where('student_id','=',$student->id)
             ->whereYear('class_time', Carbon::now()->year)
             ->whereMonth('class_time', Carbon::now()->month)
             ->get();
           //  fill selectbox
           $month= StudentHefzReport::where('student_id','=',$student->id)
          ->get()
          ->groupBy(function($val) {
          return Carbon::parse($val->class_time)->format('Y,M');
    });
    
             $total=0;
             $ontime=0;
             $late=0;
             $absent=0;
             $exe=0;
             $centers    = Center::where('status', '=', 'active')->get()->pluck('name', 'id');
    
            $programs   = Program::where('status', '=', 'active')->get()->pluck('title', 'id');
            return view(theme_path("guardian.hefz_report"), compact('student','programs','centers',  'hefzrep','rev','atten','ontime','late','absent','exe','total','todayDate','month'));
        }
       
    }




    public function gurdianaction(Request $request)
{
    $month_id = $request->bymonth;
    $student_id=$request->student_id;
   // dd($student_id);
    if($student_id){
        $student    = Student::findOrFail($student_id);
    }else{
        $student    = auth()->user();
    }
    $centers    = Center::where('status', '=', 'active')->get()->pluck('name', 'id');

        $programs   = Program::where('status', '=', 'active')->get()->pluck('title', 'id');
    if($month_id!= ''){
        $months = date('m', strtotime($request->input('bymonth')));
        $years ="20".+ date('y', strtotime($request->input('bymonth')));

        $hefzrep=StudentHefzReport::where('student_id','=',$student->id)
        ->whereMonth('class_time', '=', $months)
       ->whereYear('class_time', '=', $years)
       ->get();
        $rev=StudentRevisionReport::where('student_id','=',$student->id)
        ->whereMonth('class_time', '=', $months)
        ->whereYear('class_time', '=', $years)
        ->get();
         $atten=StudentAttendance::where('student_id','=',$student->id)
         ->whereMonth('class_time', '=', $months)
         ->whereYear('class_time', '=', $years)
         ->get();
        
        
        }
        else {

       
            $hefzrep=StudentHefzReport::where('student_id','=',$student->id)
            ->whereYear('class_time', Carbon::now()->year)
            ->whereMonth('class_time', Carbon::now()->month)
           ->get();
            $rev=StudentRevisionReport::where('student_id','=',$student->id)
            ->whereYear('class_time', Carbon::now()->year)
            ->whereMonth('class_time', Carbon::now()->month)
            ->get();
             $atten=StudentAttendance::where('student_id','=',$student->id)
             ->whereYear('class_time', Carbon::now()->year)
             ->whereMonth('class_time', Carbon::now()->month)
             ->get();
        }
        $month= StudentHefzReport::where('student_id','=',$student->id)
        ->get()
        ->groupBy(function($val) {
        return Carbon::parse($val->class_time)->format('Y,M');});
       
                 $total=0;
                 $ontime=0;
                 $late=0;
                 $absent=0;
                 $exe=0;

         //  dd($student_id);      
           
        return view(theme_path("guardian.hefz_report"), compact('student',  'programs','hefzrep','rev','atten','ontime','late','absent','exe','total','todayDate','month'));

}
}

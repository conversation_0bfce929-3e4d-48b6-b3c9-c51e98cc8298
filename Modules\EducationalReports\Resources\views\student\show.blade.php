@extends('layouts.hound')

@section('content')

<div>
    <div class="container-fluid">

        <!-- Row -->
        <div class="panel-heading clearfix">
            <h3 class="pull-left"> Class Information </h3> 
            <div class="pull-right">
                <a href="{{ url('/workplace/admission/students') }}" title="Back"><button class="btn btn-warning btn-xs txt-light"><i class="fa fa-arrow-left txt-light" aria-hidden="true"></i> Back</button></a>
                
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="panel panel-default card-view  pa-0">
                    <div class="panel-wrapper collapse in">
                        <div class="panel-body  pa-0">
                            <div class="profile-box">
                                <div class="profile-cover-pic">
                                </div>
                                <div class="profile-info text-center">
                                    <div class="profile-img-wrap"> 
                                        <img class="inline-block mb-10" src="{{ StudentImage::getStudentImageUrl($student) }}" alt="user"/>
                                        <div class="fileupload btn btn-default">
                                            <span class="btn-text">edit</span>
                                            <input class="upload" type="file">
                                        </div>
                                    </div>	
                                    <h5 class="block mt-10 mb-5 weight-500 capitalize-font txt-danger">{{ $student->full_name }}</h5>
                                    <h6 class="block capitalize-font pb-20">{{ $student->status }}</h6>
                                    <h6 class="block capitalize-font pb-20">ID.NO {{ $student->student_number }}</h6>

                                </div>	
                                <div class="social-info">
                                    <button class="btn btn-default btn-block btn-outline btn-anim" data-toggle="modal" data-target="#myModal"><i class="fa fa-pencil"></i><span class="btn-text">edit profile</span></button>
                                    <div id="myModal" class="modal fade in" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                                                    <h5 class="modal-title" id="myModalLabel">Edit Profile</h5>
                                                </div>
                                                <div class="modal-body">
                                                    <!-- Row -->

                                                    {!! Form::model($student, [
                                                        'method' => 'PATCH',
                                                        'route' => ['students.update', $student->id],
                                                        'files' => true
                                                    ]) !!}
                                                    {!! Form::hidden('update_profile', 1) !!}

                                                    @include('forms.student.profile')
                                                    {!! Form::close() !!}
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-default waves-effect" data-dismiss="modal">Cancel</button>
                                                </div>
                                            </div>
                                            <!-- /.modal-content -->
                                        </div>
                                        <!-- /.modal-dialog -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
           
              
            <div class="col-md-8">
                    <div class="card-view pb-20">
                            <h4>Student Details</h4>
                        </br>
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="card">
                                        <div class="card-body ">
                                               
                                                <h5>Full Name</h5>
                                                <label for="validationDefault01">{{$student->full_name}}</label>
                                        </div>
                                    </div>
                                </div>
                                  <div class="col-sm-4">
                                      <div class="card">
                                          <div class="card-body ">
                                                <h5>Full name [Arabic]</h5>
                                                <label for="validationDefault01">{{$student->full_name_trans}}</label>
                                            </div>
                                        </div>
                                    </div>
                                       <div class="col-sm-4">
                                           <div class="card">
                                               <div class="card-body ">
                                                    <h5>Date of birth</h5>
                                                    <label for="validationDefault01">{{$student->date_of_birth}}</label>

                                                </div>
                                            </div>
                                        </div>
                             </div>
                            </br>
                             <div class="row">
                                    <div class="col-sm-4">
                                        <div class="card">
                                            <div class="card-body ">
                                                    <h5>Gender</h5>
                                                    <label for="validationDefault01">{{$student->gender}}</label>

                                            </div>
                                        </div>
                                    </div>
                                      <div class="col-sm-4">
                                          <div class="card">
                                              <div class="card-body ">
                                                  <h5>Nationality</h5>
                                                    <label for="validationDefault01">{{$student->nationality}}</label>
                                                </div>
                                            </div>
                                        </div>
                                           <div class="col-sm-4">
                                               <div class="card">
                                                   <div class="card-body ">
                                                       <h5>Identity Number</h5>
                                                        <label for="validationDefault01">{{$student->identity_number}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                 </div>
                                </br>
                                 <div class="row">
                                        <div class="col-sm-4">
                                            <div class="card">
                                                <div class="card-body ">
                                                    <h5>Mobile</h5>
                                                        <label for="validationDefault01">{{$student->mobile}}</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                                <div class="card">
                                                    <div class="card-body ">
                                                        <h5>Email</h5>
                                                            <label for="validationDefault01">{{$student->email}}</label>
                                                    </div>
                                                </div>
                                            </div>
                                         
                                     </div>
                    </div>
                      

                <div class="card-view pb-20">
                    <h4>Admissions</h4>
                    @if($student->current_admission)
                    <h6>Current Admission</h6>
                    <div class="row alert-success pt-5 pb-5">
                        <div class="col-sm-3 pt-5">Class : {{ $student->current_admission->class->name }}</div>
                        <div class="col-sm-3 pt-5">Center: {{ $student->current_admission->center->name }}</div>
                        <div class="col-sm-4 pt-5">Status: {{ ucwords(str_replace('_' , ' ' , $student->current_admission->status)) }}</div>
                        <div class="col-sm-2">
                            @if($student->current_admission->status != 'active')
                                <button class="btn btn-xs btn-warning"  data-toggle="modal" href='#edit_admission'>Edit</button>
                            @else
                            @if(auth()->user()->can('change student class') || auth()->user()->can('change student center')|| auth()->user()->can('change student program'))


                          <button class="btn btn-xs btn-danger"  data-toggle="modal" href='#update_class'>Edit</button>
                        @endif
                              
                      
                            @endif
                        </div>
                    </div>
                    <div class="row text-center mt-10">
                        @if($student->current_admission->status == 'active')
                        <div class="text-left pa-20">
                            <h6 class="text-left">Student Programs</h6>
                            <ul>
                            @foreach($student->current_admission->Programs as $program)
                                <li><strong>{{ $program->title }}</strong>
                                @if(isset($program->setting['special_program_code']) && $program->setting['special_program_code'] &&  $program->setting['special_program_code']== 'hefz')
                                @if(isset($student->current_hefz_plan) &&  $student->current_hefz_plan)
                                    <div class="label label-success">{{ $student->current_hefz_plan->status }}</div>
                                       <div class="col-md-12"> Memorization Direction: 
                                        @if($student->current_hefz_plan->study_direction == 'forward')
                                            Albaqarah to Alnas
                                        @else
                                            Alnas to Albaqarah
                                        @endif
                                        <br> Memorizing  {{ $student->current_hefz_plan->num_to_memorize }} {{ $student->current_hefz_plan->memorization_mood }} per class and revise {{ $student->current_hefz_plan->pages_to_revise }} pages for each momerized juz'
                                        <br> Start Memorizing from {{ getSurahNameById($student->current_hefz_plan->start_from_surat) }}, ayah  {{$student->current_hefz_plan->start_from_ayat }}<br>
                                       </div>
                                       @if($student->current_hefz_plan->status == 'active')                                       
                                        <div class="col-md-12">starts on {{ $student->current_hefz_plan->start_date }} </div>
                                       @else
                                       @can('approve_hefz_plan')
                                        <div class="col-md-12 text-center">
                                            
                                            <button type="button" class="btn btn-xs btn-danger" id="approve_hefz_plan">Approve the Plan</button>
                                        </div>
                                        @endcan
                                       @endif
                                    @else
                                        <button type="button" class="btn btn-xs btn-danger" id="approve_hefz_plan">Create Student Study Plan</button>
                                    @endif
                                @endif
                                </li>
                            @endforeach
                            </ul>
                        </div>


                        @elseif($student->current_admission->status == 'new_admission')
                        <a class="btn btn-danger" data-toggle="modal" href='#approve'>Approve/Reject</a>

                        @elseif($student->current_admission->status == 'waiting_for_interview')
                            @foreach($student->current_admission->interviews as $interview)
                                @if(in_array(auth()->user()->id , $interview->interviewers->pluck('id')->toArray() ))        
                                <a class="btn btn-danger" data-toggle="modal" href='#interviewForm{{ $interview->id }}'>Interview Form</a>
                                @endif
                            @endforeach
                        @elseif($student->current_admission->status == 'preparing_for_orientation')
                            <a class="btn btn-danger" data-toggle="modal" href='#set_orientation'>Set Orientation Appointment</a>
                        @else 
                        {{--  //if($student->current_admission->status == 'waiting_for_orientation')  --}}
                            @if(isset($student->current_admission->orientation) && $student->current_admission->orientation->note)
                            <div class="alert alert-info">{{ $student->current_admission->orientation->note }}</div>
                            @endif
                            @include('admission::student.forms.finalize_registration')
                        @endif
                    </div>                    
                    @else
                    <div class="alert alert-warning text-center">Not Registered  
                        <br>
                        @if(config('settings.student_form_guardian') == 'required')
                            <button class="btn btn-success">Add Students' Guardian Information before Registeration</button>                        
                        @else
                            <button class="btn btn-success" id="#register_course"  data-toggle="modal" data-target="#register_course_modal">Register Now</button>
                        @endif
                    </div>
                    @endif
                    @if(isset($student->admissions_history) )
                    @if(count($student->admissions_history))
                        @foreach($student->admissions as $admission)
                            {{ $admission->status }}
                        @endforeach
                    @endif
                    @endif
                </div>
                @if(isset($student->payments))
                @if(count($student->payments))
                <div class="card-view pb-20">
                    <h4>Payments</h4>
                    <table class="table table-responsive table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>Amount</th>
                                <th>Added By</th>
                                <th>Payment Category</th>
                                <th>Verified By</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                        @foreach($student->payments as $payment)
                            <tr>
                                <td>{{ $payment->amount }}</td>
                                <td>{{ $payment->creator_role }}</td>
                                <td>{{ $payment->payment_category }}</td>
                                <td class="text-center">
                                    @if($payment->verified_by)
                                        {{ $payment->verifier->name }}
                                    @else
                                        <a href="{{ route('student_payments.edit' , $payment->id ) }}" class="btn btn-success btn-xs">Verify</a>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if($payment->approved == null)
                                    <div class="alert-danger pa-5 text-center">
                                        Waiting Verification
                                    </div>
                                    @elseif($payment->approved == 0)
                                    Not Valid
                                    @elseif($payment->approved == 1)
                                    Verified
                                    @endif
                                </td>
                            </tr>
                        @endforeach                    
                        </tbody>
                    </table>
                </div>
                @endif
           @endif

            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="register_course_modal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">Register Course</h4>
            </div>

            <div class="modal-body clearfix">
                @include('forms.admission.application')
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>


@if($student->current_admission)
<div class="modal fade" id="approve">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">Approve Student Admission</h4>
            </div>
            
            <div class="modal-body clearfix">
                @include('admission::student.forms.set_interview')                            
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

    @if($student->current_admission->status != 'active')

        <div class="modal fade" id="edit_admission">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title">Edit Student Admission</h4>
                    </div>
                    
                    <div class="modal-body clearfix">
                        @include('admission::student.forms.edit_admission')                            
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

    @else

    <!--  change student class or center if student statue active -->
  

        <div class="modal fade" id="update_class">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title">Change Student Form</h4>
                    </div>
                    
                    <div class="modal-body clearfix">
                        @include('admission::student.forms.update_student_class')                            
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
 
    @endif
        <!------------------------------>

@endif

@if($student->current_admission && $student->current_admission->status == 'waiting_for_interview' )
    @foreach($student->current_admission->interviews as $interview)
        @if(in_array(auth()->user()->id , $interview->interviewers->pluck('id')->toArray() ))
        <div class="modal fade" id="interviewForm{{ $interview->id }}">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title">Interview Assesment Form</h4>
                    </div>
                    <div class="modal-body clearfix">
                    @include('admission::student.forms.interview_report')
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        @endif
    @endforeach
@endif
@if($student->current_admission && $student->current_admission->status == 'preparing_for_orientation' )

<div class="modal fade" id="set_orientation">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">Prepare For Orientation Meeting</h4>
            </div>
            
            <div class="modal-body clearfix">
                @include('admission::student.forms.set_orientation')                            
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endif

@if(isset($program) && isset($program->setting['special_program_code']) && $program->setting['special_program_code'] &&  $program->setting['special_program_code']== 'hefz')
<div class="modal fade" id="hefz_plan_form">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">{{ $student->full_name}}</h4>
            </div>
            @if($student->current_hefz_plan)
        
            <br>
           

            {!! Form::open(['route' => 'admission.approve_hefz_plan']) !!}
            {!! Form::label('student_number', $student->student_number, ['class' => 'col-md-4 control-label']) !!}
            <div class="col-md-3">
                {!! Form::text('student_number' , null, ['class' => 'form-control'  ,"required"  ]) !!}       
                <p class="text-secondary">Last Student Number :{{$last_student_number}}</p>

            </div>
            {!! Form::hidden('plan_id' , $student->current_hefz_plan->id) !!}
            {!! Form::hidden('student_id' , $student->id) !!}
            @else
          
            {!! Form::open(['route' => 'admission.create_hefz_plan']) !!}
            {!! Form::hidden('student_id' , $student->id) !!}
       
            @endif

            <div class="modal-body clearfix">
                    @include('admission::student.forms.hefz_plan' ,['plan' => $student->current_hefz_plan ?? null])                            
                    <div class="form-group clearfix ">
                        <label for="start_date" class="col-md-4 control-label">Start Date</label>
                        <div class="col-md-4">
                            {!! Form::text('hefz[start_date]', null, ['class' => 'datetime form-control' , 'required']) !!}  <p class="text-danger">*You Should Choose Start Date </p>
                     </div>
                    </div>
    
                </div>

            <div class="modal-footer">
                <button type="submit" class="btn btn-success">Approve</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
            {!! Form::close() !!}
        </div>
    </div>
</div>
@section('js')

<script>

$('#approve_hefz_plan').click(function () {
    $('#hefz_plan_form').modal('show');
})
</script>
@append
@endif

@endsection

@include('jssnippets.select2')

@include('jssnippets.flatpickr')

@section('js')

<script>
    $('.approve_btn').click(function () {
        var id = $(this).attr('program-id');

        if($(this).val() > 0){
            $('#interview_form_'+id).show();
        }else{
            $('#interview_form_'+id).hide();
        }
    })

    $('.interview_approve_btn').click(function () {
        var id = $(this).attr('interview-id');

        if($(this).val() > 0){
            $('#programLevels'+id).show();
        }else{
            $('#programLevels'+id).hide();
        }
    })
    

    $('#select_committee').select2();

    $('#select_employee_id').select2();

    $('form#approve_admission').submit(function (e) {
        e.preventDefault();
        $.ajax({
            type: "POST",
            url: "{{ route('admission.approve') }}",
            data: $(this).serialize(),
            dataType: "json",
            success: function (response) {
                if(response.status == "success");
                window.location.reload();
                console.log(response);
            },
            error: function (response) {  
                console.log(response);
                $.each(response.responseJSON, function (index, value) { 
                    var i = index.replace('.', '_');
                    i = i.replace('.', '_');
                    
                    var input_nice_name = index.split('.');
                    $.each(value, function (k, error) { 
                        var msg = error.replace(index , input_nice_name[input_nice_name.length -1])
                                        .replace('_' , ' ');
                         $('#error_'+i).text(msg);
                    });
                     
                });
            }
        });
    })

    $('form#interview_report').submit(function (e) {

        e.preventDefault();
        var formData = new FormData($(this)[0]);

        $.ajax({
            url: "{{ route('admission-interviews.report') }}",
            data :new FormData($("#interview_report")[0]),
            dataType:'json',
            async:false,
            type:'post',
            processData: false,
            contentType: false,
            success: function (response) {
                if(response.status == "success");
                window.location.reload();
                console.log(response);
            },
            error: function (response) {  
                console.log(response);
                $.each(response.responseJSON, function (index, value) { 
                    var i = index.replace('.', '_');
                    i = i.replace('.', '_');
                    
                    var input_nice_name = index.split('.');
                    $.each(value, function (k, error) { 
                        var msg = error.replace(index , input_nice_name[input_nice_name.length -1])
                                        .replace('_' , ' ');
                         $('#error_'+i).text(msg);
                    });
                });
            }
        });
        return false;
    })

    flatpickr('.datetime', {
        enableTime: true,
        minDate: "today",
        "plugins": [new confirmDatePlugin({})]
    });

    
</script>

@append
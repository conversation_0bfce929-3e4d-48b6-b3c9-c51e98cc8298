<?php

namespace Modules\Education\Http\Controllers;

use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Modules\Education\Http\Requests\ClassReportStaatisticsRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class CentersReportsController extends Controller
{

    public function __construct()
    {
        $this->middleware('writeCurrentClassReportOnly', ['only' => ['create']]);
    }


    public function __invoke(Request $request)
    {


        DB::connection()->enableQueryLog();


        $centers = Classes::with('teachers')->with('center')->withCount('students')
            ->get();



        return \Yajra\DataTables\DataTables::of($centers)
            ->addIndexColumn()
            ->addColumn('teacher', function ($classDetails) use ($request) {

               $details  =  $classDetails->teachers->pluck('name');
                $teacherNames = '';
               foreach ($details as $teacher){
                   $teacherNames .= '<span class="badge badge-primary">'.$teacher.'"</span>';
               }
               return $teacherNames;
            })
            ->addColumn('halaqah', function ($classDetails) use ($request) {
                return 'work in progress';


                    return $classDetails->name;
             
            })
            ->addColumn('studentsCount', function ($classDetails) use ($request) {
                return 'work in progress';
                    return $classDetails->students_count;


            })
            ->rawColumns(['teacher'])

            ->make(true);


    }


}

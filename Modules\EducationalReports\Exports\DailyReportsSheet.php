<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\StudentHefzReport;
use App\StudentRevisionReport;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

final class DailyReportsSheet implements WithTitle, WithStyles, WithEvents
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }



    /**
     * Get memorization reports
     */
    private function getMemorizationReports(): Collection
    {
        $classId = $this->filters['classId'];
        $year = $this->filters['year'];
        $month = $this->filters['month'];
        $studentId = $this->filters['studentId'] ?? null;

        $query = StudentHefzReport::with([
            'student',
            'classes.center',
            'classes.programs',
            'classes.teachers',
            'fromSurat',
            'toSurat',
            'result',
            'attendanceOptions',
            'hefzPlan'
        ])
        ->where('class_id', $classId)
        ->whereYear('created_at', $year)
        ->whereMonth('created_at', $month)
        ->whereNotNull('hefz_from_surat')
        ->whereNotNull('hefz_from_ayat')
        ->whereNotNull('hefz_to_surat')
        ->whereNotNull('hefz_to_ayat');

        // Apply student filter if provided
        if (!empty($studentId)) {
            $query->where('student_id', $studentId);
        }

        $reports = $query->get();

        return $reports->map(function ($report) {
            // Get class program
            $classProgram = $report->classes->programs->first()->title ?? 'N/A';
            
            // Get teacher names
            $teacherNames = $report->classes->teachers->pluck('full_name')->join(', ');
            
            // Calculate number of pages
            $numberOfPages = $this->calculateMemorizationPages($report);
            
            // Check if day is in timetable
            $isInTimetable = $this->checkIfDayInTimetable($report);

            return [
                'centre_id' => $report->classes->center->id ?? 'N/A',
                'centre_name' => $report->classes->center->name ?? 'N/A',
                'class_id' => $report->class_id,
                'class_program' => $classProgram,
                'teacher_name' => $teacherNames ?: 'N/A',
                'date' => $report->created_at->format('Y-m-d'),
                'is_day_in_timetable' => $isInTimetable ? 'Yes' : 'No',
                'student_id' => $report->student_id,
                'student_name' => $report->student->full_name ?? 'N/A',
                'attendance' => $report->attendanceOptions->title ?? 'N/A',
                'from_surah' => $report->fromSurat->name ?? 'N/A',
                'from_verse' => $report->hefz_from_ayat ?? 'N/A',
                'to_surah' => $report->toSurat->name ?? 'N/A',
                'to_verse' => $report->hefz_to_ayat ?? 'N/A',
                'no_of_pages' => $numberOfPages,
                'evaluation' => $report->result->title ?? 'N/A',
                'evaluation_note' => $report->hefz_evaluation_note ?? '',
            ];
        });
    }

    /**
     * Get revision reports
     */
    private function getRevisionReports(): Collection
    {
        $classId = $this->filters['classId'];
        $year = $this->filters['year'];
        $month = $this->filters['month'];
        $studentId = $this->filters['studentId'] ?? null;

        $query = StudentRevisionReport::with([
            'student',
            'classes.center',
            'classes.programs',
            'classes.teachers',
            'fromSurat',
            'toSurat',
            'result',
            'revisionPlan',
            'attendanceOptions'
        ])
        ->where('class_id', $classId)
        ->whereYear('created_at', $year)
        ->whereMonth('created_at', $month)
        ->whereNotNull('revision_from_surat')
        ->whereNotNull('revision_from_ayat')
        ->whereNotNull('revision_to_surat')
        ->whereNotNull('revision_to_ayat');

        // Apply student filter if provided
        if (!empty($studentId)) {
            $query->where('student_id', $studentId);
        }

        $reports = $query->get();

        return $reports->map(function ($report) {
            // Get class program
            $classProgram = $report->classes->programs->first()->title ?? 'N/A';
            
            // Get teacher names
            $teacherNames = $report->classes->teachers->pluck('full_name')->join(', ');
            
            // Calculate number of pages
            $numberOfPages = $this->calculateRevisionPages($report);
            
            // Check if day is in timetable
            $isInTimetable = $this->checkIfDayInTimetable($report);

            return [
                'centre_id' => $report->classes->center->id ?? 'N/A',
                'centre_name' => $report->classes->center->name ?? 'N/A',
                'class_id' => $report->class_id,
                'class_program' => $classProgram,
                'teacher_name' => $teacherNames ?: 'N/A',
                'date' => $report->created_at->format('Y-m-d'),
                'is_day_in_timetable' => $isInTimetable ? 'Yes' : 'No',
                'student_id' => $report->student_id,
                'student_name' => $report->student->full_name ?? 'N/A',
                'attendance' => $report->attendanceOptions->title ?? 'N/A',
                'from_surah' => $report->fromSurat->name ?? 'N/A',
                'from_verse' => $report->revision_from_ayat ?? 'N/A',
                'to_surah' => $report->toSurat->name ?? 'N/A',
                'to_verse' => $report->revision_to_ayat ?? 'N/A',
                'no_of_pages' => $numberOfPages,
                'evaluation' => $report->result->title ?? 'N/A',
                'evaluation_note' => $report->revision_evaluation_note ?? '',
            ];
        });
    }

    /**
     * Calculate memorization pages using stored procedure
     */
    private function calculateMemorizationPages(StudentHefzReport $report): int
    {
        try {
            if ($report->hefzPlan && $report->hefzPlan->study_direction === 'backward') {
                $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $report->hefz_from_surat,
                    $report->hefz_from_ayat,
                    $report->hefz_to_surat,
                    $report->hefz_to_ayat
                ]);
                
                return $result[0]->numberofPagesSum ?? 0;
            } else {
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $report->hefz_from_surat,
                    $report->hefz_from_ayat,
                    $report->hefz_to_surat,
                    $report->hefz_to_ayat
                ]);

                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                return $results[0]->number_of_pages_sum ?? 0;
            }
        } catch (\Exception $e) {
            \Log::error('Error calculating memorization pages: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Calculate revision pages using stored procedure
     */
    private function calculateRevisionPages(StudentRevisionReport $report): int
    {
        try {
            if ($report->revisionPlan && $report->revisionPlan->study_direction === 'backward') {
                $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $report->revision_from_surat,
                    $report->revision_from_ayat,
                    $report->revision_to_surat,
                    $report->revision_to_ayat
                ]);
                
                return $result[0]->numberofPagesSum ?? 0;
            } else {
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $report->revision_from_surat,
                    $report->revision_from_ayat,
                    $report->revision_to_surat,
                    $report->revision_to_ayat
                ]);

                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                return $results[0]->number_of_pages_sum ?? 0;
            }
        } catch (\Exception $e) {
            \Log::error('Error calculating revision pages: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Check if the day is in timetable
     */
    private function checkIfDayInTimetable($report): bool
    {
        try {
            // Get the day of week from the report date
            $dayOfWeek = strtolower($report->created_at->format('D')); // sat, sun, mon, etc.

            // Get the class timetable
            $timetable = DB::table('class_timetable')
                ->where('class_id', $report->class_id)
                ->whereNull('deleted_at')
                ->first();

            if (!$timetable) {
                return false;
            }

            // Check if the day has a time set (not null)
            return !is_null($timetable->$dayOfWeek);

        } catch (\Exception $e) {
            \Log::error('Error checking timetable: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get table headings (without Type column since we have separate tables)
     */
    private function getTableHeadings(): array
    {
        return [
            'Centre ID',
            'Centre Name',
            'Class ID',
            'Class Program',
            'Teacher Name',
            'Date',
            'Is Day in Timetable?',
            'Student ID',
            'Student Name',
            'Attendance',
            'From Surah',
            'From Verse',
            'To Surah',
            'To Verse',
            'No. of Pages',
            'Evaluation',
            'Evaluation Note',
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Daily Reports';
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [];
    }

    /**
     * Register events for creating dual tables
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $this->createDualTables($event->sheet);
            },
        ];
    }

    /**
     * Create dual tables on the worksheet with daily performance analytics
     */
    private function createDualTables($sheet)
    {
        $worksheet = $sheet->getDelegate();

        // Get data
        $memorizationData = $this->getMemorizationReports()->sortBy('date');
        $revisionData = $this->getRevisionReports()->sortBy('date');
        $headings = $this->getTableHeadings();

        // Get daily analytics
        $analytics = $this->getDailyAnalytics();

        // Set main title
        $monthName = $this->filters['monthName'];
        $year = $this->filters['year'];
        $className = $this->filters['class']->name ?? 'N/A';

        $worksheet->setCellValue('A1', "DAILY REPORTS - {$className} - {$monthName} {$year}");
        $worksheet->mergeCells('A1:Q1');

        // Style main title
        $worksheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 16],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'E8F4F8']]
        ]);

        $currentRow = 3;

        // Add Performance Dashboard
        $currentRow = $this->createPerformanceDashboard($worksheet, $currentRow, $analytics);
        $currentRow += 2;

        // Create Memorization Table
        $currentRow = $this->createTable($worksheet, $currentRow, 'MEMORIZATION REPORTS', $headings, $memorizationData);

        // Add spacing
        $currentRow += 2;

        // Create Revision Table
        $this->createTable($worksheet, $currentRow, 'REVISION REPORTS', $headings, $revisionData);

        // Auto-size columns
        foreach (range('A', 'Q') as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }
    }

    /**
     * Create a single table with title, headers, and data
     */
    private function createTable($worksheet, $startRow, $title, $headings, $data): int
    {
        // Set table title
        $worksheet->setCellValue("A{$startRow}", $title);
        $worksheet->mergeCells("A{$startRow}:Q{$startRow}");

        // Style table title
        $worksheet->getStyle("A{$startRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2E7D32']]
        ]);

        $headerRow = $startRow + 1;

        // Set headers
        $col = 'A';
        foreach ($headings as $heading) {
            $worksheet->setCellValue($col . $headerRow, $heading);
            $col++;
        }

        // Style headers
        $worksheet->getStyle("A{$headerRow}:Q{$headerRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '4CAF50']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $dataStartRow = $headerRow + 1;
        $currentDataRow = $dataStartRow;

        // Add data rows
        foreach ($data as $row) {
            $col = 'A';
            foreach ($row as $value) {
                $worksheet->setCellValue($col . $currentDataRow, $value);
                $col++;
            }
            $currentDataRow++;
        }

        // Style data rows
        if ($data->count() > 0) {
            $dataEndRow = $currentDataRow - 1;
            $worksheet->getStyle("A{$dataStartRow}:Q{$dataEndRow}")->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ]);

            // Alternate row colors
            for ($row = $dataStartRow; $row <= $dataEndRow; $row++) {
                if (($row - $dataStartRow) % 2 == 1) {
                    $worksheet->getStyle("A{$row}:Q{$row}")->applyFromArray([
                        'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'F5F5F5']]
                    ]);
                }
            }

            return $dataEndRow;
        }

        // If no data, add "No data available" message
        $worksheet->setCellValue("A{$dataStartRow}", 'No data available');
        $worksheet->mergeCells("A{$dataStartRow}:Q{$dataStartRow}");
        $worksheet->getStyle("A{$dataStartRow}")->applyFromArray([
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'font' => ['italic' => true],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        return $dataStartRow;
    }

    /**
     * Get daily analytics for supervisory insights
     */
    private function getDailyAnalytics(): array
    {
        $classId = $this->filters['classId'];
        $year = $this->filters['year'];
        $month = $this->filters['month'];
        $planYearMonth = sprintf('%d-%02d', $year, $month);

        // Get daily performance trends
        $dailyTrends = DB::select("
            SELECT
                DATE(shr.created_at) as report_date,
                COUNT(DISTINCT shr.student_id) as active_students,
                COUNT(shr.id) as total_sessions,
                AVG(eso.weight) as avg_score,
                COUNT(CASE WHEN ao.title IN ('on_time', 'late') THEN 1 END) as present_count,
                COUNT(CASE WHEN ao.title = 'absent' THEN 1 END) as absent_count,
                SUM(COALESCE(shr.pages_memorized, 0)) as pages_memorized
            FROM student_hefz_report shr
            LEFT JOIN evaluation_schema_options eso ON shr.hefz_evaluation_id = eso.id
            LEFT JOIN attendance_options ao ON shr.attendance_id = ao.id
            WHERE shr.class_id = ?
            AND DATE_FORMAT(shr.created_at, '%Y-%m') = ?
            AND shr.deleted_at IS NULL
            GROUP BY DATE(shr.created_at)
            ORDER BY report_date DESC
        ", [$classId, $planYearMonth]);

        // Get at-risk students (poor attendance or performance)
        $atRiskStudents = DB::select("
            SELECT
                s.id,
                s.full_name,
                COUNT(shr.id) as total_sessions,
                COUNT(CASE WHEN ao.title IN ('on_time', 'late') THEN 1 END) as attended_sessions,
                AVG(eso.weight) as avg_score,
                ROUND((COUNT(CASE WHEN ao.title IN ('on_time', 'late') THEN 1 END) * 100.0 / COUNT(shr.id)), 1) as attendance_rate
            FROM students s
            LEFT JOIN student_hefz_report shr ON s.id = shr.student_id
                AND shr.class_id = ? AND DATE_FORMAT(shr.created_at, '%Y-%m') = ? AND shr.deleted_at IS NULL
            LEFT JOIN evaluation_schema_options eso ON shr.hefz_evaluation_id = eso.id
            LEFT JOIN attendance_options ao ON shr.attendance_id = ao.id
            WHERE s.id IN (
                SELECT DISTINCT student_id FROM student_hefz_report
                WHERE class_id = ? AND DATE_FORMAT(created_at, '%Y-%m') = ? AND deleted_at IS NULL
            )
            GROUP BY s.id, s.full_name
            HAVING (attendance_rate < 70 OR avg_score < 0.5 OR total_sessions < 3)
            ORDER BY attendance_rate ASC, avg_score ASC
        ", [$classId, $planYearMonth, $classId, $planYearMonth]);

        // Get teacher performance comparison
        $teacherPerformance = DB::select("
            SELECT
                emp.id,
                emp.full_name as teacher_name,
                COUNT(DISTINCT shr.student_id) as students_taught,
                COUNT(shr.id) as sessions_conducted,
                AVG(eso.weight) as avg_evaluation,
                ROUND((COUNT(CASE WHEN ao.title IN ('on_time', 'late') THEN 1 END) * 100.0 / COUNT(shr.id)), 1) as class_attendance_rate,
                SUM(COALESCE(shr.pages_memorized, 0)) as total_pages_taught
            FROM employees emp
            LEFT JOIN student_hefz_report shr ON emp.id = shr.teacher_id
                AND shr.class_id = ? AND DATE_FORMAT(shr.created_at, '%Y-%m') = ? AND shr.deleted_at IS NULL
            LEFT JOIN evaluation_schema_options eso ON shr.hefz_evaluation_id = eso.id
            LEFT JOIN attendance_options ao ON shr.attendance_id = ao.id
            WHERE emp.id IN (
                SELECT DISTINCT teacher_id FROM student_hefz_report
                WHERE class_id = ? AND DATE_FORMAT(created_at, '%Y-%m') = ? AND deleted_at IS NULL
            )
            GROUP BY emp.id, emp.full_name
            ORDER BY avg_evaluation DESC, class_attendance_rate DESC
        ", [$classId, $planYearMonth, $classId, $planYearMonth]);

        return [
            'daily_trends' => $dailyTrends,
            'at_risk_students' => $atRiskStudents,
            'teacher_performance' => $teacherPerformance,
        ];
    }

    /**
     * Create performance dashboard for supervisors
     */
    private function createPerformanceDashboard($worksheet, $startRow, $analytics): int
    {
        $dailyTrends = $analytics['daily_trends'];
        $atRiskStudents = $analytics['at_risk_students'];
        $teacherPerformance = $analytics['teacher_performance'];

        // Title
        $worksheet->setCellValue("A{$startRow}", "🎯 PERFORMANCE DASHBOARD - DAILY INSIGHTS");
        $worksheet->mergeCells("A{$startRow}:Q{$startRow}");
        $worksheet->getStyle("A{$startRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'D32F2F']]
        ]);

        $currentRow = $startRow + 2;

        // Recent Performance Trends (Last 5 days)
        $worksheet->setCellValue("A{$currentRow}", "📈 RECENT PERFORMANCE TRENDS");
        $worksheet->mergeCells("A{$currentRow}:F{$currentRow}");
        $currentRow++;

        $trendHeaders = ['Date', 'Students', 'Sessions', 'Avg Score', 'Attendance %', 'Pages'];
        foreach ($trendHeaders as $index => $header) {
            $col = chr(65 + $index); // A, B, C, etc.
            $worksheet->setCellValue("{$col}{$currentRow}", $header);

            // Add dynamic comments to trend headers
            $this->addTrendHeaderComment($worksheet, "{$col}{$currentRow}", $header, $dailyTrends);
        }
        $currentRow++;

        foreach (array_slice($dailyTrends, 0, 5) as $trend) {
            $attendanceRate = $trend->total_sessions > 0 ? round(((float)$trend->present_count / (float)$trend->total_sessions) * 100, 1) : 0;
            $worksheet->setCellValue("A{$currentRow}", $trend->report_date);
            $worksheet->setCellValue("B{$currentRow}", $trend->active_students);
            $worksheet->setCellValue("C{$currentRow}", $trend->total_sessions);
            $worksheet->setCellValue("D{$currentRow}", round((float)($trend->avg_score ?? 0) * 100, 1) . '%');
            $worksheet->setCellValue("E{$currentRow}", $attendanceRate . '%');
            $worksheet->setCellValue("F{$currentRow}", $trend->pages_memorized);
            $currentRow++;
        }

        $currentRow += 2;

        // At-Risk Students Alert
        $worksheet->setCellValue("A{$currentRow}", "🚨 AT-RISK STUDENTS (Immediate Attention Required)");
        $worksheet->mergeCells("A{$currentRow}:Q{$currentRow}");
        $worksheet->getStyle("A{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => 'FF5722']]
        ]);
        $currentRow++;

        if (count($atRiskStudents) > 0) {
            $riskHeaders = ['Student Name', 'Sessions', 'Attended', 'Attendance %', 'Avg Score', 'Risk Level'];
            foreach ($riskHeaders as $index => $header) {
                $col = chr(65 + $index);
                $worksheet->setCellValue("{$col}{$currentRow}", $header);
            }
            $currentRow++;

            foreach (array_slice($atRiskStudents, 0, 10) as $student) {
                $riskLevel = '🔴 Critical';
                if ($student->attendance_rate >= 50 && $student->avg_score >= 0.3) {
                    $riskLevel = '⚠️ Moderate';
                }

                $worksheet->setCellValue("A{$currentRow}", $student->full_name);
                $worksheet->setCellValue("B{$currentRow}", $student->total_sessions);
                $worksheet->setCellValue("C{$currentRow}", $student->attended_sessions);
                $worksheet->setCellValue("D{$currentRow}", $student->attendance_rate . '%');
                $worksheet->setCellValue("E{$currentRow}", round((float)($student->avg_score ?? 0) * 100, 1) . '%');
                $worksheet->setCellValue("F{$currentRow}", $riskLevel);

                // Add dynamic comment for at-risk student
                $this->addAtRiskStudentComment($worksheet, "A{$currentRow}", $student);

                $currentRow++;
            }
        } else {
            $worksheet->setCellValue("A{$currentRow}", "✅ No at-risk students identified");
            $currentRow++;
        }

        $currentRow += 2;

        // Teacher Performance Summary
        $worksheet->setCellValue("A{$currentRow}", "👨‍🏫 TEACHER PERFORMANCE SUMMARY");
        $worksheet->mergeCells("A{$currentRow}:Q{$currentRow}");
        $currentRow++;

        if (count($teacherPerformance) > 0) {
            $teacherHeaders = ['Teacher', 'Students', 'Sessions', 'Avg Score', 'Attendance %', 'Pages Taught', 'Performance'];
            foreach ($teacherHeaders as $index => $header) {
                $col = chr(65 + $index);
                $worksheet->setCellValue("{$col}{$currentRow}", $header);
            }
            $currentRow++;

            foreach ($teacherPerformance as $teacher) {
                $performance = '✅ Excellent';
                if ($teacher->avg_evaluation < 0.7 || $teacher->class_attendance_rate < 75) {
                    $performance = '⚠️ Needs Support';
                }
                if ($teacher->avg_evaluation < 0.5 || $teacher->class_attendance_rate < 60) {
                    $performance = '🔴 Requires Training';
                }

                $worksheet->setCellValue("A{$currentRow}", $teacher->teacher_name);
                $worksheet->setCellValue("B{$currentRow}", $teacher->students_taught);
                $worksheet->setCellValue("C{$currentRow}", $teacher->sessions_conducted);
                $worksheet->setCellValue("D{$currentRow}", round((float)($teacher->avg_evaluation ?? 0) * 100, 1) . '%');
                $worksheet->setCellValue("E{$currentRow}", $teacher->class_attendance_rate . '%');
                $worksheet->setCellValue("F{$currentRow}", $teacher->total_pages_taught);
                $worksheet->setCellValue("G{$currentRow}", $performance);

                // Add dynamic comment for teacher performance
                $this->addTeacherPerformanceComment($worksheet, "A{$currentRow}", $teacher);

                $currentRow++;
            }
        }

        return $currentRow;
    }

    /**
     * Add dynamic comments to trend headers
     */
    private function addTrendHeaderComment($worksheet, $cellAddress, $header, $dailyTrends)
    {
        $comment = $worksheet->getComment($cellAddress);
        $commentText = '';

        // Calculate trend statistics
        $totalDays = count($dailyTrends);
        $avgStudents = $totalDays > 0 ? round(array_sum(array_column($dailyTrends, 'active_students')) / $totalDays, 1) : 0;
        $avgSessions = $totalDays > 0 ? round(array_sum(array_column($dailyTrends, 'total_sessions')) / $totalDays, 1) : 0;
        $avgScore = $totalDays > 0 ? round(array_sum(array_column($dailyTrends, 'avg_score')) / $totalDays * 100, 1) : 0;
        $totalPages = array_sum(array_column($dailyTrends, 'pages_memorized'));

        switch ($header) {
            case 'Date':
                $commentText = "DAILY TRACKING PERIOD:\n\n";
                $commentText .= "• Total Days Analyzed: {$totalDays}\n";
                $commentText .= "• Period: Last 5 days of activity\n\n";
                $commentText .= "This shows the most recent daily\n";
                $commentText .= "performance trends for quick\n";
                $commentText .= "identification of patterns.";
                break;

            case 'Students':
                $commentText = "DAILY STUDENT PARTICIPATION:\n\n";
                $commentText .= "• Average Students/Day: {$avgStudents}\n";
                $commentText .= "• Total Days: {$totalDays}\n\n";
                $commentText .= "CALCULATION: Count of unique students\n";
                $commentText .= "who had sessions on each day.\n\n";
                if ($avgStudents >= 15) {
                    $commentText .= "✅ HIGH PARTICIPATION: Excellent\n";
                    $commentText .= "daily student engagement.";
                } elseif ($avgStudents >= 10) {
                    $commentText .= "📊 MODERATE PARTICIPATION: Good\n";
                    $commentText .= "but could be improved.";
                } else {
                    $commentText .= "⚠️ LOW PARTICIPATION: Consider\n";
                    $commentText .= "attendance improvement strategies.";
                }
                break;

            case 'Sessions':
                $commentText = "DAILY SESSION VOLUME:\n\n";
                $commentText .= "• Average Sessions/Day: {$avgSessions}\n";
                $commentText .= "• Total Days: {$totalDays}\n\n";
                $commentText .= "CALCULATION: Total daily reports\n";
                $commentText .= "submitted each day.\n\n";
                if ($avgSessions >= 30) {
                    $commentText .= "🌟 HIGH VOLUME: Excellent daily\n";
                    $commentText .= "teaching activity.";
                } elseif ($avgSessions >= 20) {
                    $commentText .= "✅ GOOD VOLUME: Solid daily\n";
                    $commentText .= "session activity.";
                } else {
                    $commentText .= "📊 MODERATE VOLUME: Consider\n";
                    $commentText .= "increasing session frequency.";
                }
                break;

            case 'Avg Score':
                $commentText = "DAILY PERFORMANCE AVERAGE:\n\n";
                $commentText .= "• Average Score: {$avgScore}%\n";
                $commentText .= "• Total Days: {$totalDays}\n\n";
                $commentText .= "CALCULATION: Average of all evaluation\n";
                $commentText .= "scores for each day × 100\n\n";
                $commentText .= "SCORE MAPPING:\n";
                $commentText .= "• Excellent = 100% • Very Good = 85%\n";
                $commentText .= "• Good = 70% • Weak = 30%\n\n";
                if ($avgScore >= 85) {
                    $commentText .= "🌟 EXCELLENT: Outstanding daily\n";
                    $commentText .= "performance consistency.";
                } elseif ($avgScore >= 70) {
                    $commentText .= "✅ GOOD: Solid daily performance\n";
                    $commentText .= "with room for improvement.";
                } else {
                    $commentText .= "🔴 CONCERN: Below target performance.\n";
                    $commentText .= "Review teaching methods urgently.";
                }
                break;

            case 'Attendance %':
                $commentText = "DAILY ATTENDANCE TRACKING:\n\n";
                $commentText .= "• Total Days: {$totalDays}\n\n";
                $commentText .= "CALCULATION: (Present Sessions ÷\n";
                $commentText .= "Total Sessions) × 100 per day\n\n";
                $commentText .= "Present = 'on_time' + 'late' reports\n";
                $commentText .= "Absent = 'absent' reports\n\n";
                $commentText .= "Monitor daily attendance patterns\n";
                $commentText .= "to identify trends and issues.";
                break;

            case 'Pages':
                $commentText = "DAILY MEMORIZATION PROGRESS:\n\n";
                $commentText .= "• Total Pages (All Days): {$totalPages}\n";
                $commentText .= "• Average Pages/Day: " . round($totalPages / max($totalDays, 1), 1) . "\n";
                $commentText .= "• Total Days: {$totalDays}\n\n";
                $commentText .= "CALCULATION: Sum of all pages\n";
                $commentText .= "memorized by all students each day.\n\n";
                if ($totalPages >= 100) {
                    $commentText .= "🌟 EXCELLENT: High memorization\n";
                    $commentText .= "productivity across all days.";
                } elseif ($totalPages >= 50) {
                    $commentText .= "✅ GOOD: Steady memorization\n";
                    $commentText .= "progress being made.";
                } else {
                    $commentText .= "📊 MODERATE: Consider strategies\n";
                    $commentText .= "to increase memorization pace.";
                }
                break;
        }

        if (!empty($commentText)) {
            $comment->getText()->createText($commentText);
            $comment->setWidth('350px');
            $comment->setHeight('180px');
        }
    }

    /**
     * Add dynamic comments for at-risk students
     */
    private function addAtRiskStudentComment($worksheet, $cellAddress, $student)
    {
        $comment = $worksheet->getComment($cellAddress);

        $attendanceRate = (float)$student->attendance_rate;
        $avgScore = (float)($student->avg_score ?? 0) * 100;
        $totalSessions = (int)$student->total_sessions;
        $attendedSessions = (int)$student->attended_sessions;
        $absentSessions = $totalSessions - $attendedSessions;

        $commentText = "AT-RISK STUDENT ANALYSIS:\n\n";
        $commentText .= "Student: {$student->full_name}\n\n";
        $commentText .= "ATTENDANCE DETAILS:\n";
        $commentText .= "• Total Sessions: {$totalSessions}\n";
        $commentText .= "• Attended: {$attendedSessions}\n";
        $commentText .= "• Absent: {$absentSessions}\n";
        $commentText .= "• Rate: {$attendanceRate}%\n\n";

        $commentText .= "PERFORMANCE DETAILS:\n";
        $commentText .= "• Average Score: " . round($avgScore, 1) . "%\n\n";

        $commentText .= "RISK FACTORS:\n";
        $riskFactors = [];

        if ($attendanceRate < 70) {
            $riskFactors[] = "• Low Attendance (<70%)";
        }
        if ($avgScore < 50) {
            $riskFactors[] = "• Poor Performance (<50%)";
        }
        if ($totalSessions < 3) {
            $riskFactors[] = "• Insufficient Sessions (<3)";
        }

        $commentText .= implode("\n", $riskFactors) . "\n\n";

        $commentText .= "RECOMMENDED ACTIONS:\n";
        if ($attendanceRate < 50) {
            $commentText .= "• URGENT: Contact student/family\n";
            $commentText .= "• Investigate attendance barriers\n";
        } elseif ($attendanceRate < 70) {
            $commentText .= "• Monitor attendance closely\n";
            $commentText .= "• Implement attendance plan\n";
        }

        if ($avgScore < 30) {
            $commentText .= "• URGENT: Intensive tutoring needed\n";
            $commentText .= "• Review learning approach\n";
        } elseif ($avgScore < 50) {
            $commentText .= "• Additional practice sessions\n";
            $commentText .= "• Peer tutoring support\n";
        }

        if ($totalSessions < 3) {
            $commentText .= "• Encourage regular participation\n";
            $commentText .= "• Address scheduling conflicts\n";
        }

        $comment->getText()->createText($commentText);
        $comment->setWidth('400px');
        $comment->setHeight('250px');
    }

    /**
     * Add dynamic comments for teacher performance
     */
    private function addTeacherPerformanceComment($worksheet, $cellAddress, $teacher)
    {
        $comment = $worksheet->getComment($cellAddress);

        $avgEvaluation = (float)($teacher->avg_evaluation ?? 0) * 100;
        $attendanceRate = (float)$teacher->class_attendance_rate;
        $studentsCount = (int)$teacher->students_taught;
        $sessionsCount = (int)$teacher->sessions_conducted;
        $pagesCount = (int)$teacher->total_pages_taught;

        $avgPagesPerSession = $sessionsCount > 0 ? round($pagesCount / $sessionsCount, 1) : 0;
        $avgSessionsPerStudent = $studentsCount > 0 ? round($sessionsCount / $studentsCount, 1) : 0;

        $commentText = "TEACHER PERFORMANCE ANALYSIS:\n\n";
        $commentText .= "Teacher: {$teacher->teacher_name}\n\n";

        $commentText .= "TEACHING METRICS:\n";
        $commentText .= "• Students Taught: {$studentsCount}\n";
        $commentText .= "• Sessions Conducted: {$sessionsCount}\n";
        $commentText .= "• Avg Sessions/Student: {$avgSessionsPerStudent}\n\n";

        $commentText .= "PERFORMANCE METRICS:\n";
        $commentText .= "• Avg Evaluation: " . round($avgEvaluation, 1) . "%\n";
        $commentText .= "• Class Attendance: {$attendanceRate}%\n";
        $commentText .= "• Pages Taught: {$pagesCount}\n";
        $commentText .= "• Avg Pages/Session: {$avgPagesPerSession}\n\n";

        $commentText .= "PERFORMANCE ASSESSMENT:\n";

        if ($avgEvaluation >= 85 && $attendanceRate >= 85) {
            $commentText .= "🌟 EXCELLENT TEACHER:\n";
            $commentText .= "• High evaluation scores\n";
            $commentText .= "• Strong attendance rates\n";
            $commentText .= "• Effective teaching methods\n\n";
            $commentText .= "RECOMMENDATION: Share best practices\n";
            $commentText .= "with other teachers.";
        } elseif ($avgEvaluation >= 70 && $attendanceRate >= 75) {
            $commentText .= "✅ GOOD PERFORMANCE:\n";
            $commentText .= "• Solid evaluation scores\n";
            $commentText .= "• Acceptable attendance\n\n";
            $commentText .= "RECOMMENDATION: Continue current\n";
            $commentText .= "methods with minor improvements.";
        } else {
            $commentText .= "⚠️ NEEDS SUPPORT:\n";

            if ($avgEvaluation < 70) {
                $commentText .= "• Low evaluation scores\n";
                $commentText .= "• Review teaching methods\n";
            }
            if ($attendanceRate < 75) {
                $commentText .= "• Low class attendance\n";
                $commentText .= "• Investigate engagement issues\n";
            }

            $commentText .= "\nRECOMMENDATION:\n";
            $commentText .= "• Provide additional training\n";
            $commentText .= "• Mentoring support\n";
            $commentText .= "• Regular performance reviews";
        }

        if ($avgPagesPerSession < 1.0) {
            $commentText .= "\n\n📊 MEMORIZATION PACE:\n";
            $commentText .= "Below average pages per session.\n";
            $commentText .= "Consider pace improvement strategies.";
        }

        $comment->getText()->createText($commentText);
        $comment->setWidth('400px');
        $comment->setHeight('280px');
    }
}

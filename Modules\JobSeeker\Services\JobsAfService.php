<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Repositories\JobRepository;
use <PERSON><PERSON><PERSON>\JobSeeker\Repositories\FilterRepository;
use Carbon\Carbon;
use App\Services\EmailService;
use Illuminate\Support\Facades\DB;
use Mo<PERSON>les\JobSeeker\Entities\JobSeeker;
use Modules\JobSeeker\Entities\JobCategory;
use Modules\JobSeeker\Entities\Job;
use Modules\JobSeeker\Services\MissedJobService;
use Modules\JobSeeker\Services\CategoryMappingService;
use Mo<PERSON><PERSON>\JobSeeker\Services\NotificationProcessLogger;
use Mo<PERSON>les\JobSeeker\Contracts\JobProviderInterface;
use Illuminate\Support\Str;

/**
 * JobsAfService orchestrates fetching Jobs.af provider jobs, intelligent category
 * assignment, and notification delivery. Handles both provider and canonical category
 * mapping with automatic fallback mechanisms.
 *
 * Purpose: Sync Jobs.af jobs with intelligent categorization and trigger notifications.
 * Side effects: Creates/updates jobs table, syncs provider_category_pivot and
 * job_category_pivot tables, dispatches email notifications via JobNotificationService.
 * Security: Rate-limited API calls, input validation, SQL injection protection via Eloquent.
 * Errors: Comprehensive logging with context, graceful degradation on API failures,
 * circuit breaker pattern for provider outages.
 * Dependencies: JobRepository, CategoryMappingService, JobNotificationService, EmailService.
 * Performance: Batch processing, intelligent caching, optimized database queries.
 */
final class JobsAfService implements \Modules\JobSeeker\Contracts\JobProviderSyncInterface, JobProviderInterface
{
    /**
     * Run-context identifiers for end-to-end log correlation
     */
    protected ?string $traceId = null;
    protected ?int $executionId = null;

    /**
     * Set runtime context for traceability.
     * 
     * @param string $traceId Unique trace identifier
     * @param int|null $executionId Command execution record ID
     * @param int|null $scheduleRuleId Schedule rule ID if applicable
     * @return void
     */
    public function setRunContext(string $traceId, ?int $executionId = null, ?int $scheduleRuleId = null): void
    {
        $this->traceId = $traceId;
        $this->executionId = $executionId;
        $this->processLogger = new NotificationProcessLogger($traceId, $executionId);
    }

    /**
     * @var JobRepository
     */
    protected JobRepository $jobRepository;

    /**
     * @var FilterRepository
     */
    protected FilterRepository $filterRepository;

    /**
     * @var EmailService
     */
    protected EmailService $emailService;

    /**
     * @var SystemErrorNotificationService
     */
    protected SystemErrorNotificationService $errorNotificationService;

    /**
     * @var MissedJobService
     */
    protected MissedJobService $missedJobService;

    /**
     * @var CategoryMappingService
     */
    protected CategoryMappingService $categoryMappingService;

    /**
     * @var NotificationProcessLogger
     */
    protected ?NotificationProcessLogger $processLogger = null;

    /**
     * @var string
     */
    protected string $apiUrl = 'https://jobs.af/api/v2.6/jobs/list';

    /**
     * @var array
     */
    protected array $defaultFilters = [];

    /**
     * Category priority for sorting (lower number = higher priority)
     * @var array<string, int>
     */
    protected array $categoryPriority = [];

    /**
     * Static cache for active canonical categories
     * @var array<string, JobCategory>|null
     */
    private static ?array $canonicalCategoriesCache = null;

    /**
     * Last time the cache was refreshed
     * @var int|null
     */
    private static ?int $lastCacheRefresh = null;

    /**
     * Cache TTL in seconds (5 minutes)
     * @var int
     */
    private const CACHE_TTL = 300;

    /**
     * Random delay configuration for HTTP requests (in seconds)
     * @var array
     */
    private array $delayConfig;

    /**
     * Whether to include non-English job titles during this run (per schedule rule)
     */
    private bool $allowNonEnglish = false;

    /**
     * Schedule rule ID for tracking missed call notifications
     */
    private ?int $scheduleRuleId = null;

    /**
     * List of diverse User-Agent strings for rotation to avoid detection
     * @var array<string>
     */
    private array $userAgents = [
        // Chrome on Windows
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',

        // Chrome on macOS
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',

        // Chrome on Linux
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',

        // Firefox on Windows
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',

        // Firefox on macOS
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:122.0) Gecko/20100101 Firefox/122.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',

        // Safari on macOS
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.2 Safari/605.1.15',

        // Edge on Windows
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',

        // Mobile User Agents for diversity
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',

        // Opera
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********'
    ];

    /**
     * JobsAfService constructor.
     *
     * @param JobRepository $jobRepository
     * @param FilterRepository $filterRepository
     * @param EmailService $emailService
     * @param SystemErrorNotificationService $errorNotificationService
     * @param MissedJobService $missedJobService
     * @param CategoryMappingService $categoryMappingService
     */
    public function __construct(
        JobRepository $jobRepository,
        FilterRepository $filterRepository,
        EmailService $emailService,
        SystemErrorNotificationService $errorNotificationService,
        MissedJobService $missedJobService,
        CategoryMappingService $categoryMappingService
    ) {
        $this->jobRepository = $jobRepository;
        $this->filterRepository = $filterRepository;
        $this->emailService = $emailService;
        $this->errorNotificationService = $errorNotificationService;
        $this->missedJobService = $missedJobService;
        $this->categoryMappingService = $categoryMappingService;

        // Load default filters from configuration
        $this->defaultFilters = config('jobseeker.jobs_af_default_filters', []);

        // Load category priorities from configuration
        $this->categoryPriority = config('jobseeker.jobs_af_category_priorities', []);

        // Initialize delay configuration from config with validation
        $configuredDelays = config('jobseeker.jobs_af_scraping.random_delays', [
            'min_delay' => 120,  // 2 minutes default
            'max_delay' => 600,  // 10 minutes default
            'enabled' => true,
            'skip_first_request' => true
        ]);

        // Validate the loaded configuration to prevent issues from misconfigured values
        $this->delayConfig = $this->validateDelayConfig($configuredDelays);

        // Load categories cache on instantiation
        $this->loadCanonicalCategoriesCache();
    }

    /**
     * Get filters for a specific schedule rule or fallback to defaults
     * 
     * @param int|null $scheduleRuleId
     * @param array $categoryIds Manual category filtering (takes precedence)
     * @return array Filters ready for Jobs.af API consumption (with translation applied)
     */
    protected function getFiltersForScheduleRule(?int $scheduleRuleId, array $categoryIds = []): array
    {
        try {
            // Priority 1: Manual category filtering takes precedence (provider identifiers like ACBAR)
            if (!empty($categoryIds)) {
                $requested = array_map('strval', $categoryIds);
                $validIdentifiers = [];
                foreach ($requested as $identifier) {
                    $exists = \Modules\JobSeeker\Entities\ProviderJobCategory::where('provider_name', 'jobs.af')
                        ->where('provider_identifier', $identifier)
                        ->exists();
                    if ($exists) { $validIdentifiers[] = $identifier; }
                    else {
                        Log::warning('JobsAfService: Invalid provider identifier (manual)', [
                            'provider' => 'jobs.af', 'identifier' => $identifier
                        ]);
                    }
                }

                Log::info('JobsAfService: Using manual provider identifiers for categories', [
                    'valid_identifiers' => $validIdentifiers,
                    'count' => count($validIdentifiers)
                ]);

                $manualFilters = $this->defaultFilters;
                $manualFilters['searchFilters'] = $manualFilters['searchFilters'] ?? [];
                $manualFilters['searchFilters']['categories'] = $validIdentifiers;
                return $manualFilters;
            }

            // Priority 2: If a schedule rule ID is provided, use translated filters
            if ($scheduleRuleId) {
                // Use new translation method to get filters ready for Jobs.af API
                $translatedFilters = $this->filterRepository->getJobsAfTranslatedFilters($scheduleRuleId);

                // Apply advanced settings to service configuration
                $this->applyAdvancedSettings($translatedFilters, $scheduleRuleId);

                Log::info('JobsAfService: Using translated filters for schedule rule', [
                    'schedule_rule_id' => $scheduleRuleId,
                    'categories_count' => count($translatedFilters['searchFilters']['categories'] ?? []),
                    'locations_count' => count($translatedFilters['searchFilters']['locations'] ?? []),
                    'search_term' => $translatedFilters['searchFilters']['searchTerm'] ?? '',
                    'work_type' => $translatedFilters['searchFilters']['workType'] ?? '',
                    'page_number' => $translatedFilters['page'] ?? 1,
                    'advanced_settings_applied' => true
                ]);

                return $translatedFilters;
            }

            Log::info('JobsAfService: Using default filters (no rule-specific filters found)', [
                'schedule_rule_id' => $scheduleRuleId
            ]);

            return $this->defaultFilters;

        } catch (\Exception $e) {
            Log::warning('JobsAfService: Error loading translated filters, falling back to defaults', [
                'schedule_rule_id' => $scheduleRuleId,
                'error' => $e->getMessage()
            ]);

            return $this->defaultFilters;
        }
    }

    /**
     * Apply advanced settings from database to service configuration
     *
     * @param array $filters
     * @param int $scheduleRuleId
     * @return void
     */
    protected function applyAdvancedSettings(array $filters, int $scheduleRuleId): void
    {
        try {
            // Extract advanced settings from filters array
            $requestDelay = $filters['request_delay'] ?? null;
            $userAgentRotation = $filters['user_agent_rotation'] ?? null;
            $randomDelays = $filters['random_delays'] ?? null;
            $maxPages = $filters['max_pages'] ?? null;

            // Apply request delay settings
            if ($requestDelay !== null) {
                $delaySeconds = (float) $requestDelay;

                // Configure delay settings - convert seconds to the service's expected format
                $newDelayConfig = [
                    'enabled' => $delaySeconds > 0,
                    'min_delay' => max(0, (int) ($delaySeconds * 0.8)), // 80% of target as minimum
                    'max_delay' => max(1, (int) ($delaySeconds * 1.2)), // 120% of target as maximum
                    'skip_first_request' => true, // Keep existing behavior
                ];

                $this->setDelayConfig($newDelayConfig);

                Log::info('JobsAfService: Applied request delay settings from database', [
                    'rule_id' => $scheduleRuleId,
                    'target_delay_seconds' => $delaySeconds,
                    'applied_config' => $newDelayConfig
                ]);
            }

            // Note: User agent rotation and random delays are currently always enabled
            // but could be made configurable here in the future
            if ($userAgentRotation !== null) {
                Log::debug('JobsAfService: User agent rotation setting noted', [
                    'rule_id' => $scheduleRuleId,
                    'enabled' => (bool) $userAgentRotation
                ]);
            }

            if ($randomDelays !== null) {
                Log::debug('JobsAfService: Random delays setting noted', [
                    'rule_id' => $scheduleRuleId,
                    'enabled' => (bool) $randomDelays
                ]);
            }

            if ($maxPages !== null) {
                Log::debug('JobsAfService: Max pages setting noted for future use', [
                    'rule_id' => $scheduleRuleId,
                    'max_pages' => (int) $maxPages
                ]);
                // TODO: This could be used to limit pagination in future implementations
            }

        } catch (\Exception $e) {
            Log::warning('JobsAfService: Error applying advanced settings', [
                'rule_id' => $scheduleRuleId,
                'error' => $e->getMessage(),
                'filters' => $filters
            ]);
        }
    }

    /**
     * Get a random User-Agent string for requests to avoid detection
     *
     * @return string
     */
    protected function getRandomUserAgent(): string
    {
        $randomIndex = array_rand($this->userAgents);
        $selectedUserAgent = $this->userAgents[$randomIndex];

        Log::debug('JobsAfService: Selected random User-Agent', [
            'index' => $randomIndex,
            'user_agent' => substr($selectedUserAgent, 0, 50) . '...' // Log only first 50 chars for brevity
        ]);

        return $selectedUserAgent;
    }

    /**
     * Apply random delay between HTTP requests to mimic human behavior
     *
     * @param bool $isFirstRequest Whether this is the first request in a sequence
     * @return void
     */
    protected function applyRandomDelay(bool $isFirstRequest = false): void
    {
        // Skip delay if disabled or if it's the first request and configured to skip
        if (!$this->delayConfig['enabled'] || ($isFirstRequest && $this->delayConfig['skip_first_request'])) {
            if ($isFirstRequest && $this->delayConfig['skip_first_request']) {
                Log::debug('JobsAfService: Skipping delay for first HTTP request');
            }
            return;
        }

        // Validate delay configuration to prevent RandomRangeException
        $minDelay = $this->delayConfig['min_delay'] ?? 0;
        $maxDelay = $this->delayConfig['max_delay'] ?? 0;

        // Ensure values are integers and non-negative
        $minDelay = max(0, (int) $minDelay);
        $maxDelay = max(0, (int) $maxDelay);

        // Validate that min_delay is not greater than max_delay
        if ($minDelay > $maxDelay) {
            Log::warning('JobsAfService: Invalid delay configuration - min_delay > max_delay, using defaults', [
                'original_min' => $this->delayConfig['min_delay'],
                'original_max' => $this->delayConfig['max_delay'],
                'corrected_min' => 120,
                'corrected_max' => 600
            ]);

            // Use safe default values
            $minDelay = 120;  // 2 minutes
            $maxDelay = 600;  // 10 minutes
        }

        // Additional safety check - if both values are 0, skip delay
        if ($minDelay === 0 && $maxDelay === 0) {
            Log::info('JobsAfService: Both min and max delay are 0, skipping delay');
            return;
        }

        try {
            // Generate random delay between min and max (in seconds)
            $delaySeconds = random_int($minDelay, $maxDelay);

            Log::info('JobsAfService: Applying random delay between HTTP requests', [
                'delay_seconds' => $delaySeconds,
                'delay_minutes' => round($delaySeconds / 60, 2),
                'min_configured' => $minDelay,
                'max_configured' => $maxDelay,
                'is_first_request' => $isFirstRequest
            ]);

            // Apply the delay
            sleep($delaySeconds);

            Log::debug('JobsAfService: Random delay completed', [
                'delay_applied_seconds' => $delaySeconds
            ]);

        } catch (\Exception $e) {
            Log::error('JobsAfService: RandomException occurred while generating delay', [
                'min_delay' => $minDelay,
                'max_delay' => $maxDelay,
                'error' => $e->getMessage(),
                'is_first_request' => $isFirstRequest
            ]);

            // Fallback: use a fixed moderate delay
            $fallbackDelay = 300; // 5 minutes
            Log::info('JobsAfService: Using fallback delay', [
                'fallback_delay_seconds' => $fallbackDelay
            ]);

            sleep($fallbackDelay);
        }
    }

    /**
     * Get current delay configuration
     *
     * @return array
     */
    public function getDelayConfig(): array
    {
        return $this->delayConfig;
    }

    /**
     * Update delay configuration with validation
     *
     * @param array $config New delay configuration
     * @return void
     */
    public function setDelayConfig(array $config): void
    {
        $originalConfig = $this->delayConfig;

        // Merge the new configuration
        $newConfig = array_merge($this->delayConfig, $config);

        // Validate the merged configuration
        $validatedConfig = $this->validateDelayConfig($newConfig);

        $this->delayConfig = $validatedConfig;

        // Log the configuration update with any corrections applied
        Log::info('JobsAfService: Updated delay configuration', [
            'original_config' => $originalConfig,
            'requested_config' => $config,
            'final_config' => $this->delayConfig,
            'was_corrected' => $newConfig !== $validatedConfig
        ]);
    }

    /**
     * Validate delay configuration and correct invalid values
     *
     * @param array $config Delay configuration to validate
     * @return array Validated and corrected configuration
     */
    private function validateDelayConfig(array $config): array
    {
        $validated = $config;
        $corrections = [];

        // Validate min_delay
        if (isset($config['min_delay'])) {
            $minDelay = (int) $config['min_delay'];
            if ($minDelay < 0) {
                $validated['min_delay'] = 0;
                $corrections[] = "min_delay was negative ({$config['min_delay']}), corrected to 0";
            } else {
                $validated['min_delay'] = $minDelay;
            }
        }

        // Validate max_delay
        if (isset($config['max_delay'])) {
            $maxDelay = (int) $config['max_delay'];
            if ($maxDelay < 0) {
                $validated['max_delay'] = 600; // Default to 10 minutes
                $corrections[] = "max_delay was negative ({$config['max_delay']}), corrected to 600 seconds";
            } else {
                $validated['max_delay'] = $maxDelay;
            }
        }

        // Ensure min_delay is not greater than max_delay
        $minDelay = $validated['min_delay'] ?? 0;
        $maxDelay = $validated['max_delay'] ?? 600;

        if ($minDelay > $maxDelay) {
            if ($minDelay <= 600) {
                // If min_delay is reasonable, adjust max_delay
                $validated['max_delay'] = max($minDelay, 600);
                $corrections[] = "max_delay ({$maxDelay}) was less than min_delay ({$minDelay}), adjusted max_delay to {$validated['max_delay']}";
            } else {
                // If min_delay is too high, use safe defaults
                $validated['min_delay'] = 120;
                $validated['max_delay'] = 600;
                $corrections[] = "min_delay ({$minDelay}) > max_delay ({$maxDelay}), reset to safe defaults (120-600 seconds)";
            }
        }

        // Validate enabled flag
        if (isset($config['enabled'])) {
            $validated['enabled'] = (bool) $config['enabled'];
        }

        // Validate skip_first_request flag
        if (isset($config['skip_first_request'])) {
            $validated['skip_first_request'] = (bool) $config['skip_first_request'];
        }

        // Log any corrections that were made
        if (!empty($corrections)) {
            Log::warning('JobsAfService: Delay configuration corrected', [
                'corrections' => $corrections,
                'original_config' => $config,
                'validated_config' => $validated
            ]);
        }

        return $validated;
    }

    /**
     * Flush the static category cache
     * This should be called when categories are modified
     *
     * @return void
     */
    public static function flushCategoryCache(): void
    {
        self::$canonicalCategoriesCache = null;
        self::$lastCacheRefresh = null;
        Log::info('JobsAfService: Category cache flushed');
    }

    /**
     * Check if the cache needs refreshing based on TTL
     *
     * @return bool
     */
    private function shouldRefreshCache(): bool
    {
        if (self::$canonicalCategoriesCache === null || self::$lastCacheRefresh === null) {
            return true;
        }

        return (time() - self::$lastCacheRefresh) > self::CACHE_TTL;
    }

    /**
     * Get HTTP client options with secure defaults and rotating User-Agent
     *
     * @return array
     */
    protected function getHttpClientOptions(): array
    {
        return [
            'timeout' => 30,
            'connect_timeout' => 10,
            'verify' => true, // Always enforce SSL verification for security
            'headers' => [
                'User-Agent' => $this->getRandomUserAgent(),
                'Accept' => 'application/json, text/html, application/xhtml+xml, application/xml;q=0.9, */*;q=0.8',
                'Accept-Language' => 'en-US,en;q=0.5',
                'Accept-Encoding' => 'gzip, deflate',
                'Connection' => 'keep-alive',
                'Upgrade-Insecure-Requests' => '1'
            ]
        ];
    }

    /**
     * Fetch jobs from the jobs.af API with custom filters
     *
     * @param int $page
     * @param int $maxRetries
     * @param array|null $customFilters Custom filters to use instead of defaults
     * @return array
     */
    public function fetchJobs(int $page = 1, int $maxRetries = 3, ?array $customFilters = null): array
    {
        Log::info("JobsAfService: Fetching jobs from API for page: {$page}");

        $attempt = 0;
        $lastException = null;

        while ($attempt < $maxRetries) {
            try {
                $attempt++;
                $filters = $customFilters ?: $this->defaultFilters;
                $filters['page'] = $page;

                // Apply random delay if this is not the first page request (to avoid detection)
                if ($page > 1) {
                    $this->applyRandomDelay(false);
                }

                // Configure HTTP client with secure options and rotating User-Agent headers
                $httpOptions = $this->getHttpClientOptions();
                $response = Http::withOptions([
                    'timeout' => $httpOptions['timeout'],
                    'connect_timeout' => $httpOptions['connect_timeout'],
                    'verify' => $httpOptions['verify']
                ])
                ->withHeaders(array_merge($httpOptions['headers'], [
                    'Origin' => 'https://jobs.af',
                    'Referer' => 'https://jobs.af/'
                ]))
                ->get($this->apiUrl, [
                    'filter' => json_encode($filters)
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    Log::info("JobsAfService: Successfully fetched jobs from API. Total jobs: " . ($data['total'] ?? 0));
                    return $data;
                } else {
                    $errorMessage = "Failed to fetch jobs. HTTP Status: {$response->status()}";
                    if ($response->body()) {
                        $errorMessage .= " Response: " . substr($response->body(), 0, 200);
                    }
                    Log::error($errorMessage);

                    // Only sleep and retry if we haven't reached max retries
                    if ($attempt < $maxRetries) {
                        $backoffSeconds = pow(2, $attempt); // Exponential backoff
                        Log::info("Retrying in {$backoffSeconds} seconds (attempt {$attempt} of {$maxRetries})");
                        sleep($backoffSeconds);
                        continue;
                    }

                    throw new Exception($errorMessage);
                }
            } catch (Exception $e) {
                $lastException = $e;
                $message = "Exception occurred while fetching jobs (attempt {$attempt} of {$maxRetries}): " . $e->getMessage();
                Log::error($message, [
                    'exception' => get_class($e),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]);

                // Only sleep and retry if we haven't reached max retries
                if ($attempt < $maxRetries) {
                    $backoffSeconds = pow(2, $attempt); // Exponential backoff
                    Log::info("Retrying in {$backoffSeconds} seconds");
                    sleep($backoffSeconds);
                    continue;
                }
                // No sleep needed on final attempt since we're about to throw
            }
        }

        // If we get here, all retries failed
        throw new Exception(
            "Failed to fetch jobs after {$maxRetries} attempts. Last error: " . 
            ($lastException ? $lastException->getMessage() : 'Unknown error')
        );
    }

    /**
     * Check if a string is primarily in English
     * 
     * @param string $text
     * @return bool
     */
    protected function isEnglishText(string $text): bool
    {
        // Backward compatibility wrapper around centralized service
        $detector = app(\Modules\JobSeeker\Services\LanguageDetectionService::class);
        return $detector->isEnglishTitle($text);
    }

    /**
     * Get provider categories from schedule rule for filtering
     * This is the new correct approach using provider categories
     *
     * @param int|null $scheduleRuleId
     * @return array Array of provider_identifier values for API calls
     */
    protected function getProviderCategoriesFromScheduleRule(?int $scheduleRuleId): array
    {
        if (!$scheduleRuleId) {
            // No schedule rule - return empty array (fetch all categories)
            return [];
        }

        try {
            $rule = \Modules\JobSeeker\Entities\CommandScheduleRule::with('filter')->find($scheduleRuleId);
            if (!$rule || !$rule->filter || empty($rule->filter->categories)) {
                Log::info('JobsAfService: No category filter in schedule rule', [
                    'schedule_rule_id' => $scheduleRuleId
                ]);
                return []; // No category filter - fetch all
            }

            $providerCategories = \Modules\JobSeeker\Entities\ProviderJobCategory::whereIn('id', $rule->filter->categories)
                ->where('provider_name', 'jobs.af')
                ->pluck('provider_identifier')
                ->toArray();

            Log::info('JobsAfService: Retrieved provider categories from schedule rule filter', [
                'schedule_rule_id' => $scheduleRuleId,
                'filter_category_ids' => $rule->filter->categories,
                'provider_identifiers' => $providerCategories
            ]);

            return $providerCategories;

        } catch (\Exception $e) {
            Log::error('JobsAfService: Error getting provider categories from schedule rule', [
                'schedule_rule_id' => $scheduleRuleId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get provider locations from schedule rule for filtering
     *
     * @param int|null $scheduleRuleId
     * @return array Array of provider_identifier values for API calls
     */
    protected function getProviderLocationsFromScheduleRule(?int $scheduleRuleId): array
    {
        if (!$scheduleRuleId) {
            return [];
        }

        try {
            $rule = \Modules\JobSeeker\Entities\CommandScheduleRule::with('filter')->find($scheduleRuleId);
            if (!$rule || !$rule->filter || empty($rule->filter->locations)) {
                return [];
            }

            return \Modules\JobSeeker\Entities\ProviderJobLocation::whereIn('id', $rule->filter->locations)
                ->where('provider_name', 'jobs.af')
                ->where('is_active', true)
                ->pluck('provider_identifier')
                ->toArray();

        } catch (\Exception $e) {
            Log::error('JobsAfService: Error getting provider locations from schedule rule', [
                'schedule_rule_id' => $scheduleRuleId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Save job with provider categories (new correct approach)
     *
     * @param array $jobData
     * @param array $providerCategoryIds Array of ProviderJobCategory IDs
     * @return \Modules\JobSeeker\Entities\Job
     */
    protected function saveJobWithProviderCategories(array $jobData, array $providerCategoryIds): \Modules\JobSeeker\Entities\Job
    {
        // Create or update the job
        $job = $this->jobRepository->createOrUpdate($jobData);

        // Sync provider categories (direct relationship)
        if (!empty($providerCategoryIds)) {
            $job->providerCategories()->sync($providerCategoryIds);

            Log::info('JobsAfService: Synced job with provider categories', [
                'job_id' => $job->id,
                'position' => $job->position,
                'provider_category_ids' => $providerCategoryIds
            ]);
        }

        return $job;
    }

    /**
     * Get provider category IDs for a job based on its position
     * This replaces the old canonical category determination logic
     *
     * @param string $position
     * @return array Array of ProviderJobCategory IDs
     */
    protected function getProviderCategoryIdsForJob(string $position): array
    {
        try {
            // For now, we'll use a simple fallback approach
            // In the future, this could be enhanced with ML or better matching

            // Get all Jobs.af provider categories
            $allProviderCategories = \Modules\JobSeeker\Entities\ProviderJobCategory::where('provider_name', 'jobs.af')
                ->get();

            // Simple keyword matching against provider category names
            $position = strtolower($position);
            $matchedCategoryIds = [];

            foreach ($allProviderCategories as $category) {
                $categoryName = strtolower($category->name);

                // Simple contains check - can be enhanced later
                if (str_contains($position, $categoryName) || str_contains($categoryName, $position)) {
                    $matchedCategoryIds[] = $category->id;
                }
            }

            // If no matches, default to a general category (e.g., first IT category)
            if (empty($matchedCategoryIds)) {
                $defaultCategory = \Modules\JobSeeker\Entities\ProviderJobCategory::where('provider_name', 'jobs.af')
                    ->where('name', 'LIKE', '%IT%')
                    ->orWhere('name', 'LIKE', '%Information Technology%')
                    ->first();

                if ($defaultCategory) {
                    $matchedCategoryIds[] = $defaultCategory->id;
                }
            }

            Log::debug('JobsAfService: Matched provider categories for position', [
                'position' => $position,
                'matched_category_ids' => $matchedCategoryIds
            ]);

            return $matchedCategoryIds;

        } catch (\Exception $e) {
            Log::error('JobsAfService: Error getting provider category IDs for job', [
                'position' => $position,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Load or refresh the canonical categories cache
     *
     * @deprecated This method will be removed after migration to provider categories
     * @return void
     */
    private function loadCanonicalCategoriesCache(): void
    {
        try {
            // Check if we need to refresh the cache
            if ($this->shouldRefreshCache()) {
                self::$canonicalCategoriesCache = JobCategory::where('is_active', true)
                    ->where('is_canonical', true)
                    ->get()
                    ->keyBy('name')
                    ->all();

                self::$lastCacheRefresh = time();

                Log::info('JobsAfService: Loaded canonical categories cache', [
                    'count' => count(self::$canonicalCategoriesCache),
                    'timestamp' => self::$lastCacheRefresh,
                    'category_names' => array_keys(self::$canonicalCategoriesCache)
                ]);
            }
        } catch (\Exception $e) {
            Log::error('JobsAfService: Failed to load canonical categories cache', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // Initialize empty cache to prevent repeated failed attempts
            self::$canonicalCategoriesCache = [];
            self::$lastCacheRefresh = time();
        }
    }

    /**
     * Determine job categories based on position and predefined mappings
     *
     * @param string $position
     * @return array
     */
    protected function determineJobCategories(string $position): array
    {
        $position = strtolower($position);
        $categories = [];

        try {
            // Ensure cache is loaded
            $this->loadCanonicalCategoriesCache();

            // Map position keywords to canonical category IDs (aligned with actual database category names)
            $categoryMappings = [
                // IT and Software related categories (mapped to "Technology")
                'Technology' => ['technology', 'information technology', 'it specialist', 'network engineer', 'helpdesk specialist', 'technical support', 'developer', 'engineer', 'software', 'programming', 'web development', 'database', 'system architect', 'computer science', 'data scientist', 'machine learning', 'artificial intelligence', 'backend', 'frontend', 'web', 'system', 'network'],

                // Management and Leadership categories
                'Management' => ['manager', 'management', 'supervisor', 'coordinator', 'project manager'],
                'Leadership' => ['lead', 'chief', 'head of', 'director', 'ceo', 'cto', 'cfo', 'executive'],

                // Administrative and Business categories  
                'Administration' => ['administrative', 'admin', 'office manager', 'executive secretary', 'administrative assistant', 'office coordinator', 'business administration', 'business analyst', 'operations', 'business development'],
                'Human Resources' => ['human resources', 'hr', 'recruitment', 'talent acquisition', 'hiring manager', 'hr specialist'],

                // Sales and Marketing
                'Marketing' => ['sales', 'marketing', 'social media', 'seo specialist', 'content strategist', 'digital marketing'],

                // Professional Services
                'Translation' => ['translation', 'translator', 'interpreter', 'linguistics', 'language'],
                'Research/Survey' => ['research', 'survey', 'data analyst', 'scientist', 'researcher', 'analysis'],

                // Healthcare and Social
                'Health' => ['nurse', 'nursing', 'healthcare', 'medical assistant', 'clinical', 'health', 'medical', 'doctor'],

                // Security and Safety
                'Security' => ['security', 'safety', 'guard', 'protection', 'security officer', 'safety officer'],

                // Other categories
                'Transportation' => ['transportation', 'logistics', 'driver', 'supply chain', 'shipping'],
                'Travel/Tourism' => ['travel', 'tourism', 'hospitality', 'tour guide', 'hotel'],
                'Education' => ['education', 'teacher', 'instructor', 'trainer', 'academic', 'school', 'university'],
                'Engineering' => ['engineering', 'engineer', 'civil engineer', 'mechanical engineer', 'electrical engineer'],
                'Finance' => ['finance', 'financial', 'accounting', 'accountant', 'budget', 'fiscal'],
                'Banking' => ['banking', 'bank', 'banker', 'credit', 'loan officer'],
                'Legal' => ['legal', 'lawyer', 'attorney', 'paralegal', 'law', 'legal aid'],
                'Media' => ['media', 'journalism', 'journalist', 'communications', 'public relations'],
                'Programme' => ['programme', 'program', 'project', 'implementation'],

                // Industrial and Technical
                'Industrial' => ['industrial', 'manufacturing', 'production', 'quality control', 'technician'],

                // Internships
                'Internships' => ['intern', 'internship', 'trainee', 'junior', 'entry level']
            ];

            // Convert all keywords to lowercase once
            $categoryMappings = array_map(function($keywords) {
                return array_map('mb_strtolower', $keywords);
            }, $categoryMappings);

            // Convert position to lowercase once for comparison
            $position = mb_strtolower($position);

            foreach ($categoryMappings as $categoryName => $keywords) {
                if (isset(self::$canonicalCategoriesCache[$categoryName])) {
                    foreach ($keywords as $keyword) {
                        if (str_contains($position, $keyword)) {
                            $categories[] = self::$canonicalCategoriesCache[$categoryName]->id;
                            break; // Break once we've matched a keyword for this category
                        }
                    }
                }
            }

            // If no categories matched, default to Technology if position contains relevant keywords
            if (empty($categories) && isset(self::$canonicalCategoriesCache['Technology'])) {
                $techKeywords = ['software', 'developer', 'programming', 'web', 'database', 'system', 'network', 'it', 'technical'];
                foreach ($techKeywords as $keyword) {
                    if (str_contains($position, $keyword)) {
                        $categories[] = self::$canonicalCategoriesCache['Technology']->id;
                        Log::info("JobsAfService: Matched Technology category based on tech keyword: {$keyword}", [
                            'position' => $position
                        ]);
                        break;
                    }
                }
            }

            // Remove duplicates
            $categories = array_unique($categories);

            Log::info('JobsAfService: Determined categories for position', [
                'position' => $position,
                'categories' => $categories,
                'categories_count' => count($categories)
            ]);

            return $categories;

        } catch (\Exception $e) {
            Log::error('JobsAfService: Error determining job categories', [
                'position' => $position,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback to empty array if something goes wrong
            return [];
        }
    }

    /**
     * Format job data for database storage with intelligent category assignment.
     * Handles both explicit provider categories and intelligent fallback assignment.
     *
     * @param array $jobData Raw job data from API
     * @param array $providerCategoryIds Array of ProviderJobCategory IDs from schedule filter
     * @return array Formatted job data including provider_category_ids and canonical categories
     *
     * Purpose: Transform API data to database format with complete category mapping.
     * Side effects: Calls CategoryMappingService and assignProviderCategoriesFromJobTitle,
     * logs category assignment decisions for troubleshooting.
     * Errors: Graceful fallback to legacy category determination if intelligent assignment fails.
     */
    protected function formatJobData(array $jobData, array $providerCategoryIds = []): array
    {
        $position = $jobData['position'] ?? '';
        $source = 'Jobs.af';

        // Extract company name consistently
        $companyName = $jobData['company']['name'] ?? '';
        $locations = $jobData['locations'] ?? '';
        $sourceId = $jobData['id'] ?? null;

        // Use CategoryMappingService to get canonical categories from provider categories
        $canonicalCategories = [];
        Log::info('JobsAfService: formatJobData category determination start', [
            'provider_category_ids' => $providerCategoryIds,
            'provider_category_ids_empty' => empty($providerCategoryIds),
            'position' => $position
        ]);
        
        if (!empty($providerCategoryIds)) {
            $canonicalCategories = $this->categoryMappingService->mapProviderToCanonicalCategoryIds($providerCategoryIds, 'jobs.af');
            Log::info('JobsAfService: Mapped provider categories to canonical categories', [
                'position' => $position,
                'provider_category_ids' => $providerCategoryIds,
                'canonical_category_ids' => $canonicalCategories
            ]);
        } else {
            // Fallback: if no provider categories provided, use intelligent category assignment
            Log::info('JobsAfService: No provider category IDs provided, using intelligent category assignment', [
                'position' => $position
            ]);

            // Get provider categories through intelligent assignment
            $assignedProviderCategories = $this->assignProviderCategoriesFromJobTitle($position);
            $providerCategoryIds = array_column($assignedProviderCategories, 'id');

            // Map to canonical categories
            if (!empty($providerCategoryIds)) {
                $canonicalCategories = $this->categoryMappingService->mapProviderToCanonicalCategoryIds($providerCategoryIds, 'jobs.af');
                Log::info('JobsAfService: Intelligent category assignment completed', [
                    'position' => $position,
                    'assigned_provider_categories' => $assignedProviderCategories,
                    'provider_category_ids' => $providerCategoryIds,
                    'canonical_category_ids' => $canonicalCategories
                ]);
            } else {
                // Final fallback to legacy method
                $canonicalCategories = $this->determineJobCategories($position);
                Log::info('JobsAfService: Used legacy fallback category determination', [
                    'position' => $position,
                    'canonical_category_ids' => $canonicalCategories
                ]);
            }
        }

        // Ensure we have provider category IDs for the return (either from parameter or intelligent assignment)
        $finalProviderCategoryIds = !empty($providerCategoryIds) ? $providerCategoryIds :
            (isset($assignedProviderCategories) ? array_column($assignedProviderCategories, 'id') : []);

        return [
            'position' => $position,
            'number_of_vacancy' => $jobData['number_of_vacancy'] ?? null,
            'vacancy_number' => $jobData['vacancy_number'] ?? null,
            'is_featured' => $jobData['is_featured'] ?? false,
            'locations' => $locations,
            'contract_type' => $jobData['contract_type'] ?? '',
            'work_type' => $jobData['work_type'] ?? '',
            'gender' => $jobData['gender'] ?? '',
            'company_name' => $companyName,
            'publish_date' => $jobData['publish_date'] ?? now(),
            'expire_date' => $jobData['expire_date'] ?? null,
            'salary' => $jobData['salary'] ?? '',
            'can_apply_online' => $jobData['can_apply_online'] ?? false,
            'slug' => $jobData['slug'] ?? Str::slug($position . '-' . ($companyName ?: 'company')),
            'categories' => $canonicalCategories, // Canonical categories for job_category_pivot
            'provider_category_ids' => $finalProviderCategoryIds, // Provider category IDs for job_provider_category_pivot
            'source' => $source,
            'source_id' => $sourceId,
            // Additional fields for enhanced de-duplication consistency
            'organizationName' => $companyName,
            'jobTitle' => $position,
            'location' => $locations,
            'url' => $jobData['url'] ?? null,
        ];
    }

    /**
     * Intelligently assign provider categories to a job based on its title.
     * Uses keyword matching against Jobs.af provider categories.
     *
     * @param string $jobTitle The job title to analyze
     * @return array Array of provider category objects with id and name
     */
    protected function assignProviderCategoriesFromJobTitle(string $jobTitle): array
    {
        if (empty($jobTitle)) {
            return [];
        }

        $jobTitle = strtolower(trim($jobTitle));
        $assignedCategories = [];

        // Define keyword patterns for major Jobs.af categories
        $categoryKeywords = [
            // Technology categories (271-283)
            'technology' => [
                'keywords' => ['software', 'developer', 'programming', 'it ', 'computer', 'data', 'system', 'web', 'mobile', 'app', 'database', 'network', 'security', 'hardware', 'technical'],
                'category_ids' => [271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283]
            ],
            // Management categories (290, 296, 297, 326, 327, 335)
            'management' => [
                'keywords' => ['manager', 'management', 'director', 'supervisor', 'coordinator', 'lead', 'head', 'chief', 'officer', 'consultant', 'adviser', 'quality'],
                'category_ids' => [290, 296, 297, 326, 327, 335]
            ],
            // Administration categories (288, 289, 291)
            'administration' => [
                'keywords' => ['admin', 'administrative', 'clerk', 'assistant', 'secretary', 'office', 'customer service', 'support'],
                'category_ids' => [288, 289, 291]
            ],
            // Human Resources (284, 285, 286)
            'hr' => [
                'keywords' => ['human resource', 'hr ', 'personnel', 'recruitment', 'hiring'],
                'category_ids' => [284, 285, 286]
            ],
            // Marketing (287, 308)
            'marketing' => [
                'keywords' => ['marketing', 'sales', 'graphic', 'designer', 'promotion', 'advertising'],
                'category_ids' => [287, 308]
            ],
            // Finance (317, 318, 334)
            'finance' => [
                'keywords' => ['accounting', 'finance', 'financial', 'banking', 'economic', 'budget'],
                'category_ids' => [317, 318, 334]
            ],
            // Engineering (309, 310, 311, 312)
            'engineering' => [
                'keywords' => ['engineer', 'engineering', 'electrical', 'mechanical', 'civil', 'industrial', 'soil'],
                'category_ids' => [309, 310, 311, 312]
            ],
            // Health (314, 315, 316)
            'health' => [
                'keywords' => ['health', 'medical', 'nurse', 'nursing', 'doctor', 'physician', 'fitness'],
                'category_ids' => [314, 315, 316]
            ],
            // Education (299, 320, 321, 322, 323)
            'education' => [
                'keywords' => ['education', 'teacher', 'instructor', 'training', 'intern', 'literature', 'academic'],
                'category_ids' => [299, 320, 321, 322, 323]
            ],
            // Media (319, 324, 325, 332)
            'media' => [
                'keywords' => ['media', 'journalist', 'editor', 'writer', 'communication', 'reporter', 'content'],
                'category_ids' => [319, 324, 325, 332]
            ]
        ];

        // Find matching categories
        foreach ($categoryKeywords as $categoryGroup => $data) {
            foreach ($data['keywords'] as $keyword) {
                if (strpos($jobTitle, $keyword) !== false) {
                    // Pick the most relevant category ID from the group (first one)
                    $categoryId = $data['category_ids'][0];

                    // Avoid duplicates
                    $alreadyAssigned = false;
                    foreach ($assignedCategories as $assigned) {
                        if ($assigned['id'] === $categoryId) {
                            $alreadyAssigned = true;
                            break;
                        }
                    }

                    if (!$alreadyAssigned) {
                        // Get the actual category name from database
                        $providerCategory = \Modules\JobSeeker\Entities\ProviderJobCategory::find($categoryId);
                        if ($providerCategory) {
                            $assignedCategories[] = [
                                'id' => $categoryId,
                'name' => $providerCategory->name,
                                'matched_keyword' => $keyword
                            ];
                        }
                    }
                    break; // Only match one keyword per group
                }
            }
        }

        // If no specific matches, assign a general category based on common patterns
        if (empty($assignedCategories)) {
            // Default to Administrative for general positions
            $defaultCategoryId = 288; // Administrative
            $providerCategory = \Modules\JobSeeker\Entities\ProviderJobCategory::find($defaultCategoryId);
            if ($providerCategory) {
                $assignedCategories[] = [
                    'id' => $defaultCategoryId,
                    'name' => $providerCategory->name,
                    'matched_keyword' => 'default_fallback'
                ];
            }
        }

        Log::info('JobsAfService: Intelligent category assignment completed', [
            'job_title' => $jobTitle,
            'assigned_categories' => $assignedCategories,
            'assignment_count' => count($assignedCategories)
        ]);

        return $assignedCategories;
    }

    /**
     * Check if job is located in Kabul
     * 
     * @param string $locations Job locations string
     * @return bool
     */
    protected function isLocationInKabul(string $locations): bool
    {
        if (empty($locations)) {
            return false;
        }

        return stripos($locations, 'Kabul') !== false;
    }

    /**
     * Check if job data has significant changes
     *
     * @param object $existingJob
     * @param array $newJobData
     * @return bool
     */
    protected function hasJobChanged($existingJob, array $newJobData): bool
    {
        // Important fields to compare for changes
        $compareFields = [
            'position',
            'number_of_vacancy',
            'vacancy_number',
            'is_featured',
            'locations',
            'contract_type',
            'work_type',
            'gender',
            'company_name',
            'expire_date',
            'salary',
            'can_apply_online'
        ];

        foreach ($compareFields as $field) {
            if (isset($newJobData[$field]) && $existingJob->$field != $newJobData[$field]) {
                Log::info("JobsAfService: Job {$existingJob->slug} changed: field {$field} from '{$existingJob->$field}' to '{$newJobData[$field]}'");
                return true;
            }
        }

        return false;
    }

    /**
     * Convert a Job model to an array
     * 
     * @param mixed $job
     * @return array
     */
    protected function jobToArray($job): array
    {
        // If already an array, return it
        if (is_array($job)) {
            return $job;
        }

        // Convert stdClass to array
        if (is_object($job) && get_class($job) === 'stdClass') {
            return [
                'id' => $job->id ?? null,
                'title' => $job->position ?? $job->title ?? null,
                'company' => $job->company ?? null,
                'location' => $job->location ?? $job->locations ?? null,
                'url' => $job->url ?? null,
                'publish_date' => $job->publish_date ?? null,
                'expires_at' => $job->expires_at ?? null,
                'job_type' => $job->job_type ?? null,
                'created_at' => $job->created_at ?? null,
                'updated_at' => $job->updated_at ?? null,
                'deadline' => $job->deadline ?? null,
                'description' => $job->description ?? null,
                'salary' => $job->salary ?? null,
                'original_id' => $job->original_id ?? null,
                'slug' => $job->slug ?? null,
                'source' => $job->source ?? null,
                'is_active' => $job->is_active ?? true,
                'categories' => $job->categories ?? []
            ];
        }

        if (is_object($job) && $job instanceof \Modules\JobSeeker\Entities\Job) {
            return [
                'id' => $job->id,
                'title' => $job->position,
                'company' => $job->company,
                'location' => $job->locations,
                'url' => $job->url,
                'publish_date' => $job->publish_date,
                'expires_at' => $job->expires_at,
                'job_type' => $job->job_type,
                'created_at' => $job->created_at,
                'updated_at' => $job->updated_at,
                'deadline' => $job->deadline,
                'description' => $job->description,
                'salary' => $job->salary,
                'experience' => $job->experience,
                'original_id' => $job->original_id,
                'slug' => $job->slug,
                'source' => $job->source,
                'is_active' => $job->is_active,
                'categories' => $job->categories ? $job->categories->pluck('id')->toArray() : []
            ];
        }

        // Return empty array if input is invalid
        return [];
    }

    /**
     * Get relative time string (e.g., "2 hours ago", "1 day ago")
     *
     * @param string $dateString
     * @return string
     */
    protected function getTimeAgo(string $dateString): string
    {
        try {
            $date = Carbon::parse($dateString);
            $now = Carbon::now();

            if ($date->diffInMinutes($now) < 60) {
                return $date->diffInMinutes($now) . ' minutes ago';
            } elseif ($date->diffInHours($now) < 24) {
                return $date->diffInHours($now) . ' hours ago';
            } else {
                return $date->diffInDays($now) . ' days ago';
            }
        } catch (Exception $e) {
            Log::warning("JobsAfService: Failed to parse date for time ago: {$dateString} - " . $e->getMessage());
            return 'recently';
        }
    }

    /**
     * Format job data for email template
     * 
     * @param array $jobs
     * @return array
     */
    protected function formatJobsForEmail(array $jobs): array
    {
        $formattedJobs = [];
        foreach ($jobs as $job) {
            // Handle different possible data structures 
            $position = $job['position'] ?? $job['title'] ?? '';
            $companyName = $job['company_name'] ?? ($job['company']['name'] ?? ($job['company'] ?? ''));
            $locations = $job['locations'] ?? $job['location'] ?? '';
            $contractType = $job['contract_type'] ?? $job['contractType'] ?? $job['type'] ?? 'Full-time';
            $workType = $job['work_type'] ?? $job['workType'] ?? 'On-site';
            $publishDate = $job['publish_date'] ?? $job['publishDate'] ?? $job['published_at'] ?? now()->subDays(3)->format('Y-m-d H:i:s');

            // Determine job category priority based on position or category
            $priority = 10; // Default low priority
            $position = $position ?: 'Unknown Position'; // Ensure position is never empty
            $positionLower = strtolower($position);
            $category = $job['category'] ?? '';

            // Check position for keywords first
            if (strpos($positionLower, 'developer') !== false || 
                strpos($positionLower, 'engineer') !== false || 
                strpos($positionLower, 'software') !== false || 
                strpos($positionLower, 'it') !== false) {
                $priority = 1; // IT jobs
            } elseif (strpos($positionLower, 'lead') !== false || 
                     strpos($positionLower, 'chief') !== false || 
                     strpos($positionLower, 'head') !== false || 
                     strpos($positionLower, 'director') !== false) {
                $priority = 2; // Leadership jobs
            } elseif (strpos($positionLower, 'manager') !== false || 
                     strpos($positionLower, 'management') !== false) {
                $priority = 3; // Management jobs
            }

            // If category is set, use the priority from categoryPriority array
            if (!empty($category) && isset($this->categoryPriority[$category])) {
                $priority = $this->categoryPriority[$category];
            }

            // Ensure we have a salary value - set to "Negotiable" if not provided
            $salary = !empty($job['salary']) ? $job['salary'] : 'As per company salary scale';

            $formattedJobs[] = [
                'position' => $position,
                'company_name' => $companyName ?: 'Unknown Company',
                'locations' => $locations ?: 'Kabul, Afghanistan',
                'contract_type' => $contractType,
                'work_type' => $workType,
                'publish_date' => $publishDate,
                'salary' => $salary,
                'experience' => $job['experience'] ?? null,
                'slug' => $job['slug'] ?? '',
                'updated_at' => $job['updated_at'] ?? now()->format('Y-m-d H:i:s'),
                'priority' => $priority // Add priority for sorting
            ];

            // Log formatted job data to verify it's complete
            Log::debug('JobsAfService: Formatted job for email', [
                'original_position' => $job['position'] ?? 'not set',
                'formatted_position' => $position,
                'original_locations' => $job['locations'] ?? 'not set',
                'formatted_locations' => $locations ?: 'Kabul, Afghanistan',
                'original_contract_type' => $job['contract_type'] ?? 'not set',
                'formatted_contract_type' => $contractType,
            ]);
        }

        // Sort formatted jobs by priority (lower number first)
        usort($formattedJobs, function($a, $b) {
            return $a['priority'] <=> $b['priority'];
        });

        return $formattedJobs;
    }

    /**
     * Process individual job data during sync
     *
     * @param array $job
     * @param array &$stats
     * @param array &$newJobs
     * @param array &$updatedJobs
     * @return bool
     */
    protected function processJobData(array $job, array &$stats, array &$newJobs, array &$updatedJobs): bool
    {
        try {
            // Skip non-English jobs if rule does not allow them
            if (!$this->allowNonEnglish && !$this->isEnglishText($job['position'])) {
                $stats['non_english_skipped']++;
                return false;
            }

            // Location filtering is handled via provider location identifiers in translated filters; no string heuristics

            // Format job data for database
            $jobData = $this->formatJobData($job);

            // Check if job exists
            $existingJob = $this->jobRepository->findBySlug($job['slug']);

            DB::beginTransaction();
            try {
                $job = null;
                $jobWasCreated = false;
                $jobWasUpdated = false;

                if (!$existingJob) {
                    // Create new job
                    $job = $this->jobRepository->createOrUpdate($jobData);
                    $jobWasCreated = true;

                    // NEW APPROACH: Sync provider categories
                    if (!empty($jobData['provider_category_ids'])) {
                        $job->providerCategories()->sync($jobData['provider_category_ids']);
                        Log::info('JobsAfService: Synced job with provider categories', [
                            'job_id' => $job->id,
                            'provider_category_ids' => $jobData['provider_category_ids']
                        ]);
                    }

                    // CRITICAL: Sync canonical categories for notification system compatibility
                    Log::info('JobsAfService: About to sync categories', [
                        'job_id' => $job->id,
                        'position' => $job->position,
                        'categories_data' => $jobData['categories'] ?? 'null',
                        'categories_empty' => empty($jobData['categories']),
                        'categories_count' => is_array($jobData['categories']) ? count($jobData['categories']) : 'not_array'
                    ]);
                    
                    if (!empty($jobData['categories'])) {
                        $job->categories()->syncWithoutDetaching($jobData['categories']);
                        Log::info('JobsAfService: Categories synced successfully', [
                            'job_id' => $job->id,
                            'categories' => $jobData['categories'],
                            'trace_id' => $this->traceId,
                            'execution_id' => $this->executionId
                        ]);
                        
                        // Verify the sync worked by checking the database
                        $verifyCategories = $job->categories()->pluck('id')->toArray();
                        Log::info('JobsAfService: Categories verification', [
                            'job_id' => $job->id,
                            'categories_in_db' => $verifyCategories,
                            'sync_successful' => !empty($verifyCategories),
                            'trace_id' => $this->traceId,
                            'execution_id' => $this->executionId
                        ]);
                    } else {
                        Log::warning('JobsAfService: No categories to sync for job', [
                            'job_id' => $job->id,
                            'position' => $job->position
                        ]);
                    }

                    // Build notification-compatible payload with provider_category_ids (CRITICAL FIX)
                    $providerCategoryIdsForNotif = $jobData['provider_category_ids'] ?? [];
                    if (empty($providerCategoryIdsForNotif)) {
                        // Fallback 1: get from relation (if synced via pivot)
                        try {
                            $providerCategoryIdsForNotif = $job->providerCategories()->pluck('provider_job_categories.id')->toArray();
                        } catch (\Throwable $t) {
                            // ignore and try next fallback
                        }
                    }
                    if (empty($providerCategoryIdsForNotif)) {
                        // Fallback 2: derive from title/position with heuristic
                        $providerCategoryIdsForNotif = $this->getProviderCategoryIdsForJob($job->position);
                    }

                    $notificationPayload = [
                        'id' => $job->id,
                        'position' => $job->position,
                        'company_name' => $job->company_name,
                        'locations' => $job->locations,
                        'publish_date' => $job->publish_date,
                        'provider_category_ids' => $providerCategoryIdsForNotif,  // Required by JobNotificationService
                    ];
                    $newJobs[] = $notificationPayload;

                    $stats['created']++;
                    Log::info('JobsAfService: Created new job and synced categories', [
                        'position' => $job['position'],
                        'company' => $job['company']['name'] ?? 'Unknown',
                        'id' => $job->id,
                        'categories' => $jobData['categories']
                    ]);
                } else {
                    // Check if job content has changed
                    $hasChanges = $this->hasJobChanged($existingJob, $jobData);

                    if ($hasChanges) {
                        // Update existing job
                        $job = $this->jobRepository->createOrUpdate($jobData);
                        $jobWasUpdated = true;

                        // NEW APPROACH: Sync provider categories
                        if (!empty($jobData['provider_category_ids'])) {
                            $job->providerCategories()->sync($jobData['provider_category_ids']);
                            Log::info('JobsAfService: Updated job with provider categories', [
                                'job_id' => $job->id,
                                'provider_category_ids' => $jobData['provider_category_ids']
                            ]);
                        }

                        // DEPRECATED: Old canonical category sync (keeping temporarily for compatibility)
                        if (!empty($jobData['categories'])) {
                            $job->categories()->syncWithoutDetaching($jobData['categories']);
                        }

                        $stats['updated']++;
                        Log::info('JobsAfService: Updated existing job and synced categories', [
                            'position' => $job['position'],
                            'company' => $job['company']['name'] ?? 'Unknown',
                            'id' => $job->id,
                            'categories' => $jobData['categories']
                        ]);

                        // Build notification-compatible payload with provider_category_ids (CRITICAL FIX)
                        $providerCategoryIdsForNotif = $jobData['provider_category_ids'] ?? [];
                        if (empty($providerCategoryIdsForNotif)) {
                            try {
                                $providerCategoryIdsForNotif = $job->providerCategories()->pluck('provider_job_categories.id')->toArray();
                            } catch (\Throwable $t) {}
                        }
                        if (empty($providerCategoryIdsForNotif)) {
                            $providerCategoryIdsForNotif = $this->getProviderCategoryIdsForJob($job->position);
                        }

                        $notificationPayload = [
                            'id' => $job->id,
                            'position' => $job->position,
                            'company_name' => $job->company_name,
                            'locations' => $job->locations,
                            'publish_date' => $job->publish_date,
                            'provider_category_ids' => $providerCategoryIdsForNotif,  // Required by JobNotificationService
                        ];
                        $updatedJobs[] = $notificationPayload;
                    } else {
                        // If no changes, assign the existing job for potential event dispatch
                        $job = $existingJob;
                    }
                }

                DB::commit();

                // Service-driven only (no events) – aggregation handled by hub later
                $stats['processed']++;
                return true;
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('JobsAfService: Error processing individual job', [
                'position' => $job['position'] ?? 'Unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $stats['errors']++;
            return false;
        }
    }

         /**
      * Enhanced version of job processing that tracks category assignments for health dashboard
      *
      * @param array $job
      * @param array &$stats
      * @param array &$newJobs
      * @param array &$updatedJobs
      * @param array $providerCategoryIds Provider category IDs from schedule filter
      * @return bool
      */
     protected function processJobDataWithCategoryTracking(array $job, array &$stats, array &$newJobs, array &$updatedJobs, array $providerCategoryIds = []): bool
    {
        try {
            $position = trim((string) ($job['position'] ?? ''));

            // Guard empty or too-short titles
            if ($position === '' || mb_strlen($position) < 2) {
                $stats['empty_title_skipped'] = ($stats['empty_title_skipped'] ?? 0) + 1;
                return false;
            }

            // Skip/include non-English jobs depending on toggle
            $detector = app(\Modules\JobSeeker\Services\LanguageDetectionService::class);
            $isEnglish = $detector->isEnglishTitle($position, 'jobs.af');
            if (!$this->allowNonEnglish && !$isEnglish) {
                $stats['non_english_skipped'] = ($stats['non_english_skipped'] ?? 0) + 1;
                return false;
            } elseif ($this->allowNonEnglish && !$isEnglish) {
                $stats['non_english_included'] = ($stats['non_english_included'] ?? 0) + 1;
            }

            // Skip jobs not in Kabul
            if (!$this->isLocationInKabul($job['locations'] ?? '')) {
                $stats['location_filtered']++;
                return false;
            }

            // Format job data for database
            Log::info('JobsAfService: About to format job data', [
                'position' => $job['position'] ?? 'unknown',
                'provider_category_ids' => $providerCategoryIds,
                'provider_category_ids_count' => count($providerCategoryIds),
                'trace_id' => $this->traceId,
                'execution_id' => $this->executionId
            ]);
            $jobData = $this->formatJobData($job, $providerCategoryIds);
            Log::info('JobsAfService: Job data formatted', [
                'position' => $job['position'] ?? 'unknown',
                'canonical_categories' => $jobData['categories'] ?? 'none',
                'categories_count' => is_array($jobData['categories']) ? count($jobData['categories']) : 0,
                'trace_id' => $this->traceId,
                'execution_id' => $this->executionId
            ]);

            // Track category assignments for health dashboard (based on provider categories → canonical category names)
            if (!empty($jobData['provider_category_ids'])) {
                try {
                    $providerCategories = \Modules\JobSeeker\Entities\ProviderJobCategory::whereIn('id', $jobData['provider_category_ids'])
                        ->with('canonicalCategory')
                        ->get();
                    foreach ($providerCategories as $pc) {
                        $categoryName = $pc->canonicalCategory?->name;
                        if ($categoryName) {
                            if (!isset($stats['jobs_by_category'][$categoryName])) {
                                $stats['jobs_by_category'][$categoryName] = 0;
                            }
                            $stats['jobs_by_category'][$categoryName]++;
                        }
                    }
                } catch (\Throwable $t) {
                    // best-effort only
                }
            }

            // Check if job exists
            $existingJob = $this->jobRepository->findBySlug($job['slug']);

            DB::beginTransaction();
            try {
                $job = null;
                $jobWasCreated = false;
                $jobWasUpdated = false;

                if (!$existingJob) {
                    // Create new job
                    $job = $this->jobRepository->createOrUpdate($jobData);
                    $jobWasCreated = true;

                    // NEW APPROACH: Sync provider categories
                    if (!empty($jobData['provider_category_ids'])) {
                        $job->providerCategories()->sync($jobData['provider_category_ids']);
                        Log::info('JobsAfService: Synced job with provider categories', [
                            'job_id' => $job->id,
                            'provider_category_ids' => $jobData['provider_category_ids']
                        ]);
                    }

                    // CRITICAL: Also sync canonical categories for notification system compatibility
                    if (!empty($jobData['categories'])) {
                        $job->categories()->syncWithoutDetaching($jobData['categories']);
                        Log::info('JobsAfService: Synced job with canonical categories', [
                            'job_id' => $job->id,
                            'canonical_category_ids' => $jobData['categories']
                        ]);
                    }

                    // Build notification-compatible payload with provider_category_ids (CRITICAL FIX)
                    $notificationPayload = [
                        'id' => $job->id,
                        'position' => $job->position,
                        'company_name' => $job->company_name,
                        'locations' => $job->locations,
                        'publish_date' => $job->publish_date,
                        'provider_category_ids' => $jobData['provider_category_ids'],  // Required by JobNotificationService
                    ];
                    $newJobs[] = $notificationPayload;

                    $stats['created']++;
                    Log::info('JobsAfService: Created new job and synced categories', [
                        'position' => $job['position'],
                        'company' => $job['company']['name'] ?? 'Unknown',
                        'id' => $job->id,
                        'categories' => $jobData['categories']
                    ]);
                } else {
                    // Check if job content has changed
                    $hasChanges = $this->hasJobChanged($existingJob, $jobData);

                    if ($hasChanges) {
                        // Update existing job
                        $job = $this->jobRepository->createOrUpdate($jobData);
                        $jobWasUpdated = true;

                        // NEW APPROACH: Sync provider categories
                        if (!empty($jobData['provider_category_ids'])) {
                            $job->providerCategories()->sync($jobData['provider_category_ids']);
                            Log::info('JobsAfService: Updated job with provider categories', [
                                'job_id' => $job->id,
                                'provider_category_ids' => $jobData['provider_category_ids']
                            ]);
                        }

                        // CRITICAL: Also sync canonical categories for notification system compatibility
                        if (!empty($jobData['categories'])) {
                            $job->categories()->syncWithoutDetaching($jobData['categories']);
                            Log::info('JobsAfService: Updated job with canonical categories', [
                                'job_id' => $job->id,
                                'canonical_category_ids' => $jobData['categories']
                            ]);
                        }

                        $stats['updated']++;
                        Log::info('JobsAfService: Updated existing job and synced categories', [
                            'position' => $job['position'],
                            'company' => $job['company']['name'] ?? 'Unknown',
                            'id' => $job->id,
                            'categories' => $jobData['categories']
                        ]);

                        // Build notification-compatible payload with provider_category_ids (CRITICAL FIX)
                        $notificationPayload = [
                            'id' => $job->id,
                            'position' => $job->position,
                            'company_name' => $job->company_name,
                            'locations' => $job->locations,
                            'publish_date' => $job->publish_date,
                            'provider_category_ids' => $jobData['provider_category_ids'],  // Required by JobNotificationService
                        ];
                        $updatedJobs[] = $notificationPayload;
                    } else {
                        // If no changes, assign the existing job for potential event dispatch
                        $job = $existingJob;
                    }
                }

                DB::commit();

                // Service-driven only (no events) – aggregation handled by hub later
                $stats['processed']++;
                return true;
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('JobsAfService: Error processing individual job', [
                'position' => $job['position'] ?? 'Unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $stats['errors']++;

            // Track error types for health dashboard
            $errorType = $this->classifyError($e);
            if (!isset($stats['error_types'][$errorType])) {
                $stats['error_types'][$errorType] = 0;
            }
            $stats['error_types'][$errorType]++;

            return false;
        }
    }

    /**
     * Send job notification email
     * 
     * @param array $newJobs Array of new jobs
     * @param array $updatedJobs Array of updated jobs
     * @param array $missedJobs Array of jobs that were missed
     * @return bool Success status
     */
    protected function sendJobNotificationEmail(array $newJobs, array $updatedJobs, array $missedJobs): bool
    {
        Log::info('JobsAfService: Preparing to send job notifications via unified service', [
            'new_jobs' => count($newJobs),
            'updated_jobs' => count($updatedJobs),
            'missed_jobs' => count($missedJobs),
            'trace_id' => $this->traceId,
            'execution_id' => $this->executionId,
        ]);

        try {
            // Build schedule context with provider and traceability info
            $scheduleContext = [
                'provider' => 'jobs.af',
                'schedule_rule_id' => $this->scheduleRuleId ?? null,
                'execution_id' => $this->executionId,
                'trace_id' => $this->traceId,
            ];

            // Use unified notification service for canonical category-based notifications
            $notificationService = app(JobNotificationService::class);
            $result = $notificationService->notifyAggregatedJobs($newJobs, $updatedJobs, $missedJobs, $scheduleContext);
            
            Log::info('JobsAfService: Job notifications processed via unified JobNotificationService', [
                'result' => $result,
                'schedule_context' => $scheduleContext,
                'trace_id' => $this->traceId,
                'execution_id' => $this->executionId,
            ]);
            return $result;
        } catch (\Throwable $e) {
            Log::error('JobsAfService: Error sending notifications via unified service, no emails sent', [
                'error' => $e->getMessage(),
                'trace_id' => $this->traceId,
                'execution_id' => $this->executionId,
            ]);
            return false;
        }
    }

    /**
     * Fetch jobs from the provider and send notifications.
     * 
     * This method should:
     * 1. Fetch jobs from the external provider API
     * 2. Process and categorize jobs with provider_category_ids
     * 3. Call JobNotificationService for unified notification handling
     * 
     * @param array $scheduleContext Context including trace_id, execution_id
     * @return array Statistics about the sync process
     */
    public function fetchAndNotifyJobs(array $scheduleContext): array
    {
        // Extract parameters from schedule context for backward compatibility
        $categoryIds = $scheduleContext['category_ids'] ?? [];
        $scheduleRuleId = $scheduleContext['schedule_rule_id'] ?? null;
        
        Log::info('JobsAfService: START fetch and notify', [
            'trace_id' => $this->traceId,
            'execution_id' => $this->executionId,
        ]);

        // Initialize process logging with null safety
        if ($this->processLogger === null) {
            // Lazy initialize if setRunContext wasn't called
            $this->processLogger = new NotificationProcessLogger(
                $this->traceId ?? Str::uuid()->toString(), 
                $this->executionId
            );
        }
        
        $this->processLogger->logStep('process_start', 'success', 'start', 'jobs.af', 'Started Jobs.af sync process', [
            'category_ids' => $categoryIds,
            'schedule_rule_id' => $scheduleRuleId
        ]);

        try {
            // Store schedule rule ID for missed call tracking
            $this->scheduleRuleId = $scheduleRuleId;

            // Get filters for this schedule rule or use defaults
            $filters = $this->getFiltersForScheduleRule($scheduleRuleId, $categoryIds);
            // Resolve allow_non_english with precedence: rule > provider default > global
            $source = 'global';
            $resolved = (bool) (config('jobseeker.jobs_af_default_filters.allow_non_english') ?? false);
            // Provider default
            $providerSetting = \Modules\JobSeeker\Entities\ProviderSetting::forProvider('jobs.af');
            if ($providerSetting) {
                $resolved = (bool) $providerSetting->allow_non_english_default;
                $source = 'provider';
            }
            // Rule-level override if scheduleRuleId provided and key exists
            if ($scheduleRuleId !== null && is_array($filters) && array_key_exists('allow_non_english', $filters)) {
                $resolved = (bool) $filters['allow_non_english'];
                $source = 'rule';
            }
            $this->allowNonEnglish = $resolved;
            Log::info('JobsAfService: Resolved allow_non_english', [
                'provider' => 'jobs.af',
                'schedule_rule_id' => $scheduleRuleId,
                'source' => $source,
                'value' => $this->allowNonEnglish,
                'trace_id' => $this->traceId,
                'execution_id' => $this->executionId,
            ]);

            Log::info('JobsAfService: Fetching jobs from API', [
                'url' => $this->apiUrl,
                'filters' => $filters
            ]);

            try {
                // Track API response time for error reporting
                $startTime = microtime(true);

                // Fetch jobs from the API with rotating User-Agent
                $httpOptions = $this->getHttpClientOptions();
                $response = Http::withOptions([
                    'timeout' => 60,
                    'connect_timeout' => $httpOptions['connect_timeout'],
                    'verify' => $httpOptions['verify']
                ])
                ->withHeaders(array_merge($httpOptions['headers'], [
                    'Origin' => 'https://jobs.af',
                    'Referer' => 'https://jobs.af/'
                ]))
                ->retry(2, 30000) // Retry 2 times, waiting 30 seconds (30000 ms) between attempts
                ->get($this->apiUrl, [
                    'filter' => json_encode($filters)
                ]);

                // Process the response
                if (!$response->successful()) {
                    throw new \Illuminate\Http\Client\RequestException($response);
                }
            } catch (\Illuminate\Http\Client\RequestException | \Illuminate\Http\Client\ConnectionException $e) {
                // Build error context for both connection and request exceptions
                $errorContext = [
                    'status' => $e instanceof \Illuminate\Http\Client\RequestException ? $e->response->status() : 'Connection Failed',
                    'body' => $e instanceof \Illuminate\Http\Client\RequestException ? $e->response->body() : $e->getMessage(),
                    'url' => $this->apiUrl,
                    'filters' => $filters,
                    'timestamp' => Carbon::now()->toDateTimeString(),
                    'exception_type' => get_class($e),
                    'exception_message' => $e->getMessage()
                ];

                Log::error('JobsAfService: Failed to fetch jobs from jobs.af API', $errorContext);

                // Use the new SystemErrorNotificationService for comprehensive error reporting
                $this->errorNotificationService->reportApiFailure(
                    $this->apiUrl,
                    $errorContext['exception_message'],
                    microtime(true) - $startTime, // Response time
                    $errorContext,
                    $e
                );

                return [
                    'success' => false,
                    'error' => 'API fetch failed',
                    'created' => 0,
                    'updated' => 0,
                    'errors' => 1,
                    'processed' => 0,
                    'missed' => 0
                ]; // Stop further processing as API fetch failed
            }

            $jobs = $response->json();

            Log::info('JobsAfService: API Response received', [
                'status' => $response->status(),
                'total_jobs' => $jobs['total'] ?? 0,
                'current_page' => $jobs['currentPage'] ?? 1,
                'total_pages' => $jobs['pages'] ?? 1
            ]);

            if (empty($jobs['data'])) {
                Log::warning('JobsAfService: No jobs found in API response', [
                    'response' => $jobs
                ]);
                return [
                    'success' => true,
                    'message' => 'No jobs found in API response',
                    'created' => 0,
                    'updated' => 0,
                    'errors' => 0,
                    'processed' => 0,
                    'missed' => 0
                ];
            }

            Log::info('JobsAfService: Processing ' . count($jobs['data']) . ' jobs');

            // Initialize variables outside the transaction
            $newJobs = [];
            $updatedJobs = [];
            $missedJobs = [];
            $apiResponseTime = microtime(true) - LARAVEL_START;
            $stats = [
                'created' => 0,
                'updated' => 0,
                'errors' => 0,
                'non_english_skipped' => 0,
                'non_english_included' => 0,
                'empty_title_skipped' => 0,
                'location_filtered' => 0,
                'processed' => 0,
                'missed' => 0,
                'null_notified' => 0,
                // Health dashboard fields
                'jobs_fetched' => count($jobs['data'] ?? []),
                'jobs_by_category' => [],
                'error_types' => [],
                'api_response_time' => $apiResponseTime
            ];

                         // Get provider category IDs from schedule filter OR manual command
             $providerCategoryIds = [];
             if ($scheduleRuleId) {
                 // Scheduled execution: Get categories from schedule filter
                 try {
                     $rule = \Modules\JobSeeker\Entities\CommandScheduleRule::with('filter')->find($scheduleRuleId);
                     if ($rule && $rule->filter && !empty($rule->filter->categories)) {
                         $providerCategoryIds = $rule->filter->categories;
                         Log::info('JobsAfService: Retrieved provider category IDs from schedule filter', [
                             'schedule_rule_id' => $scheduleRuleId,
                             'provider_category_ids' => $providerCategoryIds,
                             'count' => count($providerCategoryIds),
                             'source' => 'schedule_filter'
                         ]);
                     }
                 } catch (\Exception $e) {
                     Log::warning('JobsAfService: Could not retrieve provider category IDs from schedule filter', [
                         'schedule_rule_id' => $scheduleRuleId,
                         'error' => $e->getMessage()
                     ]);
                 }
             } else {
                 // Manual execution: Use categories from schedule context (translated by command)
                 $providerCategoryIds = $categoryIds;
                 Log::info('JobsAfService: Using provider category IDs from manual command', [
                     'provider_category_ids' => $providerCategoryIds,
                     'count' => count($providerCategoryIds),
                     'source' => 'manual_command'
                 ]);
             }

             // Process jobs in a transaction
             DB::transaction(function () use ($jobs, &$newJobs, &$updatedJobs, &$missedJobs, &$stats, $providerCategoryIds) {
                 $now = Carbon::now();

                 foreach ($jobs['data'] as $job) {
                     $this->processJobDataWithCategoryTracking($job, $stats, $newJobs, $updatedJobs, $providerCategoryIds);
                 }

                // Use centralized MissedJobService to find missed jobs
                $missedJobsCollection = $this->missedJobService->findMissedJobs('jobs.af', 48);

                Log::info("JobsAfService: Found " . $missedJobsCollection->count() . " potential missed jobs from MissedJobService");

                if ($missedJobsCollection->isNotEmpty()) {
                    foreach ($missedJobsCollection as $job) {
                        // Include based on rule: English-only unless allowNonEnglish is enabled
                        $detector = app(\Modules\JobSeeker\Services\LanguageDetectionService::class);
                        $passesLanguage = $this->allowNonEnglish ? true : $detector->isEnglishTitle((string) $job->position);
                        if ($passesLanguage && $this->isLocationInKabul($job->locations)) {
                            // Check if job is already in newJobs (to avoid duplicate notifications)
                            $isDuplicate = false;
                            foreach ($newJobs as $newJob) {
                                if (isset($newJob['id']) && $newJob['id'] == $job->id) {
                                    $isDuplicate = true;
                                    break;
                                }
                            }

                            if (!$isDuplicate) {
                                // Build notification-compatible payload with provider_category_ids (CRITICAL FIX)
                                $providerCategoryIds = DB::table('job_provider_category_pivot')
                                    ->where('job_id', $job->id)
                                    ->pluck('provider_category_id')
                                    ->toArray();

                                $missedJobPayload = [
                                    'id' => $job->id,
                                    'position' => $job->position,
                                    'company_name' => $job->company_name,
                                    'locations' => $job->locations,
                                    'publish_date' => $job->publish_date,
                                    'provider_category_ids' => $providerCategoryIds,  // Required by JobNotificationService
                                ];
                                $missedJobs[] = $missedJobPayload;
                                $stats['missed']++;
                                Log::info("JobsAfService: Adding recent job to missed notifications", [
                                    'id' => $job->id,
                                    'position' => $job->position
                                ]);
                            } else {
                                Log::info("JobsAfService: Skipping duplicate job in missed notifications", [
                                    'id' => $job->id,
                                    'position' => $job->position
                                ]);
                            }
                        }
                    }
                    Log::info("JobsAfService: Added {$stats['missed']} jobs to missed notifications queue");
                }

                Log::info('JobsAfService: Job processing completed', [
                    'total_processed' => $stats['processed'],
                    'total_created' => $stats['created'],
                    'total_updated' => $stats['updated'],
                    'total_errors' => $stats['errors'],
                    'non_english_skipped' => $stats['non_english_skipped'],
                    'location_filtered' => $stats['location_filtered'],
                    'new_jobs' => count($newJobs),
                    'updated_jobs' => count($updatedJobs),
                    'missed_jobs' => count($missedJobs)
                ]);

                // Always call notification hub to enable missed call tracking even when no jobs
                $notificationsSent = $this->sendJobNotificationEmail($newJobs, $updatedJobs, $missedJobs);

                // Trigger missed call alert if no notifications were sent
                $allJobs = array_merge($newJobs, $updatedJobs, $missedJobs);
                if (count($allJobs) > 0 && !$notificationsSent) {
                    $context = [
                        'command' => 'jobseeker:sync-jobs-af',
                        'provider' => 'Jobs.af',
                        'schedule_rule_id' => $this->scheduleRuleId ?? null,
                        'total_jobs' => count($allJobs),
                        'new_jobs' => count($newJobs),
                        'updated_jobs' => count($updatedJobs),
                        'missed_jobs' => count($missedJobs),
                        'reason' => 'Jobs found but no notifications sent'
                    ];
                    $this->missedJobService->triggerMissedCallAlert($context);
                }

                // Note: Notification tracking is now handled within sendJobNotificationEmail()
                // via JobNotificationSentJob records, so no additional marking is needed
                Log::info('JobsAfService: Job notifications processed', [
                    'new_jobs_count' => count($newJobs),
                    'updated_jobs_count' => count($updatedJobs),
                    'missed_jobs_count' => count($missedJobs)
                ]);
            });

            Log::info('JobsAfService: Job fetch and notification process completed successfully');

            // Return the statistics from the transaction
            return [
                'success' => true,
                'created' => $stats['created'] ?? 0,
                'updated' => $stats['updated'] ?? 0,
                'errors' => $stats['errors'] ?? 0,
                'skipped_no_category_map' => $stats['skipped_no_category_map'] ?? 0,
                'categories_processed' => $stats['categories_processed'] ?? 0,
                'non_english_skipped' => $stats['non_english_skipped'] ?? 0,
                'non_english_included' => $stats['non_english_included'] ?? 0,
                'empty_title_skipped' => $stats['empty_title_skipped'] ?? 0,
                'location_filtered' => $stats['location_filtered'] ?? 0,
                // Health dashboard fields
                'jobs_fetched' => $stats['jobs_fetched'] ?? 0,
                'jobs_by_category' => $stats['jobs_by_category'] ?? [],
                'error_types' => $stats['error_types'] ?? [],
                'api_response_time' => $stats['api_response_time'] ?? 0,
                'category_response_time' => $stats['category_response_time'] ?? 0,
            ];

        } catch (\Exception $e) {
            Log::error('JobsAfService: Error in fetchAndNotifyJobs', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'created' => 0,
                'updated' => 0,
                'errors' => 1,
                'processed' => 0,
                'missed' => 0,
                // Health dashboard fields for error case
                'jobs_fetched' => 0,
                'jobs_by_category' => [],
                'error_types' => ['UNKNOWN' => 1],
                'api_response_time' => 0
            ];
        }
    }

    /**
     * Unified provider sync + aggregation entrypoint.
     * Accepts provider category identifiers and optional schedule rule id.
     */
    public function syncAndAggregate(?array $providerCategoryIdentifiers = null, ?int $scheduleRuleId = null): array
    {
        $ids = $providerCategoryIdentifiers ?? [];
        return $this->fetchAndNotifyJobs($ids, $scheduleRuleId);
    }

    /**
     * Fetch and extract detailed job descriptions for each job
     * This method visits the job detail page for each job and extracts specific sections
     * 
     * @param array|null $jobIds Array of job IDs to fetch descriptions for (null = all jobs missing descriptions)
     * @param int $limit Maximum number of jobs to process
     * @return array Statistics about the process
     */
    public function fetchJobDescriptions(?array $jobIds = null, int $limit = 50): array
    {
        Log::info('JobsAfService: Starting to fetch detailed job descriptions', [
            'job_ids' => $jobIds ? count($jobIds) : 'all jobs missing descriptions',
            'limit' => $limit
        ]);

        $stats = [
            'total' => 0,
            'processed' => 0,
            'success' => 0,
            'failed' => 0,
            'skipped' => 0,
            'existing' => 0,
            'network_errors' => 0
        ];

        try {
            // Query for jobs that need description updates (case-insensitive source match)
            $query = Job::whereRaw('LOWER(source) = ?', ['jobs.af']);

            if ($jobIds) {
                $query->whereIn('id', $jobIds);
            } else {
                // Find jobs missing descriptions
                $query->where(function($q) {
                    $q->whereNull('about_company')
                      ->orWhereNull('job_summary')
                      ->orWhereNull('duties_responsibilities')
                      ->orWhereNull('job_requirements')
                      ->orWhereNull('submission_guideline');
                });
            }

            $jobs = $query->limit($limit)->get();
            $stats['total'] = $jobs->count();

            Log::info('JobsAfService: Found ' . $stats['total'] . ' jobs to process for detailed descriptions', [
                'delay_config' => $this->delayConfig
            ]);

            $isFirstRequest = true;
            foreach ($jobs as $job) {
                try {
                    if (!$job->slug) {
                        Log::warning('JobsAfService: Job has no slug, skipping', ['job_id' => $job->id]);
                        $stats['skipped']++;
                        continue;
                    }

                    $url = 'https://jobs.af/jobs/' . $job->slug;
                    Log::info('JobsAfService: Fetching job details from: ' . $url, [
                        'job_id' => $job->id,
                        'is_first_request' => $isFirstRequest
                    ]);

                    // Apply random delay between requests to mimic human behavior
                    $this->applyRandomDelay($isFirstRequest);

                    // Attempt to fetch the job details page with rotating User-Agent
                    try {
                        $httpOptions = $this->getHttpClientOptions();
                        $client = new \GuzzleHttp\Client([
                            'timeout' => $httpOptions['timeout'],
                            'connect_timeout' => $httpOptions['connect_timeout'],
                            'verify' => $httpOptions['verify'],
                            'headers' => array_merge($httpOptions['headers'], [
                                'Referer' => 'https://jobs.af/',
                                'Origin' => 'https://jobs.af'
                            ])
                        ]);
                        $response = $client->get($url);
                        $html = (string) $response->getBody();
                    } catch (\Exception $e) {
                        Log::error('JobsAfService: Network error fetching job details', [
                            'job_id' => $job->id,
                            'url' => $url,
                            'error' => $e->getMessage()
                        ]);
                        $stats['network_errors']++;
                        continue;
                    }

                    // Extract sections from the HTML
                    $sections = $this->extractJobDescriptionSections($html);

                    // Check if we got any useful data
                    $hasNewData = false;
                    foreach ($sections as $key => $value) {
                        if (!empty($value)) {
                            $hasNewData = true;
                            break;
                        }
                    }

                    if ($hasNewData) {
                        // Update the job with new description data
                        $job->update([
                            'about_company' => $sections['about_company'],
                            'job_summary' => $sections['job_summary'],
                            'duties_responsibilities' => $sections['duties_responsibilities'],
                            'job_requirements' => $sections['job_requirements'],
                            'experience' => $sections['experience'],
                            'submission_guideline' => $sections['submission_guideline']
                        ]);

                        Log::info('JobsAfService: Updated job with new description data', [
                            'job_id' => $job->id,
                            'sections_updated' => array_filter(array_keys($sections), function($key) use ($sections) {
                                return !empty($sections[$key]);
                            })
                        ]);

                        $stats['success']++;
                    } else {
                        Log::info('JobsAfService: No new description data to update', ['job_id' => $job->id]);
                        $stats['skipped']++;
                    }

                    $stats['processed']++;

                    // Mark that we've processed the first request
                    $isFirstRequest = false;

                } catch (\Exception $e) {
                    Log::error('JobsAfService: Error processing job', [
                        'job_id' => $job->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    $stats['failed']++;

                    // Mark that we've processed the first request even if it failed
                    $isFirstRequest = false;
                }
            }

            Log::info('JobsAfService: Completed fetching job descriptions', $stats);
            return $stats;

        } catch (\Exception $e) {
            Log::error('JobsAfService: Error in fetchJobDescriptions', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Extract job description sections from HTML content
     * 
     * @param string $html HTML content from the job details page
     * @return array Extracted sections
     */
    protected function extractJobDescriptionSections(string $html): array
    {
        $results = [
            'about_company' => null,
            'job_summary' => null,
            'duties_responsibilities' => null,
            'job_requirements' => null,
            'experience' => null,
            'submission_guideline' => null
        ];

        try {
            // Extract "About the Company" section
            if (preg_match('/<h3[^>]*>(?:\s*About\s*(?:the\s*)?Company|\s*About\s*([^<]+))<\/h3>.*?(?:<div[^>]*class="[^"]*ql-wrapper[^"]*"[^>]*>|<p[^>]*>)(.*?)(?:<\/div>|<\/p>)/si', $html, $matches)) {
                $results['about_company'] = trim(strip_tags($matches[2]));
            }

            // Extract "Job Summary" section
            if (preg_match('/<h3[^>]*>(?:\s*Job\s*Summary|\s*About\s*the\s*Role|\s*Position\s*Summary)<\/h3>.*?(?:<div[^>]*class="[^"]*ql-wrapper[^"]*"[^>]*>|<p[^>]*>)(.*?)(?:<\/div>|<\/p>)/si', $html, $matches)) {
                $results['job_summary'] = trim(strip_tags($matches[1]));
            }

            // Extract "Duties & Responsibilities" section
            if (preg_match('/<h3[^>]*>(?:\s*Duties\s*(?:&amp;|and)\s*Responsibilities|\s*Key\s*Responsibilities|\s*Main\s*Responsibilities)<\/h3>.*?(?:<div[^>]*class="[^"]*ql-wrapper[^"]*"[^>]*>|<p[^>]*>)(.*?)(?:<\/div>|<\/p>)/si', $html, $matches)) {
                $results['duties_responsibilities'] = trim(strip_tags($matches[1]));
            }

            // Extract "Job Requirements" section
            if (preg_match('/<h3[^>]*>(?:\s*Job\s*Requirements|\s*Requirements|\s*Qualifications|\s*Required\s*Qualifications)<\/h3>.*?(?:<div[^>]*class="[^"]*ql-wrapper[^"]*"[^>]*>|<p[^>]*>)(.*?)(?:<\/div>|<\/p>)/si', $html, $matches)) {
                $results['job_requirements'] = trim(strip_tags($matches[1]));
            }

            // Extract "Experience" section or experience from job requirements
            if (preg_match('/<h3[^>]*>(?:\s*Experience|\s*Work\s*Experience|\s*Required\s*Experience)<\/h3>.*?(?:<div[^>]*class="[^"]*ql-wrapper[^"]*"[^>]*>|<p[^>]*>)(.*?)(?:<\/div>|<\/p>)/si', $html, $matches)) {
                $results['experience'] = trim(strip_tags($matches[1]));
            } elseif ($results['job_requirements']) {
                // Try to extract experience from job requirements text
                if (preg_match('/(?:minimum\s*of\s*)?(\d+(?:\+|\s*to\s*\d+|\s*-\s*\d+)?\s*years?\s*(?:of\s*)?(?:experience|work\s*experience))/i', $results['job_requirements'], $matches)) {
                    $results['experience'] = trim($matches[1]);
                } elseif (preg_match('/(entry\s*level|fresh\s*graduate|no\s*experience|junior|senior|mid\s*level)/i', $results['job_requirements'], $matches)) {
                    $results['experience'] = trim($matches[1]);
                }
            }

            // Extract "Submission Guideline" section
            if (preg_match('/<h3[^>]*>(?:\s*Submission\s*Guideline|\s*How\s*to\s*Apply|\s*Application\s*Process)<\/h3>.*?(?:<div[^>]*class="[^"]*ql-wrapper[^"]*"[^>]*>|<p[^>]*>)(.*?)(?:<\/div>|<\/p>)/si', $html, $matches)) {
                $results['submission_guideline'] = trim(strip_tags($matches[1]));
            }

            // Clean up extracted content
            foreach ($results as $key => $value) {
                if ($value !== null) {
                    // Remove extra whitespace and normalize line endings
                    $value = preg_replace('/\s+/', ' ', $value);
                    $value = str_replace(["\r", "\n"], ' ', $value);
                    $value = trim($value);

                    // Convert HTML entities
                    $value = html_entity_decode($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');

                    // Remove any remaining HTML tags
                    $value = strip_tags($value);

                    $results[$key] = $value;
                }
            }

            // Log extraction results for debugging
            Log::debug('JobsAfService: Extracted job description sections', [
                'about_company_length' => $results['about_company'] ? strlen($results['about_company']) : 0,
                'job_summary_length' => $results['job_summary'] ? strlen($results['job_summary']) : 0,
                'duties_responsibilities_length' => $results['duties_responsibilities'] ? strlen($results['duties_responsibilities']) : 0,
                'job_requirements_length' => $results['job_requirements'] ? strlen($results['job_requirements']) : 0,
                'experience_length' => $results['experience'] ? strlen($results['experience']) : 0,
                'submission_guideline_length' => $results['submission_guideline'] ? strlen($results['submission_guideline']) : 0
            ]);

            return $results;

        } catch (\Exception $e) {
            Log::error('JobsAfService: Error parsing job description HTML', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $results;
        }
    }

    /**
     * Get category name by ID from cache or database
     * 
     * @param int $categoryId
     * @return string|null
     */
    protected function getCategoryNameById(int $categoryId): ?string
    {
        try {
            // Use the existing canonical categories cache
            if (self::$canonicalCategoriesCache === null) {
                $this->loadCanonicalCategoriesCache();
            }

            foreach (self::$canonicalCategoriesCache as $category) {
                if ($category->id === $categoryId) {
                    return $category->name;
                }
            }

            // Fallback: Direct database query if not found in cache
            $category = JobCategory::find($categoryId);
            return $category ? $category->name : null;

        } catch (\Exception $e) {
            Log::warning('JobsAfService: Error getting category name by ID', [
                'category_id' => $categoryId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Classify error type for health dashboard tracking
     * 
     * @param \Exception $exception
     * @return string
     */
    protected function classifyError(\Exception $exception): string
    {
        $message = strtolower($exception->getMessage());
        $exceptionClass = get_class($exception);

        if (str_contains($message, 'rate limit') || str_contains($message, 'too many requests') || str_contains($message, '429')) return 'RATE_LIMIT';
        if (str_contains($message, 'timeout') || str_contains($message, 'time out') || str_contains($exceptionClass, 'Timeout')) return 'TIMEOUT';
        if (str_contains($message, 'connection') || str_contains($message, 'network') || str_contains($message, 'curl') || str_contains($exceptionClass, 'Connection')) return 'NETWORK';
        if (str_contains($message, 'api') || str_contains($message, 'json') || str_contains($message, 'response') || str_contains($message, 'http') || str_contains($exceptionClass, 'Request') || str_contains($exceptionClass, 'Response')) return 'API';
        if (str_contains($message, 'database') || str_contains($message, 'sql') || str_contains($message, 'duplicate') || str_contains($message, 'constraint') || str_contains($exceptionClass, 'Query') || str_contains($exceptionClass, 'Database')) return 'DATA';
        return 'UNKNOWN';
    }

    /**
     * Send admin alert for critical job sync failures
     * 
     * @param string $errorType
     * @param array $errorDetails
     * @param array $stats
     * @return void
     */
    protected function sendAdminAlert(string $errorType, array $errorDetails, array $stats): void
    {
        try {
            $adminEmail = config('jobseeker.admin_notification_email');
            if (!$adminEmail) {
                Log::warning('JobsAfService: No admin email configured for alerts');
                return;
            }

            $subject = "ALERT: Jobs.af Sync {$errorType} - " . now()->format('Y-m-d H:i');

            $viewData = [
                'errorType' => $errorType,
                'errorDetails' => $errorDetails,
                'stats' => $stats,
                'timestamp' => now()->toDateTimeString(),
                'service' => 'Jobs.af'
            ];

            $fromDetails = [
                'email' => config('mail.from.address', '<EMAIL>'),
                'name' => config('app.name', 'ITQAN') . ' Job Sync Monitor'
            ];

            $this->emailService->send(
                $adminEmail,
                $subject,
                '',
                $viewData,
                'jobseeker::emails.admin.job_sync_alert',
                [],
                [],
                $fromDetails['email'],
                $fromDetails['name']
            );

            Log::info('JobsAfService: Admin alert sent successfully', [
                'recipient' => $adminEmail,
                'error_type' => $errorType
            ]);

        } catch (\Exception $e) {
            Log::error('JobsAfService: Failed to send admin alert', [
                'error' => $e->getMessage(),
                'original_error_type' => $errorType
            ]);
        }
    }

    /**
     * Format execution stats for command schedule health tracking
     * 
     * @param array $stats
     * @return array
     */
    public function formatExecutionStats(array $stats): array
    {
        try {
            return [
                'jobs_fetched' => (int) ($stats['jobs_fetched'] ?? $stats['processed'] ?? 0),
                'jobs_by_category' => $stats['jobs_by_category'] ?? [],
                'error_type' => $this->determineMainErrorType($stats['error_types'] ?? []),
                'error_details' => [
                    'total_errors' => (int) ($stats['errors'] ?? 0),
                    'error_breakdown' => $stats['error_types'] ?? [],
                    'api_response_time' => (float) ($stats['api_response_time'] ?? 0),
                    'category_response_time' => (float) ($stats['category_response_time'] ?? 0),
                    'categories_processed' => (int) ($stats['categories_processed'] ?? 0),
                    'skipped_no_category_map' => (int) ($stats['skipped_no_category_map'] ?? 0),
                    'non_english_skipped' => (int) ($stats['non_english_skipped'] ?? 0),
                    'non_english_included' => (int) ($stats['non_english_included'] ?? 0),
                    'empty_title_skipped' => (int) ($stats['empty_title_skipped'] ?? 0),
                    'location_filtered' => (int) ($stats['location_filtered'] ?? 0)
                ]
            ];
        } catch (\Exception $e) {
            Log::error('JobsAfService: Error formatting execution stats', [
                'error' => $e->getMessage(),
                'stats' => $stats
            ]);

            return [
                'jobs_fetched' => 0,
                'jobs_by_category' => [],
                'error_type' => 'UNKNOWN',
                'error_details' => ['total_errors' => 1, 'format_error' => $e->getMessage()]
            ];
        }
    }

    /**
     * Determine the main error type from error statistics
     * 
     * @param array $errorTypes
     * @return string
     */
    protected function determineMainErrorType(array $errorTypes): string
    {
        if (empty($errorTypes)) {
            return 'NONE';
        }

        // Return the error type with the highest count
        arsort($errorTypes);
        return array_key_first($errorTypes);
    }
} 

<?php

namespace Modules\Finance\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Admission;
use App\Student;
use App\StudentPayment;

class StudentPaymentsController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Request $request)
    {
        $keyword = $request->get('search');
        $perPage = 25;

        if (!empty($keyword)) {
            $student_payments = StudentPayment::all()
                /* where('Name', 'LIKE', "%$keyword%")
				->orWhere('Location', 'LIKE', "%$keyword%")
				->orWhere('Status', 'LIKE', "%$keyword%") */
				->paginate($perPage);
        } else {
            $student_payments = StudentPayment::paginate($perPage);
        }

        return view('finance::student_payments.index', compact('student_payments'));
    }


    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        $students = Student::all();

        return view('finance::student_payments.create', compact('students'));
    }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function store(Request $request)
    {

        $this->validate($request, [
            'student_id' => 'required|numeric',
            'payment_category' => 'required',
            'approved' => 'required',
            'method_of_payment' => 'required',
            'amount' => 'required|numeric',
            'payment_proof' => 'mimes:jpeg,jpg,png,pdf,zip | max:5000'

        ]);
        $student = Student::findOrFail($request->student_id);

        $payment = new StudentPayment;

        $payment->student_id = $request->student_id;
        $payment->admission_id = $student->current_admission->id ?? 0;
        $payment->organization_id= config('organization_id');
        $payment->creator_role = 'employee';
        $payment->creator_id = auth()->user()->id;
        $payment->payment_category = $request->payment_category;
        $payment->amount = $request->amount;
        $payment->transaction_id = $request->transaction_id;
        $payment->method_of_payment = $request->method_of_payment;
        $payment->approved = $request->approved;
        $payment->notes = $request->notes;

        $payment->verified_by = auth()->user()->id;
        

        if($request->hasFile('payment_proof')){

            $file_name = \Illuminate\Support\Str::random(9).'.'. $request->file('payment_proof')->getClientOriginalExtension();
            
            $path = 'userfiles/'.userfolder($admission->student->id).'/'.\Illuminate\Support\Str::random(9).'_payment_proof/';

            $request->file('payment_proof')->move(
                base_path() .'/public/'. $path, $file_name
            );
            $payment->payment_proof = $path.$file_name;          
        
        }
        if($payment->payment_category == "program_fees"){
            $admission = Admission::findOrFail($payment->admission_id);

            if($request->approved == 1){
                $admission->status = "preparing_for_orientation";
            }else{
                $admission->status = "payment_proof_not_valid";            
            }

            $admission->save();            
        }


        $payment->save();

        flash('Student Payment Updated');

        return redirect()->route('students.show',$payment->student_id);
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show()
    {
        return view('finance::student_payments.show');
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        $payment = StudentPayment::findOrFail($id);

        return view('finance::student_payments.edit' , compact('payment'));
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update($id, Request $request)
    {
        $this->validate($request, [
            'approved' => 'required',
            'amount' => 'required|numeric',
            'payment_proof' => 'mimes:jpeg,jpg,png,pdf,zip | max:5000'

        ]);
        $payment = StudentPayment::findOrFail($id);



        $payment->amount = $request->amount;
        $payment->transaction_id = $request->transaction_id;
        $payment->method_of_payment = $request->method_of_payment;
        $payment->approved = $request->approved;
        $payment->notes = $request->notes;

        $payment->verified_by = auth()->user()->id;
        

        if($request->hasFile('payment_proof')){

            $file_name = \Illuminate\Support\Str::random(9).'.'. $request->file('payment_proof')->getClientOriginalExtension();
            
            $path = 'userfiles/'.userfolder($admission->student->id).'/'.\Illuminate\Support\Str::random(9).'_payment_proof/';

            $request->file('payment_proof')->move(
                base_path() .'/public/'. $path, $file_name
            );
            $payment->payment_proof = $path.$file_name;          
        
        }
        if($payment->payment_category == "program_fees"){
            $admission = Admission::findOrFail($payment->admission_id);

            if($request->approved == 1){
                $admission->status = "preparing_for_orientation";
            }else{
                $admission->status = "payment_proof_not_valid";            
            }

            $admission->save();            
        }


        $payment->save();

        flash('Student Payment Updated');

        return redirect()->route('students.show',$payment->student_id);

    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }

    public function data()
    {
        $payments = StudentPayment::all();

        return DataTables::of($payments)->make();
    }
}

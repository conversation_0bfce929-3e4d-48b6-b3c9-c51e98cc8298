<?php

if(config('is_platform')){

Route::group(['domain' => config('platform_domain')], function() {
		
	Route::get('home', function() {
		return redirect(config('app.locale').'/home');
	});
	
});

Route::group(['middleware' => ['web'],'prefix' => 'superior', 'namespace' => 'App\Http\Controllers'], function () {
	Route::get('/login', 'SuperiorAuth\LoginController@showLoginForm');
	Route::post('/login', 'SuperiorAuth\LoginController@login');
	Route::post('/logout', 'SuperiorAuth\LoginController@logout');
  
	Route::get('/register', 'SuperiorAuth\RegisterController@showRegistrationForm');
	Route::post('/register', 'SuperiorAuth\RegisterController@register');
  
	Route::post('/password/email', 'SuperiorAuth\ForgotPasswordController@sendResetLinkEmail');
	Route::post('/password/reset', 'SuperiorAuth\ResetPasswordController@reset');
	Route::get('/password/reset', 'SuperiorAuth\ForgotPasswordController@showLinkRequestForm');
	Route::get('/password/reset/{token}', 'SuperiorAuth\ResetPasswordController@showResetForm');
  });
  
Route::group(['middleware' => ['web'], 'prefix' =>config('app.locale') ,  'domain' => config('platform_domain'), 'namespace' => 'Modules\Platform\Http\Controllers'], function()
{

	Route::get('/home', function () {
	    return view('organization.home');
	})->name('home');
	
    Route::get('/', 'PlatformController@index');
//	Route::get('/login', 'OrganizationAuth\LoginController@showLoginForm');
//	Route::post('/login', 'OrganizationAuth\LoginController@login');
//	Route::post('/logout', 'OrganizationAuth\LoginController@logout');

	Route::get('/register', 'OrganizationAuth\RegisterController@showRegistrationForm');
	Route::post('/register', 'OrganizationAuth\RegisterController@register');

	Route::post('/password/email', 'OrganizationAuth\ForgotPasswordController@sendResetLinkEmail');
	Route::post('/password/reset', 'OrganizationAuth\ResetPasswordController@reset');
	Route::get('/password/reset', 'OrganizationAuth\ForgotPasswordController@showLinkRequestForm');
	Route::get('/password/reset/{token}', 'OrganizationAuth\ResetPasswordController@showResetForm');

	Route::group(['middleware' => ['organization', 'auth:organization']], function()
	{    

	    Route::get('/home', 'PlatformController@home');
	    Route::post('/home', 'OrganizationController@update');


	});
	
});
}

<?php

namespace Modules\General\Http\Controllers;



use App\MissedClockOut;
use Carbon\Carbon;
use Illuminate\Http\Request;

use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\ParameterBag;


class IndividualEmployeeMissedClockoutsListController extends Controller
{

    public function __invoke($empId,$yearMonth = null)
    {


//        dd($employeeAttendanceDate);




        $year = Carbon::now()->year;
        $month = Carbon::now()->month;

        if(is_null($yearMonth))
        {

            $explodedYearMonth = explode('-', $yearMonth);
            $year = $explodedYearMonth[0];
            $month = $explodedYearMonth[1];


        }




            $allMissedClockOutAttendance = MissedClockOut::withTrashed()->with('attendance')->with('employee')->where('employee_id',$empId)->whereYear("clock", $year)->whereMonth('clock', $month);




            if ((auth()->user()->hasRole(['education-manager_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {


                $allMissedClockOutAttendance->whereHas("employee.roles", function ($q) {
                     $q->where('name', 'supervisor_' . config('organization_id') . '_');
                });
            }



        $yesterday = date("Y-m-d", strtotime( '-1 days' ) );
        $countYesterday = MissedClockOut::withTrashed()->with('attendance')->whereDate('clock', $yesterday )->get()->groupBy(['employee_id',date('clock')])->count();
            $paginateCount = 40;


        if(request()->filled('last-year-records'))
        {
            $missedClockoutList = $allMissedClockOutAttendance->orderByRaw('date(clock) desc, employee_id')->cursor();
            return view('general::individual_employee_missed_clockouts_list',compact('missedClockoutList','monthYear','employeeAttendanceDate'));



        }
        else{



            $lastYear = (Carbon::now()->year)-1;

            $missedClockoutList = $allMissedClockOutAttendance->orderByRaw('date(clock) desc, employee_id')->whereYear('clock','>',$lastYear)->cursor();

            return view('general::individual_employee_missed_clockouts_list',compact('missedClockoutList','monthYear','employeeAttendanceDate'));


        }













            return \Yajra\DataTables\DataTables::of($allMissedClockOutAttendance->select())
                ->with('count', function() use ($row) {


                    $yesterday = date("Y-m-d", strtotime( '-1 days' ) );
                    $countYesterday = MissedClockOut::withTrashed()->with('attendance')->whereDate('clock', $yesterday )->get()->groupBy(['employee_id',date('clock')])->count();
                    return $countYesterday;

                })
                ->addColumn('full_name', function ($row) {


                    return '<span  data-missedClockOut_id="' . $row->id . '" data-employee_id="' . $row->employee_id . '">' . $row->employee->full_name . '</span>';
                })
                ->addColumn('clock', function ($row) {


                    return $row->clock->toDateString();


                })
                ->addColumn('in', function ($row) {
                    $html = '';
                    if ($row->type == 'in') {
                        $html .= '<span class="btn-success">' . $row->clock->format('h:iA') . '</span>';

                        return $html;
                    }


                })
                ->addColumn('out', function ($row) {


                    $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                        ->whereDate('clock', $row->clock->toDateString())->where('type', 'out');

                    if (optional($out)->exists()) {
                        $out = $out->first();
                        $html = ' <span class="btn-warning">' . optional(optional($out)->clock)->format('h:iA') . '</span>';

                        return $html;
                    } else {

                        // show the modal trigger button
                        return ' <button class="ui button item" data-value="matt" data-toggle="modal"
                                             id="addOutAttendanceRecordBtnHound"
                                             data-date="' . $row->clock->toDateString() . '"
                                             data-employee_id="' . $row->employee_id . '"
                                             data-in="' . $row->clock . '"
                                             data-target="#addOutAttendanceRecordHound">
                                                 Add Out record
                                        </button>';

                    }


                })
                ->addColumn('numberofHours', function ($row) {

                    $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                        ->whereDate('clock', $row->clock->toDateString())->where('type', 'out')->first();
                    if ($row->type == 'in') {

                        $in = is_null($row->clock) ? \Carbon\Carbon::now() : Carbon::parse($row->clock);


                    } else {

                        $in = \Carbon\Carbon::now();

                    }
                    if (optional($out)->exists()) {
                        $hours = $in->diffInHours($out->clock);
                        $seconds = $in->diffInMinutes($out->clock);
                        return $hours . ':' . $seconds % 60;
                    }

                })
                ->addColumn('clockoutReason', function ($row) {
                    $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                        ->whereDate('clock', $row->clock->toDateString())->where('type', 'out')->first();
                    return optional($out)->clockout_reason;
                })
                ->addColumn('action', function ($row) {

                    $clockDate = $row->clock->toDateString();


                    $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                        ->whereDate('clock', $clockDate)->where('type', 'out');

                    if ($out->exists()) {

                        $in = \App\MissedClockOut::onlyTrashed()->where('employee_id', $row->employee_id)
                            ->whereDate('clock', $clockDate)->where('type', 'in');

                        $in = $in->first();



                        $out = $out->first();



                        if($row->type == 'in' ){
                            $in = $row->id;

                        }
                        if($row->type == 'out' ){
                            $out = $row->id;

                        }
                        return '<button 
                    
                    class="btn btn-success btn-xs deleteModalTriggerBtn "
                     id="deleteModalTriggerBtn "
                 
                     data-toggle="modal"
                   data-missed_clockin_id="' .$in. '"
//                     data-missed_clockin_id="' . optional($in)->id . '"
//                     data-missed_clockout_id="' . optional($out)->id . '"
                     data-missed_clockout_id="' . $out . '"
                     data-attendance_in_id="' . optional($row)->attendance_id . '"
                     data-employee_id="' . optional($row)->employee_id . '"
                     data-clock="' . optional($out)->clock . '"
                     data-clockout_reason="' . optional($out)->clockout_reason . '"
                      data-target="#validateMissedClockoutEntry"><strong>Validate</strong></button>
                     <button 
                    
                    class="btn btn-default btn-xs deleteModalTriggerBtn "
                     id="deleteModalTriggerBtn "
                    
                     data-toggle="modal"
                     data-missed_clockin_id="' . optional($in)->id . '"
                     data-missed_clockout_id="' . optional($out)->id . '"
                     data-attendance_in_id="' . optional($row)->attendance_id . '"
                     data-employee_id="' . optional($row)->employee_id . '"
                     data-clock="' . optional($out)->clock . '"
                     data-clockout_reason="' . optional($out)->clockout_reason . '"
                      data-target="#revertMissedClockoutEntry"><strong>Revert</strong></button>';
                    }


                })->rawColumns(['action', 'in', 'out','full_name'])
                ->toJson();




    }

}









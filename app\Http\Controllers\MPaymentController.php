<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use App\PayTransaction;
use App\SenangPay;
use Session;
use Redirect;
use Validator;
class MPaymentController extends Controller
{
    public function payment()
    {
        
        return view('payment.payment_form');
    }
    public function addpayment(Request $request){
    
        $this->validate($request, [
            'amount' => 'required|numeric|min:2',
            'donate_type' => 'required'
        ]);

        $notes='';
        if(!empty($request->student_name)){
        
            $order = 'ST' . date('ym') .  rand(100,1000); 
            $nots=$request->student_name;
        }
        else{
            $order = 'OTH' . date('ym') .  rand(100,1000); 
        }

        $payment=new PayTransaction();
        $payment->order_id= $order ;
        $payment->transaction_stutes=0;
        $payment->amount= $request->amount;
        $payment->detail=$request->donate_type;
        if(!empty($request->student_name)){
        
            $payment->is_student='1';
            $payment->student_name=$request->student_name;
            $payment->months_number=$request->daterange;
            
        }
        else
        {
            $payment->is_student='0';
        }
        
        $payment->save();
        $payerNameFromSession = session()->get("payer_name") ? session()->get("payer_name") : "Name";

        $payerEmail = session()->get("payer_email") ? session()->get("payer_email") : "<EMAIL>";

        $stripped = str_replace(' ', '', $request->donate_type);
//        $payerName = "Name";
        $payerName = $payerNameFromSession;
//        $payerEmail = "<EMAIL>";
        $payerEmail = $payerEmail;
        $payerPhone = "01123456789";
        $detail =    $stripped ; // Change to any title of this order
        $orderId = $payment->id; // Make sure it is a unique no and not a running number that payer can guest.
        $amount = $request->amount; // Equals to RM300.00
        $merchantId = '121158775027823';
        $secretkey = '19651-926';
      
        // $merchantId = '841158934486412';
        // $secretkey = '2536-508';
        

        $hash = hash_hmac('sha256', $secretkey.$detail.$amount.$orderId, $secretkey);
       

        // $hash = hash_hmac('sha256', $secretkey.urldecode( $detail).urldecode($amount).urldecode($order_id), $secretkey);

       
        //  $hash= md5($secretkey.urldecode($detail).urldecode($amount).urldecode($orderId ));
       $data['detail']=$detail;
       $data['amount']=$amount;

       $data['order_id']=$orderId;
       $data['name']=$payerName;

       $data['email']= $payerEmail;
       $data['phone']= $payerPhone;
       $data['hashed_string']= $hash;
       $data['merchant_id']=$merchantId;
      
        return view('payment.pay_proced', $data);
        // $httpQuery = http_build_query([
        //     'detail' => $detail,
        //     'amount' => $amount,
        //     'order_id' => $orderId,
        //     'hash' => $hash,
        //     'name' => $name,
        //     'email' => $email,
        //     'phone'=> $phone
         
        // ]);
        
      
      
        // // $getUrl = 'https://app.senangpay.my/payment/'.$merchantId.'?'.$httpQuery;
        //        $getUrl = 'https://sandbox.senangpay.my/payment/'.$merchantId.'?'.$httpQuery;

        // $ch = curl_init();
        // curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        // curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
        // curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        // curl_setopt($ch, CURLOPT_URL, $getUrl);
        // curl_setopt($ch, CURLOPT_TIMEOUT, 80);

        //  $response = curl_exec($ch);

        // return  $response;
       
    }

    public function paymentReturn(Request $request){
        $merchantId = '121158775027823';
        $secretkey = '19651-926';
        // $merchantId = '841158934486412';
        // $secretkey = '2536-508';
      
        $data[]='';

        if(isset($_GET['status_id']) && isset($_GET['order_id']) && isset($_GET['msg']) && isset($_GET['transaction_id']) && isset($_GET['hash'])){
           
            $hashed_string = hash_hmac('sha256', $secretkey.urldecode($_GET['status_id']).urldecode($_GET['order_id']).urldecode($_GET['transaction_id']).urldecode($_GET['msg']).'&hash=[HASH]', $secretkey);

        # if hash is the same then we know the data is valid
        if($hashed_string ==urldecode($_GET['hash']))
      {
        if(urldecode($_GET['status_id'])== 1){
            $data['order_id']=urldecode($_GET['order_id']);
            $data['msg']=$_GET['msg'];
            $data['status']=urldecode($_GET['status_id']);
          
        if(!empty(urldecode($_GET['order_id']))){
            $payment=PayTransaction::find(urldecode($_GET['order_id']));
            if(!empty( $payment)){
                 $payment->transaction_stutes=1;
                
                 $payment->save();
                }
                $data['amount']= $payment->amount;
                $data['detail']= $payment->detail;
           
        }
       
           return view('payment.payment_status', $data);
        }
       
        else
           {
            $data=['stats'=>'Fail'];
            Session::flash('message',$request->msg);
            return Redirect::to('payment');
           }
            
    }
    else{
        if(urldecode($_GET['status_id'] )== 1){
            $data['order_id']=urldecode($_GET['order_id']);
            $data['msg']=$_GET['msg'];
            $data['status']=urldecode($_GET['status_id']);
          
        if(!empty(urldecode($_GET['order_id']))){
            $payment=PayTransaction::find(urldecode($_GET['order_id']));
            if(!empty( $payment)){
                 $payment->transaction_stutes=1;
                
                 $payment->save();
                }
                $data['amount']= $payment->amount;
                $data['detail']= $payment->detail;
           
        }
       
           return view('payment.payment_status', $data);
        }
       
        else
           {
            $data=['stats'=>'Fail'];
            Session::flash('message',$request->msg);
            return Redirect::to('payment');
           }
        
        // $data['msg']=$_GET['msg'];
        // $data['order_id']=urldecode($_GET['order_id']);
        // $data['status']=urldecode($_GET['status_id']);
      
        //     return view('payment.payment_status', $data);
    }
       
   }
   else{
    Session::flash('message','Fail');
    return Redirect::to('payment');
   }
  


    }

    public function callbackReturn(Request $request){  
        if($request->status_id == '1'){

        
        return view('payment.payment_status', $data);
          }    
            else{
            Session::flash('message','Fail');
             return Redirect::to('payment');
            }

    }
}

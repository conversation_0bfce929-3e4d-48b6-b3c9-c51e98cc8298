<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * JobSeeker Account Lockout Entity
 * 
 * Tracks account lockouts due to failed login attempts
 * or other security violations
 */
final class JobSeekerAccountLockout extends Model
{
    use HasFactory;

    protected $table = 'job_seeker_account_lockouts';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'job_seeker_id',
        'email',
        'failed_attempts',
        'first_failed_at',
        'last_failed_at',
        'is_locked',
        'locked_until',
        'lock_reason',
        'unlock_reason',
        'last_ip_address',
        'user_agent',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'first_failed_at' => 'datetime',
        'last_failed_at' => 'datetime',
        'locked_until' => 'datetime',
        'is_locked' => 'boolean',
        'failed_attempts' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the job seeker that owns this lockout record.
     */
    public function jobSeeker(): BelongsTo
    {
        return $this->belongsTo(JobSeeker::class, 'job_seeker_id');
    }

    /**
     * Check if the lockout is currently active
     *
     * @return bool
     */
    public function isCurrentlyActive(): bool
    {
        return $this->is_locked && $this->locked_until->isFuture();
    }

    /**
     * Get the remaining lockout time in minutes
     *
     * @return int
     */
    public function getRemainingMinutes(): int
    {
        if (!$this->isCurrentlyActive()) {
            return 0;
        }

        return $this->locked_until->diffInMinutes(now());
    }

    /**
     * Get human-readable lockout reason
     *
     * @return string
     */
    public function getReasonDescription(): string
    {
        return match($this->lock_reason) {
            'failed_login_attempts' => 'Too many failed login attempts',
            'suspicious_activity' => 'Suspicious account activity detected',
            'security_violation' => 'Security policy violation',
            'manual_lock' => 'Manually locked by administrator',
            default => 'Account temporarily locked'
        };
    }
} 
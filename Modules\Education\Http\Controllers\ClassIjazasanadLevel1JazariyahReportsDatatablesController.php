<?php

namespace Modules\Education\Http\Controllers;


use App\Classes;
use App\EvaluationSchemaOption;
use App\IjazasanadMemorizationPlan;
use App\MoshafSurah;
use App\ProgramLevelLesson;
use App\StudentIjazasanadMemorizationReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;


class ClassIjazasanadLevel1JazariyahReportsDatatablesController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function getRecords(Request $request)
    {

        DB::connection()->enableQueryLog();

        if ($request->filled('studentId') || $request->filled('classDate')) {

            $year = Carbon::parse($request->get('classDate'))->year;
            $month = Carbon::parse($request->get('classDate'))->month;
            $classId = $request->get('classId');
            $classDate = $request->get('classDate');


            // Fetching memorization reports based on class and date


            $StudentIjazasanadMemorizationReports = StudentIjazasanadMemorizationReport::where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereNull('jazariyah_from_lesson')
                ->whereNull('jazariyah_to_lesson')
//                ->whereNull('hefz_to_surat')
//                ->whereNull('hefz_to_ayat')
                ->with(['student', 'ijazasanadMemorizationPlan'])
                ->get()
                ->groupBy('student_id')
                ->map(function ($records) {
                    $firstReport = $records->first();
                    $lastReport = $records->last();

                    $from_jazariyah = $records->min('jazariyah_from_lesson');
                    $to_jazariyah = $records->max('jazariyah_to_lesson');

                    $plannedLessons = $firstReport->ijazasanadMemorizationPlan
                        ? ($firstReport->ijazasanadMemorizationPlan->jazariyah_to_lesson - $firstReport->ijazasanadMemorizationPlan->jazariyah_from_lesson) + 1
                        : 0;

                    $completedLessons = ($to_jazariyah - $from_jazariyah) + 1;

                    return [
                        'student' => $firstReport->student,
                        'planned_lessons' => $plannedLessons,
                        'completed_lessons' => $completedLessons,
                        'from_jazariyah' => $from_jazariyah,
                        'to_jazariyah' => $to_jazariyah,
                    ];
                });

            return \Yajra\DataTables\DataTables::of($StudentIjazasanadMemorizationReports)
                ->addIndexColumn()
                ->addColumn('student', function ($reportDetails) {
                    $studentName = ucfirst($reportDetails['student']->full_name);
                    $studentProfileUrl = route('students.show', ['id' => $reportDetails['student']->user_id]);
                    return '<a style="color:#b4eeb0; padding: 2px; display: inline-block;" data-id='.$reportDetails['student']->id.' class="section class-link" target="_blank" href="' . $studentProfileUrl . '">' . $studentName . '</a>';
                })
                ->addColumn('monthlyJazariyahPlan', function ($reportDetails) {
                    $fromLesson = ProgramLevelLesson::find($reportDetails['from_jazariyah']);
                    $toLesson = ProgramLevelLesson::find($reportDetails['to_jazariyah']);
                    if ($fromLesson && $toLesson) {
                        $fromVerse = $fromLesson->properties['jazariyah'] ?? '';
                        $toVerse = $toLesson->properties['jazariyah'] ?? '';
                        $lessonName = htmlspecialchars(" {$fromVerse} To : {$toVerse}");
                        return strlen($lessonName) > 15
                            ? '<a style="color:#b4eeb0;" title="pages: ' . $lessonName . '">' . $lessonName . '</a>'
                            : '<span style="color: #b4eeb0;">' . $lessonName . '</span>';
                    }
                    return '<span style="color: #b4eeb0;">N/A</span>';
                })
                ->addColumn('monthlyJazariyahReport', function ($reportDetails) {
                    $fromLesson = ProgramLevelLesson::find($reportDetails['from_jazariyah']);
                    $toLesson = ProgramLevelLesson::find($reportDetails['to_jazariyah']);
                    if ($fromLesson && $toLesson) {
                        $fromVerse = $fromLesson->properties['jazariyah'] ?? '';
                        $toVerse = $toLesson->properties['jazariyah'] ?? '';
                        return '<span style="color: #b4eeb0;">' . htmlspecialchars("{$fromVerse} To : {$toVerse}") . '</span>';
                    }
                    return '<span style="color: #b4eeb0;">N/A</span>';
                })
                ->addColumn('totalLessons', function ($reportDetails) {
                    $totalLessons = $reportDetails['completed_lessons'];
                    return '<h2 style="color: #1fff0f; font-weight: bold;">' . $totalLessons . '</h2>';
                })
                ->addColumn('totalPlannedLessons', function ($reportDetails) {
                    $totalPlannedLessons = $reportDetails['planned_lessons'];
                    return '<h2 style="color: #1fff0f; font-weight: bold;">' . $totalPlannedLessons . '</h2>';
                })
                ->addColumn('attendanceDaysPercentage', function ($reportDetails) use ($classId, $year, $month) {
                    $classTimetable = Classes::find($classId)->timetable;
                    $totalClasses = 0;
                    $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);
                    for ($day = 1; $day <= $daysInMonth; $day++) {
                        $dayOfWeek = strtolower(date('D', strtotime("$year-$month-$day")));
                        if ($classTimetable && !is_null($classTimetable->$dayOfWeek)) {
                            $totalClasses++;
                        }
                    }

                    $attendedClasses = \App\StudentIjazasanadMemorizationReport::where('student_id', $reportDetails['student']->id)
                        ->where('class_id', $classId)
                        ->whereYear('created_at', $year)
                        ->whereMonth('created_at', $month)
                        ->whereIn('attendance_id', [1, 2])
                        ->count();

//                    $attendedClasses = $reportDetails['completed_lessons'];
                    $result = $totalClasses > 0 ? round(($attendedClasses / $totalClasses) * 100, 2) : 0;
                    return '<div class="progress" style="position: relative;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' . $result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%; background-color: #1fff0f;">
                        <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
                    </div>
                </div>';
                })
                ->addColumn('jazariyahAchievementComparedtoJazariyahPlan', function ($reportDetails) {
                    $plannedLessons = $reportDetails['planned_lessons'];
                    $completedLessons = $reportDetails['completed_lessons'];
                    $result = $plannedLessons > 0 ? round(($completedLessons / $plannedLessons) * 100, 2) : 0;
                    return '<div class="progress" style="position: relative;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" role="progressbar" aria-valuenow="' . $result . '" aria-valuemin="0" aria-valuemax="100" style="width: ' . $result . '%; background-color: #1fff0f;">
                        <span style="position: absolute; left: 0; right: 0; top: 50%; transform: translateY(-50%); color: #333333; font-size: 1.5em;">' . $result . '%</span>
                    </div>
                </div>';
                })
                ->rawColumns(['student', 'monthlyJazariyahPlan', 'monthlyJazariyahReport', 'totalLessons', 'totalPlannedLessons', 'attendanceDaysPercentage', 'jazariyahAchievementComparedtoJazariyahPlan'])
                ->make(true);


        }

    }

}

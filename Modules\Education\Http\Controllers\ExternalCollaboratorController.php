<?php

namespace Modules\Education\Http\Controllers;
use App\AuditLog;
use App\Http\Controllers\Controller;
use App\User;
use App\Center;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class ExternalCollaboratorController extends Controller
{
    /**
     * List all external collaborator users.
     */
    public function index()
    {
        // We assume 'external_collaborator' is the name of the role
        // (or 'external_collaborator_'.config("organization_id").'_')
        $roleName = 'external_collaborator';

        // Fetch users with that role + any needed data
        $users = User::role($roleName)->with(['allowedCenters'])->get();

        // Return a minimal view
        return view('humanresource.external_collab.index', compact('users'));
    }

    /**
     * Show form to create a new external collaborator
     */
    public function create()
    {
        // All centers to select from
        $centers = Center::orderBy('location')->get();
        return view('humanresource.external_collab.create', compact('centers'));
    }

    /**
     * Store new external collaborator
     */
    public function store(Request $request)
    {
        // Validate
        $request->validate([
            'name' => 'required|string|max:191',
            'email' => 'required|email|unique:users,email', // <-- Enforce unique here
            'password' => 'required|min:6|confirmed',
            'center_ids' => 'required|array',
        ]);

        // Create user
        $user = new User();
        $user->name = $request->input('name');
        $user->email = $request->input('email');
        $user->password = Hash::make($request->input('password'));
        $user->status = 'active'; // or use boolean for is_active
        $user->save();

        // Attach role
        $user->assignRole('external_collaborator');

        // Attach centers in pivot
        $user->allowedCenters()->sync($request->input('center_ids'));

        // PHASE 5: Audit log creation
        AuditLog::create([
            'action'        => 'Created external collaborator',
            'performed_by'  => auth()->id(),           // HR/IT user ID
            'target_user_id'=> $user->id,              // newly created user
            'ip_address'    => $request->ip(),
            'info'          => json_encode([
                'assigned_centers' => $request->input('center_ids'),
                'email'            => $user->email
            ]),
        ]);

        return redirect()->route('external.collab.index')->with('success','External user created');
    }

    /**
     * Edit form for existing user
     */
    public function edit(User $user)
    {
        $centers = Center::orderBy('location')->get();

        // Check if user actually has the external_collaborator role
        // else abort(403)
        if (!$user->hasRole('external_collaborator')) {
            abort(403,'Not an external collaborator');
        }

        // existing center IDs
        $assignedCenters = $user->allowedCenters->pluck('id')->toArray();

        return view('humanresource.external_collab.edit', compact('user','centers','assignedCenters'));
    }

    /**
     * Update user details, reassign centers
     */
    public function update(Request $request, User $user)
    {
        // Basic validation
        $request->validate([
            'name' => 'required|string|max:191',
            'email' => 'required|email|unique:users,email,'.$user->id,
            'center_ids' => 'required|array'
        ]);

        // If they want to update password optionally
        if ($request->filled('password')) {
            $request->validate([
                'password' => 'min:6|confirmed'
            ]);
            $user->password = Hash::make($request->input('password'));
        }

        $user->name = $request->input('name');
        $user->email = $request->input('email');
        $user->save();

        // Re-sync centers
        $user->allowedCenters()->sync($request->input('center_ids'));

        // PHASE 5: Audit log for updates
        AuditLog::create([
            'action'        => 'Updated external collaborator',
            'performed_by'  => auth()->id(),
            'target_user_id'=> $user->id,
            'ip_address'    => $request->ip(),
            'info'          => json_encode([
                'updated_fields' => $request->except('_token', 'password', 'password_confirmation'),
                'assigned_centers' => $request->input('center_ids')
            ]),
        ]);


        return redirect()->route('external.collab.index')->with('success','External user updated');
    }

    /**
     * Toggle user status (enable/disable)
     */
    public function toggleStatus(User $user)
    {
        // Flip status
        if($user->status == 'active') {
            $user->status = 'inactive';
        } else {
            $user->status = 'active';
        }
        $user->save();
// PHASE 5: Audit log for enable/disable
        \App\AuditLog::create([
            'action'        => 'Toggled external collaborator status',
            'performed_by'  => auth()->id(),
            'target_user_id'=> $user->id,
            'ip_address'    => request()->ip(),
            'info'          => json_encode([
                'new_status' => $user->status
            ]),
        ]);

        return redirect()->back()->with('success','User status changed to '. $user->status);
    }
}


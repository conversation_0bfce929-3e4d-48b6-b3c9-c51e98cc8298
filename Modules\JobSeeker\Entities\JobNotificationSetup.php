<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Entities\ProviderJobCategory;

final class JobNotificationSetup extends Model
{
    protected $table = 'job_notification_setups';
    
    protected $fillable = [
        'job_seeker_id',
        'name',
        'provider_name',
        'category_count',
        'sent_count',
        'last_notified_at',
        'last_sync_at',
        'is_active',
        'requires_review_reason',
        'last_activity_check_at',
        'receive_push_notifications',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'category_count' => 'integer',
        'sent_count' => 'integer',
        'last_notified_at' => 'datetime',
        'last_sync_at' => 'datetime',
        'last_activity_check_at' => 'datetime',
        'is_active' => 'boolean',
        'receive_push_notifications' => 'boolean',
    ];

    /**
     * Get the job seeker that owns this notification setup.
     */
    public function jobSeeker(): BelongsTo
    {
        return $this->belongsTo(JobSeeker::class, 'job_seeker_id');
    }

    /**
     * The categories (master/canonical) that belong to this notification setup.
     *
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(
            JobCategory::class,
            'job_notification_category',
            'setup_id',
            'category_id'
        )->withTimestamps();
    }

    /**
     * The provider-specific categories that belong to this notification setup.
     */
    public function providerCategories(): BelongsToMany
    {
        return $this->belongsToMany(
            ProviderJobCategory::class,
            'job_notification_provider_category',
            'setup_id',
            'provider_category_id'
        )->withTimestamps();
    }
    
    /**
     * Get the recipients for this notification setup.
     */
    public function recipients(): HasMany
    {
        return $this->hasMany(JobNotificationRecipient::class, 'setup_id');
    }
    
    /**
     * Increment the sent counter
     * 
     * @return bool
     */
    public function incrementSentCount(): bool
    {
        $previousCount = $this->sent_count ?? 0;
        $newCount = $previousCount + 1;
        
        Log::debug('JobNotificationSetup: ENTRY - Incrementing sent count', [
            'setup_id' => $this->id,
            'setup_name' => $this->name,
            'previous_count' => $previousCount,
            'new_count' => $newCount
        ]);
        
        try {
            $this->sent_count = $newCount;
            $result = $this->save();
            
            if ($result) {
                Log::info('JobNotificationSetup: Successfully incremented sent count', [
                    'setup_id' => $this->id,
                    'setup_name' => $this->name,
                    'previous_count' => $previousCount,
                    'new_count' => $newCount,
                    'timestamp' => now()->toDateTimeString()
                ]);
            } else {
                Log::warning('JobNotificationSetup: Failed to save sent count increment', [
                    'setup_id' => $this->id,
                    'setup_name' => $this->name,
                    'attempted_count' => $newCount
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('JobNotificationSetup: EXCEPTION - Error incrementing sent count', [
                'setup_id' => $this->id,
                'setup_name' => $this->name ?? 'Unknown',
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'previous_count' => $previousCount
            ]);
            
            return false;
        }
    }

    /**
     * Mark this setup as requiring review with a reason
     * 
     * @param string $reason
     * @return bool
     */
    public function markForReview(string $reason): bool
    {
        $previousReason = $this->requires_review_reason;
        
        Log::info('JobNotificationSetup: ENTRY - Marking setup for review', [
            'setup_id' => $this->id,
            'setup_name' => $this->name,
            'new_reason' => $reason,
            'previous_reason' => $previousReason,
            'was_already_requiring_review' => !empty($previousReason),
            'timestamp' => now()->toDateTimeString()
        ]);
        
        try {
            $this->requires_review_reason = $reason;
            $result = $this->save();
            
            if ($result) {
                Log::warning('JobNotificationSetup: Setup marked for review successfully', [
                    'setup_id' => $this->id,
                    'setup_name' => $this->name,
                    'reason' => $reason,
                    'job_seeker_id' => $this->job_seeker_id,
                    'categories_count' => $this->categories()->count(),
                    'timestamp' => now()->toDateTimeString()
                ]);
            } else {
                Log::error('JobNotificationSetup: Failed to save review requirement', [
                    'setup_id' => $this->id,
                    'setup_name' => $this->name,
                    'attempted_reason' => $reason
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('JobNotificationSetup: EXCEPTION - Error marking setup for review', [
                'setup_id' => $this->id,
                'setup_name' => $this->name ?? 'Unknown',
                'attempted_reason' => $reason,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return false;
        }
    }

    /**
     * Clear the review requirement
     * 
     * @return bool
     */
    public function clearReviewRequirement(): bool
    {
        $previousReason = $this->requires_review_reason;
        
        Log::info('JobNotificationSetup: ENTRY - Clearing review requirement', [
            'setup_id' => $this->id,
            'setup_name' => $this->name,
            'previous_reason' => $previousReason,
            'was_requiring_review' => !empty($previousReason),
            'timestamp' => now()->toDateTimeString()
        ]);
        
        try {
            $this->requires_review_reason = null;
            $result = $this->save();
            
            if ($result) {
                Log::info('JobNotificationSetup: Review requirement cleared successfully', [
                    'setup_id' => $this->id,
                    'setup_name' => $this->name,
                    'previous_reason' => $previousReason,
                    'job_seeker_id' => $this->job_seeker_id,
                    'timestamp' => now()->toDateTimeString()
                ]);
            } else {
                Log::warning('JobNotificationSetup: Failed to save review requirement clearing', [
                    'setup_id' => $this->id,
                    'setup_name' => $this->name,
                    'previous_reason' => $previousReason
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('JobNotificationSetup: EXCEPTION - Error clearing review requirement', [
                'setup_id' => $this->id,
                'setup_name' => $this->name ?? 'Unknown',
                'previous_reason' => $previousReason,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return false;
        }
    }

    /**
     * Check if this setup requires review
     * 
     * @return bool
     */
    public function requiresReview(): bool
    {
        return !empty($this->requires_review_reason);
    }

    /**
     * Update the last activity check timestamp
     * 
     * @return bool
     */
    public function updateLastActivityCheck(): bool
    {
        $previousTimestamp = $this->last_activity_check_at;
        $newTimestamp = now();
        
        Log::debug('JobNotificationSetup: ENTRY - Updating last activity check timestamp', [
            'setup_id' => $this->id,
            'setup_name' => $this->name,
            'previous_timestamp' => $previousTimestamp?->toDateTimeString(),
            'new_timestamp' => $newTimestamp->toDateTimeString()
        ]);
        
        try {
            $this->last_activity_check_at = $newTimestamp;
            $result = $this->save();
            
            if ($result) {
                Log::debug('JobNotificationSetup: Activity check timestamp updated successfully', [
                    'setup_id' => $this->id,
                    'setup_name' => $this->name,
                    'timestamp' => $newTimestamp->toDateTimeString(),
                    'time_since_last_check' => $previousTimestamp ? $previousTimestamp->diffForHumans($newTimestamp) : 'Never checked before'
                ]);
            } else {
                Log::warning('JobNotificationSetup: Failed to save activity check timestamp', [
                    'setup_id' => $this->id,
                    'setup_name' => $this->name,
                    'attempted_timestamp' => $newTimestamp->toDateTimeString()
                ]);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('JobNotificationSetup: EXCEPTION - Error updating activity check timestamp', [
                'setup_id' => $this->id,
                'setup_name' => $this->name ?? 'Unknown',
                'attempted_timestamp' => $newTimestamp->toDateTimeString(),
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return false;
        }
    }

    /**
     * Get provider identifiers for this setup (used for API calls)
     */
    public function getProviderIdentifiers(): array
    {
        return $this->providerCategories()
            ->where('provider_name', $this->provider_name)
            ->pluck('provider_identifier')
            ->unique()
            ->values()
            ->toArray();
    }

    /**
     * Sync categories with command schedule filters for this provider
     */
    public function syncWithScheduleFilters(): bool
    {
        if (!$this->provider_name || $this->provider_name === 'all') {
            Log::warning('JobNotificationSetup: Cannot sync categories - no specific provider set', [
                'setup_id' => $this->id,
                'provider_name' => $this->provider_name
            ]);
            return false;
        }

        try {
            // Get categories from command schedule filters for this provider
            $scheduleCategories = \DB::table('command_schedule_filters as csf')
                ->join('command_schedule_rules as csr', 'csf.schedule_rule_id', '=', 'csr.id')
                ->where('csr.command', 'like', '%' . $this->provider_name . '%')
                ->whereNotNull('csf.categories')
                ->pluck('csf.categories')
                ->flatMap(function ($categories) {
                    return json_decode($categories, true) ?: [];
                })
                ->unique()
                ->filter(function ($categoryId) {
                    return is_numeric($categoryId);
                })
                ->values();

            if ($scheduleCategories->isEmpty()) {
                Log::info('JobNotificationSetup: No schedule categories found for provider', [
                    'setup_id' => $this->id,
                    'provider_name' => $this->provider_name
                ]);
                return true;
            }

            // Validate that these categories exist in provider_job_categories
            $validCategories = ProviderJobCategory::where('provider_name', $this->provider_name)
                ->whereIn('id', $scheduleCategories->toArray())
                ->pluck('id');

            // Sync the categories
            $this->providerCategories()->sync($validCategories->toArray());

            // Update sync timestamp
            $this->last_sync_at = now();
            $this->save();

            Log::info('JobNotificationSetup: Successfully synced categories with schedule filters', [
                'setup_id' => $this->id,
                'provider_name' => $this->provider_name,
                'categories_synced' => $validCategories->count(),
                'category_ids' => $validCategories->toArray()
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('JobNotificationSetup: Failed to sync categories with schedule filters', [
                'setup_id' => $this->id,
                'provider_name' => $this->provider_name,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Check if this setup should receive notifications for given provider categories
     */
    public function shouldReceiveNotificationForCategories(array $providerCategoryIds): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $setupCategoryIds = $this->providerCategories()->pluck('id')->toArray();

        // Check if there's any overlap between setup categories and job categories
        return !empty(array_intersect($setupCategoryIds, $providerCategoryIds));
    }

    /**
     * Get canonical category names for display purposes
     */
    public function getCanonicalCategoryNames(): array
    {
        return $this->providerCategories()
            ->join('job_categories', 'provider_job_categories.canonical_category_id', '=', 'job_categories.id')
            ->pluck('job_categories.name')
            ->unique()
            ->values()
            ->toArray();
    }

    /**
     * Get provider category names for display purposes
     */
    public function getProviderCategoryNames(): array
    {
        return $this->providerCategories()
            ->pluck('name')
            ->toArray();
    }

    /**
     * Get allowed canonical category IDs for this setup (canonical-first design).
     *
     * @return array<int,int>
     */
    public function getAllowedCanonicalCategoryIds(): array
    {
        try {
            $ids = $this->categories()->pluck('job_categories.id')->all();
            if (!is_array($ids)) {
                return [];
            }
            return array_values(array_unique(array_map('intval', $ids)));
        } catch (\Throwable $e) {
            Log::error('JobNotificationSetup: Failed to resolve allowed canonical category IDs', [
                'setup_id' => $this->id ?? null,
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * Map current canonical categories to provider-specific identifiers for a given provider.
     *
     * @param string $providerName e.g., 'jobs.af', 'acbar'
     * @return array<int,string> Provider identifiers
     */
    public function mapCanonicalToProviderIdentifiers(string $providerName): array
    {
        $canonicalIds = $this->getAllowedCanonicalCategoryIds();
        if (empty($canonicalIds)) {
            return [];
        }
        $rows = ProviderJobCategory::query()
            ->where('provider_name', $providerName)
            ->whereIn('canonical_category_id', $canonicalIds)
            ->pluck('provider_identifier');
        return $rows ? $rows->filter()->values()->all() : [];
    }
}
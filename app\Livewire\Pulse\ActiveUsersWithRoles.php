<?php

namespace App\Livewire\Pulse;

use Livewire\Component;

class ActiveUsersWithRoles extends Component
{


    public function render()
    {
        // Retrieve active users with their roles
        $activeUsers = \DB::table('users')
            ->join('sessions', 'users.id', '=', 'sessions.user_id')
            ->leftJoin('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->leftJoin('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->whereNotNull('sessions.user_id')
            ->select('users.id', 'users.full_name', 'roles.name as role')
            ->distinct()
            ->get();


        return view('livewire.pulse.active-users-with-roles', [
            'activeUsers' => $activeUsers,
        ]);
    }

}

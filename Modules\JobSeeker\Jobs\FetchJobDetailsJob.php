<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Entities\Job;
use Modules\JobSeeker\Services\JobDetailFetchingService;

/**
 * FetchJobDetailsJob
 * 
 * Background job to fetch detailed job information from provider websites
 * Processes jobs asynchronously to avoid blocking main job synchronization
 */
final class FetchJobDetailsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public int $timeout = 300; // 5 minutes

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public int $backoff = 60; // 1 minute

    /**
     * The job ID
     *
     * @var int
     */
    private int $jobId;

    /**
     * Create a new job instance.
     *
     * @param Job $job
     */
    public function __construct(Job $job)
    {
        $this->jobId = $job->id;
        $this->onQueue('job-details'); // Dedicated queue for job detail fetching
        
        // Add random delay to avoid overwhelming providers
        $this->delay(now()->addSeconds(rand(30, 180)));
        
        Log::info('FetchJobDetailsJob: Job queued for detail fetching', [
            'job_id' => $this->jobId,
            'position' => $job->position,
            'company' => $job->company_name,
            'source' => $job->source,
            'queue' => 'job-details'
        ]);
    }

    /**
     * Execute the job.
     *
     * @param JobDetailFetchingService $fetchingService
     * @return void
     */
    public function handle(JobDetailFetchingService $fetchingService): void
    {
        $startTime = microtime(true);
        
        Log::info('FetchJobDetailsJob: Starting job detail fetch', [
            'job_id' => $this->jobId,
            'attempt' => $this->attempts(),
            'max_attempts' => $this->tries
        ]);

        try {
            // Load the job
            $job = Job::find($this->jobId);
            
            if (!$job) {
                Log::warning('FetchJobDetailsJob: Job not found, skipping', [
                    'job_id' => $this->jobId
                ]);
                return;
            }

            // Check if job needs detailed fetch
            if (!$fetchingService->needsDetailedFetch($job)) {
                Log::info('FetchJobDetailsJob: Job does not need detailed fetch, skipping', [
                    'job_id' => $this->jobId,
                    'position' => $job->position
                ]);
                return;
            }

            // Fetch job details
            $success = $fetchingService->fetchJobDetails($job);
            
            $duration = round((microtime(true) - $startTime) * 1000, 2); // milliseconds

            if ($success) {
                Log::info('FetchJobDetailsJob: Successfully fetched job details', [
                    'job_id' => $this->jobId,
                    'position' => $job->position,
                    'company' => $job->company_name,
                    'source' => $job->source,
                    'duration_ms' => $duration,
                    'attempt' => $this->attempts()
                ]);
            } else {
                Log::warning('FetchJobDetailsJob: Failed to fetch job details', [
                    'job_id' => $this->jobId,
                    'position' => $job->position,
                    'source' => $job->source,
                    'duration_ms' => $duration,
                    'attempt' => $this->attempts(),
                    'will_retry' => $this->attempts() < $this->tries
                ]);

                // If this is not the final attempt, throw exception to trigger retry
                if ($this->attempts() < $this->tries) {
                    throw new \Exception('Job detail fetch failed, will retry');
                }
            }

        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            Log::error('FetchJobDetailsJob: Exception during job detail fetch', [
                'job_id' => $this->jobId,
                'error' => $e->getMessage(),
                'duration_ms' => $duration,
                'attempt' => $this->attempts(),
                'max_attempts' => $this->tries,
                'trace' => $e->getTraceAsString()
            ]);

            // Re-throw to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('FetchJobDetailsJob: Job permanently failed after all retries', [
            'job_id' => $this->jobId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
            'max_attempts' => $this->tries
        ]);

        // Optionally, mark the job as permanently failed in the database
        try {
            $job = Job::find($this->jobId);
            if ($job && $job->provider) {
                \Modules\JobSeeker\Entities\JobDetailedInfo::updateOrCreate(
                    [
                        'job_id' => $this->jobId,
                        'provider_id' => $job->provider->id,
                    ],
                    [
                        'fetch_success' => false,
                        'fetch_error' => 'Permanently failed after ' . $this->tries . ' attempts: ' . $exception->getMessage(),
                        'fetched_at' => now(),
                    ]
                );
            }
        } catch (\Exception $e) {
            Log::error('FetchJobDetailsJob: Failed to record permanent failure', [
                'job_id' => $this->jobId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return int
     */
    public function backoff(): int
    {
        // Exponential backoff: 1 min, 2 min, 4 min
        return $this->backoff * pow(2, $this->attempts() - 1);
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array
     */
    public function tags(): array
    {
        return ['job-details', 'job-' . $this->jobId];
    }

    /**
     * Determine if the job should be retried based on the exception.
     *
     * @param \Throwable $exception
     * @return bool
     */
    public function retryUntil(): \DateTime
    {
        // Retry for up to 1 hour from the initial attempt
        return now()->addHour();
    }
}

<?php

namespace Modules\ExaminationCertification\Http\Controllers;


use App\Admission;
use App\BaseSetup;
use App\Center;
use App\CenterEmployee;
use App\Classes;
use App\EmailSetting;
use App\Employee;
use App\GeneralSettings;
use App\Program;
use App\Rules\CheckIfStringIsEnglish;
use App\Student;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Validation\Rule;
use Modules\Admission\Facades\Settings;
use Illuminate\Routing\Controller;
use Modules\ApplicationCenter\Http\Requests\StudentRegistrationByEmployeeRequest;

class StudentRegisterationByEmployeeController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;


    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = '/';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Get the response for a successful user verification.
     *
     * @param string $response
     * @return \Illuminate\Http\RedirectResponse
     */
    protected function sendVerificationResponse($response)
    {

        return redirect($this->redirectPath())->with('success', trans($response));
    }




    /**
     * Get a validator for an incoming registration request.
     *
     * @param array $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected
    function validator(array $data)
    {



        return Validator::make($data, [
            'fullname' => ['required','string','max:255','different:email',new CheckIfStringIsEnglish()],
            'nationality' => ['required',Rule::notIn(['no'])],
            'displayname' => 'required|string|max:255|different:email',
            'username' => 'required|string|max:25|unique:users,username',
            'email' => 'required|string|email|max:255|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
        ]);

    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param array $data
     * @return \App\User
     */
    protected
    function create(array $data, $roleName = null)
    {

        $user = User::create([
            'email' => $data['email'],
            'display_name' => $data['displayname'],
            'username' => $data['username'],
            'full_name' => $data['fullname'],
            'nationality' => $data['nationality'],
            'access_status' => '0',
            'is_administrator' => 'no',
            'organization_id' => config('organization_id'),
            'password' => bcrypt($data['password']),
        ]);
        $this->studentStore($data,$role='');
        event(new Verified($user));
//        $user->sendEmailVerificationNotification();
        // assign default role
        $this->assignDefaultRoles($user, $roleName);
//        $user->assignRole(Role::find($data['role'])->name);
//        if ($data['role'] == Role::where("name", 'parent')->first()->id) {
//
//
//            Guardian::create([
//                'name' => $data['email'],
//                'full_name' => $data['fullname'],
//                'nationality' => $data['nationality'],
//                'email' => $data['email'],
//                'organization_id' => config('organization_id'),
//                'role_id' => $data['role'],
//                'user_id' => $user->id
//            ]);
//
//            // register a record in the guardians table as well
//        } else { // register as a student
//            Student::create([
//                'name' => $data['email'],
//                'full_name' => $data['fullname'],
//                'email' => $data['email'],
//                'nationality' => $data['nationality'],
//                'organization_id' => config('organization_id'),
//                'role_id' => $data['role'],
//                'user_id' => $user->id
//            ]);
//
//
//        }



        return $user;

    }

    public function studentStore(\Modules\ExaminationCertification\Http\Requests\StudentRegistrationByEmployeeRequest $request)
    {


        $document_file_1 = "";
        if ($request->file('document_file_1') != "") {
            $file = $request->file('document_file_1');
            $document_file_1 = 'doc1-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            $file->move('public/uploads/student/document/', $document_file_1);
            $document_file_1 = 'public/uploads/student/document/' . $document_file_1;
        }

        $document_file_2 = "";
        if ($request->file('document_file_2') != "") {
            $file = $request->file('document_file_2');
            $document_file_2 = 'doc2-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            $file->move('public/uploads/student/document/', $document_file_2);
            $document_file_2 = 'public/uploads/student/document/' . $document_file_2;
        }

        $document_file_3 = "";
        if ($request->file('document_file_3') != "") {
            $file = $request->file('document_file_3');
            $document_file_3 = 'doc3-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            $file->move('public/uploads/student/document/', $document_file_3);
            $document_file_3 = 'public/uploads/student/document/' . $document_file_3;
        }

        $document_file_4 = "";
        if ($request->file('document_file_4') != "") {
            $file = $request->file('document_file_4');
            $document_file_4 = 'doc4-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
            $file->move('public/uploads/student/document/', $document_file_4);
            $document_file_4 = 'public/uploads/student/document/' . $document_file_4;
        }

        DB::beginTransaction();
        try {

            $user_stu = Auth::guard("web")->user();


            if ($request->has("gender")) {
                $user_stu->gender = BaseSetup::find($request->gender)->base_setup_name;
                $user_stu->save();
                $user_stu->toArray();
            }
//            $user_stu->assignRole('student');
            try {


                // if not applied so far, then proceed with this condition
                if(is_null($user_stu->student)){


                    if ($request->filled('date_of_birth')){
                        $dob = Carbon::parse($request->get('date_of_birth'))->format('Y-m-d');


                    }


                    $student = new Student();
                    $student->user_id = $user_stu->id;
                    // student number format ( two digits of year+two digits of month+two digits of  hour+two digits of minutes+two digits of  seconds
                    $student->student_number = Carbon::now()->format('y') . Carbon::now()->format('n') . Carbon::now()->format('H') . Carbon::now()->format('i') . Carbon::now()->format('s');
                    $student->display_name = $user_stu->display_name;
                    $student->full_name = $user_stu->full_name;
                    $student->full_name_trans = $user_stu->full_name_trans;
                    $student->identity_number = strtoupper($request->national_id_number);
                    $student->nationality = $user_stu->nationality;
                    $student->gender = BaseSetup::find($request->gender)->base_setup_name;
                    $student->date_of_birth = $dob;
                    $student->email = $user_stu->email;
                    $student->mobile = $request->student_mobile;
                    $student->organization_id = $user_stu->organization_id;
                    $student->status = 'new_admission';
                    $student->active_status = '0';
//                    $student->admission_date = date('Y-m-d', strtotime(now()));


                }else{


                    $student = Student::where('user_id',$user_stu->id)->first();
                    $dob = $student->date_of_birth;
                }


                if (Session::get('student_photo') != "") {
                    $student->student_photo = Session::get('student_photo');
                    $student->image = Session::get('student_photo');

                }



                $student->document_title_1 = $request->document_title_1;
                if ($document_file_1 != "") {
                    $student->document_file_1 = $document_file_1;
                }

                $student->document_title_2 = $request->document_title_2;
                if ($document_file_2 != "") {
                    $student->document_file_2 = $document_file_2;
                }

                $student->document_title_3 = $request->document_title_3;
                if ($document_file_3 != "") {
                    $student->document_file_3 = $document_file_3;
                }

                $student->document_title_4 = $request->document_title_4;

                if ($document_file_4 != "") {
                    $student->document_file_4 = $document_file_4;
                }
                $student->save();
                $student->toArray();
                $studentPhotoUrl = $student->image ? asset($student->image) : asset('avatar.jpg');
                try {
                    $admission = new Admission();
                    $admission->program_id = $request->program;
                    $admission->center_id = $request->center;
                    $admission->class_id = $request->classes;
                    $admission->gender_id = $request->gender;
                    $admission->creator_role = Auth::user()->roles->pluck('name')->toArray()[0];
                    $admission->created_by = Auth::user()->id;
                    $admission->status = 'new_admission';
                    $admission->student_id = $student->id;
                    $admission->student_email = $user_stu->email;
                    $admission->student_mobile = $request->student_mobile;
                    $admission->date_of_birth = $dob;
                    $admission->age = $request->age;
                    $admission->organization_id = config('organization_id');
                    $admission->save();
                    $admission->programs()->sync([$request->program]);


                    try {
                        $user_info = [];

//                        if ($request->student_email != "") {
                        $user_info[] = array('email' => $user_stu->email, 'id' => $student->id, 'slug' => 'student');
//                        }


                        DB::commit();

                        // session null

                        Session::put('student_photo', '');
//                        Session::put('fathers_photo', '');
//                        Session::put('mothers_photo', '');
//                        Session::put('guardians_photo', '');


                        try {

                            $user_info[0]["applicationConfirmationTime"] = $admission->updated_at->format('m/d/Y g:i A');
                            $user_info[0]["student_id"] = $admission->student_id;
                            $user_info[0]['studentName'] = $student->full_name;
                            $user_info[0]['studentNationality'] = $student->nationality;
                            $user_info[0]['programName'] = Program::find($request->program)->title;
                            $user_info[0]['CenterName'] = Center::find($request->center)->name;
                            $user_info[0]['ClassName'] = Classes::find($request->classes)->name;
                            $user_info[0]['username'] = $user_stu->username;
                            $user_info[0]['studentId'] = $student->id;
                            $user_info[0]['studentEmail'] = $student->email;
                            $user_info[0]['refNo'] = $admission->id;
                            $user_info[0]['studentPhotoUrl'] = $studentPhotoUrl;
                            if (count($user_info) != 0) {
                                $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);

                                $systemEmail = EmailSetting::find(1);

                                $system_email = $systemEmail->from_email;
                                $organization_name = $systemSetting->organization_name;

//                                the system email should be the one that is authorized in the mailjet . currently <EMAIL> is <NAME_EMAIL>, but still check in the mailjet account of the company account. get the credentials for mailjet from itqan management
                                $sender['system_email'] = $system_email;
                                $sender['organization_name'] = $organization_name;


//
                                // TODO : transfer this logic to the place where the offered status is given to the student; after status changed to offer, send login details to the student and guardian
//                                dispatch(new \App\Jobs\SendUserMailJob($user_info, $sender));
                                $data = [];
                                $data['programName'] = Program::find($request->program)->title;
                                $data['CenterName'] = Center::find($request->center)->name;
                                $data['ClassName'] = Classes::find($request->classes)->name;
                                $data['applicationDate'] = $admission->created_at;


                                $data['refNo'] = $admission->id;

                                // storing the user application letter confirmation in the storage
                                $pdf = \App::make('dompdf.wrapper');
                                $pdf->loadHTML(view('modules.site.templates.wajeha.backEnd.studentInformation.student_application_confirmation', compact('data'))->render());
                                $content = $pdf->download()->getOriginalContent();
                                \Storage::put("applicationLetters/" . $admission->student_id . "-applicationLetter.pdf", $content);

                                // TODO: send a confirmation stating that Itqan received your application
                                dispatch(new \App\Jobs\SendApplicationConfirmationEmailJob($user_info, $sender));



                                $supervisorEmail = Employee::whereIn('id',CenterEmployee::where('cen_id',$request->center)->pluck('emp_id')->toArray())->pluck('email');
                                // send the notification to the related center supervisor
                                dispatch(new \App\Jobs\SendNewApplicationNotificationToSupervisor($user_info,$sender,$supervisorEmail));



                            }
                        } catch (\Exception $e) {
                            DB::rollback();
                            Log::alert($e->getMessage());

                            Toastr::warning('Operation Failed', 'Failed');
                            return redirect('student-list');
                        }
//                        Session::put('fathers_photo', '');

                        session()->remove('program_id');
                        session()->remove('center_id');
                        session()->remove('class_id');

//                        dd(url("student-list"));
                        Toastr::success('Operation successful', 'Success');
                        return redirect(route("application.list"));
                    } catch (\Exception $e) {
                        DB::rollback();
                        Log::alert($e->getMessage());
                        dd($e->getMessage());
                        Toastr::error('Operation Failed', 'Failed');
                        return redirect()->back();
                    }
                } catch (\Exception $e) {
                    DB::rollback();
                    Log::alert($e->getMessage());
                    dd($e->getMessage());
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            } catch (\Exception $e) {
                DB::rollback();
                Log::alert($e->getMessage());
                dd($e->getMessage());
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            DB::rollback();
            Log::alert($e->getMessage());
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }




    /**
     * Show the application registration form.
     *
     * @return \Illuminate\Http\Response
     */
    public
    function showRegistrationForm()
    {

        $countryList = Settings::getCountriesList();

        return view('auth.registerItqan', compact('countryList'));
    }

    /**
     * Handle a registration request for the application.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public
    function registerStudentDetails(Request $request, $roleName = null)
    {

        $this->validator($request->all())->validate();
        DB::beginTransaction();
        try {
            event(new Registered($user = $this->create($request->all(), $roleName)));


            // send verification email
//            app()->make('Lunaweb\EmailVerification\EmailVerification')->sendVerifyLink($user);


//        $this->guard()->login($user);
//        return $this->registered($request, $user)
//            ?: redirect($this->redirectPath());
//        Toastr::success('Operation successful, Please contact with administrator for confirmation', 'Success');
            DB::commit();

            return redirect()->back()->with('success', 'Please confirm your email or wait for manual confirmation');
        } catch (\Exception $e) {
            DB::rollback();
            dd($e->getMessage());
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }

    protected
    function guard()
    {
        return Auth::guard("web");
    }

    // unique usernamecheck by ajax
    public function checkUsername(Request $request)
    {
        $student = User::where('username', $request->id)->first();

        if ($student != "") {
            return response()->json(1);
        } else {
            return response()->json(0);
        }
    }


    // unique email by ajax
    public function checkEmail(Request $request)
    {
        $student = User::where('email', $request->id)->first();

        if ($student != "") {
            return response()->json(1);
        } else {
            return response()->json(0);
        }
    }



    /**
     * Show form to the user which allows resending the verification mail
     *
     */
    public function showResendVerificationEmailForm(Request $request)
    {


        $user = Auth::user();


        return view('emailverification::resend', ['verified' => $user->verified, 'email' => $user->email]);
    }

    public function resendVerificationEmail(Request $request)
    {
        $user = Auth::user();
        $this->validate($request, [
            'username' => 'required|max:255|exists:users,username'
        ]);



        $user =  User::where('username',$request->get('username'))->first();

        $sent = resolve('Lunaweb\EmailVerification\EmailVerification')->sendVerifyLink($user);
        Session::flash($sent == EmailVerification::VERIFY_LINK_SENT ? 'success' : 'error', trans($sent));
        if(EmailVerification::VERIFY_LINK_SENT == true){
            Toastr::success(trans($sent), 'Success');
        }else{

            Toastr::error(trans($sent), 'Failed');
        }



        return redirect()->back();
    }



    public    function assignDefaultRoles($user, $roleName = null)
    {


//        $default_role_name = Settings::get('default_user_role', 'member');
//        $default_role_name = Settings::get('default_user_role', 'member');


//        $roleName = $default_role_name;


        $user->assignRole('member');
    }


}

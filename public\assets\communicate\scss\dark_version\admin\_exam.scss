.exam-cus-btns {
	margin-top: -150px;
}
.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background-color: $primary-color2;
}
.dataTables_filter > label:before {
    background: $primary-color2;
}
button.dt-button:hover:not(.disabled), div.dt-button:hover:not(.disabled), a.dt-button:hover:not(.disabled) {
	background-color: $primary-color2;
}
div.dt-buttons {
    border: 1px solid $primary-color2;
}
button.dt-button, div.dt-button, a.dt-button {
    border-left: 1px solid $primary-color2;
}
table.dataTable tbody th, table.dataTable tbody td {
    padding: 20px 5px 20px 0px;
    color: $primary-color2;
}
table.dataTable thead th, table.dataTable thead td {
    color: $black;
}
table.dataTable thead td {
    text-transform: uppercase;
    font-size: 12px;
    color: $black;
    font-weight: 600;
    padding: 10px 18px 10px 0px;
    border-bottom: 1px solid #000;
}
table.dataTable {
    width: 100%;
    background: #ffffff;
    padding: 30px 15px;
    border-radius: 5px;
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, .2);
    margin: 0 auto;
    clear: both;
    border-collapse: separate;
    border-spacing: 0;
}
button.dt-button span {
    color: $black;
}
.dataTables_filter > label i {
    color: $primary-color;
}
.dataTables_wrapper .dataTables_filter input {
    color: $primary-color;
}
.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    color: #ffffff !important;
    border: 0px;
    background-color: $primary-color2;
    box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);
}
.dataTables_wrapper .dataTables_length, .dataTables_wrapper .dataTables_filter, .dataTables_wrapper .dataTables_info, .dataTables_wrapper .dataTables_processing, .dataTables_wrapper .dataTables_paginate {
    color: $black;
}
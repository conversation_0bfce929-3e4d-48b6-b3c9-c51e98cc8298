<?php

return [
    'default_location' => 'Kabul',
    'name' => 'JobSeeker',
    'description' => 'Job seeker module for authentication and job notifications',
    
    // Category management settings
    'category_management' => [
        // Number of days to check for category activity (for "empty" category detection)
        'activity_check_days' => 30,
        
        // Number of days to consider for recent job activity when filtering categories
        'recent_activity_days' => 7,
        
        // Whether to show categories with no recent activity by default
        'show_inactive_categories_by_default' => false,
    ],
    
    // Job notification settings
    'job_notifications' => [
        // Number of minutes before verification links expire
        'verification_expiry_minutes' => 60,
        
        // Rate limiting for subscription attempts
        'throttle_attempts' => 5,
        'throttle_minutes' => 30,
    ],

    // Admin access control settings
    'admin_emails' => [
        // List of email addresses that have admin access to JobSeeker module
        // These emails will have access to admin features regardless of role assignments
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>', // Added for testing
        // Add more admin emails as needed
        // '<EMAIL>',
    ],
    
    // Jobs.af scraping settings
    'jobs_af_scraping' => [
        // Random delay configuration for HTTP requests to avoid detection
        'random_delays' => [
            // Minimum delay between HTTP requests (in seconds)
            'min_delay' => env('JOBSEEKER_MIN_DELAY', 120), // 2 minutes default
            
            // Maximum delay between HTTP requests (in seconds)
            'max_delay' => env('JOBSEEKER_MAX_DELAY', 600), // 10 minutes default
            
            // Enable/disable random delays
            'enabled' => env('JOBSEEKER_DELAYS_ENABLED', true),
            
            // Skip delay before the first request in a sequence
            'skip_first_request' => env('JOBSEEKER_SKIP_FIRST_DELAY', true),
        ],
    ],
    
    'facebook' => [
        'page_token' => env('FACEBOOK_PAGE_TOKEN', ''),
        'app_secret' => env('FACEBOOK_APP_SECRET', ''),
        'recipient_id' => env('FACEBOOK_RECIPIENT_ID', ''),
        'version' => env('FACEBOOK_GRAPH_API_VERSION', '4.0'),
    ],
    "units" => [
        "dashboards" => [
            "icon" => "building-o",
            "actions" => [
                "have teacher dashboard",
                "have supervisor dashboard",
                "have finance dashboard",
                "have human_resource dashboard",
                "view approval-awaiting new applications"
            ]
        ],
        "backups" => [
            "icon" => "building-o",
            "actions" => [
                "have students backup",
                "download students backup",
            ]
        ],
        "status" => [
            "icon" => "building-o",
            "actions" => [
                "show system Logs"
            ]
        ],
        "commands" => [
            "icon" => "building-o",
            "actions" => [
                "show comamands interface for admins"
            ]
        ],
        "roles" => [
            "icon" => "key",
            "actions" => [
                "access roles",
                "add role",
                "show role",
                "show role create form",
                "show role edit form",
                "update role",
                "remove role"
            ],
        ],
        "strategies" => [
            "icon" => "bullseye",
            "actions" => [
                "access strategies",
                "add strategy",
                "update strategy",
                "delete strategy",
                "view strategy",
            ]
        ],
        "form_builder" => [
            "icon" => "paper",
            "actions" => [
                "access form_builder",
                "add builder_form",
                "update builder_form",
                "delete builder_form",
                "view builder_form",
            ]
        ],
        "user_verifier" => [
            "icon" => "user",
            "actions" => [
                "access user_verifier",
                "add user_verifier",
                "update user_verifier",
                "delete user_verifier",
                "view user_verifier",
            ]
        ]
    ],
];

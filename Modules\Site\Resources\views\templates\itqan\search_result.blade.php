@extends('home.layouts.home')
@section('page_title') {{trans('home_header.search')}}   @endsection

@section('content')

    <section class="page-header">
        <div class="container">
            <h1>{{trans('home_content.search')}}</h1>
        </div>
    </section>

    <section>
        <div class="container">


            @if(Input::has('query'))
                <h3 class="text-center">
                    {{trans('home_content.search_by_string_result')}}
                    <span style="color:red;">{{Input::get('query')}}</span>
                </h3>

                <span class="alert alert-warning">
                        <strong>
                           -> {{count($posts)}} <-
                            {{trans('home_content.result_found')}}
                        </strong>
                </span>
                <hr/>


                @foreach($posts as $post)
                    <div class="blog-post-item">
                        <div class="blog-item-small-image">
                            <!-- OWL SLIDER -->
                            <div class="owl-carousel buttons-autohide controlls-over"
                                 data-plugin-options='{"items": 1, "autoPlay": 3000, "autoHeight": false, "navigation": true, "pagination": true, "transitionStyle":"fadeUp", "progressBar":"false"}'>
                                <?php /*counter */$count_displayed_pics = 0; ?>
                                @foreach($post->attachments as $att)
                                    <?php /*get file extention */ $ext = pathinfo($att->post_attachment, PATHINFO_EXTENSION);?>
                                    @if($ext=="jpeg" || $ext=="jpg" || $ext=="png" || $ext=="gif")
                                        <?php $count_displayed_pics++; ?>
                                        <div>
                                            <img class="img-responsive"
                                                 style="height: 241px;max-height: 241px;min-height: 241px;"
                                                 src="{{URL::to($att->post_attachment)}}"
                                                 alt="<?php echo $post->{'post_title_' . App::getLocale()} ?>">
                                        </div>
                                    @endif
                                @endforeach

                            <!-- if all attachment files are not image typed (show default image from 2 default images 1.jpg and 2.jpg  randomly) -->
                                @if($count_displayed_pics==0)
                                    <div>
                                        <img class="img-responsive"
                                             style="height: 241px;max-height: 241px;min-height: 241px;"
                                             src="{{URL::to('home_style/images/default_blog_pic/'.random_int(1,3).'.jpg')}}"
                                             alt="<?php echo $post->{'post_title_' . App::getLocale()} ?>">
                                    </div>
                                @endif

                            <!-- if there is no attachmed pic to show-->
                                @if(count($post->attachments)==0)
                                    <div>
                                        <img class="img-responsive"
                                             style="height: 241px;max-height: 241px;min-height: 241px;"
                                             src="{{URL::to('home_style/images/default_blog_pic/'.random_int(1,3).'.jpg')}}"
                                             alt="<?php echo $post->{'post_title_' . App::getLocale()} ?>">
                                    </div>
                                @endif


                            </div>
                            <!-- /OWL SLIDER -->
                        </div>

                        <div class="blog-item-small-content">
                            <h2>
                                <a href="{{url('home/blog/post/'.$post->id.'/'.str_replace(' ','_',$post->{'post_title_'.App::getLocale()}))}}">"<?php echo $post->{'post_title_' . App::getLocale()} ?></a>
                            </h2>
                            <ul class="blog-post-info list-inline">
                                <li>
                                    <a href="#">
                                        <i class="fa fa-clock-o"></i>
                                        <span class="font-lato">{{date("F d, Y", strtotime($post->created_at))}}</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <i class="fa fa-comment-o"></i>
                                        <span class="font-lato">({{count($post->comments)}}
                                            ) {{trans('home_content.comments')}}</span>
                                    </a>
                                </li>
                            </ul>
                            <p style="width: 300px;height: 38px;" class="">
                                <?php echo substr($post->{'post_content_' . App::getLocale()}, 0, 50) . '...' ?>
                            </p>

                            <a href="{{url('home/blog/post/'.$post->id.'/'.str_replace(' ','_',$post->{'post_title_'.App::getLocale()}))}}"
                               class="btn btn-reveal btn-default">
                                <i class="fa fa-plus"></i><span>{{trans('home_content.chech_more')}}</span>
                            </a>
                        </div>
                    </div>
                @endforeach



            @endif

            @if(Input::has('categories_query'))
                <h3 class="text-center">
                    {{trans('home_content.search_by_categ_result')}}
                    {{getCategtitleById(filter_var(Input::get('categories_query'), FILTER_SANITIZE_NUMBER_INT))}}
                </h3>
                <span class="alert alert-warning">
                        <strong>
                           -> {{count($posts)}} <-
                            {{trans('home_content.result_found')}}
                        </strong>
                </span>
                <hr/>
                @foreach($posts as $post)
                    <div class="blog-post-item">
                        <div class="blog-item-small-image">
                            <!-- OWL SLIDER -->
                            <div class="owl-carousel buttons-autohide controlls-over"
                                 data-plugin-options='{"items": 1, "autoPlay": 3000, "autoHeight": false, "navigation": true, "pagination": false, "transitionStyle":"fadeUp", "progressBar":"false"}'>
                                <?php /*counter */$count_displayed_pics = 0; ?>
                                @foreach($post->attachments as $att)
                                    <?php /*get file extention */ $ext = pathinfo($att->post_attachment, PATHINFO_EXTENSION);?>
                                    @if($ext=="jpeg" || $ext=="jpg" || $ext=="png" || $ext=="gif")
                                        <?php $count_displayed_pics++; ?>
                                        <div>
                                            <img class="img-responsive"
                                                 style="height: 241px;max-height: 241px;min-height: 241px;"
                                                 src="{{URL::to($att->post_attachment)}}"
                                                 alt="<?php echo $post->{'post_title_' . App::getLocale()} ?>">
                                        </div>
                                    @endif
                                @endforeach

                            <!-- if all attachment files are not image typed (show default image from 2 default images 1.jpg and 2.jpg  randomly) -->
                                @if($count_displayed_pics==0)
                                    <div>
                                        <img class="img-responsive"
                                             style="height: 241px;max-height: 241px;min-height: 241px;"
                                             src="{{URL::to('home_style/images/default_blog_pic/'.random_int(1,3).'.jpg')}}"
                                             alt="<?php echo $post->{'post_title_' . App::getLocale()} ?>">
                                    </div>
                                @endif

                            <!-- if there is no attachmed pic to show-->
                                @if(count($post->attachments)==0)
                                    <div>
                                        <img class="img-responsive"
                                             style="height: 241px;max-height: 241px;min-height: 241px;"
                                             src="{{URL::to('home_style/images/default_blog_pic/'.random_int(1,3).'.jpg')}}"
                                             alt="<?php echo $post->{'post_title_' . App::getLocale()} ?>">
                                    </div>
                                @endif


                            </div>
                            <!-- /OWL SLIDER -->
                        </div>

                        <div class="blog-item-small-content">
                            <h2>
                                <a href="{{url('home/blog/post/'.$post->id.'/'.str_replace(' ','_',$post->{'post_title_'.App::getLocale()}))}}">"<?php echo $post->{'post_title_' . App::getLocale()} ?></a>
                            </h2>
                            <ul class="blog-post-info list-inline">
                                <li>
                                    <a href="#">
                                        <i class="fa fa-clock-o"></i>
                                        <span class="font-lato">{{date("F d, Y", strtotime($post->created_at))}}</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#">
                                        <i class="fa fa-comment-o"></i>
                                        <span class="font-lato">({{count($post->comments)}}
                                            ) {{trans('home_content.comments')}}</span>
                                    </a>
                                </li>
                            </ul>
                            <p style="width: 300px;height: 38px;" class="">
                                <?php echo substr($post->{'post_content_' . App::getLocale()}, 0, 50) . '...' ?>
                            </p>

                            <a href="{{url('home/blog/post/'.$post->id.'/'.str_replace(' ','_',$post->{'post_title_'.App::getLocale()}))}}"
                               class="btn btn-reveal btn-default">
                                <i class="fa fa-plus"></i><span>{{trans('home_content.chech_more')}}</span>
                            </a>
                        </div>
                    </div>
                @endforeach

            @endif


        </div>
    </section>



@endsection













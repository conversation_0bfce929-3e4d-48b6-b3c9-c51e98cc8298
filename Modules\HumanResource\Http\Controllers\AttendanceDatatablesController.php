<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Attendance;
use App\Role;
use App\Employee;

use Carbon\Carbon;
use DB;
use App\Permission;
use App\EmployeeSalary;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\EmployeeTimetable;
use App\Http\Controllers\Controller;


class AttendanceDatatablesController extends Controller
{

    public function getDateBasedAttendanceTransactions(Request $request,$id,$date)
    {

        if ($request->ajax()) {





                $trxDatatables = Attendance::where("employee_id",$id)->where(DB::raw("date(clock)"),$date)
                    ->select(\DB::raw('date(clock) attDate,clock,type,employee_id,id,note,created_at'))->get();







                return \Yajra\DataTables\DataTables::of($trxDatatables)
                    ->addIndexColumn()
                    ->addColumn("type",function($record){
                        $iconType = $record->type == 'in' ? "download" : "upload";

                        return '<div class="ui dropdown">
  '.$record->type.'<i class="dropdown icon"></i>
  
</div>';


                    })
                    ->addColumn("time",function($record){

                      return  '<div class="one field">
                                            <div class="field">
                                          
                                                <input readonly="readonly" name="timeIn" id="timeIn" class=" existingAttendanceRecordFlatpickr form-control flatpickr flatpickr-input active" data-inverted="" data-position="top left"  data-tooltip="' . Carbon::parse($record->clock)->format('g:i:s l jS F Y') . '" type="text" value="'.Carbon::parse($record->clock)->format('g:i A').'"
                                                       placeholder="Select Time In" readonly="readonly">
                                            </div>

                                        </div>';
//                        return '<input type="text"  class=" form-control" value="'.Carbon::parse($record->clock)->toTimeString().'">';



                    })
                    ->addColumn('action', function ($record) {



                        if($record->type == 'in'){
                            $noteBtnText = (is_null($record->note) == true || empty($record->note== true )) == true ? "Add Note" : "Update Note";
                            $noteDataTarget = (is_null($record->note) == true || empty($record->note== true )) == true  ? "#addNoteIn" : "#updateNoteIn" ;
//                            $noteModalTriggerBtn = ( is_null($record->note) == true || empty($record->note== true )) == true ? "addNoteToAttendanceModalTriggerBtnIn" : "updateaddNoteToAttendanceModalTriggerBtnIn" ;
                            $noteModalTriggerBtnClass = ( is_null($record->note) == true || empty($record->note== true )) == true ? "addNoteToAttendanceModalTriggerBtnIn" : "updateaddNoteToAttendanceModalTriggerBtnIn" ;

                            $str = '</div><br>';
                            if (\Auth::user()->can('update attendance')) {
                                $btns = '<div class="mini ui buttons">
                      
                     
                       <button class=" ui default button updateAttendancePairModalTriggerBtnIn" id="updateAttendancePairModalTriggerBtnIn" data-date="'.$record->attDate.'" data-testing="'.next($record).'" data-atttype="'.$record->type.'"  data-id="'.$record->id.'"  data-toggle = "modal"
                                                                                                        data-target = "#updateAttendanceModalIn" >update</button>
                                                                                                        <div class="or"></div>
                        <button class=" ui negative button deleteModalTriggerBtnIn" id="deleteIndividualAttendanceModalTriggerBtnIn" data-toggle = "modal" data-atttype="'.$record->type.'"  data-id="'.$record->id.'"
                                                                                                        data-target = "#deleteAttendanceConfirmationModalIn" >Delete</button>
                        
                        </div>';

                            }




//
                            return $str . $btns;

                        }
                        else{

                            $noteBtnText = (is_null($record->note) == true || empty($record->note== true )) == true ? "Add Note" : "Update Note";
                            $noteDataTarget = (is_null($record->note) == true || empty($record->note== true )) == true  ? "#addNoteOut" : "#updateNoteOut" ;
                            $noteModalTriggerBtn = ( is_null($record->note) == true || empty($record->note== true )) == true ? "addNoteToAttendanceModalTriggerBtnOut" : "updateaddNoteToAttendanceModalTriggerBtnOut" ;
                            $noteModalTriggerBtnClass = ( is_null($record->note) == true || empty($record->note== true )) == true ? "addNoteToAttendanceModalTriggerBtnOut" : "updateaddNoteToAttendanceModalTriggerBtnOut" ;

                            $str = '</div><br>';
                            if (\Auth::user()->can('update attendance')) {
                                $btns = '<div class="mini ui buttons">
                      
                     
                       <button class=" ui default button updateAttendancePairModalTriggerBtnOut" id="updateAttendancePairModalTriggerBtnOut" data-date="'.$record->attDate.'" data-testing="'.next($record).'" data-atttype="'.$record->type.'"  data-id="'.$record->id.'"  data-toggle = "modal"
                                                                                                        data-target = "#updateAttendanceModalOut" >update</button>
                                                                                                        <div class="or"></div>
                        <button class=" ui negative button deleteModalTriggerBtnOut" id="deleteIndividualAttendanceModalTriggerBtnOut" data-toggle = "modal" data-atttype="'.$record->type.'"  data-id="'.$record->id.'"
                                                                                                        data-target = "#deleteAttendanceConfirmationModalOut" >Delete</button>
                        
                        </div>';

                            }




//
                            return $str . $btns;

                        }
                    })
                   ->rawColumns(['time','type','action'])
                    ->toJson();
//            }

        }


    }
    public function getAttendanceReport(Request $request,$id)
    {

        if ($request->ajax()) {


//            if ($request->has('months') or $request->has('year') or $request->has('days')) {
//
//                if ($months = $request->months and isset($request->months)) {
//                    $arr = join(",", $request->months);
//                    $bindings['roles'] = $months;
//                    $requestedMonths = " AND month(clock) IN (" . $arr . ")";
//
//                    $monthCondition = " AND month(clock) =  " . "'" . $requestedMonths . "'";
//
//                }
//
//                if ($days = $request->days and isset($request->days)) {
//
//                    $arr = join(",", $request->days);
//                    $DaysCondition = " AND day(clock) IN (" . $arr . ")";
//                    $bindings['days'] = $days;
//
//
//                }
//
//
//                if ($year = $request->year and isset($request->year)) {
//                    $bindings['gender'] = $year;
//                    $yearCondition = " AND year(clock) = " . "'" . $request->year . "'";
//                }
//
//
//                $trxDatatables = DB::select(
//                    'SELECT * from attendances where organization_id = 148 and employee_id = 48
//                                            ' . $yearCondition . '
//                                            ' . $DaysCondition . '
//                                            ' . $monthCondition);
//
//
//                return \Yajra\DataTables\DataTables::of($trxDatatables)
//                    ->addIndexColumn()
//                    ->make(true);
//
//
//            }
//
//            else {

            $trxDatatables = Attendance::where("employee_id",$id)->get();






            return \Yajra\DataTables\DataTables::of($trxDatatables)
                ->addIndexColumn()
                ->toJson();
//            }

        }


    }
}
<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * CommandScheduleExecution Entity
 * 
 * Tracks execution history and status for scheduled commands
 * 
 * @property int $id
 * @property int|null $schedule_rule_id
 * @property string $command
 * @property \Illuminate\Support\Carbon $started_at
 * @property \Illuminate\Support\Carbon|null $finished_at
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property string $status
 * @property int|null $exit_code
 * @property string|null $output
 * @property string|null $error_output
 * @property int|null $execution_time_seconds
 * @property int|null $duration_seconds
 * @property float|null $memory_usage_mb
 * @property int|null $jobs_fetched
 * @property array|null $jobs_by_category
 * @property string|null $error_type
 * @property array|null $error_details
 * @property string|null $error_message
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read CommandScheduleRule|null $scheduleRule
 * @property-read CommandScheduleRule|null $commandSchedule
 * @property-read string $status_badge
 * @property-read string $duration_human
 * @property-read bool $is_running
 * @property-read bool $is_completed
 * @property-read bool $is_failed
 * @property-read bool $is_timeout
 */
final class CommandScheduleExecution extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'command_schedule_executions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'schedule_rule_id',
        'command',
        'started_at',
        'finished_at',
        'completed_at',
        'status',
        'exit_code',
        'output',
        'error_output',
        'execution_time_seconds',
        'duration_seconds',
        'memory_usage_mb',
        'jobs_fetched',
        'jobs_by_category',
        'error_type',
        'error_details',
        'error_message',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'schedule_rule_id' => 'integer',
        'started_at' => 'datetime',
        'finished_at' => 'datetime',
        'completed_at' => 'datetime',
        'exit_code' => 'integer',
        'execution_time_seconds' => 'integer',
        'duration_seconds' => 'integer',
        'memory_usage_mb' => 'decimal:2',
        'jobs_fetched' => 'integer',
        'jobs_by_category' => 'array',
        'error_details' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Status constants
     */
    public const STATUS_RUNNING = 'running';
    public const STATUS_COMPLETED = 'completed';
    public const STATUS_FAILED = 'failed';
    public const STATUS_TIMEOUT = 'timeout';

    /**
     * Error type constants
     */
    public const ERROR_TYPE_NONE = 'none';
    public const ERROR_TYPE_NETWORK = 'network';
    public const ERROR_TYPE_API = 'api';
    public const ERROR_TYPE_TIMEOUT = 'timeout';
    public const ERROR_TYPE_DATA = 'data';
    public const ERROR_TYPE_UNKNOWN = 'unknown';

    /**
     * Get the schedule rule that owns this execution
     *
     * @return BelongsTo
     */
    public function scheduleRule(): BelongsTo
    {
        return $this->belongsTo(CommandScheduleRule::class, 'schedule_rule_id');
    }

    /**
     * Alias for scheduleRule for backward compatibility
     *
     * @return BelongsTo
     */
    public function commandSchedule(): BelongsTo
    {
        return $this->scheduleRule();
    }

    /**
     * Get status badge HTML
     *
     * @return string
     */
    public function getStatusBadgeAttribute(): string
    {
        switch ($this->status) {
            case self::STATUS_RUNNING:
                return '<span class="badge bg-primary">Running</span>';
            case self::STATUS_COMPLETED:
                return '<span class="badge bg-success">Completed</span>';
            case self::STATUS_FAILED:
                return '<span class="badge bg-danger">Failed</span>';
            case self::STATUS_TIMEOUT:
                return '<span class="badge bg-warning">Timeout</span>';
            default:
                return '<span class="badge bg-secondary">Unknown</span>';
        }
    }

    /**
     * Get human-readable duration
     *
     * @return string
     */
    public function getDurationHumanAttribute(): string
    {
        if ($this->execution_time_seconds === null) {
            if ($this->status === self::STATUS_RUNNING) {
                $elapsed = Carbon::now()->diffInSeconds($this->started_at);
                return $this->formatDuration($elapsed) . ' (running)';
            }
            return 'Unknown';
        }

        return $this->formatDuration($this->execution_time_seconds);
    }

    /**
     * Check if execution is currently running
     *
     * @return bool
     */
    public function getIsRunningAttribute(): bool
    {
        return $this->status === self::STATUS_RUNNING;
    }

    /**
     * Check if execution completed successfully
     *
     * @return bool
     */
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if execution failed
     *
     * @return bool
     */
    public function getIsFailedAttribute(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Check if execution timed out
     *
     * @return bool
     */
    public function getIsTimeoutAttribute(): bool
    {
        return $this->status === self::STATUS_TIMEOUT;
    }

    /**
     * Get jobs fetched with fallback to 0
     *
     * @return int
     */
    public function getJobsFetchedAttribute(): int
    {
        return $this->attributes['jobs_fetched'] ?? 0;
    }

    /**
     * Get jobs by category with fallback to empty array
     *
     * @return array
     */
    public function getJobsByCategoryAttribute(): array
    {
        return $this->jobs_by_category ?? [];
    }

    /**
     * Get error type badge HTML
     *
     * @return string
     */
    public function getErrorTypeBadgeAttribute(): string
    {
        switch ($this->error_type) {
            case self::ERROR_TYPE_NONE:
                return '<span class="badge bg-success">No Errors</span>';
            case self::ERROR_TYPE_NETWORK:
                return '<span class="badge bg-warning">Network</span>';
            case self::ERROR_TYPE_API:
                return '<span class="badge bg-danger">API Error</span>';
            case self::ERROR_TYPE_TIMEOUT:
                return '<span class="badge bg-warning">Timeout</span>';
            case self::ERROR_TYPE_DATA:
                return '<span class="badge bg-info">Data Error</span>';
            case self::ERROR_TYPE_UNKNOWN:
            default:
                return '<span class="badge bg-secondary">Unknown</span>';
        }
    }

    /**
     * Check if execution has errors
     *
     * @return bool
     */
    public function getHasErrorsAttribute(): bool
    {
        return $this->error_type !== self::ERROR_TYPE_NONE;
    }

    /**
     * Get total jobs by category count
     *
     * @return int
     */
    public function getTotalJobsByCategoryAttribute(): int
    {
        return array_sum($this->jobs_by_category);
    }

    /**
     * Format duration in seconds to human readable format
     *
     * @param int $seconds
     * @return string
     */
    private function formatDuration(int $seconds): string
    {
        if ($seconds < 60) {
            return "{$seconds}s";
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return "{$minutes}m {$remainingSeconds}s";
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            $remainingSeconds = $seconds % 60;
            return "{$hours}h {$minutes}m {$remainingSeconds}s";
        }
    }

    /**
     * Mark execution as started
     *
     * @param int $scheduleRuleId
     * @param string $command
     * @return static
     */
    public static function markStarted(int $scheduleRuleId, string $command): self
    {
        $execution = static::create([
            'schedule_rule_id' => $scheduleRuleId,
            'command' => $command,
            'started_at' => Carbon::now(),
            'status' => self::STATUS_RUNNING,
        ]);

        Log::info('CommandScheduleExecution: Execution started', [
            'execution_id' => $execution->id,
            'schedule_rule_id' => $scheduleRuleId,
            'command' => $command,
            'started_at' => $execution->started_at->toDateTimeString()
        ]);

        return $execution;
    }

    /**
     * Mark execution as completed
     *
     * @param int $exitCode
     * @param string|null $output
     * @param float|null $memoryUsageMb
     * @param int|null $jobsFetched
     * @param array|null $jobsByCategory
     * @param string|null $errorType
     * @param array|null $errorDetails
     * @return bool
     */
    public function markCompleted(
        int $exitCode = 0,
        ?string $output = null,
        ?float $memoryUsageMb = null,
        ?int $jobsFetched = null,
        ?array $jobsByCategory = null,
        ?string $errorType = null,
        ?array $errorDetails = null
    ): bool {
        $completedAt = Carbon::now();
        $executionTime = $completedAt->diffInSeconds($this->started_at);

        $updateData = [
            'completed_at' => $completedAt,
            'status' => self::STATUS_COMPLETED,
            'exit_code' => $exitCode,
            'output' => $output,
            'execution_time_seconds' => $executionTime,
            'memory_usage_mb' => $memoryUsageMb,
        ];

        // Add health tracking data if provided
        if ($jobsFetched !== null) {
            $updateData['jobs_fetched'] = $jobsFetched;
        }
        if ($jobsByCategory !== null) {
            $updateData['jobs_by_category'] = $jobsByCategory;
        }
        if ($errorType !== null) {
            $updateData['error_type'] = $errorType;
        } else {
            $updateData['error_type'] = self::ERROR_TYPE_NONE;
        }
        if ($errorDetails !== null) {
            $updateData['error_details'] = $errorDetails;
        }

        $this->update($updateData);

        Log::info('CommandScheduleExecution: Execution completed with health metrics', [
            'execution_id' => $this->id,
            'schedule_rule_id' => $this->schedule_rule_id,
            'command' => $this->command,
            'completed_at' => $completedAt->toDateTimeString(),
            'execution_time_seconds' => $executionTime,
            'exit_code' => $exitCode,
            'memory_usage_mb' => $memoryUsageMb,
            'jobs_fetched' => $jobsFetched,
            'jobs_by_category_count' => $jobsByCategory ? array_sum($jobsByCategory) : null,
            'error_type' => $errorType ?? self::ERROR_TYPE_NONE,
            'has_error_details' => $errorDetails !== null
        ]);

        return true;
    }

    /**
     * Mark execution as failed
     *
     * @param int $exitCode
     * @param string|null $errorOutput
     * @param string|null $output
     * @param float|null $memoryUsageMb
     * @return bool
     */
    public function markFailed(int $exitCode = 1, ?string $errorOutput = null, ?string $output = null, ?float $memoryUsageMb = null): bool
    {
        $completedAt = Carbon::now();
        $executionTime = $completedAt->diffInSeconds($this->started_at);

        $this->update([
            'completed_at' => $completedAt,
            'status' => self::STATUS_FAILED,
            'exit_code' => $exitCode,
            'output' => $output,
            'error_output' => $errorOutput,
            'execution_time_seconds' => $executionTime,
            'memory_usage_mb' => $memoryUsageMb,
        ]);

        Log::error('CommandScheduleExecution: Execution failed', [
            'execution_id' => $this->id,
            'schedule_rule_id' => $this->schedule_rule_id,
            'command' => $this->command,
            'completed_at' => $completedAt->toDateTimeString(),
            'execution_time_seconds' => $executionTime,
            'exit_code' => $exitCode,
            'error_output' => $errorOutput,
            'memory_usage_mb' => $memoryUsageMb
        ]);

        return true;
    }

    /**
     * Mark execution as timed out
     *
     * @param string|null $output
     * @param float|null $memoryUsageMb
     * @return bool
     */
    public function markTimeout(?string $output = null, ?float $memoryUsageMb = null): bool
    {
        $completedAt = Carbon::now();
        $executionTime = $completedAt->diffInSeconds($this->started_at);

        $this->update([
            'completed_at' => $completedAt,
            'status' => self::STATUS_TIMEOUT,
            'exit_code' => 124, // Standard timeout exit code
            'output' => $output,
            'error_output' => 'Command execution timed out',
            'execution_time_seconds' => $executionTime,
            'memory_usage_mb' => $memoryUsageMb,
        ]);

        Log::warning('CommandScheduleExecution: Execution timed out', [
            'execution_id' => $this->id,
            'schedule_rule_id' => $this->schedule_rule_id,
            'command' => $this->command,
            'completed_at' => $completedAt->toDateTimeString(),
            'execution_time_seconds' => $executionTime,
            'memory_usage_mb' => $memoryUsageMb
        ]);

        return true;
    }

    /**
     * Get recent executions for a command
     *
     * @param string $command
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getRecentForCommand(string $command, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('command', $command)
            ->orderBy('started_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get running executions
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getRunning(): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('status', self::STATUS_RUNNING)
            ->orderBy('started_at', 'asc')
            ->get();
    }

    /**
     * Get executions that have been running for too long (potential zombies)
     *
     * @param int $maxRuntimeMinutes
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getZombieExecutions(int $maxRuntimeMinutes = 120): \Illuminate\Database\Eloquent\Collection
    {
        $cutoffTime = Carbon::now()->subMinutes($maxRuntimeMinutes);
        
        return static::where('status', self::STATUS_RUNNING)
            ->where('started_at', '<', $cutoffTime)
            ->get();
    }

    /**
     * Clean up old execution records
     * TODO: Implement automatic cleanup job for records older than 48 hours
     *
     * @param int $hoursOld
     * @return int Number of deleted records
     */
    public static function cleanupOldExecutions(int $hoursOld = 48): int
    {
        $cutoffTime = Carbon::now()->subHours($hoursOld);
        
        $deletedCount = static::where('created_at', '<', $cutoffTime)
            ->whereIn('status', [self::STATUS_COMPLETED, self::STATUS_FAILED, self::STATUS_TIMEOUT])
            ->delete();

        if ($deletedCount > 0) {
            Log::info('CommandScheduleExecution: Cleaned up old execution records', [
                'deleted_count' => $deletedCount,
                'cutoff_time' => $cutoffTime->toDateTimeString()
            ]);
        }

        return $deletedCount;
    }

    /**
     * Get execution statistics for a command
     *
     * @param string $command
     * @param int $days
     * @return array
     */
    public static function getCommandStats(string $command, int $days = 7): array
    {
        $since = Carbon::now()->subDays($days);
        
        $executions = static::where('command', $command)
            ->where('started_at', '>=', $since)
            ->get();

        $total = $executions->count();
        $completed = $executions->where('status', self::STATUS_COMPLETED)->count();
        $failed = $executions->where('status', self::STATUS_FAILED)->count();
        $timeout = $executions->where('status', self::STATUS_TIMEOUT)->count();
        $running = $executions->where('status', self::STATUS_RUNNING)->count();

        $avgDuration = $executions->whereNotNull('execution_time_seconds')
            ->avg('execution_time_seconds');

        return [
            'total_executions' => $total,
            'completed' => $completed,
            'failed' => $failed,
            'timeout' => $timeout,
            'running' => $running,
            'success_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
            'average_duration_seconds' => $avgDuration ? round($avgDuration, 2) : null,
            'period_days' => $days,
            'since' => $since->toDateTimeString()
        ];
    }

    /**
     * Get health statistics for dashboard
     *
     * @param string|null $command
     * @param string $timeRange
     * @return array
     */
    public static function getHealthStats(?string $command = null, string $timeRange = '7d'): array
    {
        // Parse time range
        $since = match ($timeRange) {
            '24h' => Carbon::now()->subHours(24),
            '7d' => Carbon::now()->subDays(7),
            '30d' => Carbon::now()->subDays(30),
            default => Carbon::now()->subDays(7)
        };

        $query = static::where('started_at', '>=', $since);
        
        if ($command) {
            $query->where('command', $command);
        }

        $executions = $query->orderBy('started_at', 'asc')->get();

        $total = $executions->count();
        $successful = $executions->where('status', self::STATUS_COMPLETED)->count();
        $failed = $executions->whereIn('status', [self::STATUS_FAILED, self::STATUS_TIMEOUT])->count();
        $timeouts = $executions->where('status', self::STATUS_TIMEOUT)->count();

        // Calculate totals for health metrics
        $totalJobsFetched = $executions->sum('jobs_fetched');
        $avgDuration = $executions->whereNotNull('execution_time_seconds')->avg('execution_time_seconds');
        $avgMemoryUsage = $executions->whereNotNull('memory_usage_mb')->avg('memory_usage_mb');

        // Aggregate jobs by category
        $categoryTotals = static::aggregateJobsByCategory($executions);

        // Error breakdown
        $errorBreakdown = $executions->groupBy('error_type')->map(function ($group) {
            return $group->count();
        })->toArray();

        return [
            'summary' => [
                'total_executions' => $total,
                'successful' => $successful,
                'failed' => $failed,
                'timeouts' => $timeouts,
                'success_rate' => $total > 0 ? round(($successful / $total) * 100, 2) : 0,
            ],
            'metrics' => [
                'total_jobs_fetched' => $totalJobsFetched,
                'average_duration_seconds' => $avgDuration ? round($avgDuration, 2) : null,
                'average_memory_mb' => $avgMemoryUsage ? round($avgMemoryUsage, 2) : null,
            ],
            'jobs_by_category' => $categoryTotals,
            'error_breakdown' => $errorBreakdown,
            'period' => [
                'range' => $timeRange,
                'since' => $since->toDateTimeString(),
                'until' => Carbon::now()->toDateTimeString(),
            ]
        ];
    }

    /**
     * Get recent executions for health dashboard
     *
     * @param string $timeRange
     * @param array|null $providers
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getRecentForHealthDashboard(string $timeRange = '7d', ?array $providers = null): \Illuminate\Database\Eloquent\Collection
    {
        // Parse time range
        $since = match ($timeRange) {
            '24h' => Carbon::now()->subHours(24),
            '7d' => Carbon::now()->subDays(7),
            '30d' => Carbon::now()->subDays(30),
            default => Carbon::now()->subDays(7)
        };

        $query = static::where('started_at', '>=', $since)
            ->with('scheduleRule');

        // Filter by providers if specified
        if ($providers && !empty($providers)) {
            $commandFilters = [];
            if (in_array('Jobs.af', $providers)) {
                $commandFilters[] = 'jobseeker:sync-jobs-af';
            }
            if (in_array('ACBAR', $providers)) {
                $commandFilters[] = 'jobseeker:sync-acbar-jobs';
            }
            
            if (!empty($commandFilters)) {
                $query->whereIn('command', $commandFilters);
            }
        }

        return $query->orderBy('started_at', 'asc')->get();
    }

    /**
     * Check if there are consecutive failures
     *
     * @param string $command
     * @param int $consecutiveCount
     * @return bool
     */
    public static function hasConsecutiveFailures(string $command, int $consecutiveCount = 3): bool
    {
        $recentExecutions = static::where('command', $command)
            ->orderBy('started_at', 'desc')
            ->limit($consecutiveCount)
            ->get();

        if ($recentExecutions->count() < $consecutiveCount) {
            return false;
        }

        return $recentExecutions->every(function ($execution) {
            return in_array($execution->status, [self::STATUS_FAILED, self::STATUS_TIMEOUT]);
        });
    }

    /**
     * Check if there are consecutive zero job runs
     *
     * @param string $command
     * @param int $consecutiveCount
     * @return bool
     */
    public static function hasConsecutiveZeroJobs(string $command, int $consecutiveCount = 3): bool
    {
        $recentExecutions = static::where('command', $command)
            ->where('status', self::STATUS_COMPLETED)
            ->orderBy('started_at', 'desc')
            ->limit($consecutiveCount)
            ->get();

        if ($recentExecutions->count() < $consecutiveCount) {
            return false;
        }

        return $recentExecutions->every(function ($execution) {
            return ($execution->jobs_fetched ?? 0) === 0;
        });
    }

    /**
     * Aggregate jobs by category from multiple executions
     *
     * @param \Illuminate\Database\Eloquent\Collection $executions
     * @return array
     */
    private static function aggregateJobsByCategory(\Illuminate\Database\Eloquent\Collection $executions): array
    {
        $categoryTotals = [];

        foreach ($executions as $execution) {
            $jobsByCategory = $execution->jobs_by_category ?? [];
            
            foreach ($jobsByCategory as $category => $count) {
                if (!isset($categoryTotals[$category])) {
                    $categoryTotals[$category] = 0;
                }
                $categoryTotals[$category] += $count;
            }
        }

        return $categoryTotals;
    }
} 
<?php

namespace Modules\Admission\Http\Controllers;

use App\Admission;
use App\AdmissionInterview;
use App\Http\Controllers\Controller;
use App\StudentAdmissionHefzPlan;
use App\StudentHefzPlan;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;
use App\Student;
use Tests\Psalm\LaravelPlugin\Models\Car;

class AdmissionHefzPlanController extends Controller
{

    public function __invoke(Request $request)
    {


        try {
            DB::beginTransaction();
            $planYearMonth = $request->start_date;
            $dateMonthArray = explode('-', $planYearMonth);
            $year = $dateMonthArray[0];
            $month = $dateMonthArray[1];
            $planYearMonth = Carbon::createFromDate($year, $month, 1);
            $planYearMonth = $planYearMonth->format('Y-m');



            StudentAdmissionHefzPlan::updateOrInsert(     [
                'student_id' => $request->student_id,
                'class_id'   => $request->class_id],
                [
                    "plan_year_and_month" => $planYearMonth,
                    "status" => 'active',
                    "class_id" => $request->class_id,
                    "student_id" => $request->student_id,
                    "study_direction" => $request->study_direction,
                    "start_date" => Carbon::parse($request->start_date)->toDateString(),
                    "num_to_memorize" => $request->num_to_memorize,
                    "memorization_mood" => $request->memorization_mood,
                    "pages_to_revise" => $request->pages_to_revise,
                    "supervisor_comment" => $request->get('note'),
                    "organization_id" => config("organization_id"),
                    "created_by" => auth()->user()->id,
                    "created_at" => Carbon::now(),
                    "level_id" => $request->level
            ]);
            // update AdmissionInterview status

            $admissionId = Admission::where('student_id', $request->student_id)->first()->id;
            AdmissionInterview::where('admission_id', $admissionId)->update(
                [
                    'status' => 'interviewed',
                    'confirmed_at' => Carbon::now(),
                    'updated_by' => auth()->user()->id
                ]);

            DB::commit();

            Toastr::success('Student Memorization Plan Approved !', 'Success');

            return redirect()->back();

        } catch (\Exception $e) {

            DB::rollback();

            Toastr::warning($e->getMessage(), 'Danger');
            return redirect()->back();
        }
    }

}

<?php

use <PERSON><PERSON>les\General\Http\Controllers\CenterClassStudentsBackupController;
use Modules\Education\Http\Controllers\IjazasanadMemorizationMonthlyPlanController;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\MenuController;
use Illuminate\Support\Facades\Route;
use Modules\General\Http\Controllers\JobsController;



Route::group(['middleware' => ['web' , 'auth:employee','missedClockOutMiddleware', \App\Http\Middleware\SystemViewerDataAccess::class], 'prefix' => 'workplace', 'namespace' => 'Modules\General\Http\Controllers'], function () {
    // Route::get('logs', '\Rap2hpoutre\LaravelLogViewer\LogViewerController@index');








    // Sidebar Settings Routes
    Route::group(['middleware' => ['permission:manage-sidebar-settings']], function () {
        Route::get('/sidebar-settings', 'SidebarSettingsController@index')->name('sidebar.settings');
        Route::post('/sidebar-settings', 'SidebarSettingsController@update')->name('sidebar.settings.update');
        Route::get('/sidebar-settings/reset', 'SidebarSettingsController@reset')->name('sidebar.settings.reset');
    });

    // API route for sidebar settings - available to all authenticated users
    Route::group(['prefix' => 'workplace'], function () {
        Route::get('/api/sidebar-settings', '\App\Http\Controllers\Api\SidebarSettingsApiController@getSettings')->name('api.sidebar.settings');
    });








    Route::get('/controller-action-statistics', 'ControllerActionStatisticsController@index');
    Route::get('/last-seven-days-attendance', [\Modules\General\Http\Controllers\AttendanceController::class, 'lastSevenDaysAttendance'])->name('lastSevenDaysAttendance');
    Route::get('/current-month-employee-attendance', [\Modules\General\Http\Controllers\AttendanceController::class, 'currentMonthAttendance'])->name('currentMonthEmployeeAttendance');
    Route::get('/today-employee-attendance', [\Modules\General\Http\Controllers\AttendanceController::class, 'todayAttendance'])->name('todayEmployeeAttendance');

    Route::get('/dashboard/students/hefz-plans-need-approval', 'HefzPlanDatatablesController@getPlansNeedApproval');
    Route::get('/dashboard/students/ijazasanad-memorization-plans-need-approval', 'IjazasanadMemorizationPlanDatatablesController@getPlansNeedApproval');
    Route::get('/dashboard/students/show-archived-students', 'ShowArchivedhStudentsDatatablesController');
    Route::get('/dashboard/students/show-students-awaiting-approvals', 'ShowStudentsAwaitingApprovalsDatatablesController');
    Route::get('/dashboard/students/show-halaqah-students/{existing_class_id?}', 'ShowHalaqahStudentsDatatablesController')
        ->name('show-halaqah-students');

    Route::get('/dashboard/students/show-non-halaqah-students', 'ShowNonHalaqahStudentsDatatablesController');
    Route::put('/dashboard/students/add-non-halaqah-student-to-another-halaqah', 'AddNonHalaqahStudenttoAnotherHalahController')->name('add-non-halaqah-student-to-another-halaqah');
    Route::put('/dashboard/students/add-halaqah-student-to-another-halaqah', 'AddHalaqahStudenttoAnotherHalahController')->name('add-halaqah-student-to-another-halaqah');
    Route::match(['put','post'],'/dashboard/students/add-archived-student-to-halaqah', 'AddArchivedStudenttoHalaqahController')->name('add-archived-student-to-halaqah');
    Route::match(['put','post'],'/dashboard/students/add-new-student-to-halaqah', 'AddNewStudenttoHalaqahController')->name('add-new-student-to-halaqah');
    Route::get('/dashboard/students/revision-plans-need-approval', 'RevisionPlanDatatablesController@getPlansNeedApproval');
    Route::get('/dashboard/students/ijazasanad-revision-plans-need-approval', 'IjazasanadRevisionPlanDatatablesController@getPlansNeedApproval');
    Route::get('/dashboard/students/nouranya-plans-need-approval', 'NouranyaPlanDatatablesController@getPlansNeedApproval');
    Route::get('/dashboard/students/ijazasanadlevel1-plans-need-approval', 'IjazasanadLevel1PlanDatatablesController@getPlansNeedApproval');
    Route::put('/dashboard/students/approve-monthly-hefz-plan', 'ApproveHefzPlanController')->name('general.dashboard.approve_hefz_plan')->middleware('permission:approve_hefz_plan');
    Route::get('nouranyaplan-based-classes', 'NouranyaPlanController@getNouranyaBasedClasses')->name('nouranyaplan-based-classes');
    Route::get('ijazasanadplan-based-classes', [IjazasanadMemorizationMonthlyPlanController::class, 'getIjazasanadBasedClasses'])->name('ijazasanadplan-based-classes');

    Route::get('/dashboard/employees/missed-clockouts', 'MissedClockoutsDatatablesController');
    Route::get('/dashboard/employees/missed-clockouts-list', 'MissedClockoutsListController')->name('missed.clockout.list');
    Route::get('/dashboard/individual-employee/missed-clockouts', 'IndividualEmployeeMissedClockoutsDatatablesController');
    Route::get('/dashboard/employee/missed-clockouts-list/{id?}/{yearMonth?}/', 'IndividualEmployeeMissedClockoutsListController')->name('individual.employee.missed.clockout.list');


    Route::put('/dashboard/students/approve-monthly-revision-plan', 'ApproveRevisionPlanController')->name('general.dashboard.approve_revision_plan')->middleware('permission:approve_revision_plan');
    Route::put('/dashboard/students/approve-individual-revision-plan', 'ApproveIndividualRevisionPlanController')->name('general.dashboard.approve_individual_revision_plan')->middleware('permission:approve_revision_plan');
    Route::post('/dashboard/students/add-revision-plan-comment', 'AddRevisionPlanCommentController')->name('general.dashboard.add_revision_plan_comment')->middleware('permission:approve_revision_plan');
    Route::put('/dashboard/students/approve-ijazasanad-monthly-memorization-plan', 'ApproveIjazasanadMemorizationPlanController')->name('general.dashboard.approve_ijazasanad_memorization_plan')->middleware('permission:approve_ijazasanad_plan');
    Route::put('/dashboard/students/approve-ijazasanad-monthly-revision-plan', 'ApproveIjazasanadRevisionPlanController')->name('general.dashboard.approve_ijazasanad_revision_plan')->middleware('permission:approve_ijazasanad_plan');
    Route::put('/dashboard/students/approve-monthly-nouranya-plan', 'ApproveNouranyaPlanController')->name('general.dashboard.approve_nouranya_plan')->middleware('permission:approve_nouranya_plan');
    Route::put('/dashboard/students/approve-individual-nouranya-plan', 'ApproveIndividualNouranyaPlanController')->name('general.dashboard.approve_individual_nouranya_plan')->middleware('permission:approve_nouranya_plan');
    Route::put('/dashboard/students/approve-individual-ijazasanad-level1-plan', 'ApproveIndividualIjazasanadLevel1PlanController')->name('general.dashboard.approve_individual_ijazasanad_level1_plan')->middleware('permission:approve_ijazasanad_plan');
    Route::put('/dashboard/students/approve-monthly-ijazasanad-level1-plan', 'ApproveIjazasanadLevel1PlanController')->name('general.dashboard.approve_ijazasanad_level1_plan')->middleware('permission:approve_ijazasanad_plan');
    Route::get('/dashboard/students/interviews-not-confirmed-yet', 'UnconfirmedStudentInterviewsDatatablesController');
    Route::get('/dashboard/students/students-with-no-study-plans', 'StudentsWithNoStudyPlansDatatablesController');
    Route::get('/dashboard/students/application-waiting-approval', 'GeneralDatatablesController@getApplicationsWaitingApprovals');
    Route::get('/dashboard/employee/missed-clockouts', 'GeneralDatatablesController@getMissedClockOuts');
    Route::get('/dashboard', 'GeneralController@index')->name('employee.dashboard');
    Route::get('/dashboard/fetch/widgets/counts', 'GeneralDashboardWidgetCounterController')->name('general_dashboad.fetch.widget.counts');
    Route::get('/supervisor-dashboard/fetch/widgets/counts', 'SupervisorDashboardWidgetCounterController')->name('supervisor_teacher_dashboad.fetch.widget.counts');
    Route::get('/dashboard/fetch/last-attendance-status', 'WorkplaceController@lastAttendanceStatus')->name('workplace.fetch.lastAttendanceStatus');
    Route::get('/dashboard/fetch/extra-worked-hours', 'WorkplaceController@extraWorkedHours')->name('workplace.fetch.extraWorkedHours');
    Route::get('/dashboard/fetch/last-out-attendance-time', 'WorkplaceController@lastOutAttendanceTime')->name('workplace.fetch.lastOutAttendanceTime');
    Route::get('/dashboard/fetch/last-attendance-type', 'WorkplaceController@lastAttendanceType')->name('workplace.fetch.lastAttendanceType');
    Route::get('/dashboard/fetch/first-clockin-attendanceoftheday', 'WorkplaceController@firstClockInAttendanceofTheDay')->name('workplace.fetch.firstClockInAttendanceofTheDay');
    Route::get('/dashboard/fetch/organization-name', 'WorkplaceController@getOrganizationName')->name('workplace.fetch.getOrganizationName');

    Route::get('/dashboard/students/quick-hefz-report/class/{id}/{date}/', 'SpecificDateHefzReportDatatablesController@getDetails');
    Route::get('/dashboard/students/quick-revision-report/class/{id}/{date}/', 'SpecificDateRevisionReportDatatablesController@getDetails');
        Route::get('departments', 'DepartmentController@index')->name('departments');

        Route::get('departments/create', 'DepartmentController@create')->name('departments.create');
        Route::post('departments', 'DepartmentController@store')->name('departments.store');
        Route::get('departments/{id}/edit', 'DepartmentController@edit')->name('departments.edit');
        Route::patch('departments/{id}', 'DepartmentController@update')->name('departments.update');
        Route::get('departments/{id}', 'DepartmentController@delete')->name('departments.delete');


        Route::group(['as' => 'general.','prefix' =>'general'], function () {


            // Add this route to your existing routes
            Route::get('/centerclassstudentsbackup/get-center-password', [
                'as' => 'centerclasstudentsbackup.getCenterPassword',
                'uses' => 'CenterClassStudentsBackupController@getCenterPassword'
            ]);

            Route::get('centerclasstudentsbackup/center-passwords', [CenterClassStudentsBackupController::class, 'getCenterPasswords'])
                ->name('centerclasstudentsbackup.getCenterPasswords');

            Route::post('centerclasstudentsbackup/update-center-passwords', [CenterClassStudentsBackupController::class, 'updateCenterPasswords'])
                ->name('centerclasstudentsbackup.updateCenterPassword');



            // Backup delete routes
                    Route::post('centerclasstudentsbackup/delete', [CenterClassStudentsBackupController::class, 'delete'])
                    ->name('centerclasstudentsbackup.delete');
                Route::post('centerclasstudentsbackup/delete-multiple', [CenterClassStudentsBackupController::class, 'deleteMultiple'])
                    ->name('centerclasstudentsbackup.deleteMultiple');

                // Add download route
                Route::post('centerclasstudentsbackup/download', [CenterClassStudentsBackupController::class, 'download'])
                    ->name('centerclasstudentsbackup.download')
                    ->middleware('permission:download students backup');

            Route::post(
                '/centerclassstudentsbackup/preview',
                [CenterClassStudentsBackupController::class, 'previewRestore']
            )->name('centerclasstudentsbackup.previewRestore');
        
            Route::post(
                '/center-class-students-backup/restore-student-data',
                [CenterClassStudentsBackupController::class, 'restoreStudentData']
            )->name('centerclasstudentsbackup.restoreStudentData');

            Route::post(
                '/center-class-students-backup/create-student-data',
                [CenterClassStudentsBackupController::class, 'createStudentDataBackup']
            )->name('centerclasstudentsbackup.createStudentDataBackup');


            Route::get('/backups', [CenterClassStudentsBackupController::class, 'index'])
                ->name('centerclassstudentsbackup.index');
            Route::get('/center-class-students-backup/list', [CenterClassStudentsBackupController::class, 'listBackups'])
                ->name('centerclasstudentsbackup.list');

            // New routes for dynamic dropdowns:
            Route::get('/center-class-students-backup/centers', [CenterClassStudentsBackupController::class, 'getCenters'])
                ->name('centerclasstudentsbackup.getCenters');

            Route::get('/center-class-students-backup/classes', [CenterClassStudentsBackupController::class, 'getClasses'])
                ->name('centerclasstudentsbackup.getClasses');

                Route::get('centerclasstudentsbackup/getBackupDates', [
                    'as' => 'centerclasstudentsbackup.getBackupDates',
                    'uses' => 'CenterClassStudentsBackupController@getBackupDates',
                    // 'middleware' => ['permission:centerclasstudentsbackup-filter']
                ]);
                
                Route::get('centerclasstudentsbackup/getClassesByBackupDate', [
                    'as' => 'centerclasstudentsbackup.getClassesByBackupDate',
                    'uses' => 'CenterClassStudentsBackupController@getClassesByBackupDate',
                    // 'middleware' => ['permission:centerclasstudentsbackup-filter']
                ]); 

            // Add the missing routes here
            Route::get('/center-class-students-backup/centers-with-backups', [CenterClassStudentsBackupController::class, 'getCentersWithBackups'])
                ->name('centerclasstudentsbackup.getCentersWithBackups');

            Route::get('/center-class-students-backup/classes-with-backups', [CenterClassStudentsBackupController::class, 'getClassesWithBackups'])
                ->name('centerclasstudentsbackup.getClassesWithBackups');

            Route::get('/center-class-students-backup/backup-form-classes', [CenterClassStudentsBackupController::class, 'getBackupFormClasses'])
                ->name('centerclasstudentsbackup.getBackupFormClasses');

            Route::get('/center-class-students-backup/statuses', [CenterClassStudentsBackupController::class, 'getBackupStatuses'])
                ->name('centerclasstudentsbackup.getStatuses');




            Route::get('/commands', [\Modules\General\Http\Controllers\CommandsController::class, 'home'])->name('commands.home');


            // Show settings page
            Route::get('/commands/ijazasanad', [\Modules\General\Http\Controllers\CommandsController::class, 'showIjazasanadSettings'])
                ->name('commands.ijazasanad.show');

            Route::get('/commands/nouranya', [\Modules\General\Http\Controllers\NouranyaApprovalNotificationCommandsController::class, 'showNouranyaSettings'])
                ->name('commands.nouranya.show');
            Route::get('/commands/memorizationrevision', [\Modules\General\Http\Controllers\MemorizationRevisionApprovalNotificationCommandsController::class, 'showMemorizationrevisionSettings'])
                ->name('commands.memorization.show');




            // Legacy routes kept for backward compatibility
            Route::post('/commands/ijazasanad/save', [\Modules\General\Http\Controllers\CommandsController::class, 'saveIjazasanadSettings'])
                ->name('commands.ijazasanad.save');

            Route::post('/commands/ijazasanad/exclusions/remove', [\Modules\General\Http\Controllers\CommandsController::class, 'removeExclusion'])
                ->name('commands.ijazasanad.exclusions.remove');

            Route::post('/commands/ijazasanad/resend', [\Modules\General\Http\Controllers\CommandsController::class, 'resendFailedEmail'])
                ->name('commands.ijazasanad.resend');

            // Add new route for manually running the Ijazasanad command
            Route::post('/commands/ijazasanad/run', [\Modules\General\Http\Controllers\CommandsController::class, 'runIjazasanadCommand'])
                ->name('commands.ijazasanad.run');
            
            // Add new route for testing the Ijazasanad command with a specific email
            Route::post('/commands/ijazasanad/test', [\Modules\General\Http\Controllers\CommandsController::class, 'testIjazasanadCommand'])
                ->name('commands.ijazasanad.test');

            // Update all settings at once (preferred method)
            Route::post('/ijazasanad/update', [\Modules\General\Http\Controllers\CommandsController::class, 'updateAll'])
                ->name('commands.ijazasanad.updateAll');
            
            Route::post('/nouranya/update', [\Modules\General\Http\Controllers\NouranyaApprovalNotificationCommandsController::class, 'updateAll'])
                ->name('commands.nouranya.updateAll');
            
            Route::post('/memorization/update', [\Modules\General\Http\Controllers\MemorizationRevisionApprovalNotificationCommandsController::class, 'updateAll'])
                ->name('commands.memorization.updateAll');



            Route::post('/ijazasanad/update', [\Modules\General\Http\Controllers\CommandsController::class, 'updateAll'])->name('commands.ijazasanad.updateAll');
            Route::post('/nouranya/update', [\Modules\General\Http\Controllers\NouranyaApprovalNotificationCommandsController::class, 'updateAll'])->name('commands.nouranya.updateAll');
            Route::post('/memorization/update', [\Modules\General\Http\Controllers\MemorizationRevisionApprovalNotificationCommandsController::class, 'updateAll'])->name('commands.memorization.updateAll');

                //            Route::get('/commands/memorization', [\Modules\General\Http\Controllers\CommandsController::class, 'showMemorization'])->name('commands.memorization.show');
                 //            Route::get('/commands/nouranya', [\Modules\General\Http\Controllers\CommandsController::class, 'showNouranya'])->name('commands.nouranya.show');
            Route::get('/commands/trashClockOut', [\Modules\General\Http\Controllers\CommandsController::class, 'showTrashClockOut'])->name('commands.trashClockOut.show');
            Route::get('/commands/cacheTrashed', [\Modules\General\Http\Controllers\CommandsController::class, 'showCacheTrashed'])->name('commands.cacheTrashed.show');
            Route::get('/commands/attendanceSponsors', [\Modules\General\Http\Controllers\CommandsController::class, 'showAttendanceSponsors'])->name('commands.attendanceSponsors.show');


             //    Route::get('/system-log', [\Modules\General\Http\Controllers\SystemLogController::class, 'index'])->name('system-log.index');
            Route::get('/status', [\Modules\General\Http\Controllers\SystemLogController::class, 'index'])->name('system-log.index');
            Route::post('/system-log/clear', [\Modules\General\Http\Controllers\SystemLogController::class, 'clear'])->name('system-log.clear');
            Route::get('/system-log/refresh', [\Modules\General\Http\Controllers\SystemLogController::class, 'refresh'])->name('system-log.refresh');


            Route::get('roles', 'RoleController@index')->name('roles.index')->middleware('permission:access roles');
            Route::post('roles', 'RoleController@store')->name('roles.store')->middleware('permission:add role');
            Route::get('roles/create', 'RoleController@create')->name('roles.create')->middleware('permission:');;
            Route::get('roles/{id}', 'RoleController@show')->name('roles.show')->middleware('permission:show role');
            Route::get('roles/{id}/edit', 'RoleController@edit')->name('roles.edit')->middleware('permission:show role edit form');
            Route::match(['put','patch'],'roles/{id}', 'RoleController@update')->name('roles.update')->middleware('permission:update role');
            Route::delete('roles/{id}', 'RoleController@destroy')->name('roles.destroy')->middleware('permission:remove role');
            
            // Employee role management routes
            Route::post('roles/{roleId}/detach-employee/{employeeId}', 'RoleController@detachEmployee')->name('roles.detach-employee')->middleware('permission:update role');
            Route::post('roles/{roleId}/attach-employee/{employeeId}', 'RoleController@attachEmployee')->name('roles.attach-employee')->middleware('permission:update role');
            Route::get('roles/{roleId}/available-employees', 'RoleController@getAvailableEmployees')->name('roles.available-employees')->middleware('permission:update role');
            Route::post('roles/{roleId}/bulk-attach-employees', 'RoleController@bulkAttachEmployees')->name('roles.bulk-attach-employees')->middleware('permission:update role');
            Route::post('roles/{roleId}/bulk-detach-employees', 'RoleController@bulkDetachEmployees')->name('roles.bulk-detach-employees')->middleware('permission:update role');
            


            Route::get('form_builder', 'FormBuildersController@index')->name('form.builder.index')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');

            Route::get('form_builder/create', 'FormBuildersController@create')->name('form.builder.create')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');
            Route::post('form_builder', 'FormBuildersController@store')->name('form.builder.store')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');

            Route::get('form_builder/{id}/edit', 'FormBuildersController@edit')->name('form.builder.edit')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');
            Route::patch('form_builder/{id}', 'FormBuildersController@update')->name('form.builder.update')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_');

            Route::get('forms', 'FormsController@index')->name('forms');

            Route::get('forms/{id}', 'FormsController@show')->name('form.view');
            
            Route::post('forms/{id}/review', 'FormsController@review')->name('form.review');
            
            Route::get('forms/create/{builder_id}', 'FormsController@create')->name('form.create');
            
            Route::post('forms/{builder_id}', 'FormsController@store')->name('forms.store');

            Route::get('centerclasstudentsbackup/backup-details', [CenterClassStudentsBackupController::class, 'getBackupDetails'])
                ->name('centerclasstudentsbackup.getBackupDetails');

                Route::get('centerclasstudentsbackup/getBackupDates', [
                    'as' => 'centerclasstudentsbackup.getBackupDates',
                    'uses' => 'CenterClassStudentsBackupController@getBackupDates',
                    'middleware' => ['permission:centerclasstudentsbackup-filter']
                ]);

            // Add new route for database documentation generation
            Route::post('/commands/dbdocs/generate', [\Modules\General\Http\Controllers\CommandsController::class, 'generateDatabaseDocs'])
                ->name('commands.dbdocs.generate');
                
            // Add route for viewing the database documentation
            Route::get('/commands/dbdocs/view', [\Modules\General\Http\Controllers\CommandsController::class, 'viewDatabaseDocs'])
                ->name('commands.dbdocs.view');
                
            // Add check-it-role route within the general prefix
            Route::get('/check-it-role', function () {
                $user = auth()->user();
                
                if (!$user) {
                    return response()->json(['is_it_role' => false]);
                }
                
                // Get organization_id from the user
                $organizationId = $user->organization_id;
                
                $isITRole = DB::table('model_has_roles')
                    ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
                    ->where('model_has_roles.model_id', $user->id)
                    ->where(function($query) use ($organizationId) {
                        // Look for any IT officer role for the user's organization
                        $query->where(function($q) use ($organizationId) {
                            $q->where('roles.name', 'LIKE', 'it-officer_%')
                              ->where('roles.organization_id', $organizationId);
                        });
                    })
                    ->exists();
                
                return response()->json(['is_it_role' => $isITRole]);
            })->name('check-it-role');

            // System Viewer Management Routes
            Route::group(['middleware' => ['employee', 'auth:employee']], function () {
                // Viewer-specific routes
                Route::post('/viewer/toggle-demo-mode', '\App\Http\Controllers\ViewerManagementController@toggleDemoMode')->name('viewer.demo.toggle');
                Route::get('/viewer/status', '\App\Http\Controllers\ViewerManagementController@getViewerStatus')->name('viewer.status');
                Route::get('/viewer/demo/status', '\App\Http\Controllers\ViewerManagementController@getDemoModeStatus')->name('viewer.demo.status');

                // Admin-only viewer management routes
                Route::get('/viewer/dashboard', '\App\Http\Controllers\ViewerManagementController@viewerDashboard')->name('viewer.dashboard');
                Route::post('/viewer/reset', '\App\Http\Controllers\ViewerManagementController@resetViewerAccount')->name('viewer.reset');
                Route::put('/viewer/account/update', '\App\Http\Controllers\ViewerManagementController@updateViewerAccount')->name('viewer.account.update');
                Route::get('/viewer/data/sessions', '\App\Http\Controllers\ViewerManagementController@getActiveSessions')->name('viewer.data.sessions');
                Route::get('/viewer/data/views', '\App\Http\Controllers\ViewerManagementController@getPageViews')->name('viewer.data.views');
                Route::get('/viewer/data/blocked', '\App\Http\Controllers\ViewerManagementController@getBlockedOperations')->name('viewer.data.blocked');
                
                // Enhanced Viewer Analytics Routes
                Route::get('/viewer/data/detailed-views', '\App\Http\Controllers\ViewerManagementController@getDetailedPageViews')->name('viewer.data.detailed-views');
                Route::get('/viewer/data/interests', '\App\Http\Controllers\ViewerManagementController@getViewerInterests')->name('viewer.data.interests');
                
                // Keep legacy route names for backward compatibility (with general prefix)
                Route::get('/viewer/stats/sessions', '\App\Http\Controllers\ViewerManagementController@getActiveSessions')->name('viewer.stats.sessions');
                Route::get('/viewer/stats/pageviews', '\App\Http\Controllers\ViewerManagementController@getPageViews')->name('viewer.stats.pageviews');
                Route::get('/viewer/stats/blocked', '\App\Http\Controllers\ViewerManagementController@getBlockedOperations')->name('viewer.stats.blocked');
                Route::get('/viewer/stats/public', '\App\Http\Controllers\ViewerManagementController@getPublicViewerStats')->name('viewer.stats.public');
            });



                    // Add this route to handle parent-teacher dashboard
            // Route::get('/parent-teacher-dashboard', [MenuController::class, 'parentTeacherDashboardIndex'])
            //     ->name('parent-teacher-dashboard.index');

            
        });


});


 




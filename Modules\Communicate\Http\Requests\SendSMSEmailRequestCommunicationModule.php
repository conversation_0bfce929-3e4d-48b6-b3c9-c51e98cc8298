<?php

namespace Modules\Communicate\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SendSMSEmailRequestCommunicationModule extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email_sms_title' => "required",
            'send_through' => "required",
            'description' => "required",
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            'email_sms_title.required' => 'Please insert the title',
            'send_through.required' => 'Please select one of the communication channels: email or sms',
            'description.required' => 'Description required'



        ];
    }
}

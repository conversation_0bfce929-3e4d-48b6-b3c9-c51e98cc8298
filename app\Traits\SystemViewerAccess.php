<?php

namespace App\Traits;

use App\Center;
use Illuminate\Database\Eloquent\Builder;

/**
 * Provides system viewer access patterns across the application.
 * 
 * This trait centralizes the logic for granting system viewers (read-only accounts)
 * access to all organizational data without requiring explicit associations.
 */
trait SystemViewerAccess
{
    /**
     * Get all centers for system viewers, respecting organizational boundaries.
     * 
     * @return Builder
     */
    public function getAllCentersForViewer(): Builder
    {
        return Center::where('organization_id', config('organization_id'))
            ->whereNull('deleted_at');
    }
    
    /**
     * Check if current user is a system viewer.
     * 
     * @return bool
     */
    public function isSystemViewer(): bool
    {
        return $this->hasRole('system_viewer_' . config('organization_id') . '_');
    }
    
    /**
     * Check if current user is managing director.
     * 
     * @return bool
     */
    public function isManagingDirector(): bool
    {
        return $this->hasRole('managing-director_' . config('organization_id') . '_');
    }
    
    /**
     * Get center IDs that user has access to (including system viewers).
     * This is a helper method for common auth()->user()->center->pluck('id')->toArray() patterns.
     * 
     * @return array
     */
    public function getAccessibleCenterIds(): array
    {
        if ($this->isSystemViewer() || $this->isManagingDirector()) {
            return $this->getAllCentersForViewer()->pluck('id')->toArray();
        }
        
        return $this->center->pluck('id')->toArray();
    }
} 
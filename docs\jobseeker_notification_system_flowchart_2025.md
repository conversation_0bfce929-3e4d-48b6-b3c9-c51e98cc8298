# JobSeeker Notification System - Complete Flowchart (2025-08-26)

## System Status: FULLY OPERATIONAL
✅ All critical bugs resolved  
✅ End-to-end notifications working  
✅ Perfect idempotency guaranteed  
✅ Type safety implemented  

## Complete End-to-End Flow

```mermaid
flowchart TD
    A[1 Dynamic Scheduler: loadDynamicCommandSchedule] --> B[2 Read command_schedule_rules table]
    B --> C[3 Execute Provider Commands]
    
    C --> D1[4a Jobs.af: SyncJobsAfCommand]
    C --> D2[4b ACBAR: SyncAcbarJobsCommand] 
    C --> D3[4c ReliefWeb: SyncReliefWebJobsCommand]
    
    D1 --> E1[5a JobsAfService::fetchAndNotifyJobs]
    D2 --> E2[5b AcbarJobService::syncAcbarJobs]
    D3 --> E3[5c ReliefWebService::fetchAndNotifyJobs]
    
    E1 --> F1[6a Fetch Jobs.af API + normalize]
    E2 --> F2[6b Fetch ACBAR API + normalize]
    E3 --> F3[6c Fetch ReliefWeb API + normalize]
    
    F1 --> G1[7a Build notification payload with provider_category_ids]
    F2 --> G2[7b Build notification payload with provider_category_ids]
    F3 --> G3[7c Build notification payload with provider_category_ids]
    
    G1 --> H[8 JobNotificationService::notifyAggregatedJobs]
    G2 --> H
    G3 --> H
    
    H --> I[9 CategoryMappingService: provider→canonical mapping]
    I --> J[10 JobNotificationHub::processAndDeliverNotifications]
    
    J --> K[11 Load existing sent jobs for deduplication]
    K --> L[12 Build sentMap for in-memory deduplication]
    L --> M[13 Group jobs by canonical categories]
    
    M --> N[14 For each JobNotificationSetup]
    N --> O[15 Match canonical categories with user subscriptions]
    O --> P[16 Build unified job list: current + backlog]
    
    P --> Q{17 Any jobs to send?}
    Q -- No --> R[18 Continue to next setup]
    Q -- Yes --> S[19 Apply aggregation limits]
    
    S --> T[20 EmailService::sendEmail]
    T --> U{21 Email sent successfully?}
    
    U -- Yes --> V[22 Record sent jobs in job_notification_sent_jobs]
    U -- No --> W[23 Log error and continue]
    
    V --> X[24 Update sentMap for current execution]
    W --> R
    X --> R
    
    R --> Y{25 More setups to process?}
    Y -- Yes --> N
    Y -- No --> Z[26 Batch insert sent job records]
    
    Z --> AA[27 Log completion and metrics]
    AA --> BB{28 Any notifications sent?}
    
    BB -- Yes --> CC[29 SUCCESS: Job alerts delivered]
    BB -- No --> DD[30 MissedJobService: Send missed call alert]
    
    DD --> EE[31 Admin receives diagnostic email]
    CC --> FF[32 END: Pipeline complete]
    EE --> FF
```

## Idempotency Guarantee (Triple-Layer Protection)

```mermaid
flowchart TD
    A[Job Ready for Notification] --> B{Layer 1: In-Memory sentMap Check}
    B -- Already sent --> C[Skip job]
    B -- Not sent --> D{Layer 2: Database Query Check}
    D -- Found in job_notification_sent_jobs --> C
    D -- Not found --> E[Include in email]
    E --> F[Send email successfully]
    F --> G[Prepare database record]
    G --> H{Layer 3: Database Constraint}
    H --> I[insertOrIgnore with unique constraint]
    I --> J[Record saved or ignored if duplicate]
    J --> K[Update in-memory sentMap]
    K --> L[Job marked as sent]
```

## Category Mapping Flow with Fallbacks

```mermaid
flowchart TD
    A[Provider Job with Categories] --> B{Has provider_category_ids?}
    B -- Yes --> C[CategoryMappingService::mapProviderToCanonicalIds]
    B -- No --> D[Fallback 1: Query job->providerCategories()]
    D --> E{Found provider categories?}
    E -- Yes --> C
    E -- No --> F[Fallback 2: getProviderCategoryIdsForJob position]
    F --> C
    C --> G[Map to canonical categories]
    G --> H{Mapping successful?}
    H -- Yes --> I[Job included in notifications]
    H -- No --> J[Job excluded from notifications]
```

## Database Schema Key Points

### job_notification_sent_jobs
- **Primary Key**: id (auto-increment)
- **Unique Constraint**: uniq_setup_job_recipient (setup_id, job_id, recipient_email)
- **Purpose**: Prevents duplicate notifications across all sync runs
- **Insert Method**: insertOrIgnore (handles constraint violations gracefully)

### Deduplication Logic
1. **Setup Level**: Each JobNotificationSetup has its own notification tracking
2. **Job Level**: Same job can be sent to different setups, but only once per setup
3. **Recipient Level**: Same job+setup combination sent only once per recipient email

## Recent Success Evidence
- **Email Sent**: "Job Alert: 1 new jobs in Technology" 
- **Timestamp**: 2025-08-26T10:20:06Z
- **Job**: Settlement Planner (UN-Habitat)
- **Recipient**: <EMAIL>
- **Tracking Record**: job_notification_sent_jobs.id=32599

## Critical Fixes Implemented
- **Type Safety**: JobNotificationHub method signatures accept ?int $executionId (was ?string)
- **Category Robustness**: JobsAfService ensures ALL jobs have provider_category_ids via fallbacks
- **Idempotency Enforcement**: Triple-layer deduplication prevents duplicate notifications
- **Database Constraints**: uniq_setup_job_recipient index enforces uniqueness
- **Error Handling**: Comprehensive try-catch blocks and defensive type casting

This pipeline is now **PRODUCTION-READY** with complete reliability and perfect idempotency.

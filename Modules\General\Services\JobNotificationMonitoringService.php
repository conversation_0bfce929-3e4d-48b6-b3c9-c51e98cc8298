<?php

declare(strict_types=1);

namespace Modules\General\Services;

use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Modules\General\Entities\JobNotificationFailure;
use Modules\General\Entities\JobNotificationHealthMetric;
use Modules\General\Entities\JobNotificationSetup;
use Modules\General\Jobs\RetryFailedNotificationJob;
use App\Services\EmailService;
use Throwable;

class JobNotificationMonitoringService
{
    /**
     * Maximum number of retries for a failed notification
     */
    protected const MAX_RETRIES = 3;
    
    /**
     * Retry delay in minutes between attempts
     */
    protected const RETRY_DELAYS = [15, 60, 240]; // 15 min, 1 hour, 4 hours
    
    /**
     * Error alert thresholds
     */
    protected const ERROR_ALERT_THRESHOLD = 5; // Alert after 5 failures in an hour
    protected const ERROR_RATE_THRESHOLD = 0.1; // Alert if error rate exceeds 10%
    
    /**
     * @var EmailService
     */
    protected $emailService;
    
    /**
     * Constructor
     * 
     * @param EmailService $emailService
     */
    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }
    
    /**
     * Track a failure in the job notification system
     *
     * @param string $errorType Type of error (e.g., 'email_send', 'processing', 'database')
     * @param string $errorMessage Error message
     * @param int|null $setupId Related setup ID (if applicable)
     * @param int|null $jobId Related job ID (if applicable)
     * @param string|null $recipientEmail Related recipient email (if applicable)
     * @param array $additionalData Any additional data to store
     * @param Throwable|null $exception The exception that caused the failure
     * @return JobNotificationFailure
     */
    public function trackFailure(
        string $errorType,
        string $errorMessage,
        ?int $setupId = null,
        ?int $jobId = null,
        ?string $recipientEmail = null,
        array $additionalData = [],
        ?Throwable $exception = null
    ): JobNotificationFailure {
        try {
            // Log the failure
            Log::error("Job notification failure: {$errorType}", [
                'setup_id' => $setupId,
                'job_id' => $jobId,
                'recipient_email' => $recipientEmail,
                'error_message' => $errorMessage,
                'exception' => $exception ? $exception->getMessage() : null
            ]);
            
            // Create failure record
            $failure = new JobNotificationFailure();
            $failure->error_type = $errorType;
            $failure->error_message = $errorMessage;
            $failure->setup_id = $setupId;
            $failure->job_id = $jobId;
            $failure->recipient_email = $recipientEmail;
            $failure->additional_data = $additionalData;
            
            // Add stack trace if exception provided
            if ($exception) {
                $failure->stack_trace = $exception->getTraceAsString();
            }
            
            $failure->save();
            
            // Update metrics
            $this->updateFailureMetrics($errorType);
            
            // Check if we need to send an alert based on failure rate
            $this->checkFailureThresholds($errorType);
            
            // Schedule a retry if appropriate
            if ($this->canRetry($errorType, $setupId, $jobId, $recipientEmail)) {
                $this->scheduleRetry($failure);
            }
            
            return $failure;
        } catch (Exception $e) {
            // Last resort fallback logging if we can't even track the failure
            Log::critical("Failed to track notification failure: {$e->getMessage()}", [
                'original_error' => $errorMessage,
                'setup_id' => $setupId,
                'job_id' => $jobId,
                'recipient_email' => $recipientEmail
            ]);
            
            // Create a basic failure record
            $fallbackFailure = new JobNotificationFailure();
            $fallbackFailure->error_type = 'tracking_failure';
            $fallbackFailure->error_message = "Failed to track original error: {$errorMessage}. Tracking error: {$e->getMessage()}";
            $fallbackFailure->save();
            
            return $fallbackFailure;
        }
    }
    
    /**
     * Schedule a retry for a failed notification
     *
     * @param JobNotificationFailure $failure
     * @return void
     */
    protected function scheduleRetry(JobNotificationFailure $failure): void
    {
        $retryCount = $failure->retry_count;
        
        // Get the appropriate delay based on retry count
        $delayMinutes = self::RETRY_DELAYS[$retryCount] ?? self::RETRY_DELAYS[count(self::RETRY_DELAYS) - 1];
        
        // Mark as retrying
        $failure->markAsRetrying();
        
        // Schedule the retry job with custom connection
        RetryFailedNotificationJob::dispatch($failure->id)
            ->onConnection('job_notifications')
            ->onQueue('notification_retries')
            ->delay(now()->addMinutes($delayMinutes));
        
        Log::info("Scheduled retry for failure ID {$failure->id}", [
            'retry_count' => $retryCount,
            'delay_minutes' => $delayMinutes,
            'will_retry_at' => now()->addMinutes($delayMinutes),
            'queue_connection' => 'job_notifications'
        ]);
    }
    
    /**
     * Process retries for pending failures
     *
     * @param int $limit Maximum number of failures to process
     * @return array Statistics about retries
     */
    public function processRetries(int $limit = 50): array
    {
        $stats = [
            'processed' => 0,
            'scheduled' => 0,
            'max_retries_reached' => 0,
            'errors' => 0
        ];
        
        try {
            // Get pending failures that haven't been retried recently
            $failures = JobNotificationFailure::where('status', 'pending')
                ->where(function($query) {
                    $query->whereNull('last_retry_at')
                        ->orWhere('last_retry_at', '<', now()->subMinutes(5));
                })
                ->limit($limit)
                ->get();
            
            foreach ($failures as $failure) {
                try {
                    $stats['processed']++;
                    
                    // Check if max retries reached
                    if ($failure->retry_count >= self::MAX_RETRIES) {
                        $failure->markAsFailed();
                        $stats['max_retries_reached']++;
                        continue;
                    }
                    
                    // Schedule retry
                    $this->scheduleRetry($failure);
                    $stats['scheduled']++;
                } catch (Exception $e) {
                    $stats['errors']++;
                    Log::error("Error processing retry for failure ID {$failure->id}: {$e->getMessage()}");
                }
            }
            
            return $stats;
        } catch (Exception $e) {
            Log::error("Error processing retries: {$e->getMessage()}");
            $stats['errors']++;
            return $stats;
        }
    }
    
    /**
     * Track metrics for email sending
     *
     * @param bool $success Whether the email was sent successfully
     * @param int $timeMs Time taken to send the email in milliseconds
     * @return void
     */
    public function trackEmailMetrics(bool $success, int $timeMs): void
    {
        try {
            $metrics = JobNotificationHealthMetric::getCurrentHourMetrics();
            
            if ($success) {
                $metrics->incrementEmailsSent();
                $metrics->updateAvgEmailTime($timeMs);
            } else {
                $metrics->incrementEmailsFailed();
            }
        } catch (Exception $e) {
            Log::error("Failed to track email metrics: {$e->getMessage()}");
        }
    }
    
    /**
     * Track metrics for setup processing
     *
     * @param int $setupId The setup ID
     * @param int $recipientCount Number of recipients processed
     * @param int $processingTimeMs Processing time in milliseconds
     * @param int $memoryUsedMb Memory used in MB
     * @return void
     */
    public function trackSetupProcessing(
        int $setupId,
        int $recipientCount,
        int $processingTimeMs,
        int $memoryUsedMb
    ): void {
        try {
            $metrics = JobNotificationHealthMetric::getCurrentHourMetrics();
            
            $metrics->incrementSetupsProcessed();
            $metrics->incrementRecipientsProcessed($recipientCount);
            $metrics->updateProcessingTime($processingTimeMs);
            $metrics->updateMaxMemory($memoryUsedMb);
            
            Log::debug("Tracked setup processing metrics", [
                'setup_id' => $setupId,
                'recipient_count' => $recipientCount,
                'processing_time_ms' => $processingTimeMs,
                'memory_used_mb' => $memoryUsedMb
            ]);
        } catch (Exception $e) {
            Log::error("Failed to track setup processing metrics: {$e->getMessage()}");
        }
    }
    
    /**
     * Update metrics for a failure
     *
     * @param string $errorType
     * @return void
     */
    protected function updateFailureMetrics(string $errorType): void
    {
        try {
            $metrics = JobNotificationHealthMetric::getCurrentHourMetrics();
            
            if ($errorType === 'email_send') {
                $metrics->incrementEmailsFailed();
            }
        } catch (Exception $e) {
            Log::error("Failed to update failure metrics: {$e->getMessage()}");
        }
    }
    
    /**
     * Check if failure thresholds have been exceeded and send alerts if needed
     *
     * @param string $errorType
     * @return void
     */
    protected function checkFailureThresholds(string $errorType): void
    {
        try {
            $metrics = JobNotificationHealthMetric::getCurrentHourMetrics();
            
            // Check absolute error count threshold
            if ($metrics->emails_failed >= self::ERROR_ALERT_THRESHOLD) {
                // Check if error rate threshold exceeded
                $totalEmails = $metrics->emails_sent + $metrics->emails_failed;
                $errorRate = $totalEmails > 0 ? $metrics->emails_failed / $totalEmails : 0;
                
                if ($errorRate >= self::ERROR_RATE_THRESHOLD) {
                    $this->sendSystemAlert(
                        "High failure rate detected in job notification system",
                        [
                            'error_type' => $errorType,
                            'failure_count' => $metrics->emails_failed,
                            'total_emails' => $totalEmails,
                            'error_rate' => $errorRate,
                            'timestamp' => now()->toDateTimeString()
                        ]
                    );
                }
            }
        } catch (Exception $e) {
            Log::error("Failed to check failure thresholds: {$e->getMessage()}");
        }
    }
    
    /**
     * Send a system alert to administrators using EmailService
     *
     * @param string $subject
     * @param array $data
     * @return void
     */
    protected function sendSystemAlert(string $subject, array $data): void
    {
        try {
            $adminEmails = config('general.system_alert_emails', []);
            
            if (empty($adminEmails)) {
                Log::warning("No admin emails configured for system alerts");
                return;
            }
            
            foreach ($adminEmails as $email) {
                $recipient = [
                    'email' => $email,
                    'name' => 'System Administrator'
                ];
                
                $from = [
                    'email' => config('mail.from.address'),
                    'name' => 'Job Notification System'
                ];
                
                $viewData = [
                    'alertSubject' => $subject,
                    'alertData' => $data,
                    'timestamp' => now()->format('Y-m-d H:i:s'),
                    'serverName' => request()->getHttpHost() ?? env('APP_URL', 'unknown')
                ];
                
                $this->emailService->sendEmail(
                    $recipient,
                    '[SYSTEM ALERT] ' . $subject,
                    'emails.system_alert',
                    $viewData,
                    []
                );
            }
            
            Log::info("Sent system alert: {$subject}", [
                'data' => $data,
                'recipients' => $adminEmails
            ]);
        } catch (Exception $e) {
            Log::error("Failed to send system alert: {$e->getMessage()}");
        }
    }
    
    /**
     * Determine if we should retry a failure
     *
     * @param string $errorType
     * @param int|null $setupId
     * @param int|null $jobId
     * @param string|null $recipientEmail
     * @return bool
     */
    protected function canRetry(
        string $errorType,
        ?int $setupId = null,
        ?int $jobId = null,
        ?string $recipientEmail = null
    ): bool {
        // Don't retry certain error types
        $nonRetryableErrors = ['invalid_data', 'quota_exceeded', 'invalid_recipient'];
        
        if (in_array($errorType, $nonRetryableErrors)) {
            return false;
        }
        
        // Need at least some information to retry
        if ($setupId === null && $jobId === null && $recipientEmail === null) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get system health statistics
     *
     * @param int $days Number of days to look back
     * @return array
     */
    public function getSystemHealth(int $days = 1): array
    {
        try {
            $startDate = Carbon::now()->subDays($days)->startOfDay();
            
            // Get metrics for the period
            $metrics = JobNotificationHealthMetric::where('metric_date', '>=', $startDate)
                ->orderBy('metric_date')
                ->orderBy('metric_hour')
                ->get();
            
            // Get failure counts
            $failures = JobNotificationFailure::where('created_at', '>=', $startDate)
                ->selectRaw('error_type, status, COUNT(*) as count')
                ->groupBy('error_type', 'status')
                ->get();
            
            // Calculate totals
            $totalSetups = $metrics->sum('setups_processed');
            $totalRecipients = $metrics->sum('recipients_processed');
            $totalEmailsSent = $metrics->sum('emails_sent');
            $totalEmailsFailed = $metrics->sum('emails_failed');
            $avgProcessingTime = $metrics->avg('processing_time_ms');
            $avgEmailTime = $metrics->avg('avg_email_time_ms');
            $maxMemoryUsed = $metrics->max('max_memory_used_mb');
            
            // Calculate error rate
            $totalEmailAttempts = $totalEmailsSent + $totalEmailsFailed;
            $errorRate = $totalEmailAttempts > 0 ? ($totalEmailsFailed / $totalEmailAttempts) : 0;
            
            // Format failures by type and status
            $formattedFailures = [];
            foreach ($failures as $failure) {
                if (!isset($formattedFailures[$failure->error_type])) {
                    $formattedFailures[$failure->error_type] = [
                        'pending' => 0,
                        'retrying' => 0,
                        'failed' => 0,
                        'resolved' => 0,
                        'total' => 0
                    ];
                }
                
                $formattedFailures[$failure->error_type][$failure->status] = $failure->count;
                $formattedFailures[$failure->error_type]['total'] += $failure->count;
            }
            
            // Get hourly metrics for chart data
            $hourlyData = [];
            foreach ($metrics as $metric) {
                $dateHour = $metric->metric_date->format('Y-m-d') . ' ' . str_pad($metric->metric_hour, 2, '0', STR_PAD_LEFT) . ':00';
                $hourlyData[] = [
                    'date_hour' => $dateHour,
                    'setups_processed' => $metric->setups_processed,
                    'recipients_processed' => $metric->recipients_processed,
                    'emails_sent' => $metric->emails_sent,
                    'emails_failed' => $metric->emails_failed,
                    'error_rate' => ($metric->emails_sent + $metric->emails_failed > 0) 
                        ? ($metric->emails_failed / ($metric->emails_sent + $metric->emails_failed)) 
                        : 0
                ];
            }
            
            return [
                'summary' => [
                    'total_setups_processed' => $totalSetups,
                    'total_recipients_processed' => $totalRecipients,
                    'total_emails_sent' => $totalEmailsSent,
                    'total_emails_failed' => $totalEmailsFailed,
                    'error_rate' => $errorRate,
                    'avg_processing_time_ms' => $avgProcessingTime,
                    'avg_email_time_ms' => $avgEmailTime,
                    'max_memory_used_mb' => $maxMemoryUsed
                ],
                'failures' => $formattedFailures,
                'hourly_data' => $hourlyData
            ];
        } catch (Exception $e) {
            Log::error("Error getting system health: {$e->getMessage()}");
            
            return [
                'error' => 'Failed to retrieve system health metrics',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Send a system health report to administrators using EmailService
     *
     * @param array $healthData
     * @return bool
     */
    public function sendSystemHealthReport(array $healthData): bool
    {
        try {
            $adminEmails = config('general.system_alert_emails', []);
            
            if (empty($adminEmails)) {
                Log::warning("No admin emails configured for system health reports");
                return false;
            }
            
            $subject = "Job Notification System Health Report";
            $viewData = [
                'alertSubject' => $subject,
                'alertData' => [
                    'report_type' => 'health_report',
                    'summary' => $healthData['summary'],
                    'failures' => $healthData['failures'],
                    'hourly_data' => $healthData['hourly_data'],
                    'timestamp' => now()->toDateTimeString(),
                    'report_period_days' => count($healthData['hourly_data']) > 0 
                        ? ceil(count($healthData['hourly_data']) / 24) 
                        : 1
                ],
                'timestamp' => now()->format('Y-m-d H:i:s'),
                'serverName' => request()->getHttpHost() ?? env('APP_URL', 'unknown')
            ];
            
            $from = [
                'email' => config('mail.from.address'),
                'name' => 'Job Notification System'
            ];
            
            $sentCount = 0;
            foreach ($adminEmails as $email) {
                try {
                    $recipient = [
                        'email' => $email,
                        'name' => 'System Administrator'
                    ];
                    
                    $result = $this->emailService->sendEmail(
                        $recipient,
                        $subject,
                        'emails.system_alert',
                        $viewData,
                        []
                    );
                    
                    if ($result['success']) {
                        $sentCount++;
                    }
                } catch (\Exception $e) {
                    Log::error("Failed to send system health report to {$email}: {$e->getMessage()}");
                }
            }
            
            Log::info("Sent system health report", [
                'recipients' => $adminEmails,
                'success_count' => $sentCount
            ]);
            
            return $sentCount > 0;
        } catch (\Exception $e) {
            Log::error("Failed to send system health report: {$e->getMessage()}");
            return false;
        }
    }
} 
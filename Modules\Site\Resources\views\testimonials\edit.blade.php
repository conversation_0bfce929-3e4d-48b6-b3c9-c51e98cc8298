@extends('layouts.hound')

@section('content')
<div class="col-sm-12">
    <div class="panel panel-default card-view">
        <div class="panel-heading">
            <div class="pull-left">
                <h6 class="panel-title txt-dark">{{ trans('common.edit') }} Testimonial - {{ $testimonial->name }}</h6>
            </div>
            <a href="{{ url('/workplace/site/testimonials') }}" class="pull-right" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>            
            <div class="clearfix"></div>
        </div>
        <div class="panel-wrapper collapse in">
            <div class="panel-body">
                @if ($errors->any())
                    <ul class="alert alert-danger">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                @endif

                {!! Form::model($testimonial, [
                    'method' => 'PATCH',
                    'url' => ['/workplace/site/testimonials', $testimonial->id],
                    'class' => 'form-horizontal',
                    'files' => true
                ]) !!}

                @include ('site::testimonials.form', ['submitButtonText' => 'Update'])

                {!! Form::close() !!}

            </div>
        </div>
    </div>
</div>
@endsection

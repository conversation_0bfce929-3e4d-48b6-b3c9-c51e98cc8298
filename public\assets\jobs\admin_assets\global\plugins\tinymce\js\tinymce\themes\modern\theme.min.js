!function(){"use strict";var e,t,n,i,r,o=tinymce.util.Tools.resolve("tinymce.ThemeManager"),s=tinymce.util.Tools.resolve("tinymce.EditorManager"),a=tinymce.util.Tools.resolve("tinymce.util.Tools"),l=function(e){return!1!==u(e)},u=function(e){return e.getParam("menubar")},c=function(e){return e.getParam("toolbar_items_size")},d=function(e){return e.getParam("menu")},f=function(e){return!1===e.settings.skin},h=function(e){var t=e.getParam("resize","vertical");return!1===t?"none":"both"===t?"both":"vertical"},m=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),g=tinymce.util.Tools.resolve("tinymce.ui.Factory"),p=tinymce.util.Tools.resolve("tinymce.util.I18n"),v=function(e){return e.fire("SkinLoaded")},b=function(e){return e.fire("ResizeEditor")},y=function(e){return e.fire("BeforeRenderUI")},x=function(e,t){return function(){var n=e.find(t)[0];n&&n.focus(!0)}},w=function(e,t){e.shortcuts.add("Alt+F9","",x(t,"menubar")),e.shortcuts.add("Alt+F10,F10","",x(t,"toolbar")),e.shortcuts.add("Alt+F11","",x(t,"elementpath")),t.on("cancel",function(){e.focus()})},_=tinymce.util.Tools.resolve("tinymce.geom.Rect"),R=tinymce.util.Tools.resolve("tinymce.util.Delay"),C=function(e){return function(){return e}},k={noop:function(){},noarg:function(e){return function(){return e()}},compose:function(e,t){return function(){return e(t.apply(null,arguments))}},constant:C,identity:function(e){return e},tripleEquals:function(e,t){return e===t},curry:function(e){for(var t=new Array(arguments.length-1),n=1;n<arguments.length;n++)t[n-1]=arguments[n];return function(){for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];var r=t.concat(n);return e.apply(null,r)}},not:function(e){return function(){return!e.apply(null,arguments)}},die:function(e){return function(){throw new Error(e)}},apply:function(e){return e()},call:function(e){e()},never:C(!1),always:C(!0)},E=k.never,H=k.always,S=function(){return M},M=(i={fold:function(e,t){return e()},is:E,isSome:E,isNone:H,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},or:n,orThunk:t,map:S,ap:S,each:function(){},bind:S,flatten:S,exists:E,forall:H,filter:S,equals:e=function(e){return e.isNone()},equals_:e,toArray:function(){return[]},toString:k.constant("none()")},Object.freeze&&Object.freeze(i),i),T=function(e){var t=function(){return e},n=function(){return r},i=function(t){return t(e)},r={fold:function(t,n){return n(e)},is:function(t){return e===t},isSome:H,isNone:E,getOr:t,getOrThunk:t,getOrDie:t,or:n,orThunk:n,map:function(t){return T(t(e))},ap:function(t){return t.fold(S,function(t){return T(t(e))})},each:function(t){t(e)},bind:i,flatten:t,exists:i,forall:i,filter:function(t){return t(e)?r:M},equals:function(t){return t.is(e)},equals_:function(t,n){return t.fold(E,function(t){return n(e,t)})},toArray:function(){return[e]},toString:function(){return"some("+e+")"}};return r},P={some:T,none:S,from:function(e){return null===e||e===undefined?M:T(e)}},W=function(e){return e?e.getRoot().uiContainer:null},D={getUiContainerDelta:function(e){var t=W(e);if(t&&"static"!==m.DOM.getStyle(t,"position",!0)){var n=m.DOM.getPos(t),i=t.scrollLeft-n.x,r=t.scrollTop-n.y;return P.some({x:i,y:r})}return P.none()},setUiContainer:function(e,t){var n=m.DOM.select(e.settings.ui_container)[0];t.getRoot().uiContainer=n},getUiContainer:W,inheritUiContainer:function(e,t){return t.uiContainer=W(e)}},N=function(e,t,n){var i,r=[];if(t)return a.each(t.split(/[ ,]/),function(t){var o,s=function(){var n=e.selection;t.settings.stateSelector&&n.selectorChanged(t.settings.stateSelector,function(e){t.active(e)},!0),t.settings.disabledStateSelector&&n.selectorChanged(t.settings.disabledStateSelector,function(e){t.disabled(e)})};"|"===t?i=null:(i||(i={type:"buttongroup",items:[]},r.push(i)),e.buttons[t]&&(o=t,"function"==typeof(t=e.buttons[o])&&(t=t()),t.type=t.type||"button",t.size=n,t=g.create(t),i.items.push(t),e.initialized?s():e.on("init",s)))}),{type:"toolbar",layout:"flow",items:r}},A=N,B=function(e,t){var n,i,r=[];if(a.each(!1===(i=(n=e).getParam("toolbar"))?[]:a.isArray(i)?a.grep(i,function(e){return e.length>0}):function(e,t){for(var n=[],i=1;i<10;i++){var r=e["toolbar"+i];if(!r)break;n.push(r)}var o=e.toolbar?[e.toolbar]:[t];return n.length>0?n:o}(n.settings,"undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image"),function(n){var i;(i=n)&&r.push(N(e,i,t))}),r.length)return{type:"panel",layout:"stack",classes:"toolbar-grp",ariaRoot:!0,ariaRemember:!0,items:r}},O=m.DOM,z=function(e){return{left:e.x,top:e.y,width:e.w,height:e.h,right:e.x+e.w,bottom:e.y+e.h}},L=function(e,t){e.moveTo(t.left,t.top)},I=function(e,t,n,i,r,o){return o=z({x:t,y:n,w:o.w,h:o.h}),e&&(o=e({elementRect:z(i),contentAreaRect:z(r),panelRect:o})),o},F=function(e){var t,n=function(){return e.contextToolbars||[]},i=function(t,n){var i,r,o,s,l,u,c,d=e.getParam("inline_toolbar_position_handler");if(!e.removed){if(!t||!t.toolbar.panel)return f=e,void a.each(f.contextToolbars,function(e){e.panel&&e.panel.hide()});var f,h,m,g,p;c=["bc-tc","tc-bc","tl-bl","bl-tl","tr-br","br-tr"],l=t.toolbar.panel,n&&l.show(),h=t.element,m=O.getPos(e.getContentAreaContainer()),g=e.dom.getRect(h),"BODY"===(p=e.dom.getRoot()).nodeName&&(g.x-=p.ownerDocument.documentElement.scrollLeft||p.scrollLeft,g.y-=p.ownerDocument.documentElement.scrollTop||p.scrollTop),g.x+=m.x,g.y+=m.y,o=g,r=O.getRect(l.getEl()),s=O.getRect(e.getContentAreaContainer()||e.getBody());var v,b,y,x=D.getUiContainerDelta(l).getOr({x:0,y:0});if(o.x+=x.x,o.y+=x.y,r.x+=x.x,r.y+=x.y,s.x+=x.x,s.y+=x.y,"inline"!==O.getStyle(t.element,"display",!0)){var w=t.element.getBoundingClientRect();o.w=w.width,o.h=w.height}e.inline||(s.w=e.getDoc().documentElement.offsetWidth),e.selection.controlSelection.isResizable(t.element)&&o.w<25&&(o=_.inflate(o,0,8)),i=_.findBestRelativePosition(r,o,s,c),o=_.clamp(o,s),i?(u=_.relativePosition(r,o,i),L(l,I(d,u.x,u.y,o,s,r))):(s.h+=r.h,(o=_.intersect(s,o))?(i=_.findBestRelativePosition(r,o,s,["bc-tc","bl-tl","br-tr"]))?(u=_.relativePosition(r,o,i),L(l,I(d,u.x,u.y,o,s,r))):L(l,I(d,o.x,o.y,o,s,r)):l.hide()),v=l,y=function(e,t){return e===t},b=(b=i)?b.substr(0,2):"",a.each({t:"down",b:"up"},function(e,t){v.classes.toggle("arrow-"+e,y(t,b.substr(0,1)))}),a.each({l:"left",r:"right"},function(e,t){v.classes.toggle("arrow-"+e,y(t,b.substr(1,1)))})}},r=function(t){return function(){R.requestAnimationFrame(function(){e.selection&&i(l(e.selection.getNode()),t)})}},o=function(n){var o;if(n.toolbar.panel)return n.toolbar.panel.show(),void i(n);o=g.create({type:"floatpanel",role:"dialog",classes:"tinymce tinymce-inline arrow",ariaLabel:"Inline toolbar",layout:"flex",direction:"column",align:"stretch",autohide:!1,autofix:!0,fixed:!0,border:1,items:A(e,n.toolbar.items),oncancel:function(){e.focus()}}),D.setUiContainer(e,o),function(n){if(!t){var i=r(!0),o=D.getUiContainer(n);t=e.selection.getScrollContainer()||e.getWin(),O.bind(t,"scroll",i),O.bind(o,"scroll",i),e.on("remove",function(){O.unbind(t,"scroll",i),O.unbind(o,"scroll",i)})}}(o),n.toolbar.panel=o,o.renderTo().reflow(),i(n)},s=function(){a.each(n(),function(e){e.panel&&e.panel.hide()})},l=function(t){var i,r,o,s=n();for(i=(o=e.$(t).parents().add(t)).length-1;i>=0;i--)for(r=s.length-1;r>=0;r--)if(s[r].predicate(o[i]))return{toolbar:s[r],element:o[i]};return null};e.on("click keyup setContent ObjectResized",function(t){("setcontent"!==t.type||t.selection)&&R.setEditorTimeout(e,function(){var t;(t=l(e.selection.getNode()))?(s(),o(t)):s()})}),e.on("blur hide contextmenu",s),e.on("ObjectResizeStart",function(){var t=l(e.selection.getNode());t&&t.toolbar.panel&&t.toolbar.panel.hide()}),e.on("ResizeEditor ResizeWindow",r(!0)),e.on("nodeChange",r(!1)),e.on("remove",function(){a.each(n(),function(e){e.panel&&e.panel.remove()}),e.contextToolbars={}}),e.shortcuts.add("ctrl+shift+e > ctrl+shift+p","",function(){var t=l(e.selection.getNode());t&&t.toolbar.panel&&t.toolbar.panel.items()[0].focus()})},U=(r=Array.prototype.indexOf)===undefined?function(e,t){return J(e,t)}:function(e,t){return r.call(e,t)},V=function(e,t){return U(e,t)>-1},j=function(e,t){for(var n=e.length,i=new Array(n),r=0;r<n;r++){var o=e[r];i[r]=t(o,r,e)}return i},Y=function(e,t){for(var n=0,i=e.length;n<i;n++)t(e[n],n,e)},q=function(e,t){for(var n=e.length-1;n>=0;n--)t(e[n],n,e)},$=function(e,t){for(var n=[],i=0,r=e.length;i<r;i++){var o=e[i];t(o,i,e)&&n.push(o)}return n},X=function(e,t){for(var n=0,i=e.length;n<i;n++)if(t(e[n],n,e))return P.some(n);return P.none()},J=function(e,t){for(var n=0,i=e.length;n<i;++n)if(e[n]===t)return n;return-1},G=Array.prototype.push,K=function(e){for(var t=[],n=0,i=e.length;n<i;++n){if(!Array.prototype.isPrototypeOf(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);G.apply(t,e[n])}return t},Z=function(e,t){for(var n=0,i=e.length;n<i;++n)if(!0!==t(e[n],n,e))return!1;return!0},Q=Array.prototype.slice,ee={map:j,each:Y,eachr:q,partition:function(e,t){for(var n=[],i=[],r=0,o=e.length;r<o;r++){var s=e[r];(t(s,r,e)?n:i).push(s)}return{pass:n,fail:i}},filter:$,groupBy:function(e,t){if(0===e.length)return[];for(var n=t(e[0]),i=[],r=[],o=0,s=e.length;o<s;o++){var a=e[o],l=t(a);l!==n&&(i.push(r),r=[]),n=l,r.push(a)}return 0!==r.length&&i.push(r),i},indexOf:function(e,t){var n=U(e,t);return-1===n?P.none():P.some(n)},foldr:function(e,t,n){return q(e,function(e){n=t(n,e)}),n},foldl:function(e,t,n){return Y(e,function(e){n=t(n,e)}),n},find:function(e,t){for(var n=0,i=e.length;n<i;n++){var r=e[n];if(t(r,n,e))return P.some(r)}return P.none()},findIndex:X,flatten:K,bind:function(e,t){var n=j(e,t);return K(n)},forall:Z,exists:function(e,t){return X(e,t).isSome()},contains:V,equal:function(e,t){return e.length===t.length&&Z(e,function(e,n){return e===t[n]})},reverse:function(e){var t=Q.call(e,0);return t.reverse(),t},chunk:function(e,t){for(var n=[],i=0;i<e.length;i+=t){var r=e.slice(i,i+t);n.push(r)}return n},difference:function(e,t){return $(e,function(e){return!V(t,e)})},mapToObject:function(e,t){for(var n={},i=0,r=e.length;i<r;i++){var o=e[i];n[String(o)]=t(o,i)}return n},pure:function(e){return[e]},sort:function(e,t){var n=Q.call(e,0);return n.sort(t),n},range:function(e,t){for(var n=[],i=0;i<e;i++)n.push(t(i));return n},head:function(e){return 0===e.length?P.none():P.some(e[0])},last:function(e){return 0===e.length?P.none():P.some(e[e.length-1])}},te={file:{title:"File",items:"newdocument restoredraft | preview | print"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen"},insert:{title:"Insert",items:"image link media template codesample inserttable | charmap hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | blockformats align | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck"},table:{title:"Table"},help:{title:"Help"}},ne=function(e,t){return"|"===e?{name:"|",item:{text:"|"}}:t?{name:e,item:t}:null},ie=function(e){return e&&"|"===e.item.text},re=function(e,t,n,i){var r,o,s,l,u,c,d,f;return t?(o=t[i],l=!0):o=te[i],o&&(r={text:o.title},s=[],a.each((o.items||"").split(/[ ,]/),function(t){var n=ne(t,e[t]);n&&s.push(n)}),l||a.each(e,function(e,t){var n,r;e.context!==i||(n=s,r=t,ee.findIndex(n,function(e){return e.name===r}).isSome())||("before"===e.separator&&s.push({name:"|",item:{text:"|"}}),e.prependToContext?s.unshift(ne(t,e)):s.push(ne(t,e)),"after"===e.separator&&s.push({name:"|",item:{text:"|"}}))}),r.menu=ee.map((u=s,c=n,d=ee.filter(u,function(e){return!1===c.hasOwnProperty(e.name)}),f=ee.filter(d,function(e,t,n){return!ie(e)||!ie(n[t-1])}),ee.filter(f,function(e,t,n){return!ie(e)||t>0&&t<n.length-1})),function(e){return e.item}),!r.menu.length)?null:r},oe=function(e){for(var t,n=[],i=function(e){var t,n=[],i=d(e);if(i)for(t in i)n.push(t);else for(t in te)n.push(t);return n}(e),r=a.makeMap((t=e,t.getParam("removed_menuitems","")).split(/[ ,]/)),o=u(e),s="string"==typeof o?o.split(/[ ,]/):i,l=0;l<s.length;l++){var c=s[l],f=re(e.menuItems,d(e),r,c);f&&n.push(f)}return n},se=m.DOM,ae=function(e){return{width:e.clientWidth,height:e.clientHeight}},le=function(e,t,n){var i,r,o,s;i=e.getContainer(),r=e.getContentAreaContainer().firstChild,o=ae(i),s=ae(r),null!==t&&(t=Math.max(e.getParam("min_width",100,"number"),t),t=Math.min(e.getParam("max_width",65535,"number"),t),se.setStyle(i,"width",t+(o.width-s.width)),se.setStyle(r,"width",t)),n=Math.max(e.getParam("min_height",100,"number"),n),n=Math.min(e.getParam("max_height",65535,"number"),n),se.setStyle(r,"height",n),b(e)},ue=le,ce=function(e,t,n){var i=e.getContentAreaContainer();le(e,i.clientWidth+t,i.clientHeight+n)},de=tinymce.util.Tools.resolve("tinymce.Env"),fe=function(e,t,n){var i,r=e.settings[n];r&&r((i=t.getEl("body"),{element:function(){return i}}))},he=function(e,t,n){return function(i){var r,o,s,l,u,c=i.control,d=c.parents().filter("panel")[0],f=d.find("#"+t)[0],h=(r=n,o=t,a.grep(r,function(e){return e.name===o})[0]);s=t,l=d,u=n,a.each(u,function(e){var t=l.items().filter("#"+e.name)[0];t&&t.visible()&&e.name!==s&&(fe(e,t,"onhide"),t.visible(!1))}),c.parent().items().each(function(e){e.active(!1)}),f&&f.visible()?(fe(h,f,"onhide"),f.hide(),c.active(!1)):(f?(f.show(),fe(h,f,"onshow")):(f=g.create({type:"container",name:t,layout:"stack",classes:"sidebar-panel",html:""}),d.prepend(f),fe(h,f,"onrender"),fe(h,f,"onshow")),c.active(!0)),b(e)}},me=function(e){return!(de.ie&&!(de.ie>=11)||!e.sidebars)&&e.sidebars.length>0},ge=function(e){return{type:"panel",name:"sidebar",layout:"stack",classes:"sidebar",items:[{type:"toolbar",layout:"stack",classes:"sidebar-toolbar",items:a.map(e.sidebars,function(t){var n=t.settings;return{type:"button",icon:n.icon,image:n.image,tooltip:n.tooltip,onclick:he(e,t.name,e.sidebars)}})}]}},pe=function(e){var t=function(){e._skinLoaded=!0,v(e)};return function(){e.initialized?t():e.on("init",t)}},ve=m.DOM,be=function(e){return{type:"panel",name:"iframe",layout:"stack",classes:"edit-area",border:e,html:""}},ye=function(e,t,n){var i,r,o,s,a;if(!1===f(e)&&n.skinUiCss?ve.styleSheetLoader.load(n.skinUiCss,pe(e)):pe(e)(),i=t.panel=g.create({type:"panel",role:"application",classes:"tinymce",style:"visibility: hidden",layout:"stack",border:1,items:[{type:"container",classes:"top-part",items:[!1===l(e)?null:{type:"menubar",border:"0 0 1 0",items:oe(e)},B(e,c(e))]},me(e)?(s=e,{type:"panel",layout:"stack",classes:"edit-aria-container",border:"1 0 0 0",items:[be("0"),ge(s)]}):be("1 0 0 0")]}),D.setUiContainer(e,i),"none"!==h(e)&&(r={type:"resizehandle",direction:h(e),onResizeStart:function(){var t=e.getContentAreaContainer().firstChild;o={width:t.clientWidth,height:t.clientHeight}},onResize:function(t){"both"===h(e)?ue(e,o.width+t.deltaX,o.height+t.deltaY):ue(e,null,o.height+t.deltaY)}}),e.getParam("statusbar",!0,"boolean")){var u=p.translate(["Powered by {0}",'<a href="https://www.tinymce.com/?utm_campaign=editor_referral&utm_medium=poweredby&utm_source=tinymce" rel="noopener" target="_blank" role="presentation" tabindex="-1">tinymce</a>']),d=e.getParam("branding",!0,"boolean")?{type:"label",classes:"branding",html:" "+u}:null;i.add({type:"panel",name:"statusbar",classes:"statusbar",layout:"flow",border:"1 0 0 0",ariaRoot:!0,items:[{type:"elementpath",editor:e},r,d]})}return y(e),e.on("SwitchMode",(a=i,function(e){a.find("*").disabled("readonly"===e.mode)})),i.renderBefore(n.targetNode).reflow(),e.getParam("readonly",!1,"boolean")&&e.setMode("readonly"),n.width&&ve.setStyle(i.getEl(),"width",n.width),e.on("remove",function(){i.remove(),i=null}),w(e,i),F(e),{iframeContainer:i.find("#iframe")[0].getEl(),editorContainer:i.getEl()}},xe=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),we=0,_e={id:function(){return"mceu_"+we++},create:function(e,t,n){var i=document.createElement(e);return m.DOM.setAttribs(i,t),"string"==typeof n?i.innerHTML=n:a.each(n,function(e){e.nodeType&&i.appendChild(e)}),i},createFragment:function(e){return m.DOM.createFragment(e)},getWindowSize:function(){return m.DOM.getViewPort()},getSize:function(e){var t,n;if(e.getBoundingClientRect){var i=e.getBoundingClientRect();t=Math.max(i.width||i.right-i.left,e.offsetWidth),n=Math.max(i.height||i.bottom-i.bottom,e.offsetHeight)}else t=e.offsetWidth,n=e.offsetHeight;return{width:t,height:n}},getPos:function(e,t){return m.DOM.getPos(e,t||_e.getContainer())},getContainer:function(){return de.container?de.container:document.body},getViewPort:function(e){return m.DOM.getViewPort(e)},get:function(e){return document.getElementById(e)},addClass:function(e,t){return m.DOM.addClass(e,t)},removeClass:function(e,t){return m.DOM.removeClass(e,t)},hasClass:function(e,t){return m.DOM.hasClass(e,t)},toggleClass:function(e,t,n){return m.DOM.toggleClass(e,t,n)},css:function(e,t,n){return m.DOM.setStyle(e,t,n)},getRuntimeStyle:function(e,t){return m.DOM.getStyle(e,t,!0)},on:function(e,t,n,i){return m.DOM.bind(e,t,n,i)},off:function(e,t,n){return m.DOM.unbind(e,t,n)},fire:function(e,t,n){return m.DOM.fire(e,t,n)},innerHtml:function(e,t){m.DOM.setHTML(e,t)}},Re=function(e){return"static"===_e.getRuntimeStyle(e,"position")},Ce=function(e){return e.state.get("fixed")};function ke(e,t,n){var i,r,o,s,a,l,u,c,d,f;return d=Ee(),o=(r=_e.getPos(t,D.getUiContainer(e))).x,s=r.y,Ce(e)&&Re(document.body)&&(o-=d.x,s-=d.y),i=e.getEl(),a=(f=_e.getSize(i)).width,l=f.height,u=(f=_e.getSize(t)).width,c=f.height,"b"===(n=(n||"").split(""))[0]&&(s+=c),"r"===n[1]&&(o+=u),"c"===n[0]&&(s+=Math.round(c/2)),"c"===n[1]&&(o+=Math.round(u/2)),"b"===n[3]&&(s-=l),"r"===n[4]&&(o-=a),"c"===n[3]&&(s-=Math.round(l/2)),"c"===n[4]&&(o-=Math.round(a/2)),{x:o,y:s,w:a,h:l}}var Ee=function(){var e=window,t=Math.max(e.pageXOffset,document.body.scrollLeft,document.documentElement.scrollLeft),n=Math.max(e.pageYOffset,document.body.scrollTop,document.documentElement.scrollTop);return{x:t,y:n,w:t+(e.innerWidth||document.documentElement.clientWidth),h:n+(e.innerHeight||document.documentElement.clientHeight)}},He=function(e){var t,n=D.getUiContainer(e);return n&&!Ce(e)?{x:0,y:0,w:(t=n).scrollWidth-1,h:t.scrollHeight-1}:Ee()},Se={testMoveRel:function(e,t){for(var n=He(this),i=0;i<t.length;i++){var r=ke(this,e,t[i]);if(Ce(this)){if(r.x>0&&r.x+r.w<n.w&&r.y>0&&r.y+r.h<n.h)return t[i]}else if(r.x>n.x&&r.x+r.w<n.w&&r.y>n.y&&r.y+r.h<n.h)return t[i]}return t[0]},moveRel:function(e,t){"string"!=typeof t&&(t=this.testMoveRel(e,t));var n=ke(this,e,t);return this.moveTo(n.x,n.y)},moveBy:function(e,t){var n=this.layoutRect();return this.moveTo(n.x+e,n.y+t),this},moveTo:function(e,t){var n=this;function i(e,t,n){return e<0?0:e+n>t&&(e=t-n)<0?0:e}if(n.settings.constrainToViewport){var r=He(this),o=n.layoutRect();e=i(e,r.w,o.w),t=i(t,r.h,o.h)}var s=D.getUiContainer(n);return s&&Re(s)&&!Ce(n)&&(e-=s.scrollLeft,t-=s.scrollTop),s&&(e+=1,t+=1),n.state.get("rendered")?n.layoutRect({x:e,y:t}).repaint():(n.settings.x=e,n.settings.y=t),n.fire("move",{x:e,y:t}),n}},Me=tinymce.util.Tools.resolve("tinymce.util.Class"),Te=tinymce.util.Tools.resolve("tinymce.util.EventDispatcher"),Pe=function(e){var t;if(e)return"number"==typeof e?{top:e=e||0,left:e,bottom:e,right:e}:(1===(t=(e=e.split(" ")).length)?e[1]=e[2]=e[3]=e[0]:2===t?(e[2]=e[0],e[3]=e[1]):3===t&&(e[3]=e[1]),{top:parseInt(e[0],10)||0,right:parseInt(e[1],10)||0,bottom:parseInt(e[2],10)||0,left:parseInt(e[3],10)||0})},We=function(e,t){function n(t){var n=parseFloat(function(t){var n=e.ownerDocument.defaultView;if(n){var i=n.getComputedStyle(e,null);return i?(t=t.replace(/[A-Z]/g,function(e){return"-"+e}),i.getPropertyValue(t)):null}return e.currentStyle[t]}(t));return isNaN(n)?0:n}return{top:n(t+"TopWidth"),right:n(t+"RightWidth"),bottom:n(t+"BottomWidth"),left:n(t+"LeftWidth")}};function De(){}function Ne(e){this.cls=[],this.cls._map={},this.onchange=e||De,this.prefix=""}a.extend(Ne.prototype,{add:function(e){return e&&!this.contains(e)&&(this.cls._map[e]=!0,this.cls.push(e),this._change()),this},remove:function(e){if(this.contains(e)){var t=void 0;for(t=0;t<this.cls.length&&this.cls[t]!==e;t++);this.cls.splice(t,1),delete this.cls._map[e],this._change()}return this},toggle:function(e,t){var n=this.contains(e);return n!==t&&(n?this.remove(e):this.add(e),this._change()),this},contains:function(e){return!!this.cls._map[e]},_change:function(){delete this.clsValue,this.onchange.call(this)}}),Ne.prototype.toString=function(){var e;if(this.clsValue)return this.clsValue;e="";for(var t=0;t<this.cls.length;t++)t>0&&(e+=" "),e+=this.prefix+this.cls[t];return e};var Ae,Be,Oe,ze=/^([\w\\*]+)?(?:#([\w\-\\]+))?(?:\.([\w\\\.]+))?(?:\[\@?([\w\\]+)([\^\$\*!~]?=)([\w\\]+)\])?(?:\:(.+))?/i,Le=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g,Ie=/^\s*|\s*$/g,Fe=Me.extend({init:function(e){var t=this.match;function n(e,n,r){var o;function s(e){e&&n.push(e)}return s(function(e){if(e)return e=e.toLowerCase(),function(t){return"*"===e||t.type===e}}((o=ze.exec(e.replace(Ie,"")))[1])),s(function(e){if(e)return function(t){return t._name===e}}(o[2])),s(function(e){if(e)return e=e.split("."),function(t){for(var n=e.length;n--;)if(!t.classes.contains(e[n]))return!1;return!0}}(o[3])),s(function(e,t,n){if(e)return function(i){var r=i[e]?i[e]():"";return t?"="===t?r===n:"*="===t?r.indexOf(n)>=0:"~="===t?(" "+r+" ").indexOf(" "+n+" ")>=0:"!="===t?r!==n:"^="===t?0===r.indexOf(n):"$="===t&&r.substr(r.length-n.length)===n:!!n}}(o[4],o[5],o[6])),s(function(e){var n;if(e)return(e=/(?:not\((.+)\))|(.+)/i.exec(e))[1]?(n=i(e[1],[]),function(e){return!t(e,n)}):(e=e[2],function(t,n,i){return"first"===e?0===n:"last"===e?n===i-1:"even"===e?n%2==0:"odd"===e?n%2==1:!!t[e]&&t[e]()})}(o[7])),n.pseudo=!!o[7],n.direct=r,n}function i(e,t){var r,o,s,a=[];do{if(Le.exec(""),(o=Le.exec(e))&&(e=o[3],a.push(o[1]),o[2])){r=o[3];break}}while(o);for(r&&i(r,t),e=[],s=0;s<a.length;s++)">"!==a[s]&&e.push(n(a[s],[],">"===a[s-1]));return t.push(e),t}this._selectors=i(e,[])},match:function(e,t){var n,i,r,o,s,a,l,u,c,d,f,h,m;for(n=0,i=(t=t||this._selectors).length;n<i;n++){for(m=e,h=0,r=(o=(s=t[n]).length)-1;r>=0;r--)for(u=s[r];m;){if(u.pseudo)for(c=d=(f=m.parent().items()).length;c--&&f[c]!==m;);for(a=0,l=u.length;a<l;a++)if(!u[a](m,c,d)){a=l+1;break}if(a===l){h++;break}if(r===o-1)break;m=m.parent()}if(h===o)return!0}return!1},find:function(e){var t,n,i=[],r=this._selectors;function o(e,t,n){var r,s,a,l,u,c=t[n];for(r=0,s=e.length;r<s;r++){for(u=e[r],a=0,l=c.length;a<l;a++)if(!c[a](u,r,s)){a=l+1;break}if(a===l)n===t.length-1?i.push(u):u.items&&o(u.items(),t,n+1);else if(c.direct)return;u.items&&o(u.items(),t,n)}}if(e.items){for(t=0,n=r.length;t<n;t++)o(e.items(),r[t],0);n>1&&(i=function(e){for(var t,n=[],i=e.length;i--;)(t=e[i]).__checked||(n.push(t),t.__checked=1);for(i=n.length;i--;)delete n[i].__checked;return n}(i))}return Ae||(Ae=Fe.Collection),new Ae(i)}}),Ue=Array.prototype.push,Ve=Array.prototype.slice;Oe={length:0,init:function(e){e&&this.add(e)},add:function(e){return a.isArray(e)?Ue.apply(this,e):e instanceof Be?this.add(e.toArray()):Ue.call(this,e),this},set:function(e){var t,n=this,i=n.length;for(n.length=0,n.add(e),t=n.length;t<i;t++)delete n[t];return n},filter:function(e){var t,n,i,r,o=[];for("string"==typeof e?(e=new Fe(e),r=function(t){return e.match(t)}):r=e,t=0,n=this.length;t<n;t++)r(i=this[t])&&o.push(i);return new Be(o)},slice:function(){return new Be(Ve.apply(this,arguments))},eq:function(e){return-1===e?this.slice(e):this.slice(e,+e+1)},each:function(e){return a.each(this,e),this},toArray:function(){return a.toArray(this)},indexOf:function(e){for(var t=this.length;t--&&this[t]!==e;);return t},reverse:function(){return new Be(a.toArray(this).reverse())},hasClass:function(e){return!!this[0]&&this[0].classes.contains(e)},prop:function(e,t){var n;return t!==undefined?(this.each(function(n){n[e]&&n[e](t)}),this):(n=this[0])&&n[e]?n[e]():void 0},exec:function(e){var t=a.toArray(arguments).slice(1);return this.each(function(n){n[e]&&n[e].apply(n,t)}),this},remove:function(){for(var e=this.length;e--;)this[e].remove();return this},addClass:function(e){return this.each(function(t){t.classes.add(e)})},removeClass:function(e){return this.each(function(t){t.classes.remove(e)})}},a.each("fire on off show hide append prepend before after reflow".split(" "),function(e){Oe[e]=function(){var t=a.toArray(arguments);return this.each(function(n){e in n&&n[e].apply(n,t)}),this}}),a.each("text name disabled active selected checked visible parent value data".split(" "),function(e){Oe[e]=function(t){return this.prop(e,t)}}),Be=Me.extend(Oe),Fe.Collection=Be;var je=Be,Ye=function(e){this.create=e.create};Ye.create=function(e,t){return new Ye({create:function(n,i){var r,o=function(e){n.set(i,e.value)};return n.on("change:"+i,function(n){e.set(t,n.value)}),e.on("change:"+t,o),(r=n._bindings)||(r=n._bindings=[],n.on("destroy",function(){for(var e=r.length;e--;)r[e]()})),r.push(function(){e.off("change:"+t,o)}),e.get(t)}})};var qe=tinymce.util.Tools.resolve("tinymce.util.Observable");function $e(e){return e.nodeType>0}var Xe,Je,Ge=Me.extend({Mixins:[qe],init:function(e){var t,n;for(t in e=e||{})(n=e[t])instanceof Ye&&(e[t]=n.create(this,t));this.data=e},set:function(e,t){var n,i,r=this.data[e];if(t instanceof Ye&&(t=t.create(this,e)),"object"==typeof e){for(n in e)this.set(n,e[n]);return this}return function o(e,t){var n,i;if(e===t)return!0;if(null===e||null===t)return e===t;if("object"!=typeof e||"object"!=typeof t)return e===t;if(a.isArray(t)){if(e.length!==t.length)return!1;for(n=e.length;n--;)if(!o(e[n],t[n]))return!1}if($e(e)||$e(t))return e===t;for(n in i={},t){if(!o(e[n],t[n]))return!1;i[n]=!0}for(n in e)if(!i[n]&&!o(e[n],t[n]))return!1;return!0}(r,t)||(this.data[e]=t,i={target:this,name:e,value:t,oldValue:r},this.fire("change:"+e,i),this.fire("change",i)),this},get:function(e){return this.data[e]},has:function(e){return e in this.data},bind:function(e){return Ye.create(this,e)},destroy:function(){this.fire("destroy")}}),Ke={},Ze={add:function(e){var t=e.parent();if(t){if(!t._layout||t._layout.isNative())return;Ke[t._id]||(Ke[t._id]=t),Xe||(Xe=!0,R.requestAnimationFrame(function(){var e,t;for(e in Xe=!1,Ke)(t=Ke[e]).state.get("rendered")&&t.reflow();Ke={}},document.body))}},remove:function(e){Ke[e._id]&&delete Ke[e._id]}},Qe="onmousewheel"in document,et=!1,tt=0,nt={Statics:{classPrefix:"mce-"},isRtl:function(){return Je.rtl},classPrefix:"mce-",init:function(e){var t,n,i=this;function r(e){var t;for(e=e.split(" "),t=0;t<e.length;t++)i.classes.add(e[t])}i.settings=e=a.extend({},i.Defaults,e),i._id=e.id||"mceu_"+tt++,i._aria={role:e.role},i._elmCache={},i.$=xe,i.state=new Ge({visible:!0,active:!1,disabled:!1,value:""}),i.data=new Ge(e.data),i.classes=new Ne(function(){i.state.get("rendered")&&(i.getEl().className=this.toString())}),i.classes.prefix=i.classPrefix,(t=e.classes)&&(i.Defaults&&(n=i.Defaults.classes)&&t!==n&&r(n),r(t)),a.each("title text name visible disabled active value".split(" "),function(t){t in e&&i[t](e[t])}),i.on("click",function(){if(i.disabled())return!1}),i.settings=e,i.borderBox=Pe(e.border),i.paddingBox=Pe(e.padding),i.marginBox=Pe(e.margin),e.hidden&&i.hide()},Properties:"parent,name",getContainerElm:function(){var e=D.getUiContainer(this);return e||_e.getContainer()},getParentCtrl:function(e){for(var t,n=this.getRoot().controlIdLookup;e&&n&&!(t=n[e.id]);)e=e.parentNode;return t},initLayoutRect:function(){var e,t,n,i,r,o,s,a,l,u,c=this,d=c.settings,f=c.getEl();e=c.borderBox=c.borderBox||We(f,"border"),c.paddingBox=c.paddingBox||We(f,"padding"),c.marginBox=c.marginBox||We(f,"margin"),u=_e.getSize(f),a=d.minWidth,l=d.minHeight,r=a||u.width,o=l||u.height,n=d.width,i=d.height,s=void 0!==(s=d.autoResize)?s:!n&&!i,n=n||r,i=i||o;var h=e.left+e.right,m=e.top+e.bottom,g=d.maxWidth||65535,p=d.maxHeight||65535;return c._layoutRect=t={x:d.x||0,y:d.y||0,w:n,h:i,deltaW:h,deltaH:m,contentW:n-h,contentH:i-m,innerW:n-h,innerH:i-m,startMinWidth:a||0,startMinHeight:l||0,minW:Math.min(r,g),minH:Math.min(o,p),maxW:g,maxH:p,autoResize:s,scrollW:0},c._lastLayoutRect={},t},layoutRect:function(e){var t,n,i,r,o,s=this,a=s._layoutRect;return a||(a=s.initLayoutRect()),e?(i=a.deltaW,r=a.deltaH,e.x!==undefined&&(a.x=e.x),e.y!==undefined&&(a.y=e.y),e.minW!==undefined&&(a.minW=e.minW),e.minH!==undefined&&(a.minH=e.minH),(n=e.w)!==undefined&&(n=(n=n<a.minW?a.minW:n)>a.maxW?a.maxW:n,a.w=n,a.innerW=n-i),(n=e.h)!==undefined&&(n=(n=n<a.minH?a.minH:n)>a.maxH?a.maxH:n,a.h=n,a.innerH=n-r),(n=e.innerW)!==undefined&&(n=(n=n<a.minW-i?a.minW-i:n)>a.maxW-i?a.maxW-i:n,a.innerW=n,a.w=n+i),(n=e.innerH)!==undefined&&(n=(n=n<a.minH-r?a.minH-r:n)>a.maxH-r?a.maxH-r:n,a.innerH=n,a.h=n+r),e.contentW!==undefined&&(a.contentW=e.contentW),e.contentH!==undefined&&(a.contentH=e.contentH),(t=s._lastLayoutRect).x===a.x&&t.y===a.y&&t.w===a.w&&t.h===a.h||((o=Je.repaintControls)&&o.map&&!o.map[s._id]&&(o.push(s),o.map[s._id]=!0),t.x=a.x,t.y=a.y,t.w=a.w,t.h=a.h),s):a},repaint:function(){var e,t,n,i,r,o,s,a,l,u,c=this;l=document.createRange?function(e){return e}:Math.round,e=c.getEl().style,i=c._layoutRect,a=c._lastRepaintRect||{},o=(r=c.borderBox).left+r.right,s=r.top+r.bottom,i.x!==a.x&&(e.left=l(i.x)+"px",a.x=i.x),i.y!==a.y&&(e.top=l(i.y)+"px",a.y=i.y),i.w!==a.w&&(u=l(i.w-o),e.width=(u>=0?u:0)+"px",a.w=i.w),i.h!==a.h&&(u=l(i.h-s),e.height=(u>=0?u:0)+"px",a.h=i.h),c._hasBody&&i.innerW!==a.innerW&&(u=l(i.innerW),(n=c.getEl("body"))&&((t=n.style).width=(u>=0?u:0)+"px"),a.innerW=i.innerW),c._hasBody&&i.innerH!==a.innerH&&(u=l(i.innerH),(n=n||c.getEl("body"))&&((t=t||n.style).height=(u>=0?u:0)+"px"),a.innerH=i.innerH),c._lastRepaintRect=a,c.fire("repaint",{},!1)},updateLayoutRect:function(){var e=this;e.parent()._lastRect=null,_e.css(e.getEl(),{width:"",height:""}),e._layoutRect=e._lastRepaintRect=e._lastLayoutRect=null,e.initLayoutRect()},on:function(e,t){var n,i,r,o=this;return it(o).on(e,"string"!=typeof(n=t)?n:function(e){return i||o.parentsAndSelf().each(function(e){var t=e.settings.callbacks;if(t&&(i=t[n]))return r=e,!1}),i?i.call(r,e):(e.action=n,void this.fire("execute",e))}),o},off:function(e,t){return it(this).off(e,t),this},fire:function(e,t,n){if((t=t||{}).control||(t.control=this),t=it(this).fire(e,t),!1!==n&&this.parent)for(var i=this.parent();i&&!t.isPropagationStopped();)i.fire(e,t,!1),i=i.parent();return t},hasEventListeners:function(e){return it(this).has(e)},parents:function(e){var t,n=new je;for(t=this.parent();t;t=t.parent())n.add(t);return e&&(n=n.filter(e)),n},parentsAndSelf:function(e){return new je(this).add(this.parents(e))},next:function(){var e=this.parent().items();return e[e.indexOf(this)+1]},prev:function(){var e=this.parent().items();return e[e.indexOf(this)-1]},innerHtml:function(e){return this.$el.html(e),this},getEl:function(e){var t=e?this._id+"-"+e:this._id;return this._elmCache[t]||(this._elmCache[t]=xe("#"+t)[0]),this._elmCache[t]},show:function(){return this.visible(!0)},hide:function(){return this.visible(!1)},focus:function(){try{this.getEl().focus()}catch(e){}return this},blur:function(){return this.getEl().blur(),this},aria:function(e,t){var n=this,i=n.getEl(n.ariaTarget);return void 0===t?n._aria[e]:(n._aria[e]=t,n.state.get("rendered")&&i.setAttribute("role"===e?e:"aria-"+e,t),n)},encode:function(e,t){return!1!==t&&(e=this.translate(e)),(e||"").replace(/[&<>"]/g,function(e){return"&#"+e.charCodeAt(0)+";"})},translate:function(e){return Je.translate?Je.translate(e):e},before:function(e){var t=this.parent();return t&&t.insert(e,t.items().indexOf(this),!0),this},after:function(e){var t=this.parent();return t&&t.insert(e,t.items().indexOf(this)),this},remove:function(){var e,t,n=this,i=n.getEl(),r=n.parent();if(n.items){var o=n.items().toArray();for(t=o.length;t--;)o[t].remove()}r&&r.items&&(e=[],r.items().each(function(t){t!==n&&e.push(t)}),r.items().set(e),r._lastRect=null),n._eventsRoot&&n._eventsRoot===n&&xe(i).off();var s=n.getRoot().controlIdLookup;return s&&delete s[n._id],i&&i.parentNode&&i.parentNode.removeChild(i),n.state.set("rendered",!1),n.state.destroy(),n.fire("remove"),n},renderBefore:function(e){return xe(e).before(this.renderHtml()),this.postRender(),this},renderTo:function(e){return xe(e||this.getContainerElm()).append(this.renderHtml()),this.postRender(),this},preRender:function(){},render:function(){},renderHtml:function(){return'<div id="'+this._id+'" class="'+this.classes+'"></div>'},postRender:function(){var e,t,n,i,r,o=this,s=o.settings;for(i in o.$el=xe(o.getEl()),o.state.set("rendered",!0),s)0===i.indexOf("on")&&o.on(i.substr(2),s[i]);if(o._eventsRoot){for(n=o.parent();!r&&n;n=n.parent())r=n._eventsRoot;if(r)for(i in r._nativeEvents)o._nativeEvents[i]=!0}rt(o),s.style&&(e=o.getEl())&&(e.setAttribute("style",s.style),e.style.cssText=s.style),o.settings.border&&(t=o.borderBox,o.$el.css({"border-top-width":t.top,"border-right-width":t.right,"border-bottom-width":t.bottom,"border-left-width":t.left}));var a=o.getRoot();for(var l in a.controlIdLookup||(a.controlIdLookup={}),a.controlIdLookup[o._id]=o,o._aria)o.aria(l,o._aria[l]);!1===o.state.get("visible")&&(o.getEl().style.display="none"),o.bindStates(),o.state.on("change:visible",function(e){var t,n=e.value;o.state.get("rendered")&&(o.getEl().style.display=!1===n?"none":"",o.getEl().getBoundingClientRect()),(t=o.parent())&&(t._lastRect=null),o.fire(n?"show":"hide"),Ze.add(o)}),o.fire("postrender",{},!1)},bindStates:function(){},scrollIntoView:function(e){var t,n,i,r,o,s,a=this.getEl(),l=a.parentNode,u=function(e,t){var n,i,r=e;for(n=i=0;r&&r!==t&&r.nodeType;)n+=r.offsetLeft||0,i+=r.offsetTop||0,r=r.offsetParent;return{x:n,y:i}}(a,l);return t=u.x,n=u.y,i=a.offsetWidth,r=a.offsetHeight,o=l.clientWidth,s=l.clientHeight,"end"===e?(t-=o-i,n-=s-r):"center"===e&&(t-=o/2-i/2,n-=s/2-r/2),l.scrollLeft=t,l.scrollTop=n,this},getRoot:function(){for(var e,t=this,n=[];t;){if(t.rootControl){e=t.rootControl;break}n.push(t),e=t,t=t.parent()}e||(e=this);for(var i=n.length;i--;)n[i].rootControl=e;return e},reflow:function(){Ze.remove(this);var e=this.parent();return e&&e._layout&&!e._layout.isNative()&&e.reflow(),this}};function it(e){return e._eventDispatcher||(e._eventDispatcher=new Te({scope:e,toggleEvent:function(t,n){n&&Te.isNative(t)&&(e._nativeEvents||(e._nativeEvents={}),e._nativeEvents[t]=!0,e.state.get("rendered")&&rt(e))}})),e._eventDispatcher}function rt(e){var t,n,i,r,o,s;function a(t){var n=e.getParentCtrl(t.target);n&&n.fire(t.type,t)}function l(){var e=r._lastHoverCtrl;e&&(e.fire("mouseleave",{target:e.getEl()}),e.parents().each(function(e){e.fire("mouseleave",{target:e.getEl()})}),r._lastHoverCtrl=null)}function u(t){var n,i,o,s=e.getParentCtrl(t.target),a=r._lastHoverCtrl,l=0;if(s!==a){if(r._lastHoverCtrl=s,(i=s.parents().toArray().reverse()).push(s),a){for((o=a.parents().toArray().reverse()).push(a),l=0;l<o.length&&i[l]===o[l];l++);for(n=o.length-1;n>=l;n--)(a=o[n]).fire("mouseleave",{target:a.getEl()})}for(n=l;n<i.length;n++)(s=i[n]).fire("mouseenter",{target:s.getEl()})}}function c(t){t.preventDefault(),"mousewheel"===t.type?(t.deltaY=-.025*t.wheelDelta,t.wheelDeltaX&&(t.deltaX=-.025*t.wheelDeltaX)):(t.deltaX=0,t.deltaY=t.detail),t=e.fire("wheel",t)}if(o=e._nativeEvents){for((i=e.parents().toArray()).unshift(e),t=0,n=i.length;!r&&t<n;t++)r=i[t]._eventsRoot;for(r||(r=i[i.length-1]||e),e._eventsRoot=r,n=t,t=0;t<n;t++)i[t]._eventsRoot=r;var d=r._delegates;for(s in d||(d=r._delegates={}),o){if(!o)return!1;"wheel"!==s||et?("mouseenter"===s||"mouseleave"===s?r._hasMouseEnter||(xe(r.getEl()).on("mouseleave",l).on("mouseover",u),r._hasMouseEnter=1):d[s]||(xe(r.getEl()).on(s,a),d[s]=!0),o[s]=!1):Qe?xe(e.getEl()).on("mousewheel",c):xe(e.getEl()).on("DOMMouseScroll",c)}}}a.each("text title visible disabled active value".split(" "),function(e){nt[e]=function(t){return 0===arguments.length?this.state.get(e):(void 0!==t&&this.state.set(e,t),this)}});var ot=Je=Me.extend(nt),st=function(e){return!!e.getAttribute("data-mce-tabstop")};function at(e){var t,n,i=e.root;function r(e){return e&&1===e.nodeType}try{t=document.activeElement}catch(y){t=document.body}function o(e){return r(e=e||t)?e.getAttribute("role"):null}function s(e){for(var n,i=e||t;i=i.parentNode;)if(n=o(i))return n}function a(e){var n=t;if(r(n))return n.getAttribute("aria-"+e)}function l(e){var t=e.tagName.toUpperCase();return"INPUT"===t||"TEXTAREA"===t||"SELECT"===t}function u(e){var t=[];return function n(e){if(1===e.nodeType&&"none"!==e.style.display&&!e.disabled){var i;(l(i=e)&&!i.hidden||st(i)||/^(button|menuitem|checkbox|tab|menuitemcheckbox|option|gridcell|slider)$/.test(o(i)))&&t.push(e);for(var r=0;r<e.childNodes.length;r++)n(e.childNodes[r])}}(e||i.getEl()),t}function c(e){var t,i;(i=(e=e||n).parents().toArray()).unshift(e);for(var r=0;r<i.length&&!(t=i[r]).settings.ariaRoot;r++);return t}function d(e,t){return e<0?e=t.length-1:e>=t.length&&(e=0),t[e]&&t[e].focus(),e}function f(e,n){var i=-1,r=c();n=n||u(r.getEl());for(var o=0;o<n.length;o++)n[o]===t&&(i=o);i+=e,r.lastAriaIndex=d(i,n)}function h(){"tablist"===s()?f(-1,u(t.parentNode)):n.parent().submenu?v():f(-1)}function m(){var e=o(),n=s();"tablist"===n?f(1,u(t.parentNode)):"menuitem"===e&&"menu"===n&&a("haspopup")?b():f(1)}function g(){f(-1)}function p(){var e=o(),t=s();"menuitem"===e&&"menubar"===t?b():"button"===e&&a("haspopup")?b({key:"down"}):f(1)}function v(){n.fire("cancel")}function b(e){e=e||{},n.fire("click",{target:t,aria:e})}return n=i.getParentCtrl(t),i.on("keydown",function(e){function i(e,n){l(t)||st(t)||"slider"!==o(t)&&!1!==n(e)&&e.preventDefault()}if(!e.isDefaultPrevented())switch(e.keyCode){case 37:i(e,h);break;case 39:i(e,m);break;case 38:i(e,g);break;case 40:i(e,p);break;case 27:v();break;case 14:case 13:case 32:i(e,b);break;case 9:!function(e){if("tablist"===s()){var t=u(n.getEl("body"))[0];t&&t.focus()}else f(e.shiftKey?-1:1)}(e),e.preventDefault()}}),i.on("focusin",function(e){t=e.target,n=e.control}),{focusFirst:function(e){var t=c(e),n=u(t.getEl());t.settings.ariaRemember&&"lastAriaIndex"in t?d(t.lastAriaIndex,n):d(0,n)}}}var lt={},ut=ot.extend({init:function(e){var t=this;t._super(e),(e=t.settings).fixed&&t.state.set("fixed",!0),t._items=new je,t.isRtl()&&t.classes.add("rtl"),t.bodyClasses=new Ne(function(){t.state.get("rendered")&&(t.getEl("body").className=this.toString())}),t.bodyClasses.prefix=t.classPrefix,t.classes.add("container"),t.bodyClasses.add("container-body"),e.containerCls&&t.classes.add(e.containerCls),t._layout=g.create((e.layout||"")+"layout"),t.settings.items?t.add(t.settings.items):t.add(t.render()),t._hasBody=!0},items:function(){return this._items},find:function(e){return(e=lt[e]=lt[e]||new Fe(e)).find(this)},add:function(e){return this.items().add(this.create(e)).parent(this),this},focus:function(e){var t,n,i,r=this;if(!e||!(n=r.keyboardNav||r.parents().eq(-1)[0].keyboardNav))return i=r.find("*"),r.statusbar&&i.add(r.statusbar.items()),i.each(function(e){if(e.settings.autofocus)return t=null,!1;e.canFocus&&(t=t||e)}),t&&t.focus(),r;n.focusFirst(r)},replace:function(e,t){for(var n,i=this.items(),r=i.length;r--;)if(i[r]===e){i[r]=t;break}r>=0&&((n=t.getEl())&&n.parentNode.removeChild(n),(n=e.getEl())&&n.parentNode.removeChild(n)),t.parent(this)},create:function(e){var t,n=this,i=[];return a.isArray(e)||(e=[e]),a.each(e,function(e){e&&(e instanceof ot||("string"==typeof e&&(e={type:e}),t=a.extend({},n.settings.defaults,e),e.type=t.type=t.type||e.type||n.settings.defaultType||(t.defaults?t.defaults.type:null),e=g.create(t)),i.push(e))}),i},renderNew:function(){var e=this;return e.items().each(function(t,n){var i;t.parent(e),t.state.get("rendered")||((i=e.getEl("body")).hasChildNodes()&&n<=i.childNodes.length-1?xe(i.childNodes[n]).before(t.renderHtml()):xe(i).append(t.renderHtml()),t.postRender(),Ze.add(t))}),e._layout.applyClasses(e.items().filter(":visible")),e._lastRect=null,e},append:function(e){return this.add(e).renderNew()},prepend:function(e){return this.items().set(this.create(e).concat(this.items().toArray())),this.renderNew()},insert:function(e,t,n){var i,r,o;return e=this.create(e),i=this.items(),!n&&t<i.length-1&&(t+=1),t>=0&&t<i.length&&(r=i.slice(0,t).toArray(),o=i.slice(t).toArray(),i.set(r.concat(e,o))),this.renderNew()},fromJSON:function(e){for(var t in e)this.find("#"+t).value(e[t]);return this},toJSON:function(){var e={};return this.find("*").each(function(t){var n=t.name(),i=t.value();n&&void 0!==i&&(e[n]=i)}),e},renderHtml:function(){var e=this,t=e._layout,n=this.settings.role;return e.preRender(),t.preRender(e),'<div id="'+e._id+'" class="'+e.classes+'"'+(n?' role="'+this.settings.role+'"':"")+'><div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+(e.settings.html||"")+t.renderHtml(e)+"</div></div>"},postRender:function(){var e,t=this;return t.items().exec("postRender"),t._super(),t._layout.postRender(t),t.state.set("rendered",!0),t.settings.style&&t.$el.css(t.settings.style),t.settings.border&&(e=t.borderBox,t.$el.css({"border-top-width":e.top,"border-right-width":e.right,"border-bottom-width":e.bottom,"border-left-width":e.left})),t.parent()||(t.keyboardNav=at({root:t})),t},initLayoutRect:function(){var e=this._super();return this._layout.recalc(this),e},recalc:function(){var e=this,t=e._layoutRect,n=e._lastRect;if(!n||n.w!==t.w||n.h!==t.h)return e._layout.recalc(e),t=e.layoutRect(),e._lastRect={x:t.x,y:t.y,w:t.w,h:t.h},!0},reflow:function(){var e;if(Ze.remove(this),this.visible()){for(ot.repaintControls=[],ot.repaintControls.map={},this.recalc(),e=ot.repaintControls.length;e--;)ot.repaintControls[e].repaint();"flow"!==this.settings.layout&&"stack"!==this.settings.layout&&this.repaint(),ot.repaintControls=[]}return this}});function ct(e){var t,n;if(e.changedTouches)for(t="screenX screenY pageX pageY clientX clientY".split(" "),n=0;n<t.length;n++)e[t[n]]=e.changedTouches[0][t[n]]}function dt(e,t){var n,i,r,o,s,a,l,u=t.document||document;t=t||{};var c=u.getElementById(t.handle||e);r=function(e){var r,d,f,h,m,g,p,v,b,y,x,w=(r=u,b=Math.max,d=r.documentElement,f=r.body,h=b(d.scrollWidth,f.scrollWidth),m=b(d.clientWidth,f.clientWidth),g=b(d.offsetWidth,f.offsetWidth),p=b(d.scrollHeight,f.scrollHeight),v=b(d.clientHeight,f.clientHeight),{width:h<g?m:h,height:p<b(d.offsetHeight,f.offsetHeight)?v:p});ct(e),e.preventDefault(),i=e.button,y=c,a=e.screenX,l=e.screenY,x=window.getComputedStyle?window.getComputedStyle(y,null).getPropertyValue("cursor"):y.runtimeStyle.cursor,n=xe("<div></div>").css({position:"absolute",top:0,left:0,width:w.width,height:w.height,zIndex:2147483647,opacity:1e-4,cursor:x}).appendTo(u.body),xe(u).on("mousemove touchmove",s).on("mouseup touchend",o),t.start(e)},s=function(e){if(ct(e),e.button!==i)return o(e);e.deltaX=e.screenX-a,e.deltaY=e.screenY-l,e.preventDefault(),t.drag(e)},o=function(e){ct(e),xe(u).off("mousemove touchmove",s).off("mouseup touchend",o),n.remove(),t.stop&&t.stop(e)},this.destroy=function(){xe(c).off()},xe(c).on("mousedown touchstart",r)}var ft,ht,mt,gt,pt={init:function(){this.on("repaint",this.renderScroll)},renderScroll:function(){var e=this,t=2;function n(){var n,i,r;function o(r,o,s,a,l,u){var c,d,f,h,m,g,p,v;if(d=e.getEl("scroll"+r)){if(p=o.toLowerCase(),v=s.toLowerCase(),xe(e.getEl("absend")).css(p,e.layoutRect()[a]-1),!l)return void xe(d).css("display","none");xe(d).css("display","block"),c=e.getEl("body"),f=e.getEl("scroll"+r+"t"),h=c["client"+s]-2*t,m=(h-=n&&i?d["client"+u]:0)/c["scroll"+s],(g={})[p]=c["offset"+o]+t,g[v]=h,xe(d).css(g),(g={})[p]=c["scroll"+o]*m,g[v]=h*m,xe(f).css(g)}}r=e.getEl("body"),n=r.scrollWidth>r.clientWidth,i=r.scrollHeight>r.clientHeight,o("h","Left","Width","contentW",n,"Height"),o("v","Top","Height","contentH",i,"Width")}e.settings.autoScroll&&(e._hasScroll||(e._hasScroll=!0,function(){function n(n,i,r,o,s){var a,l=e._id+"-scroll"+n,u=e.classPrefix;xe(e.getEl()).append('<div id="'+l+'" class="'+u+"scrollbar "+u+"scrollbar-"+n+'"><div id="'+l+'t" class="'+u+'scrollbar-thumb"></div></div>'),e.draghelper=new dt(l+"t",{start:function(){a=e.getEl("body")["scroll"+i],xe("#"+l).addClass(u+"active")},drag:function(l){var u,c,d,f,h=e.layoutRect();c=h.contentW>h.innerW,d=h.contentH>h.innerH,f=e.getEl("body")["client"+r]-2*t,u=(f-=c&&d?e.getEl("scroll"+n)["client"+s]:0)/e.getEl("body")["scroll"+r],e.getEl("body")["scroll"+i]=a+l["delta"+o]/u},stop:function(){xe("#"+l).removeClass(u+"active")}})}e.classes.add("scroll"),n("v","Top","Height","Y","Width"),n("h","Left","Width","X","Height")}(),e.on("wheel",function(t){var i=e.getEl("body");i.scrollLeft+=10*(t.deltaX||0),i.scrollTop+=10*t.deltaY,n()}),xe(e.getEl("body")).on("scroll",n)),n())}},vt=ut.extend({Defaults:{layout:"fit",containerCls:"panel"},Mixins:[pt],renderHtml:function(){var e=this,t=e._layout,n=e.settings.html;return e.preRender(),t.preRender(e),void 0===n?n='<div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+t.renderHtml(e)+"</div>":("function"==typeof n&&(n=n.call(e)),e._hasBody=!1),'<div id="'+e._id+'" class="'+e.classes+'" hidefocus="1" tabindex="-1" role="group">'+(e._preBodyHtml||"")+n+"</div>"}}),bt={resizeToContent:function(){this._layoutRect.autoResize=!0,this._lastRect=null,this.reflow()},resizeTo:function(e,t){if(e<=1||t<=1){var n=_e.getWindowSize();e=e<=1?e*n.w:e,t=t<=1?t*n.h:t}return this._layoutRect.autoResize=!1,this.layoutRect({minW:e,minH:t,w:e,h:t}).reflow()},resizeBy:function(e,t){var n=this.layoutRect();return this.resizeTo(n.w+e,n.h+t)}},yt=[],xt=[];function wt(e,t){for(;e;){if(e===t)return!0;e=e.parent()}}function _t(){ft||(ft=function(e){2!==e.button&&function(e){for(var t=yt.length;t--;){var n=yt[t],i=n.getParentCtrl(e.target);if(n.settings.autohide){if(i&&(wt(i,n)||n.parent()===i))continue;(e=n.fire("autohide",{target:e.target})).isDefaultPrevented()||n.hide()}}}(e)},xe(document).on("click touchstart",ft))}function Rt(e){var t=_e.getViewPort().y;function n(t,n){for(var i,r=0;r<yt.length;r++)if(yt[r]!==e)for(i=yt[r].parent();i&&(i=i.parent());)i===e&&yt[r].fixed(t).moveBy(0,n).repaint()}e.settings.autofix&&(e.state.get("fixed")?e._autoFixY>t&&(e.fixed(!1).layoutRect({y:e._autoFixY}).repaint(),n(!1,e._autoFixY-t)):(e._autoFixY=e.layoutRect().y,e._autoFixY<t&&(e.fixed(!0).layoutRect({y:0}).repaint(),n(!0,t-e._autoFixY))))}function Ct(e,t){var n,i,r=kt.zIndex||65535;if(e)xt.push(t);else for(n=xt.length;n--;)xt[n]===t&&xt.splice(n,1);if(xt.length)for(n=0;n<xt.length;n++)xt[n].modal&&(r++,i=xt[n]),xt[n].getEl().style.zIndex=r,xt[n].zIndex=r,r++;var o=xe("#"+t.classPrefix+"modal-block",t.getContainerElm())[0];i?xe(o).css("z-index",i.zIndex-1):o&&(o.parentNode.removeChild(o),gt=!1),kt.currentZIndex=r}var kt=vt.extend({Mixins:[Se,bt],init:function(e){var t=this;t._super(e),t._eventsRoot=t,t.classes.add("floatpanel"),e.autohide&&(_t(),function(){if(!mt){var e=document.documentElement,t=e.clientWidth,n=e.clientHeight;mt=function(){document.all&&t===e.clientWidth&&n===e.clientHeight||(t=e.clientWidth,n=e.clientHeight,kt.hideAll())},xe(window).on("resize",mt)}}(),yt.push(t)),e.autofix&&(ht||(ht=function(){var e;for(e=yt.length;e--;)Rt(yt[e])},xe(window).on("scroll",ht)),t.on("move",function(){Rt(this)})),t.on("postrender show",function(e){if(e.control===t){var n,i=t.classPrefix;t.modal&&!gt&&((n=xe("#"+i+"modal-block",t.getContainerElm()))[0]||(n=xe('<div id="'+i+'modal-block" class="'+i+"reset "+i+'fade"></div>').appendTo(t.getContainerElm())),R.setTimeout(function(){n.addClass(i+"in"),xe(t.getEl()).addClass(i+"in")}),gt=!0),Ct(!0,t)}}),t.on("show",function(){t.parents().each(function(e){if(e.state.get("fixed"))return t.fixed(!0),!1})}),e.popover&&(t._preBodyHtml='<div class="'+t.classPrefix+'arrow"></div>',t.classes.add("popover").add("bottom").add(t.isRtl()?"end":"start")),t.aria("label",e.ariaLabel),t.aria("labelledby",t._id),t.aria("describedby",t.describedBy||t._id+"-none")},fixed:function(e){var t=this;if(t.state.get("fixed")!==e){if(t.state.get("rendered")){var n=_e.getViewPort();e?t.layoutRect().y-=n.y:t.layoutRect().y+=n.y}t.classes.toggle("fixed",e),t.state.set("fixed",e)}return t},show:function(){var e,t=this._super();for(e=yt.length;e--&&yt[e]!==this;);return-1===e&&yt.push(this),t},hide:function(){return Et(this),Ct(!1,this),this._super()},hideAll:function(){kt.hideAll()},close:function(){return this.fire("close").isDefaultPrevented()||(this.remove(),Ct(!1,this)),this},remove:function(){Et(this),this._super()},postRender:function(){return this.settings.bodyRole&&this.getEl("body").setAttribute("role",this.settings.bodyRole),this._super()}});function Et(e){var t;for(t=yt.length;t--;)yt[t]===e&&yt.splice(t,1);for(t=xt.length;t--;)xt[t]===e&&xt.splice(t,1)}kt.hideAll=function(){for(var e=yt.length;e--;){var t=yt[e];t&&t.settings.autohide&&(t.hide(),yt.splice(e,1))}};var Ht=function(e,t){return!(!e||t.settings.ui_container)},St=function(e,t,n){var i,r,o=m.DOM,s=e.getParam("fixed_toolbar_container");s&&(r=o.select(s)[0]);var a=function(){if(i&&i.moveRel&&i.visible()&&!i._fixed){var t=e.selection.getScrollContainer(),n=e.getBody(),r=0,s=0;if(t){var a=o.getPos(n),l=o.getPos(t);r=Math.max(0,l.x-a.x),s=Math.max(0,l.y-a.y)}i.fixed(!1).moveRel(n,e.rtl?["tr-br","br-tr"]:["tl-bl","bl-tl","tr-br"]).moveBy(r,s)}},u=function(){i&&(i.show(),a(),o.addClass(e.getBody(),"mce-edit-focus"))},d=function(){i&&(i.hide(),kt.hideAll(),o.removeClass(e.getBody(),"mce-edit-focus"))},h=function(){i?i.visible()||u():(i=t.panel=g.create({type:r?"panel":"floatpanel",role:"application",classes:"tinymce tinymce-inline",layout:"flex",direction:"column",align:"stretch",autohide:!1,autofix:Ht(r,e),fixed:Ht(r,e),border:1,items:[!1===l(e)?null:{type:"menubar",border:"0 0 1 0",items:oe(e)},B(e,c(e))]}),D.setUiContainer(e,i),y(e),r?i.renderTo(r).reflow():i.renderTo().reflow(),w(e,i),u(),F(e),e.on("nodeChange",a),e.on("ResizeWindow",a),e.on("activate",u),e.on("deactivate",d),e.nodeChanged())};return e.settings.content_editable=!0,e.on("focus",function(){!1===f(e)&&n.skinUiCss?o.styleSheetLoader.load(n.skinUiCss,h,h):h()}),e.on("blur hide",d),e.on("remove",function(){i&&(i.remove(),i=null)}),!1===f(e)&&n.skinUiCss?o.styleSheetLoader.load(n.skinUiCss,pe(e)):pe(e)(),{}};function Mt(e,t){var n,i,r=this,o=ot.classPrefix;r.show=function(s,a){function l(){n&&(xe(e).append('<div class="'+o+"throbber"+(t?" "+o+"throbber-inline":"")+'"></div>'),a&&a())}return r.hide(),n=!0,s?i=R.setTimeout(l,s):l(),r},r.hide=function(){var t=e.lastChild;return R.clearTimeout(i),t&&-1!==t.className.indexOf("throbber")&&t.parentNode.removeChild(t),n=!1,r}}var Tt=function(e,t){var n;e.on("ProgressState",function(e){n=n||new Mt(t.panel.getEl("body")),e.state?n.show(e.time):n.hide()})},Pt=function(e,t,n){var i=function(e){var t=e.settings,n=t.skin,i=t.skin_url;if(!1!==n){var r=n||"lightgray";i=i?e.documentBaseURI.toAbsolute(i):s.baseURL+"/skins/"+r}return i}(e);return i&&(n.skinUiCss=i+"/skin.min.css",e.contentCSS.push(i+"/content"+(e.inline?".inline":"")+".min.css")),Tt(e,t),e.getParam("inline",!1,"boolean")?St(e,t,n):ye(e,t,n)},Wt=ot.extend({Mixins:[Se],Defaults:{classes:"widget tooltip tooltip-n"},renderHtml:function(){var e=this,t=e.classPrefix;return'<div id="'+e._id+'" class="'+e.classes+'" role="presentation"><div class="'+t+'tooltip-arrow"></div><div class="'+t+'tooltip-inner">'+e.encode(e.state.get("text"))+"</div></div>"},bindStates:function(){var e=this;return e.state.on("change:text",function(t){e.getEl().lastChild.innerHTML=e.encode(t.value)}),e._super()},repaint:function(){var e,t;e=this.getEl().style,t=this._layoutRect,e.left=t.x+"px",e.top=t.y+"px",e.zIndex=131070}}),Dt=ot.extend({init:function(e){var t=this;t._super(e),e=t.settings,t.canFocus=!0,e.tooltip&&!1!==Dt.tooltips&&(t.on("mouseenter",function(n){var i=t.tooltip().moveTo(-65535);if(n.control===t){var r=i.text(e.tooltip).show().testMoveRel(t.getEl(),["bc-tc","bc-tl","bc-tr"]);i.classes.toggle("tooltip-n","bc-tc"===r),i.classes.toggle("tooltip-nw","bc-tl"===r),i.classes.toggle("tooltip-ne","bc-tr"===r),i.moveRel(t.getEl(),r)}else i.hide()}),t.on("mouseleave mousedown click",function(){t.tooltip().remove(),t._tooltip=null})),t.aria("label",e.ariaLabel||e.tooltip)},tooltip:function(){return this._tooltip||(this._tooltip=new Wt({type:"tooltip"}),D.inheritUiContainer(this,this._tooltip),this._tooltip.renderTo()),this._tooltip},postRender:function(){var e=this,t=e.settings;e._super(),e.parent()||!t.width&&!t.height||(e.initLayoutRect(),e.repaint()),t.autofocus&&e.focus()},bindStates:function(){var e=this;function t(t){e.aria("disabled",t),e.classes.toggle("disabled",t)}function n(t){e.aria("pressed",t),e.classes.toggle("active",t)}return e.state.on("change:disabled",function(e){t(e.value)}),e.state.on("change:active",function(e){n(e.value)}),e.state.get("disabled")&&t(!0),e.state.get("active")&&n(!0),e._super()},remove:function(){this._super(),this._tooltip&&(this._tooltip.remove(),this._tooltip=null)}}),Nt=Dt.extend({Defaults:{value:0},init:function(e){this._super(e),this.classes.add("progress"),this.settings.filter||(this.settings.filter=function(e){return Math.round(e)})},renderHtml:function(){var e=this._id,t=this.classPrefix;return'<div id="'+e+'" class="'+this.classes+'"><div class="'+t+'bar-container"><div class="'+t+'bar"></div></div><div class="'+t+'text">0%</div></div>'},postRender:function(){return this._super(),this.value(this.settings.value),this},bindStates:function(){var e=this;function t(t){t=e.settings.filter(t),e.getEl().lastChild.innerHTML=t+"%",e.getEl().firstChild.firstChild.style.width=t+"%"}return e.state.on("change:value",function(e){t(e.value)}),t(e.state.get("value")),e._super()}}),At=function(e,t){e.getEl().lastChild.textContent=t+(e.progressBar?" "+e.progressBar.value()+"%":"")},Bt=ot.extend({Mixins:[Se],Defaults:{classes:"widget notification"},init:function(e){var t=this;t._super(e),t.maxWidth=e.maxWidth,e.text&&t.text(e.text),e.icon&&(t.icon=e.icon),e.color&&(t.color=e.color),e.type&&t.classes.add("notification-"+e.type),e.timeout&&(e.timeout<0||e.timeout>0)&&!e.closeButton?t.closeButton=!1:(t.classes.add("has-close"),t.closeButton=!0),e.progressBar&&(t.progressBar=new Nt),t.on("click",function(e){-1!==e.target.className.indexOf(t.classPrefix+"close")&&t.close()})},renderHtml:function(){var e,t=this,n=t.classPrefix,i="",r="",o="";return t.icon&&(i='<i class="'+n+"ico "+n+"i-"+t.icon+'"></i>'),e=' style="max-width: '+t.maxWidth+"px;"+(t.color?"background-color: "+t.color+';"':'"'),t.closeButton&&(r='<button type="button" class="'+n+'close" aria-hidden="true">\xd7</button>'),t.progressBar&&(o=t.progressBar.renderHtml()),'<div id="'+t._id+'" class="'+t.classes+'"'+e+' role="presentation">'+i+'<div class="'+n+'notification-inner">'+t.state.get("text")+"</div>"+o+r+'<div style="clip: rect(1px, 1px, 1px, 1px);height: 1px;overflow: hidden;position: absolute;width: 1px;" aria-live="assertive" aria-relevant="additions" aria-atomic="true"></div></div>'},postRender:function(){var e=this;return R.setTimeout(function(){e.$el.addClass(e.classPrefix+"in"),At(e,e.state.get("text"))},100),e._super()},bindStates:function(){var e=this;return e.state.on("change:text",function(t){e.getEl().firstChild.innerHTML=t.value,At(e,t.value)}),e.progressBar&&(e.progressBar.bindStates(),e.progressBar.state.on("change:value",function(t){At(e,e.state.get("text"))})),e._super()},close:function(){return this.fire("close").isDefaultPrevented()||this.remove(),this},repaint:function(){var e,t;e=this.getEl().style,t=this._layoutRect,e.left=t.x+"px",e.top=t.y+"px",e.zIndex=65534}});function Ot(e){var t=function(e){return e.inline?e.getElement():e.getContentAreaContainer()};return{open:function(n,i){var r,o=a.extend(n,{maxWidth:(r=t(e),_e.getSize(r).width)}),s=new Bt(o);return s.args=o,o.timeout>0&&(s.timer=setTimeout(function(){s.close(),i()},o.timeout)),s.on("close",function(){i()}),s.renderTo(),s},close:function(e){e.close()},reposition:function(n){var i;i=n,ee.each(i,function(e){e.moveTo(0,0)}),function(n){if(n.length>0){var i=n.slice(0,1)[0],r=t(e);i.moveRel(r,"tc-tc"),ee.each(n,function(e,t){t>0&&e.moveRel(n[t-1].getEl(),"bc-tc")})}}(n)},getArgs:function(e){return e.args}}}var zt=[],Lt="";function It(e){var t,n=xe("meta[name=viewport]")[0];!1!==de.overrideViewPort&&(n||((n=document.createElement("meta")).setAttribute("name","viewport"),document.getElementsByTagName("head")[0].appendChild(n)),(t=n.getAttribute("content"))&&void 0!==Lt&&(Lt=t),n.setAttribute("content",e?"width=device-width,initial-scale=1.0,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0":Lt))}function Ft(e,t){(function(){for(var e=0;e<zt.length;e++)if(zt[e]._fullscreen)return!0;return!1})()&&!1===t&&xe([document.documentElement,document.body]).removeClass(e+"fullscreen")}var Ut=kt.extend({modal:!0,Defaults:{border:1,layout:"flex",containerCls:"panel",role:"dialog",callbacks:{submit:function(){this.fire("submit",{data:this.toJSON()})},close:function(){this.close()}}},init:function(e){var t=this;t._super(e),t.isRtl()&&t.classes.add("rtl"),t.classes.add("window"),t.bodyClasses.add("window-body"),t.state.set("fixed",!0),e.buttons&&(t.statusbar=new vt({layout:"flex",border:"1 0 0 0",spacing:3,padding:10,align:"center",pack:t.isRtl()?"start":"end",defaults:{type:"button"},items:e.buttons}),t.statusbar.classes.add("foot"),t.statusbar.parent(t)),t.on("click",function(e){var n=t.classPrefix+"close";(_e.hasClass(e.target,n)||_e.hasClass(e.target.parentNode,n))&&t.close()}),t.on("cancel",function(){t.close()}),t.aria("describedby",t.describedBy||t._id+"-none"),t.aria("label",e.title),t._fullscreen=!1},recalc:function(){var e,t,n,i,r=this,o=r.statusbar;r._fullscreen&&(r.layoutRect(_e.getWindowSize()),r.layoutRect().contentH=r.layoutRect().innerH),r._super(),e=r.layoutRect(),r.settings.title&&!r._fullscreen&&(t=e.headerW)>e.w&&(n=e.x-Math.max(0,t/2),r.layoutRect({w:t,x:n}),i=!0),o&&(o.layoutRect({w:r.layoutRect().innerW}).recalc(),(t=o.layoutRect().minW+e.deltaW)>e.w&&(n=e.x-Math.max(0,t-e.w),r.layoutRect({w:t,x:n}),i=!0)),i&&r.recalc()},initLayoutRect:function(){var e,t=this,n=t._super(),i=0;if(t.settings.title&&!t._fullscreen){e=t.getEl("head");var r=_e.getSize(e);n.headerW=r.width,n.headerH=r.height,i+=n.headerH}t.statusbar&&(i+=t.statusbar.layoutRect().h),n.deltaH+=i,n.minH+=i,n.h+=i;var o=_e.getWindowSize();return n.x=t.settings.x||Math.max(0,o.w/2-n.w/2),n.y=t.settings.y||Math.max(0,o.h/2-n.h/2),n},renderHtml:function(){var e=this,t=e._layout,n=e._id,i=e.classPrefix,r=e.settings,o="",s="",a=r.html;return e.preRender(),t.preRender(e),r.title&&(o='<div id="'+n+'-head" class="'+i+'window-head"><div id="'+n+'-title" class="'+i+'title">'+e.encode(r.title)+'</div><div id="'+n+'-dragh" class="'+i+'dragh"></div><button type="button" class="'+i+'close" aria-hidden="true"><i class="mce-ico mce-i-remove"></i></button></div>'),r.url&&(a='<iframe src="'+r.url+'" tabindex="-1"></iframe>'),void 0===a&&(a=t.renderHtml(e)),e.statusbar&&(s=e.statusbar.renderHtml()),'<div id="'+n+'" class="'+e.classes+'" hidefocus="1"><div class="'+e.classPrefix+'reset" role="application">'+o+'<div id="'+n+'-body" class="'+e.bodyClasses+'">'+a+"</div>"+s+"</div></div>"},fullscreen:function(e){var t,n,i=this,r=document.documentElement,o=i.classPrefix;if(e!==i._fullscreen)if(xe(window).on("resize",function(){var e;if(i._fullscreen)if(t)i._timer||(i._timer=R.setTimeout(function(){var e=_e.getWindowSize();i.moveTo(0,0).resizeTo(e.w,e.h),i._timer=0},50));else{e=(new Date).getTime();var n=_e.getWindowSize();i.moveTo(0,0).resizeTo(n.w,n.h),(new Date).getTime()-e>50&&(t=!0)}}),n=i.layoutRect(),i._fullscreen=e,e){i._initial={x:n.x,y:n.y,w:n.w,h:n.h},i.borderBox=Pe("0"),i.getEl("head").style.display="none",n.deltaH-=n.headerH+2,xe([r,document.body]).addClass(o+"fullscreen"),i.classes.add("fullscreen");var s=_e.getWindowSize();i.moveTo(0,0).resizeTo(s.w,s.h)}else i.borderBox=Pe(i.settings.border),i.getEl("head").style.display="",n.deltaH+=n.headerH,xe([r,document.body]).removeClass(o+"fullscreen"),i.classes.remove("fullscreen"),i.moveTo(i._initial.x,i._initial.y).resizeTo(i._initial.w,i._initial.h);return i.reflow()},postRender:function(){var e,t=this;setTimeout(function(){t.classes.add("in"),t.fire("open")},0),t._super(),t.statusbar&&t.statusbar.postRender(),t.focus(),this.dragHelper=new dt(t._id+"-dragh",{start:function(){e={x:t.layoutRect().x,y:t.layoutRect().y}},drag:function(n){t.moveTo(e.x+n.deltaX,e.y+n.deltaY)}}),t.on("submit",function(e){e.isDefaultPrevented()||t.close()}),zt.push(t),It(!0)},submit:function(){return this.fire("submit",{data:this.toJSON()})},remove:function(){var e,t=this;for(t.dragHelper.destroy(),t._super(),t.statusbar&&this.statusbar.remove(),Ft(t.classPrefix,!1),e=zt.length;e--;)zt[e]===t&&zt.splice(e,1);It(zt.length>0)},getContentWindow:function(){var e=this.getEl().getElementsByTagName("iframe")[0];return e?e.contentWindow:null}});!function(){if(!de.desktop){var e={w:window.innerWidth,h:window.innerHeight};R.setInterval(function(){var t=window.innerWidth,n=window.innerHeight;e.w===t&&e.h===n||(e={w:t,h:n},xe(window).trigger("resize"))},100)}xe(window).on("resize",function(){var e,t,n=_e.getWindowSize();for(e=0;e<zt.length;e++)t=zt[e].layoutRect(),zt[e].moveTo(zt[e].settings.x||Math.max(0,n.w/2-t.w/2),zt[e].settings.y||Math.max(0,n.h/2-t.h/2))})}();var Vt,jt=Ut.extend({init:function(e){e={border:1,padding:20,layout:"flex",pack:"center",align:"center",containerCls:"panel",autoScroll:!0,buttons:{type:"button",text:"Ok",action:"ok"},items:{type:"label",multiline:!0,maxWidth:500,maxHeight:200}},this._super(e)},Statics:{OK:1,OK_CANCEL:2,YES_NO:3,YES_NO_CANCEL:4,msgBox:function(e){var t,n=e.callback||function(){};function i(e,t,i){return{type:"button",text:e,subtype:i?"primary":"",onClick:function(e){e.control.parents()[1].close(),n(t)}}}switch(e.buttons){case jt.OK_CANCEL:t=[i("Ok",!0,!0),i("Cancel",!1)];break;case jt.YES_NO:case jt.YES_NO_CANCEL:t=[i("Yes",1,!0),i("No",0)],e.buttons===jt.YES_NO_CANCEL&&t.push(i("Cancel",-1));break;default:t=[i("Ok",!0,!0)]}return new Ut({padding:20,x:e.x,y:e.y,minWidth:300,minHeight:100,layout:"flex",pack:"center",align:"center",buttons:t,title:e.title,role:"alertdialog",items:{type:"label",multiline:!0,maxWidth:500,maxHeight:200,text:e.text},onPostRender:function(){this.aria("describedby",this.items()[0]._id)},onClose:e.onClose,onCancel:function(){n(!1)}}).renderTo(document.body).reflow()},alert:function(e,t){return"string"==typeof e&&(e={text:e}),e.callback=t,jt.msgBox(e)},confirm:function(e,t){return"string"==typeof e&&(e={text:e}),e.callback=t,e.buttons=jt.OK_CANCEL,jt.msgBox(e)}}}),Yt=function(e){return{renderUI:function(t){return Pt(e,this,t)},resizeTo:function(t,n){return ue(e,t,n)},resizeBy:function(t,n){return ce(e,t,n)},getNotificationManagerImpl:function(){return Ot(e)},getWindowManagerImpl:function(){return{open:function(e,t,n){var i;return e.title=e.title||" ",e.url=e.url||e.file,e.url&&(e.width=parseInt(e.width||320,10),e.height=parseInt(e.height||240,10)),e.body&&(e.items={defaults:e.defaults,type:e.bodyType||"form",items:e.body,data:e.data,callbacks:e.commands}),e.url||e.buttons||(e.buttons=[{text:"Ok",subtype:"primary",onclick:function(){i.find("form")[0].submit()}},{text:"Cancel",onclick:function(){i.close()}}]),(i=new Ut(e)).on("close",function(){n(i)}),e.data&&i.on("postRender",function(){this.find("*").each(function(t){var n=t.name();n in e.data&&t.value(e.data[n])})}),i.features=e||{},i.params=t||{},i=i.renderTo(document.body).reflow()},alert:function(e,t,n){var i;return(i=jt.alert(e,function(){t()})).on("close",function(){n(i)}),i},confirm:function(e,t,n){var i;return(i=jt.confirm(e,function(e){t(e)})).on("close",function(){n(i)}),i},close:function(e){e.close()},getParams:function(e){return e.params},setParams:function(e,t){e.params=t}}}}},qt=Me.extend({Defaults:{firstControlClass:"first",lastControlClass:"last"},init:function(e){this.settings=a.extend({},this.Defaults,e)},preRender:function(e){e.bodyClasses.add(this.settings.containerClass)},applyClasses:function(e){var t,n,i,r,o=this.settings;t=o.firstControlClass,n=o.lastControlClass,e.each(function(e){e.classes.remove(t).remove(n).add(o.controlClass),e.visible()&&(i||(i=e),r=e)}),i&&i.classes.add(t),r&&r.classes.add(n)},renderHtml:function(e){var t="";return this.applyClasses(e.items()),e.items().each(function(e){t+=e.renderHtml()}),t},recalc:function(){},postRender:function(){},isNative:function(){return!1}}),$t=qt.extend({Defaults:{containerClass:"abs-layout",controlClass:"abs-layout-item"},recalc:function(e){e.items().filter(":visible").each(function(e){var t=e.settings;e.layoutRect({x:t.x,y:t.y,w:t.w,h:t.h}),e.recalc&&e.recalc()})},renderHtml:function(e){return'<div id="'+e._id+'-absend" class="'+e.classPrefix+'abs-end"></div>'+this._super(e)}}),Xt=Dt.extend({Defaults:{classes:"widget btn",role:"button"},init:function(e){var t,n=this;n._super(e),e=n.settings,t=n.settings.size,n.on("click mousedown",function(e){e.preventDefault()}),n.on("touchstart",function(e){n.fire("click",e),e.preventDefault()}),e.subtype&&n.classes.add(e.subtype),t&&n.classes.add("btn-"+t),e.icon&&n.icon(e.icon)},icon:function(e){return arguments.length?(this.state.set("icon",e),this):this.state.get("icon")},repaint:function(){var e,t=this.getEl().firstChild;t&&((e=t.style).width=e.height="100%"),this._super()},renderHtml:function(){var e,t,n=this,i=n._id,r=n.classPrefix,o=n.state.get("icon"),s=n.state.get("text"),a="",l=n.settings;return(e=l.image)?(o="none","string"!=typeof e&&(e=window.getSelection?e[0]:e[1]),e=" style=\"background-image: url('"+e+"')\""):e="",s&&(n.classes.add("btn-has-text"),a='<span class="'+r+'txt">'+n.encode(s)+"</span>"),o=o?r+"ico "+r+"i-"+o:"",t="boolean"==typeof l.active?' aria-pressed="'+l.active+'"':"",'<div id="'+i+'" class="'+n.classes+'" tabindex="-1"'+t+'><button id="'+i+'-button" role="presentation" type="button" tabindex="-1">'+(o?'<i class="'+o+'"'+e+"></i>":"")+a+"</button></div>"},bindStates:function(){var e=this,t=e.$,n=e.classPrefix+"txt";function i(i){var r=t("span."+n,e.getEl());i?(r[0]||(t("button:first",e.getEl()).append('<span class="'+n+'"></span>'),r=t("span."+n,e.getEl())),r.html(e.encode(i))):r.remove(),e.classes.toggle("btn-has-text",!!i)}return e.state.on("change:text",function(e){i(e.value)}),e.state.on("change:icon",function(t){var n=t.value,r=e.classPrefix;e.settings.icon=n,n=n?r+"ico "+r+"i-"+e.settings.icon:"";var o=e.getEl().firstChild,s=o.getElementsByTagName("i")[0];n?(s&&s===o.firstChild||(s=document.createElement("i"),o.insertBefore(s,o.firstChild)),s.className=n):s&&o.removeChild(s),i(e.state.get("text"))}),e._super()}}),Jt=Xt.extend({init:function(e){e=a.extend({text:"Browse...",multiple:!1,accept:null},e),this._super(e),this.classes.add("browsebutton"),e.multiple&&this.classes.add("multiple")},postRender:function(){var e=this,t=_e.create("input",{type:"file",id:e._id+"-browse",accept:e.settings.accept});e._super(),xe(t).on("change",function(t){var n=t.target.files;e.value=function(){return n.length?e.settings.multiple?n:n[0]:null},t.preventDefault(),n.length&&e.fire("change",t)}),xe(t).on("click",function(e){e.stopPropagation()}),xe(e.getEl("button")).on("click",function(e){e.stopPropagation(),t.click()}),e.getEl().appendChild(t)},remove:function(){xe(this.getEl("button")).off(),xe(this.getEl("input")).off(),this._super()}}),Gt=ut.extend({Defaults:{defaultType:"button",role:"group"},renderHtml:function(){var e=this,t=e._layout;return e.classes.add("btn-group"),e.preRender(),t.preRender(e),'<div id="'+e._id+'" class="'+e.classes+'"><div id="'+e._id+'-body">'+(e.settings.html||"")+t.renderHtml(e)+"</div></div>"}}),Kt=Dt.extend({Defaults:{classes:"checkbox",role:"checkbox",checked:!1},init:function(e){var t=this;t._super(e),t.on("click mousedown",function(e){e.preventDefault()}),t.on("click",function(e){e.preventDefault(),t.disabled()||t.checked(!t.checked())}),t.checked(t.settings.checked)},checked:function(e){return arguments.length?(this.state.set("checked",e),this):this.state.get("checked")},value:function(e){return arguments.length?this.checked(e):this.checked()},renderHtml:function(){var e=this,t=e._id,n=e.classPrefix;return'<div id="'+t+'" class="'+e.classes+'" unselectable="on" aria-labelledby="'+t+'-al" tabindex="-1"><i class="'+n+"ico "+n+'i-checkbox"></i><span id="'+t+'-al" class="'+n+'label">'+e.encode(e.state.get("text"))+"</span></div>"},bindStates:function(){var e=this;function t(t){e.classes.toggle("checked",t),e.aria("checked",t)}return e.state.on("change:text",function(t){e.getEl("al").firstChild.data=e.translate(t.value)}),e.state.on("change:checked change:value",function(n){e.fire("change"),t(n.value)}),e.state.on("change:icon",function(t){var n=t.value,i=e.classPrefix;if(void 0===n)return e.settings.icon;e.settings.icon=n,n=n?i+"ico "+i+"i-"+e.settings.icon:"";var r=e.getEl().firstChild,o=r.getElementsByTagName("i")[0];n?(o&&o===r.firstChild||(o=document.createElement("i"),r.insertBefore(o,r.firstChild)),o.className=n):o&&r.removeChild(o)}),e.state.get("checked")&&t(!0),e._super()}}),Zt=tinymce.util.Tools.resolve("tinymce.util.VK"),Qt=Dt.extend({init:function(e){var t=this;t._super(e),e=t.settings,t.classes.add("combobox"),t.subinput=!0,t.ariaTarget="inp",e.menu=e.menu||e.values,e.menu&&(e.icon="caret"),t.on("click",function(n){var i=n.target,r=t.getEl();if(xe.contains(r,i)||i===r)for(;i&&i!==r;)i.id&&-1!==i.id.indexOf("-open")&&(t.fire("action"),e.menu&&(t.showMenu(),n.aria&&t.menu.items()[0].focus())),i=i.parentNode}),t.on("keydown",function(e){var n;13===e.keyCode&&"INPUT"===e.target.nodeName&&(e.preventDefault(),t.parents().reverse().each(function(e){if(e.toJSON)return n=e,!1}),t.fire("submit",{data:n.toJSON()}))}),t.on("keyup",function(e){if("INPUT"===e.target.nodeName){var n=t.state.get("value"),i=e.target.value;i!==n&&(t.state.set("value",i),t.fire("autocomplete",e))}}),t.on("mouseover",function(e){var n=t.tooltip().moveTo(-65535);if(t.statusLevel()&&-1!==e.target.className.indexOf(t.classPrefix+"status")){var i=t.statusMessage()||"Ok",r=n.text(i).show().testMoveRel(e.target,["bc-tc","bc-tl","bc-tr"]);n.classes.toggle("tooltip-n","bc-tc"===r),n.classes.toggle("tooltip-nw","bc-tl"===r),n.classes.toggle("tooltip-ne","bc-tr"===r),n.moveRel(e.target,r)}})},statusLevel:function(e){return arguments.length>0&&this.state.set("statusLevel",e),this.state.get("statusLevel")},statusMessage:function(e){return arguments.length>0&&this.state.set("statusMessage",e),this.state.get("statusMessage")},showMenu:function(){var e,t=this,n=t.settings;t.menu||((e=n.menu||[]).length?e={type:"menu",items:e}:e.type=e.type||"menu",t.menu=g.create(e).parent(t).renderTo(t.getContainerElm()),t.fire("createmenu"),t.menu.reflow(),t.menu.on("cancel",function(e){e.control===t.menu&&t.focus()}),t.menu.on("show hide",function(e){e.control.items().each(function(e){e.active(e.value()===t.value())})}).fire("show"),t.menu.on("select",function(e){t.value(e.control.value())}),t.on("focusin",function(e){"INPUT"===e.target.tagName.toUpperCase()&&t.menu.hide()}),t.aria("expanded",!0)),t.menu.show(),t.menu.layoutRect({w:t.layoutRect().w}),t.menu.moveRel(t.getEl(),t.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"])},focus:function(){this.getEl("inp").focus()},repaint:function(){var e,t,n=this,i=n.getEl(),r=n.getEl("open"),o=n.layoutRect(),s=0,a=i.firstChild;n.statusLevel()&&"none"!==n.statusLevel()&&(s=parseInt(_e.getRuntimeStyle(a,"padding-right"),10)-parseInt(_e.getRuntimeStyle(a,"padding-left"),10)),e=r?o.w-_e.getSize(r).width-10:o.w-10;var l=document;return l.all&&(!l.documentMode||l.documentMode<=8)&&(t=n.layoutRect().h-2+"px"),xe(a).css({width:e-s,lineHeight:t}),n._super(),n},postRender:function(){var e=this;return xe(this.getEl("inp")).on("change",function(t){e.state.set("value",t.target.value),e.fire("change",t)}),e._super()},renderHtml:function(){var e,t,n,i=this,r=i._id,o=i.settings,s=i.classPrefix,a=i.state.get("value")||"",l="",u="";return"spellcheck"in o&&(u+=' spellcheck="'+o.spellcheck+'"'),o.maxLength&&(u+=' maxlength="'+o.maxLength+'"'),o.size&&(u+=' size="'+o.size+'"'),o.subtype&&(u+=' type="'+o.subtype+'"'),n='<i id="'+r+'-status" class="mce-status mce-ico" style="display: none"></i>',i.disabled()&&(u+=' disabled="disabled"'),(e=o.icon)&&"caret"!==e&&(e=s+"ico "+s+"i-"+o.icon),t=i.state.get("text"),(e||t)&&(l='<div id="'+r+'-open" class="'+s+"btn "+s+'open" tabIndex="-1" role="button"><button id="'+r+'-action" type="button" hidefocus="1" tabindex="-1">'+("caret"!==e?'<i class="'+e+'"></i>':'<i class="'+s+'caret"></i>')+(t?(e?" ":"")+t:"")+"</button></div>",i.classes.add("has-open")),'<div id="'+r+'" class="'+i.classes+'"><input id="'+r+'-inp" class="'+s+'textbox" value="'+i.encode(a,!1)+'" hidefocus="1"'+u+' placeholder="'+i.encode(o.placeholder)+'" />'+n+l+"</div>"},value:function(e){return arguments.length?(this.state.set("value",e),this):(this.state.get("rendered")&&this.state.set("value",this.getEl("inp").value),this.state.get("value"))},showAutoComplete:function(e,t){var n=this;if(0!==e.length){n.menu?n.menu.items().remove():n.menu=g.create({type:"menu",classes:"combobox-menu",layout:"flow"}).parent(n).renderTo(),a.each(e,function(e){var i,r;n.menu.add({text:e.title,url:e.previewUrl,match:t,classes:"menu-item-ellipsis",onclick:(i=e.value,r=e.title,function(){n.fire("selectitem",{title:r,value:i})})})}),n.menu.renderNew(),n.hideMenu(),n.menu.on("cancel",function(e){e.control.parent()===n.menu&&(e.stopPropagation(),n.focus(),n.hideMenu())}),n.menu.on("select",function(){n.focus()});var i=n.layoutRect().w;n.menu.layoutRect({w:i,minW:0,maxW:i}),n.menu.repaint(),n.menu.reflow(),n.menu.show(),n.menu.moveRel(n.getEl(),n.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"])}else n.hideMenu()},hideMenu:function(){this.menu&&this.menu.hide()},bindStates:function(){var e=this;e.state.on("change:value",function(t){e.getEl("inp").value!==t.value&&(e.getEl("inp").value=t.value)}),e.state.on("change:disabled",function(t){e.getEl("inp").disabled=t.value}),e.state.on("change:statusLevel",function(t){var n=e.getEl("status"),i=e.classPrefix,r=t.value;_e.css(n,"display","none"===r?"none":""),_e.toggleClass(n,i+"i-checkmark","ok"===r),_e.toggleClass(n,i+"i-warning","warn"===r),_e.toggleClass(n,i+"i-error","error"===r),e.classes.toggle("has-status","none"!==r),e.repaint()}),_e.on(e.getEl("status"),"mouseleave",function(){e.tooltip().hide()}),e.on("cancel",function(t){e.menu&&e.menu.visible()&&(t.stopPropagation(),e.hideMenu())});var t=function(e,t){t&&t.items().length>0&&t.items().eq(e)[0].focus()};return e.on("keydown",function(n){var i=n.keyCode;"INPUT"===n.target.nodeName&&(i===Zt.DOWN?(n.preventDefault(),e.fire("autocomplete"),t(0,e.menu)):i===Zt.UP&&(n.preventDefault(),t(-1,e.menu)))}),e._super()},remove:function(){xe(this.getEl("inp")).off(),this.menu&&this.menu.remove(),this._super()}}),en=Qt.extend({init:function(e){var t=this;e.spellcheck=!1,e.onaction&&(e.icon="none"),t._super(e),t.classes.add("colorbox"),t.on("change keyup postrender",function(){t.repaintColor(t.value())})},repaintColor:function(e){var t=this.getEl("open"),n=t?t.getElementsByTagName("i")[0]:null;if(n)try{n.style.background=e}catch(i){}},bindStates:function(){var e=this;return e.state.on("change:value",function(t){e.state.get("rendered")&&e.repaintColor(t.value)}),e._super()}}),tn=Xt.extend({showPanel:function(){var e=this,t=e.settings;if(e.classes.add("opened"),e.panel)e.panel.show();else{var n=t.panel;n.type&&(n={layout:"grid",items:n}),n.role=n.role||"dialog",n.popover=!0,n.autohide=!0,n.ariaRoot=!0,e.panel=new kt(n).on("hide",function(){e.classes.remove("opened")}).on("cancel",function(t){t.stopPropagation(),e.focus(),e.hidePanel()}).parent(e).renderTo(e.getContainerElm()),e.panel.fire("show"),e.panel.reflow()}var i=e.panel.testMoveRel(e.getEl(),t.popoverAlign||(e.isRtl()?["bc-tc","bc-tl","bc-tr"]:["bc-tc","bc-tr","bc-tl"]));e.panel.classes.toggle("start","bc-tl"===i),e.panel.classes.toggle("end","bc-tr"===i),e.panel.moveRel(e.getEl(),i)},hidePanel:function(){this.panel&&this.panel.hide()},postRender:function(){var e=this;return e.aria("haspopup",!0),e.on("click",function(t){t.control===e&&(e.panel&&e.panel.visible()?e.hidePanel():(e.showPanel(),e.panel.focus(!!t.aria)))}),e._super()},remove:function(){return this.panel&&(this.panel.remove(),this.panel=null),this._super()}}),nn=m.DOM,rn=tn.extend({init:function(e){this._super(e),this.classes.add("splitbtn"),this.classes.add("colorbutton")},color:function(e){return e?(this._color=e,this.getEl("preview").style.backgroundColor=e,this):this._color},resetColor:function(){return this._color=null,this.getEl("preview").style.backgroundColor=null,this},renderHtml:function(){var e=this,t=e._id,n=e.classPrefix,i=e.state.get("text"),r=e.settings.icon?n+"ico "+n+"i-"+e.settings.icon:"",o=e.settings.image?" style=\"background-image: url('"+e.settings.image+"')\"":"",s="";return i&&(e.classes.add("btn-has-text"),s='<span class="'+n+'txt">'+e.encode(i)+"</span>"),'<div id="'+t+'" class="'+e.classes+'" role="button" tabindex="-1" aria-haspopup="true"><button role="presentation" hidefocus="1" type="button" tabindex="-1">'+(r?'<i class="'+r+'"'+o+"></i>":"")+'<span id="'+t+'-preview" class="'+n+'preview"></span>'+s+'</button><button type="button" class="'+n+'open" hidefocus="1" tabindex="-1"> <i class="'+n+'caret"></i></button></div>'},postRender:function(){var e=this,t=e.settings.onclick;return e.on("click",function(n){n.aria&&"down"===n.aria.key||n.control!==e||nn.getParent(n.target,"."+e.classPrefix+"open")||(n.stopImmediatePropagation(),t.call(e,n))}),delete e.settings.onclick,e._super()}}),on=tinymce.util.Tools.resolve("tinymce.util.Color"),sn=Dt.extend({Defaults:{classes:"widget colorpicker"},init:function(e){this._super(e)},postRender:function(){var e,t,n,i,r,o=this,s=o.color();function a(e,t){var n,i,r=_e.getPos(e);return n=t.pageX-r.x,i=t.pageY-r.y,{x:n=Math.max(0,Math.min(n/e.clientWidth,1)),y:i=Math.max(0,Math.min(i/e.clientHeight,1))}}function l(e,t){var s=(360-e.h)/360;_e.css(n,{top:100*s+"%"}),t||_e.css(r,{left:e.s+"%",top:100-e.v+"%"}),i.style.background=on({s:100,v:100,h:e.h}).toHex(),o.color().parse({s:e.s,v:e.v,h:e.h})}function u(t){var n;n=a(i,t),e.s=100*n.x,e.v=100*(1-n.y),l(e),o.fire("change")}function c(n){var i;i=a(t,n),(e=s.toHsv()).h=360*(1-i.y),l(e,!0),o.fire("change")}t=o.getEl("h"),n=o.getEl("hp"),i=o.getEl("sv"),r=o.getEl("svp"),o._repaint=function(){l(e=s.toHsv())},o._super(),o._svdraghelper=new dt(o._id+"-sv",{start:u,drag:u}),o._hdraghelper=new dt(o._id+"-h",{start:c,drag:c}),o._repaint()},rgb:function(){return this.color().toRgb()},value:function(e){if(!arguments.length)return this.color().toHex();this.color().parse(e),this._rendered&&this._repaint()},color:function(){return this._color||(this._color=on()),this._color},renderHtml:function(){var e,t=this._id,n=this.classPrefix,i="#ff0000,#ff0080,#ff00ff,#8000ff,#0000ff,#0080ff,#00ffff,#00ff80,#00ff00,#80ff00,#ffff00,#ff8000,#ff0000";return e='<div id="'+t+'-h" class="'+n+'colorpicker-h" style="background: -ms-linear-gradient(top,'+i+");background: linear-gradient(to bottom,"+i+');">'+function(){var e,t,r,o,s="";for(r="filter:progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr=",e=0,t=(o=i.split(",")).length-1;e<t;e++)s+='<div class="'+n+'colorpicker-h-chunk" style="height:'+100/t+"%;"+r+o[e]+",endColorstr="+o[e+1]+");-ms-"+r+o[e]+",endColorstr="+o[e+1]+')"></div>';return s}()+'<div id="'+t+'-hp" class="'+n+'colorpicker-h-marker"></div></div>','<div id="'+t+'" class="'+this.classes+'"><div id="'+t+'-sv" class="'+n+'colorpicker-sv"><div class="'+n+'colorpicker-overlay1"><div class="'+n+'colorpicker-overlay2"><div id="'+t+'-svp" class="'+n+'colorpicker-selector1"><div class="'+n+'colorpicker-selector2"></div></div></div></div></div>'+e+"</div>"}}),an=Dt.extend({init:function(e){e=a.extend({height:100,text:"Drop an image here",multiple:!1,accept:null},e),this._super(e),this.classes.add("dropzone"),e.multiple&&this.classes.add("multiple")},renderHtml:function(){var e,t,n=this.settings;return e={id:this._id,hidefocus:"1"},t=_e.create("div",e,"<span>"+this.translate(n.text)+"</span>"),n.height&&_e.css(t,"height",n.height+"px"),n.width&&_e.css(t,"width",n.width+"px"),t.className=this.classes,t.outerHTML},postRender:function(){var e=this,t=function(t){t.preventDefault(),e.classes.toggle("dragenter"),e.getEl().className=e.classes};e._super(),e.$el.on("dragover",function(e){e.preventDefault()}),e.$el.on("dragenter",t),e.$el.on("dragleave",t),e.$el.on("drop",function(t){if(t.preventDefault(),!e.state.get("disabled")){var n=function(t){var n=e.settings.accept;if("string"!=typeof n)return t;var i=new RegExp("("+n.split(/\s*,\s*/).join("|")+")$","i");return a.grep(t,function(e){return i.test(e.name)})}(t.dataTransfer.files);e.value=function(){return n.length?e.settings.multiple?n:n[0]:null},n.length&&e.fire("change",t)}})},remove:function(){this.$el.off(),this._super()}}),ln=Dt.extend({init:function(e){var t=this;e.delimiter||(e.delimiter="\xbb"),t._super(e),t.classes.add("path"),t.canFocus=!0,t.on("click",function(e){var n;(n=e.target.getAttribute("data-index"))&&t.fire("select",{value:t.row()[n],index:n})}),t.row(t.settings.row)},focus:function(){return this.getEl().firstChild.focus(),this},row:function(e){return arguments.length?(this.state.set("row",e),this):this.state.get("row")},renderHtml:function(){return'<div id="'+this._id+'" class="'+this.classes+'">'+this._getDataPathHtml(this.state.get("row"))+"</div>"},bindStates:function(){var e=this;return e.state.on("change:row",function(t){e.innerHtml(e._getDataPathHtml(t.value))}),e._super()},_getDataPathHtml:function(e){var t,n,i=e||[],r="",o=this.classPrefix;for(t=0,n=i.length;t<n;t++)r+=(t>0?'<div class="'+o+'divider" aria-hidden="true"> '+this.settings.delimiter+" </div>":"")+'<div role="button" class="'+o+"path-item"+(t===n-1?" "+o+"last":"")+'" data-index="'+t+'" tabindex="-1" id="'+this._id+"-"+t+'" aria-level="'+(t+1)+'">'+i[t].name+"</div>";return r||(r='<div class="'+o+'path-item">\xa0</div>'),r}}),un=ln.extend({postRender:function(){var e=this,t=e.settings.editor;function n(e){if(1===e.nodeType){if("BR"===e.nodeName||e.getAttribute("data-mce-bogus"))return!0;if("bookmark"===e.getAttribute("data-mce-type"))return!0}return!1}return!1!==t.settings.elementpath&&(e.on("select",function(e){t.focus(),t.selection.select(this.row()[e.index].element),t.nodeChanged()}),t.on("nodeChange",function(i){for(var r=[],o=i.parents,s=o.length;s--;)if(1===o[s].nodeType&&!n(o[s])){var a=t.fire("ResolveName",{name:o[s].nodeName.toLowerCase(),target:o[s]});if(a.isDefaultPrevented()||r.push({name:a.name,element:o[s]}),a.isPropagationStopped())break}e.row(r)})),e._super()}}),cn=ut.extend({Defaults:{layout:"flex",align:"center",defaults:{flex:1}},renderHtml:function(){var e=this,t=e._layout,n=e.classPrefix;return e.classes.add("formitem"),t.preRender(e),'<div id="'+e._id+'" class="'+e.classes+'" hidefocus="1" tabindex="-1">'+(e.settings.title?'<div id="'+e._id+'-title" class="'+n+'title">'+e.settings.title+"</div>":"")+'<div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+(e.settings.html||"")+t.renderHtml(e)+"</div></div>"}}),dn=ut.extend({Defaults:{containerCls:"form",layout:"flex",direction:"column",align:"stretch",flex:1,padding:15,labelGap:30,spacing:10,callbacks:{submit:function(){this.submit()}}},preRender:function(){var e=this,t=e.items();e.settings.formItemDefaults||(e.settings.formItemDefaults={layout:"flex",autoResize:"overflow",defaults:{flex:1}}),t.each(function(t){var n,i=t.settings.label;i&&((n=new cn(a.extend({items:{type:"label",id:t._id+"-l",text:i,flex:0,forId:t._id,disabled:t.disabled()}},e.settings.formItemDefaults))).type="formitem",t.aria("labelledby",t._id+"-l"),"undefined"==typeof t.settings.flex&&(t.settings.flex=1),e.replace(t,n),n.add(t))})},submit:function(){return this.fire("submit",{data:this.toJSON()})},postRender:function(){this._super(),this.fromJSON(this.settings.data)},bindStates:function(){var e=this;function t(){var t,n,i=0,r=[];if(!1!==e.settings.labelGapCalc)for(("children"===e.settings.labelGapCalc?e.find("formitem"):e.items()).filter("formitem").each(function(e){var t=e.items()[0],n=t.getEl().clientWidth;i=n>i?n:i,r.push(t)}),n=e.settings.labelGap||0,t=r.length;t--;)r[t].settings.minWidth=i+n}e._super(),e.on("show",t),t()}}),fn=dn.extend({Defaults:{containerCls:"fieldset",layout:"flex",direction:"column",align:"stretch",flex:1,padding:"25 15 5 15",labelGap:30,spacing:10,border:1},renderHtml:function(){var e=this,t=e._layout,n=e.classPrefix;return e.preRender(),t.preRender(e),'<fieldset id="'+e._id+'" class="'+e.classes+'" hidefocus="1" tabindex="-1">'+(e.settings.title?'<legend id="'+e._id+'-title" class="'+n+'fieldset-title">'+e.settings.title+"</legend>":"")+'<div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+(e.settings.html||"")+t.renderHtml(e)+"</div></fieldset>"}}),hn=0,mn=function(e){var t=(new Date).getTime();return e+"_"+Math.floor(1e9*Math.random())+ ++hn+String(t)},gn=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:k.constant(e)}},pn={fromHtml:function(e,t){var n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||n.childNodes.length>1)throw console.error("HTML does not have a single root node",e),"HTML must have a single root node";return gn(n.childNodes[0])},fromTag:function(e,t){var n=(t||document).createElement(e);return gn(n)},fromText:function(e,t){var n=(t||document).createTextNode(e);return gn(n)},fromDom:gn,fromPoint:function(e,t,n){return P.from(e.dom().elementFromPoint(t,n)).map(gn)}},vn=function(e){var t,n=!1;return function(){return n||(n=!0,t=e.apply(null,arguments)),t}},bn=8,yn=9,xn=1,wn=3,_n=function(e){return e.dom().nodeName.toLowerCase()},Rn=function(e){return e.dom().nodeType},Cn=function(e){return function(t){return Rn(t)===e}},kn=Cn(xn),En=Cn(wn),Hn=Cn(yn),Sn={name:_n,type:Rn,value:function(e){return e.dom().nodeValue},isElement:kn,isText:En,isDocument:Hn,isComment:function(e){return Rn(e)===bn||"#comment"===_n(e)}},Mn=(vn(function(){return Mn(pn.fromDom(document))}),function(e){var t=e.dom().body;if(null===t||t===undefined)throw"Body is not available yet";return pn.fromDom(t)}),Tn=function(e){return function(t){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(t)===e}},Pn={isString:Tn("string"),isObject:Tn("object"),isArray:Tn("array"),isNull:Tn("null"),isBoolean:Tn("boolean"),isUndefined:Tn("undefined"),isFunction:Tn("function"),isNumber:Tn("number")},Wn=(Vt=Object.keys)===undefined?function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t}:Vt,Dn=function(e,t){for(var n=Wn(e),i=0,r=n.length;i<r;i++){var o=n[i];t(e[o],o,e)}},Nn=function(e,t){var n={};return Dn(e,function(i,r){var o=t(i,r,e);n[o.k]=o.v}),n},An=function(e,t){var n=[];return Dn(e,function(e,i){n.push(t(e,i))}),n},Bn=function(e){return An(e,function(e){return e})},On={bifilter:function(e,t){var n={},i={};return Dn(e,function(e,r){(t(e,r)?n:i)[r]=e}),{t:n,f:i}},each:Dn,map:function(e,t){return Nn(e,function(e,n,i){return{k:n,v:t(e,n,i)}})},mapToArray:An,tupleMap:Nn,find:function(e,t){for(var n=Wn(e),i=0,r=n.length;i<r;i++){var o=n[i],s=e[o];if(t(s,o,e))return P.some(s)}return P.none()},keys:Wn,values:Bn,size:function(e){return Bn(e).length}},zn=function(e){return e.slice(0).sort()},Ln={sort:zn,reqMessage:function(e,t){throw new Error("All required keys ("+zn(e).join(", ")+") were not specified. Specified keys were: "+zn(t).join(", ")+".")},unsuppMessage:function(e){throw new Error("Unsupported keys for object: "+zn(e).join(", "))},validateStrArr:function(e,t){if(!Pn.isArray(t))throw new Error("The "+e+" fields must be an array. Was: "+t+".");ee.each(t,function(t){if(!Pn.isString(t))throw new Error("The value "+t+" in the "+e+" fields was not a string.")})},invalidTypeMessage:function(e,t){throw new Error("All values need to be of type: "+t+". Keys ("+zn(e).join(", ")+") were not.")},checkDupes:function(e){var t=zn(e);ee.find(t,function(e,n){return n<t.length-1&&e===t[n+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})}},In={immutable:function(){var e=arguments;return function(){for(var t=new Array(arguments.length),n=0;n<t.length;n++)t[n]=arguments[n];if(e.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+e.length+']", got '+t.length+" arguments");var i={};return ee.each(e,function(e,n){i[e]=k.constant(t[n])}),i}},immutableBag:function(e,t){var n=e.concat(t);if(0===n.length)throw new Error("You must specify at least one required or optional field.");return Ln.validateStrArr("required",e),Ln.validateStrArr("optional",t),Ln.checkDupes(n),function(i){var r=On.keys(i);ee.forall(e,function(e){return ee.contains(r,e)})||Ln.reqMessage(e,r);var o=ee.filter(r,function(e){return!ee.contains(n,e)});o.length>0&&Ln.unsuppMessage(o);var s={};return ee.each(e,function(e){s[e]=k.constant(i[e])}),ee.each(t,function(e){s[e]=k.constant(Object.prototype.hasOwnProperty.call(i,e)?P.some(i[e]):P.none())}),s}}},Fn=("undefined"!=typeof window?window:Function("return this;")(),function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++){var i=e[n];if(i.test(t))return i}return undefined}(e,t);if(!n)return{major:0,minor:0};var i=function(e){return Number(t.replace(n,"$"+e))};return Vn(i(1),i(2))}),Un=function(){return Vn(0,0)},Vn=function(e,t){return{major:e,minor:t}},jn={nu:Vn,detect:function(e,t){var n=String(t).toLowerCase();return 0===e.length?Un():Fn(e,n)},unknown:Un},Yn="Firefox",qn=function(e,t){return function(){return t===e}},$n=function(e){var t=e.current;return{current:t,version:e.version,isEdge:qn("Edge",t),isChrome:qn("Chrome",t),isIE:qn("IE",t),isOpera:qn("Opera",t),isFirefox:qn(Yn,t),isSafari:qn("Safari",t)}},Xn={unknown:function(){return $n({current:undefined,version:jn.unknown()})},nu:$n,edge:k.constant("Edge"),chrome:k.constant("Chrome"),ie:k.constant("IE"),opera:k.constant("Opera"),firefox:k.constant(Yn),safari:k.constant("Safari")},Jn="Windows",Gn="Android",Kn="Solaris",Zn="FreeBSD",Qn=function(e,t){return function(){return t===e}},ei=function(e){var t=e.current;return{current:t,version:e.version,isWindows:Qn(Jn,t),isiOS:Qn("iOS",t),isAndroid:Qn(Gn,t),isOSX:Qn("OSX",t),isLinux:Qn("Linux",t),isSolaris:Qn(Kn,t),isFreeBSD:Qn(Zn,t)}},ti={unknown:function(){return ei({current:undefined,version:jn.unknown()})},nu:ei,windows:k.constant(Jn),ios:k.constant("iOS"),android:k.constant(Gn),linux:k.constant("Linux"),osx:k.constant("OSX"),solaris:k.constant(Kn),freebsd:k.constant(Zn)},ni=function(e,t){var n=String(t).toLowerCase();return ee.find(e,function(e){return e.search(n)})},ii=function(e,t){return ni(e,t).map(function(e){var n=jn.detect(e.versionRegexes,t);return{current:e.name,version:n}})},ri=function(e,t){return ni(e,t).map(function(e){var n=jn.detect(e.versionRegexes,t);return{current:e.name,version:n}})},oi=function(e,t){return-1!==e.indexOf(t)},si=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ai=function(e){return function(t){return oi(t,e)}},li=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return oi(e,"edge/")&&oi(e,"chrome")&&oi(e,"safari")&&oi(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,si],search:function(e){return oi(e,"chrome")&&!oi(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return oi(e,"msie")||oi(e,"trident")}},{name:"Opera",versionRegexes:[si,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ai("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ai("firefox")},{name:"Safari",versionRegexes:[si,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(oi(e,"safari")||oi(e,"mobile/"))&&oi(e,"applewebkit")}}],ui=[{name:"Windows",search:ai("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return oi(e,"iphone")||oi(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ai("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:ai("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ai("linux"),versionRegexes:[]},{name:"Solaris",search:ai("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ai("freebsd"),versionRegexes:[]}],ci={browsers:k.constant(li),oses:k.constant(ui)},di=function(e){var t,n,i,r,o,s,a,l,u,c,d,f=ci.browsers(),h=ci.oses(),m=ii(f,e).fold(Xn.unknown,Xn.nu),g=ri(h,e).fold(ti.unknown,ti.nu);return{browser:m,os:g,deviceType:(n=m,i=e,r=(t=g).isiOS()&&!0===/ipad/i.test(i),o=t.isiOS()&&!r,s=t.isAndroid()&&3===t.version.major,a=t.isAndroid()&&4===t.version.major,l=r||s||a&&!0===/mobile/i.test(i),u=t.isiOS()||t.isAndroid(),c=u&&!l,d=n.isSafari()&&t.isiOS()&&!1===/safari/i.test(i),{isiPad:k.constant(r),isiPhone:k.constant(o),isTablet:k.constant(l),isPhone:k.constant(c),isTouch:k.constant(u),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:k.constant(d)})}},fi=vn(function(){var e=navigator.userAgent;return di(e)}),hi=xn,mi=yn,gi=function(e){return e.nodeType!==hi&&e.nodeType!==mi||0===e.childElementCount},pi={all:function(e,t){var n=t===undefined?document:t.dom();return gi(n)?[]:ee.map(n.querySelectorAll(e),pn.fromDom)},is:function(e,t){var n=e.dom();if(n.nodeType!==hi)return!1;if(n.matches!==undefined)return n.matches(t);if(n.msMatchesSelector!==undefined)return n.msMatchesSelector(t);if(n.webkitMatchesSelector!==undefined)return n.webkitMatchesSelector(t);if(n.mozMatchesSelector!==undefined)return n.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},one:function(e,t){var n=t===undefined?document:t.dom();return gi(n)?P.none():P.from(n.querySelector(e)).map(pn.fromDom)}},vi=(fi().browser.isIE(),In.immutable("element","offset"),function(e,t){return pi.all(t,e)}),bi=a.trim,yi=function(e){return function(t){if(t&&1===t.nodeType){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1}},xi=yi("true"),wi=yi("false"),_i=function(e,t,n,i,r){return{type:e,title:t,url:n,level:i,attach:r}},Ri=function(e){return e.innerText||e.textContent},Ci=function(e){return(t=e)&&"A"===t.nodeName&&(t.id||t.name)&&Ei(e);var t},ki=function(e){return e&&/^(H[1-6])$/.test(e.nodeName)},Ei=function(e){return function(e){for(;e=e.parentNode;){var t=e.contentEditable;if(t&&"inherit"!==t)return xi(e)}return!1}(e)&&!wi(e)},Hi=function(e){return ki(e)&&Ei(e)},Si=function(e){var t,n,i=(t=e).id?t.id:mn("h");return _i("header",Ri(e),"#"+i,ki(n=e)?parseInt(n.nodeName.substr(1),10):0,function(){e.id=i})},Mi=function(e){var t=e.id||e.name,n=Ri(e);return _i("anchor",n||"#"+t,"#"+t,0,k.noop)},Ti=function(e){var t,n;return t="h1,h2,h3,h4,h5,h6,a:not([href])",n=e,ee.map(vi(pn.fromDom(n),t),function(e){return e.dom()})},Pi=function(e){return bi(e.title).length>0},Wi=function(e){var t,n,i=Ti(e);return ee.filter((n=i,ee.map(ee.filter(n,Hi),Si)).concat((t=i,ee.map(ee.filter(t,Ci),Mi))),Pi)},Di={},Ni=function(e){return{title:e.title,value:{title:{raw:e.title},url:e.url,attach:e.attach}}},Ai=function(e,t){return{title:e,value:{title:e,url:t,attach:k.noop}}},Bi=function(e,t,n){var i=t in e?e[t]:n;return!1===i?null:i},Oi=function(e,t,n,i){var r,o,s,l,u={title:"-"},c=function(e){var i=e.hasOwnProperty(n)?e[n]:[],r=ee.filter(i,function(e){return n=e,i=t,!ee.exists(i,function(e){return e.url===n});var n,i});return a.map(r,function(e){return{title:e,value:{title:e,url:e,attach:k.noop}}})},d=function(e){var n,i=ee.filter(t,function(t){return t.type===e});return n=i,a.map(n,Ni)};return!1===i.typeahead_urls?[]:"file"===n?(r=[zi(e,c(Di)),zi(e,d("header")),zi(e,(o=d("anchor"),s=Bi(i,"anchor_top","#top"),l=Bi(i,"anchor_bottom","#bottom"),null!==s&&o.unshift(Ai("<top>",s)),null!==l&&o.push(Ai("<bottom>",l)),o))],ee.foldl(r,function(e,t){return 0===e.length||0===t.length?e.concat(t):e.concat(u,t)},[])):zi(e,c(Di))},zi=function(e,t){var n=e.toLowerCase(),i=a.grep(t,function(e){return-1!==e.title.toLowerCase().indexOf(n)});return 1===i.length&&i[0].title===e?[]:i},Li=function(e,t,n,i){var r=function(r){var o=Wi(n),s=Oi(r,o,i,t);e.showAutoComplete(s,r)};e.on("autocomplete",function(){r(e.value())}),e.on("selectitem",function(t){var n=t.value;e.value(n.url);var r,o=(r=n.title).raw?r.raw:r;"image"===i?e.fire("change",{meta:{alt:o,attach:n.attach}}):e.fire("change",{meta:{text:o,attach:n.attach}}),e.focus()}),e.on("click",function(t){0===e.value().length&&"INPUT"===t.target.nodeName&&r("")}),e.on("PostRender",function(){e.getRoot().on("submit",function(t){var n,r,o;t.isDefaultPrevented()||(n=e.value(),o=Di[r=i],/^https?/.test(n)&&(o?-1===ee.indexOf(o,n)&&(Di[r]=o.slice(0,5).concat(n)):Di[r]=[n]))})})},Ii=function(e,t,n){var i=t.filepicker_validator_handler;i&&e.state.on("change:value",function(t){var r;0!==(r=t.value).length?i({url:r,type:n},function(t){var n,i,r,o=(i=(n=t).status,r=n.message,"valid"===i?{status:"ok",message:r}:"unknown"===i?{status:"warn",message:r}:"invalid"===i?{status:"warn",message:r}:{status:"none",message:""});e.statusMessage(o.message),e.statusLevel(o.status)}):e.statusLevel("none")})},Fi=Qt.extend({Statics:{clearHistory:function(){Di={}}},init:function(e){var t,n,i,r=this,o=window.tinymce?window.tinymce.activeEditor:s.activeEditor,l=o.settings,u=e.filetype;e.spellcheck=!1,(i=l.file_picker_types||l.file_browser_callback_types)&&(i=a.makeMap(i,/[, ]/)),i&&!i[u]||(!(n=l.file_picker_callback)||i&&!i[u]?!(n=l.file_browser_callback)||i&&!i[u]||(t=function(){n(r.getEl("inp").id,r.value(),u,window)}):t=function(){var e=r.fire("beforecall").meta;e=a.extend({filetype:u},e),n.call(o,function(e,t){r.value(e).fire("change",{meta:t})},r.value(),e)}),t&&(e.icon="browse",e.onaction=t),r._super(e),r.classes.add("filepicker"),Li(r,l,o.getBody(),u),Ii(r,l,u)}}),Ui=$t.extend({recalc:function(e){var t=e.layoutRect(),n=e.paddingBox;e.items().filter(":visible").each(function(e){e.layoutRect({x:n.left,y:n.top,w:t.innerW-n.right-n.left,h:t.innerH-n.top-n.bottom}),e.recalc&&e.recalc()})}}),Vi=$t.extend({recalc:function(e){var t,n,i,r,o,s,a,l,u,c,d,f,h,m,g,p,v,b,y,x,w,_,R,C,k,E,H,S,M,T,P,W,D,N,A,B,O,z=[],L=Math.max,I=Math.min;for(i=e.items().filter(":visible"),r=e.layoutRect(),o=e.paddingBox,s=e.settings,f=e.isRtl()?s.direction||"row-reversed":s.direction,a=s.align,l=e.isRtl()?s.pack||"end":s.pack,u=s.spacing||0,"row-reversed"!==f&&"column-reverse"!==f||(i=i.set(i.toArray().reverse()),f=f.split("-")[0]),"column"===f?(C="y",_="h",R="minH",k="maxH",H="innerH",E="top",S="deltaH",M="contentH",N="left",W="w",T="x",P="innerW",D="minW",A="right",B="deltaW",O="contentW"):(C="x",_="w",R="minW",k="maxW",H="innerW",E="left",S="deltaW",M="contentW",N="top",W="h",T="y",P="innerH",D="minH",A="bottom",B="deltaH",O="contentH"),d=r[H]-o[E]-o[E],w=c=0,t=0,n=i.length;t<n;t++)m=(h=i[t]).layoutRect(),d-=t<n-1?u:0,(g=h.settings.flex)>0&&(c+=g,m[k]&&z.push(h),m.flex=g),d-=m[R],(p=o[N]+m[D]+o[A])>w&&(w=p);if((y={})[R]=d<0?r[R]-d+r[S]:r[H]-d+r[S],y[D]=w+r[B],y[M]=r[H]-d,y[O]=w,y.minW=I(y.minW,r.maxW),y.minH=I(y.minH,r.maxH),y.minW=L(y.minW,r.startMinWidth),y.minH=L(y.minH,r.startMinHeight),!r.autoResize||y.minW===r.minW&&y.minH===r.minH){for(b=d/c,t=0,n=z.length;t<n;t++)v=(m=(h=z[t]).layoutRect())[k],(p=m[R]+m.flex*b)>v?(d-=m[k]-m[R],c-=m.flex,m.flex=0,m.maxFlexSize=v):m.maxFlexSize=0;for(b=d/c,x=o[E],y={},0===c&&("end"===l?x=d+o[E]:"center"===l?(x=Math.round(r[H]/2-(r[H]-d)/2)+o[E])<0&&(x=o[E]):"justify"===l&&(x=o[E],u=Math.floor(d/(i.length-1)))),y[T]=o[N],t=0,n=i.length;t<n;t++)p=(m=(h=i[t]).layoutRect()).maxFlexSize||m[R],"center"===a?y[T]=Math.round(r[P]/2-m[W]/2):"stretch"===a?(y[W]=L(m[D]||0,r[P]-o[N]-o[A]),y[T]=o[N]):"end"===a&&(y[T]=r[P]-m[W]-o.top),m.flex>0&&(p+=m.flex*b),y[_]=p,y[C]=x,h.layoutRect(y),h.recalc&&h.recalc(),x+=p+u}else if(y.w=y.minW,y.h=y.minH,e.layoutRect(y),this.recalc(e),null===e._lastRect){var F=e.parent();F&&(F._lastRect=null,F.recalc())}}}),ji=qt.extend({Defaults:{containerClass:"flow-layout",controlClass:"flow-layout-item",endClass:"break"},recalc:function(e){e.items().filter(":visible").each(function(e){e.recalc&&e.recalc()})},isNative:function(){return!0}}),Yi=function(e,t){return pi.one(t,e)},qi=function(e,t){return function(){e.execCommand("mceToggleFormat",!1,t)}},$i=function(e,t){return function(){var n=this;e.formatter?e.formatter.formatChanged(t,function(e){n.active(e)}):e.on("init",function(){e.formatter.formatChanged(t,function(e){n.active(e)})})}},Xi=function(e){e.addMenuItem("align",{text:"Align",menu:[{text:"Left",icon:"alignleft",onclick:qi(e,"alignleft")},{text:"Center",icon:"aligncenter",onclick:qi(e,"aligncenter")},{text:"Right",icon:"alignright",onclick:qi(e,"alignright")},{text:"Justify",icon:"alignjustify",onclick:qi(e,"alignjustify")}]}),a.each({alignleft:["Align left","JustifyLeft"],aligncenter:["Align center","JustifyCenter"],alignright:["Align right","JustifyRight"],alignjustify:["Justify","JustifyFull"],alignnone:["No alignment","JustifyNone"]},function(t,n){e.addButton(n,{active:!1,tooltip:t[0],cmd:t[1],onPostRender:$i(e,n)})})},Ji=function(e){return function(t,n){return P.from(n).map(pn.fromDom).filter(Sn.isElement).bind(function(n){return function(e,t,n){for(;n!==t;){if(n.style[e]){var i=n.style[e];return""!==i?P.some(i):P.none()}n=n.parentNode}return P.none()}(e,t,n.dom()).or((i=e,r=n.dom(),P.from(m.DOM.getStyle(r,i,!0))));var i,r}).getOr("")}},Gi={getFontSize:Ji("fontSize"),getFontFamily:k.compose(function(e){return e.replace(/[\'\"\\]/g,"").replace(/,\s+/g,",")},Ji("fontFamily")),toPt:function(e,t){return/[0-9.]+px$/.test(e)?(n=72*parseInt(e,10)/96,i=t||0,r=Math.pow(10,i),Math.round(n*r)/r+"pt"):e;var n,i,r}},Ki=function(e){return e?e.split(",")[0]:""},Zi=function(e,t){return function(){var n=this;e.on("init nodeChange",function(i){var r,o,s,l=Gi.getFontFamily(e.getBody(),i.element),u=(r=t,o=l,a.each(r,function(e){e.value.toLowerCase()===o.toLowerCase()&&(s=e.value)}),a.each(r,function(e){s||Ki(e.value).toLowerCase()!==Ki(o).toLowerCase()||(s=e.value)}),s);n.value(u||null),!u&&l&&n.text(Ki(l))})}},Qi=function(e){e.addButton("fontselect",function(){var t,n=(t=function(e){for(var t=(e=e.replace(/;$/,"").split(";")).length;t--;)e[t]=e[t].split("=");return e}(e.settings.font_formats||"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"),a.map(t,function(e){return{text:{raw:e[0]},value:e[1],textStyle:-1===e[1].indexOf("dings")?"font-family:"+e[1]:""}}));return{type:"listbox",text:"Font Family",tooltip:"Font Family",values:n,fixedWidth:!0,onPostRender:Zi(e,n),onselect:function(t){t.control.settings.value&&e.execCommand("FontName",!1,t.control.settings.value)}}})},er=function(e){Qi(e)},tr=function(e,t,n){var i;return a.each(e,function(e){e.value===n?i=n:e.value===t&&(i=t)}),i},nr=function(e){e.addButton("fontsizeselect",function(){var t,n,i,r=(t=e.settings.fontsize_formats||"8pt 10pt 12pt 14pt 18pt 24pt 36pt",a.map(t.split(" "),function(e){var t=e,n=e,i=e.split("=");return i.length>1&&(t=i[0],n=i[1]),{text:t,value:n}}));return{type:"listbox",text:"Font Sizes",tooltip:"Font Sizes",values:r,fixedWidth:!0,onPostRender:(n=e,i=r,function(){var e=this;n.on("init nodeChange",function(t){var r,o,s,a;if(r=Gi.getFontSize(n.getBody(),t.element))for(s=3;!a&&s>=0;s--)o=Gi.toPt(r,s),a=tr(i,o,r);e.value(a||null),a||e.text(o)})}),onclick:function(t){t.control.settings.value&&e.execCommand("FontSize",!1,t.control.settings.value)}}})},ir=function(e){nr(e)},rr=function(e,t){var n=t.length;return a.each(t,function(t){t.menu&&(t.hidden=0===rr(e,t.menu));var i=t.format;i&&(t.hidden=!e.formatter.canApply(i)),t.hidden&&n--}),n},or=function(e,t){var n=t.items().length;return t.items().each(function(t){t.menu&&t.visible(or(e,t.menu)>0),!t.menu&&t.settings.menu&&t.visible(rr(e,t.settings.menu)>0);var i=t.settings.format;i&&t.visible(e.formatter.canApply(i)),t.visible()||n--}),n},sr=function(e){var t,n,i,r,o,s,l,u,c=(n=0,i=[],r=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],o=function(e){var t=[];if(e)return a.each(e,function(e){var r={text:e.title,icon:e.icon};if(e.items)r.menu=o(e.items);else{var s=e.format||"custom"+n++;e.format||(e.name=s,i.push(e)),r.format=s,r.cmd=e.cmd}t.push(r)}),t},(t=e).on("init",function(){a.each(i,function(e){t.formatter.register(e.name,e)})}),{type:"menu",items:t.settings.style_formats_merge?t.settings.style_formats?o(r.concat(t.settings.style_formats)):o(r):o(t.settings.style_formats||r),onPostRender:function(e){t.fire("renderFormatsMenu",{control:e.control})},itemDefaults:{preview:!0,textStyle:function(){if(this.settings.format)return t.formatter.getCssText(this.settings.format)},onPostRender:function(){var e=this;e.parent().on("show",function(){var n,i;(n=e.settings.format)&&(e.disabled(!t.formatter.canApply(n)),e.active(t.formatter.match(n))),(i=e.settings.cmd)&&e.active(t.queryCommandState(i))})},onclick:function(){this.settings.format&&qi(t,this.settings.format)(),this.settings.cmd&&t.execCommand(this.settings.cmd)}}});s=c,e.addMenuItem("formats",{text:"Formats",menu:s}),u=c,(l=e).addButton("styleselect",{type:"menubutton",text:"Formats",menu:u,onShowMenu:function(){l.settings.style_formats_autohide&&or(l,this.menu)}})},ar=function(e,t){return function(){var n,i,r,o=[];return a.each(t,function(t){o.push({text:t[0],value:t[1],textStyle:function(){return e.formatter.getCssText(t[1])}})}),{type:"listbox",text:t[0][0],values:o,fixedWidth:!0,onselect:function(t){if(t.control){var n=t.control.value();qi(e,n)()}},onPostRender:(n=e,i=o,function(){var e=this;n.on("nodeChange",function(t){var o=n.formatter,s=null;a.each(t.parents,function(e){if(a.each(i,function(t){if(r?o.matchNode(e,r,{value:t.value})&&(s=t.value):o.matchNode(e,t.value)&&(s=t.value),s)return!1}),s)return!1}),e.value(s)})})}}},lr=function(e){var t,n,i=function(e){for(var t=(e=e.replace(/;$/,"").split(";")).length;t--;)e[t]=e[t].split("=");return e}(e.settings.block_formats||"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre");e.addMenuItem("blockformats",{text:"Blocks",menu:(t=e,n=i,a.map(n,function(e){return{text:e[0],onclick:qi(t,e[1]),textStyle:function(){return t.formatter.getCssText(e[1])}}}))}),e.addButton("formatselect",ar(e,i))},ur=function(e,t){var n,i;if("string"==typeof t)i=t.split(" ");else if(a.isArray(t))return ee.flatten(a.map(t,function(t){return ur(e,t)}));return n=a.grep(i,function(t){return"|"===t||t in e.menuItems}),a.map(n,function(t){return"|"===t?{text:"-"}:e.menuItems[t]})},cr=function(e){return e&&"-"===e.text},dr=function(e){var t=ee.filter(e,function(e,t,n){return!cr(e)||!cr(n[t-1])});return ee.filter(t,function(e,t,n){return!cr(e)||t>0&&t<n.length-1})},fr=function(e){var t,n,i,r,o=e.settings.insert_button_items;return dr(o?ur(e,o):(t=e,n="insert",i=[{text:"-"}],r=a.grep(t.menuItems,function(e){return e.context===n}),a.each(r,function(e){"before"===e.separator&&i.push({text:"|"}),e.prependToContext?i.unshift(e):i.push(e),"after"===e.separator&&i.push({text:"|"})}),i))},hr=function(e){var t;(t=e).addButton("insert",{type:"menubutton",icon:"insert",menu:[],oncreatemenu:function(){this.menu.add(fr(t)),this.menu.renderNew()}})},mr=function(e){var t,n,i;t=e,a.each({bold:"Bold",italic:"Italic",underline:"Underline",strikethrough:"Strikethrough",subscript:"Subscript",superscript:"Superscript"},function(e,n){t.addButton(n,{active:!1,tooltip:e,onPostRender:$i(t,n),onclick:qi(t,n)})}),n=e,a.each({outdent:["Decrease indent","Outdent"],indent:["Increase indent","Indent"],cut:["Cut","Cut"],copy:["Copy","Copy"],paste:["Paste","Paste"],help:["Help","mceHelp"],selectall:["Select all","SelectAll"],visualaid:["Visual aids","mceToggleVisualAid"],newdocument:["New document","mceNewDocument"],removeformat:["Clear formatting","RemoveFormat"],remove:["Remove","Delete"]},function(e,t){n.addButton(t,{tooltip:e[0],cmd:e[1]})}),i=e,a.each({blockquote:["Blockquote","mceBlockQuote"],subscript:["Subscript","Subscript"],superscript:["Superscript","Superscript"]},function(e,t){i.addButton(t,{active:!1,tooltip:e[0],cmd:e[1],onPostRender:$i(i,t)})})},gr=function(e){var t;mr(e),t=e,a.each({bold:["Bold","Bold","Meta+B"],italic:["Italic","Italic","Meta+I"],underline:["Underline","Underline","Meta+U"],strikethrough:["Strikethrough","Strikethrough"],subscript:["Subscript","Subscript"],superscript:["Superscript","Superscript"],removeformat:["Clear formatting","RemoveFormat"],newdocument:["New document","mceNewDocument"],cut:["Cut","Cut","Meta+X"],copy:["Copy","Copy","Meta+C"],paste:["Paste","Paste","Meta+V"],selectall:["Select all","SelectAll","Meta+A"]},function(e,n){t.addMenuItem(n,{text:e[0],icon:n,shortcut:e[2],cmd:e[1]})}),t.addMenuItem("codeformat",{text:"Code",icon:"code",onclick:qi(t,"code")})},pr=function(e,t){return function(){var n=this,i=function(){var n="redo"===t?"hasRedo":"hasUndo";return!!e.undoManager&&e.undoManager[n]()};n.disabled(!i()),e.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",function(){n.disabled(e.readonly||!i())})}},vr=function(e){var t,n;(t=e).addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onPostRender:pr(t,"undo"),cmd:"undo"}),t.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onPostRender:pr(t,"redo"),cmd:"redo"}),(n=e).addButton("undo",{tooltip:"Undo",onPostRender:pr(n,"undo"),cmd:"undo"}),n.addButton("redo",{tooltip:"Redo",onPostRender:pr(n,"redo"),cmd:"redo"})},br=function(e){var t,n;(t=e).addMenuItem("visualaid",{text:"Visual aids",selectable:!0,onPostRender:(n=t,function(){var e=this;n.on("VisualAid",function(t){e.active(t.hasVisual)}),e.active(n.hasVisual)}),cmd:"mceToggleVisualAid"})},yr={setup:function(e){var t;e.rtl&&(ot.rtl=!0),e.on("mousedown",function(){kt.hideAll()}),(t=e).settings.ui_container&&(de.container=Yi(pn.fromDom(document.body),t.settings.ui_container).fold(k.constant(null),function(e){return e.dom()})),Dt.tooltips=!de.iOS,ot.translate=function(e){return s.translate(e)},lr(e),Xi(e),gr(e),vr(e),ir(e),er(e),sr(e),br(e),hr(e)}},xr=$t.extend({recalc:function(e){var t,n,i,r,o,s,a,l,u,c,d,f,h,m,g,p,v,b,y,x,w,_,R,C,k,E,H,S,M=[],T=[];t=e.settings,r=e.items().filter(":visible"),o=e.layoutRect(),i=t.columns||Math.ceil(Math.sqrt(r.length)),n=Math.ceil(r.length/i),b=t.spacingH||t.spacing||0,y=t.spacingV||t.spacing||0,x=t.alignH||t.align,w=t.alignV||t.align,p=e.paddingBox,S="reverseRows"in t?t.reverseRows:e.isRtl(),x&&"string"==typeof x&&(x=[x]),w&&"string"==typeof w&&(w=[w]);for(d=0;d<i;d++)M.push(0);for(f=0;f<n;f++)T.push(0);for(f=0;f<n;f++)for(d=0;d<i&&(c=r[f*i+d]);d++)C=(u=c.layoutRect()).minW,k=u.minH,M[d]=C>M[d]?C:M[d],T[f]=k>T[f]?k:T[f];for(E=o.innerW-p.left-p.right,_=0,d=0;d<i;d++)_+=M[d]+(d>0?b:0),E-=(d>0?b:0)+M[d];for(H=o.innerH-p.top-p.bottom,R=0,f=0;f<n;f++)R+=T[f]+(f>0?y:0),H-=(f>0?y:0)+T[f];if(_+=p.left+p.right,R+=p.top+p.bottom,(l={}).minW=_+(o.w-o.innerW),l.minH=R+(o.h-o.innerH),l.contentW=l.minW-o.deltaW,l.contentH=l.minH-o.deltaH,l.minW=Math.min(l.minW,o.maxW),l.minH=Math.min(l.minH,o.maxH),l.minW=Math.max(l.minW,o.startMinWidth),l.minH=Math.max(l.minH,o.startMinHeight),!o.autoResize||l.minW===o.minW&&l.minH===o.minH){var P;o.autoResize&&((l=e.layoutRect(l)).contentW=l.minW-o.deltaW,l.contentH=l.minH-o.deltaH),P="start"===t.packV?0:H>0?Math.floor(H/n):0;var W=0,D=t.flexWidths;if(D)for(d=0;d<D.length;d++)W+=D[d];else W=i;var N=E/W;for(d=0;d<i;d++)M[d]+=D?D[d]*N:N;for(m=p.top,f=0;f<n;f++){for(h=p.left,a=T[f]+P,d=0;d<i&&(c=r[S?f*i+i-1-d:f*i+d]);d++)g=c.settings,u=c.layoutRect(),s=Math.max(M[d],u.startMinWidth),u.x=h,u.y=m,"center"===(v=g.alignH||(x?x[d]||x[0]:null))?u.x=h+s/2-u.w/2:"right"===v?u.x=h+s-u.w:"stretch"===v&&(u.w=s),"center"===(v=g.alignV||(w?w[d]||w[0]:null))?u.y=m+a/2-u.h/2:"bottom"===v?u.y=m+a-u.h:"stretch"===v&&(u.h=a),c.layoutRect(u),h+=s+b,c.recalc&&c.recalc();m+=a+y}}else if(l.w=l.minW,l.h=l.minH,e.layoutRect(l),this.recalc(e),null===e._lastRect){var A=e.parent();A&&(A._lastRect=null,A.recalc())}}}),wr=Dt.extend({renderHtml:function(){var e=this;return e.classes.add("iframe"),e.canFocus=!1,'<iframe id="'+e._id+'" class="'+e.classes+'" tabindex="-1" src="'+(e.settings.url||"javascript:''")+'" frameborder="0"></iframe>'},src:function(e){this.getEl().src=e},html:function(e,t){var n=this,i=this.getEl().contentWindow.document.body;return i?(i.innerHTML=e,t&&t()):R.setTimeout(function(){n.html(e)}),this}}),_r=Dt.extend({init:function(e){this._super(e),this.classes.add("widget").add("infobox"),this.canFocus=!1},severity:function(e){this.classes.remove("error"),this.classes.remove("warning"),this.classes.remove("success"),this.classes.add(e)},help:function(e){this.state.set("help",e)},renderHtml:function(){var e=this,t=e.classPrefix;return'<div id="'+e._id+'" class="'+e.classes+'"><div id="'+e._id+'-body">'+e.encode(e.state.get("text"))+'<button role="button" tabindex="-1"><i class="'+t+"ico "+t+'i-help"></i></button></div></div>'},bindStates:function(){var e=this;return e.state.on("change:text",function(t){e.getEl("body").firstChild.data=e.encode(t.value),e.state.get("rendered")&&e.updateLayoutRect()}),e.state.on("change:help",function(t){e.classes.toggle("has-help",t.value),e.state.get("rendered")&&e.updateLayoutRect()}),e._super()}}),Rr=Dt.extend({init:function(e){var t=this;t._super(e),t.classes.add("widget").add("label"),t.canFocus=!1,e.multiline&&t.classes.add("autoscroll"),e.strong&&t.classes.add("strong")},initLayoutRect:function(){var e=this,t=e._super();return e.settings.multiline&&(_e.getSize(e.getEl()).width>t.maxW&&(t.minW=t.maxW,e.classes.add("multiline")),e.getEl().style.width=t.minW+"px",t.startMinH=t.h=t.minH=Math.min(t.maxH,_e.getSize(e.getEl()).height)),t},repaint:function(){return this.settings.multiline||(this.getEl().style.lineHeight=this.layoutRect().h+"px"),this._super()},severity:function(e){this.classes.remove("error"),this.classes.remove("warning"),this.classes.remove("success"),this.classes.add(e)},renderHtml:function(){var e,t,n=this,i=n.settings.forId,r=n.settings.html?n.settings.html:n.encode(n.state.get("text"));return!i&&(t=n.settings.forName)&&(e=n.getRoot().find("#"+t)[0])&&(i=e._id),i?'<label id="'+n._id+'" class="'+n.classes+'"'+(i?' for="'+i+'"':"")+">"+r+"</label>":'<span id="'+n._id+'" class="'+n.classes+'">'+r+"</span>"},bindStates:function(){var e=this;return e.state.on("change:text",function(t){e.innerHtml(e.encode(t.value)),e.state.get("rendered")&&e.updateLayoutRect()}),e._super()}}),Cr=ut.extend({Defaults:{role:"toolbar",layout:"flow"},init:function(e){this._super(e),this.classes.add("toolbar")},postRender:function(){return this.items().each(function(e){e.classes.add("toolbar-item")}),this._super()}}),kr=Cr.extend({Defaults:{role:"menubar",containerCls:"menubar",ariaRoot:!0,defaults:{type:"menubutton"}}}),Er=Xt.extend({init:function(e){var t=this;t._renderOpen=!0,t._super(e),e=t.settings,t.classes.add("menubtn"),e.fixedWidth&&t.classes.add("fixed-width"),t.aria("haspopup",!0),t.state.set("menu",e.menu||t.render())},showMenu:function(e){var t,n=this;if(n.menu&&n.menu.visible()&&!1!==e)return n.hideMenu();n.menu||(t=n.state.get("menu")||[],n.classes.add("opened"),t.length?t={type:"menu",animate:!0,items:t}:(t.type=t.type||"menu",t.animate=!0),t.renderTo?n.menu=t.parent(n).show().renderTo():n.menu=g.create(t).parent(n).renderTo(),n.fire("createmenu"),n.menu.reflow(),n.menu.on("cancel",function(e){e.control.parent()===n.menu&&(e.stopPropagation(),n.focus(),n.hideMenu())}),n.menu.on("select",function(){n.focus()}),n.menu.on("show hide",function(e){e.control===n.menu&&(n.activeMenu("show"===e.type),n.classes.toggle("opened","show"===e.type)),n.aria("expanded","show"===e.type)}).fire("show")),n.menu.show(),n.menu.layoutRect({w:n.layoutRect().w}),n.menu.repaint(),n.menu.moveRel(n.getEl(),n.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"]),n.fire("showmenu")},hideMenu:function(){this.menu&&(this.menu.items().each(function(e){e.hideMenu&&e.hideMenu()}),this.menu.hide())},activeMenu:function(e){this.classes.toggle("active",e)},renderHtml:function(){var e,t=this,n=t._id,i=t.classPrefix,r=t.settings.icon,o=t.state.get("text"),s="";return(e=t.settings.image)?(r="none","string"!=typeof e&&(e=window.getSelection?e[0]:e[1]),e=" style=\"background-image: url('"+e+"')\""):e="",o&&(t.classes.add("btn-has-text"),s='<span class="'+i+'txt">'+t.encode(o)+"</span>"),r=t.settings.icon?i+"ico "+i+"i-"+r:"",t.aria("role",t.parent()instanceof kr?"menuitem":"button"),'<div id="'+n+'" class="'+t.classes+'" tabindex="-1" aria-labelledby="'+n+'"><button id="'+n+'-open" role="presentation" type="button" tabindex="-1">'+(r?'<i class="'+r+'"'+e+"></i>":"")+s+' <i class="'+i+'caret"></i></button></div>'},postRender:function(){var e=this;return e.on("click",function(t){t.control===e&&function(e,t){for(;e;){if(t===e)return!0;e=e.parentNode}return!1}(t.target,e.getEl())&&(e.focus(),e.showMenu(!t.aria),t.aria&&e.menu.items().filter(":visible")[0].focus())}),e.on("mouseenter",function(t){var n,i=t.control,r=e.parent();i&&r&&i instanceof Er&&i.parent()===r&&(r.items().filter("MenuButton").each(function(e){e.hideMenu&&e!==i&&(e.menu&&e.menu.visible()&&(n=!0),e.hideMenu())}),n&&(i.focus(),i.showMenu()))}),e._super()},bindStates:function(){var e=this;return e.state.on("change:menu",function(){e.menu&&e.menu.remove(),e.menu=null}),e._super()},remove:function(){this._super(),this.menu&&this.menu.remove()}}),Hr=kt.extend({Defaults:{defaultType:"menuitem",border:1,layout:"stack",role:"application",bodyRole:"menu",ariaRoot:!0},init:function(e){if(e.autohide=!0,e.constrainToViewport=!0,"function"==typeof e.items&&(e.itemsFactory=e.items,e.items=[]),e.itemDefaults)for(var t=e.items,n=t.length;n--;)t[n]=a.extend({},e.itemDefaults,t[n]);this._super(e),this.classes.add("menu"),e.animate&&11!==de.ie&&this.classes.add("animate")},repaint:function(){return this.classes.toggle("menu-align",!0),this._super(),this.getEl().style.height="",this.getEl("body").style.height="",this},cancel:function(){this.hideAll(),this.fire("select")},load:function(){var e,t=this;function n(){t.throbber&&(t.throbber.hide(),t.throbber=null)}t.settings.itemsFactory&&(t.throbber||(t.throbber=new Mt(t.getEl("body"),!0),0===t.items().length?(t.throbber.show(),t.fire("loading")):t.throbber.show(100,function(){t.items().remove(),t.fire("loading")}),t.on("hide close",n)),t.requestTime=e=(new Date).getTime(),t.settings.itemsFactory(function(i){0!==i.length?t.requestTime===e&&(t.getEl().style.width="",t.getEl("body").style.width="",n(),t.items().remove(),t.getEl("body").innerHTML="",t.add(i),t.renderNew(),t.fire("loaded")):t.hide()}))},hideAll:function(){return this.find("menuitem").exec("hideMenu"),this._super()},preRender:function(){var e=this;return e.items().each(function(t){var n=t.settings;if(n.icon||n.image||n.selectable)return e._hasIcons=!0,!1}),e.settings.itemsFactory&&e.on("postrender",function(){e.settings.itemsFactory&&e.load()}),e.on("show hide",function(t){t.control===e&&("show"===t.type?R.setTimeout(function(){e.classes.add("in")},0):e.classes.remove("in"))}),e._super()}}),Sr=Er.extend({init:function(e){var t,n,i,r,o=this;o._super(e),e=o.settings,o._values=t=e.values,t&&("undefined"!=typeof e.value&&function s(t){for(var r=0;r<t.length;r++){if(n=t[r].selected||e.value===t[r].value)return i=i||t[r].text,o.state.set("value",t[r].value),!0;if(t[r].menu&&s(t[r].menu))return!0}}(t),!n&&t.length>0&&(i=t[0].text,o.state.set("value",t[0].value)),o.state.set("menu",t)),o.state.set("text",e.text||i),o.classes.add("listbox"),o.on("select",function(t){var n=t.control;r&&(t.lastControl=r),e.multiple?n.active(!n.active()):o.value(t.control.value()),r=n})},bindStates:function(){var e=this;return e.on("show",function(t){var n,i;n=t.control,i=e.value(),n instanceof Hr&&n.items().each(function(e){e.hasMenus()||e.active(e.value()===i)})}),e.state.on("change:value",function(t){var n=function i(e,t){var n;if(e)for(var r=0;r<e.length;r++){if(e[r].value===t)return e[r];if(e[r].menu&&(n=i(e[r].menu,t)))return n}}(e.state.get("menu"),t.value);n?e.text(n.text):e.text(e.settings.text)}),e._super()}}),Mr=Dt.extend({Defaults:{border:0,role:"menuitem"},init:function(e){var t,n=this;n._super(e),e=n.settings,n.classes.add("menu-item"),e.menu&&n.classes.add("menu-item-expand"),e.preview&&n.classes.add("menu-item-preview"),"-"!==(t=n.state.get("text"))&&"|"!==t||(n.classes.add("menu-item-sep"),n.aria("role","separator"),n.state.set("text","-")),e.selectable&&(n.aria("role","menuitemcheckbox"),n.classes.add("menu-item-checkbox"),e.icon="selected"),e.preview||e.selectable||n.classes.add("menu-item-normal"),n.on("mousedown",function(e){e.preventDefault()}),e.menu&&!e.ariaHideMenu&&n.aria("haspopup",!0)},hasMenus:function(){return!!this.settings.menu},showMenu:function(){var e,t=this,n=t.settings,i=t.parent();if(i.items().each(function(e){e!==t&&e.hideMenu()}),n.menu){(e=t.menu)?e.show():((e=n.menu).length?e={type:"menu",items:e}:e.type=e.type||"menu",i.settings.itemDefaults&&(e.itemDefaults=i.settings.itemDefaults),(e=t.menu=g.create(e).parent(t).renderTo()).reflow(),e.on("cancel",function(n){n.stopPropagation(),t.focus(),e.hide()}),e.on("show hide",function(e){e.control.items&&e.control.items().each(function(e){e.active(e.settings.selected)})}).fire("show"),e.on("hide",function(n){n.control===e&&t.classes.remove("selected")}),e.submenu=!0),e._parentMenu=i,e.classes.add("menu-sub");var r=e.testMoveRel(t.getEl(),t.isRtl()?["tl-tr","bl-br","tr-tl","br-bl"]:["tr-tl","br-bl","tl-tr","bl-br"]);e.moveRel(t.getEl(),r),e.rel=r,r="menu-sub-"+r,e.classes.remove(e._lastRel).add(r),e._lastRel=r,t.classes.add("selected"),t.aria("expanded",!0)}},hideMenu:function(){var e=this;return e.menu&&(e.menu.items().each(function(e){e.hideMenu&&e.hideMenu()}),e.menu.hide(),e.aria("expanded",!1)),e},renderHtml:function(){var e,t=this,n=t._id,i=t.settings,r=t.classPrefix,o=t.state.get("text"),s=t.settings.icon,a="",l=i.shortcut,u=t.encode(i.url);function c(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function d(e){var t=i.match||"";return t?e.replace(new RegExp(c(t),"gi"),function(e){return"!mce~match["+e+"]mce~match!"}):e}function f(e){return e.replace(new RegExp(c("!mce~match["),"g"),"<b>").replace(new RegExp(c("]mce~match!"),"g"),"</b>")}return s&&t.parent().classes.add("menu-has-icons"),i.image&&(a=" style=\"background-image: url('"+i.image+"')\""),l&&(l=function(e){var t,n,i={};for(i=de.mac?{alt:"&#x2325;",ctrl:"&#x2318;",shift:"&#x21E7;",meta:"&#x2318;"}:{meta:"Ctrl"},e=e.split("+"),t=0;t<e.length;t++)(n=i[e[t].toLowerCase()])&&(e[t]=n);return e.join("+")}(l)),s=r+"ico "+r+"i-"+(t.settings.icon||"none"),e="-"!==o?'<i class="'+s+'"'+a+"></i>\xa0":"",o=f(t.encode(d(o))),u=f(t.encode(d(u))),'<div id="'+n+'" class="'+t.classes+'" tabindex="-1">'+e+("-"!==o?'<span id="'+n+'-text" class="'+r+'text">'+o+"</span>":"")+(l?'<div id="'+n+'-shortcut" class="'+r+'menu-shortcut">'+l+"</div>":"")+(i.menu?'<div class="'+r+'caret"></div>':"")+(u?'<div class="'+r+'menu-item-link">'+u+"</div>":"")+"</div>"},postRender:function(){var e=this,t=e.settings,n=t.textStyle;if("function"==typeof n&&(n=n.call(this)),n){var i=e.getEl("text");i&&(i.setAttribute("style",n),e._textStyle=n)}return e.on("mouseenter click",function(n){n.control===e&&(t.menu||"click"!==n.type?(e.showMenu(),n.aria&&e.menu.focus(!0)):(e.fire("select"),R.requestAnimationFrame(function(){e.parent().hideAll()})))}),e._super(),e},hover:function(){return this.parent().items().each(function(e){e.classes.remove("selected")}),this.classes.toggle("selected",!0),this},active:function(e){return function(e,t){var n=e._textStyle;if(n){var i=e.getEl("text");i.setAttribute("style",n),t&&(i.style.color="",i.style.backgroundColor="")}}(this,e),void 0!==e&&this.aria("checked",e),this._super(e)},remove:function(){this._super(),this.menu&&this.menu.remove()}}),Tr=Kt.extend({Defaults:{classes:"radio",role:"radio"}}),Pr=Dt.extend({renderHtml:function(){var e=this,t=e.classPrefix;return e.classes.add("resizehandle"),"both"===e.settings.direction&&e.classes.add("resizehandle-both"),e.canFocus=!1,'<div id="'+e._id+'" class="'+e.classes+'"><i class="'+t+"ico "+t+'i-resize"></i></div>'},postRender:function(){var e=this;e._super(),e.resizeDragHelper=new dt(this._id,{start:function(){e.fire("ResizeStart")},drag:function(t){"both"!==e.settings.direction&&(t.deltaX=0),e.fire("Resize",t)},stop:function(){e.fire("ResizeEnd")}})},remove:function(){return this.resizeDragHelper&&this.resizeDragHelper.destroy(),this._super()}});function Wr(e){var t="";if(e)for(var n=0;n<e.length;n++)t+='<option value="'+e[n]+'">'+e[n]+"</option>";return t}var Dr=Dt.extend({Defaults:{classes:"selectbox",role:"selectbox",options:[]},init:function(e){var t=this;t._super(e),t.settings.size&&(t.size=t.settings.size),t.settings.options&&(t._options=t.settings.options),t.on("keydown",function(e){var n;13===e.keyCode&&(e.preventDefault(),t.parents().reverse().each(function(e){if(e.toJSON)return n=e,!1}),t.fire("submit",{data:n.toJSON()}))})},options:function(e){return arguments.length?(this.state.set("options",e),this):this.state.get("options")},renderHtml:function(){var e,t=this,n="";return e=Wr(t._options),t.size&&(n=' size = "'+t.size+'"'),'<select id="'+t._id+'" class="'+t.classes+'"'+n+">"+e+"</select>"},bindStates:function(){var e=this;return e.state.on("change:options",function(t){e.getEl().innerHTML=Wr(t.value)}),e._super()}});function Nr(e,t,n){return e<t&&(e=t),e>n&&(e=n),e}function Ar(e,t,n){e.setAttribute("aria-"+t,n)}function Br(e,t){var n,i,r,o,s;"v"===e.settings.orientation?(r="top",i="height",n="h"):(r="left",i="width",n="w"),s=e.getEl("handle"),o=((e.layoutRect()[n]||100)-_e.getSize(s)[i])*((t-e._minValue)/(e._maxValue-e._minValue))+"px",s.style[r]=o,s.style.height=e.layoutRect().h+"px",Ar(s,"valuenow",t),Ar(s,"valuetext",""+e.settings.previewFilter(t)),Ar(s,"valuemin",e._minValue),Ar(s,"valuemax",e._maxValue)}var Or=Dt.extend({init:function(e){var t=this;e.previewFilter||(e.previewFilter=function(e){return Math.round(100*e)/100}),t._super(e),t.classes.add("slider"),"v"===e.orientation&&t.classes.add("vertical"),t._minValue=Pn.isNumber(e.minValue)?e.minValue:0,t._maxValue=Pn.isNumber(e.maxValue)?e.maxValue:100,t._initValue=t.state.get("value")},renderHtml:function(){var e=this._id,t=this.classPrefix;return'<div id="'+e+'" class="'+this.classes+'"><div id="'+e+'-handle" class="'+t+'slider-handle" role="slider" tabindex="-1"></div></div>'},reset:function(){this.value(this._initValue).repaint()},postRender:function(){var e,t,n,i,r,o,s,a,l,u,c,d,f,h,m=this;e=m._minValue,t=m._maxValue,"v"===m.settings.orientation?(n="screenY",i="top",r="height",o="h"):(n="screenX",i="left",r="width",o="w"),m._super(),function(e,t){function n(n){var i,r,o;i=Nr(i=(((i=m.value())+(o=e))/(t-o)+.05*n)*(t-(r=e))-r,e,t),m.value(i),m.fire("dragstart",{value:i}),m.fire("drag",{value:i}),m.fire("dragend",{value:i})}m.on("keydown",function(e){switch(e.keyCode){case 37:case 38:n(-1);break;case 39:case 40:n(1)}})}(e,t),s=e,a=t,l=m.getEl("handle"),m._dragHelper=new dt(m._id,{handle:m._id+"-handle",start:function(e){u=e[n],c=parseInt(m.getEl("handle").style[i],10),d=(m.layoutRect()[o]||100)-_e.getSize(l)[r],m.fire("dragstart",{value:h})},drag:function(e){var t=e[n]-u;f=Nr(c+t,0,d),l.style[i]=f+"px",h=s+f/d*(a-s),m.value(h),m.tooltip().text(""+m.settings.previewFilter(h)).show().moveRel(l,"bc tc"),m.fire("drag",{value:h})},stop:function(){m.tooltip().hide(),m.fire("dragend",{value:h})}})},repaint:function(){this._super(),Br(this,this.value())},bindStates:function(){var e=this;return e.state.on("change:value",function(t){Br(e,t.value)}),e._super()}}),zr=Dt.extend({renderHtml:function(){return this.classes.add("spacer"),this.canFocus=!1,'<div id="'+this._id+'" class="'+this.classes+'"></div>'}}),Lr=Er.extend({Defaults:{classes:"widget btn splitbtn",role:"button"},repaint:function(){var e,t,n=this.getEl(),i=this.layoutRect();return this._super(),e=n.firstChild,t=n.lastChild,xe(e).css({width:i.w-_e.getSize(t).width,height:i.h-2}),xe(t).css({height:i.h-2}),this},activeMenu:function(e){xe(this.getEl().lastChild).toggleClass(this.classPrefix+"active",e)},renderHtml:function(){var e,t,n=this,i=n._id,r=n.classPrefix,o=n.state.get("icon"),s=n.state.get("text"),a=n.settings,l="";return(e=a.image)?(o="none","string"!=typeof e&&(e=window.getSelection?e[0]:e[1]),e=" style=\"background-image: url('"+e+"')\""):e="",o=a.icon?r+"ico "+r+"i-"+o:"",s&&(n.classes.add("btn-has-text"),l='<span class="'+r+'txt">'+n.encode(s)+"</span>"),t="boolean"==typeof a.active?' aria-pressed="'+a.active+'"':"",'<div id="'+i+'" class="'+n.classes+'" role="button"'+t+' tabindex="-1"><button type="button" hidefocus="1" tabindex="-1">'+(o?'<i class="'+o+'"'+e+"></i>":"")+l+'</button><button type="button" class="'+r+'open" hidefocus="1" tabindex="-1">'+(n._menuBtnText?(o?"\xa0":"")+n._menuBtnText:"")+' <i class="'+r+'caret"></i></button></div>'},postRender:function(){var e=this.settings.onclick;return this.on("click",function(t){var n=t.target;if(t.control===this)for(;n;){if(t.aria&&"down"!==t.aria.key||"BUTTON"===n.nodeName&&-1===n.className.indexOf("open"))return t.stopImmediatePropagation(),void(e&&e.call(this,t));n=n.parentNode}}),delete this.settings.onclick,this._super()}}),Ir=ji.extend({Defaults:{containerClass:"stack-layout",controlClass:"stack-layout-item",endClass:"break"},isNative:function(){return!0}}),Fr=vt.extend({Defaults:{layout:"absolute",defaults:{type:"panel"}},activateTab:function(e){var t;this.activeTabId&&(t=this.getEl(this.activeTabId),xe(t).removeClass(this.classPrefix+"active"),t.setAttribute("aria-selected","false")),this.activeTabId="t"+e,(t=this.getEl("t"+e)).setAttribute("aria-selected","true"),xe(t).addClass(this.classPrefix+"active"),this.items()[e].show().fire("showtab"),this.reflow(),this.items().each(function(t,n){e!==n&&t.hide()})},renderHtml:function(){var e=this,t=e._layout,n="",i=e.classPrefix;return e.preRender(),t.preRender(e),e.items().each(function(t,r){var o=e._id+"-t"+r;t.aria("role","tabpanel"),t.aria("labelledby",o),n+='<div id="'+o+'" class="'+i+'tab" unselectable="on" role="tab" aria-controls="'+t._id+'" aria-selected="false" tabIndex="-1">'+e.encode(t.settings.title)+"</div>"}),'<div id="'+e._id+'" class="'+e.classes+'" hidefocus="1" tabindex="-1"><div id="'+e._id+'-head" class="'+i+'tabs" role="tablist">'+n+'</div><div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+t.renderHtml(e)+"</div></div>"},postRender:function(){var e=this;e._super(),e.settings.activeTab=e.settings.activeTab||0,e.activateTab(e.settings.activeTab),this.on("click",function(t){var n=t.target.parentNode;if(n&&n.id===e._id+"-head")for(var i=n.childNodes.length;i--;)n.childNodes[i]===t.target&&e.activateTab(i)})},initLayoutRect:function(){var e,t,n,i=this;t=(t=_e.getSize(i.getEl("head")).width)<0?0:t,n=0,i.items().each(function(e){t=Math.max(t,e.layoutRect().minW),n=Math.max(n,e.layoutRect().minH)}),i.items().each(function(e){e.settings.x=0,e.settings.y=0,e.settings.w=t,e.settings.h=n,e.layoutRect({x:0,y:0,w:t,h:n})});var r=_e.getSize(i.getEl("head")).height;return i.settings.minWidth=t,i.settings.minHeight=n+r,(e=i._super()).deltaH+=r,e.innerH=e.h-e.deltaH,e}}),Ur=Dt.extend({init:function(e){var t=this;t._super(e),t.classes.add("textbox"),e.multiline?t.classes.add("multiline"):(t.on("keydown",function(e){var n;13===e.keyCode&&(e.preventDefault(),t.parents().reverse().each(function(e){if(e.toJSON)return n=e,!1}),t.fire("submit",{data:n.toJSON()}))}),t.on("keyup",function(e){t.state.set("value",e.target.value)}))},repaint:function(){var e,t,n,i,r,o=this,s=0;e=o.getEl().style,t=o._layoutRect,r=o._lastRepaintRect||{};var a=document;return!o.settings.multiline&&a.all&&(!a.documentMode||a.documentMode<=8)&&(e.lineHeight=t.h-s+"px"),i=(n=o.borderBox).left+n.right+8,s=n.top+n.bottom+(o.settings.multiline?8:0),t.x!==r.x&&(e.left=t.x+"px",r.x=t.x),t.y!==r.y&&(e.top=t.y+"px",r.y=t.y),t.w!==r.w&&(e.width=t.w-i+"px",r.w=t.w),t.h!==r.h&&(e.height=t.h-s+"px",r.h=t.h),o._lastRepaintRect=r,o.fire("repaint",{},!1),o},renderHtml:function(){var e,t,n=this,i=n.settings;return e={id:n._id,hidefocus:"1"},a.each(["rows","spellcheck","maxLength","size","readonly","min","max","step","list","pattern","placeholder","required","multiple"],function(t){e[t]=i[t]}),n.disabled()&&(e.disabled="disabled"),i.subtype&&(e.type=i.subtype),(t=_e.create(i.multiline?"textarea":"input",e)).value=n.state.get("value"),t.className=n.classes,t.outerHTML},value:function(e){return arguments.length?(this.state.set("value",e),this):(this.state.get("rendered")&&this.state.set("value",this.getEl().value),this.state.get("value"))},postRender:function(){var e=this;e.getEl().value=e.state.get("value"),e._super(),e.$el.on("change",function(t){e.state.set("value",t.target.value),e.fire("change",t)})},bindStates:function(){var e=this;return e.state.on("change:value",function(t){e.getEl().value!==t.value&&(e.getEl().value=t.value)}),e.state.on("change:disabled",function(t){e.getEl().disabled=t.value}),e._super()},remove:function(){this.$el.off(),this._super()}}),Vr=function(){return{Selector:Fe,Collection:je,ReflowQueue:Ze,Control:ot,Factory:g,KeyboardNavigation:at,Container:ut,DragHelper:dt,Scrollable:pt,Panel:vt,Movable:Se,Resizable:bt,FloatPanel:kt,Window:Ut,MessageBox:jt,Tooltip:Wt,Widget:Dt,Progress:Nt,Notification:Bt,Layout:qt,AbsoluteLayout:$t,Button:Xt,ButtonGroup:Gt,Checkbox:Kt,ComboBox:Qt,ColorBox:en,PanelButton:tn,ColorButton:rn,ColorPicker:sn,Path:ln,ElementPath:un,FormItem:cn,Form:dn,FieldSet:fn,FilePicker:Fi,FitLayout:Ui,FlexLayout:Vi,FlowLayout:ji,FormatControls:yr,GridLayout:xr,Iframe:wr,InfoBox:_r,Label:Rr,Toolbar:Cr,MenuBar:kr,MenuButton:Er,MenuItem:Mr,Throbber:Mt,Menu:Hr,ListBox:Sr,Radio:Tr,ResizeHandle:Pr,SelectBox:Dr,Slider:Or,Spacer:zr,SplitButton:Lr,StackLayout:Ir,TabPanel:Fr,TextBox:Ur,DropZone:an,BrowseButton:Jt}},jr=function(e){e.ui?a.each(Vr(),function(t,n){e.ui[n]=t}):e.ui=Vr()};a.each(Vr(),function(e,t){g.add(t,e)}),jr(window.tinymce?window.tinymce:{}),o.add("modern",function(e){return yr.setup(e),Yt(e)})}();
<?php

namespace Modules\HumanResource\Http\Requests;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class MissedClockOutRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {



        $rules =  [
            'date' => 'required|date',
            'in'   => 'required|date_format:Y-m-d H:i',
            'out' => 'required|date_format:Y-m-d H:i|after_or_equal:in',
            'employee_id' => 'required|integer',
        ];

        if (!auth()->user()->hasAnyRole([
            'supervisor_' . config('organization_id') . '_',
            'education-manager_' . config('organization_id') . '_',
            'human-resource_' . config('organization_id') . '_',
            'website-editor_' . config('organization_id') . '_',
            'managing-director_' . config('organization_id') . '_',
        ])) {
            $rules['outNote'] = 'required|min:12'; // Enforces note for other roles
       }

        return $rules;

    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            'outNote.required' => 'Please provide a valid reason for your missed Clock-Out',
            'out.required' => 'Please missed clock Out time',
            'out.after_or_equal' => 'The clock-out time must be equal to or after the clock-in time.',


        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
//    protected function prepareForValidation()
//    {
//        $this->merge([
//            'in' => date('H:i', strtotime($this->in)), // Convert 'in' to 24-hour format
//            'out' => date('H:i', strtotime($this->out)), // Convert 'out' to 24-hour format
//        ]);
//    }


    protected function prepareForValidation()
    {
        // Step 1: Parse date from the request (the "base" date for the shift).
        // e.g., 2025-01-14
        $date = Carbon::parse($this->date);

        // Step 2: Combine that base date with the 'in' time.
        // e.g., "2025-01-14 23:00"
        $inDateTime = Carbon::parse($date->format('Y-m-d') . ' ' . $this->in);

        // Step 3: Combine that base date with the 'out' time.
        // e.g., "2025-01-14 01:00"
        $outDateTime = Carbon::parse($date->format('Y-m-d') . ' ' . $this->out);

        // Step 4: If outTime is earlier than inTime, assume next day.
        if ($outDateTime->lessThan($inDateTime)) {
            $outDateTime->addDay();
        }


        // Step 5: Merge the new times back. You can merge them as strings or full datetimes.
        // Here, we’ll still keep them as H:i in the final validated data,
        // but logically the 'out' is from the next day.
        $this->merge([
            'in'  => $inDateTime->format('Y-m-d H:i'), // Full datetime for 'in'
            'out' => $outDateTime->format('Y-m-d H:i'), // Full datetime for 'out'
            'date' => $outDateTime->format('Y-m-d'), // Update the date if necessary

        ]);





    }


}

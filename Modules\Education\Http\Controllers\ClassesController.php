<?php

namespace Modules\Education\Http\Controllers;

use App\BaseSetup;
use App\ClassProgram;
use App\Employee;
use App\IjazasanadMemorizationPlan;
use App\ProgramLevel;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\StudentHefzReport;
use App\StudentIjazasanadMemorizationReport;
use App\StudentNouranyaPlan;
use App\StudentNouranyaReport;
use App\StudentProgramLevel;
use App\Subject;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Matrix\Builder;
use Modules\UserActivityLog\Traits\LogActivity;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use Carbon\Carbon;
use App\ClassStudent;
use App\ClassTeacher;
use App\Http\Requests;
use App\ClassTeacherSubject;
use Illuminate\Http\Request;
use App\ClassSubjectTimetable;
use App\Http\Controllers\Controller;
use App\Cen_Emp;
use Tests\Psalm\LaravelPlugin\Models\Car;
use App\StudentRevisionReport;
use App\Traits\ImageStore;
use Modules\Education\Http\Requests\ClassesRequest;
use Modules\Education\Http\Requests\ClassReportRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Modules\Education\Http\Requests\ClassReportStaatisticsRequest;
use App\Services\StudentLevelService;

class ClassesController extends Controller
{
    use ImageStore;

    protected $studentLevelService;

    public function __construct(StudentLevelService $studentLevelService)
    {
        $this->studentLevelService = $studentLevelService;
    }

    public function getClassTeachers(Request $request)
    {
        $classId = $request->input('classId');
        $class = Classes::find($classId);

        if (!$class) {
            return response()->json(['error' => 'Class not found'], 404);
        }

        // Get teacher full names as an array.
        $classTeachers = $class->teachers()->pluck('full_name')->toArray();

        return response()->json(['teachers' => $classTeachers]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        
       

        if (is_null($request->input('classStsCount'))) {
            $request->merge(['stCountOperator' => null]);
        }


        try {

            $admin = auth()->user()->hasRole(
                ["managing-director_2_"]) || auth()->user()->hasRole('system_viewer_' . config('organization_id') . '_');

            $classesQuery = Classes::with([
                'center', 
                'programs.translations',
                'translations',
                'teachers'
            ])
                ->withCount(['students as active_students_count' => function ($query) {
                    $query->where('status', 'active')->select(DB::raw('count(distinct(student_id))'));
                }]) ->withCount(['hefz_reports as hefz_reports_count' => function ($query) {
                    $query->whereNotNull('hefz_from_surat')
                        ->whereNotNull('hefz_from_ayat')
                        ->whereNotNull('hefz_to_surat')
                        ->whereNotNull('hefz_to_ayat');
                }]);



            $perPage = 0;

            $classTeachers = \App\ClassTeacher::with('employee')
                ->get()
                ->groupBy('employee_id')  // Group by employee to get the classes count per teacher
                ->map(function ($group, $employeeId) {
                    $teacher = $group->first();  // Get the first item in the group to access shared properties
                    if ($teacher && $teacher->employee) {
                        $fullName = $teacher->employee->full_name;
                        $classesCount = $group->count();  // Count the number of items in the group
                        $label = sprintf("%s (%d)", $fullName, $classesCount);
                        return [$teacher->id => $label];
                    }
                    return null;  // Return null if there's no employee to avoid adding it to the list
                })
                ->filter()  // Filter out the null values
                ->sort()
                ->prepend('Select Teacher', '');


            $selectedTeacherId = $request->get('teachers');


            $hasNonEmptyCenters = $request->has('centers') && count(array_filter($request->input('centers', []))) > 0;

            if ($admin) {
                $keyword = $request->get('className');
                $centersKeyword = $request->get('centers');
                $programKeyword = $request->get('program');
                $perPage = 25;
                $classesQuery->when($request->filled("className"), function ($query, $make) use ($keyword) {
                    $query->where('class_code', 'LIKE', "%$keyword%")
                        ->orWhere('status', 'LIKE', "%$keyword%")
                        ->orWhereHas('translations', function ($tq) use ($keyword) {
                            $tq->where('name', 'LIKE', "%$keyword%");
                        })->orWhereHas('teachers', function ($tq) use ($keyword) {
                            $tq->where('full_name', 'LIKE', "%$keyword%");

                        });
                });

                $classesQuery->when($request->filled("teachers"), function ($query) use ($selectedTeacherId) {
                    // Adjust the query based on the selected teacher. This assumes that there's a relationship named 'teachers'
                    // on the Classes model that can be used to filter the classes by the selected teacher.
                    $query->whereHas('teachers', function ($q) use ($selectedTeacherId) {
                        $q->whereIn('employee_id', $selectedTeacherId);
                    });
                });
                $classesQuery->when($request->filled("program"), function ($query, $make) use ($programKeyword) {

                    $query->whereHas('programs', function ($tq) use ($programKeyword) {
                        $tq->where('id',  $programKeyword);
                    });


                });


                $classStsCount = is_null($request->get('classStsCount')) == true ? 0 : $request->get('classStsCount');

                $classesQuery->when($request->filled("classStsCount"), function ($query, $make) use ($classStsCount, $request) {
                    $query->has('students', $request->get('stCountOperator'), $classStsCount)
                        ->orderByRaw('active_students_count desc');
                });


                $classesQuery->when($hasNonEmptyCenters, function ($query, $make) use ($centersKeyword) {

                    $query->whereHas('center', function ($tq) use ($centersKeyword) {
                        $tq->whereIn('id', $centersKeyword);
                    });
                });


            }
            else {


                $super = Employee::whereHas('roles', function ($q) {
                    return $q->where('name', 'like', 'supervisor_%_');
                })->where('id', auth()->user()->id)->exists();


                if ($super == true) {
                    $cen_emp = Cen_Emp::where('emp_id', '=', auth()->user()->id)->pluck('cen_id');

                    $keyword = $request->get('className');
                    $centersKeyword = $request->get('centers');
                    $programKeyword = $request->get('program');
                    $classesQuery->with(['teachers' => function ($q) {

                        $q->where('employee_id', auth()->user()->id);
                    }])->whereIn('center_id', $cen_emp);

                    $classesQuery->when($request->filled("className"), function ($query, $make) use ($keyword) {
                        $query->where('class_code', 'LIKE', "%$keyword%")
                            ->orWhere('status', 'LIKE', "%$keyword%")
                            ->orWhereHas('translations', function ($tq) use ($keyword) {
                                $tq->where('name', 'LIKE', "%$keyword%");
                            })->orWhereHas('teachers', function ($tq) use ($keyword) {
                                $tq->where('full_name', 'LIKE', "%$keyword%");

                            });
                    });

                    $classesQuery->when($request->filled("teachers"), function ($query) use ($selectedTeacherId) {
                        // Adjust the query based on the selected teacher. This assumes that there's a relationship named 'teachers'
                        // on the Classes model that can be used to filter the classes by the selected teacher.
                        $query->whereHas('teachers', function ($q) use ($selectedTeacherId) {
                            $q->whereIn('employee_id', $selectedTeacherId);
                        });
                    });
                    $classesQuery->when($request->filled("program"), function ($query, $make) use ($programKeyword) {
                        $query->whereHas('programs', function ($tq) use ($programKeyword) {
                            $tq->where('id', 'LIKE', "%$programKeyword%");
                        });


                    });


                    $classStsCount = is_null($request->get('classStsCount')) == true ? 0 : $request->get('classStsCount');

                    $classesQuery->when($request->filled("classStsCount"), function ($query, $make) use ($classStsCount, $request) {
                        $query->has('students', $request->get('stCountOperator'), $classStsCount)
                            ->orderByRaw('active_students_count desc');
                    });


                    $classesQuery->when($hasNonEmptyCenters, function ($query, $make) use ($centersKeyword) {

                        $query->whereHas('center', function ($tq) use ($centersKeyword) {
                            $tq->whereIn('id', $centersKeyword);
                        });
                    });
                    $perPage = 25;


                } else {

                    $classesQuery->whereHas('teachers', function ($q) {

                        $q->where('employee_id', auth()->user()->id);
                    });

                    $keyword = $request->get('search');
                    $perPage = 25;
                    $cen_emp = Cen_Emp::where('emp_id', '=', auth()->user()->id)->pluck('cen_id');

                    $keyword = $request->get('className');
                    $centersKeyword = $request->get('centers');
                    $programKeyword = $request->get('program');
                    $classesQuery->when($request->filled("className"), function ($query, $make) use ($keyword) {
                        $query->where('class_code', 'LIKE', "%$keyword%")
                            ->orWhere('status', 'LIKE', "%$keyword%")
                            ->orWhereHas('translations', function ($tq) use ($keyword) {
                                $tq->where('name', 'LIKE', "%$keyword%");
                            })->orWhereHas('teachers', function ($tq) use ($keyword) {
                                $tq->where('full_name', 'LIKE', "%$keyword%");

                            });
                    });

                    $classesQuery->when($request->filled("teachers"), function ($query) use ($selectedTeacherId) {
                        // Adjust the query based on the selected teacher. This assumes that there's a relationship named 'teachers'
                        // on the Classes model that can be used to filter the classes by the selected teacher.
                        $query->whereHas('teachers', function ($q) use ($selectedTeacherId) {
                            $q->whereIn('employee_id', $selectedTeacherId);
                        });
                    });
                    $classesQuery->when($request->filled("program"), function ($query, $make) use ($programKeyword) {
                        $query->whereHas('programs', function ($tq) use ($programKeyword) {
                            $tq->where('id', 'LIKE', "%$programKeyword%");
                        });


                    });


                    $classStsCount = is_null($request->get('classStsCount')) == true ? 0 : $request->get('classStsCount');

                    $classesQuery->when($request->filled("classStsCount"), function ($query, $make) use ($classStsCount, $request) {
                        $query->has('students', $request->get('stCountOperator'), $classStsCount)
                            ->orderByRaw('active_students_count desc');
                    });


                    $classesQuery->when($hasNonEmptyCenters, function ($query, $make) use ($centersKeyword) {

                        $query->whereHas('center', function ($tq) use ($centersKeyword) {
                            $tq->whereIn('id', $centersKeyword);
                        });
                    });

                }
            }
            $year = Carbon::today()->year;
            $month = Carbon::today()->month;
            $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);


            $classes = $classesQuery->orderBy('class_code', 'asc')->paginate($perPage);


            $programs = \App\Program::with('translations')->get()->sortBy('title');


            return view('education::classes.index', compact('classes', 'year', 'month', 'days', 'programs','classTeachers'));
        } catch (\Exception $e) {

            dd($e->getMessage());
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }


    public
    function studentAttendanceReportSearch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'class' => 'required',
            'month' => 'required',
            'year' => 'required'
        ]);

        if ($validator->fails()) {

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $year = $request->year;
            $month = $request->month;
            $class_id = $request->class;
            $current_day = date('d');
            $class = Classes::findOrFail($request->class);
            $days = cal_days_in_month(CAL_GREGORIAN, $request->month, $request->year);

            $classes = Classes::with('programs')->get();

            $students = Student::whereHas('joint_classes', function ($q) use ($request) {
                $q->where('class_id', $request->class);
            })->get();

            $attendances = [];
            foreach ($students as $student) {
                $attendance = StudentHefzPlan::where('student_id', $student->id)->where('created_at', 'like', $request->year . '-' . $request->month . '%')->get();
                if (count($attendance) != 0) {
                    $attendances[] = $attendance;
                }
            }


            return view('education::classes.index', compact('classes', 'attendances', 'students', 'days', 'year', 'month', 'current_day',
                'class_id', 'class'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public
    function create()
    {



        $centers = Center::all()->pluck('name', 'id');
        $employees = Employee::with('roles')->get();
        $teachers = $employees->filter(function ($employees, $key) {
            return $employees->hasRole('teacher_' . config('organization_id') . '_');
        })->pluck('name', 'id');

        return view('education::classes.create', compact('centers', 'teachers'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public
    function store(Request $request)
    {

        $requestData = $request->all();

        $requestData['organization_id'] = config('organization_id');

        $class = Classes::create($requestData);

        foreach ($request->translate as $code => $translate) {
            $class->translateOrNew($code)->name = $translate['name'];
        }

        $class->save();


        Session::flash('flash_message', 'Class added!');


        return redirect('workplace/education/classes');
    }







    public function updateStudentLevel(Request $request)
    {
       
        $validatedData = $request->validate([
            'student_id' => 'required|integer|exists:students,id',
            'class_id' => 'required|integer|exists:classes,id',
            'level_id' => 'required|integer|exists:program_levels,id',
            'program_id' => 'required|integer|exists:programs,id',
            'program_title' => 'required|string'
        ]);

        $studentId = $validatedData['student_id'];
        $classId = $validatedData['class_id'];
        $levelId = $validatedData['level_id'];
        $programId = $validatedData['program_id'];
        $programTitle = strtolower($validatedData['program_title']);
        
            DB::beginTransaction();
        try {
                $this->studentLevelService->updateLevel($studentId, $classId, $levelId);
          

                if (stripos($programTitle, 'ijazasand') !== false) {
                    // Update IjazasanadMemorizationPlan
                    $query = IjazasanadMemorizationPlan::where('student_id', $studentId)
                    ->where('class_id', $classId)->update(['level_id' => $levelId]);
                    
                } elseif (strpos($programTitle, 'nouranya') !== false || strpos($programTitle, 'nuraniyah') !== false) {
                    // Update StudentNouranyaPlan
                    $query = StudentNouranyaPlan::where('student_id', $studentId)
                        ->where('class_id', $classId)->update(['level_id' => $levelId]);
                
                    
                }
                else {
                    $query = StudentHefzPlan::where('student_id', $studentId)
                        ->where('class_id', $classId)->update(['level_id' => $levelId]);
                }
           
            // }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Student level updated successfully.'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Failed to update student level: ' . $e->getMessage(), ['student_id' => $studentId, 'class_id' => $classId, 'level_id' => $levelId]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to update student level. ' . $e->getMessage()
            ], 500);
        }
    }


    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public
    function show(Request $request, $id)
    {



        if ($request->ajax()) {


            
            $class = Classes::findOrFail($id);
            $program = $class->programs->first(); // Assumes one program per class
            $programTitle = strtolower($program->title);

// Build the base query
            $classQuery = Student::select('*')
                ->whereHas('joint_classes', function ($q) use ($id) {
                    $q->where('class_id', $id);
                })
                ->with('user')
                ->with(['joint_classes' => function ($q) use ($id) {
                    $q->where('class_id', $id);
                }])
                ->with('admissions')
                ->where('status', 'active')
                ->withCount('reports');

// Load the appropriate relationship based on program
            if (stripos($programTitle, 'ijazasand') !== false) {
                $classQuery->with('completedIjazasanadReportforCurrentMonth');
                $classQuery = $classQuery->with('ijazasanad_memorization_plans.programlevel');
                return $this->buildIjazasanadDataTable($classQuery, $id, $program);
            } elseif (strpos($programTitle, 'nouranya') !== false || strpos($programTitle, 'nuraniyah') !== false) {
               
               
                $classQuery->with('completedNouranyaReportforCurrentMonth');
                $classQuery = $classQuery->with('nouranya_plans.programlevel');
                return $this->buildNouranyaDataTable($classQuery, $id, $program);

            } else  {
                $classQuery->with('completedHefzReportforCurrentMonth');
                $classQuery = $classQuery->with('studentProgramLevels.programlevel');
                return $this->buildDefaultDataTable($classQuery, $id, $program);
            }




        }

        $class = Classes::with('teachers')->with('students')->findOrFail($id);


//        $programs = Program::whereNotIn('id', $class->programs->pluck('id')->toArray())->get();
//        $programs = Program::whereIn('id', $class->programs->pluck('id')->toArray())->get();
        $programs = Program::get();


        $teachers = [];
        // if(auth()->user()->can('add teachers_to_class')){
        $teachers = Employee::orderBy('full_name')->get()->filter(function ($employee) use ($class) {
            return !in_array($employee->id, $class->teachers->pluck('id')->toArray());
        })->pluck('name', 'id');

        $subjects = Subject::all()->pluck('title', 'id')->sortBy('title');

        // }

        $class_teachers = ClassTeacher::where('class_id', $class->id)
            ->where('end_date', null)
            ->whereHas('employee', function ($query) {
                $query->whereNull('deleted_at');
            })->with('employee')->with('subjects')->get();



        $class_programs = [];


        $class_teachers = $class_teachers->isEmpty() == true ? null : $class_teachers;


        foreach ($class->programs as $key => $program) {
            $data = [];
            $data['info'] = $program;


            if (isset($program->setting['special_program_code']) && $program->setting['special_program_code'] == 'hefz') {
                $teacher = $class_teachers;
                $data['teacher'] = $teacher;

                if (($teacher) && ($teacher[0]->subjects()->where('program_id', $program->id)->exists())) {
                    if (($teacher[0]->subjects()->where('program_id', $program->id)->first()->timetable()->exists())) {
                        $data['timetable'] = $teacher[0]->subjects()->where('program_id', $program->id)->first()->timetable()->first();
//                    $data['class_teacher_subject_id'] = $teacher->subjects()->where('program_id', $program->id)->first()->id;
                    }
                }

            } else {


                $teacher = $class_teachers;
                $data['teacher'] = $teacher;


                if (isset($program->levels->find($program->pivot->program_level_id)->subjects)) {


                    foreach ($program->levels->find($program->pivot->program_level_id)->subjects as $index => $subject) {
                        $data['class_subjects'][$index] = $subject;


//                        $teacher = $class_teachers->filter(function ($teacher) use ($subject) {
//                            return (count($teacher->subjects) && in_array($subject->id, $teacher->subjects->pluck('subject_id')->toArray()));
//                        })->first();
                        $data['class_subjects'][$index]['teacher'] = $teacher;
                        if ($teacher) {
                            $classTeacherSubjectId = ClassTeacherSubject::where('class_teacher_id', $class_teachers[0]->id)->first()->id;

                            $data['class_subjects'][$index]['timetable'] = ClassSubjectTimetable::where('class_teacher_subject_id', $classTeacherSubjectId)->first();
                            $data['class_subjects'][$index]['class_teacher_subject_id'] = $classTeacherSubjectId;
                        }
                    }
                }
            }


            $class_programs[$key] = $data;
        }


        // return $class_programs;

        $classes = Classes::all()->sortBy('name')->pluck('name', 'id');



        return view('education::classes.show', compact('class', 'teachers', 'programs', 'class_programs', 'classes', 'subjects'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public
    function edit($id)
    {
        $class = Classes::findOrFail($id);
        $centers = Center::all()->pluck('name', 'id');


        return view('education::classes.edit', compact('class', 'centers'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public
    function update($id, Request $request)
    {

        $requestData = $request->all();

        $class = Classes::findOrFail($id);
        $class->update($requestData);

        foreach ($request->translate as $code => $translate) {
            $class->translateOrNew($code)->name = $translate['name'];
        }

        $class->save();

        Session::flash('flash_message', 'Class updated!');

        return redirect('workplace/education/classes');
    }



//    public
//    function programs(Request $request)
//    function updateClassPrograms(Request $request)
//    {
//
//        // Authorization check (assuming a method exists for this purpose)
//        if (!auth()->user()->can('edit class_programs')) {
//            abort(403);
//        }
//
//        $classId = $request->class_id;
//
//        // Start the transaction
//        DB::beginTransaction();
//
//        try {
//
//
//            // First, detach or remove existing relationships that are not in the current selection
//            // This step ensures that only the selected programs are stored
//            DB::table('class_programs')
//                ->where('class_id', $classId)
//                ->whereNotIn('program_id', $request->class_programs ?? [])
//                ->delete();
//
//
//            // Process only the selected programs
//            if (!empty($request->class_programs)) {
//                foreach ($request->class_programs as $programId) {
//                    $programLevelId = $request->class_programs_level[$programId] ?? null;
//
//
//
//                    // Use updateOrCreate to update existing or insert new records in the pivot table
//                    ClassProgram::updateOrCreate(
//                        [
//                            'class_id' => (int) $classId,
//                            'program_id' => (int) $programId,
//                        ],
//                        [
//                            'program_level_id' => $programLevelId,
//                            'created_at' => Carbon::now(), // Optional: Eloquent usually handles these automatically
//                            'updated_at' => Carbon::now(), // Optional: Eloquent usually handles these automatically
//                        ]
//                    );
//
//
//
//                }
//            }
//
//
//            Session::flash('flash_message', 'Program updated!');
//
//            if ($request->ajax()) {
//                return response()->json(['status' => 'success']);
//            }
//
//            return redirect()->back();
//        }catch (\Exception $e) {
//                // Rollback the transaction if an error occurs
//                DB::rollBack();
//
//                // Log the error for debugging
//                \Log::error('Failed to update class programs: '.$e->getMessage());
//
//                // Return an error response for AJAX requests
//                if ($request->ajax()) {
//                    return response()->json(['status' => 'error', 'message' => 'Failed to update class programs: '.$e->getMessage()]);
//                }
//
//                // Flash error message for non-AJAX requests
//                Session::flash('error_message', 'Failed to update programs. Please try again.');
//
//                return redirect()->back()->withErrors(['error' => 'Failed to update programs. Please try again.']);
//            }
//    }

    public function updateClassPrograms(Request $request)
    {




        $classId = $request->class_id;

        // Start the transaction
        DB::beginTransaction();

        try {
            // Find the class by ID
            $class = Classes::findOrFail($classId);

            // Prepare an array for sync with program_id and additional pivot data (program_level_id)
            $programData = [];



//            if (!empty($request->class_programs)) {
//                foreach ($request->class_programs as $programId) {
//                    $programLevelId = $request->class_programs_level ?? null;
//                    $programData[$request->class_programs] = ['program_level_id' => $programLevelId];
//                }
//            }




            // Sync the programs with the class
            $class->programs()->sync($request->class_programs);

            // Commit the transaction
            DB::commit();

            // Flash success message
            Session::flash('flash_message', 'Program updated!');

            if ($request->ajax()) {
                return response()->json(['status' => 'success']);
            }

            return redirect()->back();
        } catch (\Exception $e) {
            // Rollback the transaction if an error occurs
            DB::rollBack();

            // Log the error for debugging
            \Log::error('Failed to update class programs: ' . $e->getMessage());

            // Return an error response for AJAX requests
            if ($request->ajax()) {
                return response()->json(['status' => 'error', 'message' => 'Failed to update class programs: ' . $e->getMessage()]);
            }

            // Flash error message for non-AJAX requests
            Session::flash('error_message', 'Failed to update programs. Please try again.');

            return redirect()->back()->withErrors(['error' => 'Failed to update programs. Please try again.']);
        }
    }


    public function updateClassCode(Request $request)
    {


        // Validate the request
        $validator = Validator::make($request->all(), [
            'class_code' => 'required|unique:classes,class_code,' . $request->class_id,
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => $validator->errors()->first('class_code')
            ], 400);
        }

        // Find the class and update its class_code
        $class = Classes::findOrFail($request->class_id);
        $class->class_code = $request->class_code;
        $class->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Class code updated successfully'
        ]);
    }
    public
    function reports($id)
    {
        $class = Classes::find($id);

        $month = Carbon::create(date('Y'), date('m'), 01);

        return view('education::classes.reports.index', compact('class', 'month'));
    }

    /**
     * Add a teacher to a class.
     *
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public
    function addTeacher(Request $request)
    {

        DB::beginTransaction();
        try {
            $class_teacher = new ClassTeacher();
            $class_teacher->class_id = $request->class_id;
            $class_teacher->employee_id = $request->teacher_id;
            $class_teacher->start_date = $request->start_at;
            $class_teacher->save();

            $teacher_subject = new ClassTeacherSubject();
            $teacher_subject->class_teacher_id = $class_teacher->id;
            $teacher_subject->subject_id = $request->get('subj_id');
            $teacher_subject->program_id = $request->program_id;
            $teacher_subject->start_date = $request->start_at;

            $teacher_subject->save();
            DB::commit();

            if ($request->ajax()) {
                return response()->json(['status' => 'success', 'message' => 'Teacher was added Succesfully!']);
            } else {
                Session::flash('flash_message', 'Teacher was added Succesfully!');
                return redirect()->back();
            }
        } catch (\Exception $e) {

            DB::rollBack();
            LogActivity::errorLog($e->getMessage() . ' - Error has been detected for Journal update');
            if ($request->ajax()) {
                return response()->json(['status' => 'error', 'message' => 'Something Went Wrong!']);
            } else {
                Toastr::error(__('common.Something Went Wrong'), __('common.Error'));
                return back();
            }
        }

    }

    /**
     * Change student class
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public
    function changeStudentClass(Request $request)
    {

        // validate and check authorization
        $this->validate($request, [
            "student_id" => "required",
            "class_id" => "required",
            "new_class_id" => "required",
            "ended_at" => "required|date",
            "start_at" => "required|date",
        ]);

        // delete current teacher
        $class_student = ClassStudent::where('student_id', $request->student_id)
            ->where('class_id', $request->class_id)
            ->where('end_date', NULL)
            ->first();
        if ($class_student) {
            $class_student->end_date = $request->ended_at;
            $class_student->deleted_at = Carbon::now();
            $class_student->save();

            $new_class_student = new ClassStudent();

            $new_class_student->student_id = $request->student_id;
            $new_class_student->class_id = $request->new_class_id;
            $new_class_student->start_date = $request->start_at;

            $new_class_student->save();

        } else {

            // return with error

            Session::flash('flash_message', 'Error while Transfering Student!');
        }

        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public
    function destroy($id)
    {
        Classes::destroy($id);

        Session::flash('flash_message', 'Class deleted!');

        return redirect('workplace/education/classes');
    }


    public function updateStudentExamReadiness(Request $request)
    {





        $returnMessage = '';
        if(empty($request->get('examReadiness')) || is_null($request->get('examReadiness')))
        {
            $examReadiness = NULL;
            $examReadinessDate = NULL;
            $returnMessage = 'Student exam readiness status updated';
        }else{

            $examReadiness = 'on';
            $examReadinessDate = Carbon::now();

            $returnMessage = 'student is now ready for exam';


        }

        Student::where('user_id',$request->get('userId'))->update([

            'ready_for_exam' => $examReadiness,
            'exam_readiness_date' => $examReadinessDate


        ]);


        return response()->json($returnMessage,200);
    }


    /**
     * Add one or more teachers to a class.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    function addTeachers(Request $request)
    {
        DB::beginTransaction();
        try {
            $class = Classes::findOrFail($request->class_id);
            $teacherId = $request->teacher; // Expecting an array of teacher IDs

                // Check if the teacher is already assigned to the class
                $existingAssignment = $class->teachers()->where('employee_id', $teacherId)->exists();
                if (!$existingAssignment) {
                    // Create the ClassTeacher record
                    $class_teacher = new ClassTeacher();
                    $class_teacher->class_id = $request->class_id;
                    $class_teacher->employee_id = $teacherId;
                    $class_teacher->start_date = Carbon::now();
                    $class_teacher->save();

                    // Create the ClassTeacherSubject record
                    $teacher_subject = new ClassTeacherSubject();
                    $teacher_subject->class_teacher_id = $class_teacher->id;
                    $teacher_subject->subject_id = $request->get('subj_id');
                    $teacher_subject->program_id = $request->program_id;
                    $teacher_subject->start_date = Carbon::now();
                    $teacher_subject->save();
                }


            DB::commit();

            if ($request->ajax()) {
                return response()->json(['status' => 'success', 'message' => 'Teachers were added successfully!']);
            } else {
                Session::flash('flash_message', 'Teachers were added successfully!');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            DB::rollBack();
            LogActivity::errorLog($e->getMessage() . ' - Error has been detected for teacher assignment');
            if ($request->ajax()) {
                return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
                return response()->json(['status' => 'error', 'message' => 'Something went wrong!']);
            } else {
                Toastr::error(__('common.Something Went Wrong'), __('common.Error'));
                return back();
            }
        }
    }

    public function detachTeachers(Request $request)
    {


        DB::beginTransaction();
        try {
            // Find the class by ID
            $class = Classes::findOrFail($request->class_id);

            // Teachers to be removed (expecting an array of teacher IDs)
            $teacherId = $request->removed_teacher;

//            foreach ($teachers as $teacher_id) {
                // Check if the teacher is already assigned to the class
                $existingAssignment = $class->teachers()->where('employee_id', $teacherId)->exists();
                if ($existingAssignment) {
                    // Detach the teacher from the class
                    $class->teachers()->detach($teacherId);

                }
//            }

            DB::commit();

            if ($request->ajax()) {
                return response()->json(['status' => 'success', 'message' => 'Teachers were successfully removed from the class!']);
            } else {
                Session::flash('flash_message', 'Teacher was successfully removed from the class!');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            DB::rollBack();
            LogActivity::errorLog($e->getMessage() . ' - Error detected during teacher removal');
            if ($request->ajax()) {
                return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
            } else {
                Toastr::error(__('common.Something Went Wrong'), __('common.Error'));
                return back();
            }
        }
    }

    protected function buildNouranyaDataTable($classQuery, $id, $program)
    {
        return \DataTables::eloquent($classQuery)
            ->addColumn('studentName', function ($row) {
                return '<a target="_blank" href="'
                    . route('students.show', ['id' => $row->user_id])
                    . '">' . e($row->full_name) . '</a>';
            })
            ->addColumn('levelDetails', function ($row) use ($id, $program) {

                $programId = $program->id;
                $programTitle = $program->title; // Original Title (not lowercased)
                $levels = ProgramLevel::where('program_id', $programId)->get()->pluck('title', 'id');


                // We assume $row->studentNouranyaPlan is a single record
                $currentPlan = $row->nouranya_plans->first();
                $currentLevelId = $row->studentProgramLevels->first();

                // Check role
                $isSupervisorOrDirector = auth()->user()->hasRole('supervisor_' . config('organization_id') . '_')
                    || auth()->user()->hasRole('managing-director_' . config('organization_id') . '_');

                if ($isSupervisorOrDirector) {
                    $dropdown = '<select class="level-select select2 form-control" '
                        . 'data-program-id="' . $programId . '" '
                        . 'data-program-title="' . e($programTitle) . '" '
                        . 'data-student-id="' . $row->id . '" '
                        . 'data-class-id="' . $id . '">';
                    $dropdown .= '<option value="" disabled '
                        . (!$currentLevelId ? 'selected' : '')
                        . '>Select one</option>';

                    foreach ($levels as $levelId => $title) {
                        $selected = $currentLevelId && $currentLevelId->level_id == $levelId ? 'selected' : '';
                        $dropdown .= '<option value="' . $levelId . '" ' . $selected . '>' . e($title) . '</option>';
                    }
                    $dropdown .= '</select>';

                    return $dropdown;
                } else {
                    if ($currentPlan && $currentPlan->programlevel) {
                        return e($currentPlan->programlevel->title);
                    }
                    return 'No level assigned';
                }
            })
            // Note: hasHefzReports is not included here, as requested
            ->addColumn('nationality', function ($row) {
                return Str::upper($row->nationality);
            })
            ->addColumn('classStartDate', function ($row) {
                return \Carbon\Carbon::parse(
                    optional($row->joint_classes->first())->pivot->start_date
                )->diffForHumans();
            })
            ->addColumn('hasReports', function ($row) use ($request, $id, $programTitle) {
                $stReportShowRoute = route('reports.create', ['id' => $id, 'from_date' => Carbon::now()->toDateString()]);
                
                // Get report count based on program type using the new methods - passing the class ID
                $count = match(true) {
                    stripos($programTitle, 'ijazasand') !== false => 
                        $row->countUniqueIjazasanadReportDays($id),
                        
                    strpos($programTitle, 'nouranya') !== false || strpos($programTitle, 'nuraniyah') !== false => 
                        $row->countUniqueNouranyaReportDays($id),
                        
                    default => 
                        $row->countUniqueHefzReportDays($id)
                };
                
                return '<a target="_blank" href="' . $stReportShowRoute . '" class="ui teal circular">' . $count . '</a>';
            })
            ->addColumn('status', function ($row) {
                return $row->status;
            })
            ->addColumn('addTransferDetails', function ($row) {
                if (!is_null(optional($row->joint_classes->first())->pivot->transfer_at)) {
                    $dataTooltip = optional($row->joint_classes->first())->pivot->transfer_at;
                    $details = 'T - ' . \Carbon\Carbon::parse($dataTooltip)->diffForHumans();
                    return '<a href="#"><strong data-tooltip="' . $dataTooltip . '">' . $details . '</strong></a>';
                }

                if (!is_null(optional($row->joint_classes->first())->pivot->added_at)) {
                    $dataTooltip = optional($row->joint_classes->first())->pivot->added_at;
                    $details = 'A - ' . \Carbon\Carbon::parse($dataTooltip)->diffForHumans();
                    return '<a href="#"><strong data-tooltip="' . $dataTooltip . '">' . $details . '</strong></a>';
                }
            })
            ->addColumn('examReady', function ($row) {
                $checked = $row->ready_for_exam == 'on' ? 'checked' : '';
                return '<div class="ui toggle checkbox">
                <input data-userid="' . $row->user_id . '" 
                       data-examReadiness="' . $row->ready_for_exam . '"
                       type="checkbox" ' . $checked . '
                       name="ready_for_exam"
                       class="readyForExamCheckbox">
                <label></label>
            </div>';
            })
            ->addColumn('action', function ($row) {
                $details = '<div class="btn-group">
                <button type="button" class="btn btn-primary btn-xs dropdown-toggle"
                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Actions <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">';

                if (isset($row->ready_for_exam)) {
                    $details .= '<li><a target="_blank" href="'
                        . route('online-exam.student', [$row->id, $row->joint_classes->first()->id])
                        . '">Exam Hub</a></li>';
                }

                // No hasHefzReports column, so we skip that part

                $details .= '
                <li>
                    <a data-toggle="modal"
                       data-target="#change_student_class"
                       href="#"
                       class="changeStudentClassTrgBtn"
                       id="changeStudentClassTrgBtn"
                       data-student_id="' . $row->id . '"
                       data-st_fullname="' . $row->full_name . '">
                       Change Students Class
                    </a>
                </li>
                <li>
                    <a data-catid="' . $row->id . '" data-toggle="modal" data-target="#delete">
                        Delete Student
                    </a>
                </li>
            </ul>
            </div>';

                return $details;
            })
            ->setRowClass(function ($row) {
                // Optionally highlight certain rows
            })
            ->rawColumns([
                'action',
                'addTransferDetails',
                'examReady',
                'levelDetails',
                'studentName',
                'hasReports'
            ])
            ->make(true);
    }



    protected function buildIjazasanadDataTable($classQuery, $id, $program)
    {
        return \DataTables::eloquent($classQuery)
            ->addColumn('studentName', function ($row) {
                return '<a target="_blank" href="'
                    . route('students.show', ['id' => $row->user_id])
                    . '">' . e($row->full_name) . '</a>';
            })
            ->addColumn('levelDetails', function ($row) use ($id, $program) {

                $programId = $program->id;
                $programTitle = $program->title;
                $levels = ProgramLevel::where('program_id', $programId)->get()->pluck('title', 'id');

                // We assume $row->ijazasanadMemorizationPlan is a single record
                $currentPlan = $row->ijazasanad_memorization_plans->first();
                $currentLevelId = $row->studentProgramLevels->first();

                $isSupervisorOrDirector = auth()->user()->hasRole('supervisor_' . config('organization_id') . '_')
                    || auth()->user()->hasRole('managing-director_' . config('organization_id') . '_');

                if ($isSupervisorOrDirector) {
                    $dropdown = '<select class="level-select select2 form-control" '
                        . 'data-program-id="' . $programId . '" '
                        . 'data-program-title="' . e($programTitle) . '" '
                        . 'data-student-id="' . $row->id . '" '
                        . 'data-class-id="' . $id . '">';
                    $dropdown .= '<option value="" disabled '
                        . (!$currentLevelId ? 'selected' : '')
                        . '>Select one</option>';

                    foreach ($levels as $levelId => $title) {
                        $selected = $currentLevelId && $currentLevelId->level_id == $levelId ? 'selected' : '';
                        $dropdown .= '<option value="' . $levelId . '" ' . $selected . '>' . e($title) . '</option>';
                    }
                    $dropdown .= '</select>';

                    return $dropdown;
                } else {
                    if ($currentPlan && $currentPlan->programlevel) {
                        return e($currentPlan->programlevel->title);
                    }
                    return 'No level assigned';
                }
            })
            ->addColumn('hasReports', function ($row) use ($request, $id, $programTitle) {
                $stReportShowRoute = route('reports.create', ['id' => $id, 'from_date' => Carbon::now()->toDateString()]);
                
                // Get report count based on program type using the new methods - passing the class ID
                $count = match(true) {
                    stripos($programTitle, 'ijazasand') !== false => 
                        $row->countUniqueIjazasanadReportDays($id),
                        
                    strpos($programTitle, 'nouranya') !== false || strpos($programTitle, 'nuraniyah') !== false => 
                        $row->countUniqueNouranyaReportDays($id),
                        
                    default => 
                        $row->countUniqueHefzReportDays($id)
                };
                
                return '<a target="_blank" href="' . $stReportShowRoute . '" class="ui teal circular">' . $count . '</a>';
            })
            // Note: hasHefzReports is not included here either, as requested
            ->addColumn('nationality', function ($row) {
                return Str::upper($row->nationality);
            })
            ->addColumn('classStartDate', function ($row) {
                return \Carbon\Carbon::parse(
                    optional($row->joint_classes->first())->pivot->start_date
                )->diffForHumans();
            })
            ->addColumn('status', function ($row) {
                return $row->status;
            })
            ->addColumn('addTransferDetails', function ($row) {
                if (!is_null(optional($row->joint_classes->first())->pivot->transfer_at)) {
                    $dataTooltip = optional($row->joint_classes->first())->pivot->transfer_at;
                    $details = 'T - ' . \Carbon\Carbon::parse($dataTooltip)->diffForHumans();
                    return '<a href="#"><strong data-tooltip="' . $dataTooltip . '">' . $details . '</strong></a>';
                }

                if (!is_null(optional($row->joint_classes->first())->pivot->added_at)) {
                    $dataTooltip = optional($row->joint_classes->first())->pivot->added_at;
                    $details = 'A - ' . \Carbon\Carbon::parse($dataTooltip)->diffForHumans();
                    return '<a href="#"><strong data-tooltip="' . $dataTooltip . '">' . $details . '</strong></a>';
                }
            })
            ->addColumn('examReady', function ($row) {
                $checked = $row->ready_for_exam == 'on' ? 'checked' : '';
                return '<div class="ui toggle checkbox">
                <input data-userid="' . $row->user_id . '" 
                       data-examReadiness="' . $row->ready_for_exam . '"
                       type="checkbox" ' . $checked . '
                       name="ready_for_exam"
                       class="readyForExamCheckbox">
                <label></label>
            </div>';
            })
            ->addColumn('action', function ($row) {
                $details = '<div class="btn-group">
                <button type="button" class="btn btn-primary btn-xs dropdown-toggle"
                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Actions <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">';

                if (isset($row->ready_for_exam)) {
                    $details .= '<li><a target="_blank" href="'
                        . route('online-exam.student', [$row->id, $row->joint_classes->first()->id])
                        . '">Exam Hub</a></li>';
                }

                $details .= '
                <li>
                    <a data-toggle="modal"
                       data-target="#change_student_class"
                       href="#"
                       class="changeStudentClassTrgBtn"
                       id="changeStudentClassTrgBtn"
                       data-student_id="' . $row->id . '"
                       data-st_fullname="' . $row->full_name . '">
                       Change Students Class
                    </a>
                </li>
                <li>
                    <a data-catid="' . $row->id . '"  data-toggle="modal" data-target="#delete">
                        Delete Student
                    </a>
                </li>
            </ul>
            </div>';

                return $details;
            })
            ->setRowClass(function ($row) {
                // Optionally highlight rows
            })
            ->rawColumns([
                'action',
                'addTransferDetails',
                'examReady',
                'levelDetails',
                'studentName',
                'hasReports'
            ])
            ->make(true);
    }


    protected function buildDefaultDataTable($classQuery, $id, $program)
    {
        $programTitle = strtolower($program->title);
        
        return \DataTables::eloquent($classQuery)
            ->addColumn('studentName', function ($row) {
                return '<a target="_blank" href="' . route('students.show', ['id' => $row->user_id]) . '">' . e($row->full_name) . '</a>';
            })
            ->addColumn('levelDetails', function ($row) use ($id) {
                $class = Classes::find($id);
                $program = $class->programs->first(); // Assuming one program per class
                $programId = $program->id; // Assuming one program per class
                $programtitle = $program->title; // Assuming one program per class
                $levels = ProgramLevel::where('program_id', $programId)->get()->pluck('title', 'id');
                $currentLevel = $row->studentProgramLevels->first(); // Assuming one level per student

// Check if the logged-in user has the 'supervisor' or 'managing-director' role
                $isSupervisorOrDirector = auth()->user()->hasRole('supervisor_' . config('organization_id') . '_') || auth()->user()->hasRole('managing-director_' . config('organization_id') . '_');

                if ($isSupervisorOrDirector) {
                    $dropdown = '<select class="level-select select2 form-control" data-program-id="'.$programId.'" data-program-title="'.$programtitle.'" data-student-id="' . $row->id . '" data-class-id="' . $id . '">';
                    $dropdown .= '<option value="" disabled ' . ($currentLevel ? '' : 'selected') . '>Select one</option>'; // Default option
                    foreach ($levels as $levelId => $title) {
                        $selected = $currentLevel && $currentLevel->level_id == $levelId ? 'selected' : '';
                        $dropdown .= '<option value="' . $levelId . '" ' . $selected . '>' . $title . '</option>';
                    }
                    $dropdown .= '</select>';
                } else {
                    $dropdown = $currentLevel ? $currentLevel->programlevel->title : 'No level assigned';
                }

                return $dropdown;
            })
            ->addColumn('nationality', function ($row) use ($request) {
                return Str::upper($row->nationality);
            })
            ->addColumn('classStartDate', function ($row) use ($request) {
                return \Carbon\Carbon::parse(optional($row->joint_classes->first())->pivot->start_date)->diffForHumans();
            })
            ->addColumn('hasReports', function ($row) use ($request, $id, $programTitle) {
                $stReportShowRoute = route('reports.create', ['id' => $id, 'from_date' => Carbon::now()->toDateString()]);
                
                // Get report count based on program type using the new methods - passing the class ID
                $count = match(true) {
                    stripos($programTitle, 'ijazasand') !== false => 
                        $row->countUniqueIjazasanadReportDays($id),
                        
                    strpos($programTitle, 'nouranya') !== false || strpos($programTitle, 'nuraniyah') !== false => 
                        $row->countUniqueNouranyaReportDays($id),
                        
                    default => 
                        $row->countUniqueHefzReportDays($id)
                };
                
                return '<a target="_blank" href="' . $stReportShowRoute . '" class="ui teal circular">' . $count . '</a>';
            })
            ->addColumn('status', function ($row) use ($request) {
                return $row->status;
            })
            ->addColumn('addTransferDetails', function ($row) use ($request) {
                if (!is_null(optional($row->joint_classes->first())->pivot->transfer_at)) {
                    $dataTooltip = optional($row->joint_classes->first())->pivot->transfer_at;
                    $details = 'T -' . \Carbon\Carbon::parse(optional($row->joint_classes->first())->pivot->transfer_at)->diffForHumans();

                    return '<a  href="#" ><strong data-tooltip="' . $dataTooltip . '" >' . $details . '</strong></a>';
                }

                if (!is_null(optional($row->joint_classes->first())->pivot->added_at)) {
                    $dataTooltip = optional($row->joint_classes->first())->pivot->added_at;
                    $details = 'A - ' . \Carbon\Carbon::parse(optional($row->joint_classes->first())->pivot->added_at)->diffForHumans();
                    return '<a  href="#" ><strong data-tooltip="' . $dataTooltip . '" >' . $details . '</strong></a>';
                }
            })
            ->addColumn('examReady', function ($row) {
                $checked = $row->ready_for_exam == 'on' ? 'checked' : '';
                return '<div class="ui toggle checkbox">
        <input 
        data-userid="' . $row->user_id . '" 
        data-examReadiness="' . $row->ready_for_exam . '" 
         type="checkbox"  ' . $checked . ' 
         name="ready_for_exam" 
         class="readyForExamCheckbox">
        <label></label>
    </div>';
            })
            ->addColumn('action', function ($row) use ($programTitle) {
                $details = '<div class="btn-group"><button type="button" class="btn btn-primary btn-xs dropdown-toggle"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Actions <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">';

                if(isset($row->ready_for_exam)) {
                    $details .= '<li><a target="_blank" href="' . route('online-exam.student', [$row->id,$row->joint_classes->first()->id]) . '">Exam Hub</a></li>';
                }

                // Add program-specific report links
                $hasReport = match(true) {
                    stripos($programTitle, 'ijazasand') !== false => 
                        $row->completedIjazasanadReportforCurrentMonth()->exists(),
                    strpos($programTitle, 'nouranya') !== false || strpos($programTitle, 'nuraniyah') !== false => 
                        $row->completedNouranyaReportforCurrentMonth()->exists(),
                    default => 
                        $row->completedHefzReportforCurrentMonth()->exists()
                };

                if($hasReport) {
                    $completedReport = match(true) {
                        stripos($programTitle, 'ijazasand') !== false => 
                            $row->completedIjazasanadReportforCurrentMonth()->first(),
                        strpos($programTitle, 'nouranya') !== false || strpos($programTitle, 'nuraniyah') !== false => 
                            $row->completedNouranyaReportforCurrentMonth()->first(),
                        default => 
                            $row->completedHefzReportforCurrentMonth()->first()
                    };

                    $fromDate = $completedReport->created_at->toDateString();
                    $stReportShowRoute = route('reports.create', [
                        'id' => $row->joint_classes->first()->id, 
                        'from_date' => $fromDate
                    ]);

                    $reportLabel = match(true) {
                        stripos($programTitle, 'ijazasand') !== false => 'Ijazasanad Report',
                        strpos($programTitle, 'nouranya') !== false || strpos($programTitle, 'nuraniyah') !== false => 
                            'Nouranya Report',
                        default => 'Hefz Report'
                    };

                    $details .= '<li data-value="' . $reportLabel . '" ><a target="_blank" href="' . $stReportShowRoute . '">' . $reportLabel . '</a></li>';
                }

                $admissionIds = $row->admissions->pluck('id')->toArray();
                $details .= '
                    <li><a data-toggle="modal" data-target="#change_student_class" href="#" 
                        class="changeStudentClassTrgBtn" id="changeStudentClassTrgBtn" 
                        data-student_id="' . $row->id . '" 
                        data-st_fullname="' . $row->full_name . '"
                        data-admission_ids="' . implode(',', $admissionIds) . '">Change Students Class</a></li>
                    <li><a data-catid="' . $row->id . '" data-admissionid="' . implode(',', $admissionIds) . '" data-toggle="modal" data-target="#delete">
                        Delete Student</a></li>
                </ul>
            </div>';

                return $details;
            })
            ->setRowClass(function ($row) use ($programTitle) {
                $hasReport = match(true) {
                    stripos($programTitle, 'ijazasand') !== false => 
                        $row->completedIjazasanadReportforCurrentMonth()->exists(),
                    strpos($programTitle, 'nouranya') !== false || strpos($programTitle, 'nuraniyah') !== false => 
                        $row->completedNouranyaReportforCurrentMonth()->exists(),
                    default => 
                        $row->completedHefzReportforCurrentMonth()->exists()
                };

                return $hasReport ? $programTitle . '-report-row' : '';
            })
            ->rawColumns(['action', 'addTransferDetails', 'hasReports', 'examReady', 'levelDetails', 'studentName'])
            ->make(true);
    }

    /**
     * Get classes grouped by program for navigation dropdown
     *
     * @param Request $request
     * @param int|null $currentClassId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getClassesGroupedByProgram(Request $request, $currentClassId = null)
    {
        try {
            // Check user permissions
            $admin = auth()->user()->hasRole(["managing-director_2_"]);
            
            $classesQuery = Classes::with([
                'center', 
                'programs.translations',
                'translations',
                'teachers'
            ])
            ->withCount(['students as active_students_count' => function ($query) {
                $query->where('status', 'active');
            }]);

            if (!$admin) {
                // Apply center restrictions for non-admin users
                $super = Employee::whereHas('roles', function ($q) {
                    return $q->where('name', 'like', 'supervisor_%_');
                })->where('id', auth()->user()->id)->exists();

                if ($super == true) {
                    $cen_emp = \App\Cen_Emp::where('emp_id', '=', auth()->user()->id)->pluck('cen_id');
                    $classesQuery->whereIn('center_id', $cen_emp);
                } else {
                    // For teachers, show only their classes
                    $classesQuery->whereHas('teachers', function ($q) {
                        $q->where('employee_id', auth()->user()->id);
                    });
                }
            }

            $classes = $classesQuery->get();

            // Group classes by program
            $groupedClasses = [];
            
            foreach ($classes as $class) {
                foreach ($class->programs as $program) {
                    // Use translatable title, fallback to code or 'No Program'
                    $programTitle = $program->title ?? $program->code ?? 'No Program';
                    
                    if (!isset($groupedClasses[$programTitle])) {
                        $groupedClasses[$programTitle] = [
                            'program_id' => $program->id,
                            'program_title' => $programTitle,
                            'classes' => []
                        ];
                    }
                    
                    // Prepare teacher data with IDs for links
                    $teachersData = $class->teachers->map(function ($teacher) {
                        return [
                            'id' => $teacher->id,
                            'name' => $teacher->full_name
                        ];
                    })->toArray();
                    
                    $teacherNames = $class->teachers->pluck('full_name')->take(2)->implode(', ');
                    if ($class->teachers->count() > 2) {
                        $teacherNames .= ' +' . ($class->teachers->count() - 2);
                    }
                    
                    $currentDate = \Carbon\Carbon::now()->format('Y-m-d');
                    
                    $classData = [
                        'id' => $class->id,
                        'name' => $class->name,
                        'class_code' => $class->class_code,
                        'center_name' => $class->center->location ?? 'N/A',
                        'teachers' => $teacherNames,
                        'teachers_data' => $teachersData, // Include teacher IDs for links
                        'student_count' => $class->active_students_count,
                        'is_current' => $currentClassId && $currentClassId == $class->id,
                        'program_type' => strtolower($programTitle), // Add program type for frontend logic
                        'report_url' => route('reports.create', ['id' => $class->id, 'from_date' => $currentDate]),
                        'class_show_url' => route('classes.show', ['id' => $class->id]),
                        'monthly_plan_url' => route('monthly-plan.show', ['id' => $class->id, 'from_date' => $currentDate])
                    ];
                    
                    $groupedClasses[$programTitle]['classes'][] = $classData;
                }
            }

            // Sort programs and classes
            ksort($groupedClasses);
            foreach ($groupedClasses as &$programData) {
                usort($programData['classes'], function($a, $b) {
                    return strcmp($a['name'], $b['name']);
                });
            }

            return response()->json([
                'success' => true,
                'data' => array_values($groupedClasses),
                'current_class_id' => $currentClassId
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching classes: ' . $e->getMessage()
            ], 500);
        }
    }
}

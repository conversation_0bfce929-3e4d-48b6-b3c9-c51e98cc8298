<?php

namespace App;

use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Model;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\SoftDeletes;

class News extends Model
{
    use Translatable,SoftDeletes;

    public $translatedAttributes = array('content' , 'title');

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'news';

    /**
     * The database primary key value.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['category_id', 'slug', 'status' , 'featured' , 'created_at' , 'organization_id','position'];


    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
    }

    public function deleteData($id)
    {
        return static::find($id)->delete();
    }

}

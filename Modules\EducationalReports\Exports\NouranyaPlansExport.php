<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\Student;
use App\StudentNouranyaPlan;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * NouranyaPlansExport creates a single aggregated Plans sheet for Nouranya programs.
 * 
 * Purpose: Export monthly Nouranya plans across multiple classes in a single Excel sheet.
 * Data source: student_nouranya_plans table with lesson ranges and line numbers.
 * Calculations: Lesson range calculations and Talaqqi/Talqeen component displays.
 * Context: Part of program-specific export system; focuses on lesson-based progression rather than Surah/Ayat.
 * Output: Single sheet with columns for Center, Class, Student, Lesson Ranges, Line Numbers, Talaqqi/Talqeen, Status.
 */
final class NouranyaPlansExport implements WithTitle, WithEvents
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get plans data aggregated across all classes
     */
    private function getPlansData(): array
    {
        $classIds = $this->filters['classIds'];
        $month = $this->filters['month'];
        $year = $this->filters['year'];
        $studentIds = $this->filters['studentIds'] ?? [];

        $planYearMonth = sprintf('%d-%02d', $year, $month);

        // Build per-class enrollment list first, then left-join plan for the month (to include students without plans)
        $enrolled = \App\Classes::with(['students' => function($q){
                $q->select('students.id','full_name');
            }, 'center'])
            ->whereIn('id', $classIds)
            ->get();

        $plans = collect();
        foreach ($enrolled as $cls) {
            foreach ($cls->students as $student) {
                if (!empty($studentIds) && !in_array($student->id, $studentIds)) { continue; }

                $plan = StudentNouranyaPlan::where('class_id', $cls->id)
                    ->where('student_id', $student->id)
                    ->where(function ($q) use ($month, $year, $planYearMonth) {
                        $q->where('plan_year_and_month', $planYearMonth)
                          ->orWhere(function ($q2) use ($month, $year) { $q2->whereYear('start_date',$year)->whereMonth('start_date',$month); })
                          ->orWhere(function ($q3) use ($month, $year) { $q3->whereYear('created_at',$year)->whereMonth('created_at',$month); });
                    })
                    ->where('status','active')
                    ->first();

                $plans->push((object) [
                    'class_obj' => $cls,
                    'student_obj' => $student,
                    'plan' => $plan,
                ]);
            }
        }

        $results = [];
        foreach ($plans as $row) {
            $cls = $row->class_obj; $student = $row->student_obj; $plan = $row->plan;
            $centerName = $cls->center->name ?? $cls->center->location ?? 'Unknown Center';
            $className = $cls->name ?? 'Unknown Class';
            $studentName = $student->full_name ?? 'Unknown Student';

            // Build lesson range display
            $lessonRange = $plan ? $this->formatLessonRange($plan) : '—';
            
            // Build line number range
            $lineRange = $plan ? $this->formatLineRange($plan) : '—';

            // Build Talaqqi range
            $talaqqi = $plan ? $this->formatTalaqqi($plan) : '—';

            // Build Talqeen range
            $talqeen = $plan ? $this->formatTalqeen($plan) : '—';

            // Calculate total lessons planned
            $totalLessons = $plan ? $this->calculateTotalLessons($plan) : 0;

            $results[] = [
                'center_name' => $centerName,
                'class_name' => $className,
                'class_id' => $cls->id,
                'student_name' => $studentName,
                'lesson_range' => $lessonRange,
                'line_range' => $lineRange,
                'talaqqi_range' => $talaqqi,
                'talqeen_range' => $talqeen,
                'total_lessons' => $totalLessons,
                'status' => $plan ? ucfirst($plan->status ?? 'active') : 'No Plan',
                'start_date' => $plan && $plan->start_date ? $plan->start_date->format('Y-m-d') : '',
                'created_by' => $plan->created_by ?? '',
                'approved_by' => $this->resolveApprover($plan),
                'supervisor_comment' => $plan->supervisor_comment ?? ''
            ];
        }

        return $results;
    }

    private function resolveApprover($plan): string
    {
        if (!$plan || empty($plan->approved_by)) { return ''; }
        $emp = \App\Employee::find($plan->approved_by);
        if (!$emp) { return (string) $plan->approved_by; }
        // Prefer name; fallback to email if available
        return $emp->full_name ?? ($emp->email ?? (string) $plan->approved_by);
    }

    /**
     * Format lesson range for display
     */
    private function formatLessonRange($plan): string
    {
        if (!$plan->from_lesson || !$plan->to_lesson) {
            return '—';
        }

        if ($plan->from_lesson === $plan->to_lesson) {
            return "Lesson {$plan->from_lesson}";
        }

        return "Lessons {$plan->from_lesson}–{$plan->to_lesson}";
    }

    /**
     * Format line number range for display
     */
    private function formatLineRange($plan): string
    {
        if (!$plan->from_lesson_line_number && !$plan->to_lesson_line_number) {
            return '—';
        }

        $fromLine = $plan->from_lesson_line_number ?? '';
        $toLine = $plan->to_lesson_line_number ?? '';

        if ($fromLine && $toLine) {
            if ($fromLine === $toLine) {
                return "Line {$fromLine}";
            }
            return "Lines {$fromLine}–{$toLine}";
        } elseif ($fromLine) {
            return "From Line {$fromLine}";
        } elseif ($toLine) {
            return "To Line {$toLine}";
        }

        return '—';
    }

    /**
     * Format Talaqqi range for display
     */
    private function formatTalaqqi($plan): string
    {
        if (!$plan->talaqqi_from_lesson || !$plan->talaqqi_to_lesson) {
            return '—';
        }

        if ($plan->talaqqi_from_lesson === $plan->talaqqi_to_lesson) {
            return "Lesson {$plan->talaqqi_from_lesson}";
        }

        return "Lessons {$plan->talaqqi_from_lesson}–{$plan->talaqqi_to_lesson}";
    }

    /**
     * Format Talqeen range for display
     */
    private function formatTalqeen($plan): string
    {
        if (!$plan->talqeen_from_lesson || !$plan->talqeen_to_lesson) {
            return '—';
        }

        $fromLesson = $plan->talqeen_from_lesson;
        $toLesson = $plan->talqeen_to_lesson;

        // Include line number if available
        $fromLine = $plan->talqeen_from_line_number ?? null;
        $lineInfo = $fromLine ? ":{$fromLine}" : '';

        if ($fromLesson === $toLesson) {
            return "Lesson {$fromLesson}{$lineInfo}";
        }

        return "Lessons {$fromLesson}{$lineInfo}–{$toLesson}";
    }

    /**
     * Calculate total lessons planned
     */
    private function calculateTotalLessons($plan): int
    {
        if (!$plan->from_lesson || !$plan->to_lesson) {
            return 0;
        }

        return max(0, $plan->to_lesson - $plan->from_lesson + 1);
    }

    /**
     * Get table headings
     */
    private function getTableHeadings(): array
    {
        return [
            'Center',
            'Class',
            'Class ID',
            'Student',
            'Lesson Range',
            'Line Range',
            'Talaqqi Range',
            'Talqeen Range',
            'Total Lessons',
            'Status',
            'Start Date',
            'Created By',
            'Approved By',
            'Supervisor Comment'
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Nouranya Plans';
    }

    /**
     * Register events for creating the styled table
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $this->createStyledTable($event->sheet);
            },
        ];
    }

    /**
     * Create styled table with data
     */
    private function createStyledTable($sheet)
    {
        $worksheet = $sheet->getDelegate();
        
        // Get data and headings
        $plansData = $this->getPlansData();
        $headings = $this->getTableHeadings();

        // Set main title
        $monthName = $this->filters['monthName'];
        $year = $this->filters['year'];
        $classNames = collect($this->filters['classes'])->pluck('class_code')->join(', ');
        
        $title = "NOURANYA MONTHLY PLANS - {$classNames} - {$monthName} {$year}";
        $worksheet->setCellValue('A1', $title);
        $worksheet->mergeCells('A1:N1');

        // Style main title
        $worksheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 16, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '6f42c1']]
        ]);

        $currentRow = 3;

        // Set headers
        $col = 'A';
        foreach ($headings as $heading) {
            $worksheet->setCellValue($col . $currentRow, $heading);
            $col++;
        }

        // Style headers
        $worksheet->getStyle("A{$currentRow}:N{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '8b5cf6']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $dataStartRow = $currentRow + 1;
        $currentDataRow = $dataStartRow;

        // Add data rows
        foreach ($plansData as $row) {
            $col = 'A';
            foreach ($row as $value) {
                $worksheet->setCellValue($col . $currentDataRow, $value);
                $col++;
            }
            $currentDataRow++;
        }

        // Style data rows
        if (count($plansData) > 0) {
            $dataEndRow = $currentDataRow - 1;
            $worksheet->getStyle("A{$dataStartRow}:N{$dataEndRow}")->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ]);

            // Center-align specific columns
            $worksheet->getStyle("I{$dataStartRow}:I{$dataEndRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Total Lessons
            $worksheet->getStyle("J{$dataStartRow}:J{$dataEndRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Status
            $worksheet->getStyle("K{$dataStartRow}:K{$dataEndRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Start Date
        } else {
            // No data message
            $worksheet->setCellValue("A{$dataStartRow}", 'No Nouranya plans found for the specified criteria');
            $worksheet->mergeCells("A{$dataStartRow}:N{$dataStartRow}");
            $worksheet->getStyle("A{$dataStartRow}")->applyFromArray([
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'font' => ['italic' => true],
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ]
            ]);
        }

        // Auto-size columns
        foreach (range('A', 'N') as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Set specific column widths for better readability
        $worksheet->getColumnDimension('N')->setWidth(30); // Supervisor Comment
    }
}

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

/**
 * ProviderJobCategory Entity
 * 
 * Manages mappings between provider-specific job categories and canonical categories.
 * This enables dynamic category translation without hardcoded configuration.
 * 
 * @property int $id
 * @property string $provider_name
 * @property string $name
 * @property string $provider_identifier
 * @property int $canonical_category_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
final class ProviderJobCategory extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'provider_job_categories';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'provider_name',
        'name',
        'provider_identifier',
        'canonical_category_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'id' => 'integer',
        'canonical_category_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Validation rules for the model
     *
     * @var array<string, string>
     */
    public static array $validationRules = [
        'provider_name' => 'required|string|max:100',
        'name' => 'required|string|max:255',
        'provider_identifier' => 'required|string|max:255',
        'canonical_category_id' => 'required|integer|exists:job_categories,id',
        // Composite unique validation - provider_name + provider_identifier must be unique
        'provider_composite' => 'unique:provider_job_categories,provider_identifier,NULL,id,provider_name,{provider_name}',
    ];

    /**
     * Get the canonical category that this provider category maps to
     *
     * @return BelongsTo
     */
    public function canonicalCategory(): BelongsTo
    {
        return $this->belongsTo(JobCategory::class, 'canonical_category_id');
    }

    /**
     * Get the jobs associated with this provider category.
     * Uses the new job_provider_category_pivot table for the relationship.
     *
     * @return BelongsToMany
     */
    public function jobs(): BelongsToMany
    {
        return $this->belongsToMany(
            \Modules\JobSeeker\Entities\Job::class,
            'job_provider_category_pivot',
            'provider_category_id',
            'job_id'
        )->withTimestamps();
    }

    /**
     * Scope to filter by provider name
     *
     * @param Builder $query
     * @param string $providerName
     * @return Builder
     */
    public function scopeForProvider(Builder $query, string $providerName): Builder
    {
        return $query->where('provider_name', $providerName);
    }

    /**
     * Scope to filter by canonical category ID
     *
     * @param Builder $query
     * @param int $canonicalCategoryId
     * @return Builder
     */
    public function scopeForCanonicalCategory(Builder $query, int $canonicalCategoryId): Builder
    {
        return $query->where('canonical_category_id', $canonicalCategoryId);
    }

    /**
     * Get provider identifiers for specific provider categories
     *
     * @param array $providerCategoryIds
     * @param string $providerName
     * @return array
     */
    public static function getProviderIdentifiers(array $providerCategoryIds, string $providerName): array
    {
        if (empty($providerCategoryIds)) {
            return [];
        }

        try {
            $identifiers = static::whereIn('id', $providerCategoryIds)
                ->where('provider_name', $providerName)
                ->pluck('provider_identifier')
                ->toArray();

            Log::info('ProviderJobCategory: Retrieved provider identifiers', [
                'provider_category_ids' => $providerCategoryIds,
                'provider_name' => $providerName,
                'identifiers_count' => count($identifiers),
                'identifiers' => $identifiers
            ]);

            return $identifiers;
        } catch (\Exception $e) {
            Log::error('ProviderJobCategory: Error getting provider identifiers', [
                'provider_category_ids' => $providerCategoryIds,
                'provider_name' => $providerName,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get all provider categories for a specific provider
     *
     * @param string $providerName
     * @return Collection
     */
    public static function getForProvider(string $providerName): Collection
    {
        try {
            $categories = static::forProvider($providerName)
                ->with('canonicalCategory')
                ->orderBy('name')
                ->get();

            Log::debug('ProviderJobCategory: Retrieved categories for provider', [
                'provider_name' => $providerName,
                'categories_count' => $categories->count()
            ]);

            return $categories;
        } catch (\Exception $e) {
            Log::error('ProviderJobCategory: Error getting categories for provider', [
                'provider_name' => $providerName,
                'error' => $e->getMessage()
            ]);
            return new Collection();
        }
    }

    /**
     * Get provider categories mapped to specific canonical categories
     *
     * @param array $canonicalCategoryIds
     * @param string $providerName
     * @return Collection
     */
    public static function getForCanonicalCategories(array $canonicalCategoryIds, string $providerName): Collection
    {
        try {
            if (empty($canonicalCategoryIds)) {
                Log::debug('ProviderJobCategory: No canonical category IDs provided', [
                    'provider_name' => $providerName
                ]);
                return new Collection();
            }

            $categories = static::whereIn('canonical_category_id', $canonicalCategoryIds)
                ->where('provider_name', $providerName)
                ->with('canonicalCategory')
                ->get();

            Log::debug('ProviderJobCategory: Retrieved categories for canonical categories', [
                'canonical_category_ids' => $canonicalCategoryIds,
                'provider_name' => $providerName,
                'categories_count' => $categories->count()
            ]);

            return $categories;
        } catch (\Exception $e) {
            Log::error('ProviderJobCategory: Error getting categories for canonical categories', [
                'canonical_category_ids' => $canonicalCategoryIds,
                'provider_name' => $providerName,
                'error' => $e->getMessage()
            ]);
            return new Collection();
        }
    }

    /**
     * Create or update a provider category mapping
     *
     * @param string $providerName
     * @param string $categoryName
     * @param string $providerIdentifier
     * @param int $canonicalCategoryId
     * @return static
     */
    public static function createOrUpdateMapping(
        string $providerName,
        string $categoryName,
        string $providerIdentifier,
        int $canonicalCategoryId
    ): static {
        return static::updateOrCreate([
            'provider_name' => $providerName,
            'provider_identifier' => $providerIdentifier,
        ], [
            'name' => $categoryName,
            'canonical_category_id' => $canonicalCategoryId,
        ]);
    }

    /**
     * Get formatted data for Select2 dropdown
     *
     * @param string $providerName
     * @return array
     */
    public static function getForSelect2(string $providerName): array
    {
        try {
            $categories = static::forProvider($providerName)
                ->orderBy('name')
                ->get(['id', 'name']);

            $result = $categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'text' => $category->name
                ];
            })->toArray();

            Log::debug('ProviderJobCategory: Generated Select2 options', [
                'provider_name' => $providerName,
                'options_count' => count($result)
            ]);

            return $result;
        } catch (\Exception $e) {
            Log::error('ProviderJobCategory: Error generating Select2 options', [
                'provider_name' => $providerName,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
}

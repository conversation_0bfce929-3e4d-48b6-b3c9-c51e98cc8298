<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Employee;
use App\EmployeeSalary;
use App\EmployeeTimetable;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;

class EmployeeSalaryController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return view('humanresource::index');
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view('humanresource::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Response
     */
    public function show($id)
    {
        return view('humanresource::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Response
     */
    public function edit($id)
    {
        return view('humanresource::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
//    public function update(Request $request, $id)
//    {
//        $salary = $request->all();
//
//        $employee = Employee::findOrFail($request->employee_id);
//
//        if ($employee->current_salary) {
//            $current_salary = $employee->current_salary;
//            $current_salary->end_at = Carbon::createFromFormat('Y-m-d', $request->start_at)->subDay()->format('Y-m-d');
//            $current_salary->save();
//        }
//
//        $employee_salary = EmployeeSalary::create([
//            'employee_id' => $request->employee_id,
//            "start_at" => $request->start_at,
//            "work_mood" => $request->work_mood,
//            'basic_salary' => $request->basic_salary,
//            'hours_per_month' => $request->hours_per_month ?? null,
//        ]);
//
//        if ($request->timetable && ($request->flexable != true)) {
//            foreach ($request->timetable as $day => $daily) {
//                if ($daily && is_array($daily) && $daily != 'off') {
//                    $salary_timetable = EmployeeTimetable::create([
//                        'employee_salary_id' => $employee_salary->id,
//                        'day' => $day,
//                        'clockin' => $daily['clockin'],
//                        'clockout' => $daily['clockout'],
//                        'break' => $daily['break']
//                    ]);
//                }
//            }
//        }
//
//        return redirect()->back();
//    }

    public function update(Request $request)
    {
        $salary = $request->all();

        $employee = Employee::findOrFail($request->employee_id)->update(['salary' => $request->salary]);
        return response()->json([
            'message' => 'employee salary updated'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Response
     */
    public function destroy($id)
    {
        //
    }
}

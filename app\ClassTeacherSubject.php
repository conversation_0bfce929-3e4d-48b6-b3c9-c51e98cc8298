<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\ClassTeacherSubject
 *
 * @property int $id
 * @property int $class_teacher_id
 * @property int|null $subject_id
 * @property string $start_date
 * @property string|null $end_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $program_id
 * @property-read \App\ClassTeacher $teacher
 * @property-read \App\ClassSubjectTimetable|null $timetable
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTeacherSubject newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTeacherSubject newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTeacherSubject query()
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTeacherSubject whereClassTeacherId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTeacherSubject whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTeacherSubject whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTeacherSubject whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTeacherSubject whereProgramId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTeacherSubject whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTeacherSubject whereSubjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ClassTeacherSubject whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ClassTeacherSubject extends Model
{

    use SoftDeletes;
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'class_teacher_subjects';
    protected $fillable = ['class_teacher_id','subject_id','start_date','end_date','program_id','created_at','updated_at'];

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    public function teacher()
    {
        return $this->belongsTo('App\ClassTeacher', 'class_employee_id' , 'id');
    }

    public function timetable()
    {
        return $this->hasOne('App\ClassSubjectTimetable' , 'class_teacher_subject_id')->where('end_at' , null);
    }

    
}

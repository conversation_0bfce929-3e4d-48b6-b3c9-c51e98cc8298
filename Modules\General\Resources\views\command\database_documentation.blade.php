@extends('backEnd.master')
@section('title') 
    @lang('reports.database_documentation')
@endsection

@section('mainContent')
<section class="sms-breadcrumb mb-40 white-box">
    <div class="container-fluid">
        <div class="row justify-content-between">
            <h1>@lang('reports.database_documentation')</h1>
            <div class="bc-pages">
                <a href="{{route('dashboard')}}">@lang('common.dashboard')</a>
                <a href="#">@lang('reports.reports')</a>
                <a href="#">@lang('reports.database_documentation')</a>
            </div>
        </div>
    </div>
</section>

<section class="admin-visitor-area">
    <div class="container-fluid p-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="white-box">
                    <div class="row pb-30">
                        <div class="col-lg-12 text-right">
                            <a href="{{ route('general.commands.home', ['active_tab' => 'dbDocs']) }}" class="primary-btn small fix-gr-bg">
                                <span class="ti-arrow-left pr-2"></span>
                                @lang('common.back')
                            </a>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-3 nav-section">
                            <div class="form-group">
                                <input type="text" id="searchInput" class="form-control" placeholder="@lang('common.search')">
                            </div>
                            <ul class="table-index" id="tableIndex">
                                @foreach($tableLinks as $tableLink)
                                    {!! $tableLink !!}
                                @endforeach
                            </ul>
                        </div>
                        
                        <div class="col-lg-9 content-section">
                            <div id="contentArea">
                                {!! $contentHtml !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section('script')
<script>
    $(document).ready(function() {
        // Search functionality
        $('#searchInput').on('keyup', function() {
            let value = $(this).val().toLowerCase();
            $('#tableIndex li').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });
        
        // Jump to table when clicking links
        $('.table-link').on('click', function(e) {
            e.preventDefault();
            const target = $(this).attr('href');
            const targetElement = $(target);
            
            if (targetElement.length) {
                $('html, body').animate({
                    scrollTop: targetElement.offset().top - 100
                }, 500);
            }
        });
    });
</script>
@endsection
@endsection 
@extends('layouts.hound')

@section('mytitle', 'Activity Details')

@section('content')
<div class="panel panel-default card-view">
    <div class="panel-heading"><h4>Activity {{ $activity->id }}</h4></div>
    <div class="panel-body">

        <a href="{{ url('/workplace/activities') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
        <a href="{{ url('/workplace/activities/' . $activity->id . '/edit') }}" title="Edit Activity"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>
        {!! Form::open([
            'method'=>'DELETE',
            'url' => ['workplace/activities', $activity->id],
            'style' => 'display:inline'
        ]) !!}
            {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                    'type' => 'submit',
                    'class' => 'btn btn-danger btn-xs',
                    'title' => 'Delete Activity',
                    'onclick'=>'return confirm("Confirm delete?")'
            ))!!}
        {!! Form::close() !!}
        <br/>
        <br/>

        <div class="table-responsive">
            <table class="table table-borderless">
                <tbody>
                    <tr>
                        <th>ID</th><td>{{ $activity->id }}</td>
                    </tr>
                    <tr><th> Organization Id </th><td> {{ $activity->organization_id }} </td></tr><tr><th> Activity Time </th><td> {{ $activity->activity_time }} </td></tr><tr><th> Duration </th><td> {{ $activity->duration }} </td></tr>
                </tbody>
            </table>
        </div>

    </div>
</div>
@endsection

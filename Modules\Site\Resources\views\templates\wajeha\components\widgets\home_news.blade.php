<!-- News -->
<div class="clearfix" id="home_news">
    <h2>{{ $home_news_block_title }}</span></h2>

    <div class="col-md-4">
    <!-- news item -->
    @if($home_news_special_data['featured'])
    <div class="inews-item">
        <a class="" href="{{ url(config('app.locale').'/news/'.$home_news_special_data['featured']->slug) }}">
            <img class="img-responsive" src="{{ url($home_news_special_data['featured']->image ?? '-')}}" alt="image">
        </a>
        
        <div class="inews-item-content" style="padding:0">

            {{-- <div class="inews-date-wrapper">
                <span class="inews-date-day" style="font-size:22px">{{ $home_news_special_data['featured']->created_at->format('d') }}</span>
                <span class="inews-date-month">{{ $home_news_special_data['featured']->created_at->format('F') }}</span>
                <span class="inews-date-year"  style="font-size:22px">{{ $home_news_special_data['featured']->created_at->format('Y') }}</span>
            </div> --}}

            <div class="inews-content-inner">

                <h3 class="size-20"><a href="{{ url(config('app.locale').'/news/'.$home_news_special_data['featured']->slug) }}">{{ $home_news_special_data['featured']->title }}</a></h3>
                <!-- <ul class="blog-post-info list-inline noborder margin-bottom-20 nopadding">
                    <li>
                        <a href="page-profile.html">
                            <i class="fa fa-user"></i> 
                            <span class="font-lato">By John Doe</span>
                        </a>
                    </li>
                    <li>
                        <i class="fa fa-folder-open-o"></i> 

                        <a class="category" href="#">
                            <span class="font-lato">Design</span>
                        </a>
                        <a class="category" href="#">
                            <span class="font-lato">Photography</span>
                        </a>
                    </li>
                </ul> -->

                <p>{{ str_limit(strip_tags($home_news_special_data['featured']->content) ,150 , ' ...' ) }}</p>
            </div>
            

        </div>
    </div>
    @endif
    <!-- /news item -->
    </div>
    <div class="col-md-8">
    @foreach($home_news_special_data['news'] as $news)
    <!-- news item -->
    <div class="inews-item col-md-6" style="display:flex">
        <a class="inews-thumbnail" style="width:40%" href="{{ url(config('app.locale').'/news/'.$news->slug) }}">
            <img class="img-responsive " src="{{ url($news->image ?? '-')}}" alt="image">
        </a>
        
        <div class="inews-item-content"  style="width:60% ; padding:0">

            {{-- <div class="inews-date-wrapper">
                <span class="inews-date-day" style="font-size:22px">{{ $news->created_at->format('d') }}</span>
                <span class="inews-date-month">{{ $news->created_at->format('F') }}</span>
                <span class="inews-date-year"  style="font-size:22px">{{ $news->created_at->format('Y') }}</span>
            </div> --}}

            <div class="inews-content-inner">

                <h3 class="size-20"><a href="{{ url(config('app.locale').'/news/'.$news->slug) }}">{{ $news->title }}</a></h3>
                <!-- <ul class="blog-post-info list-inline noborder margin-bottom-20 nopadding">
                    <li>
                        <a href="page-profile.html">
                            <i class="fa fa-user"></i> 
                            <span class="font-lato">By John Doe</span>
                        </a>
                    </li>
                    <li>
                        <i class="fa fa-folder-open-o"></i> 

                        <a class="category" href="#">
                            <span class="font-lato">Design</span>
                        </a>
                        <a class="category" href="#">
                            <span class="font-lato">Photography</span>
                        </a>
                    </li>
                </ul> -->

                <p>{{ str_limit(strip_tags($news->content) ,50 , ' ...' ) }}</p>
            </div>
            

        </div>
    </div>
    <!-- /news item -->
    @endforeach
    </div>
</div>
<!-- /News -->
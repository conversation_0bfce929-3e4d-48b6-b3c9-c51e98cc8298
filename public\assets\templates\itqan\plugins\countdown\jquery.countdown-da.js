﻿/* http://keith-wood.name/countdown.html
   Danish initialisation for the jQuery countdown extension
   Written by B<PERSON> (<EMAIL>). */
(function($) {
	$.countdown.regionalOptions['da'] = {
		labels: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
		labels1: ['<PERSON><PERSON>', '<PERSON>å<PERSON>', 'U<PERSON>', 'Dag', 'Time', 'Minut', 'Sekund'],
		compactLabels: ['Å', 'M', 'U', 'D'],
		whichLabels: null,
		digits: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
		timeSeparator: ':', isRTL: false};
	$.countdown.setDefaults($.countdown.regionalOptions['da']);
})(jQuery);

<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class User extends JsonResource
{


    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
//        return parent::toArray($request);

        return [
            'email' => $this->email,
            'username' => $this->username,
             'phone' => $this->phone,
            'student_identity_number' => optional($this->student)->identity_number,
            'student' => $this->when($request->include == 'hashmat',$this->student), // conditionally loading API relationships

            'role'=> $this->whenPivotLoaded('role_user', function () {
        return $this->pivot->expires_at;
    }),
//            'student' => $this->student,
//            'password' => $this->password,
//            'role_id' => $this->role_id,
//            'full_name' => $this->full_name,
//            'organization_id' => $this->organization_id,
//            'is_administrator' => $this->is_administrator,
//            'display_name' => $this->display_name,
//            'full_name_trans' => $this->full_name_trans,
//            'access_status' => $this->access_status,
//            'nationality' => $this->nationality,
//            'address_1' => $this->address_1,
//            'address_2' => $this->address_2,
//            'state' => $this->state,
//            'zip_code' => $this->zip_code,
//            'gender' => $this->gender,
//            'email_verified_at' => $this->email_verified_at,
//            'students' => StudentResource::collection($this->whenLoaded('student')),

        ];
    }


    //Get any additional data that should be returned with the resource array.

    public function with($request)
    {
        return [
            'foo' => 'bar',
            'status' => 'success',
            ];

        return parent::with($request); // TODO: Change the autogenerated stub
    }

    public function withResponse($request, $response)
    {
        $response->header('X-Vlue','True');
        parent::withResponse($request, $response); // TODO: Change the autogenerated stub
    }
}

<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;


class PublicHoliday extends Model
{
//    protected static function boot()
//    {
//        parent::boot();
//        static::addGlobalScope(new OrganizationScope);
//    }


    protected $table = 'public_holidays';

    protected $fillable = [
        'month_name',
        'month__no',
        'day',
        'year',
        'organization_id',
        'holiday'
    ];

}

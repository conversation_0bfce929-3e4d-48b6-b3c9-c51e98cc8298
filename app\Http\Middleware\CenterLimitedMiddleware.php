<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CenterLimitedMiddleware
{
    /**
     * Handle an incoming request.
     *
     * This ensures that if the user is an external_collaborator,
     * we apply center-limited logic.
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();

        if ($user && $user->hasRole('external_collaborator')
            && in_array($request->method(), ['POST','PUT','PATCH','DELETE'])) {
            abort(403, 'Read-only access for external collaborators.');
        }


        if (!$user) {
            // Not logged in
            return redirect()->route('login')->withErrors('Please login first.');
        }

        // Check role
        if ($user->hasRole('external_collaborator')) {

            // 1) Enforce read-only for external_collaborators
            if (in_array($request->method(), ['POST','PUT','PATCH','DELETE'])) {
                abort(403, 'External collaborators have read-only access.');
            }

            // Possibly check disclaimers acceptance
            if (!$user->hasAcceptedDisclaimers()) {
                // Force disclaimers acceptance
                return redirect()->route('disclaimers.accept');
            }

            // Possibly check forced password reset
            if ($user->needsPasswordReset()) {
                return redirect()->route('password.reset.forced');
            }

            // Now the user is restricted to certain centers
            // We can store an array of center IDs in the session if needed
            $centerIds = $user->allowedCenters()->pluck('centers.id')->toArray();

            // If the request references a specific center_id,
            // e.g. from query or route param, ensure it's in centerIds
            $requestedCenter = $request->query('center_id')
                ?? $request->route('center_id')
                ?? null;


        }

        return $next($request);
    }
}

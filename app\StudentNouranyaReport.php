<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Matrix\Builder;

class StudentNouranyaReport extends Model
{

    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'student_nouranya_report';

    /**
     * The fillable columns
     */
    protected $fillable = [
      'created_at',
      'updated_at',
      'class_id',
      'organization_id',
      'student_id',
      'from_lesson',
      'to_lesson',
      'level_id',
      'nouranya_evaluation_id',
      'nouranya_evaluation_note',
        'attendance_id',
        'nouranya_plan_id',
        'program_id',
        'teacher_id',
        'delete_reason',
        'talaqqi_from_lesson',
        'talqeen_from_lesson',
        'talaqqi_to_lesson',
        'talqeen_to_lesson',
        'deleted_at',
        'from_lesson_line_number',
        'to_lesson_line_number',
        'plan_year_and_month',

    ];
    // Define a local scope in the StudentHefzReport model
    public function scopeDistinctMonthsYears($query) {
        return $query->selectRaw('DISTINCT MONTHNAME(created_at) as month, YEAR(created_at) as year')
            ->orderBy('created_at', 'desc');
    }

    public function scopeCurrentMonthAndYear($query)
    {
        $currentMonth = now()->month;
        $currentYear = now()->year;

        $query->where(function($q) use ($currentMonth, $currentYear) {
            $q->whereYear('created_at', $currentYear)
                ->whereMonth('created_at', $currentMonth)

            ;
        });
    }

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';
    protected $casts = ['start_date' => 'date','created_at'];
//    public $timestamps = false;





    public function toSurat(){


        return $this->belongsTo(MoshafSurah::class,'hefz_to_surat','id');
    }

    public function result()
    {
        return $this->hasOne('App\EvaluationSchemaOption', 'id', 'hefz_evaluation_id');
    }

    public function student()
    {
        return $this->belongsTo('App\Student');
//        return $this->belongsTo(Student::class,'student_id','id');
    }
    public function attendanceOptions()
    {
        return $this->belongsTo(AttendanceOption::class,'attendance_id','id');
    }




    public function classes()
    {

        return $this->belongsTo(Classes::class,'class_id','id');
    }
    public function scopeAfter(Builder $query, string $date): Builder{

        return $query->where($this->qualifyColumn('created_at'),'>=',$date.' 00:00:00');

        // example query could be StudentHefzReport::query()->after('2022-02-14')->before('2022-02-21')->get()
    }
    public function scopeBefore(Builder $query, string $date): Builder{

        return $query->where($this->qualifyColumn('created_at'),'<=',$date.' 23:59:59');

        // example query could be StudentHefzReport::query()->after('2022-02-14')->before('2022-02-21')->get()
    }


    public function nouranyaPlan(){

        return $this->belongsTo(StudentNouranyaPlan::class,'nouranya_plan_id','id');
    }



}

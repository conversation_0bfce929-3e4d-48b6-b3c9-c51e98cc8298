<?php

namespace Modules\HumanResource\Http\Requests;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class dailyAddClockOutRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {




        if ((auth()->user()->hasAnyRole(['supervisor_' . config('organization_id') . '_','education-manager_' . config('organization_id') . '_','human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {

            return [
                'in' => 'required|date_format:Y-m-d H:i:s',
                'out' => 'required|date_format:Y-m-d H:i:s|after_or_equal:in',
            'employee_id' => 'required',
//            'outNote' => 'required|min:12'


        ];
    }

        else{
            return [
                'date' => 'required',
                'in' => 'required|date_format:Y-m-d H:i:s',
                'out' => 'required|date_format:Y-m-d H:i:s|after_or_equal:in',
                'employee_id' => 'required',
                'outNote' => 'required|min:12'


            ];

        }
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            'outNote.required' => 'Please provide a valid reason for your missed Clock-Out.',
            'out.required' => 'Please provide the missed clock-out time.',
            'out.after' => 'The clock-out time must be later than the clock-in time.',
            'in.required' => 'Please provide the clock-in time.',
            'in.date_format' => 'The clock-in time must be in the format YYYY-MM-DD HH:MM:SS.',
            'out.date_format' => 'The clock-out time must be in the format YYYY-MM-DD HH:MM:SS.',
            'employee_id.required' => 'Please specify the employee ID.',
            'date.required' => 'Please provide the date of the attendance.',
        ];
    }


    protected function prepareForValidation()
    {
        $inTime = Carbon::parse($this->in);
        $outTime = Carbon::parse($this->out);

        // If `out` does not have seconds, add them from `in` for consistency
        if ($outTime->second === 0) {
            $outTime->setSecond($inTime->second);
        }

        $this->merge([
            'in' => $inTime->format('Y-m-d H:i:s'),
            'out' => $outTime->format('Y-m-d H:i:s'),
        ]);


    }






}

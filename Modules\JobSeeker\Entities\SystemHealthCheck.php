<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

/**
 * System Health Check Entity
 * 
 * Stores current health status and metrics for system components
 */
final class SystemHealthCheck extends Model
{
    use HasFactory;

    protected $table = 'system_health_checks';

    protected $fillable = [
        'check_name',
        'check_type',
        'status',
        'message',
        'metrics',
        'execution_time_ms',
        'last_healthy_at',
        'consecutive_failures',
        'recovery_attempted',
        'recovery_successful',
        'next_check_at',
    ];

    protected $casts = [
        'metrics' => 'array',
        'last_healthy_at' => 'datetime',
        'next_check_at' => 'datetime',
        'recovery_attempted' => 'boolean',
        'recovery_successful' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Check types
     */
    public const TYPE_CRITICAL = 'critical';
    public const TYPE_IMPORTANT = 'important';
    public const TYPE_INFORMATIONAL = 'informational';

    /**
     * Health statuses
     */
    public const STATUS_HEALTHY = 'healthy';
    public const STATUS_WARNING = 'warning';
    public const STATUS_CRITICAL = 'critical';
    public const STATUS_UNKNOWN = 'unknown';

    /**
     * Get health history for this check
     */
    public function history()
    {
        return $this->hasMany(SystemHealthHistory::class, 'check_name', 'check_name');
    }

    /**
     * Check if this health check is overdue
     */
    public function isOverdue(): bool
    {
        return $this->next_check_at && $this->next_check_at->isPast();
    }

    /**
     * Check if this health check is healthy
     */
    public function isHealthy(): bool
    {
        return $this->status === self::STATUS_HEALTHY;
    }

    /**
     * Check if this health check is critical
     */
    public function isCritical(): bool
    {
        return $this->status === self::STATUS_CRITICAL;
    }

    /**
     * Check if this health check needs attention
     */
    public function needsAttention(): bool
    {
        return in_array($this->status, [self::STATUS_WARNING, self::STATUS_CRITICAL]);
    }

    /**
     * Update health status
     */
    public function updateStatus(
        string $status,
        string $message = null,
        array $metrics = null,
        int $executionTimeMs = null
    ): bool {
        $wasHealthy = $this->isHealthy();
        
        $this->status = $status;
        $this->message = $message;
        $this->metrics = $metrics;
        $this->execution_time_ms = $executionTimeMs;
        
        // Update consecutive failures
        if ($status === self::STATUS_HEALTHY) {
            $this->consecutive_failures = 0;
            $this->last_healthy_at = now();
        } else {
            $this->consecutive_failures++;
        }
        
        // Schedule next check
        $this->scheduleNextCheck();
        
        $saved = $this->save();
        
        // Record in history
        if ($saved) {
            $this->recordHistory();
        }
        
        return $saved;
    }

    /**
     * Schedule next check based on status and type
     */
    public function scheduleNextCheck(): void
    {
        $minutes = $this->getCheckInterval();
        $this->next_check_at = now()->addMinutes($minutes);
    }

    /**
     * Get check interval based on status and type
     */
    private function getCheckInterval(): int
    {
        // More frequent checks for critical issues
        if ($this->status === self::STATUS_CRITICAL) {
            return $this->check_type === self::TYPE_CRITICAL ? 5 : 10;
        }
        
        if ($this->status === self::STATUS_WARNING) {
            return $this->check_type === self::TYPE_CRITICAL ? 10 : 15;
        }
        
        // Normal intervals for healthy checks
        return match ($this->check_type) {
            self::TYPE_CRITICAL => 15,
            self::TYPE_IMPORTANT => 30,
            self::TYPE_INFORMATIONAL => 60,
            default => 30,
        };
    }

    /**
     * Record this check in history
     */
    private function recordHistory(): void
    {
        SystemHealthHistory::create([
            'check_name' => $this->check_name,
            'status' => $this->status,
            'message' => $this->message,
            'metrics' => $this->metrics,
            'execution_time_ms' => $this->execution_time_ms,
            'checked_at' => now(),
        ]);
    }

    /**
     * Mark recovery attempt
     */
    public function markRecoveryAttempted(bool $successful = null): bool
    {
        $this->recovery_attempted = true;
        $this->recovery_successful = $successful;
        return $this->save();
    }

    /**
     * Get status color for UI display
     */
    public function getStatusColor(): string
    {
        return match ($this->status) {
            self::STATUS_HEALTHY => '#28a745',
            self::STATUS_WARNING => '#ffc107',
            self::STATUS_CRITICAL => '#dc3545',
            self::STATUS_UNKNOWN => '#6c757d',
            default => '#6c757d',
        };
    }

    /**
     * Get status icon for UI display
     */
    public function getStatusIcon(): string
    {
        return match ($this->status) {
            self::STATUS_HEALTHY => 'fas fa-check-circle',
            self::STATUS_WARNING => 'fas fa-exclamation-triangle',
            self::STATUS_CRITICAL => 'fas fa-times-circle',
            self::STATUS_UNKNOWN => 'fas fa-question-circle',
            default => 'fas fa-question-circle',
        };
    }

    /**
     * Scope for checks that need to run
     */
    public function scopeDue($query)
    {
        return $query->where('next_check_at', '<=', now());
    }

    /**
     * Scope for critical checks
     */
    public function scopeCritical($query)
    {
        return $query->where('check_type', self::TYPE_CRITICAL);
    }

    /**
     * Scope for unhealthy checks
     */
    public function scopeUnhealthy($query)
    {
        return $query->whereIn('status', [self::STATUS_WARNING, self::STATUS_CRITICAL]);
    }

    /**
     * Scope for checks with consecutive failures
     */
    public function scopeWithFailures($query, int $minFailures = 1)
    {
        return $query->where('consecutive_failures', '>=', $minFailures);
    }

    /**
     * Get system health summary
     */
    public static function getHealthSummary(): array
    {
        $checks = static::all();
        
        return [
            'total_checks' => $checks->count(),
            'healthy' => $checks->where('status', self::STATUS_HEALTHY)->count(),
            'warning' => $checks->where('status', self::STATUS_WARNING)->count(),
            'critical' => $checks->where('status', self::STATUS_CRITICAL)->count(),
            'unknown' => $checks->where('status', self::STATUS_UNKNOWN)->count(),
            'overdue' => $checks->filter->isOverdue()->count(),
            'critical_issues' => $checks->where('check_type', self::TYPE_CRITICAL)
                                      ->whereIn('status', [self::STATUS_WARNING, self::STATUS_CRITICAL])
                                      ->count(),
        ];
    }
}

<?php

namespace App\Traits\Spatie\Activitylog;

trait LogsActivity
{
    protected static function bootLogsActivity()
    {
        // This is a placeholder implementation
    }

    public function getActivitylogOptions(): array
    {
        return [
            'log_name' => 'default',
            'log_attributes' => [],
            'log_only_dirty' => true,
        ];
    }

    public function getDescriptionForEvent(string $eventName): string
    {
        return $eventName;
    }
}
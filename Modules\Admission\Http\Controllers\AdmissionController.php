<?php

namespace Modules\Admission\Http\Controllers;

use App\BaseSetup;
use App\Center;
use App\Classes;
use App\EmailSetting;
use App\Employee;
use App\GeneralSettings;
use App\Http\Requests\StudentApproveRequest;
use App\Services\EmailService;
use Illuminate\Support\Facades\Log;
use App\Organization;
use App\Program;
use App\ProgramTranslation;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Admission;
use App\AdmissionInterview;
use App\AdmissionInterviewer;
use App\Student;
use App\StudentHefzPlan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;
use Modules\Admission\Notifications\DependantStudentAccepted;
use App\Role;
use App\YearCheck;
use App\Services\StudentLevelService;


class AdmissionController extends Controller
{
    protected $studentLevelService;
    protected $emailService;
    
    public function __construct(StudentLevelService $studentLevelService)
    {
        $this->middleware('auth');
        $this->studentLevelService = $studentLevelService;
        $this->emailService = app(\App\Services\EmailService::class);
    }

    public function createHefzPlan(Request $request, $redirect = false)
    {
        $this->validate($request, [
            "hefz.study_direction" => "required",
            "hefz.start_from_surat" => "required",
            "hefz.start_from_ayat" => "required",
            "hefz.num_to_memorize" => "required",
            "hefz.memorization_mood" => "required",
            "hefz.pages_to_revise" => "required",
        ]);

        $plan = new StudentHefzPlan();

        $plan->study_direction = $request->hefz['study_direction'];
        $plan->start_from_surat = $request->hefz['start_from_surat'];
        $plan->start_from_ayat = $request->hefz['start_from_ayat'];
        $plan->num_to_memorize = $request->hefz['num_to_memorize'];
        $plan->memorization_mood = $request->hefz['memorization_mood'];
        $plan->pages_to_revise = $request->hefz['pages_to_revise'];
        $plan->student_id = $request->student_id;
        $plan->organization_id = config('organization_id');
        $plan->created_by = auth()->user()->id;
        if (isset($request->hefz['start_date'])) {
            $plan->start_date = $request->hefz['start_date'];
            $plan->status = 'waiting_for_approval';
        } else {
            $plan->status = 'waiting_for_approval';
        }

        $plan->save();

        if ($redirect) {
            Session::flash('flash_message', 'Student Study Plan was Added <Successfuly></Successfuly>!');
        }
        return redirect()->back();

    }
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return view('admission::index');
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view('admission::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function reRegister(Request $request)
    {
        try {
            DB::beginTransaction();
            //   return $request->all();
            $this->validate($request, [
                'center_id' => 'required',
                'class_id' => 'required',
                'student_id' => 'required',
                'program_id' => 'required'
            ]);

            $student = Student::withTrashed()->find($request->student_id);
            $student->delete_notice = NULL;
            $student->deleted_at = NULL;
            $student->status = 'new_admission';
            $student->delete_reason = NULL;
            $student->save();

            $student->joint_classes()->attach($request->class_id,['start_date' => date('Y-m-d')]);


            // assign class to the student


            $admission = new Admission();
            $admission->program_id = $request->program_id;
            $admission->center_id = $request->center_id;
            $admission->class_id = $request->class_id;
            $admission->gender_id = $request->gender;
            $admission->creator_role = Auth::user()->roles->pluck('name')->toArray()[0];
            $admission->created_by = Auth::user()->id;
            $admission->status = 'new_admission';
            $admission->student_id = $request->student_id;
            $admission->student_email = $student->email;
            $admission->student_mobile = $student->mobile;
            $admission->date_of_birth = $student->date_of_birth;
            $admission->start_date = date('Y-m-d');
            $admission->age = $student->date_of_birth->age;
            $admission->organization_id = config('organization_id');
            $admission->save();
            $admission->programs()->attach($request->program_id,['created_at' => Carbon::now()]);

//            $admission->programs()->sync([$request->program_id]);

            DB::commit();
            Toastr::success('Operation successful', 'Success');

            return redirect()->back();

        } catch (\Exception $e) {

            DB::rollback();

            Toastr::warning($e->getMessage(), 'Danger');
            return redirect()->back();
        }


    }


    /**
     * Show the specified resource.
     * @return Response
     */
    public function show()
    {
        return view('admission::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit()
    {
        return view('admission::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @return Response
     */
    public function update(Request $request)
    {



        try {
            DB::beginTransaction();


            $admission = Admission::findOrFail($request->admission_id);


            //   return $request->all();
            $this->validate($request, [
                'center_id' => 'required',
                'class_id' => 'required',
                'admission_status' => 'required',
                'program_id' => 'required'
            ]);

            $student = $admission->student;


            // temporaray solution until we find out why there is no user is created
            if (is_null($admission->student->user)) {

                $user = User::create([
                    'email' => $student->email,
                    'display_name' => $student->full_name,
                    'full_name_trans' => $student->full_name_trans,
                    'username' => $student->id,
                    'full_name' => $student->full_name,
                    'nationality' => $student->nationality,
                    'access_status' => '0',
                    'is_administrator' => 'no',
                    'organization_id' => config('organization_id'),
                    'password' => bcrypt($student->password),
                ]);

                $this->assignDefaultRoles($user, 'member');
                $student->user_id = $user->id;


            } else {
                $user = $admission->student->user;

            }

            $admission = Admission::findOrFail($request->admission_id);

            // the reason we are reinserting the program values here is that that the education department may change the program a student has applied for
            $admission->center_id = $request->center_id;
            $admission->class_id = $request->class_id;
            $admission->status = $request->admission_status;
            $student->status = $request->admission_status;
            $admission->update();
            $student->save();
            if($request->has('class_id'))
            {
                $student->joint_classes()->attach($request->class_id,['start_date' =>date('Y-m-d')]);

            }
            $admission->programs()->sync([$request->program_id]);
            $admission = tap($admission, function ($admission) use ($request) {
                if ($request->admission_status == 'offered') {
                    $admission->update(
                        [
                            'offer_letter_issuance_date' => Carbon::now()->toDateString()
                        ]);
                }


            });
            $userInfo[0]["student_id"] = $admission->student_id;
            $userInfo[0]["student_email"] = $admission->student_email;
            $userInfo[0]["channel"] = "email";
            $userInfo[0]["studentName"] = Student::find($admission->student_id)->full_name;
            $userInfo[0]["programTitle"] = Program::find($admission->program_id)->title;
            if ($request->admission_status == 'accepted') {


                // update AdmissionInterview status
                AdmissionInterview::where('admission_id', $admission->id)->update(
                    [
                        'status' => 'interviewed',
                        'confirmed_at' => Carbon::now(),
                        'updated_by' => auth()->user()->id

                    ]);
                // assign this role after the payment is made
                $user->assignRole('student');

                //
                $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);

                $systemEmail = EmailSetting::find(1);

                $system_email = $systemEmail->from_email;
                $organization_name = $systemSetting->organization_name;

                $sender['system_email'] = $system_email;
                $sender['organization_name'] = $organization_name;

                if ($admission->creator_role == 'parent') {
                    $userInfo[0]["guardian_email"] = $admission->guardian_email;
                }



                // assign a password to a student who has a guardian and send related notification
                if ($student->guardian_id) {

                    $userInfo[0]["username"] = User::find($student->user_id)->username;
                    $userInfo[0]["password"] = trim($student->student_number);
                    $user->password = bcrypt($student->student_number);
                    $emailHtmlContent = view('modules.site.templates.wajeha.backEnd.humanResource.new_employee_welcome_message', ['data' => $userInfo[0]])->render();
                   
                    $this->emailService->sendEmail(
                        ['email' => $admission->student_email, 'name' => $admission->student->full_name],
                        'You are accepted to the ' . $userInfo[0]["programTitle"] . ' Program',
                        'modules.site.templates.wajeha.backEnd.studentInformation.dependant_student_acceptance_confirmation', // Updated view path
                        ['data' => $userInfo[0], 'programTitle' => $userInfo[0]["programTitle"]] // Passing necessary data to the view
                    );

//                    $response = $this->mailjetService->sendEmail(
//                        $admission->student_email,
//                        $admission->$student->full_name,
//                        'You are accepted to the ' . $userInfo[0]["programTitle"] . ' Program',
//                        $emailHtmlContent
//                    );


//                    $user->notify(new DependantStudentAccepted($userInfo, $sender));
                    Toastr::success('Email successfully sent', 'Success');


                } else {


                    $userInfo[0]["view"] = view('modules.site.templates.wajeha.backEnd.studentInformation.student_acceptance_confirmation',['data'=> $userInfo[0]])->render();
                    $userInfo[0]["subject"] =  'You are accepted to the '.$userInfo[0]['programTitle']. ' Program';
                    try {
                        // TODO: this email-sending feature should be implemented in a separate class, not in the controller

                        $to = ['email' => $user->email, 'name' => $user->full_name];
                        $viewPath = 'modules.site.templates.wajeha.backEnd.studentInformation.student_acceptance_confirmation';
                        $viewData = ['data'=> $userInfo[0]];
                        $this->emailService->sendEmail($to, $userInfo[0]["subject"], $viewPath, $viewData);

//                                            $user->notify(new StudentAccepted($userInfo, $sender));


//                        Mail::to('<EMAIL>')->send(new \App\Mail\StudentAccepted($userInfo, $sender));
                        // Success message

                    } catch (\Exception $e) {


                        // Log the error
                        Log::error('Email failed to send. Error: ' . $e->getMessage());
                        // Error message
                        return redirect()->back()->withErrors(['error' => 'Email failed to send. Please try again later.']);
                    }


//                    Mail::to('<EMAIL>')->send(new \App\Mail\StudentAccepted($student));

//                    $user->notify(new StudentAccepted($userInfo, $sender));
                    Toastr::success('Email successfully sent', 'Success');


                }


            }
            $user->save();
            DB::commit();

            if ($request->admission_status == 'offered') {



                // offer letter arrangement starts here
                $offerLetterRawContent = $admission->programs()->pluck('offer')[0];
                $offerLetterRawContent = str_replace("[date]", $admission->offer_letter_issuance_date, $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[address]", 'Dummy Address input by Hashmat', $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[studentName]", Student::find($admission->student_id)->full_name, $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[programName]", "<i>" . Program::find($request->program_id)->title . "</i>", $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[registrationFees]", ' < strong>100 </strong > ', $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[monthlyFeesAmount]", '<strong > 200</strong > ', $offerLetterRawContent);


                $pdf = app()->make('dompdf.wrapper');
                $pdf->loadHTML(htmlspecialchars_decode($offerLetterRawContent));
                $content = $pdf->download()->getOriginalContent();
                //save the pdf offer letter in storage folder
                \Storage::put("offerLetters/" . $admission->student_id . "-offerLetter.pdf", $content);

//            we are going to attach the offer letter in the Job by making using of the student_is as it is used as the filename in the storage/app/offerLetters directory
                $userInfo[0]["view"] = view('modules.site.templates.wajeha.backEnd.studentInformation.student_offer_letter', ['data'=> $userInfo[0]])->render();
                $userInfo[0]["subject"] =  'Offer Letter '.$userInfo[0]['programTitle'].' program';
                $userInfo[0]["Filename"] =  $userInfo[0]['student_id'].'-offerLetter.pdf';
                $userInfo[0]["filePath"] =  storage_path("app/offerLetters/".$userInfo[0]['student_id'].'-offerLetter.pdf');
                $userInfo[0]["attachment"] =  true;


                $to = ['email' => $user->email, 'name' => $user->full_name];
                $viewPath = 'modules.site.templates.wajeha.backEnd.studentInformation.student_offer_letter';
                $viewData = ['data' => $userInfo[0]];
                $attachments = [
                    [
                        'path' => $userInfo[0]["filePath"],
                        'name' => $userInfo[0]["Filename"]
                    ]
                ];
                $this->emailService->sendEmail($to, $userInfo[0]["subject"], $viewPath, $viewData, $attachments);




// TODO: enable this when the mail laravel mail functionality starts working
//                $user->notify(new StudentOfffered($userInfo, $sender));


                Toastr::success('Email successfully sent', 'Success');

            }
        } catch (\Exception $e) {

            DB::rollback();

            Toastr::warning($e->getMessage(), 'Danger');
            return redirect()->back();
        }


        return redirect()->back();
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @return Response
     */
    public function approve(StudentApproveRequest $request)
    {


        try {

            DB::beginTransaction();



        $admission = Admission::findOrFail($request->admission_id);
        $student = $admission->student;
        $user = $admission->student->user;
        $interviewTime = $request->get('interview_time');
        $hoursToInterview = $this->calculateHoursToInterview($interviewTime);



            // return $admission->programs;
        $waiting_for_interview = false;
        $approved_programs = 0;
        // get admission programs
        foreach ($admission->programs as $program) {
            // check if the admission aproved
            if ($request->interview[$program->id]['approve']) {
                $approved_programs++;
                // check if an interview is required
                if ($program->require_interview) {
                    $waiting_for_interview = true;
                    // create a new interview record
                    $interview_details = $request->interview[$program->id];
                    $interview_details['admission_id'] = $request->admission_id;
                    $interview_details['program_id'] = $program->id;
                    $interview_details['status'] = 'waiting_for_interview';
                    $userInfo[] = array('interviewLocation' => $interview_details['location'], 'interview_duration' => '1 hour',
                        'interviewDateTime' => $interview_details['interview_time'], 'email' => $admission->student_email,
                        'id' => $student->id, 'slug' => 'student', 'interviewCommitteeIds' => $interview_details['committee'],
                        'interviewDetails' => $interview_details['notes'], 'programTitle' => $program->programTranslations->first()->title);
                    $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);
                    $systemEmail = EmailSetting::find(1);
                    $system_email = $systemEmail->from_email;
                    $organization_name = $systemSetting->organization_name;
                    $sender['system_email'] = $system_email;
                    $sender['organization_name'] = $organization_name;
                    if ($admission->creator_role == 'parent') {
                        $userInfo[0]["guardian_email"] = $admission->guardian_email;
                    }
                    $userInfo[0]["email"] = $admission->student_email;
                    $userInfo[0]["channel"] = "email";
                    $userInfo[0]["interviewConfirmationExpiryHours"] = $hoursToInterview;
                    $userInfo[0]["admissionId"] = $request->admission_id;
                    $userInfo[0]["phone"] = GeneralSettings::where("organization_id", config('organization_id'))->first()->phone;
                    $userInfo[0]["studentName"] = Student::find($admission->student_id)->full_name;
                    $userInfo[0]["subjectInterviewParticipants"] = implode(", ", Employee::whereIn("id", $userInfo[0]['interviewCommitteeIds'])->pluck("name")->toArray()) . " & " . $student->display_name;
                    $userInfo[0]["InterviewerEmails"] = Employee::whereIn("id", $userInfo[0]['interviewCommitteeIds'])->pluck("email")->toArray();
                    // save the interview details
                    $interview = AdmissionInterview::create($interview_details);
                    // set interview committee
                    $interview_committee = $interview_details['committee'];

                    $this->createInterviewCommitteeMembers($interview_committee, $interview->id);
                    $this->sendInterviewInvitationEmail($userInfo, $sender);


                }
            }
        }


        if ($waiting_for_interview) {
            $admission->status = 'waiting_for_interview';
            $student->status = 'waiting_for_interview';
        } else if ($approved_programs == $admission->programs->count()) {
            $admission->status = 'offer';
            $student->status = 'offer';
        } else if ($approved_programs > 0) {
            $admission->status = 'conditional_offer';
        } else {

            $admission->status = 'rejected';
            $admission->rejected_status_note = $request->interview[$program->id]['notes'];
            $student->status = 'rejected';
        }
        $admission->save();
        $student->save();
            DB::commit();
        if ($request->ajax()) {
            return response()->json([
                "status" => "success"
            ]);
        }
        return redirect()->back();

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Failed to approve student: ' . $e->getMessage());
            if ($request->ajax()) {
                return response()->json([
                    "status" => "error",
                    "message" => 'Failed to approve student: ' . $e->getMessage()
                ]);
            }else{
                return back()->withErrors(['error' => 'Failed to approve student: ' . $e->getMessage()]);
            }
            // Handle the error
        }

    }

    public function setOrientation()
    {
        $request = request();

        $admission = Admission::findOrFail($request->admission_id);

        $this->validate($request, [
            'admission_id' => 'required | numeric',
            'employee_id' => 'required | numeric',
            'orientation_time' => 'required | date',
            'location' => 'required'
        ]);

        $requestData = $request->all();

        $requestData['organization_id'] = config('organization_id');
        $requestData['created_by'] = auth()->user()->id;

        \App\AdmissionOrientation::create($requestData);
        $admission->status = "waiting_for_orientation";

        $admission->save();

        flash('Orintation Session Has Been Set . ');

        return redirect()->back();

    }

    public function finalize(Request $request)
    {
        $admission = Admission::findOrFail($request->admission_id);

        $admission->status = 'active';
        $admission->start_date = date('Y-m-d');

        $admission->save();

        $student = Student::findOrFail($admission->student_id);
        $student->status = 'active';
        $student->save();

        // Check if the program is Nuraniyah or Ijazah and assign initial level using the service
        $program = Program::find($admission->program_id);
        if ($program) {
            try {
                $this->studentLevelService->assignInitialLevel($student, $admission->class_id, $program);
            } catch (\Exception $e) {
                // Handle exception (e.g., log error, flash message)
                Log::error("Failed to assign initial level during finalize: " . $e->getMessage(), ['admission_id' => $admission->id, 'student_id' => $student->id]);
                // Optionally, flash an error message to the user
                // flash('Failed to assign student level. Please check program configuration.')->error();
            }
        }

        if (!\App\ClassStudent::where('class_id', $admission->class_id)->where('student_id', $admission->student_id)->first()) {

            $join_class = new \App\ClassStudent();

            $join_class->student_id = $admission->student_id;
            $join_class->class_id = $admission->class_id;
            $join_class->start_date = date('Y - m - d');

            $join_class->save();
            flash('Student was added to  class successfully.');
        } else {
            flash('Student already has joined the class.');
        }


        return redirect()->back();

    }


    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }

    // new student registration / application form view method
    public function registration()
    {
        try {
            $max_admission_id = Student::max('student_number');
            $max_roll_id = Student::max('roll_no');
            $organizations = Organization::all();
            $classes = Classes::all();
            $programs = ProgramTranslation::where("locale", "en")->get();
            $genders = BaseSetup::where('base_group_id', ' = ', '1')->get();
            $reg_setting = RegistrationSetting::find(1);
            return view('admission::registration', compact('organizations', 'classes', 'programs', 'genders', 'reg_setting', 'max_admission_id', 'max_roll_id'));
//            return view('studentapplication::registration', compact('organizations', 'classes', 'programs', 'genders', 'reg_setting'));
        } catch (\Exception $e) {


            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    function admissionPic(Request $r)
    {

        try {
            $validator = Validator::make($r->all(), [
                'logo_pic' => 'sometimes | required | mimes:jpg,png | max:40000',

            ]);
            if ($validator->fails()) {
                return response()->json(['error' => 'error'], 201);
            }

            if ($r->hasFile('logo_pic')) {
                $file = $r->file('logo_pic');
                $images = Image::make($file)->insert($file);
                $pathImage = 'public/uploads / student / ';
                if (!file_exists($pathImage)) {
                    mkdir($pathImage, 0777, true);
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    $images->save('public/uploads / student / ' . $name);
                    $imageName = 'public/uploads / student / ' . $name;
                    // $data->staff_photo =  $imageName;
                    Session::put('student_photo', $imageName);
                } else {
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    if (file_exists(Session::get('student_photo'))) {
                        File::delete(Session::get('student_photo'));
                    }
                    $images->save('public/uploads / student / ' . $name);
                    $imageName = 'public/uploads / student / ' . $name;
                    // $data->student_photo =  $imageName;
                    Session::put('student_photo', $imageName);
                }
            }


            return response()->json('success', 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'error'], 201);
        }
    }

    // Get class for regular school and saas for new student registration
    public function getCenters(Request $request)
    {


        $centers = Center::whereHas("programs", function ($q) use ($request) {
            return $q->where('program_id', $request->id);
        })->get();


        return response()->json([$centers]);
    }

    // Get section for new registration by ajax
    public function getClasses(Request $request)
    {


        $classes = Classes::where('center_id', $request->id)->get();


        return response()->json([$classes]);
    }

    public function assignDefaultRoles($user, $roleName = null)
    {


//        $default_role_name = Settings::get('default_user_role', 'member');
//        $default_role_name = Settings::get('default_user_role', 'member');


//        $roleName = $default_role_name;


        $user->assignRole('member');
    }

    function sendInterviewInvitationEmail($userInfo, $sender)
    {

        // send an email to the student inviting them to an interview
        // TODO: fix the default laravel email functionality so that this line of code works fine
//                    $user->notify(new InterviewInvitationSent($userInfo, $sender));

        $success = retry(5, function () use ($userInfo, $sender) {
            dispatch(new \App\Jobs\SendStudentInterviewInvitationEmailJob($userInfo[0], $sender));
            return true;
        }, 100);
        if (!$success) {
            \Log::error('Failed to send interview invitation email after 5 attempts');
            return false;
        }
        Toastr::success('Email Successfully sent', 'Success');

    }

    function createInterviewCommitteeMembers($interview_committee, $interview_id) {
        foreach ($interview_committee as $interviewer) {
            // create a new interview committee member record
            AdmissionInterviewer::create([
                'admission_interview_id' => $interview_id,
                'employee_id' => $interviewer
            ]);
        }
    }

    function calculateHoursToInterview($interviewTime) {
        $date = strtotime($interviewTime); // Convert to a PHP date (a second count)
        $diff = $date - time(); // time() returns current time in seconds
        $days = floor($diff / (60 * 60 * 24)); // seconds/minute*minutes/hour*hours/day)
        $hoursToInterview = round(($diff - $days * 60 * 60 * 24) / (60 * 60));
        return (int) $hoursToInterview;
    }





}

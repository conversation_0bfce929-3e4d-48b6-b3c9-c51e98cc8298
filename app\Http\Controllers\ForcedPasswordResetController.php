<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class ForcedPasswordResetController extends Controller
{
    public function showResetForm()
    {
        return view('auth.forced_password_reset');
    }

    public function submitNewPassword(Request $request)
    {
        $request->validate([
            'password' => 'required|min:8|confirmed',
        ]);

        $user = Auth::user();
        $user->password = Hash::make($request->input('password'));
        $user->markPasswordAsChanged();

        return redirect()->route('workplace.dashboard')
            ->with('message','Password changed successfully!');
    }
}

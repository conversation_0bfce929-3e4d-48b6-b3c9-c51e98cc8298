<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentLastIjazasanadMemorizationRecord extends Model
{
    use HasFactory;
    protected $table = 'student_last_ijazasanad_memorization_record';

    protected $fillable = [
        'student_id',
        'ijazasanad_year_month_day',
        'talqeen_from_lesson',
        'talqeen_to_lesson',
        'revision_from_lesson',
        'revision_to_lesson',
        'jazariyah_from_lesson',
        'jazariyah_to_lesson',
        'seminars_from_lesson',
        'seminars_to_lesson',
        'from_surat',
        'from_ayat',
        'to_surat',
        'to_ayat',
//        'level_id',
        'approved_by',
        'updated_at',
        'class_id',
        'organization_id',
        'plan_year_and_month',
        'start_date',
        'end_date',
        'center_id',
        'status',
        'supervisor_comment',
        'created_by',
        'updated_by',
        'delete_reason',
        'deleted_at',
        'talqeen_talaqqi',
        'from_lesson_line_number',
        'to_lesson_line_number',
        'plan_year_month_day',
        'from_surat',
        'from_ayat',
        'to_surat',
        'to_ayat',
    ];

    /**
     * Get the student that owns the plan.
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }


}

<?php

namespace App;

use App\Employee;
use App\Guardian;
use App\Scopes\OrganizationScope;
use App\Student;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Book
 *
 * @property int $id
 * @property string|null $book_title
 * @property string|null $book_number
 * @property string|null $isbn_no
 * @property string|null $publisher_name
 * @property string|null $author_name
 * @property string|null $rack_number
 * @property int|null $quantity
 * @property int|null $book_price
 * @property string|null $post_date
 * @property string|null $details
 * @property int $active_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $subject_id
 * @property int|null $book_category_id
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $organization_id
 * @property int|null $academic_id
 * @property-read \App\BookCategory|null $bookCategory
 * @property-read \App\Subject $bookSubject
 * @method static \Illuminate\Database\Eloquent\Builder|Book newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Book newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Book query()
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereAcademicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereActiveStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereAuthorName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereBookCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereBookNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereBookPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereBookTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereIsbnNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book wherePostDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book wherePublisherName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereRackNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereSubjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Book whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class Book extends Model
{
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);


    }
    public function bookCategory(){
    	return $this->belongsTo('App\BookCategory', 'book_category_id', 'id');
    }
    
    public function bookSubject(){
    	return $this->belongsTo('App\Subject', 'subject', 'id');
    }

    public static function getMemberDetails($memberID){
        
        try {
            return $getMemberDetails = Student::select('full_name', 'email', 'mobile')->where('id', '=', $memberID)->first();
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    }

    public static function getMemberStaffsDetails($memberID){
        
        try {
            return $getMemberDetails = Employee::select('full_name', 'email', 'mobile')->where('user_id', '=', $memberID)->first();
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    }

    public static function getParentDetails($memberID){
        
        try {
            return $getMemberDetails = Guardian::select('full_name', 'email', 'mobile')->where('user_id', '=', $memberID)->first();
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    }

    
}

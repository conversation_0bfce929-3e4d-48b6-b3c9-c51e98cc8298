<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class GenderValidation implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        // Check if the gender value matches any of the allowed values, ignoring case
        $allowedGenders = ['male', 'female'];
        return in_array(strtolower($value), $allowedGenders);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The :attribute must be male or female.';
    }
}

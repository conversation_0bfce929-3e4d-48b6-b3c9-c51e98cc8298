# Feature Request: Dynamic Provider Category Mapping for JobSeeker Module

## Context

The JobSeeker module in our Laravel application currently fetches jobs from two providers: Jobs.af and ACBAR. The current implementation uses hardcoded category mappings in the `config/jobseeker.php` file to translate between our canonical categories and provider-specific categories. We need to refactor this to use a dynamic, database-driven approach.

## Current State

1. **CommandScheduleRule** model has a `provider_category_ids` column (JSON type) that is currently not being used
2. **ProviderJobCategory** model exists with the following structure:
   - `id`
   - `provider_name` (e.g., 'jobs.af', 'acbar')
   - `name` (provider's category name)
   - `provider_identifier` (the value sent to provider's API)
   - `canonical_category_id` (FK to JobCategory)

3. Current flow uses `CommandScheduleFilter` → `FilterTranslationService` → hardcoded config mappings

## Required Changes

### 1. Update the Category Selection Flow

Instead of using canonical category IDs from `CommandScheduleFilter`, the system should:

1. Read `provider_category_ids` from `CommandScheduleRule`
2. Query `ProviderJobCategory` table by these IDs
3. Extract the `provider_identifier` values
4. Send these identifiers to the respective APIs

### 2. Modify FilterRepository

Update the `getJobsAfTranslatedFilters()` and `getAcbarTranslatedFilters()` methods to:

1. Check if the `CommandScheduleRule` has `provider_category_ids` set
2. If yes, query `ProviderJobCategory::whereIn('id', $provider_category_ids)`
3. Filter by `provider_name` (jobs.af or acbar)
4. Use `provider_identifier` values for API calls
5. If `provider_category_ids` is empty/null, fetch all jobs (no category filter)

### 3. Remove Config Dependencies

The following config arrays should no longer be used for category translation:
- `config('jobseeker.jobs_af_default_filters.searchFilters.categories')`
- `config('jobseeker.acbar_default_filters.category_mapping')`

### 4. Update Services

**JobsAfService**: 
- Should receive provider identifiers directly (no translation needed)
- For Jobs.af, `provider_identifier` contains the exact category name to send

**AcbarJobService**:
- Should receive provider identifiers directly (no translation needed)
- For ACBAR, `provider_identifier` contains the numeric category ID

### 5. Migration Considerations

- Ensure existing `CommandScheduleRule` records work without `provider_category_ids` (backward compatibility)
- When `provider_category_ids` is null/empty, fetch all jobs from the provider

## Example Implementation Flow

```php
// In FilterRepository::getJobsAfTranslatedFilters()
$rule = CommandScheduleRule::find($ruleId);
if ($rule && !empty($rule->provider_category_ids)) {
    $providerCategories = ProviderJobCategory::whereIn('id', $rule->provider_category_ids)
        ->where('provider_name', 'jobs.af')
        ->pluck('provider_identifier')
        ->toArray();
    
    // Use $providerCategories in the API request
} else {
    // No category filter - fetch all jobs
    $providerCategories = [];
}
```

## Testing Requirements

1. Test with existing rules that don't have `provider_category_ids` set
2. Test with new rules that have `provider_category_ids` set
3. Verify Jobs.af receives correct category names
4. Verify ACBAR receives correct numeric category IDs
5. Test empty `provider_category_ids` fetches all jobs

## Benefits

1. Dynamic category mapping without code changes
2. Each provider can have different category structures
3. Easy to add new providers
4. No hardcoded configuration
5. Admin can manage mappings through database

## Important Notes

- Maintain backward compatibility for existing schedule rules
- The `FilterTranslationService` dependency on config should be removed
- Location filtering should remain unchanged (still uses Province names/IDs)
- Other filters (search term, experience levels, etc.) remain unchanged

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Entities\Job;
use Mo<PERSON><PERSON>\JobSeeker\Entities\ProviderJobCategory;
use Modules\JobSeeker\Services\CategoryMappingService;
use Carbon\Carbon;

/**
 * BackfillJobCategoriesCommand
 *
 * Backfills canonical categories for jobs that have provider categories but are missing
 * canonical category assignments. This fixes the notification system for recent jobs.
 *
 * Purpose: Restore notification functionality for jobs created before category mapping fix.
 * Side effects: Updates job_category_pivot table with canonical category assignments.
 * Dependencies: CategoryMappingService for provider-to-canonical mapping.
 */
class BackfillJobCategoriesCommand extends Command
{
    protected $signature = 'jobseeker:backfill-job-categories 
                            {--days=30 : Number of days back to process jobs}
                            {--limit=100 : Maximum number of jobs to process per run}
                            {--dry-run : Show what would be done without making changes}';

    protected $description = 'Backfill canonical categories for jobs that have provider categories but missing canonical assignments';

    private CategoryMappingService $categoryMappingService;

    public function __construct(CategoryMappingService $categoryMappingService)
    {
        parent::__construct();
        $this->categoryMappingService = $categoryMappingService;
    }

    public function handle(): int
    {
        $days = (int) $this->option('days');
        $limit = (int) $this->option('limit');
        $dryRun = $this->option('dry-run');

        $this->info("Starting job category backfill process...");
        $this->info("Parameters: days={$days}, limit={$limit}, dry-run=" . ($dryRun ? 'yes' : 'no'));

        try {
            // Find jobs that need backfilling
            $jobsToProcess = $this->findJobsNeedingBackfill($days, $limit);
            
            if ($jobsToProcess->isEmpty()) {
                $this->info('No jobs found that need category backfilling.');
                return 0;
            }

            $this->info("Found {$jobsToProcess->count()} jobs that need category backfilling.");

            $stats = [
                'processed' => 0,
                'updated' => 0,
                'skipped' => 0,
                'errors' => 0
            ];

            foreach ($jobsToProcess as $job) {
                $result = $this->processJob($job, $dryRun);
                $stats[$result]++;
                $stats['processed']++;

                // Show progress
                if ($stats['processed'] % 10 === 0) {
                    $this->info("Processed {$stats['processed']}/{$jobsToProcess->count()} jobs...");
                }
            }

            // Display final results
            $this->displayResults($stats, $dryRun);

            return 0;

        } catch (\Exception $e) {
            $this->error("Error during backfill process: " . $e->getMessage());
            Log::error('BackfillJobCategoriesCommand: Error during execution', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * Find jobs that have provider categories but are missing canonical categories.
     */
    private function findJobsNeedingBackfill(int $days, int $limit)
    {
        $cutoffDate = Carbon::now()->subDays($days);

        return Job::select('jobs.*')
            ->join('job_provider_category_pivot as jpcp', 'jobs.id', '=', 'jpcp.job_id')
            ->leftJoin('job_category_pivot as jcp', 'jobs.id', '=', 'jcp.job_id')
            ->where('jobs.created_at', '>=', $cutoffDate)
            ->whereNull('jcp.job_id') // Jobs without canonical categories
            ->groupBy('jobs.id', 'jobs.position', 'jobs.company_name', 'jobs.source', 'jobs.created_at')
            ->orderBy('jobs.created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Process a single job to backfill its canonical categories.
     */
    private function processJob(Job $job, bool $dryRun): string
    {
        try {
            // Get provider categories for this job
            $providerCategories = $job->providerCategories()->get();
            
            if ($providerCategories->isEmpty()) {
                $this->warn("Job {$job->id} has no provider categories - skipping");
                return 'skipped';
            }

            // Group by provider name for mapping
            $providerGroups = $providerCategories->groupBy('provider_name');
            $allCanonicalIds = [];

            foreach ($providerGroups as $providerName => $categories) {
                $providerCategoryIds = $categories->pluck('id')->toArray();
                
                // Map to canonical categories
                $canonicalIds = $this->categoryMappingService->mapProviderToCanonicalCategoryIds(
                    $providerCategoryIds, 
                    $providerName
                );

                $allCanonicalIds = array_merge($allCanonicalIds, $canonicalIds);
            }

            // Remove duplicates
            $allCanonicalIds = array_unique($allCanonicalIds);

            if (empty($allCanonicalIds)) {
                $this->warn("Job {$job->id} - no canonical categories mapped from provider categories");
                return 'skipped';
            }

            if ($dryRun) {
                $this->line("DRY RUN: Would assign canonical categories " . implode(', ', $allCanonicalIds) . " to job {$job->id} ({$job->position})");
                return 'updated';
            }

            // Assign canonical categories
            $job->categories()->syncWithoutDetaching($allCanonicalIds);

            $this->line("✓ Job {$job->id}: Assigned " . count($allCanonicalIds) . " canonical categories");
            
            Log::info('BackfillJobCategoriesCommand: Successfully backfilled job categories', [
                'job_id' => $job->id,
                'position' => $job->position,
                'source' => $job->source,
                'canonical_category_ids' => $allCanonicalIds,
                'provider_categories_count' => $providerCategories->count()
            ]);

            return 'updated';

        } catch (\Exception $e) {
            $this->error("Error processing job {$job->id}: " . $e->getMessage());
            
            Log::error('BackfillJobCategoriesCommand: Error processing job', [
                'job_id' => $job->id,
                'error' => $e->getMessage()
            ]);

            return 'errors';
        }
    }

    /**
     * Display final results of the backfill process.
     */
    private function displayResults(array $stats, bool $dryRun): void
    {
        $this->info("\n" . str_repeat('=', 50));
        $this->info("BACKFILL RESULTS" . ($dryRun ? " (DRY RUN)" : ""));
        $this->info(str_repeat('=', 50));
        
        $this->info("Jobs processed: {$stats['processed']}");
        $this->info("Jobs updated: {$stats['updated']}");
        $this->info("Jobs skipped: {$stats['skipped']}");
        $this->info("Errors: {$stats['errors']}");

        if ($stats['updated'] > 0 && !$dryRun) {
            $this->info("\n✅ Successfully backfilled canonical categories for {$stats['updated']} jobs!");
            $this->info("These jobs should now be eligible for notifications.");
        }

        if ($dryRun && $stats['updated'] > 0) {
            $this->info("\n🔍 Dry run completed. Run without --dry-run to apply changes.");
        }
    }
}

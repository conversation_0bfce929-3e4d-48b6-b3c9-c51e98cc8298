<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Modules\JobSeeker\Services\ProviderDiagnosticService;

/**
 * Provider Diagnostic API Controller
 * 
 * Handles HTTP requests for provider diagnostic functionality.
 * This is a thin layer that validates input and delegates to ProviderDiagnosticService.
 */
final class ProviderDiagnosticController extends Controller
{
    /**
     * ProviderDiagnosticController constructor.
     */
    public function __construct()
    {
        // Dependencies will be resolved via method injection
    }

    /**
     * Run diagnostic for a specific schedule rule
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function runDiagnostic(Request $request, ProviderDiagnosticService $diagnosticService): JsonResponse
    {
        $startTime = microtime(true);
        
        try {
            // Validate request input
            $validator = Validator::make($request->all(), [
                'rule_id' => 'required|integer|min:1'
            ]);

            if ($validator->fails()) {
                Log::warning('Provider diagnostic API called with invalid parameters', [
                    'errors' => $validator->errors()->toArray(),
                    'request_data' => $request->all()
                ]);

                return response()->json([
                    'success' => false,
                    'error' => 'Invalid parameters',
                    'validation_errors' => $validator->errors(),
                    'execution_time' => round((microtime(true) - $startTime) * 1000, 2)
                ], 400);
            }

            $ruleId = (int) $request->input('rule_id');

            Log::info('Starting provider diagnostic via API', [
                'rule_id' => $ruleId,
                'user_ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // Run the diagnostic using the service
            $diagnosticResults = $diagnosticService->runDiagnostic($ruleId);

            // Add API-specific metadata
            $response = [
                'success' => true,
                'data' => $diagnosticResults,
                'api_execution_time' => round((microtime(true) - $startTime) * 1000, 2),
                'timestamp' => now()->toISOString()
            ];

            Log::info('Provider diagnostic completed via API', [
                'rule_id' => $ruleId,
                'overall_status' => $diagnosticResults['overall_status'],
                'execution_time' => $diagnosticResults['execution_time'] ?? 0,
                'api_execution_time' => $response['api_execution_time']
            ]);

            return response()->json($response);

        } catch (\Exception $e) {
            Log::error('Provider diagnostic API failed with exception', [
                'rule_id' => $request->input('rule_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'api_execution_time' => round((microtime(true) - $startTime) * 1000, 2)
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Diagnostic execution failed',
                'message' => $e->getMessage(),
                'execution_time' => round((microtime(true) - $startTime) * 1000, 2)
            ], 500);
        }
    }

    /**
     * Get available providers and their configurations
     * 
     * This endpoint can be used by the frontend to understand what providers are supported
     *
     * @return JsonResponse
     */
    public function getProviderInfo(): JsonResponse
    {
        try {
            $providerInfo = [
                'supported_commands' => [
                    'jobseeker:sync-jobs-af' => [
                        'name' => 'Jobs.af',
                        'description' => 'Sync jobs from Jobs.af API',
                        'type' => 'api',
                        'endpoint_type' => 'JSON API'
                    ],
                    'jobseeker:sync-acbar-jobs' => [
                        'name' => 'ACBAR',
                        'description' => 'Sync jobs from ACBAR.org website',
                        'type' => 'web_scraping',
                        'endpoint_type' => 'HTML Website'
                    ],
                    'jobseeker:fetch-jobs-af-descriptions' => [
                        'name' => 'Jobs.af (Descriptions)',
                        'description' => 'Fetch detailed descriptions from Jobs.af',
                        'type' => 'api',
                        'endpoint_type' => 'JSON API'
                    ],
                    'jobseeker:cleanup-old-jobs' => [
                        'name' => 'Database Cleanup',
                        'description' => 'Internal database cleanup operation',
                        'type' => 'internal',
                        'endpoint_type' => 'Database Operation'
                    ]
                ],
                'diagnostic_steps' => [
                    'host_connectivity' => 'Tests basic connectivity to the provider host',
                    'api_endpoint' => 'Tests API endpoint availability and response',
                    'data_structure_validation' => 'Validates response data against expected structure'
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $providerInfo
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get provider info', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve provider information',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Health check endpoint for the diagnostic service
     *
     * @return JsonResponse
     */
    public function healthCheck(): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'status' => 'healthy',
                'service' => 'Provider Diagnostic Service',
                'timestamp' => now()->toISOString(),
                'version' => '1.0.0'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
<?php

namespace Modules\Admission\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class StudentAccepted extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @param $userInfo
     * @param $sender
     */
    public function __construct($userInfo, $sender)
    {

        $this->userInfo = $userInfo;
        $this->sender = $sender;
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {





        return (new MailMessage)
                    ->subject('You are accepted to the '.$this->userInfo[0]['programTitle']. ' Program')
            ->view('modules.site.templates.wajeha.backEnd.studentInformation.student_acceptance_confirmation',['data'=> $this->userInfo[0]]);
//                    ->line('Congratulations!')
//                    ->line('We are pleased to inform you that you are accepted to the '.$this->userInfo[0]['programTitle'].  ' program')
//                    ->action('Notification Action', url('/'))
//                    ->line('Thank you for using our application!');
    }


    public function toDatabase($notifiable)
    {


    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}

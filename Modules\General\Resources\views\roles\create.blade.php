@extends('layouts.hound')

@section('content')
    <div class="container">
        <div class="panel panel-primary card-view" >
            <div class="panel-heading">
                <h3>Create New Role</h3>
            </div>
            <div class="panel-body">
                <a href="{{ route('general.roles.index') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
                <br />
                @if ($errors->any())
                    <ul class="alert alert-danger">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                @endif

                {!! Form::open(['route' => 'general.roles.store', 'class' => 'form-horizontal', 'files' => true]) !!}

                @include ('general::roles.form')

                {!! Form::close() !!}

            </div>
        </div>
    </div>
@endsection

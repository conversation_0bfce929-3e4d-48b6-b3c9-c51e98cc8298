<?php

namespace Modules\Education\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\ProgramLevel;
use App\User;
use App\Student;
use App\Admission;
use App\BaseSetup;
use Illuminate\Http\Request;
use Session;
use App\Subject;
use Modules\Education\Http\Requests\BulkAssignRequest;
use Modules\Education\Http\Requests\BulkCreateUsersRequest;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Log;
use App\Services\EmailService;
use App\Services\StudentUserService;
use Carbon\Carbon;

class StudentMissingDataController extends Controller
{

// StudentController.php
    public function missingDataCount() {
        $count = \Cache::remember('missing_data_count', now()->addMinutes(10), function() {
            return User::where(function ($query) {
                $query->whereHas('student', function ($subQuery) {
                    $subQuery->doesntHave('admission');
                })
                    ->orWhereDoesntHave('student');
            })->count();
        });


        return response()->json(['count' => $count]);
    }

    /**
     * Create user account for student with active admission (interactive button)
     */
    public function createUserForAdmission(Request $request)
    {
        try {
            $studentId = $request->input('student_id');
            $student = Student::with(['admissions' => function($q) {
                $q->whereNull('deleted_at')
                  ->where('status', '!=', 'archived');
            }])->find($studentId);

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student not found'
                ], 404);
            }

            // Check if student has active admission
            $admission = $student->admissions->first();
            if (!$admission || !$admission->program_id || !$admission->center_id || !$admission->class_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student does not have a complete active admission'
                ], 400);
            }

            // Check if user already exists
            if ($student->user_id && $student->user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User account already exists for this student'
                ], 400);
            }

            // Use the centralized service to create user
            $studentUserService = new StudentUserService();
            $result = $studentUserService->createUserForStudent($student);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'User account created successfully',
                    'student_name' => $student->full_name ?: $student->display_name,
                    'student_email' => $student->email,
                    'username' => $result['credentials']['username'],
                    'password' => $result['credentials']['password']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Error creating user for admission: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the user account'
            ], 500);
        }
    }

    /**
     * Bulk assign program, center, and/or class to selected students
     */
    public function bulkAssign(BulkAssignRequest $request)
    {
        try {
            $studentIds = $request->student_ids;
            $programId = $request->program_id;
            $centerId = $request->center_id;
            $classId = $request->class_id;


            // Missing data fields for complete admissions (bulk defaults removed - only individual assignments)
            $defaultGuardianId = $request->guardian_id;
            $studentGenders = (!empty($request->student_genders)) ? $request->student_genders : [];
            $studentDatesOfBirth = (!empty($request->student_dates_of_birth)) ? $request->student_dates_of_birth : [];
            $studentGuardians = (!empty($request->student_guardians)) ? $request->student_guardians : [];

            $successCount = 0;
            $errorCount = 0;
            $errors = [];
            $validationErrors = []; // Track validation errors with student details for frontend highlighting

            // Preload all gender records to avoid N+1 queries
            $genders = BaseSetup::where('base_group_id', 1)
                ->where('active_status', 1)
                ->get();
            $genderMap = $genders->keyBy(function ($gender) {
                return strtolower($gender->base_setup_name);
            });

            // Eager load students with their admissions to avoid N+1 queries
            $students = Student::with('admissions')
                ->whereIn('id', $studentIds)
                ->get()
                ->keyBy('id');

            // Initialize StudentUserService for automatic user creation
            $userService = new StudentUserService();
            $createdUsers = [];

            // Process each student in its own transaction to prevent individual failures from affecting others
            foreach ($studentIds as $studentId) {
                try {
                    DB::transaction(function () use ($students, $studentId, $programId, $centerId, $classId, $defaultGuardianId, $studentGenders, $studentDatesOfBirth, $studentGuardians, $genderMap, $userService, &$successCount, &$createdUsers) {
                        $student = $students->get($studentId);
                        if (!$student) {
                            throw new \Exception("Student with ID {$studentId} not found");
                        }

                        // AUTOMATIC USER CREATION: Create user first if student doesn't have one
                        if (!$userService->hasValidUser($student)) {
                            $userResult = $userService->createUserForStudent($student);
                            if ($userResult['success']) {
                                $createdUsers[] = [
                                    'student_id' => $student->id,
                                    'student_name' => $student->full_name,
                                    'student_email' => $student->email,
                                    'username' => $userResult['credentials']['username'],
                                    'password' => $userResult['credentials']['password'],
                                    'restored' => isset($userResult['restored']) ? $userResult['restored'] : false
                                ];
                            }
                            // Note: If user creation fails, we continue with assignment operations
                            // The user creation error will be logged but won't prevent assignment
                        }

                        // Check if student already has an admission (using eager loaded data)
                        $admission = $student->admissions->first();

                        if ($admission) {
                            // Update existing admission
                            $updateData = [];
                            if ($programId) $updateData['program_id'] = $programId;
                            if ($centerId) $updateData['center_id'] = $centerId;
                            if ($classId) $updateData['class_id'] = $classId;

                            if (!empty($updateData)) {
                                $admission->update($updateData);

                                // If class was updated, sync student-class relationship
                                if ($classId) {
                                    $student->joint_classes()->sync([$classId => [
                                        'start_date' => Carbon::now()->toDateString()
                                    ]]);
                                }

                                // program() is a BelongsTo relationship - handled by program_id foreign key above
                                // center() and class() are BelongsTo relationships - handled by foreign keys above

                                $successCount++;
                            }
                        } else {
                            // Create new admission if all required fields are provided
                            if ($programId && $centerId && $classId) {
                                // Determine gender_id for this student (only from individual assignment or existing data)
                                $genderId = null;
                                if (isset($studentGenders[$studentId])) {
                                    $genderId = $studentGenders[$studentId];
                                } elseif ($student->gender) {
                                    // Convert gender string to gender_id using preloaded map
                                    $gender = $genderMap->get(strtolower($student->gender));
                                    $genderId = $gender ? $gender->id : null;
                                }

                                // Determine date_of_birth for this student (only from individual assignment or existing data)
                                $dateOfBirth = null;
                                if (isset($studentDatesOfBirth[$studentId])) {
                                    $dateOfBirth = $studentDatesOfBirth[$studentId];
                                } elseif ($student->date_of_birth) {
                                    $dateOfBirth = $student->date_of_birth;
                                }

                                // Determine guardian_id for this student
                                $guardianId = null;
                                if (isset($studentGuardians[$studentId])) {
                                    $guardianId = $studentGuardians[$studentId];
                                } elseif ($defaultGuardianId) {
                                    $guardianId = $defaultGuardianId;
                                } elseif ($student->guardian_id) {
                                    $guardianId = $student->guardian_id;
                                }

                                // Calculate age from date_of_birth
                                $age = null;
                                if ($dateOfBirth) {
                                    $age = \Carbon\Carbon::parse($dateOfBirth)->age;
                                }

                                // Validate required fields for complete admission (guardian is optional)
                                $missingFields = [];
                                if (!$genderId) $missingFields[] = 'gender';
                                if (!$dateOfBirth) $missingFields[] = 'date_of_birth';

                                if (!empty($missingFields)) {
                                    throw new \Exception("Student {$student->full_name} is missing required data: " . implode(', ', str_replace('_', ' ', $missingFields)) . "|VALIDATION_ERROR|" . json_encode([
                                        'student_id' => $studentId,
                                        'student_name' => $student->full_name,
                                        'missing_fields' => $missingFields
                                    ]));
                                }

                                $admission = Admission::create([
                                    'student_id' => $studentId,
                                    'program_id' => $programId,
                                    'center_id' => $centerId,
                                    'class_id' => $classId,
                                    'gender_id' => $genderId,
                                    'guardian_id' => $guardianId,
                                    'date_of_birth' => $dateOfBirth,
                                    'age' => $age,
                                    'status' => 'active',
                                    'admission_date' => Carbon::now(),
                                    'start_date' => Carbon::now()->toDateString(),
                                    'organization_id' => config('education.organization_id', config('organization_id')),
                                    'created_by' => auth()->id(),
                                    'creator_role' => auth()->user()->getRoleNames()->implode(','),
                                    'student_email' => $student->email,
                                    'student_mobile' => $student->phone,
                                    'added_from' => 'bulk_assign'
                                ]);

                                // Update student record if we provided missing data
                                $studentUpdateData = [];
                                if (!$student->gender && $genderId) {
                                    // Convert gender_id back to gender string for student table
                                    $gender = $genderMap->firstWhere('id', $genderId);
                                    if ($gender) {
                                        $studentUpdateData['gender'] = strtolower($gender->base_setup_name);
                                    }
                                }
                                if (!$student->date_of_birth && $dateOfBirth) {
                                    $studentUpdateData['date_of_birth'] = $dateOfBirth;
                                }
                                if (!$student->guardian_id && $guardianId) {
                                    $studentUpdateData['guardian_id'] = $guardianId;
                                }

                                if (!empty($studentUpdateData)) {
                                    $student->update($studentUpdateData);
                                }

                                // Sync student-class relationship (enforce one class per student rule)
                                // Note: timestamps are handled automatically by withTimestamps() in the relationship
                                $student->joint_classes()->sync([$classId => [
                                    'start_date' => Carbon::now()->toDateString()
                                ]]);

                                // program() is a BelongsTo relationship - handled by program_id foreign key above
                                // center() and class() are BelongsTo relationships - handled by foreign keys above

                                $successCount++;
                            } else {
                                throw new \Exception("Student {$student->full_name} requires all fields (program, center, class) for new admission");
                            }
                        }
                    });
                } catch (\Exception $e) {
                    $errorCount++;
                    $errorMessage = $e->getMessage();

                    // Check if this is a validation error with special formatting
                    if (strpos($errorMessage, '|VALIDATION_ERROR|') !== false) {
                        $parts = explode('|VALIDATION_ERROR|', $errorMessage);
                        $displayMessage = $parts[0];
                        $validationData = json_decode($parts[1], true);

                        // Add to validation errors for frontend highlighting
                        $validationErrors[] = $validationData;
                        $errors[] = $displayMessage;
                    } else {
                        $errors[] = "Error processing student ID {$studentId}: " . $errorMessage;
                    }
                }
            }

            // Prepare success message
            $message = "Bulk assignment completed. {$successCount} students updated successfully.";
            if ($errorCount > 0) {
                $message .= " {$errorCount} errors occurred.";
            }
            if (count($createdUsers) > 0) {
                $message .= " " . count($createdUsers) . " user accounts created automatically.";
            }

            // Return success: false if any errors occurred during bulk operations
            $hasErrors = $errorCount > 0;

            $responseDetails = [
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'errors' => $errors,
                'total_processed' => count($studentIds),
                'validation_errors' => $validationErrors, // For frontend highlighting
                'created_users' => $createdUsers // Automatically created user credentials
            ];

            return response()->json([
                'success' => !$hasErrors,
                'message' => $message,
                'details' => $responseDetails
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred during bulk assignment: ' . $e->getMessage()
            ], 500);
        }
    }





    /**
     * Send user credentials via email to a single student
     */
    public function sendCredentialsEmail(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'student_id' => 'required|integer|exists:students,id',
                'student_name' => 'required|string',
                'student_email' => 'required|email',
                'username' => 'required|string',
                'password' => 'required|string'
            ]);

            $studentData = [
                'student_id' => $request->student_id,
                'student_name' => $request->student_name,
                'student_email' => $request->student_email,
                'username' => $request->username,
                'password' => $request->password
            ];

            // Send email using centralized EmailService
            $emailService = app(EmailService::class);

            $subject = 'Your Login Credentials - ' . config('app.name');
            $viewData = [
                'student_name' => $studentData['student_name'],
                'username' => $studentData['username'],
                'password' => $studentData['password'],
                'login_url' => url('/login'),
                'organization_name' => config('app.name')
            ];

            $result = $emailService->send(
                $studentData['student_email'],
                $subject,
                '', // body will be generated from view
                $viewData,
                'emails.user_credentials' // email template
            );

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Credentials sent successfully to ' . $studentData['student_name']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send email'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send user credentials via email to multiple students
     *
     * Includes abuse prevention controls:
     * - Maximum batch size limit (configurable, default: 100)
     * - Admin approval requirement for large batches (configurable threshold, default: 50)
     * - Hourly rate limiting per user (configurable, default: 200 emails/hour)
     * - Comprehensive logging of bulk operations
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendAllCredentialsEmails(Request $request)
    {
        try {
            // Get configuration values
            $maxRecipients = config('education.bulk_email.max_recipients_per_batch', 100);
            $adminThreshold = config('education.bulk_email.admin_approval_threshold', 50);
            $requireAdminApproval = config('education.bulk_email.require_admin_approval', true);

            // Validate request with size constraints
            $request->validate([
                'credentials' => "required|array|max:{$maxRecipients}",
                'credentials.*.student_id' => 'required|integer|exists:students,id',
                'credentials.*.student_name' => 'required|string',
                'credentials.*.student_email' => 'required|email',
                'credentials.*.username' => 'required|string',
                'credentials.*.password' => 'required|string',
                'admin_approved' => 'sometimes|boolean'
            ]);

            $credentials = $request->credentials;
            $credentialsCount = count($credentials);

            // Check if admin approval is required for large batches
            if ($requireAdminApproval && $credentialsCount > $adminThreshold) {
                $adminApproved = $request->boolean('admin_approved', false);

                if (!$adminApproved) {
                    return response()->json([
                        'success' => false,
                        'requires_admin_approval' => true,
                        'message' => "This operation requires admin approval as it involves {$credentialsCount} recipients (threshold: {$adminThreshold}). Please confirm to proceed.",
                        'credentials_count' => $credentialsCount,
                        'admin_threshold' => $adminThreshold
                    ], 422);
                }

                // Log admin-approved bulk operation
                Log::info('Bulk email operation approved by admin', [
                    'user_id' => auth()->id(),
                    'credentials_count' => $credentialsCount,
                    'admin_threshold' => $adminThreshold,
                    'timestamp' => now()
                ]);
            }

            // Rate limiting using Laravel's RateLimiter
            $hourlyLimit = config('education.bulk_email.rate_limit_per_hour', 200);
            $rateLimiterKey = 'bulk_email_' . auth()->id();

            // Check if rate limit would be exceeded
            if (RateLimiter::tooManyAttempts($rateLimiterKey, $hourlyLimit)) {
                $availableIn = RateLimiter::availableIn($rateLimiterKey);
                return response()->json([
                    'success' => false,
                    'message' => "Rate limit exceeded. You can send up to {$hourlyLimit} emails per hour. Try again in " . gmdate('H:i:s', $availableIn),
                    'rate_limit_exceeded' => true,
                    'hourly_limit' => $hourlyLimit,
                    'available_in_seconds' => $availableIn,
                    'requested_count' => $credentialsCount
                ], 429);
            }

            // Hit the rate limiter for the number of emails being sent
            RateLimiter::hit($rateLimiterKey, 3600, $credentialsCount); // 3600 seconds = 1 hour

            $emailService = app(EmailService::class);

            $sentCount = 0;
            $failedCount = 0;
            $errors = [];

            foreach ($credentials as $studentData) {
                try {
                    $subject = 'Your Login Credentials - ' . config('app.name');
                    $viewData = [
                        'student_name' => $studentData['student_name'],
                        'username' => $studentData['username'],
                        'password' => $studentData['password'],
                        'login_url' => url('/login'),
                        'organization_name' => config('app.name')
                    ];

                    $result = $emailService->send(
                        $studentData['student_email'],
                        $subject,
                        '', // body will be generated from view
                        $viewData,
                        'emails.user_credentials' // email template
                    );

                    if ($result) {
                        $sentCount++;
                    } else {
                        $failedCount++;
                        $errors[] = "Failed to send email to {$studentData['student_name']}";
                    }

                } catch (\Exception $e) {
                    $failedCount++;
                    $errors[] = "Error sending email to {$studentData['student_name']}: " . $e->getMessage();
                }
            }

            $totalCount = count($credentials);
            $success = $failedCount === 0;

            // Log bulk email operation
            if ($sentCount > 0) {
                Log::info('Bulk email operation completed', [
                    'user_id' => auth()->id(),
                    'sent_count' => $sentCount,
                    'failed_count' => $failedCount,
                    'total_count' => $totalCount,
                    'rate_limiter_key' => $rateLimiterKey,
                    'timestamp' => now()
                ]);
            }

            return response()->json([
                'success' => $success,
                'message' => $success
                    ? "All {$sentCount} emails sent successfully"
                    : "{$sentCount} emails sent, {$failedCount} failed",
                'sent_count' => $sentCount,
                'details' => [
                    'sent_count' => $sentCount,
                    'failed_count' => $failedCount,
                    'total_count' => $totalCount,
                    'errors' => $errors,
                    'rate_limit_info' => [
                        'hourly_limit' => $hourlyLimit,
                        'rate_limiter_key' => $rateLimiterKey,
                        'remaining_attempts' => RateLimiter::remaining($rateLimiterKey, $hourlyLimit)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending emails: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update individual student gender
     */
    public function updateIndividualGender(Request $request, $studentId)
    {
        try {
            $student = Student::findOrFail($studentId);
            $genderId = $request->input('gender_id');

            if ($genderId) {
                // Get gender name from BaseSetup
                $gender = \App\BaseSetup::where('base_group_id', 1)
                    ->where('active_status', 1)
                    ->where('id', $genderId)
                    ->first();

                if ($gender) {
                    $createdUser = null;

                    DB::transaction(function () use ($student, $gender, &$createdUser) {
                        // AUTOMATIC USER CREATION: Create user first if student doesn't have one
                        $userService = new StudentUserService();
                        if (!$userService->hasValidUser($student)) {
                            $userResult = $userService->createUserForStudent($student);
                            if ($userResult['success']) {
                                $createdUser = [
                                    'student_name' => $student->full_name,
                                    'student_email' => $student->email,
                                    'username' => $userResult['credentials']['username'],
                                    'password' => $userResult['credentials']['password']
                                ];
                            }
                        }

                        // Update student gender
                        $student->update([
                            'gender' => strtolower($gender->base_setup_name)
                        ]);
                    });

                    $response = [
                        'success' => true,
                        'message' => 'Gender updated successfully'
                    ];

                    // Include user creation info if a user was created
                    if ($createdUser) {
                        $action = $createdUser['restored'] ? 'restored' : 'created';
                        $response['user_created'] = $createdUser;
                        $response['message'] .= " and user account {$action} automatically";
                    }

                    return response()->json($response);
                }
            }

            return response()->json([
                'success' => false,
                'message' => 'Invalid gender selection'
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating gender: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update individual student date of birth
     */
    public function updateIndividualDateOfBirth(Request $request, $studentId)
    {
        try {
            $student = Student::findOrFail($studentId);
            $dateOfBirth = $request->input('date_of_birth');

            if ($dateOfBirth) {
                // Validate age (minimum 3 years old)
                $dob = \Carbon\Carbon::parse($dateOfBirth);
                $age = $dob->age;

                if ($age < 3) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Student must be at least 3 years old'
                    ], 400);
                }

                $createdUser = null;

                DB::transaction(function () use ($student, $dateOfBirth, &$createdUser) {
                    // AUTOMATIC USER CREATION: Create user first if student doesn't have one
                    $userService = new StudentUserService();
                    if (!$userService->hasValidUser($student)) {
                        $userResult = $userService->createUserForStudent($student);
                        if ($userResult['success']) {
                            $createdUser = [
                                'student_name' => $student->full_name,
                                'student_email' => $student->email,
                                'username' => $userResult['credentials']['username'],
                                'password' => $userResult['credentials']['password']
                            ];
                        }
                    }

                    // Update student date of birth
                    $student->update([
                        'date_of_birth' => $dateOfBirth
                    ]);
                });

                $response = [
                    'success' => true,
                    'message' => 'Date of birth updated successfully'
                ];

                // Include user creation info if a user was created
                if ($createdUser) {
                    $action = $createdUser['restored'] ? 'restored' : 'created';
                    $response['user_created'] = $createdUser;
                    $response['message'] .= " and user account {$action} automatically";
                }

                return response()->json($response);
            }

            return response()->json([
                'success' => false,
                'message' => 'Invalid date of birth'
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating date of birth: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update individual student full name
     */
    public function updateIndividualFullName(Request $request, $studentId)
    {
        try {
            $student = Student::with('user')->findOrFail($studentId);
            $fullName = trim($request->input('full_name'));

            if (!empty($fullName)) {
                // Validate name length and format
                if (strlen($fullName) < 2) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Full name must be at least 2 characters long'
                    ], 400);
                }

                if (strlen($fullName) > 100) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Full name must not exceed 100 characters'
                    ], 400);
                }

                // Use transaction to ensure both updates succeed or fail together
                \DB::transaction(function () use ($student, $fullName) {
                    // Update all student name fields to be on the safe side
                    $student->update([
                        'full_name' => $fullName,
                        'display_name' => $fullName,
                        'full_name_trans' => $fullName  // Can be updated later with actual translation
                    ]);

                    // If the student has a user record, update all user name fields as well
                    if ($student->user) {
                        $student->user->update([
                            'full_name' => $fullName,
                            'display_name' => $fullName,
                            'full_name_trans' => $fullName  // Can be updated later with actual translation
                        ]);
                    }
                });

                return response()->json([
                    'success' => true,
                    'message' => 'Full name updated successfully for both student and user records'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Full name is required'
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating full name: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update individual student nationality
     */
    public function updateIndividualNationality(Request $request, $studentId)
    {
        try {
            $student = Student::with('user')->findOrFail($studentId);
            $nationalityCode = trim($request->input('nationality'));

            if (!empty($nationalityCode)) {
                // Validate nationality code exists in countries table
                $country = \App\Country::where('code', $nationalityCode)->first();
                if (!$country) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid nationality selection'
                    ], 400);
                }

                $createdUser = null;

                // Use transaction to ensure all updates succeed or fail together
                \DB::transaction(function () use ($student, $nationalityCode, &$createdUser) {
                    // AUTOMATIC USER CREATION: Create user first if student doesn't have one
                    $userService = new StudentUserService();
                    if (!$userService->hasValidUser($student)) {
                        $userResult = $userService->createUserForStudent($student);
                        if ($userResult['success']) {
                            $createdUser = [
                                'student_name' => $student->full_name,
                                'student_email' => $student->email,
                                'username' => $userResult['credentials']['username'],
                                'password' => $userResult['credentials']['password'],
                                'restored' => isset($userResult['restored']) ? $userResult['restored'] : false
                            ];
                            // Refresh the student relationship to get the newly created user
                            $student->load('user');
                        }
                    }

                    // Update the student's nationality field
                    $student->update([
                        'nationality' => $nationalityCode
                    ]);

                    // Update the user's nationality as well (now guaranteed to exist)
                    if ($student->user) {
                        $student->user->update([
                            'nationality' => $nationalityCode
                        ]);
                    }
                });

                $response = [
                    'success' => true,
                    'message' => 'Nationality updated successfully for both student and user records'
                ];

                // Include user creation info if a user was created
                if ($createdUser) {
                    $action = $createdUser['restored'] ? 'restored' : 'created';
                    $response['user_created'] = $createdUser;
                    $response['message'] = "Nationality updated successfully and user account {$action} automatically";
                }

                return response()->json($response);
            }

            return response()->json([
                'success' => false,
                'message' => 'Nationality is required'
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating nationality: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update individual student image
     */
    public function updateIndividualImage(Request $request, $studentId)
    {
        try {
            $student = Student::findOrFail($studentId);

            if (!$request->hasFile('image')) {
                return response()->json([
                    'success' => false,
                    'message' => 'No image file provided'
                ], 400);
            }

            $file = $request->file('image');

            // Validate file
            if (!$file->isValid()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid file upload'
                ], 400);
            }

            // Validate file type
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file->getMimeType(), $allowedTypes)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'
                ], 400);
            }

            // Validate file size (max 5MB)
            if ($file->getSize() > 5 * 1024 * 1024) {
                return response()->json([
                    'success' => false,
                    'message' => 'File size too large. Maximum size is 5MB.'
                ], 400);
            }

            $createdUser = null;
            $storedPath = null;

            // Use transaction to ensure all operations succeed or fail together
            \DB::transaction(function () use ($student, $file, &$createdUser, &$storedPath) {
                // AUTOMATIC USER CREATION: Create user first if student doesn't have one
                $userService = new StudentUserService();
                if (!$userService->hasValidUser($student)) {
                    $userResult = $userService->createUserForStudent($student);
                    if ($userResult['success']) {
                        $createdUser = [
                            'student_name' => $student->full_name,
                            'student_email' => $student->email,
                            'username' => $userResult['credentials']['username'],
                            'password' => $userResult['credentials']['password'],
                            'restored' => isset($userResult['restored']) ? $userResult['restored'] : false
                        ];
                        // Refresh the student relationship to get the newly created user
                        $student->load('user');
                    }
                }

                // Use StudentImageService to handle the upload
                $imageService = app(\App\Services\StudentImageService::class);
                $storedPath = $imageService->storeStudentImage($student, $file);
            });

            $response = [
                'success' => true,
                'message' => 'Image uploaded successfully',
                'image_url' => asset($storedPath)
            ];

            // Include user creation info if a user was created
            if ($createdUser) {
                $action = $createdUser['restored'] ? 'restored' : 'created';
                $response['user_created'] = $createdUser;
                $response['message'] .= " and user account {$action} automatically";
            }

            return response()->json($response);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error uploading image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export students with missing data to Excel
     */
    public function exportToExcel(Request $request)
    {
        try {
            $selectedStudents = $request->input('selected_students', []);
            $userStatusFilter = $request->input('user_status_filter');
            $programFilter = $request->input('program_filter');
            $centerFilter = $request->input('center_filter');
            $classFilter = $request->input('class_filter');

            // Build the query - Fix relationship issue by using admissions relationships
            $query = Student::with(['user', 'admissions.program', 'admissions.center', 'admissions.class'])
                ->select('students.*');

            // Apply filters if no specific students are selected
            if (empty($selectedStudents)) {
                // Apply user status filter
                if ($userStatusFilter === 'no_user') {
                    $query->whereNull('user_id');
                } elseif ($userStatusFilter === 'has_user') {
                    $query->whereNotNull('user_id');
                }

                // Apply program filter
                if ($programFilter) {
                    $query->where('program_id', $programFilter);
                }

                // Apply center filter
                if ($centerFilter) {
                    $query->where('center_id', $centerFilter);
                }

                // Apply class filter
                if ($classFilter) {
                    $query->where('class_id', $classFilter);
                }

                // Filter for students with missing data
                $query->where(function($q) {
                    $q->whereNull('gender')
                      ->orWhereNull('date_of_birth')
                      ->orWhereNull('user_id');
                });
            } else {
                // Export only selected students
                $query->whereIn('id', $selectedStudents);
            }

            $students = $query->get();

            // Prepare CSV data
            $csvData = [];
            $csvData[] = [
                'ID',
                'Full Name',
                'Email',
                'Gender',
                'Date of Birth',
                'Age',
                'Program',
                'Center',
                'Class',
                'Username',
                'Has User Account',
                'Missing Data',
                'Created At'
            ];

            foreach ($students as $student) {
                $missingData = [];
                if (!$student->gender) $missingData[] = 'Gender';
                if (!$student->date_of_birth) $missingData[] = 'Date of Birth';
                if (!$student->user_id) $missingData[] = 'User Account';

                $age = '';
                if ($student->date_of_birth) {
                    $age = \Carbon\Carbon::parse($student->date_of_birth)->age;
                }

                // Get admission data for program, center, class relationships
                $admission = $student->admissions->first();

                $csvData[] = [
                    $student->id,
                    $student->full_name,
                    $student->student_email,
                    $student->gender ?: 'Not Set',
                    $student->date_of_birth ? \Carbon\Carbon::parse($student->date_of_birth)->format('Y-m-d') : 'Not Set',
                    $age,
                    $admission && $admission->program ? $admission->program->title : 'Not Assigned',
                    $admission && $admission->center ? $admission->center->name : 'Not Assigned',
                    $admission && $admission->class ? $admission->class->name : 'Not Assigned',
                    $student->user ? $student->user->username : 'No Account',
                    $student->user_id ? 'Yes' : 'No',
                    implode(', ', $missingData),
                    $student->created_at->format('Y-m-d H:i:s')
                ];
            }

            // Generate CSV content
            $filename = 'students_missing_data_' . date('Y-m-d_H-i-s') . '.csv';

            $callback = function() use ($csvData) {
                $file = fopen('php://output', 'w');
                foreach ($csvData as $row) {
                    fputcsv($file, $row);
                }
                fclose($file);
            };

            return response()->stream($callback, 200, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

}

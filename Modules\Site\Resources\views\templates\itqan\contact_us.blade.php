@extends('home.layouts.home')
@section('page_title') {{trans('home_header.contact_us')}}   @endsection

@section('content')
    <style>
        fieldset {
            border: 0px solid #c0c0c0;
            margin: 0px;
            padding: 0px;
        }

    </style>


    @if(App::getLocale()=="ar")
        <style>
            .control-label {
                text-align: right !important;
            }
        </style>
    @else
        <style>
            .control-label {
                text-align: left !important;
            }
        </style>
    @endif



    <?php
    echo "<script>";
    echo "var map;";
    echo "var infowindow;";
    echo "function initMap() {";
    echo "var map = new google.maps.Map(document.getElementById('map'), {";
    echo "zoom: " . cache('localisation_zoom') . ",";
    echo "center: {lat: " . cache('localisation_lat') . ", lng: " . cache('localisation_lng') . "}";
    echo "});";
    echo "var icon =";
    echo "{";
    echo "url: '" . URL::to('home_style/images/sys_logo/matrker.png') . "', ";
    echo "scaledSize: new google.maps.Size(100, 120), ";
    echo "origin: new google.maps.Point(0, 0),";
    echo "anchor: new google.maps.Point(0, 0) ";
    echo "};";
    echo "var beachMarker = new google.maps.Marker({";
    echo "position: {lat: " . cache('localisation_lat') . ", lng: " . cache('localisation_lng') . "},";
    echo "map: map,";
    echo "icon: icon,";
    echo "animation: google.maps.Animation.DROP,";
    echo "title: '" . cache('title_' . App::getLocale()) . "'";
    echo "});";
    echo "}";
    echo "</script>";
    ?>

    <!-- GOOGLE MAPS-->
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyD0Wia_3MzOkl8a7dw3crSlw0TxIbfbK5Y&libraries=places&callback=initMap"
            async defer></script>
    <!---->

    <section class="page-header page-header-xs dark">
        <div class="container">
            <h1>{{trans('home_content.contact')}}</h1>

        </div>
    </section>

    <!-- -->
    <section>

        <div id="map" class="contact-over-map"></div>

        <div class="container">
            <div class="contact-over-box pull-right ">
                <h3 class="size-20"><strong><em>
                            {{trans('home_content.contact_us')}}
                        </em></strong></h3>


                <form class="form-horizontal text-center" id="contact_us_form" action="{{url('home/contact_us_post')}}"
                      method="POST">

                    <div class="form-group {{ $errors->has('full_name') ? 'has-error' : ''}}">
                        {!! Form::label('contact_full_name', trans('home_content.full_name'), ['class' => 'col-md-12 control-label']) !!}
                        <div class="col-md-12">
                            {!! Form::text('contact_full_name', null, ['class' => 'form-control']) !!}
                            {!! $errors->first('contact_full_name', '<p class="help-block">:message</p>') !!}
                        </div>
                    </div>

                    <div class="form-group {{ $errors->has('email') ? 'has-error' : ''}}">
                        {!! Form::label('contact_email', trans('home_content.email'), ['class' => 'col-md-12 control-label']) !!}
                        <div class="col-md-12">
                            {!! Form::text('contact_email', null, ['class' => 'form-control']) !!}
                            {!! $errors->first('contact_email', '<p class="help-block">:message</p>') !!}
                        </div>
                    </div>

                    <div class="form-group {{ $errors->has('tel') ? 'has-error' : ''}}">
                        {!! Form::label('contact_tel', trans('home_content.tel'), ['class' => 'col-md-12 control-label']) !!}
                        <div class="col-md-12">
                            {!! Form::text('contact_tel', null, ['class' => 'form-control']) !!}
                            {!! $errors->first('contact_tel', '<p class="help-block">:message</p>') !!}
                        </div>
                    </div>

                    <div class="form-group {{ $errors->has('subject') ? 'has-error' : ''}}">
                        {!! Form::label('contact_subject',trans('home_content.subject'), ['class' => 'col-md-12 control-label']) !!}
                        <div class="col-md-12">
                            {!! Form::text('contact_subject', null, ['class' => 'form-control']) !!}
                            {!! $errors->first('contact_subject', '<p class="help-block">:message</p>') !!}
                        </div>
                    </div>

                    <div class="form-group {{ $errors->has('message') ? 'has-error' : ''}}">
                        {!! Form::label('contact_message',trans('home_content.message'), ['class' => 'col-md-12 control-label']) !!}
                        <div class="col-md-12">
                            {!! Form::textarea('contact_message', null, ['class' => 'form-control']) !!}
                            {!! $errors->first('contact_message', '<p class="help-block">:message</p>') !!}
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-md-offset-4 col-md-4">

                            <button class="btn btn-success" type="submit"
                                    id="submith_newsletter">{{trans('home_content.send')}}</button>
                        </div>

                    </div>

                </form>


            </div>
        </div>


    </section>
    <!-- / -->
@endsection
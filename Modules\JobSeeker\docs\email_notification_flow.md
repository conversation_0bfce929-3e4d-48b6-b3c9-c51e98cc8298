flowchart TD
    A["📅 app/Console/Kernel.php<br/>loadDynamicCommandSchedule()"] --> B["🗃️ CommandScheduleRule Entity<br/>Gets active rules from DB"]
    
    B --> C["⚡ Command Triggered:<br/>jobseeker:notify-job-seekers<br/>--schedule-rule-id=X"]
    
    C --> D["📋 Modules/JobSeeker/Console/Commands/<br/>NotifyJobSeekersCommand.php<br/>handle()"]
    
    D --> E["🔧 Modules/JobSeeker/Services/<br/>JobService.php<br/>notifyJobSeekers()"]
    
    E --> F["📊 Query Active JobSeekers<br/>with Notification Setups<br/>Process in chunks of 50"]
    
    F --> G["🚀 Dispatch Job:<br/>Modules/JobSeeker/Jobs/<br/>ProcessJobNotificationSetupJob.php"]
    
    G --> H["⚙️ ProcessJobNotificationSetupJob<br/>handle() - Queue: setup_processors"]
    
    H --> I["📋 Load JobNotificationSetup<br/>with categories & recipients"]
    
    I --> J{"✅ Setup Active &<br/>New Jobs Found?"}
    
    J -->|No| Z1["❌ Skip - Log & Exit"]
    J -->|Yes| K["🚀 Dispatch Job:<br/>Modules/JobSeeker/Jobs/<br/>ProcessJobNotificationForRecipientJob.php"]
    
    K --> L["⚙️ ProcessJobNotificationForRecipientJob<br/>handle() - Queue: recipient_processors"]
    
    L --> M["📧 Create Anonymous Notifiable Object<br/>with email & name"]
    
    M --> N["🔔 Modules/JobSeeker/Notifications/<br/>JobAlertNotification.php<br/>toMail()"]
    
    N --> O["📧 app/Services/EmailService.php<br/>sendEmail() - Legacy Method"]
    
    O --> P["📧 EmailService.php<br/>send() - Main Method"]
    
    P --> Q["📦 Create OutgoingEmail Record<br/>(Transactional Outbox Pattern)"]
    
    Q --> R{"🔄 Sync or Async<br/>Mode?"}
    
    R -->|Async| S["🚀 Dispatch Job:<br/>app/Jobs/SendEmailJob.php"]
    R -->|Sync| T["📧 Direct Email Send<br/>with Provider Failover"]
    
    S --> U["⚙️ SendEmailJob.php<br/>handle()"]
    
    U --> V["🎨 generateEmailContent()<br/>View::make() renders template"]
    
    V --> W["📄 resources/views/modules/jobseeker/<br/>emails/jobs/jobseeker_notification.blade.php"]
    
    W --> X["📧 Provider Selection:<br/>Gmail/Mailtrap/Resend<br/>via PHPMailer/MailtrapClient"]
    
    T --> X
    
    X --> Y{"✅ Email Sent<br/>Successfully?"}
    
    Y -->|Yes| AA["✅ Update Records:<br/>- OutgoingEmail::markAsSent()<br/>- EmailSendingLog (success)<br/>- JobNotificationSentJob::create()"]
    Y -->|No| BB["❌ Update Records:<br/>- OutgoingEmail::markSendFailed()<br/>- EmailSendingLog (failure)<br/>- Retry up to 5 times"]
    
    AA --> CC["📱 Send FCM Push Notification<br/>(if enabled)"]
    BB --> CC
    
    CC --> DD["📊 Update Setup:<br/>- last_notified_at<br/>- increment sent_count"]
    
    Z1 --> DD
    
    style A fill:#e3f2fd
    style D fill:#f3e5f5
    style E fill:#e8f5e8
    style G fill:#fff3e0
    style K fill:#fff3e0
    style N fill:#fce4ec
    style O fill:#e1f5fe
    style S fill:#fff8e1
    style W fill:#f1f8e9
    style X fill:#fef7ff
    style AA fill:#e8f5e8
    style BB fill:#ffebee
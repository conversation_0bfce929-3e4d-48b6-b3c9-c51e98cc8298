<?php

namespace Modules\General\Http\Controllers;

use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Role;
use App\Permission;
use App\Authorizable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\RolePermission\Entities\PermissionAssign;


class RoleController extends Controller
{
    // use Authorizable;

    public function index()
    {
        $roles = Role::all();
        $permissions = Permission::all();

        return view('general::roles.index', compact('roles', 'permissions'));
    }

    public function create()
    {

        $messages = [];// Message::limit(5)->get();
        return view('general::roles.create', compact('messages'));

    }

    public function store(Request $request)
    {
        try {
            // Validate the request
            $this->validate($request, ['description' => 'required']);
            // Modify the request data
            $request->merge(['name' => \Illuminate\Support\Str::slug($request->description) . '_' . config('organization_id') . '_']);
            // Validate the modified request data
            $this->validate($request, ['name' => 'required|unique:roles']);

            // Create an empty role array
            $role = [];

            // Debugging: dump the request data
            // Populate the role array
            $role['name'] = $request->name;
            $role['description'] = $request->description;
            $role['organization_id'] = config('organization_id');
            $newRole = Role::create($role);
            // Attempt to create a new role
            if ($newRole) {
                // Flash a success message
                flash('Role Added');

                // Get the selected permissions from the request
                $permissions = $request->get('permissions', []);

                foreach ($permissions as $permission) {
                    // Check if the permission already exists in the database
                    if (!Permission::where('name', $permission)->where('guard_name', 'employee')->first()) {
                        // Create a new permission if it does not exist
                        Permission::create(['name' => $permission, 'organization_id' => 2]);
                    }
                }

                // Synchronize the new role's permissions
                $newRole->syncPermissions($permissions);

            } else {
                // Flash an error message
                flash('Error occurred while adding role');
            }

            // Redirect to the roles page
            return redirect('workplace/general/roles');
        } catch (\Exception $e) {
            // Handle any exceptions
            flash('Error occured while adding role');
            return redirect()->back()->withErrors(['error' => $e->getMessage()]);        }
    }



    /**
     * Show the form for editing the specified role.
     * 
     * This method fetches the role and all associated employees
     * to provide administrators with a complete view of who will be affected
     * by permission changes.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $role = Role::with('permissions')->findOrFail($id);
        
        // Get employees associated with this role
        $roleEmployees = $this->getRoleEmployees($id);

        // Get total permissions count for this role's context (all permissions available)
        $totalPermissions = Permission::count();

        return view('general::roles.edit', compact('role', 'roleEmployees', 'totalPermissions'));
    }

    /**
     * Get all employees assigned to a specific role
     *
     * @param int $roleId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getRoleEmployees($roleId)
    {
        try {
            return DB::table('model_has_roles')
                ->join('employees', 'model_has_roles.model_id', '=', 'employees.id')
                ->where('model_has_roles.role_id', $roleId)
                ->where('model_has_roles.model_type', 'App\\Employee')
                ->whereNull('employees.deleted_at')
                ->select('employees.id', 'employees.name', 'employees.email', 'employees.full_name', 'employees.display_name', 'employees.image', 'employees.gender')
                ->get();
        } catch (\Exception $e) {
            \Log::error("Error fetching employees for role {$roleId}: " . $e->getMessage());
            return collect(); // Return empty collection on error
        }
    }

    /**
     * Detach an employee from a specific role
     *
     * @param int $roleId
     * @param int $employeeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function detachEmployee($roleId, $employeeId)
    {
        try {
            DB::beginTransaction();
            
            // Verify the role exists
            $role = Role::findOrFail($roleId);
            
            // Check if employee has this role
            $hasRole = DB::table('model_has_roles')
                ->where('role_id', $roleId)
                ->where('model_id', $employeeId)
                ->where('model_type', 'App\\Employee')
                ->exists();
            
            if (!$hasRole) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Employee does not have this role'
                ], 400);
            }
            
            // Detach the employee from the role
            DB::table('model_has_roles')
                ->where('role_id', $roleId)
                ->where('model_id', $employeeId)
                ->where('model_type', 'App\\Employee')
                ->delete();
            
            DB::commit();
            
            // Log the action
            \Log::info("Employee ID {$employeeId} detached from role ID {$roleId} by user " . auth()->id());
            
            return response()->json([
                'status' => 'success',
                'message' => 'Employee successfully detached from role'
            ]);
            
        } catch (\Exception $e) {
            DB::rollback();
            \Log::error("Error detaching employee {$employeeId} from role {$roleId}: " . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while detaching the employee'
            ], 500);
        }
    }

    /**
     * Attach (re-assign) an employee to a role
     *
     * @param int $roleId
     * @param int $employeeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function attachEmployee($roleId, $employeeId)
    {
        try {
            DB::beginTransaction();
            
            // Verify the role exists
            $role = Role::findOrFail($roleId);
            
            // Check if employee already has this role
            $hasRole = DB::table('model_has_roles')
                ->where('role_id', $roleId)
                ->where('model_id', $employeeId)
                ->where('model_type', 'App\\Employee')
                ->exists();
            
            if ($hasRole) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Employee already has this role'
                ], 400);
            }
            
            // Attach the employee to the role
            DB::table('model_has_roles')->insert([
                'role_id' => $roleId,
                'model_type' => 'App\\Employee',
                'model_id' => $employeeId
            ]);
            
            DB::commit();
            
            // Log the action
            \Log::info("Employee ID {$employeeId} attached to role ID {$roleId} by user " . auth()->id());
            
            return response()->json([
                'status' => 'success',
                'message' => 'Employee successfully re-assigned to role'
            ]);
            
        } catch (\Exception $e) {
            DB::rollback();
            \Log::error("Error attaching employee {$employeeId} to role {$roleId}: " . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while re-assigning the employee'
            ], 500);
        }
    }

    /**
     * Get available employees that can be assigned to a role
     *
     * @param int $roleId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableEmployees($roleId)
    {
        try {
            // Get employees not currently assigned to this role
            $availableEmployees = DB::table('employees')
                ->leftJoin('model_has_roles', function($join) use ($roleId) {
                    $join->on('employees.id', '=', 'model_has_roles.model_id')
                         ->where('model_has_roles.role_id', '=', $roleId)
                         ->where('model_has_roles.model_type', '=', 'App\\Employee');
                })
                ->whereNull('model_has_roles.model_id')
                ->whereNull('employees.deleted_at')
                ->select('employees.id', 'employees.name', 'employees.email', 'employees.full_name', 'employees.display_name', 'employees.image', 'employees.gender')
                ->orderBy('employees.full_name', 'asc')
                ->get();

            return response()->json([
                'status' => 'success',
                'data' => $availableEmployees
            ]);
        } catch (\Exception $e) {
            \Log::error("Error fetching available employees for role {$roleId}: " . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while fetching available employees'
            ], 500);
        }
    }

    /**
     * Bulk attach multiple employees to a role
     *
     * @param int $roleId
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkAttachEmployees($roleId)
    {
        $request = request();
        $employeeIds = $request->input('employee_ids', []);
        
        if (empty($employeeIds)) {
            return response()->json([
                'status' => 'error',
                'message' => 'No employees selected'
            ], 400);
        }

        // Validate role exists
        $role = Role::find($roleId);
        if (!$role) {
            return response()->json([
                'status' => 'error',
                'message' => 'Role not found'
            ], 404);
        }

        DB::beginTransaction();
        
        try {
            $attachedCount = 0;
            $errors = [];

            foreach ($employeeIds as $employeeId) {
                // Check if employee is already assigned to this role
                $existingAssignment = DB::table('model_has_roles')
                    ->where('role_id', $roleId)
                    ->where('model_id', $employeeId)
                    ->where('model_type', 'App\\Employee')
                    ->exists();

                if ($existingAssignment) {
                    $errors[] = "Employee ID {$employeeId} is already assigned to this role";
                    continue;
                }

                // Check if employee exists and is not deleted
                $employee = DB::table('employees')
                    ->where('id', $employeeId)
                    ->whereNull('deleted_at')
                    ->exists();

                if (!$employee) {
                    $errors[] = "Employee ID {$employeeId} not found or deleted";
                    continue;
                }

                // Attach employee to role
                DB::table('model_has_roles')->insert([
                    'role_id' => $roleId,
                    'model_type' => 'App\\Employee',
                    'model_id' => $employeeId
                ]);

                $attachedCount++;
            }

            DB::commit();

            $message = "Successfully attached {$attachedCount} employee(s) to the role";
            if (!empty($errors)) {
                $message .= ". Some errors occurred: " . implode(', ', $errors);
            }

            return response()->json([
                'status' => 'success',
                'message' => $message,
                'attached_count' => $attachedCount,
                'errors' => $errors
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            \Log::error("Error bulk attaching employees to role {$roleId}: " . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while attaching employees'
            ], 500);
        }
    }

    /**
     * Bulk detach employees from a role.
     *
     * @param int $roleId
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkDetachEmployees($roleId)
    {
        $request = request();
        $employeeIds = $request->input('employee_ids', []);
        
        if (empty($employeeIds)) {
            return response()->json([
                'status' => 'error',
                'message' => 'No employees selected'
            ], 400);
        }

        // Validate role exists
        $role = Role::find($roleId);
        if (!$role) {
            return response()->json([
                'status' => 'error',
                'message' => 'Role not found'
            ], 404);
        }

        DB::beginTransaction();
        
        try {
            $detachedCount = 0;
            $errors = [];

            foreach ($employeeIds as $employeeId) {
                // Check if employee is actually assigned to this role
                $existingAssignment = DB::table('model_has_roles')
                    ->where('role_id', $roleId)
                    ->where('model_id', $employeeId)
                    ->where('model_type', 'App\\Employee')
                    ->exists();

                if (!$existingAssignment) {
                    $errors[] = "Employee ID {$employeeId} is not assigned to this role";
                    continue;
                }

                // Detach employee from role
                DB::table('model_has_roles')
                    ->where('role_id', $roleId)
                    ->where('model_id', $employeeId)
                    ->where('model_type', 'App\\Employee')
                    ->delete();

                $detachedCount++;
            }

            DB::commit();

            $message = "Successfully detached {$detachedCount} employee(s) from the role";
            if (!empty($errors)) {
                $message .= ". Some errors occurred: " . implode(', ', $errors);
            }

            return response()->json([
                'status' => 'success',
                'message' => $message,
                'detached_count' => $detachedCount,
                'errors' => $errors
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            \Log::error("Error bulk detaching employees from role {$roleId}: " . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while detaching employees'
            ], 500);
        }
    }

    public function update(Request $request, $id)
    {






        try {
            DB::beginTransaction();

            Schema::disableForeignKeyConstraints();
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            PermissionAssign::where('role_id', $request->role_id)->delete();



            if ($request->module_id) {

                foreach ($request->module_id as $module) {

                    $assign = new PermissionAssign();
                    $assign->module_id = $module;
                    $assign->role_id = $request->role_id;
                    $assign->organization_id = Auth::user()->organization_id;
                    $assign->save();
                }
            }

        $role = Role::findOrFail($id);
        if($role) {
            //     $role->syncPermissions(Permission::all());
            //     return redirect()->route('roles.index');
            $this->validate($request, ['description' => 'required']);
            
            
            $role->description = $request->description;
            $role->save();
//            $permissions = $request->get('permissions', []);

            $permissions = collect($request->permissions)->filter(function($value, $key) {
                return  $value != null;
            })->toArray();



//            foreach ($request->permissions as $permission ) {
            foreach ($permissions as $key => $permission ) {

                if(Permission::where('name' , $permission)->where('guard_name' , 'employee')->doesntExist()){
                    Permission::create(['name' => $permission , 'organization_id' => config('organization_id')]);
                }
            }


            $role->syncPermissions($permissions);
            flash( $role->description . ' permissions has been updated.');
            DB::commit();

        } else {
            flash()->error( 'Role with id '. $id .' note found.');
        }

        return redirect()->back();
        } catch (\Exception $e) {
            DB::rollback();
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }
    public function destroy($id)
    {
        // find the role by id
        $role = Role::findOrFail($id);

        // delete the role
        $role->delete();
        $roles = Role::all();
        $permissions = Permission::all();
        return view('general::roles.index', compact('roles', 'permissions'))->with('success', 'Role has been deleted.');

        // redirect to the index page with success message
        return redirect()->route('general::roles.index')->with('success', 'Role has been deleted.');
    }

}

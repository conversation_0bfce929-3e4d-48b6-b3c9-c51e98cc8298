!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=tinymce.util.Tools.resolve("tinymce.util.Tools"),n={hasDimensions:function(e){return!1!==e.settings.image_dimensions},hasAdvTab:function(e){return!0===e.settings.image_advtab},getPrependUrl:function(e){return e.getParam("image_prepend_url","")},getClassList:function(e){return e.getParam("image_class_list")},hasDescription:function(e){return!1!==e.settings.image_description},hasImageTitle:function(e){return!0===e.settings.image_title},hasImageCaption:function(e){return!0===e.settings.image_caption},getImageList:function(e){return e.getParam("image_list",!1)},hasUploadUrl:function(e){return e.getParam("images_upload_url",!1)},hasUploadHandler:function(e){return e.getParam("images_upload_handler",!1)},getUploadUrl:function(e){return e.getParam("images_upload_url")},getUploadHandler:function(e){return e.getParam("images_upload_handler")},getUploadBasePath:function(e){return e.getParam("images_upload_base_path")},getUploadCredentials:function(e){return e.getParam("images_upload_credentials")}},a="undefined"!=typeof window?window:Function("return this;")(),i=function(e,t){for(var n=t!==undefined&&null!==t?t:a,i=0;i<e.length&&n!==undefined&&null!==n;++i)n=n[e[i]];return n},r=function(e,t){var n=e.split(".");return i(n,t)},o={getOrDie:function(e,t){var n=r(e,t);if(n===undefined||null===n)throw e+" not available on this browser";return n}};function l(){return new(o.getOrDie("FileReader"))}var s=tinymce.util.Tools.resolve("tinymce.util.Promise"),u=tinymce.util.Tools.resolve("tinymce.util.XHR"),c=function(e,t){return Math.max(parseInt(e,10),parseInt(t,10))},d={getImageSize:function(e,t){var n=document.createElement("img");function a(e,a){n.parentNode&&n.parentNode.removeChild(n),t({width:e,height:a})}n.onload=function(){a(c(n.width,n.clientWidth),c(n.height,n.clientHeight))},n.onerror=function(){a(0,0)};var i=n.style;i.visibility="hidden",i.position="fixed",i.bottom=i.left="0px",i.width=i.height="auto",document.body.appendChild(n),n.src=e},buildListItems:function(e,n,a){return function i(e,a){return a=a||[],t.each(e,function(e){var t={text:e.text||e.title};e.menu?t.menu=i(e.menu):(t.value=e.value,n(t)),a.push(t)}),a}(e,a||[])},removePixelSuffix:function(e){return e&&(e=e.replace(/px$/,"")),e},addPixelSuffix:function(e){return e.length>0&&/^[0-9]+$/.test(e)&&(e+="px"),e},mergeMargins:function(e){if(e.margin){var t=e.margin.split(" ");switch(t.length){case 1:e["margin-top"]=e["margin-top"]||t[0],e["margin-right"]=e["margin-right"]||t[0],e["margin-bottom"]=e["margin-bottom"]||t[0],e["margin-left"]=e["margin-left"]||t[0];break;case 2:e["margin-top"]=e["margin-top"]||t[0],e["margin-right"]=e["margin-right"]||t[1],e["margin-bottom"]=e["margin-bottom"]||t[0],e["margin-left"]=e["margin-left"]||t[1];break;case 3:e["margin-top"]=e["margin-top"]||t[0],e["margin-right"]=e["margin-right"]||t[1],e["margin-bottom"]=e["margin-bottom"]||t[2],e["margin-left"]=e["margin-left"]||t[1];break;case 4:e["margin-top"]=e["margin-top"]||t[0],e["margin-right"]=e["margin-right"]||t[1],e["margin-bottom"]=e["margin-bottom"]||t[2],e["margin-left"]=e["margin-left"]||t[3]}delete e.margin}return e},createImageList:function(e,t){var a=n.getImageList(e);"string"==typeof a?u.send({url:a,success:function(e){t(JSON.parse(e))}}):"function"==typeof a?a(t):t(a)},waitLoadImage:function(e,t,a){function i(){a.onload=a.onerror=null,e.selection&&(e.selection.select(a),e.nodeChanged())}a.onload=function(){t.width||t.height||!n.hasDimensions(e)||e.dom.setAttribs(a,{width:a.clientWidth,height:a.clientHeight}),i()},a.onerror=i},blobToDataUri:function(e){return new s(function(t,n){var a=new l;a.onload=function(){t(a.result)},a.onerror=function(){n(l.error.message)},a.readAsDataURL(e)})}},g={makeTab:function(e,t){return{title:"Advanced",type:"form",pack:"start",items:[{label:"Style",name:"style",type:"textbox",onchange:(a=e,function(e){var t=a.dom,i=e.control.rootControl;if(n.hasAdvTab(a)){var r=i.toJSON(),o=t.parseStyle(r.style);i.find("#vspace").value(""),i.find("#hspace").value(""),((o=d.mergeMargins(o))["margin-top"]&&o["margin-bottom"]||o["margin-right"]&&o["margin-left"])&&(o["margin-top"]===o["margin-bottom"]?i.find("#vspace").value(d.removePixelSuffix(o["margin-top"])):i.find("#vspace").value(""),o["margin-right"]===o["margin-left"]?i.find("#hspace").value(d.removePixelSuffix(o["margin-right"])):i.find("#hspace").value("")),o["border-width"]&&i.find("#border").value(d.removePixelSuffix(o["border-width"])),i.find("#style").value(t.serializeStyle(t.parseStyle(t.serializeStyle(o))))}})},{type:"form",layout:"grid",packV:"start",columns:2,padding:0,defaults:{type:"textbox",maxWidth:50,onchange:function(n){t(e,n.control.rootControl)}},items:[{label:"Vertical space",name:"vspace"},{label:"Border width",name:"border"},{label:"Horizontal space",name:"hspace"},{label:"Border style",type:"listbox",name:"borderStyle",width:90,maxWidth:90,onselect:function(n){t(e,n.control.rootControl)},values:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]}]}]};var a}},m=function(e,t){e.state.set("oldVal",e.value()),t.state.set("oldVal",t.value())},f=function(e,t){var n=e.find("#width")[0],a=e.find("#height")[0],i=e.find("#constrain")[0];n&&a&&i&&t(n,a,i.checked())},p=function(e,t,n){var a=e.state.get("oldVal"),i=t.state.get("oldVal"),r=e.value(),o=t.value();n&&a&&i&&r&&o&&(r!==a?(o=Math.round(r/a*o),isNaN(o)||t.value(o)):(r=Math.round(o/i*r),isNaN(r)||e.value(r))),m(e,t)},h=function(e){f(e,p)},b={createUi:function(){var e=function(e){h(e.control.rootControl)};return{type:"container",label:"Dimensions",layout:"flex",align:"center",spacing:5,items:[{name:"width",type:"textbox",maxLength:5,size:5,onchange:e,ariaLabel:"Width"},{type:"label",text:"x"},{name:"height",type:"textbox",maxLength:5,size:5,onchange:e,ariaLabel:"Height"},{name:"constrain",type:"checkbox",checked:!0,text:"Constrain proportions"}]}},syncSize:function(e){f(e,m)},updateSize:h},v=function(e){e.meta=e.control.rootControl.toJSON()},y=function(e,a){var i=[{name:"src",type:"filepicker",filetype:"image",label:"Source",autofocus:!0,onchange:function(a){var i,r,o,l,s,u,c,g,m;r=e,u=(i=a).meta||{},c=i.control,g=c.rootControl,(m=g.find("#image-list")[0])&&m.value(r.convertURL(c.value(),"src")),t.each(u,function(e,t){g.find("#"+t).value(e)}),u.width||u.height||(o=r.convertURL(c.value(),"src"),l=n.getPrependUrl(r),s=new RegExp("^(?:[a-z]+:)?//","i"),l&&!s.test(o)&&o.substring(0,l.length)!==l&&(o=l+o),c.value(o),d.getImageSize(r.documentBaseURI.toAbsolute(c.value()),function(e){e.width&&e.height&&n.hasDimensions(r)&&(g.find("#width").value(e.width),g.find("#height").value(e.height),b.updateSize(g))}))},onbeforecall:v},a];return n.hasDescription(e)&&i.push({name:"alt",type:"textbox",label:"Image description"}),n.hasImageTitle(e)&&i.push({name:"title",type:"textbox",label:"Image Title"}),n.hasDimensions(e)&&i.push(b.createUi()),n.getClassList(e)&&i.push({name:"class",type:"listbox",label:"Class",values:d.buildListItems(n.getClassList(e),function(t){t.value&&(t.textStyle=function(){return e.formatter.getCssText({inline:"img",classes:[t.value]})})})}),n.hasImageCaption(e)&&i.push({name:"caption",type:"checkbox",label:"Caption"}),i},x={makeTab:function(e,t){return{title:"General",type:"form",items:y(e,t)}},getGeneralItems:y},w=function(){return o.getOrDie("URL")},S=function(e){return w().createObjectURL(e)},U=function(e){w().revokeObjectURL(e)},T=tinymce.util.Tools.resolve("tinymce.ui.Factory"),C=function(){},I=function(e,t){return e?e.replace(/\/$/,"")+"/"+t.replace(/^\//,""):t};function P(e){var n=function(t,n,a,i){var r,l;(r=new function(){return new(o.getOrDie("XMLHttpRequest"))}).open("POST",e.url),r.withCredentials=e.credentials,r.upload.onprogress=function(e){i(e.loaded/e.total*100)},r.onerror=function(){a("Image upload failed due to a XHR Transport error. Code: "+r.status)},r.onload=function(){var t;r.status<200||r.status>=300?a("HTTP Error: "+r.status):(t=JSON.parse(r.responseText))&&"string"==typeof t.location?n(I(e.basePath,t.location)):a("Invalid JSON: "+r.responseText)},(l=new FormData).append("file",t.blob(),t.filename()),r.send(l)};return e=t.extend({credentials:!1,handler:n},e),{upload:function(t){return e.url||e.handler!==n?(a=t,i=e.handler,new s(function(e,t){try{i(a,e,t,C)}catch(n){t(n.message)}})):s.reject("Upload url missing from the settings.");var a,i}}}var L=function(e){return function(t){var a=T.get("Throbber"),i=t.control.rootControl,r=new a(i.getEl()),o=t.control.value(),l=S(o),s=P({url:n.getUploadUrl(e),basePath:n.getUploadBasePath(e),credentials:n.getUploadCredentials(e),handler:n.getUploadHandler(e)}),u=function(){r.hide(),U(l)};return r.show(),d.blobToDataUri(o).then(function(t){var n=e.editorUpload.blobCache.create({blob:o,blobUri:l,name:o.name?o.name.replace(/\.[^\.]+$/,""):null,base64:t.split(",")[1]});return s.upload(n).then(function(e){var t=i.find("#src");return t.value(e),i.find("tabpanel")[0].activateTab(0),t.fire("change"),u(),e})})["catch"](function(t){e.windowManager.alert(t),u()})}},_=".jpg,.jpeg,.png,.gif",N={makeTab:function(e){return{title:"Upload",type:"form",layout:"flex",direction:"column",align:"stretch",padding:"20 20 20 20",items:[{type:"container",layout:"flex",direction:"column",align:"center",spacing:10,items:[{text:"Browse for an image",type:"browsebutton",accept:_,onchange:L(e)},{text:"OR",type:"label"}]},{text:"Drop an image here",type:"dropzone",accept:_,height:100,onchange:L(e)}]}}};function A(e){var a=function(e,t){if(n.hasAdvTab(e)){var a=e.dom,i=t.toJSON(),r=a.parseStyle(i.style);r=d.mergeMargins(r),i.vspace&&(r["margin-top"]=r["margin-bottom"]=d.addPixelSuffix(i.vspace)),i.hspace&&(r["margin-left"]=r["margin-right"]=d.addPixelSuffix(i.hspace)),i.border&&(r["border-width"]=d.addPixelSuffix(i.border)),i.borderStyle&&(r["border-style"]=i.borderStyle),t.find("#style").value(a.serializeStyle(a.parseStyle(a.serializeStyle(r))))}};function i(i){var r,o,l,s,u={},c=e.dom;function m(){var n,i;b.updateSize(r),a(e,r),(u=t.extend(u,r.toJSON())).alt||(u.alt=""),u.title||(u.title=""),""===u.width&&(u.width=null),""===u.height&&(u.height=null),u.style||(u.style=null),u={src:u.src,alt:u.alt,title:u.title,width:u.width,height:u.height,style:u.style,caption:u.caption,"class":u["class"]},e.undoManager.transact(function(){if(u.src){if(""===u.title&&(u.title=null),o?c.setAttribs(o,u):(u.id="__mcenew",e.focus(),e.selection.setContent(c.createHTML("img",u)),o=c.get("__mcenew"),c.setAttrib(o,"id",null)),e.editorUpload.uploadImagesAuto(),!1===u.caption&&c.is(o.parentNode,"figure.image")&&(n=o.parentNode,c.insertAfter(o,n),c.remove(n)),!0!==u.caption)d.waitLoadImage(e,u,o);else if(!c.is(o.parentNode,"figure.image")){i=o,o=o.cloneNode(!0),(n=c.create("figure",{"class":"image"})).appendChild(o),n.appendChild(c.create("figcaption",{contentEditable:!0},"Caption")),n.contentEditable=!1;var t=c.getParent(i,function(t){return e.schema.getTextBlockElements()[t.nodeName]});t?c.split(t,i,n):c.replace(n,i),e.selection.select(n)}}else if(o){var a=c.is(o.parentNode,"figure.image")?o.parentNode:o;c.remove(a),e.focus(),e.nodeChanged(),c.isEmpty(e.getBody())&&(e.setContent(""),e.selection.setCursorLocation())}})}if(o=e.selection.getNode(),(l=c.getParent(o,"figure.image"))&&(o=c.select("img",l)[0]),o&&("IMG"!==o.nodeName||o.getAttribute("data-mce-object")||o.getAttribute("data-mce-placeholder"))&&(o=null),o&&(u={src:c.getAttrib(o,"src"),alt:c.getAttrib(o,"alt"),title:c.getAttrib(o,"title"),"class":c.getAttrib(o,"class"),width:c.getAttrib(o,"width"),height:c.getAttrib(o,"height"),caption:!!l}),i&&(s={type:"listbox",label:"Image list",name:"image-list",values:d.buildListItems(i,function(t){t.value=e.convertURL(t.value||t.url,"src")},[{text:"None",value:""}]),value:u.src&&e.convertURL(u.src,"src"),onselect:function(e){var t=r.find("#alt");(!t.value()||e.lastControl&&t.value()===e.lastControl.text())&&t.value(e.control.text()),r.find("#src").value(e.control.value()).fire("change")},onPostRender:function(){s=this}}),n.hasAdvTab(e)||n.hasUploadUrl(e)||n.hasUploadHandler(e)){var f=[x.makeTab(e,s)];n.hasAdvTab(e)&&(o&&(o.style.marginLeft&&o.style.marginRight&&o.style.marginLeft===o.style.marginRight&&(u.hspace=d.removePixelSuffix(o.style.marginLeft)),o.style.marginTop&&o.style.marginBottom&&o.style.marginTop===o.style.marginBottom&&(u.vspace=d.removePixelSuffix(o.style.marginTop)),o.style.borderWidth&&(u.border=d.removePixelSuffix(o.style.borderWidth)),u.borderStyle=o.style.borderStyle,u.style=e.dom.serializeStyle(e.dom.parseStyle(e.dom.getAttrib(o,"style")))),f.push(g.makeTab(e,a))),(n.hasUploadUrl(e)||n.hasUploadHandler(e))&&f.push(N.makeTab(e)),r=e.windowManager.open({title:"Insert/edit image",data:u,bodyType:"tabpanel",body:f,onSubmit:m})}else r=e.windowManager.open({title:"Insert/edit image",data:u,body:x.getGeneralItems(e,s),onSubmit:m});b.syncSize(r)}return{open:function(){d.createImageList(e,i)}}}var k=function(e){e.addCommand("mceImage",A(e).open)},D=function(e){return function(n){for(var a,i,r=n.length,o=function(t){t.attr("contenteditable",e?"true":null)};r--;)a=n[r],(i=a.attr("class"))&&/\bimage\b/.test(i)&&(a.attr("contenteditable",e?"false":null),t.each(a.getAll("figcaption"),o))}},R=function(e){e.on("preInit",function(){e.parser.addNodeFilter("figure",D(!0)),e.serializer.addNodeFilter("figure",D(!1))})},z=function(e){e.addButton("image",{icon:"image",tooltip:"Insert/edit image",onclick:A(e).open,stateSelector:"img:not([data-mce-object],[data-mce-placeholder]),figure.image"}),e.addMenuItem("image",{icon:"image",text:"Image",onclick:A(e).open,context:"insert",prependToContext:!0})};e.add("image",function(e){R(e),z(e),k(e)})}();
# JobSeeker Module – Jobs.af Per-Rule Filter Support

> **Scope:** Allow admins to specify categories, provinces, search term, companies, and experience-levels *per* `SyncJobsAfCommand` schedule rule via the existing Bootstrap-5 modal. The command will fall back to legacy defaults when no filters are stored.

---
## 📅 Epic / Phase Breakdown

### Phase 0 – Database Foundations
1. **`provinces` master table**  
   • SQL file: `YYYYMMDD_HHMMSS_create_provinces_table.sql` (34 provinces seeded).  
2. **`command_schedule_filters` table**  
   • SQL file: `YYYYMMDD_HHMMSS_create_command_schedule_filters_table.sql`  
   • Columns: `id, schedule_rule_id FK, categories JSON, locations JSON, companies JSON, experience_levels JSON, search_term VARCHAR(255), work_type VARCHAR(50), timestamps`.
3. **Default seed**  
   • SQL file: `YYYYMMDD_HHMMSS_seed_default_jobsaf_filters.sql` – inserts one row replicating the config defaults to preserve fallback behaviour.

### Phase 1 – Domain Layer
1. **Models & Casts**  
   • `Province` (if absent)  
   • `CommandScheduleFilter` with `$casts` to `array` for all JSON columns.
2. **Repository**  
   • `FilterRepository` providing CRUD + `getByRule($ruleId)` + `getFallback()` + cache.

### Phase 2 – Backend Integration
1. **Controller End-Points**  
   • Extend existing `store`, `update`, `show` routes in `CommandScheduleController` to handle `filters[...]` payload.  
   • New route `admin.jobseeker.command_schedule.filters.defaults` – returns JSON of categories & provinces for modal population.
2. **JobsAfService Refactor**  
   • Inject `FilterRepository`.  
   • Before composing API request body:  
     – `filters = repo->getByRule($ruleId) ?: repo->getFallback()`  
     – Merge with hard defaults where individual arrays empty.

### Phase 3 – Front-End / UX
1. **Modal Enhancements** (`resources/views/modules/jobseeker/admin/command_schedule/index.blade.php`)  
   • Hidden accordion **Jobs.af Filters** revealed when `#ruleCommand` == `jobseeker:sync-jobs-af`.  
   • Components (all Select2 multi-select):  
     – **Categories** (preloaded static list).  
     – **Locations** (AJAX to provinces endpoint).  
     – **Companies** (free-text tags).  
     – **Experience Levels** (select).  
     – **Search Term** input.  
2. **JS Workflow**  
   • On modal open (create): fetch defaults → pre-fill.  
   • On modal open (edit): fetch stored filters → pre-select.  
   • On submit: pack chosen values into JSON inside `FormData`.
3. **Visual Integrity**  
   • Dark-mode colours via CSS variables already defined in `app.blade.php`.  
   • Use existing toastr & loader patterns.

### Phase 4 – Testing & QA
1. **Unit tests**  
   • Repository returns correct data & fallbacks.  
   • JobsAfService builds correct API payload given various scenarios.  
2. **Browser tests**  
   • Modal show/hide triggers.  
   • Select2 multi-select UX on desktop & mobile.  
   • Validation errors handled gracefully.
3. **Regression**  
   • Other schedule rules continue to save & load unaffected.  
   • SyncJobsAfCommand without filters uses fallback and logs informational message.

### Phase 5 – Deployment Checklist
- Execute SQL files in order (Provinces → Filters → Seed).  
- `config/jobseeker.php` – remove `jobs_af_default_filters` array after verifying runtime uses DB.  
- Clear config & route caches.  
- Tag new release and update API documentation.

---
## Outstanding Decisions / Risks
- **Companies / Experience-levels master data** not yet formalised; currently free-text tags may lead to duplicates.  
- Performance of JSON queries acceptable for low volume; monitor once live.  
- Future: move Categories to master table + pivot for validation & reporting. 
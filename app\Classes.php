<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Translatable;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class Classes extends Model
{
    use Translatable;


    use SoftDeletes;




    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'classes';

    /**
     * The database primary key value.
     *
     * @var string
     */
    protected $primaryKey = 'id';



    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['class_code', 'center_id', 'organization_id', 'status','subject_id','classes_per_month'];

    public $translationModel = 'App\ClassTranslation';

    public $translatedAttributes = array('name' , 'description');


    // Define the scope for permission-based filtering






    // In the Classes model

    public function studentProgramLevels()
    {
        return $this->hasMany(StudentProgramLevel::class, 'class_id');
    }



    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
    }

    
    public function onlineExam(){
        return $this->hasMany('App\OnlineExam', 'class_id', 'id');
    }

    public function center()
    {
        return $this->belongsTo('App\Center');
    }
    

    public function teachers()
    {

        //return $this->hasMany('App\ClassTeacher', 'class_id'); //->withPivot('start_date' , 'end_date'); /* edited by hayat  */
        return $this->belongsToMany(Employee::class, 'class_teachers','class_id','employee_id')->wherePivot('deleted_at', null)->withTimestamps()->withPivot('id')
            ;
    }

    /**
     * Calculate the average attendance percentage for students within a specific month and year.
     *
     * @param  int|string $month The month for the attendance calculation.
     * @param  int|string $year The year for the attendance calculation.
     * @return float|null The average attendance percentage or null if no students are found.
     */
    public function averageAttendancePercentage($month, $year) {
        // Initialize the total attendance percentage.
        $totalAttendancePercentage = 0;

        // Retrieve students with an active 'HefzPlan' for the given month and year.
        $studentsWithActivePlans = $this->students()
            ->whereHas('hefz_plans', function ($query) use ($month, $year) {


                $query->whereMonth('start_date', $month)
                    ->whereYear('start_date', $year)
                    ->where('status','active');

//                $query->where(function ($query) use ($month, $year) {
//                    // Check the start_date column
//                    $query->whereMonth('start_date', $month)
//                        ->whereYear('start_date', $year)
//                        // Check the created_at column
//                        ->orWhere(function ($query) use ($month, $year) {
//                            $query->whereMonth('created_at', $month)
//                                ->whereYear('created_at', $year);
//                        })
//                        // Check the updated_at column
//                        ->orWhere(function ($query) use ($month, $year) {
//                            $query->whereMonth('updated_at', $month)
//                                ->whereYear('updated_at', $year);
//                        })
//                        // Check the plan_year_and_month column
//                        ->orWhere(function ($query) use ($month, $year) {
//                            // Format the month to ensure it's in MM format
//                            $formattedMonth = str_pad($month, 2, '0', STR_PAD_LEFT);
//                            // Use the SQL LIKE operator to match the year and month in YYYY-MM format
//                            $query->where('plan_year_and_month', 'LIKE', "{$year}-{$formattedMonth}");
//                        });
//                });


            })
            ->get();

        // Count the students with active plans.
        $activeStudentsCount = $studentsWithActivePlans->count();

        // If there are no students with active plans, return null.
        if ($activeStudentsCount === 0) {
            return null;
        }

        // Calculate the total attendance percentage for all active students.
        $totalAttendancePercentage = $studentsWithActivePlans
            ->sum(function ($student) use ($month, $year) {
                return $student->attendancePercentageForMonth($month, $year);
            });

        // Calculate and return the average attendance percentage.
        return round($totalAttendancePercentage / $activeStudentsCount, 2);
    }
    public function ijazasanadAverageAttendancePercentage($month, $year) {

        // Retrieve students with an active 'HefzPlan' for the given month and year.
        $studentsWithActivePlans = $this->students()
            ->whereHas('ijazasanad_memorization_plans', function ($query) use ($month, $year) {


                $query->whereMonth('start_date', $month)
                    ->whereYear('start_date', $year)
                    ->where('status','active');


            })
            ->get();

        // Count the students with active plans.
        $activeStudentsCount = $studentsWithActivePlans->count();

        // If there are no students with active plans, return null.
        if ($activeStudentsCount === 0) {
            return null;
        }

        // Calculate the total attendance percentage for all active students.
        $totalAttendancePercentage = $studentsWithActivePlans
            ->sum(function ($student) use ($month, $year) {
                return $student->ijazasanadAttendancePercentageForMonth($month, $year);
            });

        // Calculate and return the average attendance percentage.
        return round($totalAttendancePercentage / $activeStudentsCount, 2);
    }


    public function timetable()
    {

        return $this->hasOne(ClassTimetable::class,'class_id');
    }




    public function currentTeachers()
    {
        return $this->belongsToMany(Employee::class, 'class_teachers', 'class_id')->where('end_date', null);
    }
    public function students()
    {

        return $this->belongsToMany('App\Student', 'class_students', 'class_id')
            ->whereNull('class_students.deleted_at') // when an existing student status changes to new_admission from workplace/admission/students route, then we need to make that student unavailable in the related class. there I added the deleted_at column
            ->whereNull("class_students.end_date")
            ->wherePivot('deleted_at', '=', null)
            ->withPivot('start_date','end_date','class_id','student_id','transfer_at','transfer_from','added_at')
            ->withTimestamps();

    }

    public function studentsAtDate($date)
    {
        return $this->belongsToMany('App\Student', 'class_students', 'class_id')->where("start_date", "<", $date)->where(function ($q) use ($date) {
            $q->where("end_date", ">=", $date)->orWhere("end_date", null);
        })->withPivot('start_date')->get();
    }

    public function programs()
    {
        return $this->belongsToMany('App\Program', 'class_programs', 'class_id','program_id')->withPivot(['program_level_id','program_id'])->withTimestamps();
    }

    public function getCurrentTeacherAttribute()
    {
        // need to check if the user is a 
        if (in_array(auth()->user()->id, $this->teachers()->pluck('employee_id')->toArray())) {
            return auth()->user();
        }
        return null;
    }


    public function sections()
    {
        return $this->hasMany('App\Section', 'id', 'section_id');
    }

    public function subject()
    {
        return $this->belongsTo('App\Subject' , 'subject_id');

    }

//    public function subject()
//    {
//        return $this->hasOne('App\Subject', 'id', 'subject_id');
//    }



    public function classSections()
    {
        return $this->hasMany('App\ClassSection', 'class_id', 'id');
    }


    // New Classes added by Hayat

    /**
     * Class EducationalReports
     * @return HasMany
     */
    public function reports(): HasMany
    {
        return $this->hasMany(ClassReport::class,'class_id','id');
    }

    public function hefz_plans()
    {
        return $this->hasMany('App\StudentHefzPlan','class_id');
    }
    public function ijazasanad_memorization_plans()
    {
        return $this->hasMany('App\IjazasanadMemorizationPlan','class_id');
    }


    public function hefz_reports()
    {
        return $this->hasMany(StudentHefzReport::class,'class_id');
    }
    public function ijazaMemorizationReport()
    {
        return $this->hasMany(StudentIjazasanadMemorizationReport::class,'class_id');
    }
    public function ijazaRevisionReport()
    {
        return $this->hasMany(StudentIjazasanadRevisionReport::class,'class_id');
    }

    public function completedHefzReport()
    {
        return $this->hasMany('App\StudentHefzReport','class_id','id')->where(function($q) {
            $q->whereNotNull('hefz_from_surat')
                ->whereNotNull('hefz_from_ayat')
                ->whereNotNull('hefz_to_surat')
                ->whereNotNull('hefz_to_ayat');

        });
    }
    public function completedRevisionReport()
    {
        return $this->hasMany('App\StudentRevisionReport','class_id','id')->where(function($q) {
            $q->whereNotNull('revision_from_surat')
                ->whereNotNull('revision_from_ayat')
                ->whereNotNull('revision_to_surat')
                ->whereNotNull('revision_to_ayat');

        });
    }
    public function revision_reports()
    {
        return $this->hasMany(StudentRevisionReport::class,'class_id');
    }


    public function revision_plans()
    {
        return $this->hasMany(StudentRevisionPlan::class,'class_id');
    }

    public function ijazasanad_revision_plans()
    {
        return $this->hasMany(IjazasanadRevisionPlan::class,'class_id');
    }


    public function scopeOfName($query, $name)
    {



        return $query->whereRaw('select name from class_translations where name like '%'.$name.'%'');
    }






}

<?php

namespace App\Rules;

use App\User;
use Illuminate\Contracts\Validation\Rule;

/**
 * @property $totDaysRequested
 * @property $approvedleavesByType
 * @property $leaveTypeId
 */
class CheckEmployeeLeave implements Rule
{
    /**
     * @var array
     */
    private $totLeaveDaysperType;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($totDaysRequested,$ApprovedleavesByType,$leaveTypeId)
    {
        $this->totDaysRequested = $totDaysRequested;
        $this->approvedleavesByType = $ApprovedleavesByType;
        $this->leaveTypeId = $leaveTypeId;
        $this->totLeaveDaysperType =  \DB::select('call getemployeetotleavesByLeaveType(?,?,?)',[auth()->user()->id,2022,$leaveTypeId]);

    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if(($this->approvedleavesByType+$this->totDaysRequested) > $this->totLeaveDaysperType[0]->totLeaveDays)
        {
            return false;
        }else{
            return true;
        }
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Remaining total leaves for this leave type are: '.($this->totLeaveDaysperType[0]->totLeaveDays - $this->approvedleavesByType.', while you have requested: ' . $this->totDaysRequested);
    }
}

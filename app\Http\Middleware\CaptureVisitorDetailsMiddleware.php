<?php

namespace App\Http\Middleware;

use App\WebsiteVisitor;
use Closure;

class CaptureVisitorDetailsMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        return $next($request);
    }



    // capture the visitor details per day per ip when the response is on its way to the browser after we lost our chance to modify the response or the request
    public function terminate($request,$response){

        $ip = hash('sha512', $request->ip());
//        if (WebsiteVisitor::where('date', today())->where('ip', $ip)->where('url',$request->url())->count() < 1)
        if (WebsiteVisitor::where('date', today())->where('ip', $ip)->count() < 1)
        {
            WebsiteVisitor::create([
                'date' => today(),
                'ip' => $ip,
                'url' => $request->url(),
                'user_id' => auth()->user()->id
            ]);
        }


    }
}

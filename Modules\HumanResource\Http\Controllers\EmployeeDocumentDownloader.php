<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Document;
use App\Employee;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;

class EmployeeDocumentDownloader extends Controller
{

    public function __invoke(Request $request,$file_name)
    {
        $urlFilename = explode('-',$file_name);

        $file = public_path() . '/uploads/staff/'.$urlFilename[0].'/' . $urlFilename[1];



        if (file_exists($file)) {
            return response()->download($file);
        }
    }
}

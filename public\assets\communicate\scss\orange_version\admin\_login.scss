body.login {
	background-color: $primary-color2;
	.footer_area {
		background: transparent;
		border: 0;
		@media (max-width: 1199px) {
			text-align: center;
		}
		a {
			color: $white;
		}
		p{
			color: $white;
		}
	}
}

.login-area {
	a {
		color: $white;
		&:hover {
			opacity: .8;
		}
	}
	.login-height {
		min-height: 95vh;
	}
	.form-wrap {
		position: relative;
		z-index: 1;
		padding: 50px 70px;
		&:after{
			position: absolute;
			content: "";
			width: 100%;
			height: 100%;
			top: 0;
			left: 0;
			background-color: $primary-color2;
			z-index: -1;
			opacity: .5;
			box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2)
		}
		@media (max-width: 1199px) and (min-width: 992px) {
			padding: 50px 20px;
		}
		@media (max-width: 480px) {
			padding: 50px 20px;
		}
	}
	.logo-container {
	}
	h5 {
		margin-top: 40px;
		margin-bottom: 25px;
		color: $white;
		letter-spacing: 2px;
		font-size: 14px;
		font-weight: 700;
	}
	.form-group {
		.form-control {
			color: $white;
			border: 0px;
			border-bottom: 1px solid rgba(247, 247, 255, 0.2);
			border-radius: 0px;
			background: transparent!important;
			padding: 0px 30px;
			font-size: 12px;
			font-weight: 400;
			letter-spacing: 1px;
			&:focus {
				outline: none;
				box-shadow: none;
			}
			@include placeholder {
				position: relative;
				left: 0px;
				top: 0px;
				font-size: 12px;
				font-weight: 400;
				text-transform: uppercase;
				color: $white;
				letter-spacing: 1px;
			}
			
		}
		
		a {
			font-size: 12px;
			font-weight: 700;
		}
		i {
			color: $white;
			display: inline-block;
			position: relative;
			top: 6px;
			left: 14px;
			font-size: 12px;
			font-weight: 400;
		}
		::placeholder{
			color: $white !important;
		}
		input:-internal-autofill-selected {
			color: $white !important;
		}
		
	}
	.checkbox {
		input {
			margin-right: 6px;
		}
	}
	/*Checkboxes styles*/
	input[type="checkbox"] {
		display: none;
	}
	input[type="checkbox"] + label {
		display: block;
		position: relative;
		padding-left: 25px;
		margin-bottom: 20px;
		font: 12px/20px $primary-font;
		color: $white;
		cursor: pointer;
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
	}
	input[type="checkbox"] + label:last-child {
		margin-bottom: 0;
	}
	input[type="checkbox"] + label:before {
		content: '';
		display: block;
		width: 12px;
		height: 12px;
		border: 2px solid $white;
		border-radius: 50px;
		position: absolute;
		left: 0;
		top: 4px;
		opacity: .6;
		@include transition();
	}
	input[type="checkbox"]:checked + label:before {
		width: 8px;
		top: 1px;
		left: 5px;
		border-radius: 0px;
		opacity: 1;
		border-top-color: transparent;
		border-left-color: transparent;
		-webkit-transform: rotate(45deg);
		transform: rotate(45deg);
	}
	.primary-btn.fix-gr-bg {
		background-color: $primary-color2;
		box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
		color: #ffffff;
		background-size: 200% auto;
		-webkit-transition: all 0.4s ease 0s;
		-moz-transition: all 0.4s ease 0s;
		-o-transition: all 0.4s ease 0s;
		transition: all 0.4s ease 0s;
	}
}
.login-area .primary-btn.fix-gr-bg{
	box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
<?php

namespace Modules\ExaminationCertification\Http\Controllers;

use App\AttendanceOption;
use App\EvaluationSchemaOption;
use App\MoshafJuz;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Modules\ExaminationCertification\Http\Requests\ClassReportStaatisticsRequest;
use Modules\ExaminationCertification\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class StudentsAttendanceReportsController extends Controller
{

    public function __construct()
    {
//        $this->middleware('writeCurrentClassReportOnly', ['only' => ['create']]);
    }


    public function __invoke(Request $request)
    {


        DB::connection()->enableQueryLog();


        $students = Student::has('hefz')->with('joint_classes.programs')->with('hefz')->with('joint_classes.hefz_plans')->with('revision')
            ->get();


        return \Yajra\DataTables\DataTables::of($students)
            ->addIndexColumn()
            ->addColumn('name', function ($studentDetails) use ($request) {



                return $studentDetails->full_name;



            })
            ->addColumn('age', function ($studentDetails) use ($request) {


                return $studentDetails->age;
            })
            ->addColumn('nationality', function ($studentDetails) use ($request) {

                return $studentDetails->nationality;


            })
            ->addColumn('program', function ($studentDetails) use ($request) {

                $program = $studentDetails->joint_classes->map(function ($classes) {

                    return $classes->programs->first()->title;
                });

                return $program->unique()[0];


            })
            ->addColumn('lastSurat', function ($studentDetails) use ($request) { // get the last surat for hefz and revision
                $surats = MoshafSurah::all();
                $revisionLastSurat = '';
                foreach ($surats as $key => $surat)
                {

                    if ($studentDetails->hefz->last()->hefz_to_surat == $surat->id) {
                        $hefzLastSurat =  $surat->name;
                    }

                }

//                foreach ($surats as $key => $surat)
//                {
//
//                    if ($studentDetails->revision->last()->revision_to_surat == $surat->id) {
//                        $revisionLastSurat =  $surat->name;
//                    }
//
//                }





                $lastSurats = '';
                $lastSurats .= is_null($hefzLastSurat) ? '' : '<span class="badge badge-primary" ><code style="color: white">'. $hefzLastSurat.' </code></span><br>';
//                $lastSurats .= is_null($revisionLastSurat) ? '' :'<span class="badge badge-primary" ><code style="color: white">[ revision => '. $revisionLastSurat.' ]</code></span>';
                return $lastSurats;

            })
            ->addColumn('noOfJuz', function ($studentDetails) use ($request) { // number of juz



                $pageCount = 0;
                $juz = [];
                foreach($studentDetails->hefz as $key => $reports){


                    $numberofPages =DB::select( DB::raw("select *,abs(first_page-last_page) as pageCount
         from
         (select id,page_number as first_page
                   from moshaf_pages
                   where (surah_id = :startSurahId and first_ayah <= :startAyah)
                   order by page_number desc
                   limit 1) A
                  INNER JOIN
    (select id,page_number as last_page
                   from moshaf_pages
                   where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                   order by page_number desc
                   limit 1) B
on A.id <> B.id"), array(
                        'startSurahId' => $reports->hefz_from_surat,
                        'startAyah' => $reports->hefz_from_ayat,
                        'lastSurahId' => $reports->hefz_to_surat,
                        'lastAyah' => $reports->hefz_to_ayat,
                        'lastAyah2' => $reports->hefz_to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
                    ));


                    $juz[$reports->from_surat_juz_id] = $reports->from_surat_juz_id;
                    $juz[$reports->to_surat_juz_id] = $reports->to_surat_juz_id;

                    $pageCount += count(range($numberofPages[0]->first_page,$numberofPages[0]->last_page));






                }
                $juz = Arr::sort($juz);
                $juz = implode(',',$juz);

                $pageCount =   $pageCount/20;

                // A standard quran chapter( Sephara) contains almost 20 pages and we consider that here according to the top management decision ( Dr Nashwan)
                   return '<span class="ui icon" data-toggle="tooltip"  title="'.$juz.'"  data-placement="right" >'.$pageCount.'</span>
                       <hr><span  class="badge badge-info">'.number_format(($pageCount*100)/604,2).'% of Quran</span>';

            })
            ->addColumn('level', function ($studentDetails) use ($request) {
                return 'work in progress';

            })
            ->rawColumns(['noOfJuz','lastSurat'])

            ->make(true);


    }


}

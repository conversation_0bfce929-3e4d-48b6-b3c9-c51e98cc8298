!function(){"use strict";var n,e,t,r,o,i,u,a=function(n){return function(){return n}},c={noop:function(){},noarg:function(n){return function(){return n()}},compose:function(n,e){return function(){return n(e.apply(null,arguments))}},constant:a,identity:function(n){return n},tripleEquals:function(n,e){return n===e},curry:function(n){for(var e=new Array(arguments.length-1),t=1;t<arguments.length;t++)e[t-1]=arguments[t];return function(){for(var t=new Array(arguments.length),r=0;r<t.length;r++)t[r]=arguments[r];var o=e.concat(t);return n.apply(null,o)}},not:function(n){return function(){return!n.apply(null,arguments)}},die:function(n){return function(){throw new Error(n)}},apply:function(n){return n()},call:function(n){n()},never:a(!1),always:a(!0)},s={contextmenu:c.constant("contextmenu"),touchstart:c.constant("touchstart"),touchmove:c.constant("touchmove"),touchend:c.constant("touchend"),gesturestart:c.constant("gesturestart"),mousedown:c.constant("mousedown"),mousemove:c.constant("mousemove"),mouseout:c.constant("mouseout"),mouseup:c.constant("mouseup"),mouseover:c.constant("mouseover"),focusin:c.constant("focusin"),keydown:c.constant("keydown"),input:c.constant("input"),change:c.constant("change"),focus:c.constant("focus"),click:c.constant("click"),transitionend:c.constant("transitionend"),selectstart:c.constant("selectstart")},f=function(n){var e,t=!1;return function(){return t||(t=!0,e=n.apply(null,arguments)),e}},l=function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};var r=function(n){return Number(e.replace(t,"$"+n))};return m(r(1),r(2))},d=function(){return m(0,0)},m=function(n,e){return{major:n,minor:e}},p={nu:m,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?d():l(n,t)},unknown:d},g="Edge",h="Chrome",v="Opera",y="Firefox",b="Safari",w=function(n,e){return function(){return e===n}},S=function(n){var e=n.current;return{current:e,version:n.version,isEdge:w(g,e),isChrome:w(h,e),isIE:w("IE",e),isOpera:w(v,e),isFirefox:w(y,e),isSafari:w(b,e)}},x={unknown:function(){return S({current:undefined,version:p.unknown()})},nu:S,edge:c.constant(g),chrome:c.constant(h),ie:c.constant("IE"),opera:c.constant(v),firefox:c.constant(y),safari:c.constant(b)},O="Windows",T="Android",k="Linux",C="Solaris",E="FreeBSD",D=function(n,e){return function(){return e===n}},A=function(n){var e=n.current;return{current:e,version:n.version,isWindows:D(O,e),isiOS:D("iOS",e),isAndroid:D(T,e),isOSX:D("OSX",e),isLinux:D(k,e),isSolaris:D(C,e),isFreeBSD:D(E,e)}},M={unknown:function(){return A({current:undefined,version:p.unknown()})},nu:A,windows:c.constant(O),ios:c.constant("iOS"),android:c.constant(T),linux:c.constant(k),osx:c.constant("OSX"),solaris:c.constant(C),freebsd:c.constant(E)},R=c.never,F=c.always,B=function(){return I},I=(r={fold:function(n,e){return n()},is:R,isSome:R,isNone:F,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},or:t,orThunk:e,map:B,ap:B,each:function(){},bind:B,flatten:B,exists:R,forall:F,filter:B,equals:n=function(n){return n.isNone()},equals_:n,toArray:function(){return[]},toString:c.constant("none()")},Object.freeze&&Object.freeze(r),r),H=function(n){var e=function(){return n},t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:F,isNone:R,getOr:e,getOrThunk:e,getOrDie:e,or:t,orThunk:t,map:function(e){return H(e(n))},ap:function(e){return e.fold(B,function(e){return H(e(n))})},each:function(e){e(n)},bind:r,flatten:e,exists:r,forall:r,filter:function(e){return e(n)?o:I},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(R,function(e){return t(n,e)})},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},N={some:H,none:B,from:function(n){return null===n||n===undefined?I:H(n)}},V=(o=Array.prototype.indexOf)===undefined?function(n,e){return K(n,e)}:function(n,e){return o.call(n,e)},j=function(n,e){return V(n,e)>-1},P=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var i=n[o];r[o]=e(i,o,n)}return r},L=function(n,e){for(var t=0,r=n.length;t<r;t++)e(n[t],t,n)},W=function(n,e){for(var t=n.length-1;t>=0;t--)e(n[t],t,n)},U=function(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var i=n[r];e(i,r,n)&&t.push(i)}return t},z=function(n,e){for(var t=0,r=n.length;t<r;t++)if(e(n[t],t,n))return N.some(t);return N.none()},K=function(n,e){for(var t=0,r=n.length;t<r;++t)if(n[t]===e)return t;return-1},G=Array.prototype.push,$=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!Array.prototype.isPrototypeOf(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);G.apply(e,n[t])}return e},q=function(n,e){for(var t=0,r=n.length;t<r;++t)if(!0!==e(n[t],t,n))return!1;return!0},_=Array.prototype.slice,X={map:P,each:L,eachr:W,partition:function(n,e){for(var t=[],r=[],o=0,i=n.length;o<i;o++){var u=n[o];(e(u,o,n)?t:r).push(u)}return{pass:t,fail:r}},filter:U,groupBy:function(n,e){if(0===n.length)return[];for(var t=e(n[0]),r=[],o=[],i=0,u=n.length;i<u;i++){var a=n[i],c=e(a);c!==t&&(r.push(o),o=[]),t=c,o.push(a)}return 0!==o.length&&r.push(o),r},indexOf:function(n,e){var t=V(n,e);return-1===t?N.none():N.some(t)},foldr:function(n,e,t){return W(n,function(n){t=e(t,n)}),t},foldl:function(n,e,t){return L(n,function(n){t=e(t,n)}),t},find:function(n,e){for(var t=0,r=n.length;t<r;t++){var o=n[t];if(e(o,t,n))return N.some(o)}return N.none()},findIndex:z,flatten:$,bind:function(n,e){var t=P(n,e);return $(t)},forall:q,exists:function(n,e){return z(n,e).isSome()},contains:j,equal:function(n,e){return n.length===e.length&&q(n,function(n,t){return n===e[t]})},reverse:function(n){var e=_.call(n,0);return e.reverse(),e},chunk:function(n,e){for(var t=[],r=0;r<n.length;r+=e){var o=n.slice(r,r+e);t.push(o)}return t},difference:function(n,e){return U(n,function(n){return!j(e,n)})},mapToObject:function(n,e){for(var t={},r=0,o=n.length;r<o;r++){var i=n[r];t[String(i)]=e(i,r)}return t},pure:function(n){return[n]},sort:function(n,e){var t=_.call(n,0);return t.sort(e),t},range:function(n,e){for(var t=[],r=0;r<n;r++)t.push(e(r));return t},head:function(n){return 0===n.length?N.none():N.some(n[0])},last:function(n){return 0===n.length?N.none():N.some(n[n.length-1])}},Y=function(n,e){var t=String(e).toLowerCase();return X.find(n,function(n){return n.search(t)})},J=function(n,e){return Y(n,e).map(function(n){var t=p.detect(n.versionRegexes,e);return{current:n.name,version:t}})},Q=function(n,e){return Y(n,e).map(function(n){var t=p.detect(n.versionRegexes,e);return{current:n.name,version:t}})},Z=function(n,e){return n.replace(/\${([^{}]*)}/g,function(n,t){var r,o=e[t];return"string"==(r=typeof o)||"number"===r?o:n})},nn=function(n,e){return-1!==n.indexOf(e)},en=function(n){return n.replace(/^\s+|\s+$/g,"")},tn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,rn=function(n){return function(e){return nn(e,n)}},on=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return nn(n,"edge/")&&nn(n,"chrome")&&nn(n,"safari")&&nn(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,tn],search:function(n){return nn(n,"chrome")&&!nn(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return nn(n,"msie")||nn(n,"trident")}},{name:"Opera",versionRegexes:[tn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:rn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:rn("firefox")},{name:"Safari",versionRegexes:[tn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(nn(n,"safari")||nn(n,"mobile/"))&&nn(n,"applewebkit")}}],un=[{name:"Windows",search:rn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return nn(n,"iphone")||nn(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:rn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:rn("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:rn("linux"),versionRegexes:[]},{name:"Solaris",search:rn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:rn("freebsd"),versionRegexes:[]}],an={browsers:c.constant(on),oses:c.constant(un)},cn=function(n){var e,t,r,o,i,u,a,s,f,l,d,m=an.browsers(),p=an.oses(),g=J(m,n).fold(x.unknown,x.nu),h=Q(p,n).fold(M.unknown,M.nu);return{browser:g,os:h,deviceType:(t=g,r=n,o=(e=h).isiOS()&&!0===/ipad/i.test(r),i=e.isiOS()&&!o,u=e.isAndroid()&&3===e.version.major,a=e.isAndroid()&&4===e.version.major,s=o||u||a&&!0===/mobile/i.test(r),f=e.isiOS()||e.isAndroid(),l=f&&!s,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(r),{isiPad:c.constant(o),isiPhone:c.constant(i),isTablet:c.constant(s),isPhone:c.constant(l),isTouch:c.constant(f),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:c.constant(d)})}},sn={detect:f(function(){var n=navigator.userAgent;return cn(n)})},fn={tap:c.constant("alloy.tap")},ln={focus:c.constant("alloy.focus"),postBlur:c.constant("alloy.blur.post"),receive:c.constant("alloy.receive"),execute:c.constant("alloy.execute"),focusItem:c.constant("alloy.focus.item"),tap:fn.tap,tapOrClick:sn.detect().deviceType.isTouch()?fn.tap:s.click,longpress:c.constant("alloy.longpress"),sandboxClose:c.constant("alloy.sandbox.close"),systemInit:c.constant("alloy.system.init"),windowScroll:c.constant("alloy.system.scroll"),attachedToDom:c.constant("alloy.system.attached"),detachedFromDom:c.constant("alloy.system.detached"),changeTab:c.constant("alloy.change.tab"),dismissTab:c.constant("alloy.dismiss.tab")},dn=function(n){return function(e){return function(n){if(null===n)return"null";var e=typeof n;return"object"===e&&Array.prototype.isPrototypeOf(n)?"array":"object"===e&&String.prototype.isPrototypeOf(n)?"string":e}(e)===n}},mn={isString:dn("string"),isObject:dn("object"),isArray:dn("array"),isNull:dn("null"),isBoolean:dn("boolean"),isUndefined:dn("undefined"),isFunction:dn("function"),isNumber:dn("number")},pn=function(n){return function(){for(var e=new Array(arguments.length),t=0;t<e.length;t++)e[t]=arguments[t];if(0===e.length)throw new Error("Can't merge zero objects");for(var r={},o=0;o<e.length;o++){var i=e[o];for(var u in i)i.hasOwnProperty(u)&&(r[u]=n(r[u],i[u]))}return r}},gn=pn(function(n,e){return mn.isObject(n)&&mn.isObject(e)?gn(n,e):e}),hn=pn(function(n,e){return e}),vn={deepMerge:gn,merge:hn},yn=(i=Object.keys)===undefined?function(n){var e=[];for(var t in n)n.hasOwnProperty(t)&&e.push(t);return e}:i,bn=function(n,e){for(var t=yn(n),r=0,o=t.length;r<o;r++){var i=t[r];e(n[i],i,n)}},wn=function(n,e){var t={};return bn(n,function(r,o){var i=e(r,o,n);t[i.k]=i.v}),t},Sn=function(n,e){var t=[];return bn(n,function(n,r){t.push(e(n,r))}),t},xn=function(n){return Sn(n,function(n){return n})},On={bifilter:function(n,e){var t={},r={};return bn(n,function(n,o){(e(n,o)?t:r)[o]=n}),{t:t,f:r}},each:bn,map:function(n,e){return wn(n,function(n,t,r){return{k:t,v:e(n,t,r)}})},mapToArray:Sn,tupleMap:wn,find:function(n,e){for(var t=yn(n),r=0,o=t.length;r<o;r++){var i=t[r],u=n[i];if(e(u,i,n))return N.some(u)}return N.none()},keys:yn,values:xn,size:function(n){return xn(n).length}},Tn=function(n,e){kn(n,n.element(),e,{})},kn=function(n,e,t,r){var o=vn.deepMerge({target:e},r);n.getSystem().triggerEvent(t,e,On.map(o,c.constant))},Cn=Tn,En=function(n,e,t){kn(n,n.element(),e,t)},Dn=function(n){Tn(n,ln.execute())},An=function(n,e,t){kn(n,e,t,{})},Mn=function(n,e,t,r){n.getSystem().triggerEvent(t,e,r.event())},Rn=function(n,e){n.getSystem().triggerFocus(e,n.element())},Fn=function(n){return n.slice(0).sort()},Bn={sort:Fn,reqMessage:function(n,e){throw new Error("All required keys ("+Fn(n).join(", ")+") were not specified. Specified keys were: "+Fn(e).join(", ")+".")},unsuppMessage:function(n){throw new Error("Unsupported keys for object: "+Fn(n).join(", "))},validateStrArr:function(n,e){if(!mn.isArray(e))throw new Error("The "+n+" fields must be an array. Was: "+e+".");X.each(e,function(e){if(!mn.isString(e))throw new Error("The value "+e+" in the "+n+" fields was not a string.")})},invalidTypeMessage:function(n,e){throw new Error("All values need to be of type: "+e+". Keys ("+Fn(n).join(", ")+") were not.")},checkDupes:function(n){var e=Fn(n);X.find(e,function(n,t){return t<e.length-1&&n===e[t+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+e.join(", ")+"].")})}},In={immutable:function(){var n=arguments;return function(){for(var e=new Array(arguments.length),t=0;t<e.length;t++)e[t]=arguments[t];if(n.length!==e.length)throw new Error('Wrong number of arguments to struct. Expected "['+n.length+']", got '+e.length+" arguments");var r={};return X.each(n,function(n,t){r[n]=c.constant(e[t])}),r}},immutableBag:function(n,e){var t=n.concat(e);if(0===t.length)throw new Error("You must specify at least one required or optional field.");return Bn.validateStrArr("required",n),Bn.validateStrArr("optional",e),Bn.checkDupes(t),function(r){var o=On.keys(r);X.forall(n,function(n){return X.contains(o,n)})||Bn.reqMessage(n,o);var i=X.filter(o,function(n){return!X.contains(t,n)});i.length>0&&Bn.unsuppMessage(i);var u={};return X.each(n,function(n){u[n]=c.constant(r[n])}),X.each(e,function(n){u[n]=c.constant(Object.prototype.hasOwnProperty.call(r,n)?N.some(r[n]):N.none())}),u}}},Hn=function(n,e){for(var t=[],r=function(n){return t.push(n),e(n)},o=e(n);(o=o.bind(r)).isSome(););return t},Nn="undefined"!=typeof window?window:Function("return this;")(),Vn=function(n,e){for(var t=e!==undefined&&null!==e?e:Nn,r=0;r<n.length&&t!==undefined&&null!==t;++r)t=t[n[r]];return t},jn=function(n,e){var t=n.split(".");return Vn(t,e)},Pn={getOrDie:function(n,e){var t=jn(n,e);if(t===undefined||null===t)throw n+" not available on this browser";return t}},Ln=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:c.constant(n)}},Wn={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||t.childNodes.length>1)throw console.error("HTML does not have a single root node",n),"HTML must have a single root node";return Ln(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return Ln(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return Ln(t)},fromDom:Ln,fromPoint:function(n,e,t){return N.from(n.dom().elementFromPoint(e,t)).map(Ln)}},Un=8,zn=9,Kn=1,Gn=3,$n=Kn,qn=zn,_n=function(n){return n.nodeType!==$n&&n.nodeType!==qn||0===n.childElementCount},Xn={all:function(n,e){var t=e===undefined?document:e.dom();return _n(t)?[]:X.map(t.querySelectorAll(n),Wn.fromDom)},is:function(n,e){var t=n.dom();if(t.nodeType!==$n)return!1;if(t.matches!==undefined)return t.matches(e);if(t.msMatchesSelector!==undefined)return t.msMatchesSelector(e);if(t.webkitMatchesSelector!==undefined)return t.webkitMatchesSelector(e);if(t.mozMatchesSelector!==undefined)return t.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},one:function(n,e){var t=e===undefined?document:e.dom();return _n(t)?N.none():N.from(t.querySelector(n)).map(Wn.fromDom)}},Yn=function(n,e){return n.dom()===e.dom()},Jn=(sn.detect().browser.isIE(),Yn),Qn=function(n){return Wn.fromDom(n.dom().ownerDocument)},Zn=function(n){var e=n.dom();return N.from(e.parentNode).map(Wn.fromDom)},ne=function(n){var e=n.dom();return N.from(e.previousSibling).map(Wn.fromDom)},ee=function(n){var e=n.dom();return N.from(e.nextSibling).map(Wn.fromDom)},te=function(n){var e=n.dom();return X.map(e.childNodes,Wn.fromDom)},re=function(n,e){var t=n.dom().childNodes;return N.from(t[e]).map(Wn.fromDom)},oe=In.immutable("element","offset"),ie={owner:Qn,defaultView:function(n){var e=n.dom().ownerDocument.defaultView;return Wn.fromDom(e)},documentElement:function(n){var e=Qn(n);return Wn.fromDom(e.dom().documentElement)},parent:Zn,findIndex:function(n){return Zn(n).bind(function(e){var t=te(e);return X.findIndex(t,function(e){return Jn(n,e)})})},parents:function(n,e){for(var t=mn.isFunction(e)?e:c.constant(!1),r=n.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=Wn.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o},siblings:function(n){return Zn(n).map(te).map(function(e){return X.filter(e,function(e){return!Jn(n,e)})}).getOr([])},prevSibling:ne,offsetParent:function(n){var e=n.dom();return N.from(e.offsetParent).map(Wn.fromDom)},prevSiblings:function(n){return X.reverse(Hn(n,ne))},nextSibling:ee,nextSiblings:function(n){return Hn(n,ee)},children:te,child:re,firstChild:function(n){return re(n,0)},lastChild:function(n){return re(n,n.dom().childNodes.length-1)},childNodesCount:function(n){return n.dom().childNodes.length},hasChildNodes:function(n){return n.dom().hasChildNodes()},leaf:function(n,e){var t=te(n);return t.length>0&&e<t.length?oe(t[e],0):oe(n,e)}},ue=function(n,e){ie.parent(n).each(function(t){t.dom().insertBefore(e.dom(),n.dom())})},ae=function(n,e){n.dom().appendChild(e.dom())},ce={before:ue,after:function(n,e){ie.nextSibling(n).fold(function(){ie.parent(n).each(function(n){ae(n,e)})},function(n){ue(n,e)})},prepend:function(n,e){ie.firstChild(n).fold(function(){ae(n,e)},function(t){n.dom().insertBefore(e.dom(),t.dom())})},append:ae,appendAt:function(n,e,t){ie.child(n,t).fold(function(){ae(n,e)},function(n){ue(n,e)})},wrap:function(n,e){ue(n,e),ae(e,n)}},se={before:function(n,e){X.each(e,function(e){ce.before(n,e)})},after:function(n,e){X.each(e,function(t,r){var o=0===r?n:e[r-1];ce.after(o,t)})},prepend:function(n,e){X.each(e.slice().reverse(),function(e){ce.prepend(n,e)})},append:function(n,e){X.each(e,function(e){ce.append(n,e)})}},fe=function(n){var e=n.dom();null!==e.parentNode&&e.parentNode.removeChild(e)},le={empty:function(n){n.dom().textContent="",X.each(ie.children(n),function(n){fe(n)})},remove:fe,unwrap:function(n){var e=ie.children(n);e.length>0&&se.before(n,e),fe(n)}},de=function(n){return n.dom().nodeName.toLowerCase()},me=function(n){return n.dom().nodeType},pe=function(n){return function(e){return me(e)===n}},ge=pe(Kn),he=pe(Gn),ve=pe(zn),ye={name:de,type:me,value:function(n){return n.dom().nodeValue},isElement:ge,isText:he,isDocument:ve,isComment:function(n){return me(n)===Un||"#comment"===de(n)}},be=f(function(){return we(Wn.fromDom(document))}),we=function(n){var e=n.dom().body;if(null===e||e===undefined)throw"Body is not available yet";return Wn.fromDom(e)},Se={body:be,getBody:we,inBody:function(n){var e=ye.isText(n)?n.dom().parentNode:n.dom();return e!==undefined&&null!==e&&e.ownerDocument.body.contains(e)}},xe=function(n){Cn(n,ln.detachedFromDom());var e=n.components();X.each(e,xe)},Oe=function(n){var e=n.components();X.each(e,Oe),Cn(n,ln.attachedToDom())},Te=function(n,e,t){n.getSystem().addToWorld(e),t(n.element(),e.element()),Se.inBody(n.element())&&Oe(e),n.syncComponents()},ke=function(n){xe(n),le.remove(n.element()),n.getSystem().removeFromWorld(n)},Ce={attach:function(n,e){Te(n,e,ce.append)},attachWith:Te,detach:function(n){var e=ie.parent(n.element()).bind(function(e){return n.getSystem().getByDom(e).fold(N.none,N.some)});ke(n),e.each(function(n){n.syncComponents()})},detachChildren:function(n){var e=n.components();X.each(e,ke),le.empty(n.element()),n.syncComponents()},attachSystem:function(n,e){ce.append(n,e.element());var t=ie.children(e.element());X.each(t,function(n){e.getByDom(n).each(Oe)})},detachSystem:function(n){var e=ie.children(n.element());X.each(e,function(e){n.getByDom(e).each(xe)}),le.remove(n.element())}},Ee=function(n,e){var t=(e||document).createElement("div");return t.innerHTML=n,ie.children(Wn.fromDom(t))},De=function(n){return n.dom().innerHTML},Ae=De,Me=function(n,e){var t=ie.owner(n).dom(),r=Wn.fromDom(t.createDocumentFragment()),o=Ee(e,t);se.append(r,o),le.empty(n),ce.append(n,r)},Re=function(n){var e=Wn.fromTag("div"),t=Wn.fromDom(n.dom().cloneNode(!0));return ce.append(e,t),De(e)},Fe=function(n,e,t){if(!(mn.isString(t)||mn.isBoolean(t)||mn.isNumber(t)))throw console.error("Invalid call to Attr.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")},Be=function(n,e,t){Fe(n.dom(),e,t)},Ie=function(n,e){var t=n.dom().getAttribute(e);return null===t?undefined:t},He=function(n,e){var t=n.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(e)},Ne={clone:function(n){return X.foldl(n.dom().attributes,function(n,e){return n[e.name]=e.value,n},{})},set:Be,setAll:function(n,e){var t=n.dom();On.each(e,function(n,e){Fe(t,e,n)})},get:Ie,has:He,remove:function(n,e){n.dom().removeAttribute(e)},hasNone:function(n){var e=n.dom().attributes;return e===undefined||null===e||0===e.length},transfer:function(n,e,t){ye.isElement(n)&&ye.isElement(e)&&X.each(t,function(t){var r,o,i;o=e,He(r=n,i=t)&&!He(o,i)&&Be(o,i,Ie(r,i))})}},Ve=function(n,e){return Wn.fromDom(n.dom().cloneNode(e))},je=function(n){return Ve(n,!1)},Pe=function(n){var e=je(n);return Re(e)},Le={element:function(n){return Pe(n)}},We=function(n){return{is:function(e){return n===e},isValue:c.always,isError:c.never,getOr:c.constant(n),getOrThunk:c.constant(n),getOrDie:c.constant(n),or:function(e){return We(n)},orThunk:function(e){return We(n)},fold:function(e,t){return t(n)},map:function(e){return We(e(n))},each:function(e){e(n)},bind:function(e){return e(n)},exists:function(e){return e(n)},forall:function(e){return e(n)},toOption:function(){return N.some(n)}}},Ue=function(n){return{is:c.never,isValue:c.never,isError:c.always,getOr:c.identity,getOrThunk:function(n){return n()},getOrDie:function(){return c.die(n)()},or:function(n){return n},orThunk:function(n){return n()},fold:function(e,t){return e(n)},map:function(e){return Ue(n)},each:c.noop,bind:function(e){return Ue(n)},exists:c.never,forall:c.always,toOption:N.none}},ze={value:We,error:Ue},Ke=function(n){if(!mn.isArray(n))throw new Error("cases must be an array");if(0===n.length)throw new Error("there must be at least one case");var e=[],t={};return X.each(n,function(r,o){var i=On.keys(r);if(1!==i.length)throw new Error("one and only one name per case");var u=i[0],a=r[u];if(t[u]!==undefined)throw new Error("duplicate key detected:"+u);if("cata"===u)throw new Error("cannot have a case named cata (sorry)");if(!mn.isArray(a))throw new Error("case arguments must be an array");e.push(u),t[u]=function(){var t=arguments.length;if(t!==a.length)throw new Error("Wrong number of arguments to case "+u+". Expected "+a.length+" ("+a+"), got "+t);for(var r=new Array(t),i=0;i<r.length;i++)r[i]=arguments[i];return{fold:function(){if(arguments.length!==n.length)throw new Error("Wrong number of arguments to fold. Expected "+n.length+", got "+arguments.length);return arguments[o].apply(null,r)},match:function(n){var t=On.keys(n);if(e.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+e.join(",")+"\nActual: "+t.join(","));if(!X.forall(e,function(n){return X.contains(t,n)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+e.join(", "));return n[u].apply(null,r)},log:function(n){console.log(n,{constructors:e,constructor:u,params:r})}}}}),t},Ge=(Ke([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n){var e=[],t=[];return X.each(n,function(n){n.fold(function(n){e.push(n)},function(n){t.push(n)})}),{errors:e,values:t}}),$e=function(n){return c.compose(ze.error,X.flatten)(n)},qe=function(n,e){var t,r,o=Ge(n);return o.errors.length>0?$e(o.errors):(t=o.values,r=e,ze.value(vn.deepMerge.apply(undefined,[r].concat(t))))},_e=function(n){var e=Ge(n);return e.errors.length>0?$e(e.errors):ze.value(e.values)},Xe=function(n,e){var t={};return X.each(e,function(e){n[e]!==undefined&&n.hasOwnProperty(e)&&(t[e]=n[e])}),t},Ye=function(n,e){var t={};return On.each(n,function(n,r){X.contains(e,r)||(t[r]=n)}),t},Je=function(n,e){var t={};return X.each(n,function(n){var r=n[e];t[r]=n}),t},Qe=function(n){return function(e){return e.hasOwnProperty(n)?N.from(e[n]):N.none()}},Ze=Qe,nt=function(n,e){return function(t){return Qe(n)(t).getOr(e)}},et=function(n,e){return Qe(e)(n)},tt=function(n,e){return n.hasOwnProperty(e)&&n[e]!==undefined&&null!==n[e]},rt=function(n,e){var t={};return t[n]=e,t},ot=function(n){var e={};return X.each(n,function(n){e[n.key]=n.value}),e},it={narrow:function(n,e){return Xe(n,e)},exclude:function(n,e){return Ye(n,e)},readOpt:function(n){return Ze(n)},readOr:function(n,e){return nt(n,e)},readOptFrom:function(n,e){return et(n,e)},wrap:function(n,e){return rt(n,e)},wrapAll:function(n){return ot(n)},indexOnKey:function(n,e){return Je(n,e)},hasKey:function(n,e){return tt(n,e)},consolidate:function(n,e){return qe(n,e)}},ut=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e},at=function(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return N.none()},ct="unknown",st="__CHROME_INSPECTOR_CONNECTION_TO_ALLOY__",ft=[],lt=["alloy/data/Fields","alloy/debugging/Debugging"],dt={logEventCut:c.noop,logEventStopped:c.noop,logNoParent:c.noop,logEventNoHandlers:c.noop,logEventResponse:c.noop,write:c.noop},mt=function(){return window[st]!==undefined?window[st]:(window[st]={systems:{},lookup:function(n){var e=window[st].systems,t=On.keys(e);return at(t,function(t){return e[t].getByUid(n).toOption().map(function(n){return it.wrap(Le.element(n.element()),(e=function(n){var t=n.spec();return{"(original.spec)":t,"(dom.ref)":n.element().dom(),"(element)":Le.element(n.element()),"(initComponents)":X.map(t.components!==undefined?t.components:[],e),"(components)":X.map(n.components(),e),"(bound.events)":On.mapToArray(n.events(),function(n,e){return[e]}).join(", "),"(behaviours)":t.behaviours!==undefined?On.map(t.behaviours,function(e,t){return e===undefined?"--revoked--":{config:e.configAsRaw(),"original-config":e.initialConfig,state:n.readState(t)}}):"none"}})(n));var e})})}},window[st])},pt=function(n,e,t){},gt=(c.constant(dt),function(){var n=new Error;if(n.stack!==undefined){var e=n.stack.split("\n");return X.find(e,function(n){return n.indexOf("alloy")>0&&!X.exists(lt,function(e){return n.indexOf(e)>-1})}).getOr(ct)}return ct}),ht=function(n,e,t){var r,o="*"===ft||X.contains(ft,n)?(r=[],{logEventCut:function(n,e,t){r.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){r.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){r.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){r.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){r.push({outcome:"response",purpose:t,target:e})},write:function(){X.contains(["mousemove","mouseover","mouseout",ln.systemInit()],n)||console.log(n,{event:n,target:e.dom(),sequence:X.map(r,function(n){return X.contains(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+Le.element(n.target)+")":n.outcome})})}}):dt,i=t(o);return o.write(),i},vt=(c.constant(!0),function(n,e){mt().systems[n]=e}),yt=function(n,e){return Jn(n.element(),e.event().target())},bt=Ke([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),wt={strict:bt.strict,asOption:bt.asOption,defaulted:function(n){return bt.defaultedThunk(c.constant(n))},defaultedThunk:bt.defaultedThunk,asDefaultedOption:function(n){return bt.asDefaultedOptionThunk(c.constant(n))},asDefaultedOptionThunk:bt.asDefaultedOptionThunk,mergeWith:function(n){return bt.mergeWithThunk(c.constant(n))},mergeWithThunk:bt.mergeWithThunk},St={typeAdt:Ke([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),fieldAdt:Ke([{field:["name","presence","type"]},{state:["name"]}])},xt=function(){return Pn.getOrDie("JSON")},Ot=function(n,e,t){return xt().stringify(n,e,t)},Tt=function(n){return mn.isObject(n)&&On.keys(n).length>100?" removed due to size":Ot(n,null,2)},kt=function(n){var e=n.length>10?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n;return X.map(e,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()})},Ct=function(n,e){return ze.error([{path:n,getErrorInfo:e}])},Et=function(n,e,t){return Ct(n,function(){return'Could not find valid *strict* value for "'+e+'" in '+Tt(t)})},Dt=function(n,e){return Ct(n,function(){return'Choice schema did not contain choice key: "'+e+'"'})},At=function(n,e,t){return Ct(n,function(){return'The chosen schema: "'+t+'" did not exist in branches: '+Tt(e)})},Mt=function(n,e){return Ct(n,function(){return"There are unsupported fields: ["+e.join(", ")+"] specified"})},Rt=function(n,e){return Ct(n,function(){return e})},Ft=Ke([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),Bt=function(n,e,t){var r=et(n,e).fold(function(){return t(n)},c.identity);return ze.value(r)},It=function(n,e,t,r){return t.fold(function(t,o,i,u){var a=function(e){return u.extract(n.concat([t]),r,e).map(function(n){return rt(o,r(n))})},s=function(e){return e.fold(function(){var n=rt(o,r(N.none()));return ze.value(n)},function(e){return u.extract(n.concat([t]),r,e).map(function(n){return rt(o,r(N.some(n)))})})};return i.fold(function(){return(r=n,o=e,i=t,et(o,i).fold(function(){return Et(r,i,o)},ze.value)).bind(a);var r,o,i},function(n){return Bt(e,t,n).bind(a)},function(){return(n=e,r=t,ze.value(et(n,r))).bind(s);var n,r},function(n){return(r=e,o=t,i=n,u=et(r,o).map(function(n){return!0===n?i(r):n}),ze.value(u)).bind(s);var r,o,i,u},function(n){var r=n(e);return Bt(e,t,c.constant({})).map(function(n){return vn.deepMerge(r,n)}).bind(a)})},function(n,t){var o=t(e);return ze.value(rt(n,r(o)))})},Ht=function(n){return{extract:function(e,t,r){return n(r,t).fold(function(n){return Rt(e,n)},ze.value)},toString:function(){return"val"},toDsl:function(){return St.typeAdt.itemOf(n)}}},Nt=function(n){return{extract:function(e,t,r){return o=e,i=r,u=n,a=t,c=X.map(u,function(n){return It(o,i,n,a)}),qe(c,{});var o,i,u,a,c},toString:function(){return"obj{\n"+X.map(n,function(n){return n.fold(function(n,e,t,r){return n+" -> "+r.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"},toDsl:function(){return St.typeAdt.objOf(X.map(n,function(n){return n.fold(function(n,e,t,r){return St.fieldAdt.field(n,t,r)},function(n,e){return St.fieldAdt.state(n)})}))}}},Vt=function(n){return{extract:function(e,t,r){var o=X.map(r,function(r,o){return n.extract(e.concat(["["+o+"]"]),t,r)});return _e(o)},toString:function(){return"array("+n.toString()+")"},toDsl:function(){return St.typeAdt.arrOf(n)}}},jt=Ht(ze.value),Pt=c.compose(Vt,Nt),Lt={anyValue:c.constant(jt),value:Ht,obj:Nt,objOnly:function(n){var e=Nt(n),t=X.foldr(n,function(n,e){return e.fold(function(e){return vn.deepMerge(n,it.wrap(e,!0))},c.constant(n))},{});return{extract:function(n,r,o){var i,u,a=mn.isBoolean(o)?[]:(i=o,u=On.keys(i),X.filter(u,function(n){return it.hasKey(i,n)})),c=X.filter(a,function(n){return!it.hasKey(t,n)});return 0===c.length?e.extract(n,r,o):Mt(n,c)},toString:e.toString,toDsl:e.toDsl}},arr:Vt,setOf:function(n,e){return{extract:function(t,r,o){var i,u,a=On.keys(o);return(i=t,u=a,Vt(Ht(n)).extract(i,c.identity,u)).bind(function(n){var i=X.map(n,function(n){return Ft.field(n,n,wt.strict(),e)});return Nt(i).extract(t,r,o)})},toString:function(){return"setOf("+e.toString()+")"},toDsl:function(){return St.typeAdt.setOf(n,e)}}},arrOfObj:Pt,state:Ft.state,field:Ft.field,output:function(n,e){return Ft.state(n,c.constant(e))},snapshot:function(n){return Ft.state(n,c.identity)},thunk:function(n,e){var t=f(function(){return e()});return{extract:function(n,e,r){return t().extract(n,e,r)},toString:function(){return t().toString()},toDsl:function(){return St.typeAdt.thunk(n)}}},func:function(n,e,t){return{extract:Ht(function(e,r){return mn.isFunction(e)?ze.value(function(){var o=Array.prototype.slice.call(arguments,0).slice(0,n.length),i=e.apply(null,o);return t(i,r)}):ze.error("Not a function")}).extract,toString:function(){return"function"},toDsl:function(){return St.typeAdt.func(n,e)}}}},Wt=function(n,e){return Lt.field(n,n,wt.strict(),e)},Ut=function(n){return Lt.field(n,n,wt.strict(),Lt.anyValue())},zt=Wt,Kt=function(n,e){return Lt.field(n,n,wt.strict(),Lt.obj(e))},Gt=function(n){return Lt.field(n,n,wt.strict(),Lt.value(function(n){return mn.isFunction(n)?ze.value(n):ze.error("Not a function")}))},$t=function(n,e){return Lt.field(n,n,wt.asOption(),Lt.value(function(t){return ze.error("The field: "+n+" is forbidden. "+e)}))},qt=function(n){return Lt.field(n,n,wt.asOption(),Lt.anyValue())},_t=function(n,e){return Lt.field(n,n,wt.asOption(),e)},Xt=function(n,e){return Lt.field(n,n,wt.asOption(),Lt.obj(e))},Yt=function(n,e){return Lt.field(n,n,wt.asOption(),Lt.objOnly(e))},Jt=function(n,e){return Lt.field(n,n,wt.defaulted(e),Lt.anyValue())},Qt=function(n,e,t){return Lt.field(n,n,wt.defaulted(e),t)},Zt=function(n,e,t){return Lt.field(n,n,wt.defaulted(e),Lt.obj(t))},nr=function(n,e,t,r){return Lt.field(n,e,t,r)},er=function(n,e){return Lt.state(n,e)},tr=function(n,e){return{extract:function(t,r,o){return it.readOptFrom(o,n).fold(function(){return Dt(t,n)},function(n){return i=t,u=r,a=o,c=e,s=n,it.readOptFrom(c,s).fold(function(){return At(i,c,s)},function(n){return Lt.obj(n).extract(i.concat(["branch: "+s]),u,a)});var i,u,a,c,s})},toString:function(){return"chooseOn("+n+"). Possible values: "+On.keys(e)},toDsl:function(){return St.typeAdt.choiceOf(n,e)}}},rr=Lt.value(ze.value),or=Lt.arr,ir=Lt.obj,ur=Lt.objOnly,ar=Lt.setOf,cr=function(n,e,t,r){return e.extract([n],t,r).fold(function(n){return ze.error({input:r,errors:n})},ze.value)},sr=function(n,e,t){return cr(n,e,c.constant,t)},fr=function(n,e,t){return cr(n,e,c.identity,t)},lr=function(n){return n.fold(function(n){throw new Error(dr(n))},c.identity)},dr=function(n){return"Errors: \n"+kt(n.errors)+"\n\nInput object: "+Tt(n.input)},mr={anyValue:c.constant(rr),arrOfObj:function(n){return Lt.arrOfObj(n)},arrOf:or,arrOfVal:function(){return Lt.arr(rr)},valueOf:function(n){return Lt.value(function(e){return n(e)})},setOf:ar,objOf:ir,objOfOnly:ur,asStruct:sr,asRaw:fr,asStructOrDie:function(n,e,t){return lr(sr(n,e,t))},asRawOrDie:function(n,e,t){return lr(fr(n,e,t))},getOrDie:lr,formatError:dr,choose:function(n,e){return tr(n,e)},thunkOf:function(n,e){return Lt.thunk(n,e)},funcOrDie:function(n,e){return Lt.func(n,e,function(n,t){return lr(cr("()",e,t,n))})}},pr=function(n){if(!it.hasKey(n,"can")&&!it.hasKey(n,"abort")&&!it.hasKey(n,"run"))throw new Error("EventHandler defined by: "+Ot(n,null,2)+" does not have can, abort, or run!");return mr.asRawOrDie("Extracting event.handler",mr.objOfOnly([Jt("can",c.constant(!0)),Jt("abort",c.constant(!1)),Jt("run",c.noop)]),n)},gr=function(n){return mn.isFunction(n)?{can:c.constant(!0),abort:c.constant(!1),run:n}:n},hr=function(n){var e,t,r,o,i=(e=n,t=function(n){return n.can},function(){var n=Array.prototype.slice.call(arguments,0);return X.foldl(e,function(e,r){return e&&t(r).apply(undefined,n)},!0)}),u=(r=n,o=function(n){return n.abort},function(){var n=Array.prototype.slice.call(arguments,0);return X.foldl(r,function(e,t){return e||o(t).apply(undefined,n)},!1)});return pr({can:i,abort:u,run:function(){var e=Array.prototype.slice.call(arguments,0);X.each(n,function(n){n.run.apply(undefined,e)})}})},vr=pr,yr=it.wrapAll,br=function(n,e){return{key:n,value:vr({run:e})}},wr=function(n){return function(e){return{key:n,value:vr({run:function(n,t){yt(n,t)&&e(n,t)}})}}},Sr=function(n,e){return br(n,function(t,r){t.getSystem().getByUid(e).each(function(e){Mn(e,e.element(),n,r)})})},xr={derive:yr,run:br,preventDefault:function(n){return{key:n,value:vr({run:function(n,e){e.event().prevent()}})}},runActionExtra:function(n,e,t){return{key:n,value:vr({run:function(n){e.apply(undefined,[n].concat(t))}})}},runOnAttached:wr(ln.attachedToDom()),runOnDetached:wr(ln.detachedFromDom()),runOnInit:wr(ln.systemInit()),runOnExecute:(u=ln.execute(),function(n){return br(u,n)}),redirectToUid:Sr,redirectToPart:function(n,e,t){var r=e.partUids()[t];return Sr(n,r)},runWithTarget:function(n,e){return br(n,function(n,t){n.getSystem().getByDom(t.event().target()).each(function(r){e(n,r,t)})})},abort:function(n,e){return{key:n,value:vr({abort:e})}},can:function(n,e){return{key:n,value:vr({can:e})}},cutter:function(n){return br(n,function(n,e){e.cut()})},stopper:function(n){return br(n,function(n,e){e.stop()})}},Or=(N.none,function(n,e,t){return n}),Tr=function(n,e){return n},kr=function(n,e){return n},Cr=In.immutableBag(["tag"],["classes","attributes","styles","value","innerHtml","domChildren","defChildren"]),Er=function(n){return{tag:n.tag(),classes:n.classes().getOr([]),attributes:n.attributes().getOr({}),styles:n.styles().getOr({}),value:n.value().getOr("<none>"),innerHtml:n.innerHtml().getOr("<none>"),defChildren:n.defChildren().getOr("<none>"),domChildren:n.domChildren().fold(function(){return"<none>"},function(n){return 0===n.length?"0 children, but still specified":String(n.length)})}},Dr={nu:Cr,defToStr:function(n){var e=Er(n);return Ot(e,null,2)},defToRaw:Er},Ar=In.immutableBag([],["classes","attributes","styles","value","innerHtml","defChildren","domChildren"]),Mr=function(n){return{classes:n.classes().getOr("<none>"),attributes:n.attributes().getOr("<none>"),styles:n.styles().getOr("<none>"),value:n.value().getOr("<none>"),innerHtml:n.innerHtml().getOr("<none>"),defChildren:n.defChildren().getOr("<none>"),domChildren:n.domChildren().fold(function(){return"<none>"},function(n){return 0===n.length?"0 children, but still specified":String(n.length)})}},Rr=function(n,e,t){return e.fold(function(){return t.fold(function(){return{}},function(e){return it.wrap(n,e)})},function(e){return t.fold(function(){return it.wrap(n,e)},function(e){return it.wrap(n,e)})})},Fr={nu:Ar,derive:function(n){var e={},t=On.keys(n);return X.each(t,function(t){n[t].each(function(n){e[t]=n})}),Ar(e)},merge:function(n,e){var t=vn.deepMerge({tag:n.tag(),classes:e.classes().getOr([]).concat(n.classes().getOr([])),attributes:vn.merge(n.attributes().getOr({}),e.attributes().getOr({})),styles:vn.merge(n.styles().getOr({}),e.styles().getOr({}))},e.innerHtml().or(n.innerHtml()).map(function(n){return it.wrap("innerHtml",n)}).getOr({}),Rr("domChildren",e.domChildren(),n.domChildren()),Rr("defChildren",e.defChildren(),n.defChildren()),e.value().or(n.value()).map(function(n){return it.wrap("value",n)}).getOr({}));return Dr.nu(t)},modToStr:function(n){var e=Mr(n);return Ot(e,null,2)},modToRaw:Mr},Br=function(n){return{key:n,value:undefined}},Ir=function(n,e,t,r,o,i,u){var a=function(n){return it.hasKey(n,t)?n[t]():N.none()},s=On.map(o,function(n,e){return r=t,Or(function(n){var e=arguments;return n.config({name:c.constant(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+i)},function(t){var r=Array.prototype.slice.call(e,1);return o.apply(undefined,[n,t.config,t.state].concat(r))})},i=e,o=n);var r,o,i}),l=On.map(i,function(n,e){return Tr(n,e)}),d=vn.deepMerge(l,s,{revoke:c.curry(Br,t),config:function(e){var r=mr.asStructOrDie(t+"-config",n,e);return{key:t,value:{config:r,me:d,configAsRaw:f(function(){return mr.asRawOrDie(t+"-config",n,e)}),initialConfig:e,state:u}}},schema:function(){return e},exhibit:function(n,e){return a(n).bind(function(n){return it.readOptFrom(r,"exhibit").map(function(t){return t(e,n.config,n.state)})}).getOr(Fr.nu({}))},name:function(){return t},handlers:function(n){return a(n).bind(function(n){return it.readOptFrom(r,"events").map(function(e){return e(n.config,n.state)})}).getOr({})}});return d},Hr=function(n,e,t){return xr.runOnExecute(function(r){t(r,n,e)})},Nr=function(n,e,t){return xr.runOnInit(function(r,o){t(r,n,e)})},Vr=function(n,e,t,r,o,i){var u=mr.objOfOnly(n),a=Xt(e,[Yt("config",n)]);return Ir(u,a,e,t,r,o,i)},jr=function(n,e,t,r,o,i){var u=n,a=Xt(e,[_t("config",n)]);return Ir(u,a,e,t,r,o,i)},Pr=function(n,e){return Lr(n,e,{validate:mn.isFunction,label:"function"})},Lr=function(n,e,t){if(0===e.length)throw new Error("You must specify at least one required field.");return Bn.validateStrArr("required",e),Bn.checkDupes(e),function(r){var o=On.keys(r);X.forall(e,function(n){return X.contains(o,n)})||Bn.reqMessage(e,o),n(e,o);var i=X.filter(e,function(n){return!t.validate(r[n],n)});return i.length>0&&Bn.invalidTypeMessage(i,t.label),r}},Wr=c.noop,Ur={exactly:c.curry(Pr,function(n,e){var t=X.filter(e,function(e){return!X.contains(n,e)});t.length>0&&Bn.unsuppMessage(t)}),ensure:c.curry(Pr,Wr),ensureWith:c.curry(Lr,Wr)},zr=Ur.ensure(["readState"]),Kr={init:function(){return zr({readState:function(){return"No State required"}})}},Gr=mr.objOfOnly([Ut("fields"),Ut("name"),Jt("active",{}),Jt("apis",{}),Jt("extra",{}),Jt("state",Kr)]),$r=mr.objOfOnly([Ut("branchKey"),Ut("branches"),Ut("name"),Jt("active",{}),Jt("apis",{}),Jt("extra",{}),Jt("state",Kr)]),qr={derive:function(n){return it.wrapAll(n)},revoke:c.constant(undefined),noActive:c.constant({}),noApis:c.constant({}),noExtra:c.constant({}),noState:c.constant(Kr),create:function(n){var e=mr.asRawOrDie("Creating behaviour: "+n.name,Gr,n);return Vr(e.fields,e.name,e.active,e.apis,e.extra,e.state)},createModes:function(n){var e=mr.asRawOrDie("Creating behaviour: "+n.name,$r,n);return jr(mr.choose(e.branchKey,e.branches),e.name,e.active,e.apis,e.extra,e.state)}};function _r(n,e,t){var r=t||!1,o=function(){e(),r=!0},i=function(){n(),r=!1};return{on:o,off:i,toggle:function(){(r?i:o)()},isOn:function(){return r}}}var Xr=function(n,e){var t=Ne.get(n,e);return t===undefined||""===t?[]:t.split(" ")},Yr=Xr,Jr=function(n,e,t){var r=Xr(n,e).concat([t]);Ne.set(n,e,r.join(" "))},Qr=function(n,e,t){var r=X.filter(Xr(n,e),function(n){return n!==t});r.length>0?Ne.set(n,e,r.join(" ")):Ne.remove(n,e)},Zr=function(n){return Yr(n,"class")},no=function(n,e){return Jr(n,"class",e)},eo=function(n,e){return Qr(n,"class",e)},to=Zr,ro=no,oo=eo,io=function(n,e){X.contains(Zr(n),e)?eo(n,e):no(n,e)},uo=function(n){return n.dom().classList!==undefined},ao=function(n,e){return uo(n)&&n.dom().classList.contains(e)},co={add:function(n,e){uo(n)?n.dom().classList.add(e):ro(n,e)},remove:function(n,e){var t;uo(n)?n.dom().classList.remove(e):oo(n,e),0===(uo(t=n)?t.dom().classList:to(t)).length&&Ne.remove(t,"class")},toggle:function(n,e){return uo(n)?n.dom().classList.toggle(e):io(n,e)},toggler:function(n,e){var t=uo(n),r=n.dom().classList;return _r(function(){t?r.remove(e):oo(n,e)},function(){t?r.add(e):ro(n,e)},ao(n,e))},has:ao},so=function(n,e,t){co.remove(n,t),co.add(n,e)},fo={toAlpha:function(n,e,t){so(n.element(),e.alpha(),e.omega())},toOmega:function(n,e,t){so(n.element(),e.omega(),e.alpha())},isAlpha:function(n,e,t){return co.has(n.element(),e.alpha())},isOmega:function(n,e,t){return co.has(n.element(),e.omega())},clear:function(n,e,t){co.remove(n.element(),e.alpha()),co.remove(n.element(),e.omega())}},lo=[Ut("alpha"),Ut("omega")],mo=qr.create({fields:lo,name:"swapping",apis:fo}),po=function(n){var e=n,t=function(){return e};return{get:t,set:function(n){e=n},clone:function(){return po(t())}}};function go(n,e,t,r,o){return n(t,r)?N.some(t):mn.isFunction(o)&&o(t)?N.none():e(t,r,o)}var ho=function(n,e,t){for(var r=n.dom(),o=mn.isFunction(t)?t:c.constant(!1);r.parentNode;){r=r.parentNode;var i=Wn.fromDom(r);if(e(i))return N.some(i);if(o(i))break}return N.none()},vo=function(n,e){return X.find(n.dom().childNodes,c.compose(e,Wn.fromDom)).map(Wn.fromDom)},yo=function(n,e){var t=function(n){for(var r=0;r<n.childNodes.length;r++){if(e(Wn.fromDom(n.childNodes[r])))return N.some(Wn.fromDom(n.childNodes[r]));var o=t(n.childNodes[r]);if(o.isSome())return o}return N.none()};return t(n.dom())},bo={first:function(n){return yo(Se.body(),n)},ancestor:ho,closest:function(n,e,t){return go(function(n){return e(n)},ho,n,e,t)},sibling:function(n,e){var t=n.dom();return t.parentNode?vo(Wn.fromDom(t.parentNode),function(t){return!Jn(n,t)&&e(t)}):N.none()},child:vo,descendant:yo},wo=function(n){n.dom().focus()},So=function(n){var e=n!==undefined?n.dom():document;return N.from(e.activeElement).map(Wn.fromDom)},xo=function(n){var e=ie.owner(n).dom();return n.dom()===e.activeElement},Oo=wo,To=function(n){n.dom().blur()},ko=So,Co=function(n){return So(ie.owner(n)).filter(function(e){return n.dom().contains(e.dom())})},Eo=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Do=tinymce.util.Tools.resolve("tinymce.ThemeManager"),Ao=function(n){var e=document.createElement("a");e.target="_blank",e.href=n.href,e.rel="noreferrer noopener";var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),document.body.appendChild(e),e.dispatchEvent(t),document.body.removeChild(e)},Mo=function(n){return!1===n.settings.skin},Ro={formatChanged:c.constant("formatChanged"),orientationChanged:c.constant("orientationChanged"),dropupDismissed:c.constant("dropupDismissed")},Fo={events:function(n){return xr.derive([xr.run(ln.receive(),function(e,t){var r,o,i=n.channels(),u=On.keys(i),a=(r=u,(o=t).universal()?r:X.filter(r,function(n){return X.contains(o.channels(),n)}));X.each(a,function(n){var r=i[n](),o=r.schema(),u=mr.asStructOrDie("channel["+n+"] data\nReceiver: "+Le.element(e.element()),o,t.data());r.onReceive()(e,u)})})])}},Bo=[Ut("menu"),Ut("selectedMenu")],Io=[Ut("item"),Ut("selectedItem")],Ho=mr.objOfOnly(Io.concat(Bo)),No=mr.objOfOnly(Io),Vo={menuFields:c.constant(Bo),itemFields:c.constant(Io),schema:c.constant(Ho),itemSchema:c.constant(No)},jo=Kt("initSize",[Ut("numColumns"),Ut("numRows")]),Po=function(n,e,t){var r=gt();return nr(e,e,t,mr.valueOf(function(t){return ze.value(function(){return pt(n,e,r),t.apply(undefined,arguments)})}))},Lo={initSize:c.constant(jo),itemMarkers:function(){return zt("markers",Vo.itemSchema())},menuMarkers:function(){return zt("markers",Vo.schema())},tieredMenuMarkers:function(){return Kt("markers",[Ut("backgroundMenu")].concat(Vo.menuFields()).concat(Vo.itemFields()))},markers:function(n){return Kt("markers",X.map(n,Ut))},onHandler:function(n){return Po("onHandler",n,wt.defaulted(c.noop))},onKeyboardHandler:function(n){return Po("onKeyboardHandler",n,wt.defaulted(N.none))},onStrictHandler:function(n){return Po("onHandler",n,wt.strict())},onStrictKeyboardHandler:function(n){return Po("onKeyboardHandler",n,wt.strict())},output:function(n,e){return er(n,c.constant(e))},snapshot:function(n){return er(n,c.identity)}},Wo=[zt("channels",mr.setOf(ze.value,mr.objOfOnly([Lo.onStrictHandler("onReceive"),Jt("schema",mr.anyValue())])))],Uo=qr.create({fields:Wo,name:"receiving",active:Fo}),zo=function(n,e){var t=$o(n,e),r=e.aria();r.update()(n,r,t)},Ko=function(n,e,t){co.add(n.element(),e.toggleClass()),zo(n,e)},Go=function(n,e,t){co.remove(n.element(),e.toggleClass()),zo(n,e)},$o=function(n,e){return co.has(n.element(),e.toggleClass())},qo={onLoad:function(n,e,t){(e.selected()?Ko:Go)(n,e,t)},toggle:function(n,e,t){co.toggle(n.element(),e.toggleClass()),zo(n,e)},isOn:$o,on:Ko,off:Go},_o={exhibit:function(n,e,t){return Fr.nu({})},events:function(n,e){var t=Hr(n,e,qo.toggle),r=Nr(n,e,qo.onLoad);return xr.derive(X.flatten([n.toggleOnExecute()?[t]:[],[r]]))}},Xo=function(n,e,t){Ne.set(n.element(),"aria-expanded",t)},Yo={button:["aria-pressed"],"input:checkbox":["aria-checked"]},Jo={button:["aria-pressed"],listbox:["aria-pressed","aria-expanded"],menuitemcheckbox:["aria-checked"]},Qo={updatePressed:function(n,e,t){Ne.set(n.element(),"aria-pressed",t),e.syncWithExpanded()&&Xo(n,e,t)},updateSelected:function(n,e,t){Ne.set(n.element(),"aria-selected",t)},updateChecked:function(n,e,t){Ne.set(n.element(),"aria-checked",t)},updateExpanded:Xo,updateAuto:function(n,e,t){var r=function(n){var e=n.element();if(Ne.has(e,"role")){var t=Ne.get(e,"role");return it.readOptFrom(Jo,t)}return N.none()}(n).orThunk(function(){return e=n.element(),t=ye.name(e),r="input"===t&&Ne.has(e,"type")?":"+Ne.get(e,"type"):"",it.readOptFrom(Yo,t+r);var e,t,r}).getOr([]);X.each(r,function(e){Ne.set(n.element(),e,t)})}},Zo=[Jt("selected",!1),Ut("toggleClass"),Jt("toggleOnExecute",!0),Qt("aria",{mode:"none"},mr.choose("mode",{pressed:[Jt("syncWithExpanded",!1),Lo.output("update",Qo.updatePressed)],checked:[Lo.output("update",Qo.updateChecked)],expanded:[Lo.output("update",Qo.updateExpanded)],selected:[Lo.output("update",Qo.updateSelected)],none:[Lo.output("update",c.noop)]}))],ni=qr.create({fields:Zo,name:"toggling",active:_o,apis:qo}),ei=function(n,e){return Uo.config({channels:it.wrap(Ro.formatChanged(),{onReceive:function(t,r){r.command===n&&e(t,r.state)}})})},ti=function(n){return Uo.config({channels:it.wrap(Ro.orientationChanged(),{onReceive:n})})},ri=function(n,e){return{key:n,value:{onReceive:e}}},oi="tinymce-mobile",ii={resolve:function(n){return oi+"-"+n},prefix:c.constant(oi)},ui={focus:function(n,e){e.ignore()||(Oo(n.element()),e.onFocus()(n))},blur:function(n,e){e.ignore()||To(n.element())},isFocused:function(n){return xo(n.element())}},ai={exhibit:function(n,e){return e.ignore()?Fr.nu({}):Fr.nu({attributes:{tabindex:"-1"}})},events:function(n){return xr.derive([xr.run(ln.focus(),function(e,t){ui.focus(e,n),t.stop()})])}},ci=[Lo.onHandler("onFocus"),Jt("ignore",!1)],si=qr.create({fields:ci,name:"focusing",active:ai,apis:ui}),fi={BACKSPACE:c.constant([8]),TAB:c.constant([9]),ENTER:c.constant([13]),SHIFT:c.constant([16]),CTRL:c.constant([17]),ALT:c.constant([18]),CAPSLOCK:c.constant([20]),ESCAPE:c.constant([27]),SPACE:c.constant([32]),PAGEUP:c.constant([33]),PAGEDOWN:c.constant([34]),END:c.constant([35]),HOME:c.constant([36]),LEFT:c.constant([37]),UP:c.constant([38]),RIGHT:c.constant([39]),DOWN:c.constant([40]),INSERT:c.constant([45]),DEL:c.constant([46]),META:c.constant([91,93,224]),F10:c.constant([121])},li=function(n,e,t,r){var o=n+e;return o>r?t:o<t?r:o},di=function(n,e,t){return n<=e?e:n>=t?t:n},mi=function(n,e,t){return X.filter(ie.parents(n,t),e)},pi=function(n,e){return X.filter(ie.siblings(n),e)},gi=function(n){return Xn.all(n)},hi=function(n,e,t){return mi(n,function(n){return Xn.is(n,e)},t)},vi=function(n,e){return pi(n,function(n){return Xn.is(n,e)})},yi=function(n,e){return Xn.all(e,n)},bi=function(n,e,t){return bo.ancestor(n,function(n){return Xn.is(n,e)},t)},wi=function(n){return Xn.one(n)},Si=bi,xi=function(n,e){return Xn.one(e,n)},Oi=function(n,e,t){return go(Xn.is,bi,n,e,t)},Ti=function(n,e,t){var r=yi(n.element(),"."+e.highlightClass());X.each(r,function(t){co.remove(t,e.highlightClass()),n.getSystem().getByDom(t).each(function(t){e.onDehighlight()(n,t)})})},ki=function(n,e,t,r){var o=Ci(n,e,t,r);Ti(n,e),co.add(r.element(),e.highlightClass()),o||e.onHighlight()(n,r)},Ci=function(n,e,t,r){return co.has(r.element(),e.highlightClass())},Ei=function(n,e,t,r){var o=yi(n.element(),"."+e.itemClass());return N.from(o[r]).fold(function(){return ze.error("No element found with index "+r)},n.getSystem().getByDom)},Di=function(n,e,t){return xi(n.element(),"."+e.itemClass()).bind(n.getSystem().getByDom)},Ai=function(n,e,t){var r=yi(n.element(),"."+e.itemClass());return(r.length>0?N.some(r[r.length-1]):N.none()).bind(n.getSystem().getByDom)},Mi=function(n,e,t,r){var o=yi(n.element(),"."+e.itemClass());return X.findIndex(o,function(n){return co.has(n,e.highlightClass())}).bind(function(e){var t=li(e,r,0,o.length-1);return n.getSystem().getByDom(o[t])})},Ri={dehighlightAll:Ti,dehighlight:function(n,e,t,r){var o=Ci(n,e,t,r);co.remove(r.element(),e.highlightClass()),o&&e.onDehighlight()(n,r)},highlight:ki,highlightFirst:function(n,e,t){Di(n,e,t).each(function(r){ki(n,e,t,r)})},highlightLast:function(n,e,t){Ai(n,e,t).each(function(r){ki(n,e,t,r)})},highlightAt:function(n,e,t,r){Ei(n,e,t,r).fold(function(n){throw new Error(n)},function(r){ki(n,e,t,r)})},highlightBy:function(n,e,t,r){var o=yi(n.element(),"."+e.itemClass()),i=ut(X.map(o,function(e){return n.getSystem().getByDom(e).toOption()}));X.find(i,r).each(function(r){ki(n,e,t,r)})},isHighlighted:Ci,getHighlighted:function(n,e,t){return xi(n.element(),"."+e.highlightClass()).bind(n.getSystem().getByDom)},getFirst:Di,getLast:Ai,getPrevious:function(n,e,t){return Mi(n,e,0,-1)},getNext:function(n,e,t){return Mi(n,e,0,1)}},Fi=[Ut("highlightClass"),Ut("itemClass"),Lo.onHandler("onHighlight"),Lo.onHandler("onDehighlight")],Bi=qr.create({fields:Fi,name:"highlighting",apis:Ri}),Ii=function(){return{get:function(n){return Co(n.element())},set:function(n,e){n.getSystem().triggerFocus(e,n.element())}}},Hi=function(){return{get:function(n){return Bi.getHighlighted(n).map(function(n){return n.element()})},set:function(n,e){n.getSystem().getByDom(e).fold(c.noop,function(e){Bi.highlight(n,e)})}}},Ni=function(n){return!0===n.raw().shiftKey},Vi=function(n){return!0===n.raw().ctrlKey},ji={inSet:function(n){return function(e){return X.contains(n,e.raw().which)}},and:function(n){return function(e){return X.forall(n,function(n){return n(e)})}},is:function(n){return function(e){return e.raw().which===n}},isShift:Ni,isNotShift:c.not(Ni),isControl:Vi,isNotControl:c.not(Vi)},Pi=function(n,e){return{matches:n,classification:e}},Li=function(n,e){return X.find(n,function(n){return n.matches(e)}).map(function(n){return n.classification})},Wi=function(n,e,t,r,o,i){var u=function(n,e,r,o){var i=t(n,e,r,o);return Li(i,e.event()).bind(function(t){return t(n,e,r,o)})},a={schema:function(){return n.concat([Jt("focusManager",Ii()),Lo.output("handler",a),Lo.output("state",e)])},processKey:u,toEvents:function(n,e){var t=r(n,e),o=xr.derive(i.map(function(t){return xr.run(ln.focus(),function(r,o){t(r,n,e,o),o.stop()})}).toArray().concat([xr.run(s.keydown(),function(t,r){u(t,r,n,e).each(function(n){r.stop()})})]));return vn.deepMerge(t,o)},toApis:o};return a},Ui={cyclePrev:function(n,e,t){var r=X.reverse(n.slice(0,e)),o=X.reverse(n.slice(e+1));return X.find(r.concat(o),t)},cycleNext:function(n,e,t){var r=n.slice(0,e),o=n.slice(e+1);return X.find(o.concat(r),t)},tryPrev:function(n,e,t){var r=X.reverse(n.slice(0,e));return X.find(r,t)},tryNext:function(n,e,t){var r=n.slice(e+1);return X.find(r,t)}},zi={isSupported:function(n){return n.style!==undefined}},Ki=function(n,e,t){if(!mn.isString(t))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);zi.isSupported(n)&&n.style.setProperty(e,t)},Gi=function(n,e){zi.isSupported(n)&&n.style.removeProperty(e)},$i=function(n,e,t){var r=n.dom();Ki(r,e,t)},qi=function(n,e){return zi.isSupported(n)?n.style.getPropertyValue(e):""},_i=function(n,e){var t=n.dom(),r=qi(t,e);return N.from(r).filter(function(n){return n.length>0})},Xi={copy:function(n,e){var t=n.dom(),r=e.dom();zi.isSupported(t)&&zi.isSupported(r)&&(r.style.cssText=t.style.cssText)},set:$i,preserve:function(n,e){var t=Ne.get(n,"style"),r=e(n);return(t===undefined?Ne.remove:Ne.set)(n,"style",t),r},setAll:function(n,e){var t=n.dom();On.each(e,function(n,e){Ki(t,e,n)})},setOptions:function(n,e){var t=n.dom();On.each(e,function(n,e){n.fold(function(){Gi(t,e)},function(n){Ki(t,e,n)})})},remove:function(n,e){var t=n.dom();Gi(t,e),Ne.has(n,"style")&&""===en(Ne.get(n,"style"))&&Ne.remove(n,"style")},get:function(n,e){var t=n.dom(),r=window.getComputedStyle(t).getPropertyValue(e),o=""!==r||Se.inBody(n)?r:qi(t,e);return null===o?undefined:o},getRaw:_i,getAllRaw:function(n){var e={},t=n.dom();if(zi.isSupported(t))for(var r=0;r<t.style.length;r++){var o=t.style.item(r);e[o]=t.style[o]}return e},isValidValue:function(n,e,t){var r=Wn.fromTag(n);return $i(r,e,t),_i(r,e).isSome()},reflow:function(n){return n.dom().offsetWidth},transfer:function(n,e,t){ye.isElement(n)&&ye.isElement(e)&&X.each(t,function(t){var r,o;r=e,_i(n,o=t).each(function(n){_i(r,o).isNone()&&$i(r,o,n)})})}};function Yi(n,e){var t=function(t){var r=e(t);if(r<=0||null===r){var o=Xi.get(t,n);return parseFloat(o)||0}return r},r=function(n,e){return X.foldl(e,function(e,t){var r=Xi.get(n,t),o=r===undefined?0:parseInt(r,10);return isNaN(o)?e:e+o},0)};return{set:function(e,t){if(!mn.isNumber(t)&&!t.match(/^[0-9]+$/))throw n+".set accepts only positive integer values. Value was "+t;var r=e.dom();zi.isSupported(r)&&(r.style[n]=t+"px")},get:t,getOuter:t,aggregate:r,max:function(n,e,t){var o=r(n,t);return e>o?e-o:0}}}var Ji=Yi("height",function(n){return Se.inBody(n)?n.dom().getBoundingClientRect().height:n.dom().offsetHeight}),Qi=function(n){return Ji.get(n)},Zi=function(n){var e=[qt("onEscape"),qt("onEnter"),Jt("selector",'[data-alloy-tabstop="true"]'),Jt("firstTabstop",0),Jt("useTabstopAt",c.constant(!0)),qt("visibilitySelector")].concat([n]),t=function(n,e){var t=n.visibilitySelector().bind(function(n){return Oi(e,n)}).getOr(e);return Qi(t)>0},r=function(n,e,r,o,i){return i(e,r,function(n){return t(e=o,r=n)&&e.useTabstopAt()(r);var e,r}).fold(function(){return o.cyclic()?N.some(!0):N.none()},function(e){return o.focusManager().set(n,e),N.some(!0)})},o=function(n,e,t,o){var i,u,a=yi(n.element(),t.selector());return(i=n,u=t,u.focusManager().get(i).bind(function(n){return Oi(n,u.selector())})).bind(function(e){return X.findIndex(a,c.curry(Jn,e)).bind(function(e){return r(n,a,e,t,o)})})},i=c.constant([Pi(ji.and([ji.isShift,ji.inSet(fi.TAB())]),function(n,e,t,r){var i=t.cyclic()?Ui.cyclePrev:Ui.tryPrev;return o(n,0,t,i)}),Pi(ji.inSet(fi.TAB()),function(n,e,t,r){var i=t.cyclic()?Ui.cycleNext:Ui.tryNext;return o(n,0,t,i)}),Pi(ji.inSet(fi.ESCAPE()),function(n,e,t,r){return t.onEscape().bind(function(t){return t(n,e)})}),Pi(ji.and([ji.isNotShift,ji.inSet(fi.ENTER())]),function(n,e,t,r){return t.onEnter().bind(function(t){return t(n,e)})})]),u=c.constant({}),a=c.constant({});return Wi(e,Kr.init,i,u,a,N.some(function(n,e,r){var o,i,u,a;(o=n,i=e,u=yi(o.element(),i.selector()),a=X.filter(u,function(n){return t(i,n)}),N.from(a[i.firstTabstop()])).each(function(t){e.focusManager().set(n,t)})}))},nu=Zi(er("cyclic",c.constant(!1))),eu=Zi(er("cyclic",c.constant(!0))),tu=function(n){return"input"===ye.name(n)&&"radio"!==Ne.get(n,"type")||"textarea"===ye.name(n)},ru=function(n,e,t){return tu(t)&&ji.inSet(fi.SPACE())(e.event())?N.none():(An(n,t,ln.execute()),N.some(!0))},ou=[Jt("execute",ru),Jt("useSpace",!1),Jt("useEnter",!0),Jt("useControlEnter",!1),Jt("useDown",!1)],iu=function(n,e,t,r){return t.execute()(n,e,n.element())},uu=c.constant({}),au=c.constant({}),cu=Wi(ou,Kr.init,function(n,e,t,r){var o=t.useSpace()&&!tu(n.element())?fi.SPACE():[],i=t.useEnter()?fi.ENTER():[],u=t.useDown()?fi.DOWN():[],a=o.concat(i).concat(u);return[Pi(ji.inSet(a),iu)].concat(t.useControlEnter()?[Pi(ji.and([ji.isControl,ji.inSet(fi.ENTER())]),iu)]:[])},uu,au,N.none()),su={flatgrid:function(n){var e=po(N.none());return zr({readState:c.constant({}),setGridSize:function(n,t){e.set(N.some({numRows:c.constant(n),numColumns:c.constant(t)}))},getNumRows:function(){return e.get().map(function(n){return n.numRows()})},getNumColumns:function(){return e.get().map(function(n){return n.numColumns()})}})},init:function(n){return n.state()(n)}},fu=function(n){return"rtl"===Xi.get(n,"direction")?"rtl":"ltr"},lu=function(n,e){return function(t){return"rtl"===fu(t)?e:n}},du=function(n){return function(e,t,r,o){var i=n(e.element());return pu(i,e,t,r,o)}},mu=function(n){return function(e,t,r,o){return pu(n,e,t,r,o)}},pu=function(n,e,t,r,o){return r.focusManager().get(e).bind(function(t){return n(e.element(),t,r,o)}).map(function(n){return r.focusManager().set(e,n),!0})},gu=function(n,e){var t=lu(e,n);return du(t)},hu=function(n,e){var t=lu(n,e);return du(t)},vu=mu,yu=mu,bu=mu,wu=In.immutableBag(["index","candidates"],[]),Su=function(n,e){return X.findIndex(n,e).map(function(e){return wu({index:e,candidates:n})})},xu=function(n){var e,t=n.dom();return!((e=t).offsetWidth<=0&&e.offsetHeight<=0)},Ou=function(n,e,t,r){var o=c.curry(Jn,e),i=yi(n,t),u=X.filter(i,xu);return Su(u,o)},Tu=function(n,e,t){return Ou(n,e,t,xu)},ku=function(n,e){return X.findIndex(n,function(n){return Jn(e,n)})},Cu=function(n,e,t,r){return r(Math.floor(e/t),e%t).bind(function(e){var r=e.row()*t+e.column();return r>=0&&r<n.length?N.some(n[r]):N.none()})},Eu=function(n,e,t,r,o){return Cu(n,e,r,function(e,i){var u=e===t-1?n.length-e*r:r,a=li(i,o,0,u-1);return N.some({row:c.constant(e),column:c.constant(a)})})},Du=function(n,e,t,r,o){return Cu(n,e,r,function(e,i){var u=li(e,o,0,t-1),a=u===t-1?n.length-u*r:r,s=di(i,0,a-1);return N.some({row:c.constant(u),column:c.constant(s)})})},Au=function(n,e,t,r){return Du(n,e,t,r,1)},Mu=function(n,e,t,r){return Du(n,e,t,r,-1)},Ru=function(n,e,t,r){return Eu(n,e,t,r,-1)},Fu=function(n,e,t,r){return Eu(n,e,t,r,1)},Bu=[Ut("selector"),Jt("execute",ru),Lo.onKeyboardHandler("onEscape"),Jt("captureTab",!1),Lo.initSize()],Iu=function(n){return function(e,t,r,o){return Tu(e,t,r.selector()).bind(function(e){return n(e.candidates(),e.index(),o.getNumRows().getOr(r.initSize().numRows()),o.getNumColumns().getOr(r.initSize().numColumns()))})}},Hu=function(n,e,t,r){return t.captureTab()?N.some(!0):N.none()},Nu=Iu(Ru),Vu=Iu(Fu),ju=Iu(Mu),Pu=Iu(Au),Lu=c.constant([Pi(ji.inSet(fi.LEFT()),hu(Nu,Vu)),Pi(ji.inSet(fi.RIGHT()),gu(Nu,Vu)),Pi(ji.inSet(fi.UP()),vu(ju)),Pi(ji.inSet(fi.DOWN()),yu(Pu)),Pi(ji.and([ji.isShift,ji.inSet(fi.TAB())]),Hu),Pi(ji.and([ji.isNotShift,ji.inSet(fi.TAB())]),Hu),Pi(ji.inSet(fi.ESCAPE()),function(n,e,t,r){return t.onEscape()(n,e)}),Pi(ji.inSet(fi.SPACE().concat(fi.ENTER())),function(n,e,t,r){return(o=n,i=t,i.focusManager().get(o).bind(function(n){return Oi(n,i.selector())})).bind(function(r){return t.execute()(n,e,r)});var o,i})]),Wu=c.constant({}),Uu=Wi(Bu,su.flatgrid,Lu,Wu,{},N.some(function(n,e,t){xi(n.element(),e.selector()).each(function(t){e.focusManager().set(n,t)})})),zu=function(n,e,t,r){return Tu(n,t,e,c.constant(!0)).bind(function(n){var e=n.index(),t=n.candidates(),o=li(e,r,0,t.length-1);return N.from(t[o])})},Ku=[Ut("selector"),Jt("getInitial",N.none),Jt("execute",ru),Jt("executeOnMove",!1)],Gu=function(n,e,t){return(r=n,o=t,o.focusManager().get(r).bind(function(n){return Oi(n,o.selector())})).bind(function(r){return t.execute()(n,e,r)});var r,o},$u=function(n,e,t){return zu(n,t.selector(),e,-1)},qu=function(n,e,t){return zu(n,t.selector(),e,1)},_u=function(n){return function(e,t,r){return n(e,t,r).bind(function(){return r.executeOnMove()?Gu(e,t,r):N.some(!0)})}},Xu=c.constant({}),Yu=c.constant({}),Ju=Wi(Ku,Kr.init,function(n){return[Pi(ji.inSet(fi.LEFT().concat(fi.UP())),_u(hu($u,qu))),Pi(ji.inSet(fi.RIGHT().concat(fi.DOWN())),_u(gu($u,qu))),Pi(ji.inSet(fi.ENTER()),Gu),Pi(ji.inSet(fi.SPACE()),Gu)]},Xu,Yu,N.some(function(n,e){e.getInitial()(n).or(xi(n.element(),e.selector())).each(function(t){e.focusManager().set(n,t)})})),Qu=In.immutableBag(["rowIndex","columnIndex","cell"],[]),Zu=function(n,e,t){return N.from(n[e]).bind(function(n){return N.from(n[t]).map(function(n){return Qu({rowIndex:e,columnIndex:t,cell:n})})})},na=function(n,e,t,r){var o=n[e].length,i=li(t,r,0,o-1);return Zu(n,e,i)},ea=function(n,e,t,r){var o=li(t,r,0,n.length-1),i=n[o].length,u=di(e,0,i-1);return Zu(n,o,u)},ta=function(n,e,t,r){var o=n[e].length,i=di(t+r,0,o-1);return Zu(n,e,i)},ra=function(n,e,t,r){var o=di(t+r,0,n.length-1),i=n[o].length,u=di(e,0,i-1);return Zu(n,o,u)},oa=function(n,e,t){return na(n,e,t,1)},ia=function(n,e,t){return na(n,e,t,-1)},ua=function(n,e,t){return ea(n,t,e,-1)},aa=function(n,e,t){return ea(n,t,e,1)},ca=function(n,e,t){return ta(n,e,t,-1)},sa=function(n,e,t){return ta(n,e,t,1)},fa=function(n,e,t){return ra(n,t,e,-1)},la=function(n,e,t){return ra(n,t,e,1)},da=[Kt("selectors",[Ut("row"),Ut("cell")]),Jt("cycles",!0),Jt("previousSelector",N.none),Jt("execute",ru)],ma=function(n,e){return function(t,r,o){var i=o.cycles()?n:e;return Oi(r,o.selectors().row()).bind(function(n){var e=yi(n,o.selectors().cell());return ku(e,r).bind(function(e){var r=yi(t,o.selectors().row());return ku(r,n).bind(function(n){var t,u,a=(t=r,u=o,X.map(t,function(n){return yi(n,u.selectors().cell())}));return i(a,n,e).map(function(n){return n.cell()})})})})}},pa=ma(ia,ca),ga=ma(oa,sa),ha=ma(ua,fa),va=ma(aa,la),ya=c.constant([Pi(ji.inSet(fi.LEFT()),hu(pa,ga)),Pi(ji.inSet(fi.RIGHT()),gu(pa,ga)),Pi(ji.inSet(fi.UP()),vu(ha)),Pi(ji.inSet(fi.DOWN()),yu(va)),Pi(ji.inSet(fi.SPACE().concat(fi.ENTER())),function(n,e,t){return Co(n.element()).bind(function(r){return t.execute()(n,e,r)})})]),ba=c.constant({}),wa=c.constant({}),Sa=Wi(da,Kr.init,ya,ba,wa,N.some(function(n,e){e.previousSelector()(n).orThunk(function(){var t=e.selectors();return xi(n.element(),t.cell())}).each(function(t){e.focusManager().set(n,t)})})),xa=[Ut("selector"),Jt("execute",ru),Jt("moveOnTab",!1)],Oa=function(n,e,t){return t.focusManager().get(n).bind(function(r){return t.execute()(n,e,r)})},Ta=function(n,e,t){return zu(n,t.selector(),e,-1)},ka=function(n,e,t){return zu(n,t.selector(),e,1)},Ca=c.constant([Pi(ji.inSet(fi.UP()),bu(Ta)),Pi(ji.inSet(fi.DOWN()),bu(ka)),Pi(ji.and([ji.isShift,ji.inSet(fi.TAB())]),function(n,e,t){return t.moveOnTab()?bu(Ta)(n,e,t):N.none()}),Pi(ji.and([ji.isNotShift,ji.inSet(fi.TAB())]),function(n,e,t){return t.moveOnTab()?bu(ka)(n,e,t):N.none()}),Pi(ji.inSet(fi.ENTER()),Oa),Pi(ji.inSet(fi.SPACE()),Oa)]),Ea=c.constant({}),Da=c.constant({}),Aa=Wi(xa,Kr.init,Ca,Ea,Da,N.some(function(n,e,t){xi(n.element(),e.selector()).each(function(t){e.focusManager().set(n,t)})})),Ma=[Lo.onKeyboardHandler("onSpace"),Lo.onKeyboardHandler("onEnter"),Lo.onKeyboardHandler("onShiftEnter"),Lo.onKeyboardHandler("onLeft"),Lo.onKeyboardHandler("onRight"),Lo.onKeyboardHandler("onTab"),Lo.onKeyboardHandler("onShiftTab"),Lo.onKeyboardHandler("onUp"),Lo.onKeyboardHandler("onDown"),Lo.onKeyboardHandler("onEscape"),qt("focusIn")],Ra=c.constant({}),Fa=c.constant({}),Ba=Wi(Ma,Kr.init,function(n,e,t){return[Pi(ji.inSet(fi.SPACE()),t.onSpace()),Pi(ji.and([ji.isNotShift,ji.inSet(fi.ENTER())]),t.onEnter()),Pi(ji.and([ji.isShift,ji.inSet(fi.ENTER())]),t.onShiftEnter()),Pi(ji.and([ji.isShift,ji.inSet(fi.TAB())]),t.onShiftTab()),Pi(ji.and([ji.isNotShift,ji.inSet(fi.TAB())]),t.onTab()),Pi(ji.inSet(fi.UP()),t.onUp()),Pi(ji.inSet(fi.DOWN()),t.onDown()),Pi(ji.inSet(fi.LEFT()),t.onLeft()),Pi(ji.inSet(fi.RIGHT()),t.onRight()),Pi(ji.inSet(fi.SPACE()),t.onSpace()),Pi(ji.inSet(fi.ESCAPE()),t.onEscape())]},Ra,Fa,N.some(function(n,e){return e.focusIn().bind(function(t){return t(n,e)})})),Ia={acyclic:nu.schema(),cyclic:eu.schema(),flow:Ju.schema(),flatgrid:Uu.schema(),matrix:Sa.schema(),execution:cu.schema(),menu:Aa.schema(),special:Ba.schema()},Ha=qr.createModes({branchKey:"mode",branches:Ia,name:"keying",active:{events:function(n,e){return n.handler().toEvents(n,e)}},apis:{focusIn:function(n){n.getSystem().triggerFocus(n.element(),n.element())},setGridSize:function(n,e,t,r,o){it.hasKey(t,"setGridSize")?t.setGridSize(r,o):console.error("Layout does not support setGridSize")}},state:su}),Na=function(n,e){return Zt(n,{},X.map(e,function(e){return $t(e.name(),"Cannot configure "+e.name()+" for "+n)}).concat([er("dump",c.identity)]))},Va=function(n){return n.dump()},ja=0,Pa=function(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++ja+String(e)},La=Pa("alloy-premade"),Wa=Pa("api"),Ua={apiConfig:c.constant(Wa),makeApi:function(n){return kr(function(e){var t=Array.prototype.slice.call(arguments,0),r=e.config(Wa);return n.apply(undefined,[r].concat(t))},n)},premade:function(n){return it.wrap(La,n)},getPremade:function(n){return it.readOptFrom(n,La)}},za=Ke([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Ka=Jt("factory",{sketch:c.identity}),Ga=Jt("schema",[]),$a=Ut("name"),qa=nr("pname","pname",wt.defaultedThunk(function(n){return"<alloy."+Pa(n.name)+">"}),mr.anyValue()),_a=Jt("defaults",c.constant({})),Xa=Jt("overrides",c.constant({})),Ya=mr.objOf([Ka,Ga,$a,qa,_a,Xa]),Ja=mr.objOf([Ka,Ga,$a,_a,Xa]),Qa=mr.objOf([Ka,Ga,$a,qa,_a,Xa]),Za=mr.objOf([Ka,Ga,$a,Ut("unit"),qa,_a,Xa]),nc=function(n,e){return function(t){var r=mr.asStructOrDie("Converting part type",e,t);return n(r)}},ec={required:nc(za.required,Ya),external:nc(za.external,Ja),optional:nc(za.optional,Qa),group:nc(za.group,Za),asNamedPart:function(n){return n.fold(N.some,N.none,N.some,N.some)},name:function(n){var e=function(n){return n.name()};return n.fold(e,e,e,e)},asCommon:function(n){return n.fold(c.identity,c.identity,c.identity,c.identity)},original:c.constant("entirety")},tc="placeholder",rc=Ke([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),oc=function(n,e,t,r){return t.uiType===tc?(i=t,u=r,(o=n).exists(function(n){return n!==i.owner})?rc.single(!0,c.constant(i)):it.readOptFrom(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+On.keys(u)+"]\nNamespace: "+o.getOr("none")+"\nSpec: "+Ot(i,null,2))},function(n){return n.replace()})):rc.single(!1,c.constant(t));var o,i,u},ic=function(n,e,t,r){return oc(n,0,t,r).fold(function(o,i){var u=i(e,t.config,t.validated),a=it.readOptFrom(u,"components").getOr([]),c=X.bind(a,function(t){return ic(n,e,t,r)});return[vn.deepMerge(u,{components:c})]},function(n,r){return r(e,t.config,t.validated)})},uc=function(n,e,t,r){return X.bind(t,function(t){return ic(n,e,t,r)})},ac={single:rc.single,multiple:rc.multiple,isSubstitute:function(n){return X.contains([tc],n)},placeholder:c.constant(tc),substituteAll:uc,substitutePlaces:function(n,e,t,r){var o=On.map(r,function(n,e){return t=e,r=n,o=!1,{name:c.constant(t),required:function(){return r.fold(function(n,e){return n},function(n,e){return n})},used:function(){return o},replace:function(){if(!0===o)throw new Error("Trying to use the same placeholder more than once: "+t);return o=!0,r}};var t,r,o}),i=uc(n,e,t,o);return On.each(o,function(t){if(!1===t.used()&&t.required())throw new Error("Placeholder: "+t.name()+" was not found in components list\nNamespace: "+n.getOr("none")+"\nComponents: "+Ot(e.components(),null,2))}),i},singleReplace:function(n,e){return e.fold(function(e,t){return[t(n)]},function(e,t){return t(n)})}},cc=function(n,e,t,r){var o=t;return vn.deepMerge(e.defaults()(n,t,r),t,{uid:n.partUids()[e.name()]},e.overrides()(n,t,r),{"debug.sketcher":it.wrap("part-"+e.name(),o)})},sc=function(n,e,t){var r={},o={};return X.each(t,function(n){n.fold(function(n){r[n.pname()]=ac.single(!0,function(e,t,r){return n.factory().sketch(cc(e,n,t,r))})},function(n){var t=e.parts()[n.name()]();o[n.name()]=c.constant(cc(e,n,t[ec.original()]()))},function(n){r[n.pname()]=ac.single(!1,function(e,t,r){return n.factory().sketch(cc(e,n,t,r))})},function(n){r[n.pname()]=ac.multiple(!0,function(e,t,r){var o=e[n.name()]();return X.map(o,function(t){return n.factory().sketch(vn.deepMerge(n.defaults()(e,t),t,n.overrides()(e,t)))})})})}),{internals:c.constant(r),externals:c.constant(o)}},fc=function(n,e){return{uiType:ac.placeholder(),owner:n,name:e}},lc=function(n){return X.map(n,ec.name)},dc=function(n,e,t){var r=e.partUids()[t];return n.getSystem().getByUid(r).toOption()},mc=function(n,e){var t=lc(e);return it.wrapAll(X.map(t,function(e){return{key:e,value:n+"-"+e}}))},pc=function(n,e){var t={};return X.each(e,function(e){ec.asNamedPart(e).each(function(e){var r=fc(n,e.pname());t[e.name()]=function(t){var o=mr.asRawOrDie("Part: "+e.name()+" in "+n,mr.objOf(e.schema()),t);return vn.deepMerge(r,{config:t,validated:o})}})}),t},gc=function(n,e,t){return{uiType:ac.placeholder(),owner:n,name:e,config:t,validated:{}}},hc=function(n){return X.bind(n,function(n){return n.fold(N.none,N.some,N.none,N.none).map(function(n){return Kt(n.name(),n.schema().concat([Lo.snapshot(ec.original())]))}).toArray()})},vc=function(n,e,t){return sc(n,e,t)},yc=function(n,e,t){return ac.substitutePlaces(N.some(n),e,e.components(),t)},bc=function(n){return nr("partUids","partUids",wt.mergeWithThunk(function(e){return mc(e.uid,n)}),mr.anyValue())},wc=function(n,e){var t=n.getSystem();return On.map(e.partUids(),function(n,e){return c.constant(t.getByUid(n))})},Sc=dc,xc=function(n,e,t){return dc(n,e,t).getOrDie("Could not find part: "+t)},Oc={prefix:c.constant("alloy-id-"),idAttr:c.constant("data-alloy-id")},Tc=Oc.prefix(),kc=Oc.idAttr(),Cc={revoke:function(n){Ne.remove(n,kc)},write:function(n,e){var t=Pa(Tc+n);return Ne.set(e,kc,t),t},writeOnly:function(n,e){Ne.set(n,kc,e)},read:function(n){var e=ye.isElement(n)?Ne.get(n,kc):null;return N.from(e)},find:function(n,e){return xi(n,e)},generate:function(n){return Pa(n)},attribute:c.constant(kc)},Ec=function(n,e,t,r){return(e.length>0?[Kt("parts",e)]:[]).concat([Ut("uid"),Jt("dom",{}),Jt("components",[]),Lo.snapshot("originalSpec"),Jt("debug.sketcher",{})]).concat(t)},Dc=function(n,e,t,r,o){var i=Ec(0,r,o);return mr.asStructOrDie(n+" [SpecSchema]",mr.objOfOnly(i.concat(e)),t)},Ac=function(n){return vn.deepMerge({uid:Cc.generate("uid")},n)},Mc=function(n,e,t,r){var o=Ac(r),i=Dc(n,e,o,[],[]);return vn.deepMerge(t(i,o),{"debug.sketcher":it.wrap(n,r)})},Rc=function(n,e,t,r,o){var i=Ac(o),u=hc(t),a=bc(t),c=Dc(n,e,i,u,[a]),s=vc(n,c,t),f=yc(n,c,s.internals());return vn.deepMerge(r(c,f,i,s.externals()),{"debug.sketcher":it.wrap(n,o)})},Fc=mr.objOfOnly([Ut("name"),Ut("factory"),Ut("configFields"),Jt("apis",{}),Jt("extraApis",{})]),Bc=mr.objOfOnly([Ut("name"),Ut("factory"),Ut("configFields"),Ut("partFields"),Jt("apis",{}),Jt("extraApis",{})]),Ic=function(n){var e=mr.asRawOrDie("Sketcher for "+n.name,Fc,n),t=On.map(e.apis,Ua.makeApi),r=On.map(e.extraApis,function(n,e){return Tr(n,e)});return vn.deepMerge({name:c.constant(e.name),partFields:c.constant([]),configFields:c.constant(e.configFields),sketch:function(n){return Mc(e.name,e.configFields,e.factory,n)}},t,r)},Hc=function(n){var e=mr.asRawOrDie("Sketcher for "+n.name,Bc,n),t=pc(e.name,e.partFields),r=On.map(e.apis,Ua.makeApi),o=On.map(e.extraApis,function(n,e){return Tr(n,e)});return vn.deepMerge({name:c.constant(e.name),partFields:c.constant(e.partFields),configFields:c.constant(e.configFields),sketch:function(n){return Rc(e.name,e.configFields,e.partFields,e.factory,n)},parts:c.constant(t)},r,o)},Nc=function(n){var e=function(n,e){e.stop(),Dn(n)},t=sn.detect().deviceType.isTouch()?[xr.run(ln.tap(),e)]:[xr.run(s.click(),e),xr.run(s.mousedown(),function(n,e){e.cut()})];return xr.derive(X.flatten([n.map(function(n){return xr.run(ln.execute(),function(e,t){n(e),t.stop()})}).toArray(),t]))},Vc=Ic({name:"Button",factory:function(n,e){var t=Nc(n.action()),r=it.readOptFrom(n.dom(),"attributes").bind(it.readOpt("type")),o=it.readOptFrom(n.dom(),"tag");return{uid:n.uid(),dom:n.dom(),components:n.components(),events:t,behaviours:vn.deepMerge(qr.derive([si.config({}),Ha.config({mode:"execution",useSpace:!0,useEnter:!0})]),Va(n.buttonBehaviours())),domModification:{attributes:vn.deepMerge(r.fold(function(){return o.is("button")?{type:"button"}:{}},function(n){return{}}),{role:n.role().getOr("button")})},eventOrder:n.eventOrder()}},configFields:[Jt("uid",undefined),Ut("dom"),Jt("components",[]),Na("buttonBehaviours",[si,Ha]),qt("action"),qt("role"),Jt("eventOrder",{})]}),jc={events:function(n){return xr.derive([xr.abort(s.selectstart(),c.constant(!0))])},exhibit:function(n,e){return Fr.nu({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}},Pc=qr.create({fields:[],name:"unselecting",active:jc}),Lc=function(n){var e,t,r,o=Wn.fromHtml(n),i=ie.children(o),u=(t=(e=o).dom().attributes!==undefined?e.dom().attributes:[],X.foldl(t,function(n,e){return"class"===e.name?n:vn.deepMerge(n,it.wrap(e.name,e.value))},{})),a=(r=o,Array.prototype.slice.call(r.dom().classList,0)),c=0===i.length?{}:{innerHtml:Ae(o)};return vn.deepMerge({tag:ye.name(o),classes:a,attributes:u},c)},Wc=Lc,Uc=function(n){var e=Z(n,{prefix:ii.prefix()});return Wc(e)},zc={dom:Uc,spec:function(n){return{dom:Uc(n)}}},Kc=function(n){return qr.derive([ni.config({toggleClass:ii.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),ei(n,function(n,e){(e?ni.on:ni.off)(n)})])},Gc=function(n,e,t){return Vc.sketch({dom:zc.dom('<span class="${prefix}-toolbar-button ${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:e,buttonBehaviours:vn.deepMerge(qr.derive([Pc.config({})]),t)})},$c={forToolbar:Gc,forToolbarCommand:function(n,e){return Gc(e,function(){n.execCommand(e)},{})},forToolbarStateAction:function(n,e,t,r){var o=Kc(t);return Gc(e,r,o)},forToolbarStateCommand:function(n,e){var t=Kc(e);return Gc(e,function(){n.execCommand(e)},t)}},qc=function(n,e,t){return Math.max(e,Math.min(t,n))},_c=function(n,e,t,r){return n<e?n:n>t?t:n===e?e-1:Math.max(e,n-r)},Xc=function(n,e,t,r){return n>t?n:n<e?e:n===t?t+1:Math.min(t,n+r)},Yc=function(n,e,t,r,o,i,u){var a=t-e;if(r<n.left)return e-1;if(r>n.right)return t+1;var c,s,f,l,d=Math.min(n.right,Math.max(r,n.left))-n.left,m=qc(d/n.width*a+e,e-1,t+1),p=Math.round(m);return i&&m>=e&&m<=t?(c=m,s=e,f=t,l=o,u.fold(function(){var n=c-s,e=Math.round(n/l)*l;return qc(s+e,s-1,f+1)},function(n){var e=(c-n)%l,t=Math.round(e/l),r=Math.floor((c-n)/l),o=Math.floor((f-n)/l),i=n+Math.min(o,r+t)*l;return Math.max(n,i)})):p},Jc="slider.change.value",Qc=sn.detect().deviceType.isTouch(),Zc=function(n){var e;return(e=n.event().raw(),Qc&&e.touches!==undefined&&1===e.touches.length?N.some(e.touches[0]):Qc&&e.touches!==undefined?N.none():Qc||e.clientX===undefined?N.none():N.some(e)).map(function(n){return n.clientX})},ns=function(n,e){En(n,Jc,{value:e})},es={setXFromEvent:function(n,e,t,r){return Zc(r).map(function(r){var o,i,u,a;return o=n,u=r,a=Yc(t,(i=e).min(),i.max(),u,i.stepSize(),i.snapToGrid(),i.snapStart()),ns(o,a),r})},setToLedge:function(n,e){ns(n,e.min()-1)},setToRedge:function(n,e){ns(n,e.max()+1)},moveLeftFromRedge:function(n,e){ns(n,e.max())},moveRightFromLedge:function(n,e){ns(n,e.min())},moveLeft:function(n,e){var t=_c(e.value().get(),e.min(),e.max(),e.stepSize());ns(n,t)},moveRight:function(n,e){var t=Xc(e.value().get(),e.min(),e.max(),e.stepSize());ns(n,t)},changeEvent:c.constant(Jc)},ts=sn.detect().deviceType.isTouch(),rs=function(n,e){return ec.optional({name:n+"-edge",overrides:function(n){var t=xr.derive([xr.runActionExtra(s.touchstart(),e,[n])]),r=xr.derive([xr.runActionExtra(s.mousedown(),e,[n]),xr.runActionExtra(s.mousemove(),function(n,t){t.mouseIsDown().get()&&e(n,t)},[n])]);return{events:ts?t:r}}})},os=[rs("left",es.setToLedge),rs("right",es.setToRedge),ec.required({name:"thumb",defaults:c.constant({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:xr.derive([xr.redirectToPart(s.touchstart(),n,"spectrum"),xr.redirectToPart(s.touchmove(),n,"spectrum"),xr.redirectToPart(s.touchend(),n,"spectrum")])}}}),ec.required({schema:[er("mouseIsDown",function(){return po(!1)})],name:"spectrum",overrides:function(n){var e=function(e,t){var r=e.element().dom().getBoundingClientRect();es.setXFromEvent(e,n,r,t)},t=xr.derive([xr.run(s.touchstart(),e),xr.run(s.touchmove(),e)]),r=xr.derive([xr.run(s.mousedown(),e),xr.run(s.mousemove(),function(t,r){n.mouseIsDown().get()&&e(t,r)})]);return{behaviours:qr.derive(ts?[]:[Ha.config({mode:"special",onLeft:function(e){return es.moveLeft(e,n),N.some(!0)},onRight:function(e){return es.moveRight(e,n),N.some(!0)}}),si.config({})]),events:ts?t:r}}})],is={onLoad:function(n,e,t){e.store().manager().onLoad(n,e,t)},onUnload:function(n,e,t){e.store().manager().onUnload(n,e,t)},setValue:function(n,e,t,r){e.store().manager().setValue(n,e,t,r)},getValue:function(n,e,t){return e.store().manager().getValue(n,e,t)}},us={events:function(n,e){var t=n.resetOnDom()?[xr.runOnAttached(function(t,r){is.onLoad(t,n,e)}),xr.runOnDetached(function(t,r){is.onUnload(t,n,e)})]:[Nr(n,e,is.onLoad)];return xr.derive(t)}},as={memory:function(){var n=po(null);return zr({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})},dataset:function(){var n=po({});return zr({readState:function(){return{mode:"dataset",dataset:n.get()}},set:n.set,get:n.get})},manual:function(){return zr({readState:function(){}})},init:function(n){return n.store().manager().state(n)}},cs=function(n,e,t,r){e.store().getDataKey(),t.set({}),e.store().setData()(n,r),e.onSetValue()(n,r)},ss=[qt("initialValue"),Ut("getFallbackEntry"),Ut("getDataKey"),Ut("setData"),Lo.output("manager",{setValue:cs,getValue:function(n,e,t){var r=e.store().getDataKey()(n),o=t.get();return it.readOptFrom(o,r).fold(function(){return e.store().getFallbackEntry()(r)},function(n){return n})},onLoad:function(n,e,t){e.store().initialValue().each(function(r){cs(n,e,t,r)})},onUnload:function(n,e,t){t.set({})},state:as.dataset})],fs=[Ut("getValue"),Jt("setValue",c.noop),qt("initialValue"),Lo.output("manager",{setValue:function(n,e,t,r){e.store().setValue()(n,r),e.onSetValue()(n,r)},getValue:function(n,e,t){return e.store().getValue()(n)},onLoad:function(n,e,t){e.store().initialValue().each(function(t){e.store().setValue()(n,t)})},onUnload:c.noop,state:Kr.init})],ls=[qt("initialValue"),Lo.output("manager",{setValue:function(n,e,t,r){t.set(r),e.onSetValue()(n,r)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store().initialValue().each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:as.memory})],ds=[Qt("store",{mode:"memory"},mr.choose("mode",{memory:ls,manual:fs,dataset:ss})),Lo.onHandler("onSetValue"),Jt("resetOnDom",!1)],ms=qr.create({fields:ds,name:"representing",active:us,apis:is,extra:{setValueFrom:function(n,e){var t=ms.getValue(e);ms.setValue(n,t)}},state:as}),ps=sn.detect().deviceType.isTouch(),gs=[Ut("min"),Ut("max"),Jt("stepSize",1),Jt("onChange",c.noop),Jt("onInit",c.noop),Jt("onDragStart",c.noop),Jt("onDragEnd",c.noop),Jt("snapToGrid",!1),qt("snapStart"),Ut("getInitialValue"),Na("sliderBehaviours",[Ha,ms]),er("value",function(n){return po(n.min)})].concat(ps?[]:[er("mouseIsDown",function(){return po(!1)})]),hs=Yi("width",function(n){return n.dom().offsetWidth}),vs=function(n,e){hs.set(n,e)},ys=function(n){return hs.get(n)},bs=sn.detect().deviceType.isTouch(),ws=Hc({name:"Slider",configFields:gs,partFields:os,factory:function(n,e,t,r){var o=n.max()-n.min(),i=function(n){var e=n.element().dom().getBoundingClientRect();return(e.left+e.right)/2},u=function(e){return xc(e,n,"thumb")},a=function(e){var t,r,u,a,c=xc(e,n,"spectrum").element().dom().getBoundingClientRect(),s=e.element().dom().getBoundingClientRect(),f=(t=e,r=c,(a=(u=n).value().get())<u.min()?Sc(t,u,"left-edge").fold(function(){return 0},function(n){return i(n)-r.left}):a>u.max()?Sc(t,u,"right-edge").fold(function(){return r.width},function(n){return i(n)-r.left}):(u.value().get()-u.min())/o*r.width);return c.left-s.left+f},f=function(n){var e=a(n),t=u(n),r=ys(t.element())/2;Xi.set(t.element(),"left",e-r+"px")},l=function(e,t){var r=n.value().get(),o=u(e);return r!==t||Xi.getRaw(o.element(),"left").isNone()?(n.value().set(t),f(e),n.onChange()(e,o,t),N.some(!0)):N.none()},d=bs?[xr.run(s.touchstart(),function(e,t){n.onDragStart()(e,u(e))}),xr.run(s.touchend(),function(e,t){n.onDragEnd()(e,u(e))})]:[xr.run(s.mousedown(),function(e,t){t.stop(),n.onDragStart()(e,u(e)),n.mouseIsDown().set(!0)}),xr.run(s.mouseup(),function(e,t){n.onDragEnd()(e,u(e)),n.mouseIsDown().set(!1)})];return{uid:n.uid(),dom:n.dom(),components:e,behaviours:vn.deepMerge(qr.derive(X.flatten([bs?[]:[Ha.config({mode:"special",focusIn:function(e){return Sc(e,n,"spectrum").map(Ha.focusIn).map(c.constant(!0))}})],[ms.config({store:{mode:"manual",getValue:function(e){return n.value().get()}}})]])),Va(n.sliderBehaviours())),events:xr.derive([xr.run(es.changeEvent(),function(n,e){l(n,e.event().value())}),xr.runOnAttached(function(e,t){n.value().set(n.getInitialValue()());var r=u(e);f(e),n.onInit()(e,r,n.value().get())})].concat(d)),apis:{resetToMin:function(e){l(e,n.min())},resetToMax:function(e){l(e,n.max())},refresh:f},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,e){n.resetToMin(e)},resetToMax:function(n,e){n.resetToMax(e)},refresh:function(n,e){n.refresh(e)}}}),Ss=function(n,e,t){return $c.forToolbar(e,function(){var r=t();n.setContextToolbar([{label:e+" group",items:r}])},{})},xs=function(n){return[(e=n,t=function(n){return n<0?"black":n>360?"white":"hsl("+n+", 100%, 50%)"},ws.sketch({dom:zc.dom('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[ws.parts()["left-edge"](zc.spec('<div class="${prefix}-hue-slider-black"></div>')),ws.parts().spectrum({dom:zc.dom('<div class="${prefix}-slider-gradient-container"></div>'),components:[zc.spec('<div class="${prefix}-slider-gradient"></div>')],behaviours:qr.derive([ni.config({toggleClass:ii.resolve("thumb-active")})])}),ws.parts()["right-edge"](zc.spec('<div class="${prefix}-hue-slider-white"></div>')),ws.parts().thumb({dom:zc.dom('<div class="${prefix}-slider-thumb"></div>'),behaviours:qr.derive([ni.config({toggleClass:ii.resolve("thumb-active")})])})],onChange:function(n,r,o){var i=t(o);Xi.set(r.element(),"background-color",i),e.onChange(n,r,i)},onDragStart:function(n,e){ni.on(e)},onDragEnd:function(n,e){ni.off(e)},onInit:function(n,e,r){var o=t(r);Xi.set(e.element(),"background-color",o)},stepSize:10,min:0,max:360,getInitialValue:e.getInitialValue,sliderBehaviours:qr.derive([ti(ws.refresh)])}))];var e,t},Os=function(n,e){var t={onChange:function(n,t,r){e.undoManager.transact(function(){e.formatter.apply("forecolor",{value:r}),e.nodeChanged()})},getInitialValue:function(){return-1}};return Ss(n,"color",function(){return xs(t)})},Ts=mr.objOfOnly([Ut("getInitialValue"),Ut("onChange"),Ut("category"),Ut("sizes")]),ks=function(n){var e=mr.asRawOrDie("SizeSlider",Ts,n);return ws.sketch({dom:{tag:"div",classes:[ii.resolve("slider-"+e.category+"-size-container"),ii.resolve("slider"),ii.resolve("slider-size-container")]},onChange:function(n,t,r){var o;(o=r)>=0&&o<e.sizes.length&&e.onChange(r)},onDragStart:function(n,e){ni.on(e)},onDragEnd:function(n,e){ni.off(e)},min:0,max:e.sizes.length-1,stepSize:1,getInitialValue:e.getInitialValue,snapToGrid:!0,sliderBehaviours:qr.derive([ti(ws.refresh)]),components:[ws.parts().spectrum({dom:zc.dom('<div class="${prefix}-slider-size-container"></div>'),components:[zc.spec('<div class="${prefix}-slider-size-line"></div>')]}),ws.parts().thumb({dom:zc.dom('<div class="${prefix}-slider-thumb"></div>'),behaviours:qr.derive([ni.config({toggleClass:ii.resolve("thumb-active")})])})]})},Cs=function(n,e,t){for(var r=n.dom(),o=mn.isFunction(t)?t:c.constant(!1);r.parentNode;){r=r.parentNode;var i=Wn.fromDom(r),u=e(i);if(u.isSome())return u;if(o(i))break}return N.none()},Es=function(n,e,t){return e(n).orThunk(function(){return t(n)?N.none():Cs(n,e,t)})},Ds=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],As=function(n){var e,t,r=n.selection.getStart(),o=Wn.fromDom(r),i=Wn.fromDom(n.getBody()),u=(e=function(n){return Jn(i,n)},t=o,(ye.isElement(t)?N.some(t):ie.parent(t)).map(function(n){return Es(n,function(n){return Xi.getRaw(n,"font-size")},e).getOrThunk(function(){return Xi.get(n,"font-size")})}).getOr(""));return X.find(Ds,function(n){return u===n}).getOr("medium")},Ms={candidates:c.constant(Ds),get:function(n){var e,t=As(n);return(e=t,X.findIndex(Ds,function(n){return n===e})).getOr(2)},apply:function(n,e){var t;(t=e,N.from(Ds[t])).each(function(e){var t,r;r=e,As(t=n)!==r&&t.execCommand("fontSize",!1,r)})}},Rs=Ms.candidates(),Fs=function(n){return[zc.spec('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),(e=n,ks({onChange:e.onChange,sizes:Rs,category:"font",getInitialValue:e.getInitialValue})),zc.spec('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')];var e},Bs=function(n,e){var t={onChange:function(n){Ms.apply(e,n)},getInitialValue:function(){return Ms.get(e)}};return Ss(n,"font-size",function(){return Fs(t)})},Is=function(n){var e=it.hasKey(n,"uid")?n.uid:Cc.generate("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).fold(N.none,N.some)},asSpec:function(){return vn.deepMerge(n,{uid:e})}}};function Hs(n,e){return Vs(document.createElement("canvas"),n,e)}function Ns(n){return n.getContext("2d")}function Vs(n,e,t){return n.width=e,n.height=t,n}var js={create:Hs,clone:function(n){var e;return Ns(e=Hs(n.width,n.height)).drawImage(n,0,0),e},resize:Vs,get2dContext:Ns,get3dContext:function(n){var e=null;try{e=n.getContext("webgl")||n.getContext("experimental-webgl")}catch(t){}return e||(e=null),e}},Ps={getWidth:function(n){return n.naturalWidth||n.width},getHeight:function(n){return n.naturalHeight||n.height}},Ls=window.Promise?window.Promise:function(){var n=function(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],c(n,t(i,this),t(u,this))},e=n.immediateFn||"function"==typeof setImmediate&&setImmediate||function(n){setTimeout(n,1)};function t(n,e){return function(){n.apply(e,arguments)}}var r=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)};function o(n){var t=this;null!==this._state?e(function(){var e=t._state?n.onFulfilled:n.onRejected;if(null!==e){var r;try{r=e(t._value)}catch(o){return void n.reject(o)}n.resolve(r)}else(t._state?n.resolve:n.reject)(t._value)}):this._deferreds.push(n)}function i(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void c(t(e,n),t(i,this),t(u,this))}this._state=!0,this._value=n,a.call(this)}catch(r){u.call(this,r)}}function u(n){this._state=!1,this._value=n,a.call(this)}function a(){for(var n=0,e=this._deferreds.length;n<e;n++)o.call(this,this._deferreds[n]);this._deferreds=null}function c(n,e,t){var r=!1;try{n(function(n){r||(r=!0,e(n))},function(n){r||(r=!0,t(n))})}catch(o){if(r)return;r=!0,t(o)}}return n.prototype["catch"]=function(n){return this.then(null,n)},n.prototype.then=function(e,t){var r=this;return new n(function(n,i){o.call(r,new function(n,e,t,r){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.resolve=t,this.reject=r}(e,t,n,i))})},n.all=function(){var e=Array.prototype.slice.call(1===arguments.length&&r(arguments[0])?arguments[0]:arguments);return new n(function(n,t){if(0===e.length)return n([]);var r=e.length;function o(i,u){try{if(u&&("object"==typeof u||"function"==typeof u)){var a=u.then;if("function"==typeof a)return void a.call(u,function(n){o(i,n)},t)}e[i]=u,0==--r&&n(e)}catch(c){t(c)}}for(var i=0;i<e.length;i++)o(i,e[i])})},n.resolve=function(e){return e&&"object"==typeof e&&e.constructor===n?e:new n(function(n){n(e)})},n.reject=function(e){return new n(function(n,t){t(e)})},n.race=function(e){return new n(function(n,t){for(var r=0,o=e.length;r<o;r++)e[r].then(n,t)})},n}();function Ws(){return new(Pn.getOrDie("FileReader"))}var Us={atob:function(n){return Pn.getOrDie("atob")(n)},requestAnimationFrame:function(n){Pn.getOrDie("requestAnimationFrame")(n)}};function zs(n){return new Ls(function(e,t){var r=URL.createObjectURL(n),o=new Image,i=function(){o.removeEventListener("load",u),o.removeEventListener("error",a)};function u(){i(),e(o)}function a(){i(),t("Unable to load data of type "+n.type+": "+r)}o.addEventListener("load",u),o.addEventListener("error",a),o.src=r,o.complete&&u()})}function Ks(n){return new Ls(function(e,t){var r=new XMLHttpRequest;r.open("GET",n,!0),r.responseType="blob",r.onload=function(){200==this.status&&e(this.response)},r.onerror=function(){var n,e=this;t(0===this.status?((n=new Error("No access to download image")).code=18,n.name="SecurityError",n):new Error("Error "+e.status+" downloading image"))},r.send()})}function Gs(n){var e=n.split(","),t=/data:([^;]+)/.exec(e[0]);if(!t)return N.none();for(var r,o,i,u=t[1],a=e[1],c=Us.atob(a),s=c.length,f=Math.ceil(s/1024),l=new Array(f),d=0;d<f;++d){for(var m=1024*d,p=Math.min(m+1024,s),g=new Array(p-m),h=m,v=0;h<p;++v,++h)g[v]=c[h].charCodeAt(0);l[d]=(r=g,new(Pn.getOrDie("Uint8Array"))(r))}return N.some((o=l,i={type:u},new(Pn.getOrDie("Blob"))(o,i)))}function $s(n){return new Ls(function(e,t){Gs(n).fold(function(){t("uri is not base64: "+n)},e)})}function qs(n){return new Ls(function(e){var t=new Ws;t.onloadend=function(){e(t.result)},t.readAsDataURL(n)})}var _s={blobToImage:zs,imageToBlob:function(n){return(e=n,new Ls(function(n){e.complete?n(e):e.addEventListener("load",function t(){e.removeEventListener("load",t),n(e)})})).then(function(n){var e=n.src;return 0===e.indexOf("blob:")?Ks(e):0===e.indexOf("data:")?$s(e):Ks(e)});var e},blobToArrayBuffer:function(n){return new Ls(function(e){var t=new Ws;t.onloadend=function(){e(t.result)},t.readAsArrayBuffer(n)})},blobToDataUri:qs,blobToBase64:function(n){return qs(n).then(function(n){return n.split(",")[1]})},dataUriToBlobSync:Gs,canvasToBlob:function(n,e,t){return e=e||"image/png",HTMLCanvasElement.prototype.toBlob?new Ls(function(r){n.toBlob(function(n){r(n)},e,t)}):$s(n.toDataURL(e,t))},canvasToDataURL:function(n,e,t){return e=e||"image/png",n.then(function(n){return n.toDataURL(e,t)})},blobToCanvas:function(n){return zs(n).then(function(n){var e,t;return e=n,URL.revokeObjectURL(e.src),t=js.create(Ps.getWidth(n),Ps.getHeight(n)),js.get2dContext(t).drawImage(n,0,0),t})},uriToBlob:function(n){return 0===n.indexOf("blob:")?Ks(n):0===n.indexOf("data:")?$s(n):null}},Xs=function(n){return _s.blobToBase64(n)},Ys=function(n){var e=Is({dom:{tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},events:xr.derive([xr.cutter(s.click()),xr.run(s.change(),function(e,t){var r,o,i;(r=t,o=r.event(),i=o.raw().target.files||o.raw().dataTransfer.files,N.from(i[0])).each(function(e){var t,r;t=n,Xs(r=e).then(function(n){t.undoManager.transact(function(){var e=t.editorUpload.blobCache,o=e.create(Pa("mceu"),r,n);e.add(o);var i=t.dom.createHTML("img",{src:o.blobUri()});t.insertContent(i)})})})})])});return Vc.sketch({dom:zc.dom('<span class="${prefix}-toolbar-button ${prefix}-icon-image ${prefix}-icon"></span>'),components:[e.asSpec()],action:function(n){e.get(n).element().dom().click()}})},Js=function(n){return n.dom().textContent},Qs=function(n,e){n.dom().textContent=e},Zs=function(n){return n.length>0},nf=function(n){return n===undefined||null===n?"":n},ef=function(n,e,t){return t.text.filter(Zs).fold(function(){return Ne.get(t=n,"href")===Js(t)?N.some(e):N.none();var t},N.some)},tf=function(n){var e=Wn.fromDom(n.selection.getStart());return Oi(e,"a")},rf={getInfo:function(n){return tf(n).fold(function(){return{url:"",text:n.selection.getContent({format:"text"}),title:"",target:"",link:N.none()}},function(n){return t=Js(e=n),r=Ne.get(e,"href"),o=Ne.get(e,"title"),i=Ne.get(e,"target"),{url:nf(r),text:t!==r?nf(t):"",title:nf(o),target:nf(i),link:N.some(e)};var e,t,r,o,i})},applyInfo:function(n,e){e.url.filter(Zs).fold(function(){var t;t=n,e.link.bind(c.identity).each(function(n){t.execCommand("unlink")})},function(t){var r,o,i=(r=e,(o={}).href=t,r.title.filter(Zs).each(function(n){o.title=n}),r.target.filter(Zs).each(function(n){o.target=n}),o);e.link.bind(c.identity).fold(function(){var r=e.text.filter(Zs).getOr(t);n.insertContent(n.dom.createHTML("a",i,n.dom.encode(r)))},function(n){var r=ef(n,t,e);Ne.setAll(n,i),r.each(function(e){Qs(n,e)})})})},query:tf},of=sn.detect(),uf=function(n,e){var t=e.selection.getRng();n(),e.selection.setRng(t)},af=function(n,e){(of.os.isAndroid()?uf:c.apply)(e,n)},cf=function(n,e){var t=xr.derive(e);return qr.create({fields:[Ut("enabled")],name:n,active:{events:c.constant(t)}})},sf={events:cf,config:function(n,e){return{key:n,value:{config:{},me:cf(n,e),configAsRaw:c.constant({}),initialConfig:{},state:qr.noState()}}}},ff={getCurrent:function(n,e,t){return e.find()(n)}},lf=[Ut("find")],df=qr.create({fields:lf,name:"composing",apis:ff}),mf=Ic({name:"Container",factory:function(n,e){return{uid:n.uid(),dom:vn.deepMerge({tag:"div",attributes:{role:"presentation"}},n.dom()),components:n.components(),behaviours:Va(n.containerBehaviours()),events:n.events(),domModification:n.domModification(),eventOrder:n.eventOrder()}},configFields:[Jt("components",[]),Na("containerBehaviours",[]),Jt("events",{}),Jt("domModification",{}),Jt("eventOrder",{})]}),pf=Ic({name:"DataField",factory:function(n,e){return{uid:n.uid(),dom:n.dom(),behaviours:vn.deepMerge(qr.derive([ms.config({store:{mode:"memory",initialValue:n.getInitialValue()()}}),df.config({find:N.some})]),Va(n.dataBehaviours())),events:xr.derive([xr.runOnAttached(function(e,t){ms.setValue(e,n.getInitialValue()())})])}},configFields:[Ut("uid"),Ut("dom"),Ut("getInitialValue"),Na("dataBehaviours",[ms,df])]}),gf=function(n,e){if(e===undefined)throw new Error("Value.set was undefined");n.dom().value=e},hf=function(n){return n.dom().value},vf=[qt("data"),Jt("inputAttributes",{}),Jt("inputStyles",{}),Jt("type","input"),Jt("tag","input"),Jt("inputClasses",[]),Lo.onHandler("onSetValue"),Jt("styles",{}),qt("placeholder"),Jt("eventOrder",{}),Na("inputBehaviours",[ms,si]),Jt("selectOnFocus",!0)],yf={schema:c.constant(vf),behaviours:function(n){return vn.deepMerge(qr.derive([ms.config({store:{mode:"manual",initialValue:n.data().getOr(undefined),getValue:function(n){return hf(n.element())},setValue:function(n,e){hf(n.element())!==e&&gf(n.element(),e)}},onSetValue:n.onSetValue()}),si.config({onFocus:!1===n.selectOnFocus()?c.noop:function(n){var e=n.element(),t=hf(e);e.dom().setSelectionRange(0,t.length)}})]),Va(n.inputBehaviours()))},dom:function(n){return{tag:n.tag(),attributes:vn.deepMerge(it.wrapAll([{key:"type",value:n.type()}].concat(n.placeholder().map(function(n){return{key:"placeholder",value:n}}).toArray())),n.inputAttributes()),styles:n.inputStyles(),classes:n.inputClasses()}}},bf=Ic({name:"Input",configFields:yf.schema(),factory:function(n,e){return{uid:n.uid(),dom:yf.dom(n),components:[],behaviours:yf.behaviours(n),eventOrder:n.eventOrder()}}}),wf={exhibit:function(n,e){return Fr.nu({attributes:it.wrapAll([{key:e.tabAttr(),value:"true"}])})}},Sf=[Jt("tabAttr","data-alloy-tabstop")],xf=qr.create({fields:Sf,name:"tabstopping",active:wf}),Of=function(n,e){var t=Is(bf.sketch({placeholder:e,onSetValue:function(n,e){Cn(n,s.input())},inputBehaviours:qr.derive([df.config({find:N.some}),xf.config({}),Ha.config({mode:"execution"})]),selectOnFocus:!1})),r=Is(Vc.sketch({dom:zc.dom('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(n){var e=t.get(n);ms.setValue(e,"")}}));return{name:n,spec:mf.sketch({dom:zc.dom('<div class="${prefix}-input-container"></div>'),components:[t.asSpec(),r.asSpec()],containerBehaviours:qr.derive([ni.config({toggleClass:ii.resolve("input-container-empty")}),df.config({find:function(n){return N.some(t.get(n))}}),sf.config("input-clearing",[xr.run(s.input(),function(n){var e=t.get(n);(ms.getValue(e).length>0?ni.off:ni.on)(n)})])])})}},Tf=function(n){return{name:n,spec:pf.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return N.none()}})}},kf=["input","button","textarea"],Cf=function(n){return X.contains(kf,ye.name(n.element()))},Ef=function(n){Ne.set(n.element(),"disabled","disabled")},Df=function(n){Ne.remove(n.element(),"disabled")},Af=function(n){Ne.set(n.element(),"aria-disabled","true")},Mf=function(n){Ne.set(n.element(),"aria-disabled","false")},Rf=function(n,e,t){e.disableClass().each(function(e){co.add(n.element(),e)}),(Cf(n)?Ef:Af)(n)},Ff={enable:function(n,e,t){e.disableClass().each(function(e){co.remove(n.element(),e)}),(Cf(n)?Df:Mf)(n)},disable:Rf,isDisabled:function(n){return Cf(n)?Ne.has(n.element(),"disabled"):"true"===Ne.get(n.element(),"aria-disabled")},onLoad:function(n,e,t){e.disabled()&&Rf(n,e,t)}},Bf={exhibit:function(n,e,t){return Fr.nu({classes:e.disabled()?e.disableClass().map(X.pure).getOr([]):[]})},events:function(n,e){return xr.derive([xr.abort(ln.execute(),function(t,r){return Ff.isDisabled(t,n,e)}),Nr(n,e,Ff.onLoad)])}},If=[Jt("disabled",!1),qt("disableClass")],Hf=qr.create({fields:If,name:"disabling",active:Bf,apis:Ff}),Nf=[Na("formBehaviours",[ms])],Vf=function(n){return"<alloy.field."+n+">"},jf=function(n,e,t){return vn.deepMerge({"debug.sketcher":{Form:t},uid:n.uid(),dom:n.dom(),components:e,behaviours:vn.deepMerge(qr.derive([ms.config({store:{mode:"manual",getValue:function(e){var t=wc(e,n);return On.map(t,function(n,e){return n().bind(df.getCurrent).map(ms.getValue)})},setValue:function(e,t){On.each(t,function(t,r){Sc(e,n,r).each(function(n){df.getCurrent(n).each(function(n){ms.setValue(n,t)})})})}}})]),Va(n.formBehaviours())),apis:{getField:function(e,t){return Sc(e,n,t).bind(df.getCurrent)}}})},Pf=(Ua.makeApi(function(n,e,t){return n.getField(e,t)}),function(n){var e,t=(e=[],{field:function(n,t){return e.push(n),gc("form",Vf(n),t)},record:function(){return e}}),r=n(t),o=t.record(),i=X.map(o,function(n){return ec.required({name:n,pname:Vf(n)})});return Rc("form",Nf,i,jf,r)}),Lf=function(n){var e=po(N.none()),t=function(){e.get().each(n)};return{clear:function(){t(),e.set(N.none())},isSet:function(){return e.get().isSome()},set:function(n){t(),e.set(N.some(n))}}},Wf={destroyable:function(){return Lf(function(n){n.destroy()})},unbindable:function(){return Lf(function(n){n.unbind()})},api:function(){var n=po(N.none()),e=function(){n.get().each(function(n){n.destroy()})};return{clear:function(){e(),n.set(N.none())},isSet:function(){return n.get().isSome()},set:function(t){e(),n.set(N.some(t))},run:function(e){n.get().each(e)}}},value:function(){var n=po(N.none());return{clear:function(){n.set(N.none())},set:function(e){n.set(N.some(e))},isSet:function(){return n.get().isSome()},on:function(e){n.get().each(e)}}}},Uf=function(n){return{xValue:n,points:[]}},zf=function(n,e){if(e===n.xValue)return n;var t=e-n.xValue>0?1:-1,r={direction:t,xValue:e};return{xValue:e,points:(0===n.points.length?[]:n.points[n.points.length-1].direction===t?n.points.slice(0,n.points.length-1):n.points).concat([r])}},Kf=function(n){if(0===n.points.length)return 0;var e=n.points[0].direction,t=n.points[n.points.length-1].direction;return-1===e&&-1===t?-1:1===e&&1===t?1:0},Gf=function(n){var e="navigateEvent",t=mr.objOf([Ut("fields"),Jt("maxFieldIndex",n.fields.length-1),Ut("onExecute"),Ut("getInitialValue"),er("state",function(){return{dialogSwipeState:Wf.value(),currentScreen:po(0)}})]),r=mr.asRawOrDie("SerialisedDialog",t,n),o=function(n,t,r){return Vc.sketch({dom:zc.dom('<span class="${prefix}-icon-'+t+' ${prefix}-icon"></span>'),action:function(t){En(t,e,{direction:n})},buttonBehaviours:qr.derive([Hf.config({disableClass:ii.resolve("toolbar-navigation-disabled"),disabled:!r})])})},i=function(n,e){var t=yi(n.element(),"."+ii.resolve("serialised-dialog-screen"));xi(n.element(),"."+ii.resolve("serialised-dialog-chain")).each(function(n){r.state.currentScreen.get()+e>=0&&r.state.currentScreen.get()+e<t.length&&(Xi.getRaw(n,"left").each(function(r){var o=parseInt(r,10),i=ys(t[0]);Xi.set(n,"left",o-e*i+"px")}),r.state.currentScreen.set(r.state.currentScreen.get()+e))})},u=function(n){var e=yi(n.element(),"input");N.from(e[r.state.currentScreen.get()]).each(function(e){n.getSystem().getByDom(e).each(function(e){Rn(n,e.element())})});var t=c.get(n);Bi.highlightAt(t,r.state.currentScreen.get())},a=Is(Pf(function(n){return{dom:zc.dom('<div class="${prefix}-serialised-dialog"></div>'),components:[mf.sketch({dom:zc.dom('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:X.map(r.fields,function(e,t){return t<=r.maxFieldIndex?mf.sketch({dom:zc.dom('<div class="${prefix}-serialised-dialog-screen"></div>'),components:X.flatten([[o(-1,"previous",t>0)],[n.field(e.name,e.spec)],[o(1,"next",t<r.maxFieldIndex)]])}):n.field(e.name,e.spec)})})],formBehaviours:qr.derive([ti(function(n,e){var t;t=e,xi(n.element(),"."+ii.resolve("serialised-dialog-chain")).each(function(n){Xi.set(n,"left",-r.state.currentScreen.get()*t.width+"px")})}),Ha.config({mode:"special",focusIn:function(n){u(n)},onTab:function(n){return i(n,1),N.some(!0)},onShiftTab:function(n){return i(n,-1),N.some(!0)}}),sf.config("form-events",[xr.runOnAttached(function(n,e){r.state.currentScreen.set(0),r.state.dialogSwipeState.clear();var t=c.get(n);Bi.highlightFirst(t),r.getInitialValue(n).each(function(e){ms.setValue(n,e)})}),xr.runOnExecute(r.onExecute),xr.run(s.transitionend(),function(n,e){"left"===e.event().raw().propertyName&&u(n)}),xr.run(e,function(n,e){var t=e.event().direction();i(n,t)})])])}})),c=Is({dom:zc.dom('<div class="${prefix}-dot-container"></div>'),behaviours:qr.derive([Bi.config({highlightClass:ii.resolve("dot-active"),itemClass:ii.resolve("dot-item")})]),components:X.bind(r.fields,function(n,e){return e<=r.maxFieldIndex?[zc.spec('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})});return{dom:zc.dom('<div class="${prefix}-serializer-wrapper"></div>'),components:[a.asSpec(),c.asSpec()],behaviours:qr.derive([Ha.config({mode:"special",focusIn:function(n){var e=a.get(n);Ha.focusIn(e)}}),sf.config("serializer-wrapper-events",[xr.run(s.touchstart(),function(n,e){r.state.dialogSwipeState.set(Uf(e.event().raw().touches[0].clientX))}),xr.run(s.touchmove(),function(n,e){r.state.dialogSwipeState.on(function(n){e.event().prevent(),r.state.dialogSwipeState.set(zf(n,e.event().raw().touches[0].clientX))})}),xr.run(s.touchend(),function(n){r.state.dialogSwipeState.on(function(e){var t=a.get(n),r=-1*Kf(e);i(t,r)})})])])}},$f=f(function(n,e){return[{label:"the link group",items:[Gf({fields:[Of("url","Type or paste URL"),Of("text","Link text"),Of("title","Link title"),Of("target","Link target"),Tf("link")],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return N.some(rf.getInfo(e))},onExecute:function(t){var r=ms.getValue(t);rf.applyInfo(e,r),n.restoreToolbar(),e.focus()}})]}]}),qf=function(n,e){return $c.forToolbarStateAction(e,"link","link",function(){var t=$f(n,e);n.setContextToolbar(t),af(e,function(){n.focusToolbar()}),rf.query(e).each(function(n){e.selection.select(n.dom())})})},_f=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],Xf=function(n,e){var t=X.map(e,function(n){return nr(n.name(),n.name(),wt.asOption(),mr.objOf([Ut("config"),Jt("state",Kr)]))}),r=mr.asStruct("component.behaviours",mr.objOf(t),n.behaviours).fold(function(e){throw new Error(mr.formatError(e)+"\nComplete spec:\n"+Ot(n,null,2))},c.identity);return{list:e,data:On.map(r,function(n){var e=n();return c.constant(e.map(function(n){return{config:n.config(),state:n.state().init(n.config())}}))})}},Yf=function(n){return n.list},Jf=function(n){return n.data},Qf=function(n,e){return Xf(n,e)},Zf=function(n){var e,t,r,o=(e=n,t=it.readOptFrom(e,"behaviours").getOr({}),r=X.filter(On.keys(t),function(n){return t[n]!==undefined}),X.map(r,function(n){return e.behaviours[n].me}));return Qf(n,o)},nl=Ur.exactly(["getSystem","config","hasConfigured","spec","connect","disconnect","element","syncComponents","readState","components","events"]),el=Ur.exactly(["debugInfo","triggerFocus","triggerEvent","triggerEscape","addToWorld","removeFromWorld","addToGui","removeFromGui","build","getByUid","getByDom","broadcast","broadcastOn"]);function tl(n){var e=function(e){return function(){throw new Error("The component must be in a context to send: "+e+"\n"+Le.element(n().element())+" is not in context.")}};return el({debugInfo:c.constant("fake"),triggerEvent:e("triggerEvent"),triggerFocus:e("triggerFocus"),triggerEscape:e("triggerEscape"),build:e("build"),addToWorld:e("addToWorld"),removeFromWorld:e("removeFromWorld"),addToGui:e("addToGui"),removeFromGui:e("removeFromGui"),getByUid:e("getByUid"),getByDom:e("getByDom"),broadcast:e("broadcast"),broadcastOn:e("broadcastOn")})}var rl,ol,il,ul,al,cl=function(n,e){var t={};return On.each(n,function(n,r){On.each(n,function(n,o){var i=it.readOr(o,[])(t);t[o]=i.concat([e(r,n)])})}),t},sl=function(n,e){return{name:c.constant(n),modification:e}},fl=function(n,e,t){return n.length>1?ze.error('Multiple behaviours have tried to change DOM "'+e+'". The guilty behaviours are: '+Ot(X.map(n,function(n){return n.name()}))+". At this stage, this is not supported. Future releases might provide strategies for resolving this."):0===n.length?ze.value({}):ze.value(n[0].modification().fold(function(){return{}},function(n){return it.wrap(e,n)}))},ll=function(n,e){return X.foldl(n,function(t,r){var o=r.modification().getOr({});return t.bind(function(t){var r=On.mapToArray(o,function(r,o){return t[o]!==undefined?(i=e,u=o,a=n,ze.error("Mulitple behaviours have tried to change the _"+u+'_ "'+i+'". The guilty behaviours are: '+Ot(X.bind(a,function(n){return n.modification().getOr({})[u]!==undefined?[n.name()]:[]}),null,2)+". This is not currently supported.")):ze.value(it.wrap(o,r));var i,u,a});return it.consolidate(r,t)})},ze.value({})).map(function(n){return it.wrap(e,n)})},dl={classes:function(n,e){var t=X.bind(n,function(n){return n.modification().getOr([])});return ze.value(it.wrap(e,t))},attributes:ll,styles:ll,domChildren:fl,defChildren:fl,innerHtml:fl,value:fl},ml=function(n,e,t,r){var o=vn.deepMerge({},e);X.each(t,function(e){o[e.name()]=e.exhibit(n,r)});var i=cl(o,sl),u=On.map(i,function(n,e){return X.bind(n,function(n){return n.modification().fold(function(){return[]},function(e){return[n]})})}),a=On.mapToArray(u,function(n,e){return it.readOptFrom(dl,e).fold(function(){return ze.error("Unknown field type: "+e)},function(t){return t(n,e)})});return it.consolidate(a,{}).map(Fr.nu)},pl=function(n,e,t,r){var o=t.slice(0);try{var i=o.sort(function(t,o){var i=t[e](),u=o[e](),a=r.indexOf(i),c=r.indexOf(u);if(-1===a)throw new Error("The ordering for "+n+" does not have an entry for "+i+".\nOrder specified: "+Ot(r,null,2));if(-1===c)throw new Error("The ordering for "+n+" does not have an entry for "+u+".\nOrder specified: "+Ot(r,null,2));return a<c?-1:c<a?1:0});return ze.value(i)}catch(u){return ze.error([u])}},gl={nu:function(n,e){return{handler:n,purpose:c.constant(e)}},curryArgs:function(n,e){return{handler:c.curry.apply(undefined,[n.handler].concat(e)),purpose:n.purpose}},getHandler:function(n){return n.handler}},hl=function(n,e){return{name:c.constant(n),handler:c.constant(e)}},vl=function(n,e,t){var r,o,i,u=vn.deepMerge(t,(r=e,o=n,i={},X.each(r,function(n){i[n.name()]=n.handlers(o)}),i));return cl(u,hl)},yl=function(n,e,t){var r,o,i=e[t];return i?pl("Event: "+t,"name",n,i).map(function(n){var e=X.map(n,function(n){return n.handler()});return hr(e)}):(r=t,o=n,ze.error(["The event ("+r+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+Ot(X.map(o,function(n){return n.name()}),null,2)]))},bl=function(n,e){var t=On.mapToArray(n,function(n,t){return(1===n.length?ze.value(n[0].handler()):yl(n,e,t)).map(function(r){var o,i=(o=gr(r),function(n,e){var t=Array.prototype.slice.call(arguments,0);o.abort.apply(undefined,t)?e.stop():o.can.apply(undefined,t)&&o.run.apply(undefined,t)}),u=n.length>1?X.filter(e,function(e){return X.contains(n,function(n){return n.name()===e})}).join(" > "):n[0].name();return it.wrap(t,gl.nu(i,u))})});return it.consolidate(t,{})},wl=function(n,e,t,r){var o=vl(n,t,r);return bl(o,e)},Sl=function(n){return mr.asStruct("custom.definition",mr.objOfOnly([nr("dom","dom",wt.strict(),mr.objOfOnly([Ut("tag"),Jt("styles",{}),Jt("classes",[]),Jt("attributes",{}),qt("value"),qt("innerHtml")])),Ut("components"),Ut("uid"),Jt("events",{}),Jt("apis",c.constant({})),nr("eventOrder","eventOrder",wt.mergeWith({"alloy.execute":["disabling","alloy.base.behaviour","toggling"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing"]}),mr.anyValue()),qt("domModification"),Lo.snapshot("originalSpec"),Jt("debug.sketcher","unknown")]),n)},xl=function(n){var e,t={tag:n.dom().tag(),classes:n.dom().classes(),attributes:vn.deepMerge((e=n,it.wrap(Oc.idAttr(),e.uid())),n.dom().attributes()),styles:n.dom().styles(),domChildren:X.map(n.components(),function(n){return n.element()})};return Dr.nu(vn.deepMerge(t,n.dom().innerHtml().map(function(n){return it.wrap("innerHtml",n)}).getOr({}),n.dom().value().map(function(n){return it.wrap("value",n)}).getOr({})))},Ol=function(n){return n.domModification().fold(function(){return Fr.nu({})},Fr.nu)},Tl=function(n){return n.events()},kl={add:function(n,e){X.each(e,function(e){co.add(n,e)})},remove:function(n,e){X.each(e,function(e){co.remove(n,e)})},toggle:function(n,e){X.each(e,function(e){co.toggle(n,e)})},hasAll:function(n,e){return X.forall(e,function(e){return co.has(n,e)})},hasAny:function(n,e){return X.exists(e,function(e){return co.has(n,e)})},get:function(n){return uo(n)?function(n){for(var e=n.dom().classList,t=new Array(e.length),r=0;r<e.length;r++)t[r]=e.item(r);return t}(n):to(n)}},Cl=function(n){var e=Wn.fromTag(n.tag());Ne.setAll(e,n.attributes().getOr({})),kl.add(e,n.classes().getOr([])),Xi.setAll(e,n.styles().getOr({})),Me(e,n.innerHtml().getOr(""));var t=function(n){if(n.domChildren().isSome()&&n.defChildren().isSome())throw new Error("Cannot specify children and child specs! Must be one or the other.\nDef: "+Dr.defToStr(n));return n.domChildren().fold(function(){var e=n.defChildren().getOr([]);return X.map(e,El)},function(n){return n})}(n);return se.append(e,t),n.value().each(function(n){gf(e,n)}),e},El=function(n){var e=Dr.nu(n);return Cl(e)},Dl=Cl,Al=function(n){var e=function(){return h},t=po(tl(e)),r=mr.getOrDie(Sl(vn.deepMerge(n,{behaviours:undefined}))),o=Zf(n),i=Yf(o),u=Jf(o),a=xl(r),s={"alloy.base.modification":Ol(r)},f=ml(u,s,i,a).getOrDie(),l=Fr.merge(a,f),d=Dl(l),m={"alloy.base.behaviour":Tl(r)},p=wl(u,r.eventOrder(),i,m).getOrDie(),g=po(r.components()),h=nl({getSystem:t.get,config:function(e){if(e===Ua.apiConfig())return r.apis();var t=u;return(mn.isFunction(t[e.name()])?t[e.name()]:function(){throw new Error("Could not find "+e.name()+" in "+Ot(n,null,2))})()},hasConfigured:function(n){return mn.isFunction(u[n.name()])},spec:c.constant(n),readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},connect:function(n){t.set(n)},disconnect:function(){t.set(tl(e))},element:c.constant(d),syncComponents:function(){var n=ie.children(d),e=X.bind(n,function(n){return t.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});g.set(e)},components:g.get,events:c.constant(p)});return h},Ml={events:xr.derive([xr.can(ln.focus(),function(n,e){var t,r,o=e.event().originator(),i=e.event().target();return r=i,!(Jn(t=o,n.element())&&!Jn(t,r)&&(console.warn(ln.focus()+" did not get interpreted by the desired target. \nOriginator: "+Le.element(o)+"\nTarget: "+Le.element(i)+"\nCheck the "+ln.focus()+" event handlers"),1))})])},Rl=function(n){return n},Fl=function(n){var e,t,r=Rl(n),o=(e=r,t=it.readOr("components",[])(e),X.map(t,Il)),i=vn.deepMerge(Ml,r,it.wrap("components",o));return ze.value(Al(i))},Bl=function(n){var e=mr.asStructOrDie("external.component",mr.objOfOnly([Ut("element"),qt("uid")]),n),t=po(tl());e.uid().each(function(n){Cc.writeOnly(e.element(),n)});var r=nl({getSystem:t.get,config:N.none,hasConfigured:c.constant(!1),connect:function(n){t.set(n)},disconnect:function(){t.set(tl(function(){return r}))},element:c.constant(e.element()),spec:c.constant(n),readState:c.constant("No state"),syncComponents:c.noop,components:c.constant([]),events:c.constant({})});return Ua.premade(r)},Il=function(n){return Ua.getPremade(n).fold(function(){var e=vn.deepMerge({uid:Cc.generate("")},n);return Fl(e).getOrDie()},function(n){return n})},Hl={build:Il,premade:Ua.premade,external:Bl,text:function(n){var e=Wn.fromText(n);return Bl({element:e})}},Nl="alloy.item-hover",Vl="alloy.item-focus",jl={hover:c.constant(Nl),focus:c.constant(Vl),onHover:function(n){(Co(n.element()).isNone()||si.isFocused(n))&&(si.isFocused(n)||si.focus(n),En(n,Nl,{item:n}))},onFocus:function(n){En(n,Vl,{item:n})}},Pl=[Ut("data"),Ut("components"),Ut("dom"),qt("toggling"),Jt("itemBehaviours",{}),Jt("ignoreFocus",!1),Jt("domModification",{}),Lo.output("builder",function(n){return{dom:vn.deepMerge(n.dom(),{attributes:{role:n.toggling().isSome()?"menuitemcheckbox":"menuitem"}}),behaviours:vn.deepMerge(qr.derive([n.toggling().fold(ni.revoke,function(n){return ni.config(vn.deepMerge({aria:{mode:"checked"}},n))}),si.config({ignore:n.ignoreFocus(),onFocus:function(n){jl.onFocus(n)}}),Ha.config({mode:"execution"}),ms.config({store:{mode:"memory",initialValue:n.data()}})]),n.itemBehaviours()),events:xr.derive([xr.runWithTarget(ln.tapOrClick(),Dn),xr.cutter(s.mousedown()),xr.run(s.mouseover(),jl.onHover),xr.run(ln.focusItem(),si.focus)]),components:n.components(),domModification:n.domModification()}})],Ll=[Ut("dom"),Ut("components"),Lo.output("builder",function(n){return{dom:n.dom(),components:n.components(),events:xr.derive([xr.stopper(ln.focusItem())])}})],Wl=[ec.required({name:"widget",overrides:function(n){return{behaviours:qr.derive([ms.config({store:{mode:"manual",getValue:function(e){return n.data()},setValue:function(){}}})])}}})],Ul={owner:c.constant("item-widget"),parts:c.constant(Wl)},zl=[Ut("uid"),Ut("data"),Ut("components"),Ut("dom"),Jt("autofocus",!1),Jt("domModification",{}),bc(Ul.parts()),Lo.output("builder",function(n){var e=vc(Ul.owner(),n,Ul.parts()),t=yc(Ul.owner(),n,e.internals()),r=function(e){return Sc(e,n,"widget").map(function(n){return Ha.focusIn(n),n})},o=function(e,t){return tu(t.event().target())?N.none():n.autofocus()?(t.setSource(e.element()),N.none()):N.none()};return vn.deepMerge({dom:n.dom(),components:t,domModification:n.domModification(),events:xr.derive([xr.runOnExecute(function(n,e){r(n).each(function(n){e.stop()})}),xr.run(s.mouseover(),jl.onHover),xr.run(ln.focusItem(),function(e,t){n.autofocus()?r(e):si.focus(e)})]),behaviours:qr.derive([ms.config({store:{mode:"memory",initialValue:n.data()}}),si.config({onFocus:function(n){jl.onFocus(n)}}),Ha.config({mode:"special",onLeft:o,onRight:o,onEscape:function(e,t){return si.isFocused(e)||n.autofocus()?n.autofocus()?(t.setSource(e.element()),N.none()):N.none():(si.focus(e),N.some(!0))}})])})})],Kl=mr.choose("type",{widget:zl,item:Pl,separator:Ll}),Gl=[ec.group({factory:{sketch:function(n){var e=mr.asStructOrDie("menu.spec item",Kl,n);return e.builder()(e)}},name:"items",unit:"item",defaults:function(n,e){var t=Cc.generate("");return vn.deepMerge({uid:t},e)},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus(),domModification:{classes:[n.markers().item()]}}}})],$l=[Ut("value"),Ut("items"),Ut("dom"),Ut("components"),Jt("eventOrder",{}),Na("menuBehaviours",[Bi,ms,df,Ha]),Qt("movement",{mode:"menu",moveOnTab:!0},mr.choose("mode",{grid:[Lo.initSize(),Lo.output("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers().item(),initSize:{numColumns:e.initSize().numColumns(),numRows:e.initSize().numRows()},focusManager:n.focusManager()}})],menu:[Jt("moveOnTab",!0),Lo.output("config",function(n,e){return{mode:"menu",selector:"."+n.markers().item(),moveOnTab:e.moveOnTab(),focusManager:n.focusManager()}})]})),Lo.itemMarkers(),Jt("fakeFocus",!1),Jt("focusManager",Ii()),Lo.onHandler("onHighlight")],ql={name:c.constant("Menu"),schema:c.constant($l),parts:c.constant(Gl)},_l={focus:c.constant("alloy.menu-focus")},Xl={make:function(n,e,t,r){return vn.deepMerge({dom:vn.deepMerge(n.dom(),{attributes:{role:"menu"}}),uid:n.uid(),behaviours:vn.deepMerge(qr.derive([Bi.config({highlightClass:n.markers().selectedItem(),itemClass:n.markers().item(),onHighlight:n.onHighlight()}),ms.config({store:{mode:"memory",initialValue:n.value()}}),df.config({find:c.identity}),Ha.config(n.movement().config()(n,n.movement()))]),Va(n.menuBehaviours())),events:xr.derive([xr.run(jl.focus(),function(n,e){var t=e.event();n.getSystem().getByDom(t.target()).each(function(t){Bi.highlight(n,t),e.stop(),En(n,_l.focus(),{menu:n,item:t})})}),xr.run(jl.hover(),function(n,e){var t=e.event().item();Bi.highlight(n,t)})]),components:e,eventOrder:n.eventOrder()})}},Yl=Hc({name:"Menu",configFields:ql.schema(),partFields:ql.parts(),factory:Xl.make}),Jl=function(n,e){var t=ie.owner(e),r=ko(t).bind(function(n){var t=function(e){return Jn(n,e)};return t(e)?N.some(e):bo.descendant(e,t)}),o=n(e);return r.each(function(n){ko(t).filter(function(e){return Jn(e,n)}).orThunk(function(){Oo(n)})}),o},Ql=function(n,e,t,r){var o=n.getSystem().build(r);Ce.attachWith(n,o,t)},Zl=function(n,e){return n.components()},nd={append:function(n,e,t,r){Ql(n,0,ce.append,r)},prepend:function(n,e,t,r){Ql(n,0,ce.prepend,r)},remove:function(n,e,t,r){var o=Zl(n,e);X.find(o,function(n){return Jn(r.element(),n.element())}).each(Ce.detach)},set:function(n,e,t,r){Ce.detachChildren(n),Jl(function(){var e=X.map(r,n.getSystem().build);X.each(e,function(e){Ce.attach(n,e)})},n.element())},contents:Zl},ed=qr.create({fields:[],name:"replacing",apis:nd}),td=function(n,e,t,r){return it.readOptFrom(t,r).bind(function(r){return it.readOptFrom(n,r).bind(function(r){var o=td(n,e,t,r);return N.some([r].concat(o))})}).getOr([])},rd={generate:function(n,e){var t={};On.each(n,function(n,e){X.each(n,function(n){t[n]=e})});var r,o=e,i=(r=e,On.tupleMap(r,function(n,e){return{k:n,v:e}})),u=On.map(i,function(n,e){return[e].concat(td(t,o,i,e))});return On.map(t,function(n){return it.readOptFrom(u,n).getOr([n])})}},od={make:function(n,e){var t,r,o,i,u,a,s=(t=po({}),r=po({}),o=po({}),i=po(N.none()),u=po(c.constant([])),{setContents:function(n,e,a,c){i.set(N.some(n)),t.set(a),r.set(e),u.set(c);var s=c(e),f=rd.generate(s,a);o.set(f)},expand:function(n){return it.readOptFrom(t.get(),n).map(function(e){var t=it.readOptFrom(o.get(),n).getOr([]);return[e].concat(t)})},refresh:function(n){return it.readOptFrom(o.get(),n)},collapse:function(n){return it.readOptFrom(o.get(),n).bind(function(n){return n.length>1?N.some(n.slice(1)):N.none()})},lookupMenu:a=function(n){return it.readOptFrom(r.get(),n)},otherMenus:function(n){var e=u.get()(r.get());return X.difference(On.keys(e),n)},getPrimary:function(){return i.get().bind(a)},getMenus:function(){return r.get()},clear:function(){t.set({}),r.set({}),o.set({}),i.set(N.none())},isClear:function(){return i.get().isNone()}}),f=function(t){var r,o,i=(r=t,o=n.data().menus(),On.map(o,function(t,o){var i=Yl.sketch(vn.deepMerge(t,{value:o,items:t.items,markers:it.narrow(e.markers,["item","selectedItem"]),fakeFocus:n.fakeFocus(),onHighlight:n.onHighlight(),focusManager:n.fakeFocus()?Hi():Ii()}));return r.getSystem().build(i)}));return s.setContents(n.data().primary(),i,n.data().expansions(),function(n){return d(t,n)}),s.getPrimary()},l=function(n){return ms.getValue(n).value},d=function(e,t){return On.map(n.data().menus(),function(n,e){return X.bind(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})},m=function(n,e){Bi.highlight(n,e),Bi.getHighlighted(e).orThunk(function(){return Bi.getFirst(e)}).each(function(e){An(n,e.element(),ln.focusItem())})},p=function(n,e){return ut(X.map(e,n.lookupMenu))},g=function(e,t,r){return N.from(r[0]).bind(t.lookupMenu).map(function(o){var i=p(t,r.slice(1));X.each(i,function(e){co.add(e.element(),n.markers().backgroundMenu())}),Se.inBody(o.element())||ed.append(e,Hl.premade(o)),kl.remove(o.element(),[n.markers().backgroundMenu()]),m(e,o);var u=p(t,t.otherMenus(r));return X.each(u,function(t){kl.remove(t.element(),[n.markers().backgroundMenu()]),n.stayInDom()||ed.remove(e,t)}),o})},h=function(e,t){var r=l(t);return s.expand(r).bind(function(r){return N.from(r[0]).bind(s.lookupMenu).each(function(r){Se.inBody(r.element())||ed.append(e,Hl.premade(r)),n.onOpenSubmenu()(e,t,r),Bi.highlightFirst(r)}),g(e,s,r)})},v=function(e,t){var r=l(t);return s.collapse(r).bind(function(r){return g(e,s,r).map(function(r){return n.onCollapseMenu()(e,t,r),r})})},y=function(e){return function(t,r){return Oi(r.getSource(),"."+n.markers().item()).bind(function(n){return t.getSystem().getByDom(n).bind(function(n){return e(t,n)})})}},b=xr.derive([xr.run(_l.focus(),function(n,e){var t=e.event().menu();Bi.highlight(n,t)}),xr.runOnExecute(function(e,t){var r=t.event().target();return e.getSystem().getByDom(r).bind(function(t){return 0===l(t).indexOf("collapse-item")?v(e,t):h(e,t).orThunk(function(){return n.onExecute()(e,t)})})}),xr.runOnAttached(function(e,t){f(e).each(function(t){ed.append(e,Hl.premade(t)),n.openImmediately()&&(m(e,t),n.onOpenMenu()(e,t))})})].concat(n.navigateOnHover()?[xr.run(jl.hover(),function(e,t){var r,o,i=t.event().item();r=e,o=l(i),s.refresh(o).bind(function(n){return g(r,s,n)}),h(e,i),n.onHover()(e,i)})]:[]));return{uid:n.uid(),dom:n.dom(),behaviours:vn.deepMerge(qr.derive([Ha.config({mode:"special",onRight:y(function(n,e){return tu(e.element())?N.none():h(n,e)}),onLeft:y(function(n,e){return tu(e.element())?N.none():v(n,e)}),onEscape:y(function(e,t){return v(e,t).orThunk(function(){return n.onEscape()(e,t)})}),focusIn:function(n,e){s.getPrimary().each(function(e){An(n,e.element(),ln.focusItem())})}}),Bi.config({highlightClass:n.markers().selectedMenu(),itemClass:n.markers().menu()}),df.config({find:function(n){return Bi.getHighlighted(n)}}),ed.config({})]),Va(n.tmenuBehaviours())),eventOrder:n.eventOrder(),apis:{collapseMenu:function(n){Bi.getHighlighted(n).each(function(e){Bi.getHighlighted(e).each(function(e){v(n,e)})})}},events:b}},collapseItem:c.constant("collapse-item")},id=Ic({name:"TieredMenu",configFields:[Lo.onStrictKeyboardHandler("onExecute"),Lo.onStrictKeyboardHandler("onEscape"),Lo.onStrictHandler("onOpenMenu"),Lo.onStrictHandler("onOpenSubmenu"),Lo.onHandler("onCollapseMenu"),Jt("openImmediately",!0),Kt("data",[Ut("primary"),Ut("menus"),Ut("expansions")]),Jt("fakeFocus",!1),Lo.onHandler("onHighlight"),Lo.onHandler("onHover"),Lo.tieredMenuMarkers(),Ut("dom"),Jt("navigateOnHover",!0),Jt("stayInDom",!1),Na("tmenuBehaviours",[Ha,Bi,df,ed]),Jt("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)}},factory:od.make,extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:it.wrap(n,e),expansions:{}}},collapseItem:function(n){return{value:Pa(od.collapseItem()),text:n}}}}),ud=function(n,e,t,r){return it.readOptFrom(e.routes(),r.start()).map(c.apply).bind(function(n){return it.readOptFrom(n,r.destination()).map(c.apply)})},ad=function(n,e,t,r){return ud(0,e,0,r).bind(function(n){return n.transition().map(function(e){return{transition:c.constant(e),route:c.constant(n)}})})},cd=function(n,e,t){var r,o,i;(r=n,o=e,i=t,sd(r,o,i).bind(function(n){return ad(r,o,i,n)})).each(function(t){var r=t.transition();co.remove(n.element(),r.transitionClass()),Ne.remove(n.element(),e.destinationAttr())})},sd=function(n,e,t){var r=n.element();return Ne.has(r,e.destinationAttr())?N.some({start:c.constant(Ne.get(n.element(),e.stateAttr())),destination:c.constant(Ne.get(n.element(),e.destinationAttr()))}):N.none()},fd=function(n,e,t,r){cd(n,e,t),Ne.has(n.element(),e.stateAttr())&&Ne.get(n.element(),e.stateAttr())!==r&&e.onFinish()(n,r),Ne.set(n.element(),e.stateAttr(),r)},ld={findRoute:ud,disableTransition:cd,getCurrentRoute:sd,jumpTo:fd,progressTo:function(n,e,t,r){var o,i;i=e,Ne.has((o=n).element(),i.destinationAttr())&&(Ne.set(o.element(),i.stateAttr(),Ne.get(o.element(),i.destinationAttr())),Ne.remove(o.element(),i.destinationAttr()));var u,a,s,f=(u=n,a=e,s=r,{start:c.constant(Ne.get(u.element(),a.stateAttr())),destination:c.constant(s)});ad(n,e,t,f).fold(function(){fd(n,e,t,r)},function(o){cd(n,e,t);var i=o.transition();co.add(n.element(),i.transitionClass()),Ne.set(n.element(),e.destinationAttr(),r)})},getState:function(n,e,t){var r=n.element();return Ne.has(r,e.stateAttr())?N.some(Ne.get(r,e.stateAttr())):N.none()}},dd={events:function(n,e){return xr.derive([xr.run(s.transitionend(),function(t,r){var o=r.event().raw();ld.getCurrentRoute(t,n,e).each(function(r){ld.findRoute(t,n,e,r).each(function(i){i.transition().each(function(i){o.propertyName===i.property()&&(ld.jumpTo(t,n,e,r.destination()),n.onTransition()(t,r))})})})}),xr.runOnAttached(function(t,r){ld.jumpTo(t,n,e,n.initialState())})])}},md=[Jt("destinationAttr","data-transitioning-destination"),Jt("stateAttr","data-transitioning-state"),Ut("initialState"),Lo.onHandler("onTransition"),Lo.onHandler("onFinish"),zt("routes",mr.setOf(ze.value,mr.setOf(ze.value,mr.objOfOnly([Yt("transition",[Ut("property"),Ut("transitionClass")])]))))],pd=qr.create({fields:md,name:"transitioning",active:dd,apis:ld,extra:{createRoutes:function(n){var e={};return On.each(n,function(n,t){var r=t.split("<->");e[r[0]]=it.wrap(r[1],n),e[r[1]]=it.wrap(r[0],n)}),e},createBistate:function(n,e,t){return it.wrapAll([{key:n,value:it.wrap(e,t)},{key:e,value:it.wrap(n,t)}])},createTristate:function(n,e,t,r){return it.wrapAll([{key:n,value:it.wrapAll([{key:e,value:r},{key:t,value:r}])},{key:e,value:it.wrapAll([{key:n,value:r},{key:t,value:r}])},{key:t,value:it.wrapAll([{key:n,value:r},{key:e,value:r}])}])}}}),gd=ii.resolve("scrollable"),hd={register:function(n){co.add(n,gd)},deregister:function(n){co.remove(n,gd)},scrollable:c.constant(gd)},vd=function(n){return it.readOptFrom(n,"format").getOr(n.title)},yd=function(n,e,t,r,o){return{data:{value:n,text:e},type:"item",dom:{tag:"div",classes:o?[ii.resolve("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:ii.resolve("format-matches"),selected:t},itemBehaviours:qr.derive(o?[]:[ei(n,function(n,e){(e?ni.on:ni.off)(n)})]),components:[{dom:{tag:"div",attributes:{style:r},innerHtml:e}}]}},bd=function(n,e,t,r){return{value:n,dom:{tag:"div"},components:[Vc.sketch({dom:{tag:"div",classes:[ii.resolve("styles-collapser")]},components:r?[{dom:{tag:"span",classes:[ii.resolve("styles-collapse-icon")]}},Hl.text(n)]:[Hl.text(n)],action:function(n){if(r){var e=t().get(n);id.collapseMenu(e)}}}),{dom:{tag:"div",classes:[ii.resolve("styles-menu-items-container")]},components:[Yl.parts().items({})],behaviours:qr.derive([sf.config("adhoc-scrollable-menu",[xr.runOnAttached(function(n,e){Xi.set(n.element(),"overflow-y","auto"),Xi.set(n.element(),"-webkit-overflow-scrolling","touch"),hd.register(n.element())}),xr.runOnDetached(function(n){Xi.remove(n.element(),"overflow-y"),Xi.remove(n.element(),"-webkit-overflow-scrolling"),hd.deregister(n.element())})])])}],items:e,menuBehaviours:qr.derive([pd.config({initialState:"after",routes:pd.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},wd=function(n){var e,t,r,o,i,u=(e=n.formats,t=function(){return a},r=bd("Styles",[].concat(X.map(e.items,function(n){return yd(vd(n),n.title,n.isSelected(),n.getPreview(),it.hasKey(e.expansions,vd(n)))})),t,!1),o=On.map(e.menus,function(n,r){var o=X.map(n,function(n){return yd(vd(n),n.title,n.isSelected!==undefined&&n.isSelected(),n.getPreview!==undefined?n.getPreview():"",it.hasKey(e.expansions,vd(n)))});return bd(r,o,t,!0)}),i=vn.deepMerge(o,it.wrap("styles",r)),{tmenu:id.tieredData("styles",i,e.expansions)}),a=Is(id.sketch({dom:{tag:"div",classes:[ii.resolve("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(e,t){var r=ms.getValue(t);n.handle(t,r.value)},onEscape:function(){},onOpenMenu:function(n,e){var t=ys(n.element());vs(e.element(),t),pd.jumpTo(e,"current")},onOpenSubmenu:function(n,e,t){var r=ys(n.element()),o=Si(e.element(),'[role="menu"]').getOrDie("hacky"),i=n.getSystem().getByDom(o).getOrDie();vs(t.element(),r),pd.progressTo(i,"before"),pd.jumpTo(t,"after"),pd.progressTo(t,"current")},onCollapseMenu:function(n,e,t){var r=Si(e.element(),'[role="menu"]').getOrDie("hacky"),o=n.getSystem().getByDom(r).getOrDie();pd.progressTo(o,"after"),pd.progressTo(t,"current")},navigateOnHover:!1,openImmediately:!0,data:u.tmenu,markers:{backgroundMenu:ii.resolve("styles-background-menu"),menu:ii.resolve("styles-menu"),selectedMenu:ii.resolve("styles-selected-menu"),item:ii.resolve("styles-item"),selectedItem:ii.resolve("styles-selected-item")}}));return a.asSpec()},Sd=function(n){return it.hasKey(n,"items")?(e=n,t=vn.deepMerge(it.exclude(e,["items"]),{menu:!0}),r=xd(e.items),{item:t,menus:vn.deepMerge(r.menus,it.wrap(e.title,r.items)),expansions:vn.deepMerge(r.expansions,it.wrap(e.title,e.title))}):{item:n,menus:{},expansions:{}};var e,t,r},xd=function(n){return X.foldr(n,function(n,e){var t=Sd(e);return{menus:vn.deepMerge(n.menus,t.menus),items:[t.item].concat(n.items),expansions:vn.deepMerge(n.expansions,t.expansions)}},{menus:{},expansions:{},items:[]})},Od={expand:xd},Td=function(n,e){var t=function(e){return function(){return n.formatter.match(e)}},r=function(e){return function(){return n.formatter.getCssText(e)}},o=it.readOptFrom(e,"style_formats").getOr(_f),i=function(e){return X.map(e,function(e){if(it.hasKey(e,"items")){var o=i(e.items);return vn.deepMerge((l=e,vn.deepMerge(l,{isSelected:c.constant(!1),getPreview:c.constant("")})),{items:o})}return it.hasKey(e,"format")?(f=e,vn.deepMerge(f,{isSelected:t(f.format),getPreview:r(f.format)})):(a=Pa((u=e).title),s=vn.deepMerge(u,{format:a,isSelected:t(a),getPreview:r(a)}),n.formatter.register(a,s),s);var u,a,s,f,l})};return i(o)},kd=function(n,e,t){var r,o,i,u=(r=n,i=(o=function(n){return X.bind(n,function(n){return n.items!==undefined?o(n.items).length>0?[n]:[]:!it.hasKey(n,"format")||r.formatter.canApply(n.format)?[n]:[]})})(e),Od.expand(i));return wd({formats:u,handle:function(e,r){n.undoManager.transact(function(){ni.isOn(e)?n.formatter.remove(r):n.formatter.apply(r)}),t()}})},Cd=["undo","bold","italic","link","image","bullist","styleselect"],Ed=function(n){var e=n.replace(/\|/g," ").trim();return e.length>0?e.split(/\s+/):[]},Dd=function(n){return X.bind(n,function(n){return mn.isArray(n)?Dd(n):Ed(n)})},Ad=function(n){var e=n.toolbar!==undefined?n.toolbar:Cd;return mn.isArray(e)?Dd(e):Ed(e)},Md=function(n,e){var t=function(n){return function(){return $c.forToolbarCommand(e,n)}},r=function(n){return function(){return $c.forToolbarStateCommand(e,n)}},o=function(n,t,r){return function(){return $c.forToolbarStateAction(e,n,t,r)}},i=t("undo"),u=t("redo"),a=r("bold"),c=r("italic"),s=r("underline"),f=t("removeformat"),l=o("unlink","link",function(){e.execCommand("unlink",null,!1)}),d=o("unordered-list","ul",function(){e.execCommand("InsertUnorderedList",null,!1)}),m=o("ordered-list","ol",function(){e.execCommand("InsertOrderedList",null,!1)}),p=Td(e,e.settings),g=function(){return kd(e,p,function(){e.fire("scrollIntoView")})},h=function(n,t){return{isSupported:function(){return n.forall(function(n){return it.hasKey(e.buttons,n)})},sketch:t}};return{undo:h(N.none(),i),redo:h(N.none(),u),bold:h(N.none(),a),italic:h(N.none(),c),underline:h(N.none(),s),removeformat:h(N.none(),f),link:h(N.none(),function(){return qf(n,e)}),unlink:h(N.none(),l),image:h(N.none(),function(){return Ys(e)}),bullist:h(N.some("bullist"),d),numlist:h(N.some("numlist"),m),fontsizeselect:h(N.none(),function(){return Bs(n,e)}),forecolor:h(N.none(),function(){return Os(n,e)}),styleselect:h(N.none(),function(){return $c.forToolbar("style-formats",function(t){e.fire("toReading"),n.dropup().appear(g,ni.on,t)},qr.derive([ni.config({toggleClass:ii.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Uo.config({channels:it.wrapAll([ri(Ro.orientationChanged(),ni.off),ri(Ro.dropupDismissed(),ni.off)])})]))})}},Rd=function(n,e){var t=Ad(n),r={};return X.bind(t,function(n){var t=!it.hasKey(r,n)&&it.hasKey(e,n)&&e[n].isSupported()?[e[n].sketch()]:[];return r[n]=!0,t})},Fd=function(n,e){return function(t){if(n(t)){var r,o,i,u,a,s,f,l=Wn.fromDom(t.target),d=function(){t.stopPropagation()},m=function(){t.preventDefault()},p=c.compose(m,d),g=(r=l,o=t.clientX,i=t.clientY,u=d,a=m,s=p,f=t,{target:c.constant(r),x:c.constant(o),y:c.constant(i),stop:u,prevent:a,kill:s,raw:c.constant(f)});e(g)}}},Bd=function(n,e,t,r,o){var i=Fd(t,r);return n.dom().addEventListener(e,i,o),{unbind:c.curry(Id,n,e,i,o)}},Id=function(n,e,t,r){n.dom().removeEventListener(e,t,r)},Hd=function(n,e,t,r){return Bd(n,e,t,r,!1)},Nd=function(n,e,t,r){return Bd(n,e,t,r,!0)},Vd=c.constant(!0),jd={bind:function(n,e,t){return Hd(n,e,Vd,t)},capture:function(n,e,t){return Nd(n,e,Vd,t)}},Pd=function(n){var e=n.matchMedia("(orientation: portrait)").matches;return{isPortrait:c.constant(e)}},Ld=Pd,Wd=function(n,e){var t=Wn.fromDom(n),r=null,o=jd.bind(t,"orientationchange",function(){clearInterval(r);var t=Pd(n);e.onChange(t),i(function(){e.onReady(t)})}),i=function(e){clearInterval(r);var t=n.innerHeight,o=0;r=setInterval(function(){t!==n.innerHeight?(clearInterval(r),e(N.some(n.innerHeight))):o>20&&(clearInterval(r),e(N.none())),o++},50)};return{onAdjustment:i,destroy:function(){o.unbind()}}},Ud=function(n){var e=sn.detect().os.isiOS(),t=Pd(n).isPortrait();return e&&!t?n.screen.height:n.screen.width},zd=function(n){return n.raw().touches===undefined||1!==n.raw().touches.length?N.none():N.some(n.raw().touches[0])},Kd=function(n){var e,t,r,o=po(N.none()),i=(e=function(e){o.set(N.none()),n.triggerEvent(ln.longpress(),e)},t=400,r=null,{cancel:function(){null!==r&&(clearTimeout(r),r=null)},schedule:function(){var n=arguments;r=setTimeout(function(){e.apply(null,n),r=null},t)}}),u=it.wrapAll([{key:s.touchstart(),value:function(n){return zd(n).each(function(e){i.cancel();var t={x:c.constant(e.clientX),y:c.constant(e.clientY),target:n.target};i.schedule(t),o.set(N.some(t))}),N.none()}},{key:s.touchmove(),value:function(n){return i.cancel(),zd(n).each(function(n){o.get().each(function(e){var t,r,i,u;t=n,r=e,i=Math.abs(t.clientX-r.x()),u=Math.abs(t.clientY-r.y()),(i>5||u>5)&&o.set(N.none())})}),N.none()}},{key:s.touchend(),value:function(e){return i.cancel(),o.get().filter(function(n){return Jn(n.target(),e.target())}).map(function(t){return n.triggerEvent(ln.tap(),e)})}}]);return{fireIfReady:function(n,e){return it.readOptFrom(u,e).bind(function(e){return e(n)})}}},Gd=function(n){var e=Kd({triggerEvent:function(e,t){n.onTapContent(t)}});return{fireTouchstart:function(n){e.fireIfReady(n,"touchstart")},onTouchend:function(){return jd.bind(n.body(),"touchend",function(n){e.fireIfReady(n,"touchend")})},onTouchmove:function(){return jd.bind(n.body(),"touchmove",function(n){e.fireIfReady(n,"touchmove")})}}},$d=sn.detect().os.version.major>=6,qd=function(n,e,t){var r=Gd(n),o=ie.owner(e),i=function(n){return!Jn(n.start(),n.finish())||n.soffset()!==n.foffset()},u=function(){var r=n.doc().dom().hasFocus()&&n.getSelection().exists(i);t.getByDom(e).each(!0===(r||ko(o).filter(function(n){return"input"===ye.name(n)}).exists(function(n){return n.dom().selectionStart!==n.dom().selectionEnd}))?ni.on:ni.off)},a=[jd.bind(n.body(),"touchstart",function(e){n.onTouchContent(),r.fireTouchstart(e)}),r.onTouchmove(),r.onTouchend(),jd.bind(e,"touchstart",function(e){n.onTouchToolstrip()}),n.onToReading(function(){To(n.body())}),n.onToEditing(c.noop),n.onScrollToCursor(function(e){e.preventDefault(),n.getCursorBox().each(function(e){var t=n.win(),r=e.top()>t.innerHeight||e.bottom()>t.innerHeight?e.bottom()-t.innerHeight+50:0;0!==r&&t.scrollTo(t.pageXOffset,t.pageYOffset+r)})})].concat(!0===$d?[]:[jd.bind(Wn.fromDom(n.win()),"blur",function(){t.getByDom(e).each(ni.off)}),jd.bind(o,"select",u),jd.bind(n.doc(),"selectionchange",u)]);return{destroy:function(){X.each(a,function(n){n.unbind()})}}},_d=function(n,e){var t=parseInt(Ne.get(n,e),10);return isNaN(t)?0:t},Xd=(rl=ye.isText,ol="text",il=function(n){return rl(n)?N.from(n.dom().nodeValue):N.none()},ul=sn.detect().browser,{get:function(n){if(!rl(n))throw new Error("Can only get "+ol+" value of a "+ol+" node");return al(n).getOr("")},getOption:al=ul.isIE()&&10===ul.version.major?function(n){try{return il(n)}catch(e){return N.none()}}:il,set:function(n,e){if(!rl(n))throw new Error("Can only set raw "+ol+" value of a "+ol+" node");n.dom().nodeValue=e}}),Yd=function(n){return Xd.getOption(n)},Jd=function(n){return"img"===ye.name(n)?1:Yd(n).fold(function(){return ie.children(n).length},function(n){return n.length})},Qd=Jd,Zd=Ke([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),nm={before:Zd.before,on:Zd.on,after:Zd.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(c.identity,c.identity,c.identity)}},em=Ke([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),tm=In.immutable("start","soffset","finish","foffset"),rm={domRange:em.domRange,relative:em.relative,exact:em.exact,exactFromRange:function(n){return em.exact(n.start(),n.soffset(),n.finish(),n.foffset())},range:tm,getWin:function(n){var e=n.match({domRange:function(n){return Wn.fromDom(n.startContainer)},relative:function(n,e){return nm.getStart(n)},exact:function(n,e,t,r){return n}});return ie.defaultView(e)}},om=function(n,e,t,r){var o=ie.owner(n).dom().createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o},im=function(n,e,t,r){var o=om(n,e,t,r),i=Jn(n,t)&&e===r;return o.collapsed&&!i},um=function(n,e){n.selectNodeContents(e.dom())},am=function(n){n.deleteContents()},cm=function(n){return{left:c.constant(n.left),top:c.constant(n.top),right:c.constant(n.right),bottom:c.constant(n.bottom),width:c.constant(n.width),height:c.constant(n.height)}},sm={create:function(n){return n.document.createRange()},replaceWith:function(n,e){am(n),n.insertNode(e.dom())},selectNodeContents:function(n,e){var t=n.document.createRange();return um(t,e),t},selectNodeContentsUsing:um,relativeToNative:function(n,e,t){var r,o,i=n.document.createRange();return r=i,e.fold(function(n){r.setStartBefore(n.dom())},function(n,e){r.setStart(n.dom(),e)},function(n){r.setStartAfter(n.dom())}),o=i,t.fold(function(n){o.setEndBefore(n.dom())},function(n,e){o.setEnd(n.dom(),e)},function(n){o.setEndAfter(n.dom())}),i},exactToNative:function(n,e,t,r,o){var i=n.document.createRange();return i.setStart(e.dom(),t),i.setEnd(r.dom(),o),i},deleteContents:am,cloneFragment:function(n){var e=n.cloneContents();return Wn.fromDom(e)},getFirstRect:function(n){var e=n.getClientRects(),t=e.length>0?e[0]:n.getBoundingClientRect();return t.width>0||t.height>0?N.some(t).map(cm):N.none()},getBounds:function(n){var e=n.getBoundingClientRect();return e.width>0||e.height>0?N.some(e).map(cm):N.none()},isWithin:function(n,e){return e.compareBoundaryPoints(n.END_TO_START,n)<1&&e.compareBoundaryPoints(n.START_TO_END,n)>-1},toString:function(n){return n.toString()}},fm=Ke([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),lm=function(n,e,t){return e(Wn.fromDom(t.startContainer),t.startOffset,Wn.fromDom(t.endContainer),t.endOffset)},dm=function(n,e){var t,r,o,i=(t=n,e.match({domRange:function(n){return{ltr:c.constant(n),rtl:N.none}},relative:function(n,e){return{ltr:f(function(){return sm.relativeToNative(t,n,e)}),rtl:f(function(){return N.some(sm.relativeToNative(t,e,n))})}},exact:function(n,e,r,o){return{ltr:f(function(){return sm.exactToNative(t,n,e,r,o)}),rtl:f(function(){return N.some(sm.exactToNative(t,r,o,n,e))})}}}));return(o=(r=i).ltr()).collapsed?r.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return fm.rtl(Wn.fromDom(n.endContainer),n.endOffset,Wn.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return lm(0,fm.ltr,o)}):lm(0,fm.ltr,o)},mm=(fm.ltr,fm.rtl,dm),pm=function(n,e){return dm(n,e).match({ltr:function(e,t,r,o){var i=n.document.createRange();return i.setStart(e.dom(),t),i.setEnd(r.dom(),o),i},rtl:function(e,t,r,o){var i=n.document.createRange();return i.setStart(r.dom(),o),i.setEnd(e.dom(),t),i}})},gm=(document.caretPositionFromPoint||document.caretRangeFromPoint,function(n,e){var t=ye.name(n);return"input"===t?nm.after(n):X.contains(["br","img"],t)?0===e?nm.before(n):nm.after(n):nm.on(n,e)}),hm=function(n,e,t,r){var o=gm(n,e),i=gm(t,r);return rm.relative(o,i)},vm=hm,ym=function(n,e){N.from(n.getSelection()).each(function(n){n.removeAllRanges(),n.addRange(e)})},bm=function(n,e,t,r,o){var i=sm.exactToNative(n,e,t,r,o);ym(n,i)},wm=function(n,e){return mm(n,e).match({ltr:function(e,t,r,o){bm(n,e,t,r,o)},rtl:function(e,t,r,o){var i=n.getSelection();i.setBaseAndExtent?i.setBaseAndExtent(e.dom(),t,r.dom(),o):i.extend?(i.collapse(e.dom(),t),i.extend(r.dom(),o)):bm(n,r,o,e,t)}})},Sm=function(n){var e=Wn.fromDom(n.anchorNode),t=Wn.fromDom(n.focusNode);return im(e,n.anchorOffset,t,n.focusOffset)?N.some(rm.range(Wn.fromDom(n.anchorNode),n.anchorOffset,Wn.fromDom(n.focusNode),n.focusOffset)):function(n){if(n.rangeCount>0){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return N.some(rm.range(Wn.fromDom(e.startContainer),e.startOffset,Wn.fromDom(t.endContainer),t.endOffset))}return N.none()}(n)},xm=function(n){var e=n.getSelection();return e.rangeCount>0?Sm(e):N.none()},Om=function(n,e,t,r,o){var i=vm(e,t,r,o);wm(n,i)},Tm=xm,km=function(n){return xm(n).map(function(n){return rm.exact(n.start(),n.soffset(),n.finish(),n.foffset())})},Cm=function(n){n.getSelection().removeAllRanges()},Em=function(n,e){var t=pm(n,e);return sm.getFirstRect(t)},Dm=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:c.constant(2),height:n.height}},Am=function(n){return{left:c.constant(n.left),top:c.constant(n.top),right:c.constant(n.right),bottom:c.constant(n.bottom),width:c.constant(n.width),height:c.constant(n.height)}},Mm={getRectangles:function(n){var e=n.getSelection();return e!==undefined&&e.rangeCount>0?function(n){if(n.collapsed){var e=Wn.fromDom(n.startContainer);return ie.parent(e).bind(function(t){var r=rm.exact(e,n.startOffset,t,Qd(t));return Em(n.startContainer.ownerDocument.defaultView,r).map(Dm).map(X.pure)}).getOr([])}return X.map(n.getClientRects(),Am)}(e.getRangeAt(0)):[]}},Rm=function(n){n.focus();var e=Wn.fromDom(n.document.body);(ko().exists(function(n){return X.contains(["input","textarea"],ye.name(n))})?function(n){setTimeout(function(){n()},0)}:c.apply)(function(){ko().each(To),Oo(e)})},Fm="data-"+ii.resolve("last-outer-height"),Bm=function(n,e){Ne.set(n,Fm,e)},Im=function(n){return{top:c.constant(n.top()),bottom:c.constant(n.top()+n.height())}},Hm=function(n,e){var t=_d(e,Fm),r=n.innerHeight;return t>r?N.some(t-r):N.none()},Nm=function(n,e){var t=Wn.fromDom(e.document.body),r=jd.bind(Wn.fromDom(n),"resize",function(){Hm(n,t).each(function(n){var t,r;(t=e,r=Mm.getRectangles(t),r.length>0?N.some(r[0]).map(Im):N.none()).each(function(t){var r,o,i,u=(r=e,i=n,(o=t).top()>r.innerHeight||o.bottom()>r.innerHeight?Math.min(i,o.bottom()-r.innerHeight+50):0);0!==u&&e.scrollTo(e.pageXOffset,e.pageYOffset+u)})}),Bm(t,n.innerHeight)});return Bm(t,n.innerHeight),{toEditing:function(){Rm(e)},destroy:function(){r.unbind()}}},Vm=function(n){return N.some(Wn.fromDom(n.dom().contentWindow.document.body))},jm=function(n){return N.some(Wn.fromDom(n.dom().contentWindow.document))},Pm=function(n){return N.from(n.dom().contentWindow)},Lm=function(n){return Pm(n).bind(Tm)},Wm=function(n){return n.getFrame()},Um=function(n,e){return function(t){return t[n].getOrThunk(function(){var n=Wm(t);return function(){return e(n)}})()}},zm=function(n,e,t,r){return n[t].getOrThunk(function(){return function(n){return jd.bind(e,r,n)}})},Km=function(n){return{left:c.constant(n.left),top:c.constant(n.top),right:c.constant(n.right),bottom:c.constant(n.bottom),width:c.constant(n.width),height:c.constant(n.height)}},Gm={getBody:Um("getBody",Vm),getDoc:Um("getDoc",jm),getWin:Um("getWin",Pm),getSelection:Um("getSelection",Lm),getFrame:Wm,getActiveApi:function(n){var e=Wm(n);return Vm(e).bind(function(t){return jm(e).bind(function(r){return Pm(e).map(function(o){var i=Wn.fromDom(r.dom().documentElement),u=n.getCursorBox.getOrThunk(function(){return function(){return km(o).bind(function(n){return Em(o,n).orThunk(function(){return Tm(o).filter(function(n){return Jn(n.start(),n.finish())&&n.soffset()===n.foffset()}).bind(function(n){var e=n.start().dom().getBoundingClientRect();return e.width>0||e.height>0?N.some(e).map(Km):N.none()})})})}}),a=n.setSelection.getOrThunk(function(){return function(n,e,t,r){Om(o,n,e,t,r)}}),s=n.clearSelection.getOrThunk(function(){return function(){Cm(o)}});return{body:c.constant(t),doc:c.constant(r),win:c.constant(o),html:c.constant(i),getSelection:c.curry(Lm,e),setSelection:a,clearSelection:s,frame:c.constant(e),onKeyup:zm(n,r,"onKeyup","keyup"),onNodeChanged:zm(n,r,"onNodeChanged","selectionchange"),onDomChanged:n.onDomChanged,onScrollToCursor:n.onScrollToCursor,onScrollToElement:n.onScrollToElement,onToReading:n.onToReading,onToEditing:n.onToEditing,onToolbarScrollStart:n.onToolbarScrollStart,onTouchContent:n.onTouchContent,onTapContent:n.onTapContent,onTouchToolstrip:n.onTouchToolstrip,getCursorBox:u}})})})}},$m="data-ephox-mobile-fullscreen-style",qm="position:absolute!important;",_m="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;",Xm=sn.detect().os.isAndroid(),Ym=function(n,e){var t,r,o=function(n){return function(e){var t=Ne.get(e,"style"),r=t===undefined?"no-styles":t.trim();r!==n&&(Ne.set(e,$m,r),Ne.set(e,"style",n))}},i=hi(n,"*"),u=X.bind(i,function(n){return vi(n,"*")}),a=(t=e,(r=Xi.get(t,"background-color"))!==undefined&&""!==r?"background-color:"+r+"!important":"background-color:rgb(255,255,255)!important;");X.each(u,o("display:none!important;")),X.each(i,o(qm+_m+a)),o((!0===Xm?"":qm)+_m+a)(n)},Jm=function(){var n=gi("["+$m+"]");X.each(n,function(n){var e=Ne.get(n,$m);"no-styles"!==e?Ne.set(n,"style",e):Ne.remove(n,"style"),Ne.remove(n,$m)})},Qm=function(){var n=wi("head").getOrDie(),e=wi('meta[name="viewport"]').getOrThunk(function(){var e=Wn.fromTag("meta");return Ne.set(e,"name","viewport"),ce.append(n,e),e}),t=Ne.get(e,"content");return{maximize:function(){Ne.set(e,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},restore:function(){t!==undefined&&null!==t&&t.length>0?Ne.set(e,"content",t):Ne.set(e,"content","user-scalable=yes")}}},Zm=function(n,e){var t=Qm(),r=Wf.api(),o=Wf.api();return{enter:function(){e.hide(),co.add(n.container,ii.resolve("fullscreen-maximized")),co.add(n.container,ii.resolve("android-maximized")),t.maximize(),co.add(n.body,ii.resolve("android-scroll-reload")),r.set(Nm(n.win,Gm.getWin(n.editor).getOrDie("no"))),Gm.getActiveApi(n.editor).each(function(e){Ym(n.container,e.body()),o.set(qd(e,n.toolstrip,n.alloy))})},exit:function(){t.restore(),e.show(),co.remove(n.container,ii.resolve("fullscreen-maximized")),co.remove(n.container,ii.resolve("android-maximized")),Jm(),co.remove(n.body,ii.resolve("android-scroll-reload")),o.clear(),r.clear()}}},np=function(n,e){var t=null;return{cancel:function(){null!==t&&(clearTimeout(t),t=null)},throttle:function(){var r=arguments;null===t&&(t=setTimeout(function(){n.apply(null,r),t=null,r=null},e))}}},ep=function(n,e){var t=null;return{cancel:function(){null!==t&&(clearTimeout(t),t=null)},throttle:function(){var r=arguments;null!==t&&clearTimeout(t),t=setTimeout(function(){n.apply(null,r),t=null,r=null},e)}}},tp=function(n,e){var t=Is(mf.sketch({dom:zc.dom('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:qr.derive([ni.config({toggleClass:ii.resolve("mask-tap-icon-selected"),toggleOnExecute:!1})])})),r=np(n,200);return mf.sketch({dom:zc.dom('<div class="${prefix}-disabled-mask"></div>'),components:[mf.sketch({dom:zc.dom('<div class="${prefix}-content-container"></div>'),components:[Vc.sketch({dom:zc.dom('<div class="${prefix}-content-tap-section"></div>'),components:[t.asSpec()],action:function(n){r.throttle()},buttonBehaviours:qr.derive([ni.config({toggleClass:ii.resolve("mask-tap-icon-selected")})])})]})]})},rp=mr.objOf([Kt("editor",[Ut("getFrame"),qt("getBody"),qt("getDoc"),qt("getWin"),qt("getSelection"),qt("setSelection"),qt("clearSelection"),qt("cursorSaver"),qt("onKeyup"),qt("onNodeChanged"),qt("getCursorBox"),Ut("onDomChanged"),Jt("onTouchContent",c.noop),Jt("onTapContent",c.noop),Jt("onTouchToolstrip",c.noop),Jt("onScrollToCursor",c.constant({unbind:c.noop})),Jt("onScrollToElement",c.constant({unbind:c.noop})),Jt("onToEditing",c.constant({unbind:c.noop})),Jt("onToReading",c.constant({unbind:c.noop})),Jt("onToolbarScrollStart",c.identity)]),Ut("socket"),Ut("toolstrip"),Ut("dropup"),Ut("toolbar"),Ut("container"),Ut("alloy"),er("win",function(n){return ie.owner(n.socket).dom().defaultView}),er("body",function(n){return Wn.fromDom(n.socket.dom().ownerDocument.body)}),Jt("translate",c.identity),Jt("setReadOnly",c.noop)]),op={produce:function(n){var e=mr.asRawOrDie("Getting AndroidWebapp schema",rp,n);Xi.set(e.toolstrip,"width","100%");var t=Hl.build(tp(function(){e.setReadOnly(!0),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}};ce.append(e.container,t.element());var o=Zm(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:c.noop,enter:o.enter,exit:o.exit,destroy:c.noop}}},ip=[Jt("shell",!0),Na("toolbarBehaviours",[ed])],up=[ec.optional({name:"groups",overrides:function(n){return{behaviours:qr.derive([ed.config({})])}}})],ap={name:c.constant("Toolbar"),schema:c.constant(ip),parts:c.constant(up)},cp=Hc({name:"Toolbar",configFields:ap.schema(),partFields:ap.parts(),factory:function(n,e,t,r){var o=function(e){return n.shell()?N.some(e):Sc(e,n,"groups")},i=n.shell()?{behaviours:[ed.config({})],components:[]}:{behaviours:[],components:e};return{uid:n.uid(),dom:n.dom(),components:i.components,behaviours:vn.deepMerge(qr.derive(i.behaviours),Va(n.toolbarBehaviours())),apis:{setGroups:function(n,e){o(n).fold(function(){throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){ed.set(n,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,e,t){n.setGroups(e,t)}}}),sp=[Ut("items"),Lo.markers(["itemClass"]),Na("tgroupBehaviours",[Ha])],fp=[ec.group({name:"items",unit:"item",overrides:function(n){return{domModification:{classes:[n.markers().itemClass()]}}}})],lp={name:c.constant("ToolbarGroup"),schema:c.constant(sp),parts:c.constant(fp)},dp=Hc({name:"ToolbarGroup",configFields:lp.schema(),partFields:lp.parts(),factory:function(n,e,t,r){return vn.deepMerge({dom:{attributes:{role:"toolbar"}}},{uid:n.uid(),dom:n.dom(),components:e,behaviours:vn.deepMerge(qr.derive([Ha.config({mode:"flow",selector:"."+n.markers().itemClass()})]),Va(n.tgroupBehaviours())),"debug.sketcher":t["debug.sketcher"]})}}),mp="data-"+ii.resolve("horizontal-scroll"),pp=function(n){return n.dom().scrollTop>0||function(n){n.dom().scrollTop=1;var e=0!==n.dom().scrollTop;return n.dom().scrollTop=0,e}(n)},gp=function(n){return n.dom().scrollLeft>0||function(n){n.dom().scrollLeft=1;var e=0!==n.dom().scrollLeft;return n.dom().scrollLeft=0,e}(n)},hp=function(n){return"true"===Ne.get(n,mp)?gp:pp},vp={exclusive:function(n,e){return jd.bind(n,"touchmove",function(n){Oi(n.target(),e).filter(hp).fold(function(){n.raw().preventDefault()},c.noop)})},markAsHorizontal:function(n){Ne.set(n,mp,"true")}};function yp(){var n=function(n){var e=!0===n.scrollable?"${prefix}-toolbar-scrollable-group":"";return{dom:zc.dom('<div aria-label="'+n.label+'" class="${prefix}-toolbar-group '+e+'"></div>'),tgroupBehaviours:qr.derive([sf.config("adhoc-scrollable-toolbar",!0===n.scrollable?[xr.runOnInit(function(n,e){Xi.set(n.element(),"overflow-x","auto"),vp.markAsHorizontal(n.element()),hd.register(n.element())})]:[])]),components:[mf.sketch({components:[dp.parts().items({})]})],markers:{itemClass:ii.resolve("toolbar-group-item")},items:n.items}},e=Hl.build(cp.sketch({dom:zc.dom('<div class="${prefix}-toolbar"></div>'),components:[cp.parts().groups({})],toolbarBehaviours:qr.derive([ni.config({toggleClass:ii.resolve("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),Ha.config({mode:"cyclic"})]),shell:!0})),t=Hl.build(mf.sketch({dom:{classes:[ii.resolve("toolstrip")]},components:[Hl.premade(e)],containerBehaviours:qr.derive([ni.config({toggleClass:ii.resolve("android-selection-context-toolbar"),toggleOnExecute:!1})])})),r=function(){cp.setGroups(e,o.get()),ni.off(e)},o=po([]);return{wrapper:c.constant(t),toolbar:c.constant(e),createGroups:function(e){return X.map(e,c.compose(dp.sketch,n))},setGroups:function(n){o.set(n),r()},setContextToolbar:function(n){ni.on(e),cp.setGroups(e,n)},restoreToolbar:function(){ni.isOn(e)&&r()},refresh:function(){cp.refresh(e)},focus:function(){Ha.focusIn(e)}}}var bp=function(n,e){ed.append(n,Hl.premade(e))},wp=function(n,e){ed.remove(n,e)},Sp={makeEditSwitch:function(n){return Hl.build(Vc.sketch({dom:zc.dom('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){n.run(function(n){n.setReadOnly(!1)})}}))},makeSocket:function(){return Hl.build(mf.sketch({dom:zc.dom('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:qr.derive([ed.config({})])}))},updateMode:function(n,e,t,r){(!0===t?mo.toAlpha:mo.toOmega)(r),(t?bp:wp)(n,e)}},xp=function(n,e){return e.getAnimationRoot().fold(function(){return n.element()},function(e){return e(n)})},Op=function(n){return n.dimension().property()},Tp=function(n,e){return n.dimension().getDimension()(e)},kp=function(n,e){var t=xp(n,e);kl.remove(t,[e.shrinkingClass(),e.growingClass()])},Cp=function(n,e){co.remove(n.element(),e.openClass()),co.add(n.element(),e.closedClass()),Xi.set(n.element(),Op(e),"0px"),Xi.reflow(n.element())},Ep=function(n,e){co.remove(n.element(),e.closedClass()),co.add(n.element(),e.openClass()),Xi.remove(n.element(),Op(e))},Dp=function(n,e,t){t.setCollapsed(),Xi.set(n.element(),Op(e),Tp(e,n.element())),Xi.reflow(n.element());var r=xp(n,e);co.add(r,e.shrinkingClass()),Cp(n,e),e.onStartShrink()(n)},Ap=function(n,e,t){var r=function(n,e){Ep(n,e);var t=Tp(e,n.element());return Cp(n,e),t}(n,e),o=xp(n,e);co.add(o,e.growingClass()),Ep(n,e),Xi.set(n.element(),Op(e),r),t.setExpanded(),e.onStartGrow()(n)},Mp=function(n,e,t){var r=xp(n,e);return!0===co.has(r,e.growingClass())},Rp=function(n,e,t){var r=xp(n,e);return!0===co.has(r,e.shrinkingClass())},Fp={grow:function(n,e,t){t.isExpanded()||Ap(n,e,t)},shrink:function(n,e,t){t.isExpanded()&&Dp(n,e,t)},immediateShrink:function(n,e,t){var r,o;t.isExpanded()&&(r=n,o=e,t.setCollapsed(),Xi.set(r.element(),Op(o),Tp(o,r.element())),Xi.reflow(r.element()),kp(r,o),Cp(r,o),o.onStartShrink()(r),o.onShrunk()(r))},hasGrown:function(n,e,t){return t.isExpanded()},hasShrunk:function(n,e,t){return t.isCollapsed()},isGrowing:Mp,isShrinking:Rp,isTransitioning:function(n,e,t){return!0===Mp(n,e)||!0===Rp(n,e)},toggleGrow:function(n,e,t){(t.isExpanded()?Dp:Ap)(n,e,t)},disableTransitions:kp},Bp={exhibit:function(n,e){return e.expanded()?Fr.nu({classes:[e.openClass()],styles:{}}):Fr.nu({classes:[e.closedClass()],styles:it.wrap(e.dimension().property(),"0px")})},events:function(n,e){return xr.derive([xr.run(s.transitionend(),function(t,r){r.event().raw().propertyName===n.dimension().property()&&(Fp.disableTransitions(t,n,e),e.isExpanded()&&Xi.remove(t.element(),n.dimension().property()),(e.isExpanded()?n.onGrown():n.onShrunk())(t,r))})])}},Ip=[Ut("closedClass"),Ut("openClass"),Ut("shrinkingClass"),Ut("growingClass"),qt("getAnimationRoot"),Lo.onHandler("onShrunk"),Lo.onHandler("onStartShrink"),Lo.onHandler("onGrown"),Lo.onHandler("onStartGrow"),Jt("expanded",!1),zt("dimension",mr.choose("property",{width:[Lo.output("property","width"),Lo.output("getDimension",function(n){return ys(n)+"px"})],height:[Lo.output("property","height"),Lo.output("getDimension",function(n){return Qi(n)+"px"})]}))],Hp={init:function(n){var e=po(n.expanded());return zr({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:c.curry(e.set,!1),setExpanded:c.curry(e.set,!0),readState:function(){return"expanded: "+e.get()}})}},Np=qr.create({fields:Ip,name:"sliding",active:Bp,apis:Fp,state:Hp}),Vp={build:function(n,e){var t=Hl.build(mf.sketch({dom:{tag:"div",classes:ii.resolve("dropup")},components:[],containerBehaviours:qr.derive([ed.config({}),Np.config({closedClass:ii.resolve("dropup-closed"),openClass:ii.resolve("dropup-open"),shrinkingClass:ii.resolve("dropup-shrinking"),growingClass:ii.resolve("dropup-growing"),dimension:{property:"height"},onShrunk:function(t){n(),e(),ed.set(t,[])},onGrown:function(t){n(),e()}}),ti(function(n,e){r(c.noop)})])})),r=function(n){window.requestAnimationFrame(function(){n(),Np.shrink(t)})};return{appear:function(n,e,r){!0===Np.hasShrunk(t)&&!1===Np.isTransitioning(t)&&window.requestAnimationFrame(function(){e(r),ed.set(t,[n()]),Np.grow(t)})},disappear:r,component:c.constant(t),element:t.element}}},jp=sn.detect().browser.isFirefox(),Pp=mr.objOfOnly([Gt("triggerEvent"),Gt("broadcastEvent"),Jt("stopBackspace",!0)]),Lp=function(n,e){var t,r,o,i,u=mr.asRawOrDie("Getting GUI events settings",Pp,e),a=sn.detect().deviceType.isTouch()?["touchstart","touchmove","touchend","gesturestart"]:["mousedown","mouseup","mouseover","mousemove","mouseout","click"],c=Kd(u),s=X.map(a.concat(["selectstart","input","contextmenu","change","transitionend","dragstart","dragover","drop"]),function(e){return jd.bind(n,e,function(n){c.fireIfReady(n,e).each(function(e){e&&n.kill()}),u.triggerEvent(e,n)&&n.kill()})}),f=jd.bind(n,"keydown",function(n){var e;u.triggerEvent("keydown",n)?n.kill():!0!==u.stopBackspace||(e=n).raw().which!==fi.BACKSPACE()[0]||X.contains(["input","textarea"],ye.name(e.target()))||n.prevent()}),l=(t=n,r=function(n){u.triggerEvent("focusin",n)&&n.kill()},jp?jd.capture(t,"focus",r):jd.bind(t,"focusin",r)),d=(o=n,i=function(n){u.triggerEvent("focusout",n)&&n.kill(),setTimeout(function(){u.triggerEvent(ln.postBlur(),n)},0)},jp?jd.capture(o,"blur",i):jd.bind(o,"focusout",i)),m=ie.defaultView(n),p=jd.bind(m,"scroll",function(n){u.broadcastEvent(ln.windowScroll(),n)&&n.kill()});return{unbind:function(){X.each(s,function(n){n.unbind()}),f.unbind(),l.unbind(),d.unbind(),p.unbind()}}},Wp=function(n,e){var t=it.readOptFrom(n,"target").map(function(n){return n()}).getOr(e);return po(t)},Up=function(n,e){var t=po(!1),r=po(!1);return{stop:function(){t.set(!0)},cut:function(){r.set(!0)},isStopped:t.get,isCut:r.get,event:c.constant(n),setSource:e.set,getSource:e.get}},zp=Up,Kp=function(n){var e=po(!1);return{stop:function(){e.set(!0)},cut:c.noop,isStopped:e.get,isCut:c.constant(!1),event:c.constant(n),setTarget:c.die(new Error("Cannot set target of a broadcasted event")),getTarget:c.die(new Error("Cannot get target of a broadcasted event"))}},Gp=Ke([{stopped:[]},{resume:["element"]},{complete:[]}]),$p=function(n,e,t,r,o,i){var u=n(e,r),a=zp(t,o);return u.fold(function(){return i.logEventNoHandlers(e,r),Gp.complete()},function(n){var t=n.descHandler();return gl.getHandler(t)(a),a.isStopped()?(i.logEventStopped(e,n.element(),t.purpose()),Gp.stopped()):a.isCut()?(i.logEventCut(e,n.element(),t.purpose()),Gp.complete()):ie.parent(n.element()).fold(function(){return i.logNoParent(e,n.element(),t.purpose()),Gp.complete()},function(r){return i.logEventResponse(e,n.element(),t.purpose()),Gp.resume(r)})})},qp=function(n,e,t,r,o,i){return $p(n,e,t,r,o,i).fold(function(){return!0},function(r){return qp(n,e,t,r,o,i)},function(){return!1})},_p=function(n,e,t,r,o){var i=Wp(t,r);return qp(n,e,t,r,i,o)},Xp={triggerHandler:function(n,e,t,r,o){var i=Wp(t,r);return $p(n,e,t,r,i,o)},triggerUntilStopped:function(n,e,t,r){var o=t.target();return _p(n,e,t,o,r)},triggerOnUntilStopped:_p,broadcast:function(n,e,t){var r=Kp(e);return X.each(n,function(n){var e=n.descHandler();gl.getHandler(e)(r)}),r.isStopped()}},Yp={closest:function(n,e,t){return bo.closest(n,function(n){return e(n).isSome()},t).bind(e)}},Jp=In.immutable("element","descHandler"),Qp=function(n,e){return{id:c.constant(n),descHandler:c.constant(e)}};function Zp(){var n={};return{registerId:function(e,t,r){On.each(r,function(r,o){var i=n[o]!==undefined?n[o]:{};i[t]=gl.curryArgs(r,e),n[o]=i})},unregisterId:function(e){On.each(n,function(n,t){n.hasOwnProperty(e)&&delete n[e]})},filterByType:function(e){return it.readOptFrom(n,e).map(function(n){return On.mapToArray(n,function(n,e){return Qp(e,n)})}).getOr([])},find:function(e,t,r){var o=it.readOpt(t)(n);return Yp.closest(r,function(n){return e=o,t=n,Cc.read(t).fold(function(n){return N.none()},function(n){var r=it.readOpt(n);return e.bind(r).map(function(n){return Jp(t,n)})});var e,t},e)}}}function ng(){var n=Zp(),e={},t=function(t){Cc.read(t.element()).each(function(t){e[t]=undefined,n.unregisterId(t)})};return{find:function(e,t,r){return n.find(e,t,r)},filter:function(e){return n.filterByType(e)},register:function(r){var o,i,u=(i=(o=r).element(),Cc.read(i).fold(function(){return Cc.write("uid-",o.element())},function(n){return n}));it.hasKey(e,u)&&function(n,r){var o=e[r];if(o!==n)throw new Error('The tagId "'+r+'" is already used by: '+Le.element(o.element())+"\nCannot use it for: "+Le.element(n.element())+"\nThe conflicting element is"+(Se.inBody(o.element())?" ":" not ")+"already in the DOM");t(n)}(r,u);var a=[r];n.registerId(a,u,r.events()),e[u]=r},unregister:t,getById:function(n){return it.readOpt(n)(e)}}}var eg=function(n){var e=function(e){return ie.parent(n.element()).fold(function(){return!0},function(n){return Jn(e,n)})},t=ng(),r=function(n,r){return t.find(e,n,r)},o=Lp(n.element(),{triggerEvent:function(n,e){return ht(n,e.target(),function(t){return Xp.triggerUntilStopped(r,n,e,t)})},broadcastEvent:function(n,e){var r=t.filter(n);return Xp.broadcast(r,e)}}),i=el({debugInfo:c.constant("real"),triggerEvent:function(n,e,t){ht(n,e,function(o){Xp.triggerOnUntilStopped(r,n,t,e,o)})},triggerFocus:function(n,e){Cc.read(n).fold(function(){Oo(n)},function(t){ht(ln.focus(),n,function(t){Xp.triggerHandler(r,ln.focus(),{originator:c.constant(e),target:c.constant(n)},n,t)})})},triggerEscape:function(n,e){i.triggerEvent("keydown",n.element(),e.event())},getByUid:function(n){return p(n)},getByDom:function(n){return g(n)},build:Hl.build,addToGui:function(n){s(n)},removeFromGui:function(n){f(n)},addToWorld:function(n){u(n)},removeFromWorld:function(n){a(n)},broadcast:function(n){d(n)},broadcastOn:function(n,e){m(n,e)}}),u=function(n){n.connect(i),ye.isText(n.element())||(t.register(n),X.each(n.components(),u),i.triggerEvent(ln.systemInit(),n.element(),{target:c.constant(n.element())}))},a=function(n){ye.isText(n.element())||(X.each(n.components(),a),t.unregister(n)),n.disconnect()},s=function(e){Ce.attach(n,e)},f=function(n){Ce.detach(n)},l=function(n){var e=t.filter(ln.receive());X.each(e,function(e){var t=e.descHandler();gl.getHandler(t)(n)})},d=function(n){l({universal:c.constant(!0),data:c.constant(n)})},m=function(n,e){l({universal:c.constant(!1),channels:c.constant(n),data:c.constant(e)})},p=function(n){return t.getById(n).fold(function(){return ze.error(new Error('Could not find component with uid: "'+n+'" in system.'))},ze.value)},g=function(n){return Cc.read(n).bind(p)};return u(n),{root:c.constant(n),element:n.element,destroy:function(){o.unbind(),le.remove(n.element())},add:s,remove:f,getByUid:p,getByDom:g,addToWorld:u,removeFromWorld:a,broadcast:d,broadcastOn:m}},tg={create:function(){var n=Hl.build(mf.sketch({dom:{tag:"div"}}));return eg(n)},takeover:eg},rg=c.constant(ii.resolve("readonly-mode")),og=c.constant(ii.resolve("edit-mode"));function ig(n){var e=Hl.build(mf.sketch({dom:{classes:[ii.resolve("outer-container")].concat(n.classes)},containerBehaviours:qr.derive([mo.config({alpha:rg(),omega:og()})])}));return tg.takeover(e)}var ug=function(n,e){var t=Wn.fromTag("input");Xi.setAll(t,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),ce.append(n,t),Oo(t),e(t),le.remove(t)},ag=function(n){var e=n.getSelection();if(e.rangeCount>0){var t=e.getRangeAt(0),r=n.document.createRange();r.setStart(t.startContainer,t.startOffset),r.setEnd(t.endContainer,t.endOffset),e.removeAllRanges(),e.addRange(r)}},cg={resume:function(n,e){ko().each(function(n){Jn(n,e)||To(n)}),n.focus(),Oo(Wn.fromDom(n.document.body)),ag(n)}},sg={stubborn:function(n,e,t,r){var o=function(){cg.resume(e,r)},i=jd.bind(t,"keydown",function(n){X.contains(["input","textarea"],ye.name(n.target()))||o()});return{toReading:function(){ug(n,To)},toEditing:o,onToolbarTouch:function(){},destroy:function(){i.unbind()}}},timid:function(n,e,t,r){var o=function(){To(r)};return{toReading:function(){o()},toEditing:function(){cg.resume(e,r)},onToolbarTouch:function(){o()},destroy:c.noop}}},fg=function(n,e,t,r,o){var i=function(){e.run(function(n){n.refreshSelection()})},u=function(n,t){var o=n-r.dom().scrollTop;e.run(function(n){n.scrollIntoView(o,o+t)})},a=function(){e.run(function(n){n.clearSelection()})},c=function(){n.getCursorBox().each(function(n){u(n.top(),n.height())}),e.run(function(n){n.syncHeight()})},s=Gd(n),f=ep(c,300),l=[n.onKeyup(function(){a(),f.throttle()}),n.onNodeChanged(i),n.onDomChanged(f.throttle),n.onDomChanged(i),n.onScrollToCursor(function(n){n.preventDefault(),f.throttle()}),n.onScrollToElement(function(n){n.element(),u(e,r)}),n.onToEditing(function(){e.run(function(n){n.toEditing()})}),n.onToReading(function(){e.run(function(n){n.toReading()})}),jd.bind(n.doc(),"touchend",function(e){Jn(n.html(),e.target())||Jn(n.body(),e.target())}),jd.bind(t,"transitionend",function(n){var r;"height"===n.raw().propertyName&&(r=Qi(t),e.run(function(n){n.setViewportOffset(r)}),i(),c())}),jd.capture(t,"touchstart",function(t){var r;e.run(function(n){n.highlightSelection()}),r=t,e.run(function(n){n.onToolbarTouch(r)}),n.onTouchToolstrip()}),jd.bind(n.body(),"touchstart",function(e){a(),n.onTouchContent(),s.fireTouchstart(e)}),s.onTouchmove(),s.onTouchend(),jd.bind(n.body(),"click",function(n){n.kill()}),jd.bind(t,"touchmove",function(){n.onToolbarScrollStart()})];return{destroy:function(){X.each(l,function(n){n.unbind()})}}},lg=function(n){var e=N.none(),t=[],r=function(n){o()?u(n):t.push(n)},o=function(){return e.isSome()},i=function(n){X.each(n,u)},u=function(n){e.each(function(e){setTimeout(function(){n(e)},0)})};return n(function(n){e=N.some(n),i(t),t=[]}),{get:r,map:function(n){return lg(function(e){r(function(t){e(n(t))})})},isReady:o}},dg={nu:lg,pure:function(n){return lg(function(e){e(n)})}},mg=function(n){return function(){var e=Array.prototype.slice.call(arguments),t=this;setTimeout(function(){n.apply(t,e)},0)}},pg=function(n){var e=function(e){n(mg(e))};return{map:function(n){return pg(function(t){e(function(e){var r=n(e);t(r)})})},bind:function(n){return pg(function(t){e(function(e){n(e).get(t)})})},anonBind:function(n){return pg(function(t){e(function(e){n.get(t)})})},toLazy:function(){return dg.nu(e)},get:e}},gg={nu:pg,pure:function(n){return pg(function(e){e(n)})}},hg=function(n,e,t){return Math.abs(n-e)<=t?N.none():n<e?N.some(n+t):N.some(n-t)},vg=function(){var n=null;return{animate:function(e,t,r,o,i,u){var a=!1,c=function(n){a=!0,i(n)};clearInterval(n);var s=function(e){clearInterval(n),c(e)};n=setInterval(function(){var i=e();hg(i,t,r).fold(function(){clearInterval(n),c(t)},function(r){if(o(r,s),!a){var u=e();(u!==r||Math.abs(u-t)>Math.abs(i-t))&&(clearInterval(n),c(t))}})},u)}}},yg=function(n,e){return at([{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}],function(t){return n<=t.width&&e<=t.height?N.some(t.keyboard):N.none()}).getOr({portrait:e/5,landscape:n/4})},bg=function(n){var e,t=Ld(n).isPortrait(),r=yg((e=n).screen.width,e.screen.height),o=t?r.portrait:r.landscape;return(t?n.screen.height:n.screen.width)-n.innerHeight>o?0:o},wg=function(n,e){var t=ie.owner(n).dom().defaultView;return Qi(n)+Qi(e)-bg(t)},Sg=wg,xg=function(n,e,t){var r=wg(e,t),o=Qi(e)+Qi(t)-r;Xi.set(n,"padding-bottom",o+"px")},Og=Ke([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),Tg="data-"+ii.resolve("position-y-fixed"),kg="data-"+ii.resolve("y-property"),Cg="data-"+ii.resolve("scrolling"),Eg="data-"+ii.resolve("last-window-height"),Dg=function(n){return _d(n,Tg)},Ag=function(n,e){var t=Ne.get(n,kg);return Og.fixed(n,t,e)},Mg=function(n,e){return Og.scroller(n,e)},Rg=function(n){var e=Dg(n);return("true"===Ne.get(n,Cg)?Mg:Ag)(n,e)},Fg=function(n,e,t){var r=ie.owner(n).dom().defaultView.innerHeight;return Ne.set(n,Eg,r+"px"),r-e-t},Bg=function(n){var e=yi(n,"["+Tg+"]");return X.map(e,Rg)},Ig=function(n,e,t,r){var o,i,u,a,s,f,l,d,m=ie.owner(n).dom().defaultView,p=(d=Ne.get(l=t,"style"),Xi.setAll(l,{position:"absolute",top:"0px"}),Ne.set(l,Tg,"0px"),Ne.set(l,kg,"top"),{restore:function(){Ne.set(l,"style",d||""),Ne.remove(l,Tg),Ne.remove(l,kg)}}),g=Qi(t),h=Qi(r),v=Fg(n,g,h),y=(u=g,a=v,f=Ne.get(s=n,"style"),hd.register(s),Xi.setAll(s,{position:"absolute",height:a+"px",width:"100%",top:u+"px"}),Ne.set(s,Tg,u+"px"),Ne.set(s,Cg,"true"),Ne.set(s,kg,"top"),{restore:function(){hd.deregister(s),Ne.set(s,"style",f||""),Ne.remove(s,Tg),Ne.remove(s,Cg),Ne.remove(s,kg)}}),b=(i=Ne.get(o=r,"style"),Xi.setAll(o,{position:"absolute",bottom:"0px"}),Ne.set(o,Tg,"0px"),Ne.set(o,kg,"bottom"),{restore:function(){Ne.set(o,"style",i||""),Ne.remove(o,Tg),Ne.remove(o,kg)}}),w=!0,S=function(){return m.innerHeight>_d(n,Eg)},x=function(){if(w){var o=Qi(t),i=Qi(r),u=Fg(n,o,i);Ne.set(n,Tg,o+"px"),Xi.set(n,"height",u+"px"),Xi.set(r,"bottom",-(o+u+i)+"px"),xg(e,n,r)}};return xg(e,n,r),{setViewportOffset:function(e){Ne.set(n,Tg,e+"px"),x()},isExpanding:S,isShrinking:c.not(S),refresh:x,restore:function(){w=!1,p.restore(),y.restore(),b.restore()}}},Hg=Dg,Ng=vg(),Vg="data-"+ii.resolve("last-scroll-top"),jg=function(n){var e=Xi.getRaw(n,"top").getOr(0);return parseInt(e,10)},Pg=function(n){return parseInt(n.dom().scrollTop,10)},Lg=function(n,e){var t=e+Hg(n)+"px";Xi.set(n,"top",t)},Wg=function(n,e,t){return gg.nu(function(r){var o=c.curry(Pg,n);Ng.animate(o,e,15,function(e){n.dom().scrollTop=e,Xi.set(n,"top",jg(n)+15+"px")},function(){n.dom().scrollTop=e,Xi.set(n,"top",t+"px"),r(e)},10)})},Ug=function(n,e){return gg.nu(function(t){var r=c.curry(Pg,n);Ne.set(n,Vg,r());var o=Math.abs(e-r()),i=Math.ceil(o/10);Ng.animate(r,e,i,function(e,t){_d(n,Vg)!==n.dom().scrollTop?t(n.dom().scrollTop):(n.dom().scrollTop=e,Ne.set(n,Vg,e))},function(){n.dom().scrollTop=e,Ne.set(n,Vg,e),t(e)},10)})},zg=function(n,e){return gg.nu(function(t){var r=c.curry(jg,n),o=function(e){Xi.set(n,"top",e+"px")},i=Math.abs(e-r()),u=Math.ceil(i/10);Ng.animate(r,e,u,o,function(){o(e),t(e)},10)})},Kg=function(n,e,t){var r=ie.owner(n).dom().defaultView;return gg.nu(function(o){Lg(n,t),Lg(e,t),r.scrollTo(0,t),o(t)})},Gg=function(n,e,t,r,o){var i=Sg(e,t),u=c.curry(ag,n);r>i||o>i?Ug(e,e.dom().scrollTop-i+o).get(u):r<0&&Ug(e,e.dom().scrollTop+r).get(u)},$g=function(n,e){return e(function(e){var t=[],r=0;0===n.length?e([]):X.each(n,function(o,i){var u;o.get((u=i,function(o){t[u]=o,++r>=n.length&&e(t)}))})})},qg=function(n){return $g(n,gg.nu)},_g=qg,Xg=function(n,e){return n.fold(function(n,t,r){return o=n,i=t,a=e+(u=r),Xi.set(o,i,a+"px"),gg.pure(u);var o,i,u,a},function(n,t){return r=n,i=e+(o=t),u=Xi.getRaw(r,"top").getOr(o),a=i-parseInt(u,10),c=r.dom().scrollTop+a,Wg(r,c,i);var r,o,i,u,a,c})},Yg=function(n,e){var t=Bg(n),r=X.map(t,function(n){return Xg(n,e)});return _g(r)},Jg=function(n,e,t,r,o,i){var u,a,s=(u=function(t){return Kg(n,e,t)},a=po(dg.pure({})),{start:function(n){var e=dg.nu(function(e){return u(n).get(e)});a.set(e)},idle:function(n){a.get().get(function(){n()})}}),f=ep(function(){s.idle(function(){Yg(t,r.pageYOffset).get(function(){var n;(n=Mm.getRectangles(i),N.from(n[0]).bind(function(n){var t=n.top()-e.dom().scrollTop;return t>r.innerHeight+5||t<-5?N.some({top:c.constant(t),bottom:c.constant(t+n.height())}):N.none()})).each(function(n){e.dom().scrollTop=e.dom().scrollTop+n.top()}),s.start(0),o.refresh()})})},1e3),l=jd.bind(Wn.fromDom(r),"scroll",function(){r.pageYOffset<0||f.throttle()});return Yg(t,r.pageYOffset).get(c.identity),{unbind:l.unbind}},Qg=function(n){var e=n.cWin(),t=n.ceBody(),r=n.socket(),o=n.toolstrip(),i=n.toolbar(),u=n.contentElement(),a=n.keyboardType(),s=n.outerWindow(),f=n.dropup(),l=Ig(r,t,o,f),d=a(n.outerBody(),e,Se.body(),u,o,i),m=Wd(s,{onChange:c.noop,onReady:l.refresh});m.onAdjustment(function(){l.refresh()});var p=jd.bind(Wn.fromDom(s),"resize",function(){l.isExpanding()&&l.refresh()}),g=Jg(o,r,n.outerBody(),s,l,e),h=function(n,e){var t=n.document,r=Wn.fromTag("div");co.add(r,ii.resolve("unfocused-selections")),ce.append(Wn.fromDom(t.documentElement),r);var o=jd.bind(r,"touchstart",function(t){t.prevent(),cg.resume(n,e),u()}),i=function(n){var e=Wn.fromTag("span");return kl.add(e,[ii.resolve("layer-editor"),ii.resolve("unfocused-selection")]),Xi.setAll(e,{left:n.left()+"px",top:n.top()+"px",width:n.width()+"px",height:n.height()+"px"}),e},u=function(){le.empty(r)};return{update:function(){u();var e=Mm.getRectangles(n),t=X.map(e,i);se.append(r,t)},isActive:function(){return ie.children(r).length>0},destroy:function(){o.unbind(),le.remove(r)},clear:u}}(e,u),v=function(){h.clear()};return{toEditing:function(){d.toEditing(),v()},toReading:function(){d.toReading()},onToolbarTouch:function(n){d.onToolbarTouch(n)},refreshSelection:function(){h.isActive()&&h.update()},clearSelection:v,highlightSelection:function(){h.update()},scrollIntoView:function(n,t){Gg(e,r,f,n,t)},updateToolbarPadding:c.noop,setViewportOffset:function(n){l.setViewportOffset(n),zg(r,n).get(c.identity)},syncHeight:function(){Xi.set(u,"height",u.dom().contentWindow.document.body.scrollHeight+"px")},refreshStructure:l.refresh,destroy:function(){l.restore(),m.destroy(),g.unbind(),p.unbind(),d.destroy(),h.destroy(),ug(Se.body(),To)}}},Zg=function(n,e){var t=Qm(),r=Wf.value(),o=Wf.value(),i=Wf.api(),u=Wf.api();return{enter:function(){e.hide();var a=Wn.fromDom(document);Gm.getActiveApi(n.editor).each(function(e){r.set({socketHeight:Xi.getRaw(n.socket,"height"),iframeHeight:Xi.getRaw(e.frame(),"height"),outerScroll:document.body.scrollTop}),o.set({exclusives:vp.exclusive(a,"."+hd.scrollable())}),co.add(n.container,ii.resolve("fullscreen-maximized")),Ym(n.container,e.body()),t.maximize(),Xi.set(n.socket,"overflow","scroll"),Xi.set(n.socket,"-webkit-overflow-scrolling","touch"),Oo(e.body());var s=In.immutableBag(["cWin","ceBody","socket","toolstrip","toolbar","dropup","contentElement","cursor","keyboardType","isScrolling","outerWindow","outerBody"],[]);i.set(Qg(s({cWin:e.win(),ceBody:e.body(),socket:n.socket,toolstrip:n.toolstrip,toolbar:n.toolbar,dropup:n.dropup.element(),contentElement:e.frame(),cursor:c.noop,outerBody:n.body,outerWindow:n.win,keyboardType:sg.stubborn,isScrolling:function(){return o.get().exists(function(n){return n.socket.isScrolling()})}}))),i.run(function(n){n.syncHeight()}),u.set(fg(e,i,n.toolstrip,n.socket,n.dropup))})},refreshStructure:function(){i.run(function(n){n.refreshStructure()})},exit:function(){t.restore(),u.clear(),i.clear(),e.show(),r.on(function(e){e.socketHeight.each(function(e){Xi.set(n.socket,"height",e)}),e.iframeHeight.each(function(e){Xi.set(n.editor.getFrame(),"height",e)}),document.body.scrollTop=e.scrollTop}),r.clear(),o.on(function(n){n.exclusives.unbind()}),o.clear(),co.remove(n.container,ii.resolve("fullscreen-maximized")),Jm(),hd.deregister(n.toolbar),Xi.remove(n.socket,"overflow"),Xi.remove(n.socket,"-webkit-overflow-scrolling"),To(n.editor.getFrame()),Gm.getActiveApi(n.editor).each(function(n){n.clearSelection()})}}},nh={produce:function(n){var e=mr.asRawOrDie("Getting IosWebapp schema",rp,n);Xi.set(e.toolstrip,"width","100%"),Xi.set(e.container,"position","relative");var t=Hl.build(tp(function(){e.setReadOnly(!0),r.enter()},e.translate));e.alloy.add(t);var r=Zg(e,{show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}});return{setReadOnly:e.setReadOnly,refreshStructure:r.refreshStructure,enter:r.enter,exit:r.exit,destroy:c.noop}}},eh=tinymce.util.Tools.resolve("tinymce.EditorManager"),th=function(n){var e=it.readOptFrom(n.settings,"skin_url").fold(function(){return eh.baseURL+"/skins/lightgray"},function(n){return n});return{content:e+"/content.mobile.min.css",ui:e+"/skin.mobile.min.css"}},rh=function(n,e,t){n.system().broadcastOn([Ro.formatChanged()],{command:e,state:t})},oh=function(n,e){var t=On.keys(e.formatter.get());X.each(t,function(t){e.formatter.formatChanged(t,function(e){rh(n,t,e)})}),X.each(["ul","ol"],function(t){e.selection.selectorChanged(t,function(e,r){rh(n,t,e)})})},ih=(c.constant(["x-small","small","medium","large","x-large"]),function(n){var e=function(){n._skinLoaded=!0,n.fire("SkinLoaded")};return function(){n.initialized?e():n.on("init",e)}}),uh=c.constant("toReading"),ah=c.constant("toEditing");Do.add("mobile",function(n){return{getNotificationManagerImpl:function(){return{open:c.identity,close:c.noop,reposition:c.noop,getArgs:c.identity}},renderUI:function(e){var t=th(n);!1===Mo(n)?(n.contentCSS.push(t.content),Eo.DOM.styleSheetLoader.load(t.ui,ih(n))):ih(n)();var r,o,i,u,a,s,f,l,d,m,p,g,h,v,y=function(){n.fire("scrollIntoView")},b=Wn.fromTag("div"),w=sn.detect().os.isAndroid()?(l=y,d=ig({classes:[ii.resolve("android-container")]}),m=yp(),p=Wf.api(),g=Sp.makeEditSwitch(p),h=Sp.makeSocket(),v=Vp.build(c.noop,l),d.add(m.wrapper()),d.add(h),d.add(v.component()),{system:c.constant(d),element:d.element,init:function(n){p.set(op.produce(n))},exit:function(){p.run(function(n){n.exit(),ed.remove(h,g)})},setToolbarGroups:function(n){var e=m.createGroups(n);m.setGroups(e)},setContextToolbar:function(n){var e=m.createGroups(n);m.setContextToolbar(e)},focusToolbar:function(){m.focus()},restoreToolbar:function(){m.restoreToolbar()},updateMode:function(n){Sp.updateMode(h,g,n,d.root())},socket:c.constant(h),dropup:c.constant(v)}):(r=y,o=ig({classes:[ii.resolve("ios-container")]}),i=yp(),u=Wf.api(),a=Sp.makeEditSwitch(u),s=Sp.makeSocket(),f=Vp.build(function(){u.run(function(n){n.refreshStructure()})},r),o.add(i.wrapper()),o.add(s),o.add(f.component()),{system:c.constant(o),element:o.element,init:function(n){u.set(nh.produce(n))},exit:function(){u.run(function(n){ed.remove(s,a),n.exit()})},setToolbarGroups:function(n){var e=i.createGroups(n);i.setGroups(e)},setContextToolbar:function(n){var e=i.createGroups(n);i.setContextToolbar(e)},focusToolbar:function(){i.focus()},restoreToolbar:function(){i.restoreToolbar()},updateMode:function(n){Sp.updateMode(s,a,n,o.root())},socket:c.constant(s),dropup:c.constant(f)}),S=Wn.fromDom(e.targetNode);ce.after(S,b),Ce.attachSystem(b,w.system());var x=e.targetNode.ownerDocument.defaultView,O=Wd(x,{onChange:function(){w.system().broadcastOn([Ro.orientationChanged()],{width:Ud(x)})},onReady:c.noop}),T=function(e,t,r){!1===r&&n.selection.collapse(),w.setToolbarGroups(r?e.get():t.get()),n.setMode(!0===r?"readonly":"design"),n.fire(!0===r?uh():ah()),w.updateMode(r)},k=function(e,t){return n.on(e,t),{unbind:function(){n.off(e)}}};return n.on("init",function(){w.init({editor:{getFrame:function(){return Wn.fromDom(n.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:c.noop}},onToReading:function(n){return k(uh(),n)},onToEditing:function(n){return k(ah(),n)},onScrollToCursor:function(e){return n.on("scrollIntoView",function(n){e(n)}),{unbind:function(){n.off("scrollIntoView"),O.destroy()}}},onTouchToolstrip:function(){e()},onTouchContent:function(){var t,r=Wn.fromDom(n.editorContainer.querySelector("."+ii.resolve("toolbar")));(t=r,Co(t).bind(function(n){return w.system().getByDom(n).toOption()})).each(Dn),w.restoreToolbar(),e()},onTapContent:function(e){var t=e.target();"img"===ye.name(t)?(n.selection.select(t.dom()),e.kill()):"a"===ye.name(t)&&w.system().getByDom(Wn.fromDom(n.editorContainer)).each(function(n){mo.isAlpha(n)&&Ao(t.dom())})}},container:Wn.fromDom(n.editorContainer),socket:Wn.fromDom(n.contentAreaContainer),toolstrip:Wn.fromDom(n.editorContainer.querySelector("."+ii.resolve("toolstrip"))),toolbar:Wn.fromDom(n.editorContainer.querySelector("."+ii.resolve("toolbar"))),dropup:w.dropup(),alloy:w.system(),translate:c.noop,setReadOnly:function(n){T(s,a,n)}});var e=function(){w.dropup().disappear(function(){w.system().broadcastOn([Ro.dropupDismissed()],{})})};vt("remove this",w.system());var t={label:"The first group",scrollable:!1,items:[$c.forToolbar("back",function(){n.selection.collapse(),w.exit()},{})]},r={label:"Back to read only",scrollable:!1,items:[$c.forToolbar("readonly-back",function(){T(s,a,!0)},{})]},o=Md(w,n),i=Rd(n.settings,o),u={label:"The extra group",scrollable:!1,items:[]},a=po([r,{label:"the action group",scrollable:!0,items:i},u]),s=po([t,{label:"The read only mode group",scrollable:!0,items:[]},u]);oh(w,n)}),{iframeContainer:w.socket().element().dom(),editorContainer:w.element().dom()}}}})}();
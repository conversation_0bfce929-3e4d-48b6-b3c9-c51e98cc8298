var EcommerceProducts=function(){var e=function(){$(".date-picker").datepicker({rtl:App.isRTL(),autoclose:!0})},a=function(){var e=new Datatable;e.init({src:$("#datatable_products"),onSuccess:function(e){},onError:function(e){},loadingMessage:"Loading...",dataTable:{lengthMenu:[[10,20,50,100,150],[10,20,50,100,150]],pageLength:10,ajax:{url:"../demo/ecommerce_products.php"},order:[[1,"asc"]]}}),e.getTableWrapper().on("click",".table-group-action-submit",function(a){a.preventDefault();var t=$(".table-group-action-input",e.getTableWrapper());""!=t.val()&&e.getSelectedRowsCount()>0?(e.setAjaxParam("customActionType","group_action"),e.setAjaxParam("customActionName",t.val()),e.setAjaxParam("id",e.getSelectedRows()),e.getDataTable().ajax.reload(),e.clearAjaxParams()):""==t.val()?App.alert({type:"danger",icon:"warning",message:"Please select an action",container:e.getTableWrapper(),place:"prepend"}):0===e.getSelectedRowsCount()&&App.alert({type:"danger",icon:"warning",message:"No record selected",container:e.getTableWrapper(),place:"prepend"})})};return{init:function(){a(),e()}}}();jQuery(document).ready(function(){EcommerceProducts.init()});
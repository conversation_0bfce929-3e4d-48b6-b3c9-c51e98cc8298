<?php

namespace Modules\General\Http\Controllers;


use App\Admission;
use App\Center;
use App\CenterEmployee;
use App\MoshafSurah;
use App\Scopes\OrganizationScope;
use App\Student;
use App\StudentHefzPlan;
use App\StudentNouranyaPlan;
use Carbon\Carbon;
use Illuminate\Http\Request;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Services\StudentImageService;


class NouranyaPlanDatatablesController extends Controller
{
    protected $studentImageService;

    public function __construct(StudentImageService $studentImageService)
    {
        $this->studentLevelService = $studentLevelService;
        $this->studentImageService = $studentImageService;
    }


    public function getPlansNeedApproval(Request $request)
    {



        if ($request->ajax()) {



            $classroomIds = $request->input('classroomId'); // Get classroom IDs from the request


            if (auth()->user()->hasRole(["managing-director_2_"])) {
                $studentsApprovalQuery = StudentNouranyaPlan::where('status', '=', 'waiting_for_approval')
                    ->where(function ($query) {
                        $query->where(function ($query) {
                            $query->whereNotNull('from_lesson')
                                ->orWhereNotNull('to_lesson');
                        })
                            ->orWhere(function ($query) {
                                $query->whereNotNull('talaqqi_from_lesson')
                                    ->orWhereNotNull('talqeen_from_lesson')
                                    ->orWhereNotNull('talaqqi_to_lesson')
                                    ->orWhereNotNull('talqeen_to_lesson');
                            });
                    })
                    ->whereNotNull('class_id')
                    ->when($classroomIds, function ($query) use ($classroomIds) {
                        return $query->whereIn('class_id', $classroomIds);
                    })
                    ->with('student')->with('center')->orderBy('updated_at', 'DESC');
            } else {
                $studentsApprovalQuery = StudentNouranyaPlan::where('status', '=', 'waiting_for_approval')
                    ->where(function ($query) {
                        $query->where(function ($query) {
                            $query->whereNotNull('from_lesson')
                                ->orWhereNotNull('to_lesson');
                        })
                            ->orWhere(function ($query) {
                                $query->whereNotNull('talaqqi_from_lesson')
                                    ->orWhereNotNull('talqeen_from_lesson')
                                    ->orWhereNotNull('talaqqi_to_lesson')
                                    ->orWhereNotNull('talqeen_to_lesson');
                            });
                    })
                    ->whereNotNull('class_id')
                    ->whereIn('center_id', auth()->user()->center->pluck('id')->toArray())
                    ->when($classroomIds, function ($query) use ($classroomIds) {
                        return $query->whereIn('class_id', $classroomIds);
                    })
                    ->with('student')->with('center')->orderBy('updated_at', 'DESC');
            }




            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
                ->addColumn('select', function ($row) {
                    return '<input type="checkbox" class="checkbox" name="id[]" value="'.$row->id.'">';
                })
                ->addColumn('studentName', function ($row) {
                    $student = $row->student;

                    // Ensure the student relationship exists
                    if (!$student) {
                        return '<span class="student-name">N/A</span>';
                    }

                    // Clean and format the student's full name
                    $cleanName = str_replace(['"', "'"], '', $student->full_name);
                    $studentName = ucwords(strtolower($cleanName));
                    $studentNameLimited = Str::limit($studentName, 15, '...');

                    // Determine the gender-based placeholder image
                    $gender = strtolower($student->gender);
                    $femalePlaceholder = asset('assets/workplace/img/female_student_profile_placeholder.png');
                    $malePlaceholder = asset('assets/workplace/img/male_profile_picture_placeholder.png');
                    $genderBasedDefaultImage = ($gender === 'female') ? $femalePlaceholder : $malePlaceholder;

                    // Determine the image URL (fix null check)
                    $imageUrl = (!empty($student->student_photo) && is_string($student->student_photo) && Storage::disk('public')->exists($student->student_photo))
                        ? Storage::url($student->student_photo)
                        : $genderBasedDefaultImage;

                    // Sanitize names
                    $safeFullName = e($studentName);
                    $safeTruncatedName = e($studentNameLimited);

                    // Generate the student profile route
                    $studentProfileRoute = route('students.show', $student->user_id);

                    // Create tooltip with link to student profile, including inline CSS effects
                    $nameWithTooltip = '<a href="' . $studentProfileRoute . '" target="_blank" class="student-name-tooltip" data-toggle="tooltip" title="' . $safeFullName . '" style="text-decoration: none; color: #007bff; cursor: pointer;" onmouseover="this.style.textDecoration=\'underline\';" onmouseout="this.style.textDecoration=\'none\';">' . $safeTruncatedName . '</a>';

                    // Generate edit monthly plan route
                    $stEditMonthlyPlanRoute = route('monthly-plan.show', ['id' => $row->class_id, 'from_date' => \Carbon\Carbon::parse($row->start_date)->toDateString()]);

                    // Action buttons HTML
                    $actionButtons = '
                        <div class="row-action-buttons" style="margin-top: 8px; display: flex; gap: 4px; flex-wrap: wrap;">
                            <button type="button" class="btn btn-success nouranya-individual-approve-btn" 
                                    data-nouranya-plan-id="' . $row->id . '" 
                                    data-student-name="' . $safeFullName . '"
                                    title="Approve Plan"
                                    style="padding: 2px 6px; font-size: 10px; line-height: 1.2; border-radius: 3px; min-width: auto;">
                                <i class="glyphicon glyphicon-ok" style="font-size: 9px;"></i> Approve
                            </button>
                            <a href="' . $stEditMonthlyPlanRoute . '" target="_blank" 
                               class="btn btn-primary" title="Edit Plan"
                               style="padding: 2px 6px; font-size: 10px; line-height: 1.2; border-radius: 3px; min-width: auto; text-decoration: none; display: inline-block;">
                                <i class="glyphicon glyphicon-edit" style="font-size: 9px;"></i> Edit
                            </a>
                            <button type="button" class="btn btn-warning commentNouranyaModalTriggerBtn" 
                                    data-nouranya-plan_id="' . $row->id . '"
                                    data-nouranya-supervisor_comment="' . optional($row)->supervisor_comment . '"
                                    data-nouranya-student_id="' . $row->student_id . '"
                                    data-catid="' . $row->id . '" 
                                    data-toggle="modal"
                                    data-target="#commentNouranyaPlan"
                                    title="Add Comment"
                                    style="padding: 2px 6px; font-size: 10px; line-height: 1.2; border-radius: 3px; min-width: auto;">
                                <i class="glyphicon glyphicon-comment" style="font-size: 9px;"></i> Comment
                            </button>
                        </div>';

                        $image = $student ? $this->studentImageService->getStudentImageUrl($student) : asset('maleStudentProfilePicture.png');


                    // Combine HTML
                    $html = '
<div class="student-container">
    <div style="flex-shrink: 0;">
                                <img class="studentImage" style="border-radius: 50%; width: 60px; height: 60px; object-fit: cover;" src="' . $image . '">
    </div>
    <div class="student-details">
        ' . $nameWithTooltip . '
        ' . $actionButtons . '
    </div>
</div>
';

                    return $html;
                })
                ->addColumn('studentAge', function ($row) {

                    return $row->student->age;

                })
                ->addColumn('halaqah', function ($row) {
                    $stEditMonthlyPlanRoute = route('monthly-plan.show', [$row->class_id, $row->created_at->format('Y-m-d')]);
                    return '<a href="' . e($stEditMonthlyPlanRoute) . '" target="_blank" class="halaqah-link">' . e($row->halaqah->name) . '</a>';
                })
                ->addColumn('centerName', function ($row) {
                    return $row->center->name;

                }) ->addColumn('level_id', function ($row) {
                    return $row->programLevel->program_level_order;

                })
                ->addColumn('createdByUpdatedBy', function ($row) {


                    if( $row->created_by === $row->updated_by){



//                        $stShowRoute = route('admission.students.show', $row->allStudents->user->id);
                        $genderColor = $row->creator['gender'] == 'male' ? '#34b8bc;!important' : '#FA5661;!important';

                        if (strlen($row->creator['full_name']) > 22) {
                            $fullname = Str::limit(Str::title($row->creator['full_name']),22,' ...');
//                            $fullname = Str::title($row->creator['full_name']);


                            return '<a style="color:' . $genderColor . '" target="_blank" href="#" ><strong data-tooltip="' . Str::title($row->creator['full_name']) . '" >' . $fullname . '</strong></a>';
                        } else {
                            $fullname = Str::title($row->creator['full_name']);
                            return '<a style="color:' . $genderColor . '" target="_blank" href="#" ><strong data-tooltip="' . Str::title($row->creator['full_name']) . '" style="color:"' . $genderColor . '">' . $fullname . '</strong></a>';
                        }



//                        return  $row->creator['full_name'];
                        }
                        else{

                            $genderColor = $row->creator['gender'] == 'male' ? '#34b8bc;!important' : '#FA5661;!important';



                            if (strlen($row->creator['full_name']) > 22) {
                                $creatorFullname = Str::limit(Str::title($row->creator['full_name']),22,' ...');


                            } else {
                                $creatorFullname = Str::title($row->creator['full_name']);

                            }
                            if (strlen($row->updator['full_name']) > 22) {
                                $updatorFullname = Str::limit(Str::title($row->updator['full_name']),22,' ...');


                            } else {
                                $updatorFullname = Str::title($row->updator['full_name']);

                            }

                            return '<a style="color:' . $genderColor . '" target="_blank" href="#" ><strong data-tooltip="' . Str::title($row->creator['full_name'].'/'.$row->updator['full_name']) . '" >' .  $creatorFullname.' / '. $updatorFullname . '</strong></a>';




                        }





                })
                ->addColumn('planUpdateDate', function ($row) {
                    $planDetails = $row->updated_at->diffForHumans();
                    return $planDetails;

                })

                ->addColumn('fromtoLesson', function ($row) {



                    // there are 3 different levels for the nouranya plan. for each make a seperate condition
                    // Check main lesson values
                    $fromLesson = $row->from_lesson ?? null;
                    $toLesson = $row->to_lesson ?? null;
                    $fromLessonLineNumber = $row->from_lesson_line_number ?? null;
                    $toLessonLineNumber = $row->to_lesson_line_number ?? null;

                    // Check Talaqi lesson values
                    $talaqqiFromLesson = $row->talaqqi_from_lesson ?? null;
                    $talaqqiToLesson = $row->talaqqi_to_lesson ?? null;

                    // Check Talqeen lesson values
                    $talqeenFromLesson = $row->talqeen_from_lesson ?? null;
                    $talqeenToLesson = $row->talqeen_to_lesson ?? null;

                    // Initialize lesson display string
                    $lessonDisplay = '';



                    if ($fromLesson && $toLesson) {
                        // Prepare the display value for the "from" lesson
                        $fromDisplay = $fromLesson;
                        if (!empty($fromLessonLineNumber)) {
                            $fromDisplay .= '.' . $fromLessonLineNumber;
                        }

                        // Prepare the display value for the "to" lesson
                        $toDisplay = $toLesson;
                        if (!empty($toLessonLineNumber)) {
                            $toDisplay .= '.' . $toLessonLineNumber;
                        }

                        // Build the lesson display links
                        $lessonDisplay .= '<a href="#" data-toggle="tooltip" title="' . $fromDisplay . '">' . $fromDisplay . '</a> - ';
                        $lessonDisplay .= '<a href="#" data-toggle="tooltip" title="' . $toDisplay . '">' . $toDisplay . '</a>';
                    }
                    else {
                        // Display Talaqi lessons if main lessons are null or empty
                        if ($talaqqiFromLesson && $talaqqiToLesson) {
                            $talaqqiFromDisplay = MoshafSurah::find($talaqqiFromLesson)->eng_name . ' : ' . $talaqqiFromLesson;
                            $talaqqiToDisplay = MoshafSurah::find($talaqqiToLesson)->eng_name . ' : ' . $talaqqiToLesson;

                            $lessonDisplay .= '<div><strong>Talaqi From:</strong> <a href="#" data-toggle="tooltip" title="' . $talaqqiFromDisplay . '">' . $talaqqiFromLesson . '</a> - ';
                            $lessonDisplay .= '<strong>Talaqi To:</strong> <a href="#" data-toggle="tooltip" title="' . $talaqqiToDisplay . '">' . $talaqqiToLesson . '</a></div>';
                        }

                        // Display Talqeen lessons if available
                        if ($talqeenFromLesson && $talqeenToLesson) {
                            $talqeenFromDisplay = MoshafSurah::find($talqeenFromLesson)->eng_name . ' : ' . $talqeenFromLesson;
                            $talqeenToDisplay = MoshafSurah::find($talqeenToLesson)->eng_name . ' : ' . $talqeenToLesson;

                            $lessonDisplay .= '<div><strong>Talqeen From:</strong> <a href="#" data-toggle="tooltip" title="' . $talqeenFromDisplay . '">' . $talqeenFromLesson . '</a> - ';
                            $lessonDisplay .= '<strong>Talqeen To:</strong> <a href="#" data-toggle="tooltip" title="' . $talqeenToDisplay . '">' . $talqeenToLesson . '</a></div>';
                        }
                    }

                    return $lessonDisplay ?: 'No lessons assigned';




//                        return   '<a data-placement="top" href="#" data-toggle="tooltip" title="'.$row->from_lesson.'">'.$row->from_lesson.' - </a><a data-placement="top" href="#" data-toggle="tooltip" title="'.$row->to_lesson.'">'.$row->to_lesson.'</a>';


                })
                ->addColumn('planDate', function ($row) {

                        return Carbon::parse($row->start_date)->format('F Y');

                })
                ->addColumn('created_at', function ($row) {

                    return value($row['created_at'])->diffForHumans();
                })
                ->setRowAttr([
                    'data-nouranyaPlan_id' => function($row) {
                        return $row->id; // Assuming $row->id is your report ID
                    },
                    'data-studentName' => function($row) {
                        return $row->student->full_name; // Assuming $row->id is your report ID
                    },

                    'data-studentMonthlyNouranyaPlanRoute' => function($row) {
                        $stEditMonthlyPlanRoute = route('monthly-plan.show',[$row->class_id,$row->created_at->format('Y-m-d')]);

                        return $stEditMonthlyPlanRoute; // Assuming $row->id is your report ID
                    },
                    'data-nouranyaPlan-student_id' => function($row) {
                        return $row->student->id; // Assuming $row->student->id is your student ID
                    }
                ])
                ->rawColumns(['select','halaqah','studentName','fromtoLesson','planUpdateDate', 'createdByUpdatedBy'])
                ->toJson();

        }


    }

}


//            }







@startuml Quran Studies Module Schema

!theme vibrant

entity "students" {
  * id: int
  --
  full_name: varchar(255)
  organization_id: int
}

entity "hefz_levels" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "student_hefz_plans" {
  * id: int
  --
  student_id: int <<FK>>
  surah_id: int <<FK>>
  start_verse: int
  end_verse: int
  organization_id: int
}

entity "student_hefz_report" {
  * id: int
  --
  student_id: int <<FK>>
  plan_id: int <<FK>>
  report_date: date
  grade: varchar(255)
  notes: text
  organization_id: int
}

entity "student_revision_plans" {
  * id: int
  --
  student_id: int <<FK>>
  surah_id: int <<FK>>
  start_verse: int
  end_verse: int
  organization_id: int
}

entity "student_revision_report" {
  * id: int
  --
  student_id: int <<FK>>
  plan_id: int <<FK>>
  report_date: date
  grade: varchar(255)
  notes: text
  organization_id: int
}

entity "moshaf" {
  * id: int
  --
  name: varchar(255)
  type: varchar(255)
  organization_id: int
}

entity "moshaf_surah" {
  * id: int
  --
  moshaf_id: int <<FK>>
  name: varchar(255)
  number: int
  verses_count: int
}

entity "moshaf_juz" {
  * id: int
  --
  moshaf_id: int <<FK>>
  number: int
  name: varchar(255)
}

entity "moshaf_pages" {
  * id: int
  --
  moshaf_id: int <<FK>>
  page_number: int
}

entity "surah" {
  * id: int
  --
  name: varchar(255)
  number: int
  verses_count: int
}

students ||--o{ student_hefz_plans : "has"
students ||--o{ student_revision_plans : "has"
student_hefz_plans ||--o{ student_hefz_report : "evaluated in"
student_revision_plans ||--o{ student_revision_report : "evaluated in"
moshaf ||--o{ moshaf_surah : "contains"
moshaf ||--o{ moshaf_juz : "contains"
moshaf ||--o{ moshaf_pages : "has"
moshaf_surah }o--|| surah : "references"
student_hefz_plans }o--|| surah : "memorizes"
student_revision_plans }o--|| surah : "revises"

@enduml

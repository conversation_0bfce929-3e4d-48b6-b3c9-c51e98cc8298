<div class="col-md-12">
    
<div class="form-group {{ $errors->has('parent') ? 'has-error' : ''}}">
    {!! Form::label('parent', 'Parent', ['class' => 'control-label']) !!}
    
        {!! Form::select('parent', [ null => "==Root=="] +  $menus->toArray() , null, ['class' => 'form-control']) !!}
        {!! $errors->first('parent', '
        <p class="help-block">
            :message
        </p>
        ') !!}
</div>
<div class="form-group {{ $errors->has('type') ? 'has-error' : ''}}">
    {!! Form::label('type', 'Page Type', ['class' => 'control-label']) !!}
    
        {!! Form::select('type', [1 => 'Content' , 2 => 'Link' , 3 => 'Text Only'] , null, ['class' => 'form-control' , 'id' => 'type']) !!}
        {!! $errors->first('type', '
        <p class="help-block">
            :message
        </p>
        ') !!}
</div>
<div class="nav-tabs-custom">
    <ul class="nav nav-tabs">
        @foreach($languages as $key => $language)
        <li @if($key == 0 ) class="active" @endif>
            <a aria-expanded="true" data-toggle="tab" href="#{{$language}}">
                {{strtoupper($language)}}
            </a>
        </li>
        @endforeach
    </ul>
    <div class="tab-content">
        @foreach($languages as $key => $language)
        <div class="tab-pane clearfix @if($key == 0 ) active @endif" id="{{$language}}">
            <div class="col-md-12">
                <div class="form-group {{ $errors->has('translate.'.$language.'.title') ? 'has-error' : ''}}">
                    {!! Form::label('title', 'Title ['.$language.']', ['class' => 'control-label']) !!}
                
                    {!! Form::text('translate['.$language.'][title]', isset($menu_detail) && isset($menu_detail->translate($language)->title) ? $menu_detail->translate($language)->title : '' , ['class' => 'form-control']) !!}
                    {!! $errors->first('translate.'.$language.'.title', '
                    <p class="help-block">
                        :message
                    </p>
                    ') !!}
                </div>

                <div class="form-group {{ $errors->has('translate.'.$language.'.content') ? 'has-error' : ''}} description" >
                    {!! Form::label('content', 'Content ['.$language.']', ['class' => 'control-label']) !!}
                    {!! Form::textarea('translate['.$language.'][content]',isset($menu_detail) && isset($menu_detail->translate($language)->content) ? $menu_detail->translate($language)->content : '' , ['class' => 'form-control']) !!}
                    {!! $errors->first('translate.'.$language.'.content', '
                    <p class="help-block">
                        :message
                    </p>
                    ') !!}
                </div>
            </div>
        </div>
        <!-- /.tab-pane -->
        @endforeach
    </div>
    <!-- /.tab-content -->
</div>
<!-- /.nav-tabs-custom -->
<div class="col-md-12">
    <div class="form-group {{ $errors->has('slug') ? 'has-error' : ''}} link">
        {!! Form::label('slug', 'Page URL', ['class' => 'control-label']) !!}
        
            {!! Form::text('slug', null, ['class' => 'form-control']) !!}
            {!! $errors->first('slug', '
            <p class="help-block">
                :message
            </p>
            ') !!}
    </div>

    <div class="form-group {{ $errors->has('status') ? 'has-error' : ''}}">
        {!! Form::label('status', 'Status', ['class' => 'control-label']) !!}
        
            {!! Form::select('status', [1 => 'Published' , 0 => 'Disabled/Not Published'] , null, ['class' => 'form-control']) !!}
            {!! $errors->first('status', '
            <p class="help-block">
                :message
            </p>
            ') !!}
    </div>
    <div class="form-group">
        <div class="col-md-offset-4" >
            {!! Form::submit(isset($submitButtonText) ? $submitButtonText : 'Create', ['class' => 'btn btn-primary']) !!}
        </div>
    </div>
    </div>
</div>
@section('css')
<link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.6/summernote.css" rel="stylesheet">
@endsection
@section('js')
<script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.8/summernote.js"></script>
<script src="{{ asset('assets/common/js/summernote-cleaner.js') }}"></script>
<script src="{{ asset('assets/lfm/js/lfm.js') }}"></script>

<script>
    
    var updateMenuType = function () {
        var type = $('#type').val();
        switch(type){
            case "2":
            
                $('.link').show();
                $('.description').hide();
                break;
            case "3":
                $('.link').hide();
                $('.description').hide();
                break;
            default:
                $('.link').show();
                $('.description').show();
        }
    }

    $('#type').change(function() {
        updateMenuType();
    });

    $(document).ready(function() {
        // Define function to open filemanager window
        var lfm = function(options, cb) {
            var route_prefix = (options && options.prefix) ? options.prefix : '/en/manage/uploader';
            window.open(route_prefix + '?type=' + options.type || 'file', 'FileManager', 'width=900,height=600');
            window.SetUrl = cb;
        };
        var LFMButton = function(context) {
        var ui = $.summernote.ui;
        {{--  var button = ui.button({
            contents: '<i class="note-icon-picture"></i> ',
            tooltip: 'Insert image with filemanager',
            click: function() {
	    
                lfm({type: 'image', prefix: '/en/manage/uploader'}, function(url, path) {
                    context.invoke('insertImage', url);
                });

            }
        });  --}}
        var button = ui.button({
            contents: '<i class="fa fa-doc"></i> ',
            tooltip: 'Upload PDF with filemanager',
            click: function() {
	    
                lfm({type: 'link', prefix: '/en/manage/uploader'}, function(url, path) {
                    context.invoke('createLink',{
                    text: 'Doc Link',
                    url: url,
                    isNewWindow: true
                    });
                });


            }
        });
        return button.render();
    };


        updateMenuType();
        $('textarea').summernote({
    toolbar:[
        ['cleaner',['cleaner']], // The Button
        ['style',['style']],
        ['font',['bold','italic','underline','clear']],
        ['fontname',['fontname']],
        ['color',['color']],
        ['para',['ul','ol','paragraph']],
        ['height',['height']],
        ['table',['table']],
        ['insert',['media','link','hr']],
        ['view',['fullscreen','codeview']],
        ['popovers', ['lfm']],
        ['help',['help']]
    ],buttons: {
            lfm: LFMButton
        },
    cleaner:{
          notTime: 2400, // Time to display Notifications.
          action: 'both', // both|button|paste 'button' only cleans via toolbar button, 'paste' only clean when pasting content, both does both options.
          newline: '<br>', // Summernote's default is to use '<p><br></p>'
          notStyle: 'position:absolute;top:0;left:0;right:0', // Position of Notification
          icon: '<i class="note-icon">[Your Button]</i>',
          keepHtml: false, // Remove all Html formats
          {{--  keepOnlyTags: ['<p>', '<br>', '<ul>', '<li>', '<b>', '<strong>','<i>', '<a>'], // If keepHtml is true, remove all tags except these  --}}
          keepClasses: false, // Remove Classes
          badTags: ['style', 'script', 'applet', 'embed', 'noframes', 'noscript', 'html'], // Remove full tags with contents
          badAttributes: ['style', 'start'] // Remove attributes from remaining tags
    }
});
        {{--  $('textarea').summernote({
            minHeight : 300
        });  --}}
    });

</script>
@endsection
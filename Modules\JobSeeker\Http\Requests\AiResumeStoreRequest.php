<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\File;
use Modules\JobSeeker\Entities\JobSeekerResume;

/**
 * AiResumeStoreRequest validates file uploads for AI resume tailoring.
 * 
 * Purpose: Enforce file type, size, count limits and prevent abuse for resume uploads.
 * Business rules: PDF/DOC/DOCX/TXT only; max 5MB; max 5 per user; MIME validation.
 * Security: Validates against malicious files, enforces user ownership, prevents overflows.
 */
final class AiResumeStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request
     */
    public function authorize(): bool
    {
        // User must be authenticated as job_seeker and have manage_resumes permission
        return auth('job_seeker')->check() && 
               auth('job_seeker')->user()->can('jobseeker.manage_resumes');
    }

    /**
     * Get the validation rules that apply to the request
     */
    public function rules(): array
    {
        return [
            'resume_file' => [
                'required',
                'file',
                File::types(['pdf', 'doc', 'docx', 'txt'])
                    ->max(5 * 1024) // 5MB in KB
                    ->rules(['mimes:pdf,doc,docx,txt']),
            ],
            'title' => [
                'required',
                'string',
                'min:3',
                'max:150',
                'regex:/^[a-zA-Z0-9\s\-_().]+$/', // Alphanumeric with common punctuation
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules
     */
    public function messages(): array
    {
        return [
            'resume_file.required' => 'Please select a resume file to upload.',
            'resume_file.file' => 'The uploaded item must be a valid file.',
            'resume_file.mimes' => 'Only PDF, DOC, DOCX, and TXT files are allowed.',
            'resume_file.max' => 'Resume file size cannot exceed 5MB.',
            'title.required' => 'Please provide a title for your resume.',
            'title.min' => 'Resume title must be at least 3 characters long.',
            'title.max' => 'Resume title cannot exceed 150 characters.',
            'title.regex' => 'Resume title contains invalid characters. Use only letters, numbers, and common punctuation.',
        ];
    }

    /**
     * Configure the validator instance
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if user has reached the 5-resume limit
            $currentCount = JobSeekerResume::countForJobSeeker(auth('job_seeker')->id());
            
            if ($currentCount >= 5) {
                $validator->errors()->add('resume_file', 
                    'You can only store up to 5 resumes. Please delete an existing resume before uploading a new one.');
            }

            // Additional MIME type validation for security
            if ($this->hasFile('resume_file')) {
                $file = $this->file('resume_file');
                $detectedMime = $file->getMimeType();
                
                $allowedMimes = [
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'text/plain',
                ];
                
                if (!in_array($detectedMime, $allowedMimes)) {
                    $validator->errors()->add('resume_file', 
                        'File type not allowed. Detected type: ' . $detectedMime);
                }

                // Check for zero-byte files
                if ($file->getSize() === 0) {
                    $validator->errors()->add('resume_file', 
                        'Empty files are not allowed.');
                }
            }
        });
    }

    /**
     * Get custom attributes for error messages
     */
    public function attributes(): array
    {
        return [
            'resume_file' => 'resume file',
            'title' => 'resume title',
        ];
    }

    /**
     * Handle a failed validation attempt
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        // Log validation failures for monitoring
        \Illuminate\Support\Facades\Log::info('Resume upload validation failed', [
            'user_id' => auth('job_seeker')->id(),
            'errors' => $validator->errors()->toArray(),
            'file_info' => $this->hasFile('resume_file') ? [
                'original_name' => $this->file('resume_file')->getClientOriginalName(),
                'size' => $this->file('resume_file')->getSize(),
                'mime' => $this->file('resume_file')->getMimeType(),
            ] : null,
        ]);

        parent::failedValidation($validator);
    }
}

<?php

namespace Modules\JobSeeker\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WhatsAppNotifier
{
    /**
     * @var string
     */
    protected $accessToken;
    
    /**
     * @var string
     */
    protected $phoneNumberId;
    
    /**
     * @var string
     */
    protected $recipientPhone;
    
    /**
     * @var string
     */
    protected $apiVersion;
    
    /**
     * WhatsAppNotifier constructor.
     */
    public function __construct()
    {
        // Load values from config or directly from env if config is not loading correctly
        $this->accessToken = env('WHATSAPP_ACCESS_TOKEN') ?: config('jobseeker.whatsapp.access_token');
        $this->phoneNumberId = env('WHATSAPP_PHONE_NUMBER_ID') ?: config('jobseeker.whatsapp.phone_number_id');
        $this->recipientPhone = env('WHATSAPP_RECIPIENT_PHONE') ?: config('jobseeker.whatsapp.recipient_phone');
        $this->apiVersion = env('WHATSAPP_API_VERSION') ?: config('jobseeker.whatsapp.api_version', 'v22.0');
        
        // Ensure phone number is in proper format (strip any non-digit characters except leading +)
        if (substr($this->recipientPhone, 0, 1) === '+') {
            $this->recipientPhone = substr($this->recipientPhone, 1); // Remove leading +
        }
        
        // Debug info
        Log::info("WhatsApp initialized with phone: {$this->recipientPhone}, API version: {$this->apiVersion}");
        
        // Fallback to hardcoded values if any values are missing
        if (empty($this->accessToken) || empty($this->phoneNumberId) || empty($this->recipientPhone)) {
            Log::warning("Missing WhatsApp config values, falling back to hardcoded test values");
            $this->accessToken = 'EAAJ6ZBVzQaEABO7gRiVU762NANgq81d8cavTPUPtlIVZAVj8TDckjd0dM8x1IUeU2M4uQcg5bZAciB4UmeSYGAtdn9WXCxJ8XmDemMfA2rAxr2YwFX0GNttfsMHb4VENLqW6ZADWgXI0OnA18gvl0PIAKNdFWYKF5NZBE4iLr3y0Fp8vYWUyJMmSH5oQcC4yaQ4ZCjYqr23sePiAPoooGLbQvt7VdZASb7Mhd5x';
            $this->phoneNumberId = '682752831577542';
            $this->recipientPhone = '+601111709378';
            $this->apiVersion = 'v22.0';
        }
    }
    
    /**
     * Send a text message via WhatsApp Cloud API
     *
     * @param string $message
     * @return bool
     */
    public function sendMessage($message)
    {
        // Add console output for debugging
        echo "📱 Sending WhatsApp message...\n";
        
        if (empty($this->accessToken) || empty($this->phoneNumberId) || empty($this->recipientPhone)) {
            Log::error("WhatsApp notification not sent: Missing required configuration");
            Log::error("Access Token: " . (empty($this->accessToken) ? "MISSING" : "Present"));
            Log::error("Phone Number ID: " . (empty($this->phoneNumberId) ? "MISSING" : $this->phoneNumberId));
            Log::error("Recipient Phone: " . (empty($this->recipientPhone) ? "MISSING" : $this->recipientPhone));
            
            // Add console output for debugging
            echo "❌ Error: Missing WhatsApp configuration\n";
            return false;
        }
        
        try {
            $endpoint = "https://graph.facebook.com/{$this->apiVersion}/{$this->phoneNumberId}/messages";
            Log::info("Sending WhatsApp message to: {$this->recipientPhone}");
            Log::info("Using endpoint: {$endpoint}");
            
            // Add console output for debugging
            echo "→ Sending to: {$this->recipientPhone}\n";
            echo "→ API endpoint: {$endpoint}\n";
            
            $payload = [
                'messaging_product' => 'whatsapp',
                'recipient_type' => 'individual',
                'to' => $this->recipientPhone,
                'type' => 'text',
                'text' => [
                    'body' => $message,
                    'preview_url' => true
                ]
            ];
            
            Log::info("WhatsApp request payload: " . json_encode($payload, JSON_PRETTY_PRINT));
            
            $response = Http::withToken($this->accessToken)
                ->post($endpoint, $payload);
            
            if ($response->successful()) {
                Log::info("WhatsApp notification sent successfully");
                Log::info("Response: " . $response->body());
                
                // Add console output for debugging
                echo "✅ WhatsApp message sent successfully!\n";
                return true;
            } else {
                Log::error("Failed to send WhatsApp notification. Status: " . $response->status());
                Log::error("Response body: " . $response->body());
                
                // Add console output for debugging
                echo "❌ Error sending WhatsApp message: " . $response->status() . "\n";
                echo "Response: " . $response->body() . "\n";
                return false;
            }
        } catch (\Exception $e) {
            Log::error("Exception when sending WhatsApp notification: " . $e->getMessage());
            Log::error("Stack trace: " . $e->getTraceAsString());
            
            // Add console output for debugging
            echo "❌ Exception sending WhatsApp message: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * Send a notification for a new job
     *
     * @param array $jobData
     * @return bool
     */
    public function notifyNewJob($jobData)
    {
        $message = "🆕 NEW JOB ALERT\n\n";
        $message .= $this->formatJobMessage($jobData);
        return $this->sendMessage($message);
    }
    
    /**
     * Send a notification for an updated job
     *
     * @param array $jobData
     * @return bool
     */
    public function notifyUpdatedJob($jobData)
    {
        $message = "🔄 JOB UPDATED\n\n";
        $message .= $this->formatJobMessage($jobData);
        return $this->sendMessage($message);
    }
    
    /**
     * Format job data into a message
     *
     * @param array $jobData
     * @return string
     */
    protected function formatJobMessage($jobData)
    {
        $message = "{$jobData['position']}\n";
        $message .= "📍 Location: {$jobData['locations']}\n";
        $message .= "🏢 Company: {$jobData['company_name']}\n";
        $message .= "📅 Expires: {$jobData['expire_date']}\n";
        $message .= "💼 Type: {$jobData['work_type']} - {$jobData['contract_type']}\n";
        $message .= "👥 Vacancy: {$jobData['number_of_vacancy']}\n";
        
        if (!empty($jobData['vacancy_number'])) {
            $message .= "🔢 Vacancy #: {$jobData['vacancy_number']}\n";
        }
        
        $message .= "💰 Salary: {$jobData['salary']}\n";
        
        // Add apply online info if applicable
        if (!empty($jobData['can_apply_online']) && $jobData['can_apply_online']) {
            $message .= "✅ Can apply online\n";
        }
        
        return $message;
    }
} 
# Responsive FileManager for TinyMCE and CKEditor

Responsive FileManager is a free open-source file manager made with the jQuery library, CSS3, PHP and HTML5 that offers a nice and elegant way to upload and insert files, images and videos. You can use it as external plugin for TinyMCE version 4.x. or CKEditor and you can also use it as a stand-alone file manager to manage and select files. The script automatically creates thumbnails of images for the preview list and can create also external thumbnails to use in your cms or site. It can be configured for automatic resizing of uploaded images or to automatically limit the size. You can personalize the configuration for each folder. You can set a subfolder as the root and change the configuration for each user, page or FileManager call. Is compatible with multi-user mode and you can edit images with aviary editor, sorting files.

DEMO AND DOCUMENTATION: http://www.responsivefilemanager.com/

PRODUCTION VERSION DOWNLOAD: https://github.com/trippo/ResponsiveFilemanager/releases

Released under Creative Commons Attribution-NonCommercial 3.0 Unported License.

Creator : <EMAIL> - tr1pp0

### Localization

- AZE [<PERSON><PERSON><PERSON>] 
- B<PERSON> [<PERSON><PERSON>]
- BR<PERSON> [pauloman<PERSON><PERSON>]
- CAT [<PERSON><PERSON>]
- CHN [Vu <PERSON>]
- CRO 
- CZE [jlusticky]
- DAN [Morten Hesselberg Grove]
- ENG
- ESP [Roberto Santamaria] 
- FRA [Mathieu Ducharme]
- GER [Oliver Beta]
- GRC [vkouvelis]
- Hebrew [sagie212]
- HUN [Novak Szabolcs]
- IND [urayogi]
- ITA
- JPN [Vu Doan Thang]
- LTU [Tomas Norkūnas]
- MON [Tumenzul Batjargal]
- NLD [Martijn van der Made]
- NOR [Pål Schroeder]
- Persian [web2web esf ir]
- POL [Michell Hoduń]
- POR [Sérgio Lima]
- RUS [vasromand] 
- SLO [Roman Šovčík]
- SVN [Peter Benko]
- SWE [Jon Sten]
- TUR [Ahmed Faruk Bora]
- UKR [Sergey]

### Development

In order to work on assets, you need to install [Node.js](http://nodejs.org), [Bower](http://bower.io) and [gulp](http://gulpjs.com), then cd to your folder and run these commands:

1. Install bower packages according to bower.json (directory app/assets/components)

   ```
   bower install
   ```
2. Install gulp packages according to gulpfile.js (directory node_modules)

   ```
   npm install
   ```
3. Compile assets

   ```
   gulp
   ```

Gulp use [laravel-elixir](http://laravel.com/docs/5.0/elixir)
### Credits

- [Bootstrap](http://twitter.github.io/bootstrap)
- [Bootstrap Lightbox](http://jbutz.github.io/bootstrap-lightbox)
- [Dropzonejs](http://www.dropzonejs.com)
- [Fancybox](http://fancybox.net)
- [TouchSwipe](http://labs.rampinteractive.co.uk/touchSwipe/demos)
- [PHP Image Magician](http://phpimagemagician.jarrodoberto.com)
- [Mini icons](http://www.fatcow.com/free-icons)
- [Jupload](http://jupload.sourceforge.net)
- [Bootbox](http://bootboxjs.com)
- [jQuery contextMenu](http://medialize.github.io/jQuery-contextMenu)
- [Bootstrap-modal](https://github.com/jschr/bootstrap-modal)
- [jPlayer](http://jplayer.org)
- [ViewerJS](http://viewerjs.org)
- [Lazy Load Plugin for jQuery](http://www.appelsiini.net/projects/lazyload)

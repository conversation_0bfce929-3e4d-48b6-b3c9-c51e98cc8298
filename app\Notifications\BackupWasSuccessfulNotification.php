<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Services\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Spatie\Backup\Events\BackupWasSuccessful;

class BackupWasSuccessfulNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected BackupWasSuccessful $event;

    public function __construct(BackupWasSuccessful $event)
    {
        $this->event = $event;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $correlationId = Str::uuid()->toString();
        
        // Send via EmailService asynchronously
        $this->sendViaEmailService($notifiable, $correlationId);
        
        // Return a basic MailMessage for compatibility (won't be used due to EmailService)
        return (new MailMessage)
            ->subject('Backup Successful')
            ->line('The backup process completed successfully.')
            ->line('Backup destination: ' . $this->event->backupDestination->backupName());
    }

    protected function sendViaEmailService($notifiable, string $correlationId): void
    {
        try {
            $emailService = app(EmailService::class);
            
            $subject = '✅ Database Backup Completed Successfully - ' . config('app.name');
            $recipient = $notifiable->routeNotificationFor('mail');
            
            $htmlContent = $this->generateHtmlContent();
            
            $emailService->sendEmail(
                to: $recipient,
                subject: $subject,
                htmlBody: $htmlContent,
                textBody: strip_tags($htmlContent),
                correlationId: $correlationId,
                tags: ['backup', 'success', 'routine'],
                isAsync: true
            );
            
            Log::info('Backup success notification sent via EmailService', [
                'correlation_id' => $correlationId,
                'recipient' => $recipient,
                'backup_destination' => $this->event->backupDestination->backupName()
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send backup success notification via EmailService', [
                'correlation_id' => $correlationId,
                'error' => $e->getMessage(),
                'backup_destination' => $this->event->backupDestination->backupName()
            ]);
        }
    }

    protected function generateHtmlContent(): string
    {
        $backupDestination = $this->event->backupDestination;
        $timestamp = now()->format('Y-m-d H:i:s T');
        $backupInfo = $this->getBackupInfo();
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='utf-8'>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f4f4f4; }
                .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .header { background-color: #28a745; color: white; padding: 20px; border-radius: 8px 8px 0 0; margin: -30px -30px 30px -30px; }
                .header h1 { margin: 0; font-size: 24px; }
                .success-alert { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
                .info-section { margin-bottom: 25px; }
                .info-section h3 { color: #333; border-bottom: 2px solid #28a745; padding-bottom: 8px; }
                .backup-details { background-color: #e9ecef; padding: 15px; border-radius: 4px; }
                .backup-details table { width: 100%; border-collapse: collapse; }
                .backup-details td { padding: 8px; border-bottom: 1px solid #dee2e6; }
                .backup-details td:first-child { font-weight: bold; width: 30%; }
                .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
                .stat-card { background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 4px; text-align: center; }
                .stat-card .number { font-size: 24px; font-weight: bold; color: #28a745; }
                .stat-card .label { font-size: 12px; color: #666; text-transform: uppercase; }
                .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #666; font-size: 12px; }
                .icon { font-size: 20px; margin-right: 8px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>✅ Database Backup Completed</h1>
                    <p style='margin: 10px 0 0 0;'>Automated backup successful for " . config('app.name') . "</p>
                </div>
                
                <div class='success-alert'>
                    <strong>🎉 Success!</strong> Your database backup has been completed successfully and is ready for use.
                </div>
                
                <div class='stats-grid'>
                    <div class='stat-card'>
                        <div class='number'>{$backupInfo['file_size']}</div>
                        <div class='label'>Backup Size</div>
                    </div>
                    <div class='stat-card'>
                        <div class='number'>{$backupInfo['duration']}</div>
                        <div class='label'>Duration</div>
                    </div>
                    <div class='stat-card'>
                        <div class='number'>{$backupInfo['file_count']}</div>
                        <div class='label'>Files Created</div>
                    </div>
                </div>
                
                <div class='info-section'>
                    <h3>📋 Backup Details</h3>
                    <div class='backup-details'>
                        <table>
                            <tr><td><span class='icon'>📅</span>Backup Date</td><td>{$timestamp}</td></tr>
                            <tr><td><span class='icon'>🗄️</span>Database</td><td>" . config('database.connections.mysql.database') . "</td></tr>
                            <tr><td><span class='icon'>📁</span>Backup Destination</td><td>" . $backupDestination->backupName() . "</td></tr>
                            <tr><td><span class='icon'>🏢</span>Application</td><td>" . config('app.name') . "</td></tr>
                            <tr><td><span class='icon'>⚙️</span>Environment</td><td>" . config('app.env') . "</td></tr>
                            <tr><td><span class='icon'>🖥️</span>Server</td><td>" . gethostname() . "</td></tr>
                        </table>
                    </div>
                </div>
                
                <div class='info-section'>
                    <h3>📊 System Status</h3>
                    <div class='backup-details'>
                        <table>
                            <tr><td>Backup Storage</td><td>{$backupInfo['storage_info']}</td></tr>
                            <tr><td>Next Scheduled Backup</td><td>{$backupInfo['next_backup']}</td></tr>
                            <tr><td>Retention Policy</td><td>Keep daily backups for 7 days</td></tr>
                            <tr><td>Backup Status</td><td><span style='color: #28a745; font-weight: bold;'>✅ Healthy</span></td></tr>
                        </table>
                    </div>
                </div>
                
                <div class='info-section'>
                    <h3>🔧 Backup Management</h3>
                    <p><strong>Manual Backup:</strong> <code>php artisan backup:run --only-db</code></p>
                    <p><strong>View Backup Status:</strong> <code>php artisan backup:list</code></p>
                    <p><strong>Cleanup Old Backups:</strong> <code>php artisan backup:clean</code></p>
                </div>
                
                <div class='footer'>
                    <p>This notification was sent automatically by the backup monitoring system.</p>
                    <p>Server: " . gethostname() . " | Time: {$timestamp} | Correlation ID: " . Str::uuid() . "</p>
                </div>
            </div>
        </body>
        </html>";
    }

    protected function getBackupInfo(): array
    {
        $backupDestination = $this->event->backupDestination;
        
        // Get the newest backup file information
        $backupInfo = [
            'file_size' => 'Unknown',
            'duration' => 'Unknown',
            'file_count' => 1,
            'storage_info' => 'Unknown',
            'next_backup' => 'Tomorrow at 00:10 MYT'
        ];
        
        try {
            // Try to get the latest backup file size
            $backupDisk = $backupDestination->disk();
            $newestBackup = $backupDestination->newestBackup();
            
            if ($newestBackup) {
                $fileSize = $backupDisk->size($newestBackup->path());
                $backupInfo['file_size'] = $this->formatBytes($fileSize);
                
                // Calculate duration (rough estimate based on file creation)
                $createdAt = $backupDisk->lastModified($newestBackup->path());
                $duration = now()->timestamp - $createdAt;
                $backupInfo['duration'] = $this->formatDuration($duration);
            }
            
            // Get storage information
            $backupPath = storage_path('dbBackups');
            if (is_dir($backupPath)) {
                $freeSpace = disk_free_space($backupPath);
                $totalSpace = disk_total_space($backupPath);
                
                if ($freeSpace && $totalSpace) {
                    $usedPercent = round((($totalSpace - $freeSpace) / $totalSpace) * 100, 1);
                    $backupInfo['storage_info'] = $this->formatBytes($freeSpace) . ' free (' . $usedPercent . '% used)';
                }
            }
            
        } catch (\Exception $e) {
            Log::warning('Could not retrieve detailed backup information', [
                'error' => $e->getMessage()
            ]);
        }
        
        return $backupInfo;
    }

    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    protected function formatDuration(int $seconds): string
    {
        if ($seconds < 60) {
            return $seconds . 's';
        } elseif ($seconds < 3600) {
            return round($seconds / 60, 1) . 'm';
        } else {
            return round($seconds / 3600, 1) . 'h';
        }
    }
} 
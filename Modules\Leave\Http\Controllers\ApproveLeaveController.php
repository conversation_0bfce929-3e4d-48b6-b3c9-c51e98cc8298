<?php

namespace Modules\Leave\Http\Controllers;

use App\Notifications\LeaveApprovedNotification;
use App\Repositories\UserRepository;
use App\Role;
use App\Employee;
use App\Guardian;
use App\AssignClassTeacher;
use App\ClassTeacher;
use App\LeaveRequest;
use App\LeaveType;
use App\Scopes\OrganizationScope;
use App\User;
use App\YearCheck;
use App\ApiBaseMethod;
use App\LeaveDefine;
use App\Notification;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Routing\Controller;
use Modules\Leave\Repositories\LeaveRepository;
use Modules\Leave\Repositories\LeaveTypeRepository;
use Modules\Setup\Repositories\DepartmentRepository;

class ApproveLeaveController extends Controller
{
    private $leaveRepository, $userRepository, $deptRepo,$leaveTypeRepository;

    public function __construct(LeaveRepository $leaveRepository, UserRepository $userRepository,DepartmentRepository $deptRepo,LeaveTypeRepository $leaveTypeRepository)
    {
        $this->leaveRepository = $leaveRepository;
        $this->userRepository = $userRepository;
        $this->deptRepo = $deptRepo;
        $this->leaveTypeRepository = $leaveTypeRepository;
    }



    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        try {


            $applied_leaves = $this->leaveRepository->approved_all();

//            $user = Auth::user();
//
//            if (checkAdmin()) {
//                $applied_leaves = LeaveRequest::where( 'approve_status','P')->get();
//            } else {
//                $applied_leaves = LeaveRequest::where([['approve_status', '!=', 'P'], ['employee_id', '=', $user->id]])->get();
//            }
//            $leave_types = LeaveType::all();
//            $roles = Role::where('id', '!=', 1)->where('id', '!=', 2)->where('id', '!=', 3)->get();

            return view('leave::approveLeaveRequest', compact('applied_leaves', 'leave_types', 'roles'));

        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function pendingLeave(Request $request)
    {
        try {
            $employee = Employee::find(Auth::user()->id)->first();


            if (checkAdmin()) {
                $applied_leaves = LeaveRequest::where([['approve_status', '!=', 'A']])
                    ->get();
            } elseif ($employee->hasRole('teacher_2_')) {
                $class_teacher = ClassTeacher::where('employee_id', $employee->id)
                    ->first();


                if ($class_teacher) {
                    $leaves = LeaveRequest::where([
                        ['active_status', 1],
                        ['approve_status', '!=', 'A'],
                        ['role_id', '=', 2]
                    ])
                        ->first();
                    $smAssignClassTeacher = AssignClassTeacher::find($class_teacher->assign_class_teacher_id);
                    if ($leaves) {
                        $apply_leaves = LeaveRequest::with(array('student' => function ($query) use ($smAssignClassTeacher) {
                            $query->where('class_id', $smAssignClassTeacher->class_id)->where('section_id', $smAssignClassTeacher->section_id);
                        }))->where([
                            ['active_status', 1],
                            ['approve_status', '!=', 'A'],
                            ['role_id', '=', 2]
                        ])
                            ->get();
                    }
                } else {
                    $applied_leaves = LeaveRequest::where([
                        ['active_status', 1],
                        ['approve_status', '!=', 'A'],
                        ['employee_id', '=', $employee->id],
                        ['role_id', '!=', 2]
                    ])
                        ->get();
                }
            }
            
            $leave_types = LeaveType::all();
            $roles = Role::where('name', '!=', 'administrative_2_ /** super admin */')->where('name', '!=', 'parent')->get();


            $pendingRequest = LeaveRequest::select('leave_requests.id', 'full_name', 'apply_date', 'leave_from', 'leave_to', 'reason', 'file', 'leave_types.type', 'approve_status')
                ->withoutGlobalScope(OrganizationScope::class)
                ->join('leave_defines', 'leave_requests.leave_define_id', '=', 'leave_defines.id')
                ->join('employees', 'leave_requests.employee_id', '=', 'employees.id')
                ->leftjoin('leave_types', 'leave_requests.type_id', '=', 'leave_types.id')
                ->where('leave_requests.approve_status', '=', 'P')
                ->where('leave_requests.organization_id', '=', Auth::user()->organization_id)
                ->get();



            return view('leave::approveLeaveRequest', compact('applied_leaves', 'leave_types', 'roles'));

        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function approvedLeave(Request $request)
    {




        try {
            $approved_request = LeaveRequest::where('leave_requests.active_status', 1)
                ->select('leave_requests.id', 'full_name', 'apply_date', 'leave_from', 'leave_to', 'reason', 'file', 'type', 'approve_status')
                ->join('leave_defines', 'leave_requests.leave_define_id', '=', 'leave_defines.id')
                ->join('employees', 'leave_requests.employee_id', '=', 'employees.id')
                ->join('leave_types', 'leave_requests.type_id', '=', 'leave_types.id')
                ->where('leave_requests.approve_status', '=', 'A')
                ->where('leave_requests.organization_id', '=', Auth::user()->organization_id)
                ->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['approved_request'] = $approved_request->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function rejectLeave(Request $request)
    {
        try {
            $reject_request = LeaveRequest::where('leave_requests.active_status', 1)
                ->select('leave_requests.id', 'full_name', 'apply_date', 'leave_from', 'leave_to', 'reason', 'file', 'type', 'approve_status')
                ->join('leave_defines', 'leave_requests.leave_define_id', '=', 'leave_defines.id')
                ->join('employees', 'leave_requests.employee_id', '=', 'employees.id')
                ->join('leave_types', 'leave_requests.type_id', '=', 'leave_types.id')
                ->where('leave_requests.approve_status', '=', 'R')
                ->where('leave_requests.organization_id', '=', Auth::user()->organization_id)
                ->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['reject_request'] = $reject_request->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function applyLeave(Request $request)
    {
        $input = $request->all();
        // return $request->input();
        if (ApiBaseMethod::checkUrl($request->fullUrl())) {
            $validator = Validator::make($input, [
                'apply_date' => "required",
                'leave_type' => "required",
                'leave_from' => 'required|before_or_equal:leave_to',
                'leave_to' => "required",
                'employee_id' => "required",
                'reason' => "required",

            ]);
        }

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
        }

        try {
            //return $request->input('apply_date');
            $fileName = "";
            if ($request->file('attach_file') != "") {
                $file = $request->file('attach_file');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/leave_request/', $fileName);
                $fileName = 'public/uploads/leave_request/' . $fileName;
            }

            $apply_leave = new LeaveRequest();
            $apply_leave->employee_id = $request->input('employee_id');
            $apply_leave->role_id = 4;
            $apply_leave->apply_date = date('Y-m-d');
            $apply_leave->leave_define_id = $request->input('leave_type');
            $apply_leave->type_id = $request->input('leave_type');
            $apply_leave->leave_from = $request->input('leave_from');
            $apply_leave->leave_to = $request->input('leave_to');
            $apply_leave->approve_status = 'P';
            $apply_leave->reason = $request->input('reason');
            $apply_leave->organization_id = Auth::user()->organization_id;
            //return $request->teacher_id;
            if ($fileName != "") {
                $apply_leave->file = $fileName;
            }

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {

                $result = $apply_leave->save();

                return ApiBaseMethod::sendResponse($result, null);
            }
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function updateLeave(Request $request)
    {

        try {
            //$leave_request = DB::table('leave_requests')->where('id', $id)->first();
            $leave_request_data = LeaveRequest::find($request->id);
            $employee_id = $leave_request_data->employee_id;
            $role_id = $leave_request_data->role_id;
            $leave_request_data->approve_status = $request->status;
            $result = $leave_request_data->save();


            $notification = new Notification;
            $notification->user_id = $employee_id;
            $notification->role_id = $role_id;
            $notification->date = date('Y-m-d');
            $notification->message = 'Leave status updated';
            $notification->organization_id = Auth::user()->organization_id;
            $notification->save();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = '';
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $input = $request->all();
        if (ApiBaseMethod::checkUrl($request->fullUrl())) {
            $validator = Validator::make($input, [
                'apply_date' => "required",
                'leave_type' => "required",
                'leave_from' => "required",
                'leave_to' => "required",
                'reason' => "required",
                'login_id' => "required",
                'role_id' => "required"
            ]);
        } else {
            $validator = Validator::make($input, [
                'employee_id' => "required",
                'apply_date' => "required",
                'leave_type' => "required",
                'leave_from' => "required",
                'leave_to' => "required",
                'reason' => "required"
            ]);
        }
        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $fileName = "";
            if ($request->file('attach_file') != "") {
                $file = $request->file('attach_file');
                $fileName = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('public/uploads/leave_request/', $fileName);
                $fileName = 'public/uploads/leave_request/' . $fileName;
            }

            $user = Auth()->user();

            if ($user) {
                $login_id = $user->id;
                $role_id = $user->role_id;
            } else {
                $login_id = $request->login_id;
                $role_id = $request->role_id;
            }
            $leave_request_data = new LeaveRequest();
            $leave_request_data->employee_id = $login_id;
            $leave_request_data->role_id = $role_id;
            $leave_request_data->apply_date = date('Y-m-d', strtotime($request->apply_date));
            $leave_request_data->type_id = $request->leave_type;
            $leave_request_data->leave_from = date('Y-m-d', strtotime($request->leave_from));
            $leave_request_data->leave_to = date('Y-m-d', strtotime($request->leave_to));
            $leave_request_data->approve_status = $request->approve_status;
            $leave_request_data->reason = $request->reason;
            $leave_request_data->file = $fileName;
            $leave_request_data->organization_id = Auth::user()->organization_id;
            $result = $leave_request_data->save();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($result) {
                    return ApiBaseMethod::sendResponse(null, 'Leave Request has been created successfully.');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
                }
            } else {
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function edit(Request $request, $id)
    {


        try {
            $editData = LeaveRequest::find($id);
            $employeesByRole = Employee::where('role_id', '=', Role::where('id', $editData->role_id)->first()->name)->get();
            $roles = Role::whereOr(['organization_id', Auth::user()->organization_id], ['organization_id', 1])->get();
            $apply_leaves = LeaveRequest::all();
            $leave_types = LeaveType::all();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['editData'] = $editData->toArray();
                $data['employeesByRole'] = $employeesByRole->toArray();
                $data['apply_leaves'] = $apply_leaves->toArray();
                $data['leave_types'] = $leave_types->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }

            return view('backEnd.humanResource.approveLeaveRequest', compact('editData', 'employeesByRole', 'apply_leaves', 'leave_types', 'roles'));
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function destroy($id)
    {
        $leaveRequest = LeaveRequest::destroy($id);

//        $leaveRequest->delete();
        \Session::flash('flash_message', 'Leave Request deleted!');
        return redirect()->back();
    }


    public function staffNameByRole(Request $request)
    {

        try {




            if ($request->id != 25 /** parent */) {
                $allStaffs = Employee::whereHas('roles', function ($q) use ($request) {
                    return $q->where('id', $request->id);
                })->get();
                $employees = [];
                foreach ($allStaffs as $employeesvalue) {
                    $employees[] = Employee::find($employeesvalue->id);
                }
            } else {
                $employees = Parent::all();
            }

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendResponse($employees, null);
            }

            return response()->json([$employees]);
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function updateApproveLeave(Request $request)
    {
        DB::beginTransaction();

        try {
            if (checkAdmin()) {
                $leave_request_data = LeaveRequest::find($request->id);
            }else{
                $leave_request_data = LeaveRequest::where('id',$request->id)->first();
            }
            $employee_id = $leave_request_data->employee_id;
            $role_id = $leave_request_data->role_id;
            $leave_request_data->approve_status = $request->approve_status;
            $result = $leave_request_data->save();


            $notification = new Notification;
            // $notification->user_id = $leave_request_data->id;
            $notification->user_id = $leave_request_data->employee_id;
            $notification->role_id = $role_id;
            $notification->date = date('Y-m-d');
            $notification->message = 'Leave status updated';
            $notification->organization_id = Auth::user()->organization_id;
            $notification->save();
            DB::commit();

//            $user=User::find($notification->user_id);
            $user=Employee::find($notification->user_id);
            \Illuminate\Support\Facades\Notification::send($user, new LeaveApprovedNotification($notification));

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($result) {
                    return ApiBaseMethod::sendResponse(null, 'Leave Request has been updates successfully.');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
                }
            } else {
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->route('leave.approve-leave');
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            DB::rollback();

            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function viewLeaveDetails(Request $request, $id)
    {
        try {


            $leaveDetails = LeaveRequest::find($id);


            $employee_leaves = LeaveDefine::where('role_id', $leaveDetails->role_id)->where('user_id',Auth::user()->id)->with('leaveType')->get();


            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['leaveDetails'] = $leaveDetails->toArray();
                $data['staff_leaves'] = $employee_leaves->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('leave::viewLeaveDetails', compact('leaveDetails', 'employee_leaves'));

        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}
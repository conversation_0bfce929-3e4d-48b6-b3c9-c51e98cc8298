<?php

namespace Modules\Admission\Http\Controllers;

use App\BaseSetup;
use App\Center;
use App\Classes;
use App\EmailSetting;
use App\Employee;
use App\GeneralSettings;
use App\Guardian;
use App\Organization;
use App\Program;
use App\ProgramTranslation;
use App\Role;
use App\Section;
use App\UserLog;
use App\User;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Dompdf\Dompdf;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Admission;
use App\AdmissionInterview;
use App\AdmissionInterviewer;
use App\Student;
use App\StudentHefzPlan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;
use Modules\ApplicationCenter\Entities\RegistrationSetting;


class AdmissionApplicationController extends Controller
{


    public function __construct()
    {
//        $this->middleware('web');
        $this->middleware('auth');
//        $this->middleware('PM');


    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return view('admission::index');
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view('admission::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        return $request;
    }


    /**
     * Show the specified resource.
     * @return Response
     */
    public function show()
    {
        return view('admission::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit()
    {
        return view('admission::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @return Response
     */
    public function update(Request $request)
    {
        $admission = Admission::findOrFail($request->admission_id);

        $student = $admission->student;
        //   return $request->all();
        $this->validate($request, [
            'center_id' => 'required',
            'class_id' => 'required',
            'admission_status' => 'required',
            'program_id' => 'required'
        ]);

        $admission->center_id = $request->center_id;
        $admission->class_id = $request->class_id;
        $admission->status = $request->admission_status;
        $student->status = $request->admission_status;
        $admission->save();
        $student->save();
        $admission->programs()->sync([$request->program_id]);


        // if status is offered, then send a copy of the offer letter to the guardian and the student in PDF format

        try {
            if ($request->admission_status == 'offered') {
                $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);

                $systemEmail = EmailSetting::find(1);

                $system_email = $systemEmail->from_email;
                $organization_name = $systemSetting->organization_name;

                $sender['system_email'] = $system_email;
                $sender['organization_name'] = $organization_name;

                if ($admission->creator_role == 'parent') {
                    $user_info[0]["guardian_email"] = $admission->guardian_email;
                }
                $user_info[0]["student_id"] = $admission->student_id;
                $user_info[0]["student_email"] = $admission->student_email;
                $user_info[0]["channel"] = "email";
                $user_info[0]["studentName"] = Student::find($admission->student_id)->full_name;
                $user_info[0]["programTitle"] = Program::find($admission->program_id)->title;


                // offer letter arrangement starts here
                $offerLetterRawContent = $admission->programs()->pluck('offer')[0];
                $offerLetterRawContent = str_replace("[date]", Carbon::now()->toDate()->format("Y-m-d"), $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[address]", 'Dummy Address input by Hashmat', $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[studentName]", Student::find($admission->student_id)->full_name, $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[programName]", "<i>" . Program::find($request->program_id)->title . "</i>", $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[registrationFees]", '<strong>100</strong>', $offerLetterRawContent);
                $offerLetterRawContent = str_replace("[monthlyFeesAmount]", '<strong>200</strong>', $offerLetterRawContent);


                $pdf = app()->make('dompdf.wrapper');
//                $dompdf=new Dompdf();
                $pdf->setOptions('isHtml5ParserEnabled', true);
                $pdf->setOptions('isRemoteEnabled', true);
                $pdf->loadHTML(htmlspecialchars_decode($offerLetterRawContent));
                $pdf->render();
                $content = $pdf->download()->getOriginalContent();
                //save the pdf offer letter in storage folder
                \Storage::put("offerLetters/" . $admission->student_id . "-offerLetter.pdf", $content);


//            we are going to attach the offer letter in the Job by making using of the student_is as it is used as the filename in the storage/app/offerLetters directory
                dispatch(new \App\Jobs\SendOfferLetterMailJob($user_info, $sender));
                Toastr::success('Email successfully sent', 'Warning');

            }
        } catch (\Exception $e) {
            dd($e->getMessage());

            Toastr::warning($e->getMessage(), 'Warning');
            return redirect()->back();
        }


        return redirect()->back();
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @return Response
     */
    public function approve(Request $request)
    {
        $admission = Admission::findOrFail($request->admission_id);
        $student = $admission->student;


        // return $admission->programs;

        $waiting_for_interview = false;
        $approved_programs = 0;

        if (!$admission) {
        }


        // get admission programs

        foreach ($admission->programs as $program) {
            // check if the admission aproved
            if ($request->interview[$program->id]['approve']) {
                $approved_programs++;

                // check if an interview is required
                if ($program->require_interview) {
                    // validate request

                    $fields_names = ["interview_time" => "Interview Time"];

                    $this->validate($request, [
                        "interview.*.interview_time" => 'required|date_format:Y-m-d H:i',
                        "interview.*.location" => 'required'
                    ], $fields_names);

                    $waiting_for_interview = true;

                    // set interview details

                    // making the token for the student interview invitation
                    do {
                        //generate a random string using Laravel's str_random helper
                        $token = \Illuminate\Support\Str::random();
                    } //check if the token already exists and if it does, try again
                    while (AdmissionInterview::where('interview_invitation_token', $token)->first());

                    //create a new invite record

                    $interview_details = $request->interview[$program->id];

                    $interview_details['admission_id'] = $request->admission_id;
                    $interview_details['interview_invitation_token'] = $token;
                    $interview_details['program_id'] = $program->id;

                    $interview_details['status'] = 'waiting_for_interview';
                    $user_info[] = array('interviewLocation' => $interview_details['location'], 'interview_duration' => '1 hour',
                        'interviewDateTime' => $interview_details['interview_time'], 'email' => $admission->student_email,
                        'id' => $student->id, 'slug' => 'student', 'interviewCommitteeIds' => $interview_details['committee'],
                        'interviewDetails' => $interview_details['notes'], 'programTitle' => $program->programTranslations->first()->title);
                    try {
                        $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);

                        $systemEmail = EmailSetting::find(1);

                        $system_email = $systemEmail->from_email;
                        $organization_name = $systemSetting->organization_name;

                        $sender['system_email'] = $system_email;
                        $sender['organization_name'] = $organization_name;

                        if ($admission->creator_role == 'parent') {
                            $user_info[0]["guardian_email"] = $admission->guardian_email;
                        }

                        $user_info[0]["token"] = $token;
                        $user_info[0]["channel"] = "email";
                        $user_info[0]["phone"] = GeneralSettings::where("organization_id", config('organization_id'))->first()->phone;
                        $user_info[0]["studentName"] = Student::find($admission->student_id)->full_name;
                        $user_info[0]["subjectInterviewParticipants"] = implode(", ", Employee::whereIn("id", $user_info[0]['interviewCommitteeIds'])->pluck("name")->toArray()) . " & " . $student->display_name;
                        $user_info[0]["InterviewerEmails"] = Employee::whereIn("id", $user_info[0]['interviewCommitteeIds'])->pluck("email")->toArray();

                        dispatch(new \App\Jobs\SendStudentInterviewMailJob($user_info, $sender));

                        $interview = AdmissionInterview::create($interview_details);
                    } catch (\Exception $e) {

                        dd($e->getMessage());
                        Toastr::success($e->getMessage(), 'Warning');
                        return redirect('student-list');
                    }

                    // set interview committee

                    $interview_committee = $interview_details['committee'];

                    foreach ($interview_committee as $interviewer) {
                        AdmissionInterviewer::create([
                            'admission_interview_id' => $interview->id,
                            'employee_id' => $interviewer
                        ]);
                    }
                }
            }
        }


        if ($waiting_for_interview) {
            $admission->status = 'waiting_for_interview';
            $student->status = 'waiting_for_interview';
        } else if ($approved_programs == $admission->programs->count()) {
            $admission->status = 'offer';
            $student->status = 'offer';
        } else if ($approved_programs > 0) {
            $admission->status = 'conditional_offer';
        } else {
            $admission->status = 'rejected';
            $student->status = 'rejected';
        }

        $admission->save();
        $student->save();


        // change admission status & student status
        if ($request->ajax()) {
            return response()->json([
                "status" => "success"
            ]);
        }


        return redirect()->back();
    }

    public function setOrientation()
    {
        $request = request();

        $admission = Admission::findOrFail($request->admission_id);

        $this->validate($request, [
            'admission_id' => 'required|numeric',
            'employee_id' => 'required|numeric',
            'orientation_time' => 'required|date',
            'location' => 'required'
        ]);

        $requestData = $request->all();

        $requestData['organization_id'] = config('organization_id');
        $requestData['created_by'] = auth()->user()->id;

        \App\AdmissionOrientation::create($requestData);
        $admission->status = "waiting_for_orientation";

        $admission->save();

        flash('Orintation Session Has Been Set.');

        return redirect()->back();

    }

    public function finalize(Request $request)
    {
        $admission = Admission::findOrFail($request->admission_id);

        $admission->status = 'active';
        $admission->start_date = date('Y-m-d');

        $admission->save();

        $student = Student::findOrFail($admission->student_id);
        $student->status = 'active';
        $student->save();

        // Check if the program is Nuraniyah
        $program = Program::find($admission->program_id);
        if (\Illuminate\Support\Str::contains(strtolower($program->title), ['nuraniyah', 'nouranya'])) {
            // Fetch the ProgramLevel where the translated title contains 'level 1' or 'level1'
            $levelId = \App\ProgramLevel::whereTranslationLike('title', 'LIKE', '%level 1%')
                    ->orWhereTranslationLike('title', 'LIKE', '%level1%')->first()->id;
            $studentProgramLevel = $student->studentProgramLevels()->updateOrCreate(
                ['student_id' => $student->id, 'class_id' => $admission->class_id],
                ['level_id' => $levelId, 'status' => 'active']
            );
        }


        if (\Illuminate\Support\Str::contains(strtolower($program->title), ['Ijazah and Sanad', 'ijazah and sanad'])) {
            // Fetch the ProgramLevel where the translated title contains 'level 1' or 'level1'
            $levelId = \App\ProgramLevel::whereTranslationLike('title', 'LIKE', '%Level 1: Preparation Course%')->first()->id;
            $studentProgramLevel = $student->studentProgramLevels()->updateOrCreate(
                ['student_id' => $student->id, 'class_id' => $admission->class_id],
                ['level_id' => $levelId, 'status' => 'active']
            );
        }




        if (!\App\ClassStudent::where('class_id', $admission->class_id)->where('student_id', $admission->student_id)->first()) {

            $join_class = new \App\ClassStudent();

            $join_class->student_id = $admission->student_id;
            $join_class->class_id = $admission->class_id;
            $join_class->start_date = date('Y-m-d');

            $join_class->save();
            flash('Student was added to  class successfully.');
        } else {
            flash('Student already has joined the class.');
        }


        return redirect()->back();

    }

    public function createHefzPlan(Request $request, $redirect = false)
    {
        $this->validate($request, [
            "hefz.study_direction" => "required",
            "hefz.start_from_surat" => "required",
            "hefz.start_from_ayat" => "required",
            "hefz.num_to_memorize" => "required",
            "hefz.memorization_mood" => "required",
            "hefz.pages_to_revise" => "required",
        ]);

        $plan = new StudentHefzPlan();

        $plan->study_direction = $request->hefz['study_direction'];
        $plan->start_from_surat = $request->hefz['start_from_surat'];
        $plan->start_from_ayat = $request->hefz['start_from_ayat'];
        $plan->num_to_memorize = $request->hefz['num_to_memorize'];
        $plan->memorization_mood = $request->hefz['memorization_mood'];
        $plan->pages_to_revise = $request->hefz['pages_to_revise'];
        $plan->student_id = $request->student_id;
        $plan->organization_id = config('organization_id');
        $plan->created_by = auth()->user()->id;
        if (isset($request->hefz['start_date'])) {
            $plan->start_date = $request->hefz['start_date'];
            $plan->status = 'waiting_for_approval';
        } else {
            $plan->status = 'waiting_for_approval';
        }

        $plan->save();

        if ($redirect) {
            Session::flash('flash_message', 'Student Study Plan was Added <Successfuly></Successfuly>!');
        }
        return redirect()->back();

    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }

    // new student registration / application form view method
    public function registration()
    {

        try {
            $max_admission_id = Student::max('student_number');
            $max_roll_id = Student::max('roll_no');
            $organizations = Organization::all();
            $classes = Classes::all();
            $programs = ProgramTranslation::where("locale", "en")->get();
            $genders = BaseSetup::where('base_group_id', '=', '1')->get();
            $reg_setting = RegistrationSetting::find(1);
            return view('admission::registration', compact('organizations', 'classes', 'programs', 'genders', 'reg_setting', 'max_admission_id', 'max_roll_id'));
//            return view('studentapplication::registration', compact('organizations', 'classes', 'programs', 'genders', 'reg_setting'));
        } catch (\Exception $e) {


            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    function admissionPic(Request $r)
    {

        try {
            $validator = Validator::make($r->all(), [
                'logo_pic' => 'sometimes|required|mimes:jpg,png|max:40000',

            ]);
            if ($validator->fails()) {
                return response()->json(['error' => 'error'], 201);
            }

            if ($r->hasFile('logo_pic')) {
                $file = $r->file('logo_pic');
                $images = Image::make($file)->insert($file);
                $pathImage = 'public/uploads/student/';
                if (!file_exists($pathImage)) {
                    mkdir($pathImage, 0777, true);
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    $images->save('public/uploads/student/' . $name);
                    $imageName = 'public/uploads/student/' . $name;
                    // $data->staff_photo =  $imageName;
                    Session::put('student_photo', $imageName);
                } else {
                    $name = md5($file->getClientOriginalName() . time()) . "." . "png";
                    if (file_exists(Session::get('student_photo'))) {
                        File::delete(Session::get('student_photo'));
                    }
                    $images->save('public/uploads/student/' . $name);
                    $imageName = 'public/uploads/student/' . $name;
                    // $data->student_photo =  $imageName;
                    Session::put('student_photo', $imageName);
                }
            }


            return response()->json('success', 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'error'], 201);
        }
    }

    // Get class for regular school and saas for new student registration
    public function getCenters(Request $request)
    {


        $centers = Center::whereHas("programs", function ($q) use ($request) {
            return $q->where('program_id', $request->id);
        })->get();


        return response()->json([$centers]);
    }

    public function getClasses(Request $request)
    {


        $classes = Classes::where('center_id', $request->id)->get();


        return response()->json([$classes]);
    }
}

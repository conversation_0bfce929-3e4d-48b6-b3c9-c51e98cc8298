<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;
use Modules\JobSeeker\Entities\SystemHealthCheck;

/**
 * Automated Recovery Service
 * 
 * Attempts to automatically fix common system issues to prevent
 * prolonged outages and reduce manual intervention requirements.
 */
final class AutomatedRecoveryService
{
    /**
     * Recovery strategies for different health checks
     */
    private const RECOVERY_STRATEGIES = [
        'email_notifications' => 'recoverEmailNotifications',
        'queue_processing' => 'recoverQueueProcessing',
        'database_connectivity' => 'recoverDatabaseConnectivity',
        'configuration_validation' => 'recoverConfiguration',
        'disk_space' => 'recoverDiskSpace',
        'memory_usage' => 'recoverMemoryUsage',
    ];

    /**
     * Maximum recovery attempts per check per hour
     */
    private const MAX_RECOVERY_ATTEMPTS = 3;

    /**
     * Attempt automated recovery for a specific health check
     */
    public function attemptRecovery(string $checkName, array $checkResult): array
    {
        try {
            // Check if recovery is available for this check
            if (!isset(self::RECOVERY_STRATEGIES[$checkName])) {
                return [
                    'attempted' => false,
                    'successful' => false,
                    'message' => 'No automated recovery strategy available',
                ];
            }

            // Check rate limiting to prevent excessive recovery attempts
            $rateLimitKey = "recovery_attempts:{$checkName}";
            $attempts = Cache::get($rateLimitKey, 0);
            
            if ($attempts >= self::MAX_RECOVERY_ATTEMPTS) {
                return [
                    'attempted' => false,
                    'successful' => false,
                    'message' => 'Maximum recovery attempts exceeded for this hour',
                ];
            }

            // Increment attempt counter
            Cache::put($rateLimitKey, $attempts + 1, 3600); // 1 hour

            // Execute recovery strategy
            $strategyMethod = self::RECOVERY_STRATEGIES[$checkName];
            $result = $this->$strategyMethod($checkResult);

            Log::info('AutomatedRecovery: Recovery attempted', [
                'check_name' => $checkName,
                'strategy' => $strategyMethod,
                'result' => $result,
            ]);

            return array_merge([
                'attempted' => true,
            ], $result);

        } catch (Exception $e) {
            Log::error('AutomatedRecovery: Recovery attempt failed', [
                'check_name' => $checkName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'attempted' => true,
                'successful' => false,
                'message' => 'Recovery attempt failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Recover email notification issues
     */
    private function recoverEmailNotifications(array $checkResult): array
    {
        $recoveryActions = [];
        $successful = false;

        try {
            // Check if notifications are disabled and attempt to enable them
            if (isset($checkResult['metrics']['notifications_disabled']) && 
                $checkResult['metrics']['notifications_disabled']) {
                
                // This is a critical configuration issue that requires manual intervention
                // We cannot automatically modify .env files for security reasons
                return [
                    'successful' => false,
                    'message' => 'CRITICAL: Notifications disabled in configuration - requires manual intervention',
                    'actions' => ['Configuration change required: Set JOBSEEKER_DISABLE_EVENT_NOTIFICATIONS=false'],
                ];
            }

            // Clear any stuck notification jobs
            if (isset($checkResult['metrics']['queue_size']) && 
                $checkResult['metrics']['queue_size'] > 100) {
                
                try {
                    // Restart queue workers (if using supervisor)
                    Artisan::call('queue:restart');
                    $recoveryActions[] = 'Restarted queue workers';
                } catch (Exception $e) {
                    $recoveryActions[] = 'Failed to restart queue workers: ' . $e->getMessage();
                }
            }

            // Clear cache that might be affecting notifications
            try {
                Cache::flush();
                $recoveryActions[] = 'Cleared application cache';
            } catch (Exception $e) {
                $recoveryActions[] = 'Failed to clear cache: ' . $e->getMessage();
            }

            // If we performed any actions, consider it a partial success
            $successful = !empty($recoveryActions);

            return [
                'successful' => $successful,
                'message' => $successful ? 
                    'Recovery actions performed: ' . implode(', ', $recoveryActions) :
                    'No recovery actions could be performed',
                'actions' => $recoveryActions,
            ];

        } catch (Exception $e) {
            return [
                'successful' => false,
                'message' => 'Email notification recovery failed: ' . $e->getMessage(),
                'actions' => $recoveryActions,
            ];
        }
    }

    /**
     * Recover queue processing issues
     */
    private function recoverQueueProcessing(array $checkResult): array
    {
        $recoveryActions = [];

        try {
            // Restart queue workers
            Artisan::call('queue:restart');
            $recoveryActions[] = 'Restarted queue workers';

            // Clear failed jobs if there are too many
            $failedJobs = Queue::size('failed');
            if ($failedJobs > 50) {
                Artisan::call('queue:flush');
                $recoveryActions[] = "Cleared {$failedJobs} failed jobs";
            }

            return [
                'successful' => true,
                'message' => 'Queue processing recovery completed: ' . implode(', ', $recoveryActions),
                'actions' => $recoveryActions,
            ];

        } catch (Exception $e) {
            return [
                'successful' => false,
                'message' => 'Queue processing recovery failed: ' . $e->getMessage(),
                'actions' => $recoveryActions,
            ];
        }
    }

    /**
     * Recover database connectivity issues
     */
    private function recoverDatabaseConnectivity(array $checkResult): array
    {
        $recoveryActions = [];

        try {
            // Clear database connection cache
            \DB::purge();
            $recoveryActions[] = 'Purged database connections';

            // Clear configuration cache
            Artisan::call('config:clear');
            $recoveryActions[] = 'Cleared configuration cache';

            // Test database connection
            \DB::connection()->getPdo();
            $recoveryActions[] = 'Database connection test successful';

            return [
                'successful' => true,
                'message' => 'Database connectivity recovery completed: ' . implode(', ', $recoveryActions),
                'actions' => $recoveryActions,
            ];

        } catch (Exception $e) {
            return [
                'successful' => false,
                'message' => 'Database connectivity recovery failed: ' . $e->getMessage(),
                'actions' => $recoveryActions,
            ];
        }
    }

    /**
     * Recover configuration issues
     */
    private function recoverConfiguration(array $checkResult): array
    {
        $recoveryActions = [];

        try {
            // Clear configuration cache
            Artisan::call('config:clear');
            $recoveryActions[] = 'Cleared configuration cache';

            // Clear route cache
            Artisan::call('route:clear');
            $recoveryActions[] = 'Cleared route cache';

            // Clear view cache
            Artisan::call('view:clear');
            $recoveryActions[] = 'Cleared view cache';

            // Note: We cannot automatically fix configuration values for security reasons
            // This recovery focuses on clearing caches that might be causing issues

            return [
                'successful' => true,
                'message' => 'Configuration recovery completed: ' . implode(', ', $recoveryActions),
                'actions' => $recoveryActions,
            ];

        } catch (Exception $e) {
            return [
                'successful' => false,
                'message' => 'Configuration recovery failed: ' . $e->getMessage(),
                'actions' => $recoveryActions,
            ];
        }
    }

    /**
     * Recover disk space issues
     */
    private function recoverDiskSpace(array $checkResult): array
    {
        $recoveryActions = [];

        try {
            // Clear Laravel logs older than 7 days
            $logPath = storage_path('logs');
            if (is_dir($logPath)) {
                $files = glob($logPath . '/laravel-*.log');
                $cutoff = time() - (7 * 24 * 60 * 60); // 7 days ago
                
                foreach ($files as $file) {
                    if (filemtime($file) < $cutoff) {
                        unlink($file);
                        $recoveryActions[] = 'Deleted old log file: ' . basename($file);
                    }
                }
            }

            // Clear cache files
            Artisan::call('cache:clear');
            $recoveryActions[] = 'Cleared cache files';

            // Clear compiled views
            Artisan::call('view:clear');
            $recoveryActions[] = 'Cleared compiled views';

            return [
                'successful' => !empty($recoveryActions),
                'message' => !empty($recoveryActions) ? 
                    'Disk space recovery completed: ' . implode(', ', $recoveryActions) :
                    'No disk space recovery actions were needed',
                'actions' => $recoveryActions,
            ];

        } catch (Exception $e) {
            return [
                'successful' => false,
                'message' => 'Disk space recovery failed: ' . $e->getMessage(),
                'actions' => $recoveryActions,
            ];
        }
    }

    /**
     * Recover memory usage issues
     */
    private function recoverMemoryUsage(array $checkResult): array
    {
        $recoveryActions = [];

        try {
            // Clear all caches to free memory
            Cache::flush();
            $recoveryActions[] = 'Cleared application cache';

            // Force garbage collection
            if (function_exists('gc_collect_cycles')) {
                $collected = gc_collect_cycles();
                $recoveryActions[] = "Garbage collection freed {$collected} cycles";
            }

            // Clear opcache if available
            if (function_exists('opcache_reset')) {
                opcache_reset();
                $recoveryActions[] = 'Reset OPcache';
            }

            return [
                'successful' => true,
                'message' => 'Memory usage recovery completed: ' . implode(', ', $recoveryActions),
                'actions' => $recoveryActions,
            ];

        } catch (Exception $e) {
            return [
                'successful' => false,
                'message' => 'Memory usage recovery failed: ' . $e->getMessage(),
                'actions' => $recoveryActions,
            ];
        }
    }

    /**
     * Get recovery statistics
     */
    public function getRecoveryStatistics(): array
    {
        $stats = [];
        
        foreach (array_keys(self::RECOVERY_STRATEGIES) as $checkName) {
            $rateLimitKey = "recovery_attempts:{$checkName}";
            $attempts = Cache::get($rateLimitKey, 0);
            
            $stats[$checkName] = [
                'attempts_this_hour' => $attempts,
                'max_attempts' => self::MAX_RECOVERY_ATTEMPTS,
                'can_attempt' => $attempts < self::MAX_RECOVERY_ATTEMPTS,
            ];
        }
        
        return $stats;
    }
}

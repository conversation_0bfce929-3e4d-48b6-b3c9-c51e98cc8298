<?php

return [
    'name' => 'General',
    "units" => [
        "dashboards" => [
            "icon" => "building-o",
            "actions" => [
                "have teacher dashboard",
                "have supervisor dashboard",
                "have finance dashboard",
                "have human_resource dashboard",
                "view approval-awaiting new applications"
            ]
        ],
        "backups" => [
            "icon" => "building-o",
            "actions" => [
                "have students backup",
                "download students backup"
            ]
        ],
        "status" => [
            "icon" => "building-o",
            "actions" => [
                "show system Logs"
            ]
        ],
        "commands" => [
            "icon" => "building-o",
            "actions" => [
                "show commands interface for admins"
            ]
        ],
//        "departments" => [
//            "icon" => "building-o",
//            "actions" => [
//                "access departments",
//                "add department",
//                "update department",
//                "delete department",
//                "view department",
//            ],
//        ],
        "roles" => [
            "icon" => "key",
            "actions" => [
                "access roles",
                "add role",
                "show role",
                "show role create form",
                "show role edit form",
                "update role",
                "remove role"
            ],
        ],
        "strategies" => [
            "icon" => "bullseye",
            "actions" => [
                "access strategies",
                "add strategy",
                "update strategy",
                "delete strategy",
                "view strategy",
            ]
        ],
        "form_builder" => [
            "icon" => "paper",
            "actions" => [
                "access form_builder",
                "add builder_form",
                "update builder_form",
                "delete builder_form",
                "view builder_form",
            ]
        ],
        "user_verifier" => [
            "icon" => "user",
            "actions" => [
                "access user_verifier",
                "add user_verifier",
                "update user_verifier",
                "delete user_verifier",
                "view user_verifier",
            ]
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Job Notifications Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration settings for the job notification system
    | including rate limits and security measures to prevent abuse and email
    | service blacklisting.
    |
    */
    
    'job_notifications' => [
        // System-wide rate limits
        'hourly_limit' => env('JOB_NOTIFICATION_HOURLY_LIMIT', 100),
        'daily_limit' => env('JOB_NOTIFICATION_DAILY_LIMIT', 500),
        
        // Per-subscriber rate limits
        'subscriber_hourly_limit' => env('JOB_NOTIFICATION_SUBSCRIBER_HOURLY_LIMIT', 2),
        'subscriber_daily_limit' => env('JOB_NOTIFICATION_SUBSCRIBER_DAILY_LIMIT', 5),
        
        // Timing controls (in milliseconds)
        'jitter_ms' => env('JOB_NOTIFICATION_JITTER_MS', 2000),  // Random delay up to 2 seconds
        'email_spacing_ms' => env('JOB_NOTIFICATION_EMAIL_SPACING_MS', 500),  // 500ms between each email
        
        // Security measures
        'max_recipients_per_setup' => env('JOB_NOTIFICATION_MAX_RECIPIENTS', 10),
        'max_setups_per_subscriber' => env('JOB_NOTIFICATION_MAX_SETUPS', 5),
        'min_notification_interval' => env('JOB_NOTIFICATION_MIN_INTERVAL', 1), // Minimum 1 hour between notifications
        
        // Throttling settings
        'throttle_attempts' => env('JOB_NOTIFICATION_THROTTLE_ATTEMPTS', 5),
        'throttle_minutes' => env('JOB_NOTIFICATION_THROTTLE_MINUTES', 30),
        
        // Additional protection
        'require_verification' => env('JOB_NOTIFICATION_REQUIRE_VERIFICATION', true),
        'verification_expiry_minutes' => env('JOB_NOTIFICATION_VERIFICATION_EXPIRY', 60),
    ],
];

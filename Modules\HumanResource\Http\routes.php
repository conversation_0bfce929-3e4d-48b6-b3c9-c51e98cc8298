<?php


use App\Attendance;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

Route::group(['middleware' => ['web', 'auth:employee','missedClockOutMiddleware'], 'prefix' => 'workplace/humanresource', 'namespace' => 'Modules\HumanResource\Http\Controllers'], function () {



    Route::get('/employees/{employee}/logout-everywhere', [\Modules\HumanResource\Http\Controllers\EmployeesController::class, 'logoutAll'])
        ->name('employees.logoutAll');
    Route::prefix('attendance/export')->name('attendance.per.employee.export.')->group(function () {
        Route::get('pdf/{employeeId}', [\Modules\HumanResource\Http\Controllers\AttendanceExportController::class, 'exportPdf'])->name('exportPdf');
        Route::get('excel/{employeeId}', [\Modules\HumanResource\Http\Controllers\AttendanceExportController::class, 'exportExcel'])->name('exportExcel');
        Route::get('word/{employeeId}', [\Modules\HumanResource\Http\Controllers\AttendanceExportController::class, 'exportWord'])->name('exportWord');
    });


//    Route::get('/mindmap/supervisor/{employee_id}', [\Modules\HumanResource\Http\Controllers\SupervisorMindmapController::class, 'getSupervisorMindMap'])->name('mindmap.supervisor');;
    Route::get('/mindmap/supervisor/{employee_id}', [\Modules\HumanResource\Http\Controllers\SupervisorMindmapController::class, 'show'])->name('mindmap.supervisor.show');




    Route::get('/permissions/', [\Modules\HumanResource\Http\Controllers\PermissionController::class, 'getPermissions'])->name('employee.permissions.list');
    Route::post('/permissions/create', [\Modules\HumanResource\Http\Controllers\PermissionController::class, 'createPermission'])->name('employee.permissions.create')->middleware('permission:add permission');
    Route::post('/permissions/assign-bulk', [\Modules\HumanResource\Http\Controllers\PermissionController::class, 'assignPermissionsBulk'])->name('employee.permissions.assign.bulk');
    Route::post('/permissions/revoke', [\Modules\HumanResource\Http\Controllers\PermissionController::class, 'revokePermission'])->name('employee.permissions.revoke');
    Route::post('/permissions/revoke-bulk', [\Modules\HumanResource\Http\Controllers\PermissionController::class, 'revokePermissionsBulk'])->name('employee.permissions.revoke.bulk');


    Route::post('employee-joining-letter-upload', 'EmployeeJoiningLetterController')->name('employee_joining_letter_upload');
    Route::post('employee/image/update', [\Modules\HumanResource\Http\Controllers\ProfileImageController::class, 'update'])->name('employee.updateImage');


//    Route::get('age-groups/bar-chart', 'EmployeeAgeGroupController')->name('employee.age.groups');
    Route::post('menusnav/reorder', 'EmployeesManagerController')->name('employee.manager');

    Route::post('/work-mode-user', 'WorkModeBasedEmployeesController')->name('employee.get.work.mode');
    Route::get('employees-list-pdf/date/{monthYear?}', 'EmployeesTablesPDFController@downloadPDF')->name('employees-list-pdf');
    Route::get('/employees/export-excel', [\Modules\HumanResource\Http\Controllers\EmployeesExcelDataController::class, 'exportEmployeesToExcel'])->name('employees-list-excel');
    Route::get('delete-staff-document/{id}', 'EmployeesController@deleteStaffDocument');

    Route::resource('employee_document', 'EmployeeDocumentController');

    Route::get('download-staff-document/{file_name}', '\Modules\HumanResource\Http\Controllers\EmployeeDocumentDownloader')->name('download.staff.document');
    Route::post('employee-document-reorder', '\Modules\HumanResource\Http\Controllers\EmployeeDocumentReorderer')->name('employee.document.reorder');

    Route::get('download-staff-joining-letter/{file_name}', function ($file_name = null) {
        $file = public_path() . '/uploads/staff_joining_letter/' . $file_name;
        if (file_exists($file)) {
            return Response::download($file);
        }
    })->name('download.staff.joining.letter');

    Route::get('download-resume/{file_name}', function ($file_name = null) {
        $file = public_path() . '/uploads/resume/' . $file_name;
        if (file_exists($file)) {
            return Response::download($file);
        }
    })->name('download.staff.resume');

    Route::get('download-other-document/{file_name}', function ($file_name = null) {
        $file = public_path() . '/uploads/others_documents/' . $file_name;
        if (file_exists($file)) {
            return Response::download($file);
        }
    })->name('download.staff.other.document');

    Route::get('download-staff-timeline-doc/{file_name}', function ($file_name = null) {
        $file = public_path() . '/uploads/staff/timeline/' . $file_name;
        if (file_exists($file)) {
            return Response::download($file);
        }
    })->name('download.staff.timeline.document');

    Route::get('/', 'HumanResourceController@index');


    Route::get('employees/json-data', 'EmployeesController@getEmployeesJsonFormat')->name('employees.json.data');
    Route::get('employees/status-data', 'EmployeesController@getEmployeesStatusJsonFormat')->name('employees.status.json.data');
    Route::get('employees/remaining-leaves-data', 'EmployeesController@getEmployeesRemainingLeaves')->name('employees.remaining.leaves.json.data');
    Route::get('missed-clockout-employees-based-on-gender', 'GenderBasedMissedClockoutEmployeesController@getGenderBasedEmployees')->name('missed.clockout.employees.based.on.gender.json.data');
    Route::put('update-student-program-level', 'StudentProgramLevelController@update')->name('update.student.program.level');
    Route::put('update-student-exam-readiness-ready', 'AddStudenttoExaminationController')->name('update.student.exam.readiness.status.ready');
    Route::put('update-student-exam-readiness-notready', 'RemoveStudentFromExaminationController')->name('update.student.exam.readiness.status.notReady');
    Route::post('get-states-by-country', 'CountryStateCityController@getState')->name('getState');


    Route::put('employees/archive/{empId}', 'ArchivedEmployeesController@archiveEmployee')->name('employees.archive');
//    Route::post('employees/add', 'EmployeesController@store')->name('employees.store');
    Route::match(['put', 'patch'], 'employees/archive/{archived_employee}', 'ArchivedEmployeesController@update')->name('employees.archive.update')->middleware('permission:update employee|unarchive employee');

//    Route::get('employees', 'EmployeesController@index')->name('employees');


    Route::get('employees/{id}/report/attendance/datatable', 'EmployeesDatatablesController@getAttendanceReport')->name("employees.attendance.report");
    Route::get('employees/{id}/bank-details/datatable', 'EmployeesBankDetailsDatatablesController@getBankDetails')->name("employees.bank.details");
//    Route::get('employees/{id}/bank/datatable', 'EmployeesBankAccountController@update')->name("employees.bank.details");


    Route::get('attendance-monthly-report/pdf/{year}/{month}/', 'MonthlyAttendanceReportPrintController@getPdf')->name('attendance.monthly.report.pdf');

//    Route::resource('employees', 'EmployeesController');

    Route::get('employees', 'EmployeesController@index')->name('employees.index')
        ->middleware('permission:access employees');
    Route::post('employees', 'EmployeesController@store')->name('employees.store')
        ->middleware('permission:add employee');
    Route::get('employees/create', 'EmployeesController@create')->name('employees.create')
        ->middleware('permission:show employee create form');
    Route::get('employees/{employee}', 'EmployeesController@show')->name('employees.show')
        ->middleware('permission:show employee');
    Route::get('employees/{id}/edit', 'EmployeesController@show')->name('employees.edit')
        ->middleware('permission:show employee edit form');
    Route::match(['put', 'patch'], 'employees/{employee}', 'EmployeesController@update')->name('employees.update')
        ->middleware('permission:update employee');
    Route::delete('employees/{id}', 'EmployeesController@destroy')->name('employees.delete')
        ->middleware('permission:remove employee');

    Route::get('employee-disable-enable', 'EmployeesController@staffDisableEnable');
    Route::get('employees/archived/{archived_employee}', 'ArchivedEmployeesController@showArchivedEmployees')->name("employees.show.archived");
    Route::get('teacher-center', 'TeacherCenterController@index')->name('teacher-center.index')->middleware('permission:access teacher center');
    Route::put('teacher-center-class', 'TeacherCenterClassController@update')->name('teacher-center.update')->middleware('permission:update teacher center');

    Route::get('departments', 'EmployeeDepartmentController@index')->name('employee-departments.index')->middleware('permission:access departments');
// Route for Employee Applicable Leave Date
    Route::get('employee-applicable-leave-date', 'EmployeeApplicableLeaveDateController@index')
        ->name('employee-applicable-leave-date.index')
        ->middleware('permission:access employees applicable leave date');

    Route::put('departments', 'EmployeeDepartmentController@update')->name('employee-departments.update')->middleware('permission:update departments');
    Route::put('employee-applicable-leave-date', 'EmployeeApplicableLeaveDateController@update')
        ->name('employee-applicable-leave-date.update')
        ->middleware('permission:update employees applicable leave date');
    Route::post('departments', 'EmployeeDepartmentController@store')
        ->name('employee-departments.store')
        ->middleware('permission:add departments');
    Route::delete('departments/{id}', 'EmployeeDepartmentController@destroy')
        ->name('employee-departments.destroy')
        ->middleware('permission:remove departments');

//    Route::post('employees', 'EmployeesController@updateSalary')->name('employees.salary.update');
//    Route::resource('employee-salary', 'EmployeeSalaryController')->middleware('permission:access salary');

    Route::get('employee-salary', 'EmployeeSalaryController@index')
        ->name('employee-salary.index')
        ->middleware('permission:access salary');

    Route::get('employee-salary/create', 'EmployeeSalaryController@create')
        ->name('employee-salary.create')
        ->middleware('permission:add salary');

    Route::post('employee-salary', 'EmployeeSalaryController@store')
        ->name('employee-salary.store')
        ->middleware('permission:add salary');

    Route::get('employee-salary/{id}/edit', 'EmployeeSalaryController@edit')
        ->name('employee-salary.edit')
        ->middleware('permission:update salary');

    Route::put('employee-salary/{id}', 'EmployeeSalaryController@update')
        ->name('employee-salary.update')
        ->middleware('permission:update salary');


    Route::resource('employee-document-dataTable', 'EmployeeDocumentDatatableController')->middleware('only.ajax');


    // Route::resource('roles', 'RoleController');

    Route::post('activate-employee', 'EmployeesController@activate')->name("activate.employee");

    Route::resource('attendance', 'AttendanceController');
    Route::resource('clockin', 'ClockInController');
    Route::resource('clockout', 'ClockOutController');
    Route::resource('attendanceclockinout', 'AttendanceClockInOutController');
    Route::resource('attendanceValidation', 'AttendanceValidationController');
    Route::resource('missedattendanceRevert', 'MissedAttendanceRevertController');

        Route::get('individual-employee/monthly-attendance/{id}', 'IndividualEmployeeMonthlyAttendanceController@show')->name('individual.employee.monthly.attendance') ->middleware('permission:show employee monthly attendance');

//    Route::get('attendance/{id}/{date}', 'AttendanceController@show')->middleware('role:managing-director_'.config('organization_id').'_|human-resource_'.config('organization_id').'_|it-officer_'.config('organization_id').'_|education-manager_'.config('organization_id').'_|supervisor_'.config('organization_id').'_', 'permission:show employee monthly attendance');

    Route::get('mobile/attendance', 'AttendanceController@mobile')->middleware('permission:manage self-attendance');

//    Route::resource('leave-requests', 'LeaveRequestController');

//    Route::resource('calendar', 'CalendarController');

    // mapping:
    Route::get('calendar', 'CalendarController@index')
        ->name('calendar.index')
        ->middleware('permission:access calendar');

    Route::get('calendar/{id}', 'CalendarController@show')
        ->name('calendar.show')
        ->middleware('permission:review calendar');

    Route::get('calendar/create', 'CalendarController@create')
        ->name('calendar.create')
        ->middleware('permission:add calendar');

    Route::post('calendar', 'CalendarController@store')
        ->name('calendar.store')
        ->middleware('permission:add calendar');

    Route::get('calendar/{id}/edit', 'CalendarController@edit')
        ->name('calendar.edit')
        ->middleware('permission:update calendar');

    Route::put('calendar/{id}', 'CalendarController@update')
        ->name('calendar.update')
        ->middleware('permission:update calendar');



    Route::get('calendar/members/{type}', 'CalendarController@members');

    Route::get(
        'salaries/reports/{employee_id}/{year_month}/create',
        'SalaryReportController@create'
    )->name('salary.report.create');

    Route::post(
        'salaries/reports',
        'SalaryReportController@store'
    )->name('salary.report.store');


    Route::get('live_search', 'EmployeesController@live_search')->name('employees.live_search');
    Route::get('searche', 'AttendanceController@searche')->name('attendance.searche');

    // Route::resource('events', 'CalendarController');
    Route::resource('profile', 'ProfileController');
    Route::get('daily-attendance-report', 'AttendanceController@daily_report')->name('attendance.daily_report');
//    Route::get('daily_report/search', 'AttendanceController@daily_report_search')->name('attendance.daily_report_search');
    Route::match(['post','get'],'daily_report/search', 'AttendanceController@daily_report_search')->name('attendance.daily_report_search');
    Route::get('monthly-attendance', [\Modules\HumanResource\Http\Controllers\AttendanceController::class, 'getMonthlyAttendanceData'])->name('attendance.monthly');

    Route::get('monthly_report', 'AttendanceController@monthly_report')->name('attendance.monthly_report');
//    Route::get('monthly-report-latest', 'EmployeesMonthlyAttendanceReportController')->name('attendance.monthly.report');
    Route::match(['get','post'],'attendance-monthly-report', 'EmployeesMonthlyAttendanceReportController')->name('attendance.monthly.report')->middleware('permission:access attendance monthly report|show attendance monthly report');
    Route::match(['get', 'post'], 'monthly-attendance-per-employee-report/{employee_id?}/{month?}/{year?}', 'PerEmployeeAttendanceReportController')
        ->name('attendance.per.employee.report');
//    ->middleware('permission:access attendance monthly report|show attendance monthly report');

    Route::post('attendance-per-employee-report/export/excel', 'PerEmployeeAttendanceReportController@exportExcel')
        ->name('attendance.per.employee.export.excel');
//        ->middleware('permission:access attendance monthly report|show attendance monthly report');

    Route::post('attendance-per-employee-report/export/word', 'PerEmployeeAttendanceReportController@exportWord')
        ->name('attendance.per.employee.export.word');
//        ->middleware('permission:access attendance monthly report|show attendance monthly report');

    Route::post('attendance-per-employee-report/export/pdf', 'PerEmployeeAttendanceReportController@exportPdf')
        ->name('attendance.per.employee.export.pdf');





    Route::get('employees-dropdown-refresh',  '\Modules\HumanResource\Http\Controllers\EmployeesController@refreshDropdown' )
        ->name('employees.dropdown.refresh');
//        ->middleware('auth'); // or any appropriate middleware

    Route::get('monthly_report/search', 'AttendanceController@monthly_report_search')->name('attendance.monthly_report_search');
    Route::get('datatable/attendance/employee/{employeeId}/{date}', 'AttendanceDatatablesController@getDateBasedAttendanceTransactions')->name('datatable.employeeAttendance.dateBased');
    Route::put('attendance/add-note/{employeeId}', 'AttendanceController@addNoteToAtttendance')->name('employeeAttendance.dateBased');
    Route::get('get-note/attendance/{attendanceId}', 'AttendanceController@getAttendanceNote')->name('employeeAttendance.getNote');
    Route::get('employee/{employeeId}/attendance/{attendanceId}/pair/{type}', 'AttendanceController@getAttendancePair')
        ->name('employee-attendance.get-attendance-pair');

    Route::post('record/today/attendance/in', 'AttendanceController@storeInRecord')->name('record.employee.in.attendance.today');
    Route::post('record/attendance/in', 'IndividualEmployeeMonthlyAttendanceController@hrstoreInRecord')->name('record.employee.in.attendance');
    Route::post('record/attendance/out', 'IndividualEmployeeMonthlyAttendanceController@hrstoreOutRecord')->name('record.employee.out.attendance');
    Route::post('record/today/attendance/out', 'AttendanceController@storeOutRecord')->name('record.employee.out.attendance.today');
    Route::post('record/today/attendance/', 'AttendanceController@storePairRecord')->name('record.employee.attendance.today');

//    Route::put('update/attendance/pair', 'AttendanceController@updateAttendanceEntryPair')->name('record.employee.attendance.today');
    Route::put('employee/attendance/pair/update', 'AttendanceController@updateAttendanceEntryPair')
        ->name('employee-attendance.update-pair');
//    Route::put('update/attendance/in', 'ClockInController@updateAttendanceIn')->name('record.employee.attendance.today');
    Route::put('attendance/update/in', 'ClockInController@updateAttendanceIn')
        ->name('employee-attendance.update-in');
    Route::put('update/attendance/out', 'ClockOutController@updateAttendanceOut')->name('record.employee.attendance.out')->middleware('permission:update employee');
    Route::put('daily-attendance/add/attendance/out', 'DailyAttendanceAddClockOutController@addAttendanceOut')->name('daily.attendance.add.employee.attendance.out')->middleware('permission:update employee');
    Route::delete('delete/attendance/individual-record/{id}/', 'AttendanceController@destroyIndividualRecord')->name('destroy.employee.attendance.pair');
    Route::get('download/clockindocuments/{attendance_id}/{file_name}', function ($attendance_id, $file_name = null) {
        $file = storage_path('app/public/clockindocuments/' . $attendance_id . '/' . $file_name);

        if (file_exists($file)) {
            return Response::download($file);
        }
    })->name('download.staff.clockin.document');
    Route::get('download/clockoutdocuments/{attendance_id}/{file_name}', function ($attendance_id, $file_name = null) {
        $file = storage_path('app/public/clockoutdocuments/' . $attendance_id . '/' . $file_name);

        if (file_exists($file)) {
            return Response::download($file);
        }
    })->name('download.staff.clockout.document');
    Route::get('hefz-revision-calendar-data/{classId}', '\Modules\Education\Http\Controllers\HefzRevisionCalendar');



});

Route::group(['middleware' => ['web', 'auth:employee'], 'prefix' => 'workplace/humanresource', 'namespace' => 'Modules\HumanResource\Http\Controllers'], function () {
    Route::put('missed/attendance/out', 'ClockOutController@updateMissedAttendanceOut')->name('update.employee.missed.attendance.out');
    Route::put('add-missed/attendance/out', 'ClockOutController@addMissedAttendanceOut')->name('add.employee.missed.attendance.out');
});
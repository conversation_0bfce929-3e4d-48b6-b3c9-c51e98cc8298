!function(o,i,t,e){"use strict";o("#js-grid-mosaic").cubeportfolio({filters:"#js-filters-mosaic",loadMore:"#js-loadMore-mosaic",loadMoreAction:"click",layoutMode:"mosaic",sortToPreventGaps:!0,mediaQueries:[{width:1500,cols:5},{width:1100,cols:4},{width:800,cols:3},{width:480,cols:2},{width:320,cols:1}],defaultFilter:"*",animationType:"quicksand",gapHorizontal:0,gapVertical:0,gridAdjustment:"responsive",caption:"zoom",displayType:"sequentially",displayTypeSpeed:100,lightboxDelegate:".cbp-lightbox",lightboxGallery:!0,lightboxTitleSrc:"data-title",lightboxCounter:'<div class="cbp-popup-lightbox-counter">{{current}} of {{total}}</div>'})}(jQuery,window,document);
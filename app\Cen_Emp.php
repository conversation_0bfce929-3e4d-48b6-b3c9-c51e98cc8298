<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Cen_Emp extends Model
{

    public $table = 'cen_emps';

    public function center(){
        return $this->belongsTo('App\Centers','center.id');
    }


    public function employee(){

        return $this->belongsToMany(Employee::class, 'cen_emps', 'emp_id', 'cen_id')
            ->using(CenterEmployee::class)->withTimestamps();

//        return $this->belongsTo('App\Employee','employee.id');
//        return $this->belongsToMany('App\Employee','employee.id');
    }


}

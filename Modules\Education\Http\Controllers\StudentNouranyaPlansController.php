<?php

namespace Modules\Education\Http\Controllers;

use App\StudentNouranyaPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StudentNouranyaPlansController
{
// StudentNouranyaPlansController.php



    public function getCurrentToLesson($studentId, $yearMonth)
    {

        $date = \Carbon\Carbon::createFromFormat('F Y', $yearMonth);
        $planYearAndMonth = $date->format('Y-m');


        $currentPlan = StudentNouranyaPlan::where('student_id', $studentId)
            ->where('plan_year_and_month', $planYearAndMonth)
            ->latest('id')
            ->first(['to_lesson']);

        if ($currentPlan) {
            return response()->json(['success' => true, 'toLessonId' => $currentPlan->to_lesson]);
        }

        return response()->json(['success' => false, 'message' => 'No current plan found.'], 404);
    }

    public function updateToLesson(Request $request, $studentId)
    {


        $validatedData = $request->validate([
            'program_level_id' => 'required',
            'fromDate' => 'required',
            'classId' => 'required|integer|exists:classes,id',
            'centerId' => 'required|integer|exists:centers,id',
            'toLesson' => 'required|integer',
            // other fields if necessary...
        ]);

//        DB::beginTransaxction();
        try {

            $date = \Carbon\Carbon::createFromFormat('F Y', $validatedData['fromDate']);
            $planYearAndMonth = $date->format('Y-m');
            $planYearAndMonthDay = $date->startOfMonth()->format('Y-m-d');

            // Check if there's an existing record with a different class ID
            $existingRecord = StudentNouranyaPlan::where('student_id', $studentId)
                ->where('plan_year_and_month', $planYearAndMonth)
                ->first();

                
                
            if ($existingRecord && $existingRecord->class_id != $validatedData['classId']) {
                // If a record exists but for a different class, create a new plan entry for the current class


                // Create a new record
                $nouranyaPlan = StudentNouranyaPlan::create([
                    'student_id' => $studentId,
                    'plan_year_and_month' => $planYearAndMonth,
                    'class_id' => $validatedData['classId'],
                    'to_lesson' => $validatedData['toLesson'],
                    'center_id' => $validatedData['centerId'],
                    'start_date' => $planYearAndMonthDay,
                    'status' => 'waiting_for_approval',  // Set status to waiting_for_approval for new records

                    // Include additional fields that need updating...
                ]);
            } else {


                $nouranyaPlan = StudentNouranyaPlan::updateOrCreate(
                    [
                        'student_id' => $studentId,
                        'plan_year_and_month' => $planYearAndMonth,
                        'class_id' => $validatedData['classId'],
                    ],
                    [
                        'organization_id' => config('organization_id'), // Assuming organization_id comes from the request and is validated
                        'status' => 'waiting_for_approval', // For example, set a default status
                        'to_lesson' => $validatedData['toLesson'],
                        'center_id' => $validatedData['centerId'],
                        'start_date' => $planYearAndMonthDay,
                        // Include additional fields that need updating...
                    ]
                );
            }

            // Return a success response with the current status
            return response()->json([
                'success' => true,
                'message' => 'To lesson updated successfully.',
//                'status' => $nouranyaPlan->status // Include the status in the response
                'status' => 'Waiting for Approval' // Include the status in the response
            ]);
        } catch (\Exception $e) {
//            DB::rollBack();
            Log::error("Failed to update student's to_lesson: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update to_lesson. '.$e->getMessage()], 500);
        }
    }
    public function updateTalaqqiToLesson(Request $request, $studentId)
    {


        $validatedData = $request->validate([
            'program_level_id' => 'required',
            'fromDate' => 'required',
            'classId' => 'required|integer|exists:classes,id',
            'centerId' => 'required|integer|exists:centers,id',
            'toLesson' => 'required|integer',
            // other fields if necessary...
        ]);

        DB::beginTransaction();
        try {
            $date = \Carbon\Carbon::createFromFormat('F Y', $validatedData['fromDate']);
            $planYearAndMonth = $date->format('Y-m');
            $planYearAndMonthDay = $date->startOfMonth()->format('Y-m-d');

            // Check if there's an existing record with a different class ID
            $existingRecord = StudentNouranyaPlan::where('student_id', $studentId)
                ->where('plan_year_and_month', $planYearAndMonth)
                ->first();


            if ($existingRecord && $existingRecord->class_id != $validatedData['classId']) {
                // Create a new record
                $nouranyaPlan = StudentNouranyaPlan::create([
                    'student_id' => $studentId,
                    'plan_year_and_month' => $planYearAndMonth,
                    'class_id' => $validatedData['classId'],
                    'talaqqi_to_lesson' => $validatedData['toLesson'],
                    'center_id' => $validatedData['centerId'],
                    'start_date' => $planYearAndMonthDay,
                    'status' => 'waiting_for_approval', // Set the status to 'waiting_for_approval'
                    'organization_id' => config('organization_id')
                    // Include additional fields that need updating...
                ]);
            } else {
                // Update or create the record
                $nouranyaPlan = StudentNouranyaPlan::updateOrCreate(
                    [
                        'student_id' => $studentId,
                        'plan_year_and_month' => $planYearAndMonth,
                        'class_id' => $validatedData['classId'],
                    ],
                    [
                        'to_lesson' => NULL,
                        'talaqqi_to_lesson' => $validatedData['toLesson'],
                        'center_id' => $validatedData['centerId'],
                        'start_date' => $planYearAndMonthDay,
                        'status' => 'waiting_for_approval', // Set the status to 'waiting_for_approval'
                        'organization_id' => config('organization_id')
                        // Include additional fields that need updating...
                    ]
                );
                // Remove all other duplicate entries with the same student_id, plan_year_and_month, and class_id
                StudentNouranyaPlan::where('student_id', $studentId)
                    ->where('plan_year_and_month', $planYearAndMonth)
                    ->where('class_id', $validatedData['classId'])
                    ->where('id', '!=', $nouranyaPlan->id)
                    ->delete();
            }


            // Determine the status message based on missing fields
            $missingFields = [];
            if ($nouranyaPlan->status == 'waiting_for_approval') {
                // Check Talaqqi fields
                if (isset($nouranyaPlan->talaqqi_from_lesson) && is_null($nouranyaPlan->talaqqi_to_lesson)) {
                    $missingFields[] = 'Talaqqi To Lesson';
                }
                if (is_null($nouranyaPlan->talaqqi_from_lesson) && isset($nouranyaPlan->talaqqi_to_lesson)) {
                    $missingFields[] = 'Talaqqi From Lesson';
                }

                // Check Talqeen fields
                if (isset($nouranyaPlan->talqeen_from_lesson) && is_null($nouranyaPlan->talqeen_to_lesson)) {
                    $missingFields[] = 'Talqeen To Lesson';
                }
                if (is_null($nouranyaPlan->talqeen_from_lesson) && isset($nouranyaPlan->talqeen_to_lesson)) {
                    $missingFields[] = 'Talqeen From Lesson';
                }

                // Check General lessons fields
                if (isset($nouranyaPlan->from_lesson) && is_null($nouranyaPlan->to_lesson)) {
                    $missingFields[] = 'To Lesson';
                }
                if (is_null($nouranyaPlan->from_lesson) && isset($nouranyaPlan->to_lesson)) {
                    $missingFields[] = 'From Lesson';
                }
            }

            $statusMessage = '';
            if (!empty($missingFields)) {
                $statusMessage = 'Please fill in: ' . implode(', ', $missingFields);
            } else {
                $statusMessage = 'Waiting for Approval';
            }





            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'To lesson updated successfully.',
                'status' => $statusMessage,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to update student's to_lesson: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update to_lesson. '.$e->getMessage()], 500);
        }
    }
    public function updateTalqeenToLesson(Request $request, $studentId)
    {
        $validatedData = $request->validate([
            'program_level_id' => 'required',
            'fromDate' => 'required',
            'classId' => 'required|integer|exists:classes,id',
            'centerId' => 'required|integer|exists:centers,id',
            'toLesson' => 'required|integer',
            // other fields if necessary...
        ]);

        DB::beginTransaction();
        try {
            $date = \Carbon\Carbon::createFromFormat('F Y', $validatedData['fromDate']);
            $planYearAndMonth = $date->format('Y-m');
            $planYearAndMonthDay = $date->startOfMonth()->format('Y-m-d');

            // Check if there's an existing record with a different class ID
            $existingRecord = StudentNouranyaPlan::where('student_id', $studentId)
                ->where('plan_year_and_month', $planYearAndMonth)
                ->first();

            if ($existingRecord && $existingRecord->class_id != $validatedData['classId']) {
                // Create a new record
                $nouranyaPlan = StudentNouranyaPlan::create([
                    'student_id' => $studentId,
                    'plan_year_and_month' => $planYearAndMonth,
                    'class_id' => $validatedData['classId'],
                    'talqeen_to_lesson' => $validatedData['toLesson'],
                    'center_id' => $validatedData['centerId'],
                    'start_date' => $planYearAndMonthDay,
                    'status' => 'waiting_for_approval', // Set the status to 'waiting_for_approval'

                    // Include additional fields that need updating...
                ]);
            } else {
                // Update or create the record
                $nouranyaPlan = StudentNouranyaPlan::updateOrCreate(
                    [
                        'student_id' => $studentId,
                        'plan_year_and_month' => $planYearAndMonth,
                        'class_id' => $validatedData['classId'],
                    ],
                    [
                        'talqeen_to_lesson' => $validatedData['toLesson'],
                        'center_id' => $validatedData['centerId'],
                        'start_date' => $planYearAndMonthDay,
                        'status' => 'waiting_for_approval', // Set the status to 'waiting_for_approval'

                        // Include additional fields that need updating...
                    ]
                );
                // Remove all other duplicate entries with the same student_id, plan_year_and_month, and class_id
                StudentNouranyaPlan::where('student_id', $studentId)
                    ->where('plan_year_and_month', $planYearAndMonth)
                    ->where('class_id', $validatedData['classId'])
                    ->where('id', '!=', $nouranyaPlan->id)
                    ->delete();
            }
            // Determine the status message based on missing fields
            $missingFields = [];
            if ($nouranyaPlan->status == 'waiting_for_approval') {
                // Check Talaqqi fields
                if (isset($nouranyaPlan->talqeen_from_lesson) && is_null($nouranyaPlan->talaqqi_to_lesson)) {
                    $missingFields[] = 'Talaqqi To Lesson';
                }
                if (is_null($nouranyaPlan->talaqqi_from_lesson) && isset($nouranyaPlan->talaqqi_to_lesson)) {
                    $missingFields[] = 'Talaqqi From Lesson';
                }

                // Check Talqeen fields
                if (isset($nouranyaPlan->talqeen_from_lesson) && is_null($nouranyaPlan->talqeen_to_lesson)) {
                    $missingFields[] = 'Talqeen To Lesson';
                }
                if (is_null($nouranyaPlan->talqeen_from_lesson) && isset($nouranyaPlan->talqeen_to_lesson)) {
                    $missingFields[] = 'Talqeen From Lesson';
                }

                // Check General lessons fields
                if (isset($nouranyaPlan->from_lesson) && is_null($nouranyaPlan->to_lesson)) {
                    $missingFields[] = 'To Lesson';
                }
                if (is_null($nouranyaPlan->from_lesson) && isset($nouranyaPlan->to_lesson)) {
                    $missingFields[] = 'From Lesson';
                }
            }

            $statusMessage = '';
            if (!empty($missingFields)) {
                $statusMessage = 'Please fill in: ' . implode(', ', $missingFields);
            } else {
                $statusMessage = 'Waiting for Approval';
            }





            DB::commit();
            return response()->json(['success' => true, 'message' => 'To lesson updated successfully.', 'status' => $statusMessage,]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to update student's to_lesson: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update to_lesson. '.$e->getMessage()], 500);
        }
    }
    public function updateLineNumber(Request $request, $studentId)
    {
        $validatedData = $request->validate([
            'program_level_id' => 'required',
            'fromDate' => 'required',
            'classId' => 'required|integer|exists:classes,id',
            'centerId' => 'required|integer|exists:centers,id',
            'lineNumber' => 'required|string', // Assuming line numbers are strings
            'lessonNo' => 'required|integer', // Validate the lesson ID
            // other fields if necessary...
        ]);

        DB::beginTransaction();
        try {
            $date = \Carbon\Carbon::createFromFormat('F Y', $validatedData['fromDate']);
            $planYearAndMonth = $date->format('Y-m');
            $planYearAndMonthDay = $date->startOfMonth()->format('Y-m-d');

            // Check if there's an existing record with a different class ID
            $existingRecord = StudentNouranyaPlan::where('student_id', $studentId)
                ->where('plan_year_and_month', $planYearAndMonth)
                ->first();

            if ($existingRecord && $existingRecord->class_id != $validatedData['classId']) {
                // Create a new record
                $nouranyaPlan = StudentNouranyaPlan::create([
                    'student_id' => $studentId,
                    'plan_year_and_month' => $planYearAndMonth,
                    'class_id' => $validatedData['classId'],
                    'from_lesson_line_number' => $validatedData['lineNumber'],
                    'center_id' => $validatedData['centerId'],
                    'from_lesson' => $validatedData['lessonNo'], // Include lesson ID
                    'start_date' => $planYearAndMonthDay,
                    'organization_id' => config('organization_id'),

                    // Include additional fields that need updating...
                ]);
            } else {
                // Update or create the record
                $nouranyaPlan = StudentNouranyaPlan::updateOrCreate(
                    [
                        'student_id' => $studentId,
                        'plan_year_and_month' => $planYearAndMonth,
                        'class_id' => $validatedData['classId'],
                    ],
                    [
                        'from_lesson_line_number' => $validatedData['lineNumber'],
                        'center_id' => $validatedData['centerId'],
                        'from_lesson' => $validatedData['lessonNo'], // Include lesson ID
                        'start_date' => $planYearAndMonthDay,
                        // Include additional fields that need updating...
                    ]
                );
            }

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Line number and lesson updated successfully.']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to update student's line_number and lesson: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update line number and lesson. '.$e->getMessage()], 500);
        }
    }
    public function updateToLessonLineNumber(Request $request, $studentId)
    {
        $validatedData = $request->validate([
            'program_level_id' => 'required',
            'fromDate' => 'required',
            'classId' => 'required|integer|exists:classes,id',
            'centerId' => 'required|integer|exists:centers,id',
            'lineNumber' => 'required|string', // Assuming line numbers are strings
            'lessonNo' => 'required|integer', // Validate the lesson ID
            // other fields if necessary...
        ]);

        DB::beginTransaction();
        try {
            $date = \Carbon\Carbon::createFromFormat('F Y', $validatedData['fromDate']);
            $planYearAndMonth = $date->format('Y-m');
            $planYearAndMonthDay = $date->startOfMonth()->format('Y-m-d');

            // Check if there's an existing record with a different class ID
            $existingRecord = StudentNouranyaPlan::where('student_id', $studentId)
                ->where('plan_year_and_month', $planYearAndMonth)
                ->first();

            // If a record exists for the same month but for a different class, create a new plan entry for the current class
            if ($existingRecord && $existingRecord->class_id != $validatedData['classId']) {
                // Create a new record
                $nouranyaPlan = StudentNouranyaPlan::create([
                    'student_id' => $studentId,
                    'plan_year_and_month' => $planYearAndMonth,
                    'class_id' => $validatedData['classId'],
                    'to_lesson_line_number' => $validatedData['lineNumber'],
                    'center_id' => $validatedData['centerId'],
                    'to_lesson' => $validatedData['toLesson'], // Include lesson ID
                    'start_date' => $planYearAndMonthDay,
                    'status' => 'waiting_for_approval', // Set the status to 'waiting_for_approval'

                    // Include additional fields that need updating...
                ]);
            } else {
                // Update or create the record
                $nouranyaPlan = StudentNouranyaPlan::updateOrCreate(
                    [
                        'student_id' => $studentId,
                        'plan_year_and_month' => $planYearAndMonth,
                        'class_id' => $validatedData['classId'],

                    ],
                    [
                        'to_lesson_line_number' => $validatedData['lineNumber'],
                        'center_id' => $validatedData['centerId'],
//                        'to_lesson' => $validatedData['toLesson'], // Include lesson ID
                        'start_date' => $planYearAndMonthDay,
                        'status' => 'waiting_for_approval', // Set the status to 'waiting_for_approval'

                        // Include additional fields that need updating...
                    ]
                );
                $message = $existingRecord ? 'Existing plan updated and set to waiting for approval.' : 'Plan created and set to waiting for approval.';

            }

            DB::commit();
//            return response()->json(['success' => true, 'message' => $message, 'status' => $nouranyaPlan->status]);
            return response()->json(['success' => true, 'message' => $message, 'status' => 'Waiting for Approval']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to update student's to_lesson_line_number: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update to lesson line number. '.$e->getMessage()], 500);
        }
    }
}
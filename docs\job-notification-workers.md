# Job Notification System: Parallel Queue Workers

This document explains how to configure, manage, and scale the queue workers for the job notification system to handle large volumes of notifications efficiently.

## Overview

The job notification system uses a multi-queue approach with specialized worker pools to maximize throughput and performance. The system is designed to efficiently handle 10,000+ notification setups with multiple recipients each.

### Queue Structure

The system uses the following specialized queues:

| Queue Name | Purpose | Worker Count | Priority |
|------------|---------|--------------|----------|
| `setup_processors` | Process notification setups and dispatch recipient jobs | 2 | High |
| `recipient_processors` | Process individual recipient notifications | 8 | Medium |
| `notifications` | General notification-related jobs | 4 | Medium |
| `default` | Other application jobs | 2 | Normal |

## Installation and Setup

### 1. Prerequisites

- Supervisor (`sudo apt-get install supervisor` on Ubuntu/Debian)
- PHP 8.1+ with required extensions
- MySQL database
- Access to cron for scheduling

### 2. Configuration Files

The system uses the following configuration files:

1. **Queue Worker Configuration**: `config/queue_workers.php`
2. **Supervisor Configuration**: `scripts/supervisor/job-notification-workers.conf`
3. **Worker Management Script**: `scripts/manage-queue-workers.sh`

### 3. Setup Instructions

1. **Set the correct path in configuration files**:

   Edit the worker configuration paths in the Supervisor config:

   ```bash
   # Edit paths in the Supervisor config
   sed -i "s#/path/to/itqan#/your/actual/path#g" scripts/supervisor/job-notification-workers.conf
   ```

2. **Make the management script executable**:

   ```bash
   chmod +x scripts/manage-queue-workers.sh
   ```

3. **Set the correct path in the management script**:

   ```bash
   # Edit APP_ROOT in the script
   sed -i "s#/path/to/itqan#/your/actual/path#g" scripts/manage-queue-workers.sh
   ```

4. **Configure Supervisor**:

   ```bash
   sudo ./scripts/manage-queue-workers.sh --configure
   ```

5. **Verify configuration**:

   ```bash
   sudo ./scripts/manage-queue-workers.sh --status
   ```

## Managing Queue Workers

The `scripts/manage-queue-workers.sh` script provides several commands for managing queue workers:

```bash
# Show help
./scripts/manage-queue-workers.sh --help

# Check worker status and queue statistics
sudo ./scripts/manage-queue-workers.sh --status

# Restart all workers
sudo ./scripts/manage-queue-workers.sh --restart

# Restart specific worker groups
sudo ./scripts/manage-queue-workers.sh --restart-program recipient

# View worker logs
sudo ./scripts/manage-queue-workers.sh --logs

# Clear all queues (use with caution!)
sudo ./scripts/manage-queue-workers.sh --clear
```

## Scaling for Higher Loads

The system is preconfigured to handle approximately 10,000 notification setups with 5 recipients each. For larger volumes, you can scale the worker pools as needed.

### Scaling Options

1. **Increasing worker count**: More workers allow more parallel processing.

   ```bash
   # Scale recipient workers to 16 (default is 8)
   sudo ./scripts/manage-queue-workers.sh --scale recipient-workers 16
   ```

2. **Adjusting worker parameters**: Optimize memory and job processing settings.

   Edit `config/queue_workers.php` to adjust parameters like memory limits, timeout values, etc.

3. **Horizontal scaling**: For very high volumes, consider running workers on multiple servers.

### General Scaling Guidelines

| Notification Setups | Recommended Setup Workers | Recommended Recipient Workers |
|---------------------|--------------------------|------------------------------|
| Up to 5,000         | 2                        | 4-8                         |
| 5,000 - 15,000      | 2-4                      | 8-16                        |
| 15,000 - 30,000     | 4-6                      | 16-32                       |
| 30,000+             | 6+                       | 32+                         |

## Monitoring

### Real-time Monitoring

Check the current state of all queue workers:

```bash
sudo supervisorctl status
```

View real-time worker logs:

```bash
sudo ./scripts/manage-queue-workers.sh --logs
```

### Queue Statistics

View statistics about job queues:

```bash
php artisan queue:size setup_processors
php artisan queue:size recipient_processors
```

Check failed jobs:

```bash
php artisan queue:failed
```

### Handling Failed Jobs

If jobs fail, you can retry them:

```bash
# Retry all failed jobs
php artisan queue:retry all

# Retry a specific failed job ID
php artisan queue:retry 5

# Clear failed jobs
php artisan queue:flush
```

## Troubleshooting

### Common Issues

1. **Workers not processing jobs**:
   - Check Supervisor status: `sudo supervisorctl status`
   - Restart workers: `sudo ./scripts/manage-queue-workers.sh --restart`
   - Check for errors in logs: `sudo ./scripts/manage-queue-workers.sh --logs`

2. **High memory usage**:
   - Decrease max_jobs parameter to restart workers more frequently
   - Increase memory limit if needed
   - Check for memory leaks in job handlers

3. **Jobs stuck in the queue**:
   - Check for failed workers: `sudo supervisorctl status`
   - Verify database connection issues
   - Check for locks or deadlocks

### Performance Tuning

For optimal performance:

1. Use a dedicated database server for queue tables if possible
2. Create proper indexes on the jobs table
3. Regularly clean up processed and failed jobs
4. Consider implementing a queue monitoring dashboard

## Best Practices

1. **Graceful deployment**: Pause workers before deploying application updates
2. **Regular monitoring**: Set up alerts for failed jobs and queue size
3. **Backup plan**: Document procedures for manual job handling if the queue system fails
4. **Load testing**: Test the system with expected peak loads before going live 
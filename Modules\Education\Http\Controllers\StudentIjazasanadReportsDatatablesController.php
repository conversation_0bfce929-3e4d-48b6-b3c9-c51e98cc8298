<?php

declare(strict_types=1);

namespace Modules\Education\Http\Controllers;

use App\AttendanceOption;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use App\Student;
use App\StudentIjazasanadMemorizationReport;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

final class StudentIjazasanadReportsDatatablesController extends Controller
{
    public function getStudents(Request $request): JsonResponse
    {
        try {
            $classId = (int)$request->input('class_id');
            
            if (!$classId) {
                return response()->json(['error' => 'Class ID is required'], 400);
            }

            // Get students for the class
            $students = Student::whereHas('joint_classes', function ($query) use ($classId) {
                $query->where('class_id', $classId);
            })
            ->where('status', 'active')
            ->orderBy('full_name', 'asc')
            ->get(['id', 'full_name', 'student_photo']);

            $data = [];
            foreach ($students as $index => $student) {
                $data[] = [
                    'DT_RowIndex' => $index + 1,
                    'student_id' => $student->id,
                    'student_name' => $student->full_name,
                    'student_photo' => $student->student_photo ? asset('students/' . $student->student_photo) : null,
                    'select_button' => '<button class="btn btn-primary btn-sm select-student" data-student-id="' . $student->id . '" data-student-name="' . $student->full_name . '">
                                        <i class="fa fa-check"></i> Select
                                      </button>'
                ];
            }

            return DataTables::of($data)
                ->rawColumns(['select_button'])
                ->toJson();

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function getStudentReports(Request $request): JsonResponse
    {
        try {
            $studentId = (int)$request->input('student_id');
            $classId = (int)$request->input('class_id');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            if (!$studentId || !$classId) {
                return response()->json(['error' => 'Student ID and Class ID are required'], 400);
            }

            // Parse dates
            $startDate = $startDate ? Carbon::createFromFormat('Y-m-d', $startDate) : Carbon::now()->startOfYear();
            $endDate = $endDate ? Carbon::createFromFormat('Y-m-d', $endDate) : Carbon::now()->endOfYear();

            // Get student reports
            $reports = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
                ->where('class_id', $classId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->orderBy('created_at', 'desc')
                ->get();

            $data = [];
            foreach ($reports as $index => $report) {
                $data[] = [
                    'DT_RowIndex' => $index + 1,
                    'date' => $this->formatDateWithLink($report->created_at, $classId),
                    'day' => $report->created_at->format('l'),
                    'from_surah' => $this->getSurahName($report->hefz_from_surat),
                    'from_ayat' => $report->hefz_from_ayat ?? '—',
                    'to_surah' => $this->getSurahName($report->hefz_to_surat),
                    'to_ayat' => $report->hefz_to_ayat ?? '—',
                    'juz_covered' => $this->calculateJuzCovered($report),
                    'attendance' => $this->getAttendanceStatus($report->attendance_id),
                    'evaluation' => $this->getEvaluationStatus($report->hefz_evaluation_id),
                    'mistakes' => $report->hefz_mistakes ?? 0,
                    'notes' => $report->hefz_evaluation_note ?? '—'
                ];
            }

            return DataTables::of($data)
                ->rawColumns(['attendance', 'evaluation'])
                ->toJson();

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function getStudentSummary(Request $request): JsonResponse
    {
        try {
            $studentId = (int)$request->input('student_id');
            $classId = (int)$request->input('class_id');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            if (!$studentId || !$classId) {
                return response()->json(['error' => 'Student ID and Class ID are required'], 400);
            }

            // Parse dates
            $startDate = $startDate ? Carbon::createFromFormat('Y-m-d', $startDate) : Carbon::now()->startOfYear();
            $endDate = $endDate ? Carbon::createFromFormat('Y-m-d', $endDate) : Carbon::now()->endOfYear();

            // Get student reports for the period
            $reports = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
                ->where('class_id', $classId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->get();

            // Calculate summary metrics
            $totalDays = $reports->count();
            $attendedDays = $reports->whereIn('attendance_id', [1, 2])->count(); // 1 = late, 2 = on time
            $absentDays = $reports->where('attendance_id', 3)->count(); // 3 = absent
            $attendancePercentage = $totalDays > 0 ? ($attendedDays / $totalDays) * 100 : 0;

            // Calculate Juz' progress
            $totalJuzCovered = $this->calculateTotalJuzCovered($reports);
            
            // Calculate average evaluation
            $evaluatedReports = $reports->whereNotNull('hefz_evaluation_id');
            $averageEvaluation = $evaluatedReports->count() > 0 ? 
                $evaluatedReports->avg('hefz_evaluation_id') : 0;

            // Total mistakes
            $totalMistakes = $reports->sum('hefz_mistakes');

            $data = [[
                'total_days' => $totalDays,
                'attended_days' => $attendedDays,
                'absent_days' => $absentDays,
                'attendance_percentage' => number_format($attendancePercentage, 1) . '%',
                'total_juz_covered' => $totalJuzCovered,
                'average_evaluation' => number_format($averageEvaluation, 1),
                'total_mistakes' => $totalMistakes,
                'period' => $startDate->format('M Y') . ' - ' . $endDate->format('M Y')
            ]];

            return DataTables::of($data)->toJson();

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function studentRecords(Request $request): JsonResponse
    {
        try {
            $studentId = (int)$request->input('studentId');
            $monthYear = $request->input('monthYear');

            // If parameters missing, return empty dataset to avoid 400 errors during initial table setup
            if (!$studentId || !$monthYear) {
                return DataTables::of([])->toJson();
            }

            // Parse month and year from monthYear (format: "Dec 2024")
            $date = Carbon::createFromFormat('M Y', $monthYear);
            $month = $date->month;
            $year = $date->year;

            // Get student reports for the specified month/year
            $reports = StudentIjazasanadMemorizationReport::where('student_id', $studentId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereNotNull('hefz_from_surat')
                ->whereNotNull('hefz_from_ayat')
                ->whereNotNull('hefz_to_surat')
                ->whereNotNull('hefz_to_ayat')
                ->with('ijazasanadMemorizationPlan')
                ->orderBy('created_at', 'desc')
                ->get();

            $data = [];
            foreach ($reports as $index => $report) {
                $data[] = [
                    'DT_RowIndex' => $index + 1,
                    'date' => $this->formatDateWithLink($report->created_at, $request->input('classId')),
                    'day' => $report->created_at->format('D'),
                    'from_surah' => $this->getSurahDisplayName($report->hefz_from_surat),
                    'from_ayat' => $report->hefz_from_ayat ?? '—',
                    'to_surah' => $this->getSurahDisplayName($report->hefz_to_surat),
                    'to_ayat' => $report->hefz_to_ayat ?? '—',
                    'juz_covered' => $this->calculateJuzCovered($report),
                    'pages_memorized' => $report->pages_memorized ?? 0,
                    'attendance' => $this->getAttendanceDisplay($report->attendance_id),
                    'evaluation' => $this->getEvaluationDisplay($report->ijazasanad_evaluation_id ?? $report->hefz_evaluation_id),
                    'mistakes' => $report->hefz_mistakes ?? 0,
                    'teacher_comments' => $report->ijazasanad_evaluation_note ?? $report->hefz_evaluation_note ?? '—',
                    'performance' => $this->getEvaluationDisplay($report->ijazasanad_evaluation_id ?? $report->hefz_evaluation_id),
                ];
            }

            return DataTables::of($data)
                ->rawColumns(['date', 'attendance', 'evaluation', 'from_surah', 'to_surah', 'teacher_comments', 'performance'])
                ->toJson();

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    private function getSurahName(?int $surahId): string
    {
        if (!$surahId) {
            return '—';
        }

        $surahName = MoshafSurah::where('id', $surahId)->value('eng_name');
        return $surahName ?? '—';
    }

    private function calculateJuzCovered($report): string
    {
        if (!$report->hefz_from_surat || !$report->hefz_to_surat) {
            return '—';
        }

        // Simplified Juz calculation - you may need to implement a more accurate calculation
        $fromJuz = $this->getJuzFromSurahAyat($report->hefz_from_surat, $report->hefz_from_ayat);
        $toJuz = $this->getJuzFromSurahAyat($report->hefz_to_surat, $report->hefz_to_ayat);

        if ($fromJuz && $toJuz) {
            if ($fromJuz == $toJuz) {
                return "Juz' {$fromJuz}";
            } else {
                return "Juz' {$fromJuz}-{$toJuz}";
            }
        }

        return '—';
    }

    private function getJuzFromSurahAyat(?int $surahId, ?int $ayat): ?int
    {
        if (!$surahId || !$ayat) {
            return null;
        }
        return (int)ceil($surahId / 4);
    }

    private function calculateTotalJuzCovered($reports): int
    {
        $uniqueJuzSet = collect();

        foreach ($reports as $report) {
            if ($report->hefz_from_surat && $report->hefz_to_surat) {
                $fromJuz = $this->getJuzFromSurahAyat($report->hefz_from_surat, $report->hefz_from_ayat);
                $toJuz = $this->getJuzFromSurahAyat($report->hefz_to_surat, $report->hefz_to_ayat);
                
                if ($fromJuz && $toJuz) {
                    for ($juz = $fromJuz; $juz <= $toJuz; $juz++) {
                        $uniqueJuzSet->push($juz);
                    }
                }
            }
        }

        return $uniqueJuzSet->unique()->count();
    }

    private function getAttendanceStatus(?int $attendanceId): string
    {
        switch ($attendanceId) {
            case 1:
                return '<span class="label label-warning">Late</span>';
            case 2:
                return '<span class="label label-success">On Time</span>';
            case 3:
                return '<span class="label label-danger">Absent</span>';
            default:
                return '<span class="label label-default">Unknown</span>';
        }
    }

    private function getEvaluationStatus(?int $evaluationId): string
    {
        switch ($evaluationId) {
            case 1:
                return '<span class="label label-danger">Poor</span>';
            case 2:
                return '<span class="label label-warning">Fair</span>';
            case 3:
                return '<span class="label label-info">Good</span>';
            case 4:
                return '<span class="label label-success">Very Good</span>';
            case 5:
                return '<span class="label label-primary">Excellent</span>';
            default:
                return '<span class="label label-default">Not Evaluated</span>';
        }
    }

    private function getSurahDisplayName(?int $surahId): string
    {
        if (!$surahId) {
            return '—';
        }

        $surah = \App\MoshafSurah::where('id', $surahId)->first();
        if (!$surah) {
            return '—';
        }

        return "{$surah->id}. {$surah->eng_name}";
    }

    private function getAttendanceDisplay(?int $attendanceId): string
    {
        if (!$attendanceId) {
            return '<span class="ui label grey">—</span>';
        }

        $attendance = AttendanceOption::where('id', $attendanceId)->first();
        if (!$attendance) {
            return '<span class="ui label grey">—</span>';
        }

        $colorMap = [
            1 => 'orange', // Late
            2 => 'green',  // On time
            3 => 'red'     // Absent
        ];

        $color = $colorMap[$attendanceId] ?? 'grey';
        return "<span class='ui label {$color}'>{$attendance->title}</span>";
    }

    private function getEvaluationDisplay(?int $evaluationId): string
    {
        if (!$evaluationId) {
            return '<span class="ui label grey">—</span>';
        }

        $evaluation = \App\EvaluationSchemaOption::where('id', $evaluationId)->first();
        if (!$evaluation) {
            return '<span class="ui label grey">—</span>';
        }

        // Color based on evaluation level
        $colorMap = [
            1 => 'red',    // Poor
            2 => 'orange', // Fair
            3 => 'yellow', // Good
            4 => 'green',  // Very Good
            5 => 'blue'    // Excellent
        ];

        $color = $colorMap[$evaluationId] ?? 'grey';
        return "<span class='ui label {$color}'>{$evaluation->title}</span>";
    }

    private function formatDateWithLink($date, $classId): string
    {
        $formattedDate = $date->format('Y-m-d');
        
        // Ensure classId is valid
        if (!$classId) {
            return $formattedDate; // Return plain date if no class ID
        }
        
        $createReportUrl = route('reports.create', [
            'id' => $classId,
            'from_date' => $formattedDate
        ]);
        
        return '<a href="' . $createReportUrl . '" target="_blank" style="color: #28a745 !important; text-decoration: none !important; font-weight: 500 !important; border-bottom: 1px dotted #28a745 !important;" title="Create daily report for ' . $formattedDate . '">' . $formattedDate . '</a>';
    }
}

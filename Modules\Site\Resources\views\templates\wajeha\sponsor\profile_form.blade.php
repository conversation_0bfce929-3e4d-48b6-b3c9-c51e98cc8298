<div class="col-md-12">
    <!-- <h3>{{ucfirst(trans('registration'))}}</h3> -->
@if($student->status == null || $student->status == "update_profile")
    <ul class="process-steps nav nav-justified">

        <li class="active">
            <a href="#"><i class="fa fa-user"></i></a>
            <h5>{{ trans('common.personal_info') }}</h5>
        </li>
    </ul>

    <h3>{{ trans('common.update_profile_data') }}</h3>
    <div class="well">
    {!! Form::model($student, [
        'method' => 'PATCH',
        'route' => ['students.update', $student->id],
        'class' => 'form-horizontal',
        'files' => true
    ]) !!}
    {!! Form::hidden('update_profile', 1) !!}

    @include('forms.student.profile')
    {!! Form::close() !!}
    </div>

@elseif(config('settings.student_form_guardian') && $student->status == "update_guardian")
    <ul class="process-steps nav nav-justified">
        <li class="active">
            <a href="#"><i class="fa fa-users"></i></a>
            <h5>{{ trans('common.guardian_info') }}</h5>
        </li>
    </ul>

    <h3>{{ trans('common.update_guardian_data') }}</h3>
    <div class="well">
        {!! Form::model($student, [
        'method' => 'POST',
        'route' => ['guardian.add'],
        'class' => 'form-horizontal',
        'files' => true
    ]) !!}
    {!! Form::hidden('add_student_guardian', 1) !!}

    @include('forms.guardian.profile')

    {!! Form::close() !!}
    
    </div>
    @elseif($student->status == "profile_completed")
    <ul class="process-steps nav nav-justified">
        <li class="active">
            <a href="#"><i class="icon-ok"></i></a>
            <h5>{{ trans('common.program_registration') }}</h5>
        </li>
    </ul>
    <h3>{{ trans('common.program_registration') }}</h3>
    <div class="well">

    @include('forms.admission.application')
    
    </div>
    @elseif($student->status == "new_admission")
    <div class="well">
        @if($student->current_admission->status == "offered")
        <div class="alert alert-success">
            Congratulations, You have an offer to register in the program with the following details
        </div>
        @elseif($student->current_admission->status == "rejected")
        <div class="alert alert-danger">
            Sorry, Your application to register  the program is rejected
        </div>
        @elseif($student->current_admission->status == "waiting_for_interview")
        <div class="alert alert-warning">
            An Interview appointment has been set, Please check the details bellow
        </div>
        @else
        <div class="alert alert-warning">
            Your application is still under process
        </div>
        @endif
        <h3>Admission Details</h3>
        <table class="table table-responsive table-stripped">
            <tr>
                <td>Center</td>
                <td>{{ $student->current_admission->center->name }}</td>
            </tr>
            <tr>
                <td>Class</td>
                <td>{{ $student->current_admission->class->name }}</td>
            </tr>
            <tr>
                <td>Program</td>
                <td>
                    @foreach($student->current_admission->programs as $program)
                    <div>
                        {{ $program->title }}                            
                    </div>
                    @endforeach
                </td>
                <tr>
                    <td>Status</td>
                    <td>{{ $student->current_admission->status }}</td>
                </tr>
            </tr>
            
        </table>
        @if($student->current_admission->status == "waiting_for_interview")
        <h3>Interview Details</h3>
        @foreach($student->current_admission->interviews as $interview)
        <table class="table table-responsive table-stripped">
            <tr>
                <td>Program</td>
                <td>{{ $interview->program->title }}</td>
            </tr>
            <tr>
                <td>Date & Time</td>
                <td>{{ $interview->interview_time }}</td>
            </tr>
            <tr>
                <td>Location</td>
                <td>{{ $interview->location }}</td>
            </tr>
        </table>
        @endforeach
        @elseif($student->current_admission->status == "offered")
        <div class="text-center">
            {!! Form::open(['url' => route('admission.offer_response')]) !!}
            {!! Form::hidden('admission_id' , $student->current_admission->id ) !!}             
            <button name="offer_response" value="1" type="submit"  class="btn btn-success">Accept</button>
            <button name="offer_response" value="0" type="submit"  class="btn btn-danger">Reject</button>
            {!! Form::close() !!}
        </div>
        @elseif($student->current_admission->status == "waiting_for_payment" || $student->current_admission->status == "payment_proof_not_valid")
        <h3>Payment Details</h3>
        {!! Form::open(['url' => route('admission.upload_payment_proof') , 'files' => true]) !!}        
        {!! Form::hidden('admission_id' , $student->current_admission->id ) !!} 
        
        @if(count($errors->all()))
        <div class="alert alert-danger">
            @foreach($errors->all() as $error)
            <p>{{ $error }}</p>
            @endforeach
        </div>
        @endif
        <table class="table table-responsive table-stripped">
            <tr>
                <td>Required Fees Amount</td>
                <td>RM**</td>
            </tr>
            <tr>
                <td>Payment Method</td>
                <td> 
                        <input type="radio" name="payment_method" id="" checked> Upload Proof of Payment/Receipt/Payslip <br>
                        <input type="radio" name="payment_method" id="" disabled> Online Banking <br>
                        <input type="radio" name="payment_method" id="" disabled> Visa/Master  <br>
                </td>
            </tr>
            <tr>
                <td>Paid Amount</td>
                <td>
                    {!! Form::number('paid_amount' , null , ['class' => 'form-control' ,'step' => '.01'  , 'required']) !!}
                    {!! $errors->first('paid_amount', '
                    <p class="help-block alert-danger">
                        :message
                    </p>
                    ') !!}
                </td>
            </tr>
            <tr>
                <td>Proof of Payment</td>
                <td>
                    {!! Form::file('payment_proof' , null , ['class' => 'form-control' , 'required' ]) !!}
                    {!! $errors->first('payment_proof', '
                    <p class="help-block alert-danger">
                        :message
                    </p>
                    ') !!}
                </td>
            </tr>
            <tr>
                <td colspan="2" class="text-center">
                    {!! Form::submit('Upload'  , ['class' => 'btn btn-success']) !!}
                </td>

            </tr>
        </table>
        {!! Form::close() !!}
        @endif
    </div>
    @endif
 
</div>
<?php

namespace App\Exports;

use App\Employee;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class EmployeesExport implements FromCollection, WithHeadings, WithMapping
{
    /**
     * Return the collection of employees.
     */
    public function collection()
    {
        return Employee::with('teacherCenter','classes','roles')->orderBy('full_name','asc')->get();
    }

    /**
     * Define the headings for the columns.
     */
    public function headings(): array
    {
        return [
            'Full Name',
            'Roles',
            'Identification or passport number',
            'Email',
            'Telephone number',
            'Address',
            'Center',
            'Class',

        ];
    }
    /**
     * Map each employee to the corresponding row in the Excel sheet.
     */
    public function map($employee): array
    {
        $center = $employee->hasAnyRole(['supervisor_2_', 'teacher_2_']) ? $employee->teacherCenter->first()->name : null;

        // Determine if the class should be displayed based on the user's role
        $class = $employee->hasRole('teacher_2_') ? $employee->classes->first()->name: null;
        return [
            $employee->full_name,
            $employee->roles->pluck('description')->sort()->implode(', '),
            $employee->identity_number,
            $employee->email,
            $employee->mobile,
            $employee->address,
            $center,
            $class,
        ];
    }

}


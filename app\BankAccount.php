<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BankAccount extends Model
{


    use SoftDeletes;


    protected $fillable = [
        'bank_name',
        'account_name',
        'account_number',
        'account_type_id',
        'bank_id',
        'emp_id',
        'note',
        'active_status',
        'organization_id'
    ];


    public function employee(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {


        return $this->belongsTo(Employee::class,'emp_id');
    }
    public function bank()
    {
        return $this->belongsTo(Bank::class);
    }
    public function bankAccountType()
    {
        return $this->belongsTo(BankAccountType::class,'account_type_id');
    }



}

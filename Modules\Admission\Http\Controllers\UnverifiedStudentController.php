<?php

namespace Modules\Admission\Http\Controllers;


use App\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

class UnverifiedStudentController extends Controller
{


    public function __invoke(Request $request)
    {


        DB::connection()->enableQueryLog();

        if ($request->ajax()) {
            try {


                $unverifiedUsersQuery = User::whereNull('email_verified_at')->orderBy('created_at', 'desc')->get();


                $trxDatatables = $unverifiedUsersQuery;


                return \Yajra\DataTables\DataTables::of($trxDatatables)
                    ->addIndexColumn()
                    ->addColumn('created_at', function ($row) {

                        return value($row->created_at)->diffForHumans();
//                    return $row->created_at->diffForHumans();
                    })
                    ->addColumn('action', function ($row) use ($request) {



                        $input = '  <br>

<button  
 data-stname="' . $row->full_name . '" 
 data-userid="' . $row->id . '" 
 data-center="' . $row->center . '" 
 data-class="' . $row->className . '" 
 data-programid="' . $row->program_id . '" 
 data-centerid="' . $row->center_id . '" 
 data-classid="' . $row->class_id . '" 
 class="ui button unverifiedStatusBtn "
 id="unverifiedStatusCheckbox "  
 data-toggle="modal" 
    data-target="#studentVerifyConfirmation">
    Verify
                               </button>
                               
                               
                             ';

//                        <div class="ui left floated compact segment">
//                                  <div class="ui fitted toggle checkbox">
//                                    <input data-stname="' . $row->full_name . '" data-userid="' . $row->id . '" class="archivedStatusCheckbox" id="unverifiedStatusCheckbox" type="checkbox">
//                                    <label></label>
//                                  </div>
//                                </div>
                        return $input;
                    })
                    ->rawColumns(['action'])
                    ->make(true);

            }
            catch
            (Exception $e) {

//                Toastr::error('Operation Failed', 'Failed');
                return response()->json($e->getMessage());
            }
}


    }


}
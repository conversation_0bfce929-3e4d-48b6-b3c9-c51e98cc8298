<?php

namespace Modules\ExaminationCertification\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;


/**
 * Class ClassReportStoreRequest
 * @package Modules\Communicate\Http\Requests
 *
 *
 */
class ClassTeacherTimetableRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
//            'teacher_id' => 'required|exists:employees,id',
            'class_id' => 'required|exists:classes,id',
//            'subject_id' => 'required',
            'mon' => 'nullable|string',
            'tue' => 'nullable|string',
            'wed' => 'nullable|string',
            'thu' => 'nullable|string',
            'fri' => 'nullable|string',
            'sat' => 'nullable|string',
            'sun' => 'nullable|string',
            'duration' => 'required|integer',
//            'class_teacher_subject_id' => 'nullable|exists:class_teacher_subjects,id',
//            'class_teacher_id' => 'required|exists:class_teachers,id',
//            'subj_id' => 'required',
            'start_at' => 'required|date',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return [
            'start_at.required' => 'Please specify the start date for the class',
//            'id.required' => 'Please select a role',
//            'id.exists' => 'Role does not exists'



        ];
    }
}

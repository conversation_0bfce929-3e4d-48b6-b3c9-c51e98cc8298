<?php

namespace Modules\EducationalReports\Http\Controllers;


use App\Classes;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;


class ClassTeachersController extends Controller
{

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function getTeachers($classId)
    {
        $class = Classes::findOrFail($classId);

        // Get the teachers associated with the class
        $teachers = $class->teachers;

        // Get the teachers associated with the class
        $teachers = $class->teachers->pluck('name');

        // Return the teacher names as a JSON response
        return \Illuminate\Support\Facades\Response::json($teachers);
    }

}

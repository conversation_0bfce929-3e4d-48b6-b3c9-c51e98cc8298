<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Translatable;
use App\Scopes\OrganizationScope;


class Subject extends Model
{
    use Translatable;
    
    public $translatedAttributes = array('title');
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'subjects';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['organization_id', 'preface', 'language', 'status'];

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
    }

    public function contents(){
        return $this->belongsToMany('App\Content' , 'subject_content')->orderBy('content_order', 'asc');
    }
    
    public function program_levels()
    {
        return $this->belongsToMany('App\ProgramLevel' , 'program_level_subjects' , 'subject_id','program_level_id');

    }


    public function classes()
    {
        return $this->hasMany('App\Classes' , 'subject_id');

    }


}

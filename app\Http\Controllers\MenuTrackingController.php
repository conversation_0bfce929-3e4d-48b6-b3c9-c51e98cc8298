<?php

namespace App\Http\Controllers;

use App\Models\MenuUsageStatistic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MenuTrackingController extends Controller
{
    /**
     * Track menu usage
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function trackMenuUsage(Request $request)
    {
        try {
            // Log the start of the request
            Log::info('Menu tracking request received', [
                'endpoint' => 'trackMenuUsage',
                'method' => $request->method(),
                'url' => $request->fullUrl(),
                'auth_check' => auth()->check() ? 'authenticated' : 'unauthenticated'
            ]);
            
            // Debug auth guards
            $guards = ['web', 'student', 'guardian', 'employee', 'superior', 'organization'];
            $authStatus = [];
            $detectedUserId = null;
            
            foreach ($guards as $guard) {
                $isAuthenticated = auth($guard)->check();
                $guardUserId = $isAuthenticated ? auth($guard)->id() : null;
                $authStatus[$guard] = [
                    'authenticated' => $isAuthenticated,
                    'user_id' => $guardUserId
                ];
                
                // Save the first valid user ID we find
                if ($isAuthenticated && $guardUserId && !$detectedUserId) {
                    $detectedUserId = $guardUserId;
                }
            }
            
            Log::info('Auth guard status', ['guards' => $authStatus, 'detected_user_id' => $detectedUserId]);
            
            // Validate request data
            $validated = $request->validate([
                'menu_title' => 'required|string|max:255',
                'menu_url' => 'required|string|max:255',
                'parent_menu' => 'nullable|string|max:255',
            ]);
            
            // Try all possible methods to get the user ID
            $userId = null;
            
            // Method 1: Default auth
            if (auth()->check()) {
                $userId = auth()->id();
                Log::info('Got user ID from default auth', ['user_id' => $userId]);
            }
            
            // Method 2: Use the detected user ID from guards check
            if (!$userId && $detectedUserId) {
                $userId = $detectedUserId;
                Log::info('Got user ID from auth guards check', ['user_id' => $userId]);
            }
            
            // Method 3: Try to get from session directly
            if (!$userId && $request->session()->has('login_user_id')) {
                $userId = $request->session()->get('login_user_id');
                Log::info('Got user ID from session login_user_id', ['user_id' => $userId]);
            }
            
            // Method 4: Check for login.id which is common in Laravel session auth
            if (!$userId && $request->session()->has('login.id')) {
                $userId = $request->session()->get('login.id');
                Log::info('Got user ID from session login.id', ['user_id' => $userId]);
            }
            
            // Method 5: Check common Laravel auth session keys
            $sessionKeys = [
                'login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d',  // Default Laravel remember token
                'login_employee_59ba36addc2b2f9401580f014c7f58ea4e30989d',
                'login_student_59ba36addc2b2f9401580f014c7f58ea4e30989d',
                'login_guardian_59ba36addc2b2f9401580f014c7f58ea4e30989d',
                'login_superior_59ba36addc2b2f9401580f014c7f58ea4e30989d',
                'login_organization_59ba36addc2b2f9401580f014c7f58ea4e30989d'
            ];
            
            foreach ($sessionKeys as $key) {
                if (!$userId && $request->session()->has($key)) {
                    $sessionUserId = $request->session()->get($key);
                    if (is_numeric($sessionUserId)) {
                        $userId = $sessionUserId;
                        Log::info("Got user ID from session key: {$key}", ['user_id' => $userId]);
                        break;
                    }
                }
            }
            
            // Method 6: Check if we can get the user from request()->user()
            if (!$userId && $request->user()) {
                $userId = $request->user()->id;
                Log::info('Got user ID from request()->user()', ['user_id' => $userId]);
            }
            
            // Method 7: Last resort - inspect all session data and look for User ID keys
            if (!$userId) {
                // Get all session data
                $sessionData = $request->session()->all();
                Log::info('All session data keys', ['keys' => array_keys($sessionData)]);
                
                // First, look for direct auth keys
                $authKeys = [
                    'auth_user_id', 
                    'auth_employee_id', 
                    'auth_student_id',
                    'auth_guardian_id',
                    'auth_superior_id', 
                    'auth_organization_id',
                    'user_id',
                    'employee_id',
                    'student_id',
                    'guardian_id',
                    'superior_id',
                    'organization_id'
                ];
                
                foreach ($authKeys as $key) {
                    if (isset($sessionData[$key]) && is_numeric($sessionData[$key])) {
                        $userId = $sessionData[$key];
                        Log::info("Found user ID in session key: {$key}", ['user_id' => $userId]);
                        break;
                    }
                }
                
                // If still no userId, check for login user session data
                if (!$userId) {
                    foreach ($sessionData as $key => $value) {
                        // Look for a login session key
                        if (strpos($key, 'login_') === 0 && is_numeric($value)) {
                            $userId = $value;
                            Log::info("Found user ID in login session key: {$key}", ['user_id' => $userId]);
                            break;
                        }
                    }
                }
                
                // If still no userId, check for 'auth' session data
                if (!$userId && isset($sessionData['auth'])) {
                    $auth = $sessionData['auth'];
                    if (is_array($auth) && isset($auth['id']) && is_numeric($auth['id'])) {
                        $userId = $auth['id'];
                        Log::info("Found user ID in auth array", ['user_id' => $userId]);
                    }
                }
            }
            
            // Final fallback: Get from REMOTE_USER if using SSO
            if (!$userId && isset($_SERVER['REMOTE_USER'])) {
                // This might need to be mapped to a real user ID in your system
                Log::info("Found REMOTE_USER in server vars", ['remote_user' => $_SERVER['REMOTE_USER']]);
                // You may need to lookup the user ID from your database based on this value
                // $userId = User::where('username', $_SERVER['REMOTE_USER'])->value('id');
            }
            
            // Check if user_id was directly sent from JavaScript
            if (!$userId && $request->filled('user_id')) {
                $userId = $request->input('user_id');
                Log::info('Using user ID from request parameter', ['user_id' => $userId]);
            }
            
            // Final attempt: Query sessions table directly
            if (!$userId) {
                try {
                    $sessionId = $request->session()->getId();
                    // Try to get the user ID from the sessions table
                    $sessionRecord = DB::table('sessions')
                        ->where('id', $sessionId)
                        ->first();
                    
                    if ($sessionRecord && isset($sessionRecord->user_id) && $sessionRecord->user_id) {
                        $userId = $sessionRecord->user_id;
                        Log::info('Got user ID from sessions table', ['user_id' => $userId]);
                    }
                } catch (\Exception $e) {
                    Log::error('Error accessing sessions table', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }
            
            // Log the final user ID determination
            Log::info('Final user ID determination', [
                'user_id' => $userId ?? 'null',
                'auth_state' => auth()->check() ? 'authenticated' : 'unauthenticated',
                'session_id' => $request->session()->getId()
            ]);
            
            // For better tracking, ensure we're using a consistent session
            $sessionId = $request->session()->getId();
            
            // Query based on matching conditions
            $query = MenuUsageStatistic::query()
                ->where('menu_url', $validated['menu_url'])
                ->where('session_id', $sessionId);
            
            // If we have a user ID, add it to the query
            if ($userId) {
                $query->where(function($q) use ($userId, $sessionId) {
                    $q->where('user_id', $userId)
                      ->orWhere('session_id', $sessionId);
                });
            }
            
            $menuStat = $query->first();
            
            if ($menuStat) {
                // Update existing record
                $menuStat->access_count += 1;
                $menuStat->last_accessed_at = now();
                $menuStat->ip_address = $request->ip();
                $menuStat->user_agent = $request->userAgent();
                // Always ensure user_id is set if available
                if ($userId && !$menuStat->user_id) {
                    $menuStat->user_id = $userId;
                    Log::info('Updated user_id on existing record', ['record_id' => $menuStat->id, 'user_id' => $userId]);
                }
                
                // Update the menu title and parent menu in case they were incorrect before
                $menuStat->menu_title = $validated['menu_title'];
                $menuStat->parent_menu = $validated['parent_menu'];
                
                $menuStat->save();
                
                Log::info('Menu record updated', [
                    'id' => $menuStat->id, 
                    'user_id' => $menuStat->user_id, 
                    'menu_title' => $menuStat->menu_title,
                    'parent_menu' => $menuStat->parent_menu,
                    'session' => $sessionId,
                    'url' => $validated['menu_url']
                ]);
            } else {
                // Create new record
                $menuStat = new MenuUsageStatistic();
                $menuStat->user_id = $userId; // Use the determined user ID
                $menuStat->menu_title = $validated['menu_title'];
                $menuStat->menu_url = $validated['menu_url'];
                $menuStat->parent_menu = $validated['parent_menu'];
                $menuStat->access_count = 1;
                $menuStat->last_accessed_at = now();
                $menuStat->ip_address = $request->ip();
                $menuStat->user_agent = $request->userAgent();
                $menuStat->session_id = $sessionId;
                $menuStat->save();
                
                Log::info('New menu record created', [
                    'id' => $menuStat->id, 
                    'user_id' => $userId, 
                    'menu_title' => $validated['menu_title'],
                    'parent_menu' => $validated['parent_menu'],
                    'session' => $sessionId,
                    'url' => $validated['menu_url']
                ]);
            }
            
            return response()->json(['success' => true, 'message' => 'Menu access logged successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to log menu access: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
                'auth_check' => auth()->check() ? 'authenticated' : 'unauthenticated',
                'user_id' => auth()->check() ? auth()->id() : 'not authenticated'
            ]);
            return response()->json(['success' => false, 'message' => 'Failed to log menu access: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get most accessed menus
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMostAccessedMenus(Request $request)
    {
        $limit = $request->input('limit', 10);
        $userId = auth()->id();
        
        if (!$userId) {
            return response()->json(['success' => false, 'message' => 'User not authenticated'], 401);
        }
        
        $menus = MenuUsageStatistic::where('user_id', $userId)
            ->orderBy('access_count', 'desc')
            ->limit($limit)
            ->get();
            
        return response()->json(['success' => true, 'data' => $menus]);
    }

    /**
     * Get recently accessed menus
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRecentMenus(Request $request)
    {
        $limit = $request->input('limit', 10);
        $userId = auth()->id();
        
        if (!$userId) {
            return response()->json(['success' => false, 'message' => 'User not authenticated'], 401);
        }
        
        $menus = MenuUsageStatistic::where('user_id', $userId)
            ->orderBy('last_accessed_at', 'desc')
            ->limit($limit)
            ->get();
            
        return response()->json(['success' => true, 'data' => $menus]);
    }
} 
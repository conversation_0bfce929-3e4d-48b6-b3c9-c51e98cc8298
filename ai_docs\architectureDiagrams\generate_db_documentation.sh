#!/bin/bash

# Change to the directory containing the script
cd "$(dirname "$0")"

# Check if PHP is installed
if ! command -v php &> /dev/null; then
    echo "Error: PHP is not installed or not in your PATH"
    exit 1
fi

# Check if the PHP script exists
if [ ! -f "generate_table_definitions.php" ]; then
    echo "Error: generate_table_definitions.php not found in the current directory"
    exit 1
fi

# Check if the template HTML file exists
if [ ! -f "complete_database_documentation.html" ]; then
    echo "Error: complete_database_documentation.html template not found in the current directory"
    exit 1
fi

echo "=== Itqan Database Documentation Generator ==="
echo "This script will generate comprehensive HTML documentation for all database tables."
echo ""
echo "Before running, please ensure you have updated the database credentials in the PHP script."
echo ""
read -p "Continue? (y/n): " confirm

if [ "$confirm" != "y" ]; then
    echo "Operation cancelled."
    exit 0
fi

# Run the PHP script
echo "Generating documentation..."
php generate_table_definitions.php

# Check if the generation was successful
if [ $? -eq 0 ]; then
    echo "Documentation generation completed successfully."
    
    # Try to open the documentation in the default browser
    output_file="complete_database_documentation_with_tables.html"
    
    if [ -f "$output_file" ]; then
        echo "Attempting to open the documentation in your browser..."
        
        # Detect OS and open browser accordingly
        case "$(uname)" in
            "Darwin")  # macOS
                open "$output_file"
                ;;
            "Linux")
                if command -v xdg-open &> /dev/null; then
                    xdg-open "$output_file"
                else
                    echo "Could not auto-open the file. Please open it manually at:"
                    echo "$(pwd)/$output_file"
                fi
                ;;
            *)
                echo "Could not auto-open the file. Please open it manually at:"
                echo "$(pwd)/$output_file"
                ;;
        esac
    else
        echo "Warning: Output file not found."
    fi
else
    echo "Error: Documentation generation failed."
    exit 1
fi 
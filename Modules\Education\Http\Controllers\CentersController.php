<?php

namespace Modules\Education\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use App\Employee;
use App\Center;
use App\Cen_Emp;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;
use App\Cent_Emp;
use Illuminate\Support\Facades\Log;

class CentersController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $keyword = $request->get('search');
        if (!empty($keyword)) {
            $centers = Center::where('Name', 'LIKE', "%$keyword%")
                ->orWhere('Location', 'LIKE', "%$keyword%")
                ->orWhere('Status', 'LIKE', "%$keyword%")
                ->with('employee')
                ->orderBy('location')
                ->get();
        } else {

            $centers = Center::with('employee')->orderBy('location')->get();
        }


        return view('education::centers.index', compact('centers'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('education::centers.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'location' => 'required',
            'translate.*.name' => 'required'
        ]);

        $center = new Center;

        $center->organization_id = config('organization_id');

        $center->status = $request->status;

        $center->location = $request->location;

        foreach ($request->translate as $code => $translate) {
            $center->translateOrNew($code)->name = $translate['name'];
            $center->translateOrNew($code)->description = $translate['description'];
        }

        $center->save();

        Session::flash('flash_message', 'Center added!');

        return redirect('workplace/education/centers');
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $center = Center::findOrFail($id);

        return view('education::centers.show', compact('center'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {


        $cmid = null;
        $empname = null;
        $ce = Cen_Emp::where('cen_id', '=', $id)->first();
        $employees = Employee::all()->pluck('full_name', 'id');
        $cid = 0;
        $emp = Employee::whereHas('roles', function ($q) {
            return $q->where('name', 'like', 'supervisor_%_');
        })
//        ->where('id','=',auth()->user()->id)->first();
            ->where('id', '=', auth()->user()->id)->get();
        if ($ce != null) {
            $cid = $ce->emp_id;
            $em = Employee::where('id', '=', $cid)->first();
            $empname = $em->name;
            $cmid = $ce->id;
            $eid = $em->id;
            $emp = Employee::whereHas('roles', function ($q) {
                return $q->where('name', 'like', 'supervisor_%_');
            })
                ->where('id', '!=', $eid)->pluck('name', 'id');
        }


        $center = Center::findOrFail($id);

        return view('education::centers.edit', ["empname" => $empname], ["cmid" => $cmid])->with(compact('center'))->with(compact('ce'))->with(compact('emp'))->with(compact('employees'));
    }

    /**
     * Update the specified resource in storage.
     *emp
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {
        DB::beginTransaction();
        try {
            $center = Center::findOrFail($id);
            // Log the incoming request data
            Log::info('Center update request data:', [
                'center_id'    => $id,
                'supervisors'  => $request->get('centerSupervisors'),
                'request_data' => $request->all(),
            ]);

            // Update basic center info
            $center->status   = $request->status;
            $center->location = $request->location;
            $center->phone    = $request->phone;

            // Update translations
            foreach ($request->translate as $code => $translate) {
                $center->translateOrNew($code)->name        = $translate['name'];
                $center->translateOrNew($code)->description = $translate['description'];
            }
            $center->save();

            // Update supervisor relationships
            $supervisors = $request->get('centerSupervisors', []);
            if (!is_array($supervisors)) {
                $supervisors = [$supervisors];
            }
            Log::info('Syncing supervisors for center:', [
                'center_id'      => $id,
                'supervisor_ids' => $supervisors,
            ]);
            $center->employee()->sync($supervisors);

            DB::commit();

            Session::flash('flash_message', 'Center updated successfully!');
            return redirect()->back(); // goes back to the form origin

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Center update failed:', [
                'center_id' => $id,
                'error'     => $e->getMessage(),
                'trace'     => $e->getTraceAsString(),
            ]);

            Toastr::error('Center update failed: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Center::destroy($id);

        Session::flash('flash_message', 'Center deleted!');

        return redirect('workplace/education/centers');
    }


}

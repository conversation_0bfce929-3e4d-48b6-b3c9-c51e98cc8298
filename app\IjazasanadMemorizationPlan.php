<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class IjazasanadMemorizationPlan extends Model
{
    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'ijazasanad_memorization_plans';
    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];
    protected $appends = ['NooFPages'];

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    protected $fillable = [
        'updated_at',
        'class_id',
        'organization_id',
        'student_id',
        'class_report_id',
        'start_from_surat',
        'start_from_ayat',
        'to_surat',
        'to_ayat',
        'plan_year_and_month',
        'start_date',
        'end_date',
        'study_direction',
        'level_id',
        'center_id',
        'status',
        'approved_by',
        'talqeen_from_lesson',
        'talqeen_to_lesson',
        'revision_from_lesson',
        'revision_to_lesson',
        'jazariyah_from_lesson',
        'jazariyah_to_lesson',
        'seminars_from_lesson',
        'seminars_to_lesson',
        'supervisor_comment',
         'from_surat_juz_id',
        'to_surat_juz_id',
         'created_by',
        'updated_by',
        'delete_reason',
        'deleted_at',
    ];




    protected static function boot() {


        parent::boot();

        static::creating(function ($model) {
            $model->created_by = is_object(\Auth::guard('employee')->user()) ? \Auth::guard('employee')->user()->id : 1;
            $model->updated_by = NULL;
        });

        static::updating(function ($model) {
            $model->updated_by = is_object(\Auth::guard('employee')->user()) ? \Auth::guard('employee')->user()->id : 1;
        });
    }

    public function program_level()
    {
        return $this->belongsTo('App\ProgramLevel','level_id','id');
    }
    public function student_program_level()
    {
        return $this->belongsTo('App\StudentProgramLevel','level_id','id');
    }

    public function center()
    {
        return $this->belongsTo('App\Center');
    }

    public function creator(){

        return $this->belongsTo(Employee::class,'created_by');
    }

    public function updator(){

        return $this->belongsTo(Employee::class,'updated_by');
    }


    public function student(){

        return $this->belongsTo(Student::class);
    }


    public function teacher()
    {

        return $this->belongsTo(Employee::class,'teacher_id','id');
    }

    public function approver()
    {

        return $this->belongsTo(Employee::class,'approved_by','id');
    }


    public function getPagesMemorizedAttribute()
    {



        if ($this->study_direction == 'backward') {

            $memorizedNumberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                $this->start_from_surat,
                $this->start_from_ayat,
                $this->to_surat,
                $this->to_ayat
            ]);


            $memorizedNumberofPages = $memorizedNumberofPages[0]->numberofPagesSum;

        }

        else {
            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                $this->start_from_surat,
                $this->start_from_ayat,
                $this->to_surat,
                $this->to_ayat
            ]);

            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
            $memorizedNumberofPages = $results[0]->number_of_pages_sum;

        }


        $memorizedNumberofPages = isset($memorizedNumberofPages) ? $memorizedNumberofPages : 0;


        return $memorizedNumberofPages;

    }


    public function getNooFPagesAttribute (){




        $numberofPages = DB::select('CALL getNumberofPagesStudentHefzPlan(?, ?, ?, ?, ?)', [
            $this->start_from_surat,
            $this->start_from_ayat,
            $this->to_surat,
            $this->to_ayat,
            $this->to_ayat,
        ]);
        if (empty($numberofPages)) {
            // Handle the situation where no rows were found
            // For example, you can display an error message to the user


            return 0;
        } else {
            // Handle the situation where rows were found
            // For example, you can display the result to the user
            return $numberofPages[0]->pageCount;
        }



        $numberofPages =DB::select("select *,abs(first_page-last_page) as pageCount
         from
         (select id,page_number as first_page
                   from moshaf_pages
                   where (surah_id = :startSurahId and first_ayah <= :startAyah)
                   order by page_number desc
                   limit 1) A
                  INNER JOIN
    (select id,page_number as last_page
                   from moshaf_pages
                   where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                   order by page_number desc
                   limit 1) B
on A.id <> B.id", array(
            'startSurahId' => $this->start_from_surat,
            'startAyah' => $this->start_from_ayat,
            'lastSurahId' => $this->to_surat,
            'lastAyah' => $this->to_ayat,
            'lastAyah2' => $this->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
        ));





        return $numberofPages[0]->pageCount;

    }


    public function getNooFJuzu($hefz_plans){






        $numberofPages =DB::select("select *,abs(first_page-last_page) as pageCount
         from
         (select id,page_number as first_page
                   from moshaf_pages
                   where (surah_id = :startSurahId and first_ayah <= :startAyah)
                   order by page_number desc
                   limit 1) A
                  INNER JOIN
    (select id,page_number as last_page
                   from moshaf_pages
                   where (surah_id = :lastSurahId and (first_ayah <= :lastAyah or last_ayah <= :lastAyah2))
                   order by page_number desc
                   limit 1) B
on A.id <> B.id", array(
            'startSurahId' => $hefz_plans->start_from_surat,
            'startAyah' => $hefz_plans->start_from_ayat,
            'lastSurahId' => $hefz_plans->to_surat,
            'lastAyah' => $hefz_plans->to_ayat,
            'lastAyah2' => $hefz_plans->to_ayat, // You cannot use a named parameter marker of the same name more than once in a prepared statement, unless emulation mode is on.
        ));





        $hefz_plans->no_of_pages = $numberofPages[0]->pageCount;

    }


    public function fromSurat(){
        return $this->belongsTo(MoshafSurah::class,'start_from_surat','id');
    }

    public function toSurat(){
        return $this->belongsTo(MoshafSurah::class,'to_surat','id');
    }


    public function halaqah()
    {
        // Alias kept for legacy references where plans were accessed as a halaqah
        return $this->belongsTo(Classes::class, 'class_id', 'id');
    }

    /**
     * Standard relation expected by new exports: `$plan->class`.
     */
    public function class()
    {
        return $this->belongsTo(Classes::class, 'class_id', 'id');
    }










}

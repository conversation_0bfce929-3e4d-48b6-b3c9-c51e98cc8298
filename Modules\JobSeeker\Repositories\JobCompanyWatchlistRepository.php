<?php

namespace Modules\JobSeeker\Repositories;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Mo<PERSON>les\JobSeeker\Entities\JobCompanyWatchlist;
use Mo<PERSON>les\JobSeeker\Entities\JobCompanyWatchlistCount;
use Illuminate\Support\Str;

class JobCompanyWatchlistRepository
{
    /**
     * Get all company watchlist entries for a user
     *
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllForUser($userId)
    {
        Log::info("Getting all company watchlist entries for user ID: {$userId}");
        return JobCompanyWatchlist::where('user_id', $userId)
            ->where('is_active', true)
            ->orderBy('company_name')
            ->get();
    }

    /**
     * Find a company watchlist entry by ID and user ID
     *
     * @param int $id
     * @param int $userId
     * @return JobCompanyWatchlist|null
     */
    public function findByIdAndUser($id, $userId)
    {
        Log::info("Finding company watchlist entry with ID: {$id} for user ID: {$userId}");
        return JobCompanyWatchlist::where('id', $id)
            ->where('user_id', $userId)
            ->first();
    }

    /**
     * Find if a company is already in a user's watchlist
     *
     * @param string $companyName
     * @param int $userId
     * @return JobCompanyWatchlist|null
     */
    public function findByCompanyAndUser($companyName, $userId)
    {
        Log::info("Checking if company '{$companyName}' is in user {$userId}'s watchlist");
        
        // Try exact match first
        $watchlist = JobCompanyWatchlist::where('company_name', $companyName)
            ->where('user_id', $userId)
            ->first();
            
        if (!$watchlist) {
            // Try with LIKE query as fallback
            $watchlist = JobCompanyWatchlist::where('company_name', 'like', "%{$companyName}%")
                ->where('user_id', $userId)
                ->first();
        }
        
        return $watchlist;
    }

    /**
     * Create a new company watchlist entry
     *
     * @param array $data
     * @return JobCompanyWatchlist
     */
    public function create(array $data)
    {
        Log::info("Creating company watchlist entry", [
            'company_name' => $data['company_name'], 
            'user_id' => $data['user_id']
        ]);
        
        // Ensure slug is created if not provided
        if (!isset($data['slug']) && isset($data['company_name'])) {
            $data['slug'] = Str::slug($data['company_name']);
        }
        
        // Set is_active to true by default
        $data['is_active'] = $data['is_active'] ?? true;
        
        try {
            $watchlist = JobCompanyWatchlist::create($data);
            
            // Initialize count record
            $this->updateJobCount($watchlist->id);
            
            return $watchlist;
        } catch (\Exception $e) {
            Log::error("Error creating company watchlist entry: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update a company watchlist entry
     *
     * @param int $id
     * @param array $data
     * @return JobCompanyWatchlist
     */
    public function update($id, array $data)
    {
        Log::info("Updating company watchlist entry with ID: {$id}");
        
        try {
            $watchlist = JobCompanyWatchlist::findOrFail($id);
            
            // Update slug if company_name changed
            if (isset($data['company_name']) && $data['company_name'] !== $watchlist->company_name) {
                $data['slug'] = Str::slug($data['company_name']);
            }
            
            $watchlist->update($data);
            
            // Re-calculate job count if company name changed
            if (isset($data['company_name']) && $data['company_name'] !== $watchlist->company_name) {
                $this->updateJobCount($id);
            }
            
            return $watchlist->fresh();
        } catch (\Exception $e) {
            Log::error("Error updating company watchlist entry: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a company watchlist entry
     *
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        Log::info("Deleting company watchlist entry with ID: {$id}");
        
        try {
            $watchlist = JobCompanyWatchlist::findOrFail($id);
            return $watchlist->delete();
        } catch (\Exception $e) {
            Log::error("Error deleting company watchlist entry: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update the job count for a company watchlist entry
     *
     * @param int $watchlistId
     * @param int $days Number of days to look back for jobs
     * @return JobCompanyWatchlistCount
     */
    public function updateJobCount($watchlistId, $days = 60)
    {
        Log::info("Updating job count for watchlist ID: {$watchlistId}");
        
        try {
            $watchlist = JobCompanyWatchlist::findOrFail($watchlistId);
            $companyName = $watchlist->company_name;
            
            // Get count of jobs for this company in last X days
            $jobs = DB::table('jobs')
                ->where('company_name', 'like', "%{$companyName}%")
                ->where('publish_date', '>=', Carbon::now()->subDays($days))
                ->orderBy('publish_date', 'desc')
                ->get();
            
            $jobsCount = $jobs->count();
            $lastJobDate = $jobs->isNotEmpty() ? $jobs->first()->publish_date : null;
            
            Log::info("Found {$jobsCount} jobs for company '{$companyName}'", [
                'last_job_date' => $lastJobDate
            ]);
            
            // Create or update count record
            $count = JobCompanyWatchlistCount::updateOrCreate(
                [
                    'watchlist_id' => $watchlistId,
                    'date_checked' => Carbon::today()->format('Y-m-d')
                ],
                [
                    'jobs_count' => $jobsCount,
                    'last_job_date' => $lastJobDate
                ]
            );
            
            return $count;
        } catch (\Exception $e) {
            Log::error("Error updating job count for watchlist: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get recent jobs for a company watchlist entry
     *
     * @param int $watchlistId
     * @param int $days Number of days to look back
     * @return \Illuminate\Support\Collection
     */
    public function getRecentJobs($watchlistId, $days = 60)
    {
        Log::info("Getting recent jobs for watchlist ID: {$watchlistId}");
        
        try {
            $watchlist = JobCompanyWatchlist::findOrFail($watchlistId);
            $companyName = $watchlist->company_name;
            
            return DB::table('jobs')
                ->where('company_name', 'like', "%{$companyName}%")
                ->where('publish_date', '>=', Carbon::now()->subDays($days))
                ->orderBy('publish_date', 'desc')
                ->get();
        } catch (\Exception $e) {
            Log::error("Error getting recent jobs for watchlist: " . $e->getMessage());
            throw $e;
        }
    }
} 
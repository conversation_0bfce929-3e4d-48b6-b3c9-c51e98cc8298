<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Exceptions\UnauthorizedException;

/**
 * Enhanced RoleMiddleware that allows system viewers READ-ONLY access
 * CRITICAL: System viewers should NEVER be allowed to perform write operations
 */
final class EnhancedRoleMiddleware
{
    public function handle(Request $request, Closure $next, $role, $guard = null)
    {
        $authGuard = Auth::guard($guard);
        $user = $authGuard->user();

        if (!$user) {
            throw UnauthorizedException::notLoggedIn();
        }

        // CRITICAL SECURITY FIX: System viewers get read-only access
        if ($user->hasRole('system_viewer_' . config('organization_id') . '_')) {
            // Block ALL write operations for system viewers
            if (in_array($request->method(), ['POST', 'PUT', 'PATCH', 'DELETE'])) {
                throw UnauthorizedException::forRoles([$role]);
            }
            
            // Allow GET operations (read-only access)
            if ($request->method() === 'GET') {
                return $next($request);
            }
            
            // Block any other methods
            throw UnauthorizedException::forRoles([$role]);
        }

        // For managing directors, also allow bypass (existing logic)
        if ($user->hasRole('managing-director_' . config('organization_id') . '_')) {
            return $next($request);
        }

        $roles = is_array($role) ? $role : explode('|', $role);

        if (!$user->hasAnyRole($roles)) {
            throw UnauthorizedException::forRoles($roles);
        }

        return $next($request);
    }
} 
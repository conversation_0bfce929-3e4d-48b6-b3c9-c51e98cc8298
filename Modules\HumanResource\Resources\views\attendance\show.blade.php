@extends('layouts.hound')

@section('mytitle', 'Employees')

@section('content')

    <div class="row">
        <div class="col-md-12">
            <h3 class="modal-title">Attendence Report [{{ $current_month->copy()->addDays(10)->format('M/Y') }}]  {{ $employee->full_name }}
            <button type="button" class="btn btn-primary btn-sm hidden-print" onclick="window.print()"><i class="fa fa-print"></i></button>
                
                <select class="pull-right hidden-print" name="" id="" onchange="window.location = '{{ url('workplace/humanresource/attendance/'.$employee->id.'/')}}/'+this.value">
                    <option value="{{ $current_month->copy()->format('Y-m') }}">{{ $current_month->copy()->format('M/Y') }}</option>
                    @for($d = $first_working_month->copy(); $d <= Date('Y-m-d') ; $d->addMonth() )
                    <option value="{{ $d->format('Y-m') }}">{{ $d->format('M/Y') }}</option>
                    @endfor
                </select>
            </h3>
            <button class="btn btn-sm btn-danger pull-right toggleNote hidden-print">Show/Hide All Notes</button>
        </div>
    </div>

    <div class="result-set">
    {{--  {{ dd($attendance) }}  --}}
    <table class="table table-responsive table-bordered">
    @for($date = $current_month->copy() ; $date->lt($next_month)  ; $date->addDay())
        <tr 
        @if(in_array($date->format('D') , $weekend))
        class="alert-success"
        @endif
        >
            <td class="text-center">
                {{ $date->format('D') }}<br class="hidden-print">
                {{ $date->format('Y-m-d') }}           
            </td>
            @isset($attendance[$date->format('Y-m-d')])
            <td>
                @foreach($attendance[$date->format('Y-m-d')] as $key => $record)
                <div class="row p-5">
                    <div class="col-xs-8">
                        @isset($record['in'])
                        <div class="col-xs-6 attendance-record" >
                            <button type="button" class="btn btn-default btn-outline btn-xs col-md-12" title="" data-toggle="tooltip" data-placement="top"  data-original-title="{{ $record['in']->note }}">
                                <small class="label label-success">in</small>{{ $record['in']->clock->format('H:iA') }}@if($record['in']->note ) * @endif
                                <div class="clock-note">
                                        {{ $record['in']->note }}
                                </div>
                            </button>
                        </div>
                        @endisset

                        @isset($record['out'])
                        <div class="col-xs-6 attendance-record" data-note="">
                            <button type="button" class="btn btn-default btn-outline btn-xs col-md-12"  data-toggle="tooltip"  title=""  data-placement="top" data-original-title="{{ $record['out']->note }}">
                                <small class="label label-warning">out</small> {{ $record['out']->clock->format('H:iA') }} @if($record['out']->note ) * @endif
                                <div class="clock-note">
                                        {{ $record['out']->note }}
                                </div>

                            </button>
                        </div>
                        @endisset
                    </div>
                    <div class="col-xs-4">
                        @isset($record['duration_in_hours'])
                        <small class="label label-success">duration</small> {{ $record['duration_in_hours'] }}
                        @endisset
                    </div>
                </div>
                @endforeach
                @if($key == 'allowed_time')
                <div class="row alert-success">
                    <div class="col-xs-8 text-center">
                            On leave
                    </div>
                    <div class="col-xs-4">
                        <small class="label label-success">duration</small> {{ $record }}
                    </div>
                </div>
                @endif
            </td>

            @else
                @if(in_array($date->format('D') , $weekend))
                <td class="text-center">
                Weekend
                </td>
                @elseif(in_array($date->format('Y-m-d') , $public_holidays))
                <td class="text-center alert-info">
                Public Holiday
                </td>
                @else
                <td class="text-center alert-danger">
                Absent
                </td>
                @endif
 
            @endisset
           <td>
            </td>
        </tr>
    @endfor
    </table>
    </div>
    @if($current_month->format('Ym') < date('Ym'))
    <div class="row">
        <h2>Salary Report</h2>
        <table class="table">

            <tr>
                <td>Total Working hours</td>
                <td>{{ $total_working_hours }} Hours</td>
            </tr>
            <tr>
                <td>Total Required Working hours</td>
                <td>{{ $total_required_hours }} Hours</td>
            </tr>

        </table>
    </div>
    @endif

@endsection
@section('js')
<script>
$('.attendance-record').each(function(i ,el){
	var note = $(this).attr('data-note');
	if(note){
	 $(this).append('<small class="record-note label label-danger float-right">!</small>')
    }
})
$('.toggleNote').click(function () {  
    $('.clock-note').toggle();
})
</script>
@endsection
@section('css')
<style>
    .clock-note{
        display: none;
        width: 100%;
        background: #f3f3f3;
        border: #dff0d8 1px solid;
    }
</style>
@endsection
<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Http\Controllers;


use App\EvaluationSchemaOption;
use App\MoshafSurah;
use App\Student;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;


final class ClassWiseStudentRevisionReportDatatablesController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function studentRecords(Request $request)
    {
        DB::connection()->enableQueryLog();
        if ($request->filled('classDate')) {
            // Handle multiple class IDs (comma-separated string or array)
            $classIds = $request->get('classId');
            if (is_string($classIds)) {
                $classIds = array_map('intval', array_filter(explode(',', $classIds)));
            } elseif (!is_array($classIds)) {
                $classIds = [(int) $classIds];
            } else {
                $classIds = array_map('intval', $classIds);
            }
            
            // Validate class IDs
            $classIds = array_filter($classIds, function($id) {
                return $id > 0;
            });
            
            if (empty($classIds)) {
                return response()->json(['error' => 'No valid class IDs provided.'], 400);
            }
            
            $studentId = $request->get('studentId');
            $planYearMonth = Carbon::parse($request->get('classDate'))->format('Y-m');
            $year = Carbon::parse($request->get('classDate'))->year;
            $month = Carbon::parse($request->get('classDate'))->month;

            $studentDetails = Student::query()
                ->whereHas('revision_plans', function ($query) use ($month, $year, $classIds) {
                    $query->whereMonth('start_date', $month)
                        ->whereIn('class_id', $classIds)
                        ->whereYear('start_date', $year)
                        ->where('status','active');
                })
                ->with(['joint_classes' => function ($query) use ($classIds) {
                    $query->whereIn('class_id', $classIds);
                }])
                ->with(['revision' => function ($query) use ($year, $month, $classIds) {
                    $query->where(function ($q) use ($year, $month, $classIds) {
                        $q->whereYear('created_at', $year)
                            ->whereMonth('created_at', $month)
                            ->whereIn('class_id', $classIds)
                            ->whereNotNull('revision_from_surat')
                            ->whereNotNull('revision_from_ayat')
                            ->whereNotNull('revision_to_surat')
                            ->whereNotNull('revision_to_ayat');
                    });
                }])
                ->with(['revision_plans' => function ($query) use ($month, $year, $classIds) {
                    $query->whereMonth('created_at', $month)
                        ->whereYear('created_at', $year)
                        ->where('status','active')
                        ->whereIn('class_id', $classIds);
                }])
                ->withCount(['revision_plans' => function ($query) use ($month, $year, $classIds) {
                    $query->whereMonth('created_at', $month)
                        ->whereYear('created_at', $year)
                        ->where('status','active')
                        ->whereIn('class_id', $classIds);
                }])
                ->withCount(['revision as revision_count' => function ($query) use ($month, $year, $classIds) {
                    $start_date = Carbon::createFromDate($year, $month, 1); // first day of the month
                    $end_date = Carbon::createFromDate($year, $month, 1)->endOfMonth(); // last day of the month
                    $query->whereBetween('created_at', [$start_date, $end_date])
                        ->whereIn('class_id', $classIds);
                }]);

            // Apply student filter if provided
            if (!empty($studentId)) {
                if (is_array($studentId)) {
                    $studentDetails->whereIn('id', $studentId);
                } else {
                    $studentDetails->where('id', $studentId);
                }
            }

            $studentDetails = $studentDetails->get();

            // Ensure rows are grouped contiguously by class_id to avoid duplicate group headers
            $resolveClassId = static function ($student) use ($classIds) {
                // Re-use the exact same resolution logic applied in the DataTables columns
                $jointClass = $student->joint_classes->first();
                if ($jointClass && in_array($jointClass->id, $classIds, true)) {
                    return (int) $jointClass->id;
                }

                $revisionPlan = $student->revision_plans->first();
                if ($revisionPlan && $revisionPlan->class_id && in_array($revisionPlan->class_id, $classIds, true)) {
                    return (int) $revisionPlan->class_id;
                }

                foreach ($classIds as $classId) {
                    $hasJointClass    = $student->joint_classes->where('id', $classId)->isNotEmpty();
                    $hasRevisionPlan  = $student->revision_plans->where('class_id', $classId)->isNotEmpty();

                    if ($hasJointClass || $hasRevisionPlan) {
                        return (int) $classId;
                    }
                }

                return (int) $classIds[0]; // Safe fallback
            };

            // Sort collection by resolved class_id then student name to guarantee contiguous grouping
            $studentDetails = $studentDetails->sortBy(function ($student) use ($resolveClassId) {
                return sprintf('%05d_%s', $resolveClassId($student), $student->full_name);
            })->values();



            return \Yajra\DataTables\DataTables::of($studentDetails)
                ->addIndexColumn()
                ->addColumn('class_id', function ($student) use ($classIds) {
                    // 🎯 CRITICAL FIX: Ensure consistent class_id for grouping
                    // Priority 1: Use joint_classes relationship if available
                    $jointClass = $student->joint_classes->first();
                    if ($jointClass && in_array($jointClass->id, $classIds)) {
                        return (int) $jointClass->id;
                    }
                    
                    // Priority 2: Use revision_plans class_id if available
                    $revisionPlan = $student->revision_plans->first();
                    if ($revisionPlan && $revisionPlan->class_id && in_array($revisionPlan->class_id, $classIds)) {
                        return (int) $revisionPlan->class_id;
                    }
                    
                    // Priority 3: Find any matching class from the requested classIds
                    foreach ($classIds as $classId) {
                        // Check if student has any relationship to this class
                        $hasJointClass = $student->joint_classes->where('id', $classId)->isNotEmpty();
                        $hasRevisionPlan = $student->revision_plans->where('class_id', $classId)->isNotEmpty();
                        
                        if ($hasJointClass || $hasRevisionPlan) {
                            return (int) $classId;
                        }
                    }
                    
                    // Fallback: Log warning and return first requested class ID
                    \Log::warning('Student revision class_id fallback used', [
                        'student_id' => $student->id,
                        'student_name' => $student->full_name,
                        'requested_class_ids' => $classIds,
                        'joint_classes' => $student->joint_classes->pluck('id')->toArray(),
                        'revision_plan_class_ids' => $student->revision_plans->pluck('class_id')->toArray()
                    ]);
                    
                    return (int) $classIds[0]; // Never return 0
                })
                ->addColumn('class_name', function ($student) use ($classIds) {
                    // 🎯 CRITICAL FIX: Use IDENTICAL logic as class_id to ensure consistency
                    // Priority 1: Use joint_classes relationship if available
                    $jointClass = $student->joint_classes->first();
                    if ($jointClass && in_array($jointClass->id, $classIds)) {
                        $className = $jointClass->name ?: 'Unnamed Class';
                        $classCode = $jointClass->class_code ?: '';
                        return $classCode ? "{$classCode} - {$className}" : $className;
                    }
                    
                    // Priority 2: Use revision_plans class_id if available
                    $revisionPlan = $student->revision_plans->first();
                    if ($revisionPlan && $revisionPlan->class_id && in_array($revisionPlan->class_id, $classIds)) {
                        $class = \App\Classes::find($revisionPlan->class_id);
                        if ($class) {
                            $className = $class->name ?: 'Unnamed Class';
                            $classCode = $class->class_code ?: '';
                            return $classCode ? "{$classCode} - {$className}" : $className;
                        }
                    }
                    
                    // Priority 3: Find any matching class from the requested classIds
                    foreach ($classIds as $classId) {
                        // Check if student has any relationship to this class
                        $hasJointClass = $student->joint_classes->where('id', $classId)->isNotEmpty();
                        $hasRevisionPlan = $student->revision_plans->where('class_id', $classId)->isNotEmpty();
                        
                        if ($hasJointClass || $hasRevisionPlan) {
                            $class = \App\Classes::find($classId);
                            if ($class) {
                                $className = $class->name ?: 'Unnamed Class';
                                $classCode = $class->class_code ?: '';
                                return $classCode ? "{$classCode} - {$className}" : $className;
                            }
                        }
                    }
                    
                    // Fallback: Return name of first requested class
                    $fallbackClass = \App\Classes::find($classIds[0]);
                    if ($fallbackClass) {
                        $className = $fallbackClass->name ?: 'Unnamed Class';
                        $classCode = $fallbackClass->class_code ?: '';
                        return $classCode ? "{$classCode} - {$className}" : $className;
                    }
                    
                    return 'Unknown Class';
                })
                ->addColumn('revisedPages', function ($student) use ($request) {


                    try {

                        if ($student->revision_count > 0) {
                            $firstRevision = $student->revision->sortBy(function ($row) {
                                return [$row->revision_from_surat, $row->revision_from_ayat];
                            })->first();

                            $lastRevision = $student->revision->sortByDesc(function ($row) {
                                return [$row->revision_to_surat, $row->revision_to_ayat];
                            })->first();

                            $min_revision_from_surat = $firstRevision->revision_from_surat;
                            $min_revision_from_ayat = $firstRevision->revision_from_ayat;
                            $max_revision_to_surat = $lastRevision->revision_to_surat;
                            $max_revision_to_ayat = $lastRevision->revision_to_ayat;
                            $revisionPlan = $student->revision_plans[0];


                            // find out the number of pages memorized so far
                            if ($revisionPlan->study_direction == 'backward') {
                                $revisedNumberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $min_revision_from_surat,
                                    $min_revision_from_ayat,
                                    $max_revision_to_surat,
                                    $max_revision_to_ayat
                                ]);


                                $revisedNumberofPages = $revisedNumberofPages[0]->numberofPagesSum;



                            } else {

                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $min_revision_from_surat,
                                    $min_revision_from_ayat,
                                    $max_revision_to_surat,
                                    $max_revision_to_ayat
                                ]);

                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $revisedNumberofPages = $results[0]->number_of_pages_sum;

                            }


                            return '<h2 style="color: #1fff0f;
    font-weight: bolder;
    font-size: 24px;">' . $revisedNumberofPages . '</h2>';
                            return $revisedNumberofPages;
                        }

                    } catch (\Exception $exception) {
                        dd(8);
                    }
                })
                ->addColumn('student', function ($student) use ($request) {
                    $studentName = ucfirst($student->full_name);
                    $studentProfileUrl = route('students.show', ['id' => $student->user_id]);
                    return '<a style="color:#b4eeb0; padding: 2px; display: inline-block; transition: all 0.3s ease;" data-id='.$student->id.' class="section class-link" target="_blank" href="' . $studentProfileUrl . '">' . $studentName . '</a>';



                })
                ->addColumn('monthlyRevisionPlan', function ($student) use ($request) {
                    try {
                        if ($student->revision_plans_count > 0) {

                            $firstPlanSurat = $student->revision_plans->first()->start_from_surat;
                            $firstPlanAyat = $student->revision_plans->first()->start_from_ayat;
                            $lastPlanSurat = $student->revision_plans->first()->to_surat;
                            $lastPlanAyat = $student->revision_plans->first()->to_ayat;



                            $studyDirection = \Modules\Education\Http\Controllers\MonthlyPlanController::determineStudyDirection($firstPlanSurat, $lastPlanSurat, $firstPlanAyat, $lastPlanAyat);

                            if ($studyDirection == 'backward') {


                                $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $firstPlanSurat,
                                    $firstPlanAyat,
                                    $lastPlanSurat,
                                    $lastPlanAyat
                                ]);
                                $numberofPagesSum = $numberofPages[0]->numberofPagesSum;


                            } else {
                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $firstPlanSurat,
                                    $firstPlanAyat,
                                    $lastPlanSurat,
                                    $lastPlanAyat
                                ]);

                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $numberofPagesSum = $results[0]->number_of_pages_sum;


                            }
                            $string = "({$firstPlanSurat}:{$firstPlanAyat}) to ({$lastPlanSurat}:{$lastPlanAyat}), [{$numberofPagesSum}]";
                            $stEditMonthlyPlanRoute = route('monthly-plan.show',[$student->revision_plans->first()->class_id,$student->revision_plans->first()->created_at->format('Y-m-d')]);

                            return '<a style="color:#b4eeb0; padding: 2px; display: inline-block; transition: all 0.3s ease;" data-id='.$student->id.' class="section class-link" target="_blank" href="' . $stEditMonthlyPlanRoute . '"> ' . $string . '</a>';



                            return '<a style="color:#b4eeb0; padding: 2px; display: inline-block; transition: all 0.3s ease;" data-id='.$student->id.' class="section student-link" target="_blank" href="' . route('monthly-plan.show', ['id' => $student->revision_plans->first()->class_id, 'from_date' => $student->revision_plans->first()->start_date]) . '"> ' . $fromSuratAyat . ' – ' . $lastSuratAyat . '</a>';



                            return $fromSuratAyat . ' – ' . $lastSuratAyat;


                        }

                    } catch (\Exception $exception) {
                        return $exception->getMessage();
                    }

                })
                ->addColumn('monthlyRevisionReport', function ($student) use ($request, $classId) {
                    try {
                        if ($student->revision_count <= 0) {
                            return "";
                        }

                        if ($student->revision_count > 0) {

                            $planYearMonth = Carbon::parse($request->get('classDate'));
                            $year = $planYearMonth->year;
                            $month = $planYearMonth->month;
                            $attendance_id = 2;

                            $firstRevision = $student->revision->sortBy(function ($row) {
                                return [$row->revision_from_surat, $row->revision_from_ayat];
                            })->first();


                            $lastRevision = $student->revision->sortByDesc(function ($row) {
                                return [$row->revision_to_surat, $row->revision_to_ayat];
                            })->first();
                            if (!$firstRevision || !$lastRevision) {
                                return "";
                            }


                            if ($student->revision_plans->first()->study_direction == 'backward') {


                                $revisedNumberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                    $firstRevision->revision_from_surat,
                                    $firstRevision->revision_from_ayat,
                                    $lastRevision->revision_to_surat,
                                    $lastRevision->revision_to_ayat
                                ]);


                                $revisedNumberofPages = $revisedNumberofPages[0]->numberofPagesSum;



                            }
                            else {

                                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                    $firstRevision->revision_from_surat,
                                    $firstRevision->revision_from_ayat,
                                    $lastRevision->revision_to_surat,
                                    $lastRevision->revision_to_ayat
                                ]);

                                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                                $revisedNumberofPages = $results[0]->number_of_pages_sum;

                            }

                            $numberofPagesSum = $revisedNumberofPages;
                            $stEditMonthlyRevisionReportRoute = url('workplace/education/classes/' . $student->revision->first()->class_id . '/reports/create?from_date=' . $planYearMonth->toDateString());
                            return $this->generateLink($student->id, $stEditMonthlyRevisionReportRoute, $firstRevision, $lastRevision, $numberofPagesSum);


                        }

                    } catch (\Exception $exception) {
                        dd(4);
                    }
                })
                ->addColumn('revisionAchievementComparedtoHefzPlan', function ($student) use ($request, $classId) {


                    try {
                        $planYearMonth = Carbon::parse($request->get('classDate'));
                        $year = $planYearMonth->year;
                        $month = $planYearMonth->month;
                        $attendance_id = 2;

                        $data = StudentRevisionReport::where('student_id', $student->id)
                            ->where('class_id', $classId)
                            ->whereMonth('created_at', $month)
                            ->whereYear('created_at', $year)
                            ->where('attendance_id', $attendance_id)
                            ->selectRaw('min(revision_from_surat) as min_revision_from_surat, min(revision_from_ayat) as min_revision_from_ayat, max(revision_to_surat) as max_revision_to_surat, max(revision_to_ayat) as max_revision_to_ayat')
                            ->first();
                        $min_revision_from_surat = $data->min_revision_from_surat;
                        $min_revision_from_ayat = $data->min_revision_from_ayat;
                        $max_revision_to_surat = $data->max_revision_to_surat;
                        $max_revision_to_ayat = $data->max_revision_to_ayat;
                        // find out the number of pages memorized so far

                        $studyDirection = \Modules\Education\Http\Controllers\MonthlyPlanController::determineStudyDirection($min_revision_from_surat, $max_revision_to_surat, $min_revision_from_ayat, $max_revision_to_ayat);
//                        if ($student->revision_plans->first()->study_direction == 'backward') {
                        if ($studyDirection == 'backward') {

                            $revisedNumberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                $min_revision_from_surat,
                                $min_revision_from_ayat,
                                $max_revision_to_surat,
                                $max_revision_to_ayat
                            ]);


                            $revisedNumberofPages = $revisedNumberofPages[0]->numberofPagesSum;


                        } else {


                            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                $min_revision_from_surat,
                                $min_revision_from_ayat,
                                $max_revision_to_surat,
                                $max_revision_to_ayat
                            ]);

                            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                            $revisedNumberofPages = $results[0]->number_of_pages_sum;

                        }
                        $firstPlanSurat = $student->revision_plans->first()->start_from_surat;
                        $firstPlanAyat = $student->revision_plans->first()->start_from_ayat;
                        $lastPlanSurat = $student->revision_plans->first()->to_surat;
                        $lastPlanAyat = $student->revision_plans->first()->to_ayat;
                        // now find out the number of pages asssigned at the hefz plan
                        if ($studyDirection == 'backward') {

                            $plannedNumberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                $firstPlanSurat,
                                $firstPlanAyat,
                                $lastPlanSurat,
                                $lastPlanAyat
                            ]);


                            $plannedNumberofPages = $plannedNumberofPages[0]->numberofPagesSum;


                        } else {


                            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                $firstPlanSurat,
                                $firstPlanAyat,
                                $lastPlanSurat,
                                $lastPlanAyat
                            ]);

                            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                            $plannedNumberofPages = $results[0]->number_of_pages_sum;

                        }


                        if (empty($revisedNumberofPages) || is_null($revisedNumberofPages)) {
//                            $result = "No revision so far";
                            $result = 0;
                        } elseif (empty($plannedNumberofPages) || is_null($plannedNumberofPages)) {
//                            $result = "No plan available";
                            $result = 0;
                        } else {
                            $result = $revisedNumberofPages / $plannedNumberofPages * 100;
                            $result = round($result,2);
                        }

                        // Determine color class based on percentage
                        $colorClass = 'progress-excellent';
                        if ($result === 0.0) {
                            $colorClass = 'progress-zero';
                        } elseif ($result < 50) {
                            $colorClass = 'progress-poor';
                        } elseif ($result < 75) {
                            $colorClass = 'progress-good';
                        }

                        return '<div class="enhanced-progress-container ' . $colorClass . '"
                                     data-tooltip="Revision Achievement: ' . $result . '%"
                                     data-calculation-type="revision_achievement">
                            <div class="enhanced-progress-fill" style="width: ' . $result . '%"></div>
                            <div class="enhanced-progress-text">' . $result . '%</div>
                        </div>';


                    } catch (\Exception $exception) {
                        dd(5);
                    }

                })
                ->addColumn('attendanceDaysPercentage', function ($student) use ($request,$month, $year) {



                    try {
//                        $result = round($student->revision_report_attendance_percentage, 2);

                        $revisionAttendancePercentage = $student->revisionAttendancePercentageForMonth($month, $year);
                        $result = $revisionAttendancePercentage !== null ? round($revisionAttendancePercentage, 2) : 0.0;

                        // Determine color class based on percentage
                        $colorClass = 'progress-excellent';
                        if ($result === 0.0) {
                            $colorClass = 'progress-zero';
                        } elseif ($result < 50) {
                            $colorClass = 'progress-poor';
                        } elseif ($result < 75) {
                            $colorClass = 'progress-good';
                        }

                        return '<div class="enhanced-progress-container ' . $colorClass . '"
                                     data-tooltip="Attendance: ' . $result . '%"
                                     data-calculation-type="attendance">
                            <div class="enhanced-progress-fill" style="width: ' . $result . '%"></div>
                            <div class="enhanced-progress-text">' . $result . '%</div>
                        </div>';


                    } catch (\Exception $exception) {
                        dd(6);
                    }
                })
                ->rawColumns(['monthlyRevisionReport','monthlyRevisionPlan','revisionAchievementComparedtoHefzPlan','attendanceDaysPercentage','student', 'if ($student->total_sessions > 0) {', 'monthlyHefzPlan', 'monthlyHefzReport', 'hefzAchievementComparedtoHefzPlan','revisedPages'])
                ->make(true);

        }




    }

    function generateLink($studentId, $route, $firstHefz, $lastHefz, $revisedNumberofPages) {
        $string = "({$firstHefz->revision_from_surat}:{$firstHefz->revision_from_ayat}) to ({$lastHefz->revision_to_surat}:{$lastHefz->revision_to_ayat}), [{$revisedNumberofPages}]";
        return '<a style="color:#b4eeb0; padding: 2px; display: inline-block; transition: all 0.3s ease;" data-id=' . $studentId . ' class="section class-link" target="_blank" href="' . $route . '">' . $string . '</a>';
    }



}

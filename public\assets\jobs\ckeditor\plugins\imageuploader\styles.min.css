.editbarDiv,.fileDiv{transition:all .16s ease-in-out}#header,.imgDiv{text-align:center;font-family:sans-serif}#header,.background,.fileDiv,.folderInfo,.headerBtn,.imgActionP,.settingsh3,.uploadP,input{cursor:pointer}.background,.dropzone{left:0;right:0;bottom:0}html{background-color:#F2F2F2}body{max-width:700px;margin:auto;padding:90px 4px 20px}#header{position:fixed;top:0;left:0;width:100%;background-color:#2C3E50;padding:10px;font-size:16px;color:#F80B6D;z-index:999}.fileDiv{border:1px solid #E6E7E6;background-color:#FFF;border-radius:1px;width:30%;float:left;height:173px;margin-right:1%;margin-left:1%;margin-bottom:2.4%;overflow:hidden;-webkit-box-shadow:0 1px 3px 0 rgba(0,0,0,.05);-moz-box-shadow:0 1px 3px 0 rgba(0,0,0,.05);box-shadow:0 1px 3px 0 rgba(0,0,0,.05);-moz-transition:all .16s ease-in-out;-webkit-transition:all .16s ease-in-out}.fileDiv:hover{transform:scale(1.05);-moz-transform:scale(1.05);-webkit-transform:scale(1.05)}.fileDiv:active{transform:scale(1.03);-webkit-transform:scale(1.03)}.selected{background-color:#81CFE0}.selected:hover{background-color:#81CFE0!important}@media only all and (min-width:615px){.fileDiv{width:31%}}@media only all and (max-width:615px){.fileDiv{width:30.5%}}@media only all and (max-width:450px){.fileDiv{width:47%}}@media only all and (max-width:367px){.fileDiv{width:46%}}@media only all and (max-width:270px){.fileDiv{width:95%}}.imgDiv{height:120px;width:100%;overflow:hidden;border-bottom:solid 1px #EAEAEA;padding-bottom:5px;font-size:38px;font-weight:700}.fileDescription,.fileTime{font-family:sans-serif;text-align:left;padding-left:4px}.fileImg{width:auto;height:100%;margin-bottom:0}.fileDescription{font-size:13px;margin-bottom:2px;margin-top:5px}.fileTime{font-size:10px;margin-top:0;margin-bottom:0}#imageFullSreen,#uploadImgDiv{width:92%;overflow-y:auto;padding:0 8px}#imageFullSreen{position:fixed;max-width:695px;max-height:70%;overflow-x:hidden;background-color:#FFF;display:none;z-index:1001;border-radius:0;text-align:center;top:13%;-webkit-box-shadow:0 1px 1px 0 rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.25) inset;-moz-box-shadow:0 1px 1px 0 rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.25) inset;box-shadow:0 1px 1px 0 rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.25) inset}#imageFSimg{width:100%;max-width:695px;margin-top:16px;margin-bottom:5px}.imgActionP{display:inline-block;width:48%;max-width:345.5px;font-family:sans-serif;font-size:16px;font-weight:700}.headerA,.uploadP{text-decoration:none;font-weight:lighter;font-family:"Open Sans",sans-serif}.background{background-color:#F2F2F2;filter:blur(100px);z-index:1000;width:100%;height:100%;position:fixed;top:0;display:none}.headerBtn{border:none;background-color:#E0EAF1;border-radius:5px;padding:3px;font-size:15px;color:#07C;margin-right:3px;margin-top:6px}#folderError button,#updates a{border-bottom:2px solid #C1DDF5}.redBtn{background-color:#F2DEDE;color:#A94442}.greyBtn{background-color:#F1F1F1;color:#171B1F}.greenBtn{background-color:#DFF0D8;color:#3C763D}.headerBtn:hover{background-color:#07C;color:#fff}.redBtn:hover{background-color:#A94442;color:#fff}.greyBtn:hover{background-color:#818185;color:#F1F1F1}.greenBtn:hover{background-color:#3C763D;color:#fff}.headerA{color:#222;background-color:#FFF;font-size:22px}.headerA:hover{color:#FFF;background-color:#F90B6D}#uploadImgDiv,.buttonBar,.uploadP{background-color:#FFF}.buttonBar{position:fixed;text-align:left;max-width:695px;padding-bottom:7px;padding-top:6px;opacity:.9}#settingsDiv,#uploadImgDiv{position:fixed;max-height:70%;overflow-x:hidden;z-index:1001;text-align:center;top:13%}#uploadImgDiv{max-width:695px;border-radius:0;box-shadow:0 1px 1px 0 rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.25) inset}#updates,#uploadImgDiv{display:none;-webkit-box-shadow:0 1px 1px 0 rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.25) inset;-moz-box-shadow:0 1px 1px 0 rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.25) inset}.uploadP{color:#222;font-size:18px;max-width:200px;margin:10px auto 8px}#updates,.folderInfo{font-size:14px;font-family:"Helvetica Neue",Helvetica,Arial,sans-serif}.uploadP:hover,editable:hover{color:#FFF;background-color:#F90B6D}#updates{font-weight:inherit;padding:15px 20px 15px 42px;border-radius:1px;margin-bottom:16px;margin-top:-16px;color:#2C3E50;background:url(img/cd-icon-updates.png) center left 12px no-repeat #FFF;background-size:22px 22px;box-shadow:0 1px 1px 0 rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.25) inset}#updates a{font-weight:700;text-decoration:none;border-radius:0;background-color:transparent;box-shadow:0 -6px 0 #C1DDF5 inset;color:inherit}#updates a:hover{background-color:#C1DDF5}.folderInfo{margin-right:1.2%;margin-left:1.2%;margin-bottom:2.4%;overflow:hidden;font-weight:700}.headerIcon{height:18px;vertical-align:text-bottom}.headerIconLogo{height:35px;vertical-align:text-bottom;float:left;margin-left:4px}.headerIconCenter{height:29px;float:left;margin-left:20px;margin-top:4px}.headerIconRight{height:20px;float:right;margin-right:25px;margin-top:7px}.iconHover:hover{-webkit-filter:brightness(8);filter:brightness(8)}#settingsDiv{width:92%;max-width:695px;overflow-y:auto;background-color:#FFF;display:none;border-radius:0;padding:0 8px;box-shadow:0 1px 1px 0 rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.25) inset}#folderError,#settingsDiv{-webkit-box-shadow:0 1px 1px 0 rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.25) inset;-moz-box-shadow:0 1px 1px 0 rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.25) inset}.editbarDiv,.fullWidth30percent,.fullWidthfileDescription,.fullWidthimgDiv,.qEditIconsDiv{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.settingsh3{font-weight:bolder;color:#222;text-decoration:none;font-family:"Open Sans",sans-serif;font-size:14px;max-width:200px;margin:10px auto 8px}.editableActive{color:#222!important;background-color:#FFF!important;cursor:inherit!important}#folderError{font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:14px;font-weight:inherit;padding:15px 20px 15px 42px;border-radius:1px;margin-bottom:16px;margin-top:-5px;color:#2C3E50;background:url(img/cd-icon-warning.png) center left 12px no-repeat #FFF;background-size:22px 22px;box-shadow:0 1px 1px 0 rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.05),0 1px 0 rgba(255,255,255,.25) inset}.pathHistory,.saveUploadPathP{display:none;font-weight:lighter}#folderError button{padding:0 2px;border-radius:0;background-color:transparent;box-shadow:0 -6px 0 #C1DDF5 inset;color:inherit}#folderError button:hover{background-color:#C1DDF5}.saveUploadPathP{margin-bottom:-5px}.pathHistory{color:#222;text-decoration:none;font-family:"Open Sans",sans-serif;font-size:13px;max-width:240px;cursor:pointer;margin:10px auto -5px}.pathHistory:hover{color:#55ACEE}.saveUploadPathA{display:none;margin-top:18px}.fileMime{display:inline-block;border:solid #404040;border-width:thin;padding:0 1px;font-size:9px;font-family:Verdana,Geneva,sans-serif;text-transform:uppercase;font-weight:bolder;color:#404040}.fullWidthfileDescription,.fullWidthfileTime{font-family:sans-serif;font-size:15px;display:inline-block}.editIcon{width:15px;float:right;margin-right:4px}.fullWidthFileDiv{width:100%;cursor:pointer}.fullWidthFileDiv:hover{background-color:#E0EAF1}.fullWidthfileImg{height:16px}.fullWidthimgDiv{display:inline-block;width:30px;margin-bottom:12px;text-align:left}.fullWidthfileDescription{text-align:left;font-weight:600;max-width:25%;width:25%}.fullWidthfileTime{font-weight:300;text-align:center;float:right;margin-left:8%;max-width:9%;width:9%;max-height:16px;overflow-y:hidden}#editbar,#qEditBtnDone,.dropzone{display:none}.fullWidth30percent{max-width:30%;width:27%}@media only all and (max-width:580px){.fullWidthfileTime{margin-left:2%;max-width:12%;width:12%}.fullWidth30percent{max-width:20%;margin-left:0}.fullWidthfileDescription{max-width:35%;width:35%}}.fullWidthfileMime{text-transform:uppercase;text-align:right}.floatRight{float:right;margin-left:8px}.qEditIconsImg{height:16px}.qEditIconsDiv{float:right;display:none;margin-bottom:-20px;text-align:right;padding-top:16px;margin-left:8%;max-width:9%;width:9%}#editbar,.dropzone,.dropzone p{position:fixed;width:100%}.dropzone{top:0;height:100%;z-index:1002;cursor:pointer;background:#EEE}.dropzone p{text-align:center;top:37%;font-weight:lighter;font-family:"Open Sans",sans-serif;font-size:18px;animation:popdrop 1s infinite;-webkit-animation:popdrop 1s infinite}@keyframes popdrop{from,to{transform:scale(.9)}80%{transform:scale(1.08)}}@-webkit-keyframes popdrop{from{-webkit-transform:scale(.8)}80%{-webkit-transform:scale(1.02)}to{-webkit-transform:scale(1)}}#editbar{top:55px;margin:auto;max-width:680px;cursor:pointer;padding:10px;font-family:sans-serif;font-size:16px;color:#F80B6D;text-align:left;z-index:999;border:1px solid #E6E7E6;border-top:none;background-color:#FFF;border-radius:1px;-webkit-box-shadow:0 1px 3px 0 rgba(0,0,0,.05);-moz-box-shadow:0 1px 3px 0 rgba(0,0,0,.05);box-shadow:0 1px 3px 0 rgba(0,0,0,.05)}@media only all and (max-width:707px){#editbar{width:96%}}@media only all and (max-width:666px){#editbar{width:95.9%}}@media only all and (max-width:649px){#editbar{width:95.8%}}@media only all and (max-width:634px){#editbar{width:95.7%}}@media only all and (max-width:617px){#editbar{width:95.5%}}@media only all and (max-width:591px){#editbar{width:95.4%}}@media only all and (max-width:580px){#editbar{width:95%}}@media only all and (max-width:552px){#editbar{width:94.6%}}@media only all and (max-width:500px){#editbar{width:94.2%}}@media only all and (max-width:461px){#editbar{width:93.8%}}@media only all and (max-width:430px){#editbar{width:93.4%}}@media only all and (max-width:404px){#editbar{width:93%}}@media only all and (max-width:381px){#editbar{width:92.6%}}@media only all and (max-width:360px){#editbar{width:91.6%}}@media only all and (max-width:325px){#editbar{width:91%}}@media only all and (max-width:310px){#editbar{width:90.6%}}@media only all and (max-width:287px){#editbar{width:90.2%}}@media only all and (max-width:274px){#editbar{width:89%}}.editbarDiv{display:inline-block;float:left;margin-right:18px;max-width:15%;-moz-transition:all .16s ease-in-out;-webkit-transition:all .16s ease-in-out}.editbarDiv:hover{transform:scale(1.1);-moz-transform:scale(1.1);-webkit-transform:scale(1.1)}.editbarIcon{-webkit-filter:grayscale(100%);filter:grayscale(100%);-moz-transition:all .16s ease-in-out;-webkit-transition:all .16s ease-in-out}.editbarIcon,.editbarIconRight{transition:all .16s ease-in-out}.editbarIconRight{width:15px;float:right;-moz-transition:all .16s ease-in-out;-webkit-transition:all .16s ease-in-out}.editbarIconRight:hover{transform:scale(1.1);-moz-transform:scale(1.1);-webkit-transform:scale(1.1)}.editbarIconLeft{width:15px;float:left;margin-right:5px}.editbarText{font-weight:lighter;font-family:"Open Sans",sans-serif;font-size:14px;display:inline-block;margin:0;color:#000}.popout{animation:popout .5s ease;-webkit-animation:popout .5s ease}@keyframes popout{from{transform:scale(.8)}80%{transform:scale(1.02)}to{transform:scale(1)}}@-webkit-keyframes popout{from{-webkit-transform:scale(.8)}80%{-webkit-transform:scale(1.02)}to{-webkit-transform:scale(1)}}.deleteAnimation{animation:deleteAnimation .5s ease;-webkit-animation:deleteAnimation .5s ease}@keyframes deleteAnimation{from{transform:scale(1)}to{transform:scale(0);width:0;display:none}}@-webkit-keyframes deleteAnimation{from{-webkit-transform:scale(1)}to{-webkit-transform:scale(0);width:0;display:none}}#spinnerDiv{position:fixed;left:0;right:0;display:block;width:100%;text-align:center}.spinner{width:40px;height:40px;background-color:#333;margin:100px auto;-webkit-animation:sk-rotateplane 1.2s infinite ease-in-out;animation:sk-rotateplane 1.2s infinite ease-in-out}@-webkit-keyframes sk-rotateplane{0%{-webkit-transform:perspective(120px)}50%{-webkit-transform:perspective(120px) rotateY(180deg)}100%{-webkit-transform:perspective(120px) rotateY(180deg) rotateX(180deg)}}@keyframes sk-rotateplane{0%{transform:perspective(120px) rotateX(0) rotateY(0);-webkit-transform:perspective(120px) rotateX(0) rotateY(0)}50%{transform:perspective(120px) rotateX(-180.1deg) rotateY(0);-webkit-transform:perspective(120px) rotateX(-180.1deg) rotateY(0)}100%{transform:perspective(120px) rotateX(-180deg) rotateY(-179.9deg);-webkit-transform:perspective(120px) rotateX(-180deg) rotateY(-179.9deg)}}

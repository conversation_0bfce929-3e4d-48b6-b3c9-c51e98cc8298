@extends('layouts.hound')

@section('content')

        <div class="row">
                <div class="panel panel-default">
                    <div class="panel-heading">Edit News : {{ $news->title }}</div>
                    <div class="panel-body">

                        @if ($errors->any())
                            <ul class="alert alert-danger">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        @endif

                        {!! Form::model($news, [
                            'method' => 'PATCH',
                            'route' => ['news.update', $news->id],
                            'class' => 'form-horizontal',
                            'files' => true
                        ]) !!}

                        @include ('site::news.form', ['submitButtonText' => 'Update'])

                        {!! Form::close() !!}

                    </div>
                </div>
    </div>
@endsection
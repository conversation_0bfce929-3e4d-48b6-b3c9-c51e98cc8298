<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Helpers\StudentImageUploadHelper;
use App\Services\StudentImageService;
use App\Student;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;

/**
 * Repository for student image operations
 */
class StudentImageRepository
{
    /**
     * Update student photo
     * 
     * @param Student $student The student to update
     * @param UploadedFile $file The uploaded photo file
     * @return Student The updated student
     * @throws \Exception If the upload fails
     */
    public function updateStudentPhoto(Student $student, UploadedFile $file): Student
    {
        Log::info('Updating student photo', [
            'student_id' => $student->id, 
            'original_filename' => $file->getClientOriginalName()
        ]);
        
        // Delete existing photo if it exists
        if ($student->student_photo) {
            StudentImageUploadHelper::deleteStudentPhoto($student->student_photo);
        }
        
        // Upload new photo
        $imagePath = StudentImageUploadHelper::uploadStudentPhoto($file, $student);
        
        // Return the updated student
        return $student->refresh();
    }
    
    /**
     * Delete student photo
     * 
     * @param Student $student The student to update
     * @return bool True if the photo was deleted, false otherwise
     */
    public function deleteStudentPhoto(Student $student): bool
    {
        if (empty($student->student_photo)) {
            Log::info('No photo to delete for student', ['student_id' => $student->id]);
            return false;
        }
        
        $result = StudentImageUploadHelper::deleteStudentPhoto($student->student_photo);
        
        if ($result) {
            $student->student_photo = null;
            $student->save();
            
            Log::info('Student photo deleted and record updated', ['student_id' => $student->id]);
        }
        
        return $result;
    }
    
    /**
     * Get the URL for a student's photo
     * 
     * @param Student $student
     * @return string The URL to the student's photo
     */
    public function getStudentPhotoUrl(Student $student): string
    {
        return app(StudentImageService::class)->getStudentImageUrl($student);
    }
} 
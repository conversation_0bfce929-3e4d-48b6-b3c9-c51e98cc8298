!function(){"use strict";var t=tinymce.util.Tools.resolve("tinymce.PluginManager"),n=tinymce.util.Tools.resolve("tinymce.util.Tools"),e=function(t){return t.getParam("noneditable_noneditable_class","mceNonEditable")},r=function(t){return t.getParam("noneditable_editable_class","mceEditable")},a=function(t){var n=t.getParam("noneditable_regexp",[]);return n&&n.constructor===RegExp?[n]:n},i=function(t){return function(n){return-1!==(" "+n.attr("class")+" ").indexOf(t)}},o=function(t,n,e){return function(r){var a=arguments,i=a[a.length-2],o=i>0?n.charAt(i-1):"";if('"'===o)return r;if(">"===o){var c=n.lastIndexOf("<",i);if(-1!==c&&-1!==n.substring(c,i).indexOf('contenteditable="false"'))return r}return'<span class="'+e+'" data-mce-content="'+t.dom.encode(a[0])+'">'+t.dom.encode("string"==typeof a[1]?a[1]:a[0])+"</span>"}},c=function(t){var c,l,u="contenteditable";c=" "+n.trim(r(t))+" ",l=" "+n.trim(e(t))+" ";var f=i(c),s=i(l),d=a(t);t.on("PreInit",function(){d.length>0&&t.on("BeforeSetContent",function(n){!function(t,n,r){var a=n.length,i=r.content;if("raw"!==r.format){for(;a--;)i=i.replace(n[a],o(t,i,e(t)));r.content=i}}(t,d,n)}),t.parser.addAttributeFilter("class",function(t){for(var n,e=t.length;e--;)n=t[e],f(n)?n.attr(u,"true"):s(n)&&n.attr(u,"false")}),t.serializer.addAttributeFilter(u,function(t){for(var n,e=t.length;e--;)n=t[e],(f(n)||s(n))&&(d.length>0&&n.attr("data-mce-content")?(n.name="#text",n.type=3,n.raw=!0,n.value=n.attr("data-mce-content")):n.attr(u,null))})})};t.add("noneditable",function(t){c(t)})}();
<?php

namespace Modules\EducationalReports\Http\Controllers;

use App\Center;
use App\CenterTranslation;
use App\Country;
use App\Notifications\SuperiorResetPassword;
use App\Notifications\UserStatusChangedToNewApplication;
use App\Organization;
use App\Program;
use App\Scopes\OrganizationScope;
use App\User;
use Carbon\Carbon;
use Faker\Factory;
use Faker\Provider\en_UG\PhoneNumber;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Mail\StudentCreated;
use App\Student;
use App\Guardian;
use App\Employee;
use App\Role;
use App\Cen_Emp;
use App\Admission;
use App\Classes;
use App\StudentHefzPlan;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Mail;
use PhpParser\Node\Stmt\Foreach_;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class StudentController extends Controller
{


    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Request $request)
    {

        if (!auth()->user()->can('access students')) {
            // return redirect()->route('not-authorized');
            return redirect()->back();
        } else {

            DB::connection()->enableQueryLog();

            if ($request->ajax()) {




                $students = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                    ->has('programs')
                    ->with(['programs.programTranslations' => function ($query) {
                        $query->select('program_id', 'title');
                    }])
                    ->has('center')
                    ->with('center.translations')
                    ->has('allStudents')
                    ->with('allStudents.user') // includes softdeleted students

                    ->withoutGlobalScope(OrganizationScope::class)
                    ->orderByRaw('admissions.created_at desc');

                if (request()->filled('status') && request()->get('status') == 'archived') {
                    $students->withTrashed();
                }



                    $students->select();


                return DataTables::eloquent($students)
                    ->filter(function ($query) use ($students) {

                        if (request()->filled('programs')) {

                            $query->whereHas('programs', function ($query) {

                                $query->whereId(request('programs'));
                            });

                        }

                        if (request()->filled('centers')) {

                            $query->whereHas('center', function ($query) {

                                $query->whereId(request('centers'));
                            });

                        }
                        if (request()->filled('name')) {

                            $query->studentFullName(request('name'));
                        }

                        if (request()->filled('dateRange')) {
                            $range =  explode(",", request('dateRange'));

                            $query->whereBetween('created_at', $range);
                        }
                        if (request()->filled('status') && request()->get('status') == 'new_admission') {

                            // query scope inside Admission Model
                            $query->whereStatus(request('status'));
                        }
                        if (request()->filled('status') && request()->get('status') == 'plan_waiting_approval') {


                            // query scope inside Admission Model
                            $query->planWaitingApproval(request('status'));
                        }

                        if (request()->filled('status') && request()->get('status') == 'archived') {
                            $query->whereNotNull('deleted_at');
                        }


                        if (request()->filled('gender')) {
                            $query->whereHas('allStudents', function ($query) {

                                $query->whereGender(request('gender'));
                            });
                        }


                    },true)
                    ->addIndexColumn()
                    ->addColumn('login', function ($row) use ($request) {


                        if (is_null($row->deleted_at)) {
                            $impersonationRoute = route('multiAuthImpersonate', ['id' => $row->student()->first()->user_id, 'guardName' => 'web']);
                            return '<span class="badge badge-primary badge-pill"><a
                                style="color: white; padding: 0px; background: transparent "
                                href="' . $impersonationRoute . '">login</a></span>';
                        }


                    })->addColumn('identity_number', function ($row) use ($request) {

                        return Str::upper($row->allStudents->identity_number);


                    })
                    ->addColumn('nationality', function ($row) use ($request) {

                        return Str::upper($row->allStudents->nationality);


                    })
                    ->addColumn('full_name', function ($row) use ($request) {

                        $stShowRoute = route('students.show', $row->allStudents->id);
                        $genderColor = $row->allStudents->gender == 'Male' ? '#34b8bc;!important' : '#FA5661;!important';

                        if(strlen($row->allStudents->full_name) > 13){
//                            $fullname = Str::limit(Str::title($row->allStudents->full_name),13,' ...');
                            $fullname = Str::title($row->allStudents->full_name);



                            return '<a style="color:'.$genderColor.'" target="_blank" href="' . $stShowRoute . '" ><strong data-tooltip="' . Str::title($row->allStudents->full_name) . '" >' .$fullname. '</strong></a>';
                        }
                        else{
                            $fullname = Str::title($row->allStudents->full_name);
                            return '<a style="color:'.$genderColor.'" target="_blank" href="' . $stShowRoute . '" ><strong data-tooltip="' . Str::title($row->allStudents->full_name) . '" style="color:"'.$genderColor.'">' .$fullname. '</strong></a>';
//                            return '<a target="_blank" href="' . $stShowRoute . '" ><strong  style="color: #34b8bc">' .$fullname. '</strong></a>';
                        }





                    })
                    ->addColumn('email', function ($row) use ($request) {




                        $details = '';

                            $email = Str::title($row->allStudents->email);
                        if(strlen($row->allStudents->email) > 24) {
                            $email = Str::limit(Str::title($email),24,' ...');

                            $details .= '<strong  data-tooltip="' . Str::title($row->allStudents->email) . '" style="color: #34b8bc">' . $email . '</strong><br>';
                        }else{
                            $details .= '<strong  style="color: #34b8bc">' .$email. '</strong><br>';

                        }
                             $details .= $row->allStudents->mobile.'<br>';

                             return $details;
                    })
                    ->addColumn('full_name_trans', function ($row) use ($request) {




                        return $row->allStudents->full_name_trans;

                    })
                    ->addColumn('date_of_birth', function ($row) use ($request) {

                        return $row->allStudents->date_of_birth;

                    })
//                    ->addColumn('gender', function ($row) use ($request) {
//
//
//
//
//                        return Str::upper($row->allStudents->gender);
//
//                    })
                    ->addColumn('image', function ($row) use ($request) {


                        $genderBasedDefaultImage = $row->allStudents->gender == 'Male' ? asset('uploads/staff/demo/mstaff.jfif') : asset('uploads/staff/demo/fstaff.jpg');
                        return file_exists($row->allStudents->student_photo) ? asset($row->allStudents->student_photo) : $genderBasedDefaultImage;
                        return  '<img class=""  src="'.file_exists($row->allStudents->student_photo) ? asset($row->allStudents->student_photo) : asset('public/uploads/staff/demo/staff.jpg').'" width="100" height="100" alt="'.asset($row->allStudents->student_photo).'">';


                    })
                    ->addColumn('created_at', function ($row) {

                        return '<div class="ui icon button" data-tooltip="' . value($row['created_at'])->format('Y-d-M  g:i a') . '">' . value($row['created_at'])->diffForHumans() . '</div>';
                        return value($row['created_at'])->diffForHumans();
                    })
                    ->addColumn('center', function ($row) {
                        return $row->center->translations->filter(function ($value,$key){
                            return $value->locale == 'en';
                        })->map(function ($center) {
                            return Str::limit($center->name, 30, '...');
                        })->implode('<br>');
                    })
                    ->addColumn('program', function ($row) {
                        return $row->programs->map(function ($program) {

                            return Str::limit($program->programTranslations->first()->title, 30, '...');
                        })->implode('<br>');
                    })
                    ->addColumn('mobile', function ($row) {
                        $mobileNumbers = '';
                        if ($row->allStudents->mobile == true) {
                            $mobileNumbers .= '<a class="ui label">' . $row->allStudents->mobile . '</a>&nbsp;';
                        }
                        if ($row->allStudents->mobile_2 == true) {
                            $mobileNumbers .= '<a class="ui  label">' . $row->allStudents->mobile_2 . '</a>&nbsp;';
                        }
                        return $mobileNumbers;
                    })
                    ->addColumn('date_of_birth', function ($row) {
                        return $row->allStudents->date_of_birth;
                    })
                    ->addColumn('username', function ($row) {

                        return $row->allStudents->user->username;

                    })

                    ->addColumn('action', function ($row) {

                        $stShowRoute = route('students.show', $row->allStudents->id);
                        $stEditRoute = route('students.edit', $row->allStudents->id);
                        $btns = ' <a href="' . $stShowRoute . '" class="btn btn-success btn-xs" title="View Student"><span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a> 

                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
                                                                                    data-catid=' . $row->student->id . ' data-toggle="modal"
                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
                                                                                                                title="Delete Student"/></button>
                                                                                                                ';


                        if(request()->get('status') == 'new_admission' || $row->student->status == 'New Admission')
                        {

                            $btns .= '<button class="btn btn-default btn-xs "
                                                                                    data-student_id=' . $row->student->id . ' data-toggle="modal"
                                                                                     data-target="#newStudentModal"><span class="glyphicon glyphicon-arrow-right"
                                                                                                                title="Add student to halaqah"/></button>';
                        }
                        return $btns;
                    })
                    ->rawColumns(['center','action', 'login', 'mobile', 'created_at','full_name','fullnameTrans','email','image'])
                    ->make(true);

            }

            $filter_name = $request->filter_name;
            $filter = $request->filter;

            $perPage = 25;
            $students = Student::orderBy('id', 'DESC');
            // chackif user is supervisor

            $super = Employee::whereHas('roles', function ($q) {
                return $q->where('name', 'like', 'supervisor_%_');
            })
                ->where('id', '=', auth()->user()->id)->first();

            ///////////////////////


            if (isset($request->filter_name)) {
                $students->where('display_name', 'like', '%' . $request->filter_name . '%')
                    ->orWhere('full_name', 'like', '%' . $request->filter_name . '%');
            } elseif (isset($request->filter) && $request->filter == "application_waiting_approval") {
                $students->where('status', '!=', 'active')
                    ->where('status', '!=', 'suspended')
                    ->where('status', '!=', 'graduated');
            } elseif (isset($request->filter) && $request->filter == "plan_waiting_approval") {
                $students->whereHas('hefz_plans', function ($query) {
                    $query->where('status', 'waiting_for_approval');
                });
            } else {
            }

            //////////////  supervisor////////////////////////////////////
            if (isset($super)) {

                $cen_emp = DB::table('cen_emps')->where('emp_id', '=', auth()->user()->id)->first();
                if (isset($cen_emp)) {
                    $center = $cen_emp->cen_id;
                    $students = null;
                    $addmision = Admission::where('center_id', '=', $center)->pluck('student_id');

                    $students = Student::whereIn('id', $addmision);
                    if (isset($request->filter_name)) {
                        $students->where('display_name', 'like', '%' . $request->filter_name . '%')
                            ->orWhere('full_name', 'like', '%' . $request->filter_name . '%')
                            ->whereIn('id', $addmision);
                    } elseif (isset($request->filter) && $request->filter == "application_waiting_approval") {

                        $students->where('status', '!=', 'active')
                            ->where('status', '!=', 'suspended')
                            ->where('status', '!=', 'graduated')
                            ->whereIn('id', $addmision);
                    } elseif (isset($request->filter) && $request->filter == "plan_waiting_approval") {
                        $students->whereHas('hefz_plans', function ($query) {
                            $query->where('status', 'waiting_for_approval');
                        })->whereIn('id', $addmision);
                    }
                } else {
                    flash('You Do Not Have Permission To Access');
                    return redirect()->back();
                }
            }
            //////////////  end of supervisor////////////////////////////////////
            $centers = DB::select("SELECT center_translations.name AS 'name', centers.id AS 'value'
            FROM centers, center_translations
            WHERE centers.id = center_translations.center_id AND center_translations.locale ='en' AND
             deleted_at IS NULL
            ORDER BY center_translations.name ASC");
            $programs = DB::select("SELECT programs.id AS value, program_translations.title AS name FROM programs,program_translations WHERE programs.id = program_translations.program_id  and program_translations.locale= 'en'");


            return view("admission::student.index", compact('students', 'filter_name', 'filter', 'items', 'statuses', 'centers', 'programs'));


        }
    }




    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view("admission::student.create");
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {

        if (!(auth()->guard('guardian')->check() || auth()->user()->can('add student'))) {
            // return back with warining
            flash('You do not have permission to add new student');
        }
        $this->validation($request);
        if (auth()->guard('guardian')->check() || auth()->guard('employee')->check()) {
            $request->merge([
                "guardian_id" => auth()->user()->id,
            ]);
            $request->merge([
                "password" => \Illuminate\Support\Str::random(8)
            ]);
        }
        $request->merge([
            "organization_id" => config('organization_id'),
        ]);

        $student = Student::create($request->all());

        Mail::to($student)->send(new StudentCreated($request->all()));

        return redirect()->route('students.show', $student);
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show(Student $student)
    {
        $student->loadMissing('admissions');
        $interviewers = Employee::all()->pluck('name', 'id');

        $programs = \App\Program::with('translations')->get()->pluck('title', 'id');

        $surat = [
            ["name" => "	Al-Fatihah	 - 	الفاتحة	", "num_ayat" => 7],
            ["name" => "	Al-Baqarah	 - 	البقرة	", "num_ayat" => 286],
            ["name" => "	Al Imran	 - 	آل عمران	", "num_ayat" => 200],
            ["name" => "	An-Nisa'	 - 	النساء	", "num_ayat" => 176],
            ["name" => "	Al-Ma'idah	 - 	المائدة	", "num_ayat" => 120],
            ["name" => "	Al-An'am	 - 	الأنعام	", "num_ayat" => 165],
            ["name" => "	Al-A'raf	 - 	الأعراف	", "num_ayat" => 206],
            ["name" => "	Al-Anfal	 - 	الأنفال	", "num_ayat" => 75],
            ["name" => "	At-Tawbah	 - 	التوبة	", "num_ayat" => 129],
            ["name" => "	Yunus	 - 	يونس	", "num_ayat" => 109],
            ["name" => "	Hud	 - 	هود	", "num_ayat" => 123],
            ["name" => "	Yusuf	 - 	يوسف	", "num_ayat" => 111],
            ["name" => "	Ar-Ra'd	 - 	الرعد	", "num_ayat" => 43],
            ["name" => "	Ibraheem	 - 	إبراهيم	", "num_ayat" => 52],
            ["name" => "	Al-Hijr	 - 	الحجر	", "num_ayat" => 99],
            ["name" => "	An-Nahl	 - 	النحل	", "num_ayat" => 128],
            ["name" => "	Al-Isra	 - 	الإسراء	", "num_ayat" => 111],
            ["name" => "	Al-Kahf	 - 	الكهف	", "num_ayat" => 110],
            ["name" => "	Maryam	 - 	مريم	", "num_ayat" => 98],
            ["name" => "	Ta-Ha	 - 	طه	", "num_ayat" => 135],
            ["name" => "	Al-Anbiya'	 - 	الأنبياء	", "num_ayat" => 112],
            ["name" => "	Al-Hajj	 - 	الحج	", "num_ayat" => 78],
            ["name" => "	Al-Mu'minoon	 - 	المؤمنون	", "num_ayat" => 118],
            ["name" => "	An-Nur	 - 	النور	", "num_ayat" => 64],
            ["name" => "	Al-Furqan	 - 	الفرقان	", "num_ayat" => 77],
            ["name" => "	ash-Shu`ara'	 - 	الشعراء	", "num_ayat" => 227],
            ["name" => "	An-Naml	 - 	النمل	", "num_ayat" => 93],
            ["name" => "	Al-Qasas	 - 	القصص	", "num_ayat" => 88],
            ["name" => "	Al-`Ankabut	 - 	العنكبوت	", "num_ayat" => 69],
            ["name" => "	Ar-Rum	 - 	الروم	", "num_ayat" => 60],
            ["name" => "	Luqman	 - 	لقمان	", "num_ayat" => 34],
            ["name" => "	As-Sajdah	 - 	السجدة	", "num_ayat" => 30],
            ["name" => "	Al-Ahzab	 - 	الأحزاب	", "num_ayat" => 73],
            ["name" => "	Saba'	 - 	سبأ	", "num_ayat" => 54],
            ["name" => "	Fatir	 - 	فاطر	", "num_ayat" => 45],
            ["name" => "	Ya seen	 - 	يس	", "num_ayat" => 83],
            ["name" => "	As-Saffat	 - 	الصافات	", "num_ayat" => 182],
            ["name" => "	Sad	 - 	ص	", "num_ayat" => 88],
            ["name" => "	Az-Zumar	 - 	الزمر	", "num_ayat" => 75],
            ["name" => "	Ghafir	 - 	غافر	", "num_ayat" => 85],
            ["name" => "	Fussilat	 - 	فصلت	", "num_ayat" => 54],
            ["name" => "	Ash-Shura	 - 	الشورى	", "num_ayat" => 53],
            ["name" => "	Az-Zukhruf	 - 	الزخرف	", "num_ayat" => 89],
            ["name" => "	Ad-Dukhan	 - 	الدخان	", "num_ayat" => 59],
            ["name" => "	Al-Jathiyah	 - 	الجاثية	", "num_ayat" => 37],
            ["name" => "	Al-Ahqaf	 - 	الأحقاف	", "num_ayat" => 35],
            ["name" => "	Muhammad	 - 	محمد	", "num_ayat" => 38],
            ["name" => "	Al-Fath	 - 	الفتح	", "num_ayat" => 29],
            ["name" => "	Al-Hujurat	 - 	الحجرات	", "num_ayat" => 18],
            ["name" => "	Qaf	 - 	ق	", "num_ayat" => 45],
            ["name" => "	Ad-Dhariyat	 - 	الذاريات	", "num_ayat" => 60],
            ["name" => "	At-Tur	 - 	الطور	", "num_ayat" => 49],
            ["name" => "	An-Najm	 - 	النجم	", "num_ayat" => 62],
            ["name" => "	Al-Qamar	 - 	القمر	", "num_ayat" => 55],
            ["name" => "	Ar-Rahman	 - 	الرحمن	", "num_ayat" => 78],
            ["name" => "	Al-Waqi'ah	 - 	الواقعة	", "num_ayat" => 96],
            ["name" => "	Al-Hadeed	 - 	الحديد	", "num_ayat" => 29],
            ["name" => "	Al-Mujadilah	 - 	المجادلة	", "num_ayat" => 22],
            ["name" => "	Al-Hashr	 - 	الحشر	", "num_ayat" => 24],
            ["name" => "	Al-Mumtahanah	 - 	الممتحنة	", "num_ayat" => 13],
            ["name" => "	As-Saff	 - 	الصف	", "num_ayat" => 14],
            ["name" => "	Al-Jumu'ah	 - 	الجمعة	", "num_ayat" => 11],
            ["name" => "	Al-Munafiqun	 - 	المنافقون	", "num_ayat" => 11],
            ["name" => "	At-Taghabun	 - 	التغابن	", "num_ayat" => 18],
            ["name" => "	At-Talaq	 - 	الطلاق	", "num_ayat" => 12],
            ["name" => "	At-Tahreem	 - 	التحريم	", "num_ayat" => 12],
            ["name" => "	Al-Mulk	 - 	الملك	", "num_ayat" => 30],
            ["name" => "	Al-Qalam	 - 	القلم	", "num_ayat" => 52],
            ["name" => "	Al-Haqqah	 - 	الحاقة	", "num_ayat" => 52],
            ["name" => "	Al-Ma'aarij	 - 	المعارج	", "num_ayat" => 44],
            ["name" => "	Nuh	 - 	نوح	", "num_ayat" => 28],
            ["name" => "	Al-Jinn	 - 	الجن	", "num_ayat" => 28],
            ["name" => "	Al-Muzzammil	 - 	المزّمِّل	", "num_ayat" => 20],
            ["name" => "	Al-Muddathir	 - 	المدّثر	", "num_ayat" => 56],
            ["name" => "	Al-Qiyamah	 - 	القيامة	", "num_ayat" => 40],
            ["name" => "	Al-Insan	 - 	الإنسان	", "num_ayat" => 31],
            ["name" => "	Al-Mursalat	 - 	المرسلات	", "num_ayat" => 50],
            ["name" => "	An-Naba'	 - 	النبأ	", "num_ayat" => 40],
            ["name" => "	An-Nazi'at	 - 	النازعات	", "num_ayat" => 46],
            ["name" => "	`Abasa	 - 	عبس	", "num_ayat" => 42],
            ["name" => "	At-Takweer	 - 	التكوير	", "num_ayat" => 29],
            ["name" => "	Al-Infitar	 - 	الانفطار	", "num_ayat" => 19],
            ["name" => "	Al-Mutaffifeen	 - 	المطففين	", "num_ayat" => 36],
            ["name" => "	Al-Inshiqaq	 - 	الانشقاق	", "num_ayat" => 25],
            ["name" => "	Al-Burooj	 - 	البروج	", "num_ayat" => 22],
            ["name" => "	At-Tariq	 - 	الطارق	", "num_ayat" => 17],
            ["name" => "	Al-A'la	 - 	الاعلى	", "num_ayat" => 19],
            ["name" => "	Al-Ghashiyah	 - 	الغاشية	", "num_ayat" => 26],
            ["name" => "	Al-Fajr	 - 	الفجر	", "num_ayat" => 30],
            ["name" => "	Al-Balad	 - 	البلد 	", "num_ayat" => 20],
            ["name" => "	Ash-Shams	 - 	الشمس	", "num_ayat" => 15],
            ["name" => "	Al-Lail	 - 	الليل 	", "num_ayat" => 21],
            ["name" => "	Ad-Dhuha	 - 	الضحى	", "num_ayat" => 11],
            ["name" => "	Al-Inshirah	 - 	الشرح	", "num_ayat" => 8],
            ["name" => "	Al-Teen	 - 	التين 	", "num_ayat" => 8],
            ["name" => "	al-`Alaq	 - 	العلق	", "num_ayat" => 19],
            ["name" => "	Al-Qadr	 - 	القدر	", "num_ayat" => 5],
            ["name" => "	Al-Bayyinah	 - 	البينة 	", "num_ayat" => 8],
            ["name" => "	Az-Zalzala	 - 	الزلزلة	", "num_ayat" => 8],
            ["name" => "	Al-Adiyat	 - 	العاديات	", "num_ayat" => 11],
            ["name" => "	al-Qari`ah	 - 	القارعة	", "num_ayat" => 11],
            ["name" => "	At-Takathur	 - 	التكاثر	", "num_ayat" => 8],
            ["name" => "	Al-Asr	 - 	العصر	", "num_ayat" => 3],
            ["name" => "	Al-Humazah	 - 	الهمزة 	", "num_ayat" => 9],
            ["name" => "	Al-Feel	 - 	الفيل 	", "num_ayat" => 5],
            ["name" => "	Al-Quraish	 - 	قريش	", "num_ayat" => 4],
            ["name" => "	Al-Maa'oun	 - 	الماعون	", "num_ayat" => 7],
            ["name" => "	Al-Kawthar	 - 	 الكوثر	", "num_ayat" => 3],
            ["name" => "	Al-Kafiroun	 - 	الكافرون	", "num_ayat" => 6],
            ["name" => "	An-Nasr	 - 	النصر	", "num_ayat" => 3],
            ["name" => "	Al-Masad	 - 	المسد 	", "num_ayat" => 5],
            ["name" => "	Al-Ikhlas	 - 	الإخلاص	", "num_ayat" => 4],
            ["name" => "	Al-Falaq	 - 	الفلق	", "num_ayat" => 5],
            ["name" => "	Al-Nas	 - 	الناس	", "num_ayat" => 6]
        ];
        $plan = StudentHefzPlan::where('student_id', '=', $student->id)->get();

        // get last student number
        $last_student_number = Student::max('student_number');
        ///////
        return view("admission::students.show", compact('student', 'interviewers', 'programs', 'surat', 'plan', 'last_student_number'));
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        // check_permission('update student');

        $student = Student::findOrFail($id);

        return view("admission::student.edit", compact('student'));
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @return Response
     */
    public function update(Request $request, $id = null)
    {

        if (
            (auth()->guard('student')->check() && auth()->user()->id == $id)
//            || (auth()->guard('guardian')->check() && is_guardian(auth()->user(), $id)
            || (auth()->guard('guardian')->check()
                || (auth()->guard('employee')->check())) // && can update student
        ) {

            $roles = [];


            if (isset($request->update_profile)) {

                $this->validation($request);

                $student = Student::findOrFail($id);

                if (!auth()->guard('employee')->check()) {
                    $request->merge(['status' => 'update_guardian']);
                }
//                if ($request->get('image')) {
//                    $student->image = $request->get('image');
//                }
                $student->fill($request->all());


                $student->save();

                flash('Profile updated!!');
                return redirect()->route('students.show', $student);
            }

            return back();
        }
    }

    private function validation($request)
    {
        if (config("settings.student_form_full_name") == "required") {
            $roles["full_name"] = "required";
        }
        if (config("settings.student_form_full_name_trans") == "required") {
            $roles["full_name_trans"] = "required";
        }
        if (config("settings.student_form_full_name_language") == "required") {
            $roles["full_name_language"] = "required";
        }
        if (config("settings.student_form_gender") == "required") {
            $roles["gender"] = "required";
        }
        if (config("settings.student_form_date_of_birth") == "required") {
            $roles["date_of_birth"] = "required";
        }
        if (config("settings.student_form_identity_number") == "required") {
            $roles["identity_number"] = "required";
        }
        if (config("settings.student_form_identity") == "required") {
            $roles["identity"] = "required| mimes:jpeg,jpg,bmp,png,gif,svg,pdf,zip | max:5000";
        }
        if (config("settings.student_form_image") == "required") {
//            $roles["image"] = "required|image| max:3000";
            $roles["image"] = "sometimes|required| max:3000";
        }
        if (config("settings.student_form_nationality") == "required") {
            $roles["nationality"] = "required";
        }
        if (config("settings.student_form_mobile") == "required") {
            $roles["mobile"] = "required";
        }
        if ($roles) {
            $this->validate($request, $roles);
        }
    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy(Request $request, Student $student)
    {


//        $str = 'User is deleted. You can <button class="btn btn-success revokeModalTriggerBtn" id="revokeModalTriggerBtn" data-stid=' . $request->id . ' title="Revoke">Revoke</button> the deleted student.';


        $id = is_null($request->id) == true ? $request->s_id : $request->id; // this ternary operation is place here because multiple places are calling this method. future refactoring is needed.
        $student = Student::findOrFail($id);


        $nm = $student->full_name; // show the name of student
        $student->delete_reason = $request->reason;
        $student->delete_notice = $request->notice;
        $student->archived_by = \Auth::id();
        $student->archived_by_ip = request()->ip();
        $student->status = 'archived';
        $student->save();



        $student->delete();

        if ($request->s_id) {
            return back();
        }
        return response()->json(["stId" => $request->id, "message" => 'Student: ' . $nm . ' Deleted !!']);
//
    }


    public function restoreStudent(Request $request)
    {


        $id = is_null($request->id) == true ? $request->stuId : $request->id; // this ternary operation is place here because multiple places are calling this method. future refactoring is needed.


        $student = Student::withTrashed()->findOrFail($id);

        $student->delete_reason = NULL;
        $student->delete_notice = NULL;
//        $student->deleted_at = NULL;
        $student->status = 'active';
        $student->save();

        $student->restore();

        return response()->json("Student: " . $student->full_name . " Revoked");
    }


    public function verify(Request $request)
    {


//        We all know that update() returns boolean but we want it to return our edited $user so we can pass it to our json response fo.Here come the rule of tap()
        $user = User::where('id', $request->get('userId'))->first();
        $user = tap($user, function ($user) {

            $user->update(
                [
                    'email_verified_at' => Carbon::now()->toDateTimeString()
                ]);


        });

        return response()->json("Successfull!  <b>" . $user->full_name . "</b> can login now");
    }

    public function returnStudentToPreviousHalaqah(Request $request)
    {


//        $id = is_null($request->id) == true ? $request->stuId : $request->id; // this ternary operation is place here because multiple places are calling this method. future refactoring is needed.

        if (\Auth::guard('student')->check()) {
            $student_id = \Auth::user()->id;
            $creator_role = 'student';
        } elseif (\Auth::guard('guardian')->check()) {

            $creator_role = 'guardian';
            $student_id = $request->stuId;

        } elseif (\Auth::guard('employee')->check()) {
            //  if(!auth()->user()->can('register student') || !$request->student_id){

            //     flash('Error. Not Authorized');

            //     return redirect()->back();
            //  }
            $creator_role = 'employee';
            $student_id = $request->stuId;
        }


        $student = Student::withTrashed()->findOrFail($student_id);
        $student->joint_classes()->updateExistingPivot($request->class_id, ['deleted_at' => NULL]);
        $admission = new Admission;

        $admission->organization_id = config('organization_id');
        $admission->student_id = $student_id;
        $admission->creator_role = $creator_role;
        $admission->created_by = \Auth::user()->id;;
        $admission->center_id = $request->center_id;
        $admission->class_id = $request->class_id;
        $admission->start_date = date('Y-m-d');
        $admission->status = 'reapplication';

        $admission->save();

        $admission->programs()->attach($request->program_id);

        $student->status = "active";
        $student->deleted_at = NULL;
        $student->save();

        $student->delete_reason = NULL;
        $student->delete_notice = NULL;
        $student->deleted_at = NULL;
//        $student->status = 'active';
        $student->save();


        return response()->json("Student: " . $student->full_name . " Revoked");
    }


    public function getStudentsJsonFormat(Request $request)
    {
        $requestedName = '%' . $request->q . '%';
        $name_condition = "display_name LIKE " . "'" . $requestedName . "'" . " OR full_name LIKE  " . "'" . $requestedName . "'" . " OR full_name_trans LIKE " . "'" . $requestedName . "'";

        $addmision = Admission::pluck('student_id');

        $student = Student::whereIn('id', $addmision)->whereRaw($name_condition)->get();


        $totalCounts = $student->count();
//        $my_query = "select * from students where " . $name_condition;


//        $student = DB::select($my_query, array($request->q));
//        $totalCounts = DB::select($my_query, array($request->q));
//        $totalCounts = count($totalCounts);

        $searchLang = is_arabic($request->q) == true ? "Arabic" : "English";

//        $student = Student::where("full_name",'LIKE','%'.$request->q.'%')->orWhere("full_name_trans",'LIKE','%'.$request->q.'%')->get();

        return response()->json(["total_count" => $totalCounts, "incomplete_results" => false, "items" => $student, 'language' => $searchLang], 200);

    }

    public function getCentersJsonFormat(Request $request)
    {
        $center = DB::select("select location as name, id as value from centers where deleted_at is null order by location");


        return response()->json(["success" => true, "results" => $center], 200);

    }

    public function getCenterBasedHalaqah(Request $request)
    {


        $center_id = $request->get('center_id');
        $classes = Classes::where('center_id',$center_id)
            ->get();


        return response()->json(["success" => true, "results" => $classes], 200);

    }

    public function getProgramsJsonFormat(Request $request)
    {
        $programs = DB::select("SELECT programs.id AS value, program_translations.title AS name FROM programs,program_translations WHERE programs.id = program_translations.program_id  and program_translations.locale= 'en'");


        return response()->json(["success" => true, "results" => $programs], 200);

    }

    public function getStudentStatusJsonFormat(Request $request)
    {
        $center = DB::select('SELECT a.value,
                CASE a.name  
                WHEN "active" then "Active"
                WHEN "new_admission" then "New Admission"
                WHEN "update_guardian" then "Update Guardian"
                WHEN "profile_completed" then "Profile Completed"
                else "Not Defined"  END AS "name"  FROM (SELECT DISTINCT STATUS AS NAME,STATUS AS value FROM students) a');


        return response()->json(["success" => true, "results" => $center], 200);

    }

    public function hasInput(Request $request)
    {
        if ($request->has('_token')) {
            return count($request->all()) > 1;
        } else {
            return count($request->all()) > 0;
        }
    }

}
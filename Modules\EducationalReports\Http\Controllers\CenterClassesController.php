<?php

namespace Modules\EducationalReports\Http\Controllers;


use App\Classes;
use App\ClassStudent;
use App\EvaluationSchemaOption;
use App\MoshafSurah;
use App\Student;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;


class CenterClassesController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    protected static function booted()
    {
        static::saved(function ($class) {
            // When a class is saved, clear the cache for that class
            \Cache::forget("students-classId-{$class->id}");
        });

        static::deleted(function ($class) {
            // When a class is deleted, clear the cache for that class
            \Cache::forget("students-classId-{$class->id}");
        });
    }


    /**
     * 🚀 ENHANCED: Get classes with comprehensive data including timetable, teachers, and center filtering
     * Supports both single center and multiple centers (comma-separated)
     * ✅ FIXED: Uses actual database structure with correct field names and relationships
     * ✅ OPTIMIZED: Removed pagination to load all classes for selected centers efficiently
     */
    public function getClassesEnhanced(Request $request)
    {
        try {
            $centerIds = $request->get('center_ids', []);

            // Handle null, empty string, and array cases
            if ($centerIds === null || $centerIds === '') {
                $centerIds = [];
            } elseif (is_string($centerIds)) {
                // Handle comma-separated string format
                if (trim($centerIds) === '') {
                    $centerIds = [];
                } else {
                    $centerIds = array_filter(explode(',', $centerIds));
                }
            }

            // Convert to integers and remove empty values (only if we have an array)
            if (is_array($centerIds)) {
                $centerIds = array_filter(array_map('intval', $centerIds));
            } else {
                $centerIds = [];
            }

            // Validate that center IDs are provided
            if (empty($centerIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No centers selected. Please select at least one center.',
                    'classes' => [],
                    'classes_by_center' => [],
                    'total_classes' => 0,
                    'centers_count' => 0,
                    'loaded_classes' => 0
                ], 400);
            }

            // ✅ FIXED: Use correct status value (0 = active) and proper relationships
            $query = Classes::with([
                'center:id,location', // ✅ FIXED: centers table uses 'location' not 'name'
                'timetable:id,class_id,mon,tue,wed,thu,fri,sat,sun,start_at,class_duration',
                'teachers' => function($query) {
                    $query->whereNull('class_teachers.end_date') // Only active teachers
                          ->whereNull('class_teachers.deleted_at'); // Not soft-deleted
                },
                'programs.programTranslations' // ✅ FIXED: Use direct relationship (already filtered by 'en')
            ])
            ->withCount(['students' => function($query) {
                $query->whereNull('class_students.deleted_at') // Only non-deleted students
                      ->whereNull('class_students.end_date'); // Only active enrollments
            }])
            ->where('status', '0') // ✅ FIXED: Use correct status value (0 = active)
            ->whereNull('deleted_at'); // Exclude soft-deleted classes

            // Apply center filtering if specified
            if (!empty($centerIds)) {
                $query->whereIn('center_id', $centerIds);
            }

            // ✅ OPTIMIZED: Load all classes without pagination for complete data retrieval
            $totalClasses = $query->count();
            $classes = $query->orderBy('center_id', 'asc')
                           ->orderBy('class_code', 'asc')
                           ->get();

            // Transform data to match MVP structure
            $transformedClasses = $classes->map(function ($class) {
                $timetable = $class->timetable;
                $schedule = [];
                $timeInfo = 'Not Set';

                if ($timetable) {
                    // Build schedule array from timetable days
                    $days = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
                    $dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

                    foreach ($days as $index => $day) {
                        if ($timetable->$day && $timetable->$day !== '00:00:00') {
                            $schedule[] = $dayNames[$index];
                        }
                    }

                    if ($timetable->start_at) {
                        $timeInfo = \Carbon\Carbon::parse($timetable->start_at)->format('g:i A');
                    }
                }

                // Get teacher name - ✅ FIXED: Use correct relationship structure
                $teacherName = 'Not Assigned';
                if ($class->teachers->isNotEmpty()) {
                    $activeTeacher = $class->teachers->first();
                    // Teachers relationship returns Employee models directly
                    if ($activeTeacher && $activeTeacher->full_name) {
                        $teacherName = $activeTeacher->full_name;
                    }
                }

                // Get program title
                $programTitle = 'Not Assigned';
                if ($class->programs->isNotEmpty()) {
                    $program = $class->programs->first();
                    if ($program && $program->programTranslations->isNotEmpty()) {
                        $programTitle = $program->programTranslations->first()->title;
                    }
                }

                return [
                    'id' => $class->id,
                    'name' => $class->class_code, // Use class_code as name
                    'class_code' => $class->class_code,
                    'center_id' => $class->center_id,
                    'center_name' => $class->center->location ?? 'Unknown Center', // ✅ FIXED: Use 'location'
                    'program' => $programTitle,
                    'subject' => 'Quran', // Default subject for Islamic education
                    'teacher' => $teacherName,
                    'students_count' => $class->students_count ?? 0,
                    'schedule' => !empty($schedule) ? implode('-', $schedule) : 'Not Set',
                    'time' => $timeInfo,
                    'duration' => $timetable->class_duration ?? null,
                    'status' => $class->status == '0' ? 'active' : 'inactive',
                    'level' => 'All Levels' // Default for now
                ];
            });

            // Group by center for organized display
            $classesByCenter = $transformedClasses->groupBy('center_name');

            return response()->json([
                'success' => true,
                'classes' => $transformedClasses,
                'classes_by_center' => $classesByCenter,
                'total_classes' => $totalClasses,
                'centers_count' => $classesByCenter->count(),
                'loaded_classes' => $transformedClasses->count()
            ]);

        } catch (\Exception $e) {
            \Log::error('Enhanced Classes Loading Error: ' . $e->getMessage(), [
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to load classes',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getClasses($centerId)
    {


        if ($centerId === 'all') {
            // Return all classes with center information
            $classes = \App\Classes::with(['center', 'programs'])
                ->withCount('students')
                ->get()
                ->map(function ($class) {
                    return [
                        'id' => $class->id,
                        'name' => $class->name,
                        'center_name' => $class->center->name ?? 'N/A',
                        'program_title' => $class->programs->first()->title ?? 'N/A',
                        'students_count' => $class->students_count
                    ];
                });
        } else {
            // Return classes for specific center
            $classes = \App\Classes::whereHas('center', function ($q) use ($centerId) {
                return $q->where('center_id', $centerId);
            })
            ->with(['center', 'programs'])
            ->withCount('students')
            ->get()
            ->map(function ($class) {
                return [
                    'id' => $class->id,
                    'name' => $class->name,
                    'center_name' => $class->center->name ?? 'N/A',
                    'program_title' => $class->programs->first()->title ?? 'N/A',
                    'students_count' => $class->students_count
                ];
            });
        }





        return response()->json($classes);
    }
}

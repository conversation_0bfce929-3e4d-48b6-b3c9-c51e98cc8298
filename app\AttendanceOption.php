<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;


class AttendanceOption extends Model
{
    public $table = 'attendance_options';
    public $casts = ['clock'];

//    protected $fillable = ['clock','note','organization_id','employee_id','type','location', 'device', 'ip','created_by'];
    protected $guarded = ['created_at' , 'updated_at'];


}

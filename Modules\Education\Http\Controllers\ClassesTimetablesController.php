<?php

namespace Modules\Education\Http\Controllers;

use App\BaseSetup;
use App\Employee;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\StudentHefzReport;
use App\Subject;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Matrix\Builder;
use Modules\UserActivityLog\Traits\LogActivity;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use Carbon\Carbon;
use App\ClassStudent;
use App\ClassTeacher;
use App\Http\Requests;
use App\ClassTeacherSubject;
use Illuminate\Http\Request;
use App\ClassSubjectTimetable;
use App\Http\Controllers\Controller;
use App\Cen_Emp;
use Tests\Psalm\LaravelPlugin\Models\Car;

class ClassesTimetablesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {


        try {
            $perPage = 0;

//            $classes = Classes::with([
//                'center',
//                'translations',
//                'timetable',
//                'programs'
//            ])
//                ->orderBy('class_code', 'asc')
//                ->paginate($perPage);

            // Fetch current user
            $employee = auth()->user();

// Initialize the query
            $query = Classes::with(['center', 'translations', 'timetable', 'programs'])->orderBy('class_code', 'asc');

// Check if the user is a Supervisor
            if ($employee->hasRole('supervisor_2_')) {
                // Get the center IDs that the supervisor has access to
                $centerIds = $employee->center()->pluck('cen_id')->toArray();

                // Restrict the query to only those centers
                $query->whereIn('center_id', $centerIds);
            }

// Admin and Education Manager can access all classes, so no need to modify the query for them

// Pagination and getting the classes
            $classes = $query->paginate($perPage);




            $year = Carbon::today()->year;
            $month = Carbon::today()->month;
            $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);




            $programs = \App\Program::with('translations')->get()->sortBy('title');



            return view('education::classes.timetables.index', compact('classes', 'year', 'month', 'days', 'programs'));
        } catch (\Exception $e) {

            dd($e->getMessage());
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }




}

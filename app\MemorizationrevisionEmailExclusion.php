<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MemorizationrevisionEmailExclusion extends Model
{

    // Explicit table name (optional if you follow <PERSON><PERSON>'s naming conventions)
    protected $table = 'memorizationrevision_email_exclusions';

    // Allow mass assignment for the email and reason fields.
    protected $fillable = [
        'email',
        'reason',
    ];

    use HasFactory;
}

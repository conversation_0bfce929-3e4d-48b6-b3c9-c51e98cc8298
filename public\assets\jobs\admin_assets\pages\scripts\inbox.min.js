var AppInbox=function(){var i=$(".inbox-content"),n=$(".inbox-loading"),t="",e=function(e,o){var a="app_inbox_inbox.html",c=$(".inbox-nav > li."+o+" a").attr("data-title");t=o,n.show(),i.html(""),h(e),$.ajax({type:"GET",cache:!1,url:a,dataType:"html",success:function(t){h(e),$(".inbox-nav > li.active").removeClass("active"),$(".inbox-nav > li."+o).addClass("active"),$(".inbox-header > h1").text(c),n.hide(),i.html(t),Layout.fixContentHeight&&Layout.fixContentHeight(),App.initUniform()},error:function(i,n,t){h(e)},async:!1}),jQuery("body").on("change",".mail-group-checkbox",function(){var i=jQuery(".mail-checkbox"),n=jQuery(this).is(":checked");jQuery(i).each(function(){$(this).attr("checked",n)}),jQuery.uniform.update(i)})},o=function(t,e,o){var a="app_inbox_view.html";n.show(),i.html(""),h(t);var c=t.parent("tr").attr("data-messageid");$.ajax({type:"GET",cache:!1,url:a,dataType:"html",data:{message_id:c},success:function(e){h(t),o&&$(".inbox-nav > li.active").removeClass("active"),$(".inbox-header > h1").text("View Message"),n.hide(),i.html(e),Layout.fixContentHeight(),App.initUniform()},error:function(i,n,e){h(t)},async:!1})},a=function(){$(".inbox-wysihtml5").wysihtml5({stylesheets:["../assets/global/plugins/bootstrap-wysihtml5/wysiwyg-color.css"]})},c=function(){$("#fileupload").fileupload({url:"../assets/global/plugins/jquery-file-upload/server/php/",autoUpload:!0}),$.support.cors&&$.ajax({url:"../assets/global/plugins/jquery-file-upload/server/php/",type:"HEAD"}).fail(function(){$('<span class="alert alert-error"/>').text("Upload server currently unavailable - "+new Date).appendTo("#fileupload")})},s=function(t){var e="app_inbox_compose.html";n.show(),i.html(""),h(t),$.ajax({type:"GET",cache:!1,url:e,dataType:"html",success:function(e){h(t),$(".inbox-nav > li.active").removeClass("active"),$(".inbox-header > h1").text("Compose"),n.hide(),i.html(e),c(),a(),$(".inbox-wysihtml5").focus(),Layout.fixContentHeight(),App.initUniform()},error:function(i,n,e){h(t)},async:!1})},l=function(t){var e=$(t).attr("data-messageid"),o="app_inbox_reply.html&messageid="+e;n.show(),i.html(""),h(t),$.ajax({type:"GET",cache:!1,url:o,dataType:"html",success:function(e){h(t),$(".inbox-nav > li.active").removeClass("active"),$(".inbox-header > h1").text("Reply"),n.hide(),i.html(e),$('[name="message"]').val($("#reply_email_content_body").html()),r(),c(),a(),Layout.fixContentHeight(),App.initUniform()},error:function(i,n,e){h(t)},async:!1})},r=function(){var i=$(".inbox-compose .mail-to .inbox-cc"),n=$(".inbox-compose .input-cc");i.hide(),n.show(),$(".close",n).click(function(){n.hide(),i.show()})},u=function(){var i=$(".inbox-compose .mail-to .inbox-bcc"),n=$(".inbox-compose .input-bcc");i.hide(),n.show(),$(".close",n).click(function(){n.hide(),i.show()})},h=function(i){"undefined"!=typeof i&&(i.attr("disabled")?i.attr("disabled",!1):i.attr("disabled",!0))};return{init:function(){$(".inbox").on("click",".compose-btn a",function(){s($(this))}),$(".inbox").on("click",".inbox-discard-btn",function(i){i.preventDefault(),e($(this),t)}),$(".inbox").on("click",".reply-btn",function(){l($(this))}),$(".inbox-content").on("click",".view-message",function(){o($(this))}),$(".inbox-nav > li.inbox > a").click(function(){e($(this),"inbox")}),$(".inbox-nav > li.sent > a").click(function(){e($(this),"sent")}),$(".inbox-nav > li.draft > a").click(function(){e($(this),"draft")}),$(".inbox-nav > li.trash > a").click(function(){e($(this),"trash")}),$(".inbox-content").on("click",".mail-to .inbox-cc",function(){r()}),$(".inbox-content").on("click",".mail-to .inbox-bcc",function(){u()}),"view"===App.getURLParameter("a")?o():"compose"===App.getURLParameter("a")?s():$(".inbox-nav > li.inbox > a").click()}}}();jQuery(document).ready(function(){AppInbox.init()});
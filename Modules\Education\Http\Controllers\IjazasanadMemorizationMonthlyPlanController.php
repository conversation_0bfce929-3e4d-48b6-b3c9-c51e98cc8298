<?php

namespace Modules\Education\Http\Controllers;

use App\Attendance;
use App\AttendanceOption;
use App\Cen_Emp;
use App\ClassProgram;
use App\IjazasanadMemorizationPlan;
use App\MoshafJuz;
use App\Student;
use App\StudentHefzPlan;
use App\StudentNouranyaPlan;
use App\YearCheck;
use http\Client\Curl\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Modules\EducationalReports\Http\Requests\CreateHefzPlanRequest;
use Modules\Education\Http\Requests\ClassReportStoreRequest;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use App\Subject;
use Carbon\Carbon;
use App\ClassReport;
use App\MoshafSurah;
use App\ClassTeacher;
use App\LessonReport;
use App\Http\Requests;
use App\EvaluationSchema;
use App\StudentAttendance;
use App\StudentHefzReport;
use Illuminate\Http\Request;
use App\StudentRevisionReport;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class IjazasanadMemorizationMonthlyPlanController extends Controller
{


    public function __construct()
    {
//        $this->middleware('writeCurrentClassReportOnly', ['only' => ['show']]);

        // User::checkAuth();
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request, $id)
    {


        $surats = MoshafSurah::all();
        $hefzEvaluationOptions = EvaluationSchema::where('target', 'hefz')->first()->options;
        // return $this->studentReport(1);
        $request = request();


        $class = Classes::withTrashed()->find($id);


        // handling the wrong $id
        if (is_null($class)) {
            flash('Class ' . $id . ' does not exist');
            return redirect()->to("workplace/education/classes");
        }

        if (!is_null($class->deleted_at)) {

            // return redirect back and flush a message stating that the $class->class_code is archived
            flash('Class ' . $class->class_code . ' is archived and is not accessable. please try another class');
            return redirect()->to("workplace/education/classes");

        }
        $class = Classes::with('programs.settings')->findOrFail($id);
//        $class =  Classes::findOrFail($id);

        if ($request->from_date) {
            $from_date = Carbon::parse($request->from_date);
        } else {
            $from_date = Carbon::create(date('Y'), date('m'), 01);
        }

        if ($request->to_date) {
            $to_date = Carbon::parse($request->to_date);
        } else {
            $to_date = $from_date->copy()->addMonth();
        }
        $class_teachers = ClassTeacher::where('class_id', $class->id)
            ->where('end_date', null)->get();

        $class_programs = [];

        foreach ($class->programs as $key => $program) {
            $data = [];
            $data['info'] = $program;


            if (isset($program->setting['special_program_code']) && $program->setting['special_program_code'] == 'hefz') {
                $teacher = $class_teachers->filter(function ($teacher) use ($program) {
                    return (count($teacher->subjects) && $teacher->subjects->filter(function ($subject) use ($program) {
                            return $subject->program_id == $program->id;
                        })->count());
                })->first();

                if (!$teacher) {
                    return $this->errorNoTeacher($class_id);
                }

                if (!auth()->user()->can('view class_reports') && $teacher && $teacher->employee_id != auth()->user()->id) {
                    continue;
                }
                $data['teacher'] = $teacher;
                $data['type'] = 'program';

                if ($teacher) {
                    $data['timetable'] = $teacher->subjects()->where('program_id', $program->id)->first()->timetable;
                    if (!$data['timetable']) {
                        return $this->errorNoTimetable($class->id);
                    }
                    $data['class_teacher_subject_id'] = $teacher->subjects()->where('program_id', $program->id)->first()->id;

                    $last_report = ClassReport::where('class_id', $class->id)
                        ->where('program_id', $program->id)
                        ->where('status', 'completed')
                        ->get()->last();

                    $data['next_report_date'] = $this->getNextReportTime($last_report->class_time ?? null, $data['timetable'], $class);
                }
            } else {
                $data['type'] = 'subjects';
                if (isset($program->levels->find($program->pivot->program_level_id)->subjects)) {
                    foreach ($program->levels->find($program->pivot->program_level_id)->subjects as $index => $subject) {
                        $teacher = $class_teachers->filter(function ($teacher) use ($subject) {
                            return (count($teacher->subjects) && in_array($subject->id, $teacher->subjects->pluck('subject_id')->toArray()));
                        })->first();


                        if (!$teacher) {
                            return $this->errorNoTeacher($class->id);
                        }

                        if (!auth()->user()->can('view class_reports') && $teacher->employee_id != auth()->user()->id) {
                            continue;
                        }
                        $data['class_subjects'][$index] = $subject;
                        $data['class_subjects'][$index]['teacher'] = $teacher;
                        if ($teacher) {
                            $data['class_subjects'][$index]['timetable'] = $teacher->subjects()->where('subject_id', $subject->id)->first()->timetable;
                            if (!$data['class_subjects'][$index]['timetable']) {
                                return $this->errorNoTimetable($class->id);
                            }
                            $data['class_subjects'][$index]['class_teacher_subject_id'] = $teacher->subjects()->where('subject_id', $subject->id)->first()->id;
                            $last_report = ClassReport::where('class_id', $class->id)
                                ->where('subject_id', $subject->id)
                                ->where('status', 'completed')
                                ->get()->last();
                            $data['next_report_date'] = $this->getNextReportTime($last_report->class_time ?? null, $data['class_subjects'][$index]['timetable'], $class);
                        }
                    }
                }
                if (!isset($data['class_subjects']) || !count($data['class_subjects'])) {
                    continue;
                }
            }

            $class_programs[$key] = $data;
        }

        $report_summery = [];

        $class_subjects = [];

        $class_reports = ClassReport::where('class_id', $class->id)
            ->where('class_time', ">=", $from_date)
            ->where('class_time', "<=", $to_date)
            ->get();

        $classReportsIds = $class_reports->pluck('id')->toArray();
        for ($i = $from_date->copy(); $i <= date('Y/m/d') && $i <= $to_date->copy(); $i->addDay()) {
            $report_summery[$i->format("Y/m/d")]["y"] = $i->format("Y/m/d");
        }

        $class_subjects_reports = [];

        foreach ($class_reports as $key => $report) {
            $report_summery[$report->class_time->format("Y/m/d")]["y"] = $report->class_time->format("Y/m/d");
            if ($report->subject_id) {
                $class_subjects_reports['subject_' . $report->subject_id][$report->class_time->format("Y/m/d")] = $report;
                $report_summery[$report->class_time->format("Y/m/d")][$report->subject->title] = $report->attendace->where('attendance', "!=", "absent")->where('attendance', "!=", "excused")->count();
                if (!in_array($report->subject->title, $class_subjects)) {
                    $class_subjects[] = $report->subject->title;
                }
            }
            if ($report->program_id) {
                $class_subjects_reports['program_' . $report->program_id][$report->class_time->format("Y/m/d")] = $report;
                $report_summery[$report->class_time->format("Y/m/d")][$report->program->title] = $report->attendace->where('attendance', "!=", "absent")->where('attendance', "!=", "excused")->count();
                if (!in_array($report->program->title, $class_subjects)) {
                    $class_subjects[] = $report->program->title;
                }
            }
        }
        $teacher = null;
        $teacher_timetable = null;
        if ($class->teachers->where('employee_id', auth()->user()->id)->first()) {
            $teacher = $class->teachers->where('employee_id', auth()->user()->id)->first();
        }


        //Student Attendance

        $students = Student::whereHas('class', function ($q) use ($id) {
            $q->where('class_id', $id);
        })->pluck('id')->toArray();

        $students_full_details = Student::whereHas('class', function ($q) use ($id) {
            $q->where('class_id', $id);
        })->get();


        $year = Carbon::parse($request->from_date)->year;
        $month = Carbon::parse($request->from_date)->month;
        $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);


        $attendances = [];

        $attendance = StudentAttendance::whereIn('student_id', $students)
            ->whereIn('class_report_id', $classReportsIds)
            ->whereYear('class_time', '=', $year)
            ->whereMonth('class_time', '=', $month)
            ->get();

        if (count($attendance) != 0) {
            $attendances[] = $attendance;
        }


        // return $class_programs;
        return view('education::classes.reports.index', compact('attendances', 'days', 'year', 'month', 'current_day', 'class', 'teacher_timetable', 'teacher', 'from_date', 'to_date', 'report_summery', 'class_programs', 'class_subjects_reports', 'class_subjects', 'students_full_details', 'surats', 'hefzEvaluationOptions'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create(Request $request, $id)
    {


        $class = Classes::findOrFail($id)->with('students.hefz_plans');

        $students = $class->students()->get();
        $report_id = $request->get('report_id');
        $from_date = $request->from_date;
        $surats = MoshafSurah::all();
        $teachers = $class->teachers()->pluck('employees.name', 'employees.id');
        $hefz_evaluation_schema = EvaluationSchema::where('target', 'hefz')->first();
        $revision_evaluation_schema = EvaluationSchema::where('target', 'revision')->first();
        $hefz_valuation_options = $hefz_evaluation_schema->options()->get();
        $revision_valuation_options = $revision_evaluation_schema->options()->get();

//        if (isset($report_id)){
        if (isset($from_date)) {
            foreach ($students as $student) {
//                $student->hefz_report = $student->hefz()->where('class_report_id',$report_id)->first();
                $student->hefz_report = $student->hefz()->whereDate('created_at', '=', Carbon::parse($from_date)->toDate())->first();


                $student->hefz_plans->start_from_ayat = $this->getAyatListBySurat($student->hefz_plans->hefz_from_surat, $student->hefz_plans->start_from_ayat);
                $student->hefz_plans->to_ayat = $this->getAyatListBySurat($student->hefz_plans->hefz_to_surat, $student->hefz_plans->to_ayat);

//                $student->revision_report = $student->revision()->where('class_report_id',$report_id)->first();
                $student->revision_report = $student->revision()->whereDate('created_at', Carbon::parse($from_date)->toDate())->first();

                $student->revision_report->revision_from_ayat = $this->getAyatListBySurat($student->revision_report->revision_from_surat, $student->revision_report->revision_from_ayat);
                $student->revision_report->revision_to_ayat = $this->getAyatListBySurat($student->revision_report->revision_to_surat, $student->revision_report->revision_to_ayat);
            }
        }

        //dd($hefz_valuation_options);
        return view('education::classes.reports.create', compact('students', 'class', 'from_date', 'surats', 'teachers', 'hefz_valuation_options', 'revision_valuation_options'));


        // Old stuffs
        if (in_array(auth()->user()->id, $class->teachers()->pluck('employee_id')->toArray())) {
            //$teachers = [auth()->user()->id => auth()->user()->full_name];
        } else {
            //$teachers = $class->teachers()->pluck('full_name', 'employee_id');
        }

        $subjects = [];

        foreach ($class->programs as $program) {
            if ($program->pivot->program_level_id == 0) {
                $subjects['p' . $program->id] = $program->title . ' Program [All levels & Subjects]';
            } else {
                foreach ($program->levels as $level) {
                    if ($level->id == $program->pivot->program_level_id) {
                        foreach ($level->subjects as $subject) {
                            $subjects[$subject->id] = $subject->title;
                        }
                    }
                }
            }
        }

        // $subjects = $class->programs[0]->subjects;

        // return $class->programs[1]->levels[1]->subjects;

        return view('education::classes.reports.create', compact('class', 'teachers', 'subjects'));
    }

    /**
     * Get list of ayats based on surah
     *
     * @param $surah_id
     * @param $ayat_num
     * @return string
     */
    public function getAyatListBySurat($surah_id, $ayat_num): string
    {
        $surah = MoshafSurah::find($surah_id);
        $options = '<option value="" selected>Select</option>';

        if (isset($surah)) {
            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {
                if ($i === $ayat_num) {
                    $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                } else {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';
                }
            }
        }

        return $options;
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $organization_id = $request->get('organization_id');
        $table = $request->get('table');
        $planYearMonth = $request->get('from_date');
        $dateMonthArray = explode('-', $planYearMonth);
        $year = $dateMonthArray[0];
        $month = $dateMonthArray[1];
        $planYearMonth = Carbon::createFromDate($year, $month, 1);
        $planYearMonth = $planYearMonth->format('Y-m');

        $hefz_from_surat = $request->get('start_from_surat');
        $start_from_ayat = $request->get('start_from_ayat');
        $to_surat = $request->get('to_surat');
        $to_ayat = $request->get('to_ayat');
        
        // Level 1 specific fields
        $talqeen_from_lesson = $request->get('talqeen_from_lesson');
        $talqeen_to_lesson = $request->get('talqeen_to_lesson');
        $revision_from_lesson = $request->get('revision_from_lesson');
        $revision_to_lesson = $request->get('revision_to_lesson');
        $jazariyah_from_lesson = $request->get('jazariyah_from_lesson');
        $jazariyah_to_lesson = $request->get('jazariyah_to_lesson');
        $seminars_from_lesson = $request->get('seminars_from_lesson');
        $seminars_to_lesson = $request->get('seminars_to_lesson');

        $from_surat_juz_id = DB::select("SELECT * FROM moshaf_juz WHERE :startSuratId BETWEEN start_surah AND end_surah;", array(
            'startSuratId' => $hefz_from_surat,
        ));

        $to_surat_juz_id = DB::select("SELECT * FROM moshaf_juz WHERE :lastSurahId BETWEEN start_surah AND end_surah;", array(
            'lastSurahId' => $to_surat,
        ));

        $from_surat_juz_id = collect($from_surat_juz_id)->first()->juz;
        $to_surat_juz_id = collect($to_surat_juz_id)->last()->juz;

        $student = Student::whereHas('joint_classes', function ($query) use ($request) {
            $query->whereRaw('classes.id=' . $request->get('class_id'));
        })->find($request->get('student_id'));

        // get center id
        $supervisorCenterId = Classes::where('id', $request->get('class_id'))->first()->center_id;

        $studyDirection = $this->determineStudyDirection($hefz_from_surat, $to_surat, $start_from_ayat, $to_ayat);

        if ($studyDirection == 'backward') {
            $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                $hefz_from_surat,
                $start_from_ayat,
                $to_surat,
                $to_ayat
            ]);

            $numberofPages = $numberofPages[0]->numberofPagesSum;
        }
        else {
            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                $hefz_from_surat,
                $start_from_ayat,
                $to_surat,
                $to_ayat
            ]);

            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
            $numberofPages = $results[0]->number_of_pages_sum;
        }

        // status column default value is waiting_for_approval which is set in the DB side.
        $plan = $student->ijazasanad_memorization_plans()->updateOrCreate(
            [
                'class_id' => $request->get('class_id'),
                'student_id' => $student->id,
                "plan_year_and_month" => $planYearMonth
            ],
            [
                'organization_id' => $organization_id,
                'deleted_at' => NULL,
                "start_from_surat" => !empty($hefz_from_surat) ? $hefz_from_surat : null,
                "start_from_ayat" => !empty($start_from_ayat) ? $start_from_ayat : null,
                "to_surat" => !empty($to_surat) ? $to_surat : null,
                "to_ayat" => !empty($to_ayat) ? $to_ayat : NULL,
                "start_date" => $request->get('from_date'),
                'level_id' => studentLevel($hefz_from_surat, $to_surat),
                'center_id' => $supervisorCenterId,
                "from_surat_juz_id" => $from_surat_juz_id,
                "to_surat_juz_id" => $to_surat_juz_id,
                "study_direction" => $studyDirection,
                // Level 1 specific fields
                'talqeen_from_lesson' => $talqeen_from_lesson,
                'talqeen_to_lesson' => $talqeen_to_lesson,
                'revision_from_lesson' => $revision_from_lesson,
                'revision_to_lesson' => $revision_to_lesson,
                'jazariyah_from_lesson' => $jazariyah_from_lesson,
                'jazariyah_to_lesson' => $jazariyah_to_lesson,
                'seminars_from_lesson' => $seminars_from_lesson,
                'seminars_to_lesson' => $seminars_to_lesson,
            ]
        );

        // Determine if this is a Level 1 student
        $isLevel1Student = false;
        if ($student->studentProgramLevels && $student->studentProgramLevels->isNotEmpty()) {
            $programLevelTitle = $student->studentProgramLevels->first()->programlevel->title ?? '';
            $isLevel1Student = stripos($programLevelTitle, 'Level 1: Preparation Course') !== false;
        }

        // Apply status logic based on student level
        if ($isLevel1Student) {
            // For Level 1 students: Check if ALL FOUR entry pairs are completely filled
            $allLevel1FieldsFilled = (
                !empty($talqeen_from_lesson) && !empty($talqeen_to_lesson) &&
                !empty($revision_from_lesson) && !empty($revision_to_lesson) &&
                !empty($jazariyah_from_lesson) && !empty($jazariyah_to_lesson) &&
                !empty($seminars_from_lesson) && !empty($seminars_to_lesson)
            );

            if ($allLevel1FieldsFilled) {
                $plan->status = 'waiting_for_approval';
            } else {
                // If not all Level 1 fields are filled, show no status (empty string)
                $plan->status = '';
            }
        } else {
            // For non-Level 1 students: Use the original logic
            $checkIfQuranRelatedColumnsHaveValues = ($plan->start_from_surat && $plan->start_from_ayat && $plan->to_surat && $plan->to_ayat);

            if ($checkIfQuranRelatedColumnsHaveValues == true) {
                $plan->status = 'waiting_for_approval';
            } else {
                $plan->status = '';
            }
        }

        $plan->save();

        $checkIfAllRequiredColumnsHaveValues = (is_null($plan->study_direction) || is_null($plan->start_from_surat) || is_null($plan->start_from_ayat) || is_null($plan->to_surat) || is_null($plan->to_ayat)) == true ? false : true;

        return response()->json(['message' => 'success', 'plan' => $plan, 'numberofPages' => $numberofPages, 'allRequiredFieldsFilled' => $checkIfAllRequiredColumnsHaveValues, 'table' => $table], 200);
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function show(Request $request, $id, $date)
    {


        $from_date_year = Carbon::parse($date)->year;
        $from_date_month = Carbon::parse($date)->month;
        $from_date = $from_date_year . '-' . $from_date_month;

        $class = Classes::whereId($id)
            ->with(['students.hefz_plans' => function ($query) use ($from_date_year, $from_date_month) {
                $query->whereYear('start_date', $from_date_year)
                    ->whereMonth('start_date', $from_date_month);
            }])->with(['students.revision_plans' => function ($query) use ($from_date_year, $from_date_month) {
                $query->whereYear('start_date', $from_date_year)
                    ->whereMonth('start_date', $from_date_month);
            }])
            ->get();
       $programId =  ClassProgram::where('class_id',$id)->first()->program_id;


       $classProgramDetails = Program::where('id',$programId)->first();

        $classStudents = Student::whereHas('joint_classes', function ($q) use ($id) {
            $q->where('class_id', $id);
        })
            ->with('user')->with(['joint_classes' => function ($q) use ($id) {

                $q->where('class_id', $id);

            }])
            ->with(['hefz_plans' => function ($query) use ($from_date_year, $from_date_month) {
                $query->whereYear('start_date', $from_date_year)
                    ->whereMonth('start_date', $from_date_month);
            }])
            ->with(['revision_plans' => function ($query) use ($from_date_year, $from_date_month) {
                $query->whereYear('start_date', $from_date_year)
                    ->whereMonth('start_date', $from_date_month);
            }])
            ->with(['nouranya_plans' => function ($query) use ($from_date_year, $from_date_month,$id) {
                $query->whereYear('start_date', $from_date_year)
                    ->whereMonth('start_date', $from_date_month)
                    ->where ('class_id', $id);
            }])
            ->withCount(['hefz_plans as memorizationPlan_with_non_null_fields_count' => function ($q) {
                $q->whereNotNull('start_from_surat')
                    ->whereNotNull('start_from_ayat')
                    ->whereNotNull('to_surat')
                    ->whereNotNull('to_ayat');
            }])->withCount(['revision_plans as revisionPlan_with_non_null_fields_count' => function ($q) {
                $q->whereNotNull('start_from_surat')
                    ->whereNotNull('start_from_ayat')
                    ->whereNotNull('to_surat')
                    ->whereNotNull('to_ayat');
            }])  ->with(['lastApprovedMemorizationPlan'])
            ->with(['lastApprovedRevisionPlan'])
            ->where('status', 'active')->withCount('reports')
            ->get();

        $surats = MoshafSurah::all();
        $hefz_evaluation_schema = EvaluationSchema::where('target', 'hefz')->first();
        $revision_evaluation_schema = EvaluationSchema::where('target', 'revision')->first();

        if ($revision_evaluation_schema) {
            $hefz_valuation_options = $revision_evaluation_schema->options()->get();
            // Continue with your code logic using $hefz_valuation_options
        } else {
            // Handle the case when $hefz_evaluation_schema is null
            // For example, you can log an error message or provide a default value.
            $hefz_valuation_options = '';
        }
        $revision_valuation_options = $revision_evaluation_schema->options()->get();

        if (isset($from_date)) {
            foreach ($class as $classDtails) {
                foreach ($classDtails->students as $student) {
                    foreach ($student->hefz_plans as $key => $hefz_plans) {
                        $hefz_plans->start_from_ayat_select_dropdown = $this->getAyatListBySurat($hefz_plans->start_from_surat, $hefz_plans->start_from_ayat);
                        $hefz_plans->to_ayatPlain = $hefz_plans->to_ayat;
                        $hefz_plans->to_ayat_select_dropdown = $this->getAyatListBySurat($hefz_plans->to_surat, $hefz_plans->to_ayat);
                        $student->revision_report = $student->revision()->whereDate('created_at', Carbon::parse($from_date)->toDate())->first();

                        optional($student->revision_report)->revision_from_ayat = $this->getAyatListBySurat(optional($student->revision_report)->revision_from_surat, optional($student->revision_report)->revision_from_ayat);
                        optional($student->revision_report)->revision_to_ayat = $this->getAyatListBySurat(optional($student->revision_report)->revision_to_surat, optional($student->revision_report)->revision_to_ayat);
                    }
                }
            }

                foreach ($classStudents as $student) {

                    foreach ($student->hefz_plans as $key => $hefz_plans) {

                        $hefz_plans->start_from_ayat_select_dropdown = $this->getAyatListBySurat($hefz_plans->start_from_surat, $hefz_plans->start_from_ayat);
                        $hefz_plans->to_ayatPlain = $hefz_plans->to_ayat;
                        $hefz_plans->to_ayat_select_dropdown = $this->getAyatListBySurat($hefz_plans->to_surat, $hefz_plans->to_ayat);


                        $student->revision_report = $student->revision()->whereDate('created_at', Carbon::parse($from_date)->toDate())->first();

                        optional($student->revision_report)->revision_from_ayat = $this->getAyatListBySurat(optional($student->revision_report)->revision_from_surat, optional($student->revision_report)->revision_from_ayat);
                        optional($student->revision_report)->revision_to_ayat = $this->getAyatListBySurat(optional($student->revision_report)->revision_to_surat, optional($student->revision_report)->revision_to_ayat);
                    }
                }




        }
        return view('education::monthlyPlan.create', compact('classProgramDetails','classStudents', 'id', 'class', 'from_date', 'surats', 'hefz_valuation_options', 'revision_valuation_options'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($class_id, $report_id)
    {
        $surats = MoshafSurah::all();

        $report = ClassReport::findOrFail($report_id);

        $class = Classes::findOrFail($report->class_id);
        $subject = [];
        $special_program_data = [];

        if ($report->subject_id == 0) {
            if ($report->program->setting['special_program_code']) {
                if ($report->program->setting['special_program_code'] = 'hefz') {
                    $special_program_data['data'] = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->evaluation($report);

                    $special_program_data['hefz_evaluation_schema'] = EvaluationSchema::where('target', 'hefz')->first();

                    $special_program_data['revision_evaluation_schema'] = EvaluationSchema::where('target', 'revision')->first();
                }
            }
        } else {
            $subject = Subject::findOrFail($report->subject_id);
        }

        $students = [];
        // return $special_program_data;
        // return $subject->contents;

        return view('education::classes.reports.edit', compact('class', 'report', 'students', 'subject', 'special_program_data', 'surats'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request)
    {
        $requestData = $request->all();
        // return $requestData;

        $report = ClassReport::findOrFail($request->report_id);
        if (isset($request->teacher_attendance) && $request->teacher_attendance == 'absent') {
            $report->status = 'completed';
            $report->notes = 'teacher is absent';
            $report->save();

            return redirect('workplace/education/classes/' . $report->class_id . '/reports');
        } elseif (isset($request->student_attendance)) {
            foreach ($request->student_attendance as $key => $value) {
                $attendance = new StudentAttendance();

                $attendance->organization_id = config('organization_id');
                $attendance->class_report_id = $report->id;
                $attendance->student_id = $key;
                $attendance->class_time = $report->class_time;
                $attendance->attendance = $value;
                $attendance->created_by = auth()->user()->id;

                $attendance->save();
                // $attendance->note = $report->;
            }


            $report->status = 'attendance_submited';

            $report->save();
        } elseif (isset($request->student_performance)) {
            if ($report->subject_id == 0) {
                if ($report->program->setting['special_program_code']) {
                    if ($report->program->setting['special_program_code'] = 'hefz') {
                        $report_data = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->evaluation($report);
                        foreach ($request->student_performance as $student_id => $result) {
                            if (isset($report_data[$student_id])) {
                                if (isset($report_data[$student_id]['hefz']) && $report_data[$student_id]['hefz'] && $result['hefz']) {
                                    $hefz_report = new StudentHefzReport();

                                    $hefz_plans->student_id = $student_id;
                                    $hefz_plans->organization_id = config('organization_id');
                                    $hefz_plans->class_id = $report->class_id;
                                    $hefz_plans->class_time = $report->class_time;
                                    $hefz_plans->created_by = auth()->user()->id;
                                    // $hefz_plans->hefz_from_surat = $report_data[$student_id]['hefz']['from_surat'];
                                    // $hefz_plans->start_from_ayat = $report_data[$student_id]['hefz']['from_ayat'];
                                    // $hefz_plans->hefz_to_surat = $report_data[$student_id]['hefz']['to_surat'];
                                    // $hefz_plans->to_ayat = $report_data[$student_id]['hefz']['to_ayat'];

                                    $hefz_plans->hefz_from_surat = $requestData['report'][$student_id]['hefz']['from_surat'];
                                    $hefz_plans->start_from_ayat = $requestData['report'][$student_id]['hefz']['from_ayat'];
                                    $hefz_plans->hefz_to_surat = $requestData['report'][$student_id]['hefz']['to_surat'];
                                    $hefz_plans->to_ayat = $requestData['report'][$student_id]['hefz']['to_ayat'];

                                    $hefz_plans->hefz_evaluation_id = $result['hefz'];

                                    $hefz_plans->class_report_id = $report->id;


                                    $hefz_plans->save();
                                }
                                if (isset($report_data[$student_id]['revision']) && $report_data[$student_id]['revision'] && $result['revision']) {
                                    $revision_report = new StudentRevisionReport();

                                    $revision_report->student_id = $student_id;
                                    $revision_report->organization_id = config('organization_id');
                                    $revision_report->class_id = $report->class_id;
                                    $revision_report->created_by = auth()->user()->id;
                                    // $revision_report->revision_from_surat = $report_data[$student_id]['revision']['from_surat'];
                                    // $revision_report->revision_from_ayat = $report_data[$student_id]['revision']['from_ayat'];
                                    // $revision_report->revision_to_surat = $report_data[$student_id]['revision']['to_surat'];
                                    // $revision_report->revision_to_ayat = $report_data[$student_id]['revision']['to_ayat'];

                                    $revision_report->revision_from_surat = $requestData['report'][$student_id]['revision']['from_surat'];
                                    $revision_report->revision_from_ayat = $requestData['report'][$student_id]['revision']['from_ayat'];
                                    $revision_report->revision_to_surat = $requestData['report'][$student_id]['revision']['to_surat'];
                                    $revision_report->revision_to_ayat = $requestData['report'][$student_id]['revision']['to_ayat'];

                                    if (isset($requestData['report'][$student_id]['revision']['revision_note'])) {
                                        $revision_report->revision_evaluation_note = $requestData['report'][$student_id]['revision']['revision_note'];
                                    }
                                    if (isset($requestData['report'][$student_id]['revision']['revision_type'])) {
                                        $revision_report->revision_type = $requestData['report'][$student_id]['revision']['revision_type'];
                                    }


                                    $revision_report->revision_evaluation_id = $result['revision'];

                                    $revision_report->class_report_id = $report->id;

                                    $revision_report->save();
                                }
                            }
                        }

                        $report->status = 'completed';

                        $report->save();
                    }
                }
            } else {
            }
        }

        // return $requestData;

        Session::flash('flash_message', 'Class updated!');

        return redirect('workplace/education/classes/' . $report->class_id . '/reports/' . $report->id . '/edit');
    }


    /**
     * Update programs availabilty in class.
     *
     * @param int $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function programs(Request $request)
    {
        auth()->user()->can('edit class_programs');

        $id = $request->class_id;

        $class = Classes::findOrFail($id);

        $class->programs()->sync($request->class_programs);

        Session::flash('flash_message', 'Program updated!');

        if ($request->ajax()) {
            return response()->json(['status' => 'success']);
        }

        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Classes::destroy($id);

        Session::flash('flash_message', 'Class deleted!');

        return redirect('workplace/education/classes');
    }


    public function studentReport($student_id)
    {
        return DB::table('student_attendances')
            ->leftJoin('class_reports', 'student_attendances.class_report_id', 'class_reports.id')
            ->leftJoin('student_hefz_report', 'student_attendances.class_report_id', '=', 'student_hefz_report.class_report_id')
            ->leftJoin('evaluation_schema_options as hefz_evaluation', 'hefz_evaluation.id', '=', 'student_hefz_report.hefz_evaluation_id')
            ->leftJoin('student_revision_report', 'student_attendances.class_report_id', '=', 'student_revision_report.class_report_id')
            ->leftJoin('evaluation_schema_options as revision_evaluation', 'revision_evaluation.id', '=', 'student_revision_report.revision_evaluation_id')
            ->select(
                'student_attendances.attendance',
                'student_attendances.note as student_attendance_note',
                'class_reports.*',
                'student_hefz_report.*',
                'hefz_evaluation.title as hefz_evaluation_title',
                'student_revision_report.*',
                'revision_evaluation.title as revision_evaluation_title'
            )
            ->where('student_attendances.student_id', $student_id)
            ->orderBy('class_reports.class_time')
            // ->select('users.*', 'contacts.phone', 'orders.price')
            ->get();
    }

    public function studentExpectedProgressPlan($student_id) // Hefz and Morja'ah Program
    {
    }

    private function getNextReportTime($last_report_time, $timetable, $class)
    {

        if (!$last_report_time) {
            $class_date = $timetable->start_at;
        } else {
            $class_date = Carbon::parse($last_report_time);
            $class_date = $class_date->addDay();
//            $class_date = $last_report_time->addDay();
        }

        while ($class->studentsAtDate($class_date)->count() < 1 && $class_date < Carbon::now()) {
            $class_date = $class_date->addDay();
        }

        while (!$timetable[strtolower($class_date->format('D'))]) {
            $class_date = $class_date->addDay();
        }
        $class_date = $class_date->addDay();
        // $class_date = $class_date->addDay();
        // dump($class_date);

        return $class_date;
    }

    private function errorNoTeacher($class_id)
    {
        $error_msg = "Opps! . Some Subjects In the Class Don't have Teacher!!";

        return view('education::classes.errors', compact('class_id', 'error_msg'));
    }

    private function errorNoTimetable($class_id)
    {
        $error_msg = "Opps! . Some Subjects In the Class Don't have Timetable!!";

        return view('education::classes.errors', compact('class_id', 'error_msg'));
    }


    // V2

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\View\View
     */
    public function prepareReport($class_id, $report_id)
    {
        $report = ClassReport::findOrFail($report_id);


        if ($report->status == 'completed') {
            return redirect('workplace/education/classes/' . $report->class_id . '/reports/' . $report->id . '/edit');
        }
        $surats = MoshafSurah::all();

        $suar = $surats->map(function ($surah) {
            return [
                'text' => $surah->name,
                'value' => $surah->id,
                'num_ayat' => $surah->num_ayat
            ];
        });


        $class = Classes::with('students.hefz')->with('students.revision')->findOrFail($report->class_id);


        $subject = [];
        $special_program_data = [];
        $special_program = null;

        $hefzEvaluationOptions = [];
        $revisionEvaluationOptions = [];

        if ($report->subject_id == 0 && $report->program->setting['special_program_code'] && $report->program->setting['special_program_code'] = 'hefz') {
            $special_program_data = app('Modules\Education\Http\Controllers\SpecialPrograms\HefzController')->studentsEvaluation($report);

            $hefzEvaluationOptions = EvaluationSchema::where('target', 'hefz')->first()->options->map(function ($option) {
                return ['text' => $option->code . ' - ' . $option->title, 'value' => $option->id];
            });

            $revisionEvaluationOptions = EvaluationSchema::where('target', 'revision')->first()->options->map(function ($option) {
                return ['text' => $option->code . ' - ' . $option->title, 'value' => $option->id];
            });

            $special_program = 'hefz';
        } else {
            $subject = Subject::findOrFail($report->subject_id);
        }
        // dd($special_program_data);
        $students = [];
        $lessons = [];
        // $class->students->map(function($student){
        //     return [

        //     ]
        // });

        foreach ($class->students as $student) {
            if ($special_program
                && $special_program == 'hefz'
                && isset($special_program_data[$student->id])
            ) {
                $students[$student->id] = $special_program_data[$student->id];
                $students[$student->id]['attandance'] = null;
                $students[$student->id]['evaluation'] = null;
            } else {
                $students[$student->id] = [
                    'attandance' => null,
                    'evaluation' => null,
                    'hefz' => [],
                    'revision' => [],
                    'lesson' => [
                        'id' => null,
                        'evaluations' => json_decode('{}')
                    ]
                ];
                if ($subject) {
                    $lessons = $subject->contents->map(function ($lesson) {
                        $evaluation_schems = [];
                        foreach ($lesson->evaluation_schemas as $evaluation) {
                            foreach ($evaluation->options as $option) {
                                $evaluation_schems[$evaluation->title][] = [
                                    'text' => $option->title,
                                    'value' => $option->id
                                ];
                            }
                        }

                        return [
                            'text' => $lesson->title,
                            'value' => $lesson->id,
                            'evaluation_schems' => $evaluation_schems
                        ];
                    });
                }
            }
        }


        return view('education::classes.reports.v3.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
//        return view('education::classes.reports.v2.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
//        return view('education::classes.reportsBackup.v2.create', compact('class', 'suar', 'report', 'students', 'subject', 'lessons', 'special_program', 'hefzEvaluationOptions', 'revisionEvaluationOptions', 'surats'));
    }

    public function storeTempReport($class_id, $report_id)
    {

        $report = ClassReport::findOrFail($report_id);
        $report->temp_data = json_encode(request()->except('_token'));
        $report->save();
    }

    public function storeFinalReport(Request $request, $class_id, $report_id)
    {
        // dd($request->all());
        $this->validate($request, [
            'students.*' => 'required',
            'teacher_attended' => 'required'
        ]);

        $report = ClassReport::findOrFail($report_id);

        StudentAttendance::where('class_report_id', $report->id)->delete();
        StudentHefzReport::where('class_report_id', $report->id)->delete();
        StudentRevisionReport::where('class_report_id', $report->id)->delete();


        $requestData = $request->all();

        if (!$request->teacher_attended) {
            $report->status = 'completed';
            $report->notes = 'teacher is absent';
            $report->temp_data = '';
            $report->save();

            return response()->json(['status' => 'completed'], 200);
        }

        foreach ($request->students as $studentID => $studentReport) {
            $attendance = new StudentAttendance();

            $attendance->organization_id = config('organization_id');
            $attendance->class_report_id = $report->id;
            $attendance->student_id = $studentID;
            $attendance->class_time = $report->class_time;
            $attendance->attendance = $studentReport['attandance'];
            $attendance->created_by = auth()->user()->id;
            $attendance->save();

            if (in_array($attendance->attendance, ['on_time', 'late'])) {
                if ($report->subject_id == 0 && $report->program->setting['special_program_code'] && $report->program->setting['special_program_code'] = 'hefz') {
                    if (isset($studentReport['hefz']) && $studentReport['hefz']) {
                        $hefz_report = new StudentHefzReport();
                        $hefz_plans->student_id = $studentID;
                        $hefz_plans->organization_id = config('organization_id');
                        $hefz_plans->class_id = $report->class_id;
                        $hefz_plans->class_time = $report->class_time;
                        $hefz_plans->created_by = auth()->user()->id;

                        $hefz_plans->hefz_from_surat = $studentReport['hefz']['from_surat'];
                        $hefz_plans->start_from_ayat = $studentReport['hefz']['from_ayat'];
                        $hefz_plans->hefz_to_surat = $studentReport['hefz']['to_surat'];
                        $hefz_plans->to_ayat = $studentReport['hefz']['to_ayat'];

                        $hefz_plans->hefz_evaluation_id = $studentReport['hefz']['evaluation'];
                        $hefz_plans->class_report_id = $report->id;

                        $hefz_plans->save();
                    }
                    if (isset($studentReport['revision']) && $studentReport['revision']) {
                        $revision_report = new StudentRevisionReport();

                        $revision_report->student_id = $studentID;
                        $revision_report->organization_id = config('organization_id');
                        $revision_report->class_id = $report->class_id;
                        $revision_report->created_by = auth()->user()->id;


                        $revision_report->revision_from_surat = $studentReport['revision']['from_surat'];
                        $revision_report->revision_from_ayat = $studentReport['revision']['from_ayat'];
                        $revision_report->revision_to_surat = $studentReport['revision']['to_surat'];
                        $revision_report->revision_to_ayat = $studentReport['revision']['to_ayat'];

                        if (isset($studentReport['revision']['revision_note'])) {
                            $revision_report->revision_evaluation_note = $studentReport['revision']['revision_note'];
                        }
                        if (isset($studentReport['revision']['revision_type'])) {
                            $revision_report->revision_type = $studentReport['revision']['revision_type'];
                        }


                        $revision_report->revision_evaluation_id = $studentReport['revision']['evaluation'];

                        $revision_report->class_report_id = $report->id;

                        $revision_report->save();
                    }
                } else {
                    // Todo: ADD REPORT OF NORMAL PROGRAM
                    if (isset($studentReport['lesson']) && $studentReport['lesson']) {
                        $lesson_report = new LessonReport();

                        $lesson_report->student_id = $studentID;
                        $lesson_report->organization_id = config('organization_id');
                        $lesson_report->class_id = $report->class_id;
                        $lesson_report->created_by = auth()->user()->id;
                        $lesson_report->class_time = $report->class_time;


                        $lesson_report->lesson_id = $studentReport['lesson']['id'];

                        if (isset($studentReport['lesson']['note'])) {
                            $lesson_report->note = $studentReport['revision']['note'];
                        }

                        $lesson_report->class_report_id = $report->id;
                        $lesson_report->save();
                        foreach ($studentReport['lesson']['evaluations'] as $label => $evaluation_option) {
                            $lesson_report->evaluations()->create(['evaluation_option_id' => $evaluation_option]);
                        }
                    }
                }
            }
        }
        $report->temp_data = '';
        $report->status = 'completed';
        $report->save();
        return response()->json(['status' => 'completed'], 200);
    }

    public static function determineStudyDirection($hefz_from_surat, $to_surat, $start_from_ayat, $to_ayat)
    {


        if (!empty($hefz_from_surat) && !empty($to_surat)) {

            if (($hefz_from_surat == $to_surat) && $start_from_ayat < $to_ayat) {


                return 'forward';

            }

            if (($hefz_from_surat == $to_surat) && $start_from_ayat > $to_ayat) {


                return 'backward';

            }


            if ($hefz_from_surat > $to_surat) {

                return 'backward';

            }

            if ($hefz_from_surat < $to_surat) {

                return 'forward';

            }
        }

    }

    public function updateToLessonLineNumber(Request $request, $studentId)
    {
        $validatedData = $request->validate([
            'program_level_id' => 'required',
            'fromDate' => 'required',
            'classId' => 'required|integer|exists:classes,id',
            'centerId' => 'required|integer|exists:centers,id',
            'lineNumber' => 'required|string', // Assuming line numbers are strings
            'lessonNo' => 'required|integer', // Validate the lesson ID
            // other fields if necessary...
        ]);

        DB::beginTransaction();
        try {
            $date = \Carbon\Carbon::createFromFormat('F Y', $validatedData['fromDate']);
            $planYearAndMonth = $date->format('Y-m');
            $planYearAndMonthDay = $date->startOfMonth()->format('Y-m-d');

            // Check if there's an existing record with a different class ID
            $existingRecord = IjazasanadMemorizationPlan::where('student_id', $studentId)
                ->where('plan_year_and_month', $planYearAndMonth)
                ->first();

            // If a record exists for the same month but for a different class, create a new plan entry for the current class
            if ($existingRecord && $existingRecord->class_id != $validatedData['classId']) {
                // Create a new record
                $ijazasanadMemorizationPlan = IjazasanadMemorizationPlan::create([
                    'student_id' => $studentId,
                    'plan_year_and_month' => $planYearAndMonth,
                    'class_id' => $validatedData['classId'],
                    'to_lesson_line_number' => $validatedData['lineNumber'],
                    'center_id' => $validatedData['centerId'],
                    'to_lesson' => $validatedData['toLesson'], // Include lesson ID
                    'start_date' => $planYearAndMonthDay,
                    'status' => 'waiting_for_approval', // Set the status to 'waiting_for_approval'

                    // Include additional fields that need updating...
                ]);
            } else {
                // Update or create the record
                $ijazasanadMemorizationPlan = IjazasanadMemorizationPlan::updateOrCreate(
                    [
                        'student_id' => $studentId,
                        'plan_year_and_month' => $planYearAndMonth,
                        'class_id' => $validatedData['classId'],

                    ],
                    [
                        'to_lesson_line_number' => $validatedData['lineNumber'],
                        'center_id' => $validatedData['centerId'],
//                        'to_lesson' => $validatedData['toLesson'], // Include lesson ID
                        'start_date' => $planYearAndMonthDay,
                        'status' => 'waiting_for_approval', // Set the status to 'waiting_for_approval'

                        // Include additional fields that need updating...
                    ]
                );
                $message = $existingRecord ? 'Existing plan updated and set to waiting for approval.' : 'Plan created and set to waiting for approval.';

            }

            DB::commit();
//            return response()->json(['success' => true, 'message' => $message, 'status' => $nouranyaPlan->status]);
            return response()->json(['success' => true, 'message' => $message, 'status' => 'Waiting for Approval']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to update student's to_lesson_line_number: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update to lesson line number. '.$e->getMessage()], 500);
        }
    }

    public function updateToLesson(Request $request, $studentId)
    {


        $validatedData = $request->validate([
            'program_level_id' => 'required',
            'fromDate' => 'required',
            'classId' => 'required|integer|exists:classes,id',
            'centerId' => 'required|integer|exists:centers,id',
            'toLesson' => 'required|integer',
            // other fields if necessary...
        ]);

//        DB::beginTransaxction();
        try {

            $date = \Carbon\Carbon::createFromFormat('F Y', $validatedData['fromDate']);
            $planYearAndMonth = $date->format('Y-m');
            $planYearAndMonthDay = $date->startOfMonth()->format('Y-m-d');

            // Check if there's an existing record with a different class ID
            $existingRecord = IjazasanadMemorizationPlan::where('student_id', $studentId)
                ->where('plan_year_and_month', $planYearAndMonth)
                ->first();

            if ($existingRecord && $existingRecord->class_id != $validatedData['classId']) {
                // If a record exists but for a different class, create a new plan entry for the current class


                // Create a new record
                $ijazasanadMemorizationPlan = IjazasanadMemorizationPlan::create([
                    'student_id' => $studentId,
                    'plan_year_and_month' => $planYearAndMonth,
                    'class_id' => $validatedData['classId'],
                    'to_lesson' => $validatedData['toLesson'],
                    'center_id' => $validatedData['centerId'],
                    'start_date' => $planYearAndMonthDay,
                    'status' => 'waiting_for_approval',  // Set status to waiting_for_approval for new records

                    // Include additional fields that need updating...
                ]);
            } else {


                $ijazasanadMemorizationPlan = IjazasanadMemorizationPlan::updateOrCreate(
                    [
                        'student_id' => $studentId,
                        'plan_year_and_month' => $planYearAndMonth,
                        'class_id' => $validatedData['classId'],
                    ],
                    [
                        'organization_id' => config('organization_id'), // Assuming organization_id comes from the request and is validated
                        'status' => 'waiting_for_approval', // For example, set a default status
                        'to_lesson' => $validatedData['toLesson'],
                        'center_id' => $validatedData['centerId'],
                        'start_date' => $planYearAndMonthDay,
                        // Include additional fields that need updating...
                    ]
                );
            }

            // Return a success response with the current status
            return response()->json([
                'success' => true,
                'message' => 'To lesson updated successfully.',
//                'status' => $nouranyaPlan->status // Include the status in the response
                'status' => 'Waiting for Approval' // Include the status in the response
            ]);
        } catch (\Exception $e) {
//            DB::rollBack();
            Log::error("Failed to update student's to_lesson: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to update to_lesson. '.$e->getMessage()], 500);
        }
    }



    public function getIjazasanadBasedClasses()
    {
        try {
            // Assuming you have a model called `NouranyaPlan` which is linked to the classes
            $nouranyaplanBasedClasses = IjazasanadMemorizationPlan::with('halaqah')
                ->get()
                ->map(function ($nouranyaPlan) {
                    // Check if the halaqah relationship is loaded and is not null
                    if ($nouranyaPlan->halaqah) {
                        return [
                            'id' => $nouranyaPlan->halaqah->id,
                            'name' => $nouranyaPlan->halaqah->name
                        ];
                    }
                })
                ->filter() // This will remove any null values that might have been added in the map due to missing halaqahs
                ->unique('id') // This will filter out duplicate classes by their 'id'
                ->values(); // This will reset the keys to ensure they are sequential after filtering


            return response()->json($nouranyaplanBasedClasses);
        } catch (\Exception $e) {
            // Handle the error accordingly
            Log::error('Error fetching nouranyaplan-based classes: ' . $e->getMessage());
            return response()->json(['error' => 'Unable to fetch classes'], 500);
        }
    }



}

<?php

namespace App;

use App\Notifications\EmployeeResetPassword;
use App\Scopes\EmployeeCenterAccessScope;
use App\Scopes\OrganizationScope;
use HashmatWaziri\LaravelMultiAuthImpersonate\Models\Impersonate;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

use Modules\Account\Entities\ChartAccount;
use Modules\Leave\Entities\ApplyLeave;
use Modules\Leave\Entities\LeaveDefine;
use Modules\Payroll\Entities\Payroll;

use Modules\Sale\Entities\Sale;
use Modules\Setup\Entities\ApplyLoan;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\SystemViewerAccess;
use App\Relations\SystemViewerBelongsToMany;

class Employee extends Authenticatable
{
    use Notifiable, SoftDeletes, Impersonate, <PERSON><PERSON><PERSON><PERSON>, HasFactory, SystemViewerAccess;
    // Set the guard_name to match the permissions table
    protected $guard_name = 'employee';

    protected $casts = ['clock'];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name'
        , 'full_name'
        , 'employee_number'
        , 'full_name_trans'
        , 'gender'
        , 'nationality'
        , 'email'
        , 'mobile'
        , 'date_of_birth'
        , 'password'
        , 'organization_id'
        , 'marital_status'
        , 'archived_by'
        , 'status'
        , 'archived_by_ip'
        , 'salary'
        , 'bank_account_no'
        , 'work_mood'
        , 'hours_per_month'
        , 'resume'
        , 'joining_letter'
        , 'other_document'
        , 'epf_no'
        , 'contract_type'
        , 'bank_name'
        , 'bank_account_no'
        , 'bank_account_name'
        , 'identity_type'
        , 'identity_number'
        , 'start_at'
        , 'address_1'
        , 'address_2'
        , 'address_state'
        , 'address_city'
        , 'address_country'
        , 'department_id'
        , 'leave_applicable_date',
        'archive_reason',
        'archive_other_reason',
        'employee_creation_email_sent_at',
        'employee_creation_email'

    ];

    // public static function boot()
    // {
    //     static::addGlobalScope(new OrganizationScope);
    // }

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * Get the path to the employee's profile image.
     *
     * Returns the employee's custom image if it exists, otherwise
     * returns a default image based on the employee's gender.
     *
     * @param  string|null  $value
     * @return string
     */
    public function getImageAttribute($value)
    {
        if (!empty($value)) {
            // If value contains https, return it as is (external URL)
            if (str_contains($value, 'https')) {
                return $value;
            }
            
            // Check if local file exists
            if (file_exists(public_path($value))) {
                return $value;
            }
        }

        return strtolower($this->gender) === 'female'
            ? 'assets/workplace/hound/img/defaultFemaleEmployeeImage.png'
            : 'assets/workplace/hound/img/defaultEmployeeImage.png';
    }
    // protected $attributes = array(
    //    'organization_id' => 1//Config::get('organization_id'),
    // );


    public function exams()
    {
        return $this->hasMany(OnlineExam::class);
    }
    public function is($roleName)
    {
        foreach ($this->roles()->get() as $role)
        {
            if ($role->type == $roleName)
            {
                return true;
            }
        }

        return false;
    }

    public function roleIds()
    {
        $roleIdArr = [];
        foreach ($this->roles()->get() as $role)
        {

            $roleIdArr[] = $role->id ;

        }

        return $roleIdArr;
    }

    public function workMode()

    {


        return $this->belongsTo(EmployeeWorkMood::class,'work_mood','id');
    }

    public function buildMenu($menu, $parentid = 0)
    {
        $result = null;
        foreach($menu as $item)
            if ($item->reports_to == $parentid) {
                $result .= "<li class='dd-item nested-list-item' data-order='{$item->order}' data-id='{$item->id}'>
          <div class='dd-handle nested-list-handle'>
            <i class='fas fa-arrows-alt'></i>
          </div>
          <div class='nested-list-content'>{$item->full_name}
            <div class='float-right'>
              <a href='/admin/menusnav/{$item->id}'>Edit</a> |
              <a href='#' class='delete_toggle text-danger' rel='{$item->id}'>Delete</a>
            </div>
          </div>".$this->buildMenu($menu, $item->id) . "</li>";
            }
        return $result ?  "\n<ol class=\"dd-list\">\n$result</ol>\n" : null;
    }
    // Getter for the HTML menu builder
    public function getHTML($items)
    {
        return $this->buildMenu($items);
    }

    public function documents()
    {

        return $this->morphMany('App\Document', 'documentable');

    }

    protected static function boot()
    {
        parent::boot();
        // dd(config('organization_id'));

        static::addGlobalScope(new OrganizationScope);

    }

    public function sendPasswordResetNotification($token)
    {


        $email = $this->email;
        $name  = $this->full_name;


            dispatch(new \App\Jobs\SendEmployeeResetPasswordEmailJob($token,$email,$name));

            \Log::error('Failed to send APPLICATION-RECEIVED email after 5 attempts');
            return back()->withErrors(['error' => 'Something went wrong. Please try again later.']);

        // TODO: this feature should be reenabled after the Laravel Default Mail start to work
//        $this->notify(new EmployeeResetPassword($token));
    }

    public function organization()
    {
        return $this->belongsTo('App\Organization');
    }

    // if employee is teacher, he'll have classes

//    public function classes()
//    {
//        return $this->hasMany("App\ClassTeacher", 'employee_id')->where("end_date", null);
//    }

    public function classes()
    {
//        return $this->belongsToMany(Classes::class, 'class_teachers','employee_id','class_id')->withTimestamps()->withPivot('id');
        return $this->belongsToMany(Classes::class, 'class_teachers','employee_id','class_id')->withTimestamps()->withPivot('id')->whereNull('class_teachers.deleted_at');
    }

    public function hasClasses()
    {
        // Optionally log or dd() to inspect
        \Log::info("User {$this->id} has classes: " . ($hasClasses ? 'Yes' : 'No'));

        $hasClasses = $this->classes()->exists();
        return $hasClasses;

    }



    public function salaries()
    {
        return $this->hasMany('App\EmployeeSalary');
    }

//    public function salaryReports()
//    {
//        return $this->hasMany('App\EmployeeSalaryReport');
//    }

//    public function getCurrentSalaryAttribute($query)
//    {
//        return $this->salaries->where('end_at', null)->first();
//    }

    public function salaryOn($date)
    {

        return $this->salaries()->where('start_at', '<=', $date)->where(function ($query) use ($date) {
            $query->where('end_at', null)->orWhere('end_at', '>=', $date);
        })->first();
    }

    public function salaryReportOn($date)
    {
        return $this->salaryReports()->where('report_month', $date)->first();
    }


    public function canImpersonate()
    {
        // For example
//        return $this->role_id == 24 || $this->role_id == 1 ;
        return true;

    }

    public static function takeRedirectTo()
    {
        return url('/workplace/dashboard');
    }

    /**
     * The URI to redirect after leaving an impersonation.
     *
     */
    public static function leaveRedirectTo()
    {
        return url('/workplace/dashboard');
    }



    public function leaveType(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {

        return $this->belongsToMany(LeaveType::class,'leave_defines','employee_id','leave_type_id');

    }



    public function center()
    {
        return $this->belongsToMany(
            \App\Center::class,
            'cen_emps',
            'emp_id',
            'cen_id'
        )
        ->using(\App\CenterEmployee::class)
        ->withTimestamps()
        ->when($this->hasRole('managing-director_' . config('organization_id') . '_'), function ($query) {
            // For managing directors, remove any existing where clauses and return all centers
            return $query->withoutGlobalScopes();
        });
    }



    public function teacherCenter()
    {

        return $this->belongsToMany(Center::class, 'cen_teachers', 'emp_id', 'cen_id')
            ->withTimestamps();
    }
    public function loans()
    {
        return $this->hasMany(ApplyLoan::class)->where('approval',1);
    }
    public function getAccountsAttribute()
    {
        $sales = $this->sales;
        $payable_amount = $sales->sum('payable_amount');

        $paid_amount = 0;
        $sales_return_amount = 0;
        $debit_amount = 0;
        $crebit_amount = 0;
        $total_amount = 0;
        $chart_account = ChartAccount::where('contactable_type', 'App\User')->where('contactable_id', $this->id)->first();
        $sales = $this->sales;
        $payable_amount = $sales->sum('payable_amount');

        foreach ($sales as $sale)
        {
            $paid_amount += ($sale->payments->sum('amount') - $sale->payments->sum('return_amount'));
            $sales_return_amount += $sale->items->sum('return_amount');
        }
        $total_amount = $payable_amount + $this->agent->opening_balance - $sales_return_amount;
        $due_amount = $chart_account->BalanceAmount + $this->agent->opening_balance;

        $accounts['total'] = $total_amount;
        $accounts['paid'] = $paid_amount;
        $accounts['due'] = $due_amount;
        $accounts['total_invoice'] = count($sales);
        $accounts['due_invoice'] = count($sales->where('status', '!=', 1));

        return $accounts;
    }
    public function lastInvoice()
    {
        return $this->hasOne(Sale::class,'agent_user_id')->latest();
    }
    public function getLoanInfoAttribute()
    {
        $loans = $this->loans;

        $total_loan = $loans->sum('amount');

        $total_paid = $loans->sum('paid_loan_amount');

        $total_due = $total_loan - $total_paid;

        return [
            'total_loan' => $total_loan,
            'total_paid' => $total_paid,
            'total_due' => $total_due,
        ];
    }

    public function departments()
    {

        return $this->belongsTo(Department::class)->withDefault();
//        return $this->belongsToMany(Department::class, 'employee_department', 'employee_id', 'department_id')
//            ->using(EmployeeDepartment::class)->withTimestamps();
    }



    public function payrolls(){
        return $this->hasMany(Payroll::class, 'employee_id', 'id');
    }
    public function bankAccounts()
    {

        return $this->hasMany(BankAccount::class, 'emp_id');
    }
    public function user()
    {
        return $this->belongsTo(User::class);
    }


    /**
     * Scope a query to only include popular users.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCenterSpecific($query)
    {

        // if an authenticated employee role is supervisor, then show the related employees he supervises across center(s)

        if (auth()->guard('employee')->user()->hasRole('supervisor_2_')) {

            $query->where('votes', '>', 100);
        }
        return $query->where('votes', '>', 100);
    }




    public function attendance()
    {

        return $this->hasMany(Attendance::class, 'employee_id');
    }

    public function timetable()
    {
        return $this->hasMany('App\EmployeeTimetable');
    }
    public function timetableModal()
    {
        return $this->hasMany('App\EmployeeTimetable')->getModel();
    }

    /**
     * Remove all current days and set the given ones.
     *
     *
     * @return $this
     */
    public function syncTimeTable($request,$employeeId)
    {
       $days = $request->get('days');

        \DB::table("employee_timetables")->whereNotIn('day',$days)->where('employee_id',$employeeId)->delete();

                foreach ($days as $day) {


                    switch ($day) {
                        case 'mon':
                            $d_no = 1;
                            break;
                        case 'tue':
                            $d_no = 2;
                            break;
                        case 'wed':
                            $d_no = 3;
                            break;
                        case 'thu':
                            $d_no = 4;
                            break;
                        case 'fri':
                            $d_no = 5;
                            break;
                        case 'sat':
                            $d_no = 6;
                            break;
                        case 'sun':
                            $d_no = 7;
                            break;


                        default:break;
                    }


                    EmployeeTimetable::updateOrCreate([
                        'employee_id' => $employeeId,
                        'day' => $day
                    ],[
                        'clockin' => '09:00:00',
                        'clockout' => '18:00:00',
                        'break' => '0',
                        'day_order' => $d_no
                    ]);
            }

    }


    public function hefzPlan(){

        return $this->hasManyThrough(StudentHefzPlan::class, CenterEmployee::class,'emp_id','center_id','id');

    }


    public function missedClockouts(): \Illuminate\Database\Eloquent\Relations\hasMany
    {

        return $this->hasMany(MissedClockOut::class)->whereNULL('deleted_at');
    }



    public function leaves()
    {
        return $this->hasMany(ApplyLeave::class)->CarryForward();
    }

    public function leaveDefines()
    {
        return $this->hasMany(LeaveDefine::class,'employee_id','id');
    }

    public function getCarryForwardAttribute()
    {
        $total_leave = $this->leaveDefines->sum('total_days');
        $leave = $this->leaves->sum('total_days');

        return $total_leave - $leave;
    }


    public function teams()
    {
        return $this->belongsToMany(Team::class, 'team_user', 'user_id', 'team_id');
    }










}

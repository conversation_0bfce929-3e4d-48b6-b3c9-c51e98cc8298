<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * Outgoing Email Model - Transactional Outbox Pattern Implementation
 * 
 * This model represents emails stored using the transactional outbox pattern,
 * ensuring atomic persistence before any sending attempts. Based on Oracle's
 * enterprise microservices patterns for ultimate data integrity.
 * 
 * Key Features:
 * - Atomic email storage before sending attempts
 * - Complete lifecycle tracking (pending -> sent/failed)
 * - Automatic retry management with configurable limits
 * - Full error history and recovery capabilities
 * - Export functionality for external processing
 * 
 * @property int $id
 * @property string $recipient
 * @property string $subject
 * @property string $body
 * @property string $provider
 * @property string $mode
 * @property string $priority
 * @property array|null $email_data
 * @property string $status
 * @property int $send_attempts
 * @property int $max_attempts
 * @property string|null $last_error_message
 * @property array|null $error_history
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon|null $scheduled_at
 * @property Carbon|null $first_attempt_at
 * @property Carbon|null $sent_at
 * @property Carbon|null $failed_at
 * @property string|null $recovery_notes
 * @property Carbon|null $exported_at
 * @property string|null $exported_by
 */
final class OutgoingEmail extends Model
{
    protected $table = 'outgoing_emails';
    
    protected $fillable = [
        'recipient',
        'subject', 
        'body',
        'provider',
        'mode',
        'priority',
        'email_data',
        'idempotency_key',
        'jobset_hash',
        'minute_bucket',
        'status',
        'send_attempts',
        'max_attempts',
        'last_error_message',
        'error_history',
        'scheduled_at',
        'first_attempt_at',
        'sent_at',
        'failed_at',
        'recovery_notes',
        'exported_at',
        'exported_by',
    ];

    protected $casts = [
        'email_data' => 'array',
        'error_history' => 'array',
        'scheduled_at' => 'datetime',
        'first_attempt_at' => 'datetime',
        'sent_at' => 'datetime',
        'failed_at' => 'datetime',
        'exported_at' => 'datetime',
        'send_attempts' => 'integer',
        'max_attempts' => 'integer',
    ];

    /**
     * Email status constants
     */
    public const STATUS_PENDING = 'pending';
    public const STATUS_QUEUED = 'queued';
    public const STATUS_SENDING = 'sending';
    public const STATUS_SENT = 'sent';
    public const STATUS_FAILED = 'failed';
    public const STATUS_CANCELLED = 'cancelled';
    public const STATUS_RETRY_SCHEDULED = 'retry_scheduled';

    /**
     * Priority constants
     */
    public const PRIORITY_HIGH = 'high';
    public const PRIORITY_NORMAL = 'normal';
    public const PRIORITY_LOW = 'low';

    /**
     * Provider constants
     */
    public const PROVIDER_GMAIL = 'gmail';
    public const PROVIDER_MAILTRAP = 'mailtrap';
    public const PROVIDER_MAIL = 'mail';

    /**
     * Create a new outgoing email using transactional outbox pattern
     * 
     * This method implements the atomic "write before send" principle:
     * 1. Email data is persisted BEFORE any sending attempt
     * 2. Initial status is 'pending' for immediate processing
     * 3. All metadata is captured for complete audit trail
     * 
     * @param string $recipient
     * @param string $subject
     * @param string $body
     * @param string $provider
     * @param string $mode
     * @param array $emailData Additional data (attachments, CC, etc.)
     * @param string $priority
     * @param int $maxAttempts
     * @return self
     */
    public static function createInOutbox(
        string $recipient,
        string $subject,
        string $body,
        string $provider,
        string $mode = 'sync',
        array $emailData = [],
        string $priority = self::PRIORITY_NORMAL,
        int $maxAttempts = 3
    ): self {
        // Compute idempotency metadata from jobs in view_data
        $jobs = $emailData['view_data']['jobs'] ?? [];
        // Include category context if available to reduce collisions across different categories
        $categoryIdForContext = null;
        if (isset($emailData['view_data']['category']) && is_array($emailData['view_data']['category'])) {
            $categoryIdForContext = $emailData['view_data']['category']['id'] ?? null;
        } elseif (isset($emailData['view_data']['setup']) && is_object($emailData['view_data']['setup'])) {
            // Fallback: derive a stable hash from associated category IDs on the setup if passed
            try {
                if (method_exists($emailData['view_data']['setup'], 'categories')) {
                    $ids = $emailData['view_data']['setup']->categories()->pluck('job_categories.id')->toArray();
                    sort($ids);
                    $categoryIdForContext = hash('sha256', implode(',', $ids));
                }
            } catch (\Throwable $e) {
                // ignore
            }
        }
        $fingerprints = [];
        foreach ($jobs as $j) {
            $fpParts = [
                strtolower(trim((string)($j['id'] ?? ''))),
                strtolower(trim((string)($j['title'] ?? $j['position'] ?? ''))),
                strtolower(trim((string)($j['company'] ?? $j['company_name'] ?? ''))),
                strtolower(trim((string)($j['locations'] ?? $j['location'] ?? ''))),
            ];
            $fingerprints[] = implode('|', $fpParts);
        }
        sort($fingerprints);
        $jobsetHash = hash('sha256', implode('||', $fingerprints));
        $minuteBucket = now()->format('YmdHi');
        $idempotencyKeySeed = strtolower($recipient) . '|' . $jobsetHash . '|' . $minuteBucket;
        if ($categoryIdForContext !== null) {
            $idempotencyKeySeed .= '|' . (string) $categoryIdForContext;
        }
        $idempotencyKey = hash('sha256', $idempotencyKeySeed);

        $email = new self([
            'recipient' => $recipient,
            'subject' => $subject,
            'body' => $body,
            'provider' => $provider,
            'mode' => $mode,
            'priority' => $priority,
            'email_data' => $emailData,
            'idempotency_key' => $idempotencyKey,
            'jobset_hash' => $jobsetHash,
            'minute_bucket' => $minuteBucket,
            'status' => self::STATUS_PENDING,
            'send_attempts' => 0,
            'max_attempts' => $maxAttempts,
        ]);

        try {
            $email->save();
        } catch (\Throwable $e) {
            // If duplicate idempotency key occurs due to race or retry, fetch and return the existing row
            if (str_contains(strtolower($e->getMessage()), 'duplicate') || str_contains(strtolower($e->getMessage()), 'unique')) {
                $existing = self::where('idempotency_key', $idempotencyKey)->first();
                if ($existing) {
                    Log::channel('email')->warning('OutgoingEmail: Duplicate idempotency key detected; returning existing record', [
                        'recipient' => $recipient,
                        'subject' => $subject,
                        'idempotency_key' => $idempotencyKey,
                    ]);
                    return $existing;
                }
            }
            throw $e;
        }

        Log::channel('email')->info('OutgoingEmail: Created in transactional outbox', [
            'outgoing_email_id' => $email->id,
            'recipient' => $recipient,
            'subject' => $subject,
            'provider' => $provider,
            'mode' => $mode,
            'status' => $email->status,
        ]);

        return $email;
    }

    /**
     * Mark email as queued (for async processing)
     */
    public function markAsQueued(): void
    {
        $this->update([
            'status' => self::STATUS_QUEUED,
        ]);

        Log::channel('email')->info('OutgoingEmail: Marked as queued', [
            'outgoing_email_id' => $this->id,
            'recipient' => $this->recipient,
        ]);
    }

    /**
     * Mark sending attempt started
     */
    public function markSendingStarted(): void
    {
        $updates = [
            'status' => self::STATUS_SENDING,
            'send_attempts' => $this->send_attempts + 1,
        ];

        // Set first attempt timestamp if this is the first attempt
        if ($this->send_attempts === 0) {
            $updates['first_attempt_at'] = now();
        }

        $this->update($updates);

        Log::channel('email')->info('OutgoingEmail: Sending attempt started', [
            'outgoing_email_id' => $this->id,
            'recipient' => $this->recipient,
            'attempt_number' => $this->send_attempts,
        ]);
    }

    /**
     * Mark email as successfully sent
     */
    public function markAsSent(): void
    {
        $this->update([
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
            'last_error_message' => null, // Clear any previous errors
        ]);

        Log::channel('email')->info('OutgoingEmail: Successfully sent', [
            'outgoing_email_id' => $this->id,
            'recipient' => $this->recipient,
            'final_attempt' => $this->send_attempts,
            'provider' => $this->provider,
        ]);
    }

    /**
     * Mark sending attempt as failed with error tracking
     */
    public function markSendFailed(string $errorMessage): void
    {
        // Add error to history
        $errorHistory = $this->error_history ?? [];
        $errorHistory[] = [
            'attempt' => $this->send_attempts,
            'error' => $errorMessage,
            'timestamp' => now()->toISOString(),
            'provider' => $this->provider,
        ];

        $updates = [
            'last_error_message' => $errorMessage,
            'error_history' => $errorHistory,
        ];

        // Determine if this is a permanent failure
        if ($this->send_attempts >= $this->max_attempts) {
            $updates['status'] = self::STATUS_FAILED;
            $updates['failed_at'] = now();
        } else {
            // Schedule for retry
            $updates['status'] = self::STATUS_RETRY_SCHEDULED;
            $updates['scheduled_at'] = $this->calculateNextRetryTime();
        }

        $this->update($updates);

        if ($this->status === self::STATUS_FAILED) {
            Log::channel('email')->error('OutgoingEmail: Permanently failed after max attempts', [
                'outgoing_email_id' => $this->id,
                'recipient' => $this->recipient,
                'total_attempts' => $this->send_attempts,
                'final_error' => $errorMessage,
            ]);
        } else {
            Log::channel('email')->warning('OutgoingEmail: Send failed, scheduled for retry', [
                'outgoing_email_id' => $this->id,
                'recipient' => $this->recipient,
                'attempt' => $this->send_attempts,
                'retry_at' => $updates['scheduled_at'],
                'error' => $errorMessage,
            ]);
        }
    }

    /**
     * Calculate next retry time using exponential backoff
     */
    private function calculateNextRetryTime(): Carbon
    {
        // Exponential backoff: 2^attempt minutes (capped at 60 minutes)
        $delayMinutes = min(pow(2, $this->send_attempts), 60);
        return now()->addMinutes($delayMinutes);
    }

    /**
     * Mark email as cancelled
     */
    public function markAsCancelled(): void
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
        ]);

        Log::channel('email')->info('OutgoingEmail: Marked as cancelled', [
            'outgoing_email_id' => $this->id,
            'recipient' => $this->recipient,
        ]);
    }

    /**
     * Mark email as exported
     */
    public function markAsExported(string $exportedBy): void
    {
        $this->update([
            'exported_at' => now(),
            'exported_by' => $exportedBy,
        ]);

        Log::channel('email')->info('OutgoingEmail: Marked as exported', [
            'outgoing_email_id' => $this->id,
            'recipient' => $this->recipient,
            'exported_by' => $exportedBy,
        ]);
    }

    /**
     * Update recovery notes
     */
    public function updateRecoveryNotes(string $notes): void
    {
        $this->update(['recovery_notes' => $notes]);
    }

    /**
     * Check if email can be retried
     */
    public function canRetry(): bool
    {
        return $this->send_attempts < $this->max_attempts 
            && in_array($this->status, [self::STATUS_FAILED, self::STATUS_RETRY_SCHEDULED]);
    }

    /**
     * Check if email is ready for retry
     */
    public function isReadyForRetry(): bool
    {
        return $this->status === self::STATUS_RETRY_SCHEDULED
            && $this->scheduled_at !== null
            && $this->scheduled_at->isPast();
    }

    /**
     * Scope: Get emails by status
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope: Get failed emails
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * Scope: Get emails ready for retry
     */
    public function scopeReadyForRetry($query)
    {
        return $query->where('status', self::STATUS_RETRY_SCHEDULED)
                    ->where('scheduled_at', '<=', now());
    }

    /**
     * Scope: Get emails by provider
     */
    public function scopeByProvider($query, string $provider)
    {
        return $query->where('provider', $provider);
    }

    /**
     * Scope: Get recent emails (last 24 hours)
     */
    public function scopeRecent($query)
    {
        return $query->where('created_at', '>=', now()->subDay());
    }

    /**
     * Get human-readable status with emoji
     */
    public function getStatusDisplayAttribute(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => '⏳ Pending',
            self::STATUS_QUEUED => '📤 Queued',
            self::STATUS_SENDING => '📡 Sending',
            self::STATUS_SENT => '✅ Sent',
            self::STATUS_FAILED => '❌ Failed',
            self::STATUS_CANCELLED => '🚫 Cancelled',
            self::STATUS_RETRY_SCHEDULED => '🔄 Retry Scheduled',
            default => '❓ Unknown',
        };
    }

    /**
     * Get next retry time (human readable)
     */
    public function getNextRetryDisplayAttribute(): ?string
    {
        if ($this->status !== self::STATUS_RETRY_SCHEDULED || !$this->scheduled_at) {
            return null;
        }

        return $this->scheduled_at->diffForHumans();
    }

    /**
     * Get summary of sending attempts
     */
    public function getAttemptsSummaryAttribute(): string
    {
        return "{$this->send_attempts}/{$this->max_attempts}";
    }
} 
@extends('layouts.hound')

@section('mytitle', ' Employee Profile' )

@section('content')

<div class="row">
    <div class="col-md-5">
        <h5>Welcome  {{ $employee->name }}</h5>
    </div>
   
</div>
<div class="">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-content">
                    <div class="container bootstrap snippet" id="employee_form">
                        <div class="row">
                            <div class="col-sm-3">
                                <!--left col-->

                                <div class="text-center">
                                    <img src="https://ssl.gstatic.com/accounts/ui/avatar_2x.png" class="avatar img-circle img-thumbnail"
                                        alt="avatar">
                                    <h6>Upload a different photo...</h6>
                                    <input type="file" class="text-center center-block file-upload">
                                </div>
                                </hr><br>


                                <div class="panel panel-default">
                                    <div class="panel-heading">info <i class="fa fa-link fa-1x"></i></div>
                                </div>


                                <ul class="list-group">
                                    <li class="list-group-item text-muted">info <i class="fa fa-dashboard fa-1x"></i></li>
                                </ul>
                            </div>
                            <!--/col-3-->
                            <div class="col-sm-9">
                                <ul class="nav nav-tabs">
                                    <li class="active"><a data-toggle="tab" href="#home">Bio</a></li>
                                    <li><a data-toggle="tab" href="#salary">Salary</a></li>
                                    <li><a data-toggle="tab" href="#settings">Reports</a></li>
                                </ul>
                                <div class="tab-content">
                                    <div class="tab-pane active form-control" id="home">
                                        {!! Form::model($employee, ['method' => 'PUT', 'route' => ['profile.update',
                                        $employee->id ] ]) !!}

                                       
                                        <div class="row">
                                            
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <div class="@if ($errors->has('display_name')) has-error @endif">
                                                        <strong for="display_name">
                                                            Display name
                                                        </strong>
                                                        {!! Form::text('name', null, ['class' => 'form-control',
                                                        'placeholder' => 'Display Name' , 'disabled']) !!}
                                                        @if ($errors->has('name')) <p class="help-block">{{
                                                            $errors->first('name') }}</p> @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <div class="@if ($errors->has('full_name')) has-error @endif">
                                                        <strong for="full_name">
                                                            Full name
                                                        </strong>
                                                        {!! Form::text('full_name', null, ['class' => 'form-control',
                                                        'placeholder' => 'Full Name' , 'disabled']) !!}
                                                        @if ($errors->has('full_name')) <p class="help-block">{{
                                                            $errors->first('full_name') }}</p> @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <div class="@if ($errors->has('email')) has-error @endif">
                                                        <strong for="email">
                                                            Email
                                                        </strong>
                                                        {!! Form::text('email', null, ['class' => 'form-control',
                                                        'placeholder' => 'Email' , 'disabled']) !!}
                                                        @if ($errors->has('email')) <p class="help-block">{{
                                                            $errors->first('email') }}</p> @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <div class="@if ($errors->has('mobile')) has-error @endif">
                                                        <strong for="mobile">
                                                            Mobile Number
                                                        </strong>

                                                        {!! Form::text('mobile', null, ['class' => 'form-control',
                                                        'placeholder' => 'Mobile Number' , 'disabled']) !!}
                                                        @if ($errors->has('mobile')) <p class="help-block">{{
                                                            $errors->first('mobile') }}</p> @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <div class="@if ($errors->has('nationality')) has-error @endif">
                                                        <strong for="nationality">
                                                            Nationality Type
                                                        </strong>

                                                        {!! Form::select('nationality',
                                                        Countries::lookup(config('app.locale')) ,$employee->nationality
                                                        ?? '' , ['class' => 'select2' , 'disabled']) !!}
                                                        @if ($errors->has('nationality')) <p class="help-block">{{
                                                            $errors->first('nationality') }}</p> @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <div class="@if ($errors->has('identity')) has-error @endif">
                                                        <strong for="identity">
                                                            Identity Type
                                                        </strong>

                                                        {!! Form::text('identity', null, ['class' => 'form-control',
                                                        'placeholder' => 'Identity Type' , 'disabled']) !!}
                                                        @if ($errors->has('identity')) <p class="help-block">{{
                                                            $errors->first('identity') }}</p> @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-6">
                                                <div class="form-group">
                                                    <div class="@if ($errors->has('identity_number')) has-error @endif">
                                                        <strong for="identity_number">
                                                            Identity Number
                                                        </strong>

                                                        {!! Form::text('identity_number', null, ['class' =>
                                                        'form-control', 'placeholder' => 'Identity Number' ,
                                                        'disabled']) !!}
                                                        @if ($errors->has('identity_number')) <p class="help-block">{{
                                                            $errors->first('identity_number') }}</p> @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-12">
                                                <div class="form-group">
                                                    <div class="@if ($errors->has('address')) has-error @endif">
                                                        <strong for="address">
                                                            Address
                                                        </strong>

                                                        {!! Form::text('address', null, ['class' => 'form-control',
                                                        'placeholder' => 'Address' , 'disabled']) !!}
                                                        @if ($errors->has('address')) <p class="help-block">{{
                                                            $errors->first('address') }}</p> @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-4">
                                                <div class="form-group">
                                                    <div class="@if ($errors->has('date_of_birth')) has-error @endif">
                                                        <strong for="date_of_birth">
                                                            Date of Birth
                                                        </strong>
                                                        {!! Form::text('date_of_birth', null , ['class' => 'date
                                                        form-control', 'placeholder' => 'Date of Birth' , 'disabled'])
                                                        !!}
                                                        @if ($errors->has('date_of_birth')) <p class="help-block">{{
                                                            $errors->first('date_of_birth') }}</p> @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xs-4">
                                                    <div class="form-group">
                                                        <div class="@if ($errors->has('gender')) has-error @endif">
                                                            <strong for="gender">
                                                                    Gender
                                                            </strong>
                                                            {!! Form::text('gender', null, ['class' => 'form-control',
                                                            'placeholder' => 'gender' , 'disabled']) !!}
                                                            @if ($errors->has('gender')) <p class="help-block">{{
                                                                $errors->first('gender') }}</p> @endif
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-xs-4">
                                                        <div class="form-group">
                                                            <div class="@if ($errors->has('marital_status')) has-error @endif">
                                                                <strong for="marital_status">
                                                            Marital Status
                                                          
                                                                </strong>
                                                                {!! Form::text('marital_status', null, ['class' => 'form-control',
                                                                'placeholder' => 'Marital Status' , 'disabled']) !!}
                                                                @if ($errors->has('marital_status')) <p class="help-block">{{
                                                                    $errors->first('marital_status') }}</p> @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                

                                            
                                            
                                            <div class="col-xs-12">
                                                <hr>
                                                <div class="form-group">
                                                    <div class="@if ($errors->has('password')) has-error @endif">
                                                        <strong for="password">
                                                            Reset Password
                                                        </strong>

                                                        {!! Form::password('password', ['class' => 'form-control',
                                                        'placeholder' => 'Password']) !!}

                                                        @if ($errors->has('password')) <p class="help-block">{{
                                                            $errors->first('password') }}</p> @endif
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <div class="col-xs-12">
                                                    <br>
                                                    <button class="btn btn-lg btn-success" type="submit"><i class="glyphicon glyphicon-ok-sign"></i>
                                                        Save</button>
                                                </div>
                                            </div>
                                        </div>

                                        <hr>
                                        {!! Form::close() !!}
                                    </div>
                                    <!--/tab-pane-->
                                    <div class="tab-pane form-control clearfix" id="salary">

                                        <h4>Current Salary
                                        </h4>
                                        <div class="modal fade " id="updateSalary">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content clearfix">
                                                    <div class="modal-header">
                                                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                                                        <h4 class="modal-title">Update Salary</h4>
                                                    </div>
                                                    <div class="modal-body">
                                                        @include('humanresource::employees.forms.salary')
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @if($employee->current_salary)    
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <div class="@if ($errors->has('start_at')) has-error @endif">
                                                    <strong for="start_at">
                                                        Start on 
                                                    </strong>
                                                    <div class="form-control">
                                                        {{ $employee->current_salary->start_at }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <div class="@if ($errors->has('work_mood')) has-error @endif">
                                                    <strong for="work_mood">
                                                        Work Mode
                                                    </strong>
                                                    <div class="form-control">
                                                    {{ $employee->current_salary->work_mood }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <div class="@if ($errors->has('basic_salary')) has-error @endif">
                                                    <strong for="basic_salary">
                                                        @if($employee->current_salary->work_mood == 'per_hour')
                                                        Cost Per Hour
                                                        @else
                                                        Basic Salary 
                                                        @endif
                                                    </strong>
                                                    <div class="input-group">
                                                        <div class="input-group-addon">RM</div>
                                                        <div class="form-control">
                                                            {{ $employee->current_salary->basic_salary }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @if($employee->current_salary->timetable)
                                        <div class="col-md-12">
                                            <strog>Working Timetable:</strog>
                                            <table class="table table-bordered table-hover table-compact">
                                                <thead>
                                                    <tr>
                                                        <th>Day</th>
                                                        <th>Start</th>
                                                        <th>End</th>
                                                        <th>Break</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php $timestamp = strtotime('next Monday'); ?>
                                                    @for ($i=0; $i < 7 ; $i++) 
                                                    <?php $day = $employee->timetable->where('day' , strftime('%a', $timestamp))->first(); ?>
                                                    <tr>
                                                        <td>{{strftime('%A', $timestamp)}}</td>
                                                        @if($day)
                                                        <td>
                                                            {{ $day->clockin }}
                                                        </td>
                                                        <td>
                                                            {{ $day->clockout }}
                                                        </td>
                                                        <td>
                                                                {{ $day->break }} Hour(s)

                                                        </td>
                                                        @else
                                                        <td colspan="3" class="alert-warning text-center">
                                                            Day off
                                                        </td>
                                                        @endif
                                                    </tr>
                                                    <?php $timestamp = strtotime('+1 day', $timestamp)?>
                                                    @endfor
                                                </tbody>
                                            </table>
                                        </div>
                                        @endif
                                    @endif
                                    <hr>
                                    @if($employee->salaries->reject($employee->current_salary)->count())
                                    <h4>History</h4>
                                    <div class="col-md-12">
                                        <table class="table table-hover table-compact">
                                            <thead>
                                                <tr>
                                                    <th>Basic Salary</th>
                                                    <th>Work Mode</th>
                                                    <th>Started at</th>
                                                    <th>Ended at</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($employee->salaries->reject($employee->current_salary) as $salary)
                                                <tr>
                                                    <td>RM{{ $salary->basic_salary}}</td>
                                                    <td>{{ $salary->work_mood}}</td>
                                                    <td>{{ $salary->start_at}}</td>
                                                    <td>{{ $salary->end_at}}</td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                    @endif
                                    </div>
                                    <!--/tab-pane-->
                                    <div class="tab-pane" id="settings">


                                        <hr>

                                    </div>

                                </div>
                                <!--/tab-pane-->
                            </div>
                            <!--/tab-content-->

                        </div>
                        <!--/col-9-->
                    </div>
                    <!--/row-->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@include('jssnippets.select2')

@include('jssnippets.flatpickr')

@section('js')
<script>
    flatpickr('.date', {
        maxDate: '{{date('Y')-19}}-12-31'
    });
    flatpickr('.freedate');
    flatpickr('.time', {
        enableTime: true,
        noCalendar: true,
        dateFormat: "H:i",
    });

    $('$employee_form .edit_input').click(function (el) {
        $('[disabled]').removeAttr('disabled');
    });

</script>
@append
@section('css')
<style lang="">
    $employee_form *[disabled],
    $employee_form .select2-container--default.select2-container--disabled .select2-selection--single,
    $employee_form .select2-container--default.select2-container--disabled .select2-selection--multiple {
        border: none;
        pointer-events: none;
        background-color: transparent;
    }

    $employee_form .select2-selection--single {
        height: 42px !important;
        padding: 5px;
    }

    $employee_form .select2-selection__rendered {
        background: transparent !important;
        border: none !important;
    }
</style>
@append
-- =================================================================
-- ReliefWeb Provider Category Mappings
-- Created: 2025-08-20
-- Purpose: Map ReliefWeb career categories to canonical categories
-- =================================================================

-- Context: Integration of ReliefWeb as new job provider in centralized notification system
-- Provider: reliefweb
-- API Documentation: https://apidoc.reliefweb.int/
-- Career Categories: 9 standard categories from ReliefWeb API

-- ============================================================
-- ReliefWeb Career Categories → Canonical Category Mappings
-- ============================================================

-- Human Resources (6863) → Human Resources (8)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id, created_at, updated_at)
VALUES ('reliefweb', 'Human Resources', '6863', 8, NOW(), NOW());

-- Logistics/Procurement (36601) → Logistics (28)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id, created_at, updated_at)
VALUES ('reliefweb', 'Logistics/Procurement', '36601', 28, NOW(), NOW());

-- Monitoring and Evaluation (6868) → Monitoring and Evaluation (72)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id, created_at, updated_at)
VALUES ('reliefweb', 'Monitoring and Evaluation', '6868', 72, NOW(), NOW());

-- Donor Relations/Grants Management (20966) → Finance (25)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id, created_at, updated_at)
VALUES ('reliefweb', 'Donor Relations/Grants Management', '20966', 25, NOW(), NOW());

-- Information Management (20971) → Information Technology (67)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id, created_at, updated_at)
VALUES ('reliefweb', 'Information Management', '20971', 67, NOW(), NOW());

-- Advocacy/Communications (6865) → Communication (83)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id, created_at, updated_at)
VALUES ('reliefweb', 'Advocacy/Communications', '6865', 83, NOW(), NOW());

-- Program/Project Management (6867) → Program (74)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id, created_at, updated_at)
VALUES ('reliefweb', 'Program/Project Management', '6867', 74, NOW(), NOW());

-- Information and Communications Technology (6866) → Technology (1)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id, created_at, updated_at)
VALUES ('reliefweb', 'Information and Communications Technology', '6866', 1, NOW(), NOW());

-- Administration/Finance (6864) → Administration (24)
INSERT INTO provider_job_categories (provider_name, name, provider_identifier, canonical_category_id, created_at, updated_at)
VALUES ('reliefweb', 'Administration/Finance', '6864', 24, NOW(), NOW());

-- ============================================================
-- Provider Category Verification
-- ============================================================

-- Verify mappings created correctly
SELECT 
    pc.provider_name,
    pc.name as provider_category,
    pc.provider_identifier,
    jc.name as canonical_category,
    pc.canonical_category_id
FROM provider_job_categories pc
JOIN job_categories jc ON jc.id = pc.canonical_category_id
WHERE pc.provider_name = 'reliefweb'
ORDER BY pc.canonical_category_id;

-- Check total count
SELECT COUNT(*) as total_reliefweb_categories 
FROM provider_job_categories 
WHERE provider_name = 'reliefweb';

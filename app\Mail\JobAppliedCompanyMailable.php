<?php

namespace App\Mail;

use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class JobAppliedCompanyMailable extends Mailable
{

    use SerializesModels;

    public $job;
    public $jobApply;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($job, $jobApply)
    {
        $this->job = $job;
        $this->jobApply = $jobApply;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $company = $this->job->getCompany();
        $user = $this->jobApply->getUser();

        return $this->from(config('mail.recieve_to.address'), config('mail.recieve_to.name'))
                        ->replyTo(config('mail.recieve_to.address'), config('mail.recieve_to.name'))
                        ->to($company->email, $company->name)
                        ->subject('Job seeker named "' . $user->name . '" has applied on job "' . $this->job->title)
                        ->view('emails.job_applied_company_message')
                        ->with(
                                [
                                    'job_title' => $this->job->title,
                                    'organization_name' => $company->name,
                                    'user_name' => $user->name,
                                    'user_link' => route('user.profile', $user->id),
                                    'job_link' => route('job.detail', [$this->job->slug])
                                ]
        );
    }

}

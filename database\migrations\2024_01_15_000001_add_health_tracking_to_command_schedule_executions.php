<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddHealthTrackingToCommandScheduleExecutions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('command_schedule_executions', function (Blueprint $table) {
            // Add health tracking fields for dashboard metrics
            $table->integer('jobs_fetched')->nullable()->default(null)->comment('Raw number of jobs fetched from API before filtering');
            $table->json('jobs_by_category')->nullable()->default(null)->comment('Category breakdown as key-value pairs');
            $table->enum('error_type', ['none', 'network', 'api', 'timeout', 'data', 'unknown'])->default('none')->comment('Structured error classification');
            $table->json('error_details')->nullable()->default(null)->comment('Additional error context and metadata');
            
            // Add index for efficient filtering in dashboard queries
            $table->index('error_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('command_schedule_executions', function (Blueprint $table) {
            $table->dropIndex(['error_type']);
            $table->dropColumn(['jobs_fetched', 'jobs_by_category', 'error_type', 'error_details']);
        });
    }
} 
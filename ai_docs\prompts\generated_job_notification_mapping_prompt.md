# LLM Prompt: Refactor Notification Setup and Add CategoryMappingService

Reference files and symbols
- `Modules/JobSeeker/Http/Controllers/JobsController.php`
   - `storeNotificationSetup` (near line 610)
   - `updateNotificationSetup`, `deleteNotificationSetup`, `getNotificationSetup`
   - `checkForDuplicateNotificationSetup`, `persistRecipientsAsPersonalContacts`
- `Modules/JobSeeker/Entities/JobNotificationSetup.php`
   - `providerCategories()` relation
   - `getAllowedCanonicalCategoryIds()`
   - `mapCanonicalToProviderIdentifiers(string $providerName)`
   - `shouldReceiveNotificationForCategories(array $providerCategoryIds)`
- `Modules/JobSeeker/Entities/ProviderJobCategory.php`
- `Modules/JobSeeker/Entities/JobCategory.php`

Goal
Implement a focused refactor so notification setup logic moves from `JobsController` into a new controller and uses provider-category IDs (not canonical IDs) for delivery.

Required changes
1. Create `Modules/JobSeeker/Http/Controllers/JobNotificationController.php` and move notification-related methods (`store`, `update`, `delete`, `get`, `bulkDelete`, listing) from `JobsController`. Keep existing public route URIs unchanged.

2. Add a reusable service `Modules/JobSeeker/Services/CategoryMappingService.php` with:
    - `public function mapCanonicalToProviderCategoryIds(array $canonicalCategoryIds, string $providerName): array`
    - Behavior: validate input, query `ProviderJobCategory` by `canonical_category_id` and `provider_name`, dedupe and return provider category IDs (ints). Log unmapped canonical IDs.

3. In create/update flows call the mapping service to convert canonical IDs from requests into provider category IDs, then persist provider IDs using `$setup->providerCategories()->sync(...)` (or `syncWithoutDetaching` for additive updates).

4. Update duplicate detection to compare provider-category ID sets and normalized recipient emails. Preserve `bypass_duplicate_check` behavior and existing response semantics (409 + `allow_override`).

5. Keep recipient handling, personal contact persistence, initial notification dispatch, rate limiting, logging, and responses unchanged. Ensure transactions wrap DB work.

Tests
- Unit test for `CategoryMappingService` including a case where a canonical ID maps to no provider categories.
- Integration tests:
   - Create notification setup: canonical IDs map to provider IDs, assert 201 and `providerCategories` contains expected IDs.
   - Duplicate detection: creating a duplicate setup returns 409 with `allow_override` true.

Acceptance criteria
- Routes unchanged externally.
- Provider category IDs stored on `providerCategories()` and used for delivery.
- Duplicate detection operates on provider categories.
- `CategoryMappingService` is reusable and documented.

Suggestions (include these in the generated code or tests)
- Optionally store canonical `categories()` for display while using `providerCategories()` for delivery.
- If partial updates are allowed, prefer non-destructive sync patterns (e.g., `syncWithoutDetaching`) or compute diffs before syncing.
- If pivot attributes will be needed, have the mapping service return `id => pivotAttributes` so controllers can call `sync` with attributes.
- Consider optimistic locking for concurrent edits.

Deliverables requested from the code-generating LLM
1. Patch or commit adding `JobNotificationController` and `CategoryMappingService` and updating routes.
2. Tests described above.
3. Short README describing `CategoryMappingService` and usage examples.

Notes
- If `storeNotificationSetup` line differs in the repo, search for the symbol rather than relying on the line number.

End of prompt.

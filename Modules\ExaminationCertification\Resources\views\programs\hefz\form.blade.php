<h5>Memoraization Al-Quran Program</h5>
<hr>
<div class="form-group {{ $errors->has('code') ? 'has-error' : ''}}">
    {!! Form::label('code', 'Program Code', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::text('code', null, ['class' => 'form-control']) !!}
        {!! $errors->first('code', '<p class="help-block">:message</p>') !!}
    </div>
</div>
<div class="nav-tabs-custom">
    <ul class="nav nav-tabs">
        @foreach(config('app.locales') as $key => $language)
        <li @if($key == 0 ) class="active" @endif>
            <a aria-expanded="true" data-toggle="tab" href="#{{$language}}">
                {{strtoupper($language)}}
            </a>
        </li>
        @endforeach
    </ul>
    <div class="tab-content">
        @foreach(config('app.locales') as $key => $language)
        <div class="tab-pane clearfix @if($key == 0 ) active @endif" id="{{$language}}">
            <div class="col-md-12">
                <div class="form-group {{ $errors->has('translate.'.$language.'.title') ? 'has-error' : ''}}">
                    {!! Form::label('title', 'Title ['.$language.']', ['class' => 'control-label']) !!}
                
                    {!! Form::text('translate['.$language.'][title]', isset($program) && isset($program->translate($language)->title) ? $program->translate($language)->title : '' , ['class' => 'form-control' , 'placeholder' => 'title']) !!}
                    {!! $errors->first('translate.'.$language.'.title', '
                    <p class="help-block">
                        :message
                    </p>
                    ') !!}
                </div>

                <div class="form-group {{ $errors->has('translate.'.$language.'.description') ? 'has-error' : ''}} description" >
                    {!! Form::label('description', 'Content ['.$language.']', ['class' => 'control-label']) !!}
                    {!! Form::textarea('translate['.$language.'][description]',isset($program) && isset($program->translate($language)->description) ? $program->translate($language)->description : '' , ['class' => 'form-control']) !!}
                    {!! $errors->first('translate.'.$language.'.description', '
                    <p class="help-block">
                        :message
                    </p>
                    ') !!}
                </div>
            </div>
        </div>
        <!-- /.tab-pane -->
        @endforeach
    </div>
    <!-- /.tab-description -->
</div>

<div class="form-group {{ $errors->has('language') ? 'has-error' : ''}}">
    {!! Form::label('language', 'Program Language', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('language', config('app.locales') , null, ['class' => 'form-control']) !!}
        {!! $errors->first('language', '<p class="help-block">:message</p>') !!}
    </div>
</div>
<div class="form-group {{ $errors->has('require_interview') ? 'has-error' : ''}}">
    {!! Form::label('require_interview', 'Require Interview Before Registeration', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('require_interview', [1 => 'Has Interview' , 0 => 'Register Without Interview'] , null, ['class' => 'form-control']) !!}
        {!! $errors->first('require_interview', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group {{ $errors->has('status') ? 'has-error' : ''}}">
    {!! Form::label('status', 'Status', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('status', ['active' => 'Active' , 'suspended' => 'Suspended'] , null, ['class' => 'form-control']) !!}
        {!! $errors->first('status', '<p class="help-block">:message</p>') !!}
    </div>
</div>
<h5>Program Setting</h5>


<div class="form-group {{ $errors->has('moshaf_id') ? 'has-error' : ''}}">
    {!! Form::label('moshaf_id', 'Moshaf Name', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('settings[moshaf_id]', ['1' => 'Moshaf Al-Madinah'] , null, ['class' => 'form-control']) !!} {!!
        $errors->first('moshaf_id', '
        <p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group {{ $errors->has('hefz_restricted_to_revision') ? 'has-error' : ''}}">
    {!! Form::label('hefz_restricted_to_revision', 'Restrict New Memorization Lesson to Passing Previous Revision', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('settings[hefz_restricted_to_revision]', ['0' => 'Not Restricted' ,'1' => 'Restricted'] , null, ['class' => 'form-control']) !!} {!!
        $errors->first('hefz_restricted_to_revision', '
        <p class="help-block">:message</p>') !!}
    </div>
</div>



<h5>Program Evaluation Schema</h5>

<h6>Memorization</h6>
<div id="hefz_options">

@isset($hefz_evaluation_schema)
    @foreach($hefz_evaluation_schema->options as $option)
        
        <div class="row">
            <div class="col-sm-2">
                <label for="code">Option Code/Value </label>
                <div class="form-control"> {{ $option->code }} </div>
            </div>
            <div class="col-sm-3">
                <label for="title">Option Title </label>
                <div class="form-control"> {{ $option->title }} </div>
            </div>
            <div class="col-sm-3">
                <label for="description">Option Description</label>
                <div class="form-control">  {{ $option->description }} </div>
            </div>
            <div class="col-sm-2">
                <label for="description">Require To Repeat Lesson</label>
                <div class="form-control">  {{ $option->extra_field == 1 ? 'Yes' : 'No'  }} </div>
            </div>

            <div class="col-sm-2">
                <br>
                <button type="button" class="btn btn-danger ">Disable</button>
            </div>
        </div>
    @endforeach
@endisset
   </div>
    <button type="button" class="btn btn-sm btn-primary" onclick="addEvaluationOption('hefz')">Add New Memorization's Evaluation Option</button>

<h6>Revision</h6>
<div id="revision_options">
@isset($revision_evaluation_schema)
    @foreach($revision_evaluation_schema->options as $option)
        
        <div class="row">
            <div class="col-sm-2">
                <label for="code">Option Code/Value </label>
                <div class="form-control"> {{ $option->code }} </div>
            </div>
            <div class="col-sm-3">
                <label for="title">Option Title </label>
                <div class="form-control"> {{ $option->title }} </div>
            </div>
            <div class="col-sm-5">
                <label for="description">Option Description</label>
                <div class="form-control">  {{ $option->description }} </div>
            </div>
            <div class="col-sm-2">
                <br>
                <button type="button" class="btn btn-danger ">Delete</button>
            </div>
        </div>
    @endforeach
@endisset
</div>

    <button type="button" class="btn btn-sm btn-large btn-primary" onclick="addEvaluationOption('revision')">Add New Revision Evaluation Option</button>

<div class="form-group">
    <div class="col-md-offset-4 col-md-4">
        {!! Form::submit(isset($submitButtonText) ? $submitButtonText : 'Create', ['class' => 'btn btn-primary']) !!}
    </div>
</div>

@section('js')
<script>
    var num_rows=0;
    $(document).ready(function() {
        checkSchemaType();
        $('#type').change(function(){
            checkSchemaType();
        });
        
    });
    var checkSchemaType = function(){
        if($('#type').val() == 'marks'){
            $('#evaluation_options').hide();
        }else{
            $('#evaluation_options').show();            
        }
    } 
    var addEvaluationOption = function(target){
        num_rows++;
        var option = '<div class="row" id="option-row-'+num_rows+'">'
                    +'<div class="col-sm-2">'
                        +'<label for="code">Option Code/Value </label>'
                        +'<input class="form-control" type="text" name="'+target+'[options][new'+num_rows+'][code]" id="code" value="">'
                    +'</div>'
                    +'<div class="col-sm-3">'
                        +'<label for="title">Option Title </label>'
                        +'<input class="form-control" type="text" name="'+target+'[options][new'+num_rows+'][title]" id="title" value="">'
                    +'</div>'
                    +'<div class="col-sm-3">'
                        +'<label for="description">Option Description</label>'
                        +'<input class="form-control" type="text" name="'+target+'[options][new'+num_rows+'][description]" id="description" value="">'
                    +'</div>'
                    +'<div class="col-sm-2">'
                        +'<label for="description">Require To Repeat Lesson</label>'
                        +'<select class="form-control"  name="'+target+'[options][new'+num_rows+'][extra_field]" >'
                        +'<option value="0">No</option>'
                        +'<option value="1">Yes</option>'
                        +'</select>'
                    +'</div>'
                    +'<div class="col-sm-2">'
                        +'<br>'
                        +'<button type="button" class="btn btn-danger " onclick="$(\'#option-row-'+num_rows+'\').remove()">Delete</button>'
                    +'</div>'
                +'</div>';
        $('#'+target+'_options').append(option);
    }
</script>
@endsection
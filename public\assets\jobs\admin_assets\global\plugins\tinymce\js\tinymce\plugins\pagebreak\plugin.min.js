!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),a=tinymce.util.Tools.resolve("tinymce.Env"),n=function(e){return e.getParam("pagebreak_separator","\x3c!-- pagebreak --\x3e")},t=function(e){return e.getParam("pagebreak_split_block",!1)},r=function(){return"mce-pagebreak"},c=function(){return'<img src="'+a.transparentSrc+'" class="mce-pagebreak" data-mce-resize="false" data-mce-placeholder />'},o=function(e){var a=n(e),r=new RegExp(a.replace(/[\?\.\*\[\]\(\)\{\}\+\^\$\:]/g,function(e){return"\\"+e}),"gi");e.on("BeforeSetContent",function(e){e.content=e.content.replace(r,c())}),e.on("PreInit",function(){e.serializer.addNodeFilter("img",function(n){for(var r,c,o=n.length;o--;)if((c=(r=n[o]).attr("class"))&&-1!==c.indexOf("mce-pagebreak")){var i=r.parent;if(e.schema.getBlockElements()[i.name]&&t(e)){i.type=3,i.value=a,i.raw=!0,r.remove();continue}r.type=3,r.value=a,r.raw=!0}})})},i=c,g=r,u=function(e){e.addCommand("mcePageBreak",function(){e.settings.pagebreak_split_block?e.insertContent("<p>"+i()+"</p>"):e.insertContent(i())})},m=function(e){e.on("ResolveName",function(a){"IMG"===a.target.nodeName&&e.dom.hasClass(a.target,g())&&(a.name="pagebreak")})},s=function(e){e.addButton("pagebreak",{title:"Page break",cmd:"mcePageBreak"}),e.addMenuItem("pagebreak",{text:"Page break",icon:"pagebreak",cmd:"mcePageBreak",context:"insert"})};e.add("pagebreak",function(e){u(e),s(e),o(e),m(e)})}();
<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Mo<PERSON>les\JobSeeker\Entities\CommandScheduleRule;
use Mo<PERSON><PERSON>\JobSeeker\Entities\JobSeeker;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

/**
 * Test suite for CommandSchedule Clone functionality
 *
 * Tests the "clone superpower" feature that allows replicating schedule rules
 * between job providers (Jobs.af and ACBAR) with configurable time offsets.
 */
class CommandScheduleCloneTest extends TestCase
{
    use DatabaseTransactions, WithoutMiddleware;

    protected function setUp(): void
    {
        parent::setUp();

        // Create and authenticate an admin user for testing
        $adminUser = JobSeeker::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'full_name' => 'Test Admin',
            'is_active' => true,
            'is_admin' => true,
            'email_verified_at' => now()
        ]);

        // Authenticate the user with job_seeker guard
        Auth::guard('job_seeker')->login($adminUser);
    }

    /**
     * Test successful cloning from Jobs.af to ACBAR with 15-minute offset
     */
    public function test_clone_jobs_af_to_acbar_with_15_minute_offset(): void
    {
        // Create a Jobs.af schedule rule
        $sourceRule = CommandScheduleRule::create([
            'name' => 'Jobs.af Daily Scrape',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '09:00', // 9:00 AM daily
            'schedule_type' => 'daily_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 1,
            'description' => 'Daily scraping of Jobs.af',
            'provider_category_ids' => [1, 2, 3],
            'provider_location_ids' => [10, 20]
        ]);

        // Test cloning with 15-minute offset
        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [$sourceRule->id],
            'from_provider' => 'jobsaf',
            'to_provider' => 'acbar',
            'time_offset_minutes' => 15
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'cloned_count' => 1
        ]);

        $responseData = $response->json();
        $this->assertStringContainsString('Successfully cloned 1 rule(s)', $responseData['message']);
        $this->assertArrayHasKey('cloned_rules', $responseData);
        $this->assertCount(1, $responseData['cloned_rules']);

        // Verify the cloned rule exists with correct properties
        $clonedRule = CommandScheduleRule::where('name', 'LIKE', '%Cloned to ACBAR%')->first();
        $this->assertNotNull($clonedRule);
        $this->assertEquals('jobseeker:sync-acbar-jobs', $clonedRule->command);
        $this->assertEquals('09:15', $clonedRule->schedule_expression); // 9:15 AM (15 minutes offset)
        $this->assertEquals('daily_at', $clonedRule->schedule_type);
        $this->assertTrue($clonedRule->is_active);
        $this->assertEquals($sourceRule->priority, $clonedRule->priority);
        $this->assertEquals($sourceRule->provider_category_ids, $clonedRule->provider_category_ids);
        $this->assertEquals($sourceRule->provider_location_ids, $clonedRule->provider_location_ids);
    }

    /**
     * Test successful cloning from ACBAR to Jobs.af with 30-minute offset
     */
    public function test_clone_acbar_to_jobs_af_with_30_minute_offset(): void
    {
        // Create an ACBAR schedule rule
        $sourceRule = CommandScheduleRule::create([
            'name' => 'ACBAR Weekly Scrape',
            'command' => 'jobseeker:sync-acbar-jobs',
            'schedule_expression' => '1 14:00', // 2:00 PM on Mondays
            'schedule_type' => 'weekly_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 2,
            'description' => 'Weekly scraping of ACBAR',
            'provider_category_ids' => [4, 5],
            'provider_location_ids' => [30, 40, 50]
        ]);

        // Test cloning with 30-minute offset
        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [$sourceRule->id],
            'from_provider' => 'acbar',
            'to_provider' => 'jobsaf',
            'time_offset_minutes' => 30
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'cloned_count' => 1
        ]);

        $responseData = $response->json();
        $this->assertStringContainsString('Successfully cloned 1 rule(s)', $responseData['message']);

        // Verify the cloned rule exists with correct time offset
        $clonedRule = CommandScheduleRule::where('name', 'LIKE', '%Cloned to JOBSAF%')->first();
        $this->assertNotNull($clonedRule);
        $this->assertEquals('jobseeker:sync-jobs-af', $clonedRule->command);
        $this->assertEquals('1 14:30', $clonedRule->schedule_expression); // 2:30 PM on Mondays
        $this->assertEquals('weekly_at', $clonedRule->schedule_type);
    }

    /**
     * Test cloning with 60-minute offset and cron expression
     */
    public function test_clone_with_60_minute_offset_cron_expression(): void
    {
        // Create a rule with complex cron expression
        $sourceRule = CommandScheduleRule::create([
            'name' => 'Jobs.af Complex Schedule',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '30 9 * * 1-5', // 9:30 AM on weekdays
            'schedule_type' => 'cron',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 3,
            'description' => 'Complex scheduling pattern'
        ]);

        // Test cloning with 60-minute offset
        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [$sourceRule->id],
            'from_provider' => 'jobsaf',
            'to_provider' => 'acbar',
            'time_offset_minutes' => 60
        ]);

        $response->assertStatus(200);

        // Verify the cloned rule has correct time offset
        $clonedRule = CommandScheduleRule::where('name', 'LIKE', '%Cloned to ACBAR%')->first();
        $this->assertNotNull($clonedRule);
        $this->assertEquals('30 10 * * 1-5', $clonedRule->schedule_expression); // 10:30 AM (+1 hour)
        $this->assertEquals('cron', $clonedRule->schedule_type);
    }

    /**
     * Test validation error when trying to clone from and to the same provider
     */
    public function test_clone_same_provider_validation_error(): void
    {
        $sourceRule = CommandScheduleRule::create([
            'name' => 'Test Rule',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '10:00',
            'schedule_type' => 'daily_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 1
        ]);

        // Try to clone from jobsaf to jobsaf (same provider)
        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [$sourceRule->id],
            'from_provider' => 'jobsaf',
            'to_provider' => 'jobsaf',
            'time_offset_minutes' => 15
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['to_provider']);
        $response->assertJson([
            'errors' => [
                'to_provider' => ['The to provider and from provider must be different.']
            ]
        ]);
    }

    /**
     * Test validation error for invalid provider names
     */
    public function test_clone_invalid_provider_validation_error(): void
    {
        $sourceRule = CommandScheduleRule::create([
            'name' => 'Test Rule',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '10:00',
            'schedule_type' => 'daily_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 1
        ]);

        // Try to clone with invalid provider
        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [$sourceRule->id],
            'from_provider' => 'invalid-provider',
            'to_provider' => 'acbar',
            'time_offset_minutes' => 15
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['from_provider']);
    }

    /**
     * Test that original rules remain unchanged after cloning
     */
    public function test_original_rules_unchanged_after_cloning(): void
    {
        $originalData = [
            'name' => 'Original Rule',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '11:00',
            'schedule_type' => 'daily_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 1,
            'description' => 'Original description',
            'provider_category_ids' => [1, 2],
            'provider_location_ids' => [10]
        ];

        $sourceRule = CommandScheduleRule::create($originalData);
        $originalId = $sourceRule->id;

        // Clone the rule
        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [$sourceRule->id],
            'from_provider' => 'jobsaf',
            'to_provider' => 'acbar',
            'time_offset_minutes' => 15
        ]);

        $response->assertStatus(200);

        // Verify original rule is unchanged
        $originalRule = CommandScheduleRule::find($originalId);
        $this->assertNotNull($originalRule);
        $this->assertEquals($originalData['name'], $originalRule->name);
        $this->assertEquals($originalData['command'], $originalRule->command);
        $this->assertEquals($originalData['schedule_expression'], $originalRule->schedule_expression);
        $this->assertEquals($originalData['description'], $originalRule->description);
        $this->assertEquals($originalData['provider_category_ids'], $originalRule->provider_category_ids);
        $this->assertEquals($originalData['provider_location_ids'], $originalRule->provider_location_ids);
    }

    /**
     * Test cloning multiple rules at once
     */
    public function test_clone_multiple_rules(): void
    {
        // Create multiple source rules
        $sourceRule1 = CommandScheduleRule::create([
            'name' => 'Jobs.af Rule 1',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '09:00',
            'schedule_type' => 'daily_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 1
        ]);

        $sourceRule2 = CommandScheduleRule::create([
            'name' => 'Jobs.af Rule 2',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '15:00',
            'schedule_type' => 'daily_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 2
        ]);

        // Clone both rules
        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [$sourceRule1->id, $sourceRule2->id],
            'from_provider' => 'jobsaf',
            'to_provider' => 'acbar',
            'time_offset_minutes' => 30
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'cloned_count' => 2
        ]);

        $responseData = $response->json();
        $this->assertStringContainsString('Successfully cloned 2 rule(s)', $responseData['message']);

        // Verify both cloned rules exist
        $clonedRule1 = CommandScheduleRule::where('name', 'LIKE', '%Rule 1%Cloned to ACBAR%')->first();
        $clonedRule2 = CommandScheduleRule::where('name', 'LIKE', '%Rule 2%Cloned to ACBAR%')->first();

        $this->assertNotNull($clonedRule1);
        $this->assertNotNull($clonedRule2);
        $this->assertEquals('09:30', $clonedRule1->schedule_expression); // 9:30 AM
        $this->assertEquals('15:30', $clonedRule2->schedule_expression); // 3:30 PM
    }

    /**
     * Test cloning with inactive rules
     */
    public function test_clone_inactive_rules(): void
    {
        $sourceRule = CommandScheduleRule::create([
            'name' => 'Inactive Rule',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '10:00',
            'schedule_type' => 'daily_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => false, // Inactive rule
            'priority' => 1
        ]);

        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [$sourceRule->id],
            'from_provider' => 'jobsaf',
            'to_provider' => 'acbar',
            'time_offset_minutes' => 15
        ]);

        $response->assertStatus(200);

        // Verify cloned rule maintains inactive status
        $clonedRule = CommandScheduleRule::where('name', 'LIKE', '%Cloned to ACBAR%')->first();
        $this->assertNotNull($clonedRule);
        $this->assertFalse($clonedRule->is_active);
    }

    /**
     * Test error handling for invalid schedule expressions
     */
    public function test_clone_with_invalid_schedule_expression(): void
    {
        $sourceRule = CommandScheduleRule::create([
            'name' => 'Invalid Schedule Rule',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => 'invalid-cron-expression',
            'schedule_type' => 'cron',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 1
        ]);

        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [$sourceRule->id],
            'from_provider' => 'jobsaf',
            'to_provider' => 'acbar',
            'time_offset_minutes' => 15
        ]);

        // Should handle gracefully - invalid expressions are cloned as-is
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'cloned_count' => 1
        ]);
    }

    /**
     * Test cloning with empty rule list (should clone all rules from provider)
     */
    public function test_clone_with_empty_rule_list(): void
    {
        // Create a specific rule to ensure we have at least one to clone
        $sourceRule = CommandScheduleRule::create([
            'name' => 'Test Rule for Empty List',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '10:00',
            'schedule_type' => 'daily_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 1
        ]);

        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [],
            'from_provider' => 'jobsaf',
            'to_provider' => 'acbar',
            'time_offset_minutes' => 15
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);

        $responseData = $response->json();
        $this->assertGreaterThan(0, $responseData['cloned_count']);
        $this->assertStringContainsString('Successfully cloned', $responseData['message']);
    }

    /**
     * Test cloning with non-existent rule IDs (should return validation error)
     */
    public function test_clone_with_non_existent_rule_ids(): void
    {
        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [99999, 99998], // Non-existent IDs
            'from_provider' => 'jobsaf',
            'to_provider' => 'acbar',
            'time_offset_minutes' => 15
        ]);

        // Should return validation error for non-existent rule IDs
        $response->assertStatus(422);
        $response->assertJsonStructure([
            'errors'
        ]);
    }

    /**
     * Test time offset calculation edge cases
     */
    public function test_time_offset_edge_cases(): void
    {
        // Test with time that would overflow to next hour
        $sourceRule = CommandScheduleRule::create([
            'name' => 'Edge Case Rule',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '23:50', // 11:50 PM
            'schedule_type' => 'daily_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 1
        ]);

        // Add 30 minutes - should become 00:20 next day
        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [$sourceRule->id],
            'from_provider' => 'jobsaf',
            'to_provider' => 'acbar',
            'time_offset_minutes' => 30
        ]);

        $response->assertStatus(200);

        $clonedRule = CommandScheduleRule::where('name', 'LIKE', '%Cloned to ACBAR%')->first();
        $this->assertNotNull($clonedRule);
        $this->assertEquals('00:20', $clonedRule->schedule_expression); // 00:20 (12:20 AM)
    }

    /**
     * Test that cloned rules are properly saved to database
     */
    public function test_cloned_rules_database_persistence(): void
    {
        $sourceRule = CommandScheduleRule::create([
            'name' => 'Persistence Test',
            'command' => 'jobseeker:sync-jobs-af',
            'schedule_expression' => '12:00',
            'schedule_type' => 'daily_at',
            'timezone' => 'Asia/Kabul',
            'is_active' => true,
            'priority' => 1,
            'description' => 'Test persistence',
            'provider_category_ids' => [1, 2, 3],
            'provider_location_ids' => [10, 20]
        ]);

        $response = $this->postJson('/admin/jobseeker/command-schedule/api/clone-rules', [
            'rule_ids' => [$sourceRule->id],
            'from_provider' => 'jobsaf',
            'to_provider' => 'acbar',
            'time_offset_minutes' => 30
        ]);

        $response->assertStatus(200);

        // Verify the rule exists in database with all relationships
        $clonedRule = CommandScheduleRule::where('name', 'LIKE', '%Cloned to ACBAR%')->first();
        $this->assertNotNull($clonedRule);
        $this->assertDatabaseHas('command_schedule_rules', [
            'id' => $clonedRule->id,
            'command' => 'jobseeker:sync-acbar-jobs',
            'schedule_expression' => '12:30',
            'is_active' => true
        ]);

        // Verify JSON fields are properly stored
        $this->assertEquals([1, 2, 3], $clonedRule->provider_category_ids);
        $this->assertEquals([10, 20], $clonedRule->provider_location_ids);
    }
}

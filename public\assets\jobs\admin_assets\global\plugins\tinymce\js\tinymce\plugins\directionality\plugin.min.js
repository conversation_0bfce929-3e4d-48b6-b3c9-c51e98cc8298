!function(){"use strict";var t=tinymce.util.Tools.resolve("tinymce.PluginManager"),e=tinymce.util.Tools.resolve("tinymce.util.Tools"),i=function(t,i){var n,o=t.dom,c=t.selection.getSelectedBlocks();c.length&&(n=o.getAttrib(c[0],"dir"),e.each(c,function(t){o.getParent(t.parentNode,'*[dir="'+i+'"]',o.getRoot())||o.setAttrib(t,"dir",n!==i?i:null)}),t.nodeChanged())},n=function(t){t.addCommand("mceDirectionLTR",function(){i(t,"ltr")}),t.addCommand("mceDirectionRTL",function(){i(t,"rtl")})},o=function(t){var i=[];return e.each("h1 h2 h3 h4 h5 h6 div p".split(" "),function(e){i.push(e+"[dir="+t+"]")}),i.join(",")},c=function(t){t.addButton("ltr",{title:"Left to right",cmd:"mceDirectionLTR",stateSelector:o("ltr")}),t.addButton("rtl",{title:"Right to left",cmd:"mceDirectionRTL",stateSelector:o("rtl")})};t.add("directionality",function(t){n(t),c(t)})}();
<?php

namespace Modules\Education\Http\Controllers;

use App\Classes;
use App\Employee;
use App\Guardian;
use App\MarksGrade;
use App\Program;
use App\ProgramLevel;
use App\Section;
use App\Student;
use App\Subject;
use App\YearCheck;
use App\OnlineExam;
use App\ApiBaseMethod;
use App\Notification;
use App\QuestionBank;
use App\AssignSubject;
use App\OnlineExamMark;
use App\GeneralSettings;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\OnlineExamQuestion;
use App\StudentTakeOnlineExam;
use Illuminate\Support\Facades\DB;
use App\OnlineExamQuestionAssign;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use App\OnlineExamQuestionMuOption;
use Illuminate\Support\Facades\Schema;
use App\OnlineExamStudentAnswerMarking;
use Illuminate\Support\Facades\Validator;
use Modules\OnlineExam\Entities\InfixOnlineExam;
use Modules\OnlineExam\Entities\InfixStudentTakeOnlineExam;
use App\Http\Controllers\Controller;


class OnlineExamController extends Controller
{

    public function __construct()
    {
//        $this->middleware('PM');
        // User::checkAuth();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function index()
    {
        $time_zone_setup = GeneralSettings::join('time_zones', 'time_zones.id', '=', 'general_settings.time_zone_id')
            ->where('school_id', Auth::user()->school_id)->first();
        date_default_timezone_set($time_zone_setup->time_zone);
        try {

            if (!Schema::hasColumn('sm_online_exams', 'auto_mark')) {
                Schema::table('sm_online_exams', function ($table) {
                    $table->integer('auto_mark')->default(0);
                });
            }

            if (teacherAccess()) {
                $teacher_info = SmStaff::where('user_id', Auth::user()->id)->first();
                $online_exams = OnlineExam::where('status', '!=', 2)
                    ->join('sm_assign_subjects', 'sm_assign_subjects.subject_id', '=', 'sm_online_exams.subject_id')
                    ->where('sm_assign_subjects.teacher_id', $teacher_info->id)
                    ->where('sm_online_exams.academic_id', getAcademicId())
                    ->where('sm_online_exams.school_id', Auth::user()->school_id)
                    ->select('sm_online_exams.*')
                    ->distinct('id')
                    ->get();

                $classes = SmAssignSubject::where('teacher_id', $teacher_info->id)
                    ->join('sm_classes', 'sm_classes.id', 'sm_assign_subjects.class_id')
                    ->where('sm_assign_subjects.academic_id', getAcademicId())
                    ->where('sm_assign_subjects.active_status', 1)
                    ->where('sm_assign_subjects.school_id', Auth::user()->school_id)
                    ->select('sm_classes.id', 'class_name')
                    ->groupBy('sm_classes.id')
                    ->get();
            } else {
                $classes = Classes::where('active_status', 1)
                    ->where('academic_id', getAcademicId())
                    ->where('school_id', Auth::user()->school_id)
                    ->get();

                $online_exams = OnlineExam::where('status', '!=', 2)
                    ->where('academic_id', getAcademicId())
                    ->where('school_id', Auth::user()->school_id)
                    ->get();
            }
            $sections = SmSection::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();
            $subjects = SmSubject::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();
            $present_date_time = date("Y-m-d H:i:s");
            $present_time = date("H:i:s");

            return view('backEnd.examination.online_exam', compact('online_exams', 'classes', 'sections', 'subjects', 'present_date_time', 'present_time'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function viewOnlineExam($id)
    {
        try {

            $online_exam = OnlineExam::find($id);
            $assigned_questions = OnlineExamQuestionAssign::where('online_exam_id', $online_exam->id)
                ->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            return view('backEnd.examination.view_online_question', compact('online_exam', 'assigned_questions'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function store(Request $request)
    {






        $gradeId = MarksGrade::whereRaw('? between percent_from  and percent_upto', [$request->score])->first()->id;


        $class = Classes::whereHas('students',function($q) use($request) {

            $q->where('students.id', $request->get('studentId'));

        })->with('programs')->first();


        $request->validate([
//            'title' => 'required',
//            'classId' => 'required',
//            'programId' => 'required',
//            'date' => 'required',
//            'start_time' => 'required',
//            'end_time' => 'required',
//            'percentage' => 'required',
//            'instruction' => 'required'
        ]);
        DB::beginTransaction();


        try {
            $date = strtotime($request->date);
            $newformat = date('Y-m-d', $date);

            // extract program id based on the class in which the student is
            $online_exam = new OnlineExam();
            $online_exam->title = $request->title;
            $online_exam->class_id = $request->classId;
            $online_exam->student_id = $request->studentId;
            // Check if the programs relationship is not null
            if ($class->programs) {
                $online_exam->program_id = $class->programs->first()->id;
                $program = $class->programs->first();
                if ($request->score > $program->minimum_mark_to_pass) {
                    $online_exam->result = 'pass';
                    $student = Student::find($request->studentId);
                    if(is_null($student->level))
                    {
                        $student->level = $program->levels->first()->id;
                    }
                    else{
                        $student->level = ProgramLevel::where('id', $student->level)->first()->program_level_order++;

                    }
                    $student->ready_for_exam = NULL;
                    $student->save();
                } else {
                    $online_exam->result = 'fail';
                }
            }
            $online_exam->examiner_id = $request->examinerId;
            $online_exam->date = $newformat;
            $online_exam->start_time = Carbon::parse($request->start_time)->toTimeString();
            $online_exam->end_time = Carbon::parse($request->end_time)->toTimeString();
            $online_exam->end_date_time = date('Y-m-d H:i:s', strtotime($request->date . ' ' . $request->end_time));
            $online_exam->score = $request->score;
            $online_exam->marks_grades_id = $gradeId;
            $online_exam->details = $request->remarks;
            $online_exam->status = 1; // 1  = published
            $online_exam->is_taken = 1; // 1  = published

            $online_exam->organization_id = config('organization_id');
            $result = $online_exam->save();

            if ($result) {
                // $data = SmStudent::where('class_id', $request->class)->where('section_id', $request->section)->where('academic_id', getAcademicId())->where('school_id',Auth::user()->school_id)->get();
                // if (!empty($data)) {
                //     foreach ($data as $key => $value) {
                //         $notify = new SmNotification();
                //         $notify->date = date('Y-m-d', strtotime($request->date));
                //         $notify->message = 'Online exam - ' . $request->title;
                //         $notify->url = 'student-online-exam';
                //         $notify->user_id = $value->id;
                //         $notify->role_id = $value->role_id;
                //         $notify->created_by = Auth::user()->id;
                //         $notify->school_id = $value->school_id;
                //         $notify->save();
                //     }
                // }
                $online_exam = $online_exam->loadMissing('studentInfo');

                DB::commit();
                Toastr::success('Operation successful', 'Success');
                return redirect()->route('online-exam.student', ['studentId' => $request->studentId,'classId'=>$request->classId]);
                return view('education::examination.online_exam', compact('online_exam', 'class', 'online_exams'));

                return redirect()->back();
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {

            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function edit($id, $classId)
    {
        $time_zone_setup = GeneralSettings::join('time_zones', 'time_zones.id', '=', 'general_settings.time_zone_id')
            ->first();
        date_default_timezone_set($time_zone_setup->time_zone);

        try {
            $online_exams = OnlineExam::where('class_id', $classId)->get();
            $classes = Classes::all();
            $class = Classes::find($classId);
            $online_exam = OnlineExam::find($id);


            $present_date_time = date("Y-m-d H:i:s");
            $present_time = date("H:i:s");

            return view('education::examination.online_exam', compact('online_exams', 'classes', 'online_exam', 'present_date_time', 'present_time', 'class'));
        } catch (\Exception $e) {


            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {





        $request->validate([
            'title' => 'required',
            'classId' => 'required',
            'date' => 'required',
            'start_time' => 'required',
            'end_time' => 'required',
            'score' => 'required',
            'remarks' => 'required'
        ]);
        try {
            $date = strtotime($request->date);
            $newformat = date('Y-m-d', $date);
            // $online_exam = OnlineExam::find($id);
            $online_exam = OnlineExam::find($id);


            $online_exam->title = $request->title;
            $online_exam->class_id = $request->classId;

            $online_exam->date = date('Y-m-d', strtotime($request->date));
            $online_exam->start_time = date('H:i:s', strtotime($request->start_time));
            $online_exam->end_time = date('H:i:s', strtotime($request->end_time));
            $online_exam->end_date_time = date('Y-m-d H:i:s', strtotime($request->date . ' ' . $request->end_time));
            $online_exam->score = $request->score;
            $online_exam->details = $request->remarks;
//            if ($request->auto_mark) {
//                $online_exam->auto_mark = $request->auto_mark;
//            }

            $online_exams = OnlineExam::where('class_id', $request->classId)->get();
            $class = Classes::find($request->classId);

            $result = $online_exam->save();


            if ($result) {
                Toastr::success('Operation successful', 'Success');
                return redirect()->route('online-exam.class', ['classId' => $request->classId]);
                return redirect()->back();
                return redirect('online-exam');
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function section()
    {

        try {
            $id = $_GET['id'];
            return response()->json(['response' => 'This is get method']);
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function delete(Request $request)
    {
        try {
            $id_key = 'online_exam_id';
            $id = $request->online_exam_id;

            $tables = \App\tableList::getTableList($id_key, $id);

            try {
                if ($tables == null) {

                    // $delete_query = OnlineExam::destroy($request->id);
                    if (checkAdmin()) {
                        $delete_query = OnlineExam::destroy($id);
                    } else {
                        $delete_query = OnlineExam::where('id', $id)->where('school_id', Auth::user()->school_id)->delete();
                    }


                    if ($delete_query) {
                        Toastr::success('Operation successful', 'Success');
                        return redirect()->back();
                    } else {
                        Toastr::error('Operation Failed', 'Failed');
                        return redirect()->back();
                    }

                } else {
                    $msg = 'This data already used in  : ' . $tables . ' Please remove those data first';
                    Toastr::error($msg, 'Failed');
                    return redirect()->back();
                }


            } catch (\Illuminate\Database\QueryException $e) {

                $msg = 'This data already used in  : ' . $tables . ' Please remove those data first';
                Toastr::error($msg, 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function manageOnlineExamQuestion($id)
    {

        try {
            // $online_exam = OnlineExam::find($id);
            if (checkAdmin()) {
                $online_exam = OnlineExam::find($id);
            } else {
                $online_exam = OnlineExam::where('id', $id)->where('school_id', Auth::user()->school_id)->first();
            }
            $question_banks = SmQuestionBank::where('class_id', $online_exam->class_id)->where('section_id', $online_exam->section_id)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            $assigned_questions = OnlineExamQuestionAssign::where('online_exam_id', $id)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();
            $already_assigned = [];
            foreach ($assigned_questions as $assigned_question) {
                $already_assigned[] = $assigned_question->question_bank_id;
            }


            return view('backEnd.examination.manage_online_exam', compact('online_exam', 'question_banks', 'already_assigned'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function manageOnlineExamQuestionStore(Request $request)
    {


        try {
            if ($request->question_type != 'M') {
                $online_question = new OnlineExamQuestion();
                $online_question->online_exam_id = $request->online_exam_id;
                $online_question->type = $request->question_type;
                $online_question->mark = $request->mark;
                $online_question->title = $request->question_title;
                $online_question->school_id = Auth::user()->school_id;
                $online_question->organization_id = config('organization_id');
                if ($request->question_type == "F") {
                    $online_question->suitable_words = $request->suitable_words;
                } elseif ($request->question_type == "T") {
                    $online_question->trueFalse = $request->trueOrFalse;
                }
                $result = $online_question->save();
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            } else {

                DB::beginTransaction();

                try {
                    $online_question = new OnlineExamQuestion();
                    $online_question->online_exam_id = $request->online_exam_id;
                    $online_question->type = $request->question_type;
                    $online_question->mark = $request->mark;
                    $online_question->title = $request->question_title;
                    $online_question->school_id = Auth::user()->school_id;
                    $online_question->organization_id = config('organization_id');
                    $online_question->save();
                    $online_question->toArray();
                    $i = 0;
                    if (isset($request->option)) {
                        foreach ($request->option as $option) {
                            $i++;
                            $option_check = 'option_check_' . $i;
                            $online_question_option = new OnlineExamQuestionMuOption();
                            $online_question_option->online_exam_question_id = $online_question->id;
                            $online_question_option->title = $option;
                            $online_question_option->school_id = Auth::user()->school_id;
                            $online_question_option->organization_id = config('organization_id');
                            if (isset($request->$option_check)) {
                                $online_question_option->status = 1;
                            } else {
                                $online_question_option->status = 0;
                            }
                            $online_question_option->save();
                        }
                    }
                    DB::commit();
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                } catch (\Exception $e) {
                    DB::rollBack();
                }
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function onlineExamPublish($id)
    {

        try {
            $time_zone_setup = GeneralSettings::join('time_zones', 'time_zones.id', '=', 'general_settings.time_zone_id')
                ->where('school_id', Auth::user()->school_id)->first();
            date_default_timezone_set($time_zone_setup->time_zone);
            $present_date_time = date("Y-m-d H:i:s");

            // $publish = OnlineExam::find($id);
            if (checkAdmin()) {
                $publish = OnlineExam::find($id);
            } else {
                $publish = OnlineExam::where('id', $id)->where('school_id', Auth::user()->school_id)->first();
            }
            $class_id = $publish->class_id;
            $section_id = $publish->section_id;
            if ($present_date_time > $publish->end_date_time) {
                Toastr::error('Please update exam time', 'Failed');
                return redirect()->back();
            }
            $publish->status = 1;
            $publish->save();

            $students = SmStudent::where('class_id', $class_id)->where('section_id', $section_id)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            foreach ($students as $student) {
                $notification = new SmNotification;
                $notification->user_id = $student->user_id;
                $notification->role_id = 2;
                $notification->date = date('Y-m-d');
                $notification->message = 'New online exam published';
                $notification->url = 'student-online-exam';
                $notification->school_id = Auth::user()->school_id;
                $notification->organization_id = config('organization_id');
                $notification->save();

                $parent = SmParent::find($student->parent_id);
                $notidication = new SmNotification();
                $notidication->role_id = 3;
                $notidication->message = "New online exam published for your child";
                $notidication->date = date('Y-m-d');
                $notidication->user_id = $parent->user_id;
                $notidication->organization_id = config('organization_id');
                $notidication->save();
            }

            Toastr::success('Operation successful', 'Success');
            return redirect()->back();
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function onlineExamPublishCancel($id)
    {

        try {
            // $publish = OnlineExam::find($id);
            if (checkAdmin()) {
                $publish = OnlineExam::find($id);
            } else {
                $publish = OnlineExam::where('id', $id)->where('school_id', Auth::user()->school_id)->first();
            }
            $publish->status = 3;
            $publish->save();
            Toastr::error('Exam Expired', 'Failed');
            return redirect()->back();
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function onlineQuestionEdit($id, $type, $examId)
    {

        try {
            $online_exam_question = OnlineExamQuestion::find($id);
            return view('backEnd.examination.online_exam_question_edit', compact('id', 'type', 'examId', 'online_exam_question'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function onlineExamQuestionEdit(Request $request)
    {

        try {
            if ($request->question_type != 'M') {
                // $online_question = OnlineExamQuestion::find($request->id);
                if (checkAdmin()) {
                    $online_question = OnlineExam::find($request->id);
                } else {
                    $online_question = OnlineExam::where('id', $request->id)->where('school_id', Auth::user()->school_id)->first();
                }
                $online_question->online_exam_id = $request->online_exam_id;
                $online_question->type = $request->question_type;
                $online_question->mark = $request->mark;
                $online_question->title = $request->question_title;
                if ($request->question_type == "F") {
                    $online_question->suitable_words = $request->suitable_words;
                } elseif ($request->question_type == "T") {
                    $online_question->trueFalse = $request->trueOrFalse;
                }
                $result = $online_question->save();
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            } else {

                DB::beginTransaction();

                try {
                    // $online_question = OnlineExamQuestion::find($request->id);
                    if (checkAdmin()) {
                        $online_question = OnlineExamQuestion::find($request->id);
                    } else {
                        $online_question = OnlineExamQuestion::where('id', $request->id)->where('school_id', Auth::user()->school_id)->first();
                    }
                    $online_question->online_exam_id = $request->online_exam_id;
                    $online_question->type = $request->question_type;
                    $online_question->mark = $request->mark;
                    $online_question->title = $request->question_title;
                    $online_question->save();
                    $online_question->toArray();

                    OnlineExamQuestionMuOption::where('online_exam_question_id', $online_question->id)->delete();

                    $i = 0;
                    if (isset($request->option)) {
                        foreach ($request->option as $option) {
                            $i++;
                            $option_check = 'option_check_' . $i;
                            $online_question_option = new OnlineExamQuestionMuOption();
                            $online_question_option->online_exam_question_id = $online_question->id;
                            $online_question_option->title = $option;
                            $online_question_option->school_id = Auth::user()->school_id;
                            $online_question_option->organization_id = config('organization_id');
                            if (isset($request->$option_check)) {
                                $online_question_option->status = 1;
                            } else {
                                $online_question_option->status = 0;
                            }
                            $online_question_option->save();
                        }
                    }
                    DB::commit();
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                } catch (\Exception $e) {
                    DB::rollBack();
                }
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function onlineExamQuestionDelete(Request $request)
    {

        try {
            // $online_exam_question = OnlineExamQuestion::find($request->id);
            if (checkAdmin()) {
                $online_exam_question = OnlineExamQuestion::find($request->id);
            } else {
                $online_exam_question = OnlineExamQuestion::where('id', $request->id)->where('school_id', Auth::user()->school_id)->first();
            }
            if ($online_exam_question->type == "M") {
                OnlineExamQuestionMuOption::where('online_exam_question_id', $online_exam_question->id)->delete();
                $online_exam_question->delete();
            } else {
                $online_exam_question->delete();
            }
            Toastr::success('Operation successful', 'Success');
            return redirect()->back();
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function onlineExamMarksRegister($id)
    {


        try {
            // $online_exam_question = OnlineExam::find($id);
            if (checkAdmin()) {
                $online_exam_question = OnlineExam::find($id);
            } else {
                $online_exam_question = OnlineExam::where('id', $id)->where('school_id', Auth::user()->school_id)->first();
            }
            $students = SmStudent::where('class_id', $online_exam_question->class_id)->where('section_id', $online_exam_question->section_id)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();
            $present_students = [];
            foreach ($students as $student) {
                $take_exam = SmStudentTakeOnlineExam::where('student_id', $student->id)->where('online_exam_id', $online_exam_question->id)->first();
                if ($take_exam != "") {
                    $present_students[] = $student->id;
                }
            }

            return view('backEnd.examination.online_exam_marks_register', compact('online_exam_question', 'students', 'present_students'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function onlineExamMarksStore(Request $request)
    {
        try {
            // OnlineExamMark::where('exam_id', $request->exam_id)->delete();
            if (checkAdmin()) {
                OnlineExamMark::where('exam_id', $request->exam_id)->delete();
            } else {
                OnlineExamMark::where('exam_id', $request->exam_id)->where('school_id', Auth::user()->school_id)->delete();
            }
            $counter = 0;
            foreach ($request->students as $student) {
                $counter++;

                $marks = 'marks_' . $counter;
                $abs = 'abs_' . $counter;

                $online_mark = new OnlineExamMark();
                $online_mark->exam_id = $request->exam_id;
                $online_mark->subject_id = $request->subject_id;
                $online_mark->student_id = $student;
                $online_mark->organization_id = config('organization_id');
                if (isset($request->$abs)) {
                    $online_mark->abs = $request->$abs;
                } else {
                    $online_mark->marks = $request->$marks;
                    $online_mark->abs = 0;
                }
                $online_mark->save();
            }
            Toastr::success('Operation successful', 'Success');
            return redirect('online-exam');
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function onlineExamResult($id)
    {
        try {
            // $online_exam_question = OnlineExam::find($id);
            if (checkAdmin()) {
                $online_exam_question = OnlineExam::find($id);
            } else {
                $online_exam_question = OnlineExam::where('id', $id)->where('school_id', Auth::user()->school_id)->first();
            }
            $students = SmStudent::where('class_id', $online_exam_question->class_id)->where('section_id', $online_exam_question->section_id)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();
            $present_students = [];
            foreach ($students as $student) {
                $take_exam = SmStudentTakeOnlineExam::where('student_id', $student->id)->where('online_exam_id', $online_exam_question->id)->first();
                if ($take_exam != "") {
                    $present_students[] = $student->id;
                }
            }
            $total_marks = 0;
            foreach ($online_exam_question->assignQuestions as $assignQuestion) {
                $total_marks = $total_marks + $assignQuestion->questionBank->marks;
            }
            return view('backEnd.examination.online_exam_result_view', compact('online_exam_question', 'students', 'present_students', 'total_marks'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function onlineExamQuestionAssign(Request $request)
    {
        try {
            if ($request->checkbox) {
                $assign = new OnlineExamQuestionAssign();
                $assign->online_exam_id = $request->online_exam_id;
                $assign->question_bank_id = $request->questions;
                $assign->school_id = Auth::user()->school_id;
                $assign->organization_id = config('organization_id');
                $assign->save();
            } else {
                OnlineExamQuestionAssign::where('question_bank_id', $request->questions)->delete();
                if (checkAdmin()) {
                    OnlineExamQuestionAssign::where('question_bank_id', $request->questions)->delete();
                } else {
                    OnlineExamQuestionAssign::where('question_bank_id', $request->questions)->where('school_id', Auth::user()->school_id)->delete();
                }
            }
            return response()->json('success');
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function viewOnlineQuestionModal($id)
    {

        try {
            // $question_bank = SmQuestionBank::find($id);
            if (checkAdmin()) {
                $question_bank = SmQuestionBank::find($id);
            } else {
                $question_bank = SmQuestionBank::where('id', $id)->where('school_id', Auth::user()->school_id)->first();
            }
            return view('backEnd.examination.online_eaxm_question_view_modal', compact('question_bank'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function onlineExamMarking($exam_id, $s_id)
    {
        $online_exam_info = OnlineExam::find($exam_id);

        try {
            $online_take_exam_mark = SmStudentTakeOnlineExam::where('online_exam_id', $exam_id)
                ->where('student_id', $s_id)
                ->where('academic_id', getAcademicId())
                ->first();
            $assign_questions = OnlineExamQuestionAssign::where('online_exam_id', $exam_id)->get();
            $total_mark = 0;
            foreach ($assign_questions as $q) {
                $marks = SmQuestionBank::find($q->question_bank_id)->marks;
                $total_mark += $marks;
            }

            if ($online_exam_info->auto_mark == 1) {
                $take_online_exam = SmStudentTakeOnlineExam::where('online_exam_id', $exam_id)->where('student_id', $s_id)->where('academic_id', getAcademicId())->first();

                return view('backEnd.examination.online_answer_auto_marking', compact('take_online_exam', 'online_exam_info', 's_id', 'online_take_exam_mark', 'assign_questions', 'total_mark'));

            } else {
                $take_online_exam = SmStudentTakeOnlineExam::where('online_exam_id', $exam_id)->where('student_id', $s_id)->where('academic_id', getAcademicId())->first();

                return view('backEnd.examination.online_answer_marking', compact('take_online_exam', 'online_exam_info', 's_id', 'online_take_exam_mark', 'assign_questions', 'total_mark'));

            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function onlineExamMarkingStore(Request $request)
    {
        try {

            // return $request;
            $exam_questions = OnlineExamQuestionAssign::where('online_exam_id', $request->online_exam_id)->get();

            foreach ($exam_questions as $question) {
                $question_id = $question->question_bank_id;
                $trueFalse = 'trueOrFalse_' . $question_id;
                $trueFalse = $request->$trueFalse;

                $suitable_words = 'suitable_words_' . $question_id;
                $suitable_words = $request->$suitable_words;

                $mcq_answer = 'options_' . $question_id;
                $mcq_answer = $request->$mcq_answer;
                $exam_info = OnlineExam::find($request->online_exam_id);
                $question_info = SmQuestionBank::find($question_id);
                $question_answer = OnlineExamStudentAnswerMarking::where('online_exam_id', $request->online_exam_id)
                    ->where('student_id', $request->student_id)->where('question_id', $question_id)->first();


                if ($question_answer == null) {
                    $question_answer = new OnlineExamStudentAnswerMarking();
                }
                $question_answer->online_exam_id = $exam_info->id;
                $question_answer->student_id = $request->student_id;
                $question_answer->question_id = $question_id;
                if ($question_info->type == 'M') {
                    $question_answer->user_answer = $mcq_answer;
                    $currect_answer = $question_info->questionMu->where('status', 1)->first();
                    if ($mcq_answer != null && $mcq_answer == $currect_answer->id) {
                        $question_answer->answer_status = 1;
                        $question_answer->obtain_marks = $question_info->marks;
                    } else {
                        $question_answer->answer_status = 0;
                        $question_answer->obtain_marks = 0;

                    }
                    // return $mcq_answer;
                }
                if ($question_info->type == 'MI') {


                    if ($question_info->answer_type == 'radio') {
                        $question_answer->user_answer = $mcq_answer;
                        $currect_answer = $question_info->questionMu->where('status', 1)->first();
                        if ($mcq_answer != null && $mcq_answer == $currect_answer->id) {
                            $question_answer->answer_status = 1;
                            $question_answer->obtain_marks = $question_info->marks;
                        } else {
                            $question_answer->answer_status = 0;
                            $question_answer->obtain_marks = 0;

                        }
                    } else {
                        $image_answers = OnlineExamStudentAnswerMarking::where('online_exam_id', $request->online_exam_id)
                            ->where('student_id', $request->student_id)->where('question_id', $question_id)->get();
                        // return $request->marks;
                        // return $image_answers;
                        // $currect_answer=$question_info->questionMu->where('status',1)->get();
                        // return $question_info->questionMu;


                        $student_answers = [];
                        foreach ($image_answers as $key => $value) {
                            $student_answers[] = (int)$value->user_answer;
                        }

                        $question_answers = [];

                        foreach ($question_info->questionMu as $key => $value) {
                            if ($value->status == 1) {
                                $question_answers[] = $value->id;
                            }

                        }

                        // return $question_answers;

                        // return $question_info;

                        sort($student_answers);
                        sort($question_answers);


                        if ($student_answers === $question_answers) {
                            $question_answer->answer_status = 1;
                            $question_answer->obtain_marks = $question_info->marks;
                        } else {
                            $question_answer->answer_status = 0;
                            $question_answer->obtain_marks = 0;

                        }
                    }


                }
                if ($question_info->type == 'T') {
                    $question_answer->user_answer = $trueFalse;
                    $currect_answer = $question_info->trueFalse;
                    if ($trueFalse != null && $trueFalse == $currect_answer) {
                        $question_answer->answer_status = 1;
                        $question_answer->obtain_marks = $question_info->marks;
                    } else {
                        $question_answer->answer_status = 0;
                        $question_answer->obtain_marks = 0;

                    }
                }
                if ($question_info->type == 'F') {
                    $question_answer->user_answer = $suitable_words;
                    $currect_answer = $question_info->suitable_words;
                    if ($suitable_words != null && in_array($question_id, $request->marks)) {
                        $question_answer->answer_status = 1;
                        $question_answer->obtain_marks = $question_info->marks;
                    } else {
                        $question_answer->answer_status = 0;
                        $question_answer->obtain_marks = 0;

                    }
                    //  return $suitable_words;
                }

                $question_answer->school_id = Auth::user()->school_id;
                $question_answer->marked_by = Auth::user()->id;
                $question_answer->save();
            }

            $total_obtain_marks = OnlineExamStudentAnswerMarking::where('online_exam_id', $request->online_exam_id)
                ->where('student_id', $request->student_id)->sum('obtain_marks');

            $online_take_exam_mark = SmStudentTakeOnlineExam::where('online_exam_id', $request->online_exam_id)->where('student_id', $request->student_id)->where('academic_id', getAcademicId())->first();

            $online_take_exam_mark->total_marks = $total_obtain_marks;
            $online_take_exam_mark->status = 2;
            $online_take_exam_mark->save();

            $wrong = OnlineExamStudentAnswerMarking::where('user_answer', '=', '')->delete();

            Toastr::success('Operation successful', 'Success');
            return redirect('online-exam-marks-register/' . $request->online_exam_id);
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function onlineExamReport(Request $request)
    {
        try {
            if (moduleStatusCheck('OnlineExam') == TRUE) {
                $exams = InfixOnlineExam::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            } else {
                $exams = OnlineExam::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            }

            $classes = Classes::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['exams'] = $exams->toArray();
                $data['classes'] = $classes->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('backEnd.reports.online_exam_report', compact('exams', 'classes'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function onlineExamReportSearch(Request $request)
    {

        $input = $request->all();
        $validator = Validator::make($input, [
            'exam' => 'required',
            'class' => 'required',
            'section' => 'required'
        ]);


        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            date_default_timezone_set("Asia/Dhaka");
            $present_date_time = date("Y-m-d H:i:s");

            // $online_exam_question = OnlineExam::find($request->exam);
            $students = SmStudent::where('class_id', $request->class)->where('section_id', $request->section)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();
            if ($students->count() == 0 && $online_exam == "") {
                Toastr::error('No Result Found', 'Failed');
                return redirect('online-exam-report');
            }
            if (moduleStatusCheck('OnlineExam') == TRUE) {
                $online_exam_question = InfixOnlineExam::find($request->exam);
                $online_exam = InfixOnlineExam::where('class_id', $request->class)->where('section_id', $request->section)->where('id', $request->exam)->where('end_date_time', '<', $present_date_time)->where('status', 1)->first();
                $present_students = [];
                foreach ($students as $student) {
                    $take_exam = InfixStudentTakeOnlineExam::where('student_id', $student->id)->where('online_exam_id', $online_exam_question->id)->first();
                    if ($take_exam != "") {
                        $present_students[] = $student->id;
                    }
                }
                $total_marks = 0;
                foreach ($online_exam_question->assignQuestions as $assignQuestion) {
                    $total_marks = $total_marks + $assignQuestion->questionBank->marks;
                }
                $exams = InfixOnlineExam::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            } else {
                $online_exam_question = OnlineExam::find($request->exam);
                $online_exam = OnlineExam::where('class_id', $request->class)->where('section_id', $request->section)->where('id', $request->exam)->where('end_date_time', '<', $present_date_time)->where('status', 1)->first();

                $present_students = [];
                foreach ($students as $student) {
                    $take_exam = SmStudentTakeOnlineExam::where('student_id', $student->id)->where('online_exam_id', $online_exam_question->id)->first();
                    if ($take_exam != "") {
                        $present_students[] = $student->id;
                    }
                }
                $total_marks = 0;
                foreach ($online_exam_question->assignQuestions as $assignQuestion) {
                    $total_marks = $total_marks + $assignQuestion->questionBank->marks;
                }
                $exams = OnlineExam::where('active_status', 1)->where('academic_id', getAcademicId())->where('school_id', Auth::user()->school_id)->get();

            }


            if (teacherAccess()) {
                $teacher_info = SmStaff::where('user_id', Auth::user()->id)->first();
                $classes = SmAssignSubject::where('teacher_id', $teacher_info->id)->join('sm_classes', 'sm_classes.id', 'sm_assign_subjects.class_id')
                    ->where('sm_assign_subjects.academic_id', getAcademicId())
                    ->where('sm_assign_subjects.active_status', 1)
                    ->where('sm_assign_subjects.school_id', Auth::user()->school_id)
                    ->select('sm_classes.id', 'class_name')
                    ->groupBy('sm_classes.id')
                    ->get();
            } else {
                $classes = Classes::where('active_status', 1)
                    ->where('academic_id', getAcademicId())
                    ->where('school_id', Auth::user()->school_id)
                    ->get();
            }
            $class_id = $request->class;
            $exam_id = $request->exam;

            $clas = Classes::find($request->class);
            $sec = SmSection::find($request->section);
            return view('backEnd.reports.online_exam_report', compact('online_exam_question', 'students', 'present_students', 'total_marks', 'exams', 'classes', 'class_id', 'exam_id', 'clas', 'sec'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}
<?php

namespace App\Jobs;

use App\Employee;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\EmailSetting;

use Illuminate\Contracts\Mail\Mailer;
use Illuminate\Support\Arr;

class SendStudentInterviewMailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user_info = [];
    protected $sender;
    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_info, $sender)
    {
        $this->user_info = $user_info;
        $this->sender = $sender;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(Mailer $mailer)
    {



        foreach($this->user_info as $info){


            $mailer->send('modules.site.templates.wajeha.backEnd.studentInformation.student_interview', ['data'=> $info], function ($message) use($info) {

                $message->from($this->sender['system_email'], $this->sender['organization_name']);

                $message->to($info['email'])->subject('Interview at ITQAN for the '.$info['programTitle'].' program')->bcc($info['InterviewerEmails']);

            });
        } 
    }
}

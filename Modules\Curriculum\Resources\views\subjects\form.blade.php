<div class="form-group {{ $errors->has('language') ? 'has-error' : ''}}">
    {!! Form::label('language', 'Subject Language', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('language', languages()  , null, ['class' => 'form-control']) !!}
        {!! $errors->first('language', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="nav-tabs-custom">
    <ul class="nav nav-tabs">
        @foreach(config('app.locales') as $key => $language)
        <li @if($key == 0 ) class="active" @endif>
            <a aria-expanded="true" data-toggle="tab" href="#{{$language}}">
                {{strtoupper($language)}}
            </a>
        </li>
        @endforeach
    </ul>
    <div class="tab-content">
        @foreach(config('app.locales') as $key => $language)
        <div class="tab-pane clearfix @if($key == 0 ) active @endif" id="{{$language}}">
            <div class="col-md-12">
                <div class="form-group {{ $errors->has('translate.'.$language.'.title') ? 'has-error' : ''}}">
                    {!! Form::label('title', 'Title ['.$language.']', ['class' => 'control-label']) !!}
                
                    {!! Form::text('translate['.$language.'][title]', isset($subject) && isset($subject->translate($language)->title) ? $subject->translate($language)->title : '' , ['class' => 'form-control rich' , 'placeholder' => 'title']) !!}
                    {!! $errors->first('translate.'.$language.'.title', '
                    <p class="help-block">
                        :message
                    </p>
                    ') !!}
                </div>
            </div>
        </div>
        <!-- /.tab-pane -->
        @endforeach
    </div>
    <!-- /.tab-preface -->
</div>
<hr>
<div class="col-md-12">
    <div class="form-group {{ $errors->has('preface') ? 'has-error' : ''}} preface" >
        {!! Form::label('preface', 'Preface' , ['class' => 'control-label']) !!}
        {!! Form::textarea('preface', null , ['class' => 'form-control']) !!}
        {!! $errors->first('preface', '
        <p class="help-block">
            :message
        </p>
        ') !!}
    </div>
    <div class="form-group {{ $errors->has('status') ? 'has-error' : ''}}">
        {!! Form::label('status', 'Status', ['class' => 'col-md-4 control-label']) !!}
        <div class="col-md-6">
            {!! Form::select('status', [1 => 'active'] , null, ['class' => 'form-control']) !!}
            {!! $errors->first('status', '<p class="help-block">:message</p>') !!}
        </div>
    </div>
</div>

<div class="form-group">
    <div class="col-md-offset-4 col-md-4">
        {!! Form::submit(isset($submitButtonText) ? $submitButtonText : trans('common.create'), ['class' => 'btn btn-primary']) !!}
    </div>
</div>

@section('css')
<link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.6/summernote.css" rel="stylesheet">
@endsection
@section('js')
<script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.6/summernote.js"></script>
<script>
    $(document).ready(function() {
        $('textarea').summernote({
            minHeight : 300
        });
    });
</script>
@endsection

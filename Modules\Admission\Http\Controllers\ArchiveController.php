<?php

namespace Modules\Admission\Http\Controllers;

use App\Classes;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Admission;
use App\AdmissionInterview;
use App\AdmissionInterviewer;
use App\Student;
use App\Center;
use App\Program;
use Illuminate\Support\Facades\DB;
use App\Guardian;
use App\Employee;
use App\Role;
use App\Cen_Emp;

use App\StudentHefzPlan;
use Illuminate\Database\Eloquent\Model;
class ArchiveController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index(Request $request)
    {



        $filter_name = $request->filter_name;
        $filter = $request->filter;

        $perPage = 25;

        $students = Student::onlyTrashed()->get();

     
     
        return view("admission::student.archive.index" ,compact('students' , 'filter_name','filter'));
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view("admission::student.archive");
    }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        return $request;
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show($id)
    {
        $student = Student::onlyTrashed()
        ->where('id', $id)
        ->restore();
        $student=Student::findOrFail($id);
        $student->delete_reason=null;
        $student->deleted_at=null;
        $student->status='new_admission';

        $student->delete_notice=null;
        $student->save();

        Admission::onlyTrashed()->where('student_id',$student->id)->restore();
        Admission::where('student_id',$student->id)->update(['status' => 'new_admission']);



        flash('Student :'.$student->full_name.'  Restored!!'); 

        return redirect()->back();
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit()
    {
        return view('admission::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(Request $request)
    {
        $admission = Admission::findOrFail($request->admission_id);

    //   return $request->all();
        $this->validate($request, [
            'center_id' => 'required',
            'class_id' => 'required',
            'admission_status' => 'required',
            'program_id' => 'required'
        ]);

        $admission->center_id = $request->center_id;
        $admission->class_id = $request->class_id;
        $admission->status = $request->admission_status;

        $admission->save();
        $admission->programs()->sync([$request->program_id]);



        flash('Admission details were Updated');
        return redirect()->back();
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function approve(Request $request)
    {
        $admission = Admission::findOrFail($request->admission_id);

        // return $admission->programs;

        $waiting_for_interview = false;
        $approved_programs = 0;

        if(!$admission){}

        
        // get admission programs

        foreach ($admission->programs as $program ) 
        {
            // check if the admission aproved
            if($request->interview[$program->id]['approve'])
            {
                $approved_programs++;

                // check if an interview is required
                if($program->require_interview)
                {
                    // validate request

                    $fields_names = [ "interview_time" => "Interview Time"];

                    $this->validate($request, [
                        "interview.*.interview_time" => 'required|date_format:Y-m-d H:i',
                        "interview.*.location" => 'required'
                    ], $fields_names);

                    $waiting_for_interview = true;

                    // set interview details 

                    $interview_details = $request->interview[$program->id];

                    $interview_details['admission_id'] = $request->admission_id;
                    $interview_details['program_id'] =  $program->id;

                    $interview_details['status'] = 'waiting_for_interview';

                    $interview = AdmissionInterview::create($interview_details);


                    // set interview committee

                    $interview_committee = $interview_details['committee'];

                    foreach($interview_committee as $interviewer){
                        AdmissionInterviewer::create([
                            'admission_interview_id' => $interview->id,
                            'employee_id' => $interviewer
                        ]);
                    }
                }
            }
        }


        if($waiting_for_interview){
            $admission->status = 'waiting_for_interview';
        }else if($approved_programs == $admission->programs->count()){
            $admission->status = 'offer';            
        }else if($approved_programs > 0){
            $admission->status = 'conditional_offer';                        
        }else{
            $admission->status = 'rejected';                        
        }

        $admission->save();


        // change admission status & student status
        if($request->ajax()){
            return response()->json([
                "status" => "success"
            ]);
        }


        return redirect()->back();
    }

    public function setOrientation()
    {
        $request = request();
        
        $admission = Admission::findOrFail($request->admission_id);

        $this->validate($request , [
            'admission_id' => 'required|numeric',
            'employee_id' => 'required|numeric',
            'orientation_time' => 'required|date',
            'location' => 'required'
        ]);

        $requestData = $request->all();

        $requestData['organization_id'] = config('organization_id');
        $requestData['created_by'] = auth()->user()->id;

        \App\AdmissionOrientation::create($requestData);
        $admission->status = "waiting_for_orientation";
        
        $admission->save();

        flash('Orintation Session Has Been Set.');

        return redirect()->back();

    }

    public function finalize(Request $request)
    {
        $admission = Admission::findOrFail($request->admission_id);

        $admission->status = 'active';
        $admission->start_date = date('Y-m-d');

        $admission->save();

        $student = Student::findOrFail($admission->student_id);
        $student->status = 'active';
        $student->save();
        // Check if the program is Nuraniyah
        $program = Program::find($admission->program_id);
        if (\Illuminate\Support\Str::contains(strtolower($program->title), ['nuraniyah', 'nouranya'])) {
            // Fetch the ProgramLevel where the translated title contains 'level 1' or 'level1'
            $levelId = \App\ProgramLevel::whereHas('translations', function ($query) {
                $query->where('title', 'LIKE', '%level 1%')
                    ->orWhere('title', 'LIKE', '%level1%');
            })->first()->id;
            // Set student level to 1 if it belongs to a Nuraniyah program
            $studentProgramLevel = $student->studentProgramLevels()->updateOrCreate(
                ['student_id' => $student->id, 'class_id' => $admission->class_id],
                ['level_id' => $levelId, 'status' => 'active']
            );
        }
        if (\Illuminate\Support\Str::contains(strtolower($program->title), ['Ijazah and Sanad', 'ijazah and sanad'])) {
            // Fetch the ProgramLevel where the translated title contains 'level 1' or 'level1'
            $levelId = \App\ProgramLevel::whereHas('translations', function ($query) {
                $query->where('title', 'LIKE','%Level 1: Preparation Course%');
            })->first()->id;
            // Set student level to 1 if it belongs to a Nuraniyah program
            $studentProgramLevel = $student->studentProgramLevels()->updateOrCreate(
                ['student_id' => $student->id, 'class_id' => $admission->class_id],
                ['level_id' => $levelId, 'status' => 'active']
            );
        }
        if(!\App\ClassStudent::where('class_id' ,  $admission->class_id)->where('student_id' ,  $admission->student_id)->first()){

            $join_class = new \App\ClassStudent();
    
            $join_class->student_id = $admission->student_id;
            $join_class->class_id = $admission->class_id;
            $join_class->start_date = date('Y-m-d');
    
            $join_class->save();
            flash('Student was added to  class successfully.');
        }else{
            flash('Student already has joined the class.');
        }

    


        return redirect()->back();

    }

    public function createHefzPlan(Request $request , $redirect = false)
    {
        $this->validate($request,[
            "hefz.study_direction" => "required",
            "hefz.start_from_surat" => "required",
            "hefz.start_from_ayat" => "required",
            "hefz.num_to_memorize" => "required",
            "hefz.memorization_mood" => "required",
            "hefz.pages_to_revise" => "required",
        ]);

        $plan = new StudentHefzPlan();

        $plan->study_direction = $request->hefz['study_direction'];
        $plan->start_from_surat = $request->hefz['start_from_surat'];
        $plan->start_from_ayat = $request->hefz['start_from_ayat'];
        $plan->num_to_memorize = $request->hefz['num_to_memorize'];
        $plan->memorization_mood = $request->hefz['memorization_mood'];
        $plan->pages_to_revise = $request->hefz['pages_to_revise'];
        $plan->student_id = $request->student_id;
        $plan->organization_id = config('organization_id');
        $plan->created_by = auth()->user()->id;
        if(isset($request->hefz['start_date'])){
            $plan->start_date = $request->hefz['start_date'];
            $plan->status = 'active';
        }else{
            $plan->status = 'waiting_for_approval';
        }
        
        $plan->save();
        
        if($redirect){
            Session::flash('flash_message', 'Student Study Plan was Added <Successfuly></Successfuly>!');
        }
        return redirect()->back();

    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }

}

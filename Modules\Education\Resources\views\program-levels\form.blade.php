@if(isset($program_id))
    {!! Form::hidden('program_id',$program_id) !!}
@else
<div class="form-group {{ $errors->has('program_id') ? 'has-error' : ''}}">
    {!! Form::label('program_id', 'Program Id', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::number('program_id', null, ['class' => 'form-control']) !!}
        {!! $errors->first('program_id', '<p class="help-block">:message</p>') !!}
    </div>
</div>
@endif


<div class="nav-tabs-custom">
<ul class="nav nav-tabs">
    @foreach(config('app.locales') as $key => $language)
    <li @if($key == 0 ) class="active" @endif>
        <a aria-expanded="true" data-toggle="tab" href="#level{{$language}}">
            {{strtoupper($language)}}
        </a>
    </li>
    @endforeach
</ul>
<div class="tab-content">
    @foreach(config('app.locales') as $key => $language)
    <div class="tab-pane clearfix @if($key == 0 ) active @endif" id="level{{$language}}">
        <div class="col-md-12">
            <div class="form-group {{ $errors->has('translate.'.$language.'.title') ? 'has-error' : ''}}">
                {!! Form::label('title', 'Title ['.$language.']', ['class' => 'control-label']) !!}
            
                {!! Form::text('translate['.$language.'][title]', isset($programlevel) && isset($programlevel->translate($language)->title) ? $programlevel->translate($language)->title : '' , ['class' => 'form-control' , 'placeholder' => 'title']) !!}
                {!! $errors->first('translate.'.$language.'.title', '
                <p class="help-block">
                    :message
                </p>
                ') !!}
            </div>
        </div>
    </div>
    <!-- /.tab-pane -->
    @endforeach
</div>
<!-- /.tab-description -->
</div>

<div class="form-group {{ $errors->has('status') ? 'has-error' : ''}}">
    {!! Form::label('status', 'Status', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('status', [1 => 'active'], null, ['class' => 'form-control']) !!}
        {!! $errors->first('status', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group">
    <div class="col-md-offset-4 col-md-4">
        {!! Form::submit(isset($submitButtonText) ? $submitButtonText : trans('common.create'), ['class' => 'btn btn-primary']) !!}
    </div>
</div>

<?php

namespace App;

use App\Notifications\EmployeeResetPassword;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;

use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\SoftDeletes;

class CenterEmployee extends Pivot
{

    protected $table = 'cen_emps';
    public $incrementing = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */





    public function hefzPlan(){



        return $this->hasMany(StudentHefzPlan::class,'center_id','cen_id');
    }





    // public static function boot()
    // {
    //     static::addGlobalScope(new OrganizationScope);
    // }


    // protected $attributes = array(
    //    'organization_id' => 1//Config::get('organization_id'),
    // );

//    protected static function boot()
//    {
//        parent::boot();
//        // dd(config('organization_id'));
//
//        static::addGlobalScope(new OrganizationScope);
//    }







}

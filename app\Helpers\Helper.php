<?php

use App\Classes;
use App\LeaveDefine;
use App\MoshafSurah;
use App\Organization;
use App\Student;
use App\Guardian;
use App\DateFormat;
use App\GeneralSettings;
use Illuminate\Support\Facades\Auth;
use App\Employee;


if (! function_exists('calculateUniqueLessonsPerStudent')) {
    function calculateUniqueLessonsPerStudent($collection, $fromField, $toField) {
        $uniqueLessons = [];

        // Group the collection by student_id to calculate per-student unique lessons
        $grouped = $collection->groupBy('student_id');

        foreach ($grouped as $studentId => $reports) {
            $studentLessons = [];

            foreach ($reports as $report) {
                $from = $report->{$fromField};
                $to = $report->{$toField};

                // Ensure both from and to are not null and are numeric
                if (!is_null($from) && !is_null($to) && is_numeric($from) && is_numeric($to)) {
                    $from = (int)$from;
                    $to = (int)$to;

                    // Ensure from <= to
                    if ($from <= $to) {
                        // Merge the range of lessons
                        $studentLessons = array_merge($studentLessons, range($from, $to));
                    }
                }
            }

            // Remove duplicates for the student and add to the overall unique lessons
            $uniqueLessons = array_merge($uniqueLessons, array_unique($studentLessons));
        }

        // Remove duplicates across all students
        return count(array_unique($uniqueLessons));
    }

}

if (! function_exists('calculateIjazasanadLevel1AttendancePercentage')) {
    function calculateIjazasanadLevel1AttendancePercentage($classId, $year, $month,$totalClasses) {


        $result = DB::select('CALL CalculateIjazasanadlevel1AttendancePercentage(?, ?, ?, ?)', [
            $classId,
            $month,
            $year,
            $totalClasses
        ]);


        return $result[0]->attendance_percentage;
    }
}
if (! function_exists('calculateTotalClassesInMonth')) {
 function calculateTotalClassesInMonth($classId, $year, $month)
{
    $classTimetable = Classes::find($classId)->timetable;
    if (!$classTimetable) {
        return 0;
    }

    $totalClasses = 0;
    $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);

    for ($day = 1; $day <= $daysInMonth; $day++) {
        $dayOfWeek = strtolower(date('D', strtotime("$year-$month-$day")));
        if (!is_null($classTimetable->$dayOfWeek)) {
            $totalClasses++;
        }
    }

    return $totalClasses;
}
}


if (! function_exists('calculateDailyHours')) {
    function calculateDailyHours($employeeId, $date)
    {
        // Fetch attendance records for the given employee and date
        $attendances = DB::table('attendances')
            ->where('employee_id', $employeeId)
            ->whereDate('clock', $date)
            ->orderBy('clock')
            ->get();

        $timeRanges = [];
        $lastInTime = null;

        foreach ($attendances as $attendance) {
            if ($attendance->type === 'in') {
                $lastInTime = \Carbon\Carbon::parse($attendance->clock);
            } elseif ($attendance->type === 'out' && $lastInTime) {
                $outTime = \Carbon\Carbon::parse($attendance->clock);
                $timeRanges[] = [$lastInTime, $outTime];
                $lastInTime = null;
            }
        }

        // Sort the time ranges just in case
        usort($timeRanges, function($a, $b) {
            return $a[0]->timestamp - $b[0]->timestamp;
        });

        // Merge overlapping time ranges
        $mergedRanges = [];
        foreach ($timeRanges as $range) {
            if (empty($mergedRanges)) {
                $mergedRanges[] = $range;
            } else {
                $lastMergedRange = &$mergedRanges[count($mergedRanges) - 1];
                if ($range[0]->lte($lastMergedRange[1])) {
                    // Overlapping or adjacent, merge the ranges
                    $lastMergedRange[1] = max($lastMergedRange[1], $range[1]);
                } else {
                    $mergedRanges[] = $range;
                }
            }
        }


        // Calculate the total worked hours (ignoring minutes, on an hour basis)
        $totalWorkedHours = 0;
        foreach ($mergedRanges as $range) {
            $hours = $range[1]->diffInHours($range[0]);
            $totalWorkedHours += $hours;
        }

        return $totalWorkedHours; // Return the total worked hours on an hour basis
    }}



if (! function_exists('convertOffsetToTimezoneIdentifier')) {

     function convertOffsetToTimezoneIdentifier($offset)
    {
        // Initialize DateTime object in UTC
        $dateTime = new DateTime('now', new DateTimeZone('UTC'));

        // Apply the offset by adding or subtracting the duration
        if (preg_match('/UTC([+-])(\d{2}):(\d{2})/', $offset, $matches)) {
            $sign = $matches[1];
            $hours = (int)$matches[2];
            $minutes = (int)$matches[3];

            // Calculate the total offset in seconds
            $totalOffsetInSeconds = ($hours * 3600) + ($minutes * 60);
            if ($sign === '-') {
                $totalOffsetInSeconds = -$totalOffsetInSeconds;
            }

            // Adjust the DateTime object by this offset
            $dateTime->modify(($sign === '+' ? '+' : '-') . $hours . ' hours ' . $minutes . ' minutes');
        }

        // Get the offset in seconds from the adjusted DateTime
        $offsetInSeconds = $dateTime->getOffset();

        // Initialize the variable to store the timezone identifier
        $timezoneIdentifier = null;

        // Loop through all timezones and find the one that matches the offset
        foreach (DateTimeZone::listIdentifiers() as $identifier) {
            $timezone = new DateTimeZone($identifier);
            if ($timezone->getOffset($dateTime) == $offsetInSeconds) {
                $timezoneIdentifier = $identifier;
                break;
            }
        }

        // Return the timezone identifier
        return $timezoneIdentifier;
    }

}
if (! function_exists('containsSubstrings')) {

    function containsSubstrings($string, $substrings) {
        foreach ($substrings as $substring) {
            if (stripos($string, $substring) !== false) {
                return true;
            }
        }
        return false;
    }


}
if (! function_exists('studentRedirectPath')) {

    function studentRedirectPath()
    {


        if (\Auth::guard("web")->user()->hasRole("parent")) {
            $redirectTo = '/parent-dashboard';

        } elseif (\Auth::guard("web")->user()->hasRole("student")) {
            $redirectTo = '/student-dashboard';

        } else {


            // redirect to the studentapplication route
            $redirectTo = route("student.application.form");

        }


        return $redirectTo;
    }

}


if (! function_exists('gradientColor')) {

    function gradientColor($index, $total) {
        $startColor = [255, 0, 0]; // Red
        $endColor = [0, 0, 255]; // Blue
        $step = [
            ($endColor[0] - $startColor[0]) / $total,
            ($endColor[1] - $startColor[1]) / $total,
            ($endColor[2] - $startColor[2]) / $total,
        ];

        $color = [
            (int)($startColor[0] + ($step[0] * $index)),
            (int)($startColor[1] + ($step[1] * $index)),
            (int)($startColor[2] + ($step[2] * $index)),
        ];

        return "rgb(" . implode(',', $color) . ")";
    }




}

if (! function_exists('formatStudentName')) {

function formatNameProperly($name) {
    $name = strtolower($name); // Convert the name to lowercase
    $formattedName = preg_replace_callback('/([^\s\.]+)(\s*\.?\s*)/', function($matches) {
        $word = $matches[1];
        $spaceOrDot = $matches[2];
        return ucfirst($word) . $spaceOrDot;
    }, $name);

    return $formattedName;
}
}
if (! function_exists('generateEmployeeNumber')) {
    function generateEmployeeNumber()
    {
        // Get the maximum numeric value of the employee_number column from the employees table
        $maxNumber = Employee::select(DB::raw('MAX(CAST(SUBSTR(employee_number, -LENGTH(employee_number) + 1) AS UNSIGNED)) AS max_number'))->value('max_number');

        // Generate a new employee number by incrementing the maximum numeric value by 1
        $newNumber = $maxNumber + 1;

        // Generate a new employee number by concatenating a prefix with the new numeric value
        $newEmployeeNumber = 'EMP' . $newNumber;

        return $newEmployeeNumber;
    }
}
if (! function_exists('generateCertificateSerialNumber')) {
    function generateCertificateSerialNumber()
    {
        $serialNumber = strtoupper(str_random(10));
        while (\App\GeneratedStudentCertificate::where('serial_number', $serialNumber)->first()) {
            $serialNumber = strtoupper(str_random(10));
        }
        return $serialNumber;
    }
}


//formats price to home default price with convertion
if (!function_exists('single_price')) {
    function single_price($price)
    {
        if (!$price)
            $price = 0;

        if (\DB::table('general_settings')->first()->currency_symbol != null) {
            return \DB::table('general_settings')->first()->currency_symbol . " " . number_format($price, 2);
        } else {
            return number_format($price, 2) . " bdt";
        }
    }
}
if (!function_exists('showDate')){
    function showDate($date)
    {
        if (!$date){
            return;
        }


        return \Carbon\Carbon::parse($date)->formatLocalized('%d %b, %Y');
    }
}

if (!function_exists('classProgram')){
    function classProgram($classId)
    {



        $class = Classes::findOrFail(107);
        $class =   $class->programs->first()->pivot->program_id;
        dd($class);


    }
}

function validationMessage($validation_rules)
{
    $message = [];
    foreach ($validation_rules as $attribute => $rules) {

        if (is_array($rules)) {
            $single_rule = $rules;
        } else {
            $single_rule = explode('|', $rules);
        }

        foreach ($single_rule as $rule) {
            $string = explode(':', $rule);
            $message [$attribute . '.' . $string[0]] = __('validation.' . $attribute . '.' . $string[0]);
        }
    }

    return $message;
}


if (!function_exists('userName')) {
    function userName($id)
    {


//        if (User::find($id) != null) {
//            return User::find($id)->full_name;
//        }
        if (Employee::find($id) != null) {
            return Employee::find($id)->full_name;
        }



        return null;
    }
}



function get_user_by_token($token)
{


    $guardName = explode("/", request()->route()->getPrefix());
    $guardName = $guardName[1]; // get the first child-prefix of locale prefix
    $records = DB::table($guardName . '_password_resets')->get();
    foreach ($records as $record) {
        if (Hash::check($token, $record->token)) {
            return $record->email;
        }
    }
}

function theme_path($path)
{
    return "site::templates." . config('website_theme', 'wajeha') . '.' . $path;
}

function studentLevel($hefz_from_surat, $to_surat)
{

    if ($hefz_from_surat == 1 or $to_surat == 1) {

        return 'Hafiz';
    }


    if (($hefz_from_surat >= 2 && $hefz_from_surat <= 4) or ($to_surat >= 2 && $to_surat <= 4)) {

        return 'Level Six';
    }

    if (($hefz_from_surat >= 5 && $hefz_from_surat <= 8) or ($to_surat >= 2 && $to_surat <= 8)) {

        return 'Level 5';
    }

    if (($hefz_from_surat >= 9 && $hefz_from_surat <= 17) or ($to_surat >= 5 && $to_surat <= 17)) {

        return 'Level 4';
    }

    if (($hefz_from_surat >= 18 && $hefz_from_surat <= 29) or ($to_surat >= 18 && $to_surat <= 29)) {

        return 'Level 3';
    }

    if (($hefz_from_surat >= 30 && $hefz_from_surat <= 45) or ($to_surat >= 30 && $to_surat <= 45)) {

        return 'Level 2';
    }

    if (($hefz_from_surat >= 46 && $hefz_from_surat <= 77) or ($to_surat >= 46 && $to_surat <= 77)) {

        return 'Level 1';
    }

    if (($hefz_from_surat >= 78 && $hefz_from_surat <= 114) or ($to_surat >= 78 && $to_surat <= 114)) {

        return 'التمهيدي';
    }


}

function change_language($to)
{
    // dd(request()->url);
    $url = request()->url();
    $url_array = explode('/', $url);

    $index = array_search(request()->segment(1), $url_array);

    $url_array[$index] = $to;

    $url = implode('/', $url_array);

    return $url;
}

function get_language_name($code, $local = true)
{
    if ($local) {
        return config('translator.languages.' . $code . '.local');
    } else {
        return config('translator.languages.' . $code . '.name');
    }
}

function is_permission_granted($role, $action)
{
    $permission = App\Permission::where('name', '=', $action)
        ->where('guard_name', '=', $role->guard_name)
        ->get();
    if (!count($permission))
        return false;
    if ($role->hasPermissionTo($action))
        return true;

    return false;
}

function is_guardian_of_student($guardian_id, $student_id)
{
    return (App\Student::where('id', '=', $student_id)
            ->where('guardian_id', '=', $guardian_id)
            ->get()->count()) > 0;
}

function languages()
{
    $languages = [];

    foreach (config('app.locales') as $lang) {
        $languages[$lang] = get_language_name($lang);
    }
    return $languages;
}

function userfolder($id = null)
{
    if ($id && is_int($id)) {
        return ($id * 128 - 24);
    }
    return (auth()->user()->id * 128 - 24);
}

function getSurahNameById($id)
{
    return \App\MoshafSurah::find($id)->name;
}

function getLevelBySurahId($id)
{
    switch ($id) {
        case $id < 3:
            return 5;
            break;

        case $id < 20:
            return 4;
            break;
        default:
            return 1;
    }
}

function generateUserName($data)
{

    $userName = $data['displayname'] . '_' . substr(strstr($data['email'], '@', true), 0, 2);


    $user_count = \App\User::where('display_name', '=', $data['displayname'])
        ->where('full_name', '=', $data['fullname'])->count();

    if ($user_count > 0) {
        $counter = $user_count + 1;
        $userName = $userName . $counter;


    }

    return substr(str_replace(" ", "_", $userName), 0, 7);
}

function organizationId()
{

    $domain = str_replace('http://', '', request()->root());
    $domain = str_replace('https://', '', $domain);
    if (Organization::where("domain", $domain)->exists()) {

        $organizationId = Organization::where("domain", $domain)->first()->id;
    } else {

        $organizationId = Organization::where("username", \Illuminate\Support\Arr::first(explode('.', request()->getHost())))->first()->id;
    }

    return $organizationId;
}

function hoursToMinutes($hours)
{
    $minutes = 0;
    if (strpos($hours, ':') !== false) {
        // Split hours and minutes.
        list($hours, $minutes) = explode(':', $hours);
    }
    return $hours * 60 + $minutes;
}

function uniord($u)
{
    // i just copied this function fron the php.net comments, but it should work fine!
    $k = mb_convert_encoding($u, 'UCS-2LE', 'UTF-8');
    $k1 = ord(substr($k, 0, 1));
    $k2 = ord(substr($k, 1, 1));
    return $k2 * 256 + $k1;
}

function is_arabic($str)
{
    if (mb_detect_encoding($str) !== 'UTF-8') {
        $str = mb_convert_encoding($str, mb_detect_encoding($str), 'UTF-8');
    }

    /*
    $str = str_split($str); <- this function is not mb safe, it splits by bytes, not characters. we cannot use it
    $str = preg_split('//u',$str); <- this function woulrd probably work fine but there was a bug reported in some php version so it pslits by bytes and not chars as well
    */
    preg_match_all('/.|\n/u', $str, $matches);
    $chars = $matches[0];
    $arabic_count = 0;
    $latin_count = 0;
    $total_count = 0;
    foreach ($chars as $char) {
        //$pos = ord($char); we cant use that, its not binary safe
        $pos = uniord($char);
//        echo $char ." --> ".$pos.PHP_EOL;

        if ($pos >= 1536 && $pos <= 1791) {
            $arabic_count++;
        } else if ($pos > 123 && $pos < 123) {
            $latin_count++;
        }
        $total_count++;
    }
    if (($arabic_count / $total_count) > 0.6) {
        // 60% arabic chars, its probably arabic
        return true;
    }
    return false;
}

function sendEmailBio($data, $to_name, $to_email, $email_sms_title)
{
    $systemSetting = DB::table('general_settings')->select('organization_name', 'email')->find(1);
    $systemEmail = DB::table('email_settings')->find(1);
    $system_email = $systemEmail->from_email;
    $organization_name = $systemSetting->organization_name;
    if (!empty($system_email)) {
        $data['email_sms_title'] = $email_sms_title;
        $data['system_email'] = $system_email;
        $data['organization_name'] = $organization_name;
        $details = $to_email;
        dispatch(new \App\Jobs\SendEmailJob($data, $details));
        $error_data = [];
        return true;
    } else {
        $error_data[0] = 'success';
        $error_data[1] = 'Operation Failed, Please Updated System Mail';
        return $error_data;
    }

}

function sendSMSApi($to_mobile, $sms, $id)
{
    $activeSmsGateway = sGateway::find($id);
    if ($activeSmsGateway->gateway_name == 'Twilio') {
        $client = new Twilio\Rest\Client($activeSmsGateway->twilio_account_sid, $activeSmsGateway->twilio_authentication_token);
        if (!empty($to_mobile)) {
            $result = $message = $client->messages->create($to_mobile, array('from' => $activeSmsGateway->twilio_registered_no, 'body' => $sms));
            return $result;
        }
    } //end Twilio
    else if ($activeSmsGateway->gateway_name == 'Clickatell') {

        // config(['clickatell.api_key' => $activeSmsGateway->clickatell_api_id]); //set a variale in config file(clickatell.php)

        $clickatell = new \Clickatell\Rest();
        $result = $clickatell->sendMessage(['to' => $to_mobile, 'content' => $sms]);
    } //end Clickatell

    else if ($activeSmsGateway->gateway_name == 'Msg91') {
        $msg91_authentication_key_sid = $activeSmsGateway->msg91_authentication_key_sid;
        $msg91_sender_id = $activeSmsGateway->msg91_sender_id;
        $msg91_route = $activeSmsGateway->msg91_route;
        $msg91_country_code = $activeSmsGateway->msg91_country_code;

        $curl = curl_init();

        $url = "https://api.msg91.com/api/sendhttp.php?mobiles=" . $to_mobile . "&authkey=" . $msg91_authentication_key_sid . "&route=" . $msg91_route . "&sender=" . $msg91_sender_id . "&message=" . $sms . "&country=91";

        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true, CURLOPT_ENCODING => "", CURLOPT_MAXREDIRS => 10, CURLOPT_TIMEOUT => 30, CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, CURLOPT_CUSTOMREQUEST => "GET", CURLOPT_SSL_VERIFYHOST => 0, CURLOPT_SSL_VERIFYPEER => 0,
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        if ($err) {
            $result = "cURL Error #:" . $err;
        } else {
            $result = $response;
        }
    } //end Msg91

    return $result;

}

function sendSMSBio($to_mobile, $sms)
{
    $activeSmsGateway = sGateway::where('active_status', '=', 1)->first();
    if ($activeSmsGateway->gateway_name == 'Twilio') {

        config(['TWILIO.SID' => $activeSmsGateway->twilio_account_sid]);
        config(['TWILIO.TOKEN' => $activeSmsGateway->twilio_authentication_token]);
        config(['TWILIO.FROM' => $activeSmsGateway->twilio_registered_no]);
        $account_id = $activeSmsGateway->twilio_account_sid; // Your Account SID from www.twilio.com/console
        $auth_token = $activeSmsGateway->twilio_authentication_token; // Your Auth Token from www.twilio.com/console
        $from_phone_number = $activeSmsGateway->twilio_registered_no;
        $client = new Twilio\Rest\Client($account_id, $auth_token);
        if (!empty($to_mobile)) {
            $result = $message = $client->messages->create($to_mobile, array('from' => $from_phone_number, 'body' => $sms));
            return $result;
        }
    } //end Twilio
    else if ($activeSmsGateway->gateway_name == 'Clickatell') {


        // config(['clickatell.api_key' => $activeSmsGateway->clickatell_api_id]); //set a variale in config file(clickatell.php)

        $clickatell = new \Clickatell\Rest();
        $result = $clickatell->sendMessage(['to' => $to_mobile, 'content' => $sms]);
    } //end Clickatell

    else if ($activeSmsGateway->gateway_name == 'Msg91') {
        $msg91_authentication_key_sid = $activeSmsGateway->msg91_authentication_key_sid;
        $msg91_sender_id = $activeSmsGateway->msg91_sender_id;
        $msg91_route = $activeSmsGateway->msg91_route;
        $msg91_country_code = $activeSmsGateway->msg91_country_code;

        $curl = curl_init();

        $url = "https://api.msg91.com/api/sendhttp.php?mobiles=" . $to_mobile . "&authkey=" . $msg91_authentication_key_sid . "&route=" . $msg91_route . "&sender=" . $msg91_sender_id . "&message=" . $sms . "&country=91";

        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true, CURLOPT_ENCODING => "", CURLOPT_MAXREDIRS => 10, CURLOPT_TIMEOUT => 30, CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, CURLOPT_CUSTOMREQUEST => "GET", CURLOPT_SSL_VERIFYHOST => 0, CURLOPT_SSL_VERIFYPEER => 0,
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        if ($err) {
            $result = "cURL Error #:" . $err;
        } else {
            $result = $response;
        }
    } //end Msg91

    return $result;

}

function getValueByString($student_id, $string, $extra = null)
{
    $student = Student::find($student_id);
    if ($extra != null) {
        return $student->$string->$extra;

    } else {
        return $student->$string;
    }
}

function getParentName($student_id, $string, $extra = null)
{
    $student = Student::find($student_id);
    $parent = Guardian::where('id', $student->guardian_id)->first();
    if ($extra != null) {
        return $student->$parent->$extra;

    } else {
        return $parent->fathers_name;;
    }
}

function SMSBody($body, $s_id, $time)
{
    try {
        $original_message = $body;
        // $original_message= "Dear Parent [fathers_name], your child [class] came to the school at [section]";
        $chars = preg_split('/[\s,]+/', $original_message, -1, PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE);
        foreach ($chars as $item) {
            if (strstr($item[0], "[")) {
                $str = str_replace('[', '', $item);
                $str = str_replace(']', '', $str);
                $str = str_replace('.', '', $str);
                if ($str == "class") {
                    $str = "class";
                    $extra = "class_name";
                    $custom_array[$item] = getValueByString($s_id, $str, $extra);
                } elseif ($str == "section") {
                    $str = "section";
                    $extra = "section_name";
                    $custom_array[$item] = getValueByString($s_id, $str, $extra);
                } elseif ($str == 'check_in_time') {
                    $custom_array[$item] = $time;
                } elseif ($str == 'fathers_name') {
                    $str = "parents";
                    $extra = "fathers_name";
                    $custom_array[$item] = getValueByString($s_id, $str, $extra);
                    // $custom_array[$item]= 'father';
                } else {
                    $custom_array[$item] = getValueByString($s_id, $str);
                }
            }
        }

        foreach ($custom_array as $key => $value) {
            $original_message = str_replace($key, $value, $original_message);
        }


        return $original_message;


    } catch (\Exception $e) {
        $data = [];
        return $data;
    }

}

function FeesDueSMSBody($body, $s_id, $time)
{
    try {
        $original_message = $body;
        // $original_message= "Dear Parent [fathers_name], your child [class] came to the school at [section]";


        //  $original_message= "Fee Due Reminder for your child |StudentName|.  Dear Parent |ParentName|, please find the below fee summary.Fee: Rs.|Fee|, Back dues
        // Adjustment: Rs.|Adjustment|,
        // Total: Rs.|Total|,
        // Paid: Rs.|Paid|,
        // Balance: Rs.|Balance|.
        // Please ignore in case already paid.";

        $chars = preg_split('/[\s,]+/', $original_message, -1, PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE);
        foreach ($chars as $item) {
            if (strstr($item[0], "|")) {
                $str = str_replace('|', '', $item);
                // return $str;
                $str = str_replace('|', '', $str);
                $str = str_replace('.', '', $str);
                if ($str == "StudentName") {
                    $str = "StudentName";
                    $extra = "full_name";
                    $custom_array[$item] = getValueByString($s_id, $str, $extra);

                } elseif ($str == 'fathers_name') {
                    $str = "parents";
                    $extra = "fathers_name";
                    $custom_array[$item] = getValueByString($s_id, $str, $extra);
                    // $custom_array[$item]= 'father';
                } else {
                    $custom_array[$item] = getValueByString($s_id, $str);
                }
            }
        }

        foreach ($custom_array as $key => $value) {
            $original_message = str_replace($key, $value, $original_message);
        }

        return $original_message;


    } catch (\Exception $e) {
        $data = [];
        return $data;
    }

}

function DateConvat($input_date)
{
    $generalSetting = GeneralSettings::find(1);
    $system_date_foramt = DateFormat::find($generalSetting->date_format_id);
    $DATE_FORMAT = $system_date_foramt->format;
    echo date_format(date_create($input_date), $DATE_FORMAT);
}


function get_guard()
{
    if (Auth::guard('superior')->check()) {
        return "superior";
    } elseif (Auth::guard('student')->check()) {
        return "student";
    } elseif (Auth::guard('employee')->check()) {
        return "employee";
    } elseif (Auth::guard('guardian')->check()) {
        return "guardian";
    } elseif (Auth::guard('organization')->check()) {
        return "organization";
    } elseif (Auth::guard('web')->check()) {
        return "web";
    } elseif (Auth::guard('api')->check()) {
        return "api";
    }
}

function getDepartmentEmail($department)
{


    switch ($department) {
        case 'Finance':
        case 'General':
            $email = '<EMAIL>';
            break;
        case 'Education':
            $email = '<EMAIL>';
            break;
        case 'Management':

            $email = '<EMAIL>';
            break;
        case 'Technical Support':
            $email = '<EMAIL>';
            break;

        default:
            $email = '<EMAIL>';
    }

    return $email;
}
if (!function_exists('canApprove')) {
    function canApprove($staff_id = null)
    {







        if (Auth::user()->hasRole('Managing Director')  || Auth::user()->hasRole('IT Developer') )
            return true;
        else {
            if ($staff_id) {
                $staff = Employee::find($staff_id);
                if ($staff) {
                    $department = \App\Department::find($staff->department_id);
                    if ($department->user_id && in_array(auth()->id(), $department->user_id))
                        return true;
                }
            }
            return false;
        }
    }
}
if (!function_exists('totalLeaveByLeaveType')) {

    function totalLeaveByLeaveType($leaveTypeId)
    {


        $now = date('Y');


        return \DB::select('call getemployeetotleavesByLeaveType(?,?,?)',[auth()->user()->id,$now,$leaveTypeId])[0]->totLeaveDays;


    }
}

if (!function_exists('approvedleavesByType')) {

    function approvedleavesByType($userId, $leaveTypeId)
    {
        $ApprovedleavesByType =   \DB::select("select case when sum(al.total_days) is null then 0 else sum(al.total_days) end as totalDays from apply_leaves al where al.user_id = ? and al.leave_type_id = ? and al.status = 1; ",[$userId,$leaveTypeId]);
        $ApprovedleavesByType = (int)$ApprovedleavesByType[0]->totalDays;
        return $ApprovedleavesByType;


    }
}

if (!function_exists('permissionCheck')) {
    function permissionCheck($route_name)
    {


//        if (auth()->check()) {


//            if (auth()->user()->role->type == "system_user") {
            return TRUE;
//            }


//            else {
//                $roles = app('permission_list');
//                $role = $roles->where('id', auth()->user()->role_id)->first();
//                if ($role != null && $role->permissions->contains('route', $route_name)) {
//                    return TRUE;
//                } else {
//                    return FALSE;
//                }
//            }
//        }
//        return FALSE;
//        }
    }
}


if (!function_exists('userPermission')) {
    function userPermission($assignId, $role_id = null, $purpose = null): bool
    {
//        $role_id = Auth::user()->role_id;
        $role_id = Auth::user()->hasRole(18/** 18 refers to the admin - this should be refactored later on */);


        $permissions = app('permission');
        if ($role_id/** if user is super admin */) {
            return True;
        }
//        if ((!empty($permissions)) && ($role_id != 1)) {
        if ((!empty($permissions)) && ($role_id != 18)) {
            if (@in_array($assignId, $permissions)) {
                return True;
            } else {
                return False;
            }
        } else {
            return True;
        }
    }
}
if (!function_exists('checkAdmin')) {
    function checkAdmin()
    {
        if (Auth::check()) {
//            if (Auth::user()->hasRole('administrative_2_')) {
            if (Auth::user()->hasRole('managing-director_2_')) {
                return true;
            } elseif (Auth::user()->is_saas == 1) {
                return true;
            } else {
                return false;
            }
        }

    }
}
if (!function_exists('dateConvert')) {

    function dateConvert($input_date)
    {
        try {
            $system_date_format = session()->get('system_date_format');

            if (empty($system_date_format)) {
                $date_format_id = GeneralSettings::where('id', 1)->first(['date_format_id'])->date_format_id;
                $system_date_format = DateFormat::where('id', $date_format_id)->first(['format'])->format;
                session()->put('system_date_format', $system_date_format);
                return date_format(date_create($input_date), $system_date_format);
            } else {

                return date_format(date_create($input_date), $system_date_format);

            }
        } catch (\Throwable $th) {

            return $input_date;
        }


    }
}


if (!function_exists('generalSetting')) {
    function generalSetting()
    {
        session()->forget('generalSetting');
        if (session()->has('generalSetting')) {
            return session()->get('generalSetting');
        } else {
            $generalSetting = GeneralSettings::first();
            session()->put('generalSetting', $generalSetting);

            return session()->get('generalSetting');
        }
    }
}

if (!function_exists('teacherAccess')) {
    function teacherAccess()
    {
        try {
            $user = Auth::user();
            if ($user->role_id == 4) {
                return true;
            } else {
                return false;
            }
        } catch (\Exception $e) {
            return false;
        }
    }


}

// this line of code is very strategic decision made by Itqan top management. since an employee has more than
// one role, therefore, we consider the one with the highest number of leave days
if (!function_exists('getMaxLeaveDaysRoleId')) {
    function getMaxLeaveDaysRoleId($userId)
    {
        try {
            $maxLeaveDayCountRecord = LeaveDefine::where('user_id', $userId)->orderBy('days', 'desc')->take(1)->first()->role_id;

            return $maxLeaveDayCountRecord;

        } catch (\Exception $e) {
            return false;
        }
    }
}

if (!function_exists('moduleStatusCheck')) {
    function moduleStatusCheck($module)
    {

        try {
            // get all module from session;
            $all_module = session()->get('all_module');
            //check module status
            $modulestatus = Module::find($module)->isDisabled();

            //if session exist and non empty
            if (!empty($all_module)) {
                if ((in_array($module, $all_module)) && $modulestatus == false) {

                    return True;
                } else {
                    return False;
                }

            } //if session failed or empty data then hit database
            else {
                // is available Modules / FeesCollection1 / Providers / FeesCollectionServiceProvider . php
                $is_module_available = 'Modules/' . $module . '/Providers/' . $module . 'ServiceProvider.php';

                if (file_exists($is_module_available)) {
                    $modulestatus = Module::find($module)->isDisabled();

                    if ($modulestatus == FALSE) {
                        $is_verify = \App\ModuleManager::where('name', $module)->first();

                        if (!empty($is_verify->purchase_code)) {
                            return TRUE;

                        }
                    }
                }
            }
        } catch (\Throwable $th) {
            return FALSE;
        }

    }
}


if (!function_exists('getAyatListBySurat')) {
    /**
     * Get list of ayats based on surah
     *
     */
//    function getAyatListBySurat($surah_id, $ayat_num): string
//    {
//
//        $surah = MoshafSurah::find($surah_id);
//        $options = '<option value="" selected>Select</option>';
//
//        if (isset($surah)) {
//            $num_ayat = $surah->num_ayat;
//            for ($i = 1; $i <= $num_ayat; $i++) {
//                if ($i === $ayat_num) {
//                    $options .= '<option selected value="' . $i . '">' . $i . '</option>';
//                } else {
//                    $options .= '<option value="' . $i . '">' . $i . '</option>';
//                }
//            }
//        }
//
//        return $options;
//    }


    function getIjazasanadAyatListBySurat($surah_id, $hefzPlanId, $fromDate, $selectedAyat): string
    {



        // Build a unique cache key using surah, hefzPlan, fromDate, and the selected ayat.
//        $cacheKey = "ayat_options_{$surah_id}_{$hefzPlanId}_" . md5($fromDate) . "_{$selectedAyat}";

//        return Cache::remember($cacheKey, now()->addHours(12), function () use ($surah_id, $hefzPlanId, $fromDate, $selectedAyat) {
            $surah = \App\MoshafSurah::find($surah_id);
            $hefzPlan = \App\IjazasanadMemorizationPlan::find($hefzPlanId);
            // Retrieve the report for the given date
            $reports = \App\StudentIjazasanadMemorizationReport::where('hefz_plan_id', $hefzPlanId)
                ->whereDate('created_at', $fromDate)
                ->first();

            $options = '<option value="" selected>Select</option>';

            if (!$surah) {
                return $options;
            }

            $num_ayat = $surah->num_ayat;

            // If the surah is the target surah, limit the options accordingly.
            if ($surah->id == $hefzPlan->to_surat) {
                // Loop from the report's hefz_from_ayat to the plan's to_ayat
                $start = $reports->hefz_from_ayat;
                $end   = $hefzPlan->to_ayat;
                for ($i = $start; $i <= $end; $i++) {
                    if ($i == $selectedAyat) {
                        $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                    } else {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';
                    }
                }
            } else if ($surah->id == $hefzPlan->start_from_surat) {
                // If the surah is the starting surah, only include ayats greater than or equal to the plan's start_from_ayat.
                for ($i = 1; $i <= $num_ayat; $i++) {
                    if ($i >= $hefzPlan->start_from_ayat) {
                        if ($i == $selectedAyat) {
                            $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                        } else {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';
                        }
                    }
                }
            } else {
                // Otherwise, simply loop through all ayats in the surah.
                for ($i = 1; $i <= $num_ayat; $i++) {
                    if ($i == $selectedAyat) {
                        $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                    } else {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';
                    }
                }
            }

            return $options;
//        });
    }



    function getAyatListBySurat($surah_id, $ayat_num): string
    {
        // Define a unique cache key based on surah_id and ayat_num
        $cacheKey = "ayat_options_{$surah_id}_{$ayat_num}";

        // Try to retrieve from cache
        return Cache::remember($cacheKey, now()->addHours(12), function () use ($surah_id, $ayat_num) {
            $surah = MoshafSurah::find($surah_id);
            $options = '<option value="" selected>Select</option>';

            if (isset($surah)) {
                $num_ayat = $surah->num_ayat;
                for ($i = 1; $i <= $num_ayat; $i++) {
                    if ($i === $ayat_num) {
                        $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                    } else {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';
                    }
                }
            }

            return $options;
        });
    }

}
if (!function_exists('getFromAyatListBySurat')) {
    /**
     * Get list of ayats based on surah
     *
     */
    function getFromAyatListBySurat($toSurahId, $hefzPlanId, $hefzReportId): string
    {

        $toSurah = \App\MoshafSurah::find($toSurahId);
        $hefzPlan = \App\StudentHefzPlan::find($hefzPlanId);
        $hefzReport = \App\StudentHefzReport::find($hefzReportId);
        $options = '<option value="" selected>Select</option>';


        $toSurahNum_ayat = $toSurah->num_ayat;


        if ($hefzPlan->study_direction == 'forward') {


            if ($hefzReport->hefz_from_surat == $toSurah->id) {
                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//            if ($toSurahId == $hefzReport->hefz_from_surat) { // if from_surat and to_surats are the same ( ex: al-fatiha to al-fatiha)
//                if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) {
                    if ($i > $hefzReport->hefz_from_ayat) {
                        if ($i == $hefzReport->hefz_to_ayat) {
                            $options .= '<option selected value="' . $i . '">' . $i . '</option>';


                        } else {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';

                        }

                    }

                }
            } // if the from and to surats are not similar
            else {

                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//            if ($toSurahId == $hefzReport->hefz_from_surat) { // if from_surat and to_surats are the same ( ex: al-fatiha to al-fatiha)
//                if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';


                }
            }
        }

        if ($hefzPlan->study_direction == 'backward') {


            if ($hefzReport->hefz_from_surat == $toSurah->id) {
                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//            if ($toSurahId == $hefzReport->hefz_from_surat) { // if from_surat and to_surats are the same ( ex: al-fatiha to al-fatiha)
//                if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) {
                    if ($i < $hefzReport->hefz_from_ayat) {
                        if ($i == $hefzReport->hefz_to_ayat) {
                            $options .= '<option selected value="' . $i . '">' . $i . '</option>';

                        }
                        $options .= '<option value="' . $i . '">' . $i . '</option>';

                    }

                }
            } // if the from and to surats are not similar
            else {

                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//            if ($toSurahId == $hefzReport->hefz_from_surat) { // if from_surat and to_surats are the same ( ex: al-fatiha to al-fatiha)
//                if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';


                }
            }


        }

        return $options;


        $surah = MoshafSurah::find($surah_id);
        $options = '<option value="" selected>Select</option>';

        if (isset($surah)) {
            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {
                if ($i === $ayat_num) {
                    $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                } else {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';
                }
            }
        }

        return $options;
    }

}
if (!function_exists('hefzPlanGetFromAyatListBySurat')) {
    /**
     * Get list of ayats based on surah
     *
     */
    function hefzPlanGetFromAyatListBySurat($toSurahId, $hefzPlanId): string
    {

        $toSurah = \App\MoshafSurah::find($toSurahId);
        $hefzPlan = \App\StudentHefzPlan::find($hefzPlanId);
        $options = '<option value="" selected>Select</option>';


        if ($hefzPlan->study_direction == 'forward') {


            if ($hefzPlan->start_from_surat == $toSurah->id) {
                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i > $hefzPlan->start_from_ayat) {
                        if ($i == $hefzPlan->to_ayat) {
                            $options .= '<option selected value="' . $i . '">' . $i . '</option>';

                        } else {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';
                        }

                    }

                }
            } // if the from and to surats are not similar
            else {

                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';


                }
            }
        }

        if ($hefzPlan->study_direction == 'backward') {


            if ($hefzPlan->start_from_surat == $toSurah->id) {
                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {

                    if ($i < $hefzPlan->start_from_ayat) {
                        if ($i == $hefzPlan->to_ayat) {
                            $options .= '<option selected value="' . $i . '">' . $i . '</option>';

                        }
                        $options .= '<option value="' . $i . '">' . $i . '</option>';

                    }

                }
            } // if the from and to surats are not similar
            else {

                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {

                    $options .= '<option value="' . $i . '">' . $i . '</option>';


                }
            }


        }

        return $options;


        $surah = MoshafSurah::find($surah_id);
        $options = '<option value="" selected>Select</option>';

        if (isset($surah)) {
            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {
                if ($i === $ayat_num) {
                    $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                } else {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';
                }
            }
        }

        return $options;
    }

}

if (!function_exists('hefzPlanGetToAyatListBySurat')) {

    function hefzPlanGetToAyatListBySurat($toSurahId, $hefzPlanId, $ayat_num): string
    {


//
//        $toSurah = \App\MoshafSurah::find($toSurahId);
//        $hefzPlan = \App\StudentHefzPlan::find($hefzPlanId);
//        $options = '<option value="" selected>Select</option>';
//
//
//        for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//            if ($i > $hefzPlan->start_from_ayat) {
//                if ($i == $hefzPlan->to_ayat) {
//                    $options .= '<option selected value="' . $i . '">' . $i . '</option>';
//                } else {
//                    $options .= '<option value="' . $i . '">' . $i . '</option>';
//
//                }
//
//            }
//        }
//
//
//        return $options;

        $surah = MoshafSurah::find($toSurahId);
        $options = '<option value="" selected>Select</option>';

        if (isset($surah)) {
            $hefzPlan = \App\StudentHefzPlan::find($hefzPlanId);
            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {


                if ($hefzPlan->start_from_surat === $hefzPlan->to_surat) {
                    if ($i > $hefzPlan->start_from_ayat) {

                        if ($i === $ayat_num) {
                            $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                        } else {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';
                        }


//                        $options .= '<option value="' . $i . '">' . $i . '</option>';
                    }

                } else {

                    if ($i === $ayat_num) {
                        $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                    } else {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';
                    }

//                    $options .= '<option value="' . $i . '">' . $i . '</option>';

                }


            }


//            asdsadasd

//            for ($i = 1; $i <= $num_ayat; $i++) {
//                if ($i === $ayat_num) {
//                    $options .= '<option selected value="' . $i . '">' . $i . '</option>';
//                } else {
//                    $options .= '<option value="' . $i . '">' . $i . '</option>';
//                }
//            }
        }

        return $options;
    }
}
if (!function_exists('getToAyatListBySurat')) {
    /**
     * Get list of ayats based on surah
     *
     */
    function getToAyatListBySurat($toSurahId, $hefzPlanId, $hefzReportId): string
    {


        $toSurah = \App\MoshafSurah::find($toSurahId);
        $hefzPlan = \App\StudentHefzPlan::find($hefzPlanId);
        $hefzReport = \App\StudentHefzReport::find($hefzReportId);
        $options = '<option value="" selected>Select</option>';


        $toSurahNum_ayat = $toSurah->num_ayat;


//        if ($hefzPlan->study_direction == 'forward') {


//            if ($hefzReport->hefz_from_surat == $toSurah->id) {
        for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
            if ($i > $hefzReport->hefz_from_ayat) {
                if ($i == $hefzReport->hefz_to_ayat) {
                    $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                } else {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';

                }

            }
        }
//            } // if the from and to surats are not similar
//            else {
//                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//                    $options .= '<option value="' . $i . '">' . $i . '</option>';
//                }
//            }
//        }

//        if ($hefzPlan->study_direction == 'backward') {
//
//
//            if ($hefzReport->hefz_from_surat == $toSurah->id) {
//                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//
//                    if ($i < $hefzReport->hefz_from_ayat) {
//                        if($i == $hefzReport->hefz_to_ayat){
//                            $options .= '<option selected value="' . $i . '">' . $i . '</option>';
//
//                        }
//                        $options .= '<option value="' . $i . '">' . $i . '</option>';
//
//                    }
//
//                }
//            } // if the from and to surats are not similar
//            else {
//                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//                    $options .= '<option value="' . $i . '">' . $i . '</option>';
//                }
//            }
//
//
//        }

        return $options;


        $surah = MoshafSurah::find($surah_id);
        $options = '<option value="" selected>Select</option>';

        if (isset($surah)) {
            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {
                if ($i === $ayat_num) {
                    $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                } else {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';
                }
            }
        }

        return $options;
    }


    function cal_percentage($num_amount, $num_total)
    {
        $count1 = $num_amount / $num_total;
        $count2 = $count1 * 100;
        $count = number_format($count2, 0);
        return $count;
    }

    function is_english($str)
    {
        if (strlen($str) != strlen(utf8_decode($str))) {
            return false;
        } else {
            return true;
        }
    }


}
if (!function_exists('getHefzReportToAyatListBySurat')) {
    /**
     * Get list of ayats based on surah
     * By adding the ? before string, you indicate that the function may return either a string or a null value.
     */
    function getHefzReportToAyatListBySurat($toSurahId, $hefzPlanId, $hefzReportId): ?string
    {

        $toSurah = \App\MoshafSurah::find($toSurahId);
        $hefzPlan = \App\StudentHefzPlan::find($hefzPlanId);
        $hefzReport = \App\StudentHefzReport::find($hefzReportId);


        if ($hefzPlan->study_direction == 'forward') {
            if ($hefzReport->hefz_from_surat == $toSurah->id) {
//                return '<option selected value="' . $toSurah->num_ayat . '">' . $toSurah->num_ayat . '</option>';

                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i == $hefzReport->hefz_to_ayat) {
                        $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                    } else {
//                        if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) { // this was the last commented one.
                        if ($i > $hefzReport->hefz_from_ayat && $i <= $toSurah->num_ayat) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';
                        }
                    }
                }
            } else {
                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i == $hefzReport->hefz_to_ayat) {
                        $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                    } else {
                        if ($i > $hefzReport->hefz_from_ayat) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';
                        }
                    }
                }

            }

        } // if the from and to surats are not similar



        else {


            // if to surat == revisionPlan->toSurat, then apply this rule
            if ($hefzReport->hefz_from_surat == $hefzPlan->to_surat) {

                for ($i = 1; $i <= $hefzPlan->to_ayat; $i++) {


                    $options .= '<option value="' . $i . '">' . $i . '</option>';


                }
            } else {
                if ($toSurahId == $hefzPlan->to_surat) {

                    for ($i = 1; $i <= $hefzPlan->to_ayat; $i++) {


                        $options .= '<option value="' . $i . '">' . $i . '</option>';


                    }

                } else {

                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {


                        $options .= '<option value="' . $i . '">' . $i . '</option>';


                    }

                }


            }
        }





        if ($hefzPlan->study_direction == 'backward') {
            if ($hefzReport->hefz_from_surat == $toSurah->id) {
                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i == $hefzReport->hefz_to_ayat) {
                        $options .= '<option selected value="' . $i . '">' . $i . '</option>';


                    }
                    else{
                        if ($i > $hefzReport->hefz_from_ayat) {

                            $options .= '<option value="' . $i . '">' . $i . '</option>';
                        }
                    }

                }
            } // if the from and to surats are not similar
            else {


                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i == $hefzReport->hefz_to_ayat) {
                        $options .= '<option selected value="' . $i . '">' . $i . '</option>';


                    }
                    else{

//                        if ($i > $hefzRevisionReport->revision_from_ayat && $i <= $revisionPlan->to_ayat) {

                        $options .= '<option value="' . $i . '">' . $i . '</option>';
//                        }
                    }


                }
            }
        }
        return $options;


    }


}

if (!function_exists('getIjazasanadMemorizationReportToAyatListBySurat')) {
    /**
     * Get list of ayats based on surah
     * By adding the ? before string, you indicate that the function may return either a string or a null value.
     */
    function getIjazasanadMemorizationReportToAyatListBySurat($toSurahId, $hefzPlanId, $hefzReportId): ?string
    {
        // Start with a default "Select" option.
        $options = '<option value="" selected>Select</option>';

        // Retrieve the necessary records.
        $toSurah   = \App\MoshafSurah::find($toSurahId);
        $hefzPlan  = \App\IjazasanadMemorizationPlan::find($hefzPlanId);
        $hefzReport = \App\StudentIjazasanadMemorizationReport::find($hefzReportId);

        // If any of these are missing, return the default options.
        if (!$toSurah || !$hefzPlan || !$hefzReport) {
            return $options;
        }

        // Study Direction: Forward
        if ($hefzPlan->study_direction == 'forward') {
            // If the report's from_surat equals the current toSurah...
            if ($hefzReport->hefz_from_surat == $toSurah->id) {
                // If this surah is also the plan's designated "to_surat"
                if ($toSurah->id == $hefzPlan->to_surat) {
                    $end = $hefzPlan->to_ayat;
                    for ($i = $hefzReport->hefz_from_ayat + 1; $i <= $end; $i++) {
                        $options .= '<option' . ($i == $hefzReport->hefz_to_ayat ? ' selected' : '') . ' value="' . $i . '">' . $i . '</option>';
                    }
                } else {
                    // Otherwise, loop up to the total number of ayat in the surah.
                    $end = $toSurah->num_ayat;
                    for ($i = $hefzReport->hefz_from_ayat + 1; $i <= $end; $i++) {
                        $options .= '<option' . ($i == $hefzReport->hefz_to_ayat ? ' selected' : '') . ' value="' . $i . '">' . $i . '</option>';
                    }
                }
            } else {
                // When the report's from_surat differs from the current surah:
                if ($hefzReport->hefz_from_surat == $hefzPlan->to_surat) {
                    $end = $hefzPlan->to_ayat;
                    for ($i = 1; $i <= $end; $i++) {
                        $options .= '<option' . ($i == $hefzReport->hefz_to_ayat ? ' selected' : '') . ' value="' . $i . '">' . $i . '</option>';
                    }
                } else {
                    if ($toSurahId == $hefzPlan->to_surat) {
                        $end = $hefzPlan->to_ayat;
                        for ($i = 1; $i <= $end; $i++) {
                            $options .= '<option' . ($i == $hefzReport->hefz_to_ayat ? ' selected' : '') . ' value="' . $i . '">' . $i . '</option>';
                        }
                    } else {
                        $end = $toSurah->num_ayat;
                        for ($i = 1; $i <= $end; $i++) {
                            $options .= '<option' . ($i == $hefzReport->hefz_to_ayat ? ' selected' : '') . ' value="' . $i . '">' . $i . '</option>';
                        }
                    }
                }
            }
        }
        // Study Direction: Backward
        elseif ($hefzPlan->study_direction == 'backward') {
            if ($hefzReport->hefz_from_surat == $toSurah->id) {
                $end = $toSurah->num_ayat;
                for ($i = $hefzReport->hefz_from_ayat + 1; $i <= $end; $i++) {
                    $options .= '<option' . ($i == $hefzReport->hefz_to_ayat ? ' selected' : '') . ' value="' . $i . '">' . $i . '</option>';
                }
            } else {
                $end = $toSurah->num_ayat;
                for ($i = 1; $i <= $end; $i++) {
                    $options .= '<option' . ($i == $hefzReport->hefz_to_ayat ? ' selected' : '') . ' value="' . $i . '">' . $i . '</option>';
                }
            }
        }

        return $options;
    }


}

if (!function_exists('getLessonLineRange')) {

    function getLessonLineRange($student, $fromDate)
    {

        // Retrieve the student's Nouranya report and plan.
        $nouranya     = $student->nouranya->first();
        $nouranyaPlan = $student->nouranya_plans->first();


        $planFromLesson = $nouranyaPlan->from_lesson;  // e.g., 5
        $planToLesson   = $nouranyaPlan->to_lesson;      // e.g., 10

        // Validate and get the current lesson number.
        $currentLessonNo = $nouranya->from_lesson;



        // Get the program level id.
        $programLevelId = optional($student->studentProgramLevels->first())->level_id;
        if (is_null($programLevelId)) {
            throw new \Exception("Program level id is missing.");
        }

        // Fetch the corresponding lesson record.
        $lesson = \App\ProgramLevelLesson::where('program_level_id', $programLevelId)
            ->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(properties, '$.no')) = ?", [(int)$currentLessonNo])
            ->first();



        // Normalize the lesson's 'lines' property to an array of integers.
        $linesValue = $lesson->properties['lines'];
        if (is_array($linesValue)) {
            $lineNumbers = array_map('intval', $linesValue);
        } else {
            $lineNumbers = array_map('intval', explode(',', $linesValue));
        }
        $lineNumbers = array_unique($lineNumbers);
        sort($lineNumbers);
        $lessonMaxLine = count($lineNumbers) ? max($lineNumbers) : 0;

        // Line Boundaries Within Each Lesson:
        // Cap the lesson's maximum line using the plan's to_lesson_line_number if defined.
        $planMaxLine = isset($nouranyaPlan->to_lesson_line_number)
            ? min($lessonMaxLine, (int)$nouranyaPlan->to_lesson_line_number)
            : $lessonMaxLine;

        $planFromLine = (int)$nouranyaPlan->from_lesson_line_number;  // e.g., 5
        $planToLine = isset($nouranyaPlan->to_lesson_line_number)
            ? min($lessonMaxLine, (int)$nouranyaPlan->to_lesson_line_number)  // e.g., 11
            : $lessonMaxLine;

        // Format the provided date and fetch the student's daily report for that day.
        $formattedDate = \Carbon\Carbon::createFromFormat('Y-m-d', $fromDate)->format('Y-m-d');
        $dailyReport   = \App\StudentNouranyaReport::where('student_id', $student->id)
            ->whereDate('created_at', $formattedDate)
            ->first();


        $dailyFromLesson = $dailyReport->from_lesson;
        $dailyFromLine   = $dailyReport->from_lesson_line_number;
        $dailyToLesson   = isset($dailyReport->to_lesson) ? $dailyReport->to_lesson : $dailyFromLesson;
        $dailyToLine     = isset($dailyReport->to_lesson_line_number) ? $dailyReport->to_lesson_line_number : $planToLine;


    if(($dailyFromLesson < $planToLesson) && ($dailyFromLesson== $planFromLesson) ) {

//        dd(44);

    // For the first lesson in the plan:
    $startLineNumber = (int)$nouranyaPlan->from_lesson_line_number;
    $endLineNumber   = $linesValue;


    } elseif ($dailyFromLesson == $planFromLesson && $dailyFromLine == $planFromLine) {

        $startLineNumber = $planFromLine;
        $endLineNumber   = $planToLine;
    }  else if ($dailyFromLesson == $planFromLesson && $dailyFromLine > $planFromLine) {

        $startLineNumber = $dailyFromLine;
        $endLineNumber   = $planToLine;
    }
    else if (($dailyFromLesson > $planFromLesson) && ($dailyFromLesson < $planToLesson) && ($dailyFromLesson == $dailyToLesson)) {
//        dd(44);
        // If the daily report's from line is lower than plan's from line, default to plan's start; otherwise, use the reported value.
        $startLineNumber = 1;
        $endLineNumber   = (int)$linesValue;


    }

    else if ($dailyFromLesson > $planFromLesson && $dailyFromLesson < $planToLesson) {
//        dd(44);
        // If the daily report's from line is lower than plan's from line, default to plan's start; otherwise, use the reported value.
        $startLineNumber = 1;
        $endLineNumber   = $linesValue;
    }
    elseif(is_null($dailyFromLine) && ($dailyFromLesson == $planToLesson && $dailyFromLesson != $planFromLesson)){




        $startLineNumber = 1;
        $endLineNumber   = $planToLine;

    }
    elseif(!is_null($dailyFromLine) && ($dailyFromLesson == $planToLesson && $dailyFromLesson != $planFromLesson)){



        $startLineNumber = 1;
        $endLineNumber   = $planToLine;

    }
    else if ($dailyFromLesson == $planToLesson && $dailyToLesson == $planToLesson) {



        $startLineNumber = (int)$dailyFromLine;
        $endLineNumber   = $planToLine;

    }
    else {
//        dd($dailyFromLesson,$planToLesson, $dailyFromLesson,$planFromLesson);
//        dd(3333);
//        dd(4444);
            // For subsequent lessons, use the full available range (capped by the plan if applicable).
            $startLineNumber = 1;
            $endLineNumber   = $planMaxLine;
        }

        // Logical Range Check: Ensure that the start line does not exceed the end line.
        if ($startLineNumber > $endLineNumber) {
            $startLineNumber = 1;
        }

        $lineRange = range($startLineNumber, $endLineNumber);





//        dd([
//            'number_of_lines' => $lessonMaxLine,
//            'nouranyaPlanFromLineNumberStartingFrom' => $startLineNumber,
//            'line_range' => $lineRange,
//            'currentFromLessonLineNumber' => $dailyReport->from_lesson_line_number,
//            'dailyToLine' => $dailyReport->to_lesson_line_number,
//        ]);


        return [
            'number_of_lines' => $lessonMaxLine,
            'nouranyaPlanFromLineNumberStartingFrom' => $startLineNumber,
            'line_range' => $lineRange,
            'currentFromLessonLineNumber' => $dailyReport->from_lesson_line_number,
            'dailyToLine' => $dailyReport->to_lesson_line_number,
        ];
    }

}


if (!function_exists('getNouranyaReportToLessonLineNumbers')) {
     function getNouranyaReportToLessonLineNumbers($fromDate, $student)
    {

        // Retrieve the related Nouranya plan and daily report.
        $nouranyaPlan = $student->nouranya_plans->first();

        $nouranyaDailyReport = $student->nouranya->first();
        $lessonNo = (int)$student->nouranya->first()->to_lesson;



        // Get the program level id.
        $programLevelId = $student->nouranya_plans->first()->level_id;

        // Find the lesson based on the toLessonId.
        $lesson = \App\ProgramLevelLesson::where('program_level_id', $programLevelId)
            ->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(properties, '$.no')) = ?", [$lessonNo])
            ->first();

        if ($lesson && isset($lesson->properties['lines'])) {
            // Retrieve the number of lines from the lesson's properties.
            // (Assumes $lines is stored as a comma-separated string or as an array.)
            $lines = $lesson->properties['lines'];

            // Define the plan's line boundaries.
            $planFromLine = (int)$nouranyaPlan->from_lesson_line_number;  // e.g., expected 7
            $planToLine = isset($nouranyaPlan->to_lesson_line_number)
                ? (int)$nouranyaPlan->to_lesson_line_number
                : $lines;
            $planFromLine = isset($nouranyaPlan->from_lesson_line_number)
                ? (int)$nouranyaPlan->from_lesson_line_number
                : $lines;

            // Also, define the plan's lesson boundaries.
            $planFromLesson = (int)$nouranyaPlan->from_lesson;
            $planToLesson = (int)$nouranyaPlan->to_lesson;

            // Cast daily report values to integers.
            $dailyFromLesson = (int)$nouranyaDailyReport->from_lesson;
            $dailyToLesson   = (int)$nouranyaDailyReport->to_lesson;
            // For the "to" side, get the to_lesson_line_number; fallback to plan's to line if not set.
            $dailyToLine = isset($nouranyaDailyReport->to_lesson_line_number)
                ? (int)$nouranyaDailyReport->to_lesson_line_number
                : $planToLine;
            $dailyFromLine = isset($nouranyaDailyReport->from_lesson_line_number)
                ? (int)$nouranyaDailyReport->from_lesson_line_number
                : $planFromLine;

            // Fallback: If dailyToLesson is 0, use the lesson number from the request.
            if ($dailyToLesson === 0) {
                $dailyToLesson = $lessonNo;
            }

            // Debug logging.
            \Log::debug("Plan: from_lesson={$planFromLesson}, to_lesson={$planToLesson}, from_line={$planFromLine}, to_line={$planToLine}");
            \Log::debug("Daily: from_lesson={$dailyFromLesson}, to_lesson={$dailyToLesson}, to_line={$dailyToLine}");

            // Determine the ending line number range using conditions similar to the "from" method.
            // NOTE: In this "to" method, we use dailyToLine instead of dailyFromLine.

            if( ($dailyFromLesson == $planFromLesson) && ( $dailyFromLesson == $dailyToLesson) && ($dailyFromLesson < $planToLesson)){



                $startLineNumber = $dailyFromLine;

                $lineNumberRange = range($startLineNumber, (int)$lines);
                $numberOfLines = collect($lineNumberRange)->count();

            }

            elseif (($dailyFromLesson == $planFromLesson) && ($dailyFromLesson == $dailyToLesson)) {

                $endLineNumber = $dailyFromLine;
                $lineNumberRange = range($endLineNumber, $lines);
                $numberOfLines = collect($lineNumberRange)->count();

            }
            elseif(($dailyToLesson > $dailyFromLesson) && ( $dailyToLesson != $planToLesson)){

                $startLineNumber = $lines;

                $lineNumberRange = range(1, $startLineNumber);
                $numberOfLines = collect($lineNumberRange)->count();

            }
            elseif(($dailyToLesson > $dailyFromLesson) && ( $dailyToLesson == $planToLesson)){

                $startLineNumber = $lines;

                $lineNumberRange = range(1, $planToLine);
                $numberOfLines = collect($lineNumberRange)->count();

            }
            elseif (($dailyToLesson != $planToLesson) && ($dailyFromLesson == $dailyToLesson)) {


                // Case 2: Daily report covers a single lesson that is not the final lesson of the plan.
                $endLineNumber = $dailyFromLine;
                $lineNumberRange = range($endLineNumber, $lines);
                $numberOfLines = collect($lineNumberRange)->count();

            }
            elseif (($dailyToLesson == $planToLesson) && ($dailyFromLesson == $dailyToLesson)) {


                // Case 3: Daily report covers the final lesson of the plan.
                // Use the plan's to_lesson_line_number as the ending boundary.
                $endLineNumber = $planToLine;
                $lineNumberRange = range($dailyFromLine, $endLineNumber);
                $numberOfLines = collect($lineNumberRange)->count();

            }

            else {


                // Fallback: If none of the above conditions match, default to the plan's full to-line range.
                $endLineNumber = $planToLine;
                $lineNumberRange = range(1, $endLineNumber);
                $numberOfLines = collect($lineNumberRange)->count();
            }

            // Logical Range Check: Ensure that the computed end line is not less than the start.
            // (For the "to" method, if ambiguous, we leave it as is since the range is built from the computed value.)
            if ($endLineNumber < 1) {
                $endLineNumber = $planToLine;
            }


//            dd([
//                'number_of_lines' => $numberOfLines,
//                'line_number_range' => $lineNumberRange,
//                'nouranyaPlanToLineNumberEndingAt' => $endLineNumber,
//                'dailyToLine' => $dailyToLine
//            ]);

            return [
                'number_of_lines' => $numberOfLines,
                'line_number_range' => $lineNumberRange,
                'nouranyaPlanToLineNumberEndingAt' => $endLineNumber,
                'dailyToLine' => $dailyToLine
            ];
        }
    }


}
if (!function_exists('getRevisionReportToAyatListBySurat')) {
    /**
     * Get list of ayats based on surah
     *
     */
    function getRevisionReportToAyatListBySurat($toSurahId, $revisionPlanId, $revisionReportId): ?string
    {

        $toSurah = \App\MoshafSurah::find($toSurahId);
        $revisionPlan = \App\StudentRevisionPlan::find($revisionPlanId);
        $hefzRevisionReport = \App\StudentRevisionReport::find($revisionReportId);
        $options = '<option value="" selected>Select</option>';


        if ($revisionPlan->study_direction == 'forward') {


//            if ($revisionPlan->start_from_surat == $toSurah->id) {
            if ($hefzRevisionReport->revision_from_surat == $toSurah->id) {

                    $toSurahNum_ayat = $revisionPlan->to_ayat;

//                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                        if ($i == $hefzRevisionReport->revision_to_ayat) {
                            $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                        } else {
                        if ($i > $hefzRevisionReport->revision_from_ayat && $i <= $revisionPlan->to_ayat) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }
                        }


                    }
                } else {

                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//                if ($toSurahId == $hefzReport->hefz_from_surat) { // if from_surat and to_surats are the same ( ex: al-fatiha to al-fatiha)
//                    if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) {

                        if ($i == $hefzRevisionReport->revision_to_ayat) {
                            $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                        } else {


                        if ($i > $hefzRevisionReport->revision_from_ayat) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }
                        }


                    }

                }

            } // if the from and to surats are not similar


            else {


                // if to surat == revisionPlan->toSurat, then apply this rule
                if ($hefzRevisionReport->revision_from_surat == $revisionPlan->to_surat) {

                    for ($i = 1; $i <= $revisionPlan->to_ayat; $i++) {


                        $options .= '<option value="' . $i . '">' . $i . '</option>';


                    }
                } else {
                    if ($toSurahId == $revisionPlan->to_surat) {

//                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                        for ($i = 1; $i <= $revisionPlan->to_ayat; $i++) {


                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }

                    } else {

                        for ($i = 1; $i <= $toSurah->num_ayat; $i++) {


                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }

                    }


                }
            }





        if ($revisionPlan->study_direction == 'backward') {
            if ($hefzRevisionReport->revision_from_surat == $toSurah->id) {
                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i == $hefzRevisionReport->revision_to_ayat) {
                        $options .= '<option selected value="' . $i . '">' . $i . '</option>';


                    }
                    else{
                    if ($i > $hefzRevisionReport->revision_from_ayat) {

                        $options .= '<option value="' . $i . '">' . $i . '</option>';
                    }
                    }

                }
            } // if the from and to surats are not similar
            else {


                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i == $hefzRevisionReport->revision_to_ayat) {
                        $options .= '<option selected value="' . $i . '">' . $i . '</option>';


                    }
                    else{

//                        if ($i > $hefzRevisionReport->revision_from_ayat && $i <= $revisionPlan->to_ayat) {

                            $options .= '<option value="' . $i . '">' . $i . '</option>';
//                        }
                    }


                }
            }
        }
        return $options;


    }


}
if (!function_exists('getIjazasanadRevisionReportToAyatListBySurat')) {
    /**
     * Get list of ayats based on surah
     *
     */
    function getIjazasanadRevisionReportToAyatListBySurat($toSurahId, $revisionPlanId, $revisionReportId): ?string
    {

        $toSurah = \App\MoshafSurah::find($toSurahId);
        $revisionPlan = \App\IjazasanadRevisionPlan::find($revisionPlanId);
        $hefzRevisionReport = \App\StudentIjazasanadRevisionReport::find($revisionReportId);
        $options = '<option value="" selected>Select</option>';


        if ($revisionPlan->study_direction == 'forward') {


//            if ($revisionPlan->start_from_surat == $toSurah->id) {
            if ($hefzRevisionReport->revision_from_surat == $toSurah->id) {

                    $toSurahNum_ayat = $revisionPlan->to_ayat;

//                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                        if ($i == $hefzRevisionReport->revision_to_ayat) {
                            $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                        } else {
                        if ($i > $hefzRevisionReport->revision_from_ayat && $i <= $revisionPlan->to_ayat) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }
                        }


                    }
                } else {

                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//                if ($toSurahId == $hefzReport->hefz_from_surat) { // if from_surat and to_surats are the same ( ex: al-fatiha to al-fatiha)
//                    if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) {

                        if ($i == $hefzRevisionReport->revision_to_ayat) {
                            $options .= '<option selected value="' . $i . '">' . $i . '</option>';
                        } else {


                        if ($i > $hefzRevisionReport->revision_from_ayat) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }
                        }


                    }

                }

            } // if the from and to surats are not similar


            else {


                // if to surat == revisionPlan->toSurat, then apply this rule
                if ($hefzRevisionReport->revision_from_surat == $revisionPlan->to_surat) {

                    for ($i = 1; $i <= $revisionPlan->to_ayat; $i++) {


                        $options .= '<option value="' . $i . '">' . $i . '</option>';


                    }
                } else {
                    if ($toSurahId == $revisionPlan->to_surat) {

//                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                        for ($i = 1; $i <= $revisionPlan->to_ayat; $i++) {


                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }

                    } else {

                        for ($i = 1; $i <= $toSurah->num_ayat; $i++) {


                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }

                    }


                }
            }





        if ($revisionPlan->study_direction == 'backward') {
            if ($hefzRevisionReport->revision_from_surat == $toSurah->id) {
                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i == $hefzRevisionReport->revision_to_ayat) {
                        $options .= '<option selected value="' . $i . '">' . $i . '</option>';


                    }
                    else{
                    if ($i > $hefzRevisionReport->revision_from_ayat) {

                        $options .= '<option value="' . $i . '">' . $i . '</option>';
                    }
                    }

                }
            } // if the from and to surats are not similar
            else {


                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i == $hefzRevisionReport->revision_to_ayat) {
                        $options .= '<option selected value="' . $i . '">' . $i . '</option>';


                    }
                    else{

//                        if ($i > $hefzRevisionReport->revision_from_ayat && $i <= $revisionPlan->to_ayat) {

                            $options .= '<option value="' . $i . '">' . $i . '</option>';
//                        }
                    }


                }
            }
        }
        return $options;


    }


}





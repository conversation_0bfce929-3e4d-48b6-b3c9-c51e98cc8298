@extends('layouts.hound')

@section('content')
    <div class="container">
        <div class="row">
           

            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">content_category {{ $content_category->id }}</div>
                    <div class="panel-body">

                        <a href="{{ url('/workplace/curriculum/content_categories') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
                        <a href="{{ url('/workplace/curriculum/content_categories/' . $content_category->id . '/edit') }}" title="Edit content_category"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>
                        {!! Form::open([
                            'method'=>'DELETE',
                            'url' => ['workplace/curriculum/content_categories', $content_category->id],
                            'style' => 'display:inline'
                        ]) !!}
                            {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                                    'type' => 'submit',
                                    'class' => 'btn btn-danger btn-xs',
                                    'title' => 'Delete content_category',
                                    'onclick'=>'return confirm("Confirm delete?")'
                            ))!!}
                        {!! Form::close() !!}
                        <br/>
                        <br/>

                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <th>ID</th><td>{{ $content_category->id }}</td>
                                    </tr>
                                    <tr><th> Title </th><td> {{ $content_category->title }} </td></tr><tr><th> Status </th><td> {{ $content_category->status }} </td></tr>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

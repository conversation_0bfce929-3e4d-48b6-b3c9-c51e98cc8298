### **Software Requirements Specification (SRS)**
### **Intelli-Resume: AI-Powered Resume Builder**

| **Document Version:** | 3.0 |
| :--- | :--- |
| **Date:** | 2025-07-10 |
| **Module:** | JobSeeker |
| **Author:** | Gemini AI |

---

### **1. Introduction**

#### **1.1 Purpose**
This document provides a detailed specification for the "Intelli-Resume" feature within the JobSeeker module. Its purpose is to define the functional and non-functional requirements for a system that allows authenticated job seekers to use an AI-powered tool to tailor their resumes for specific job postings they receive via email notifications.

#### **1.2 Scope**
The scope of this project includes the design, development, and integration of a new resume builder SPA (Single Page Application). This encompasses:
*   **In Scope:**
    *   An email template with a clear call-to-action to use the Intelli-Resume feature for a specific job.
    *   A secure, deep-linked route from the email to the resume builder application.
    *   A React + Inertia.js frontend that automatically loads job data and provides a seamless, interactive resume editing experience.
    *   A backend service that orchestrates a multi-step AI process to analyze and rewrite resume content.
    *   Functionality for users to upload a new resume or select from their existing library.
    *   An interactive, component-based editor for reviewing and modifying the AI-generated resume.
    *   **A voice-guided conversational interface** for hands-free resume editing, accessible on standard mobile devices.
    *   Functionality to save, manage, and download the final resume as a PDF.
*   **Out of Scope (for this version):**
    *   Directly submitting the generated resume to an external employer's application portal.
    *   AI generation of a resume from scratch (without a base resume).
    *   Real-time collaboration or sharing of resumes.

#### **1.3 Definitions, Acronyms, and Abbreviations**
*   **SRS:** Software Requirements Specification
*   **AI:** Artificial Intelligence
*   **SPA:** Single Page Application
*   **ATS:** Applicant Tracking System
*   **UI:** User Interface
*   **WYSIWYG:** What You See Is What You Get

---

### **2. Overall Description**

#### **2.1 Product Perspective**
The Intelli-Resume feature is a new, high-value component of the JobSeeker module. It is designed to increase user engagement by providing a proactive tool directly within their job notification workflow. It will be implemented as a Single Page Application using React and Inertia.js, leveraging the existing Laravel backend for API services and data management.

#### **2.2 User Experience (UX) Principles**
The user experience will be guided by the following core principles to ensure the journey is as seamless and efficient as possible:
*   **Seamless Transitions:** The user journey from email to editor shall feel like a single, uninterrupted flow. All transitions between steps (e.g., authentication, data loading, AI processing) will be handled smoothly with single clicks and without jarring page reloads.
*   **Minimalist Interaction:** The UI will minimize form fields and clicks. It will use smart defaults and auto-populated data wherever possible to reduce the user's cognitive load.
*   **Transparent Progress:** The system will use clear progress indicators (e.g., completion percentages, step-by-step checklists) and visual feedback during the AI generation phase to keep the user informed and engaged.
*   **Mobile-First and Touch-Friendly:** The entire interface will be designed with a mobile-first approach, ensuring a flawless experience on smaller screens. All interactive elements, such as buttons and drag-and-drop targets, will be large (minimum 44x44 pixels) and easy to use with a touchscreen.
*   **Data Persistence & Auto-Save:** The system will provide persistent access to previous work, preventing any data loss between sessions. An intelligent auto-save feature will trigger at each significant step or change, eliminating the need for manual saving.
*   **Distraction-Free Environment:** The editing interface will be clean, focused, and free of unnecessary UI elements to provide an optimal workspace.
*   **Contextual & On-Demand Support:** Help and guidance will be provided contextually, appearing only when the user is likely to need it, rather than cluttering the interface.
*   **Accessibility & Personalization:** The interface will include accessibility features, such as an optional dark mode and a **voice-guided conversational mode**, to accommodate diverse user preferences and needs, such as multitasking professionals who may be commuting.

#### **2.3 User Journey & Product Features**
The core user journey begins proactively from the user's email inbox:

1.  **Email Notification:** The user receives a job notification email based on their saved preferences.
2.  **Initiate AI Builder:** Within the email, next to each job listing, there is a distinct "AI Optimize" icon or button.
3.  **Deep Link to App:** The user clicks the icon. This is a secure, unique link that directs them to the Intelli-Resume SPA.
4.  **Automatic Data Import:** The system authenticates the user (prompting login if necessary) and automatically loads the builder interface with the specific job's title, description, and other details pre-populated.
5.  **Base Resume Input:** The user is prompted to either upload a new resume or select one from their personal library.
6.  **AI Processing with Feedback:** The user triggers the AI generation. The UI provides real-time status updates as the 10 specialized agents process and optimize the resume.
7.  **Interactive Editing:** The tailored resume is rendered in a component-based "canvas" editor, allowing for direct manipulation of text and sections. Alternatively, the user can activate a voice-guided mode to edit the resume through natural conversation.
8.  **Resume Management:** The user can save the new version to their library, manage multiple versions, and download the final resume as a PDF.

#### **2.4 User Classes and Characteristics**
The primary user class is the **Authenticated Job Seeker**. This user is actively engaged in their job search and receives email notifications from the platform. They are looking for a competitive edge and value tools that save time and improve the quality of their applications.

#### **2.5 Operating Environment & Constraints**
*   **Backend:** Laravel 10, PHP 8.1+, MySQL.
*   **Frontend:** **React** with **Inertia.js**. This dictates a component-based architecture and API-driven data flow.
*   **Browser Support:** The SPA must be fully functional on the latest versions of modern web browsers (Chrome, Firefox, Safari, Edge).
*   **Constraint:** The interactive editor must be built with React components. A true HTML5 `<canvas>` element is not suitable for this purpose. The goal is a "canvas-like" *experience* of direct manipulation.
*   **Constraint:** The AI processing must be handled by a queued, asynchronous backend job to prevent UI blocking and request timeouts. Communication of progress will be handled via API polling or WebSockets.

---

### **3. System Features (Functional Requirements)**

#### **3.1 Feature: Email-Driven Initiation**
-   **REQ-101:** Job notification emails shall include a distinct, touch-friendly call-to-action (icon or button) labeled "AI Optimize" for each job listed.
-   **REQ-102:** Each "AI Optimize" link shall be a unique, secure, deep-link URL containing the identifiers for the user and the specific job.
-   **REQ-103:** The system shall handle the deep link by seamlessly authenticating the user and redirecting them to the Intelli-Resume SPA with the correct job context. Post-login redirects must maintain the original job context.

#### **3.2 Feature: Resume Builder SPA**
-   **REQ-201:** The system shall provide a dedicated SPA built with React and Inertia.js, following a mobile-first responsive design.
-   **REQ-202:** On page load, the SPA shall display a skeleton screen while making an API call to the Laravel backend to fetch the full details of the job specified in the URL.
-   **REQ-203:** The UI shall feature a two-panel layout that is responsive and adapts to a single-column view on mobile devices.

#### **3.3 Feature: Base Resume Input**
-   **REQ-301:** The system shall provide a large, touch-friendly React component for uploading a new resume file (formats: `.pdf`, `.docx`, `.txt`).
-   **REQ-302:** The system shall provide a visually intuitive React component (e.g., a carousel or grid of cards) for selecting a resume from the user's existing library.
-   **REQ-303:** All resume files shall be stored securely, associated with the user's ID, and managed via the backend.

#### **3.4 Feature: Asynchronous AI-Powered Resume Generation**
-   **REQ-401:** A "Generate Tailored Resume" button shall trigger an API call to the backend to start the AI generation process.
-   **REQ-402:** The backend shall dispatch a queued job to handle the 10-step AI algorithm, immediately returning a `job_id` to the frontend.
-   **REQ-403:** The frontend shall display a progress indicator showing the completion percentage and the current step of the AI process (e.g., "Step 3/10: Analyzing Skills...").
-   **REQ-404 (Agent 1 - Job Parser):** The system shall parse the job description to extract keywords, required skills, and key responsibilities.
-   **REQ-405 (Agent 2 - Resume Parser):** The system shall parse the user's base resume to identify and structure its core sections.
-   **REQ-406 (Agent 3 - Gap Analyst):** The system shall compare the outputs of the previous agents to identify skill matches and gaps.
-   **REQ-407 (Agent 4 - Summary Tailor):** The system shall rewrite the professional summary.
-   **REQ-408 (Agent 5 - Experience Refiner):** The system shall rephrase work experience bullet points.
-   **REQ-409 (Agent 6 - Skills Optimizer):** The system shall rebuild the skills section.
-   **REQ-410 (Agent 7 - Keyword Integration):** The system shall ensure optimal keyword density.
-   **REQ-411 (Agent 8 - Tone & Polish):** The system shall check for grammar and professional tone.
-   **REQ-412 (Agent 9 - Template Engine):** The system shall structure the output in a standardized JSON format for the React frontend.
-   **REQ-413 (Agent 10 - Scorer):** The system shall generate a "Match Score" and a summary of improvements.

#### **3.5 Feature: Interactive React-Based Editor**
-   **REQ-501:** The generated resume content (delivered as JSON) shall be rendered using React components.
-   **REQ-502:** Each text block (paragraph, bullet point) shall be a separate component that, when clicked, becomes an editable input field.
-   **REQ-503:** The main sections of the resume shall be rendered as components that can be reordered using a drag-and-drop library compatible with React (e.g., `react-beautiful-dnd`).
-   **REQ-504:** The system shall provide at least three professional resume templates, implemented as different sets of React components and CSS styles.

#### **3.6 Feature: Voice-Guided Conversational Editing**
-   **REQ-601:** The system shall provide a voice activation button (e.g., a microphone icon) to initiate the conversational editing mode.
-   **REQ-602:** The system shall use the browser's built-in Web Speech API for speech-to-text and text-to-speech functionalities, requiring no special equipment.
-   **REQ-603:** The user shall be able to navigate the resume sections by voice (e.g., "Go to work experience," "Read the summary").
-   **REQ-604:** The user shall be able to select and modify specific content by voice (e.g., "In the first job, change 'managed a team' to 'led a team'").
-   **REQ-605:** The system shall provide clear audio feedback to confirm actions and read back the modified text.
-   **REQ-606:** The voice interface shall intelligently handle ambiguous commands by asking clarifying questions (e.g., "Which of the three bullet points would you like to change?").
-   **REQ-607:** All voice inputs shall first be transcribed into text and displayed within a confirmation interface (e.g., a modal or a dedicated input box). The user must be able to review, edit, and explicitly submit the transcribed command before the system executes it.

#### **3.7 Feature: Output and Resume Library Management**
-   **REQ-701:** A "Save to Library" button shall make an API call to save the current state of the resume. The system shall use a smart default for the filename (e.g., "Resume for Senior Developer at Acme Corp").
-   **REQ-702:** A "Download PDF" button shall make an API call, sending the final resume JSON to the backend, which will generate and stream back a high-quality PDF.
-   **REQ-703:** The user's resume library shall display all base and tailored resumes, allowing for deletion and management.

---

### **4. Non-Functional Requirements**

| ID | Requirement |
|---|---|
| **NFR-01** | **Performance:** The initial SPA load shall be under 3 seconds. API responses for data fetching must be under 500ms. The AI generation job must complete within 90 seconds. |
| **NFR-02** | **Security:** All API endpoints must be protected by authentication and authorization middleware. Deep links from emails must use single-use tokens to prevent unauthorized access. |
| **NFR-03** | **Usability:** The UI must be modern, intuitive, and responsive, providing a seamless experience consistent with a Single Page Application. All interactive elements must have a minimum touch target size of 44x44 pixels. |
| **NFR-04** | **Reliability:** If the backend AI job fails, it must update its status accordingly, and the frontend must display a user-friendly error message with an option to retry. |
| **NFR-05** | **Maintainability:** The React frontend must be built with a clear component hierarchy and state management strategy (e.g., Context API or Redux). |
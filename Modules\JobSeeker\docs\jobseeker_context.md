# JobSeeker Module – Working Context (Living Document)

Last updated: 2025-08-13

This document captures the current working context, decisions, architecture notes, and operational conventions for the JobSeeker module. It is intended to be updated over time and used as the authoritative reference during development and UAT.

## Executive summary
- Founder-first Email Content Management System (ECMS) with live preview and Bootstrap 5 admin.
- Reliable job synchronization from Jobs.af and ACBAR with language filtering, category/location mapping, and observability.
- Non‑English title handling is controlled at three levels with clear precedence: Rule > Provider default > Global.
- Scheduler-driven execution (via app/Console/Kernel.php) with dynamic rule loading from DB.
- Testing discipline: never use RefreshDatabase; use DatabaseTransactions; targeted runs only.

## Key features and files

### 1) Email Content Management System (ECMS)
- Admin UI: Email Content Manager
  - View: [email-content-manager/index.blade.php](mdc:resources/views/modules/jobseeker/admin/email-content-manager/index.blade.php)
  - Controller: [EmailContentManagerController.php](mdc:Modules/JobSeeker/Http/Controllers/Admin/EmailContentManagerController.php)
  - Routes: `admin/jobseeker/email-content-manager/*` in [routes.php](mdc:Modules/JobSeeker/Http/routes.php)
- UI highlights
  - Bootstrap 5, mobile-first; live email preview via iframe
  - “Focus mode”, “Show enabled only”, “Compact rows”
  - Aggregation limits saved via dedicated endpoint

### 2) Language detection & Non‑English policy
- Centralized detection service
  - [LanguageDetectionService.php](mdc:Modules/JobSeeker/Services/LanguageDetectionService.php)
  - Policy is configurable in [config/jobseeker.php](mdc:config/jobseeker.php) → `language_detection`:
    - `mixed_as_english` (bool, default true)
    - `max_rtl_ratio` (float 0.0–0.5, used when strict)
- Provider-aware consumption
  - Services call `isEnglishTitle($text, $providerSlug)` with `'jobs.af'` or `'acbar'`.

### 3) Allow Non‑English title – Precedence & sources (CRITICAL)
- Precedence order:
  1) Rule-level: `CommandScheduleFilter.allow_non_english` (if rule exists)
  2) Provider default: `ProviderSetting.allow_non_english_default` per provider
  3) Global default: `config('jobseeker.*_default_filters.allow_non_english')`
- Enforcement & timing
  - Language filtering happens after fetch (during processing), not at fetch time.
  - Jobs.af: [JobsAfService.php](mdc:Modules/JobSeeker/Services/JobsAfService.php) resolves and logs `{source: rule|provider|global}`.
  - ACBAR: [AcbarJobService.php](mdc:Modules/JobSeeker/Services/AcbarJobService.php)
    - Manual runs: provider default > global
    - With rule: rule’s filter if present; else provider default > global

### 4) Provider Settings (Founder UI)
- Page: Admin → Job Providers
  - View: [providers/index.blade.php](mdc:resources/views/modules/jobseeker/admin/providers/index.blade.php)
  - Controller: [ProviderSettingsController.php](mdc:Modules/JobSeeker/Http/Controllers/Admin/ProviderSettingsController.php)
  - Routes: `admin/jobseeker/providers/*` (requires `jobseeker.manage_settings`) in [routes.php](mdc:Modules/JobSeeker/Http/routes.php)
- Storage: `jobseeker_provider_settings`
  - Model: [ProviderSetting.php](mdc:Modules/JobSeeker/Entities/ProviderSetting.php)
  - Bound to [JobProvider.php](mdc:Modules/JobSeeker/Entities/JobProvider.php) via `provider_id` (preferred) and `provider_name` (legacy)
  - SQL (idempotent): [20250811_120000_create_jobseeker_provider_settings.sql](mdc:Modules/JobSeeker/docs/sql/20250811_120000_create_jobseeker_provider_settings.sql)

### 5) Scheduler & entrypoint
- Kernel entrypoint: [app/Console/Kernel.php](mdc:app/Console/Kernel.php) → `schedule()` calls `loadDynamicCommandSchedule($schedule)` (around line 207)
- Inspect rules: `php artisan schedule:list`
- Manual triggers:
  - `php artisan jobseeker:sync-jobs-af --schedule-rule-id=<id>`
  - `php artisan jobseeker:sync-acbar-jobs --schedule-rule-id=<id>`
 - Schedule context threading: Kernel appends `--schedule-rule-id=<id>` to every scheduled command; commands/services pass this context down to the notification hub so admin alerts can reference the exact dynamic rule/execution.

### 6) Email sending & UAT
- All sending via [EmailService.php](mdc:app/Services/EmailService.php)
- UAT option: add `config('jobseeker.job_notifications.dry_run')` guard to log intent and skip transports (not currently enforced). Use service logs to validate flow without sending.
 - Admin/system emails (including missed-call alerts) also use `EmailService` and inherit circuit‑breaker/failover behavior.

### 7) Admin navigation
- Layout: [layouts/app.blade.php](mdc:resources/views/modules/jobseeker/layouts/app.blade.php)
  - Admin group detection fixed for `admin/jobseeker*`
  - Added submenu for Job Providers with proper active state

### 8) Missed Call Notification System (Admin Alerts)
- Purpose: Immediately alert the admin when a scheduled JobSeeker run results in zero emails sent to job seekers, ensuring visibility into notification gaps across all providers.
- Triggers (fires every time the condition occurs):
  - No jobs fetched from provider (empty result set)
  - Jobs fetched but zero notifications sent (e.g., filtered out by language/location/thresholds or category/recipient filters)
- Recipients: `jobseeker_settings.admin_notification_email` (DB key: `admin_notification_email`).
- Content includes: provider, command, total/new/updated job counts, categories considered, reasons why no emails were sent, schedule_rule_id, execution_id (when available), correlation id.
- Implementation:
  - Service: [MissedCallNotificationService.php](mdc:Modules/JobSeeker/Services/MissedCallNotificationService.php)
  - Hub integration: [JobNotificationHub::notifyAggregatedJobs](mdc:Modules/JobSeeker/Services/JobNotificationHub.php)
    - Signature extended to accept optional `$scheduleContext`
    - Sends admin alert when `$sentCount === 0` and also in the early "no jobs" path
  - Email view: [emails/admin/missed_call_notification.blade.php](mdc:resources/views/modules/jobseeker/emails/admin/missed_call_notification.blade.php)
- Notes:
  - Provider‑agnostic by design; works for current and future providers
  - Routed through `EmailService` for provider failover and circuit‑breaker safety
  - Minimal risk: alerting does not interrupt the main sync flow (exceptions are logged, not thrown)

## Data model & SQL
- Provider Settings
  - Table: `jobseeker_provider_settings`
  - Columns: `provider_id` (nullable FK), `provider_name` (unique), `allow_non_english_default` (TINYINT), `mixed_as_english` (TINYINT), `max_rtl_ratio` (DECIMAL), timestamps
  - Defaults UPSERT for `'jobs.af'`, `'acbar'`; backfill `provider_id` via join on slug/name
- Job Providers
  - Table: `jobseeker_job_providers`
  - Model: [JobProvider.php](mdc:Modules/JobSeeker/Entities/JobProvider.php) with `settings()` hasOne binding

### Provider category mapping (Unified)
- `provider_job_categories` holds provider→canonical mappings for all providers.
- Schedule categories are sourced from `CommandScheduleFilter.categories` (containing `provider_job_categories.id`) for ALL providers.
- `job_provider_category_pivot` is the single source of truth for job↔provider categories (legacy canonical pivot writes removed).

### Locations (Unified)
- Location filtering uses provider location identifiers via `provider_job_locations` and repository‑translated filters.
- String‑based post‑fetch heuristics (e.g., “Kabul” contains) were removed.

## Testing & safety conventions
- NEVER use `RefreshDatabase` (data loss risk). Use `DatabaseTransactions` only.
- Run targeted tests only; avoid destructive suites.
- Preferred UAT: run via scheduler/commands and verify via logs and reported stats.
- For clean tests, use the scoped SQL reset files under `Modules/JobSeeker/docs/sql/` (e.g., `*_test_reset_*`), never drop tables. Remove any temporary helpers after testing.
 - Missed‑call end‑to‑end check (Technology category example):
   1) Reset recent test data (jobs created in last N days and recent `job_notification_sent_jobs`) using the SQL helpers in `docs/sql/`.
   2) Run first sync from Kernel/commands for Jobs.af and ACBAR with Technology categories; expect normal job seeker emails when new/updated jobs exist.
   3) Run the same sync again immediately; expect a missed‑call admin email when zero notifications are sent.

## Standardized provider stats & interface
- Both `JobsAfService` and `AcbarJobService` implement `JobProviderSyncInterface::syncAndAggregate(?array $providerCategoryIdentifiers, ?int $scheduleRuleId)`.
- Standard stats keys (always present):
  - success, created, updated, errors, skipped_no_category_map, categories_processed
  - jobs_fetched, jobs_by_category, error_types, api_response_time, category_response_time
  - non_english_skipped, non_english_included, empty_title_skipped, location_filtered

## Logging & observability
- Services log: rule id, provider, source of `allow_non_english`, `non_english_skipped`, `non_english_included`, `empty_title_skipped`.
- Health metrics per sync: total fetched, per-category counts, error types.
- Execution records tracked in admin dashboards.

## Edge cases & TODOs
- Providers list: UI currently shows settings rows; consider left-join on providers to display all providers.
- Permissions: ensure `jobseeker.manage_settings` exists and is assigned (provide SQL if needed).
- Ratio slider UX vs storage: UI shows 0–50%; backend expects 0–0.5; ensure consistent save/display.
- Language scope: only titles are checked; extend if non-title content filtering becomes a requirement.
- Long-running ACBAR sync (~200s): ensure lock/overlap protection for scheduled runs.
- Preview coupling: ensure `window.refreshEmailPreview` exists where invoked.

## How to update this document
- Update this file whenever we change:
  - Precedence rules, UI locations, routes, detection policy, or scheduler behavior
  - Data model and SQL seeds/constraints
  - Testing conventions and UAT practices

## Quick references
- Jobs.af: [JobsAfService.php](mdc:Modules/JobSeeker/Services/JobsAfService.php)
- ACBAR: [AcbarJobService.php](mdc:Modules/JobSeeker/Services/AcbarJobService.php)
- Detection: [LanguageDetectionService.php](mdc:Modules/JobSeeker/Services/LanguageDetectionService.php)
- ProviderSetting: [ProviderSetting.php](mdc:Modules/JobSeeker/Entities/ProviderSetting.php)
- Providers UI: [providers/index.blade.php](mdc:resources/views/modules/jobseeker/admin/providers/index.blade.php)
- Routes: [Modules/JobSeeker/Http/routes.php](mdc:Modules/JobSeeker/Http/routes.php)
- Kernel scheduler: [app/Console/Kernel.php](mdc:app/Console/Kernel.php)
 - Missed‑call alert service: [MissedCallNotificationService.php](mdc:Modules/JobSeeker/Services/MissedCallNotificationService.php)
 - Notification hub: [JobNotificationHub.php](mdc:Modules/JobSeeker/Services/JobNotificationHub.php)
 - Admin email template: [missed_call_notification.blade.php](mdc:resources/views/modules/jobseeker/emails/admin/missed_call_notification.blade.php)

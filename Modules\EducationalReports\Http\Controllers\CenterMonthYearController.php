<?php

namespace Modules\EducationalReports\Http\Controllers;


use App\Center;
use App\EvaluationSchemaOption;
use App\MoshafSurah;
use App\Student;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;


class CenterMonthYearController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function getMonthYears(Center $center)
    {

//        considering laravel code, a class_id is inside StudentHefzReport model with belongsTo relationship where each class is assigned to a center_id. update this query accordingly:
        $dates = \App\StudentHefzReport::whereHas('classes', function ($query) use ($center) {
            $query->where('center_id', $center->id);
        })
            ->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year')
            ->groupBy('year', 'month')
            ->orderByDesc('year')
            ->orderByRaw("FIELD(month, 'December', 'November', 'October', 'September', 'August', 'July', 'June', 'May', 'April', 'March', 'February', 'January')")
            ->get();




        return response()->json($dates);
    }
}

<?php

namespace Modules\Education\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\ProgramLevel;
use App\User;
use Illuminate\Http\Request;
use Session;
use App\Subject;

class StudentRequireEmailVerificationController extends Controller
{

// StudentController.php
    public function requireEmailVerificationCount() {
        $count  = \App\User::whereNull("email_verified_at")->count();

        return response()->json(['count' => $count]);
    }


}

var EcommerceOrdersView=function(){var e=function(){var e=new Datatable;e.init({src:$("#datatable_invoices"),onSuccess:function(e){},onError:function(e){},loadingMessage:"Loading...",dataTable:{lengthMenu:[[10,20,50,100,150,-1],[19,20,50,100,150,"All"]],pageLength:10,ajax:{url:"../demo/ecommerce_order_invoices.php"},order:[[1,"asc"]]}}),e.getTableWrapper().on("click",".table-group-action-submit",function(a){a.preventDefault();var t=$(".table-group-action-input",e.getTableWrapper());""!=t.val()&&e.getSelectedRowsCount()>0?(e.setAjaxParam("customActionType","group_action"),e.setAjaxParam("customActionName",t.val()),e.setAjaxParam("id",e.getSelectedRows()),e.getDataTable().ajax.reload(),e.clearAjaxParams()):""==t.val()?App.alert({type:"danger",icon:"warning",message:"Please select an action",container:e.getTableWrapper(),place:"prepend"}):0===e.getSelectedRowsCount()&&App.alert({type:"danger",icon:"warning",message:"No record selected",container:e.getTableWrapper(),place:"prepend"})})},a=function(){var e=new Datatable;e.init({src:$("#datatable_credit_memos"),onSuccess:function(e){},onError:function(e){},loadingMessage:"Loading...",dataTable:{lengthMenu:[[10,20,50,100,150,-1],[10,20,50,100,150,"All"]],pageLength:10,ajax:{url:"../demo/ecommerce_order_credit_memos.php"},columnDefs:[{orderable:!0,targets:[0]}],order:[[0,"asc"]]}})},t=function(){var e=new Datatable;e.init({src:$("#datatable_shipment"),onSuccess:function(e){},onError:function(e){},loadingMessage:"Loading...",dataTable:{lengthMenu:[[10,20,50,100,150,-1],[10,20,50,100,150,"All"]],pageLength:10,ajax:{url:"../demo/ecommerce_order_shipment.php"},columnDefs:[{orderable:!0,targets:[0]}],order:[[0,"asc"]]}})},n=function(){var e=new Datatable;e.init({src:$("#datatable_history"),onSuccess:function(e){},onError:function(e){},loadingMessage:"Loading...",dataTable:{lengthMenu:[[10,20,50,100,150,-1],[10,20,50,100,150,"All"]],pageLength:10,ajax:{url:"../demo/ecommerce_order_history.php"},columnDefs:[{orderable:!0,targets:[0]}],order:[[0,"asc"]]}}),e.getTableWrapper().on("click",".table-group-action-submit",function(a){a.preventDefault();var t=$(".table-group-action-input",e.getTableWrapper());""!=t.val()&&e.getSelectedRowsCount()>0?(e.setAjaxParam("customActionType","group_action"),e.setAjaxParam("customActionName",t.val()),e.setAjaxParam("id",e.getSelectedRows()),e.getDataTable().ajax.reload(),e.clearAjaxParams()):""==t.val()?App.alert({type:"danger",icon:"warning",message:"Please select an action",container:e.getTableWrapper(),place:"prepend"}):0===e.getSelectedRowsCount()&&App.alert({type:"danger",icon:"warning",message:"No record selected",container:e.getTableWrapper(),place:"prepend"})})},r=function(){$(".date-picker").datepicker({rtl:App.isRTL(),autoclose:!0}),$(".datetime-picker").datetimepicker({isRTL:App.isRTL(),autoclose:!0,todayBtn:!0,pickerPosition:App.isRTL()?"bottom-right":"bottom-left",minuteStep:10})};return{init:function(){r(),e(),a(),t(),n()}}}();jQuery(document).ready(function(){EcommerceOrdersView.init()});
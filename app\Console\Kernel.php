<?php
declare(strict_types=1);

namespace App\Console;

use App\Console\Commands\CacheTrashedStudentsCommand;

use App\Console\Commands\InvalidateModulesCache;
use App\Console\Commands\LogMonitorCommand;
use App\Console\Commands\SendPendingMemorizationRevisionPlansReminderEmails;
use App\Console\Commands\TrashMissedClockOutAttendanceUpdate;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

use Modules\JobSeeker\Entities\CommandScheduleRule;
use Modules\JobSeeker\Entities\CommandScheduleExecution;
use Modules\JobSeeker\Services\CommandScheduleService;
use Illuminate\Support\Facades\Log;



class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        TrashMissedClockOutAttendanceUpdate::class,
        SendPendingMemorizationRevisionPlansReminderEmails::class,
        CacheTrashedStudentsCommand::class,
        InvalidateModulesCache::class,
        Commands\CleanupMissedClockoutRecords::class,
        // Commands\SendPendingIjazasanadPlansReminderEmails::class,
        // SendPendingINouranyaPlansReminderEmails::class,
        // Commands\EmailSponsorsMonthlyAttendance::class,
        Commands\CacheClassesCommand::class,
        Commands\EmailTestingCommand::class,
        LogMonitorCommand::class,
        // GenerateDatabaseDocumentation::class,
        // Commands\WriteToFileCommand::class,
    ];

    /**
     * Log command failure to both general and individual command log files
     * 
     * @param string $commandName The name of the failed command
     * @param string $error Additional error details
     * @return void
     */
    protected function logCommandFailure(string $commandName, string $error = ''): void
    {
        $timestamp = now();
        $errorMessage = "[COMMAND FAILURE] {$commandName} failed at " . $timestamp;
        if ($error) {
            $errorMessage .= " | Error: {$error}";
        }
        
        // Create detailed error information
        $detailedError = [
            'timestamp' => $timestamp->format('Y-m-d H:i:s T'),
            'command' => $commandName,
            'error' => $error,
            'server_load' => sys_getloadavg(),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'disk_free_space' => disk_free_space(storage_path()),
            'process_id' => getmypid(),
        ];
        
        // Log to the general error log
        Log::error($errorMessage, $detailedError);
        
        // Log to the general command failures log
        Log::channel('command_failures')->error($errorMessage, $detailedError);
        
        // Create individual command log file
        $this->logToIndividualCommandFile($commandName, $errorMessage, $detailedError);
    }

    /**
     * Log command failure to an individual command-specific log file
     * 
     * @param string $commandName The name of the failed command
     * @param string $errorMessage The formatted error message
     * @param array $detailedError Detailed error information
     * @return void
     */
    protected function logToIndividualCommandFile(string $commandName, string $errorMessage, array $detailedError): void
    {
        try {
            // Sanitize command name for file system
            $sanitizedCommand = preg_replace('/[^a-zA-Z0-9_-]/', '_', $commandName);
            $sanitizedCommand = trim($sanitizedCommand, '_');
            
            // Create command failures directory if it doesn't exist
            $logDir = storage_path('logs/command_failures');
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
            
            // Individual command log file path
            $logFile = $logDir . '/' . $sanitizedCommand . '.log';
            
            // Prepare comprehensive log entry
            $logEntry = [
                'timestamp' => $detailedError['timestamp'],
                'message' => $errorMessage,
                'command_details' => [
                    'command_name' => $commandName,
                    'error_description' => $detailedError['error'],
                ],
                'system_info' => [
                    'server_load' => $detailedError['server_load'],
                    'memory_usage_mb' => round($detailedError['memory_usage'] / 1024 / 1024, 2),
                    'memory_peak_mb' => round($detailedError['memory_peak'] / 1024 / 1024, 2),
                    'disk_free_space_gb' => round($detailedError['disk_free_space'] / 1024 / 1024 / 1024, 2),
                    'process_id' => $detailedError['process_id'],
                    'php_version' => PHP_VERSION,
                    'laravel_version' => app()->version(),
                ],
                'environment' => [
                    'app_env' => config('app.env'),
                    'app_debug' => config('app.debug'),
                    'queue_driver' => config('queue.default'),
                    'cache_driver' => config('cache.default'),
                ],
                'recent_commands' => $this->getRecentCommandHistory(),
            ];
            
            // Format as JSON for easy parsing and readability
            $formattedEntry = json_encode($logEntry, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n" . str_repeat('-', 80) . "\n";
            
            // Append to individual command log file
            file_put_contents($logFile, $formattedEntry, FILE_APPEND | LOCK_EX);
            
            // Also create a human-readable version
            $humanReadableFile = $logDir . '/' . $sanitizedCommand . '_readable.log';
            $humanReadableEntry = sprintf(
                "[%s] COMMAND FAILURE: %s\n" .
                "Error: %s\n" .
                "Memory Usage: %s MB (Peak: %s MB)\n" .
                "Server Load: %s\n" .
                "Disk Free: %s GB\n" .
                "Process ID: %s\n" .
                "Environment: %s\n" .
                "%s\n\n",
                $detailedError['timestamp'],
                $commandName,
                $detailedError['error'] ?: 'No specific error details provided',
                round($detailedError['memory_usage'] / 1024 / 1024, 2),
                round($detailedError['memory_peak'] / 1024 / 1024, 2),
                is_array($detailedError['server_load']) ? implode(', ', $detailedError['server_load']) : 'N/A',
                round($detailedError['disk_free_space'] / 1024 / 1024 / 1024, 2),
                $detailedError['process_id'],
                config('app.env'),
                str_repeat('-', 80)
            );
            
            file_put_contents($humanReadableFile, $humanReadableEntry, FILE_APPEND | LOCK_EX);
            
        } catch (\Exception $e) {
            // Fallback logging if individual file logging fails
            Log::error('Failed to create individual command log file', [
                'command' => $commandName,
                'file_error' => $e->getMessage(),
                'original_error' => $errorMessage
            ]);
        }
    }

    /**
     * Get recent command execution history for context
     * 
     * @return array
     */
    protected function getRecentCommandHistory(): array
    {
        try {
            // Get recent log entries from scheduler log
            $schedulerLog = storage_path('logs/scheduler.log');
            if (!file_exists($schedulerLog)) {
                return ['message' => 'No scheduler log found'];
            }
            
            // Get last 10 lines for context
            $lines = file($schedulerLog, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $recentLines = array_slice($lines, -10);
            
            return [
                'recent_scheduler_entries' => $recentLines,
                'total_log_lines' => count($lines)
            ];
        } catch (\Exception $e) {
            return ['error' => 'Could not read command history: ' . $e->getMessage()];
        }
    }

   /**
    * 
    */
    protected function schedule(Schedule $schedule)
    {
        // Dynamic scheduler. All active rules are loaded from `command_schedule_rules` and each run
        // is tracked in `command_schedule_executions`. We append `--schedule-rule-id={id}` to every
        // scheduled command for observability and health reporting.
        /*
         * End-to-End Job Notification Flow (Dynamic Scheduler ➜ Providers ➜ Orchestration ➜ Email)
         *
         * This documents the complete technical implementation of the job notification system.
         * Updated: 2025-08-26 to reflect current implementation with intelligent category assignment
         * and dual-pivot category synchronization for notification matching.
         *
         * 1) Kernel (`app/Console/Kernel.php`)
         *    - loadDynamicCommandSchedule($schedule) reads active rows from `command_schedule_rules`,
         *      schedules their `command` and injects "--schedule-rule-id={id}" for traceability.
         *    - Execution lifecycle and next-run timestamps are persisted via `command_schedule_executions`.
         *
         * 2) Commands (multiple provider paths - extensible architecture)
         *    - PATH A (Jobs.af):   jobseeker:sync-jobs-af
         *         ➜ Modules/JobSeeker/Console/Commands/SyncJobsAfCommand::handle()
         *         ➜ Modules/JobSeeker/Services/JobsAfService::fetchAndNotifyJobs()
         *    - PATH B (ACBAR):     jobseeker:sync-acbar-jobs
         *         ➜ Modules/JobSeeker/Console/Commands/SyncAcbarJobsCommand::handle()
         *         ➜ Modules/JobSeeker/Services/AcbarJobService::syncAcbarJobs()
         *    - PATH C (ReliefWeb): jobseeker:sync-reliefweb-jobs
         *         ➜ Modules/JobSeeker/Console/Commands/SyncReliefWebJobsCommand::handle()
         *         ➜ Modules/JobSeeker/Services/ReliefWebService::fetchAndNotifyJobs()
         *    - PATH D+ (Future):   Any new provider follows same pattern with JobNotificationService integration
         *
         * 3) Provider services (fetch + normalize + dual-pivot category assignment)
         *    - Fetch recent jobs from provider APIs, normalize, and persist to `jobs` table.
         *    - Build notification payloads with provider_category_ids for JobNotificationService compatibility.
         *    - Category assignment process:
         *      * Jobs.af: assignProviderCategoriesFromJobTitle() performs keyword matching against
         *        provider_job_categories table where provider_name='jobs.af'. Uses predefined
         *        keyword arrays (technology, management, administration, etc.) to match job titles.
         *        Falls back to Administrative category (ID 288) if no keywords match.
         *      * ACBAR: Uses existing provider_job_categories mappings based on ACBAR category IDs.
         *      * Both providers: formatJobData() calls CategoryMappingService to convert provider
         *        category IDs to canonical category IDs via canonical_category_id foreign key.
         *    - Database synchronization: Job creation syncs to TWO pivot tables:
         *      * job_provider_category_pivot: Links jobs to provider-specific categories
         *      * job_category_pivot: Links jobs to canonical categories (required for notifications)
         *    - Call JobNotificationService::notifyAggregatedJobs() - NO provider-specific notification logic.
         *
         * 4) Unified notification orchestration (provider-agnostic matching)
         *    - JobNotificationService::notifyAggregatedJobs() serves as centralized entry point for ALL providers.
         *    - Job-to-user matching: Queries job_category_pivot table to find jobs with canonical categories
         *      that match user notification setups in job_notification_category table. This requires jobs
         *      to have entries in job_category_pivot (not just job_provider_category_pivot).
         *    - JobNotificationHub::processAndDeliverNotifications() sends SINGLE consolidated email per recipient.
         *    - Job aggregation: Includes jobs from ALL providers within configurable "job age window" (default 7 days).
         *    - Recipient matching: Uses canonical category IDs to match jobs against user setup preferences.
         *    - TRIPLE-LAYER DEDUPLICATION prevents duplicate notifications:
         *      * Layer 1: In-memory sentMap guards within same execution
         *      * Layer 2: Database query of job_notification_sent_jobs before processing
         *      * Layer 3: Unique constraint (setup_id, job_id, recipient_email) + insertOrIgnore
         *    - Applies configurable aggregation limits (min/max jobs per email) to UNIFIED job list.
         *    - ENHANCED MISSED CALL: Real-time DB check with proper cache invalidation.
         *    - ELIMINATES DUPLICATE EMAILS: Single email contains all relevant jobs grouped by category.
         *
         * 5) Email delivery (synchronous for JobSeeker + IDEMPOTENCY TRACKING)
         *    - app/Services/EmailService::sendEmail(...) renders and sends immediately (no queue).
         *    - View: resources/views/modules/jobseeker/emails/jobs/jobseeker_notification_new.blade.php
         *      expects: jobs (array), jobSeeker (object), setup (object). Multiple jobs are rendered.
         *    - CRITICAL: After successful send, records each (setup_id, job_id, recipient_email) tuple in
         *      job_notification_sent_jobs table with unique constraint enforcement.
         *    - Future runs skip jobs already recorded as sent to prevent duplicate notifications.
         *
         * 6) Admin pre-flight preview (not scheduled; for safety)
         *    - UI: /admin/jobseeker/email-content-manager
         *    - Controller: Modules/JobSeeker/Http/Controllers/Admin/EmailContentManagerController::preview
         *      uses EmailContentManagerService::getSampleJobData() to render the same template with
         *      multiple jobs, validates critical routes, and runs HTML checks to catch regressions early.
         *
         * 7) Health monitoring and missed call alerts
         *    - MissedCallNotificationService sends comprehensive diagnostic emails when notifications fail.
         *    - Includes job statistics, category mapping analysis, user setup diagnostics, and actionable steps.
         *    - Scheduled monitors/cleanups keep the pipeline healthy; failures/metrics recorded per execution.
         *
         * === COMPLETE INTEGRATION TESTING PROMPT (No Scheduler Wait Required) ===
         *
         * Use this prompt for AI-assisted end-to-end testing of the entire pipeline:
         *
         * "Test the complete job notification pipeline from dynamic scheduler through email delivery.
         * Perform comprehensive integration testing without waiting for scheduler timing by:
         *
         * 1) SCHEDULER VALIDATION: Query command_schedule_rules and command_schedule_executions tables
         *    to verify active rules, recent executions, health metrics (jobs_fetched, execution_time,
         *    memory_usage, jobs_by_category). Check for failed executions and error patterns.
         *
         * 2) PROVIDER COMMAND TESTING: Execute sync commands manually with SINGLE CATEGORY targeting:
         *    - php artisan jobseeker:sync-jobs-af --category='IT - Software' (use provider categories)
         *    - php artisan jobseeker:sync-acbar-jobs --category=5 (use canonical categories)
         *    - php artisan jobseeker:sync-reliefweb-jobs --category=6866 (use ReliefWeb career category IDs)
         *    CRITICAL: Test ONE category at a time, never all categories. Verify job fetching, normalization,
         *    provider_category_ids payload structure, deduplication effectiveness, and execution tracking.
         *
         * 3) DATA FLOW VERIFICATION: Query jobs, job_notification_setups, job_notification_category,
         *    job_notification_sent_jobs, and outgoing_emails tables to trace job-to-recipient matching,
         *    aggregation logic, and email delivery status. Check for route failures in error messages.
         *
         * 4) EMAIL RENDERING TESTING: Test the admin preview system at /admin/jobseeker/email-content-manager
         *    by clicking 'Preview Email' or calling the preview API endpoint. Verify multiple jobs are
         *    rendered, validation errors/warnings are displayed, and resilient code handles route failures.
         *
         * 5) RESILIENT CODE VALIDATION: Check email templates for try-catch blocks around route() calls,
         *    error indicators (btn-ai-tailor-error), and graceful degradation. Test by temporarily breaking
         *    routes to ensure emails still send with error messages instead of crashing.
         *
         * 6) HEALTH MONITORING: Examine recent outgoing_emails for failed deliveries, error patterns,
         *    retry attempts, and success rates. Verify circuit breaker protection and comprehensive logging.
         *
         * === KEY SQL QUERIES FOR VALIDATION ===
         * - SELECT * FROM command_schedule_rules WHERE is_active=1 ORDER BY priority;
         * - SELECT * FROM command_schedule_executions ORDER BY started_at DESC LIMIT 10;
         * - SELECT * FROM jobs WHERE source IN ('Jobs.af','ACBAR','ReliefWeb') ORDER BY created_at DESC LIMIT 10;
         * - SELECT * FROM outgoing_emails WHERE subject LIKE '%job%' ORDER BY created_at DESC LIMIT 10;
         * - SELECT * FROM job_notification_sent_jobs WHERE recipient_email='<EMAIL>' LIMIT 10;
         * - SHOW INDEX FROM job_notification_sent_jobs; -- Verify unique constraint exists
         *
         * === PROVIDER INTEGRATION DETAILS ===
         * Jobs.af: Uses provider category mapping with fallback logic
         *    - Category translation: "IT - Software" → ProviderJobCategory ID 271 → Canonical "Technology"
         *    - Fallback logic: job->providerCategories() → getProviderCategoryIdsForJob() if needed
         *    - Example: "Job Alert: 1 new jobs in Technology" sent at 2025-08-26T10:20:06Z
         * ACBAR: Uses canonical category translation
         *    - Category mapping: Canonical ID 5 (Education) → ACBAR provider IDs [16,59,71]
         *    - Example: 19 jobs categorized as Admin-Clerical
         * ReliefWeb: Uses error handling with reportJobFetchFailure()
         *
         * === TECHNICAL IMPLEMENTATION DETAILS ===
         * Type Safety: JobNotificationHub method signatures accept ?int $executionId (was ?string)
         * Category Robustness: JobsAfService ensures ALL jobs have provider_category_ids via fallbacks
         * Idempotency Enforcement: Triple-layer deduplication prevents duplicate notifications
         * Database Constraints: uniq_setup_job_recipient index enforces (setup_id, job_id, recipient_email)
         * Error Handling: Comprehensive try-catch blocks and defensive type casting
         *
         * === EMAIL NOTIFICATION IMPLEMENTATION ===
         * Consolidated Emails: Single email per recipient with jobs from multiple providers
         * Deduplication: No job sent twice to same recipient for same setup
         * Job Age Controls: Configurable 1-30 day notification window via UI
         * Missed Call Toggle: Real-time database checks with FILTER_VALIDATE_BOOLEAN
         * Aggregation Limits: Min/max jobs per email with UI controls
         *
         * === UI & BROWSER IMPLEMENTATION ===
         * Email Control Board: All settings functional and validated
         * Browser Automation: Playwright testing confirms UI reliability
         * JavaScript Guards: Defensive coding prevents ReferenceErrors
         * Route Validation: All admin routes properly defined and secured
         *
         * === PERFORMANCE & RELIABILITY IMPLEMENTATION ===
         * Database Optimization: Unified date expressions, active() scope usage
         * Memory Management: Replaced static caches with instance properties
         * PII Protection: Email masking in logs for privacy compliance
         * Exception Safety: Comprehensive error handling throughout system
         *
         * === NOTIFICATION FLOW DIAGRAM ===
         *
         * Dynamic Scheduler → Provider Command → Provider Service → JobNotificationService
         *                                                                      ↓
         * EmailService ← JobNotificationHub ← CategoryMappingService ← [Maps provider→canonical]
         *      ↓
         * outgoing_emails + job_notification_sent_jobs (idempotency tracking)
         *
         * DEDUPLICATION LAYERS:
         * 1) sentMap (in-memory): Prevents duplicates within same execution
         * 2) Database query: Loads existing sent jobs before processing
         * 3) Unique constraint: (setup_id, job_id, recipient_email) + insertOrIgnore
         *
         * TECHNICAL ARCHITECTURE SUMMARY:
         * - Dual-pivot category system: provider_job_categories → job_provider_category_pivot (tracking)
         *   and job_categories → job_category_pivot (notification matching)
         * - Intelligent fallback: Jobs.af uses keyword matching when no explicit categories provided
         * - Canonical mapping: CategoryMappingService translates provider categories to canonical
         *   categories via canonical_category_id foreign key relationships
         * - Notification dependency: System requires jobs to exist in job_category_pivot table
         *   with canonical categories to match against user notification setups
         *
         * Technical implementation details documented with working examples and actual database
         * records. Category handling includes intelligent fallback logic, type safety enforced
         * through method signatures, and idempotency guaranteed through database constraints.
         *
         * Complete flowchart: Modules/JobSeeker/docs/gap_analysis_and_improvement_plan.md
         */
        $this->loadDynamicCommandSchedule($schedule);



      

        // Clean up stale device tokens - run weekly
        $schedule->command('jobseeker:cleanup-device-tokens --days=30')
            ->weekly()
            ->sundays()
            ->at('03:00')
            ->timezone('Asia/Kuala_Lumpur')
            ->appendOutputTo(storage_path('logs/scheduler.log'))
            ->before(function () {
                Log::info("Starting device token cleanup at " . now());
            })
            ->onSuccess(function () {
                Log::info("Device token cleanup executed successfully at " . now());
            })
            ->onFailure(function () {
                $this->logCommandFailure('jobseeker:cleanup-device-tokens');
            });

        // Clean up command execution history - run every 48 hours
        $schedule->command('jobseeker:cleanup-execution-history --hours=48')
            ->cron('0 4 */2 * *') // Every 2 days at 4:00 AM
            ->timezone('Asia/Kuala_Lumpur')
            ->appendOutputTo(storage_path('logs/scheduler.log'))
            ->before(function () {
                Log::info("Starting command execution history cleanup at " . now());
            })
            ->onSuccess(function () {
                Log::info("Command execution history cleanup executed successfully at " . now());
            })
            ->onFailure(function () {
                $this->logCommandFailure('jobseeker:cleanup-execution-history');
            });

        // Log monitor command - run hourly to check for issues
        $schedule->command('logs:monitor --date=today')
            ->cron('0 0 */2 * *')
            ->timezone('Asia/Kuala_Lumpur')
            ->appendOutputTo(storage_path('logs/scheduler.log'))
            ->onSuccess(function () {
                Log::info("Command logs:monitor for today's logs executed successfully at " . now());
            })
            ->onFailure(function () {
                $this->logCommandFailure('logs:monitor');
            });

        // Memorization Revision Reminder Scheduling
        // $revSetting = MemorizationrevisionEmailSetting::first();
        // if ($revSetting) {
        //     $tz = $revSetting->timezone ?: 'Asia/Kuala_Lumpur';

        //     switch ($revSetting->frequency) {
        //         case 'daily':
        //             $time = $revSetting->daily_time ?: '23:58';
        //             $schedule->command('email:send-pending-memorization-revision-plans-reminder')
        //                 ->dailyAt($time)
        //                 ->timezone($tz)
        //                 ->appendOutputTo(storage_path('logs/scheduler.log'))
        //                 ->onSuccess(function () use ($time, $tz) {
        //                     Log::info("Command email:send-pending-memorization-revision-plans-reminder (daily at {$time}, {$tz}) executed successfully at " . now());
        //                 })
        //                 ->onFailure(function () use ($time, $tz) {
        //                     $this->logCommandFailure('email:send-pending-memorization-revision-plans-reminder', "Failed for daily schedule at {$time}, {$tz}");
        //                 });
        //             break;

        //         case 'weekly':
        //             $time = $revSetting->daily_time ?: '23:58';
        //             $daysMapping = [
        //                 'Sun' => 0,
        //                 'Mon' => 1,
        //                 'Tue' => 2,
        //                 'Wed' => 3,
        //                 'Thu' => 4,
        //                 'Fri' => 5,
        //                 'Sat' => 6,
        //             ];
        //             $dayNumber = isset($daysMapping[$revSetting->weekly_day]) ? $daysMapping[$revSetting->weekly_day] : 1;
        //             $schedule->command('email:send-pending-memorization-revision-plans-reminder')
        //                 ->weeklyOn($dayNumber, $time)
        //                 ->timezone($tz)
        //                 ->appendOutputTo(storage_path('logs/scheduler.log'))
        //                 ->onSuccess(function () use ($dayNumber, $time, $tz) {
        //                     Log::info("Command email:send-pending-memorization-revision-plans-reminder (weekly on day {$dayNumber} at {$time}, {$tz}) executed successfully at " . now());
        //                 })
        //                 ->onFailure(function () use ($dayNumber, $time, $tz) {
        //                     $this->logCommandFailure('email:send-pending-memorization-revision-plans-reminder', "Failed for weekly schedule on day {$dayNumber} at {$time}, {$tz}");
        //                 });
        //             break;

        //         case 'monthly':
        //             $time = $revSetting->daily_time ?: '23:58';
        //             $day = $revSetting->monthly_day ?: 1;
        //             $schedule->command('email:send-pending-memorization-revision-plans-reminder')
        //                 ->monthlyOn($day, $time)
        //                 ->timezone($tz)
        //                 ->appendOutputTo(storage_path('logs/scheduler.log'))
        //                 ->onSuccess(function () use ($day, $time, $tz) {
        //                     Log::info("Command email:send-pending-memorization-revision-plans-reminder (monthly on day {$day} at {$time}, {$tz}) executed successfully at " . now());
        //                 })
        //                 ->onFailure(function () use ($day, $time, $tz) {
        //                     $this->logCommandFailure('email:send-pending-memorization-revision-plans-reminder', "Failed for monthly schedule on day {$day} at {$time}, {$tz}");
        //                 });
        //             break;

        //         default:
        //             $schedule->command('email:send-pending-memorization-revision-plans-reminder')
        //                 ->dailyAt('23:58')
        //                 ->timezone($tz)
        //                 ->appendOutputTo(storage_path('logs/scheduler.log'))
        //                 ->onSuccess(function () use ($tz) {
        //                     Log::info("Command email:send-pending-memorization-revision-plans-reminder (default daily at 23:58, {$tz}) executed successfully at " . now());
        //                 })
        //                 ->onFailure(function () use ($tz) {
        //                     $this->logCommandFailure('email:send-pending-memorization-revision-plans-reminder', "Failed for default daily schedule at 23:58, {$tz}");
        //                 });
        //             break;
        //     }
        // } else {
        //     // Fallback if no revision settings found
        //     $schedule->command('email:send-pending-memorization-revision-plans-reminder')
        //         ->dailyAt('23:58')
        //         ->timezone('Asia/Kuala_Lumpur')
        //         ->appendOutputTo(storage_path('logs/scheduler.log'))
        //         ->onSuccess(function () {
        //             Log::info("Command email:send-pending-memorization-revision-plans-reminder (fallback daily at 23:58, Asia/Kuala_Lumpur) executed successfully at " . now());
        //         })
        //         ->onFailure(function () {
        //             $this->logCommandFailure('email:send-pending-memorization-revision-plans-reminder', "Failed for fallback daily schedule at 23:58, Asia/Kuala_Lumpur");
        //         });
        // }

        // Nouranya Reminder Scheduling
        // $nouranyaSetting = \App\NouranyaEmailSetting::first();

        // if ($nouranyaSetting) {
        //     $tz = $nouranyaSetting->timezone ?: 'Asia/Kuala_Lumpur';



        //     switch ($nouranyaSetting->frequency) {
        //         case 'daily':
        //             $time = date('H:i', strtotime($nouranyaSetting->daily_time));

        //             $schedule->command('email:send-pending-nouranya-plans-reminder')
        //                 ->dailyAt($time)
        //                 ->timezone($tz)
        //                 ->appendOutputTo(storage_path('logs/scheduler.log'))
        //                 ->onSuccess(function () use ($time, $tz) {
        //                     Log::info("Command email:send-pending-nouranya-plans-reminder (daily at {$time}, {$tz}) executed successfully at " . now());
        //                 })
        //                 ->onFailure(function () use ($time, $tz) {
        //                     $this->logCommandFailure('email:send-pending-nouranya-plans-reminder', "Failed for daily schedule at {$time}, {$tz}");
        //                 });
        //             break;

        //         case 'weekly':
        //             $time = $nouranyaSetting->daily_time ?: '23:59';
        //             $daysMapping = [
        //                 'Sun' => 0,
        //                 'Mon' => 1,
        //                 'Tue' => 2,
        //                 'Wed' => 3,
        //                 'Thu' => 4,
        //                 'Fri' => 5,
        //                 'Sat' => 6,
        //             ];
        //             $dayNumber = isset($daysMapping[$nouranyaSetting->weekly_day]) ? $daysMapping[$nouranyaSetting->weekly_day] : 1;
        //             $schedule->command('email:send-pending-nouranya-plans-reminder')
        //                 ->weeklyOn($dayNumber, $time)
        //                 ->timezone($tz)
        //                 ->appendOutputTo(storage_path('logs/scheduler.log'))
        //                 ->onSuccess(function () use ($dayNumber, $time, $tz) {
        //                     Log::info("Command email:send-pending-nouranya-plans-reminder (weekly on day {$dayNumber} at {$time}, {$tz}) executed successfully at " . now());
        //                 })
        //                 ->onFailure(function () use ($dayNumber, $time, $tz) {
        //                     $this->logCommandFailure('email:send-pending-nouranya-plans-reminder', "Failed for weekly schedule on day {$dayNumber} at {$time}, {$tz}");
        //                 });
        //             break;

        //         case 'monthly':
        //             $time = $nouranyaSetting->daily_time ?: '23:59';
        //             $day = $nouranyaSetting->monthly_day ?: 1;
        //             $schedule->command('email:send-pending-nouranya-plans-reminder')
        //                 ->monthlyOn($day, $time)
        //                 ->timezone($tz)
        //                 ->appendOutputTo(storage_path('logs/scheduler.log'))
        //                 ->onSuccess(function () use ($day, $time, $tz) {
        //                     Log::info("Command email:send-pending-nouranya-plans-reminder (monthly on day {$day} at {$time}, {$tz}) executed successfully at " . now());
        //                 })
        //                 ->onFailure(function () use ($day, $time, $tz) {
        //                     $this->logCommandFailure('email:send-pending-nouranya-plans-reminder', "Failed for monthly schedule on day {$day} at {$time}, {$tz}");
        //                 });
        //             break;

        //         default:
        //             $schedule->command('email:send-pending-nouranya-plans-reminder')
        //                 ->dailyAt('23:59')
        //                 ->timezone($tz)
        //                 ->appendOutputTo(storage_path('logs/scheduler.log'))
        //                 ->onSuccess(function () use ($tz) {
        //                     Log::info("Command email:send-pending-nouranya-plans-reminder (default daily at 23:59, {$tz}) executed successfully at " . now());
        //                 })
        //                 ->onFailure(function () use ($tz) {
        //                     $this->logCommandFailure('email:send-pending-nouranya-plans-reminder', "Failed for default daily schedule at 23:59, {$tz}");
        //                 });
        //             break;
        //     }
        // } else {
        //     // Fallback if no nouranya settings found
        //     $schedule->command('email:send-pending-nouranya-plans-reminder')
        //         ->dailyAt('23:59')
        //         ->timezone('Asia/Kuala_Lumpur')
        //         ->appendOutputTo(storage_path('logs/scheduler.log'))
        //         ->onSuccess(function () {
        //             Log::info("Command email:send-pending-nouranya-plans-reminder (fallback daily at 23:59, Asia/Kuala_Lumpur) executed successfully at " . now());
        //         })
        //         ->onFailure(function () {
        //             $this->logCommandFailure('email:send-pending-nouranya-plans-reminder', "Failed for fallback daily schedule at 23:59, Asia/Kuala_Lumpur");
        //         });
        // }


        $setting = \App\IjazasanadEmailSetting::first();


        // if ($setting) {
        //     $tz = $setting->timezone ?: 'Asia/Kuala_Lumpur';

        //     switch ($setting->frequency) {
        //         case 'daily':
        //             $time = $setting->daily_time ?: '23:58';
        //             $schedule->command('email:send-pending-ijazasanad-plans-reminder')
        //                 ->dailyAt($time)
        //                 ->timezone($tz)
        //                 ->appendOutputTo(storage_path('logs/scheduler.log'))
        //                 ->onSuccess(function () use ($time, $tz) {
        //                     Log::info("Command email:send-pending-ijazasanad-plans-reminder (daily at {$time}, {$tz}) executed successfully at " . now());
        //                 })
        //                 ->onFailure(function () use ($time, $tz) {
        //                     $this->logCommandFailure('email:send-pending-ijazasanad-plans-reminder', "Failed for daily schedule at {$time}, {$tz}");
        //                 });
        //             break;

        //         case 'weekly':
        //             $time = $setting->daily_time ?: '23:58';
        //             // Map day string to numeric (0=Sunday, 1=Monday, etc.)
        //             $daysMapping = [
        //                 'Sun' => 0,
        //                 'Mon' => 1,
        //                 'Tue' => 2,
        //                 'Wed' => 3,
        //                 'Thu' => 4,
        //                 'Fri' => 5,
        //                 'Sat' => 6,
        //             ];
        //             $dayNumber = isset($daysMapping[$setting->weekly_day]) ? $daysMapping[$setting->weekly_day] : 1;
        //             $schedule->command('email:send-pending-ijazasanad-plans-reminder')
        //                 ->weeklyOn($dayNumber, $time)
        //                 ->timezone($tz)
        //                 ->appendOutputTo(storage_path('logs/scheduler.log'))
        //                 ->onSuccess(function () use ($dayNumber, $time, $tz) {
        //                     Log::info("Command email:send-pending-ijazasanad-plans-reminder (weekly on day {$dayNumber} at {$time}, {$tz}) executed successfully at " . now());
        //                 })
        //                 ->onFailure(function () use ($dayNumber, $time, $tz) {
        //                     $this->logCommandFailure('email:send-pending-ijazasanad-plans-reminder', "Failed for weekly schedule on day {$dayNumber} at {$time}, {$tz}");
        //                 });
        //             break;

        //         case 'monthly':
        //             $time = $setting->daily_time ?: '23:58';
        //             $day = $setting->monthly_day ?: 1;
        //             $schedule->command('email:send-pending-ijazasanad-plans-reminder')
        //                 ->monthlyOn($day, $time)
        //                 ->timezone($tz)
        //                 ->appendOutputTo(storage_path('logs/scheduler.log'))
        //                 ->onSuccess(function () use ($day, $time, $tz) {
        //                     Log::info("Command email:send-pending-ijazasanad-plans-reminder (monthly on day {$day} at {$time}, {$tz}) executed successfully at " . now());
        //                 })
        //                 ->onFailure(function () use ($day, $time, $tz) {
        //                     $this->logCommandFailure('email:send-pending-ijazasanad-plans-reminder', "Failed for monthly schedule on day {$day} at {$time}, {$tz}");
        //                 });
        //             break;

        //         default:
        //             // Fallback to daily at 23:58
        //             $schedule->command('email:send-pending-ijazasanad-plans-reminder')
        //                 ->dailyAt('23:58')
        //                 ->timezone($tz)
        //                 ->appendOutputTo(storage_path('logs/scheduler.log'))
        //                 ->onSuccess(function () use ($tz) {
        //                     Log::info("Command email:send-pending-ijazasanad-plans-reminder (default daily at 23:58, {$tz}) executed successfully at " . now());
        //                 })
        //                 ->onFailure(function () use ($tz) {
        //                     $this->logCommandFailure('email:send-pending-ijazasanad-plans-reminder', "Failed for default daily schedule at 23:58, {$tz}");
        //                 });
        //             break;
        //     }
        // }
        // else {
        //     // If no settings exist, fallback to the default schedule.
        //     $schedule->command('email:send-pending-ijazasanad-plans-reminder')
        //         ->dailyAt('23:58')
        //         ->timezone('Asia/Kuala_Lumpur')
        //         ->appendOutputTo(storage_path('logs/scheduler.log'))
        //         ->onSuccess(function () {
        //             Log::info("Command email:send-pending-ijazasanad-plans-reminder (fallback daily at 23:58, Asia/Kuala_Lumpur) executed successfully at " . now());
        //         })
        //         ->onFailure(function () {
        //             $this->logCommandFailure('email:send-pending-ijazasanad-plans-reminder', "Failed for fallback daily schedule at 23:58, Asia/Kuala_Lumpur");
        //         });
        // }


        $schedule->command('trash:missedclockouts')
            ->dailyAt('00:05')
            ->timezone('Asia/Kuala_Lumpur')
            ->appendOutputTo(storage_path('logs/scheduler.log'))
            ->onSuccess(function () {
                Log::info('Command trash:missedclockouts executed successfully at ' . now());
            })
            ->onFailure(function () {
                $this->logCommandFailure('trash:missedclockouts');
            });

        $schedule->command('cache:trashedstudents')
            ->dailyAt('00:06')
            ->timezone('Asia/Kuala_Lumpur')
            ->appendOutputTo(storage_path('logs/scheduler.log'))
            ->onSuccess(function () {
                Log::info('Command cache:trashedstudents executed successfully at ' . now());
            })
            ->onFailure(function () {
                $this->logCommandFailure('cache:trashedstudents');
            });

        $schedule->command('optimize:clear')
            ->dailyAt('23:59')
            ->timezone('Asia/Kuala_Lumpur')
            ->appendOutputTo(storage_path('logs/scheduler.log'))
            ->onSuccess(function () {
                Log::info('Command optimize:clear executed successfully at ' . now());
            })
            ->onFailure(function () {
                $this->logCommandFailure('optimize:clear');
            });

        $schedule->command('attendance:email-sponsors')
            ->monthlyOn(1, '2:00')
            ->appendOutputTo(storage_path('logs/scheduler.log'))
            ->onSuccess(function () {
                Log::info('Command attendance:email-sponsors executed successfully at ' . now());
            })
            ->onFailure(function () {
                $this->logCommandFailure('attendance:email-sponsors');
            });


       

        // Process retries every 30 minutes
        // $schedule->command('jobseeker:process-notification-retries --limit=100')
        //     ->everyThirtyMinutes()
        //     ->withoutOverlapping()
        //     ->appendOutputTo(storage_path('logs/notification-retries.log'));

        // Monitor system health daily and send a report
        $schedule->command('jobseeker:monitor-job-notifications --days=1 --send-report')
            ->dailyAt('08:00')
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/notification-health.log'));

        // Quick health check every hour without sending report
        $schedule->command('jobseeker:monitor-job-notifications --days=1')
            ->hourlyAt(15)
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/notification-health-hourly.log'));

        // Add cleanup command to the schedule
        $schedule->command('jobseeker:cleanup-job-notifications --days=30 --failures-days=90 --metrics-days=180')
            ->weekly()
            ->sundays()
            ->at('01:30')
            ->withoutOverlapping()
            ->appendOutputTo(storage_path('logs/notification-cleanup.log'));

        // Note: Job notifications are handled automatically via JobProcessedEvent
        // when jobs are created/updated. No manual scheduling needed.

            $schedule->command('backup:run --only-db')
            ->dailyAt('00:10')
            ->timezone('Asia/Kuala_Lumpur')
            ->appendOutputTo(storage_path('logs/scheduler.log'))
            ->onSuccess(function () {
                Log::info('Command backup:run --only-db executed successfully at ' . now());
            })
            ->onFailure(function () {
                $this->logCommandFailure('backup:run --only-db');
            });

        // $schedule->command('telescope:prune --hours=48')->daily();
    }
    
    protected function loadDynamicCommandSchedule(Schedule $schedule): void
    {
        try {
            Log::info('Loading dynamic command schedule rules');
            
            // Get all active schedule rules ordered by priority
            $scheduleRules = CommandScheduleRule::getActiveRules();
            
            Log::info('Found dynamic schedule rules', [
                'count' => $scheduleRules->count(),
                'rules' => $scheduleRules->pluck('name', 'id')->toArray()
            ]);
            
            foreach ($scheduleRules as $rule) {
                $this->applyDynamicScheduleRule($schedule, $rule);
            }
            
        } catch (\Exception $e) {
            Log::error('Failed to load dynamic schedule rules', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Apply a single dynamic schedule rule to the scheduler
     *
     * @param Schedule $schedule
     * @param CommandScheduleRule $rule
     * @return void
     */
    protected function applyDynamicScheduleRule(Schedule $schedule, CommandScheduleRule $rule): void
    {
        try {
            Log::debug('Applying dynamic schedule rule', [
                'rule_id' => $rule->id,
                'rule_name' => $rule->name,
                'command' => $rule->command,
                'expression' => $rule->schedule_expression,
                'type' => $rule->schedule_type
            ]);

            // Create the scheduled command with schedule rule ID parameter
            $commandWithParams = $rule->command . ' --schedule-rule-id=' . $rule->id;
            $scheduledCommand = $schedule->command($commandWithParams);

            Log::debug('Kernel: Added schedule-rule-id parameter to command', [
                'rule_id' => $rule->id,
                'original_command' => $rule->command,
                'command_with_params' => $commandWithParams
            ]);
            
            // Apply the schedule based on type
            switch ($rule->schedule_type) {
                case 'cron':
                    $scheduledCommand->cron($rule->schedule_expression);
                    break;
                    
                case 'daily_at':
                    $scheduledCommand->dailyAt($rule->schedule_expression);
                    break;
                    
                case 'weekly_at':
                    // The schedule expressions in the database are already proper cron expressions
                    // like "56 7 * * 1" for Monday at 7:56, so we should use them directly
                    $scheduledCommand->cron($rule->schedule_expression);
                    Log::debug('Applied weekly_at rule using cron expression', [
                        'rule_id' => $rule->id,
                        'cron_expression' => $rule->schedule_expression
                    ]);
                    break;
                    
                default:
                    // For custom types, try to parse as cron expression
                    $scheduledCommand->cron($rule->schedule_expression);
                    break;
            }
            
            // Apply timezone
            $scheduledCommand->timezone($rule->timezone);
            
            // Set execution limits - use execution_timeout if available, fallback to max_execution_time
            $timeoutSeconds = $rule->execution_timeout ?? $rule->max_execution_time;
            $maxExecutionMinutes = $timeoutSeconds > 0 ? intval($timeoutSeconds / 60) : 1;
            $maxExecutionMinutes = max(1, $maxExecutionMinutes); // Ensure at least 1 minute
            $scheduledCommand->withoutOverlapping($maxExecutionMinutes);
            
            // Add logging and output
            $scheduledCommand->appendOutputTo(storage_path('logs/dynamic_scheduler.log'));
            
            // Add before/success/failure callbacks with execution tracking
            $scheduledCommand->before(function () use ($rule) {
                // Create execution record
                $execution = CommandScheduleExecution::markStarted($rule->id, $rule->command);
                
                // Update rule's next_run_at to track execution start
                $rule->markExecutionStarted();
                
                Log::info("Starting dynamic rule execution", [
                    'rule_id' => $rule->id,
                    'rule_name' => $rule->name,
                    'command' => $rule->command,
                    'execution_id' => $execution->id,
                    'time' => now()->format('Y-m-d H:i:s T')
                ]);
            });
            
            $scheduledCommand->onSuccess(function () use ($rule) {
                // Get the latest execution for this rule
                $execution = $rule->getLastExecution();
                
                if ($execution && $execution->is_running) {
                    $memoryUsageMb = round(memory_get_peak_usage(true) / 1024 / 1024, 2);
                    $execution->markCompleted(0, null, $memoryUsageMb);
                }
                
                // Calculate and update next run time
                $service = new CommandScheduleService();
                $nextRunTime = $service->calculateNextRunTime($rule);
                $rule->markExecutionCompleted($nextRunTime);
                
                Log::info("Dynamic rule executed successfully", [
                    'rule_id' => $rule->id,
                    'rule_name' => $rule->name,
                    'command' => $rule->command,
                    'execution_id' => $execution?->id,
                    'next_run_at' => $nextRunTime?->format('Y-m-d H:i:s T'),
                    'time' => now()->format('Y-m-d H:i:s T')
                ]);
                
                // Handle dependent commands
                $this->executeDependentCommands($rule);
            });
            
            $scheduledCommand->onFailure(function () use ($rule) {
                // Get the latest execution for this rule
                $execution = $rule->getLastExecution();
                
                if ($execution && $execution->is_running) {
                    $memoryUsageMb = round(memory_get_peak_usage(true) / 1024 / 1024, 2);
                    $execution->markFailed(1, 'Command execution failed', null, $memoryUsageMb);
                }
                
                // Calculate next run time for retry
                $service = new CommandScheduleService();
                $nextRunTime = $service->calculateNextRunTime($rule);
                $rule->markExecutionFailed('Command execution failed', $nextRunTime);
                
                $this->logCommandFailure(
                    "Dynamic Rule: {$rule->name} ({$rule->command})",
                    "Rule ID: {$rule->id}, Expression: {$rule->schedule_expression}, Execution ID: {$execution?->id}"
                );
            });
            
            Log::info('Dynamic schedule rule applied successfully', [
                'rule_id' => $rule->id,
                'rule_name' => $rule->name,
                'command' => $rule->command,
                'final_cron_expression' => $rule->schedule_expression
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to apply dynamic schedule rule', [
                'rule_id' => $rule->id,
                'rule_name' => $rule->name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Execute commands that depend on the completed rule
     *
     * @param CommandScheduleRule $completedRule
     * @return void
     */
    protected function executeDependentCommands(CommandScheduleRule $completedRule): void
    {
        try {
            // Find rules that depend on this command
            $dependentRules = CommandScheduleRule::where('depends_on_command', $completedRule->command)
                ->where('is_active', true)
                ->orderBy('priority')
                ->get();
            
            if ($dependentRules->isEmpty()) {
                return;
            }
            
            Log::info('Found dependent commands to execute', [
                'completed_command' => $completedRule->command,
                'dependent_count' => $dependentRules->count(),
                'dependent_rules' => $dependentRules->pluck('name', 'id')->toArray()
            ]);
            
            foreach ($dependentRules as $dependentRule) {
                // Schedule the dependent command to run after the specified delay
                $delay = $dependentRule->delay_after_dependency ?? 900; // Default 15 minutes
                
                // Use a dedicated job class to execute the dependent command after delay
                \App\Jobs\ExecuteArtisanCommand::dispatch(
                    $dependentRule->id,
                    $dependentRule->name,
                    $dependentRule->command
                )->delay(now()->addSeconds($delay));
                
                Log::info('Scheduled dependent command for execution', [
                    'dependent_rule_id' => $dependentRule->id,
                    'dependent_command' => $dependentRule->command,
                    'delay_seconds' => $delay,
                    'execute_at' => now()->addSeconds($delay)->format('Y-m-d H:i:s T')
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('Failed to execute dependent commands', [
                'completed_command' => $completedRule->command,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Check if dynamic rules exist for a specific command
     *
     * @param string $command
     * @return bool
     */
    protected function hasDynamicRulesForCommand(string $command): bool
    {
        try {
            return CommandScheduleRule::where('command', $command)
                ->where('is_active', true)
                ->exists();
        } catch (\Exception $e) {
            Log::error('Failed to check for dynamic rules', [
                'command' => $command,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        // Only include the console.php file if it exists
        $consolePath = base_path('routes/console.php');
        if (file_exists($consolePath)) {
            require $consolePath;
        }
    }
}

<?php

declare(strict_types=1);

namespace Modules\Education\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use App\Classes;
use Carbon\Carbon;

final class ClassIjazasanadReportController extends Controller
{
    /**
     * Display the Ijazasanad Class Report
     *
     * @param int $id Class ID
     * @return View
     */
    public function index(int $id): View
    {
        return $this->ijazasanadReport($id);
    }

    /**
     * Display the Ijazasanad Class Report
     *
     * @param int $id Class ID
     * @return View
     */
    public function ijazasanadReport(int $id): View
    {
        $class = Classes::withTrashed()->find($id);

        // Handle wrong class ID
        if (is_null($class)) {
            abort(404, 'Class not found');
        }

        // Generate month-year options for the dropdown
        $monthYearOptions = $this->generateMonthYearOptions();

        return view('modules.education.classes.reports.class.ijazasanadHalaqah', [
            'class' => $class,
            'monthYearOptions' => $monthYearOptions
        ]);
    }

    /**
     * Generate month-year options for the dropdown
     *
     * @return array
     */
    private function generateMonthYearOptions(): array
    {
        $options = [];
        $currentDate = Carbon::now();
        $currentMonthYear = $currentDate->format('M Y');

        // Generate options for the current year and previous year
        for ($year = $currentDate->year; $year >= ($currentDate->year - 1); $year--) {
            for ($month = 12; $month >= 1; $month--) {
                $date = Carbon::create($year, $month, 1);
                $monthYear = $date->format('M Y');
                
                $options[] = [
                    'value' => $monthYear,
                    'label' => $monthYear,
                    'selected' => $monthYear === $currentMonthYear
                ];

                // Stop if we've reached current date
                if ($date->isFuture()) {
                    break;
                }
            }
        }

        return $options;
    }

    /**
     * Display the student Ijazasanad report
     *
     * @return View
     */
    public function studentReport(): View
    {
        // Get all classes for the dropdown
        $classes = Classes::where('status', 'active')
            ->orderBy('class_name', 'asc')
            ->get(['id', 'class_name']);

        return view('modules.education.classes.reports.student.ijazasanad', [
            'classes' => $classes
        ]);
    }
} 
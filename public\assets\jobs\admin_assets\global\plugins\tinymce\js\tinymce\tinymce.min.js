// 4.7.9 (2018-02-27)
!function(){"use strict";var e,t,n,r,o,i,a,u,s,c,l,f,d,m,p,g,h,v=function(e){return function(){return e}},y={noop:function(){},noarg:function(e){return function(){return e()}},compose:function(e,t){return function(){return e(t.apply(null,arguments))}},constant:v,identity:function(e){return e},tripleEquals:function(e,t){return e===t},curry:function(e){for(var t=new Array(arguments.length-1),n=1;n<arguments.length;n++)t[n-1]=arguments[n];return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];var o=t.concat(n);return e.apply(null,o)}},not:function(e){return function(){return!e.apply(null,arguments)}},die:function(e){return function(){throw new Error(e)}},apply:function(e){return e()},call:function(e){e()},never:v(!1),always:v(!0)},b=y.never,C=y.always,x=function(){return w},w=(r={fold:function(e,t){return e()},is:b,isSome:b,isNone:C,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},or:n,orThunk:t,map:x,ap:x,each:function(){},bind:x,flatten:x,exists:b,forall:C,filter:x,equals:e=function(e){return e.isNone()},equals_:e,toArray:function(){return[]},toString:y.constant("none()")},Object.freeze&&Object.freeze(r),r),N=function(e){var t=function(){return e},n=function(){return o},r=function(t){return t(e)},o={fold:function(t,n){return n(e)},is:function(t){return e===t},isSome:C,isNone:b,getOr:t,getOrThunk:t,getOrDie:t,or:n,orThunk:n,map:function(t){return N(t(e))},ap:function(t){return t.fold(x,function(t){return N(t(e))})},each:function(t){t(e)},bind:r,flatten:t,exists:r,forall:r,filter:function(t){return t(e)?o:w},equals:function(t){return t.is(e)},equals_:function(t,n){return t.fold(b,function(t){return n(e,t)})},toArray:function(){return[e]},toString:function(){return"some("+e+")"}};return o},E={some:N,none:x,from:function(e){return null===e||e===undefined?w:N(e)}},S=(o=Array.prototype.indexOf)===undefined?function(e,t){return D(e,t)}:function(e,t){return o.call(e,t)},k=function(e,t){return S(e,t)>-1},T=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o,e)}return r},A=function(e,t){for(var n=0,r=e.length;n<r;n++)t(e[n],n,e)},_=function(e,t){for(var n=e.length-1;n>=0;n--)t(e[n],n,e)},R=function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var i=e[r];t(i,r,e)&&n.push(i)}return n},B=function(e,t){for(var n=0,r=e.length;n<r;n++)if(t(e[n],n,e))return E.some(n);return E.none()},D=function(e,t){for(var n=0,r=e.length;n<r;++n)if(e[n]===t)return n;return-1},O=Array.prototype.push,P=function(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!Array.prototype.isPrototypeOf(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);O.apply(t,e[n])}return t},L=function(e,t){for(var n=0,r=e.length;n<r;++n)if(!0!==t(e[n],n,e))return!1;return!0},I=Array.prototype.slice,M={map:T,each:A,eachr:_,partition:function(e,t){for(var n=[],r=[],o=0,i=e.length;o<i;o++){var a=e[o];(t(a,o,e)?n:r).push(a)}return{pass:n,fail:r}},filter:R,groupBy:function(e,t){if(0===e.length)return[];for(var n=t(e[0]),r=[],o=[],i=0,a=e.length;i<a;i++){var u=e[i],s=t(u);s!==n&&(r.push(o),o=[]),n=s,o.push(u)}return 0!==o.length&&r.push(o),r},indexOf:function(e,t){var n=S(e,t);return-1===n?E.none():E.some(n)},foldr:function(e,t,n){return _(e,function(e){n=t(n,e)}),n},foldl:function(e,t,n){return A(e,function(e){n=t(n,e)}),n},find:function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n,e))return E.some(o)}return E.none()},findIndex:B,flatten:P,bind:function(e,t){var n=T(e,t);return P(n)},forall:L,exists:function(e,t){return B(e,t).isSome()},contains:k,equal:function(e,t){return e.length===t.length&&L(e,function(e,n){return e===t[n]})},reverse:function(e){var t=I.call(e,0);return t.reverse(),t},chunk:function(e,t){for(var n=[],r=0;r<e.length;r+=t){var o=e.slice(r,r+t);n.push(o)}return n},difference:function(e,t){return R(e,function(e){return!k(t,e)})},mapToObject:function(e,t){for(var n={},r=0,o=e.length;r<o;r++){var i=e[r];n[String(i)]=t(i,r)}return n},pure:function(e){return[e]},sort:function(e,t){var n=I.call(e,0);return n.sort(t),n},range:function(e,t){for(var n=[],r=0;r<e;r++)n.push(t(r));return n},head:function(e){return 0===e.length?E.none():E.some(e[0])},last:function(e){return 0===e.length?E.none():E.some(e[e.length-1])}},F="undefined"!=typeof window?window:Function("return this;")(),z=function(e,t){for(var n=t!==undefined&&null!==t?t:F,r=0;r<e.length&&n!==undefined&&null!==n;++r)n=n[e[r]];return n},U=function(e,t){var n=e.split(".");return z(n,t)},q={getOrDie:function(e,t){var n=U(e,t);if(n===undefined||null===n)throw e+" not available on this browser";return n}},V=function(){return q.getOrDie("URL")},H={createObjectURL:function(e){return V().createObjectURL(e)},revokeObjectURL:function(e){V().revokeObjectURL(e)}},j=navigator,$=j.userAgent,W=function(e){return"matchMedia"in window&&matchMedia(e).matches};d=/Android/.test($),a=(a=!(i=/WebKit/.test($))&&/MSIE/gi.test($)&&/Explorer/gi.test(j.appName))&&/MSIE (\w+)\./.exec($)[1],u=-1!==$.indexOf("Trident/")&&(-1!==$.indexOf("rv:")||-1!==j.appName.indexOf("Netscape"))&&11,s=-1!==$.indexOf("Edge/")&&!a&&!u&&12,a=a||u||s,c=!i&&!u&&/Gecko/.test($),l=-1!==$.indexOf("Mac"),f=/(iPad|iPhone)/.test($),m="FormData"in window&&"FileReader"in window&&"URL"in window&&!!H.createObjectURL,p=W("only screen and (max-device-width: 480px)")&&(d||f),g=W("only screen and (min-width: 800px)")&&(d||f),h=-1!==$.indexOf("Windows Phone"),s&&(i=!1);var K,X,Y,G,J,Q,Z,ee,te,ne,re,oe,ie,ae,ue,se,ce,le,fe,de={opera:!1,webkit:i,ie:a,gecko:c,mac:l,iOS:f,android:d,contentEditable:!f||m||parseInt($.match(/AppleWebKit\/(\d*)/)[1],10)>=534,transparentSrc:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",caretAfter:8!==a,range:window.getSelection&&"Range"in window,documentMode:a&&!s?document.documentMode||7:10,fileApi:m,ceFalse:!1===a||a>8,cacheSuffix:"",container:null,overrideViewPort:null,experimentalShadowDom:!1,canHaveCSP:!1===a||a>11,desktop:!p&&!g,windowsPhone:h},me=window.Promise?window.Promise:function(){function e(e,t){return function(){e.apply(t,arguments)}}var t=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},n=function(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],s(t,e(i,this),e(a,this))},r=n.immediateFn||"function"==typeof setImmediate&&setImmediate||function(e){setTimeout(e,1)};function o(e){var t=this;null!==this._state?r(function(){var n=t._state?e.onFulfilled:e.onRejected;if(null!==n){var r;try{r=n(t._value)}catch(o){return void e.reject(o)}e.resolve(r)}else(t._state?e.resolve:e.reject)(t._value)}):this._deferreds.push(e)}function i(t){try{if(t===this)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if("function"==typeof n)return void s(e(n,t),e(i,this),e(a,this))}this._state=!0,this._value=t,u.call(this)}catch(r){a.call(this,r)}}function a(e){this._state=!1,this._value=e,u.call(this)}function u(){for(var e=0,t=this._deferreds.length;e<t;e++)o.call(this,this._deferreds[e]);this._deferreds=null}function s(e,t,n){var r=!1;try{e(function(e){r||(r=!0,t(e))},function(e){r||(r=!0,n(e))})}catch(o){if(r)return;r=!0,n(o)}}return n.prototype["catch"]=function(e){return this.then(null,e)},n.prototype.then=function(e,t){var r=this;return new n(function(n,i){o.call(r,new function(e,t,n,r){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.resolve=n,this.reject=r}(e,t,n,i))})},n.all=function(){var e=Array.prototype.slice.call(1===arguments.length&&t(arguments[0])?arguments[0]:arguments);return new n(function(t,n){if(0===e.length)return t([]);var r=e.length;function o(i,a){try{if(a&&("object"==typeof a||"function"==typeof a)){var u=a.then;if("function"==typeof u)return void u.call(a,function(e){o(i,e)},n)}e[i]=a,0==--r&&t(e)}catch(s){n(s)}}for(var i=0;i<e.length;i++)o(i,e[i])})},n.resolve=function(e){return e&&"object"==typeof e&&e.constructor===n?e:new n(function(t){t(e)})},n.reject=function(e){return new n(function(t,n){n(e)})},n.race=function(e){return new n(function(t,n){for(var r=0,o=e.length;r<o;r++)e[r].then(t,n)})},n}(),pe=function(e,t){return"number"!=typeof t&&(t=0),setTimeout(e,t)},ge=function(e,t){return"number"!=typeof t&&(t=1),setInterval(e,t)},he=function(e,t){var n,r;return(r=function(){var r=arguments;clearTimeout(n),n=pe(function(){e.apply(this,r)},t)}).stop=function(){clearTimeout(n)},r},ve={requestAnimationFrame:function(e,t){K?K.then(e):K=new me(function(e){t||(t=document.body),function(e,t){var n,r=window.requestAnimationFrame,o=["ms","moz","webkit"];for(n=0;n<o.length&&!r;n++)r=window[o[n]+"RequestAnimationFrame"];r||(r=function(e){window.setTimeout(e,0)}),r(e,t)}(e,t)}).then(e)},setTimeout:pe,setInterval:ge,setEditorTimeout:function(e,t,n){return pe(function(){e.removed||t()},n)},setEditorInterval:function(e,t,n){var r;return r=ge(function(){e.removed?clearInterval(r):t()},n)},debounce:he,throttle:he,clearInterval:function(e){return clearInterval(e)},clearTimeout:function(e){return clearTimeout(e)}},ye=/^(?:mouse|contextmenu)|click/,be={keyLocation:1,layerX:1,layerY:1,returnValue:1,webkitMovementX:1,webkitMovementY:1,keyIdentifier:1},Ce=function(){return!1},xe=function(){return!0},we=function(e,t,n,r){e.addEventListener?e.addEventListener(t,n,r||!1):e.attachEvent&&e.attachEvent("on"+t,n)},Ne=function(e,t,n,r){e.removeEventListener?e.removeEventListener(t,n,r||!1):e.detachEvent&&e.detachEvent("on"+t,n)},Ee=function(e,t){var n,r,o,i,a,u,s=t||{};for(n in e)be[n]||(s[n]=e[n]);if(s.target||(s.target=s.srcElement||document),de.experimentalShadowDom&&(s.target=(r=e,o=s.target,a=o,(i=r.path)&&i.length>0&&(a=i[0]),r.composedPath&&(i=r.composedPath())&&i.length>0&&(a=i[0]),a)),e&&ye.test(e.type)&&e.pageX===undefined&&e.clientX!==undefined){var c=s.target.ownerDocument||document,l=c.documentElement,f=c.body;s.pageX=e.clientX+(l&&l.scrollLeft||f&&f.scrollLeft||0)-(l&&l.clientLeft||f&&f.clientLeft||0),s.pageY=e.clientY+(l&&l.scrollTop||f&&f.scrollTop||0)-(l&&l.clientTop||f&&f.clientTop||0)}return s.preventDefault=function(){s.isDefaultPrevented=xe,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},s.stopPropagation=function(){s.isPropagationStopped=xe,e&&(e.stopPropagation?e.stopPropagation():e.cancelBubble=!0)},s.stopImmediatePropagation=function(){s.isImmediatePropagationStopped=xe,s.stopPropagation()},0==((u=s).isDefaultPrevented===xe||u.isDefaultPrevented===Ce)&&(s.isDefaultPrevented=Ce,s.isPropagationStopped=Ce,s.isImmediatePropagationStopped=Ce),"undefined"==typeof s.metaKey&&(s.metaKey=!1),s},Se=function(e,t,n){var r=e.document,o={type:"ready"};if(n.domLoaded)t(o);else{var i=function(){return"complete"===r.readyState||"interactive"===r.readyState&&r.body},a=function(){n.domLoaded||(n.domLoaded=!0,t(o))},u=function(){i()&&(Ne(r,"readystatechange",u),a())},s=function(){try{r.documentElement.doScroll("left")}catch(e){return void ve.setTimeout(s)}a()};!r.addEventListener||de.ie&&de.ie<11?(we(r,"readystatechange",u),r.documentElement.doScroll&&e.self===e.top&&s()):i()?a():we(e,"DOMContentLoaded",a),we(e,"load",a)}},ke=function(){var e,t,n,r,o,i=this,a={};t="mce-data-"+(+new Date).toString(32),r="onmouseenter"in document.documentElement,n="onfocusin"in document.documentElement,o={mouseenter:"mouseover",mouseleave:"mouseout"},e=1,i.domLoaded=!1,i.events=a;var u=function(e,t){var n,r,o,i,u=a[t];if(n=u&&u[e.type])for(r=0,o=n.length;r<o;r++)if((i=n[r])&&!1===i.func.call(i.scope,e)&&e.preventDefault(),e.isImmediatePropagationStopped())return};i.bind=function(s,c,l,f){var d,m,p,g,h,v,y,b=window,C=function(e){u(Ee(e||b.event),d)};if(s&&3!==s.nodeType&&8!==s.nodeType){for(s[t]?d=s[t]:(d=e++,s[t]=d,a[d]={}),f=f||s,p=(c=c.split(" ")).length;p--;)v=C,h=y=!1,"DOMContentLoaded"===(g=c[p])&&(g="ready"),i.domLoaded&&"ready"===g&&"complete"===s.readyState?l.call(f,Ee({type:g})):(r||(h=o[g])&&(v=function(e){var t,n;if(t=e.currentTarget,(n=e.relatedTarget)&&t.contains)n=t.contains(n);else for(;n&&n!==t;)n=n.parentNode;n||((e=Ee(e||b.event)).type="mouseout"===e.type?"mouseleave":"mouseenter",e.target=t,u(e,d))}),n||"focusin"!==g&&"focusout"!==g||(y=!0,h="focusin"===g?"focus":"blur",v=function(e){(e=Ee(e||b.event)).type="focus"===e.type?"focusin":"focusout",u(e,d)}),(m=a[d][g])?"ready"===g&&i.domLoaded?l({type:g}):m.push({func:l,scope:f}):(a[d][g]=m=[{func:l,scope:f}],m.fakeName=h,m.capture=y,m.nativeHandler=v,"ready"===g?Se(s,v,i):we(s,h||g,v,y)));return s=m=0,l}},i.unbind=function(e,n,r){var o,u,s,c,l,f;if(!e||3===e.nodeType||8===e.nodeType)return i;if(o=e[t]){if(f=a[o],n){for(s=(n=n.split(" ")).length;s--;)if(u=f[l=n[s]]){if(r)for(c=u.length;c--;)if(u[c].func===r){var d=u.nativeHandler,m=u.fakeName,p=u.capture;(u=u.slice(0,c).concat(u.slice(c+1))).nativeHandler=d,u.fakeName=m,u.capture=p,f[l]=u}r&&0!==u.length||(delete f[l],Ne(e,u.fakeName||l,u.nativeHandler,u.capture))}}else{for(l in f)u=f[l],Ne(e,u.fakeName||l,u.nativeHandler,u.capture);f={}}for(l in f)return i;delete a[o];try{delete e[t]}catch(g){e[t]=null}}return i},i.fire=function(e,n,r){var o;if(!e||3===e.nodeType||8===e.nodeType)return i;for((r=Ee(null,r)).type=n,r.target=e;(o=e[t])&&u(r,o),(e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow)&&!r.isPropagationStopped(););return i},i.clean=function(e){var n,r,o=i.unbind;if(!e||3===e.nodeType||8===e.nodeType)return i;if(e[t]&&o(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName)for(o(e),n=(r=e.getElementsByTagName("*")).length;n--;)(e=r[n])[t]&&o(e);return i},i.destroy=function(){a={}},i.cancel=function(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1}};(ke.Event=new ke).bind(window,"ready",function(){});var Te="sizzle"+-new Date,Ae=window.document,_e=0,Re=0,Be=lt(),De=lt(),Oe=lt(),Pe=function(e,t){return e===t&&(oe=!0),0},Le=typeof undefined,Ie=1<<31,Me={}.hasOwnProperty,Fe=[],ze=Fe.pop,Ue=Fe.push,qe=Fe.push,Ve=Fe.slice,He=Fe.indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(this[t]===e)return t;return-1},je="[\\x20\\t\\r\\n\\f]",$e="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",We="\\["+je+"*("+$e+")(?:"+je+"*([*^$|!~]?=)"+je+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+$e+"))|)"+je+"*\\]",Ke=":("+$e+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+We+")*)|.*)\\)|)",Xe=new RegExp("^"+je+"+|((?:^|[^\\\\])(?:\\\\.)*)"+je+"+$","g"),Ye=new RegExp("^"+je+"*,"+je+"*"),Ge=new RegExp("^"+je+"*([>+~]|"+je+")"+je+"*"),Je=new RegExp("="+je+"*([^\\]'\"]*?)"+je+"*\\]","g"),Qe=new RegExp(Ke),Ze=new RegExp("^"+$e+"$"),et={ID:new RegExp("^#("+$e+")"),CLASS:new RegExp("^\\.("+$e+")"),TAG:new RegExp("^("+$e+"|[*])"),ATTR:new RegExp("^"+We),PSEUDO:new RegExp("^"+Ke),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+je+"*(even|odd|(([+-]|)(\\d*)n|)"+je+"*(?:([+-]|)"+je+"*(\\d+)|))"+je+"*\\)|)","i"),bool:new RegExp("^(?:checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped)$","i"),needsContext:new RegExp("^"+je+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+je+"*((?:-\\d)?\\d*)"+je+"*\\)|)(?=[^-]|$)","i")},tt=/^(?:input|select|textarea|button)$/i,nt=/^h\d$/i,rt=/^[^{]+\{\s*\[native \w/,ot=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,it=/[+~]/,at=/'|\\/g,ut=new RegExp("\\\\([\\da-f]{1,6}"+je+"?|("+je+")|.)","ig"),st=function(e,t,n){var r="0x"+t-65536;return r!=r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)};try{qe.apply(Fe=Ve.call(Ae.childNodes),Ae.childNodes),Fe[Ae.childNodes.length].nodeType}catch(vx){qe={apply:Fe.length?function(e,t){Ue.apply(e,Ve.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}var ct=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m;if((t?t.ownerDocument||t:Ae)!==ae&&ie(t),n=n||[],!e||"string"!=typeof e)return n;if(1!==(u=(t=t||ae).nodeType)&&9!==u)return[];if(se&&!r){if(o=ot.exec(e))if(a=o[1]){if(9===u){if(!(i=t.getElementById(a))||!i.parentNode)return n;if(i.id===a)return n.push(i),n}else if(t.ownerDocument&&(i=t.ownerDocument.getElementById(a))&&fe(t,i)&&i.id===a)return n.push(i),n}else{if(o[2])return qe.apply(n,t.getElementsByTagName(e)),n;if((a=o[3])&&Y.getElementsByClassName)return qe.apply(n,t.getElementsByClassName(a)),n}if(Y.qsa&&(!ce||!ce.test(e))){if(f=l=Te,d=t,m=9===u&&e,1===u&&"object"!==t.nodeName.toLowerCase()){for(c=Z(e),(l=t.getAttribute("id"))?f=l.replace(at,"\\$&"):t.setAttribute("id",f),f="[id='"+f+"'] ",s=c.length;s--;)c[s]=f+yt(c[s]);d=it.test(e)&&ht(t.parentNode)||t,m=c.join(",")}if(m)try{return qe.apply(n,d.querySelectorAll(m)),n}catch(p){}finally{l||t.removeAttribute("id")}}}return te(e.replace(Xe,"$1"),t,n,r)};function lt(){var e=[];return function t(n,r){return e.push(n+" ")>G.cacheLength&&delete t[e.shift()],t[n+" "]=r}}function ft(e){return e[Te]=!0,e}function dt(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||Ie)-(~e.sourceIndex||Ie);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function mt(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}function pt(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function gt(e){return ft(function(t){return t=+t,ft(function(n,r){for(var o,i=e([],n.length,t),a=i.length;a--;)n[o=i[a]]&&(n[o]=!(r[o]=n[o]))})})}function ht(e){return e&&typeof e.getElementsByTagName!==Le&&e}for(X in Y=ct.support={},Q=ct.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},ie=ct.setDocument=function(e){var t,n=e?e.ownerDocument||e:Ae,r=n.defaultView;return n!==ae&&9===n.nodeType&&n.documentElement?(ae=n,ue=n.documentElement,se=!Q(n),r&&r!==function(e){try{return e.top}catch(t){}return null}(r)&&(r.addEventListener?r.addEventListener("unload",function(){ie()},!1):r.attachEvent&&r.attachEvent("onunload",function(){ie()})),Y.attributes=!0,Y.getElementsByTagName=!0,Y.getElementsByClassName=rt.test(n.getElementsByClassName),Y.getById=!0,G.find.ID=function(e,t){if(typeof t.getElementById!==Le&&se){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},G.filter.ID=function(e){var t=e.replace(ut,st);return function(e){return e.getAttribute("id")===t}},G.find.TAG=Y.getElementsByTagName?function(e,t){if(typeof t.getElementsByTagName!==Le)return t.getElementsByTagName(e)}:function(e,t){var n,r=[],o=0,i=t.getElementsByTagName(e);if("*"===e){for(;n=i[o++];)1===n.nodeType&&r.push(n);return r}return i},G.find.CLASS=Y.getElementsByClassName&&function(e,t){if(se)return t.getElementsByClassName(e)},le=[],ce=[],Y.disconnectedMatch=!0,ce=ce.length&&new RegExp(ce.join("|")),le=le.length&&new RegExp(le.join("|")),t=rt.test(ue.compareDocumentPosition),fe=t||rt.test(ue.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},Pe=t?function(e,t){if(e===t)return oe=!0,0;var r=!e.compareDocumentPosition-!t.compareDocumentPosition;return r||(1&(r=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!Y.sortDetached&&t.compareDocumentPosition(e)===r?e===n||e.ownerDocument===Ae&&fe(Ae,e)?-1:t===n||t.ownerDocument===Ae&&fe(Ae,t)?1:re?He.call(re,e)-He.call(re,t):0:4&r?-1:1)}:function(e,t){if(e===t)return oe=!0,0;var r,o=0,i=e.parentNode,a=t.parentNode,u=[e],s=[t];if(!i||!a)return e===n?-1:t===n?1:i?-1:a?1:re?He.call(re,e)-He.call(re,t):0;if(i===a)return dt(e,t);for(r=e;r=r.parentNode;)u.unshift(r);for(r=t;r=r.parentNode;)s.unshift(r);for(;u[o]===s[o];)o++;return o?dt(u[o],s[o]):u[o]===Ae?-1:s[o]===Ae?1:0},n):ae},ct.matches=function(e,t){return ct(e,null,null,t)},ct.matchesSelector=function(e,t){if((e.ownerDocument||e)!==ae&&ie(e),t=t.replace(Je,"='$1']"),Y.matchesSelector&&se&&(!le||!le.test(t))&&(!ce||!ce.test(t)))try{var n=(void 0).call(e,t);if(n||Y.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(vx){}return ct(t,ae,null,[e]).length>0},ct.contains=function(e,t){return(e.ownerDocument||e)!==ae&&ie(e),fe(e,t)},ct.attr=function(e,t){(e.ownerDocument||e)!==ae&&ie(e);var n=G.attrHandle[t.toLowerCase()],r=n&&Me.call(G.attrHandle,t.toLowerCase())?n(e,t,!se):undefined;return r!==undefined?r:Y.attributes||!se?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},ct.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},ct.uniqueSort=function(e){var t,n=[],r=0,o=0;if(oe=!Y.detectDuplicates,re=!Y.sortStable&&e.slice(0),e.sort(Pe),oe){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)e.splice(n[r],1)}return re=null,e},J=ct.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=J(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[r++];)n+=J(t);return n},(G=ct.selectors={cacheLength:50,createPseudo:ft,match:et,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(ut,st),e[3]=(e[3]||e[4]||e[5]||"").replace(ut,st),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||ct.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&ct.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return et.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&Qe.test(n)&&(t=Z(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(ut,st).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=Be[e+" "];return t||(t=new RegExp("(^|"+je+")"+e+"("+je+"|$)"))&&Be(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==Le&&e.getAttribute("class")||"")})},ATTR:function(e,t,n){return function(r){var o=ct.attr(r,e);return null==o?"!="===t:!t||(o+="","="===t?o===n:"!="===t?o!==n:"^="===t?n&&0===o.indexOf(n):"*="===t?n&&o.indexOf(n)>-1:"$="===t?n&&o.slice(-n.length)===n:"~="===t?(" "+o+" ").indexOf(n)>-1:"|="===t&&(o===n||o.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,o){var i="nth"!==e.slice(0,3),a="last"!==e.slice(-4),u="of-type"===t;return 1===r&&0===o?function(e){return!!e.parentNode}:function(t,n,s){var c,l,f,d,m,p,g=i!==a?"nextSibling":"previousSibling",h=t.parentNode,v=u&&t.nodeName.toLowerCase(),y=!s&&!u;if(h){if(i){for(;g;){for(f=t;f=f[g];)if(u?f.nodeName.toLowerCase()===v:1===f.nodeType)return!1;p=g="only"===e&&!p&&"nextSibling"}return!0}if(p=[a?h.firstChild:h.lastChild],a&&y){for(m=(c=(l=h[Te]||(h[Te]={}))[e]||[])[0]===_e&&c[1],d=c[0]===_e&&c[2],f=m&&h.childNodes[m];f=++m&&f&&f[g]||(d=m=0)||p.pop();)if(1===f.nodeType&&++d&&f===t){l[e]=[_e,m,d];break}}else if(y&&(c=(t[Te]||(t[Te]={}))[e])&&c[0]===_e)d=c[1];else for(;(f=++m&&f&&f[g]||(d=m=0)||p.pop())&&((u?f.nodeName.toLowerCase()!==v:1!==f.nodeType)||!++d||(y&&((f[Te]||(f[Te]={}))[e]=[_e,d]),f!==t)););return(d-=o)===r||d%r==0&&d/r>=0}}},PSEUDO:function(e,t){var n,r=G.pseudos[e]||G.setFilters[e.toLowerCase()]||ct.error("unsupported pseudo: "+e);return r[Te]?r(t):r.length>1?(n=[e,e,"",t],G.setFilters.hasOwnProperty(e.toLowerCase())?ft(function(e,n){for(var o,i=r(e,t),a=i.length;a--;)e[o=He.call(e,i[a])]=!(n[o]=i[a])}):function(e){return r(e,0,n)}):r}},pseudos:{not:ft(function(e){var t=[],n=[],r=ee(e.replace(Xe,"$1"));return r[Te]?ft(function(e,t,n,o){for(var i,a=r(e,null,o,[]),u=e.length;u--;)(i=a[u])&&(e[u]=!(t[u]=i))}):function(e,o,i){return t[0]=e,r(t,null,i,n),!n.pop()}}),has:ft(function(e){return function(t){return ct(e,t).length>0}}),contains:ft(function(e){return e=e.replace(ut,st),function(t){return(t.textContent||t.innerText||J(t)).indexOf(e)>-1}}),lang:ft(function(e){return Ze.test(e||"")||ct.error("unsupported lang: "+e),e=e.replace(ut,st).toLowerCase(),function(t){var n;do{if(n=se?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(e){var t=window.location&&window.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===ue},focus:function(e){return e===ae.activeElement&&(!ae.hasFocus||ae.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!G.pseudos.empty(e)},header:function(e){return nt.test(e.nodeName)},input:function(e){return tt.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:gt(function(){return[0]}),last:gt(function(e,t){return[t-1]}),eq:gt(function(e,t,n){return[n<0?n+t:n]}),even:gt(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:gt(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:gt(function(e,t,n){for(var r=n<0?n+t:n;--r>=0;)e.push(r);return e}),gt:gt(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=G.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})G.pseudos[X]=mt(X);for(X in{submit:!0,reset:!0})G.pseudos[X]=pt(X);function vt(){}function yt(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function bt(e,t,n){var r=t.dir,o=n&&"parentNode"===r,i=Re++;return t.first?function(t,n,i){for(;t=t[r];)if(1===t.nodeType||o)return e(t,n,i)}:function(t,n,a){var u,s,c=[_e,i];if(a){for(;t=t[r];)if((1===t.nodeType||o)&&e(t,n,a))return!0}else for(;t=t[r];)if(1===t.nodeType||o){if((u=(s=t[Te]||(t[Te]={}))[r])&&u[0]===_e&&u[1]===i)return c[2]=u[2];if(s[r]=c,c[2]=e(t,n,a))return!0}}}function Ct(e){return e.length>1?function(t,n,r){for(var o=e.length;o--;)if(!e[o](t,n,r))return!1;return!0}:e[0]}function xt(e,t,n,r,o){for(var i,a=[],u=0,s=e.length,c=null!=t;u<s;u++)(i=e[u])&&(n&&!n(i,r,o)||(a.push(i),c&&t.push(u)));return a}function wt(e,t,n,r,o,i){return r&&!r[Te]&&(r=wt(r)),o&&!o[Te]&&(o=wt(o,i)),ft(function(i,a,u,s){var c,l,f,d=[],m=[],p=a.length,g=i||function(e,t,n){for(var r=0,o=t.length;r<o;r++)ct(e,t[r],n);return n}(t||"*",u.nodeType?[u]:u,[]),h=!e||!i&&t?g:xt(g,d,e,u,s),v=n?o||(i?e:p||r)?[]:a:h;if(n&&n(h,v,u,s),r)for(c=xt(v,m),r(c,[],u,s),l=c.length;l--;)(f=c[l])&&(v[m[l]]=!(h[m[l]]=f));if(i){if(o||e){if(o){for(c=[],l=v.length;l--;)(f=v[l])&&c.push(h[l]=f);o(null,v=[],c,s)}for(l=v.length;l--;)(f=v[l])&&(c=o?He.call(i,f):d[l])>-1&&(i[c]=!(a[c]=f))}}else v=xt(v===a?v.splice(p,v.length):v),o?o(null,a,v,s):qe.apply(a,v)})}function Nt(e){for(var t,n,r,o=e.length,i=G.relative[e[0].type],a=i||G.relative[" "],u=i?1:0,s=bt(function(e){return e===t},a,!0),c=bt(function(e){return He.call(t,e)>-1},a,!0),l=[function(e,n,r){return!i&&(r||n!==ne)||((t=n).nodeType?s(e,n,r):c(e,n,r))}];u<o;u++)if(n=G.relative[e[u].type])l=[bt(Ct(l),n)];else{if((n=G.filter[e[u].type].apply(null,e[u].matches))[Te]){for(r=++u;r<o&&!G.relative[e[r].type];r++);return wt(u>1&&Ct(l),u>1&&yt(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace(Xe,"$1"),n,u<r&&Nt(e.slice(u,r)),r<o&&Nt(e=e.slice(r)),r<o&&yt(e))}l.push(n)}return Ct(l)}vt.prototype=G.filters=G.pseudos,G.setFilters=new vt,Z=ct.tokenize=function(e,t){var n,r,o,i,a,u,s,c=De[e+" "];if(c)return t?0:c.slice(0);for(a=e,u=[],s=G.preFilter;a;){for(i in n&&!(r=Ye.exec(a))||(r&&(a=a.slice(r[0].length)||a),u.push(o=[])),n=!1,(r=Ge.exec(a))&&(n=r.shift(),o.push({value:n,type:r[0].replace(Xe," ")}),a=a.slice(n.length)),G.filter)!(r=et[i].exec(a))||s[i]&&!(r=s[i](r))||(n=r.shift(),o.push({value:n,type:i,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?ct.error(e):De(e,u).slice(0)},ee=ct.compile=function(e,t){var n,r,o,i,a,u,s=[],c=[],l=Oe[e+" "];if(!l){for(t||(t=Z(e)),n=t.length;n--;)(l=Nt(t[n]))[Te]?s.push(l):c.push(l);(l=Oe(e,(r=c,i=(o=s).length>0,a=r.length>0,u=function(e,t,n,u,s){var c,l,f,d=0,m="0",p=e&&[],g=[],h=ne,v=e||a&&G.find.TAG("*",s),y=_e+=null==h?1:Math.random()||.1,b=v.length;for(s&&(ne=t!==ae&&t);m!==b&&null!=(c=v[m]);m++){if(a&&c){for(l=0;f=r[l++];)if(f(c,t,n)){u.push(c);break}s&&(_e=y)}i&&((c=!f&&c)&&d--,e&&p.push(c))}if(d+=m,i&&m!==d){for(l=0;f=o[l++];)f(p,g,t,n);if(e){if(d>0)for(;m--;)p[m]||g[m]||(g[m]=ze.call(u));g=xt(g)}qe.apply(u,g),s&&!e&&g.length>0&&d+o.length>1&&ct.uniqueSort(u)}return s&&(_e=y,ne=h),p},i?ft(u):u))).selector=e}return l},te=ct.select=function(e,t,n,r){var o,i,a,u,s,c="function"==typeof e&&e,l=!r&&Z(e=c.selector||e);if(n=n||[],1===l.length){if((i=l[0]=l[0].slice(0)).length>2&&"ID"===(a=i[0]).type&&Y.getById&&9===t.nodeType&&se&&G.relative[i[1].type]){if(!(t=(G.find.ID(a.matches[0].replace(ut,st),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(i.shift().value.length)}for(o=et.needsContext.test(e)?0:i.length;o--&&(a=i[o],!G.relative[u=a.type]);)if((s=G.find[u])&&(r=s(a.matches[0].replace(ut,st),it.test(i[0].type)&&ht(t.parentNode)||t))){if(i.splice(o,1),!(e=r.length&&yt(i)))return qe.apply(n,r),n;break}}return(c||ee(e,l))(r,t,!se,n,it.test(e)&&ht(t.parentNode)||t),n},Y.sortStable=Te.split("").sort(Pe).join("")===Te,Y.detectDuplicates=!!oe,ie(),Y.sortDetached=!0;var Et=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},St=function(e,t,n){var r,o;if(!e)return 0;if(n=n||e,e.length!==undefined){for(r=0,o=e.length;r<o;r++)if(!1===t.call(n,e[r],r,e))return 0}else for(r in e)if(e.hasOwnProperty(r)&&!1===t.call(n,e[r],r,e))return 0;return 1},kt=function(e,t,n){var r,o;for(r=0,o=e.length;r<o;r++)if(t.call(n,e[r],r,e))return r;return-1},Tt={isArray:Et,toArray:function(e){var t,n,r=e;if(!Et(e))for(r=[],t=0,n=e.length;t<n;t++)r[t]=e[t];return r},each:St,map:function(e,t){var n=[];return St(e,function(r,o){n.push(t(r,o,e))}),n},filter:function(e,t){var n=[];return St(e,function(r,o){t&&!t(r,o,e)||n.push(r)}),n},indexOf:function(e,t){var n,r;if(e)for(n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},reduce:function(e,t,n,r){var o=0;for(arguments.length<3&&(n=e[0]);o<e.length;o++)n=t.call(r,n,e[o],o);return n},findIndex:kt,find:function(e,t,n){var r=kt(e,t,n);return-1!==r?e[r]:undefined},last:function(e){return e[e.length-1]}},At=/^\s*|\s*$/g,_t=function(e){return null===e||e===undefined?"":(""+e).replace(At,"")},Rt=function(e,t){return t?!("array"!==t||!Tt.isArray(e))||typeof e===t:e!==undefined},Bt=function(e,t,n,r){r=r||this,e&&(n&&(e=e[n]),Tt.each(e,function(e,o){if(!1===t.call(r,e,o,n))return!1;Bt(e,t,n,r)}))},Dt={trim:_t,isArray:Tt.isArray,is:Rt,toArray:Tt.toArray,makeMap:function(e,t,n){var r;for(t=t||",","string"==typeof(e=e||[])&&(e=e.split(t)),n=n||{},r=e.length;r--;)n[e[r]]={};return n},each:Tt.each,map:Tt.map,grep:Tt.filter,inArray:Tt.indexOf,hasOwn:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},extend:function(e,t){for(var n,r,o,i=[],a=2;a<arguments.length;a++)i[a-2]=arguments[a];var u,s=arguments;for(n=1,r=s.length;n<r;n++)for(o in t=s[n])t.hasOwnProperty(o)&&(u=t[o])!==undefined&&(e[o]=u);return e},create:function(e,t,n){var r,o,i,a,u,s=this,c=0;if(e=/^((static) )?([\w.]+)(:([\w.]+))?/.exec(e),i=e[3].match(/(^|\.)(\w+)$/i)[2],!(o=s.createNS(e[3].replace(/\.\w+$/,""),n))[i]){if("static"===e[2])return o[i]=t,void(this.onCreate&&this.onCreate(e[2],e[3],o[i]));t[i]||(t[i]=function(){},c=1),o[i]=t[i],s.extend(o[i].prototype,t),e[5]&&(r=s.resolve(e[5]).prototype,a=e[5].match(/\.(\w+)$/i)[1],u=o[i],o[i]=c?function(){return r[a].apply(this,arguments)}:function(){return this.parent=r[a],u.apply(this,arguments)},o[i].prototype[i]=o[i],s.each(r,function(e,t){o[i].prototype[t]=r[t]}),s.each(t,function(e,t){r[t]?o[i].prototype[t]=function(){return this.parent=r[t],e.apply(this,arguments)}:t!==i&&(o[i].prototype[t]=e)})),s.each(t["static"],function(e,t){o[i][t]=e})}},walk:Bt,createNS:function(e,t){var n,r;for(t=t||window,e=e.split("."),n=0;n<e.length;n++)t[r=e[n]]||(t[r]={}),t=t[r];return t},resolve:function(e,t){var n,r;for(t=t||window,n=0,r=(e=e.split(".")).length;n<r&&(t=t[e[n]]);n++);return t},explode:function(e,t){return!e||Rt(e,"array")?e:Tt.map(e.split(t||","),_t)},_addCacheSuffix:function(e){var t=de.cacheSuffix;return t&&(e+=(-1===e.indexOf("?")?"?":"&")+t),e}},Ot=document,Pt=Array.prototype.push,Lt=Array.prototype.slice,It=/^(?:[^#<]*(<[\w\W]+>)[^>]*$|#([\w\-]*)$)/,Mt=ke.Event,Ft=Dt.makeMap("children,contents,next,prev"),zt=function(e){return void 0!==e},Ut=function(e){return"string"==typeof e},qt=function(e,t){var n,r,o;for(o=(t=t||Ot).createElement("div"),n=t.createDocumentFragment(),o.innerHTML=e;r=o.firstChild;)n.appendChild(r);return n},Vt=function(e,t,n,r){var o;if(Ut(t))t=qt(t,nn(e[0]));else if(t.length&&!t.nodeType){if(t=Jt.makeArray(t),r)for(o=t.length-1;o>=0;o--)Vt(e,t[o],n,r);else for(o=0;o<t.length;o++)Vt(e,t[o],n,r);return e}if(t.nodeType)for(o=e.length;o--;)n.call(e[o],t);return e},Ht=function(e,t){return e&&t&&-1!==(" "+e.className+" ").indexOf(" "+t+" ")},jt=function(e,t,n){var r,o;return t=Jt(t)[0],e.each(function(){var e=this;n&&r===e.parentNode?o.appendChild(e):(r=e.parentNode,o=t.cloneNode(!1),e.parentNode.insertBefore(o,e),o.appendChild(e))}),e},$t=Dt.makeMap("fillOpacity fontWeight lineHeight opacity orphans widows zIndex zoom"," "),Wt=Dt.makeMap("checked compact declare defer disabled ismap multiple nohref noshade nowrap readonly selected"," "),Kt={"for":"htmlFor","class":"className",readonly:"readOnly"},Xt={"float":"cssFloat"},Yt={},Gt={},Jt=function(e,t){return new Jt.fn.init(e,t)},Qt=/^\s*|\s*$/g,Zt=function(e){return null===e||e===undefined?"":(""+e).replace(Qt,"")},en=function(e,t){var n,r,o,i;if(e)if((n=e.length)===undefined){for(r in e)if(e.hasOwnProperty(r)&&(i=e[r],!1===t.call(i,r,i)))break}else for(o=0;o<n&&(i=e[o],!1!==t.call(i,o,i));o++);return e},tn=function(e,t){var n=[];return en(e,function(e,r){t(r,e)&&n.push(r)}),n},nn=function(e){return e?9===e.nodeType?e:e.ownerDocument:Ot};Jt.fn=Jt.prototype={constructor:Jt,selector:"",context:null,length:0,init:function(e,t){var n,r,o=this;if(!e)return o;if(e.nodeType)return o.context=o[0]=e,o.length=1,o;if(t&&t.nodeType)o.context=t;else{if(t)return Jt(e).attr(t);o.context=t=document}if(Ut(e)){if(o.selector=e,!(n="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:It.exec(e)))return Jt(t).find(e);if(n[1])for(r=qt(e,nn(t)).firstChild;r;)Pt.call(o,r),r=r.nextSibling;else{if(!(r=nn(t).getElementById(n[2])))return o;if(r.id!==n[2])return o.find(e);o.length=1,o[0]=r}}else this.add(e,!1);return o},toArray:function(){return Dt.toArray(this)},add:function(e,t){var n,r,o=this;if(Ut(e))return o.add(Jt(e));if(!1!==t)for(n=Jt.unique(o.toArray().concat(Jt.makeArray(e))),o.length=n.length,r=0;r<n.length;r++)o[r]=n[r];else Pt.apply(o,Jt.makeArray(e));return o},attr:function(e,t){var n,r=this;if("object"==typeof e)en(e,function(e,t){r.attr(e,t)});else{if(!zt(t)){if(r[0]&&1===r[0].nodeType){if((n=Yt[e])&&n.get)return n.get(r[0],e);if(Wt[e])return r.prop(e)?e:undefined;null===(t=r[0].getAttribute(e,2))&&(t=undefined)}return t}this.each(function(){var n;if(1===this.nodeType){if((n=Yt[e])&&n.set)return void n.set(this,t);null===t?this.removeAttribute(e,2):this.setAttribute(e,t,2)}})}return r},removeAttr:function(e){return this.attr(e,null)},prop:function(e,t){var n=this;if("object"==typeof(e=Kt[e]||e))en(e,function(e,t){n.prop(e,t)});else{if(!zt(t))return n[0]&&n[0].nodeType&&e in n[0]?n[0][e]:t;this.each(function(){1===this.nodeType&&(this[e]=t)})}return n},css:function(e,t){var n,r,o=this,i=function(e){return e.replace(/-(\D)/g,function(e,t){return t.toUpperCase()})},a=function(e){return e.replace(/[A-Z]/g,function(e){return"-"+e})};if("object"==typeof e)en(e,function(e,t){o.css(e,t)});else if(zt(t))e=i(e),"number"!=typeof t||$t[e]||(t=t.toString()+"px"),o.each(function(){var n=this.style;if((r=Gt[e])&&r.set)r.set(this,t);else{try{this.style[Xt[e]||e]=t}catch(o){}null!==t&&""!==t||(n.removeProperty?n.removeProperty(a(e)):n.removeAttribute(e))}});else{if(n=o[0],(r=Gt[e])&&r.get)return r.get(n);if(n.ownerDocument.defaultView)try{return n.ownerDocument.defaultView.getComputedStyle(n,null).getPropertyValue(a(e))}catch(u){return undefined}else if(n.currentStyle)return n.currentStyle[i(e)]}return o},remove:function(){for(var e,t=this.length;t--;)e=this[t],Mt.clean(e),e.parentNode&&e.parentNode.removeChild(e);return this},empty:function(){for(var e,t=this.length;t--;)for(e=this[t];e.firstChild;)e.removeChild(e.firstChild);return this},html:function(e){var t,n=this;if(zt(e)){t=n.length;try{for(;t--;)n[t].innerHTML=e}catch(r){Jt(n[t]).empty().append(e)}return n}return n[0]?n[0].innerHTML:""},text:function(e){var t,n=this;if(zt(e)){for(t=n.length;t--;)"innerText"in n[t]?n[t].innerText=e:n[0].textContent=e;return n}return n[0]?n[0].innerText||n[0].textContent:""},append:function(){return Vt(this,arguments,function(e){(1===this.nodeType||this.host&&1===this.host.nodeType)&&this.appendChild(e)})},prepend:function(){return Vt(this,arguments,function(e){(1===this.nodeType||this.host&&1===this.host.nodeType)&&this.insertBefore(e,this.firstChild)},!0)},before:function(){return this[0]&&this[0].parentNode?Vt(this,arguments,function(e){this.parentNode.insertBefore(e,this)}):this},after:function(){return this[0]&&this[0].parentNode?Vt(this,arguments,function(e){this.parentNode.insertBefore(e,this.nextSibling)},!0):this},appendTo:function(e){return Jt(e).append(this),this},prependTo:function(e){return Jt(e).prepend(this),this},replaceWith:function(e){return this.before(e).remove()},wrap:function(e){return jt(this,e)},wrapAll:function(e){return jt(this,e,!0)},wrapInner:function(e){return this.each(function(){Jt(this).contents().wrapAll(e)}),this},unwrap:function(){return this.parent().each(function(){Jt(this).replaceWith(this.childNodes)})},clone:function(){var e=[];return this.each(function(){e.push(this.cloneNode(!0))}),Jt(e)},addClass:function(e){return this.toggleClass(e,!0)},removeClass:function(e){return this.toggleClass(e,!1)},toggleClass:function(e,t){var n=this;return"string"!=typeof e?n:(-1!==e.indexOf(" ")?en(e.split(" "),function(){n.toggleClass(this,t)}):n.each(function(n,r){var o,i;(i=Ht(r,e))!==t&&(o=r.className,i?r.className=Zt((" "+o+" ").replace(" "+e+" "," ")):r.className+=o?" "+e:e)}),n)},hasClass:function(e){return Ht(this[0],e)},each:function(e){return en(this,e)},on:function(e,t){return this.each(function(){Mt.bind(this,e,t)})},off:function(e,t){return this.each(function(){Mt.unbind(this,e,t)})},trigger:function(e){return this.each(function(){"object"==typeof e?Mt.fire(this,e.type,e):Mt.fire(this,e)})},show:function(){return this.css("display","")},hide:function(){return this.css("display","none")},slice:function(){return new Jt(Lt.apply(this,arguments))},eq:function(e){return-1===e?this.slice(e):this.slice(e,+e+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},find:function(e){var t,n,r=[];for(t=0,n=this.length;t<n;t++)Jt.find(e,this[t],r);return Jt(r)},filter:function(e){return Jt("function"==typeof e?tn(this.toArray(),function(t,n){return e(n,t)}):Jt.filter(e,this.toArray()))},closest:function(e){var t=[];return e instanceof Jt&&(e=e[0]),this.each(function(n,r){for(;r;){if("string"==typeof e&&Jt(r).is(e)){t.push(r);break}if(r===e){t.push(r);break}r=r.parentNode}}),Jt(t)},offset:function(e){var t,n,r,o,i=0,a=0;return e?this.css(e):((t=this[0])&&(r=(n=t.ownerDocument).documentElement,t.getBoundingClientRect&&(i=(o=t.getBoundingClientRect()).left+(r.scrollLeft||n.body.scrollLeft)-r.clientLeft,a=o.top+(r.scrollTop||n.body.scrollTop)-r.clientTop)),{left:i,top:a})},push:Pt,sort:[].sort,splice:[].splice},Dt.extend(Jt,{extend:Dt.extend,makeArray:function(e){return(t=e)&&t===t.window||e.nodeType?[e]:Dt.toArray(e);var t},inArray:function(e,t){var n;if(t.indexOf)return t.indexOf(e);for(n=t.length;n--;)if(t[n]===e)return n;return-1},isArray:Dt.isArray,each:en,trim:Zt,grep:tn,find:ct,expr:ct.selectors,unique:ct.uniqueSort,text:ct.getText,contains:ct.contains,filter:function(e,t,n){var r=t.length;for(n&&(e=":not("+e+")");r--;)1!==t[r].nodeType&&t.splice(r,1);return t=1===t.length?Jt.find.matchesSelector(t[0],e)?[t[0]]:[]:Jt.find.matches(e,t)}});var rn=function(e,t,n){var r=[],o=e[t];for("string"!=typeof n&&n instanceof Jt&&(n=n[0]);o&&9!==o.nodeType;){if(n!==undefined){if(o===n)break;if("string"==typeof n&&Jt(o).is(n))break}1===o.nodeType&&r.push(o),o=o[t]}return r},on=function(e,t,n,r){var o=[];for(r instanceof Jt&&(r=r[0]);e;e=e[t])if(!n||e.nodeType===n){if(r!==undefined){if(e===r)break;if("string"==typeof r&&Jt(e).is(r))break}o.push(e)}return o},an=function(e,t,n){for(e=e[t];e;e=e[t])if(e.nodeType===n)return e;return null};en({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return rn(e,"parentNode")},next:function(e){return an(e,"nextSibling",1)},prev:function(e){return an(e,"previousSibling",1)},children:function(e){return on(e.firstChild,"nextSibling",1)},contents:function(e){return Dt.toArray(("iframe"===e.nodeName?e.contentDocument||e.contentWindow.document:e).childNodes)}},function(e,t){Jt.fn[e]=function(n){var r=[];return this.each(function(){var e=t.call(r,this,n,r);e&&(Jt.isArray(e)?r.push.apply(r,e):r.push(e))}),this.length>1&&(Ft[e]||(r=Jt.unique(r)),0===e.indexOf("parents")&&(r=r.reverse())),r=Jt(r),n?r.filter(n):r}}),en({parentsUntil:function(e,t){return rn(e,"parentNode",t)},nextUntil:function(e,t){return on(e,"nextSibling",1,t).slice(1)},prevUntil:function(e,t){return on(e,"previousSibling",1,t).slice(1)}},function(e,t){Jt.fn[e]=function(n,r){var o=[];return this.each(function(){var e=t.call(o,this,n,o);e&&(Jt.isArray(e)?o.push.apply(o,e):o.push(e))}),this.length>1&&(o=Jt.unique(o),0!==e.indexOf("parents")&&"prevUntil"!==e||(o=o.reverse())),o=Jt(o),r?o.filter(r):o}}),Jt.fn.is=function(e){return!!e&&this.filter(e).length>0},Jt.fn.init.prototype=Jt.fn,Jt.overrideDefaults=function(e){var t,n=function(r,o){return t=t||e(),0===arguments.length&&(r=t.element),o||(o=t.context),new n.fn.init(r,o)};return Jt.extend(n,this),n};var un=function(e,t,n){en(n,function(n,r){e[n]=e[n]||{},e[n][t]=r})};de.ie&&de.ie<8&&(un(Yt,"get",{maxlength:function(e){var t=e.maxLength;return 2147483647===t?undefined:t},size:function(e){var t=e.size;return 20===t?undefined:t},"class":function(e){return e.className},style:function(e){var t=e.style.cssText;return 0===t.length?undefined:t}}),un(Yt,"set",{"class":function(e,t){e.className=t},style:function(e,t){e.style.cssText=t}})),de.ie&&de.ie<9&&(Xt["float"]="styleFloat",un(Gt,"set",{opacity:function(e,t){var n=e.style;null===t||""===t?n.removeAttribute("filter"):(n.zoom=1,n.filter="alpha(opacity="+100*t+")")}})),Jt.attrHooks=Yt,Jt.cssHooks=Gt;var sn,cn=function(e){var t,n=!1;return function(){return n||(n=!0,t=e.apply(null,arguments)),t}},ln=function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.test(t))return r}return undefined}(e,t);if(!n)return{major:0,minor:0};var r=function(e){return Number(t.replace(n,"$"+e))};return dn(r(1),r(2))},fn=function(){return dn(0,0)},dn=function(e,t){return{major:e,minor:t}},mn={nu:dn,detect:function(e,t){var n=String(t).toLowerCase();return 0===e.length?fn():ln(e,n)},unknown:fn},pn="Firefox",gn=function(e,t){return function(){return t===e}},hn=function(e){var t=e.current;return{current:t,version:e.version,isEdge:gn("Edge",t),isChrome:gn("Chrome",t),isIE:gn("IE",t),isOpera:gn("Opera",t),isFirefox:gn(pn,t),isSafari:gn("Safari",t)}},vn={unknown:function(){return hn({current:undefined,version:mn.unknown()})},nu:hn,edge:y.constant("Edge"),chrome:y.constant("Chrome"),ie:y.constant("IE"),opera:y.constant("Opera"),firefox:y.constant(pn),safari:y.constant("Safari")},yn="Windows",bn="Android",Cn="Solaris",xn="FreeBSD",wn=function(e,t){return function(){return t===e}},Nn=function(e){var t=e.current;return{current:t,version:e.version,isWindows:wn(yn,t),isiOS:wn("iOS",t),isAndroid:wn(bn,t),isOSX:wn("OSX",t),isLinux:wn("Linux",t),isSolaris:wn(Cn,t),isFreeBSD:wn(xn,t)}},En={unknown:function(){return Nn({current:undefined,version:mn.unknown()})},nu:Nn,windows:y.constant(yn),ios:y.constant("iOS"),android:y.constant(bn),linux:y.constant("Linux"),osx:y.constant("OSX"),solaris:y.constant(Cn),freebsd:y.constant(xn)},Sn=function(e,t){var n=String(t).toLowerCase();return M.find(e,function(e){return e.search(n)})},kn=function(e,t){return Sn(e,t).map(function(e){var n=mn.detect(e.versionRegexes,t);return{current:e.name,version:n}})},Tn=function(e,t){return Sn(e,t).map(function(e){var n=mn.detect(e.versionRegexes,t);return{current:e.name,version:n}})},An=function(e,t){return-1!==e.indexOf(t)},_n=function(e){return e.replace(/^\s+|\s+$/g,"")},Rn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Bn=function(e){return function(t){return An(t,e)}},Dn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return An(e,"edge/")&&An(e,"chrome")&&An(e,"safari")&&An(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Rn],search:function(e){return An(e,"chrome")&&!An(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return An(e,"msie")||An(e,"trident")}},{name:"Opera",versionRegexes:[Rn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Bn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Bn("firefox")},{name:"Safari",versionRegexes:[Rn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(An(e,"safari")||An(e,"mobile/"))&&An(e,"applewebkit")}}],On=[{name:"Windows",search:Bn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return An(e,"iphone")||An(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Bn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Bn("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Bn("linux"),versionRegexes:[]},{name:"Solaris",search:Bn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Bn("freebsd"),versionRegexes:[]}],Pn={browsers:y.constant(Dn),oses:y.constant(On)},Ln=function(e){var t,n,r,o,i,a,u,s,c,l,f,d=Pn.browsers(),m=Pn.oses(),p=kn(d,e).fold(vn.unknown,vn.nu),g=Tn(m,e).fold(En.unknown,En.nu);return{browser:p,os:g,deviceType:(n=p,r=e,o=(t=g).isiOS()&&!0===/ipad/i.test(r),i=t.isiOS()&&!o,a=t.isAndroid()&&3===t.version.major,u=t.isAndroid()&&4===t.version.major,s=o||a||u&&!0===/mobile/i.test(r),c=t.isiOS()||t.isAndroid(),l=c&&!s,f=n.isSafari()&&t.isiOS()&&!1===/safari/i.test(r),{isiPad:y.constant(o),isiPhone:y.constant(i),isTablet:y.constant(s),isPhone:y.constant(l),isTouch:y.constant(c),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:y.constant(f)})}},In={detect:cn(function(){var e=navigator.userAgent;return Ln(e)})},Mn=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:y.constant(e)}},Fn={fromHtml:function(e,t){var n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||n.childNodes.length>1)throw console.error("HTML does not have a single root node",e),"HTML must have a single root node";return Mn(n.childNodes[0])},fromTag:function(e,t){var n=(t||document).createElement(e);return Mn(n)},fromText:function(e,t){var n=(t||document).createTextNode(e);return Mn(n)},fromDom:Mn,fromPoint:function(e,t,n){return E.from(e.dom().elementFromPoint(t,n)).map(Mn)}},zn=8,Un=9,qn=1,Vn=3,Hn=function(e){return e.dom().nodeName.toLowerCase()},jn=function(e){return e.dom().nodeType},$n=function(e){return function(t){return jn(t)===e}},Wn=$n(qn),Kn=$n(Vn),Xn=$n(Un),Yn={name:Hn,type:jn,value:function(e){return e.dom().nodeValue},isElement:Wn,isText:Kn,isDocument:Xn,isComment:function(e){return jn(e)===zn||"#comment"===Hn(e)}},Gn=function(e){return function(t){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(t)===e}},Jn={isString:Gn("string"),isObject:Gn("object"),isArray:Gn("array"),isNull:Gn("null"),isBoolean:Gn("boolean"),isUndefined:Gn("undefined"),isFunction:Gn("function"),isNumber:Gn("number")},Qn=(sn=Object.keys)===undefined?function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t}:sn,Zn=function(e,t){for(var n=Qn(e),r=0,o=n.length;r<o;r++){var i=n[r];t(e[i],i,e)}},er=function(e,t){var n={};return Zn(e,function(r,o){var i=t(r,o,e);n[i.k]=i.v}),n},tr=function(e,t){var n=[];return Zn(e,function(e,r){n.push(t(e,r))}),n},nr=function(e){return tr(e,function(e){return e})},rr={bifilter:function(e,t){var n={},r={};return Zn(e,function(e,o){(t(e,o)?n:r)[o]=e}),{t:n,f:r}},each:Zn,map:function(e,t){return er(e,function(e,n,r){return{k:n,v:t(e,n,r)}})},mapToArray:tr,tupleMap:er,find:function(e,t){for(var n=Qn(e),r=0,o=n.length;r<o;r++){var i=n[r],a=e[i];if(t(a,i,e))return E.some(a)}return E.none()},keys:Qn,values:nr,size:function(e){return nr(e).length}},or=function(e,t,n){if(!(Jn.isString(n)||Jn.isBoolean(n)||Jn.isNumber(n)))throw console.error("Invalid call to Attr.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},ir=function(e,t,n){or(e.dom(),t,n)},ar=function(e,t){var n=e.dom().getAttribute(t);return null===n?undefined:n},ur=function(e,t){var n=e.dom();return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},sr={clone:function(e){return M.foldl(e.dom().attributes,function(e,t){return e[t.name]=t.value,e},{})},set:ir,setAll:function(e,t){var n=e.dom();rr.each(t,function(e,t){or(n,t,e)})},get:ar,has:ur,remove:function(e,t){e.dom().removeAttribute(t)},hasNone:function(e){var t=e.dom().attributes;return t===undefined||null===t||0===t.length},transfer:function(e,t,n){Yn.isElement(e)&&Yn.isElement(t)&&M.each(n,function(n){var r,o,i;o=t,ur(r=e,i=n)&&!ur(o,i)&&ir(o,i,ar(r,i))})}},cr=cn(function(){return lr(Fn.fromDom(document))}),lr=function(e){var t=e.dom().body;if(null===t||t===undefined)throw"Body is not available yet";return Fn.fromDom(t)},fr={body:cr,getBody:lr,inBody:function(e){var t=Yn.isText(e)?e.dom().parentNode:e.dom();return t!==undefined&&null!==t&&t.ownerDocument.body.contains(t)}},dr=function(e){return e.style!==undefined},mr=function(e,t,n){if(!Jn.isString(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);dr(e)&&e.style.setProperty(t,n)},pr=function(e,t){return dr(e)?e.style.getPropertyValue(t):""},gr=function(e,t){var n=e.dom();rr.each(t,function(e,t){mr(n,t,e)})},hr=function(e,t){var n=e.dom(),r=window.getComputedStyle(n).getPropertyValue(t),o=""!==r||fr.inBody(e)?r:pr(n,t);return null===o?undefined:o},vr=function(e){return e.slice(0).sort()},yr={sort:vr,reqMessage:function(e,t){throw new Error("All required keys ("+vr(e).join(", ")+") were not specified. Specified keys were: "+vr(t).join(", ")+".")},unsuppMessage:function(e){throw new Error("Unsupported keys for object: "+vr(e).join(", "))},validateStrArr:function(e,t){if(!Jn.isArray(t))throw new Error("The "+e+" fields must be an array. Was: "+t+".");M.each(t,function(t){if(!Jn.isString(t))throw new Error("The value "+t+" in the "+e+" fields was not a string.")})},invalidTypeMessage:function(e,t){throw new Error("All values need to be of type: "+t+". Keys ("+vr(e).join(", ")+") were not.")},checkDupes:function(e){var t=vr(e);M.find(t,function(e,n){return n<t.length-1&&e===t[n+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})}},br={immutable:function(){var e=arguments;return function(){for(var t=new Array(arguments.length),n=0;n<t.length;n++)t[n]=arguments[n];if(e.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+e.length+']", got '+t.length+" arguments");var r={};return M.each(e,function(e,n){r[e]=y.constant(t[n])}),r}},immutableBag:function(e,t){var n=e.concat(t);if(0===n.length)throw new Error("You must specify at least one required or optional field.");return yr.validateStrArr("required",e),yr.validateStrArr("optional",t),yr.checkDupes(n),function(r){var o=rr.keys(r);M.forall(e,function(e){return M.contains(o,e)})||yr.reqMessage(e,o);var i=M.filter(o,function(e){return!M.contains(n,e)});i.length>0&&yr.unsuppMessage(i);var a={};return M.each(e,function(e){a[e]=y.constant(r[e])}),M.each(t,function(e){a[e]=y.constant(Object.prototype.hasOwnProperty.call(r,e)?E.some(r[e]):E.none())}),a}}},Cr=function(e,t){for(var n=[],r=function(e){return n.push(e),t(e)},o=t(e);(o=o.bind(r)).isSome(););return n},xr=function(){return q.getOrDie("Node")},wr=function(e,t,n){return 0!=(e.compareDocumentPosition(t)&n)},Nr=function(e,t){return wr(e,t,xr().DOCUMENT_POSITION_CONTAINED_BY)},Er=qn,Sr=Un,kr=function(e){return e.nodeType!==Er&&e.nodeType!==Sr||0===e.childElementCount},Tr={all:function(e,t){var n=t===undefined?document:t.dom();return kr(n)?[]:M.map(n.querySelectorAll(e),Fn.fromDom)},is:function(e,t){var n=e.dom();if(n.nodeType!==Er)return!1;if(n.matches!==undefined)return n.matches(t);if(n.msMatchesSelector!==undefined)return n.msMatchesSelector(t);if(n.webkitMatchesSelector!==undefined)return n.webkitMatchesSelector(t);if(n.mozMatchesSelector!==undefined)return n.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},one:function(e,t){var n=t===undefined?document:t.dom();return kr(n)?E.none():E.from(n.querySelector(e)).map(Fn.fromDom)}},Ar=function(e,t){return e.dom()===t.dom()},_r=In.detect().browser.isIE()?function(e,t){return Nr(e.dom(),t.dom())}:function(e,t){var n=e.dom(),r=t.dom();return n!==r&&n.contains(r)},Rr={eq:Ar,isEqualNode:function(e,t){return e.dom().isEqualNode(t.dom())},member:function(e,t){return M.exists(t,y.curry(Ar,e))},contains:_r,is:Tr.is},Br=function(e){return Fn.fromDom(e.dom().ownerDocument)},Dr=function(e){var t=e.dom();return E.from(t.parentNode).map(Fn.fromDom)},Or=function(e){var t=e.dom();return E.from(t.previousSibling).map(Fn.fromDom)},Pr=function(e){var t=e.dom();return E.from(t.nextSibling).map(Fn.fromDom)},Lr=function(e){var t=e.dom();return M.map(t.childNodes,Fn.fromDom)},Ir=function(e,t){var n=e.dom().childNodes;return E.from(n[t]).map(Fn.fromDom)},Mr=br.immutable("element","offset"),Fr={owner:Br,defaultView:function(e){var t=e.dom().ownerDocument.defaultView;return Fn.fromDom(t)},documentElement:function(e){var t=Br(e);return Fn.fromDom(t.dom().documentElement)},parent:Dr,findIndex:function(e){return Dr(e).bind(function(t){var n=Lr(t);return M.findIndex(n,function(t){return Rr.eq(e,t)})})},parents:function(e,t){for(var n=Jn.isFunction(t)?t:y.constant(!1),r=e.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,a=Fn.fromDom(i);if(o.push(a),!0===n(a))break;r=i}return o},siblings:function(e){return Dr(e).map(Lr).map(function(t){return M.filter(t,function(t){return!Rr.eq(e,t)})}).getOr([])},prevSibling:Or,offsetParent:function(e){var t=e.dom();return E.from(t.offsetParent).map(Fn.fromDom)},prevSiblings:function(e){return M.reverse(Cr(e,Or))},nextSibling:Pr,nextSiblings:function(e){return Cr(e,Pr)},children:Lr,child:Ir,firstChild:function(e){return Ir(e,0)},lastChild:function(e){return Ir(e,e.dom().childNodes.length-1)},childNodesCount:function(e){return e.dom().childNodes.length},hasChildNodes:function(e){return e.dom().hasChildNodes()},leaf:function(e,t){var n=Lr(e);return n.length>0&&t<n.length?Mr(n[t],0):Mr(e,t)}},zr=In.detect().browser,Ur=function(e){return M.find(e,Yn.isElement)},qr=function(e,t,n){var r,o,i,a=0,u=0,s=e.ownerDocument;if(n=n||e,t){if(n===e&&t.getBoundingClientRect&&"static"===hr(Fn.fromDom(e),"position"))return{x:a=(o=t.getBoundingClientRect()).left+(s.documentElement.scrollLeft||e.scrollLeft)-s.documentElement.clientLeft,y:u=o.top+(s.documentElement.scrollTop||e.scrollTop)-s.documentElement.clientTop};for(r=t;r&&r!==n&&r.nodeType;)a+=r.offsetLeft||0,u+=r.offsetTop||0,r=r.offsetParent;for(r=t.parentNode;r&&r!==n&&r.nodeType;)a-=r.scrollLeft||0,u-=r.scrollTop||0,r=r.parentNode;u+=(i=Fn.fromDom(t),zr.isFirefox()&&"table"===Yn.name(i)?Ur(Fr.children(i)).filter(function(e){return"caption"===Yn.name(e)}).bind(function(e){return Ur(Fr.nextSiblings(e)).map(function(t){var n=t.dom().offsetTop,r=e.dom().offsetTop,o=e.dom().offsetHeight;return n<=r?-o:0})}).getOr(0):0)}return{x:a,y:u}},Vr=function(e){var t=E.none(),n=[],r=function(e){o()?a(e):n.push(e)},o=function(){return t.isSome()},i=function(e){M.each(e,a)},a=function(e){t.each(function(t){setTimeout(function(){e(t)},0)})};return e(function(e){t=E.some(e),i(n),n=[]}),{get:r,map:function(e){return Vr(function(t){r(function(n){t(e(n))})})},isReady:o}},Hr={nu:Vr,pure:function(e){return Vr(function(t){t(e)})}},jr=function(e){return function(){var t=Array.prototype.slice.call(arguments),n=this;setTimeout(function(){e.apply(n,t)},0)}},$r=function(e){var t=function(t){e(jr(t))};return{map:function(e){return $r(function(n){t(function(t){var r=e(t);n(r)})})},bind:function(e){return $r(function(n){t(function(t){e(t).get(n)})})},anonBind:function(e){return $r(function(n){t(function(t){e.get(n)})})},toLazy:function(){return Hr.nu(t)},get:t}},Wr={nu:$r,pure:function(e){return $r(function(t){t(e)})}},Kr=function(e,t){return t(function(t){var n=[],r=0;0===e.length?t([]):M.each(e,function(o,i){var a;o.get((a=i,function(o){n[a]=o,++r>=e.length&&t(n)}))})})},Xr=function(e){return Kr(e,Wr.nu)},Yr={par:Xr,mapM:function(e,t){var n=M.map(e,t);return Xr(n)},compose:function(e,t){return function(n){return t(n).bind(e)}}},Gr=function(e){return{is:function(t){return e===t},isValue:y.always,isError:y.never,getOr:y.constant(e),getOrThunk:y.constant(e),getOrDie:y.constant(e),or:function(t){return Gr(e)},orThunk:function(t){return Gr(e)},fold:function(t,n){return n(e)},map:function(t){return Gr(t(e))},each:function(t){t(e)},bind:function(t){return t(e)},exists:function(t){return t(e)},forall:function(t){return t(e)},toOption:function(){return E.some(e)}}},Jr=function(e){return{is:y.never,isValue:y.never,isError:y.always,getOr:y.identity,getOrThunk:function(e){return e()},getOrDie:function(){return y.die(e)()},or:function(e){return e},orThunk:function(e){return e()},fold:function(t,n){return t(e)},map:function(t){return Jr(e)},each:y.noop,bind:function(t){return Jr(e)},exists:y.never,forall:y.always,toOption:E.none}},Qr={value:Gr,error:Jr};function Zr(e,t){var n=e,r=function(e,n,r,o){var i,a;if(e){if(!o&&e[n])return e[n];if(e!==t){if(i=e[r])return i;for(a=e.parentNode;a&&a!==t;a=a.parentNode)if(i=a[r])return i}}};this.current=function(){return n},this.next=function(e){return n=r(n,"firstChild","nextSibling",e)},this.prev=function(e){return n=r(n,"lastChild","previousSibling",e)},this.prev2=function(e){return n=function(e,n,r,o){var i,a,u;if(e){if(i=e[r],t&&i===t)return;if(i){if(!o)for(u=i[n];u;u=u[n])if(!u[n])return u;return i}if((a=e.parentNode)&&a!==t)return a}}(n,"lastChild","previousSibling",e)}}var eo,to,no,ro=function(e){var t;return function(n){return(t=t||M.mapToObject(e,y.constant(!0))).hasOwnProperty(Yn.name(n))}},oo=ro(["h1","h2","h3","h4","h5","h6"]),io=ro(["article","aside","details","div","dt","figcaption","footer","form","fieldset","header","hgroup","html","main","nav","section","summary","body","p","dl","multicol","dd","figure","address","center","blockquote","h1","h2","h3","h4","h5","h6","listing","xmp","pre","plaintext","menu","dir","ul","ol","li","hr","table","tbody","thead","tfoot","th","tr","td","caption"]),ao=function(e){return Yn.isElement(e)&&!io(e)},uo=function(e){return Yn.isElement(e)&&"br"===Yn.name(e)},so=ro(["h1","h2","h3","h4","h5","h6","p","div","address","pre","form","blockquote","center","dir","fieldset","header","footer","article","section","hgroup","aside","nav","figure"]),co=ro(["ul","ol","dl"]),lo=ro(["li","dd","dt"]),fo=ro(["area","base","basefont","br","col","frame","hr","img","input","isindex","link","meta","param","embed","source","wbr","track"]),mo=ro(["thead","tbody","tfoot"]),po=ro(["td","th"]),go=function(e){return function(t){return!!t&&t.nodeType===e}},ho=go(1),vo=function(e){var t=e.toLowerCase().split(" ");return function(e){var n,r;if(e&&e.nodeType)for(r=e.nodeName.toLowerCase(),n=0;n<t.length;n++)if(r===t[n])return!0;return!1}},yo=function(e){return function(t){if(ho(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1}},bo=go(3),Co=go(8),xo=go(9),wo=vo("br"),No=yo("true"),Eo=yo("false"),So={isText:bo,isElement:ho,isComment:Co,isDocument:xo,isBr:wo,isContentEditableTrue:No,isContentEditableFalse:Eo,matchNodeNames:vo,hasPropValue:function(e,t){return function(n){return ho(n)&&n[e]===t}},hasAttribute:function(e,t){return function(t){return ho(t)&&t.hasAttribute(e)}},hasAttributeValue:function(e,t){return function(n){return ho(n)&&n.getAttribute(e)===t}},matchStyleValues:function(e,t){var n=t.toLowerCase().split(" ");return function(t){var r;if(ho(t))for(r=0;r<n.length;r++)if(t.ownerDocument.defaultView.getComputedStyle(t,null).getPropertyValue(e)===n[r])return!0;return!1}},isBogus:function(e){return ho(e)&&e.hasAttribute("data-mce-bogus")},isBogusAll:function(e){return ho(e)&&"all"===e.getAttribute("data-mce-bogus")},isTable:function(e){return ho(e)&&"TABLE"===e.tagName}},ko=function(e){return e&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type")},To=function(e,t){var n,r=t.childNodes;if(!So.isElement(t)||!ko(t)){for(n=r.length-1;n>=0;n--)To(e,r[n]);if(!1===So.isDocument(t)){if(So.isText(t)&&t.nodeValue.length>0){var o=Dt.trim(t.nodeValue).length;if(e.isBlock(t.parentNode)||o>0)return;if(0===o&&(a=(i=t).previousSibling&&"SPAN"===i.previousSibling.nodeName,u=i.nextSibling&&"SPAN"===i.nextSibling.nodeName,a&&u))return}else if(So.isElement(t)&&(1===(r=t.childNodes).length&&ko(r[0])&&t.parentNode.insertBefore(r[0],t),r.length||fo(Fn.fromDom(t))))return;e.remove(t)}var i,a,u;return t}},Ao={trimNode:To},_o=Dt.makeMap,Ro=/[&<>\"\u0060\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Bo=/[<>&\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Do=/[<>&\"\']/g,Oo=/&#([a-z0-9]+);?|&([a-z0-9]+);/gi,Po={128:"\u20ac",130:"\u201a",131:"\u0192",132:"\u201e",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02c6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017d",145:"\u2018",146:"\u2019",147:"\u201c",148:"\u201d",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02dc",153:"\u2122",154:"\u0161",155:"\u203a",156:"\u0153",158:"\u017e",159:"\u0178"};to={'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;","&":"&amp;","`":"&#96;"},no={"&lt;":"<","&gt;":">","&amp;":"&","&quot;":'"',"&apos;":"'"};var Lo=function(e,t){var n,r,o,i={};if(e){for(e=e.split(","),t=t||10,n=0;n<e.length;n+=2)r=String.fromCharCode(parseInt(e[n],t)),to[r]||(o="&"+e[n+1]+";",i[r]=o,i[o]=r);return i}};eo=Lo("50,nbsp,51,iexcl,52,cent,53,pound,54,curren,55,yen,56,brvbar,57,sect,58,uml,59,copy,5a,ordf,5b,laquo,5c,not,5d,shy,5e,reg,5f,macr,5g,deg,5h,plusmn,5i,sup2,5j,sup3,5k,acute,5l,micro,5m,para,5n,middot,5o,cedil,5p,sup1,5q,ordm,5r,raquo,5s,frac14,5t,frac12,5u,frac34,5v,iquest,60,Agrave,61,Aacute,62,Acirc,63,Atilde,64,Auml,65,Aring,66,AElig,67,Ccedil,68,Egrave,69,Eacute,6a,Ecirc,6b,Euml,6c,Igrave,6d,Iacute,6e,Icirc,6f,Iuml,6g,ETH,6h,Ntilde,6i,Ograve,6j,Oacute,6k,Ocirc,6l,Otilde,6m,Ouml,6n,times,6o,Oslash,6p,Ugrave,6q,Uacute,6r,Ucirc,6s,Uuml,6t,Yacute,6u,THORN,6v,szlig,70,agrave,71,aacute,72,acirc,73,atilde,74,auml,75,aring,76,aelig,77,ccedil,78,egrave,79,eacute,7a,ecirc,7b,euml,7c,igrave,7d,iacute,7e,icirc,7f,iuml,7g,eth,7h,ntilde,7i,ograve,7j,oacute,7k,ocirc,7l,otilde,7m,ouml,7n,divide,7o,oslash,7p,ugrave,7q,uacute,7r,ucirc,7s,uuml,7t,yacute,7u,thorn,7v,yuml,ci,fnof,sh,Alpha,si,Beta,sj,Gamma,sk,Delta,sl,Epsilon,sm,Zeta,sn,Eta,so,Theta,sp,Iota,sq,Kappa,sr,Lambda,ss,Mu,st,Nu,su,Xi,sv,Omicron,t0,Pi,t1,Rho,t3,Sigma,t4,Tau,t5,Upsilon,t6,Phi,t7,Chi,t8,Psi,t9,Omega,th,alpha,ti,beta,tj,gamma,tk,delta,tl,epsilon,tm,zeta,tn,eta,to,theta,tp,iota,tq,kappa,tr,lambda,ts,mu,tt,nu,tu,xi,tv,omicron,u0,pi,u1,rho,u2,sigmaf,u3,sigma,u4,tau,u5,upsilon,u6,phi,u7,chi,u8,psi,u9,omega,uh,thetasym,ui,upsih,um,piv,812,bull,816,hellip,81i,prime,81j,Prime,81u,oline,824,frasl,88o,weierp,88h,image,88s,real,892,trade,89l,alefsym,8cg,larr,8ch,uarr,8ci,rarr,8cj,darr,8ck,harr,8dl,crarr,8eg,lArr,8eh,uArr,8ei,rArr,8ej,dArr,8ek,hArr,8g0,forall,8g2,part,8g3,exist,8g5,empty,8g7,nabla,8g8,isin,8g9,notin,8gb,ni,8gf,prod,8gh,sum,8gi,minus,8gn,lowast,8gq,radic,8gt,prop,8gu,infin,8h0,ang,8h7,and,8h8,or,8h9,cap,8ha,cup,8hb,int,8hk,there4,8hs,sim,8i5,cong,8i8,asymp,8j0,ne,8j1,equiv,8j4,le,8j5,ge,8k2,sub,8k3,sup,8k4,nsub,8k6,sube,8k7,supe,8kl,oplus,8kn,otimes,8l5,perp,8m5,sdot,8o8,lceil,8o9,rceil,8oa,lfloor,8ob,rfloor,8p9,lang,8pa,rang,9ea,loz,9j0,spades,9j3,clubs,9j5,hearts,9j6,diams,ai,OElig,aj,oelig,b0,Scaron,b1,scaron,bo,Yuml,m6,circ,ms,tilde,802,ensp,803,emsp,809,thinsp,80c,zwnj,80d,zwj,80e,lrm,80f,rlm,80j,ndash,80k,mdash,80o,lsquo,80p,rsquo,80q,sbquo,80s,ldquo,80t,rdquo,80u,bdquo,810,dagger,811,Dagger,81g,permil,81p,lsaquo,81q,rsaquo,85c,euro",32);var Io=function(e,t){return e.replace(t?Ro:Bo,function(e){return to[e]||e})},Mo=function(e,t){return e.replace(t?Ro:Bo,function(e){return e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":to[e]||"&#"+e.charCodeAt(0)+";"})},Fo=function(e,t,n){return n=n||eo,e.replace(t?Ro:Bo,function(e){return to[e]||n[e]||e})},zo={encodeRaw:Io,encodeAllRaw:function(e){return(""+e).replace(Do,function(e){return to[e]||e})},encodeNumeric:Mo,encodeNamed:Fo,getEncodeFunc:function(e,t){var n=Lo(t)||eo,r=_o(e.replace(/\+/g,","));return r.named&&r.numeric?function(e,t){return e.replace(t?Ro:Bo,function(e){return to[e]!==undefined?to[e]:n[e]!==undefined?n[e]:e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":"&#"+e.charCodeAt(0)+";"})}:r.named?t?function(e,t){return Fo(e,t,n)}:Fo:r.numeric?Mo:Io},decode:function(e){return e.replace(Oo,function(e,t){return t?(t="x"===t.charAt(0).toLowerCase()?parseInt(t.substr(1),16):parseInt(t,10))>65535?(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t))):Po[t]||String.fromCharCode(t):no[e]||eo[e]||(n=e,(r=Fn.fromTag("div").dom()).innerHTML=n,r.textContent||r.innerText||n);var n,r})}},Uo={},qo={},Vo=Dt.makeMap,Ho=Dt.each,jo=Dt.extend,$o=Dt.explode,Wo=Dt.inArray,Ko=function(e,t){return(e=Dt.trim(e))?e.split(t||" "):[]},Xo=function(e){var t,n,r,o,i,a,u={},s=function(e,n,r){var o,i,a,s=function(e,t){var n,r,o={};for(n=0,r=e.length;n<r;n++)o[e[n]]=t||{};return o};for(n=n||"","string"==typeof(r=r||[])&&(r=Ko(r)),o=(e=Ko(e)).length;o--;)a={attributes:s(i=Ko([t,n].join(" "))),attributesOrder:i,children:s(r,qo)},u[e[o]]=a},c=function(e,t){var n,r,o,i;for(n=(e=Ko(e)).length,t=Ko(t);n--;)for(r=u[e[n]],o=0,i=t.length;o<i;o++)r.attributes[t[o]]={},r.attributesOrder.push(t[o])};return Uo[e]?Uo[e]:(t="id accesskey class dir lang style tabindex title role",n="address blockquote div dl fieldset form h1 h2 h3 h4 h5 h6 hr menu ol p pre table ul",r="a abbr b bdo br button cite code del dfn em embed i iframe img input ins kbd label map noscript object q s samp script select small span strong sub sup textarea u var #text #comment","html4"!==e&&(t+=" contenteditable contextmenu draggable dropzone hidden spellcheck translate",n+=" article aside details dialog figure header footer hgroup section nav",r+=" audio canvas command datalist mark meter output picture progress time wbr video ruby bdi keygen"),"html5-strict"!==e&&(t+=" xml:lang",r=[r,a="acronym applet basefont big font strike tt"].join(" "),Ho(Ko(a),function(e){s(e,"",r)}),n=[n,i="center dir isindex noframes"].join(" "),o=[n,r].join(" "),Ho(Ko(i),function(e){s(e,"",o)})),o=o||[n,r].join(" "),s("html","manifest","head body"),s("head","","base command link meta noscript script style title"),s("title hr noscript br"),s("base","href target"),s("link","href rel media hreflang type sizes hreflang"),s("meta","name http-equiv content charset"),s("style","media type scoped"),s("script","src async defer type charset"),s("body","onafterprint onbeforeprint onbeforeunload onblur onerror onfocus onhashchange onload onmessage onoffline ononline onpagehide onpageshow onpopstate onresize onscroll onstorage onunload",o),s("address dt dd div caption","",o),s("h1 h2 h3 h4 h5 h6 pre p abbr code var samp kbd sub sup i b u bdo span legend em strong small s cite dfn","",r),s("blockquote","cite",o),s("ol","reversed start type","li"),s("ul","","li"),s("li","value",o),s("dl","","dt dd"),s("a","href target rel media hreflang type",r),s("q","cite",r),s("ins del","cite datetime",o),s("img","src sizes srcset alt usemap ismap width height"),s("iframe","src name width height",o),s("embed","src type width height"),s("object","data type typemustmatch name usemap form width height",[o,"param"].join(" ")),s("param","name value"),s("map","name",[o,"area"].join(" ")),s("area","alt coords shape href target rel media hreflang type"),s("table","border","caption colgroup thead tfoot tbody tr"+("html4"===e?" col":"")),s("colgroup","span","col"),s("col","span"),s("tbody thead tfoot","","tr"),s("tr","","td th"),s("td","colspan rowspan headers",o),s("th","colspan rowspan headers scope abbr",o),s("form","accept-charset action autocomplete enctype method name novalidate target",o),s("fieldset","disabled form name",[o,"legend"].join(" ")),s("label","form for",r),s("input","accept alt autocomplete checked dirname disabled form formaction formenctype formmethod formnovalidate formtarget height list max maxlength min multiple name pattern readonly required size src step type value width"),s("button","disabled form formaction formenctype formmethod formnovalidate formtarget name type value","html4"===e?o:r),s("select","disabled form multiple name required size","option optgroup"),s("optgroup","disabled label","option"),s("option","disabled label selected value"),s("textarea","cols dirname disabled form maxlength name readonly required rows wrap"),s("menu","type label",[o,"li"].join(" ")),s("noscript","",o),"html4"!==e&&(s("wbr"),s("ruby","",[r,"rt rp"].join(" ")),s("figcaption","",o),s("mark rt rp summary bdi","",r),s("canvas","width height",o),s("video","src crossorigin poster preload autoplay mediagroup loop muted controls width height buffered",[o,"track source"].join(" ")),s("audio","src crossorigin preload autoplay mediagroup loop muted controls buffered volume",[o,"track source"].join(" ")),s("picture","","img source"),s("source","src srcset type media sizes"),s("track","kind src srclang label default"),s("datalist","",[r,"option"].join(" ")),s("article section nav aside header footer","",o),s("hgroup","","h1 h2 h3 h4 h5 h6"),s("figure","",[o,"figcaption"].join(" ")),s("time","datetime",r),s("dialog","open",o),s("command","type label icon disabled checked radiogroup command"),s("output","for form name",r),s("progress","value max",r),s("meter","value min max low high optimum",r),s("details","open",[o,"summary"].join(" ")),s("keygen","autofocus challenge disabled form keytype name")),"html5-strict"!==e&&(c("script","language xml:space"),c("style","xml:space"),c("object","declare classid code codebase codetype archive standby align border hspace vspace"),c("embed","align name hspace vspace"),c("param","valuetype type"),c("a","charset name rev shape coords"),c("br","clear"),c("applet","codebase archive code object alt name width height align hspace vspace"),c("img","name longdesc align border hspace vspace"),c("iframe","longdesc frameborder marginwidth marginheight scrolling align"),c("font basefont","size color face"),c("input","usemap align"),c("select","onchange"),c("textarea"),c("h1 h2 h3 h4 h5 h6 div p legend caption","align"),c("ul","type compact"),c("li","type"),c("ol dl menu dir","compact"),c("pre","width xml:space"),c("hr","align noshade size width"),c("isindex","prompt"),c("table","summary width frame rules cellspacing cellpadding align bgcolor"),c("col","width align char charoff valign"),c("colgroup","width align char charoff valign"),c("thead","align char charoff valign"),c("tr","align char charoff valign bgcolor"),c("th","axis align char charoff valign nowrap bgcolor width height"),c("form","accept"),c("td","abbr axis scope align char charoff valign nowrap bgcolor width height"),c("tfoot","align char charoff valign"),c("tbody","align char charoff valign"),c("area","nohref"),c("body","background bgcolor text link vlink alink")),"html4"!==e&&(c("input button select textarea","autofocus"),c("input textarea","placeholder"),c("a","download"),c("link script img","crossorigin"),c("iframe","sandbox seamless allowfullscreen")),Ho(Ko("a form meter progress dfn"),function(e){u[e]&&delete u[e].children[e]}),delete u.caption.children.table,delete u.script,Uo[e]=u,u)},Yo=function(e,t){var n;return e&&(n={},"string"==typeof e&&(e={"*":e}),Ho(e,function(e,r){n[r]=n[r.toUpperCase()]="map"===t?Vo(e,/[, ]/):$o(e,/[, ]/)})),n};function Go(e){var t,n,r,o,i,a,u,s,c,l,f,d,m,p={},g={},h=[],v={},y={},b=function(t,n,r){var o=e[t];return o?o=Vo(o,/[, ]/,Vo(o.toUpperCase(),/[, ]/)):(o=Uo[t])||(o=Vo(n," ",Vo(n.toUpperCase()," ")),o=jo(o,r),Uo[t]=o),o};r=Xo((e=e||{}).schema),!1===e.verify_html&&(e.valid_elements="*[*]"),t=Yo(e.valid_styles),n=Yo(e.invalid_styles,"map"),s=Yo(e.valid_classes,"map"),o=b("whitespace_elements","pre script noscript style textarea video audio iframe object code"),i=b("self_closing_elements","colgroup dd dt li option p td tfoot th thead tr"),a=b("short_ended_elements","area base basefont br col frame hr img input isindex link meta param embed source wbr track"),u=b("boolean_attributes","checked compact declare defer disabled ismap multiple nohref noresize noshade nowrap readonly selected autoplay loop controls"),l=b("non_empty_elements","td th iframe video audio object script pre code",a),f=b("move_caret_before_on_enter_elements","table",l),d=b("text_block_elements","h1 h2 h3 h4 h5 h6 p div address pre form blockquote center dir fieldset header footer article section hgroup aside nav figure"),c=b("block_elements","hr table tbody thead tfoot th tr td li ol ul caption dl dt dd noscript menu isindex option datalist select optgroup figcaption",d),m=b("text_inline_elements","span strong b em i font strike u var cite dfn code mark q sup sub samp"),Ho((e.special||"script noscript noframes noembed title style textarea xmp").split(" "),function(e){y[e]=new RegExp("</"+e+"[^>]*>","gi")});var C=function(e){return new RegExp("^"+e.replace(/([?+*])/g,".$1")+"$")},x=function(e){var t,n,r,o,i,a,u,s,c,l,f,d,m,g,v,y,b,x,w,N=/^([#+\-])?([^\[!\/]+)(?:\/([^\[!]+))?(?:(!?)\[([^\]]+)\])?$/,E=/^([!\-])?(\w+[\\:]:\w+|[^=:<]+)?(?:([=:<])(.*))?$/,S=/[*?+]/;if(e)for(e=Ko(e,","),p["@"]&&(y=p["@"].attributes,b=p["@"].attributesOrder),t=0,n=e.length;t<n;t++)if(i=N.exec(e[t])){if(g=i[1],c=i[2],v=i[3],s=i[5],a={attributes:d={},attributesOrder:m=[]},"#"===g&&(a.paddEmpty=!0),"-"===g&&(a.removeEmpty=!0),"!"===i[4]&&(a.removeEmptyAttrs=!0),y){for(x in y)d[x]=y[x];m.push.apply(m,b)}if(s)for(r=0,o=(s=Ko(s,"|")).length;r<o;r++)if(i=E.exec(s[r])){if(u={},f=i[1],l=i[2].replace(/[\\:]:/g,":"),g=i[3],w=i[4],"!"===f&&(a.attributesRequired=a.attributesRequired||[],a.attributesRequired.push(l),u.required=!0),"-"===f){delete d[l],m.splice(Wo(m,l),1);continue}g&&("="===g&&(a.attributesDefault=a.attributesDefault||[],a.attributesDefault.push({name:l,value:w}),u.defaultValue=w),":"===g&&(a.attributesForced=a.attributesForced||[],a.attributesForced.push({name:l,value:w}),u.forcedValue=w),"<"===g&&(u.validValues=Vo(w,"?"))),S.test(l)?(a.attributePatterns=a.attributePatterns||[],u.pattern=C(l),a.attributePatterns.push(u)):(d[l]||m.push(l),d[l]=u)}y||"@"!==c||(y=d,b=m),v&&(a.outputName=c,p[v]=a),S.test(c)?(a.pattern=C(c),h.push(a)):p[c]=a}},w=function(e){p={},h=[],x(e),Ho(r,function(e,t){g[t]=e.children})},N=function(e){var t=/^(~)?(.+)$/;e&&(Uo.text_block_elements=Uo.block_elements=null,Ho(Ko(e,","),function(e){var n=t.exec(e),r="~"===n[1],o=r?"span":"div",i=n[2];if(g[i]=g[o],v[i]=o,r||(c[i.toUpperCase()]={},c[i]={}),!p[i]){var a=p[o];delete(a=jo({},a)).removeEmptyAttrs,delete a.removeEmpty,p[i]=a}Ho(g,function(e,t){e[o]&&(g[t]=e=jo({},g[t]),e[i]=e[o])})}))},E=function(t){var n=/^([+\-]?)(\w+)\[([^\]]+)\]$/;Uo[e.schema]=null,t&&Ho(Ko(t,","),function(e){var t,r,o=n.exec(e);o&&(r=o[1],t=r?g[o[2]]:g[o[2]]={"#comment":{}},t=g[o[2]],Ho(Ko(o[3],"|"),function(e){"-"===r?delete t[e]:t[e]={}}))})},S=function(e){var t,n=p[e];if(n)return n;for(t=h.length;t--;)if((n=h[t]).pattern.test(e))return n};return e.valid_elements?w(e.valid_elements):(Ho(r,function(e,t){p[t]={attributes:e.attributes,attributesOrder:e.attributesOrder},g[t]=e.children}),"html5"!==e.schema&&Ho(Ko("strong/b em/i"),function(e){e=Ko(e,"/"),p[e[1]].outputName=e[0]}),Ho(Ko("ol ul sub sup blockquote span font a table tbody tr strong em b i"),function(e){p[e]&&(p[e].removeEmpty=!0)}),Ho(Ko("p h1 h2 h3 h4 h5 h6 th td pre div address caption li"),function(e){p[e].paddEmpty=!0}),Ho(Ko("span"),function(e){p[e].removeEmptyAttrs=!0})),N(e.custom_elements),E(e.valid_children),x(e.extended_valid_elements),E("+ol[ul|ol],+ul[ul|ol]"),Ho({dd:"dl",dt:"dl",li:"ul ol",td:"tr",th:"tr",tr:"tbody thead tfoot",tbody:"table",thead:"table",tfoot:"table",legend:"fieldset",area:"map",param:"video audio object"},function(e,t){p[t]&&(p[t].parentsRequired=Ko(e))}),e.invalid_elements&&Ho($o(e.invalid_elements),function(e){p[e]&&delete p[e]}),S("span")||x("span[!data-mce-type|*]"),{children:g,elements:p,getValidStyles:function(){return t},getValidClasses:function(){return s},getBlockElements:function(){return c},getInvalidStyles:function(){return n},getShortEndedElements:function(){return a},getTextBlockElements:function(){return d},getTextInlineElements:function(){return m},getBoolAttrs:function(){return u},getElementRule:S,getSelfClosingElements:function(){return i},getNonEmptyElements:function(){return l},getMoveCaretBeforeOnEnterElements:function(){return f},getWhiteSpaceElements:function(){return o},getSpecialElements:function(){return y},isValidChild:function(e,t){var n=g[e.toLowerCase()];return!(!n||!n[t.toLowerCase()])},isValid:function(e,t){var n,r,o=S(e);if(o){if(!t)return!0;if(o.attributes[t])return!0;if(n=o.attributePatterns)for(r=n.length;r--;)if(n[r].pattern.test(e))return!0}return!1},getCustomElements:function(){return v},addValidElements:x,setValidElements:w,addCustomElements:N,addValidChildren:E}}var Jo=function(e,t,n,r){var o=function(e){return(e=parseInt(e,10).toString(16)).length>1?e:"0"+e};return"#"+o(t)+o(n)+o(r)};function Qo(e,t){var n,r,o,i,a=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/gi,u=/(?:url(?:(?:\(\s*\"([^\"]+)\"\s*\))|(?:\(\s*\'([^\']+)\'\s*\))|(?:\(\s*([^)\s]+)\s*\))))|(?:\'([^\']+)\')|(?:\"([^\"]+)\")/gi,s=/\s*([^:]+):\s*([^;]+);?/g,c=/\s+$/,l={},f="\ufeff";for(e=e||{},t&&(o=t.getValidStyles(),i=t.getInvalidStyles()),r=("\\\" \\' \\; \\: ; : "+f).split(" "),n=0;n<r.length;n++)l[r[n]]=f+n,l[f+n]=r[n];return{toHex:function(e){return e.replace(a,Jo)},parse:function(t){var r,o,i,d,m,p,g,h,v={},y=e.url_converter,b=e.url_converter_scope||this,C=function(e,t,r){var o,i,a,u;if((o=v[e+"-top"+t])&&(i=v[e+"-right"+t])&&(a=v[e+"-bottom"+t])&&(u=v[e+"-left"+t])){var s=[o,i,a,u];for(n=s.length-1;n--&&s[n]===s[n+1];);n>-1&&r||(v[e+t]=-1===n?s[0]:s.join(" "),delete v[e+"-top"+t],delete v[e+"-right"+t],delete v[e+"-bottom"+t],delete v[e+"-left"+t])}},x=function(e){var t,n=v[e];if(n){for(t=(n=n.split(" ")).length;t--;)if(n[t]!==n[0])return!1;return v[e]=n[0],!0}},w=function(e){return d=!0,l[e]},N=function(e,t){return d&&(e=e.replace(/\uFEFF[0-9]/g,function(e){return l[e]})),t||(e=e.replace(/\\([\'\";:])/g,"$1")),e},E=function(e){return String.fromCharCode(parseInt(e.slice(1),16))},S=function(e){return e.replace(/\\[0-9a-f]+/gi,E)},k=function(t,n,r,o,i,a){if(i=i||a)return"'"+(i=N(i)).replace(/\'/g,"\\'")+"'";if(n=N(n||r||o),!e.allow_script_urls){var u=n.replace(/[\s\r\n]+/g,"");if(/(java|vb)script:/i.test(u))return"";if(!e.allow_svg_data_urls&&/^data:image\/svg/i.test(u))return""}return y&&(n=y.call(b,n,"style")),"url('"+n.replace(/\'/g,"\\'")+"')"};if(t){for(t=(t=t.replace(/[\u0000-\u001F]/g,"")).replace(/\\[\"\';:\uFEFF]/g,w).replace(/\"[^\"]+\"|\'[^\']+\'/g,function(e){return e.replace(/[;:]/g,w)});r=s.exec(t);)if(s.lastIndex=r.index+r[0].length,o=r[1].replace(c,"").toLowerCase(),i=r[2].replace(c,""),o&&i){if(o=S(o),i=S(i),-1!==o.indexOf(f)||-1!==o.indexOf('"'))continue;if(!e.allow_script_urls&&("behavior"===o||/expression\s*\(|\/\*|\*\//.test(i)))continue;"font-weight"===o&&"700"===i?i="bold":"color"!==o&&"background-color"!==o||(i=i.toLowerCase()),i=(i=i.replace(a,Jo)).replace(u,k),v[o]=d?N(i,!0):i}C("border","",!0),C("border","-width"),C("border","-color"),C("border","-style"),C("padding",""),C("margin",""),m="border",g="border-style",h="border-color",x(p="border-width")&&x(g)&&x(h)&&(v[m]=v[p]+" "+v[g]+" "+v[h],delete v[p],delete v[g],delete v[h]),"medium none"===v.border&&delete v.border,"none"===v["border-image"]&&delete v["border-image"]}return v},serialize:function(e,t){var n,r,a,u,s,c="",l=function(t){var n,r,i,a;if(n=o[t])for(r=0,i=n.length;r<i;r++)t=n[r],(a=e[t])&&(c+=(c.length>0?" ":"")+t+": "+a+";")};if(t&&o)l("*"),l(t);else for(n in e)!(r=e[n])||i&&(a=n,u=t,s=void 0,(s=i["*"])&&s[a]||(s=i[u])&&s[a])||(c+=(c.length>0?" ":"")+n+": "+r+";");return c}}}var Zo=Dt.each,ei=Dt.is,ti=Dt.grep,ni=de.ie,ri=/^([a-z0-9],?)+$/i,oi=/^[ \t\r\n]*$/,ii=function(e,t){var n=t.attr("style");(n=e.serializeStyle(e.parseStyle(n),t[0].nodeName))||(n=null),t.attr("data-mce-style",n)},ai=function(e,t){var n,r,o=0;if(e)for(n=e.nodeType,e=e.previousSibling;e;e=e.previousSibling)r=e.nodeType,(!t||3!==r||r!==n&&e.nodeValue.length)&&(o++,n=r);return o},ui=function(e,t){var n,r,o,i,a,u,s=this;s.doc=e,s.win=window,s.files={},s.counter=0,s.stdMode=!ni||e.documentMode>=8,s.boxModel=!ni||"CSS1Compat"===e.compatMode||s.stdMode,s.styleSheetLoader=function(e,t){var n,r=0,o={};n=(t=t||{}).maxLoadTime||5e3;var i=function(t){e.getElementsByTagName("head")[0].appendChild(t)},a=function(t,a,u){var s,c,l,f,d=function(){for(var e=f.passed,t=e.length;t--;)e[t]();f.status=2,f.passed=[],f.failed=[]},m=function(){for(var e=f.failed,t=e.length;t--;)e[t]();f.status=3,f.passed=[],f.failed=[]},p=function(e,t){e()||((new Date).getTime()-l<n?ve.setTimeout(t):m())},g=function(){p(function(){for(var t,n,r=e.styleSheets,o=r.length;o--;)if((n=(t=r[o]).ownerNode?t.ownerNode:t.owningElement)&&n.id===s.id)return d(),!0},g)},h=function(){p(function(){try{var e=c.sheet.cssRules;return d(),!!e}catch(t){}},h)};if(t=Dt._addCacheSuffix(t),o[t]?f=o[t]:(f={passed:[],failed:[]},o[t]=f),a&&f.passed.push(a),u&&f.failed.push(u),1!==f.status)if(2!==f.status)if(3!==f.status){if(f.status=1,(s=e.createElement("link")).rel="stylesheet",s.type="text/css",s.id="u"+r++,s.async=!1,s.defer=!1,l=(new Date).getTime(),"onload"in s&&!((v=navigator.userAgent.match(/WebKit\/(\d*)/))&&parseInt(v[1],10)<536))s.onload=g,s.onerror=m;else{if(navigator.userAgent.indexOf("Firefox")>0)return(c=e.createElement("style")).textContent='@import "'+t+'"',h(),void i(c);g()}var v;i(s),s.href=t}else m();else d()},u=function(e){return Wr.nu(function(t){a(e,y.compose(t,y.constant(Qr.value(e))),y.compose(t,y.constant(Qr.error(e))))})},s=function(e){return e.fold(y.identity,y.identity)};return{load:a,loadAll:function(e,t,n){Yr.par(M.map(e,u)).get(function(e){var r=M.partition(e,function(e){return e.isValue()});r.fail.length>0?n(r.fail.map(s)):t(r.pass.map(s))})}}}(e),s.boundEvents=[],s.settings=t=t||{},s.schema=t.schema?t.schema:Go({}),s.styles=Qo({url_converter:t.url_converter,url_converter_scope:t.url_converter_scope},t.schema),s.fixDoc(e),s.events=t.ownEvents?new ke(t.proxy):ke.Event,s.attrHooks=(r=s,a={},u=(o=t).keep_values,i={set:function(e,t,n){o.url_converter&&(t=o.url_converter.call(o.url_converter_scope||r,t,n,e[0])),e.attr("data-mce-"+n,t).attr(n,t)},get:function(e,t){return e.attr("data-mce-"+t)||e.attr(t)}},a={style:{set:function(e,t){null===t||"object"!=typeof t?(u&&e.attr("data-mce-style",t),e.attr("style",t)):e.css(t)},get:function(e){var t=e.attr("data-mce-style")||e.attr("style");return t=r.serializeStyle(r.parseStyle(t),e[0].nodeName)}}},u&&(a.href=a.src=i),a),n=s.schema.getBlockElements(),s.$=Jt.overrideDefaults(function(){return{context:e,element:s.getRoot()}}),s.isBlock=function(e){if(!e)return!1;var t=e.nodeType;return t?!(1!==t||!n[e.nodeName]):!!n[e]}};ui.prototype={$$:function(e){return"string"==typeof e&&(e=this.get(e)),this.$(e)},root:null,fixDoc:function(e){},clone:function(e,t){var n,r,o=this;return!ni||1!==e.nodeType||t?e.cloneNode(t):(r=o.doc,t?n.firstChild:(n=r.createElement(e.nodeName),Zo(o.getAttribs(e),function(t){o.setAttrib(n,t.nodeName,o.getAttrib(e,t.nodeName))}),n))},getRoot:function(){return this.settings.root_element||this.doc.body},getViewPort:function(e){var t,n;return t=(e=e||this.win).document,n=this.boxModel?t.documentElement:t.body,{x:e.pageXOffset||n.scrollLeft,y:e.pageYOffset||n.scrollTop,w:e.innerWidth||n.clientWidth,h:e.innerHeight||n.clientHeight}},getRect:function(e){var t,n;return e=this.get(e),t=this.getPos(e),n=this.getSize(e),{x:t.x,y:t.y,w:n.w,h:n.h}},getSize:function(e){var t,n;return e=this.get(e),t=this.getStyle(e,"width"),n=this.getStyle(e,"height"),-1===t.indexOf("px")&&(t=0),-1===n.indexOf("px")&&(n=0),{w:parseInt(t,10)||e.offsetWidth||e.clientWidth,h:parseInt(n,10)||e.offsetHeight||e.clientHeight}},getParent:function(e,t,n){return this.getParents(e,t,n,!1)},getParents:function(e,t,n,r){var o,i=this,a=[];for(e=i.get(e),r=r===undefined,n=n||("BODY"!==i.getRoot().nodeName?i.getRoot().parentNode:null),ei(t,"string")&&(o=t,t="*"===t?function(e){return 1===e.nodeType}:function(e){return i.is(e,o)});e&&e!==n&&e.nodeType&&9!==e.nodeType;){if(!t||t(e)){if(!r)return e;a.push(e)}e=e.parentNode}return r?a:null},get:function(e){var t;return e&&this.doc&&"string"==typeof e&&(t=e,(e=this.doc.getElementById(e))&&e.id!==t)?this.doc.getElementsByName(t)[1]:e},getNext:function(e,t){return this._findSib(e,t,"nextSibling")},getPrev:function(e,t){return this._findSib(e,t,"previousSibling")},select:function(e,t){return ct(e,this.get(t)||this.settings.root_element||this.doc,[])},is:function(e,t){var n;if(!e)return!1;if(e.length===undefined){if("*"===t)return 1===e.nodeType;if(ri.test(t)){for(t=t.toLowerCase().split(/,/),e=e.nodeName.toLowerCase(),n=t.length-1;n>=0;n--)if(t[n]===e)return!0;return!1}}if(e.nodeType&&1!==e.nodeType)return!1;var r=e.nodeType?[e]:e;return ct(t,r[0].ownerDocument||r[0],null,r).length>0},add:function(e,t,n,r,o){var i=this;return this.run(e,function(e){var a;return a=ei(t,"string")?i.doc.createElement(t):t,i.setAttribs(a,n),r&&(r.nodeType?a.appendChild(r):i.setHTML(a,r)),o?a:e.appendChild(a)})},create:function(e,t,n){return this.add(this.doc.createElement(e),e,t,n,1)},createHTML:function(e,t,n){var r,o="";for(r in o+="<"+e,t)t.hasOwnProperty(r)&&null!==t[r]&&"undefined"!=typeof t[r]&&(o+=" "+r+'="'+this.encode(t[r])+'"');return void 0!==n?o+">"+n+"</"+e+">":o+" />"},createFragment:function(e){var t,n,r,o=this.doc;for(r=o.createElement("div"),t=o.createDocumentFragment(),e&&(r.innerHTML=e);n=r.firstChild;)t.appendChild(n);return t},remove:function(e,t){return e=this.$$(e),t?e.each(function(){for(var e;e=this.firstChild;)3===e.nodeType&&0===e.data.length?this.removeChild(e):this.parentNode.insertBefore(e,this)}).remove():e.remove(),e.length>1?e.toArray():e[0]},setStyle:function(e,t,n){e=this.$$(e).css(t,n),this.settings.update_styles&&ii(this,e)},getStyle:function(e,t,n){return e=this.$$(e),n?e.css(t):("float"===(t=t.replace(/-(\D)/g,function(e,t){return t.toUpperCase()}))&&(t=de.ie&&de.ie<12?"styleFloat":"cssFloat"),e[0]&&e[0].style?e[0].style[t]:undefined)},setStyles:function(e,t){e=this.$$(e).css(t),this.settings.update_styles&&ii(this,e)},removeAllAttribs:function(e){return this.run(e,function(e){var t,n=e.attributes;for(t=n.length-1;t>=0;t--)e.removeAttributeNode(n.item(t))})},setAttrib:function(e,t,n){var r,o,i=this.settings;""===n&&(n=null),r=(e=this.$$(e)).attr(t),e.length&&((o=this.attrHooks[t])&&o.set?o.set(e,n,t):e.attr(t,n),r!==n&&i.onSetAttrib&&i.onSetAttrib({attrElm:e,attrName:t,attrValue:n}))},setAttribs:function(e,t){var n=this;n.$$(e).each(function(e,r){Zo(t,function(e,t){n.setAttrib(r,t,e)})})},getAttrib:function(e,t,n){var r,o;return(e=this.$$(e)).length&&(o=(r=this.attrHooks[t])&&r.get?r.get(e,t):e.attr(t)),void 0===o&&(o=n||""),o},getPos:function(e,t){return qr(this.doc.body,this.get(e),t)},parseStyle:function(e){return this.styles.parse(e)},serializeStyle:function(e,t){return this.styles.serialize(e,t)},addStyle:function(e){var t,n,r=this.doc;if(this!==ui.DOM&&r===document){var o=ui.DOM.addedStyles;if((o=o||[])[e])return;o[e]=!0,ui.DOM.addedStyles=o}(n=r.getElementById("mceDefaultStyles"))||((n=r.createElement("style")).id="mceDefaultStyles",n.type="text/css",(t=r.getElementsByTagName("head")[0]).firstChild?t.insertBefore(n,t.firstChild):t.appendChild(n)),n.styleSheet?n.styleSheet.cssText+=e:n.appendChild(r.createTextNode(e))},loadCSS:function(e){var t,n=this,r=n.doc;n===ui.DOM||r!==document?(e||(e=""),t=r.getElementsByTagName("head")[0],Zo(e.split(","),function(e){var o;e=Dt._addCacheSuffix(e),n.files[e]||(n.files[e]=!0,o=n.create("link",{rel:"stylesheet",href:e}),ni&&r.documentMode&&r.recalc&&(o.onload=function(){r.recalc&&r.recalc(),o.onload=null}),t.appendChild(o))})):ui.DOM.loadCSS(e)},addClass:function(e,t){this.$$(e).addClass(t)},removeClass:function(e,t){this.toggleClass(e,t,!1)},hasClass:function(e,t){return this.$$(e).hasClass(t)},toggleClass:function(e,t,n){this.$$(e).toggleClass(t,n).each(function(){""===this.className&&Jt(this).attr("class",null)})},show:function(e){this.$$(e).show()},hide:function(e){this.$$(e).hide()},isHidden:function(e){return"none"===this.$$(e).css("display")},uniqueId:function(e){return(e||"mce_")+this.counter++},setHTML:function(e,t){e=this.$$(e),ni?e.each(function(e,n){if(!1!==n.canHaveHTML){for(;n.firstChild;)n.removeChild(n.firstChild);try{n.innerHTML="<br>"+t,n.removeChild(n.firstChild)}catch(r){Jt("<div></div>").html("<br>"+t).contents().slice(1).appendTo(n)}return t}}):e.html(t)},getOuterHTML:function(e){return 1===(e=this.get(e)).nodeType&&"outerHTML"in e?e.outerHTML:Jt("<div></div>").append(Jt(e).clone()).html()},setOuterHTML:function(e,t){var n=this;n.$$(e).each(function(){try{if("outerHTML"in this)return void(this.outerHTML=t)}catch(e){}n.remove(Jt(this).html(t),!0)})},decode:zo.decode,encode:zo.encodeAllRaw,insertAfter:function(e,t){return t=this.get(t),this.run(e,function(e){var n,r;return n=t.parentNode,(r=t.nextSibling)?n.insertBefore(e,r):n.appendChild(e),e})},replace:function(e,t,n){return this.run(t,function(t){return ei(t,"array")&&(e=e.cloneNode(!0)),n&&Zo(ti(t.childNodes),function(t){e.appendChild(t)}),t.parentNode.replaceChild(e,t)})},rename:function(e,t){var n,r=this;return e.nodeName!==t.toUpperCase()&&(n=r.create(t),Zo(r.getAttribs(e),function(t){r.setAttrib(n,t.nodeName,r.getAttrib(e,t.nodeName))}),r.replace(n,e,1)),n||e},findCommonAncestor:function(e,t){for(var n,r=e;r;){for(n=t;n&&r!==n;)n=n.parentNode;if(r===n)break;r=r.parentNode}return!r&&e.ownerDocument?e.ownerDocument.documentElement:r},toHex:function(e){return this.styles.toHex(Dt.trim(e))},run:function(e,t,n){var r,o=this;return"string"==typeof e&&(e=o.get(e)),!!e&&(n=n||this,e.nodeType||!e.length&&0!==e.length?t.call(n,e):(r=[],Zo(e,function(e,i){e&&("string"==typeof e&&(e=o.get(e)),r.push(t.call(n,e,i)))}),r))},getAttribs:function(e){var t;return(e=this.get(e))?ni?(t=[],"OBJECT"===e.nodeName?e.attributes:("OPTION"===e.nodeName&&this.getAttrib(e,"selected")&&t.push({specified:1,nodeName:"selected"}),e.cloneNode(!1).outerHTML.replace(/<\/?[\w:\-]+ ?|=[\"][^\"]+\"|=\'[^\']+\'|=[\w\-]+|>/gi,"").replace(/[\w:\-]+/gi,function(e){t.push({specified:1,nodeName:e})}),t)):e.attributes:[]},isEmpty:function(e,t){var n,r,o,i,a,u,s=0;if(e=e.firstChild){a=new Zr(e,e.parentNode),t=t||(this.schema?this.schema.getNonEmptyElements():null),i=this.schema?this.schema.getWhiteSpaceElements():{};do{if(1===(o=e.nodeType)){var c=e.getAttribute("data-mce-bogus");if(c){e=a.next("all"===c);continue}if(u=e.nodeName.toLowerCase(),t&&t[u]){if("br"===u){s++,e=a.next();continue}return!1}for(n=(r=this.getAttribs(e)).length;n--;)if("name"===(u=r[n].nodeName)||"data-mce-bookmark"===u)return!1}if(8===o)return!1;if(3===o&&!oi.test(e.nodeValue))return!1;if(3===o&&e.parentNode&&i[e.parentNode.nodeName]&&oi.test(e.nodeValue))return!1;e=a.next()}while(e)}return s<=1},createRng:function(){return this.doc.createRange()},nodeIndex:ai,split:function(e,t,n){var r,o,i,a=this.createRng();if(e&&t)return a.setStart(e.parentNode,this.nodeIndex(e)),a.setEnd(t.parentNode,this.nodeIndex(t)),r=a.extractContents(),(a=this.createRng()).setStart(t.parentNode,this.nodeIndex(t)+1),a.setEnd(e.parentNode,this.nodeIndex(e)+1),o=a.extractContents(),(i=e.parentNode).insertBefore(Ao.trimNode(this,r),e),n?i.insertBefore(n,e):i.insertBefore(t,e),i.insertBefore(Ao.trimNode(this,o),e),this.remove(e),n||t},bind:function(e,t,n,r){if(Dt.isArray(e)){for(var o=e.length;o--;)e[o]=this.bind(e[o],t,n,r);return e}return!this.settings.collect||e!==this.doc&&e!==this.win||this.boundEvents.push([e,t,n,r]),this.events.bind(e,t,n,r||this)},unbind:function(e,t,n){var r;if(Dt.isArray(e)){for(r=e.length;r--;)e[r]=this.unbind(e[r],t,n);return e}if(this.boundEvents&&(e===this.doc||e===this.win))for(r=this.boundEvents.length;r--;){var o=this.boundEvents[r];e!==o[0]||t&&t!==o[1]||n&&n!==o[2]||this.events.unbind(o[0],o[1],o[2])}return this.events.unbind(e,t,n)},fire:function(e,t,n){return this.events.fire(e,t,n)},getContentEditable:function(e){var t;return e&&1===e.nodeType?(t=e.getAttribute("data-mce-contenteditable"))&&"inherit"!==t?t:"inherit"!==e.contentEditable?e.contentEditable:null:null},getContentEditableParent:function(e){for(var t=this.getRoot(),n=null;e&&e!==t&&null===(n=this.getContentEditable(e));e=e.parentNode);return n},destroy:function(){if(this.boundEvents){for(var e=this.boundEvents.length;e--;){var t=this.boundEvents[e];this.events.unbind(t[0],t[1],t[2])}this.boundEvents=null}ct.setDocument&&ct.setDocument(),this.win=this.doc=this.root=this.events=this.frag=null},isChildOf:function(e,t){for(;e;){if(t===e)return!0;e=e.parentNode}return!1},dumpRng:function(e){return"startContainer: "+e.startContainer.nodeName+", startOffset: "+e.startOffset+", endContainer: "+e.endContainer.nodeName+", endOffset: "+e.endOffset},_findSib:function(e,t,n){var r=this,o=t;if(e)for("string"==typeof o&&(o=function(e){return r.is(e,t)}),e=e[n];e;e=e[n])if(o(e))return e;return null}},ui.DOM=new ui(document),ui.nodeIndex=ai;var si=ui.DOM,ci=Dt.each,li=Dt.grep,fi=function(e){return"function"==typeof e},di=function(){var e={},t=[],n={},r=[],o=0;this.isDone=function(t){return 2===e[t]},this.markDone=function(t){e[t]=2},this.add=this.load=function(r,o,i,a){e[r]===undefined&&(t.push(r),e[r]=0),o&&(n[r]||(n[r]=[]),n[r].push({success:o,failure:a,scope:i||this}))},this.remove=function(t){delete e[t],delete n[t]},this.loadQueue=function(e,n,r){this.loadScripts(t,e,n,r)},this.loadScripts=function(t,i,a,u){var s,c=[],l=function(e,t){ci(n[t],function(t){fi(t[e])&&t[e].call(t.scope)}),n[t]=undefined};r.push({success:i,failure:u,scope:a||this}),(s=function(){var n=li(t);if(t.length=0,ci(n,function(t){var n,r,i,a,u,f,d;2!==e[t]?3!==e[t]?1!==e[t]&&(e[t]=1,o++,n=t,r=function(){e[t]=2,o--,l("success",t),s()},i=function(){e[t]=3,o--,c.push(t),l("failure",t),s()},d=function(){f.remove(u),a&&(a.onreadystatechange=a.onload=a=null),r()},u=(f=si).uniqueId(),(a=document.createElement("script")).id=u,a.type="text/javascript",a.src=Dt._addCacheSuffix(n),"onreadystatechange"in a?a.onreadystatechange=function(){/loaded|complete/.test(a.readyState)&&d()}:a.onload=d,a.onerror=function(){fi(i)?i():"undefined"!=typeof console&&console.log&&console.log("Failed to load script: "+n)},(document.getElementsByTagName("head")[0]||document.body).appendChild(a)):l("failure",t):l("success",t)}),!o){var i=r.slice(0);r.length=0,ci(i,function(e){0===c.length?fi(e.success)&&e.success.call(e.scope):fi(e.failure)&&e.failure.call(e.scope,c)})}})()}};di.ScriptLoader=new di;var mi=Dt.each,pi=function(){this.items=[],this.urls={},this.lookup={},this._listeners=[]};pi.prototype={get:function(e){return this.lookup[e]?this.lookup[e].instance:undefined},dependencies:function(e){var t;return this.lookup[e]&&(t=this.lookup[e].dependencies),t||[]},requireLangPack:function(e,t){var n=pi.language;if(n&&!1!==pi.languageLoad){if(t)if(-1!==(t=","+t+",").indexOf(","+n.substr(0,2)+","))n=n.substr(0,2);else if(-1===t.indexOf(","+n+","))return;di.ScriptLoader.add(this.urls[e]+"/langs/"+n+".js")}},add:function(e,t,n){this.items.push(t),this.lookup[e]={instance:t,dependencies:n};var r=M.partition(this._listeners,function(t){return t.name===e});return this._listeners=r.fail,mi(r.pass,function(e){e.callback()}),t},remove:function(e){delete this.urls[e],delete this.lookup[e]},createUrl:function(e,t){return"object"==typeof t?t:{prefix:e.prefix,resource:t,suffix:e.suffix}},addComponents:function(e,t){var n=this.urls[e];mi(t,function(e){di.ScriptLoader.add(n+"/"+e)})},load:function(e,t,n,r,o){var i=this,a=t,u=function(){var o=i.dependencies(e);mi(o,function(e){var n=i.createUrl(t,e);i.load(n.resource,n,undefined,undefined)}),n&&(r?n.call(r):n.call(di))};i.urls[e]||("object"==typeof t&&(a=t.prefix+t.resource+t.suffix),0!==a.indexOf("/")&&-1===a.indexOf("://")&&(a=pi.baseURL+"/"+a),i.urls[e]=a.substring(0,a.lastIndexOf("/")),i.lookup[e]?u():di.ScriptLoader.add(a,u,r,o))},waitFor:function(e,t){this.lookup.hasOwnProperty(e)?t():this._listeners.push({name:e,callback:t})}},pi.PluginManager=new pi,pi.ThemeManager=new pi;var gi,hi="\ufeff",vi=function(e){return e===hi},yi=hi,bi=function(e){return e.replace(new RegExp(hi,"g"),"")},Ci=So.isElement,xi=So.isText,wi=function(e){return xi(e)&&(e=e.parentNode),Ci(e)&&e.hasAttribute("data-mce-caret")},Ni=function(e){return xi(e)&&vi(e.data)},Ei=function(e){return wi(e)||Ni(e)},Si=function(e){return e.firstChild!==e.lastChild||!So.isBr(e.firstChild)},ki=function(e){var t=e.container();return e&&So.isText(t)&&t.data.charAt(e.offset())===yi},Ti=function(e){var t=e.container();return e&&So.isText(t)&&t.data.charAt(e.offset()-1)===yi},Ai=function(e,t,n){var r,o,i;return(r=t.ownerDocument.createElement(e)).setAttribute("data-mce-caret",n?"before":"after"),r.setAttribute("data-mce-bogus","all"),r.appendChild(((i=document.createElement("br")).setAttribute("data-mce-bogus","1"),i)),o=t.parentNode,n?o.insertBefore(r,t):t.nextSibling?o.insertBefore(r,t.nextSibling):o.appendChild(r),r},_i=function(e){return xi(e)&&e.data[0]===yi},Ri=function(e){return xi(e)&&e.data[e.data.length-1]===yi},Bi=function(e){return e&&e.hasAttribute("data-mce-caret")?(t=e.getElementsByTagName("br"),n=t[t.length-1],So.isBogus(n)&&n.parentNode.removeChild(n),e.removeAttribute("data-mce-caret"),e.removeAttribute("data-mce-bogus"),e.removeAttribute("style"),e.removeAttribute("_moz_abspos"),e):null;var t,n},Di=So.isContentEditableTrue,Oi=So.isContentEditableFalse,Pi=So.isBr,Li=So.isText,Ii=So.matchNodeNames("script style textarea"),Mi=So.matchNodeNames("img input textarea hr iframe video audio object"),Fi=So.matchNodeNames("table"),zi=Ei,Ui=function(e){return!zi(e)&&(Li(e)?!Ii(e.parentNode):Mi(e)||Pi(e)||Fi(e)||Oi(e))},qi=function(e,t){return Ui(e)&&function(e,t){for(e=e.parentNode;e&&e!==t;e=e.parentNode){if(Oi(e))return!1;if(Di(e))return!0}return!0}(e,t)},Vi=Math.round,Hi=function(e){return e?{left:Vi(e.left),top:Vi(e.top),bottom:Vi(e.bottom),right:Vi(e.right),width:Vi(e.width),height:Vi(e.height)}:{left:0,top:0,bottom:0,right:0,width:0,height:0}},ji=function(e,t){return e=Hi(e),t?e.right=e.left:(e.left=e.left+e.width,e.right=e.left),e.width=0,e},$i=function(e,t,n){return e>=0&&e<=Math.min(t.height,n.height)/2},Wi=function(e,t){return e.bottom-e.height/2<t.top||!(e.top>t.bottom)&&$i(t.top-e.bottom,e,t)},Ki=function(e,t){return e.top>t.bottom||!(e.bottom<t.top)&&$i(t.bottom-e.top,e,t)},Xi=function(e){var t=e.startContainer,n=e.startOffset;return t.hasChildNodes()&&e.endOffset===n+1?t.childNodes[n]:null},Yi=function(e,t){return 1===e.nodeType&&e.hasChildNodes()&&(t>=e.childNodes.length&&(t=e.childNodes.length-1),e=e.childNodes[t]),e},Gi=new RegExp("[\u0300-\u036f\u0483-\u0487\u0488-\u0489\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u0610-\u061a\u064b-\u065f\u0670\u06d6-\u06dc\u06df-\u06e4\u06e7-\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0859-\u085b\u08e3-\u0902\u093a\u093c\u0941-\u0948\u094d\u0951-\u0957\u0962-\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2-\u09e3\u0a01-\u0a02\u0a3c\u0a41-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a70-\u0a71\u0a75\u0a81-\u0a82\u0abc\u0ac1-\u0ac5\u0ac7-\u0ac8\u0acd\u0ae2-\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62-\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c00\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c62-\u0c63\u0c81\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc-\u0ccd\u0cd5-\u0cd6\u0ce2-\u0ce3\u0d01\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62-\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb-\u0ebc\u0ec8-\u0ecd\u0f18-\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86-\u0f87\u0f8d-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039-\u103a\u103d-\u103e\u1058-\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085-\u1086\u108d\u109d\u135d-\u135f\u1712-\u1714\u1732-\u1734\u1752-\u1753\u1772-\u1773\u17b4-\u17b5\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927-\u1928\u1932\u1939-\u193b\u1a17-\u1a18\u1a1b\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1ab0-\u1abd\u1abe\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80-\u1b81\u1ba2-\u1ba5\u1ba8-\u1ba9\u1bab-\u1bad\u1be6\u1be8-\u1be9\u1bed\u1bef-\u1bf1\u1c2c-\u1c33\u1c36-\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1cf4\u1cf8-\u1cf9\u1dc0-\u1df5\u1dfc-\u1dff\u200c-\u200d\u20d0-\u20dc\u20dd-\u20e0\u20e1\u20e2-\u20e4\u20e5-\u20f0\u2cef-\u2cf1\u2d7f\u2de0-\u2dff\u302a-\u302d\u302e-\u302f\u3099-\u309a\ua66f\ua670-\ua672\ua674-\ua67d\ua69e-\ua69f\ua6f0-\ua6f1\ua802\ua806\ua80b\ua825-\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\ua9e5\uaa29-\uaa2e\uaa31-\uaa32\uaa35-\uaa36\uaa43\uaa4c\uaa7c\uaab0\uaab2-\uaab4\uaab7-\uaab8\uaabe-\uaabf\uaac1\uaaec-\uaaed\uaaf6\uabe5\uabe8\uabed\ufb1e\ufe00-\ufe0f\ufe20-\ufe2f\uff9e-\uff9f]"),Ji=function(e){return"string"==typeof e&&e.charCodeAt(0)>=768&&Gi.test(e)},Qi=[].slice,Zi=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=Qi.call(arguments);return r.length-1>=e.length?e.apply(this,r.slice(1)):function(){var e=r.concat([].slice.call(arguments));return Zi.apply(this,e)}},ea={constant:function(e){return function(){return e}},negate:function(e){return function(t){return!e(t)}},and:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Qi.call(arguments);return function(e){for(var t=0;t<n.length;t++)if(!n[t](e))return!1;return!0}},or:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Qi.call(arguments);return function(e){for(var t=0;t<n.length;t++)if(n[t](e))return!0;return!1}},curry:Zi,compose:function(e,t){return function(n){return e(t(n))}},noop:function(){}},ta=So.isElement,na=Ui,ra=So.matchStyleValues("display","block table"),oa=So.matchStyleValues("float","left right"),ia=ea.and(ta,na,ea.negate(oa)),aa=ea.negate(So.matchStyleValues("white-space","pre pre-line pre-wrap")),ua=So.isText,sa=So.isBr,ca=ui.nodeIndex,la=Yi,fa=function(e){return"createRange"in e?e.createRange():ui.DOM.createRng()},da=function(e){return e&&/[\r\n\t ]/.test(e)},ma=function(e){return!!e.setStart&&!!e.setEnd},pa=function(e){var t,n=e.startContainer,r=e.startOffset;return!!(da(e.toString())&&aa(n.parentNode)&&So.isText(n)&&(t=n.data,da(t[r-1])||da(t[r+1])))},ga=function(e){return 0===e.left&&0===e.right&&0===e.top&&0===e.bottom},ha=function(e){var t,n,r,o,i,a,u,s;return t=(n=e.getClientRects()).length>0?Hi(n[0]):Hi(e.getBoundingClientRect()),!ma(e)&&sa(e)&&ga(t)?(i=(r=e).ownerDocument,a=fa(i),u=i.createTextNode("\xa0"),(s=r.parentNode).insertBefore(u,r),a.setStart(u,0),a.setEnd(u,1),o=Hi(a.getBoundingClientRect()),s.removeChild(u),o):ga(t)&&ma(e)?function(e){var t=e.startContainer,n=e.endContainer,r=e.startOffset,o=e.endOffset;if(t===n&&So.isText(n)&&0===r&&1===o){var i=e.cloneRange();return i.setEndAfter(n),ha(i)}return null}(e):t},va=function(e,t){var n=ji(e,t);return n.width=1,n.right=n.left+1,n},ya=function(e){var t,n,r=[],o=function(e){var t,n;0!==e.height&&(r.length>0&&(t=e,n=r[r.length-1],t.left===n.left&&t.top===n.top&&t.bottom===n.bottom&&t.right===n.right)||r.push(e))},i=function(e,t){var n=fa(e.ownerDocument);if(t<e.data.length){if(Ji(e.data[t]))return r;if(Ji(e.data[t-1])&&(n.setStart(e,t),n.setEnd(e,t+1),!pa(n)))return o(va(ha(n),!1)),r}t>0&&(n.setStart(e,t-1),n.setEnd(e,t),pa(n)||o(va(ha(n),!1))),t<e.data.length&&(n.setStart(e,t),n.setEnd(e,t+1),pa(n)||o(va(ha(n),!0)))};if(ua(e.container()))return i(e.container(),e.offset()),r;if(ta(e.container()))if(e.isAtEnd())n=la(e.container(),e.offset()),ua(n)&&i(n,n.data.length),ia(n)&&!sa(n)&&o(va(ha(n),!1));else{if(n=la(e.container(),e.offset()),ua(n)&&i(n,0),ia(n)&&e.isAtEnd())return o(va(ha(n),!1)),r;t=la(e.container(),e.offset()-1),ia(t)&&!sa(t)&&(ra(t)||ra(n)||!ia(n))&&o(va(ha(t),!1)),ia(n)&&o(va(ha(n),!0))}return r};function ba(e,t,n){var r=function(){return n||(n=ya(ba(e,t))),n};return{container:ea.constant(e),offset:ea.constant(t),toRange:function(){var n;return(n=fa(e.ownerDocument)).setStart(e,t),n.setEnd(e,t),n},getClientRects:r,isVisible:function(){return r().length>0},isAtStart:function(){return ua(e),0===t},isAtEnd:function(){return ua(e)?t>=e.data.length:t>=e.childNodes.length},isEqual:function(n){return n&&e===n.container()&&t===n.offset()},getNode:function(n){return la(e,n?t-1:t)}}}(gi=ba||(ba={})).fromRangeStart=function(e){return gi(e.startContainer,e.startOffset)},gi.fromRangeEnd=function(e){return gi(e.endContainer,e.endOffset)},gi.after=function(e){return gi(e.parentNode,ca(e)+1)},gi.before=function(e){return gi(e.parentNode,ca(e))},gi.isAtStart=function(e){return!!e&&e.isAtStart()},gi.isAtEnd=function(e){return!!e&&e.isAtEnd()},gi.isTextPosition=function(e){return!!e&&So.isText(e.container())};var Ca,xa,wa=ba,Na=So.isElement,Ea=So.isText,Sa=function(e){var t=e.parentNode;t&&t.removeChild(e)},ka=function(e,t){0===t.length?Sa(e):e.nodeValue=t},Ta=function(e){var t=bi(e);return{count:e.length-t.length,text:t}},Aa=function(e,t){return Ba(e),t},_a=function(e,t){return Ea(e)&&t.container()===e?(r=t,o=Ta((n=e).data.substr(0,r.offset())),i=Ta(n.data.substr(r.offset())),(a=o.text+i.text).length>0?(ka(n,a),wa(n,r.offset()-o.count)):r):Aa(e,t);var n,r,o,i,a},Ra=function(e,t){return t.container()===e.parentNode?(n=e,o=(r=t).container(),i=M.indexOf(o.childNodes,n).map(function(e){return e<r.offset()?wa(o,r.offset()-1):r}).getOr(r),Ba(n),i):Aa(e,t);var n,r,o,i},Ba=function(e){if(Na(e)&&Ei(e)&&(Si(e)?e.removeAttribute("data-mce-caret"):Sa(e)),Ea(e)){var t=bi(function(e){try{return e.nodeValue}catch(t){return""}}(e));ka(e,t)}},Da={removeAndReposition:function(e,t){return wa.isTextPosition(t)?_a(e,t):Ra(e,t)},remove:Ba},Oa=function(e){return wa.isTextPosition(e)?0===e.offset():Ui(e.getNode())},Pa=function(e){if(wa.isTextPosition(e)){var t=e.container();return e.offset()===t.data.length}return Ui(e.getNode(!0))},La=function(e,t){return!wa.isTextPosition(e)&&!wa.isTextPosition(t)&&e.getNode()===t.getNode(!0)},Ia=function(e,t,n){return e?!La(t,n)&&(r=t,!(!wa.isTextPosition(r)&&So.isBr(r.getNode())))&&Pa(t)&&Oa(n):!La(n,t)&&Oa(t)&&Pa(n);var r},Ma=function(e,t,n){var r=ls(t);return E.from(e?r.next(n):r.prev(n))},Fa=function(e,t){var n,r,o,i,a,u=e?t.firstChild:t.lastChild;return So.isText(u)?E.some(wa(u,e?0:u.data.length)):u?Ui(u)?E.some(e?wa.before(u):(a=u,So.isBr(a)?wa.before(a):wa.after(a))):(r=t,o=u,i=(n=e)?wa.before(o):wa.after(o),Ma(n,r,i)):E.none()},za={fromPosition:Ma,nextPosition:y.curry(Ma,!0),prevPosition:y.curry(Ma,!1),navigate:function(e,t,n){return Ma(e,t,n).bind(function(r){return Pu(n,r,t)&&Ia(e,n,r)?Ma(e,t,r):E.some(r)})},positionIn:Fa,firstPositionIn:y.curry(Fa,!0),lastPositionIn:y.curry(Fa,!1)},Ua=So.isContentEditableTrue,qa=So.isContentEditableFalse,Va=function(e,t,n,r,o){return t._selectionOverrides.showCaret(e,n,r,o)},Ha=function(e,t){var n,r;return e.fire("BeforeObjectSelected",{target:t}).isDefaultPrevented()?null:((r=(n=t).ownerDocument.createRange()).selectNode(n),r)},ja=function(e,t,n){var r,o;return t=Uu(1,e.getBody(),t),r=wa.fromRangeStart(t),qa(r.getNode())?Va(1,e,r.getNode(),!r.isAtEnd(),!1):qa(r.getNode(!0))?Va(1,e,r.getNode(!0),!1,!1):(o=e.dom.getParent(r.getNode(),function(e){return qa(e)||Ua(e)}),qa(o)?Va(1,e,o,!1,n):null)},$a=function(e,t,n){return t&&t.collapsed&&ja(e,t,n)||t},Wa=function(e,t){for(var n=[],r=0;r<e.length;r++){var o=e[r];if(!o.isSome())return E.none();n.push(o.getOrDie())}return E.some(t.apply(null,n))};(xa=Ca||(Ca={}))[xa.Br=0]="Br",xa[xa.Block=1]="Block",xa[xa.Wrap=2]="Wrap",xa[xa.Eol=3]="Eol";var Ka,Xa,Ya=function(e,t){return e===Ka.Backwards?t.reverse():t},Ga=function(e,t,n,r){for(var o,i,a,u,s,c,l=ls(n),f=r,d=[];f&&(s=l,c=f,o=t===Ka.Forwards?s.next(c):s.prev(c));){if(So.isBr(o.getNode(!1)))return t===Ka.Forwards?{positions:Ya(t,d).concat([o]),breakType:Ca.Br,breakAt:E.some(o)}:{positions:Ya(t,d),breakType:Ca.Br,breakAt:E.some(o)};if(o.isVisible()){if(e(f,o)){var m=(i=t,a=f,u=o,So.isBr(u.getNode(i===Ka.Forwards))?Ca.Br:!1===Pu(a,u)?Ca.Block:Ca.Wrap);return{positions:Ya(t,d),breakType:m,breakAt:E.some(o)}}d.push(o),f=o}else f=o}return{positions:Ya(t,d),breakType:Ca.Eol,breakAt:E.none()}},Ja=function(e,t,n,r){return t(n,r).breakAt.map(function(r){var o=t(n,r).positions;return e===Ka.Backwards?o.concat(r):[r].concat(o)}).getOr([])},Qa=function(e,t){return M.foldl(e,function(e,n){return e.fold(function(){return E.some(n)},function(r){return Wa([M.head(r.getClientRects()),M.head(n.getClientRects())],function(e,o){var i=Math.abs(t-e.left);return Math.abs(t-o.left)<=i?n:r}).or(e)})},E.none())},Za=function(e,t){return M.head(t.getClientRects()).bind(function(t){return Qa(e,t.left)})},eu=y.curry(Ga,function(e,t){return Wa([M.head(t.getClientRects()),M.last(e.getClientRects())],Wi).getOr(!1)},-1),tu=y.curry(Ga,function(e,t){return Wa([M.last(t.getClientRects()),M.head(e.getClientRects())],function(e,t){return Ki(e,t)}).getOr(!1)},1),nu=y.curry(Ja,-1,eu),ru=y.curry(Ja,1,tu),ou=function(e,t){return Tr.all(t,e)},iu=function(e,t,n,r,o){var i,a,u,s,c,l=ou(Fn.fromDom(n),"td,th").map(function(e){return e.dom()}),f=M.filter((i=e,a=l,M.bind(a,function(e){var t,n,r=(t=e.getBoundingClientRect(),n=-1,{left:t.left-n,top:t.top-n,right:t.right+2*n,bottom:t.bottom+2*n,width:t.width+n,height:t.height+n});return[{x:r.left,y:i(r),cell:e},{x:r.right,y:i(r),cell:e}]})),function(e){return t(e,o)});return(u=f,s=r,c=o,M.foldl(u,function(e,t){return e.fold(function(){return E.some(t)},function(e){var n=Math.sqrt(Math.abs(e.x-s)+Math.abs(e.y-c)),r=Math.sqrt(Math.abs(t.x-s)+Math.abs(t.y-c));return E.some(r<n?t:e)})},E.none())).map(function(e){return e.cell})},au=y.curry(iu,function(e){return e.bottom},function(e,t){return e.y<t}),uu=y.curry(iu,function(e){return e.top},function(e,t){return e.y>t}),su=function(e,t){return M.head(t.getClientRects()).bind(function(t){return au(e,t.left,t.top)}).bind(function(e){return Za((n=e,za.lastPositionIn(n).map(function(e){return eu(n,e).positions.concat(e)}).getOr([])),t);var n})},cu=function(e,t){return M.last(t.getClientRects()).bind(function(t){return uu(e,t.left,t.top)}).bind(function(e){return Za((n=e,za.firstPositionIn(n).map(function(e){return[e].concat(tu(n,e).positions)}).getOr([])),t);var n})},lu=In.detect().browser,fu=function(){return lu.isIE()||lu.isEdge()||lu.isFirefox()},du=function(e,t,n){var r=e(t,n);return r.breakType===Ca.Wrap&&0===r.positions.length?r.breakAt.map(function(n){return e(t,n).breakAt.isNone()}).getOr(!0):r.breakAt.isNone()},mu=ea.curry(du,eu),pu=ea.curry(du,tu),gu=function(e,t,n,r){var o,i,a,u,s=e.selection.getRng(),c=t?1:-1;if(fu()&&(o=t,i=s,a=n,u=wa.fromRangeStart(i),za.positionIn(!o,a).map(function(e){return e.isEqual(u)}).getOr(!1))){var l=Va(c,e,n,!t,!0);return e.selection.setRng(l),!0}return!1},hu=function(e,t,n,r){var o,i,a,u,s,c,l=e.selection.getRng(),f=wa.fromRangeStart(l),d=e.getBody();if(!t&&mu(r,f)){var m=(u=d,su(s=n,c=f).orThunk(function(){return M.head(c.getClientRects()).bind(function(e){return Qa(nu(u,wa.before(s)),e.left)})}).getOr(wa.before(s)));return e.selection.setRng(m.toRange()),!0}return!(!t||!pu(r,f))&&(o=d,m=cu(i=n,a=f).orThunk(function(){return M.head(a.getClientRects()).bind(function(e){return Qa(ru(o,wa.after(i)),e.left)})}).getOr(wa.after(i)),e.selection.setRng(m.toRange()),!0)},vu=function(e,t){return function(){return E.from(e.dom.getParent(e.selection.getNode(),"td,th")).bind(function(n){return E.from(e.dom.getParent(n,"table")).map(function(n){return gu(e,t,n)})}).getOr(!1)}},yu=function(e,t){return function(){return E.from(e.dom.getParent(e.selection.getNode(),"td,th")).bind(function(n){return E.from(e.dom.getParent(n,"table")).map(function(r){return hu(e,t,r,n)})}).getOr(!1)}},bu=So.isContentEditableFalse,Cu=function(e,t,n){var r,o,i=null,a=function(){!function(e){var t,n,r,o,i;for(t=Jt("*[contentEditable=false]",e),o=0;o<t.length;o++)r=(n=t[o]).previousSibling,Ri(r)&&(1===(i=r.data).length?r.parentNode.removeChild(r):r.deleteData(i.length-1,1)),r=n.nextSibling,_i(r)&&(1===(i=r.data).length?r.parentNode.removeChild(r):r.deleteData(0,1))}(e),o&&(Da.remove(o),o=null),i&&(i.remove(),i=null),clearInterval(r)},u=function(){r=ve.setInterval(function(){n()?Jt("div.mce-visual-caret",e).toggleClass("mce-visual-caret-hidden"):Jt("div.mce-visual-caret",e).addClass("mce-visual-caret-hidden")},500)};return{show:function(n,r){var s,c,l,f,d,m,p,g,h,v,y,b;return a(),l=r,So.isElement(l)&&/^(TD|TH)$/i.test(l.tagName)?null:t(r)?(o=Ai("p",r,n),f=e,m=n,b=ji((d=r).getBoundingClientRect(),m),"BODY"===f.tagName?(p=f.ownerDocument.documentElement,g=f.scrollLeft||p.scrollLeft,h=f.scrollTop||p.scrollTop):(y=f.getBoundingClientRect(),g=f.scrollLeft-y.left,h=f.scrollTop-y.top),b.left+=g,b.right+=g,b.top+=h,b.bottom+=h,b.width=1,(v=d.offsetWidth-d.clientWidth)>0&&(m&&(v*=-1),b.left+=v,b.right+=v),s=b,Jt(o).css("top",s.top),i=Jt('<div class="mce-visual-caret" data-mce-bogus="all"></div>').css(s).appendTo(e),n&&i.addClass("mce-visual-caret-before"),u(),(c=r.ownerDocument.createRange()).setStart(o,0),c.setEnd(o,0),c):(o=function(e,t){var n,r,o;if(r=e.ownerDocument.createTextNode(yi),o=e.parentNode,t){if(n=e.previousSibling,xi(n)){if(Ei(n))return n;if(Ri(n))return n.splitText(n.data.length-1)}o.insertBefore(r,e)}else{if(n=e.nextSibling,xi(n)){if(Ei(n))return n;if(_i(n))return n.splitText(1),n}e.nextSibling?o.insertBefore(r,e.nextSibling):o.appendChild(r)}return r}(r,n),c=r.ownerDocument.createRange(),bu(o.nextSibling)?(c.setStart(o,0),c.setEnd(o,0)):(c.setStart(o,1),c.setEnd(o,1)),c)},hide:a,getCss:function(){return".mce-visual-caret {position: absolute;background-color: black;background-color: currentcolor;}.mce-visual-caret-hidden {display: none;}*[data-mce-caret] {position: absolute;left: -1000px;right: auto;top: 0;margin: 0;padding: 0;}"},destroy:function(){return ve.clearInterval(r)}}},xu=function(e){return bu(e)||So.isTable(e)&&fu()},wu=So.isContentEditableFalse,Nu=So.matchStyleValues("display","block table table-cell table-caption list-item"),Eu=Ei,Su=wi,ku=ea.curry,Tu=So.isElement,Au=Ui,_u=function(e){return e>0},Ru=function(e){return e<0},Bu=function(e,t){for(var n;n=e(t);)if(!Su(n))return n;return null},Du=function(e,t,n,r,o){var i=new Zr(e,r);if(Ru(t)){if((wu(e)||Su(e))&&n(e=Bu(i.prev,!0)))return e;for(;e=Bu(i.prev,o);)if(n(e))return e}if(_u(t)){if((wu(e)||Su(e))&&n(e=Bu(i.next,!0)))return e;for(;e=Bu(i.next,o);)if(n(e))return e}return null},Ou=function(e,t){for(;e&&e!==t;){if(Nu(e))return e;e=e.parentNode}return null},Pu=function(e,t,n){return Ou(e.container(),n)===Ou(t.container(),n)},Lu=function(e,t){var n,r;return t?(n=t.container(),r=t.offset(),Tu(n)?n.childNodes[r+e]:null):null},Iu=function(e,t){var n=t.ownerDocument.createRange();return e?(n.setStartBefore(t),n.setEndBefore(t)):(n.setStartAfter(t),n.setEndAfter(t)),n},Mu=function(e,t,n){var r,o,i,a;for(o=e?"previousSibling":"nextSibling";n&&n!==t;){if(r=n[o],Eu(r)&&(r=r[o]),wu(r)){if(a=n,Ou(r,i=t)===Ou(a,i))return r;break}if(Au(r))break;n=n.parentNode}return null},Fu=ku(Iu,!0),zu=ku(Iu,!1),Uu=function(e,t,n){var r,o,i,a,u=ku(Mu,!0,t),s=ku(Mu,!1,t);if(o=n.startContainer,i=n.startOffset,wi(o)){if(Tu(o)||(o=o.parentNode),"before"===(a=o.getAttribute("data-mce-caret"))&&(r=o.nextSibling,xu(r)))return Fu(r);if("after"===a&&(r=o.previousSibling,xu(r)))return zu(r)}if(!n.collapsed)return n;if(So.isText(o)){if(Eu(o)){if(1===e){if(r=s(o))return Fu(r);if(r=u(o))return zu(r)}if(-1===e){if(r=u(o))return zu(r);if(r=s(o))return Fu(r)}return n}if(Ri(o)&&i>=o.data.length-1)return 1===e&&(r=s(o))?Fu(r):n;if(_i(o)&&i<=1)return-1===e&&(r=u(o))?zu(r):n;if(i===o.data.length)return(r=s(o))?Fu(r):n;if(0===i)return(r=u(o))?zu(r):n}return n},qu=function(e,t){var n=Lu(e,t);return wu(n)&&!So.isBogusAll(n)},Vu=function(e,t){return So.isTable(Lu(e,t))},Hu=function(e,t){return E.from(Lu(e?0:-1,t)).filter(wu)},ju=function(e,t,n){var r=Uu(e,t,n);return-1===e?ba.fromRangeStart(r):ba.fromRangeEnd(r)},$u=ku(qu,0),Wu=ku(qu,-1),Ku=ku(Vu,0),Xu=ku(Vu,-1);(Xa=Ka||(Ka={}))[Xa.Backwards=-1]="Backwards",Xa[Xa.Forwards=1]="Forwards";var Yu,Gu,Ju,Qu,Zu,es=So.isContentEditableFalse,ts=So.isText,ns=So.isElement,rs=So.isBr,os=Ui,is=function(e){return Mi(e)||!!Oi(t=e)&&!0!==Tt.reduce(t.getElementsByTagName("*"),function(e,t){return e||Di(t)},!1);var t},as=qi,us=function(e,t){return e.hasChildNodes()&&t<e.childNodes.length?e.childNodes[t]:null},ss=function(e,t){if(_u(e)){if(os(t.previousSibling)&&!ts(t.previousSibling))return wa.before(t);if(ts(t))return wa(t,0)}if(Ru(e)){if(os(t.nextSibling)&&!ts(t.nextSibling))return wa.after(t);if(ts(t))return wa(t,t.data.length)}return Ru(e)?rs(t)?wa.before(t):wa.after(t):wa.before(t)},cs=function(e,t,n){var r,o,i,a,u;if(!ns(n)||!t)return null;if(t.isEqual(wa.after(n))&&n.lastChild){if(u=wa.after(n.lastChild),Ru(e)&&os(n.lastChild)&&ns(n.lastChild))return rs(n.lastChild)?wa.before(n.lastChild):u}else u=t;var s,c,l,f=u.container(),d=u.offset();if(ts(f)){if(Ru(e)&&d>0)return wa(f,--d);if(_u(e)&&d<f.length)return wa(f,++d);r=f}else{if(Ru(e)&&d>0&&(o=us(f,d-1),os(o)))return!is(o)&&(i=Du(o,e,as,o))?ts(i)?wa(i,i.data.length):wa.after(i):ts(o)?wa(o,o.data.length):wa.before(o);if(_u(e)&&d<f.childNodes.length&&(o=us(f,d),os(o)))return s=o,c=n,So.isBr(s)&&(l=cs(1,wa.after(s),c))&&!Pu(wa.before(s),wa.before(l),c)?cs(e,wa.after(o),n):!is(o)&&(i=Du(o,e,as,o))?ts(i)?wa(i,0):wa.before(i):ts(o)?wa(o,0):wa.after(o);r=o||u.getNode()}return(_u(e)&&u.isAtEnd()||Ru(e)&&u.isAtStart())&&(r=Du(r,e,ea.constant(!0),n,!0),as(r,n))?ss(e,r):(o=Du(r,e,as,n),!(a=Tt.last(Tt.filter(function(e,t){for(var n=[];e&&e!==t;)n.push(e),e=e.parentNode;return n}(f,n),es)))||o&&a.contains(o)?o?ss(e,o):null:u=_u(e)?wa.after(a):wa.before(a))},ls=function(e){return{next:function(t){return cs(Ka.Forwards,t,e)},prev:function(t){return cs(Ka.Backwards,t,e)}}},fs=function(e){return Dt.grep(e.childNodes,function(e){return"LI"===e.nodeName})},ds=function(e){return e&&e.firstChild&&e.firstChild===e.lastChild&&("\xa0"===(t=e.firstChild).data||So.isBr(t));var t},ms=function(e){return e.length>0&&(!(t=e[e.length-1]).firstChild||ds(t))?e.slice(0,-1):e;var t},ps=function(e,t){var n=e.getParent(t,e.isBlock);return n&&"LI"===n.nodeName?n:null},gs=function(e,t){var n=wa.after(e),r=ls(t).prev(n);return r?r.toRange():null},hs=function(e,t,n){var r,o,i,a,u=e.parentNode;return Dt.each(t,function(t){u.insertBefore(t,e)}),r=e,o=n,i=wa.before(r),(a=ls(o).next(i))?a.toRange():null},vs=function(e,t){var n,r,o,i,a,u,s=t.firstChild,c=t.lastChild;return s&&"meta"===s.name&&(s=s.next),c&&"mce_marker"===c.attr("id")&&(c=c.prev),r=c,u=(n=e).getNonEmptyElements(),r&&(r.isEmpty(u)||(o=r,n.getBlockElements()[o.name]&&(a=o).firstChild&&a.firstChild===a.lastChild&&("br"===(i=o.firstChild).name||"\xa0"===i.value)))&&(c=c.prev),!(!s||s!==c||"ul"!==s.name&&"ol"!==s.name)},ys=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m,p,g,h,v,y,b,C,x,w,N=(o=t,i=r,c=e.serialize(i),l=o.createFragment(c),u=(a=l).firstChild,s=a.lastChild,u&&"META"===u.nodeName&&u.parentNode.removeChild(u),s&&"mce_marker"===s.id&&s.parentNode.removeChild(s),a),E=ps(t,n.startContainer),S=ms(fs(N.firstChild)),k=t.getRoot(),T=function(e){var r=wa.fromRangeStart(n),o=ls(t.getRoot()),i=1===e?o.prev(r):o.next(r);return!i||ps(t,i.getNode())!==E};return T(1)?hs(E,S,k):T(2)?(f=E,d=S,m=k,t.insertAfter(d.reverse(),f),gs(d[0],m)):(g=S,h=k,v=p=E,b=(y=n).cloneRange(),C=y.cloneRange(),b.setStartBefore(v),C.setEndAfter(v),x=[b.cloneContents(),C.cloneContents()],(w=p.parentNode).insertBefore(x[0],p),Dt.each(g,function(e){w.insertBefore(e,p)}),w.insertBefore(x[1],p),w.removeChild(p),gs(g[g.length-1],h))},bs=function(e,t){return!!ps(e,t)},Cs=So.isText,xs=So.isBogus,ws=ui.nodeIndex,Ns=function(e){var t=e.parentNode;return xs(t)?Ns(t):t},Es=function(e){return e?Tt.reduce(e.childNodes,function(e,t){return xs(t)&&"BR"!==t.nodeName?e=e.concat(Es(t)):e.push(t),e},[]):[]},Ss=function(e){return function(t){return e===t}},ks=function(e){var t,n,r,o;return(Cs(e)?"text()":e.nodeName.toLowerCase())+"["+(n=Es(Ns(t=e)),r=Tt.findIndex(n,Ss(t),t),n=n.slice(0,r+1),o=Tt.reduce(n,function(e,t,r){return Cs(t)&&Cs(n[r-1])&&e++,e},0),n=Tt.filter(n,So.matchNodeNames(t.nodeName)),(r=Tt.findIndex(n,Ss(t),t))-o)+"]"},Ts=function(e,t){var n,r,o,i,a,u=[];return n=t.container(),r=t.offset(),Cs(n)?o=function(e,t){for(;(e=e.previousSibling)&&Cs(e);)t+=e.data.length;return t}(n,r):(r>=(i=n.childNodes).length?(o="after",r=i.length-1):o="before",n=i[r]),u.push(ks(n)),a=function(e,t,n){var r=[];for(t=t.parentNode;!(t===e||n&&n(t));t=t.parentNode)r.push(t);return r}(e,n),a=Tt.filter(a,ea.negate(So.isBogus)),(u=u.concat(Tt.map(a,function(e){return ks(e)}))).reverse().join("/")+","+o},As=function(e,t){var n,r,o;return t?(t=(n=t.split(","))[0].split("/"),o=n.length>1?n[1]:"before",(r=Tt.reduce(t,function(e,t){return(t=/([\w\-\(\)]+)\[([0-9]+)\]/.exec(t))?("text()"===t[1]&&(t[1]="#text"),n=e,r=t[1],o=parseInt(t[2],10),i=Es(n),i=Tt.filter(i,function(e,t){return!Cs(e)||!Cs(i[t-1])}),(i=Tt.filter(i,So.matchNodeNames(r)))[o]):null;var n,r,o,i},e))?Cs(r)?function(e,t){for(var n,r=e,o=0;Cs(r);){if(n=r.data.length,t>=o&&t<=o+n){e=r,t-=o;break}if(!Cs(r.nextSibling)){e=r,t=n;break}o+=n,r=r.nextSibling}return Cs(e)&&t>e.data.length&&(t=e.data.length),wa(e,t)}(r,parseInt(o,10)):(o="after"===o?ws(r)+1:ws(r),wa(r.parentNode,o)):null):null},_s=So.isContentEditableFalse,Rs=function(e,t,n,r,o){var i,a=r[o?"startContainer":"endContainer"],u=r[o?"startOffset":"endOffset"],s=[],c=0,l=e.getRoot();for(So.isText(a)?s.push(n?function(e,t,n){var r,o;for(o=e(t.data.slice(0,n)).length,r=t.previousSibling;r&&So.isText(r);r=r.previousSibling)o+=e(r.data).length;return o}(t,a,u):u):(u>=(i=a.childNodes).length&&i.length&&(c=1,u=Math.max(0,i.length-1)),s.push(e.nodeIndex(i[u],n)+c));a&&a!==l;a=a.parentNode)s.push(e.nodeIndex(a,n));return s},Bs=function(e){So.isText(e)&&0===e.data.length&&e.parentNode.removeChild(e)},Ds=function(e,t,n){var r=0;return Dt.each(e.select(t),function(e){if("all"!==e.getAttribute("data-mce-bogus"))return e!==n&&void r++}),r},Os=function(e,t){var n,r,o,i=t?"start":"end";n=e[i+"Container"],r=e[i+"Offset"],So.isElement(n)&&"TR"===n.nodeName&&(n=(o=n.childNodes)[Math.min(t?r:r-1,o.length-1)])&&(r=t?0:n.childNodes.length,e["set"+(t?"Start":"End")](n,r))},Ps=function(e){return Os(e,!0),Os(e,!1),e},Ls=function(e,t){var n;if(So.isElement(e)&&(e=Yi(e,t),_s(e)))return e;if(Ei(e)){if(So.isText(e)&&wi(e)&&(e=e.parentNode),n=e.previousSibling,_s(n))return n;if(n=e.nextSibling,_s(n))return n}},Is=function(e,t,n){var r,o,i,a,u,s,c,l=n.getNode(),f=l?l.nodeName:null,d=n.getRng();return _s(l)||"IMG"===f?{name:f,index:Ds(n.dom,f,l)}:(l=Ls((r=d).startContainer,r.startOffset)||Ls(r.endContainer,r.endOffset))?{name:f=l.tagName,index:Ds(n.dom,f,l)}:(o=e,a=t,u=d,s=(i=n).dom,(c={}).start=Rs(s,o,a,u,!0),i.isCollapsed()||(c.end=Rs(s,o,a,u,!1)),c)},Ms={getBookmark:function(e,t,n){return 2===t?Is(bi,n,e):3===t?(o=(r=e).getRng(),{start:Ts(r.dom.getRoot(),wa.fromRangeStart(o)),end:Ts(r.dom.getRoot(),wa.fromRangeEnd(o))}):t?{rng:e.getRng()}:function(e){var t=e.dom,n=e.getRng(),r=t.uniqueId(),o=e.isCollapsed(),i="overflow:hidden;line-height:0px",a=e.getNode(),u=a.nodeName;if("IMG"===u)return{name:u,index:Ds(t,u,a)};var s=Ps(n.cloneRange());if(!o){s.collapse(!1);var c=t.create("span",{"data-mce-type":"bookmark",id:r+"_end",style:i},"&#xFEFF;");s.insertNode(c),Bs(c.nextSibling)}(n=Ps(n)).collapse(!0);var l=t.create("span",{"data-mce-type":"bookmark",id:r+"_start",style:i},"&#xFEFF;");return n.insertNode(l),Bs(l.previousSibling),e.moveToBookmark({id:r,keep:1}),{id:r}}(e);var r,o},getUndoBookmark:y.curry(Is,y.identity,!0)},Fs=function(e,t){return!e.isBlock(t)||t.innerHTML||de.ie||(t.innerHTML='<br data-mce-bogus="1" />'),t},zs=function(e,t,n,r){var o,i,a,u,s=n[t?"start":"end"],c=e.getRoot();if(s){for(a=s[0],i=c,o=s.length-1;o>=1;o--){if(u=i.childNodes,s[o]>u.length-1)return;i=u[s[o]]}3===i.nodeType&&(a=Math.min(s[0],i.nodeValue.length)),1===i.nodeType&&(a=Math.min(s[0],i.childNodes.length)),t?r.setStart(i,a):r.setEnd(i,a)}return!0},Us=function(e,t,n){var r,o,i,a,u,s,c=e.get(n.id+"_"+t),l=n.keep;if(c){if(r=c.parentNode,l?(r=c.firstChild,o=1):o=e.nodeIndex(c),u=r,s=o,!l){for(a=c.previousSibling,i=c.nextSibling,Dt.each(Dt.grep(c.childNodes),function(e){So.isText(e)&&(e.nodeValue=e.nodeValue.replace(/\uFEFF/g,""))});c=e.get(n.id+"_"+t);)e.remove(c,1);a&&i&&a.nodeType===i.nodeType&&So.isText(a)&&!de.opera&&(o=a.nodeValue.length,a.appendData(i.nodeValue),e.remove(i),u=a,s=o)}return E.some(wa(u,s))}return E.none()},qs=function(e,t){var n,r,o,i,a,u,s,c,l,f,d,m,p,g,h,v=e.dom;if(t){if(Dt.isArray(t.start))return g=t,h=(p=v).createRng(),zs(p,!0,g,h)&&zs(p,!1,g,h)?E.some(h):E.none();if("string"==typeof t.start)return E.some((f=t,d=(l=v).createRng(),m=As(l.getRoot(),f.start),d.setStart(m.container(),m.offset()),m=As(l.getRoot(),f.end),d.setEnd(m.container(),m.offset()),d));if(t.id)return s=Us(o=v,"start",i=t),c=Us(o,"end",i),Wa([s,(a=c,u=s,a.isSome()?a:u)],function(e,t){var n=o.createRng();return n.setStart(Fs(o,e.container()),e.offset()),n.setEnd(Fs(o,t.container()),t.offset()),n});if(t.name)return n=v,r=t,E.from(n.select(r.name)[r.index]).map(function(e){var t=n.createRng();return t.selectNode(e),t});if(t.rng)return E.some(t.rng)}return E.none()},Vs={getBookmark:function(e,t,n){return Ms.getBookmark(e,t,n)},moveToBookmark:function(e,t){qs(e,t).each(function(t){e.setRng(t)})},isBookmarkNode:function(e){return So.isElement(e)&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type")}},Hs=Dt.each,js=function(e){this.compare=function(t,n){if(t.nodeName!==n.nodeName)return!1;var r=function(t){var n={};return Hs(e.getAttribs(t),function(r){var o=r.nodeName.toLowerCase();0!==o.indexOf("_")&&"style"!==o&&0!==o.indexOf("data-")&&(n[o]=e.getAttrib(t,o))}),n},o=function(e,t){var n,r;for(r in e)if(e.hasOwnProperty(r)){if(void 0===(n=t[r]))return!1;if(e[r]!==n)return!1;delete t[r]}for(r in t)if(t.hasOwnProperty(r))return!1;return!0};return!(!o(r(t),r(n))||!o(e.parseStyle(e.getAttrib(t,"style")),e.parseStyle(e.getAttrib(n,"style")))||Vs.isBookmarkNode(t)||Vs.isBookmarkNode(n))}},$s=function(e,t){Fr.parent(e).each(function(n){n.dom().insertBefore(t.dom(),e.dom())})},Ws=function(e,t){e.dom().appendChild(t.dom())},Ks={before:$s,after:function(e,t){Fr.nextSibling(e).fold(function(){Fr.parent(e).each(function(e){Ws(e,t)})},function(e){$s(e,t)})},prepend:function(e,t){Fr.firstChild(e).fold(function(){Ws(e,t)},function(n){e.dom().insertBefore(t.dom(),n.dom())})},append:Ws,appendAt:function(e,t,n){Fr.child(e,n).fold(function(){Ws(e,t)},function(e){$s(e,t)})},wrap:function(e,t){$s(e,t),Ws(t,e)}},Xs=function(e,t){M.each(t,function(t){Ks.before(e,t)})},Ys=function(e,t){M.each(t,function(t){Ks.append(e,t)})},Gs=function(e){var t=e.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},Js={empty:function(e){e.dom().textContent="",M.each(Fr.children(e),function(e){Gs(e)})},remove:Gs,unwrap:function(e){var t=Fr.children(e);t.length>0&&Xs(e,t),Gs(e)}},Qs=(Yu=Yn.isText,Gu="text",Ju=function(e){return Yu(e)?E.from(e.dom().nodeValue):E.none()},Qu=In.detect().browser,{get:function(e){if(!Yu(e))throw new Error("Can only get "+Gu+" value of a "+Gu+" node");return Zu(e).getOr("")},getOption:Zu=Qu.isIE()&&10===Qu.version.major?function(e){try{return Ju(e)}catch(vx){return E.none()}}:Ju,set:function(e,t){if(!Yu(e))throw new Error("Can only set raw "+Gu+" value of a "+Gu+" node");e.dom().nodeValue=t}}),Zs=function(e){return Qs.get(e)},ec=function(e){var t=ou(e,"br"),n=M.filter(function(e){for(var t=[],n=e.dom();n;)t.push(Fn.fromDom(n)),n=n.lastChild;return t}(e).slice(-1),uo);t.length===n.length&&M.each(n,Js.remove)},tc=function(e){Js.empty(e),Ks.append(e,Fn.fromHtml('<br data-mce-bogus="1">'))},nc=function(e){Fr.lastChild(e).each(function(t){Fr.prevSibling(t).each(function(n){io(e)&&uo(t)&&io(n)&&Js.remove(t)})})},rc=Dt.makeMap;function oc(e){var t,n,r,o,i,a=[];return t=(e=e||{}).indent,n=rc(e.indent_before||""),r=rc(e.indent_after||""),o=zo.getEncodeFunc(e.entity_encoding||"raw",e.entities),i="html"===e.element_format,{start:function(e,u,s){var c,l,f,d;if(t&&n[e]&&a.length>0&&(d=a[a.length-1]).length>0&&"\n"!==d&&a.push("\n"),a.push("<",e),u)for(c=0,l=u.length;c<l;c++)f=u[c],a.push(" ",f.name,'="',o(f.value,!0),'"');a[a.length]=!s||i?">":" />",s&&t&&r[e]&&a.length>0&&(d=a[a.length-1]).length>0&&"\n"!==d&&a.push("\n")},end:function(e){var n;a.push("</",e,">"),t&&r[e]&&a.length>0&&(n=a[a.length-1]).length>0&&"\n"!==n&&a.push("\n")},text:function(e,t){e.length>0&&(a[a.length]=t?e:o(e))},cdata:function(e){a.push("<![CDATA[",e,"]]>")},comment:function(e){a.push("\x3c!--",e,"--\x3e")},pi:function(e,n){n?a.push("<?",e," ",o(n),"?>"):a.push("<?",e,"?>"),t&&a.push("\n")},doctype:function(e){a.push("<!DOCTYPE",e,">",t?"\n":"")},reset:function(){a.length=0},getContent:function(){return a.join("").replace(/\n$/,"")}}}function ic(e,t){void 0===t&&(t=Go());var n=oc(e);return(e=e||{}).validate=!("validate"in e)||e.validate,{serialize:function(r){var o,i;i=e.validate,o={3:function(e){n.text(e.value,e.raw)},8:function(e){n.comment(e.value)},7:function(e){n.pi(e.name,e.value)},10:function(e){n.doctype(e.value)},4:function(e){n.cdata(e.value)},11:function(e){if(e=e.firstChild)for(;a(e),e=e.next;);}},n.reset();var a=function(e){var r,u,s,c,l,f,d,m,p,g=o[e.type];if(g)g(e);else{if(r=e.name,u=e.shortEnded,s=e.attributes,i&&s&&s.length>1&&((f=[]).map={},p=t.getElementRule(e.name))){for(d=0,m=p.attributesOrder.length;d<m;d++)(c=p.attributesOrder[d])in s.map&&(l=s.map[c],f.map[c]=l,f.push({name:c,value:l}));for(d=0,m=s.length;d<m;d++)(c=s[d].name)in f.map||(l=s.map[c],f.map[c]=l,f.push({name:c,value:l}));s=f}if(n.start(e.name,s,u),!u){if(e=e.firstChild)for(;a(e),e=e.next;);n.end(r)}}};return 1!==r.type||e.inner?o[11](r):a(r),n.getContent()}}}var ac=function(e){var t=wa.fromRangeStart(e),n=wa.fromRangeEnd(e),r=e.commonAncestorContainer;return za.fromPosition(!1,r,n).map(function(o){return!Pu(t,n,r)&&Pu(t,o,r)?(i=t.container(),a=t.offset(),u=o.container(),s=o.offset(),(c=document.createRange()).setStart(i,a),c.setEnd(u,s),c):e;var i,a,u,s,c}).getOr(e)},uc=function(e){return(t=e).collapsed?t:ac(t);var t},sc=So.matchNodeNames("td th"),cc=function(e,t,n){var r,o,i,a,u,s,c,l,f,d,m,p,g=e.schema.getTextInlineElements(),h=e.selection,v=e.dom;if(/^ | $/.test(t)&&(t=function(e){var t,n,r;t=h.getRng(),n=t.startContainer,r=t.startOffset;var o=function(e){return n[e]&&3===n[e].nodeType};return 3===n.nodeType&&(r>0?e=e.replace(/^&nbsp;/," "):o("previousSibling")||(e=e.replace(/^ /,"&nbsp;")),r<n.length?e=e.replace(/&nbsp;(<br>|)$/," "):o("nextSibling")||(e=e.replace(/(&nbsp;| )(<br>|)$/,"&nbsp;"))),e}(t)),r=e.parser,p=n.merge,o=ic({validate:e.settings.validate},e.schema),m='<span id="mce_marker" data-mce-type="bookmark">&#xFEFF;&#x200B;</span>',s={content:t,format:"html",selection:!0,paste:n.paste},(s=e.fire("BeforeSetContent",s)).isDefaultPrevented())e.fire("SetContent",{content:s.content,format:"html",selection:!0,paste:n.paste});else{-1===(t=s.content).indexOf("{$caret}")&&(t+="{$caret}"),t=t.replace(/\{\$caret\}/,m);var y,b,C,x,w=(l=h.getRng()).startContainer||(l.parentElement?l.parentElement():null),N=e.getBody();w===N&&h.isCollapsed()&&v.isBlock(N.firstChild)&&(y=N.firstChild)&&!e.schema.getShortEndedElements()[y.nodeName]&&v.isEmpty(N.firstChild)&&((l=v.createRng()).setStart(N.firstChild,0),l.setEnd(N.firstChild,0),h.setRng(l)),h.isCollapsed()||(e.selection.setRng(uc(e.selection.getRng())),e.getDoc().execCommand("Delete",!1,null),C=(b=h.getRng()).startContainer,x=b.startOffset,3===C.nodeType&&b.collapsed&&("\xa0"===C.data[x]?(C.deleteData(x,1),/[\u00a0| ]$/.test(t)||(t+=" ")):"\xa0"===C.data[x-1]&&(C.deleteData(x-1,1),/[\u00a0| ]$/.test(t)||(t=" "+t))));var S,k,T,A={context:(i=h.getNode()).nodeName.toLowerCase(),data:n.data,insert:!0};if(u=r.parse(t,A),!0===n.paste&&vs(e.schema,u)&&bs(v,i))return l=ys(o,v,e.selection.getRng(!0),u),e.selection.setRng(l),void e.fire("SetContent",s);if(function(e){for(var t=e;t=t.walk();)1===t.type&&t.attr("data-mce-fragment","1")}(u),"mce_marker"===(f=u.lastChild).attr("id"))for(c=f,f=f.prev;f;f=f.walk(!0))if(3===f.type||!v.isBlock(f.name)){e.schema.isValidChild(f.parent.name,"span")&&f.parent.insert(c,f,"br"===f.name);break}if(e._selectionOverrides.showBlockCaretContainer(i),A.invalid){for(h.setContent(m),i=h.getNode(),a=e.getBody(),9===i.nodeType?i=f=a:f=i;f!==a;)i=f,f=f.parentNode;t=i===a?a.innerHTML:v.getOuterHTML(i),t=o.serialize(r.parse(t.replace(/<span (id="mce_marker"|id=mce_marker).+?<\/span>/i,function(){return o.serialize(u)}))),i===a?v.setHTML(a,t):v.setOuterHTML(i,t)}else t=o.serialize(u),function(e,t,n){if("all"===n.getAttribute("data-mce-bogus"))n.parentNode.insertBefore(e.dom.createFragment(t),n);else{var r=n.firstChild,o=n.lastChild;!r||r===o&&"BR"===r.nodeName?e.dom.setHTML(n,t):e.selection.setContent(t)}}(e,t,i);!function(){if(p){var t=e.getBody(),n=new js(v);Dt.each(v.select("*[data-mce-fragment]"),function(e){for(var r=e.parentNode;r&&r!==t;r=r.parentNode)g[e.nodeName.toLowerCase()]&&n.compare(r,e)&&v.remove(e,!0)})}}(),function(t){var n,r,o;if(t){if(h.scrollIntoView(t),n=function(t){for(var n=e.getBody();t&&t!==n;t=t.parentNode)if("false"===e.dom.getContentEditable(t))return t;return null}(t))return v.remove(t),void h.select(n);l=v.createRng(),(f=t.previousSibling)&&3===f.nodeType?(l.setStart(f,f.nodeValue.length),de.ie||(d=t.nextSibling)&&3===d.nodeType&&(f.appendData(d.data),d.parentNode.removeChild(d))):(l.setStartBefore(t),l.setEndBefore(t)),r=v.getParent(t,v.isBlock),v.remove(t),r&&v.isEmpty(r)&&(e.$(r).empty(),l.setStart(r,0),l.setEnd(r,0),sc(r)||r.getAttribute("data-mce-fragment")||!(o=function(t){var n=wa.fromRangeStart(t);if(n=ls(e.getBody()).next(n))return n.toRange()}(l))?v.add(r,v.create("br",{"data-mce-bogus":"1"})):(l=o,v.remove(r))),h.setRng(l)}}(v.get("mce_marker")),S=e.getBody(),Dt.each(S.getElementsByTagName("*"),function(e){e.removeAttribute("data-mce-fragment")}),k=e.dom,T=e.selection.getStart(),E.from(k.getParent(T,"td,th")).map(Fn.fromDom).each(nc),e.fire("SetContent",s),e.addVisual()}},lc={insertAtCaret:function(e,t){var n,r,o="string"!=typeof(n=t)?(r=Dt.extend({paste:n.paste,data:{paste:n.paste}},n),{content:n.content,details:r}):{content:n,details:{}};cc(e,o.content,o.details)}};function fc(e,t,n,r,o){return e(n,r)?E.some(n):Jn.isFunction(o)&&o(n)?E.none():t(n,r,o)}var dc=function(e,t,n){for(var r=e.dom(),o=Jn.isFunction(n)?n:y.constant(!1);r.parentNode;){r=r.parentNode;var i=Fn.fromDom(r);if(t(i))return E.some(i);if(o(i))break}return E.none()},mc=function(e,t){return M.find(e.dom().childNodes,y.compose(t,Fn.fromDom)).map(Fn.fromDom)},pc=function(e,t){var n=function(e){for(var r=0;r<e.childNodes.length;r++){if(t(Fn.fromDom(e.childNodes[r])))return E.some(Fn.fromDom(e.childNodes[r]));var o=n(e.childNodes[r]);if(o.isSome())return o}return E.none()};return n(e.dom())},gc={first:function(e){return pc(fr.body(),e)},ancestor:dc,closest:function(e,t,n){return fc(function(e){return t(e)},dc,e,t,n)},sibling:function(e,t){var n=e.dom();return n.parentNode?mc(Fn.fromDom(n.parentNode),function(n){return!Rr.eq(e,n)&&t(n)}):E.none()},child:mc,descendant:pc},hc=br.immutable("sections","settings"),vc=In.detect().deviceType.isTouch(),yc=["lists","autolink","autosave"],bc={theme:"mobile"},Cc=function(e){var t=Jn.isArray(e)?e.join(" "):e,n=M.map(Jn.isString(t)?t.split(" "):[],_n);return M.filter(n,function(e){return e.length>0})},xc=function(e,t){return e.sections().hasOwnProperty(t)},wc=function(e,t,n,r){var o,i,a=Cc(n.forced_plugins),u=Cc(r.plugins),s=e&&xc(t,"mobile")?(o=u,M.filter(o,y.curry(M.contains,yc))):u,c=(i=s,[].concat(Cc(a)).concat(Cc(i)));return Dt.extend(r,{plugins:c.join(" ")})},Nc=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m,p,g,h,v=(o=["mobile"],i=r,a=rr.bifilter(i,function(e,t){return M.contains(o,t)}),hc(a.t,a.f)),y=Dt.extend(t,n,v.settings(),(p=e,h=(g=v).settings().inline,p&&xc(g,"mobile")&&!h?(l="mobile",f=bc,d=v.sections(),m=d.hasOwnProperty(l)?d[l]:{},Dt.extend({},f,m)):{}),{validate:!0,content_editable:v.settings().inline,external_plugins:(u=n,s=v.settings(),c=s.external_plugins?s.external_plugins:{},u&&u.external_plugins?Dt.extend({},u.external_plugins,c):c)});return wc(e,v,n,y)},Ec=function(e,t,n){return E.from(t.settings[n]).filter(e)},Sc=y.curry(Ec,Jn.isString),kc=function(e,t,n,r){var o,i,a=t in e.settings?e.settings[t]:n;return"hash"===r?(i={},"string"==typeof(o=a)?M.each(o.indexOf("=")>0?o.split(/[;,](?![^=;,]*(?:[;,]|$))/):o.split(","),function(e){(e=e.split("=")).length>1?i[Dt.trim(e[0])]=Dt.trim(e[1]):i[Dt.trim(e[0])]=Dt.trim(e)}):i=o,i):"string"===r?Ec(Jn.isString,e,t).getOr(n):"number"===r?Ec(Jn.isNumber,e,t).getOr(n):"boolean"===r?Ec(Jn.isBoolean,e,t).getOr(n):"object"===r?Ec(Jn.isObject,e,t).getOr(n):"array"===r?Ec(Jn.isArray,e,t).getOr(n):"function"===r?Ec(Jn.isFunction,e,t).getOr(n):a},Tc=/[\u0591-\u07FF\uFB1D-\uFDFF\uFE70-\uFEFC]/,Ac=function(e,t){var n=t.container(),r=t.offset();return e?Ni(n)?So.isText(n.nextSibling)?wa(n.nextSibling,0):wa.after(n):ki(t)?wa(n,r+1):t:Ni(n)?So.isText(n.previousSibling)?wa(n.previousSibling,n.previousSibling.data.length):wa.before(n):Ti(t)?wa(n,r-1):t},_c={isInlineTarget:function(e,t){var n=Sc(e,"inline_boundaries_selector").getOr("a[href],code");return Tr.is(Fn.fromDom(t),n)},findRootInline:function(e,t,n){var r,o,i,a=(r=e,o=t,i=n,M.filter(ui.DOM.getParents(i.container(),"*",o),r));return E.from(a[a.length-1])},isRtl:function(e){return"rtl"===ui.DOM.getStyle(e,"direction",!0)||(t=e.textContent,Tc.test(t));var t},isAtZwsp:function(e){return ki(e)||Ti(e)},normalizePosition:Ac,normalizeForwards:y.curry(Ac,!0),normalizeBackwards:y.curry(Ac,!1),hasSameParentBlock:function(e,t,n){var r=Ou(t,e),o=Ou(n,e);return r&&r===o}},Rc=function(e,t){return Rr.contains(e,t)?gc.closest(t,function(e){return so(e)||lo(e)},(n=e,function(e){return Rr.eq(n,Fn.fromDom(e.dom().parentNode))})):E.none();var n},Bc=function(e){var t,n,r;e.dom.isEmpty(e.getBody())&&(e.setContent(""),n=(t=e).getBody(),r=n.firstChild&&t.dom.isBlock(n.firstChild)?n.firstChild:n,t.selection.setCursorLocation(r,0))},Dc=function(e,t,n){return Wa([za.firstPositionIn(n),za.lastPositionIn(n)],function(r,o){var i=_c.normalizePosition(!0,r),a=_c.normalizePosition(!1,o),u=_c.normalizePosition(!1,t);return e?za.nextPosition(n,u).map(function(e){return e.isEqual(a)&&t.isEqual(i)}).getOr(!1):za.prevPosition(n,u).map(function(e){return e.isEqual(i)&&t.isEqual(a)}).getOr(!1)}).getOr(!0)},Oc=function(e,t,n){return gc.ancestor(e,function(e){return Tr.is(e,t)},n)},Pc=Oc,Lc=function(e,t){return Tr.one(t,e)},Ic=function(e,t,n){return fc(Tr.is,Oc,e,t,n)},Mc=function(e,t,n){return Pc(e,t,n).isSome()},Fc=function(e,t){return So.isText(t)&&/^[ \t\r\n]*$/.test(t.data)&&!1===(n=e,r=t,o=Fn.fromDom(n),i=Fn.fromDom(r),Mc(i,"pre,code",y.curry(Rr.eq,o)));var n,r,o,i},zc=function(e,t){return Ui(t)&&!1===Fc(e,t)||(n=t,So.isElement(n)&&"A"===n.nodeName&&n.hasAttribute("name"))||Uc(t);var n},Uc=So.hasAttribute("data-mce-bookmark"),qc=So.hasAttribute("data-mce-bogus"),Vc=So.hasAttributeValue("data-mce-bogus","all"),Hc=function(e){return function(e){var t,n,r=0;if(zc(e,e))return!1;if(!(n=e.firstChild))return!0;t=new Zr(n,e);do{if(Vc(n))n=t.next(!0);else if(qc(n))n=t.next();else if(So.isBr(n))r++,n=t.next();else{if(zc(e,n))return!1;n=t.next()}}while(n);return r<=1}(e.dom())},jc=br.immutable("block","position"),$c=br.immutable("from","to"),Wc=function(e,t){var n=Fn.fromDom(e),r=Fn.fromDom(t.container());return Rc(n,r).map(function(e){return jc(e,t)})},Kc=function(e,t,n){var r=Wc(e,wa.fromRangeStart(n)),o=r.bind(function(n){return za.fromPosition(t,e,n.position()).bind(function(n){return Wc(e,n).map(function(n){return r=e,o=t,i=n,So.isBr(i.position().getNode())&&!1===Hc(i.block())?za.positionIn(!1,i.block().dom()).bind(function(e){return e.isEqual(i.position())?za.fromPosition(o,r,e).bind(function(e){return Wc(r,e)}):E.some(i)}).getOr(i):i;var r,o,i})})});return Wa([r,o],$c).filter(function(e){return r=e,!1===Rr.eq(r.from().block(),r.to().block())&&(n=e,Fr.parent(n.from().block()).bind(function(e){return Fr.parent(n.to().block()).filter(function(t){return Rr.eq(e,t)})}).isSome())&&(t=e,!1===So.isContentEditableFalse(t.from().block())&&!1===So.isContentEditableFalse(t.to().block()));var t,n,r})},Xc=function(e,t,n){return n.collapsed?Kc(e,t,n):E.none()},Yc=function(e,t,n){return Rr.contains(t,e)?Fr.parents(e,function(e){return n(e)||Rr.eq(e,t)}).slice(0,-1):[]},Gc=function(e,t){return Yc(e,t,y.constant(!1))},Jc=Gc,Qc=function(e,t){return[e].concat(Gc(e,t))},Zc=function(e){var t,n,r=(t=e,n=Fr.children(t),M.findIndex(n,io).fold(function(){return n},function(e){return n.slice(0,e)}));return M.each(r,function(e){Js.remove(e)}),r},el=function(e,t){za.positionIn(e,t.dom()).each(function(e){var t=e.getNode();So.isBr(t)&&Js.remove(Fn.fromDom(t))})},tl=function(e,t){var n=Qc(t,e);return M.find(n.reverse(),Hc).each(Js.remove)},nl=function(e,t){return Rr.contains(t,e)?Fr.parent(e).bind(function(n){return Rr.eq(n,t)?E.some(e):(r=t,o=e,i=Fr.parents(o,function(e){return Rr.eq(e,r)}),E.from(i[i.length-2]));var r,o,i}):E.none()},rl=function(e,t,n){if(Hc(n))return Js.remove(n),Hc(t)&&tc(t),za.firstPositionIn(t.dom());el(!0,t),el(!1,n);var r=Zc(t);return nl(t,n).fold(function(){tl(e,t);var o=za.lastPositionIn(n.dom());return M.each(r,function(e){Ks.append(n,e)}),o},function(o){var i=za.prevPosition(n.dom(),wa.before(o.dom()));return M.each(r,function(e){Ks.before(o,e)}),tl(e,t),i})},ol=function(e,t,n,r){return t?rl(e,r,n):rl(e,n,r)},il=function(e,t){var n,r=Fn.fromDom(e.getBody());return(n=Xc(r.dom(),t,e.selection.getRng()).bind(function(e){return ol(r,t,e.from().block(),e.to().block())})).each(function(t){e.selection.setRng(t.toRange())}),n.isSome()},al=function(e,t){var n=Fn.fromDom(t),r=y.curry(Rr.eq,e);return gc.ancestor(n,po,r).isSome()},ul=function(e,t){var n,r,o=za.prevPosition(e.dom(),wa.fromRangeStart(t)).isNone(),i=za.nextPosition(e.dom(),wa.fromRangeEnd(t)).isNone();return!(al(n=e,(r=t).startContainer)||al(n,r.endContainer))&&o&&i},sl=function(e){var t,n,r,o,i=Fn.fromDom(e.getBody()),a=e.selection.getRng();return ul(i,a)?((o=e).setContent(""),o.selection.setCursorLocation(),!0):(t=i,n=e.selection,r=n.getRng(),Wa([Rc(t,Fn.fromDom(r.startContainer)),Rc(t,Fn.fromDom(r.endContainer))],function(e,o){return!1===Rr.eq(e,o)&&(r.deleteContents(),ol(t,!0,e,o).each(function(e){n.setRng(e.toRange())}),!0)}).getOr(!1))},cl=function(e,t){return!e.selection.isCollapsed()&&sl(e)},ll=function(e){if(!Jn.isArray(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");var t=[],n={};return M.each(e,function(r,o){var i=rr.keys(r);if(1!==i.length)throw new Error("one and only one name per case");var a=i[0],u=r[a];if(n[a]!==undefined)throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!Jn.isArray(u))throw new Error("case arguments must be an array");t.push(a),n[a]=function(){var n=arguments.length;if(n!==u.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+u.length+" ("+u+"), got "+n);for(var r=new Array(n),i=0;i<r.length;i++)r[i]=arguments[i];return{fold:function(){if(arguments.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+arguments.length);return arguments[o].apply(null,r)},match:function(e){var n=rr.keys(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!M.forall(t,function(e){return M.contains(n,e)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,r)},log:function(e){console.log(e,{constructors:t,constructor:a,params:r})}}}}),n},fl=ll([{remove:["element"]},{moveToElement:["element"]},{moveToPosition:["position"]}]),dl=function(e,t,n,r){var o=r.getNode(!1===t);return Rc(Fn.fromDom(e),Fn.fromDom(n.getNode())).map(function(e){return Hc(e)?fl.remove(e.dom()):fl.moveToElement(o)}).orThunk(function(){return E.some(fl.moveToElement(o))})},ml=function(e,t,n){return za.fromPosition(t,e,n).bind(function(r){return c=r.getNode(),po(Fn.fromDom(c))||lo(Fn.fromDom(c))?E.none():(o=e,u=r,s=function(e){return ao(Fn.fromDom(e))&&!Pu(a,u,o)},Hu(!(i=t),a=n).fold(function(){return Hu(i,u).fold(y.constant(!1),s)},s)?E.none():t&&So.isContentEditableFalse(r.getNode())?dl(e,t,n,r):!1===t&&So.isContentEditableFalse(r.getNode(!0))?dl(e,t,n,r):t&&Wu(n)?E.some(fl.moveToPosition(r)):!1===t&&$u(n)?E.some(fl.moveToPosition(r)):E.none());var o,i,a,u,s,c})},pl=function(e,t,n){return i=t,a=n.getNode(!1===i),u=i?"after":"before",So.isElement(a)&&a.getAttribute("data-mce-caret")===u?(r=t,o=n.getNode(!1===t),r&&So.isContentEditableFalse(o.nextSibling)?E.some(fl.moveToElement(o.nextSibling)):!1===r&&So.isContentEditableFalse(o.previousSibling)?E.some(fl.moveToElement(o.previousSibling)):E.none()).fold(function(){return ml(e,t,n)},E.some):ml(e,t,n).bind(function(t){return r=e,o=n,t.fold(function(e){return E.some(fl.remove(e))},function(e){return E.some(fl.moveToElement(e))},function(e){return Pu(o,e,r)?E.none():E.some(fl.moveToPosition(e))});var r,o});var r,o,i,a,u},gl=function(e,t){return r=e,o=(n=t).container(),i=n.offset(),!1===wa.isTextPosition(n)&&o===r.parentNode&&i>wa.before(r).offset()?wa(t.container(),t.offset()-1):t;var n,r,o,i},hl=function(e){return Ui(e.previousSibling)?E.some((t=e.previousSibling,So.isText(t)?wa(t,t.data.length):wa.after(t))):e.previousSibling?za.lastPositionIn(e.previousSibling):E.none();var t},vl=function(e){return Ui(e.nextSibling)?E.some((t=e.nextSibling,So.isText(t)?wa(t,0):wa.before(t))):e.nextSibling?za.firstPositionIn(e.nextSibling):E.none();var t},yl=function(e,t){return hl(t).orThunk(function(){return vl(t)}).orThunk(function(){return n=e,r=t,o=wa.before(r.previousSibling?r.previousSibling:r.parentNode),za.prevPosition(n,o).fold(function(){return za.nextPosition(n,wa.after(r))},E.some);var n,r,o})},bl=function(e,t){return vl(t).orThunk(function(){return hl(t)}).orThunk(function(){return n=e,r=t,za.nextPosition(n,wa.after(r)).fold(function(){return za.prevPosition(n,wa.before(r))},E.some);var n,r})},Cl=function(e,t,n){return(r=e,o=t,i=n,r?bl(o,i):yl(o,i)).map(y.curry(gl,n));var r,o,i},xl=function(e,t,n){n.fold(function(){e.focus()},function(n){e.selection.setRng(n.toRange(),t)})},wl=function(e,t){return t&&e.schema.getBlockElements().hasOwnProperty(Yn.name(t))},Nl=function(e){if(Hc(e)){var t=Fn.fromHtml('<br data-mce-bogus="1">');return Js.empty(e),Ks.append(e,t),E.some(wa.before(t.dom()))}return E.none()},El=function(e,t,n){var r,o,i,a=Cl(t,e.getBody(),n.dom()),u=gc.ancestor(n,y.curry(wl,e),(r=e.getBody(),function(e){return e.dom()===r})),s=(o=n,i=a,Wa([Fr.prevSibling(o),Fr.nextSibling(o),i],function(e,t,n){var r,i=e.dom(),a=t.dom();return So.isText(i)&&So.isText(a)?(r=i.data.length,i.appendData(a.data),Js.remove(t),Js.remove(o),n.container()===a?wa(i,r):n):(Js.remove(o),n)}).orThunk(function(){return Js.remove(o),i}));e.dom.isEmpty(e.getBody())?(e.setContent(""),e.selection.setCursorLocation()):u.bind(Nl).fold(function(){xl(e,t,s)},function(n){xl(e,t,E.some(n))})},Sl=function(e,t){var n,r,o,i,a;return(n=e.getBody(),r=t,o=e.selection.getRng(),i=Uu(r?1:-1,n,o),a=wa.fromRangeStart(i),!1===r&&Wu(a)?E.some(fl.remove(a.getNode(!0))):r&&$u(a)?E.some(fl.remove(a.getNode())):pl(n,r,a)).map(function(n){return n.fold((a=e,u=t,function(e){return a._selectionOverrides.hideFakeCaret(),El(a,u,Fn.fromDom(e)),!0}),(o=e,i=t,function(e){var t=i?wa.before(e):wa.after(e);return o.selection.setRng(t.toRange()),!0}),(r=e,function(e){return r.selection.setRng(e.toRange()),!0}));var r,o,i,a,u}).getOr(!1)},kl=function(e,t){var n,r=e.selection.getNode();return!!So.isContentEditableFalse(r)&&(n=Fn.fromDom(e.getBody()),M.each(ou(n,".mce-offscreen-selection"),Js.remove),El(e,t,Fn.fromDom(e.selection.getNode())),Bc(e),!0)},Tl=function(e,t){return e.selection.isCollapsed()?Sl(e,t):kl(e,t)},Al=function(e){var t,n=function(e,t){for(;t&&t!==e;){if(So.isContentEditableTrue(t)||So.isContentEditableFalse(t))return t;t=t.parentNode}return null}(e.getBody(),e.selection.getNode());return So.isContentEditableTrue(n)&&e.dom.isBlock(n)&&e.dom.isEmpty(n)&&(t=e.dom.create("br",{"data-mce-bogus":"1"}),e.dom.setHTML(n,""),n.appendChild(t),e.selection.setRng(wa.before(t).toRange())),!0},_l=So.isText,Rl=function(e){return _l(e)&&e.data[0]===yi},Bl=function(e){return _l(e)&&e.data[e.data.length-1]===yi},Dl=function(e){return e.ownerDocument.createTextNode(yi)},Ol=function(e,t){return e?function(e){if(_l(e.previousSibling))return Bl(e.previousSibling)?e.previousSibling:(e.previousSibling.appendData(yi),e.previousSibling);if(_l(e))return Rl(e)?e:(e.insertData(0,yi),e);var t=Dl(e);return e.parentNode.insertBefore(t,e),t}(t):function(e){if(_l(e.nextSibling))return Rl(e.nextSibling)?e.nextSibling:(e.nextSibling.insertData(0,yi),e.nextSibling);if(_l(e))return Bl(e)?e:(e.appendData(yi),e);var t=Dl(e);return e.nextSibling?e.parentNode.insertBefore(t,e.nextSibling):e.parentNode.appendChild(t),t}(t)},Pl=y.curry(Ol,!0),Ll=y.curry(Ol,!1),Il=function(e,t){return So.isText(e.container())?Ol(t,e.container()):Ol(t,e.getNode())},Ml=function(e,t){var n=t.get();return n&&e.container()===n&&Ni(n)},Fl=function(e,t){return t.fold(function(t){Da.remove(e.get());var n=Pl(t);return e.set(n),E.some(wa(n,n.length-1))},function(t){return za.firstPositionIn(t).map(function(t){if(Ml(t,e))return wa(e.get(),1);Da.remove(e.get());var n=Il(t,!0);return e.set(n),wa(n,1)})},function(t){return za.lastPositionIn(t).map(function(t){if(Ml(t,e))return wa(e.get(),e.get().length-1);Da.remove(e.get());var n=Il(t,!1);return e.set(n),wa(n,n.length-1)})},function(t){Da.remove(e.get());var n=Ll(t);return e.set(n),E.some(wa(n,1))})},zl=function(e){return e&&/^(IMG)$/.test(e.nodeName)},Ul=function(e){return e&&3===e.nodeType&&/^([\t \r\n]+|)$/.test(e.nodeValue)},ql=function(e,t,n){return"color"!==n&&"backgroundColor"!==n||(t=e.toHex(t)),"fontWeight"===n&&700===t&&(t="bold"),"fontFamily"===n&&(t=t.replace(/[\'\"]/g,"").replace(/,\s+/g,",")),""+t},Vl={isInlineBlock:zl,moveStart:function(e,t,n){var r,o,i,a=n.startContainer,u=n.startOffset;if((n.startContainer!==n.endContainer||!zl(n.startContainer.childNodes[n.startOffset]))&&(3===a.nodeType&&u>=a.nodeValue.length&&(u=e.nodeIndex(a),a=a.parentNode),1===a.nodeType))for(u<(i=a.childNodes).length?r=new Zr(a=i[u],e.getParent(a,e.isBlock)):(r=new Zr(a=i[i.length-1],e.getParent(a,e.isBlock))).next(!0),o=r.current();o;o=r.next())if(3===o.nodeType&&!Ul(o))return n.setStart(o,0),void t.setRng(n)},getNonWhiteSpaceSibling:function(e,t,n){if(e)for(t=t?"nextSibling":"previousSibling",e=n?e:e[t];e;e=e[t])if(1===e.nodeType||!Ul(e))return e},isTextBlock:function(e,t){return t.nodeType&&(t=t.nodeName),!!e.schema.getTextBlockElements()[t.toLowerCase()]},isValid:function(e,t,n){return e.schema.isValidChild(t,n)},isWhiteSpaceNode:Ul,replaceVars:function(e,t){return"string"!=typeof e?e=e(t):t&&(e=e.replace(/%(\w+)/g,function(e,n){return t[n]||e})),e},isEq:function(e,t){return t=t||"",e=""+((e=e||"").nodeName||e),t=""+(t.nodeName||t),e.toLowerCase()===t.toLowerCase()},normalizeStyleValue:ql,getStyle:function(e,t,n){return ql(e,e.getStyle(t,n),n)},getTextDecoration:function(e,t){var n;return e.getParent(t,function(t){return(n=e.getStyle(t,"text-decoration"))&&"none"!==n}),n},getParents:function(e,t,n){return e.getParents(t,n,e.getRoot())}},Hl=Vs.isBookmarkNode,jl=Vl.getParents,$l=Vl.isWhiteSpaceNode,Wl=Vl.isTextBlock,Kl=function(e,t){for(void 0===t&&(t=3===e.nodeType?e.length:e.childNodes.length);e&&e.hasChildNodes();)(e=e.childNodes[t])&&(t=3===e.nodeType?e.length:e.childNodes.length);return{node:e,offset:t}},Xl=function(e,t){for(var n=t;n;){if(1===n.nodeType&&e.getContentEditable(n))return"false"===e.getContentEditable(n)?n:t;n=n.parentNode}return t},Yl=function(e,t,n,r){var o,i,a=n.nodeValue;return void 0===r&&(r=e?a.length:0),e?-1===(o=(o=a.lastIndexOf(" ",r))>(i=a.lastIndexOf("\xa0",r))?o:i)||t||o++:(o=a.indexOf(" ",r),i=a.indexOf("\xa0",r),o=-1!==o&&(-1===i||o<i)?o:i),o},Gl=function(e,t,n,r,o,i){var a,u,s,c;if(3===n.nodeType){if(-1!==(s=Yl(o,i,n,r)))return{container:n,offset:s};c=n}for(a=new Zr(n,e.getParent(n,e.isBlock)||t);u=a[o?"prev":"next"]();)if(3===u.nodeType){if(c=u,-1!==(s=Yl(o,i,u)))return{container:u,offset:s}}else if(e.isBlock(u))break;if(c)return{container:c,offset:r=o?0:c.length}},Jl=function(e,t,n,r,o){var i,a,u,s;for(3===r.nodeType&&0===r.nodeValue.length&&r[o]&&(r=r[o]),i=jl(e,r),a=0;a<i.length;a++)for(u=0;u<t.length;u++)if(!("collapsed"in(s=t[u])&&s.collapsed!==n.collapsed)&&e.is(i[a],s.selector))return i[a];return r},Ql=function(e,t,n,r){var o,i=e.dom,a=i.getRoot();if(t[0].wrapper||(o=i.getParent(n,t[0].block,a)),!o){var u=i.getParent(n,"LI,TD,TH");o=i.getParent(3===n.nodeType?n.parentNode:n,function(t){return t!==a&&Wl(e,t)},u)}if(o&&t[0].wrapper&&(o=jl(i,o,"ul,ol").reverse()[0]||o),!o)for(o=n;o[r]&&!i.isBlock(o[r])&&(o=o[r],!Vl.isEq(o,"br")););return o||n},Zl=function(e,t,n,r,o,i,a){var u,s,c,l,f,d;if(u=s=a?n:o,l=a?"previousSibling":"nextSibling",f=e.getRoot(),3===u.nodeType&&!$l(u)&&(a?r>0:i<u.nodeValue.length))return u;for(;;){if(!t[0].block_expand&&e.isBlock(s))return s;for(c=s[l];c;c=c[l])if(!Hl(c)&&!$l(c)&&("BR"!==(d=c).nodeName||!d.getAttribute("data-mce-bogus")||d.nextSibling))return s;if(s===f||s.parentNode===f){u=s;break}s=s.parentNode}return u},ef=function(e,t,n,r){var o,i=t.startContainer,a=t.startOffset,u=t.endContainer,s=t.endOffset,c=e.dom;return 1===i.nodeType&&i.hasChildNodes()&&3===(i=Yi(i,a)).nodeType&&(a=0),1===u.nodeType&&u.hasChildNodes()&&3===(u=Yi(u,t.collapsed?s:s-1)).nodeType&&(s=u.nodeValue.length),i=Xl(c,i),u=Xl(c,u),(Hl(i.parentNode)||Hl(i))&&3===(i=(i=Hl(i)?i:i.parentNode).nextSibling||i).nodeType&&(a=0),(Hl(u.parentNode)||Hl(u))&&3===(u=(u=Hl(u)?u:u.parentNode).previousSibling||u).nodeType&&(s=u.length),n[0].inline&&(t.collapsed&&((o=Gl(c,e.getBody(),i,a,!0,r))&&(i=o.container,a=o.offset),(o=Gl(c,e.getBody(),u,s,!1,r))&&(u=o.container,s=o.offset)),u=r?u:function(e,t){var n=Kl(e,t);if(n.node){for(;n.node&&0===n.offset&&n.node.previousSibling;)n=Kl(n.node.previousSibling);n.node&&n.offset>0&&3===n.node.nodeType&&" "===n.node.nodeValue.charAt(n.offset-1)&&n.offset>1&&(e=n.node).splitText(n.offset-1)}return e}(u,s)),(n[0].inline||n[0].block_expand)&&(n[0].inline&&3===i.nodeType&&0!==a||(i=Zl(c,n,i,a,u,s,!0)),n[0].inline&&3===u.nodeType&&s!==u.nodeValue.length||(u=Zl(c,n,i,a,u,s,!1))),n[0].selector&&!1!==n[0].expand&&!n[0].inline&&(i=Jl(c,n,t,i,"previousSibling"),u=Jl(c,n,t,u,"nextSibling")),(n[0].block||n[0].selector)&&(i=Ql(e,n,i,"previousSibling"),u=Ql(e,n,u,"nextSibling"),n[0].block&&(c.isBlock(i)||(i=Zl(c,n,i,a,u,s,!0)),c.isBlock(u)||(u=Zl(c,n,i,a,u,s,!1)))),1===i.nodeType&&(a=c.nodeIndex(i),i=i.parentNode),1===u.nodeType&&(s=c.nodeIndex(u)+1,u=u.parentNode),{startContainer:i,startOffset:a,endContainer:u,endOffset:s}},tf=Vl.isEq,nf=function(e,t,n){var r=e.formatter.get(n);if(r)for(var o=0;o<r.length;o++)if(!1===r[o].inherit&&e.dom.is(t,r[o].selector))return!0;return!1},rf=function(e,t,n,r){var o=e.dom.getRoot();return t!==o&&(t=e.dom.getParent(t,function(t){return!!nf(e,t,n)||t.parentNode===o||!!uf(e,t,n,r,!0)}),uf(e,t,n,r))},of=function(e,t,n){return!!tf(t,n.inline)||!!tf(t,n.block)||(n.selector?1===t.nodeType&&e.is(t,n.selector):void 0)},af=function(e,t,n,r,o,i){var a,u,s,c=n[r];if(n.onmatch)return n.onmatch(t,n,r);if(c)if("undefined"==typeof c.length){for(a in c)if(c.hasOwnProperty(a)){if(u="attributes"===r?e.getAttrib(t,a):Vl.getStyle(e,t,a),o&&!u&&!n.exact)return;if((!o||n.exact)&&!tf(u,Vl.normalizeStyleValue(e,Vl.replaceVars(c[a],i),a)))return}}else for(s=0;s<c.length;s++)if("attributes"===r?e.getAttrib(t,c[s]):Vl.getStyle(e,t,c[s]))return n;return n},uf=function(e,t,n,r,o){var i,a,u,s,c=e.formatter.get(n),l=e.dom;if(c&&t)for(a=0;a<c.length;a++)if(i=c[a],of(e.dom,t,i)&&af(l,t,i,"attributes",o,r)&&af(l,t,i,"styles",o,r)){if(s=i.classes)for(u=0;u<s.length;u++)if(!e.dom.hasClass(t,s[u]))return;return i}},sf={matchNode:uf,matchName:of,match:function(e,t,n,r){var o;return r?rf(e,r,t,n):(r=e.selection.getNode(),!!rf(e,r,t,n)||!((o=e.selection.getStart())===r||!rf(e,o,t,n)))},matchAll:function(e,t,n){var r,o=[],i={};return r=e.selection.getStart(),e.dom.getParent(r,function(r){var a,u;for(a=0;a<t.length;a++)u=t[a],!i[u]&&uf(e,r,u,n)&&(i[u]=!0,o.push(u))},e.dom.getRoot()),o},canApply:function(e,t){var n,r,o,i,a,u=e.formatter.get(t),s=e.dom;if(u)for(n=e.selection.getStart(),r=Vl.getParents(s,n),i=u.length-1;i>=0;i--){if(!(a=u[i].selector)||u[i].defaultBlock)return!0;for(o=r.length-1;o>=0;o--)if(s.is(r[o],a))return!0}return!1},matchesUnInheritedFormatSelector:nf},cf=function(e,t){return e.splitText(t)},lf={split:function(e){var t=e.startContainer,n=e.startOffset,r=e.endContainer,o=e.endOffset;return t===r&&So.isText(t)?n>0&&n<t.nodeValue.length&&(t=(r=cf(t,n)).previousSibling,o>n?(t=r=cf(r,o-=n).previousSibling,o=r.nodeValue.length,n=0):o=0):(So.isText(t)&&n>0&&n<t.nodeValue.length&&(t=cf(t,n),n=0),So.isText(r)&&o>0&&o<r.nodeValue.length&&(o=(r=cf(r,o).previousSibling).nodeValue.length)),{startContainer:t,startOffset:n,endContainer:r,endOffset:o}}},ff=yi,df="_mce_caret",mf=function(e){return 1===e.nodeType&&e.id===df},pf=function(e){return function(e){for(var t=[];e;){if(3===e.nodeType&&e.nodeValue!==ff||e.childNodes.length>1)return[];1===e.nodeType&&t.push(e),e=e.firstChild}return t}(e).length>0},gf=function(e){var t;if(e)for(e=(t=new Zr(e,e)).current();e;e=t.next())if(3===e.nodeType)return e;return null},hf=function(e){var t=Fn.fromTag("span");return sr.setAll(t,{id:df,"data-mce-bogus":"1","data-mce-type":"format-caret"}),e&&Ks.append(t,Fn.fromText(ff)),t},vf=function(e,t){for(;t&&t!==e;){if(t.id===df)return t;t=t.parentNode}return null},yf=function(e,t,n,r){var o,i,a,u;o=t.getRng(!0),i=e.getParent(n,e.isBlock),pf(n)?(!1!==r&&(o.setStartBefore(n),o.setEndBefore(n)),e.remove(n)):((u=gf(n))&&u.nodeValue.charAt(0)===ff&&u.deleteData(0,1),a=u,o.startContainer===a&&o.startOffset>0&&o.setStart(a,o.startOffset-1),o.endContainer===a&&o.endOffset>0&&o.setEnd(a,o.endOffset-1),e.remove(n,!0)),i&&e.isEmpty(i)&&tc(Fn.fromDom(i)),t.setRng(o)},bf=function(e,t,n,r,o){if(r)yf(t,n,r,o);else if(!(r=vf(e,n.getStart())))for(;r=t.get(df);)yf(t,n,r,!1)},Cf=function(e,t,n){var r=e.dom,o=r.getParent(n,ea.curry(Vl.isTextBlock,e));o&&r.isEmpty(o)?n.parentNode.replaceChild(t,n):(ec(Fn.fromDom(n)),r.isEmpty(n)?n.parentNode.replaceChild(t,n):r.insertAfter(t,n))},xf=function(e,t){return e.appendChild(t),t},wf=function(e,t){var n=M.foldr(e,function(e,t){return xf(e,t.cloneNode(!1))},t);return xf(n,n.ownerDocument.createTextNode(ff))},Nf={setup:function(e){var t=e.dom,n=e.selection,r=e.getBody();e.on("mouseup keydown",function(e){var o,i,a,u;o=r,i=t,a=n,u=e.keyCode,bf(o,i,a,null,!1),8===u&&a.isCollapsed()&&a.getStart().innerHTML===ff&&bf(o,i,a,vf(o,a.getStart())),37!==u&&39!==u||bf(o,i,a,vf(o,a.getStart()))})},applyCaretFormat:function(e,t,n){var r,o,i,a,u,s,c=e.selection;a=(r=c.getRng(!0)).startOffset,s=r.startContainer.nodeValue,(o=vf(e.getBody(),c.getStart()))&&(i=gf(o));var l,f,d=/[^\s\u00a0\u00ad\u200b\ufeff]/;s&&a>0&&a<s.length&&d.test(s.charAt(a))&&d.test(s.charAt(a-1))?(u=c.getBookmark(),r.collapse(!0),r=ef(e,r,e.formatter.get(t)),r=lf.split(r),e.formatter.apply(t,n,r),c.moveToBookmark(u)):(o&&i.nodeValue===ff?e.formatter.apply(t,n,o):(l=e.getDoc(),f=hf(!0).dom(),i=(o=l.importNode(f,!0)).firstChild,r.insertNode(o),a=1,e.formatter.apply(t,n,o)),c.setCursorLocation(i,a))},removeCaretFormat:function(e,t,n,r){var o,i,a,u,s,c,l,f=e.dom,d=e.selection,m=[],p=d.getRng();for(o=p.startContainer,i=p.startOffset,s=o,3===o.nodeType&&(i!==o.nodeValue.length&&(u=!0),s=s.parentNode);s;){if(sf.matchNode(e,s,t,n,r)){c=s;break}s.nextSibling&&(u=!0),m.push(s),s=s.parentNode}if(c)if(u){a=d.getBookmark(),p.collapse(!0);var g=ef(e,p,e.formatter.get(t),!0);g=lf.split(g),e.formatter.remove(t,n,g),d.moveToBookmark(a)}else{l=vf(e.getBody(),c);var h=hf(!1).dom(),v=wf(m,h);Cf(e,h,l||c),yf(f,d,l,!1),d.setCursorLocation(v,1),f.isEmpty(c)&&f.remove(c)}},isCaretNode:mf,getParentCaretContainer:vf,replaceWithCaretFormat:function(e,t){var n=hf(!1),r=wf(t,n.dom());return Ks.before(Fn.fromDom(e),n),Js.remove(Fn.fromDom(e)),wa(r,0)},isFormatElement:function(e,t){return e.schema.getTextInlineElements().hasOwnProperty(Yn.name(t))&&!mf(t.dom())&&!So.isBogus(t.dom())}},Ef=function(e,t){for(var n=0;n<e.length;n++){var r=e[n].apply(null,t);if(r.isSome())return r}return E.none()},Sf=ll([{before:["element"]},{start:["element"]},{end:["element"]},{after:["element"]}]),kf=function(e,t){var n=Ou(t,e);return n||e},Tf=function(e,t,n){var r=_c.normalizeForwards(n),o=kf(t,r.container());return _c.findRootInline(e,o,r).fold(function(){return za.nextPosition(o,r).bind(y.curry(_c.findRootInline,e,o)).map(function(e){return Sf.before(e)})},E.none)},Af=function(e,t){return null===Nf.getParentCaretContainer(e,t)},_f=function(e,t,n){return _c.findRootInline(e,t,n).filter(y.curry(Af,t))},Rf=function(e,t,n){var r=_c.normalizeBackwards(n);return _f(e,t,r).bind(function(e){return za.prevPosition(e,r).isNone()?E.some(Sf.start(e)):E.none()})},Bf=function(e,t,n){var r=_c.normalizeForwards(n);return _f(e,t,r).bind(function(e){return za.nextPosition(e,r).isNone()?E.some(Sf.end(e)):E.none()})},Df=function(e,t,n){var r=_c.normalizeBackwards(n),o=kf(t,r.container());return _c.findRootInline(e,o,r).fold(function(){return za.prevPosition(o,r).bind(y.curry(_c.findRootInline,e,o)).map(function(e){return Sf.after(e)})},E.none)},Of=function(e){return!1===_c.isRtl(Lf(e))},Pf=function(e,t,n){return Ef([Tf,Rf,Bf,Df],[e,t,n]).filter(Of)},Lf=function(e){return e.fold(y.identity,y.identity,y.identity,y.identity)},If=function(e){return e.fold(y.constant("before"),y.constant("start"),y.constant("end"),y.constant("after"))},Mf=function(e){return e.fold(Sf.before,Sf.before,Sf.after,Sf.after)},Ff=function(e,t,n,r,o,i){return Wa([_c.findRootInline(t,n,r),_c.findRootInline(t,n,o)],function(t,r){return t!==r&&_c.hasSameParentBlock(n,t,r)?Sf.after(e?t:r):i}).getOr(i)},zf=function(e,t){return e.fold(y.constant(!0),function(e){return r=t,!(If(n=e)===If(r)&&Lf(n)===Lf(r));var n,r})},Uf=function(e,t){return e?t.fold(y.compose(E.some,Sf.start),E.none,y.compose(E.some,Sf.after),E.none):t.fold(E.none,y.compose(E.some,Sf.before),E.none,y.compose(E.some,Sf.end))},qf=function(e,t,n,r){var o=_c.normalizePosition(e,r),i=Pf(t,n,o);return Pf(t,n,o).bind(y.curry(Uf,e)).orThunk(function(){return o=e,a=t,u=n,s=i,c=r,l=_c.normalizePosition(o,c),za.fromPosition(o,u,l).map(y.curry(_c.normalizePosition,o)).fold(function(){return s.map(Mf)},function(e){return Pf(a,u,e).map(y.curry(Ff,o,a,u,l,e)).filter(y.curry(zf,s))}).filter(Of);var o,a,u,s,c,l})},Vf=Pf,Hf=qf,jf=(y.curry(qf,!1),y.curry(qf,!0),Mf),$f=function(e){return e.fold(Sf.start,Sf.start,Sf.end,Sf.end)},Wf=function(e){var t=e,n=function(){return t};return{get:n,set:function(e){t=e},clone:function(){return Wf(n())}}},Kf=function(e){return Jn.isFunction(e.selection.getSel().modify)},Xf=function(e,t,n){var r=e?1:-1;return t.setRng(wa(n.container(),n.offset()+r).toRange()),t.getSel().modify("move",e?"forward":"backward","word"),!0},Yf=function(e,t){var n=t.selection.getRng(),r=e?wa.fromRangeEnd(n):wa.fromRangeStart(n);return!!Kf(t)&&(e&&ki(r)?Xf(!0,t.selection,r):!(e||!Ti(r))&&Xf(!1,t.selection,r))},Gf=function(e,t){var n=e.dom.createRng();n.setStart(t.container(),t.offset()),n.setEnd(t.container(),t.offset()),e.selection.setRng(n)},Jf=function(e){return!1!==e.settings.inline_boundaries},Qf=function(e,t){e?t.setAttribute("data-mce-selected","inline-boundary"):t.removeAttribute("data-mce-selected")},Zf=function(e,t,n){return Fl(t,n).map(function(t){return Gf(e,t),n})},ed=function(e,t,n){return function(){return!!Jf(t)&&Yf(e,t)}},td={move:function(e,t,n){return function(){return!!Jf(e)&&(r=e,o=t,i=n,a=r.getBody(),u=wa.fromRangeStart(r.selection.getRng()),s=y.curry(_c.isInlineTarget,r),Hf(i,s,a,u).bind(function(e){return Zf(r,o,e)})).isSome();var r,o,i,a,u,s}},moveNextWord:y.curry(ed,!0),movePrevWord:y.curry(ed,!1),setupSelectedState:function(e){var t=Wf(null),n=y.curry(_c.isInlineTarget,e);return e.on("NodeChange",function(r){var o,i,a,u,s;Jf(e)&&(o=n,i=e.dom,a=r.parents,u=M.filter(i.select('*[data-mce-selected="inline-boundary"]'),o),s=M.filter(a,o),M.each(M.difference(u,s),y.curry(Qf,!1)),M.each(M.difference(s,u),y.curry(Qf,!0)),function(e,t){if(e.selection.isCollapsed()&&!0!==e.composing&&t.get()){var n=wa.fromRangeStart(e.selection.getRng());wa.isTextPosition(n)&&!1===_c.isAtZwsp(n)&&(Gf(e,Da.removeAndReposition(t.get(),n)),t.set(null))}}(e,t),function(e,t,n,r){if(t.selection.isCollapsed()){var o=M.filter(r,e);M.each(o,function(r){var o=wa.fromRangeStart(t.selection.getRng());Vf(e,t.getBody(),o).bind(function(e){return Zf(t,n,e)})})}}(n,e,t,r.parents))}),t},setCaretPosition:Gf},nd=function(e,t){return function(n){return Fl(t,n).map(function(t){return td.setCaretPosition(e,t),!0}).getOr(!1)}},rd=function(e,t,n,r){var o=e.getBody(),i=y.curry(_c.isInlineTarget,e);e.undoManager.ignore(function(){var a,u,s;e.selection.setRng((a=n,u=r,(s=document.createRange()).setStart(a.container(),a.offset()),s.setEnd(u.container(),u.offset()),s)),e.execCommand("Delete"),Vf(i,o,wa.fromRangeStart(e.selection.getRng())).map($f).map(nd(e,t))}),e.nodeChanged()},od=function(e,t,n,r){var o,i,a=(o=e.getBody(),i=r.container(),Ou(i,o)||o),u=y.curry(_c.isInlineTarget,e),s=Vf(u,a,r);return s.bind(function(e){return n?e.fold(y.constant(E.some($f(e))),E.none,y.constant(E.some(jf(e))),E.none):e.fold(E.none,y.constant(E.some(jf(e))),E.none,y.constant(E.some($f(e))))}).map(nd(e,t)).getOrThunk(function(){var o=za.navigate(n,a,r),i=o.bind(function(e){return Vf(u,a,e)});return s.isSome()&&i.isSome()?_c.findRootInline(u,a,r).map(function(t){return r=t,!!Wa([za.firstPositionIn(r),za.lastPositionIn(r)],function(e,t){var n=_c.normalizePosition(!0,e),o=_c.normalizePosition(!1,t);return za.nextPosition(r,n).map(function(e){return e.isEqual(o)}).getOr(!0)}).getOr(!0)&&(El(e,n,Fn.fromDom(t)),!0);var r}).getOr(!1):i.bind(function(i){return o.map(function(o){return n?rd(e,t,r,o):rd(e,t,o,r),!0})}).getOr(!1)})},id=function(e,t,n){if(e.selection.isCollapsed()&&!1!==e.settings.inline_boundaries){var r=wa.fromRangeStart(e.selection.getRng());return od(e,t,n,r)}return!1},ad=br.immutable("start","end"),ud=br.immutable("rng","table","cells"),sd=ll([{removeTable:["element"]},{emptyCells:["cells"]}]),cd=function(e,t){return Ic(Fn.fromDom(e),"td,th",t)},ld=function(e,t){return Pc(e,"table",t)},fd=function(e){return!1===Rr.eq(e.start(),e.end())},dd=function(e,t){return ld(e.start(),t).bind(function(n){return ld(e.end(),t).bind(function(e){return Rr.eq(n,e)?E.some(n):E.none()})})},md=function(e){return ou(e,"td,th")},pd=function(e,t){var n=cd(t.startContainer,e),r=cd(t.endContainer,e);return t.collapsed?E.none():Wa([n,r],ad).fold(function(){return n.fold(function(){return r.bind(function(t){return ld(t,e).bind(function(e){return M.head(md(e)).map(function(e){return ad(e,t)})})})},function(t){return ld(t,e).bind(function(e){return M.last(md(e)).map(function(e){return ad(t,e)})})})},function(t){return gd(e,t)?E.none():(r=e,ld((n=t).start(),r).bind(function(e){return M.last(md(e)).map(function(e){return ad(n.start(),e)})}));var n,r})},gd=function(e,t){return dd(t,e).isSome()},hd=function(e,t){var n,r,o,i,a,u=(n=e,y.curry(Rr.eq,n));return(r=t,o=u,i=cd(r.startContainer,o),a=cd(r.endContainer,o),Wa([i,a],ad).filter(fd).filter(function(e){return gd(o,e)}).orThunk(function(){return pd(o,r)})).bind(function(e){return dd(t=e,u).map(function(e){return ud(t,e,md(e))});var t})},vd=function(e,t){return M.findIndex(e,function(e){return Rr.eq(e,t)})},yd=function(e){return(t=e,Wa([vd(t.cells(),t.rng().start()),vd(t.cells(),t.rng().end())],function(e,n){return t.cells().slice(e,n+1)})).map(function(t){var n=e.cells();return t.length===n.length?sd.removeTable(e.table()):sd.emptyCells(t)});var t},bd=function(e,t){return hd(e,t).bind(yd)},Cd=function(e){var t=[];if(e)for(var n=0;n<e.rangeCount;n++)t.push(e.getRangeAt(n));return t},xd=Cd,wd=function(e){return M.bind(e,function(e){var t=Xi(e);return t?[Fn.fromDom(t)]:[]})},Nd=function(e){return Cd(e).length>1},Ed=function(e){return M.filter(wd(e),po)},Sd=function(e){return ou(e,"td[data-mce-selected],th[data-mce-selected]")},kd=function(e,t){var n=Sd(t),r=Ed(e);return n.length>0?n:r},Td=kd,Ad=function(e){return kd(xd(e.selection.getSel()),Fn.fromDom(e.getBody()))},_d=function(e,t){return M.each(t,tc),e.selection.setCursorLocation(t[0].dom(),0),!0},Rd=function(e,t){return El(e,!1,t),!0},Bd=function(e,t,n,r){return Od(t,r).fold(function(){return r=e,bd(t,n).map(function(e){return e.fold(y.curry(Rd,r),y.curry(_d,r))});var r},function(t){return Pd(e,t)}).getOr(!1)},Dd=function(e,t){return M.find(Qc(t,e),po)},Od=function(e,t){return M.find(Qc(t,e),function(e){return"caption"===Yn.name(e)})},Pd=function(e,t){return tc(t),e.selection.setCursorLocation(t.dom(),0),E.some(!0)},Ld=function(e,t,n,r,o){return za.navigate(n,e.getBody(),o).bind(function(i){return s=r,c=n,l=o,f=i,za.firstPositionIn(s.dom()).bind(function(e){return za.lastPositionIn(s.dom()).map(function(t){return c?l.isEqual(e)&&f.isEqual(t):l.isEqual(t)&&f.isEqual(e)})}).getOr(!0)?Pd(e,r):(a=r,u=i,Od(t,Fn.fromDom(u.getNode())).map(function(e){return!1===Rr.eq(e,a)}));var a,u,s,c,l,f}).or(E.some(!0))},Id=function(e,t,n,r){var o=wa.fromRangeStart(e.selection.getRng());return Dd(n,r).bind(function(r){return Hc(r)?Pd(e,r):(i=e,a=n,u=t,s=r,c=o,za.navigate(u,i.getBody(),c).bind(function(e){return Dd(a,Fn.fromDom(e.getNode())).map(function(e){return!1===Rr.eq(e,s)})}));var i,a,u,s,c})},Md=function(e,t,n){var r=Fn.fromDom(e.getBody());return Od(r,n).fold(function(){return Id(e,t,r,n)},function(n){return o=e,i=t,a=r,u=n,s=wa.fromRangeStart(o.selection.getRng()),Hc(u)?Pd(o,u):Ld(o,a,i,u,s);var o,i,a,u,s}).getOr(!1)},Fd=function(e,t){var n,r,o,i,a,u=Fn.fromDom(e.selection.getStart(!0)),s=Ad(e);return e.selection.isCollapsed()&&0===s.length?Md(e,t,u):(n=e,r=u,o=Fn.fromDom(n.getBody()),i=n.selection.getRng(),0!==(a=Ad(n)).length?_d(n,a):Bd(n,o,i,r))},zd=function(e,t){e.getDoc().execCommand(t,!1,null)},Ud={deleteCommand:function(e){Tl(e,!1)||id(e,!1)||il(e,!1)||Fd(e)||cl(e,!1)||(zd(e,"Delete"),Bc(e))},forwardDeleteCommand:function(e){Tl(e,!0)||id(e,!0)||il(e,!0)||Fd(e)||cl(e,!0)||zd(e,"ForwardDelete")}},qd={isEq:function(e,t){return e&&t&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset}},Vd=br.immutable("container","offset"),Hd=function(e,t,n){return null!==function(e,t,n){for(;e&&e!==t;){if(n(e))return e;e=e.parentNode}return null}(e,t,n)},jd=function(e,t,n){return Hd(e,t,function(e){return e.nodeName===n})},$d=function(e){return e&&"TABLE"===e.nodeName},Wd=function(e,t,n){for(var r=new Zr(t,e.getParent(t.parentNode,e.isBlock)||e.getRoot());t=r[n?"prev":"next"]();)if(So.isBr(t))return!0},Kd=function(e,t,n,r,o){var i,a,u,s,c,l,f=e.getRoot(),d=e.schema.getNonEmptyElements();if(u=e.getParent(o.parentNode,e.isBlock)||f,r&&So.isBr(o)&&t&&e.isEmpty(u))return E.some(Vd(o.parentNode,e.nodeIndex(o)));for(i=new Zr(o,u);s=i[r?"prev":"next"]();){if("false"===e.getContentEditableParent(s)||(l=f,Ei(c=s)&&!1===Hd(c,l,Nf.isCaretNode)))return E.none();if(So.isText(s)&&s.nodeValue.length>0)return!1===jd(s,f,"A")?E.some(Vd(s,r?s.nodeValue.length:0)):E.none();if(e.isBlock(s)||d[s.nodeName.toLowerCase()])return E.none();a=s}return n&&a?E.some(Vd(a,0)):E.none()},Xd=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m,p=e.getRoot(),g=!1;if(o=r[(n?"start":"end")+"Container"],i=r[(n?"start":"end")+"Offset"],l=So.isElement(o)&&i===o.childNodes.length,s=e.schema.getNonEmptyElements(),c=n,Ei(o))return E.none();if(So.isElement(o)&&i>o.childNodes.length-1&&(c=!1),So.isDocument(o)&&(o=p,i=0),o===p){if(c&&(u=o.childNodes[i>0?i-1:0])){if(Ei(u))return E.none();if(s[u.nodeName]||$d(u))return E.none()}if(o.hasChildNodes()){if(i=Math.min(!c&&i>0?i-1:i,o.childNodes.length-1),o=o.childNodes[i],i=So.isText(o)&&l?o.data.length:0,!t&&o===p.lastChild&&$d(o))return E.none();if(function(e,t){for(;t&&t!==e;){if(So.isContentEditableFalse(t))return!0;t=t.parentNode}return!1}(p,o)||Ei(o))return E.none();if(o.hasChildNodes()&&!1===$d(o)){u=o,a=new Zr(o,p);do{if(So.isContentEditableFalse(u)||Ei(u)){g=!1;break}if(So.isText(u)&&u.nodeValue.length>0){i=c?0:u.nodeValue.length,o=u,g=!0;break}if(s[u.nodeName.toLowerCase()]&&(!(f=u)||!/^(TD|TH|CAPTION)$/.test(f.nodeName))){i=e.nodeIndex(u),o=u.parentNode,"IMG"!==u.nodeName&&"PRE"!==u.nodeName||c||i++,g=!0;break}}while(u=c?a.next():a.prev())}}}return t&&(So.isText(o)&&0===i&&Kd(e,l,t,!0,o).each(function(e){o=e.container(),i=e.offset(),g=!0}),So.isElement(o)&&((u=o.childNodes[i])||(u=o.childNodes[i-1]),!u||!So.isBr(u)||(m="A",(d=u).previousSibling&&d.previousSibling.nodeName===m)||Wd(e,u,!1)||Wd(e,u,!0)||Kd(e,l,t,!0,u).each(function(e){o=e.container(),i=e.offset(),g=!0}))),c&&!t&&So.isText(o)&&i===o.nodeValue.length&&Kd(e,l,t,!1,o).each(function(e){o=e.container(),i=e.offset(),g=!0}),g?E.some(Vd(o,i)):E.none()},Yd={normalize:function(e,t){var n=t.collapsed,r=t.cloneRange();return Xd(e,n,!0,r).each(function(e){r.setStart(e.container(),e.offset())}),n||Xd(e,n,!1,r).each(function(e){r.setEnd(e.container(),e.offset())}),n&&r.collapse(!0),qd.isEq(t,r)?E.none():E.some(r)}},Gd=function(e,t,n){var r=e.create("span",{},"&nbsp;");n.parentNode.insertBefore(r,n),t.scrollIntoView(r),e.remove(r)},Jd=function(e,t,n,r){var o=e.createRng();r?(o.setStartBefore(n),o.setEndBefore(n)):(o.setStartAfter(n),o.setEndAfter(n)),t.setRng(o)},Qd=function(e,t){var n,r,o=e.selection,i=e.dom,a=o.getRng();Yd.normalize(i,a).each(function(e){a.setStart(e.startContainer,e.startOffset),a.setEnd(e.endContainer,e.endOffset)});var u=a.startOffset,s=a.startContainer;if(1===s.nodeType&&s.hasChildNodes()){var c=u>s.childNodes.length-1;s=s.childNodes[Math.min(u,s.childNodes.length-1)]||s,u=c&&3===s.nodeType?s.nodeValue.length:0}var l=i.getParent(s,i.isBlock),f=l?i.getParent(l.parentNode,i.isBlock):null,d=f?f.nodeName.toUpperCase():"",m=t&&t.ctrlKey;"LI"!==d||m||(l=f),s&&3===s.nodeType&&u>=s.nodeValue.length&&(function(e,t,n){for(var r,o=new Zr(t,n),i=e.getNonEmptyElements();r=o.next();)if(i[r.nodeName.toLowerCase()]||r.length>0)return!0}(e.schema,s,l)||(n=i.create("br"),a.insertNode(n),a.setStartAfter(n),a.setEndAfter(n),r=!0)),n=i.create("br"),a.insertNode(n),Gd(i,o,n),Jd(i,o,n,r),e.undoManager.add()},Zd=function(e,t){var n=Fn.fromTag("br");Ks.before(Fn.fromDom(t),n),e.undoManager.add()},em=function(e,t){tm(e.getBody(),t)||Ks.after(Fn.fromDom(t),Fn.fromTag("br"));var n=Fn.fromTag("br");Ks.after(Fn.fromDom(t),n),Gd(e.dom,e.selection,n.dom()),Jd(e.dom,e.selection,n.dom(),!1),e.undoManager.add()},tm=function(e,t){return n=wa.after(t),!!So.isBr(n.getNode())||za.nextPosition(e,wa.after(t)).map(function(e){return So.isBr(e.getNode())}).getOr(!1);var n},nm=function(e){return e&&"A"===e.nodeName&&"href"in e},rm=function(e){return e.fold(y.constant(!1),nm,nm,y.constant(!1))},om=function(e,t){t.fold(y.noop,y.curry(Zd,e),y.curry(em,e),y.noop)},im={insert:function(e,t){var n,r,o,i=(n=e,r=y.curry(_c.isInlineTarget,n),o=wa.fromRangeStart(n.selection.getRng()),Vf(r,n.getBody(),o).filter(rm));i.isSome()?i.each(y.curry(om,e)):Qd(e,t)}},am=ll([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),um=(am.before,am.on,am.after,function(e){return e.fold(y.identity,y.identity,y.identity)}),sm=ll([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),cm=br.immutable("start","soffset","finish","foffset"),lm={domRange:sm.domRange,relative:sm.relative,exact:sm.exact,exactFromRange:function(e){return sm.exact(e.start(),e.soffset(),e.finish(),e.foffset())},range:cm,getWin:function(e){var t=e.match({domRange:function(e){return Fn.fromDom(e.startContainer)},relative:function(e,t){return um(e)},exact:function(e,t,n,r){return e}});return Fr.defaultView(t)}},fm=In.detect().browser,dm=function(e,t){var n=Yn.isText(t)?Zs(t).length:Fr.children(t).length+1;return e>n?n:e<0?0:e},mm=function(e){return lm.range(e.start(),dm(e.soffset(),e.start()),e.finish(),dm(e.foffset(),e.finish()))},pm=function(e,t){return Rr.contains(e,t)||Rr.eq(e,t)},gm=function(e){return function(t){return pm(e,t.start())&&pm(e,t.finish())}},hm=function(e){return!0===e.inline||fm.isIE()},vm=function(e){return lm.range(Fn.fromDom(e.startContainer),e.startOffset,Fn.fromDom(e.endContainer),e.endOffset)},ym=function(e){var t=e.getSelection();return(t&&0!==t.rangeCount?E.from(t.getRangeAt(0)):E.none()).map(vm)},bm=function(e){var t=Fr.defaultView(e);return ym(t.dom()).filter(gm(e))},Cm=function(e,t){return E.from(t).filter(gm(e)).map(mm)},xm=function(e){var t=document.createRange();return t.setStart(e.start().dom(),e.soffset()),t.setEnd(e.finish().dom(),e.foffset()),E.some(t)},wm=function(e){return(e.bookmark?e.bookmark:E.none()).bind(y.curry(Cm,Fn.fromDom(e.getBody()))).bind(xm)},Nm={store:function(e){var t=hm(e)?bm(Fn.fromDom(e.getBody())):E.none();e.bookmark=t.isSome()?t:e.bookmark},storeNative:function(e,t){var n=Fn.fromDom(e.getBody()),r=(hm(e)?E.from(t):E.none()).map(vm).filter(gm(n));e.bookmark=r.isSome()?r:e.bookmark},readRange:ym,restore:function(e){wm(e).each(function(t){e.selection.setRng(t)})},getRng:wm,getBookmark:bm,validate:Cm},Em=function(e,t){var n=e.settings,r=e.dom,o=e.selection,i=e.formatter,a=/[a-z%]+$/i.exec(n.indentation)[0],u=parseInt(n.indentation,10),s=e.getParam("indent_use_margin",!1);e.queryCommandState("InsertUnorderedList")||e.queryCommandState("InsertOrderedList")||(n.forced_root_block||r.getParent(o.getNode(),r.isBlock)||i.apply("div"),M.each(o.getSelectedBlocks(),function(e){return function(e,t,n,r,o,i){if("false"!==e.getContentEditable(i)&&"LI"!==i.nodeName){var a=n?"margin":"padding";if(a="TABLE"===i.nodeName?"margin":a,a+="rtl"===e.getStyle(i,"direction",!0)?"Right":"Left","outdent"===t){var u=Math.max(0,parseInt(i.style[a]||0,10)-r);e.setStyle(i,a,u?u+o:"")}else u=parseInt(i.style[a]||0,10)+r+o,e.setStyle(i,a,u)}}(r,t,s,u,a,e)}))},Sm=Dt.each,km=Dt.extend,Tm=Dt.map,Am=Dt.inArray,_m=Dt.explode,Rm=!0,Bm=!1;function Dm(e){var t,n,r,o,i={state:{},exec:{},value:{}},a=e.settings;e.on("PreInit",function(){t=e.dom,n=e.selection,a=e.settings,r=e.formatter});var u=function(t){var n;if(!e.quirks.isHidden()&&!e.removed){if(t=t.toLowerCase(),n=i.state[t])return n(t);try{return e.getDoc().queryCommandState(t)}catch(r){}return!1}},s=function(e,t){t=t||"exec",Sm(e,function(e,n){Sm(n.toLowerCase().split(","),function(n){i[t][n]=e})})};km(this,{execCommand:function(t,n,r,o){var a,u,s=!1;if(!e.removed){if(/^(mceAddUndoLevel|mceEndUndoLevel|mceBeginUndoLevel|mceRepaint)$/.test(t)||o&&o.skip_focus?Nm.restore(e):e.focus(),(o=e.fire("BeforeExecCommand",{command:t,ui:n,value:r})).isDefaultPrevented())return!1;if(u=t.toLowerCase(),a=i.exec[u])return a(u,n,r),e.fire("ExecCommand",{command:t,ui:n,value:r}),!0;if(Sm(e.plugins,function(o){if(o.execCommand&&o.execCommand(t,n,r))return e.fire("ExecCommand",{command:t,ui:n,value:r}),s=!0,!1}),s)return s;if(e.theme&&e.theme.execCommand&&e.theme.execCommand(t,n,r))return e.fire("ExecCommand",{command:t,ui:n,value:r}),!0;try{s=e.getDoc().execCommand(t,n,r)}catch(c){}return!!s&&(e.fire("ExecCommand",{command:t,ui:n,value:r}),!0)}},queryCommandState:u,queryCommandValue:function(t){var n;if(!e.quirks.isHidden()&&!e.removed){if(t=t.toLowerCase(),n=i.value[t])return n(t);try{return e.getDoc().queryCommandValue(t)}catch(r){}}},queryCommandSupported:function(t){if(t=t.toLowerCase(),i.exec[t])return!0;try{return e.getDoc().queryCommandSupported(t)}catch(n){}return!1},addCommands:s,addCommand:function(t,n,r){t=t.toLowerCase(),i.exec[t]=function(t,o,i,a){return n.call(r||e,o,i,a)}},addQueryStateHandler:function(t,n,r){t=t.toLowerCase(),i.state[t]=function(){return n.call(r||e)}},addQueryValueHandler:function(t,n,r){t=t.toLowerCase(),i.value[t]=function(){return n.call(r||e)}},hasCustomCommand:function(e){return e=e.toLowerCase(),!!i.exec[e]}});var c=function(t,n,r){return n===undefined&&(n=Bm),r===undefined&&(r=null),e.getDoc().execCommand(t,n,r)},l=function(e){return r.match(e)},f=function(t,n){r.toggle(t,n?{value:n}:undefined),e.nodeChanged()},d=function(e){o=n.getBookmark(e)},m=function(){n.moveToBookmark(o)};s({"mceResetDesignMode,mceBeginUndoLevel":function(){},"mceEndUndoLevel,mceAddUndoLevel":function(){e.undoManager.add()},"Cut,Copy,Paste":function(t){var n,r=e.getDoc();try{c(t)}catch(i){n=Rm}if("paste"!==t||r.queryCommandEnabled(t)||(n=!0),n||!r.queryCommandSupported(t)){var o=e.translate("Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.");de.mac&&(o=o.replace(/Ctrl\+/g,"\u2318+")),e.notificationManager.open({text:o,type:"error"})}},unlink:function(){if(n.isCollapsed()){var t=e.dom.getParent(e.selection.getStart(),"a");t&&e.dom.remove(t,!0)}else r.remove("link")},"JustifyLeft,JustifyCenter,JustifyRight,JustifyFull,JustifyNone":function(e){var t=e.substring(7);"full"===t&&(t="justify"),Sm("left,center,right,justify".split(","),function(e){t!==e&&r.remove("align"+e)}),"none"!==t&&f("align"+t)},"InsertUnorderedList,InsertOrderedList":function(e){var r,o;c(e),(r=t.getParent(n.getNode(),"ol,ul"))&&(o=r.parentNode,/^(H[1-6]|P|ADDRESS|PRE)$/.test(o.nodeName)&&(d(),t.split(o,r),m()))},"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":function(e){f(e)},"ForeColor,HiliteColor,FontName":function(e,t,n){f(e,n)},FontSize:function(e,t,n){var r,o;n>=1&&n<=7&&(o=_m(a.font_size_style_values),n=(r=_m(a.font_size_classes))?r[n-1]||n:o[n-1]||n),f(e,n)},RemoveFormat:function(e){r.remove(e)},mceBlockQuote:function(){f("blockquote")},FormatBlock:function(e,t,n){return f(n||"p")},mceCleanup:function(){var t=n.getBookmark();e.setContent(e.getContent({cleanup:Rm}),{cleanup:Rm}),n.moveToBookmark(t)},mceRemoveNode:function(t,r,o){var i=o||n.getNode();i!==e.getBody()&&(d(),e.dom.remove(i,Rm),m())},mceSelectNodeDepth:function(r,o,i){var a=0;t.getParent(n.getNode(),function(e){if(1===e.nodeType&&a++===i)return n.select(e),Bm},e.getBody())},mceSelectNode:function(e,t,r){n.select(r)},mceInsertContent:function(t,n,r){lc.insertAtCaret(e,r)},mceInsertRawHTML:function(t,r,o){n.setContent("tiny_mce_marker"),e.setContent(e.getContent().replace(/tiny_mce_marker/g,function(){return o}))},mceToggleFormat:function(e,t,n){f(n)},mceSetContent:function(t,n,r){e.setContent(r)},"Indent,Outdent":function(t){Em(e,t)},mceRepaint:function(){},InsertHorizontalRule:function(){e.execCommand("mceInsertContent",!1,"<hr />")},mceToggleVisualAid:function(){e.hasVisual=!e.hasVisual,e.addVisual()},mceReplaceContent:function(t,r,o){e.execCommand("mceInsertContent",!1,o.replace(/\{\$selection\}/g,n.getContent({format:"text"})))},mceInsertLink:function(e,o,i){var a;"string"==typeof i&&(i={href:i}),a=t.getParent(n.getNode(),"a"),i.href=i.href.replace(" ","%20"),a&&i.href||r.remove("link"),i.href&&r.apply("link",i,a)},selectAll:function(){var e=t.getParent(n.getStart(),So.isContentEditableTrue);if(e){var r=t.createRng();r.selectNodeContents(e),n.setRng(r)}},"delete":function(){Ud.deleteCommand(e)},forwardDelete:function(){Ud.forwardDeleteCommand(e)},mceNewDocument:function(){e.setContent("")},InsertLineBreak:function(t,n,r){return im.insert(e,r),!0}}),s({"JustifyLeft,JustifyCenter,JustifyRight,JustifyFull":function(e){var o="align"+e.substring(7),i=n.isCollapsed()?[t.getParent(n.getNode(),t.isBlock)]:n.getSelectedBlocks(),a=Tm(i,function(e){return!!r.matchNode(e,o)});return-1!==Am(a,Rm)},"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":function(e){return l(e)},mceBlockQuote:function(){return l("blockquote")},Outdent:function(){var e;if(a.inline_styles){if((e=t.getParent(n.getStart(),t.isBlock))&&parseInt(e.style.paddingLeft,10)>0)return Rm;if((e=t.getParent(n.getEnd(),t.isBlock))&&parseInt(e.style.paddingLeft,10)>0)return Rm}return u("InsertUnorderedList")||u("InsertOrderedList")||!a.inline_styles&&!!t.getParent(n.getNode(),"BLOCKQUOTE")},"InsertUnorderedList,InsertOrderedList":function(e){var r=t.getParent(n.getNode(),"ul,ol");return r&&("insertunorderedlist"===e&&"UL"===r.tagName||"insertorderedlist"===e&&"OL"===r.tagName)}},"state"),s({"FontSize,FontName":function(e){var r,o=0;return(r=t.getParent(n.getNode(),"span"))&&(o="fontsize"===e?r.style.fontSize:r.style.fontFamily.replace(/, /g,",").replace(/[\'\"]/g,"").toLowerCase()),o}},"value"),s({Undo:function(){e.undoManager.undo()},Redo:function(){e.undoManager.redo()}})}var Om=Dt.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchmove touchend"," "),Pm=function(e){var t,n,r=this,o={},i=function(){return!1},a=function(){return!0};t=(e=e||{}).scope||r,n=e.toggleEvent||i;var u=function(e,t,a,u){var s,c,l;if(!1===t&&(t=i),t)for(t={func:t},u&&Dt.extend(t,u),l=(c=e.toLowerCase().split(" ")).length;l--;)e=c[l],(s=o[e])||(s=o[e]=[],n(e,!0)),a?s.unshift(t):s.push(t);return r},s=function(e,t){var i,a,u,s,c;if(e)for(i=(s=e.toLowerCase().split(" ")).length;i--;){if(e=s[i],a=o[e],!e){for(u in o)n(u,!1),delete o[u];return r}if(a){if(t)for(c=a.length;c--;)a[c].func===t&&(a=a.slice(0,c).concat(a.slice(c+1)),o[e]=a);else a.length=0;a.length||(n(e,!1),delete o[e])}}else{for(e in o)n(e,!1);o={}}return r};r.fire=function(n,r){var u,c,l,f;if(n=n.toLowerCase(),(r=r||{}).type=n,r.target||(r.target=t),r.preventDefault||(r.preventDefault=function(){r.isDefaultPrevented=a},r.stopPropagation=function(){r.isPropagationStopped=a},r.stopImmediatePropagation=function(){r.isImmediatePropagationStopped=a},r.isDefaultPrevented=i,r.isPropagationStopped=i,r.isImmediatePropagationStopped=i),e.beforeFire&&e.beforeFire(r),u=o[n])for(c=0,l=u.length;c<l;c++){if((f=u[c]).once&&s(n,f.func),r.isImmediatePropagationStopped())return r.stopPropagation(),r;if(!1===f.func.call(t,r))return r.preventDefault(),r}return r},r.on=u,r.off=s,r.once=function(e,t,n){return u(e,t,n,{once:!0})},r.has=function(e){return e=e.toLowerCase(),!(!o[e]||0===o[e].length)}};Pm.isNative=function(e){return!!Om[e.toLowerCase()]};var Lm,Im=function(e){return e._eventDispatcher||(e._eventDispatcher=new Pm({scope:e,toggleEvent:function(t,n){Pm.isNative(t)&&e.toggleNativeEvent&&e.toggleNativeEvent(t,n)}})),e._eventDispatcher},Mm={fire:function(e,t,n){if(this.removed&&"remove"!==e)return t;if(t=Im(this).fire(e,t,n),!1!==n&&this.parent)for(var r=this.parent();r&&!t.isPropagationStopped();)r.fire(e,t,!1),r=r.parent();return t},on:function(e,t,n){return Im(this).on(e,t,n)},off:function(e,t){return Im(this).off(e,t)},once:function(e,t){return Im(this).once(e,t)},hasEventListeners:function(e){return Im(this).has(e)}},Fm=ui.DOM,zm=function(e,t){return"selectionchange"===t?e.getDoc():!e.inline&&/^mouse|touch|click|contextmenu|drop|dragover|dragend/.test(t)?e.getDoc().documentElement:e.settings.event_root?(e.eventRoot||(e.eventRoot=Fm.select(e.settings.event_root)[0]),e.eventRoot):e.getBody()},Um=function(e,t){var n,r,o=function(e){return!e.hidden&&!e.readonly};if(e.delegates||(e.delegates={}),!e.delegates[t]&&!e.removed)if(n=zm(e,t),e.settings.event_root){if(Lm||(Lm={},e.editorManager.on("removeEditor",function(){var t;if(!e.editorManager.activeEditor&&Lm){for(t in Lm)e.dom.unbind(zm(e,t));Lm=null}})),Lm[t])return;r=function(n){for(var r=n.target,i=e.editorManager.get(),a=i.length;a--;){var u=i[a].getBody();(u===r||Fm.isChildOf(r,u))&&o(i[a])&&i[a].fire(t,n)}},Lm[t]=r,Fm.bind(n,t,r)}else r=function(n){o(e)&&e.fire(t,n)},Fm.bind(n,t,r),e.delegates[t]=r},qm={bindPendingEventDelegates:function(){var e=this;Dt.each(e._pendingNativeEvents,function(t){Um(e,t)})},toggleNativeEvent:function(e,t){var n=this;"focus"!==e&&"blur"!==e&&(t?n.initialized?Um(n,e):n._pendingNativeEvents?n._pendingNativeEvents.push(e):n._pendingNativeEvents=[e]:n.initialized&&(n.dom.unbind(zm(n,e),e,n.delegates[e]),delete n.delegates[e]))},unbindAllNativeEvents:function(){var e,t=this,n=t.getBody(),r=t.dom;if(t.delegates){for(e in t.delegates)t.dom.unbind(zm(t,e),e,t.delegates[e]);delete t.delegates}!t.inline&&n&&r&&(n.onload=null,r.unbind(t.getWin()),r.unbind(t.getDoc())),r&&(r.unbind(n),r.unbind(t.getContainer()))}},Vm=qm=Dt.extend({},Mm,qm),Hm=function(e,t,n){try{e.getDoc().execCommand(t,!1,n)}catch(r){}},jm=function(e,t){var n,r,o;e._clickBlocker&&(e._clickBlocker.unbind(),e._clickBlocker=null),t?(e._clickBlocker=(r=(n=e).getBody(),o=function(e){n.dom.getParents(e.target,"a").length>0&&e.preventDefault()},n.dom.bind(r,"click",o),{unbind:function(){n.dom.unbind(r,"click",o)}}),e.selection.controlSelection.hideResizeRect(),e.readonly=!0,e.getBody().contentEditable=!1):(e.readonly=!1,e.getBody().contentEditable=!0,Hm(e,"StyleWithCSS",!1),Hm(e,"enableInlineTableEditing",!1),Hm(e,"enableObjectResizing",!1),e.focus(),e.nodeChanged())},$m=function(e,t){var n=e.readonly?"readonly":"design";t!==n&&(e.initialized?jm(e,"readonly"===t):e.on("init",function(){jm(e,"readonly"===t)}),e.fire("SwitchMode",{mode:t}))},Wm=Dt.each,Km=Dt.explode,Xm={f9:120,f10:121,f11:122},Ym=Dt.makeMap("alt,ctrl,shift,meta,access");function Gm(e){var t={},n=[],r=function(e){var t,n,r={};for(n in Wm(Km(e,"+"),function(e){e in Ym?r[e]=!0:/^[0-9]{2,}$/.test(e)?r.keyCode=parseInt(e,10):(r.charCode=e.charCodeAt(0),r.keyCode=Xm[e]||e.toUpperCase().charCodeAt(0))}),t=[r.keyCode],Ym)r[n]?t.push(n):r[n]=!1;return r.id=t.join(","),r.access&&(r.alt=!0,de.mac?r.ctrl=!0:r.shift=!0),r.meta&&(de.mac?r.meta=!0:(r.ctrl=!0,r.meta=!1)),r},o=function(t,n,o,i){var a;return(a=Dt.map(Km(t,">"),r))[a.length-1]=Dt.extend(a[a.length-1],{func:o,scope:i||e}),Dt.extend(a[0],{desc:e.translate(n),subpatterns:a.slice(1)})},i=function(e,t){return!!t&&t.ctrl===e.ctrlKey&&t.meta===e.metaKey&&t.alt===e.altKey&&t.shift===e.shiftKey&&!!(e.keyCode===t.keyCode||e.charCode&&e.charCode===t.charCode)&&(e.preventDefault(),!0)},a=function(e){return e.func?e.func.call(e.scope):null};e.on("keyup keypress keydown",function(e){var r,o;((o=e).altKey||o.ctrlKey||o.metaKey||"keydown"===(r=e).type&&r.keyCode>=112&&r.keyCode<=123)&&!e.isDefaultPrevented()&&(Wm(t,function(t){if(i(e,t))return n=t.subpatterns.slice(0),"keydown"===e.type&&a(t),!0}),i(e,n[0])&&(1===n.length&&"keydown"===e.type&&a(n[0]),n.shift()))}),this.add=function(n,r,i,a){var u;return u=i,"string"==typeof i?i=function(){e.execCommand(u,!1,null)}:Dt.isArray(u)&&(i=function(){e.execCommand(u[0],u[1],u[2])}),Wm(Km(Dt.trim(n.toLowerCase())),function(e){var n=o(e,r,i,a);t[n.id]=n}),!0},this.remove=function(e){var n=o(e);return!!t[n.id]&&(delete t[n.id],!0)}}var Jm=function(e){var t=e!==undefined?e.dom():document;return E.from(t.activeElement).map(Fn.fromDom)},Qm=function(e){var t=Fr.owner(e).dom();return e.dom()===t.activeElement},Zm=function(e){return Jm(Fr.owner(e)).filter(function(t){return e.dom().contains(t.dom())})},ep=function(e,t){return(n=t,n.collapsed?E.from(Yi(n.startContainer,n.startOffset)).map(Fn.fromDom):E.none()).bind(function(t){return mo(t)?E.some(t):!1===Rr.contains(e,t)?E.some(e):E.none()});var n},tp=function(e,t){ep(Fn.fromDom(e.getBody()),t).bind(function(e){return za.firstPositionIn(e.dom())}).fold(function(){return e.selection.normalize()},function(t){return e.selection.setRng(t.toRange())})},np=function(e){if(e.setActive)try{e.setActive()}catch(t){e.focus()}else e.focus()},rp=function(e){var t,n=e.getBody();return n&&(t=Fn.fromDom(n),Qm(t)||Zm(t).isSome())},op=function(e){return e.inline?rp(e):(t=e).iframeElement&&Qm(Fn.fromDom(t.iframeElement));var t},ip=function(e){e.editorManager.setActive(e)},ap=function(e,t){e.removed||(t?ip(e):function(e){var t,n,r,o=e.selection,i=e.settings.content_editable,a=e.getBody(),u=o.getRng();if(e.quirks.refreshContentEditable(),n=e,r=o.getNode(),t=n.dom.getParent(r,function(e){return"true"===n.dom.getContentEditable(e)}),e.$.contains(a,t))return np(t),tp(e,u),void ip(e);e.bookmark!==undefined&&!1===op(e)&&Nm.getRng(e).each(function(t){e.selection.setRng(t),u=t}),i||(de.opera||np(a),e.getWin().focus()),(de.gecko||i)&&(np(a),tp(e,u)),ip(e)}(e))},up=op,sp=function(e,t){return t.dom()[e]},cp=function(e,t){return parseInt(hr(t,e),10)},lp=y.curry(sp,"clientWidth"),fp=y.curry(sp,"clientHeight"),dp=y.curry(cp,"margin-top"),mp=y.curry(cp,"margin-left"),pp={isXYInContentArea:function(e,t,n){var r,o,i,a,u,s,c,l,f,d,m=Fn.fromDom(e.getBody()),p=e.inline?m:Fr.documentElement(m),g=(r=e.inline,i=t,a=n,u=(o=p).dom().getBoundingClientRect(),{x:i-(r?u.left+o.dom().clientLeft+mp(o):0),y:a-(r?u.top+o.dom().clientTop+dp(o):0)});return c=g.x,l=g.y,f=lp(s=p),d=fp(s),c>=0&&l>=0&&c<=f&&l<=d},isEditorAttachedToDom:function(e){var t,n=e.inline?e.getBody():e.getContentAreaContainer();return(t=n,E.from(t).map(Fn.fromDom)).map(function(e){return Rr.contains(Fr.owner(e),e)}).getOr(!1)}};function gp(e){var t,n=[],r=function(){var t,n=e.theme;return n&&n.getNotificationManagerImpl?n.getNotificationManagerImpl():{open:t=function(){throw new Error("Theme did not provide a NotificationManager implementation.")},close:t,reposition:t,getArgs:t}},o=function(){n.length>0&&r().reposition(n)},i=function(e){M.findIndex(n,function(t){return t===e}).each(function(e){n.splice(e,1)})},a=function(t){if(!e.removed&&pp.isEditorAttachedToDom(e))return M.find(n,function(e){return n=r().getArgs(e),o=t,!(n.type!==o.type||n.text!==o.text||n.progressBar||n.timeout||o.progressBar||o.timeout);var n,o}).getOrThunk(function(){e.editorManager.setActive(e);var a,u=r().open(t,function(){i(u),o()});return a=u,n.push(a),o(),u})};return(t=e).on("SkinLoaded",function(){var e=t.settings.service_message;e&&a({text:e,type:"warning",timeout:0,icon:""})}),t.on("ResizeEditor ResizeWindow",function(){ve.requestAnimationFrame(o)}),t.on("remove",function(){M.each(n,function(e){r().close(e)})}),{open:a,close:function(){E.from(n[0]).each(function(e){r().close(e),i(e),o()})},getNotifications:function(){return n}}}function hp(e){var t=[],n=function(){var t,n=e.theme;return n&&n.getWindowManagerImpl?n.getWindowManagerImpl():{open:t=function(){throw new Error("Theme did not provide a WindowManager implementation.")},alert:t,confirm:t,close:t,getParams:t,setParams:t}},r=function(e,t){return function(){return t?t.apply(e,arguments):undefined}},o=function(n){var r;t.push(n),r=n,e.fire("OpenWindow",{win:r})},i=function(n){M.findIndex(t,function(e){return e===n}).each(function(r){var o;t.splice(r,1),o=n,e.fire("CloseWindow",{win:o}),0===t.length&&e.focus()})},a=function(){return E.from(t[t.length-1])};return e.on("remove",function(){M.each(t.slice(0),function(e){n().close(e)})}),{windows:t,open:function(t,r){e.editorManager.setActive(e),Nm.store(e);var a=n().open(t,r,i);return o(a),a},alert:function(e,t,a){var u=n().alert(e,r(a||this,t),i);o(u)},confirm:function(e,t,a){var u=n().confirm(e,r(a||this,t),i);o(u)},close:function(){a().each(function(e){n().close(e),i(e)})},getParams:function(){return a().map(n().getParams).getOr(null)},setParams:function(e){a().each(function(t){n().setParams(t,e)})},getWindows:function(){return t}}}var vp=pi.PluginManager,yp=function(e,t){var n=function(e,t){for(var n in vp.urls)if(vp.urls[n]+"/plugin"+t+".js"===e)return n;return null}(t,e.suffix);return n?"Failed to load plugin: "+n+" from url "+t:"Failed to load plugin url: "+t},bp=function(e,t){e.notificationManager.open({type:"error",text:t})},Cp=function(e,t){e._skinLoaded?bp(e,t):e.on("SkinLoaded",function(){bp(e,t)})},xp={pluginLoadError:function(e,t){Cp(e,yp(e,t))},uploadError:function(e,t){Cp(e,"Failed to upload image: "+t)},displayError:Cp,initError:function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=window.console;r&&(r.error?r.error.apply(r,arguments):r.log.apply(r,arguments))}},wp=pi.PluginManager,Np=pi.ThemeManager;function Ep(){return new(q.getOrDie("XMLHttpRequest"))}function Sp(e,t){var n={},r=function(e,n,r,o){var i,a;(i=new Ep).open("POST",t.url),i.withCredentials=t.credentials,i.upload.onprogress=function(e){o(e.loaded/e.total*100)},i.onerror=function(){r("Image upload failed due to a XHR Transport error. Code: "+i.status)},i.onload=function(){var e,o,a;i.status<200||i.status>=300?r("HTTP Error: "+i.status):(e=JSON.parse(i.responseText))&&"string"==typeof e.location?n((o=t.basePath,a=e.location,o?o.replace(/\/$/,"")+"/"+a.replace(/^\//,""):a)):r("Invalid JSON: "+i.responseText)},(a=new FormData).append("file",e.blob(),e.filename()),i.send(a)},o=function(e,t){return{url:t,blobInfo:e,status:!0}},i=function(e,t){return{url:"",blobInfo:e,status:!1,error:t}},a=function(e,t){Dt.each(n[e],function(e){e(t)}),delete n[e]},u=function(r,u){return r=Dt.grep(r,function(t){return!e.isUploaded(t.blobUri())}),me.all(Dt.map(r,function(r){return e.isPending(r.blobUri())?(f=r.blobUri(),new me(function(e){n[f]=n[f]||[],n[f].push(e)})):(s=r,c=t.handler,l=u,e.markPending(s.blobUri()),new me(function(t){var n;try{var r=function(){n&&n.close()};c(s,function(n){r(),e.markUploaded(s.blobUri(),n),a(s.blobUri(),o(s,n)),t(o(s,n))},function(n){r(),e.removeFailed(s.blobUri()),a(s.blobUri(),i(s,n)),t(i(s,n))},function(e){e<0||e>100||(n||(n=l()),n.progressBar.value(e))})}catch(u){t(i(s,u.message))}}));var s,c,l,f}))};return t=Dt.extend({credentials:!1,handler:r},t),{upload:function(e,n){return t.url||t.handler!==r?u(e,n):new me(function(e){e([])})}}}function kp(e,t){return new(q.getOrDie("Blob"))(e,t)}var Tp=function(e){return q.getOrDie("atob")(e)},Ap=function(e){var t,n;return e=decodeURIComponent(e).split(","),(n=/data:([^;]+)/.exec(e[0]))&&(t=n[1]),{type:t,data:e[1]}},_p=function(e){return new me(function(t){var n,r,o;e=Ap(e);try{n=Tp(e.data)}catch(vx){return void t(new kp([]))}for(r=new function(e){return new(q.getOrDie("Uint8Array"))(e)}(n.length),o=0;o<r.length;o++)r[o]=n.charCodeAt(o);t(new kp([r],{type:e.type}))})},Rp=function(e){return 0===e.indexOf("blob:")?(t=e,new me(function(e,n){var r=function(){n("Cannot convert "+t+" to Blob. Resource might not exist or is inaccessible.")};try{var o=new Ep;o.open("GET",t,!0),o.responseType="blob",o.onload=function(){200===this.status?e(this.response):r()},o.onerror=r,o.send()}catch(i){r()}})):0===e.indexOf("data:")?_p(e):null;var t},Bp=function(e){return new me(function(t){var n=new function(){return new(q.getOrDie("FileReader"))};n.onloadend=function(){t(n.result)},n.readAsDataURL(e)})},Dp=Ap,Op=0,Pp=function(e){return(e||"blobid")+Op++},Lp=function(e,t,n,r){var o,i;0!==t.src.indexOf("blob:")?(o=Dp(t.src).data,(i=e.findFirst(function(e){return e.base64()===o}))?n({image:t,blobInfo:i}):Rp(t.src).then(function(r){i=e.create(Pp(),r,o),e.add(i),n({image:t,blobInfo:i})},function(e){r(e)})):(i=e.getByUri(t.src))?n({image:t,blobInfo:i}):Rp(t.src).then(function(r){Bp(r).then(function(a){o=Dp(a).data,i=e.create(Pp(),r,o),e.add(i),n({image:t,blobInfo:i})})},function(e){r(e)})},Ip=function(e){return e?e.getElementsByTagName("img"):[]},Mp=0,Fp={uuid:function(e){return e+Mp+++(t=function(){return Math.round(4294967295*Math.random()).toString(36)},"s"+(new Date).getTime().toString(36)+t()+t()+t());var t}};function zp(e){var t,n,r,o,i,a,u,s,c,l,f=(t=[],n=ea.constant,r=function(e){var t,r,o;if(!e.blob||!e.base64)throw new Error("blob and base64 representations of the image are required for BlobInfo to be created");return t=e.id||Fp.uuid("blobid"),r=e.name||t,{id:n(t),name:n(r),filename:n(r+"."+(o=e.blob.type,{"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png"}[o.toLowerCase()]||"dat")),blob:n(e.blob),base64:n(e.base64),blobUri:n(e.blobUri||H.createObjectURL(e.blob)),uri:n(e.uri)}},{create:function(e,t,n,o){return r("object"==typeof e?e:{id:e,name:o,blob:t,base64:n})},add:function(e){o(e.id())||t.push(e)},get:o=function(e){return i(function(t){return t.id()===e})},getByUri:function(e){return i(function(t){return t.blobUri()===e})},findFirst:i=function(e){return Tt.filter(t,e)[0]},removeByUri:function(e){t=Tt.filter(t,function(t){return t.blobUri()!==e||(H.revokeObjectURL(t.blobUri()),!1)})},destroy:function(){Tt.each(t,function(e){H.revokeObjectURL(e.blobUri())}),t=[]}}),d=e.settings,m=(s={},c=function(e,t){return{status:e,resultUri:t}},{hasBlobUri:l=function(e){return e in s},getResultUri:function(e){var t=s[e];return t?t.resultUri:null},isPending:function(e){return!!l(e)&&1===s[e].status},isUploaded:function(e){return!!l(e)&&2===s[e].status},markPending:function(e){s[e]=c(1,null)},markUploaded:function(e,t){s[e]=c(2,t)},removeFailed:function(e){delete s[e]},destroy:function(){s={}}}),p=function(t){return function(n){return e.selection?t(n):[]}},g=function(e,t,n){for(var r=0;-1!==(r=e.indexOf(t,r))&&(e=e.substring(0,r)+n+e.substr(r+t.length),r+=n.length-t.length+1),-1!==r;);return e},h=function(e,t,n){return e=g(e,'src="'+t+'"','src="'+n+'"'),e=g(e,'data-mce-src="'+t+'"','data-mce-src="'+n+'"')},v=function(t,n){Tt.each(e.undoManager.data,function(e){"fragmented"===e.type?e.fragments=Tt.map(e.fragments,function(e){return h(e,t,n)}):e.content=h(e.content,t,n)})},y=function(){return e.notificationManager.open({text:e.translate("Image uploading..."),type:"info",timeout:-1,progressBar:!0})},b=function(t,n){f.removeByUri(t.src),v(t.src,n),e.$(t).attr({src:d.images_reuse_filename?n+"?"+(new Date).getTime():n,"data-mce-src":e.convertURL(n,"src")})},C=function(t){return a||(a=Sp(m,{url:d.images_upload_url,basePath:d.images_upload_base_path,credentials:d.images_upload_credentials,handler:d.images_upload_handler})),N().then(p(function(n){var r;return r=Tt.map(n,function(e){return e.blobInfo}),a.upload(r,y).then(p(function(r){var o=Tt.map(r,function(t,r){var o=n[r].image;return t.status&&!1!==e.settings.images_replace_blob_uris?b(o,t.url):t.error&&xp.uploadError(e,t.error),{element:o,status:t.status}});return t&&t(o),o}))}))},x=function(e){if(!1!==d.automatic_uploads)return C(e)},w=function(e){return!d.images_dataimg_filter||d.images_dataimg_filter(e)},N=function(){var t,n,r;return u||(t=m,n=f,r={},u={findAll:function(e,o){var i;o||(o=ea.constant(!0)),i=Tt.filter(Ip(e),function(e){var n=e.src;return!!de.fileApi&&!e.hasAttribute("data-mce-bogus")&&!e.hasAttribute("data-mce-placeholder")&&!(!n||n===de.transparentSrc)&&(0===n.indexOf("blob:")?!t.isUploaded(n):0===n.indexOf("data:")&&o(e))});var a=Tt.map(i,function(e){if(r[e.src])return new me(function(t){r[e.src].then(function(n){if("string"==typeof n)return n;t({image:e,blobInfo:n.blobInfo})})});var t=new me(function(t,r){Lp(n,e,t,r)}).then(function(e){return delete r[e.image.src],e})["catch"](function(t){return delete r[e.src],t});return r[e.src]=t,t});return me.all(a)}}),u.findAll(e.getBody(),w).then(p(function(t){return t=Tt.filter(t,function(t){return"string"!=typeof t||(xp.displayError(e,t),!1)}),Tt.each(t,function(e){v(e.image.src,e.blobInfo.blobUri()),e.image.src=e.blobInfo.blobUri(),e.image.removeAttribute("data-mce-src")}),t}))},E=function(t){return t.replace(/src="(blob:[^"]+)"/g,function(t,n){var r=m.getResultUri(n);if(r)return'src="'+r+'"';var o=f.getByUri(n);return o||(o=Tt.reduce(e.editorManager.get(),function(e,t){return e||t.editorUpload&&t.editorUpload.blobCache.getByUri(n)},null)),o?'src="data:'+o.blob().type+";base64,"+o.base64()+'"':t})};return e.on("setContent",function(){!1!==e.settings.automatic_uploads?x():N()}),e.on("RawSaveContent",function(e){e.content=E(e.content)}),e.on("getContent",function(e){e.source_view||"raw"===e.format||(e.content=E(e.content))}),e.on("PostRender",function(){e.parser.addNodeFilter("img",function(e){Tt.each(e,function(e){var t=e.attr("src");if(!f.getByUri(t)){var n=m.getResultUri(t);n&&e.attr("src",n)}})})}),{blobCache:f,uploadImages:C,uploadImagesAuto:x,scanForImages:N,destroy:function(){f.destroy(),m.destroy(),u=a=null}}}var Up=function(e,t){return e.hasOwnProperty(t.nodeName)},qp=function(e,t){if(So.isText(t)){if(0===t.nodeValue.length)return!0;if(/^\s+$/.test(t.nodeValue)&&(!t.nextSibling||Up(e,t.nextSibling)))return!0}return!1},Vp=function(e){var t,n,r,o,i,a,u,s,c,l,f,d=e.settings,m=e.dom,p=e.selection,g=e.schema,h=g.getBlockElements(),v=p.getStart(),y=e.getBody();if(f=d.forced_root_block,v&&So.isElement(v)&&f&&(l=y.nodeName.toLowerCase(),g.isValidChild(l,f.toLowerCase())&&(b=h,C=y,x=v,!M.exists(Jc(Fn.fromDom(x),Fn.fromDom(C)),function(e){return Up(b,e.dom())})))){var b,C,x,w,N;for(n=(t=p.getRng()).startContainer,r=t.startOffset,o=t.endContainer,i=t.endOffset,c=up(e),v=y.firstChild;v;)if(w=h,N=v,So.isText(N)||So.isElement(N)&&!Up(w,N)&&!Vs.isBookmarkNode(N)){if(qp(h,v)){u=v,v=v.nextSibling,m.remove(u);continue}a||(a=m.create(f,e.settings.forced_root_block_attrs),v.parentNode.insertBefore(a,v),s=!0),u=v,v=v.nextSibling,a.appendChild(u)}else a=null,v=v.nextSibling;s&&c&&(t.setStart(n,r),t.setEnd(o,i),p.setRng(t),e.nodeChanged())}},Hp=function(e){e.settings.forced_root_block&&e.on("NodeChange",y.curry(Vp,e))};function jp(e){var t,n=[];"onselectionchange"in e.getDoc()||e.on("NodeChange Click MouseUp KeyUp Focus",function(n){var r,o;o={startContainer:(r=e.selection.getRng()).startContainer,startOffset:r.startOffset,endContainer:r.endContainer,endOffset:r.endOffset},"nodechange"!==n.type&&qd.isEq(o,t)||e.fire("SelectionChange"),t=o}),e.on("contextmenu",function(){e.fire("SelectionChange")}),e.on("SelectionChange",function(){var t=e.selection.getStart(!0);!t||!de.range&&e.selection.isCollapsed()||!function(t){var r,o;if((o=e.$(t).parentsUntil(e.getBody()).add(t)).length===n.length){for(r=o.length;r>=0&&o[r]===n[r];r--);if(-1===r)return n=o,!0}return n=o,!1}(t)&&e.dom.isChildOf(t,e.getBody())&&e.nodeChanged({selectionChange:!0})}),e.on("MouseUp",function(t){t.isDefaultPrevented()||("IMG"===e.selection.getNode().nodeName?ve.setEditorTimeout(e,function(){e.nodeChanged()}):e.nodeChanged())}),this.nodeChanged=function(t){var n,r,o,i=e.selection;e.initialized&&i&&!e.settings.disable_nodechange&&!e.readonly&&(o=e.getBody(),(n=i.getStart(!0)||o).ownerDocument===e.getDoc()&&e.dom.isChildOf(n,o)||(n=o),r=[],e.dom.getParent(n,function(e){if(e===o)return!0;r.push(e)}),(t=t||{}).element=n,t.parents=r,e.fire("NodeChange",t))}}var $p,Wp,Kp=function(e){var t,n,r,o;return o=e.getBoundingClientRect(),n=(t=e.ownerDocument).documentElement,r=t.defaultView,{top:o.top+r.pageYOffset-n.clientTop,left:o.left+r.pageXOffset-n.clientLeft}},Xp=function(e,t){return n=(u=e).inline?Kp(u.getBody()):{left:0,top:0},a=(i=e).getBody(),r=i.inline?{left:a.scrollLeft,top:a.scrollTop}:{left:0,top:0},{pageX:(o=function(e,t){if(t.target.ownerDocument!==e.getDoc()){var n=Kp(e.getContentAreaContainer()),r=(i=(o=e).getBody(),a=o.getDoc().documentElement,u={left:i.scrollLeft,top:i.scrollTop},s={left:i.scrollLeft||a.scrollLeft,top:i.scrollTop||a.scrollTop},o.inline?u:s);return{left:t.pageX-n.left+r.left,top:t.pageY-n.top+r.top}}var o,i,a,u,s;return{left:t.pageX,top:t.pageY}}(e,t)).left-n.left+r.left,pageY:o.top-n.top+r.top};var n,r,o,i,a,u},Yp=So.isContentEditableFalse,Gp=So.isContentEditableTrue,Jp=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},Qp=function(e,t){return function(n){if(0===n.button){var r=Tt.find(t.dom.getParents(n.target),ea.or(Yp,Gp));if(u=t.getBody(),Yp(s=r)&&s!==u){var o=t.dom.getPos(r),i=t.getBody(),a=t.getDoc().documentElement;e.element=r,e.screenX=n.screenX,e.screenY=n.screenY,e.maxX=(t.inline?i.scrollWidth:a.offsetWidth)-2,e.maxY=(t.inline?i.scrollHeight:a.offsetHeight)-2,e.relX=n.pageX-o.x,e.relY=n.pageY-o.y,e.width=r.offsetWidth,e.height=r.offsetHeight,e.ghost=function(e,t,n,r){var o=t.cloneNode(!0);e.dom.setStyles(o,{width:n,height:r}),e.dom.setAttrib(o,"data-mce-selected",null);var i=e.dom.create("div",{"class":"mce-drag-container","data-mce-bogus":"all",unselectable:"on",contenteditable:"false"});return e.dom.setStyles(i,{position:"absolute",opacity:.5,overflow:"hidden",border:0,padding:0,margin:0,width:n,height:r}),e.dom.setStyles(o,{margin:0,boxSizing:"border-box"}),i.appendChild(o),i}(t,r,e.width,e.height)}}var u,s}},Zp=function(e,t){return function(n){if(e.dragging&&(u=t,l=t.selection,f=l.getSel().getRangeAt(0).startContainer,s=3===f.nodeType?f.parentNode:f,c=e.element,s!==c&&!u.dom.isChildOf(s,c)&&!Yp(s))){var r=(i=e.element,(a=i.cloneNode(!0)).removeAttribute("data-mce-selected"),a),o=t.fire("drop",{targetClone:r,clientX:n.clientX,clientY:n.clientY});o.isDefaultPrevented()||(r=o.targetClone,t.undoManager.transact(function(){Jp(e.element),t.insertContent(t.dom.getOuterHTML(r)),t._selectionOverrides.hideFakeCaret()}))}var i,a,u,s,c,l,f;eg(e)}},eg=function(e){e.dragging=!1,e.element=null,Jp(e.ghost)},tg=function(e){var t,n,r,o,i,a,u,s,c,l,f,d={};t=ui.DOM,a=document,n=Qp(d,e),u=d,s=e,c=ve.throttle(function(e,t){s._selectionOverrides.hideFakeCaret(),s.selection.placeCaretAt(e,t)},0),r=function(e){var t,n,r,o,i,a,l,f,d,m,p,g,h=Math.max(Math.abs(e.screenX-u.screenX),Math.abs(e.screenY-u.screenY));if(u.element&&!u.dragging&&h>10){if(s.fire("dragstart",{target:u.element}).isDefaultPrevented())return;u.dragging=!0,s.focus()}if(u.dragging){var v=(p=u,{pageX:(g=Xp(s,e)).pageX-p.relX,pageY:g.pageY+5});d=u.ghost,m=s.getBody(),d.parentNode!==m&&m.appendChild(d),t=u.ghost,n=v,r=u.width,o=u.height,i=u.maxX,a=u.maxY,l=0,f=0,t.style.left=n.pageX+"px",t.style.top=n.pageY+"px",n.pageX+r>i&&(l=n.pageX+r-i),n.pageY+o>a&&(f=n.pageY+o-a),t.style.width=r-l+"px",t.style.height=o-f+"px",c(e.clientX,e.clientY)}},o=Zp(d,e),l=d,f=e,i=function(){eg(l),l.dragging&&f.fire("dragend")},e.on("mousedown",n),e.on("mousemove",r),e.on("mouseup",o),t.bind(a,"mousemove",r),t.bind(a,"mouseup",i),e.on("remove",function(){t.unbind(a,"mousemove",r),t.unbind(a,"mouseup",i)})},ng=function(e){var t;tg(e),(t=e).on("drop",function(e){var n="undefined"!=typeof e.clientX?t.getDoc().elementFromPoint(e.clientX,e.clientY):null;(Yp(n)||Yp(t.dom.getContentEditableParent(n)))&&e.preventDefault()})},rg=function(e){return Tt.reduce(e,function(e,t){return e.concat(function(e){var t=function(t){return Tt.map(t,function(t){return(t=Hi(t)).node=e,t})};if(So.isElement(e))return t(e.getClientRects());if(So.isText(e)){var n=e.ownerDocument.createRange();return n.setStart(e,0),n.setEnd(e,e.data.length),t(n.getClientRects())}}(t))},[])};(Wp=$p||($p={}))[Wp.Up=-1]="Up",Wp[Wp.Down=1]="Down";var og=function(e,t,n,r,o,i){var a,u,s=0,c=[],l=function(r){var i,a,l;for(l=rg([r]),-1===e&&(l=l.reverse()),i=0;i<l.length;i++)if(a=l[i],!n(a,u)){if(c.length>0&&t(a,Tt.last(c))&&s++,a.line=s,o(a))return!0;c.push(a)}};return(u=Tt.last(i.getClientRects()))?(l(a=i.getNode()),function(e,t,n,r){for(;r=Du(r,e,qi,t);)if(n(r))return}(e,r,l,a),c):c},ig=y.curry(og,$p.Up,Wi,Ki),ag=y.curry(og,$p.Down,Ki,Wi),ug=function(e){return function(t){return n=e,t.line>n;var n}},sg=function(e){return function(t){return n=e,t.line===n;var n}},cg=So.isContentEditableFalse,lg=Du,fg=function(e,t){return Math.abs(e.left-t)},dg=function(e,t){return Math.abs(e.right-t)},mg=function(e,t){return e>=t.left&&e<=t.right},pg=function(e,t){return Tt.reduce(e,function(e,n){var r,o;return r=Math.min(fg(e,t),dg(e,t)),o=Math.min(fg(n,t),dg(n,t)),mg(t,n)?n:mg(t,e)?e:o===r&&cg(n.node)?n:o<r?n:e})},gg=function(e,t,n,r){for(;r=lg(r,e,qi,t);)if(n(r))return},hg=function(e,t,n){var r,o,i,a,u,s,c,l,f=rg((o=e,Tt.filter(Tt.toArray(o.getElementsByTagName("*")),xu))),d=Tt.filter(f,function(e){return n>=e.top&&n<=e.bottom});return(r=pg(d,t))&&(r=pg((u=e,l=function(e,t){var n;return n=Tt.filter(rg([t]),function(t){return!e(t,s)}),c=c.concat(n),0===n.length},(c=[]).push(s=r),gg($p.Up,u,y.curry(l,Wi),s.node),gg($p.Down,u,y.curry(l,Ki),s.node),c),t))&&xu(r.node)?(a=t,{node:(i=r).node,before:fg(i,a)<dg(i,a)}):null},vg=function(e,t,n){return!n.collapsed&&M.foldl(n.getClientRects(),function(n,r){return n||(a=t,(i=e)>=(o=r).left&&i<=o.right&&a>=o.top&&a<=o.bottom);var o,i,a},!1)},yg=function(e,t){var n=null;return{cancel:function(){null!==n&&(clearTimeout(n),n=null)},throttle:function(){var r=arguments;null===n&&(n=setTimeout(function(){e.apply(null,r),n=null,r=null},t))}}},bg=function(e){var t=yg(function(){if(!e.removed&&e.selection.getRng().collapsed){var t=$a(e,e.selection.getRng(),!1);e.selection.setRng(t)}},0);e.on("focus",function(){t.throttle()}),e.on("blur",function(){t.cancel()})},Cg={BACKSPACE:8,DELETE:46,DOWN:40,ENTER:13,LEFT:37,RIGHT:39,SPACEBAR:32,TAB:9,UP:38,modifierPressed:function(e){return e.shiftKey||e.ctrlKey||e.altKey||this.metaKeyPressed(e)},metaKeyPressed:function(e){return de.mac?e.metaKey:e.ctrlKey&&!e.altKey}},xg=So.isContentEditableTrue,wg=So.isContentEditableFalse,Ng=Wu,Eg=$u,Sg=function(e){var t,n,r,o=e.getBody(),i=Cu(e.getBody(),function(t){return e.dom.isBlock(t)},function(){return up(e)}),a="sel-"+e.dom.uniqueId(),u=function(t){t&&e.selection.setRng(t)},s=function(){return e.selection.getRng()},c=function(t,n,r,o){return void 0===o&&(o=!0),e.fire("ShowCaret",{target:n,direction:t,before:r}).isDefaultPrevented()?null:(o&&e.selection.scrollIntoView(n,-1===t),i.show(r,n))},l=function(e,t){return t=Uu(e,o,t),-1===e?wa.fromRangeStart(t):wa.fromRangeEnd(t)},f=function(e){return Ei(e)||_i(e)||Ri(e)},d=function(e){return f(e.startContainer)||f(e.endContainer)},m=function(n,r){var o,i,u,s,f,m,p,h,v,y,b=e.$,C=e.dom;if(!n)return null;if(n.collapsed){if(!d(n))if(!1===r){if(h=l(-1,n),xu(h.getNode(!0)))return c(-1,h.getNode(!0),!1,!1);if(xu(h.getNode()))return c(-1,h.getNode(),!h.isAtEnd(),!1)}else{if(h=l(1,n),xu(h.getNode()))return c(1,h.getNode(),!h.isAtEnd(),!1);if(xu(h.getNode(!0)))return c(1,h.getNode(!0),!1,!1)}return null}return s=n.startContainer,f=n.startOffset,m=n.endOffset,3===s.nodeType&&0===f&&wg(s.parentNode)&&(s=s.parentNode,f=C.nodeIndex(s),s=s.parentNode),1!==s.nodeType?null:(m===f+1&&(o=s.childNodes[f]),wg(o)?(v=y=o.cloneNode(!0),(p=e.fire("ObjectSelected",{target:o,targetClone:v})).isDefaultPrevented()?null:(i=Lc(Fn.fromDom(e.getBody()),"#"+a).fold(function(){return b([])},function(e){return b([e.dom()])}),v=p.targetClone,0===i.length&&(i=b('<div data-mce-bogus="all" class="mce-offscreen-selection"></div>').attr("id",a)).appendTo(e.getBody()),n=e.dom.createRng(),v===y&&de.ie?(i.empty().append('<p style="font-size: 0" data-mce-bogus="all">\xa0</p>').append(v),n.setStartAfter(i[0].firstChild.firstChild),n.setEndAfter(v)):(i.empty().append("\xa0").append(v).append("\xa0"),n.setStart(i[0].firstChild,1),n.setEnd(i[0].lastChild,0)),i.css({top:C.getPos(o,e.getBody()).y}),i[0].focus(),(u=e.selection.getSel()).removeAllRanges(),u.addRange(n),M.each(ou(Fn.fromDom(e.getBody()),"*[data-mce-selected]"),function(e){sr.remove(e,"data-mce-selected")}),o.setAttribute("data-mce-selected","1"),t=o,g(),n)):null)},p=function(){t&&(t.removeAttribute("data-mce-selected"),Lc(Fn.fromDom(e.getBody()),"#"+a).each(Js.remove),t=null)},g=function(){i.hide()};return de.ceFalse&&(function(){var n=function(t){for(var n=e.getBody();t&&t!==n;){if(xg(t)||wg(t))return t;t=t.parentNode}return null};e.on("mouseup",function(t){var n=s();n.collapsed&&pp.isXYInContentArea(e,t.clientX,t.clientY)&&u(ja(e,n,!1))}),e.on("click",function(t){var r;(r=n(t.target))&&(wg(r)&&(t.preventDefault(),e.focus()),xg(r)&&e.dom.isChildOf(r,e.selection.getNode())&&p())}),e.on("blur NewBlock",function(){p()});var r,i,l=function(t,n){var r,o,i=e.dom.getParent(t,e.dom.isBlock),a=e.dom.getParent(n,e.dom.isBlock);return i&&(r=i,o=a,!(e.dom.getParent(r,e.dom.isBlock)===e.dom.getParent(o,e.dom.isBlock)))&&function(e){var t=ls(e);if(!e.firstChild)return!1;var n=wa.before(e.firstChild),r=t.next(n);return r&&!Eg(r)&&!Ng(r)}(i)};i=!1,(r=e).on("touchstart",function(){i=!1}),r.on("touchmove",function(){i=!0}),r.on("touchend",function(e){var t=n(e.target);wg(t)&&(i||(e.preventDefault(),m(Ha(r,t))))}),e.on("mousedown",function(t){var r,i=t.target;if((i===o||"HTML"===i.nodeName||e.dom.isChildOf(i,o))&&!1!==pp.isXYInContentArea(e,t.clientX,t.clientY))if(r=n(i))wg(r)?(t.preventDefault(),m(Ha(e,r))):(p(),xg(r)&&t.shiftKey||vg(t.clientX,t.clientY,e.selection.getRng())||e.selection.placeCaretAt(t.clientX,t.clientY));else if(!1===xu(i)){p(),g();var a=hg(o,t.clientX,t.clientY);if(a&&!l(t.target,a.node)){t.preventDefault();var s=c(1,a.node,a.before,!1);e.getBody().focus(),u(s)}}}),e.on("keypress",function(t){Cg.modifierPressed(t)||(t.keyCode,wg(e.selection.getNode())&&t.preventDefault())}),e.on("getSelectionRange",function(e){var n=e.range;if(t){if(!t.parentNode)return void(t=null);(n=n.cloneRange()).selectNode(t),e.range=n}}),e.on("setSelectionRange",function(e){var t;(t=m(e.range,e.forward))&&(e.range=t)}),e.on("AfterSetSelectionRange",function(t){var n,r=t.range;d(r)||g(),n=r.startContainer.parentNode,e.dom.hasClass(n,"mce-offscreen-selection")||p()}),e.on("copy",function(t){var n,r=t.clipboardData;if(!t.isDefaultPrevented()&&t.clipboardData&&!de.ie){var o=(n=e.dom.get(a))?n.getElementsByTagName("*")[0]:n;o&&(t.preventDefault(),r.clearData(),r.setData("text/html",o.outerHTML),r.setData("text/plain",o.outerText))}}),ng(e),bg(e)}(),n=e.contentStyles,r=".mce-content-body",n.push(i.getCss()),n.push(r+" .mce-offscreen-selection {position: absolute;left: -9999999999px;max-width: 1000000px;}"+r+" *[contentEditable=false] {cursor: default;}"+r+" *[contentEditable=true] {cursor: text;}")),{showCaret:c,showBlockCaretContainer:function(t){t.hasAttribute("data-mce-caret")&&(Bi(t),u(s()),e.selection.scrollIntoView(t[0]))},hideFakeCaret:g,destroy:function(){i.destroy(),t=null}}},kg=Dt.each,Tg=function(e){return 0===e.indexOf("data-")||0===e.indexOf("aria-")},Ag=function(e){return e.replace(/<!--|-->/g,"")},_g=function(e,t,n){var r,o,i,a,u=1;for(a=e.getShortEndedElements(),(i=/<([!?\/])?([A-Za-z0-9\-_\:\.]+)((?:\s+[^"\'>]+(?:(?:"[^"]*")|(?:\'[^\']*\')|[^>]*))*|\/|\s+)>/g).lastIndex=r=n;o=i.exec(t);){if(r=i.lastIndex,"/"===o[1])u--;else if(!o[1]){if(o[2]in a)continue;u++}if(0===u)break}return r};function Rg(e,t){void 0===t&&(t=Go());var n=function(){};!1!==(e=e||{}).fix_self_closing&&(e.fix_self_closing=!0),kg("comment cdata text start end pi doctype".split(" "),function(t){t&&(self[t]=e[t]||n)});var r=e.comment?e.comment:n,o=e.cdata?e.cdata:n,i=e.text?e.text:n,a=e.start?e.start:n,u=e.end?e.end:n,s=e.pi?e.pi:n,c=e.doctype?e.doctype:n;return{parse:function(n){var l,f,d,m,p,g,h,v,y,b,C,x,w,N,E,S,k,T,A,_,R,B,D,O,P,L,I,M,F,z=0,U=[],q=0,V=zo.decode,H=Dt.makeMap("src,href,data,background,formaction,poster"),j=/((java|vb)script|mhtml):/i,$=/^data:/i,W=function(e){var t,n;for(t=U.length;t--&&U[t].name!==e;);if(t>=0){for(n=U.length-1;n>=t;n--)(e=U[n]).valid&&u(e.name);U.length=t}},K=function(t,n,r,o,i){var a,u;if(r=(n=n.toLowerCase())in C?n:V(r||o||i||""),w&&!v&&!1===Tg(n)){if(!(a=T[n])&&A){for(u=A.length;u--&&!(a=A[u]).pattern.test(n););-1===u&&(a=null)}if(!a)return;if(a.validValues&&!(r in a.validValues))return}if(H[n]&&!e.allow_script_urls){var s=r.replace(/[\s\u0000-\u001F]+/g,"");try{s=decodeURIComponent(s)}catch(c){s=unescape(s)}if(j.test(s))return;if(!e.allow_html_data_urls&&$.test(s)&&!/^data:image\//i.test(s))return}v&&(n in H||0===n.indexOf("on"))||(m.map[n]=r,m.push({name:n,value:r}))};for(P=new RegExp("<(?:(?:!--([\\w\\W]*?)--\x3e)|(?:!\\[CDATA\\[([\\w\\W]*?)\\]\\]>)|(?:!DOCTYPE([\\w\\W]*?)>)|(?:\\?([^\\s\\/<>]+) ?([\\w\\W]*?)[?/]>)|(?:\\/([A-Za-z][A-Za-z0-9\\-_\\:\\.]*)>)|(?:([A-Za-z][A-Za-z0-9\\-_\\:\\.]*)((?:\\s+[^\"'>]+(?:(?:\"[^\"]*\")|(?:'[^']*')|[^>]*))*|\\/|\\s+)>))","g"),L=/([\w:\-]+)(?:\s*=\s*(?:(?:\"((?:[^\"])*)\")|(?:\'((?:[^\'])*)\')|([^>\s]+)))?/g,b=t.getShortEndedElements(),O=e.self_closing_elements||t.getSelfClosingElements(),C=t.getBoolAttrs(),w=e.validate,y=e.remove_internals,F=e.fix_self_closing,I=t.getSpecialElements(),D=n+">";l=P.exec(D);){if(z<l.index&&i(V(n.substr(z,l.index-z))),f=l[6])":"===(f=f.toLowerCase()).charAt(0)&&(f=f.substr(1)),W(f);else if(f=l[7]){if(l.index+l[0].length>n.length){i(V(n.substr(l.index))),z=l.index+l[0].length;continue}if(":"===(f=f.toLowerCase()).charAt(0)&&(f=f.substr(1)),x=f in b,F&&O[f]&&U.length>0&&U[U.length-1].name===f&&W(f),!w||(N=t.getElementRule(f))){if(E=!0,w&&(T=N.attributes,A=N.attributePatterns),(k=l[8])?((v=-1!==k.indexOf("data-mce-type"))&&y&&(E=!1),(m=[]).map={},k.replace(L,K)):(m=[]).map={},w&&!v){if(_=N.attributesRequired,R=N.attributesDefault,B=N.attributesForced,N.removeEmptyAttrs&&!m.length&&(E=!1),B)for(p=B.length;p--;)h=(S=B[p]).name,"{$uid}"===(M=S.value)&&(M="mce_"+q++),m.map[h]=M,m.push({name:h,value:M});if(R)for(p=R.length;p--;)(h=(S=R[p]).name)in m.map||("{$uid}"===(M=S.value)&&(M="mce_"+q++),m.map[h]=M,m.push({name:h,value:M}));if(_){for(p=_.length;p--&&!(_[p]in m.map););-1===p&&(E=!1)}if(S=m.map["data-mce-bogus"]){if("all"===S){z=_g(t,n,P.lastIndex),P.lastIndex=z;continue}E=!1}}E&&a(f,m,x)}else E=!1;if(d=I[f]){d.lastIndex=z=l.index+l[0].length,(l=d.exec(n))?(E&&(g=n.substr(z,l.index-z)),z=l.index+l[0].length):(g=n.substr(z),z=n.length),E&&(g.length>0&&i(g,!0),u(f)),P.lastIndex=z;continue}x||(k&&k.indexOf("/")===k.length-1?E&&u(f):U.push({name:f,valid:E}))}else(f=l[1])?(">"===f.charAt(0)&&(f=" "+f),e.allow_conditional_comments||"[if"!==f.substr(0,3).toLowerCase()||(f=" "+f),r(f)):(f=l[2])?o(Ag(f)):(f=l[3])?c(f):(f=l[4])&&s(f,l[5]);z=l.index+l[0].length}for(z<n.length&&i(V(n.substr(z))),p=U.length-1;p>=0;p--)(f=U[p]).valid&&u(f.name)}}}(Rg||(Rg={})).findEndTag=_g;var Bg=Rg,Dg=function(e,t){var n,r,o,i,a,u,s,c,l=t,f=/<(\w+) [^>]*data-mce-bogus="all"[^>]*>/g,d=e.schema;for(u=e.getTempAttrs(),s=l,c=new RegExp(["\\s?("+u.join("|")+')="[^"]+"'].join("|"),"gi"),l=s.replace(c,""),a=d.getShortEndedElements();i=f.exec(l);)r=f.lastIndex,o=i[0].length,n=a[i[1]]?r:Bg.findEndTag(d,l,r),l=l.substring(0,r-o)+l.substring(n),f.lastIndex=r-o;return l},Og=function(e,t){return bi(Dg(e,t))},Pg=Dg,Lg=0,Ig=2,Mg=1,Fg=function(e,t){var n=e.length+t.length+2,r=new Array(n),o=new Array(n),i=function(n,r,o,a,s){var c=u(n,r,o,a);if(null===c||c.start===r&&c.diag===r-a||c.end===n&&c.diag===n-o)for(var l=n,f=o;l<r||f<a;)l<r&&f<a&&e[l]===t[f]?(s.push([0,e[l]]),++l,++f):r-n>a-o?(s.push([2,e[l]]),++l):(s.push([1,t[f]]),++f);else{i(n,c.start,o,c.start-c.diag,s);for(var d=c.start;d<c.end;++d)s.push([0,e[d]]);i(c.end,r,c.end-c.diag,a,s)}},a=function(n,r,o,i){for(var a=n;a-r<i&&a<o&&e[a]===t[a-r];)++a;return{start:n,end:a,diag:r}},u=function(n,i,u,s){var c=i-n,l=s-u;if(0===c||0===l)return null;var f,d,m,p,g,h=c-l,v=l+c,y=(v%2==0?v:v+1)/2;for(r[1+y]=n,o[1+y]=i+1,f=0;f<=y;++f){for(d=-f;d<=f;d+=2){for(m=d+y,d===-f||d!==f&&r[m-1]<r[m+1]?r[m]=r[m+1]:r[m]=r[m-1]+1,g=(p=r[m])-n+u-d;p<i&&g<s&&e[p]===t[g];)r[m]=++p,++g;if(h%2!=0&&h-f<=d&&d<=h+f&&o[m-h]<=r[m])return a(o[m-h],d+n-u,i,s)}for(d=h-f;d<=h+f;d+=2){for(m=d+y-h,d===h-f||d!==h+f&&o[m+1]<=o[m-1]?o[m]=o[m+1]-1:o[m]=o[m-1],g=(p=o[m]-1)-n+u-d;p>=n&&g>=u&&e[p]===t[g];)o[m]=p--,g--;if(h%2==0&&-f<=d&&d<=f&&o[m]<=r[m+h])return a(o[m],d+n-u,i,s)}}},s=[];return i(0,e.length,0,t.length,s),s},zg=function(e){return 1===e.nodeType?e.outerHTML:3===e.nodeType?zo.encodeRaw(e.data,!1):8===e.nodeType?"\x3c!--"+e.data+"--\x3e":""},Ug=function(e,t,n){var r=function(e){var t,n,r;for(r=document.createElement("div"),t=document.createDocumentFragment(),e&&(r.innerHTML=e);n=r.firstChild;)t.appendChild(n);return t}(t);if(e.hasChildNodes()&&n<e.childNodes.length){var o=e.childNodes[n];o.parentNode.insertBefore(r,o)}else e.appendChild(r)},qg=function(e){return Tt.filter(Tt.map(e.childNodes,zg),function(e){return e.length>0})},Vg=function(e,t){var n,r,o,i=Tt.map(t.childNodes,zg);return n=Fg(i,e),r=t,o=0,Tt.each(n,function(e){e[0]===Lg?o++:e[0]===Mg?(Ug(r,e[1],o),o++):e[0]===Ig&&function(e,t){if(e.hasChildNodes()&&t<e.childNodes.length){var n=e.childNodes[t];n.parentNode.removeChild(n)}}(r,o)}),t},Hg=function(e){return{type:"fragmented",fragments:e,content:"",bookmark:null,beforeBookmark:null}},jg=function(e){return{type:"complete",fragments:null,content:e,bookmark:null,beforeBookmark:null}},$g=function(e){return"fragmented"===e.type?e.fragments.join(""):e.content},Wg={createFragmentedLevel:Hg,createCompleteLevel:jg,createFromEditor:function(e){var t,n,r;return t=qg(e.getBody()),-1!==(n=(r=M.bind(t,function(t){var n=Pg(e.serializer,t);return n.length>0?[n]:[]})).join("")).indexOf("</iframe>")?Hg(r):jg(n)},applyToEditor:function(e,t,n){"fragmented"===t.type?Vg(t.fragments,e.getBody()):e.setContent(t.content,{format:"raw"}),e.selection.moveToBookmark(n?t.beforeBookmark:t.bookmark)},isEq:function(e,t){return!!e&&!!t&&$g(e)===$g(t)}};function Kg(e){var t,n,r=this,o=0,i=[],a=0,u=function(){return 0===a},s=function(e){u()&&(r.typing=e)},c=function(t){e.setDirty(t)},l=function(e){s(!1),r.add({},e)},f=function(){r.typing&&(s(!1),r.add())};return e.on("init",function(){r.add()}),e.on("BeforeExecCommand",function(e){var t=e.command;"Undo"!==t&&"Redo"!==t&&"mceRepaint"!==t&&(f(),r.beforeChange())}),e.on("ExecCommand",function(e){var t=e.command;"Undo"!==t&&"Redo"!==t&&"mceRepaint"!==t&&l(e)}),e.on("ObjectResizeStart Cut",function(){r.beforeChange()}),e.on("SaveContent ObjectResized blur",l),e.on("DragEnd",l),e.on("KeyUp",function(t){var o=t.keyCode;t.isDefaultPrevented()||((o>=33&&o<=36||o>=37&&o<=40||45===o||t.ctrlKey)&&(l(),e.nodeChanged()),46!==o&&8!==o||e.nodeChanged(),n&&r.typing&&!1===Wg.isEq(Wg.createFromEditor(e),i[0])&&(!1===e.isDirty()&&(c(!0),e.fire("change",{level:i[0],lastLevel:null})),e.fire("TypingUndo"),n=!1,e.nodeChanged()))}),e.on("KeyDown",function(e){var t=e.keyCode;if(!e.isDefaultPrevented())if(t>=33&&t<=36||t>=37&&t<=40||45===t)r.typing&&l(e);else{var o=e.ctrlKey&&!e.altKey||e.metaKey;!(t<16||t>20)||224===t||91===t||r.typing||o||(r.beforeChange(),s(!0),r.add({},e),n=!0)}}),e.on("MouseDown",function(e){r.typing&&l(e)}),e.on("input",function(e){var t;e.inputType&&("insertReplacementText"===e.inputType||"insertText"===(t=e).inputType&&null===t.data)&&l(e)}),e.addShortcut("meta+z","","Undo"),e.addShortcut("meta+y,meta+shift+z","","Redo"),e.on("AddUndo Undo Redo ClearUndos",function(t){t.isDefaultPrevented()||e.nodeChanged()}),r={data:i,typing:!1,beforeChange:function(){u()&&(t=Ms.getUndoBookmark(e.selection))},add:function(n,r){var a,s,l,f=e.settings;if(l=Wg.createFromEditor(e),n=n||{},n=Dt.extend(n,l),!1===u()||e.removed)return null;if(s=i[o],e.fire("BeforeAddUndo",{level:n,lastLevel:s,originalEvent:r}).isDefaultPrevented())return null;if(s&&Wg.isEq(s,n))return null;if(i[o]&&(i[o].beforeBookmark=t),f.custom_undo_redo_levels&&i.length>f.custom_undo_redo_levels){for(a=0;a<i.length-1;a++)i[a]=i[a+1];i.length--,o=i.length}n.bookmark=Ms.getUndoBookmark(e.selection),o<i.length-1&&(i.length=o+1),i.push(n),o=i.length-1;var d={level:n,lastLevel:s,originalEvent:r};return e.fire("AddUndo",d),o>0&&(c(!0),e.fire("change",d)),n},undo:function(){var t;return r.typing&&(r.add(),r.typing=!1,s(!1)),o>0&&(t=i[--o],Wg.applyToEditor(e,t,!0),c(!0),e.fire("undo",{level:t})),t},redo:function(){var t;return o<i.length-1&&(t=i[++o],Wg.applyToEditor(e,t,!1),c(!0),e.fire("redo",{level:t})),t},clear:function(){i=[],o=0,r.typing=!1,r.data=i,e.fire("ClearUndos")},hasUndo:function(){return o>0||r.typing&&i[0]&&!Wg.isEq(Wg.createFromEditor(e),i[0])},hasRedo:function(){return o<i.length-1&&!r.typing},transact:function(e){return f(),r.beforeChange(),r.ignore(e),r.add()},ignore:function(e){try{a++,e()}finally{a--}},extra:function(t,n){var a,u;r.transact(t)&&(u=i[o].bookmark,a=i[o-1],Wg.applyToEditor(e,a,!0),r.transact(n)&&(i[o-1].beforeBookmark=u))}}}var Xg,Yg,Gg={},Jg=Tt.filter,Qg=Tt.each;Yg=function(e){var t,n,r=e.selection.getRng();t=So.matchNodeNames("pre"),r.collapsed||(n=e.selection.getSelectedBlocks(),Qg(Jg(Jg(n,t),function(e){return t(e.previousSibling)&&-1!==Tt.indexOf(n,e.previousSibling)}),function(e){var t,n;t=e.previousSibling,Jt(n=e).remove(),Jt(t).append("<br><br>").append(n.childNodes)}))},Gg[Xg="pre"]||(Gg[Xg]=[]),Gg[Xg].push(Yg);var Zg=function(e,t){Qg(Gg[e],function(e){e(t)})},eh=Dt.each,th={walk:function(e,t,n){var r,o,i,a,u,s,c,l=t.startContainer,f=t.startOffset,d=t.endContainer,m=t.endOffset;if((c=e.select("td[data-mce-selected],th[data-mce-selected]")).length>0)eh(c,function(e){n([e])});else{var p,g,h,v=function(e){var t;return 3===(t=e[0]).nodeType&&t===l&&f>=t.nodeValue.length&&e.splice(0,1),t=e[e.length-1],0===m&&e.length>0&&t===d&&3===t.nodeType&&e.splice(e.length-1,1),e},y=function(e,t,n){for(var r=[];e&&e!==n;e=e[t])r.push(e);return r},b=function(e,t){do{if(e.parentNode===t)return e;e=e.parentNode}while(e)},C=function(e,t,r){var o=r?"nextSibling":"previousSibling";for(u=(a=e).parentNode;a&&a!==t;a=u)u=a.parentNode,(s=y(a===e?a:a[o],o)).length&&(r||s.reverse(),n(v(s)))};if(1===l.nodeType&&l.hasChildNodes()&&(l=l.childNodes[f]),1===d.nodeType&&d.hasChildNodes()&&(g=m,h=(p=d).childNodes,--g>h.length-1?g=h.length-1:g<0&&(g=0),d=h[g]||p),l===d)return n(v([l]));for(r=e.findCommonAncestor(l,d),a=l;a;a=a.parentNode){if(a===d)return C(l,r,!0);if(a===r)break}for(a=d;a;a=a.parentNode){if(a===l)return C(d,r);if(a===r)break}o=b(l,r)||l,i=b(d,r)||d,C(l,o,!0),(s=y(o===l?o:o.nextSibling,"nextSibling",i===d?i.nextSibling:i)).length&&n(v(s)),C(d,i)}}},nh=/^(src|href|style)$/,rh=Dt.each,oh=Vl.isEq,ih=function(e){return/^(TH|TD)$/.test(e.nodeName)},ah=function(e,t,n){var r,o,i;return r=t[n?"startContainer":"endContainer"],o=t[n?"startOffset":"endOffset"],So.isElement(r)&&(i=r.childNodes.length-1,!n&&o&&o--,r=r.childNodes[o>i?i:o]),So.isText(r)&&n&&o>=r.nodeValue.length&&(r=new Zr(r,e.getBody()).next()||r),So.isText(r)&&!n&&0===o&&(r=new Zr(r,e.getBody()).prev()||r),r},uh=function(e,t,n,r){var o=e.create(n,r);return t.parentNode.insertBefore(o,t),o.appendChild(t),o},sh=function(e,t,n,r){return!(t=Vl.getNonWhiteSpaceSibling(t,n,r))||"BR"===t.nodeName||e.isBlock(t)},ch=function(e,t,n,r,o){var i,a,u,s,c,l,f,d,m,p,g,h,v,y,b=e.dom;if(c=b,!(oh(l=r,(f=t).inline)||oh(l,f.block)||(f.selector?So.isElement(l)&&c.is(l,f.selector):void 0)||(s=r,t.links&&"A"===s.tagName)))return!1;if("all"!==t.remove)for(rh(t.styles,function(e,i){e=Vl.normalizeStyleValue(b,Vl.replaceVars(e,n),i),"number"==typeof i&&(i=e,o=0),(t.remove_similar||!o||oh(Vl.getStyle(b,o,i),e))&&b.setStyle(r,i,""),u=1}),u&&""===b.getAttrib(r,"style")&&(r.removeAttribute("style"),r.removeAttribute("data-mce-style")),rh(t.attributes,function(e,t){var i;if(e=Vl.replaceVars(e,n),"number"==typeof t&&(t=e,o=0),!o||oh(b.getAttrib(o,t),e)){if("class"===t&&(e=b.getAttrib(r,t))&&(i="",rh(e.split(/\s+/),function(e){/mce\-\w+/.test(e)&&(i+=(i?" ":"")+e)}),i))return void b.setAttrib(r,t,i);"class"===t&&r.removeAttribute("className"),nh.test(t)&&r.removeAttribute("data-mce-"+t),r.removeAttribute(t)}}),rh(t.classes,function(e){e=Vl.replaceVars(e,n),o&&!b.hasClass(o,e)||b.removeClass(r,e)}),a=b.getAttribs(r),i=0;i<a.length;i++){var C=a[i].nodeName;if(0!==C.indexOf("_")&&0!==C.indexOf("data-"))return!1}return"none"!==t.remove?(d=e,p=t,h=(m=r).parentNode,v=d.dom,y=d.settings.forced_root_block,p.block&&(y?h===v.getRoot()&&(p.list_block&&oh(m,p.list_block)||rh(Dt.grep(m.childNodes),function(e){Vl.isValid(d,y,e.nodeName.toLowerCase())?g?g.appendChild(e):(g=uh(v,e,y),v.setAttribs(g,d.settings.forced_root_block_attrs)):g=0})):v.isBlock(m)&&!v.isBlock(h)&&(sh(v,m,!1)||sh(v,m.firstChild,!0,1)||m.insertBefore(v.create("br"),m.firstChild),sh(v,m,!0)||sh(v,m.lastChild,!1,1)||m.appendChild(v.create("br")))),p.selector&&p.inline&&!oh(p.inline,m)||v.remove(m,1),!0):void 0},lh={removeFormat:ch,remove:function(e,t,n,r,o){var i,a,u=e.formatter.get(t),s=u[0],c=!0,l=e.dom,f=e.selection,d=function(r){var i,a,c,l,f,d,m=(i=e,a=r,c=t,l=n,f=o,rh(Vl.getParents(i.dom,a.parentNode).reverse(),function(e){var t;d||"_start"===e.id||"_end"===e.id||(t=sf.matchNode(i,e,c,l,f))&&!1!==t.split&&(d=e)}),d);return function(e,t,n,r,o,i,a,u){var s,c,l,f,d,m,p=e.dom;if(n){for(m=n.parentNode,s=r.parentNode;s&&s!==m;s=s.parentNode){for(c=p.clone(s,!1),d=0;d<t.length;d++)if(ch(e,t[d],u,c,c)){c=0;break}c&&(l&&c.appendChild(l),f||(f=c),l=c)}!i||a.mixed&&p.isBlock(n)||(r=p.split(n,r)),l&&(o.parentNode.insertBefore(l,o),f.appendChild(o))}return r}(e,u,m,r,r,!0,s,n)},m=function(t){var r,o,i,a,f;if(So.isElement(t)&&l.getContentEditable(t)&&(a=c,c="true"===l.getContentEditable(t),f=!0),r=Dt.grep(t.childNodes),c&&!f)for(o=0,i=u.length;o<i&&!ch(e,u[o],n,t,t);o++);if(s.deep&&r.length){for(o=0,i=r.length;o<i;o++)m(r[o]);f&&(c=a)}},p=function(e){var t=l.get(e?"_start":"_end"),n=t[e?"firstChild":"lastChild"];return Vs.isBookmarkNode(n)&&(n=n[e?"firstChild":"lastChild"]),So.isText(n)&&0===n.data.length&&(n=e?t.previousSibling||t.nextSibling:t.nextSibling||t.previousSibling),l.remove(t,!0),n},g=function(t){var n,r,o=t.commonAncestorContainer;if(t=ef(e,t,u,!0),s.split){if((n=ah(e,t,!0))!==(r=ah(e,t))){if(/^(TR|TH|TD)$/.test(n.nodeName)&&n.firstChild&&(n="TR"===n.nodeName?n.firstChild.firstChild||n:n.firstChild||n),o&&/^T(HEAD|BODY|FOOT|R)$/.test(o.nodeName)&&ih(r)&&r.firstChild&&(r=r.firstChild||r),l.isChildOf(n,r)&&n!==r&&!l.isBlock(r)&&!ih(n)&&!ih(r))return n=uh(l,n,"span",{id:"_start","data-mce-type":"bookmark"}),d(n),void(n=p(!0));n=uh(l,n,"span",{id:"_start","data-mce-type":"bookmark"}),r=uh(l,r,"span",{id:"_end","data-mce-type":"bookmark"}),d(n),d(r),n=p(!0),r=p()}else n=r=d(n);t.startContainer=n.parentNode?n.parentNode:n,t.startOffset=l.nodeIndex(n),t.endContainer=r.parentNode?r.parentNode:r,t.endOffset=l.nodeIndex(r)+1}th.walk(l,t,function(t){rh(t,function(t){m(t),So.isElement(t)&&"underline"===e.dom.getStyle(t,"text-decoration")&&t.parentNode&&"underline"===Vl.getTextDecoration(l,t.parentNode)&&ch(e,{deep:!1,exact:!0,inline:"span",styles:{textDecoration:"underline"}},null,t)})})};if(r)r.nodeType?((a=l.createRng()).setStartBefore(r),a.setEndAfter(r),g(a)):g(r);else if("false"!==l.getContentEditable(f.getNode()))f.isCollapsed()&&s.inline&&!l.select("td[data-mce-selected],th[data-mce-selected]").length?Nf.removeCaretFormat(e,t,n,o):(i=f.getBookmark(),g(f.getRng()),f.moveToBookmark(i),s.inline&&sf.match(e,t,n,f.getStart())&&Vl.moveStart(l,f,f.getRng()),e.nodeChanged());else{r=f.getNode();for(var h=0,v=u.length;h<v&&(!u[h].ceFalseOverride||!ch(e,u[h],n,r,r));h++);}}},fh=Dt.each,dh=function(e){return e&&1===e.nodeType&&!Vs.isBookmarkNode(e)&&!Nf.isCaretNode(e)&&!So.isBogus(e)},mh=function(e,t){var n;for(n=e;n;n=n[t]){if(3===n.nodeType&&0!==n.nodeValue.length)return e;if(1===n.nodeType&&!Vs.isBookmarkNode(n))return n}return e},ph=function(e,t,n){var r,o,i=new js(e);if(t&&n&&(t=mh(t,"previousSibling"),n=mh(n,"nextSibling"),i.compare(t,n))){for(r=t.nextSibling;r&&r!==n;)o=r,r=r.nextSibling,t.appendChild(o);return e.remove(n),Dt.each(Dt.grep(n.childNodes),function(e){t.appendChild(e)}),t}return n},gh=function(e,t,n){fh(e.childNodes,function(e){dh(e)&&(t(e)&&n(e),e.hasChildNodes()&&gh(e,t,n))})},hh=function(e,t){return y.curry(function(t,n){return!(!n||!Vl.getStyle(e,n,t))},t)},vh=function(e,t,n){return y.curry(function(t,n,r){e.setStyle(r,t,n),""===r.getAttribute("style")&&r.removeAttribute("style"),yh(e,r)},t,n)},yh=function(e,t){"SPAN"===t.nodeName&&0===e.getAttribs(t).length&&e.remove(t,!0)},bh=function(e,t){var n;1===t.nodeType&&t.parentNode&&1===t.parentNode.nodeType&&(n=Vl.getTextDecoration(e,t.parentNode),e.getStyle(t,"color")&&n?e.setStyle(t,"text-decoration",n):e.getStyle(t,"text-decoration")===n&&e.setStyle(t,"text-decoration",null))},Ch=function(e,t,n,r){fh(t,function(t){fh(e.dom.select(t.inline,r),function(r){dh(r)&&lh.removeFormat(e,t,n,r,t.exact?r:null)}),function(e,t,n){if(t.clear_child_styles){var r=t.links?"*:not(a)":"*";fh(e.select(r,n),function(n){dh(n)&&fh(t.styles,function(t,r){e.setStyle(n,r,"")})})}}(e.dom,t,r)})},xh=function(e,t,n,r){(t.styles.color||t.styles.textDecoration)&&(Dt.walk(r,y.curry(bh,e),"childNodes"),bh(e,r))},wh=function(e,t,n,r){t.styles&&t.styles.backgroundColor&&gh(r,hh(e,"fontSize"),vh(e,"backgroundColor",Vl.replaceVars(t.styles.backgroundColor,n)))},Nh=function(e,t,n,r){"sub"!==t.inline&&"sup"!==t.inline||(gh(r,hh(e,"fontSize"),vh(e,"fontSize","")),e.remove(e.select("sup"===t.inline?"sub":"sup",r),!0))},Eh=function(e,t,n,r){r&&!1!==t.merge_siblings&&(r=ph(e,Vl.getNonWhiteSpaceSibling(r),r),r=ph(e,r,Vl.getNonWhiteSpaceSibling(r,!0)))},Sh=function(e,t,n,r,o){sf.matchNode(e,o.parentNode,n,r)&&lh.removeFormat(e,t,r,o)||t.merge_with_parents&&e.dom.getParent(o.parentNode,function(i){if(sf.matchNode(e,i,n,r))return lh.removeFormat(e,t,r,o),!0})},kh=Dt.each,Th=function(e,t,n,r){var o,i,a=e.formatter.get(t),u=a[0],s=!r&&e.selection.isCollapsed(),c=e.dom,l=e.selection,f=function(e,t){if(t=t||u,e){if(t.onformat&&t.onformat(e,t,n,r),kh(t.styles,function(t,r){c.setStyle(e,r,Vl.replaceVars(t,n))}),t.styles){var o=c.getAttrib(e,"style");o&&e.setAttribute("data-mce-style",o)}kh(t.attributes,function(t,r){c.setAttrib(e,r,Vl.replaceVars(t,n))}),kh(t.classes,function(t){t=Vl.replaceVars(t,n),c.hasClass(e,t)||c.addClass(e,t)})}},d=function(e,t){var n=!1;return!!u.selector&&(kh(e,function(e){if(!("collapsed"in e&&e.collapsed!==s))return c.is(t,e.selector)&&!Nf.isCaretNode(t)?(f(t,e),n=!0,!1):void 0}),n)},m=function(r,o,i,s){var c,l,m=[],p=!0;c=u.inline||u.block,l=r.create(c),f(l),th.walk(r,o,function(o){var i,g=function(o){var h,v,y,b;if(b=p,h=o.nodeName.toLowerCase(),v=o.parentNode.nodeName.toLowerCase(),1===o.nodeType&&r.getContentEditable(o)&&(b=p,p="true"===r.getContentEditable(o),y=!0),Vl.isEq(h,"br"))return i=0,void(u.block&&r.remove(o));if(u.wrapper&&sf.matchNode(e,o,t,n))i=0;else{if(p&&!y&&u.block&&!u.wrapper&&Vl.isTextBlock(e,h)&&Vl.isValid(e,v,c))return o=r.rename(o,c),f(o),m.push(o),void(i=0);if(u.selector){var C=d(a,o);if(!u.inline||C)return void(i=0)}!p||y||!Vl.isValid(e,c,h)||!Vl.isValid(e,v,c)||!s&&3===o.nodeType&&1===o.nodeValue.length&&65279===o.nodeValue.charCodeAt(0)||Nf.isCaretNode(o)||u.inline&&r.isBlock(o)?(i=0,kh(Dt.grep(o.childNodes),g),y&&(p=b),i=0):(i||(i=r.clone(l,!1),o.parentNode.insertBefore(i,o),m.push(i)),i.appendChild(o))}};kh(o,g)}),!0===u.links&&kh(m,function(e){var t=function(e){"A"===e.nodeName&&f(e,u),kh(Dt.grep(e.childNodes),t)};t(e)}),kh(m,function(o){var i,s,c,l,d,p=function(e){var t=!1;return kh(e.childNodes,function(e){if((n=e)&&1===n.nodeType&&!Vs.isBookmarkNode(n)&&!Nf.isCaretNode(n)&&!So.isBogus(n))return t=e,!1;var n}),t};s=0,kh(o.childNodes,function(e){Vl.isWhiteSpaceNode(e)||Vs.isBookmarkNode(e)||s++}),i=s,!(m.length>1)&&r.isBlock(o)||0!==i?(u.inline||u.wrapper)&&(u.exact||1!==i||((l=p(c=o))&&!Vs.isBookmarkNode(l)&&sf.matchName(r,l,u)&&(d=r.clone(l,!1),f(d),r.replace(d,c,!0),r.remove(l,1)),o=d||c),Ch(e,a,n,o),Sh(e,u,t,n,o),wh(r,u,n,o),Nh(r,u,n,o),Eh(r,u,n,o)):r.remove(o,1)})};if("false"!==c.getContentEditable(l.getNode())){if(u){if(r)r.nodeType?d(a,r)||((i=c.createRng()).setStartBefore(r),i.setEndAfter(r),m(c,ef(e,i,a),0,!0)):m(c,r,0,!0);else if(s&&u.inline&&!c.select("td[data-mce-selected],th[data-mce-selected]").length)Nf.applyCaretFormat(e,t,n);else{var p=e.selection.getNode();e.settings.forced_root_block||!a[0].defaultBlock||c.getParent(p,c.isBlock)||Th(e,a[0].defaultBlock),e.selection.setRng(uc(e.selection.getRng())),o=l.getBookmark(),m(c,ef(e,l.getRng(),a)),u.styles&&xh(c,u,n,p),l.moveToBookmark(o),Vl.moveStart(c,l,l.getRng()),e.nodeChanged()}Zg(t,e)}}else{r=l.getNode();for(var g=0,h=a.length;g<h;g++)if(a[g].ceFalseOverride&&c.is(r,a[g].selector))return void f(r,a[g])}},Ah={applyFormat:Th},_h=Dt.each,Rh={formatChanged:function(e,t,n,r,o){var i,a,u,s,c,l,f,d;null===t.get()&&(a=e,u={},(i=t).set({}),a.on("NodeChange",function(e){var t=Vl.getParents(a.dom,e.element),n={};t=Dt.grep(t,function(e){return 1===e.nodeType&&!e.getAttribute("data-mce-bogus")}),_h(i.get(),function(e,r){_h(t,function(o){return a.formatter.matchNode(o,r,{},e.similar)?(u[r]||(_h(e,function(e){e(!0,{node:o,format:r,parents:t})}),u[r]=e),n[r]=e,!1):!sf.matchesUnInheritedFormatSelector(a,o,r)&&void 0})}),_h(u,function(r,o){n[o]||(delete u[o],_h(r,function(n){n(!1,{node:e.element,format:o,parents:t})}))})})),c=n,l=r,f=o,d=(s=t).get(),_h(c.split(","),function(e){d[e]||(d[e]=[],d[e].similar=f),d[e].push(l)}),s.set(d)}},Bh={get:function(e){var t={valigntop:[{selector:"td,th",styles:{verticalAlign:"top"}}],valignmiddle:[{selector:"td,th",styles:{verticalAlign:"middle"}}],valignbottom:[{selector:"td,th",styles:{verticalAlign:"bottom"}}],alignleft:[{selector:"figure.image",collapsed:!1,classes:"align-left",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"left"},inherit:!1,preview:!1,defaultBlock:"div"},{selector:"img,table",collapsed:!1,styles:{"float":"left"},preview:"font-family font-size"}],aligncenter:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"center"},inherit:!1,preview:"font-family font-size",defaultBlock:"div"},{selector:"figure.image",collapsed:!1,classes:"align-center",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"img",collapsed:!1,styles:{display:"block",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"table",collapsed:!1,styles:{marginLeft:"auto",marginRight:"auto"},preview:"font-family font-size"}],alignright:[{selector:"figure.image",collapsed:!1,classes:"align-right",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"right"},inherit:!1,preview:"font-family font-size",defaultBlock:"div"},{selector:"img,table",collapsed:!1,styles:{"float":"right"},preview:"font-family font-size"}],alignjustify:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"justify"},inherit:!1,defaultBlock:"div",preview:"font-family font-size"}],bold:[{inline:"strong",remove:"all"},{inline:"span",styles:{fontWeight:"bold"}},{inline:"b",remove:"all"}],italic:[{inline:"em",remove:"all"},{inline:"span",styles:{fontStyle:"italic"}},{inline:"i",remove:"all"}],underline:[{inline:"span",styles:{textDecoration:"underline"},exact:!0},{inline:"u",remove:"all"}],strikethrough:[{inline:"span",styles:{textDecoration:"line-through"},exact:!0},{inline:"strike",remove:"all"}],forecolor:{inline:"span",styles:{color:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},hilitecolor:{inline:"span",styles:{backgroundColor:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},fontname:{inline:"span",toggle:!1,styles:{fontFamily:"%value"},clear_child_styles:!0},fontsize:{inline:"span",toggle:!1,styles:{fontSize:"%value"},clear_child_styles:!0},fontsize_class:{inline:"span",attributes:{"class":"%value"}},blockquote:{block:"blockquote",wrapper:1,remove:"all"},subscript:{inline:"sub"},superscript:{inline:"sup"},code:{inline:"code"},link:{inline:"a",selector:"a",remove:"all",split:!0,deep:!0,onmatch:function(){return!0},onformat:function(t,n,r){Dt.each(r,function(n,r){e.setAttrib(t,r,n)})}},removeformat:[{selector:"b,strong,em,i,font,u,strike,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins",remove:"all",split:!0,expand:!1,block_expand:!0,deep:!0},{selector:"span",attributes:["style","class"],remove:"empty",split:!0,expand:!1,deep:!0},{selector:"*",attributes:["style","class"],split:!1,expand:!1,deep:!0}]};return Dt.each("p h1 h2 h3 h4 h5 h6 div address pre div dt dd samp".split(/\s/),function(e){t[e]={block:e,remove:"all"}}),t}},Dh=Dt.each,Oh=ui.DOM,Ph=function(e,t){var n,r,o,i=t&&t.schema||Go({}),a=function(e){var t,n,o;return r="string"==typeof e?{name:e,classes:[],attrs:{}}:e,t=Oh.create(r.name),n=t,(o=r).classes.length&&Oh.addClass(n,o.classes.join(" ")),Oh.setAttribs(n,o.attrs),t},u=function(e,t,n){var r,o,s,c,l,f,d,m,p=t.length>0&&t[0],g=p&&p.name;if(l=g,f="string"!=typeof(c=e)?c.nodeName.toLowerCase():c,d=i.getElementRule(f),s=!(!(m=d&&d.parentsRequired)||!m.length)&&(l&&-1!==Dt.inArray(m,l)?l:m[0]))g===s?(o=t[0],t=t.slice(1)):o=s;else if(p)o=t[0],t=t.slice(1);else if(!n)return e;return o&&(r=a(o)).appendChild(e),n&&(r||(r=Oh.create("div")).appendChild(e),Dt.each(n,function(t){var n=a(t);r.insertBefore(n,e)})),u(r,t,o&&o.siblings)};return e&&e.length?(r=e[0],n=a(r),(o=Oh.create("div")).appendChild(u(n,e.slice(1),r.siblings)),o):""},Lh=function(e){var t,n={classes:[],attrs:{}};return"*"!==(e=n.selector=Dt.trim(e))&&(t=e.replace(/(?:([#\.]|::?)([\w\-]+)|(\[)([^\]]+)\]?)/g,function(e,t,r,o,i){switch(t){case"#":n.attrs.id=r;break;case".":n.classes.push(r);break;case":":-1!==Dt.inArray("checked disabled enabled read-only required".split(" "),r)&&(n.attrs[r]=r)}if("["===o){var a=i.match(/([\w\-]+)(?:\=\"([^\"]+))?/);a&&(n.attrs[a[1]]=a[2])}return""})),n.name=t||"div",n},Ih=function(e){return e&&"string"==typeof e?(e=(e=e.split(/\s*,\s*/)[0]).replace(/\s*(~\+|~|\+|>)\s*/g,"$1"),Dt.map(e.split(/(?:>|\s+(?![^\[\]]+\]))/),function(e){var t=Dt.map(e.split(/(?:~\+|~|\+)/),Lh),n=t.pop();return t.length&&(n.siblings=t),n}).reverse()):[]},Mh={getCssText:function(e,t){var n,r,o,i,a,u,s="";if(!1===(u=e.settings.preview_styles))return"";"string"!=typeof u&&(u="font-family font-size font-weight font-style text-decoration text-transform color background-color border border-radius outline text-shadow");var c=function(e){return e.replace(/%(\w+)/g,"")};if("string"==typeof t){if(!(t=e.formatter.get(t)))return;t=t[0]}return"preview"in t&&!1===(u=t.preview)?"":(n=t.block||t.inline||"span",(i=Ih(t.selector)).length?(i[0].name||(i[0].name=n),n=t.selector,r=Ph(i,e)):r=Ph([n],e),o=Oh.select(n,r)[0]||r.firstChild,Dh(t.styles,function(e,t){(e=c(e))&&Oh.setStyle(o,t,e)}),Dh(t.attributes,function(e,t){(e=c(e))&&Oh.setAttrib(o,t,e)}),Dh(t.classes,function(e){e=c(e),Oh.hasClass(o,e)||Oh.addClass(o,e)}),e.fire("PreviewFormats"),Oh.setStyles(r,{position:"absolute",left:-65535}),e.getBody().appendChild(r),a=Oh.getStyle(e.getBody(),"fontSize",!0),a=/px$/.test(a)?parseInt(a,10):0,Dh(u.split(" "),function(t){var n=Oh.getStyle(o,t,!0);if(!("background-color"===t&&/transparent|rgba\s*\([^)]+,\s*0\)/.test(n)&&(n=Oh.getStyle(e.getBody(),t,!0),"#ffffff"===Oh.toHex(n).toLowerCase())||"color"===t&&"#000000"===Oh.toHex(n).toLowerCase())){if("font-size"===t&&/em|%$/.test(n)){if(0===a)return;n=(n=parseFloat(n)/(/%$/.test(n)?100:1))*a+"px"}"border"===t&&n&&(s+="padding:0 2px;"),s+=t+":"+n+";"}}),e.fire("AfterPreviewFormats"),Oh.remove(r),s)},parseSelector:Ih,selectorToHtml:function(e,t){return Ph(Ih(e),t)}},Fh={toggle:function(e,t,n,r,o){var i=t.get(n);!sf.match(e,n,r,o)||"toggle"in i[0]&&!i[0].toggle?Ah.applyFormat(e,n,r,o):lh.remove(e,n,r,o)}},zh={setup:function(e){e.addShortcut("meta+b","","Bold"),e.addShortcut("meta+i","","Italic"),e.addShortcut("meta+u","","Underline");for(var t=1;t<=6;t++)e.addShortcut("access+"+t,"",["FormatBlock",!1,"h"+t]);e.addShortcut("access+7","",["FormatBlock",!1,"p"]),e.addShortcut("access+8","",["FormatBlock",!1,"div"]),e.addShortcut("access+9","",["FormatBlock",!1,"address"])}};function Uh(e){var t,n,r,o=(t=e,n={},(r=function(e,t){e&&("string"!=typeof e?Dt.each(e,function(e,t){r(t,e)}):(t=t.length?t:[t],Dt.each(t,function(e){"undefined"==typeof e.deep&&(e.deep=!e.selector),"undefined"==typeof e.split&&(e.split=!e.selector||e.inline),"undefined"==typeof e.remove&&e.selector&&!e.inline&&(e.remove="none"),e.selector&&e.inline&&(e.mixed=!0,e.block_expand=!0),"string"==typeof e.classes&&(e.classes=e.classes.split(/\s+/))}),n[e]=t))})(Bh.get(t.dom)),r(t.settings.formats),{get:function(e){return e?n[e]:n},register:r,unregister:function(e){return e&&n[e]&&delete n[e],n}}),i=Wf(null);return zh.setup(e),Nf.setup(e),{get:o.get,register:o.register,unregister:o.unregister,apply:y.curry(Ah.applyFormat,e),remove:y.curry(lh.remove,e),toggle:y.curry(Fh.toggle,e,o),match:y.curry(sf.match,e),matchAll:y.curry(sf.matchAll,e),matchNode:y.curry(sf.matchNode,e),canApply:y.curry(sf.canApply,e),formatChanged:y.curry(Rh.formatChanged,e,i),getCssText:y.curry(Mh.getCssText,e)}}var qh=function(e){return function(){for(var t=new Array(arguments.length),n=0;n<t.length;n++)t[n]=arguments[n];if(0===t.length)throw new Error("Can't merge zero objects");for(var r={},o=0;o<t.length;o++){var i=t[o];for(var a in i)i.hasOwnProperty(a)&&(r[a]=e(r[a],i[a]))}return r}},Vh=qh(function(e,t){return Jn.isObject(e)&&Jn.isObject(t)?Vh(e,t):t}),Hh=qh(function(e,t){return t}),jh={deepMerge:Vh,merge:Hh},$h=function(e,t){return e.fire("PreProcess",t)},Wh=function(e,t){return e.fire("PostProcess",t)},Kh=function(e){return e.fire("remove")},Xh={register:function(e,t,n){e.addAttributeFilter("data-mce-tabindex",function(e,t){for(var n,r=e.length;r--;)(n=e[r]).attr("tabindex",n.attributes.map["data-mce-tabindex"]),n.attr(t,null)}),e.addAttributeFilter("src,href,style",function(e,r){for(var o,i,a=e.length,u="data-mce-"+r,s=t.url_converter,c=t.url_converter_scope;a--;)(i=(o=e[a]).attributes.map[u])!==undefined?(o.attr(r,i.length>0?i:null),o.attr(u,null)):(i=o.attributes.map[r],"style"===r?i=n.serializeStyle(n.parseStyle(i),o.name):s&&(i=s.call(c,i,r,o.name)),o.attr(r,i.length>0?i:null))}),e.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)(n=(t=e[r]).attr("class"))&&(n=t.attr("class").replace(/(?:^|\s)mce-item-\w+(?!\S)/g,""),t.attr("class",n.length>0?n:null))}),e.addAttributeFilter("data-mce-type",function(e,t,n){for(var r,o=e.length;o--;)"bookmark"!==(r=e[o]).attributes.map["data-mce-type"]||n.cleanup||r.remove()}),e.addNodeFilter("noscript",function(e){for(var t,n=e.length;n--;)(t=e[n].firstChild)&&(t.value=zo.decode(t.value))}),e.addNodeFilter("script,style",function(e,n){for(var r,o,i,a=e.length,u=function(e){return e.replace(/(<!--\[CDATA\[|\]\]-->)/g,"\n").replace(/^[\r\n]*|[\r\n]*$/g,"").replace(/^\s*((<!--)?(\s*\/\/)?\s*<!\[CDATA\[|(<!--\s*)?\/\*\s*<!\[CDATA\[\s*\*\/|(\/\/)?\s*<!--|\/\*\s*<!--\s*\*\/)\s*[\r\n]*/gi,"").replace(/\s*(\/\*\s*\]\]>\s*\*\/(-->)?|\s*\/\/\s*\]\]>(-->)?|\/\/\s*(-->)?|\]\]>|\/\*\s*-->\s*\*\/|\s*-->\s*)\s*$/g,"")};a--;)o=(r=e[a]).firstChild?r.firstChild.value:"","script"===n?((i=r.attr("type"))&&r.attr("type","mce-no/type"===i?null:i.replace(/^mce\-/,"")),"xhtml"===t.element_format&&o.length>0&&(r.firstChild.value="// <![CDATA[\n"+u(o)+"\n// ]]>")):"xhtml"===t.element_format&&o.length>0&&(r.firstChild.value="\x3c!--\n"+u(o)+"\n--\x3e")}),e.addNodeFilter("#comment",function(e){for(var t,n=e.length;n--;)0===(t=e[n]).value.indexOf("[CDATA[")?(t.name="#cdata",t.type=4,t.value=t.value.replace(/^\[CDATA\[|\]\]$/g,"")):0===t.value.indexOf("mce:protected ")&&(t.name="#text",t.type=3,t.raw=!0,t.value=unescape(t.value).substr(14))}),e.addNodeFilter("xml:namespace,input",function(e,t){for(var n,r=e.length;r--;)7===(n=e[r]).type?n.remove():1===n.type&&("input"!==t||"type"in n.attributes.map||n.attr("type","text"))}),e.addAttributeFilter("data-mce-type",function(t){M.each(t,function(t){"format-caret"===t.attr("data-mce-type")&&(t.isEmpty(e.schema.getNonEmptyElements())?t.remove():t.unwrap())})}),e.addAttributeFilter("data-mce-src,data-mce-href,data-mce-style,data-mce-selected,data-mce-expando,data-mce-type,data-mce-resize",function(e,t){for(var n=e.length;n--;)e[n].attr(t,null)})},trimTrailingBr:function(e){var t,n,r=function(e){return e&&"br"===e.name};r(t=e.lastChild)&&r(n=t.prev)&&(t.remove(),n.remove())}},Yh={process:function(e,t,n){return f=n,(l=e)&&l.hasEventListeners("PreProcess")&&!f.no_events?(o=t,i=n,c=(r=e).dom,o=o.cloneNode(!0),(a=document.implementation).createHTMLDocument&&(u=a.createHTMLDocument(""),Dt.each("BODY"===o.nodeName?o.childNodes:[o],function(e){u.body.appendChild(u.importNode(e,!0))}),o="BODY"!==o.nodeName?u.body.firstChild:u.body,s=c.doc,c.doc=u),$h(r,jh.merge(i,{node:o})),s&&(c.doc=s),o):t;var r,o,i,a,u,s,c,l,f}},Gh=function(e,t,n){e.addNodeFilter("font",function(e){M.each(e,function(e){var r,o,i=t.parse(e.attr("style")),a=e.attr("color"),u=e.attr("face"),s=e.attr("size");a&&(i.color=a),u&&(i["font-family"]=u),s&&(i["font-size"]=n[parseInt(e.attr("size"),10)-1]),e.name="span",e.attr("style",t.serialize(i)),r=e,o=["color","face","size"],M.each(o,function(e){r.attr(e,null)})})})},Jh=function(e,t){var n,r=Qo();t.convert_fonts_to_spans&&Gh(e,r,Dt.explode(t.font_size_legacy_values)),n=r,e.addNodeFilter("strike",function(e){M.each(e,function(e){var t=n.parse(e.attr("style"));t["text-decoration"]="line-through",e.name="span",e.attr("style",n.serialize(t))})})},Qh={register:function(e,t){t.inline_styles&&Jh(e,t)}},Zh=/^[ \t\r\n]*$/,ev={"#text":3,"#comment":8,"#cdata":4,"#pi":7,"#doctype":10,"#document-fragment":11},tv=function(e,t,n){var r,o,i=n?"lastChild":"firstChild",a=n?"prev":"next";if(e[i])return e[i];if(e!==t){if(r=e[a])return r;for(o=e.parent;o&&o!==t;o=o.parent)if(r=o[a])return r}},nv=function(){function e(e,t){this.name=e,this.type=t,1===t&&(this.attributes=[],this.attributes.map={})}return e.create=function(t,n){var r,o;if(r=new e(t,ev[t]||1),n)for(o in n)r.attr(o,n[o]);return r},e.prototype.replace=function(e){return e.parent&&e.remove(),this.insert(e,this),this.remove(),this},e.prototype.attr=function(e,t){var n,r;if("string"!=typeof e){for(r in e)this.attr(r,e[r]);return this}if(n=this.attributes){if(t!==undefined){if(null===t){if(e in n.map)for(delete n.map[e],r=n.length;r--;)if(n[r].name===e)return n=n.splice(r,1),this;return this}if(e in n.map){for(r=n.length;r--;)if(n[r].name===e){n[r].value=t;break}}else n.push({name:e,value:t});return n.map[e]=t,this}return n.map[e]}},e.prototype.clone=function(){var t,n,r,o,i,a=new e(this.name,this.type);if(r=this.attributes){for((i=[]).map={},t=0,n=r.length;t<n;t++)"id"!==(o=r[t]).name&&(i[i.length]={name:o.name,value:o.value},i.map[o.name]=o.value);a.attributes=i}return a.value=this.value,a.shortEnded=this.shortEnded,a},e.prototype.wrap=function(e){return this.parent.insert(e,this),e.append(this),this},e.prototype.unwrap=function(){var e,t;for(e=this.firstChild;e;)t=e.next,this.insert(e,this,!0),e=t;this.remove()},e.prototype.remove=function(){var e=this.parent,t=this.next,n=this.prev;return e&&(e.firstChild===this?(e.firstChild=t,t&&(t.prev=null)):n.next=t,e.lastChild===this?(e.lastChild=n,n&&(n.next=null)):t.prev=n,this.parent=this.next=this.prev=null),this},e.prototype.append=function(e){var t;return e.parent&&e.remove(),(t=this.lastChild)?(t.next=e,e.prev=t,this.lastChild=e):this.lastChild=this.firstChild=e,e.parent=this,e},e.prototype.insert=function(e,t,n){var r;return e.parent&&e.remove(),r=t.parent||this,n?(t===r.firstChild?r.firstChild=e:t.prev.next=e,e.prev=t.prev,e.next=t,t.prev=e):(t===r.lastChild?r.lastChild=e:t.next.prev=e,e.next=t.next,e.prev=t,t.next=e),e.parent=r,e},e.prototype.getAll=function(e){var t,n=[];for(t=this.firstChild;t;t=tv(t,this))t.name===e&&n.push(t);return n},e.prototype.empty=function(){var e,t,n;if(this.firstChild){for(e=[],n=this.firstChild;n;n=tv(n,this))e.push(n);for(t=e.length;t--;)(n=e[t]).parent=n.firstChild=n.lastChild=n.next=n.prev=null}return this.firstChild=this.lastChild=null,this},e.prototype.isEmpty=function(e,t,n){var r,o,i=this.firstChild;if(t=t||{},i)do{if(1===i.type){if(i.attributes.map["data-mce-bogus"])continue;if(e[i.name])return!1;for(r=i.attributes.length;r--;)if("name"===(o=i.attributes[r].name)||0===o.indexOf("data-mce-bookmark"))return!1}if(8===i.type)return!1;if(3===i.type&&!Zh.test(i.value))return!1;if(3===i.type&&i.parent&&t[i.parent.name]&&Zh.test(i.value))return!1;if(n&&n(i))return!1}while(i=tv(i,this));return!0},e.prototype.walk=function(e){return tv(this,null,e)},e}(),rv=function(e,t,n,r){(e.padd_empty_with_br||t.insert)&&n[r.name]?r.empty().append(new nv("br",1)).shortEnded=!0:r.empty().append(new nv("#text",3)).value="\xa0"},ov=function(e){return iv(e,"#text")&&"\xa0"===e.firstChild.value},iv=function(e,t){return e&&e.firstChild&&e.firstChild===e.lastChild&&e.firstChild.name===t},av=function(e,t,n,r){return r.isEmpty(t,n,function(t){return n=t,(r=e.getElementRule(n.name))&&r.paddEmpty;var n,r})},uv=function(e,t){return e&&(t[e.name]||"br"===e.name)},sv=function(e,t){var n=e.schema;t.remove_trailing_brs&&e.addNodeFilter("br",function(e,r,o){var i,a,u,s,c,l,f,d,m=e.length,p=Dt.extend({},n.getBlockElements()),g=n.getNonEmptyElements(),h=n.getNonEmptyElements();for(p.body=1,i=0;i<m;i++)if(u=(a=e[i]).parent,p[a.parent.name]&&a===u.lastChild){for(c=a.prev;c;){if("span"!==(l=c.name)||"bookmark"!==c.attr("data-mce-type")){if("br"!==l)break;if("br"===l){a=null;break}}c=c.prev}a&&(a.remove(),av(n,g,h,u)&&(f=n.getElementRule(u.name))&&(f.removeEmpty?u.remove():f.paddEmpty&&rv(t,o,p,u)))}else{for(s=a;u&&u.firstChild===s&&u.lastChild===s&&(s=u,!p[u.name]);)u=u.parent;s===u&&!0!==t.padd_empty_with_br&&((d=new nv("#text",3)).value="\xa0",a.replace(d))}}),e.addAttributeFilter("href",function(e){var n,r,o,i=e.length;if(!t.allow_unsafe_link_target)for(;i--;)"a"===(n=e[i]).name&&"_blank"===n.attr("target")&&n.attr("rel",(r=n.attr("rel"),o=r?Dt.trim(r):"",/\b(noopener)\b/g.test(o)?o:o.split(" ").filter(function(e){return e.length>0}).concat(["noopener"]).sort().join(" ")))}),t.allow_html_in_named_anchor||e.addAttributeFilter("id,name",function(e){for(var t,n,r,o,i=e.length;i--;)if("a"===(o=e[i]).name&&o.firstChild&&!o.attr("href"))for(r=o.parent,t=o.lastChild;n=t.prev,r.insert(t,o),t=n;);}),t.fix_list_elements&&e.addNodeFilter("ul,ol",function(e){for(var t,n,r=e.length;r--;)if("ul"===(n=(t=e[r]).parent).name||"ol"===n.name)if(t.prev&&"li"===t.prev.name)t.prev.append(t);else{var o=new nv("li",1);o.attr("style","list-style-type: none"),t.wrap(o)}}),t.validate&&n.getValidClasses()&&e.addAttributeFilter("class",function(e){for(var t,r,o,i,a,u,s,c=e.length,l=n.getValidClasses();c--;){for(r=(t=e[c]).attr("class").split(" "),a="",o=0;o<r.length;o++)i=r[o],s=!1,(u=l["*"])&&u[i]&&(s=!0),u=l[t.name],!s&&u&&u[i]&&(s=!0),s&&(a&&(a+=" "),a+=i);a.length||(a=null),t.attr("class",a)}})},cv=Dt.makeMap,lv=Dt.each,fv=Dt.explode,dv=Dt.extend;function mv(e,t){void 0===t&&(t=Go());var n={},r=[],o={},i={};(e=e||{}).validate=!("validate"in e)||e.validate,e.root_name=e.root_name||"body";var a=function(e){var t,a,u;a in n&&((u=o[a])?u.push(e):o[a]=[e]),t=r.length;for(;t--;)(a=r[t].name)in e.attributes.map&&((u=i[a])?u.push(e):i[a]=[e]);return e},u={schema:t,addAttributeFilter:function(e,t){lv(fv(e),function(e){var n;for(n=0;n<r.length;n++)if(r[n].name===e)return void r[n].callbacks.push(t);r.push({name:e,callbacks:[t]})})},getAttributeFilters:function(){return[].concat(r)},addNodeFilter:function(e,t){lv(fv(e),function(e){var r=n[e];r||(n[e]=r=[]),r.push(t)})},getNodeFilters:function(){var e=[];for(var t in n)n.hasOwnProperty(t)&&e.push({name:t,callbacks:n[t]});return e},filterNode:a,parse:function(u,s){var c,l,f,d,m,p,g,h,v,y,b,C=[];s=s||{},o={},i={},v=dv(cv("script,style,head,html,body,title,meta,param"),t.getBlockElements());var x=t.getNonEmptyElements(),w=t.children,N=e.validate,E="forced_root_block"in s?s.forced_root_block:e.forced_root_block,S=t.getWhiteSpaceElements(),k=/^[ \t\r\n]+/,T=/[ \t\r\n]+$/,A=/[ \t\r\n]+/g,_=/^[ \t\r\n]+$/,R=function(e,t){var r,i=new nv(e,t);return e in n&&((r=o[e])?r.push(i):o[e]=[i]),i},B=function(e){var n,r,o,i,a=t.getBlockElements();for(n=e.prev;n&&3===n.type;){if((o=n.value.replace(T,"")).length>0)return void(n.value=o);if(r=n.next){if(3===r.type&&r.value.length){n=n.prev;continue}if(!a[r.name]&&"script"!==r.name&&"style"!==r.name){n=n.prev;continue}}i=n.prev,n.remove(),n=i}};c=Bg({validate:N,allow_script_urls:e.allow_script_urls,allow_conditional_comments:e.allow_conditional_comments,self_closing_elements:function(e){var t,n={};for(t in e)"li"!==t&&"p"!==t&&(n[t]=e[t]);return n}(t.getSelfClosingElements()),cdata:function(e){b.append(R("#cdata",4)).value=e},text:function(e,t){var n;y||(e=e.replace(A," "),uv(b.lastChild,v)&&(e=e.replace(k,""))),0!==e.length&&((n=R("#text",3)).raw=!!t,b.append(n).value=e)},comment:function(e){b.append(R("#comment",8)).value=e},pi:function(e,t){b.append(R(e,7)).value=t,B(b)},doctype:function(e){b.append(R("#doctype",10)).value=e,B(b)},start:function(e,n,o){var a,u,s,c,l;if(s=N?t.getElementRule(e):{}){for((a=R(s.outputName||e,1)).attributes=n,a.shortEnded=o,b.append(a),(l=w[b.name])&&w[a.name]&&!l[a.name]&&C.push(a),u=r.length;u--;)(c=r[u].name)in n.map&&((g=i[c])?g.push(a):i[c]=[a]);v[e]&&B(a),o||(b=a),!y&&S[e]&&(y=!0)}},end:function(n){var r,o,i,a,u;if(o=N?t.getElementRule(n):{}){if(v[n]&&!y){if((r=b.firstChild)&&3===r.type)if((i=r.value.replace(k,"")).length>0)r.value=i,r=r.next;else for(a=r.next,r.remove(),r=a;r&&3===r.type;)i=r.value,a=r.next,(0===i.length||_.test(i))&&(r.remove(),r=a),r=a;if((r=b.lastChild)&&3===r.type)if((i=r.value.replace(T,"")).length>0)r.value=i,r=r.prev;else for(a=r.prev,r.remove(),r=a;r&&3===r.type;)i=r.value,a=r.prev,(0===i.length||_.test(i))&&(r.remove(),r=a),r=a}if(y&&S[n]&&(y=!1),o.removeEmpty&&av(t,x,S,b)&&!b.attributes.map.name&&!b.attr("id"))return u=b.parent,v[b.name]?b.empty().remove():b.unwrap(),void(b=u);o.paddEmpty&&(ov(b)||av(t,x,S,b))&&rv(e,s,v,b),b=b.parent}}},t);var D=b=new nv(s.context||e.root_name,11);if(c.parse(u),N&&C.length&&(s.context?s.invalid=!0:function(e){var n,r,o,i,u,s,c,l,f,d,m,p,g,h,v,y;for(p=cv("tr,td,th,tbody,thead,tfoot,table"),d=t.getNonEmptyElements(),m=t.getWhiteSpaceElements(),g=t.getTextBlockElements(),h=t.getSpecialElements(),n=0;n<e.length;n++)if((r=e[n]).parent&&!r.fixed)if(g[r.name]&&"li"===r.parent.name){for(v=r.next;v&&g[v.name];)v.name="li",v.fixed=!0,r.parent.insert(v,r.parent),v=v.next;r.unwrap(r)}else{for(i=[r],o=r.parent;o&&!t.isValidChild(o.name,r.name)&&!p[o.name];o=o.parent)i.push(o);if(o&&i.length>1){for(i.reverse(),u=s=a(i[0].clone()),f=0;f<i.length-1;f++){for(t.isValidChild(s.name,i[f].name)?(c=a(i[f].clone()),s.append(c)):c=s,l=i[f].firstChild;l&&l!==i[f+1];)y=l.next,c.append(l),l=y;s=c}av(t,d,m,u)?o.insert(r,i[0],!0):(o.insert(u,i[0],!0),o.insert(r,u)),o=i[0],(av(t,d,m,o)||iv(o,"br"))&&o.empty().remove()}else if(r.parent){if("li"===r.name){if((v=r.prev)&&("ul"===v.name||"ul"===v.name)){v.append(r);continue}if((v=r.next)&&("ul"===v.name||"ul"===v.name)){v.insert(r,v.firstChild,!0);continue}r.wrap(a(new nv("ul",1)));continue}t.isValidChild(r.parent.name,"div")&&t.isValidChild("div",r.name)?r.wrap(a(new nv("div",1))):h[r.name]?r.empty().remove():r.unwrap()}}}(C)),E&&("body"===D.name||s.isRootContent)&&function(){var n,r,o=D.firstChild,i=function(e){e&&((o=e.firstChild)&&3===o.type&&(o.value=o.value.replace(k,"")),(o=e.lastChild)&&3===o.type&&(o.value=o.value.replace(T,"")))};if(t.isValidChild(D.name,E.toLowerCase())){for(;o;)n=o.next,3===o.type||1===o.type&&"p"!==o.name&&!v[o.name]&&!o.attr("data-mce-type")?r?r.append(o):((r=R(E,1)).attr(e.forced_root_block_attrs),D.insert(r,o),r.append(o)):(i(r),r=null),o=n;i(r)}}(),!s.invalid){for(h in o){for(g=n[h],m=(l=o[h]).length;m--;)l[m].parent||l.splice(m,1);for(f=0,d=g.length;f<d;f++)g[f](l,h,s)}for(f=0,d=r.length;f<d;f++)if((g=r[f]).name in i){for(m=(l=i[g.name]).length;m--;)l[m].parent||l.splice(m,1);for(m=0,p=g.callbacks.length;m<p;m++)g.callbacks[m](l,g.name,s)}}return D}};return sv(u,e),Qh.register(u,e),u}var pv=function(e,t,n){-1===Dt.inArray(t,n)&&(e.addAttributeFilter(n,function(e,t){for(var n=e.length;n--;)e[n].attr(t,null)}),t.push(n))},gv=function(e,t,n){var r=bi(n.getInner?t.innerHTML:e.getOuterHTML(t));return n.selection?r:Dt.trim(r)},hv=function(e,t,n,r){var o=r.selection?jh.merge({forced_root_block:!1},r):r,i=e.parse(n,o);return Xh.trimTrailingBr(i),i},vv=function(e,t,n,r,o){var i,a,u,s,c=(i=r,ic(t,n).serialize(i));return a=e,s=c,!(u=o).no_events&&a?Wh(a,jh.merge(u,{content:s})).content:s};function yv(e,t){var n,r,o,i,a,u,s=(n=e,u=["data-mce-selected"],o=(r=t)&&r.dom?r.dom:ui.DOM,i=r&&r.schema?r.schema:Go(n),n.entity_encoding=n.entity_encoding||"named",n.remove_trailing_brs=!("remove_trailing_brs"in n)||n.remove_trailing_brs,a=mv(n,i),Xh.register(a,n,o),{schema:i,addNodeFilter:a.addNodeFilter,addAttributeFilter:a.addAttributeFilter,serialize:function(e,t){var u=jh.merge({format:"html"},t||{}),s=Yh.process(r,e,u),c=gv(o,s,u),l=hv(a,o,c,u);return"tree"===u.format?l:vv(r,n,i,l,u)},addRules:function(e){i.addValidElements(e)},setRules:function(e){i.setValidElements(e)},addTempAttr:y.curry(pv,a,u),getTempAttrs:function(){return u}});return{schema:s.schema,addNodeFilter:s.addNodeFilter,addAttributeFilter:s.addAttributeFilter,serialize:s.serialize,addRules:s.addRules,setRules:s.setRules,addTempAttr:s.addTempAttr,getTempAttrs:s.getTempAttrs}}var bv=function(e,t){var n;t.hasAttribute("data-mce-caret")&&(Bi(t),(n=e).selection.setRng(n.selection.getRng()),e.selection.scrollIntoView(t))},Cv=function(e,t){var n,r=(n=e,Lc(Fn.fromDom(n.getBody()),"*[data-mce-caret]").fold(y.constant(null),function(e){return e.dom()}));if(r)return"compositionstart"===t.type?(t.preventDefault(),t.stopPropagation(),void bv(e,r)):void(Si(r)&&bv(e,r))},xv=function(e){e.on("keyup compositionstart",y.curry(Cv,e))};function wv(e){return{getBookmark:y.curry(Vs.getBookmark,e),moveToBookmark:y.curry(Vs.moveToBookmark,e)}}(wv||(wv={})).isBookmarkNode=Vs.isBookmarkNode;var Nv=wv,Ev=So.isContentEditableFalse,Sv=So.isContentEditableTrue,kv=function(e,t){var n,r,o,i,a,u,s,c,l,f,d,m,p,g,h,v,y,b=t.dom,C=Dt.each,x=t.getDoc(),w=document,N=Math.abs,E=Math.round,S=t.getBody();i={nw:[0,0,-1,-1],ne:[1,0,1,-1],se:[1,1,1,1],sw:[0,1,-1,1]};var k=".mce-content-body";t.contentStyles.push(k+" div.mce-resizehandle {position: absolute;border: 1px solid black;box-sizing: content-box;background: #FFF;width: 7px;height: 7px;z-index: 10000}"+k+" .mce-resizehandle:hover {background: #000}"+k+" img[data-mce-selected],"+k+" hr[data-mce-selected] {outline: 1px solid black;resize: none}"+k+" .mce-clonedresizable {position: absolute;"+(de.gecko?"":"outline: 1px dashed black;")+"opacity: .5;filter: alpha(opacity=50);z-index: 10000}"+k+" .mce-resize-helper {background: #555;background: rgba(0,0,0,0.75);border-radius: 3px;border: 1px;color: white;display: none;font-family: sans-serif;font-size: 12px;white-space: nowrap;line-height: 14px;margin: 5px 10px;padding: 5px;position: absolute;z-index: 10001}");var T=function(e){return e&&("IMG"===e.nodeName||t.dom.is(e,"figure.image"))},A=function(e){var n,r,o=e.target;n=e,r=t.selection.getRng(),!T(n.target)||vg(n.clientX,n.clientY,r)||e.isDefaultPrevented()||(e.preventDefault(),t.selection.select(o))},_=function(e){return t.dom.is(e,"figure.image")?e.querySelector("img"):e},R=function(e){var n=t.settings.object_resizing;return!1!==n&&!de.iOS&&("string"!=typeof n&&(n="table,img,figure.image,div"),"false"!==e.getAttribute("data-mce-resize")&&e!==t.getBody()&&Tr.is(Fn.fromDom(e),n))},B=function(e){var i,C,x,w;i=e.screenX-u,C=e.screenY-s,g=i*a[2]+f,h=C*a[3]+d,g=g<5?5:g,h=h<5?5:h,(T(n)&&!1!==t.settings.resize_img_proportional?!Cg.modifierPressed(e):Cg.modifierPressed(e)||T(n)&&a[2]*a[3]!=0)&&(N(i)>N(C)?(h=E(g*m),g=E(h/m)):(g=E(h/m),h=E(g*m))),b.setStyles(_(r),{width:g,height:h}),x=(x=a.startPos.x+i)>0?x:0,w=(w=a.startPos.y+C)>0?w:0,b.setStyles(o,{left:x,top:w,display:"block"}),o.innerHTML=g+" &times; "+h,a[2]<0&&r.clientWidth<=g&&b.setStyle(r,"left",c+(f-g)),a[3]<0&&r.clientHeight<=h&&b.setStyle(r,"top",l+(d-h)),(i=S.scrollWidth-v)+(C=S.scrollHeight-y)!=0&&b.setStyles(o,{left:x-i,top:w-C}),p||(t.fire("ObjectResizeStart",{target:n,width:f,height:d}),p=!0)},D=function(){p=!1;var e=function(e,r){r&&(n.style[e]||!t.schema.isValid(n.nodeName.toLowerCase(),e)?b.setStyle(_(n),e,r):b.setAttrib(_(n),e,r))};e("width",g),e("height",h),b.unbind(x,"mousemove",B),b.unbind(x,"mouseup",D),w!==x&&(b.unbind(w,"mousemove",B),b.unbind(w,"mouseup",D)),b.remove(r),b.remove(o),O(n),t.fire("ObjectResized",{target:n,width:g,height:h}),b.setAttrib(n,"style",b.getAttrib(n,"style")),t.nodeChanged()},O=function(e){var p,N,E,k,T;P(),M(),p=b.getPos(e,S),c=p.x,l=p.y,T=e.getBoundingClientRect(),N=T.width||T.right-T.left,E=T.height||T.bottom-T.top,n!==e&&(n=e,g=h=0),k=t.fire("ObjectSelected",{target:e}),R(e)&&!k.isDefaultPrevented()?C(i,function(e,t){var i;(i=b.get("mceResizeHandle"+t))&&b.remove(i),i=b.add(S,"div",{id:"mceResizeHandle"+t,"data-mce-bogus":"all","class":"mce-resizehandle",unselectable:!0,style:"cursor:"+t+"-resize; margin:0; padding:0"}),de.ie&&(i.contentEditable=!1),b.bind(i,"mousedown",function(t){var i;t.stopImmediatePropagation(),t.preventDefault(),u=(i=t).screenX,s=i.screenY,f=_(n).clientWidth,d=_(n).clientHeight,m=d/f,a=e,e.startPos={x:N*e[0]+c,y:E*e[1]+l},v=S.scrollWidth,y=S.scrollHeight,r=n.cloneNode(!0),b.addClass(r,"mce-clonedresizable"),b.setAttrib(r,"data-mce-bogus","all"),r.contentEditable=!1,r.unSelectabe=!0,b.setStyles(r,{left:c,top:l,margin:0}),r.removeAttribute("data-mce-selected"),S.appendChild(r),b.bind(x,"mousemove",B),b.bind(x,"mouseup",D),w!==x&&(b.bind(w,"mousemove",B),b.bind(w,"mouseup",D)),o=b.add(S,"div",{"class":"mce-resize-helper","data-mce-bogus":"all"},f+" &times; "+d)}),e.elm=i,b.setStyles(i,{left:N*e[0]+c-i.offsetWidth/2,top:E*e[1]+l-i.offsetHeight/2})}):P(),n.setAttribute("data-mce-selected","1")},P=function(){var e,t;for(e in M(),n&&n.removeAttribute("data-mce-selected"),i)(t=b.get("mceResizeHandle"+e))&&(b.unbind(t),b.remove(t))},L=function(n){var r,o=function(e,t){if(e)do{if(e===t)return!0}while(e=e.parentNode)};p||t.removed||(C(b.select("img[data-mce-selected],hr[data-mce-selected]"),function(e){e.removeAttribute("data-mce-selected")}),r="mousedown"===n.type?n.target:e.getNode(),o(r=b.$(r).closest("table,img,figure.image,hr")[0],S)&&(F(),o(e.getStart(!0),r)&&o(e.getEnd(!0),r))?O(r):P())},I=function(e){return Ev(function(e,t){for(;t&&t!==e;){if(Sv(t)||Ev(t))return t;t=t.parentNode}return null}(t.getBody(),e))},M=function(){for(var e in i){var t=i[e];t.elm&&(b.unbind(t.elm),delete t.elm)}},F=function(){try{t.getDoc().execCommand("enableObjectResizing",!1,!1)}catch(e){}};return t.on("init",function(){F(),de.ie&&de.ie>=11&&(t.on("mousedown click",function(e){var n=e.target,r=n.nodeName;p||!/^(TABLE|IMG|HR)$/.test(r)||I(n)||(2!==e.button&&t.selection.select(n,"TABLE"===r),"mousedown"===e.type&&t.nodeChanged())}),t.dom.bind(S,"mscontrolselect",function(e){var n=function(e){ve.setEditorTimeout(t,function(){t.selection.select(e)})};if(I(e.target))return e.preventDefault(),void n(e.target);/^(TABLE|IMG|HR)$/.test(e.target.nodeName)&&(e.preventDefault(),"IMG"===e.target.tagName&&n(e.target))}));var e=ve.throttle(function(e){t.composing||L(e)});t.on("nodechange ResizeEditor ResizeWindow drop FullscreenStateChanged",e),t.on("keyup compositionend",function(t){n&&"TABLE"===n.nodeName&&e(t)}),t.on("hide blur",P),t.on("contextmenu",A)}),t.on("remove",M),{isResizable:R,showResizeRect:O,hideResizeRect:P,updateResizeRect:L,destroy:function(){n=r=null}}},Tv=function(e){for(var t=0,n=0,r=e;r&&r.nodeType;)t+=r.offsetLeft||0,n+=r.offsetTop||0,r=r.offsetParent;return{x:t,y:n}},Av=function(e,t,n){var r,o,i,a,u,s=e.dom,c=s.getRoot(),l=0;if(u={elm:t,alignToTop:n},e.fire("scrollIntoView",u),!u.isDefaultPrevented()&&So.isElement(t)){if(!1===n&&(l=t.offsetHeight),"BODY"!==c.nodeName){var f=e.selection.getScrollContainer();if(f)return r=Tv(t).y-Tv(f).y+l,a=f.clientHeight,void((r<(i=f.scrollTop)||r+25>i+a)&&(f.scrollTop=r<i?r:r-a+25))}o=s.getViewPort(e.getWin()),r=s.getPos(t).y+l,i=o.y,a=o.h,(r<o.y||r+25>i+a)&&e.getWin().scrollTo(0,r<i?r:r-a+25)}},_v=function(e){return So.isContentEditableTrue(e)||So.isContentEditableFalse(e)},Rv={fromPoint:function(e,t,n){var r,o,i,a,u,s=n;if(s.caretPositionFromPoint)(o=s.caretPositionFromPoint(e,t))&&((r=n.createRange()).setStart(o.offsetNode,o.offset),r.collapse(!0));else if(n.caretRangeFromPoint)r=n.caretRangeFromPoint(e,t);else if(s.body.createTextRange){r=s.body.createTextRange();try{r.moveToPoint(e,t),r.collapse(!0)}catch(c){r=function(e,t,n){var r,o,i;if(r=n.elementFromPoint(e,t),o=n.body.createTextRange(),r&&"HTML"!==r.tagName||(r=n.body),o.moveToElementText(r),(i=(i=Dt.toArray(o.getClientRects())).sort(function(e,n){return(e=Math.abs(Math.max(e.top-t,e.bottom-t)))-(n=Math.abs(Math.max(n.top-t,n.bottom-t)))})).length>0){t=(i[0].bottom+i[0].top)/2;try{return o.moveToPoint(e,t),o.collapse(!0),o}catch(a){}}return null}(e,t,n)}return i=r,a=n.body,u=i&&i.parentElement?i.parentElement():null,So.isContentEditableFalse(function(e,t,n){for(;e&&e!==t;){if(n(e))return e;e=e.parentNode}return null}(u,a,_v))?null:i}return r}},Bv=function(e,t){return M.map(t,function(t){var n=e.fire("GetSelectionRange",{range:t});return n.range!==t?n.range:t})},Dv=function(e,t){return Fn.fromDom(e.dom().cloneNode(t))},Ov=function(e){return Dv(e,!0)},Pv=function(e){return Dv(e,!1)},Lv=Ov,Iv=function(e,t){var n=(t||document).createDocumentFragment();return M.each(e,function(e){n.appendChild(e.dom())}),Fn.fromDom(n)},Mv=function(e){return Fr.firstChild(e).fold(y.constant([e]),function(t){return[e].concat(Mv(t))})},Fv=function(e){return Fr.lastChild(e).fold(y.constant([e]),function(t){return"br"===Yn.name(t)?Fr.prevSibling(t).map(function(t){return[e].concat(Fv(t))}).getOr([]):[e].concat(Fv(t))})},zv=function(e,t){return Wa([(i=t,a=i.startContainer,u=i.startOffset,So.isText(a)?0===u?E.some(Fn.fromDom(a)):E.none():E.from(a.childNodes[u]).map(Fn.fromDom)),(n=t,r=n.endContainer,o=n.endOffset,So.isText(r)?o===r.data.length?E.some(Fn.fromDom(r)):E.none():E.from(r.childNodes[o-1]).map(Fn.fromDom))],function(t,n){var r=M.find(Mv(e),y.curry(Rr.eq,t)),o=M.find(Fv(e),y.curry(Rr.eq,n));return r.isSome()&&o.isSome()}).getOr(!1);var n,r,o,i,a,u},Uv=function(e,t,n,r){var o=n,i=new Zr(n,o),a=e.schema.getNonEmptyElements();do{if(3===n.nodeType&&0!==Dt.trim(n.nodeValue).length)return void(r?t.setStart(n,0):t.setEnd(n,n.nodeValue.length));if(a[n.nodeName]&&!/^(TD|TH)$/.test(n.nodeName))return void(r?t.setStartBefore(n):"BR"===n.nodeName?t.setEndBefore(n):t.setEndAfter(n));if(de.ie&&de.ie<11&&e.isBlock(n)&&e.isEmpty(n))return void(r?t.setStart(n,0):t.setEnd(n,0))}while(n=r?i.next():i.prev());"BODY"===o.nodeName&&(r?t.setStart(o,0):t.setEnd(o,o.childNodes.length))},qv=br.immutable("element","width","rows"),Vv=br.immutable("element","cells"),Hv=br.immutable("x","y"),jv=function(e,t){var n=parseInt(sr.get(e,t),10);return isNaN(n)?1:n},$v=function(e){return M.foldl(e,function(e,t){return t.cells().length>e?t.cells().length:e},0)},Wv=function(e,t){for(var n=e.rows(),r=0;r<n.length;r++)for(var o=n[r].cells(),i=0;i<o.length;i++)if(Rr.eq(o[i],t))return E.some(Hv(i,r));return E.none()},Kv=function(e,t,n,r,o){for(var i=[],a=e.rows(),u=n;u<=o;u++){var s=a[u].cells(),c=t<r?s.slice(t,r+1):s.slice(r,t+1);i.push(Vv(a[u].element(),c))}return i},Xv=function(e){var t=qv(Pv(e),0,[]);return M.each(ou(e,"tr"),function(e,n){M.each(ou(e,"td,th"),function(r,o){!function(e,t,n,r,o){for(var i=jv(o,"rowspan"),a=jv(o,"colspan"),u=e.rows(),s=n;s<n+i;s++){u[s]||(u[s]=Vv(Lv(r),[]));for(var c=t;c<t+a;c++)u[s].cells()[c]=s===n&&c===t?o:Pv(o)}}(t,function(e,t,n){for(;r=t,o=n,i=void 0,((i=e.rows())[o]?i[o].cells():[])[r];)t++;var r,o,i;return t}(t,o,n),n,e,r)})}),qv(t.element(),$v(t.rows()),t.rows())},Yv=function(e){return t=e,i=e,n=M.map(i.rows(),function(e){var t=M.map(e.cells(),function(e){var t=Lv(e);return sr.remove(t,"colspan"),sr.remove(t,"rowspan"),t}),n=Pv(e.element());return Ys(n,t),n}),r=Pv(t.element()),o=Fn.fromTag("tbody"),Ys(o,n),Ks.append(r,o),r;var t,n,r,o,i},Gv=function(e,t,n){return Wv(e,t).bind(function(t){return Wv(e,n).map(function(n){return r=e,i=n,a=(o=t).x(),u=o.y(),s=i.x(),c=i.y(),l=u<c?Kv(r,a,u,s,c):Kv(r,a,c,s,u),qv(r.element(),$v(l),l);var r,o,i,a,u,s,c,l})})},Jv=function(e,t){return M.find(e,function(e){return"li"===Yn.name(e)&&zv(e,t)}).fold(y.constant([]),function(t){return(n=e,M.find(n,function(e){return"ul"===Yn.name(e)||"ol"===Yn.name(e)})).map(function(e){return[Fn.fromTag("li"),Fn.fromTag(Yn.name(e))]}).getOr([]);var n})},Qv=function(e,t){var n,r=Fn.fromDom(t.commonAncestorContainer),o=Qc(r,e),i=M.filter(o,function(e){return ao(e)||oo(e)}),a=Jv(o,t),u=i.concat(a.length?a:lo(n=r)?Fr.parent(n).filter(co).fold(y.constant([]),function(e){return[n,e]}):co(n)?[n]:[]);return M.map(u,Pv)},Zv=function(){return Iv([])},ey=function(e,t){return n=Fn.fromDom(t.cloneContents()),r=Qv(e,t),o=M.foldl(r,function(e,t){return Ks.append(t,e),t},n),r.length>0?Iv([o]):o;var n,r,o},ty=function(e,t){return(n=e,r=t[0],Pc(r,"table",y.curry(Rr.eq,n))).bind(function(e){var n=t[0],r=t[t.length-1],o=Xv(e);return Gv(o,n,r).map(function(e){return Iv([Yv(e)])})}).getOrThunk(Zv);var n,r},ny=function(e,t){var n,r,o=Td(t,e);return o.length>0?ty(e,o):(n=e,(r=t).length>0&&r[0].collapsed?Zv():ey(n,r[0]))},ry=function(e,t){var n,r=e.selection.getRng(),o=e.dom.create("body"),i=e.selection.getSel(),a=Bv(e,xd(i));if((t=t||{}).get=!0,t.format=t.format||"html",t.selection=!0,(t=e.fire("BeforeGetContent",t)).isDefaultPrevented())return e.fire("GetContent",t),t.content;if("text"===t.format)return e.selection.isCollapsed()?"":bi(r.text||(i.toString?i.toString():""));r.cloneContents?(n=t.contextual?ny(Fn.fromDom(e.getBody()),a).dom():r.cloneContents())&&o.appendChild(n):r.item!==undefined||r.htmlText!==undefined?(o.innerHTML="<br>"+(r.item?r.item(0).outerHTML:r.htmlText),o.removeChild(o.firstChild)):o.innerHTML=r.toString(),t.getInner=!0;var u=e.selection.serializer.serialize(o,t);return"tree"===t.format?u:(t.content=e.selection.isCollapsed()?"":u,e.fire("GetContent",t),t.content)},oy=function(e,t,n){var r,o,i,a=e.selection.getRng(),u=e.getDoc();if((n=n||{format:"html"}).set=!0,n.selection=!0,n.content=t,n.no_events||!(n=e.fire("BeforeSetContent",n)).isDefaultPrevented()){if(t=n.content,a.insertNode){t+='<span id="__caret">_</span>',a.startContainer===u&&a.endContainer===u?u.body.innerHTML=t:(a.deleteContents(),0===u.body.childNodes.length?u.body.innerHTML=t:a.createContextualFragment?a.insertNode(a.createContextualFragment(t)):(o=u.createDocumentFragment(),i=u.createElement("div"),o.appendChild(i),i.outerHTML=t,a.insertNode(o))),r=e.dom.get("__caret"),(a=u.createRange()).setStartBefore(r),a.setEndBefore(r),e.selection.setRng(a),e.dom.remove("__caret");try{e.selection.setRng(a)}catch(s){}}else a.item&&(u.execCommand("Delete",!1,null),a=e.getRng()),/^\s+/.test(t)?(a.pasteHTML('<span id="__mce_tmp">_</span>'+t),e.dom.remove("__mce_tmp")):a.pasteHTML(t);n.no_events||e.fire("SetContent",n)}else e.fire("SetContent",n)},iy=function(e,t,n,r,o){var i=n?t.startContainer:t.endContainer,a=n?t.startOffset:t.endOffset;return E.from(i).map(Fn.fromDom).map(function(e){return r&&t.collapsed?e:Fr.child(e,o(e,a)).getOr(e)}).bind(function(e){return Yn.isElement(e)?E.some(e):Fr.parent(e)}).map(function(e){return e.dom()}).getOr(e)},ay=function(e,t,n){return iy(e,t,!0,n,function(e,t){return Math.min(Fr.childNodesCount(e),t)})},uy=function(e,t,n){return iy(e,t,!1,n,function(e,t){return t>0?t-1:t})},sy=function(e,t){for(var n=e;e&&So.isText(e)&&0===e.length;)e=t?e.nextSibling:e.previousSibling;return e||n},cy=Dt.each,ly=function(e){return!!e.select},fy=function(e){return!(!e||!e.ownerDocument)&&Rr.contains(Fn.fromDom(e.ownerDocument),Fn.fromDom(e))},dy=function(e,t,n,r){var o,i,a,u,s,c=function(e,t){return oy(r,e,t)},l=function(e){var t=d();t.collapse(!!e),m(t)},f=function(){return t.getSelection?t.getSelection():t.document.selection},d=function(){var n,o,i,s,c=function(e,t,n){try{return t.compareBoundaryPoints(e,n)}catch(r){return-1}};if(!t)return null;if(null==(s=t.document))return null;if(r.bookmark!==undefined&&!1===up(r)){var l=Nm.getRng(r);if(l.isSome())return l.map(function(e){return Bv(r,[e])[0]}).getOr(s.createRange())}try{(n=f())&&(o=n.rangeCount>0?n.getRangeAt(0):n.createRange?n.createRange():s.createRange())}catch(d){}return(o=Bv(r,[o])[0])||(o=s.createRange?s.createRange():s.body.createTextRange()),o.setStart&&9===o.startContainer.nodeType&&o.collapsed&&(i=e.getRoot(),o.setStart(i,0),o.setEnd(i,0)),a&&u&&(0===c(o.START_TO_START,o,a)&&0===c(o.END_TO_END,o,a)?o=u:(a=null,u=null)),o},m=function(e,t){var n,o;if((i=e)&&(ly(i)||fy(i.startContainer)&&fy(i.endContainer))){var i,s=ly(e)?e:null;if(s){u=null;try{s.select()}catch(c){}}else{if(n=f(),e=r.fire("SetSelectionRange",{range:e,forward:t}).range,n){u=e;try{n.removeAllRanges(),n.addRange(e)}catch(c){}!1===t&&n.extend&&(n.collapse(e.endContainer,e.endOffset),n.extend(e.startContainer,e.startOffset)),a=n.rangeCount>0?n.getRangeAt(0):null}e.collapsed||e.startContainer!==e.endContainer||!n.setBaseAndExtent||de.ie||e.endOffset-e.startOffset<2&&e.startContainer.hasChildNodes()&&(o=e.startContainer.childNodes[e.startOffset])&&"IMG"===o.tagName&&(n.setBaseAndExtent(e.startContainer,e.startOffset,e.endContainer,e.endOffset),n.anchorNode===e.startContainer&&n.focusNode===e.endContainer||n.setBaseAndExtent(o,0,o,1)),r.fire("AfterSetSelectionRange",{range:e,forward:t})}}},p=function(){var t,n,r=f();return!(r&&r.anchorNode&&r.focusNode)||((t=e.createRng()).setStart(r.anchorNode,r.anchorOffset),t.collapse(!0),(n=e.createRng()).setStart(r.focusNode,r.focusOffset),n.collapse(!0),t.compareBoundaryPoints(t.START_TO_START,n)<=0)},g={bookmarkManager:null,controlSelection:null,dom:e,win:t,serializer:n,editor:r,collapse:l,setCursorLocation:function(t,n){var o=e.createRng();t?(o.setStart(t,n),o.setEnd(t,n),m(o),l(!1)):(Uv(e,o,r.getBody(),!0),m(o))},getContent:function(e){return ry(r,e)},setContent:c,getBookmark:function(e,t){return o.getBookmark(e,t)},moveToBookmark:function(e){return o.moveToBookmark(e)},select:function(t,n){var r,o,i;return(r=e,o=t,i=n,E.from(o).map(function(e){var t=r.nodeIndex(e),n=r.createRng();return n.setStart(e.parentNode,t),n.setEnd(e.parentNode,t+1),i&&(Uv(r,n,e,!0),Uv(r,n,e,!1)),n})).each(m),t},isCollapsed:function(){var e=d(),t=f();return!(!e||e.item)&&(e.compareEndPoints?0===e.compareEndPoints("StartToEnd",e):!t||e.collapsed)},isForward:p,setNode:function(t){return c(e.getOuterHTML(t)),t},getNode:function(){return e=r.getBody(),(t=d())?(o=t.startContainer,i=t.endContainer,a=t.startOffset,u=t.endOffset,n=t.commonAncestorContainer,!t.collapsed&&(o===i&&u-a<2&&o.hasChildNodes()&&(n=o.childNodes[a]),3===o.nodeType&&3===i.nodeType&&(o=o.length===a?sy(o.nextSibling,!0):o.parentNode,i=0===u?sy(i.previousSibling,!1):i.parentNode,o&&o===i))?o:n&&3===n.nodeType?n.parentNode:n):e;var e,t,n,o,i,a,u},getSel:f,setRng:m,getRng:d,getStart:function(e){return ay(r.getBody(),d(),e)},getEnd:function(e){return uy(r.getBody(),d(),e)},getSelectedBlocks:function(t,n){return function(e,t,n,r){var o,i,a=[];if(i=e.getRoot(),n=e.getParent(n||ay(i,t,!1),e.isBlock),r=e.getParent(r||uy(i,t,!1),e.isBlock),n&&n!==i&&a.push(n),n&&r&&n!==r){o=n;for(var u=new Zr(n,i);(o=u.next())&&o!==r;)e.isBlock(o)&&a.push(o)}return r&&n!==r&&r!==i&&a.push(r),a}(e,d(),t,n)},normalize:function(){var t=d();if(!Nd(f())){var n=Yd.normalize(e,t);return n.each(function(e){m(e,p())}),n.getOr(t)}return t},selectorChanged:function(t,n){var o;return s||(s={},o={},r.on("NodeChange",function(t){var n=t.element,r=e.getParents(n,null,e.getRoot()),i={};cy(s,function(t,n){cy(r,function(a){if(e.is(a,n))return o[n]||(cy(t,function(e){e(!0,{node:a,selector:n,parents:r})}),o[n]=t),i[n]=t,!1})}),cy(o,function(e,t){i[t]||(delete o[t],cy(e,function(e){e(!1,{node:n,selector:t,parents:r})}))})})),s[t]||(s[t]=[]),s[t].push(n),g},getScrollContainer:function(){for(var t,n=e.getRoot();n&&"BODY"!==n.nodeName;){if(n.scrollHeight>n.clientHeight){t=n;break}n=n.parentNode}return t},scrollIntoView:function(e,t){return Av(r,e,t)},placeCaretAt:function(e,t){return m(Rv.fromPoint(e,t,r.getDoc()))},getBoundingClientRect:function(){var e=d();return e.collapsed?wa.fromRangeStart(e).getClientRects()[0]:e.getBoundingClientRect()},destroy:function(){t=a=u=null,i.destroy()}};return o=Nv(g),i=kv(g,r),g.bookmarkManager=o,g.controlSelection=i,g},my=So.isContentEditableFalse,py=Xi,gy=Wu,hy=$u,vy=function(e,t){for(;t=e(t);)if(t.isVisible())return t;return t},yy=function(e,t,n,r){var o,i,a,u,s,c,l=e===Ka.Forwards,f=l?hy:gy;return!r.collapsed&&(o=py(r),my(o))?Va(e,t,o,e===Ka.Backwards,!0):(u=wi(r.startContainer),f(i=ju(e,t.getBody(),r))?Ha(t,i.getNode(!l)):(i=n(i))?f(i)?Va(e,t,i.getNode(!l),l,!0):f(a=n(i))&&(!(c=Pu(s=i,a))&&So.isBr(s.getNode())||c)?Va(e,t,a.getNode(!l),l,!0):u?$a(t,i.toRange(),!0):null:u?r:null)},by=function(e,t,n,r){var o,i,a,u,s,c,l,f,d;if(d=py(r),o=ju(e,t.getBody(),r),i=n(t.getBody(),ug(1),o),a=Tt.filter(i,sg(1)),s=Tt.last(o.getClientRects()),(hy(o)||Ku(o))&&(d=o.getNode()),(gy(o)||Xu(o))&&(d=o.getNode(!0)),!s)return null;if(c=s.left,(u=pg(a,c))&&my(u.node))return l=Math.abs(c-u.left),f=Math.abs(c-u.right),Va(e,t,u.node,l<f,!0);if(d){var m=function(e,t,n,r){var o,i,a,u,s,c,l=ls(t),f=[],d=0,m=function(e){return Tt.last(e.getClientRects())};1===e?(o=l.next,i=Ki,a=Wi,u=wa.after(r)):(o=l.prev,i=Wi,a=Ki,u=wa.before(r)),c=m(u);do{if(u.isVisible()&&!a(s=m(u),c)){if(f.length>0&&i(s,Tt.last(f))&&d++,(s=Hi(s)).position=u,s.line=d,n(s))return f;f.push(s)}}while(u=o(u));return f}(e,t.getBody(),ug(1),d);if(u=pg(Tt.filter(m,sg(1)),c))return $a(t,u.position.toRange(),!0);if(u=Tt.last(Tt.filter(m,sg(0))))return $a(t,u.position.toRange(),!0)}},Cy=function(e,t,n){var r,o,i,a,u=ls(e.getBody()),s=ea.curry(vy,u.next),c=ea.curry(vy,u.prev);if(n.collapsed&&e.settings.forced_root_block){if(!(r=e.dom.getParent(n.startContainer,"PRE")))return;(1===t?s(wa.fromRangeStart(n)):c(wa.fromRangeStart(n)))||(a=(i=e).dom.create(i.settings.forced_root_block),(!de.ie||de.ie>=11)&&(a.innerHTML='<br data-mce-bogus="1">'),o=a,1===t?e.$(r).after(o):e.$(r).before(o),e.selection.select(o,!0),e.selection.collapse())}},xy=function(e,t){return function(){var n,r,o,i,a,u,s,c,l,f=(r=t,i=ls((n=e).getBody()),a=ea.curry(vy,i.next),u=ea.curry(vy,i.prev),s=r?Ka.Forwards:Ka.Backwards,c=r?a:u,l=n.selection.getRng(),(o=yy(s,n,c,l))?o:(o=Cy(n,s,l))||null);return!!f&&(e.selection.setRng(f),!0)}},wy=function(e,t){return function(){var n,r,o,i,a,u,s=(i=(r=t)?1:-1,a=r?ag:ig,u=(n=e).selection.getRng(),(o=by(i,n,a,u))?o:(o=Cy(n,i,u))||null);return!!s&&(e.selection.setRng(s),!0)}},Ny=function(e,t){return M.bind((n=e,M.map(n,function(e){return jh.merge({shiftKey:!1,altKey:!1,ctrlKey:!1,metaKey:!1,keyCode:0,action:y.noop},e)})),function(e){return n=e,(r=t).keyCode===n.keyCode&&r.shiftKey===n.shiftKey&&r.altKey===n.altKey&&r.ctrlKey===n.ctrlKey&&r.metaKey===n.metaKey?[e]:[];var n,r});var n},Ey=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,r)}},Sy=function(e,t){return M.find(Ny(e,t),function(e){return e.action()})},ky=function(e,t){e.on("keydown",function(n){var r,o,i,a;!1===n.isDefaultPrevented()&&(r=e,o=t,i=n,a=In.detect().os,Sy([{keyCode:Cg.RIGHT,action:xy(r,!0)},{keyCode:Cg.LEFT,action:xy(r,!1)},{keyCode:Cg.UP,action:wy(r,!1)},{keyCode:Cg.DOWN,action:wy(r,!0)},{keyCode:Cg.RIGHT,action:vu(r,!0)},{keyCode:Cg.LEFT,action:vu(r,!1)},{keyCode:Cg.UP,action:yu(r,!1)},{keyCode:Cg.DOWN,action:yu(r,!0)},{keyCode:Cg.RIGHT,action:td.move(r,o,!0)},{keyCode:Cg.LEFT,action:td.move(r,o,!1)},{keyCode:Cg.RIGHT,ctrlKey:!a.isOSX(),altKey:a.isOSX(),action:td.moveNextWord(r,o)},{keyCode:Cg.LEFT,ctrlKey:!a.isOSX(),altKey:a.isOSX(),action:td.movePrevWord(r,o)}],i).each(function(e){i.preventDefault()}))})},Ty=function(e){return 1===Fr.children(e).length},Ay=function(e,t){var n,r=Fn.fromDom(e.getBody()),o=Fn.fromDom(e.selection.getStart()),i=M.filter((n=Qc(o,r),M.findIndex(n,io).fold(y.constant(n),function(e){return n.slice(0,e)})),Ty);return M.last(i).map(function(n){var r=wa.fromRangeStart(e.selection.getRng());return!!Dc(t,r,n.dom())&&(function(e,t,n,r){var o=y.curry(Nf.isFormatElement,t),i=M.map(M.filter(r,o),function(e){return e.dom()});if(0===i.length)El(t,e,n);else{var a=Nf.replaceWithCaretFormat(n.dom(),i);t.selection.setRng(a.toRange())}}(t,e,n,i),!0)}).getOr(!1)},_y=function(e,t){return!!e.selection.isCollapsed()&&Ay(e,t)},Ry=function(e,t){e.on("keydown",function(n){var r,o,i;!1===n.isDefaultPrevented()&&(r=e,o=t,i=n,Sy([{keyCode:Cg.BACKSPACE,action:Ey(Tl,r,!1)},{keyCode:Cg.DELETE,action:Ey(Tl,r,!0)},{keyCode:Cg.BACKSPACE,action:Ey(id,r,o,!1)},{keyCode:Cg.DELETE,action:Ey(id,r,o,!0)},{keyCode:Cg.BACKSPACE,action:Ey(cl,r,!1)},{keyCode:Cg.DELETE,action:Ey(cl,r,!0)},{keyCode:Cg.BACKSPACE,action:Ey(il,r,!1)},{keyCode:Cg.DELETE,action:Ey(il,r,!0)},{keyCode:Cg.BACKSPACE,action:Ey(Fd,r,!1)},{keyCode:Cg.DELETE,action:Ey(Fd,r,!0)},{keyCode:Cg.BACKSPACE,action:Ey(_y,r,!1)},{keyCode:Cg.DELETE,action:Ey(_y,r,!0)}],i).each(function(e){i.preventDefault()}))}),e.on("keyup",function(t){var n,r;!1===t.isDefaultPrevented()&&(n=e,r=t,Sy([{keyCode:Cg.BACKSPACE,action:Ey(Al,n)},{keyCode:Cg.DELETE,action:Ey(Al,n)}],r))})},By=function(e,t,n){var r=e.getParam(t,n);if(-1!==r.indexOf("=")){var o=e.getParam(t,"","hash");return o.hasOwnProperty(e.id)?o[e.id]:n}return r},Dy=function(e){return e.getParam("iframe_attrs",{})},Oy=function(e){return e.getParam("doctype","<!DOCTYPE html>")},Py=function(e){return e.getParam("document_base_url","")},Ly=function(e){return By(e,"body_id","tinymce")},Iy=function(e){return By(e,"body_class","")},My=function(e){return e.getParam("content_security_policy","")},Fy=function(e){return e.getParam("br_in_pre",!0)},zy=function(e){if(e.getParam("force_p_newlines",!1))return"p";var t=e.getParam("forced_root_block","p");return!1===t?"":t},Uy=function(e){return e.getParam("forced_root_block_attrs",{})},qy=function(e){return e.getParam("br_newline_selector",".mce-toc h2,figcaption,caption")},Vy=function(e){return e.getParam("no_newline_selector","")},Hy=function(e){return e.getParam("keep_styles",!0)},jy=function(e){return e.getParam("end_container_on_empty_block",!1)},$y=function(e){return E.from(e.dom.getParent(e.selection.getStart(!0),e.dom.isBlock))},Wy=function(e,t){var n,r,o,i=t,a=e.dom,u=e.schema.getMoveCaretBeforeOnEnterElements();if(t){if(/^(LI|DT|DD)$/.test(t.nodeName)){var s=function(e){for(;e;){if(1===e.nodeType||3===e.nodeType&&e.data&&/[\r\n\s]/.test(e.data))return e;e=e.nextSibling}}(t.firstChild);s&&/^(UL|OL|DL)$/.test(s.nodeName)&&t.insertBefore(a.doc.createTextNode("\xa0"),t.firstChild)}if(o=a.createRng(),t.normalize(),t.hasChildNodes()){for(n=new Zr(t,t);r=n.current();){if(So.isText(r)){o.setStart(r,0),o.setEnd(r,0);break}if(u[r.nodeName.toLowerCase()]){o.setStartBefore(r),o.setEndBefore(r);break}i=r,r=n.next()}r||(o.setStart(i,0),o.setEnd(i,0))}else So.isBr(t)?t.nextSibling&&a.isBlock(t.nextSibling)?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)):(o.setStart(t,0),o.setEnd(t,0));e.selection.setRng(o),a.remove(void 0),e.selection.scrollIntoView(t)}},Ky=function(e,t){var n,r,o=e.getRoot();for(n=t;n!==o&&"false"!==e.getContentEditable(n);)"true"===e.getContentEditable(n)&&(r=n),n=n.parentNode;return n!==o?r:o},Xy=$y,Yy=function(e){return $y(e).fold(y.constant(""),function(e){return e.nodeName.toUpperCase()})},Gy=function(e){return $y(e).filter(function(e){return lo(Fn.fromDom(e))}).isSome()},Jy=function(e,t){return e&&e.parentNode&&e.parentNode.nodeName===t},Qy=function(e){return e&&/^(OL|UL|LI)$/.test(e.nodeName)},Zy=function(e){var t=e.parentNode;return/^(LI|DT|DD)$/.test(t.nodeName)?t:e},eb=function(e,t,n){for(var r=e[n?"firstChild":"lastChild"];r&&!So.isElement(r);)r=r[n?"nextSibling":"previousSibling"];return r===t},tb=function(e,t,n,r,o){var i=e.dom,a=e.selection.getRng();if(n!==e.getBody()){var u;Qy(u=n)&&Qy(u.parentNode)&&(o="LI");var s,c,l=o?t(o):i.create("BR");if(eb(n,r,!0)&&eb(n,r,!1))Jy(n,"LI")?i.insertAfter(l,Zy(n)):i.replace(l,n);else if(eb(n,r,!0))Jy(n,"LI")?(i.insertAfter(l,Zy(n)),l.appendChild(i.doc.createTextNode(" ")),l.appendChild(n)):n.parentNode.insertBefore(l,n);else if(eb(n,r,!1))i.insertAfter(l,Zy(n));else{n=Zy(n);var f=a.cloneRange();f.setStartAfter(r),f.setEndAfter(n);var d=f.extractContents();"LI"===o&&(c="LI",(s=d).firstChild&&s.firstChild.nodeName===c)?(l=d.firstChild,i.insertAfter(d,n)):(i.insertAfter(d,n),i.insertAfter(l,n))}i.remove(r),Wy(e,l)}},nb=function(e){e.innerHTML='<br data-mce-bogus="1">'},rb=function(e,t){return e.nodeName===t||e.previousSibling&&e.previousSibling.nodeName===t},ob=function(e,t){return t&&e.isBlock(t)&&!/^(TD|TH|CAPTION|FORM)$/.test(t.nodeName)&&!/^(fixed|absolute)/i.test(t.style.position)&&"true"!==e.getContentEditable(t)},ib=function(e,t,n){return!1===So.isText(t)?n:e?1===n&&t.data.charAt(n-1)===yi?0:n:n===t.data.length-1&&t.data.charAt(n)===yi?t.data.length:n},ab=function(e,t){var n,r,o=e.getRoot();for(n=t;n!==o&&"false"!==e.getContentEditable(n);)"true"===e.getContentEditable(n)&&(r=n),n=n.parentNode;return n!==o?r:o},ub=function(e,t){var n=zy(e);n&&n.toLowerCase()===t.tagName.toLowerCase()&&e.dom.setAttribs(t,Uy(e))},sb=function(e,t){var n,r,o,i,a,u,s,c,l,f,d,m,p,g,h,v,y,b,C=e.dom,x=e.schema,w=x.getNonEmptyElements(),N=e.selection.getRng(),E=function(t){var n,i,u,s=o,c=x.getTextInlineElements();if(t||"TABLE"===f||"HR"===f?(n=C.create(t||m),ub(e,n)):n=a.cloneNode(!1),u=n,!1===Hy(e))C.setAttrib(n,"style",null),C.setAttrib(n,"class",null);else do{if(c[s.nodeName]){if(Nf.isCaretNode(s))continue;i=s.cloneNode(!1),C.setAttrib(i,"id",""),n.hasChildNodes()?(i.appendChild(n.firstChild),n.appendChild(i)):(u=i,n.appendChild(i))}}while((s=s.parentNode)&&s!==r);return nb(u),n},S=function(e){var t,n,r,u;if(u=ib(e,o,i),So.isText(o)&&(e?u>0:u<o.nodeValue.length))return!1;if(o.parentNode===a&&p&&!e)return!0;if(e&&So.isElement(o)&&o===a.firstChild)return!0;if(rb(o,"TABLE")||rb(o,"HR"))return p&&!e||!p&&e;for(t=new Zr(o,a),So.isText(o)&&(e&&0===u?t.prev():e||u!==o.nodeValue.length||t.next());n=t.current();){if(So.isElement(n)){if(!n.getAttribute("data-mce-bogus")&&(r=n.nodeName.toLowerCase(),w[r]&&"br"!==r))return!1}else if(So.isText(n)&&!/^[ \t\r\n]*$/.test(n.nodeValue))return!1;e?t.prev():t.next()}return!0},k=function(){s=/^(H[1-6]|PRE|FIGURE)$/.test(f)&&"HGROUP"!==d?E(m):E(),jy(e)&&ob(C,l)&&C.isEmpty(a)?s=C.split(l,a):C.insertAfter(s,a),Wy(e,s)};Yd.normalize(C,N).each(function(e){N.setStart(e.startContainer,e.startOffset),N.setEnd(e.endContainer,e.endOffset)}),o=N.startContainer,i=N.startOffset,m=zy(e),u=t.shiftKey,So.isElement(o)&&o.hasChildNodes()&&(p=i>o.childNodes.length-1,o=o.childNodes[Math.min(i,o.childNodes.length-1)]||o,i=p&&So.isText(o)?o.nodeValue.length:0),(r=ab(C,o))&&((m&&!u||!m&&u)&&(o=function(e,t,n,r,o){var i,a,u,s,c,l,f,d=t||"P",m=e.dom,p=ab(m,r);if(!(a=m.getParent(r,m.isBlock))||!ob(m,a)){if(l=(a=a||p)===e.getBody()||(f=a)&&/^(TD|TH|CAPTION)$/.test(f.nodeName)?a.nodeName.toLowerCase():a.parentNode.nodeName.toLowerCase(),!a.hasChildNodes())return i=m.create(d),ub(e,i),a.appendChild(i),n.setStart(i,0),n.setEnd(i,0),i;for(s=r;s.parentNode!==a;)s=s.parentNode;for(;s&&!m.isBlock(s);)u=s,s=s.previousSibling;if(u&&e.schema.isValidChild(l,d.toLowerCase())){for(i=m.create(d),ub(e,i),u.parentNode.insertBefore(i,u),s=u;s&&!m.isBlock(s);)c=s.nextSibling,i.appendChild(s),s=c;n.setStart(r,o),n.setEnd(r,o)}}return r}(e,m,N,o,i)),a=C.getParent(o,C.isBlock),l=a?C.getParent(a.parentNode,C.isBlock):null,f=a?a.nodeName.toUpperCase():"","LI"!==(d=l?l.nodeName.toUpperCase():"")||t.ctrlKey||(a=l,l=l.parentNode,f=d),/^(LI|DT|DD)$/.test(f)&&C.isEmpty(a)?tb(e,E,l,a,m):m&&a===e.getBody()||(m=m||"P",wi(a)?(s=Bi(a),C.isEmpty(a)&&nb(a),Wy(e,s)):S()?k():S(!0)?(s=a.parentNode.insertBefore(E(),a),Wy(e,rb(a,"HR")?s:a)):((n=(y=N,b=y.cloneRange(),b.setStart(y.startContainer,ib(!0,y.startContainer,y.startOffset)),b.setEnd(y.endContainer,ib(!1,y.endContainer,y.endOffset)),b).cloneRange()).setEndAfter(a),function(e){for(;So.isText(e)&&(e.nodeValue=e.nodeValue.replace(/^[\r\n]+/,"")),e=e.firstChild;);}(c=n.extractContents()),s=c.firstChild,C.insertAfter(c,a),function(e,t,n){var r,o=n,i=[];if(o){for(;o=o.firstChild;){if(e.isBlock(o))return;So.isElement(o)&&!t[o.nodeName.toLowerCase()]&&i.push(o)}for(r=i.length;r--;)!(o=i[r]).hasChildNodes()||o.firstChild===o.lastChild&&""===o.firstChild.nodeValue?e.remove(o):(a=o)&&"A"===a.nodeName&&0===Dt.trim(bi(a.innerText||a.textContent)).length&&e.remove(o);var a}}(C,w,s),g=C,(h=a).normalize(),(v=h.lastChild)&&!/^(left|right)$/gi.test(g.getStyle(v,"float",!0))||g.add(h,"br"),C.isEmpty(a)&&nb(a),s.normalize(),C.isEmpty(s)?(C.remove(s),k()):Wy(e,s)),C.setAttrib(s,"id",""),e.fire("NewBlock",{newBlock:s})))},cb=function(e,t){return Xy(e).filter(function(e){return t.length>0&&Tr.is(Fn.fromDom(e),t)}).isSome()},lb=function(e){return cb(e,qy(e))},fb=function(e){return cb(e,Vy(e))},db=ll([{br:[]},{block:[]},{none:[]}]),mb=function(e,t){return fb(e)},pb=function(e){return function(t,n){return""===zy(t)===e}},gb=function(e){return function(t,n){return Gy(t)===e}},hb=function(e){return function(t,n){return"PRE"===Yy(t)===e}},vb=function(e){return function(t,n){return Fy(t)===e}},yb=function(e,t){return lb(e)},bb=function(e,t){return t},Cb=function(e){var t=zy(e),n=Ky(e.dom,e.selection.getStart());return n&&e.schema.isValidChild(n.nodeName,t||"P")},xb=function(e,t){return function(n,r){return M.foldl(e,function(e,t){return e&&t(n,r)},!0)?E.some(t):E.none()}},wb=function(e,t){return Ef([xb([mb],db.none()),xb([hb(!0),vb(!1),bb],db.br()),xb([hb(!0),vb(!1)],db.block()),xb([hb(!0),vb(!0),bb],db.block()),xb([hb(!0),vb(!0)],db.br()),xb([gb(!0),bb],db.br()),xb([gb(!0)],db.block()),xb([pb(!0),bb,Cb],db.block()),xb([pb(!0)],db.br()),xb([yb],db.br()),xb([pb(!1),bb],db.br()),xb([Cb],db.block())],[e,t.shiftKey]).getOr(db.none())},Nb=function(e,t){wb(e,t).fold(function(){im.insert(e,t)},function(){sb(e,t)},y.noop)},Eb=function(e){e.on("keydown",function(t){var n,r,o;t.keyCode===Cg.ENTER&&(n=e,(r=t).isDefaultPrevented()||(r.preventDefault(),(o=n.undoManager).typing&&(o.typing=!1,o.add()),n.undoManager.transact(function(){!1===n.selection.isCollapsed()&&n.execCommand("Delete"),Nb(n,r)})))})},Sb=function(e,t,n){return u=t,!(!kb(n)||!So.isText(u.container())||(r=e,i=(o=t).container(),a=o.offset(),i.insertData(a,"\xa0"),r.selection.setCursorLocation(i,a+1),0));var r,o,i,a,u},kb=function(e){return e.fold(y.constant(!1),y.constant(!0),y.constant(!0),y.constant(!1))},Tb=function(e){return!!e.selection.isCollapsed()&&(t=e,n=y.curry(_c.isInlineTarget,t),r=wa.fromRangeStart(t.selection.getRng()),Vf(n,t.getBody(),r).map(y.curry(Sb,t,r)).getOr(!1));var t,n,r},Ab=function(e){e.on("keydown",function(t){var n,r;!1===t.isDefaultPrevented()&&(n=e,r=t,Sy([{keyCode:Cg.SPACEBAR,action:Ey(Tb,n)}],r).each(function(e){r.preventDefault()}))})},_b=function(e){var t=td.setupSelectedState(e);ky(e,t),Ry(e,t),Eb(e),Ab(e)};function Rb(e){var t,n,r,o=Dt.each,i=Cg.BACKSPACE,a=Cg.DELETE,u=e.dom,s=e.selection,c=e.settings,l=e.parser,f=de.gecko,d=de.ie,m=de.webkit,p="data:text/mce-internal,",g=d?"Text":"URL",h=function(t,n){try{e.getDoc().execCommand(t,!1,n)}catch(r){}},v=function(e){return e.isDefaultPrevented()},y=function(){e.shortcuts.add("meta+a",null,"SelectAll")},b=function(){e.on("keydown",function(e){if(!v(e)&&e.keyCode===i&&s.isCollapsed()&&0===s.getRng().startOffset){var t=s.getNode().previousSibling;if(t&&t.nodeName&&"table"===t.nodeName.toLowerCase())return e.preventDefault(),!1}})},C=function(){e.inline||(e.contentStyles.push("body {min-height: 150px}"),e.on("click",function(t){var n;if("HTML"===t.target.nodeName){if(de.ie>11)return void e.getBody().focus();n=e.selection.getRng(),e.getBody().focus(),e.selection.setRng(n),e.selection.normalize(),e.nodeChanged()}}))};return e.on("keydown",function(t){var n,r,o,i,a;if(!v(t)&&t.keyCode===Cg.BACKSPACE&&(r=(n=s.getRng()).startContainer,o=n.startOffset,i=u.getRoot(),a=r,n.collapsed&&0===o)){for(;a&&a.parentNode&&a.parentNode.firstChild===a&&a.parentNode!==i;)a=a.parentNode;"BLOCKQUOTE"===a.tagName&&(e.formatter.toggle("blockquote",null,a),(n=u.createRng()).setStart(r,0),n.setEnd(r,0),s.setRng(n))}}),t=function(e){var t=u.create("body"),n=e.cloneContents();return t.appendChild(n),s.serializer.serialize(t,{format:"html"})},e.on("keydown",function(n){var r,o,s,c,l,f=n.keyCode;if(!v(n)&&(f===a||f===i)){if(r=e.selection.isCollapsed(),o=e.getBody(),r&&!u.isEmpty(o))return;if(!r&&(s=e.selection.getRng(),c=t(s),(l=u.createRng()).selectNode(e.getBody()),c!==t(l)))return;n.preventDefault(),e.setContent(""),o.firstChild&&u.isBlock(o.firstChild)?e.selection.setCursorLocation(o.firstChild,0):e.selection.setCursorLocation(o,0),e.nodeChanged()}}),de.windowsPhone||e.on("keyup focusin mouseup",function(e){Cg.modifierPressed(e)||s.normalize()},!0),m&&(e.settings.content_editable||u.bind(e.getDoc(),"mousedown mouseup",function(t){var n;if(t.target===e.getDoc().documentElement)if(n=s.getRng(),e.getBody().focus(),"mousedown"===t.type){if(Ei(n.startContainer))return;s.placeCaretAt(t.clientX,t.clientY)}else s.setRng(n)}),e.on("click",function(t){var n=t.target;/^(IMG|HR)$/.test(n.nodeName)&&"false"!==u.getContentEditableParent(n)&&(t.preventDefault(),e.selection.select(n),e.nodeChanged()),"A"===n.nodeName&&u.hasClass(n,"mce-item-anchor")&&(t.preventDefault(),s.select(n))}),c.forced_root_block&&e.on("init",function(){h("DefaultParagraphSeparator",c.forced_root_block)}),e.on("init",function(){e.dom.bind(e.getBody(),"submit",function(e){e.preventDefault()})}),b(),l.addNodeFilter("br",function(e){for(var t=e.length;t--;)"Apple-interchange-newline"===e[t].attr("class")&&e[t].remove()}),de.iOS?(e.inline||e.on("keydown",function(){document.activeElement===document.body&&e.getWin().focus()}),C(),e.on("click",function(e){var t=e.target;do{if("A"===t.tagName)return void e.preventDefault()}while(t=t.parentNode)}),e.contentStyles.push(".mce-content-body {-webkit-touch-callout: none}")):y()),de.ie>=11&&(C(),b()),de.ie&&(y(),h("AutoUrlDetect",!1),e.on("dragstart",function(t){var n,r,o;(n=t).dataTransfer&&(e.selection.isCollapsed()&&"IMG"===n.target.tagName&&s.select(n.target),(r=e.selection.getContent()).length>0&&(o=p+escape(e.id)+","+escape(r),n.dataTransfer.setData(g,o)))}),e.on("drop",function(t){if(!v(t)){var n=(a=t).dataTransfer&&(u=a.dataTransfer.getData(g))&&u.indexOf(p)>=0?(u=u.substr(p.length).split(","),{id:unescape(u[0]),html:unescape(u[1])}):null;if(n&&n.id!==e.id){t.preventDefault();var r=Rv.fromPoint(t.x,t.y,e.getDoc());s.setRng(r),o=n.html,i=!0,e.queryCommandSupported("mceInsertClipboardContent")?e.execCommand("mceInsertClipboardContent",!1,{content:o,internal:i}):e.execCommand("mceInsertContent",!1,o)}}var o,i,a,u})),f&&(e.on("keydown",function(t){if(!v(t)&&t.keyCode===i){if(!e.getBody().getElementsByTagName("hr").length)return;if(s.isCollapsed()&&0===s.getRng().startOffset){var n=s.getNode(),r=n.previousSibling;if("HR"===n.nodeName)return u.remove(n),void t.preventDefault();r&&r.nodeName&&"hr"===r.nodeName.toLowerCase()&&(u.remove(r),t.preventDefault())}}}),Range.prototype.getClientRects||e.on("mousedown",function(t){if(!v(t)&&"HTML"===t.target.nodeName){var n=e.getBody();n.blur(),ve.setEditorTimeout(e,function(){n.focus()})}}),n=function(){var t=u.getAttribs(s.getStart().cloneNode(!1));return function(){var n=s.getStart();n!==e.getBody()&&(u.setAttrib(n,"style",null),o(t,function(e){n.setAttributeNode(e.cloneNode(!0))}))}},r=function(){return!s.isCollapsed()&&u.getParent(s.getStart(),u.isBlock)!==u.getParent(s.getEnd(),u.isBlock)},e.on("keypress",function(t){var o;if(!v(t)&&(8===t.keyCode||46===t.keyCode)&&r())return o=n(),e.getDoc().execCommand("delete",!1,null),o(),t.preventDefault(),!1}),u.bind(e.getDoc(),"cut",function(t){var o;!v(t)&&r()&&(o=n(),ve.setEditorTimeout(e,function(){o()}))}),c.readonly||e.on("BeforeExecCommand MouseDown",function(){h("StyleWithCSS",!1),h("enableInlineTableEditing",!1),c.object_resizing||h("enableObjectResizing",!1)}),e.on("SetContent ExecCommand",function(e){"setcontent"!==e.type&&"mceInsertLink"!==e.command||o(u.select("a"),function(e){var t=e.parentNode,n=u.getRoot();if(t.lastChild===e){for(;t&&!u.isBlock(t);){if(t.parentNode.lastChild!==t||t===n)return;t=t.parentNode}u.add(t,"br",{"data-mce-bogus":1})}})}),e.contentStyles.push("img:-moz-broken {-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}"),de.mac&&e.on("keydown",function(t){!Cg.metaKeyPressed(t)||t.shiftKey||37!==t.keyCode&&39!==t.keyCode||(t.preventDefault(),e.selection.getSel().modify("move",37===t.keyCode?"backward":"forward","lineboundary"))}),b()),{refreshContentEditable:function(){},isHidden:function(){var t;return!f||e.removed?0:!(t=e.selection.getSel())||!t.rangeCount||0===t.rangeCount}}}var Bb=ui.DOM,Db=function(e){var t;e.bindPendingEventDelegates(),e.initialized=!0,e.fire("init"),e.focus(!0),e.nodeChanged({initial:!0}),e.execCallback("init_instance_callback",e),(t=e).settings.auto_focus&&ve.setEditorTimeout(t,function(){var e;(e=!0===t.settings.auto_focus?t:t.editorManager.get(t.settings.auto_focus)).destroyed||e.focus()},100)},Ob=function(e,t){var n,r,o,i,a,u,s,c,l,f=e.settings,d=e.getElement(),m=e.getDoc();f.inline||(e.getElement().style.visibility=e.orgVisibility),t||f.content_editable||(m.open(),m.write(e.iframeHTML),m.close()),f.content_editable&&(e.on("remove",function(){var e=this.getBody();Bb.removeClass(e,"mce-content-body"),Bb.removeClass(e,"mce-edit-focus"),Bb.setAttrib(e,"contentEditable",null)}),Bb.addClass(d,"mce-content-body"),e.contentDocument=m=f.content_document||document,e.contentWindow=f.content_window||window,e.bodyElement=d,f.content_document=f.content_window=null,f.root_name=d.nodeName.toLowerCase()),(n=e.getBody()).disabled=!0,e.readonly=f.readonly,e.readonly||(e.inline&&"static"===Bb.getStyle(n,"position",!0)&&(n.style.position="relative"),n.contentEditable=e.getParam("content_editable_state",!0)),n.disabled=!1,e.editorUpload=zp(e),e.schema=Go(f),e.dom=new ui(m,{keep_values:!0,url_converter:e.convertURL,url_converter_scope:e,hex_colors:f.force_hex_style_colors,class_filter:f.class_filter,update_styles:!0,root_element:e.inline?e.getBody():null,collect:f.content_editable,schema:e.schema,onSetAttrib:function(t){e.fire("SetAttrib",t)}}),e.parser=((i=mv((o=e).settings,o.schema)).addAttributeFilter("src,href,style,tabindex",function(e,t){for(var n,r,i,a=e.length,u=o.dom;a--;)if(r=(n=e[a]).attr(t),i="data-mce-"+t,!n.attributes.map[i]){if(0===r.indexOf("data:")||0===r.indexOf("blob:"))continue;"style"===t?((r=u.serializeStyle(u.parseStyle(r),n.name)).length||(r=null),n.attr(i,r),n.attr(t,r)):"tabindex"===t?(n.attr(i,r),n.attr(t,null)):n.attr(i,o.convertURL(r,t,n.name))}}),i.addNodeFilter("script",function(e){for(var t,n,r=e.length;r--;)0!==(n=(t=e[r]).attr("type")||"no/type").indexOf("mce-")&&t.attr("type","mce-"+n)}),i.addNodeFilter("#cdata",function(e){for(var t,n=e.length;n--;)(t=e[n]).type=8,t.name="#comment",t.value="[CDATA["+t.value+"]]"}),i.addNodeFilter("p,h1,h2,h3,h4,h5,h6,div",function(e){for(var t,n=e.length,r=o.schema.getNonEmptyElements();n--;)(t=e[n]).isEmpty(r)&&0===t.getAll("br").length&&(t.append(new nv("br",1)).shortEnded=!0)}),i),e.serializer=yv(f,e),e.selection=dy(e.dom,e.getWin(),e.serializer,e),e.formatter=Uh(e),e.undoManager=Kg(e),e._nodeChangeDispatcher=new jp(e),e._selectionOverrides=Sg(e),xv(e),_b(e),Hp(e),e.fire("PreInit"),f.browser_spellcheck||f.gecko_spellcheck||(m.body.spellcheck=!1,Bb.setAttrib(n,"spellcheck","false")),e.quirks=Rb(e),e.fire("PostRender"),f.directionality&&(n.dir=f.directionality),f.nowrap&&(n.style.whiteSpace="nowrap"),f.protect&&e.on("BeforeSetContent",function(e){Dt.each(f.protect,function(t){e.content=e.content.replace(t,function(e){return"\x3c!--mce:protected "+escape(e)+"--\x3e"})})}),e.on("SetContent",function(){e.addVisual(e.getBody())}),f.padd_empty_editor&&e.on("PostProcess",function(e){e.content=e.content.replace(/^(<p[^>]*>(&nbsp;|&#160;|\s|\u00a0|<br \/>|)<\/p>[\r\n]*|<br \/>[\r\n]*)$/,"")}),e.load({initial:!0,format:"html"}),e.startContent=e.getContent({format:"raw"}),e.on("compositionstart compositionend",function(t){e.composing="compositionstart"===t.type}),e.contentStyles.length>0&&(r="",Dt.each(e.contentStyles,function(e){r+=e+"\r\n"}),e.dom.addStyle(r)),(a=e,a.inline?Bb.styleSheetLoader:a.dom.styleSheetLoader).loadAll(e.contentCSS,function(t){Db(e)},function(t){Db(e)}),f.content_style&&(u=e,s=f.content_style,c=Fn.fromDom(u.getDoc().head),l=Fn.fromTag("style"),sr.set(l,"type","text/css"),Ks.append(l,Fn.fromText(s)),Ks.append(c,l))},Pb=ui.DOM,Lb=function(e,t){var n,r,o,i,a,u,s,c=e.editorManager.translate("Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help"),l=(n=e.id,r=c,o=t.height,i=Dy(e),s=Fn.fromTag("iframe"),sr.setAll(s,i),sr.setAll(s,{id:n+"_ifr",frameBorder:"0",allowTransparency:"true",title:r}),gr(s,{width:"100%",height:(a=o,u="number"==typeof a?a+"px":a,u||""),display:"block"}),s).dom();l.onload=function(){l.onload=null,e.fire("load")};var f,d,m,p,g=function(e,t){if(document.domain!==window.location.hostname&&de.ie&&de.ie<12){var n=Fp.uuid("mce");e[n]=function(){Ob(e)};var r='javascript:(function(){document.open();document.domain="'+document.domain+'";var ed = window.parent.tinymce.get("'+e.id+'");document.write(ed.iframeHTML);document.close();ed.'+n+"(true);})()";return Pb.setAttrib(t,"src",r),!0}return!1}(e,l);return e.contentAreaContainer=t.iframeContainer,e.iframeElement=l,e.iframeHTML=(p=Oy(f=e)+"<html><head>",Py(f)!==f.documentBaseUrl&&(p+='<base href="'+f.documentBaseURI.getURI()+'" />'),p+='<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />',d=Ly(f),m=Iy(f),My(f)&&(p+='<meta http-equiv="Content-Security-Policy" content="'+My(f)+'" />'),p+='</head><body id="'+d+'" class="mce-content-body '+m+'" data-id="'+f.id+'"><br></body></html>'),Pb.add(t.iframeContainer,l),g},Ib=function(e,t){var n=Lb(e,t);t.editorContainer&&(Pb.get(t.editorContainer).style.display=e.orgDisplay,e.hidden=Pb.isHidden(t.editorContainer)),e.getElement().style.display="none",Pb.setAttrib(e.id,"aria-hidden",!0),n||Ob(e)},Mb=ui.DOM,Fb=function(e,t,n){var r,o,i=wp.get(n);if(r=wp.urls[n]||e.documentBaseUrl.replace(/\/$/,""),n=Dt.trim(n),i&&-1===Dt.inArray(t,n)){if(Dt.each(wp.dependencies(n),function(n){Fb(e,t,n)}),e.plugins[n])return;o=new i(e,r,e.$),e.plugins[n]=o,o.init&&(o.init(e,r),t.push(n))}},zb=function(e){return e.replace(/^\-/,"")},Ub=function(e){return{editorContainer:e,iframeContainer:e}},qb=function(e){var t,n,r=e.getElement();return e.inline?Ub(null):(t=r,n=Mb.create("div"),Mb.insertAfter(n,t),Ub(n))},Vb=function(e){var t,n,r,o,i,a,u,s,c,l,f,d=e.settings,m=e.getElement();return e.orgDisplay=m.style.display,Jn.isString(d.theme)?(l=(o=e).settings,f=o.getElement(),i=l.width||Mb.getStyle(f,"width")||"100%",a=l.height||Mb.getStyle(f,"height")||f.offsetHeight,u=l.min_height||100,(s=/^[0-9\.]+(|px)$/i).test(""+i)&&(i=Math.max(parseInt(i,10),100)),s.test(""+a)&&(a=Math.max(parseInt(a,10),u)),c=o.theme.renderUI({targetNode:f,width:i,height:a,deltaWidth:l.delta_width,deltaHeight:l.delta_height}),l.content_editable||(a=(c.iframeHeight||a)+("number"==typeof a?c.deltaHeight||0:""))<u&&(a=u),c.height=a,c):Jn.isFunction(d.theme)?(r=(t=e).getElement(),(n=t.settings.theme(t,r)).editorContainer.nodeType&&(n.editorContainer.id=n.editorContainer.id||t.id+"_parent"),n.iframeContainer&&n.iframeContainer.nodeType&&(n.iframeContainer.id=n.iframeContainer.id||t.id+"_iframecontainer"),n.height=n.iframeHeight?n.iframeHeight:r.offsetHeight,n):qb(e)},Hb=function(e){var t,n,r,o,i,a,u=e.settings,s=e.getElement();return e.rtl=u.rtl_ui||e.editorManager.i18n.rtl,e.editorManager.i18n.setCode(u.language),u.aria_label=u.aria_label||Mb.getAttrib(s,"aria-label",e.getLang("aria.rich_text_area")),e.fire("ScriptsLoaded"),o=(n=e).settings.theme,Jn.isString(o)?(n.settings.theme=zb(o),r=Np.get(o),n.theme=new r(n,Np.urls[o]),n.theme.init&&n.theme.init(n,Np.urls[o]||n.documentBaseUrl.replace(/\/$/,""),n.$)):n.theme={},i=e,a=[],Dt.each(i.settings.plugins.split(/[ ,]/),function(e){Fb(i,a,zb(e))}),t=Vb(e),e.editorContainer=t.editorContainer?t.editorContainer:null,u.content_css&&Dt.each(Dt.explode(u.content_css),function(t){e.contentCSS.push(e.documentBaseURI.toAbsolute(t))}),u.content_editable?Ob(e):Ib(e,t)},jb=ui.DOM,$b=function(e){return"-"===e.charAt(0)},Wb=function(e,t){var n=di.ScriptLoader;!function(e,t,n,r){var o=t.settings,i=o.theme;if(Jn.isString(i)){if(!$b(i)&&!Np.urls.hasOwnProperty(i)){var a=o.theme_url;a?Np.load(i,t.documentBaseURI.toAbsolute(a)):Np.load(i,"themes/"+i+"/theme"+n+".js")}e.loadQueue(function(){Np.waitFor(i,r)})}else r()}(n,e,t,function(){var r,o,i,a,u;r=n,(i=(o=e).settings).language&&"en"!==i.language&&!i.language_url&&(i.language_url=o.editorManager.baseURL+"/langs/"+i.language+".js"),i.language_url&&!o.editorManager.i18n.data[i.language]&&r.add(i.language_url),a=e.settings,u=t,Dt.isArray(a.plugins)&&(a.plugins=a.plugins.join(" ")),Dt.each(a.external_plugins,function(e,t){wp.load(t,e),a.plugins+=" "+t}),Dt.each(a.plugins.split(/[ ,]/),function(e){if((e=Dt.trim(e))&&!wp.urls[e])if($b(e)){e=e.substr(1,e.length);var t=wp.dependencies(e);Dt.each(t,function(e){var t={prefix:"plugins/",resource:e,suffix:"/plugin"+u+".js"};e=wp.createUrl(t,e),wp.load(e.resource,e)})}else wp.load(e,{prefix:"plugins/",resource:e,suffix:"/plugin"+u+".js"})}),n.loadQueue(function(){e.removed||Hb(e)},e,function(t){xp.pluginLoadError(e,t[0]),e.removed||Hb(e)})})},Kb=function(e){var t=e.settings,n=e.id,r=function(){jb.unbind(window,"ready",r),e.render()};if(ke.Event.domLoaded){if(e.getElement()&&de.contentEditable){t.inline?e.inline=!0:(e.orgVisibility=e.getElement().style.visibility,e.getElement().style.visibility="hidden");var o=e.getElement().form||jb.getParent(n,"form");o&&(e.formElement=o,t.hidden_input&&!/TEXTAREA|INPUT/i.test(e.getElement().nodeName)&&(jb.insertAfter(jb.create("input",{type:"hidden",name:n}),n),e.hasHiddenInput=!0),e.formEventDelegate=function(t){e.fire(t.type,t)},jb.bind(o,"submit reset",e.formEventDelegate),e.on("reset",function(){e.setContent(e.startContent,{format:"raw"})}),!t.submit_patch||o.submit.nodeType||o.submit.length||o._mceOldSubmit||(o._mceOldSubmit=o.submit,o.submit=function(){return e.editorManager.triggerSave(),e.setDirty(!1),o._mceOldSubmit(o)})),e.windowManager=hp(e),e.notificationManager=gp(e),"xml"===t.encoding&&e.on("GetContent",function(e){e.save&&(e.content=jb.encode(e.content))}),t.add_form_submit_trigger&&e.on("submit",function(){e.initialized&&e.save()}),t.add_unload_trigger&&(e._beforeUnload=function(){!e.initialized||e.destroyed||e.isHidden()||e.save({format:"raw",no_events:!0,set_dirty:!1})},e.editorManager.on("BeforeUnload",e._beforeUnload)),e.editorManager.add(e),Wb(e,e.suffix)}}else jb.bind(window,"ready",r)},Xb=function(e,t,n){var r=e.sidebars?e.sidebars:[];r.push({name:t,settings:n}),e.sidebars=r},Yb=Dt.each,Gb=Dt.trim,Jb="source protocol authority userInfo user password host port relative path directory file query anchor".split(" "),Qb={ftp:21,http:80,https:443,mailto:25},Zb=function(e,t){var n,r,o=this;if(e=Gb(e),n=(t=o.settings=t||{}).base_uri,/^([\w\-]+):([^\/]{2})/i.test(e)||/^\s*#/.test(e))o.source=e;else{var i=0===e.indexOf("//");0!==e.indexOf("/")||i||(e=(n&&n.protocol||"http")+"://mce_host"+e),/^[\w\-]*:?\/\//.test(e)||(r=t.base_uri?t.base_uri.path:new Zb(document.location.href).directory,""==t.base_uri.protocol?e="//mce_host"+o.toAbsPath(r,e):(e=/([^#?]*)([#?]?.*)/.exec(e),e=(n&&n.protocol||"http")+"://mce_host"+o.toAbsPath(r,e[1])+e[2])),e=e.replace(/@@/g,"(mce_at)"),e=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(e),Yb(Jb,function(t,n){var r=e[n];r&&(r=r.replace(/\(mce_at\)/g,"@@")),o[t]=r}),n&&(o.protocol||(o.protocol=n.protocol),o.userInfo||(o.userInfo=n.userInfo),o.port||"mce_host"!==o.host||(o.port=n.port),o.host&&"mce_host"!==o.host||(o.host=n.host),o.source=""),i&&(o.protocol="")}};Zb.prototype={setPath:function(e){e=/^(.*?)\/?(\w+)?$/.exec(e),this.path=e[0],this.directory=e[1],this.file=e[2],this.source="",this.getURI()},toRelative:function(e){var t;if("./"===e)return e;if("mce_host"!==(e=new Zb(e,{base_uri:this})).host&&this.host!==e.host&&e.host||this.port!==e.port||this.protocol!==e.protocol&&""!==e.protocol)return e.getURI();var n=this.getURI(),r=e.getURI();return n===r||"/"===n.charAt(n.length-1)&&n.substr(0,n.length-1)===r?n:(t=this.toRelPath(this.path,e.path),e.query&&(t+="?"+e.query),e.anchor&&(t+="#"+e.anchor),t)},toAbsolute:function(e,t){return(e=new Zb(e,{base_uri:this})).getURI(t&&this.isSameOrigin(e))},isSameOrigin:function(e){if(this.host==e.host&&this.protocol==e.protocol){if(this.port==e.port)return!0;var t=Qb[this.protocol];if(t&&(this.port||t)==(e.port||t))return!0}return!1},toRelPath:function(e,t){var n,r,o,i=0,a="";if(e=(e=e.substring(0,e.lastIndexOf("/"))).split("/"),n=t.split("/"),e.length>=n.length)for(r=0,o=e.length;r<o;r++)if(r>=n.length||e[r]!==n[r]){i=r+1;break}if(e.length<n.length)for(r=0,o=n.length;r<o;r++)if(r>=e.length||e[r]!==n[r]){i=r+1;break}if(1===i)return t;for(r=0,o=e.length-(i-1);r<o;r++)a+="../";for(r=i-1,o=n.length;r<o;r++)a+=r!==i-1?"/"+n[r]:n[r];return a},toAbsPath:function(e,t){var n,r,o,i=0,a=[];for(r=/\/$/.test(t)?"/":"",e=e.split("/"),t=t.split("/"),Yb(e,function(e){e&&a.push(e)}),e=a,n=t.length-1,a=[];n>=0;n--)0!==t[n].length&&"."!==t[n]&&(".."!==t[n]?i>0?i--:a.push(t[n]):i++);return 0!==(o=(n=e.length-i)<=0?a.reverse().join("/"):e.slice(0,n).join("/")+"/"+a.reverse().join("/")).indexOf("/")&&(o="/"+o),r&&o.lastIndexOf("/")!==o.length-1&&(o+=r),o},getURI:function(e){var t,n=this;return n.source&&!e||(t="",e||(n.protocol?t+=n.protocol+"://":t+="//",n.userInfo&&(t+=n.userInfo+"@"),n.host&&(t+=n.host),n.port&&(t+=":"+n.port)),n.path&&(t+=n.path),n.query&&(t+="?"+n.query),n.anchor&&(t+="#"+n.anchor),n.source=t),n.source}},Zb.parseDataUri=function(e){var t,n;return e=decodeURIComponent(e).split(","),(n=/data:([^;]+)/.exec(e[0]))&&(t=n[1]),{type:t,data:e[1]}},Zb.getDocumentBaseUrl=function(e){var t;return t=0!==e.protocol.indexOf("http")&&"file:"!==e.protocol?e.href:e.protocol+"//"+e.host+e.pathname,/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/")),t};var eC=function(e,t){t(e),e.firstChild&&eC(e.firstChild,t),e.next&&eC(e.next,t)},tC=function(e,t,n){var r=function(e,t,n){var r={},o={},i=[];for(var a in n.firstChild&&eC(n.firstChild,function(n){M.each(e,function(e){e.name===n.name&&(r[e.name]?r[e.name].nodes.push(n):r[e.name]={filter:e,nodes:[n]})}),M.each(t,function(e){"string"==typeof n.attr(e.name)&&(o[e.name]?o[e.name].nodes.push(n):o[e.name]={filter:e,nodes:[n]})})}),r)r.hasOwnProperty(a)&&i.push(r[a]);for(var u in o)o.hasOwnProperty(u)&&i.push(o[u]);return i}(e,t,n);M.each(r,function(e){M.each(e.filter.callbacks,function(t){t(e.nodes,e.filter.name,{})})})},nC=function(e){return e instanceof nv},rC=function(e,t,n){return void 0===n&&(n={}),n.format=n.format?n.format:"html",n.set=!0,n.content=nC(t)?"":t,nC(t)||n.no_events||(e.fire("BeforeSetContent",n),t=n.content),nC(t)?function(e,t,n){tC(e.parser.getNodeFilters(),e.parser.getAttributeFilters(),t);var r=ic({validate:e.validate},e.schema).serialize(t);return n.content=Dt.trim(r),e.dom.setHTML(e.getBody(),n.content),n.no_events||e.fire("SetContent",n),t}(e,t,n):(o=t,i=n,s=(r=e).getBody(),0===o.length||/^\s+$/.test(o)?(u='<br data-mce-bogus="1">',"TABLE"===s.nodeName?o="<tr><td>"+u+"</td></tr>":/^(UL|OL)$/.test(s.nodeName)&&(o="<li>"+u+"</li>"),(a=r.settings.forced_root_block)&&r.schema.isValidChild(s.nodeName.toLowerCase(),a.toLowerCase())?(o=u,o=r.dom.createHTML(a,r.settings.forced_root_block_attrs,o)):o||(o='<br data-mce-bogus="1">'),r.dom.setHTML(s,o),r.fire("SetContent",i)):("raw"!==i.format&&(o=ic({validate:r.validate},r.schema).serialize(r.parser.parse(o,{isRootContent:!0,insert:!0}))),i.content=Dt.trim(o),r.dom.setHTML(s,i.content),i.no_events||r.fire("SetContent",i)),i.content);var r,o,i,a,u,s},oC=ui.DOM,iC=function(e){return E.from(e).each(function(e){return e.destroy()})},aC=function(e){if(!e.removed){var t=e._selectionOverrides,n=e.editorUpload,r=e.getBody(),o=e.getElement();r&&e.save(),e.removed=1,e.unbindAllNativeEvents(),e.hasHiddenInput&&o&&oC.remove(o.nextSibling),!e.inline&&r&&(i=e,oC.setStyle(i.id,"display",i.orgDisplay)),Kh(e),e.editorManager.remove(e),oC.remove(e.getContainer()),iC(t),iC(n),e.destroy()}var i},uC=function(e,t){var n,r,o,i=e.selection,a=e.dom;e.destroyed||(t||e.removed?(t||(e.editorManager.off("beforeunload",e._beforeUnload),e.theme&&e.theme.destroy&&e.theme.destroy(),iC(i),iC(a)),(r=(n=e).formElement)&&(r._mceOldSubmit&&(r.submit=r._mceOldSubmit,r._mceOldSubmit=null),oC.unbind(r,"submit reset",n.formEventDelegate)),(o=e).contentAreaContainer=o.formElement=o.container=o.editorContainer=null,o.bodyElement=o.contentDocument=o.contentWindow=null,o.iframeElement=o.targetElm=null,o.selection&&(o.selection=o.selection.win=o.selection.dom=o.selection.dom.doc=null),e.destroyed=1):e.remove())},sC=ui.DOM,cC=Dt.extend,lC=Dt.each,fC=Dt.resolve,dC=de.ie,mC=function(e,t,n){var r,o,i,a,u,s,c,l,f,d=this;r=d.documentBaseUrl=n.documentBaseURL,o=n.baseURI,i=d,a=e,u=r,s=n.defaultSettings,c=t,f={id:a,theme:"modern",delta_width:0,delta_height:0,popup_css:"",plugins:"",document_base_url:u,add_form_submit_trigger:!0,submit_patch:!0,add_unload_trigger:!0,convert_urls:!0,relative_urls:!0,remove_script_host:!0,object_resizing:!0,doctype:"<!DOCTYPE html>",visual:!0,font_size_style_values:"xx-small,x-small,small,medium,large,x-large,xx-large",font_size_legacy_values:"xx-small,small,medium,large,x-large,xx-large,300%",forced_root_block:"p",hidden_input:!0,padd_empty_editor:!0,render_ui:!0,indentation:"30px",inline_styles:!0,convert_fonts_to_spans:!0,indent:"simple",indent_before:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,article,hgroup,aside,figure,figcaption,option,optgroup,datalist",indent_after:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,article,hgroup,aside,figure,figcaption,option,optgroup,datalist",entity_encoding:"named",url_converter:(l=i).convertURL,url_converter_scope:l,ie7_compat:!0},t=Nc(vc,f,s,c),d.settings=t,pi.language=t.language||"en",pi.languageLoad=t.language_load,pi.baseURL=n.baseURL,d.id=e,d.setDirty(!1),d.plugins={},d.documentBaseURI=new Zb(t.document_base_url,{base_uri:o}),d.baseURI=o,d.contentCSS=[],d.contentStyles=[],d.shortcuts=new Gm(d),d.loadedCSS={},d.editorCommands=new Dm(d),d.suffix=n.suffix,d.editorManager=n,d.inline=t.inline,d.buttons={},d.menuItems={},t.cache_suffix&&(de.cacheSuffix=t.cache_suffix.replace(/^[\?\&]+/,"")),!1===t.override_viewport&&(de.overrideViewPort=!1),n.fire("SetupEditor",{editor:d}),d.execCallback("setup",d),d.$=Jt.overrideDefaults(function(){return{context:d.inline?d.getBody():d.getDoc(),element:d.getBody()}})};cC(mC.prototype={render:function(){Kb(this)},focus:function(e){ap(this,e)},execCallback:function(e){var t,n=this.settings[e];if(n)return this.callbackLookup&&(t=this.callbackLookup[e])&&(n=t.func,t=t.scope),"string"==typeof n&&(t=(t=n.replace(/\.\w+$/,""))?fC(t):0,n=fC(n),this.callbackLookup=this.callbackLookup||{},this.callbackLookup[e]={func:n,scope:t}),n.apply(t||this,Array.prototype.slice.call(arguments,1))},translate:function(e){if(e&&Dt.is(e,"string")){var t=this.settings.language||"en",n=this.editorManager.i18n;e=n.data[t+"."+e]||e.replace(/\{\#([^\}]+)\}/g,function(e,r){return n.data[t+"."+r]||"{#"+r+"}"})}return this.editorManager.translate(e)},getLang:function(e,t){return this.editorManager.i18n.data[(this.settings.language||"en")+"."+e]||(t!==undefined?t:"{#"+e+"}")},getParam:function(e,t,n){return kc(this,e,t,n)},nodeChanged:function(e){this._nodeChangeDispatcher.nodeChanged(e)},addButton:function(e,t){var n=this;t.cmd&&(t.onclick=function(){n.execCommand(t.cmd)}),t.stateSelector&&"undefined"==typeof t.active&&(t.active=!1),t.text||t.icon||(t.icon=e),n.buttons=n.buttons,t.tooltip=t.tooltip||t.title,n.buttons[e]=t},addSidebar:function(e,t){return Xb(this,e,t)},addMenuItem:function(e,t){var n=this;t.cmd&&(t.onclick=function(){n.execCommand(t.cmd)}),n.menuItems=n.menuItems,n.menuItems[e]=t},addContextToolbar:function(e,t){var n,r=this;r.contextToolbars=r.contextToolbars||[],"string"==typeof e&&(n=e,e=function(e){return r.dom.is(e,n)}),r.contextToolbars.push({id:Fp.uuid("mcet"),predicate:e,items:t})},addCommand:function(e,t,n){this.editorCommands.addCommand(e,t,n)},addQueryStateHandler:function(e,t,n){this.editorCommands.addQueryStateHandler(e,t,n)},addQueryValueHandler:function(e,t,n){this.editorCommands.addQueryValueHandler(e,t,n)},addShortcut:function(e,t,n,r){this.shortcuts.add(e,t,n,r)},execCommand:function(e,t,n,r){return this.editorCommands.execCommand(e,t,n,r)},queryCommandState:function(e){return this.editorCommands.queryCommandState(e)},queryCommandValue:function(e){return this.editorCommands.queryCommandValue(e)},queryCommandSupported:function(e){return this.editorCommands.queryCommandSupported(e)},show:function(){this.hidden&&(this.hidden=!1,this.inline?this.getBody().contentEditable=!0:(sC.show(this.getContainer()),sC.hide(this.id)),this.load(),this.fire("show"))},hide:function(){var e=this,t=e.getDoc();e.hidden||(dC&&t&&!e.inline&&t.execCommand("SelectAll"),e.save(),e.inline?(e.getBody().contentEditable=!1,e===e.editorManager.focusedEditor&&(e.editorManager.focusedEditor=null)):(sC.hide(e.getContainer()),sC.setStyle(e.id,"display",e.orgDisplay)),e.hidden=!0,e.fire("hide"))},isHidden:function(){return!!this.hidden},setProgressState:function(e,t){this.fire("ProgressState",{state:e,time:t})},load:function(e){var t,n=this.getElement();return this.removed?"":n?((e=e||{}).load=!0,t=this.setContent(n.value!==undefined?n.value:n.innerHTML,e),e.element=n,e.no_events||this.fire("LoadContent",e),e.element=n=null,t):void 0},save:function(e){var t,n,r=this,o=r.getElement();if(o&&r.initialized&&!r.removed)return(e=e||{}).save=!0,e.element=o,e.content=r.getContent(e),e.no_events||r.fire("SaveContent",e),"raw"===e.format&&r.fire("RawSaveContent",e),t=e.content,/TEXTAREA|INPUT/i.test(o.nodeName)?o.value=t:(r.inline||(o.innerHTML=t),(n=sC.getParent(r.id,"form"))&&lC(n.elements,function(e){if(e.name===r.id)return e.value=t,!1})),e.element=o=null,!1!==e.set_dirty&&r.setDirty(!1),t},setContent:function(e,t){return rC(this,e,t)},getContent:function(e){return function(e,t){var n;void 0===t&&(t={});var r=e.getBody();if(e.removed)return"";if(t.format=t.format?t.format:"html",t.get=!0,t.getInner=!0,t.no_events||e.fire("BeforeGetContent",t),"raw"===t.format)n=Dt.trim(Og(e.serializer,r.innerHTML));else if("text"===t.format)n=r.innerText||r.textContent;else{if("tree"===t.format)return e.serializer.serialize(r,t);n=e.serializer.serialize(r,t)}return"text"!==t.format?t.content=Dt.trim(n):t.content=n,t.no_events||e.fire("GetContent",t),t.content}(this,e)},insertContent:function(e,t){t&&(e=cC({content:e},t)),this.execCommand("mceInsertContent",!1,e)},isDirty:function(){return!this.isNotDirty},setDirty:function(e){var t=!this.isNotDirty;this.isNotDirty=!e,e&&e!==t&&this.fire("dirty")},setMode:function(e){$m(this,e)},getContainer:function(){return this.container||(this.container=sC.get(this.editorContainer||this.id+"_parent")),this.container},getContentAreaContainer:function(){return this.contentAreaContainer},getElement:function(){return this.targetElm||(this.targetElm=sC.get(this.id)),this.targetElm},getWin:function(){var e;return this.contentWindow||(e=this.iframeElement)&&(this.contentWindow=e.contentWindow),this.contentWindow},getDoc:function(){var e;return this.contentDocument||(e=this.getWin())&&(this.contentDocument=e.document),this.contentDocument},getBody:function(){var e=this.getDoc();return this.bodyElement||(e?e.body:null)},convertURL:function(e,t,n){var r=this.settings;return r.urlconverter_callback?this.execCallback("urlconverter_callback",e,n,!0,t):!r.convert_urls||n&&"LINK"===n.nodeName||0===e.indexOf("file:")||0===e.length?e:r.relative_urls?this.documentBaseURI.toRelative(e):e=this.documentBaseURI.toAbsolute(e,r.remove_script_host)},addVisual:function(e){var t,n=this,r=n.settings,o=n.dom;e=e||n.getBody(),n.hasVisual===undefined&&(n.hasVisual=r.visual),lC(o.select("table,a",e),function(e){var i;switch(e.nodeName){case"TABLE":return t=r.visual_table_class||"mce-item-table",void((i=o.getAttrib(e,"border"))&&"0"!==i||!n.hasVisual?o.removeClass(e,t):o.addClass(e,t));case"A":return void(o.getAttrib(e,"href",!1)||(i=o.getAttrib(e,"name")||e.id,t=r.visual_anchor_class||"mce-item-anchor",i&&n.hasVisual?o.addClass(e,t):o.removeClass(e,t)))}}),n.fire("VisualAid",{element:e,hasVisual:n.hasVisual})},remove:function(){aC(this)},destroy:function(e){uC(this,e)},uploadImages:function(e){return this.editorUpload.uploadImages(e)},_scanForImages:function(){return this.editorUpload.scanForImages()}},Vm);var pC,gC,hC,vC={isEditorUIElement:function(e){return-1!==e.className.toString().indexOf("mce-")}},yC=function(e,t){var n,r,o=In.detect().browser;o.isIE()||o.isEdge()?(r=e).on("focusout",function(){Nm.store(r)}):(n=t,e.on("mouseup touchend",function(e){n.throttle()})),e.on("keyup nodechange",function(t){var n;"nodechange"===(n=t).type&&n.selectionChange||Nm.store(e)})},bC=function(e){var t,n,r,o=yg(function(){Nm.store(e)},0);e.inline&&(t=e,n=o,r=function(){n.throttle()},ui.DOM.bind(document,"mouseup",r),t.on("remove",function(){ui.DOM.unbind(document,"mouseup",r)})),e.on("init",function(){yC(e,o)}),e.on("remove",function(){o.cancel()})},CC=ui.DOM,xC=function(e){return vC.isEditorUIElement(e)},wC=function(e,t){var n=e?e.settings.custom_ui_selector:"";return null!==CC.getParent(t,function(t){return xC(t)||!!n&&e.dom.is(t,n)})},NC=function(e,t){var n=t.editor;bC(n),n.on("focusin",function(){var t=e.focusedEditor;t!==this&&(t&&t.fire("blur",{focusedEditor:this}),e.setActive(this),e.focusedEditor=this,this.fire("focus",{blurredEditor:t}),this.focus(!0))}),n.on("focusout",function(){var t=this;ve.setEditorTimeout(t,function(){var n=e.focusedEditor;wC(t,function(){try{return document.activeElement}catch(e){return document.body}}())||n!==t||(t.fire("blur",{focusedEditor:null}),e.focusedEditor=null)})}),pC||(pC=function(t){var n,r=e.activeEditor;n=t.target,r&&n.ownerDocument===document&&(n===document.body||wC(r,n)||e.focusedEditor!==r||(r.fire("blur",{focusedEditor:null}),e.focusedEditor=null))},CC.bind(document,"focusin",pC))},EC=function(e,t){e.focusedEditor===t.editor&&(e.focusedEditor=null),e.activeEditor||(CC.unbind(document,"focusin",pC),pC=null)},SC=function(e){e.on("AddEditor",y.curry(NC,e)),e.on("RemoveEditor",y.curry(EC,e))},kC={},TC="en",AC={setCode:function(e){e&&(TC=e,this.rtl=!!this.data[e]&&"rtl"===this.data[e]._dir)},getCode:function(){return TC},rtl:!1,add:function(e,t){var n=kC[e];for(var r in n||(kC[e]=n={}),t)n[r]=t[r];this.setCode(e)},translate:function(e){var t=kC[TC]||{},n=function(e){return Dt.is(e,"function")?Object.prototype.toString.call(e):r(e)?"":""+e},r=function(e){return""===e||null===e||Dt.is(e,"undefined")},o=function(e){return e=n(e),Dt.hasOwn(t,e)?n(t[e]):e};if(r(e))return"";if(Dt.is(e,"object")&&Dt.hasOwn(e,"raw"))return n(e.raw);if(Dt.is(e,"array")){var i=e.slice(1);e=o(e[0]).replace(/\{([0-9]+)\}/g,function(e,t){return Dt.hasOwn(i,t)?n(i[t]):e})}return o(e).replace(/{context:\w+}$/,"")},data:kC},_C=ui.DOM,RC=Dt.explode,BC=Dt.each,DC=Dt.extend,OC=0,PC=!1,LC=[],IC=[],MC=function(e){BC(hC.get(),function(t){"scroll"===e.type?t.fire("ScrollWindow",e):t.fire("ResizeWindow",e)})},FC=function(e){e!==PC&&(e?Jt(window).on("resize scroll",MC):Jt(window).off("resize scroll",MC),PC=e)},zC=function(e){var t=IC;delete LC[e.id];for(var n=0;n<LC.length;n++)if(LC[n]===e){LC.splice(n,1);break}return IC=M.filter(IC,function(t){return e!==t}),hC.activeEditor===e&&(hC.activeEditor=IC.length>0?IC[0]:null),hC.focusedEditor===e&&(hC.focusedEditor=null),t.length!==IC.length};DC(hC={defaultSettings:{},$:Jt,majorVersion:"4",minorVersion:"7.9",releaseDate:"2018-02-27",editors:LC,i18n:AC,activeEditor:null,settings:{},setup:function(){var e,t,n,r,o="";if(t=Zb.getDocumentBaseUrl(document.location),/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/")),n=window.tinymce||window.tinyMCEPreInit)e=n.base||n.baseURL,o=n.suffix;else{for(var i=document.getElementsByTagName("script"),a=0;a<i.length;a++){var u=(r=i[a].src).substring(r.lastIndexOf("/"));if(/tinymce(\.full|\.jquery|)(\.min|\.dev|)\.js/.test(r)){-1!==u.indexOf(".min")&&(o=".min"),e=r.substring(0,r.lastIndexOf("/"));break}}!e&&document.currentScript&&(-1!==(r=document.currentScript.src).indexOf(".min")&&(o=".min"),e=r.substring(0,r.lastIndexOf("/")))}this.baseURL=new Zb(t).toAbsolute(e),this.documentBaseURL=t,this.baseURI=new Zb(this.baseURL),this.suffix=o,SC(this)},overrideDefaults:function(e){var t,n;(t=e.base_url)&&(this.baseURL=new Zb(this.documentBaseURL).toAbsolute(t.replace(/\/+$/,"")),this.baseURI=new Zb(this.baseURL)),n=e.suffix,e.suffix&&(this.suffix=n),this.defaultSettings=e;var r=e.plugin_base_urls;for(var o in r)pi.PluginManager.urls[o]=r[o]},init:function(e){var t,n,r=this;n=Dt.makeMap("area base basefont br col frame hr img input isindex link meta param embed source wbr track colgroup option tbody tfoot thead tr script noscript style textarea video audio iframe object menu"," ");var o=function(e){var t=e.id;return t||(t=(t=e.name)&&!_C.get(t)?e.name:_C.uniqueId(),e.setAttribute("id",t)),t},i=function(e,t){return t.constructor===RegExp?t.test(e.className):_C.hasClass(e,t)},a=function(e){t=e},u=function(){var t,s=0,c=[],l=function(e,n,o){var i=new mC(e,n,r);c.push(i),i.on("init",function(){++s===t.length&&a(c)}),i.targetElm=i.targetElm||o,i.render()};_C.unbind(window,"ready",u),function(t){var n=e[t];n&&n.apply(r,Array.prototype.slice.call(arguments,2))}("onpageload"),t=Jt.unique(function(e){var t,n=[];if(de.ie&&de.ie<11)return xp.initError("TinyMCE does not support the browser you are using. For a list of supported browsers please see: https://www.tinymce.com/docs/get-started/system-requirements/"),[];if(e.types)return BC(e.types,function(e){n=n.concat(_C.select(e.selector))}),n;if(e.selector)return _C.select(e.selector);if(e.target)return[e.target];switch(e.mode){case"exact":(t=e.elements||"").length>0&&BC(RC(t),function(e){var t;(t=_C.get(e))?n.push(t):BC(document.forms,function(t){BC(t.elements,function(t){t.name===e&&(e="mce_editor_"+OC++,_C.setAttrib(t,"id",e),n.push(t))})})});break;case"textareas":case"specific_textareas":BC(_C.select("textarea"),function(t){e.editor_deselector&&i(t,e.editor_deselector)||e.editor_selector&&!i(t,e.editor_selector)||n.push(t)})}return n}(e)),e.types?BC(e.types,function(n){Dt.each(t,function(t){return!_C.is(t,n.selector)||(l(o(t),DC({},e,n),t),!1)})}):(Dt.each(t,function(e){var t;(t=r.get(e.id))&&t.initialized&&!(t.getContainer()||t.getBody()).parentNode&&(zC(t),t.unbindAllNativeEvents(),t.destroy(!0),t.removed=!0,t=null)}),0===(t=Dt.grep(t,function(e){return!r.get(e.id)})).length?a([]):BC(t,function(t){var r;r=t,e.inline&&r.tagName.toLowerCase()in n?xp.initError("Could not initialize inline editor on invalid inline target element",t):l(o(t),e,t)}))};return r.settings=e,_C.bind(window,"ready",u),new me(function(e){t?e(t):a=function(t){e(t)}})},get:function(e){return 0===arguments.length?IC.slice(0):Jn.isString(e)?M.find(IC,function(t){return t.id===e}).getOr(null):Jn.isNumber(e)&&IC[e]?IC[e]:null},add:function(e){var t=this;return LC[e.id]===e?e:(null===t.get(e.id)&&("length"!==e.id&&(LC[e.id]=e),LC.push(e),IC.push(e)),FC(!0),t.activeEditor=e,t.fire("AddEditor",{editor:e}),gC||(gC=function(){t.fire("BeforeUnload")},_C.bind(window,"beforeunload",gC)),e)},createEditor:function(e,t){return this.add(new mC(e,t,this))},remove:function(e){var t,n,r=this;if(e)return Jn.isString(e)?(e=e.selector||e,void BC(_C.select(e),function(e){(n=r.get(e.id))&&r.remove(n)})):(n=e,Jn.isNull(r.get(n.id))?null:(zC(n)&&r.fire("RemoveEditor",{editor:n}),0===IC.length&&_C.unbind(window,"beforeunload",gC),n.remove(),FC(IC.length>0),n));for(t=IC.length-1;t>=0;t--)r.remove(IC[t])},execCommand:function(e,t,n){var r=this.get(n);switch(e){case"mceAddEditor":return this.get(n)||new mC(n,this.settings,this).render(),!0;case"mceRemoveEditor":return r&&r.remove(),!0;case"mceToggleEditor":return r?(r.isHidden()?r.show():r.hide(),!0):(this.execCommand("mceAddEditor",0,n),!0)}return!!this.activeEditor&&this.activeEditor.execCommand(e,t,n)},triggerSave:function(){BC(IC,function(e){e.save()})},addI18n:function(e,t){AC.add(e,t)},translate:function(e){return AC.translate(e)},setActive:function(e){var t=this.activeEditor;this.activeEditor!==e&&(t&&t.fire("deactivate",{relatedTarget:e}),e.fire("activate",{relatedTarget:t})),this.activeEditor=e}},Mm),hC.setup();var UC,qC=hC;function VC(e){return{walk:function(t,n){return th.walk(e,t,n)},split:lf.split,normalize:function(t){return Yd.normalize(e,t).fold(y.constant(!1),function(e){return t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),!0})}}}(UC=VC||(VC={})).compareRanges=qd.isEq,UC.getCaretRangeFromPoint=Rv.fromPoint,UC.getSelectedNode=Xi,UC.getNode=Yi;var HC,jC,$C=VC,WC=Math.min,KC=Math.max,XC=Math.round,YC=function(e,t,n){var r,o,i,a,u,s;return r=t.x,o=t.y,i=e.w,a=e.h,u=t.w,s=t.h,"b"===(n=(n||"").split(""))[0]&&(o+=s),"r"===n[1]&&(r+=u),"c"===n[0]&&(o+=XC(s/2)),"c"===n[1]&&(r+=XC(u/2)),"b"===n[3]&&(o-=a),"r"===n[4]&&(r-=i),"c"===n[3]&&(o-=XC(a/2)),"c"===n[4]&&(r-=XC(i/2)),GC(r,o,i,a)},GC=function(e,t,n,r){return{x:e,y:t,w:n,h:r}},JC={inflate:function(e,t,n){return GC(e.x-t,e.y-n,e.w+2*t,e.h+2*n)},relativePosition:YC,findBestRelativePosition:function(e,t,n,r){var o,i;for(i=0;i<r.length;i++)if((o=YC(e,t,r[i])).x>=n.x&&o.x+o.w<=n.w+n.x&&o.y>=n.y&&o.y+o.h<=n.h+n.y)return r[i];return null},intersect:function(e,t){var n,r,o,i;return n=KC(e.x,t.x),r=KC(e.y,t.y),o=WC(e.x+e.w,t.x+t.w),i=WC(e.y+e.h,t.y+t.h),o-n<0||i-r<0?null:GC(n,r,o-n,i-r)},clamp:function(e,t,n){var r,o,i,a,u,s,c,l,f,d;return u=e.x,s=e.y,c=e.x+e.w,l=e.y+e.h,f=t.x+t.w,d=t.y+t.h,r=KC(0,t.x-u),o=KC(0,t.y-s),i=KC(0,c-f),a=KC(0,l-d),u+=r,s+=o,n&&(c+=r,l+=o,u-=i,s-=a),GC(u,s,(c-=i)-u,(l-=a)-s)},create:GC,fromClientRect:function(e){return GC(e.left,e.top,e.width,e.height)}},QC={},ZC={add:function(e,t){QC[e.toLowerCase()]=t},has:function(e){return!!QC[e.toLowerCase()]},get:function(e){var t=e.toLowerCase(),n=QC.hasOwnProperty(t)?QC[t]:null;if(null===n)throw new Error("Could not find module for type: "+e);return n},create:function(e,t){var n;if("string"==typeof e?(t=t||{}).type=e:e=(t=e).type,e=e.toLowerCase(),!(n=QC[e]))throw new Error("Could not find control by type: "+e);return(n=new n(t)).type=e,n}},ex=Dt.each,tx=Dt.extend,nx=function(){};nx.extend=HC=function(e){var t,n,r,o=this.prototype,i=function(){var e,t,n;if(!jC&&(this.init&&this.init.apply(this,arguments),t=this.Mixins))for(e=t.length;e--;)(n=t[e]).init&&n.init.apply(this,arguments)},a=function(){return this},u=function(e,t){return function(){var n,r=this._super;return this._super=o[e],n=t.apply(this,arguments),this._super=r,n}};for(n in jC=!0,t=new this,jC=!1,e.Mixins&&(ex(e.Mixins,function(t){for(var n in t)"init"!==n&&(e[n]=t[n])}),o.Mixins&&(e.Mixins=o.Mixins.concat(e.Mixins))),e.Methods&&ex(e.Methods.split(","),function(t){e[t]=a}),e.Properties&&ex(e.Properties.split(","),function(t){var n="_"+t;e[t]=function(e){return e!==undefined?(this[n]=e,this):this[n]}}),e.Statics&&ex(e.Statics,function(e,t){i[t]=e}),e.Defaults&&o.Defaults&&(e.Defaults=tx({},o.Defaults,e.Defaults)),e)"function"==typeof(r=e[n])&&o[n]?t[n]=u(n,r):t[n]=r;return i.prototype=t,i.constructor=i,i.extend=HC,i};var rx=Math.min,ox=Math.max,ix=Math.round,ax=function(e,t){var n,r,o,i;if(t=t||'"',null===e)return"null";if("string"==(o=typeof e))return r="\bb\tt\nn\ff\rr\"\"''\\\\",t+e.replace(/([\u0080-\uFFFF\x00-\x1f\"\'\\])/g,function(e,o){return'"'===t&&"'"===e?e:(n=r.indexOf(o))+1?"\\"+r.charAt(n+1):(e=o.charCodeAt().toString(16),"\\u"+"0000".substring(e.length)+e)})+t;if("object"===o){if(e.hasOwnProperty&&"[object Array]"===Object.prototype.toString.call(e)){for(n=0,r="[";n<e.length;n++)r+=(n>0?",":"")+ax(e[n],t);return r+"]"}for(i in r="{",e)e.hasOwnProperty(i)&&(r+="function"!=typeof e[i]?(r.length>1?","+t:t)+i+t+":"+ax(e[i],t):"");return r+"}"}return""+e},ux={serialize:ax,parse:function(e){try{return JSON.parse(e)}catch(t){}}},sx={callbacks:{},count:0,send:function(e){var t=this,n=ui.DOM,r=e.count!==undefined?e.count:t.count,o="tinymce_jsonp_"+r;t.callbacks[r]=function(i){n.remove(o),delete t.callbacks[r],e.callback(i)},n.add(n.doc.body,"script",{id:o,src:e.url,type:"text/javascript"}),t.count++}},cx={send:function(e){var t,n=0,r=function(){!e.async||4===t.readyState||n++>1e4?(e.success&&n<1e4&&200===t.status?e.success.call(e.success_scope,""+t.responseText,t,e):e.error&&e.error.call(e.error_scope,n>1e4?"TIMED_OUT":"GENERAL",t,e),t=null):setTimeout(r,10)};if(e.scope=e.scope||this,e.success_scope=e.success_scope||e.scope,e.error_scope=e.error_scope||e.scope,e.async=!1!==e.async,e.data=e.data||"",cx.fire("beforeInitialize",{settings:e}),t=new Ep){if(t.overrideMimeType&&t.overrideMimeType(e.content_type),t.open(e.type||(e.data?"POST":"GET"),e.url,e.async),e.crossDomain&&(t.withCredentials=!0),e.content_type&&t.setRequestHeader("Content-Type",e.content_type),e.requestheaders&&Dt.each(e.requestheaders,function(e){t.setRequestHeader(e.key,e.value)}),t.setRequestHeader("X-Requested-With","XMLHttpRequest"),(t=cx.fire("beforeSend",{xhr:t,settings:e}).xhr).send(e.data),!e.async)return r();setTimeout(r,10)}}};Dt.extend(cx,Mm);var lx=Dt.extend,fx=function(e){this.settings=lx({},e),this.count=0};fx.sendRPC=function(e){return(new fx).send(e)},fx.prototype={send:function(e){var t=e.error,n=e.success;(e=lx(this.settings,e)).success=function(r,o){void 0===(r=ux.parse(r))&&(r={error:"JSON Parse error."}),r.error?t.call(e.error_scope||e.scope,r.error,o):n.call(e.success_scope||e.scope,r.result)},e.error=function(n,r){t&&t.call(e.error_scope||e.scope,n,r)},e.data=ux.serialize({id:e.id||"c"+this.count++,method:e.method,params:e.params}),e.content_type="application/json",cx.send(e)}};var dx,mx=window.localStorage,px=qC,gx={geom:{Rect:JC},util:{Promise:me,Delay:ve,Tools:Dt,VK:Cg,URI:Zb,Class:nx,EventDispatcher:Pm,Observable:Mm,I18n:AC,XHR:cx,JSON:ux,JSONRequest:fx,JSONP:sx,LocalStorage:mx,Color:function(e){var t={},n=0,r=0,o=0,i=function(e){var i;return"object"==typeof e?"r"in e?(n=e.r,r=e.g,o=e.b):"v"in e&&function(e,t,i){var a,u,s,c;if(e=(parseInt(e,10)||0)%360,t=parseInt(t,10)/100,i=parseInt(i,10)/100,t=ox(0,rx(t,1)),i=ox(0,rx(i,1)),0!==t){switch(a=e/60,s=(u=i*t)*(1-Math.abs(a%2-1)),c=i-u,Math.floor(a)){case 0:n=u,r=s,o=0;break;case 1:n=s,r=u,o=0;break;case 2:n=0,r=u,o=s;break;case 3:n=0,r=s,o=u;break;case 4:n=s,r=0,o=u;break;case 5:n=u,r=0,o=s;break;default:n=r=o=0}n=ix(255*(n+c)),r=ix(255*(r+c)),o=ix(255*(o+c))}else n=r=o=ix(255*i)}(e.h,e.s,e.v):(i=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)[^\)]*\)/gi.exec(e))?(n=parseInt(i[1],10),r=parseInt(i[2],10),o=parseInt(i[3],10)):(i=/#([0-F]{2})([0-F]{2})([0-F]{2})/gi.exec(e))?(n=parseInt(i[1],16),r=parseInt(i[2],16),o=parseInt(i[3],16)):(i=/#([0-F])([0-F])([0-F])/gi.exec(e))&&(n=parseInt(i[1]+i[1],16),r=parseInt(i[2]+i[2],16),o=parseInt(i[3]+i[3],16)),n=n<0?0:n>255?255:n,r=r<0?0:r>255?255:r,o=o<0?0:o>255?255:o,t};return e&&i(e),t.toRgb=function(){return{r:n,g:r,b:o}},t.toHsv=function(){return e=n,t=r,i=o,u=0,(s=rx(e/=255,rx(t/=255,i/=255)))===(c=ox(e,ox(t,i)))?{h:0,s:0,v:100*(u=s)}:(a=(c-s)/c,u=c,{h:ix(60*((e===s?3:i===s?1:5)-(e===s?t-i:i===s?e-t:i-e)/(c-s))),s:ix(100*a),v:ix(100*u)});var e,t,i,a,u,s,c},t.toHex=function(){var e=function(e){return(e=parseInt(e,10).toString(16)).length>1?e:"0"+e};return"#"+e(n)+e(r)+e(o)},t.parse=i,t}},dom:{EventUtils:ke,Sizzle:ct,DomQuery:Jt,TreeWalker:Zr,DOMUtils:ui,ScriptLoader:di,RangeUtils:$C,Serializer:yv,ControlSelection:kv,BookmarkManager:Nv,Selection:dy,Event:ke.Event},html:{Styles:Qo,Entities:zo,Node:nv,Schema:Go,SaxParser:Bg,DomParser:mv,Writer:oc,Serializer:ic},ui:{Factory:ZC},Env:de,AddOnManager:pi,Formatter:Uh,UndoManager:Kg,EditorCommands:Dm,WindowManager:hp,NotificationManager:gp,EditorObservable:Vm,Shortcuts:Gm,Editor:mC,FocusManager:vC,EditorManager:qC,DOM:ui.DOM,ScriptLoader:di.ScriptLoader,PluginManager:pi.PluginManager,ThemeManager:pi.ThemeManager,trim:Dt.trim,isArray:Dt.isArray,is:Dt.is,toArray:Dt.toArray,makeMap:Dt.makeMap,each:Dt.each,map:Dt.map,grep:Dt.grep,inArray:Dt.inArray,extend:Dt.extend,create:Dt.create,walk:Dt.walk,createNS:Dt.createNS,resolve:Dt.resolve,explode:Dt.explode,_addCacheSuffix:Dt._addCacheSuffix,isOpera:de.opera,isWebKit:de.webkit,isIE:de.ie,isGecko:de.gecko,isMac:de.mac},hx=px=Dt.extend(px,gx);dx=hx,window.tinymce=dx,window.tinyMCE=dx,function(e){if("object"==typeof module)try{module.exports=e}catch(t){}}(hx)}();
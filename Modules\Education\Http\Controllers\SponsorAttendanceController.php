<?php

namespace Modules\Education\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use App\Role;
use App\Employee;
use DB;

class SponsorAttendanceController extends Controller
{
    /**
     * Display attendance report limited to sponsor centers.
     */
    public function monthlyReport(Request $request)
    {
        $user = Auth::user();
        $centerIds = $user->allowedCenters()->pluck('centers.id')->toArray();

        // Validate if the requested center belongs to the user
        $requestedCenterId = $request->input('teacherCenters');
        if (!in_array($requestedCenterId, $centerIds)) {
            abort(403, 'Unauthorized access to the center.');
        }

        // Get filters from the request or defaults
        $month = $request->input('month', now()->month);
        $year = $request->input('year', now()->year);
        $roles = $request->input('role_id', Role::where('type', 'regular_user')->pluck('id')->toArray());
        $employeeId = $request->input('employee_id', []);

        // Format inputs for stored procedure
        $roleParam = implode(',', $roles);
        $employeeParam = is_array($employeeId) ? implode(',', $employeeId) : $employeeId;
        $centerParam = is_array($requestedCenterId) ? implode(',', $requestedCenterId) : $requestedCenterId;

        // Call stored procedure to fetch attendance
        $attendanceRaw = DB::select(
            'CALL GetEmployeeDailyAttendanceReportByYearMonthRole(?,?,?,?,?)',
            [$month, $year, $roleParam, $employeeParam, $centerParam]
        );

        // Format the data for the view
        $attendance = $this->formatAttendanceData($attendanceRaw);

        // Prepare dropdowns
        $months = $this->getMonthsDropdown();
        $filteredEmployees = Employee::whereIn('id', $employeeId)
            ->orderBy('full_name')
            ->pluck('full_name', 'id')
            ->toArray();

        return view('modules.humanresource.attendance.sponsor_center_employee_monthly_report_latest', compact(
            'attendance', 'months', 'month', 'year', 'filteredEmployees'
        ));
    }

    private function getMonthsDropdown()
    {
        return [
            1  => 'January', 2  => 'February', 3  => 'March', 4  => 'April',
            5  => 'May',     6  => 'June',     7  => 'July',   8  => 'August',
            9  => 'September',10 => 'October', 11 => 'November', 12 => 'December',
        ];
    }

    private function formatAttendanceData($rawData)
    {
        $data = [];
        foreach ($rawData as $row) {
            $employeeId = $row->employee_id;
            if (!isset($data[$employeeId])) {
                $data[$employeeId] = [
                    'employee' => ['full_name' => $row->full_name, 'id' => $employeeId],
                    'details' => [],
                ];
            }
            $data[$employeeId]['details'][] = [
                'formatted_date' => $row->formatted_date,
                'day_name' => $row->day_name,
                'status' => $row->status,
                'in_time' => $row->in_time,
                'out_time' => $row->out_time,
                'hours_worked' => $row->hours_worked,
                'volunteer_hours' => $row->volunteer_hours,
                'salary_percentage' => $row->salary_percentage,
            ];
        }
        return $data;
    }
}

<?php

namespace App\Console\Commands;

use App\Classes;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CacheClassesCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'cache:classes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cache classes for improved application performance';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            Log::info('Starting cache:classes command');
            $this->info('Fetching classes...');
            
            // Get start time to measure performance
            $startTime = microtime(true);
            
            $classes = Classes::all()->sortBy('name')->pluck('name', 'id');
            
            $count = $classes->count();
            Log::info("Retrieved {$count} classes");
            $this->info("Retrieved {$count} classes");

            // Put results in cache
            Cache::put('classes', $classes);
            
            $executionTime = round(microtime(true) - $startTime, 2);
            Log::info("Cached {$count} classes successfully in {$executionTime} seconds");
            $this->info("Cached {$count} classes successfully in {$executionTime} seconds");
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            Log::error('Failed to cache classes: ' . $e->getMessage());
            $this->error('Command failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}

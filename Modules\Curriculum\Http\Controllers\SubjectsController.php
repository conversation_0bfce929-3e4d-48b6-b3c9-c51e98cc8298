<?php

namespace Modules\Curriculum\Http\Controllers;

use App\Classes;
use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Content;
use App\Subject;
use Illuminate\Http\Request;
use Session;

class SubjectsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $keyword = $request->get('search');
        $perPage = 25;

        $classes = Classes::all()->sortBy('name')->pluck('name', 'id');

        if (!empty($keyword)) {
            $subjects = Subject::with('classes')->with('translations')->where('title', 'LIKE', "%$keyword%")
				->orWhere('preface', 'LIKE', "%$keyword%")
				->orWhere('status', 'LIKE', "%$keyword%")
				->orWhere('language', 'LIKE', "%$keyword%")
				->orWhere('status', 'LIKE', "%$keyword%")
				->paginate($perPage);
//				->get();
        } else {
            $subjects = Subject::with('classes')->with('translations')
                ->paginate($perPage);
//                ->get();
        }

        return view('curriculum::subjects.index', compact('subjects','classes'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('curriculum::subjects.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $this->validateSubject($request);
        $requestData = $request->all();

        $requestData['organization_id'] = config('organization_id');
        
        $subject = Subject::create($requestData);

        foreach ($request->translate as $code => $translate) {
            $subject->translateOrNew($code)->title = $translate['title'];
        }

        $subject->save();

        Session::flash('flash_message', 'Subject added!');

        return redirect('workplace/curriculum/subjects');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $subject = Subject::findOrFail($id);

        $contents = Content::where("language" , "=" , $subject->language)->get();

        return view('curriculum::subjects.show', compact('subject' , 'contents'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $subject = Subject::findOrFail($id);

        return view('curriculum::subjects.edit', compact('subject'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {
        $this->validateSubject($request);
        $requestData = $request->all();
        
        $subject = Subject::findOrFail($id);
        $subject->update($requestData);

        foreach ($request->translate as $code => $translate) {
            $subject->translateOrNew($code)->title = $translate['title'];
        }

        $subject->save();

        Session::flash('flash_message', 'Subject updated!');

        return redirect('workplace/curriculum/subjects');
    }


    /**
     * Update Subject Contents.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function contents(Request $request)
    {
        auth()->user()->can('edit subject_contents');

        $id = $request->subject_id;

        $subject = Subject::findOrFail($id);
        
        $subject->contents()->attach($request->contents);
        
        Session::flash('flash_message', 'Program updated!');
        
        if($request->ajax()){
            return response()->json(['status' => 'success']);
        }

        return redirect()->back();
    }

    public function delete_content($id)
    {

     $content = Content::findOrFail($id);
    
 
    $content->subjects()->detach();
  
    return redirect()->back();
     
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Subject::destroy($id);

        Session::flash('flash_message', 'Subject deleted!');

        return redirect('workplace/curriculum/subjects');
    }
    private function validateSubject($request){
        $rules = [];

        $rules['translate.*.title'] = 'required|min:3';
        $rules['language'] = 'required';
        $rules['status'] = 'required';



        $this->validate($request, $rules);
    }
}

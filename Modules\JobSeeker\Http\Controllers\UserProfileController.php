<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;
use Mo<PERSON>les\JobSeeker\Entities\JobSeeker;
use Modules\JobSeeker\Entities\JobSeekerProfileSettings;
use Modules\JobSeeker\Entities\JobSeekerSecuritySettings;
use Modules\JobSeeker\Entities\JobSeekerAccountPreferences;
use Modules\JobSeeker\Services\PasswordHistoryService;
use App\Http\Controllers\Controller;

final class UserProfileController extends Controller
{
    public function __construct(
        private readonly PasswordHistoryService $passwordHistoryService
    ) {
        $this->middleware('auth:job_seeker');
    }

    /**
     * Show the user profile page.
     */
    public function profile(): View
    {
        $user = Auth::guard('job_seeker')->user();
        
        // Ensure profile settings exist
        $profileSettings = $user->profileSettings ?? new JobSeekerProfileSettings(['jobseeker_id' => $user->id]);
        
        Log::info('User viewing profile page', [
            'user_id' => $user->id,
        ]);

        return view('modules.jobseeker.profile.index', [
            'user' => $user,
            'profileSettings' => $profileSettings,
        ]);
    }

    /**
     * Update the user profile.
     */
    public function updateProfile(Request $request): RedirectResponse
    {
        $user = Auth::guard('job_seeker')->user();

        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:job_seekers,email,' . $user->id],
            'bio' => ['nullable', 'string', 'max:1000'],
            'phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:500'],
            'city' => ['nullable', 'string', 'max:100'],
            'state' => ['nullable', 'string', 'max:100'],
            'country' => ['nullable', 'string', 'max:100'],
            'postal_code' => ['nullable', 'string', 'max:20'],
            'date_of_birth' => ['nullable', 'date', 'before:today'],
            'gender' => ['nullable', 'in:male,female,other,prefer_not_to_say'],
            'linkedin_url' => ['nullable', 'url', 'max:255'],
            'github_url' => ['nullable', 'url', 'max:255'],
            'portfolio_url' => ['nullable', 'url', 'max:255'],
            'skills' => ['nullable', 'array'],
            'skills.*' => ['string', 'max:100'],
            'experience_years' => ['nullable', 'integer', 'min:0', 'max:50'],
            'education_level' => ['nullable', 'in:high_school,associate,bachelor,master,phd,other'],
            'preferred_job_types' => ['nullable', 'array'],
            'preferred_job_types.*' => ['string', 'max:100'],
            'preferred_locations' => ['nullable', 'array'],
            'preferred_locations.*' => ['string', 'max:100'],
            'salary_expectation_min' => ['nullable', 'numeric', 'min:0'],
            'salary_expectation_max' => ['nullable', 'numeric', 'min:0', 'gte:salary_expectation_min'],
            'currency' => ['nullable', 'string', 'size:3'],
            'availability' => ['nullable', 'in:immediate,2_weeks,1_month,3_months,not_available'],
            'remote_work_preference' => ['nullable', 'in:remote_only,hybrid,onsite_only,flexible'],
            'profile_visibility' => ['nullable', 'in:public,private,connections_only'],
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput()
                ->with('error', 'Please correct the errors below.');
        }

        try {
            // Update basic user info
            $user->update([
                'name' => $request->input('name'),
                'email' => $request->input('email'),
            ]);

            // Update or create profile settings
            $profileData = $request->only([
                'bio', 'phone', 'address', 'city', 'state', 'country', 'postal_code',
                'date_of_birth', 'gender', 'linkedin_url', 'github_url', 'portfolio_url',
                'skills', 'experience_years', 'education_level', 'preferred_job_types',
                'preferred_locations', 'salary_expectation_min', 'salary_expectation_max',
                'currency', 'availability', 'remote_work_preference', 'profile_visibility'
            ]);

            $user->profileSettings()->updateOrCreate(
                ['jobseeker_id' => $user->id],
                $profileData
            );

            Log::info('User profile updated successfully', [
                'user_id' => $user->id,
                'updated_fields' => array_keys($profileData),
            ]);

            return redirect()->route('jobseeker.profile')
                ->with('success', 'Profile updated successfully!');

        } catch (\Exception $e) {
            Log::error('Failed to update user profile', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update profile. Please try again.');
        }
    }

    /**
     * Show the account settings page.
     */
    public function accountSettings(): View
    {
        $user = Auth::guard('job_seeker')->user();
        
        // Ensure preferences exist
        $preferences = $user->accountPreferences ?? new JobSeekerAccountPreferences(['jobseeker_id' => $user->id]);

        return view('modules.jobseeker.account.settings', [
            'user' => $user,
            'preferences' => $preferences,
        ]);
    }

    /**
     * Update account settings.
     */
    public function updateAccountSettings(Request $request): RedirectResponse
    {
        $user = Auth::guard('job_seeker')->user();

        $validator = Validator::make($request->all(), [
            'theme_preference' => ['required', 'in:light,dark,system'],
            'language' => ['required', 'string', 'size:2'],
            'timezone' => ['required', 'string', 'max:50'],
            'date_format' => ['required', 'string', 'max:20'],
            'time_format' => ['required', 'in:12h,24h'],
            'currency_display' => ['required', 'string', 'size:3'],
            'notification_frequency' => ['required', 'in:immediate,daily,weekly,monthly'],
            'email_digest_frequency' => ['required', 'in:never,daily,weekly,monthly'],
            'auto_save_applications' => ['boolean'],
            'show_salary_in_listings' => ['boolean'],
            'show_company_ratings' => ['boolean'],
            'job_search_radius' => ['nullable', 'integer', 'min:1', 'max:500'],
            'profile_completion_reminders' => ['boolean'],
            'application_deadline_reminders' => ['boolean'],
            'interview_reminders' => ['boolean'],
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput()
                ->with('error', 'Please correct the errors below.');
        }

        try {
            $preferencesData = $request->only([
                'theme_preference', 'language', 'timezone', 'date_format', 'time_format',
                'currency_display', 'notification_frequency', 'email_digest_frequency',
                'auto_save_applications', 'show_salary_in_listings', 'show_company_ratings',
                'job_search_radius', 'profile_completion_reminders', 'application_deadline_reminders',
                'interview_reminders'
            ]);

            // Convert checkbox values to boolean
            $booleanFields = [
                'auto_save_applications', 'show_salary_in_listings', 'show_company_ratings',
                'profile_completion_reminders', 'application_deadline_reminders', 'interview_reminders'
            ];

            foreach ($booleanFields as $field) {
                $preferencesData[$field] = $request->has($field);
            }

            $user->accountPreferences()->updateOrCreate(
                ['jobseeker_id' => $user->id],
                $preferencesData
            );

            Log::info('User account settings updated successfully', [
                'user_id' => $user->id,
                'updated_preferences' => array_keys($preferencesData),
            ]);

            return redirect()->route('jobseeker.account.settings')
                ->with('success', 'Account settings updated successfully!');

        } catch (\Exception $e) {
            Log::error('Failed to update account settings', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update account settings. Please try again.');
        }
    }

    /**
     * Show the change password page.
     */
    public function changePassword(): View
    {
        $user = Auth::guard('job_seeker')->user();

        return view('modules.jobseeker.account.change-password', [
            'user' => $user,
        ]);
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request): RedirectResponse
    {
        $user = Auth::guard('job_seeker')->user();

        $validator = Validator::make($request->all(), [
            'current_password' => ['required', 'string'],
            'password' => ['required', 'string', Password::defaults(), 'confirmed'],
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->with('error', 'Please correct the errors below.');
        }

        // Verify current password
        if (!Hash::check($request->input('current_password'), $user->password)) {
            return redirect()->back()
                ->withErrors(['current_password' => 'The current password is incorrect.'])
                ->with('error', 'Current password is incorrect.');
        }

        $newPassword = $request->input('password');

        // Check password history
        if ($this->passwordHistoryService->isPasswordReused($user, $newPassword)) {
            return redirect()->back()
                ->withErrors(['password' => 'You cannot reuse a recent password.'])
                ->with('error', 'You cannot reuse a recent password.');
        }

        try {
            // Update password
            $user->update([
                'password' => Hash::make($newPassword),
                'password_reset_required' => false,
                'password_reset_reason' => null,
                'password_reset_required_at' => null,
            ]);

            // Store password in history
            $this->passwordHistoryService->storePasswordHistory($user, $newPassword);

            // Update security settings
            $user->securitySettings()->updateOrCreate(
                ['jobseeker_id' => $user->id],
                ['last_password_change' => now()]
            );

            Log::info('User password changed successfully', [
                'user_id' => $user->id,
            ]);

            return redirect()->route('jobseeker.account.change-password')
                ->with('success', 'Password changed successfully!');

        } catch (\Exception $e) {
            Log::error('Failed to change user password', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->back()
                ->with('error', 'Failed to change password. Please try again.');
        }
    }

    /**
     * Show the security settings page.
     */
    public function securitySettings(): View
    {
        $user = Auth::guard('job_seeker')->user();
        
        // Ensure security settings exist
        $securitySettings = $user->securitySettings ?? new JobSeekerSecuritySettings(['jobseeker_id' => $user->id]);

        return view('modules.jobseeker.account.security', [
            'user' => $user,
            'securitySettings' => $securitySettings,
        ]);
    }

    /**
     * Update security settings.
     */
    public function updateSecuritySettings(Request $request): RedirectResponse
    {
        $user = Auth::guard('job_seeker')->user();

        $validator = Validator::make($request->all(), [
            'two_factor_enabled' => ['boolean'],
            'login_notifications' => ['boolean'],
            'email_notifications' => ['boolean'],
            'push_notifications' => ['boolean'],
            'job_alert_notifications' => ['boolean'],
            'session_timeout' => ['required', 'integer', 'min:15', 'max:10080'], // 15 minutes to 1 week
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput()
                ->with('error', 'Please correct the errors below.');
        }

        try {
            $securityData = [
                'two_factor_enabled' => $request->has('two_factor_enabled'),
                'login_notifications' => $request->has('login_notifications'),
                'email_notifications' => $request->has('email_notifications'),
                'push_notifications' => $request->has('push_notifications'),
                'job_alert_notifications' => $request->has('job_alert_notifications'),
                'session_timeout' => $request->input('session_timeout'),
            ];

            $user->securitySettings()->updateOrCreate(
                ['jobseeker_id' => $user->id],
                $securityData
            );

            Log::info('User security settings updated successfully', [
                'user_id' => $user->id,
                'updated_settings' => array_keys($securityData),
            ]);

            return redirect()->route('jobseeker.account.security')
                ->with('success', 'Security settings updated successfully!');

        } catch (\Exception $e) {
            Log::error('Failed to update security settings', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update security settings. Please try again.');
        }
    }
} 
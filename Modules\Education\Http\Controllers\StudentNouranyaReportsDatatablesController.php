<?php

declare(strict_types=1);

namespace Modules\Education\Http\Controllers;

use App\Classes;
use App\Http\Controllers\Controller;
use App\PublicHoliday;
use App\StudentNouranyaReport;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;

final class StudentNouranyaReportsDatatablesController extends Controller
{
    public function studentRecords(Request $request): JsonResponse
    {
        try {
            $studentId = (int)$request->input('studentId');
            $classId = (int)$request->input('classId');
            $monthYear = $request->input('monthYear');

            if (!$studentId || !$classId || !$monthYear) {
                return response()->json(['data' => []]);
            }

            try {
                $date = Carbon::createFromFormat('F Y', $monthYear);
            } catch (\Exception $e) {
                return response()->json(['data' => [], 'error' => 'Invalid date format. Please use "Month Year".']);
            }
            
            $month = $date->month;
            $year = $date->year;

            $class = Classes::with('timetable')->find($classId);
            if (!$class || !$class->timetable) {
                return response()->json(['data' => [], 'error' => 'Class timetable not found.']);
            }

            $classDays = $class->timetable->getScheduledDaysOfWeek();

            $publicHolidays = [];
            foreach (PublicHoliday::all() as $holiday) {
                if (isset($holiday->year) && isset($holiday->month__no) && isset($holiday->day)) {
                    $holidayDate = Carbon::createFromDate($holiday->year, $holiday->month__no, $holiday->day);
                    $publicHolidays[] = $holidayDate->format('Y-m-d');
                }
            }
            $publicHolidays = array_unique($publicHolidays);

            $reports = StudentNouranyaReport::with('attendanceOptions', 'result')
                ->where('student_id', $studentId)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get()
                ->keyBy(fn ($report) => Carbon::parse($report->created_at)->format('Y-m-d'));

            $tableData = [];
            $period = CarbonPeriod::create($date->copy()->startOfMonth(), $date->copy()->endOfMonth());
            
            foreach ($period as $currentDate) {
                $dateString = $currentDate->format('Y-m-d');
                if (in_array($currentDate->dayOfWeekIso, $classDays) && !in_array($dateString, $publicHolidays)) {
                    $report = $reports->get($dateString);

                    $tableData[] = [
                        'day' => $currentDate->format('l'),
                        'date' => $currentDate->format('d/m/Y'),
                        'attendance' => $report && $report->attendanceOptions ? $report->attendanceOptions->name : ($report ? 'Present' : 'Absent'),
                        'lesson_range' => $report && $report->from_lesson ? "{$report->from_lesson} - {$report->to_lesson}" : '',
                        'unique_lessons' => $report && $report->from_lesson && $report->to_lesson ? ((int)$report->to_lesson - (int)$report->from_lesson + 1) : '',
                        'teacher_comments' => $report->nouranya_evaluation_note ?? '',
                        'supervisor_comments' => '', // Field does not exist in the model
                        'performance' => $report && $report->result ? $report->result->name : '',
                    ];
                }
            }

            return DataTables::of($tableData)
                ->addIndexColumn()
                ->rawColumns(['attendance', 'lesson_range', 'teacher_comments', 'performance'])
                ->make(true);

        } catch (\Exception $e) {
            \Log::error('Error in StudentNouranyaReportsDatatablesController: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return response()->json(['error' => 'An error occurred.'], 500);
        }
    }
} 
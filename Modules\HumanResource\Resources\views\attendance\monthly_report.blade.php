@extends('layouts.hound')

@section('mytitle', 'Monthly Attendance Report')

@section('content')

    <div class="row">
        <div class="col-md-12">
        </div>
    </div>

    <div class="panel-heading">
            <h4>
             <a class=" pull-right" href="{{ url('/workplace/humanresource/attendance') }}" title="Back"><button class="btn btn-warning btn-xs txt-light"><i class="fa fa-arrow-left txt-light" aria-hidden="true"></i> Back</button></a>
            </br>
            </h4>
            </div>

                <div class="col-sm-4">                            
                   
                </div>
            
               
                {!! Form::open(['url' => route('attendance.monthly_report_search'), 'class' => 'form-horizontal', 'method' => 'get']) !!}
                <div class="col-sm-3">
                            <select name="role" id="role" class="form-control" >
                            
                            <option value="0">All Rolles</option>
                              @foreach ($role as $item)
                             <option value="{{$item->id}}">{{$item->name}}</option>
                           @endforeach
                       
                        </select>                                    
                 </div>
           
                    <div class="col-sm-4  mb-15">
                        <div class="input-group">
                            
                            <button type="submit" class="btn btn-success btn-anim"><i class="icon-rocket"></i><span class="btn-text">Search</span></button>
                             
                        </div>                
                        
                    </div>
            
                    {!! Form::close() !!}
                   
             
                    

    <div class="result-set">
    
        <table class="table table-bordered table-striped table-hover" id="data-table">
            <thead>
            <tr>
                    <th>Employee ID</th>
                <th>Employee Name</th>
               
                <th  colspan="7" style="text-align: center;">Working Days</th> 
                <th>Requierd Days</th> 
                <th>Performed Days</th>
                <th>Requierd Hours</th>
                <th>Performed Hours</th>
            </tr>
            <tr>
                 <th colspan="2"></th>
                   
                        <td>Mon</td> 
                        <td>Tue</td>
                        <td>Wed</td>
                        <td>Thur</td>
                        <td>Fri</td>
                        <td>Sat</td>
                        <td>Sun</td>
                 <th colspan="4"></th> 
            </tr>
        
            </thead>
            <tbody>
              
            @if(isset($attendance))
          
            @foreach($items as $item)
            <?php  $t=0;
              ?> 
                <tr>
                        <td>{{$item['employee']->id}}</td>
                    <?php $max=0;?>
                    <td>{{$item['employee']->name}}</td>
                   
        
                 
                    @foreach ($item['employee']->salaries as $profile) 
                      @if ($max < $profile->id)
                  <?php $max=$profile->id;?>
                    @endif
                 
                    @endforeach


                    @if($max>0)
                    <?php $gethoure=\App\EmployeeTimetable::where(['employee_salary_id'=>$max])->get();?>
                    <td class="btn-success">
                        <?php  $number_days=null;
                               $number_hours=null;?>
                    @foreach($gethoure as $h)
                     
                    <?php $in=Carbon\Carbon::parse($h->clockin);
                    $out=Carbon\Carbon::parse($h->clockout);
                    $hours =$in->diffInHours($out)-$h->break;
               ?>
                        @if($h->day=='mon')
                        {{ $hours}} 
                        <?php $number_days[1]=App\Attendance::get_day_number(Carbon\Carbon::now()->year,Carbon\Carbon::now()->month,$h->day);
                              $number_hours[1]=$hours*$number_days[1];?>
                        @endif 
                       
                     @endforeach
                    </td>
                    <td class="btn-success">
                        @foreach($gethoure as $h)
                        <?php $in=Carbon\Carbon::parse($h->clockin);
                        $out=Carbon\Carbon::parse($h->clockout);
                        $hours =$in->diffInHours($out)-$h->break;?>
                            @if($h->day=='tue')
                            {{ $hours}}
                            <?php $number_days[2]=App\Attendance::get_day_number(Carbon\Carbon::now()->year,Carbon\Carbon::now()->month,$h->day);
                            $number_hours[2]=$hours*$number_days[2];?>
                            @endif
                            @endforeach
                        </td>
                        <td class="btn-success">

                            @foreach($gethoure as $h)
                            <?php $in=Carbon\Carbon::parse($h->clockin);
                            $out=Carbon\Carbon::parse($h->clockout);
                            $hours =$in->diffInHours($out)-$h->break;?>
                                @if($h->day=='wed')
                                {{ $hours}}
                                <?php $number_days[3]=App\Attendance::get_day_number(Carbon\Carbon::now()->year,Carbon\Carbon::now()->month,$h->day);
                                      $number_hours[3]=$hours*$number_days[3];?>
                                @endif 
                             @endforeach
                            </td>
                            <td class="btn-success">
                                @foreach($gethoure as $h)
                                <?php $in=Carbon\Carbon::parse($h->clockin);
                                $out=Carbon\Carbon::parse($h->clockout);
                                $hours =$in->diffInHours($out)-$h->break;?>
                                    @if($h->day=='thu')
                                    {{ $hours}} 
                                    <?php $number_days[4]=App\Attendance::get_day_number(Carbon\Carbon::now()->year,Carbon\Carbon::now()->month,$h->day);
                                          $number_hours[4]=$hours*$number_days[4];?>
                                    @endif 
                                 @endforeach
                                </td>
                                <td class="btn-success">

                                    @foreach($gethoure as $h)
                                    <?php $in=Carbon\Carbon::parse($h->clockin);
                                    $out=Carbon\Carbon::parse($h->clockout);
                                    $hours =$in->diffInHours($out)-$h->break;?>
                                        @if($h->day=='fri')
                                        {{ $hours}} 
                                        <?php $number_days[5]=App\Attendance::get_day_number(Carbon\Carbon::now()->year,Carbon\Carbon::now()->month,$h->day);
                                              $number_hours[5]=$hours*$number_days[5];?>
                                        @endif 
                                     @endforeach
                                    </td>
                                    <td class="btn-success">

                                        @foreach($gethoure as $h)
                                        <?php $in=Carbon\Carbon::parse($h->clockin);
                                        $out=Carbon\Carbon::parse($h->clockout);
                                        $hours =$in->diffInHours($out)-$h->break;?>
                                            @if($h->day=='sat')
                                            {{ $hours}}
                                            <?php $number_days[6]=App\Attendance::get_day_number(Carbon\Carbon::now()->year,Carbon\Carbon::now()->month,$h->day);
                                                 $number_hours[6]=$hours*$number_days[6];?>
                                            @endif 
                                         @endforeach
                                        </td>
                                        <td class="btn-success">

                                            @foreach($gethoure as $h)
                                            <?php $in=Carbon\Carbon::parse($h->clockin);
                                            $out=Carbon\Carbon::parse($h->clockout);
                                            $hours =$in->diffInHours($out)-$h->break;
                                               $t+= $hours;?>
                                                @if($h->day=='sun')
                                                {{ $hours}}
                                                <?php $number_days[7]=App\Attendance::get_day_number(Carbon\Carbon::now()->year,Carbon\Carbon::now()->month,$h->day);
                                                  $number_hours[7]=$hours*$number_days[7];?>
                                                @endif 
                                             @endforeach
                                            </td>
                                            <?php $days=0;
                                                  $hours=0?>
                                                  @if(isset($number_days))
                                            <td>@foreach($number_days as $h)
                                                <?php $days=$days+$h;?>

                                                @endforeach
                                                {{$days}}
                                                @endif
                                            </td>
                                            
                                           


                                            <td>
                                                <?php $att_hours=App\Attendance::whereBetween('clock', [Carbon\Carbon::now()->startOfMonth(), Carbon\Carbon::now()->endOfMonth()])->where('employee_id','=',$item['employee']->id)->orderBy('clock')->get();
                                            $in_da=null;
                                            $last_day=null;
                                            $count_day=0;
                                            foreach($att_hours as $atho){
                                                $in_da=$atho->clock->format('d');
                                              if($atho->type=='in' &&  $atho->clock->format('d')!=$last_day){
                                            
                                               $count_day=  $count_day + 1;
                                               $last_day= $in_da;
                                               }
                                              
                                          }
                            
                                             ?>

                                          {{ $count_day}}
                                            </td> 




                                            <td>   @if(isset($number_days))
                                                 @foreach($number_hours as $ho)
                                                <?php $hours=$hours+$ho;?>

                                                @endforeach
                                                {{ $hours}} 
                                                @endif</td> 


                                            <td><?php   $in_date=null;
                                            $out_date=null;
                                            $in_hour=null;
                                            $out_hour=null;
                                            $total_minutes=0;
                                            $total_hour=null;
                                            $diff_hour=null;
                                            $diff_minut=null;?>
                                                @foreach($att_hours as $atho)
                                      
                                              <?php 
                                              if($atho->type=='in'){
                                             $in_date=$atho->clock->format('d');
                                              $in_hour=Carbon\Carbon::parse($atho->clock);}
                                           else{
                                            $out_date=$atho->clock->format('d');
                                            $out_hour=Carbon\Carbon::parse($atho->clock);
                                                }
                                             if( $out_date==$in_date){
                                                $total_hour =   $total_hour +$in_hour->diffInMinutes($out_hour)


                                               ?>
                                               

                                            <?php }
                                         
                                               ?>  
                                                @endforeach
                                                {{ intval( $total_hour/60)}}: {{  $total_hour  % 60}}

                                                
                                           
                                            </td> 
                     @else

                <td></td> 
                <td></td> 
                <td></td> 
                <td></td> 
                <td></td> 
                <td></td> 
                <td></td>
                <td></td> 
                <td></td> 
                <td></td> 
                <td></td> 
                
                      @endif
            
        @endforeach
        @else
        
        @endif
   
</tr>
            </tbody>
        </table>
        
        <div class="text-center">
        </div>
    </div>


@endsection


   
    
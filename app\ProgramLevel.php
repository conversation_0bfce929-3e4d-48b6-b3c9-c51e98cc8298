<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Translatable;

class ProgramLevel extends Model
{

    use Translatable;

    public $translatedAttributes = array('title');

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'program_levels';

    /**
     * The database primary key value.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['program_id', 'status','from_surat','to_surat','from_lesson','to_lesson','program_level_order'];



    // In the ProgramLevel model

    public function studentProgramLevels()
    {
        return $this->hasMany(StudentProgramLevel::class, 'level_id');
    }









    public function program()
    {
        return $this->belongsTo('App\Program');
    }

    public function student()

    {
        return $this->hasMany('App\Student', 'level', 'id');
    }

    function subjects()
    {
        return $this->belongsToMany(Subject::class, 'program_level_subjects', 'program_level_id', 'subject_id');
    }

    public function arabic()
    {
//        return $this->hasMany('App\ProgramTranslation');
        return $this->hasMany(ProgramLevelTranslation::class)->where("locale",'ar');
    }
    protected static function boot()
    {
        parent::boot();

        ProgramLevel::creating(function ($model) {
            // Ensure that program_id is set
            if (request()->filled('program_id')) {
                // Assign program_level_order based on the max order for the given program_id, plus one
                $maxOrder = ProgramLevel::where('program_id', $model->program_id)->max('program_level_order');
                $model->program_level_order = $maxOrder ? $maxOrder + 1 : 1;
            }
        });
    }

    // In App\ProgramLevel

    public function lessons()
    {
        return $this->hasMany('App\ProgramLevelLesson', 'program_level_id');
    }


}

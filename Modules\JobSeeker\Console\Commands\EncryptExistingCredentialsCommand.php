<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Encrypt Existing Credentials Command
 * 
 * This command encrypts plaintext credentials that already exist in the
 * jobseeker_settings table to ensure security compliance. It should be
 * run once during deployment to secure existing data.
 */
final class EncryptExistingCredentialsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'jobseeker:encrypt-credentials 
                            {--dry-run : Show what would be encrypted without making changes}
                            {--force : Skip confirmation prompt}';

    /**
     * The console command description.
     */
    protected $description = 'Encrypt existing plaintext credentials in jobseeker_settings table';

    /**
     * List of setting keys that contain sensitive credentials
     */
    private const CREDENTIAL_KEYS = [
        'gmail_password',
        'gmail_username', 
        'mailtrap_api_key',
        'mailtrap_username',
        'mailtrap_password',
    ];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('JobSeeker Credential Encryption Tool');
        $this->info('=====================================');

        try {
            // Check if jobseeker_settings table exists
            if (!$this->tableExists()) {
                $this->error('The jobseeker_settings table does not exist.');
                return 1;
            }

            // Find credentials that need encryption
            $credentialsToEncrypt = $this->findPlaintextCredentials();

            if ($credentialsToEncrypt->isEmpty()) {
                $this->info('✅ No plaintext credentials found. All credentials are already encrypted or don\'t exist.');
                return 0;
            }

            $this->displayCredentialsFound($credentialsToEncrypt);

            // Dry run mode
            if ($this->option('dry-run')) {
                $this->info('🔍 DRY RUN MODE: No changes will be made.');
                return 0;
            }

            // Confirmation prompt
            if (!$this->option('force') && !$this->confirm('Do you want to encrypt these credentials?')) {
                $this->info('Operation cancelled by user.');
                return 0;
            }

            // Encrypt the credentials
            $encryptedCount = $this->encryptCredentials($credentialsToEncrypt);

            $this->info("✅ Successfully encrypted {$encryptedCount} credentials.");
            $this->info('🔒 All sensitive data is now secured in the database.');

            return 0;

        } catch (Exception $e) {
            $this->error("❌ Error: {$e->getMessage()}");
            Log::error('EncryptExistingCredentialsCommand failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    /**
     * Check if the jobseeker_settings table exists
     */
    private function tableExists(): bool
    {
        try {
            return DB::getSchemaBuilder()->hasTable('jobseeker_settings');
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Find credentials that appear to be in plaintext
     */
    private function findPlaintextCredentials()
    {
        return DB::table('jobseeker_settings')
            ->whereIn('key', self::CREDENTIAL_KEYS)
            ->whereNotNull('value')
            ->where('value', '!=', '')
            ->get()
            ->filter(function ($setting) {
                // Check if the value is already encrypted
                // Laravel encrypted values start with "eyJpdiI6" (base64 encoded JSON)
                return !$this->isAlreadyEncrypted($setting->value);
            });
    }

    /**
     * Check if a value is already encrypted by Laravel
     */
    private function isAlreadyEncrypted(string $value): bool
    {
        // Laravel encrypted values are base64 encoded JSON
        // They typically start with "eyJpdiI6" when base64 decoded starts with '{"iv":'
        try {
            $decoded = base64_decode($value, true);
            if ($decoded === false) {
                return false;
            }
            
            $json = json_decode($decoded, true);
            return is_array($json) && isset($json['iv']) && isset($json['value']) && isset($json['mac']);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Display the credentials that were found
     */
    private function displayCredentialsFound($credentials): void
    {
        $this->info("Found {$credentials->count()} plaintext credentials:");
        $this->table(
            ['ID', 'Key', 'Value Preview', 'Length'],
            $credentials->map(function ($credential) {
                return [
                    $credential->id,
                    $credential->key,
                    $this->maskValue($credential->value),
                    strlen($credential->value),
                ];
            })->toArray()
        );
    }

    /**
     * Mask sensitive values for display
     */
    private function maskValue(string $value): string
    {
        if (strlen($value) <= 8) {
            return str_repeat('*', strlen($value));
        }
        
        return substr($value, 0, 3) . str_repeat('*', strlen($value) - 6) . substr($value, -3);
    }

    /**
     * Encrypt the credentials and update the database
     */
    private function encryptCredentials($credentials): int
    {
        $encryptedCount = 0;
        $progressBar = $this->output->createProgressBar($credentials->count());
        $progressBar->start();

        foreach ($credentials as $credential) {
            try {
                // Encrypt the value
                $encryptedValue = encrypt($credential->value);

                // Update the database
                DB::table('jobseeker_settings')
                    ->where('id', $credential->id)
                    ->update([
                        'value' => $encryptedValue,
                        'updated_at' => now(),
                    ]);

                $encryptedCount++;
                
                Log::info('Credential encrypted', [
                    'setting_id' => $credential->id,
                    'key' => $credential->key,
                ]);

            } catch (Exception $e) {
                $this->error("\n❌ Failed to encrypt credential ID {$credential->id} ({$credential->key}): {$e->getMessage()}");
                Log::error('Failed to encrypt credential', [
                    'setting_id' => $credential->id,
                    'key' => $credential->key,
                    'error' => $e->getMessage(),
                ]);
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->line('');

        return $encryptedCount;
    }
} 
<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\Classes;
use App\IjazasanadMemorizationPlan;
use App\Student;
use App\StudentIjazasanadMemorizationReport;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * IjazasanadClassSummarySheet creates the Class Summary sheet for Ijazah & Sanad reports.
 * 
 * Purpose: Export per-class summary metrics for Ijazah & Sanad programs aggregated across students.
 * Data source: Aggregates from student_ijazasanad_memorization_report and ijazasanad_memorization_plans.
 * Calculations: Average attendance/achievement, total planned/completed Juz and pages metrics.
 * Context: Mirrors the aggregated DataTables structure from MonthEndIjazasanadSummaryAggregatedController.
 * Output: Single sheet with per-class summary rows showing key performance indicators.
 */
final class IjazasanadClassSummarySheet implements WithTitle, WithEvents
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get class summary data aggregated across students
     */
    private function getClassSummaryData(): array
    {
        $classIds = $this->filters['classIds'];
        $month = $this->filters['month'];
        $year = $this->filters['year'];
        $studentIds = $this->filters['studentIds'] ?? [];

        $results = [];

        foreach ($classIds as $classId) {
            $class = Classes::find($classId);
            if (!$class) {
                continue;
            }

            // Load active students for this class
            $query = Student::whereHas('joint_classes', function ($q) use ($classId) {
                $q->where('class_id', $classId);
            })
            ->where('status', 'active')
            ->orderBy('full_name', 'asc');

            if (!empty($studentIds)) {
                $query->whereIn('id', $studentIds);
            }

            $students = $query->get();

            $studentCount = $students->count();
            $avgAttendance = $this->calculateAverageAttendance($students, $classId, $month, $year);
            $avgAchievement = $this->calculateAverageAchievement($students, $classId, $month, $year);

            $juzMetrics = $this->calculateJuzMetrics($students, $classId, $month, $year);
            $pageMetrics = $this->calculatePageMetrics($students, $classId, $month, $year);

            $teacherNames = $class->teachers ? $class->teachers->pluck('full_name')->join(', ') : '';
            $programTitle = $class->programs ? optional($class->programs->first())->title : null;

            $results[] = [
                'center_name' => $class->center->name ?? 'Unknown Center',
                'class_name' => $class->name ?? 'Unknown Class',
                'class_program' => $programTitle ?? 'N/A',
                'teacher_name' => $teacherNames ?: 'N/A',
                'no_of_students' => $studentCount,
                'avg_attendance' => number_format($avgAttendance, 1) . '%',
                'avg_achievement' => number_format($avgAchievement, 1) . '%',
                'total_planned_juz' => $juzMetrics['planned'],
                'total_completed_juz' => $juzMetrics['completed'],
                'juz_progress' => number_format($juzMetrics['percentage'], 1) . '%',
                'total_planned_pages' => $pageMetrics['planned'],
                'total_completed_pages' => $pageMetrics['completed'],
                'page_progress' => number_format($pageMetrics['percentage'], 1) . '%',
            ];
        }

        return $results;
    }

    /**
     * Calculate average attendance across students in a class
     */
    private function calculateAverageAttendance($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) {
            return 0.0;
        }

        $class = Classes::find($classId);
        if (!$class || !$class->timetable) {
            return 0.0;
        }

        $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
        if ($totalClasses <= 0) {
            return 0.0;
        }

        $sum = 0;
        $count = 0;

        foreach ($students as $student) {
            $attended = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereIn('attendance_id', [1, 2]) // Late and On Time
                ->count();

            $sum += min(100.0, ($attended / $totalClasses) * 100);
            $count++;
        }

        return $count > 0 ? $sum / $count : 0.0;
    }

    /**
     * Calculate average achievement across students in a class
     */
    private function calculateAverageAchievement($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) {
            return 0.0;
        }

        $sum = 0;
        $count = 0;

        foreach ($students as $student) {
            $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
                ->where(function ($q) use ($month, $year, $classId) {
                    $q->where(function ($q1) use ($year, $month, $classId) {
                        $q1->whereYear('created_at', $year)
                           ->whereMonth('created_at', $month)
                           ->where('class_id', $classId)
                           ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                           ->whereMonth('start_date', $month)
                           ->where('class_id', $classId)
                           ->where('status', 'active');
                    });
                })
                ->first();

            if (!$plan) {
                continue;
            }

            $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            if ($reports->isEmpty()) {
                continue;
            }

            $student->loadMissing('studentProgramLevels.programlevel');
            $level = $this->detectStudentLevel($student);
            $percent = ($level === 'level1') ? 
                $this->calculateLevel1Completion($plan, $reports)['completion_rate'] : 
                $this->calculateLevel2Completion($plan, $reports)['completion_rate'];

            if ($percent > 0) {
                $sum += $percent;
                $count++;
            }
        }

        return $count > 0 ? $sum / $count : 0.0;
    }

    /**
     * Calculate Juz metrics (planned vs completed)
     */
    private function calculateJuzMetrics($students, int $classId, int $month, int $year): array
    {
        if ($students->isEmpty()) {
            return ['planned' => 0, 'completed' => 0, 'percentage' => 0];
        }

        $planned = 0;
        $completed = 0;

        foreach ($students as $student) {
            $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
                ->where(function ($q) use ($month, $year, $classId) {
                    $q->where(function ($q1) use ($year, $month, $classId) {
                        $q1->whereYear('created_at', $year)
                           ->whereMonth('created_at', $month)
                           ->where('class_id', $classId)
                           ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                           ->whereMonth('start_date', $month)
                           ->where('class_id', $classId)
                           ->where('status', 'active');
                    });
                })
                ->first();

            if ($plan && $plan->from_surat_juz_id && $plan->to_surat_juz_id) {
                $planned += max(0, $plan->to_surat_juz_id - $plan->from_surat_juz_id + 1);
            }

            $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereNotNull('hefz_from_surat')
                ->whereNotNull('hefz_to_surat')
                ->get();

            if ($reports->isNotEmpty()) {
                $completed += $this->calculateAchievedJuzFromReports($reports);
            }
        }

        $percentage = $planned > 0 ? min(100, round(($completed / $planned) * 100, 1)) : 0;

        return ['planned' => $planned, 'completed' => $completed, 'percentage' => $percentage];
    }

    /**
     * Calculate achieved Juz from reports
     */
    private function calculateAchievedJuzFromReports($reports): int
    {
        $juzSet = collect();
        foreach ($reports as $report) {
            $from = (int) ceil(($report->hefz_from_surat ?? 1) / 4);
            $to = (int) ceil(($report->hefz_to_surat ?? 1) / 4);
            for ($j = $from; $j <= $to; $j++) {
                $juzSet->push($j);
            }
        }
        return $juzSet->unique()->count();
    }

    /**
     * Calculate page metrics (planned vs completed)
     */
    private function calculatePageMetrics($students, int $classId, int $month, int $year): array
    {
        if ($students->isEmpty()) {
            return ['planned' => 0, 'completed' => 0, 'percentage' => 0];
        }

        $planned = 0;
        $completed = 0;

        foreach ($students as $student) {
            $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
                ->where(function ($q) use ($month, $year, $classId) {
                    $q->where(function ($q1) use ($year, $month, $classId) {
                        $q1->whereYear('created_at', $year)
                           ->whereMonth('created_at', $month)
                           ->where('class_id', $classId)
                           ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                           ->whereMonth('start_date', $month)
                           ->where('class_id', $classId)
                           ->where('status', 'active');
                    });
                })
                ->first();

            if ($plan) {
                $plannedPages = 0;
                try {
                    if ($plan->study_direction == 'backward') {
                        $rows = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                            $plan->start_from_surat, $plan->start_from_ayat, $plan->to_surat, $plan->to_ayat
                        ]);
                        $plannedPages = $rows[0]->numberofPagesSum ?? 0;
                    } else {
                        DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                            $plan->start_from_surat, $plan->start_from_ayat, $plan->to_surat, $plan->to_ayat
                        ]);
                        $res = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                        $plannedPages = $res[0]->number_of_pages_sum ?? 0;
                    }
                } catch (\Throwable $e) {
                    $plannedPages = 0;
                }
                $planned += $plannedPages;
            }

            $completed += (int) (StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->sum('pages_memorized') ?? 0);
        }

        $percentage = $planned > 0 ? min(100, round(($completed / $planned) * 100, 1)) : 0;

        return ['planned' => $planned, 'completed' => $completed, 'percentage' => $percentage];
    }

    /**
     * Helper methods
     */
    private function detectStudentLevel($studentDetails): ?string
    {
        foreach ($studentDetails->studentProgramLevels as $spl) {
            if ($spl->programlevel) {
                $n = strtolower($spl->programlevel->title);
                if (str_contains($n, 'level 1')) return 'level1';
                if (str_contains($n, 'level 2')) return 'level2';
            }
        }
        return null;
    }

    private function calculateLevel1Completion($plan, $reports): array
    {
        // Simplified calculation
        $components = ['talqeen', 'revision', 'jazariyah', 'seminars'];
        $total = 0;
        $valid = 0;
        foreach ($components as $n) {
            $f = "{$n}_from_lesson";
            $t = "{$n}_to_lesson";
            if (!empty($plan->$f) && !empty($plan->$t) && $plan->$f <= $plan->$t) {
                $valid++;
                $total += 25; // Equal weight
            }
        }
        $overall = $valid > 0 ? $total / $valid : 0;
        return ['completion_rate' => round($overall, 2)];
    }

    private function calculateLevel2Completion($plan, $reports): array
    {
        $achievedPages = $reports->sum('pages_memorized') ?? 0;
        $plannedPages = $this->calculatePlannedPages($plan);
        $percentage = $plannedPages > 0 ? round(($achievedPages / $plannedPages) * 100) : 0;
        return ['completion_rate' => $percentage];
    }

    private function calculatePlannedPages($plan): int
    {
        if (!$plan->start_from_surat || !$plan->start_from_ayat || !$plan->to_surat || !$plan->to_ayat) {
            return 0;
        }

        try {
            if ($plan->study_direction === 'backward') {
                $result = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                    $plan->start_from_surat, $plan->start_from_ayat, $plan->to_surat, $plan->to_ayat
                ]);
                return $result[0]->numberofPagesSum ?? 0;
            } else {
                DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                    $plan->start_from_surat, $plan->start_from_ayat, $plan->to_surat, $plan->to_ayat
                ]);
                $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                return $results[0]->number_of_pages_sum ?? 0;
            }
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get table headings
     */
    private function getTableHeadings(): array
    {
        return [
            'Center',
            'Class',
            'No. of Students',
            'Avg. Attendance %',
            'Avg. Achievement %',
            'Total Planned Juz',
            'Total Completed Juz',
            'Juz Progress %',
            'Total Planned Pages',
            'Total Completed Pages',
            'Page Progress %'
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Class Summary';
    }

    /**
     * Register events for creating the styled table
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $this->createStyledTable($event->sheet);
            },
        ];
    }

    /**
     * Create styled table with data
     */
    private function createStyledTable($sheet)
    {
        $worksheet = $sheet->getDelegate();
        
        // Get data and headings
        $classData = $this->getClassSummaryData();
        $headings = $this->getTableHeadings();

        // Set main title
        $monthName = $this->filters['monthName'];
        $year = $this->filters['year'];
        $classNames = collect($this->filters['classes'])->pluck('class_code')->join(', ');
        
        $title = "IJAZAH & SANAD CLASS SUMMARY - {$classNames} - {$monthName} {$year}";
        $worksheet->setCellValue('A1', $title);
        $worksheet->mergeCells('A1:K1');

        // Style main title
        $worksheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 16, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '1f4e79']]
        ]);

        $currentRow = 3;

        // Set headers
        $col = 'A';
        foreach ($headings as $heading) {
            $worksheet->setCellValue($col . $currentRow, $heading);
            $col++;
        }

        // Style headers
        $worksheet->getStyle("A{$currentRow}:K{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2f75b5']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $dataStartRow = $currentRow + 1;
        $currentDataRow = $dataStartRow;

        // Add data rows
        foreach ($classData as $row) {
            $col = 'A';
            foreach ($row as $value) {
                $worksheet->setCellValue($col . $currentDataRow, $value);
                $col++;
            }
            $currentDataRow++;
        }

        // Style data rows
        if (count($classData) > 0) {
            $dataEndRow = $currentDataRow - 1;
            $worksheet->getStyle("A{$dataStartRow}:K{$dataEndRow}")->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ]);

            // Center-align numeric columns
            $worksheet->getStyle("C{$dataStartRow}:K{$dataEndRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        } else {
            // No data message
            $worksheet->setCellValue("A{$dataStartRow}", 'No Ijazah & Sanad class data found for the specified criteria');
            $worksheet->mergeCells("A{$dataStartRow}:K{$dataStartRow}");
            $worksheet->getStyle("A{$dataStartRow}")->applyFromArray([
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'font' => ['italic' => true],
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
                ]
            ]);
        }

        // Auto-size columns
        foreach (range('A', 'K') as $column) {
            $worksheet->getColumnDimension($column)->setAutoSize(true);
        }
    }
}

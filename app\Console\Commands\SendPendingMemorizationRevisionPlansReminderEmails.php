<?php

namespace App\Console\Commands;

use App\Employee;
use App\Services\EmailService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SendPendingMemorizationRevisionPlansReminderEmails extends Command
{
    protected $signature = 'email:send-pending-memorization-revision-plans-reminder';
    protected $description = 'Send reminder emails to employees with pending memorization and revision plans awaiting approval.';
    
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        try {
            Log::info('Starting email:send-pending-memorization-revision-plans-reminder command');
            $this->info('Processing memorization/revision reminders...');
            
            // Start transaction
            DB::beginTransaction();

            // Determine if we should run based on frequency settings
            $shouldRun = $this->shouldRunBasedOnFrequency();
            
            if (!$shouldRun) {
                Log::info("Skipping Memorization Revision reminder as frequency does not match today.");
                $this->info("Command skipped: Frequency does not match today's date.");
                DB::commit();
                return Command::SUCCESS;
            }

            // Get excluded emails from the database
            $excludedEmails = $this->getExcludedEmails();
            Log::info("Found " . count($excludedEmails) . " excluded emails");

            // Find supervisors with pending plans
            $supervisors = $this->getSupervisorsWithPendingPlans();
            
            if ($supervisors->isEmpty()) {
                Log::info("No supervisors found with pending memorization/revision plans.");
                $this->info("No supervisors found with pending plans.");
                DB::commit();
                return Command::SUCCESS;
            }
            
            Log::info("Found " . $supervisors->count() . " supervisors with pending plans");
            $this->info("Found " . $supervisors->count() . " supervisors with pending plans");

            // Calculate total pending plans
            $totalPending = $this->calculateTotalPendingPlans($supervisors);
            
            if ($totalPending === 0) {
                Log::info('No pending memorization/revision plans awaiting approval.');
                $this->info('No pending plans found that need approval.');
                DB::commit();
                return Command::SUCCESS;
            }
            
            Log::info("Total of {$totalPending} plans pending approval");
            $this->info("Total of {$totalPending} plans pending approval");

            // Send emails to supervisors
            $emailsSent = 0;
            $emailsFailed = 0;

            // Process each supervisor
            foreach ($supervisors as $supervisor) {
                if (in_array($supervisor->email, $excludedEmails)) {
                    Log::info("Skipping excluded email: {$supervisor->email}");
                    $this->line("Skipping excluded email: {$supervisor->email}");
                    continue;
                }

                try {
                    // Calculate plan count for this supervisor
                    $planCount = $this->calculateSupervisorPendingPlans($supervisor);
                    
                    if ($planCount === 0) {
                        Log::info("Supervisor {$supervisor->email} has no pending plans, skipping");
                        continue;
                    }

                    // Prepare email data
                    $to = [
                        'email' => $supervisor->email,
                        'name'  => $supervisor->name ?? ''
                    ];

                    $subject = 'Memorization and Revision Pending Plans Awaiting Your Approval';
                    $view = 'emails.pending_plans_reminder';
                    $viewData = [
                        'employee'     => $supervisor,
                        'planCount'    => $planCount,
                        'totalPending' => $totalPending,
                    ];

                    // Send the email
                    $emailService = app(\App\Services\EmailService::class);
                    $emailService->sendEmail($to, $subject, $view, $viewData);
                    $emailsSent++;
                    
                    Log::info("Email sent to {$supervisor->email} for {$planCount} pending plans");
                    $this->line("Email sent to {$supervisor->email} for {$planCount} pending plans");
                    
                } catch (\Exception $e) {
                    $emailsFailed++;
                    Log::error("Failed to send email to {$supervisor->email}: " . $e->getMessage());
                    $this->error("Failed to send email to {$supervisor->email}: " . $e->getMessage());
                    // Continue with next supervisor, don't let one failure stop the whole process
                }
            }

            DB::commit();
            
            $message = "Command completed: {$emailsSent} emails sent, {$emailsFailed} failed";
            Log::info($message);
            $this->info($message);
            
            return ($emailsFailed > 0 && $emailsSent === 0) ? Command::FAILURE : Command::SUCCESS;
            
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error("Command failed: " . $exception->getMessage());
            Log::error($exception->getTraceAsString());
            $this->error('Failed to send memorization/revision reminder emails: ' . $exception->getMessage());
            return Command::FAILURE;
        }
    }
    
    /**
     * Determine if the command should run based on frequency settings
     * 
     * @return bool
     */
    private function shouldRunBasedOnFrequency(): bool
    {
        $settings = \App\MemorizationRevisionEmailSetting::first();
        $today = Carbon::now()->timezone('Asia/Kuala_Lumpur');
        
        if (!$settings) {
            return true; // If no settings, run by default
        }
        
        switch ($settings->frequency) {
            case 'daily':
                return true;
            case 'weekly':
                return $today->format('D') === substr($settings->weekly_day, 0, 3);
            case 'monthly':
                return $today->format('j') == $settings->monthly_day;
            default:
                return true;
        }
    }
    
    /**
     * Get list of excluded emails
     * 
     * @return array
     */
    private function getExcludedEmails(): array
    {
        return DB::table('memorizationrevision_email_exclusions')->pluck('email')->toArray();
    }
    
    /**
     * Get supervisors with pending plans
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getSupervisorsWithPendingPlans()
    {
        return Employee::query()
            ->whereHas('center.hefzPlan', function ($planQuery) {
                $planQuery->whereNotNull('start_from_surat')
                    ->whereNotNull('start_from_ayat')
                    ->whereNotNull('to_surat')
                    ->whereNotNull('to_ayat')
                    ->whereNotNull('center_id')
                    ->where('status', 'waiting_for_approval');
            })
            ->with([
                'center' => function ($centerQuery) {
                    $centerQuery->with(['hefzPlan' => function ($planQuery) {
                        $planQuery->whereNotNull('start_from_surat')
                            ->whereNotNull('start_from_ayat')
                            ->whereNotNull('to_surat')
                            ->whereNotNull('to_ayat')
                            ->whereNotNull('center_id')
                            ->where('status', 'waiting_for_approval');
                    }]);
                }
            ])
            ->get();
    }
    
    /**
     * Calculate total pending plans across all supervisors
     * 
     * @param \Illuminate\Database\Eloquent\Collection $supervisors
     * @return int
     */
    private function calculateTotalPendingPlans($supervisors): int
    {
        $totalPending = 0;
        foreach ($supervisors as $supervisor) {
            $totalPending += $this->calculateSupervisorPendingPlans($supervisor);
        }
        return $totalPending;
    }
    
    /**
     * Calculate pending plan count for a single supervisor
     * 
     * @param Employee $supervisor
     * @return int
     */
    private function calculateSupervisorPendingPlans(Employee $supervisor): int
    {
        return $supervisor->center->sum(function ($center) {
            return $center->hefzPlan->count();
        });
    }
}

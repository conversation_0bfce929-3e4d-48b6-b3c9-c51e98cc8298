@startuml Database Documentation Flow

skinparam backgroundColor white
skinparam handwritten false
skinparam DefaultFontName "Arial"
skinparam DefaultFontSize 14
skinparam roundcorner 15
skinparam ArrowColor #2c3e50
skinparam PackageBackgroundColor WhiteSmoke

title Database Documentation Flow

' Define participants with custom styles
participant "db:docs\nArtisan Command" as DBDocsCommand #98ddde
database "Database Schema" as Schema #e1e8f0
artifact "Generated HTML\nai_docs/architectureDiagrams/\ncomplete_database_documentation_with_tables.html" as HTMLFile #f2dede
control "CommandsController\nviewDatabaseDocs()" as Controller #dff0d8
boundary "database_documentation.blade.php\n(Template)" as Template #fcf8e3
actor "User" as User #fefefe

' Define triggers
note right of DBDocsCommand
  Triggered by:
  - Manual regeneration button
  - Weekly scheduler (Sundays 1:00 AM)
  - After migrations in non-prod environments
end note

' Define flow
User -> Controller : Request to view database docs
activate Controller

Controller -> HTMLFile : Read HTML file
activate HTMLFile
HTMLFile --> Controller : Return HTML content
deactivate HTMLFile

Controller -> Controller : Extract components using regex:\n1. Table links for sidebar\n2. Table content for main display\n3. Last updated timestamp

Controller -> Template : Pass extracted data
activate Template
Template --> User : Render final view with:\n- Fixed sidebar navigation\n- Clickable table index\n- Highlighted table display\n- Back to top button
deactivate Template
deactivate Controller

' Generation flow (separate)
note over User, Schema : The documentation generation process

DBDocsCommand -> Schema : Query for all tables, columns, and relationships
activate DBDocsCommand
activate Schema
Schema --> DBDocsCommand : Return database structure
deactivate Schema

DBDocsCommand -> HTMLFile : Generate and write HTML file
activate HTMLFile
HTMLFile --> DBDocsCommand : File created successfully
deactivate HTMLFile
deactivate DBDocsCommand

@enduml 
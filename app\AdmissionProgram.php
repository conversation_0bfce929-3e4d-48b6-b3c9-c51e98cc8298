<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Translatable;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Relations\Pivot;

class AdmissionProgram extends Pivot
{
//    use Translatable;

//    public $translatedAttributes = array('description', 'title');

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'admission_program';

    /**
     * The database primary key value.
     *
     * @var string
     */
//    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
//    protected $fillable = ['code', 'status', 'language', 'require_interview', 'organization_id'];


//    protected static function boot()
//    {
//        parent::boot();
//
//        static::addGlobalScope(new OrganizationScope);
//    }
}

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Province model for Afghanistan locations
 * 
 * Manages the 34 provinces of Afghanistan for location-based filtering
 * in jobs.af API requests and notification setups.
 */
final class Province extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'provinces';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'name_pashto',
        'name_dari',
        'code',
        'is_active'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get all active provinces
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get province by code
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $code
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCode($query, string $code)
    {
        return $query->where('code', $code);
    }

    /**
     * Search provinces by name (supports English, Pashto, and Dari)
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'LIKE', "%{$search}%")
              ->orWhere('name_pashto', 'LIKE', "%{$search}%")
              ->orWhere('name_dari', 'LIKE', "%{$search}%")
              ->orWhere('code', 'LIKE', "%{$search}%");
        });
    }

    /**
     * Get provinces formatted for Select2 dropdown
     *
     * @return array
     */
    public static function getForSelect2(): array
    {
        return static::active()
            ->orderBy('name')
            ->get()
            ->map(function ($province) {
                return [
                    'id' => $province->name, // Use name as value for jobs.af API compatibility
                    'text' => $province->name,
                    'code' => $province->code,
                    'pashto' => $province->name_pashto,
                    'dari' => $province->name_dari
                ];
            })
            ->toArray();
    }

    /**
     * Get the display name with local language support
     *
     * @param string $locale
     * @return string
     */
    public function getDisplayName(string $locale = 'en'): string
    {
        switch ($locale) {
            case 'ps':
                return $this->name_pashto ?: $this->name;
            case 'fa':
            case 'dr':
                return $this->name_dari ?: $this->name;
            default:
                return $this->name;
        }
    }

    /**
     * Check if this is Kabul province (commonly used default)
     *
     * @return bool
     */
    public function isKabul(): bool
    {
        return strtolower($this->name) === 'kabul' || $this->code === 'KBL';
    }
} 
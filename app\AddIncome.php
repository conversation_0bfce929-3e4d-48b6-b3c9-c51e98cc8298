<?php

namespace App;

use App\ItemSell;
use App\AddExpense;
use App\FeesPayment;
use App\ItemReceive;
use App\HrPayrollGenerate;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;

class AddIncome extends Model
{
    public function incomeHeads(){
    	return $this->belongsTo('App\IncomeHead','income_head_id', 'id');
    }

    public function ACHead(){
    	return $this->belongsTo('App\ChartOfAccount','income_head_id', 'id');
    }

    public function account(){
    	return $this->belongsTo('App\BankAccount', 'account_id', 'id');
    }

    public function paymentMethod(){
    	return $this->belongsTo('App\PaymentMethhod', 'payment_method_id', 'id');
    }

    public function scopeAddIncome($query,$date_from, $date_to, $payment_method){
            return $query->where('date', '>=', $date_from)
            ->where('date', '<=', $date_to)
            ->where('active_status', 1)
            
            ->where('payment_method_id',$payment_method);
    }

    public static function monthlyIncome($i){
        try {
            $m_add_incomes = AddIncome::where('academic_id',getAcademicId())
                            ->where('name','!=','Fund Transfer')
                            
                            ->where('active_status', 1)
                            ->where('date', 'like', date('Y-m-').$i)
                            ->sum('amount');

            $m_total_income = $m_add_incomes;

            return $m_total_income;
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    } 

    public static function monthlyExpense($i){
        try {
            $m_add_expenses = AddExpense::where('academic_id',getAcademicId())
                            ->where('name','!=','Fund Transfer')
                            
                            ->where('active_status', 1)
                            ->where('date', 'like', date('Y-m-').$i)
                            ->sum('amount');
            $m_total_expense = $m_add_expenses;
                return $m_total_expense;
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    } 


    public static function yearlyIncome($i){
        try {
            $y_add_incomes = AddIncome::where('academic_id',getAcademicId())
                            ->where('name','!=','Fund Transfer')
                            
                            ->where('active_status', 1)
                            ->where('date', 'like', date('Y-'.$i).'%')
                            ->sum('amount');

            $y_total_income = $y_add_incomes;
         
            return $y_total_income;
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    } 

    public static function yearlyExpense($i){
        try {
            $m_add_expenses = AddExpense::where('academic_id',getAcademicId())
                            ->where('name','!=','Fund Transfer')
                            
                            ->where('active_status', 1)
                            ->where('date', 'like', date('Y-'.$i).'%')
                            ->sum('amount');
            $m_total_expense = $m_add_expenses;
            return $m_total_expense;
        } catch (\Exception $e) {
            $data=[];
            return $data;
        }
    } 
}

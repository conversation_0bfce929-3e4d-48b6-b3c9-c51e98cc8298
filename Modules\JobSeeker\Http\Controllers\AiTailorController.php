<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Controllers;

use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\Job;
use Mo<PERSON>les\JobSeeker\Http\Requests\AiTailorRunRequest;
use Mo<PERSON>les\JobSeeker\Services\Ai\AiResumeService;
use Modules\JobSeeker\Services\Ai\AiTailorService;

/**
 * AiTailorController orchestrates the AI resume tailoring user interface and pipeline execution.
 * 
 * Purpose: Provide entry point from email CTA and manage the complete tailoring workflow.
 * User journey: Job slug → resume selection page → AI processing → results display.
 * Security: Enforces job_seeker authentication, permission checks, and job ownership validation.
 * UI/UX: Bootstrap 5 responsive interface with loading states, progress indicators, and error handling.
 * Performance: Async processing with real-time status updates via JSON API endpoints.
 */
final class AiTailorController extends Controller
{
    public function __construct(
        private readonly AiResumeService $resumeService,
        private readonly AiTailorService $tailorService
    ) {
        // All routes require job_seeker authentication
        $this->middleware('auth:job_seeker');
    }

    /**
     * Show the resume selection and AI tailoring page for a specific job
     * 
     * @param Request $request
     * @param string $slug Job slug from email CTA or direct access
     * @return View The main tailoring interface
     */
    public function show(Request $request, string $slug): View
    {
        $user = auth('job_seeker')->user();
        $correlationId = 'tailor_show_' . uniqid();

        Log::info('AiTailorController: Loading tailoring page', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'job_slug' => $slug,
            'request_ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
        ]);

        try {
            // Find the job by slug
            $job = Job::where('slug', $slug)->first();

            if (!$job) {
                Log::warning('AiTailorController: Job not found for slug', [
                    'correlation_id' => $correlationId,
                    'job_seeker_id' => $user->id,
                    'job_slug' => $slug,
                ]);

                abort(404, 'Job not found');
            }

            // Get user's resumes
            $resumes = $this->resumeService->listForUser($user->id);
            $canAddResumes = $this->resumeService->canAddMore($user->id);

            // Get recent tailoring runs for this user
            $recentRuns = $this->tailorService->getRecentRuns($user->id, 5);

            Log::info('AiTailorController: Successfully loaded tailoring page', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'job_id' => $job->id,
                'job_title' => $job->title,
                'resume_count' => $resumes->count(),
                'recent_runs_count' => $recentRuns->count(),
            ]);

            return view('modules.jobseeker.ai.tailor-page', compact(
                'job',
                'resumes',
                'canAddResumes',
                'recentRuns',
                'user'
            ));

        } catch (\Exception $e) {
            Log::error('AiTailorController: Failed to load tailoring page', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'job_slug' => $slug,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            abort(500, 'Unable to load tailoring page');
        }
    }

    /**
     * Execute the AI tailoring pipeline
     * 
     * @param AiTailorRunRequest $request Validated tailoring request
     * @return JsonResponse Processing status and run ID for polling
     */
    public function run(AiTailorRunRequest $request): JsonResponse
    {
        $user = auth('job_seeker')->user();
        $correlationId = 'tailor_run_' . uniqid();

        Log::info('AiTailorController: Starting AI tailoring execution', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'job_slug' => $request->input('job_slug'),
            'resume_ids' => $request->input('resume_ids'),
            'constraints' => $request->input('constraints'),
            'request_ip' => $request->ip(),
        ]);

        try {
            // Get validated entities from the request
            $job = $request->getValidatedJob();
            $resumes = $request->getValidatedResumes();

            if (!$job || !$resumes || $resumes->isEmpty()) {
                throw new \Exception('Validated job or resumes not found');
            }

            // Execute the tailoring pipeline
            $tailorRun = $this->tailorService->executeTailoringPipeline(
                $user,
                $job,
                $resumes,
                $request->input('constraints', []),
                $request->input('idempotency_key')
            );

            Log::info('AiTailorController: Successfully initiated AI tailoring', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'tailor_run_id' => $tailorRun->id,
                'status' => $tailorRun->status,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'AI tailoring completed successfully!',
                'data' => [
                    'run_id' => $tailorRun->id,
                    'status' => $tailorRun->status,
                    'correlation_id' => $tailorRun->correlation_id,
                    'processing_time' => $tailorRun->formatted_processing_time,
                    'estimated_cost' => $tailorRun->estimated_cost,
                ],
                'meta' => [
                    'job_title' => $job->title,
                    'company' => $job->company,
                    'resume_count' => $resumes->count(),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('AiTailorController: Failed to execute AI tailoring', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'job_slug' => $request->input('job_slug'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 422);
        }
    }

    /**
     * Get the status and progress of a tailoring run
     * 
     * @param Request $request
     * @param int $runId The tailoring run ID
     * @return JsonResponse Current status and progress information
     */
    public function status(Request $request, int $runId): JsonResponse
    {
        $user = auth('job_seeker')->user();
        $correlationId = 'tailor_status_' . uniqid();

        Log::debug('AiTailorController: Checking tailoring run status', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'tailor_run_id' => $runId,
        ]);

        try {
            $tailorRun = $this->tailorService->getTailoringRun($runId, $user->id);

            if (!$tailorRun) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tailoring run not found.',
                ], 404);
            }

            $response = [
                'success' => true,
                'data' => [
                    'run_id' => $tailorRun->id,
                    'status' => $tailorRun->status,
                    'correlation_id' => $tailorRun->correlation_id,
                    'is_terminal' => $tailorRun->isTerminal(),
                    'is_processing' => $tailorRun->isProcessing(),
                    'is_successful' => $tailorRun->isSuccessful(),
                    'has_failed' => $tailorRun->hasFailed(),
                    'processing_time' => $tailorRun->formatted_processing_time,
                    'estimated_cost' => $tailorRun->estimated_cost,
                    'artifacts_count' => $tailorRun->artifacts->count(),
                    'guardrail_flags' => $tailorRun->guardrail_flags,
                ],
            ];

            // Include results if run is successful
            if ($tailorRun->isSuccessful()) {
                try {
                    $results = $this->tailorService->getRunResults($tailorRun);
                    $response['data']['results_available'] = true;
                } catch (\Exception $e) {
                    $response['data']['results_available'] = false;
                    $response['data']['results_error'] = $e->getMessage();
                }
            }

            return response()->json($response);

        } catch (\Exception $e) {
            Log::error('AiTailorController: Failed to get tailoring run status', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'tailor_run_id' => $runId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get status. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Get the final results (diff and tailored resume) for a completed run
     * 
     * @param Request $request
     * @param int $runId The tailoring run ID
     * @return JsonResponse Final diff and resume content
     */
    public function results(Request $request, int $runId): JsonResponse
    {
        $user = auth('job_seeker')->user();
        $correlationId = 'tailor_results_' . uniqid();

        Log::info('AiTailorController: Fetching tailoring run results', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'tailor_run_id' => $runId,
        ]);

        try {
            $tailorRun = $this->tailorService->getTailoringRun($runId, $user->id);

            if (!$tailorRun) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tailoring run not found.',
                ], 404);
            }

            if (!$tailorRun->isSuccessful()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Results are not available for this run.',
                    'data' => [
                        'status' => $tailorRun->status,
                        'guardrail_flags' => $tailorRun->guardrail_flags,
                    ],
                ], 400);
            }

            $results = $this->tailorService->getRunResults($tailorRun);

            Log::info('AiTailorController: Successfully retrieved tailoring results', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'tailor_run_id' => $runId,
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'run_id' => $tailorRun->id,
                    'diff_content' => $results['diff'],
                    'final_resume_html' => $results['final_resume'],
                    'artifacts' => [
                        'diff' => [
                            'id' => $results['diff_artifact']->id,
                            'step_number' => $results['diff_artifact']->step_number,
                            'content_mime' => $results['diff_artifact']->content_mime,
                            'processing_time' => $results['diff_artifact']->formatted_processing_time,
                        ],
                        'final' => [
                            'id' => $results['final_artifact']->id,
                            'step_number' => $results['final_artifact']->step_number,
                            'content_mime' => $results['final_artifact']->content_mime,
                            'processing_time' => $results['final_artifact']->formatted_processing_time,
                        ],
                    ],
                ],
                'meta' => [
                    'job_title' => $tailorRun->job->title ?? 'Unknown Job',
                    'company' => $tailorRun->job->company ?? 'Unknown Company',
                    'processing_time' => $tailorRun->formatted_processing_time,
                    'estimated_cost' => $tailorRun->estimated_cost,
                    'total_artifacts' => $tailorRun->artifacts->count(),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('AiTailorController: Failed to fetch tailoring results', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'tailor_run_id' => $runId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to load results. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * List recent tailoring runs for the authenticated user
     * 
     * @param Request $request
     * @return JsonResponse List of recent runs with summary information
     */
    public function history(Request $request): JsonResponse
    {
        $user = auth('job_seeker')->user();
        $correlationId = 'tailor_history_' . uniqid();

        Log::debug('AiTailorController: Fetching tailoring run history', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
        ]);

        try {
            $limit = min((int) $request->query('limit', 20), 100); // Max 100
            $recentRuns = $this->tailorService->getRecentRuns($user->id, $limit);

            $historyData = $recentRuns->map(function ($run) {
                return [
                    'id' => $run->id,
                    'status' => $run->status,
                    'job_title' => $run->job->title ?? 'Unknown Job',
                    'company' => $run->job->company ?? 'Unknown Company',
                    'job_slug' => $run->job_slug,
                    'processing_time' => $run->formatted_processing_time,
                    'estimated_cost' => $run->estimated_cost,
                    'artifacts_count' => $run->artifacts->count(),
                    'created_at' => $run->created_at?->format('M j, Y g:i A'),
                    'is_successful' => $run->isSuccessful(),
                    'has_failed' => $run->hasFailed(),
                    'guardrail_flags' => $run->guardrail_flags,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $historyData,
                'meta' => [
                    'total_count' => $recentRuns->count(),
                    'limit' => $limit,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('AiTailorController: Failed to fetch tailoring history', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to load history. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}

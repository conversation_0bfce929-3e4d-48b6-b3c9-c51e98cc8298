<?php

namespace App;

use App\Notifications\EmployeeResetPassword;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;

use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmployeeDepartment extends Pivot
{

    protected $table = 'employee_department';
    public $incrementing = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */









}

-- Purpose: Seed default settings for Global Pause of JobSeeker job alert emails
-- Scope: Only JobSeeker job alert emails (emails that contain jobs). Fetching/scheduling unaffected.
-- Context: Controlled from Email Control Board; enforced in JobService and JobAlertNotification.

INSERT INTO jobseeker_settings (`key`, `value`, `created_at`, `updated_at`)
VALUES ('job_alerts_global_pause', 'false', NOW(), NOW())
ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `updated_at`=VALUES(`updated_at`);

INSERT INTO jobseeker_settings (`key`, `value`, `created_at`, `updated_at`)
VALUES ('job_alerts_global_pause_reason', '', NOW(), NOW())
ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `updated_at`=VALUES(`updated_at`);

INSERT INTO jobseeker_settings (`key`, `value`, `created_at`, `updated_at`)
VALUES ('job_alerts_global_pause_at', '', NOW(), NOW())
ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `updated_at`=VALUES(`updated_at`);



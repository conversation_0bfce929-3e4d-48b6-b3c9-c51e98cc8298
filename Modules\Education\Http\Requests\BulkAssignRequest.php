<?php

namespace Modules\Education\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BulkAssignRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'student_ids' => 'required|array|min:1',
            'student_ids.*' => 'required|integer|exists:students,id',
            'program_id' => 'nullable|integer|exists:programs,id',
            'center_id' => 'nullable|integer|exists:centers,id',
            'class_id' => 'nullable|integer|exists:classes,id',

            // User creation option - accept boolean or string boolean values
            'create_users' => 'nullable|in:true,false,1,0',

            // Additional fields for complete admission creation (guardian_id removed as per requirements)
            'guardian_id' => 'nullable|integer|exists:guardians,id',

            // Bulk assignment for students missing data
            // 'student_genders' => 'nullable|array',
            // 'student_genders.*' => 'nullable|integer|exists:base_setups,id',
            // 'student_dates_of_birth' => 'nullable|array',
            // 'student_dates_of_birth.*' => 'nullable|date|before:' . date('Y-m-d', strtotime('-3 years')) . '|after:' . date('Y-m-d', strtotime('-100 years')),
            'student_guardians' => 'nullable|array',
            'student_guardians.*' => 'nullable|integer|exists:guardians,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'student_ids.required' => 'Please select at least one student.',
            'student_ids.array' => 'Student IDs must be provided as an array.',
            'student_ids.min' => 'Please select at least one student.',
            'student_ids.*.required' => 'Each student ID is required.',
            'student_ids.*.integer' => 'Student IDs must be integers.',
            'student_ids.*.exists' => 'One or more selected students do not exist.',
            'program_id.integer' => 'Program ID must be an integer.',
            'program_id.exists' => 'The selected program does not exist.',
            'center_id.integer' => 'Center ID must be an integer.',
            'center_id.exists' => 'The selected center does not exist.',
            'class_id.integer' => 'Class ID must be an integer.',
            'class_id.exists' => 'The selected class does not exist.',
            'create_users.in' => 'The create users field must be true or false.',

            // Additional validation messages for complete admissions
            'guardian_id.integer' => 'Guardian ID must be an integer.',
            'guardian_id.exists' => 'The selected guardian does not exist.',

            // Bulk assignment validation messages
            'student_genders.array' => 'Student genders must be provided as an array.',
            'student_genders.*.integer' => 'Each gender ID must be an integer.',
            'student_genders.*.exists' => 'One or more selected genders do not exist.',
            'student_dates_of_birth.array' => 'Student dates of birth must be provided as an array.',
            'student_dates_of_birth.*.date' => 'Each date of birth must be a valid date.',
            'student_dates_of_birth.*.before' => 'Each student must be at least 3 years old.',
            'student_dates_of_birth.*.after' => 'Date of birth cannot be more than 100 years ago.',
            'student_guardians.array' => 'Student guardians must be provided as an array.',
            'student_guardians.*.integer' => 'Each guardian ID must be an integer.',
            'student_guardians.*.exists' => 'One or more selected guardians do not exist.',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Ensure at least one assignment field is provided
            if (!$this->program_id && !$this->center_id && !$this->class_id) {
                $validator->errors()->add('assignment', 'Please select at least one assignment (Program, Center, or Class).');
            }

            // Note: Complete admission validation removed as per requirements
            // Gender and date of birth are handled individually per student row, not in bulk operations
            // Bulk operations only handle Program/Center/Class assignment and user creation

            // Note: Bulk gender_id validation removed since bulk assignment is no longer supported

            if ($this->student_genders) {
                foreach ($this->student_genders as $index => $genderId) {
                    if ($genderId) {
                        $gender = \App\BaseSetup::where('id', $genderId)->where('base_group_id', 1)->first();
                        if (!$gender) {
                            $validator->errors()->add("student_genders.{$index}", 'The selected gender is not valid.');
                        }
                    }
                }
            }
        });
    }
}

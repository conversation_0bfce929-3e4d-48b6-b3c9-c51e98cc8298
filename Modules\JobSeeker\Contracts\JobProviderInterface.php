<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Contracts;

/**
 * JobProviderInterface defines the contract for all job provider services.
 * 
 * This interface ensures consistent method signatures across all provider
 * services (JobsAfService, AcbarJobService, etc.) while allowing each
 * provider to implement their specific job fetching logic.
 * 
 * Purpose: Standardize provider service methods for maintainability.
 * Dependencies: None - this is a pure contract.
 */
interface JobProviderInterface
{
    /**
     * Fetch jobs from the provider and send notifications.
     * 
     * This method should:
     * 1. Fetch jobs from the external provider API
     * 2. Process and categorize jobs with provider_category_ids
     * 3. Call JobNotificationService for unified notification handling
     * 
     * @param array $scheduleContext Context including trace_id, execution_id
     * @return array Statistics about the sync process
     */
    public function fetchAndNotifyJobs(array $scheduleContext): array;

    /**
     * Set runtime context for traceability.
     * 
     * @param string $traceId Unique trace identifier
     * @param int|null $executionId Command execution record ID
     * @param int|null $scheduleRuleId Schedule rule ID if applicable
     * @return void
     */
    public function setRunContext(string $traceId, ?int $executionId = null, ?int $scheduleRuleId = null): void;
}

<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class CreateRolePermissionTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('role_permission', function (Blueprint $table) {
            $table->id();
            $table->integer('permission_id')->nullable();
            $table->integer('role_id')->nullable()->unsigned();
            $table->boolean('status')->default(1);
            $table->integer('created_by')->default(1)->unsigned();
            $table->integer('updated_by')->default(1)->unsigned();
            $table->timestamps();

        });

        DB::statement("INSERT INTO `role_permission` (`id`, `permission_id`, `role_id`, `status`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(50, 1, 3, 1, 1, 1, NULL, NULL),
(51, 361, 3, 1, 1, 1, NULL, NULL),
(52, 400, 3, 1, 1, 1, NULL, NULL),
(53, 170, 3, 1, 1, 1, NULL, NULL),
(54, 325, 3, 1, 1, 1, NULL, NULL),
(55, 1013, 3, 1, 1, 1, NULL, NULL),
(56, 1014, 3, 1, 1, 1, NULL, NULL),
(57, 177, 3, 1, 1, 1, NULL, NULL),
(58, 193, 3, 1, 1, 1, NULL, NULL),
(59, 194, 3, 1, 1, 1, NULL, NULL),
(60, 339, 3, 1, 1, 1, NULL, NULL),
(61, 217, 3, 1, 1, 1, NULL, NULL),
(62, 218, 3, 1, 1, 1, NULL, NULL),
(63, 219, 3, 1, 1, 1, NULL, NULL),
(64, 220, 3, 1, 1, 1, NULL, NULL),
(65, 221, 3, 1, 1, 1, NULL, NULL),
(66, 222, 3, 1, 1, 1, NULL, NULL),
(67, 223, 3, 1, 1, 1, NULL, NULL),
(68, 224, 3, 1, 1, 1, NULL, NULL),
(69, 225, 3, 1, 1, 1, NULL, NULL),
(70, 226, 3, 1, 1, 1, NULL, NULL),
(71, 227, 3, 1, 1, 1, NULL, NULL),
(72, 228, 3, 1, 1, 1, NULL, NULL),
(73, 230, 3, 1, 1, 1, NULL, NULL),
(74, 342, 3, 1, 1, 1, NULL, NULL),
(75, 231, 3, 1, 1, 1, NULL, NULL),
(76, 232, 3, 1, 1, 1, NULL, NULL),
(77, 320, 3, 1, 1, 1, NULL, NULL),
(78, 321, 3, 1, 1, 1, NULL, NULL),
(79, 233, 3, 1, 1, 1, NULL, NULL),
(80, 234, 3, 1, 1, 1, NULL, NULL),
(81, 235, 3, 1, 1, 1, NULL, NULL),
(82, 236, 3, 1, 1, 1, NULL, NULL),
(83, 237, 3, 1, 1, 1, NULL, NULL),
(84, 341, 3, 1, 1, 1, NULL, NULL),
(85, 239, 3, 1, 1, 1, NULL, NULL),
(86, 254, 3, 1, 1, 1, NULL, NULL),
(87, 255, 3, 1, 1, 1, NULL, NULL),
(88, 256, 3, 1, 1, 1, NULL, NULL),
(89, 257, 3, 1, 1, 1, NULL, NULL),
(90, 258, 3, 1, 1, 1, NULL, NULL),
(91, 259, 3, 1, 1, 1, NULL, NULL),
(92, 264, 3, 1, 1, 1, NULL, NULL),
(93, 289, 3, 1, 1, 1, NULL, NULL),
(94, 290, 3, 1, 1, 1, NULL, NULL),
(95, 291, 3, 1, 1, 1, NULL, NULL),
(96, 292, 3, 1, 1, 1, NULL, NULL),
(97, 294, 3, 1, 1, 1, NULL, NULL),
(98, 315, 3, 1, 1, 1, NULL, NULL),
(99, 316, 3, 1, 1, 1, NULL, NULL),
(100, 1, 2, 1, 1, 1, NULL, NULL),
(101, 346, 2, 1, 1, 1, NULL, NULL),
(102, 347, 2, 1, 1, 1, NULL, NULL),
(103, 348, 2, 1, 1, 1, NULL, NULL),
(104, 349, 2, 1, 1, 1, NULL, NULL),
(105, 350, 2, 1, 1, 1, NULL, NULL),
(106, 351, 2, 1, 1, 1, NULL, NULL),
(107, 352, 2, 1, 1, 1, NULL, NULL),
(108, 353, 2, 1, 1, 1, NULL, NULL),
(109, 354, 2, 1, 1, 1, NULL, NULL),
(110, 355, 2, 1, 1, 1, NULL, NULL),
(111, 356, 2, 1, 1, 1, NULL, NULL),
(112, 357, 2, 1, 1, 1, NULL, NULL),
(113, 358, 2, 1, 1, 1, NULL, NULL),
(114, 359, 2, 1, 1, 1, NULL, NULL),
(115, 360, 2, 1, 1, 1, NULL, NULL),
(116, 361, 2, 1, 1, 1, NULL, NULL),
(117, 400, 2, 1, 1, 1, NULL, NULL),
(118, 2, 2, 1, 1, 1, NULL, NULL),
(119, 3, 2, 1, 1, 1, NULL, NULL),
(120, 4, 2, 1, 1, 1, NULL, NULL),
(121, 5, 2, 1, 1, 1, NULL, NULL),
(122, 6, 2, 1, 1, 1, NULL, NULL),
(123, 7, 2, 1, 1, 1, NULL, NULL),
(124, 8, 2, 1, 1, 1, NULL, NULL),
(125, 9, 2, 1, 1, 1, NULL, NULL),
(126, 10, 2, 1, 1, 1, NULL, NULL),
(127, 11, 2, 1, 1, 1, NULL, NULL),
(128, 12, 2, 1, 1, 1, NULL, NULL),
(129, 13, 2, 1, 1, 1, NULL, NULL),
(130, 14, 2, 1, 1, 1, NULL, NULL),
(131, 1031, 2, 1, 1, 1, NULL, NULL),
(132, 15, 2, 1, 1, 1, NULL, NULL),
(133, 16, 2, 1, 1, 1, NULL, NULL),
(134, 17, 2, 1, 1, 1, NULL, NULL),
(135, 18, 2, 1, 1, 1, NULL, NULL),
(136, 19, 2, 1, 1, 1, NULL, NULL),
(137, 1030, 2, 1, 1, 1, NULL, NULL),
(138, 20, 2, 1, 1, 1, NULL, NULL),
(139, 21, 2, 1, 1, 1, NULL, NULL),
(140, 22, 2, 1, 1, 1, NULL, NULL),
(141, 23, 2, 1, 1, 1, NULL, NULL),
(142, 24, 2, 1, 1, 1, NULL, NULL),
(143, 1032, 2, 1, 1, 1, NULL, NULL),
(144, 31, 2, 1, 1, 1, NULL, NULL),
(145, 32, 2, 1, 1, 1, NULL, NULL),
(146, 33, 2, 1, 1, 1, NULL, NULL),
(147, 34, 2, 1, 1, 1, NULL, NULL),
(148, 35, 2, 1, 1, 1, NULL, NULL),
(149, 36, 2, 1, 1, 1, NULL, NULL),
(150, 37, 2, 1, 1, 1, NULL, NULL),
(151, 38, 2, 1, 1, 1, NULL, NULL),
(152, 39, 2, 1, 1, 1, NULL, NULL),
(153, 40, 2, 1, 1, 1, NULL, NULL),
(154, 41, 2, 1, 1, 1, NULL, NULL),
(155, 42, 2, 1, 1, 1, NULL, NULL),
(156, 43, 2, 1, 1, 1, NULL, NULL),
(157, 44, 2, 1, 1, 1, NULL, NULL),
(158, 45, 2, 1, 1, 1, NULL, NULL),
(159, 46, 2, 1, 1, 1, NULL, NULL),
(160, 47, 2, 1, 1, 1, NULL, NULL),
(161, 48, 2, 1, 1, 1, NULL, NULL),
(162, 49, 2, 1, 1, 1, NULL, NULL),
(163, 50, 2, 1, 1, 1, NULL, NULL),
(164, 1029, 2, 1, 1, 1, NULL, NULL),
(165, 61, 2, 1, 1, 1, NULL, NULL),
(166, 1011, 2, 1, 1, 1, NULL, NULL),
(167, 1012, 2, 1, 1, 1, NULL, NULL),
(168, 62, 2, 1, 1, 1, NULL, NULL),
(169, 63, 2, 1, 1, 1, NULL, NULL),
(170, 64, 2, 1, 1, 1, NULL, NULL),
(171, 1026, 2, 1, 1, 1, NULL, NULL),
(172, 1027, 2, 1, 1, 1, NULL, NULL),
(173, 66, 2, 1, 1, 1, NULL, NULL),
(174, 67, 2, 1, 1, 1, NULL, NULL),
(175, 68, 2, 1, 1, 1, NULL, NULL),
(176, 69, 2, 1, 1, 1, NULL, NULL),
(177, 70, 2, 1, 1, 1, NULL, NULL),
(178, 71, 2, 1, 1, 1, NULL, NULL),
(179, 72, 2, 1, 1, 1, NULL, NULL),
(180, 720, 2, 1, 1, 1, NULL, NULL),
(181, 721, 2, 1, 1, 1, NULL, NULL),
(182, 722, 2, 1, 1, 1, NULL, NULL),
(183, 723, 2, 1, 1, 1, NULL, NULL),
(184, 724, 2, 1, 1, 1, NULL, NULL),
(185, 725, 2, 1, 1, 1, NULL, NULL),
(186, 89, 2, 1, 1, 1, NULL, NULL),
(187, 90, 2, 1, 1, 1, NULL, NULL),
(188, 91, 2, 1, 1, 1, NULL, NULL),
(189, 92, 2, 1, 1, 1, NULL, NULL),
(190, 93, 2, 1, 1, 1, NULL, NULL),
(191, 94, 2, 1, 1, 1, NULL, NULL),
(192, 95, 2, 1, 1, 1, NULL, NULL),
(193, 96, 2, 1, 1, 1, NULL, NULL),
(194, 97, 2, 1, 1, 1, NULL, NULL),
(195, 98, 2, 1, 1, 1, NULL, NULL),
(196, 99, 2, 1, 1, 1, NULL, NULL),
(197, 100, 2, 1, 1, 1, NULL, NULL),
(198, 106, 2, 1, 1, 1, NULL, NULL),
(199, 107, 2, 1, 1, 1, NULL, NULL),
(200, 108, 2, 1, 1, 1, NULL, NULL),
(201, 109, 2, 1, 1, 1, NULL, NULL),
(202, 110, 2, 1, 1, 1, NULL, NULL),
(203, 111, 2, 1, 1, 1, NULL, NULL),
(204, 112, 2, 1, 1, 1, NULL, NULL),
(205, 113, 2, 1, 1, 1, NULL, NULL),
(206, 114, 2, 1, 1, 1, NULL, NULL),
(207, 115, 2, 1, 1, 1, NULL, NULL),
(208, 116, 2, 1, 1, 1, NULL, NULL),
(209, 992, 2, 1, 1, 1, NULL, NULL),
(210, 1035, 2, 1, 1, 1, NULL, NULL),
(211, 726, 2, 1, 1, 1, NULL, NULL),
(212, 117, 2, 1, 1, 1, NULL, NULL),
(213, 118, 2, 1, 1, 1, NULL, NULL),
(214, 119, 2, 1, 1, 1, NULL, NULL),
(215, 120, 2, 1, 1, 1, NULL, NULL),
(216, 121, 2, 1, 1, 1, NULL, NULL),
(217, 122, 2, 1, 1, 1, NULL, NULL),
(218, 123, 2, 1, 1, 1, NULL, NULL),
(219, 124, 2, 1, 1, 1, NULL, NULL),
(220, 125, 2, 1, 1, 1, NULL, NULL),
(221, 126, 2, 1, 1, 1, NULL, NULL),
(222, 127, 2, 1, 1, 1, NULL, NULL),
(223, 128, 2, 1, 1, 1, NULL, NULL),
(224, 163, 2, 1, 1, 1, NULL, NULL),
(225, 164, 2, 1, 1, 1, NULL, NULL),
(226, 165, 2, 1, 1, 1, NULL, NULL),
(227, 166, 2, 1, 1, 1, NULL, NULL),
(228, 167, 2, 1, 1, 1, NULL, NULL),
(229, 168, 2, 1, 1, 1, NULL, NULL),
(230, 169, 2, 1, 1, 1, NULL, NULL),
(231, 999, 2, 1, 1, 1, NULL, NULL),
(232, 1028, 2, 1, 1, 1, NULL, NULL),
(233, 170, 2, 1, 1, 1, NULL, NULL),
(234, 171, 2, 1, 1, 1, NULL, NULL),
(235, 172, 2, 1, 1, 1, NULL, NULL),
(236, 173, 2, 1, 1, 1, NULL, NULL),
(237, 174, 2, 1, 1, 1, NULL, NULL),
(238, 175, 2, 1, 1, 1, NULL, NULL),
(239, 324, 2, 1, 1, 1, NULL, NULL),
(240, 176, 2, 1, 1, 1, NULL, NULL),
(241, 325, 2, 1, 1, 1, NULL, NULL),
(242, 1013, 2, 1, 1, 1, NULL, NULL),
(243, 1014, 2, 1, 1, 1, NULL, NULL),
(244, 1015, 2, 1, 1, 1, NULL, NULL),
(245, 1016, 2, 1, 1, 1, NULL, NULL),
(246, 326, 2, 1, 1, 1, NULL, NULL),
(247, 203, 2, 1, 1, 1, NULL, NULL),
(248, 204, 2, 1, 1, 1, NULL, NULL),
(249, 205, 2, 1, 1, 1, NULL, NULL),
(250, 206, 2, 1, 1, 1, NULL, NULL),
(251, 931, 2, 1, 1, 1, NULL, NULL),
(252, 932, 2, 1, 1, 1, NULL, NULL),
(253, 933, 2, 1, 1, 1, NULL, NULL),
(254, 177, 2, 1, 1, 1, NULL, NULL),
(255, 178, 2, 1, 1, 1, NULL, NULL),
(256, 179, 2, 1, 1, 1, NULL, NULL),
(257, 180, 2, 1, 1, 1, NULL, NULL),
(258, 181, 2, 1, 1, 1, NULL, NULL),
(259, 182, 2, 1, 1, 1, NULL, NULL),
(260, 183, 2, 1, 1, 1, NULL, NULL),
(261, 1034, 2, 1, 1, 1, NULL, NULL),
(262, 184, 2, 1, 1, 1, NULL, NULL),
(263, 185, 2, 1, 1, 1, NULL, NULL),
(264, 186, 2, 1, 1, 1, NULL, NULL),
(265, 187, 2, 1, 1, 1, NULL, NULL),
(266, 188, 2, 1, 1, 1, NULL, NULL),
(267, 189, 2, 1, 1, 1, NULL, NULL),
(268, 190, 2, 1, 1, 1, NULL, NULL),
(269, 191, 2, 1, 1, 1, NULL, NULL),
(270, 192, 2, 1, 1, 1, NULL, NULL),
(271, 193, 2, 1, 1, 1, NULL, NULL),
(272, 194, 2, 1, 1, 1, NULL, NULL),
(273, 1023, 2, 1, 1, 1, NULL, NULL),
(274, 339, 2, 1, 1, 1, NULL, NULL),
(275, 1024, 2, 1, 1, 1, NULL, NULL),
(276, 195, 2, 1, 1, 1, NULL, NULL),
(277, 196, 2, 1, 1, 1, NULL, NULL),
(278, 197, 2, 1, 1, 1, NULL, NULL),
(279, 198, 2, 1, 1, 1, NULL, NULL),
(280, 199, 2, 1, 1, 1, NULL, NULL),
(281, 200, 2, 1, 1, 1, NULL, NULL),
(282, 201, 2, 1, 1, 1, NULL, NULL),
(283, 202, 2, 1, 1, 1, NULL, NULL),
(284, 1001, 2, 1, 1, 1, NULL, NULL),
(285, 1017, 2, 1, 1, 1, NULL, NULL),
(286, 1002, 2, 1, 1, 1, NULL, NULL),
(287, 1003, 2, 1, 1, 1, NULL, NULL),
(288, 1004, 2, 1, 1, 1, NULL, NULL),
(289, 1018, 2, 1, 1, 1, NULL, NULL),
(290, 327, 2, 1, 1, 1, NULL, NULL),
(291, 1019, 2, 1, 1, 1, NULL, NULL),
(292, 1020, 2, 1, 1, 1, NULL, NULL),
(293, 1021, 2, 1, 1, 1, NULL, NULL),
(294, 1022, 2, 1, 1, 1, NULL, NULL),
(295, 217, 2, 1, 1, 1, NULL, NULL),
(296, 218, 2, 1, 1, 1, NULL, NULL),
(297, 219, 2, 1, 1, 1, NULL, NULL),
(298, 220, 2, 1, 1, 1, NULL, NULL),
(299, 221, 2, 1, 1, 1, NULL, NULL),
(300, 222, 2, 1, 1, 1, NULL, NULL),
(301, 223, 2, 1, 1, 1, NULL, NULL),
(302, 224, 2, 1, 1, 1, NULL, NULL),
(303, 930, 2, 1, 1, 1, NULL, NULL),
(304, 1005, 2, 1, 1, 1, NULL, NULL),
(305, 1006, 2, 1, 1, 1, NULL, NULL),
(306, 1007, 2, 1, 1, 1, NULL, NULL),
(307, 950, 2, 1, 1, 1, NULL, NULL),
(308, 225, 2, 1, 1, 1, NULL, NULL),
(309, 226, 2, 1, 1, 1, NULL, NULL),
(310, 227, 2, 1, 1, 1, NULL, NULL),
(311, 228, 2, 1, 1, 1, NULL, NULL),
(312, 229, 2, 1, 1, 1, NULL, NULL),
(313, 230, 2, 1, 1, 1, NULL, NULL),
(314, 342, 2, 1, 1, 1, NULL, NULL),
(315, 343, 2, 1, 1, 1, NULL, NULL),
(316, 344, 2, 1, 1, 1, NULL, NULL),
(317, 231, 2, 1, 1, 1, NULL, NULL),
(318, 232, 2, 1, 1, 1, NULL, NULL),
(319, 318, 2, 1, 1, 1, NULL, NULL),
(320, 319, 2, 1, 1, 1, NULL, NULL),
(321, 320, 2, 1, 1, 1, NULL, NULL),
(322, 321, 2, 1, 1, 1, NULL, NULL),
(323, 322, 2, 1, 1, 1, NULL, NULL),
(324, 323, 2, 1, 1, 1, NULL, NULL),
(325, 233, 2, 1, 1, 1, NULL, NULL),
(326, 234, 2, 1, 1, 1, NULL, NULL),
(327, 235, 2, 1, 1, 1, NULL, NULL),
(328, 236, 2, 1, 1, 1, NULL, NULL),
(329, 237, 2, 1, 1, 1, NULL, NULL),
(330, 238, 2, 1, 1, 1, NULL, NULL),
(331, 340, 2, 1, 1, 1, NULL, NULL),
(332, 341, 2, 1, 1, 1, NULL, NULL),
(333, 239, 2, 1, 1, 1, NULL, NULL),
(334, 248, 2, 1, 1, 1, NULL, NULL),
(335, 249, 2, 1, 1, 1, NULL, NULL),
(336, 250, 2, 1, 1, 1, NULL, NULL),
(337, 251, 2, 1, 1, 1, NULL, NULL),
(338, 252, 2, 1, 1, 1, NULL, NULL),
(339, 253, 2, 1, 1, 1, NULL, NULL),
(340, 1008, 2, 1, 1, 1, NULL, NULL),
(341, 1009, 2, 1, 1, 1, NULL, NULL),
(342, 254, 2, 1, 1, 1, NULL, NULL),
(343, 255, 2, 1, 1, 1, NULL, NULL),
(344, 256, 2, 1, 1, 1, NULL, NULL),
(345, 257, 2, 1, 1, 1, NULL, NULL),
(346, 258, 2, 1, 1, 1, NULL, NULL),
(347, 259, 2, 1, 1, 1, NULL, NULL),
(348, 264, 2, 1, 1, 1, NULL, NULL),
(349, 289, 2, 1, 1, 1, NULL, NULL),
(350, 290, 2, 1, 1, 1, NULL, NULL),
(351, 291, 2, 1, 1, 1, NULL, NULL),
(352, 292, 2, 1, 1, 1, NULL, NULL),
(353, 293, 2, 1, 1, 1, NULL, NULL),
(354, 294, 2, 1, 1, 1, NULL, NULL),
(355, 295, 2, 1, 1, 1, NULL, NULL),
(356, 296, 2, 1, 1, 1, NULL, NULL),
(357, 297, 2, 1, 1, 1, NULL, NULL),
(358, 298, 2, 1, 1, 1, NULL, NULL),
(359, 299, 2, 1, 1, 1, NULL, NULL),
(360, 300, 2, 1, 1, 1, NULL, NULL),
(361, 301, 2, 1, 1, 1, NULL, NULL),
(362, 302, 2, 1, 1, 1, NULL, NULL),
(363, 303, 2, 1, 1, 1, NULL, NULL),
(364, 304, 2, 1, 1, 1, NULL, NULL),
(365, 305, 2, 1, 1, 1, NULL, NULL),
(366, 306, 2, 1, 1, 1, NULL, NULL),
(367, 1033, 2, 1, 1, 1, NULL, NULL),
(368, 307, 2, 1, 1, 1, NULL, NULL),
(369, 308, 2, 1, 1, 1, NULL, NULL),
(370, 309, 2, 1, 1, 1, NULL, NULL),
(371, 310, 2, 1, 1, 1, NULL, NULL),
(372, 311, 2, 1, 1, 1, NULL, NULL),
(373, 312, 2, 1, 1, 1, NULL, NULL),
(374, 283, 2, 1, 1, 1, NULL, NULL),
(375, 284, 2, 1, 1, 1, NULL, NULL),
(376, 285, 2, 1, 1, 1, NULL, NULL),
(377, 286, 2, 1, 1, 1, NULL, NULL),
(378, 313, 2, 1, 1, 1, NULL, NULL),
(379, 314, 2, 1, 1, 1, NULL, NULL),
(380, 998, 2, 1, 1, 1, NULL, NULL),
(381, 997, 2, 1, 1, 1, NULL, NULL),
(382, 996, 2, 1, 1, 1, NULL, NULL),
(383, 995, 2, 1, 1, 1, NULL, NULL),
(384, 994, 2, 1, 1, 1, NULL, NULL),
(385, 993, 2, 1, 1, 1, NULL, NULL),
(386, 315, 2, 1, 1, 1, NULL, NULL),
(387, 316, 2, 1, 1, 1, NULL, NULL),
(388, 338, 2, 1, 1, 1, NULL, NULL),
(389, 240, 2, 1, 1, 1, NULL, NULL),
(390, 241, 2, 1, 1, 1, NULL, NULL),
(391, 242, 2, 1, 1, 1, NULL, NULL),
(392, 243, 2, 1, 1, 1, NULL, NULL),
(393, 244, 2, 1, 1, 1, NULL, NULL),
(394, 245, 2, 1, 1, 1, NULL, NULL),
(395, 246, 2, 1, 1, 1, NULL, NULL),
(396, 247, 2, 1, 1, 1, NULL, NULL),
(397, 800, 2, 1, 1, 1, NULL, NULL),
(398, 900, 2, 1, 1, 1, NULL, NULL),
(399, 901, 2, 1, 1, 1, NULL, NULL),
(400, 902, 2, 1, 1, 1, NULL, NULL),
(401, 903, 2, 1, 1, 1, NULL, NULL),
(402, 904, 2, 1, 1, 1, NULL, NULL),
(403, 905, 2, 1, 1, 1, NULL, NULL),
(404, 906, 2, 1, 1, 1, NULL, NULL),
(405, 907, 2, 1, 1, 1, NULL, NULL),
(406, 908, 2, 1, 1, 1, NULL, NULL),
(407, 910, 2, 1, 1, 1, NULL, NULL);");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('role_permission');
    }
}

<?php

namespace Modules\Education\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Program;
use App\ProgramLevel;
use App\ProgramLevelLesson;
use App\ProgramLevelLessonFormInputs;
use App\StudentNouranyaPlan;
use App\Talaqqi;
use App\Talqeen;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Session;
use App\Subject;

class ProgramLevelOptionsController extends Controller
{



    public function getProgramLevelOptions($programLevelId)
    {
        try {
            // Fetch the program level
            $programLevel = ProgramLevel::find($programLevelId);
            if (!$programLevel) {
                return response()->json(['success' => false, 'message' => 'Program level not found.'], 404);
            }

            $programLevelTitle = strtolower($programLevel->title);
            $options = [];

            // Check the title to determine the data source
            if (strpos($programLevelTitle, 'level 3') !== false ||
                strpos($programLevelTitle, 'talqeen') !== false ||
                strpos($programLevelTitle, 'talaqqi') !== false) {

                $options = [
                    'Talaqqi' => 'Talaqqi',
                    'Talqeen' => 'Talqeen'
                ];
            }

            return response()->json(['success' => true, 'options' => $options]);
        } catch (\Exception $e) {
            \Log::error('Failed to fetch program level options: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to fetch program level options.'], 500);
        }
    }




}

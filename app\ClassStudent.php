<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Database\Eloquent\SoftDeletes;


class ClassStudent extends Pivot
{
    use SoftDeletes;

    protected $dates = ['deleted_at', 'start_date', 'created_at', 'added_at'];



    protected $fillable = [
        'student_id',
        'deleted_at',
        'class_id',
        'start_date',
        'end_date',
        'transfer_from',
        'transfer_at',
        'added_at'
    ];
    protected $casts = ['transfer_at'];


    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'class_students';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';
    public function addStudentToClass($admission)
    {
        if (!$this->where('class_id', $admission->class_id)->where('student_id', $admission->student_id)->exists())
        {
            $this->create([
                'student_id' => $admission->student_id,
                'class_id' => $admission->class_id,
                'start_date' => date('Y-m-d'),
            ]);

            return 'Student was added to class successfully.';
        }

        return 'Student already has joined the class.';
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function class()
    {
        return $this->belongsTo(Classes::class);
    }
    
}

<?php

namespace Modules\HumanResource\Http\Controllers;

use App\CenterTranslation;
use App\MissedClockOut;
use App\Organization;
use App\Student;
use Doctrine\DBAL\Cache\CacheException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;

use Illuminate\Validation\ValidationException;
use Jenssegers\Agent\Agent;

use App\Employee;

use App\Authorizable;

use App\Attendance;
use Carbon\Carbon;
use App\LeaveRequest;
use App\Role;
use Modules\HumanResource\Http\Requests\ClockOutRequest;
use Modules\HumanResource\Http\Requests\dailyAddClockOutRequest;
use Modules\HumanResource\Http\Requests\MissedClockOutRequest;


class DailyAttendanceAddClockOutController extends Controller
{







    public function addAttendanceOut(dailyAddClockOutRequest $request)
    {


        try {


            $outNote = $request->get('outNote');

            $outTime = Carbon::parse($request->get('out'));

            $agent = new Agent();

            $outAttendance = Attendance::insert([
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),
                'type' => 'out',
                'parent_clock_in_id' => $request->get('in_id'), /** column parent_clock_in_id is specifically used for  this functionality */
                'employee_id' => $request->employee_id,
                'clock' => $outTime,
                'created_at' => Carbon::now(),
                'note' => $outNote,
                'created_by' => auth()->user()->id,
                'organization_id' => \Config::get('organization_id'),
                'updated_by' => auth()->user()->id,
                'action_trigerred_from_url' => 'NOT',
            ]);
            return response()->json("details added to the attendance", 200);


//        } catch(\Illuminate\Database\QueryException $ex){
        } catch (\Exception $ex) {

            return response()->json($ex->getMessage(), 500);


            // Note any method of class PDOException can be called on $ex.
        }


    }
    public function addMissedAttendanceOut(MissedClockOutRequest $request): JsonResponse
    {




        try {

            \DB::beginTransaction();
            $outNote = $request->get('outNote');
            $date = Carbon::parse($request->get('date'))->toDateString();
            $out = Carbon::parse($request->get('out'))->toTimeString();
            $outDateTime = $date.' '. $out;



            // get clockIn date and append the time to it
            $outAttendance = MissedClockOut::insert([
                'employee_id' => $request->employee_id,
                'created_at' => Carbon::now(),
//                'location' => $request->location ?? 'NOT',
//                'device' => $agent->device(),
//                'ip' => $request->ip(),
                'type' => 'out',
//                'clock' =>$in,
                'clock' =>$outDateTime,
                'clockout_reason' => $outNote,
                'created_by' => auth()->user()->id,
                'organization_id' => \Config::get('organization_id'),
                'updated_by' => auth()->user()->id,
            ]);


            // mark all entries for this employee as trashed
            MissedClockOut::where('employee_id',$request->employee_id)->update(['deleted_at' =>Carbon::now()]);




            $allMissedClockOutAttendance = MissedClockOut::withTrashed()->with('attendance')->orderByRaw('date(clock) desc, employee_id')->get();
            \DB::commit();
            return response()->json([
                'success' => 'success',
                'TableData' => (string)view('general::partials.missedClockoutList', compact('allMissedClockOutAttendance'))
            ]);


        } catch (\Exception $exception) {


            \DB::rollBack();

            \Log::error($exception);

            return response()->json($exception->getMessage(), 500);


            // Note any method of class PDOException can be called on $ex.
        }


    }

    public function updateMissedAttendanceOut(MissedClockOutRequest $request): JsonResponse
    {


        try {

            \DB::beginTransaction();
            $outNote = $request->get('outNote');
            $date = Carbon::parse($request->get('date'))->toDateString();
            $out = Carbon::parse($request->get('out'))->toTimeString();
            $in = Carbon::parse($request->get('in'))->toDateString();
            $in = $in.' '. $out;
            $outDate = $date.' '. $out;


            // get clockIn date and append the time to it
            $outAttendance = MissedClockOut::insert([
                'employee_id' => $request->employee_id,
                'created_at' => Carbon::now(),
//                'location' => $request->location ?? 'NOT',
//                'device' => $agent->device(),
//                'ip' => $request->ip(),
                'type' => 'out',
//                'clock' =>$in,
                'clock' =>$outDate,
                'clockout_reason' => $outNote,
                'created_by' => auth()->user()->id,
                'organization_id' => \Config::get('organization_id'),
                'updated_by' => auth()->user()->id,
            ]);


            // mark all entries for this employee as trashed
            MissedClockOut::where('employee_id',$request->employee_id)->update(['deleted_at' =>Carbon::now()]);

            \DB::commit();
            return response()->json('success',200);

        } catch (\Exception $exception) {


            \DB::rollBack();

            \Log::error($exception);

            return response()->json($exception->getMessage(), 500);


            // Note any method of class PDOException can be called on $ex.
        }


    }

    public function getAttendanceNote($id)
    {


        $note = Attendance::where("id", $id)->first()->note;


        return response()->json($note);

    }

    public function getAttendancePair($employeeId,$id,$type)
    {

        $result = [];
        if($type =='in'){

            $attDate = Attendance::where("id",$id)->select("date(clock) as date")->first()->date;


            $nextRow = Attendance::where("employee_id",$employeeId)->where("id",'>',$id)->where(\DB::raw("date(clock)"),$attDate)->select('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note')->first();



            if(is_null($nextRow)){

                $nextDate = new Carbon($nextRow->date);
                $currentRow = Attendance::where("id",$id)->select('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note')->first();
                $currentDate = new Carbon($currentRow->date);
                $currentTime = new Carbon($currentRow->time);

                if($nextDate->gt($currentDate)){

                    $attendance = Attendance::where("id", $id)->select('time(clock) attTime,clock,type,employee_id,id,note')->first()->toArray();
                    $systemGeneratedRecord = ['attTime' => $currentTime->addSeconds(44)->toTimeString(), 'clock' => $currentDate->toDateString() .' '. $currentTime->addSeconds(44)->toTimeString(), 'employee_id' => $currentRow->employee_id,'id' => '','note' => '', 'type' => 'out'];

                    $result[] = $attendance;
                    $result[] = $systemGeneratedRecord;



                }
            }
            else{
                $result = Attendance::where(\DB::raw("date(clock)"),$attDate)->whereIn("id", [$id,$nextRow->id])->select('time(clock) attTime,time(clock) as time,clock,type,employee_id,id,note')->get();


            }


        }else{


            $attDate = Attendance::where("id",$id)->select("date(clock) as date")->first()->date;


            $previous = Attendance::where("employee_id",$employeeId)->where("id",'<',$id)->where(\DB::raw("date(clock)"),$attDate)->select('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note')->first();



            // if there is no other record before this record for the $attDate
            if(is_null($previous)){


                $currentRow = Attendance::where("employee_id",$employeeId)->where("id",$id)->select('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note')->first();
                $currentDate = new Carbon($currentRow->date);
                $currentTime = new Carbon($currentRow->time);



                    $attendance = Attendance::where("id", $id)->select('time(clock) attTime,clock,type,employee_id,id,note')->first()->toArray();
                    $systemGeneratedRecord = ['attTime' => $currentTime->subSeconds(120)->toTimeString(), 'clock' => $currentDate->toDateString() .' '. $currentTime->subSeconds(44)->toTimeString(), 'employee_id' => $currentRow->employee_id,'id' => '','note' => 'This is temporary system generated note and time, please proceed to edit the details here for the Punch in', 'type' => 'in'];

                    $result[] = $attendance;
                    $result[] = $systemGeneratedRecord;

            }
            else{
                $result = Attendance::where(\DB::raw("date(clock)"),$attDate)->whereIn("id", [$id,--$id])->select('time(clock) attTime,time(clock) as time,clock,type,employee_id,id,note')->get();


            }


            // original record before the edit
//            $result = Attendance::whereIn("id", [$id,])->select(\DB::raw('time(clock) attTime,clock,time(clock) as time, type,employee_id,id,note'))->get();

        }







        return response()->json($result);

    }


}

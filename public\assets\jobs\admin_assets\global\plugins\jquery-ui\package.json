{"name": "jquery-ui", "title": "jQuery <PERSON>", "description": "A curated set of user interface interactions, effects, widgets, and themes built on top of the jQuery JavaScript Library.", "version": "1.12.1", "homepage": "http://jqueryui.com", "author": {"name": "jQuery Foundation and other contributors", "url": "https://github.com/jquery/jquery-ui/blob/1.12.1/AUTHORS.txt"}, "main": "ui/widget.js", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://scottgonzalez.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "joern.z<PERSON><PERSON><PERSON>@gmail.com", "url": "http://bassistance.de"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mike.sherov.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjvantoll.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.felixnagel.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/arschmitz"}], "repository": {"type": "git", "url": "git://github.com/jquery/jquery-ui.git"}, "bugs": "https://bugs.jqueryui.com/", "license": "MIT", "scripts": {"test": "grunt"}, "dependencies": {}, "devDependencies": {"commitplease": "2.3.0", "grunt": "0.4.5", "grunt-bowercopy": "1.2.4", "grunt-cli": "0.1.13", "grunt-compare-size": "0.4.0", "grunt-contrib-concat": "0.5.1", "grunt-contrib-csslint": "0.5.0", "grunt-contrib-jshint": "0.12.0", "grunt-contrib-qunit": "1.0.1", "grunt-contrib-requirejs": "0.4.4", "grunt-contrib-uglify": "0.11.1", "grunt-git-authors": "3.1.0", "grunt-html": "6.0.0", "grunt-jscs": "2.1.0", "load-grunt-tasks": "3.4.0", "rimraf": "2.5.1", "testswarm": "1.1.0"}, "keywords": []}
<?php

namespace Modules\Education\Http\Controllers;

use App\Classes;
use App\Student;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

class IjazasanadMonthYearController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function getMonthYears($studentParam, $classParam)
    {
        // Handle both route parameter formats: Student/Classes models or IDs
        if ($studentParam instanceof Student) {
            $student = $studentParam;
        } else {
            $student = Student::find($studentParam);
        }
        
        if ($classParam instanceof Classes) {
            $class = $classParam;
        } else {
            $class = Classes::find($classParam);
        }

        if (!$student || !$class) {
            return response()->json([]);
        }

        // Check if the student has a memorization plan that links to level 1
        $hasLevelOnePlan = $student->ijazasanad_memorization_plans()
            ->whereHas('program_level.translations', function ($query) {
                $query->where('title', 'LIKE', '%level 1%')
                    ->orWhere('title', 'LIKE', '%level1%');
            })
            ->exists();

        if($hasLevelOnePlan ){
            // Retrieve only memorization dates if level 1 exists
            $memorizationDates = \App\StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $class->id)
                ->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year, class_id')
                ->groupBy('year', 'month', 'class_id')
                ->orderByDesc('year')
                ->orderByRaw("FIELD(month, 'December', 'November', 'October', 'September', 'August', 'July', 'June', 'May', 'April', 'March', 'February', 'January')")
                ->get();

            // Optional: map with timetable count (if needed)
            $datesWithCount = $memorizationDates->map(function($date) {
                $classTimetable = \App\ClassTimetable::where('class_id', $date->class_id)->first();
                if ($classTimetable) {
                    $monthNumber = date('n', strtotime($date->month));
                    $days = $classTimetable->daysCountPerMonth($monthNumber, $date->year);
                } else {
                    $days = 0;
                }
                
                return [
                    'month_year' => $date->month . ' ' . $date->year,
                    'days' => $days
                ];
            });
        }else{
            $memorizationDates = \App\StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $class->id)
                ->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year, class_id')
                ->groupBy('year', 'month', 'class_id')
                ->orderByDesc('year')
                ->orderByRaw("FIELD(month, 'December', 'November', 'October', 'September', 'August', 'July', 'June', 'May', 'April', 'March', 'February', 'January')")
                ->get();

            $datesWithCount = $memorizationDates->map(function($date) {
                $classTimetable = \App\ClassTimetable::where('class_id', $date->class_id)->first();
                if ($classTimetable) {
                    // Parse month name to month number
                    $monthNumber = date('n', strtotime($date->month));
                    $days = $classTimetable->daysCountPerMonth($monthNumber, $date->year);
                } else {
                    $days = 0;
                }
                
                return [
                    'month_year' => $date->month . ' ' . $date->year,
                    'days' => $days
                ];
            });
        }
        
        return response()->json($datesWithCount);
    }

    /**
     * Get month-year data for class reports
     * Following the Nouranya pattern for class-level reports
     */
    public function getClassReportData($classId)
    {
        $class = Classes::withTrashed()->find($classId);

        // handling the wrong $id
        if (is_null($class)) {
            flash('Class ' . $classId . ' does not exist');
            return redirect()->to("workplace/education/classes");
        }

        if (!is_null($class->deleted_at)) {
            flash('Class ' . $class->class_code . ' is archived and is not accessible. please try another class');
            return redirect()->to("workplace/education/classes");
        }

        $class->loadMissing('students', 'teachers');

        // Get available month-year combinations for reports
        // Query from ijazasanad memorization plans based on class students
        $monthlyHalaqahReportMonthYearList = DB::table('ijazasanad_memorization_plans')
            ->whereIn('student_id', $class->students->pluck('id'))
            ->select(DB::raw('MONTHNAME(created_at) as month'), DB::raw('YEAR(created_at) as year'))
            ->distinct()
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        // If no plans exist, try to get data from memorization reports
        if ($monthlyHalaqahReportMonthYearList->isEmpty()) {
            $monthlyHalaqahReportMonthYearList = DB::table('student_ijazasanad_memorization_reports')
                ->whereIn('student_id', $class->students->pluck('id'))
                ->where('class_id', $classId)
                ->select(DB::raw('MONTHNAME(created_at) as month'), DB::raw('YEAR(created_at) as year'))
                ->distinct()
                ->orderBy('year', 'desc')
                ->orderBy('month', 'desc')
                ->get();
        }

        return view('modules.education.classes.reports.class.ijazasanadHalaqah', compact(
            'class',
            'monthlyHalaqahReportMonthYearList'
        ));
    }
}

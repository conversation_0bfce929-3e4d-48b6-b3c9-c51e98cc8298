<?php

namespace App;

use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Homework
 *
 * @property int $id
 * @property string|null $homework_date
 * @property string|null $submission_date
 * @property string|null $evaluation_date
 * @property string|null $file
 * @property string|null $marks
 * @property string|null $description
 * @property int $active_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $evaluated_by
 * @property int|null $class_id
 * @property int|null $section_id
 * @property int|null $subject_id
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $organization_id
 * @property int|null $academic_id
 * @property-read \App\Classes|null $classes
 * @property-read \App\Section|null $sections
 * @property-read \App\Subject|null $subjects
 * @property-read \App\User|null $users
 * @method static \Illuminate\Database\Eloquent\Builder|Homework newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Homework newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Homework query()
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereAcademicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereActiveStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereClassId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereEvaluatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereEvaluationDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereFile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereHomeworkDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereMarks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereSectionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereSubjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereSubmissionDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Homework whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class Homework extends Model
{
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);


    }
	protected $table ="homeworks";
	
    public function classes(){
		return $this->belongsTo('App\Classes', 'class_id', 'id');
	}

	public function sections(){
		return $this->belongsTo('App\Section', 'section_id', 'id');
	}

	public function subjects(){
		return $this->belongsTo('App\Subject', 'subject_id', 'id');
	}


	public function users(){
		return $this->belongsTo('App\User', 'created_by', 'id');
	}

	public static function getHomeworkPercentage($class_id, $section_id, $homework_id){
		try {
			$allStudents = Student::select('id')
								->where('class_id', $class_id)
								->where('section_id', $section_id)
								->get();

			$totalStudents = count($allStudents);
			$HomeworkCompleted = HomeworkStudent::select('id')
								->where('homework_id', $homework_id)
								->where('complete_status', 'C')
								->get();

			$totalHomeworkCompleted = count($HomeworkCompleted);

			if(isset($totalStudents)){
				$homeworks = array(
					'totalStudents' => $totalStudents,
					'totalHomeworkCompleted' => $totalHomeworkCompleted

					);
				return $homeworks;
			}
			else{
				return false;
			}
		} catch (\Exception $e) {
			return false;
		}
	}

	public static function evaluationHomework($s_id, $h_id){
		
		try {
			$abc = HomeworkStudent::where('homework_id', $h_id)->where('student_id', $s_id)->first();
				return $abc;
		} catch (\Exception $e) {
			$data=[];
			return $data;
		}
	}

	public static function uploadedContent($s_id, $h_id){
		
		try {
			$abc = UploadHomeworkContent::where('homework_id', $h_id)->where('student_id', $s_id)->first();
			
				return $abc;
		} catch (\Exception $e) {
			$data=[];
			return $data;
		}
	}
}

@extends('layouts.hound')

@section('mytitle', $class->name )

@section('content')
<div class="panel panel-default card-view">
    <div class="panel-heading clearfix">
        <h3 class="pull-left"> Class Information </h3> 
        <div class="pull-right">
            <a href="{{ url('/workplace/education/classes') }}" title="Back"><button class="btn btn-warning btn-xs txt-light"><i class="fa fa-arrow-left txt-light" aria-hidden="true"></i> Back</button></a>
            @can('update class') 
            <a href="{{ url('/workplace/education/classes/' . $class->id . '/edit') }}" title="Edit class"><button class="btn btn-primary btn-xs txt-light">
                <i class="fa fa-pencil-square-o txt-light" aria-hidden="true"></i> Edit</button></a>
                @endcan
           @can('remove class')
           {!! Form::open([
                'method'=>'DELETE',
                'url' => ['workplace/education/classes', $class->id],
                'style' => 'display:inline'
            ]) !!}
                {!! Form::button('<i class="fa fa-trash-o txt-light" aria-hidden="true"></i> Delete', array(
                        'type' => 'submit',
                        'class' => 'btn btn-danger btn-xs',
                        'title' => 'Delete class',
                        'onclick'=>'return confirm("Confirm delete?")'
                ))!!}
            {!! Form::close() !!}   
            @endcan         
        </div>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            @if ($errors->any())
                <ul class="alert alert-danger">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            @endif
            <table class="table table-borderless">
                <tbody>
                    <tr>
                        <td><b>Class Name :</b> {{ $class->name }} </td>
                        <td><b>Class Code :</b> {{ $class->class_code }} </td>
                        <td><b>Center:</b>@if(isset( $class->center->name )){{ $class->center->name }} @endif </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="panel panel-default card-view">
        <div class="panel-heading">
            <div class="pull-left">
                <h3>Class Programs</h3>
            
            </div>
           
            <a data-toggle="modal" href='#editClassPrograms' class="btn btn-success btn-sm pull-right"><i class="fa fa-edit" aria-hidden="true"></i>Edit Class Programs</a>
          
            <div class="clearfix"></div>
        </div>
        <div class="panel-wrapper collapse in">
            <div class="panel-body clearfix">
                @if(!count($class->programs))
                <div class="alert alert-warning">There is no programs in this class yet.</div>
                @endif
                @foreach($class_programs as $program)
                <div class="timeline-panel pa-30">
                    <div class="timeline-body">
                        <h4 class=" mb-5">{{$program['info']['title'] }}</h4>
                        @if(isset($program['info']->setting['special_program_code']) && $program['info']->setting['special_program_code'] == 'hefz')
                        <div class="pl-20 pr-20">
                                <div class="panel panel-default card-view clearfix pb-15">
                                    <h5> Program Details
                                        <button class="btn btn-xs pull-right btn-danger" 
                                            @if($program['teacher'])
                                            onclick='change_program_teacher({{ $program['info']->id }} , {{$program['teacher']->data->id }}, "{{$program['teacher']->data->full_name}}")'> Change 
                                            @else 
                                            onclick="add_program_teacher({{ $program['info']->id }})"> Add 
                                            @endif
                                            Teacher 
                                        </button>
                                    </h5>
                                    {{-- @foreach($class->teachers as $teacher) --}}
                                    @if($program['teacher'])

                                    <b>Program Teacher :</b> {{ $program['teacher']->data->full_name ?? '' }}
                                        <p>
                                            <b>Program Time Table</b>
                                        </p>                                            
                                        <div>
                                            
                                            <button class="btn btn-xs pull-right btn-danger" 
                                                @if(isset($program['timetable']) && $program['timetable'])
                                                onclick='change_subject_timetable({{ $program['class_teacher_subject_id'] }} , {{$program['timetable']->id}})'> Change 
                                                @else 
                                                onclick="add_subject_timetable({{ $program['class_teacher_subject_id'] }})"> Add 
                                                @endif
                                                Timetable 
                                            </button>
                                            @if(isset($program['timetable']) && $program['timetable'])
                                            <table class="table table-bordered text-center">
                                                <thead>
                                                    <tr>
                                                        <th>Mon</th>
                                                        <th>Tue</th>
                                                        <th>Wed</th>
                                                        <th>Thu</th>
                                                        <th>Fri</th>
                                                        <th>Sat</th>
                                                        <th>Sun</th>
                                                        <th class="bg-grey txt-light">Class Duration</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>{{ $program['timetable']->mon ?? 'no class' }}</td>
                                                        <td>{{ $program['timetable']->tue ?? 'no class' }}</td>
                                                        <td>{{ $program['timetable']->wed ?? 'no class' }}</td>
                                                        <td>{{ $program['timetable']->thu ?? 'no class' }}</td>
                                                        <td>{{ $program['timetable']->fri ?? 'no class' }}</td>
                                                        <td>{{ $program['timetable']->sat ?? 'no class' }}</td>
                                                        <td>{{ $program['timetable']->sun ?? 'no class' }}</td>
                                                        <td class="bg-grey txt-light">{{ $program['timetable']->class_duration }}</td>
                                                    </tr>
                                                </tbody>

                                            </table>

                                            @else
                                                <div class="text-center alert alert-info">

                                                    Add Timetable to this subject first!
                                                </div>
                                            @endif
                                        </div>
                                    @else
                                    <div class="text-center alert alert-warning">
                                        Add Teacher to this program first!
                                    </div>
                                    @endif
                                </div>
                            </div>
                        @else
                        <p class="lead  mb-20">Subjects & Teachers</p>
                        <div class="pl-20 pr-20">
                            @isset($program['class_subjects'] )
                                @foreach($program['class_subjects'] as $subject)

                                <div class="panel panel-default card-view clearfix pb-15">
                                    <h5>{{ $subject['title'] }} 
                                        <button class="btn btn-xs pull-right btn-danger" 
                                            @if($subject['teacher'])
                                            onclick='change_subject_teacher({{ $subject->id }} , {{$subject['teacher']->data->id}} ,"{{ $subject['teacher']->data->full_name }}")'> Change 
                                            @else 
                                            onclick="add_subject_teacher({{ $subject->id }})"> Add 
                                            @endif
                                            Teacher 
                                        </button>
                                    </h5>
                                    {{-- @foreach($class->teachers as $teacher) --}}
                                    @if($subject['teacher'])
                                        <b>Subject Teacher :</b> {{ $subject['teacher']->data->full_name ?? '' }}
                                        <p>
                                            <b>Subject Time Table</b>
                                            <div>
                                                <button class="btn btn-xs pull-right btn-danger" 
                                                    @if($subject['timetable'])
                                                    onclick='change_subject_timetable({{ $subject['class_teacher_subject_id'] }} , {{$subject['timetable']->id}})'> Change 
                                                    @else 
                                                    onclick="add_subject_timetable({{ $subject['class_teacher_subject_id'] }})"> Add 
                                                    @endif
                                                    Timetable 
                                                </button>
                                                @if($subject->timetable)
                                                <table class="table table-bordered text-center">
                                                    <thead>
                                                        <tr>
                                                            <th>Mon</th>
                                                            <th>Tue</th>
                                                            <th>Wed</th>
                                                            <th>Thu</th>
                                                            <th>Fri</th>
                                                            <th>Sat</th>
                                                            <th>Sun</th>
                                                            <th class="bg-grey txt-light">Class Duration</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td>{{ $subject->timetable->mon ?? 'no class' }}</td>
                                                            <td>{{ $subject->timetable->tue ?? 'no class' }}</td>
                                                            <td>{{ $subject->timetable->wed ?? 'no class' }}</td>
                                                            <td>{{ $subject->timetable->thu ?? 'no class' }}</td>
                                                            <td>{{ $subject->timetable->fri ?? 'no class' }}</td>
                                                            <td>{{ $subject->timetable->sat ?? 'no class' }}</td>
                                                            <td>{{ $subject->timetable->sun ?? 'no class' }}</td>
                                                            <td class="bg-grey txt-light">{{ $subject->timetable->class_duration }}</td>
                                                        </tr>
                                                    </tbody>

                                                </table>

                                                @else
                                                    <div class="text-center alert alert-info">

                                                        Add Timetable to this subject first!
                                                    </div>
                                                @endif
                                            </div>
                                        </p>
                                    @else
                                    <div class="text-center alert alert-warning">
                                        Add Teacher to this subject first!
                                    </div>
                                    @endif

                                </div>
                                @endforeach 
                            @else
                            <div class="text-center alert alert-warning">
                                No Subjects in this Program!!!
                            </div>
                            @endisset

                        </div>
                        @endif
                    </div>
                </div>
                @endforeach

        </div>
    </div>
</div>

@if(count($class->programs))
<div class="card-view" >
    <h3> Class Students </h3>
    <table class="table table-responsive" >
        @foreach($class->students as $student)
        <tr>
            <td>{{$student->full_name }}</td>
            <td>{{$student->nationality }}</td>
            <td>{{$student->pivot->start_date }}</td>
            <td>{{$student->status }}</td>
            <td>
                <!-- Single button -->
                <div class="btn-group">

                    <button type="button" class="btn btn-primary btn-xs dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Actions <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu">
                    <li><a href="{{ route('students.show' , $student->user_id) }}">View Profile</a></li>
                    <li><a href="#" onclick="change_student_class({{ $student->id }} , '{{$student->full_name }}')">Change Student's Class</a></li>
                    <li> <a data-catid={{$student->id}} data-toggle="modal" data-target="#delete"> Delete Student</a></li>

                </ul>
                </div>
                          
                {{--<button class="btn btn-danger btn-xs ">Update</button>
                 | <button class="btn btn-danger ">suspend</button> | <button class="btn btn-danger ">graduated</button> --}}
            </td>
        </tr>
        @endforeach
    </table>
</div>

<!-- Modals -->

<div class="modal fade" id="add_teacher">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">Add Teacher To Class</h4>
            </div>
            {!! Form::open(['route' => 'class.add_teacher']) !!}
            <div class="modal-body">
            {!! Form::hidden('class_id' , $class->id) !!}
            <div class="form-group">
            {!! Form::label('Teacher name') !!}
            {!! Form::select('employee_id' , $teachers ?? [], null ,['class' => 'select2 form-control' , 'required']) !!}
            </div>        
            <div class="form-group">
                <input type="hidden" id="subject_id" name="subject_id" value="">
                <input type="hidden" id="program_id" name="program_id" value="">
            </div>        
            <div class="form-group">
            {!! Form::label('Starting Date') !!}
            {!! Form::text('start_at' , null ,['class' => 'form-control date' , 'required']) !!}
            </div>        
                
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Save changes</button>
            </div>
            {!! Form::close() !!}
        </div>
    </div>
</div>


<div class="modal fade" id="change_teacher">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">Change Teacher</h4>
            </div>
            {!! Form::open(['route' => 'class.change_teacher']) !!}
            <div class="modal-body">
            {!! Form::hidden('class_id' , $class->id) !!}
            <div class="form-group">
                <label for="">Current Teacher</label>
                <div id="current_teacher_name"  class="form-control pt-10" ></div>
            </div>        
            <div class="form-group">
                {!! Form::label('Current Teacher Last Day in this Class') !!}
                {!! Form::text('ended_at' , null ,['class' => 'form-control date' , 'required']) !!}
            </div>        
        
            <div class="form-group">
            {!! Form::label('Teacher name') !!}
            {!! Form::select('employee_id' , $teachers ?? [], null ,['class' => 'select2 form-control' , 'required']) !!}
            </div>        
            <div class="form-group">
                <input type="hidden" id="current_employee_id" name="current_employee_id" value="">
                <input type="hidden" id="change_form_subject_id" name="subject_id" value="">
                <input type="hidden" id="change_form_program_id" name="program_id" value="">
            </div>        
            <div class="form-group">
            {!! Form::label('Starting Date') !!}
            {!! Form::text('start_at' , null ,['class' => 'form-control date' , 'required']) !!}
            </div>        
                
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Save changes</button>
            </div>
            {!! Form::close() !!}
        </div>
    </div>
</div>


<div class="modal fade" id="change_student_class">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">Transfer Student to another Class</h4>
            </div>
            {!! Form::open(['route' => 'class.change_student_class']) !!}
            <div class="modal-body">
            {!! Form::hidden('class_id' , $class->id) !!}
            {!! Form::hidden('student_id' , null , ['id' => 'student_id']) !!}
            <div class="form-group">
                <label for="">Student Name</label>
                <div id="student_name"  class="form-control pt-10" ></div>
            </div>        
            <div class="form-group  ">
                {!! Form::label('Student Last Day in this Class')  !!}
                {!! Form::text('ended_at' , null ,['class' => 'form-control date' , 'required']) !!}
            </div>        
        
            <div class="form-group ">
            {!! Form::label('New Class') !!}
            {!! Form::select('new_class_id' , $classes ?? [], null ,['class' => 'select2 form-control' , 'required']) !!}
            </div>        
            <div class="form-group">
                <input type="hidden" id="student_id" name="current_employee_id" value="">
            </div>        
            <div class="form-group">
            {!! Form::label('Starting Date') !!}
            {!! Form::text('start_at' , null ,['class' => 'form-control date' , 'required']) !!}
            </div>        
                
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Save changes</button>
            </div>
            {!! Form::close() !!}
        </div>
    </div>
</div>

<div class="modal fade" id="add_timetable">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">Add Timetable</h4>
            </div>
            {!! Form::open(['route' => 'class.add_timetable']) !!}
            <div class="modal-body">
            {!! Form::hidden('class_id' , $class->id) !!}
            <table class="table table-bordered text-center">
                    <tr>
                        <td>Mon</td>
                        <td>
                            <input type="text" name="mon" id="input_mon" value="" class="time form-control">
                        </td>
                        <td>
                            <button class="btn btn-xs btn-danger" onclick="$('#input_mon').val('')" type="button">X No Class</button>
                        </td>
                    </tr>
                    <tr>
                        <td>Tue</td>
                        <td>
                            <input type="text" name="tue" id="input_tue" value="" class="time form-control">
                        </td>
                        <td>
                            <button class="btn btn-xs btn-danger" onclick="$('#input_tue').val('')" type="button">X No Class</button>
                        </td>
                    </tr>
                    <tr>
                        <td>Wed</td>
                        <td>
                            <input type="text" name="wed" id="input_wed" value="" class="time form-control">
                        </td>
                        <td>
                            <button class="btn btn-xs btn-danger" onclick="$('#input_wed').val('')" type="button">X No Class</button>
                        </td>
                    </tr>
                    <tr>
                        <td>Thu</td>
                        <td>
                            <input type="text" name="thu" id="input_thu" value="" class="time form-control">
                        </td>
                        <td>
                            <button class="btn btn-xs btn-danger" onclick="$('#input_thu').val('')" type="button">X No Class</button>
                        </td>
                    </tr>
                    <tr>
                        <td>Fri</td>
                        <td>
                            <input type="text" name="fri" id="input_fri" value="" class="time form-control">
                        </td>
                        <td>
                            <button class="btn btn-xs btn-danger" onclick="$('#input_fri').val('')" type="button">X No Class</button>
                        </td>
                    </tr>
                    <tr>
                        <td>Sat</td>
                        <td>
                            <input type="text" name="sat" id="input_sat" value="" class="time form-control">
                        </td>
                        <td>
                            <button class="btn btn-xs btn-danger" onclick="$('#input_sat').val('')" type="button">X No Class</button>
                        </td>
                    </tr>
                    <tr>
                        <td>Sun</td>
                        <td>
                            <input type="text" name="sun" id="input_sun" value="" class="time form-control">
                        </td>
                        <td>
                            <button class="btn btn-xs btn-danger" onclick="$('#input_sun').val('')" type="button">X No Class</button>
                        </td>
                    </tr>
                    <tr>
                        <td class="bg-grey txt-light">Class Duration</td>
                        <td><input type="text" name="duration" value="" class="form-control"></td>
                    </tr>
            </table>
            <div class="form-group">
                <input type="hidden" id="class_teacher_subject_id" name="class_teacher_subject_id" value="">
            </div>        
            <div class="form-group">
            {!! Form::label('Starting Date') !!}
            {!! Form::text('start_at' , null ,['class' => 'form-control date' , 'required']) !!}
            </div>        
                
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Save changes</button>
            </div>
            {!! Form::close() !!}
        </div>
    </div>
</div>
    
@endif

<div class="modal fade" id="editClassPrograms">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title"> {{ trans('examinationcertification::actions.edit_class_programs') }} </h4>
            </div>
            {!! Form::open(['url' => '/workplace/education/class-programs', 'class' => 'form-horizontal', 'id' => 'editPrograms', 'files' => false]) !!}
            <div class="modal-body">
                <div id="programs_form_errors" class="error alert-danger">
                </div>
                <div class="clearfix">
                {!! Form::hidden('class_id' , $class->id ) !!}
                @foreach($programs as $program)
                    <label>
                    {!! Form::checkbox('class_programs[]' , $program->id , in_array($program->id , $class->programs->pluck('id')->toArray() ) , ['class' => 'control-label'])  !!}
                        {{$program->title}}
                    </label>
                    {!! Form::select('class_programs_level['.$program->id.']' ,['0' => 'All Levels'] + $program->levels->pluck('title' , 'id')->toArray() , null , ['class' => 'form-control']) !!}
                @endforeach
                </div>
            </div>
            <div class="modal-footer">
                {!! Form::submit(trans('common.save'), ['class' => 'btn btn-primary']) !!}
            
                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
            </div>
            {!! Form::close() !!}
            
        </div>
    </div>

</div>
<div class="modal modal-danger fade" id="delete" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
          <h4 class="modal-title text-center" id="myModalLabel">Delete Confirmation</h4>
        </div>
     
        {!! Form::open([
            'method'=>'DELETE',
            'route' => ['students.destroy', '1'],
            'style' => 'display:inline'
        ]) !!}
       
                
            <div class="modal-body">
                  <p class="text-center">
                      Are you sure you want to delete the student?"
                  </p>
                    <input type="hidden" name="s_id" id="cat_id" value="">
                    <div class="form-group">
                            <label for="Resone for Deletion">Resone fpr Deletion</label>
                            <select class="form-control" id="reson" name="reson">
                              <option>نقل السكن</option>
                              <option>السفر النهائي</option>
                              <option>فصل من الحلقة</option>
                              <option>تجاوز فترة الغياب المسموحة</option>
                              <option>مرض</option>
                              <option>بيانات خاطئة</option>
                            </select>
                          </div>

                          <div class="form-group">
                                <label for="notice">Notice</label>
                                <textarea class="form-control" id="notice" rows="3" name="notice"></textarea>
                         </div>
            
  
            </div>
            <div class="modal-footer">
                    {!! Form::button('Yes, Delete', array(
                        'type' => 'submit',
                        'class' => 'btn btn-danger ',
                        'title' => 'Delete Student',
                        'onclick'=>'return confirm("Confirm delete?")'
                )) !!}
                 {!! Form::button('No, Cancel', array(
                    'type' => 'submit',
                    'class' => 'btn btn-success ',
                    'title' => 'No, Cancel',
                    ' data-dismiss'=>'modal'
            )) !!}
            </div>
            {!! Form::close() !!}
      </div>
    </div>
  </div>
@endsection

@include('jssnippets.select2')
@include('jssnippets.flatpickr')

@section('js')
<script>
    flatpickr('.date' , {});

    flatpickr('.time' , {
        enableTime: true,
        noCalendar: true,
        dateFormat: "H:i",
    });
    var add_program_teacher = function(program_id){
        $('#add_teacher input#subject_id').val('');
        $('#add_teacher input#program_id').val(program_id);
        $('#add_teacher').modal('show');

    }
    var add_subject_teacher = function(subject_id){
        $('#add_teacher input#subject_id').val(subject_id);
        $('#add_teacher input#program_id').val('');
        $('#add_teacher').modal('show');
    }

    var change_subject_teacher = function(subject_id , current_employee_id , current_teacher_name){
        $('div#current_teacher_name').text(current_teacher_name);
        $('input#current_employee_id').val(current_employee_id);
        $('input#change_form_subject_id').val(subject_id);
        $('input#change_form_program_id').val('');
        $('#change_teacher').modal('show');
    }

    var change_program_teacher = function(program_id , current_employee_id , current_teacher_name){
        $('div#current_teacher_name').text(current_teacher_name);
        $('input#current_employee_id').val(current_employee_id);
        $('input#change_form_subject_id').val('');
        $('input#change_form_program_id').val(program_id);
        $('#change_teacher').modal('show');
    }

    var add_subject_timetable = function(class_teacher_subject_id){
        $('input#class_teacher_subject_id').val(class_teacher_subject_id)
        $('#add_timetable').modal('show');
    }

    $('form#editPrograms').submit(function(e){
        e.preventDefault();

        $.ajax({
            type: "post",
            url: '/workplace/education/class-programs',
            data: $(this).serialize(),
            dataType: "json",
            success: function (response) {
                if(response.status = "success"){
                    window.location.reload();
                }
            }
        }).fail (function (req){
            
            $('#programs_form_errors').text('');
            $('#programs_form_errors').addClass('alert');
            $.each(req.responseJSON , function (index , value) {
                $.each(value, function (key , err) {
                    $('#programs_form_errors').append(err+'<br>');
                })
            })
        });
    })
    

    var change_student_class = function(student_id, student_name){
        $('div#student_name').text(student_name);
        $('input#student_id').val(student_id);
        $('#change_student_class').modal('show');
    }

 
  
  $('#delete').on('show.bs.modal', function (event) {
      var button = $(event.relatedTarget) 
      var cat_id = button.data('catid') 
      var modal = $(this)
      modal.find('.modal-body #cat_id').val(cat_id);
 
     
     
})
        
    </script>
@append

@extends('layouts.hound')

@section('content')
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-primary card-view">
                    <div class="panel-heading">
                        <h4 class="panel-title txt-light">Home Page Slideshow
                            <a href="{{ route('slider.create') }}" class="btn btn-danger btn-xs pull-right" title="Add New Menu"><span class="glyphicon glyphicon-plus" aria-hidden="true"/></a>
                        </h4>
                    
                    </div>
                    <div class="panel-body">

                        <div class="col-md-12 clearfix">
                            <div class="row text-center panel-heading alert alert-info">
                                <div class="col-sm-5">Image</div>
                                <div class="col-sm-4">Title </div>
                                <div class="col-sm-3">Actions</div>
                            </div>
                            <div id="slides" class="">
                            @foreach($sliders as $item)
                                <div class="row alert alert-success" slide-id="{{ $item->id }}">
                                    <div class="col-sm-5" style="cursor: move;"><img src="{{ asset('images/150x100/'.$item->image) }}"></div>
                                    <div class="col-sm-4">{{ $item->title }}</div>
                                    <div class="col-sm-3">
                                        <a href="{{route('slider.edit',$item->id) }}" class="btn btn-primary btn-xs" title="Edit Menu"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>
                                        {!! Form::open([
                                            'method'=>'DELETE',
                                            'route' => ['slider.destroy', $item->id],
                                            'style' => 'display:inline'
                                        ]) !!}
                                            {!! Form::button('<span class="glyphicon glyphicon-trash" aria-hidden="true" title="Delete Menu" />', array(
                                                    'type' => 'submit',
                                                    'class' => 'btn btn-danger btn-xs',
                                                    'title' => 'Delete Menu',
                                                    'onclick'=>'return confirm("Confirm delete?")'
                                            )) !!}
                                        {!! Form::close() !!}
                                    </div>
                                </div>
                            @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('js')
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

<script>
	        
	$("#slides").sortable({
		update: function (event, ui) {
			sortSlides();
		}
	});
	var sortSlides = function () {  
		var data = {};
		data._token = "{{ csrf_token() }}";
		data.slides = [];
		$.each($('#slides').children(), function (i, el) {
				data.slides.push($(el).attr('slide-id'));
		});
        console.log(data);
		$.ajax({
			type: "post",
			url: "{{ route('sort_slides') }}",
			data: data,
			success: function (response) {
				swal("Good job!", "Slides Have been Sorted Succesfully!", "success", {
					timer: 1500,
				});
			}
		});	
	}
</script>
@endsection
Write php script to print line by line 1-100:
after each number devisable by 3, print "Hi"
after each number devisable by 5, print "tOWER"
after each number devisable by 3 or 5, print "hi tower"



foreach(range(1,100) as $rangeNumber)
{

if($rangeNumber%3 == 0)
{
 echo "hi";
}


if($rangeNumber%5 == 0)

{


echo "tower";



}

if($rangeNumber%5 == 0) || ($rangeNumber%3 == 0)
{

echo "hi tower";
}
}



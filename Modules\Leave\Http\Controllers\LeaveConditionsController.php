<?php

namespace Modules\Leave\Http\Controllers;

use App\Employee;
use App\GeneralSettings;
use App\User;
use Carbon\Carbon;
use App\Traits\PdfGenerate;
use App\Traits\Notification;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use App\Repositories\UserRepository;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Modules\Leave\Http\Requests\EmployeeApplyLeaveStoreRequest;
use Modules\Leave\Http\Requests\EmployeeEditLeaveStoreRequest;
use Modules\Leave\Repositories\LeaveRepository;
use Modules\UserActivityLog\Traits\LogActivity;
use Modules\Leave\Repositories\LeaveTypeRepository;
use Modules\Setup\Repositories\DepartmentRepository;

class LeaveConditionsController extends Controller
{



    public function __invoke(){

        return view("leave::leave_defines.leaveDefinitionTermsConditions");
    }
}

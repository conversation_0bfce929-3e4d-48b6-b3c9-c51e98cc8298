<?php

namespace Modules\Leave\Http\Controllers;
use App\Classes;
use App\Employee;
use App\Role;
use App\LeaveDefine;
use App\LeaveType;
use App\User;
use App\YearCheck;
use App\ApiBaseMethod;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Routing\Controller;
use Modules\Leave\Http\Requests\LeaveDefineStoreRequest;
use Modules\Leave\Repositories\LeaveDefineRepository;
use Modules\Leave\Repositories\LeaveTypeRepository;
use Modules\RolePermission\Entities\InfixRole;
use Modules\RolePermission\Repositories\RoleRepository;
use Modules\UserActivityLog\Traits\LogActivity;

class LeaveDefineController extends Controller
{
    public function __construct(LeaveDefineRepository $leaveDefineRepository,RoleRepository $roleRepo,LeaveTypeRepository $leaveTypeRpo)
    {
        $this->leaveDefineRepository = $leaveDefineRepository;
        $this->roleRepo = $roleRepo;
        $this->leaveTypeRpo = $leaveTypeRpo;
    }



    public function index(Request $request)
    {


        try {


            $data['LeaveDefineList'] = $this->leaveDefineRepository->all();
            $data['RoleList'] = $this->roleRepo->regularRoles();
            $data['LeaveTypeList'] = $this->leaveTypeRpo->all();



            return view('leave::leave_define', $data);

        } catch (\Exception $e) {
            LogActivity::errorLog($e->getMessage());
            Toastr::error('Operation failed');
            return back();
        }




        try{
            $leave_types = LeaveType::all();
//            $roles = Role::where('id', '!=', 18)->where('id', '!=', 25)->where('id', '!=', 10)->get();
            $roles = Role::where('id', '!=', 18)->where('id', '!=', 25)->has('users')->get();

            $classes = Classes::all();

            $leave_defines = LeaveDefine::all();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['leave_types'] = $leave_types->toArray();
                $data['roles'] = $roles->toArray();
                $data['leave_defines'] = $leave_defines->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('leave::leave_define', compact('leave_types', 'roles', 'leave_defines','classes'));

        }catch (\Exception $e) {
            \Log::error($e->getMessage());

           Toastr::error('Operation Failed', 'Failed');
           return redirect()->back();
        }
    }

    public function store(LeaveDefineStoreRequest $request)
    {

        try{

            DB::beginTransaction();
            $defined = $this->leaveDefineRepository->roleWiseLeave($request->leave_type_id,$request->role_id);
            $request['adjust_days'] = $defined ? $defined->total_days : 0;
            if ($defined && empty($request->users))
            {
                return response()->json(trans('leave.Leave Type For this role already defined'));
            }
            $this->leaveDefineRepository->create($request->all());
            $LeaveDefineList = $this->leaveDefineRepository->all();


            DB::commit();
            LogActivity::successLog("Leave Define added Successfully");
            return response()->json([
                'success' => trans('leave.Leave Defined Successfully'),
                'TableData' => (string)view('leave::leave_defines.components.list', compact('LeaveDefineList'))
            ]);


            $leave_type = LeaveType::find($request->leave_type);

            $previous = null;
            if(! is_null($request->addition) ){
                $previous =  LeaveDefine::where('role_id',$request->member_type)
                    ->orwhere('user_id',$request->staff)
                    ->orwhere('user_id',$request->student)
                    ->where('type_id',$request->leave_type)
                    ->latest()->first();

            }

            if( is_numeric($request->student)  || is_numeric($request->staff) ){
                if(is_null($previous)){

                    $leave_define = new LeaveDefine();
                    $leave_define->role_id = $request->member_type;
                    $leave_define->type_id = $request->leave_type;
                    $leave_define->days = $request->days;
                    $leave_define->organization_id = Auth::user()->organization_id;

                    if(is_numeric($request->student)){
                        $leave_define->user_id = $request->student;
                    }
                    elseif(is_numeric($request->staff)){
                        $leave_define->user_id = $request->staff;
                    }
                    $results = $leave_define->save();

                }
                else{

                    $previous->days = ($previous->days + $request->days) ;
                    $results = $previous->save();
                }


            }

            else{


                // if $request->member_type = 24 /** student */, then use User, else use Employee

                if($request->member_type == 24) {

                    $allUsers = User::whereHas('roles', function ($q) use ($request) {

                        $q->where('id', $request->member_type);

                    })->get(['id']);


                }else{

                    $allUsers = Employee::whereHas('roles', function ($q) use ($request) {

                        $q->where('id', $request->member_type);

                    })->get(['id']);
                }





                if( count($allUsers) > 0)  {
                    foreach($allUsers as $user){
                        $leave_define = new LeaveDefine();
                        $leave_define->role_id = $request->member_type;
                        $leave_define->type_id = $request->leave_type;
                        $leave_define->days = $request->days;
                        $leave_define->organization_id = Auth::user()->organization_id;
                        $leave_define->user_id = $user->id;
                        $results = $leave_define->save();
                    }

                    DB::commit();

                }
            }


            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($results) {
                    return ApiBaseMethod::sendResponse(null, 'Visitor has been created successfully.');
                }
                return ApiBaseMethod::sendError('Something went wrong, please try again.');
            } else {
                if ($results) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                }
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        }

        catch (\Exception $e) {
            \Log::error($e->getMessage());
            DB::rollback();

            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {



        try{
            $leave_types = LeaveType::all();
            $roles = Role::all();
            $leave_defines = LeaveDefine::all();
            $leave_define = LeaveDefine::find($id);

            $roles = Role::where('id', '!=', 18)->where('id', '!=', 25)->get();

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['leave_types'] = $leave_types->toArray();
                $data['roles'] = $roles->toArray();
                $data['leave_defines'] = $leave_defines->toArray();
                $data['leave_define'] = $leave_define->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('leave::leave_define', compact('leave_types', 'roles', 'leave_defines', 'leave_define'));

        }catch (\Exception $e) {
            \Log::error($e->getMessage());

            Toastr::error('Operation Failed', 'Failed');
           return redirect()->back();
        }
    }

    public function update(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'role_id' => 'required',
            'leave_type_id' => 'required',
            'total_days' => 'required',
            'max_forward' => 'required_if:balance_forward,==,1',
        ]);


        DB::beginTransaction();
        try {

            $this->leaveDefineRepository->update($request->all(), $request->id);

            $LeaveDefineList = $this->leaveDefineRepository->all();
            DB::commit();
            LogActivity::successLog("Leave Define updated Successfully");
            return response()->json([
                'success' => trans('leave.Leave Define Updated Successfully'),
                'TableData' => (string)view('leave::leave_defines.components.list', compact('LeaveDefineList'))
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            LogActivity::errorLog($e->getMessage());
            return response()->json(trans('common.Something Went Wrong'));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {

        $input = $request->all();
        $validator = Validator::make($input, [
            'id' => "required"
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try{
            $id = $request->id;
            $tables = \App\tableList::getTableList('leave_define_id', $id);

            try {
                if ($tables==null) {
                    if (checkAdmin()) {
                        $result = LeaveDefine::destroy($id);
                    }else{
                        $result = LeaveDefine::where('id',$id)->delete();
                    }
                    if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                        if ($result) {
                            return ApiBaseMethod::sendResponse(null, 'Leave Define has been deleted successfully');
                        } else {
                            return ApiBaseMethod::sendError('Something went wrong, please try again.');
                        }
                    } else {
                        if ($result) {
                            Toastr::success('Operation successful', 'Success');
                            return redirect()->back();
                        } else {
                            Toastr::error('Operation Failed', 'Failed');
                            return redirect()->back();
                        }
                    }
                } else {
                    $msg = 'This data already used in  : ' . $tables . ' Please remove those data first';
                    Toastr::error($msg, 'Failed');
                    return redirect()->back();
                }

            } catch (\Illuminate\Database\QueryException $e) {

                $msg = 'This data already used in  : ' . $tables . ' Please remove those data first';
                Toastr::error($msg, 'Failed');
                return redirect()->back();
            }
        }catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}
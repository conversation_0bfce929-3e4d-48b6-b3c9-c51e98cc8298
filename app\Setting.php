<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

use App\Scopes\OrganizationScope;

class Setting extends Model
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'settings';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'value', 'organization_id'];


    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
    }


    
}

@startuml Teaching Module Schema

!theme vibrant

entity "employees" {
  * id: int
  --
  user_id: int <<FK>>
  department_id: int <<FK>>
  full_name: var<PERSON>r(255)
  employee_number: varchar(255)
  email: varchar(255)
  organization_id: int
}

entity "employee_department" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "class_teachers" {
  * id: int
  --
  class_id: int <<FK>>
  employee_id: int <<FK>>
  organization_id: int
}

entity "class_teacher_subjects" {
  * id: int
  --
  class_teacher_id: int <<FK>>
  subject_id: int <<FK>>
}

entity "classes" {
  * id: int
  --
  class_code: varchar(255)
  subject_id: int <<FK>>
  organization_id: int
}

entity "subjects" {
  * id: int
  --
  name: varchar(255)
  code: varchar(255)
  organization_id: int
}

entity "employee_timetables" {
  * id: int
  --
  employee_id: int <<FK>>
  day: varchar(255)
  start_time: time
  end_time: time
}

entity "class_timetable" {
  * id: int
  --
  class_id: int <<FK>>
  day: varchar(255)
  start_time: time
  end_time: time
}

employees }o--|| employee_department : "belongs to"
employees }o--o{ class_teachers : "teaches"
classes }o--o{ class_teachers : "has teachers"
classes }o--|| subjects : "has subject"
class_teachers }o--o{ class_teacher_subjects : "teaches subject"
employees ||--o{ employee_timetables : "has schedule"
classes ||--o{ class_timetable : "has schedule"

@enduml

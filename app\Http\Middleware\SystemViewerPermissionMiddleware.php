<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

/**
 * Middleware to bypass role/permission checks for system viewers
 * This ensures system viewers can access all parts of the system for demonstration purposes
 */
final class SystemViewerPermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  ...$guards
     * @return mixed
     */
    public function handle(Request $request, Closure $next, ...$guards)
    {
        $user = Auth::guard('employee')->user();
        
        // If user is a system viewer, bypass all role/permission checks
        if ($user && $user->hasRole('system_viewer_' . config('organization_id') . '_')) {
            return $next($request);
        }
        
        // For non-system viewers, continue with normal permission checking
        return $next($request);
    }
} 
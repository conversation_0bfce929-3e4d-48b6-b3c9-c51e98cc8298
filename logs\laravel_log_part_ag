[2025-06-02 18:57:02] production.INFO: Job notifications process started after job fetch at 2025-06-02 18:57:02  
[2025-06-02 18:57:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:57:02 | Error: Notification process failed to complete  
[2025-06-02 18:58:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:58:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:58:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:58:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:58:02] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 18:58:02] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.INFO: Loading dynamic schedule rules for Jobs.af commands  
[2025-06-02 18:58:02] production.INFO: Found dynamic schedule rules {"count":14,"rules":{"1":"Jobs.af Sync - Saturday Morning Peak 1","5":"Jobs.af Sync - Sunday Morning Peak 1","9":"Jobs.af Sync - Monday Morning Peak 1","2":"Jobs.af Sync - Saturday Morning Peak 2","6":"Jobs.af Sync - Sunday Morning Peak 2","10":"Jobs.af Sync - Monday Morning Peak 2","3":"Jobs.af Sync - Saturday Mid-Morning 1","7":"Jobs.af Sync - Sunday Mid-Morning 1","11":"Jobs.af Sync - Monday Mid-Morning 1","4":"Jobs.af Sync - Saturday Mid-Morning 2","8":"Jobs.af Sync - Sunday Mid-Morning 2","12":"Jobs.af Sync - Monday Mid-Morning 2","13":"Jobs.af Sync - Friday Evening","14":"Jobs.af Sync - Saturday Evening"}} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 6","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":1,"rule_name":"Jobs.af Sync - Saturday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 0","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":5,"rule_name":"Jobs.af Sync - Sunday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af","expression":"0 7 * * 1","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":9,"rule_name":"Jobs.af Sync - Monday Morning Peak 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 6","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":2,"rule_name":"Jobs.af Sync - Saturday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 0","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":6,"rule_name":"Jobs.af Sync - Sunday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af","expression":"30 7 * * 1","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":10,"rule_name":"Jobs.af Sync - Monday Morning Peak 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 6","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":3,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 0","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":7,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af","expression":"0 10 * * 1","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":11,"rule_name":"Jobs.af Sync - Monday Mid-Morning 1","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 6","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":4,"rule_name":"Jobs.af Sync - Saturday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 0","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":8,"rule_name":"Jobs.af Sync - Sunday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af","expression":"15 11 * * 1","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":12,"rule_name":"Jobs.af Sync - Monday Mid-Morning 2","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 5","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":13,"rule_name":"Jobs.af Sync - Friday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.DEBUG: Applying dynamic schedule rule {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af","expression":"0 18 * * 6","type":"cron"} 
[2025-06-02 18:58:02] production.INFO: Dynamic schedule rule applied successfully {"rule_id":14,"rule_name":"Jobs.af Sync - Saturday Evening","command":"jobseeker:sync-jobs-af"} 
[2025-06-02 18:58:02] production.INFO: Starting job subscriber notifications at 2025-06-02 18:58:02  
[2025-06-02 18:58:02] production.INFO: Starting general:queue-notify-job-subscribers at 2025-06-02 18:58:02  
[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:58:02 | Error: Queue dispatch may be locked or failed to complete  
[2025-06-02 18:58:02] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:58:02 | Error: Queue dispatch may be locked or failed to complete {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:58:02 | Error: Queue dispatch may be locked or failed to complete at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(129): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 18:58:02] production.INFO: Job notifications process started after job fetch at 2025-06-02 18:58:02  
[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:58:02 | Error: Notification process failed to complete  
[2025-06-02 18:58:02] production.ERROR: The stream or file "/var/www/html/itqanalquran/storage/logs/command_failures.log" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:58:02 | Error: Notification process failed to complete {"exception":"[object] (UnexpectedValueException(code: 0): The stream or file \"/var/www/html/itqanalquran/storage/logs/command_failures.log\" could not be opened in append mode: Failed to open stream: Permission denied
The exception occurred while attempting to log: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:58:02 | Error: Notification process failed to complete at /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/StreamHandler.php:156)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php(44): Monolog\\Handler\\StreamHandler->write()
#1 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(391): Monolog\\Handler\\AbstractProcessingHandler->handle()
#2 /var/www/html/itqanalquran/vendor/monolog/monolog/src/Monolog/Logger.php(646): Monolog\\Logger->addRecord()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(184): Monolog\\Logger->error()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Log/Logger.php(97): Illuminate\\Log\\Logger->writeLog()
#5 /var/www/html/itqanalquran/app/Console/Kernel.php(63): Illuminate\\Log\\Logger->error()
#6 /var/www/html/itqanalquran/app/Console/Kernel.php(574): App\\Console\\Kernel->logCommandFailure()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Kernel->App\\Console\\{closure}()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(871): Illuminate\\Container\\Container->call()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\Event->Illuminate\\Console\\Scheduling\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(339): Illuminate\\Container\\Container->call()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(311): Illuminate\\Console\\Scheduling\\Event->callAfterCallbacks()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/Event.php(226): Illuminate\\Console\\Scheduling\\Event->finish()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(194): Illuminate\\Console\\Scheduling\\Event->run()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(188): Illuminate\\Console\\View\\Components\\Factory->__call()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Scheduling/ScheduleRunCommand.php(133): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#32 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#34 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#35 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#36 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#38 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#39 {main}
"} 
[2025-06-02 18:58:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:58:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:58:02] production.ERROR: Command "jobseeker:notify-job-subscribers" is not defined.

Did you mean one of these?
    general:notify-job-seekers
    general:queue-notify-job-subscribers
    jobseeker:check-inactive-categories
    jobseeker:cleanup-device-tokens
    jobseeker:discover-acbar-categories
    jobseeker:fetch-jobs-af-descriptions
    jobseeker:sync-acbar-jobs
    jobseeker:sync-jobs-af
    jobseeker:test-fcm-notification
    jobseeker:test-refactored-fcm-notification {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"jobseeker:notify-job-subscribers\" is not defined.

Did you mean one of these?
    general:notify-job-seekers
    general:queue-notify-job-subscribers
    jobseeker:check-inactive-categories
    jobseeker:cleanup-device-tokens
    jobseeker:discover-acbar-categories
    jobseeker:fetch-jobs-af-descriptions
    jobseeker:sync-acbar-jobs
    jobseeker:sync-jobs-af
    jobseeker:test-fcm-notification
    jobseeker:test-refactored-fcm-notification at /var/www/html/itqanalquran/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()
#1 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#3 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] jobseeker:notify-job-subscribers failed at 2025-06-02 18:58:02  
[2025-06-02 18:58:02] production.INFO: Starting general:queue-notify-job-subscribers at 2025-06-02 18:58:02  
[2025-06-02 18:58:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:02] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:58:02] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:58:02] production.INFO: Starting job notification queue dispatch from command  
[2025-06-02 18:58:02] production.ERROR: Call to undefined method Modules\JobSeeker\Services\JobService::notifySubscribers() {"exception":"[object] (Error(code: 0): Call to undefined method Modules\\JobSeeker\\Services\\JobService::notifySubscribers() at /var/www/html/itqanalquran/Modules/General/Console/Commands/QueueNotifyJobSubscribers.php:51)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Modules\\General\\Console\\Commands\\QueueNotifyJobSubscribers->handle()
#1 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#6 /var/www/html/itqanalquran/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#8 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#9 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#10 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#12 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#13 {main}
"} 
[2025-06-02 18:58:02] production.ERROR: [COMMAND FAILURE] general:queue-notify-job-subscribers failed at 2025-06-02 18:58:02 | Error: Queue dispatch may be locked or failed to complete  
[2025-06-02 18:58:02] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:02] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:03] production.DEBUG: JobsAfService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:58:03] production.DEBUG: JobService: Loaded canonical categories cache {"count":0,"timestamp":**********} 
[2025-06-02 18:58:03] production.ERROR: Command "general:notify-all-job-subscribers" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"general:notify-all-job-subscribers\" is not defined.

Did you mean one of these?
    general:cleanup-old-jobs
    general:notify-job-seekers
    general:queue-notify-job-subscribers at /var/www/html/itqanalquran/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()
#1 /var/www/html/itqanalquran/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#3 /var/www/html/itqanalquran/artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-02 18:58:03] production.INFO: Job notifications process started after job fetch at 2025-06-02 18:58:03  
[2025-06-02 18:58:03] production.ERROR: [COMMAND FAILURE] general:notify-all-job-subscribers failed at 2025-06-02 18:58:03 | Error: Notification process failed to complete  
[2025-06-02 18:58:18] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:18] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:18] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:18] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:23] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:23] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:23] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:23] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:23] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:23] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:23] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:23] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:23] production.ERROR: Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database {"userId":148,"exception":"[object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:198)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#1 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#10 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#12 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#14 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#42 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#61 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#62 {main}

[previous exception] [object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:91)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#2 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#9 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#11 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#13 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#15 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#17 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#41 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#43 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 {main}

[previous exception] [object] (Exception(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:89)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(89): DateTime->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#2 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#3 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#10 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#12 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#14 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#18 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#42 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#44 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#63 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#64 {main}
"} 
[2025-06-02 18:58:24] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:24] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:24] production.ERROR: Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database {"userId":148,"exception":"[object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:198)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#1 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#10 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#12 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#14 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#42 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#61 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#62 {main}

[previous exception] [object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:91)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#2 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#9 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#11 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#13 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#15 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#17 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#41 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#43 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 {main}

[previous exception] [object] (Exception(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:89)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(89): DateTime->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#2 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#3 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#10 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#12 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#14 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#18 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#42 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#44 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#63 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#64 {main}
"} 
[2025-06-02 18:58:25] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:25] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:25] production.ERROR: Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database {"userId":148,"exception":"[object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:198)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#1 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#10 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#12 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#14 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#42 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#61 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#62 {main}

[previous exception] [object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:91)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#2 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#9 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#11 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#13 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#15 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#17 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#41 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#43 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 {main}

[previous exception] [object] (Exception(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:89)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(89): DateTime->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#2 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#3 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#10 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#12 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#14 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#18 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#42 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#44 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#62 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#63 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#64 {main}
"} 
[2025-06-02 18:58:27] production.INFO: JobSeekerServiceProvider: Middleware registered successfully  
[2025-06-02 18:58:27] production.INFO: JobSeekerServiceProvider: Boot method completed successfully  
[2025-06-02 18:58:27] production.ERROR: Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database {"userId":148,"exception":"[object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Could not parse 'favicon.ico': Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:198)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#1 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#2 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#10 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#12 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#14 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#16 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#42 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#61 /var/www/html/itqanalquran/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#62 {main}

[previous exception] [object] (Carbon\\Exceptions\\InvalidFormatException(code: 0): Failed to parse time string (favicon.ico) at position 0 (f): The timezone could not be found in the database at /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php:91)
[stacktrace]
#0 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(187): Carbon\\Carbon->__construct()
#1 /var/www/html/itqanalquran/vendor/nesbot/carbon/src/Carbon/Traits/Creator.php(224): Carbon\\Carbon::rawParse()
#2 /var/www/html/itqanalquran/Modules/Education/Http/Controllers/MonthlyPlanController.php(574): Carbon\\Carbon::parse()
#3 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): Modules\\Education\\Http\\Controllers\\MonthlyPlanController->show()
#4 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#5 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#6 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#7 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#8 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#9 /var/www/html/itqanalquran/vendor/spatie/laravel-permission/src/Middlewares/PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#10 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle()
#11 /var/www/html/itqanalquran/app/Http/Middleware/CheckMissedClockOutMiddleware.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#12 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckMissedClockOutMiddleware->handle()
#13 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#14 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#15 /var/www/html/itqanalquran/app/Http/Middleware/SetLocale.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle()
#17 /var/www/html/itqanalquran/app/Http/Middleware/ShareAuthDataWithViews.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ShareAuthDataWithViews->handle()
#19 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#23 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#36 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#38 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#39 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#40 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#41 /var/www/html/itqanalquran/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#43 /var/www/html/itqanalquran/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#45 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#53 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/html/itqanalquran/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()

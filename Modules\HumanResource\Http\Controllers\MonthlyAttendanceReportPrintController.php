<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Employee;
use App\Role;
use Dompdf\Dompdf;
use Modules\UserActivityLog\Traits\LogActivity;
use PDF;
use App\User;
use App\Traits\PdfGenerate;
use App\Traits\Notification;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Auth;
use Modules\Setup\Entities\ApplyLoan;
use Illuminate\Contracts\Support\Renderable;
use App\Repositories\UserRepositoryInterface;
use Modules\Payroll\Http\Requests\PayrollFilterFormRequest;
use Modules\Payroll\Http\Requests\PayrollReportFormRequest;
use Modules\Payroll\Repositories\PayrollRepositoryInterface;

class MonthlyAttendanceReportPrintController extends Controller
{
    use Notification, PdfGenerate;
    protected $payrollRepository,$userRepository;

    public function __construct(PayrollRepositoryInterface $payrollRepository)
    {
        $this->middleware(['auth', 'verified']);
        $this->payrollRepository = $payrollRepository;
    }





    public function getPdf($year,$month)
    {
        try {
            $allRoles = Role::where('type','regular_user')->get()->pluck('id')->toArray();

            $r = implode(',',$allRoles);


            $attendance = \DB::select('CALL GetEmployeeMonthlyAttendanceReportByYearMonthRole(?,?,?)',[$month,$year,$r]);


            app()->make('dompdf.wrapper')
                ->setOptions([
                    'logOutputFile' => storage_path('logs/dompdf.html'),
                    'tempDir' => storage_path('fonts'),
                    'isRemoteEnabled' => true
                ]);



            return  PDF::loadHTML(view('humanresource::viewAttendanceMonthlyReport', array('attendance' => $attendance))->render())->setPaper('A4', 'landscape')->download(date('m').''.date('Y').'MonthlyAttReport.pdf');
//            $pdf =




//            return $pdf->stream(date('m').''.date('Y').'MonthlyAttReport.pdf');
            return $pdf->download(date('m').''.date('Y').'MonthlyAttReport.pdf');

//            $payrollDetails = $this->payrollRepository->find($id);
//			return $this->getAttendanceMonthlyReport('Monthly Attendance Report','humanresource::viewAttendanceMonthlyReport', $attendance);
        } catch (\Exception $e) {
            LogActivity::errorLog($e->getMessage());

            dd($e);

            Toastr::error(__('common.Something Went Wrong'), __('common.Error'));
            return back();
        }
    }
}

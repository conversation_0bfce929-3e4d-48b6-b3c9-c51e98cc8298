<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Http\Controllers;

use App\Classes;
use App\Http\Controllers\Controller;
use App\IjazasanadMemorizationPlan;
use App\Student;
use App\StudentIjazasanadMemorizationReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Modules\EducationalReports\Exports\IjazasanadPlansExport;
use Modules\EducationalReports\Exports\IjazasanadReportsExport;
use ZipArchive;

/**
 * IjazasanadExcelExportController orchestrates Excel exports for Ijazah & Sanad programs.
 * Handles both Plans and Reports export types across multiple classes with optional student filtering.
 *
 * Inputs: classes (CSV), monthYear (e.g., "Jan 2025"), studentId (CSV, optional), exportType (plans|reports).
 * Outputs: Aggregated Excel workbook with program-specific sheets.
 * Side effects: Downloads XLSX file; logs export parameters and counts.
 * Security: Requires 'access halaqah export' permission.
 * Errors: Returns 400 for invalid inputs, 404 for no data, 500 for processing errors.
 * Performance: Sets memory/time limits; uses eager loading to prevent N+1 queries.
 */
final class IjazasanadExcelExportController extends Controller
{
    public function __invoke(Request $request)
    {
        try {
            // Validate and parse inputs
            $classIds = $this->parseClassIds($request->input('classes'));
            $monthYear = (string) $request->input('monthYear', '');
            $studentIds = $this->parseStudentIds($request->input('studentId', ''));
            $exportType = in_array($request->input('exportType'), ['plans', 'reports', 'combined']) 
                ? $request->input('exportType') 
                : 'plans';

            if (empty($classIds) || empty($monthYear)) {
                return response()->json([
                    'error' => 'Missing required parameters: classes and monthYear are required'
                ], 400);
            }

            // Parse month and year
            try {
                $date = Carbon::createFromFormat('M Y', $monthYear);
                $month = (int) $date->month;
                $year = (int) $date->year;
            } catch (\Exception $e) {
                return response()->json([
                    'error' => 'Invalid monthYear format. Expected format: "Jan 2025"'
                ], 400);
            }

            // Load and validate classes
            $classes = Classes::with('center')->whereIn('id', $classIds)->get();
            if ($classes->isEmpty()) {
                return response()->json([
                    'error' => 'No valid classes found for the provided class IDs'
                ], 404);
            }

            // Check data availability based on export type
            if ($exportType === 'plans') {
                $hasData = $this->checkPlansDataAvailability($classIds, $month, $year, $studentIds);
                if (!$hasData) {
                    return response()->json([
                        'error' => 'No Ijazah & Sanad plans found for the specified criteria',
                        'details' => 'No active plans found for the selected classes, month-year' . 
                                   (!empty($studentIds) ? ', and students' : '')
                    ], 404);
                }
            } else {
                $hasData = $this->checkReportsDataAvailability($classIds, $month, $year, $studentIds);
                if (!$hasData) {
                    return response()->json([
                        'error' => 'No Ijazah & Sanad reports found for the specified criteria',
                        'details' => 'No reports found for the selected classes, month-year' . 
                                   (!empty($studentIds) ? ', and students' : '')
                    ], 404);
                }
            }

            // Set memory and time limits for large exports
            ini_set('memory_limit', '1024M');
            set_time_limit(600); // 10 minutes

            // Build filters
            $filters = [
                'classIds' => $classIds,
                'month' => $month,
                'year' => $year,
                'monthName' => $date->format('M'),
                'monthYear' => $monthYear,
                'studentIds' => $studentIds,
                'classes' => $classes,
                'exportType' => $exportType
            ];

            // Generate filename
            $filename = $this->generateFilename($classes, $date->format('M'), $year, $exportType, !empty($studentIds));

            // Log export parameters
            \Log::info('Ijazasanad Excel export initiated', [
                'export_type' => $exportType,
                'classes_count' => count($classIds),
                'students_count' => count($studentIds),
                'month_year' => $monthYear,
                'filename' => $filename
            ]);

            // Export based on type
            if ($exportType === 'plans') {
                return Excel::download(new IjazasanadPlansExport($filters), $filename);
            } elseif ($exportType === 'reports') {
                return Excel::download(new IjazasanadReportsExport($filters), $filename);
            } else {
                // combined: create ZIP with two separate files (Plans.xlsx, Reports.xlsx)
                $tempDir = storage_path('tmp/ijazasanad_export_' . uniqid());
                if (!file_exists($tempDir)) {
                    mkdir($tempDir, 0755, true);
                }

                $filesToZip = [];

                // Plans file
                $plansName = 'Ijazasanad_Plans_' . $date->format('M') . '_' . $year . '_' . date('Y-m-d_H-i-s') . '.xlsx';
                $plansPath = $tempDir . '/' . $plansName;
                Excel::store(new IjazasanadPlansExport($filters), $plansName, 'local', \Maatwebsite\Excel\Excel::XLSX);
                $storedPlansPath = storage_path('app/' . $plansName);
                rename($storedPlansPath, $plansPath);
                $filesToZip[] = $plansPath;

                // Reports file
                $reportsName = 'Ijazasanad_Reports_' . $date->format('M') . '_' . $year . '_' . date('Y-m-d_H-i-s') . '.xlsx';
                $reportsPath = $tempDir . '/' . $reportsName;
                Excel::store(new IjazasanadReportsExport($filters), $reportsName, 'local', \Maatwebsite\Excel\Excel::XLSX);
                $storedReportsPath = storage_path('app/' . $reportsName);
                rename($storedReportsPath, $reportsPath);
                $filesToZip[] = $reportsPath;

                // Build ZIP
                $zipFilename = 'Ijazasanad_Export_' . $date->format('M') . '_' . $year . '_' . date('Y-m-d_H-i-s') . '.zip';
                $zipPath = $tempDir . '/' . $zipFilename;
                $zip = new \ZipArchive();
                if ($zip->open($zipPath, \ZipArchive::CREATE) !== true) {
                    return response()->json(['error' => 'Failed to create ZIP file'], 500);
                }
                foreach ($filesToZip as $fp) {
                    if (file_exists($fp)) {
                        $zip->addFile($fp, basename($fp));
                    }
                }
                $zip->close();

                $response = response()->download($zipPath, $zipFilename)->deleteFileAfterSend(true);
                register_shutdown_function(function() use ($tempDir, $filesToZip) {
                    foreach ($filesToZip as $fp) {
                        if (file_exists($fp)) { unlink($fp); }
                    }
                    if (file_exists($tempDir)) { rmdir($tempDir); }
                });
                return $response;
            }

        } catch (\Exception $e) {
            \Log::error('Error exporting Ijazasanad Excel: ' . $e->getMessage(), [
                'classes' => $request->input('classes'),
                'monthYear' => $request->input('monthYear'),
                'studentId' => $request->input('studentId'),
                'exportType' => $request->input('exportType'),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to export Ijazah & Sanad data',
                'message' => 'An error occurred while processing your export request',
                'details' => config('app.debug') ? $e->getMessage() : 'Enable debug mode for details'
            ], 500);
        }
    }

    /**
     * Parse comma-separated class IDs into array of integers
     */
    private function parseClassIds(string $input): array
    {
        if (empty($input)) {
            return [];
        }
        
        $ids = array_filter(array_map('trim', explode(',', $input)));
        return array_values(array_unique(array_map('intval', array_filter($ids, 'is_numeric'))));
    }

    /**
     * Parse comma-separated student IDs into array of integers
     */
    private function parseStudentIds(string $input): array
    {
        if (empty($input)) {
            return [];
        }
        
        $ids = array_filter(array_map('trim', explode(',', $input)));
        return array_values(array_unique(array_map('intval', array_filter($ids, 'is_numeric'))));
    }

    /**
     * Check if plans data exists for the given criteria
     */
    private function checkPlansDataAvailability(array $classIds, int $month, int $year, array $studentIds): bool
    {
        $planYearMonth = sprintf('%d-%02d', $year, $month);
        
        $query = IjazasanadMemorizationPlan::whereIn('class_id', $classIds)
            ->where(function ($q) use ($month, $year, $planYearMonth) {
                $q->where('plan_year_and_month', $planYearMonth)
                  ->orWhere(function ($q2) use ($month, $year) {
                      $q2->whereYear('start_date', $year)
                         ->whereMonth('start_date', $month);
                  })
                  ->orWhere(function ($q3) use ($month, $year) {
                      $q3->whereYear('created_at', $year)
                         ->whereMonth('created_at', $month);
                  });
            })
            ->where('status', 'active');

        if (!empty($studentIds)) {
            $query->whereIn('student_id', $studentIds);
        }

        return $query->exists();
    }

    /**
     * Check if reports data exists for the given criteria
     */
    private function checkReportsDataAvailability(array $classIds, int $month, int $year, array $studentIds): bool
    {
        $query = StudentIjazasanadMemorizationReport::whereIn('class_id', $classIds)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month);

        if (!empty($studentIds)) {
            $query->whereIn('student_id', $studentIds);
        }

        return $query->exists();
    }

    /**
     * Generate descriptive filename for the export
     */
    private function generateFilename($classes, string $monthName, int $year, string $exportType, bool $hasStudentFilter): string
    {
        $filename = 'ijazasanad_' . $exportType;

        // Add center/class info (max 3, then "and_More")
        $centerNames = $classes->pluck('center.name')->filter()->unique()->values();
        if ($centerNames->count() > 0) {
            $centerPart = $centerNames->take(3)->map(function($name) {
                return str_replace([' ', '/', '\\', ':', '*', '?', '"', '<', '>', '|'], '_', $name);
            })->join('_');
            
            if ($centerNames->count() > 3) {
                $centerPart .= '_and_More';
            }
            
            $filename .= '_' . $centerPart;
        }

        // Add month and year
        $filename .= '_' . $monthName . '_' . $year;

        // Add student filter indicator
        if ($hasStudentFilter) {
            $filename .= '_filtered';
        }

        // Add timestamp to avoid conflicts
        $filename .= '_' . date('Y-m-d_H-i-s');

        return $filename . '.xlsx';
    }
}

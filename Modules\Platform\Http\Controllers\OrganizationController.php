<?php

namespace Modules\Platform\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
// use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use App\Organization;
use App\Employee;
use App\Package;
use App\EvaluationSchema;

class OrganizationController extends Controller
{

    public function __construct()
    {
        $this->middleware('organization');
    }

    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {
        return view('platform::index');
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        return view('platform::create');
    }

    /**
     * Store a newly created resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function store(Request $request)
    {
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show()
    {
        return view('platform::show');
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit()
    {
        return view('platform::edit');
    }

    /**
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function update(Request $request)
    {
        $data = $request->all();
        //validate
        if(!Auth::user())
            return redirect()->back();

        $organization = Organization::findOrFail(Auth::user()->id);


        if(isset($request->domain)){

            $organization->domain = $request->domain;

        }


        if(isset($request->theme)){

            $organization->theme = $request->theme;

        }

        if(isset($request->org)){

            $this->validate($request,[
                'username' => 'required|max:255',
                'logo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
                'languages' => 'required',
            ]);

            $logo = time().'.'.$request->logo->getClientOriginalExtension();

            /*
            talk the select file and move it public directory and make avatars
            folder if doesn't exsit then give it that unique name.
            */
            $request->logo->move(public_path('org_logos'), $logo);

          
            // dd($request->all(),$logo);
   
            $organization->username = $request->username;

            $organization->languages = implode('|', $request->languages);

            $organization->logo =  '/org_logos/'.$logo ;//$request->logo;
         
            $organization->status = 'admin_details_needed';
        
            $organization->save();

            $cmd_options = "admin itqanplatform.com ".$request->username.".itqanplatform.com";

            // Need recheck
            
            exec("v-add-web-domain-alias ".$cmd_options);
            exec("v-add-letsencrypt-domain ".$cmd_options);

        }

        if(isset($request->admin)){

            $this->validate($request,[
                'admin_name' => 'required|max:255',
                'admin_email' => 'required|email|max:255|unique:employees,email',
                'password' => 'required|min:6|confirmed',
                'package' => 'required' 
            ]);

            $package = Package::findOrFail($request->package);
            

            $employee = Employee::create([
                'name' => $request->admin_name,
                'email' => $request->admin_email,
                'password' => bcrypt($request->password),
                'organization_id' => $organization->id,
            ]);
            $employee->assignRole($package->role->name);
            
            $organization->status = 'theme_settings_needed';

            if($employee->can('itqan_hefz_program')){
                // activate special program for this organization
                
                // Organization Languages
                $org_languages = explode('|', $organization->languages);
                /// Add New Program

                $program = \App\Program::create(
                    [
                        'code'              => 'ITQAN101',
                        'language'          => $org_languages[0],
                        'organization_id'   => Auth::user()->id,
                        'status'            => 'active',
                        'require_interview' => 1
                    ]
                );

                foreach ($org_languages as $code) {
                    $program->translateOrNew($code)->title = "Hefz and Moraj'ah";
                    $program->translateOrNew($code)->description = " ";
                }

                $program->save();

                /// Assign it as hefz special Program [ Program settings ]

                $program->settings()->create([
                    'setting'   => 'special_program_code',
                    'value'     => 'hefz'
                ]);
                $program->settings()->create([
                    'setting'   => 'program_levels',
                    'value'     => 'special'
                ]);
                $program->settings()->create([
                    'setting'   => 'moshaf_id',
                    'value'     => 1
                ]);
                $program->settings()->create([
                    'setting'   => 'hefz_restricted_to_revision',
                    'value'     => 1
                ]);

                // Add hefz and Revision Evalution Schema
                $evaluation_schema = EvaluationSchema::create([
                    'organization_id'   => $organization->id,
                    'type'              => 'grades',
                    'target'            => 'hefz',
                    'title'             => 'Memorization',
                    'created_by'        => auth()->user()->id,
                    'status'            => 1,
                ]);

                $evaluation_schema = EvaluationSchema::create([
                    'organization_id'   => $organization->id,
                    'type'              => 'grades',
                    'target'            => 'revision',
                    'title'             => 'Revision',
                    'created_by'        => auth()->user()->id,
                    'status'            => 1,
                ]);
                
            }
        
            $organization->save();

        }


        return redirect(config('app.locale').'/home');
    }

    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy()
    {
    }
}

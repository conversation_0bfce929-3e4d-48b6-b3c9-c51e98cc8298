@startuml Finance Module Schema

!theme vibrant

entity "fees_masters" {
  * id: int
  --
  fees_type_id: int <<FK>>
  class_id: int <<FK>>
  amount: decimal
  academic_year_id: int <<FK>>
  organization_id: int
}

entity "fees_types" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "fees_assigns" {
  * id: int
  --
  fees_master_id: int <<FK>>
  student_id: int <<FK>>
  due_date: date
  amount: decimal
  organization_id: int
}

entity "fees_discounts" {
  * id: int
  --
  name: varchar(255)
  amount: decimal
  organization_id: int
}

entity "fees_assign_discounts" {
  * id: int
  --
  fees_assign_id: int <<FK>>
  fees_discount_id: int <<FK>>
  organization_id: int
}

entity "fees_payments" {
  * id: int
  --
  fees_assign_id: int <<FK>>
  student_id: int <<FK>>
  amount: decimal
  payment_date: date
  organization_id: int
}

entity "students" {
  * id: int
  --
  full_name: varchar(255)
  organization_id: int
}

entity "classes" {
  * id: int
  --
  class_code: varchar(255)
  organization_id: int
}

entity "academic_years" {
  * id: int
  --
  name: varchar(255)
  organization_id: int
}

entity "chart_accounts" {
  * id: int
  --
  name: varchar(255)
  type: varchar(255)
  organization_id: int
}

fees_masters }o--|| fees_types : "has type"
fees_masters }o--|| classes : "for class"
fees_masters }o--|| academic_years : "for academic year"
fees_masters ||--o{ fees_assigns : "assigned to students"
fees_assigns }o--|| students : "for student"
fees_assigns ||--o{ fees_assign_discounts : "has discounts"
fees_discounts ||--o{ fees_assign_discounts : "applied to"
fees_assigns ||--o{ fees_payments : "has payments"
fees_payments }o--|| students : "by student"

@enduml

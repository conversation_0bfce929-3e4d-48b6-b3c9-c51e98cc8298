<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * App\CenterTranslation
 *
 * @property int $id
 * @property int $center_id
 * @property string|null $name
 * @property string|null $description
 * @property string $locale
 * @property-read \App\Center $center
 * @method static \Illuminate\Database\Eloquent\Builder|CenterTranslation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CenterTranslation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CenterTranslation query()
 * @method static \Illuminate\Database\Eloquent\Builder|CenterTranslation whereCenterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CenterTranslation whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CenterTranslation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CenterTranslation whereLocale($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CenterTranslation whereName($value)
 * @mixin \Eloquent
 */
class CenterTranslation extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'center_translations';

    public $timestamps = false;

    /**
    * The database primary key value.
    *
    * @var string
    */
    // protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'description'];

    public function center()
    {
        return $this->belongsTo('App\Center');
    }
    
}

<?php

namespace App\Services;

use App\Student;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

/**
 * Centralized service for handling student user creation operations
 * Follows DRY principle and provides consistent user creation logic across the application
 */
class StudentUserService
{
    /**
     * Create a user account for a student if they don't already have one
     *
     * @param Student $student The student to create a user for
     * @return array Result containing success status, user data, and credentials
     */
    public function createUserForStudent(Student $student)
    {
        // Check if student already has a valid user account
        if ($this->hasValidUser($student)) {
            return [
                'success' => false,
                'message' => 'Student already has a user account',
                'user_exists' => true
            ];
        }

        // Check for soft-deleted user that can be restored
        $softDeletedUser = $this->findSoftDeletedUser($student);
        if ($softDeletedUser) {
            return $this->restoreSoftDeletedUser($softDeletedUser, $student);
        }

        // Validate required student data
        $validation = $this->validateStudentData($student);
        if (!$validation['valid']) {
            return [
                'success' => false,
                'message' => $validation['message'],
                'validation_error' => true
            ];
        }

        try {
            $userData = null;
            $credentials = null;

            DB::transaction(function () use ($student, &$userData, &$credentials) {
                // Generate unique username
                $username = $this->generateUniqueUsername($student->full_name);
                
                // Generate secure password
                $password = $this->generatePassword($student);
                
                // Create user record (audit trail will be handled by User model boot method)
                $user = User::create([
                    'full_name' => $student->full_name,
                    'display_name' => $student->display_name ?: $student->full_name,
                    'full_name_trans' => $student->full_name_trans,
                    'email' => $student->email,
                    'username' => $username,
                    'password' => Hash::make($password),
                    'nationality' => $student->nationality,
                    'gender' => $student->gender,
                    'organization_id' => config('organization_id'),
                    'access_status' => '0',
                    'is_administrator' => 'no',
                    'email_verified_at' => now(), // Admin-created accounts are pre-verified
                ]);

                // Assign student role
                $this->assignStudentRole($user);

                // Update student with user_id
                $student->update(['user_id' => $user->id]);

                $userData = $user;
                $credentials = [
                    'username' => $username,
                    'password' => $password
                ];
            });

            return [
                'success' => true,
                'message' => 'User created successfully',
                'user' => $userData,
                'credentials' => $credentials,
                'student' => $student
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error creating user: ' . $e->getMessage(),
                'exception' => true
            ];
        }
    }

    /**
     * Create users for multiple students in bulk
     * 
     * @param array $studentIds Array of student IDs
     * @return array Result containing success/failure counts and created user details
     */
    public function createUsersForStudents(array $studentIds)
    {
        $results = [
            'success_count' => 0,
            'error_count' => 0,
            'already_exist_count' => 0,
            'validation_error_count' => 0,
            'created_users' => [],
            'errors' => []
        ];

        // Get students who might need user accounts
        $students = Student::whereIn('id', $studentIds)->get();

        foreach ($students as $student) {
            $result = $this->createUserForStudent($student);
            
            if ($result['success']) {
                $results['success_count']++;
                $results['created_users'][] = [
                    'student_id' => $student->id,
                    'student_name' => $student->full_name,
                    'student_email' => $student->email,
                    'username' => $result['credentials']['username'],
                    'password' => $result['credentials']['password']
                ];
            } elseif (isset($result['user_exists'])) {
                $results['already_exist_count']++;
            } elseif (isset($result['validation_error'])) {
                $results['validation_error_count']++;
                $results['errors'][] = "Student {$student->full_name}: {$result['message']}";
            } else {
                $results['error_count']++;
                $results['errors'][] = "Student {$student->full_name}: {$result['message']}";
            }
        }

        return $results;
    }

    /**
     * Check if student has a valid (non-soft-deleted) user account
     *
     * @param Student $student
     * @return bool
     */
    public function hasValidUser(Student $student)
    {
        if (!$student->user_id) {
            return false;
        }

        // Check if user actually exists and is not soft-deleted
        $user = User::find($student->user_id);
        return $user !== null;
    }

    /**
     * Find a soft-deleted user account for a student
     *
     * @param Student $student
     * @return User|null
     */
    public function findSoftDeletedUser(Student $student)
    {
        // First check if student has a user_id pointing to a soft-deleted user
        if ($student->user_id) {
            $user = User::withTrashed()->find($student->user_id);
            if ($user && $user->trashed()) {
                return $user;
            }
        }

        // If no user_id or user_id doesn't point to soft-deleted user,
        // try to find by email if student has an email
        if ($student->email) {
            $user = User::withTrashed()
                ->where('email', $student->email)
                ->whereNotNull('deleted_at')
                ->first();
            if ($user) {
                return $user;
            }
        }

        return null;
    }

    /**
     * Restore a soft-deleted user account for a student
     *
     * @param User $user The soft-deleted user to restore
     * @param Student $student The student to associate with the user
     * @return array Result containing success status, user data, and credentials
     */
    public function restoreSoftDeletedUser(User $user, Student $student)
    {
        try {
            $credentials = null;
            $userData = null;

            DB::transaction(function () use ($user, $student, &$userData, &$credentials) {
                // Restore the soft-deleted user (audit trail will be handled by User model boot method)
                $user->restore();

                // Update user information with current student data (audit trail will be handled by User model boot method)
                $user->update([
                    'full_name' => $student->full_name,
                    'display_name' => $student->display_name ?: $student->full_name,
                    'full_name_trans' => $student->full_name_trans,
                    'email' => $student->email,
                    'nationality' => $student->nationality,
                    'gender' => $student->gender,
                    'organization_id' => config('organization_id'),
                    'access_status' => '0',
                    'is_administrator' => 'no',
                    'email_verified_at' => now(), // Admin-restored accounts are pre-verified
                ]);

                // Update student with user_id if not already set
                if (!$student->user_id || $student->user_id !== $user->id) {
                    $student->update(['user_id' => $user->id]);
                }

                // Re-assign student role (in case it was removed)
                $this->assignStudentRole($user);

                $userData = $user;

                // Generate new credentials for security
                $password = $this->generatePassword($student);
                $user->update(['password' => Hash::make($password)]);

                $credentials = [
                    'username' => $user->username,
                    'password' => $password
                ];
            });

            return [
                'success' => true,
                'message' => 'User account restored successfully',
                'user' => $userData,
                'credentials' => $credentials,
                'student' => $student,
                'restored' => true
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error restoring user: ' . $e->getMessage(),
                'exception' => true
            ];
        }
    }

    /**
     * Validate that student has required data for user creation
     * 
     * @param Student $student
     * @return array
     */
    private function validateStudentData(Student $student)
    {
        if (empty($student->full_name)) {
            return ['valid' => false, 'message' => 'Student full name is required'];
        }

        if (empty($student->email)) {
            return ['valid' => false, 'message' => 'Student email is required'];
        }

        if (!filter_var($student->email, FILTER_VALIDATE_EMAIL)) {
            return ['valid' => false, 'message' => 'Student email is not valid'];
        }

        return ['valid' => true];
    }

    /**
     * Generate a unique username based on student name
     * 
     * @param string $fullName
     * @return string
     */
    private function generateUniqueUsername($fullName)
    {
        // Clean the name: remove spaces, hyphens, dashes, and take first 6 characters
        $baseName = substr(preg_replace('/[^a-zA-Z0-9]/', '', $fullName), 0, 6);
        $baseName = strtolower($baseName);
        
        // Ensure minimum length
        if (strlen($baseName) < 3) {
            $baseName = str_pad($baseName, 3, '0');
        }

        $username = $baseName;
        $counter = 1;

        // Check for uniqueness and append number if needed
        while (User::where('username', $username)->exists()) {
            $username = $baseName . $counter;
            $counter++;
        }

        return $username;
    }

    /**
     * Generate a secure password for the student
     * 
     * @param Student $student
     * @return string
     */
    private function generatePassword(Student $student)
    {
        // Use student ID + first 4 characters of name (consistent with existing logic)
        $nameChars = substr(str_replace(' ', '', $student->full_name), 0, 4);
        return $student->id . $nameChars;
    }

    /**
     * Assign student role to user
     * 
     * @param User $user
     * @return void
     */
    private function assignStudentRole(User $user)
    {
        try {
            // Try to find student role by name first
            $studentRole = Role::where('name', 'student')->first();
            
            if ($studentRole) {
                $user->assignRole($studentRole);
            } else {
                // Fallback: try to find by ID (legacy support)
                $studentRole = Role::find(23); // Common student role ID
                if ($studentRole) {
                    $user->assignRole($studentRole);
                }
            }
        } catch (\Exception $e) {
            // Log the error but don't fail user creation
            \Log::warning("Failed to assign student role to user {$user->id}: " . $e->getMessage());
        }
    }
}

<?php

namespace App\Console\Commands;

use App\MissedClockOut;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupMissedClockoutRecords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:missedclockouts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Permanently delete all soft-deleted missed clockout records';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            // Count soft-deleted records
            $count = MissedClockOut::onlyTrashed()->count();
            $this->info("Found {$count} soft-deleted records");
            Log::info("CleanupMissedClockoutRecords: Found {$count} soft-deleted records");
            
            // Force delete them
            MissedClockOut::onlyTrashed()->forceDelete();
            
            $this->info("Permanently deleted {$count} soft-deleted records");
            Log::info("CleanupMissedClockoutRecords: Permanently deleted {$count} soft-deleted records");
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Error occurred: " . $e->getMessage());
            Log::error("CleanupMissedClockoutRecords error: " . $e->getMessage());
            Log::error($e->getTraceAsString());
            
            return Command::FAILURE;
        }
    }
} 
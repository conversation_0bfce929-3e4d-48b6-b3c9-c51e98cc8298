<?php

namespace App\Exports;

use App\Student;
use App\Classes;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use App\StudentIjazasanadMemorizationReport;
use App\IjazasanadMemorizationPlan;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Color;

class IjazasanadClassReportExport implements WithMultipleSheets
{
    use Exportable;

    protected $classId;
    protected $monthYear;
    protected $class;

    public function __construct($classId, $monthYear)
    {
        $this->classId = $classId;
        $this->monthYear = $monthYear;
        
        // Load class with all necessary relationships
        $this->class = Classes::with([
            'center',
            'teachers',
            'programs.programTranslations'
        ])->findOrFail($classId);
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        // Add header sheet with class details
        $sheets[] = new IjazasanadClassHeaderSheet($this->class, $this->monthYear);
        
        // Add detailed student reports sheet
        $sheets[] = new IjazasanadClassStudentReportsSheet($this->classId, $this->monthYear);
        
        // Add summary sheet
        $sheets[] = new IjazasanadClassSummarySheet($this->classId, $this->monthYear);

        return $sheets;
    }
}

/**
 * Header Sheet with Class Details and Hyperlinks
 */
class IjazasanadClassHeaderSheet implements FromCollection, WithTitle, WithHeadings, WithEvents, ShouldAutoSize
{
    protected $class;
    protected $monthYear;

    public function __construct($class, $monthYear)
    {
        $this->class = $class;
        $this->monthYear = $monthYear;
    }

    public function collection()
    {
        // Get class details
        $className = $this->class->name ?? 'N/A';
        $centerName = $this->class->center->location ?? 'N/A';
        $centerId = $this->class->center->id ?? null;
        
        // Get teachers
        $teachers = $this->class->teachers;
        $teacherNames = $teachers->pluck('full_name')->toArray();
        $teacherIds = $teachers->pluck('id')->toArray();
        
        // Get program
        $program = $this->class->programs->first();
        $programTitle = 'N/A';
        $programId = null;
        if ($program) {
            $programTitle = $program->programTranslations->first()->title ?? $program->title ?? 'N/A';
            $programId = $program->id;
        }

        $data = collect([
            ['Field', 'Value', 'URL'], // Headers
            ['Class Title', $className, $this->class->id ? route('classes.show', $this->class->id) : ''],
            ['Center', $centerName, $centerId ? route('centers.show', $centerId) : ''],
            ['Program', $programTitle, $programId ? route('programs.show', $programId) : ''],
            ['Report Month/Year', $this->monthYear, ''],
            ['Generated At', now()->format('Y-m-d g:i A'), ''],
            [''], // Empty row
            ['Teachers:', '', ''], // Teacher section header
        ]);

        // Add teachers with their URLs
        foreach ($teacherNames as $index => $teacherName) {
            $teacherId = $teacherIds[$index] ?? null;
            $teacherUrl = $teacherId ? route('employees.show', $teacherId) : '';
            $data->push(['', $teacherName, $teacherUrl]);
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'Class Information Report - ' . $this->monthYear,
            '',
            ''
        ];
    }

    public function title(): string
    {
        return 'Class Details';
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();
                
                // Set title in A1 spanning 3 columns
                $sheet->setCellValue('A1', 'Class Information Report - ' . $this->monthYear);
                $sheet->mergeCells('A1:C1');
                
                // Style the title
                $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
                $sheet->getStyle('A1')->getAlignment()->setHorizontal('center');
                
                // Start data from row 3
                $startRow = 3;
                $data = $this->collection();
                
                foreach ($data as $rowIndex => $row) {
                    $currentRow = $startRow + $rowIndex;
                    
                    if (count($row) >= 2) {
                        $sheet->setCellValue('A' . $currentRow, $row[0]);
                        $sheet->setCellValue('B' . $currentRow, $row[1]);
                        
                        // Add hyperlink if URL exists
                        if (isset($row[2]) && !empty($row[2]) && $row[2] !== '') {
                            $sheet->getCell('B' . $currentRow)->getHyperlink()->setUrl($row[2]);
                            $sheet->getStyle('B' . $currentRow)->getFont()->setColor(new Color('0000FF'));
                            $sheet->getStyle('B' . $currentRow)->getFont()->setUnderline(true);
                        }
                    }
                }
                
                // Style the field names (column A)
                $sheet->getStyle('A:A')->getFont()->setBold(true);
                
                // Add borders to the data area
                $lastRow = $startRow + $data->count() - 1;
                if ($lastRow >= $startRow) {
                    $sheet->getStyle('A' . $startRow . ':B' . $lastRow)->getBorders()->getAllBorders()->setBorderStyle('thin');
                }
            },
        ];
    }
}

class IjazasanadClassStudentReportsSheet implements FromCollection, WithHeadings, WithMapping, WithTitle, ShouldAutoSize, WithStyles
{
    protected $classId;
    protected $date;

    public function __construct($classId, $monthYear)
    {
        $this->classId = $classId;
        $this->date = Carbon::createFromFormat('F Y', $monthYear);
    }

    public function collection()
    {
        $month = $this->date->month;
        $year = $this->date->year;

        // Get all students in the class and their reports for the month/year
        $results = DB::table('student_ijazasanad_memorization_report as simr')
            ->leftJoin('students as s', 'simr.student_id', '=', 's.id')
            ->leftJoin('evaluation_schema_options as eso', 'simr.ijazasanad_evaluation_id', '=', 'eso.id')
            ->leftJoin('moshaf_surah as ms1', 'simr.hefz_from_surat', '=', 'ms1.id')
            ->leftJoin('moshaf_surah as ms2', 'simr.hefz_to_surat', '=', 'ms2.id')
            ->leftJoin('attendance_options as ao', 'simr.attendance_id', '=', 'ao.id')
            ->where('simr.class_id', $this->classId)
            ->whereYear('simr.created_at', $year)
            ->whereMonth('simr.created_at', $month)
            ->where(function ($query) {
                $query->whereNotNull('simr.hefz_from_surat')
                      ->whereNotNull('simr.hefz_from_ayat')
                      ->whereNotNull('simr.hefz_to_surat')
                      ->whereNotNull('simr.hefz_to_ayat')
                      ->orWhereNotNull('simr.talqeen_from_lesson')
                      ->orWhereNotNull('simr.revision_from_lesson')
                      ->orWhereNotNull('simr.jazariyah_from_lesson')
                      ->orWhereNotNull('simr.seminars_from_lesson');
            })
            ->select([
                's.full_name',
                'simr.created_at as report_date',
                'simr.hefz_from_surat',
                'simr.hefz_from_ayat',
                'simr.hefz_to_surat',
                'simr.hefz_to_ayat',
                'simr.talqeen_from_lesson',
                'simr.talqeen_to_lesson',
                'simr.revision_from_lesson',
                'simr.revision_to_lesson',
                'simr.jazariyah_from_lesson',
                'simr.jazariyah_to_lesson',
                'simr.seminars_from_lesson',
                'simr.seminars_to_lesson',
                'simr.pages_memorized',
                'simr.ijazasanad_evaluation_note as teacher_comments',
                'eso.title as performance',
                'ao.title as attendance_status',
                'ms1.eng_name as from_surah_name',
                'ms2.eng_name as to_surah_name'
            ])
            ->orderBy('s.full_name')
            ->orderBy('simr.created_at')
            ->get();

        return $results->map(function ($item) {
            return (array)$item;
        });
    }

    public function headings(): array
    {
        return [
            'Student Name',
            'Date',
            'Day',
            'Attendance',
            'From Surah',
            'From Ayat',
            'To Surah',
            'To Ayat',
            'Talqeen From',
            'Talqeen To',
            'Revision From',
            'Revision To',
            'Jazariyah From',
            'Jazariyah To',
            'Seminars From',
            'Seminars To',
            'Juz Covered',
            'Pages Memorized',
            'Teacher Comments',
            'Performance'
        ];
    }

    public function map($row): array
    {
        $date = Carbon::parse($row['report_date']);
        $studentName = $row['full_name'] ?? 'N/A';
        
        // Calculate Juz coverage based on pages
        $juzCovered = $row['pages_memorized'] ? round($row['pages_memorized'] / 20, 2) : 0;
        
        return [
            $studentName,
            $date->format('Y-m-d'),
            $date->format('l'),
            $row['attendance_status'] ?? 'N/A',
            $row['from_surah_name'] ?? $row['hefz_from_surat'] ?? '',
            $row['hefz_from_ayat'] ?? '',
            $row['to_surah_name'] ?? $row['hefz_to_surat'] ?? '',
            $row['hefz_to_ayat'] ?? '',
            $row['talqeen_from_lesson'] ?? '',
            $row['talqeen_to_lesson'] ?? '',
            $row['revision_from_lesson'] ?? '',
            $row['revision_to_lesson'] ?? '',
            $row['jazariyah_from_lesson'] ?? '',
            $row['jazariyah_to_lesson'] ?? '',
            $row['seminars_from_lesson'] ?? '',
            $row['seminars_to_lesson'] ?? '',
            $juzCovered,
            $row['pages_memorized'] ?? 0,
            strip_tags($row['teacher_comments'] ?? ''),
            $row['performance'] ?? ''
        ];
    }

    public function title(): string
    {
        return 'Class Progress Reports';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}

class IjazasanadClassSummarySheet implements FromCollection, WithHeadings, WithMapping, WithTitle, ShouldAutoSize, WithStyles
{
    protected $classId;
    protected $date;

    public function __construct($classId, $monthYear)
    {
        $this->classId = $classId;
        $this->date = Carbon::createFromFormat('F Y', $monthYear);
    }

    public function collection()
    {
        $summaryData = $this->getClassSummaryData();
        return collect([$summaryData]);
    }

    public function headings(): array
    {
        return [
            'Number of Students',
            'Average Attendance (%)',
            'Average Achievement (%)',
            'Total Juz Planned',
            'Total Juz Completed',
            'Juz Progress (%)',
            'Total Pages Planned',
            'Total Pages Completed',
            'Pages Progress (%)'
        ];
    }

    public function map($row): array
    {
        return [
            $row['no_of_students'],
            $row['avg_attendance'],
            $row['avg_achievement'],
            $row['total_planned_juz'],
            $row['total_completed_juz'],
            $row['juz_progress'],
            $row['total_planned_pages'],
            $row['total_completed_pages'],
            $row['page_progress']
        ];
    }

    public function title(): string
    {
        return 'Class Summary';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }

    private function getClassSummaryData()
    {
        $month = $this->date->month;
        $year = $this->date->year;

        // Get students in the class - same logic as web controllers
        $students = Student::whereHas('joint_classes', function ($query) {
            $query->where('class_id', $this->classId)
                  ->whereNull('class_students.end_date') // Ensure the student is still active in the class
                  ->whereNull('class_students.deleted_at'); // Exclude soft-deleted relationships
        })->with(['studentProgramLevels.programlevel'])->get();

        if ($students->isEmpty()) {
            return $this->getEmptyData();
        }

        // Calculate summary metrics using EXACT same logic as MonthEndIjazasanadSummaryController
        $studentCount = $students->count();
        $avgAttendance = $this->calculateAverageAttendance($students, $this->classId, $month, $year);
        $avgAchievement = $this->calculateAverageAchievement($students, $this->classId, $month, $year);
        
        // Calculate Juz and Page metrics using EXACT same logic
        $juzMetrics = $this->calculateJuzMetrics($students, $this->classId, $month, $year);
        $pageMetrics = $this->calculatePageMetrics($students, $this->classId, $month, $year);

        return [
            'no_of_students' => $studentCount,
            'avg_attendance' => round($avgAttendance, 1),
            'avg_achievement' => round($avgAchievement, 1),
            'total_planned_juz' => $juzMetrics['planned'],
            'total_completed_juz' => round($juzMetrics['completed'], 1),
            'juz_progress' => $juzMetrics['percentage'],
            'total_planned_pages' => $pageMetrics['planned'],
            'total_completed_pages' => $pageMetrics['completed'],
            'page_progress' => $pageMetrics['percentage']
        ];
    }

    private function getEmptyData(): array
    {
        return [
            'no_of_students' => 0,
            'avg_attendance' => 0,
            'avg_achievement' => 0,
            'total_planned_juz' => 0,
            'total_completed_juz' => 0,
            'juz_progress' => 0,
            'total_planned_pages' => 0,
            'total_completed_pages' => 0,
            'page_progress' => 0
        ];
    }

    // EXACT COPY of calculateAverageAttendance from MonthEndIjazasanadSummaryController
    private function calculateAverageAttendance($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) {
            return 0.0;
        }

        // Get total scheduled classes for the month
        $class = Classes::find($classId);
        if (!$class || !$class->timetable) {
            return 0.0;
        }

        $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
        if ($totalClasses <= 0) {
            return 0.0;
        }

        $totalAttendancePercentage = 0;
        $validStudents = 0;

        foreach ($students as $student) {
            // Count attended classes (both on-time and late)
            $attendedClasses = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereIn('attendance_id', [1, 2]) // 1 = late, 2 = on time
                ->count();

            $attendancePercentage = min(100.0, ($attendedClasses / $totalClasses) * 100);
            $totalAttendancePercentage += $attendancePercentage;
            $validStudents++;
        }

        return $validStudents > 0 ? $totalAttendancePercentage / $validStudents : 0.0;
    }

    // EXACT COPY of calculateAverageAchievement from MonthEndIjazasanadSummaryController
    private function calculateAverageAchievement($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) {
            return 0.0;
        }

        $totalAchievementPercentage = 0;
        $validStudents = 0;

        foreach ($students as $student) {
            // Load student program levels for level detection
            $student->loadMissing('studentProgramLevels.programlevel');
            
            // Get the student's plan for the month
            $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
                ->where(function ($query) use ($month, $year, $classId) {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('created_at', $year)
                            ->whereMonth('created_at', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                            ->whereMonth('start_date', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    });
                })
                ->first();

            if (!$plan) {
                continue;
            }

            // Get actual achievements from reports
            $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            if ($reports->isEmpty()) {
                continue;
            }

            // Detect student level and calculate achievement accordingly
            $studentLevel = $this->detectStudentLevel($student);
            
            if ($studentLevel === 'level1') {
                $completionData = $this->calculateLevel1Completion($plan, $reports);
                $achievementPercentage = $completionData['completion_rate'];
            } else {
                // Level 2 or fallback calculation
                $completionData = $this->calculateLevel2Completion($plan, $reports);
                $achievementPercentage = $completionData['completion_rate'];
            }
            
            if ($achievementPercentage > 0) {
                $totalAchievementPercentage += $achievementPercentage;
                $validStudents++;
            }
        }

        return $validStudents > 0 ? $totalAchievementPercentage / $validStudents : 0.0;
    }

    // EXACT COPY of detectStudentLevel from MonthEndIjazasanadSummaryController
    private function detectStudentLevel($studentDetails): ?string
    {
        if (method_exists($studentDetails, 'loadMissing')) {
            $studentDetails->loadMissing('studentProgramLevels.programlevel');
        }

        foreach ($studentDetails->studentProgramLevels as $studentProgramLevel) {
            if ($studentProgramLevel->programlevel) {
                $levelName = strtolower($studentProgramLevel->programlevel->title);
                if (str_contains($levelName, 'level 1')) {
                    return 'level1';
                }
                if (str_contains($levelName, 'level 2')) {
                    return 'level2';
                }
            }
        }
        return null;
    }

    // EXACT COPY of calculateLevel1Completion from MonthEndIjazasanadSummaryController
    private function calculateLevel1Completion($plan, $reports): array
    {
        $components = ['talqeen', 'revision', 'jazariyah', 'seminars'];
        $componentDetails = [];
        $totalCompletion = 0;
        $validComponents = 0;

        foreach ($components as $componentName) {
            $fromField = "{$componentName}_from_lesson";
            $toField = "{$componentName}_to_lesson";

            if (!empty($plan->$fromField) && !empty($plan->$toField) && $plan->$fromField <= $plan->$toField) {
                $validComponents++;
                $plannedFrom = $plan->$fromField;
                $plannedTo = $plan->$toField;
                $plannedRangeCount = $plannedTo - $plannedFrom + 1;

                $completedLessons = $this->getUniqueCompletedLessonsForComponent($reports, $componentName);
                $plannedLessonsRange = range($plannedFrom, $plannedTo);
                $achievedInRange = count(array_intersect($completedLessons, $plannedLessonsRange));
                
                $componentProgress = ($plannedRangeCount > 0) ? ($achievedInRange / $plannedRangeCount) * 100 : 0;
                $totalCompletion += $componentProgress;
                $componentDetails[$componentName] = round($componentProgress, 2);
            } else {
                $componentDetails[$componentName] = 0;
            }
        }

        $overallCompletionRate = $validComponents > 0 ? ($totalCompletion / $validComponents) : 0;

        return [
            'completion_rate' => round($overallCompletionRate, 2),
            'type' => 'level1',
            'components' => $componentDetails,
            'valid_components' => $validComponents,
        ];
    }

    // EXACT COPY of getUniqueCompletedLessonsForComponent from MonthEndIjazasanadSummaryController
    private function getUniqueCompletedLessonsForComponent($reports, string $componentName): array
    {
        $allLessons = [];
        $fromField = "{$componentName}_from_lesson";
        $toField = "{$componentName}_to_lesson";

        foreach ($reports as $report) {
            if (!empty($report->$fromField) && !empty($report->$toField) && $report->$fromField <= $report->$toField) {
                $allLessons = array_merge($allLessons, range($report->$fromField, $report->$toField));
            }
        }

        return array_unique($allLessons);
    }

    // EXACT COPY of calculateLevel2Completion from MonthEndIjazasanadSummaryController
    private function calculateLevel2Completion($plan, $reports): array
    {
        $plannedPages = 0;
        
        // Check if we have valid hefz plan coordinates
        if (!empty($plan->start_from_surat) && !empty($plan->start_from_ayat) && 
            !empty($plan->to_surat) && !empty($plan->to_ayat)) {
            
            try {
                // Use existing page calculation logic
                if ($plan->study_direction == 'backward') {
                    $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                        $plan->start_from_surat,
                        $plan->start_from_ayat,
                        $plan->to_surat,
                        $plan->to_ayat
                    ]);
                    $plannedPages = $numberofPages[0]->numberofPagesSum ?? 0;
                } else {
                    // Forward direction
                    DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                        $plan->start_from_surat,
                        $plan->start_from_ayat,
                        $plan->to_surat,
                        $plan->to_ayat
                    ]);
                    $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                    $plannedPages = $results[0]->number_of_pages_sum ?? 0;
                }
            } catch (\Exception $e) {
                \Log::error('Error calculating planned pages: ' . $e->getMessage());
            }
        }

        // Calculate achieved pages from reports
        $achievedPages = $reports->sum('pages_memorized') ?? 0;
        
        // Calculate completion rate with 100% cap
        $completionRate = ($plannedPages > 0) ? min(100, ($achievedPages / $plannedPages) * 100) : 0;

        return [
            'completion_rate' => round($completionRate, 2),
            'type' => 'level2',
            'planned_pages' => $plannedPages,
            'achieved_pages' => $achievedPages,
        ];
    }

    // EXACT COPY of calculateJuzMetrics from MonthEndIjazasanadSummaryController
    private function calculateJuzMetrics($students, int $classId, int $month, int $year): array
    {
        if ($students->isEmpty()) {
            return ['planned' => 0, 'completed' => 0, 'percentage' => 0];
        }

        $totalPlannedJuz = 0;
        $totalCompletedJuz = 0;

        foreach ($students as $student) {
            // Get planned Juz from plan
            $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
                ->where(function ($query) use ($month, $year, $classId) {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('created_at', $year)
                            ->whereMonth('created_at', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                            ->whereMonth('start_date', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    });
                })
                ->first();

            if ($plan) {
                // Count planned Juz' based on from_surat_juz_id to to_surat_juz_id
                if ($plan->from_surat_juz_id && $plan->to_surat_juz_id) {
                    $plannedJuz = max(0, $plan->to_surat_juz_id - $plan->from_surat_juz_id + 1);
                    $totalPlannedJuz += $plannedJuz;
                }
            }

            // Get completed Juz from reports
            $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereNotNull('hefz_from_surat')
                ->whereNotNull('hefz_to_surat')
                ->get();

            if ($reports->isNotEmpty()) {
                $achievedJuz = $this->calculateAchievedJuzFromReports($reports);
                $totalCompletedJuz += $achievedJuz;
            }
        }

        $percentage = $totalPlannedJuz > 0 ? min(100, round(($totalCompletedJuz / $totalPlannedJuz) * 100, 1)) : 0;

        return [
            'planned' => $totalPlannedJuz,
            'completed' => $totalCompletedJuz,
            'percentage' => $percentage,
        ];
    }

    // EXACT COPY of calculateAchievedJuzFromReports from MonthEndIjazasanadSummaryController
    private function calculateAchievedJuzFromReports($reports): int
    {
        $uniqueJuzSet = collect();

        foreach ($reports as $report) {
            // Get Juz information for the covered range
            $fromJuz = $this->getJuzFromSurahAyat($report->hefz_from_surat, $report->hefz_from_ayat);
            $toJuz = $this->getJuzFromSurahAyat($report->hefz_to_surat, $report->hefz_to_ayat);
            
            if ($fromJuz && $toJuz) {
                for ($juz = $fromJuz; $juz <= $toJuz; $juz++) {
                    $uniqueJuzSet->push($juz);
                }
            }
        }

        return $uniqueJuzSet->unique()->count();
    }

    // EXACT COPY of getJuzFromSurahAyat from MonthEndIjazasanadSummaryController
    private function getJuzFromSurahAyat(?int $surahId, ?int $ayat): ?int
    {
        if (!$surahId || !$ayat) {
            return null;
        }
        return (int)ceil($surahId / 4);
    }

    // EXACT COPY of calculatePageMetrics from MonthEndIjazasanadSummaryController
    private function calculatePageMetrics($students, int $classId, int $month, int $year): array
    {
        if ($students->isEmpty()) {
            return ['planned' => 0, 'completed' => 0, 'percentage' => 0];
        }

        $totalPlannedPages = 0;
        $totalCompletedPages = 0;

        foreach ($students as $student) {
            // Get planned pages from plan
            $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
                ->where(function ($query) use ($month, $year, $classId) {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('created_at', $year)
                            ->whereMonth('created_at', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                            ->whereMonth('start_date', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    });
                })
                ->first();

            if ($plan) {
                // Calculate planned pages using same logic as Level 2 calculation
                $plannedPages = 0;
                
                // Check if we have valid hefz plan coordinates
                if (!empty($plan->start_from_surat) && !empty($plan->start_from_ayat) && 
                    !empty($plan->to_surat) && !empty($plan->to_ayat)) {
                    
                    try {
                        // Use existing page calculation logic
                        if ($plan->study_direction == 'backward') {
                            $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                $plan->start_from_surat,
                                $plan->start_from_ayat,
                                $plan->to_surat,
                                $plan->to_ayat
                            ]);
                            $plannedPages = $numberofPages[0]->numberofPagesSum ?? 0;
                        } else {
                            // Forward direction
                            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                $plan->start_from_surat,
                                $plan->start_from_ayat,
                                $plan->to_surat,
                                $plan->to_ayat
                            ]);
                            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                            $plannedPages = $results[0]->number_of_pages_sum ?? 0;
                        }
                        
                        $totalPlannedPages += $plannedPages;
                    } catch (\Exception $e) {
                        \Log::error('Error calculating planned pages for student ' . $student->id . ': ' . $e->getMessage());
                    }
                }
            }

            // Get completed pages from reports
            $completedPages = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->sum('pages_memorized') ?? 0;

            $totalCompletedPages += $completedPages;
        }

        $percentage = $totalPlannedPages > 0 ? min(100, round(($totalCompletedPages / $totalPlannedPages) * 100, 1)) : 0;

        return [
            'planned' => $totalPlannedPages,
            'completed' => $totalCompletedPages,
            'percentage' => $percentage,
        ];
    }
} 
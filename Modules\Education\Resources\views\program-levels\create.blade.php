@extends('layouts.hound')
@section('mytitle', 'Create Program Level')

@section('content')
<div class="col-sm-12">
    <div class="panel panel-default card-view">
        <div class="panel-heading">
            <div class="pull-left">
                <h6 class="panel-title txt-dark">{{ trans('common.create') }}  ProgramLevel</h6>
            </div>
            <a href="{{ url('/workplace/education/program-levels') }}" class="pull-right" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
            <div class="clearfix"></div>
        </div>
        <div class="panel-wrapper collapse in">
            @if ($errors->any())
                <ul class="alert alert-danger">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            @endif

            {!! Form::open(['url' => '/workplace/education/program-levels', 'class' => 'form-horizontal', 'files' => true]) !!}

            @include ('education::program-levels.form')

            {!! Form::close() !!}

        </div>
    </div>
</div>
@endsection

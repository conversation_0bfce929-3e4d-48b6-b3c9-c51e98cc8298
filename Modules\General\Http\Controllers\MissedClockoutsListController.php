<?php

namespace Modules\General\Http\Controllers;


use App\Admission;
use App\Center;
use App\CenterEmployee;
use App\Form;
use App\MissedClockOut;
use App\Role;
use App\Scopes\OrganizationScope;
use App\Student;
use Carbon\Carbon;
use Illuminate\Http\Request;

use App\Http\Controllers\Controller;


class MissedClockoutsListController extends Controller
{

    public function __invoke(Request $request)
    {





            $allMissedClockOutAttendance = MissedClockOut::withTrashed()->with('attendance');
            if (($request->filled('gender'))) {
                $allMissedClockOutAttendance->whereHas("employee", function ($q) use ($request) {
                    return $q->where('gender', $request->gender);
                });
            }


            if (($request->filled('roles'))) {
                $allMissedClockOutAttendance->whereHas("employee.roles", function ($q) use ($request) {
                    return $q->whereIn('id', $request->roles);
                });
            }

            if ($request->filled('name')) {
                $name = $request->name;
                $requestedName = '%' . $name . '%';
                $allMissedClockOutAttendance->whereHas("employee", function ($q) use ($requestedName) {
                    return $q->where('name', 'LIKE', $requestedName)
                        ->orWhere('display_name', 'LIKE', $requestedName)
                        ->orWhere('full_name', 'LIKE', $requestedName)
                        ->orWhere('full_name_trans', 'LIKE', $requestedName);
                });
            }


            if ((auth()->user()->hasRole(['education-manager_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {


                $allMissedClockOutAttendance->whereHas("employee.roles", function ($q) use ($request) {
                     $q->where('name', 'supervisor_' . config('organization_id') . '_');
                });
            }


            if ((auth()->user()->hasRole(['supervisor_' . config('organization_id') . '_'])) && !(auth()->user()->hasRole(['human-resource_' . config('organization_id') . '_', 'website-editor_' . config('organization_id') . '_', 'managing-director_' . config('organization_id') . '_']))) {
                $supervisorCenId = auth()->user()->center()->get()->pluck('id');
                $supervisorCenId = $supervisorCenId->toArray();


//                $allMissedClockOutAttendance->whereHas("employee.roles", function ($q) use ($request) {
//                    return $q->where('name', 'teacher_' . config('organization_id') . '_');
//                });

                if($request->filled('teacherCenters'))
                {

                    $allMissedClockOutAttendance->whereHas("employee.teacherCenter", function ($q) use ($request) {
                        $teacherCenIds = $request->get('teacherCenters');
                        $q->whereIn("cen_id", $teacherCenIds);
                    });
                }
                else{



                    $allMissedClockOutAttendance->whereHas("employee.teacherCenter", function ($q) use ($supervisorCenId) {
                        $supervisorCenId = auth()->user()->center()->get()->pluck('id');
                        $supervisorCenId = $supervisorCenId->toArray();
                        $q->whereIn("cen_id", $supervisorCenId);
                    });
                }

            }

        $yesterday = date("Y-m-d", strtotime( '-1 days' ) );
        $countYesterday = MissedClockOut::withTrashed()->with('attendance')->whereDate('clock', $yesterday )->get()->groupBy(['employee_id',date('clock')])->count();
            $paginateCount = 40;


        if($request->filled('last-year-records'))
        {
            $missedClockoutList = $allMissedClockOutAttendance->orderByRaw('date(clock) desc, employee_id')->cursor();
            return view('general::missed_clockouts_list',compact('missedClockoutList'));



        }
        else{



            $lastYear = (Carbon::now()->year)-1;

            $missedClockoutList = $allMissedClockOutAttendance->orderByRaw('date(clock) desc, employee_id')->whereYear('clock','>',$lastYear)->cursor();

            return view('general::missed_clockouts_list',compact('missedClockoutList'));


        }




        if($countYesterday == 0)
        {

            if($request->filled('last-year-records'))
            {
//                $missedClockoutList = $allMissedClockOutAttendance->orderBy('clock','desc')->paginate(10);
                $missedClockoutList = $allMissedClockOutAttendance->orderByRaw('date(clock) desc, employee_id')->paginate(10);
                return view('general::missed_clockouts_list',compact('missedClockoutList'));



            }
            else{



                $lastYear = (Carbon::now()->year)-1;

                $missedClockoutList = $allMissedClockOutAttendance->orderByRaw('date(clock) desc, employee_id')->whereYear('clock','>',$lastYear)->paginate(10);


                return view('general::missed_clockouts_list',compact('missedClockoutList'));


            }





        }
        if($countYesterday > 0)
        {

            if($request->filled('last-year-records'))
            {
                $missedClockoutList = $allMissedClockOutAttendance->orderByRaw('date(clock) desc, employee_id')->paginate($countYesterday);
                return view('general::missed_clockouts_list',compact('missedClockoutList'));



            }
            else{


                $lastYear = (Carbon::now()->year)-1;

                $missedClockoutList = $allMissedClockOutAttendance->orderByRaw('date(clock) desc, employee_id')->whereYear('clock','>',$lastYear)->paginate($countYesterday);

                return view('general::missed_clockouts_list',compact('missedClockoutList'));


            }





        }



            if($request->has('page') && $request->get('page') > 1)
            {

                $missedClockoutList = $allMissedClockOutAttendance->orderByRaw('date(clock) desc, employee_id')->paginate(10);

                return view('general::missed_clockouts_list',compact('missedClockoutList'));

            }


            return \Yajra\DataTables\DataTables::of($allMissedClockOutAttendance->select())
                ->with('count', function() use ($row) {


                    $yesterday = date("Y-m-d", strtotime( '-1 days' ) );
                    $countYesterday = MissedClockOut::withTrashed()->with('attendance')->whereDate('clock', $yesterday )->get()->groupBy(['employee_id',date('clock')])->count();
                    return $countYesterday;

                })
                ->addColumn('full_name', function ($row) {


                    return '<span  data-missedClockOut_id="' . $row->id . '" data-employee_id="' . $row->employee_id . '">' . $row->employee->full_name . '</span>';
                })
                ->addColumn('clock', function ($row) {


                    return $row->clock->toDateString();


                })
                ->addColumn('in', function ($row) {
                    $html = '';
                    if ($row->type == 'in') {
                        $html .= '<span class="btn-success">' . $row->clock->format('h:iA') . '</span>';

                        return $html;
                    }


                })
                ->addColumn('out', function ($row) {


                    $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                        ->whereDate('clock', $row->clock->toDateString())->where('type', 'out');

                    if (optional($out)->exists()) {
                        $out = $out->first();
                        $html = ' <span class="btn-warning">' . optional(optional($out)->clock)->format('h:iA') . '</span>';

                        return $html;
                    } else {

                        // show the modal trigger button
                        return ' <button class="ui button item" data-value="matt" data-toggle="modal"
                                             id="addOutAttendanceRecordBtnHound"
                                             data-date="' . $row->clock->toDateString() . '"
                                             data-employee_id="' . $row->employee_id . '"
                                             data-in="' . $row->clock . '"
                                             data-target="#addOutAttendanceRecordHound">
                                                 Add Out record
                                        </button>';

                    }


                })
                ->addColumn('numberofHours', function ($row) {

                    $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                        ->whereDate('clock', $row->clock->toDateString())->where('type', 'out')->first();
                    if ($row->type == 'in') {

                        $in = is_null($row->clock) ? \Carbon\Carbon::now() : Carbon::parse($row->clock);


                    } else {

                        $in = \Carbon\Carbon::now();

                    }
                    if (optional($out)->exists()) {
                        $hours = $in->diffInHours($out->clock);
                        $seconds = $in->diffInMinutes($out->clock);
                        return $hours . ':' . $seconds % 60;
                    }

                })
                ->addColumn('clockoutReason', function ($row) {
                    $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                        ->whereDate('clock', $row->clock->toDateString())->where('type', 'out')->first();
                    return optional($out)->clockout_reason;
                })

                ->addColumn('action', function ($row) {

                    $clockDate = $row->clock->toDateString();


                    $out = \App\MissedClockOut::withTrashed()->where('employee_id', $row->employee_id)
                        ->whereDate('clock', $clockDate)->where('type', 'out');

                    if ($out->exists()) {

                        $in = \App\MissedClockOut::onlyTrashed()->where('employee_id', $row->employee_id)
                            ->whereDate('clock', $clockDate)->where('type', 'in');

                        $in = $in->first();



                        $out = $out->first();



                        if($row->type == 'in' ){
                            $in = $row->id;

                        }
                        if($row->type == 'out' ){
                            $out = $row->id;

                        }
                        return '<button 
                    
                    class="btn btn-success btn-xs deleteModalTriggerBtn "
                     id="deleteModalTriggerBtn "
                 
                     data-toggle="modal"
                   data-missed_clockin_id="' .$in. '"
//                     data-missed_clockin_id="' . optional($in)->id . '"
//                     data-missed_clockout_id="' . optional($out)->id . '"
                     data-missed_clockout_id="' . $out . '"
                     data-attendance_in_id="' . optional($row)->attendance_id . '"
                     data-employee_id="' . optional($row)->employee_id . '"
                     data-clock="' . optional($out)->clock . '"
                     data-clockout_reason="' . optional($out)->clockout_reason . '"
                      data-target="#validateMissedClockoutEntry"><strong>Validate</strong></button>
                     <button 
                    
                    class="btn btn-default btn-xs deleteModalTriggerBtn "
                     id="deleteModalTriggerBtn "
                    
                     data-toggle="modal"
                     data-missed_clockin_id="' . optional($in)->id . '"
                     data-missed_clockout_id="' . optional($out)->id . '"
                     data-attendance_in_id="' . optional($row)->attendance_id . '"
                     data-employee_id="' . optional($row)->employee_id . '"
                     data-clock="' . optional($out)->clock . '"
                     data-clockout_reason="' . optional($out)->clockout_reason . '" 
                     data-target="#revertMissedClockoutEntry"><strong>Revert</strong></button>';
                    }


                })->rawColumns(['action', 'in', 'out','full_name'])
                ->toJson();




    }

}









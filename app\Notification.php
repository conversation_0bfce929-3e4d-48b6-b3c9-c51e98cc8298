<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Auth;

class Notification extends Model
{

//    protected $fillable = ['user_id', 'role', 'type', 'data', 'url', 'notifiable_id', 'notifiable_type'];
    protected $fillable = ['user_id', 'role', 'type', 'data', 'url', 'notifiable_id', 'notifiable_type'];

    public function notifiable() {
        return $this->morphTo();
    }


    
    
    public static function notifications(){

        $user = Auth()->user();
        if ($user) {
            if($user->role_id == 2){
                return Notification::where('user_id', \Illuminate\Support\Facades\Auth::id())->where('role_id', 2)->where('is_read', 0)->orderBy('id','DESC')->get();
                // return Notification::where('role_id', 2)->where('is_read', 0)->get();
            }
            if($user->role_id == 3){
                return Notification::where('user_id', Auth::user()->id)->where('role_id', 3)->where('is_read', 0)->orderBy('id','DESC')->get();
                // return Notification::where('role_id', 2)->where('is_read', 0)->get();
            }
            if($user->role_id == 1){
                return Notification::where('user_id', Auth::user()->id)->where('role_id', 1)->where('is_read', 0)->orderBy('id','DESC')->get();
                // return Notification::where('role_id', 2)->where('is_read', 0)->get();
            }
            if($user->role_id == 10){
                return Notification::where('is_read', 0)->orderBy('id','DESC')->get();
            }else{
                return Notification::where('user_id', $user->id)->where('role_id', '!=', 2)->where('is_read', 0)->orderBy('id','DESC')->get();
            }
        }

    }
    
    
}
@if($report->notes)
<div class="alert alert-info text-center" role="alert">
    {{ $report->notes }}
</div>
@endif
<h5>Memorization Report</h5>
<table class="table table-responsive table-striped">
    <thead>
        <tr>
            <th>Student Name</th>
            <th>Lesson</th>
            <th>Memorization Result</th>
            <th>Notes</th>
        </tr>
    </thead>
    @foreach($report->hefz as $lesson_report)
    <tr>
        <td>
            {{ $lesson_report->student->full_name }}
        </td>
        <td>
            Form Surah {{ getSurahNameById($lesson_report->hefz_from_surat) }}, {{ $lesson_report->hefz_from_ayat }} to
            {{ getSurahNameById($lesson_report->hefz_to_surat) }}, {{ $lesson_report->hefz_to_ayat }}
        </td>
        <td>
            [{{ $lesson_report->result->code }}] {{ $lesson_report->result->title }}
        </td>
        <td>
            @if($lesson_report->result->extra_field)
            Have to repeat
            @endif
        </td>
    </tr>
    @endforeach
</table>

<h5>Revision Report</h5>
<table class="table table-responsive table-striped">
    <thead>
        <tr>
            <th>Student Name</th>
            <th>Lesson</th>
            <th>Revision Result</th>
            <th>Notes</th>
        </tr>
    </thead>
    @foreach($report->revision as $lesson_report)
    <tr>
        <td>
            {{ $lesson_report->student->full_name }}
        </td>
        <td>
            Form Surah {{ getSurahNameById($lesson_report->revision_from_surat) }},
            {{ $lesson_report->revision_from_ayat }} to {{ getSurahNameById($lesson_report->revision_to_surat) }},
            {{ $lesson_report->revision_to_ayat }}
        </td>
        <td>
            [{{ $lesson_report->result->code }}] {{ $lesson_report->result->title }}
        </td>
        <td>
            @if($lesson_report->result->extra_field)
            Have to repeat
            @endif
        </td>
    </tr>
    @endforeach
</table>
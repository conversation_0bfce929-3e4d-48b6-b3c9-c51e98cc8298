@foreach(config('app.locales') as $key => $language)
<div class="form-group {{ $errors->has($language.'.name') ? 'has-error' : ''}}">
    {!! Form::label('name', 'Class Name ['.get_language_name($language).']', ['class' => 'col-md-4 control-label']) !!}

    <div class="col-md-6">
        {!! Form::text('translate['.$language.'][name]', isset($class) && isset($class->translate($language)->name) ? $class->translate($language)->name : '' , ['class' => 'form-control' , 'placeholder' => 'name']) !!}
        {!! $errors->first('name', '
        <p class="help-block">
            :message
        </p>
        ') !!}
    </div>
</div>
@endforeach

<div class="form-group {{ $errors->has('class_code') ? 'has-error' : ''}}">
    {!! Form::label('class_code', 'Class Code', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::text('class_code', null, ['class' => 'form-control']) !!}
        {!! $errors->first('class_code', '<p class="help-block">:message</p>') !!}
    </div>
</div><div class="form-group {{ $errors->has('center_id') ? 'has-error' : ''}}">
    {!! Form::label('center_id', 'Center', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
    
        {!! Form::select('center_id', $centers, null, ['class' => 'form-control']) !!}
        {!! $errors->first('center_id', '<p class="help-block">:message</p>') !!}
    </div>
</div><div class="form-group {{ $errors->has('status') ? 'has-error' : ''}}">
    {!! Form::label('status', 'Status', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('status', ['Active' , 'Full' , 'Suspended'] , null, ['class' => 'form-control']) !!}
        {!! $errors->first('status', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group">
    <div class="col-md-offset-4 col-md-4">
        {!! Form::submit(isset($submitButtonText) ? $submitButtonText : 'Create', ['class' => 'btn btn-primary']) !!}
    </div>
</div>

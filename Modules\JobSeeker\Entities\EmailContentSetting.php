<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

/**
 * EmailContentSetting Entity
 * 
 * Controls which job fields appear in notification emails and how they're formatted
 * 
 * @property int $id
 * @property string $field_name
 * @property bool $is_enabled
 * @property string $display_label
 * @property int $display_order
 * @property string $field_group
 * @property array|null $formatting_options
 * @property bool $conditional_display
 * @property bool $requires_provider_fetch
 * @property string $created_by
 * @property string $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
final class EmailContentSetting extends Model
{
    protected $table = 'jobseeker_email_content_settings';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'field_name',
        'is_enabled',
        'display_label',
        'display_order',
        'field_group',
        'formatting_options',
        'conditional_display',
        'requires_provider_fetch',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_enabled' => 'boolean',
        'display_order' => 'integer',
        'formatting_options' => 'array',
        'conditional_display' => 'boolean',
        'requires_provider_fetch' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Field group constants
     */
    public const GROUP_BASIC = 'basic';
    public const GROUP_COMPANY = 'company';
    public const GROUP_DETAILS = 'details';
    public const GROUP_REQUIREMENTS = 'requirements';
    public const GROUP_APPLICATION = 'application';

    /**
     * Available field groups
     */
    public static function getAvailableGroups(): array
    {
        return [
            self::GROUP_BASIC => 'Basic Information',
            self::GROUP_COMPANY => 'Company Details',
            self::GROUP_DETAILS => 'Job Details',
            self::GROUP_REQUIREMENTS => 'Requirements',
            self::GROUP_APPLICATION => 'Application Information',
        ];
    }

    /**
     * Get all enabled settings ordered by group and display order
     */
    public static function getEnabledSettings(): Collection
    {
        return Cache::remember('email_content_settings_enabled', 3600, function () {
            return self::where('is_enabled', true)
                ->orderBy('field_group')
                ->orderBy('display_order')
                ->get();
        });
    }

    /**
     * Get settings grouped by field group
     */
    public static function getSettingsGrouped(): Collection
    {
        return Cache::remember('email_content_settings_grouped', 3600, function () {
            return self::orderBy('field_group')
                ->orderBy('display_order')
                ->get()
                ->groupBy('field_group');
        });
    }

    /**
     * Check if a specific field is enabled
     */
    public static function isFieldEnabled(string $fieldName): bool
    {
        return Cache::remember("email_field_enabled_{$fieldName}", 3600, function () use ($fieldName) {
            return self::where('field_name', $fieldName)
                ->where('is_enabled', true)
                ->exists();
        });
    }

    /**
     * Get field setting by name
     */
    public static function getFieldSetting(string $fieldName): ?EmailContentSetting
    {
        return Cache::remember("email_field_setting_{$fieldName}", 3600, function () use ($fieldName) {
            return self::where('field_name', $fieldName)->first();
        });
    }

    /**
     * Clear all email content settings cache
     */
    public static function clearCache(): void
    {
        Cache::forget('email_content_settings_enabled');
        Cache::forget('email_content_settings_grouped');
        
        // Clear individual field caches
        $fieldNames = self::pluck('field_name');
        foreach ($fieldNames as $fieldName) {
            Cache::forget("email_field_enabled_{$fieldName}");
            Cache::forget("email_field_setting_{$fieldName}");
        }
    }

    /**
     * Scope for enabled settings
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * Scope for specific field group
     */
    public function scopeGroup($query, string $group)
    {
        return $query->where('field_group', $group);
    }

    /**
     * Scope for fields that require provider fetch
     */
    public function scopeRequiresProviderFetch($query)
    {
        return $query->where('requires_provider_fetch', true);
    }

    /**
     * Get the human readable group name
     */
    public function getGroupNameAttribute(): string
    {
        $groups = self::getAvailableGroups();
        return $groups[$this->field_group] ?? ucfirst($this->field_group);
    }

    /**
     * Check if field should be displayed conditionally
     */
    public function shouldDisplayConditionally(): bool
    {
        return $this->conditional_display;
    }

    /**
     * Get formatting option value
     */
    public function getFormattingOption(string $key, mixed $default = null): mixed
    {
        return $this->formatting_options[$key] ?? $default;
    }

    /**
     * Set formatting option
     */
    public function setFormattingOption(string $key, mixed $value): void
    {
        $options = $this->formatting_options ?? [];
        $options[$key] = $value;
        $this->formatting_options = $options;
    }

    /**
     * Boot method to clear cache when model is updated
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function () {
            self::clearCache();
        });

        static::deleted(function () {
            self::clearCache();
        });
    }
}

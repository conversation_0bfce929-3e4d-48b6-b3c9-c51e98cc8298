<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithTitle;

final class HalaqahReportsExport implements WithMultipleSheets
{
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        return [
            new DailyReportsMemorizationSheet($this->filters),
            new DailyReportsRevisionSheet($this->filters),
        ];
    }
}

<?php

namespace App\Http\Controllers;

use App\Http\Requests\SearchUsernameRequest;
use App\User;
use App\Classes;
use App\Employee;
use App\Guardian;
use App\Section;
use App\Student;
use App\Subject;
use App\ClassRoom;
use App\ClassTime;
use App\ApiBaseMethod;
use App\EmailSetting;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class AuthController extends Controller
{

    public function __construct()
    {
//        $this->middleware('PM');
        // User::checkAuth();
    }

    public function get_class_name(Request $request, $id)
    {
        $get_class_name = Classes::select('class_name as name')->where('id', $id)->first();
        return $get_class_name;
    }

    public function get_section_name(Request $request, $id)
    {
        $get_section_name = Section::select('section_name as name')->where('id', $id)->first();
        return $get_section_name;
    }

    public function get_teacher_name(Request $request, $id)
    {
        $get_teacher_name = Employee::select('full_name as name')->where('id', $id)->first();
        return $get_teacher_name;
    }

    public function get_subject_name(Request $request, $id)
    {
        $get_subject_name = Subject::select('subject_name as name')->where('id', $id)->first();
        return $get_subject_name;
    }


    public function get_room_name(Request $request, $id)
    {
        $get_room_name = ClassRoom::select('room_no as name')->where('id', $id)->first();
        return $get_room_name;
    }

    public function get_class_period_name(Request $request, $id)
    {
        $get_class_period_name = ClassTime::select('period as name', 'start_time', 'end_time')->where('id', $id)->first();
        return $get_class_period_name;
    }


    public function getLoginAccess(Request $request)
    {


        if ($request->value == "Student") {
            $user = User::where('role_id', 2)->first();
        } elseif ($request->value == "Parents") {
            $user = User::role('parent')->first();
        } elseif ($request->value == "Super Admin") {
            $user = User::where('role_id', 1)->first();
        } elseif ($request->value == "Admin") {
            $user = User::where('role_id', 5)->first();
        } elseif ($request->value == "Teacher") {
            $user = User::where('role_id', 3/** teacher **/)->first();
        } elseif ($request->value == "Accountant") {
            $user = User::where('role_id', 6)->first();
        } elseif ($request->value == "Receptionist") {
            $user = User::where('role_id', 7)->first();
        } elseif ($request->value == "Librarian") {
            $user = User::where('role_id', 8)->first();
        }

        return response()->json($user);
    }

    public function recoveryPassord()
    {
        try {
            return view('auth.recovery_password');
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function recoveryUsername()
    {
        try {
            return view('auth.iforgotusername');
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function findUsername(SearchUsernameRequest $request)
    {


        try {



            $usernames = (new User())->newQuery(); //where Unit is the model
            if($request->filled('email')){
                $usernames->orWhere('email',$request->email);
            }
            if($request->filled('full_name')){
                $usernames->orWhere('full_name',$request->full_name);
            } if($request->filled('display_name')){
                $usernames->orWhere('display_name',$request->display_name);
            }

            $usernames = $usernames->pluck('username')->toArray();


            $usernameCount = count($usernames);

            if ($usernameCount > 0) {
                $messageSuccess = 'Success ! Please check your email';
                return view('auth.username_found', compact('usernames', 'usernameCount', 'messageSuccess'));
            }
            return view('auth.username_found');
        } catch (\Exception $e) {
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function emailVerify(Request $request)
    {

        $this->validate($request, [
//            'email' => 'required'
            'username' => 'required|exists:users,username'
        ]);
        try {


//            $emailCheck = User::select('*')->where('email', $request->email)->first();
            $usernameCheck = User::select('*')->where('username', $request->username)->first();


            if ($usernameCheck == "") {
                return redirect()->back()->with('message-danger', "Invalid Username, Please try again");
            } else {


                $data['username'] = $request->username;
                $data['random'] = Str::random(32);
                $data['id'] = Str::random(32);


                $user = User::where('username', $request->username)->first();
                $user->random_code = $data['random'];

//                $data['role_id'] = $user->role_id;

                $user->save();

                $settings = EmailSetting::find(1);
                $email = $settings->from_email;
                $organizationName = $settings->from_name;

                if ($settings->email_engine_type == "email") {
                    $mailto = $usernameCheck->email;
                    $subject = 'Password Reset';
                    $message = '<a href="' . url('reset/password' . '/' . $data['username'] . '/' . $data['random']) . '" style="color:#ffffff; text-decoration:none;" target="_blank">Click Here </a>';


                    $separator = md5(time());
                    $eol = "\r\n";
                    $headers = "From: name <" . $email . ">" . $eol;
                    $headers .= "MIME-Version: 1.0" . $eol;
                    $headers .= "Content-Type: multipart/mixed; boundary=\"" . $separator . "\"" . $eol;
                    $headers .= "Content-Transfer-Encoding: 7bit" . $eol;
                    $headers .= "This is a MIME encoded message." . $eol;

                    // message
                    $body = "--" . $separator . $eol;
                    $body .= "Content-Type: text/plain; charset=\"iso-8859-1\"" . $eol;
                    $body .= "Content-Transfer-Encoding: 8bit" . $eol;
                    $body .= $message . $eol;

                    // attachment
                    $body .= "--" . $separator . $eol;
                    $body .= "Content-Transfer-Encoding: base64" . $eol;
                    $body .= "Content-Disposition: attachment" . $eol;
                    $body .= $eol;
                    $body .= "--" . $separator . "--";
                    mail($mailto, $subject, $body, $headers);
                } else {


                    Mail::send('auth.confirmation_reset', compact('data'), function ($message) use ($request, $usernameCheck) {
                        $settings = EmailSetting::find(1);
                        $email = $settings->from_email;
//                        $organizationName = $settings->from_name;
                        $organizationName = 'ITQAN';

                        $message->to($usernameCheck->email, $organizationName)->subject('Reset Password');
                        $message->from($email, $organizationName);
                    });

                    // }
                    return redirect('login')->with('message-success', 'Success ! Please check your email');

//                     return redirect()->back()->with('message-success', 'Success ! Please check your email');
                }
            }

        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function resetEmailConfirtmation($username, $code)
    {

        try {
            $user = User::where('username', $username)->where('random_code', $code)->first();

            if ($user != "") {
                $username = $user->username;

                return view('auth.new_password', compact('username'));
            } else {
                Toastr::error('You have clicked on a invalid link', 'Failed');
                return redirect('recovery/password');
                // return redirect('recovery/password')->with('message-danger', 'You have clicked on a invalid link, please try again');
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function storeNewPassword(Request $request)
    {
        $this->validate($request, [
            'new_password' => 'required|same:confirm_password',
            'confirm_password' => 'required'
        ]);

        try {
            $user = User::where('username', $request->username)->first();
//            $user->password = Hash::make($request->new_password);
            $user->password = bcrypt($request->new_password);
            $user->random_code = '';
            $result = $user->save();

            if ($result) {
                Toastr::success('Password has beed reset successfully', 'Success');
//                return redirect()->back();
                return redirect('login')->with('message-success', 'Password has been reset successfully');
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
                // return redirect()->back()->with('message-danger', 'Something went wrong, please try again');
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function mobileLogin(Request $request)
    {

        $input = $request->all();

        $validator = Validator::make($input, [
            'email' => "required",
            'password' => "required"
        ]);


        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $user = User::where('email', $request->email)->first();
            if ($user != "") {
                if (Hash::check($request->password, $user->password)) {

                    $data = [];

                    $data['user'] = $user->toArray();
                    $role_id = $user->role_id;
                    if ($role_id == 23) {
                        //$data['userDetails'] = Student::where('user_id', $user->id)->first();
                        $data['userDetails'] = DB::table('students')->select('students.*', 'parents.*', 'classes.*', 'sections.*')
                            ->join('parents', 'parents.id', '=', 'students.guardian_id')
                            ->join('classes', 'classes.id', '=', 'students.class_id')
                            ->join('sections', 'sections.id', '=', 'students.section_id')
                            ->where('students.user_id', $user->id)
                            ->first();

                        $data['religion'] = DB::table('students')->select('base_setups.base_setup_name as name')
                            ->join('base_setups', 'base_setups.id', '=', 'students.religion_id')
                            ->where('students.user_id', $user->id)
                            ->first();

                        $data['blood_group'] = DB::table('students')->select('base_setups.base_setup_name as name')
                            ->join('base_setups', 'base_setups.id', '=', 'students.bloodgroup_id')
                            ->where('students.user_id', $user->id)
                            ->first();


                        $data['transport'] = DB::table('students')
                            ->select('vehicles.vehicle_no', 'vehicles.vehicle_model', 'Employees.full_name as driver_name', 'vehicles.note')
                            ->join('vehicles', 'vehicles.id', '=', 'students.vechile_id')
                            ->join('Employees', 'Employees.id', '=', 'vehicles.driver_id')
                            ->where('students.user_id', $user->id)
                            ->first();
                        $data['system_settings'] = DB::table('general_settings')->get();
                        $data['TTL_RTL_status'] = '1=RTL,2=TTL';
                    } else if (\Auth::guard("web")->user()->hasRole("parent")) {
                        $data['userDetails'] = Guardian::where('user_id', $user->id)->first();
                    } else {
                        $data['userDetails'] = Employee::where('id', $user->id)->first();
                    }

                    return ApiBaseMethod::sendResponse($data, 'Login successful.');
                } else {
                    return ApiBaseMethod::sendError('These credentials do not match our records.');
                }
            } else {
                return ApiBaseMethod::sendError('These credentials do not match our records.');
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function setToken(Request $request)
    {
        try {
            $user = User::find($request->id);
            $user->notificationToken = $request->token;
            $user->save();
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = '';
                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function childInfo(Request $request, $user_id)
    {

        try {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {

                //$student_detail = Student::where('user_id', $user_id)->first();

                //$user = User::where('user_id', $student_detail->email)->first();
                $user = Student::where('user_id', $user_id)->first();
                $data = [];

                $data['user'] = $user->toArray();
                //$role_id = $user->role_id;

                //$data['userDetails'] = Student::where('user_id', $user->id)->first();
                $data['userDetails'] = DB::table('students')->select('students.*', 'parents.*', 'classes.*', 'sections.*')
                    ->join('parents', 'parents.id', '=', 'students.guardian_id')
                    ->join('classes', 'classes.id', '=', 'students.class_id')
                    ->join('sections', 'sections.id', '=', 'students.section_id')
                    ->where('students.id', $user->id)
                    ->first();

                $data['religion'] = DB::table('students')->select('base_setups.base_setup_name as name')
                    ->join('base_setups', 'base_setups.id', '=', 'students.religion_id')
                    ->where('students.id', $user->id)
                    ->first();

                $data['blood_group'] = DB::table('students')->select('base_setups.base_setup_name as name')
                    ->join('base_setups', 'base_setups.id', '=', 'students.bloodgroup_id')
                    ->where('students.id', $user->id)
                    ->first();


                $data['transport'] = DB::table('students')
                    ->select('vehicles.vehicle_no', 'vehicles.vehicle_model', 'Employees.full_name as driver_name', 'vehicles.note')
                    ->join('vehicles', 'vehicles.id', '=', 'students.vechile_id')
                    ->join('Employees', 'Employees.id', '=', 'students.vechile_id')
                    ->where('students.id', $user->id)
                    ->first();

                //$data['userDetails'] = Student::where('id', $user->id)->first();


                return ApiBaseMethod::sendResponse($data, null);
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}
